package com.actonia.value.object;

import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.utils.EmailSenderComponent;
import com.actonia.utils.SpringBeanFactory;
import com.google.gson.Gson;
import org.junit.Test;

import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import static org.junit.Assert.assertEquals;

public class TargetUrlDeadQueueDTOTest {
    private final Gson gson = new Gson();

    @Test
    public void test() throws ParseException {
        System.out.println(StandardCharsets.UTF_8.name());
        EmailSenderComponent emailSenderComponent =  SpringBeanFactory.getBean("emailSenderComponent");
        Map<String, Object> map = new HashMap<>();
        map.put("informationalMessage", "test");
        final ChangeIndicatorPreviousCurrent changeIndicatorPreviousCurrent = new ChangeIndicatorPreviousCurrent();
        changeIndicatorPreviousCurrent.setUrl("test");
        changeIndicatorPreviousCurrent.setSeverity("High");
        changeIndicatorPreviousCurrent.setChangeIndicatorDesc("Title changed");
        changeIndicatorPreviousCurrent.setPrevious("Craps – Gioca a dadi su 888 Casino ");
        changeIndicatorPreviousCurrent.setCurrent("Craps – Gioca a dadi su 888 Casino");
        final List<ChangeIndicatorPreviousCurrent> changeIndicatorPreviousCurrentList = Collections.singletonList(changeIndicatorPreviousCurrent);
        final TargetUrlChangeAlertDetails targetUrlChangeAlertDetails = new TargetUrlChangeAlertDetails();
        targetUrlChangeAlertDetails.setChangeIndicatorPreviousCurrentList(changeIndicatorPreviousCurrentList);
        targetUrlChangeAlertDetails.setUrl("test");
        targetUrlChangeAlertDetails.setTotalChanges(1);
        List<TargetUrlChangeAlertDetails> targetUrlChangeAlertDetailsList = Collections.singletonList(targetUrlChangeAlertDetails);
        map.put("targetUrlChangeAlertDetailsList", targetUrlChangeAlertDetailsList);
        final String result = emailSenderComponent.getContentByTemplate("mail_target_url_change_detailed_alert.html", map);
        System.out.println(result);
//        final TargetUrlDeadQueueDTO targetUrlDeadQueueDTO = new TargetUrlDeadQueueDTO();
//        final HtmlClickHouseEntity htmlClickHouseEntity = new HtmlClickHouseEntity();
//        final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
//        final Date trackDate = simpleDateFormat.parse("2023-07-21");
//        final java.sql.Date sqlDate = new java.sql.Date(trackDate.getTime());
//        htmlClickHouseEntity.setTrackDate(trackDate);
//        targetUrlDeadQueueDTO.setHtmlClickHouseEntityPrevious(htmlClickHouseEntity);
//        final String json = gson.toJson(targetUrlDeadQueueDTO);
//        System.out.println(json);
////        json.replaceAll("(\"trackDate\":\"\\w+.*)\"", "$1 12:00:00 AM");
//        String result = "{\"sendMessageTime\":\"20230825023535\",\"domainId\":8711,\"domainCrawlParameters\":{\"domainId\":8711,\"userAgent\":\"Mozilla/5.0 (compatible; ClarityBot/9.0; +https://www.seoclarity.net/bot.html)\",\"messagesPerIteration\":10,\"delayInSecondsPerHttpRequest\":0,\"enableJavascriptCrawl\":false,\"javascriptTimeoutInSecond\":10,\"crawlerRequestHeaders\":{\"Accept\":\"*/*\",\"Accept-Encoding\":\"*\",\"Accept-Language\":\"*\"},\"maxConcurrentThreads\":1,\"additionalContentEntityList\":[]},\"urlMetricsEntityV3\":{\"languageCode\":\"en\",\"targetUrlHtmlTrackDate\":\"2022-08-29\",\"amphtml_flag\":false,\"analyzed_url_flg_s\":\"Yes\",\"archive_flg\":\"No\",\"archive_flg_x_tag\":\"No\",\"blocked_by_robots\":\"No\",\"canonical_flg\":\"Yes\",\"canonical_header_flag\":false,\"canonical_header_type\":\"NONE\",\"canonical_type\":\"SAME\",\"canonical_url_is_consistent\":\"No\",\"content_type\":\"text/html; charset\\u003dutf-8\",\"description_flg\":\"Yes\",\"description_length\":175,\"description_simhash\":\"12076953950716462744\",\"error_message\":\"\",\"follow_flg\":\"No\",\"follow_flg_x_tag\":\"No\",\"h1_count\":1,\"h1_flg\":\"Yes\",\"h1_length\":65,\"h1_md5\":[\"9c913c27ea33c49a95366bcf908348b6\"],\"h2_hash\":\"df01bc5ab265ba9911897d2473107876\",\"h2_total\":2,\"header_noarchive\":false,\"header_nofollow\":false,\"header_noindex\":false,\"header_noodp\":false,\"header_nosnippet\":false,\"header_noydir\":false,\"hreflang_links_out_count\":0,\"hreflang_url_count\":0,\"index_flg\":\"No\",\"index_flg_x_tag\":\"No\",\"indexable\":true,\"insecure_resources_flag\":false,\"meta_charset\":\" charset\\u003dutf-8\",\"meta_content_type\":\"text/html\",\"meta_disabled_sitelinks\":false,\"meta_noodp\":false,\"meta_nosnippet\":false,\"meta_noydir\":false,\"meta_redirect\":false,\"mobile_rel_alternate_url_is_consistent\":true,\"noodp\":false,\"nosnippet\":false,\"noydir\":false,\"og_markup_flag\":false,\"og_markup_length\":0,\"redirect_blocked_reason\":\"\",\"redirect_flg\":false,\"response_code\":\"200\",\"robots\":\"\",\"robots_contents\":\"\",\"robots_flg\":\"No\",\"robots_flg_x_tag\":\"No\",\"title_flg\":\"Yes\",\"title_length\":78,\"title_md5\":[\"32a4cee115d4974d31a97595314778b9\"],\"title_simhash\":\"9767460631717455582\",\"viewport_flag\":true,\"pageAnalysisResultArray\":[{\"rule\":1,\"result\":0},{\"rule\":2,\"result\":0},{\"rule\":3,\"result\":0},{\"rule\":4,\"result\":0},{\"rule\":5,\"result\":0},{\"rule\":6,\"result\":0},{\"rule\":7,\"result\":1},{\"rule\":8,\"result\":0},{\"rule\":9,\"result\":1},{\"rule\":10,\"result\":0},{\"rule\":11,\"result\":0},{\"rule\":12,\"result\":0},{\"rule\":13,\"result\":0},{\"rule\":14,\"result\":0},{\"rule\":15,\"result\":1},{\"rule\":16,\"result\":1},{\"rule\":17,\"result\":1},{\"rule\":18,\"result\":0},{\"rule\":19,\"result\":0},{\"rule\":20,\"result\":0},{\"rule\":21,\"result\":1},{\"rule\":22,\"result\":0},{\"rule\":23,\"result\":0},{\"rule\":24,\"result\":0},{\"rule\":25,\"result\":0},{\"rule\":26,\"result\":0},{\"rule\":27,\"result\":0},{\"rule\":28,\"result\":0},{\"rule\":29,\"result\":0},{\"rule\":30,\"result\":0},{\"rule\":31,\"result\":0},{\"rule\":32,\"result\":0},{\"rule\":33,\"result\":0},{\"rule\":34,\"result\":1},{\"rule\":35,\"result\":0},{\"rule\":36,\"result\":1},{\"rule\":37,\"result\":1},{\"rule\":38,\"result\":1},{\"rule\":39,\"result\":0},{\"rule\":40,\"result\":0},{\"rule\":41,\"result\":0},{\"rule\":42,\"result\":0},{\"rule\":43,\"result\":0},{\"rule\":44,\"result\":0},{\"rule\":45,\"result\":0},{\"rule\":46,\"result\":0},{\"rule\":47,\"result\":0},{\"rule\":48,\"result\":0},{\"rule\":49,\"result\":0},{\"rule\":50,\"result\":0},{\"rule\":51,\"result\":0},{\"rule\":52,\"result\":1},{\"rule\":53,\"result\":1},{\"rule\":54,\"result\":0},{\"rule\":55,\"result\":0},{\"rule\":56,\"result\":0},{\"rule\":57,\"result\":0},{\"rule\":58,\"result\":1},{\"rule\":59,\"result\":0},{\"rule\":60,\"result\":0},{\"rule\":61,\"result\":0},{\"rule\":62,\"result\":0},{\"rule\":63,\"result\":1},{\"rule\":64,\"result\":0},{\"rule\":65,\"result\":0},{\"rule\":66,\"result\":0},{\"rule\":67,\"result\":0},{\"rule\":68,\"result\":0},{\"rule\":69,\"result\":0},{\"rule\":70,\"result\":0},{\"rule\":71,\"result\":0},{\"rule\":72,\"result\":0},{\"rule\":73,\"result\":0},{\"rule\":74,\"result\":0},{\"rule\":75,\"result\":0},{\"rule\":76,\"result\":0},{\"rule\":77,\"result\":0},{\"rule\":78,\"result\":0},{\"rule\":79,\"result\":0},{\"rule\":80,\"result\":0},{\"rule\":81,\"result\":0},{\"rule\":82,\"result\":0},{\"rule\":83,\"result\":0},{\"rule\":84,\"result\":0},{\"rule\":85,\"result\":0},{\"rule\":86,\"result\":0},{\"rule\":87,\"result\":0},{\"rule\":88,\"result\":0},{\"rule\":89,\"result\":0},{\"rule\":90,\"result\":0},{\"rule\":91,\"result\":0},{\"rule\":92,\"result\":0},{\"rule\":93,\"result\":0},{\"rule\":94,\"result\":0},{\"rule\":95,\"result\":0},{\"rule\":96,\"result\":0},{\"rule\":97,\"result\":1},{\"rule\":98,\"result\":0},{\"rule\":99,\"result\":0},{\"rule\":100,\"result\":0},{\"rule\":101,\"result\":0},{\"rule\":102,\"result\":0},{\"rule\":103,\"result\":0},{\"rule\":104,\"result\":0},{\"rule\":105,\"result\":0}],\"pageAnalysisResultInd\":true,\"changeTrackingHashCdJsonArray\":[{\"name\":\"analyzed_url_s\",\"value\":\"b7ef3a22622971a1ee13024a9a6a3445\"},{\"name\":\"canonical\",\"value\":\"b7ef3a22622971a1ee13024a9a6a3445\"},{\"name\":\"description\",\"value\":\"bd36f4cb37a6695b2d947b6b547eb034\"},{\"name\":\"h1\",\"value\":\"88a42f9d3dbc829cabbf304f332761a2\"},{\"name\":\"page_link\",\"value\":\"4316d48bf0a190b994e3c121b82ee865\"},{\"name\":\"structured_data\",\"value\":\"edb6505397b6770a006de042cbd2d636\"},{\"name\":\"title\",\"value\":\"32a4cee115d4974d31a97595314778b9\"},{\"name\":\"viewport_content\",\"value\":\"c23001ceb32a3b06b2eb725a451934d0\"},{\"name\":\"page_analysis_fragments\",\"value\":\"2b0e637b345aeac877229f12e8adfe29\"}],\"targetUrlHtmlDailyDataInd\":true,\"crawl_timestamp\":\"2022-08-29 18:55:07\",\"base_tag\":\"\",\"base_tag_flag\":false,\"base_tag_target\":\"\",\"response_header_names\":[\"Alt-Svc\",\"Cache-Control\",\"Cf-Cache-Status\",\"Cf-Ray\",\"Connection\",\"Content-Encoding\",\"Content-Security-Policy\",\"Content-Type\",\"Date\",\"Expect-Ct\",\"Lb_Pool\",\"Retry-After\",\"Server\",\"Set-Cookie\",\"Transfer-Encoding\",\"X-Frame-Options\"],\"url\":\"https://www.indeed.com/cmp/University-of-California---Santa-Cruz/faq\"},\"htmlClickHouseEntityPrevious\":{\"trackDate\":\"Aug 29, 2022\",\"crawlerResponse\":{\"response_code\":\"200 12:00:00 AM}}}";
//        final String string = result.replaceFirst("(\"trackDate\":\"\\w+ \\d+, \\d+)\"", "$1 12:00:00 AM\"");
//        final TargetUrlDeadQueueDTO targetUrlDeadQueueDTO1 = gson.fromJson(string, TargetUrlDeadQueueDTO.class);
//        assertEquals(htmlClickHouseEntity.getTrackDate(), targetUrlDeadQueueDTO1.getHtmlClickHouseEntityPrevious().getTrackDate());
    }

}
