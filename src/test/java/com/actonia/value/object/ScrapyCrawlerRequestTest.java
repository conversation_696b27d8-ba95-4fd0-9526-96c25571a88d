package com.actonia.value.object;
import com.actonia.IConstants;
import com.google.common.collect.Maps;

import com.google.gson.Gson;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicHeader;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.junit.Test;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.util.*;


public class ScrapyCrawlerRequestTest {

    private static final CloseableHttpClient httpClient;

    static {
        // use the TrustSelfSignedStrategy to allow Self Signed Certificates
        SSLContext sslContext = null;
        try {
            sslContext = SSLContextBuilder.create().loadTrustMaterial(new TrustSelfSignedStrategy()).build();
        } catch (NoSuchAlgorithmException | KeyManagementException | KeyStoreException e) {
            throw new RuntimeException(e);
        }

        // we can optionally disable hostname verification.
        // if you don't want to further weaken the security, you don't have to include this.
        HostnameVerifier allowAllHosts = new NoopHostnameVerifier();

        // create an SSL Socket Factory to use the SSLContext with the trust self signed certificate strategy
        // and allow all hosts verifier.
        SSLConnectionSocketFactory connectionFactory = new SSLConnectionSocketFactory(sslContext, allowAllHosts);

        final PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(200);
        connectionManager.setDefaultMaxPerRoute(200);


        Collection<Header> defaultHeaders = new ArrayList<>(4);
        defaultHeaders.add(new BasicHeader(IConstants.CONTENT_DASH_TYPE, IConstants.APPLICATION_SLASH_JSON));
        defaultHeaders.add(new BasicHeader(IConstants.ACCESS_KEY, "cHVwcmVuZGVyX2ZhY3R3ZWF2ZXJz"));
        defaultHeaders.add(new BasicHeader(IConstants.CACHE_CONTROL, IConstants.NO_CACHE));
        defaultHeaders.add(new BasicHeader(IConstants.CONTENT_TYPE, IConstants.APPLICATION_SLASH_JSON));

        httpClient = HttpClients.custom()
                .setSSLSocketFactory(connectionFactory)
                .setConnectionManager(connectionManager)
                .setConnectionManagerShared(true)
                .setDefaultHeaders(defaultHeaders)
                .build();
    }

    @Test
    public void test() throws UnsupportedEncodingException {
        final ScrapyCrawlerRequest scrapyCrawlerRequest = new ScrapyCrawlerRequest();
        scrapyCrawlerRequest.setUrl(Collections.singletonList("https://www.seoclarity.net/"));
        final ScrapyCrawlerRequest.ScrapySettings scrapySettings = new ScrapyCrawlerRequest.ScrapySettings();
        final HashMap<String, String> defaultRequestHeaders = Maps.newHashMap();
        defaultRequestHeaders.put("Accept", "*/*");
        defaultRequestHeaders.put("Accept-Encoding", "*/*");
        defaultRequestHeaders.put("Accept-Language", "*/*");
        defaultRequestHeaders.put("Cookie", "_authorized=ilm_seoclarity_u8g16&6^yUu0B34LT%KN");
        scrapySettings.setDefaultRequestHeaders(defaultRequestHeaders);
        scrapySettings.setUserAgent("Mozilla/5.0 (compatible; ClarityBot/9.0; +https://www.seoclarity.net/bot.html)");
        scrapyCrawlerRequest.setScrapySettings(scrapySettings);
        final String requestJson = new Gson().toJson(scrapyCrawlerRequest);
        final HttpPost httpPost = new HttpPost("http://localhost:9999/crawl");
        httpPost.setHeader("x-seoclarity-token", "testToken");
        httpPost.setEntity(new StringEntity(requestJson));
        try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
            final HttpEntity entity = response.getEntity();
            System.out.println(EntityUtils.toString(entity));
            EntityUtils.consume(entity);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

}
