package com.actonia.entity;

import org.apache.commons.lang.time.DateUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.Test;

import java.util.Calendar;
import java.util.Date;

import static org.junit.Assert.assertEquals;

public class PoliteCrawlInstanceTest {

    private final Logger log = LogManager.getLogger(PoliteCrawlInstanceTest.class);
    @Test
    public void getCrawlType() {
        PoliteCrawlInstance instance = new PoliteCrawlInstance();
        int value = PoliteCrawlInstance.CrawlTypeEnum.TARGET_URL.getValue();
        instance.setCrawlType(PoliteCrawlInstance.CrawlTypeEnum.values()[value - 1]);
        assertEquals(1, instance.getCrawlType().getValue().intValue());
    }

    // test getCrawlStatus()
    @Test
    public void getCrawlStatus() {
        PoliteCrawlInstance instance = new PoliteCrawlInstance();
        instance.setCrawlStatus(PoliteCrawlInstance.CrawlStatusEnum.values()[1]);
        log.debug("getCrawlStatus: {}", instance.getCrawlStatus());
        assertEquals(1, instance.getCrawlStatus().getValue().intValue());
    }

    @Test
    public void test() {
        // get a date of 2024-01-16
        Calendar calendar = Calendar.getInstance();
        calendar.set(2024, Calendar.JANUARY, 16);
        Date date = calendar.getTime();
        System.out.println(date);
    }
}
