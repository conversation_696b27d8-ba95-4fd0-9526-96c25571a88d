package com.actonia.dao;

import com.actonia.entity.TargetUrlEntity;
import com.actonia.utils.MurmurHashUtils;
import com.actonia.utils.SpringBeanFactory;
import org.junit.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.junit.Assert.*;

public class TargetUrlEntityDAOTest {

    private final TargetUrlEntityDAO targetUrlEntityDAO = SpringBeanFactory.getBean("targetUrlEntityDAO");


    /**
     * Should return null when there is no robots.txt url for the given domain id
     */

    @Test
    public void getTargetUrlList() {
        final List<TargetUrlEntity> targetUrlEntities = targetUrlEntityDAO.getTargetUrlList(11813);
        System.out.println(targetUrlEntities.size());
        assertNotNull(targetUrlEntities);
    }

}
