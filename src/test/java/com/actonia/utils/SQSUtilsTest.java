package com.actonia.utils;

import com.actonia.IConstants;
import com.actonia.entity.UrlMetricsEntityV3;
import com.actonia.value.object.ChangeTrackingHashCdJson;
import com.actonia.value.object.DomainCrawlParameters;
import com.actonia.value.object.TargetUrlDeadQueueDTO;
import com.amazonaws.services.sqs.model.ListQueuesResult;
import com.amazonaws.services.sqs.model.Message;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import junit.framework.TestCase;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.Test;

import java.io.*;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

public class SQSUtilsTest extends TestCase {

    private final ExecutorService executor = Executors.newFixedThreadPool(20);
    Logger logger = LogManager.getLogger(SQSUtilsTest.class);

    public void testGetApproximateNumberOfMessagesAndInflight() throws Exception {
        final Integer[] approximateNumberOfMessagesAndInflight = SQSUtils.getInstance().getApproximateNumberOfMessagesAndInflight("TEST_TARGET_URL_HTML_QUEUE_NAMES");
        logger.info("approximateNumberOfMessages: {}, inflight: {}", approximateNumberOfMessagesAndInflight[0], approximateNumberOfMessagesAndInflight[1]);
    }

    public void testGetMessageFromQueue() throws Exception {
        AtomicInteger i = new AtomicInteger(1);
        while (true) {
            final SQSUtils instance = SQSUtils.getInstance();
            final String queueUrl = IConstants.QUEUE_NAME_TARGET_URL_DAILY_HTML_PREFIX+ "EN_4765";
            final List<Message> messages = instance.getMessageFromQueue(queueUrl, 10, 120);
            if (messages == null || messages.isEmpty()) {
                System.out.println("no more messages");
                break;
            }
            Map<String, String> queueMessages = new HashMap<>();
            AtomicLong j = new AtomicLong(0);
            final Future<?> submit = executor.submit(() -> {
                messages.forEach(message -> {
                    final String body = message.getBody();
                    queueMessages.put(String.valueOf(System.nanoTime() + j.get()), body);
                    logger.info("message: {} {}", i, body);
                    try {
//                        instance.deleteMessageFromQueue(queueUrl, message);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                    i.getAndIncrement();
                });
            });
            submit.get();
//            instance.sendBatchMessageToQueue("TEST_" + queueUrl, queueMessages);
        }
    }

    public void testSendControllerQueue() throws Exception {
        String queueUrl = "NEW_URL_HTML_QUEUE_NAMES.fifo";
        Map<String, String> queueMessages = new HashMap<>();
        queueMessages.put("10720", "[{\"type\":\"queueName\",\"data\":\"NEW_URL_HTML_EN_11720\"},{\"type\":\"delayInSeconds\",\"data\":\"0\"},{\"type\":\"maxConcurrentThreads\",\"data\":\"1\"},{\"type\":\"userAgent\",\"data\":\"LLF-seoclarity23\"},{\"type\":\"enableJavascriptCrawl\",\"data\":\"false\"},{\"type\":\"region\",\"data\":\"New York\"},{\"type\":\"pageCrawlerApiRequestHeader-Accept\",\"data\":\"*/*\"},{\"type\":\"pageCrawlerApiRequestHeader-Accept-Encoding\",\"data\":\"*\"},{\"type\":\"pageCrawlerApiRequestHeader-Accept-Language\",\"data\":\"*\"}]");
        SQSUtils.getInstance().sendBatchMessageToFifoQueue(queueUrl, queueMessages);
    }


    public void testGetQueueListByQueueNamePrefix() throws Exception {
        final SQSUtils sqsUtils = SQSUtils.getInstance();
        final ListQueuesResult queueListByQueueNamePrefix = sqsUtils.getQueueListByQueueNamePrefix("TARGET_URL_DEAD_QUEUES_", 1000, null);
        if (queueListByQueueNamePrefix.getNextToken() != null) {
            final ListQueuesResult queueListByQueueNamePrefix1 = sqsUtils.getQueueListByQueueNamePrefix(IConstants.QUEUE_NAME_TARGET_URL_DEAD_QUEUES_PREFIX, 1000, queueListByQueueNamePrefix.getNextToken());
            queueListByQueueNamePrefix.getQueueUrls().addAll(queueListByQueueNamePrefix1.getQueueUrls());
        }
        final List<String> queueUrls = queueListByQueueNamePrefix.getQueueUrls();
        logger.info("queueListByQueueNamePrefix size: {}", queueUrls.size());
        final List<QueueMessages> messages = queueUrls.parallelStream().map(queueUrl -> {
            try {
//                sqsUtils.purgeQueue(queueUrl);
                final Integer[] approximateNumberOfMessagesAndInflightByQueueUrl = sqsUtils.getApproximateNumberOfMessagesAndInflightByQueueUrl(queueUrl);
                final QueueMessages queueMessages = new QueueMessages();
                queueMessages.setMessages(approximateNumberOfMessagesAndInflightByQueueUrl[0]);
                queueMessages.setQueueUrl(queueUrl);
                queueMessages.setInFlight(approximateNumberOfMessagesAndInflightByQueueUrl[1]);
                return queueMessages;
            } catch (Exception e) {
                logger.error("queueUrl: {}, error: {}", queueUrl, e.getMessage());
                return null;
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());
        final List<QueueMessages> filterMessages = messages.stream()
                .parallel()
                .sorted(Comparator.comparingInt(QueueMessages::getMessages).reversed())
                .filter(queueMessages -> queueMessages.getMessages() > 0 || queueMessages.getInFlight() > 0)
                .collect(Collectors.toList());
        final long count = messages.parallelStream().filter(message -> message.getMessages() > 0).count();
        final long count1 = messages.parallelStream().filter(message -> message.getMessages() == 0 && message.getInFlight() > 0).count();
        logger.info("filterMessages size: {}, have messages: {}, inFlight: {}", filterMessages.size(), count, count1);
        filterMessages
//                .limit(10)
                .forEach(queueMessages -> {
            logger.info("queueUrl: {}, messages: {}, inFlight: {}", queueMessages.getQueueUrl(), queueMessages.getMessages(), queueMessages.getInFlight());
        });
//        Collections.reverse(queueUrls);
    }

    public void testQueue4() throws Exception {
        final SQSUtils sqsUtils = SQSUtils.getInstance();
        final String queueUrl = sqsUtils.createQueue("");
        ExecutorService executor = Executors.newFixedThreadPool(20);
        LinkedBlockingQueue<String> queueNameQueue = new LinkedBlockingQueue<>();
        Gson gson = new Gson();
        while (true) {
            final List<Message> messageFromQueue = sqsUtils.getMessageFromQueue(queueUrl, 10, 120);
            if (messageFromQueue == null || messageFromQueue.isEmpty()) {
                break;
            }
            messageFromQueue.forEach(message -> queueNameQueue.add(message.getBody()));
//            executor.submit(() -> {
//                final List<String> queueNames = messageFromQueue.stream().map(Message::getBody).map(message -> {
//                    final TargetUrlDeadQueueDTO targetUrlDeadQueueDTO = gson.fromJson(message, TargetUrlDeadQueueDTO.class);
//                    final DomainCrawlParameters domainCrawlParameters = targetUrlDeadQueueDTO.getDomainCrawlParameters();
//                    return domainCrawlParameters.getQueueName();
//                }).collect(Collectors.toList());
//                queueNameQueue.addAll(queueNames);
//            });
            queueNameQueue.forEach(System.out::println);
        }
//        executor.shutdown();
//        System.out.println(queueNameQueue.size());
//        final List<String> notTargetUrls = queueNameQueue.stream().parallel().filter(queueName -> !queueName.startsWith("TARGET_URL_")).collect(Collectors.toList());
//        System.out.println(notTargetUrls.size());
//        notTargetUrls.forEach(System.out::println);
    }

    public void testPurgeQueue() throws Exception {
        final SQSUtils sqsUtils = SQSUtils.getInstance();
        final String queueUrl = sqsUtils.createQueue("NEW_URL_HTML_EN_4765");
        sqsUtils.purgeQueue(queueUrl);
    }

    public void test() throws Exception {
        final SQSUtils instance = SQSUtils.getInstance();
        final String queueUrl = instance.createQueue("NEW_URL_HTML_EN_0");
        instance.deleteQueueByUrl(queueUrl);
    }

    static class QueueMessages {
        int messages;

        int inFlight;
        String queueUrl;

        public int getInFlight() {
            return inFlight;
        }

        public void setInFlight(int inFlight) {
            this.inFlight = inFlight;
        }

        public int getMessages() {
            return messages;
        }

        public void setMessages(int messages) {
            this.messages = messages;
        }

        public String getQueueUrl() {
            return queueUrl;
        }

        public void setQueueUrl(String queueUrl) {
            this.queueUrl = queueUrl;
        }
    }


}
