package com.actonia.utils;

import com.actonia.IConstants;
import com.actonia.entity.AdditionalContentEntity;
import com.actonia.value.object.ScrapyCrawlerRequest;
import com.actonia.value.object.ScrapyCrawlerResponse;
import com.google.gson.Gson;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runners.MethodSorters;
import org.springframework.core.annotation.Order;

import java.util.*;

import static org.junit.Assert.*;

public class ScrapyApiUtilsTest {

    private final ScrapyApiUtils scrapyApiUtils = ScrapyApiUtils.getInstance();

    private static final int domainId = 6783;

    private static final String url = "https://www.myprotein.com/thezone/nutrition/how-many-calories-should-you-eat-to-lose-weight/";

    private static final String userAgent = "Mozilla/5.0 (compatible; ClarityBot/9.0; +https://www.seoclarity.net/bot.html)";

    private static final AdditionalContentEntity additionalContentEntity;

    private static final List<AdditionalContentEntity> additionalContentEntities = new ArrayList<>();

    private static final String region = "New York";

    private static final boolean enableJavascriptCrawl = true;

    private static final Integer javascriptTimeoutInSecond = 10;

    private static final int crawlType = 1;

    private static final Map<String, String> pageCrawlerApiRequestHeaders = new HashMap<>();

    private static final Gson gson = new Gson();

    private static final CrawlerUtils crawlerUtils;

    private static final String endpoint;

    private ScrapyCrawlerRequest scrapyCrawlerRequest;

    static {
        additionalContentEntity = new AdditionalContentEntity();
        additionalContentEntity.setSelector("//div[@class='productDescription_synopsisContent']");
        additionalContentEntity.setDomainId(domainId);
        additionalContentEntity.setSelectorType(1);
        additionalContentEntity.setUrlSelector(".html");
        additionalContentEntity.setUrlSelectorType(4);
        additionalContentEntities.add(additionalContentEntity);
        pageCrawlerApiRequestHeaders.put("Accept", "*/*");
        pageCrawlerApiRequestHeaders.put("Accept-Encoding", "*");
        pageCrawlerApiRequestHeaders.put("Accept-Language", "*");
        try {
            crawlerUtils = CrawlerUtils.getInstance();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        endpoint = ScrapyApiUtils.getEndpoint(region);
        assertEquals(PoliteCrawlConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_SCRAPY_CRAWLER_ENDPOINTS).split(",")[0], endpoint);
    }

    @Test
    public void getInstance() {
        assertNotNull(scrapyApiUtils);
    }

    @Test
    @Before
    public void getScrapyCrawlerRequest() throws Exception {
        scrapyCrawlerRequest = scrapyApiUtils.getScrapyCrawlerRequest(domainId, url, userAgent, additionalContentEntities, enableJavascriptCrawl, javascriptTimeoutInSecond, crawlType, pageCrawlerApiRequestHeaders, false, null, endpoint);
    }

    @Test
    public void checkIfUrlSelected() throws Exception {
        final boolean b = scrapyApiUtils.checkIfUrlSelected(url, additionalContentEntity);
        final boolean b1 = CrawlerUtils.getInstance().checkIfUrlSelected(url, additionalContentEntity);
        assertEquals(b, b1);
    }

    @Test
    public void getScrapyCrawlerResponse() throws Exception {
        final String json = gson.toJson(scrapyCrawlerRequest);
        final ScrapyCrawlerResponse scrapyCrawlerResponse = crawlerUtils.getScrapyFormattedResponse(null, "TARGET_URL_HTML_EN_9783", url, userAgent, additionalContentEntities, enableJavascriptCrawl, javascriptTimeoutInSecond, crawlType, pageCrawlerApiRequestHeaders, false, "", false, region);
        final ScrapyCrawlerResponse response = scrapyApiUtils.getScrapyCrawlerResponse(scrapyCrawlerRequest, crawlType, domainId, endpoint);
        assertEquals(scrapyCrawlerResponse.getHtml(), response.getHtml());
        assertEquals(scrapyCrawlerResponse, response);
    }
}
