package com.actonia.utils;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.PutObjectResult;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import org.junit.Test;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.Assert.*;

public class CrawlHtmlSeagateUtilsTest {

    private final AmazonS3 s3client = CrawlHtmlSeagateUtils.s3client;

    @Test
    public void test() throws IOException {
        final PutObjectResult putObjectResult = s3client.putObject(CrawlHtmlSeagateUtils.bucketName, String.format("aaaaaa/test"), "test content111");
        final S3Object object = s3client.getObject(CrawlHtmlSeagateUtils.bucketName, "aaaaaa/Untitled-1.py");
        final S3ObjectInputStream s3ObjectInputStream = object.getObjectContent();
        BufferedReader reader = new BufferedReader(new InputStreamReader(s3ObjectInputStream));
        final String s = reader.readLine();
        System.out.println(s);

    }

}
