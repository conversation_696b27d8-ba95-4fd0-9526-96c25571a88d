package com.actonia.utils;

import com.actonia.IConstants;
import org.apache.commons.lang.StringUtils;
import org.junit.Test;

import java.util.Arrays;
import java.util.concurrent.ConcurrentLinkedQueue;

import static org.junit.Assert.*;

public class CrawlerUtilsTest {


    @Test
    public void test() {
        ConcurrentLinkedQueue<String> queue = new ConcurrentLinkedQueue<>();
        queue.add(null);
        for (int i = 0; i < 1000000; i++) {
            final String poll = queue.poll();
            System.out.println(poll);
        }
        String scrapyCrawlerEndpoints = PoliteCrawlConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_SCRAPY_CRAWLER_ENDPOINTS);
        String[] scrapyCrawlerEndpointArray = new String[0];
        if (StringUtils.isNotBlank(scrapyCrawlerEndpoints)) {
            scrapyCrawlerEndpointArray = scrapyCrawlerEndpoints.split(IConstants.COMMA);
            if (scrapyCrawlerEndpointArray != null && scrapyCrawlerEndpointArray.length > 0) {
                FormatUtils.getInstance().logMemoryUsage("CrawlerUtils() scrapyCrawlerEndpointArray=" + Arrays.toString(scrapyCrawlerEndpointArray));
            } else {
                FormatUtils.getInstance().logMemoryUsage("CrawlerUtils() scrapyCrawlerEndpointArray is empty.");
            }
        }
        System.out.println(scrapyCrawlerEndpointArray[1]);
    }

}
