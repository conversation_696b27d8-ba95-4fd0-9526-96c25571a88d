package com.actonia.utils;

import com.actonia.IConstants;
import com.actonia.dao.ClickHouseCrawlDatabaseConfigurations;
import com.actonia.dao.CompetitorUrlHtmlTransferClickHouseDAO;
import com.actonia.dao.ContentGuardTransferClickHouseDAO;
import com.actonia.dao.TargetUrlCustomDataTransferClickHouseDAO;
import com.actonia.dao.TargetUrlHtmlDailyTransferClickHouseDAO;
import com.actonia.dao.TargetUrlHtmlFileNameTransferClickHouseDAO;
import com.actonia.dao.TargetUrlHtmlTransferClickHouseDAO;

public class TransferUtils {

	private static TransferUtils transferUtils;

	public TransferUtils() {
		super();
	}

	public static TransferUtils getInstance() {
		if (transferUtils == null) {
			transferUtils = new TransferUtils();
		}
		return transferUtils;
	}

	public TargetUrlHtmlTransferClickHouseDAO getTargetUrlHtmlTransferClickHouseDAO(String databaseConnectionUrl) {
		FormatUtils.getInstance().logMemoryUsage("getTargetUrlHtmlTransferClickHouseDAO() begins. databaseConnectionUrl=" + databaseConnectionUrl);
		TargetUrlHtmlTransferClickHouseDAO targetUrlHtmlTransferClickHouseDAO = null;
		String[] clickHouseDatabaseHostnameArray = new String[] { databaseConnectionUrl };
		String clickHouseDatabasePort = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_PORT);
		String clickHouseDatabaseName = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_NAME);
		String clickHouseDatabaseUser = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_USER);
		String clickHouseDatabasePassword = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_PASSWORD);
		int clickHouseBatchCreationSize = ClickHouseCrawlDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_BATCH_CREATION_SIZE, 100);
		int clickHouseconnectionTimeoutInMilliseconds = ClickHouseCrawlDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_CONNECTION_TIMEOUT_MILLISECONDS, 88888);
		int clickHouseMaximumRetryCounts = ClickHouseCrawlDatabaseConfigurations.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_MAX_RETRY_COUNTS,
				8);
		int clickHouseRetryWaitMilliseconds = ClickHouseCrawlDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_RETRY_WAIT_MILLISECONDS, 1000);
		try {
			targetUrlHtmlTransferClickHouseDAO = new TargetUrlHtmlTransferClickHouseDAO(clickHouseDatabaseHostnameArray, clickHouseDatabasePort, clickHouseDatabaseName,
					clickHouseBatchCreationSize, clickHouseDatabaseUser, clickHouseDatabasePassword, clickHouseconnectionTimeoutInMilliseconds,
					clickHouseMaximumRetryCounts, clickHouseRetryWaitMilliseconds);
		} catch (Exception e) {
			e.printStackTrace();
		}

		FormatUtils.getInstance()
				.logMemoryUsage("getTargetUrlHtmlTransferClickHouseDAO() ends. targetUrlHtmlTransferClickHouseDAO=" + targetUrlHtmlTransferClickHouseDAO.toString());
		return targetUrlHtmlTransferClickHouseDAO;
	}

	public ContentGuardTransferClickHouseDAO getContentGuardTransferClickHouseDAO(String databaseConnectionUrl) {
		FormatUtils.getInstance().logMemoryUsage("getContentGuardTransferClickHouseDAO() begins. databaseConnectionUrl=" + databaseConnectionUrl);
		ContentGuardTransferClickHouseDAO contentGuardTransferClickHouseDAO = null;
		String[] clickHouseDatabaseHostnameArray = new String[] { databaseConnectionUrl };
		String clickHouseDatabasePort = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_PORT);
		String clickHouseDatabaseName = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_NAME);
		String clickHouseDatabaseUser = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_USER);
		String clickHouseDatabasePassword = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_PASSWORD);
		int clickHouseBatchCreationSize = ClickHouseCrawlDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_BATCH_CREATION_SIZE, 100);
		int clickHouseconnectionTimeoutInMilliseconds = ClickHouseCrawlDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_CONNECTION_TIMEOUT_MILLISECONDS, 88888);
		int clickHouseMaximumRetryCounts = ClickHouseCrawlDatabaseConfigurations.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_MAX_RETRY_COUNTS,
				8);
		int clickHouseRetryWaitMilliseconds = ClickHouseCrawlDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_RETRY_WAIT_MILLISECONDS, 1000);
		try {
			contentGuardTransferClickHouseDAO = new ContentGuardTransferClickHouseDAO(clickHouseDatabaseHostnameArray, clickHouseDatabasePort, clickHouseDatabaseName,
					clickHouseBatchCreationSize, clickHouseDatabaseUser, clickHouseDatabasePassword, clickHouseconnectionTimeoutInMilliseconds,
					clickHouseMaximumRetryCounts, clickHouseRetryWaitMilliseconds);
		} catch (Exception e) {
			e.printStackTrace();
		}

		FormatUtils.getInstance()
				.logMemoryUsage("getContentGuardTransferClickHouseDAO() ends. contentGuardTransferClickHouseDAO=" + contentGuardTransferClickHouseDAO.toString());
		return contentGuardTransferClickHouseDAO;
	}

	public CompetitorUrlHtmlTransferClickHouseDAO getCompetitorUrlHtmlTransferClickHouseDAO(String databaseConnectionUrl) {
		FormatUtils.getInstance().logMemoryUsage("getCompetitorUrlHtmlTransferClickHouseDAO() begins. databaseConnectionUrl=" + databaseConnectionUrl);
		CompetitorUrlHtmlTransferClickHouseDAO competitorUrlHtmlTransferClickHouseDAO = null;
		String[] clickHouseDatabaseHostnameArray = new String[] { databaseConnectionUrl };
		String clickHouseDatabasePort = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_PORT);
		String clickHouseDatabaseName = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_NAME);
		String clickHouseDatabaseUser = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_USER);
		String clickHouseDatabasePassword = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_PASSWORD);
		int clickHouseBatchCreationSize = ClickHouseCrawlDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_BATCH_CREATION_SIZE, 100);
		int clickHouseconnectionTimeoutInMilliseconds = ClickHouseCrawlDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_CONNECTION_TIMEOUT_MILLISECONDS, 88888);
		int clickHouseMaximumRetryCounts = ClickHouseCrawlDatabaseConfigurations.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_MAX_RETRY_COUNTS,
				8);
		int clickHouseRetryWaitMilliseconds = ClickHouseCrawlDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_RETRY_WAIT_MILLISECONDS, 1000);
		try {
			competitorUrlHtmlTransferClickHouseDAO = new CompetitorUrlHtmlTransferClickHouseDAO(clickHouseDatabaseHostnameArray, clickHouseDatabasePort,
					clickHouseDatabaseName, clickHouseBatchCreationSize, clickHouseDatabaseUser, clickHouseDatabasePassword, clickHouseconnectionTimeoutInMilliseconds,
					clickHouseMaximumRetryCounts, clickHouseRetryWaitMilliseconds);
		} catch (Exception e) {
			e.printStackTrace();
		}

		FormatUtils.getInstance().logMemoryUsage(
				"getCompetitorUrlHtmlTransferClickHouseDAO() ends. competitorUrlHtmlTransferClickHouseDAO=" + competitorUrlHtmlTransferClickHouseDAO.toString());
		return competitorUrlHtmlTransferClickHouseDAO;
	}

	public TargetUrlHtmlFileNameTransferClickHouseDAO getTargetUrlHtmlFileNameTransferClickHouseDAO(String databaseConnectionUrl) {
		FormatUtils.getInstance().logMemoryUsage("getTargetUrlHtmlFileNameTransferClickHouseDAO() begins. databaseConnectionUrl=" + databaseConnectionUrl);
		TargetUrlHtmlFileNameTransferClickHouseDAO targetUrlHtmlFileNameTransferClickHouseDAO = null;
		String[] clickHouseDatabaseHostnameArray = new String[] { databaseConnectionUrl };
		String clickHouseDatabasePort = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_PORT);
		String clickHouseDatabaseName = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_NAME);
		String clickHouseDatabaseUser = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_USER);
		String clickHouseDatabasePassword = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_PASSWORD);
		int clickHouseBatchCreationSize = ClickHouseCrawlDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_BATCH_CREATION_SIZE, 100);
		int clickHouseconnectionTimeoutInMilliseconds = ClickHouseCrawlDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_CONNECTION_TIMEOUT_MILLISECONDS, 88888);
		int clickHouseMaximumRetryCounts = ClickHouseCrawlDatabaseConfigurations.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_MAX_RETRY_COUNTS,
				8);
		int clickHouseRetryWaitMilliseconds = ClickHouseCrawlDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_RETRY_WAIT_MILLISECONDS, 1000);
		try {
			targetUrlHtmlFileNameTransferClickHouseDAO = new TargetUrlHtmlFileNameTransferClickHouseDAO(clickHouseDatabaseHostnameArray, clickHouseDatabasePort,
					clickHouseDatabaseName, clickHouseBatchCreationSize, clickHouseDatabaseUser, clickHouseDatabasePassword, clickHouseconnectionTimeoutInMilliseconds,
					clickHouseMaximumRetryCounts, clickHouseRetryWaitMilliseconds);
		} catch (Exception e) {
			e.printStackTrace();
		}

		FormatUtils.getInstance().logMemoryUsage("getTargetUrlHtmlFileNameTransferClickHouseDAO() ends. targetUrlHtmlFileNameTransferClickHouseDAO="
				+ targetUrlHtmlFileNameTransferClickHouseDAO.toString());
		return targetUrlHtmlFileNameTransferClickHouseDAO;
	}

	public TargetUrlHtmlDailyTransferClickHouseDAO getTargetUrlHtmlDailyTransferClickHouseDAO(String databaseConnectionUrl) {
		FormatUtils.getInstance().logMemoryUsage("getTargetUrlHtmlDailyTransferClickHouseDAO() begins. databaseConnectionUrl=" + databaseConnectionUrl);
		TargetUrlHtmlDailyTransferClickHouseDAO targetUrlHtmlDailyTransferClickHouseDAO = null;
		String[] clickHouseDatabaseHostnameArray = new String[] { databaseConnectionUrl };
		String clickHouseDatabasePort = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_PORT);
		String clickHouseDatabaseName = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_NAME);
		String clickHouseDatabaseUser = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_USER);
		String clickHouseDatabasePassword = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_PASSWORD);
		int clickHouseBatchCreationSize = ClickHouseCrawlDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_BATCH_CREATION_SIZE, 100);
		int clickHouseconnectionTimeoutInMilliseconds = ClickHouseCrawlDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_CONNECTION_TIMEOUT_MILLISECONDS, 88888);
		int clickHouseMaximumRetryCounts = ClickHouseCrawlDatabaseConfigurations.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_MAX_RETRY_COUNTS,
				8);
		int clickHouseRetryWaitMilliseconds = ClickHouseCrawlDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_RETRY_WAIT_MILLISECONDS, 1000);
		try {
			targetUrlHtmlDailyTransferClickHouseDAO = new TargetUrlHtmlDailyTransferClickHouseDAO(clickHouseDatabaseHostnameArray, clickHouseDatabasePort,
					clickHouseDatabaseName, clickHouseBatchCreationSize, clickHouseDatabaseUser, clickHouseDatabasePassword, clickHouseconnectionTimeoutInMilliseconds,
					clickHouseMaximumRetryCounts, clickHouseRetryWaitMilliseconds);
		} catch (Exception e) {
			e.printStackTrace();
		}

		FormatUtils.getInstance().logMemoryUsage(
				"getTargetUrlHtmlDailyTransferClickHouseDAO() ends. targetUrlHtmlDailyTransferClickHouseDAO=" + targetUrlHtmlDailyTransferClickHouseDAO.toString());
		return targetUrlHtmlDailyTransferClickHouseDAO;
	}

	public TargetUrlCustomDataTransferClickHouseDAO getTargetUrlCustomDataTransferClickHouseDAO(String databaseConnectionUrl) {
		FormatUtils.getInstance().logMemoryUsage("getTargetUrlCustomDataTransferClickHouseDAO() begins. databaseConnectionUrl=" + databaseConnectionUrl);
		TargetUrlCustomDataTransferClickHouseDAO targetUrlCustomDataTransferClickHouseDAO = null;
		String[] clickHouseDatabaseHostnameArray = new String[] { databaseConnectionUrl };
		String clickHouseDatabasePort = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_PORT);
		String clickHouseDatabaseName = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_NAME);
		String clickHouseDatabaseUser = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_USER);
		String clickHouseDatabasePassword = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_PASSWORD);
		int clickHouseBatchCreationSize = ClickHouseCrawlDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_BATCH_CREATION_SIZE, 100);
		int clickHouseconnectionTimeoutInMilliseconds = ClickHouseCrawlDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_CONNECTION_TIMEOUT_MILLISECONDS, 88888);
		int clickHouseMaximumRetryCounts = ClickHouseCrawlDatabaseConfigurations.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_MAX_RETRY_COUNTS,
				8);
		int clickHouseRetryWaitMilliseconds = ClickHouseCrawlDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_RETRY_WAIT_MILLISECONDS, 1000);
		try {
			targetUrlCustomDataTransferClickHouseDAO = new TargetUrlCustomDataTransferClickHouseDAO(clickHouseDatabaseHostnameArray, clickHouseDatabasePort,
					clickHouseDatabaseName, clickHouseBatchCreationSize, clickHouseDatabaseUser, clickHouseDatabasePassword, clickHouseconnectionTimeoutInMilliseconds,
					clickHouseMaximumRetryCounts, clickHouseRetryWaitMilliseconds);
		} catch (Exception e) {
			e.printStackTrace();
		}

		FormatUtils.getInstance().logMemoryUsage(
				"getTargetUrlCustomDataTransferClickHouseDAO() ends. targetUrlCustomDataTransferClickHouseDAO=" + targetUrlCustomDataTransferClickHouseDAO.toString());
		return targetUrlCustomDataTransferClickHouseDAO;
	}
}
