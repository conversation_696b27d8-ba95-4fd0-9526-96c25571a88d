/**
 * 
 */
package com.actonia.utils;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.net.UnknownHostException;

import org.apache.commons.lang.StringUtils;

import com.google.common.net.InternetDomainName;


/**
 * com.actonia.saas.htmlparser.URLConnectionUtils.java
 *
 * @version $Revision:$
 *          $Author:$
 */
public class URLConnectionUtils {

	/**
	 * get HttpURLConnection by url String
	 * @param httpUrl
	 * @return
	 * @throws MalformedURLException
	 */
	public static HttpURLConnection doHttpConnect(String httpUrl) throws MalformedURLException {
		if (StringUtils.isBlank(httpUrl)) {
			return null;
		}
		
		URL url = new URL(fixSpaces(httpUrl.trim()));
		return doHttpConnect(url);
	}
	
	/**
	 * get HttpURLConnection by URL object
	 * @param url
	 * @return
	 */
	private static HttpURLConnection doHttpConnect(URL url) {
		if (url == null) {
			return null;
		}
		
		try {
			HttpURLConnection http = null;
			URLConnection conn = url.openConnection();
			
			if (conn instanceof HttpURLConnection) {
                http = (HttpURLConnection)conn;
                http.setInstanceFollowRedirects (false);
                
    			try {
    				http.connect ();
                    return http;
                } catch (UnknownHostException uhe) {
                    int message = (int)(Math.random () * FOUR_OH_FOUR.length);
                    System.out.println(FOUR_OH_FOUR[message]);
                    //throw new ParserException (FOUR_OH_FOUR[message], uhe);
                } catch (IOException ioe) {
                	System.out.println(ioe.getMessage ());
                    //throw new ParserException (ioe.getMessage (), ioe);
                }
            }
			
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
		return null;
	}
	
	/**
	 * copy from htmlparser.jar
	 * 
	 * Get the Location field if any.
	 * @param http
	 * @return
	 */
	public static String getRedirectUrl(HttpURLConnection http) {
        String key;
        String value;
        String ret;

        ret = null;

        for (int i = 0; ((null == ret) && (null != (value = http.getHeaderField (i)))); i++)
            if ((null != (key = http.getHeaderFieldKey (i))) && (key.equalsIgnoreCase ("Location")))
                ret = value;

        return (ret);
    }
	
	/**
	 * is response code 
	 * @param responseCode
	 * @return
	 * @throws IOException
	 */
	public static boolean isRedirectResponse(int responseCode) {
		return 3 == (responseCode / 100);
	}
	
	/**
	 * check response code 200, 301, 302
	 * @param responseCode
	 * @return
	 */
	public static boolean isResponseAvailable(int responseCode) {
		return responseCode == 200 || isRedirectResponse(responseCode);
	}
	
    /**
     * Copy from HtmlPaeser.jar
     * 
     * Turn spaces into %20.
     * ToDo: make this more generic
     * (see RFE #1010593 provide URL encoding/decoding utilities).
     * @param url The url containing spaces.
     * @return The URL with spaces as %20 sequences.
     */
    public static String fixSpaces (String url) {
        int index;
        int length;
        char ch;
        StringBuffer buffer;

        index = url.indexOf (' ');
        if (-1 != index)
        {
            length = url.length ();
            buffer = new StringBuffer (length * 3);
            buffer.append (url.substring (0, index));
            for (int i = index; i < length; i++)
            {
                ch = url.charAt (i);
                if (ch==' ')
                    buffer.append ("%20");
                else
                    buffer.append (ch);
            }
            url = buffer.toString ();
        }

        return (url);
    }
    
    /**
     * copy from htmlparser.jar 
     */
    private static final String[] FOUR_OH_FOUR =
    {
        "The web site you seek cannot be located,"
            + " but countless more exist",
        "You step in the stream, but the water has moved on."
            + " This page is not here.",
        "Yesterday the page existed. Today it does not."
            + " The internet is like that.",
        "That page was so big. It might have been very useful."
            + " But now it is gone.",
        "Three things are certain: death, taxes and broken links."
            + " Guess which has occured.",
        "Chaos reigns within. Reflect, repent and enter the correct URL."
            + " Order shall return.",
        "Stay the patient course. Of little worth is your ire."
            + " The page is not found.",
        "A non-existant URL reduces your expensive computer to a simple stone.",
        "Many people have visited that page."
            + " Today, you are not one of the lucky ones.",
        "Cutting the wind with a knife. Bookmarking a URL."
            + " Both are ephemeral.",
    };
    
    /**
     * parse domain form a url link
     * @param url
     * @param remove3w  remove 'www.' if true
     * @return
     */
    public static String getDomainByUrl(String url, boolean remove3w, Object... domainId) {
		String result = url;
		
		//remove 'http://'
		if (StringUtils.contains(result, "://")) {
			result = StringUtils.substringAfter(result, "://");
		}
		
		//remove the string after domain
		result = StringUtils.substringBefore(result, "/");
		
		//remove 'www.' if the domain string contains it
		if (remove3w && StringUtils.isNotBlank(result) && result.startsWith("www.")) {
			result = StringUtils.substringAfter(result, "www.");
		} 
		// disabled by Alps (domain.com.au)
//		else if (remove3w && StringUtils.isNotBlank(result) && !result.startsWith("www.") && StringUtils.countMatches(result, ".") >=2) {
//			result = StringUtils.substringAfter(result, ".");
//		}
		// added by Alps (testing purpose )
		// https://www.wrike.com/open.htm?id=132115351
		else if(remove3w && StringUtils.isNotBlank(result) && !result.startsWith("www.") && domainId !=null && domainId.length==1) {
			InternetDomainName internetDomainName = InternetDomainName.from(result);
			String domain = internetDomainName.topPrivateDomain().toString();
			if(StringUtils.isNotBlank(domain)) {
				return domain;
			}
		}
		return result;
	}

    public static boolean isUrlBelongDomain(String url, String domain) {
//    	String urlDomain = getDomainByUrl(url, true);
//    	
//    	if (StringUtils.equalsIgnoreCase(urlDomain, domain)) {
//    		return true;
//    	}
//    	
//		if (StringUtils.isNotBlank(domain) && domain.startsWith("www.")) {
//			domain = StringUtils.substringAfter(domain, "www.");
//		}
//    	
//		return StringUtils.equalsIgnoreCase(urlDomain, domain);
    	return isSameDomain(url, domain);
    }
    
    public static boolean isSameDomain(String sourceDomain, String ownDomain) {
		sourceDomain = URLConnectionUtils.getDomainByUrl(sourceDomain, true);
		ownDomain = URLConnectionUtils.getDomainByUrl(ownDomain, true);
		if (StringUtils.equalsIgnoreCase(sourceDomain, ownDomain)) {
			return true;
		}
		if (StringUtils.endsWithIgnoreCase(sourceDomain, "." + ownDomain)) {
			return true;
		}
		return false;
	}
    
    /**
     * RegularEx for url format
     */
    private static final String URL_REGULAREX = "^((https|http|ftp|rtsp|mms)://)"
	  		+ "(([0-9a-z_!~*'().&=+$%-]+: )?[0-9a-z_!~*'().&=+$%-]+@)?" 
	        + "(([0-9]{1,3}\\.){3}[0-9]{1,3}" //ip
	        + "|" 
	        + "([0-9a-z_!~*'()-]+\\.)*" // domain www. 
	        + "([0-9a-z][0-9a-z-]{0,61})?[0-9a-z]\\." // second level domain
	        + "[a-z]{2,6})" // first level domain
	        + "(:[0-9]{1,4})?" // port
    		+ "((/?)|"
    		+ "(/[0-9a-z_!~*'().;?:@&=+$,%#-]+)+/?)$";
    
    /**
     * check if the url string is a right format
     * @return
     */
    public static boolean isURL(String url){
		if (StringUtils.isBlank(url)) {
			return false;
		}
		return url.toLowerCase().matches(URL_REGULAREX);
	}
}
