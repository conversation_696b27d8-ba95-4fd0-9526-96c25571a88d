package com.actonia.utils;

import java.util.Properties;

public class GoogleTagUtils {

	private static GoogleTagUtils googleTagUtils;

	public static GoogleTagUtils getInstance() {
		if (googleTagUtils == null) {
			googleTagUtils = new GoogleTagUtils();
		}
		return googleTagUtils;
	}

	public String getGoogleAccessToken(String runtimePropertyName) {
		//FormatUtils.getInstance().logMemoryUsage("getGoogleAccessToken() begins. runtimePropertyName=" + runtimePropertyName);
		String googleAccessToken = null;
		Properties properties = null;
		try {
			properties = new Properties();
			properties.load(GoogleTagUtils.class.getResourceAsStream("/google_tag.properties"));
			googleAccessToken = properties.getProperty(runtimePropertyName);
		} catch (Exception e) {
			e.printStackTrace();
			FormatUtils.getInstance().logMemoryUsage("getGoogleAccessToken() exception message=" + e.getMessage());
			System.exit(-1);
		}
		//FormatUtils.getInstance().logMemoryUsage("getGoogleAccessToken() ends. googleAccessToken=" + googleAccessToken);
		return googleAccessToken;
	}
}
