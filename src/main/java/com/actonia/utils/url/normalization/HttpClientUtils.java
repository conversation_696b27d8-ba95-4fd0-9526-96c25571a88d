package com.actonia.utils.url.normalization;

import com.actonia.IConstants;
import com.actonia.utils.PoliteCrawlConfigurations;
import org.apache.http.Header;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicHeader;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Collection;

public class HttpClientUtils {
    private static final Logger logger = LogManager.getLogger(HttpClientUtils.class);
    private HttpClientUtils() {
        // private constructor to prevent instantiation
    }

    public static CloseableHttpClient getHttpClientInstance() {
        return HttpClientHolder.INSTANCE;
    }

    public static void closeHttpClient() {
        try {
            CloseableHttpClient httpClient = HttpClientHolder.INSTANCE;
            if (httpClient != null) {
                httpClient.close();
            }
        } catch (Exception e) {
            logger.error("An error occurred while closing HttpClient", e);
        }
    }

    private static class HttpClientHolder {
        private static final CloseableHttpClient INSTANCE;
        private static final int CONNECT_TIMEOUT_IN_SECONDS = 168;
        private static final int CONNECTION_REQUEST_TIMEOUT_IN_SECONDS = 168;
        private static final int SOCKET_TIMEOUT_IN_SECONDS = 168;

        static {
            try {
                INSTANCE = createHttpClient();
            } catch (NoSuchAlgorithmException | KeyStoreException | KeyManagementException e) {
                throw new RuntimeException(e);
            }
        }

        private static CloseableHttpClient createHttpClient() throws NoSuchAlgorithmException, KeyStoreException, KeyManagementException {

            // use the TrustSelfSignedStrategy to allow Self Signed Certificates
            SSLContext sslContext = SSLContextBuilder.create().loadTrustMaterial(new TrustSelfSignedStrategy()).build();

            // we can optionally disable hostname verification.
            // if you don't want to further weaken the security, you don't have to include this.
            HostnameVerifier allowAllHosts = new NoopHostnameVerifier();

            // create an SSL Socket Factory to use the SSLContext with the trust self signed certificate strategy
            // and allow all hosts verifier.
            SSLConnectionSocketFactory connectionFactory = new SSLConnectionSocketFactory(sslContext, allowAllHosts);

            final PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
            connectionManager.setMaxTotal(200);
            connectionManager.setDefaultMaxPerRoute(200);
            Collection<Header> defaultHeaders = new ArrayList<>(4);
            defaultHeaders.add(new BasicHeader(IConstants.CONTENT_TYPE, IConstants.APPLICATION_SLASH_JSON));
            defaultHeaders.add(new BasicHeader(IConstants.CONTENT_DASH_TYPE, IConstants.APPLICATION_SLASH_JSON));
            defaultHeaders.add(new BasicHeader(IConstants.ACCESS_KEY, PoliteCrawlConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_SCRAPY_CRAWLER_ACCESS_KEY)));
            defaultHeaders.add(new BasicHeader(IConstants.CACHE_CONTROL, IConstants.NO_CACHE));

            RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(CONNECT_TIMEOUT_IN_SECONDS * 1000)
                    .setConnectionRequestTimeout(CONNECTION_REQUEST_TIMEOUT_IN_SECONDS * 1000).setSocketTimeout(SOCKET_TIMEOUT_IN_SECONDS * 1000).build();

            return HttpClients.custom()
                    .setSSLSocketFactory(connectionFactory)
                    .setConnectionManager(connectionManager)
                    .setDefaultRequestConfig(requestConfig)
                    .setConnectionManagerShared(true)
                    .setDefaultHeaders(defaultHeaders)
                    .build();
        }
    }
}
