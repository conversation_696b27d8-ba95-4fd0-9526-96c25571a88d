package com.actonia.utils;

import java.io.File;
import java.io.FilenameFilter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.io.FileUtils;

import com.actonia.IConstants;

public class SimhashUtils {

	private static SimhashUtils simhashUtils;

	private SimhashUtils() throws Exception {
		super();
	}

	public static SimhashUtils getInstance() throws Exception {
		if (simhashUtils == null) {
			simhashUtils = new SimhashUtils();
		}
		return simhashUtils;
	}

	public Map<String, String> getKeyValueMap(String path) throws Exception {
		Map<String, String> keyValueMap = new HashMap<String, String>();
		List<String> stringList = null;
		StringBuilder stringBuilder = null;

		File[] allFiles = new File(path).listFiles(new FilenameFilter() {
			@Override
			public boolean accept(File dir, String name) {
				return !name.equals(".DS_Store");
			}
		});

		for (File file : allFiles) {
			stringList = FileUtils.readLines(file, "UTF-8");
			stringBuilder = new StringBuilder();
			if (stringList != null && stringList.size() > 0) {
				for (String testString : stringList) {
					//stringBuilder.append(testString).append(IConstants.EMPTY_STRING);
					stringBuilder.append(testString);
				}
			}
			keyValueMap.put(file.getName(), stringBuilder.toString());
		}

		return keyValueMap;
	}
}