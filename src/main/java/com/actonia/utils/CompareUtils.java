//package com.actonia.utils;
//
//import com.actonia.IConstants;
//import com.actonia.concurrency.CacheModleFactory;
//import com.actonia.dao.*;
//import com.actonia.entity.*;
//import com.actonia.exception.GatewayException;
//import com.actonia.polite.crawl.PoliteCrawl;
//import com.actonia.polite.crawl.UpdateTargetUrlChange;
//import com.actonia.value.object.*;
//import com.amazonaws.services.sqs.model.Message;
//import com.google.gson.Gson;
//import com.google.gson.JsonElement;
//import com.google.gson.JsonObject;
//import com.google.gson.JsonParser;
//import org.apache.commons.lang.BooleanUtils;
//import org.apache.commons.lang.StringUtils;
//import org.apache.commons.lang.math.NumberUtils;
//import org.apache.commons.lang.time.DateFormatUtils;
//import org.apache.commons.lang.time.DateUtils;
//import org.apache.http.Header;
//import org.apache.http.HttpEntity;
//import org.apache.http.client.config.RequestConfig;
//import org.apache.http.client.methods.CloseableHttpResponse;
//import org.apache.http.client.methods.HttpPost;
//import org.apache.http.conn.ssl.NoopHostnameVerifier;
//import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
//import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
//import org.apache.http.entity.StringEntity;
//import org.apache.http.impl.client.CloseableHttpClient;
//import org.apache.http.impl.client.HttpClients;
//import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
//import org.apache.http.message.BasicHeader;
//import org.apache.http.ssl.SSLContextBuilder;
//import org.apache.http.util.EntityUtils;
//import org.apache.logging.log4j.LogManager;
//import org.apache.logging.log4j.Logger;
//import org.springframework.web.util.UriUtils;
//
//import javax.net.ssl.HostnameVerifier;
//import javax.net.ssl.SSLContext;
//import java.io.File;
//import java.io.FileWriter;
//import java.io.FilenameFilter;
//import java.math.BigInteger;
//import java.net.*;
//import java.security.KeyManagementException;
//import java.security.KeyStoreException;
//import java.security.MessageDigest;
//import java.security.NoSuchAlgorithmException;
//import java.sql.ResultSet;
//import java.text.ParseException;
//import java.time.LocalDate;
//import java.time.format.DateTimeFormatter;
//import java.util.*;
//import java.util.concurrent.*;
//import java.util.ArrayList;
//import java.util.regex.Pattern;
//
//public class CompareUtils {
//
//    /**
//     * 简化版的trackChanges方法，只需要两个HtmlClickHouseEntity对象进行比较
//     * @param currentEntity 当前的HtmlClickHouseEntity对象（新爬取的数据）
//     * @param previousEntity 之前的HtmlClickHouseEntity对象（历史数据）
//     * @return 如果两个对象之间有差异，则返回true；否则返回false
//     */
//    public boolean trackChangesBetweenEntities(HtmlClickHouseEntity currentEntity, HtmlClickHouseEntity previousEntity) {
//        if (currentEntity == null || previousEntity == null) {
//            return false;
//        }
//
//        boolean isDifferent = false;
//        String ip = "localhost"; // 默认IP地址
//
//        // 获取响应码
//        String currentResponseCode = currentEntity.getCrawlerResponse().getResponse_code();
//        String previousResponseCode = previousEntity.getCrawlerResponse().getResponse_code();
//
//        // 检查响应码是否可用
//        if (StringUtils.isBlank(previousResponseCode) ||
//            StringUtils.equalsIgnoreCase(previousResponseCode, IConstants.RESPONSE_CODE_0)) {
//            return false;
//        }
//
//        // 比较响应码
//        if (!StringUtils.equals(currentResponseCode, previousResponseCode)) {
//            currentEntity.setResponseCodeChgInd(true);
//            isDifferent = true;
//
//            // 转换为整数以进行数值比较
//            int previousInteger = previousResponseCode != null ? NumberUtils.toInt(previousResponseCode) : 0;
//            int currentInteger = currentResponseCode != null ? NumberUtils.toInt(currentResponseCode) : 0;
//
//            // 404_detected_ind (当前是404且之前不是404)
//            if (currentInteger == 404 && previousInteger != 404) {
//                currentEntity.setResponseCode404DetectedInd(true);
//            }
//
//            // 404_removed_ind (当前不是404且之前是404)
//            if (currentInteger != 404 && previousInteger == 404) {
//                currentEntity.setResponseCode404RemovedInd(true);
//            }
//
//            // redirect_301_detected_ind (当前是301且之前不是3xx)
//            if (currentInteger == 301 && (previousInteger < 300 || previousInteger > 399)) {
//                currentEntity.setRedirect301DetectedInd(true);
//            }
//
//            // redirect_301_removed_ind (当前不是3xx且之前是301)
//            if ((currentInteger < 300 || currentInteger > 399) && previousInteger == 301) {
//                currentEntity.setRedirect301RemovedInd(true);
//            }
//
//            // redirect_302_detected_ind (当前是302且之前不是3xx)
//            if (currentInteger == 302 && (previousInteger < 300 || previousInteger > 399)) {
//                currentEntity.setRedirect302DetectedInd(true);
//            }
//
//            // redirect_302_removed_ind (当前不是3xx且之前是302)
//            if ((currentInteger < 300 || currentInteger > 399) && previousInteger == 302) {
//                currentEntity.setRedirect302RemovedInd(true);
//            }
//
//            // redirect_diff_code_ind (当前是3xx且之前是3xx但不相同)
//            if (previousInteger >= 300 && previousInteger <= 399 &&
//                currentInteger >= 300 && currentInteger <= 399 &&
//                previousInteger != currentInteger) {
//                currentEntity.setRedirectDiffCodeInd(true);
//            }
//
//            return isDifferent;
//        }
//
//        // 当响应码相同时，根据状态码类型进行不同的比较
//        Integer httpStatusCode = currentEntity.getHttpStatusCode();
//        if (httpStatusCode != null) {
//            // 3xx 重定向状态码
//            if (httpStatusCode >= 300 && httpStatusCode <= 399) {
//                // 比较重定向相关字段
//                if (!StringUtils.equals(currentEntity.getCrawlerResponse().getRedirect_blocked(),
//                               previousEntity.getCrawlerResponse().getRedirect_blocked())) {
//                    currentEntity.setRedirectBlockedChgInd(true);
//                    isDifferent = true;
//                }
//
//                if (!StringUtils.equals(currentEntity.getCrawlerResponse().getRedirect_blocked_reason(),
//                               previousEntity.getCrawlerResponse().getRedirect_blocked_reason())) {
//                    currentEntity.setRedirectBlockedReasonChgInd(true);
//                    isDifferent = true;
//                }
//
//                // 比较重定向链和最终URL的哈希值
//                String currentRedirectChainHash = getHashCodeFromArray(currentEntity.getChangeTrackingHashCdJsonArray(), IConstants.REDIRECT_CHAIN);
//                String previousRedirectChainHash = getHashCodeFromArray(previousEntity.getChangeTrackingHashCdJsonArray(), IConstants.REDIRECT_CHAIN);
//
//                if (!StringUtils.equalsIgnoreCase(currentRedirectChainHash, previousRedirectChainHash)) {
//                    currentEntity.setRedirectChainChgInd(true);
//                    isDifferent = true;
//
//                    // 设置特定重定向类型的变化标志
//                    if (httpStatusCode == 301) {
//                        currentEntity.setRedirect301ChgInd(true);
//                    }
//
//                    if (httpStatusCode == 302) {
//                        currentEntity.setRedirect302ChgInd(true);
//                    }
//                }
//
//                String currentRedirectFinalUrlHash = getHashCodeFromArray(currentEntity.getChangeTrackingHashCdJsonArray(), IConstants.REDIRECT_FINAL_URL);
//                String previousRedirectFinalUrlHash = getHashCodeFromArray(previousEntity.getChangeTrackingHashCdJsonArray(), IConstants.REDIRECT_FINAL_URL);
//
//                if (!StringUtils.equalsIgnoreCase(currentRedirectFinalUrlHash, previousRedirectFinalUrlHash)) {
//                    currentEntity.setRedirectFinalUrlChgInd(true);
//                    isDifferent = true;
//                }
//
//                if (!StringUtils.equals(currentEntity.getCrawlerResponse().getRedirect_flg(),
//                               previousEntity.getCrawlerResponse().getRedirect_flg())) {
//                    currentEntity.setRedirectFlgChgInd(true);
//                    isDifferent = true;
//                }
//
//                if (!StringUtils.equals(currentEntity.getCrawlerResponse().getRedirect_times(),
//                               previousEntity.getCrawlerResponse().getRedirect_times())) {
//                    currentEntity.setRedirectTimesChgInd(true);
//                    isDifferent = true;
//                }
//            }
//            // 200 成功状态码
//            else if (httpStatusCode == 200) {
//                // 比较页面内容相关字段
//
//                // 比较alternate_links
//                String currentAlternateLinksHash = getHashCodeFromArray(currentEntity.getChangeTrackingHashCdJsonArray(), IConstants.ALTERNATE_LINKS);
//                String previousAlternateLinksHash = getHashCodeFromArray(previousEntity.getChangeTrackingHashCdJsonArray(), IConstants.ALTERNATE_LINKS);
//
//                if (!StringUtils.equalsIgnoreCase(currentAlternateLinksHash, previousAlternateLinksHash)) {
//                    currentEntity.setAlternateLinksChgInd(true);
//                    isDifferent = true;
//                }
//
//                // 比较amphtml_flag和amphtml_href
//                if (!StringUtils.equals(currentEntity.getCrawlerResponse().getAmphtml_flag(),
//                               previousEntity.getCrawlerResponse().getAmphtml_flag())) {
//                    currentEntity.setAmphtmlFlagChgInd(true);
//                    isDifferent = true;
//                }
//
//                String currentAmphtmlHrefHash = getHashCodeFromArray(currentEntity.getChangeTrackingHashCdJsonArray(), IConstants.AMPHTML_HREF);
//                String previousAmphtmlHrefHash = getHashCodeFromArray(previousEntity.getChangeTrackingHashCdJsonArray(), IConstants.AMPHTML_HREF);
//
//                if (!StringUtils.equalsIgnoreCase(currentAmphtmlHrefHash, previousAmphtmlHrefHash)) {
//                    currentEntity.setAmphtmlHrefChgInd(true);
//                    isDifferent = true;
//                }
//
//                // 比较analyzed_url相关字段
//                if (!StringUtils.equals(currentEntity.getCrawlerResponse().getAnalyzed_url_flg_s(),
//                               previousEntity.getCrawlerResponse().getAnalyzed_url_flg_s())) {
//                    currentEntity.setAnalyzedUrlFlgSChgInd(true);
//                    isDifferent = true;
//                }
//
//                String currentAnalyzedUrlSHash = getHashCodeFromArray(currentEntity.getChangeTrackingHashCdJsonArray(), IConstants.ANALYZED_URL_S);
//                String previousAnalyzedUrlSHash = getHashCodeFromArray(previousEntity.getChangeTrackingHashCdJsonArray(), IConstants.ANALYZED_URL_S);
//
//                if (!StringUtils.equalsIgnoreCase(currentAnalyzedUrlSHash, previousAnalyzedUrlSHash)) {
//                    currentEntity.setAnalyzedUrlSChgInd(true);
//                    isDifferent = true;
//                }
//
//                // 比较archive相关字段
//                if (!StringUtils.equals(currentEntity.getCrawlerResponse().getArchive_flg(),
//                               previousEntity.getCrawlerResponse().getArchive_flg())) {
//                    currentEntity.setArchiveFlgChgInd(true);
//                    isDifferent = true;
//                }
//
//                if (!StringUtils.equals(currentEntity.getCrawlerResponse().getArchive_flg_x_tag(),
//                               previousEntity.getCrawlerResponse().getArchive_flg_x_tag())) {
//                    currentEntity.setArchiveFlgXTagChgInd(true);
//                    isDifferent = true;
//                }
//
//                // 比较blocked_by_robots
//                if (!StringUtils.equals(currentEntity.getCrawlerResponse().getBlocked_by_robots(),
//                               previousEntity.getCrawlerResponse().getBlocked_by_robots())) {
//                    currentEntity.setBlockedByRobotsChgInd(true);
//                    isDifferent = true;
//                }
//
//                // 比较canonical相关字段
//                if (!StringUtils.equals(currentEntity.getCrawlerResponse().getCanonical_flg(),
//                               previousEntity.getCrawlerResponse().getCanonical_flg())) {
//                    currentEntity.setCanonicalFlgChgInd(true);
//                    isDifferent = true;
//                } else {
//                    String currentCanonicalHash = getHashCodeFromArray(currentEntity.getChangeTrackingHashCdJsonArray(), IConstants.CANONICAL);
//                    String previousCanonicalHash = getHashCodeFromArray(previousEntity.getChangeTrackingHashCdJsonArray(), IConstants.CANONICAL);
//
//                    if (!StringUtils.equalsIgnoreCase(currentCanonicalHash, previousCanonicalHash)) {
//                        currentEntity.setCanonicalChgInd(true);
//                        isDifferent = true;
//                    }
//                }
//
//                if (!StringUtils.equals(currentEntity.getCrawlerResponse().getCanonical_header_flag(),
//                               previousEntity.getCrawlerResponse().getCanonical_header_flag())) {
//                    currentEntity.setCanonicalHeaderFlagChgInd(true);
//                    isDifferent = true;
//                }
//
//                if (!StringUtils.equals(currentEntity.getCrawlerResponse().getCanonical_header_type(),
//                               previousEntity.getCrawlerResponse().getCanonical_header_type())) {
//                    currentEntity.setCanonicalHeaderTypeChgInd(true);
//                    isDifferent = true;
//                }
//
//                if (!StringUtils.equals(currentEntity.getCrawlerResponse().getCanonical_type(),
//                               previousEntity.getCrawlerResponse().getCanonical_type())) {
//                    currentEntity.setCanonicalTypeChgInd(true);
//                    isDifferent = true;
//                }
//
//                // 比较title相关字段
//                if (!StringUtils.equals(currentEntity.getCrawlerResponse().getTitle_flg(),
//                               previousEntity.getCrawlerResponse().getTitle_flg())) {
//                    currentEntity.setTitleFlgChgInd(true);
//                    isDifferent = true;
//                } else {
//                    String currentTitleHash = getHashCodeFromArray(currentEntity.getChangeTrackingHashCdJsonArray(), IConstants.TITLE);
//                    String previousTitleHash = getHashCodeFromArray(previousEntity.getChangeTrackingHashCdJsonArray(), IConstants.TITLE);
//
//                    if (!StringUtils.equalsIgnoreCase(currentTitleHash, previousTitleHash)) {
//                        currentEntity.setTitleChgInd(true);
//                        isDifferent = true;
//                    }
//                }
//
//                if (!StringUtils.equals(currentEntity.getCrawlerResponse().getTitle_length(),
//                               previousEntity.getCrawlerResponse().getTitle_length())) {
//                    currentEntity.setTitleLengthChgInd(true);
//                    isDifferent = true;
//                }
//
//                if (!StringUtils.equals(currentEntity.getCrawlerResponse().getTitle_md5(),
//                               previousEntity.getCrawlerResponse().getTitle_md5())) {
//                    currentEntity.setTitleMd5ChgInd(true);
//                    isDifferent = true;
//                }
//
//                // 比较description相关字段
//                if (!StringUtils.equals(currentEntity.getCrawlerResponse().getDescription_flg(),
//                               previousEntity.getCrawlerResponse().getDescription_flg())) {
//                    currentEntity.setDescriptionFlgChgInd(true);
//                    isDifferent = true;
//                } else {
//                    String currentDescriptionHash = getHashCodeFromArray(currentEntity.getChangeTrackingHashCdJsonArray(), IConstants.DESCRIPTION);
//                    String previousDescriptionHash = getHashCodeFromArray(previousEntity.getChangeTrackingHashCdJsonArray(), IConstants.DESCRIPTION);
//
//                    if (!StringUtils.equalsIgnoreCase(currentDescriptionHash, previousDescriptionHash)) {
//                        currentEntity.setDescriptionChgInd(true);
//                        isDifferent = true;
//                    }
//                }
//
//                if (!StringUtils.equals(currentEntity.getCrawlerResponse().getDescription_length(),
//                               previousEntity.getCrawlerResponse().getDescription_length())) {
//                    currentEntity.setDescriptionLengthChgInd(true);
//                    isDifferent = true;
//                }
//
//                // 比较H1相关字段
//                if (!StringUtils.equals(currentEntity.getCrawlerResponse().getH1_flg(),
//                               previousEntity.getCrawlerResponse().getH1_flg())) {
//                    currentEntity.setH1FlgChgInd(true);
//                    isDifferent = true;
//                }
//
//                if (!StringUtils.equals(currentEntity.getCrawlerResponse().getH1_count(),
//                               previousEntity.getCrawlerResponse().getH1_count())) {
//                    currentEntity.setH1CountChgInd(true);
//                    isDifferent = true;
//                }
//
//                // 比较index相关字段
//                if (!StringUtils.equals(currentEntity.getCrawlerResponse().getIndex_flg(),
//                               previousEntity.getCrawlerResponse().getIndex_flg())) {
//                    currentEntity.setIndexFlgChgInd(true);
//                    isDifferent = true;
//                }
//
//                if (!StringUtils.equals(currentEntity.getCrawlerResponse().getIndex_flg_x_tag(),
//                               previousEntity.getCrawlerResponse().getIndex_flg_x_tag())) {
//                    currentEntity.setIndexFlgXTagChgInd(true);
//                    isDifferent = true;
//                }
//
//                // 比较robots相关字段
//                if (!StringUtils.equals(currentEntity.getCrawlerResponse().getRobots_flg(),
//                               previousEntity.getCrawlerResponse().getRobots_flg())) {
//                    currentEntity.setRobotsFlgChgInd(true);
//                    isDifferent = true;
//                } else if (!StringUtils.equals(currentEntity.getCrawlerResponse().getRobots_contents(),
//                                  previousEntity.getCrawlerResponse().getRobots_contents())) {
//                    currentEntity.setRobotsContentsChgInd(true);
//                    isDifferent = true;
//                }
//
//                // 比较viewport相关字段
//                if (!StringUtils.equals(currentEntity.getCrawlerResponse().getViewport_flag(),
//                               previousEntity.getCrawlerResponse().getViewport_flag())) {
//                    currentEntity.setViewportFlagChgInd(true);
//                    isDifferent = true;
//                }
//
//                // 比较base_tag相关字段
//                if (!StringUtils.equals(currentEntity.getCrawlerResponse().getBase_tag_flag(),
//                               previousEntity.getCrawlerResponse().getBase_tag_flag())) {
//                    currentEntity.setBaseTagFlagChgInd(true);
//                    isDifferent = true;
//                } else if (!StringUtils.equals(currentEntity.getCrawlerResponse().getBase_tag(),
//                                   previousEntity.getCrawlerResponse().getBase_tag())) {
//                    currentEntity.setBaseTagChgInd(true);
//                    isDifferent = true;
//                }
//            }
//        }
//
//        return isDifferent;
//    }
//
//    /**
//     * 从ChangeTrackingHashCdJson数组中获取指定名称的哈希值
//     * @param hashArray 哈希数组
//     * @param fieldName 字段名称
//     * @return 哈希值，如果未找到则返回null
//     */
//    private String getHashCodeFromArray(ChangeTrackingHashCdJson[] hashArray, String fieldName) {
//        if (hashArray == null || hashArray.length == 0 || StringUtils.isBlank(fieldName)) {
//            return null;
//        }
//
//        for (ChangeTrackingHashCdJson hashCd : hashArray) {
//            if (StringUtils.equalsIgnoreCase(hashCd.getName(), fieldName)) {
//                return hashCd.getValue();
//            }
//        }
//
//        return null;
//    }
//
//
//    // for target URLs crawl, 'urlMetricsEntityV3' contains previous crawl result of the change tracking fields or their MD5 hash codes in the 'target_url_html_daily' table
//	public boolean trackChanges(String ip, HtmlClickHouseEntity htmlClickHouseEntity, UrlMetricsEntityV3 urlMetricsEntityV3,
//	                            ChangeTrackingHashCdJson[] currentChangeTrackingHashCdJsonArray, String previousResponseCodeInCickHouse, Date previousTrackDateInCickHouse, int crawlType,
//	                            Boolean urlSkipDomainNameFlg, Boolean textCaseInsensitiveFlg) {
//		boolean isDifferent = false;
//		ChangeTrackingHashCdJson[] previousChangeTrackingHashCdJsonArray = urlMetricsEntityV3.getChangeTrackingHashCdJsonArray();
//		List<String> changeTrackingFields = getChangeTrackingFieldNames();
//		String previousChangeTrackingHashCode = null;
//		String currentChangeTrackingHashCode = null;
//		String trackDateStringInClickHouse = null;
//		String trackDateStringInQueueMessage = null;
//		PageAnalysisResultChgInd[] pageAnalysisResultChgIndArray = null;
//		String pageAnalysisResultsChgIndJson = null;
//		int previousInteger = 0;
//		int currentInteger = 0;
//		int changeInteger = 0;
//		String[] previousStringArray = null;
//		String[] currentStringArray = null;
//		boolean isCustomDataAvailable = false;
//		OgMarkup[] ogMarkup = null;
//		String json = null;
//		PageLink[] pageLink = null;
//		String[] currentResponseHeaderNames = null;
//
//		// when competitor URLs
//		final String previousResponseCode = urlMetricsEntityV3.getResponse_code();
//		if (crawlType == IConstants.CRAWL_TYPE_COMPETITOR_URL_HTML) {
//			// when no last historical data in urlMetricsEntityV3 (ie.isTargetUrlHtmlDailyDataInd()=false)
//			if (urlMetricsEntityV3.isTargetUrlHtmlDailyDataInd() == false) {
//				// when current HTTP status code is different from previous HTTP status code, then crawl results different
//				if (isDifferent(previousResponseCode, previousResponseCodeInCickHouse) == true) {
//					isDifferent = true;
//				} else {
//					isDifferent = false;
//				}
//				return isDifferent;
//			}
//		} else {
//			if (urlMetricsEntityV3.isTargetUrlHtmlDailyDataInd() == false) {
//				isDifferent = false;
//				FormatUtils.getInstance().logMemoryUsage("trackChanges() error--ip=" + ip + ",domainId=" + htmlClickHouseEntity.getDomainId() + ",url="
//						+ htmlClickHouseEntity.getUrl() + ",urlMetricsEntityV3.isTargetUrlHtmlDailyDataInd()=" + urlMetricsEntityV3.isTargetUrlHtmlDailyDataInd());
//				return isDifferent;
//			}
//		}
//
//		// when previous HTTP status code not available
//		if (StringUtils.isBlank(previousResponseCode)
//				|| StringUtils.equalsIgnoreCase(previousResponseCode, IConstants.RESPONSE_CODE_0)
//				|| StringUtils.isBlank(previousResponseCodeInCickHouse)) {
//			isDifferent = false;
//			FormatUtils.getInstance()
//					.logMemoryUsage("trackChanges() error--ip=" + ip + ",domainId=" + htmlClickHouseEntity.getDomainId() + ",url=" + htmlClickHouseEntity.getUrl()
//							+ ",urlMetricsEntityV3.getResponse_code()=" + previousResponseCode + ",previousResponseCodeInCickHouse="
//							+ previousResponseCodeInCickHouse);
//			return isDifferent;
//		}
//		// when previous HTTP status code is available
//		else {
//			// skip when response code in clickhouse is different from the response code in the queue message
//			if (isDifferent(previousResponseCode, previousResponseCodeInCickHouse)) {
//				isDifferent = false;
//				FormatUtils.getInstance()
//						.logMemoryUsage("trackChanges() error--ip=" + ip + ",domainId=" + htmlClickHouseEntity.getDomainId() + ",url=" + htmlClickHouseEntity.getUrl()
//								+ ",urlMetricsEntityV3.getResponse_code()=" + previousResponseCode + ",previousResponseCodeInCickHouse="
//								+ previousResponseCodeInCickHouse);
//				return isDifferent;
//			}
//
//			// skip when track date in clickhouse is different from the track date in the queue message
//			trackDateStringInClickHouse = DateFormatUtils.format(previousTrackDateInCickHouse, IConstants.DATE_FORMAT_YYYY_MM_DD);
//			trackDateStringInQueueMessage = urlMetricsEntityV3.getTargetUrlHtmlTrackDate();
//			if (isDifferent(trackDateStringInQueueMessage, trackDateStringInClickHouse)) {
//				isDifferent = false;
//				FormatUtils.getInstance()
//						.logMemoryUsage("trackChanges() error--ip=" + ip + ",domainId=" + htmlClickHouseEntity.getDomainId() + ",url=" + htmlClickHouseEntity.getUrl()
//								+ ",trackDateStringInQueueMessage=" + trackDateStringInQueueMessage + ",trackDateStringInClickHouse=" + trackDateStringInClickHouse);
//				return isDifferent;
//			}
//
//			// when previous HTTP status code is different from current HTTP status code
//			final String currentResponseCode = htmlClickHouseEntity.getCrawlerResponse().getResponse_code();
//			if (isDifferent(currentResponseCode, previousResponseCode)) {
//				htmlClickHouseEntity.setResponseCodeChgInd(true);
//				isDifferent = true;
//				FormatUtils.getInstance()
//						.logMemoryUsage("trackChanges() ip=" + ip + ",domainId=" + htmlClickHouseEntity.getDomainId() + ",url=" + htmlClickHouseEntity.getUrl()
//								+ ",field=" + IConstants.RESPONSE_CODE + ",previous=" + previousResponseCode + ",current="
//								+ currentResponseCode);
//
//				previousInteger = previousResponseCode != null ? NumberUtils.toInt(previousResponseCode) : 0;
//				currentInteger = currentResponseCode != null ? NumberUtils.toInt(currentResponseCode) : 0;
//
//				// 404_detected_ind (ie. current is 404 and previous is not 4xx)
//				if (currentInteger == 404 && previousInteger != 404) {
//					htmlClickHouseEntity.setResponseCode404DetectedInd(true);
//				}
//
//				// 404_removed_ind (ie. current is not 4xx and previous is 404)
//				if (currentInteger != 404 && previousInteger == 404) {
//					htmlClickHouseEntity.setResponseCode404RemovedInd(true);
//				}
//
//				// redirect_301_detected_ind (ie. current is 301 and previous is not 3xx)
//				if (currentInteger == 301 && (previousInteger < 300 || previousInteger > 399)) {
//					htmlClickHouseEntity.setRedirect301DetectedInd(true);
//				}
//
//				// redirect_301_removed_ind (ie. current is not 3xx and previous is 301)
//				if ((currentInteger < 300 || currentInteger > 399) && currentInteger == 301) {
//					htmlClickHouseEntity.setRedirect301RemovedInd(true);
//				}
//
//				// redirect_302_detected_ind (ie. current is 302 and previous is not 3xx)
//				if (currentInteger == 302 && (previousInteger < 300 || previousInteger > 399)) {
//					htmlClickHouseEntity.setRedirect302DetectedInd(true);
//				}
//
//				// redirect_302_removed_ind (ie. current is not 3xx and previous is 302)
//				if ((currentInteger < 300 || currentInteger > 399) && currentInteger == 302) {
//					htmlClickHouseEntity.setRedirect302RemovedInd(true);
//				}
//
//				// redirect_diff_code_ind (current is 3xx and previous is 3xx and current != previous)
//				if (previousInteger >= 300 && previousInteger <= 399 && currentInteger >= 300 && currentInteger <= 399 && previousInteger != currentInteger) {
//					htmlClickHouseEntity.setRedirectDiffCodeInd(true);
//				}
//
//				return isDifferent;
//			}
//			// when previous HTTP status code is same as current HTTP status code
//			else {
//				// when current HTTP status code is 3xx, track changes
//				if (htmlClickHouseEntity.getHttpStatusCode().intValue() >= 300 && htmlClickHouseEntity.getHttpStatusCode().intValue() <= 399) {
//					for (String changeTrackingField : changeTrackingFields) {
//						previousChangeTrackingHashCode = null;
//						currentChangeTrackingHashCode = null;
//						switch (changeTrackingField) {
//							// redirect_blocked
//							case IConstants.REDIRECT_BLOCKED: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getRedirect_blocked(), urlMetricsEntityV3.getRedirect_blocked()) == true) {
//									htmlClickHouseEntity.setRedirectBlockedChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// redirect_blocked_reason
//							case IConstants.REDIRECT_BLOCKED_REASON: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getRedirect_blocked_reason(),
//										urlMetricsEntityV3.getRedirect_blocked_reason()) == true) {
//									htmlClickHouseEntity.setRedirectBlockedReasonChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// redirect_chain
//							case IConstants.REDIRECT_CHAIN: {
//								if (currentChangeTrackingHashCdJsonArray != null && currentChangeTrackingHashCdJsonArray.length > 0) {
//									nextCurrentChangeTrackingHashCdJson: for (ChangeTrackingHashCdJson changeTrackingHashCdJson : currentChangeTrackingHashCdJsonArray) {
//										if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.REDIRECT_CHAIN) == true) {
//											currentChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
//											break nextCurrentChangeTrackingHashCdJson;
//										}
//									}
//								}
//								if (previousChangeTrackingHashCdJsonArray != null && previousChangeTrackingHashCdJsonArray.length > 0) {
//									nextPreviousChangeTrackingHashCdJson: for (ChangeTrackingHashCdJson changeTrackingHashCdJson : previousChangeTrackingHashCdJsonArray) {
//										if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.REDIRECT_CHAIN) == true) {
//											previousChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
//											break nextPreviousChangeTrackingHashCdJson;
//										}
//									}
//								}
//
//								if (StringUtils.equalsIgnoreCase(previousChangeTrackingHashCode, currentChangeTrackingHashCode) == false) {
//									htmlClickHouseEntity.setRedirectChainChgInd(true);
//									isDifferent = true;
//
//									// redirect_301_chg_ind
//									if (htmlClickHouseEntity.getHttpStatusCode().intValue() == 301) {
//										htmlClickHouseEntity.setRedirect301ChgInd(true);
//									}
//
//									// redirect_302_chg_ind
//									if (htmlClickHouseEntity.getHttpStatusCode().intValue() == 302) {
//										htmlClickHouseEntity.setRedirect302ChgInd(true);
//									}
//
//								}
//								break;
//							}
//							// redirect_final_url
//							case IConstants.REDIRECT_FINAL_URL: {
//								if (currentChangeTrackingHashCdJsonArray != null && currentChangeTrackingHashCdJsonArray.length > 0) {
//									nextCurrentChangeTrackingHashCdJson: for (ChangeTrackingHashCdJson changeTrackingHashCdJson : currentChangeTrackingHashCdJsonArray) {
//										if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.REDIRECT_FINAL_URL) == true) {
//											currentChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
//											break nextCurrentChangeTrackingHashCdJson;
//										}
//									}
//								}
//								if (previousChangeTrackingHashCdJsonArray != null && previousChangeTrackingHashCdJsonArray.length > 0) {
//									nextPreviousChangeTrackingHashCdJson: for (ChangeTrackingHashCdJson changeTrackingHashCdJson : previousChangeTrackingHashCdJsonArray) {
//										if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.REDIRECT_FINAL_URL) == true) {
//											previousChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
//											break nextPreviousChangeTrackingHashCdJson;
//										}
//									}
//								}
//
//								if (StringUtils.equalsIgnoreCase(previousChangeTrackingHashCode, currentChangeTrackingHashCode) == false) {
//									htmlClickHouseEntity.setRedirectFinalUrlChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// redirect_flg
//							case IConstants.REDIRECT_FLG: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getRedirect_flg(), urlMetricsEntityV3.getRedirect_flg()) == true) {
//									htmlClickHouseEntity.setRedirectFlgChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// redirect_times
//							case IConstants.REDIRECT_TIMES: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getRedirect_times(), urlMetricsEntityV3.getRedirect_times()) == true) {
//									htmlClickHouseEntity.setRedirectTimesChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							default:
//								break;
//						}
//					}
//				}
//				// when current HTTP status code is 200, track changes
//				else if (htmlClickHouseEntity.getHttpStatusCode().intValue() == 200) {
//					for (String changeTrackingField : changeTrackingFields) {
//						previousChangeTrackingHashCode = null;
//						currentChangeTrackingHashCode = null;
//						switch (changeTrackingField) {
//							// alternate_links
//							case IConstants.ALTERNATE_LINKS: {
//								if (currentChangeTrackingHashCdJsonArray != null && currentChangeTrackingHashCdJsonArray.length > 0) {
//									nextCurrentChangeTrackingHashCdJson: for (ChangeTrackingHashCdJson changeTrackingHashCdJson : currentChangeTrackingHashCdJsonArray) {
//										if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.ALTERNATE_LINKS) == true) {
//											currentChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
//											break nextCurrentChangeTrackingHashCdJson;
//										}
//									}
//								}
//								if (previousChangeTrackingHashCdJsonArray != null && previousChangeTrackingHashCdJsonArray.length > 0) {
//									nextPreviousChangeTrackingHashCdJson: for (ChangeTrackingHashCdJson changeTrackingHashCdJson : previousChangeTrackingHashCdJsonArray) {
//										if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.ALTERNATE_LINKS) == true) {
//											previousChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
//											break nextPreviousChangeTrackingHashCdJson;
//										}
//									}
//								}
//
//								if (StringUtils.equalsIgnoreCase(previousChangeTrackingHashCode, currentChangeTrackingHashCode) == false) {
//									htmlClickHouseEntity.setAlternateLinksChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// amphtml_flag
//							case IConstants.AMPHTML_FLAG: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getAmphtml_flag(), urlMetricsEntityV3.getAmphtml_flag()) == true) {
//									htmlClickHouseEntity.setAmphtmlFlagChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// amphtml_href
//							case IConstants.AMPHTML_HREF: {
//								if (currentChangeTrackingHashCdJsonArray != null && currentChangeTrackingHashCdJsonArray.length > 0) {
//									nextCurrentChangeTrackingHashCdJson: for (ChangeTrackingHashCdJson changeTrackingHashCdJson : currentChangeTrackingHashCdJsonArray) {
//										if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.AMPHTML_HREF) == true) {
//											currentChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
//											break nextCurrentChangeTrackingHashCdJson;
//										}
//									}
//								}
//								if (previousChangeTrackingHashCdJsonArray != null && previousChangeTrackingHashCdJsonArray.length > 0) {
//									nextPreviousChangeTrackingHashCdJson: for (ChangeTrackingHashCdJson changeTrackingHashCdJson : previousChangeTrackingHashCdJsonArray) {
//										if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.AMPHTML_HREF) == true) {
//											previousChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
//											break nextPreviousChangeTrackingHashCdJson;
//										}
//									}
//								}
//
//								if (StringUtils.equalsIgnoreCase(previousChangeTrackingHashCode, currentChangeTrackingHashCode) == false) {
//									htmlClickHouseEntity.setAmphtmlHrefChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// analyzed_url_flg_s
//							case IConstants.ANALYZED_URL_FLG_S: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getAnalyzed_url_flg_s(), urlMetricsEntityV3.getAnalyzed_url_flg_s()) == true) {
//									htmlClickHouseEntity.setAnalyzedUrlFlgSChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// analyzed_url_s
//							case IConstants.ANALYZED_URL_S: {
//								if (BooleanUtils.isTrue(urlSkipDomainNameFlg)) {
//									if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getAnalyzed_url_s(), urlMetricsEntityV3.getAnalyzed_url_s()) == true) {
//										htmlClickHouseEntity.setAnalyzedUrlSChgInd(true);
//										isDifferent = true;
//									}
//								} else {
//									if (currentChangeTrackingHashCdJsonArray != null && currentChangeTrackingHashCdJsonArray.length > 0) {
//										nextCurrentChangeTrackingHashCdJson: for (ChangeTrackingHashCdJson changeTrackingHashCdJson : currentChangeTrackingHashCdJsonArray) {
//											if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.ANALYZED_URL_S) == true) {
//												currentChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
//												break nextCurrentChangeTrackingHashCdJson;
//											}
//										}
//									}
//									if (previousChangeTrackingHashCdJsonArray != null && previousChangeTrackingHashCdJsonArray.length > 0) {
//										nextPreviousChangeTrackingHashCdJson: for (ChangeTrackingHashCdJson changeTrackingHashCdJson : previousChangeTrackingHashCdJsonArray) {
//											if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.ANALYZED_URL_S) == true) {
//												previousChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
//												break nextPreviousChangeTrackingHashCdJson;
//											}
//										}
//									}
//
//									if (StringUtils.equalsIgnoreCase(previousChangeTrackingHashCode, currentChangeTrackingHashCode) == false) {
//										htmlClickHouseEntity.setAnalyzedUrlSChgInd(true);
//										isDifferent = true;
//									}
//								}
//								break;
//							}
//							// archive_flg
//							case IConstants.ARCHIVE_FLG: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getArchive_flg(), urlMetricsEntityV3.getArchive_flg()) == true) {
//									htmlClickHouseEntity.setArchiveFlgChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// archive_flg_x_tag
//							case IConstants.ARCHIVE_FLG_X_TAG: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getArchive_flg_x_tag(), urlMetricsEntityV3.getArchive_flg_x_tag()) == true) {
//									htmlClickHouseEntity.setArchiveFlgXTagChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// blocked_by_robots
//							case IConstants.BLOCKED_BY_ROBOTS: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getBlocked_by_robots(), urlMetricsEntityV3.getBlocked_by_robots()) == true) {
//									htmlClickHouseEntity.setBlockedByRobotsChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// canonical
//							case IConstants.CANONICAL: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getCanonical_flg(), urlMetricsEntityV3.getCanonical_flg()) == false) {
//									if (BooleanUtils.isTrue(urlSkipDomainNameFlg)) {
//										if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getCanonical(), urlMetricsEntityV3.getCanonical()) == true) {
//											htmlClickHouseEntity.setCanonicalChgInd(true);
//											isDifferent = true;
//										}
//									} else {
//										if (currentChangeTrackingHashCdJsonArray != null && currentChangeTrackingHashCdJsonArray.length > 0) {
//											nextCurrentChangeTrackingHashCdJson: for (ChangeTrackingHashCdJson changeTrackingHashCdJson : currentChangeTrackingHashCdJsonArray) {
//												if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.CANONICAL) == true) {
//													currentChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
//													break nextCurrentChangeTrackingHashCdJson;
//												}
//											}
//										}
//										if (previousChangeTrackingHashCdJsonArray != null && previousChangeTrackingHashCdJsonArray.length > 0) {
//											nextPreviousChangeTrackingHashCdJson: for (ChangeTrackingHashCdJson changeTrackingHashCdJson : previousChangeTrackingHashCdJsonArray) {
//												if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.CANONICAL) == true) {
//													previousChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
//													break nextPreviousChangeTrackingHashCdJson;
//												}
//											}
//										}
//
//										if (StringUtils.equalsIgnoreCase(previousChangeTrackingHashCode, currentChangeTrackingHashCode) == false) {
//											htmlClickHouseEntity.setCanonicalChgInd(true);
//											isDifferent = true;
//										}
//									}
//								}
//								break;
//							}
//							// canonical_flg
//							case IConstants.CANONICAL_FLG: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getCanonical_flg(), urlMetricsEntityV3.getCanonical_flg()) == true) {
//									htmlClickHouseEntity.setCanonicalFlgChgInd(true);
//									isDifferent = true;
//
//									// canonical_added_ind (ie. current is Yes, previous is not Yes)
//									if (StringUtils.equalsIgnoreCase(htmlClickHouseEntity.getCrawlerResponse().getCanonical_flg(), IConstants.YES) == true
//											&& StringUtils.equalsIgnoreCase(urlMetricsEntityV3.getCanonical_flg(), IConstants.YES) == false) {
//										htmlClickHouseEntity.setCanonicalAddedInd(true);
//									}
//									// canonical_removed_ind (ie. current is No, previous is not No)
//									else if (StringUtils.equalsIgnoreCase(htmlClickHouseEntity.getCrawlerResponse().getCanonical_flg(), IConstants.NO) == true
//											&& StringUtils.equalsIgnoreCase(urlMetricsEntityV3.getCanonical_flg(), IConstants.NO) == false) {
//										htmlClickHouseEntity.setCanonicalRemovedInd(true);
//									}
//
//								}
//								break;
//							}
//							// canonical_header_flag
//							case IConstants.CANONICAL_HEADER_FLAG: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getCanonical_header_flag(),
//										urlMetricsEntityV3.getCanonical_header_flag()) == true) {
//									htmlClickHouseEntity.setCanonicalHeaderFlagChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// canonical_header_type
//							case IConstants.CANONICAL_HEADER_TYPE: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getCanonical_header_type(),
//										urlMetricsEntityV3.getCanonical_header_type()) == true) {
//									htmlClickHouseEntity.setCanonicalHeaderTypeChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// canonical_type
//							case IConstants.CANONICAL_TYPE: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getCanonical_type(), urlMetricsEntityV3.getCanonical_type()) == true) {
//									htmlClickHouseEntity.setCanonicalTypeChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// canonical_url_is_consistent
//							case IConstants.CANONICAL_URL_IS_CONSISTENT: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getCanonical_url_is_consistent(),
//										urlMetricsEntityV3.getCanonical_url_is_consistent()) == true) {
//									htmlClickHouseEntity.setCanonicalUrlIsConsistentChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// content_type
//							case IConstants.CONTENT_TYPE: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getContent_type(), urlMetricsEntityV3.getContent_type()) == true) {
//									htmlClickHouseEntity.setContentTypeChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// custom_data
//							case IConstants.CUSTOM_DATA: {
//								if (previousChangeTrackingHashCdJsonArray != null && previousChangeTrackingHashCdJsonArray.length > 0) {
//									nextPreviousChangeTrackingHashCdJson: for (ChangeTrackingHashCdJson changeTrackingHashCdJson : previousChangeTrackingHashCdJsonArray) {
//										if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.CUSTOM_DATA) == true) {
//											previousChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
//											break nextPreviousChangeTrackingHashCdJson;
//										}
//									}
//								}
//								isCustomDataAvailable = checkIfCustomDataAvailable(htmlClickHouseEntity.getCrawlerResponse().getCustom_data());
//								if (isCustomDataAvailable == false && StringUtils.isBlank(previousChangeTrackingHashCode)) {
//									break;
//								}
//								if (currentChangeTrackingHashCdJsonArray != null && currentChangeTrackingHashCdJsonArray.length > 0) {
//									nextCurrentChangeTrackingHashCdJson: for (ChangeTrackingHashCdJson changeTrackingHashCdJson : currentChangeTrackingHashCdJsonArray) {
//										if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.CUSTOM_DATA) == true) {
//											currentChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
//											break nextCurrentChangeTrackingHashCdJson;
//										}
//									}
//								}
//								if (StringUtils.equalsIgnoreCase(previousChangeTrackingHashCode, currentChangeTrackingHashCode) == false) {
//									isDifferent = true;
//									if (StringUtils.isNotBlank(previousChangeTrackingHashCode) && StringUtils.isNotBlank(currentChangeTrackingHashCode)) {
//										htmlClickHouseEntity.setCustomDataChgInd(true);
//									} else {
//										// custom_data_added_ind
//										if (StringUtils.isBlank(previousChangeTrackingHashCode) && StringUtils.isNotBlank(currentChangeTrackingHashCode)) {
//											htmlClickHouseEntity.setCustomDataAddedInd(true);
//										}
//										// custom_data_removed_ind
//										else if (StringUtils.isNotBlank(previousChangeTrackingHashCode) && StringUtils.isBlank(currentChangeTrackingHashCode)) {
//											htmlClickHouseEntity.setCustomDataRemovedInd(true);
//										}
//									}
//								}
//								break;
//							}
//							// description
//							case IConstants.DESCRIPTION: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getDescription_flg(), urlMetricsEntityV3.getDescription_flg()) == false) {
//									if (BooleanUtils.isTrue(textCaseInsensitiveFlg)) {
//										if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getDescription(), urlMetricsEntityV3.getDescription()) == true) {
//											htmlClickHouseEntity.setDescriptionChgInd(true);
//											isDifferent = true;
//										}
//									} else {
//										if (currentChangeTrackingHashCdJsonArray != null && currentChangeTrackingHashCdJsonArray.length > 0) {
//											nextCurrentChangeTrackingHashCdJson: for (ChangeTrackingHashCdJson changeTrackingHashCdJson : currentChangeTrackingHashCdJsonArray) {
//												if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.DESCRIPTION) == true) {
//													currentChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
//													break nextCurrentChangeTrackingHashCdJson;
//												}
//											}
//										}
//										if (previousChangeTrackingHashCdJsonArray != null && previousChangeTrackingHashCdJsonArray.length > 0) {
//											nextPreviousChangeTrackingHashCdJson: for (ChangeTrackingHashCdJson changeTrackingHashCdJson : previousChangeTrackingHashCdJsonArray) {
//												if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.DESCRIPTION) == true) {
//													previousChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
//													break nextPreviousChangeTrackingHashCdJson;
//												}
//											}
//										}
//
//										if (StringUtils.equalsIgnoreCase(previousChangeTrackingHashCode, currentChangeTrackingHashCode) == false) {
//											htmlClickHouseEntity.setDescriptionChgInd(true);
//											isDifferent = true;
//										}
//									}
//								}
//								break;
//							}
//							// description_flg
//							case IConstants.DESCRIPTION_FLG: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getDescription_flg(), urlMetricsEntityV3.getDescription_flg()) == true) {
//									htmlClickHouseEntity.setDescriptionFlgChgInd(true);
//									isDifferent = true;
//
//									// description_added_ind (ie. current is Yes, previous is not Yes)
//									if (StringUtils.equalsIgnoreCase(htmlClickHouseEntity.getCrawlerResponse().getDescription_flg(), IConstants.YES) == true
//											&& StringUtils.equalsIgnoreCase(urlMetricsEntityV3.getDescription_flg(), IConstants.YES) == false) {
//										htmlClickHouseEntity.setDescriptionAddedInd(true);
//									}
//									// description_removed_ind (ie. current is No, previous is not No)
//									else if (StringUtils.equalsIgnoreCase(htmlClickHouseEntity.getCrawlerResponse().getDescription_flg(), IConstants.NO) == true
//											&& StringUtils.equalsIgnoreCase(urlMetricsEntityV3.getDescription_flg(), IConstants.NO) == false) {
//										htmlClickHouseEntity.setDescriptionRemovedInd(true);
//									}
//								}
//								break;
//							}
//							// description_length
//							case IConstants.DESCRIPTION_LENGTH: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getDescription_length(), urlMetricsEntityV3.getDescription_length()) == true) {
//									htmlClickHouseEntity.setDescriptionLengthChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// description_simhash
//							case IConstants.DESCRIPTION_SIMHASH: {
//								if (BooleanUtils.isTrue(textCaseInsensitiveFlg)) {
//									// when text comparision is case insensitive, skip simhash comparison
//								} else {
//									if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getDescription_simhash(),
//											urlMetricsEntityV3.getDescription_simhash()) == true) {
//										htmlClickHouseEntity.setDescriptionSimhashChgInd(true);
//										isDifferent = true;
//									}
//								}
//								break;
//							}
//							// error_message
//							case IConstants.ERROR_MESSAGE: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getError_message(), urlMetricsEntityV3.getError_message()) == true) {
//									htmlClickHouseEntity.setErrorMessageChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// final_response_code
//							case IConstants.FINAL_RESPONSE_CODE: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getFinal_response_code(), urlMetricsEntityV3.getFinal_response_code()) == true) {
//									htmlClickHouseEntity.setFinalResponseCodeChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// follow_flg
//							case IConstants.FOLLOW_FLG: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getFollow_flg(), urlMetricsEntityV3.getFollow_flg()) == true) {
//									htmlClickHouseEntity.setFollowFlgChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// follow_flg_x_tag
//							case IConstants.FOLLOW_FLG_X_TAG: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getFollow_flg_x_tag(), urlMetricsEntityV3.getFollow_flg_x_tag()) == true) {
//									htmlClickHouseEntity.setFollowFlgXTagChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// h1
//							case IConstants.H1: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getH1_count(), urlMetricsEntityV3.getH1_count()) == false) {
//									if (BooleanUtils.isTrue(textCaseInsensitiveFlg)) {
//
//										previousChangeTrackingHashCode = null;
//										currentChangeTrackingHashCode = null;
//
//										// previous
//										previousStringArray = urlMetricsEntityV3.getH1_array();
//										if (previousStringArray != null) {
//											previousStringArray = new String[previousStringArray.length];
//											for (int i = 0; i < previousStringArray.length; i++) {
//												previousStringArray[i] = previousStringArray[i].toLowerCase();
//											}
//											json = gson.toJson(previousStringArray, String[].class);
//											if (checkIfJsonDataAvailable(json) == true) {
//												previousChangeTrackingHashCode = getSortedCharactersHashCode(json);
//											}
//										}
//
//										// current
//										currentStringArray = htmlClickHouseEntity.getCrawlerResponse().getH1();
//										if (currentStringArray != null) {
//											currentStringArray = new String[currentStringArray.length];
//											for (int i = 0; i < currentStringArray.length; i++) {
//												currentStringArray[i] = currentStringArray[i].toLowerCase();
//											}
//											json = gson.toJson(currentStringArray, String[].class);
//											if (checkIfJsonDataAvailable(json) == true) {
//												currentChangeTrackingHashCode = getSortedCharactersHashCode(json);
//											}
//										}
//									} else {
//										if (currentChangeTrackingHashCdJsonArray != null && currentChangeTrackingHashCdJsonArray.length > 0) {
//											nextCurrentChangeTrackingHashCdJson: for (ChangeTrackingHashCdJson changeTrackingHashCdJson : currentChangeTrackingHashCdJsonArray) {
//												if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.H1) == true) {
//													currentChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
//													break nextCurrentChangeTrackingHashCdJson;
//												}
//											}
//										}
//										if (previousChangeTrackingHashCdJsonArray != null && previousChangeTrackingHashCdJsonArray.length > 0) {
//											nextPreviousChangeTrackingHashCdJson: for (ChangeTrackingHashCdJson changeTrackingHashCdJson : previousChangeTrackingHashCdJsonArray) {
//												if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.H1) == true) {
//													previousChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
//													break nextPreviousChangeTrackingHashCdJson;
//												}
//											}
//										}
//									}
//
//									if (StringUtils.equalsIgnoreCase(previousChangeTrackingHashCode, currentChangeTrackingHashCode) == false) {
//										htmlClickHouseEntity.setH1ChgInd(true);
//										isDifferent = true;
//									}
//								}
//								break;
//							}
//							// h1_count
//							case IConstants.H1_COUNT: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getH1_count(), urlMetricsEntityV3.getH1_count()) == true) {
//									htmlClickHouseEntity.setH1CountChgInd(true);
//									isDifferent = true;
//
//									previousInteger = urlMetricsEntityV3.getH1_count() != null ? urlMetricsEntityV3.getH1_count().intValue() : 0;
//									currentInteger = htmlClickHouseEntity.getCrawlerResponse().getH1_count() != null
//											? htmlClickHouseEntity.getCrawlerResponse().getH1_count().intValue()
//											: 0;
//									changeInteger = currentInteger - previousInteger;
//
//									// h1_added_ind
//									if (changeInteger > 0) {
//										htmlClickHouseEntity.setH1AddedInd(true);
//									}
//									// h1_removed_ind
//									else if (changeInteger < 0) {
//										htmlClickHouseEntity.setH1RemovedInd(true);
//									}
//								}
//								break;
//							}
//							// h1_flg
//							case IConstants.H1_FLG: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getH1_flg(), urlMetricsEntityV3.getH1_flg()) == true) {
//									htmlClickHouseEntity.setH1FlgChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// h1_length
//							case IConstants.H1_LENGTH: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getH1_length(), urlMetricsEntityV3.getH1_length()) == true) {
//									htmlClickHouseEntity.setH1LengthChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// h1_md5
//							case IConstants.H1_MD5: {
//								if (BooleanUtils.isTrue(textCaseInsensitiveFlg)) {
//									// when text comparision is case insensitive, skip md5 comparison
//								} else {
//									if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getH1_md5(), urlMetricsEntityV3.getH1_md5()) == true) {
//										htmlClickHouseEntity.setH1Md5ChgInd(true);
//										isDifferent = true;
//									}
//								}
//								break;
//							}
//							// h2
//							case IConstants.H2: {
//								if (BooleanUtils.isTrue(textCaseInsensitiveFlg)) {
//
//									previousChangeTrackingHashCode = null;
//									currentChangeTrackingHashCode = null;
//									previousStringArray = null;
//									currentStringArray = null;
//
//									// previous
//									if (urlMetricsEntityV3.getH2_total() != null) {
//										if (urlMetricsEntityV3.getH2_total() > 0) {
//											previousStringArray = new String[urlMetricsEntityV3.getH2_total()];
//											previousChangeTrackingHashCode = urlMetricsEntityV3.getH2_hash();
//										}
//									} else {
//										previousStringArray = urlMetricsEntityV3.getH2_array();
//										if (previousStringArray != null) {
//											previousStringArray = new String[previousStringArray.length];
//											for (int i = 0; i < previousStringArray.length; i++) {
//												previousStringArray[i] = previousStringArray[i].toLowerCase();
//											}
//											json = gson.toJson(previousStringArray, String[].class);
//											if (checkIfJsonDataAvailable(json) == true) {
//												previousChangeTrackingHashCode = getSortedCharactersHashCode(json);
//											}
//										}
//									}
//
//									// current
//									currentStringArray = htmlClickHouseEntity.getCrawlerResponse().getH2();
//									if (currentStringArray != null) {
//										currentStringArray = new String[currentStringArray.length];
//										for (int i = 0; i < currentStringArray.length; i++) {
//											currentStringArray[i] = currentStringArray[i].toLowerCase();
//										}
//										json = gson.toJson(currentStringArray, String[].class);
//										if (checkIfJsonDataAvailable(json) == true) {
//											currentChangeTrackingHashCode = getSortedCharactersHashCode(json);
//										}
//									}
//								} else {
//									// When URL is not 'http://www.martinstabe.com/links/', compare H2 changes
//									if (StringUtils.equalsIgnoreCase(htmlClickHouseEntity.getUrl(), IConstants.URL_WITH_TOO_MANY_H2) == false) {
//										if (urlMetricsEntityV3.getH2_total() != null) {
//											if (urlMetricsEntityV3.getH2_total() > 0) {
//												previousStringArray = new String[urlMetricsEntityV3.getH2_total()];
//												previousChangeTrackingHashCode = urlMetricsEntityV3.getH2_hash();
//											}
//										} else {
//											previousStringArray = urlMetricsEntityV3.getH2_array();
//											previousChangeTrackingHashCode = getHashCodeOfStringArray(previousStringArray);
//										}
//										currentStringArray = htmlClickHouseEntity.getCrawlerResponse().getH2();
//										currentChangeTrackingHashCode = getHashCodeOfStringArray(currentStringArray);
//									}
//								}
//
//								if (StringUtils.equalsIgnoreCase(previousChangeTrackingHashCode, currentChangeTrackingHashCode) == false) {
//									isDifferent = true;
//									previousInteger = previousStringArray != null ? previousStringArray.length : 0;
//									currentInteger = currentStringArray != null ? currentStringArray.length : 0;
//									changeInteger = currentInteger - previousInteger;
//									if (changeInteger == 0) {
//										htmlClickHouseEntity.setH2ChgInd(true);
//									} else {
//										// h2_added_ind
//										if (changeInteger > 0) {
//											htmlClickHouseEntity.setH2AddedInd(true);
//										}
//										// h2_removed_ind
//										else if (changeInteger < 0) {
//											htmlClickHouseEntity.setH2RemovedInd(true);
//										}
//									}
//								}
//								break;
//							}
//							// header_noarchive
//							case IConstants.HEADER_NOARCHIVE: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getHeader_noarchive(), urlMetricsEntityV3.getHeader_noarchive()) == true) {
//									htmlClickHouseEntity.setHeaderNoarchiveChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// header_nofollow
//							case IConstants.HEADER_NOFOLLOW: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getHeader_nofollow(), urlMetricsEntityV3.getHeader_nofollow()) == true) {
//									htmlClickHouseEntity.setHeaderNofollowChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// header_noindex
//							case IConstants.HEADER_NOINDEX: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getHeader_noindex(), urlMetricsEntityV3.getHeader_noindex()) == true) {
//									htmlClickHouseEntity.setHeaderNoindexChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// header_noodp
//							case IConstants.HEADER_NOODP: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getHeader_noodp(), urlMetricsEntityV3.getHeader_noodp()) == true) {
//									htmlClickHouseEntity.setHeaderNoodpChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// header_nosnippet
//							case IConstants.HEADER_NOSNIPPET: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getHeader_nosnippet(), urlMetricsEntityV3.getHeader_nosnippet()) == true) {
//									htmlClickHouseEntity.setHeaderNosnippetChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// header_noydir
//							case IConstants.HEADER_NOYDIR: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getHeader_noydir(), urlMetricsEntityV3.getHeader_noydir()) == true) {
//									htmlClickHouseEntity.setHeaderNoydirChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// hreflang_errors
//							case IConstants.HREFLANG_ERRORS: {
//								if (currentChangeTrackingHashCdJsonArray != null && currentChangeTrackingHashCdJsonArray.length > 0) {
//									nextCurrentChangeTrackingHashCdJson: for (ChangeTrackingHashCdJson changeTrackingHashCdJson : currentChangeTrackingHashCdJsonArray) {
//										if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.HREFLANG_ERRORS) == true) {
//											currentChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
//											break nextCurrentChangeTrackingHashCdJson;
//										}
//									}
//								}
//								if (previousChangeTrackingHashCdJsonArray != null && previousChangeTrackingHashCdJsonArray.length > 0) {
//									nextPreviousChangeTrackingHashCdJson: for (ChangeTrackingHashCdJson changeTrackingHashCdJson : previousChangeTrackingHashCdJsonArray) {
//										if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.HREFLANG_ERRORS) == true) {
//											previousChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
//											break nextPreviousChangeTrackingHashCdJson;
//										}
//									}
//								}
//
//								if (StringUtils.equalsIgnoreCase(previousChangeTrackingHashCode, currentChangeTrackingHashCode) == false) {
//									htmlClickHouseEntity.setHreflangErrorsChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// hreflang_links
//							case IConstants.HREFLANG_LINKS: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getHreflang_url_count(), urlMetricsEntityV3.getHreflang_url_count()) == false) {
//									if (currentChangeTrackingHashCdJsonArray != null && currentChangeTrackingHashCdJsonArray.length > 0) {
//										nextCurrentChangeTrackingHashCdJson: for (ChangeTrackingHashCdJson changeTrackingHashCdJson : currentChangeTrackingHashCdJsonArray) {
//											if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.HREFLANG_LINKS) == true) {
//												currentChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
//												break nextCurrentChangeTrackingHashCdJson;
//											}
//										}
//									}
//									if (previousChangeTrackingHashCdJsonArray != null && previousChangeTrackingHashCdJsonArray.length > 0) {
//										nextPreviousChangeTrackingHashCdJson: for (ChangeTrackingHashCdJson changeTrackingHashCdJson : previousChangeTrackingHashCdJsonArray) {
//											if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.HREFLANG_LINKS) == true) {
//												previousChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
//												break nextPreviousChangeTrackingHashCdJson;
//											}
//										}
//									}
//
//									if (StringUtils.equalsIgnoreCase(previousChangeTrackingHashCode, currentChangeTrackingHashCode) == false) {
//										htmlClickHouseEntity.setHreflangLinksChgInd(true);
//										isDifferent = true;
//									}
//								}
//								break;
//							}
//							// hreflang_links_out_count
//							case IConstants.HREFLANG_LINKS_OUT_COUNT: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getHreflang_links_out_count(),
//										urlMetricsEntityV3.getHreflang_links_out_count()) == true) {
//									htmlClickHouseEntity.setHreflangLinksOutCountChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// hreflang_url_count
//							case IConstants.HREFLANG_URL_COUNT: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getHreflang_url_count(), urlMetricsEntityV3.getHreflang_url_count()) == true) {
//									htmlClickHouseEntity.setHreflangUrlCountChgInd(true);
//									isDifferent = true;
//
//									previousInteger = urlMetricsEntityV3.getHreflang_url_count() != null ? urlMetricsEntityV3.getHreflang_url_count().intValue() : 0;
//									currentInteger = htmlClickHouseEntity.getCrawlerResponse().getHreflang_url_count() != null
//											? htmlClickHouseEntity.getCrawlerResponse().getHreflang_url_count().intValue()
//											: 0;
//									changeInteger = currentInteger - previousInteger;
//
//									// hreflang_links_added_ind
//									if (changeInteger > 0) {
//										htmlClickHouseEntity.setHreflangLinksAddedInd(true);
//									}
//									// hreflang_links_removed_ind
//									else if (changeInteger < 0) {
//										htmlClickHouseEntity.setHreflangLinksRemovedInd(true);
//									}
//
//								}
//								break;
//							}
//							// index_flg
//							case IConstants.INDEX_FLG: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getIndex_flg(), urlMetricsEntityV3.getIndex_flg()) == true) {
//									htmlClickHouseEntity.setIndexFlgChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// index_flg_x_tag
//							case IConstants.INDEX_FLG_X_TAG: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getIndex_flg_x_tag(), urlMetricsEntityV3.getIndex_flg_x_tag()) == true) {
//									htmlClickHouseEntity.setIndexFlgXTagChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// indexable
//							case IConstants.INDEXABLE: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getIndexable(), urlMetricsEntityV3.getIndexable()) == true) {
//									htmlClickHouseEntity.setIndexableChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// insecure_resources
//							case IConstants.INSECURE_RESOURCES: {
//								if (currentChangeTrackingHashCdJsonArray != null && currentChangeTrackingHashCdJsonArray.length > 0) {
//									nextCurrentChangeTrackingHashCdJson: for (ChangeTrackingHashCdJson changeTrackingHashCdJson : currentChangeTrackingHashCdJsonArray) {
//										if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.INSECURE_RESOURCES) == true) {
//											currentChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
//											break nextCurrentChangeTrackingHashCdJson;
//										}
//									}
//								}
//								if (previousChangeTrackingHashCdJsonArray != null && previousChangeTrackingHashCdJsonArray.length > 0) {
//									nextPreviousChangeTrackingHashCdJson: for (ChangeTrackingHashCdJson changeTrackingHashCdJson : previousChangeTrackingHashCdJsonArray) {
//										if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.INSECURE_RESOURCES) == true) {
//											previousChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
//											break nextPreviousChangeTrackingHashCdJson;
//										}
//									}
//								}
//
//								if (StringUtils.equalsIgnoreCase(previousChangeTrackingHashCode, currentChangeTrackingHashCode) == false) {
//									htmlClickHouseEntity.setInsecureResourcesChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// insecure_resources_flag
//							case IConstants.INSECURE_RESOURCES_FLAG: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getInsecure_resources_flag(),
//										urlMetricsEntityV3.getInsecure_resources_flag()) == true) {
//									htmlClickHouseEntity.setInsecureResourcesFlagChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// meta_charset
//							case IConstants.META_CHARSET: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getMeta_charset(), urlMetricsEntityV3.getMeta_charset()) == true) {
//									htmlClickHouseEntity.setMetaCharsetChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// meta_content_type
//							case IConstants.META_CONTENT_TYPE: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getMeta_content_type(), urlMetricsEntityV3.getMeta_content_type()) == true) {
//									htmlClickHouseEntity.setMetaContentTypeChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// meta_disabled_sitelinks
//							case IConstants.META_DISABLED_SITELINKS: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getMeta_disabled_sitelinks(),
//										urlMetricsEntityV3.getMeta_disabled_sitelinks()) == true) {
//									htmlClickHouseEntity.setMetaDisabledSitelinksChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// meta_noodp
//							case IConstants.META_NOODP: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getMeta_noodp(), urlMetricsEntityV3.getMeta_noodp()) == true) {
//									htmlClickHouseEntity.setMetaNoodpChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// meta_nosnippet
//							case IConstants.META_NOSNIPPET: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getMeta_nosnippet(), urlMetricsEntityV3.getMeta_nosnippet()) == true) {
//									htmlClickHouseEntity.setMetaNosnippetChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// meta_noydir
//							case IConstants.META_NOYDIR: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getMeta_noydir(), urlMetricsEntityV3.getMeta_noydir()) == true) {
//									htmlClickHouseEntity.setMetaNoydirChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// meta_redirect
//							case IConstants.META_REDIRECT: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getMeta_redirect(), urlMetricsEntityV3.getMeta_redirect()) == true) {
//									htmlClickHouseEntity.setMetaRedirectChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// mixed_redirects
//							case IConstants.MIXED_REDIRECTS: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getMixed_redirects(), urlMetricsEntityV3.getMixed_redirects()) == true) {
//									htmlClickHouseEntity.setMixedRedirectsChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// mobile_rel_alternate_url_is_consistent
//							case IConstants.MOBILE_REL_ALTERNATE_URL_IS_CONSISTENT: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getMobile_rel_alternate_url_is_consistent(),
//										urlMetricsEntityV3.getMobile_rel_alternate_url_is_consistent()) == true) {
//									htmlClickHouseEntity.setMobileRelAlternateUrlIsConsistentChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// noodp
//							case IConstants.NOODP: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getNoodp(), urlMetricsEntityV3.getNoodp()) == true) {
//									htmlClickHouseEntity.setNoodpChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// nosnippet
//							case IConstants.NOSNIPPET: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getNosnippet(), urlMetricsEntityV3.getNosnippet()) == true) {
//									htmlClickHouseEntity.setNosnippetChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// noydir
//							case IConstants.NOYDIR: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getNoydir(), urlMetricsEntityV3.getNoydir()) == true) {
//									htmlClickHouseEntity.setNoydirChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// og_markup
//							case IConstants.OG_MARKUP: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getOg_markup_flag(), urlMetricsEntityV3.getOg_markup_flag()) == false) {
//									if (BooleanUtils.isTrue(urlSkipDomainNameFlg)) {
//
//										previousChangeTrackingHashCode = null;
//										currentChangeTrackingHashCode = null;
//
//										// previous
//										ogMarkup = urlMetricsEntityV3.getOg_markup();
//										if (ogMarkup != null) {
//											json = gson.toJson(ogMarkup, OgMarkup[].class);
//											if (checkIfJsonDataAvailable(json) == true) {
//												previousChangeTrackingHashCode = getSortedCharactersHashCode(json);
//											}
//										}
//
//										// current
//										ogMarkup = htmlClickHouseEntity.getCrawlerResponse().getOg_markup();
//										if (ogMarkup != null) {
//											json = gson.toJson(ogMarkup, OgMarkup[].class);
//											if (checkIfJsonDataAvailable(json) == true) {
//												currentChangeTrackingHashCode = getSortedCharactersHashCode(json);
//											}
//										}
//									} else {
//										if (currentChangeTrackingHashCdJsonArray != null && currentChangeTrackingHashCdJsonArray.length > 0) {
//											nextCurrentChangeTrackingHashCdJson: for (ChangeTrackingHashCdJson changeTrackingHashCdJson : currentChangeTrackingHashCdJsonArray) {
//												if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.OG_MARKUP) == true) {
//													currentChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
//													break nextCurrentChangeTrackingHashCdJson;
//												}
//											}
//										}
//										if (previousChangeTrackingHashCdJsonArray != null && previousChangeTrackingHashCdJsonArray.length > 0) {
//											nextPreviousChangeTrackingHashCdJson: for (ChangeTrackingHashCdJson changeTrackingHashCdJson : previousChangeTrackingHashCdJsonArray) {
//												if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.OG_MARKUP) == true) {
//													previousChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
//													break nextPreviousChangeTrackingHashCdJson;
//												}
//											}
//										}
//									}
//
//									if (StringUtils.equalsIgnoreCase(previousChangeTrackingHashCode, currentChangeTrackingHashCode) == false) {
//										htmlClickHouseEntity.setOgMarkupChgInd(true);
//										isDifferent = true;
//									}
//								}
//								break;
//							}
//							// og_markup_flag
//							case IConstants.OG_MARKUP_FLAG: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getOg_markup_flag(), urlMetricsEntityV3.getOg_markup_flag()) == true) {
//									htmlClickHouseEntity.setOgMarkupFlagChgInd(true);
//									isDifferent = true;
//
//									// open_graph_added_ind (ie. current is true and previous is false)
//									if (BooleanUtils.isTrue(htmlClickHouseEntity.getCrawlerResponse().getOg_markup_flag())
//											&& BooleanUtils.isFalse(urlMetricsEntityV3.getOg_markup_flag())) {
//										htmlClickHouseEntity.setOpenGraphAddedInd(true);
//									}
//									// open_graph_removed_ind (ie. current is false and previous is true)
//									else if (BooleanUtils.isFalse(htmlClickHouseEntity.getCrawlerResponse().getOg_markup_flag())
//											&& BooleanUtils.isTrue(urlMetricsEntityV3.getOg_markup_flag())) {
//										htmlClickHouseEntity.setOpenGraphRemovedInd(true);
//									}
//
//								}
//								break;
//							}
//							// og_markup_length
//							case IConstants.OG_MARKUP_LENGTH: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getOg_markup_length(), urlMetricsEntityV3.getOg_markup_length()) == true) {
//									htmlClickHouseEntity.setOgMarkupLengthChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// outlink_count
//							case IConstants.OUTLINK_COUNT: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getOutlink_count(), urlMetricsEntityV3.getOutlink_count()) == true) {
//									htmlClickHouseEntity.setOutlinkCountChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// page_link
//							case IConstants.PAGE_LINK: {
//								if (BooleanUtils.isTrue(urlSkipDomainNameFlg)) {
//
//									previousChangeTrackingHashCode = null;
//									currentChangeTrackingHashCode = null;
//
//									// previous
//									pageLink = urlMetricsEntityV3.getPage_link();
//									if (pageLink != null) {
//										json = gson.toJson(pageLink, PageLink[].class);
//										if (checkIfJsonDataAvailable(json) == true) {
//											previousChangeTrackingHashCode = getSortedCharactersHashCode(json);
//										}
//									}
//
//									// current
//									pageLink = htmlClickHouseEntity.getCrawlerResponse().getPage_link();
//									if (pageLink != null) {
//										json = gson.toJson(pageLink, PageLink[].class);
//										if (checkIfJsonDataAvailable(json) == true) {
//											currentChangeTrackingHashCode = getSortedCharactersHashCode(json);
//										}
//									}
//								} else {
//									if (currentChangeTrackingHashCdJsonArray != null && currentChangeTrackingHashCdJsonArray.length > 0) {
//										nextCurrentChangeTrackingHashCdJson: for (ChangeTrackingHashCdJson changeTrackingHashCdJson : currentChangeTrackingHashCdJsonArray) {
//											if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.PAGE_LINK) == true) {
//												currentChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
//												break nextCurrentChangeTrackingHashCdJson;
//											}
//										}
//									}
//									if (previousChangeTrackingHashCdJsonArray != null && previousChangeTrackingHashCdJsonArray.length > 0) {
//										nextPreviousChangeTrackingHashCdJson: for (ChangeTrackingHashCdJson changeTrackingHashCdJson : previousChangeTrackingHashCdJsonArray) {
//											if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.PAGE_LINK) == true) {
//												previousChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
//												break nextPreviousChangeTrackingHashCdJson;
//											}
//										}
//									}
//								}
//
//								if (StringUtils.equalsIgnoreCase(previousChangeTrackingHashCode, currentChangeTrackingHashCode) == false) {
//									htmlClickHouseEntity.setPageLinkChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// page_analysis_results
//							case IConstants.PAGE_ANALYSIS_RESULTS: {
//								// when previous page analysis results is available in queue message (ie. pageAnalysisResultInd = true or false)
//								if (urlMetricsEntityV3.getPageAnalysisResultInd() != null) {
//									// when previous crawl did not have page analysis results (ie. pageAnalysisResultInd = false)
//									if (BooleanUtils.isFalse(urlMetricsEntityV3.getPageAnalysisResultInd())) {
//										// when current crawl has page analysis results
//										if (htmlClickHouseEntity.getPageAnalysisResultArray() != null && htmlClickHouseEntity.getPageAnalysisResultArray().length > 0) {
//											isDifferent = true;
//										}
//									}
//									// when previous crawl had page analysis results (ie. pageAnalysisResultInd = true)
//									else {
//										// when current crawl does not have page analysis results
//										if (htmlClickHouseEntity.getPageAnalysisResultArray() == null || htmlClickHouseEntity.getPageAnalysisResultArray().length == 0) {
//											isDifferent = true;
//										}
//										// when current crawl has page analysis results
//										else {
//											pageAnalysisResultChgIndArray = getPageAnalysisResultChgIndArray(ip, htmlClickHouseEntity.getUrl(),
//													htmlClickHouseEntity.getPageAnalysisResultArray(), urlMetricsEntityV3.getPageAnalysisResultArray());
//											if (pageAnalysisResultChgIndArray != null && pageAnalysisResultChgIndArray.length > 0) {
//												pageAnalysisResultsChgIndJson = transformPageAnalysisResultsChgIndArrayToString(pageAnalysisResultChgIndArray);
//												if (StringUtils.isNotBlank(pageAnalysisResultsChgIndJson)) {
//													htmlClickHouseEntity.setPageAnalysisResultsChgIndJson(pageAnalysisResultsChgIndJson);
//													isDifferent = true;
//												}
//											}
//										}
//									}
//								} else {
//									// when previous page analysis results is not available in queue message, skip comparing current and previous page analysis results
//								}
//								break;
//							}
//							// robots
//							case IConstants.ROBOTS: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getRobots(), urlMetricsEntityV3.getRobots()) == true) {
//									htmlClickHouseEntity.setRobotsChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// robots_contents
//							case IConstants.ROBOTS_CONTENTS: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getRobots_flg(), urlMetricsEntityV3.getRobots_flg()) == false
//										&& isDifferent(htmlClickHouseEntity.getCrawlerResponse().getRobots_contents(), urlMetricsEntityV3.getRobots_contents()) == true) {
//									htmlClickHouseEntity.setRobotsContentsChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// robots_contents_x_tag
//							case IConstants.ROBOTS_CONTENTS_X_TAG: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getRobots_contents_x_tag(),
//										urlMetricsEntityV3.getRobots_contents_x_tag()) == true) {
//									htmlClickHouseEntity.setRobotsContentsXTagChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// robots_flg
//							case IConstants.ROBOTS_FLG: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getRobots_flg(), urlMetricsEntityV3.getRobots_flg()) == true) {
//									htmlClickHouseEntity.setRobotsFlgChgInd(true);
//									isDifferent = true;
//
//									// robots_added_ind (ie. current is Yes, previous is not Yes)
//									if (StringUtils.equalsIgnoreCase(htmlClickHouseEntity.getCrawlerResponse().getRobots_flg(), IConstants.YES) == true
//											&& StringUtils.equalsIgnoreCase(urlMetricsEntityV3.getRobots_flg(), IConstants.YES) == false) {
//										htmlClickHouseEntity.setRobotsAddedInd(true);
//									}
//									// robots_removed_ind (ie. current is No, previous is not No)
//									else if (StringUtils.equalsIgnoreCase(htmlClickHouseEntity.getCrawlerResponse().getRobots_flg(), IConstants.NO) == true
//											&& StringUtils.equalsIgnoreCase(urlMetricsEntityV3.getRobots_flg(), IConstants.NO) == false) {
//										htmlClickHouseEntity.setRobotsRemovedInd(true);
//									}
//								}
//								break;
//							}
//							// robots_flg_x_tag
//							case IConstants.ROBOTS_FLG_X_TAG: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getRobots_flg_x_tag(), urlMetricsEntityV3.getRobots_flg_x_tag()) == true) {
//									htmlClickHouseEntity.setRobotsFlgXTagChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// https://www.wrike.com/open.htm?id=390204287
//							// structured_data
//							case IConstants.STRUCTURED_DATA: {
//								if (currentChangeTrackingHashCdJsonArray != null && currentChangeTrackingHashCdJsonArray.length > 0) {
//									nextCurrentChangeTrackingHashCdJson: for (ChangeTrackingHashCdJson changeTrackingHashCdJson : currentChangeTrackingHashCdJsonArray) {
//										if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.STRUCTURED_DATA) == true) {
//											currentChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
//											break nextCurrentChangeTrackingHashCdJson;
//										}
//									}
//								}
//								if (previousChangeTrackingHashCdJsonArray != null && previousChangeTrackingHashCdJsonArray.length > 0) {
//									nextPreviousChangeTrackingHashCdJson: for (ChangeTrackingHashCdJson changeTrackingHashCdJson : previousChangeTrackingHashCdJsonArray) {
//										if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.STRUCTURED_DATA) == true) {
//											previousChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
//											break nextPreviousChangeTrackingHashCdJson;
//										}
//									}
//								}
//
//								if (StringUtils.equalsIgnoreCase(previousChangeTrackingHashCode, currentChangeTrackingHashCode) == false) {
//									htmlClickHouseEntity.setStructuredDataChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// title
//							case IConstants.TITLE: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getTitle_flg(), urlMetricsEntityV3.getTitle_flg()) == false) {
//									if (BooleanUtils.isTrue(textCaseInsensitiveFlg)) {
//										if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getTitle(), urlMetricsEntityV3.getTitle()) == true) {
//											htmlClickHouseEntity.setTitleChgInd(true);
//											isDifferent = true;
//										}
//									} else {
//										if (currentChangeTrackingHashCdJsonArray != null && currentChangeTrackingHashCdJsonArray.length > 0) {
//											nextCurrentChangeTrackingHashCdJson: for (ChangeTrackingHashCdJson changeTrackingHashCdJson : currentChangeTrackingHashCdJsonArray) {
//												if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.TITLE) == true) {
//													currentChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
//													break nextCurrentChangeTrackingHashCdJson;
//												}
//											}
//										}
//										if (previousChangeTrackingHashCdJsonArray != null && previousChangeTrackingHashCdJsonArray.length > 0) {
//											nextPreviousChangeTrackingHashCdJson: for (ChangeTrackingHashCdJson changeTrackingHashCdJson : previousChangeTrackingHashCdJsonArray) {
//												if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.TITLE) == true) {
//													previousChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
//													break nextPreviousChangeTrackingHashCdJson;
//												}
//											}
//										}
//
//										if (StringUtils.equalsIgnoreCase(previousChangeTrackingHashCode, currentChangeTrackingHashCode) == false) {
//											htmlClickHouseEntity.setTitleChgInd(true);
//											isDifferent = true;
//										}
//									}
//								}
//								break;
//							}
//							// title_flg
//							case IConstants.TITLE_FLG: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getTitle_flg(), urlMetricsEntityV3.getTitle_flg()) == true) {
//									htmlClickHouseEntity.setTitleFlgChgInd(true);
//									isDifferent = true;
//
//									// title_added_ind (ie. current is Yes, previous is not Yes)
//									if (StringUtils.equalsIgnoreCase(htmlClickHouseEntity.getCrawlerResponse().getTitle_flg(), IConstants.YES) == true
//											&& StringUtils.equalsIgnoreCase(urlMetricsEntityV3.getTitle_flg(), IConstants.YES) == false) {
//										htmlClickHouseEntity.setTitleAddedInd(true);
//									}
//									// title_removed_ind (ie. current is No, previous is not No)
//									else if (StringUtils.equalsIgnoreCase(htmlClickHouseEntity.getCrawlerResponse().getTitle_flg(), IConstants.NO) == true
//											&& StringUtils.equalsIgnoreCase(urlMetricsEntityV3.getTitle_flg(), IConstants.NO) == false) {
//										htmlClickHouseEntity.setTitleRemovedInd(true);
//									}
//								}
//								break;
//							}
//							// title_length
//							case IConstants.TITLE_LENGTH: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getTitle_length(), urlMetricsEntityV3.getTitle_length()) == true) {
//									htmlClickHouseEntity.setTitleLengthChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// title_md5
//							case IConstants.TITLE_MD5: {
//								if (BooleanUtils.isTrue(textCaseInsensitiveFlg)) {
//									// when text comparision is case insensitive, skip md5 comparison
//								} else {
//									if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getTitle_md5(), urlMetricsEntityV3.getTitle_md5()) == true) {
//										htmlClickHouseEntity.setTitleMd5ChgInd(true);
//										isDifferent = true;
//									}
//								}
//								break;
//							}
//							// title_simhash
//							case IConstants.TITLE_SIMHASH: {
//								if (BooleanUtils.isTrue(textCaseInsensitiveFlg)) {
//									// when text comparision is case insensitive, skip simhash comparison
//								} else {
//									if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getTitle_simhash(), urlMetricsEntityV3.getTitle_simhash()) == true) {
//										htmlClickHouseEntity.setTitleSimhashChgInd(true);
//										isDifferent = true;
//									}
//								}
//								break;
//							}
//							// viewport_content
//							case IConstants.VIEWPORT_CONTENT: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getViewport_flag(), urlMetricsEntityV3.getViewport_flag()) == false) {
//									if (currentChangeTrackingHashCdJsonArray != null && currentChangeTrackingHashCdJsonArray.length > 0) {
//										nextCurrentChangeTrackingHashCdJson: for (ChangeTrackingHashCdJson changeTrackingHashCdJson : currentChangeTrackingHashCdJsonArray) {
//											if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.VIEWPORT_CONTENT) == true) {
//												currentChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
//												break nextCurrentChangeTrackingHashCdJson;
//											}
//										}
//									}
//									if (previousChangeTrackingHashCdJsonArray != null && previousChangeTrackingHashCdJsonArray.length > 0) {
//										nextPreviousChangeTrackingHashCdJson: for (ChangeTrackingHashCdJson changeTrackingHashCdJson : previousChangeTrackingHashCdJsonArray) {
//											if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.VIEWPORT_CONTENT) == true) {
//												previousChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
//												break nextPreviousChangeTrackingHashCdJson;
//											}
//										}
//									}
//
//									if (StringUtils.equalsIgnoreCase(previousChangeTrackingHashCode, currentChangeTrackingHashCode) == false) {
//										htmlClickHouseEntity.setViewportContentChgInd(true);
//										isDifferent = true;
//									}
//								}
//								break;
//							}
//							// viewport_flag
//							case IConstants.VIEWPORT_FLAG: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getViewport_flag(), urlMetricsEntityV3.getViewport_flag()) == true) {
//									htmlClickHouseEntity.setViewportFlagChgInd(true);
//									isDifferent = true;
//
//									// viewport_added_ind (ie. current is true and previous is false)
//									if (BooleanUtils.isTrue(htmlClickHouseEntity.getCrawlerResponse().getViewport_flag())
//											&& BooleanUtils.isFalse(urlMetricsEntityV3.getViewport_flag())) {
//										htmlClickHouseEntity.setViewportAddedInd(true);
//									}
//									// viewport_removed_ind (ie. current is false and previous is true)
//									else if (BooleanUtils.isFalse(htmlClickHouseEntity.getCrawlerResponse().getViewport_flag())
//											&& BooleanUtils.isTrue(urlMetricsEntityV3.getViewport_flag())) {
//										htmlClickHouseEntity.setViewportRemovedInd(true);
//									}
//
//								}
//								break;
//							}
//							// base_tag
//							case IConstants.BASE_TAG: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getBase_tag_flag(), urlMetricsEntityV3.getBase_tag_flag()) == false
//										&& isDifferent(htmlClickHouseEntity.getCrawlerResponse().getBase_tag(), urlMetricsEntityV3.getBase_tag()) == true) {
//									htmlClickHouseEntity.setBaseTagChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// base_tag_flag
//							case IConstants.BASE_TAG_FLAG: {
//
//								// No change when
//								// 1) current crawl result does not detect <base> tag (ie. base_tag_flag is false)
//								// 2) record created before the changes for https://www.wrike.com/open.htm?id=539211931 is null (ie. base_tag_flag is null)
//								if (BooleanUtils.isFalse(htmlClickHouseEntity.getCrawlerResponse().getBase_tag_flag()) && urlMetricsEntityV3.getBase_tag_flag() == null) {
//									// No change
//								} else if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getBase_tag_flag(), urlMetricsEntityV3.getBase_tag_flag()) == true) {
//
//									isDifferent = true;
//
//									// base_tag_added_ind (ie. current is true and previous is false)
//									if (BooleanUtils.isTrue(htmlClickHouseEntity.getCrawlerResponse().getBase_tag_flag())
//											&& BooleanUtils.isFalse(urlMetricsEntityV3.getBase_tag_flag())) {
//										htmlClickHouseEntity.setBaseTagAddedInd(true);
//									}
//									// base_tag_removed_ind (ie. current is false and previous is true)
//									else if (BooleanUtils.isFalse(htmlClickHouseEntity.getCrawlerResponse().getBase_tag_flag())
//											&& BooleanUtils.isTrue(urlMetricsEntityV3.getBase_tag_flag())) {
//										htmlClickHouseEntity.setBaseTagRemovedInd(true);
//									}
//
//								}
//								break;
//							}
//							// base_tag_target
//							case IConstants.BASE_TAG_TARGET: {
//								if (isDifferent(htmlClickHouseEntity.getCrawlerResponse().getBase_tag_target(), urlMetricsEntityV3.getBase_tag_target()) == true) {
//									htmlClickHouseEntity.setBaseTagTargetChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// page_analysis_fragments
//							case IConstants.PAGE_ANALYSIS_FRAGMENTS: {
//								if (currentChangeTrackingHashCdJsonArray != null && currentChangeTrackingHashCdJsonArray.length > 0) {
//									nextCurrentChangeTrackingHashCdJson: for (ChangeTrackingHashCdJson changeTrackingHashCdJson : currentChangeTrackingHashCdJsonArray) {
//										if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.PAGE_ANALYSIS_FRAGMENTS) == true) {
//											currentChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
//											break nextCurrentChangeTrackingHashCdJson;
//										}
//									}
//								}
//								if (previousChangeTrackingHashCdJsonArray != null && previousChangeTrackingHashCdJsonArray.length > 0) {
//									nextPreviousChangeTrackingHashCdJson: for (ChangeTrackingHashCdJson changeTrackingHashCdJson : previousChangeTrackingHashCdJsonArray) {
//										if (StringUtils.equalsIgnoreCase(changeTrackingHashCdJson.getName(), IConstants.PAGE_ANALYSIS_FRAGMENTS) == true) {
//											previousChangeTrackingHashCode = changeTrackingHashCdJson.getValue();
//											break nextPreviousChangeTrackingHashCdJson;
//										}
//									}
//								}
//								if (StringUtils.equalsIgnoreCase(previousChangeTrackingHashCode, currentChangeTrackingHashCode) == false) {
//									htmlClickHouseEntity.setPageAnalysisFragmentsChgInd(true);
//									isDifferent = true;
//								}
//								break;
//							}
//							// response_headers (names)
//							case IConstants.RESPONSE_HEADERS: {
//								currentResponseHeaderNames = getResponseHeaderNames(htmlClickHouseEntity.getCrawlerResponse().getResponse_headers());
//								//if (isDebug == true) {
//								// (tested) test case 1: current response header names is not empty, previous response header names is not empty, and current = previous, no change
//								// (tested) test case 2: current response header names is empty, previous response header names is not empty, response_headers_removed_ind = true
//								// (tested) test case 3: current response header names is not empty, previous response header names is empty,  response_headers_added_ind = true
//								// (tested) test case 4: current response header names is not empty, previous response header names is not empty, current header name added, response_headers_added_ind = true
//								// (tested) test case 5: current response header names is not empty, previous response header names is not empty, previous header name removed, response_headers_removed_ind = true
//								// (tested) test case 6: current response header names is not empty, previous response header names is not empty, current header name added, response_headers_added_ind = true
//								// (tested) test case 6: current response header names is not empty, previous response header names is not empty, previous header name removed, response_headers_removed_ind = true
//								//List<ResponseHeaders> testResponseHeaderList = new ArrayList<ResponseHeaders>(Arrays.asList(htmlClickHouseEntity.getCrawlerResponse().getResponse_headers()));
//								//testResponseHeaderList.remove(0);
//								//ResponseHeaders responseHeaders = new ResponseHeaders();
//								//responseHeaders.setName("New-Response-Header-Name1");
//								//responseHeaders.setValue("New-Response-Header-Value1");
//								//testResponseHeaderList.add(responseHeaders);
//								//responseHeaders = new ResponseHeaders();
//								//responseHeaders.setName("New-Response-Header-Name2");
//								//responseHeaders.setValue("New-Response-Header-Value2");
//								//testResponseHeaderList.add(responseHeaders);
//								//htmlClickHouseEntity.getCrawlerResponse().setResponse_headers(testResponseHeaderList.toArray(new ResponseHeaders[0]));
//								//currentResponseHeaderNames = getResponseHeaderNames(htmlClickHouseEntity.getCrawlerResponse().getResponse_headers());
//								//if (currentResponseHeaderNames != null && currentResponseHeaderNames.length > 0) {
//								//	System.out.println("currentResponseHeaderNames=" + Arrays.asList(currentResponseHeaderNames).toString());
//								//} else {
//								//	System.out.println("currentResponseHeaderNames is empty.");
//								//}
//								//if (urlMetricsEntityV3.getResponse_header_names() != null && urlMetricsEntityV3.getResponse_header_names().length > 0) {
//								//	System.out.println("previousResponseHeaderNames=" + Arrays.asList(urlMetricsEntityV3.getResponse_header_names()).toString());
//								//} else {
//								//	System.out.println("previousResponseHeaderNames is empty.");
//								//}
//								//}
//								htmlClickHouseEntity
//										.setResponseHeadersAddedInd(getResponseHeadersAddedInd(currentResponseHeaderNames, urlMetricsEntityV3.getResponse_header_names()));
//								htmlClickHouseEntity.setResponseHeadersRemovedInd(
//										getResponseHeadersRemovedInd(currentResponseHeaderNames, urlMetricsEntityV3.getResponse_header_names()));
//
//								if (BooleanUtils.isTrue(htmlClickHouseEntity.getResponseHeadersAddedInd())
//										|| BooleanUtils.isTrue(htmlClickHouseEntity.getResponseHeadersRemovedInd())) {
//									isDifferent = true;
//								}
//
//								//if (isDebug == true) {
//								//	System.out.println("responseHeadersAddedInd=" + htmlClickHouseEntity.getResponseHeadersAddedInd() + ",responseHeadersRemovedInd="
//								//			+ htmlClickHouseEntity.getResponseHeadersRemovedInd() + ",isDifferent=" + isDifferent);
//								//}
//
//								break;
//							}
//							default:
//								break;
//						}
//					}
//				}
//			}
//		}
//		return isDifferent;
//	}
//
//public boolean isDifferent(Boolean value1, Boolean value2) {
//		boolean output = false;
//		if (value1 == null && value2 == null) {
//			output = false;
//		} else if (value1 != null && value2 == null) {
//			output = true;
//		} else if (value1 == null && value2 != null) {
//			output = true;
//		} else if (value1.booleanValue() != value2.booleanValue()) {
//			output = true;
//		}
//		return output;
//	}
//
//	public boolean isDifferent(Integer value1, Integer value2) {
//		boolean output = false;
//		if (value1 == null && value2 == null) {
//			output = false;
//		} else if (value1 != null && value2 == null) {
//			output = true;
//		} else if (value1 == null && value2 != null) {
//			output = true;
//		} else if (value1.intValue() != value2.intValue()) {
//			output = true;
//		}
//		return output;
//	}
//
//	public boolean isDifferent(String value1, String value2) {
//		boolean output = false;
//		if (StringUtils.isBlank(value1) && StringUtils.isBlank(value2)) {
//			output = false;
//		} else if (StringUtils.isBlank(value1) && StringUtils.isNotBlank(value2)) {
//			output = true;
//		} else if (StringUtils.isNotBlank(value1) && StringUtils.isBlank(value2)) {
//			output = true;
//		} else if (StringUtils.isNotBlank(value1) && StringUtils.isNotBlank(value2) && !StringUtils.equalsIgnoreCase(value1, value2)) {
//			output = true;
//		}
//		return output;
//	}
//
//	public boolean isDifferent(BigInteger value1, BigInteger value2) {
//		boolean output = false;
//		if (value1 == null && value2 == null) {
//			output = false;
//		} else if (value1 != null && value2 == null) {
//			output = true;
//		} else if (value1 == null && value2 != null) {
//			output = true;
//		} else if (value1.compareTo(value2) != 0) {
//			output = true;
//		}
//		return output;
//	}
//
//	public boolean isDifferent(Date value1, Date value2) {
//		boolean output = false;
//		String formattedDateString1 = null;
//		String formattedDateString2 = null;
//		if (value1 == null && value2 == null) {
//			output = false;
//		} else if (value1 != null && value2 == null) {
//			output = true;
//		} else if (value1 == null && value2 != null) {
//			output = true;
//		} else {
//			formattedDateString1 = DateFormatUtils.format(value1, IConstants.DATE_FORMAT_YYYYMMDDHHMMSS);
//			formattedDateString2 = DateFormatUtils.format(value2, IConstants.DATE_FORMAT_YYYYMMDDHHMMSS);
//			if (!StringUtils.equalsIgnoreCase(formattedDateString1, formattedDateString2)) {
//				output = true;
//			}
//		}
//		return output;
//	}
//
//	public boolean isDifferent(String[] value1, String[] value2) {
//		boolean output = false;
//		StringBuilder stringBuilder1 = null;
//		StringBuilder stringBuilder2 = null;
//		String string1 = null;
//		String string2 = null;
//		if (value1 == null && value2 == null) {
//			output = false;
//		} else if (value1 != null && value2 == null) {
//			output = true;
//		} else if (value1 == null && value2 != null) {
//			output = true;
//		} else {
//			stringBuilder1 = new StringBuilder();
//			for (String testString : value1) {
//				stringBuilder1.append(testString);
//			}
//			string1 = getSortedCharactersHashCode(stringBuilder1.toString());
//			stringBuilder2 = new StringBuilder();
//			for (String testString : value2) {
//				stringBuilder2.append(testString);
//			}
//			string2 = getSortedCharactersHashCode(stringBuilder2.toString());
//			if (StringUtils.equalsIgnoreCase(string1, string2) == false) {
//				output = true;
//			}
//		}
//		return output;
//	}
//}
