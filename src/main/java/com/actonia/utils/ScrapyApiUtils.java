package com.actonia.utils;

import com.actonia.IConstants;
import com.actonia.entity.AdditionalContentEntity;
import com.actonia.exception.GatewayException;
import com.actonia.value.object.*;
import com.google.gson.Gson;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicHeader;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.web.util.UriUtils;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import java.io.File;
import java.io.FileWriter;
import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.net.SocketTimeoutException;
import java.net.URL;
import java.net.URLEncoder;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class ScrapyApiUtils {

    private static final Logger log = LogManager.getLogger(ScrapyApiUtils.class);
    private static final String[] scrapyCrawlerEndpointArray;
    private static final String[] scrapyCrawlerLondonEndpointArray;
    private static final String scrapyMobileUserAgent = PoliteCrawlConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_SCRAPY_MOBILE_USER_AGENT);
    private static final int CONNECT_TIMEOUT_IN_SECONDS = 168;
    private static final int CONNECTION_REQUEST_TIMEOUT_IN_SECONDS = 168;
    private static final int SOCKET_TIMEOUT_IN_SECONDS = 168;
    private static final String scrapyCrawlerAccessKey = PoliteCrawlConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_SCRAPY_CRAWLER_ACCESS_KEY);
    private static final String SCRAPY_CRAWLER_REQUEST_LOG_LOCATION = "/data/polite_crawl_backup/scrapy_crawler_request/";
    private static final String today = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
    private static final String LINE_SEPARATOR = "==================================";
    private static final String REQUEST_SPLIT_LINE = "----------------------------------";
    private static final CloseableHttpClient httpClient;
    // create a single instance
    private static final ScrapyApiUtils INSTANCE = new ScrapyApiUtils();

    static {
        // use the TrustSelfSignedStrategy to allow Self Signed Certificates
        SSLContext sslContext;
        try {
            sslContext = SSLContextBuilder.create().loadTrustMaterial(new TrustSelfSignedStrategy()).build();
        } catch (NoSuchAlgorithmException | KeyManagementException | KeyStoreException e) {
            throw new RuntimeException(e);
        }

        // we can optionally disable hostname verification.
        // if you don't want to further weaken the security, you don't have to include this.
        HostnameVerifier allowAllHosts = new NoopHostnameVerifier();

        // create an SSL Socket Factory to use the SSLContext with the trust self signed certificate strategy
        // and allow all hosts verifier.
        SSLConnectionSocketFactory connectionFactory = new SSLConnectionSocketFactory(sslContext, allowAllHosts);

        final PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(200);
        connectionManager.setDefaultMaxPerRoute(200);

        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(CONNECT_TIMEOUT_IN_SECONDS * 1000)
                .setSocketTimeout(SOCKET_TIMEOUT_IN_SECONDS * 1000)
                .setConnectionRequestTimeout(CONNECTION_REQUEST_TIMEOUT_IN_SECONDS * 1000)
                .build();

        Collection<Header> defaultHeaders = new ArrayList<>(4);
        defaultHeaders.add(new BasicHeader(IConstants.CONTENT_DASH_TYPE, IConstants.APPLICATION_SLASH_JSON));
        defaultHeaders.add(new BasicHeader(IConstants.ACCESS_KEY, scrapyCrawlerAccessKey));
        defaultHeaders.add(new BasicHeader(IConstants.CACHE_CONTROL, IConstants.NO_CACHE));
        defaultHeaders.add(new BasicHeader(IConstants.CONTENT_TYPE, IConstants.APPLICATION_SLASH_JSON));

        httpClient = HttpClients.custom()
                .setSSLSocketFactory(connectionFactory)
                .setConnectionManager(connectionManager)
                .setDefaultRequestConfig(requestConfig)
                .setConnectionManagerShared(true)
                .setDefaultHeaders(defaultHeaders)
                .build();
        final String scrapyCrawlerEndpoints = PoliteCrawlConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_SCRAPY_CRAWLER_ENDPOINTS);
        scrapyCrawlerEndpointArray = scrapyCrawlerEndpoints.split(",");
        final String scrapyCrawlerLondonEndpoints = PoliteCrawlConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_SCRAPY_CRAWLER_LONDON_ENDPOINTS);
        scrapyCrawlerLondonEndpointArray = scrapyCrawlerLondonEndpoints.split(",");

    }

    private final Gson gson = new Gson();

    public static ScrapyApiUtils getInstance() {
        return INSTANCE;
    }

    private static SiteclaritySettings getSiteclaritySettings(int crawlType) {
        boolean includeStructuredDataValidationErrors = false;
        boolean includeStructuredDataValidationData = false;
        boolean extractStructuredData = true;
        switch (crawlType) {
            case IConstants.CRAWL_TYPE_TARGET_URL_HTML:
            case IConstants.CRAWL_TYPE_CONTENT_GUARD:
                // https://www.wrike.com/open.htm?id=390204287
                includeStructuredDataValidationErrors = true;
                break;
            case IConstants.CRAWL_TYPE_COMPETITOR_URL_HTML:
                // https://www.wrike.com/open.htm?id=390204287
                break;
            default:
                extractStructuredData = false;
                includeStructuredDataValidationData = true;
                break;
        }
        final SiteclaritySettings siteclaritySettings = new SiteclaritySettings();
        siteclaritySettings.setExtractStructuredData(extractStructuredData);
        siteclaritySettings.setIncludeStructuredDataValidationErrors(includeStructuredDataValidationErrors);
        siteclaritySettings.setIncludeStructuredDataValidationData(includeStructuredDataValidationData);
        siteclaritySettings.setValidate_structured_schema(true);
        return siteclaritySettings;
    }

    public ScrapyCrawlerRequest getScrapyCrawlerRequest(int domainId,
                                                        String encodedUrlString,
                                                        String userAgent,
                                                        List<AdditionalContentEntity> additionalContentEntities,
                                                        Boolean enableJavascriptCrawl,
                                                        Integer javascriptTimeoutInSecond,
                                                        int crawlType,
                                                        Map<String, String> pageCrawlerApiRequestHeaders,
                                                        Boolean isStoreHtml,
                                                        String s3Location,
                                                        String endpoint) {
        DecodedEncodedUrlValueObject decodedEncodedUrlValueObject = CrawlerUtils.getDecodedAndEncodedUrlString(encodedUrlString, false);
        final SiteclaritySettings siteclaritySettings = getSiteclaritySettings(crawlType);

        final String encodedUrl = decodedEncodedUrlValueObject.getEncodedUrlString();
        try {
            final URL url = new URL(encodedUrl);
            final String host = url.getHost();
            if (StringUtils.endsWithIgnoreCase(host, IConstants.GRAINGER_COM)) {
                if (StringUtils.containsIgnoreCase(encodedUrlString, IConstants.SLASH_MOBILE_SLASH)
                        || StringUtils.endsWithIgnoreCase(host, IConstants.M_GRAINGER_COM)) {
                    userAgent = IConstants.WWW_GRAINGER_COM_MOBILE_USER_AGENT;
                }
            } else {
                if (StringUtils.isNotBlank(host) && StringUtils.startsWithIgnoreCase(host, IConstants.MOBILE_SITE_HOSTNAME_PREFIX)) {
                    userAgent = scrapyMobileUserAgent;
                }
            }
        } catch (MalformedURLException e) {
            log.error("MalformedURLException: {}", encodedUrl);
        }
        if (crawlType != IConstants.CRAWL_TYPE_TARGET_URL_HTML && crawlType != IConstants.CRAWL_TYPE_CONTENT_GUARD) {
            domainId = 0;
        }
        final ScrapyCrawlerRequest.ScrapySettings scrapySettings = new ScrapyCrawlerRequest.ScrapySettings();
        scrapySettings.setUserAgent(userAgent);
        scrapySettings.setDefaultRequestHeaders(pageCrawlerApiRequestHeaders);
        if (enableJavascriptCrawl) {
            final JavascriptSettings javascriptSettings = new JavascriptSettings();
            float pageLoadTime = 10f;
            if (javascriptTimeoutInSecond != null && javascriptTimeoutInSecond > 0) {
                pageLoadTime = javascriptTimeoutInSecond.floatValue();
            }
            javascriptSettings.setPage_load_timeout(pageLoadTime);
            javascriptSettings.setWait_after_last_request(2f);
            javascriptSettings.setHeaders(pageCrawlerApiRequestHeaders);
            // move SPLASH_URL field from siteclarity_settings to scrapy_settings # https://www.wrike.com/open.htm?id=1330304395
            if (StringUtils.equalsIgnoreCase(endpoint, IConstants.RUNTIME_PROPERTY_NAME_SCRAPY_CRAWLER_ENDPOINTS)) {
                // https://www.wrike.com/open.htm?id=979079133
                //siteclarity_settings.setSplash_request_url("http://************:3000/execute");
                scrapySettings.setSplashUrl("http://*************/execute");
                javascriptSettings.setAudit_resources(false);
            }
            // https://www.wrike.com/open.htm?id=985233501
            else if (StringUtils.equalsIgnoreCase(endpoint, IConstants.RUNTIME_PROPERTY_NAME_SCRAPY_CRAWLER_LONDON_ENDPOINTS)) {
                scrapySettings.setSplashUrl("http://**************:3000/execute");
                javascriptSettings.setAudit_resources(false);
            }
            siteclaritySettings.setJavascript_enabled(true);
            siteclaritySettings.setJavascript_settings(javascriptSettings);
        }

        // Check if the additionalContentEntities list is not null and not empty
        if (additionalContentEntities != null && !additionalContentEntities.isEmpty()) {
            // Retrieve a list of CustomDataRequests using the encoded URL string and additionalContentEntities
            final List<CustomDataRequest> customDataRequestList = getCustomDataRequestList(encodedUrlString, additionalContentEntities);
            // Check if the customDataRequestList is not null
            if (customDataRequestList != null) {
                // Convert the customDataRequestList to an array and set it in siteclaritySettings
                final CustomDataRequest[] customDataRequestListArray = customDataRequestList.toArray(new CustomDataRequest[0]);
                siteclaritySettings.setCustom_data_requests(customDataRequestListArray);
            }
        }
        // when Content Guard crawl target URL, request Page Crawler API to store the raw HTML in S3
        if (crawlType == IConstants.CRAWL_TYPE_CONTENT_GUARD) {
            if (isStoreHtml) {
                siteclaritySettings.setStore_html(true);
                siteclaritySettings.setDomain_id(domainId);
            }
        }

        if (StringUtils.isNotBlank(s3Location)) {
            siteclaritySettings.setJavascript_enabled(true);
            siteclaritySettings.setStore_html(false);
            siteclaritySettings.setEnable_custom_source(true);
            CustomSourceInfo customSourceInfo = new CustomSourceInfo();
            customSourceInfo.setResource_url(s3Location);
            customSourceInfo.setResource_type(IConstants.RESOURCE_TYPE_WARC);
            customSourceInfo.setResource_location_type(IConstants.RESOURCE_LOCATION_TYPE_S3);
            siteclaritySettings.setCustom_source_info(customSourceInfo);
        }

        ScrapyCrawlerRequest request = new ScrapyCrawlerRequest();
        request.setUrl(Collections.singletonList(encodedUrl));
        request.setSiteclarity_settings(siteclaritySettings);
        request.setScrapySettings(scrapySettings);
        return request;
    }

    /**
     * Retrieves a list of CustomDataRequests based on the given encoded URL string and list of AdditionalContentEntity.
     *
     * @param encodedUrlString The encoded URL string
     * @param additionalContentEntityList The list of AdditionalContentEntity
     * @return The list of CustomDataRequests
     */
    private List<CustomDataRequest> getCustomDataRequestList(String encodedUrlString, List<AdditionalContentEntity> additionalContentEntityList) {
        AtomicInteger customDataRequestIndex = new AtomicInteger();
        return additionalContentEntityList.stream()
                // Filter the AdditionalContentEntity based on the selected URL
                .filter(additionalContentEntity -> checkIfUrlSelected(encodedUrlString, additionalContentEntity))
                // Map the AdditionalContentEntity to a CustomDataRequest
                .map(additionalContentEntity -> createCustomDataRequest(additionalContentEntity, customDataRequestIndex.incrementAndGet()))
                // Collect the CustomDataRequests to a list
                .collect(Collectors.toList());
    }

    /**
     * Creates a CustomDataRequest based on the given AdditionalContentEntity and index.
     *
     * @param additionalContentEntity The AdditionalContentEntity containing the data for the request
     * @param index The index of the request
     * @return The created CustomDataRequest
     */
    private CustomDataRequest createCustomDataRequest(AdditionalContentEntity additionalContentEntity, int index) {
        // Create a new CustomDataRequest
        final CustomDataRequest customDataRequest = new CustomDataRequest();
        // Set the index
        customDataRequest.setIndex(index);
        // Set the selector type based on the AdditionalContentEntity selector type
        customDataRequest.setSelector_type(getSelectorType(additionalContentEntity.getSelectorType()));
        // Set the selector from AdditionalContentEntity
        customDataRequest.setSelector(additionalContentEntity.getSelector());
        // Create and set the required fields
        customDataRequest.setFields_required(createFieldsRequired());
        // Enable multi-match
        customDataRequest.setEnable_multi_match(true);
        // Return the created CustomDataRequest
        return customDataRequest;
    }

    private String getSelectorType(int selectorType) {
        switch (selectorType) {
            case IConstants.SELECTOR_TYPE_NUMBER_XPATH:
                return IConstants.SELECTOR_TYPE_TEXT_XPATH;
            case IConstants.SELECTOR_TYPE_NUMBER_CSS:
                return IConstants.SELECTOR_TYPE_TEXT_CSS;
            case IConstants.SELECTOR_TYPE_NUMBER_DIV_ID:
                return IConstants.SELECTOR_TYPE_TEXT_DIV_ID;
            case IConstants.SELECTOR_TYPE_NUMBER_DIV_CLASS:
                return IConstants.SELECTOR_TYPE_TEXT_DIV_CLASS;
            default:
                return null;
        }
    }
    private FieldsRequired createFieldsRequired() {
        FieldsRequired fieldsRequired = new FieldsRequired();
        fieldsRequired.setLink(true);
        fieldsRequired.setContent(true);
        fieldsRequired.setWord_count(true);
        return fieldsRequired;
    }

    /**
     * This method checks if the given URL is selected based on the type of URL selector.
     *
     * @param encodedUrlString The encoded URL string to check against
     * @param additionalContentEntity The object containing URL selector type and URL selector
     * @return true if the URL is selected, else false
     */
    public boolean checkIfUrlSelected(String encodedUrlString, AdditionalContentEntity additionalContentEntity) {
        // Get the URL selector type from additionalContentEntity
        int urlSelectorType = additionalContentEntity.getUrlSelectorType();
        // Get the URL selector from additionalContentEntity
        final String urlSelector = additionalContentEntity.getUrlSelector();
        // Check if the URL selector is blank
        if (StringUtils.isBlank(urlSelector)) {
            // Log a warning
            log.warn("checkIfUrlSelected() urlSelector is blank return false.encodedUrlString= {}", encodedUrlString);
            return false;
        }
        // Check the URL selector type and return the result accordingly
        switch (urlSelectorType) {
            case IConstants.URL_SELECTOR_TYPE_ALL:
                return true;
            case IConstants.URL_SELECTOR_TYPE_CONTAINS:
                return checkContainsSelector(encodedUrlString, urlSelector);
            case IConstants.URL_SELECTOR_TYPE_EQUALS:
                return checkEqualsSelector(encodedUrlString, urlSelector);
            case IConstants.URL_SELECTOR_TYPE_REGEXP:
                return checkRegexpSelector(encodedUrlString, urlSelector);
            default:
                return false;
        }
    }

    /**
     * Checks if the encoded URL string contains the URL selector after decoding and encoding it if needed.
     *
     * @param encodedUrlString The encoded URL string to check against
     * @param urlSelector The URL selector to check for in the encoded URL string
     * @return true if the encoded URL string contains the URL selector, false otherwise
     */
    private boolean checkContainsSelector(String encodedUrlString, String urlSelector) {
        try {
            // Decode and encode the URL selector if necessary
            DecodedEncodedUrlValueObject decodedAndEncodedUrlString = CrawlerUtils.getDecodedAndEncodedUrlString(urlSelector, false);
            String urlSelectorEncoded = decodedAndEncodedUrlString.getErrorIndicator() ?
                    StringUtils.replace(URLEncoder.encode(urlSelector, IConstants.UTF_8), IConstants.SLASH_UTF8_ENCODED, IConstants.SLASH) :
                    decodedAndEncodedUrlString.getEncodedUrlString();

            // Check if the encoded URL string contains the encoded URL selector ignoring case
            return StringUtils.containsIgnoreCase(encodedUrlString, urlSelectorEncoded);
        } catch (Exception e) {
            log.error("checkContainsSelector() urlSelector={}, encodedUrlString= {}, caught exception: {}", urlSelector, encodedUrlString, e.getMessage());
            return false;
        }
    }

    /**
     * Checks if the encoded URL string is equal to the encoded URL selector.
     *
     * @param encodedUrlString The encoded URL string to compare.
     * @param urlSelector The URL selector to compare.
     * @return true if the encoded URL string is equal to the encoded URL selector, false otherwise.
     */
    private boolean checkEqualsSelector(String encodedUrlString, String urlSelector) {
        try {
            // Encode the URL selector using UTF-8
            String urlSelectorEncoded = UriUtils.encodeUri(urlSelector, IConstants.UTF_8);

            // Compare the encoded URL string with the encoded URL selector
            return StringUtils.equalsIgnoreCase(encodedUrlString, urlSelectorEncoded);
        } catch (Exception e) {
            // Log the error if an exception occurs
            log.error("checkEqualsSelector() urlSelector={}, encodedUrlString= {}, caught exception: {}", urlSelector, encodedUrlString, e.getMessage());
            return false;
        }
    }

    /**
     * Checks if the given URL matches the specified regular expression selector after decoding the URL.
     *
     * @param encodedUrlString The URL string to be decoded.
     * @param urlSelector The regular expression selector to match against the decoded URL.
     * @return true if the decoded URL matches the selector, false otherwise.
     */
    private boolean checkRegexpSelector(String encodedUrlString, String urlSelector) {
        try {
            // Decode the URL string
            final String unencodedUrlString = UriUtils.decode(encodedUrlString, IConstants.UTF_8);

            // Check if the decoded URL matches the specified regular expression selector
            return Pattern.matches(urlSelector, unencodedUrlString);
        } catch (UnsupportedEncodingException e) {
            // Log error if there's an exception during decoding
            log.error("checkRegexpSelector() urlSelector={}, encodedUrlString= {}, caught exception: {}", urlSelector, encodedUrlString, e.getMessage());
            return false;
        }
    }

    public ScrapyCrawlerResponse getScrapyCrawlerResponse(ScrapyCrawlerRequest scrapyCrawlerRequest, int crawlType, int domainId, String endpoint) {

        final String requestLogFileName = SCRAPY_CRAWLER_REQUEST_LOG_LOCATION + File.separator + today + File.separator + domainId + ".log";
        createDirectoryIfNotExists(requestLogFileName);
        final String url = scrapyCrawlerRequest.getUrl().get(0);
        final String requestJSON = gson.toJson(scrapyCrawlerRequest, ScrapyCrawlerRequest.class);
        final StringEntity stringEntity = new StringEntity(requestJSON, IConstants.UTF_8);
        ScrapyCrawlerResponse scrapyCrawlerResponse = new ScrapyCrawlerResponse();
        HttpEntity responseEntity = null;
        int retryCount = 0;
        while (retryCount <= IConstants.MAX_SCRAPY_API_RETRY_COUNT) {
            final HttpPost httpPost = new HttpPost(endpoint);
            httpPost.setEntity(stringEntity);
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                final int statusCode = response.getStatusLine().getStatusCode();
                responseEntity = response.getEntity();
                final String responseEntityString = EntityUtils.toString(responseEntity, IConstants.UTF_8);
                writeResponseToFileAsync(requestLogFileName, requestJSON, statusCode, retryCount, responseEntityString);
                log.info("Scrapy response - domainId={}, encodedUrlString= {}, statusCode={}, endpoint={}, retryCount={}", domainId, url, statusCode, endpoint, retryCount);
                final boolean statusCode5xx = statusCode >= IConstants.HTTP_STATUS_CODE_500 && statusCode < IConstants.HTTP_STATUS_CODE_600;
                if (statusCode5xx) {
                    log.error("Bad Gateway status code = {}, domainId = {}, encodedUrlString = {}, endpoint = {}, retryCount = {}", statusCode, domainId, url, endpoint, retryCount);
                    // when TARGET_URL_HTML_PT_7726, no need to retry
                    if (domainId == 7726) {
                        break;
                    }
                    throw new GatewayException(IConstants.GATEWAY_EXCEPTION_MSG);
                }

                CrawlerResponse crawlerResponse;
                if (statusCode == IConstants.HTTP_STATUS_CODE_200) {
                    crawlerResponse = gson.fromJson(responseEntityString, CrawlerResponse.class);
                    final int responseCode = NumberUtils.toInt(crawlerResponse.getResponse_code());

                    // when processing target URLs or content guard URLs, retry when HTTP status code is 999, 502, 504
                    // or 4xx https://www.wrike.com/open.htm?id=1066871196
                    if (crawlType == IConstants.CRAWL_TYPE_TARGET_URL_HTML || crawlType == IConstants.CRAWL_TYPE_CONTENT_GUARD) {
                        final boolean status4xx = responseCode >= IConstants.HTTP_STATUS_CODE_400 && responseCode < IConstants.HTTP_STATUS_CODE_500;
                        final boolean status5xx = responseCode >= IConstants.HTTP_STATUS_CODE_500 && responseCode < IConstants.HTTP_STATUS_CODE_600;
                        final boolean status9xx = responseCode >= IConstants.HTTP_STATUS_CODE_900;
                        if (status4xx || status5xx || status9xx) {
                            // when TARGET_URL_HTML_PT_7726, no need to retry jump out the loop
                            if (domainId == 7726) {
                                break;
                            }
                            // retry
                            if (retryCount <= IConstants.MAX_SCRAPY_API_RETRY_COUNT) {
                                log.warn("pause and retry. domainId={}, encodedUrlString= {}, httpStatus= {}, endpoint= {}, retryCount={}.", domainId, url, responseCode, endpoint, retryCount);
                                sleepForRetryScrapyAPi();
                                retryCount++;
                                continue;
                            }
                        }
                    }
                    scrapyCrawlerResponse.setCrawlerResponse(crawlerResponse);
                    scrapyCrawlerResponse.setStatus(responseCode);
                }
                // when API status code is not 200
                else {
                    scrapyCrawlerResponse.setStatus(statusCode);
                    scrapyCrawlerResponse.setUrl(url);
                    // the web page cannot be crawled when httpEntityString contains HTML source
                    if (StringUtils.startsWithIgnoreCase(responseEntityString, IConstants.HTML_SOURCE_PREFIX)) {
                        crawlerResponse = new CrawlerResponse();
                        crawlerResponse.setResponse_code(String.valueOf(statusCode));
                    } else {
                        crawlerResponse = gson.fromJson(responseEntityString, CrawlerResponse.class);
                    }
                    scrapyCrawlerResponse.setCrawlerResponse(crawlerResponse);
                    scrapyCrawlerResponse.setStatus(statusCode);
                }
                break;
            } catch (GatewayException e) {
                if (retryCount == IConstants.MAX_SCRAPY_API_RETRY_COUNT) {
                    if (endpoint.equals(scrapyCrawlerEndpointArray[0])) {
                        // switch endpoint to old endpoint if it fails 3 times to try one more time
                        log.warn("gateway exception, switch endpoint to old endpoint after 3 failures to try one more time, domainId = {}, encodedUrlString = {}", domainId, url);
                        endpoint = scrapyCrawlerEndpointArray[1];
                        continue;
                    }
                    scrapyCrawlerResponse.setUrl(url);
                    scrapyCrawlerResponse.setExceptionMessage(e.getMessage());
                    return scrapyCrawlerResponse;
                } else {
                    sleepForRetryScrapyAPi();
                    retryCount++;
                }
            } catch (SocketTimeoutException e) {
                log.error("domainId={}, endpoint={} catch a SocketTimeoutException, retryCount={}, encodedUrlString= {}, e.getMessage()={}", domainId, endpoint, retryCount, url, e.getMessage());
                if (retryCount == IConstants.MAX_SCRAPY_API_RETRY_COUNT && endpoint.equals(scrapyCrawlerEndpointArray[0])) {
                    log.error("switch endpoint to old endpoint because of SocketTimeoutException, domainId = {}, encodedUrlString = {}", domainId, url);
                    endpoint = scrapyCrawlerEndpointArray[1];
                    continue;
                }
                sleepForRetryScrapyAPi();
                retryCount++;
            } catch (Exception e) {
                if (domainId == 7726) {
                    return scrapyCrawlerResponse;
                }
                if (retryCount == IConstants.MAX_SCRAPY_API_RETRY_COUNT) {
                    if (!StringUtils.containsIgnoreCase(e.getMessage(), "IllegalStateException")) {
                        log.error("retry times reached MAX_SCRAPY_API_RETRY_COUNT, catch exception not caused by IllegalStateException, domainId={}, url= {}, endpoint={}, e.getMessage()={}", domainId, endpoint, url, e.getMessage());
                    }
                    if (endpoint.equals(scrapyCrawlerEndpointArray[0])) {
                        // switch back to use old endpoint if it fails 3 times to try one more time
                        log.warn("switch endpoint to old endpoint to try one more time because catch exception and reached MAX_SCRAPY_API_RETRY_COUNT failures, domainId={}, url= {}", domainId, url);
                        endpoint = scrapyCrawlerEndpointArray[1];
                        continue;
                    }
                    scrapyCrawlerResponse.setUrl(url);
                    scrapyCrawlerResponse.setExceptionMessage(e.getMessage());
                    return scrapyCrawlerResponse;
                }
                log.error("retry another time after a sleep when catch exception, domainId={}, endpoint={}, encodedUrlString= {}, retryCount={}, e.getMessage()={}", domainId, endpoint, url, retryCount, e.getMessage());
                sleepForRetryScrapyAPi();
                retryCount++;
            } finally {
                EntityUtils.consumeQuietly(responseEntity);
            }
        }
        return scrapyCrawlerResponse;
    }

    private static void createDirectoryIfNotExists(String requestLogFileName) {
        try {
            final File parentFile = new File(requestLogFileName).getParentFile();
            if (!parentFile.exists() && !parentFile.mkdirs()) {
                log.error("Failed to create directory: {}", parentFile.getAbsolutePath());
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * Writes the response to a file.
     *
     * @param  fileName                 the name of the file to write the response to
     * @param  requestParametersJson    the JSON string representing the request parameters
     * @param  responseCode             the response code
     * @param  retryCount               the number of times the request has been retried
     * @param  httpEntityString         the string representation of the HTTP entity
     */
    private static void writeResponseToFileAsync(String fileName, String requestParametersJson, int responseCode, int retryCount, String httpEntityString) {
        CompletableFuture.runAsync(() -> {
            try (FileWriter responseWriter = new FileWriter(fileName, true)) {
                final String content = LINE_SEPARATOR + "\nrequestParametersJson:\n" + requestParametersJson +
                        "\n" + REQUEST_SPLIT_LINE + " responseCode= " + responseCode + " , retryCount=" + retryCount +
                        " , httpEntityString:\n" + httpEntityString;
                responseWriter.write(content);
            } catch (Exception e) {
                log.error("writeResponseToFileAsync() error writing requestParameterJson= {}\n responseCode={}\nhttpEntityString= {},error={}", requestParametersJson, responseCode, httpEntityString, e);
            }
        });
    }

    private static void sleepForRetryScrapyAPi() {
        try {
            Thread.sleep(IConstants.RETRY_SCRAPY_API_WAIT_MILLISECONDS);
        } catch (InterruptedException e) {
            log.error(e.getMessage());
        }
    }

    public static String getEndpoint(String region) {
        String endpoint;
        if (region.equalsIgnoreCase(IConstants.LONDON)) {
            endpoint = scrapyCrawlerLondonEndpointArray[0];
        } else {
            endpoint = scrapyCrawlerEndpointArray[0];
        }
        return endpoint;
    }
}
