package com.actonia.utils;

import org.apache.commons.lang.StringUtils;
import org.jasypt.encryption.StringEncryptor;
import org.jasypt.encryption.pbe.StandardPBEStringEncryptor;
import org.jasypt.encryption.pbe.config.EnvironmentStringPBEConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ScStringEncryptor {

	private static final Logger log = LoggerFactory.getLogger(ScStringEncryptor.class);

    private static final String ENCRYPTION_ALGORITHM = "PBEWithMD5AndDES";
	private static final String ENV_PWD_ENCRYPTION = "PWD_ENCRYPTION";

	private static final StringEncryptor stringEncryptor;

	static {
		log.info("Starting stringEncryptor configuration.");
		String encKey = System.getenv(ENV_PWD_ENCRYPTION);
		if (StringUtils.isEmpty(encKey)) {
			throw new RuntimeException("Can not get `PWD_ENCRYPTION` from Environment");
		}
        log.info("Get environment variable, ENV_PWD_ENCRYPTION --> {}", encKey);
		EnvironmentStringPBEConfig config = new EnvironmentStringPBEConfig();
		config.setAlgorithm(ENCRYPTION_ALGORITHM);
		config.setPassword(encKey);
		StandardPBEStringEncryptor standardPBEStringEncryptor = new StandardPBEStringEncryptor();
		standardPBEStringEncryptor.setConfig(config);
		stringEncryptor = standardPBEStringEncryptor;
		log.info("Finished stringEncryptor initialization.");
	}

	public String encrypt(String message) {
		return stringEncryptor.encrypt(message);
	}

	public static String decrypt(String message) {
		return stringEncryptor.decrypt(message);
	}

}
