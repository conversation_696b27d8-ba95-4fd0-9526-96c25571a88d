package com.actonia.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.actonia.IConstants;
import com.actonia.value.object.PageRankNode;

public class InternalLinksPageRankUtils {

	//private boolean isDebug = false;

	// map key = node ID
	// map value = URL string
	private Map<Integer, String> nodeIdUrlStringMap = new HashMap<Integer, String>();

	private static InternalLinksPageRankUtils internalLinksPageRankUtils;

	// map key = node ID
	// map value = PageRankNode
	private static Map<Integer, PageRankNode> nodeIdPageRankNodeMap = new HashMap<Integer, PageRankNode>();

	// map key = source node ID
	// map value = list of target node ID
	private static Map<Integer, List<Integer>> sourceNodeIdTargetNodeIdListMap = new HashMap<Integer, List<Integer>>();

	private InternalLinksPageRankUtils() {
		super();
	}

	public static InternalLinksPageRankUtils getInstance() {
		if (internalLinksPageRankUtils == null) {
			internalLinksPageRankUtils = new InternalLinksPageRankUtils();
		}
		return internalLinksPageRankUtils;
	}

	public void resetNodeIdPageRankNodeMap() {
		nodeIdPageRankNodeMap = new HashMap<Integer, PageRankNode>();
	}

	public void resetSourceNodeIdTargetNodeIdListMap() {
		sourceNodeIdTargetNodeIdListMap = new HashMap<Integer, List<Integer>>();
	}

	public void createNodeIdUrlString(Integer nodeId, String urlString) {
		nodeIdUrlStringMap.put(nodeId, urlString);
	}

	public String getUrlString(int nodeId) {
		return nodeIdUrlStringMap.get(nodeId);
	}

	public void createPageRankNode(Integer nodeId, PageRankNode pageRankNode) {
		nodeIdPageRankNodeMap.put(nodeId, pageRankNode);
	}

	public void createSourceNodeIdTargetNodeIdList(Integer sourceNodeId, List<Integer> targetNodeIdList) {
		sourceNodeIdTargetNodeIdListMap.put(sourceNodeId, targetNodeIdList);
	}

	public int getTotalNodes() {
		return nodeIdPageRankNodeMap.size();
	}

	public List<Integer> getNodeIdList() {
		return new ArrayList<Integer>(nodeIdPageRankNodeMap.keySet());
	}

	public Map<Integer, Integer> getSourceNodeIdTargetNodeCountMap() {
		Map<Integer, Integer> sourceNodeIdTargetNodeCountMap = new HashMap<Integer, Integer>();
		for (Integer sourceNodeId : sourceNodeIdTargetNodeIdListMap.keySet()) {
			sourceNodeIdTargetNodeCountMap.put(sourceNodeId, sourceNodeIdTargetNodeIdListMap.get(sourceNodeId).size());
		}
		return sourceNodeIdTargetNodeCountMap;
	}

	public void updateNodeCount(int nodeId, int nodeCount) {
		PageRankNode pageRankNode = nodeIdPageRankNodeMap.get(nodeId);
		pageRankNode.setNodeCount(nodeCount);
		nodeIdPageRankNodeMap.put(nodeId, pageRankNode);
	}

	public boolean isNotConvergedExists() {
		boolean output = false;
		nextPageRankNode: for (PageRankNode pageRankNode : nodeIdPageRankNodeMap.values()) {
			if (pageRankNode.isNodeConverged() == false) {
				output = true;
				break nextPageRankNode;
			}
		}
		return output;
	}

	public List<PageRankNode> getPageRankNodeList() {
		return new ArrayList<PageRankNode>(nodeIdPageRankNodeMap.values());
	}

	public Map<Integer, BigDecimal> getTargetNodeIdTransferredNodeWeightMap() {

		// map key = target node ID
		// map value = transferred node weight
		Map<Integer, BigDecimal> targetNodeIdTransferredNodeWeightMap = new HashMap<Integer, BigDecimal>();

		// map key = target node ID
		// map value = node weight divided by node count
		Map<Integer, BigDecimal> targetNodeIdNodeWeightOverNodeCountMap = new HashMap<Integer, BigDecimal>();

		BigDecimal nodeWeightOverNodeCount = null;
		List<Integer> targetNodeIdList = null;
		PageRankNode pageRankNode = null;
		BigDecimal totalNodeWeightOverNodeCount = null;
		BigDecimal transferredNodeWeight = null;

		for (Integer nodeId : nodeIdPageRankNodeMap.keySet()) {
			pageRankNode = nodeIdPageRankNodeMap.get(nodeId);
			if (sourceNodeIdTargetNodeIdListMap.containsKey(nodeId) == true) {
				targetNodeIdList = new ArrayList<Integer>();
				for (Integer targetNodeId : sourceNodeIdTargetNodeIdListMap.get(nodeId)) {
					if (nodeId != targetNodeId) {
						targetNodeIdList.add(targetNodeId);
					}
				}
				if (targetNodeIdList != null && targetNodeIdList.size() > 0) {
					for (Integer targetNodeId : targetNodeIdList) {
						nodeWeightOverNodeCount = pageRankNode.getNodeWeight().divide(new BigDecimal(pageRankNode.getNodeCount()), RoundingMode.HALF_UP);
						if (targetNodeIdNodeWeightOverNodeCountMap.containsKey(targetNodeId) == true) {
							totalNodeWeightOverNodeCount = targetNodeIdNodeWeightOverNodeCountMap.get(targetNodeId).add(nodeWeightOverNodeCount);
						} else {
							totalNodeWeightOverNodeCount = nodeWeightOverNodeCount;
						}
						targetNodeIdNodeWeightOverNodeCountMap.put(targetNodeId, totalNodeWeightOverNodeCount);
					}
				}
			}
		}

		if (targetNodeIdNodeWeightOverNodeCountMap != null && targetNodeIdNodeWeightOverNodeCountMap.size() > 0) {
			for (Integer testTargetNodeId : targetNodeIdNodeWeightOverNodeCountMap.keySet()) {
				totalNodeWeightOverNodeCount = targetNodeIdNodeWeightOverNodeCountMap.get(testTargetNodeId);
				transferredNodeWeight = totalNodeWeightOverNodeCount.multiply(IConstants.DAMPING_FACTOR);
				targetNodeIdTransferredNodeWeightMap.put(testTargetNodeId, transferredNodeWeight);
			}
		}

		return targetNodeIdTransferredNodeWeightMap;
	}

	public void updateNodeWeightNodeConverged(int nodeId, BigDecimal nodeWeight, boolean isNodeConverged) {
		PageRankNode pageRankNode = nodeIdPageRankNodeMap.get(nodeId);
		pageRankNode.setNodeWeight(nodeWeight);
		pageRankNode.setNodeConverged(isNodeConverged);
		nodeIdPageRankNodeMap.put(nodeId, pageRankNode);
	}
}