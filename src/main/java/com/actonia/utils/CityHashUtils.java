package com.actonia.utils;

import java.io.UnsupportedEncodingException;
import java.math.BigInteger;

public class CityHashUtils {

    public static String getUrlHashForString(String str){
        if (str.endsWith("/") || str.endsWith("?") || str.endsWith("#")) {
            str = str.substring(0, str.length() - 1);
        }
        return getCityHash64ForString(str);
    }

    public static BigInteger getUrlHashForBigInteger(String str){
        if (str.endsWith("/") || str.endsWith("?") || str.endsWith("#")) {
            str = str.substring(0, str.length() - 1);
        }
        return getCityHash64ForBigInteger(str);
    }


    public static String getCityHash64ForString(String str){
        byte[] bytes = str.getBytes();
        long trueCode = CityHash.CityHash64(bytes, 0, bytes.length);
        return Long.toUnsignedString(trueCode, 10);
    }

    public static BigInteger getCityHash64ForBigInteger(String str){
        byte[] bytes = str.getBytes();
        long trueCode = CityHash.CityHash64(bytes, 0, bytes.length);
        return new BigInteger(Long.toBinaryString(trueCode), 2);
    }

    /**64位 无符号数转换对齐*/
    private static BigInteger UnsignedDigitAlignment(Long initHash){
        String s = Long.toBinaryString(initHash);
        return new BigInteger(s, 2);
    }

    /**64位 带符号对齐*/
    private static BigInteger digitAlignment(Long initHash){
        BigInteger initHashBit = new BigInteger(String.valueOf(-initHash), 10);
        String BinaryStr = initHashBit.toString(2);
        byte[] BinaryByt = BinaryStr.getBytes();
        byte[] addDigitsByt = new byte[64];
        if (BinaryByt.length < 64) {
            addDigitsByt[0] = 49;
            for (int i = 1; i < 64; i++) {
                if (i <= 64 - BinaryByt.length - 1) {
                    addDigitsByt[i] = 48;
                } else {
                    addDigitsByt[i] = BinaryByt[BinaryByt.length - (64 - i)];
                }
            }
        }
        for (int i = 1; i < addDigitsByt.length; i++) {
            addDigitsByt[i] = (byte) (((int) addDigitsByt[i]) ^ 1);
        }

        String ones_complementCodeStr = new String(addDigitsByt);
        BigInteger ones_complementCodeBinary = new BigInteger(ones_complementCodeStr, 2);
        BigInteger one = new BigInteger("1", 2);
        return ones_complementCodeBinary.add(one);
    }

    public static void main(String[] args) throws UnsupportedEncodingException {
        //test
        String TestKeywordName2 = "https://www.petvacationhomes.com/ewain/testCrawlStatus/20230911/14";
        String cityHash64 = CityHashUtils.getUrlHashForString(TestKeywordName2.toLowerCase());
        System.out.println(cityHash64);

//        BigInteger cityHash642 = CityHashUtil.getUrlHashForBigInteger(str.toLowerCase());
//        System.out.println(cityHash642.toString());

//        System.out.println("ß" + "---" +"ß".toLowerCase());
//        System.out.println("ÿ" + "---" +"ÿ".toUpperCase());
    }
}
