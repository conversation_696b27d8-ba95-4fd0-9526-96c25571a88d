package com.actonia.utils;

import java.io.File;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.BooleanUtils;

import com.actonia.IConstants;
import com.actonia.dao.TargetUrlChangeIndClickHouseDAO;
import com.actonia.dao.ZapierWebhookDAO;
import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
import com.actonia.entity.ZapierWebhookEntity;
import com.actonia.exception.ExceededTotalRowsException;
import com.actonia.value.object.HtmlHeading;
import com.actonia.value.object.ResponseCodeFilter;
import com.actonia.value.object.TargetUrlChangeContentType;
import com.actonia.value.object.TargetUrlChangeRequest;
import com.actonia.value.object.TargetUrlChangeTotal;
import com.actonia.value.object.UrlFilter;
import com.actonia.value.object.ZapierTargetUrlChangeAlert;
import com.actonia.web.service.TargetUrlChangeWebServiceMessage;
import com.google.gson.Gson;

import ch.ethz.ssh2.Connection;
import ch.ethz.ssh2.SCPClient;

public class TargetUrlChangeUtils {

	//private boolean isDebug = false;

	private static TargetUrlChangeUtils targetUrlChangeUtils;
	private ZapierWebhookDAO zapierWebhookDAO;
	private static final String FTP_SERVER_HOSTNAME = "ftp_prod_internal";
	private static final String FTP_SERVER_ACCOUNT = "devuser";
	private static final String FTP_SERVER_PASSWORD = "vdRA6kAPgGON4ms3";
	private static final String FTP_SERVER_FOLDER_PREIFX = "/home/<USER>/public_html/instant_download/";
	private static final String DOWNLOADS_SEOCLARITY_NET_EXTRACT_PREFIX = "https://downloads.seoclarity.net/instant_download/";
	private static final String OUTPUT_FILE_LOCATION = "/home/<USER>/source/polite_crawl_web_service/output/";
	private static final String LOCAL_TMP_FILE_LOCATION = "/tmp/";

	private TargetUrlChangeUtils() {
		super();
		this.zapierWebhookDAO = SpringBeanFactory.getBean("zapierWebhookDAO");
	}

	public static TargetUrlChangeUtils getInstance() {
		if (targetUrlChangeUtils == null) {
			targetUrlChangeUtils = new TargetUrlChangeUtils();
		}
		return targetUrlChangeUtils;
	}

	public String getErrorMessage(String errorCode, String supplementalMessageText) {
		String errorMessage = null;
		String errorTemplate = TargetUrlChangeWebServiceMessage.getStringProperty(errorCode);
		FormatUtils.getInstance().logMemoryUsage("getErrorMessage() errorTemplate=" + errorTemplate);
		if (StringUtils.isNotBlank(supplementalMessageText)) {
			errorMessage = MessageFormat.format(errorTemplate, supplementalMessageText);
		} else {
			errorMessage = errorTemplate;
		}
		FormatUtils.getInstance().logMemoryUsage("getErrorMessage() errorMessage=" + errorMessage);
		return errorMessage;
	}

	public void sendZapierTargetUrlChangeAlert(String ip, int domainId, List<ZapierTargetUrlChangeAlert> zapierTargetUrlChangeAlertList, Integer pageTagId)
			throws Exception {
		FormatUtils.getInstance().logMemoryUsage("sendZapierTargetUrlChangeAlert() begins. ip=" + ip + ",domainId=" + domainId + ",pageTagId=" + pageTagId
				+ ",zapierTargetUrlChangeAlertList.size()=" + zapierTargetUrlChangeAlertList.size());
		Integer zapierWebhookPageTagId = null;
		List<ZapierWebhookEntity> zapierWebhookEntityList = zapierWebhookDAO.getByDomainIdTriggerType(domainId, IConstants.ZAPIER_TRIGGER_TYPE_TARGET_URL_CHANGE_ALERT);
		if (zapierWebhookEntityList != null && zapierWebhookEntityList.size() > 0) {
			// the optional 'sub_type' of the 'zapier_webhook' MySQL record is the page Tag ID
			nextZapierWebhookEntity: for (ZapierWebhookEntity zapierWebhookEntity : zapierWebhookEntityList) {

				// when the user is no longer active in the domain
				if (ContentGuardUtils.getInstance().isUserActiveInDomain(zapierWebhookEntity.getUserId(), zapierWebhookEntity.getDomainId()) == false) {
					FormatUtils.getInstance().logMemoryUsage("sendZapierTargetUrlChangeAlert() skipped, user ID no longer active. ip=" + ip + ",domainId=" + domainId
							+ ",userId=" + zapierWebhookEntity.getUserId());
					continue nextZapierWebhookEntity;
				}

				// when page tag level Zapier alert is required
				if (pageTagId != null) {
					// when page Tag ID is available in 'zapier_webhook' MySQL record
					if (StringUtils.isNotBlank(zapierWebhookEntity.getSubType())) {
						zapierWebhookPageTagId = new Integer(zapierWebhookEntity.getSubType());
						// when the page tag requires Zapier trigger webhook
						if (pageTagId.intValue() == zapierWebhookPageTagId.intValue()) {
							sendZapierAlert(ip, domainId, pageTagId, zapierWebhookEntity.getCallbackUrl(), zapierTargetUrlChangeAlertList);
						}
					}
				}
				// when domain level Zapier alert is required
				else {
					sendZapierAlert(ip, domainId, pageTagId, zapierWebhookEntity.getCallbackUrl(), zapierTargetUrlChangeAlertList);
				}
			}
		} else {
			FormatUtils.getInstance().logMemoryUsage(
					"sendZapierTargetUrlChangeAlert() begins. ip=" + ip + ",domainId=" + domainId + ",pageTagId=" + pageTagId + ",zapierWebhookEntityList is empty.");
		}
		FormatUtils.getInstance().logMemoryUsage("sendZapierTargetUrlChangeAlert() ends. ip=" + ip + ",domainId=" + domainId + ",pageTagId=" + pageTagId
				+ ",zapierTargetUrlChangeAlertList.size()=" + zapierTargetUrlChangeAlertList.size());
	}

	private void sendZapierAlert(String ip, int domainId, Integer pageTagId, String callbackUrl, List<ZapierTargetUrlChangeAlert> zapierTargetUrlChangeAlertList)
			throws Exception {
		FormatUtils.getInstance()
				.logMemoryUsage("sendZapierAlert() begins. ip=" + ip + ",domainId=" + domainId + ",pageTagId=" + pageTagId + ",callbackUrl=" + callbackUrl);
		String requestParameters = null;
		Gson gson = new Gson();
		boolean isSendGetRequest = false;
		String responseString = null;
		for (ZapierTargetUrlChangeAlert zapierTargetUrlChangeAlert : zapierTargetUrlChangeAlertList) {
			requestParameters = gson.toJson(zapierTargetUrlChangeAlert, ZapierTargetUrlChangeAlert.class);
			FormatUtils.getInstance()
					.logMemoryUsage("sendZapierAlert() ip=" + ip + ",domainId=" + domainId + ",pageTagId=" + pageTagId + ",requestParameters=" + requestParameters);
			try {
				responseString = HttpUtils.getInstance().getResponseString(callbackUrl, isSendGetRequest, requestParameters);
				FormatUtils.getInstance()
						.logMemoryUsage("sendZapierAlert() ip=" + ip + ",domainId=" + domainId + ",pageTagId=" + pageTagId + ",responseString=" + responseString);
			} catch (Exception e) {
				// re-throw exception when not 'httpStatusCode=410,httpReasonPhrase=Gone'
				if (StringUtils.containsIgnoreCase(e.getMessage(), "httpStatusCode=410,httpReasonPhrase=Gone") == false) {
					throw e;
				}
			}
		}
		FormatUtils.getInstance()
				.logMemoryUsage("sendZapierAlert() ends. ip=" + ip + ",domainId=" + domainId + ",pageTagId=" + pageTagId + ",callbackUrl=" + callbackUrl);

	}

	public HtmlHeading[] getHtmlHeadings(String[] stringArray) {
		HtmlHeading[] htmlHeadings = null;
		List<HtmlHeading> htmlHeadingList = null;
		HtmlHeading htmlHeading = null;
		if (stringArray != null && stringArray.length > 0) {
			htmlHeadingList = new ArrayList<HtmlHeading>();
			for (int i = 0; i < stringArray.length; i++) {
				htmlHeading = new HtmlHeading();
				htmlHeading.setIndex(i + 1);
				htmlHeading.setValue(stringArray[i]);
				htmlHeadingList.add(htmlHeading);
			}
			htmlHeadings = htmlHeadingList.toArray(new HtmlHeading[0]);
		}
		return htmlHeadings;
	}

	public TargetUrlChangeTotal calculateTotalRows(String command, TargetUrlChangeRequest targetUrlChangeRequest) throws Exception {

		TargetUrlChangeTotal targetUrlChangeTotal = new TargetUrlChangeTotal();

		int domainId = targetUrlChangeRequest.getDomain_id();
		String startCrawlTimestampString = targetUrlChangeRequest.getStart_crawl_timestamp();
		String endCrawlTimestampString = targetUrlChangeRequest.getEnd_crawl_timestamp();
		int sortBy = targetUrlChangeRequest.getSort_by();
		int rowsPerPage = targetUrlChangeRequest.getRows_per_page();
		int pageNumber = targetUrlChangeRequest.getPage_number();
		Integer[] pageTagIdArray = targetUrlChangeRequest.getPage_tag_ids();
		List<TargetUrlChangeIndClickHouseEntity> targetUrlChangeIndClickHouseEntityList = null;
		int lastPageNumber = 0;
		int totalRows = 0;
		boolean isLastPage = false;
		List<String> databaseFields = null;

		String urlPredicate = getUrlPredicate(targetUrlChangeRequest.getDomain_id(), targetUrlChangeRequest.getUrls());

		String contentTypePredicate = getContentTypePredicate(targetUrlChangeRequest.getDomain_id(), targetUrlChangeRequest.getContent_types());

		String responseCodePredicate = getResponseCodePredicate(targetUrlChangeRequest.getDomain_id(), targetUrlChangeRequest.getResponse_codes());

		String[] changeIndicatorArray = targetUrlChangeRequest.getChange_indicators();

		int offset = (pageNumber - 1) * rowsPerPage;

		if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_LIST_CHANGE_INDICATORS)) {
			databaseFields = getChangeDetailsDatabaseFields(changeIndicatorArray);
		}

		// retrieve total
		boolean isTotal = true;
		if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_LIST_CHANGE_INDICATORS)) {
			targetUrlChangeIndClickHouseEntityList = TargetUrlChangeIndClickHouseDAO.getInstance().getIndicatorDetail(domainId, startCrawlTimestampString,
					endCrawlTimestampString, databaseFields, sortBy, rowsPerPage, offset, isTotal, changeIndicatorArray, pageTagIdArray, contentTypePredicate,
					urlPredicate, responseCodePredicate);
		} else if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_GET_URL_SUMMARY)) {
			targetUrlChangeIndClickHouseEntityList = TargetUrlChangeIndClickHouseDAO.getInstance().getUrlSummary(domainId, startCrawlTimestampString,
					endCrawlTimestampString, sortBy, rowsPerPage, offset, isTotal, changeIndicatorArray, pageTagIdArray, contentTypePredicate, urlPredicate,
					responseCodePredicate);
		}
		if (targetUrlChangeIndClickHouseEntityList != null && targetUrlChangeIndClickHouseEntityList.size() > 0) {
			//if (isDebug == true) {
			//	FormatUtils.getInstance().logMemoryUsage("calculateTotalRows() command=" + command + ",isTotal=" + isTotal
			//			+ ",targetUrlChangeIndClickHouseEntityList.size()=" + targetUrlChangeIndClickHouseEntityList.size());
			//}
			for (TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity : targetUrlChangeIndClickHouseEntityList) {
				totalRows = targetUrlChangeIndClickHouseEntity.getTotal();
				targetUrlChangeTotal.setTotalRows(totalRows);
				//if (isDebug == true) {
				//	FormatUtils.getInstance().logMemoryUsage("calculateTotalRows() command=" + command + ",isTotal=" + isTotal + ",totalRows=" + totalRows);
				//}
			}
		} else {
			//if (isDebug == true) {
			//	FormatUtils.getInstance()
			//			.logMemoryUsage("calculateTotalRows() command=" + command + ",isTotal=" + isTotal + ",targetUrlChangeIndClickHouseEntityList is null.");
			//}
		}

		if (targetUrlChangeTotal.getTotalRows() > 0) {
			lastPageNumber = getLastPageNumber(totalRows, rowsPerPage);
			if (pageNumber > lastPageNumber) {
				throw new ExceededTotalRowsException(totalRows);
			} else if (pageNumber == lastPageNumber) {
				isLastPage = true;
			} else {
				isLastPage = false;
			}
			//if (isDebug == true) {
			//	FormatUtils.getInstance().logMemoryUsage("calculateTotalRows() command=" + command + ",isLastPage=" + isLastPage);
			//}
			targetUrlChangeTotal.setLastPage(isLastPage);
		}
		return targetUrlChangeTotal;
	}

	private int getLastPageNumber(int totalRows, int rowsPerPage) {
		//if (isDebug == true) {
		//	FormatUtils.getInstance().logMemoryUsage("getLastPageNumber() begins. totalRows=" + totalRows + ",rowsPerPage=" + rowsPerPage);
		//}
		int lastPageNumber = 0;
		int initialPageNumber = totalRows / rowsPerPage;
		int modulus = totalRows % rowsPerPage;
		if (modulus > 0) {
			lastPageNumber = initialPageNumber + 1;
		} else {
			lastPageNumber = initialPageNumber;
		}
		//if (isDebug == true) {
		//	FormatUtils.getInstance().logMemoryUsage("getLastPageNumber() ends. lastPageNumber=" + lastPageNumber);
		//}
		return lastPageNumber;
	}

	public static String getUrlPredicate(int domainId, UrlFilter[] urlFilterArray) {
		String urlPredicate = null;
		StringBuilder stringBuilder = null;
		if (urlFilterArray != null && urlFilterArray.length > 0) {
			stringBuilder = new StringBuilder();
			for (UrlFilter urlFilter : urlFilterArray) {
				if (StringUtils.equalsIgnoreCase(urlFilter.getAction(), IConstants.CONTENT_TYPE_ACTION_CONTAINS)) {
					stringBuilder.append(" and url like '%");
					stringBuilder.append(StringEscapeUtils.escapeSql(urlFilter.getValue()));
					stringBuilder.append("%'");
				} else if (StringUtils.equalsIgnoreCase(urlFilter.getAction(), IConstants.CONTENT_TYPE_ACTION_DOES_NOT_CONTAIN)) {
					stringBuilder.append(" and url not like '%");
					stringBuilder.append(StringEscapeUtils.escapeSql(urlFilter.getValue()));
					stringBuilder.append("%'");
				} else if (StringUtils.equalsIgnoreCase(urlFilter.getAction(), IConstants.CONTENT_TYPE_ACTION_ENDS_WITH)) {
					stringBuilder.append(" and url like '%");
					stringBuilder.append(StringEscapeUtils.escapeSql(urlFilter.getValue()));
					stringBuilder.append("'");
				} else if (StringUtils.equalsIgnoreCase(urlFilter.getAction(), IConstants.CONTENT_TYPE_ACTION_IS)) {
					stringBuilder.append(" and url = '");
					stringBuilder.append(StringEscapeUtils.escapeSql(urlFilter.getValue()));
					stringBuilder.append("'");
				} else if (StringUtils.equalsIgnoreCase(urlFilter.getAction(), IConstants.CONTENT_TYPE_ACTION_IS_NOT)) {
					stringBuilder.append(" and url != '");
					stringBuilder.append(StringEscapeUtils.escapeSql(urlFilter.getValue()));
					stringBuilder.append("'");
				} else if (StringUtils.equalsIgnoreCase(urlFilter.getAction(), IConstants.CONTENT_TYPE_ACTION_REGEXP)) {
					stringBuilder.append(" and match(url, '");
					stringBuilder.append(StringEscapeUtils.escapeSql(urlFilter.getValue()));
					stringBuilder.append("') = 1");
				} else if (StringUtils.equalsIgnoreCase(urlFilter.getAction(), IConstants.CONTENT_TYPE_ACTION_REGEXP_NOT_MATCH)) {
					stringBuilder.append(" and match(url, '");
					stringBuilder.append(StringEscapeUtils.escapeSql(urlFilter.getValue()));
					stringBuilder.append("') = 0");
				} else if (StringUtils.equalsIgnoreCase(urlFilter.getAction(), IConstants.CONTENT_TYPE_ACTION_STARTS_WITH)) {
					stringBuilder.append(" and url like '");
					stringBuilder.append(StringEscapeUtils.escapeSql(urlFilter.getValue()));
					stringBuilder.append("%'");
				}
			}
			if (stringBuilder.length() > 0) {
				urlPredicate = stringBuilder.toString();
			}
		}
		//if (isDebug == true) {
		//	FormatUtils.getInstance().logMemoryUsage("getUrlPredicate() domainId=" + domainId + ",urlPredicate=" + urlPredicate);
		//}
		return urlPredicate;
	}

	public static String getContentTypePredicate(int domainId, TargetUrlChangeContentType[] textTypeFilterArray) {
		String sqlPredicate = null;
		String testSqlPredicate = null;
		boolean isFirstParam = false;
		StringBuilder finalSqlPredicateStringBuilder = null;
		List<String> predicateList = null;
		StringBuilder stringBuilder = null;

		if (textTypeFilterArray != null && textTypeFilterArray.length > 0) {
			//if (isDebug == true) {
			//	FormatUtils.getInstance()
			//			.logMemoryUsage("getContentTypePredicate() begins. domainId=" + domainId + ",textTypeFilterArray.length=" + textTypeFilterArray.length);
			//}
			isFirstParam = true;
			if (textTypeFilterArray.length > 1) {
				finalSqlPredicateStringBuilder = new StringBuilder();
				finalSqlPredicateStringBuilder.append(" ( ");
			}
			for (TargetUrlChangeContentType testTargetUrlChangeContentType : textTypeFilterArray) {
				//if (isDebug == true) {
				//	FormatUtils.getInstance().logMemoryUsage("getContentTypePredicate() domainId=" + domainId + ",json="
				//			+ gson.toJson(testTargetUrlChangeContentType, TargetUrlChangeContentType.class));
				//}
				generatePredicate(testTargetUrlChangeContentType);
				rollupLevelTwoPredicate(testTargetUrlChangeContentType, new TargetUrlChangeContentType(), new ArrayList<String>());
				rollupLevelOnePredicate(testTargetUrlChangeContentType, new TargetUrlChangeContentType(), new ArrayList<String>());
				predicateList = new ArrayList<String>();
				combineLevelZeroPredicate(testTargetUrlChangeContentType, new TargetUrlChangeContentType(), predicateList);
				stringBuilder = new StringBuilder();
				for (String predicate : predicateList) {
					stringBuilder.append(predicate);
				}
				testSqlPredicate = stringBuilder.toString();
				//if (isDebug == true) {
				//	FormatUtils.getInstance().logMemoryUsage("getContentTypePredicate() domainId=" + domainId + ",testSqlPredicate=" + testSqlPredicate);
				//}
				if (textTypeFilterArray.length > 1) {
					if (isFirstParam == true) {
						isFirstParam = false;
					} else {
						finalSqlPredicateStringBuilder.append(" or ");
					}
					finalSqlPredicateStringBuilder.append(testSqlPredicate);
				}
			}
			if (textTypeFilterArray.length > 1) {
				finalSqlPredicateStringBuilder.append(" ) ");
				sqlPredicate = finalSqlPredicateStringBuilder.toString();
			} else {
				sqlPredicate = testSqlPredicate;
			}
			//if (isDebug == true) {
			//	FormatUtils.getInstance().logMemoryUsage("getContentTypePredicate() ends. domainId=" + domainId + ",textTypeFilterArray.length="
			//			+ textTypeFilterArray.length + ",sqlPredicate=" + sqlPredicate);
			//}
		} else {
			//if (isDebug == true) {
			//	FormatUtils.getInstance().logMemoryUsage("getContentTypePredicate() skipped. domainId=" + domainId + ",textTypeFilterArray is empty.");
			//}
		}
		return sqlPredicate;
	}

	private static void generatePredicate(TargetUrlChangeContentType targetUrlChangeContentType) {
		StringBuilder stringBuilder = null;
		if (BooleanUtils.toBoolean(targetUrlChangeContentType.getLeaf()) == true) {
			stringBuilder = new StringBuilder();
			stringBuilder.append("(");
			if (StringUtils.equalsIgnoreCase(targetUrlChangeContentType.getAction(), IConstants.CONTENT_TYPE_ACTION_CONTAINS)) {
				stringBuilder.append("url like '%");
				stringBuilder.append(StringEscapeUtils.escapeSql(targetUrlChangeContentType.getValue()));
				stringBuilder.append("%'");
			} else if (StringUtils.equalsIgnoreCase(targetUrlChangeContentType.getAction(), IConstants.CONTENT_TYPE_ACTION_DOES_NOT_CONTAIN)) {
				stringBuilder.append("url not like '%");
				stringBuilder.append(StringEscapeUtils.escapeSql(targetUrlChangeContentType.getValue()));
				stringBuilder.append("%'");
			} else if (StringUtils.equalsIgnoreCase(targetUrlChangeContentType.getAction(), IConstants.CONTENT_TYPE_ACTION_ENDS_WITH)) {
				stringBuilder.append("url like '%");
				stringBuilder.append(StringEscapeUtils.escapeSql(targetUrlChangeContentType.getValue()));
				stringBuilder.append("'");
			} else if (StringUtils.equalsIgnoreCase(targetUrlChangeContentType.getAction(), IConstants.CONTENT_TYPE_ACTION_IS)) {
				stringBuilder.append("url = '");
				stringBuilder.append(StringEscapeUtils.escapeSql(targetUrlChangeContentType.getValue()));
				stringBuilder.append("'");
			} else if (StringUtils.equalsIgnoreCase(targetUrlChangeContentType.getAction(), IConstants.CONTENT_TYPE_ACTION_IS_NOT)) {
				stringBuilder.append("url != '");
				stringBuilder.append(StringEscapeUtils.escapeSql(targetUrlChangeContentType.getValue()));
				stringBuilder.append("'");
			} else if (StringUtils.equalsIgnoreCase(targetUrlChangeContentType.getAction(), IConstants.CONTENT_TYPE_ACTION_REGEXP)) {
				stringBuilder.append("match(url, '");
				stringBuilder.append(StringEscapeUtils.escapeSql(targetUrlChangeContentType.getValue()));
				stringBuilder.append("') = 1");
			} else if (StringUtils.equalsIgnoreCase(targetUrlChangeContentType.getAction(), IConstants.CONTENT_TYPE_ACTION_REGEXP_NOT_MATCH)) {
				stringBuilder.append("match(url, '");
				stringBuilder.append(StringEscapeUtils.escapeSql(targetUrlChangeContentType.getValue()));
				stringBuilder.append("') = 0");
			} else if (StringUtils.equalsIgnoreCase(targetUrlChangeContentType.getAction(), IConstants.CONTENT_TYPE_ACTION_STARTS_WITH)) {
				stringBuilder.append("url like '");
				stringBuilder.append(StringEscapeUtils.escapeSql(targetUrlChangeContentType.getValue()));
				stringBuilder.append("%'");
			}
			stringBuilder.append(")");
			targetUrlChangeContentType.setPredicate(stringBuilder.toString());
		}
		TargetUrlChangeContentType[] items = targetUrlChangeContentType.getItems();
		if (items != null && items.length > 0) {
			for (TargetUrlChangeContentType item : items) {
				generatePredicate(item);
			}
		}
	}

	private static void rollupLevelTwoPredicate(TargetUrlChangeContentType targetUrlChangeContentType, TargetUrlChangeContentType previousLeveltargetUrlChangeContentType,
	                                            List<String> predicateList) {
		TargetUrlChangeContentType[] items = null;
		StringBuilder stringBuilder = null;
		if (targetUrlChangeContentType.getLevel() == 2 && StringUtils.isNotBlank(targetUrlChangeContentType.getPredicate())) {
			if (predicateList.size() > 0) {
				predicateList.add(
						IConstants.ONE_SPACE + previousLeveltargetUrlChangeContentType.getCond() + IConstants.ONE_SPACE + targetUrlChangeContentType.getPredicate());
			} else {
				predicateList.add(targetUrlChangeContentType.getPredicate());
			}
		} else {
			items = targetUrlChangeContentType.getItems();
			if (items != null && items.length > 0) {
				predicateList = new ArrayList<String>();
				for (TargetUrlChangeContentType item : items) {
					rollupLevelTwoPredicate(item, targetUrlChangeContentType, predicateList);
				}
				//System.out.println("=============================================");
				//System.out.println("rollupLevelTwoPredicate() leaf=" + targetUrlChangeContentType.getLeaf());
				//System.out.println("rollupLevelTwoPredicate() non-leaf cond=" + previousLeveltargetUrlChangeContentType.getCond());
				//System.out.println("rollupLevelTwoPredicate() action=" + targetUrlChangeContentType.getAction());
				//System.out.println("rollupLevelTwoPredicate() value=" + targetUrlChangeContentType.getValue());
				//System.out.println("rollupLevelTwoPredicate() level=" + targetUrlChangeContentType.getLevel());
				//System.out.println("rollupLevelTwoPredicate() cond=" + targetUrlChangeContentType.getCond());
				stringBuilder = new StringBuilder();
				stringBuilder.append(" ( ");
				for (String predicate : predicateList) {
					stringBuilder.append(predicate);
				}
				stringBuilder.append(" ) ");
				targetUrlChangeContentType.setPredicate(stringBuilder.toString());
				//System.out.println("rollupLevelTwoPredicate() predicate=" + targetUrlChangeContentType.getPredicate());
			}
		}
	}

	private static void rollupLevelOnePredicate(TargetUrlChangeContentType targetUrlChangeContentType, TargetUrlChangeContentType previousLeveltargetUrlChangeContentType,
	                                            List<String> predicateList) {
		TargetUrlChangeContentType[] items = null;
		StringBuilder stringBuilder = null;
		if (targetUrlChangeContentType.getLevel() == 1 && StringUtils.isNotBlank(targetUrlChangeContentType.getPredicate())) {
			if (predicateList.size() > 0) {
				predicateList.add(
						IConstants.ONE_SPACE + previousLeveltargetUrlChangeContentType.getCond() + IConstants.ONE_SPACE + targetUrlChangeContentType.getPredicate());
			} else {
				predicateList.add(targetUrlChangeContentType.getPredicate());
			}
		} else {
			items = targetUrlChangeContentType.getItems();
			if (items != null && items.length > 0) {
				predicateList = new ArrayList<String>();
				for (TargetUrlChangeContentType item : items) {
					rollupLevelOnePredicate(item, targetUrlChangeContentType, predicateList);
				}
				//System.out.println("=============================================");
				//System.out.println("rollupLevelOnePredicate() leaf=" + targetUrlChangeContentType.getLeaf());
				//System.out.println("rollupLevelOnePredicate() non-leaf cond=" + previousLeveltargetUrlChangeContentType.getCond());
				//System.out.println("rollupLevelOnePredicate() action=" + targetUrlChangeContentType.getAction());
				//System.out.println("rollupLevelOnePredicate() value=" + targetUrlChangeContentType.getValue());
				//System.out.println("rollupLevelOnePredicate() level=" + targetUrlChangeContentType.getLevel());
				//System.out.println("rollupLevelOnePredicate() cond=" + targetUrlChangeContentType.getCond());
				stringBuilder = new StringBuilder();
				stringBuilder.append(" ( ");
				for (String predicate : predicateList) {
					stringBuilder.append(predicate);
				}
				stringBuilder.append(" ) ");
				targetUrlChangeContentType.setPredicate(stringBuilder.toString());
				//System.out.println("rollupLevelOnePredicate() predicate=" + targetUrlChangeContentType.getPredicate());
			}
		}
	}

	private static void combineLevelZeroPredicate(TargetUrlChangeContentType targetUrlChangeContentType, TargetUrlChangeContentType previousLeveltargetUrlChangeContentType,
	                                              List<String> predicateList) {
		if (targetUrlChangeContentType.getLevel() == 0 && StringUtils.isNotBlank(targetUrlChangeContentType.getPredicate())) {
			if (predicateList.size() > 0) {
				predicateList.add(
						IConstants.ONE_SPACE + previousLeveltargetUrlChangeContentType.getCond() + IConstants.ONE_SPACE + targetUrlChangeContentType.getPredicate());
			} else {
				predicateList.add(targetUrlChangeContentType.getPredicate());
			}
		}
		TargetUrlChangeContentType[] items = targetUrlChangeContentType.getItems();
		if (items != null && items.length > 0) {
			for (TargetUrlChangeContentType item : items) {
				combineLevelZeroPredicate(item, targetUrlChangeContentType, predicateList);
			}
		}
	}

	public static String getResponseCodePredicate(int domainId, ResponseCodeFilter[] responseCodeFilterArray) {
		String responseCodePredicate = null;
		int totalResponseCodePairs = 0;
		StringBuilder stringBuilder = null;
		if (responseCodeFilterArray != null && responseCodeFilterArray.length > 0) {
			stringBuilder = new StringBuilder();
			stringBuilder.append(" (change_indicator = 'response_code_chg_ind' and (");
			for (ResponseCodeFilter responseCodeFilter : responseCodeFilterArray) {
				totalResponseCodePairs++;
				if (totalResponseCodePairs > 1) {
					stringBuilder.append(" or");
				}
				stringBuilder.append(" (response_code_previous = '");
				stringBuilder.append(responseCodeFilter.getResponse_code_previous());
				stringBuilder.append("'");
				stringBuilder.append(" and response_code_current = '");
				stringBuilder.append(responseCodeFilter.getResponse_code_current());
				stringBuilder.append("'");
				stringBuilder.append(")");
			}
			stringBuilder.append(" ))");
			responseCodePredicate = stringBuilder.toString();
		}
		//if (isDebug == true) {
		//	FormatUtils.getInstance().logMemoryUsage("getResponseCodePredicate() domainId=" + domainId + ",responseCodePredicate=" + responseCodePredicate);
		//}
		return responseCodePredicate;
	}

	public List<String> getChangeDetailsDatabaseFields(String[] changeIndicatorArray) throws Exception {
		List<String> databaseFields = null;
		Set<String> databaseFieldSet = null;
		if (changeIndicatorArray != null && changeIndicatorArray.length > 0) {
			databaseFieldSet = new HashSet<String>();
			databaseFieldSet.add(IConstants.URL);
			databaseFieldSet.add(IConstants.CURRENT_CRAWL_TIMESTAMP);
			databaseFieldSet.add(IConstants.PREVIOUS_CRAWL_TIMESTAMP);
			databaseFieldSet.add(IConstants.CHANGE_INDICATOR);
			databaseFieldSet.add(IConstants.CHANGE_TYPE);
			databaseFieldSet.add(IConstants.CRITICAL_IND);
			databaseFieldSet.add(IConstants.URL_HASH);
			databaseFieldSet.add(IConstants.URL_MURMUR_HASH);
			for (String changeIndicator : changeIndicatorArray) {
				if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.ALTERNATE_LINKS_CHG_IND)) {
					databaseFieldSet.add(IConstants.ALTERNATE_LINKS + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.ALTERNATE_LINKS + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.AMPHTML_HREF_CHG_IND)) {
					databaseFieldSet.add(IConstants.AMPHTML_HREF + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.AMPHTML_HREF + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.ANALYZED_URL_S_CHG_IND)) {
					databaseFieldSet.add(IConstants.ANALYZED_URL_S + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.ANALYZED_URL_S + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.ARCHIVE_FLG_CHG_IND)) {
					databaseFieldSet.add(IConstants.ARCHIVE_FLG + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.ARCHIVE_FLG + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.BASE_TAG_ADDED_IND)) {
					databaseFieldSet.add(IConstants.BASE_TAG + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.BASE_TAG + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.BASE_TAG_CHG_IND)) {
					databaseFieldSet.add(IConstants.BASE_TAG + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.BASE_TAG + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.BASE_TAG_REMOVED_IND)) {
					databaseFieldSet.add(IConstants.BASE_TAG + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.BASE_TAG + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.BASE_TAG_TARGET_CHG_IND)) {
					databaseFieldSet.add(IConstants.BASE_TAG_TARGET + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.BASE_TAG_TARGET + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.BLOCKED_BY_ROBOTS_CHG_IND)) {
					databaseFieldSet.add(IConstants.BLOCKED_BY_ROBOTS + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.BLOCKED_BY_ROBOTS + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.CANONICAL_ADDED_IND)) {
					databaseFieldSet.add(IConstants.CANONICAL + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.CANONICAL + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.CANONICAL_CHG_IND)) {
					databaseFieldSet.add(IConstants.CANONICAL + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.CANONICAL + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.CANONICAL_REMOVED_IND)) {
					databaseFieldSet.add(IConstants.CANONICAL + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.CANONICAL + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.CANONICAL_HEADER_FLAG_CHG_IND)) {
					databaseFieldSet.add(IConstants.CANONICAL_HEADER_FLAG + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.CANONICAL_HEADER_FLAG + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.CANONICAL_HEADER_TYPE_CHG_IND)) {
					databaseFieldSet.add(IConstants.CANONICAL_HEADER_TYPE + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.CANONICAL_HEADER_TYPE + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.CANONICAL_TYPE_CHG_IND)) {
					databaseFieldSet.add(IConstants.CANONICAL_TYPE + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.CANONICAL_TYPE + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.CANONICAL_URL_IS_CONSISTENT_CHG_IND)) {
					databaseFieldSet.add(IConstants.CANONICAL_URL_IS_CONSISTENT + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.CANONICAL_URL_IS_CONSISTENT + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.CONTENT_TYPE_CHG_IND)) {
					databaseFieldSet.add(IConstants.CONTENT_TYPE + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.CONTENT_TYPE + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.CUSTOM_DATA_ADDED_IND)) {
					databaseFieldSet.add(IConstants.CUSTOM_DATA + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.CUSTOM_DATA + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.CUSTOM_DATA_CHG_IND)) {
					databaseFieldSet.add(IConstants.CUSTOM_DATA + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.CUSTOM_DATA + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.CUSTOM_DATA_REMOVED_IND)) {
					databaseFieldSet.add(IConstants.CUSTOM_DATA + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.CUSTOM_DATA + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.DESCRIPTION_ADDED_IND)) {
					databaseFieldSet.add(IConstants.DESCRIPTION + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.DESCRIPTION + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.DESCRIPTION_CHG_IND)) {
					databaseFieldSet.add(IConstants.DESCRIPTION + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.DESCRIPTION + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.DESCRIPTION_REMOVED_IND)) {
					databaseFieldSet.add(IConstants.DESCRIPTION + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.DESCRIPTION + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.DESCRIPTION_LENGTH_CHG_IND)) {
					databaseFieldSet.add(IConstants.DESCRIPTION_LENGTH + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.DESCRIPTION_LENGTH + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.ERROR_MESSAGE_CHG_IND)) {
					databaseFieldSet.add(IConstants.ERROR_MESSAGE + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.ERROR_MESSAGE + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.FINAL_RESPONSE_CODE_CHG_IND)) {
					databaseFieldSet.add(IConstants.FINAL_RESPONSE_CODE + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.FINAL_RESPONSE_CODE + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.FOLLOW_FLG_CHG_IND)) {
					databaseFieldSet.add(IConstants.FOLLOW_FLG + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.FOLLOW_FLG + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.H1_ADDED_IND)) {
					databaseFieldSet.add(IConstants.H1 + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.H1 + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.H1_CHG_IND)) {
					databaseFieldSet.add(IConstants.H1 + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.H1 + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.H1_REMOVED_IND)) {
					databaseFieldSet.add(IConstants.H1 + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.H1 + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.H1_COUNT_CHG_IND)) {
					databaseFieldSet.add(IConstants.H1_COUNT + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.H1_COUNT + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.H1_LENGTH_CHG_IND)) {
					databaseFieldSet.add(IConstants.H1_LENGTH + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.H1_LENGTH + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.H2_ADDED_IND)) {
					databaseFieldSet.add(IConstants.H2 + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.H2 + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.H2_CHG_IND)) {
					databaseFieldSet.add(IConstants.H2 + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.H2 + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.H2_REMOVED_IND)) {
					databaseFieldSet.add(IConstants.H2 + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.H2 + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.HEADER_NOARCHIVE_CHG_IND)) {
					databaseFieldSet.add(IConstants.HEADER_NOARCHIVE + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.HEADER_NOARCHIVE + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.HEADER_NOFOLLOW_CHG_IND)) {
					databaseFieldSet.add(IConstants.HEADER_NOFOLLOW + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.HEADER_NOFOLLOW + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.HEADER_NOINDEX_CHG_IND)) {
					databaseFieldSet.add(IConstants.HEADER_NOINDEX + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.HEADER_NOINDEX + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.HEADER_NOODP_CHG_IND)) {
					databaseFieldSet.add(IConstants.HEADER_NOODP + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.HEADER_NOODP + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.HEADER_NOSNIPPET_CHG_IND)) {
					databaseFieldSet.add(IConstants.HEADER_NOSNIPPET + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.HEADER_NOSNIPPET + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.HEADER_NOYDIR_CHG_IND)) {
					databaseFieldSet.add(IConstants.HEADER_NOYDIR + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.HEADER_NOYDIR + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.HREFLANG_ERRORS_CHG_IND)) {
					databaseFieldSet.add(IConstants.HREFLANG_ERRORS + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.HREFLANG_ERRORS + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.HREFLANG_LINKS_CHG_IND)) {
					databaseFieldSet.add(IConstants.HREFLANG_URL_COUNT + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.HREFLANG_URL_COUNT + IConstants.UNDERSCORE_PREVIOUS);
					databaseFieldSet.add(IConstants.HREFLANG_LINKS + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.HREFLANG_LINKS + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.HREFLANG_LINKS_OUT_COUNT_CHG_IND)) {
					databaseFieldSet.add(IConstants.HREFLANG_LINKS_OUT_COUNT + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.HREFLANG_LINKS_OUT_COUNT + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.HREFLANG_LINKS_ADDED_IND)) {
					databaseFieldSet.add(IConstants.HREFLANG_URL_COUNT + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.HREFLANG_URL_COUNT + IConstants.UNDERSCORE_PREVIOUS);
					databaseFieldSet.add(IConstants.HREFLANG_LINKS + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.HREFLANG_LINKS + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.HREFLANG_URL_COUNT_CHG_IND)) {
					databaseFieldSet.add(IConstants.HREFLANG_URL_COUNT + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.HREFLANG_URL_COUNT + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.HREFLANG_LINKS_REMOVED_IND)) {
					databaseFieldSet.add(IConstants.HREFLANG_URL_COUNT + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.HREFLANG_URL_COUNT + IConstants.UNDERSCORE_PREVIOUS);
					databaseFieldSet.add(IConstants.HREFLANG_LINKS + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.HREFLANG_LINKS + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.INDEX_FLG_CHG_IND)) {
					databaseFieldSet.add(IConstants.INDEX_FLG + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.INDEX_FLG + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.INDEXABLE_CHG_IND)) {
					databaseFieldSet.add(IConstants.INDEXABLE + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.INDEXABLE + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.INSECURE_RESOURCES_CHG_IND)) {
					databaseFieldSet.add(IConstants.INSECURE_RESOURCES + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.INSECURE_RESOURCES + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.META_CHARSET_CHG_IND)) {
					databaseFieldSet.add(IConstants.META_CHARSET + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.META_CHARSET + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.META_CONTENT_TYPE_CHG_IND)) {
					databaseFieldSet.add(IConstants.META_CONTENT_TYPE + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.META_CONTENT_TYPE + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.META_DISABLED_SITELINKS_CHG_IND)) {
					databaseFieldSet.add(IConstants.META_DISABLED_SITELINKS + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.META_DISABLED_SITELINKS + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.META_NOODP_CHG_IND)) {
					databaseFieldSet.add(IConstants.META_NOODP + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.META_NOODP + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.META_NOSNIPPET_CHG_IND)) {
					databaseFieldSet.add(IConstants.META_NOSNIPPET + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.META_NOSNIPPET + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.META_NOYDIR_CHG_IND)) {
					databaseFieldSet.add(IConstants.META_NOYDIR + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.META_NOYDIR + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.META_REDIRECT_CHG_IND)) {
					databaseFieldSet.add(IConstants.META_REDIRECT + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.META_REDIRECT + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.MIXED_REDIRECTS_CHG_IND)) {
					databaseFieldSet.add(IConstants.MIXED_REDIRECTS + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.MIXED_REDIRECTS + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.MOBILE_REL_ALTERNATE_URL_IS_CONSISTENT_CHG_IND)) {
					databaseFieldSet.add(IConstants.MOBILE_REL_ALTERNATE_URL_IS_CONSISTENT + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.MOBILE_REL_ALTERNATE_URL_IS_CONSISTENT + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.NOODP_CHG_IND)) {
					databaseFieldSet.add(IConstants.NOODP + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.NOODP + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.NOSNIPPET_CHG_IND)) {
					databaseFieldSet.add(IConstants.NOSNIPPET + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.NOSNIPPET + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.NOYDIR_CHG_IND)) {
					databaseFieldSet.add(IConstants.NOYDIR + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.NOYDIR + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.OPEN_GRAPH_ADDED_IND)) {
					databaseFieldSet.add(IConstants.OG_MARKUP + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.OG_MARKUP + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.OG_MARKUP_CHG_IND)) {
					databaseFieldSet.add(IConstants.OG_MARKUP + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.OG_MARKUP + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.OPEN_GRAPH_REMOVED_IND)) {
					databaseFieldSet.add(IConstants.OG_MARKUP + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.OG_MARKUP + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.OG_MARKUP_LENGTH_CHG_IND)) {
					databaseFieldSet.add(IConstants.OG_MARKUP_LENGTH + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.OG_MARKUP_LENGTH + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.OUTLINK_COUNT_CHG_IND)) {
					databaseFieldSet.add(IConstants.OUTLINK_COUNT + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.OUTLINK_COUNT + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.PAGE_ANALYSIS_RESULTS_CHG_IND_JSON)) {
					databaseFieldSet.add(IConstants.PAGE_ANALYSIS_RESULTS_CHG_IND_JSON);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.PAGE_LINK_CHG_IND)) {
					databaseFieldSet.add(IConstants.PAGE_LINK + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.PAGE_LINK + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.REDIRECT_BLOCKED_CHG_IND)) {
					databaseFieldSet.add(IConstants.REDIRECT_BLOCKED + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.REDIRECT_BLOCKED + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.REDIRECT_BLOCKED_REASON_CHG_IND)) {
					databaseFieldSet.add(IConstants.REDIRECT_BLOCKED_REASON + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.REDIRECT_BLOCKED_REASON + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.REDIRECT_CHAIN_CHG_IND)) {
					databaseFieldSet.add(IConstants.REDIRECT_CHAIN + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.REDIRECT_CHAIN + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.REDIRECT_FINAL_URL_CHG_IND)) {
					databaseFieldSet.add(IConstants.REDIRECT_FINAL_URL + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.REDIRECT_FINAL_URL + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.REDIRECT_TIMES_CHG_IND)) {
					databaseFieldSet.add(IConstants.REDIRECT_TIMES + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.REDIRECT_TIMES + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.RESPONSE_CODE_CHG_IND)) {
					databaseFieldSet.add(IConstants.RESPONSE_CODE + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.RESPONSE_CODE + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.REDIRECT_301_DETECTED_IND)) {
					databaseFieldSet.add(IConstants.RESPONSE_CODE + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.RESPONSE_CODE + IConstants.UNDERSCORE_PREVIOUS);
					databaseFieldSet.add(IConstants.REDIRECT_FINAL_URL + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.REDIRECT_FINAL_URL + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.REDIRECT_301_REMOVED_IND)) {
					databaseFieldSet.add(IConstants.RESPONSE_CODE + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.RESPONSE_CODE + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.REDIRECT_302_DETECTED_IND)) {
					databaseFieldSet.add(IConstants.RESPONSE_CODE + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.RESPONSE_CODE + IConstants.UNDERSCORE_PREVIOUS);
					databaseFieldSet.add(IConstants.REDIRECT_FINAL_URL + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.REDIRECT_FINAL_URL + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.REDIRECT_302_REMOVED_IND)) {
					databaseFieldSet.add(IConstants.RESPONSE_CODE + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.RESPONSE_CODE + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.REDIRECT_DIFF_CODE_IND)) {
					databaseFieldSet.add(IConstants.RESPONSE_CODE + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.RESPONSE_CODE + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.RESPONSE_HEADERS_ADDED_IND)) {
					databaseFieldSet.add(IConstants.RESPONSE_HEADERS + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.RESPONSE_HEADERS + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.RESPONSE_HEADERS_REMOVED_IND)) {
					databaseFieldSet.add(IConstants.RESPONSE_HEADERS + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.RESPONSE_HEADERS + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.ROBOTS_ADDED_IND)) {
					databaseFieldSet.add(IConstants.ROBOTS_CONTENTS + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.ROBOTS_CONTENTS + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.ROBOTS_CONTENTS_CHG_IND)) {
					databaseFieldSet.add(IConstants.ROBOTS_CONTENTS + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.ROBOTS_CONTENTS + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.ROBOTS_REMOVED_IND)) {
					databaseFieldSet.add(IConstants.ROBOTS_CONTENTS + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.ROBOTS_CONTENTS + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.STRUCTURED_DATA_CHG_IND)) {
					databaseFieldSet.add(IConstants.STRUCTURED_DATA + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.STRUCTURED_DATA + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.TITLE_ADDED_IND)) {
					databaseFieldSet.add(IConstants.TITLE + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.TITLE + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.TITLE_CHG_IND)) {
					databaseFieldSet.add(IConstants.TITLE + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.TITLE + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.TITLE_REMOVED_IND)) {
					databaseFieldSet.add(IConstants.TITLE + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.TITLE + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.TITLE_LENGTH_CHG_IND)) {
					databaseFieldSet.add(IConstants.TITLE_LENGTH + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.TITLE_LENGTH + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.VIEWPORT_ADDED_IND)) {
					databaseFieldSet.add(IConstants.VIEWPORT_CONTENT + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.VIEWPORT_CONTENT + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.VIEWPORT_CONTENT_CHG_IND)) {
					databaseFieldSet.add(IConstants.VIEWPORT_CONTENT + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.VIEWPORT_CONTENT + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.VIEWPORT_REMOVED_IND)) {
					databaseFieldSet.add(IConstants.VIEWPORT_CONTENT + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.VIEWPORT_CONTENT + IConstants.UNDERSCORE_PREVIOUS);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.ROBOTS_TXT_CHG_IND)) {
					databaseFieldSet.add(IConstants.ROBOT_TXT + IConstants.UNDERSCORE_CURRENT);
					databaseFieldSet.add(IConstants.ROBOT_TXT + IConstants.UNDERSCORE_PREVIOUS);
				}
			}
			databaseFields = new ArrayList<String>(databaseFieldSet);
		} else {
			databaseFields = CrawlerUtils.getInstance().getTargetUrlChangeIndTableAllFieldNames();
		}
		databaseFields.add(IConstants.RESPONSE_CODE + IConstants.UNDERSCORE_CURRENT);
		databaseFields.add(IConstants.RESPONSE_CODE + IConstants.UNDERSCORE_PREVIOUS);
		return databaseFields.stream().distinct().collect(Collectors.toList());
	}

	public String uploadDownloadAllFileToFTP(String outputFileName, List<String> outputList) throws Exception {

		String yearMonthSlash = DateFormatUtils.format(new Date(), IConstants.DATE_FORMAT_YYYYMM) + IConstants.SLASH;

		String downloadAllFileFtpLocation = null;

		String outputTimestamp = DateFormatUtils.format(new Date(), IConstants.DATE_FORMAT_YYYYMMDD_HHMMSS_SSS);
		String fullOutputFileName = outputFileName + IConstants.UNDERSCORE + outputTimestamp + IConstants.FILE_EXTENSION_CSV;

		String filename = OUTPUT_FILE_LOCATION + fullOutputFileName;
		File outputFile = new File(filename);
		FileUtils.writeLines(outputFile, IConstants.UTF_8, outputList);

		// upload output file to FTP server
		boolean isOutputFileUploaded = uploadToFtpServer(filename, yearMonthSlash);

		// downloadAllFileFtpLocation = https://downloads.seoclarity.net/extract/crawl_request_log_id_123456_20171101_120011608.txt
		// physical folder is: /home/<USER>/public_html/extract

		// when output file could not be uploaded to FTP server, do not send alert and do not update request to completed....
		if (isOutputFileUploaded == false) {
			FormatUtils.getInstance().logMemoryUsage("uploadDownloadAllFileToFTP() error--isOutputFileUploaded=" + isOutputFileUploaded);
			return null;
		} else {
			downloadAllFileFtpLocation = DOWNLOADS_SEOCLARITY_NET_EXTRACT_PREFIX + yearMonthSlash + fullOutputFileName;
			FormatUtils.getInstance().logMemoryUsage("uploadDownloadAllFileToFTP() downloadAllFileFtpLocation=" + downloadAllFileFtpLocation);
			return downloadAllFileFtpLocation;
		}
	}

	private boolean uploadToFtpServer(String inputFileLocation, String yearMonthSlash) {
		FormatUtils.getInstance().logMemoryUsage("uploadToFtpServer() inputFileLocation=" + inputFileLocation);
		boolean isOutputFileUploaded = false;
		Connection connection = null;
		SCPClient scpClient = null;
		try {
			connection = new Connection(FTP_SERVER_HOSTNAME);
			connection.connect();
			if (connection.authenticateWithPassword(FTP_SERVER_ACCOUNT, FTP_SERVER_PASSWORD)) {
				FormatUtils.getInstance().logMemoryUsage("uploadToFtpServer() connected to FTP server=" + FTP_SERVER_HOSTNAME);
				scpClient = connection.createSCPClient();
				scpClient.put(inputFileLocation, FTP_SERVER_FOLDER_PREIFX + yearMonthSlash, "0644");
				connection.close();
				isOutputFileUploaded = true;
				FormatUtils.getInstance().logMemoryUsage(
						"uploadToFtpServer() file " + inputFileLocation + " uploaded to " + FTP_SERVER_FOLDER_PREIFX + yearMonthSlash + " on " + FTP_SERVER_HOSTNAME);
			} else {
				FormatUtils.getInstance().logMemoryUsage("uploadToFtpServer() error--could not connect to FTP server=" + FTP_SERVER_HOSTNAME);
			}

		} catch (Exception e) {
			e.printStackTrace();
		}
		return isOutputFileUploaded;
	}

	public String uploadDownloadAllFileToSeagate(String outputFileName, List<String> outputList) throws Exception {

		String outputTimestamp = DateFormatUtils.format(new Date(), IConstants.DATE_FORMAT_YYYYMMDD_HHMMSS_SSS);
		String fileNameConcatTimestamp = outputFileName + IConstants.UNDERSCORE + outputTimestamp + IConstants.FILE_EXTENSION_CSV;

		String filename = LOCAL_TMP_FILE_LOCATION + fileNameConcatTimestamp;
		File outputFile = new File(filename);
		FileUtils.writeLines(outputFile, IConstants.UTF_8, outputList);

		// upload output file to Seagate
		if (SeagateUtils.saveFileToDefaultSeagate(0, filename)) {
			String seagatePresignedUrl = SeagateUtils.getDefaultSeagatePresignedUrl(0, fileNameConcatTimestamp);
			if (!seagatePresignedUrl.isEmpty()) {
				outputFile.deleteOnExit();
			}
			FormatUtils.getInstance().logMemoryUsage("seagatePresignedUrl() seagatePresignedUrl=" + seagatePresignedUrl);
			return seagatePresignedUrl;
		} else {
			FormatUtils.getInstance().logMemoryUsage("seagatePresignedUrl() error--filename=" + filename);
			return null;
		}
	}

}
