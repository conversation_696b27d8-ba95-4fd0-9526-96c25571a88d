package com.actonia.utils;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;

public class CrawlHtmlSeagateUtils extends SeagateUtils {

    public static final String bucketName = "polite-crawl-non200-htmls";

    private static final AWSCredentials credentials = AwsCredentialsEnvKeyConstructor.getInstance()
            .getAwsCredentials(AwsCredentialsEnvKeyConstructor.ENV_SEAGATE_TEMP_FILE_ACCESS_KEY_S3,
            AwsCredentialsEnvKeyConstructor.ENV_SEAGATE_TEMP_FILE_SECRET_KEY_S3);

    public static final AmazonS3 s3client = AmazonS3ClientBuilder.standard()
            .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(SEAGATE_SERVICE_ENDPOINT, SEAGATE_SIGNING_REGION_US_EAST_1))
            .withCredentials(new AWSStaticCredentialsProvider(credentials)).build();
}
