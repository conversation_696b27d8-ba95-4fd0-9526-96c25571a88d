package com.actonia.utils;

import java.util.Properties;

public class StormCrawlerUtils {

	private static StormCrawlerUtils stormCrawlerUtils;
	private static Properties properties;

	public static StormCrawlerUtils getInstance() {
		if (stormCrawlerUtils == null) {
			stormCrawlerUtils = new StormCrawlerUtils();
		}
		return stormCrawlerUtils;
	}

	public String getRuntimePropertyString(String runtimePropertyName) {
		FormatUtils.getInstance().logMemoryUsage("getRuntimePropertyString() begins. runtimePropertyName=" + runtimePropertyName);
		String runtimePropertyString = null;
		if (properties == null) {
			try {
				properties = new Properties();
				properties.load(StormCrawlerUtils.class.getResourceAsStream("/storm_crawler.properties"));
				runtimePropertyString = properties.getProperty(runtimePropertyName);
			} catch (Exception e) {
				e.printStackTrace();
				FormatUtils.getInstance().logMemoryUsage("getRuntimePropertyString() exception message=" + e.getMessage());
				System.exit(-1);
			}
		}
		FormatUtils.getInstance().logMemoryUsage("getRuntimePropertyString() ends. runtimePropertyString=" + runtimePropertyString);
		return runtimePropertyString;
	}
}
