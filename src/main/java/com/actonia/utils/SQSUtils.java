package com.actonia.utils;

import java.util.*;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;

import com.actonia.IConstants;
import com.amazonaws.ClientConfiguration;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.AmazonSQSClientBuilder;
import com.amazonaws.services.sqs.model.BatchResultErrorEntry;
import com.amazonaws.services.sqs.model.ChangeMessageVisibilityRequest;
import com.amazonaws.services.sqs.model.CreateQueueRequest;
import com.amazonaws.services.sqs.model.DeleteMessageBatchRequest;
import com.amazonaws.services.sqs.model.DeleteMessageBatchRequestEntry;
import com.amazonaws.services.sqs.model.DeleteMessageRequest;
import com.amazonaws.services.sqs.model.DeleteQueueRequest;
import com.amazonaws.services.sqs.model.GetQueueAttributesRequest;
import com.amazonaws.services.sqs.model.GetQueueAttributesResult;
import com.amazonaws.services.sqs.model.ListQueuesRequest;
import com.amazonaws.services.sqs.model.ListQueuesResult;
import com.amazonaws.services.sqs.model.Message;
import com.amazonaws.services.sqs.model.MessageAttributeValue;
import com.amazonaws.services.sqs.model.PurgeQueueRequest;
import com.amazonaws.services.sqs.model.ReceiveMessageRequest;
import com.amazonaws.services.sqs.model.SendMessageBatchRequest;
import com.amazonaws.services.sqs.model.SendMessageBatchRequestEntry;
import com.amazonaws.services.sqs.model.SendMessageBatchResult;

public class SQSUtils {

	private static final int MAX_ERROR_RETRY = 3;
	private static final int MAX_CONNECTIONS = 1000;
	private static SQSUtils sqsUtils;
	private static AmazonSQS amazonSQS;
	private static final String ATTRIBUTE_NAME_APPROXIMATE_NUMBER_OF_MESSAGES = "ApproximateNumberOfMessages";
	private static final String ATTRIBUTE_NAME_APPROXIMATE_NUMBER_OF_MESSAGES_IN_FLIGHT = "ApproximateNumberOfMessagesNotVisible";
	private static final String FIFO_MESSAGE_GROUP_1 = "fifomessagegroup1";

	private SQSUtils() throws Exception {
		super();
		if (amazonSQS == null) {
			amazonSQS = instantiateAmazonSQS();
		}
	}

	public static SQSUtils getInstance() throws Exception {
		if (sqsUtils == null) {
			sqsUtils = new SQSUtils();
		}
		return sqsUtils;
	}

	private AmazonSQS getAmazonSQS() throws Exception {
		if (amazonSQS == null) {
			amazonSQS = instantiateAmazonSQS();
		}
		return amazonSQS;
	}

	private AmazonSQS instantiateAmazonSQS() throws Exception {
		AmazonSQS amazonSQS = null;
		AmazonSQSClientBuilder amazonSQSClientBuilder = null;
		ClientConfiguration clientConfiguration = null;
		BasicAWSCredentials basicAWSCredentials = null;
		AWSStaticCredentialsProvider awsStaticCredentialsProvider = null;

		amazonSQSClientBuilder = AmazonSQSClientBuilder.standard();

		// configuration
		clientConfiguration = new ClientConfiguration().withMaxErrorRetry(MAX_ERROR_RETRY).withMaxConnections(MAX_CONNECTIONS);
		amazonSQSClientBuilder.withClientConfiguration(clientConfiguration);

		// credentials
		String accessKeyId = AwsCredentialsEnvKeyConstructor.getInstance().getPlainTextSQSAccessKey();
		String secretAccessKey = AwsCredentialsEnvKeyConstructor.getInstance().getSQSDecryptedSecretKey();
		basicAWSCredentials = new BasicAWSCredentials(accessKeyId, secretAccessKey);
		awsStaticCredentialsProvider = new AWSStaticCredentialsProvider(basicAWSCredentials);
		amazonSQSClientBuilder.withCredentials(awsStaticCredentialsProvider);

		// endpoint
		amazonSQSClientBuilder.withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(IConstants.SQS_LOCATION_OREGON, Regions.US_WEST_2.getName()));

		amazonSQS = amazonSQSClientBuilder.build();
		return amazonSQS;

	}

	public String getSqsUrl(String SQS_NAME) {
		String queueUrl = null;
		try {
			queueUrl = createQueue(SQS_NAME);
			getAmazonSQS().deleteQueue(new DeleteQueueRequest(queueUrl));
			Thread.sleep(120000);
			queueUrl = createQueue(SQS_NAME);

		} catch (Exception e) {
			e.printStackTrace();
		}
		return queueUrl;
	}

	public void deleteFifoQueue(String SQS_NAME) throws Exception {
		String queueUrl = createFifoQueue(SQS_NAME);
		getAmazonSQS().deleteQueue(new DeleteQueueRequest(queueUrl));
	}

	public void deleteQueue(String SQS_NAME) throws Exception {
		String queueUrl = createQueue(SQS_NAME);
		getAmazonSQS().deleteQueue(new DeleteQueueRequest(queueUrl));
	}

	public void deleteQueueByUrl(String queueUrl) throws Exception {
		getAmazonSQS().deleteQueue(new DeleteQueueRequest(queueUrl));
	}

	public String createQueue(String queueName) throws Exception {
		CreateQueueRequest request = new CreateQueueRequest();
		request.setQueueName(queueName);
		request.getAttributes().put("VisibilityTimeout", "20");
		// 1209600 seconds = 14 days
		request.getAttributes().put("MessageRetentionPeriod", "1209600");
		return getAmazonSQS().createQueue(request).getQueueUrl();
	}

	public Map<String, String> getAttribute(String queueUrl) throws Exception {
		GetQueueAttributesRequest request = new GetQueueAttributesRequest();
		request.setQueueUrl(queueUrl);
		request.getAttributeNames();
		return getAmazonSQS().getQueueAttributes(request).getAttributes();
	}

	public List<Message> getMessageFromQueue(String queueUrl, int maxMessages, int visibilityTimeOut) throws Exception {
		List<Message> messageList = null;
		int retryCount = 0;

		List<String> attNames = new ArrayList<String>();
		attNames.add("All");
		ReceiveMessageRequest receiver = new ReceiveMessageRequest();
		receiver.setMaxNumberOfMessages(maxMessages);
		receiver.setVisibilityTimeout(visibilityTimeOut);
		receiver.setQueueUrl(queueUrl);
		receiver.setAttributeNames(attNames);

		retryLoop: while (retryCount < 8) {
			messageList = getAmazonSQS().receiveMessage(receiver).getMessages();
			// when queue name not ends with "STOP_CRAWL_QUEUE" and "QUEUE_NAMES"
			if (StringUtils.contains(queueUrl, "COMPETITOR_URL_HTML") == false && StringUtils.endsWith(queueUrl, "STOP_CRAWL_QUEUE") == false
					&& StringUtils.endsWith(queueUrl, "QUEUE_NAMES") == false) {
				if (messageList != null && messageList.size() > 0) {
					retryCount = 8;
					break retryLoop;
				} else {
					retryCount++;
					FormatUtils.getInstance().logMemoryUsage("getMessageFromQueue() messageList empty, retryCount=" + retryCount + ",queueUrl=" + queueUrl);
					Thread.sleep(8000);
					continue retryLoop;
				}
			} else {
				retryCount = 8;
				break retryLoop;
			}
		}

		return messageList;
	}

	public void deleteMessageFromQueue(String queueUrl, Message message) throws Exception {
		if (StringUtils.isEmpty(queueUrl)) {
			FormatUtils.getInstance().logMemoryUsage("SqsQueryUrl is Empty.");
			return;
		}
		DeleteMessageRequest deleteMessageRequest = new DeleteMessageRequest();
		deleteMessageRequest.setQueueUrl(queueUrl);
		deleteMessageRequest.setReceiptHandle(message.getReceiptHandle());

		getAmazonSQS().deleteMessage(deleteMessageRequest);

	}

	public void deleteMessageFromQueue(String queueUrl, Message message, Object... obj) throws Exception {
		if (obj != null && obj.length == 1) {
			int tryCount = 0;
			try {
				tryCount = Integer.parseInt((String) obj[0]);
			} catch (Exception e) {
				tryCount = 5;
			}

			int receiveCount = 0;
			try {
				receiveCount = Integer.parseInt(message.getAttributes().get("ApproximateReceiveCount"));
			} catch (Exception e) {
				e.printStackTrace();
				receiveCount = 0;
			}
			FormatUtils.getInstance().logMemoryUsage("receiveCount: " + receiveCount + " tryCount: " + tryCount);
			if (receiveCount < tryCount) {
				return;
			}
			FormatUtils.getInstance().logMemoryUsage("going to delete.");
		}

		DeleteMessageRequest deleteMessageRequest = new DeleteMessageRequest();
		deleteMessageRequest.setQueueUrl(queueUrl);
		deleteMessageRequest.setReceiptHandle(message.getReceiptHandle());
		getAmazonSQS().deleteMessage(deleteMessageRequest);
	}

	/**
	 * get Approximate Number Of Messages
	 * 
	 * @param service
	 * @param url
	 * @throws Exception 
	 */
	public String getApproximateNumberOfMessages(String queueUrl) throws Exception {
		GetQueueAttributesRequest request = new GetQueueAttributesRequest();
		request.setQueueUrl(queueUrl);
		request.getAttributeNames().add("ApproximateNumberOfMessages");
		return getAmazonSQS().getQueueAttributes(request).getAttributes().get("ApproximateNumberOfMessages");
	}

	private Map<String, String> getPropertyAboutURL(String queueUrl) throws Exception {
		GetQueueAttributesRequest request = new GetQueueAttributesRequest();
		request.setQueueUrl(queueUrl);
		request.getAttributeNames().add("All");

		return getAmazonSQS().getQueueAttributes(request).getAttributes();
	}

	public Integer getMessageNumberInQueue(String queueUrl) throws Exception {
		Map<String, String> map = getPropertyAboutURL(queueUrl);
		return NumberUtils.createInteger(map.get("ApproximateNumberOfMessagesNotVisible")) + NumberUtils.createInteger(map.get("ApproximateNumberOfMessages"));
	}

	public void sendBatchMessageToQueue(String queueUrl, Map<String, String> messages) throws Exception {
		List<SendMessageBatchRequestEntry> batchUploadEnties = new ArrayList<SendMessageBatchRequestEntry>();
		for (String messageId : messages.keySet()) {
			SendMessageBatchRequestEntry sendMessageBatchRequestEntry = new SendMessageBatchRequestEntry();
			sendMessageBatchRequestEntry.setMessageBody(messages.get(messageId));
			sendMessageBatchRequestEntry.setId(messageId);
			batchUploadEnties.add(sendMessageBatchRequestEntry);
		}

		SendMessageBatchRequest sendMessageBatchRequest = new SendMessageBatchRequest();
		sendMessageBatchRequest.setQueueUrl(queueUrl);
		sendMessageBatchRequest.setEntries(batchUploadEnties);

		SendMessageBatchResult result = getAmazonSQS().sendMessageBatch(sendMessageBatchRequest);
		if (result.getFailed() != null && result.getFailed().size() >= 0) {
			// try again
			batchUploadEnties = new ArrayList<SendMessageBatchRequestEntry>();
			for (BatchResultErrorEntry erroryEntity : result.getFailed()) {
				FormatUtils.getInstance().logMemoryUsage(erroryEntity.getMessage());

				SendMessageBatchRequestEntry sendMessageBatchRequestEntry = new SendMessageBatchRequestEntry();
				sendMessageBatchRequestEntry.setMessageBody(messages.get(erroryEntity.getId()));
				sendMessageBatchRequestEntry.setId(erroryEntity.getId());
				batchUploadEnties.add(sendMessageBatchRequestEntry);
			}

			sendMessageBatchRequest = new SendMessageBatchRequest();
			sendMessageBatchRequest.setQueueUrl(queueUrl);
			sendMessageBatchRequest.setEntries(batchUploadEnties);
		}
	}

	public void purgeQueue(String queueUrl) throws Exception {
		PurgeQueueRequest purgeQueueRequest = new PurgeQueueRequest();
		purgeQueueRequest.setQueueUrl(queueUrl);
		getAmazonSQS().purgeQueue(purgeQueueRequest);
	}

	public void sendBatchMessageToQueueThrowsException(String queueUrl, Map<String, String> messages) throws Exception {
		sendBatchMessageToQueueThrowsException(queueUrl, messages, null);
	}

	public void sendBatchMessageToQueueThrowsException(String queueUrl, Map<String, String> messages,
			Map<String, Map<String, MessageAttributeValue>> messageAttributeMapMap) throws Exception {
		List<SendMessageBatchRequestEntry> batchUploadEnties = new ArrayList<SendMessageBatchRequestEntry>();
		for (String messageKey : messages.keySet()) {
			SendMessageBatchRequestEntry sendMessageBatchRequestEntry = new SendMessageBatchRequestEntry();
			sendMessageBatchRequestEntry.setMessageBody(messages.get(messageKey));
			sendMessageBatchRequestEntry.setId(messageKey);
			if (messageAttributeMapMap != null && messageAttributeMapMap.size() > 0) {
				sendMessageBatchRequestEntry.setMessageAttributes(messageAttributeMapMap.get(messageKey));
			}
			batchUploadEnties.add(sendMessageBatchRequestEntry);
		}

		SendMessageBatchRequest sendMessageBatchRequest = new SendMessageBatchRequest();
		sendMessageBatchRequest.setQueueUrl(queueUrl);
		sendMessageBatchRequest.setEntries(batchUploadEnties);

		getAmazonSQS().sendMessageBatch(sendMessageBatchRequest);
	}

	public void changeVisibiltyTimeout(String queueUrl, Message message, int newTimeOut) throws Exception {
		ChangeMessageVisibilityRequest changeMessageVisibilityRequest = new ChangeMessageVisibilityRequest();
		changeMessageVisibilityRequest.setReceiptHandle(message.getReceiptHandle());
		changeMessageVisibilityRequest.setQueueUrl(queueUrl);
		changeMessageVisibilityRequest.setVisibilityTimeout(newTimeOut);
		getAmazonSQS().changeMessageVisibility(changeMessageVisibilityRequest);
	}

	public void deleteMessagesFromQueue(String queueUrl, List<Message> messages, int messagesPerIteration) throws Exception {
		DeleteMessageBatchRequestEntry deleteMessageBatchRequestEntry = null;
		List<DeleteMessageBatchRequestEntry> deleteMessageBatchRequestEntryList = new ArrayList<DeleteMessageBatchRequestEntry>();
		DeleteMessageBatchRequest deleteMessageBatchRequest = new DeleteMessageBatchRequest();
		deleteMessageBatchRequest.setQueueUrl(queueUrl);
		for (Message message : messages) {
			deleteMessageBatchRequestEntry = new DeleteMessageBatchRequestEntry();
			deleteMessageBatchRequestEntry.setId(String.valueOf(System.nanoTime()));
			deleteMessageBatchRequestEntry.setReceiptHandle(message.getReceiptHandle());
			deleteMessageBatchRequestEntryList.add(deleteMessageBatchRequestEntry);
			if (deleteMessageBatchRequestEntryList.size() >= messagesPerIteration) {
				deleteMessageBatchRequest.setEntries(deleteMessageBatchRequestEntryList);
				getAmazonSQS().deleteMessageBatch(deleteMessageBatchRequest);
				deleteMessageBatchRequestEntryList = new ArrayList<DeleteMessageBatchRequestEntry>();
			}
		}
		if (deleteMessageBatchRequestEntryList != null && deleteMessageBatchRequestEntryList.size() > 0) {
			deleteMessageBatchRequest.setEntries(deleteMessageBatchRequestEntryList);
			getAmazonSQS().deleteMessageBatch(deleteMessageBatchRequest);
			deleteMessageBatchRequestEntryList = new ArrayList<DeleteMessageBatchRequestEntry>();
		}
	}

	// output:
	// [0] = number of messages in queue
	// [1] = number of messages in flight
	public Integer[] getApproximateNumberOfMessagesAndInflight(String queueName) throws Exception {
		String queueUrl = createQueue(queueName);
		return getApproximateNumberOfMessagesAndInflightByQueueUrl(queueUrl);
	}

	// output:
	// [0] = number of messages in queue
	// [1] = number of messages in flight
	public Integer[] getApproximateNumberOfMessagesAndInflightByQueueUrl(String queueUrl) throws Exception {
		Integer[] numberOfMessagesArray = null;
		GetQueueAttributesResult getQueueAttributesResult = null;
		String testString = null;
		List<String> attributeNames = new ArrayList<String>();
		attributeNames.add(ATTRIBUTE_NAME_APPROXIMATE_NUMBER_OF_MESSAGES);
		attributeNames.add(ATTRIBUTE_NAME_APPROXIMATE_NUMBER_OF_MESSAGES_IN_FLIGHT);
		getQueueAttributesResult = getAmazonSQS().getQueueAttributes(queueUrl, attributeNames);
		if (getQueueAttributesResult != null) {
			numberOfMessagesArray = new Integer[2];
			// 
			testString = getQueueAttributesResult.getAttributes().get(ATTRIBUTE_NAME_APPROXIMATE_NUMBER_OF_MESSAGES);
			if (StringUtils.isNotBlank(testString)) {
				numberOfMessagesArray[0] = NumberUtils.toInt(testString);
			}
			// 
			testString = getQueueAttributesResult.getAttributes().get(ATTRIBUTE_NAME_APPROXIMATE_NUMBER_OF_MESSAGES_IN_FLIGHT);
			if (StringUtils.isNotBlank(testString)) {
				numberOfMessagesArray[1] = NumberUtils.toInt(testString);
			}
		}
		return numberOfMessagesArray;
	}

	public List<String> getQueueUrlsByPrefix(String queueNamePrefix) throws Exception {
		List<String> queueUrlList = new ArrayList<String>();
		ListQueuesResult listQueuesResult = getAmazonSQS().listQueues(new ListQueuesRequest(queueNamePrefix));
		if (listQueuesResult != null && listQueuesResult.getQueueUrls() != null && listQueuesResult.getQueueUrls().size() > 0) {
			for (String queueUrl : listQueuesResult.getQueueUrls()) {
				queueUrlList.add(queueUrl);
			}
		}
		return queueUrlList;
	}

	public void shutdown() throws Exception {
		getAmazonSQS().shutdown();
	}

	public String createFifoQueue(String queueName) throws Exception {
		CreateQueueRequest request = new CreateQueueRequest();
		request.setQueueName(queueName);
		request.getAttributes().put("VisibilityTimeout", "20");
		request.getAttributes().put("MessageRetentionPeriod", "1209600"); // 1209600 seconds = 14 days
		request.getAttributes().put("FifoQueue", "true");
		request.getAttributes().put("ContentBasedDeduplication", "true");		
		return getAmazonSQS().createQueue(request).getQueueUrl();
	}

	public void sendBatchMessageToFifoQueue(String queueUrl, Map<String, String> messages) throws Exception {
		List<SendMessageBatchRequestEntry> batchUploadEnties = new ArrayList<SendMessageBatchRequestEntry>();
		for (String messageId : messages.keySet()) {
			SendMessageBatchRequestEntry sendMessageBatchRequestEntry = new SendMessageBatchRequestEntry();
			sendMessageBatchRequestEntry.setMessageBody(messages.get(messageId));
			sendMessageBatchRequestEntry.setId(messageId);
			sendMessageBatchRequestEntry.setMessageGroupId(messageId);
			batchUploadEnties.add(sendMessageBatchRequestEntry);
		}

		SendMessageBatchRequest sendMessageBatchRequest = new SendMessageBatchRequest();
		sendMessageBatchRequest.setQueueUrl(queueUrl);
		sendMessageBatchRequest.setEntries(batchUploadEnties);

		SendMessageBatchResult result = getAmazonSQS().sendMessageBatch(sendMessageBatchRequest);
		if (result.getFailed() != null && result.getFailed().size() >= 0) {
			// try again
			batchUploadEnties = new ArrayList<SendMessageBatchRequestEntry>();
			for (BatchResultErrorEntry erroryEntity : result.getFailed()) {
				FormatUtils.getInstance().logMemoryUsage(erroryEntity.getMessage());

				SendMessageBatchRequestEntry sendMessageBatchRequestEntry = new SendMessageBatchRequestEntry();
				sendMessageBatchRequestEntry.setMessageBody(messages.get(erroryEntity.getId()));
				sendMessageBatchRequestEntry.setId(erroryEntity.getId());
				batchUploadEnties.add(sendMessageBatchRequestEntry);
			}

			sendMessageBatchRequest = new SendMessageBatchRequest();
			sendMessageBatchRequest.setQueueUrl(queueUrl);
			sendMessageBatchRequest.setEntries(batchUploadEnties);
		}
	}

	/**
	 * Retrieves a list of queue URLs based on the given prefix and maximum number of results.
	 *
	 * @param prefix     the prefix to filter queue names
	 * @param maxResults the maximum number of results to return
	 * @return a list of queue URLs matching the prefix and limited by the maximum number of results
	 * @throws Exception if an error occurs while retrieving the queue list
	 */
	public ListQueuesResult getQueueListByQueueNamePrefix(String prefix, Integer maxResults, String nextToken) throws Exception {
		final ListQueuesRequest listQueuesRequest = new ListQueuesRequest(prefix);
		if (nextToken != null) {
			listQueuesRequest.setNextToken(nextToken);
		}
		if (maxResults != null) {
			listQueuesRequest.setMaxResults(maxResults);
		}
        return getAmazonSQS().listQueues(listQueuesRequest);
	}
}
