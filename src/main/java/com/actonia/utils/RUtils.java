package com.actonia.utils;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.value.object.BestMatch;
import com.actonia.value.object.CausalImpactObject;
import com.actonia.value.object.CausalImpactRequest;
import com.actonia.value.object.CausalImpactResponse;
import com.actonia.value.object.Coefficients;
import com.actonia.value.object.Inference;
import com.actonia.value.object.MarketMatchingRequest;
import com.actonia.value.object.MarketMatchingResponse;
import com.actonia.value.object.PosteriorInference;
import com.actonia.value.object.ProphetRequest;
import com.actonia.value.object.ProphetResponse;
import com.actonia.value.object.RRequest;
import com.actonia.value.object.RResponse;
import com.actonia.value.object.WebServiceError;
import com.github.rcaller.datatypes.DataFrame;
import com.github.rcaller.rstuff.RCaller;
import com.github.rcaller.rstuff.RCode;
import com.google.gson.Gson;

public class RUtils {

	private boolean isDebug = false;

	private static final String INTERNAL_CONTROL_SERIES_NAME_PREFIX = "control";

	private static RUtils rUtils;

	private RUtils() {
		super();
	}

	public static RUtils getInstance() {
		if (rUtils == null) {
			rUtils = new RUtils();
		}
		return rUtils;
	}

	// Reason for the 'synchronized' method:
	// As of 2021-08-18, the R on 'scripts20' server is single-threaded.  
	// Therefore, only one request can be processed at a time.
	public RResponse invokeR(RRequest rRequest) throws Exception {
		RResponse rResponse = null;
		boolean isWithControlSeries = false;
		if (rRequest != null) {
			rResponse = new RResponse();
			// when invoking the 'CausalImpact' package
			if (rRequest.getrPackage() == IConstants.R_PACKAGE_CAUSAL_IMPACT) {
				if (StringUtils.equalsIgnoreCase(rRequest.getCommand(), IConstants.COMMAND_CALCULATE_CORRELATION_COEFFICIENT)) {
					rResponse.setCausalImpactResponse(calculateCorrelationCoefficient(rRequest.getCausalImpactRequest()));
				} else if (StringUtils.equalsIgnoreCase(rRequest.getCommand(), IConstants.COMMAND_ANALYZE)) {
					isWithControlSeries = true;
					rResponse.setCausalImpactResponse(analyze(rRequest.getCausalImpactRequest(), isWithControlSeries));
				} else if (StringUtils.equalsIgnoreCase(rRequest.getCommand(), IConstants.COMMAND_ANALYZE_WITHOUT_CONTROL_SERIES)) {
					isWithControlSeries = false;
					rResponse.setCausalImpactResponse(analyze(rRequest.getCausalImpactRequest(), isWithControlSeries));
				}
			}
			// when invoking the 'prophet' package
			else if (rRequest.getrPackage() == IConstants.R_PACKAGE_PROPHET) {
				rResponse.setProphetResponse(forecast(rRequest.getProphetRequest()));
			}
			// when invoking the 'MarketMatching' package
			else if (rRequest.getrPackage() == IConstants.R_PACKAGE_MARKET_MATCHING) {
				rResponse.setMarketMatchingResponse(marketMatching(rRequest.getMarketMatchingRequest()));
			}
		}
		return rResponse;
	}

	private ProphetResponse forecast(ProphetRequest prophetRequest) throws Exception {

		ProphetResponse prophetResponse = new ProphetResponse();

		double[] testDoubleArray = null;

		RCaller rCaller = RCaller.create();
		RCode code = RCode.create();
		code.addRCode("library(prophet)");
		Object[][] objects = new Object[][] { prophetRequest.getDate_array(), prophetRequest.getValue_array() };
		String[] names = new String[] { "ds", "y" };
		DataFrame dataFrame = DataFrame.create(objects, names);
		code.addDataFrame("df", dataFrame);
		code.addRCode("m <- prophet(df)");
		code.addRCode("future <- make_future_dataframe(m, periods = " + prophetRequest.getForecast_days() + ")");
		code.addRCode("forecast <- predict(m, future)");
		rCaller.setRCode(code);
		rCaller.runAndReturnResult("forecast");
		for (String name : rCaller.getParser().getNames()) {
			testDoubleArray = rCaller.getParser().getAsDoubleArray(name);
			if (StringUtils.equalsIgnoreCase(name, IConstants.TREND)) {
				prophetResponse.setTrend_array(testDoubleArray);
			} else if (StringUtils.equalsIgnoreCase(name, IConstants.TREND_LOWER)) {
				prophetResponse.setTrend_lower_array(testDoubleArray);
			} else if (StringUtils.equalsIgnoreCase(name, IConstants.TREND_UPPER)) {
				prophetResponse.setTrend_upper_array(testDoubleArray);
			} else if (StringUtils.equalsIgnoreCase(name, IConstants.YHAT)) {
				prophetResponse.setForecast_array(testDoubleArray);
			} else if (StringUtils.equalsIgnoreCase(name, IConstants.YHAT_LOWER)) {
				prophetResponse.setForecast_lower_array(testDoubleArray);
			} else if (StringUtils.equalsIgnoreCase(name, IConstants.YHAT_UPPER)) {
				prophetResponse.setForecast_upper_array(testDoubleArray);
			} else if (StringUtils.equalsIgnoreCase(name, IConstants.WEEKLY)) {
				prophetResponse.setWeekly_seasonality_map(getWeeklySeasonalityMap(prophetRequest.getDate_array(), testDoubleArray));
			} else if (StringUtils.equalsIgnoreCase(name, IConstants.YEARLY)) {
				prophetResponse.setYearly_seasonality_array(getYearlySeasonalityArray(prophetRequest.getDate_array(), testDoubleArray));
			}
		}
		prophetResponse.setDate_array(getProphetResponseDateArray(prophetRequest));
		return prophetResponse;
	}

	private String[] getProphetResponseDateArray(ProphetRequest prophetRequest) throws Exception {
		Date testDate = null;
		String testString = null;
		List<String> dateList = Arrays.asList(prophetRequest.getDate_array());
		List<String> responseDateList = new ArrayList<String>(dateList);
		String startDateString = prophetRequest.getDate_array()[prophetRequest.getDate_array().length - 1];
		Date startDate = DateUtils.parseDateStrictly(startDateString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
		int forecastDays = prophetRequest.getForecast_days();
		for (int i = 0; i < forecastDays; i++) {
			testDate = DateUtils.addDays(startDate, (i + 1));
			testString = DateFormatUtils.format(testDate, IConstants.DATE_FORMAT_YYYY_MM_DD);
			responseDateList.add(testString);
		}
		return responseDateList.toArray(new String[0]);
	}

	private Map<Integer, Double> getWeeklySeasonalityMap(String[] dateArray, double[] weeklyArray) {

		// map key = days of week
		// map value = seasonality value
		Map<Integer, Double> weeklySeasonalityMap = null;

		Integer dayOfWeekNumber = null;

		// there must be at least one week's data
		if (dateArray.length >= 7 && weeklyArray.length >= 7) {
			weeklySeasonalityMap = new HashMap<Integer, Double>();
			nextDate: for (int i = 0; i < dateArray.length; i++) {
				dayOfWeekNumber = LocalDate.parse(dateArray[i]).getDayOfWeek().getValue();
				if (weeklySeasonalityMap.containsKey(dayOfWeekNumber) == false) {
					weeklySeasonalityMap.put(dayOfWeekNumber, weeklyArray[i]);
				}

				// when all day of weeks have been processed
				if (weeklySeasonalityMap.size() == 7) {
					break nextDate;
				}
			}
		}

		return weeklySeasonalityMap;
	}

	private double[] getYearlySeasonalityArray(String[] dateArray, double[] yearlyArray) {

		double[] yearlySeasonalityArray = null;
		int dayOfYearNumber = 0;
		Integer month = null;
		Integer day = null;
		int lastDayOfMonth = 0;
		String nonLeapYearDateString = null;
		Map<Integer, Integer> monthLastDayMap = null;
		String dateString = null;
		double yearlySeasonality = 0d;

		Map<Integer, Double> dayOfYearSeasonalityMap = new HashMap<Integer, Double>();

		// there must be at least one year's data
		if (dateArray.length >= 365 && yearlyArray.length >= 365) {
			monthLastDayMap = FormatUtils.getInstance().getMonthLastDayMap();
			nextDate: for (int i = 0; i < dateArray.length; i++) {
				dateString = dateArray[i];
				month = FormatUtils.getInstance().getMonth(dateString);
				if (month != null) {
					if (monthLastDayMap.containsKey(month)) {
						lastDayOfMonth = monthLastDayMap.get(month);
						day = FormatUtils.getInstance().getDay(dateString);
						if (day != null) {
							if (day <= lastDayOfMonth) {
								nonLeapYearDateString = IConstants.NON_LEAP_YEAR + IConstants.DASH + StringUtils.substring(dateString, 5, 10);
								dayOfYearNumber = LocalDate.parse(nonLeapYearDateString).getDayOfYear();
								if (dayOfYearSeasonalityMap.containsKey(dayOfYearNumber) == false) {
									dayOfYearSeasonalityMap.put(dayOfYearNumber, yearlyArray[i]);
								}

								if (dayOfYearSeasonalityMap.size() >= 365) {
									break nextDate;
								}
							}
						}
					}
				}
			}

			if (dayOfYearSeasonalityMap != null && dayOfYearSeasonalityMap.size() > 0) {
				yearlySeasonalityArray = new double[365];
				for (int i = 0; i < yearlySeasonalityArray.length; i++) {
					yearlySeasonalityArray[i] = 0d;
				}
				for (Integer testDayOfYear : dayOfYearSeasonalityMap.keySet()) {
					yearlySeasonality = dayOfYearSeasonalityMap.get(testDayOfYear);
					yearlySeasonalityArray[testDayOfYear - 1] = yearlySeasonality;
				}
			}
		}

		return yearlySeasonalityArray;
	}

	private CausalImpactResponse calculateCorrelationCoefficient(CausalImpactRequest causalImpactRequest) throws Exception {
		if (isDebug == true) {
			FormatUtils.getInstance().logMemoryUsage("calculateCorrelationCoefficient() begins. causalImpactRequest=" + causalImpactRequest.toString());
		}
		long startTimestamp = System.currentTimeMillis();
		CausalImpactResponse causalImpactResponse = null;
		String[] testStringArray = null;
		RCaller rCaller = null;
		RCode code = null;
		StringBuilder stringBuilder = null;
		String originalControlName = null;
		String internalControlName = null;
		int internalControlNameIndex = 0;

		// map key = control time series name
		// map value = control time series correlation coefficient
		Map<String, Double> controlNameCorrelationCoefficientMap = null;

		// map key = original control series name
		// map value = internal control series name
		Map<String, String> controlSeriesNameMap = new HashMap<String, String>();

		try {
			rCaller = RCaller.create();
			code = RCode.create();
			code.addRCode("library(CausalImpact)");
			code.addDoubleArray("testTimeSeries", causalImpactRequest.getTest_time_series());

			for (int i = 0; i < causalImpactRequest.getControl_name_list().size(); i++) {
				originalControlName = causalImpactRequest.getControl_name_list().get(i);
				internalControlName = INTERNAL_CONTROL_SERIES_NAME_PREFIX + ++internalControlNameIndex;
				controlSeriesNameMap.put(originalControlName, internalControlName);
				code.addDoubleArray(internalControlName, causalImpactRequest.getControl_time_series_list().get(i));
			}

			stringBuilder = new StringBuilder();
			stringBuilder.append("data <- cbind(");
			stringBuilder.append("testTimeSeries");
			for (int i = 0; i < causalImpactRequest.getControl_name_list().size(); i++) {
				originalControlName = causalImpactRequest.getControl_name_list().get(i);
				internalControlName = controlSeriesNameMap.get(originalControlName);
				stringBuilder.append(IConstants.COMMA);
				stringBuilder.append(internalControlName);
			}
			stringBuilder.append(IConstants.CLOSING_PARENTHESIS);
			code.addRCode(stringBuilder.toString());

			code.addRCode("cor(data)");
			code.addRCode("result <- cor(data)");
			rCaller.setRCode(code);

			rCaller.runAndReturnResultOnline("result");

			if (isDebug == true) {
				for (String name : rCaller.getParser().getNames()) {
					System.out.println("calculateCorrelationCoefficient() rCaller.getParser().getNames()=" + name);
				}
			}

			testStringArray = rCaller.getParser().getAsStringArray("result");
			controlNameCorrelationCoefficientMap = new HashMap<String, Double>();
			for (int i = 0; i < testStringArray.length; i++) {
				if (i == 0) {
					continue;
				}
				controlNameCorrelationCoefficientMap.put(causalImpactRequest.getControl_name_list().get(i - 1),
						FormatUtils.getInstance().convertStringToDouble(testStringArray[i]));
				if ((i + 1) > causalImpactRequest.getControl_name_list().size()) {
					break;
				}
			}
			causalImpactResponse = new CausalImpactResponse();
			causalImpactResponse.setSuccess(true);
			causalImpactResponse.setControl_name_corr_coef_map(controlNameCorrelationCoefficientMap);
		} catch (Exception e) {
			throw e;
		} finally {
			if (rCaller != null) {
				rCaller.stopRCallerOnline();
			}
		}
		if (isDebug == true) {
			FormatUtils.getInstance().logMemoryUsage("calculateCorrelationCoefficient() ends. causalImpactResponse=" + causalImpactResponse.toString()
					+ ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		}
		return causalImpactResponse;
	}

	public static void main(String[] args) throws Exception {
		RUtils rUtils = new RUtils();
		String json = "{\"ownDomainId\":12283,\"access_token\":\"c09yxv13-opr3-d745-9734-8pu48420nj67\",\"test_time_series\":[8.0,4.0,3.0,2.0,5.0,1.0,7.0,7.0,4.0,1.0,4.0,4.0,4.0,3.0,6.0,3.0,0.0,1.0,5.0,7.0,6.0,2.0,4.0,2.0,3.0,5.0,3.0,4.0,0.0,4.0,1.0,0.0,4.0,2.0,6.0,4.0,3.0,1.0,4.0,5.0,3.0,5.0,3.0,4.0,0.0,4.0,1.0,1.0,3.0,2.0,7.0,1.0,0.0,1.0,4.0,5.0,5.0,1.0,1.0,2.0,5.0],\"control_name_list\":[\"7732180\"],\"control_time_series_list\":[[4.0,2.0,1.0,2.0,3.0,7.0,4.0,2.0,4.0,1.0,2.0,7.0,2.0,6.0,4.0,4.0,2.0,4.0,3.0,3.0,0.0,6.0,4.0,3.0,3.0,0.0,4.0,4.0,3.0,2.0,0.0,1.0,6.0,5.0,4.0,4.0,2.0,1.0,3.0,1.0,4.0,4.0,5.0,2.0,2.0,2.0,2.0,2.0,2.0,1.0,5.0,3.0,3.0,1.0,4.0,4.0,6.0,3.0,0.0,2.0,2.0]],\"version\":3,\"pre_period_start_date\":\"2025-05-01\",\"pre_period_end_date\":\"2025-06-30\",\"post_period_start_date\":\"\",\"post_period_end_date\":\"\",\"testTimeSeriesAvailable\":true}";
		final CausalImpactRequest causalImpactRequest = new Gson().fromJson(json, CausalImpactRequest.class);
		final CausalImpactResponse causalImpactResponse = rUtils.calculateCorrelationCoefficient(causalImpactRequest);
		final CausalImpactResponse analyze = rUtils.analyze(causalImpactRequest, true);
		String result = new Gson().toJson(analyze);
	}

	private CausalImpactResponse analyze(CausalImpactRequest causalImpactRequest, boolean isWithControlSeries) throws Exception {
		if (isDebug == true) {
			FormatUtils.getInstance().logMemoryUsage("analyze() begins. causalImpactRequest=" + causalImpactRequest.toString());
		}
		long startTimestamp = System.currentTimeMillis();
		CausalImpactResponse causalImpactResponse = new CausalImpactResponse();
		causalImpactResponse.setSuccess(true);
		RCaller rCaller = null;
		String[] testStringArray = null;
		Long totalPreAndPostPeriodDays = null;
		StringBuilder stringBuilder = null;
		PosteriorInference posteriorInferenceAverage = null;
		PosteriorInference posteriorInferenceCumulative = null;
		String summary = null;

		try {
			final long preDays = ChronoUnit.DAYS.between(LocalDate.parse(causalImpactRequest.getPre_period_start_date()),
					LocalDate.parse(causalImpactRequest.getPre_period_end_date())) + 1;
			final long postDays = ChronoUnit.DAYS.between(LocalDate.parse(causalImpactRequest.getPost_period_start_date()),
					LocalDate.parse(causalImpactRequest.getPost_period_end_date())) + 1;
			totalPreAndPostPeriodDays = preDays + postDays;
			if (isWithControlSeries == true) {
				rCaller = getRCallerWithControlTimeSeries(causalImpactRequest, preDays, postDays);
			} else {
				rCaller = getRCallerWithoutControlTimeSeries(causalImpactRequest, preDays, postDays);
			}
			try {

				// detailed summary
				rCaller.runAndReturnResultOnline("impact$summary");

				// posterior inference
				posteriorInferenceAverage = new PosteriorInference();
				posteriorInferenceCumulative = new PosteriorInference();
				for (String name : rCaller.getParser().getNames()) {
					testStringArray = rCaller.getParser().getAsStringArray(name);
					System.out.println("analyze() rCaller.getParser().getNames()=" + name + ",testStringArray=" + testStringArray[0] + "," + testStringArray[1]);
					if (StringUtils.equalsIgnoreCase(name, IConstants.ACTUAL)) {
						posteriorInferenceAverage.setActual(FormatUtils.getInstance().convertStringToDouble(testStringArray[0]));
						posteriorInferenceCumulative.setActual(FormatUtils.getInstance().convertStringToDouble(testStringArray[1]));
					} else if (StringUtils.equalsIgnoreCase(name, IConstants.PRED)) {
						posteriorInferenceAverage.setPrediction(FormatUtils.getInstance().convertStringToDouble(testStringArray[0]));
						posteriorInferenceCumulative.setPrediction(FormatUtils.getInstance().convertStringToDouble(testStringArray[1]));
					} else if (StringUtils.equalsIgnoreCase(name, IConstants.PRED_LOWER)) {
						posteriorInferenceAverage.setPrediction_lower(FormatUtils.getInstance().convertStringToDouble(testStringArray[0]));
						posteriorInferenceCumulative.setPrediction_lower(FormatUtils.getInstance().convertStringToDouble(testStringArray[1]));
					} else if (StringUtils.equalsIgnoreCase(name, IConstants.PRED_UPPER)) {
						posteriorInferenceAverage.setPrediction_upper(FormatUtils.getInstance().convertStringToDouble(testStringArray[0]));
						posteriorInferenceCumulative.setPrediction_upper(FormatUtils.getInstance().convertStringToDouble(testStringArray[1]));
					} else if (StringUtils.equalsIgnoreCase(name, IConstants.PRED_SD)) {
						posteriorInferenceAverage.setPrediction_standard_derivation(FormatUtils.getInstance().convertStringToDouble(testStringArray[0]));
						posteriorInferenceCumulative.setPrediction_standard_derivation(FormatUtils.getInstance().convertStringToDouble(testStringArray[1]));
					} else if (StringUtils.equalsIgnoreCase(name, IConstants.ABS_EFFECT)) {
						posteriorInferenceAverage.setAbsolute_effect(FormatUtils.getInstance().convertStringToDouble(testStringArray[0]));
						posteriorInferenceCumulative.setAbsolute_effect(FormatUtils.getInstance().convertStringToDouble(testStringArray[1]));
					} else if (StringUtils.equalsIgnoreCase(name, IConstants.ABS_EFFECT_LOWER)) {
						posteriorInferenceAverage.setAbsolute_effect_lower(FormatUtils.getInstance().convertStringToDouble(testStringArray[0]));
						posteriorInferenceCumulative.setAbsolute_effect_lower(FormatUtils.getInstance().convertStringToDouble(testStringArray[1]));
					} else if (StringUtils.equalsIgnoreCase(name, IConstants.ABS_EFFECT_UPPER)) {
						posteriorInferenceAverage.setAbsolute_effect_upper(FormatUtils.getInstance().convertStringToDouble(testStringArray[0]));
						posteriorInferenceCumulative.setAbsolute_effect_upper(FormatUtils.getInstance().convertStringToDouble(testStringArray[1]));
					} else if (StringUtils.equalsIgnoreCase(name, IConstants.ABS_EFFECT_SD)) {
						posteriorInferenceAverage.setAbsolute_effect_standard_derivation(FormatUtils.getInstance().convertStringToDouble(testStringArray[0]));
						posteriorInferenceCumulative.setAbsolute_effect_standard_derivation(FormatUtils.getInstance().convertStringToDouble(testStringArray[1]));
					} else if (StringUtils.equalsIgnoreCase(name, IConstants.REL_EFFECT)) {
						posteriorInferenceAverage.setRelative_effect(FormatUtils.getInstance().convertStringToDouble(testStringArray[0]));
						posteriorInferenceCumulative.setRelative_effect(FormatUtils.getInstance().convertStringToDouble(testStringArray[1]));
					} else if (StringUtils.equalsIgnoreCase(name, IConstants.REL_EFFECT_LOWER)) {
						posteriorInferenceAverage.setRelative_effect_lower(FormatUtils.getInstance().convertStringToDouble(testStringArray[0]));
						posteriorInferenceCumulative.setRelative_effect_lower(FormatUtils.getInstance().convertStringToDouble(testStringArray[1]));
					} else if (StringUtils.equalsIgnoreCase(name, IConstants.REL_EFFECT_UPPER)) {
						posteriorInferenceAverage.setRelative_effect_upper(FormatUtils.getInstance().convertStringToDouble(testStringArray[0]));
						posteriorInferenceCumulative.setRelative_effect_upper(FormatUtils.getInstance().convertStringToDouble(testStringArray[1]));
					} else if (StringUtils.equalsIgnoreCase(name, IConstants.REL_EFFECT_SD)) {
						posteriorInferenceAverage.setRelative_effect_standard_derivation(FormatUtils.getInstance().convertStringToDouble(testStringArray[0]));
						posteriorInferenceCumulative.setRelative_effect_standard_derivation(FormatUtils.getInstance().convertStringToDouble(testStringArray[1]));
					} else if (StringUtils.equalsIgnoreCase(name, IConstants.ALPHA)) {
						posteriorInferenceAverage.setAlpha(FormatUtils.getInstance().convertStringToDouble(testStringArray[0]));
						posteriorInferenceCumulative.setAlpha(FormatUtils.getInstance().convertStringToDouble(testStringArray[1]));
					} else if (StringUtils.equalsIgnoreCase(name, IConstants.P)) {
						posteriorInferenceAverage.setTail_area_probability(FormatUtils.getInstance().convertStringToDouble(testStringArray[0]));
						posteriorInferenceCumulative.setTail_area_probability(FormatUtils.getInstance().convertStringToDouble(testStringArray[1]));
					}
				}

				// summary report
				rCaller.runAndReturnResultOnline("impact");
				testStringArray = rCaller.getParser().getAsStringArray("report");
				if (testStringArray != null && testStringArray.length > 0) {
					stringBuilder = new StringBuilder();
					for (String testString : testStringArray) {
						stringBuilder.append(testString);
					}
					summary = stringBuilder.toString();
				}

				// time series
				testStringArray = rCaller.getParser().getAsStringArray("series");
				causalImpactResponse.setPre_period_start_date(causalImpactRequest.getPre_period_start_date());
				causalImpactResponse.setPre_period_end_date(causalImpactRequest.getPre_period_end_date());
				causalImpactResponse.setPost_period_start_date(causalImpactRequest.getPost_period_start_date());
				causalImpactResponse.setPost_period_end_date(causalImpactRequest.getPost_period_end_date());
				causalImpactResponse.setSummary(summary);
				causalImpactResponse.setPosterior_inference_average(posteriorInferenceAverage);
				causalImpactResponse.setPosterior_inference_cumulative(posteriorInferenceCumulative);
				causalImpactResponse.setTest_time_series(causalImpactRequest.getTest_time_series());

				causalImpactResponse.setCounter_factual_prediction_time_series(
						extractTimeSeries(IConstants.COUNTER_FACTUAL_PREDICTION_DATA_POINTS_POSITION, totalPreAndPostPeriodDays, testStringArray));
				causalImpactResponse.setCounter_factual_prediction_time_series_lower(
						extractTimeSeries(IConstants.COUNTER_FACTUAL_PREDICTION_DATA_POINTS_POSITION_LOWER, totalPreAndPostPeriodDays, testStringArray));
				causalImpactResponse.setCounter_factual_prediction_time_series_upper(
						extractTimeSeries(IConstants.COUNTER_FACTUAL_PREDICTION_DATA_POINTS_POSITION_UPPER, totalPreAndPostPeriodDays, testStringArray));

				causalImpactResponse.setPointwise_causal_effect_time_series(
						extractTimeSeries(IConstants.POINTWISE_CAUSAL_EFFECT_DATA_POINTS_POSITION, totalPreAndPostPeriodDays, testStringArray));
				causalImpactResponse.setPointwise_causal_effect_time_series_lower(
						extractTimeSeries(IConstants.POINTWISE_CAUSAL_EFFECT_DATA_POINTS_POSITION_LOWER, totalPreAndPostPeriodDays, testStringArray));
				causalImpactResponse.setPointwise_causal_effect_time_series_upper(
						extractTimeSeries(IConstants.POINTWISE_CAUSAL_EFFECT_DATA_POINTS_POSITION_UPPER, totalPreAndPostPeriodDays, testStringArray));

				causalImpactResponse.setCumulative_effect_time_series(
						extractTimeSeries(IConstants.CUMULATIVE_EFFECT_DATA_POINTS_POSITION, totalPreAndPostPeriodDays, testStringArray));
				causalImpactResponse.setCumulative_effect_time_series_lower(
						extractTimeSeries(IConstants.CUMULATIVE_EFFECT_DATA_POINTS_POSITION_LOWER, totalPreAndPostPeriodDays, testStringArray));
				causalImpactResponse.setCumulative_effect_time_series_upper(
						extractTimeSeries(IConstants.CUMULATIVE_EFFECT_DATA_POINTS_POSITION_UPPER, totalPreAndPostPeriodDays, testStringArray));
			} catch (Exception e) {
				// proceed when 'Variable report not found'
				e.printStackTrace();

				causalImpactResponse.setSuccess(false);
				WebServiceError webServiceError = new WebServiceError();
				webServiceError.setError_code(IConstants.MSG_CD_CAUSAL_IMPACT_WEB_SERVICE_METHOD_EXCEPTION);
				webServiceError.setError_message("Data cannot be analyzed.");
				causalImpactResponse.setError(webServiceError);

				//StringWriter stringWriter = new StringWriter();
				//e.printStackTrace(new PrintWriter(stringWriter));

				// send support alert
				//String message = stringWriter.toString();
				//String requestParameters = new Gson().toJson(causalImpactRequest, CausalImpactRequest.class);
				//CommonUtils.sendRUtilsAlert(message, requestParameters);

			}
		} catch (Exception e) {
			throw e;
		} finally {
			if (rCaller != null) {
				rCaller.stopRCallerOnline();
			}
		}
		if (isDebug == true) {
			FormatUtils.getInstance().logMemoryUsage(
					"analyze() ends. causalImpactResponse=" + causalImpactResponse.toString() + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		}
		return causalImpactResponse;
	}

	private RCaller getRCallerWithoutControlTimeSeries(CausalImpactRequest causalImpactRequest, long preDays, long postDays) {
		RCaller rCaller = RCaller.create();
		RCode code = RCode.create();
		code.addRCode("library(CausalImpact)");
		final double[] y = causalImpactRequest.getTest_time_series();
		code.addDoubleArray("y", y);

		code.addRCode("data <- zoo(cbind(y), 1:length(y))");
		code.addRCode("pre.period <- c(1, " + preDays + ")");
		code.addRCode("post.period <- c(" + (preDays + 1) + ", " + (preDays + postDays) + ")");
		code.addRCode("impact <- CausalImpact(data, pre.period, post.period)");

		rCaller.setRCode(code);

		return rCaller;
	}

	private RCaller getRCallerWithControlTimeSeries(CausalImpactRequest causalImpactRequest, long preDays, long postDays) {
		RCaller rCaller = RCaller.create();
		RCode code = RCode.create();
		code.addRCode("library(CausalImpact)");
		final double[] y = causalImpactRequest.getTest_time_series();
		code.addDoubleArray("y", y);

		for (int i = 0; i < causalImpactRequest.getControl_time_series_list().size(); i++) {
			code.addDoubleArray("x" + (i + 1), causalImpactRequest.getControl_time_series_list().get(i));
		}

		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("data <- zoo(cbind(");
		stringBuilder.append("y");
		for (int i = 0; i < causalImpactRequest.getControl_time_series_list().size(); i++) {
			stringBuilder.append(IConstants.COMMA);
			stringBuilder.append("x" + (i + 1));
		}
		stringBuilder.append("), 1:length(y))");
		code.addRCode(stringBuilder.toString());

		code.addRCode("pre.period <- c(1, " + preDays + ")");
		code.addRCode("post.period <- c(" + (preDays + 1) + ", " + (preDays + postDays) + ")");
		code.addRCode("impact <- CausalImpact(data, pre.period, post.period)");

		rCaller.setRCode(code);

		return rCaller;
	}

	public double[] extractTimeSeries(int dataPointsPosition, Long totalPreAndPostPeriodDays, String[] inputStringArray) {
		int startPosition = dataPointsPosition * totalPreAndPostPeriodDays.intValue();
		int endPosition = startPosition + totalPreAndPostPeriodDays.intValue() - 1;
		return FormatUtils.getInstance().extractDoubleArray(startPosition, endPosition, inputStringArray);
	}

	private MarketMatchingResponse marketMatching(MarketMatchingRequest marketMatchingRequest) throws Exception {
		if (isDebug == true) {
			FormatUtils.getInstance().logMemoryUsage("marketMatching() begins. requestJson=" + new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class));
		}
		long startTimestamp = System.currentTimeMillis();
		long testStartTimestamp = 0L;
		MarketMatchingResponse marketMatchingResponse = null;
		RCaller rCaller = null;
		RCode code = null;
		String[] testStringArray = null;
		Long totalPreAndPostPeriodDays = null;
		Object[][] objects = null;
		String[] names = null;
		DataFrame dataFrame = null;
		List<BestMatch> bestMatchList = new ArrayList<BestMatch>();
		BestMatch bestMatch = null;
		List<String> areaList = new ArrayList<String>();
		List<String> bestControlList = new ArrayList<String>();
		List<Double> relativeDistanceList = new ArrayList<Double>();
		List<Double> correlationList = new ArrayList<Double>();
		List<Double> lengthList = new ArrayList<Double>();
		List<Double> sumTestList = new ArrayList<Double>();
		List<Double> sumCntlList = new ArrayList<Double>();
		List<Double> rawDistList = new ArrayList<Double>();
		List<Double> correlationOfLogsList = new ArrayList<Double>();
		List<Double> rankList = new ArrayList<Double>();
		List<Double> normDistList = new ArrayList<Double>();
		Inference inference = new Inference();
		Coefficients coefficients = new Coefficients();
		List<String> marketList = new ArrayList<String>();
		List<Double> averageBetaList = new ArrayList<Double>();
		CausalImpactObject causalImpactObject = new CausalImpactObject();

		try {
			marketMatchingResponse = new MarketMatchingResponse();
			marketMatchingResponse.setSuccess(true);
			marketMatchingResponse.setTest_market(marketMatchingRequest.getTest_market());
			marketMatchingResponse.setPre_period_start_date(marketMatchingRequest.getPre_period_start_date());
			marketMatchingResponse.setPre_period_end_date(marketMatchingRequest.getPre_period_end_date());
			marketMatchingResponse.setPost_period_start_date(marketMatchingRequest.getPost_period_start_date());
			marketMatchingResponse.setPost_period_end_date(marketMatchingRequest.getPost_period_end_date());
			marketMatchingResponse.setNumber_of_best_matches(marketMatchingRequest.getNumber_of_best_matches());

			totalPreAndPostPeriodDays = ChronoUnit.DAYS.between(LocalDate.parse(marketMatchingRequest.getPre_period_start_date()),
					LocalDate.parse(marketMatchingRequest.getPost_period_end_date())) + IConstants.LONG_ONE;

			rCaller = RCaller.create();
			code = RCode.create();
			code.addRCode("library(MarketMatching)");
			code.addRCode("library(magrittr)");
			code.addRCode("library(tidyverse)");
			objects = new Object[][] { getAreaArray(marketMatchingRequest), getDateArray(marketMatchingRequest), getValueArray(marketMatchingRequest) };
			names = new String[] { "Area", "Date", "Value" };
			dataFrame = DataFrame.create(objects, names);
			code.addDataFrame("dataframe", dataFrame);
			code.addRCode("dataframe <- dataframe %>% ");
			code.addRCode(" mutate(Date = as.Date(Date))");
			code.addRCode("mm <- MarketMatching::best_matches(data=dataframe,id_variable=\"Area\",markets_to_be_matched=c(\"" + marketMatchingRequest.getTest_market()
					+ "\"),date_variable=\"Date\",matching_variable=\"Value\",parallel=TRUE,warping_limit=1,dtw_emphasis=1,matches="
					+ marketMatchingRequest.getNumber_of_best_matches() + ",start_match_period=\"" + marketMatchingRequest.getPre_period_start_date()
					+ "\",end_match_period=\"" + marketMatchingRequest.getPre_period_end_date() + "\")");
			code.addRCode("results <- MarketMatching::inference(matched_markets = mm,test_market = \"" + marketMatchingRequest.getTest_market() + "\")");
			rCaller.setRCode(code);

			// mm$BestMatches
			testStartTimestamp = System.currentTimeMillis();
			rCaller.runAndReturnResultOnline("mm$BestMatches");
			if (isDebug == true) {
				FormatUtils.getInstance()
						.logMemoryUsage("marketMatching() mm$BestMatches rCaller.getParser().getNames().size()=" + rCaller.getParser().getNames().size());
			}
			for (String name : rCaller.getParser().getNames()) {
				if (isDebug == true) {
					FormatUtils.getInstance().logMemoryUsage("marketMatching() mm$BestMatches name=" + name);
				}
				testStringArray = rCaller.getParser().getAsStringArray(name);
				for (String value : testStringArray) {
					if (StringUtils.equalsIgnoreCase(name, IConstants.AREA)) {
						areaList.add(value);
					} else if (StringUtils.equalsIgnoreCase(name, IConstants.BEST_CONTROL)) {
						bestControlList.add(value);
					} else if (StringUtils.equalsIgnoreCase(name, IConstants.RELATIVE_DISTANCE)) {
						relativeDistanceList.add(new Double(value));
					} else if (StringUtils.equalsIgnoreCase(name, IConstants.CORRELATION)) {
						correlationList.add(new Double(value));
					} else if (StringUtils.equalsIgnoreCase(name, IConstants.LENGTH)) {
						lengthList.add(new Double(value));
					} else if (StringUtils.equalsIgnoreCase(name, IConstants.SUM_TEST)) {
						sumTestList.add(new Double(value));
					} else if (StringUtils.equalsIgnoreCase(name, IConstants.SUM_CNTL)) {
						sumCntlList.add(new Double(value));
					} else if (StringUtils.equalsIgnoreCase(name, IConstants.RAW_DIST)) {
						rawDistList.add(new Double(value));
					} else if (StringUtils.equalsIgnoreCase(name, IConstants.CORRELATION_OF_LOGS)) {
						correlationOfLogsList.add(new Double(value));
					} else if (StringUtils.equalsIgnoreCase(name, IConstants.RANK)) {
						rankList.add(new Double(value));
					} else if (StringUtils.equalsIgnoreCase(name, IConstants.NORM_DIST)) {
						normDistList.add(new Double(value));
					}
				}
			}
			for (int i = 0; i < areaList.size(); i++) {
				bestMatch = new BestMatch();
				bestMatch.setArea(areaList.get(i));
				bestMatch.setBest_control(bestControlList.get(i));
				bestMatch.setRelative_distance(relativeDistanceList.get(i));
				bestMatch.setCorrelation(correlationList.get(i));
				bestMatch.setLength(lengthList.get(i));
				bestMatch.setSum_test(sumTestList.get(i));
				bestMatch.setSum_cntl(sumCntlList.get(i));
				bestMatch.setRaw_dist(rawDistList.get(i));
				bestMatch.setCorrelation_of_logs(correlationOfLogsList.get(i));
				bestMatch.setRank(rankList.get(i));
				bestMatch.setNorm_dist(normDistList.get(i));
				bestMatchList.add(bestMatch);
			}
			marketMatchingResponse.setBest_match_array(bestMatchList.toArray(new BestMatch[0]));
			if (isDebug == true) {
				FormatUtils.getInstance().logMemoryUsage("marketMatching() mm$BestMatches elapsed(ms.)=" + (System.currentTimeMillis() - testStartTimestamp));
			}

			// results
			testStartTimestamp = System.currentTimeMillis();
			rCaller.runAndReturnResultOnline("results");
			if (isDebug == true) {
				FormatUtils.getInstance().logMemoryUsage("marketMatching() results rCaller.getParser().getNames().size()=" + rCaller.getParser().getNames().size());
			}
			for (String name : rCaller.getParser().getNames()) {
				if (isDebug == true) {
					FormatUtils.getInstance().logMemoryUsage("marketMatching() results name=" + name);
				}
				if (StringUtils.equalsIgnoreCase(name, IConstants.TEST_DATA) == false && StringUtils.equalsIgnoreCase(name, IConstants.ZOO_DATA) == false
						&& StringUtils.equalsIgnoreCase(name, IConstants.TEST_NAME) == false && StringUtils.equalsIgnoreCase(name, IConstants.CONTROL_NAME) == false) {
					testStringArray = rCaller.getParser().getAsStringArray(name);
					for (String value : testStringArray) {
						if (StringUtils.equalsIgnoreCase(name, IConstants.ABSOLUTE_EFFECT)) {
							inference.setAbsolute_effect(new Double(value));
						} else if (StringUtils.equalsIgnoreCase(name, IConstants.ABSOLUTE_EFFECT_LOWER)) {
							inference.setAbsolute_effect_lower(new Double(value));
						} else if (StringUtils.equalsIgnoreCase(name, IConstants.ABSOLUTE_EFFECT_UPPER)) {
							inference.setAbsolute_effect_upper(new Double(value));
						} else if (StringUtils.equalsIgnoreCase(name, IConstants.RELATIVE_EFFECT)) {
							inference.setRelative_effect(new Double(value));
						} else if (StringUtils.equalsIgnoreCase(name, IConstants.RELATIVE_EFFECT_LOWER)) {
							inference.setRelative_effect_lower(new Double(value));
						} else if (StringUtils.equalsIgnoreCase(name, IConstants.RELATIVE_EFFECT_UPPER)) {
							inference.setRelative_effect_upper(new Double(value));
						} else if (StringUtils.equalsIgnoreCase(name, IConstants.TAIL_PROB)) {
							inference.setTail_prob(new Double(value));
						} else if (StringUtils.equalsIgnoreCase(name, IConstants.PRE_PERIOD_MAPE)) {
							inference.setPre_period_mape(new Double(value));
						} else if (StringUtils.equalsIgnoreCase(name, IConstants.DW)) {
							inference.setDw(new Double(value));
						}
					}
				}
			}
			marketMatchingResponse.setInference(inference);
			if (isDebug == true) {
				FormatUtils.getInstance().logMemoryUsage("marketMatching() results elapsed(ms.)=" + (System.currentTimeMillis() - testStartTimestamp));
			}

			// results$CausalImpactObject
			testStartTimestamp = System.currentTimeMillis();
			rCaller.runAndReturnResultOnline("results$CausalImpactObject");
			if (isDebug == true) {
				FormatUtils.getInstance()
						.logMemoryUsage("marketMatching() results$CausalImpactObject rCaller.getParser().getNames().size()=" + rCaller.getParser().getNames().size());
			}
			for (String name : rCaller.getParser().getNames()) {
				if (isDebug == true) {
					FormatUtils.getInstance().logMemoryUsage("marketMatching() results$CausalImpactObject name=" + name);
				}
				testStringArray = rCaller.getParser().getAsStringArray(name);
				for (String value : testStringArray) {
					if (StringUtils.equalsIgnoreCase(name, IConstants.REPORT)) {
						causalImpactObject.setReport(value);
					} else if (StringUtils.equalsIgnoreCase(name, IConstants.SERIES)) {
						causalImpactObject.setTest_time_series(
								RUtils.getInstance().extractTimeSeries(IConstants.TEST_DATA_POINTS_POSITION, totalPreAndPostPeriodDays, testStringArray));
						causalImpactObject.setCounter_factual_prediction_time_series(RUtils.getInstance()
								.extractTimeSeries(IConstants.COUNTER_FACTUAL_PREDICTION_DATA_POINTS_POSITION, totalPreAndPostPeriodDays, testStringArray));
						causalImpactObject.setCounter_factual_prediction_time_series_lower(RUtils.getInstance()
								.extractTimeSeries(IConstants.COUNTER_FACTUAL_PREDICTION_DATA_POINTS_POSITION_LOWER, totalPreAndPostPeriodDays, testStringArray));
						causalImpactObject.setCounter_factual_prediction_time_series_upper(RUtils.getInstance()
								.extractTimeSeries(IConstants.COUNTER_FACTUAL_PREDICTION_DATA_POINTS_POSITION_UPPER, totalPreAndPostPeriodDays, testStringArray));
						causalImpactObject.setPointwise_causal_effect_time_series(RUtils.getInstance()
								.extractTimeSeries(IConstants.POINTWISE_CAUSAL_EFFECT_DATA_POINTS_POSITION, totalPreAndPostPeriodDays, testStringArray));
						causalImpactObject.setPointwise_causal_effect_time_series_lower(RUtils.getInstance()
								.extractTimeSeries(IConstants.POINTWISE_CAUSAL_EFFECT_DATA_POINTS_POSITION_LOWER, totalPreAndPostPeriodDays, testStringArray));
						causalImpactObject.setPointwise_causal_effect_time_series_upper(RUtils.getInstance()
								.extractTimeSeries(IConstants.POINTWISE_CAUSAL_EFFECT_DATA_POINTS_POSITION_UPPER, totalPreAndPostPeriodDays, testStringArray));
						causalImpactObject.setCumulative_effect_time_series(
								RUtils.getInstance().extractTimeSeries(IConstants.CUMULATIVE_EFFECT_DATA_POINTS_POSITION, totalPreAndPostPeriodDays, testStringArray));
						causalImpactObject.setCumulative_effect_time_series_lower(RUtils.getInstance()
								.extractTimeSeries(IConstants.CUMULATIVE_EFFECT_DATA_POINTS_POSITION_LOWER, totalPreAndPostPeriodDays, testStringArray));
						causalImpactObject.setCumulative_effect_time_series_upper(RUtils.getInstance()
								.extractTimeSeries(IConstants.CUMULATIVE_EFFECT_DATA_POINTS_POSITION_UPPER, totalPreAndPostPeriodDays, testStringArray));
					}
				}
			}
			marketMatchingResponse.setCausal_impact_object(causalImpactObject);
			if (isDebug == true) {
				FormatUtils.getInstance()
						.logMemoryUsage("marketMatching() results$CausalImpactObject elapsed(ms.)=" + (System.currentTimeMillis() - testStartTimestamp));
			}

			// results$Coefficients
			testStartTimestamp = System.currentTimeMillis();
			rCaller.runAndReturnResultOnline("results$Coefficients");
			if (isDebug == true) {
				FormatUtils.getInstance()
						.logMemoryUsage("marketMatching() results$Coefficients rCaller.getParser().getNames().size()=" + rCaller.getParser().getNames().size());
			}
			for (String name : rCaller.getParser().getNames()) {
				if (isDebug == true) {
					FormatUtils.getInstance().logMemoryUsage("marketMatching() results$Coefficients name=" + name);
				}
				testStringArray = rCaller.getParser().getAsStringArray(name);
				for (String value : testStringArray) {
					if (StringUtils.equalsIgnoreCase(name, IConstants.MARKET)) {
						marketList.add(value);
					} else if (StringUtils.equalsIgnoreCase(name, IConstants.AVERAGE_BETA)) {
						averageBetaList.add(new Double(value));
					}
				}
			}
			coefficients.setMarket_array(marketList.toArray(new String[0]));
			coefficients.setAverage_beta(averageBetaList.toArray(new Double[0]));
			marketMatchingResponse.setCoefficients(coefficients);
			if (isDebug == true) {
				FormatUtils.getInstance().logMemoryUsage("marketMatching() results$Coefficients elapsed(ms.)=" + (System.currentTimeMillis() - testStartTimestamp));
			}
		} catch (Exception e) {
			e.printStackTrace();

			marketMatchingResponse = new MarketMatchingResponse();
			marketMatchingResponse.setSuccess(false);
			WebServiceError webServiceError = new WebServiceError();
			webServiceError.setError_code(IConstants.MSG_CD_CAUSAL_IMPACT_WEB_SERVICE_METHOD_EXCEPTION);
			webServiceError.setError_message("Data cannot be analyzed.");
			marketMatchingResponse.setError(webServiceError);

			StringWriter stringWriter = new StringWriter();
			e.printStackTrace(new PrintWriter(stringWriter));

			// send support alert
			String message = stringWriter.toString();
			String requestParameters = new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class);
		} finally {
			if (rCaller != null) {
				rCaller.stopRCallerOnline();
			}
		}
		if (isDebug == true) {
			FormatUtils.getInstance().logMemoryUsage("marketMatching() responseJson=" + new Gson().toJson(marketMatchingResponse, MarketMatchingResponse.class));
			FormatUtils.getInstance().logMemoryUsage("marketMatching() ends. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		}
		return marketMatchingResponse;
	}

	public String[] getAreaArray(MarketMatchingRequest marketMatchingRequest) {
		String[] areaArray = null;
		List<String> stringList = new ArrayList<String>();
		int totalLength = marketMatchingRequest.getArea_date_value_array().length;
		if (totalLength > 0) {
			for (int i = 0; i < totalLength; i++) {
				if (marketMatchingRequest.getArea_date_value_array()[i] != null
						&& StringUtils.isNotBlank(marketMatchingRequest.getArea_date_value_array()[i].getArea())) {
					stringList.add(marketMatchingRequest.getArea_date_value_array()[i].getArea());
				}
			}
		}
		if (stringList != null && stringList.size() > 0) {
			areaArray = stringList.toArray(new String[0]);
		}
		return areaArray;
	}

	public String[] getDateArray(MarketMatchingRequest marketMatchingRequest) {
		String[] dateArray = null;
		List<String> stringList = new ArrayList<String>();
		int totalLength = marketMatchingRequest.getArea_date_value_array().length;
		if (totalLength > 0) {
			for (int i = 0; i < totalLength; i++) {
				if (marketMatchingRequest.getArea_date_value_array()[i] != null
						&& StringUtils.isNotBlank(marketMatchingRequest.getArea_date_value_array()[i].getDate())) {
					stringList.add(marketMatchingRequest.getArea_date_value_array()[i].getDate());
				}
			}
		}
		if (stringList != null && stringList.size() > 0) {
			dateArray = stringList.toArray(new String[0]);
		}
		return dateArray;
	}

	public Double[] getValueArray(MarketMatchingRequest marketMatchingRequest) {
		Double[] doubleArray = null;
		List<Double> doubleList = new ArrayList<Double>();
		int totalLength = marketMatchingRequest.getArea_date_value_array().length;
		if (totalLength > 0) {
			for (int i = 0; i < totalLength; i++) {
				if (marketMatchingRequest.getArea_date_value_array()[i] != null && marketMatchingRequest.getArea_date_value_array()[i].getValue() != null) {
					doubleList.add(marketMatchingRequest.getArea_date_value_array()[i].getValue());
				}
			}
		}
		if (doubleList != null && doubleList.size() > 0) {
			doubleArray = doubleList.toArray(new Double[0]);
		}
		return doubleArray;
	}
}
