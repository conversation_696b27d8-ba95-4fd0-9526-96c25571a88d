package com.actonia.utils;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Collection;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.util.JSONPObject;

// https://www.wrike.com/open.htm?id=40580950
// by floyd
public class JsonMapper {

	private ObjectMapper mapper;

	public JsonMapper() {
		this(false);
	}

	public JsonMapper(boolean originalMode) {
		mapper = new ObjectMapper();
		if (!originalMode) {
			mapper.setDateFormat(new SimpleDateFormat("MM/dd/yyyy"));
			mapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
			mapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
		}
	}

	public String toJson(Object object) {

		try {
			return mapper.writeValueAsString(object);
		} catch (IOException e) {
			e.printStackTrace();
			return null;
		}
	}
	
	public String toJson(Object object, String defaultValue) { 
		String json = toJson(object);
		if (StringUtils.isBlank(json)) {
			return defaultValue;
		}
		return json;
	}

	public <T> T fromJson(String jsonString, Class<T> clazz) {
		if (StringUtils.isBlank(jsonString)) {
			return null;
		}

		try {
			return mapper.readValue(jsonString, clazz);
		} catch (IOException e) {
			e.printStackTrace();
			return null;
		}
	}

	public <T> T fromJson(String jsonString, JavaType javaType) {
		if (StringUtils.isBlank(jsonString)) {
			return null;
		}

		try {
			return (T) mapper.readValue(jsonString, javaType);
		} catch (IOException e) {
			e.printStackTrace();
			return null;
		}
	}

	public JavaType contructCollectionType(
			Class<? extends Collection> collectionClass, Class<?> elementClass) {
		return mapper.getTypeFactory().constructCollectionType(collectionClass,
				elementClass);
	}

	public JavaType contructMapType(Class<? extends Map> mapClass,
			Class<?> keyClass, Class<?> valueClass) {
		return mapper.getTypeFactory().constructMapType(mapClass, keyClass,
				valueClass);
	}

	public void update(String jsonString, Object object) {
		try {
			mapper.readerForUpdating(object).readValue(jsonString);
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	public String toJsonP(String functionName, Object object) {
		return toJson(new JSONPObject(functionName, object));
	}

	public ObjectMapper getMapper() {
		return mapper;
	}
}
