package com.actonia.utils;

import java.net.URL;
import java.net.URLDecoder;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.text.StrTokenizer;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.web.util.UriUtils;

import com.actonia.IConstants;
import com.actonia.value.object.DomainIdLanguageCodeValueObject;
import com.google.common.collect.ImmutableList;
import com.google.common.net.InternetDomainName;
import com.google.gson.Gson;

public class FormatUtils {

	private static final String DATE_PATTERN_1 = "MM/dd/yyyy";

	private static final String DATE_FORMAT_YYYYMMDD = "yyyyMMdd";

	private static final Pattern PATTERN_ENCODE_TEXT_END_PERCENT = Pattern.compile("%\\w?$");

	private static final String SLASH = "/";

	private static final String HTTP_SLASH_SLASH = "http://";

	private static final String HTTPS_SLASH_SLASH = "https://";

	private static final String WWW_DOT = "www.";

	private static final String DOT = ".";

	private static Pattern pattern = null;

	private static final String URL_1ST_SEGMENT_REG_EXP_PATTERN = "www\\d+\\.";

	private static FormatUtils formatUtils;

	private static final String CARRIAGE_RETURN = "\r";
	private static final String NEWLINE = "\n";
	private static final String NO_BREAK_SPACE = "\u00A0";
	private static final String TAB = "\t";
	private static final String ANY_SPACES = " +";
	private static final String ONE_SPACE = " ";

	// map key = month number
	// map value = last day of the month
	private Map<Integer, Integer> monthLastDayMap;

	private FormatUtils() {
		super();
		if (monthLastDayMap == null) {
			monthLastDayMap = new HashMap<Integer, Integer>();
			monthLastDayMap.put(1, 31);
			monthLastDayMap.put(2, 28);
			monthLastDayMap.put(3, 31);
			monthLastDayMap.put(4, 30);
			monthLastDayMap.put(5, 31);
			monthLastDayMap.put(6, 30);
			monthLastDayMap.put(7, 31);
			monthLastDayMap.put(8, 31);
			monthLastDayMap.put(9, 30);
			monthLastDayMap.put(10, 31);
			monthLastDayMap.put(11, 30);
			monthLastDayMap.put(12, 31);
		}
	}

	public static FormatUtils getInstance() {
		if (formatUtils == null) {
			formatUtils = new FormatUtils();
		}
		return formatUtils;
	}

	public void logMemoryUsage(String logText) {
		System.out.println(logText + ",memory(free/max/total)=" + String.valueOf(NumberFormat.getIntegerInstance().format(Runtime.getRuntime().freeMemory())) + " / "
				+ String.valueOf(NumberFormat.getIntegerInstance().format(Runtime.getRuntime().maxMemory())) + " / "
				+ String.valueOf(NumberFormat.getIntegerInstance().format(Runtime.getRuntime().totalMemory())) + " at " + new Date());
	}

	public String formatDate(Date date) {
		if (date == null) {
			return "";
		}
		return DateFormatUtils.format(date, DATE_PATTERN_1);
	}

	public String formatDate(Date date, String pattern) {
		if (date == null || StringUtils.isBlank(pattern)) {
			return "";
		}
		return DateFormatUtils.format(date, pattern);
	}

	public int formatDateToYyyyMmDd(Date date) {
		if (date == null) {
			return 0;
		}
		return NumberUtils.toInt(formatDate(date, DATE_FORMAT_YYYYMMDD));
	}

	public int getDayOfWeekNumber(Date date) {
		String dayOfWeek = DateFormatUtils.format(date, "EEE", Locale.US);
		if (StringUtils.endsWithIgnoreCase(dayOfWeek, "Mon")) {
			return 1;
		} else if (StringUtils.endsWithIgnoreCase(dayOfWeek, "Tue")) {
			return 2;
		} else if (StringUtils.endsWithIgnoreCase(dayOfWeek, "Wed")) {
			return 3;
		} else if (StringUtils.endsWithIgnoreCase(dayOfWeek, "Thu")) {
			return 4;
		} else if (StringUtils.endsWithIgnoreCase(dayOfWeek, "Fri")) {
			return 5;
		} else if (StringUtils.endsWithIgnoreCase(dayOfWeek, "Sat")) {
			return 6;
		} else {
			return 7;
		}
	}

	public String getQueryParamString(Set<String> input) {
		if (input == null || input.isEmpty()) {
			return null;
		}
		StringBuilder stringBuilder = new StringBuilder();
		for (String tempString : input) {
			if (StringUtils.isNotBlank(tempString)) {
				stringBuilder.append("'").append(tempString).append("',");
			}
		}
		return StringUtils.removeEndIgnoreCase(stringBuilder.toString(), ",");
	}

	public String getQueryParamString(List<String> input) {
		if (input == null || input.isEmpty()) {
			return null;
		}
		StringBuilder stringBuilder = new StringBuilder();
		for (String tempString : input) {
			if (StringUtils.isNotBlank(tempString)) {
				stringBuilder.append("'").append(tempString).append("',");
			}
		}
		return StringUtils.removeEndIgnoreCase(stringBuilder.toString(), ",");
	}

	public String[] parseCsvLine(String csvLine, String delimiter) {
		if (StringUtils.isBlank(csvLine)) {
			return null;
		}
		StrTokenizer st = StrTokenizer.getCSVInstance(csvLine);
		if (StringUtils.isNotBlank(delimiter)) {
			//default is Comma
			st.setDelimiterString(delimiter);
		}
		return st.getTokenArray();
	}

	public String decodeAndEscapeHtml(String text) {
		if (StringUtils.isBlank(text)) {
			return "";
		}
		try {
			//https://www.wrike.com/open.htm?id=17553058
			Matcher matcher = PATTERN_ENCODE_TEXT_END_PERCENT.matcher(text);
			if (matcher.find()) {
				text = matcher.replaceFirst("");
			}

			//https://www.wrike.com/open.htm?id=5004372
			//https://www.wrike.com/open.htm?id=11244354
			return StringEscapeUtils.unescapeXml(URLDecoder.decode(text, "UTF-8"));
			//			return StringEscapeUtils.escapeHtml(URLDecoder.decode(value, "UTF-8"));
		} catch (Exception e) {
			e.printStackTrace();
		}
		return text;
	}

	public String formatElapsedTime(long seconds) {
		long day = TimeUnit.SECONDS.toDays(seconds);
		long hours = TimeUnit.SECONDS.toHours(seconds) - TimeUnit.DAYS.toHours(day);
		long minute = TimeUnit.SECONDS.toMinutes(seconds) - TimeUnit.HOURS.toMinutes(TimeUnit.SECONDS.toHours(seconds));
		long second = TimeUnit.SECONDS.toSeconds(seconds) - TimeUnit.MINUTES.toSeconds(TimeUnit.SECONDS.toMinutes(seconds));
		String formattedElapsedTime = day + "d" + hours + "h" + minute + "m" + second + "s";
		return formattedElapsedTime;
	}

	/**
	 * will return a valid url, otherwise null
	 *
	 * @param url
	 * @return
	 */
	public String formatUrl(String url) {
		url = StringUtils.trim(url);
		if (StringUtils.isBlank(url)) {
			return null;
		}

		if (StringUtils.endsWith(url, "?")) {
			url = StringUtils.removeEnd(url, "?");
		}

		if (StringUtils.endsWith(url, "/")) {
			url = StringUtils.removeEnd(url, "/");
		}

		if (isURL(url)) {
			return url;
		}

		if (StringUtils.startsWithIgnoreCase(url, "http://") || StringUtils.startsWithIgnoreCase(url, "https://")) {
			// some url is http://http://www.xyz.com
			url = StringUtils.removeStart(url, "http://");
			if (isURL(url)) {
				return url;
			}
			return null;
		}

		url = "http://" + url;
		if (isURL(url)) {
			return url;
		}
		return null;
	}

	/**
	 * RegularEx for url format
	 */
	private static final String URL_REGULAREX = "^((https|http|ftp|rtsp|mms)://)" + "(([0-9a-z_!~*'().&=+$%-]+: )?[0-9a-z_!~*'().&=+$%-]+@)?"
			+ "(([0-9]{1,3}\\.){3}[0-9]{1,3}" // ip
			+ "|" + "([0-9a-z_!~*'()-]+\\.)*" // domain www.
			+ "([0-9a-z~][0-9a-z-~]{0,61})?[0-9a-z]\\." // second level domain
			+ "[a-z]{2,6})" // first level domain
			+ "(:[0-9]{1,4})?" // port
			+ "((/?)|" + "(/[0-9a-z_!~*'().;?:@&=+$,%#-]+)+/?)$";

	/**
	 * check if the url string is a right format
	 *
	 * @return
	 */
	public boolean isURL(String url) {
		if (StringUtils.isBlank(url)) {
			return false;
		}
		return url.toLowerCase().matches(URL_REGULAREX);
	}

	public String getMatchingUrl(String targetUrl, String gaDefaultPage) {
		String gaDefaultPageLowerCase = null;
		int firstDotPos = 0;
		String firstSement = null;
		Matcher matcher = null;
		boolean result = false;

		String matchingUrl = null;

		if (targetUrl != null) {
			if (gaDefaultPage != null && gaDefaultPage.trim().length() > 0) {
				gaDefaultPageLowerCase = gaDefaultPage.toLowerCase();
			}

			matchingUrl = targetUrl.toLowerCase();

			if (gaDefaultPageLowerCase != null && gaDefaultPageLowerCase.trim().length() > 0 && matchingUrl.endsWith(gaDefaultPageLowerCase)) {
				matchingUrl = StringUtils.removeEndIgnoreCase(matchingUrl, gaDefaultPageLowerCase);
			}

			if (matchingUrl.endsWith(SLASH)) {
				matchingUrl = StringUtils.removeEndIgnoreCase(matchingUrl, SLASH);
			}

			if (StringUtils.contains(matchingUrl, HTTP_SLASH_SLASH)) {
				matchingUrl = StringUtils.substringAfter(matchingUrl, HTTP_SLASH_SLASH);
			}

			if (StringUtils.contains(matchingUrl, HTTPS_SLASH_SLASH)) {
				matchingUrl = StringUtils.substringAfter(matchingUrl, HTTPS_SLASH_SLASH);
			}

			if (StringUtils.contains(matchingUrl, WWW_DOT)) {
				matchingUrl = StringUtils.substringAfter(matchingUrl, WWW_DOT);
			} else {
				firstDotPos = matchingUrl.indexOf(DOT);
				firstSement = matchingUrl.substring(0, (firstDotPos + 1));
				if (pattern == null) {
					pattern = Pattern.compile(URL_1ST_SEGMENT_REG_EXP_PATTERN);
				}
				matcher = pattern.matcher(firstSement);
				result = matcher.matches();
				if (result == true) {
					matchingUrl = StringUtils.substringAfter(matchingUrl, firstSement);
				}
			}
		}

		return matchingUrl;
	}

	public String reverseDomainNameByDot(String arg) {
		if (StringUtils.isBlank(arg)) {
			return "";
		}
		String domainName = StringUtils.reverseDelimited(arg, IConstants.DOT_CHAR);
		return domainName;
	}

	public Integer calculateWeekOfYear(Date inputDate) {
		Integer response = null;
		try {
			SimpleDateFormat simpleDateFormat = new SimpleDateFormat(IConstants.DATE_FORMAT_YYYYWW);
			String weekInYearString = simpleDateFormat.format(DateUtils.truncate(inputDate, Calendar.DAY_OF_MONTH));
			if (weekInYearString != null) {
				response = new Integer(weekInYearString);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return response;
	}

	public String getReversedDomainName(String domainNameInput) {
		String domainName = null;
		String reversedDomainName = null;
		URL url = null;

		try {
			url = new URL(domainNameInput);
			domainName = url.getHost();
		} catch (Exception e) {
			domainName = domainNameInput;
		}
		String testString = StringUtils.substringBefore(domainName, IConstants.SLASH);
		try {
			if (StringUtils.isNotBlank(testString)) {
				reversedDomainName = StringUtils.reverseDelimited(testString, IConstants.DOT_STRING.charAt(0));
			} else {
				reversedDomainName = IConstants.EMPTY_STRING;
			}
		} catch (Exception e) {
			//e.printStackTrace();
			//FormatUtils.logMemoryUsage("getReversedDomainName() domainName=" + domainName + ",exception message=" + e.getMessage());
		}
		return reversedDomainName;
	}

	public String getReversedRootDomainName(String domainNameInput) {
		String domainName = null;
		String reversedRootDomainName = null;
		String rootDomainName = null;
		String reversedDomainName = null;
		String testString = null;
		ImmutableList<String> immutableList = null;
		int totalDomainNameSegments = 0;
		URL url = null;

		try {
			url = new URL(domainNameInput);
			domainName = url.getHost();
		} catch (Exception e) {
			domainName = domainNameInput;
		}

		testString = StringUtils.substringBefore(domainName, IConstants.SLASH);
		try {
			if (StringUtils.isNotBlank(testString)) {
				rootDomainName = InternetDomainName.from(testString).topPrivateDomain().toString();
				reversedRootDomainName = getReversedDomainName(rootDomainName);
			} else {
				reversedRootDomainName = IConstants.EMPTY_STRING;
			}
		}
		// when exception, return a null reversedRootDomainName
		catch (Exception e) {
			immutableList = InternetDomainName.from(testString).parts();
			if (immutableList != null && immutableList.size() > 0) {
				totalDomainNameSegments = immutableList.size();
				reversedDomainName = getReversedDomainName(testString);
				if (totalDomainNameSegments == 3) {
					if (StringUtils.endsWithIgnoreCase(reversedDomainName, IConstants.DOT_WWW)) {
						reversedRootDomainName = StringUtils.removeEnd(reversedDomainName, IConstants.DOT_WWW);
					} else {
						reversedRootDomainName = StringUtils.substringBeforeLast(reversedDomainName, IConstants.DOT_STRING);
					}
				} else if (totalDomainNameSegments == 2) {
					reversedRootDomainName = reversedDomainName;
				}
			}
		}
		return reversedRootDomainName;
	}

	public String getDomainIdLanguageCodeJson(DomainIdLanguageCodeValueObject[] domainIdLanguageCodeValueObjectArray) {
		String domainIdLanguageCodeJson = IConstants.EMPTY_STRING;
		if (domainIdLanguageCodeValueObjectArray != null && domainIdLanguageCodeValueObjectArray.length > 0) {
			domainIdLanguageCodeJson = new Gson().toJson(domainIdLanguageCodeValueObjectArray, DomainIdLanguageCodeValueObject[].class);
		}
		return domainIdLanguageCodeJson;
	}

	//    public static String[] getStartEndCrawlTime(int domainId) {
	//    	String[] crawlTimeArray = null;
	//    	if (domainIdStartCrawlTimeStringMap == null || domainIdEndCrawlTimeStringMap == null) {
	//    		domainIdStartCrawlTimeStringMap = new HashMap<Integer, String>();
	//    		domainIdStartCrawlTimeStringMap.put(6683, "02:00:00");
	//    		domainIdEndCrawlTimeStringMap = new HashMap<Integer, String>();
	//    		domainIdEndCrawlTimeStringMap.put(6683, "07:00:00");
	//    	}
	//    	if (domainIdStartCrawlTimeStringMap.containsKey(domainId) && domainIdEndCrawlTimeStringMap.containsKey(domainId)) {
	//    		crawlTimeArray = new String[2];
	//    		crawlTimeArray[0] = domainIdStartCrawlTimeStringMap.get(domainId);
	//    		crawlTimeArray[1] = domainIdEndCrawlTimeStringMap.get(domainId);
	//    	}
	//		return crawlTimeArray;
	//    }

	public boolean isOwnDomainLink(String linkUrl, String ownDomain) {
		return isSameDomain(linkUrl, ownDomain);
	}

	private boolean isSameDomain(String sourceDomain, String ownDomain) {
		sourceDomain = getDomainByUrl(sourceDomain, true);
		ownDomain = getDomainByUrl(ownDomain, true);
		if (StringUtils.equalsIgnoreCase(sourceDomain, ownDomain)) {
			return true;
		}
		if (StringUtils.endsWithIgnoreCase(sourceDomain, "." + ownDomain)) {
			return true;
		}
		return false;
	}

	private String getDomainByUrl(String url, boolean remove3w) {
		String result = url;

		// remove 'http://'
		if (StringUtils.contains(result, "://")) {
			result = StringUtils.substringAfter(result, "://");
		}

		// remove the string after domain
		result = StringUtils.substringBefore(result, "/");

		// remove 'www.' if the domain string contains it
		if (remove3w && StringUtils.isNotBlank(result) && result.startsWith("www.")) {
			result = StringUtils.substringAfter(result, "www.");
		}

		return result;
	}

	public String trimText(final String inputText) {
		String trimmedText = inputText;
		if (StringUtils.isNotBlank(inputText)) {
			trimmedText = StringUtils.trim(inputText.replaceAll(CARRIAGE_RETURN, ONE_SPACE).replaceAll(NEWLINE, ONE_SPACE).replaceAll(TAB, ONE_SPACE)
					.replaceAll(NO_BREAK_SPACE, ONE_SPACE).replaceAll(ANY_SPACES, ONE_SPACE));
		}
		return trimmedText;
	}

	public int calculateYearWeekBasedOnStartDayOfWeek(Date dateToBeChecked, int startDayOfWeek) {
		int yearWeek = 0;
		Date testDate = null;
		boolean isFirstIteration = true;
		int currentYear = 0;
		int finalWeekNumber = 0;
		try {
			testDate = dateToBeChecked;
			Calendar calendar = new GregorianCalendar();
			calendar.setTime(testDate);
			int testWeekNumber = 0;
			Date startDateOfWeek = null;
			Date endDateOfWeek = null;
			currentYear = calendar.get(Calendar.YEAR);

			Calendar testCalendar = null;
			int testYear = currentYear;

			List<Date> startDateList = new ArrayList<Date>();
			List<Date> endDateList = new ArrayList<Date>();

			whileLoop1: while (true) {

				// calculate start date of week based on input date
				startDateOfWeek = calculateStartDateOfWeek(testDate, startDayOfWeek);

				// calculate the year number of start date of week
				testCalendar = new GregorianCalendar();
				testCalendar.setTime(startDateOfWeek);
				testYear = testCalendar.get(Calendar.YEAR);

				if (isFirstIteration == true) {
					isFirstIteration = false;
					currentYear = testYear;
				} else if (testYear != currentYear) {
					break whileLoop1;
				}

				// calculate end date of week based on input date
				endDateOfWeek = DateUtils.addDays(startDateOfWeek, +6);

				//System.out.println("testDate=" + testDate + ",startDateOfWeek=" + startDateOfWeek + ",endDateOfWeek=" + endDateOfWeek);

				startDateList.add(startDateOfWeek);
				endDateList.add(endDateOfWeek);

				// move input date back by one week
				testDate = DateUtils.addWeeks(testDate, -1);
			}

			int totalWeeks = startDateList.size();
			forLoop1: for (int i = 0; i < totalWeeks; i++) {
				testWeekNumber = i + 1;
				startDateOfWeek = startDateList.get(totalWeeks - testWeekNumber);
				endDateOfWeek = endDateList.get(totalWeeks - testWeekNumber);
				//System.out.println("testWeekNumber=" + testWeekNumber + ",startDateOfWeek=" + startDateOfWeek + ",endDateOfWeek=" + endDateOfWeek);

				if (!dateToBeChecked.before(startDateOfWeek) && !dateToBeChecked.after(endDateOfWeek)) {
					finalWeekNumber = testWeekNumber;
					break forLoop1;
				}
			}
			String yearWeekString = String.valueOf(currentYear).concat(String.format("%02d", finalWeekNumber));
			yearWeek = NumberUtils.toInt(yearWeekString);

		} catch (Exception e) {
			e.printStackTrace();
		}
		return yearWeek;
	}

	public Date calculateStartDateOfWeek(Date dateOfWeekDate, Integer domainStartDayOfWeek) {

		Date startDateOfWeek = null;

		Integer dateOfWeekInteger = Integer.parseInt(DateFormatUtils.format(dateOfWeekDate, IConstants.DATE_FORMAT_YYYYMMDD));

		Integer startDateOfWeekNumber = null;

		// domainStartDayOfWeek mapping:
		// 1 = Monday
		// 2 = Tuesday
		// 3 = Wednesday
		// 4 = Thursday
		// 5 = Friday
		// 6 = Saturday
		// 7 = Sunday

		Date dateOfWeek = null;
		Calendar calendar = null;
		int dayOfWeek = 0;
		int numberOfDays = 0;
		String startDateOfWeekString = null;
		int tempDayOfWeek = 0;

		try {
			dateOfWeek = DateUtils.parseDate(String.valueOf(dateOfWeekInteger), new String[] { IConstants.DATE_FORMAT_YYYYMMDD });
			calendar = new GregorianCalendar();
			calendar.setTime(dateOfWeek);

			// Java calendar, day of week mapping:
			// 1 = Sunday
			// 2 = Monday
			// 3 = Tuesday
			// 4 = Wednesday
			// 5 = Thursday
			// 6 = Friday
			// 7 = Saturday
			dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);

			// convert Java day of week to seoclarity day of week
			dayOfWeek = dayOfWeek - 1;
			if (dayOfWeek == 0) {
				dayOfWeek = 7;
			}

			if (domainStartDayOfWeek == dayOfWeek) {
				startDateOfWeekNumber = dateOfWeekInteger;
			} else {
				if (domainStartDayOfWeek > dayOfWeek) {
					tempDayOfWeek = dayOfWeek + 7;
				} else {
					tempDayOfWeek = dayOfWeek;
				}
				numberOfDays = domainStartDayOfWeek - tempDayOfWeek;
				calendar.add(Calendar.DAY_OF_YEAR, numberOfDays);
				startDateOfWeekString = DateFormatUtils.format(calendar.getTime(), IConstants.DATE_FORMAT_YYYYMMDD);
				startDateOfWeekNumber = Integer.parseInt(startDateOfWeekString);
			}
			//System.out.println("calculateStartDateOfWeek() domainStartDayOfWeek=" + domainStartDayOfWeek + ",dateOfWeekInteger=" + dateOfWeekInteger
			//		+ ",dateOfWeek=" + dateOfWeek + ",dayOfWeek=" + dayOfWeek + ",numberOfDays=" + numberOfDays + ",startDateOfWeek="
			//		+ startDateOfWeek);

		} catch (ParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		try {
			startDateOfWeek = DateUtils.parseDate(String.valueOf(startDateOfWeekNumber), new String[] { IConstants.DATE_FORMAT_YYYYMMDD });
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		return startDateOfWeek;
	}

	public Date toDate(String dateString, String pattern) {
		if (StringUtils.isBlank(dateString)) {
			return null;
		}
		try {
			return DateUtils.parseDate(dateString, new String[] { pattern });
		} catch (ParseException e) {
			// e.printStackTrace();
		}
		return null;
	}

	//https://www.wrike.com/open.htm?id=15916670
	public String encodeMajesticUrl(String sourceUrlString, Boolean isRemoveTrailingSlash) {
		String encodedUrlString = sourceUrlString;
		String sourceUrlStringWithoutTrailingSlash = null;
		String decodedUrlString = null;
		String decodedUrlString2 = null;

		if (sourceUrlString != null && sourceUrlString.trim().length() > 0) {

			if (isRemoveTrailingSlash == true) {
				// remove trailing slash in competitor URL
				if (sourceUrlString.endsWith(SLASH)) {
					sourceUrlStringWithoutTrailingSlash = StringUtils.removeEndIgnoreCase(sourceUrlString, SLASH);
				} else {
					sourceUrlStringWithoutTrailingSlash = sourceUrlString;
				}
			} else {
				sourceUrlStringWithoutTrailingSlash = sourceUrlString;
			}

			if (sourceUrlStringWithoutTrailingSlash != null && sourceUrlStringWithoutTrailingSlash.trim().length() > 0) {
				try {
					// when source URL string contains 'www.costumesupercenter.com', do not decode the '+' to ' '.
					if (sourceUrlStringWithoutTrailingSlash.indexOf("www.costumesupercenter.com") != -1) {
						encodedUrlString = UriUtils.encodeHttpUrl(sourceUrlStringWithoutTrailingSlash, IConstants.UTF_8);
					} else {
						decodedUrlString = URLDecoder.decode(sourceUrlStringWithoutTrailingSlash, IConstants.UTF_8).trim();
						try {
							decodedUrlString2 = URLDecoder.decode(decodedUrlString, IConstants.UTF_8).trim();
						} catch (Exception e) {
							decodedUrlString2 = decodedUrlString;
							// continue to process the URL when it cannot be decoded.
						}
					}
				} catch (Exception e) {
					decodedUrlString2 = encodedUrlString;
					// continue to process the URL when it cannot be decoded.
				}
				try {
					encodedUrlString = UriUtils.encodeHttpUrl(decodedUrlString2, IConstants.UTF_8);
				} catch (Exception e) {
					// continue to process the URL when it cannot be encoded
				}
			}
		}

		//		System.out.println("encodeCompetitorUrl() sourceUrlString="+sourceUrlString);
		//		System.out.println("encodeCompetitorUrl() sourceUrlStringWithoutTrailingSlash="+sourceUrlStringWithoutTrailingSlash);
		//		System.out.println("encodeCompetitorUrl() decodedUrlString="+decodedUrlString);
		//		System.out.println("encodeCompetitorUrl() encodedUrlString="+encodedUrlString);

		return encodedUrlString;
	}

	public String escapeMetaCharacters(String inputString) {

		String outputString = inputString;

		// meta character \
		outputString = StringUtils.replace(outputString, IConstants.META_CHAR_BACKSLASH, IConstants.ESCAPED_ENCODED_META_CHAR + IConstants.META_CHAR_BACKSLASH);

		// meta character ^
		outputString = StringUtils.replace(outputString, IConstants.META_CHAR_CARET, IConstants.ESCAPED_ENCODED_META_CHAR + IConstants.META_CHAR_CARET);

		// meta character $
		outputString = StringUtils.replace(outputString, IConstants.META_CHAR_DOLLAR_SIGN, IConstants.ESCAPED_ENCODED_META_CHAR + IConstants.META_CHAR_DOLLAR_SIGN);

		// meta character .
		outputString = StringUtils.replace(outputString, IConstants.META_CHAR_DOT, IConstants.ESCAPED_ENCODED_META_CHAR + IConstants.META_CHAR_DOT);

		// meta character |
		outputString = StringUtils.replace(outputString, IConstants.META_CHAR_PIPE, IConstants.ESCAPED_ENCODED_META_CHAR + IConstants.META_CHAR_PIPE);

		// meta character ?
		outputString = StringUtils.replace(outputString, IConstants.META_CHAR_QUESTION_MARK, IConstants.ESCAPED_ENCODED_META_CHAR + IConstants.META_CHAR_QUESTION_MARK);

		// meta character *
		outputString = StringUtils.replace(outputString, IConstants.META_CHAR_ASTERISK, IConstants.ESCAPED_ENCODED_META_CHAR + IConstants.META_CHAR_ASTERISK);

		// meta character +
		outputString = StringUtils.replace(outputString, IConstants.META_CHAR_PLUS_SIGN, IConstants.ESCAPED_ENCODED_META_CHAR + IConstants.META_CHAR_PLUS_SIGN);

		// meta character (
		outputString = StringUtils.replace(outputString, IConstants.META_CHAR_OPENING_PARENTHESIS,
				IConstants.ESCAPED_ENCODED_META_CHAR + IConstants.META_CHAR_OPENING_PARENTHESIS);

		// meta character )
		outputString = StringUtils.replace(outputString, IConstants.CLOSING_PARENTHESIS, IConstants.ESCAPED_ENCODED_META_CHAR + IConstants.CLOSING_PARENTHESIS);

		// meta character ]
		outputString = StringUtils.replace(outputString, IConstants.OPEN_BRACKET, IConstants.ESCAPED_ENCODED_META_CHAR + IConstants.OPEN_BRACKET);

		// meta character {
		outputString = StringUtils.replace(outputString, IConstants.META_CHAR_OPENING_CURLY_BRACE,
				IConstants.ESCAPED_ENCODED_META_CHAR + IConstants.META_CHAR_OPENING_CURLY_BRACE);

		return outputString;
	}

	public int calcLengthDifference(String previousContentString, String currentContentString) {
		int stringLengthDifference = 0;
		if (StringUtils.isNotBlank(previousContentString) && StringUtils.isBlank(currentContentString)) {
			stringLengthDifference = previousContentString.length() * -1;
		} else if (StringUtils.isBlank(previousContentString) && StringUtils.isNotBlank(currentContentString)) {
			stringLengthDifference = currentContentString.length();
		} else if (StringUtils.isNotBlank(previousContentString) && StringUtils.isNotBlank(currentContentString)) {
			stringLengthDifference = previousContentString.length() - currentContentString.length();
		}
		return stringLengthDifference;
	}

	public String toString(String[] stringArray) {
		String output = null;
		StringBuilder stringBuilder = null;
		if (stringArray != null && stringArray.length > 0) {
			stringBuilder = new StringBuilder();
			for (String string : stringArray) {
				stringBuilder.append(string);
			}
			output = stringBuilder.toString();
		}
		return output;
	}

	public int calcContentDifference(String previousContentString, String currentContentString) {
		int stringContentDifference = 0;
		if (StringUtils.isNotBlank(previousContentString) && StringUtils.isNotBlank(currentContentString)
				&& previousContentString.length() == currentContentString.length()) {
			char[] charArray1 = previousContentString.toCharArray();
			char[] charArray2 = currentContentString.toCharArray();
			int lengthForComparison1 = charArray1.length;
			for (int i = 0; i < lengthForComparison1; i++) {
				if (charArray1[i] != charArray2[i]) {
					stringContentDifference++;
				}
			}
		}
		return stringContentDifference;
	}

	public String extractQueueNameFromQueueUrl(String queueUrl) {
		String queueName = null;
		logMemoryUsage("extractQueueNameFromQueueUrl() queueUrl=" + queueUrl);
		String[] stringArray = queueUrl.split(IConstants.SLASH);
		if (stringArray != null && stringArray.length > 0) {
			queueName = stringArray[stringArray.length - 1];
		}
		logMemoryUsage("extractQueueNameFromQueueUrl() queueName=" + queueName);
		return queueName;
	}

	public String convertArrayToString(Integer[] integerArray) {
		String output = null;
		StringBuilder stringBuilder = null;
		if (integerArray != null && integerArray.length > 0) {
			for (Integer input : integerArray) {
				if (stringBuilder == null) {
					stringBuilder = new StringBuilder();
				} else {
					stringBuilder.append(IConstants.COMMA);
				}
				stringBuilder.append(input);
			}
			output = stringBuilder.toString();
		}
		return output;
	}

	public String convertIntSetToString(Set<Integer> inputSet) {
		String output = null;
		StringBuilder stringBuilder = null;
		if (inputSet != null && inputSet.size() > 0) {
			for (int input : inputSet) {
				if (stringBuilder == null) {
					stringBuilder = new StringBuilder();
				} else {
					stringBuilder.append(IConstants.COMMA);
				}
				stringBuilder.append(input);
			}
			output = stringBuilder.toString();
		}
		return output;
	}

	public String convertStringListToString(List<String> inputList) {
		String output = null;
		StringBuilder stringBuilder = null;
		if (inputList != null && inputList.size() > 0) {
			for (String input : inputList) {
				if (stringBuilder == null) {
					stringBuilder = new StringBuilder();
				} else {
					stringBuilder.append(IConstants.COMMA);
				}
				stringBuilder.append(IConstants.SINGLE_QUOTE + input + IConstants.SINGLE_QUOTE);
			}
			output = stringBuilder.toString();
		}
		return output;
	}

	// positions are zero-based and inclusive
	public double[] extractDoubleArray(int extractFromPos, int extractToPos, double[] input) {
		int size = (extractToPos - extractFromPos) + 1;
		return Arrays.copyOfRange(input, extractFromPos, extractFromPos + size);
	}

	// positions are zero-based and inclusive
	public double[] extractDoubleArray(int extractFromPos, int extractToPos, String[] input) {
		int size = (extractToPos - extractFromPos) + 1;
		String[] stringArray = Arrays.copyOfRange(input, extractFromPos, extractFromPos + size);
		double[] doubleArray = new double[stringArray.length];
		for (int i = 0; i < stringArray.length; i++) {
			doubleArray[i] = convertStringToDouble(stringArray[i]);
		}
		return doubleArray;
	}

	public String removeCarriageReturnNewLine(final String inputText) {
		String trimmedText = inputText;
		if (StringUtils.isNotBlank(inputText)) {
			trimmedText = inputText.replaceAll(CARRIAGE_RETURN, IConstants.EMPTY_STRING).replaceAll(NEWLINE, IConstants.EMPTY_STRING);
		}
		return trimmedText;
	}

	// input format is yyyy-mm-dd
	//                 0123456789
	public Integer getMonth(String dateString) {
		Integer month = null;
		try {
			if (StringUtils.isNotBlank(dateString)) {
				DateUtils.parseDateStrictly(dateString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
				month = NumberUtils.toInt(StringUtils.substring(dateString, 5, 7));
			}
		} catch (Exception e) {
			// return null
		}
		return month;
	}

	// input format is yyyy-mm-dd
	public Integer getDay(String dateString) {
		Integer day = null;
		try {
			if (StringUtils.isNotBlank(dateString)) {
				DateUtils.parseDateStrictly(dateString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
				day = NumberUtils.toInt(StringUtils.substring(dateString, 8, 10));
			}
		} catch (Exception e) {
			// return null
		}
		return day;
	}

	public Map<Integer, Integer> getMonthLastDayMap() {
		return monthLastDayMap;
	}

	public double convertStringToDouble(String inputString) {
		double output = 0;
		try {
			output = new Double(inputString).doubleValue();
		} catch (Exception e) {
			// return 0 when inputString cannot be converted to a double
		}
		return output;
	}

	// input: yyyymmdd
	// output: yyyy-mm-dd
	public String convertDateNumberToString(int input) {
		String testString = String.valueOf(input);
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(StringUtils.substring(testString, 0, 4));
		stringBuilder.append(IConstants.DASH);
		stringBuilder.append(StringUtils.substring(testString, 4, 6));
		stringBuilder.append(IConstants.DASH);
		stringBuilder.append(StringUtils.substring(testString, 6));
		return stringBuilder.toString();
	}

	//https://www.wrike.com/open.htm?id=21902453
	//by sunny
	public String upperCaseFirstCharacter(String str) {

		if (StringUtils.isNotBlank(str)) {

			//    		String first = str.substring(0, 1).toUpperCase();
			//    		String rest = str.substring(1, str.length());
			//    		String newStr = new StringBuffer(first).append(rest).toString(); 
			//    		 
			//    		return newStr;

			//Cee - https://www.wrike.com/open.htm?id=391372442
			StringBuilder sb = new StringBuilder(str);
			sb.replace(0, 1, str.substring(0, 1).toUpperCase());
			return sb.toString();
		}

		return "";
	}

	public String formatDateNumberToString(int dateNumber) {
		String testDateString = String.valueOf(dateNumber);
		String dateString1 = StringUtils.substring(testDateString, 0, 4);
		String dateString2 = StringUtils.substring(testDateString, 4, 6);
		String dateString3 = StringUtils.substring(testDateString, 6, 8);
		String finalDateString = dateString1 + IConstants.DASH + dateString2 + IConstants.DASH + dateString3;
		return finalDateString;
	}

	public String cleanseJson(String input) throws Exception {
		String output = StringUtils.replace(input, IConstants.JSON_CLEANSED_1, IConstants.EMPTY_STRING);
		output = StringUtils.replace(output, IConstants.JSON_CLEANSED_2, IConstants.EMPTY_STRING);
		output = StringUtils.replace(output, IConstants.JSON_CLEANSED_3, IConstants.EMPTY_STRING);
		output = StringUtils.replace(output, IConstants.JSON_CLEANSED_4, IConstants.EMPTY_STRING);
		output = StringUtils.replace(output, IConstants.JSON_CLEANSED_5, IConstants.EMPTY_STRING);
		output = StringUtils.replace(output, IConstants.COMMA, IConstants.EMPTY_STRING);
		output = StringUtils.replace(output, IConstants.ONE_DOUBLE_QUOTES, IConstants.EMPTY_STRING);
		output = StringUtils.replace(output, IConstants.JSON_CLEANSED_6, IConstants.EMPTY_STRING);
		output = StringUtils.replace(output, IConstants.JSON_CLEANSED_7, IConstants.EMPTY_STRING);
		return output;
	}

}
