package com.actonia.utils;

import java.net.MalformedURLException;

import org.apache.commons.lang.StringUtils;

import com.actonia.IConstants;
import com.actonia.utils.url.normalization.UrlNormalizer;

public class SolrUtils {

	public static String getNormalizedUrlStringMd5HashCode(String urlString) {
		String normalizedUrlString = null;
		String md5HashCode = null;
		if (StringUtils.isNotBlank(urlString)) {
			// when URL starts with "http://" or "https://", continue processing....mostly to filter out those start with "ext:" or "ftp:"
			if (StringUtils.startsWithIgnoreCase(urlString, IConstants.HTTP_COLON_SLASH_SLASH)
					|| StringUtils.startsWithIgnoreCase(urlString, IConstants.HTTPS_COLON_SLASH_SLASH)) {
				try {
					// mostly to handling non-standard URLs (http://www.asdfasdf.com/#/index.htm) 
					urlString = StringUtils.replace(urlString, IConstants.SLASH_POUND_SLASH, IConstants.SLASH);
					normalizedUrlString = new UrlNormalizer(urlString.trim().toLowerCase()).getNormalizedUrl();
					if (StringUtils.isNotBlank(normalizedUrlString)) {
						md5HashCode = JCSManager.Md5(normalizedUrlString);
					}
				}
				// when the URL string cannot be normalized mostly due to encoding issues..
				catch (MalformedURLException e) {
					e.printStackTrace();
				}
			}
		}
		return md5HashCode;
	}
}
