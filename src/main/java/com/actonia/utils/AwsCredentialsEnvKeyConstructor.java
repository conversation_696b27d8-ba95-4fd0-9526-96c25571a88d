package com.actonia.utils;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.BasicAWSCredentials;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class AwsCredentialsEnvKeyConstructor {

    public static final String ENV_AWS_ACCESS_KEY_SQS = "AWS_ACCESS_KEY_SQS";
    public static final String ENV_AWS_ACCESS_KEY_S3 = "AWS_ACCESS_KEY_S3";
    public static final String ENV_AWS_SECRET_KEY_SQS = "AWS_SECRET_KEY_SQS";
    public static final String ENV_AWS_SECRET_KEY_S3 = "AWS_SECRET_KEY_S3";

    public static final String ENV_SEAGATE_TEMP_FILE_ACCESS_KEY_S3 = "SEAGATE_ACCESS_KEY_BUCKET_TEMPORARY_FILES";
    public static final String ENV_SEAGATE_TEMP_FILE_SECRET_KEY_S3 = "SEAGATE_SECRET_KEY_BUCKET_TEMPORARY_FILES";

    private static final Logger log = LogManager.getLogger(AwsCredentialsEnvKeyConstructor.class);

    public static AwsCredentialsEnvKeyConstructor awsCredentialsEnvKeyConstructor;

    public static AwsCredentialsEnvKeyConstructor getInstance() {
        if (awsCredentialsEnvKeyConstructor == null) {
            awsCredentialsEnvKeyConstructor = new AwsCredentialsEnvKeyConstructor();
        }
        return awsCredentialsEnvKeyConstructor;
    }

    /**
     * Get plaintext SQS ACCESS KEY from environment.
     * @return
     */
    public String getPlainTextSQSAccessKey() {
        String sqsAccessKey = System.getenv(ENV_AWS_ACCESS_KEY_SQS);
        if (StringUtils.isEmpty(sqsAccessKey)) {
            log.error("Can not get SQS encrypted access key from environment.");
            throw new RuntimeException("Can not get SQS encrypted access key from environment.");
        }
        return sqsAccessKey;
    }

    /**
     * Get plaintext S3 ACCESS KEY from environment.
     * @return
     */
    public String getPlainTextS3AccessKey() {
        String s3AccessKey = System.getenv(ENV_AWS_ACCESS_KEY_S3);
        if (StringUtils.isEmpty(s3AccessKey)) {
            log.error("Can not get S3 encrypted access key from environment.");
            throw new RuntimeException("Can not get S3 encrypted access key from environment.");
        }
        return s3AccessKey;
    }

    /**
     * Get encrypted sqs access key from environment and decrypt it.
     * @return
     */
    public String getSQSDecryptedAccessKey() {
        String sqsAccessKey = System.getenv(ENV_AWS_ACCESS_KEY_SQS);
        if (StringUtils.isEmpty(sqsAccessKey)) {
            log.error("Can not get sqs decrypted access key from environment.");
            throw new RuntimeException("Can not get sqs decrypted access key from environment.");
        }
        return ScStringEncryptor.decrypt(sqsAccessKey);
    }

    /**
     * Get encrypted s3 access key from environment and decrypt it.
     * @return
     */
    public String getS3DecryptedAccessKey() {
        String s3AccessKey = System.getenv(ENV_AWS_ACCESS_KEY_S3);
        if (StringUtils.isEmpty(s3AccessKey)) {
            log.error("Can not get s3 decrypted access key from environment.");
            throw new RuntimeException("Can not get s3 decrypted access key from environment.");
        }
        return ScStringEncryptor.decrypt(s3AccessKey);
    }

    /**
     * Get encrypted sqs secret key from environment and decrypt it.
     * @return
     */
    public String getSQSDecryptedSecretKey() {
        String sqsSecretKey = System.getenv(ENV_AWS_SECRET_KEY_SQS);
        if (StringUtils.isEmpty(sqsSecretKey)) {
            log.error("Can not get sqs decrypted secret key from environment.");
            throw new RuntimeException("Can not get sqs decrypted secret key from environment.");
        }
        return ScStringEncryptor.decrypt(sqsSecretKey);
    }

    /**
     * Get encrypted s3 secret key from environment and decrypt it.
     * @return
     */
    public String getS3DecryptedSecretKey() {
        String s3SecretKey = System.getenv(ENV_AWS_SECRET_KEY_S3);
        if (StringUtils.isEmpty(s3SecretKey)) {
            log.error("Can not get s3 decrypted secret key from environment.");
            throw new RuntimeException("Can not get s3 decrypted secret key from environment.");
        }
        return ScStringEncryptor.decrypt(s3SecretKey);
    }

    public String getAccessKey(String keyName) {
        String sqsAccessKey = System.getenv(keyName);
        if (StringUtils.isEmpty(sqsAccessKey)) {
            throw new RuntimeException("Can not get GATEWAY encrypted access key from environment.");
        }
        return sqsAccessKey;
    }

    public String getSecretKey(String keyName) {
        String sqsSecretKey = System.getenv(keyName);
        if (StringUtils.isEmpty(sqsSecretKey)) {
            throw new RuntimeException("Can not get GATEWAY decrypted secret key from environment.");
        }
        return ScStringEncryptor.decrypt(sqsSecretKey);
    }

    public AWSCredentials getAwsCredentials(String accessKeyName, String SecretKeyName) {
        String accessKey = getAccessKey(accessKeyName);
        String secretKey = getSecretKey(SecretKeyName);
        return new BasicAWSCredentials(accessKey, secretKey);
    }

}
