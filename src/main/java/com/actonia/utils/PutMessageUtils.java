package com.actonia.utils;

import com.actonia.IConstants;
import com.actonia.dao.*;
import com.actonia.entity.*;
import com.actonia.value.object.*;
import com.google.gson.*;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;

import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

public class PutMessageUtils {

	//private boolean isDebug = false;

	private static PutMessageUtils putMessageUtils;
	private static AssociatedUrlEntityDAO associatedUrlEntityDAO;
	private static AssociatedCompetitorUrlDAO associatedCompetitorUrlDAO;
	private static TempCompetitorUrlsHtmlEntityDAO tempCompetitorUrlsHtmlEntityDAO;
	private static String linkGainLossDefaultUserAgent;
	private static OwnDomainEntityDAO ownDomainEntityDAO;
	private static CommonParamDAO commonParamDAO;

	private PutMessageUtils() throws Exception {
		super();
		associatedUrlEntityDAO = SpringBeanFactory.getBean("associatedUrlEntityDAO");
		associatedCompetitorUrlDAO = SpringBeanFactory.getBean("associatedCompetitorUrlDAO");
		tempCompetitorUrlsHtmlEntityDAO = SpringBeanFactory.getBean("tempCompetitorUrlsHtmlEntityDAO");
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		commonParamDAO = SpringBeanFactory.getBean("commonParamDAO");

		// link.gain.loss.default.user.agent
		if (StringUtils.isBlank(linkGainLossDefaultUserAgent)) {
			linkGainLossDefaultUserAgent = PutMessageConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_LINK_GAIN_LOSS_DEFAULT_USER_AGENT);
			FormatUtils.getInstance().logMemoryUsage("PutMessageUtils() linkGainLossDefaultUserAgent=" + linkGainLossDefaultUserAgent);
		}
		SQSUtils.getInstance();
	}

	public static PutMessageUtils getInstance() throws Exception {
		if (putMessageUtils == null) {
			putMessageUtils = new PutMessageUtils();
		}
		return putMessageUtils;
	}

	public boolean isProcessTypeLinkClarityFull(String processType) {
		boolean output = false;
		if (StringUtils.equalsIgnoreCase(processType, IConstants.PROCESS_TYPE_LINK_CLARITY_FULL)) {
			output = true;
		}
		return output;
	}

	public boolean isProcessTypeTest(String processType) {
		boolean output = false;
		if (StringUtils.equalsIgnoreCase(processType, IConstants.PROCESS_TYPE_TEST)) {
			output = true;
		}
		return output;
	}

	public boolean isProcessTypeSolr(String processType) {
		boolean output = false;
		if (StringUtils.equalsIgnoreCase(processType, IConstants.PROCESS_TYPE_SOLR)) {
			output = true;
		}
		return output;
	}

	public boolean isProcessTypeClickHouse(String processType) {
		boolean output = false;
		if (StringUtils.equalsIgnoreCase(processType, IConstants.PROCESS_TYPE_CLICKHOUSE)) {
			output = true;
		}
		return output;
	}

	public boolean isProcessTypeControl(String processType) {
		boolean output = false;
		if (StringUtils.equalsIgnoreCase(processType, IConstants.PROCESS_TYPE_CONTROL)) {
			output = true;
		}
		return output;
	}

	public int updateDataStore(int domainId, String domainName, List<AssociatedUrlEntity> inputList, Integer trackDateNumber, String ip, String processType) {

		int totalCompetitorUrlCreated = 0;

		// retrieve the url from the temporary table to make sure only one record exists
		String hashCode = null;
		AssociatedUrlEntity associatedUrlEntityExisting = null;
		AssociatedCompetitorUrlEntity associatedCompetitorUrlEntity = null;
		String rankCheckedUrlString = null;
		//String urlStringBeforeUpdate = null;
		Integer protocolExisting = null;
		Integer protocolNew = null;
		URL url = null;
		Integer protocolRankChecked = null;
		String normalizedUrlMd5HashCode = null;
		String hostname = null;

		List<AssociatedUrlEntity> associatedUrlEntityToBeCreatedList = new ArrayList<AssociatedUrlEntity>();

		nextAssociatedUrlEntity: for (AssociatedUrlEntity associatedUrlEntityToBeCreated : inputList) {

			hostname = associatedUrlEntityToBeCreated.getHostname();

			// for each record to be created, check if already exists by hash code
			hashCode = associatedUrlEntityToBeCreated.getHashCode();

			if (isProcessTypeSolr(processType) == true) {
				associatedUrlEntityExisting = associatedUrlEntityDAO.get(hostname, hashCode);
			} else {
				associatedUrlEntityExisting = tempCompetitorUrlsHtmlEntityDAO.get(hostname, hashCode);
			}

			// when record with URL string's hash code already created....
			if (associatedUrlEntityExisting != null) {
				protocolNew = associatedUrlEntityToBeCreated.getProtocol();
				protocolExisting = associatedUrlEntityExisting.getProtocol();
				// when existing protocol is 'http' and new protocol is 'https', update url in 'temp_associated_urls_html' record with 'https'
				if (protocolNew != null && protocolExisting != null && protocolNew.intValue() == IConstants.TRUE_NUMERIC
						&& protocolExisting.intValue() == IConstants.FALSE_NUMERIC) {
					if (isProcessTypeSolr(processType) == true) {
						associatedUrlEntityDAO.updateUrl(hostname, hashCode, associatedUrlEntityToBeCreated.getUrl(), associatedUrlEntityToBeCreated.getProtocol(),
								trackDateNumber);
					} else {
						//tempCompetitorUrlsHtmlEntityDAO.updateUrl(hostname, hashCode, associatedUrlEntityToBeCreated.getUrl(),
						//		associatedUrlEntityToBeCreated.getProtocol(), trackDateNumber);
					}
				}
				continue nextAssociatedUrlEntity;
			}
			// when record with the hash code has not been created....
			else {
				if (isProcessTypeSolr(processType) == true) {
					normalizedUrlMd5HashCode = hashCode;

					// check the 'associated_competitor_url' table for the rank-checked version of the URL string with the same hash code
					associatedCompetitorUrlEntity = associatedCompetitorUrlDAO.get(normalizedUrlMd5HashCode);

					// when the competitor URL was auto-associated
					if (associatedCompetitorUrlEntity != null) {

						protocolNew = associatedUrlEntityToBeCreated.getProtocol();

						rankCheckedUrlString = associatedCompetitorUrlEntity.getCompetitorUrl();
						try {
							url = new URL(rankCheckedUrlString);

							// protocol of rank checked URL
							protocolRankChecked = null;
							if (StringUtils.equalsIgnoreCase(url.getProtocol(), IConstants.PROTOCOL_HTTPS)) {
								protocolRankChecked = IConstants.TRUE_NUMERIC;
							} else if (StringUtils.equalsIgnoreCase(url.getProtocol(), IConstants.PROTOCOL_HTTP)) {
								protocolRankChecked = IConstants.FALSE_NUMERIC;
							}
							if (protocolRankChecked != null) {

								// when rank checked protocol is 'https' and new protocol is 'http', update url in 'associatedUrlEntityToBeCreated' object with rank checked URL
								if (protocolNew != null && protocolRankChecked != null && protocolNew.intValue() == IConstants.FALSE_NUMERIC
										&& protocolRankChecked.intValue() == IConstants.TRUE_NUMERIC) {
									//FormatUtils.logMemoryUsage(
									//		"updateDataStore() 'associatedUrlEntityToBeCreated' updated with rank checked URL. associatedUrlEntityToBeCreated.getUrl()="
									//				+ associatedUrlEntityToBeCreated.getUrl() + ",rankCheckedUrlString=" + rankCheckedUrlString);
									associatedUrlEntityToBeCreated.setUrl(rankCheckedUrlString);
									associatedUrlEntityToBeCreated.setProtocol(IConstants.TRUE_NUMERIC);
								}
							}
						} catch (Exception e) {
						}
					}
				}
				associatedUrlEntityToBeCreatedList.add(associatedUrlEntityToBeCreated);
			}
		}

		if (associatedUrlEntityToBeCreatedList != null && associatedUrlEntityToBeCreatedList.size() > 0) {
			if (isProcessTypeSolr(processType) == true) {
				associatedUrlEntityDAO.insertMultiRowsBatch(associatedUrlEntityToBeCreatedList);
			} else {
				tempCompetitorUrlsHtmlEntityDAO.insertMultiRowsBatch(associatedUrlEntityToBeCreatedList);
			}
			totalCompetitorUrlCreated = associatedUrlEntityToBeCreatedList.size();
		}

		return totalCompetitorUrlCreated;
	}

	public String updateDomainIdLanguageCodeJson(AssociatedUrlEntity associatedUrlEntityExisting, int newDomainId, String newLanguageCode) {
		String revisedDomainIdLanguageCodeJson = getRevisedDomainIdLanguageCodeJson(associatedUrlEntityExisting.getDomainIdLanguageCodeJson(), newDomainId,
				newLanguageCode);
		if (StringUtils.isNotBlank(revisedDomainIdLanguageCodeJson)) {
			tempCompetitorUrlsHtmlEntityDAO.updateDomainIdLanguageCodeJson(associatedUrlEntityExisting.getHostname(), associatedUrlEntityExisting.getHashCode(),
					revisedDomainIdLanguageCodeJson);
		}
		return revisedDomainIdLanguageCodeJson;
	}

	public String getRevisedDomainIdLanguageCodeJson(String domainIdLanguageCodeJsonExisting, int newDomainId, String newLanguageCode) {
		String revisedDomainIdLanguageCodeJson = null;
		DomainIdLanguageCodeValueObject domainIdLanguageCodeValueObjectExisting = null;
		DomainIdLanguageCodeValueObject domainIdLanguageCodeValueObjectNew = null;
		Gson gson = new Gson();
		boolean isDomainIdLanguageCodeAlreadyCreated = false;
		List<Integer> domainIdList = null;
		List<String> languageCodeList = null;
		if (StringUtils.isNotBlank(domainIdLanguageCodeJsonExisting)) {
			domainIdLanguageCodeValueObjectExisting = new Gson().fromJson(domainIdLanguageCodeJsonExisting, DomainIdLanguageCodeValueObject.class);
			if (domainIdLanguageCodeValueObjectExisting != null && domainIdLanguageCodeValueObjectExisting.getDomainIds() != null
					&& domainIdLanguageCodeValueObjectExisting.getDomainIds().length > 0) {
				nextDomainId: for (int i = 0; i < domainIdLanguageCodeValueObjectExisting.getDomainIds().length; i++) {
					if (domainIdLanguageCodeValueObjectExisting.getDomainIds()[i] == newDomainId
							&& StringUtils.equalsIgnoreCase(domainIdLanguageCodeValueObjectExisting.getLanguageCodes()[i], newLanguageCode)) {
						isDomainIdLanguageCodeAlreadyCreated = true;
						//if (isDebug == true) {
						//	FormatUtils.logMemoryUsage("getRevisedDomainIdLanguageCodeJson() competitorUrl=" + competitorUrl + ",domainIdLanguageCodeJsonExisting="
						//			+ domainIdLanguageCodeJsonExisting + ",newDomainId=" + newDomainId + ",newLanguageCode=" + newLanguageCode
						//			+ ",isDomainIdLanguageCodeAlreadyCreated=" + isDomainIdLanguageCodeAlreadyCreated);
						//}
						break nextDomainId;
					}
				}

				// when domain ID and language code not already in the existing 'domain_id_language_code_json' field, the competitor URL is shared by more than one client domain
				if (isDomainIdLanguageCodeAlreadyCreated == false) {
					domainIdLanguageCodeValueObjectNew = new DomainIdLanguageCodeValueObject();

					domainIdList = new ArrayList<Integer>();
					for (int domainId : domainIdLanguageCodeValueObjectExisting.getDomainIds()) {
						domainIdList.add(domainId);
					}
					domainIdList.add(newDomainId);
					domainIdLanguageCodeValueObjectNew.setDomainIds(domainIdList.toArray(new Integer[0]));

					languageCodeList = new ArrayList<String>();
					for (String language : domainIdLanguageCodeValueObjectExisting.getLanguageCodes()) {
						languageCodeList.add(language);
					}
					languageCodeList.add(newLanguageCode);
					domainIdLanguageCodeValueObjectNew.setLanguageCodes(languageCodeList.toArray(new String[0]));
					revisedDomainIdLanguageCodeJson = gson.toJson(domainIdLanguageCodeValueObjectNew, DomainIdLanguageCodeValueObject.class);
					//if (isDebug == true) {
					//	FormatUtils.logMemoryUsage("getRevisedDomainIdLanguageCodeJson() competitorUrl=" + competitorUrl + ",domainIdLanguageCodeJsonExisting="
					//			+ domainIdLanguageCodeJsonExisting + ",newDomainId=" + newDomainId + ",newLanguageCode=" + newLanguageCode
					//			+ ",revisedDomainIdLanguageCodeJson=" + revisedDomainIdLanguageCodeJson);
					//}
				}
			}
		}
		return revisedDomainIdLanguageCodeJson;
	}

	// determine if already to put messages to competitor URLs weekly crawl queues or link clarity crawl queues
	public boolean checkIfReadyToPutMessages(String processType, int totalNumberOfQueues) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("checkIfReadyToPutMessages() begins. processType=" + processType + ",totalNumberOfQueues=" + totalNumberOfQueues);
		boolean isReady = true;
		String htmlQueueNamePrefix = null;
		String htmlQueueName = null;
		Integer[] numberOfMessagesArray = null;
		int messagesInQueue = 0;
		int messagesInFlight = 0;

		// determine the queue name prefix
		if (isProcessTypeTest(processType) == true) {
			htmlQueueNamePrefix = IConstants.QUEUE_NAME_TEST_COMPETITOR_URL_HTML_PREFIX;
		} else if (isProcessTypeClickHouse(processType) == true) {
			htmlQueueNamePrefix = IConstants.QUEUE_NAME_COMPETITOR_URL_HTML_PREFIX;
		}
		if (StringUtils.isBlank(htmlQueueNamePrefix)) {
			isReady = false;
			return isReady;
		}

		nextQueue: for (int i = 0; i < totalNumberOfQueues; i++) {
			htmlQueueName = htmlQueueNamePrefix + String.valueOf((i + 1));
			numberOfMessagesArray = SQSUtils.getInstance().getApproximateNumberOfMessagesAndInflight(htmlQueueName);
			if (numberOfMessagesArray != null && numberOfMessagesArray.length == 2) {
				messagesInQueue = numberOfMessagesArray[0];
				messagesInFlight = numberOfMessagesArray[1];
				// when there are still messages in queue or in flight for queue not in 'competitor_url_weekly_crawl_tracking' table
				if (messagesInQueue == 0) {
					// proceed
				} else if (messagesInQueue < 6168 && messagesInFlight == 0) {
					// proceed
				} else {
					FormatUtils.getInstance().logMemoryUsage("checkIfReadyToPutMessages() htmlQueueName=" + htmlQueueName + ",messagesInQueue=" + messagesInQueue
							+ ",messagesInFlight=" + messagesInFlight);
					isReady = false;
					break nextQueue;
				}
			} else {
				FormatUtils.getInstance().logMemoryUsage("checkIfReadyToPutMessages() error--numberOfMessagesArray is empty,htmlQueueName=" + htmlQueueName);
			}
		}
		FormatUtils.getInstance()
				.logMemoryUsage("checkIfReadyToPutMessages() ends. processType=" + processType + ",totalNumberOfQueues=" + totalNumberOfQueues + ",isReady=" + isReady);
		return isReady;
	}

	public int getMaxConcurrentCrawlThreads(int totalCompetitorUrls, int maxConcurrentCrawlThreads) {
		return ((totalCompetitorUrls / IConstants.COMPETITOR_WEEKLY_CRAWL_TRACKING_THRESHOLD) + 1) * maxConcurrentCrawlThreads;
	}

	public int getDelayInSecondsPerHttpRequest(int totalCompetitorUrls) {
		int delayInSecondsPerHttpRequest = (totalCompetitorUrls / IConstants.COMPETITOR_WEEKLY_CRAWL_TRACKING_THRESHOLD) - 5;
		if (delayInSecondsPerHttpRequest < 0) {
			delayInSecondsPerHttpRequest = 0;
		}
		return delayInSecondsPerHttpRequest;
	}

	public List<OwnDomainEntity> filterDomains(List<OwnDomainEntity> allOwnDomainEntityList, Boolean isExecDomainIdsInd, Set<Integer> runtimeDomainSet)
			throws Exception {

		int totalNumberOfDomains = allOwnDomainEntityList.size();
		//FormatUtils.getInstance().logMemoryUsage("filterDomains() begins. total number of domains=" + totalNumberOfDomains);
		FormatUtils.getInstance().logMemoryUsage(
				"filterDomains() totalNumberOfDomains=" + totalNumberOfDomains + ",isExecDomainIdsInd=" + isExecDomainIdsInd + ",runtimeDomainSet=" + runtimeDomainSet);

		OwnDomainEntity ownDomainEntity = null;
		int numberOfDomainsProcessed = 0;
		List<OwnDomainEntity> ownDomainEntityList = new ArrayList<OwnDomainEntity>();

		do {
			ownDomainEntity = allOwnDomainEntityList.get(numberOfDomainsProcessed++);
			//if (isDebug == true) {
			//	FormatUtils.getInstance().logMemoryUsage("filterDomains() processing ownDomainEntity="+ownDomainEntity.toString());
			//}
			if (isExecDomainIdsInd == null) {
				ownDomainEntityList.add(ownDomainEntity);
			} else if (isExecDomainIdsInd == true) {
				if (runtimeDomainSet.contains(ownDomainEntity.getId())) {
					ownDomainEntityList.add(ownDomainEntity);
				}
			} else if (isExecDomainIdsInd == false) {
				if (!runtimeDomainSet.contains(ownDomainEntity.getId())) {
					ownDomainEntityList.add(ownDomainEntity);
				}
			}

		} while (numberOfDomainsProcessed < totalNumberOfDomains);

		return ownDomainEntityList;
	}

	public Map<String, String> getDomainNameUserAgentMap(List<OwnDomainEntity> ownDomainEntityList) {

		// map key = domain name
		// map key = user agent
		Map<String, String> domainNameUserAgentMap = new HashMap<String, String>();

		UrlCrawlParametersVO[] urlCrawlParametersVoArray = null;
		Gson gson = new Gson();
		UrlCrawlParametersVO urlCrawlParametersVO = null;
		String userAgent = null;
		if (ownDomainEntityList != null && ownDomainEntityList.size() > 0) {
			for (OwnDomainEntity ownDomainEntity : ownDomainEntityList) {
				if (StringUtils.isNotBlank(ownDomainEntity.getUrlCrawlParameters())) {
					urlCrawlParametersVoArray = gson.fromJson(ownDomainEntity.getUrlCrawlParameters(), UrlCrawlParametersVO[].class);
					for (int idx = 0; idx < urlCrawlParametersVoArray.length; idx++) {
						urlCrawlParametersVO = urlCrawlParametersVoArray[idx];
						// user agent name for sending HTTP requests
						if (urlCrawlParametersVO.getType().equalsIgnoreCase(IConstants.USER_AGENT)) {
							if (StringUtils.isNotBlank(urlCrawlParametersVO.getData())
									&& StringUtils.equalsIgnoreCase(urlCrawlParametersVO.getData(), IConstants.NEW_DEFAULT_USER_AGENT) == false) {
								userAgent = urlCrawlParametersVO.getData();
								domainNameUserAgentMap.put(ownDomainEntity.getDomain(), userAgent);
							}
						}
					}
				}
			}

			if (domainNameUserAgentMap != null && domainNameUserAgentMap.size() > 0) {
				for (String domainName : domainNameUserAgentMap.keySet()) {
					FormatUtils.getInstance()
							.logMemoryUsage("getDomainNameUserAgentMap() domainName=" + domainName + ",userAgent=" + domainNameUserAgentMap.get(domainName));
				}
			}
		}

		return domainNameUserAgentMap;
	}

	public Map<Integer, String> getDomainIdUserAgentMap(List<OwnDomainEntity> ownDomainEntityList) {

		// map key = domain ID
		// map key = user agent
		Map<Integer, String> domainIdUserAgentMap = new HashMap<Integer, String>();

		UrlCrawlParametersVO[] urlCrawlParametersVoArray = null;
		Gson gson = new Gson();
		UrlCrawlParametersVO urlCrawlParametersVO = null;
		String userAgent = null;
		if (ownDomainEntityList != null && ownDomainEntityList.size() > 0) {
			for (OwnDomainEntity ownDomainEntity : ownDomainEntityList) {
				if (StringUtils.isNotBlank(ownDomainEntity.getUrlCrawlParameters())) {
					urlCrawlParametersVoArray = gson.fromJson(ownDomainEntity.getUrlCrawlParameters(), UrlCrawlParametersVO[].class);
					if (urlCrawlParametersVoArray != null && urlCrawlParametersVoArray.length > 0) {
						for (int idx = 0; idx < urlCrawlParametersVoArray.length; idx++) {
							urlCrawlParametersVO = urlCrawlParametersVoArray[idx];
							// user agent name for sending HTTP requests
							if (urlCrawlParametersVO.getType().equalsIgnoreCase(IConstants.USER_AGENT)) {
								if (StringUtils.isNotBlank(urlCrawlParametersVO.getData())) {
									userAgent = urlCrawlParametersVO.getData();
									domainIdUserAgentMap.put(ownDomainEntity.getId(), userAgent);
								}
							}
						}
					}
				}
			}

			if (domainIdUserAgentMap != null && domainIdUserAgentMap.size() > 0) {
				for (Integer domainId : domainIdUserAgentMap.keySet()) {
					FormatUtils.getInstance().logMemoryUsage("getDomainNameUserAgentMap() domainId=" + domainId + ",userAgent=" + domainIdUserAgentMap.get(domainId));
				}
			}
		}

		return domainIdUserAgentMap;
	}

	public String getDomainNames(Map<String, String> domainNameUserAgentMap) {
		String domainNames = null;
		StringBuilder stringBuilder = null;
		for (String domainName : domainNameUserAgentMap.keySet()) {
			if (stringBuilder == null) {
				stringBuilder = new StringBuilder();
			} else {
				stringBuilder.append(IConstants.COMMA);
			}
			stringBuilder.append(domainName);
		}
		domainNames = stringBuilder.toString();
		FormatUtils.getInstance().logMemoryUsage("getDomainNames() domainNames=" + domainNames);
		return domainNames;
	}

	public String getUserAgents(Map<String, String> domainNameUserAgentMap) {
		String userAgents = null;
		String userAgent = null;
		StringBuilder stringBuilder = null;

		for (String domainName : domainNameUserAgentMap.keySet()) {
			userAgent = domainNameUserAgentMap.get(domainName);
			if (stringBuilder == null) {
				stringBuilder = new StringBuilder();
			} else {
				stringBuilder.append(IConstants.COMMA);
			}
			stringBuilder.append(userAgent);
		}
		userAgents = stringBuilder.toString();
		FormatUtils.getInstance().logMemoryUsage("getUserAgents() userAgents=" + userAgents);
		return userAgents;
	}

	public String getHeaderLine() {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("DOMAIN ID");
		stringBuilder.append(IConstants.TAB);
		stringBuilder.append("DOMAIN NAME");
		stringBuilder.append(IConstants.TAB);
		stringBuilder.append("DOMAIN TYPE");
		stringBuilder.append(IConstants.TAB);
		stringBuilder.append("TOTAL URLS");
		stringBuilder.append(IConstants.TAB);
		stringBuilder.append("HTTP STATUS");
		stringBuilder.append(IConstants.TAB);
		stringBuilder.append("TOTAL URLS WITH HTTP STATUS");
		return stringBuilder.toString();
	}

	public String getOutputLine(PoliteCrawlSummaryValueObject politeCrawlSummaryValueObject) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(politeCrawlSummaryValueObject.getDomainId());
		stringBuilder.append(IConstants.TAB);
		stringBuilder.append(politeCrawlSummaryValueObject.getDomainName());
		stringBuilder.append(IConstants.TAB);
		stringBuilder.append(politeCrawlSummaryValueObject.getDomainType());
		stringBuilder.append(IConstants.TAB);
		stringBuilder.append(politeCrawlSummaryValueObject.getTotalUrls());
		stringBuilder.append(IConstants.TAB);
		stringBuilder.append(politeCrawlSummaryValueObject.getHttpStatusCode());
		stringBuilder.append(IConstants.TAB);
		stringBuilder.append(politeCrawlSummaryValueObject.getTotalUrlsWithResponseCode());
		return stringBuilder.toString();
	}

	public int getHttpStatusCode(ScrapyCrawlerResponse scrapyCrawlerResponse) {
		int httpStatusCode = 0;
		CrawlerResponse crawlerResponse = null;
		String responseCode = null;
		if (scrapyCrawlerResponse != null) {
			crawlerResponse = scrapyCrawlerResponse.getCrawlerResponse();
			if (crawlerResponse != null) {
				responseCode = crawlerResponse.getResponse_code();
				if (StringUtils.isNotBlank(responseCode)) {
					if (NumberUtils.isNumber(responseCode)) {
						httpStatusCode = NumberUtils.toInt(responseCode);
					}
					// cannot be crawled...
					else {
						httpStatusCode = 999;
					}
				}
				// cannot be crawled...
				else {
					httpStatusCode = 999;
				}
			}
			// cannot be crawled...
			else {
				httpStatusCode = 999;
			}
		}
		// cannot be crawled...
		else {
			httpStatusCode = 999;
		}

		return httpStatusCode;
	}

	public String getReversedDomainName(String domainName) {
		String reversedDomainName = null;
		String testString = StringUtils.substringBefore(domainName, IConstants.SLASH);
		try {
			if (StringUtils.isNotBlank(testString)) {
				reversedDomainName = StringUtils.reverseDelimited(testString, IConstants.DOT_STRING.charAt(0));
			} else {
				reversedDomainName = IConstants.EMPTY_STRING;
			}
		} catch (Exception e) {
			//e.printStackTrace();
			//FormatUtils.logMemoryUsage("getReversedDomainName() domainName=" + domainName + ",exception message=" + e.getMessage());
		}
		return reversedDomainName;
	}

	public List<String> getExtractOutputList(List<LinkGainLossClickHouseEntity> linkGainLossClickHouseEntityList, String delimiter) {
		List<String> outputList = new ArrayList<String>();
		StringBuilder stringBuilder = null;
		for (LinkGainLossClickHouseEntity linkGainLossClickHouseEntity : linkGainLossClickHouseEntityList) {

			stringBuilder = new StringBuilder();

			// domain_id
			stringBuilder.append(linkGainLossClickHouseEntity.getDomainId());
			stringBuilder.append(delimiter);

			// gain_loss_date
			stringBuilder.append(DateFormatUtils.format(linkGainLossClickHouseEntity.getGainLossDate(), IConstants.DATE_FORMAT_YYYY_MM_DD));
			stringBuilder.append(delimiter);

			// gain_loss_flag
			if (linkGainLossClickHouseEntity.getGainLossFlag() == IConstants.GAIN_LOSS_FLAG_GAINED) {
				stringBuilder.append("Link Gained");
			} else if (linkGainLossClickHouseEntity.getGainLossFlag() == IConstants.GAIN_LOSS_FLAG_LOST) {
				stringBuilder.append("Link Lost");
			} else {
				stringBuilder.append("Unknown");
			}
			stringBuilder.append(delimiter);

			// source_url
			stringBuilder.append(linkGainLossClickHouseEntity.getSourceUrl());
			stringBuilder.append(delimiter);

			// target_url
			stringBuilder.append(FormatUtils.getInstance().trimText(linkGainLossClickHouseEntity.getTargetUrl()));
			stringBuilder.append(delimiter);

			// anchor_text
			stringBuilder.append(IConstants.SINGLE_QUOTE + FormatUtils.getInstance().trimText(linkGainLossClickHouseEntity.getAnchorText()));
			stringBuilder.append(delimiter);

			// link_type
			if (linkGainLossClickHouseEntity.getLinkType() == IConstants.LINK_TYPE_IMAGE) {
				stringBuilder.append("Image Link");
			} else if (linkGainLossClickHouseEntity.getLinkType() == IConstants.LINK_TYPE_TEXT) {
				stringBuilder.append("Text Link");
			} else {
				stringBuilder.append("Unknown");
			}
			stringBuilder.append(delimiter);

			// target_url_http_status_code
			stringBuilder.append(linkGainLossClickHouseEntity.getTargetUrlHttpStatusCode());
			stringBuilder.append(delimiter);

			outputList.add(stringBuilder.toString());

		}
		return outputList;
	}

	// map key = target URL MD5 hash code
	// map value = target URL's latest historical crawled data
	public Map<String, HtmlClickHouseEntity> getTargetUrlHtmlClickHouseEntityMap(String ip, int domainId, String domainName, List<String> targetUrlHtmlFieldNames)
			throws Exception {

		// map key = target URL MD5 hash code
		// map value = target URL's daily HtmlClickHouseEntity
        List<HtmlClickHouseEntity> htmlClickHouseEntityList = TargetUrlHtmlClickHouseDAO.getInstance().getLatestFromHistorical(domainId, targetUrlHtmlFieldNames);
        return htmlClickHouseEntityList.parallelStream().collect(Collectors.toMap(HtmlClickHouseEntity -> {
			final String url = HtmlClickHouseEntity.getUrl();
			return Md5Util.Md5(StringUtils.trim(url));
		}, htmlClickHouseEntity -> htmlClickHouseEntity, (HTMLClickHouseEntity1, HTMLClickHouseEntity2) -> HTMLClickHouseEntity2));
    }

	// map key = content guard URL MD5 hash code
	// map value = content guard URL's latest historical crawled data
	public Map<String, HtmlClickHouseEntity> getContentGuardClickHouseEntityMap(int domainId, List<String> fieldNames) throws Exception {

		// map key = content guard URL MD5 hash code
		// map value = content guard URL's daily HtmlClickHouseEntity
		Map<String, HtmlClickHouseEntity> contentGuardClickHouseEntityMap = new HashMap<String, HtmlClickHouseEntity>();

		String contentGuardUrlString = null;
		String contentGuardUrlMd5HashCode = null;
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = ContentGuardClickHouseDAO.getInstance().getLatestFromHistorical(domainId, fieldNames, null);
		if (htmlClickHouseEntityList != null && htmlClickHouseEntityList.size() > 0) {
			for (HtmlClickHouseEntity htmlClickHouseEntity : htmlClickHouseEntityList) {
				contentGuardUrlString = htmlClickHouseEntity.getUrl();
				if (StringUtils.isNotBlank(contentGuardUrlString) == true) {
					contentGuardUrlMd5HashCode = Md5Util.Md5(StringUtils.trim(contentGuardUrlString));
					htmlClickHouseEntity.setUrl(null);
					contentGuardClickHouseEntityMap.put(contentGuardUrlMd5HashCode, htmlClickHouseEntity);
				}
			}
		}
		return contentGuardClickHouseEntityMap;
	}

	public List<String> getHistoricalHtmlFieldNames() {
		List<String> outputList = new ArrayList<String>();
		outputList.add(IConstants.URL);
		outputList.add(IConstants.AMPHTML_FLAG);
		outputList.add(IConstants.AMPHTML_HREF);
		outputList.add(IConstants.ANALYZED_URL_S);
		outputList.add(IConstants.ANALYZED_URL_FLG_S);
		outputList.add(IConstants.ARCHIVE_FLG);
		outputList.add(IConstants.ARCHIVE_FLG_X_TAG);
		outputList.add(IConstants.BLOCKED_BY_ROBOTS);
		outputList.add(IConstants.CANONICAL);
		outputList.add(IConstants.CANONICAL_FLG);
		outputList.add(IConstants.CANONICAL_HEADER_FLAG);
		outputList.add(IConstants.CANONICAL_HEADER_TYPE);
		outputList.add(IConstants.CANONICAL_TYPE);
		outputList.add(IConstants.CANONICAL_URL_IS_CONSISTENT);
		outputList.add(IConstants.CONTENT_TYPE);
		outputList.add(IConstants.DESCRIPTION);
		outputList.add(IConstants.DESCRIPTION_FLG);
		outputList.add(IConstants.DESCRIPTION_LENGTH);
		outputList.add(IConstants.DESCRIPTION_SIMHASH);
		outputList.add(IConstants.ERROR_MESSAGE);
		outputList.add(IConstants.FINAL_RESPONSE_CODE);
		outputList.add(IConstants.FOLLOW_FLG);
		outputList.add(IConstants.FOLLOW_FLG_X_TAG);
		outputList.add(IConstants.H1);
		outputList.add(IConstants.H1_COUNT);
		outputList.add(IConstants.H1_FLG);
		outputList.add(IConstants.H1_LENGTH);
		outputList.add(IConstants.H1_MD5);
		outputList.add(IConstants.H2);
		outputList.add(IConstants.HEADER_NOARCHIVE);
		outputList.add(IConstants.HEADER_NOFOLLOW);
		outputList.add(IConstants.HEADER_NOINDEX);
		outputList.add(IConstants.HEADER_NOODP);
		outputList.add(IConstants.HEADER_NOSNIPPET);
		outputList.add(IConstants.HEADER_NOYDIR);
		outputList.add(IConstants.HREFLANG_LINKS_OUT_COUNT);
		outputList.add(IConstants.HREFLANG_URL_COUNT);
		outputList.add(IConstants.HREFLANG_ERRORS);
		outputList.add(IConstants.INDEX_FLG);
		outputList.add(IConstants.INDEX_FLG_X_TAG);
		outputList.add(IConstants.INDEXABLE);
		outputList.add(IConstants.INSECURE_RESOURCES_FLAG);
		outputList.add(IConstants.META_CHARSET);
		outputList.add(IConstants.META_CONTENT_TYPE);
		outputList.add(IConstants.META_DISABLED_SITELINKS);
		outputList.add(IConstants.META_NOODP);
		outputList.add(IConstants.META_NOSNIPPET);
		outputList.add(IConstants.META_NOYDIR);
		outputList.add(IConstants.META_REDIRECT);
		outputList.add(IConstants.MIXED_REDIRECTS);
		outputList.add(IConstants.MOBILE_REL_ALTERNATE_URL_IS_CONSISTENT);
		outputList.add(IConstants.NOODP);
		outputList.add(IConstants.NOSNIPPET);
		outputList.add(IConstants.NOYDIR);
		outputList.add(IConstants.OG_MARKUP_FLAG);
		outputList.add(IConstants.OG_MARKUP_LENGTH);
		outputList.add(IConstants.OUTLINK_COUNT);
		outputList.add(IConstants.REDIRECT_BLOCKED);
		outputList.add(IConstants.REDIRECT_BLOCKED_REASON);
		outputList.add(IConstants.REDIRECT_FLG);
		outputList.add(IConstants.REDIRECT_FINAL_URL);
		outputList.add(IConstants.REDIRECT_TIMES);
		outputList.add(IConstants.RESPONSE_CODE);
		outputList.add(IConstants.RESPONSE_HEADERS);
		outputList.add(IConstants.ROBOTS);
		outputList.add(IConstants.ROBOTS_CONTENTS);
		outputList.add(IConstants.ROBOTS_CONTENTS_X_TAG);
		outputList.add(IConstants.ROBOTS_FLG);
		outputList.add(IConstants.ROBOTS_FLG_X_TAG);
		outputList.add(IConstants.TITLE);
		outputList.add(IConstants.TITLE_FLG);
		outputList.add(IConstants.TITLE_LENGTH);
		outputList.add(IConstants.TITLE_MD5);
		outputList.add(IConstants.TITLE_SIMHASH);
		outputList.add(IConstants.TRACK_DATE);
		outputList.add(IConstants.VIEWPORT_FLAG);
		outputList.add(IConstants.PAGE_ANALYSIS_RESULTS);
		outputList.add(IConstants.CHANGE_TRACKING_HASH_CD_JSON);
		outputList.add(IConstants.CRAWL_TIMESTAMP);
		outputList.add(IConstants.BASE_TAG);
		outputList.add(IConstants.BASE_TAG_FLAG);
		outputList.add(IConstants.VIEWPORT_CONTENT);
		outputList.add(IConstants.BASE_TAG_TARGET);
		return outputList;
	}

	// map key = competitor URL MD5 hash code
	// map value = competitor URL's latest historical crawled data
	public Map<String, HtmlClickHouseEntity> getCompetitorUrlHtmlClickHouseEntityMap(String ip, List<String> reversedUrlDomainList) throws Exception {

		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance()
				.logMemoryUsage("getCompetitorUrlHtmlClickHouseEntityMap() begins. ip=" + ip + ",reversedUrlDomainList.size()=" + reversedUrlDomainList.size());

		// map key = competitor URL MD5 hash code
		// map value = competitor URL's daily HtmlClickHouseEntity
		Map<String, HtmlClickHouseEntity> competitorUrlHtmlClickHouseEntityMap = new HashMap<String, HtmlClickHouseEntity>();

		String competitorUrlString = null;
		String competitorUrlMd5HashCode = null;
		//String reversedUrlDomain = FormatUtils.getInstance().getReversedDomainName(domainName);
		List<String> competitorUrlHtmlFieldNames = getHistoricalHtmlFieldNames();
		boolean filterByResponseCode = true;
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = CompetitorUrlHtmlClickHouseDAO.getInstance().getLatestFromHistorical(ip, reversedUrlDomainList,
				competitorUrlHtmlFieldNames, null, filterByResponseCode);
		if (htmlClickHouseEntityList != null && htmlClickHouseEntityList.size() > 0) {
			for (HtmlClickHouseEntity htmlClickHouseEntity : htmlClickHouseEntityList) {
				competitorUrlString = htmlClickHouseEntity.getUrl();
				htmlClickHouseEntity.setUrl(null);
				if (StringUtils.isNotBlank(competitorUrlString) == true) {
					competitorUrlMd5HashCode = Md5Util.Md5(StringUtils.trim(competitorUrlString));
					competitorUrlHtmlClickHouseEntityMap.put(competitorUrlMd5HashCode, htmlClickHouseEntity);
				}
			}
		}
		FormatUtils.getInstance()
				.logMemoryUsage("getCompetitorUrlHtmlClickHouseEntityMap() ends. ip=" + ip + ",reversedUrlDomainList.size()=" + reversedUrlDomainList.size()
						+ ",competitorUrlHtmlClickHouseEntityMap.size()=" + competitorUrlHtmlClickHouseEntityMap.size() + ",elapsed(ms.)="
						+ (System.currentTimeMillis() - startTimestamp));
		return competitorUrlHtmlClickHouseEntityMap;
	}

	public boolean updateUrlMetricsEntityWithHistoricalCrawledData(String ip, int crawlType, UrlMetricsEntityV3 urlMetricsEntity,
	                                                               Map<String, HtmlClickHouseEntity> htmlClickHouseEntityMap) {
		String md5HashCode = null;
		HtmlClickHouseEntity htmlClickHouseEntity = null;
		Integer httpStatusCode = null;
		int httpStatusCodeNumber = 0;
		boolean isHistoricalDataAvailable = false;
		String h2Hash = null;
		int h2Total = 0;

		try {
			md5HashCode = Md5Util.Md5(StringUtils.trim(urlMetricsEntity.getUrl()));

			// last historical data in 'target_url_html' clickhouse table
			if (htmlClickHouseEntityMap.containsKey(md5HashCode) == false) {
				isHistoricalDataAvailable = false;
			} else if (htmlClickHouseEntityMap.containsKey(md5HashCode) == true) {
				isHistoricalDataAvailable = true;
				urlMetricsEntity.setTargetUrlHtmlDailyDataInd(true);
				htmlClickHouseEntity = htmlClickHouseEntityMap.get(md5HashCode);
				if (htmlClickHouseEntity != null) {
					try {
						if (crawlType == IConstants.CRAWL_TYPE_TARGET_URL_HTML) {
							//if (isDebug == true) {
							//	FormatUtils.getInstance().logMemoryUsage("updateUrlMetricsEntityWithHistoricalCrawledData() ip=" + ip + ",url="
							//			+ urlMetricsEntity.getUrl() + ",previous crawl timestamp=" + htmlClickHouseEntity.getCrawlTimestamp());
							//}
							if (htmlClickHouseEntity.getCrawlTimestamp() != null) {
								urlMetricsEntity.setCrawl_timestamp(
										DateFormatUtils.format(htmlClickHouseEntity.getCrawlTimestamp(), IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS));
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					httpStatusCode = htmlClickHouseEntity.getHttpStatusCode();
					if (httpStatusCode != null) {

						httpStatusCodeNumber = httpStatusCode.intValue();

						// track_date
						urlMetricsEntity.setTargetUrlHtmlTrackDate(DateFormatUtils.format(htmlClickHouseEntity.getTrackDate(), IConstants.DATE_FORMAT_YYYY_MM_DD));
						//if (isDebug == true) {
						//	Date testDate = DateUtils.addDays(htmlClickHouseEntity.getTrackDate(), -1);
						//	urlMetricsEntity.setTargetUrlHtmlTrackDate(DateFormatUtils.format(testDate, IConstants.DATE_FORMAT_YYYY_MM_DD));
						//}

						// response_code
						urlMetricsEntity.setResponse_code(htmlClickHouseEntity.getCrawlerResponse().getResponse_code());

						if (httpStatusCodeNumber == 0) {
							// when historical HTTP status code is 0, no need to update UrlMetricsEntity with historical crawled data

						} else if (httpStatusCodeNumber > 399) {
							// when historical HTTP status code is 4xx or 5xx, no need to update UrlMetricsEntity with historical crawled data
						}
						// when historical HTTP status code is 3xx
						// when historical HTTP status code is 200
						else if (httpStatusCodeNumber == 200 || (httpStatusCodeNumber >= 300 && httpStatusCodeNumber <= 399)) {

							// amphtml_href
							urlMetricsEntity.setAmphtml_href(htmlClickHouseEntity.getCrawlerResponse().getAmphtml_href());

							// amphtml_flag
							urlMetricsEntity.setAmphtml_flag(htmlClickHouseEntity.getCrawlerResponse().getAmphtml_flag());

							// analyzed_url_s
							urlMetricsEntity.setAnalyzed_url_s(htmlClickHouseEntity.getCrawlerResponse().getAnalyzed_url_s());

							// analyzed_url_flg_s
							urlMetricsEntity.setAnalyzed_url_flg_s(htmlClickHouseEntity.getCrawlerResponse().getAnalyzed_url_flg_s());

							// archive_flg
							urlMetricsEntity.setArchive_flg(htmlClickHouseEntity.getCrawlerResponse().getArchive_flg());

							// archive_flg_x_tag
							urlMetricsEntity.setArchive_flg_x_tag(htmlClickHouseEntity.getCrawlerResponse().getArchive_flg_x_tag());

							// blocked_by_robots
							urlMetricsEntity.setBlocked_by_robots(htmlClickHouseEntity.getCrawlerResponse().getBlocked_by_robots());

							// canonical
							urlMetricsEntity.setCanonical(htmlClickHouseEntity.getCrawlerResponse().getCanonical());

							// canonical_flg
							urlMetricsEntity.setCanonical_flg(htmlClickHouseEntity.getCrawlerResponse().getCanonical_flg());

							// canonical_header_flag
							urlMetricsEntity.setCanonical_header_flag(htmlClickHouseEntity.getCrawlerResponse().getCanonical_header_flag());

							// canonical_header_type
							urlMetricsEntity.setCanonical_header_type(htmlClickHouseEntity.getCrawlerResponse().getCanonical_header_type());

							// canonical_type
							urlMetricsEntity.setCanonical_type(htmlClickHouseEntity.getCrawlerResponse().getCanonical_type());

							// canonical_url_is_consistent
							urlMetricsEntity.setCanonical_url_is_consistent(htmlClickHouseEntity.getCrawlerResponse().getCanonical_url_is_consistent());

							// content_type
							urlMetricsEntity.setContent_type(htmlClickHouseEntity.getCrawlerResponse().getContent_type());

							// description
							urlMetricsEntity.setDescription(htmlClickHouseEntity.getCrawlerResponse().getDescription());

							// description_flg
							urlMetricsEntity.setDescription_flg(htmlClickHouseEntity.getCrawlerResponse().getDescription_flg());

							// description_length
							urlMetricsEntity.setDescription_length(htmlClickHouseEntity.getCrawlerResponse().getDescription_length());

							// description_simhash
							urlMetricsEntity.setDescription_simhash(htmlClickHouseEntity.getCrawlerResponse().getDescription_simhash());

							// error_message
							urlMetricsEntity.setError_message(htmlClickHouseEntity.getCrawlerResponse().getError_message());

							// final_response_code
							urlMetricsEntity.setFinal_response_code(htmlClickHouseEntity.getCrawlerResponse().getFinal_response_code());

							// follow_flg
							urlMetricsEntity.setFollow_flg(htmlClickHouseEntity.getCrawlerResponse().getFollow_flg());

							// follow_flg_x_tag
							urlMetricsEntity.setFollow_flg_x_tag(htmlClickHouseEntity.getCrawlerResponse().getFollow_flg_x_tag());

							// h1
							urlMetricsEntity.setH1_array(htmlClickHouseEntity.getCrawlerResponse().getH1());

							// h1_count
							urlMetricsEntity.setH1_count(htmlClickHouseEntity.getCrawlerResponse().getH1_count());

							// h1_flg
							urlMetricsEntity.setH1_flg(htmlClickHouseEntity.getCrawlerResponse().getH1_flg());

							// h1_length
							urlMetricsEntity.setH1_length(htmlClickHouseEntity.getCrawlerResponse().getH1_length());

							// h1_md5
							urlMetricsEntity.setH1_md5(htmlClickHouseEntity.getCrawlerResponse().getH1_md5());

							// h2_array
							// h2_hash
							// h2_total
							if (StringUtils.equalsIgnoreCase(urlMetricsEntity.getUrl(), IConstants.URL_WITH_TOO_MANY_H2) == false) {
								urlMetricsEntity.setH2_array(htmlClickHouseEntity.getCrawlerResponse().getH2());
								h2Hash = null;
								h2Total = 0;
								if (htmlClickHouseEntity.getCrawlerResponse().getH2() != null && htmlClickHouseEntity.getCrawlerResponse().getH2().length > 0) {
									h2Total = htmlClickHouseEntity.getCrawlerResponse().getH2().length;
									h2Hash = CrawlerUtils.getInstance().getHashCodeOfStringArray(htmlClickHouseEntity.getCrawlerResponse().getH2());
								}
								urlMetricsEntity.setH2_hash(h2Hash);
								urlMetricsEntity.setH2_total(h2Total);
							}

							// header_noarchive
							urlMetricsEntity.setHeader_noarchive(htmlClickHouseEntity.getCrawlerResponse().getHeader_noarchive());

							// header_nofollow
							urlMetricsEntity.setHeader_nofollow(htmlClickHouseEntity.getCrawlerResponse().getHeader_nofollow());

							// header_noindex
							urlMetricsEntity.setHeader_noindex(htmlClickHouseEntity.getCrawlerResponse().getHeader_noindex());

							// header_noodp
							urlMetricsEntity.setHeader_noodp(htmlClickHouseEntity.getCrawlerResponse().getHeader_noodp());

							// header_nosnippet
							urlMetricsEntity.setHeader_nosnippet(htmlClickHouseEntity.getCrawlerResponse().getHeader_nosnippet());

							// header_noydir
							urlMetricsEntity.setHeader_noydir(htmlClickHouseEntity.getCrawlerResponse().getHeader_noydir());

							// hreflang_errors
							urlMetricsEntity.setHreflang_errors(htmlClickHouseEntity.getCrawlerResponse().getHreflang_errors());

							// hreflang_links_out_count
							urlMetricsEntity.setHreflang_links_out_count(htmlClickHouseEntity.getCrawlerResponse().getHreflang_links_out_count());

							// hreflang_url_count
							urlMetricsEntity.setHreflang_url_count(htmlClickHouseEntity.getCrawlerResponse().getHreflang_url_count());

							// index_flg
							urlMetricsEntity.setIndex_flg(htmlClickHouseEntity.getCrawlerResponse().getIndex_flg());

							// index_flg_x_tag
							urlMetricsEntity.setIndex_flg_x_tag(htmlClickHouseEntity.getCrawlerResponse().getIndex_flg_x_tag());

							// indexable
							urlMetricsEntity.setIndexable(htmlClickHouseEntity.getCrawlerResponse().getIndexable());

							// insecure_resources_flag
							urlMetricsEntity.setInsecure_resources_flag(htmlClickHouseEntity.getCrawlerResponse().getInsecure_resources_flag());

							// meta_charset
							urlMetricsEntity.setMeta_charset(htmlClickHouseEntity.getCrawlerResponse().getMeta_charset());

							// meta_content_type
							urlMetricsEntity.setMeta_content_type(htmlClickHouseEntity.getCrawlerResponse().getMeta_content_type());

							// meta_disabled_sitelinks
							urlMetricsEntity.setMeta_disabled_sitelinks(htmlClickHouseEntity.getCrawlerResponse().getMeta_disabled_sitelinks());

							// meta_noodp
							urlMetricsEntity.setMeta_noodp(htmlClickHouseEntity.getCrawlerResponse().getMeta_noodp());

							// meta_nosnippet
							urlMetricsEntity.setMeta_nosnippet(htmlClickHouseEntity.getCrawlerResponse().getMeta_nosnippet());

							// meta_noydir
							urlMetricsEntity.setMeta_noydir(htmlClickHouseEntity.getCrawlerResponse().getMeta_noydir());

							// meta_redirect
							urlMetricsEntity.setMeta_redirect(htmlClickHouseEntity.getCrawlerResponse().getMeta_redirect());

							// mixed_redirects
							urlMetricsEntity.setMixed_redirects(htmlClickHouseEntity.getCrawlerResponse().getMixed_redirects());

							// mobile_rel_alternate_url_is_consistent
							urlMetricsEntity
									.setMobile_rel_alternate_url_is_consistent(htmlClickHouseEntity.getCrawlerResponse().getMobile_rel_alternate_url_is_consistent());

							// noodp
							urlMetricsEntity.setNoodp(htmlClickHouseEntity.getCrawlerResponse().getNoodp());

							// nosnippet
							urlMetricsEntity.setNosnippet(htmlClickHouseEntity.getCrawlerResponse().getNosnippet());

							// noydir
							urlMetricsEntity.setNoydir(htmlClickHouseEntity.getCrawlerResponse().getNoydir());

							// og_markup_flag
							urlMetricsEntity.setOg_markup_flag(htmlClickHouseEntity.getCrawlerResponse().getOg_markup_flag());

							// og_markup_length
							urlMetricsEntity.setOg_markup_length(htmlClickHouseEntity.getCrawlerResponse().getOg_markup_length());

							// outlink_count
							urlMetricsEntity.setOutlink_count(htmlClickHouseEntity.getCrawlerResponse().getOutlink_count());

							// redirect_blocked
							urlMetricsEntity.setRedirect_blocked(htmlClickHouseEntity.getCrawlerResponse().getRedirect_blocked());

							// redirect_blocked_reason
							urlMetricsEntity.setRedirect_blocked_reason(htmlClickHouseEntity.getCrawlerResponse().getRedirect_blocked_reason());

							// redirect_flg
							urlMetricsEntity.setRedirect_flg(htmlClickHouseEntity.getCrawlerResponse().getRedirect_flg());

							// redirect_times
							urlMetricsEntity.setRedirect_times(htmlClickHouseEntity.getCrawlerResponse().getRedirect_times());

							// redirect_final_url
							urlMetricsEntity.setRedirectFinalUrl(htmlClickHouseEntity.getCrawlerResponse().getRedirect_final_url());

							// robots
							urlMetricsEntity.setRobots(htmlClickHouseEntity.getCrawlerResponse().getRobots());

							// robots_contents
							urlMetricsEntity.setRobots_contents(htmlClickHouseEntity.getCrawlerResponse().getRobots_contents());

							// robots_contents_x_tag
							urlMetricsEntity.setRobots_contents_x_tag(htmlClickHouseEntity.getCrawlerResponse().getRobots_contents_x_tag());

							// robots_flg
							urlMetricsEntity.setRobots_flg(htmlClickHouseEntity.getCrawlerResponse().getRobots_flg());

							// robots_flg_x_tag
							urlMetricsEntity.setRobots_flg_x_tag(htmlClickHouseEntity.getCrawlerResponse().getRobots_flg_x_tag());

							// title
							urlMetricsEntity.setTitle(htmlClickHouseEntity.getCrawlerResponse().getTitle());

							// title_flg
							urlMetricsEntity.setTitle_flg(htmlClickHouseEntity.getCrawlerResponse().getTitle_flg());

							// title_length
							urlMetricsEntity.setTitle_length(htmlClickHouseEntity.getCrawlerResponse().getTitle_length());

							// title_md5
							urlMetricsEntity.setTitle_md5(htmlClickHouseEntity.getCrawlerResponse().getTitle_md5());

							// title_simhash
							urlMetricsEntity.setTitle_simhash(htmlClickHouseEntity.getCrawlerResponse().getTitle_simhash());

							// viewport_flag
							urlMetricsEntity.setViewport_flag(htmlClickHouseEntity.getCrawlerResponse().getViewport_flag());

							// page_analysis_results
							urlMetricsEntity.setPageAnalysisResultArray(htmlClickHouseEntity.getPageAnalysisResultArray());
							if (htmlClickHouseEntity.getPageAnalysisResultArray() != null && htmlClickHouseEntity.getPageAnalysisResultArray().length > 0) {
								urlMetricsEntity.setPageAnalysisResultInd(true);
							} else {
								urlMetricsEntity.setPageAnalysisResultInd(false);
							}

							// change_tracking_hash_cd_json
							urlMetricsEntity.setChangeTrackingHashCdJsonArray(htmlClickHouseEntity.getChangeTrackingHashCdJsonArray());

							// base_tag
							urlMetricsEntity.setBase_tag(htmlClickHouseEntity.getCrawlerResponse().getBase_tag());

							// base_tag_flag
							urlMetricsEntity.setBase_tag_flag(htmlClickHouseEntity.getCrawlerResponse().getBase_tag_flag());

							// base_tag_target
							urlMetricsEntity.setBase_tag_target(htmlClickHouseEntity.getCrawlerResponse().getBase_tag_target());

							// response_headers (names)
							urlMetricsEntity.setResponse_header_names(
									CrawlerUtils.getInstance().getResponseHeaderNames(htmlClickHouseEntity.getCrawlerResponse().getResponse_headers()));
							urlMetricsEntity.setResponse_headers(htmlClickHouseEntity.getCrawlerResponse().getResponse_headers());

							// viewport_content
							urlMetricsEntity.setViewportContent(htmlClickHouseEntity.getCrawlerResponse().getViewport_content());
						}
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return isHistoricalDataAvailable;
	}

	public boolean checkIfStructuredDataAvailable(StructuredData structuredData) {
		boolean output = false;
		if (structuredData != null && structuredData.getData() != null) {
			if (structuredData.getData().getCheck_structured_data() != null && structuredData.getData().getCheck_structured_data().length > 0) {
				output = true;
			} else if (structuredData.getData().getValidate_structured_data() != null && structuredData.getData().getValidate_structured_data().length > 0) {
				output = true;
			}
		}
		return output;
	}

	public String extractReversedUrlDomain(String urlString) {
		String reversedUrlDomain = null;
		URL url = null;
		String hostname = null;
		try {
			url = new URL(urlString);
			hostname = url.getHost();
			reversedUrlDomain = getReversedDomainName(hostname);
		} catch (Exception e) {
			FormatUtils.getInstance().logMemoryUsage("extractReversedUrlDomain() error--urlString=" + urlString + ",e.getMessage()=" + e.getMessage());
			e.printStackTrace();
		}
		return reversedUrlDomain;
	}

	public String getSortedCharactersHashCode(Object inputObject) {
		String hashCode = null;
		String inputString = null;
		byte[] unsortedByteArray = null;
		List<Byte> byteList = null;
		String sortedCharactersString = null;
		try {
			if (inputObject != null) {
				inputString = inputObject.toString();
				if (StringUtils.isNotBlank(inputString)) {
					unsortedByteArray = inputString.getBytes(IConstants.UTF8);
					byteList = new ArrayList<Byte>();
					for (Byte testByte : unsortedByteArray) {
						byteList.add(testByte);
					}
					Collections.sort(byteList);
					sortedCharactersString = byteList.toString();
					hashCode = Md5Util.Md5(sortedCharactersString);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return hashCode;
	}

	public boolean checkIfJsonDataAvailable(String json) {
		boolean output = false;
		if (StringUtils.isNotBlank(json) == true && StringUtils.equalsIgnoreCase(json, IConstants.OPEN_CLOSE_BRACKET) == false) {
			output = true;
		}
		return output;
	}

	public boolean isDifferent(String value1, String value2) {
		boolean output = false;
		if (StringUtils.isBlank(value1) && StringUtils.isBlank(value2)) {
			output = false;
		} else if (StringUtils.isBlank(value1) && StringUtils.isNotBlank(value2)) {
			output = true;
		} else if (StringUtils.isNotBlank(value1) && StringUtils.isBlank(value2)) {
			output = true;
		} else if (StringUtils.isNotBlank(value1) && StringUtils.isNotBlank(value2) && !StringUtils.equalsIgnoreCase(value1, value2)) {
			output = true;
		}
		return output;
	}

	public boolean isDifferent(Integer value1, Integer value2) {
		boolean output = false;
		if (value1 == null && value2 == null) {
			output = false;
		} else if (value1 != null && value2 == null) {
			output = true;
		} else if (value1 == null && value2 != null) {
			output = true;
		} else if (value1.intValue() != value2.intValue()) {
			output = true;
		}
		return output;
	}

	public boolean isDifferent(Boolean value1, Boolean value2) {
		boolean output = false;
		if (value1 == null && value2 == null) {
			output = false;
		} else if (value1 != null && value2 == null) {
			output = true;
		} else if (value1 == null && value2 != null) {
			output = true;
		} else if (StringUtils.equalsIgnoreCase(value1.toString(), value2.toString()) == false) {
			output = true;
		}
		return output;
	}

	public Integer getInternalLinkCount(String urlString, CrawlerResponse crawlerResponse) {
		Integer internalLinkCount = null;
		String destinationUrlString = null;
		int testCount = 0;
		URL url = null;
		String hostname = null;
		try {
			if (crawlerResponse != null && crawlerResponse.getPage_link() != null && crawlerResponse.getPage_link().length > 0) {
				url = new URL(urlString);
				hostname = url.getHost();
				for (PageLink pageLink : crawlerResponse.getPage_link()) {
					destinationUrlString = pageLink.getDestination_url();
					if (isOwnDomainLink(destinationUrlString, hostname) == true) {
						testCount++;
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		internalLinkCount = testCount;
		return internalLinkCount;
	}

	private boolean isOwnDomainLink(String linkUrl, String ownDomain) {
		return isSameDomain(linkUrl, ownDomain);
	}

	private boolean isSameDomain(String sourceDomain, String ownDomain) {
		sourceDomain = getDomainByUrl(sourceDomain, true);
		ownDomain = getDomainByUrl(ownDomain, true);
		if (StringUtils.equalsIgnoreCase(sourceDomain, ownDomain)) {
			return true;
		}
		if (StringUtils.endsWithIgnoreCase(sourceDomain, "." + ownDomain)) {
			return true;
		}
		return false;
	}

	private String getDomainByUrl(String url, boolean remove3w) {
		String result = url;

		// remove 'http://'
		if (StringUtils.contains(result, "://")) {
			result = StringUtils.substringAfter(result, "://");
		}

		// remove the string after domain
		result = StringUtils.substringBefore(result, "/");

		// remove 'www.' if the domain string contains it
		if (remove3w && StringUtils.isNotBlank(result) && result.startsWith("www.")) {
			result = StringUtils.substringAfter(result, "www.");
		}

		return result;
	}

	public Set<String> getTargetUrlHashCodeSet(int domainId, String domainName, List<TargetUrlEntity> targetUrlEntityList) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage(
				"getTargetUrlHashCodeSet() begins. domainId=" + domainId + ",domainName=" + domainName + ",targetUrlEntityList.size()=" + targetUrlEntityList.size());
		Set<String> hashCodeSet = new HashSet<String>();
		String md5HashCode = null;
		for (TargetUrlEntity targetUrlEntity : targetUrlEntityList) {
			md5HashCode = Md5Util.Md5(StringUtils.trim(targetUrlEntity.getUrl()));
			hashCodeSet.add(md5HashCode);
		}
		FormatUtils.getInstance().logMemoryUsage("getTargetUrlHashCodeSet() ends. domainId=" + domainId + ",domainName=" + domainName + ",hashCodeSet.size()="
				+ hashCodeSet.size() + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return hashCodeSet;
	}

	// check if client domain requires target URLs auto-association
	public boolean checkIfAutoAssociateTargetUrlsRequired(int domainId, String urlCrawlParameters) {
		boolean output = false;
		if (domainId == 4765) {
			return false;
		}
		UrlCrawlParametersVO[] urlCrawlParametersVoArray = null;
		UrlCrawlParametersVO urlCrawlParametersVO = null;

		// by default, auto-associate target URLs is off (ie. when t_own_domain's 'url_crawl_parameters' is null
		// auto-associate target URLs is off when 'url_crawl_parameters' contains {"type":"autoAssociateTargetUrls","data":"false"}
		// auto-associate target URLs is on when 'url_crawl_parameters' contains {"type":"autoAssociateTargetUrls","data":"true"}

		if (StringUtils.isNotBlank(urlCrawlParameters)) {
			urlCrawlParametersVoArray = new Gson().fromJson(urlCrawlParameters, UrlCrawlParametersVO[].class);
			for (int idx = 0; idx < urlCrawlParametersVoArray.length; idx++) {
				urlCrawlParametersVO = urlCrawlParametersVoArray[idx];
				// auto associate target URLs
				if (urlCrawlParametersVO.getType().equalsIgnoreCase(IConstants.AUTO_ASSOCIATE_TARGET_URLS)) {
					output = BooleanUtils.toBoolean(urlCrawlParametersVO.getData(), "true", "false");
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("checkIfAutoAssociateTargetUrlsRequired() domainId=" + domainId + ",output=" + output);
		return output;
	}

	public List<String> getExpediaDomainNames() {
		List<String> domainList = new ArrayList<String>();
		List<String> testDomainNameList = null;

		// retrieve 'Expedia' domain names
		testDomainNameList = ownDomainEntityDAO.getDomainsByCompanyName(IConstants.COMPANY_NAME_EXPEDIA);
		if (testDomainNameList != null && testDomainNameList.size() > 0) {
			domainList.addAll(testDomainNameList);
		}

		// retrieve 'CarRentals.com' domain names
		testDomainNameList = ownDomainEntityDAO.getDomainsByCompanyName(IConstants.COMPANY_NAME_CARRENTALS_COM);
		if (testDomainNameList != null && testDomainNameList.size() > 0) {
			domainList.addAll(testDomainNameList);
		}

		//if (domainList != null && domainList.size() > 0) {
		//	for (String domain : domainList) {
		//		FormatUtils.getInstance().logMemoryUsage("getExpediaDomainNames() domain=" + domain);
		//	}
		//}
		return domainList;
	}

	public Map<String, String> getDefaultRequestHeaders(int domainId) {
		Map<String, String> defaultRequestHeaders = null;
		String decryptedString = null;
		Gson gson = new Gson();
		KeyValuePairValueObject[] keyValuePairValueObjectArray = null;
		CommonParamEntity commonParamEntity = commonParamDAO.getUiJsonByFuncNameTitle(domainId, IConstants.FUNC_NAME_SITE_CRAWL_HEADERS,
				IConstants.TITLE_DEFAULT_REQUEST_HEADERS);
		if (commonParamEntity != null) {
			//FormatUtils.getInstance()
			//		.logMemoryUsage("getDefaultRequestHeaders() domainId=" + domainId + ",commonParamEntity.getUiJson()=" + commonParamEntity.getUiJson());
			if (StringUtils.isNotBlank(commonParamEntity.getUiJson())) {
				// https://www.wrike.com/open.htm?id=891428066
				if (commonParamEntity.getVersion() != null && commonParamEntity.getVersion() == 2) {
					decryptedString = ScStringEncryptor.decrypt(commonParamEntity.getUiJson());
					//FormatUtils.getInstance().logMemoryUsage("getDefaultRequestHeaders() domainId=" + domainId + ",decryptedString=" + decryptedString);
					keyValuePairValueObjectArray = gson.fromJson(decryptedString, KeyValuePairValueObject[].class);
				} else {
					keyValuePairValueObjectArray = gson.fromJson(commonParamEntity.getUiJson(), KeyValuePairValueObject[].class);
				}
				if (keyValuePairValueObjectArray != null && keyValuePairValueObjectArray.length > 0) {
					defaultRequestHeaders = new HashMap<String, String>();
					for (KeyValuePairValueObject keyValuePairValueObject : keyValuePairValueObjectArray) {
						//FormatUtils.getInstance().logMemoryUsage("getDefaultRequestHeaders() domainId=" + domainId + ",keyValuePairValueObject key="
						//		+ keyValuePairValueObject.getKey() + ",value=" + keyValuePairValueObject.getValue());
						defaultRequestHeaders.put(keyValuePairValueObject.getKey(), keyValuePairValueObject.getValue());
					}
				} else {
					//FormatUtils.getInstance().logMemoryUsage("getDefaultRequestHeaders() domainId=" + domainId + ",defaultRequestHeaderArray is empty.");
				}
			} else {
				//FormatUtils.getInstance().logMemoryUsage("getDefaultRequestHeaders() domainId=" + domainId + ",commonParamEntity.getUiJson() is empty.");
			}
		} else {
			//FormatUtils.getInstance().logMemoryUsage("getDefaultRequestHeaders() domainId=" + domainId + ",commonParamEntity is null.");
		}
		return defaultRequestHeaders;
	}

	public String createControllerMessageBodyInJsonFormat(int domainId, String domainSpecificQueueName, int delayInSecondsPerHttpRequest, int maxConcurrentCrawlThreads,
	                                                      String specificUserAgent, Boolean enableJavascriptCrawl, Boolean enableScrapyCrawl, String region, Integer javascriptTimeoutInSecond) {
		String response = null;
		UrlCrawlParametersVO urlCrawlParametersVO = null;
		UrlCrawlParametersVO[] urlCrawlParametersVoArray = null;
		String value = null;
		List<UrlCrawlParametersVO> urlCrawlParametersVoList = new ArrayList<UrlCrawlParametersVO>();

		// domain specific queue name
		urlCrawlParametersVO = new UrlCrawlParametersVO();
		urlCrawlParametersVO.setType(IConstants.QUEUE_NAME);
		urlCrawlParametersVO.setData(domainSpecificQueueName);
		urlCrawlParametersVoList.add(urlCrawlParametersVO);

		// delay in seconds per HTTP request
		urlCrawlParametersVO = new UrlCrawlParametersVO();
		urlCrawlParametersVO.setType(IConstants.DELAY_IN_SECONDS);
		urlCrawlParametersVO.setData(String.valueOf(delayInSecondsPerHttpRequest));
		urlCrawlParametersVoList.add(urlCrawlParametersVO);

		// maximum number of current threads per queue
		urlCrawlParametersVO = new UrlCrawlParametersVO();
		urlCrawlParametersVO.setType(IConstants.MAX_CONCURRENT_THREADS);
		urlCrawlParametersVO.setData(String.valueOf(maxConcurrentCrawlThreads));
		urlCrawlParametersVoList.add(urlCrawlParametersVO);

		// domain specific user agent
		if (StringUtils.isNotBlank(specificUserAgent)) {
			urlCrawlParametersVO = new UrlCrawlParametersVO();
			urlCrawlParametersVO.setType(IConstants.USER_AGENT);
			urlCrawlParametersVO.setData(specificUserAgent);
			urlCrawlParametersVoList.add(urlCrawlParametersVO);
		}

		// enable Javascript crawl
		if (enableJavascriptCrawl != null) {
			urlCrawlParametersVO = new UrlCrawlParametersVO();
			urlCrawlParametersVO.setType(IConstants.ENABLE_JAVASCRIPT_CRAWL);
			urlCrawlParametersVO.setData(enableJavascriptCrawl.toString());
			urlCrawlParametersVoList.add(urlCrawlParametersVO);
		}

		// enable Scrapy crawl
		if (enableScrapyCrawl != null) {
			urlCrawlParametersVO = new UrlCrawlParametersVO();
			urlCrawlParametersVO.setType(IConstants.ENABLE_SCRAPY_CRAWL);
			urlCrawlParametersVO.setData(enableScrapyCrawl.toString());
			urlCrawlParametersVoList.add(urlCrawlParametersVO);
		}

		// region
		if (StringUtils.isNotBlank(region)) {
			urlCrawlParametersVO = new UrlCrawlParametersVO();
			urlCrawlParametersVO.setType(IConstants.REGION);
			urlCrawlParametersVO.setData(region);
			urlCrawlParametersVoList.add(urlCrawlParametersVO);
		}

		// javascriptTimeoutInSecond
		if (javascriptTimeoutInSecond != null && javascriptTimeoutInSecond.intValue() > 0) {
			urlCrawlParametersVO = new UrlCrawlParametersVO();
			urlCrawlParametersVO.setType(IConstants.JAVASCRIPT_TIMEOUT_IN_SECOND);
			urlCrawlParametersVO.setData(String.valueOf(javascriptTimeoutInSecond));
			urlCrawlParametersVoList.add(urlCrawlParametersVO);
		}

		Map<String, String> defaultRequestHeaders = getDefaultRequestHeaders(domainId);
		if (defaultRequestHeaders != null && defaultRequestHeaders.size() > 0) {
			for (String key : defaultRequestHeaders.keySet()) {
				value = defaultRequestHeaders.get(key);
				if (StringUtils.isNotBlank(value)) {
					urlCrawlParametersVO = new UrlCrawlParametersVO();
					urlCrawlParametersVO.setType(IConstants.PAGE_CRAWLER_API_REQUEST_HEADER + IConstants.DASH + key);
					urlCrawlParametersVO.setData(value);
					urlCrawlParametersVoList.add(urlCrawlParametersVO);
				}
			}
		}

		if (urlCrawlParametersVoList != null && urlCrawlParametersVoList.size() > 0) {
			urlCrawlParametersVoArray = urlCrawlParametersVoList.toArray(new UrlCrawlParametersVO[0]);
			response = new Gson().toJson(urlCrawlParametersVoArray, UrlCrawlParametersVO[].class);
		}
		return response;
	}

	public UrlMetricsEntityV3 createUrlMetrics(String url, HtmlClickHouseEntity htmlClickHouseEntity) {
		final UrlMetricsEntityV3 urlMetricsEntityV3 = new UrlMetricsEntityV3();
		urlMetricsEntityV3.setUrl(url);
		try {
			if (htmlClickHouseEntity != null) {
				urlMetricsEntityV3.setTargetUrlHtmlDailyDataInd(true);
				if (htmlClickHouseEntity.getCrawlTimestamp() != null) {
					urlMetricsEntityV3.setCrawl_timestamp(DateFormatUtils.format(htmlClickHouseEntity.getCrawlTimestamp(), IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS));
				}
				Integer httpStatusCode = htmlClickHouseEntity.getHttpStatusCode();
				if (httpStatusCode != null) {

					// track_date
					urlMetricsEntityV3.setTargetUrlHtmlTrackDate(DateFormatUtils.format(htmlClickHouseEntity.getTrackDate(), IConstants.DATE_FORMAT_YYYY_MM_DD));

					final CrawlerResponse crawlerResponse = htmlClickHouseEntity.getCrawlerResponse();
					urlMetricsEntityV3.setResponse_code(crawlerResponse.getResponse_code());

					// response_code
					// when historical HTTP status code is 3xx
					// when historical HTTP status code is 200
					if (httpStatusCode == 200 || (httpStatusCode >= 300 && httpStatusCode <= 399)) {

						// amphtml_flag
						urlMetricsEntityV3.setAmphtml_flag(crawlerResponse.getAmphtml_flag());

						// analyzed_url_flg_s
						urlMetricsEntityV3.setAnalyzed_url_flg_s(crawlerResponse.getAnalyzed_url_flg_s());

						// archive_flg
						urlMetricsEntityV3.setArchive_flg(crawlerResponse.getArchive_flg());

						// archive_flg_x_tag
						urlMetricsEntityV3.setArchive_flg_x_tag(crawlerResponse.getArchive_flg_x_tag());

						// blocked_by_robots
						urlMetricsEntityV3.setBlocked_by_robots(crawlerResponse.getBlocked_by_robots());

						// canonical_flg
						urlMetricsEntityV3.setCanonical_flg(crawlerResponse.getCanonical_flg());

						// canonical_header_flag
						urlMetricsEntityV3.setCanonical_header_flag(crawlerResponse.getCanonical_header_flag());

						// canonical_header_type
						urlMetricsEntityV3.setCanonical_header_type(crawlerResponse.getCanonical_header_type());

						// canonical_type
						urlMetricsEntityV3.setCanonical_type(crawlerResponse.getCanonical_type());

						// canonical_url_is_consistent
						urlMetricsEntityV3.setCanonical_url_is_consistent(crawlerResponse.getCanonical_url_is_consistent());

						// content_type
						urlMetricsEntityV3.setContent_type(crawlerResponse.getContent_type());

						// description_flg
						urlMetricsEntityV3.setDescription_flg(crawlerResponse.getDescription_flg());

						// description_length
						urlMetricsEntityV3.setDescription_length(crawlerResponse.getDescription_length());

						// description_simhash
						urlMetricsEntityV3.setDescription_simhash(crawlerResponse.getDescription_simhash());

						// error_message
						urlMetricsEntityV3.setError_message(crawlerResponse.getError_message());

						// final_response_code
						urlMetricsEntityV3.setFinal_response_code(crawlerResponse.getFinal_response_code());

						// follow_flg
						urlMetricsEntityV3.setFollow_flg(crawlerResponse.getFollow_flg());

						// follow_flg_x_tag
						urlMetricsEntityV3.setFollow_flg_x_tag(crawlerResponse.getFollow_flg_x_tag());

						// h1_count
						urlMetricsEntityV3.setH1_count(crawlerResponse.getH1_count());

						// h1_flg
						urlMetricsEntityV3.setH1_flg(crawlerResponse.getH1_flg());

						// h1_length
						urlMetricsEntityV3.setH1_length(crawlerResponse.getH1_length());

						// h1_md5
						urlMetricsEntityV3.setH1_md5(crawlerResponse.getH1_md5());

						// h2_array
						// h2_hash
						// h2_total
						if (!StringUtils.equalsIgnoreCase(urlMetricsEntityV3.getUrl(), IConstants.URL_WITH_TOO_MANY_H2)) {
							//urlMetricsEntity.setH2_array(htmlClickHouseEntity.getCrawlerResponse().getH2());
							String h2Hash = null;
							int h2Total = 0;
							if (crawlerResponse.getH2() != null && crawlerResponse.getH2().length > 0) {
								h2Total = crawlerResponse.getH2().length;
								h2Hash = CrawlerUtils.getInstance().getHashCodeOfStringArray(crawlerResponse.getH2());
							}
							urlMetricsEntityV3.setH2_hash(h2Hash);
							urlMetricsEntityV3.setH2_total(h2Total);
						}

						// header_noarchive
						urlMetricsEntityV3.setHeader_noarchive(crawlerResponse.getHeader_noarchive());

						// header_nofollow
						urlMetricsEntityV3.setHeader_nofollow(crawlerResponse.getHeader_nofollow());

						// header_noindex
						urlMetricsEntityV3.setHeader_noindex(crawlerResponse.getHeader_noindex());

						// header_noodp
						urlMetricsEntityV3.setHeader_noodp(crawlerResponse.getHeader_noodp());

						// header_nosnippet
						urlMetricsEntityV3.setHeader_nosnippet(crawlerResponse.getHeader_nosnippet());

						// header_noydir
						urlMetricsEntityV3.setHeader_noydir(crawlerResponse.getHeader_noydir());

						// hreflang_links_out_count
						urlMetricsEntityV3.setHreflang_links_out_count(crawlerResponse.getHreflang_links_out_count());

						// hreflang_url_count
						urlMetricsEntityV3.setHreflang_url_count(crawlerResponse.getHreflang_url_count());

						// index_flg
						urlMetricsEntityV3.setIndex_flg(crawlerResponse.getIndex_flg());

						// index_flg_x_tag
						urlMetricsEntityV3.setIndex_flg_x_tag(crawlerResponse.getIndex_flg_x_tag());

						// indexable
						urlMetricsEntityV3.setIndexable(crawlerResponse.getIndexable());

						// insecure_resources_flag
						urlMetricsEntityV3.setInsecure_resources_flag(crawlerResponse.getInsecure_resources_flag());

						// meta_charset
						urlMetricsEntityV3.setMeta_charset(crawlerResponse.getMeta_charset());

						// meta_content_type
						urlMetricsEntityV3.setMeta_content_type(crawlerResponse.getMeta_content_type());

						// meta_disabled_sitelinks
						urlMetricsEntityV3.setMeta_disabled_sitelinks(crawlerResponse.getMeta_disabled_sitelinks());

						// meta_noodp
						urlMetricsEntityV3.setMeta_noodp(crawlerResponse.getMeta_noodp());

						// meta_nosnippet
						urlMetricsEntityV3.setMeta_nosnippet(crawlerResponse.getMeta_nosnippet());

						// meta_noydir
						urlMetricsEntityV3.setMeta_noydir(crawlerResponse.getMeta_noydir());

						// meta_redirect
						urlMetricsEntityV3.setMeta_redirect(crawlerResponse.getMeta_redirect());

						// mixed_redirects
						urlMetricsEntityV3.setMixed_redirects(crawlerResponse.getMixed_redirects());

						// mobile_rel_alternate_url_is_consistent
						urlMetricsEntityV3
								.setMobile_rel_alternate_url_is_consistent(crawlerResponse.getMobile_rel_alternate_url_is_consistent());

						// noodp
						urlMetricsEntityV3.setNoodp(crawlerResponse.getNoodp());

						// nosnippet
						urlMetricsEntityV3.setNosnippet(crawlerResponse.getNosnippet());

						// noydir
						urlMetricsEntityV3.setNoydir(crawlerResponse.getNoydir());

						// og_markup_flag
						urlMetricsEntityV3.setOg_markup_flag(crawlerResponse.getOg_markup_flag());

						// og_markup_length
						urlMetricsEntityV3.setOg_markup_length(crawlerResponse.getOg_markup_length());

						// outlink_count
						urlMetricsEntityV3.setOutlink_count(crawlerResponse.getOutlink_count());

						// redirect_blocked
						urlMetricsEntityV3.setRedirect_blocked(crawlerResponse.getRedirect_blocked());

						// redirect_blocked_reason
						urlMetricsEntityV3.setRedirect_blocked_reason(crawlerResponse.getRedirect_blocked_reason());

						// redirect_flg
						urlMetricsEntityV3.setRedirect_flg(crawlerResponse.getRedirect_flg());

						// redirect_times
						urlMetricsEntityV3.setRedirect_times(crawlerResponse.getRedirect_times());

						// robots
						urlMetricsEntityV3.setRobots(crawlerResponse.getRobots());

						// robots_contents
						urlMetricsEntityV3.setRobots_contents(crawlerResponse.getRobots_contents());

						// robots_contents_x_tag
						urlMetricsEntityV3.setRobots_contents_x_tag(crawlerResponse.getRobots_contents_x_tag());

						// robots_flg
						urlMetricsEntityV3.setRobots_flg(crawlerResponse.getRobots_flg());

						// robots_flg_x_tag
						urlMetricsEntityV3.setRobots_flg_x_tag(crawlerResponse.getRobots_flg_x_tag());

						// title_flg
						urlMetricsEntityV3.setTitle_flg(crawlerResponse.getTitle_flg());

						// title_length
						urlMetricsEntityV3.setTitle_length(crawlerResponse.getTitle_length());

						// title_md5
						urlMetricsEntityV3.setTitle_md5(crawlerResponse.getTitle_md5());

						// title_simhash
						urlMetricsEntityV3.setTitle_simhash(crawlerResponse.getTitle_simhash());

						// viewport_flag
						urlMetricsEntityV3.setViewport_flag(crawlerResponse.getViewport_flag());

						// page_analysis_results
						urlMetricsEntityV3.setPageAnalysisResultArray(htmlClickHouseEntity.getPageAnalysisResultArray());
						if (htmlClickHouseEntity.getPageAnalysisResultArray() != null && htmlClickHouseEntity.getPageAnalysisResultArray().length > 0) {
							urlMetricsEntityV3.setPageAnalysisResultInd(true);
						} else {
							urlMetricsEntityV3.setPageAnalysisResultInd(false);
						}

						// change_tracking_hash_cd_json
						urlMetricsEntityV3.setChangeTrackingHashCdJsonArray(htmlClickHouseEntity.getChangeTrackingHashCdJsonArray());

						// base_tag
						urlMetricsEntityV3.setBase_tag(crawlerResponse.getBase_tag());

						// base_tag_flag
						urlMetricsEntityV3.setBase_tag_flag(crawlerResponse.getBase_tag_flag());

						// base_tag_target
						urlMetricsEntityV3.setBase_tag_target(crawlerResponse.getBase_tag_target());

						// response_headers (names)
						urlMetricsEntityV3.setResponse_header_names(
								CrawlerUtils.getInstance().getResponseHeaderNames(crawlerResponse.getResponse_headers()));
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return urlMetricsEntityV3;
	}

	public JsonArray createControllerMessageBodyInJsonFormat(OwnDomainEntity domainEntity) {
		String response = null;
		JsonArray jsonArray = new JsonArray();

		JsonObject queueNameJsonObject = new JsonObject();
		queueNameJsonObject.addProperty("type", IConstants.QUEUE_NAME);
		queueNameJsonObject.addProperty("data", IConstants.QUEUE_NAME_TARGET_URL_DAILY_HTML_PREFIX + domainEntity.getLanguage().toUpperCase() + IConstants.UNDERSCORE + domainEntity.getId());

		// delay in seconds per HTTP request: by default, 0 seconds delay between each HTTP request
		final JsonObject delayInSecondsJsonObject = new JsonObject();
		delayInSecondsJsonObject.addProperty("type", IConstants.DELAY_IN_SECONDS);
		delayInSecondsJsonObject.addProperty("data", 0);

		// max concurrent crawl threads: by default, 1 thread per domain
		final JsonObject maxConcurrentCrawlThreadsJsonObject = new JsonObject();
		maxConcurrentCrawlThreadsJsonObject.addProperty("type", IConstants.MAX_CONCURRENT_THREADS);
		maxConcurrentCrawlThreadsJsonObject.addProperty("data", 1);

		LinkedHashMap<String, JsonObject> map = new LinkedHashMap<>();
		map.put(IConstants.QUEUE_NAME, queueNameJsonObject);
		map.put(IConstants.DELAY_IN_SECONDS, delayInSecondsJsonObject);
		map.put(IConstants.MAX_CONCURRENT_THREADS, maxConcurrentCrawlThreadsJsonObject);

		final String urlCrawlParameters = domainEntity.getUrlCrawlParameters();
		if (StringUtils.isNotBlank(urlCrawlParameters)) {
			JsonElement jsonElement = new JsonParser().parse(urlCrawlParameters);
			JsonArray crawlParametersJsonArray = jsonElement.getAsJsonArray();
			for (JsonElement element : crawlParametersJsonArray) {
				final JsonObject jsonObject = element.getAsJsonObject();
				final String type = jsonObject.get("type").getAsString();
				if (IConstants.DELAY_IN_SECONDS.equals(type)) {
					// overwrite default delay in seconds
					map.put(IConstants.DELAY_IN_SECONDS, jsonObject);
					continue;
				}
				if (IConstants.MAX_CONCURRENT_THREADS.equals(type)) {
					// overwrite default max concurrent crawl threads
					map.put(IConstants.MAX_CONCURRENT_THREADS, jsonObject);
					continue;
				}
				map.put(type, jsonObject);
			}

		}

		map.values().forEach(jsonArray::add);

		int domainId = domainEntity.getId();
		Map<String, String> defaultRequestHeaders = getDefaultRequestHeaders(domainId);
		if (defaultRequestHeaders != null && defaultRequestHeaders.size() > 0) {
			for (String key : defaultRequestHeaders.keySet()) {
				final String value = defaultRequestHeaders.get(key);
				if (StringUtils.isNotBlank(value)) {
					JsonObject pageCrawlerApiRequestHeaderJsonObject = new JsonObject();
					pageCrawlerApiRequestHeaderJsonObject.addProperty("type", IConstants.PAGE_CRAWLER_API_REQUEST_HEADER + IConstants.DASH + key);
					pageCrawlerApiRequestHeaderJsonObject.addProperty("data", value);
					jsonArray.add(pageCrawlerApiRequestHeaderJsonObject);
				}
			}
		}

		return jsonArray;
	}

}
