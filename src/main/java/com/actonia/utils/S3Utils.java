package com.actonia.utils;

import com.amazonaws.SdkClientException;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.model.*;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class S3Utils {

	private static AmazonS3 amazonS3;

	public static boolean deleteFolder(String s3BucketName, String s3Prefix) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("deleteFolder() begins. s3BucketName=" + s3BucketName + ",s3Prefix=" + s3Prefix);
		DeleteObjectRequest deleteObjectRequest = null;
		int totalObjectsToBeDeleted = 0;
		List<String> stringList = null;
		String key = null;

		Set<String> keySet = listS3DirFiles(s3BucketName, s3Prefix);
		if (keySet != null && keySet.size() > 3) {
			totalObjectsToBeDeleted = keySet.size() - 3;
			FormatUtils.getInstance().logMemoryUsage("deleteFolder() totalObjectsToBeDeleted=" + totalObjectsToBeDeleted);
			stringList = new ArrayList<String>(keySet);
			for (int i = 0; i < totalObjectsToBeDeleted; i++) {
				key = stringList.get(i);
				deleteObjectRequest = new DeleteObjectRequest(s3BucketName, key);
				getAmazonS3().deleteObject(deleteObjectRequest);
				if (i % 500 == 0) {
					FormatUtils.getInstance().logMemoryUsage("deleteFolder() total keys deleted=" + i);
				}
			}
		}
		boolean result = deleteDirectoryContents(s3BucketName, s3Prefix);
		FormatUtils.getInstance().logMemoryUsage("deleteFolder() ends. s3BucketName=" + s3BucketName + ",s3Prefix=" + s3Prefix + ",result=" + result + ",elapsed(s.)="
				+ (System.currentTimeMillis() - startTimestamp) / 1000);
		return result;
	}

	private static boolean deleteDirectoryContents(String s3BucketName, String s3Prefix) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("deleteDirectoryContents() begins. s3BucketName=" + s3BucketName + ",s3Prefix=" + s3Prefix);
		Set<String> keysSet = listS3DirFiles(s3BucketName, s3Prefix);
		if (keysSet.isEmpty()) {
			FormatUtils.getInstance().logMemoryUsage("deleteDirectoryContents() given directory {} doesn't have any file " + s3Prefix);
			return false;
		} else {
			FormatUtils.getInstance().logMemoryUsage("deleteDirectoryContents() keysSet.size()=" + keysSet.size());
		}
		DeleteObjectsRequest deleteObjectsRequest = new DeleteObjectsRequest(s3BucketName).withKeys(keysSet.toArray(new String[0]));
		try {
			getAmazonS3().deleteObjects(deleteObjectsRequest);
		} catch (SdkClientException e) {
			FormatUtils.getInstance().logMemoryUsage("deleteDirectoryContents() exception message=" + e.getMessage());
			throw e;
		}
		return true;
	}

	private static AmazonS3 getAmazonS3() throws Exception {
		String accessKeyId = AwsCredentialsEnvKeyConstructor.getInstance().getPlainTextSQSAccessKey();
		String secretAccessKey = AwsCredentialsEnvKeyConstructor.getInstance().getSQSDecryptedSecretKey();
		BasicAWSCredentials basicAWSCredentials = new BasicAWSCredentials(accessKeyId, secretAccessKey);
		if (amazonS3 == null) {
			amazonS3 = new AmazonS3Client(basicAWSCredentials);
		}
		return amazonS3;
	}

	private static Set<String> listS3DirFiles(String bucket, String dirPrefix) throws Exception {
		ListObjectsV2Request s3FileReq = new ListObjectsV2Request().withBucketName(bucket).withPrefix(dirPrefix).withDelimiter("/");

		Set<String> filesList = new HashSet<>();
		ListObjectsV2Result objectsListing;
		try {
			do {
				objectsListing = getAmazonS3().listObjectsV2(s3FileReq);
				objectsListing.getCommonPrefixes().forEach(folderPrefix -> {
					filesList.add(folderPrefix);
					try {
						Set<String> tempPrefix = listS3DirFiles(bucket, folderPrefix);
						filesList.addAll(tempPrefix);
					} catch (Exception e) {
						e.printStackTrace();
					}
				});

				for (S3ObjectSummary summary : objectsListing.getObjectSummaries()) {
					filesList.add(summary.getKey());
				}

				s3FileReq.setContinuationToken(objectsListing.getNextContinuationToken());
			} while (objectsListing.isTruncated());
		} catch (SdkClientException e) {
			System.out.println(e.getMessage());
			throw e;
		}

		return filesList;
	}
}
