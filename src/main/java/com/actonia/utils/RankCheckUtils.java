/**
 *
 */
package com.actonia.utils;

import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;

import com.actonia.dao.RankingDetailClickHouseDAO;
import com.actonia.dao.SeoClarityKeywordEntityDAO;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.entity.SeoClarityKeywordEntity;
import com.actonia.service.ScKeywordRankService;

/**
 * com.actonia.saas.rankcheck.utils.RankCheckUtils.java
 * 
 * @version $Revision: 32584 $ $Author: liulh@SHINETECHCHINA $
 */
public class RankCheckUtils {

	private static final Pattern PATTERN_ENCODE_TEXT_END_PERCENT = Pattern.compile("%\\w?$");

	private static SeoClarityKeywordEntityDAO seoClarityKeywordEntityDAO;

	// keep any web pages with the following exact hostnames
	private static final String[] KEEP_EXACT_HOSTNAME_ARRAY = new String[] { "www.google.org", "sites.google.com", };

	// skip any web pages with the following exact hostnames
	private static final String[] SKIP_EXACT_HOSTNAME_ARRAY = new String[] { "google.com", "webcache.googleusercontent.com", "www.youtube.com", "www.realtor.com",
			"www.redfin.com", "www.trulia.com", "www.coldwellbankerhomes.com", };

	// skip any web pages with the following ending hostnames
	private static final String[] SKIP_ENDING_HOSTNAME_ARRAY = new String[] { ".wikipedia.org", };
	private static final String GOOGLE = "google";
	private static final String DOT = ".";

	// map key = keyword rankcheck ID
	// map value = keyword text
	private static ConcurrentMap<Integer, String> rankCheckIdKeywordTextMap = new ConcurrentHashMap<Integer, String>();

	static {
		seoClarityKeywordEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordEntityDAO");
	}

	public static String mergeString(String str, Object... args) {
		if (args == null || args.length == 0) {
			return str;
		}
		for (int i = 0; i < args.length; i++) {
			str = StringUtils.replace(str, "{" + i + "}", (String) args[i]);
		}
		return str;
	}

	public static String encodeKeyword(String keyword) {
		try {
			return URLEncoder.encode(StringUtils.trim(keyword), "UTF-8");
		} catch (UnsupportedEncodingException e) {
		}
		return StringUtils.replace(StringUtils.trim(keyword), " ", "+");
	}

	public static String decoderString(String str) {
		try {
			return URLDecoder.decode(str, "UTF-8");
		} catch (Exception e) {
			FormatUtils.getInstance().logMemoryUsage("Keyword : " + str);
			e.printStackTrace();
		}
		return str;
	}

	/**
	 * parse domain form a url link
	 * 
	 * @param url
	 * @param remove3w
	 *            remove 'www.' if true
	 * @return
	 */
	public static String getDomainByUrl(String url, boolean remove3w) {
		String result = url;

		// remove 'http://'
		if (StringUtils.contains(result, "://")) {
			result = StringUtils.substringAfter(result, "://");
		}

		// remove the string after domain
		result = StringUtils.substringBefore(result, "/");

		// remove 'www.' if the domain string contains it
		if (remove3w && StringUtils.isNotBlank(result) && result.startsWith("www.")) {
			result = StringUtils.substringAfter(result, "www.");
		}

		return result;
	}

	public static String decodeAndEscapeHtml(String text) {
		if (StringUtils.isBlank(text)) {
			return "";
		}
		try {
			Matcher matcher = PATTERN_ENCODE_TEXT_END_PERCENT.matcher(text);
			if (matcher.find()) {
				text = matcher.replaceFirst("");
			}

			return StringEscapeUtils.unescapeXml(URLDecoder.decode(text, "UTF-8"));
		} catch (Exception e) {
			e.printStackTrace();
			return text;
		}
	}

	public static Map<String, List<String>> getRankedKeywordCompetitorUrlListMap(String ip, OwnDomainEntity ownDomainEntity, Date rankDate, int topRankedPositions)
			throws Exception {
		//long startTimestamp = System.currentTimeMillis();
		Map<String, List<String>> keywordCompetitorUrlListMap = new HashMap<String, List<String>>();
		Set<String> rankedCompetitorUrlSet = null;
		String keywordText = null;
		SeoClarityKeywordEntity seoClarityKeywordEntity = null;
		List<String> competitorUrlList = null;
		boolean skipIndicator = false;

		int domainId = ownDomainEntity.getId();
		String domainName = ownDomainEntity.getDomain();
		Boolean isBroadMatch = ownDomainEntity.isBroadMatch();
		int searchEngineId = ScKeywordRankService.getSearchEngineId(ownDomainEntity);
		int searchLanguageId = ScKeywordRankService.getSearchLanguageId(ownDomainEntity);
		//FormatUtils.getInstance()
		//		.logMemoryUsage("getRankedKeywordCompetitorUrlListMap() begins. ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",isBroadMatch="
		//				+ isBroadMatch + ",searchEngineId=" + searchEngineId + ",searchLanguageId=" + searchLanguageId + ",topRankedPositions=" + topRankedPositions);
		final boolean isMobileDomainFlg = ownDomainEntity.isMobileDomain();
		Map<Integer, Set<String>> keywordIdCompetitorUrlSetMap = RankingDetailClickHouseDAO.getInstance().getKeywordIdCompetitorUrlSetMap(isMobileDomainFlg, domainId, searchEngineId,
				searchLanguageId, rankDate, topRankedPositions);
		if (keywordIdCompetitorUrlSetMap != null && keywordIdCompetitorUrlSetMap.size() > 0) {
			updateRankCheckIdKeywordTextMap(ip, domainId, domainName, keywordIdCompetitorUrlSetMap.keySet());
			keywordCompetitorUrlListMap = new HashMap<String, List<String>>();
			for (Integer keywordRankcheckId : keywordIdCompetitorUrlSetMap.keySet()) {
				if (rankCheckIdKeywordTextMap.containsKey(keywordRankcheckId) == true) {
					seoClarityKeywordEntity = new SeoClarityKeywordEntity();
					seoClarityKeywordEntity.setKeywordText(rankCheckIdKeywordTextMap.get(keywordRankcheckId));
				} else {
					seoClarityKeywordEntity = null;
				}
				if (seoClarityKeywordEntity != null) {
					if (rankCheckIdKeywordTextMap.containsKey(keywordRankcheckId) == false) {
						rankCheckIdKeywordTextMap.put(keywordRankcheckId, seoClarityKeywordEntity.getKeywordText());
					}
					competitorUrlList = new ArrayList<String>();
					keywordText = seoClarityKeywordEntity.getKeywordText().toLowerCase();
					rankedCompetitorUrlSet = keywordIdCompetitorUrlSetMap.get(keywordRankcheckId);
					for (String rankedCompetitorUrl : rankedCompetitorUrlSet) {
						skipIndicator = isSkipThisPage(ip, rankedCompetitorUrl, isBroadMatch, domainName);
						if (skipIndicator == false) {
							competitorUrlList.add(rankedCompetitorUrl);
						}
					}
					if (competitorUrlList != null && competitorUrlList.size() > 0) {
						keywordCompetitorUrlListMap.put(keywordText, competitorUrlList);
					}
				} else {
					FormatUtils.getInstance()
							.logMemoryUsage("getRankedKeywordCompetitorUrlListMap() error--seoClarityKeywordEntity is null,ip=" + ip + ",domainId=" + domainId
									+ ",domainName=" + domainName + ",searchEngineId=" + searchEngineId + ",searchLanguageId=" + searchLanguageId
									+ ",topRankedPositions=" + topRankedPositions + ",keywordRankcheckId=" + keywordRankcheckId);
				}
			}
		}
		//FormatUtils.getInstance().logMemoryUsage("getRankedKeywordCompetitorUrlListMap() ends. ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
		//		+ ",keywordCompetitorUrlListMap.size()=" + keywordCompetitorUrlListMap.size() + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		return keywordCompetitorUrlListMap;
	}

	private static void updateRankCheckIdKeywordTextMap(String ip, int domainId, String domainName, Set<Integer> inputSet) {
		//FormatUtils.getInstance().logMemoryUsage(
		//		"updateRankCheckIdKeywordTextMap() begins. ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",inputSet.size()=" + inputSet.size());
		Set<Integer> testInputSet = new HashSet<Integer>();
		List<SeoClarityKeywordEntity> seoClarityKeywordEntityList = null;
		for (Integer input : inputSet) {
			if (rankCheckIdKeywordTextMap.containsKey(input) == false) {
				testInputSet.add(input);
				if (testInputSet.size() == 5000) {
					seoClarityKeywordEntityList = seoClarityKeywordEntityDAO.getList(testInputSet);
					if (seoClarityKeywordEntityList != null && seoClarityKeywordEntityList.size() > 0) {
						for (SeoClarityKeywordEntity seoClarityKeywordEntity : seoClarityKeywordEntityList) {
							rankCheckIdKeywordTextMap.put(seoClarityKeywordEntity.getId(), seoClarityKeywordEntity.getKeywordText());
						}
					}
					testInputSet = new HashSet<Integer>();
				}
			}
		}
		if (testInputSet.size() > 0) {
			seoClarityKeywordEntityList = seoClarityKeywordEntityDAO.getList(testInputSet);
			if (seoClarityKeywordEntityList != null && seoClarityKeywordEntityList.size() > 0) {
				for (SeoClarityKeywordEntity seoClarityKeywordEntity : seoClarityKeywordEntityList) {
					rankCheckIdKeywordTextMap.put(seoClarityKeywordEntity.getId(), seoClarityKeywordEntity.getKeywordText());
				}
			}
			testInputSet = new HashSet<Integer>();
		}
		//FormatUtils.getInstance().logMemoryUsage(
		//		"updateRankCheckIdKeywordTextMap() ends. ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",inputSet.size()=" + inputSet.size());
	}

	private static boolean isSkipThisPage(String ip, String urlString, Boolean isBroadMatch, String domainName) {

		boolean isToBeSkipped = false;
		boolean isDoneChecking = false;

		URL url = null;
		String hostname = null;
		String[] hostnameSegments = null;
		String hostnameSecondSegment = null;

		// step zero: skip if URL is client domain's web page
		boolean isClientDomainUrl = UrlMetricsUtil.checkIfClientDomainUrl(urlString, domainName, isBroadMatch);
		if (isClientDomainUrl == true) {
			isToBeSkipped = true;
			//if (isDebug == true) {
			//	FormatUtils.getInstance().logMemoryUsage("isSkipThisPage() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",isBroadMatch="
			//			+ isBroadMatch + ",isToBeSkipped=" + isToBeSkipped + ",urlString=" + urlString);
			//}
			return isToBeSkipped;
		}

		try {
			url = new URL(urlString);
			hostname = url.getHost();
			if (StringUtils.isNotBlank(hostname)) {

				// step one: check the exact hostname array for the google web pages to be kept (ie. KEEP_EXACT_HOSTNAME_ARRAY)
				forLoop1: for (int i = 0; i < KEEP_EXACT_HOSTNAME_ARRAY.length; i++) {
					if (StringUtils.equalsIgnoreCase(KEEP_EXACT_HOSTNAME_ARRAY[i], hostname)) {
						isToBeSkipped = false;
						isDoneChecking = true;
						break forLoop1;
					}
				}

				// step two: check the ending hostname array for the web pages to be skipped (ie. SKIP_ENDING_HOSTNAME_ARRAY)
				if (isDoneChecking == false) {
					forLoop2: for (int i = 0; i < SKIP_ENDING_HOSTNAME_ARRAY.length; i++) {
						if (StringUtils.endsWithIgnoreCase(SKIP_ENDING_HOSTNAME_ARRAY[i], hostname)) {
							isToBeSkipped = true;
							isDoneChecking = true;
							break forLoop2;
						}
					}
				}

				// step three: check the exact hostname array for the google web pages to be skipped (ie. SKIP_EXACT_HOSTNAME_ARRAY)
				if (isDoneChecking == false) {
					forLoop3: for (int i = 0; i < SKIP_EXACT_HOSTNAME_ARRAY.length; i++) {
						if (StringUtils.equalsIgnoreCase(SKIP_EXACT_HOSTNAME_ARRAY[i], hostname)) {
							isToBeSkipped = true;
							isDoneChecking = true;
							break forLoop3;
						}
					}
				}

				// step four: when the hostname is composed of at least three segments,
				// skip the google web page when the second segment of the hostname of the URL is 'google'
				// eg. books.google.at, maps.google.co.uk, translate.google.ae
				if (isDoneChecking == false) {
					hostnameSegments = StringUtils.split(hostname, DOT);
					if (hostnameSegments != null && hostnameSegments.length > 2) {
						hostnameSecondSegment = hostnameSegments[1];
						if (StringUtils.equalsIgnoreCase(hostnameSecondSegment, GOOGLE)) {
							isToBeSkipped = true;
						}
					} else {
						// keep the web page when the hostname does not have second segment
					}
				}
			} else {
				// keep the web page when the hostname of cannot be determined.
			}
		}
		// keep the web page when the URL object cannot be instantiated (ie. malformed URL).
		catch (Exception e) {
			isToBeSkipped = true;
			FormatUtils.getInstance().logMemoryUsage("isSkipThisPage() ip=" + ip + ",domainName=" + domainName + ",isBroadMatch=" + isBroadMatch + ",isToBeSkipped="
					+ isToBeSkipped + ",urlString=" + urlString + ",exception message=" + e.getMessage());
		}
		//if (isDebug == true) {
		//	FormatUtils.getInstance().logMemoryUsage("isSkipThisPage() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",isBroadMatch=" + isBroadMatch
		//			+ ",isToBeSkipped=" + isToBeSkipped + ",urlString=" + urlString);
		//}
		return isToBeSkipped;
	}

	public static List<String> getTopRankedCompetitorUrls(String ip, String keywordName, Map<String, List<String>> keywordCompetitorUrlListMap) {
		List<String> competitorUrlList = null;
		if (keywordCompetitorUrlListMap.containsKey(keywordName)) {
			competitorUrlList = keywordCompetitorUrlListMap.get(keywordName);
		}
		return competitorUrlList;
	}

}
