/**
 * 
 */
package com.actonia.utils;

import org.springframework.context.ApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;
import org.springframework.util.Assert;

/**
 * 
 * com.actonia.subserver.utils.SpringBeanFactory.java
 *
 * @version $Revision: 7201 $
 *          $Author: wangjc@SHINETECHCHINA $
 */
public class SpringBeanFactory {
	
	private static ApplicationContext springApplicationContext;
    
    private static final String[] CONTEXT_FILES = 
    	new String[] {
			"/springconfig/applicationContext.xml",
			"/springconfig/applicationContext-database.xml",
			"/springconfig/applicationContext-database-bot.xml",
			"/springconfig/applicationContext-email.xml",
			"/springconfig/applicationContext-dao.xml"
		};

    private synchronized static ApplicationContext getApplicationContext() {
        if (springApplicationContext == null) {
        	springApplicationContext = new ClassPathXmlApplicationContext(CONTEXT_FILES);
        }
        return springApplicationContext;
    }
    
    public static <T> T getBean(String beanName) {
        Assert.notNull(beanName);
        return (T) getApplicationContext().getBean(beanName);
    }
}
