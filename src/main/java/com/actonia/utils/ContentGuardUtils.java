package com.actonia.utils;

import java.net.URLEncoder;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.regex.Pattern;

import com.actonia.dao.*;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.web.util.UriUtils;

import com.actonia.IConstants;
import com.actonia.entity.ContentGuardChangeTrackingEntity;
import com.actonia.entity.ContentGuardGroupEntity;
import com.actonia.entity.ContentGuardSkipUrlEntity;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.UserEntity;
import com.actonia.entity.ZapierWebhookEntity;
import com.actonia.value.object.AlternateLinks;
import com.actonia.value.object.ContentGuardChangeDetails;
import com.actonia.value.object.ContentGuardDailyGroupSeverity;
import com.actonia.value.object.ContentGuardDailyGroupTimeline;
import com.actonia.value.object.ContentGuardHourlyGroupSeverity;
import com.actonia.value.object.ContentGuardHourlyGroupTimeline;
import com.actonia.value.object.ContentGuardHourlySeverity;
import com.actonia.value.object.ContentGuardHourlyTimeline;
import com.actonia.value.object.ContentGuardIndicatorUrlChanges;
import com.actonia.value.object.ContentGuardIndicatorUrlChangesIndicatorAscendingComparator;
import com.actonia.value.object.ContentGuardResourceResponse;
import com.actonia.value.object.ContentGuardSeverity;
import com.actonia.value.object.ContentGuardTrackedPageLastUpdateTimestampAscendingComparator;
import com.actonia.value.object.ContentGuardTrackedPageLastUpdateTimestampDescendingComparator;
import com.actonia.value.object.ContentGuardTrackedPageResponseCodeAscendingComparator;
import com.actonia.value.object.ContentGuardTrackedPageResponseCodeDescendingComparator;
import com.actonia.value.object.ContentGuardTrackedPageUrlAscendingComparator;
import com.actonia.value.object.ContentGuardTrackedPageUrlDescendingComparator;
import com.actonia.value.object.ContentGuardUrlChanges;
import com.actonia.value.object.ContentGuardUrlChangesAddedAscendingComparator;
import com.actonia.value.object.ContentGuardUrlChangesAddedDescendingComparator;
import com.actonia.value.object.ContentGuardUrlChangesCrawlTimestampAscendingComparator;
import com.actonia.value.object.ContentGuardUrlChangesCrawlTimestampDescendingComparator;
import com.actonia.value.object.ContentGuardUrlChangesLastUpdateTimestampAscendingComparator;
import com.actonia.value.object.ContentGuardUrlChangesLastUpdateTimestampDescendingComparator;
import com.actonia.value.object.ContentGuardUrlChangesModifiedAscendingComparator;
import com.actonia.value.object.ContentGuardUrlChangesModifiedDescendingComparator;
import com.actonia.value.object.ContentGuardUrlChangesRemovedAscendingComparator;
import com.actonia.value.object.ContentGuardUrlChangesRemovedDescendingComparator;
import com.actonia.value.object.ContentGuardUrlChangesResponseCodeAscendingComparator;
import com.actonia.value.object.ContentGuardUrlChangesResponseCodeDescendingComparator;
import com.actonia.value.object.ContentGuardUrlChangesTotalChangesAscendingComparator;
import com.actonia.value.object.ContentGuardUrlChangesTotalChangesDescendingComparator;
import com.actonia.value.object.ContentGuardUrlChangesUrlAscendingComparator;
import com.actonia.value.object.ContentGuardUrlChangesUrlDescendingComparator;
import com.actonia.value.object.CrawlerResponse;
import com.actonia.value.object.CustomData;
import com.actonia.value.object.DecodedEncodedUrlValueObject;
import com.actonia.value.object.HreflangErrors;
import com.actonia.value.object.HreflangLinks;
import com.actonia.value.object.OgMarkup;
import com.actonia.value.object.PageAnalysisFragments;
import com.actonia.value.object.PageAnalysisResultChgInd;
import com.actonia.value.object.PageLink;
import com.actonia.value.object.RedirectChain;
import com.actonia.value.object.ResponseHeaders;
import com.actonia.value.object.StructuredData;
import com.actonia.value.object.WebServiceError;
import com.actonia.value.object.ZapierContentGuardAlert;
import com.actonia.web.service.ContentGuardTrackedPage;
import com.actonia.web.service.ContentGuardWebServiceMessage;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

public class ContentGuardUtils {
	//private boolean isDebug = false;
	private static ContentGuardUtils contentGuardUtils;
	private static List<ContentGuardChangeTrackingEntity> contentGuardChangeTrackingEntityList;
	private static List<ContentGuardChangeTrackingEntity> contentGuardChangeTrackingEntityListWithId;

	// map key = change indicator
	// map value = ContentGuardChangeTrackingEntity
	private static Map<String, ContentGuardChangeTrackingEntity> changeIndicatorContentGuardChangeTrackingEntityMap = new HashMap<String, ContentGuardChangeTrackingEntity>();
	private static Map<String, ContentGuardChangeTrackingEntity> changeIndicatorContentGuardChangeTrackingWithIdMap = new HashMap<>();


	// map key = critical change indicator
	// map value = change type
	private static Map<String, String> criticalIndicatorTypeMap;

	private ContentGuardChangeTrackingDAO contentGuardChangeTrackingDAO;
	private ContentGuardChangeTrackingWithIdDAO contentGuardChangeTrackingWithIdDAO;
	private ZapierWebhookDAO zapierWebhookDAO;
	private ContentGuardSkipUrlDAO contentGuardSkipUrlDAO;
	private ContentGuardGroupDAO contentGuardGroupDAO;
	private UserDAO userDAO;

	private ContentGuardUtils() {
		super();
		this.contentGuardGroupDAO = SpringBeanFactory.getBean("contentGuardGroupDAO");
		this.contentGuardSkipUrlDAO = SpringBeanFactory.getBean("contentGuardSkipUrlDAO");
		this.contentGuardChangeTrackingDAO = SpringBeanFactory.getBean("contentGuardChangeTrackingDAO");
		this.contentGuardChangeTrackingWithIdDAO = SpringBeanFactory.getBean("contentGuardChangeTrackingWithIdDAO");
		this.zapierWebhookDAO = SpringBeanFactory.getBean("zapierWebhookDAO");
		this.userDAO = SpringBeanFactory.getBean("userDAO");
		getContentGuardChangeTrackingEntityList();
		getCriticalIndicatorTypeMap();
	}

	public static ContentGuardUtils getInstance() {
		if (contentGuardUtils == null) {
			contentGuardUtils = new ContentGuardUtils();
		}
		return contentGuardUtils;
	}

	public List<ContentGuardChangeTrackingEntity> contentGuardChangeTrackingWithIdList() {
		if (contentGuardChangeTrackingEntityListWithId == null) {
			contentGuardChangeTrackingEntityListWithId = contentGuardChangeTrackingWithIdDAO.getList();
			if (!contentGuardChangeTrackingEntityListWithId.isEmpty()) {
				for (ContentGuardChangeTrackingEntity contentGuardChangeTrackingEntity : contentGuardChangeTrackingEntityListWithId) {
					changeIndicatorContentGuardChangeTrackingWithIdMap.put(contentGuardChangeTrackingEntity.getIndicator(), contentGuardChangeTrackingEntity);
				}
			}
		}
		return contentGuardChangeTrackingEntityListWithId;
	}

	public List<ContentGuardChangeTrackingEntity> getContentGuardChangeTrackingEntityList() {
		if (contentGuardChangeTrackingEntityList == null) {
			contentGuardChangeTrackingEntityList = contentGuardChangeTrackingDAO.getList();
			if (contentGuardChangeTrackingEntityList != null && contentGuardChangeTrackingEntityList.size() > 0) {
				for (ContentGuardChangeTrackingEntity contentGuardChangeTrackingEntity : contentGuardChangeTrackingEntityList) {
					changeIndicatorContentGuardChangeTrackingEntityMap.put(contentGuardChangeTrackingEntity.getIndicator(), contentGuardChangeTrackingEntity);
				}
			}
		}
		return contentGuardChangeTrackingEntityList;
	}

	public ContentGuardChangeTrackingEntity getContentGuardChangeTrackingEntity(String changeIndicator) {
		ContentGuardChangeTrackingEntity contentGuardChangeTrackingEntity = null;
		String testChangeIndicator = null;
		if (StringUtils.startsWithIgnoreCase(changeIndicator, IConstants.RESPONSE_CODE_CHG_IND)) {
			testChangeIndicator = IConstants.RESPONSE_CODE_CHG_IND;
		} else {
			testChangeIndicator = changeIndicator;
		}
		if (changeIndicatorContentGuardChangeTrackingEntityMap.containsKey(testChangeIndicator)) {
			contentGuardChangeTrackingEntity = changeIndicatorContentGuardChangeTrackingEntityMap.get(testChangeIndicator);
		}
		return contentGuardChangeTrackingEntity;
	}

	public String getChangeIndicatorDescription(String changeIndicator) {
		String changeIndicatorDescription = null;
		String testChangeIndicator = null;
		if (StringUtils.startsWithIgnoreCase(changeIndicator, IConstants.RESPONSE_CODE_CHG_IND)) {
			testChangeIndicator = IConstants.RESPONSE_CODE_CHG_IND;
		} else {
			testChangeIndicator = changeIndicator;
		}
		if (changeIndicatorContentGuardChangeTrackingEntityMap.containsKey(testChangeIndicator)) {
			changeIndicatorDescription = changeIndicatorContentGuardChangeTrackingEntityMap.get(testChangeIndicator).getDescription();
		}
		return changeIndicatorDescription;
	}

	public String getChangeIndicatorSeverity(String changeIndicator) {
		Integer changeIndicatorCriticalFlag = null;
		String testChangeIndicator = null;
		if (StringUtils.startsWithIgnoreCase(changeIndicator, IConstants.RESPONSE_CODE_CHG_IND)) {
			testChangeIndicator = IConstants.RESPONSE_CODE_CHG_IND;
		} else {
			testChangeIndicator = changeIndicator;
		}
		String changeIndicatorSeverity = null;
		if (changeIndicatorContentGuardChangeTrackingEntityMap.containsKey(testChangeIndicator)) {
			changeIndicatorCriticalFlag = changeIndicatorContentGuardChangeTrackingEntityMap.get(testChangeIndicator).getCriticalFlag();
			if (changeIndicatorCriticalFlag.intValue() == IConstants.CONTENT_GUARD_CHANGE_SEVERITY_CRITICAL) {
				changeIndicatorSeverity = IConstants.CONTENT_GUARD_CHANGE_SEVERITY_CRITICAL_DESC;
			} else if (changeIndicatorCriticalFlag.intValue() == IConstants.CONTENT_GUARD_CHANGE_SEVERITY_HIGH) {
				changeIndicatorSeverity = IConstants.CONTENT_GUARD_CHANGE_SEVERITY_HIGH_DESC;
			} else if (changeIndicatorCriticalFlag.intValue() == IConstants.CONTENT_GUARD_CHANGE_SEVERITY_MEDIUM) {
				changeIndicatorSeverity = IConstants.CONTENT_GUARD_CHANGE_SEVERITY_MEDIUM_DESC;
			} else if (changeIndicatorCriticalFlag.intValue() == IConstants.CONTENT_GUARD_CHANGE_SEVERITY_LOW) {
				changeIndicatorSeverity = IConstants.CONTENT_GUARD_CHANGE_SEVERITY_LOW_DESC;
			} else {
				changeIndicatorSeverity = IConstants.NOT_AVAILABLE;
			}
		}
		return changeIndicatorSeverity;
	}

	public List<String> getChangeTrackingIndicatorList() {
		List<String> changeTrackingIndicatorList = new ArrayList<String>();
		for (ContentGuardChangeTrackingEntity contentGuardChangeTrackingEntity : getContentGuardChangeTrackingEntityList()) {
			changeTrackingIndicatorList.add(contentGuardChangeTrackingEntity.getIndicator());
		}
		changeTrackingIndicatorList.remove(IConstants.RESPONSE_CODE_404_DETECTED_IND);
		changeTrackingIndicatorList.remove(IConstants.RESPONSE_CODE_404_REMOVED_IND);
		return changeTrackingIndicatorList;
	}

	// update 'response' using pass by reference
	public void processTimeline(int domainId, Long groupId, ContentGuardResourceResponse response, List<HtmlClickHouseEntity> htmlClickHouseEntityList)
			throws Exception {

		String crawlTimestamp = null;
		boolean indicatorValue = false;
		Integer crawlFrequencyType = null;

		List<ContentGuardSkipUrlEntity> contentGuardSkipUrlEntityList = getContentGuardSkipUrlEntityList(domainId, groupId);

		// when after filtering, htmlClickHouseEntityList becomes empty
		if (htmlClickHouseEntityList == null || htmlClickHouseEntityList.size() == 0) {
			return;
		}

		// map key = group ID
		// map value = crawl date in yyyy-mm-dd format (eg. 2020-11-16), crawl hour totals map map 
		Map<Long, Map<String, Map<Integer, Integer>>> groupTimeline = new TreeMap<Long, Map<String, Map<Integer, Integer>>>();

		// map key = group ID
		// map value = crawl date in yyyy-mm-dd format (eg. 2020-11-16), crawl hour, severity-totals map map map 
		Map<Long, Map<String, Map<Integer, Map<Integer, Integer>>>> groupSeverity = new TreeMap<Long, Map<String, Map<Integer, Map<Integer, Integer>>>>();

		for (HtmlClickHouseEntity htmlClickHouseEntity : htmlClickHouseEntityList) {
			crawlTimestamp = DateFormatUtils.format(htmlClickHouseEntity.getCrawlTimestamp(), IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
			for (ContentGuardChangeTrackingEntity contentGuardChangeTrackingEntity : getContentGuardChangeTrackingEntityList()) {
				// alternate_links_chg_ind
				if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.ALTERNATE_LINKS_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getAlternateLinksChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// amphtml_href_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.AMPHTML_HREF_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getAmphtmlHrefChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// analyzed_url_s_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.ANALYZED_URL_S_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getAnalyzedUrlSChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// archive_flg_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.ARCHIVE_FLG_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getArchiveFlgChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// base_tag_added_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.BASE_TAG_ADDED_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getBaseTagAddedInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// base_tag_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.BASE_TAG_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getBaseTagChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// base_tag_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.BASE_TAG_REMOVED_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getBaseTagRemovedInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// base_tag_target_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.BASE_TAG_TARGET_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getBaseTagTargetChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// blocked_by_robots_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.BLOCKED_BY_ROBOTS_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getBlockedByRobotsChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// canonical_added_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CANONICAL_ADDED_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getCanonicalAddedInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// canonical_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CANONICAL_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getCanonicalChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// canonical_header_flag_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CANONICAL_HEADER_FLAG_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getCanonicalHeaderFlagChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// canonical_header_type_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CANONICAL_HEADER_TYPE_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getCanonicalHeaderTypeChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// canonical_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CANONICAL_REMOVED_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getCanonicalRemovedInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// canonical_type_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CANONICAL_TYPE_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getCanonicalTypeChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// canonical_url_is_consistent_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CANONICAL_URL_IS_CONSISTENT_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getCanonicalUrlIsConsistentChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// content_type_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CONTENT_TYPE_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getContentTypeChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// custom_data_added_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CUSTOM_DATA_ADDED_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getCustomDataAddedInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// custom_data_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CUSTOM_DATA_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getCustomDataChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// custom_data_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CUSTOM_DATA_REMOVED_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getCustomDataRemovedInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// description_added_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.DESCRIPTION_ADDED_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getDescriptionAddedInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// description_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.DESCRIPTION_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getDescriptionChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// description_length_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.DESCRIPTION_LENGTH_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getDescriptionLengthChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// description_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.DESCRIPTION_REMOVED_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getDescriptionRemovedInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// error_message_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.ERROR_MESSAGE_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getErrorMessageChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// final_response_code_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.FINAL_RESPONSE_CODE_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getFinalResponseCodeChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// follow_flg_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.FOLLOW_FLG_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getFollowFlgChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// h1_added_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.H1_ADDED_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getH1AddedInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// h1_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.H1_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getH1ChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// h1_count_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.H1_COUNT_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getH1CountChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// h1_length_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.H1_LENGTH_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getH1LengthChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// h1_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.H1_REMOVED_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getH1RemovedInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// h2_added_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.H2_ADDED_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getH2AddedInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// h2_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.H2_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getH2ChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// h2_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.H2_REMOVED_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getH2RemovedInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// header_noarchive_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HEADER_NOARCHIVE_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getHeaderNoarchiveChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// header_nofollow_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HEADER_NOFOLLOW_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getHeaderNofollowChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// header_noindex_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HEADER_NOINDEX_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getHeaderNoindexChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// header_noodp_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HEADER_NOODP_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getHeaderNoodpChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// header_nosnippet_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HEADER_NOSNIPPET_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getHeaderNosnippetChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// header_noydir_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HEADER_NOYDIR_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getHeaderNoydirChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// hreflang_errors_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HREFLANG_ERRORS_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getHreflangErrorsChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// hreflang_links_added_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HREFLANG_LINKS_ADDED_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getHreflangLinksAddedInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// hreflang_links_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HREFLANG_LINKS_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getHreflangLinksChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// hreflang_links_out_count_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HREFLANG_LINKS_OUT_COUNT_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getHreflangLinksOutCountChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// hreflang_links_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HREFLANG_LINKS_REMOVED_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getHreflangLinksRemovedInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// hreflang_url_count_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HREFLANG_URL_COUNT_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getHreflangUrlCountChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// index_flg_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.INDEX_FLG_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getIndexFlgChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// indexable_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.INDEXABLE_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getIndexableChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// insecure_resources_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.INSECURE_RESOURCES_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getInsecureResourcesChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// meta_charset_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.META_CHARSET_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getMetaCharsetChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// meta_content_type_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.META_CONTENT_TYPE_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getMetaContentTypeChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// meta_disabled_sitelinks_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.META_DISABLED_SITELINKS_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getMetaDisabledSitelinksChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// meta_noodp_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.META_NOODP_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getMetaNoodpChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// meta_nosnippet_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.META_NOSNIPPET_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getMetaNosnippetChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// meta_noydir_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.META_NOYDIR_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getMetaNoydirChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// meta_redirect_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.META_REDIRECT_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getMetaRedirectChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// mixed_redirects_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.MIXED_REDIRECTS_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getMixedRedirectsChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// mobile_rel_alternate_url_is_consistent_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.MOBILE_REL_ALTERNATE_URL_IS_CONSISTENT_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getMobileRelAlternateUrlIsConsistentChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// noodp_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.NOODP_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getNoodpChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// nosnippet_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.NOSNIPPET_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getNosnippetChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// noydir_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.NOYDIR_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getNoydirChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// og_markup_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.OG_MARKUP_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getOgMarkupChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// og_markup_length_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.OG_MARKUP_LENGTH_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getOgMarkupLengthChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// open_graph_added_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.OPEN_GRAPH_ADDED_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getOpenGraphAddedInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// open_graph_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.OPEN_GRAPH_REMOVED_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getOpenGraphRemovedInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// outlink_count_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.OUTLINK_COUNT_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getOutlinkCountChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// page_analysis_fragments_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.PAGE_ANALYSIS_FRAGMENTS_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getPageAnalysisFragmentsChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// page_analysis_results_chg_ind_json
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.PAGE_ANALYSIS_RESULTS_CHG_IND_JSON)) {
					if (StringUtils.isNotBlank(htmlClickHouseEntity.getPageAnalysisResultsChgIndJson())) {
						indicatorValue = true;
					} else {
						indicatorValue = false;
					}
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity, indicatorValue,
							contentGuardSkipUrlEntityList, groupId);
				}
				// page_link_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.PAGE_LINK_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getPageLinkChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// redirect_301_detected_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_301_DETECTED_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRedirect301DetectedInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// redirect_301_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_301_REMOVED_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRedirect301RemovedInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// redirect_302_detected_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_302_DETECTED_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRedirect302DetectedInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// redirect_302_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_302_REMOVED_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRedirect302RemovedInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// redirect_blocked_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_BLOCKED_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRedirectBlockedChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// redirect_blocked_reason_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_BLOCKED_REASON_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRedirectBlockedReasonChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// redirect_chain_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_CHAIN_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRedirectChainChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// redirect_diff_code_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_DIFF_CODE_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRedirectDiffCodeInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// redirect_final_url_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_FINAL_URL_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRedirectFinalUrlChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// redirect_times_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_TIMES_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRedirectTimesChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// response_code_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.RESPONSE_CODE_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getResponseCodeChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// response_headers_added_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.RESPONSE_HEADERS_ADDED_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getResponseHeadersAddedInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// response_headers_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.RESPONSE_HEADERS_REMOVED_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getResponseHeadersRemovedInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// robots_added_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.ROBOTS_ADDED_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRobotsAddedInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// robots_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.ROBOTS_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRobotsChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// robots_contents_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.ROBOTS_CONTENTS_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRobotsContentsChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// robots_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.ROBOTS_REMOVED_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRobotsRemovedInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// structured_data_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.STRUCTURED_DATA_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getStructuredDataChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// title_added_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.TITLE_ADDED_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getTitleAddedInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// title_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.TITLE_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getTitleChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// title_length_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.TITLE_LENGTH_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getTitleLengthChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// title_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.TITLE_REMOVED_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getTitleRemovedInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// viewport_added_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.VIEWPORT_ADDED_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getViewportAddedInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// viewport_content_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.VIEWPORT_CONTENT_CHG_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getViewportContentChgInd(), contentGuardSkipUrlEntityList, groupId);
				}
				// viewport_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.VIEWPORT_REMOVED_IND)) {
					updateTimeline(htmlClickHouseEntity.getUrl(), crawlTimestamp, groupTimeline, groupSeverity, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getViewportRemovedInd(), contentGuardSkipUrlEntityList, groupId);
				}
			}
		}

		if (BooleanUtils.isTrue(response.getSuccess())) {
			crawlFrequencyType = getCrawlFrequencyType(domainId, groupId);
			if (crawlFrequencyType != null) {
				if (crawlFrequencyType.intValue() == IConstants.FREQUENCY_TYPE_DAILY) {
					updateDailyGroupTimelineSeverity(response, groupTimeline, groupSeverity);
				} else if (crawlFrequencyType.intValue() == IConstants.FREQUENCY_TYPE_HOURLY) {
					updateHourlyGroupTimelineSeverity(response, groupTimeline, groupSeverity);
				}
			}
		}
	}

	// update 'daily_group_timeline_list' and 'daily_group_severity_list' in response using pass by reference
	private void updateDailyGroupTimelineSeverity(ContentGuardResourceResponse response, Map<Long, Map<String, Map<Integer, Integer>>> groupTimeline,
			Map<Long, Map<String, Map<Integer, Map<Integer, Integer>>>> groupSeverity) {

		// daily_group_timeline_list
		ContentGuardDailyGroupTimeline contentGuardDailyGroupTimeline = null;
		List<ContentGuardDailyGroupTimeline> contentGuardDailyGroupTimelineList = null;
		Map<String, Map<Integer, Integer>> crawlDateCrawlHourChangesMap = null;
		Map<Integer, Integer> crawlHourChangesMap = null;
		Integer hourlyChanges = null;
		int dailyChanges = 0;

		// map key = crawl date
		// map value = severity - total daily changes map
		Map<String, Map<Integer, Integer>> crawlDateDailySeverityChangesMapMap = null;

		if (groupTimeline != null && groupTimeline.size() > 0) {
			// there is only one group in 'groupTimeline'
			for (Long groupId : groupTimeline.keySet()) {
				crawlDateCrawlHourChangesMap = groupTimeline.get(groupId);
				if (crawlDateCrawlHourChangesMap != null && crawlDateCrawlHourChangesMap.size() > 0) {
					contentGuardDailyGroupTimelineList = new ArrayList<ContentGuardDailyGroupTimeline>();
					for (String crawlDate : crawlDateCrawlHourChangesMap.keySet()) {
						dailyChanges = 0;
						crawlHourChangesMap = crawlDateCrawlHourChangesMap.get(crawlDate);
						if (crawlHourChangesMap != null && crawlHourChangesMap.size() > 0) {
							for (Integer crawlHour : crawlHourChangesMap.keySet()) {
								hourlyChanges = crawlHourChangesMap.get(crawlHour);
								dailyChanges = dailyChanges + hourlyChanges.intValue();
							}

							// for a specific crawlDate, dailyChanges
							contentGuardDailyGroupTimeline = new ContentGuardDailyGroupTimeline();
							contentGuardDailyGroupTimeline.setCrawl_date(crawlDate);
							contentGuardDailyGroupTimeline.setTotal_changes(dailyChanges);
							contentGuardDailyGroupTimelineList.add(contentGuardDailyGroupTimeline);
						}
					}
					if (contentGuardDailyGroupTimelineList != null && contentGuardDailyGroupTimelineList.size() > 0) {
						response.setDaily_group_timeline_list(contentGuardDailyGroupTimelineList.toArray(new ContentGuardDailyGroupTimeline[0]));
					}
				}
			}
		}

		// daily_group_severity_list
		Map<String, Map<Integer, Map<Integer, Integer>>> crawlDateCrawlHourSeverityChangeMapMapMap = null;
		Map<Integer, Map<Integer, Integer>> crawlHourSeverityChangeMapMap = null;
		Map<Integer, Integer> hourlySeverityChangesMap = null;
		Map<Integer, Integer> dailySeverityChangesMap = null;

		ContentGuardDailyGroupSeverity contentGuardDailyGroupSeverity = null;
		List<ContentGuardDailyGroupSeverity> contentGuardDailyGroupSeverityList = new ArrayList<ContentGuardDailyGroupSeverity>();
		List<ContentGuardSeverity> contentGuardSeverityList = null;
		ContentGuardSeverity contentGuardSeverity = null;

		if (groupSeverity != null && groupSeverity.size() > 0) {
			// there is only one group in 'groupSeverity'
			for (Long groupId : groupSeverity.keySet()) {
				crawlDateCrawlHourSeverityChangeMapMapMap = groupSeverity.get(groupId);
				if (crawlDateCrawlHourSeverityChangeMapMapMap != null && crawlDateCrawlHourSeverityChangeMapMapMap.size() > 0) {
					crawlDateDailySeverityChangesMapMap = new HashMap<String, Map<Integer, Integer>>();
					for (String crawlDate : crawlDateCrawlHourSeverityChangeMapMapMap.keySet()) {
						crawlHourSeverityChangeMapMap = crawlDateCrawlHourSeverityChangeMapMapMap.get(crawlDate);
						if (crawlHourSeverityChangeMapMap != null && crawlHourSeverityChangeMapMap.size() > 0) {
							for (Integer crawlHour : crawlHourSeverityChangeMapMap.keySet()) {
								hourlySeverityChangesMap = crawlHourSeverityChangeMapMap.get(crawlHour);
								if (hourlySeverityChangesMap != null && hourlySeverityChangesMap.size() > 0) {
									for (Integer severity : hourlySeverityChangesMap.keySet()) {
										hourlyChanges = hourlySeverityChangesMap.get(severity);

										if (crawlDateDailySeverityChangesMapMap.containsKey(crawlDate)) {
											dailySeverityChangesMap = crawlDateDailySeverityChangesMapMap.get(crawlDate);
										} else {
											dailySeverityChangesMap = new HashMap<Integer, Integer>();
										}
										if (dailySeverityChangesMap.containsKey(severity)) {
											dailyChanges = dailySeverityChangesMap.get(severity);
										} else {
											dailyChanges = 0;
										}
										dailyChanges = dailyChanges + hourlyChanges.intValue();
										dailySeverityChangesMap.put(severity, dailyChanges);
										crawlDateDailySeverityChangesMapMap.put(crawlDate, dailySeverityChangesMap);
									}
								}
							}
						}
					}
					if (crawlDateDailySeverityChangesMapMap != null && crawlDateDailySeverityChangesMapMap.size() > 0) {
						for (String crawlDate : crawlDateDailySeverityChangesMapMap.keySet()) {
							contentGuardDailyGroupSeverity = new ContentGuardDailyGroupSeverity();
							contentGuardDailyGroupSeverity.setCrawl_date(crawlDate);
							contentGuardSeverityList = new ArrayList<ContentGuardSeverity>();
							dailySeverityChangesMap = crawlDateDailySeverityChangesMapMap.get(crawlDate);
							for (Integer severity : dailySeverityChangesMap.keySet()) {
								dailyChanges = dailySeverityChangesMap.get(severity);
								contentGuardSeverity = new ContentGuardSeverity();
								contentGuardSeverity.setCritical_flag(severity);
								contentGuardSeverity.setTotal_changes(dailyChanges);
								contentGuardSeverityList.add(contentGuardSeverity);
							}
							contentGuardDailyGroupSeverity.setSeverity_list(contentGuardSeverityList.toArray(new ContentGuardSeverity[0]));
							contentGuardDailyGroupSeverityList.add(contentGuardDailyGroupSeverity);
						}
						response.setDaily_group_severity_list(contentGuardDailyGroupSeverityList.toArray(new ContentGuardDailyGroupSeverity[0]));
					}
				}
			}
		}
	}

	// update 'hourly_group_timeline_list' and 'hourly_group_severity_list' in response using pass by reference
	private void updateHourlyGroupTimelineSeverity(ContentGuardResourceResponse response, Map<Long, Map<String, Map<Integer, Integer>>> groupTimeline,
			Map<Long, Map<String, Map<Integer, Map<Integer, Integer>>>> groupSeverity) {

		// hourly_group_timeline_list
		List<ContentGuardHourlyGroupTimeline> contentGuardHourlyGroupTimelineList = null;
		ContentGuardHourlyGroupTimeline contentGuardHourlyGroupTimeline = null;
		ContentGuardHourlyTimeline contentGuardHourlyTimeline = null;
		List<ContentGuardHourlyTimeline> contentGuardHourlyTimelineList = null;
		Map<String, Map<Integer, Integer>> crawlDateCrawlHourChangesMap = null;
		Map<Integer, Integer> crawlHourChangesMap = null;
		Integer hourlyChanges = null;

		if (groupTimeline != null && groupTimeline.size() > 0) {
			// there is only one group in 'groupTimeline'
			for (Long groupId : groupTimeline.keySet()) {
				crawlDateCrawlHourChangesMap = groupTimeline.get(groupId);
				if (crawlDateCrawlHourChangesMap != null && crawlDateCrawlHourChangesMap.size() > 0) {
					contentGuardHourlyGroupTimelineList = new ArrayList<ContentGuardHourlyGroupTimeline>();
					for (String crawlDate : crawlDateCrawlHourChangesMap.keySet()) {
						contentGuardHourlyGroupTimeline = new ContentGuardHourlyGroupTimeline();
						contentGuardHourlyGroupTimeline.setCrawl_date(crawlDate);
						contentGuardHourlyTimelineList = new ArrayList<ContentGuardHourlyTimeline>();
						crawlHourChangesMap = crawlDateCrawlHourChangesMap.get(crawlDate);
						if (crawlHourChangesMap != null && crawlHourChangesMap.size() > 0) {
							for (Integer crawlHour : crawlHourChangesMap.keySet()) {
								hourlyChanges = crawlHourChangesMap.get(crawlHour);
								contentGuardHourlyTimeline = new ContentGuardHourlyTimeline();
								contentGuardHourlyTimeline.setCrawl_hour(crawlHour);
								contentGuardHourlyTimeline.setTotal_changes(hourlyChanges);
								contentGuardHourlyTimelineList.add(contentGuardHourlyTimeline);
							}
						}
						contentGuardHourlyGroupTimeline.setTimeline_list(contentGuardHourlyTimelineList.toArray(new ContentGuardHourlyTimeline[0]));
						contentGuardHourlyGroupTimelineList.add(contentGuardHourlyGroupTimeline);
					}
					if (contentGuardHourlyGroupTimelineList != null && contentGuardHourlyGroupTimelineList.size() > 0) {
						response.setHourly_group_timeline_list(contentGuardHourlyGroupTimelineList.toArray(new ContentGuardHourlyGroupTimeline[0]));
					}
				}
			}
		}

		// hourly_group_severity_list
		Map<String, Map<Integer, Map<Integer, Integer>>> crawlDateCrawlHourSeverityChangeMapMapMap = null;
		Map<Integer, Map<Integer, Integer>> crawlHourSeverityChangeMapMap = null;
		Map<Integer, Integer> hourlySeverityChangesMap = null;

		ContentGuardHourlyGroupSeverity contentGuardHourlyGroupSeverity = null;
		List<ContentGuardHourlyGroupSeverity> contentGuardHourlyGroupSeverityList = new ArrayList<ContentGuardHourlyGroupSeverity>();
		List<ContentGuardHourlySeverity> contentGuardHourlySeverityList = null;
		ContentGuardHourlySeverity contentGuardHourlySeverity = null;
		ContentGuardSeverity contentGuardSeverity = null;
		List<ContentGuardSeverity> contentGuardSeverityList = null;

		if (groupSeverity != null && groupSeverity.size() > 0) {
			// there is only one group in 'groupSeverity'
			for (Long groupId : groupSeverity.keySet()) {
				crawlDateCrawlHourSeverityChangeMapMapMap = groupSeverity.get(groupId);
				if (crawlDateCrawlHourSeverityChangeMapMapMap != null && crawlDateCrawlHourSeverityChangeMapMapMap.size() > 0) {
					for (String crawlDate : crawlDateCrawlHourSeverityChangeMapMapMap.keySet()) {
						contentGuardHourlyGroupSeverity = new ContentGuardHourlyGroupSeverity();
						contentGuardHourlyGroupSeverity.setCrawl_date(crawlDate);
						contentGuardHourlySeverityList = new ArrayList<ContentGuardHourlySeverity>();
						crawlHourSeverityChangeMapMap = crawlDateCrawlHourSeverityChangeMapMapMap.get(crawlDate);
						if (crawlHourSeverityChangeMapMap != null && crawlHourSeverityChangeMapMap.size() > 0) {
							for (Integer crawlHour : crawlHourSeverityChangeMapMap.keySet()) {
								contentGuardHourlySeverity = new ContentGuardHourlySeverity();
								contentGuardHourlySeverity.setCrawl_hour(crawlHour);
								contentGuardSeverityList = new ArrayList<ContentGuardSeverity>();
								hourlySeverityChangesMap = crawlHourSeverityChangeMapMap.get(crawlHour);
								if (hourlySeverityChangesMap != null && hourlySeverityChangesMap.size() > 0) {
									for (Integer severity : hourlySeverityChangesMap.keySet()) {
										hourlyChanges = hourlySeverityChangesMap.get(severity);
										contentGuardSeverity = new ContentGuardSeverity();
										contentGuardSeverity.setCritical_flag(severity);
										contentGuardSeverity.setTotal_changes(hourlyChanges);
										contentGuardSeverityList.add(contentGuardSeverity);
									}
									contentGuardHourlySeverity.setSeverity_list(contentGuardSeverityList.toArray(new ContentGuardSeverity[0]));
									contentGuardHourlySeverityList.add(contentGuardHourlySeverity);
								}
							}
						}
						contentGuardHourlyGroupSeverity.setSeverity_list(contentGuardHourlySeverityList.toArray(new ContentGuardHourlySeverity[0]));
						contentGuardHourlyGroupSeverityList.add(contentGuardHourlyGroupSeverity);
					}
					if (contentGuardHourlyGroupSeverityList != null && contentGuardHourlyGroupSeverityList.size() > 0) {
						response.setHourly_group_severity_list(contentGuardHourlyGroupSeverityList.toArray(new ContentGuardHourlyGroupSeverity[0]));
					}
				}
			}
		}
	}

	// update 'groupTimeline' and 'groupSeverity' using pass by reference
	private void updateTimeline(String urlString, String crawlTimestamp, Map<Long, Map<String, Map<Integer, Integer>>> groupTimeline,
			Map<Long, Map<String, Map<Integer, Map<Integer, Integer>>>> groupSeverity, ContentGuardChangeTrackingEntity contentGuardChangeTrackingEntity,
			Boolean changeTrackingIndicatorValue, List<ContentGuardSkipUrlEntity> contentGuardSkipUrlEntityList, Long groupId) throws Exception {
		String crawlDate = null;
		Integer crawlHour = null;
		Integer severity = null;

		// map key = crawl hour
		// map value = totals
		Map<Integer, Integer> groupCrawlHourTotalsMap = null;

		// map key = crawl date
		// map value = crawl hour totals map
		Map<String, Map<Integer, Integer>> groupCrawlDateCrawlHourTotalsMapMap = null;

		// map key = severity
		// map value = totals
		Map<Integer, Integer> groupSeverityTotalsMap = null;

		// map key = crawl hour
		// map value = totals
		Map<Integer, Map<Integer, Integer>> groupCrawlHourSeverityTotalsMapMap = null;

		// map key = crawl date
		// map value = crawl hour totals map
		Map<String, Map<Integer, Map<Integer, Integer>>> groupCrawlDateCrawlHourSeverityTotalsMapMapMap = null;

		boolean isSkipUrl = false;
		if (BooleanUtils.isTrue(changeTrackingIndicatorValue)) {
			isSkipUrl = checkIfSkipUrlForIndicator(contentGuardChangeTrackingEntity.getIndicator(), contentGuardSkipUrlEntityList, urlString);
			if (isSkipUrl == true) {
				return;
			}

			crawlDate = StringUtils.substring(crawlTimestamp, 0, 10);
			crawlHour = NumberUtils.toInt(StringUtils.substring(crawlTimestamp, 11, 13));

			// group timeline
			if (groupTimeline.containsKey(groupId)) {
				groupCrawlDateCrawlHourTotalsMapMap = groupTimeline.get(groupId);
			} else {
				groupCrawlDateCrawlHourTotalsMapMap = new TreeMap<String, Map<Integer, Integer>>();
			}
			if (groupCrawlDateCrawlHourTotalsMapMap.containsKey(crawlDate)) {
				groupCrawlHourTotalsMap = groupCrawlDateCrawlHourTotalsMapMap.get(crawlDate);
			} else {
				groupCrawlHourTotalsMap = new TreeMap<Integer, Integer>();
			}
			if (groupCrawlHourTotalsMap.containsKey(crawlHour)) {
				groupCrawlHourTotalsMap.put(crawlHour, groupCrawlHourTotalsMap.get(crawlHour) + 1);
			} else {
				groupCrawlHourTotalsMap.put(crawlHour, 1);
			}
			groupCrawlDateCrawlHourTotalsMapMap.put(crawlDate, groupCrawlHourTotalsMap);
			groupTimeline.put(groupId, groupCrawlDateCrawlHourTotalsMapMap);

			if (contentGuardChangeTrackingEntity.getCriticalFlag() != null) {
				severity = contentGuardChangeTrackingEntity.getCriticalFlag();
			} else {
				severity = 0;
			}

			// group severity timeline
			if (groupSeverity.containsKey(groupId)) {
				groupCrawlDateCrawlHourSeverityTotalsMapMapMap = groupSeverity.get(groupId);
			} else {
				groupCrawlDateCrawlHourSeverityTotalsMapMapMap = new TreeMap<String, Map<Integer, Map<Integer, Integer>>>();
			}
			if (groupCrawlDateCrawlHourSeverityTotalsMapMapMap.containsKey(crawlDate)) {
				groupCrawlHourSeverityTotalsMapMap = groupCrawlDateCrawlHourSeverityTotalsMapMapMap.get(crawlDate);
			} else {
				groupCrawlHourSeverityTotalsMapMap = new TreeMap<Integer, Map<Integer, Integer>>();
			}
			if (groupCrawlHourSeverityTotalsMapMap.containsKey(crawlHour)) {
				groupSeverityTotalsMap = groupCrawlHourSeverityTotalsMapMap.get(crawlHour);
			} else {
				groupSeverityTotalsMap = new TreeMap<Integer, Integer>();
			}
			if (groupSeverityTotalsMap.containsKey(severity)) {
				groupSeverityTotalsMap.put(severity, groupSeverityTotalsMap.get(severity) + 1);
			} else {
				groupSeverityTotalsMap.put(severity, 1);
			}
			groupCrawlHourSeverityTotalsMapMap.put(crawlHour, groupSeverityTotalsMap);
			groupCrawlDateCrawlHourSeverityTotalsMapMapMap.put(crawlDate, groupCrawlHourSeverityTotalsMapMap);
			groupSeverity.put(groupId, groupCrawlDateCrawlHourSeverityTotalsMapMapMap);
		}
	}

	// update 'response' using pass by reference
	public void processChangeTrackingSummary(int domainId, Long groupId, ContentGuardResourceResponse response,
			List<HtmlClickHouseEntity> htmlClickHouseEntityInputList, Map<String, String> md5HashContentGuardUrlMap, int pageNumber, int rowsPerPage, int sortBy,
			Boolean returnDetails, String filterUrl) throws Exception {

		String crawlTimestamp = null;
		String responseCode = null;
		Integer totalUrls = 0;
		Integer totalRedirectedUrls = 0;
		boolean indicatorValue = false;
		boolean isSkipUrl = false;
		ContentGuardChangeTrackingEntity clonedContentGuardChangeTrackingEntity = null;
		List<ContentGuardUrlChanges> contentGuardUrlChangesList = null;
		int filterUrlTotals = 0;

		// map key = URL MD5 hash
		// map value = ContentGuardUrlChanges
		ConcurrentMap<String, ContentGuardUrlChanges> md5HashContentGuardUrlChangesMap = new ConcurrentHashMap<String, ContentGuardUrlChanges>();

		// map key = response code
		// map value = total number of URLs
		Map<String, Integer> responseCodeTotalUrlsMap = new TreeMap<String, Integer>();

		List<ContentGuardSkipUrlEntity> contentGuardSkipUrlEntityList = getContentGuardSkipUrlEntityList(domainId, groupId);
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = deDuplicateUrls(domainId, htmlClickHouseEntityInputList, contentGuardSkipUrlEntityList);

		// total pages tracked
		response.setTotal_pages_tracked(md5HashContentGuardUrlMap.size());
		//FormatUtils.getInstance().logMemoryUsage("processChangeTrackingSummary() totalPagesTracked=" + response.getTotal_pages_tracked());

		response.setTotal_pages_changed(htmlClickHouseEntityList.size());
		//FormatUtils.getInstance().logMemoryUsage("processChangeTrackingSummary() totalPagesChanged=" + response.getTotal_pages_changed());

		response.setTotal_changes(0);
		response.setTotal_changes_added(0);
		response.setTotal_changes_modified(0);
		response.setTotal_changes_removed(0);
		response.setChange_tracking_indicator_totals_map(new TreeMap<String, Integer>());
		response.setChange_severity(new TreeMap<Integer, Integer>());
		response.setUrl_changes_list(new ArrayList<ContentGuardUrlChanges>());

		if (StringUtils.isNotBlank(filterUrl)) {
			response.setFilter_url(filterUrl);
		}

		nextHtmlClickHouseEntity: for (HtmlClickHouseEntity htmlClickHouseEntity : htmlClickHouseEntityList) {
			if (StringUtils.isNotBlank(filterUrl)) {
				// case sensitive 'contain' string filter
				if (StringUtils.contains(htmlClickHouseEntity.getUrl(), filterUrl) == false) {
					continue nextHtmlClickHouseEntity;
				}
			}
			crawlTimestamp = DateFormatUtils.format(htmlClickHouseEntity.getCrawlTimestamp(), IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
			for (ContentGuardChangeTrackingEntity contentGuardChangeTrackingEntity : getContentGuardChangeTrackingEntityList()) {
				// alternate_links_chg_ind
				if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.ALTERNATE_LINKS_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getAlternateLinksChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// amphtml_href_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.AMPHTML_HREF_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getAmphtmlHrefChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// analyzed_url_s_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.ANALYZED_URL_S_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getAnalyzedUrlSChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// archive_flg_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.ARCHIVE_FLG_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getArchiveFlgChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// base_tag_added_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.BASE_TAG_ADDED_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getBaseTagAddedInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// base_tag_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.BASE_TAG_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getBaseTagChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// base_tag_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.BASE_TAG_REMOVED_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getBaseTagRemovedInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// base_tag_target_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.BASE_TAG_TARGET_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getBaseTagTargetChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// blocked_by_robots_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.BLOCKED_BY_ROBOTS_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getBlockedByRobotsChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// canonical_added_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CANONICAL_ADDED_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getCanonicalAddedInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// canonical_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CANONICAL_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getCanonicalChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// canonical_header_flag_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CANONICAL_HEADER_FLAG_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getCanonicalHeaderFlagChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// canonical_header_type_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CANONICAL_HEADER_TYPE_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getCanonicalHeaderTypeChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// canonical_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CANONICAL_REMOVED_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getCanonicalRemovedInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// canonical_type_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CANONICAL_TYPE_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getCanonicalTypeChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// canonical_url_is_consistent_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CANONICAL_URL_IS_CONSISTENT_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getCanonicalUrlIsConsistentChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// content_type_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CONTENT_TYPE_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getContentTypeChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// custom_data_added_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CUSTOM_DATA_ADDED_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getCustomDataAddedInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// custom_data_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CUSTOM_DATA_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getCustomDataChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// custom_data_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CUSTOM_DATA_REMOVED_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getCustomDataRemovedInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// description_added_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.DESCRIPTION_ADDED_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getDescriptionAddedInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// description_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.DESCRIPTION_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getDescriptionChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// description_length_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.DESCRIPTION_LENGTH_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getDescriptionLengthChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// description_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.DESCRIPTION_REMOVED_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getDescriptionRemovedInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// error_message_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.ERROR_MESSAGE_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getErrorMessageChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// final_response_code_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.FINAL_RESPONSE_CODE_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getFinalResponseCodeChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// follow_flg_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.FOLLOW_FLG_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getFollowFlgChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// h1_added_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.H1_ADDED_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getH1AddedInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// h1_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.H1_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getH1ChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// h1_count_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.H1_COUNT_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getH1CountChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// h1_length_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.H1_LENGTH_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getH1LengthChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// h1_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.H1_REMOVED_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getH1RemovedInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// h2_added_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.H2_ADDED_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getH2AddedInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// h2_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.H2_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getH2ChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// h2_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.H2_REMOVED_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getH2RemovedInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// header_noarchive_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HEADER_NOARCHIVE_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getHeaderNoarchiveChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// header_nofollow_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HEADER_NOFOLLOW_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getHeaderNofollowChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// header_noindex_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HEADER_NOINDEX_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getHeaderNoindexChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// header_noodp_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HEADER_NOODP_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getHeaderNoodpChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// header_nosnippet_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HEADER_NOSNIPPET_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getHeaderNosnippetChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// header_noydir_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HEADER_NOYDIR_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getHeaderNoydirChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// hreflang_errors_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HREFLANG_ERRORS_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getHreflangErrorsChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// hreflang_links_added_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HREFLANG_LINKS_ADDED_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getHreflangLinksAddedInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// hreflang_links_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HREFLANG_LINKS_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getHreflangLinksChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// hreflang_links_out_count_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HREFLANG_LINKS_OUT_COUNT_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getHreflangLinksOutCountChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// hreflang_links_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HREFLANG_LINKS_REMOVED_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getHreflangLinksRemovedInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// hreflang_url_count_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HREFLANG_URL_COUNT_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getHreflangUrlCountChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// index_flg_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.INDEX_FLG_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getIndexFlgChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// indexable_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.INDEXABLE_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getIndexableChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// insecure_resources_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.INSECURE_RESOURCES_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getInsecureResourcesChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// meta_charset_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.META_CHARSET_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getMetaCharsetChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// meta_content_type_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.META_CONTENT_TYPE_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getMetaContentTypeChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// meta_disabled_sitelinks_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.META_DISABLED_SITELINKS_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getMetaDisabledSitelinksChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// meta_noodp_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.META_NOODP_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getMetaNoodpChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// meta_nosnippet_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.META_NOSNIPPET_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getMetaNosnippetChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// meta_noydir_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.META_NOYDIR_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getMetaNoydirChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// meta_redirect_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.META_REDIRECT_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getMetaRedirectChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// mixed_redirects_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.MIXED_REDIRECTS_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getMixedRedirectsChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// mobile_rel_alternate_url_is_consistent_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.MOBILE_REL_ALTERNATE_URL_IS_CONSISTENT_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getMobileRelAlternateUrlIsConsistentChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// noodp_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.NOODP_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getNoodpChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// nosnippet_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.NOSNIPPET_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getNosnippetChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// noydir_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.NOYDIR_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getNoydirChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// og_markup_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.OG_MARKUP_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getOgMarkupChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// og_markup_length_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.OG_MARKUP_LENGTH_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getOgMarkupLengthChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// open_graph_added_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.OPEN_GRAPH_ADDED_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getOpenGraphAddedInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// open_graph_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.OPEN_GRAPH_REMOVED_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getOpenGraphRemovedInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// outlink_count_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.OUTLINK_COUNT_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getOutlinkCountChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// page_analysis_fragments_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.PAGE_ANALYSIS_FRAGMENTS_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getPageAnalysisFragmentsChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// page_analysis_results_chg_ind_json
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.PAGE_ANALYSIS_RESULTS_CHG_IND_JSON)) {
					if (StringUtils.isNotBlank(htmlClickHouseEntity.getPageAnalysisResultsChgIndJson())) {
						indicatorValue = true;
					} else {
						indicatorValue = false;
					}
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity, indicatorValue,
							contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// page_link_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.PAGE_LINK_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getPageLinkChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// redirect_301_detected_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_301_DETECTED_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRedirect301DetectedInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// redirect_301_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_301_REMOVED_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRedirect301RemovedInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// redirect_302_detected_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_302_DETECTED_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRedirect302DetectedInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// redirect_302_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_302_REMOVED_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRedirect302RemovedInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// redirect_blocked_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_BLOCKED_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRedirectBlockedChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// redirect_blocked_reason_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_BLOCKED_REASON_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRedirectBlockedReasonChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// redirect_chain_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_CHAIN_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRedirectChainChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// redirect_diff_code_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_DIFF_CODE_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRedirectDiffCodeInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// redirect_final_url_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_FINAL_URL_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRedirectFinalUrlChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// redirect_times_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_TIMES_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRedirectTimesChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// response_code_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.RESPONSE_CODE_CHG_IND)) {
					if (BooleanUtils.isTrue(htmlClickHouseEntity.getResponseCodeChgInd())) {
						isSkipUrl = checkIfSkipUrlForIndicator(contentGuardChangeTrackingEntity.getIndicator(), contentGuardSkipUrlEntityList,
								htmlClickHouseEntity.getUrl());
						if (isSkipUrl == false) {
							responseCode = htmlClickHouseEntity.getCrawlerResponse().getResponse_code();
							if (responseCodeTotalUrlsMap.containsKey(responseCode)) {
								totalUrls = responseCodeTotalUrlsMap.get(responseCode);
							} else {
								totalUrls = 0;
							}
							responseCodeTotalUrlsMap.put(responseCode, ++totalUrls);
							clonedContentGuardChangeTrackingEntity = contentGuardChangeTrackingEntity.clone();
							clonedContentGuardChangeTrackingEntity
									.setIndicator(clonedContentGuardChangeTrackingEntity.getIndicator() + IConstants.UNDERSCORE_TO_UNDERSCORE + responseCode);
							updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, clonedContentGuardChangeTrackingEntity,
									htmlClickHouseEntity.getResponseCodeChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
						}
					}
				}
				// response_headers_added_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.RESPONSE_HEADERS_ADDED_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getResponseHeadersAddedInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// response_headers_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.RESPONSE_HEADERS_REMOVED_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getResponseHeadersRemovedInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// robots_added_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.ROBOTS_ADDED_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRobotsAddedInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// robots_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.ROBOTS_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRobotsChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// robots_contents_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.ROBOTS_CONTENTS_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRobotsContentsChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// robots_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.ROBOTS_REMOVED_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRobotsRemovedInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// structured_data_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.STRUCTURED_DATA_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getStructuredDataChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// title_added_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.TITLE_ADDED_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getTitleAddedInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// title_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.TITLE_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getTitleChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// title_length_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.TITLE_LENGTH_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getTitleLengthChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// title_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.TITLE_REMOVED_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getTitleRemovedInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// viewport_added_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.VIEWPORT_ADDED_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getViewportAddedInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// viewport_content_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.VIEWPORT_CONTENT_CHG_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getViewportContentChgInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
				// viewport_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.VIEWPORT_REMOVED_IND)) {
					updateChangeTrackingSummary(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getViewportRemovedInd(), contentGuardSkipUrlEntityList, md5HashContentGuardUrlChangesMap);
				}
			}
		}

		if (responseCodeTotalUrlsMap != null && responseCodeTotalUrlsMap.size() > 0) {
			for (String testResponseCode : responseCodeTotalUrlsMap.keySet()) {
				totalUrls = responseCodeTotalUrlsMap.get(testResponseCode);
				totalRedirectedUrls = totalRedirectedUrls + totalUrls;
				response.getChange_tracking_indicator_totals_map().put(IConstants.RESPONSE_CODE_CHG_IND + IConstants.UNDERSCORE_TO_UNDERSCORE + testResponseCode,
						totalUrls);
			}
			if (totalRedirectedUrls > 0) {
				response.getChange_tracking_indicator_totals_map().put(IConstants.RESPONSE_CODE_CHG_IND, totalRedirectedUrls);
			}
		}

		// update 'md5HashContentGuardUrlChangesMap' with 'content_guard_url' MySQL records when URLs not in 'md5HashContentGuardUrlChangesMap'
		contentGuardUrlChangesList = updateResponseCodeLastUpdateTimestamp(md5HashContentGuardUrlChangesMap, md5HashContentGuardUrlMap, filterUrl, domainId);

		// sort the 'contentGuardUrlChangesList' according to the 'sort_by' request parameter
		sortContentGuardUrlChangesList(contentGuardUrlChangesList, sortBy);

		// when there is no 'filter_url'
		if (StringUtils.isBlank(filterUrl)) {
			// return the required page
			paginateContentGuardUrlChangesList(domainId, groupId, response, contentGuardUrlChangesList, pageNumber, rowsPerPage, sortBy, returnDetails,
					htmlClickHouseEntityList);
		}
		// when there is 'filter_url'
		else {
			if (contentGuardUrlChangesList != null && contentGuardUrlChangesList.size() > 0) {

				filterUrlTotals = contentGuardUrlChangesList.size();

				// return the required page
				paginateContentGuardUrlChangesList(domainId, groupId, response, contentGuardUrlChangesList, pageNumber, rowsPerPage, sortBy, returnDetails,
						htmlClickHouseEntityList);
			} else {
				filterUrlTotals = 0;
			}
			response.setFilter_url_totals(filterUrlTotals);
		}
	}

	// update 'response' using pass by reference
	private void paginateContentGuardUrlChangesList(int domainId, Long groupId, ContentGuardResourceResponse response,
			List<ContentGuardUrlChanges> contentGuardUrlChangesInputList, int pageNumber, int rowsPerPage, int sortBy, Boolean returnDetails,
			List<HtmlClickHouseEntity> htmlClickHouseEntityList) throws Exception {
		//FormatUtils.getInstance().logMemoryUsage("paginateContentGuardUrlChangesList() begins. contentGuardUrlChangesInputList.size()=" + contentGuardUrlChangesInputList.size()
		//		+ ",pageNumber=" + pageNumber + ",rowsPerPage" + rowsPerPage + ",sortBy=" + sortBy + ",returnDetails=" + returnDetails);
		WebServiceError webServiceError = null;
		ContentGuardResourceResponse changeDetailsResponse = null;
		List<ContentGuardUrlChanges> contentGuardUrlChangesOutputList = new ArrayList<ContentGuardUrlChanges>();
		int fromIndex = (pageNumber - 1) * rowsPerPage;
		int toIndex = pageNumber * rowsPerPage;
		//FormatUtils.getInstance().logMemoryUsage("paginateContentGuardUrlChangesList() fromIndex=" + fromIndex + ",toIndex=" + toIndex);
		if (fromIndex <= (contentGuardUrlChangesInputList.size() - 1)) {
			if (toIndex > contentGuardUrlChangesInputList.size()) {
				toIndex = contentGuardUrlChangesInputList.size();
				//FormatUtils.getInstance().logMemoryUsage("paginateContentGuardUrlChangesList() revised toIndex=" + toIndex);
			}
			contentGuardUrlChangesOutputList = contentGuardUrlChangesInputList.subList(fromIndex, toIndex);

			if (BooleanUtils.isTrue(returnDetails)) {
				for (ContentGuardUrlChanges contentGuardUrlChanges : contentGuardUrlChangesOutputList) {
					if (contentGuardUrlChanges.getTotal_changes().intValue() > 0) {
						nextHtmlClickHouseEntity: for (HtmlClickHouseEntity htmlClickHouseEntity : htmlClickHouseEntityList) {
							if (StringUtils.equalsIgnoreCase(contentGuardUrlChanges.getUrl(), htmlClickHouseEntity.getUrl())) {
								changeDetailsResponse = getChangeTrackingDetails(domainId, groupId, htmlClickHouseEntity);
								contentGuardUrlChanges.setPrevious_crawl_timestamp(changeDetailsResponse.getPrevious_crawl_timestamp());
								contentGuardUrlChanges.setCurrent_crawl_timestamp(changeDetailsResponse.getCurrent_crawl_timestamp());
								contentGuardUrlChanges.setChange_details_list(changeDetailsResponse.getUrl_change_details_list());
								break nextHtmlClickHouseEntity;
							}
						}
					}
				}
			}

			response.setUrl_changes_list(contentGuardUrlChangesOutputList);
			if (toIndex == contentGuardUrlChangesInputList.size()) {
				response.setEnd_of_url_changes_list_flag(true);
			} else {
				response.setEnd_of_url_changes_list_flag(false);
			}

		} else {
			response.setSuccess(false);
			webServiceError = new WebServiceError();
			webServiceError.setError_code(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_OUTSIDE_DATA_RANGE);
			webServiceError.setError_message(getErrorMessage(webServiceError.getError_code(), String.valueOf(contentGuardUrlChangesInputList.size())));
			response.setError(webServiceError);
		}
		response.setPage_number(pageNumber);
		response.setRows_per_page(rowsPerPage);
		response.setSort_by(sortBy);
		//FormatUtils.getInstance()
		//		.logMemoryUsage("paginateContentGuardUrlChangesList() ends. contentGuardUrlChangesInputList.size()=" + contentGuardUrlChangesInputList.size() + ",pageNumber="
		//				+ pageNumber + ",rowsPerPage" + rowsPerPage + ",sortBy=" + sortBy + ",contentGuardUrlChangesOutputList.size()="
		//				+ contentGuardUrlChangesOutputList.size());
	}

	private void sortContentGuardUrlChangesList(List<ContentGuardUrlChanges> contentGuardUrlChangesList, int sortBy) {

		// 1 = sort URLs in ascending order
		if (sortBy == IConstants.SORT_BY_URL_ASC) {
			Collections.sort(contentGuardUrlChangesList, new ContentGuardUrlChangesUrlAscendingComparator());
		}
		// 2 = sort URLs in descending order
		else if (sortBy == IConstants.SORT_BY_URL_DESC) {
			Collections.sort(contentGuardUrlChangesList, new ContentGuardUrlChangesUrlDescendingComparator());
		}
		// 3 = sort crawl timestamp in ascending order
		else if (sortBy == IConstants.SORT_BY_CRAWL_TIMESTAMP_ASC) {
			Collections.sort(contentGuardUrlChangesList, new ContentGuardUrlChangesCrawlTimestampAscendingComparator());
		}
		// 4 = sort crawl timestamp in descending order
		else if (sortBy == IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC) {
			Collections.sort(contentGuardUrlChangesList, new ContentGuardUrlChangesCrawlTimestampDescendingComparator());
		}
		// 5 = sort total changes in ascending order
		else if (sortBy == IConstants.SORT_BY_TOTAL_CHANGES_ASC) {
			Collections.sort(contentGuardUrlChangesList, new ContentGuardUrlChangesTotalChangesAscendingComparator());
		}
		// 6 = sort total changes in descending order
		else if (sortBy == IConstants.SORT_BY_TOTAL_CHANGES_DESC) {
			Collections.sort(contentGuardUrlChangesList, new ContentGuardUrlChangesTotalChangesDescendingComparator());
		}
		// 7 = sort added in ascending order
		else if (sortBy == IConstants.SORT_BY_ADDED_ASC) {
			Collections.sort(contentGuardUrlChangesList, new ContentGuardUrlChangesAddedAscendingComparator());
		}
		// 8 = sort added in descending order
		else if (sortBy == IConstants.SORT_BY_ADDED_DESC) {
			Collections.sort(contentGuardUrlChangesList, new ContentGuardUrlChangesAddedDescendingComparator());
		}
		// 9 = sort modified in ascending order
		else if (sortBy == IConstants.SORT_BY_MODIFIED_ASC) {
			Collections.sort(contentGuardUrlChangesList, new ContentGuardUrlChangesModifiedAscendingComparator());
		}
		// 10 = sort modified in descending order
		else if (sortBy == IConstants.SORT_BY_MODIFIED_DESC) {
			Collections.sort(contentGuardUrlChangesList, new ContentGuardUrlChangesModifiedDescendingComparator());
		}
		// 11 = sort removed in ascending order
		else if (sortBy == IConstants.SORT_BY_REMOVED_ASC) {
			Collections.sort(contentGuardUrlChangesList, new ContentGuardUrlChangesRemovedAscendingComparator());
		}
		// 12 = sort removed in descending order
		else if (sortBy == IConstants.SORT_BY_REMOVED_DESC) {
			Collections.sort(contentGuardUrlChangesList, new ContentGuardUrlChangesRemovedDescendingComparator());
		}
		// 13 = sort response code in ascending order
		else if (sortBy == IConstants.SORT_BY_RESPONSE_CODE_ASC) {
			Collections.sort(contentGuardUrlChangesList, new ContentGuardUrlChangesResponseCodeAscendingComparator());
		}
		// 14 = sort response code in descending order
		else if (sortBy == IConstants.SORT_BY_RESPONSE_CODE_DESC) {
			Collections.sort(contentGuardUrlChangesList, new ContentGuardUrlChangesResponseCodeDescendingComparator());
		}
		// 15 = sort last update timestamp in ascending order
		else if (sortBy == IConstants.SORT_BY_LAST_UPDATE_TIMESTAMP_ASC) {
			Collections.sort(contentGuardUrlChangesList, new ContentGuardUrlChangesLastUpdateTimestampAscendingComparator());
		}
		// 16 = sort last update timestamp in descending order
		else if (sortBy == IConstants.SORT_BY_LAST_UPDATE_TIMESTAMP_DESC) {
			Collections.sort(contentGuardUrlChangesList, new ContentGuardUrlChangesLastUpdateTimestampDescendingComparator());
		}
	}

	private List<ContentGuardUrlChanges> updateResponseCodeLastUpdateTimestamp(Map<String, ContentGuardUrlChanges> md5HashContentGuardUrlChangesMap,
			Map<String, String> md5HashContentGuardUrlMap, String filterUrl, int domainId) throws Exception {
		ContentGuardUrlChanges contentGuardUrlChanges = null;
		String urlString = null;
		String urlHashCode = null;
		HtmlClickHouseEntity htmlClickHouseEntity = null;

		// map key = URL MD5 hash code
		// map value = URL's latest HtmlClickHouseEntity
		Map<String, HtmlClickHouseEntity> hashCodeLatestHtmlClickHouseEntityMap = retrieveLatestCrawlTimestampResponseCode(domainId);

		// when content guard URLs were deleted from the 'content_guard_url' MySQL table
		for (String urlMd5Hash : md5HashContentGuardUrlChangesMap.keySet()) {
			if (md5HashContentGuardUrlMap.containsKey(urlMd5Hash) == false) {
				FormatUtils.getInstance().logMemoryUsage("updateResponseCodeLastUpdateTimestamp() deleted md5HashContentGuardUrlChangesMap URL="
						+ md5HashContentGuardUrlChangesMap.get(urlMd5Hash).getUrl());
				md5HashContentGuardUrlChangesMap.remove(urlMd5Hash);
			}
		}

		nextUrlMd5Hash: for (String urlMd5Hash : md5HashContentGuardUrlMap.keySet()) {
			// when the content guard URLs have changes
			if (md5HashContentGuardUrlChangesMap.containsKey(urlMd5Hash) == true) {
				contentGuardUrlChanges = md5HashContentGuardUrlChangesMap.get(urlMd5Hash);
				if (hashCodeLatestHtmlClickHouseEntityMap.containsKey(urlMd5Hash)) {
					htmlClickHouseEntity = hashCodeLatestHtmlClickHouseEntityMap.get(urlMd5Hash);
					contentGuardUrlChanges.setResponse_code(htmlClickHouseEntity.getCrawlerResponse().getResponse_code());
					contentGuardUrlChanges
							.setLast_update_timestamp(DateFormatUtils.format(htmlClickHouseEntity.getCrawlTimestamp(), IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS));
					md5HashContentGuardUrlChangesMap.put(urlMd5Hash, contentGuardUrlChanges);
				}
			}
		}

		return new ArrayList<ContentGuardUrlChanges>(md5HashContentGuardUrlChangesMap.values());
	}

	public Map<String, HtmlClickHouseEntity> retrieveLatestCrawlTimestampResponseCode(int domainId) throws Exception {

		Map<String, HtmlClickHouseEntity> hashCodeLatestHtmlClickHouseEntityMap = new HashMap<String, HtmlClickHouseEntity>();

		List<HtmlClickHouseEntity> htmlClickHouseEntityList = null;
		String contentGuardUrlString = null;
		String contentGuardUrlMd5HashCode = null;
		HtmlClickHouseEntity testHtmlClickHouseEntity = null;
		CrawlerResponse crawlerResponse = null;

		List<String> changeTrackingIndicatorList = ContentGuardUtils.getInstance().getChangeTrackingIndicatorList();
		changeTrackingIndicatorList.remove(IConstants.RESPONSE_CODE_404_REMOVED_IND);
		changeTrackingIndicatorList.remove(IConstants.RESPONSE_CODE_404_DETECTED_IND);
		htmlClickHouseEntityList = ContentGuardClickHouseDAO.getInstance().getChangeTrackingSummaryList(domainId, changeTrackingIndicatorList);
		if (htmlClickHouseEntityList != null && htmlClickHouseEntityList.size() > 0) {
			for (HtmlClickHouseEntity htmlClickHouseEntity : htmlClickHouseEntityList) {
				//if (isDebug == true) {
				//	FormatUtils.getInstance().logMemoryUsage("retrieveLatestCrawlTimestampResponseCode() url 1=" + htmlClickHouseEntity.getUrl() + ",crawlTimestamp 1="
				//			+ htmlClickHouseEntity.getCrawlTimestamp() + ",response code 1=" + htmlClickHouseEntity.getCrawlerResponse().getResponse_code());
				//}
				contentGuardUrlString = htmlClickHouseEntity.getUrl();
				if (StringUtils.isNotBlank(contentGuardUrlString) == true) {
					contentGuardUrlMd5HashCode = Md5Util.Md5(StringUtils.trim(contentGuardUrlString));
					htmlClickHouseEntity.setUrl(null);
					testHtmlClickHouseEntity = new HtmlClickHouseEntity();
					crawlerResponse = new CrawlerResponse();
					crawlerResponse.setResponse_code(htmlClickHouseEntity.getCrawlerResponse().getResponse_code());
					testHtmlClickHouseEntity.setCrawlerResponse(crawlerResponse);
					testHtmlClickHouseEntity.setCrawlTimestamp(htmlClickHouseEntity.getCrawlTimestamp());
					hashCodeLatestHtmlClickHouseEntityMap.put(contentGuardUrlMd5HashCode, testHtmlClickHouseEntity);
				}
			}
		}

		List<String> fieldNames = new ArrayList<String>();
		fieldNames.add(IConstants.URL);
		fieldNames.add(IConstants.RESPONSE_CODE);
		fieldNames.add(IConstants.CRAWL_TIMESTAMP);

		// map key = content guard URL MD5 hash code
		// map value = content guard URL's daily HtmlClickHouseEntity
		Map<String, HtmlClickHouseEntity> contentGuardClickHouseEntityMap = new HashMap<String, HtmlClickHouseEntity>();

		htmlClickHouseEntityList = ContentGuardClickHouseDAO.getInstance().getEarliestFromHistorical(domainId, fieldNames, null);
		if (htmlClickHouseEntityList != null && htmlClickHouseEntityList.size() > 0) {
			for (HtmlClickHouseEntity htmlClickHouseEntity : htmlClickHouseEntityList) {
				contentGuardUrlString = htmlClickHouseEntity.getUrl();
				if (StringUtils.isNotBlank(contentGuardUrlString) == true) {
					contentGuardUrlMd5HashCode = Md5Util.Md5(StringUtils.trim(contentGuardUrlString));
					htmlClickHouseEntity.setUrl(null);
					contentGuardClickHouseEntityMap.put(contentGuardUrlMd5HashCode, htmlClickHouseEntity);
				}
			}
		}

		if (contentGuardClickHouseEntityMap != null && contentGuardClickHouseEntityMap.size() > 0) {
			for (String key : contentGuardClickHouseEntityMap.keySet()) {
				if (hashCodeLatestHtmlClickHouseEntityMap.containsKey(key) == false) {
					testHtmlClickHouseEntity = contentGuardClickHouseEntityMap.get(key);
					//if (isDebug == true) {
					//	FormatUtils.getInstance()
					//			.logMemoryUsage("retrieveLatestCrawlTimestampResponseCode() url 2=" + testHtmlClickHouseEntity.getUrl() + ",crawlTimestamp 2="
					//					+ testHtmlClickHouseEntity.getCrawlTimestamp() + ",response code 2="
					//					+ testHtmlClickHouseEntity.getCrawlerResponse().getResponse_code());
					//}
					hashCodeLatestHtmlClickHouseEntityMap.put(key, testHtmlClickHouseEntity);
				}
			}
		}

		return hashCodeLatestHtmlClickHouseEntityMap;
	}

	// when URL occurs more than once in 'htmlClickHouseEntityInputList', keep the one with the latest crawl timestamp
	private List<HtmlClickHouseEntity> deDuplicateUrls(int domainId, List<HtmlClickHouseEntity> htmlClickHouseEntityInputList,
			List<ContentGuardSkipUrlEntity> contentGuardSkipUrlEntityList) throws Exception {
		List<HtmlClickHouseEntity> htmlClickHouseEntityOutputList = new ArrayList<HtmlClickHouseEntity>();
		Map<String, HtmlClickHouseEntity> urlHtmlClickHouseEntityMap = new TreeMap<String, HtmlClickHouseEntity>();
		HtmlClickHouseEntity htmlClickHouseEntity = null;
		for (HtmlClickHouseEntity htmlClickHouseEntityInput : htmlClickHouseEntityInputList) {
			// de-duplication
			if (urlHtmlClickHouseEntityMap.containsKey(htmlClickHouseEntityInput.getUrl())) {
				htmlClickHouseEntity = urlHtmlClickHouseEntityMap.get(htmlClickHouseEntityInput.getUrl());
				if (htmlClickHouseEntityInput.getCrawlTimestamp().after(htmlClickHouseEntity.getCrawlTimestamp())) {
					urlHtmlClickHouseEntityMap.put(htmlClickHouseEntityInput.getUrl(), htmlClickHouseEntityInput);
				}
			} else {
				urlHtmlClickHouseEntityMap.put(htmlClickHouseEntityInput.getUrl(), htmlClickHouseEntityInput);
			}
		}
		if (urlHtmlClickHouseEntityMap != null && urlHtmlClickHouseEntityMap.size() > 0) {
			for (String urlString : urlHtmlClickHouseEntityMap.keySet()) {
				htmlClickHouseEntityOutputList.add(urlHtmlClickHouseEntityMap.get(urlString));
			}
		}
		return htmlClickHouseEntityOutputList;
	}

	private boolean checkIfSkipUrlForIndicator(String urlStringInput, ContentGuardSkipUrlEntity contentGuardSkipUrlEntity) throws Exception {
		boolean isSkipUrl = false;

		String urlSelector = null;
		String urlSelectorEncoded = null;
		String unencodedUrlString = null;
		boolean isRequiredJavaUrlEncoder = false;
		DecodedEncodedUrlValueObject testDecodedEncodedUrlValueObject = null;
		String encodedUrlString = null;

		int urlSelectorType = contentGuardSkipUrlEntity.getUrlSelectorType();
		if (urlSelectorType == IConstants.URL_SELECTOR_TYPE_ALL) {
			isSkipUrl = true;
		} else {
			testDecodedEncodedUrlValueObject = CrawlerUtils.getInstance().getDecodedAndEncodedUrlString(urlStringInput, isRequiredJavaUrlEncoder);
			if (testDecodedEncodedUrlValueObject == null) {
				throw new Exception("checkIfSkipUrlForIndicator() decodedEncodedUrlValueObject not available for urlStringInput=" + urlStringInput);
			}
			if (testDecodedEncodedUrlValueObject.getErrorIndicator() == true) {
				encodedUrlString = URLEncoder.encode(urlStringInput, IConstants.UTF_8);
				encodedUrlString = StringUtils.replace(encodedUrlString, IConstants.SLASH_UTF8_ENCODED, IConstants.SLASH);
			} else {
				encodedUrlString = testDecodedEncodedUrlValueObject.getEncodedUrlString();
			}

			urlSelector = contentGuardSkipUrlEntity.getUrlSelector();
			if (StringUtils.isBlank(urlSelector)) {
				throw new Exception("checkIfSkipUrlForIndicator() error--urlSelector is blank, contentGuardSkipUrlEntity=" + contentGuardSkipUrlEntity.toString());
			}

			if (urlSelectorType == IConstants.URL_SELECTOR_TYPE_CONTAINS) {
				testDecodedEncodedUrlValueObject = CrawlerUtils.getInstance().getDecodedAndEncodedUrlString(urlSelector, isRequiredJavaUrlEncoder);
				if (testDecodedEncodedUrlValueObject == null) {
					throw new Exception("checkIfSkipUrlForIndicator() decodedEncodedUrlValueObject not available for urlSelector=" + urlSelector);
				}
				if (testDecodedEncodedUrlValueObject.getErrorIndicator() == true) {
					urlSelectorEncoded = UriUtils.encodeFragment(urlSelector, IConstants.UTF_8);
					urlSelectorEncoded = StringUtils.replace(urlSelectorEncoded, IConstants.SLASH_UTF8_ENCODED, IConstants.SLASH);
				} else {
					urlSelectorEncoded = testDecodedEncodedUrlValueObject.getEncodedUrlString();
				}
				if (StringUtils.containsIgnoreCase(encodedUrlString, urlSelectorEncoded)) {
					isSkipUrl = true;
				}
			} else if (urlSelectorType == IConstants.URL_SELECTOR_TYPE_EQUALS) {
				testDecodedEncodedUrlValueObject = CrawlerUtils.getInstance().getDecodedAndEncodedUrlString(urlSelector, isRequiredJavaUrlEncoder);
				if (testDecodedEncodedUrlValueObject == null) {
					throw new Exception("checkIfSkipUrlForIndicator() decodedEncodedUrlValueObject not available for urlSelector=" + urlSelector);
				}
				if (testDecodedEncodedUrlValueObject.getErrorIndicator() == true) {
					urlSelectorEncoded = URLEncoder.encode(urlSelector, IConstants.UTF_8);
					urlSelectorEncoded = StringUtils.replace(urlSelectorEncoded, IConstants.SLASH_UTF8_ENCODED, IConstants.SLASH);
				} else {
					urlSelectorEncoded = testDecodedEncodedUrlValueObject.getEncodedUrlString();
				}
				if (StringUtils.equalsIgnoreCase(encodedUrlString, urlSelectorEncoded)) {
					isSkipUrl = true;
				}
			} else if (urlSelectorType == IConstants.URL_SELECTOR_TYPE_REGEXP) {
				unencodedUrlString = UriUtils.decode(encodedUrlString, IConstants.UTF_8);
				isSkipUrl = Pattern.matches(urlSelector, unencodedUrlString);
			}
		}
		return isSkipUrl;
	}

	public boolean checkIfSkipUrlForIndicator(String indicator, List<ContentGuardSkipUrlEntity> contentGuardSkipUrlEntityList, String urlString) throws Exception {
		boolean isSkipUrl = false;
		if (contentGuardSkipUrlEntityList == null || contentGuardSkipUrlEntityList.size() == 0) {
			isSkipUrl = false;
		} else {
			nextContentGuardSkipUrlEntity: for (ContentGuardSkipUrlEntity contentGuardSkipUrlEntity : contentGuardSkipUrlEntityList) {
				if (StringUtils.equalsIgnoreCase(indicator, contentGuardSkipUrlEntity.getIndicator())) {
					isSkipUrl = checkIfSkipUrlForIndicator(urlString, contentGuardSkipUrlEntity);
					if (isSkipUrl == true) {
						break nextContentGuardSkipUrlEntity;
					}
				}
			}
		}
		return isSkipUrl;
	}

	// update 'response' using pass by reference
	// update 'md5HashContentGuardUrlChangesMap' using pass by reference
	private void updateChangeTrackingSummary(String urlString, String crawlTimestamp, ContentGuardResourceResponse response,
			ContentGuardChangeTrackingEntity contentGuardChangeTrackingEntity, Boolean changeTrackingIndicatorValue,
			List<ContentGuardSkipUrlEntity> contentGuardSkipUrlEntityList, Map<String, ContentGuardUrlChanges> md5HashContentGuardUrlChangesMap) throws Exception {

		Integer totalPages = null;
		boolean isSkipUrl = false;
		int totalChanges = 0;
		String urlMd5Hash = null;
		ContentGuardUrlChanges contentGuardUrlChanges = null;
		Integer severity = null;
		ContentGuardChangeDetails contentGuardChangeDetails = null;

		if (BooleanUtils.isTrue(changeTrackingIndicatorValue)) {

			isSkipUrl = checkIfSkipUrlForIndicator(contentGuardChangeTrackingEntity.getIndicator(), contentGuardSkipUrlEntityList, urlString);
			if (isSkipUrl == true) {
				return;
			}

			if (contentGuardChangeTrackingEntity.getCriticalFlag() != null) {
				severity = contentGuardChangeTrackingEntity.getCriticalFlag();
			} else {
				severity = 0;
			}

			if (response.getChange_severity().containsKey(severity)) {
				response.getChange_severity().put(severity, response.getChange_severity().get(severity) + 1);
			} else {
				response.getChange_severity().put(severity, 1);
			}

			response.setTotal_changes(response.getTotal_changes() + 1);
			if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getType(), IConstants.CHANGE_TYPE_ADDED)) {
				response.setTotal_changes_added(response.getTotal_changes_added() + 1);
			} else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getType(), IConstants.CHANGE_TYPE_MODIFIED)) {
				response.setTotal_changes_modified(response.getTotal_changes_modified() + 1);
			} else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getType(), IConstants.CHANGE_TYPE_REMOVED)) {
				response.setTotal_changes_removed(response.getTotal_changes_removed() + 1);
			}

			if (response.getChange_tracking_indicator_totals_map().containsKey(contentGuardChangeTrackingEntity.getIndicator())) {
				totalPages = response.getChange_tracking_indicator_totals_map().get(contentGuardChangeTrackingEntity.getIndicator()) + 1;
			} else {
				totalPages = 1;
			}
			response.getChange_tracking_indicator_totals_map().put(contentGuardChangeTrackingEntity.getIndicator(), totalPages);

			urlMd5Hash = Md5Util.Md5(StringUtils.trim(urlString));

			if (md5HashContentGuardUrlChangesMap.containsKey(urlMd5Hash)) {
				contentGuardUrlChanges = md5HashContentGuardUrlChangesMap.get(urlMd5Hash);
			} else {
				contentGuardUrlChanges = new ContentGuardUrlChanges();
				contentGuardUrlChanges.setUrl(urlString);
				contentGuardUrlChanges.setHash_cd(CrawlerUtils.getInstance().getMd5HashCode(contentGuardUrlChanges.getUrl()));
				contentGuardUrlChanges.setCurrent_crawl_timestamp(crawlTimestamp);
			}
			if (contentGuardUrlChanges.getAdded() == null) {
				contentGuardUrlChanges.setAdded(0);
			}
			if (contentGuardUrlChanges.getModified() == null) {
				contentGuardUrlChanges.setModified(0);
			}
			if (contentGuardUrlChanges.getRemoved() == null) {
				contentGuardUrlChanges.setRemoved(0);
			}
			if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getType(), IConstants.CHANGE_TYPE_ADDED)) {
				contentGuardUrlChanges.setAdded(contentGuardUrlChanges.getAdded() + 1);
			} else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getType(), IConstants.CHANGE_TYPE_MODIFIED)) {
				contentGuardUrlChanges.setModified(contentGuardUrlChanges.getModified() + 1);
			} else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getType(), IConstants.CHANGE_TYPE_REMOVED)) {
				contentGuardUrlChanges.setRemoved(contentGuardUrlChanges.getRemoved() + 1);
			}
			if (contentGuardUrlChanges.getChange_details_list() == null) {
				contentGuardUrlChanges.setChange_details_list(new ArrayList<ContentGuardChangeDetails>());
			}
			contentGuardChangeDetails = new ContentGuardChangeDetails();
			contentGuardChangeDetails.setChange_indicator(contentGuardChangeTrackingEntity.getIndicator());
			contentGuardUrlChanges.getChange_details_list().add(contentGuardChangeDetails);
			totalChanges = contentGuardUrlChanges.getAdded() + contentGuardUrlChanges.getModified() + contentGuardUrlChanges.getRemoved();
			contentGuardUrlChanges.setTotal_changes(totalChanges);
			md5HashContentGuardUrlChangesMap.put(urlMd5Hash, contentGuardUrlChanges);
		}
	}

	public String getFieldContent(String trackedFieldName, HtmlClickHouseEntity htmlClickHouseEntity) throws Exception {
		String content = null;
		String json = null;
		AlternateLinks[] alternateLinks = null;
		String[] stringArray = null;
		HreflangErrors hreflangErrors = null;
		HreflangLinks[] hreflangLinks = null;
		OgMarkup[] ogMarkup = null;
		RedirectChain[] redirectChain = null;
		StructuredData structuredData = null;
		PageLink[] pageLink = null;
		CustomData[] customDataArray = null;
		PageAnalysisFragments[] pageAnalysisFragmentsArray = null;
		ResponseHeaders[] responseHeaders = null;
		List<String> testStringList = null;

		Gson gson = new Gson();

		// alternate_links
		if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.ALTERNATE_LINKS)) {
			alternateLinks = htmlClickHouseEntity.getCrawlerResponse().getAlternate_links();
			if (alternateLinks != null) {
				json = gson.toJson(alternateLinks, AlternateLinks[].class);
				if (CrawlerUtils.getInstance().checkIfJsonDataAvailable(json) == true) {
					content = json;
				}
			}
		}
		// amphtml_href
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.AMPHTML_HREF)) {
			if (StringUtils.isNotBlank(htmlClickHouseEntity.getCrawlerResponse().getAmphtml_href())) {
				content = htmlClickHouseEntity.getCrawlerResponse().getAmphtml_href();
			}
		}
		// analyzed_url_s
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.ANALYZED_URL_S)) {
			if (StringUtils.isNotBlank(htmlClickHouseEntity.getCrawlerResponse().getAnalyzed_url_s())) {
				content = htmlClickHouseEntity.getCrawlerResponse().getAnalyzed_url_s();
			}
		}
		// archive_flg
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.ARCHIVE_FLG)) {
			if (StringUtils.isNotBlank(htmlClickHouseEntity.getCrawlerResponse().getArchive_flg())) {
				content = htmlClickHouseEntity.getCrawlerResponse().getArchive_flg();
			}
		}
		// base_tag
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.BASE_TAG)) {
			if (StringUtils.isNotBlank(htmlClickHouseEntity.getCrawlerResponse().getBase_tag())) {
				content = htmlClickHouseEntity.getCrawlerResponse().getBase_tag();
			}
		}
		// base_tag_flag
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.BASE_TAG_FLAG)) {
			if (htmlClickHouseEntity.getCrawlerResponse().getBase_tag_flag() != null) {
				content = htmlClickHouseEntity.getCrawlerResponse().getBase_tag_flag().toString();
			}
		}
		// base_tag_target
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.BASE_TAG_TARGET)) {
			if (StringUtils.isNotBlank(htmlClickHouseEntity.getCrawlerResponse().getBase_tag_target())) {
				content = htmlClickHouseEntity.getCrawlerResponse().getBase_tag_target();
			}
		}
		// blocked_by_robots
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.BLOCKED_BY_ROBOTS)) {
			if (StringUtils.isNotBlank(htmlClickHouseEntity.getCrawlerResponse().getBlocked_by_robots())) {
				content = htmlClickHouseEntity.getCrawlerResponse().getBlocked_by_robots();
			}
		}
		// canonical
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.CANONICAL)) {
			if (StringUtils.isNotBlank(htmlClickHouseEntity.getCrawlerResponse().getCanonical())) {
				content = htmlClickHouseEntity.getCrawlerResponse().getCanonical();
			}
		}
		// canonical_flg
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.CANONICAL_FLG)) {
			if (StringUtils.isNotBlank(htmlClickHouseEntity.getCrawlerResponse().getCanonical_flg())) {
				content = htmlClickHouseEntity.getCrawlerResponse().getCanonical_flg();
			}
		}
		// canonical_header_flag
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.CANONICAL_HEADER_FLAG)) {
			if (htmlClickHouseEntity.getCrawlerResponse().getCanonical_header_flag() != null) {
				content = htmlClickHouseEntity.getCrawlerResponse().getCanonical_header_flag().toString();
			}
		}
		// canonical_header_type
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.CANONICAL_HEADER_TYPE)) {
			if (StringUtils.isNotBlank(htmlClickHouseEntity.getCrawlerResponse().getCanonical_header_type())) {
				content = htmlClickHouseEntity.getCrawlerResponse().getCanonical_header_type();
			}
		}
		// canonical_type
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.CANONICAL_TYPE)) {
			if (StringUtils.isNotBlank(htmlClickHouseEntity.getCrawlerResponse().getCanonical_type())) {
				content = htmlClickHouseEntity.getCrawlerResponse().getCanonical_type();
			}
		}
		// canonical_url_is_consistent
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.CANONICAL_URL_IS_CONSISTENT)) {
			if (StringUtils.isNotBlank(htmlClickHouseEntity.getCrawlerResponse().getCanonical_url_is_consistent())) {
				content = htmlClickHouseEntity.getCrawlerResponse().getCanonical_url_is_consistent();
			}
		}
		// content_type
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.CONTENT_TYPE)) {
			if (StringUtils.isNotBlank(htmlClickHouseEntity.getCrawlerResponse().getContent_type())) {
				content = htmlClickHouseEntity.getCrawlerResponse().getContent_type();
			}
		}
		// custom_data
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.CUSTOM_DATA)) {
			customDataArray = htmlClickHouseEntity.getCrawlerResponse().getCustom_data();
			if (customDataArray != null) {
				json = gson.toJson(customDataArray, CustomData[].class);
				if (CrawlerUtils.getInstance().checkIfJsonDataAvailable(json) == true) {
					content = json;
				}
			}
		}
		// description
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.DESCRIPTION)) {
			if (StringUtils.isNotBlank(htmlClickHouseEntity.getCrawlerResponse().getDescription())) {
				content = htmlClickHouseEntity.getCrawlerResponse().getDescription();
			}
		}
		// description_flg
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.DESCRIPTION_FLG)) {
			if (StringUtils.isNotBlank(htmlClickHouseEntity.getCrawlerResponse().getDescription_flg())) {
				content = htmlClickHouseEntity.getCrawlerResponse().getDescription_flg();
			}
		}
		// description_length
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.DESCRIPTION_LENGTH)) {
			if (htmlClickHouseEntity.getCrawlerResponse().getDescription_length() != null) {
				content = String.valueOf(htmlClickHouseEntity.getCrawlerResponse().getDescription_length());
			}
		}
		// error_message
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.ERROR_MESSAGE)) {
			if (StringUtils.isNotBlank(htmlClickHouseEntity.getCrawlerResponse().getError_message())) {
				content = htmlClickHouseEntity.getCrawlerResponse().getError_message();
			}
		}
		// final_response_code
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.FINAL_RESPONSE_CODE)) {
			if (htmlClickHouseEntity.getCrawlerResponse().getFinal_response_code() != null) {
				content = String.valueOf(htmlClickHouseEntity.getCrawlerResponse().getFinal_response_code());
			}
		}
		// follow_flg
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.FOLLOW_FLG)) {
			if (StringUtils.isNotBlank(htmlClickHouseEntity.getCrawlerResponse().getFollow_flg())) {
				content = htmlClickHouseEntity.getCrawlerResponse().getFollow_flg();
			}
		}
		// h1
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.H1)) {
			stringArray = htmlClickHouseEntity.getCrawlerResponse().getH1();
			if (stringArray != null) {
				json = gson.toJson(stringArray, String[].class);
				if (CrawlerUtils.getInstance().checkIfJsonDataAvailable(json) == true) {
					content = json;
				}
			}
		}
		// h1_count
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.H1_COUNT)) {
			if (htmlClickHouseEntity.getCrawlerResponse().getH1_count() != null) {
				content = String.valueOf(htmlClickHouseEntity.getCrawlerResponse().getH1_count());
			}
		}
		// h1_length
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.H1_LENGTH)) {
			if (htmlClickHouseEntity.getCrawlerResponse().getH1_length() != null) {
				content = String.valueOf(htmlClickHouseEntity.getCrawlerResponse().getH1_length());
			}
		}
		// h2
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.H2)) {
			stringArray = htmlClickHouseEntity.getCrawlerResponse().getH2();
			if (stringArray != null && stringArray.length > 0) {
				json = gson.toJson(stringArray, String[].class);
				if (CrawlerUtils.getInstance().checkIfJsonDataAvailable(json) == true) {
					content = json;
				}
			}
		}
		// header_noarchive
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.HEADER_NOARCHIVE)) {
			if (htmlClickHouseEntity.getCrawlerResponse().getHeader_noarchive() != null) {
				content = htmlClickHouseEntity.getCrawlerResponse().getHeader_noarchive().toString();
			}
		}
		// header_nofollow
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.HEADER_NOFOLLOW)) {
			if (htmlClickHouseEntity.getCrawlerResponse().getHeader_nofollow() != null) {
				content = htmlClickHouseEntity.getCrawlerResponse().getHeader_nofollow().toString();
			}
		}
		// header_noindex
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.HEADER_NOINDEX)) {
			if (htmlClickHouseEntity.getCrawlerResponse().getHeader_noindex() != null) {
				content = htmlClickHouseEntity.getCrawlerResponse().getHeader_noindex().toString();
			}
		}
		// header_noodp
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.HEADER_NOODP)) {
			if (htmlClickHouseEntity.getCrawlerResponse().getHeader_noodp() != null) {
				content = htmlClickHouseEntity.getCrawlerResponse().getHeader_noodp().toString();
			}
		}
		// header_nosnippet
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.HEADER_NOSNIPPET)) {
			if (htmlClickHouseEntity.getCrawlerResponse().getHeader_nosnippet() != null) {
				content = htmlClickHouseEntity.getCrawlerResponse().getHeader_nosnippet().toString();
			}
		}
		// header_noydir
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.HEADER_NOYDIR)) {
			if (htmlClickHouseEntity.getCrawlerResponse().getHeader_noydir() != null) {
				content = htmlClickHouseEntity.getCrawlerResponse().getHeader_noydir().toString();
			}
		}
		// hreflang_errors
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.HREFLANG_ERRORS)) {
			hreflangErrors = htmlClickHouseEntity.getCrawlerResponse().getHreflang_errors();
			if (hreflangErrors != null) {
				json = gson.toJson(hreflangErrors, HreflangErrors.class);
				if (CrawlerUtils.getInstance().checkIfJsonDataAvailable(json) == true) {
					content = json;
				}
			}
		}
		// hreflang_links
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.HREFLANG_LINKS)) {
			hreflangLinks = htmlClickHouseEntity.getCrawlerResponse().getHreflang_links();
			if (hreflangLinks != null) {
				json = gson.toJson(hreflangLinks, HreflangLinks[].class);
				if (CrawlerUtils.getInstance().checkIfJsonDataAvailable(json) == true) {
					content = json;
				}
			}
		}
		// hreflang_links_out_count
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.HREFLANG_LINKS_OUT_COUNT)) {
			if (htmlClickHouseEntity.getCrawlerResponse().getHreflang_links_out_count() != null) {
				content = String.valueOf(htmlClickHouseEntity.getCrawlerResponse().getHreflang_links_out_count());
			}
		}
		// hreflang_url_count
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.HREFLANG_URL_COUNT)) {
			if (htmlClickHouseEntity.getCrawlerResponse().getHreflang_url_count() != null) {
				content = String.valueOf(htmlClickHouseEntity.getCrawlerResponse().getHreflang_url_count());
			}
		}
		// index_flg
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.INDEX_FLG)) {
			if (StringUtils.isNotBlank(htmlClickHouseEntity.getCrawlerResponse().getIndex_flg())) {
				content = htmlClickHouseEntity.getCrawlerResponse().getIndex_flg();
			}
		}
		// indexable
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.INDEXABLE)) {
			if (htmlClickHouseEntity.getCrawlerResponse().getIndexable() != null) {
				content = htmlClickHouseEntity.getCrawlerResponse().getIndexable().toString();
			}
		}
		// insecure_resources
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.INSECURE_RESOURCES)) {
			stringArray = htmlClickHouseEntity.getCrawlerResponse().getInsecure_resources();
			if (stringArray != null) {
				json = gson.toJson(stringArray, String[].class);
				if (CrawlerUtils.getInstance().checkIfJsonDataAvailable(json) == true) {
					content = json;
				}
			}
		}
		// meta_charset
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.META_CHARSET)) {
			if (StringUtils.isNotBlank(htmlClickHouseEntity.getCrawlerResponse().getMeta_charset())) {
				content = htmlClickHouseEntity.getCrawlerResponse().getMeta_charset();
			}
		}
		// meta_content_type
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.META_CONTENT_TYPE)) {
			if (StringUtils.isNotBlank(htmlClickHouseEntity.getCrawlerResponse().getMeta_content_type())) {
				content = htmlClickHouseEntity.getCrawlerResponse().getMeta_content_type();
			}
		}
		// meta_disabled_sitelinks
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.META_DISABLED_SITELINKS)) {
			if (htmlClickHouseEntity.getCrawlerResponse().getMeta_disabled_sitelinks() != null) {
				content = htmlClickHouseEntity.getCrawlerResponse().getMeta_disabled_sitelinks().toString();
			}
		}
		// meta_noodp
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.META_NOODP)) {
			if (htmlClickHouseEntity.getCrawlerResponse().getMeta_noodp() != null) {
				content = htmlClickHouseEntity.getCrawlerResponse().getMeta_noodp().toString();
			}
		}
		// meta_nosnippet
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.META_NOSNIPPET)) {
			if (htmlClickHouseEntity.getCrawlerResponse().getMeta_nosnippet() != null) {
				content = htmlClickHouseEntity.getCrawlerResponse().getMeta_nosnippet().toString();
			}
		}
		// meta_noydir
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.META_NOYDIR)) {
			if (htmlClickHouseEntity.getCrawlerResponse().getMeta_noydir() != null) {
				content = htmlClickHouseEntity.getCrawlerResponse().getMeta_noydir().toString();
			}
		}
		// meta_redirect
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.META_REDIRECT)) {
			if (htmlClickHouseEntity.getCrawlerResponse().getMeta_redirect() != null) {
				content = htmlClickHouseEntity.getCrawlerResponse().getMeta_redirect().toString();
			}
		}
		// mixed_redirects
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.MIXED_REDIRECTS)) {
			if (htmlClickHouseEntity.getCrawlerResponse().getMixed_redirects() != null) {
				content = htmlClickHouseEntity.getCrawlerResponse().getMixed_redirects().toString();
			}
		}
		// mobile_rel_alternate_url_is_consistent
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.MOBILE_REL_ALTERNATE_URL_IS_CONSISTENT)) {
			if (htmlClickHouseEntity.getCrawlerResponse().getMobile_rel_alternate_url_is_consistent() != null) {
				content = htmlClickHouseEntity.getCrawlerResponse().getMobile_rel_alternate_url_is_consistent().toString();
			}
		}
		// noodp
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.NOODP)) {
			if (htmlClickHouseEntity.getCrawlerResponse().getNoodp() != null) {
				content = htmlClickHouseEntity.getCrawlerResponse().getNoodp().toString();
			}
		}
		// nosnippet
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.NOSNIPPET)) {
			if (htmlClickHouseEntity.getCrawlerResponse().getNosnippet() != null) {
				content = htmlClickHouseEntity.getCrawlerResponse().getNosnippet().toString();
			}
		}
		// noydir
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.NOYDIR)) {
			if (htmlClickHouseEntity.getCrawlerResponse().getNoydir() != null) {
				content = htmlClickHouseEntity.getCrawlerResponse().getNoydir().toString();
			}
		}
		// og_markup
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.OG_MARKUP)) {
			ogMarkup = htmlClickHouseEntity.getCrawlerResponse().getOg_markup();
			if (ogMarkup != null) {
				json = gson.toJson(ogMarkup, OgMarkup[].class);
				if (CrawlerUtils.getInstance().checkIfJsonDataAvailable(json) == true) {
					content = json;
				}
			}
		}
		// og_markup_flag
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.OG_MARKUP_FLAG)) {
			if (htmlClickHouseEntity.getCrawlerResponse().getOg_markup_flag() != null) {
				content = htmlClickHouseEntity.getCrawlerResponse().getOg_markup_flag().toString();
			}
		}
		// og_markup_length
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.OG_MARKUP_LENGTH)) {
			if (htmlClickHouseEntity.getCrawlerResponse().getOg_markup_length() != null) {
				content = String.valueOf(htmlClickHouseEntity.getCrawlerResponse().getOg_markup_length());
			}
		}
		// outlink_count
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.OUTLINK_COUNT)) {
			if (htmlClickHouseEntity.getCrawlerResponse().getOutlink_count() != null) {
				content = String.valueOf(htmlClickHouseEntity.getCrawlerResponse().getOutlink_count());
			}
		}
		// page_analysis_fragments
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.PAGE_ANALYSIS_FRAGMENTS)) {
			pageAnalysisFragmentsArray = htmlClickHouseEntity.getPageAnalysisFragmentsArray();
			if (pageAnalysisFragmentsArray != null) {
				json = gson.toJson(pageAnalysisFragmentsArray, PageAnalysisFragments[].class);
				if (CrawlerUtils.getInstance().checkIfJsonDataAvailable(json) == true) {
					content = json;
				}
			}
		}
		// page_analysis_results_chg_ind_json
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.PAGE_ANALYSIS_RESULTS_CHG_IND_JSON)) {
			if (StringUtils.isNotBlank(htmlClickHouseEntity.getPageAnalysisResultsChgIndJson())) {
				content = htmlClickHouseEntity.getPageAnalysisResultsChgIndJson();
			}
		}
		// page_link
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.PAGE_LINK)) {
			pageLink = htmlClickHouseEntity.getCrawlerResponse().getPage_link();
			if (pageLink != null) {
				json = gson.toJson(pageLink, PageLink[].class);
				if (CrawlerUtils.getInstance().checkIfJsonDataAvailable(json) == true) {
					content = json;
				}
			}
		}
		// redirect_blocked
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.REDIRECT_BLOCKED)) {
			if (htmlClickHouseEntity.getCrawlerResponse().getRedirect_blocked() != null) {
				content = htmlClickHouseEntity.getCrawlerResponse().getRedirect_blocked().toString();
			}
		}
		// redirect_blocked_reason
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.REDIRECT_BLOCKED_REASON)) {
			if (StringUtils.isNotBlank(htmlClickHouseEntity.getCrawlerResponse().getRedirect_blocked_reason())) {
				content = htmlClickHouseEntity.getCrawlerResponse().getRedirect_blocked_reason();
			}
		}
		// redirect_chain
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.REDIRECT_CHAIN)) {
			redirectChain = htmlClickHouseEntity.getCrawlerResponse().getRedirect_chain();
			if (redirectChain != null) {
				json = gson.toJson(redirectChain, RedirectChain[].class);
				if (CrawlerUtils.getInstance().checkIfJsonDataAvailable(json) == true) {
					content = json;
				}
			}
		}
		// redirect_final_url
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.REDIRECT_FINAL_URL)) {
			if (StringUtils.isNotBlank(htmlClickHouseEntity.getCrawlerResponse().getRedirect_final_url())) {
				content = htmlClickHouseEntity.getCrawlerResponse().getRedirect_final_url();
			}
		}
		// redirect_times
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.REDIRECT_TIMES)) {
			if (htmlClickHouseEntity.getCrawlerResponse().getRedirect_times() != null) {
				content = String.valueOf(htmlClickHouseEntity.getCrawlerResponse().getRedirect_times());
			}
		}
		// response_code
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.RESPONSE_CODE)) {
			if (StringUtils.isNotBlank(htmlClickHouseEntity.getCrawlerResponse().getResponse_code())) {
				content = htmlClickHouseEntity.getCrawlerResponse().getResponse_code();
			}
		}
		// response_headers (names)
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.RESPONSE_HEADERS)) {
			responseHeaders = htmlClickHouseEntity.getCrawlerResponse().getResponse_headers();
			if (responseHeaders != null && responseHeaders.length > 0) {
				stringArray = CrawlerUtils.getInstance().getResponseHeaderNames(responseHeaders);
				testStringList = Arrays.asList(stringArray);
				content = StringUtils.removeEnd(StringUtils.removeStart(testStringList.toString(), IConstants.OPEN_BRACKET), IConstants.CLOSE_BRACKET);
			}
		}
		// robots
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.ROBOTS)) {
			if (StringUtils.isNotBlank(htmlClickHouseEntity.getCrawlerResponse().getRobots())) {
				content = htmlClickHouseEntity.getCrawlerResponse().getRobots();
			}
		}
		// robots_contents
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.ROBOTS_CONTENTS)) {
			if (StringUtils.isNotBlank(htmlClickHouseEntity.getCrawlerResponse().getRobots_contents())) {
				content = htmlClickHouseEntity.getCrawlerResponse().getRobots_contents();
			}
		}
		// robots_flg
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.ROBOTS_FLG)) {
			if (StringUtils.isNotBlank(htmlClickHouseEntity.getCrawlerResponse().getRobots_flg())) {
				content = htmlClickHouseEntity.getCrawlerResponse().getRobots_flg();
			}
		}
		// structured_data
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.STRUCTURED_DATA)) {
			structuredData = htmlClickHouseEntity.getCrawlerResponse().getStructured_data();
			if (CrawlerUtils.getInstance().checkIfStructuredDataAvailable(structuredData) == true) {
				json = gson.toJson(structuredData, StructuredData.class);
				if (CrawlerUtils.getInstance().checkIfJsonDataAvailable(json) == true) {
					content = json;
				}
			}
		}
		// title
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.TITLE)) {
			if (StringUtils.isNotBlank(htmlClickHouseEntity.getCrawlerResponse().getTitle())) {
				content = htmlClickHouseEntity.getCrawlerResponse().getTitle();
			}
		}
		// title_flg
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.TITLE_FLG)) {
			if (StringUtils.isNotBlank(htmlClickHouseEntity.getCrawlerResponse().getTitle_flg())) {
				content = htmlClickHouseEntity.getCrawlerResponse().getTitle_flg();
			}
		}
		// title_length
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.TITLE_LENGTH)) {
			if (htmlClickHouseEntity.getCrawlerResponse().getTitle_length() != null) {
				content = String.valueOf(htmlClickHouseEntity.getCrawlerResponse().getTitle_length());
			}
		}
		// viewport_content
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.VIEWPORT_CONTENT)) {
			if (StringUtils.isNotBlank(htmlClickHouseEntity.getCrawlerResponse().getViewport_content())) {
				content = htmlClickHouseEntity.getCrawlerResponse().getViewport_content();
			}
		}
		// viewport_flag
		else if (StringUtils.equalsIgnoreCase(trackedFieldName, IConstants.VIEWPORT_FLAG)) {
			if (htmlClickHouseEntity.getCrawlerResponse().getViewport_flag() != null) {
				content = htmlClickHouseEntity.getCrawlerResponse().getViewport_flag().toString();
			}
		}

		return content;
	}

	public List<String> getDatabaseFields(HtmlClickHouseEntity htmlClickHouseEntity) {
		List<String> databaseFields = null;
		Set<String> uniqueFieldSet = new HashSet<String>();
		for (ContentGuardChangeTrackingEntity contentGuardChangeTrackingEntity : getContentGuardChangeTrackingEntityList()) {
			// alternate_links_chg_ind
			if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.ALTERNATE_LINKS_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getAlternateLinksChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// amphtml_href_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.AMPHTML_HREF_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getAmphtmlHrefChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// analyzed_url_s_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.ANALYZED_URL_S_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getAnalyzedUrlSChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// archive_flg_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.ARCHIVE_FLG_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getArchiveFlgChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// base_tag_added_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.BASE_TAG_ADDED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getBaseTagAddedInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// base_tag_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.BASE_TAG_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getBaseTagChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// base_tag_removed_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.BASE_TAG_REMOVED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getBaseTagRemovedInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// base_tag_target_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.BASE_TAG_TARGET_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getBaseTagTargetChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// blocked_by_robots_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.BLOCKED_BY_ROBOTS_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getBlockedByRobotsChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// canonical_added_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CANONICAL_ADDED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getCanonicalAddedInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// canonical_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CANONICAL_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getCanonicalChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// canonical_header_flag_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CANONICAL_HEADER_FLAG_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getCanonicalHeaderFlagChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// canonical_header_type_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CANONICAL_HEADER_TYPE_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getCanonicalHeaderTypeChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// canonical_removed_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CANONICAL_REMOVED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getCanonicalRemovedInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// canonical_type_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CANONICAL_TYPE_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getCanonicalTypeChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// canonical_url_is_consistent_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CANONICAL_URL_IS_CONSISTENT_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getCanonicalUrlIsConsistentChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// content_type_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CONTENT_TYPE_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getContentTypeChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// custom_data_added_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CUSTOM_DATA_ADDED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getCustomDataAddedInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// custom_data_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CUSTOM_DATA_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getCustomDataChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// custom_data_removed_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CUSTOM_DATA_REMOVED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getCustomDataRemovedInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// description_added_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.DESCRIPTION_ADDED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getDescriptionAddedInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// description_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.DESCRIPTION_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getDescriptionChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// description_length_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.DESCRIPTION_LENGTH_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getDescriptionLengthChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// description_removed_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.DESCRIPTION_REMOVED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getDescriptionRemovedInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// error_message_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.ERROR_MESSAGE_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getErrorMessageChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// final_response_code_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.FINAL_RESPONSE_CODE_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getFinalResponseCodeChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// follow_flg_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.FOLLOW_FLG_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getFollowFlgChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// h1_added_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.H1_ADDED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getH1AddedInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// h1_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.H1_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getH1ChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// h1_count_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.H1_COUNT_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getH1CountChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// h1_length_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.H1_LENGTH_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getH1LengthChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// h1_removed_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.H1_REMOVED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getH1RemovedInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// h2_added_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.H2_ADDED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getH2AddedInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// h2_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.H2_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getH2ChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// h2_removed_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.H2_REMOVED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getH2RemovedInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// header_noarchive_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HEADER_NOARCHIVE_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getHeaderNoarchiveChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// header_nofollow_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HEADER_NOFOLLOW_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getHeaderNofollowChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// header_noindex_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HEADER_NOINDEX_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getHeaderNoindexChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// header_noodp_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HEADER_NOODP_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getHeaderNoodpChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// header_nosnippet_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HEADER_NOSNIPPET_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getHeaderNosnippetChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// header_noydir_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HEADER_NOYDIR_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getHeaderNoydirChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// hreflang_errors_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HREFLANG_ERRORS_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getHreflangErrorsChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// hreflang_links_added_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HREFLANG_LINKS_ADDED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getHreflangLinksAddedInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
					uniqueFieldSet.add(IConstants.HREFLANG_LINKS);
				}
			}
			// hreflang_links_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HREFLANG_LINKS_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getHreflangLinksChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
					uniqueFieldSet.add(IConstants.HREFLANG_URL_COUNT);
				}
			}
			// hreflang_links_out_count_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HREFLANG_LINKS_OUT_COUNT_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getHreflangLinksOutCountChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// hreflang_links_removed_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HREFLANG_LINKS_REMOVED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getHreflangLinksRemovedInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
					uniqueFieldSet.add(IConstants.HREFLANG_LINKS);
				}
			}
			// hreflang_url_count_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HREFLANG_URL_COUNT_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getHreflangUrlCountChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// index_flg_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.INDEX_FLG_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getIndexFlgChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// indexable_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.INDEXABLE_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getIndexableChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// insecure_resources_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.INSECURE_RESOURCES_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getInsecureResourcesChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// meta_charset_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.META_CHARSET_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getMetaCharsetChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// meta_content_type_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.META_CONTENT_TYPE_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getMetaContentTypeChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// meta_disabled_sitelinks_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.META_DISABLED_SITELINKS_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getMetaDisabledSitelinksChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// meta_noodp_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.META_NOODP_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getMetaNoodpChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// meta_nosnippet_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.META_NOSNIPPET_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getMetaNosnippetChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// meta_noydir_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.META_NOYDIR_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getMetaNoydirChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// meta_redirect_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.META_REDIRECT_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getMetaRedirectChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// mixed_redirects_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.MIXED_REDIRECTS_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getMixedRedirectsChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// mobile_rel_alternate_url_is_consistent_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.MOBILE_REL_ALTERNATE_URL_IS_CONSISTENT_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getMobileRelAlternateUrlIsConsistentChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// noodp_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.NOODP_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getNoodpChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// nosnippet_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.NOSNIPPET_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getNosnippetChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// noydir_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.NOYDIR_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getNoydirChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// og_markup_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.OG_MARKUP_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getOgMarkupChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// og_markup_length_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.OG_MARKUP_LENGTH_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getOgMarkupLengthChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// open_graph_added_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.OPEN_GRAPH_ADDED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getOpenGraphAddedInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// open_graph_removed_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.OPEN_GRAPH_REMOVED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getOpenGraphRemovedInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// outlink_count_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.OUTLINK_COUNT_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getOutlinkCountChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// page_analysis_fragments_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.PAGE_ANALYSIS_FRAGMENTS_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getPageAnalysisFragmentsChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// page_analysis_results_chg_ind_json
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.PAGE_ANALYSIS_RESULTS_CHG_IND_JSON)) {
				if (StringUtils.isNotBlank(htmlClickHouseEntity.getPageAnalysisResultsChgIndJson())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// page_link_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.PAGE_LINK_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getPageLinkChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// redirect_301_detected_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_301_DETECTED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getRedirect301DetectedInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
					uniqueFieldSet.add(IConstants.REDIRECT_FINAL_URL);
				}
			}
			// redirect_301_removed_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_301_REMOVED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getRedirect301RemovedInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// redirect_302_detected_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_302_DETECTED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getRedirect302DetectedInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
					uniqueFieldSet.add(IConstants.REDIRECT_FINAL_URL);
				}
			}
			// redirect_302_removed_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_302_REMOVED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getRedirect302RemovedInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// redirect_blocked_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_BLOCKED_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getRedirectBlockedChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// redirect_blocked_reason_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_BLOCKED_REASON_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getRedirectBlockedReasonChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// redirect_chain_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_CHAIN_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getRedirectChainChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// redirect_diff_code_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_DIFF_CODE_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getRedirectDiffCodeInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// redirect_final_url_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_FINAL_URL_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getRedirectFinalUrlChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// redirect_times_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_TIMES_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getRedirectTimesChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// response_code_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.RESPONSE_CODE_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getResponseCodeChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// response_headers_added_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.RESPONSE_HEADERS_ADDED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getResponseHeadersAddedInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// response_headers_removed_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.RESPONSE_HEADERS_REMOVED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getResponseHeadersRemovedInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// robots_added_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.ROBOTS_ADDED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getRobotsAddedInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// robots_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.ROBOTS_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getRobotsChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// robots_contents_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.ROBOTS_CONTENTS_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getRobotsContentsChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// robots_removed_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.ROBOTS_REMOVED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getRobotsRemovedInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// structured_data_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.STRUCTURED_DATA_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getStructuredDataChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// title_added_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.TITLE_ADDED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getTitleAddedInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// title_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.TITLE_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getTitleChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// title_length_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.TITLE_LENGTH_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getTitleLengthChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// title_removed_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.TITLE_REMOVED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getTitleRemovedInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// viewport_added_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.VIEWPORT_ADDED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getViewportAddedInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// viewport_content_chg_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.VIEWPORT_CONTENT_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getViewportContentChgInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
			// viewport_removed_ind
			else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.VIEWPORT_REMOVED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntity.getViewportRemovedInd())) {
					uniqueFieldSet.add(contentGuardChangeTrackingEntity.getField());
				}
			}
		}
		if (uniqueFieldSet != null && uniqueFieldSet.size() > 0) {
			databaseFields = new ArrayList<String>(uniqueFieldSet);
		} else {
			databaseFields = new ArrayList<String>();
		}
		return databaseFields;
	}

	public List<ContentGuardSkipUrlEntity> getContentGuardSkipUrlEntityList(int domainId, Long groupId) {
		return contentGuardSkipUrlDAO.getList(domainId, groupId);
	}

	public Map<String, String> getCriticalIndicatorTypeMap() {
		List<ContentGuardChangeTrackingEntity> criticalIndicatorList = null;
		if (criticalIndicatorTypeMap == null || criticalIndicatorTypeMap.size() == 0) {
			criticalIndicatorList = contentGuardChangeTrackingDAO.getCriticalIndicatorList();
			if (criticalIndicatorList != null && criticalIndicatorList.size() > 0) {
				criticalIndicatorTypeMap = new TreeMap<String, String>();
				for (ContentGuardChangeTrackingEntity contentGuardChangeTrackingEntity : criticalIndicatorList) {
					criticalIndicatorTypeMap.put(contentGuardChangeTrackingEntity.getIndicator(), contentGuardChangeTrackingEntity.getType());
				}
			}
			if (criticalIndicatorTypeMap != null && criticalIndicatorTypeMap.size() > 0) {
				FormatUtils.getInstance().logMemoryUsage("getCriticalIndicatorTypeMap() criticalIndicatorTypeMap.size()=" + criticalIndicatorTypeMap.size());
				//for (String criticalIndicator : criticalIndicatorTypeMap.keySet()) {
				//	FormatUtils.getInstance().logMemoryUsage(
				//			"getCriticalIndicatorTypeMap() criticalIndicator=" + criticalIndicator + ",changeType=" + criticalIndicatorTypeMap.get(criticalIndicator));
				//}
			} else {
				FormatUtils.getInstance().logMemoryUsage("getCriticalIndicatorTypeMap() error--criticalIndicatorTypeMap is empty.");
			}
		}
		return criticalIndicatorTypeMap;
	}

	public Map<String, String> getCustomIndicatorTypeMap(String[] customIndicatorArray) {
		Map<String, String> customIndicatorTypeMap = new TreeMap<String, String>();
		for (String customIndicator : customIndicatorArray) {
			for (ContentGuardChangeTrackingEntity contentGuardChangeTrackingEntity : getContentGuardChangeTrackingEntityList()) {
				if (StringUtils.equalsIgnoreCase(customIndicator, contentGuardChangeTrackingEntity.getIndicator())) {
					customIndicatorTypeMap.put(contentGuardChangeTrackingEntity.getIndicator(), contentGuardChangeTrackingEntity.getType());
				}
			}
		}
		//if (customIndicatorTypeMap != null && customIndicatorTypeMap.size() > 0) {
		//	for (String customIndicator : customIndicatorTypeMap.keySet()) {
		//		FormatUtils.getInstance().logMemoryUsage(
		//				"getCustomIndicatorTypeMap() customIndicator=" + customIndicator + ",changeType=" + customIndicatorTypeMap.get(customIndicator));
		//	}
		//} else {
		//	FormatUtils.getInstance().logMemoryUsage("getCustomIndicatorTypeMap() error--customIndicatorTypeMap is empty.");
		//}
		return customIndicatorTypeMap;
	}

	public boolean checkIfChangeIndicatorIsValid(String changeIndicator) {
		boolean output = false;
		String testChangeIndicator = null;
		if (StringUtils.startsWithIgnoreCase(changeIndicator, IConstants.RESPONSE_CODE_CHG_IND)) {
			testChangeIndicator = IConstants.RESPONSE_CODE_CHG_IND;
		} else {
			testChangeIndicator = changeIndicator;
		}
		nextContentGuardChangeTrackingEntity: for (ContentGuardChangeTrackingEntity contentGuardChangeTrackingEntity : getContentGuardChangeTrackingEntityList()) {
			if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), testChangeIndicator)) {
				output = true;
				break nextContentGuardChangeTrackingEntity;
			}
		}
		return output;
	}

	public String getErrorMessage(String errorCode, String supplementalMessageText) {
		String errorMessage = null;
		String errorTemplate = ContentGuardWebServiceMessage.getStringProperty(errorCode);
		FormatUtils.getInstance().logMemoryUsage("getErrorMessage() errorTemplate=" + errorTemplate);
		if (StringUtils.isNotBlank(supplementalMessageText)) {
			errorMessage = MessageFormat.format(errorTemplate, supplementalMessageText);
		} else {
			errorMessage = errorTemplate;
		}
		FormatUtils.getInstance().logMemoryUsage("getErrorMessage() errorMessage=" + errorMessage);
		return errorMessage;
	}

	public boolean checkIfCreateChangeDetails(ContentGuardChangeTrackingEntity contentGuardChangeTrackingEntity, HtmlClickHouseEntity htmlClickHouseEntityIndicators,
			List<ContentGuardSkipUrlEntity> contentGuardSkipUrlEntityList) throws Exception {
		boolean output = true;

		boolean isSkipUrl = checkIfSkipUrlForIndicator(contentGuardChangeTrackingEntity.getIndicator(), contentGuardSkipUrlEntityList,
				htmlClickHouseEntityIndicators.getUrl());
		if (isSkipUrl == true) {
			output = false;
			return output;
		}

		// base_tag
		if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getField(), IConstants.BASE_TAG)) {
			if ((StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.BASE_TAG_ADDED_IND)
					&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getBaseTagAddedInd()))
					|| (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.BASE_TAG_CHG_IND)
							&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getBaseTagChgInd()))
					|| (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.BASE_TAG_REMOVED_IND)
							&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getBaseTagRemovedInd()))) {
				// proceed
			} else {
				output = false;
			}
		}
		// canonical
		else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getField(), IConstants.CANONICAL)) {
			if ((StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CANONICAL_ADDED_IND)
					&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getCanonicalAddedInd()))
					|| (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CANONICAL_CHG_IND)
							&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getCanonicalChgInd()))
					|| (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CANONICAL_REMOVED_IND)
							&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getCanonicalRemovedInd()))) {
				// proceed
			} else {
				output = false;
			}
		}
		// custom_data
		else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getField(), IConstants.CUSTOM_DATA)) {
			if ((StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CUSTOM_DATA_ADDED_IND)
					&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getCustomDataAddedInd()))
					|| (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CUSTOM_DATA_CHG_IND)
							&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getCustomDataChgInd()))
					|| (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CUSTOM_DATA_REMOVED_IND)
							&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getCustomDataRemovedInd()))) {
				// proceed
			} else {
				output = false;
			}
		}
		// description
		else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getField(), IConstants.DESCRIPTION)) {
			if ((StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.DESCRIPTION_ADDED_IND)
					&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getDescriptionAddedInd()))
					|| (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.DESCRIPTION_CHG_IND)
							&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getDescriptionChgInd()))
					|| (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.DESCRIPTION_REMOVED_IND)
							&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getDescriptionRemovedInd()))) {
				// proceed
			} else {
				output = false;
			}
		}
		// h1
		else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getField(), IConstants.H1)) {
			if ((StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.H1_ADDED_IND)
					&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getH1AddedInd()))
					|| (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.H1_CHG_IND)
							&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getH1ChgInd()))
					|| (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.H1_REMOVED_IND)
							&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getH1RemovedInd()))) {
				// proceed
			} else {
				output = false;
			}
		}
		// h2
		else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getField(), IConstants.H2)) {
			if ((StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.H2_ADDED_IND)
					&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getH2AddedInd()))
					|| (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.H2_CHG_IND)
							&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getH2ChgInd()))
					|| (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.H2_REMOVED_IND)
							&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getH2RemovedInd()))) {
				// proceed
			} else {
				output = false;
			}
		}
		// hreflang_url_count
		else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getField(), IConstants.HREFLANG_URL_COUNT)) {
			if ((StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HREFLANG_LINKS_ADDED_IND)
					&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getHreflangLinksAddedInd()))
					|| (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HREFLANG_LINKS_REMOVED_IND)
							&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getHreflangLinksRemovedInd()))
					|| (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HREFLANG_URL_COUNT_CHG_IND)
							&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getHreflangUrlCountChgInd()))) {
				// proceed
			} else {
				output = false;
			}
		}
		// og_markup
		else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getField(), IConstants.OG_MARKUP)) {
			if ((StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.OPEN_GRAPH_ADDED_IND)
					&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getOpenGraphAddedInd()))
					|| (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.OG_MARKUP_CHG_IND)
							&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getOgMarkupChgInd()))
					|| (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.OPEN_GRAPH_REMOVED_IND)
							&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getOpenGraphRemovedInd()))) {
				// proceed
			} else {
				output = false;
			}
		}
		// response_code
		else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getField(), IConstants.RESPONSE_CODE)) {
			if ((StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_301_DETECTED_IND)
					&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getRedirect301DetectedInd()))
					|| (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_301_REMOVED_IND)
							&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getRedirect301RemovedInd()))
					|| (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_302_DETECTED_IND)
							&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getRedirect302DetectedInd()))
					|| (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_302_REMOVED_IND)
							&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getRedirect302RemovedInd()))
					|| (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_DIFF_CODE_IND)
							&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getRedirectDiffCodeInd()))
					|| (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.RESPONSE_CODE_CHG_IND)
							&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getResponseCodeChgInd()))) {
				// proceed
			} else {
				output = false;
			}
		}
		// response_headers
		else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getField(), IConstants.RESPONSE_HEADERS)) {
			if ((StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.RESPONSE_HEADERS_ADDED_IND)
					&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getResponseHeadersAddedInd()))
					|| (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.RESPONSE_HEADERS_REMOVED_IND)
							&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getResponseHeadersRemovedInd()))) {
				// proceed
			} else {
				output = false;
			}
		}
		// robots_contents
		else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getField(), IConstants.ROBOTS_CONTENTS)) {
			if ((StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.ROBOTS_ADDED_IND)
					&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getRobotsAddedInd()))
					|| (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.ROBOTS_CONTENTS_CHG_IND)
							&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getRobotsContentsChgInd()))
					|| (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.ROBOTS_REMOVED_IND)
							&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getRobotsRemovedInd()))) {
				// proceed
			} else {
				output = false;
			}
		}
		// title
		else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getField(), IConstants.TITLE)) {
			if ((StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.TITLE_ADDED_IND)
					&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getTitleAddedInd()))
					|| (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.TITLE_CHG_IND)
							&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getTitleChgInd()))
					|| (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.TITLE_REMOVED_IND)
							&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getTitleRemovedInd()))) {
				// proceed
			} else {
				output = false;
			}
		}
		// viewport_content
		else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getField(), IConstants.VIEWPORT_CONTENT)) {
			if ((StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.VIEWPORT_ADDED_IND)
					&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getViewportAddedInd()))
					|| (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.VIEWPORT_CONTENT_CHG_IND)
							&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getViewportContentChgInd()))
					|| (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.VIEWPORT_REMOVED_IND)
							&& BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getViewportRemovedInd()))) {
				// proceed
			} else {
				output = false;
			}
		}
		return output;
	}

	public ContentGuardChangeDetails getCurrentAndPreviousContent(ContentGuardChangeTrackingEntity contentGuardChangeTrackingEntity,
			HtmlClickHouseEntity htmlClickHouseEntityIndicators, HtmlClickHouseEntity htmlClickHouseEntityCurrent, HtmlClickHouseEntity htmlClickHouseEntityPrevious)
			throws Exception {

		String testStringPrevious = null;
		String testStringCurrent = null;
		PageAnalysisResultChgInd[] pageAnalysisResultChgIndArray = null;
		JsonObject rootObject = null;
		PageAnalysisResultChgInd pageAnalysisResultChgInd = null;
		int index = 0;
		JsonParser jsonParser = null;
		JsonElement jsonElement = null;

		ContentGuardChangeDetails contentGuardChangeDetails = new ContentGuardChangeDetails();
		contentGuardChangeDetails.setChange_type(contentGuardChangeTrackingEntity.getType());
		contentGuardChangeDetails.setChange_indicator(contentGuardChangeTrackingEntity.getIndicator());
		if (contentGuardChangeTrackingEntity.getCriticalFlag() != null) {
			contentGuardChangeDetails.setSeverity(contentGuardChangeTrackingEntity.getCriticalFlag());
		} else {
			contentGuardChangeDetails.setSeverity(0);
		}
		contentGuardChangeDetails.setChange_field(contentGuardChangeTrackingEntity.getField());

		// previous
		testStringPrevious = getFieldContent(contentGuardChangeTrackingEntity.getField(), htmlClickHouseEntityPrevious);
		if (StringUtils.isNotBlank(testStringPrevious)) {
			contentGuardChangeDetails.setPrevious_content(testStringPrevious);
		} else {
			contentGuardChangeDetails.setPrevious_content(IConstants.EMPTY_STRING);
		}

		// current
		testStringCurrent = getFieldContent(contentGuardChangeTrackingEntity.getField(), htmlClickHouseEntityCurrent);
		if (StringUtils.isNotBlank(testStringCurrent)) {
			contentGuardChangeDetails.setCurrent_content(testStringCurrent);
		} else {
			contentGuardChangeDetails.setCurrent_content(IConstants.EMPTY_STRING);
		}

		// alternate_links
		if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getField(), IConstants.ALTERNATE_LINKS)) {
			if (StringUtils.isNotBlank(testStringPrevious)) {
				contentGuardChangeDetails.setAlternate_links_previous_array(new Gson().fromJson(testStringPrevious, AlternateLinks[].class));
				contentGuardChangeDetails.setPrevious_content(null);
			}
			if (StringUtils.isNotBlank(testStringCurrent)) {
				contentGuardChangeDetails.setAlternate_links_current_array(new Gson().fromJson(testStringCurrent, AlternateLinks[].class));
				contentGuardChangeDetails.setCurrent_content(null);
			}
		}
		// custom_data
		else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getField(), IConstants.CUSTOM_DATA)) {
			if (StringUtils.isNotBlank(testStringPrevious)) {
				contentGuardChangeDetails.setCustom_data_previous_array(new Gson().fromJson(testStringPrevious, CustomData[].class));
				contentGuardChangeDetails.setPrevious_content(null);
			}
			if (StringUtils.isNotBlank(testStringCurrent)) {
				contentGuardChangeDetails.setCustom_data_current_array(new Gson().fromJson(testStringCurrent, CustomData[].class));
				contentGuardChangeDetails.setCurrent_content(null);
			}
		}
		// h1
		else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getField(), IConstants.H1)) {
			if (StringUtils.isNotBlank(testStringPrevious)) {
				contentGuardChangeDetails.setH1_previous_array(new Gson().fromJson(testStringPrevious, String[].class));
				contentGuardChangeDetails.setPrevious_content(null);
			}
			if (StringUtils.isNotBlank(testStringCurrent)) {
				contentGuardChangeDetails.setH1_current_array(new Gson().fromJson(testStringCurrent, String[].class));
				contentGuardChangeDetails.setCurrent_content(null);
			}
		}
		// h2
		else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getField(), IConstants.H2)) {
			if (StringUtils.isNotBlank(testStringPrevious)) {
				contentGuardChangeDetails.setH2_previous_array(new Gson().fromJson(testStringPrevious, String[].class));
				contentGuardChangeDetails.setPrevious_content(null);
			}
			if (StringUtils.isNotBlank(testStringCurrent)) {
				contentGuardChangeDetails.setH2_current_array(new Gson().fromJson(testStringCurrent, String[].class));
				contentGuardChangeDetails.setCurrent_content(null);
			}
		}
		// hreflang_errors
		else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getField(), IConstants.HREFLANG_ERRORS)) {
			if (StringUtils.isNotBlank(testStringPrevious)) {
				contentGuardChangeDetails.setHreflang_errors_previous(new Gson().fromJson(testStringPrevious, HreflangErrors.class));
				contentGuardChangeDetails.setPrevious_content(null);
			}
			if (StringUtils.isNotBlank(testStringCurrent)) {
				contentGuardChangeDetails.setHreflang_errors_current(new Gson().fromJson(testStringCurrent, HreflangErrors.class));
				contentGuardChangeDetails.setCurrent_content(null);
			}
		}
		// hreflang_links
		else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getField(), IConstants.HREFLANG_LINKS)) {
			if (StringUtils.isNotBlank(testStringPrevious)) {
				contentGuardChangeDetails.setHreflang_links_previous_array(new Gson().fromJson(testStringPrevious, HreflangLinks[].class));
				contentGuardChangeDetails.setPrevious_content(null);
			}
			if (StringUtils.isNotBlank(testStringCurrent)) {
				contentGuardChangeDetails.setHreflang_links_current_array(new Gson().fromJson(testStringCurrent, HreflangLinks[].class));
				contentGuardChangeDetails.setCurrent_content(null);
			}
		}
		// insecure_resources
		else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getField(), IConstants.INSECURE_RESOURCES)) {
			if (StringUtils.isNotBlank(testStringPrevious)) {
				contentGuardChangeDetails.setInsecure_resources_previous_array(new Gson().fromJson(testStringPrevious, String[].class));
				contentGuardChangeDetails.setPrevious_content(null);
			}
			if (StringUtils.isNotBlank(testStringCurrent)) {
				contentGuardChangeDetails.setInsecure_resources_current_array(new Gson().fromJson(testStringCurrent, String[].class));
				contentGuardChangeDetails.setCurrent_content(null);
			}
		}
		// og_markup
		else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getField(), IConstants.OG_MARKUP)) {
			if (StringUtils.isNotBlank(testStringPrevious)) {
				contentGuardChangeDetails.setOg_markup_previous_array(new Gson().fromJson(testStringPrevious, OgMarkup[].class));
				contentGuardChangeDetails.setPrevious_content(null);
			}
			if (StringUtils.isNotBlank(testStringCurrent)) {
				contentGuardChangeDetails.setOg_markup_current_array(new Gson().fromJson(testStringCurrent, OgMarkup[].class));
				contentGuardChangeDetails.setCurrent_content(null);
			}
		}
		// page_analysis_results_chg_ind_json
		else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getField(), IConstants.PAGE_ANALYSIS_RESULTS_CHG_IND_JSON)) {
			if (StringUtils.isNotBlank(testStringCurrent)) {
				jsonParser = new JsonParser();
				jsonElement = jsonParser.parse(testStringCurrent);
				rootObject = jsonElement.getAsJsonObject();
				pageAnalysisResultChgIndArray = new PageAnalysisResultChgInd[rootObject.keySet().size()];
				for (String ruleNbr : rootObject.keySet()) {
					pageAnalysisResultChgInd = new PageAnalysisResultChgInd();
					pageAnalysisResultChgInd.setRule_nbr(NumberUtils.toInt(ruleNbr));
					pageAnalysisResultChgInd.setChg_ind(NumberUtils.toInt(rootObject.get(ruleNbr).getAsString()));
					pageAnalysisResultChgIndArray[index++] = pageAnalysisResultChgInd;
				}
				contentGuardChangeDetails.setPage_analysis_result_chg_ind_array(pageAnalysisResultChgIndArray);
				contentGuardChangeDetails.setPrevious_content(null);
				contentGuardChangeDetails.setCurrent_content(null);
			}
		}
		// page_link
		else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getField(), IConstants.PAGE_LINK)) {
			if (StringUtils.isNotBlank(testStringPrevious)) {
				contentGuardChangeDetails.setPage_link_previous_array(new Gson().fromJson(testStringPrevious, PageLink[].class));
				contentGuardChangeDetails.setPrevious_content(null);
			}
			if (StringUtils.isNotBlank(testStringCurrent)) {
				contentGuardChangeDetails.setPage_link_current_array(new Gson().fromJson(testStringCurrent, PageLink[].class));
				contentGuardChangeDetails.setCurrent_content(null);
			}
		}
		// redirect_chain
		else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getField(), IConstants.REDIRECT_CHAIN)) {
			if (StringUtils.isNotBlank(testStringPrevious)) {
				contentGuardChangeDetails.setRedirect_chain_previous_array(new Gson().fromJson(testStringPrevious, RedirectChain[].class));
				contentGuardChangeDetails.setPrevious_content(null);
			}
			if (StringUtils.isNotBlank(testStringCurrent)) {
				contentGuardChangeDetails.setRedirect_chain_current_array(new Gson().fromJson(testStringCurrent, RedirectChain[].class));
				contentGuardChangeDetails.setCurrent_content(null);
			}
		}
		// response_header
		else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getField(), IConstants.RESPONSE_HEADERS)) {
			if (StringUtils.isNotBlank(testStringPrevious)) {
				contentGuardChangeDetails.setResponse_header_previous_array(testStringPrevious.split(IConstants.COMMA));
				for (int i = 0; i < contentGuardChangeDetails.getResponse_header_previous_array().length; i++) {
					contentGuardChangeDetails.getResponse_header_previous_array()[i] = StringUtils
							.trim(contentGuardChangeDetails.getResponse_header_previous_array()[i]);
				}
				contentGuardChangeDetails.setPrevious_content(null);
			}
			if (StringUtils.isNotBlank(testStringCurrent)) {
				contentGuardChangeDetails.setResponse_header_current_array(testStringCurrent.split(IConstants.COMMA));
				for (int i = 0; i < contentGuardChangeDetails.getResponse_header_current_array().length; i++) {
					contentGuardChangeDetails.getResponse_header_current_array()[i] = StringUtils.trim(contentGuardChangeDetails.getResponse_header_current_array()[i]);
				}
				contentGuardChangeDetails.setCurrent_content(null);
			}
		}
		// structured_data
		else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getField(), IConstants.STRUCTURED_DATA)) {
			if (StringUtils.isNotBlank(testStringPrevious)) {
				contentGuardChangeDetails.setStructured_data_previous(new Gson().fromJson(testStringPrevious, StructuredData.class));
				contentGuardChangeDetails.setPrevious_content(null);
			}
			if (StringUtils.isNotBlank(testStringCurrent)) {
				contentGuardChangeDetails.setStructured_data_current(new Gson().fromJson(testStringCurrent, StructuredData.class));
				contentGuardChangeDetails.setCurrent_content(null);
			}
		}

		return contentGuardChangeDetails;
	}

	public ContentGuardResourceResponse getChangeTrackingDetails(int domainId, Long groupId, HtmlClickHouseEntity htmlClickHouseEntityIndicators) throws Exception {
		ContentGuardResourceResponse response = new ContentGuardResourceResponse();
		response.setSuccess(true);
		List<String> databaseFields = null;
		List<HtmlClickHouseEntity> testHtmlClickHouseEntityList = null;
		HtmlClickHouseEntity htmlClickHouseEntityPrevious = null;
		HtmlClickHouseEntity htmlClickHouseEntityCurrent = null;
		WebServiceError webServiceError = null;
		List<ContentGuardChangeDetails> contentGuardChangeDetailsList = new ArrayList<ContentGuardChangeDetails>();
		ContentGuardChangeDetails contentGuardChangeDetails = null;

		List<ContentGuardSkipUrlEntity> contentGuardSkipUrlEntityList = getContentGuardSkipUrlEntityList(domainId, groupId);

		databaseFields = getDatabaseFields(htmlClickHouseEntityIndicators);
		testHtmlClickHouseEntityList = ContentGuardClickHouseDAO.getInstance().getChangeTrackingFields(domainId, htmlClickHouseEntityIndicators.getUrl(),
				htmlClickHouseEntityIndicators.getCrawlTimestamp(), databaseFields);
		//FormatUtils.getInstance().logMemoryUsage("getChangeTrackingDetails() url=" + htmlClickHouseEntityIndicators.getUrl() + ",testHtmlClickHouseEntityList.size()="
		//		+ testHtmlClickHouseEntityList.size());
		if (testHtmlClickHouseEntityList != null && testHtmlClickHouseEntityList.size() == 2) {
			response.setUrl(htmlClickHouseEntityIndicators.getUrl());

			htmlClickHouseEntityCurrent = testHtmlClickHouseEntityList.get(0);
			response.setCurrent_crawl_timestamp(DateFormatUtils.format(htmlClickHouseEntityCurrent.getCrawlTimestamp(), IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS));

			htmlClickHouseEntityPrevious = testHtmlClickHouseEntityList.get(1);
			response.setPrevious_crawl_timestamp(DateFormatUtils.format(htmlClickHouseEntityPrevious.getCrawlTimestamp(), IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS));

			for (ContentGuardChangeTrackingEntity contentGuardChangeTrackingEntity : getContentGuardChangeTrackingEntityList()) {
				nextDatabaseField: for (String databaseField : databaseFields) {
					if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getField(), databaseField)) {
						if (checkIfCreateChangeDetails(contentGuardChangeTrackingEntity, htmlClickHouseEntityIndicators, contentGuardSkipUrlEntityList) == true) {
							contentGuardChangeDetails = getCurrentAndPreviousContent(contentGuardChangeTrackingEntity, htmlClickHouseEntityIndicators,
									htmlClickHouseEntityCurrent, htmlClickHouseEntityPrevious);
							if (contentGuardChangeDetails != null) {
								contentGuardChangeDetailsList.add(contentGuardChangeDetails);
							}
							break nextDatabaseField;
						}
					}
				}
			}
			if (contentGuardChangeDetailsList != null && contentGuardChangeDetailsList.size() > 0) {
				response.setUrl_change_details_list(contentGuardChangeDetailsList);
			}
		} else {
			response.setSuccess(false);
			webServiceError = new WebServiceError();
			webServiceError.setError_code(IConstants.MSG_CD_CONTENT_GUARD_WEB_SERVICE_METHOD_EXCEPTION);
			webServiceError.setError_message("Crawl data not available for URL " + htmlClickHouseEntityIndicators.getUrl() + " crawl timestamp "
					+ htmlClickHouseEntityIndicators.getCrawlTimestamp());
			response.setError(webServiceError);
		}
		return response;
	}

	// update 'response' using pass by reference
	public void processIndicatorUrlList(int domainId, Long groupId, ContentGuardResourceResponse response, List<HtmlClickHouseEntity> htmlClickHouseEntityInputList,
			Map<String, String> md5HashContentGuardUrlMap, int pageNumber, int rowsPerPage, Boolean returnDetails, String filterChangeIndicator) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("processIndicatorUrlList() begins. domainId=" + domainId + ",groupId=" + groupId
				+ ",htmlClickHouseEntityInputList.size()=" + htmlClickHouseEntityInputList.size());
		String crawlTimestamp = null;
		String responseCode = null;
		Integer totalUrls = 0;
		Integer totalRedirectedUrls = 0;
		boolean indicatorValue = false;
		boolean isSkipUrl = false;
		ContentGuardChangeTrackingEntity clonedContentGuardChangeTrackingEntity = null;
		int totalPagesChanged = 0;

		List<ContentGuardIndicatorUrlChanges> contentGuardIndicatorUrlChangesList = new ArrayList<ContentGuardIndicatorUrlChanges>();

		// map key = response code
		// map value = total number of URLs
		Map<String, Integer> responseCodeTotalUrlsMap = new TreeMap<String, Integer>();

		List<ContentGuardSkipUrlEntity> contentGuardSkipUrlEntityList = getContentGuardSkipUrlEntityList(domainId, groupId);
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = deDuplicateUrls(domainId, htmlClickHouseEntityInputList, contentGuardSkipUrlEntityList);

		// when after filtering, htmlClickHouseEntityList becomes empty
		if (htmlClickHouseEntityList == null || htmlClickHouseEntityList.size() == 0) {
			return;
		}

		// total pages tracked
		response.setTotal_pages_tracked(md5HashContentGuardUrlMap.size());
		//FormatUtils.getInstance().logMemoryUsage("processIndicatorUrlList() totalPagesTracked=" + response.getTotal_pages_tracked());

		response.setTotal_pages_changed(htmlClickHouseEntityList.size());
		//FormatUtils.getInstance().logMemoryUsage("processIndicatorUrlList() totalPagesChanged=" + response.getTotal_pages_changed());

		response.setTotal_changes(0);
		response.setTotal_changes_added(0);
		response.setTotal_changes_modified(0);
		response.setTotal_changes_removed(0);
		response.setChange_tracking_indicator_totals_map(new TreeMap<String, Integer>());
		response.setChange_severity(new TreeMap<Integer, Integer>());
		response.setIndicator_url_list(new ArrayList<ContentGuardIndicatorUrlChanges>());
		if (StringUtils.isNotBlank(filterChangeIndicator)) {
			response.setFilter_change_indicator(filterChangeIndicator);
		}

		for (HtmlClickHouseEntity htmlClickHouseEntity : htmlClickHouseEntityList) {
			crawlTimestamp = DateFormatUtils.format(htmlClickHouseEntity.getCrawlTimestamp(), IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
			nextContentGuardChangeTrackingEntity: for (ContentGuardChangeTrackingEntity contentGuardChangeTrackingEntity : getContentGuardChangeTrackingEntityList()) {

				if (StringUtils.isNotBlank(filterChangeIndicator)) {
					if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), filterChangeIndicator) == false) {
						continue nextContentGuardChangeTrackingEntity;
					} else {
						totalPagesChanged = totalPagesChanged + 1;
					}
				}

				// alternate_links_chg_ind
				if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.ALTERNATE_LINKS_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getAlternateLinksChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// amphtml_href_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.AMPHTML_HREF_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getAmphtmlHrefChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// analyzed_url_s_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.ANALYZED_URL_S_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getAnalyzedUrlSChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// archive_flg_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.ARCHIVE_FLG_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getArchiveFlgChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// base_tag_added_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.BASE_TAG_ADDED_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getBaseTagAddedInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// base_tag_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.BASE_TAG_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getBaseTagChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// base_tag_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.BASE_TAG_REMOVED_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getBaseTagRemovedInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// base_tag_target_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.BASE_TAG_TARGET_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getBaseTagTargetChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// blocked_by_robots_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.BLOCKED_BY_ROBOTS_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getBlockedByRobotsChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// canonical_added_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CANONICAL_ADDED_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getCanonicalAddedInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// canonical_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CANONICAL_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getCanonicalChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// canonical_header_flag_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CANONICAL_HEADER_FLAG_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getCanonicalHeaderFlagChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// canonical_header_type_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CANONICAL_HEADER_TYPE_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getCanonicalHeaderTypeChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// canonical_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CANONICAL_REMOVED_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getCanonicalRemovedInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// canonical_type_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CANONICAL_TYPE_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getCanonicalTypeChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// canonical_url_is_consistent_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CANONICAL_URL_IS_CONSISTENT_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getCanonicalUrlIsConsistentChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// content_type_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CONTENT_TYPE_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getContentTypeChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// custom_data_added_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CUSTOM_DATA_ADDED_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getCustomDataAddedInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// custom_data_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CUSTOM_DATA_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getCustomDataChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// custom_data_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.CUSTOM_DATA_REMOVED_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getCustomDataRemovedInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// description_added_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.DESCRIPTION_ADDED_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getDescriptionAddedInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// description_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.DESCRIPTION_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getDescriptionChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// description_length_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.DESCRIPTION_LENGTH_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getDescriptionLengthChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// description_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.DESCRIPTION_REMOVED_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getDescriptionRemovedInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// error_message_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.ERROR_MESSAGE_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getErrorMessageChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// final_response_code_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.FINAL_RESPONSE_CODE_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getFinalResponseCodeChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// follow_flg_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.FOLLOW_FLG_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getFollowFlgChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// h1_added_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.H1_ADDED_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getH1AddedInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// h1_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.H1_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getH1ChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// h1_count_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.H1_COUNT_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getH1CountChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// h1_length_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.H1_LENGTH_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getH1LengthChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// h1_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.H1_REMOVED_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getH1RemovedInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// h2_added_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.H2_ADDED_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getH2AddedInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// h2_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.H2_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getH2ChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// h2_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.H2_REMOVED_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getH2RemovedInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// header_noarchive_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HEADER_NOARCHIVE_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getHeaderNoarchiveChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// header_nofollow_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HEADER_NOFOLLOW_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getHeaderNofollowChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// header_noindex_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HEADER_NOINDEX_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getHeaderNoindexChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// header_noodp_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HEADER_NOODP_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getHeaderNoodpChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// header_nosnippet_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HEADER_NOSNIPPET_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getHeaderNosnippetChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// header_noydir_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HEADER_NOYDIR_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getHeaderNoydirChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// hreflang_errors_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HREFLANG_ERRORS_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getHreflangErrorsChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// hreflang_links_added_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HREFLANG_LINKS_ADDED_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getHreflangLinksAddedInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// hreflang_links_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HREFLANG_LINKS_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getHreflangLinksChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// hreflang_links_out_count_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HREFLANG_LINKS_OUT_COUNT_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getHreflangLinksOutCountChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// hreflang_links_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HREFLANG_LINKS_REMOVED_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getHreflangLinksRemovedInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// hreflang_url_count_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.HREFLANG_URL_COUNT_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getHreflangUrlCountChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// index_flg_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.INDEX_FLG_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getIndexFlgChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// indexable_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.INDEXABLE_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getIndexableChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// insecure_resources_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.INSECURE_RESOURCES_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getInsecureResourcesChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// meta_charset_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.META_CHARSET_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getMetaCharsetChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// meta_content_type_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.META_CONTENT_TYPE_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getMetaContentTypeChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// meta_disabled_sitelinks_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.META_DISABLED_SITELINKS_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getMetaDisabledSitelinksChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// meta_noodp_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.META_NOODP_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getMetaNoodpChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// meta_nosnippet_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.META_NOSNIPPET_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getMetaNosnippetChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// meta_noydir_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.META_NOYDIR_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getMetaNoydirChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// meta_redirect_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.META_REDIRECT_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getMetaRedirectChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// mixed_redirects_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.MIXED_REDIRECTS_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getMixedRedirectsChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// mobile_rel_alternate_url_is_consistent_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.MOBILE_REL_ALTERNATE_URL_IS_CONSISTENT_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getMobileRelAlternateUrlIsConsistentChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// noodp_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.NOODP_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getNoodpChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// nosnippet_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.NOSNIPPET_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getNosnippetChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// noydir_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.NOYDIR_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getNoydirChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// og_markup_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.OG_MARKUP_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getOgMarkupChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// og_markup_length_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.OG_MARKUP_LENGTH_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getOgMarkupLengthChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// open_graph_added_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.OPEN_GRAPH_ADDED_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getOpenGraphAddedInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// open_graph_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.OPEN_GRAPH_REMOVED_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getOpenGraphRemovedInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// outlink_count_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.OUTLINK_COUNT_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getOutlinkCountChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// page_analysis_fragments_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.PAGE_ANALYSIS_FRAGMENTS_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getPageAnalysisFragmentsChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// page_analysis_results_chg_ind_json
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.PAGE_ANALYSIS_RESULTS_CHG_IND_JSON)) {
					if (StringUtils.isNotBlank(htmlClickHouseEntity.getPageAnalysisResultsChgIndJson())) {
						indicatorValue = true;
					} else {
						indicatorValue = false;
					}
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity, indicatorValue,
							contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// page_link_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.PAGE_LINK_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getPageLinkChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// redirect_301_detected_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_301_DETECTED_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRedirect301DetectedInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// redirect_301_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_301_REMOVED_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRedirect301RemovedInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// redirect_302_detected_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_302_DETECTED_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRedirect302DetectedInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// redirect_302_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_302_REMOVED_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRedirect302RemovedInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// redirect_blocked_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_BLOCKED_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRedirectBlockedChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// redirect_blocked_reason_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_BLOCKED_REASON_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRedirectBlockedReasonChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// redirect_chain_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_CHAIN_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRedirectChainChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// redirect_diff_code_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_DIFF_CODE_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRedirectDiffCodeInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// redirect_final_url_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_FINAL_URL_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRedirectFinalUrlChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// redirect_times_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.REDIRECT_TIMES_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRedirectTimesChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// response_code_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.RESPONSE_CODE_CHG_IND)) {
					if (BooleanUtils.isTrue(htmlClickHouseEntity.getResponseCodeChgInd())) {
						isSkipUrl = checkIfSkipUrlForIndicator(contentGuardChangeTrackingEntity.getIndicator(), contentGuardSkipUrlEntityList,
								htmlClickHouseEntity.getUrl());
						if (isSkipUrl == false) {
							responseCode = htmlClickHouseEntity.getCrawlerResponse().getResponse_code();
							if (responseCodeTotalUrlsMap.containsKey(responseCode)) {
								totalUrls = responseCodeTotalUrlsMap.get(responseCode);
							} else {
								totalUrls = 0;
							}
							responseCodeTotalUrlsMap.put(responseCode, ++totalUrls);
							clonedContentGuardChangeTrackingEntity = contentGuardChangeTrackingEntity.clone();
							clonedContentGuardChangeTrackingEntity
									.setIndicator(clonedContentGuardChangeTrackingEntity.getIndicator() + IConstants.UNDERSCORE_TO_UNDERSCORE + responseCode);
							updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, clonedContentGuardChangeTrackingEntity,
									htmlClickHouseEntity.getResponseCodeChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
						}
					}
				}
				// response_headers_added_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.RESPONSE_HEADERS_ADDED_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getResponseHeadersAddedInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// response_headers_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.RESPONSE_HEADERS_REMOVED_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getResponseHeadersRemovedInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// robots_added_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.ROBOTS_ADDED_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRobotsAddedInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// robots_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.ROBOTS_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRobotsChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// robots_contents_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.ROBOTS_CONTENTS_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRobotsContentsChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// robots_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.ROBOTS_REMOVED_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getRobotsRemovedInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// structured_data_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.STRUCTURED_DATA_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getStructuredDataChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// title_added_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.TITLE_ADDED_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getTitleAddedInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// title_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.TITLE_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getTitleChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// title_length_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.TITLE_LENGTH_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getTitleLengthChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// title_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.TITLE_REMOVED_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getTitleRemovedInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// viewport_added_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.VIEWPORT_ADDED_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getViewportAddedInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// viewport_content_chg_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.VIEWPORT_CONTENT_CHG_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getViewportContentChgInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
				// viewport_removed_ind
				else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getIndicator(), IConstants.VIEWPORT_REMOVED_IND)) {
					updateIndicatorUrlsList(htmlClickHouseEntity.getUrl(), crawlTimestamp, response, contentGuardChangeTrackingEntity,
							htmlClickHouseEntity.getViewportRemovedInd(), contentGuardSkipUrlEntityList, contentGuardIndicatorUrlChangesList);
				}
			}
		}

		if (responseCodeTotalUrlsMap != null && responseCodeTotalUrlsMap.size() > 0) {
			for (String testResponseCode : responseCodeTotalUrlsMap.keySet()) {
				totalUrls = responseCodeTotalUrlsMap.get(testResponseCode);
				totalRedirectedUrls = totalRedirectedUrls + totalUrls;
				response.getChange_tracking_indicator_totals_map().put(IConstants.RESPONSE_CODE_CHG_IND + IConstants.UNDERSCORE_TO_UNDERSCORE + testResponseCode,
						totalUrls);
			}
			if (totalRedirectedUrls > 0) {
				response.getChange_tracking_indicator_totals_map().put(IConstants.RESPONSE_CODE_CHG_IND, totalRedirectedUrls);
			}
		}

		if (StringUtils.isNotBlank(filterChangeIndicator)) {
			response.setTotal_pages_changed(response.getTotal_changes());
		}

		// sort the 'contentGuardIndicatorUrlChangesList' in ascending of change indicator and URL
		Collections.sort(contentGuardIndicatorUrlChangesList, new ContentGuardIndicatorUrlChangesIndicatorAscendingComparator());

		// return the required page
		processIndicatorUrlListPagination(domainId, groupId, response, contentGuardIndicatorUrlChangesList, pageNumber, rowsPerPage, returnDetails,
				htmlClickHouseEntityList);

		if (BooleanUtils.isTrue(response.getSuccess())) {
			FormatUtils.getInstance().logMemoryUsage("processIndicatorUrlList() ends. domainId=" + domainId + ",groupId=" + groupId
					+ ",response.getIndicator_url_list().size()=" + response.getIndicator_url_list().size());
		} else {
			FormatUtils.getInstance()
					.logMemoryUsage("processIndicatorUrlList() ends. domainId=" + domainId + ",groupId=" + groupId + ",response.getSuccess()=" + response.getSuccess());
		}
	}

	// update 'response' using pass by reference
	// update 'contentGuardIndicatorUrlChangesList' using pass by reference
	private void updateIndicatorUrlsList(String urlString, String crawlTimestamp, ContentGuardResourceResponse response,
			ContentGuardChangeTrackingEntity contentGuardChangeTrackingEntity, Boolean changeTrackingIndicatorValue,
			List<ContentGuardSkipUrlEntity> contentGuardSkipUrlEntityList, List<ContentGuardIndicatorUrlChanges> contentGuardIndicatorUrlChangesList) throws Exception {

		Integer totalPages = null;
		boolean isSkipUrl = false;
		Integer severity = null;
		ContentGuardIndicatorUrlChanges contentGuardIndicatorUrlChanges = null;

		if (BooleanUtils.isTrue(changeTrackingIndicatorValue)) {

			isSkipUrl = checkIfSkipUrlForIndicator(contentGuardChangeTrackingEntity.getIndicator(), contentGuardSkipUrlEntityList, urlString);
			if (isSkipUrl == true) {
				return;
			}

			if (contentGuardChangeTrackingEntity.getCriticalFlag() != null) {
				severity = contentGuardChangeTrackingEntity.getCriticalFlag();
			} else {
				severity = 0;
			}

			if (response.getChange_severity().containsKey(severity)) {
				response.getChange_severity().put(severity, response.getChange_severity().get(severity) + 1);
			} else {
				response.getChange_severity().put(severity, 1);
			}

			response.setTotal_changes(response.getTotal_changes() + 1);
			if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getType(), IConstants.CHANGE_TYPE_ADDED)) {
				response.setTotal_changes_added(response.getTotal_changes_added() + 1);
			} else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getType(), IConstants.CHANGE_TYPE_MODIFIED)) {
				response.setTotal_changes_modified(response.getTotal_changes_modified() + 1);
			} else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getType(), IConstants.CHANGE_TYPE_REMOVED)) {
				response.setTotal_changes_removed(response.getTotal_changes_removed() + 1);
			}

			if (response.getChange_tracking_indicator_totals_map().containsKey(contentGuardChangeTrackingEntity.getIndicator())) {
				totalPages = response.getChange_tracking_indicator_totals_map().get(contentGuardChangeTrackingEntity.getIndicator()) + 1;
			} else {
				totalPages = 1;
			}
			response.getChange_tracking_indicator_totals_map().put(contentGuardChangeTrackingEntity.getIndicator(), totalPages);

			contentGuardIndicatorUrlChanges = new ContentGuardIndicatorUrlChanges();
			contentGuardIndicatorUrlChanges.setChange_indicator(contentGuardChangeTrackingEntity.getIndicator());
			contentGuardIndicatorUrlChanges.setUrl(urlString);
			contentGuardIndicatorUrlChanges.setHash_cd(CrawlerUtils.getInstance().getMd5HashCode(contentGuardIndicatorUrlChanges.getUrl()));
			contentGuardIndicatorUrlChangesList.add(contentGuardIndicatorUrlChanges);
		}
	}

	// update 'response' using pass by reference
	private void processIndicatorUrlListPagination(int domainId, Long groupId, ContentGuardResourceResponse response,
			List<ContentGuardIndicatorUrlChanges> contentGuardIndicatorUrlChangesInputList, int pageNumber, int rowsPerPage, Boolean returnDetails,
			List<HtmlClickHouseEntity> htmlClickHouseEntityList) throws Exception {
		//FormatUtils.getInstance().logMemoryUsage("processIndicatorUrlListPagination() begins. contentGuardIndicatorUrlChangesInputList.size()=" + contentGuardIndicatorUrlChangesInputList.size()
		//		+ ",pageNumber=" + pageNumber + ",rowsPerPage" + rowsPerPage + ",sortBy=" + sortBy + ",returnDetails=" + returnDetails);
		WebServiceError webServiceError = null;

		ContentGuardChangeTrackingEntity contentGuardChangeTrackingEntity = null;

		// map key = URL
		// map value = ContentGuardResourceResponse
		Map<String, ContentGuardResourceResponse> urlContentGuardResourceResponseMap = new HashMap<String, ContentGuardResourceResponse>();

		ContentGuardResourceResponse changeDetailsResponse = null;
		List<ContentGuardIndicatorUrlChanges> contentGuardIndicatorUrlChangesOutputList = new ArrayList<ContentGuardIndicatorUrlChanges>();
		int fromIndex = (pageNumber - 1) * rowsPerPage;
		int toIndex = pageNumber * rowsPerPage;
		//FormatUtils.getInstance().logMemoryUsage("processIndicatorUrlListPagination() fromIndex=" + fromIndex + ",toIndex=" + toIndex);
		if (fromIndex <= (contentGuardIndicatorUrlChangesInputList.size() - 1)) {
			if (toIndex > contentGuardIndicatorUrlChangesInputList.size()) {
				toIndex = contentGuardIndicatorUrlChangesInputList.size();
				//FormatUtils.getInstance().logMemoryUsage("processIndicatorUrlListPagination() revised toIndex=" + toIndex);
			}
			contentGuardIndicatorUrlChangesOutputList = contentGuardIndicatorUrlChangesInputList.subList(fromIndex, toIndex);

			if (BooleanUtils.isTrue(returnDetails)) {
				for (ContentGuardIndicatorUrlChanges contentGuardIndicatorUrlChanges : contentGuardIndicatorUrlChangesOutputList) {
					nextHtmlClickHouseEntity: for (HtmlClickHouseEntity htmlClickHouseEntity : htmlClickHouseEntityList) {
						if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getUrl(), htmlClickHouseEntity.getUrl())) {
							if (urlContentGuardResourceResponseMap.containsKey(htmlClickHouseEntity.getUrl())) {
								changeDetailsResponse = urlContentGuardResourceResponseMap.get(htmlClickHouseEntity.getUrl());
							} else {
								changeDetailsResponse = getChangeTrackingDetails(domainId, groupId, htmlClickHouseEntity);
								urlContentGuardResourceResponseMap.put(htmlClickHouseEntity.getUrl(), changeDetailsResponse);
							}
							contentGuardIndicatorUrlChanges.setPrevious_crawl_timestamp(changeDetailsResponse.getPrevious_crawl_timestamp());
							contentGuardIndicatorUrlChanges.setCurrent_crawl_timestamp(changeDetailsResponse.getCurrent_crawl_timestamp());
							contentGuardChangeTrackingEntity = getContentGuardChangeTrackingEntity(contentGuardIndicatorUrlChanges.getChange_indicator());
							contentGuardIndicatorUrlChanges.setChange_field(contentGuardChangeTrackingEntity.getField());
							contentGuardIndicatorUrlChanges.setChange_type(contentGuardChangeTrackingEntity.getType());
							if (contentGuardChangeTrackingEntity.getCriticalFlag() != null) {
								contentGuardIndicatorUrlChanges.setSeverity(contentGuardChangeTrackingEntity.getCriticalFlag());
							} else {
								contentGuardIndicatorUrlChanges.setSeverity(0);
							}
							updateContentGuardIndicatorUrlChanges(contentGuardIndicatorUrlChanges, changeDetailsResponse);
							break nextHtmlClickHouseEntity;
						}
					}
				}
			}

			response.setIndicator_url_list(contentGuardIndicatorUrlChangesOutputList);
			if (toIndex == contentGuardIndicatorUrlChangesInputList.size()) {
				response.setEnd_of_indicator_url_list_flag(true);
			} else {
				response.setEnd_of_indicator_url_list_flag(false);
			}

		} else {
			response.setSuccess(false);
			webServiceError = new WebServiceError();
			webServiceError.setError_code(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_OUTSIDE_DATA_RANGE);
			webServiceError.setError_message(getErrorMessage(webServiceError.getError_code(), String.valueOf(contentGuardIndicatorUrlChangesInputList.size())));
			response.setError(webServiceError);
		}
		response.setPage_number(pageNumber);
		response.setRows_per_page(rowsPerPage);
		//FormatUtils.getInstance()
		//		.logMemoryUsage("processIndicatorUrlListPagination() ends. contentGuardIndicatorUrlChangesInputList.size()=" + contentGuardIndicatorUrlChangesInputList.size() + ",pageNumber="
		//				+ pageNumber + ",rowsPerPage" + rowsPerPage + ",sortBy=" + sortBy + ",contentGuardIndicatorUrlChangesOutputList.size()="
		//				+ contentGuardIndicatorUrlChangesOutputList.size());
	}

	// update 'contentGuardIndicatorUrlChanges' using pass by reference
	private void updateContentGuardIndicatorUrlChanges(ContentGuardIndicatorUrlChanges contentGuardIndicatorUrlChanges,
			ContentGuardResourceResponse changeDetailsResponse) {

		// alternate_links
		if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.ALTERNATE_LINKS)) {
			nextContentGuardChangeDetails: for (ContentGuardChangeDetails contentGuardChangeDetails : changeDetailsResponse.getUrl_change_details_list()) {
				if (StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.ALTERNATE_LINKS)) {
					contentGuardIndicatorUrlChanges.setAlternate_links_previous_array(contentGuardChangeDetails.getAlternate_links_previous_array());
					contentGuardIndicatorUrlChanges.setAlternate_links_current_array(contentGuardChangeDetails.getAlternate_links_current_array());
					break nextContentGuardChangeDetails;
				}
			}
		}
		// custom_data
		else if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.CUSTOM_DATA)) {
			nextContentGuardChangeDetails: for (ContentGuardChangeDetails contentGuardChangeDetails : changeDetailsResponse.getUrl_change_details_list()) {
				if (StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.CUSTOM_DATA)) {
					contentGuardIndicatorUrlChanges.setCustom_data_previous_array(contentGuardChangeDetails.getCustom_data_previous_array());
					contentGuardIndicatorUrlChanges.setCustom_data_current_array(contentGuardChangeDetails.getCustom_data_current_array());
					break nextContentGuardChangeDetails;
				}
			}
		}
		// h1
		else if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.H1)) {
			nextContentGuardChangeDetails: for (ContentGuardChangeDetails contentGuardChangeDetails : changeDetailsResponse.getUrl_change_details_list()) {
				if (StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.H1)) {
					contentGuardIndicatorUrlChanges.setH1_previous_array(contentGuardChangeDetails.getH1_previous_array());
					contentGuardIndicatorUrlChanges.setH1_current_array(contentGuardChangeDetails.getH1_current_array());
					break nextContentGuardChangeDetails;
				}
			}
		}
		// h2
		else if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.H2)) {
			nextContentGuardChangeDetails: for (ContentGuardChangeDetails contentGuardChangeDetails : changeDetailsResponse.getUrl_change_details_list()) {
				if (StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.H2)) {
					contentGuardIndicatorUrlChanges.setH2_previous_array(contentGuardChangeDetails.getH2_previous_array());
					contentGuardIndicatorUrlChanges.setH2_current_array(contentGuardChangeDetails.getH2_current_array());
					break nextContentGuardChangeDetails;
				}
			}
		}
		// hreflang_errors
		else if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.HREFLANG_ERRORS)) {
			nextContentGuardChangeDetails: for (ContentGuardChangeDetails contentGuardChangeDetails : changeDetailsResponse.getUrl_change_details_list()) {
				if (StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.HREFLANG_ERRORS)) {
					contentGuardIndicatorUrlChanges.setHreflang_errors_previous(contentGuardChangeDetails.getHreflang_errors_previous());
					contentGuardIndicatorUrlChanges.setHreflang_errors_current(contentGuardChangeDetails.getHreflang_errors_current());
					break nextContentGuardChangeDetails;
				}
			}
		}
		// hreflang_links
		else if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.HREFLANG_LINKS)) {
			nextContentGuardChangeDetails: for (ContentGuardChangeDetails contentGuardChangeDetails : changeDetailsResponse.getUrl_change_details_list()) {
				if (StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.HREFLANG_LINKS)) {
					contentGuardIndicatorUrlChanges.setHreflang_links_previous_array(contentGuardChangeDetails.getHreflang_links_previous_array());
					contentGuardIndicatorUrlChanges.setHreflang_links_current_array(contentGuardChangeDetails.getHreflang_links_current_array());
					break nextContentGuardChangeDetails;
				}
			}
		}
		// insecure_resources
		else if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.INSECURE_RESOURCES)) {
			nextContentGuardChangeDetails: for (ContentGuardChangeDetails contentGuardChangeDetails : changeDetailsResponse.getUrl_change_details_list()) {
				if (StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.INSECURE_RESOURCES)) {
					contentGuardIndicatorUrlChanges.setInsecure_resources_previous_array(contentGuardChangeDetails.getInsecure_resources_previous_array());
					contentGuardIndicatorUrlChanges.setInsecure_resources_current_array(contentGuardChangeDetails.getInsecure_resources_current_array());
					break nextContentGuardChangeDetails;
				}
			}
		}
		// og_markup
		else if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.OG_MARKUP)) {
			nextContentGuardChangeDetails: for (ContentGuardChangeDetails contentGuardChangeDetails : changeDetailsResponse.getUrl_change_details_list()) {
				if (StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.OG_MARKUP)) {
					contentGuardIndicatorUrlChanges.setOg_markup_previous_array(contentGuardChangeDetails.getOg_markup_previous_array());
					contentGuardIndicatorUrlChanges.setOg_markup_current_array(contentGuardChangeDetails.getOg_markup_current_array());
					break nextContentGuardChangeDetails;
				}
			}
		}
		// page_analysis_results_chg_ind_json
		else if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.PAGE_ANALYSIS_RESULTS_CHG_IND_JSON)) {
			nextContentGuardChangeDetails: for (ContentGuardChangeDetails contentGuardChangeDetails : changeDetailsResponse.getUrl_change_details_list()) {
				if (StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.PAGE_ANALYSIS_RESULTS_CHG_IND_JSON)) {
					contentGuardIndicatorUrlChanges.setPage_analysis_result_chg_ind_array(contentGuardChangeDetails.getPage_analysis_result_chg_ind_array());
					break nextContentGuardChangeDetails;
				}
			}
		}
		// page_link
		else if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.PAGE_LINK)) {
			nextContentGuardChangeDetails: for (ContentGuardChangeDetails contentGuardChangeDetails : changeDetailsResponse.getUrl_change_details_list()) {
				if (StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.PAGE_LINK)) {
					contentGuardIndicatorUrlChanges.setPage_link_previous_array(contentGuardChangeDetails.getPage_link_previous_array());
					contentGuardIndicatorUrlChanges.setPage_link_current_array(contentGuardChangeDetails.getPage_link_current_array());
					break nextContentGuardChangeDetails;
				}
			}
		}
		// redirect_chain
		else if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.REDIRECT_CHAIN)) {
			nextContentGuardChangeDetails: for (ContentGuardChangeDetails contentGuardChangeDetails : changeDetailsResponse.getUrl_change_details_list()) {
				if (StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.REDIRECT_CHAIN)) {
					contentGuardIndicatorUrlChanges.setRedirect_chain_previous_array(contentGuardChangeDetails.getRedirect_chain_previous_array());
					contentGuardIndicatorUrlChanges.setRedirect_chain_current_array(contentGuardChangeDetails.getRedirect_chain_current_array());
					break nextContentGuardChangeDetails;
				}
			}
		}
		// response_header
		else if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.RESPONSE_HEADERS)) {
			nextContentGuardChangeDetails: for (ContentGuardChangeDetails contentGuardChangeDetails : changeDetailsResponse.getUrl_change_details_list()) {
				if (StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.RESPONSE_HEADERS)) {
					contentGuardIndicatorUrlChanges.setResponse_header_previous_array(contentGuardChangeDetails.getResponse_header_previous_array());
					contentGuardIndicatorUrlChanges.setResponse_header_current_array(contentGuardChangeDetails.getResponse_header_current_array());
					break nextContentGuardChangeDetails;
				}
			}
		}
		// structured_data
		else if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.STRUCTURED_DATA)) {
			nextContentGuardChangeDetails: for (ContentGuardChangeDetails contentGuardChangeDetails : changeDetailsResponse.getUrl_change_details_list()) {
				if (StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.STRUCTURED_DATA)) {
					contentGuardIndicatorUrlChanges.setStructured_data_previous(contentGuardChangeDetails.getStructured_data_previous());
					contentGuardIndicatorUrlChanges.setStructured_data_current(contentGuardChangeDetails.getStructured_data_current());
					break nextContentGuardChangeDetails;
				}
			}
		}
		// all other change fields
		else {
			nextContentGuardChangeDetails: for (ContentGuardChangeDetails contentGuardChangeDetails : changeDetailsResponse.getUrl_change_details_list()) {
				if (StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), contentGuardIndicatorUrlChanges.getChange_field())) {
					if (StringUtils.isNotBlank(contentGuardChangeDetails.getPrevious_content())) {
						contentGuardIndicatorUrlChanges.setPrevious_content(contentGuardChangeDetails.getPrevious_content());
					} else {
						contentGuardIndicatorUrlChanges.setPrevious_content(IConstants.EMPTY_STRING);
					}
					if (StringUtils.isNotBlank(contentGuardChangeDetails.getCurrent_content())) {
						contentGuardIndicatorUrlChanges.setCurrent_content(contentGuardChangeDetails.getCurrent_content());
					} else {
						contentGuardIndicatorUrlChanges.setCurrent_content(IConstants.EMPTY_STRING);
					}
					break nextContentGuardChangeDetails;
				}
			}
		}

	}

	public Integer getCrawlFrequencyType(int domainId, Long groupId) {
		Integer crawlFrequencyType = null;
		ContentGuardGroupEntity contentGuardGroupEntity = contentGuardGroupDAO.get(domainId, groupId);
		if (contentGuardGroupEntity != null) {
			crawlFrequencyType = contentGuardGroupEntity.getCrawlFrequencyType();
		}
		return crawlFrequencyType;
	}

	public void sendZapierContentGuardAlert(String ip, int domainId, Long groupId, List<ZapierContentGuardAlert> zapierContentGuardAlertList) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("sendZapierContentGuardAlert() begins. ip=" + ip + ",domainId=" + domainId + ",groupId=" + groupId
				+ ",zapierContentGuardAlertList.size()=" + zapierContentGuardAlertList.size());
		String callbackUrl = null;
		String requestParameters = null;
		Gson gson = new Gson();
		String responseString = null;
		boolean isSendGetRequest = false;
		Long groupIdToBeTested = null;
		List<ZapierWebhookEntity> zapierWebhookEntityList = zapierWebhookDAO.getByDomainIdTriggerType(domainId, IConstants.ZAPIER_TRIGGER_TYPE_CONTENT_GUARD_ALERT);
		if (zapierWebhookEntityList != null && zapierWebhookEntityList.size() > 0) {
			nextZapierWebhookEntity: for (ZapierWebhookEntity zapierWebhookEntity : zapierWebhookEntityList) {

				// when the user is no longer active in the domain
				if (isUserActiveInDomain(zapierWebhookEntity.getUserId(), zapierWebhookEntity.getDomainId()) == false) {
					FormatUtils.getInstance().logMemoryUsage("sendZapierContentGuardAlert() skipped, user ID no longer active. ip=" + ip + ",domainId=" + domainId
							+ ",userId=" + zapierWebhookEntity.getUserId());
					continue nextZapierWebhookEntity;
				}

				// the 'sub_type' of the 'zapier_webhook' MySQL record is the group ID
				groupIdToBeTested = new Long(zapierWebhookEntity.getSubType());

				// when the content guard group requires Zapier trigger webhook
				if (groupId.compareTo(groupIdToBeTested) == 0) {
					callbackUrl = zapierWebhookEntity.getCallbackUrl();
					FormatUtils.getInstance()
							.logMemoryUsage("sendZapierContentGuardAlert() ip=" + ip + ",domainId=" + domainId + ",groupId=" + groupId + ",callbackUrl=" + callbackUrl);
					for (ZapierContentGuardAlert zapierContentGuardAlert : zapierContentGuardAlertList) {
						requestParameters = gson.toJson(zapierContentGuardAlert, ZapierContentGuardAlert.class);
						//if (isDebug == true) {
						//	System.out.println(
						//			"sendZapierContentGuardAlert() ip=" + ip + ",domainId=" + domainId + ",groupId=" + groupId + ",requestParameters=" + requestParameters);
						//}
						try {
							responseString = HttpUtils.getInstance().getResponseString(callbackUrl, isSendGetRequest, requestParameters);
							//if (isDebug == true) {
							//	System.out
							//			.println("sendZapierContentGuardAlert() ip=" + ip + ",domainId=" + domainId + ",groupId=" + groupId + ",responseString=" + responseString);
							//}
						} catch (Exception e) {
							// re-throw exception when not 'httpStatusCode=410,httpReasonPhrase=Gone'
							if (StringUtils.containsIgnoreCase(e.getMessage(), "httpStatusCode=410,httpReasonPhrase=Gone") == false) {
								throw e;
							}
						}
					}
				}
			}
		} else {
			FormatUtils.getInstance().logMemoryUsage("sendZapierContentGuardAlert() begins. ip=" + ip + ",domainId=" + domainId + ",zapierWebhookEntityList is empty.");
		}
		FormatUtils.getInstance().logMemoryUsage("sendZapierContentGuardAlert() ends. ip=" + ip + ",domainId=" + domainId + ",groupId=" + groupId
				+ ",zapierContentGuardAlertList.size()=" + zapierContentGuardAlertList.size());
	}

	public boolean isUserActiveInDomain(int userId, int domainId) {
		boolean output = false;
		UserEntity userEntity = userDAO.getUser(userId, domainId);
		if (userEntity != null) {
			output = true;
		}
		return output;
	}

	public void sortContentGuardTrackedPageList(List<ContentGuardTrackedPage> contentGuardTrackedPageList, int sortBy) {

		// 1 = sort URLs in ascending order
		if (sortBy == IConstants.SORT_BY_URL_ASC) {
			Collections.sort(contentGuardTrackedPageList, new ContentGuardTrackedPageUrlAscendingComparator());
		}
		// 2 = sort URLs in descending order
		else if (sortBy == IConstants.SORT_BY_URL_DESC) {
			Collections.sort(contentGuardTrackedPageList, new ContentGuardTrackedPageUrlDescendingComparator());
		}
		// 3 = sort last update timestamp in ascending order
		else if (sortBy == IConstants.SORT_BY_CRAWL_TIMESTAMP_ASC) {
			Collections.sort(contentGuardTrackedPageList, new ContentGuardTrackedPageLastUpdateTimestampAscendingComparator());
		}
		// 4 = sort last update timestamp in descending order
		else if (sortBy == IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC) {
			Collections.sort(contentGuardTrackedPageList, new ContentGuardTrackedPageLastUpdateTimestampDescendingComparator());
		}
		// 13 = sort response code in ascending order
		else if (sortBy == IConstants.SORT_BY_RESPONSE_CODE_ASC) {
			Collections.sort(contentGuardTrackedPageList, new ContentGuardTrackedPageResponseCodeAscendingComparator());
		}
		// 14 = sort response code in descending order
		else if (sortBy == IConstants.SORT_BY_RESPONSE_CODE_DESC) {
			Collections.sort(contentGuardTrackedPageList, new ContentGuardTrackedPageResponseCodeDescendingComparator());
		}
	}

	// update 'response' using pass by reference
	public List<ContentGuardTrackedPage> paginateContentGuardTrackedPageList(int domainId, Long groupId, ContentGuardResourceResponse response,
			List<ContentGuardTrackedPage> contentGuardTrackedPageInputList, int pageNumber, int rowsPerPage, int sortBy) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("paginateContentGuardTrackedPageList() begins. contentGuardTrackedPageInputList.size()="
				+ contentGuardTrackedPageInputList.size() + ",pageNumber=" + pageNumber + ",rowsPerPage" + rowsPerPage + ",sortBy=" + sortBy);
		WebServiceError webServiceError = null;
		List<ContentGuardTrackedPage> contentGuardTrackedPageOutputList = new ArrayList<ContentGuardTrackedPage>();
		int fromIndex = (pageNumber - 1) * rowsPerPage;
		int toIndex = pageNumber * rowsPerPage;
		//FormatUtils.getInstance().logMemoryUsage("paginateContentGuardTrackedPageList() fromIndex=" + fromIndex + ",toIndex=" + toIndex);
		if (fromIndex <= (contentGuardTrackedPageInputList.size() - 1)) {
			if (toIndex > contentGuardTrackedPageInputList.size()) {
				toIndex = contentGuardTrackedPageInputList.size();
				//FormatUtils.getInstance().logMemoryUsage("paginateContentGuardTrackedPageList() revised toIndex=" + toIndex);
			}
			contentGuardTrackedPageOutputList = contentGuardTrackedPageInputList.subList(fromIndex, toIndex);
			if (toIndex == contentGuardTrackedPageInputList.size()) {
				response.setEnd_of_tracked_pages_flag(true);
			} else {
				response.setEnd_of_tracked_pages_flag(false);
			}

		} else {
			response.setSuccess(false);
			webServiceError = new WebServiceError();
			webServiceError.setError_code(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_OUTSIDE_DATA_RANGE);
			webServiceError.setError_message(getErrorMessage(webServiceError.getError_code(), String.valueOf(contentGuardTrackedPageInputList.size())));
			response.setError(webServiceError);
		}
		response.setPage_number(pageNumber);
		response.setRows_per_page(rowsPerPage);
		response.setSort_by(sortBy);
		FormatUtils.getInstance()
				.logMemoryUsage("paginateContentGuardTrackedPageList() ends. contentGuardTrackedPageInputList.size()=" + contentGuardTrackedPageInputList.size()
						+ ",pageNumber=" + pageNumber + ",rowsPerPage" + rowsPerPage + ",sortBy=" + sortBy + ",contentGuardTrackedPageOutputList.size()="
						+ contentGuardTrackedPageOutputList.size());
		return contentGuardTrackedPageOutputList;
	}
}
