package com.actonia.utils;

import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

import org.apache.jcs.JCS;
import org.apache.jcs.access.exception.CacheException;

public class JCSManager {
	private static final String PROC_CACHE = "procCache";
	// private static final String PROC_CACHE1 = "procCache1";
	// private static final String PROC_CACHE2 = "procCache2";
	// private static final String PROC_CACHE3 = "procCache3";
	// private static final String PROC_CACHE4 = "procCache4";
	// private static final String PROC_CACHE5 = "procCache5";
	// private static final String PROC_CACHE6 = "procCache6";

	private static JCS defaultRe = null;
	// private static JCS procCache1 = null;
	// private static JCS procCache2 = null;
	// private static JCS procCache3 = null;
	// private static JCS procCache4 = null;
	// private static JCS procCache5 = null;
	// private static JCS procCache6 = null;

	public static JCSManager jcsManager = null;

	public static String Md5(String plainText) {
		String result = "";
		try {
			MessageDigest md = MessageDigest.getInstance("MD5");
			md.update(plainText.getBytes());
			byte b[] = md.digest();
			int i;
			StringBuffer buf = new StringBuffer("");
			for (int offset = 0; offset < b.length; offset++) {
				i = b[offset];
				if (i < 0)
					i += 256;
				if (i < 16)
					buf.append("0");
				buf.append(Integer.toHexString(i));
			}
			result = buf.toString();
		} catch (NoSuchAlgorithmException e) {
			e.printStackTrace();
		}
		return result;
	}

	public static BigInteger Md5Int(String md5) {
		BigInteger md5Int = new BigInteger("0");
		try {
			String subDigest = md5.substring(0, 14);
			md5Int = new BigInteger(subDigest, 16);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return md5Int;
	}

	private JCSManager() {
		try {
			defaultRe = JCS.getInstance(PROC_CACHE);
			// procCache1 = JCS.getInstance(PROC_CACHE1);
			// procCache2 = JCS.getInstance(PROC_CACHE2);
			// procCache3 = JCS.getInstance(PROC_CACHE3);
			// procCache4 = JCS.getInstance(PROC_CACHE4);
			// procCache5 = JCS.getInstance(PROC_CACHE5);
			// procCache6 = JCS.getInstance(PROC_CACHE6);
		} catch (CacheException e) {
			e.printStackTrace();
		}
	}

	public static synchronized JCSManager getInstance() {
		if (jcsManager == null) {
			jcsManager = new JCSManager();
		}
		return jcsManager;
	}

	public void addData(String key, Object value) {
		try {
			defaultRe.put(Md5(key), value);
		} catch (CacheException e) {
			e.printStackTrace();
		}
	}

	// public void addDataByRange(String key, Object value, int rangeId) {
	// try {
	// if (rangeId == 1) {
	// procCache1.put(key, value);
	// } else if (rangeId == 2) {
	// procCache2.put(key, value);
	// } else if (rangeId == 3) {
	// procCache3.put(key, value);
	// } else if (rangeId == 4) {
	// procCache4.put(key, value);
	// } else if (rangeId == 5) {
	// procCache5.put(key, value);
	// } else if (rangeId == 6) {
	// procCache6.put(key, value);
	// }
	// } catch (CacheException e) {
	// e.printStackTrace();
	// }
	// }

	public Object getData(String key) {
		return defaultRe.get(Md5(key));
	}
}
