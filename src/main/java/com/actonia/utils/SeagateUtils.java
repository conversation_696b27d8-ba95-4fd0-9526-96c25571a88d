package com.actonia.utils;

import com.amazonaws.HttpMethod;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.transfer.TransferManager;
import com.amazonaws.services.s3.transfer.TransferManagerBuilder;
import com.amazonaws.services.s3.transfer.Upload;

import java.io.File;
import java.net.URL;
import java.time.Instant;

public class SeagateUtils { // https://www.wrike.com/open.htm?id=**********

	public static final String SEAGATE_DEFAULT_BUCKET_NAME = "temporary-files-central";
	public static final String SIGNATURE_HOSTNAME = "https://cloudv2.seoclarity.net"; // TODO
	
    public static final String SEAGATE_SERVICE_ENDPOINT = "https://s3.clarity1.lyve.seagate.com";
    public static final String SEAGATE_SIGNING_REGION_US_EAST_1 = "us-central-2"; // "seagate"
    
    public static final long DEFAULT_SIGNATURE_DURATION_MILLI_SECONDS = 1000 * 60 * 60 * 24 * 7; // 7 days = 604800 seconds
    public static final int SIGNATURE_DEFAULT_RETRY_COUNT = 3;

    private static final AWSCredentials credentials = AwsCredentialsEnvKeyConstructor.getInstance().getAwsCredentials(AwsCredentialsEnvKeyConstructor.ENV_SEAGATE_TEMP_FILE_ACCESS_KEY_S3,
		    AwsCredentialsEnvKeyConstructor.ENV_SEAGATE_TEMP_FILE_SECRET_KEY_S3);

	public static void main(String[] args) throws Exception {
    	saveFileToDefaultSeagate(6060, "D:\\Doc\\RetrieveSv_testBingUK_0303_6060_2023-03-02.tsv");
    	getDefaultSeagatePresignedUrl(6060, "RetrieveSv_testBingUK_0303_6060_2023-03-02.tsv");
    }
    
    public static boolean saveFileToDefaultSeagate(int ownDomainId, String fullPathFileName) {
    	return saveFileToDefaultSeagate(SEAGATE_DEFAULT_BUCKET_NAME, ownDomainId, fullPathFileName);	
    }
    
    public static boolean saveFileToDefaultSeagate(String bucketName, int ownDomainId, String fullPathFileName) {
    	return saveFileToSeagate(SEAGATE_SERVICE_ENDPOINT, SEAGATE_SIGNING_REGION_US_EAST_1, bucketName, ownDomainId, fullPathFileName, SIGNATURE_DEFAULT_RETRY_COUNT);	
    }
    
    public static String getDefaultSeagatePresignedUrl(int ownDomainId, String fileName) throws Exception {
    	return getDefaultSeagatePresignedUrl(SEAGATE_DEFAULT_BUCKET_NAME, ownDomainId, fileName);
    }
    
    public static String getDefaultSeagatePresignedUrl(int ownDomainId, String fileName, long durationSeconds) throws Exception {
    	return getSeagatePresignedUrl(SIGNATURE_HOSTNAME, SEAGATE_SIGNING_REGION_US_EAST_1, SEAGATE_DEFAULT_BUCKET_NAME, ownDomainId, fileName, durationSeconds, 
    		SIGNATURE_DEFAULT_RETRY_COUNT);
    }
    
    public static String getDefaultSeagatePresignedUrl(String bucketName, int ownDomainId, String fileName) throws Exception {
    	return getSeagatePresignedUrl(SIGNATURE_HOSTNAME, SEAGATE_SIGNING_REGION_US_EAST_1, bucketName, ownDomainId, fileName, 
        	DEFAULT_SIGNATURE_DURATION_MILLI_SECONDS, SIGNATURE_DEFAULT_RETRY_COUNT);
    }
    
    public static boolean saveFileToSeagate(String serviceEndpoint, String signingRegion, String bucketName, int ownDomainId, String fullPathFileName, int retryCount) {
		File tempFile = new File(fullPathFileName);
		String fileName = tempFile.getName();
		String objectKey = ownDomainId + "/" + fileName; // TODO
		for (int triedCount = 0; triedCount < retryCount; triedCount++) {
			try {
				AmazonS3 s3client = AmazonS3ClientBuilder.standard()
      				.withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(serviceEndpoint, signingRegion))
      				.withCredentials(new AWSStaticCredentialsProvider(credentials)).build();
				TransferManager transferManager = TransferManagerBuilder.standard().withS3Client(s3client).build();
				Upload uploader = transferManager.upload(new PutObjectRequest(bucketName, objectKey, tempFile));
				// loop with Transfer.isDone()
				XferMgrProgress.showTransferProgress(uploader);
				//  or block with Transfer.waitForCompletion()
				XferMgrProgress.waitForCompletion(uploader);
				transferManager.shutdownNow();
				System.out.println(" ==SavedFileToSeagate bucket:" + bucketName + " objKey:" + objectKey + " file:" + fullPathFileName);
				return true;
			} catch (Exception e) {
				e.printStackTrace();
				System.out.println(" ##FailedToSaveFileToSeagate retryCnt:" + triedCount);
				try {
					Thread.sleep((triedCount + 1) * 60 * 1000);
				} catch (Exception exp) {
					exp.printStackTrace();
				}
			}
		}
		return false;
	}
    
    // Sample parameters: signatureHostName:"https://cloud.seoclarity.net", signingRegion:"us-east-1", bucketName:SEAGATE_DEFAULT_BUCKET_NAME, fileName:"test.txt"
    public static String getSeagatePresignedUrl(String signatureHostName, String signingRegion, String bucketName, int ownDomainId, String fileName, long durationSeconds,
    		int retryCount) throws Exception {
    	int triedCount = 0;
    	while (true) {
	        try {
	            AmazonS3 s3Client = AmazonS3ClientBuilder.standard()
	                .withCredentials(new AWSStaticCredentialsProvider(credentials))
	                .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(SEAGATE_SERVICE_ENDPOINT, signingRegion)).build();
	            java.util.Date expiration = new java.util.Date();
	            long expTimeMillis = Instant.now().toEpochMilli();
	            expiration.setTime(expTimeMillis + durationSeconds);
	            String key = bucketName + "/" + ownDomainId + "/" + fileName; // TODO
	            System.out.println(" ==CreatingPresignedURL bucket:" + bucketName + " OID:" + ownDomainId + " file:" + fileName + "->objKey:" + key);
	            GeneratePresignedUrlRequest generatePresignedUrlRequest = new GeneratePresignedUrlRequest("", key).withMethod(HttpMethod.GET).withExpiration(expiration);
	            URL url = s3Client.generatePresignedUrl(generatePresignedUrlRequest);
	            System.out.println(" ==CreatedPreSignedURL:" + url.toString());
	            return url.toString().replace(SEAGATE_SERVICE_ENDPOINT, signatureHostName);
	        } catch (Exception e) {
	            e.printStackTrace();
	            System.out.println(" ##FailedToGetPresignedUrl retryCnt:" + ++triedCount);
	            if (triedCount > retryCount) {
	            	throw e;
	            }
	            try {
					Thread.sleep((triedCount + 1) * 60 * 1000);
				} catch (Exception exp) {
					exp.printStackTrace();
				}
	        } 
		}
    }
}
