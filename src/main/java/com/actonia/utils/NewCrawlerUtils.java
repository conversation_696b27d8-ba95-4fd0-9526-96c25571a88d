package com.actonia.utils;

import com.actonia.IConstants;
import com.actonia.entity.AdditionalContentEntity;
import com.actonia.value.object.CustomDataRequest;
import com.actonia.value.object.JavascriptSettings;
import com.actonia.value.object.ScrapyCrawlerRequest;
import com.actonia.value.object.SiteclaritySettings;
import com.google.gson.Gson;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;

import java.net.URL;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Log4j2
public class NewCrawlerUtils {

    private static final String CRAWLER_API_ENDPOINT;
    private static final String CRAWLER_API_LONDON_ENDPOINT;
    private static final String CRAWLER_API_ACCESS_KEY;
    private static final String today = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
    private static final Gson gson = new Gson();

    static {
        CRAWLER_API_ENDPOINT = PoliteCrawlConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_SCRAPY_CRAWLER_ENDPOINTS);
        CRAWLER_API_LONDON_ENDPOINT = PoliteCrawlConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_SCRAPY_CRAWLER_LONDON_ENDPOINTS);
        CRAWLER_API_ACCESS_KEY = PoliteCrawlConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_SCRAPY_CRAWLER_ACCESS_KEY);
    }

    public String getScrapyCrawlerRequest(int domainId, String encodedUrlString, String userAgent, List<AdditionalContentEntity> additionalContentEntityList,
                                          boolean isJavascriptCrawler, Integer javascriptTimeoutInSecond, Map<String, String> pageCrawlerApiRequestHeaders, boolean isStoreHtml, String s3Location, String region) {
        try {
            URL url = new URL(encodedUrlString);
            String host = url.getHost();
            if (StringUtils.endsWithIgnoreCase(host, IConstants.GRAINGER_COM)) {
                if (StringUtils.containsIgnoreCase(encodedUrlString, IConstants.SLASH_MOBILE_SLASH)
                        || StringUtils.endsWithIgnoreCase(host, IConstants.M_GRAINGER_COM)) {
                    userAgent = IConstants.WWW_GRAINGER_COM_MOBILE_USER_AGENT;
                }
            } else {
                if (StringUtils.isNotBlank(host) && StringUtils.startsWithIgnoreCase(host, IConstants.MOBILE_SITE_HOSTNAME_PREFIX)) {
                    userAgent = CrawlerUtils.getScrapyMobileUserAgent();
                }
            }
        } catch (Exception e) {
            // TODO: handle exception
        }

        // construct a scrapy request object
        ScrapyCrawlerRequest scrapyCrawlerRequest = new ScrapyCrawlerRequest();
        final ScrapyCrawlerRequest.ScrapySettings scrapySettings = new ScrapyCrawlerRequest.ScrapySettings();
        scrapySettings.setUserAgent(userAgent);

        if (pageCrawlerApiRequestHeaders != null && !pageCrawlerApiRequestHeaders.isEmpty()) {
            scrapySettings.setDefaultRequestHeaders(pageCrawlerApiRequestHeaders);
        }
        scrapyCrawlerRequest.setUrl(Collections.singletonList(encodedUrlString));
        scrapyCrawlerRequest.setResponse_as_html(false);
        scrapyCrawlerRequest.setHtml_in_json(false);

        SiteclaritySettings siteclarity_settings = new SiteclaritySettings();
        siteclarity_settings.setValidate_structured_schema(true);
        if (isJavascriptCrawler) {
            siteclarity_settings.setJavascript_enabled(true);
            JavascriptSettings javascript_settings = new JavascriptSettings();
            // https://www.wrike.com/open.htm?id=779910062
            if (javascriptTimeoutInSecond != null && javascriptTimeoutInSecond > 0) {
                javascript_settings.setPage_load_timeout(new Float(javascriptTimeoutInSecond));
            } else {
                javascript_settings.setPage_load_timeout(10F);
            }
            javascript_settings.setWait_after_last_request(2F);
            if (pageCrawlerApiRequestHeaders != null && !pageCrawlerApiRequestHeaders.isEmpty()) {
                javascript_settings.setHeaders(pageCrawlerApiRequestHeaders);
            }
            if (StringUtils.equalsIgnoreCase(region, IConstants.LONDON)) {
                scrapySettings.setSplashUrl("http://**************:3000");
                javascript_settings.setAudit_resources(false);
            } else {
                scrapySettings.setSplashUrl("http://***************:3000");
                javascript_settings.setAudit_resources(false);
            }

            // https://www.wrike.com/open.htm?id=976316111 ends
            siteclarity_settings.setJavascript_settings(javascript_settings);
        }

        if (additionalContentEntityList != null && !additionalContentEntityList.isEmpty()) {
            List<CustomDataRequest> customDataRequestList = CrawlerUtils.getCustomDataRequestList(encodedUrlString, additionalContentEntityList);
            if (customDataRequestList != null && !customDataRequestList.isEmpty()) {
                CustomDataRequest[] custom_data_requests = customDataRequestList.toArray(new CustomDataRequest[0]);
                siteclarity_settings.setCustom_data_requests(custom_data_requests);
            }
        }
        siteclarity_settings.setExtractStructuredData(true);
        siteclarity_settings.setIncludeStructuredDataValidationErrors(true);
        siteclarity_settings.setIncludeStructuredDataValidationData(false);

        if (isStoreHtml) {
            siteclarity_settings.setStore_html(true);
            scrapyCrawlerRequest.setDomainId(String.valueOf(domainId));
        }

        // set disable_page_size_check to true for OID 4765 to avoid response code 9xx
        // # https://www.wrike.com/open.htm?id=1533948954
        if (domainId == 4765) {
            siteclarity_settings.setDisable_page_size_check(true);
        }
        scrapyCrawlerRequest.setSiteclarity_settings(siteclarity_settings);
        scrapyCrawlerRequest.setScrapySettings(scrapySettings);
        // stringify the scrapy request object
        return gson.toJson(scrapyCrawlerRequest, ScrapyCrawlerRequest.class);
    }
}
