package com.actonia.utils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.script.Invocable;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.entity.ElementMappingPatternEntity;

public class CommonUtils {

	private static EmailSenderComponent emailSenderComponent;
	
	static {
		emailSenderComponent = SpringBeanFactory.getBean("emailSenderComponent");
	}

	public static Set<Integer> getDomainSet(String domainIds) {
		Set<Integer> returnDomain = new HashSet<Integer>();
		String[] dIds = domainIds.split(",");

		if (dIds != null && dIds.length > 0) {
			for (int i = 0; i < dIds.length; i++) {
				returnDomain.add(Integer.parseInt(dIds[i]));
			}
		}
		return returnDomain;
	}

	public static void initThreads(int threadCount) {
		Map<String, String> ipMap = new HashMap<String, String>();
		for (int i = 1; i <= threadCount; i++) {
			ipMap.put(i + "", IConstants.IP_STATUS_ALIVE);
		}
		CacheModleFactory.getInstance().setIpMap(ipMap);
	}

	public static String ignoreStrip(List<ElementMappingPatternEntity> mappings, String url) {
		if (mappings == null || mappings.size() == 0) {
			return url;
		}
		if (StringUtils.isBlank(url)) {
			return null;
		}

		String duplicatedString = null;
		String correctedString = null;

		List<String> matchPattern = new ArrayList<String>();
		try {
			for (ElementMappingPatternEntity elementMappingPatternEntity : mappings) {
				if (elementMappingPatternEntity.getProcessType() == ElementMappingPatternEntity.PROCESS_TYPE_IGNORE) {
					if (elementMappingPatternEntity.getRegularExpression() == ElementMappingPatternEntity.REGULAR_EXP_STRING) {
						if (elementMappingPatternEntity.getCaseSensitive() == ElementMappingPatternEntity.CASE_SENSITIVE_YES) {
							if (StringUtils.contains(url, elementMappingPatternEntity.getPatternStr())) {
								//System.out.println("Warnning: Ignore! Match: "
								//		+ elementMappingPatternEntity.getPatternStr() + " , URL: " + url);
								return null;
							}
						} else {
							if (StringUtils.containsIgnoreCase(url, elementMappingPatternEntity.getPatternStr())) {
								//System.out.println("Warnning: Ignore! Match: "
								//		+ elementMappingPatternEntity.getPatternStr() + " , URL: " + url);
								return null;
							}
						}
					} else if (elementMappingPatternEntity.getRegularExpression() == ElementMappingPatternEntity.REGULAR_EXP_JAVA_REGEX) {
						Pattern pattern = Pattern.compile(elementMappingPatternEntity.getPatternStr());
						Matcher matcher = pattern.matcher(url);
						if (matcher.find()) {
							//System.out.println("Warnning: Ignore! Match: "
							//		+ elementMappingPatternEntity.getPatternStr() + " , URL: " + url);
							return null;
						}
					} else if (elementMappingPatternEntity.getRegularExpression() == ElementMappingPatternEntity.REGULAR_EXP_JS_REGEX) {
						if (checkExistRegexp(elementMappingPatternEntity, url)) {
							//System.out.println("Warnning: Ignore! Match: "
							//		+ elementMappingPatternEntity.getPatternStr() + " , URL: " + url);
							return null;
						}
					}
				} else if (elementMappingPatternEntity.getProcessType() == ElementMappingPatternEntity.PROCESS_TYPE_STRIP) {
					if (elementMappingPatternEntity.getRegularExpression() == ElementMappingPatternEntity.REGULAR_EXP_STRING) {
						if (StringUtils.containsIgnoreCase(url, elementMappingPatternEntity.getPatternStr())) {
							String returnUrl = StringUtils.substringBefore(url, elementMappingPatternEntity.getPatternStr());
							//System.out.println("Warnning: STRIP! Match: " + elementMappingPatternEntity.getPatternStr()
							//		+ " ,Old Url: " + url + " ,New Url: " + returnUrl);
							return returnUrl;
						}
					} else if (elementMappingPatternEntity.getRegularExpression() == ElementMappingPatternEntity.REGULAR_EXP_JAVA_REGEX) {
						Pattern pattern = Pattern.compile(elementMappingPatternEntity.getPatternStr());
						Matcher matcher = pattern.matcher(url);

						if (matcher.find()) {
							String matcherStr = matcher.group();
							if (StringUtils.containsIgnoreCase(url, matcherStr)) {
								String returnUrl = StringUtils.substringBefore(url, matcherStr);
								//System.out.println("Warnning: STRIP! Match: " + matcherStr + " ,Old Url: " + url
								//		+ " ,New Url: " + returnUrl);
								return returnUrl;
							}
						}
					} else if (elementMappingPatternEntity.getRegularExpression() == ElementMappingPatternEntity.REGULAR_EXP_JS_REGEX) {
						// TODO
						// temporary solution for removing duplicated string in
						// domain 868's hostname
						duplicatedString = elementMappingPatternEntity.getPatternStr() + elementMappingPatternEntity.getPatternStr();
						correctedString = elementMappingPatternEntity.getPatternStr();
						String returnUrl = StringUtils.replace(url, duplicatedString, correctedString);
						return returnUrl;
					}
				} else if (elementMappingPatternEntity.getProcessType() == ElementMappingPatternEntity.PROCESS_TYPE_MATCH) {
					// if (elementMappingPatternEntity.getRegularExpression() ==
					// ElementMappingPatternEntity.REGULAR_EXP_STRING) {
					// if (StringUtils.containsIgnoreCase(url,
					// elementMappingPatternEntity.getPatternStr())) {
					// return url;
					// } else {
					// return null;
					// }
					// } else {
					// return null;
					// }
					matchPattern.add(elementMappingPatternEntity.getPatternStr());
				}
			}

			if (matchPattern.size() > 0) {
				boolean matched = false;
				for (String pattern : matchPattern) {
					if (StringUtils.containsIgnoreCase(url, pattern)) {
						matched = true;
						break;
					}
				}

				if (matched) {
					return url;
				} else {
					return null;
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return url;
	}

	public static boolean checkExistRegexp(ElementMappingPatternEntity elementMappingPatternEntity, String url) {
		ScriptEngineManager scriptEngineManager = new ScriptEngineManager();
		ScriptEngine engine = scriptEngineManager.getEngineByExtension("js");
		try {
			if (elementMappingPatternEntity.isCaseSensitiveYes()) {
				engine.eval("function exec_reg(reg,str){ var pattern = new RegExp(reg); return pattern.test(str); }");
			} else {
				engine.eval("function exec_reg(reg,str){ var pattern = new RegExp(reg,'i'); return pattern.test(str); }");
			}

			Invocable invoke = (Invocable) engine;

			boolean ifMatch = (Boolean) invoke.invokeFunction("exec_reg", elementMappingPatternEntity.getPatternStr(), url);
			return ifMatch;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
	}

	public static Integer calculateWeekOfYear(Integer inputDateNumber) {
		Integer response = null;
		try {
			Date inputDate = DateUtils.parseDate(String.valueOf(inputDateNumber), new String[] { IConstants.DATE_FORMAT_YYYYMMDD });
			response = calculateWeekOfYear(inputDate);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return response;
	}

	public static Integer calculateWeekOfYear(Date inputDate) {
		Integer response = null;
		try {
			SimpleDateFormat simpleDateFormat = new SimpleDateFormat(IConstants.DATE_FORMAT_YYYYWW);
			String weekInYearString = simpleDateFormat.format(DateUtils.truncate(inputDate, Calendar.DAY_OF_MONTH));
			if (weekInYearString != null) {
				response = new Integer(weekInYearString);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return response;
	}

	public static String formatElapsedTime(long seconds) {
		long day = TimeUnit.SECONDS.toDays(seconds);
		long hours = TimeUnit.SECONDS.toHours(seconds) - TimeUnit.DAYS.toHours(day);
		long minute = TimeUnit.SECONDS.toMinutes(seconds) - TimeUnit.HOURS.toMinutes(TimeUnit.SECONDS.toHours(seconds));
		long second = TimeUnit.SECONDS.toSeconds(seconds) - TimeUnit.MINUTES.toSeconds(TimeUnit.SECONDS.toMinutes(seconds));
		String formattedElapsedTime = day + "d" + hours + "h" + minute + "m" + second + "s";
		return formattedElapsedTime;
	}

	public static String getDomainByUrl(String url) {
		String result = url;

		// remove 'http://'
		if (StringUtils.contains(result, "://")) {
			result = StringUtils.substringAfter(result, "://");
		}

		// remove the string after domain
		result = StringUtils.substringBefore(result, "/");

		// remove 'www.' if the domain string contains it
		if (StringUtils.isNotBlank(result) && result.startsWith("www.")) {
			result = StringUtils.substringAfter(result, "www.");
		}

		return result;
	}

	public static void sendCausalImpactAlert(String message, String requestParameters) {
		FormatUtils.getInstance().logMemoryUsage("sendCausalImpactAlert() begins.");
		String[] assignedList = new String[1];
		String subject = "Causal impact web service error as of " + DateFormatUtils.format(new Date(), IConstants.DATE_FORMAT_YYYY_MM_DD_HHMMSS);
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("message", message);
		map.put("requestParameters", requestParameters);
		assignedList[0] = "<EMAIL>";
		emailSenderComponent.sendMimeMultiPartZeptoMail("<EMAIL>", assignedList, subject, "mail_web_service_error.txt", "mail_web_service_error.html", map);
		FormatUtils.getInstance().logMemoryUsage("sendCausalImpactAlert() ends.");
	}

	public static void sendRUtilsAlert(String message, String requestParameters) {
		FormatUtils.getInstance().logMemoryUsage("sendRUtilsAlert() begins.");
		String[] assignedList = new String[1];
		String subject = "RUtils error as of " + DateFormatUtils.format(new Date(), IConstants.DATE_FORMAT_YYYY_MM_DD_HHMMSS);
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("message", message);
		map.put("requestParameters", requestParameters);
		assignedList[0] = "<EMAIL>";
		emailSenderComponent.sendMimeMultiPartZeptoMail("<EMAIL>", assignedList, subject, "mail_web_service_error.txt", "mail_web_service_error.html", map);
		FormatUtils.getInstance().logMemoryUsage("sendRUtilsAlert() ends.");
	}
}
