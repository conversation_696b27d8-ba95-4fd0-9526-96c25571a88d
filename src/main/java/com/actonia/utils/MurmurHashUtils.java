package com.actonia.utils;

public class MurmurHashUtils {

    private static final int DEFAULT_OFFSET = 0;
    private static final int DEFAULT_SEED = 0;

    public static String getMurmurHash2_64(String str) {
        long trueCode = MurmurHash2.hash64(str);
        return Long.toUnsignedString(trueCode, 10);
    }

    public static String getMurmurHash2_32(String str) {
        int trueCode = MurmurHash2.hash32(str);
        return Integer.toUnsignedString(trueCode, 10);
    }

    public static String getMurmurHash3_32(String symbol) {
        byte[] bytes = symbol.getBytes();
        return MurmurHashUtils.getMurmurHash3_32(bytes, DEFAULT_OFFSET, bytes.length, DEFAULT_SEED);
    }

    public static String getMurmurHash3_32(byte[] data, int offset, int len, int seed) {
        int trueCode = MurmurHash3.murmurhash3_x86_32(data, offset, len, seed);
        return Integer.toUnsignedString(trueCode, 10);
    }

    public static String getMurmurHash3_64(String symbol) {
        byte[] data = symbol.getBytes();
        return MurmurHashUtils.getMurmurHash3_64(data, DEFAULT_OFFSET, data.length, DEFAULT_SEED);
    }

    public static String getMurmurHash3_64(byte[] data, int offset, int len, int seed) {
        MurmurHash3.LongPair pair = new MurmurHash3.LongPair();
        MurmurHash3.murmurhash3_x64_128(data, offset, len, seed, pair);
        long trueCode = pair.val1 ^ pair.val2;
        return Long.toUnsignedString(trueCode, 10);
    }

    public static void main(String[] args) {
        String murmurHash3_64 = MurmurHashUtils.getMurmurHash3_64("pc+memory+deals");
        System.out.println(murmurHash3_64);
    }

    /**
     * ewainc @Date20210119 @Desc适用于 clickhouse murmurHash_v2
     */

    public static class MurmurHash2 {

        public static int hash32(final byte[] data, int length, int seed) {
            final int m = 0x5bd1e995;
            final int r = 24;

            int h = seed ^ length;
            int length4 = length / 4;

            for (int i = 0; i < length4; i++) {
                final int i4 = i * 4;
                int k = (data[i4 + 0] & 0xff) + ((data[i4 + 1] & 0xff) << 8)
                        + ((data[i4 + 2] & 0xff) << 16) + ((data[i4 + 3] & 0xff) << 24);
                k *= m;
                k ^= k >>> r;
                k *= m;
                h *= m;
                h ^= k;
            }

            switch (length % 4) {
                case 3:
                    h ^= (data[(length & ~3) + 2] & 0xff) << 16;
                case 2:
                    h ^= (data[(length & ~3) + 1] & 0xff) << 8;
                case 1:
                    h ^= (data[length & ~3] & 0xff);
                    h *= m;
            }

            h ^= h >>> 13;
            h *= m;
            h ^= h >>> 15;

            return h;
        }

        public static int hash32(final byte[] data, int length) {
            return hash32(data, length, 0);
        }

        public static int hash32(final String text) {
            final byte[] bytes = text.getBytes();
            return hash32(bytes, bytes.length);
        }

        public static int hash32(final String text, int from, int length) {
            return hash32(text.substring(from, from + length));
        }

        public static long hash64(final byte[] data, int length, int seed) {
            final long m = 0xc6a4a7935bd1e995L;
            final int r = 47;

            long h = (seed & 0xffffffffl) ^ (length * m);

            int length8 = length / 8;

            for (int i = 0; i < length8; i++) {
                final int i8 = i * 8;
                long k = ((long) data[i8 + 0] & 0xff) + (((long) data[i8 + 1] & 0xff) << 8)
                        + (((long) data[i8 + 2] & 0xff) << 16) + (((long) data[i8 + 3] & 0xff) << 24)
                        + (((long) data[i8 + 4] & 0xff) << 32) + (((long) data[i8 + 5] & 0xff) << 40)
                        + (((long) data[i8 + 6] & 0xff) << 48) + (((long) data[i8 + 7] & 0xff) << 56);

                k *= m;
                k ^= k >>> r;
                k *= m;

                h ^= k;
                h *= m;
            }

            switch (length % 8) {
                case 7:
                    h ^= (long) (data[(length & ~7) + 6] & 0xff) << 48;
                case 6:
                    h ^= (long) (data[(length & ~7) + 5] & 0xff) << 40;
                case 5:
                    h ^= (long) (data[(length & ~7) + 4] & 0xff) << 32;
                case 4:
                    h ^= (long) (data[(length & ~7) + 3] & 0xff) << 24;
                case 3:
                    h ^= (long) (data[(length & ~7) + 2] & 0xff) << 16;
                case 2:
                    h ^= (long) (data[(length & ~7) + 1] & 0xff) << 8;
                case 1:
                    h ^= (long) (data[length & ~7] & 0xff);
                    h *= m;
            }
            ;

            h ^= h >>> r;
            h *= m;
            h ^= h >>> r;

            return h;
        }

        public static long hash64(final byte[] data, int length) {
            return hash64(data, length, 0);
        }

        public static long hash64(final String text) {
            final byte[] bytes = text.getBytes();
            return hash64(bytes, bytes.length);
        }

        public static long hash64(final String text, int from, int length) {
            return hash64(text.substring(from, from + length));
        }

    }

    public static class MurmurHash3 {
        public static final class LongPair {
            public long val1;
            public long val2;
        }

        public static final int fmix32(int h) {
            h ^= h >>> 16;
            h *= 0x85ebca6b;
            h ^= h >>> 13;
            h *= 0xc2b2ae35;
            h ^= h >>> 16;
            return h;
        }

        public static final long fmix64(long k) {
            k ^= k >>> 33;
            k *= 0xff51afd7ed558ccdL;
            k ^= k >>> 33;
            k *= 0xc4ceb9fe1a85ec53L;
            k ^= k >>> 33;
            return k;
        }

        public static final long getLongLittleEndian(byte[] buf, int offset) {
            return ((long) buf[offset + 7] << 56)
                    | ((buf[offset + 6] & 0xffL) << 48)
                    | ((buf[offset + 5] & 0xffL) << 40)
                    | ((buf[offset + 4] & 0xffL) << 32)
                    | ((buf[offset + 3] & 0xffL) << 24)
                    | ((buf[offset + 2] & 0xffL) << 16)
                    | ((buf[offset + 1] & 0xffL) << 8)
                    | ((buf[offset] & 0xffL));
        }

        public static int murmurhash3_x86_32(byte[] data, int offset, int len, int seed) {

            final int c1 = 0xcc9e2d51;
            final int c2 = 0x1b873593;

            int h1 = seed;
            int roundedEnd = offset + (len & 0xfffffffc);

            for (int i = offset; i < roundedEnd; i += 4) {
                int k1 = (data[i] & 0xff) | ((data[i + 1] & 0xff) << 8) | ((data[i + 2] & 0xff) << 16) | (data[i + 3] << 24);
                k1 *= c1;
                k1 = (k1 << 15) | (k1 >>> 17);
                k1 *= c2;

                h1 ^= k1;
                h1 = (h1 << 13) | (h1 >>> 19);
                h1 = h1 * 5 + 0xe6546b64;
            }

            int k1 = 0;

            switch (len & 0x03) {
                case 3:
                    k1 = (data[roundedEnd + 2] & 0xff) << 16;
                case 2:
                    k1 |= (data[roundedEnd + 1] & 0xff) << 8;
                case 1:
                    k1 |= (data[roundedEnd] & 0xff);
                    k1 *= c1;
                    k1 = (k1 << 15) | (k1 >>> 17);
                    k1 *= c2;
                    h1 ^= k1;
            }

            h1 ^= len;

            h1 ^= h1 >>> 16;
            h1 *= 0x85ebca6b;
            h1 ^= h1 >>> 13;
            h1 *= 0xc2b2ae35;
            h1 ^= h1 >>> 16;

            return h1;
        }

        public static int murmurhash3_x86_32(CharSequence data, int offset, int len, int seed) {

            final int c1 = 0xcc9e2d51;
            final int c2 = 0x1b873593;

            int h1 = seed;

            int pos = offset;
            int end = offset + len;
            int k1 = 0;
            int k2 = 0;
            int shift = 0;
            int bits = 0;
            int nBytes = 0;


            while (pos < end) {
                int code = data.charAt(pos++);
                if (code < 0x80) {
                    k2 = code;
                    bits = 8;

                } else if (code < 0x800) {
                    k2 = (0xC0 | (code >> 6))
                            | ((0x80 | (code & 0x3F)) << 8);
                    bits = 16;
                } else if (code < 0xD800 || code > 0xDFFF || pos >= end) {
                    k2 = (0xE0 | (code >> 12))
                            | ((0x80 | ((code >> 6) & 0x3F)) << 8)
                            | ((0x80 | (code & 0x3F)) << 16);
                    bits = 24;
                } else {
                    int utf32 = (int) data.charAt(pos++);
                    utf32 = ((code - 0xD7C0) << 10) + (utf32 & 0x3FF);
                    k2 = (0xff & (0xF0 | (utf32 >> 18)))
                            | ((0x80 | ((utf32 >> 12) & 0x3F))) << 8
                            | ((0x80 | ((utf32 >> 6) & 0x3F))) << 16
                            | (0x80 | (utf32 & 0x3F)) << 24;
                    bits = 32;
                }


                k1 |= k2 << shift;

                shift += bits;
                if (shift >= 32) {

                    k1 *= c1;
                    k1 = (k1 << 15) | (k1 >>> 17);
                    k1 *= c2;

                    h1 ^= k1;
                    h1 = (h1 << 13) | (h1 >>> 19);
                    h1 = h1 * 5 + 0xe6546b64;

                    shift -= 32;
                    if (shift != 0) {
                        k1 = k2 >>> (bits - shift);
                    } else {
                        k1 = 0;
                    }
                    nBytes += 4;
                }

            }

            if (shift > 0) {
                nBytes += shift >> 3;
                k1 *= c1;
                k1 = (k1 << 15) | (k1 >>> 17);
                k1 *= c2;
                h1 ^= k1;
            }

            h1 ^= nBytes;

            h1 ^= h1 >>> 16;
            h1 *= 0x85ebca6b;
            h1 ^= h1 >>> 13;
            h1 *= 0xc2b2ae35;
            h1 ^= h1 >>> 16;

            return h1;
        }

        public static void murmurhash3_x64_128(byte[] key, int offset, int len, int seed, LongPair out) {
            long h1 = seed & 0x00000000FFFFFFFFL;
            long h2 = seed & 0x00000000FFFFFFFFL;

            final long c1 = 0x87c37b91114253d5L;
            final long c2 = 0x4cf5ad432745937fL;

            int roundedEnd = offset + (len & 0xFFFFFFF0);
            for (int i = offset; i < roundedEnd; i += 16) {
                long k1 = getLongLittleEndian(key, i);
                long k2 = getLongLittleEndian(key, i + 8);
                k1 *= c1;
                k1 = Long.rotateLeft(k1, 31);
                k1 *= c2;
                h1 ^= k1;
                h1 = Long.rotateLeft(h1, 27);
                h1 += h2;
                h1 = h1 * 5 + 0x52dce729;
                k2 *= c2;
                k2 = Long.rotateLeft(k2, 33);
                k2 *= c1;
                h2 ^= k2;
                h2 = Long.rotateLeft(h2, 31);
                h2 += h1;
                h2 = h2 * 5 + 0x38495ab5;
            }

            long k1 = 0;
            long k2 = 0;

            switch (len & 15) {
                case 15:
                    k2 = (key[roundedEnd + 14] & 0xffL) << 48;
                case 14:
                    k2 |= (key[roundedEnd + 13] & 0xffL) << 40;
                case 13:
                    k2 |= (key[roundedEnd + 12] & 0xffL) << 32;
                case 12:
                    k2 |= (key[roundedEnd + 11] & 0xffL) << 24;
                case 11:
                    k2 |= (key[roundedEnd + 10] & 0xffL) << 16;
                case 10:
                    k2 |= (key[roundedEnd + 9] & 0xffL) << 8;
                case 9:
                    k2 |= (key[roundedEnd + 8] & 0xffL);
                    k2 *= c2;
                    k2 = Long.rotateLeft(k2, 33);
                    k2 *= c1;
                    h2 ^= k2;
                case 8:
                    k1 = ((long) key[roundedEnd + 7]) << 56;
                case 7:
                    k1 |= (key[roundedEnd + 6] & 0xffL) << 48;
                case 6:
                    k1 |= (key[roundedEnd + 5] & 0xffL) << 40;
                case 5:
                    k1 |= (key[roundedEnd + 4] & 0xffL) << 32;
                case 4:
                    k1 |= (key[roundedEnd + 3] & 0xffL) << 24;
                case 3:
                    k1 |= (key[roundedEnd + 2] & 0xffL) << 16;
                case 2:
                    k1 |= (key[roundedEnd + 1] & 0xffL) << 8;
                case 1:
                    k1 |= (key[roundedEnd] & 0xffL);
                    k1 *= c1;
                    k1 = Long.rotateLeft(k1, 31);
                    k1 *= c2;
                    h1 ^= k1;
            }

            h1 ^= len;
            h2 ^= len;

            h1 += h2;
            h2 += h1;

            h1 = fmix64(h1);
            h2 = fmix64(h2);

            h1 += h2;
            h2 += h1;

            out.val1 = h1;
            out.val2 = h2;
        }


    }
}
