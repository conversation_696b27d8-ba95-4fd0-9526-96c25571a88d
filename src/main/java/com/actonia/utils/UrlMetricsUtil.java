package com.actonia.utils;

import java.net.MalformedURLException;
import java.net.URL;

import org.apache.commons.lang.StringUtils;

public class UrlMetricsUtil {

	private static final String UNKNOWN_PROTOCOL = "unknown protocol";
	private static final String DOT = ".";
	private static final String HTTP_PROTOCOL_STRING = "http://";
	private static final String HTTPS_PROTOCOL_STRING = "https://";
	private static final String PROTOCOL_HTTPS = "https";
	private static final String PROTOCOL_HTTP = "http";

	public static Boolean checkIfClientDomainUrl(String urlStringToBeValidated, String clientDomainName, boolean isBroadMatch) {
		Boolean output = false;
		String clientDomainNameStartsWithHttp = null;
		String clientDomainNameStartsWithHttps = null;
		URL url = null;
		String protocol = null;
		String hostname = null;
		String clientDomainNameWithout3w = URLConnectionUtils.getDomainByUrl(clientDomainName, true);
		String clientDomainNameWithout3wStartsWithDot = DOT.concat(clientDomainNameWithout3w);

		// when URL string to be validated starts with the exact client domain name
		// eg. http://www.exampleclientdomain.com
		// eg. https://www.exampleclientdomain.com
		if (isBroadMatch == false) {
			clientDomainNameStartsWithHttp = HTTP_PROTOCOL_STRING.concat(clientDomainName);
			clientDomainNameStartsWithHttps = HTTPS_PROTOCOL_STRING.concat(clientDomainName);
			if (StringUtils.startsWithIgnoreCase(urlStringToBeValidated, clientDomainNameStartsWithHttp) == true
					|| StringUtils.startsWithIgnoreCase(urlStringToBeValidated, clientDomainNameStartsWithHttps) == true) {
				output = true;
			}
		}
		// when URL string to be validated starts with client domain name without the sub-domain
		// eg. http://????.exampleclientdomain.com
		// eg. https://????.exampleclientdomain.com
		else {
			try {
				url = new URL(urlStringToBeValidated);
				protocol = url.getProtocol();
				if (StringUtils.equalsIgnoreCase(protocol, PROTOCOL_HTTP) || StringUtils.equalsIgnoreCase(protocol, PROTOCOL_HTTPS)) {
					hostname = url.getHost();
					if (StringUtils.endsWithIgnoreCase(hostname, clientDomainNameWithout3w)) {
						output = true;
					}
				}
			} catch (MalformedURLException e) {
				if (!StringUtils.containsIgnoreCase(e.getMessage(), UNKNOWN_PROTOCOL)) {
					if (StringUtils.contains(urlStringToBeValidated, clientDomainNameWithout3wStartsWithDot)) {
						output = true;
					}
				}
			}
		}
		return output;
	}
}
