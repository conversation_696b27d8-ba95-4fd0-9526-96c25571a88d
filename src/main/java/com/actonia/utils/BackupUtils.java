package com.actonia.utils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.time.DateFormatUtils;

import com.actonia.IConstants;
import com.actonia.dao.BackupAuditTrailDAO;
import com.actonia.dao.ClickHouseCrawlDatabaseConfigurations;
import com.actonia.dao.CompetitorUrlHtmlBackupClickHouseDAO;
import com.actonia.dao.ContentGuardBackupClickHouseDAO;
import com.actonia.dao.TargetUrlChangeIndBackupClickHouseDAO;
import com.actonia.dao.TargetUrlCustomDataBackupClickHouseDAO;
import com.actonia.dao.TargetUrlHtmlBackupClickHouseDAO;
import com.actonia.dao.TargetUrlHtmlDailyBackupClickHouseDAO;
import com.actonia.dao.TargetUrlHtmlFileNameBackupClickHouseDAO;
import com.actonia.entity.BackupAuditTrailEntity;

public class BackupUtils {

	private static BackupUtils backupUtils;
	private BackupAuditTrailDAO backupAuditTrailDAO;

	// map key = databaseConnectionUrl
	// map value = TargetUrlHtmlBackupClickHouseDAO
	private static Map<String, TargetUrlHtmlBackupClickHouseDAO> targetUrlHtmlBackupClickHouseDAOMap = new HashMap<String, TargetUrlHtmlBackupClickHouseDAO>();

	// map key = databaseConnectionUrl
	// map value = CompetitorUrlHtmlBackupClickHouseDAO
	private static Map<String, CompetitorUrlHtmlBackupClickHouseDAO> competitorUrlHtmlBackupClickHouseDAOMap = new HashMap<String, CompetitorUrlHtmlBackupClickHouseDAO>();

	// map key = databaseConnectionUrl
	// map value = ContentGuardBackupClickHouseDAO
	private static Map<String, ContentGuardBackupClickHouseDAO> contentGuardBackupClickHouseDAOMap = new HashMap<String, ContentGuardBackupClickHouseDAO>();

	// map key = databaseConnectionUrl
	// map value = TargetUrlHtmlFileNameBackupClickHouseDAO
	private static Map<String, TargetUrlHtmlFileNameBackupClickHouseDAO> targetUrlHtmlFileNameBackupClickHouseDAOMap = new HashMap<String, TargetUrlHtmlFileNameBackupClickHouseDAO>();

	// map key = databaseConnectionUrl
	// map value = TargetUrlHtmlDailyBackupClickHouseDAO
	private static Map<String, TargetUrlHtmlDailyBackupClickHouseDAO> targetUrlHtmlDailyBackupClickHouseDAOMap = new HashMap<String, TargetUrlHtmlDailyBackupClickHouseDAO>();

	// map key = databaseConnectionUrl
	// map value = TargetUrlCustomDataBackupClickHouseDAO
	private static Map<String, TargetUrlCustomDataBackupClickHouseDAO> targetUrlCustomDataBackupClickHouseDAOMap = new HashMap<String, TargetUrlCustomDataBackupClickHouseDAO>();

	// map key = databaseConnectionUrl
	// map value = TargetUrlChangeIndBackupClickHouseDAO
	private static Map<String, TargetUrlChangeIndBackupClickHouseDAO> targetUrlChangeIndBackupClickHouseDAOMap = new HashMap<String, TargetUrlChangeIndBackupClickHouseDAO>();

	public BackupUtils() {
		super();
		this.backupAuditTrailDAO = SpringBeanFactory.getBean("backupAuditTrailDAO");
	}

	public static BackupUtils getInstance() {
		if (backupUtils == null) {
			backupUtils = new BackupUtils();
		}
		return backupUtils;
	}

	public void updateBackupAuditTrailEndDate(String sourceTableName, String backupDateString, String s3BucketName, String s3PartialPrefix, String endDateString) {
		backupAuditTrailDAO.updateEndDate(sourceTableName, s3BucketName, s3PartialPrefix, backupDateString, endDateString);
	}

	public void maintainBackupAuditTrailAndS3(String sourceTableName, String backupDateString, String s3BucketName, String s3PartialPrefix, Date startTrackDate,
			Date endTrackDate) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("maintainBackupAuditTrailAndS3() begins. backupDateString=" + backupDateString + ",s3BucketName=" + s3BucketName
				+ ",s3PartialPrefix=" + s3PartialPrefix + ",startTrackDate=" + startTrackDate + ",endTrackDate=" + endTrackDate);

		// create audit trail for the current backup
		BackupAuditTrailEntity backupAuditTrailEntity = new BackupAuditTrailEntity();
		backupAuditTrailEntity.setTableName(sourceTableName);
		backupAuditTrailEntity.setBucketName(s3BucketName);
		backupAuditTrailEntity.setPartialPrefix(s3PartialPrefix);
		backupAuditTrailEntity.setBackupDate(backupDateString);
		backupAuditTrailEntity.setStartDate(DateFormatUtils.format(startTrackDate, IConstants.DATE_FORMAT_YYYY_MM_DD));
		backupAuditTrailEntity.setEndDate(DateFormatUtils.format(endTrackDate, IConstants.DATE_FORMAT_YYYY_MM_DD));
		backupAuditTrailDAO.create(backupAuditTrailEntity);
		FormatUtils.getInstance().logMemoryUsage("maintainBackupAuditTrailAndS3() backupAuditTrailEntity created=" + backupAuditTrailEntity.toString());

		// delete S3 backup objects and audit trail when there are more than one backup
		BackupAuditTrailEntity backupAuditTrailEntityToBeDeleted = null;
		String s3BucketNameToBeDeleted = null;
		String s3PartialPrefixToBeDeleted = null;
		String s3FullPrefixToBeDeleted = null;
		String backupDateToBeDeleted = null;
		List<BackupAuditTrailEntity> backupAuditTrailEntityList = backupAuditTrailDAO.getList(sourceTableName, s3BucketName, s3PartialPrefix);
		if (backupAuditTrailEntityList != null && backupAuditTrailEntityList.size() > 1) {
			backupAuditTrailEntityToBeDeleted = backupAuditTrailEntityList.get(0);

			// delete S3 backup folder
			s3BucketNameToBeDeleted = backupAuditTrailEntityToBeDeleted.getBucketName();
			s3PartialPrefixToBeDeleted = backupAuditTrailEntityToBeDeleted.getPartialPrefix();
			backupDateToBeDeleted = backupAuditTrailEntityToBeDeleted.getBackupDate();
			s3FullPrefixToBeDeleted = s3PartialPrefixToBeDeleted + backupDateToBeDeleted + IConstants.SLASH;
			FormatUtils.getInstance().logMemoryUsage(
					"maintainBackupAuditTrailAndS3() s3BucketNameToBeDeleted=" + s3BucketNameToBeDeleted + ",s3FullPrefixToBeDeleted=" + s3FullPrefixToBeDeleted);
			S3Utils.deleteFolder(s3BucketNameToBeDeleted, s3FullPrefixToBeDeleted);

			// delete audit trail record
			backupAuditTrailDAO.delete(sourceTableName, s3BucketName, s3PartialPrefix, backupDateToBeDeleted);
			FormatUtils.getInstance().logMemoryUsage("maintainBackupAuditTrailAndS3() backupAuditTrailEntity deleted. sourceTableName=" + sourceTableName
					+ ",backupDateToBeDeleted=" + backupDateToBeDeleted);
		}
		FormatUtils.getInstance()
				.logMemoryUsage("maintainBackupAuditTrailAndS3() ends. backupDateString=" + backupDateString + ",s3BucketName=" + s3BucketName + ",s3PartialPrefix="
						+ s3PartialPrefix + ",startTrackDate=" + startTrackDate + ",endTrackDate=" + endTrackDate + ",elapsed(s.)="
						+ (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	public String getLastBackupDateString(String sourceTableName, String s3BucketName, String s3PartialPrefix) throws Exception {
		String backupDateString = null;
		BackupAuditTrailEntity backupAuditTrailEntity = null;
		List<BackupAuditTrailEntity> backupAuditTrailEntityList = backupAuditTrailDAO.getList(sourceTableName, s3BucketName, s3PartialPrefix);
		if (backupAuditTrailEntityList != null && backupAuditTrailEntityList.size() == 1) {
			backupAuditTrailEntity = backupAuditTrailEntityList.get(0);
			backupDateString = backupAuditTrailEntity.getBackupDate();
		}
		return backupDateString;
	}

	public synchronized TargetUrlHtmlBackupClickHouseDAO getTargetUrlHtmlBackupClickHouseDAO(String databaseConnectionUrl) {
		TargetUrlHtmlBackupClickHouseDAO targetUrlHtmlBackupClickHouseDAO = null;
		if (targetUrlHtmlBackupClickHouseDAOMap.containsKey(databaseConnectionUrl)) {
			targetUrlHtmlBackupClickHouseDAO = targetUrlHtmlBackupClickHouseDAOMap.get(databaseConnectionUrl);
		} else {
			String[] clickHouseDatabaseHostnameArray = new String[] { databaseConnectionUrl };
			String clickHouseDatabasePort = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_PORT);
			String clickHouseDatabaseName = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_NAME);
			String clickHouseDatabaseUser = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_USER);
			String clickHouseDatabasePassword = ClickHouseCrawlDatabaseConfigurations
					.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_PASSWORD);
			int clickHouseBatchCreationSize = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_BATCH_CREATION_SIZE, 100);
			int clickHouseconnectionTimeoutInMilliseconds = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_CONNECTION_TIMEOUT_MILLISECONDS, 88888);
			int clickHouseMaximumRetryCounts = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_MAX_RETRY_COUNTS, 8);
			int clickHouseRetryWaitMilliseconds = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_RETRY_WAIT_MILLISECONDS, 1000);
			try {
				targetUrlHtmlBackupClickHouseDAO = new TargetUrlHtmlBackupClickHouseDAO(clickHouseDatabaseHostnameArray, clickHouseDatabasePort, clickHouseDatabaseName,
						clickHouseBatchCreationSize, clickHouseDatabaseUser, clickHouseDatabasePassword, clickHouseconnectionTimeoutInMilliseconds,
						clickHouseMaximumRetryCounts, clickHouseRetryWaitMilliseconds);
				targetUrlHtmlBackupClickHouseDAOMap.put(databaseConnectionUrl, targetUrlHtmlBackupClickHouseDAO);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return targetUrlHtmlBackupClickHouseDAO;
	}

	public synchronized CompetitorUrlHtmlBackupClickHouseDAO getCompetitorUrlHtmlBackupClickHouseDAO(String databaseConnectionUrl) {
		CompetitorUrlHtmlBackupClickHouseDAO competitorUrlHtmlBackupClickHouseDAO = null;
		if (competitorUrlHtmlBackupClickHouseDAOMap.containsKey(databaseConnectionUrl)) {
			competitorUrlHtmlBackupClickHouseDAO = competitorUrlHtmlBackupClickHouseDAOMap.get(databaseConnectionUrl);
		} else {
			String[] clickHouseDatabaseHostnameArray = new String[] { databaseConnectionUrl };
			String clickHouseDatabasePort = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_PORT);
			String clickHouseDatabaseName = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_NAME);
			String clickHouseDatabaseUser = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_USER);
			String clickHouseDatabasePassword = ClickHouseCrawlDatabaseConfigurations
					.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_PASSWORD);
			int clickHouseBatchCreationSize = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_BATCH_CREATION_SIZE, 100);
			int clickHouseconnectionTimeoutInMilliseconds = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_CONNECTION_TIMEOUT_MILLISECONDS, 88888);
			int clickHouseMaximumRetryCounts = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_MAX_RETRY_COUNTS, 8);
			int clickHouseRetryWaitMilliseconds = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_RETRY_WAIT_MILLISECONDS, 1000);
			try {
				competitorUrlHtmlBackupClickHouseDAO = new CompetitorUrlHtmlBackupClickHouseDAO(clickHouseDatabaseHostnameArray, clickHouseDatabasePort,
						clickHouseDatabaseName, clickHouseBatchCreationSize, clickHouseDatabaseUser, clickHouseDatabasePassword,
						clickHouseconnectionTimeoutInMilliseconds, clickHouseMaximumRetryCounts, clickHouseRetryWaitMilliseconds);
				competitorUrlHtmlBackupClickHouseDAOMap.put(databaseConnectionUrl, competitorUrlHtmlBackupClickHouseDAO);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return competitorUrlHtmlBackupClickHouseDAO;
	}

	public synchronized ContentGuardBackupClickHouseDAO getContentGuardBackupClickHouseDAO(String databaseConnectionUrl) {
		ContentGuardBackupClickHouseDAO contentGuardBackupClickHouseDAO = null;
		if (contentGuardBackupClickHouseDAOMap.containsKey(databaseConnectionUrl)) {
			contentGuardBackupClickHouseDAO = contentGuardBackupClickHouseDAOMap.get(databaseConnectionUrl);
		} else {
			String[] clickHouseDatabaseHostnameArray = new String[] { databaseConnectionUrl };
			String clickHouseDatabasePort = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_PORT);
			String clickHouseDatabaseName = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_NAME);
			String clickHouseDatabaseUser = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_USER);
			String clickHouseDatabasePassword = ClickHouseCrawlDatabaseConfigurations
					.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_PASSWORD);
			int clickHouseBatchCreationSize = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_BATCH_CREATION_SIZE, 100);
			int clickHouseconnectionTimeoutInMilliseconds = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_CONNECTION_TIMEOUT_MILLISECONDS, 88888);
			int clickHouseMaximumRetryCounts = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_MAX_RETRY_COUNTS, 8);
			int clickHouseRetryWaitMilliseconds = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_RETRY_WAIT_MILLISECONDS, 1000);
			try {
				contentGuardBackupClickHouseDAO = new ContentGuardBackupClickHouseDAO(clickHouseDatabaseHostnameArray, clickHouseDatabasePort, clickHouseDatabaseName,
						clickHouseBatchCreationSize, clickHouseDatabaseUser, clickHouseDatabasePassword, clickHouseconnectionTimeoutInMilliseconds,
						clickHouseMaximumRetryCounts, clickHouseRetryWaitMilliseconds);
				contentGuardBackupClickHouseDAOMap.put(databaseConnectionUrl, contentGuardBackupClickHouseDAO);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return contentGuardBackupClickHouseDAO;
	}

	public synchronized TargetUrlHtmlFileNameBackupClickHouseDAO getTargetUrlHtmlFileNameBackupClickHouseDAO(String databaseConnectionUrl) {
		TargetUrlHtmlFileNameBackupClickHouseDAO targetUrlHtmlFileNameBackupClickHouseDAO = null;
		if (targetUrlHtmlFileNameBackupClickHouseDAOMap.containsKey(databaseConnectionUrl)) {
			targetUrlHtmlFileNameBackupClickHouseDAO = targetUrlHtmlFileNameBackupClickHouseDAOMap.get(databaseConnectionUrl);
		} else {
			String[] clickHouseDatabaseHostnameArray = new String[] { databaseConnectionUrl };
			String clickHouseDatabasePort = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_PORT);
			String clickHouseDatabaseName = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_NAME);
			String clickHouseDatabaseUser = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_USER);
			String clickHouseDatabasePassword = ClickHouseCrawlDatabaseConfigurations
					.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_PASSWORD);
			int clickHouseBatchCreationSize = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_BATCH_CREATION_SIZE, 100);
			int clickHouseconnectionTimeoutInMilliseconds = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_CONNECTION_TIMEOUT_MILLISECONDS, 88888);
			int clickHouseMaximumRetryCounts = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_MAX_RETRY_COUNTS, 8);
			int clickHouseRetryWaitMilliseconds = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_RETRY_WAIT_MILLISECONDS, 1000);
			try {
				targetUrlHtmlFileNameBackupClickHouseDAO = new TargetUrlHtmlFileNameBackupClickHouseDAO(clickHouseDatabaseHostnameArray, clickHouseDatabasePort,
						clickHouseDatabaseName, clickHouseBatchCreationSize, clickHouseDatabaseUser, clickHouseDatabasePassword,
						clickHouseconnectionTimeoutInMilliseconds, clickHouseMaximumRetryCounts, clickHouseRetryWaitMilliseconds);
				targetUrlHtmlFileNameBackupClickHouseDAOMap.put(databaseConnectionUrl, targetUrlHtmlFileNameBackupClickHouseDAO);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return targetUrlHtmlFileNameBackupClickHouseDAO;
	}

	public synchronized TargetUrlHtmlDailyBackupClickHouseDAO getTargetUrlHtmlDailyBackupClickHouseDAO(String databaseConnectionUrl) {
		TargetUrlHtmlDailyBackupClickHouseDAO targetUrlHtmlDailyBackupClickHouseDAO = null;
		if (targetUrlHtmlDailyBackupClickHouseDAOMap.containsKey(databaseConnectionUrl)) {
			targetUrlHtmlDailyBackupClickHouseDAO = targetUrlHtmlDailyBackupClickHouseDAOMap.get(databaseConnectionUrl);
		} else {
			String[] clickHouseDatabaseHostnameArray = new String[] { databaseConnectionUrl };
			String clickHouseDatabasePort = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_PORT);
			String clickHouseDatabaseName = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_NAME);
			String clickHouseDatabaseUser = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_USER);
			String clickHouseDatabasePassword = ClickHouseCrawlDatabaseConfigurations
					.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_PASSWORD);
			int clickHouseBatchCreationSize = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_BATCH_CREATION_SIZE, 100);
			int clickHouseconnectionTimeoutInMilliseconds = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_CONNECTION_TIMEOUT_MILLISECONDS, 88888);
			int clickHouseMaximumRetryCounts = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_MAX_RETRY_COUNTS, 8);
			int clickHouseRetryWaitMilliseconds = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_RETRY_WAIT_MILLISECONDS, 1000);
			try {
				targetUrlHtmlDailyBackupClickHouseDAO = new TargetUrlHtmlDailyBackupClickHouseDAO(clickHouseDatabaseHostnameArray, clickHouseDatabasePort,
						clickHouseDatabaseName, clickHouseBatchCreationSize, clickHouseDatabaseUser, clickHouseDatabasePassword,
						clickHouseconnectionTimeoutInMilliseconds, clickHouseMaximumRetryCounts, clickHouseRetryWaitMilliseconds);
				targetUrlHtmlDailyBackupClickHouseDAOMap.put(databaseConnectionUrl, targetUrlHtmlDailyBackupClickHouseDAO);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return targetUrlHtmlDailyBackupClickHouseDAO;
	}

	public synchronized TargetUrlCustomDataBackupClickHouseDAO getTargetUrlCustomDataBackupClickHouseDAO(String databaseConnectionUrl) {
		TargetUrlCustomDataBackupClickHouseDAO targetUrlCustomDataBackupClickHouseDAO = null;
		if (targetUrlCustomDataBackupClickHouseDAOMap.containsKey(databaseConnectionUrl)) {
			targetUrlCustomDataBackupClickHouseDAO = targetUrlCustomDataBackupClickHouseDAOMap.get(databaseConnectionUrl);
		} else {
			String[] clickHouseDatabaseHostnameArray = new String[] { databaseConnectionUrl };
			String clickHouseDatabasePort = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_PORT);
			String clickHouseDatabaseName = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_NAME);
			String clickHouseDatabaseUser = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_USER);
			String clickHouseDatabasePassword = ClickHouseCrawlDatabaseConfigurations
					.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_PASSWORD);
			int clickHouseBatchCreationSize = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_BATCH_CREATION_SIZE, 100);
			int clickHouseconnectionTimeoutInMilliseconds = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_CONNECTION_TIMEOUT_MILLISECONDS, 88888);
			int clickHouseMaximumRetryCounts = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_MAX_RETRY_COUNTS, 8);
			int clickHouseRetryWaitMilliseconds = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_RETRY_WAIT_MILLISECONDS, 1000);
			try {
				targetUrlCustomDataBackupClickHouseDAO = new TargetUrlCustomDataBackupClickHouseDAO(clickHouseDatabaseHostnameArray, clickHouseDatabasePort,
						clickHouseDatabaseName, clickHouseBatchCreationSize, clickHouseDatabaseUser, clickHouseDatabasePassword,
						clickHouseconnectionTimeoutInMilliseconds, clickHouseMaximumRetryCounts, clickHouseRetryWaitMilliseconds);
				targetUrlCustomDataBackupClickHouseDAOMap.put(databaseConnectionUrl, targetUrlCustomDataBackupClickHouseDAO);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return targetUrlCustomDataBackupClickHouseDAO;
	}

	public synchronized TargetUrlChangeIndBackupClickHouseDAO getTargetUrlChangeIndBackupClickHouseDAO(String databaseConnectionUrl) {
		TargetUrlChangeIndBackupClickHouseDAO targetUrlChangeIndBackupClickHouseDAO = null;
		if (targetUrlChangeIndBackupClickHouseDAOMap.containsKey(databaseConnectionUrl)) {
			targetUrlChangeIndBackupClickHouseDAO = targetUrlChangeIndBackupClickHouseDAOMap.get(databaseConnectionUrl);
		} else {
			String[] clickHouseDatabaseHostnameArray = new String[] { databaseConnectionUrl };
			String clickHouseDatabasePort = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_PORT);
			String clickHouseDatabaseName = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_NAME);
			String clickHouseDatabaseUser = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_USER);
			String clickHouseDatabasePassword = ClickHouseCrawlDatabaseConfigurations
					.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_PASSWORD);
			int clickHouseBatchCreationSize = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_BATCH_CREATION_SIZE, 100);
			int clickHouseconnectionTimeoutInMilliseconds = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_CONNECTION_TIMEOUT_MILLISECONDS, 88888);
			int clickHouseMaximumRetryCounts = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_MAX_RETRY_COUNTS, 8);
			int clickHouseRetryWaitMilliseconds = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_RETRY_WAIT_MILLISECONDS, 1000);
			try {
				targetUrlChangeIndBackupClickHouseDAO = new TargetUrlChangeIndBackupClickHouseDAO(clickHouseDatabaseHostnameArray, clickHouseDatabasePort, clickHouseDatabaseName,
						clickHouseBatchCreationSize, clickHouseDatabaseUser, clickHouseDatabasePassword, clickHouseconnectionTimeoutInMilliseconds,
						clickHouseMaximumRetryCounts, clickHouseRetryWaitMilliseconds);
				targetUrlChangeIndBackupClickHouseDAOMap.put(databaseConnectionUrl, targetUrlChangeIndBackupClickHouseDAO);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return targetUrlChangeIndBackupClickHouseDAO;
	}
}
