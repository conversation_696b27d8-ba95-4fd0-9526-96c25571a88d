package com.actonia.utils;

import java.net.URI;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;

import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.StatusLine;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;

import com.actonia.IConstants;

public class HttpUtils {

	private static HttpUtils httpUtils;
	private static final int HTTP_REQUEST_TIMEOUT_IN_SECONDS = 1680;

	public static HttpUtils getInstance() {
		if (httpUtils == null) {
			httpUtils = new HttpUtils();
		}
		return httpUtils;
	}

	public String getResponseString(String requestUrl, boolean isSendGetRequest, String requestParameters) throws Exception {
		return getResponseString(requestUrl, isSendGetRequest, requestParameters, null, null);
	}

	public String getResponseString(String requestUrl, boolean isSendGetRequest, String requestParameters, String headerName, String headerValue) throws Exception {
		//FormatUtils.getInstance().logMemoryUsage("getHttpResponseAsString() begins. requestUrl=" + requestUrl + ",timeOutInSeconds=" + timeOutInSeconds
		//		+ ",isSendGetRequest=" + isSendGetRequest + ",requestParameters=" + requestParameters);
		//long startTimestamp = System.currentTimeMillis();
		String responseString = null;
		StatusLine statusLine = null;
		int httpStatusCode = 0;
		String httpReasonPhrase = null;
		CloseableHttpClient httpClient = null;
		HttpGet httpGet = null;
		HttpPost httpPost = null;
		CloseableHttpResponse httpResponse = null;
		int retryCount = 0;
		nextRetry: while (retryCount < IConstants.MAX_RETRY_COUNT) {
			try {
				httpClient = getHttpClient();

				// when sending HTTP GET request.....
				if (isSendGetRequest == true) {

					httpGet = getHttpGet(requestUrl);

					// when httpGet object cannot be instantiated due to "IllegalArgumentException" 
					if (httpGet == null) {
						FormatUtils.getInstance()
								.logMemoryUsage("getResponseString() httpGet cannot be instantiated assigning HTTP status code 999, requestUrl=" + requestUrl);
						throw new Exception(String.valueOf(999));
					}

					if (StringUtils.isNotBlank(headerName) && StringUtils.isNotBlank(headerValue)) {
						FormatUtils.getInstance().logMemoryUsage("getResponseString() headerName=" + headerName + ",headerValue=" + headerValue);
						httpGet.setHeader(headerName, headerValue);
					}

					httpResponse = httpClient.execute(httpGet);
				}
				// when sending HTTP POST request.....
				else {
					httpPost = getHttpPost(requestUrl);
					httpPost.setEntity(new StringEntity(requestParameters, IConstants.UTF_8));
					httpPost.setHeader(IConstants.CACHE_CONTROL, IConstants.NO_CACHE);
					httpPost.setHeader(IConstants.CONTENT_DASH_TYPE, IConstants.APPLICATION_SLASH_JSON);
					httpResponse = httpClient.execute(httpPost);
				}
				if (httpResponse != null) {

					// HTTP status code
					statusLine = httpResponse.getStatusLine();
					if (statusLine != null) {
						httpStatusCode = statusLine.getStatusCode();
						httpReasonPhrase = statusLine.getReasonPhrase();

						// HTTP content
						if (httpStatusCode == 200) {
							responseString = getHttpResponseAsString(httpResponse);
						} else {
							FormatUtils.getInstance().logMemoryUsage("getResponseString() httpStatusCode=" + httpStatusCode + ",httpReasonPhrase=" + httpReasonPhrase
									+ ", requestUrl=" + requestUrl + ",requestParameters=" + requestParameters);
							throw new Exception(String.valueOf(httpStatusCode));
						}
					}
				}
				retryCount = IConstants.MAX_RETRY_COUNT;
				break nextRetry;
			} catch (Exception e) {
				throw e;
			} finally {
				if (httpResponse != null) {
					try {
						httpResponse.close();
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
				if (httpClient != null) {
					try {
						httpClient.close();
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			}
		}
		//FormatUtils.getInstance().logMemoryUsage("getResponseString() ends. requestUrl=" + requestUrl + ",responseString=" + responseString + ",elapsed(s.)="
		//		+ (System.currentTimeMillis() - startTimestamp) / 1000);
		return responseString;
	}

	public String getDeleteResponseString(String requestUrl) throws Exception {
		//FormatUtils.getInstance().logMemoryUsage("getHttpResponseAsString() begins. requestUrl=" + requestUrl + ",timeOutInSeconds=" + timeOutInSeconds
		//		+ ",isSendGetRequest=" + isSendGetRequest + ",requestParameters=" + requestParameters);
		//long startTimestamp = System.currentTimeMillis();
		String responseString = null;
		StatusLine statusLine = null;
		int httpStatusCode = 0;
		String httpReasonPhrase = null;
		CloseableHttpClient httpClient = null;
		HttpDelete httpDelete = null;
		CloseableHttpResponse httpResponse = null;
		int retryCount = 0;
		String errorMessage = null;
		nextRetry: while (retryCount < IConstants.MAX_RETRY_COUNT) {
			try {
				httpClient = getHttpClient();
				httpDelete = getHttpDelete(requestUrl);
				httpResponse = httpClient.execute(httpDelete);
				if (httpResponse != null) {

					// HTTP status code
					statusLine = httpResponse.getStatusLine();
					if (statusLine != null) {
						httpStatusCode = statusLine.getStatusCode();
						httpReasonPhrase = statusLine.getReasonPhrase();

						// HTTP content
						if (httpStatusCode == 200) {
							responseString = getHttpResponseAsString(httpResponse);
						} else {
							errorMessage = "getResponseString() httpStatusCode=" + httpStatusCode + ",httpReasonPhrase=" + httpReasonPhrase + ", requestUrl="
									+ requestUrl;
							FormatUtils.getInstance().logMemoryUsage(errorMessage);
							throw new Exception(errorMessage);
						}
					}
				}
				retryCount = IConstants.MAX_RETRY_COUNT;
				break nextRetry;
			} catch (Exception e) {
				throw e;
			} finally {
				if (httpResponse != null) {
					try {
						httpResponse.close();
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
				if (httpClient != null) {
					try {
						httpClient.close();
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			}
		}
		//FormatUtils.getInstance().logMemoryUsage("getDeleteResponseString() ends. requestUrl=" + requestUrl + ",responseString=" + responseString + ",elapsed(s.)="
		//		+ (System.currentTimeMillis() - startTimestamp) / 1000);
		return responseString;
	}

	public CloseableHttpClient getHttpClient() throws Exception {
		return getHttpClient(HTTP_REQUEST_TIMEOUT_IN_SECONDS);
	}

	public CloseableHttpClient getHttpClient(int timeoutInSeconds) throws Exception {

		// use the TrustSelfSignedStrategy to allow Self Signed Certificates
		SSLContext sslContext = SSLContextBuilder.create().loadTrustMaterial(new TrustSelfSignedStrategy()).build();

		// we can optionally disable hostname verification. 
		// if you don't want to further weaken the security, you don't have to include this.
		HostnameVerifier allowAllHosts = new NoopHostnameVerifier();

		// create an SSL Socket Factory to use the SSLContext with the trust self signed certificate strategy
		// and allow all hosts verifier.
		SSLConnectionSocketFactory connectionFactory = new SSLConnectionSocketFactory(sslContext, allowAllHosts);

		RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(timeoutInSeconds * 1000).setConnectionRequestTimeout(timeoutInSeconds * 1000)
				.setSocketTimeout(timeoutInSeconds * 1000).build();

		// finally create the HttpClient using HttpClient factory methods and assign the ssl socket factory
		return HttpClients.custom().setDefaultRequestConfig(requestConfig).setSSLSocketFactory(connectionFactory).disableRedirectHandling().build();
	}

	public HttpGet getHttpGet(String url) {
		HttpGet httpGet = null;
		URI uri;
		try {
			uri = new URI(url);
			httpGet = new HttpGet(uri);
		} catch (Exception e1) {
			e1.printStackTrace();
		}
		return httpGet;
	}

	public HttpPost getHttpPost(String url) {
		HttpPost httpPost = null;
		URI uri;
		try {
			uri = new URI(url);
			httpPost = new HttpPost(uri);
		} catch (Exception e1) {
			e1.printStackTrace();
		}
		return httpPost;
	}

	public HttpDelete getHttpDelete(String url) {
		HttpDelete httpDelete = null;
		URI uri;
		try {
			uri = new URI(url);
			httpDelete = new HttpDelete(uri);
		} catch (Exception e1) {
			e1.printStackTrace();
		}
		return httpDelete;
	}

	public String getHttpResponseAsString(HttpResponse httpResponse) {
		String responseString = null;
		HttpEntity httpEntity = null;
		try {
			if (httpResponse != null) {
				httpEntity = httpResponse.getEntity();
				if (httpEntity != null) {
					responseString = EntityUtils.toString(httpEntity, IConstants.UTF_8);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return responseString;
	}
}
