package com.actonia.utils;

import java.math.BigInteger;
import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.dao.SharedCountsClickHouseDAO;
import com.actonia.dao.SharedCountsCrossReferenceClickHouseDAO;

public class ClickHouseUtils {

	private static SharedCountsClickHouseDAO sharedCountsClickHouseDAO;
	private static SharedCountsCrossReferenceClickHouseDAO sharedCountsCrossReferenceClickHouseDAO;

	static {
		try {
			sharedCountsCrossReferenceClickHouseDAO = initializeSharedCountsCrossReferenceClickHouseDAO();
			sharedCountsClickHouseDAO = initializeSharedCountsClickHouseDAO();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public static Date getLastSundayDate(Integer currentDateNumber) {
		return getLastSundayDate(convertNumberToDate(currentDateNumber));
	}

	public static Date convertNumberToDate(int dateNumber) {
		Date convertedDate = null;
		String dateString = String.valueOf(dateNumber);
		try {
			convertedDate = DateUtils.parseDate(dateString, new String[] { IConstants.DATE_FORMAT_YYYYMMDD });
		} catch (ParseException e) {
			// proceed to return null
		}
		return convertedDate;
	}

	public static Date getLastSundayDate(Date currentDate) {
		Date lastSundayDate = null;
		int dayOfWeek = 0;
		Calendar calendar = null;
		try {
			calendar = new GregorianCalendar();
			calendar.setTime(currentDate);
			dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
			if (dayOfWeek == Calendar.SUNDAY) {
				lastSundayDate = currentDate;
			}
			while (lastSundayDate == null) {
				currentDate = DateUtils.addDays(currentDate, -1);
				calendar = new GregorianCalendar();
				calendar.setTime(currentDate);
				dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
				if (dayOfWeek == Calendar.SUNDAY) {
					lastSundayDate = currentDate;
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return DateUtils.truncate(lastSundayDate, Calendar.DAY_OF_MONTH);
	}

	public static BigInteger calculateUrlHash(String urlString) throws Exception {
		return sharedCountsCrossReferenceClickHouseDAO.calculateUrlHash(urlString);
	}

	public static Map<String, BigInteger> calculateUrlHash(List<String> urlStringList) throws Exception {
		return sharedCountsCrossReferenceClickHouseDAO.calculateUrlHash(urlStringList);
	}

	public static String reverseDomainNameByDot(String arg) {
		if (StringUtils.isBlank(arg)) {
			return "";
		}
		String domainName = StringUtils.reverseDelimited(arg, IConstants.DOT_CHAR);
		return domainName;
	}

	public static String removeLastDomainNameSegment(String reversedDomainName) {
		if (StringUtils.isBlank(reversedDomainName)) {
			return "";
		}
		String reversedDomainNameWithoutLastSegment = null;
		int indexOfLastDomainDot = StringUtils.lastIndexOf(reversedDomainName, IConstants.DOT_CHAR);
		if (indexOfLastDomainDot != -1) {
			reversedDomainNameWithoutLastSegment = StringUtils.substring(reversedDomainName, 0, indexOfLastDomainDot);
		}
		return reversedDomainNameWithoutLastSegment;
	}

	// initialize SharedCountsCrossReferenceClickHouseDAO based on runtime clickhouse configurations 
	private static SharedCountsCrossReferenceClickHouseDAO initializeSharedCountsCrossReferenceClickHouseDAO() throws Exception {
		FormatUtils.getInstance().logMemoryUsage("initializeSharedCountsCrossReferenceClickHouseDAO() begins.");

		SharedCountsCrossReferenceClickHouseDAO sharedCountsCrossReferenceClickHouseDAO = null;

		String clickHouseDatabaseHostnames = ClickHouseConfigurations
				.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_HOSTNAMES);
		String[] clickHouseDatabaseHostnameArray = clickHouseDatabaseHostnames.split(IConstants.COMMA);
		String clickHouseDatabasePort = ClickHouseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_PORT);
		String clickHouseDatabaseName = ClickHouseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_NAME);
		String clickHouseDatabaseUser = ClickHouseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_USER);
		String clickHouseDatabasePassword = ClickHouseConfigurations
				.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_PASSWORD);
		int clickHouseBatchCreationSize = ClickHouseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_BATCH_CREATION_SIZE, 100);
		int clickHouseconnectionTimeoutInMilliseconds = ClickHouseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_CONNECTION_TIMEOUT_MILLISECONDS, 88888);
		int clickHouseMaximumRetryCounts = ClickHouseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_MAX_RETRY_COUNTS, 8);
		int clickHouseRetryWaitMilliseconds = ClickHouseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_RETRY_WAIT_MILLISECONDS, 1000);

		try {
			sharedCountsCrossReferenceClickHouseDAO = new SharedCountsCrossReferenceClickHouseDAO(clickHouseDatabaseHostnameArray, clickHouseDatabasePort,
					clickHouseDatabaseName, clickHouseBatchCreationSize, clickHouseDatabaseUser, clickHouseDatabasePassword,
					clickHouseconnectionTimeoutInMilliseconds, clickHouseMaximumRetryCounts, clickHouseRetryWaitMilliseconds);
		} catch (Exception e) {
			e.printStackTrace();
		}

		FormatUtils.getInstance().logMemoryUsage("initializeSharedCountsCrossReferenceClickHouseDAO() ends. sharedCountsCrossReferenceClickHouseDAO="
				+ sharedCountsCrossReferenceClickHouseDAO.toString());
		return sharedCountsCrossReferenceClickHouseDAO;
	}

	public static SharedCountsCrossReferenceClickHouseDAO getSharedCountsCrossReferenceClickHouseDAO() {
		return sharedCountsCrossReferenceClickHouseDAO;
	}

	// initialize SharedCountsClickHouseDAO based on runtime clickhouse configurations 
	private static SharedCountsClickHouseDAO initializeSharedCountsClickHouseDAO() throws Exception {
		FormatUtils.getInstance().logMemoryUsage("initializeSharedCountsClickHouseDAO() begins.");

		SharedCountsClickHouseDAO sharedCountsClickHouseDAO = null;

		String clickHouseDatabaseHostnames = ClickHouseConfigurations
				.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_HOSTNAMES);
		String[] clickHouseDatabaseHostnameArray = clickHouseDatabaseHostnames.split(IConstants.COMMA);
		String clickHouseDatabasePort = ClickHouseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_PORT);
		String clickHouseDatabaseName = ClickHouseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_NAME);
		String clickHouseDatabaseUser = ClickHouseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_USER);
		String clickHouseDatabasePassword = ClickHouseConfigurations
				.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_PASSWORD);
		int clickHouseBatchCreationSize = ClickHouseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_BATCH_CREATION_SIZE, 100);
		int clickHouseconnectionTimeoutInMilliseconds = ClickHouseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_CONNECTION_TIMEOUT_MILLISECONDS, 88888);
		int clickHouseMaximumRetryCounts = ClickHouseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_MAX_RETRY_COUNTS, 8);
		int clickHouseRetryWaitMilliseconds = ClickHouseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_RETRY_WAIT_MILLISECONDS, 1000);

		try {
			sharedCountsClickHouseDAO = new SharedCountsClickHouseDAO(clickHouseDatabaseHostnameArray, clickHouseDatabasePort, clickHouseDatabaseName,
					clickHouseBatchCreationSize, clickHouseDatabaseUser, clickHouseDatabasePassword, clickHouseconnectionTimeoutInMilliseconds,
					clickHouseMaximumRetryCounts, clickHouseRetryWaitMilliseconds);
		} catch (Exception e) {
			e.printStackTrace();
		}

		FormatUtils.getInstance().logMemoryUsage("initializeSharedCountsClickHouseDAO() ends. sharedCountsClickHouseDAO=" + sharedCountsClickHouseDAO.toString());
		return sharedCountsClickHouseDAO;
	}

	public static SharedCountsClickHouseDAO getSharedCountsClickHouseDAO() {
		return sharedCountsClickHouseDAO;
	}

}
