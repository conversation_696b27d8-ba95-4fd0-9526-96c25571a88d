package com.actonia.url.metrics.update;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.concurrency.ThreadPoolService;
import com.actonia.dao.OwnDomainEntityDAO;
import com.actonia.dao.OwnDomainSettingEntityDAO;
import com.actonia.dao.TargetURLCriteriaDAO;
import com.actonia.dao.TargetUrlEntityDAO;
import com.actonia.dao.TargetUrlHtmlClickHouseDAO;
import com.actonia.dao.UrlMetricsDataEntityDAO;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.entity.OwnDomainSettingEntity;
import com.actonia.entity.TargetURLCriteria;
import com.actonia.entity.TargetUrlEntity;
import com.actonia.entity.UrlMetricsDataEntity;
import com.actonia.utils.CommonUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.Md5Util;
import com.actonia.utils.PutMessageUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.PageClarityUpdateSummaryValueObject;
import com.actonia.value.object.TargetUrlMetricsUpdateReportValueObject;

public class TargetUrlMetricsUpdate {

	private boolean isDebug = false;
	private static ThreadPoolService threadPool = ThreadPoolService.getInstance();
	private OwnDomainEntityDAO ownDomainEntityDAO;
	private TargetUrlEntityDAO targetUrlEntityDAO;
	private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
	public static int PROCESSING_THRESHOLD = 16800;
	private static int SUBLIST_SIZE = 1000;

	private Date yesterdayDate;
	private Date sevenDaysAgoDate;
	private UrlMetricsDataEntityDAO urlMetricsDataEntityDAO;
	private TargetURLCriteriaDAO targetURLCriteriaDAO;

	public TargetUrlMetricsUpdate() {
		this.ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		this.targetUrlEntityDAO = SpringBeanFactory.getBean("targetUrlEntityDAO");
		this.ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
		this.urlMetricsDataEntityDAO = SpringBeanFactory.getBean("urlMetricsDataEntityDAO");
		this.targetURLCriteriaDAO = SpringBeanFactory.getBean("targetURLCriteriaDAO");
	}

	public static void main(String args[]) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("main() begins.");
		long startTimestamp = System.currentTimeMillis();
		threadPool.init();
		CommonUtils.initThreads(8);
		TargetUrlMetricsUpdate targetUrlMetricsUpdateCommand = new TargetUrlMetricsUpdate();
		targetUrlMetricsUpdateCommand.process(args);
		threadPool.destroy();
		FormatUtils.getInstance().logMemoryUsage("main() ends. elapsed time in sec.:" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private void process(String args[]) throws Exception {

		Boolean isExecDomainIdsInd = null;
		Set<Integer> runtimeDomainSet = null;

		Properties domainProperties = new Properties();
		try {
			domainProperties.load(TargetUrlMetricsUpdate.class.getResourceAsStream("/domain.properties"));
		} catch (Exception e) {
			FormatUtils.getInstance().logMemoryUsage("process() no domain.properties file found");
			System.exit(-1);
		}

		String execDomainIds = domainProperties.getProperty("exec.domain");
		String notExecDomainIds = domainProperties.getProperty("notexec.domain");
		FormatUtils.getInstance().logMemoryUsage("process() execDomainIds=" + execDomainIds);
		FormatUtils.getInstance().logMemoryUsage("process() notExecDomainIds=" + notExecDomainIds);

		List<OwnDomainEntity> ownDomainEntityList = ownDomainEntityDAO.queryForAll();

		// process specific domains
		if (StringUtils.isNotBlank(execDomainIds)) {
			runtimeDomainSet = CommonUtils.getDomainSet(execDomainIds);
			isExecDomainIdsInd = true;
		}
		// do not process specific domains
		else if (StringUtils.isNotBlank(notExecDomainIds)) {
			runtimeDomainSet = CommonUtils.getDomainSet(notExecDomainIds);
			isExecDomainIdsInd = false;
		}
		// process all domains
		else {
			runtimeDomainSet = null;
			isExecDomainIdsInd = null;
		}

		List<OwnDomainEntity> filteredOwnDomainEntityList = PutMessageUtils.getInstance().filterDomains(ownDomainEntityList, isExecDomainIdsInd, runtimeDomainSet);
		if (filteredOwnDomainEntityList != null && filteredOwnDomainEntityList.size() > 0) {
			initialize(filteredOwnDomainEntityList);
			processByDomain(filteredOwnDomainEntityList);
		}

		//TargetUrlMetricsUpdateUtils.sendSummary();
	}

	private void initialize(List<OwnDomainEntity> ownDomainEntityList) throws Exception {
		Date todayDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
		this.yesterdayDate = DateUtils.addDays(todayDate, -1);
		this.sevenDaysAgoDate = DateUtils.addDays(todayDate, -7);

		TargetUrlHtmlClickHouseDAO.getInstance();

		CacheModleFactory.getInstance().setPageClarityUpdateSummaryValueObjectList(new ArrayList<PageClarityUpdateSummaryValueObject>());
	}

	private void processByDomain(List<OwnDomainEntity> ownDomainEntityList) throws Exception {
		List<TargetUrlEntity> targetUrlEntityList = null;
		int domainId = 0;
		String domainName = null;
		String coreName = null;
		OwnDomainSettingEntity ownDomainSettingEntity = null;
		boolean isMobileTrafficEnabled = false;
		String standardLoggingText = IConstants.EMPTY_STRING;
		int totalUrlProcessed = 0;
		Integer targetUrlHtmlDailyDateNumber = null;
		String targetUrlHtmlDailyDateString = null;
		Date targetUrlHtmlDailyDate = null;

		// map key = target URL ID
		// map value = UrlMetricsDataEntity
		Map<Long, UrlMetricsDataEntity> targetUrlIdUrlMetricsDataEntityMap = new HashMap<Long, UrlMetricsDataEntity>();

		// map key = target URL ID
		// map value = TargetURLCriteria
		Map<Long, TargetURLCriteria> targetUrlIdTargetURLCriteriaMap = new HashMap<Long, TargetURLCriteria>();

		int clientDomainStartDayOfWeek = 0;

		nextClientDomain: for (OwnDomainEntity ownDomainEntity : ownDomainEntityList) {
			domainId = ownDomainEntity.getId();
			domainName = ownDomainEntity.getDomain();
			coreName = ownDomainEntity.getLanguage();
			FormatUtils.getInstance().logMemoryUsage("processByDomain() domainId=" + domainId + ",domainName=" + domainName + ",coreName=" + coreName);
			targetUrlEntityList = targetUrlEntityDAO.getManagedTargetUrlEntityList(ownDomainEntity.getId());

			if (targetUrlEntityList != null && targetUrlEntityList.size() > 0) {
				FormatUtils.getInstance().logMemoryUsage(
						"processByDomain() domainId=" + domainId + ",domainName=" + domainName + ",targetUrlEntityList.size()=" + targetUrlEntityList.size());

				// process client domains with total number of target URLs greater than the defined processing threshold
				if (targetUrlEntityList != null && targetUrlEntityList.size() < PROCESSING_THRESHOLD) {
					FormatUtils.getInstance().logMemoryUsage("performMultiThreadedProcessing() skipped domainId=" + domainId + ",domainName=" + domainName
							+ ",targetUrlEntityList.size() < PROCESSING_THRESHOLD");
					continue nextClientDomain;
				}

				// retrieve a map object of all t_url_metrics_data records for the domain
				targetUrlIdUrlMetricsDataEntityMap = urlMetricsDataEntityDAO.getMap(domainId);

				// retrieve a map object of all targeturl_criteria for the domain
				targetUrlIdTargetURLCriteriaMap = targetURLCriteriaDAO.getMap(domainId);

				// archive existing 't_url_metrics_data' data to 't_url_metrics_data_history' data when required
				if (ownDomainEntity.getStartDayOfWeek() != null && ownDomainEntity.getStartDayOfWeek().intValue() > 0) {
					clientDomainStartDayOfWeek = ownDomainEntity.getStartDayOfWeek();
				} else {
					clientDomainStartDayOfWeek = TargetUrlMetricsUpdateUtils.CLIENT_DOMAIN_START_DAY_OF_WEEK_MONDAY;
				}
				TargetUrlMetricsUpdateUtils.archiveWeeklyData(domainId, standardLoggingText, clientDomainStartDayOfWeek, targetUrlIdUrlMetricsDataEntityMap,
						targetUrlIdTargetURLCriteriaMap, targetUrlEntityList);

				// determine if mobile traffic is enabled for the client domain
				ownDomainSettingEntity = ownDomainSettingEntityDAO.getSettingByDomainId(domainId);
				if (ownDomainSettingEntity != null && ownDomainSettingEntity.getEnableMoblie() != null
						&& ownDomainSettingEntity.getEnableMoblie() == OwnDomainSettingEntity.MOBILE_ENABLEED) {
					isMobileTrafficEnabled = true;
				} else {
					isMobileTrafficEnabled = false;
				}

				if (ownDomainSettingEntity != null) {
					if (StringUtils.equalsIgnoreCase(ownDomainSettingEntity.getCompanyName(), IConstants.COMPANY_NAME_HOMEAWAY)) {
						FormatUtils.getInstance().logMemoryUsage("processByDomain() domainId=" + domainId + ",domainName=" + domainName + ", skip companyName="
								+ ownDomainSettingEntity.getCompanyName());
						continue nextClientDomain;
					}
				}

				try {
					targetUrlHtmlDailyDate = null;
					if (ownDomainSettingEntity != null) {
						FormatUtils.getInstance().logMemoryUsage("processByDomain() domainId=" + domainId + ",domainName=" + domainName
								+ ",ownDomainSettingEntity.getTargetUrlHtmlDailyDate()=" + ownDomainSettingEntity.getTargetUrlHtmlDailyDate());
						targetUrlHtmlDailyDateNumber = ownDomainSettingEntity.getTargetUrlHtmlDailyDate();
						if (targetUrlHtmlDailyDateNumber != null && targetUrlHtmlDailyDateNumber.intValue() != 0) {
							targetUrlHtmlDailyDateString = String.valueOf(targetUrlHtmlDailyDateNumber);
							targetUrlHtmlDailyDate = DateUtils.parseDate(targetUrlHtmlDailyDateString, new String[] { IConstants.DATE_FORMAT_YYYYMMDD });
							cacheHashCodeClickHouseEntityMap(domainId, targetUrlHtmlDailyDate);
						}
					}
				} catch (Exception e) {
					e.printStackTrace();
					FormatUtils.getInstance().logMemoryUsage(
							"processByDomain() domainId=" + domainId + ",domainName=" + domainName + ",ownDomainSettingEntity.getTargetUrlHtmlDailyDate()="
									+ ownDomainSettingEntity.getTargetUrlHtmlDailyDate() + ",exception message=" + e.getMessage());
				}

				totalUrlProcessed = performMultiThreadedProcessing(domainId, domainName, coreName, targetUrlEntityList, isMobileTrafficEnabled,
						targetUrlIdUrlMetricsDataEntityMap, targetUrlIdTargetURLCriteriaMap);

				PageClarityUpdateSummaryValueObject pageClarityUpdateSummaryValueObject = new PageClarityUpdateSummaryValueObject();
				pageClarityUpdateSummaryValueObject.setDomainId(domainId);
				pageClarityUpdateSummaryValueObject.setDomainName(domainName);
				pageClarityUpdateSummaryValueObject.setTotalTargetUrls(totalUrlProcessed);
				pageClarityUpdateSummaryValueObject.setLastUpdateTimestamp(new Date());
				CacheModleFactory.getInstance().getPageClarityUpdateSummaryValueObjectList().add(pageClarityUpdateSummaryValueObject);
			} else {
				FormatUtils.getInstance().logMemoryUsage("processByDomain() domainId=" + domainId + ",domainName=" + domainName + ",targetUrlEntityList is empty.");
			}
		}
	}

	private int performMultiThreadedProcessing(int domainId, String domainName, String coreName, List<TargetUrlEntity> targetUrlEntityList,
			boolean isMobileTrafficEnabled, Map<Long, UrlMetricsDataEntity> targetUrlIdUrlMetricsDataEntityMap,
			Map<Long, TargetURLCriteria> targetUrlIdTargetURLCriteriaMap) {

		FormatUtils.getInstance()
				.logMemoryUsage("performMultiThreadedProcessing() begins. domainId=" + domainId + ",domainName=" + domainName + ",coreName=" + coreName
						+ ",targetUrlEntityList.size()=" + targetUrlEntityList.size() + ",isMobileTrafficEnabled=" + isMobileTrafficEnabled
						+ ",targetUrlIdUrlMetricsDataEntityMap.size()=" + targetUrlIdUrlMetricsDataEntityMap.size() + ",targetUrlIdTargetURLCriteriaMap.size()="
						+ targetUrlIdTargetURLCriteriaMap.size());

		int totalNumberOfUrls = targetUrlEntityList.size();
		long startTimestamp = System.currentTimeMillis();
		String ipAddress = null;
		TargetUrlMetricsUpdateCommand targetUrlMetricsUpdateCommand = null;
		int numberOfUrlsProcessed = 0;
		int fromIndex = 0;
		List<TargetUrlEntity> targetUrlEntitySubList = null;

		// map key = target URL ID
		// map value = UrlMetricsDataEntity
		Map<Long, UrlMetricsDataEntity> urlMetricsDataEntitySubMap = null;

		// map key = target URL ID
		// map value = TargetURLCriteria
		Map<Long, TargetURLCriteria> targetURLCriteriaSubMap = null;

		CacheModleFactory.getInstance().setTargetUrlMetricsUpdateReportValueObject(new TargetUrlMetricsUpdateReportValueObject());
		do {
			ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
			if (ipAddress == null) {
				continue;
			}
			numberOfUrlsProcessed = fromIndex + SUBLIST_SIZE;
			if (numberOfUrlsProcessed > totalNumberOfUrls) {
				numberOfUrlsProcessed = totalNumberOfUrls;
			}
			targetUrlEntitySubList = targetUrlEntityList.subList(fromIndex, numberOfUrlsProcessed);
			fromIndex = numberOfUrlsProcessed;
			if (isDebug == true) {
				for (int i = 0; i < targetUrlEntitySubList.size(); i++) {
					FormatUtils.getInstance().logMemoryUsage("performMultiThreadedProcessing() fromIndex=" + fromIndex + ",numberOfUrlsProcessed="
							+ numberOfUrlsProcessed + ",sub-list URL=" + targetUrlEntitySubList.get(i).getUrl());
				}
			}

			urlMetricsDataEntitySubMap = getUrlMetricsDataEntitySubMap(targetUrlEntitySubList, targetUrlIdUrlMetricsDataEntityMap);
			targetURLCriteriaSubMap = getTargetURLCriteriaSubMap(targetUrlEntitySubList, targetUrlIdTargetURLCriteriaMap);

			targetUrlMetricsUpdateCommand = getTargetUrlMetricsUpdateCommand(ipAddress, domainId, domainName, coreName, targetUrlEntitySubList, isMobileTrafficEnabled,
					urlMetricsDataEntitySubMap, targetURLCriteriaSubMap);
			if (targetUrlMetricsUpdateCommand != null) {
				try {
					threadPool.execute(targetUrlMetricsUpdateCommand);
				} catch (Exception e) {
					e.printStackTrace();
				}
			} else {
				CacheModleFactory.getInstance().setAliveIpAddress(ipAddress);
			}
			if (isDebug == true) {
				if (numberOfUrlsProcessed % 10 == 0) {
					FormatUtils.getInstance().logMemoryUsage("performMultiThreadedProcessing() domainId=" + domainId + ",domainName=" + domainName
							+ ",totalNumberOfUrls=" + totalNumberOfUrls + ",numberOfUrlsProcessed=" + numberOfUrlsProcessed);
				}
			} else {
				if (numberOfUrlsProcessed % 1000 == 0) {
					FormatUtils.getInstance().logMemoryUsage("performMultiThreadedProcessing() domainId=" + domainId + ",domainName=" + domainName
							+ ",totalNumberOfUrls=" + totalNumberOfUrls + ",numberOfUrlsProcessed=" + numberOfUrlsProcessed);
				}
			}
		} while (numberOfUrlsProcessed < totalNumberOfUrls);

		do {
			try {
				Thread.sleep(1000);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		} while (threadPool.getThreadPool().getActiveCount() > 0);
		TargetUrlMetricsUpdateReportValueObject targetUrlMetricsUpdateReportValueObject = CacheModleFactory.getInstance().getTargetUrlMetricsUpdateReportValueObject();
		FormatUtils.getInstance()
				.logMemoryUsage("performMultiThreadedProcessing() ends. domainId=" + domainId + ",domainName=" + domainName + ",totalTargetUrlsRetrieved="
						+ targetUrlMetricsUpdateReportValueObject.getTotalTargetUrlsRetrieved() + ",totalTargetUrlsProcessed="
						+ targetUrlMetricsUpdateReportValueObject.getTotalTargetUrlsProcessed() + ",totalTargetUrlsFoundInDatabase="
						+ targetUrlMetricsUpdateReportValueObject.getTotalTargetUrlsFoundInDatabase() + ",totalTargetUrlsNotFoundInDatabase="
						+ targetUrlMetricsUpdateReportValueObject.getTotalTargetUrlsNotFoundInDatabase() + ",totalInternalLinkCountCalculated="
						+ targetUrlMetricsUpdateReportValueObject.getTotalInternalLinkCountCalculated() + ",totalUrlMetricsDataEntityFound="
						+ targetUrlMetricsUpdateReportValueObject.getTotalUrlMetricsDataEntityFound() + ",totalUrlMetricsDataEntityUpdated="
						+ targetUrlMetricsUpdateReportValueObject.getTotalUrlMetricsDataEntityUpdated() + ",totalUrlMetricsDataEntityNotUpdated="
						+ targetUrlMetricsUpdateReportValueObject.getTotalUrlMetricsDataEntityNotUpdated() + ",totalUrlMetricsDataEntityNotFound="
						+ targetUrlMetricsUpdateReportValueObject.getTotalUrlMetricsDataEntityNotFound() + ",totalUrlMetricsDataEntityCreated="
						+ targetUrlMetricsUpdateReportValueObject.getTotalUrlMetricsDataEntityCreated() + ",totalTargetUrlCriteriaFound="
						+ targetUrlMetricsUpdateReportValueObject.getTotalTargetUrlCriteriaFound() + ",totalTargetUrlCriteriaUpdated="
						+ targetUrlMetricsUpdateReportValueObject.getTotalTargetUrlCriteriaUpdated() + ",totalTargetUrlCriteriaNotUpdated="
						+ targetUrlMetricsUpdateReportValueObject.getTotalTargetUrlCriteriaNotUpdated() + ",totalTargetUrlCriteriaNotFound="
						+ targetUrlMetricsUpdateReportValueObject.getTotalTargetUrlCriteriaNotFound() + ",totalTargetUrlCriteriaCreated="
						+ targetUrlMetricsUpdateReportValueObject.getTotalTargetUrlCriteriaCreated() + ",elapsed(s.)="
						+ ((System.currentTimeMillis() - startTimestamp) / 1000));
		return numberOfUrlsProcessed;
	}

	private Map<Long, UrlMetricsDataEntity> getUrlMetricsDataEntitySubMap(List<TargetUrlEntity> targetUrlEntitySubList,
			Map<Long, UrlMetricsDataEntity> targetUrlIdUrlMetricsDataEntityMap) {
		Map<Long, UrlMetricsDataEntity> urlMetricsDataEntitySubMap = new HashMap<Long, UrlMetricsDataEntity>();
		Long targetUrlId = 0L;
		for (TargetUrlEntity targetUrlEntity : targetUrlEntitySubList) {
			targetUrlId = targetUrlEntity.getId();
			if (targetUrlIdUrlMetricsDataEntityMap.containsKey(targetUrlId)) {
				urlMetricsDataEntitySubMap.put(targetUrlId, targetUrlIdUrlMetricsDataEntityMap.get(targetUrlId));
			}
		}
		return urlMetricsDataEntitySubMap;
	}

	private Map<Long, TargetURLCriteria> getTargetURLCriteriaSubMap(List<TargetUrlEntity> targetUrlEntitySubList,
			Map<Long, TargetURLCriteria> targetUrlIdTargetURLCriteriaMap) {
		Map<Long, TargetURLCriteria> targetURLCriteriaSubMap = new HashMap<Long, TargetURLCriteria>();
		Long targetUrlId = 0L;
		for (TargetUrlEntity targetUrlEntity : targetUrlEntitySubList) {
			targetUrlId = targetUrlEntity.getId();
			if (targetUrlIdTargetURLCriteriaMap.containsKey(targetUrlId)) {
				targetURLCriteriaSubMap.put(targetUrlId, targetUrlIdTargetURLCriteriaMap.get(targetUrlId));
			}
		}
		return targetURLCriteriaSubMap;
	}

	private TargetUrlMetricsUpdateCommand getTargetUrlMetricsUpdateCommand(String ip, int domainId, String domainName, String coreName,
			List<TargetUrlEntity> targetUrlEntitySubList, boolean isMobileTrafficEnabled, Map<Long, UrlMetricsDataEntity> urlMetricsDataEntitySubMap,
			Map<Long, TargetURLCriteria> targetURLCriteriaSubMap) {
		TargetUrlMetricsUpdateCommand targetUrlMetricsUpdateCommand = new TargetUrlMetricsUpdateCommand(ip, domainId, domainName, coreName, targetUrlEntitySubList,
				isMobileTrafficEnabled, yesterdayDate, sevenDaysAgoDate, urlMetricsDataEntitySubMap, targetURLCriteriaSubMap);
		targetUrlMetricsUpdateCommand.setStatus(true);
		return targetUrlMetricsUpdateCommand;
	}

	private void cacheHashCodeClickHouseEntityMap(int domainId, Date targetUrlHtmlDailyDate) throws Exception {
		Map<String, HtmlClickHouseEntity> outputMap = new HashMap<String, HtmlClickHouseEntity>();
		String hashCode = null;
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = null;
		List<String> databaseFields = new ArrayList<String>();
		databaseFields.add(IConstants.URL);
		databaseFields.add(IConstants.TRACK_DATE);
		databaseFields.add(IConstants.RESPONSE_CODE);
		databaseFields.add(IConstants.TITLE);
		databaseFields.add(IConstants.OUTLINK_COUNT);
		databaseFields.add(IConstants.INTERNAL_LINK_COUNT);
		databaseFields.add(IConstants.CRAWL_TIMESTAMP);
		htmlClickHouseEntityList = TargetUrlHtmlClickHouseDAO.getInstance().getDailyData(targetUrlHtmlDailyDate, domainId, null, databaseFields,
				IConstants.TABLE_NAME_TARGET_URL_HTML_DAILY);
		if (htmlClickHouseEntityList != null && htmlClickHouseEntityList.size() > 0) {
			for (HtmlClickHouseEntity htmlClickHouseEntity : htmlClickHouseEntityList) {
				hashCode = Md5Util.Md5(htmlClickHouseEntity.getUrl());
				outputMap.put(hashCode, htmlClickHouseEntity);
			}
		}
		CacheModleFactory.getInstance().setUrlHashCodeHtmlClickHouseEntityMap(outputMap);
		FormatUtils.getInstance().logMemoryUsage("cacheHashCodeClickHouseEntityMap() domainId=" + domainId + ",outputMap.size()=" + outputMap.size());
	}
}
