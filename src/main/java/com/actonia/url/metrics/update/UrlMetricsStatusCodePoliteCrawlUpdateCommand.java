package com.actonia.url.metrics.update;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.concurrency.BaseThreadCommand;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.dao.KeywordTargeturlEntityDAO;
import com.actonia.dao.TargetURLCriteriaDAO;
import com.actonia.dao.TargetUrlEntityDAO;
import com.actonia.dao.TargetUrlHtmlClickHouseDAO;
import com.actonia.dao.UrlMetricsDataEntityDAO;
import com.actonia.dao.UserBacklinkDAO;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.entity.TargetURLCriteria;
import com.actonia.entity.TargetUrlEntity;
import com.actonia.entity.UrlMetricsDataEntity;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.Md5Util;
import com.actonia.utils.PutMessageUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.PageClarityUpdateSummaryValueObject;
import com.actonia.value.object.UrlMetricsUpdateValueObject;

public class UrlMetricsStatusCodePoliteCrawlUpdateCommand extends BaseThreadCommand {

	private boolean isDebug = false;

	private String ip;
	private TargetUrlEntityDAO targetUrlEntityDAO;
	private UrlMetricsDataEntityDAO urlMetricsDataEntityDAO;
	private TargetURLCriteriaDAO targetURLCriteriaDAO;
	private int domainId;
	private String domainName;
	public String standardLoggingText;

	private int totalTargetUrlsRetrieved = 0;
	private int totalTargetUrlsProcessed = 0;
	private int totalTargetUrlsFoundInDatabase = 0;
	private int totalTargetUrlsNotFoundInDatabase = 0;
	private int totalInternalLinkCountCalculated = 0;

	private Date todayDate;

	private boolean isMobileTrafficEnabled;
	private Date yesterdayDate;
	private Date sevenDaysAgoDate;
	private KeywordTargeturlEntityDAO keywordTargeturlEntityDAO;
	private UserBacklinkDAO userBacklinkDAO;
	private int clientDomainStartDayOfWeek = 0;
	private Date targetUrlHtmlDailyDate;

	public UrlMetricsStatusCodePoliteCrawlUpdateCommand(String ip, OwnDomainEntity ownDomainEntity, boolean isMobileTrafficEnabled, Date yesterdayDate,
			Date sevenDaysAgoDate, Date targetUrlHtmlDailyDate) {
		super();
		this.ip = ip;
		this.domainId = ownDomainEntity.getId();
		this.domainName = ownDomainEntity.getDomain();
		this.targetUrlEntityDAO = SpringBeanFactory.getBean("targetUrlEntityDAO");
		this.urlMetricsDataEntityDAO = SpringBeanFactory.getBean("urlMetricsDataEntityDAO");
		this.targetURLCriteriaDAO = SpringBeanFactory.getBean("targetURLCriteriaDAO");
		this.standardLoggingText = "ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName;
		this.todayDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
		this.isMobileTrafficEnabled = isMobileTrafficEnabled;
		this.yesterdayDate = yesterdayDate;
		this.sevenDaysAgoDate = sevenDaysAgoDate;
		this.keywordTargeturlEntityDAO = SpringBeanFactory.getBean("keywordTargeturlEntityDAO");
		this.userBacklinkDAO = SpringBeanFactory.getBean("userBacklinkDAO");
		if (ownDomainEntity.getStartDayOfWeek() != null && ownDomainEntity.getStartDayOfWeek().intValue() > 0) {
			this.clientDomainStartDayOfWeek = ownDomainEntity.getStartDayOfWeek();
		} else {
			this.clientDomainStartDayOfWeek = TargetUrlMetricsUpdateUtils.CLIENT_DOMAIN_START_DAY_OF_WEEK_MONDAY;
		}
		this.targetUrlHtmlDailyDate = targetUrlHtmlDailyDate;
	}

	@Override
	protected void execute() throws Exception {
		try {
			process();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			CacheModleFactory.getInstance().setAliveIpAddress(ip);
		}
	}

	private void process() throws Exception {
		FormatUtils.getInstance().logMemoryUsage("process() begins. " + standardLoggingText + ",isMobileTrafficEnabled=" + isMobileTrafficEnabled);
		long startTimestamp = System.currentTimeMillis();
		String hashCode = null;
		HtmlClickHouseEntity htmlClickHouseEntity = null;
		HtmlClickHouseEntity htmlClickHouseEntityPageLink = null;
		String targetUrlString = null;
		UrlMetricsUpdateValueObject urlMetricsUpdateValueObject = null;
		UrlMetricsDataEntity urlMetricsDataEntity = null;
		TargetURLCriteria targetURLCriteria = null;
		String encodedTargetUrlString = null;
		Integer internalLinkCount = null;

		TargetUrlMetricsUpdateLogic targetUrlMetricsUpdateLogic = new TargetUrlMetricsUpdateLogic(standardLoggingText, urlMetricsDataEntityDAO, todayDate,
				targetURLCriteriaDAO, domainId, isMobileTrafficEnabled, yesterdayDate, sevenDaysAgoDate, keywordTargeturlEntityDAO, userBacklinkDAO);
		Long targetUrlId = 0L;
		List<TargetUrlEntity> targetUrlEntityList = targetUrlEntityDAO.getManagedTargetUrlEntityList(domainId);

		// map key = target URL ID
		// map value = UrlMetricsDataEntity
		Map<Long, UrlMetricsDataEntity> targetUrlIdUrlMetricsDataEntityMap = new HashMap<Long, UrlMetricsDataEntity>();

		// map key = target URL ID
		// map value = TargetURLCriteria
		Map<Long, TargetURLCriteria> targetUrlIdTargetURLCriteriaMap = new HashMap<Long, TargetURLCriteria>();

		// map key = URL hash code
		// map value = HtmlClickHouseEntity
		Map<String, HtmlClickHouseEntity> urlHashCodeHtmlClickHouseEntityMap = null;

		if (targetUrlEntityList != null && targetUrlEntityList.size() > 0) {

			urlHashCodeHtmlClickHouseEntityMap = getHashCodeClickHouseEntityMap();

			totalTargetUrlsRetrieved = targetUrlEntityList.size();

			//FormatUtils.getInstance().logMemoryUsage("process() begins. " + standardLoggingText + ",total target URLs=" + targetUrlEntityList.size()
			//		+ ",total hash codes that each maps to more than one target URLs=" + hashCodesThatMapToMoreThanOneTargetUrls.size());

			// retrieve a map object of all t_url_metrics_data records for the domain
			targetUrlIdUrlMetricsDataEntityMap = urlMetricsDataEntityDAO.getMap(domainId);

			// retrieve a map object of all targeturl_criteria for the domain
			targetUrlIdTargetURLCriteriaMap = targetURLCriteriaDAO.getMap(domainId);

			// when it is the start day of week of client domain, archive 't_url_metrics_data' and 'targeturl_criteria' data to IEE historical data table
			TargetUrlMetricsUpdateUtils.archiveWeeklyData(domainId, standardLoggingText, clientDomainStartDayOfWeek, targetUrlIdUrlMetricsDataEntityMap,
					targetUrlIdTargetURLCriteriaMap, targetUrlEntityList);

			for (TargetUrlEntity targetUrlEntity : targetUrlEntityList) {

				totalTargetUrlsProcessed++;

				urlMetricsUpdateValueObject = new UrlMetricsUpdateValueObject();
				targetUrlId = targetUrlEntity.getId();
				targetUrlString = StringUtils.trimToEmpty(targetUrlEntity.getUrl());
				hashCode = Md5Util.Md5(targetUrlString);

				// retrieve the t_url_metrics_data record for the target URL
				if (targetUrlIdUrlMetricsDataEntityMap.containsKey(targetUrlId)) {
					urlMetricsDataEntity = targetUrlIdUrlMetricsDataEntityMap.get(targetUrlId);
				} else {
					urlMetricsDataEntity = null;
				}

				// retrieve the targeturl_criteria record for the target URL
				if (targetUrlIdTargetURLCriteriaMap.containsKey(targetUrlId)) {
					targetURLCriteria = targetUrlIdTargetURLCriteriaMap.get(targetUrlId);
				} else {
					targetURLCriteria = null;
				}

				if (isDebug == true) {
					FormatUtils.getInstance()
							.logMemoryUsage("process() " + standardLoggingText + ",targetUrlId=" + targetUrlId + ",targetUrlString=" + targetUrlString);
				}

				if (totalTargetUrlsProcessed % 1000 == 0) {
					FormatUtils.getInstance().logMemoryUsage("process() " + standardLoggingText + ",totalTargetUrlsRetrieved=" + totalTargetUrlsRetrieved
							+ ",totalTargetUrlsProcessed=" + totalTargetUrlsProcessed);
				}

				try {

					encodedTargetUrlString = targetUrlMetricsUpdateLogic.getEncodedUrlString(targetUrlString);

					if (urlHashCodeHtmlClickHouseEntityMap.containsKey(hashCode) == true) {
						htmlClickHouseEntity = urlHashCodeHtmlClickHouseEntityMap.get(hashCode);
						if (StringUtils.equalsIgnoreCase(htmlClickHouseEntity.getCrawlerResponse().getResponse_code(), IConstants.RESPONSE_CODE_200)
								&& htmlClickHouseEntity.getInternalLinkCount() == null) {
							htmlClickHouseEntityPageLink = TargetUrlHtmlClickHouseDAO.getInstance()
									.getPageLinkByCrawlTimestamp(htmlClickHouseEntity.getCrawlTimestamp(), domainId, htmlClickHouseEntity.getUrl(), null);
							if (htmlClickHouseEntityPageLink != null) {
								internalLinkCount = PutMessageUtils.getInstance().getInternalLinkCount(htmlClickHouseEntity.getUrl(),
										htmlClickHouseEntityPageLink.getCrawlerResponse());
								htmlClickHouseEntity.setInternalLinkCount(internalLinkCount);
								totalInternalLinkCountCalculated++;
								//FormatUtils.getInstance().logMemoryUsage("process() domainId=" + domainId + ",domainName=" + domainName + ",url="
								//		+ htmlClickHouseEntity.getUrl() + ",internalLinkCount=" + internalLinkCount);
							}
						}
						urlMetricsUpdateValueObject = targetUrlMetricsUpdateLogic.getUrlMetricsUpdateValueObject(htmlClickHouseEntity,
								targetUrlEntity.getWeekEntrances());

						if (isDebug == true) {
							FormatUtils.getInstance()
									.logMemoryUsage("process() " + standardLoggingText + ",urlMetricsUpdateValueObject=" + urlMetricsUpdateValueObject.toString());
						}

						totalTargetUrlsFoundInDatabase++;
					} else {
						FormatUtils.getInstance().logMemoryUsage(
								"process() " + standardLoggingText + ",targetUrlString=" + targetUrlString + ",hashCode=" + hashCode + ",not found in clarityDB.");
						urlMetricsUpdateValueObject = new UrlMetricsUpdateValueObject();
						urlMetricsUpdateValueObject.setPoliteCrawlResponseCode(0);
						totalTargetUrlsNotFoundInDatabase++;
					}

					targetUrlMetricsUpdateLogic.maintainUrlMetricsData(targetUrlId, encodedTargetUrlString, urlMetricsUpdateValueObject,
							targetUrlEntity.getWeekEntrances(), urlMetricsDataEntity);

					targetUrlMetricsUpdateLogic.maintainTargetCriteriaData(targetUrlId, encodedTargetUrlString, urlMetricsUpdateValueObject, targetURLCriteria);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}

			targetUrlMetricsUpdateLogic.maintainUrlMetricsData();

			targetUrlMetricsUpdateLogic.maintainTargetCriteriaData();

		} else {
			FormatUtils.getInstance().logMemoryUsage("process() " + standardLoggingText + ",total number of target URLs=0.");
		}

		FormatUtils.getInstance()
				.logMemoryUsage("process() ends. " + standardLoggingText + ",totalTargetUrlsRetrieved=" + totalTargetUrlsRetrieved + ",totalTargetUrlsProcessed="
						+ totalTargetUrlsProcessed + ",totalTargetUrlsFoundInDatabase=" + totalTargetUrlsFoundInDatabase + ",totalTargetUrlsNotFoundInDatabase="
						+ totalTargetUrlsNotFoundInDatabase + ",totalInternalLinkCountCalculated=" + totalInternalLinkCountCalculated
						+ ",totalUrlMetricsDataEntityFound=" + targetUrlMetricsUpdateLogic.getTotalUrlMetricsDataEntityFound() + ",totalUrlMetricsDataEntityUpdated="
						+ targetUrlMetricsUpdateLogic.getTotalUrlMetricsDataEntityUpdated() + ",totalUrlMetricsDataEntityNotUpdated="
						+ targetUrlMetricsUpdateLogic.getTotalUrlMetricsDataEntityNotUpdated() + ",totalUrlMetricsDataEntityNotFound="
						+ targetUrlMetricsUpdateLogic.getTotalUrlMetricsDataEntityNotFound() + ",totalUrlMetricsDataEntityCreated="
						+ targetUrlMetricsUpdateLogic.getTotalUrlMetricsDataEntityCreated() + ",totalTargetUrlCriteriaFound="
						+ targetUrlMetricsUpdateLogic.getTotalTargetUrlCriteriaFound() + ",totalTargetUrlCriteriaUpdated="
						+ targetUrlMetricsUpdateLogic.getTotalTargetUrlCriteriaUpdated() + ",totalTargetUrlCriteriaNotUpdated="
						+ targetUrlMetricsUpdateLogic.getTotalTargetUrlCriteriaNotUpdated() + ",totalTargetUrlCriteriaNotFound="
						+ targetUrlMetricsUpdateLogic.getTotalTargetUrlCriteriaNotFound() + ",totalTargetUrlCriteriaCreated="
						+ targetUrlMetricsUpdateLogic.getTotalTargetUrlCriteriaCreated() + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		PageClarityUpdateSummaryValueObject pageClarityUpdateSummaryValueObject = new PageClarityUpdateSummaryValueObject();
		pageClarityUpdateSummaryValueObject.setDomainId(domainId);
		pageClarityUpdateSummaryValueObject.setDomainName(domainName);
		pageClarityUpdateSummaryValueObject.setTotalTargetUrls(totalTargetUrlsProcessed);
		pageClarityUpdateSummaryValueObject.setLastUpdateTimestamp(new Date());
		CacheModleFactory.getInstance().getPageClarityUpdateSummaryValueObjectList().add(pageClarityUpdateSummaryValueObject);
	}

	private Map<String, HtmlClickHouseEntity> getHashCodeClickHouseEntityMap() throws Exception {
		Map<String, HtmlClickHouseEntity> outputMap = new HashMap<String, HtmlClickHouseEntity>();
		String hashCode = null;
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = null;
		List<String> databaseFields = new ArrayList<String>();
		databaseFields.add(IConstants.URL);
		databaseFields.add(IConstants.TRACK_DATE);
		databaseFields.add(IConstants.RESPONSE_CODE);
		databaseFields.add(IConstants.TITLE);
		databaseFields.add(IConstants.OUTLINK_COUNT);
		databaseFields.add(IConstants.INTERNAL_LINK_COUNT);
		databaseFields.add(IConstants.CRAWL_TIMESTAMP);
		htmlClickHouseEntityList = TargetUrlHtmlClickHouseDAO.getInstance().getDailyData(targetUrlHtmlDailyDate, domainId, null, databaseFields,
				IConstants.TABLE_NAME_TARGET_URL_HTML_DAILY);
		if (htmlClickHouseEntityList != null && htmlClickHouseEntityList.size() > 0) {
			for (HtmlClickHouseEntity htmlClickHouseEntity : htmlClickHouseEntityList) {
				hashCode = Md5Util.Md5(htmlClickHouseEntity.getUrl());
				outputMap.put(hashCode, htmlClickHouseEntity);
			}
		}
		FormatUtils.getInstance().logMemoryUsage("getHashCodeClickHouseEntityMap() " + standardLoggingText + ",outputMap.size()=" + outputMap.size());
		return outputMap;
	}

	@Override
	protected void undo() throws Exception {
		// TODO Auto-generated method stub

	}

}
