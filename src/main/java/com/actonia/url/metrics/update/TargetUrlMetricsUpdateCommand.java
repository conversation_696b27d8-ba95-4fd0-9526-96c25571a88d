package com.actonia.url.metrics.update;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.concurrency.BaseThreadCommand;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.dao.KeywordTargeturlEntityDAO;
import com.actonia.dao.TargetURLCriteriaDAO;
import com.actonia.dao.TargetUrlHtmlClickHouseDAO;
import com.actonia.dao.UrlMetricsDataEntityDAO;
import com.actonia.dao.UserBacklinkDAO;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.TargetURLCriteria;
import com.actonia.entity.TargetUrlEntity;
import com.actonia.entity.UrlMetricsDataEntity;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.Md5Util;
import com.actonia.utils.PutMessageUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.UrlMetricsUpdateValueObject;

public class TargetUrlMetricsUpdateCommand extends BaseThreadCommand {

	private boolean isDebug = false;

	private String ip;
	private UrlMetricsDataEntityDAO urlMetricsDataEntityDAO;
	private TargetURLCriteriaDAO targetURLCriteriaDAO;
	private int domainId;
	private String standardLoggingText;

	private int totalTargetUrlsRetrieved = 0;
	private int totalTargetUrlsProcessed = 0;
	private int totalTargetUrlsFoundInDatabase = 0;
	private int totalTargetUrlsNotFoundInDatabase = 0;

	private String coreName;
	private Date todayDate;

	private List<TargetUrlEntity> targetUrlEntityList;

	private boolean isMobileTrafficEnabled;
	private Date yesterdayDate;
	private Date sevenDaysAgoDate;
	private KeywordTargeturlEntityDAO keywordTargeturlEntityDAO;
	private UserBacklinkDAO userBacklinkDAO;
	private Map<Long, UrlMetricsDataEntity> urlMetricsDataEntitySubMap;
	private Map<Long, TargetURLCriteria> targetURLCriteriaSubMap;

	public TargetUrlMetricsUpdateCommand(String ip, int domainId, String domainName, String coreName, List<TargetUrlEntity> targetUrlEntityList,
			boolean isMobileTrafficEnabled, Date yesterdayDate, Date sevenDaysAgoDate, Map<Long, UrlMetricsDataEntity> urlMetricsDataEntitySubMap,
			Map<Long, TargetURLCriteria> targetURLCriteriaSubMap) {
		super();
		this.ip = ip;
		this.domainId = domainId;
		this.urlMetricsDataEntityDAO = SpringBeanFactory.getBean("urlMetricsDataEntityDAO");
		this.targetURLCriteriaDAO = SpringBeanFactory.getBean("targetURLCriteriaDAO");
		this.standardLoggingText = "ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName;
		this.coreName = coreName;
		this.todayDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
		this.targetUrlEntityList = targetUrlEntityList;
		this.isMobileTrafficEnabled = isMobileTrafficEnabled;
		this.yesterdayDate = yesterdayDate;
		this.sevenDaysAgoDate = sevenDaysAgoDate;
		this.keywordTargeturlEntityDAO = SpringBeanFactory.getBean("keywordTargeturlEntityDAO");
		this.userBacklinkDAO = SpringBeanFactory.getBean("userBacklinkDAO");
		this.urlMetricsDataEntitySubMap = urlMetricsDataEntitySubMap;
		this.targetURLCriteriaSubMap = targetURLCriteriaSubMap;
	}

	@Override
	protected void execute() throws Exception {
		if (isDebug == true) {
			FormatUtils.getInstance().logMemoryUsage("execute() begins. " + standardLoggingText);
		}
		try {
			process();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			CacheModleFactory.getInstance().setAliveIpAddress(ip);
		}
	}

	private void process() {

		String hashCode = null;
		String targetUrlString = null;
		UrlMetricsUpdateValueObject urlMetricsUpdateValueObject = null;
		String encodedTargetUrlString = null;
		HtmlClickHouseEntity htmlClickHouseEntity = null;
		HtmlClickHouseEntity htmlClickHouseEntityPageLink = null;
		Integer internalLinkCount = null;
		Long targetUrlId = 0L;
		int totalInternalLinkCountCalculated = 0;

		TargetUrlMetricsUpdateLogic targetUrlMetricsUpdateLogic = new TargetUrlMetricsUpdateLogic(standardLoggingText, urlMetricsDataEntityDAO, todayDate,
				targetURLCriteriaDAO, domainId, isMobileTrafficEnabled, yesterdayDate, sevenDaysAgoDate, keywordTargeturlEntityDAO, userBacklinkDAO);

		UrlMetricsDataEntity urlMetricsDataEntity = null;

		TargetURLCriteria targetURLCriteria = null;

		if (targetUrlEntityList != null && targetUrlEntityList.size() > 0) {

			totalTargetUrlsRetrieved = targetUrlEntityList.size();

			if (isDebug == true) {
				FormatUtils.getInstance().logMemoryUsage("process() begins. " + standardLoggingText + ",total target URLs=" + targetUrlEntityList.size());
			}

			for (TargetUrlEntity targetUrlEntity : targetUrlEntityList) {

				totalTargetUrlsProcessed++;

				urlMetricsUpdateValueObject = new UrlMetricsUpdateValueObject();
				targetUrlId = targetUrlEntity.getId();
				targetUrlString = StringUtils.trimToEmpty(targetUrlEntity.getUrl());
				hashCode = Md5Util.Md5(targetUrlString);

				// retrieve the t_url_metrics_data record for the target URL
				if (urlMetricsDataEntitySubMap.containsKey(targetUrlId)) {
					urlMetricsDataEntity = urlMetricsDataEntitySubMap.get(targetUrlId);
				} else {
					urlMetricsDataEntity = null;
				}

				// retrieve the targeturl_criteria record for the target URL
				if (targetURLCriteriaSubMap.containsKey(targetUrlId)) {
					targetURLCriteria = targetURLCriteriaSubMap.get(targetUrlId);
				} else {
					targetURLCriteria = null;
				}

				if (isDebug == true) {
					FormatUtils.getInstance()
							.logMemoryUsage("process() " + standardLoggingText + ",targetUrlId=" + targetUrlId + ",targetUrlString=" + targetUrlString);
				}

				if (isDebug == true) {
					if (totalTargetUrlsProcessed % 1000 == 0) {
						FormatUtils.getInstance().logMemoryUsage("process() " + standardLoggingText + ",totalTargetUrlsRetrieved=" + totalTargetUrlsRetrieved
								+ ",totalTargetUrlsProcessed=" + totalTargetUrlsProcessed);
					}
				}

				try {

					encodedTargetUrlString = targetUrlMetricsUpdateLogic.getEncodedUrlString(targetUrlString);

					if (CacheModleFactory.getInstance().getUrlHashCodeHtmlClickHouseEntityMap().containsKey(hashCode) == true) {
						htmlClickHouseEntity = CacheModleFactory.getInstance().getUrlHashCodeHtmlClickHouseEntityMap().get(hashCode);
						if (StringUtils.equalsIgnoreCase(htmlClickHouseEntity.getCrawlerResponse().getResponse_code(), IConstants.RESPONSE_CODE_200)
								&& htmlClickHouseEntity.getInternalLinkCount() == null) {
							htmlClickHouseEntityPageLink = TargetUrlHtmlClickHouseDAO.getInstance()
									.getPageLinkByCrawlTimestamp(htmlClickHouseEntity.getCrawlTimestamp(), domainId, htmlClickHouseEntity.getUrl(), null);
							if (htmlClickHouseEntityPageLink != null) {
								internalLinkCount = PutMessageUtils.getInstance().getInternalLinkCount(htmlClickHouseEntity.getUrl(),
										htmlClickHouseEntityPageLink.getCrawlerResponse());
								htmlClickHouseEntity.setInternalLinkCount(internalLinkCount);
								totalInternalLinkCountCalculated++;
								//FormatUtils.getInstance().logMemoryUsage("process() domainId=" + domainId + ",domainName=" + domainName + ",url="
								//		+ htmlClickHouseEntity.getUrl() + ",internalLinkCount=" + internalLinkCount);
							}
						}
						urlMetricsUpdateValueObject = targetUrlMetricsUpdateLogic.getUrlMetricsUpdateValueObject(htmlClickHouseEntity,
								targetUrlEntity.getWeekEntrances());

						if (isDebug == true) {
							FormatUtils.getInstance()
									.logMemoryUsage("process() " + standardLoggingText + ",urlMetricsUpdateValueObject=" + urlMetricsUpdateValueObject.toString());
						}

						totalTargetUrlsFoundInDatabase++;
					} else {
						FormatUtils.getInstance().logMemoryUsage(
								"process() " + standardLoggingText + ",targetUrlString=" + targetUrlString + ",hashCode=" + hashCode + ",not found in clarityDB.");
						urlMetricsUpdateValueObject = new UrlMetricsUpdateValueObject();
						urlMetricsUpdateValueObject.setPoliteCrawlResponseCode(0);
						totalTargetUrlsNotFoundInDatabase++;
					}

					targetUrlMetricsUpdateLogic.maintainUrlMetricsData(targetUrlId, encodedTargetUrlString, urlMetricsUpdateValueObject,
							targetUrlEntity.getWeekEntrances(), urlMetricsDataEntity);

					targetUrlMetricsUpdateLogic.maintainTargetCriteriaData(targetUrlId, encodedTargetUrlString, urlMetricsUpdateValueObject, targetURLCriteria);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}

			targetUrlMetricsUpdateLogic.maintainUrlMetricsData();

			targetUrlMetricsUpdateLogic.maintainTargetCriteriaData();

		} else {
			if (isDebug == true) {
				FormatUtils.getInstance().logMemoryUsage("process() " + standardLoggingText + ",total number of target URLs=0.");
			}
		}
		CacheModleFactory.getInstance().updateTargetUrlMetricsUpdateReportValueObject(totalTargetUrlsRetrieved, totalTargetUrlsProcessed,
				totalTargetUrlsFoundInDatabase, totalTargetUrlsNotFoundInDatabase, targetUrlMetricsUpdateLogic.getTotalUrlMetricsDataEntityFound(),
				targetUrlMetricsUpdateLogic.getTotalUrlMetricsDataEntityUpdated(), targetUrlMetricsUpdateLogic.getTotalUrlMetricsDataEntityNotUpdated(),
				targetUrlMetricsUpdateLogic.getTotalUrlMetricsDataEntityNotFound(), targetUrlMetricsUpdateLogic.getTotalUrlMetricsDataEntityCreated(),
				targetUrlMetricsUpdateLogic.getTotalTargetUrlCriteriaFound(), targetUrlMetricsUpdateLogic.getTotalTargetUrlCriteriaUpdated(),
				targetUrlMetricsUpdateLogic.getTotalTargetUrlCriteriaNotUpdated(), targetUrlMetricsUpdateLogic.getTotalTargetUrlCriteriaNotFound(),
				targetUrlMetricsUpdateLogic.getTotalTargetUrlCriteriaCreated(), totalInternalLinkCountCalculated);
	}

	@Override
	protected void undo() throws Exception {
		// TODO Auto-generated method stub

	}
}
