package com.actonia.url.metrics.update;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.concurrency.ThreadPoolService;
import com.actonia.dao.OwnDomainEntityDAO;
import com.actonia.dao.OwnDomainSettingEntityDAO;
import com.actonia.dao.PageClarityTrackingEntityDAO;
import com.actonia.dao.TargetURLCriteriaDAO;
import com.actonia.dao.TargetUrlEntityDAO;
import com.actonia.dao.TargetUrlHtmlClickHouseDAO;
import com.actonia.dao.UrlMetricsDataEntityDAO;
import com.actonia.dao.UrlMetricsDataHistoryEntityDAO;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.entity.OwnDomainSettingEntity;
import com.actonia.entity.PageClarityTrackingEntity;
import com.actonia.entity.TargetUrlEntity;
import com.actonia.entity.UrlMetricsDataHistoryEntity;
import com.actonia.utils.CommonUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.PutMessageUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.PageClarityUpdateSummaryValueObject;

public class UrlMetricsStatusCodePoliteCrawlUpdate {

	private boolean isDebug = false;
	private static ThreadPoolService threadPool = ThreadPoolService.getInstance();
	private OwnDomainEntityDAO ownDomainEntityDAO;
	private UrlMetricsDataEntityDAO urlMetricsDataEntityDAO;
	private TargetURLCriteriaDAO targetURLCriteriaDAO;
	private TargetUrlEntityDAO targetUrlEntityDAO;
	private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
	private UrlMetricsDataHistoryEntityDAO urlMetricsDataHistoryEntityDAO;

	private Date yesterdayDate;
	private Date sevenDaysAgoDate;
	private PageClarityTrackingEntityDAO pageClarityTrackingEntityDAO;

	private static final int MAX_NUMBER_OF_WEEKS_OF_HISTORICAL_DATA = 26;

	public UrlMetricsStatusCodePoliteCrawlUpdate() {
		this.ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		this.urlMetricsDataEntityDAO = SpringBeanFactory.getBean("urlMetricsDataEntityDAO");
		this.targetURLCriteriaDAO = SpringBeanFactory.getBean("targetURLCriteriaDAO");
		this.targetUrlEntityDAO = SpringBeanFactory.getBean("targetUrlEntityDAO");
		this.ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
		this.urlMetricsDataHistoryEntityDAO = SpringBeanFactory.getBean("urlMetricsDataHistoryEntityDAO");
		this.pageClarityTrackingEntityDAO = SpringBeanFactory.getBean("pageClarityTrackingEntityDAO");
	}

	public static void main(String args[]) throws Exception {
		System.out.println("main() begins.");
		long startTimestamp = System.currentTimeMillis();
		threadPool.init();
		CommonUtils.initThreads(8);
		UrlMetricsStatusCodePoliteCrawlUpdate urlMetricsStatusCodePoliteCrawlUpdate = new UrlMetricsStatusCodePoliteCrawlUpdate();
		urlMetricsStatusCodePoliteCrawlUpdate.process(args);
		threadPool.destroy();
		System.out.println("main() ends. elapsed time in sec.:" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private void process(String args[]) throws Exception {

		Boolean isExecDomainIdsInd = null;
		Set<Integer> runtimeDomainSet = null;

		Properties domainProperties = new Properties();
		try {
			domainProperties.load(UrlMetricsStatusCodePoliteCrawlUpdate.class.getResourceAsStream("/domain.properties"));
		} catch (Exception e) {
			System.out.println("process() no domain.properties file found");
			System.exit(-1);
		}

		String execDomainIds = domainProperties.getProperty("exec.domain");
		String notExecDomainIds = domainProperties.getProperty("notexec.domain");
		System.out.println("process() execDomainIds=" + execDomainIds);
		System.out.println("process() notExecDomainIds=" + notExecDomainIds);

		List<OwnDomainEntity> ownDomainEntityList = ownDomainEntityDAO.queryForAll();

		// process specific domains
		if (StringUtils.isNotBlank(execDomainIds)) {
			runtimeDomainSet = CommonUtils.getDomainSet(execDomainIds);
			isExecDomainIdsInd = true;
		}
		// do not process specific domains
		else if (StringUtils.isNotBlank(notExecDomainIds)) {
			runtimeDomainSet = CommonUtils.getDomainSet(notExecDomainIds);
			isExecDomainIdsInd = false;
		}
		// process all domains
		else {
			runtimeDomainSet = null;
			isExecDomainIdsInd = null;
		}

		List<OwnDomainEntity> filteredOwnDomainEntityList = PutMessageUtils.getInstance().filterDomains(ownDomainEntityList, isExecDomainIdsInd, runtimeDomainSet);
		if (filteredOwnDomainEntityList != null && filteredOwnDomainEntityList.size() > 0) {
			initialize(filteredOwnDomainEntityList);
			performMultiThreadedProcessing(filteredOwnDomainEntityList);
		}
		//TargetUrlMetricsUpdateUtils.sendSummary();
	}

	private void initialize(List<OwnDomainEntity> ownDomainEntityList) throws Exception {
		Date todayDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
		this.yesterdayDate = DateUtils.addDays(todayDate, -1);
		this.sevenDaysAgoDate = DateUtils.addDays(todayDate, -7);

		TargetUrlHtmlClickHouseDAO.getInstance();

		//cleanupUrlMetricsData(); //debug
		//cleanupTargetUrlCriteria(); //debug
		//cleanupInactiveDomainsData(); //debug
		processHistoricalDataRetention();

		CacheModleFactory.getInstance().setPageClarityUpdateSummaryValueObjectList(new ArrayList<PageClarityUpdateSummaryValueObject>());
	}

	private void cleanupUrlMetricsData() {
		if (isDebug == true) {
			return;
		}

		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("cleanupUrlMetricsData() begins.");
		urlMetricsDataEntityDAO.cleanupTargetUrls();
		urlMetricsDataEntityDAO.cleanupCompetitorUrls();
		FormatUtils.getInstance().logMemoryUsage("cleanupUrlMetricsData() ends. elapsed (s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private void cleanupTargetUrlCriteria() {
		if (isDebug == true) {
			return;
		}
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("cleanupTargetUrlCriteria() begins.");
		targetURLCriteriaDAO.cleanup();
		FormatUtils.getInstance().logMemoryUsage("cleanupTargetUrlCriteria() ends. elapsed (s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private void performMultiThreadedProcessing(List<OwnDomainEntity> ownDomainEntityList) throws Exception {
		int totalNumberOfDomains = ownDomainEntityList.size();
		long startTimestamp = System.currentTimeMillis();
		String ipAddress = null;
		OwnDomainEntity ownDomainEntity = null;
		OwnDomainSettingEntity ownDomainSettingEntity = null;
		UrlMetricsStatusCodePoliteCrawlUpdateCommand urlMetricsStatusCodePoliteCrawlUpdateCommand = null;
		int numberOfDomainsProcessed = 0;
		int totalNumberOfUrls = 0;
		boolean isMobileTrafficEnabled = false;
		Integer targetUrlHtmlDailyDateNumber = null;
		String targetUrlHtmlDailyDateString = null;
		Date targetUrlHtmlDailyDate = null;
		int domainId = 0;
		String domainName = null;
		nextClientDomain: do {
			ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
			if (ipAddress == null) {
				continue;
			}
			ownDomainEntity = ownDomainEntityList.get(numberOfDomainsProcessed++);
			domainId = ownDomainEntity.getId();
			domainName = ownDomainEntity.getDomain();

			totalNumberOfUrls = targetUrlEntityDAO.getTargetUrlCount(ownDomainEntity.getId(), TargetUrlEntity.TYPE_ADD_BY_USER, TargetUrlEntity.STATUS_ACTIVE);
			FormatUtils.getInstance().logMemoryUsage("performMultiThreadedProcessing() domainId=" + domainId + ",domainName=" + domainName + ",totalNumberOfUrls="
					+ totalNumberOfUrls + ",numberOfDomainsProcessed=" + numberOfDomainsProcessed + ",totalNumberOfDomains=" + totalNumberOfDomains);

			// process those client domains with total number of target URLs less than the defined processing threshold
			if (totalNumberOfUrls >= TargetUrlMetricsUpdate.PROCESSING_THRESHOLD) {
				FormatUtils.getInstance().logMemoryUsage("performMultiThreadedProcessing() skipped domainId=" + domainId + ",domainName=" + domainName
						+ ",totalNumberOfUrls >= TargetUrlMetricsUpdate.PROCESSING_THRESHOLD");
				CacheModleFactory.getInstance().setAliveIpAddress(ipAddress);
				continue;
			}

			// determine if mobile traffic is enabled for the client domain
			ownDomainSettingEntity = ownDomainSettingEntityDAO.getSettingByDomainId(ownDomainEntity.getId());
			if (ownDomainSettingEntity != null && ownDomainSettingEntity.getEnableMoblie() != null
					&& ownDomainSettingEntity.getEnableMoblie() == OwnDomainSettingEntity.MOBILE_ENABLEED) {
				isMobileTrafficEnabled = true;
			} else {
				isMobileTrafficEnabled = false;
			}

			try {
				targetUrlHtmlDailyDate = null;
				if (ownDomainSettingEntity != null) {
					FormatUtils.getInstance().logMemoryUsage("performMultiThreadedProcessing() domainId=" + domainId + ",domainName=" + domainName
							+ ",ownDomainSettingEntity.getTargetUrlHtmlDailyDate()=" + ownDomainSettingEntity.getTargetUrlHtmlDailyDate());
					targetUrlHtmlDailyDateNumber = ownDomainSettingEntity.getTargetUrlHtmlDailyDate();
					if (targetUrlHtmlDailyDateNumber != null && targetUrlHtmlDailyDateNumber.intValue() != 0) {
						targetUrlHtmlDailyDateString = String.valueOf(targetUrlHtmlDailyDateNumber);
						targetUrlHtmlDailyDate = DateUtils.parseDate(targetUrlHtmlDailyDateString, new String[] { IConstants.DATE_FORMAT_YYYYMMDD });
					}
				}
			} catch (Exception e) {
				e.printStackTrace();
				FormatUtils.getInstance()
						.logMemoryUsage("performMultiThreadedProcessing() domainId=" + domainId + ",domainName=" + domainName
								+ ",ownDomainSettingEntity.getTargetUrlHtmlDailyDate()=" + ownDomainSettingEntity.getTargetUrlHtmlDailyDate() + ",exception message="
								+ e.getMessage());
			}

			if (ownDomainSettingEntity != null) {
				if (StringUtils.equalsIgnoreCase(ownDomainSettingEntity.getCompanyName(), IConstants.COMPANY_NAME_HOMEAWAY)) {
					CacheModleFactory.getInstance().setAliveIpAddress(ipAddress);
					FormatUtils.getInstance().logMemoryUsage("performMultiThreadedProcessing() ipAddress released=" + ipAddress + ",domainId=" + domainId
							+ ",domainName=" + domainName + ", skip companyName=" + ownDomainSettingEntity.getCompanyName());
					continue nextClientDomain;
				}
			}
			urlMetricsStatusCodePoliteCrawlUpdateCommand = getUrlMetricsStatusCodePoliteCrawlUpdateCommand(ipAddress, ownDomainEntity, isMobileTrafficEnabled,
					targetUrlHtmlDailyDate);
			if (urlMetricsStatusCodePoliteCrawlUpdateCommand != null) {
				try {
					threadPool.execute(urlMetricsStatusCodePoliteCrawlUpdateCommand);
				} catch (Exception e) {
					e.printStackTrace();
				}
			} else {
				CacheModleFactory.getInstance().setAliveIpAddress(ipAddress);
			}
		} while (numberOfDomainsProcessed < totalNumberOfDomains);

		do {
			try {
				Thread.sleep(88888);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
			System.out.println("performMultiThreadedProcessing() threadPool.getThreadPool().getActiveCount()=" + threadPool.getThreadPool().getActiveCount());
		} while (threadPool.getThreadPool().getActiveCount() > 0);
		System.out.println("performMultiThreadedProcessing() end. elapsed time in seconds=" + ((System.currentTimeMillis() - startTimestamp) / 1000));
	}

	private UrlMetricsStatusCodePoliteCrawlUpdateCommand getUrlMetricsStatusCodePoliteCrawlUpdateCommand(String ip, OwnDomainEntity ownDomainEntity,
			boolean isMobileTrafficEnabled, Date targetUrlHtmlDailyDate) {
		UrlMetricsStatusCodePoliteCrawlUpdateCommand urlMetricsStatusCodePoliteCrawlUpdateCommand = new UrlMetricsStatusCodePoliteCrawlUpdateCommand(ip,
				ownDomainEntity, isMobileTrafficEnabled, yesterdayDate, sevenDaysAgoDate, targetUrlHtmlDailyDate);
		urlMetricsStatusCodePoliteCrawlUpdateCommand.setStatus(true);
		return urlMetricsStatusCodePoliteCrawlUpdateCommand;
	}

	private void cleanupInactiveDomainsData() {
		long startTimestamp = System.currentTimeMillis();
		List<Integer> inactiveDomainIdList = pageClarityTrackingEntityDAO.findInactiveDomainIdList();
		FormatUtils.getInstance().logMemoryUsage("cleanupInactiveDomainsData() inactiveDomainIdList.size()=" + inactiveDomainIdList.size() + ", elapsed(s.)="
				+ (System.currentTimeMillis() - startTimestamp) / 1000);
		if (inactiveDomainIdList != null && inactiveDomainIdList.size() > 0) {
			for (Integer inactiveDomainId : inactiveDomainIdList) {
				urlMetricsDataHistoryEntityDAO.reset(inactiveDomainId);
				pageClarityTrackingEntityDAO.reset(inactiveDomainId);
				FormatUtils.getInstance().logMemoryUsage("cleanupInactiveDomainsData() inactive domain ID reset=" + inactiveDomainId);
			}
		}
	}

	private void processHistoricalDataRetention() {

		try {
			int domainIdNumberOfWeek = 0;
			List<UrlMetricsDataHistoryEntity> urlMetricsDataHistoryEntityList = urlMetricsDataHistoryEntityDAO.getUniqueDomainIdYearWeekList();

			// map key = domain ID
			// map value = list of year week in descending order
			Map<Integer, List<Integer>> domainIdYearWeekListMap = new HashMap<Integer, List<Integer>>();

			List<Integer> yearWeekList = null;

			int domainId = 0;
			int earliestYearWeek = 0;
			PageClarityTrackingEntity pageClarityTrackingEntity = null;

			// client domain level record, user ID is 0
			int userId = 0;

			if (urlMetricsDataHistoryEntityList != null && urlMetricsDataHistoryEntityList.size() > 0) {
				for (UrlMetricsDataHistoryEntity urlMetricsDataHistoryEntity : urlMetricsDataHistoryEntityList) {
					domainId = urlMetricsDataHistoryEntity.getDomainId();
					if (!domainIdYearWeekListMap.containsKey(domainId)) {
						yearWeekList = new ArrayList<Integer>();
						yearWeekList.add(urlMetricsDataHistoryEntity.getYearWeek());

					} else {
						domainIdYearWeekListMap.get(domainId).add(urlMetricsDataHistoryEntity.getYearWeek());
					}
					domainIdYearWeekListMap.put(domainId, yearWeekList);
				}
				Iterator<Integer> domainIdIterator = domainIdYearWeekListMap.keySet().iterator();
				while (domainIdIterator.hasNext()) {
					domainId = (Integer) domainIdIterator.next();
					yearWeekList = domainIdYearWeekListMap.get(domainId);
					domainIdNumberOfWeek = 0;
					for (Integer yearWeek : yearWeekList) {
						domainIdNumberOfWeek++;
						if (domainIdNumberOfWeek > MAX_NUMBER_OF_WEEKS_OF_HISTORICAL_DATA) {

							// reset historical data earlier than maximum number of historical data
							urlMetricsDataHistoryEntityDAO.reset(domainId, yearWeek);

							// retrieve existing 'page_clarity_tracking' record in the 'actonia' MySQL database
							pageClarityTrackingEntity = pageClarityTrackingEntityDAO.get(domainId, userId);

							if (pageClarityTrackingEntity != null) {
								// update page clarity tracking with the earliest year week
								pageClarityTrackingEntity.setStartYearWeek(earliestYearWeek);
								pageClarityTrackingEntityDAO.update(pageClarityTrackingEntity);
								FormatUtils.getInstance()
										.logMemoryUsage("processHistoricalDataRetention() domainId=" + domainId + ",domainIdNumberOfWeek=" + domainIdNumberOfWeek
												+ ",yearWeek=" + yearWeek + " to be reset,pageClarityTrackingEntity=" + pageClarityTrackingEntity.toString());
							}

						} else {
							//FormatUtils.getInstance().logMemoryUsage("processHistoricalDataRetention() domainId=" + domainId + ",domainIdNumberOfWeek="
							//		+ domainIdNumberOfWeek + ",yearWeek=" + yearWeek + ", no need to reset.");
							earliestYearWeek = yearWeek;
						}
					}
				}
			}

		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
