package com.actonia.url.metrics.update;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.dao.PageClarityTrackingEntityDAO;
import com.actonia.dao.UrlMetricsDataHistoryEntityDAO;
import com.actonia.entity.AgencyInfoEntity;
import com.actonia.entity.PageClarityTrackingEntity;
import com.actonia.entity.TargetURLCriteria;
import com.actonia.entity.TargetUrlEntity;
import com.actonia.entity.UrlMetricsDataEntity;
import com.actonia.entity.UrlMetricsDataHistoryEntity;
import com.actonia.service.AgencyInfoService;
import com.actonia.utils.EmailSenderComponent;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.PageClarityUpdateSummaryValueObject;

public class TargetUrlMetricsUpdateUtils {

	//private static boolean isDebug = false;

	public static final int MAX_RECORDS_PER_BATCH_UPDATE = 100;
	public static final int CLIENT_DOMAIN_START_DAY_OF_WEEK_MONDAY = 1;
	private static final int CLIENT_DOMAIN_START_DAY_OF_WEEK_TUESDAY = 2;
	private static final int CLIENT_DOMAIN_START_DAY_OF_WEEK_WEDNESDAY = 3;
	private static final int CLIENT_DOMAIN_START_DAY_OF_WEEK_THURSDAY = 4;
	private static final int CLIENT_DOMAIN_START_DAY_OF_WEEK_FRIDAY = 5;
	private static final int CLIENT_DOMAIN_START_DAY_OF_WEEK_SATURDAY = 6;
	private static final int CLIENT_DOMAIN_START_DAY_OF_WEEK_SUNDAY = 7;
	private static UrlMetricsDataHistoryEntityDAO urlMetricsDataHistoryEntityDAO;
	private static PageClarityTrackingEntityDAO pageClarityTrackingEntityDAO;

	private static AgencyInfoService agencyInfoService;

	private static EmailSenderComponent emailSenderComponent;

	private static Date startProcessingTimestamp = new Date();

	static {
		urlMetricsDataHistoryEntityDAO = SpringBeanFactory.getBean("urlMetricsDataHistoryEntityDAO");
		pageClarityTrackingEntityDAO = SpringBeanFactory.getBean("pageClarityTrackingEntityDAO");
		agencyInfoService = SpringBeanFactory.getBean("agencyInfoService");
		emailSenderComponent = SpringBeanFactory.getBean("emailSenderComponent");
	}

	public synchronized static void archiveWeeklyData(int domainId, String standardLoggingText, int clientDomainStartDayOfWeek,
			Map<Long, UrlMetricsDataEntity> targetUrlIdUrlMetricsDataEntityMap, Map<Long, TargetURLCriteria> targetUrlIdTargetURLCriteriaMap,
			List<TargetUrlEntity> targetUrlEntityList) {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance()
				.logMemoryUsage("archiveWeeklyData() begins." + standardLoggingText + ",clientDomainStartDayOfWeek=" + clientDomainStartDayOfWeek
						+ ",targetUrlIdUrlMetricsDataEntityMap.size()=" + targetUrlIdUrlMetricsDataEntityMap.size() + ",targetUrlIdTargetURLCriteriaMap.size()="
						+ targetUrlIdTargetURLCriteriaMap.size());
		Date todayDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
		Date yesterdayDate = null;
		boolean isArchiveRequired = checkIfArchiveRequired(domainId, standardLoggingText, todayDate, clientDomainStartDayOfWeek);
		int lastYearWeek = 0;

		// map key = target URL ID
		// map value = targetUrlEntity
		Map<Long, TargetUrlEntity> targetUrlEntityMap = null;

		if (isArchiveRequired == true) {
			yesterdayDate = DateUtils.addDays(todayDate, -1);
			lastYearWeek = FormatUtils.getInstance().calculateYearWeekBasedOnStartDayOfWeek(yesterdayDate, clientDomainStartDayOfWeek);

			// create a map of target URL ID - target URL entity
			targetUrlEntityMap = getTargetUrlEntityMap(targetUrlEntityList);

			// archive last week's 't_url_metrics_data' and 'targeturl_criteria' MySQL records to 't_url_metrics_data_hist' IEE table
			updateHistoricalDatabase(domainId, standardLoggingText, lastYearWeek, targetUrlIdUrlMetricsDataEntityMap, targetUrlEntityMap,
					targetUrlIdTargetURLCriteriaMap);

			// update 'end_year_week' in the 'page_clarity_tracking' record (userId = 0)
			updatePageClarityTracking(domainId, standardLoggingText, lastYearWeek);
			FormatUtils.getInstance().logMemoryUsage("archiveWeeklyData() ends." + standardLoggingText + ",yesterdayDate=" + yesterdayDate + ",lastYearWeek="
					+ lastYearWeek + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		} else {
			FormatUtils.getInstance()
					.logMemoryUsage("archiveWeeklyData() ends." + standardLoggingText + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		}
	}

	private static Map<Long, TargetUrlEntity> getTargetUrlEntityMap(List<TargetUrlEntity> targetUrlEntityList) {
		Map<Long, TargetUrlEntity> targetUrlEntityMap = new HashMap<Long, TargetUrlEntity>();
		for (TargetUrlEntity targetUrlEntity : targetUrlEntityList) {
			targetUrlEntityMap.put(new Long(String.valueOf(targetUrlEntity.getId())), targetUrlEntity);
		}
		return targetUrlEntityMap;
	}

	private static boolean checkIfArchiveRequired(int domainId, String standardLoggingText, Date todayDate, int clientDomainStartDayOfWeek) {

		boolean isArchiveRequired = false;
		Calendar todayCalendar = new GregorianCalendar();
		todayCalendar.setTime(todayDate);

		int todayDayOfWeek = todayCalendar.get(Calendar.DAY_OF_WEEK);
		if (todayDayOfWeek == Calendar.SUNDAY && clientDomainStartDayOfWeek == CLIENT_DOMAIN_START_DAY_OF_WEEK_SUNDAY) {
			isArchiveRequired = true;
		} else if (todayDayOfWeek == Calendar.MONDAY && clientDomainStartDayOfWeek == CLIENT_DOMAIN_START_DAY_OF_WEEK_MONDAY) {
			isArchiveRequired = true;
		} else if (todayDayOfWeek == Calendar.TUESDAY && clientDomainStartDayOfWeek == CLIENT_DOMAIN_START_DAY_OF_WEEK_TUESDAY) {
			isArchiveRequired = true;
		} else if (todayDayOfWeek == Calendar.WEDNESDAY && clientDomainStartDayOfWeek == CLIENT_DOMAIN_START_DAY_OF_WEEK_WEDNESDAY) {
			isArchiveRequired = true;
		} else if (todayDayOfWeek == Calendar.THURSDAY && clientDomainStartDayOfWeek == CLIENT_DOMAIN_START_DAY_OF_WEEK_THURSDAY) {
			isArchiveRequired = true;
		} else if (todayDayOfWeek == Calendar.FRIDAY && clientDomainStartDayOfWeek == CLIENT_DOMAIN_START_DAY_OF_WEEK_FRIDAY) {
			isArchiveRequired = true;
		} else if (todayDayOfWeek == Calendar.SATURDAY && clientDomainStartDayOfWeek == CLIENT_DOMAIN_START_DAY_OF_WEEK_SATURDAY) {
			isArchiveRequired = true;
		}

		// check if the archive already created for the last year week
		if (isArchiveRequired == true) {
			Date testYesterdayDate = DateUtils.addDays(todayDate, -1);
			int lastYearWeek = FormatUtils.getInstance().calculateYearWeekBasedOnStartDayOfWeek(testYesterdayDate, clientDomainStartDayOfWeek);

			// client domain level record, user ID is 0
			int userId = 0;

			// retrieve existing 'page_clarity_tracking' record in the 'actonia' MySQL database
			PageClarityTrackingEntity pageClarityTrackingEntity = pageClarityTrackingEntityDAO.get(domainId, userId);
			if (pageClarityTrackingEntity != null && pageClarityTrackingEntity.getEndYearWeek() == lastYearWeek) {
				isArchiveRequired = false;
				FormatUtils.getInstance().logMemoryUsage("checkIfArchiveRequired() " + standardLoggingText + ",archive already created for last year week="
						+ lastYearWeek + ",isArchiveRequired=" + isArchiveRequired);
			}
		}
		FormatUtils.getInstance().logMemoryUsage("checkIfArchiveRequired() " + standardLoggingText + ",todayDayOfWeek (Java convention)=" + todayDayOfWeek
				+ ",clientDomainStartDayOfWeek=" + clientDomainStartDayOfWeek + ",isArchiveRequired=" + isArchiveRequired);

		return isArchiveRequired;
	}

	private static void updateHistoricalDatabase(int domainId, String standardLoggingText, int lastYearWeek,
			Map<Long, UrlMetricsDataEntity> targetUrlIdUrlMetricsDataEntityMap, Map<Long, TargetUrlEntity> targetUrlEntityMap,
			Map<Long, TargetURLCriteria> targetUrlIdTargetURLCriteriaMap) {
		if (targetUrlIdUrlMetricsDataEntityMap == null || targetUrlIdUrlMetricsDataEntityMap.size() == 0) {
			FormatUtils.getInstance().logMemoryUsage("updateHistoricalDatabase() " + standardLoggingText + ",targetUrlIdUrlMetricsDataEntityMap is empty.");
			return;
		}
		int totalRecordsArchived = 0;
		long startTimestamp = System.currentTimeMillis();

		TargetUrlEntity targetUrlEntity = null;
		TargetURLCriteria targetURLCriteria = null;

		// reset existing weekly data
		urlMetricsDataHistoryEntityDAO.reset(domainId, lastYearWeek);

		// create new weekly data
		List<UrlMetricsDataHistoryEntity> urlMetricsDataHistoryEntityToBeCreatedList = new ArrayList<UrlMetricsDataHistoryEntity>();
		Collection<UrlMetricsDataEntity> urlMetricsDataEntityList = targetUrlIdUrlMetricsDataEntityMap.values();
		UrlMetricsDataHistoryEntity urlMetricsDataHistoryEntity = null;
		if (urlMetricsDataEntityList != null && urlMetricsDataEntityList.size() > 0) {
			forLoop1: for (UrlMetricsDataEntity urlMetricsDataEntity : urlMetricsDataEntityList) {
				urlMetricsDataHistoryEntity = new UrlMetricsDataHistoryEntity();

				// archive the historical data in the 't_url_metrics_data'
				urlMetricsDataHistoryEntity.setYearWeek(lastYearWeek);
				urlMetricsDataHistoryEntity.setId(urlMetricsDataEntity.getId());
				urlMetricsDataHistoryEntity.setUrl(urlMetricsDataEntity.getUrl());
				urlMetricsDataHistoryEntity.setUrlId(urlMetricsDataEntity.getUrlId());
				urlMetricsDataHistoryEntity.setType(urlMetricsDataEntity.getType());
				urlMetricsDataHistoryEntity.setPagerank(urlMetricsDataEntity.getPagerank());
				urlMetricsDataHistoryEntity.setInbounds(urlMetricsDataEntity.getInbounds());
				urlMetricsDataHistoryEntity.setInternalLinks(urlMetricsDataEntity.getInternalLinks());
				urlMetricsDataHistoryEntity.setOutbounds(urlMetricsDataEntity.getOutbounds());
				urlMetricsDataHistoryEntity.setCachedDate(urlMetricsDataEntity.getCachedDate());
				urlMetricsDataHistoryEntity.setRespCode(urlMetricsDataEntity.getRespCode());
				urlMetricsDataHistoryEntity.setIpaddress(urlMetricsDataEntity.getIpaddress());
				urlMetricsDataHistoryEntity.setLastUpdate(urlMetricsDataEntity.getLastUpdate());
				urlMetricsDataHistoryEntity.setPrUpdateDate(urlMetricsDataEntity.getPrUpdateDate());
				urlMetricsDataHistoryEntity.setYahooUpdateDate(urlMetricsDataEntity.getYahooUpdateDate());
				urlMetricsDataHistoryEntity.setCacheDateUpdateDate(urlMetricsDataEntity.getCacheDateUpdateDate());
				urlMetricsDataHistoryEntity.setRespcodeUpdateDate(urlMetricsDataEntity.getRespcodeUpdateDate());
				urlMetricsDataHistoryEntity.setSemrushUpdateDate(urlMetricsDataEntity.getSemrushUpdateDate());
				urlMetricsDataHistoryEntity.setPagespeedUpdateDate(urlMetricsDataEntity.getPagespeedUpdateDate());
				urlMetricsDataHistoryEntity.setGoogleIndex(urlMetricsDataEntity.getGoogleIndex());
				urlMetricsDataHistoryEntity.setSeomozUpdateDate(urlMetricsDataEntity.getSeomozUpdateDate());
				urlMetricsDataHistoryEntity.setMozRank(urlMetricsDataEntity.getMozRank());
				urlMetricsDataHistoryEntity.setPageAuthority(urlMetricsDataEntity.getPageAuthority());
				urlMetricsDataHistoryEntity.setMozInbounds(urlMetricsDataEntity.getMozInbounds());
				urlMetricsDataHistoryEntity.setRobotMeta(urlMetricsDataEntity.getRobotMeta());
				urlMetricsDataHistoryEntity.setLinks(urlMetricsDataEntity.getLinks());
				urlMetricsDataHistoryEntity.setDomainAuthority(urlMetricsDataEntity.getDomainAuthority());
				urlMetricsDataHistoryEntity.setMajesticsUpdateDate(urlMetricsDataEntity.getMajesticsUpdateDate());
				urlMetricsDataHistoryEntity.setAcRank(urlMetricsDataEntity.getAcRank());
				urlMetricsDataHistoryEntity.setPercentMobileEntrances(urlMetricsDataEntity.getPercentMobileEntrances());
				urlMetricsDataHistoryEntity.setAssociatedKeywords(urlMetricsDataEntity.getAssociatedKeywords());
				urlMetricsDataHistoryEntity.setAssociatedKeywordsRanked(urlMetricsDataEntity.getAssociatedKeywordsRanked());
				urlMetricsDataHistoryEntity.setLinksAcquiredOrganically(urlMetricsDataEntity.getLinksAcquiredOrganically());
				urlMetricsDataHistoryEntity.setLinksAcquiredManually(urlMetricsDataEntity.getLinksAcquiredManually());
				urlMetricsDataHistoryEntity.setAssociatedCompetitors(urlMetricsDataEntity.getAssociatedCompetitors());

				if (targetUrlEntityMap.containsKey(urlMetricsDataEntity.getUrlId())) {
					targetUrlEntity = targetUrlEntityMap.get(urlMetricsDataEntity.getUrlId());
					urlMetricsDataHistoryEntity.setFriendlyName(targetUrlEntity.getFriendlyName());
				}

				// archive the historical data in the 'targeturl_criteria' table
				if (targetUrlIdTargetURLCriteriaMap.containsKey(urlMetricsDataEntity.getUrlId())) {
					targetURLCriteria = targetUrlIdTargetURLCriteriaMap.get(urlMetricsDataEntity.getUrlId());
					if (targetURLCriteria != null && targetURLCriteria.getId() != null) {
						urlMetricsDataHistoryEntity.setTargetURLCriteria(targetURLCriteria);
					} else {
						continue forLoop1;
					}
				} else {
					continue forLoop1;
				}
				urlMetricsDataHistoryEntityToBeCreatedList.add(urlMetricsDataHistoryEntity);
				totalRecordsArchived++;
				if (urlMetricsDataHistoryEntityToBeCreatedList.size() >= MAX_RECORDS_PER_BATCH_UPDATE) {
					urlMetricsDataHistoryEntityDAO.insertMultiRowsBatch(urlMetricsDataHistoryEntityToBeCreatedList);
					urlMetricsDataHistoryEntityToBeCreatedList = new ArrayList<UrlMetricsDataHistoryEntity>();
				}
			}
		}
		if (urlMetricsDataHistoryEntityToBeCreatedList.size() > 0) {
			urlMetricsDataHistoryEntityDAO.insertMultiRowsBatch(urlMetricsDataHistoryEntityToBeCreatedList);
			urlMetricsDataHistoryEntityToBeCreatedList = new ArrayList<UrlMetricsDataHistoryEntity>();
		}
		FormatUtils.getInstance().logMemoryUsage("updateHistoricalDatabase() " + standardLoggingText + ",totalRecordsArchived=" + totalRecordsArchived + ",elapsed(s.)="
				+ (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private static void updatePageClarityTracking(int domainId, String standardLoggingText, int lastYearWeek) {

		// client domain level record, user ID is 0
		int userId = 0;

		// retrieve existing 'page_clarity_tracking' record in the 'actonia' MySQL database
		PageClarityTrackingEntity pageClarityTrackingEntity = pageClarityTrackingEntityDAO.get(domainId, userId);

		// update 'end_year_week'
		if (pageClarityTrackingEntity != null) {
			pageClarityTrackingEntity.setEndYearWeek(lastYearWeek);
			pageClarityTrackingEntity.setLastUpdateTimestamp(new Date());
			pageClarityTrackingEntityDAO.update(pageClarityTrackingEntity);
		}
		// create 'start_year_week' and 'end_year_week' identical
		else {
			pageClarityTrackingEntity = new PageClarityTrackingEntity();
			pageClarityTrackingEntity.setDomainId(domainId);
			pageClarityTrackingEntity.setUserId(userId);
			pageClarityTrackingEntity.setStartYearWeek(lastYearWeek);
			pageClarityTrackingEntity.setEndYearWeek(lastYearWeek);
			pageClarityTrackingEntity.setLastUpdateTimestamp(new Date());
			pageClarityTrackingEntityDAO.create(pageClarityTrackingEntity);
		}
		FormatUtils.getInstance().logMemoryUsage("updatePageClarityTracking() " + standardLoggingText + ",pageClarityTrackingEntity=" + pageClarityTrackingEntity);
	}

//	public static void sendSummary() throws Exception {
//		System.out.println("sendSummary() begins.");
//		int totalDomainProcessed = 0;
//		List<PageClarityUpdateSummaryValueObject> pageClarityUpdateSummaryList = CacheModleFactory.getInstance().getPageClarityUpdateSummaryValueObjectList();
//		if (pageClarityUpdateSummaryList != null && pageClarityUpdateSummaryList.size() > 0) {
//			totalDomainProcessed = pageClarityUpdateSummaryList.size();
//			for (PageClarityUpdateSummaryValueObject pageClarityUpdateSummaryValueObject : pageClarityUpdateSummaryList) {
//				FormatUtils.getInstance().logMemoryUsage("sendSummary() pageClarityUpdateSummaryValueObject=" + pageClarityUpdateSummaryValueObject.toString());
//			}
//		}
//
//		String[] emailAddressArray = null;
//		int retryCount = 0;
//		Map<String, Object> map = new HashMap<String, Object>();
//		String processDateString = DateFormatUtils.format(new Date(), IConstants.DATE_FORMAT_YYYY_MM_DD);
//		String emailSubject = "Page Clarity Update Summary - " + processDateString;
//		String startDateString = DateFormatUtils.format(startProcessingTimestamp, IConstants.DATE_FORMAT_YYYY_MM_DD_HHMMSS);
//		String endDateString = DateFormatUtils.format(new Date(), IConstants.DATE_FORMAT_YYYY_MM_DD_HHMMSS);
//		//map.put("pageClarityUpdateSummaryList", pageClarityUpdateSummaryList);
//		map.put("totalDomainProcessed", totalDomainProcessed);
//		map.put("startDateString", startDateString);
//		map.put("endDateString", endDateString);
//
//		AgencyInfoEntity agencyInfoEntity = agencyInfoService.getByDomainId(1701);
//		while (retryCount < IConstants.MAX_SEND_EMAIL_RETRY_COUNT) {
//			try {
//				emailSenderComponent.sendMimeMultiPartZeptoMail(NOTIFICATION_EMAIL_ADDRESS, emailAddressArray, emailSubject, "mail_page_clarity_update_summary.txt",
//						"mail_page_clarity_update_summary.html", map, agencyInfoEntity);
//				retryCount = IConstants.MAX_SEND_EMAIL_RETRY_COUNT;
//			} catch (Exception e) {
//				e.printStackTrace();
//				retryCount++;
//				if (retryCount >= IConstants.MAX_SEND_EMAIL_RETRY_COUNT) {
//					throw e;
//				} else {
//					System.out.println("sendSummary() retryCount=" + retryCount);
//					try {
//						Thread.sleep(IConstants.RETRY_WAIT_TIME_IN_MILLISECONDS);
//					} catch (InterruptedException e1) {
//						e1.printStackTrace();
//					}
//				}
//			}
//		}
//		System.out.println("sendSummary() ends.");
//	}
}
