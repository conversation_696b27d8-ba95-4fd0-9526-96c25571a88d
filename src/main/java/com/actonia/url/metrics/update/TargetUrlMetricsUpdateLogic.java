package com.actonia.url.metrics.update;

import java.net.URL;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.web.util.UriUtils;

import com.actonia.IConstants;
import com.actonia.dao.KeywordPageRelEntityDAO;
import com.actonia.dao.KeywordTargeturlEntityDAO;
import com.actonia.dao.TargetURLCriteriaDAO;
import com.actonia.dao.UrlMetricsDataEntityDAO;
import com.actonia.dao.UserBacklinkDAO;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.TargetURLCriteria;
import com.actonia.entity.UrlMetricsDataEntity;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.JCSManager;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.UrlMetricsUpdateValueObject;

public class TargetUrlMetricsUpdateLogic {

	private boolean isDebug = false;

	private static final int RESP_CODE_200 = 200;
	private static final String NOFOLLOW = "nofollow";
	private static final String FOLLOW = "follow";
	private static final String NOINDEX = "noindex";
	private static final String INDEX = "index";
	private static final int MAX_RETRY_COUNT = 8;
	private static final int MAX_PAGE_TITLE_LENGTH = 2000;
	private static final int MAX_PAGE_META_KEYWORD_LENGTH = 2000;
	private static final int MAX_PAGE_META_DESC_LENGTH = 2000;
	private static final int MAX_PAGE_HEADING_LENGTH = 2000;
	private static final String SLASH_POUND = "/#";
	private static final String SLASH_POUND_SLASH = "/#/";
	private static final String SLASH = "/";
	private static final String QUOTE = "'";
	private static final String SLASH_QUOTE = "\\'";

	private String standardLoggingText;
	private UrlMetricsDataEntityDAO urlMetricsDataEntityDAO;
	private List<UrlMetricsUpdateValueObject> urlMetricsUpdateValueObjectToBeUpdatedList;
	private Date todayDate;
	private List<UrlMetricsDataEntity> urlMetricsDataEntityToBeCreatedList;
	private TargetURLCriteriaDAO targetURLCriteriaDAO;
	private List<TargetURLCriteria> targetURLCriteriaToBeUpdatedList;
	private int domainId;
	private List<TargetURLCriteria> targetURLCriteriaToBeCreatedList;

	private int totalUrlMetricsDataEntityFound;
	private int totalUrlMetricsDataEntityUpdated;
	private int totalUrlMetricsDataEntityNotUpdated;
	private int totalUrlMetricsDataEntityNotFound;
	private int totalUrlMetricsDataEntityCreated;

	private int totalTargetUrlCriteriaFound;
	private int totalTargetUrlCriteriaUpdated;
	private int totalTargetUrlCriteriaNotUpdated;
	private int totalTargetUrlCriteriaNotFound;
	private int totalTargetUrlCriteriaCreated;

	private KeywordTargeturlEntityDAO keywordTargeturlEntityDAO;
	private UserBacklinkDAO userBacklinkDAO;
	private KeywordPageRelEntityDAO keywordPageRelEntityDAO;

	private static final int PAGE_CLARITY_VERSION = 1;

	public TargetUrlMetricsUpdateLogic(String standardLoggingText, UrlMetricsDataEntityDAO urlMetricsDataEntityDAO, Date todayDate,
			TargetURLCriteriaDAO targetURLCriteriaDAO, int domainId, boolean isMobileTrafficEnabled, Date yesterdayDate, Date sevenDaysAgoDate,
			KeywordTargeturlEntityDAO keywordTargeturlEntityDAO, UserBacklinkDAO userBacklinkDAO) {

		this.domainId = domainId;
		this.standardLoggingText = standardLoggingText;
		this.todayDate = todayDate;

		this.urlMetricsDataEntityDAO = urlMetricsDataEntityDAO;
		this.targetURLCriteriaDAO = targetURLCriteriaDAO;

		this.urlMetricsUpdateValueObjectToBeUpdatedList = new ArrayList<UrlMetricsUpdateValueObject>();
		this.urlMetricsDataEntityToBeCreatedList = new ArrayList<UrlMetricsDataEntity>();
		this.targetURLCriteriaToBeUpdatedList = new ArrayList<TargetURLCriteria>();
		this.targetURLCriteriaToBeCreatedList = new ArrayList<TargetURLCriteria>();

		this.totalUrlMetricsDataEntityFound = 0;
		this.totalUrlMetricsDataEntityUpdated = 0;
		this.totalUrlMetricsDataEntityNotUpdated = 0;
		this.totalUrlMetricsDataEntityNotFound = 0;
		this.totalUrlMetricsDataEntityCreated = 0;

		this.totalTargetUrlCriteriaFound = 0;
		this.totalTargetUrlCriteriaUpdated = 0;
		this.totalTargetUrlCriteriaNotUpdated = 0;
		this.totalTargetUrlCriteriaNotFound = 0;
		this.totalTargetUrlCriteriaCreated = 0;

		this.keywordTargeturlEntityDAO = keywordTargeturlEntityDAO;
		this.userBacklinkDAO = userBacklinkDAO;
		this.keywordPageRelEntityDAO = SpringBeanFactory.getBean("keywordPageRelEntityDAO");
	}

	public UrlMetricsUpdateValueObject getUrlMetricsUpdateValueObject(HtmlClickHouseEntity htmlClickHouseEntity, Integer targetUrlWeekEntrances) {

		UrlMetricsUpdateValueObject urlMetricsUpdateValueObject = new UrlMetricsUpdateValueObject();

		// HTTP status code
		urlMetricsUpdateValueObject.setPoliteCrawlResponseCode(htmlClickHouseEntity.getHttpStatusCode());

		// track date
		urlMetricsUpdateValueObject
				.setPoliteCrawlTrackDate(NumberUtils.toInt(DateFormatUtils.format(htmlClickHouseEntity.getTrackDate(), IConstants.DATE_FORMAT_YYYYMMDD)));

		// internal links
		urlMetricsUpdateValueObject.setPoliteCrawlInternalLinks(htmlClickHouseEntity.getInternalLinkCount());

		// outbound links
		urlMetricsUpdateValueObject.setPoliteCrawlOutboundLinks(htmlClickHouseEntity.getCrawlerResponse().getOutlink_count());

		// title
		urlMetricsUpdateValueObject.setTitle(htmlClickHouseEntity.getCrawlerResponse().getTitle());

		// target URL entrances last seven days
		urlMetricsUpdateValueObject.setTargetUrlWeekEntrances(targetUrlWeekEntrances);

		return urlMetricsUpdateValueObject;
	}

	public void maintainUrlMetricsData(Long targetUrlId, String encodedTargetUrlString, UrlMetricsUpdateValueObject urlMetricsUpdateValueObject,
			Integer lastSevenDaysEntrances, UrlMetricsDataEntity urlMetricsDataEntity) {

		if (isDebug == true) {
			FormatUtils.getInstance().logMemoryUsage(
					"maintainUrlMetricsData() begins." + standardLoggingText + ",targetUrlId=" + targetUrlId + ",encodedTargetUrlString=" + encodedTargetUrlString);
		}

		//UrlMetricsDataEntity urlMetricsDataEntity = urlMetricsDataEntityDAO.getUrlMetricsDataEntityByUrlId(targetUrlId,
		//		UrlMetricsDataEntity.TYPE_TARGET_URL);

		// update 'urlMetricsUpdateValueObject' 't_url_metrics_data' specific data and calculated data
		updateUrlMetricsUpdateValueObject(urlMetricsUpdateValueObject, urlMetricsDataEntity, targetUrlId, lastSevenDaysEntrances, encodedTargetUrlString);

		// update target URL's 't_url_metrics_data' record
		if (urlMetricsDataEntity != null) {
			updateUrlMetricsData(urlMetricsDataEntity, urlMetricsUpdateValueObject, targetUrlId, lastSevenDaysEntrances, encodedTargetUrlString);
		}
		// when the 't_target_url' record does not have a corresponding 't_url_metrics_data' record
		else {
			createUrlMetricsData(targetUrlId, encodedTargetUrlString, urlMetricsUpdateValueObject);
		}

		if (isDebug == true) {
			FormatUtils.getInstance().logMemoryUsage(
					"maintainUrlMetricsData() ends." + standardLoggingText + ",targetUrlId=" + targetUrlId + ",encodedTargetUrlString=" + encodedTargetUrlString);
		}
	}

	private void updateUrlMetricsUpdateValueObject(UrlMetricsUpdateValueObject urlMetricsUpdateValueObject, UrlMetricsDataEntity urlMetricsDataEntity, Long targetUrlId,
			Integer lastSevenDaysEntrances, String encodedTargetUrlString) {

		if (urlMetricsDataEntity != null) {
			if (urlMetricsDataEntity.getId() != null) {
				urlMetricsUpdateValueObject.setUrlMetricsId(urlMetricsDataEntity.getId());
			}
			if (urlMetricsDataEntity.getRespCode() != null) {
				urlMetricsUpdateValueObject.setUrlMetricsResponseCode(urlMetricsDataEntity.getRespCode());
			}
			if (urlMetricsDataEntity.getInternalLinks() != null) {
				urlMetricsUpdateValueObject.setUrlMetricsInternalLinks(urlMetricsDataEntity.getInternalLinks());
			}
			if (urlMetricsDataEntity.getOutbounds() != null) {
				urlMetricsUpdateValueObject.setUrlMetricsOutboundLinks(urlMetricsDataEntity.getOutbounds());
			}
			if (urlMetricsDataEntity.getPercentMobileEntrances() != null) {
				urlMetricsUpdateValueObject.setPercentMobileEntrancesExisting(urlMetricsDataEntity.getPercentMobileEntrances());
			}
			if (urlMetricsDataEntity.getAssociatedKeywords() != null) {
				urlMetricsUpdateValueObject.setAssociatedKeywordsExisting(urlMetricsDataEntity.getAssociatedKeywords());
			}
			if (urlMetricsDataEntity.getAssociatedKeywordsRanked() != null) {
				urlMetricsUpdateValueObject.setAssociatedKeywordsRankedExisting(urlMetricsDataEntity.getAssociatedKeywordsRanked());
			}
			if (urlMetricsDataEntity.getLinksAcquiredOrganically() != null) {
				urlMetricsUpdateValueObject.setLinksAcquiredOrganicallyExisting(urlMetricsDataEntity.getLinksAcquiredOrganically());
			}
			if (urlMetricsDataEntity.getLinksAcquiredManually() != null) {
				urlMetricsUpdateValueObject.setLinksAcquiredManuallyExisting(urlMetricsDataEntity.getLinksAcquiredManually());
			}
			if (urlMetricsDataEntity.getAssociatedCompetitors() != null) {
				urlMetricsUpdateValueObject.setAssociatedCompetitorsExisting(urlMetricsDataEntity.getAssociatedCompetitors());
			}
		}

		// calculate the latest associated keywords
		Integer associatedKeywordsNew = calculateAssociatedKeywords(targetUrlId);
		if (associatedKeywordsNew != null) {
			urlMetricsUpdateValueObject.setAssociatedKeywordsNew(associatedKeywordsNew);
		}

		// calculate the latest associated keywords ranked
		Integer associatedKeywordsRankedNew = calculateAssociatedKeywordsRanked(targetUrlId);
		if (associatedKeywordsRankedNew != null) {
			urlMetricsUpdateValueObject.setAssociatedKeywordsRankedNew(associatedKeywordsRankedNew);
		}

		// calculate the latest links acquired manually
		Integer linksAcquiredManuallyNew = calculateLinksAcquiredManually(encodedTargetUrlString);
		if (linksAcquiredManuallyNew != null) {
			urlMetricsUpdateValueObject.setLinksAcquiredManuallyNew(linksAcquiredManuallyNew);
		}

		// calculate the latest associated competitors
		Integer associatedCompetitorsNew = calculateAssociatedCompetitors(targetUrlId);
		if (associatedCompetitorsNew != null) {
			urlMetricsUpdateValueObject.setAssociatedCompetitorsNew(associatedCompetitorsNew);
		}

		//if (isDebug == true) {
		//	FormatUtils.getInstance().logMemoryUsage("updateUrlMetricsUpdateValueObject() urlMetricsUpdateValueObject=" + urlMetricsUpdateValueObject.toString());
		//}
	}

	public void updateUrlMetricsData(UrlMetricsDataEntity urlMetricsDataEntity, UrlMetricsUpdateValueObject urlMetricsUpdateValueObject, Long targetUrlId,
			Integer lastSevenDaysEntrances, String encodedTargetUrlString) {

		totalUrlMetricsDataEntityFound++;
		if (isUrlMetricsDataRequiresUpdate(encodedTargetUrlString, urlMetricsUpdateValueObject, urlMetricsDataEntity.getUrl())) {
			urlMetricsUpdateValueObject.setTargetUrlString(encodedTargetUrlString);
			urlMetricsUpdateValueObjectToBeUpdatedList.add(urlMetricsUpdateValueObject);
			totalUrlMetricsDataEntityUpdated++;
			if (urlMetricsUpdateValueObjectToBeUpdatedList.size() >= TargetUrlMetricsUpdateUtils.MAX_RECORDS_PER_BATCH_UPDATE) {
				urlMetricsDataEntityDAO.updateForBatch(urlMetricsUpdateValueObjectToBeUpdatedList);
				urlMetricsUpdateValueObjectToBeUpdatedList = new ArrayList<UrlMetricsUpdateValueObject>();
			}
		} else {
			totalUrlMetricsDataEntityNotUpdated++;
		}
	}

	public boolean isUrlMetricsDataRequiresUpdate(String encodedTargetUrlString, UrlMetricsUpdateValueObject urlMetricsUpdateValueObject,
			String urlMetricsDataTargetUrlString) {

		boolean updateRequired = false;

		// when URL string changed...
		if (!StringUtils.equals(encodedTargetUrlString, urlMetricsDataTargetUrlString)) {
			updateRequired = true;
		}
		// when response code changed...
		else if (urlMetricsUpdateValueObject.getPoliteCrawlResponseCode() == null && urlMetricsUpdateValueObject.getUrlMetricsResponseCode() != null) {
			updateRequired = true;
		} else if (urlMetricsUpdateValueObject.getPoliteCrawlResponseCode() != null && urlMetricsUpdateValueObject.getUrlMetricsResponseCode() == null) {
			updateRequired = true;
		} else if (urlMetricsUpdateValueObject.getPoliteCrawlResponseCode() != null && urlMetricsUpdateValueObject.getUrlMetricsResponseCode() != null
				&& urlMetricsUpdateValueObject.getPoliteCrawlResponseCode().intValue() != urlMetricsUpdateValueObject.getUrlMetricsResponseCode().intValue()) {
			updateRequired = true;
		}
		// when internal links changed..
		else if (urlMetricsUpdateValueObject.getPoliteCrawlInternalLinks() != null && urlMetricsUpdateValueObject.getUrlMetricsInternalLinks() == null) {
			updateRequired = true;
		} else if (urlMetricsUpdateValueObject.getPoliteCrawlInternalLinks() != null && urlMetricsUpdateValueObject.getUrlMetricsInternalLinks() != null
				&& urlMetricsUpdateValueObject.getPoliteCrawlInternalLinks().intValue() != urlMetricsUpdateValueObject.getUrlMetricsInternalLinks().intValue()) {
			updateRequired = true;
		}
		// when outbound links changed..
		else if (urlMetricsUpdateValueObject.getPoliteCrawlOutboundLinks() != null && urlMetricsUpdateValueObject.getUrlMetricsOutboundLinks() == null) {
			updateRequired = true;
		} else if (urlMetricsUpdateValueObject.getPoliteCrawlOutboundLinks() != null && urlMetricsUpdateValueObject.getUrlMetricsOutboundLinks() != null
				&& urlMetricsUpdateValueObject.getPoliteCrawlOutboundLinks().intValue() != urlMetricsUpdateValueObject.getUrlMetricsOutboundLinks().intValue()) {
			updateRequired = true;
		}
		// when percent mobile entrances changed....
		else if (urlMetricsUpdateValueObject.getPercentMobileEntrancesExisting() == null && urlMetricsUpdateValueObject.getPercentMobileEntrancesNew() != null) {
			updateRequired = true;
		} else if (urlMetricsUpdateValueObject.getPercentMobileEntrancesExisting() != null && urlMetricsUpdateValueObject.getPercentMobileEntrancesNew() == null) {
			updateRequired = true;
		} else if (urlMetricsUpdateValueObject.getPercentMobileEntrancesExisting() != null && urlMetricsUpdateValueObject.getPercentMobileEntrancesNew() != null
				&& urlMetricsUpdateValueObject.getPercentMobileEntrancesExisting().compareTo(urlMetricsUpdateValueObject.getPercentMobileEntrancesNew()) != 0) {
			updateRequired = true;
		}
		// when associated keywords changed...
		else if (urlMetricsUpdateValueObject.getAssociatedKeywordsExisting() == null && urlMetricsUpdateValueObject.getAssociatedKeywordsNew() != null) {
			updateRequired = true;
		} else if (urlMetricsUpdateValueObject.getAssociatedKeywordsExisting() != null && urlMetricsUpdateValueObject.getAssociatedKeywordsNew() == null) {
			updateRequired = true;
		} else if (urlMetricsUpdateValueObject.getAssociatedKeywordsExisting() != null && urlMetricsUpdateValueObject.getAssociatedKeywordsNew() != null
				&& urlMetricsUpdateValueObject.getAssociatedKeywordsExisting().intValue() != urlMetricsUpdateValueObject.getAssociatedKeywordsNew().intValue()) {
			updateRequired = true;
		}
		// when associated keywords ranked changed
		else if (urlMetricsUpdateValueObject.getAssociatedKeywordsRankedExisting() == null && urlMetricsUpdateValueObject.getAssociatedKeywordsRankedNew() != null) {
			updateRequired = true;
		} else if (urlMetricsUpdateValueObject.getAssociatedKeywordsRankedExisting() != null && urlMetricsUpdateValueObject.getAssociatedKeywordsRankedNew() == null) {
			updateRequired = true;
		} else if (urlMetricsUpdateValueObject.getAssociatedKeywordsRankedExisting() != null && urlMetricsUpdateValueObject.getAssociatedKeywordsRankedNew() != null
				&& urlMetricsUpdateValueObject.getAssociatedKeywordsRankedExisting().intValue() != urlMetricsUpdateValueObject.getAssociatedKeywordsRankedNew()
						.intValue()) {
			updateRequired = true;
		}
		// when links acquired organically changed
		else if (urlMetricsUpdateValueObject.getLinksAcquiredOrganicallyExisting() == null && urlMetricsUpdateValueObject.getLinksAcquiredOrganicallyNew() != null) {
			updateRequired = true;
		} else if (urlMetricsUpdateValueObject.getLinksAcquiredOrganicallyExisting() != null && urlMetricsUpdateValueObject.getLinksAcquiredOrganicallyNew() == null) {
			updateRequired = true;
		} else if (urlMetricsUpdateValueObject.getLinksAcquiredOrganicallyExisting() != null && urlMetricsUpdateValueObject.getLinksAcquiredOrganicallyNew() != null
				&& urlMetricsUpdateValueObject.getLinksAcquiredOrganicallyExisting().intValue() != urlMetricsUpdateValueObject.getLinksAcquiredOrganicallyNew()
						.intValue()) {
			updateRequired = true;
		}
		// when links acquired manually changed
		else if (urlMetricsUpdateValueObject.getLinksAcquiredManuallyExisting() == null && urlMetricsUpdateValueObject.getLinksAcquiredManuallyNew() != null) {
			updateRequired = true;
		} else if (urlMetricsUpdateValueObject.getLinksAcquiredManuallyExisting() != null && urlMetricsUpdateValueObject.getLinksAcquiredManuallyNew() == null) {
			updateRequired = true;
		} else if (urlMetricsUpdateValueObject.getLinksAcquiredManuallyExisting() != null && urlMetricsUpdateValueObject.getLinksAcquiredManuallyNew() != null
				&& urlMetricsUpdateValueObject.getLinksAcquiredManuallyExisting().intValue() != urlMetricsUpdateValueObject.getLinksAcquiredManuallyNew().intValue()) {
			updateRequired = true;
		}
		// when associated competitors changed
		else if (urlMetricsUpdateValueObject.getAssociatedCompetitorsExisting() == null && urlMetricsUpdateValueObject.getAssociatedCompetitorsNew() != null) {
			updateRequired = true;
		} else if (urlMetricsUpdateValueObject.getAssociatedCompetitorsExisting() != null && urlMetricsUpdateValueObject.getAssociatedCompetitorsNew() == null) {
			updateRequired = true;
		} else if (urlMetricsUpdateValueObject.getAssociatedCompetitorsExisting() != null && urlMetricsUpdateValueObject.getAssociatedCompetitorsNew() != null
				&& urlMetricsUpdateValueObject.getAssociatedCompetitorsExisting().intValue() != urlMetricsUpdateValueObject.getAssociatedCompetitorsNew().intValue()) {
			updateRequired = true;
		}

		if (updateRequired == true) {
			if (urlMetricsUpdateValueObject.getPoliteCrawlInternalLinks() == null && urlMetricsUpdateValueObject.getUrlMetricsInternalLinks() != null) {
				urlMetricsUpdateValueObject.setPoliteCrawlInternalLinks(urlMetricsUpdateValueObject.getUrlMetricsInternalLinks());
			}
			if (urlMetricsUpdateValueObject.getPoliteCrawlOutboundLinks() == null && urlMetricsUpdateValueObject.getUrlMetricsOutboundLinks() != null) {
				urlMetricsUpdateValueObject.setPoliteCrawlOutboundLinks(urlMetricsUpdateValueObject.getUrlMetricsOutboundLinks());
			}
		}

		return updateRequired;
	}

	public void createUrlMetricsData(Long targetUrlId, String encodedTargetUrlString, UrlMetricsUpdateValueObject urlMetricsUpdateValueObject) {

		totalUrlMetricsDataEntityNotFound++;

		// create a new 't_url_metrics_data' when not already in database
		UrlMetricsDataEntity urlMetricsDataEntityToBeCreated = getUrlMetricsDataEntity(targetUrlId, encodedTargetUrlString, urlMetricsUpdateValueObject);

		urlMetricsDataEntityToBeCreatedList.add(urlMetricsDataEntityToBeCreated);
		//if (isDebug == true) {
		//	FormatUtils.getInstance().logMemoryUsage("createUrlMetricsData() " + standardLoggingText + ",urlMetricsDataEntityToBeCreated="
		//			+ urlMetricsDataEntityToBeCreated.toString());
		//}
		totalUrlMetricsDataEntityCreated++;
		if (urlMetricsDataEntityToBeCreatedList.size() >= TargetUrlMetricsUpdateUtils.MAX_RECORDS_PER_BATCH_UPDATE) {
			urlMetricsDataEntityDAO.insertMultiRowsBatch(urlMetricsDataEntityToBeCreatedList);
			urlMetricsDataEntityToBeCreatedList = new ArrayList<UrlMetricsDataEntity>();
		}
	}

	private UrlMetricsDataEntity getUrlMetricsDataEntity(Long targetUrlId, String encodedTargetUrlString, UrlMetricsUpdateValueObject urlMetricsUpdateValueObject) {

		UrlMetricsDataEntity urlMetricsDataEntityToBeCreated = new UrlMetricsDataEntity();
		urlMetricsDataEntityToBeCreated.setUrlId(targetUrlId);
		urlMetricsDataEntityToBeCreated.setUrl(encodedTargetUrlString);
		urlMetricsDataEntityToBeCreated.setType(UrlMetricsDataEntity.TYPE_TARGET_URL);
		urlMetricsDataEntityToBeCreated.setLastUpdate(todayDate);
		urlMetricsDataEntityToBeCreated.setRespCode(urlMetricsUpdateValueObject.getPoliteCrawlResponseCode());
		urlMetricsDataEntityToBeCreated.setRespcodeUpdateDate(
				FormatUtils.getInstance().toDate(String.valueOf(urlMetricsUpdateValueObject.getPoliteCrawlTrackDate()), IConstants.DATE_FORMAT_YYYYMMDD));
		urlMetricsDataEntityToBeCreated.setHtmlContentUpdateDate(todayDate);
		urlMetricsDataEntityToBeCreated.setInternalLinks(urlMetricsUpdateValueObject.getPoliteCrawlInternalLinks());
		urlMetricsDataEntityToBeCreated.setOutbounds(urlMetricsUpdateValueObject.getPoliteCrawlOutboundLinks());

		// percent_mobile_entrances
		urlMetricsDataEntityToBeCreated.setPercentMobileEntrances(urlMetricsUpdateValueObject.getPercentMobileEntrancesNew());

		// associated_keywords
		urlMetricsDataEntityToBeCreated.setAssociatedKeywords(urlMetricsUpdateValueObject.getAssociatedKeywordsNew());

		// associated_keywords_ranked
		urlMetricsDataEntityToBeCreated.setAssociatedKeywordsRanked(urlMetricsUpdateValueObject.getAssociatedKeywordsRankedNew());

		// links_acquired_organically
		urlMetricsDataEntityToBeCreated.setLinksAcquiredOrganically(urlMetricsUpdateValueObject.getLinksAcquiredOrganicallyNew());

		// links_acquired_manually
		urlMetricsDataEntityToBeCreated.setLinksAcquiredManually(urlMetricsUpdateValueObject.getLinksAcquiredManuallyNew());

		// associated_competitors
		urlMetricsDataEntityToBeCreated.setAssociatedCompetitors(urlMetricsUpdateValueObject.getAssociatedCompetitorsNew());

		//if (isDebug == true) {
		//	FormatUtils.getInstance().logMemoryUsage("getUrlMetricsDataEntity() " + standardLoggingText + ",targetUrlId=" + targetUrlId + ",encodedTargetUrlString="
		//			+ encodedTargetUrlString);
		//	FormatUtils.getInstance().logMemoryUsage(
		//			"getUrlMetricsDataEntity() " + standardLoggingText + ",urlMetricsUpdateValueObject=" + urlMetricsUpdateValueObject.toString());
		//	FormatUtils.getInstance().logMemoryUsage("getUrlMetricsDataEntity() " + standardLoggingText + ",urlMetricsDataEntityToBeCreated="
		//			+ urlMetricsDataEntityToBeCreated.toString());
		//}

		return urlMetricsDataEntityToBeCreated;
	}

	public void maintainUrlMetricsData() {
		if (urlMetricsDataEntityToBeCreatedList.size() > 0) {
			urlMetricsDataEntityDAO.insertMultiRowsBatch(urlMetricsDataEntityToBeCreatedList);
			urlMetricsDataEntityToBeCreatedList = new ArrayList<UrlMetricsDataEntity>();
		}
		if (urlMetricsUpdateValueObjectToBeUpdatedList.size() > 0) {
			urlMetricsDataEntityDAO.updateForBatch(urlMetricsUpdateValueObjectToBeUpdatedList);
			urlMetricsUpdateValueObjectToBeUpdatedList = new ArrayList<UrlMetricsUpdateValueObject>();
		}
	}

	public void maintainTargetCriteriaData(Long targetUrlId, String encodedTargetUrlString, UrlMetricsUpdateValueObject urlMetricsUpdateValueObject,
			TargetURLCriteria targetURLCriteriaExisting) {

		if (isDebug == true) {
			FormatUtils.getInstance().logMemoryUsage(
					"maintainTargetCriteriaData() begins." + standardLoggingText + ",targetUrlId=" + targetUrlId + ",encodedTargetUrlString=" + encodedTargetUrlString);
		}

		//TargetURLCriteria targetURLCriteriaExisting = targetURLCriteriaDAO.get(targetUrlId, domainId);

		// update target URL's 'targeturl_criteria' record
		if (targetURLCriteriaExisting != null) {
			updateTargetCriteriaData(targetUrlId, encodedTargetUrlString, targetURLCriteriaExisting, urlMetricsUpdateValueObject);
		}
		// when the target URL does not have a corresponding 'targeturl_criteria' record...
		else {
			createTargetCriteriaData(targetUrlId, encodedTargetUrlString, urlMetricsUpdateValueObject);
		}

		if (isDebug == true) {
			FormatUtils.getInstance().logMemoryUsage(
					"maintainTargetCriteriaData() ends." + standardLoggingText + ",targetUrlId=" + targetUrlId + ",encodedTargetUrlString=" + encodedTargetUrlString);
		}
	}

	public void updateTargetCriteriaData(Long targetUrlId, String encodedTargetUrlString, TargetURLCriteria targetURLCriteriaExisting,
			UrlMetricsUpdateValueObject urlMetricsUpdateValueObject) {
		totalTargetUrlCriteriaFound++;
		TargetURLCriteria targetURLCriteriaNew = getTargetURLCriteria(targetURLCriteriaExisting.getId(), targetUrlId, encodedTargetUrlString,
				urlMetricsUpdateValueObject);
		if (isTargetCriteriaDataRequiresUpdate(targetURLCriteriaExisting, targetURLCriteriaNew) == true) {
			targetURLCriteriaToBeUpdatedList.add(targetURLCriteriaNew);
			totalTargetUrlCriteriaUpdated++;
			if (targetURLCriteriaToBeUpdatedList.size() >= TargetUrlMetricsUpdateUtils.MAX_RECORDS_PER_BATCH_UPDATE) {
				targetURLCriteriaDAO.updateForBatch(targetURLCriteriaToBeUpdatedList, standardLoggingText);
				targetURLCriteriaToBeUpdatedList = new ArrayList<TargetURLCriteria>();
			}
		} else {
			totalTargetUrlCriteriaNotUpdated++;
		}
	}

	public void createTargetCriteriaData(Long targetUrlId, String encodedTargetUrlString, UrlMetricsUpdateValueObject urlMetricsUpdateValueObject) {
		totalTargetUrlCriteriaNotFound++;
		Integer targetUrlCriteriaId = null;
		TargetURLCriteria targetURLCriteriaNew = getTargetURLCriteria(targetUrlCriteriaId, targetUrlId, encodedTargetUrlString, urlMetricsUpdateValueObject);
		if (targetURLCriteriaNew != null) {
			targetURLCriteriaToBeCreatedList.add(targetURLCriteriaNew);
			totalTargetUrlCriteriaCreated++;
			if (targetURLCriteriaToBeCreatedList.size() >= TargetUrlMetricsUpdateUtils.MAX_RECORDS_PER_BATCH_UPDATE) {
				targetURLCriteriaDAO.insertMultiRowsBatch(targetURLCriteriaToBeCreatedList);
				targetURLCriteriaToBeCreatedList = new ArrayList<TargetURLCriteria>();
			}
		}
	}

	public void maintainTargetCriteriaData() {
		if (targetURLCriteriaToBeUpdatedList.size() > 0) {
			targetURLCriteriaDAO.updateForBatch(targetURLCriteriaToBeUpdatedList, standardLoggingText);
			targetURLCriteriaToBeUpdatedList = new ArrayList<TargetURLCriteria>();
		}
		if (targetURLCriteriaToBeCreatedList.size() > 0) {
			targetURLCriteriaDAO.insertMultiRowsBatch(targetURLCriteriaToBeCreatedList);
			targetURLCriteriaToBeCreatedList = new ArrayList<TargetURLCriteria>();
		}
	}

	public TargetURLCriteria getTargetURLCriteria(Integer targetURLCriteriaId, Long targetUrlId, String encodedTargetUrlString,
			UrlMetricsUpdateValueObject urlMetricsUpdateValueObject) {

		TargetURLCriteria targetURLCriteria = new TargetURLCriteria();

		// id
		targetURLCriteria.setId(targetURLCriteriaId);

		// own_domain_id
		targetURLCriteria.setOwnDomainId(domainId);

		// targeturl_id
		targetURLCriteria.setTargeturlId(targetUrlId);

		// url
		targetURLCriteria.setUrl(encodedTargetUrlString);

		// cached_date
		targetURLCriteria.setCachedDate(todayDate);

		// resp_code
		targetURLCriteria.setRespCode(urlMetricsUpdateValueObject.getPoliteCrawlResponseCode());

		// entrances_last7days
		targetURLCriteria.setEntrancesLast7days(urlMetricsUpdateValueObject.getTargetUrlWeekEntrances());

		// count_associated_keyword
		targetURLCriteria.setCountAssociatedKeyword(urlMetricsUpdateValueObject.getAssociatedKeywordsNew());

		if (urlMetricsUpdateValueObject != null && urlMetricsUpdateValueObject.getPoliteCrawlResponseCode() != null
				&& urlMetricsUpdateValueObject.getPoliteCrawlResponseCode().intValue() == RESP_CODE_200) {

			// page_title
			targetURLCriteria.setPageTitle(truncateToMaximumLength(targetUrlId, encodedTargetUrlString, urlMetricsUpdateValueObject.getTitle(), MAX_PAGE_TITLE_LENGTH));

			// page_meta_keyword
			targetURLCriteria.setPageMetaKeyword(
					truncateToMaximumLength(targetUrlId, encodedTargetUrlString, urlMetricsUpdateValueObject.getMetaKeywords(), MAX_PAGE_META_KEYWORD_LENGTH));

			// page_meta_desc
			targetURLCriteria.setPageMetaDesc(
					truncateToMaximumLength(targetUrlId, encodedTargetUrlString, urlMetricsUpdateValueObject.getMetaDesc(), MAX_PAGE_META_DESC_LENGTH));

			// page_h1
			targetURLCriteria.setPageH1(truncateToMaximumLength(targetUrlId, encodedTargetUrlString, convertListToString(urlMetricsUpdateValueObject.getH1List()),
					MAX_PAGE_HEADING_LENGTH));

			// page_h2
			targetURLCriteria.setPageH2(truncateToMaximumLength(targetUrlId, encodedTargetUrlString, convertListToString(urlMetricsUpdateValueObject.getH2List()),
					MAX_PAGE_HEADING_LENGTH));

			// page_title_length
			if (StringUtils.isNotBlank(targetURLCriteria.getPageTitle())) {
				targetURLCriteria.setPageTitleLength(getDecodedStringLength(targetURLCriteria.getPageTitle()));
			} else {
				targetURLCriteria.setPageTitleLength(0);
			}

			// page_meta_keyword_length
			if (StringUtils.isNotBlank(targetURLCriteria.getPageMetaKeyword())) {
				targetURLCriteria.setPageMetaKeywordLength(getDecodedStringLength(targetURLCriteria.getPageMetaKeyword()));
			} else {
				targetURLCriteria.setPageMetaKeywordLength(0);
			}

			// page_meta_desc_length
			if (StringUtils.isNotBlank(targetURLCriteria.getPageMetaDesc())) {
				targetURLCriteria.setPageMetaDescLength(getDecodedStringLength(targetURLCriteria.getPageMetaDesc()));
			} else {
				targetURLCriteria.setPageMetaDescLength(0);
			}

			// page_h1_count
			targetURLCriteria.setPageH1Count(calculateOccurrences(urlMetricsUpdateValueObject.getH1List()));

			// page_h2_count
			targetURLCriteria.setPageH2Count(calculateOccurrences(urlMetricsUpdateValueObject.getH2List()));

			// meta_robot_index
			if (StringUtils.containsIgnoreCase(urlMetricsUpdateValueObject.getMetaRobots(), NOINDEX)) {
				targetURLCriteria.setMetaRobotIndex(TargetURLCriteria.ROBOT_INDEX_NOINDEX);
			} else if (StringUtils.containsIgnoreCase(urlMetricsUpdateValueObject.getMetaRobots(), INDEX)) {
				targetURLCriteria.setMetaRobotIndex(TargetURLCriteria.ROBOT_INDEX_INDEX);
			} else {
				targetURLCriteria.setMetaRobotIndex(TargetURLCriteria.ROBOT_INDEX_NOTEXIST);
			}

			// meta_robot_follow
			if (StringUtils.containsIgnoreCase(urlMetricsUpdateValueObject.getMetaRobots(), NOFOLLOW)) {
				targetURLCriteria.setMetaRobotFollow(TargetURLCriteria.ROBOT_FOLLOW_NOTFOLLOW);
			} else if (StringUtils.containsIgnoreCase(urlMetricsUpdateValueObject.getMetaRobots(), FOLLOW)) {
				targetURLCriteria.setMetaRobotFollow(TargetURLCriteria.ROBOT_FOLLOW_FOLLOW);
			} else {
				targetURLCriteria.setMetaRobotFollow(TargetURLCriteria.ROBOT_FOLLOW_NOTEXIST);
			}

		} else {

			// page_title
			targetURLCriteria.setPageTitle(null);

			// page_meta_keyword
			targetURLCriteria.setPageMetaKeyword(null);

			// page_meta_desc
			targetURLCriteria.setPageMetaDesc(null);

			// page_h1
			targetURLCriteria.setPageH1(null);

			// page_h2
			targetURLCriteria.setPageH2(null);

			// page_title_length
			targetURLCriteria.setPageTitleLength(null);

			// page_meta_keyword_length
			targetURLCriteria.setPageMetaKeywordLength(null);

			// page_meta_desc_length
			targetURLCriteria.setPageMetaDescLength(null);

			// page_h1_count			
			targetURLCriteria.setPageH1Count(null);

			// page_h2_count
			targetURLCriteria.setPageH2Count(null);

			// meta_robot_index
			targetURLCriteria.setMetaRobotIndex(null);

			// meta_robot_follow
			targetURLCriteria.setMetaRobotFollow(null);
		}
		return targetURLCriteria;
	}

	public boolean isTargetCriteriaDataRequiresUpdate(TargetURLCriteria targetURLCriteriaExisting, TargetURLCriteria targetURLCriteriaNew) {

		boolean isRequireUpdate = false;

		// url
		if (!StringUtils.equals(targetURLCriteriaNew.getUrl(), targetURLCriteriaExisting.getUrl())) {
			isRequireUpdate = true;
		}
		// resp_code
		else if (targetURLCriteriaNew.getRespCode() == null && targetURLCriteriaExisting.getRespCode() != null) {
			isRequireUpdate = true;
		} else if (targetURLCriteriaNew.getRespCode() != null && targetURLCriteriaExisting.getRespCode() == null) {
			isRequireUpdate = true;
		} else if (targetURLCriteriaNew.getRespCode() != null && targetURLCriteriaExisting.getRespCode() != null
				&& targetURLCriteriaNew.getRespCode().intValue() != targetURLCriteriaExisting.getRespCode().intValue()) {
			isRequireUpdate = true;
		}
		// entrances_last7days
		else if (targetURLCriteriaNew.getEntrancesLast7days() == null && targetURLCriteriaExisting.getEntrancesLast7days() != null) {
			isRequireUpdate = true;
		} else if (targetURLCriteriaNew.getEntrancesLast7days() != null && targetURLCriteriaExisting.getEntrancesLast7days() == null) {
			isRequireUpdate = true;
		} else if (targetURLCriteriaNew.getEntrancesLast7days() != null && targetURLCriteriaExisting.getEntrancesLast7days() != null
				&& targetURLCriteriaNew.getEntrancesLast7days().intValue() != targetURLCriteriaExisting.getEntrancesLast7days().intValue()) {
			isRequireUpdate = true;
		}
		// page_title
		else if (!StringUtils.equalsIgnoreCase(targetURLCriteriaNew.getPageTitle(), targetURLCriteriaExisting.getPageTitle())) {
			isRequireUpdate = true;
		}
		// page_meta_keyword
		else if (!StringUtils.equalsIgnoreCase(targetURLCriteriaNew.getPageMetaKeyword(), targetURLCriteriaExisting.getPageMetaKeyword())) {
			isRequireUpdate = true;
		}
		// page_meta_desc
		else if (!StringUtils.equalsIgnoreCase(targetURLCriteriaNew.getPageMetaDesc(), targetURLCriteriaExisting.getPageMetaDesc())) {
			isRequireUpdate = true;
		}
		// page_h1
		else if (!StringUtils.equalsIgnoreCase(targetURLCriteriaNew.getPageH1(), targetURLCriteriaExisting.getPageH1())) {
			isRequireUpdate = true;
		}
		// page_h2
		else if (!StringUtils.equalsIgnoreCase(targetURLCriteriaNew.getPageH2(), targetURLCriteriaExisting.getPageH2())) {
			isRequireUpdate = true;
		}
		// page_title_length
		else if (targetURLCriteriaNew.getPageTitleLength() == null && targetURLCriteriaExisting.getPageTitleLength() != null) {
			isRequireUpdate = true;
		} else if (targetURLCriteriaNew.getPageTitleLength() != null && targetURLCriteriaExisting.getPageTitleLength() == null) {
			isRequireUpdate = true;
		} else if (targetURLCriteriaNew.getPageTitleLength() != null && targetURLCriteriaExisting.getPageTitleLength() != null
				&& targetURLCriteriaNew.getPageTitleLength().intValue() != targetURLCriteriaExisting.getPageTitleLength().intValue()) {
			isRequireUpdate = true;
		}
		// page_meta_keyword_length
		else if (targetURLCriteriaNew.getPageMetaKeywordLength() == null && targetURLCriteriaExisting.getPageMetaKeywordLength() != null) {
			isRequireUpdate = true;
		} else if (targetURLCriteriaNew.getPageMetaKeywordLength() != null && targetURLCriteriaExisting.getPageMetaKeywordLength() == null) {
			isRequireUpdate = true;
		} else if (targetURLCriteriaNew.getPageMetaKeywordLength() != null && targetURLCriteriaExisting.getPageMetaKeywordLength() != null
				&& targetURLCriteriaNew.getPageMetaKeywordLength().intValue() != targetURLCriteriaExisting.getPageMetaKeywordLength().intValue()) {
			isRequireUpdate = true;
		}
		// page_meta_desc_length
		else if (targetURLCriteriaNew.getPageMetaDescLength() == null && targetURLCriteriaExisting.getPageMetaDescLength() != null) {
			isRequireUpdate = true;
		} else if (targetURLCriteriaNew.getPageMetaDescLength() != null && targetURLCriteriaExisting.getPageMetaDescLength() == null) {
			isRequireUpdate = true;
		} else if (targetURLCriteriaNew.getPageMetaDescLength() != null && targetURLCriteriaExisting.getPageMetaDescLength() != null
				&& targetURLCriteriaNew.getPageMetaDescLength().intValue() != targetURLCriteriaExisting.getPageMetaDescLength().intValue()) {
			isRequireUpdate = true;
		}
		// page_h1_count
		else if (targetURLCriteriaNew.getPageH1Count() == null && targetURLCriteriaExisting.getPageH1Count() != null) {
			isRequireUpdate = true;
		} else if (targetURLCriteriaNew.getPageH1Count() != null && targetURLCriteriaExisting.getPageH1Count() == null) {
			isRequireUpdate = true;
		} else if (targetURLCriteriaNew.getPageH1Count() != null && targetURLCriteriaExisting.getPageH1Count() != null
				&& targetURLCriteriaNew.getPageH1Count().intValue() != targetURLCriteriaExisting.getPageH1Count().intValue()) {
			isRequireUpdate = true;
		}
		// page_h2_count
		else if (targetURLCriteriaNew.getPageH2Count() == null && targetURLCriteriaExisting.getPageH2Count() != null) {
			isRequireUpdate = true;
		} else if (targetURLCriteriaNew.getPageH2Count() != null && targetURLCriteriaExisting.getPageH2Count() == null) {
			isRequireUpdate = true;
		} else if (targetURLCriteriaNew.getPageH2Count() != null && targetURLCriteriaExisting.getPageH2Count() != null
				&& targetURLCriteriaNew.getPageH2Count().intValue() != targetURLCriteriaExisting.getPageH2Count().intValue()) {
			isRequireUpdate = true;
		}
		// meta_robot_index
		else if (targetURLCriteriaNew.getMetaRobotIndex() == null && targetURLCriteriaExisting.getMetaRobotIndex() != null) {
			isRequireUpdate = true;
		} else if (targetURLCriteriaNew.getMetaRobotIndex() != null && targetURLCriteriaExisting.getMetaRobotIndex() == null) {
			isRequireUpdate = true;
		} else if (targetURLCriteriaNew.getMetaRobotIndex() != null && targetURLCriteriaExisting.getMetaRobotIndex() != null
				&& targetURLCriteriaNew.getMetaRobotIndex().intValue() != targetURLCriteriaExisting.getMetaRobotIndex().intValue()) {
			isRequireUpdate = true;
		}
		// meta_robot_follow
		else if (targetURLCriteriaNew.getMetaRobotFollow() == null && targetURLCriteriaExisting.getMetaRobotFollow() != null) {
			isRequireUpdate = true;
		} else if (targetURLCriteriaNew.getMetaRobotFollow() != null && targetURLCriteriaExisting.getMetaRobotFollow() == null) {
			isRequireUpdate = true;
		} else if (targetURLCriteriaNew.getMetaRobotFollow() != null && targetURLCriteriaExisting.getMetaRobotFollow() != null
				&& targetURLCriteriaNew.getMetaRobotFollow().intValue() != targetURLCriteriaExisting.getMetaRobotFollow().intValue()) {
			isRequireUpdate = true;
		}
		// count_associated_keyword
		else if (targetURLCriteriaNew.getCountAssociatedKeyword() == null && targetURLCriteriaExisting.getCountAssociatedKeyword() != null) {
			isRequireUpdate = true;
		} else if (targetURLCriteriaNew.getCountAssociatedKeyword() != null && targetURLCriteriaExisting.getCountAssociatedKeyword() == null) {
			isRequireUpdate = true;
		} else if (targetURLCriteriaNew.getCountAssociatedKeyword() != null && targetURLCriteriaExisting.getCountAssociatedKeyword() != null
				&& targetURLCriteriaNew.getCountAssociatedKeyword().intValue() != targetURLCriteriaExisting.getCountAssociatedKeyword().intValue()) {
			isRequireUpdate = true;
		}

		//if (isDebug == true) {
		//	FormatUtils.getInstance().logMemoryUsage("isTargetCriteriaDataRequiresUpdate() " + standardLoggingText + ",targetURLCriteriaExisting="
		//			+ targetURLCriteriaExisting.toString());
		//	FormatUtils.getInstance().logMemoryUsage(
		//			"isTargetCriteriaDataRequiresUpdate() " + standardLoggingText + ",targetURLCriteriaNew=" + targetURLCriteriaNew.toString());
		//	FormatUtils.getInstance().logMemoryUsage("isTargetCriteriaDataRequiresUpdate() " + standardLoggingText + ",isRequireUpdate=" + isRequireUpdate);
		//}

		return isRequireUpdate;
	}

	public String formatFloatToOneDecimalPlace(Float input) {
		return String.format(IConstants.FLOAT_FORMAT_ONE_DECIMAL_PLACE, input);
	}

	public String convertListToString(List<String> stringList) {
		String output = null;
		StringBuilder stringBuilder = null;
		if (stringList != null && stringList.size() > 0) {
			for (String testString : stringList) {
				if (stringBuilder == null) {
					stringBuilder = new StringBuilder();
					stringBuilder.append(testString);
				} else {
					stringBuilder.append(IConstants.ONE_SPACE);
					stringBuilder.append(testString);
				}
			}
			output = stringBuilder.toString();
		}
		return output;
	}

	public Integer calculateOccurrences(List<String> stringList) {
		int output = 0;
		if (stringList != null && stringList.size() > 0) {
			output = stringList.size();
		}
		return output;
	}

	public String truncateToMaximumLength(Long targetUrlId, String encodedTargetUrlString, String input, int maximumLength) {
		String output = null;
		String encodedInput = null;
		if (StringUtils.isNotBlank(input)) {
			try {
				encodedInput = URLEncoder.encode(input, IConstants.UTF_8);
				if (StringUtils.length(encodedInput) > maximumLength) {
					encodedInput = StringUtils.substring(encodedInput, 0, maximumLength);
					encodedInput = getValidEncodedString(encodedInput);
				}
				output = encodedInput;
			} catch (Exception e) {
				FormatUtils.getInstance().logMemoryUsage("truncateToMaximumLength() " + standardLoggingText + ",targetUrlId=" + targetUrlId + ",encodedTargetUrlString="
						+ encodedTargetUrlString + ",input=" + input + ",exception message=" + e.getMessage());
				e.printStackTrace();
			}
		} else {
			output = IConstants.EMPTY_STRING;
		}
		return output;
	}

	public String getValidEncodedString(String inputEncodedString) {
		String outputEncodedString = inputEncodedString;
		int retryCount = 0;
		doWhileLoop1: while (retryCount < MAX_RETRY_COUNT) {
			try {
				URLDecoder.decode(inputEncodedString, IConstants.UTF_8);
				outputEncodedString = inputEncodedString;
				break doWhileLoop1;
			} catch (Exception e) {
				//e.printStackTrace();
				FormatUtils.getInstance().logMemoryUsage("getValidEncodedString() inputEncodedString=" + inputEncodedString + ",exception=" + e.getMessage());
				inputEncodedString = StringUtils.substring(inputEncodedString, 0, inputEncodedString.length() - 1);
				retryCount++;
			}
		}
		return outputEncodedString;
	}

	public int getDecodedStringLength(String inputString) {
		int decodedStringLength = 0;
		String decodedString = null;
		try {
			if (StringUtils.isNotBlank(inputString)) {
				decodedString = URLDecoder.decode(inputString, IConstants.UTF_8);
				decodedStringLength = StringUtils.length(decodedString);
			} else {
				decodedStringLength = 0;
			}
		} catch (Exception e) {
			decodedStringLength = StringUtils.length(inputString);
			FormatUtils.getInstance()
					.logMemoryUsage("getDecodedStringLength() " + standardLoggingText + ",inputString=" + inputString + ",exception message=" + e.getMessage());
			e.printStackTrace();
		}
		return decodedStringLength;
	}

	public int getTotalUrlMetricsDataEntityFound() {
		return totalUrlMetricsDataEntityFound;
	}

	public void setTotalUrlMetricsDataEntityFound(int totalUrlMetricsDataEntityFound) {
		this.totalUrlMetricsDataEntityFound = totalUrlMetricsDataEntityFound;
	}

	public int getTotalUrlMetricsDataEntityUpdated() {
		return totalUrlMetricsDataEntityUpdated;
	}

	public void setTotalUrlMetricsDataEntityUpdated(int totalUrlMetricsDataEntityUpdated) {
		this.totalUrlMetricsDataEntityUpdated = totalUrlMetricsDataEntityUpdated;
	}

	public int getTotalUrlMetricsDataEntityNotUpdated() {
		return totalUrlMetricsDataEntityNotUpdated;
	}

	public void setTotalUrlMetricsDataEntityNotUpdated(int totalUrlMetricsDataEntityNotUpdated) {
		this.totalUrlMetricsDataEntityNotUpdated = totalUrlMetricsDataEntityNotUpdated;
	}

	public int getTotalUrlMetricsDataEntityNotFound() {
		return totalUrlMetricsDataEntityNotFound;
	}

	public void setTotalUrlMetricsDataEntityNotFound(int totalUrlMetricsDataEntityNotFound) {
		this.totalUrlMetricsDataEntityNotFound = totalUrlMetricsDataEntityNotFound;
	}

	public int getTotalUrlMetricsDataEntityCreated() {
		return totalUrlMetricsDataEntityCreated;
	}

	public void setTotalUrlMetricsDataEntityCreated(int totalUrlMetricsDataEntityCreated) {
		this.totalUrlMetricsDataEntityCreated = totalUrlMetricsDataEntityCreated;
	}

	public int getTotalTargetUrlCriteriaFound() {
		return totalTargetUrlCriteriaFound;
	}

	public void setTotalTargetUrlCriteriaFound(int totalTargetUrlCriteriaFound) {
		this.totalTargetUrlCriteriaFound = totalTargetUrlCriteriaFound;
	}

	public int getTotalTargetUrlCriteriaUpdated() {
		return totalTargetUrlCriteriaUpdated;
	}

	public void setTotalTargetUrlCriteriaUpdated(int totalTargetUrlCriteriaUpdated) {
		this.totalTargetUrlCriteriaUpdated = totalTargetUrlCriteriaUpdated;
	}

	public int getTotalTargetUrlCriteriaNotUpdated() {
		return totalTargetUrlCriteriaNotUpdated;
	}

	public void setTotalTargetUrlCriteriaNotUpdated(int totalTargetUrlCriteriaNotUpdated) {
		this.totalTargetUrlCriteriaNotUpdated = totalTargetUrlCriteriaNotUpdated;
	}

	public int getTotalTargetUrlCriteriaNotFound() {
		return totalTargetUrlCriteriaNotFound;
	}

	public void setTotalTargetUrlCriteriaNotFound(int totalTargetUrlCriteriaNotFound) {
		this.totalTargetUrlCriteriaNotFound = totalTargetUrlCriteriaNotFound;
	}

	public int getTotalTargetUrlCriteriaCreated() {
		return totalTargetUrlCriteriaCreated;
	}

	public void setTotalTargetUrlCriteriaCreated(int totalTargetUrlCriteriaCreated) {
		this.totalTargetUrlCriteriaCreated = totalTargetUrlCriteriaCreated;
	}

	private Integer calculateAssociatedKeywords(Long targetUrlId) {
		return keywordTargeturlEntityDAO.getAssociatedManagedKeywords(PAGE_CLARITY_VERSION, targetUrlId);
		//return keywordPageRelEntityDAO.getAssociatedManagedKeywords(targetUrlId);
	}

	private Integer calculateAssociatedKeywordsRanked(Long targetUrlId) {
		return keywordTargeturlEntityDAO.getRankedAssociatedManagedKeywords(PAGE_CLARITY_VERSION, targetUrlId);
		//return keywordPageRelEntityDAO.getRankedAssociatedManagedKeywords(targetUrlId);
	}

	private Integer calculateLinksAcquiredManually(String encodedTargetUrlString) {
		Integer linksAcquiredManually = null;
		Boolean isRemoveTrailingSlash = true;
		String trimmedUrlString = null;
		String encodedUrlString = null;
		String targetUrlMd5Hash = null;
		String decodedTargetUrlString = null;
		try {
			decodedTargetUrlString = URLDecoder.decode(encodedTargetUrlString, IConstants.UTF_8);
			trimmedUrlString = StringUtils.replace(decodedTargetUrlString, SLASH_POUND_SLASH, SLASH);
			trimmedUrlString = StringUtils.substringBefore(trimmedUrlString, SLASH_POUND);
			encodedUrlString = FormatUtils.getInstance().encodeMajesticUrl(trimmedUrlString, isRemoveTrailingSlash);
			encodedUrlString = StringUtils.replace(encodedUrlString, QUOTE, SLASH_QUOTE);
			if (StringUtils.isNotBlank(encodedUrlString)) {
				new URL(encodedUrlString);
				targetUrlMd5Hash = JCSManager.Md5(encodedUrlString);
				linksAcquiredManually = userBacklinkDAO.getTargetUrlCount(domainId, targetUrlMd5Hash);
			}
		} catch (Exception e) {
			FormatUtils.getInstance().logMemoryUsage(
					"getLinksAcquiredManually() " + standardLoggingText + ",targetUrlMd5Hash=" + targetUrlMd5Hash + ",exception message=" + e.getMessage());
			e.printStackTrace();
		}
		return linksAcquiredManually;
	}

	private Integer calculateAssociatedCompetitors(Long targetUrlId) {
		return keywordTargeturlEntityDAO.getCompetitorUrlNum(PAGE_CLARITY_VERSION, targetUrlId);
		//return keywordPageRelEntityDAO.getCompetitorUrlNum(targetUrlId);
	}

	public String getEncodedUrlString(String inputUrlString) {
		String encodedUrlString = null;
		String decodedUrlString = null;

		// decode the input URL string in case it is already encoded
		try {
			decodedUrlString = URLDecoder.decode(inputUrlString, IConstants.UTF_8);
		} catch (Exception e) {
			//e.printStackTrace();
			decodedUrlString = inputUrlString;
		}

		// encode the decoded URL string
		try {
			encodedUrlString = UriUtils.encodeHttpUrl(decodedUrlString, IConstants.UTF_8);
		} catch (Exception e) {
			//e.printStackTrace();
			encodedUrlString = decodedUrlString;
		}

		return encodedUrlString;
	}
}
