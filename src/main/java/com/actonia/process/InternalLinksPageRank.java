package com.actonia.process;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.actonia.IConstants;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.InternalLinksPageRankUtils;
import com.actonia.value.object.PageRankNode;

public class InternalLinksPageRank {

	private boolean isDebug = true; //debug

	public InternalLinksPageRank() {
		super();
	}

	public static void main(String[] args) {
		try {
			new InternalLinksPageRank().process();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void process() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("process() begins.");

		// map key = source node ID
		// map value = target node count
		Map<Integer, Integer> sourceNodeIdTargetNodeCountMap = null;

		initialize();

		// create data
		createPageRankNodeTestData();
		createPageRankEdgeTestData();

		// determine the total number of nodes
		//int totalNodes = pageRankNodeDAO.getTotalNodes();
		int totalNodes = InternalLinksPageRankUtils.getInstance().getTotalNodes();

		// retrieve a list of all node IDs
		//List<Integer> nodeIdList = pageRankNodeDAO.getNodeIdList();
		List<Integer> nodeIdList = InternalLinksPageRankUtils.getInstance().getNodeIdList();

		// retrieve map of source node ID - target node count
		sourceNodeIdTargetNodeCountMap = InternalLinksPageRankUtils.getInstance().getSourceNodeIdTargetNodeCountMap();

		updateNodeCount(totalNodes, nodeIdList, sourceNodeIdTargetNodeCountMap);

		convergeNodes();

		FormatUtils.getInstance().logMemoryUsage("process() ends. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void initialize() throws Exception {
		FormatUtils.getInstance().logMemoryUsage("initialize() begins.");

		//pageRankNodeDAO.reset();
		InternalLinksPageRankUtils.getInstance().resetNodeIdPageRankNodeMap();

		//pageRankEdgeDAO.reset();
		InternalLinksPageRankUtils.getInstance().resetSourceNodeIdTargetNodeIdListMap();

		FormatUtils.getInstance().logMemoryUsage("initialize() ends.");
	}

	//	private Map<Integer, Integer> getSourceNodeIdTargetNodeCountMap() {
	//		long startTimestamp = System.currentTimeMillis();
	//		FormatUtils.getInstance().logMemoryUsage("getSourceNodeIdTargetNodeCountMap() begins.");
	//		Map<Integer, Integer> sourceNodeIdTargetNodeCountMap = new HashMap<Integer, Integer>();
	//		List<PageRankEdgeEntity> targetNodeCountList = pageRankEdgeDAO.getTargetNodeCountList();
	//		if (targetNodeCountList != null && targetNodeCountList.size() > 0) {
	//			for (PageRankEdgeEntity pageRankEdgeEntity : targetNodeCountList) {
	//				sourceNodeIdTargetNodeCountMap.put(pageRankEdgeEntity.getSourceNodeId(), pageRankEdgeEntity.getTargetNodeCount());
	//			}
	//		}
	//		FormatUtils.getInstance().logMemoryUsage("getSourceNodeIdTargetNodeCountMap() ends. sourceNodeIdTargetNodeCountMap.size()="
	//				+ sourceNodeIdTargetNodeCountMap.size() + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	//		return sourceNodeIdTargetNodeCountMap;
	//	}

	private void updateNodeCount(int totalNodes, List<Integer> nodeIdList, Map<Integer, Integer> sourceNodeIdTargetNodeCountMap) {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("updateNodeCount() begins. totalNodes=" + totalNodes);
		Integer nodeCount = null;
		if (nodeIdList != null && nodeIdList.size() > 0) {
			for (Integer nodeId : nodeIdList) {
				// when source ID has target node count 
				if (sourceNodeIdTargetNodeCountMap.containsKey(nodeId)) {
					nodeCount = sourceNodeIdTargetNodeCountMap.get(nodeId);
				}
				// when source ID has no target node count, 
				else {
					nodeCount = totalNodes;
				}
				if (isDebug == true) {
					System.out.println("updateNodeCount() nodeId=" + nodeId + ",nodeCount=" + nodeCount);
				}
				//pageRankNodeDAO.updateNodeCount(nodeId, nodeCount);
				InternalLinksPageRankUtils.getInstance().updateNodeCount(nodeId, nodeCount);
			}
		}
		FormatUtils.getInstance().logMemoryUsage("updateNodeCount() ends. totalNodes=" + totalNodes + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void convergeNodes() {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("convergeNodes() begins.");

		BigDecimal transferredNodeWeight = null;
		List<PageRankNode> pageRankNodeList = null;
		BigDecimal nodeWeightPrevious = null;
		BigDecimal nodeWeightCurrent = null;
		boolean isNodeConverged = false;
		int iterationNumber = 0;

		// map key = target node ID
		// map value = transferred node weight
		Map<Integer, BigDecimal> targetNodeIdTransferredNodeWeightMap = null;

		//whileNotConvergedExists: while (pageRankNodeDAO.isNotConvergedExists() == true) {
		while (InternalLinksPageRankUtils.getInstance().isNotConvergedExists() == true) {

			//nodeIdNodeWeightList = pageRankNodeDAO.getNodeIdNodeWeightList();
			pageRankNodeList = InternalLinksPageRankUtils.getInstance().getPageRankNodeList();

			//targetNodeIdTransferredNodeWeightMap = pageRankNodeDAO.getTargetNodeIdTransferredNodeWeightMap();
			targetNodeIdTransferredNodeWeightMap = InternalLinksPageRankUtils.getInstance().getTargetNodeIdTransferredNodeWeightMap();

			//if (isDebug == true) {
			//	for (Integer targetNodeId : targetNodeIdTransferredNodeWeightMap.keySet()) {
			//		transferredNodeWeight = targetNodeIdTransferredNodeWeightMap.get(targetNodeId);
			//		System.out.println("updateNodeCount() targetNodeIdTransferredNodeWeightMap:targetNodeId=" + targetNodeId + ",transferredNodeWeight="
			//				+ transferredNodeWeight);
			//	}
			//}
			for (PageRankNode pageRankNode : pageRankNodeList) {
				if (targetNodeIdTransferredNodeWeightMap.containsKey(pageRankNode.getNodeId())) {
					transferredNodeWeight = targetNodeIdTransferredNodeWeightMap.get(pageRankNode.getNodeId());
				} else {
					transferredNodeWeight = IConstants.BIG_DECIMAL_ZERO;
				}
				nodeWeightPrevious = pageRankNode.getNodeWeight();
				nodeWeightCurrent = IConstants.BIG_DECIMAL_ONE.subtract(IConstants.DAMPING_FACTOR).add(transferredNodeWeight);
				isNodeConverged = checkIfConverged(nodeWeightPrevious, nodeWeightCurrent);
				//pageRankNodeDAO.updateNodeWeightNodeConverged(pageRankNode.getNodeId(), nodeWeightCurrent, isNodeConverged);
				InternalLinksPageRankUtils.getInstance().updateNodeWeightNodeConverged(pageRankNode.getNodeId(), nodeWeightCurrent, isNodeConverged);
			}
			if (isDebug == true) {
				iterationNumber++;
				pageRankNodeList = InternalLinksPageRankUtils.getInstance().getPageRankNodeList();
				if (pageRankNodeList != null && pageRankNodeList.size() > 0) {
					for (PageRankNode pageRankNode : pageRankNodeList) {
						System.out.println("convergeNodes() iteration=" + iterationNumber + ",nodeId=" + pageRankNode.getNodeId() + ",nodeUrl="
								+ InternalLinksPageRankUtils.getInstance().getUrlString(pageRankNode.getNodeId()) + ",nodeCount=" + pageRankNode.getNodeCount()
								+ ",nodeWeight=" + pageRankNode.getNodeWeight().setScale(IConstants.BIG_DECIMAL_SCALE, RoundingMode.HALF_UP) + ",isNodeConverged="
								+ pageRankNode.isNodeConverged());
					}
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("convergeNodes() ends. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private boolean checkIfConverged(BigDecimal nodeWeightPrevious, BigDecimal nodeWeightCurrent) {
		boolean output = false;
		BigDecimal nodeWeightDifference = nodeWeightPrevious.subtract(nodeWeightCurrent).setScale(IConstants.BIG_DECIMAL_SCALE, RoundingMode.HALF_UP);

		// when node weight difference is < 0, convert node weight difference to positive decimal 
		if (nodeWeightDifference.compareTo(IConstants.BIG_DECIMAL_ZERO) < 1) {
			nodeWeightDifference = nodeWeightDifference.multiply(IConstants.BIG_DECIMAL_NEGATIVE_ONE).setScale(IConstants.BIG_DECIMAL_SCALE, RoundingMode.HALF_UP);
		}
		// when node weight difference is less than margin of error
		if (nodeWeightDifference.compareTo(IConstants.MARGIN_OF_ERROR) < 1) {
			output = true;
		}
		return output;
	}

	private void createPageRankNodeTestData() {

		PageRankNode pageRankNode = null;
		String urlString = null;
		int nodeId = 0;

		// first record
		urlString = "https://www.test1.com/page1.html";
		nodeId++;
		InternalLinksPageRankUtils.getInstance().createNodeIdUrlString(nodeId, urlString);
		pageRankNode = new PageRankNode();
		pageRankNode.setNodeId(nodeId);
		InternalLinksPageRankUtils.getInstance().createPageRankNode(nodeId, pageRankNode);

		// second record
		urlString = "https://www.test2.com/page2.html";
		nodeId++;
		InternalLinksPageRankUtils.getInstance().createNodeIdUrlString(nodeId, urlString);
		pageRankNode = new PageRankNode();
		pageRankNode.setNodeId(nodeId);
		InternalLinksPageRankUtils.getInstance().createPageRankNode(nodeId, pageRankNode);

		// third record
		urlString = "https://www.test3.com/page3.html";
		nodeId++;
		InternalLinksPageRankUtils.getInstance().createNodeIdUrlString(nodeId, urlString);
		pageRankNode = new PageRankNode();
		pageRankNode.setNodeId(nodeId);
		InternalLinksPageRankUtils.getInstance().createPageRankNode(nodeId, pageRankNode);

		// fourth record
		urlString = "https://www.test4.com/page4.html";
		nodeId++;
		InternalLinksPageRankUtils.getInstance().createNodeIdUrlString(nodeId, urlString);
		pageRankNode = new PageRankNode();
		pageRankNode.setNodeId(nodeId);
		InternalLinksPageRankUtils.getInstance().createPageRankNode(nodeId, pageRankNode);
	}

	private void createPageRankEdgeTestData() {

		int sourceNodeId = 0;
		List<Integer> targetNodeIdList = null;

		// source node ID 2
		sourceNodeId = 2;
		targetNodeIdList = new ArrayList<Integer>();
		targetNodeIdList.add(1);
		targetNodeIdList.add(3);
		InternalLinksPageRankUtils.getInstance().createSourceNodeIdTargetNodeIdList(sourceNodeId, targetNodeIdList);

		// source node ID 3
		sourceNodeId = 3;
		targetNodeIdList = new ArrayList<Integer>();
		targetNodeIdList.add(1);
		InternalLinksPageRankUtils.getInstance().createSourceNodeIdTargetNodeIdList(sourceNodeId, targetNodeIdList);

		// source node ID 4
		sourceNodeId = 4;
		targetNodeIdList = new ArrayList<Integer>();
		targetNodeIdList.add(1);
		targetNodeIdList.add(2);
		targetNodeIdList.add(3);
		InternalLinksPageRankUtils.getInstance().createSourceNodeIdTargetNodeIdList(sourceNodeId, targetNodeIdList);
	}

}
