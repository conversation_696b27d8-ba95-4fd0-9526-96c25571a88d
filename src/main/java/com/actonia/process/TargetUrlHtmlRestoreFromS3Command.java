package com.actonia.process;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Date;

import org.apache.commons.lang.time.DateFormatUtils;

import com.actonia.IConstants;
import com.actonia.concurrency.BaseThreadCommand;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.utils.BackupUtils;
import com.actonia.utils.FormatUtils;

public class TargetUrlHtmlRestoreFromS3Command extends BaseThreadCommand {

	private String thread;
	private Date trackDate;
	private String s3FolderURI;
	private String destinationTableName;
	private String destinationDatabaseServerIpAddress;

	public TargetUrlHtmlRestoreFromS3Command(String thread, Date trackDate, String s3FolderURI, String destinationTableName,
			String destinationDatabaseServerIpAddress) {
		super();
		this.thread = thread;
		this.trackDate = trackDate;
		this.s3FolderURI = s3FolderURI;
		this.destinationTableName = destinationTableName;
		this.destinationDatabaseServerIpAddress = destinationDatabaseServerIpAddress;
	}

	@Override
	protected void execute() {
		long startTimestamp = System.currentTimeMillis();
		//FormatUtils.getInstance().logMemoryUsage("execute() begins. thread=" + thread + ",trackDate=" + trackDate);
		try {
			restore();
		} catch (Exception e) {
			e.printStackTrace();
			StringWriter stringWriter = new StringWriter();
			e.printStackTrace(new PrintWriter(stringWriter));
			String exceptionMessage = stringWriter.toString();
			FormatUtils.getInstance().logMemoryUsage("execute() thread=" + thread + ",trackDate=" + trackDate + ",exception=" + exceptionMessage);
		} finally {
			CacheModleFactory.getInstance().setAliveIpAddress(thread);
			//FormatUtils.getInstance()
			//		.logMemoryUsage("execute() ends. thread=" + thread + ",trackDate=" + trackDate + ",elapsed (ms.)=" + (System.currentTimeMillis() - startTimestamp));
		}
	}

	private void restore() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String trackDateString = DateFormatUtils.format(trackDate, IConstants.DATE_FORMAT_YYYY_MM_DD);
		FormatUtils.getInstance().logMemoryUsage("restore() begins. thread=" + thread + ",trackDateString=" + trackDateString);
		// https://s3.us-east-1.amazonaws.com/seoclarity-cdb/ck-cdb-123/cloudflare/local_index_item_info/2022-02-25/2020-05-08.bin.zstd
		String s3ObjectURI = s3FolderURI + trackDateString + IConstants.BACKUP_FILE_EXT;
		BackupUtils.getInstance().getTargetUrlHtmlBackupClickHouseDAO(destinationDatabaseServerIpAddress).restoreFromS3(thread, s3ObjectURI, destinationTableName);
		FormatUtils.getInstance().logMemoryUsage("restore() ends. thread=" + thread + ",trackDateString=" + trackDateString + ",s3ObjectURI=" + s3ObjectURI
				+ ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	@Override
	protected void undo() throws Exception {
		// TODO Auto-generated method stub
	}
}
