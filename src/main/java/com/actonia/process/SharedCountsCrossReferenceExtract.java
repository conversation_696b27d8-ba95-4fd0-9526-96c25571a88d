package com.actonia.process;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.dao.SharedCountsCrossReferenceClickHouseDAO;
import com.actonia.utils.ClickHouseUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.value.object.SharedCountsCrossReferenceValueObject;

public class SharedCountsCrossReferenceExtract {
	
	private SharedCountsCrossReferenceClickHouseDAO sharedCountsCrossReferenceClickHouseDAO;

	public SharedCountsCrossReferenceExtract() throws Exception {

		// instantiate clickhouse DAO based on clickhouse.properties
		this.sharedCountsCrossReferenceClickHouseDAO = ClickHouseUtils.getSharedCountsCrossReferenceClickHouseDAO();
	}

	public static void main(String[] args) throws Exception {
		new SharedCountsCrossReferenceExtract().process(args);
	}

	private void process(String[] args) throws Exception {
		String trackDateString = args[0];
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameter 1: trackDateString=" + trackDateString);
		Date testDate = DateUtils.parseDate(trackDateString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
		Date lastSundayDate = ClickHouseUtils.getLastSundayDate(testDate);
		FormatUtils.getInstance().logMemoryUsage("process() last Sunday date=" + DateFormatUtils.format(lastSundayDate, IConstants.DATE_FORMAT_YYYY_MM_DD));

		Integer limit = null;
		List<SharedCountsCrossReferenceValueObject> sharedCountsCrossReferenceValueObjectList = sharedCountsCrossReferenceClickHouseDAO.getList(DateUtils.truncate(lastSundayDate, Calendar.DAY_OF_MONTH), limit);
		if (sharedCountsCrossReferenceValueObjectList != null && sharedCountsCrossReferenceValueObjectList.size() > 0) {
			for (SharedCountsCrossReferenceValueObject sharedCountsCrossReferenceValueObject : sharedCountsCrossReferenceValueObjectList) {
				FormatUtils.getInstance().logMemoryUsage("process() sharedCountsCrossReferenceValueObject=" + sharedCountsCrossReferenceValueObject.toString());
			}
		}
	}

}
