package com.actonia.process;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.concurrency.ThreadPoolService;
import com.actonia.dao.BackupAuditTrailDAO;
import com.actonia.dao.TargetUrlHtmlBackupClickHouseDAO;
import com.actonia.entity.BackupAuditTrailEntity;
import com.actonia.utils.BackupUtils;
import com.actonia.utils.CommonUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.SpringBeanFactory;

public class TargetUrlHtmlRestoreFromS3 {

	private static ThreadPoolService threadPool = ThreadPoolService.getInstance();
	private TargetUrlHtmlBackupClickHouseDAO destinationTargetUrlHtmlBackupClickHouseDAO;
	private String destinationTableName;
	private BackupAuditTrailDAO backupAuditTrailDAO;
	private static final String S3_URI_PREFIX = "https://s3.us-east-1.amazonaws.com/";
	private int totalDaysRestored = 0;

	public TargetUrlHtmlRestoreFromS3() {
		this.backupAuditTrailDAO = SpringBeanFactory.getBean("backupAuditTrailDAO");
	}

	public static void main(String[] args) {
		new TargetUrlHtmlRestoreFromS3().process(args);
	}

	private void process(String[] args) {

		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("process() begins.");

		String destinationDatabaseServerIpAddress = null;
		String auditTrailTableName = null;
		String s3BucketName = null;
		String s3PartialPrefix = null;
		Date startTrackDate = null;
		Date endTrackDate = null;
		BackupAuditTrailEntity backupAuditTrailEntity = null;
		String s3FullPrefix = null;
		int numberOfThreads = 8;
		String s3FolderURI = null;

		try {
			threadPool.init();
			FormatUtils.getInstance().logMemoryUsage("process() numberOfThreads: " + numberOfThreads);
			CommonUtils.initThreads(numberOfThreads);

			if (args == null || args.length == 0) {
				FormatUtils.getInstance().logMemoryUsage("process() runtime parameters are required.");
				return;
			}

			// runtime paramter 1: destination database server IP address
			if (args.length >= 1) {
				destinationDatabaseServerIpAddress = args[0];
			}
			if (StringUtils.isBlank(destinationDatabaseServerIpAddress)) {
				FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 1: destination database server IP address is required.");
				return;
			}
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 1: destination database server IP address=" + destinationDatabaseServerIpAddress);

			// runtime paramter 2: destination table name
			if (args.length >= 2) {
				destinationTableName = args[1];
			}
			if (StringUtils.isBlank(destinationTableName)) {
				FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 2: destination table name is required.");
				return;
			}
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 2: destination table name=" + destinationTableName);

			// runtime paramter 3: audit trail table name
			if (args.length >= 3) {
				auditTrailTableName = args[2];
			}
			if (StringUtils.isBlank(auditTrailTableName)) {
				FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 3: audit trail table name is required.");
				return;
			}
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 3: audit trail table name=" + auditTrailTableName);

			// runtime paramter 4: S3 bucket name
			if (args.length >= 4) {
				s3BucketName = args[3];
			}
			if (StringUtils.isBlank(s3BucketName)) {
				FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 4: S3 bucket name is required.");
				return;
			}
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 4: S3 bucket name=" + s3BucketName);

			// runtime paramter 5: S3 prefix (partial)
			if (args.length >= 5) {
				s3PartialPrefix = args[4];
			}
			if (StringUtils.isBlank(s3PartialPrefix)) {
				FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 5: S3 prefix (partial) is required.");
				return;
			}
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 5: S3 prefix (partial)=" + s3PartialPrefix);

			destinationTargetUrlHtmlBackupClickHouseDAO = BackupUtils.getInstance().getTargetUrlHtmlBackupClickHouseDAO(destinationDatabaseServerIpAddress);
			if (destinationTargetUrlHtmlBackupClickHouseDAO == null) {
				FormatUtils.getInstance().logMemoryUsage(
						"process() destinationTargetUrlHtmlBackupClickHouseDAO cannot be initiated using IP address=" + destinationDatabaseServerIpAddress);
				return;
			}

			// retrieve the last 'backlink_backup_audit_trail' for source table
			List<BackupAuditTrailEntity> backupAuditTrailEntityList = backupAuditTrailDAO.getList(auditTrailTableName, s3BucketName, s3PartialPrefix);
			if (backupAuditTrailEntityList != null && backupAuditTrailEntityList.size() > 0) {

				// use the last backup to restore
				backupAuditTrailEntity = backupAuditTrailEntityList.get(backupAuditTrailEntityList.size() - 1);
				startTrackDate = DateUtils.parseDate(backupAuditTrailEntity.getStartDate(), new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
				endTrackDate = DateUtils.parseDate(backupAuditTrailEntity.getEndDate(), new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
				s3FullPrefix = s3PartialPrefix + backupAuditTrailEntity.getBackupDate();
				s3FolderURI = S3_URI_PREFIX + s3BucketName + IConstants.SLASH + s3FullPrefix + IConstants.SLASH;
				FormatUtils.getInstance()
						.logMemoryUsage("process() s3FolderURI=" + s3FolderURI + ",startTrackDate=" + startTrackDate + ",endTrackDate=" + endTrackDate);

				processTrackDatesConcurrently(startTrackDate, endTrackDate, s3FolderURI, destinationDatabaseServerIpAddress);

			} else {
				FormatUtils.getInstance().logMemoryUsage("process() backupAuditTrailEntityList is empty.");
			}
			FormatUtils.getInstance()
					.logMemoryUsage("process() ends. totalDaysRestored=" + totalDaysRestored + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			threadPool.destroy();
		}
	}

	private void processTrackDatesConcurrently(Date startTrackDate, Date endTrackDate, String s3FolderURI, String destinationDatabaseServerIpAddress) throws Exception {
		long startTimestamp = System.currentTimeMillis();

		String thread = null;
		TargetUrlHtmlRestoreFromS3Command targetUrlHtmlRestoreFromS3Command = null;

		Date currentTrackDate = endTrackDate;
		while (!currentTrackDate.before(startTrackDate)) {
			thread = CacheModleFactory.getInstance().getAliveIpAddress();
			if (thread == null) {
				continue;
			}
			targetUrlHtmlRestoreFromS3Command = getTargetUrlHtmlRestoreFromS3Command(thread, currentTrackDate, s3FolderURI, destinationDatabaseServerIpAddress);
			try {
				threadPool.execute(targetUrlHtmlRestoreFromS3Command);
			} catch (Exception e) {
				e.printStackTrace();
			}

			// calculate next track date one day before
			currentTrackDate = DateUtils.addDays(currentTrackDate, -1);
			totalDaysRestored++;
		}
		do {
			try {
				Thread.sleep(1000);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		} while (threadPool.getThreadPool().getActiveCount() > 0);
		FormatUtils.getInstance()
				.logMemoryUsage("processTrackDatesConcurrently() end. elapsed time in seconds=" + ((System.currentTimeMillis() - startTimestamp) / 1000));
	}

	private TargetUrlHtmlRestoreFromS3Command getTargetUrlHtmlRestoreFromS3Command(String thread, Date trackDate, String s3FolderURI,
			String destinationDatabaseServerIpAddress) {
		TargetUrlHtmlRestoreFromS3Command targetUrlHtmlRestoreFromS3Command = new TargetUrlHtmlRestoreFromS3Command(thread, trackDate, s3FolderURI,
				destinationTableName, destinationDatabaseServerIpAddress);
		targetUrlHtmlRestoreFromS3Command.setStatus(true);
		return targetUrlHtmlRestoreFromS3Command;
	}
}