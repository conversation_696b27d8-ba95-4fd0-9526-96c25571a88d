package com.actonia.process;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;

import com.actonia.IConstants;
import com.actonia.dao.OwnDomainEntityDAO;
import com.actonia.dao.TargetUrlEntityDAO;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.entity.TargetUrlEntity;
import com.actonia.service.AgencyInfoService;
import com.actonia.utils.EmailSenderComponent;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.SQSUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.UrlCrawlParametersVO;
import com.google.gson.Gson;

public class QueueDepthReport {

	private OwnDomainEntityDAO ownDomainEntityDAO;
	private static final String DELIMITER = "\t";
	private static final String TARGET_URL_QUEUE_NAMES_PREFIX = "TARGET_URL_HTML_";
	private static final String COMPETITOR_URL_QUEUE_NAMES_PREFIX = "COMPETITOR_URL_HTML_";
	private boolean isSendAlert = false;
	private AgencyInfoService agencyInfoService;
	private EmailSenderComponent emailSenderComponent;
	private static final String NOTIFICATION_EMAIL_ADDRESS = "<EMAIL>";
	private TargetUrlEntityDAO targetUrlEntityDAO;

	public QueueDepthReport() {
		super();
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		agencyInfoService = SpringBeanFactory.getBean("agencyInfoService");
		emailSenderComponent = SpringBeanFactory.getBean("emailSenderComponent");
		targetUrlEntityDAO = SpringBeanFactory.getBean("targetUrlEntityDAO");
	}

	public static void main(String[] args) {
		try {
			new QueueDepthReport().process(args);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void process(String[] args) throws Exception {
		SQSUtils.getInstance();
		String outputFileLocationPath = args[0];
		FormatUtils.getInstance().logMemoryUsage("process() outputFileLocationPath=" + outputFileLocationPath);
		String queueNamesString = args[1];
		String[] queueNameArray = queueNamesString.split(IConstants.COMMA);
		List<String> outputList = new ArrayList<String>();
		Integer[] numberOfMessagesArray = null;
		List<String> queueUrlList = null;

		// map key = queue name
		// map value = numberOfMessagesArray
		Map<String, Integer[]> queueNameNumberOfMessageArrayMap = new HashMap<String, Integer[]>();
		for (String queueName : queueNameArray) {
			FormatUtils.getInstance().logMemoryUsage("process() queueName=" + queueName);
			if (StringUtils.equalsIgnoreCase(queueName, TARGET_URL_QUEUE_NAMES_PREFIX)) {
				outputList.add(getTargetUrlHeading());
				outputList.addAll(processTargetUrlQueues());
			} else if (StringUtils.equalsIgnoreCase(queueName, COMPETITOR_URL_QUEUE_NAMES_PREFIX)) {
				outputList.add(getHeading());
				outputList.addAll(processCompetitorUrlQueues());
			} else {
				isSendAlert = true;
				queueUrlList = SQSUtils.getInstance().getQueueUrlsByPrefix(queueName);
				if (queueUrlList != null && queueUrlList.size() > 0) {
					for (String queueUrl : queueUrlList) {
						numberOfMessagesArray = processOneSpecificQueueUrl(queueUrl);
						if (numberOfMessagesArray != null && numberOfMessagesArray.length == 2) {
							queueNameNumberOfMessageArrayMap.put(FormatUtils.getInstance().extractQueueNameFromQueueUrl(queueUrl), numberOfMessagesArray);
						}
					}
				}
			}
		}
		if (isSendAlert == true) {
			FormatUtils.getInstance().logMemoryUsage("process() ends. queueNameNumberOfMessageArrayMap.size()=" + queueNameNumberOfMessageArrayMap.size());
		} else {
			String todayDateString = DateFormatUtils.format(new Date(), IConstants.DATE_FORMAT_YYYY_MM_DD);
			outputFileLocationPath = StringUtils.replace(outputFileLocationPath, ".txt", "_" + todayDateString + ".txt");
			File file = new File(outputFileLocationPath);
			FileUtils.writeLines(file, outputList);
			FormatUtils.getInstance().logMemoryUsage("process() ends. outputList.size()=" + outputList.size());
		}
	}

	private String getTargetUrlHeading() {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("DOMAIN ID").append(DELIMITER);
		stringBuilder.append("DOMAIN NAME").append(DELIMITER);
		stringBuilder.append("TOTAL URLS").append(DELIMITER);
		stringBuilder.append("CONCURRENT REQUESTS").append(DELIMITER);
		stringBuilder.append("QUEUE NAME").append(DELIMITER);
		stringBuilder.append("TOTAL MESSAGES").append(DELIMITER);
		stringBuilder.append("TOTAL INFLIGHT");
		return stringBuilder.toString();
	}

	private String getHeading() {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("QUEUE NAME").append(DELIMITER);
		stringBuilder.append("TOTAL MESSAGES").append(DELIMITER);
		stringBuilder.append("TOTAL INFLIGHT");
		return stringBuilder.toString();
	}

	private Integer[] processOneSpecificQueueUrl(String queueUrl) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("processOneSpecificQueueUrl() begins. queueUrl=" + queueUrl);
		Integer[] numberOfMessagesArray = SQSUtils.getInstance().getApproximateNumberOfMessagesAndInflightByQueueUrl(queueUrl);
		if (numberOfMessagesArray != null && numberOfMessagesArray.length == 2) {
			FormatUtils.getInstance().logMemoryUsage("processOneSpecificQueueUrl() queueUrl=" + queueUrl + ",messages in queue=" + numberOfMessagesArray[0]
					+ ",messages in flight=" + numberOfMessagesArray[1]);
		}
		FormatUtils.getInstance().logMemoryUsage("processOneSpecificQueueUrl() ends. queueUrl=" + queueUrl);
		return numberOfMessagesArray;
	}

	private List<String> processTargetUrlQueues() throws Exception {
		FormatUtils.getInstance().logMemoryUsage("processTargetUrlQueues() begins.");
		List<String> outputList = new ArrayList<String>();
		String language = null;
		int domainId = 0;
		String queueName = null;
		Integer[] numberOfMessagesArray = null;
		String domainName = null;
		StringBuilder stringBuilder = null;
		int totalTargetUrls = 0;
		int totalConcurrentRequests = 0;
		List<OwnDomainEntity> ownDomainEntityList = ownDomainEntityDAO.queryForAll();
		for (OwnDomainEntity ownDomainEntity : ownDomainEntityList) {
			language = ownDomainEntity.getLanguage();
			domainId = ownDomainEntity.getId();
			domainName = ownDomainEntity.getDomain();
			totalConcurrentRequests = getTotalConcurrentRequests(ownDomainEntity);
			totalTargetUrls = targetUrlEntityDAO.getTargetUrlCount(domainId, TargetUrlEntity.TYPE_ADD_BY_USER, TargetUrlEntity.STATUS_ACTIVE);
			if (totalTargetUrls > 0) {
				queueName = TARGET_URL_QUEUE_NAMES_PREFIX + language.toUpperCase() + "_" + domainId;
				numberOfMessagesArray = SQSUtils.getInstance().getApproximateNumberOfMessagesAndInflight(queueName);
				if (numberOfMessagesArray != null && numberOfMessagesArray.length == 2) {
					FormatUtils.getInstance()
							.logMemoryUsage("processTargetUrlQueues() domainId=" + domainId + ",domainName=" + domainName + ",totalTargetUrls=" + totalTargetUrls
									+ ",queueName=" + queueName + ",messages in queue=" + numberOfMessagesArray[0] + ",messages in flight=" + numberOfMessagesArray[1]);
					//if (numberOfMessagesArray[0] > 0 || numberOfMessagesArray[1] > 0) {
					stringBuilder = new StringBuilder();
					stringBuilder.append(domainId).append(DELIMITER);
					stringBuilder.append(domainName).append(DELIMITER);
					stringBuilder.append(totalTargetUrls).append(DELIMITER);
					stringBuilder.append(totalConcurrentRequests).append(DELIMITER);
					stringBuilder.append(queueName).append(DELIMITER);
					stringBuilder.append(numberOfMessagesArray[0]).append(DELIMITER);
					stringBuilder.append(numberOfMessagesArray[1]);
					outputList.add(stringBuilder.toString());
					//}
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("processTargetUrlQueues() ends.");
		return outputList;
	}

	private int getTotalConcurrentRequests(OwnDomainEntity ownDomainEntity) {
		int totalConcurrentRequests = 1;
		String urlCrawlParameters = null;
		UrlCrawlParametersVO[] urlCrawlParametersVoArray = null;
		UrlCrawlParametersVO urlCrawlParametersVo = null;
		Gson gson = null;
		if (StringUtils.isNotBlank(ownDomainEntity.getUrlCrawlParameters())) {
			gson = new Gson();
			urlCrawlParameters = ownDomainEntity.getUrlCrawlParameters();
			urlCrawlParametersVoArray = gson.fromJson(urlCrawlParameters, UrlCrawlParametersVO[].class);
			for (int idx = 0; idx < urlCrawlParametersVoArray.length; idx++) {
				urlCrawlParametersVo = urlCrawlParametersVoArray[idx];
				// maximum number of concurrent threads per domain
				if (urlCrawlParametersVo.getType().equalsIgnoreCase(IConstants.MAX_CONCURRENT_THREADS)) {
					totalConcurrentRequests = Integer.parseInt(urlCrawlParametersVo.getData());
				}
			}
		}
		return totalConcurrentRequests;
	}

	private List<String> processCompetitorUrlQueues() throws Exception {
		FormatUtils.getInstance().logMemoryUsage("processCompetitorUrlQueues() begins.");
		List<String> outputList = new ArrayList<String>();
		String queueName = null;
		Integer[] numberOfMessagesArray = null;
		StringBuilder stringBuilder = null;
		int endQueueNumber = 3000;
		for (int i = 0; i < endQueueNumber; i++) {
			queueName = COMPETITOR_URL_QUEUE_NAMES_PREFIX + (i + 1);
			numberOfMessagesArray = SQSUtils.getInstance().getApproximateNumberOfMessagesAndInflight(queueName);
			if (numberOfMessagesArray != null && numberOfMessagesArray.length == 2) {
				FormatUtils.getInstance().logMemoryUsage("processCompetitorUrlQueues() queueName=" + queueName + ",messages in queue=" + numberOfMessagesArray[0]
						+ ",messages in flight=" + numberOfMessagesArray[1]);
				if (numberOfMessagesArray[0] > 0 || numberOfMessagesArray[1] > 0) {
					stringBuilder = new StringBuilder();
					stringBuilder.append(queueName).append(DELIMITER);
					stringBuilder.append(numberOfMessagesArray[0]).append(DELIMITER);
					stringBuilder.append(numberOfMessagesArray[1]);
					outputList.add(stringBuilder.toString());
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("processCompetitorUrlQueues() ends.");
		return outputList;
	}

}
