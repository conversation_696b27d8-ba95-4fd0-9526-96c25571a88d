package com.actonia.process;

import java.io.Reader;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.dao.TargetUrlHtmlFileNameClickHouseDAO;
import com.actonia.entity.HtmlFileNameClickHouseEntity;
import com.actonia.utils.FormatUtils;

public class TargetUrlHtmlFileNameConversion {

	public TargetUrlHtmlFileNameConversion() {
	}

	public static void main(String[] args) {

		try {
			new TargetUrlHtmlFileNameConversion().process(args);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void process(String[] args) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("process() begins.");
		String contentGuardTextFilePath = null;
		String fileNameTextFilePath = null;

		if (args == null || args.length != 2) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters are required.");
			return;
		}

		// runtime parameter 1 (required): dis_content_guard.txt file path
		if (args.length >= 1) {
			contentGuardTextFilePath = args[0];
		}
		if (StringUtils.isBlank(contentGuardTextFilePath)) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 1: dis_content_guard.txt file path required.");
			return;
		}
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 1: dis_content_guard.txt file path=" + contentGuardTextFilePath);

		// runtime parameter 2 (required): dis_target_url_html_file_name.txt file path
		if (args.length >= 2) {
			fileNameTextFilePath = args[1];
		}
		if (StringUtils.isBlank(fileNameTextFilePath)) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 2: dis_target_url_html_file_name.txt file path required.");
			return;
		}
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 2: dis_target_url_html_file_name.txt file path=" + fileNameTextFilePath);

		List<HtmlFileNameClickHouseEntity> contentGuardList = parseContentGuardTextFile(contentGuardTextFilePath);
		//List<HtmlFileNameClickHouseEntity> fileNameList = parseFileNameTextFile(fileNameTextFilePath);
		//Map<Date, Map<Integer, Map<String, List<HtmlFileNameClickHouseEntity>>>> trackDateMap = getTrackDateMap(fileNameList);
		//List<HtmlFileNameClickHouseEntity> fileNameToBeCreatedList = getFileNameEntitiesToBeCreated(contentGuardList, trackDateMap);
		//updateDataStore(fileNameToBeCreatedList);
		verify(contentGuardList);

		FormatUtils.getInstance().logMemoryUsage("process() end. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private List<HtmlFileNameClickHouseEntity> parseContentGuardTextFile(String contentGuardTextFilePath) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("parseContentGuardTextFile() begins. contentGuardTextFilePath=" + contentGuardTextFilePath);
		List<HtmlFileNameClickHouseEntity> htmlFileNameClickHouseEntityList = new ArrayList<HtmlFileNameClickHouseEntity>();
		HtmlFileNameClickHouseEntity htmlFileNameClickHouseEntity = null;
		String trackDate = null;
		String domainId = null;
		String url = null;
		String crawlTimestamp = null;
		Reader reader = Files.newBufferedReader(Paths.get(contentGuardTextFilePath));
		CSVParser csvParser = new CSVParser(reader, CSVFormat.TDF);
		for (CSVRecord csvRecord : csvParser) {
			trackDate = csvRecord.get(0);
			domainId = csvRecord.get(1);
			url = csvRecord.get(2);
			crawlTimestamp = csvRecord.get(3);
			//if (htmlFileNameClickHouseEntityList.size() < 8) {
			//System.out.println("parseContentGuardTextFile() parsed trackDate=" + trackDate + ",domainId=" + domainId + ",url=" + url + ",crawlTimestamp=" + crawlTimestamp);
			//}
			htmlFileNameClickHouseEntity = new HtmlFileNameClickHouseEntity();
			htmlFileNameClickHouseEntity.setTrackDate(DateUtils.parseDate(trackDate, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD }));
			htmlFileNameClickHouseEntity.setDomainId(NumberUtils.toInt(domainId));
			htmlFileNameClickHouseEntity.setUrl(url);
			htmlFileNameClickHouseEntity.setCrawlTimestamp(DateUtils.parseDate(crawlTimestamp, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS }));
			//if (htmlFileNameClickHouseEntityList.size() < 8) {
			//System.out.println("parseContentGuardTextFile() htmlFileNameClickHouseEntity trackDate=" + htmlFileNameClickHouseEntity.getTrackDate() + ",domainId="
			//		+ htmlFileNameClickHouseEntity.getDomainId() + ",url=" + htmlFileNameClickHouseEntity.getUrl() + ",crawlTimestamp="
			//		+ htmlFileNameClickHouseEntity.getCrawlTimestamp());
			//}
			htmlFileNameClickHouseEntityList.add(htmlFileNameClickHouseEntity);
		}
		csvParser.close();
		FormatUtils.getInstance().logMemoryUsage("parseContentGuardTextFile() ends. htmlFileNameClickHouseEntityList.size()=" + htmlFileNameClickHouseEntityList.size()
				+ ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return htmlFileNameClickHouseEntityList;
	}

	private List<HtmlFileNameClickHouseEntity> parseFileNameTextFile(String fileNameTextFilePath) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("parseFileNameTextFile() begins. fileNameTextFilePath=" + fileNameTextFilePath);
		List<HtmlFileNameClickHouseEntity> htmlFileNameClickHouseEntityList = new ArrayList<HtmlFileNameClickHouseEntity>();
		HtmlFileNameClickHouseEntity htmlFileNameClickHouseEntity = null;
		String trackDate = null;
		String domainId = null;
		String url = null;
		String crawlTimestamp = null;
		String fileName = null;
		Reader reader = Files.newBufferedReader(Paths.get(fileNameTextFilePath));
		CSVParser csvParser = new CSVParser(reader, CSVFormat.TDF);
		for (CSVRecord csvRecord : csvParser) {
			trackDate = csvRecord.get(0);
			domainId = csvRecord.get(1);
			url = csvRecord.get(2);
			crawlTimestamp = csvRecord.get(3);
			fileName = csvRecord.get(4);
			//if (htmlFileNameClickHouseEntityList.size() < 8) {
			//	System.out.println("parseFileNameTextFile() parsed trackDate=" + trackDate + ",domainId=" + domainId + ",url=" + url + ",crawlTimestamp="
			//			+ crawlTimestamp + ",fileName=" + fileName);
			//}
			htmlFileNameClickHouseEntity = new HtmlFileNameClickHouseEntity();
			htmlFileNameClickHouseEntity.setTrackDate(DateUtils.parseDate(trackDate, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD }));
			htmlFileNameClickHouseEntity.setDomainId(NumberUtils.toInt(domainId));
			htmlFileNameClickHouseEntity.setUrl(url);
			htmlFileNameClickHouseEntity.setCrawlTimestamp(DateUtils.parseDate(crawlTimestamp, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS }));
			htmlFileNameClickHouseEntity.setFileName(fileName);
			//if (htmlFileNameClickHouseEntityList.size() < 8) {
			//	System.out.println("parseFileNameTextFile() htmlFileNameClickHouseEntity trackDate=" + htmlFileNameClickHouseEntity.getTrackDate() + ",domainId="
			//			+ htmlFileNameClickHouseEntity.getDomainId() + ",url=" + htmlFileNameClickHouseEntity.getUrl() + ",crawlTimestamp="
			//			+ htmlFileNameClickHouseEntity.getCrawlTimestamp() + ",fileName=" + htmlFileNameClickHouseEntity.getFileName());
			//}
			htmlFileNameClickHouseEntityList.add(htmlFileNameClickHouseEntity);
		}
		csvParser.close();
		FormatUtils.getInstance().logMemoryUsage("parseFileNameTextFile() ends. htmlFileNameClickHouseEntityList.size()=" + htmlFileNameClickHouseEntityList.size()
				+ ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return htmlFileNameClickHouseEntityList;
	}

	private Map<Date, Map<Integer, Map<String, List<HtmlFileNameClickHouseEntity>>>> getTrackDateMap(List<HtmlFileNameClickHouseEntity> inputFileNameList) {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("getTrackDateMap() begins. inputFileNameList.size()=" + inputFileNameList.size());

		Map<Date, Map<Integer, Map<String, List<HtmlFileNameClickHouseEntity>>>> trackDateMap = new HashMap<Date, Map<Integer, Map<String, List<HtmlFileNameClickHouseEntity>>>>();

		Map<Integer, Map<String, List<HtmlFileNameClickHouseEntity>>> domainMap = null;

		Map<String, List<HtmlFileNameClickHouseEntity>> urlMap = null;

		List<HtmlFileNameClickHouseEntity> testFileNameList = null;

		Date trackDate = null;
		Integer domainId = null;
		String url = null;

		for (HtmlFileNameClickHouseEntity htmlFileNameClickHouseEntity : inputFileNameList) {
			trackDate = htmlFileNameClickHouseEntity.getTrackDate();
			domainId = htmlFileNameClickHouseEntity.getDomainId();
			url = htmlFileNameClickHouseEntity.getUrl();
			if (trackDateMap.containsKey(trackDate)) {
				domainMap = trackDateMap.get(trackDate);
			} else {
				domainMap = new HashMap<Integer, Map<String, List<HtmlFileNameClickHouseEntity>>>();
			}
			if (domainMap.containsKey(domainId)) {
				urlMap = domainMap.get(domainId);
			} else {
				urlMap = new HashMap<String, List<HtmlFileNameClickHouseEntity>>();
			}
			if (urlMap.containsKey(url)) {
				testFileNameList = urlMap.get(url);
			} else {
				testFileNameList = new ArrayList<HtmlFileNameClickHouseEntity>();
			}
			testFileNameList.add(htmlFileNameClickHouseEntity);
			urlMap.put(url, testFileNameList);
			domainMap.put(domainId, urlMap);
			trackDateMap.put(trackDate, domainMap);
		}

		for (Date testTrackDate : trackDateMap.keySet()) {
			domainMap = trackDateMap.get(testTrackDate);
			for (Integer testDomainId : domainMap.keySet()) {
				urlMap = domainMap.get(testDomainId);
				for (String testUrl : urlMap.keySet()) {
					testFileNameList = urlMap.get(testUrl);
					for (HtmlFileNameClickHouseEntity htmlFileNameClickHouseEntity : testFileNameList) {
						if (htmlFileNameClickHouseEntity.getTrackDate().compareTo(testTrackDate) == 0
								&& htmlFileNameClickHouseEntity.getDomainId().intValue() == testDomainId.intValue()
								&& StringUtils.equalsIgnoreCase(htmlFileNameClickHouseEntity.getUrl(), testUrl)) {
							//System.out.println(
							//		"getTrackDateMap() testTrackDate=" + testTrackDate + ",testDomainId=" + testDomainId + ",testUrl=" + testUrl + ",crawlTimestamp="
							//				+ htmlFileNameClickHouseEntity.getCrawlTimestamp() + ",fileName=" + htmlFileNameClickHouseEntity.getFileName());
						} else {
							System.out.println("getTrackDateMap() error--testTrackDate=" + testTrackDate + ",testDomainId=" + testDomainId + ",testUrl=" + testUrl);
						}
					}
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage(
				"getTrackDateMap() ends. trackDateMap.size()=" + trackDateMap.size() + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));

		return trackDateMap;
	}

	private List<HtmlFileNameClickHouseEntity> getFileNameEntitiesToBeCreated(List<HtmlFileNameClickHouseEntity> contentGuardList,
			Map<Date, Map<Integer, Map<String, List<HtmlFileNameClickHouseEntity>>>> trackDateMap) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("getFileNameEntitiesToBeCreated() begins.");

		List<HtmlFileNameClickHouseEntity> fileNameToBeCreatedList = new ArrayList<HtmlFileNameClickHouseEntity>();

		Date trackDate = null;
		Integer domainId = null;
		String url = null;
		Date crawlTimestamp = null;
		Map<Integer, Map<String, List<HtmlFileNameClickHouseEntity>>> domainMap = null;
		Map<String, List<HtmlFileNameClickHouseEntity>> urlMap = null;
		List<HtmlFileNameClickHouseEntity> fileNameList = null;
		HtmlFileNameClickHouseEntity fileNameEntityToBeCreated = null;
		int totalFound = 0;
		int totalNotFound = 0;

		for (HtmlFileNameClickHouseEntity contentGuardEntity : contentGuardList) {
			trackDate = contentGuardEntity.getTrackDate();
			domainId = contentGuardEntity.getDomainId();
			url = contentGuardEntity.getUrl();
			crawlTimestamp = contentGuardEntity.getCrawlTimestamp();
			if (trackDateMap.containsKey(trackDate)) {
				domainMap = trackDateMap.get(trackDate);
				if (domainMap.containsKey(domainId)) {
					urlMap = domainMap.get(domainId);
					if (urlMap.containsKey(url)) {
						fileNameList = urlMap.get(url);
						totalFound++;
						if (fileNameList.size() == 1) {
							fileNameEntityToBeCreated = fileNameList.get(0).clone();
							fileNameEntityToBeCreated.setCrawlTimestamp(crawlTimestamp);
							fileNameToBeCreatedList.add(fileNameEntityToBeCreated);
						} else {
							fileNameEntityToBeCreated = null;
							nextHtmlFileNameClickHouseEntity: for (HtmlFileNameClickHouseEntity fileNameEntity : fileNameList) {
								if (fileNameEntity.getCrawlTimestamp().compareTo(crawlTimestamp) == 0) {
									fileNameEntityToBeCreated = fileNameEntity.clone();
									fileNameEntityToBeCreated.setCrawlTimestamp(crawlTimestamp);
									break nextHtmlFileNameClickHouseEntity;
								} else if (fileNameEntity.getCrawlTimestamp().before(crawlTimestamp)) {
									fileNameEntityToBeCreated = fileNameEntity.clone();
									fileNameEntityToBeCreated.setCrawlTimestamp(crawlTimestamp);
								}
							}
							if (fileNameEntityToBeCreated != null) {
								fileNameToBeCreatedList.add(fileNameEntityToBeCreated);
							}
						}
					} else {
						totalNotFound++;
					}
				} else {
					totalNotFound++;
				}
			} else {
				totalNotFound++;
			}
		}

		//if (isDebug == true) {
		//	for (HtmlFileNameClickHouseEntity htmlFileNameClickHouseEntity : fileNameToBeCreatedList) {
		//		System.out.println("getFileNameEntitiesToBeCreated() trackDate=" + htmlFileNameClickHouseEntity.getTrackDate() + ",domainId="
		//				+ htmlFileNameClickHouseEntity.getDomainId() + ",url=" + htmlFileNameClickHouseEntity.getUrl() + ",crawlTimestamp="
		//				+ htmlFileNameClickHouseEntity.getCrawlTimestamp() + ",fileName=" + htmlFileNameClickHouseEntity.getFileName());
		//	}
		//}
		FormatUtils.getInstance().logMemoryUsage("getFileNameEntitiesToBeCreated() ends. fileNameToBeCreatedList.size()=" + fileNameToBeCreatedList.size()
				+ ",totalFound=" + totalFound + ",totalNotFound=" + totalNotFound + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return fileNameToBeCreatedList;
	}

	private void updateDataStore(List<HtmlFileNameClickHouseEntity> fileNameToBeCreatedList) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		int totalCreated = 0;
		FormatUtils.getInstance().logMemoryUsage("updateDataStore() begins. fileNameToBeCreatedList.size()=" + fileNameToBeCreatedList.size());
		List<HtmlFileNameClickHouseEntity> testFileNameToBeCreatedList = new ArrayList<HtmlFileNameClickHouseEntity>();
		for (HtmlFileNameClickHouseEntity htmlFileNameClickHouseEntity : fileNameToBeCreatedList) {
			testFileNameToBeCreatedList.add(htmlFileNameClickHouseEntity);
			if (testFileNameToBeCreatedList.size() >= TargetUrlHtmlFileNameClickHouseDAO.getInstance().getBatchCreationSize()) {
				TargetUrlHtmlFileNameClickHouseDAO.getInstance().createBatch(null, null, testFileNameToBeCreatedList, null);
				totalCreated = totalCreated + testFileNameToBeCreatedList.size();
				testFileNameToBeCreatedList = new ArrayList<HtmlFileNameClickHouseEntity>();
			}
		}
		if (testFileNameToBeCreatedList.size() > 0) {
			TargetUrlHtmlFileNameClickHouseDAO.getInstance().createBatch(null, null, testFileNameToBeCreatedList, null);
			totalCreated = totalCreated + testFileNameToBeCreatedList.size();
			testFileNameToBeCreatedList = new ArrayList<HtmlFileNameClickHouseEntity>();
		}
		FormatUtils.getInstance()
				.logMemoryUsage("updateDataStore() ends. totalCreated=" + totalCreated + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void verify(List<HtmlFileNameClickHouseEntity> contentGuardList) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("verify() begins. contentGuardList.size()=" + contentGuardList.size());
		Date trackDate = null;
		Integer domainId = null;
		String urlString = null;
		Date crawlTimestamp = null;
		int totalContentGuardEntity = contentGuardList.size();
		int totalProcessed = 0;
		int totalVerified = 0;
		int totalNotVerified = 0;
		HtmlFileNameClickHouseEntity fileNameEntity = null;
		for (HtmlFileNameClickHouseEntity contentGuardEntity : contentGuardList) {
			totalProcessed++;
			trackDate = contentGuardEntity.getTrackDate();
			domainId = contentGuardEntity.getDomainId();
			urlString = contentGuardEntity.getUrl();
			crawlTimestamp = contentGuardEntity.getCrawlTimestamp();
			fileNameEntity = TargetUrlHtmlFileNameClickHouseDAO.getInstance().get(trackDate, domainId, urlString, crawlTimestamp);
			if (fileNameEntity != null) {
				totalVerified++;
			} else {
				totalNotVerified++;
				//FormatUtils.getInstance().logMemoryUsage(
				//		"verify() not verified. trackDate=" + trackDate + ",domainId=" + domainId + ",urlString=" + urlString + ",crawlTimestamp=" + crawlTimestamp);
			}
			if (totalProcessed % 1000 == 0) {
				FormatUtils.getInstance()
						.logMemoryUsage("verify() totalProcessed=" + totalProcessed + ",totalVerified=" + totalVerified + ",totalNotVerified=" + totalNotVerified);
			}
		}
		FormatUtils.getInstance().logMemoryUsage("verify() ends. totalContentGuardEntity=" + totalContentGuardEntity + ",totalProcessed=" + totalProcessed
				+ ",totalVerified=" + totalVerified + ",totalNotVerified=" + totalNotVerified + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}
}
