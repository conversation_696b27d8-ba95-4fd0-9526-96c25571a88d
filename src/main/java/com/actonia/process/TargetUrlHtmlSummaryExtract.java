package com.actonia.process;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang.math.NumberUtils;

import com.actonia.IConstants;
import com.actonia.dao.TargetUrlHtmlClickHouseDAO;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.utils.FormatUtils;
import com.actonia.value.object.PageAnalysisResult;

// domain_id: 522
// url: https://www.orbitz.com/Green-Bay-Hotels-Baymont-By-Wyndham-Green-Bay.h40154.Hotel-Information
public class TargetUrlHtmlSummaryExtract {
	//private static final String DELIMITER = "\t";
	private static final String DELIMITER = "`";
	private static final String TABLE_NAME = null;
	//private static final String TABLE_NAME = IConstants.TABLE_NAME_UNIT_TEST_TARGET_URL_HTML;

	public TargetUrlHtmlSummaryExtract() {
	}

	public static void main(String[] args) {
		new TargetUrlHtmlSummaryExtract().process(args);
	}

	private void process(String[] args) {
		String outputFilePathLocation = null;
		String domainIdString = null;
		int domainId = 0;

		if (args != null && args.length > 0) {

			// runtime parameter 1: output file location path
			if (args != null && args.length >= 1) {
				outputFilePathLocation = args[0];
				FormatUtils.getInstance().logMemoryUsage("process() runtime parameter 1 outputFilePathLocation=" + outputFilePathLocation);
			}

			// runtime parameter 2: domain ID(s)
			if (args != null && args.length >= 2) {
				domainIdString = args[1];
				if (StringUtils.isNotBlank(domainIdString)) {
					domainId = NumberUtils.toInt(domainIdString);
				}
				FormatUtils.getInstance().logMemoryUsage("process() runtime parameter 2:domain ID=" + domainId);
			}
		} else {
			FormatUtils.getInstance().logMemoryUsage("process() error--runtime parameters are required.");
			return;
		}

		try {
			extractHtml(outputFilePathLocation, domainId);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void extractHtml(String outputFilePathLocation, int domainId) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("extractHtml() begins. outputFilePathLocation=" + outputFilePathLocation);

		List<String> outputLineList = new ArrayList<String>();
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = null;
		String outputLine = null;
		Date cutoffDate = DateUtils.parseDate("2020-06-01", new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });

		List<String> databaseFields = new ArrayList<String>();
		databaseFields.add(IConstants.CRAWL_TIMESTAMP);
		databaseFields.add(IConstants.URL);
		databaseFields.add(IConstants.TITLE);
		databaseFields.add(IConstants.RESPONSE_CODE);
		databaseFields.add(IConstants.PAGE_ANALYSIS_RESULTS);

		FormatUtils.getInstance().logMemoryUsage("extractHtml() domainId=" + domainId);
		outputLine = getHeading();
		outputLineList.add(outputLine);

		htmlClickHouseEntityList = TargetUrlHtmlClickHouseDAO.getInstance().getLatestFromHistorical(domainId, databaseFields, TABLE_NAME);
		if (htmlClickHouseEntityList != null && htmlClickHouseEntityList.size() > 0) {
			for (HtmlClickHouseEntity htmlClickHouseEntity : htmlClickHouseEntityList) {
				if (htmlClickHouseEntity.getCrawlTimestamp().after(cutoffDate)) {
					outputLine = getDetail(htmlClickHouseEntity);
					outputLineList.add(outputLine);
				}
			}
		}

		// create output
		File outputFile = new File(outputFilePathLocation);

		try {
			FileUtils.writeLines(outputFile, IConstants.UTF_8, outputLineList);
		} catch (Exception e) {
			e.printStackTrace();
		}

		FormatUtils.getInstance().logMemoryUsage("extractHtml() ends.");
	}

	private String getHeading() {
		return "CRAWL TIMESTAMP" + DELIMITER + "URL" + DELIMITER + "TITLE"+ DELIMITER + "RESP CD" + DELIMITER + "PAGE ANALYSIS RULES (ONE-BASED)";
	}

	private String getDetail(HtmlClickHouseEntity htmlClickHouseEntity) {
		String outputLine = null;
		StringBuilder pageAnalysisRulesStringBuilder = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(DateFormatUtils.format(htmlClickHouseEntity.getCrawlTimestamp(), IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS));
		stringBuilder.append(DELIMITER);
		stringBuilder.append(htmlClickHouseEntity.getUrl());
		stringBuilder.append(DELIMITER);
		stringBuilder.append(htmlClickHouseEntity.getCrawlerResponse().getTitle());
		stringBuilder.append(DELIMITER);
		stringBuilder.append(htmlClickHouseEntity.getHttpStatusCode());
		stringBuilder.append(DELIMITER);
		if (htmlClickHouseEntity.getPageAnalysisResultArray() != null) {
			for (PageAnalysisResult pageAnalysisResult : htmlClickHouseEntity.getPageAnalysisResultArray()) {
				if (pageAnalysisResult.getResult() == IConstants.TRUE_NUMERIC) {
					if (pageAnalysisRulesStringBuilder == null) {
						pageAnalysisRulesStringBuilder = new StringBuilder();
					} else {
						pageAnalysisRulesStringBuilder.append(IConstants.COMMA);
					}
					pageAnalysisRulesStringBuilder.append(pageAnalysisResult.getRule());
				}
			}
			if (pageAnalysisRulesStringBuilder != null) {
				stringBuilder.append(pageAnalysisRulesStringBuilder.toString());
			}
		}
		outputLine = stringBuilder.toString();
		FormatUtils.getInstance().logMemoryUsage("getDetail() outputLine=" + outputLine);
		return outputLine;
	}
}
