package com.actonia.process;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;

import com.actonia.IConstants;
import com.actonia.dao.CompetitorUrlHtmlClickHouseDAO;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.utils.CrawlerUtils;
import com.actonia.utils.FormatUtils;

// domain ID: 7217
//url: https://www.expedia.com/Rosenfeld-Hotels-Romantic-Hotel.0-0-d3000032385-tRomanticHotel.Travel-Guide-Filter-Hotels
public class CompetitorUrlHtmlExtract {
	//private static final String DELIMITER = "\t";
	private static final String DELIMITER = "";
	private static final String TABLE_NAME = null;

	public CompetitorUrlHtmlExtract() {
	}

	public static void main(String[] args) {
		new CompetitorUrlHtmlExtract().process(args);
	}

	private void process(String[] args) {
		String outputFilePathLocation = null;
		String urlStringsString = null;
		String[] urlStringArray = null;

		if (args != null && args.length > 0) {

			// runtime parameter 1: output file location path
			outputFilePathLocation = args[0];
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameter 1 outputFilePathLocation=" + outputFilePathLocation);

			// runtime parameter 2: URL string(s)
			urlStringsString = args[1];
			if (StringUtils.isNotBlank(urlStringsString)) {
				urlStringArray = urlStringsString.split(IConstants.COMMA);
			}
			for (int i = 0; i < urlStringArray.length; i++) {
				FormatUtils.getInstance().logMemoryUsage("process() runtime parameter 2:urlString=" + urlStringArray[i]);
			}
		} else {
			FormatUtils.getInstance().logMemoryUsage("process() error--runtime parameters are required.");
			return;
		}

		try {
			extractHtml(outputFilePathLocation, urlStringArray);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void extractHtml(String outputFilePathLocation, String[] urlStringArray) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("extractHtml() begins. outputFilePathLocation=" + outputFilePathLocation);

		String urlString = null;
		List<String> outputLineList = new ArrayList<String>();
		List<String> testOutputLineList = null;
		String reversedUrlDomain = null;

		HtmlClickHouseEntity htmlClickHouseEntity = null;

		List<String> databaseFields = CrawlerUtils.getInstance().getCompetitorUrlHtmlTableAllFieldNames();

		for (int i = 0; i < urlStringArray.length; i++) {
			urlString = urlStringArray[i];
			reversedUrlDomain = CrawlerUtils.getInstance().getReversedUrlDomain(null, null, urlString);
			FormatUtils.getInstance().logMemoryUsage("extractHtml() urlString=" + urlString);
			htmlClickHouseEntity = CompetitorUrlHtmlClickHouseDAO.getInstance().getPrevious(IConstants.EMPTY_STRING, IConstants.EMPTY_STRING, reversedUrlDomain,
					urlString, databaseFields, TABLE_NAME, null);
			if (htmlClickHouseEntity != null) {
				testOutputLineList = CrawlerUtils.getInstance().getHtmlExtractOutputList(htmlClickHouseEntity, databaseFields, DELIMITER);
				outputLineList.addAll(testOutputLineList);
			} else {
				FormatUtils.getInstance().logMemoryUsage("extractHtml() htmlClickHouseEntity not available.");
			}
		}

		// create output
		File outputFile = new File(outputFilePathLocation);

		try {
			FileUtils.writeLines(outputFile, IConstants.UTF_8, outputLineList);
		} catch (Exception e) {
			e.printStackTrace();
		}

		FormatUtils.getInstance().logMemoryUsage("extractHtml() ends.");
	}
}
