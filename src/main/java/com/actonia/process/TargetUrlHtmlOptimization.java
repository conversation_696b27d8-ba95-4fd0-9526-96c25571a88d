package com.actonia.process;

import com.actonia.dao.LocalTargetUrlHtmlClickHouseDAO;
import com.actonia.utils.FormatUtils;

public class TargetUrlHtmlOptimization {

	public TargetUrlHtmlOptimization() {
	}

	public static void main(String[] args) throws Exception {
		new TargetUrlHtmlOptimization().process(args);
	}

	private void process(String[] args) throws Exception {
		optimizeTargetUrlHtml();
	}

	// optimize the 'backlink_data' table
	private void optimizeTargetUrlHtml() throws Exception {
		FormatUtils.getInstance().logMemoryUsage("optimizeTargetUrlHtml() begins.");
		long startTimestamp = System.currentTimeMillis();
		LocalTargetUrlHtmlClickHouseDAO.getInstance().optimizeAllPartitions();
		FormatUtils.getInstance().logMemoryUsage("optimizeTargetUrlHtml() ends. total elapsed (s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}
}
