package com.actonia.process;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;

import com.actonia.IConstants;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.Md5Util;
import com.actonia.value.object.LogValueObject;

public class CrawlerPerformanceComparison {

	public static void main(String[] args) {
		try {
			new CrawlerPerformanceComparison().process();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void process() throws Exception {

		LogValueObject logValueObjectCurrent = null;
		LogValueObject logValueObjectNew = null;

		// map key = URL hash code
		// map value = LogValueObject
		Map<String, LogValueObject> urlLogValueObjectMapCurrent = null;

		// map key = URL hash code
		// map value = LogValueObject
		Map<String, LogValueObject> urlLogValueObjectMapNew = null;

		// map key = domain ID
		// map value = URL hash code LogValueObject map
		Map<Integer, Map<String, LogValueObject>> domainUrlLogValueObjectMapMapCurrent = processCurrentFiles();

		// map key = domain ID
		// map value = URL hash code LogValueObject map
		Map<Integer, Map<String, LogValueObject>> domainUrlLogValueObjectMapMapNew = processNewFile();

		for (Integer domainId : domainUrlLogValueObjectMapMapCurrent.keySet()) {
			if (domainUrlLogValueObjectMapMapNew.containsKey(domainId)) {
				urlLogValueObjectMapCurrent = domainUrlLogValueObjectMapMapCurrent.get(domainId);
				urlLogValueObjectMapNew = domainUrlLogValueObjectMapMapNew.get(domainId);
				for (String urlHashCode : urlLogValueObjectMapCurrent.keySet()) {
					if (urlLogValueObjectMapCurrent.containsKey(urlHashCode) && urlLogValueObjectMapNew.containsKey(urlHashCode)) {
						logValueObjectCurrent = urlLogValueObjectMapCurrent.get(urlHashCode);
						logValueObjectNew = urlLogValueObjectMapNew.get(urlHashCode);
						System.out.println("domainId`" + logValueObjectCurrent.getDomainId() + "`url`" + logValueObjectCurrent.getUrl() + "`respCurrent`"
								+ logValueObjectCurrent.getResponseCode() + "`respNew`" + logValueObjectNew.getResponseCode() + "`respDiff`"
								+ (logValueObjectCurrent.getResponseCode() - logValueObjectNew.getResponseCode()) + "`elapsedCurrent`"
								+ logValueObjectCurrent.getElapsedInSeconds() + "`elapsedNew`" + logValueObjectNew.getElapsedInSeconds() + "`elapsedDiff`"
								+ (logValueObjectCurrent.getElapsedInSeconds() - logValueObjectNew.getElapsedInSeconds()));
					}
				}
			}
		}

	}

	private Map<Integer, Map<String, LogValueObject>> processCurrentFiles() throws Exception {
		FormatUtils.getInstance().logMemoryUsage("processCurrentFiles() begins.");

		// map key = domain ID
		// map value = URL hash code LogValueObject map
		Map<Integer, Map<String, LogValueObject>> domainUrlLogValueObjectMapMap = new HashMap<Integer, Map<String, LogValueObject>>();

		// map key = URL hash code
		// map value = LogValueObject
		Map<String, LogValueObject> urlLogValueObjectMap = null;

		String fileLocation = null;
		List<String> logList = null;
		String[] stringArray = null;
		LogValueObject logValueObjectNew = null;
		LogValueObject logValueObjectExisting = null;
		String urlHashCode = null;
		String folderLocation = "/home/<USER>/source/test_crawl/data/";
		String[] currentCrawlerLogFileNames = getCurrentCrawlerLogFileNames();
		for (String currentCrawlerLogFileName : currentCrawlerLogFileNames) {
			fileLocation = folderLocation + currentCrawlerLogFileName;
			FormatUtils.getInstance().logMemoryUsage("processCurrentFiles() fileLocation=" + fileLocation);
			logList = FileUtils.readLines(new File(fileLocation), IConstants.UTF_8);
			for (String log : logList) {
				stringArray = log.split(IConstants.COMMA);
				logValueObjectNew = new LogValueObject();
				for (int i = 0; i < stringArray.length; i++) {
					if (i == 1) {
						//FormatUtils.getInstance().logMemoryUsage("i=" + i + ",domainID=" + StringUtils.removeStart(stringArray[i], "queueName=TARGET_URL_HTML_EN_"));
						logValueObjectNew.setDomainId(NumberUtils.toInt(StringUtils.removeStart(stringArray[i], "queueName=TARGET_URL_HTML_EN_")));
					} else if (i == 2) {
						//FormatUtils.getInstance().logMemoryUsage("i=" + i + ",url=" + StringUtils.removeStart(stringArray[i], "url="));
						logValueObjectNew.setUrl(StringUtils.removeStart(stringArray[i], "url="));
					} else if (i == 3) {
						//FormatUtils.getInstance().logMemoryUsage("i=" + i + ",responseCode=" + StringUtils.removeStart(stringArray[i], "httpStatusCode="));
						logValueObjectNew.setResponseCode(NumberUtils.toInt(StringUtils.removeStart(stringArray[i], "httpStatusCode=")));
					} else if (i == 4) {
						//FormatUtils.getInstance().logMemoryUsage("i=" + i + ",elapsed=" + StringUtils.removeStart(stringArray[i], "message elapsed (sec.)="));
						logValueObjectNew.setElapsedInSeconds(NumberUtils.toInt(StringUtils.removeStart(stringArray[i], "message elapsed (sec.)=")));
					}
				}

				if (domainUrlLogValueObjectMapMap.containsKey(logValueObjectNew.getDomainId())) {
					urlLogValueObjectMap = domainUrlLogValueObjectMapMap.get(logValueObjectNew.getDomainId());
				} else {
					urlLogValueObjectMap = new HashMap<String, LogValueObject>();
				}
				urlHashCode = Md5Util.Md5(logValueObjectNew.getUrl());
				if (urlLogValueObjectMap.containsKey(urlHashCode)) {
					logValueObjectExisting = urlLogValueObjectMap.get(urlHashCode);
					logValueObjectNew.setElapsedInSeconds((logValueObjectExisting.getElapsedInSeconds() + logValueObjectNew.getElapsedInSeconds()) / 2);
				}
				urlLogValueObjectMap.put(urlHashCode, logValueObjectNew);
				domainUrlLogValueObjectMapMap.put(logValueObjectNew.getDomainId(), urlLogValueObjectMap);
			}
			FormatUtils.getInstance().logMemoryUsage("processCurrentFiles() domainUrlLogValueObjectMapMap.size()=" + domainUrlLogValueObjectMapMap.size());
		}
		FormatUtils.getInstance().logMemoryUsage("processCurrentFiles() ends.");

		//		for (Integer testDomainId : domainUrlLogValueObjectMapMap.keySet()) {
		//			urlLogValueObjectMap = domainUrlLogValueObjectMapMap.get(testDomainId);
		//			for (String testUrlHashCode : urlLogValueObjectMap.keySet()) {
		//				logValueObjectNew = urlLogValueObjectMap.get(testUrlHashCode);
		//				FormatUtils.getInstance().logMemoryUsage("processCurrentFiles() logValueObject=" + logValueObjectNew.toString());
		//			}
		//		}

		return domainUrlLogValueObjectMapMap;
	}

	private Map<Integer, Map<String, LogValueObject>> processNewFile() throws Exception {
		FormatUtils.getInstance().logMemoryUsage("processNewFile() begins.");

		// map key = domain ID
		// map value = URL hash code LogValueObject map
		Map<Integer, Map<String, LogValueObject>> domainUrlLogValueObjectMapMap = new HashMap<Integer, Map<String, LogValueObject>>();

		// map key = URL hash code
		// map value = LogValueObject
		Map<String, LogValueObject> urlLogValueObjectMap = null;

		List<String> logList = null;
		int domainId = 0;
		String[] stringArray = null;
		LogValueObject logValueObjectNew = null;
		LogValueObject logValueObjectExisting = null;
		String urlHashCode = null;
		String queueName = null;
		Map<String, Integer> queueNameDomainIdMap = getQueueNameDomainIdMap();
		String folderLocation = "/home/<USER>/source/test_crawl/data/";
		String fileLocation = folderLocation + "new.javascript.cluster.log.txt";
		FormatUtils.getInstance().logMemoryUsage("processNewFile() fileLocation=" + fileLocation);
		logList = FileUtils.readLines(new File(fileLocation), IConstants.UTF_8);
		for (String log : logList) {
			stringArray = log.split(IConstants.BACKTICK);
			logValueObjectNew = new LogValueObject();
			for (int i = 0; i < stringArray.length; i++) {
				if (i == 3) {
					queueName = stringArray[i];
					if (queueNameDomainIdMap.containsKey(queueName)) {
						domainId = queueNameDomainIdMap.get(queueName);
					} else {
						domainId = 0;
					}
					logValueObjectNew.setDomainId(domainId);
				} else if (i == 5) {
					logValueObjectNew.setUrl(stringArray[i]);
				} else if (i == 7) {
					logValueObjectNew.setResponseCode(NumberUtils.toInt(stringArray[i]));
				} else if (i == 9) {
					logValueObjectNew.setElapsedInSeconds(NumberUtils.toInt(stringArray[i]));
				}
			}

			if (domainUrlLogValueObjectMapMap.containsKey(logValueObjectNew.getDomainId())) {
				urlLogValueObjectMap = domainUrlLogValueObjectMapMap.get(logValueObjectNew.getDomainId());
			} else {
				urlLogValueObjectMap = new HashMap<String, LogValueObject>();
			}
			urlHashCode = Md5Util.Md5(logValueObjectNew.getUrl());
			if (urlLogValueObjectMap.containsKey(urlHashCode)) {
				logValueObjectExisting = urlLogValueObjectMap.get(urlHashCode);
				logValueObjectNew.setElapsedInSeconds((logValueObjectExisting.getElapsedInSeconds() + logValueObjectNew.getElapsedInSeconds()) / 2);
			}
			urlLogValueObjectMap.put(urlHashCode, logValueObjectNew);
			domainUrlLogValueObjectMapMap.put(logValueObjectNew.getDomainId(), urlLogValueObjectMap);
		}
		FormatUtils.getInstance().logMemoryUsage("processNewFile() ends. domainUrlLogValueObjectMapMap.size()=" + domainUrlLogValueObjectMapMap.size());

		//		for (Integer testDomainId : domainUrlLogValueObjectMapMap.keySet()) {
		//			urlLogValueObjectMap = domainUrlLogValueObjectMapMap.get(testDomainId);
		//			for (String testUrlHashCode : urlLogValueObjectMap.keySet()) {
		//				logValueObjectNew = urlLogValueObjectMap.get(testUrlHashCode);
		//				FormatUtils.getInstance().logMemoryUsage("processNewFile() logValueObject=" + logValueObjectNew.toString());
		//			}
		//		}

		return domainUrlLogValueObjectMapMap;
	}

	private String[] getCurrentCrawlerLogFileNames() {
		return new String[] { "domain_10619_all.log", "domain_10723_all.log", "domain_10750_all.log", "domain_10867_all.log", "domain_10871_all.log",
				"domain_10876_all.log", "domain_10893_all.log", "domain_11162_all.log", "domain_11184_all.log", "domain_1335_all.log", "domain_1352_all.log",
				"domain_1909_all.log", "domain_256_all.log", "domain_2702_all.log", "domain_4661_all.log", "domain_7255_all.log", "domain_7487_all.log",
				"domain_7684_all.log", "domain_7691_all.log", "domain_8614_all.log", "domain_9666_all.log", "domain_9688_all.log", "domain_9994_all.log", };
	}

	private Map<String, Integer> getQueueNameDomainIdMap() {
		Map<String, Integer> queueNameDomainIdMap = new HashMap<String, Integer>();
		queueNameDomainIdMap.put("TEST_COMPETITOR_URL_HTML_1", 256);
		queueNameDomainIdMap.put("TEST_COMPETITOR_URL_HTML_2", 1335);
		queueNameDomainIdMap.put("TEST_COMPETITOR_URL_HTML_3", 1352);
		queueNameDomainIdMap.put("TEST_COMPETITOR_URL_HTML_4", 1909);
		queueNameDomainIdMap.put("TEST_COMPETITOR_URL_HTML_5", 2702);
		queueNameDomainIdMap.put("TEST_COMPETITOR_URL_HTML_6", 4661);
		queueNameDomainIdMap.put("TEST_COMPETITOR_URL_HTML_7", 7255);
		queueNameDomainIdMap.put("TEST_COMPETITOR_URL_HTML_8", 7487);
		queueNameDomainIdMap.put("TEST_COMPETITOR_URL_HTML_9", 7684);
		queueNameDomainIdMap.put("TEST_COMPETITOR_URL_HTML_10", 7691);
		queueNameDomainIdMap.put("TEST_COMPETITOR_URL_HTML_11", 8614);
		queueNameDomainIdMap.put("TEST_COMPETITOR_URL_HTML_12", 9666);
		queueNameDomainIdMap.put("TEST_COMPETITOR_URL_HTML_13", 9688);
		queueNameDomainIdMap.put("TEST_COMPETITOR_URL_HTML_14", 9994);
		queueNameDomainIdMap.put("TEST_COMPETITOR_URL_HTML_15", 10619);
		queueNameDomainIdMap.put("TEST_COMPETITOR_URL_HTML_16", 10723);
		queueNameDomainIdMap.put("TEST_COMPETITOR_URL_HTML_17", 10750);
		queueNameDomainIdMap.put("TEST_COMPETITOR_URL_HTML_18", 10867);
		queueNameDomainIdMap.put("TEST_COMPETITOR_URL_HTML_19", 10871);
		queueNameDomainIdMap.put("TEST_COMPETITOR_URL_HTML_20", 10876);
		queueNameDomainIdMap.put("TEST_COMPETITOR_URL_HTML_21", 10893);
		queueNameDomainIdMap.put("TEST_COMPETITOR_URL_HTML_22", 11162);
		queueNameDomainIdMap.put("TEST_COMPETITOR_URL_HTML_23", 11184);
		return queueNameDomainIdMap;
	}

}
