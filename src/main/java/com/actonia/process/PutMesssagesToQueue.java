package com.actonia.process;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;

import com.actonia.IConstants;
import com.actonia.dao.CompetitorUrlWeeklyCrawlTrackingEntityDAO;
import com.actonia.dao.OwnDomainEntityDAO;
import com.actonia.entity.CompetitorUrlWeeklyCrawlTrackingEntity;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.utils.Md5Util;
import com.actonia.utils.PutMessageUtils;
import com.actonia.utils.SQSUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.UrlCrawlParametersVO;
import com.google.gson.Gson;

public class PutMesssagesToQueue {

	private static final int SQS_MESSAGE_BATCH_SIZE = 10;

	private OwnDomainEntityDAO ownDomainEntityDAO;

	private CompetitorUrlWeeklyCrawlTrackingEntityDAO competitorUrlWeeklyCrawlTrackingEntityDAO;

	public PutMesssagesToQueue() {
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		competitorUrlWeeklyCrawlTrackingEntityDAO = SpringBeanFactory.getBean("competitorUrlWeeklyCrawlTrackingEntityDAO");
	}

	public static void main(String[] args) throws Exception {
		new PutMesssagesToQueue().process(args);
	}

	private void process(String[] args) throws Exception {

		SQSUtils.getInstance();

		String fileLocation = args[0];

		System.out.println("process() runtime parameter: fileLocation=" + fileLocation);

		String queueName = args[1];

		System.out.println("process() runtime parameter: queueName=" + queueName);

		List<String> jsonMessageList = FileUtils.readLines(new File(fileLocation), IConstants.UTF_8);
		String queueUrl = SQSUtils.getInstance().createQueue(queueName);
		SQSUtils.getInstance().purgeQueue(queueUrl);
		//if (StringUtils.equalsIgnoreCase(queueName, "TEST_TARGET_URL_HTML_EN_7006") == false) {
		//	SQSUtils.getInstance().purgeQueue(queueUrl);
		//}
		put(jsonMessageList, queueUrl, queueName);
		System.out.println("process() end.");
	}

	private void put(List<String> jsonMessageList, String queueUrl, String queueName) throws Exception {
		String messageIdString = null;
		String messageBody = null;
		int totalMessages = 0;
		String jsonMessageFinal = null;
		List<OwnDomainEntity> ownDomainEntityList = null;
		String domainNames = null;
		String userAgents = null;
		//		String urlString = null;
		//		String fileName = null;
		int totalFileNameTooLong = 0;
		int totalFileNameNotTooLong = 0;

		// map key = domain name
		// map key = user agent
		Map<String, String> domainNameUserAgentMap = new HashMap<String, String>();

		if (StringUtils.equalsIgnoreCase(queueName, IConstants.QUEUE_NAME_SOLR_COMPETITOR_URL_HTML_QUEUE_NAMES)
				|| StringUtils.equalsIgnoreCase(queueName, IConstants.QUEUE_NAME_COMPETITOR_URL_HTML_QUEUE_NAMES)
				|| StringUtils.equalsIgnoreCase(queueName, IConstants.QUEUE_NAME_LINK_CLARITY_QUEUE_NAMES)) {
			ownDomainEntityList = ownDomainEntityDAO.queryForAll();
			domainNameUserAgentMap = PutMessageUtils.getInstance().getDomainNameUserAgentMap(ownDomainEntityList);
			domainNames = PutMessageUtils.getInstance().getDomainNames(domainNameUserAgentMap);
			userAgents = PutMessageUtils.getInstance().getUserAgents(domainNameUserAgentMap);
		}

		Map<String, String> messages = new HashMap<String, String>();
		if (jsonMessageList != null && jsonMessageList.size() > 0) {
			for (String jsonMessage : jsonMessageList) {
				System.out.println("put() original jsonMessage=" + jsonMessage);
				if (StringUtils.containsIgnoreCase(queueName, "EXTRACT_RAW_HTML_") == true
						&& StringUtils.equalsIgnoreCase(queueName, "EXTRACT_RAW_HTML_QUEUE_NAMES") == false) {
					jsonMessageFinal = "{\"languageCode\":\"en\",\"urlId\":168,\"url\":\"" + jsonMessage + "\"}";
				} else if (StringUtils.equalsIgnoreCase(queueName, IConstants.QUEUE_NAME_SOLR_COMPETITOR_URL_HTML_QUEUE_NAMES)
						|| StringUtils.equalsIgnoreCase(queueName, IConstants.QUEUE_NAME_COMPETITOR_URL_HTML_QUEUE_NAMES)
						|| StringUtils.equalsIgnoreCase(queueName, IConstants.QUEUE_NAME_LINK_CLARITY_QUEUE_NAMES)) {
					jsonMessageFinal = getJsonWithDomainNamesUserAgents(jsonMessage, queueName, domainNames, userAgents);
				} else {
					jsonMessageFinal = jsonMessage;
				}
				messageIdString = Md5Util.Md5(jsonMessageFinal);
				messageBody = jsonMessageFinal;
				try {
					messages.put(messageIdString, messageBody);
					totalMessages++;
					if (messages.size() >= SQS_MESSAGE_BATCH_SIZE) {
						SQSUtils.getInstance().sendBatchMessageToQueue(queueUrl, messages);
						System.out.println("put() total message sent=" + totalMessages);
						messages = new HashMap<String, String>();
						//messageAttributeMapMap = new HashMap<String, Map<String, MessageAttributeValue>>();
					}
					Thread.sleep(100);
				} catch (Exception e) {
					System.out.println("put() exception message=" + e.getMessage());
					e.printStackTrace();
					throw e;
				}
			}
		}
		if (messages != null && messages.size() > 0) {
			SQSUtils.getInstance().sendBatchMessageToQueue(queueUrl, messages);
			System.out.println("put() total message sent=" + totalMessages);
			messages = new HashMap<String, String>();
			//messageAttributeMapMap = new HashMap<String, Map<String, MessageAttributeValue>>();
		}

		if (StringUtils.equalsIgnoreCase(queueName, "TEST_TARGET_URL_HTML_EN_7006") == true) {
			System.out.println("put() end. totalFileNameTooLong=" + totalFileNameTooLong + ",totalFileNameNotTooLong=" + totalFileNameNotTooLong);
		}
	}

	private String getJsonWithDomainNamesUserAgents(String jsonMessageInput, String queueName, String domainNames, String userAgents) throws Exception {
		String jsonMessageOutput = null;
		List<UrlCrawlParametersVO> testUrlCrawlParametersVoList = null;
		List<UrlCrawlParametersVO> urlCrawlParametersVoList = null;
		UrlCrawlParametersVO[] urlCrawlParametersVoArray = null;
		UrlCrawlParametersVO urlCrawlParametersVO = null;
		Gson gson = new Gson();
		String crawlQueueName = null;
		String delayInSecondsPerHttpRequestString = null;
		int delayInSecondsPerHttpRequest = 0;
		String maxConcurrentCrawlThreadsString = null;
		int maxConcurrentCrawlThreads = 0;
		CompetitorUrlWeeklyCrawlTrackingEntity competitorUrlWeeklyCrawlTrackingEntity = null;
		//String competitorDomain = null;
		int totalCompetitorUrls = 0;
		urlCrawlParametersVoArray = gson.fromJson(jsonMessageInput, UrlCrawlParametersVO[].class);
		if (urlCrawlParametersVoArray != null && urlCrawlParametersVoArray.length > 0) {
			for (int idx = 0; idx < urlCrawlParametersVoArray.length; idx++) {
				urlCrawlParametersVO = urlCrawlParametersVoArray[idx];
				// queue name
				if (urlCrawlParametersVO.getType().equalsIgnoreCase(IConstants.QUEUE_NAME)) {
					crawlQueueName = urlCrawlParametersVO.getData();
				}
				// delay in seconds per HTTP request
				else if (urlCrawlParametersVO.getType().equalsIgnoreCase(IConstants.DELAY_IN_SECONDS)) {
					delayInSecondsPerHttpRequestString = urlCrawlParametersVO.getData();
					delayInSecondsPerHttpRequest = Integer.parseInt(delayInSecondsPerHttpRequestString);
				}
				// maximum number of threads per queue
				else if (urlCrawlParametersVO.getType().equalsIgnoreCase(IConstants.MAX_CONCURRENT_THREADS)) {
					maxConcurrentCrawlThreadsString = urlCrawlParametersVO.getData();
					maxConcurrentCrawlThreads = Integer.parseInt(maxConcurrentCrawlThreadsString);
				}
			}
			//FormatUtils.getInstance().logMemoryUsage("getJsonWithDomainNamesUserAgents() extracted from jsonMessage, crawlQueueName=" + crawlQueueName + ",maxConcurrentCrawlThreads="
			//		+ maxConcurrentCrawlThreads + ",delayInSecondsPerHttpRequest=" + delayInSecondsPerHttpRequest);
		}

		// check if 'competitor_url_weekly_crawl_tracking' record exists to determine maxConcurrentCrawlThreads and delayInSecondsPerHttpRequest
		competitorUrlWeeklyCrawlTrackingEntity = competitorUrlWeeklyCrawlTrackingEntityDAO.get(IConstants.PROCESS_TYPE_NUMBER_CLICKHOUSE, crawlQueueName);
		if (competitorUrlWeeklyCrawlTrackingEntity != null) {
			//competitorDomain = competitorUrlWeeklyCrawlTrackingEntity.getCompetitorDomain();
			totalCompetitorUrls = competitorUrlWeeklyCrawlTrackingEntity.getTotalUrls();
			maxConcurrentCrawlThreads = PutMessageUtils.getInstance().getMaxConcurrentCrawlThreads(totalCompetitorUrls, maxConcurrentCrawlThreads);
			delayInSecondsPerHttpRequest = PutMessageUtils.getInstance().getDelayInSecondsPerHttpRequest(totalCompetitorUrls);
			//FormatUtils.getInstance()
			//		.logMemoryUsage("getJsonWithDomainNamesUserAgents() updated based on 'competitor_url_weekly_crawl_tracking' record, crawlQueueName=" + crawlQueueName + ",competitorDomain="
			//				+ competitorDomain + ",totalCompetitorUrls=" + totalCompetitorUrls + ",maxConcurrentCrawlThreads=" + maxConcurrentCrawlThreads
			//				+ ",delayInSecondsPerHttpRequest=" + delayInSecondsPerHttpRequest);
			for (int idx = 0; idx < urlCrawlParametersVoArray.length; idx++) {
				urlCrawlParametersVO = urlCrawlParametersVoArray[idx];
				// delay in seconds per HTTP request
				if (urlCrawlParametersVO.getType().equalsIgnoreCase(IConstants.DELAY_IN_SECONDS)) {
					urlCrawlParametersVO.setData(String.valueOf(delayInSecondsPerHttpRequest));
				}
				// maximum number of threads per queue
				else if (urlCrawlParametersVO.getType().equalsIgnoreCase(IConstants.MAX_CONCURRENT_THREADS)) {
					urlCrawlParametersVO.setData(String.valueOf(maxConcurrentCrawlThreads));
				}
			}
		}

		testUrlCrawlParametersVoList = Arrays.asList(urlCrawlParametersVoArray);
		urlCrawlParametersVoList = new ArrayList<UrlCrawlParametersVO>();
		for (UrlCrawlParametersVO testUrlCrawlParametersVO : testUrlCrawlParametersVoList) {
			urlCrawlParametersVoList.add(testUrlCrawlParametersVO);
		}

		// domain names
		urlCrawlParametersVO = new UrlCrawlParametersVO();
		urlCrawlParametersVO.setType(IConstants.DOMAIN_NAMES);
		urlCrawlParametersVO.setData(domainNames);
		urlCrawlParametersVoList.add(urlCrawlParametersVO);

		// user agents
		urlCrawlParametersVO = new UrlCrawlParametersVO();
		urlCrawlParametersVO.setType(IConstants.USER_AGENTS);
		urlCrawlParametersVO.setData(userAgents);
		urlCrawlParametersVoList.add(urlCrawlParametersVO);

		// 
		urlCrawlParametersVoArray = urlCrawlParametersVoList.toArray(new UrlCrawlParametersVO[0]);

		jsonMessageOutput = new Gson().toJson(urlCrawlParametersVoArray, UrlCrawlParametersVO[].class);
		//System.out.println("getJsonWithDomainNamesUserAgents() final jsonMessage=" + jsonMessageOutput);
		return jsonMessageOutput;
	}

	//	private String getFileName(String urlString) {
	//		String fileName = urlString;
	//		fileName = StringUtils.removeEndIgnoreCase(fileName, "/");
	//		fileName = StringUtils.replace(fileName, "/", "_");
	//		fileName = StringUtils.replace(fileName, ">", "_");
	//		fileName = StringUtils.replace(fileName, "<", "_");
	//		fileName = StringUtils.replace(fileName, "|", "_");
	//		fileName = StringUtils.replace(fileName, ":", "_");
	//		fileName = StringUtils.replace(fileName, "&", "_");
	//		fileName = StringUtils.replace(fileName, "?", "_");
	//		fileName = StringUtils.replace(fileName, "=", "_");
	//		fileName = StringUtils.replace(fileName, "*", "_");
	//		fileName = StringUtils.replace(fileName, ".", "_");
	//		fileName = StringUtils.replace(fileName, "___", "_");
	//		fileName = StringUtils.replace(fileName, "__", "_");
	//		fileName = fileName + ".txt";
	//		return fileName;
	//	}
}
