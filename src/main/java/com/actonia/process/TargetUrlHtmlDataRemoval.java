package com.actonia.process;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.dao.LocalTargetUrlHtmlClickHouseDAO;
import com.actonia.dao.TargetUrlHtmlClickHouseDAO;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.utils.CrawlerUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.value.object.TargetUrlHtmlDataRemovalValueObject;

public class TargetUrlHtmlDataRemoval {

	private static final String DIS_TARGET_URL_HTML_TABLE_NAME = null;
	//private static final String DIS_TARGET_URL_HTML_TABLE_NAME = "unit_test_target_url_html";

	private static final String LOCAL_TARGET_URL_HTML_TABLE_NAME = null;
	//private static final String LOCAL_TARGET_URL_HTML_TABLE_NAME = "unit_test_target_url_html";

	private Set<Integer> processYearMonthSet = new HashSet<Integer>();

	public TargetUrlHtmlDataRemoval() {
	}

	public static void main(String[] args) throws Exception {
		try {
			new TargetUrlHtmlDataRemoval().process(args);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void process(String[] args) throws Exception {

		if (args == null || args.length == 0) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters required.");
			return;
		}

		// runtime parameter 1: input file location path
		String inputFileLocationPath = args[0];
		if (StringUtils.isBlank(inputFileLocationPath)) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameter 1: input file location path required.");
			return;
		}
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameter 1: inputFileLocationPath=" + inputFileLocationPath);

		List<TargetUrlHtmlDataRemovalValueObject> targetUrlHtmlDataRemovalValueObjectList = getTargetUrlHtmlDataRemovalValueObjectList(inputFileLocationPath);
		if (targetUrlHtmlDataRemovalValueObjectList != null && targetUrlHtmlDataRemovalValueObjectList.size() > 0) {
			FormatUtils.getInstance().logMemoryUsage("process() targetUrlHtmlDataRemovalValueObjectList.size()=" + targetUrlHtmlDataRemovalValueObjectList.size());
			createNegativeSignRecords(targetUrlHtmlDataRemovalValueObjectList);

			FormatUtils.getInstance().logMemoryUsage("process() pause for 168 seconds...");

			Thread.sleep(168000);

			optimizeTargetUrlHtml();
		} else {
			FormatUtils.getInstance().logMemoryUsage("process() targetUrlHtmlDataRemovalValueObjectList is empty.");
		}
	}

	private List<TargetUrlHtmlDataRemovalValueObject> getTargetUrlHtmlDataRemovalValueObjectList(String inputFileLocationPath) throws Exception {

		FormatUtils.getInstance().logMemoryUsage("getTargetUrlHtmlDataRemovalValueObjectList() begins. inputFileLocationPath=" + inputFileLocationPath);

		List<TargetUrlHtmlDataRemovalValueObject> targetUrlHtmlDataRemovalValueObjectList = new ArrayList<TargetUrlHtmlDataRemovalValueObject>();

		String[] stringArray = null;
		String crawlTimestampString = null;
		Date crawlTimestamp = null;
		String domainIdString = null;
		Integer domainId = null;
		String targetUrlString = null;
		TargetUrlHtmlDataRemovalValueObject targetUrlHtmlDataRemovalValueObject = null;
		File inputFile = new File(inputFileLocationPath);
		List<String> inputStringList = FileUtils.readLines(inputFile, IConstants.UTF_8);
		if (inputStringList != null && inputStringList.size() > 0) {
			FormatUtils.getInstance().logMemoryUsage("getTargetUrlHtmlDataRemovalValueObjectList() inputStringList.size()=" + inputStringList.size());
			nextInputString: for (String inputString : inputStringList) {
				stringArray = inputString.split(IConstants.TAB);
				if (stringArray != null && stringArray.length > 0) {

					if (stringArray.length >= 1) {
						crawlTimestampString = StringUtils.trim(stringArray[0]);
					} else {
						crawlTimestampString = null;
					}

					if (stringArray.length >= 2) {
						domainIdString = StringUtils.trim(stringArray[1]);
					} else {
						domainIdString = null;
					}

					if (stringArray.length >= 3) {
						targetUrlString = StringUtils.trim(stringArray[2]);
					} else {
						targetUrlString = null;
					}

					// crawl timestamp (required)
					if (StringUtils.isNotBlank(crawlTimestampString)) {
						try {
							crawlTimestamp = DateUtils.parseDateStrictly(crawlTimestampString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS });
						} catch (Exception e) {
							FormatUtils.getInstance()
									.logMemoryUsage("getTargetUrlHtmlDataRemovalValueObjectList() error--crawl timestamp not parsable, inputString=" + inputString);
							continue nextInputString;
						}
					} else {
						FormatUtils.getInstance()
								.logMemoryUsage("getTargetUrlHtmlDataRemovalValueObjectList() error--crawl timestamp not available, inputString=" + inputString);
						continue nextInputString;
					}

					// domain ID (required)
					if (StringUtils.isNotBlank(domainIdString)) {
						domainId = NumberUtils.toInt(domainIdString);
						if (domainId < 1) {
							FormatUtils.getInstance()
									.logMemoryUsage("getTargetUrlHtmlDataRemovalValueObjectList() error--domain ID invalid, inputString=" + inputString);
							continue nextInputString;
						}
					} else {
						FormatUtils.getInstance()
								.logMemoryUsage("getTargetUrlHtmlDataRemovalValueObjectList() error--domain ID not available, inputString=" + inputString);
						continue nextInputString;
					}

					// target URL (required)
					if (StringUtils.isBlank(targetUrlString)) {
						FormatUtils.getInstance()
								.logMemoryUsage("getTargetUrlHtmlDataRemovalValueObjectList() error--targetUrlString not available, inputString=" + inputString);
						continue nextInputString;
					}

					targetUrlHtmlDataRemovalValueObject = new TargetUrlHtmlDataRemovalValueObject();
					targetUrlHtmlDataRemovalValueObject.setCrawlTimestamp(crawlTimestamp);
					targetUrlHtmlDataRemovalValueObject.setDomainId(domainId);
					targetUrlHtmlDataRemovalValueObject.setTargetUrl(targetUrlString);
					targetUrlHtmlDataRemovalValueObjectList.add(targetUrlHtmlDataRemovalValueObject);
				}
			}
		}

		FormatUtils.getInstance().logMemoryUsage(
				"getTargetUrlHtmlDataRemovalValueObjectList() ends. targetUrlHtmlDataRemovalValueObjectList.size()=" + targetUrlHtmlDataRemovalValueObjectList.size());

		return targetUrlHtmlDataRemovalValueObjectList;
	}

	private void createNegativeSignRecords(List<TargetUrlHtmlDataRemovalValueObject> targetUrlHtmlDataRemovalValueObjectList) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("createNegativeSignRecords() begins.");
		int batchCreateSize = TargetUrlHtmlClickHouseDAO.getInstance().getBatchCreationSize() * 10;
		Date crawlTimestamp = null;
		Integer domainId = null;
		String targetUrlString = null;
		HtmlClickHouseEntity htmlClickHouseEntity = null;
		Integer processYearMonth = null;
		int totalTargetUrlHtmlCreated = 0;
		List<String> databaseFields = CrawlerUtils.getInstance().getTargetUrlHtmlTableAllFieldNames();
		List<HtmlClickHouseEntity> htmlClickHouseEntityToBeCreatedList = new ArrayList<HtmlClickHouseEntity>();
		for (TargetUrlHtmlDataRemovalValueObject targetUrlHtmlDataRemovalValueObject : targetUrlHtmlDataRemovalValueObjectList) {
			FormatUtils.getInstance()
					.logMemoryUsage("createNegativeSignRecords() targetUrlHtmlDataRemovalValueObject=" + targetUrlHtmlDataRemovalValueObject.toString());
			crawlTimestamp = targetUrlHtmlDataRemovalValueObject.getCrawlTimestamp();
			domainId = targetUrlHtmlDataRemovalValueObject.getDomainId();
			targetUrlString = targetUrlHtmlDataRemovalValueObject.getTargetUrl();

			// retrieve existing record
			htmlClickHouseEntity = TargetUrlHtmlClickHouseDAO.getInstance().getByCrawlTimestamp(crawlTimestamp, domainId, targetUrlString, databaseFields,
					DIS_TARGET_URL_HTML_TABLE_NAME);
			if (htmlClickHouseEntity != null) {
				FormatUtils.getInstance().logMemoryUsage("createNegativeSignRecords() htmlClickHouseEntity available for crawlTimestamp=" + crawlTimestamp
						+ ",domainId=" + domainId + ",targetUrlString=" + targetUrlString);
				htmlClickHouseEntity.setSign(IConstants.CLICKHOUSE_SIGN_NEGATIVE_1);
				processYearMonth = getProcessYearMonth(htmlClickHouseEntity);
				if (processYearMonth != null) {
					processYearMonthSet.add(processYearMonth);
				}
				htmlClickHouseEntityToBeCreatedList.add(htmlClickHouseEntity);
				if (htmlClickHouseEntityToBeCreatedList != null && htmlClickHouseEntityToBeCreatedList.size() >= batchCreateSize) {
					long startTimestamp = System.currentTimeMillis();
					TargetUrlHtmlClickHouseDAO.getInstance().createBatch(null, null, htmlClickHouseEntityToBeCreatedList, DIS_TARGET_URL_HTML_TABLE_NAME);
					totalTargetUrlHtmlCreated = totalTargetUrlHtmlCreated + htmlClickHouseEntityToBeCreatedList.size();
					FormatUtils.getInstance().logMemoryUsage("createNegativeSignRecords() totalTargetUrlHtmlCreated=" + totalTargetUrlHtmlCreated + ",elapsed(ms.)="
							+ (System.currentTimeMillis() - startTimestamp));
					htmlClickHouseEntityToBeCreatedList = new ArrayList<HtmlClickHouseEntity>();
				}
			} else {
				FormatUtils.getInstance().logMemoryUsage("createNegativeSignRecords() error--htmlClickHouseEntity not available for crawlTimestamp=" + crawlTimestamp
						+ ",domainId=" + domainId + ",targetUrlString=" + targetUrlString);
			}
		}
		if (htmlClickHouseEntityToBeCreatedList != null && htmlClickHouseEntityToBeCreatedList.size() > 0) {
			TargetUrlHtmlClickHouseDAO.getInstance().createBatch(null, null, htmlClickHouseEntityToBeCreatedList, DIS_TARGET_URL_HTML_TABLE_NAME);
			totalTargetUrlHtmlCreated = totalTargetUrlHtmlCreated + htmlClickHouseEntityToBeCreatedList.size();
			FormatUtils.getInstance().logMemoryUsage("createNegativeSignRecords() totalTargetUrlHtmlCreated=" + totalTargetUrlHtmlCreated);
			htmlClickHouseEntityToBeCreatedList = new ArrayList<HtmlClickHouseEntity>();
		}
		FormatUtils.getInstance().logMemoryUsage("createNegativeSignRecords() ends. totalTargetUrlHtmlCreated=" + totalTargetUrlHtmlCreated);
	}

	private Integer getProcessYearMonth(HtmlClickHouseEntity htmlClickHouseEntity) {
		Integer processYearMonth = null;
		Date trackDate = htmlClickHouseEntity.getTrackDate();
		if (trackDate != null) {
			processYearMonth = NumberUtils.toInt(DateFormatUtils.format(trackDate, IConstants.DATE_FORMAT_YYYYMM));
		}
		return processYearMonth;
	}

	// optimize the 'local_target_url_html' table on all servers in the cluster
	private void optimizeTargetUrlHtml() throws Exception {
		FormatUtils.getInstance().logMemoryUsage("optimizeTargetUrlHtml() begins.");
		long startTimestamp = System.currentTimeMillis();
		if (processYearMonthSet != null && processYearMonthSet.size() > 0) {
			for (Integer processYearMonth : processYearMonthSet) {
				FormatUtils.getInstance().logMemoryUsage("optimizeTargetUrlHtml() optimizing partition=" + processYearMonth);
				LocalTargetUrlHtmlClickHouseDAO.getInstance().optimizeOnePartition(processYearMonth, LOCAL_TARGET_URL_HTML_TABLE_NAME);
			}
		} else {
			FormatUtils.getInstance().logMemoryUsage("optimizeTargetUrlHtml() processYearMonthSet is empty.");
		}
		FormatUtils.getInstance().logMemoryUsage("optimizeTargetUrlHtml() ends. total elapsed (s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}
}
