package com.actonia.process;

import com.actonia.dao.HtmlBigDataClickHouseDAO;
import com.actonia.dao.TargetUrlHtmlClickHouseDAO;
import com.actonia.entity.HtmlBigData;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.NewHtmlClickHouseEntity;
import com.actonia.utils.CrawlerUtils;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.springframework.util.StopWatch;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.*;
import java.util.stream.Collectors;


@Log4j2
public class TransferHtml {

	static final ConcurrentLinkedQueue<HtmlBigData> htmlBigDataQueue = new ConcurrentLinkedQueue<>();
	static ExecutorService executorService = Executors.newFixedThreadPool(4);
	static ExecutorService partitionExecutor = Executors.newFixedThreadPool(3);
	static List<Future<?>> futureList = new ArrayList<>();
	private final TargetUrlHtmlClickHouseDAO targetUrlHtmlClickHouseDAO;
	private final HtmlBigDataClickHouseDAO htmlBigDataClickHouseDAO;
	private final LocalDate startDate;
	private final LocalDate endDate;
	private final ConcurrentLinkedQueue<NewHtmlClickHouseEntity> htmlClickHouseEntityConcurrentLinkedQueue = new ConcurrentLinkedQueue<>();
	private final List<String> fieldNames;
	private final int mod = 32;
	private final int batchCreationSize = 20000;


	public TransferHtml(LocalDate startDate, LocalDate endDate) {
		this.startDate = startDate;
		this.endDate = endDate;
		try {
			targetUrlHtmlClickHouseDAO = TargetUrlHtmlClickHouseDAO.getInstance();
			htmlBigDataClickHouseDAO = HtmlBigDataClickHouseDAO.getInstance();
			fieldNames = CrawlerUtils.getInstance().getTargetUrlHtmlTableAllFieldNames();
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	public static void main(String[] args) {
		final LocalDate startDate = args.length > 0 ? LocalDate.parse(args[0]) : LocalDate.now();
		final LocalDate endDate = args.length > 1 ? LocalDate.parse(args[1]) : LocalDate.now();
		final TransferHtml transferHtml = new TransferHtml(startDate, endDate);
		LocalDate trackDate = transferHtml.endDate;
		StopWatch stopWatch = new StopWatch("transfer");
		while (!trackDate.isBefore(transferHtml.startDate)) {
			final String trackDateStr = trackDate.format(DateTimeFormatter.ISO_LOCAL_DATE);
			stopWatch.start("transfer: " + trackDateStr);
			transferHtml.transfer(trackDateStr);
			stopWatch.stop();
			log.info(stopWatch.prettyPrint());
			trackDate = trackDate.minusDays(1);
		}
		futureList.forEach(future -> {
			try {
				future.get();
			} catch (InterruptedException | ExecutionException e) {
				throw new RuntimeException(e);
			}
		});
//        transferHtml.transfer(startDate.format(DateTimeFormatter.ISO_LOCAL_DATE));
		// wait for all tasks to complete, then shutdown the executorService and insert the rest
		if (!htmlBigDataQueue.isEmpty()) {
			final ArrayList<HtmlBigData> htmlBigDataList = new ArrayList<>(htmlBigDataQueue);
			executorService.submit(() -> transferHtml.saveBatch(htmlBigDataList));
		}
		partitionExecutor.shutdown();
		executorService.shutdown();
	}

	private void saveBatch(ArrayList<HtmlBigData> htmlBigDataList) {
		this.htmlBigDataClickHouseDAO.createBatch(htmlBigDataList);
	}

	@SneakyThrows
	private void transfer(String trackDate) {
		//        final String trackDate = startDate.format(DateTimeFormatter.ISO_LOCAL_DATE);
		for (int i = 0; i < mod; i++) {
			StopWatch stopWatch = new StopWatch("mod" + i);
			stopWatch.start("queryList");
//			final List<HtmlClickHouseEntity> htmlClickHouseEntities = targetUrlHtmlClickHouseDAO.queryListByTrackDateAndMod(trackDate, fieldNames, mod, i);
//			stopWatch.stop();
//			log.info("mod: {}, query list size: {}, cost time: {} s", i, htmlClickHouseEntities.size(), stopWatch.getTotalTimeSeconds());
//            htmlClickHouseEntities.parallelStream().map(NewHtmlClickHouseEntity::createFromHtmlClickHouseEntity).forEach(htmlClickHouseEntityConcurrentLinkedQueue::add);
//			int finalI = i;
//			partitionExecutor.submit(() -> transferMod(htmlClickHouseEntities, stopWatch, finalI, trackDate));
		}
		log.info("all mod tasks submitted, bigDataQueue size: {}", htmlBigDataQueue.size());


	}

	private void transferMod(List<HtmlClickHouseEntity> htmlClickHouseEntities, StopWatch stopWatch, int i, String trackDate) {
		// batch insert async
		stopWatch.start("createNewHtmlClickHouseEntityList");
		final Collection<HtmlClickHouseEntity> distinct = htmlClickHouseEntities.parallelStream()
				.collect(Collectors.toMap(HtmlClickHouseEntity::getUrlMurmurHash, htmlClickHouseEntity -> htmlClickHouseEntity, (old, newOne) -> old)).values();
		final List<NewHtmlClickHouseEntity> newHtmlClickHouseEntities = distinct.parallelStream()
				.map(NewHtmlClickHouseEntity::createFromHtmlClickHouseEntity)
				.collect(Collectors.toList());
		stopWatch.stop();
		stopWatch.start("batchInsert");
		final Integer bigDataSize = newHtmlClickHouseEntities.parallelStream()
				.reduce(0, (a, b) -> a + b.getHtmlBigDataList().size(), Integer::sum);
		log.info("mod:[{}] bigData size: {}", i, bigDataSize);
		newHtmlClickHouseEntities
				.forEach(fromHtmlClickHouseEntity -> {
					htmlBigDataQueue.addAll(fromHtmlClickHouseEntity.getHtmlBigDataList());
					synchronized (htmlBigDataQueue) {
						if (htmlBigDataQueue.size() >= batchCreationSize) {
							log.info("mod:[{}] batch insert bigData size: {}", i, htmlBigDataQueue.size());
							final Future<?> batchInsertBigData = executorService.submit(() -> startSaveBatch(i));
							futureList.add(batchInsertBigData);
							try {
								Thread.sleep(200);
							} catch (InterruptedException e) {
								throw new RuntimeException(e);
							}
						}
					}
				});
		stopWatch.stop();
	}

	private void startSaveBatch(int i) {
		long start = System.currentTimeMillis();
		List<HtmlBigData> htmlBigDataList = new ArrayList<>(batchCreationSize);
		for (int j = 0; j < batchCreationSize; j++) {
			final HtmlBigData htmlBigData = htmlBigDataQueue.poll();
			if (htmlBigData != null) {
				htmlBigDataList.add(htmlBigData);
			}
		}
		try {
			htmlBigDataClickHouseDAO.createBatch(htmlBigDataList);
			long end = System.currentTimeMillis();
			log.info("mod:[{}] batch insert bigData size: {}, cost time: {} s", i, htmlBigDataList.size(), (end - start) / 1000);
		} catch (Exception e) {
			log.error(e.getMessage());
		}

	}

}
