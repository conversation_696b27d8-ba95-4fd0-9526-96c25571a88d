package com.actonia.process;

import java.util.Calendar;
import java.util.Date;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.dao.TargetUrlHtmlTransferClickHouseDAO;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.TransferUtils;

public class TransferTargetUrlHtml {
	private TargetUrlHtmlTransferClickHouseDAO sourceTargetUrlHtmlTransferClickHouseDAO;
	private TargetUrlHtmlTransferClickHouseDAO destinationTargetUrlHtmlTransferClickHouseDAO;
	private int totalDaysProcessed = 0;
	private String sourceDatabaseServerIpAddress;
	private String sourceTableName;
	private String destinationDatabaseServerIpAddress;
	private String destinationTableName;

	public TransferTargetUrlHtml() {
		TransferUtils.getInstance();
	}

	public static void main(String[] args) throws Exception {
		new TransferTargetUrlHtml().process(args);
	}

	private void process(String[] args) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("process() begins.");
		String startTrackDateOverrideString = null;
		String endTrackDateOverrideString = null;
		Date startTrackDateOverride = null;
		Date endTrackDateOverride = null;

		if (args == null || args.length == 0) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters are required.");
			return;
		}

		// runtime parameter 1 (required): source database server IP address
		if (args.length >= 1) {
			sourceDatabaseServerIpAddress = args[0];
		}
		if (StringUtils.isBlank(sourceDatabaseServerIpAddress)) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 1: source database server IP address is required.");
			return;
		}
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 1: source database server IP address=" + sourceDatabaseServerIpAddress);

		// runtime parameter 2 (required): source table name
		if (args.length >= 2) {
			sourceTableName = args[1];
		}
		if (StringUtils.isBlank(sourceTableName)) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 2: source table name is required.");
			return;
		}
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 2: source table name=" + sourceTableName);

		// runtime parameter 3 (required): destination database server IP address
		if (args.length >= 3) {
			destinationDatabaseServerIpAddress = args[2];
		}
		if (StringUtils.isBlank(destinationDatabaseServerIpAddress)) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 3: destination database server IP address is required.");
			return;
		}
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 3: destination database server IP address=" + destinationDatabaseServerIpAddress);

		// runtime parameter 4 (required): destination table name
		if (args.length >= 4) {
			destinationTableName = args[3];
		}
		if (StringUtils.isBlank(destinationTableName)) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 4: destination table name is required.");
			return;
		}
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 4: destination table name=" + destinationTableName);

		// runtime parameter 5 (optional): start track date override (yyyy-mm-dd)
		if (args.length >= 5) {
			startTrackDateOverrideString = args[4];
		}
		if (StringUtils.isNotBlank(startTrackDateOverrideString)) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 5: startTrackDateOverrideString=" + startTrackDateOverrideString);
			startTrackDateOverride = DateUtils.parseDate(startTrackDateOverrideString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
		}

		// runtime parameter 6 (optional): end track date override (yyyy-mm-dd)
		if (args.length >= 6) {
			endTrackDateOverrideString = args[5];
		}
		if (StringUtils.isNotBlank(endTrackDateOverrideString)) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 6: endTrackDateOverrideString=" + endTrackDateOverrideString);
			endTrackDateOverride = DateUtils.parseDate(endTrackDateOverrideString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
		}

		sourceTargetUrlHtmlTransferClickHouseDAO = TransferUtils.getInstance().getTargetUrlHtmlTransferClickHouseDAO(sourceDatabaseServerIpAddress);

		if (sourceTargetUrlHtmlTransferClickHouseDAO == null) {
			FormatUtils.getInstance()
					.logMemoryUsage("process() sourceTargetUrlHtmlTransferClickHouseDAO cannot be initiated using IP address=" + sourceDatabaseServerIpAddress);
			return;
		}

		destinationTargetUrlHtmlTransferClickHouseDAO = TransferUtils.getInstance().getTargetUrlHtmlTransferClickHouseDAO(destinationDatabaseServerIpAddress);

		if (destinationTargetUrlHtmlTransferClickHouseDAO == null) {
			FormatUtils.getInstance().logMemoryUsage(
					"process() destinationTargetUrlHtmlTransferClickHouseDAO cannot be initiated using IP address=" + destinationDatabaseServerIpAddress);
			return;
		}

		transferAll(startTrackDateOverride, endTrackDateOverride);

		FormatUtils.getInstance()
				.logMemoryUsage("process() ends. totalDaysProcessed=" + totalDaysProcessed + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private void transferAll(Date startTrackDateOverride, Date endTrackDateOverride) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance()
				.logMemoryUsage("transferAll() begins. startTrackDateOverride=" + startTrackDateOverride + ",endTrackDateOverride=" + endTrackDateOverride);
		Date testDate = null;
		Date startTrackDate = null;
		Date endTrackDate = null;
		if (startTrackDateOverride != null && endTrackDateOverride != null) {
			startTrackDate = startTrackDateOverride;
			endTrackDate = endTrackDateOverride;
		} else {
			testDate = sourceTargetUrlHtmlTransferClickHouseDAO.getEarliestTrackDate(sourceTableName);
			startTrackDate = DateUtils.truncate(testDate, Calendar.DAY_OF_MONTH);
			testDate = sourceTargetUrlHtmlTransferClickHouseDAO.getLatestTrackDate(sourceTableName);
			//endTrackDate = DateUtils.truncate(testDate, Calendar.DAY_OF_MONTH);
			endTrackDate = DateUtils.addDays(testDate, -1);
		}
		FormatUtils.getInstance().logMemoryUsage("transferAll() startTrackDate=" + startTrackDate);
		FormatUtils.getInstance().logMemoryUsage("transferAll() endTrackDate=" + endTrackDate);

		Date currentTrackDate = startTrackDate;
		//Date testEndTrackDate = null;
		while (!currentTrackDate.after(endTrackDate)) {

			// transfer current track date data from source database to destination database
			transferOneDay(currentTrackDate);

			//testDate = sourceTargetUrlHtmlTransferClickHouseDAO.getLatestTrackDate(sourceTableName);
			//testEndTrackDate = DateUtils.truncate(testDate, Calendar.DAY_OF_MONTH);
			//if (testEndTrackDate.after(endTrackDate)) {
			//	endTrackDate = testEndTrackDate;
			//	FormatUtils.getInstance().logMemoryUsage("transferAll() endTrackDate=" + endTrackDate);
			//}

			// calculate next track date one day later
			currentTrackDate = DateUtils.addDays(currentTrackDate, +1);
			totalDaysProcessed++;
		}

		FormatUtils.getInstance().logMemoryUsage("transferAll() ends. startTrackDateOverride=" + startTrackDateOverride + ",endTrackDateOverride="
				+ endTrackDateOverride + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private void transferOneDay(Date trackDate) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String trackDateString = DateFormatUtils.format(trackDate, IConstants.DATE_FORMAT_YYYY_MM_DD);
		destinationTargetUrlHtmlTransferClickHouseDAO.transferData(sourceDatabaseServerIpAddress, sourceTableName, destinationTableName, trackDateString);

		// verify record counts on both source and destination server
		int sourceRecordCount = sourceTargetUrlHtmlTransferClickHouseDAO.getRecordCount(trackDate, sourceTableName);
		int destinationRecordCount = destinationTargetUrlHtmlTransferClickHouseDAO.getRecordCount(trackDate, destinationTableName);
		if (sourceRecordCount == destinationRecordCount) {
			FormatUtils.getInstance().logMemoryUsage("transferOneDay() trackDate=" + trackDate + ",record counts matched=" + sourceRecordCount);
		} else {
			FormatUtils.getInstance().logMemoryUsage("transferOneDay() trackDate=" + trackDate + ",record counts mismatched: sourceRecordCount=" + sourceRecordCount
					+ ",destinationRecordCount=" + destinationRecordCount);
		}
	}
}
