package com.actonia.process;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.dao.BackupAuditTrailDAO;
import com.actonia.dao.TargetUrlCustomDataBackupClickHouseDAO;
import com.actonia.entity.BackupAuditTrailEntity;
import com.actonia.utils.BackupUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.SpringBeanFactory;

public class TargetUrlCustomDataRestoreFromS3 {

	private TargetUrlCustomDataBackupClickHouseDAO destinationTargetUrlCustomDataBackupClickHouseDAO;
	private String s3FolderURI;
	private String destinationTableName;
	private BackupAuditTrailDAO backupAuditTrailDAO;
	private static final String S3_URI_PREFIX = "https://s3.us-east-1.amazonaws.com/";

	public TargetUrlCustomDataRestoreFromS3() {
		this.backupAuditTrailDAO = SpringBeanFactory.getBean("backupAuditTrailDAO");
	}

	public static void main(String[] args) throws Exception {
		new TargetUrlCustomDataRestoreFromS3().process(args);
	}

	private void process(String[] args) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("process() begins.");

		String destinationDatabaseServerIpAddress = null;
		String auditTrailTableName = null;
		String s3BucketName = null;
		String s3PartialPrefix = null;
		Date startDailyDataCreationDate = null;
		Date endDailyDataCreationDate = null;
		Date currentDailyDataCreationDate = null;
		BackupAuditTrailEntity backupAuditTrailEntity = null;
		String s3FullPrefix = null;
		int totalDaysRestored = 0;

		if (args == null || args.length == 0) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters are required.");
			return;
		}

		// runtime paramter 1: destination database server IP address
		if (args.length >= 1) {
			destinationDatabaseServerIpAddress = args[0];
		}
		if (StringUtils.isBlank(destinationDatabaseServerIpAddress)) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 1: destination database server IP address is required.");
			return;
		}
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 1: destination database server IP address=" + destinationDatabaseServerIpAddress);

		// runtime paramter 2: destination table name
		if (args.length >= 2) {
			destinationTableName = args[1];
		}
		if (StringUtils.isBlank(destinationTableName)) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 2: destination table name is required.");
			return;
		}
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 2: destination table name=" + destinationTableName);

		// runtime paramter 3: audit trail table name
		if (args.length >= 3) {
			auditTrailTableName = args[2];
		}
		if (StringUtils.isBlank(auditTrailTableName)) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 3: audit trail table name is required.");
			return;
		}
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 3: audit trail table name=" + auditTrailTableName);

		// runtime paramter 4: S3 bucket name
		if (args.length >= 4) {
			s3BucketName = args[3];
		}
		if (StringUtils.isBlank(s3BucketName)) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 4: S3 bucket name is required.");
			return;
		}
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 4: S3 bucket name=" + s3BucketName);

		// runtime paramter 5: S3 prefix (partial)
		if (args.length >= 5) {
			s3PartialPrefix = args[4];
		}
		if (StringUtils.isBlank(s3PartialPrefix)) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 5: S3 prefix (partial) is required.");
			return;
		}
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 5: S3 prefix (partial)=" + s3PartialPrefix);

		destinationTargetUrlCustomDataBackupClickHouseDAO = BackupUtils.getInstance().getTargetUrlCustomDataBackupClickHouseDAO(destinationDatabaseServerIpAddress);
		if (destinationTargetUrlCustomDataBackupClickHouseDAO == null) {
			FormatUtils.getInstance().logMemoryUsage(
					"process() destinationTargetUrlCustomDataBackupClickHouseDAO cannot be initiated using IP address=" + destinationDatabaseServerIpAddress);
			return;
		}

		// retrieve the last 'backlink_backup_audit_trail' for source table
		List<BackupAuditTrailEntity> backupAuditTrailEntityList = backupAuditTrailDAO.getList(auditTrailTableName, s3BucketName, s3PartialPrefix);
		if (backupAuditTrailEntityList != null && backupAuditTrailEntityList.size() > 0) {

			// use the last backup to restore
			backupAuditTrailEntity = backupAuditTrailEntityList.get(backupAuditTrailEntityList.size() - 1);
			startDailyDataCreationDate = DateUtils.parseDate(backupAuditTrailEntity.getStartDate(), new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
			endDailyDataCreationDate = DateUtils.parseDate(backupAuditTrailEntity.getEndDate(), new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
			s3FullPrefix = s3PartialPrefix + backupAuditTrailEntity.getBackupDate();
			s3FolderURI = S3_URI_PREFIX + s3BucketName + IConstants.SLASH + s3FullPrefix + IConstants.SLASH;
			FormatUtils.getInstance().logMemoryUsage("process() s3FolderURI=" + s3FolderURI + ",startDailyDataCreationDate=" + startDailyDataCreationDate
					+ ",endDailyDataCreationDate=" + endDailyDataCreationDate);

			currentDailyDataCreationDate = startDailyDataCreationDate;
			while (!currentDailyDataCreationDate.after(endDailyDataCreationDate)) {

				// backup current track date data from destination database to Amazon S3
				restore(currentDailyDataCreationDate);

				// calculate next track date one day later
				currentDailyDataCreationDate = DateUtils.addDays(currentDailyDataCreationDate, +1);
				totalDaysRestored++;
			}

		} else {
			FormatUtils.getInstance().logMemoryUsage("process() backupAuditTrailEntityList is empty.");
		}

		FormatUtils.getInstance()
				.logMemoryUsage("process() ends. totalDaysRestored=" + totalDaysRestored + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private void restore(Date dailyDataCreationDate) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String dailyDataCreationDateString = DateFormatUtils.format(dailyDataCreationDate, IConstants.DATE_FORMAT_YYYY_MM_DD);
		FormatUtils.getInstance()
				.logMemoryUsage("restore() begins. dailyDataCreationDate=" + dailyDataCreationDate + ",dailyDataCreationDateString=" + dailyDataCreationDateString);

		// https://s3.us-east-1.amazonaws.com/seoclarity-cdb/ck-cdb-123/backlink/local_backlink_data/2020-05-08.bin.zstd
		String s3ObjectURI = s3FolderURI + dailyDataCreationDateString + IConstants.BACKUP_FILE_EXT;
		destinationTargetUrlCustomDataBackupClickHouseDAO.restoreFromS3(s3ObjectURI, destinationTableName);
		FormatUtils.getInstance().logMemoryUsage(
				"restore() ends. dailyDataCreationDate=" + dailyDataCreationDate + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}
}
