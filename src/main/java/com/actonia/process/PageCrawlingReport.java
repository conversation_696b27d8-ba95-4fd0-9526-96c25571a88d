package com.actonia.process;

import com.actonia.dao.OwnDomainEntityDAO;
import com.actonia.dao.TargetUrlToCrawlClickHouseDAO;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.entity.TargetUrlToCrawl;
import com.actonia.utils.SpringBeanFactory;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.time.DateUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Log4j2
public class PageCrawlingReport {
    private final TargetUrlToCrawlClickHouseDAO targetUrlToCrawlClickHouseDAO;
    private final OwnDomainEntityDAO ownDomainEntityDAO;

    public PageCrawlingReport() {
        try {
            targetUrlToCrawlClickHouseDAO = TargetUrlToCrawlClickHouseDAO.getInstance();
            log.info("targetUrlToCrawlClickHouseDAO123: {}", targetUrlToCrawlClickHouseDAO);
        } catch (Exception e) {
            log.error("Error creating TargetUrlToCrawlClickHouseDAO instance, message: {}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        log.info("ownDomainEntityDAO: {}", ownDomainEntityDAO);
    }

    public static void main(String[] args) {
        PageCrawlingReport pageCrawlingReport = new PageCrawlingReport();
        pageCrawlingReport.process();
    }

    private void process() {
        log.info("Processing all domains");
        final List<OwnDomainEntity> domainsWithTargetUrls = ownDomainEntityDAO.getDomainsWithTargetUrls();
        log.info("domainsWithTargetUrls: {}", domainsWithTargetUrls.size());
        domainsWithTargetUrls.forEach(this::processEachDomain);
    }

    private void processEachDomain(OwnDomainEntity domain) {
        ReportModel reportModel = new ReportModel();
        int ownDomainId = domain.getId();
        final String urlCrawlParameters = domain.getUrlCrawlParameters();
        log.info("Processing domain: {}, urlCrawlParameters: {}", ownDomainId, urlCrawlParameters);
        reportModel.setOwnDomainId(ownDomainId);
        reportModel.setUrlCrawlParameters(urlCrawlParameters);
        final List<TargetUrlToCrawl> targetUrlToCrawls = null;
//        targetUrlToCrawls = this.targetUrlToCrawlClickHouseDAO.queryLast10TrackDateByDomainId(ownDomainId);
        // revert the list to get the latest date first
        targetUrlToCrawls.sort(Comparator.comparing(TargetUrlToCrawl::getTrackDate));
        List<TargetUrlToCrawlModel> targetUrlToCrawlModels = new ArrayList<>(10);
        for (int i = 1; i < targetUrlToCrawls.size(); i++) {
            final TargetUrlToCrawl targetUrlToCrawl = targetUrlToCrawls.get(i - 1);
            final TargetUrlToCrawl next = targetUrlToCrawls.get(i);
            // get the gap between the every two adjacent dates
            final long gap = (next.getTrackDate().getTime() - targetUrlToCrawl.getTrackDate().getTime()) / DateUtils.MILLIS_PER_DAY;
            TargetUrlToCrawlModel targetUrlToCrawlModel = new TargetUrlToCrawlModel();
            targetUrlToCrawlModel.setTrackDate(targetUrlToCrawl.getTrackDate());
            targetUrlToCrawlModel.setGap(gap);
            targetUrlToCrawlModel.setCount(targetUrlToCrawl.getCount());
            targetUrlToCrawlModels.add(targetUrlToCrawlModel);
        }
        // Calculate max, min, and average gap
        List<Long> gaps = targetUrlToCrawlModels.stream().map(TargetUrlToCrawlModel::getGap).collect(Collectors.toList());
        long maxGap = gaps.stream().max(Long::compare).orElse(0L);
        long minGap = gaps.stream().min(Long::compare).orElse(0L);
        double averageGap = gaps.stream().mapToLong(Long::longValue).average().orElse(0.0);
        log.info("OID: {}, maxGap: {}, minGap: {}, averageGap: {}", ownDomainId, maxGap, minGap, averageGap);
        reportModel.setMaxGap(maxGap);
        reportModel.setMinGap(minGap);
        reportModel.setAverageGap(new BigDecimal(averageGap).setScale(2, RoundingMode.HALF_UP));
        // calculate the targetUrlCount
        final List<Integer> counts = targetUrlToCrawlModels.stream().map(TargetUrlToCrawlModel::getCount).collect(Collectors.toList());
        Integer max = counts.stream().max(Comparator.comparingInt(o -> o)).orElse(0);
        Integer min = counts.stream().min(Comparator.comparingInt(o -> o)).orElse(0);
        int average = Math.toIntExact(Math.round(counts.stream().mapToInt(Integer::intValue).average().orElse(0.0)));
        log.info("OID: {}, max: {}, min: {}, average: {}", ownDomainId, max, min, average);
        reportModel.setMaxUrlCount(max);
        reportModel.setMinUrlCount(min);
        reportModel.setAverageUrlCount(new BigDecimal(average).setScale(2, RoundingMode.HALF_UP));
        log.info("ReportModel: {}", reportModel);
        // convert the reportModel to a csv format: domain id - count of managed urls - days of crawl all pages - crawl settingsI(json)
        log.info("csv: {},{},{},{},{},{},{},{}", reportModel.getOwnDomainId(), reportModel.getMaxUrlCount(), reportModel.getMinUrlCount(), reportModel.getAverageUrlCount(), reportModel.getMaxGap(), reportModel.getMinGap(), reportModel.getAverageGap(), reportModel.getUrlCrawlParameters());
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    static class TargetUrlToCrawlModel extends TargetUrlToCrawl {
        private Date trackDate;
        private Long gap;
    }

    @Data
    static class ReportModel {
        private int ownDomainId;
        private long maxGap;
        private long minGap;
        private BigDecimal averageGap;
        private int maxUrlCount;
        private int minUrlCount;
        private BigDecimal averageUrlCount;
        private String urlCrawlParameters;
    }
}
