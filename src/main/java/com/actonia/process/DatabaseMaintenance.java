package com.actonia.process;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.lang.StringUtils;

import com.actonia.IConstants;
import com.actonia.utils.ClickHouseConfigurations;
import com.actonia.utils.FormatUtils;

import ru.yandex.clickhouse.ClickHouseDataSource;
import ru.yandex.clickhouse.settings.ClickHouseProperties;

public class DatabaseMaintenance {

	private boolean isDebug = false;
	private List<String> databaseHostnameList;
	private String databasePort = null;
	private String databaseName = null;
	private List<Connection> connectionList;
	private int batchCreationSize;
	private String databaseUser = null;
	private String databasePassword = null;
	private List<String> connectionUrlList = null;
	private int connectionTimeoutInMilliseconds;
	private int maximumRetryCounts;
	private int retryWaitMilliseconds;

	public DatabaseMaintenance() throws SQLException {
		ClickHouseDataSource clickHouseDataSource = null;
		Connection connection = null;
		String clickHouseDatabaseHostnames = ClickHouseConfigurations
				.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_HOSTNAMES);
		String[] databaseHostnameArray = clickHouseDatabaseHostnames.split(IConstants.COMMA);
		this.databaseHostnameList = Arrays.asList(databaseHostnameArray);
		this.databasePort = ClickHouseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_PORT);
		this.databaseName = ClickHouseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_NAME);
		this.databaseUser = ClickHouseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_USER);
		this.databasePassword = ClickHouseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_PASSWORD);
		this.batchCreationSize = ClickHouseConfigurations.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_BATCH_CREATION_SIZE,
				100);
		this.connectionTimeoutInMilliseconds = ClickHouseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_CONNECTION_TIMEOUT_MILLISECONDS, 88888);
		this.maximumRetryCounts = ClickHouseConfigurations.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_MAX_RETRY_COUNTS, 8);
		this.retryWaitMilliseconds = ClickHouseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_RETRY_WAIT_MILLISECONDS, 1000);

		FormatUtils.getInstance().logMemoryUsage("databaseHostnameList=" + databaseHostnameList.toString() + ",databasePort=" + databasePort + ",databaseName="
				+ databaseName + ",batchCreationSize=" + batchCreationSize + ",databaseUser=" + databaseUser + ",databasePassword=" + databasePassword
				+ ",connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ",maximumRetryCounts=" + maximumRetryCounts
				+ ",retryWaitMilliseconds=" + retryWaitMilliseconds);
		ClickHouseProperties clickHouseProperties = new ClickHouseProperties();
		clickHouseProperties.setDecompress(true);
		clickHouseProperties.setConnectionTimeout(connectionTimeoutInMilliseconds);
		clickHouseProperties.setSocketTimeout(connectionTimeoutInMilliseconds);
		connectionList = new ArrayList<Connection>();
		String connectionUrl = null;
		connectionUrlList = new ArrayList<String>();

		for (String databaseHostname : databaseHostnameList) {
			if (StringUtils.isNotBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort
						+ IConstants.FORWARD_SLASH + databaseName + "?user=" + databaseUser + "&password=" + databasePassword;
			} else {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort
						+ IConstants.FORWARD_SLASH + databaseName;
			}
			connectionUrlList.add(connectionUrl);
			FormatUtils.getInstance().logMemoryUsage("connectionUrl=" + connectionUrl);
			clickHouseDataSource = new ClickHouseDataSource(connectionUrl, clickHouseProperties);
			connection = clickHouseDataSource.getConnection();
			connectionList.add(connection);
		}
	}

	public static void main(String[] args) throws Exception {
		//new DatabaseMaintenance().redefineSharedCountslTable();
		//new DatabaseMaintenance().redefineUrlToDomainIdsCrossReferenceTable();
	}

	private String getSharedCountsTableName() {
		return "social_detail";
	}

	private String getUrlToDomainsCrossReferenceTableName() {
		return "url_to_domains_xref";
	}

	private void redefineSharedCountslTable() throws SQLException {

		FormatUtils.getInstance().logMemoryUsage("redefineSharedCountslTable() begins.");

		StringBuilder stringBuilder = null;
		String sqlString = null;
		Connection connection = null;
		String connectionUrl = null;
		for (int i = 0; i < connectionList.size(); i++) {
			connection = connectionList.get(i);
			if (isDebug == true) {
				connectionUrl = connectionUrlList.get(i);
				FormatUtils.getInstance().logMemoryUsage("redefineSharedCountslTable() connectionUrl=" + connectionUrl);
			}

			// 
//			stringBuilder = new StringBuilder();
//			stringBuilder.append("DROP TABLE IF EXISTS " + getSharedCountsTableName());
//			sqlString = stringBuilder.toString();
//			FormatUtils.getInstance().logMemoryUsage("redefineSharedCountslTable() sqlString=" + sqlString);
//			connection.createStatement().execute(sqlString);

			//
			stringBuilder = new StringBuilder();
			stringBuilder.append(" CREATE TABLE IF NOT EXISTS " + getSharedCountsTableName());
			stringBuilder.append(" (");
			stringBuilder.append("  track_date Date,");
			stringBuilder.append("  domain String,");
			stringBuilder.append("  root_domain String,");
			stringBuilder.append("  url String,");
			stringBuilder.append("  protocol Int32,");
			stringBuilder.append("  uri String,");
			stringBuilder.append("  folder1 String,");
			stringBuilder.append("  folder2 String,");
			stringBuilder.append("  url_hash UInt64 DEFAULT URLHash(url),");
			stringBuilder.append("  uri_hash UInt64 DEFAULT URLHash(uri),");
			stringBuilder.append("  folder1_hash UInt64 DEFAULT URLHash(folder1),");
			stringBuilder.append("  folder2_hash UInt64 DEFAULT URLHash(folder2),");
			stringBuilder.append("  facebook_shared_count Int32,");
			stringBuilder.append("  linkedin_shared_count Int32,");
			stringBuilder.append("  twitter_shared_count Int32,");
			stringBuilder.append("  google_plus_shared_count Int32,");
			stringBuilder.append("  pinterest_shared_count Int32,");
			stringBuilder.append("  stumbleupon_shared_count Int32,");
			stringBuilder.append("  sign Int8,");
			stringBuilder.append("  integer_column_1 Int32,");
			stringBuilder.append("  integer_column_2 Int32,");
			stringBuilder.append("  integer_column_3 Int32,");
			stringBuilder.append("  text_column_1 String,");
			stringBuilder.append("  text_column_2 String,");
			stringBuilder.append("  text_column_3 String,");
			stringBuilder.append("  array_key Array(String),");
			stringBuilder.append("  array_value Array(String)");
			stringBuilder.append(" )");
			stringBuilder.append(" ENGINE = CollapsingMergeTree(track_date, (root_domain, domain, track_date, url_hash), 8192, sign);");
			sqlString = stringBuilder.toString();
			FormatUtils.getInstance().logMemoryUsage("redefineSharedCountslTable() sqlString=" + sqlString);
			connection.createStatement().execute(sqlString);

			connection.close();

		}

		FormatUtils.getInstance().logMemoryUsage("redefineSharedCountslTable() ends.");

	}

	private void redefineUrlToDomainIdsCrossReferenceTable() throws SQLException {

		FormatUtils.getInstance().logMemoryUsage("redefineUrlToDomainIdsCrossReferenceTable() begins.");

		StringBuilder stringBuilder = null;
		String sqlString = null;
		Connection connection = null;
		String connectionUrl = null;
		for (int i = 0; i < connectionList.size(); i++) {
			connection = connectionList.get(i);
			if (isDebug == true) {
				connectionUrl = connectionUrlList.get(i);
				FormatUtils.getInstance().logMemoryUsage("redefineUrlToDomainIdsCrossReferenceTable() connectionUrl=" + connectionUrl);
			}

			// 
			stringBuilder = new StringBuilder();
			stringBuilder.append("DROP TABLE IF EXISTS " + getUrlToDomainsCrossReferenceTableName());
			sqlString = stringBuilder.toString();
			FormatUtils.getInstance().logMemoryUsage("redefineUrlToDomainIdsCrossReferenceTable() sqlString=" + sqlString);
			connection.createStatement().execute(sqlString);

			//
			stringBuilder = new StringBuilder();
			stringBuilder.append(" CREATE TABLE IF NOT EXISTS " + getUrlToDomainsCrossReferenceTableName());
			stringBuilder.append(" (");
			stringBuilder.append("  track_date Date,");
			stringBuilder.append("  url String,");
			stringBuilder.append("  url_hash UInt64 DEFAULT URLHash(url),");
			stringBuilder.append("  domain_ids Array(String),");
			stringBuilder.append("  sign Int8");
			stringBuilder.append(" )");
			stringBuilder.append(" ENGINE = CollapsingMergeTree(track_date, (track_date, url_hash), 8192, sign);");
			sqlString = stringBuilder.toString();
			FormatUtils.getInstance().logMemoryUsage("redefineUrlToDomainIdsCrossReferenceTable() sqlString=" + sqlString);
			connection.createStatement().execute(sqlString);

			connection.close();

		}
		FormatUtils.getInstance().logMemoryUsage("redefineUrlToDomainIdsCrossReferenceTable() ends.");
	}

}
