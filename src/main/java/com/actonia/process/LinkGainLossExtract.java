package com.actonia.process;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;

import com.actonia.IConstants;
import com.actonia.dao.LinkGainLossClickHouseDAO;
import com.actonia.entity.LinkGainLossClickHouseEntity;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.PutMessageUtils;

public class LinkGainLossExtract {
	private static final String DELIMITER = "`";
	//private static final String DELIMITER = "\t";
	private static final String TABLE_NAME = null;

	public LinkGainLossExtract() {
	}

	public static void main(String[] args) {
		new LinkGainLossExtract().process(args);
	}

	private void process(String[] args) {
		String outputFilePathLocation = null;
		String domainIdsString = null;
		int[] domainIdArray = null;
		String[] tempStringArray = null;

		if (args != null && args.length > 0) {

			// runtime parameter 1: output file location path
			if (args != null && args.length >= 1) {
				outputFilePathLocation = args[0];
				FormatUtils.getInstance().logMemoryUsage("process() runtime parameter 1 outputFilePathLocation=" + outputFilePathLocation);
			}

			// runtime parameter 2: domain ID(s)
			if (args != null && args.length >= 2) {
				domainIdsString = args[1];
				if (StringUtils.isNotBlank(domainIdsString)) {
					tempStringArray = domainIdsString.split(IConstants.COMMA);
					domainIdArray = new int[tempStringArray.length];
					for (int i = 0; i < tempStringArray.length; i++) {
						domainIdArray[i] = Integer.parseInt(tempStringArray[i]);
					}
				}
				for (int i = 0; i < domainIdArray.length; i++) {
					FormatUtils.getInstance().logMemoryUsage("process() runtime parameter 2:domain ID=" + domainIdArray[i]);
				}
			}
		} else {
			FormatUtils.getInstance().logMemoryUsage("process() error--runtime parameters are required.");
			return;
		}

		try {
			extractLinkGainLoss(outputFilePathLocation, domainIdArray);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void extractLinkGainLoss(String outputFilePathLocation, int[] domainIdArray) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("extractLinkGainLoss() begins. outputFilePathLocation=" + outputFilePathLocation);

		int domainId = 0;
		List<String> outputLineList = new ArrayList<String>();
		List<String> testOutputLineList = null;
		List<LinkGainLossClickHouseEntity> linkGainLossClickHouseEntityList = null;
		int index = 0;

		if (domainIdArray == null || domainIdArray.length == 0) {
			linkGainLossClickHouseEntityList = LinkGainLossClickHouseDAO.getInstance().getUniqueDomainIds(TABLE_NAME);
			if (linkGainLossClickHouseEntityList != null && linkGainLossClickHouseEntityList.size() > 0) {
				domainIdArray = new int[linkGainLossClickHouseEntityList.size()];
				for (LinkGainLossClickHouseEntity linkGainLossClickHouseEntity : linkGainLossClickHouseEntityList) {
					domainIdArray[index++] = linkGainLossClickHouseEntity.getDomainId();
				}
			}
		}

		outputLineList.add(getHeader(DELIMITER));

		for (int i = 0; i < domainIdArray.length; i++) {
			domainId = domainIdArray[i];
			linkGainLossClickHouseEntityList = LinkGainLossClickHouseDAO.getInstance().getListByDomainId(domainId, TABLE_NAME);
			if (linkGainLossClickHouseEntityList != null && linkGainLossClickHouseEntityList.size() > 0) {
				FormatUtils.getInstance().logMemoryUsage("extractLinkGainLoss() processing domainId=" + domainId);
				testOutputLineList = PutMessageUtils.getInstance().getExtractOutputList(linkGainLossClickHouseEntityList, DELIMITER);
				outputLineList.addAll(testOutputLineList);
			} else {
				FormatUtils.getInstance().logMemoryUsage("extractLinkGainLoss() linkGainLossClickHouseEntityList is empty.");
			}
		}

		// create output
		File outputFile = new File(outputFilePathLocation);

		try {
			FileUtils.writeLines(outputFile, IConstants.UTF_8, outputLineList);
		} catch (Exception e) {
			e.printStackTrace();
		}

		FormatUtils.getInstance().logMemoryUsage("extractLinkGainLoss() ends.");
	}

	private String getHeader(String delimiter) {
		String headerLine = null;

		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(IConstants.DOMAIN_ID);
		stringBuilder.append(delimiter);
		stringBuilder.append(IConstants.GAIN_LOSS_DATE);
		stringBuilder.append(delimiter);
		stringBuilder.append(IConstants.GAIN_LOSS_FLAG);
		stringBuilder.append(delimiter);
		stringBuilder.append(IConstants.SOURCE_URL);
		stringBuilder.append(delimiter);
		stringBuilder.append(IConstants.TARGET_URL);
		stringBuilder.append(delimiter);
		stringBuilder.append(IConstants.ANCHOR_TEXT);
		stringBuilder.append(delimiter);
		stringBuilder.append(IConstants.LINK_TYPE);
		stringBuilder.append(delimiter);
		stringBuilder.append(IConstants.TARGET_URL_HTTP_STATUS_CODE);
		stringBuilder.append(delimiter);
		headerLine = stringBuilder.toString();
		return headerLine;
	}
}
