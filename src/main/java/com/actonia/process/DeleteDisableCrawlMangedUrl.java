package com.actonia.process;

import com.actonia.dao.OwnDomainSettingEntityDAO;
import com.actonia.dao.ResourceBatchDetailDAO;
import com.actonia.dao.ResourceBatchInfoDAO;
import com.actonia.dao.TargetUrlLogEntityDAO;
import com.actonia.entity.OwnDomainSettingEntity;
import com.actonia.entity.ResourceBatchDetailEntity;
import com.actonia.entity.ResourceBatchInfoEntity;
import com.actonia.entity.TargetUrlLogEntity;
import com.actonia.utils.Md5Util;
import com.actonia.utils.SpringBeanFactory;
import lombok.extern.log4j.Log4j2;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Log4j2
public class DeleteDisableCrawlMangedUrl {

	private final TargetUrlLogEntityDAO targetUrlLogEntityDAO;
	private final OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
	private final ResourceBatchInfoDAO resourceBatchInfoDAO;
	private final ResourceBatchDetailDAO resourceBatchDetailDAO;
	private final Set<Integer> domainIdSet;

	public DeleteDisableCrawlMangedUrl(Set<Integer> domainIdSet) {
		this.domainIdSet = domainIdSet;
		targetUrlLogEntityDAO = SpringBeanFactory.getBean("targetUrlLogEntityDAO");
		ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
		resourceBatchDetailDAO = SpringBeanFactory.getBean("resourceBatchDetailDAO");
		resourceBatchInfoDAO = SpringBeanFactory.getBean("resourceBatchInfoDAO");
	}

	public static void main(String[] args) {
		Set<Integer> domainIdSet = null;
		if (args != null && args.length > 0) {
			domainIdSet = Arrays.stream(args[0].split(","))
					.map(Integer::parseInt)
					.collect(Collectors.toSet());
			log.info("domainIdSet: {}", domainIdSet);
		}
		new DeleteDisableCrawlMangedUrl(domainIdSet).process();
	}

	private void process() {
		// 1. get delete_non2xx_urls_in_days for active domains
		final List<OwnDomainSettingEntity> activeDomains = ownDomainSettingEntityDAO.getDeleteNon2XxUrlsInDaysForActiveDomain();
		if (activeDomains == null || activeDomains.isEmpty()) {
			log.warn("no active domains");
			return;
		}
		log.info("Active domains fetched: {}", activeDomains.size());

		List<OwnDomainSettingEntity> deleteDomains = activeDomains;
		// filter active domains by domainIdSet
		if (domainIdSet != null && !domainIdSet.isEmpty()) {
			deleteDomains = activeDomains.stream()
					.filter(domain -> domainIdSet.contains(domain.getOwnDomainId()))
					.collect(Collectors.toList());
			log.info("Filtered domains by domainIdSet: {}", deleteDomains.size());
		}

		LocalDate today = LocalDate.now();
		for (OwnDomainSettingEntity domain : deleteDomains) {
			final Integer domainId = domain.getOwnDomainId();

			// 2. find all need delete urls from mysql table 't_target_url_log'
			final Integer deleteNon2xxUrlsInDays = domain.getDeleteNon2xxUrlsInDays();
			final int deleteNon2xxUrlsDate = Integer.parseInt(today.minusDays(deleteNon2xxUrlsInDays).format(DateTimeFormatter.BASIC_ISO_DATE));
			log.info("Domain ID: {}, delete_non2xx_urls_in_days: {}, delete_non2xx_urls_date: {}", domainId, deleteNon2xxUrlsInDays, deleteNon2xxUrlsDate);
			// find all need delete urls max(log_date) is not after deleteNon2xxUrlsDate
			final List<TargetUrlLogEntity> targetUrlLogEntities = targetUrlLogEntityDAO.getLogListByOffsetDays(domainId)
					.stream().filter(targetUrlLogEntity -> targetUrlLogEntity.getMaxLogDate() <= deleteNon2xxUrlsDate)
					.collect(Collectors.toList());
			if (targetUrlLogEntities.isEmpty()) {
				log.warn("No need delete managed urls for OID: {}", domainId);
				continue;
			}
			log.info("Urls to delete for domain ID {}: {}", domainId, targetUrlLogEntities.size());

			// 3. delete urls from mysql table 't_target_url_log'
			// 3.1 create resource batch info
			final ResourceBatchInfoEntity resourceBatchInfoEntity = createResourceBatchInfoEntity(domainId);
			final long infoId = this.resourceBatchInfoDAO.insert(resourceBatchInfoEntity);
			log.info("Created resource batch info ID: {}", infoId);

			// 3.2 create resource_batch_detail
			final List<ResourceBatchDetailEntity> resourceBatchDetailEntities = targetUrlLogEntities.stream()
					.map(targetUrlLogEntity -> createResourceBatchDetail(infoId, domainId, targetUrlLogEntity))
					.collect(Collectors.toList());
			this.resourceBatchDetailDAO.insertBatchIgnoreDup(resourceBatchDetailEntities);
			this.resourceBatchInfoDAO.updateStatusById(ResourceBatchInfoEntity.STATUS_CREATED, infoId);
			log.info("Inserted resource batch details for domain ID {}: {}", domainId, resourceBatchDetailEntities.size());
		}
	}

	private ResourceBatchDetailEntity createResourceBatchDetail(long infoId, Integer domainId, TargetUrlLogEntity targetUrlLogEntity) {
		final ResourceBatchDetailEntity resourceBatchDetailEntity = new ResourceBatchDetailEntity();
		resourceBatchDetailEntity.setInfoId(infoId);
		resourceBatchDetailEntity.setActionType(ResourceBatchInfoEntity.TYPE_DELETE);
		resourceBatchDetailEntity.setOwnDomainId(domainId);
		resourceBatchDetailEntity.setResourceId(targetUrlLogEntity.getTargetUrlId());
		resourceBatchDetailEntity.setResourceMain(targetUrlLogEntity.getUrl());
		resourceBatchDetailEntity.setResourceSubordinate(targetUrlLogEntity.getCustomUrlMurmur3Hash());
		resourceBatchDetailEntity.setResourceMd5(Md5Util.Md5(UUID.randomUUID().toString()));
		resourceBatchDetailEntity.setCreateDate(Integer.valueOf(LocalDateTime.now().format(DateTimeFormatter.BASIC_ISO_DATE)));
		resourceBatchDetailEntity.setStatus(ResourceBatchDetailEntity.STATUS_CREATED);
		return resourceBatchDetailEntity;
	}


	private static ResourceBatchInfoEntity createResourceBatchInfoEntity(int domainId) {
		final ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
		resourceBatchInfoEntity.setActionType(ResourceBatchInfoEntity.TYPE_DELETE);
		resourceBatchInfoEntity.setOwnDomainId(domainId);
		resourceBatchInfoEntity.setCreateDate(new Date());
		resourceBatchInfoEntity.setOperationType(ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_DELETE_TARGET_URL);
		resourceBatchInfoEntity.setUserId(214);
		resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_FINISH);
		return resourceBatchInfoEntity;
	}
}
