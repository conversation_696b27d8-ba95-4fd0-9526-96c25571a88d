package com.actonia.process;

import com.actonia.dao.OwnDomainEntityDAO;
import com.actonia.dao.PoliteCrawlStateLogDAO;
import com.actonia.dao.TargetUrlEntityDAO;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.entity.PoliteCrawlStateLog;
import com.actonia.utils.SpringBeanFactory;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;
import java.util.stream.Collectors;

/**
 * update disable_crawl in t_target_url by query polite_crawl_state_log
 * mvn exec:java -Dexec.mainClass=com.actonia.process.UpdateDisableCrawlUrl
 */
public class UpdateDisableCrawlUrl {

    private static final Logger log = LogManager.getLogger(UpdateDisableCrawlUrl.class);
    private final OwnDomainEntityDAO ownDomainEntityDAO;
    private final PoliteCrawlStateLogDAO politeCrawlStateLogDAO;
    private final TargetUrlEntityDAO targetUrlEntityDAO;
    private final int batchUpdateSize;

    public UpdateDisableCrawlUrl(int batchUpdateSize) {
        this.batchUpdateSize = batchUpdateSize;
        politeCrawlStateLogDAO = SpringBeanFactory.getBean("politeCrawlStateLogDAO");
        targetUrlEntityDAO = SpringBeanFactory.getBean("targetUrlEntityDAO");
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
    }

    public static void main(String[] args) {
        // default batchUpdateSize is 500
        int batchUpdateSize = 500;
        if (args.length > 0) {
            // override batchUpdateSize from args
            batchUpdateSize = Integer.parseInt(args[1], 10);
        }
        // override processSize from args
        new UpdateDisableCrawlUrl(batchUpdateSize).run();
    }

    private void run() {
        final List<OwnDomainEntity> domainsWithTargetUrl = this.ownDomainEntityDAO.findDomainsWithTargetUrl();
        for (OwnDomainEntity ownDomainEntity : domainsWithTargetUrl) {
            final Integer domainId = ownDomainEntity.getId();
            final List<PoliteCrawlStateLog> politeCrawlStateLogs = this.politeCrawlStateLogDAO.findListByOwnDomainId(domainId);
            if (politeCrawlStateLogs.isEmpty()) {
                continue;
            }
            this.batchUpdateUrlDisableCrawl(domainId, politeCrawlStateLogs);
        }
    }


    /**
     * Processes the given list of crawl state logs.
     *
     * @param politeCrawlStateLogs the list of crawl state logs to be processed
     */
    private void batchUpdateUrlDisableCrawl(int ownDomainId, List<PoliteCrawlStateLog> politeCrawlStateLogs) {
        final List<String> urlMurmur3HashList = politeCrawlStateLogs.stream().map(PoliteCrawlStateLog::getUrlMurmur3Hash).collect(Collectors.toList());
        final int size = urlMurmur3HashList.size();
        log.info("ownDomainId={}, politeCrawlStateLogs.size()={}", ownDomainId, size);
        if (size <= batchUpdateSize) {
            this.targetUrlEntityDAO.batchUpdateUrlDisableCrawl(ownDomainId, urlMurmur3HashList);
            return;
        }
        // split batch update the urls
        List<String> subList = new ArrayList<>(batchUpdateSize);
        for (String urlMurmur3Hash : urlMurmur3HashList) {
            subList.add(urlMurmur3Hash);
            if (subList.size() == batchUpdateSize) {
                this.targetUrlEntityDAO.batchUpdateUrlDisableCrawl(ownDomainId, subList);
                subList.clear();
            }
        }
        if (!subList.isEmpty()) {
            this.targetUrlEntityDAO.batchUpdateUrlDisableCrawl(ownDomainId, subList);
        }
    }

}
