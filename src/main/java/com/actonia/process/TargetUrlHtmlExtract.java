package com.actonia.process;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.dao.TargetUrlHtmlClickHouseDAO;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.utils.CrawlerUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.value.object.CrawlerResponse;
import com.google.gson.Gson;

// domain_id: 522
// url: https://www.orbitz.com/Green-Bay-Hotels-Baymont-By-Wyndham-Green-Bay.h40154.Hotel-Information
public class TargetUrlHtmlExtract {
	//private static final String DELIMITER = "\t";
	private static final String DELIMITER = "";
	private static final String TABLE_NAME = null;
	//private static final String TABLE_NAME = IConstants.TABLE_NAME_UNIT_TEST_TARGET_URL_HTML;

	public TargetUrlHtmlExtract() {
	}

	public static void main(String[] args) {
		try {
			new TargetUrlHtmlExtract().process(args);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void process(String[] args) throws Exception {
		String outputFilePathLocation = null;
		String domainIdsString = null;
		String urlString = null;
		int[] domainIdArray = null;
		String[] tempStringArray = null;
		Date crawlTimestamp = null;
		String crawlTimestampString = null;

		if (args != null && args.length > 0) {

			// runtime parameter 1: output file location path
			if (args != null && args.length >= 1) {
				outputFilePathLocation = args[0];
				FormatUtils.getInstance().logMemoryUsage("process() runtime parameter 1 outputFilePathLocation=" + outputFilePathLocation);
			}

			// runtime parameter 2: domain ID(s)
			if (args != null && args.length >= 2) {
				domainIdsString = args[1];
				if (StringUtils.isNotBlank(domainIdsString)) {
					tempStringArray = domainIdsString.split(IConstants.COMMA);
					domainIdArray = new int[tempStringArray.length];
					for (int i = 0; i < tempStringArray.length; i++) {
						domainIdArray[i] = Integer.parseInt(tempStringArray[i]);
					}
				}
				for (int i = 0; i < domainIdArray.length; i++) {
					FormatUtils.getInstance().logMemoryUsage("process() runtime parameter 2:domain ID=" + domainIdArray[i]);
				}
			}

			// runtime parameter 3: URL string
			if (args != null && args.length >= 3) {
				urlString = args[2];
				FormatUtils.getInstance().logMemoryUsage("process() runtime parameter 3:urlString=" + urlString);
			}

			// runtime parameter 4: crawl timestamp (YYYY-MM-DD HH:MM:SS)
			if (args != null && args.length >= 4) {
				crawlTimestampString = args[3];
				crawlTimestampString = StringUtils.replace(crawlTimestampString, "`", " ");
				FormatUtils.getInstance().logMemoryUsage("process() runtime parameter 4:crawlTimestampString=" + crawlTimestampString);
				if (StringUtils.isNotBlank(crawlTimestampString)) {
					crawlTimestamp = DateUtils.parseDateStrictly(crawlTimestampString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS });
				}
			}
		} else {
			FormatUtils.getInstance().logMemoryUsage("process() error--runtime parameters are required.");
			return;
		}

		try {
			extractHtml(outputFilePathLocation, domainIdArray, urlString, crawlTimestamp);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void extractHtml(String outputFilePathLocation, int[] domainIdArray, String urlString, Date crawlTimestamp) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("extractHtml() begins. outputFilePathLocation=" + outputFilePathLocation);

		int domainId = 0;
		List<String> outputLineList = new ArrayList<String>();
		List<String> testOutputLineList = null;
		Gson gson = new Gson();
		String json = null;
		HtmlClickHouseEntity htmlClickHouseEntity = null;

		List<String> databaseFields = CrawlerUtils.getInstance().getTargetUrlHtmlTableAllFieldNames();

		for (int i = 0; i < domainIdArray.length; i++) {
			domainId = domainIdArray[i];
			FormatUtils.getInstance().logMemoryUsage("extractHtml() domainId=" + domainId + ",urlString=" + urlString + ",crawlTimestamp=" + crawlTimestamp);
			if (crawlTimestamp != null) {
				htmlClickHouseEntity = TargetUrlHtmlClickHouseDAO.getInstance().getByCrawlTimestamp(crawlTimestamp, domainId, urlString, databaseFields, TABLE_NAME);
				if (htmlClickHouseEntity != null) {
					json = gson.toJson(htmlClickHouseEntity.getCrawlerResponse(), CrawlerResponse.class);
					FormatUtils.getInstance().logMemoryUsage("extractHtml() json=" + json);
					testOutputLineList = CrawlerUtils.getInstance().getHtmlExtractOutputList(htmlClickHouseEntity, databaseFields, DELIMITER);
					outputLineList.addAll(testOutputLineList);
				} else {
					FormatUtils.getInstance().logMemoryUsage("extractHtml() htmlClickHouseEntity not available.");
				}
			} else {
				htmlClickHouseEntity = TargetUrlHtmlClickHouseDAO.getInstance().getPrevious(null, null, domainId, urlString, databaseFields, TABLE_NAME, null, null);
				if (htmlClickHouseEntity != null) {
					json = gson.toJson(htmlClickHouseEntity.getCrawlerResponse(), CrawlerResponse.class);
					FormatUtils.getInstance().logMemoryUsage("extractHtml() json=" + json);
					testOutputLineList = CrawlerUtils.getInstance().getHtmlExtractOutputList(htmlClickHouseEntity, databaseFields, DELIMITER);
					outputLineList.addAll(testOutputLineList);
				} else {
					FormatUtils.getInstance().logMemoryUsage("extractHtml() htmlClickHouseEntity not available.");
				}
			}
		}

		// create output
		File outputFile = new File(outputFilePathLocation);

		try {
			FileUtils.writeLines(outputFile, IConstants.UTF_8, outputLineList);
		} catch (Exception e) {
			e.printStackTrace();
		}

		FormatUtils.getInstance().logMemoryUsage("extractHtml() ends.");
	}
}
