package com.actonia.process;

import java.util.Calendar;
import java.util.Date;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.dao.TargetUrlHtmlTransferClickHouseDAO;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.TransferUtils;

public class TargetUrlHtmlConversion {
	private TargetUrlHtmlTransferClickHouseDAO targetUrlHtmlTransferClickHouseDAO;
	private int totalDaysProcessed = 0;
	private String sourceTableName;
	private String destinationTableName;

	public TargetUrlHtmlConversion() {
		TransferUtils.getInstance();
	}

	public static void main(String[] args) throws Exception {
		new TargetUrlHtmlConversion().process(args);
	}

	private void process(String[] args) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("process() begins.");
		String databaseServerIpAddress = null;
		String startTrackDateOverrideString = null;
		String endTrackDateOverrideString = null;
		Date startTrackDateOverride = null;
		Date endTrackDateOverride = null;

		if (args == null || args.length == 0) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters are required.");
			return;
		}

		// runtime parameter 1 (required): database server IP address
		if (args.length >= 1) {
			databaseServerIpAddress = args[0];
		}
		if (StringUtils.isBlank(databaseServerIpAddress)) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 1: database server IP address is required.");
			return;
		}
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 1: database server IP address=" + databaseServerIpAddress);

		// runtime parameter 2 (required): source table name
		if (args.length >= 2) {
			sourceTableName = args[1];
		}
		if (StringUtils.isBlank(sourceTableName)) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 2: source table name is required.");
			return;
		}
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 2: source table name=" + sourceTableName);

		// runtime parameter 3 (required): destination table name
		if (args.length >= 3) {
			destinationTableName = args[2];
		}
		if (StringUtils.isBlank(destinationTableName)) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 3: destination table name is required.");
			return;
		}
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 3: destination table name=" + destinationTableName);

		// runtime parameter 4 (optional): start track date override (yyyy-mm-dd)
		if (args.length >= 4) {
			startTrackDateOverrideString = args[3];
		}
		if (StringUtils.isNotBlank(startTrackDateOverrideString)) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 4: startTrackDateOverrideString=" + startTrackDateOverrideString);
			startTrackDateOverride = DateUtils.parseDate(startTrackDateOverrideString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
		}

		// runtime parameter 5 (optional): end track date override (yyyy-mm-dd)
		if (args.length >= 5) {
			endTrackDateOverrideString = args[4];
		}
		if (StringUtils.isNotBlank(endTrackDateOverrideString)) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 5: endTrackDateOverrideString=" + endTrackDateOverrideString);
			endTrackDateOverride = DateUtils.parseDate(endTrackDateOverrideString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
		}

		targetUrlHtmlTransferClickHouseDAO = TransferUtils.getInstance().getTargetUrlHtmlTransferClickHouseDAO(databaseServerIpAddress);

		if (targetUrlHtmlTransferClickHouseDAO == null) {
			FormatUtils.getInstance().logMemoryUsage("process() targetUrlHtmlTransferClickHouseDAO cannot be initiated using IP address=" + databaseServerIpAddress);
			return;
		}

		convertAll(startTrackDateOverride, endTrackDateOverride);

		FormatUtils.getInstance()
				.logMemoryUsage("process() ends. totalDaysProcessed=" + totalDaysProcessed + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private void convertAll(Date startTrackDateOverride, Date endTrackDateOverride) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance()
				.logMemoryUsage("convertAll() begins. startTrackDateOverride=" + startTrackDateOverride + ",endTrackDateOverride=" + endTrackDateOverride);
		Date testDate = null;
		Date startTrackDate = null;
		Date endTrackDate = null;
		if (startTrackDateOverride != null && endTrackDateOverride != null) {
			startTrackDate = startTrackDateOverride;
			endTrackDate = endTrackDateOverride;
		} else {
			testDate = targetUrlHtmlTransferClickHouseDAO.getEarliestTrackDate(sourceTableName);
			startTrackDate = DateUtils.truncate(testDate, Calendar.DAY_OF_MONTH);
			testDate = targetUrlHtmlTransferClickHouseDAO.getLatestTrackDate(sourceTableName);
			endTrackDate = DateUtils.addDays(testDate, -1);
		}
		FormatUtils.getInstance().logMemoryUsage("convertAll() startTrackDate=" + startTrackDate);
		FormatUtils.getInstance().logMemoryUsage("convertAll() endTrackDate=" + endTrackDate);

		Date currentTrackDate = startTrackDate;
		//Date testEndTrackDate = null;
		while (!currentTrackDate.after(endTrackDate)) {

			// transfer current track date data from source database to destination database
			transferOneDay(currentTrackDate);

			// calculate next track date one day later
			currentTrackDate = DateUtils.addDays(currentTrackDate, +1);
			totalDaysProcessed++;
		}

		FormatUtils.getInstance().logMemoryUsage("convertAll() ends. startTrackDateOverride=" + startTrackDateOverride + ",endTrackDateOverride=" + endTrackDateOverride
				+ ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private void transferOneDay(Date trackDate) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String trackDateString = DateFormatUtils.format(trackDate, IConstants.DATE_FORMAT_YYYY_MM_DD);
		targetUrlHtmlTransferClickHouseDAO.convertData(sourceTableName, destinationTableName, trackDateString);

		// verify record counts on both source and destination server
		int sourceRecordCount = targetUrlHtmlTransferClickHouseDAO.getRecordCount(trackDate, sourceTableName);
		int destinationRecordCount = targetUrlHtmlTransferClickHouseDAO.getRecordCount(trackDate, destinationTableName);
		if (sourceRecordCount == destinationRecordCount) {
			FormatUtils.getInstance().logMemoryUsage("transferOneDay() trackDate=" + trackDate + ",record counts matched=" + sourceRecordCount);
		} else {
			FormatUtils.getInstance().logMemoryUsage("transferOneDay() trackDate=" + trackDate + ",record counts mismatched: sourceRecordCount=" + sourceRecordCount
					+ ",destinationRecordCount=" + destinationRecordCount);
		}
	}
}
