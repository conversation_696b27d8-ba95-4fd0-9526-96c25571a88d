package com.actonia.process;

import java.util.Calendar;
import java.util.Date;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.dao.TargetUrlHtmlBackupClickHouseDAO;
import com.actonia.utils.BackupUtils;
import com.actonia.utils.FormatUtils;

public class TargetUrlHtmlBackupToS3 {

	private static final String S3_URI_PREFIX = "https://s3.us-east-1.amazonaws.com/";
	private TargetUrlHtmlBackupClickHouseDAO sourceTargetUrlHtmlBackupClickHouseDAO;
	private String s3FolderURI;
	private String sourceTableName;
	private int totalDaysBackedUp = 0;

	public TargetUrlHtmlBackupToS3() {
		BackupUtils.getInstance();
	}

	public static void main(String[] args) throws Exception {
		new TargetUrlHtmlBackupToS3().process(args);
	}

	private void process(String[] args) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("process() begins.");
		String sourceDatabaseServerIpAddress = null;
		String s3BucketName = null;
		String s3PartialPrefix = null;
		String backupType = null;
		String backupTrackDateOverrideString = null;
		Date backupTrackDateOverride = null;

		if (args == null || args.length == 0) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters are required.");
			return;
		}

		// runtime parameter 1 (required): source database server IP address
		if (args.length >= 1) {
			sourceDatabaseServerIpAddress = args[0];
		}
		if (StringUtils.isBlank(sourceDatabaseServerIpAddress)) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 1: source database server IP address is required.");
			return;
		}
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 1: source database server IP address=" + sourceDatabaseServerIpAddress);

		// runtime parameter 2 (required): source table name
		if (args.length >= 2) {
			sourceTableName = args[1];
		}
		if (StringUtils.isBlank(sourceTableName)) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 2: source table name is required.");
			return;
		}
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 2: source table name=" + sourceTableName);

		// runtime parameter 3 (required): S3 bucket name
		if (args.length >= 3) {
			s3BucketName = args[2];
		}
		if (StringUtils.isBlank(s3BucketName)) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 3: S3 bucket name is required.");
			return;
		}
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 3: S3 bucket name=" + s3BucketName);

		// runtime parameter 4 (required): S3 prefix (partial)
		if (args.length >= 4) {
			s3PartialPrefix = args[3];
		}
		if (StringUtils.isBlank(s3PartialPrefix)) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 4: S3 prefix (partial) is required.");
			return;
		}
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 4: S3 prefix (partial)=" + s3PartialPrefix);

		// runtime parameter 5 (required): backup type (initial or incremental)
		if (args.length >= 5) {
			backupType = args[4];
		}
		if (StringUtils.isBlank(backupType)) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 5: backup type is required.");
			return;
		}
		if (StringUtils.equalsIgnoreCase(backupType, IConstants.BACKUP_TYPE_INITIAL) == false
				&& StringUtils.equalsIgnoreCase(backupType, IConstants.BACKUP_TYPE_INCREMENTAL) == false) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 5: backup type " + backupType + " is invalid.");
			return;
		}
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 5: backup type=" + backupType);

		// runtime parameter 6 (optional): incremental backup track date override (yyyy-mm-dd)
		if (args.length >= 6 && StringUtils.equalsIgnoreCase(backupType, IConstants.BACKUP_TYPE_INCREMENTAL) == true) {
			backupTrackDateOverrideString = args[5];
		}
		if (StringUtils.isNotBlank(backupTrackDateOverrideString)) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 6: incremental backup track date override=" + backupTrackDateOverrideString);
			backupTrackDateOverride = DateUtils.parseDate(backupTrackDateOverrideString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
		}

		sourceTargetUrlHtmlBackupClickHouseDAO = BackupUtils.getInstance().getTargetUrlHtmlBackupClickHouseDAO(sourceDatabaseServerIpAddress);

		if (sourceTargetUrlHtmlBackupClickHouseDAO == null) {
			FormatUtils.getInstance()
					.logMemoryUsage("process() sourceTargetUrlHtmlBackupClickHouseDAO cannot be initiated using IP address=" + sourceDatabaseServerIpAddress);
			return;
		}

		// initial backup
		if (StringUtils.equalsIgnoreCase(backupType, IConstants.BACKUP_TYPE_INITIAL)) {
			initialBackup(s3BucketName, s3PartialPrefix);
		}
		// incremental backup
		else {
			incrementalBackup(s3BucketName, s3PartialPrefix, backupTrackDateOverride);
		}

		FormatUtils.getInstance()
				.logMemoryUsage("process() ends. totalDaysBackedUp=" + totalDaysBackedUp + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private void initialBackup(String s3BucketName, String s3PartialPrefix) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("initialBackup() begins. s3BucketName=" + s3BucketName + ",s3PartialPrefix=" + s3PartialPrefix);

		//Date todayDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
		//String backupDateString = DateFormatUtils.format(todayDate, IConstants.DATE_FORMAT_YYYY_MM_DD);
		String backupDateString = "2022-04-05";
		FormatUtils.getInstance().logMemoryUsage("initialBackup() backupDateString=" + backupDateString);

		String s3FullPrefix = s3PartialPrefix + backupDateString;
		FormatUtils.getInstance().logMemoryUsage("initialBackup() s3FullPrefix=" + s3FullPrefix);

		s3FolderURI = S3_URI_PREFIX + s3BucketName + IConstants.SLASH + s3FullPrefix + IConstants.SLASH;
		FormatUtils.getInstance().logMemoryUsage("initialBackup() s3FolderURI=" + s3FolderURI);

		Date testDate = null;
		Date startTrackDate = null;

		//testDate = sourceTargetUrlHtmlBackupClickHouseDAO.getEarliestTrackDate(sourceTableName);
		//Date startTrackDate = DateUtils.truncate(testDate, Calendar.DAY_OF_MONTH);
		if (StringUtils.equalsIgnoreCase(s3PartialPrefix, "cdb21-url-001/crawl/local_target_url_html/")) {
			startTrackDate = DateUtils.parseDate("2021-04-13", new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
		} else if (StringUtils.equalsIgnoreCase(s3PartialPrefix, "cdb21-url-002/crawl/local_target_url_html/")) {
			startTrackDate = DateUtils.parseDate("2021-02-06", new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
		} else if (StringUtils.equalsIgnoreCase(s3PartialPrefix, "cdb21-url-003/crawl/local_target_url_html/")) {
			startTrackDate = DateUtils.parseDate("2021-04-19", new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
		}
		FormatUtils.getInstance().logMemoryUsage("initialBackup() startTrackDate=" + startTrackDate);
		testDate = sourceTargetUrlHtmlBackupClickHouseDAO.getLatestTrackDate(sourceTableName);
		//Date endTrackDate = DateUtils.truncate(testDate, Calendar.DAY_OF_MONTH);
		Date endTrackDate = DateUtils.addDays(testDate, -1);
		FormatUtils.getInstance().logMemoryUsage("initialBackup() endTrackDate=" + endTrackDate);
		Date currentTrackDate = startTrackDate;
		//Date testEndTrackDate = null;
		while (!currentTrackDate.after(endTrackDate)) {

			// backup current track date data from source database to Amazon S3
			backup(currentTrackDate);

			//testDate = sourceTargetUrlHtmlBackupClickHouseDAO.getLatestTrackDate(sourceTableName);
			//testEndTrackDate = DateUtils.truncate(testDate, Calendar.DAY_OF_MONTH);
			//if (testEndTrackDate.after(endTrackDate)) {
			//	endTrackDate = testEndTrackDate;
			//	FormatUtils.getInstance().logMemoryUsage("initialBackup() endTrackDate=" + endTrackDate);
			//}

			// calculate next track date one day later
			currentTrackDate = DateUtils.addDays(currentTrackDate, +1);
			totalDaysBackedUp++;
		}

		//BackupUtils.getInstance().maintainBackupAuditTrailAndS3(sourceTableName, backupDateString, s3BucketName, s3PartialPrefix, startTrackDate, endTrackDate);

		FormatUtils.getInstance().logMemoryUsage("initialBackup() ends. s3BucketName=" + s3BucketName + ",s3PartialPrefix=" + s3PartialPrefix + ",elapsed(s.)="
				+ (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private void incrementalBackup(String s3BucketName, String s3PartialPrefix, Date backupTrackDateOverride) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("incrementalBackup() begins. s3BucketName=" + s3BucketName + ",s3PartialPrefix=" + s3PartialPrefix
				+ ",backupTrackDateOverride=" + backupTrackDateOverride);

		String backupDateString = BackupUtils.getInstance().getLastBackupDateString(sourceTableName, s3BucketName, s3PartialPrefix);
		FormatUtils.getInstance().logMemoryUsage("incrementalBackup() backupDateString=" + backupDateString);

		String s3FullPrefix = s3PartialPrefix + backupDateString;
		FormatUtils.getInstance().logMemoryUsage("incrementalBackup() s3FullPrefix=" + s3FullPrefix);

		s3FolderURI = S3_URI_PREFIX + s3BucketName + IConstants.SLASH + s3FullPrefix + IConstants.SLASH;
		FormatUtils.getInstance().logMemoryUsage("incrementalBackup() s3FolderURI=" + s3FolderURI);

		Date todayDate = null;
		Date backupTrackDate = null;

		// backup yesterday's data from source database to Amazon S3
		if (backupTrackDateOverride == null) {
			todayDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
			backupTrackDate = DateUtils.addDays(todayDate, -1);
		} else {
			backupTrackDate = backupTrackDateOverride;
		}
		String backupTrackDateString = DateFormatUtils.format(backupTrackDate, IConstants.DATE_FORMAT_YYYY_MM_DD);
		FormatUtils.getInstance().logMemoryUsage("incrementalBackup() backupTrackDateString=" + backupTrackDateString);
		backup(backupTrackDate);
		totalDaysBackedUp++;

		BackupUtils.getInstance().updateBackupAuditTrailEndDate(sourceTableName, backupDateString, s3BucketName, s3PartialPrefix, backupTrackDateString);
		FormatUtils.getInstance().logMemoryUsage("incrementalBackup() ends. s3BucketName=" + s3BucketName + ",s3PartialPrefix=" + s3PartialPrefix + ",elapsed(s.)="
				+ (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private void backup(Date trackDate) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String trackDateString = DateFormatUtils.format(trackDate, IConstants.DATE_FORMAT_YYYY_MM_DD);
		FormatUtils.getInstance().logMemoryUsage("backup() begins. trackDateString=" + trackDateString);

		// https://s3.us-east-1.amazonaws.com/seoclarity-cdb/ck-cdb-123/cloudflare/local_index_item_info/2022-02-25/2020-05-08.bin.zstd
		String s3ObjectURI = s3FolderURI + trackDateString + IConstants.BACKUP_FILE_EXT;
		sourceTargetUrlHtmlBackupClickHouseDAO.backupToS3(s3ObjectURI, sourceTableName, trackDateString);
		FormatUtils.getInstance().logMemoryUsage("backup() ends. trackDate=" + trackDate + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}
}
