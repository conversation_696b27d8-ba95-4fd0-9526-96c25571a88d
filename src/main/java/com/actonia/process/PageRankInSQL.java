package com.actonia.process;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.actonia.dao.PageRankEdgeDAO;
import com.actonia.dao.PageRankNodeDAO;
import com.actonia.entity.PageRankEdgeEntity;
import com.actonia.entity.PageRankNodeEntity;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.Md5Util;
import com.actonia.utils.SpringBeanFactory;

public class PageRankInSQL {

	private boolean isDebug = true; //debug
	private PageRankNodeDAO pageRankNodeDAO;
	private PageRankEdgeDAO pageRankEdgeDAO;
	private static final int BIG_DECIMAL_SCALE = 5;
	private static final BigDecimal DAMPING_FACTOR = new BigDecimal(0.85).setScale(BIG_DECIMAL_SCALE, RoundingMode.HALF_UP);
	private static final BigDecimal MARGIN_OF_ERROR = new BigDecimal(0.001).setScale(BIG_DECIMAL_SCALE, RoundingMode.HALF_UP);
	private static final BigDecimal BIG_DECIMAL_ONE = new BigDecimal(1).setScale(BIG_DECIMAL_SCALE, RoundingMode.HALF_UP);
	private static final BigDecimal BIG_DECIMAL_ZERO = new BigDecimal(0).setScale(BIG_DECIMAL_SCALE, RoundingMode.HALF_UP);
	private static final BigDecimal BIG_DECIMAL_NEGATIVE_ONE = new BigDecimal(-1).setScale(BIG_DECIMAL_SCALE, RoundingMode.HALF_UP);

	public PageRankInSQL() {
		super();
		this.pageRankNodeDAO = SpringBeanFactory.getBean("pageRankNodeDAO");
		this.pageRankEdgeDAO = SpringBeanFactory.getBean("pageRankEdgeDAO");
	}

	public static void main(String[] args) {
		try {
			new PageRankInSQL().process();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void process() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("process() begins.");

		// map key = source node ID
		// map value = target node count
		Map<Integer, Integer> sourceNodeIdTargetNodeCountMap = null;

		initialize();

		// create data
		createPageRankNodeTestData();
		createPageRankEdgeTestData();

		// determine the total number of nodes
		int totalNodes = pageRankNodeDAO.getTotalNodes();

		// retrieve a list of all node IDs
		List<Integer> nodeIdList = pageRankNodeDAO.getNodeIdList();

		// retrieve map of source node ID - target node count
		sourceNodeIdTargetNodeCountMap = getSourceNodeIdTargetNodeCountMap();

		updateNodeCount(totalNodes, nodeIdList, sourceNodeIdTargetNodeCountMap);

		convergeNodes();

		FormatUtils.getInstance().logMemoryUsage("process() ends. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void initialize() throws Exception {
		FormatUtils.getInstance().logMemoryUsage("initialize() begins.");
		pageRankNodeDAO.reset();
		pageRankEdgeDAO.reset();
		FormatUtils.getInstance().logMemoryUsage("initialize() ends.");
	}

	private Map<Integer, Integer> getSourceNodeIdTargetNodeCountMap() {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("getSourceNodeIdTargetNodeCountMap() begins.");
		Map<Integer, Integer> sourceNodeIdTargetNodeCountMap = new HashMap<Integer, Integer>();
		List<PageRankEdgeEntity> targetNodeCountList = pageRankEdgeDAO.getTargetNodeCountList();
		if (targetNodeCountList != null && targetNodeCountList.size() > 0) {
			for (PageRankEdgeEntity pageRankEdgeEntity : targetNodeCountList) {
				sourceNodeIdTargetNodeCountMap.put(pageRankEdgeEntity.getSourceNodeId(), pageRankEdgeEntity.getTargetNodeCount());
			}
		}
		FormatUtils.getInstance().logMemoryUsage("getSourceNodeIdTargetNodeCountMap() ends. sourceNodeIdTargetNodeCountMap.size()="
				+ sourceNodeIdTargetNodeCountMap.size() + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return sourceNodeIdTargetNodeCountMap;
	}

	private void updateNodeCount(int totalNodes, List<Integer> nodeIdList, Map<Integer, Integer> sourceNodeIdTargetNodeCountMap) {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("updateNodeCount() begins. totalNodes=" + totalNodes);
		Integer nodeCount = null;
		if (nodeIdList != null && nodeIdList.size() > 0) {
			for (Integer nodeId : nodeIdList) {
				// when source ID has target node count 
				if (sourceNodeIdTargetNodeCountMap.containsKey(nodeId)) {
					nodeCount = sourceNodeIdTargetNodeCountMap.get(nodeId);
				}
				// when source ID has no target node count, 
				else {
					nodeCount = totalNodes;
				}
				if (isDebug == true) {
					System.out.println("updateNodeCount() nodeId=" + nodeId + ",nodeCount=" + nodeCount);
				}
				pageRankNodeDAO.updateNodeCount(nodeId, nodeCount);
			}
		}
		FormatUtils.getInstance().logMemoryUsage("updateNodeCount() ends. totalNodes=" + totalNodes + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void convergeNodes() {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("convergeNodes() begins.");

		BigDecimal transferredNodeWeight = null;
		List<PageRankNodeEntity> nodeIdNodeWeightList = null;
		BigDecimal nodeWeightPrevious = null;
		BigDecimal nodeWeightCurrent = null;
		boolean isNodeConverged = false;
		int iterationNumber = 0;
		List<PageRankNodeEntity> pageRankNodeEntityList = null;

		// map key = target node ID
		// map value = transferred node weight
		Map<Integer, BigDecimal> targetNodeIdTransferredNodeWeightMap = null;

		whileNotConvergedExists: while (pageRankNodeDAO.isNotConvergedExists() == true) {
			nodeIdNodeWeightList = pageRankNodeDAO.getNodeIdNodeWeightList();
			if (nodeIdNodeWeightList != null && nodeIdNodeWeightList.size() > 0) {
				targetNodeIdTransferredNodeWeightMap = pageRankNodeDAO.getTargetNodeIdTransferredNodeWeightMap();
				//if (isDebug == true) {
				//	for (Integer targetNodeId : targetNodeIdTransferredNodeWeightMap.keySet()) {
				//		transferredNodeWeight = targetNodeIdTransferredNodeWeightMap.get(targetNodeId);
				//		System.out.println("updateNodeCount() targetNodeIdTransferredNodeWeightMap:targetNodeId=" + targetNodeId + ",transferredNodeWeight="
				//				+ transferredNodeWeight);
				//	}
				//}
				for (PageRankNodeEntity pageRankNodeEntity : nodeIdNodeWeightList) {
					if (targetNodeIdTransferredNodeWeightMap.containsKey(pageRankNodeEntity.getNodeId())) {
						transferredNodeWeight = targetNodeIdTransferredNodeWeightMap.get(pageRankNodeEntity.getNodeId());
					} else {
						transferredNodeWeight = BIG_DECIMAL_ZERO;
					}
					nodeWeightPrevious = pageRankNodeEntity.getNodeWeight();
					nodeWeightCurrent = BIG_DECIMAL_ONE.subtract(DAMPING_FACTOR).add(transferredNodeWeight).setScale(BIG_DECIMAL_SCALE, RoundingMode.HALF_UP);
					isNodeConverged = checkIfConverged(nodeWeightPrevious, nodeWeightCurrent);
					pageRankNodeDAO.updateNodeWeightNodeConverged(pageRankNodeEntity.getNodeId(), nodeWeightCurrent, isNodeConverged);
				}
				if (isDebug == true) {
					iterationNumber++;
					pageRankNodeEntityList = pageRankNodeDAO.getList();
					if (pageRankNodeEntityList != null && pageRankNodeEntityList.size() > 0) {
						for (PageRankNodeEntity pageRankNodeEntity : pageRankNodeEntityList) {
							System.out.println("convergeNodes() iteration=" + iterationNumber + ",nodeId=" + pageRankNodeEntity.getNodeId() + ",nodeUrl="
									+ pageRankNodeEntity.getNodeUrl() + ",nodeCount=" + pageRankNodeEntity.getNodeCount() + ",nodeWeight="
									+ pageRankNodeEntity.getNodeWeight() + ",isNodeConverged=" + pageRankNodeEntity.isNodeConverged());
						}
					}
				}
			} else {
				break whileNotConvergedExists;
			}
		}
		FormatUtils.getInstance().logMemoryUsage("convergeNodes() ends. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private boolean checkIfConverged(BigDecimal nodeWeightPrevious, BigDecimal nodeWeightCurrent) {
		boolean output = false;
		BigDecimal nodeWeightDifference = nodeWeightPrevious.subtract(nodeWeightCurrent).setScale(BIG_DECIMAL_SCALE, RoundingMode.HALF_UP);

		// when node weight difference is < 0, convert node weight difference to positive decimal 
		if (nodeWeightDifference.compareTo(BIG_DECIMAL_ZERO) < 1) {
			nodeWeightDifference = nodeWeightDifference.multiply(BIG_DECIMAL_NEGATIVE_ONE).setScale(BIG_DECIMAL_SCALE, RoundingMode.HALF_UP);
		}
		// when node weight difference is less than margin of error
		if (nodeWeightDifference.compareTo(MARGIN_OF_ERROR) < 1) {
			output = true;
		}
		return output;
	}

	private void createPageRankNodeTestData() {

		List<PageRankNodeEntity> pageRankNodeEntityList = new ArrayList<PageRankNodeEntity>();
		PageRankNodeEntity pageRankNodeEntity = null;
		String urlString = null;

		// first record
		urlString = "https://www.test1.com/page1.html";
		pageRankNodeEntity = new PageRankNodeEntity();
		pageRankNodeEntity.setNodeUrlHashCode(Md5Util.Md5(urlString));
		pageRankNodeEntity.setNodeUrl(urlString);
		pageRankNodeEntityList.add(pageRankNodeEntity);

		// second record
		urlString = "https://www.test2.com/page2.html";
		pageRankNodeEntity = new PageRankNodeEntity();
		pageRankNodeEntity.setNodeUrlHashCode(Md5Util.Md5(urlString));
		pageRankNodeEntity.setNodeUrl(urlString);
		pageRankNodeEntityList.add(pageRankNodeEntity);

		// third record
		urlString = "https://www.test3.com/page3.html";
		pageRankNodeEntity = new PageRankNodeEntity();
		pageRankNodeEntity.setNodeUrlHashCode(Md5Util.Md5(urlString));
		pageRankNodeEntity.setNodeUrl(urlString);
		pageRankNodeEntityList.add(pageRankNodeEntity);

		// fourth record
		urlString = "https://www.test4.com/page4.html";
		pageRankNodeEntity = new PageRankNodeEntity();
		pageRankNodeEntity.setNodeUrlHashCode(Md5Util.Md5(urlString));
		pageRankNodeEntity.setNodeUrl(urlString);
		pageRankNodeEntityList.add(pageRankNodeEntity);

		pageRankNodeDAO.batchCreate(pageRankNodeEntityList);
	}

	private void createPageRankEdgeTestData() {

		List<PageRankEdgeEntity> pageRankEdgeEntityList = new ArrayList<PageRankEdgeEntity>();
		PageRankEdgeEntity pageRankEdgeEntity = null;

		pageRankEdgeEntity = new PageRankEdgeEntity();
		pageRankEdgeEntity.setSourceNodeId(2);
		pageRankEdgeEntity.setTargetNodeId(1);
		pageRankEdgeEntityList.add(pageRankEdgeEntity);

		pageRankEdgeEntity = new PageRankEdgeEntity();
		pageRankEdgeEntity.setSourceNodeId(2);
		pageRankEdgeEntity.setTargetNodeId(3);
		pageRankEdgeEntityList.add(pageRankEdgeEntity);

		pageRankEdgeEntity = new PageRankEdgeEntity();
		pageRankEdgeEntity.setSourceNodeId(3);
		pageRankEdgeEntity.setTargetNodeId(1);
		pageRankEdgeEntityList.add(pageRankEdgeEntity);

		pageRankEdgeEntity = new PageRankEdgeEntity();
		pageRankEdgeEntity.setSourceNodeId(4);
		pageRankEdgeEntity.setTargetNodeId(1);
		pageRankEdgeEntityList.add(pageRankEdgeEntity);

		pageRankEdgeEntity = new PageRankEdgeEntity();
		pageRankEdgeEntity.setSourceNodeId(4);
		pageRankEdgeEntity.setTargetNodeId(2);
		pageRankEdgeEntityList.add(pageRankEdgeEntity);

		pageRankEdgeEntity = new PageRankEdgeEntity();
		pageRankEdgeEntity.setSourceNodeId(4);
		pageRankEdgeEntity.setTargetNodeId(3);
		pageRankEdgeEntityList.add(pageRankEdgeEntity);

		pageRankEdgeDAO.batchCreate(pageRankEdgeEntityList);
	}

}
