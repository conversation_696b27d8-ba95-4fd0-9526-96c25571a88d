package com.actonia.process;

import java.util.Calendar;
import java.util.Date;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.dao.TargetUrlHtmlDailyTransferClickHouseDAO;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.TransferUtils;

public class TransferTargetUrlHtmlDaily {
	private TargetUrlHtmlDailyTransferClickHouseDAO sourceTargetUrlHtmlDailyTransferClickHouseDAO;
	private TargetUrlHtmlDailyTransferClickHouseDAO destinationTargetUrlHtmlDailyTransferClickHouseDAO;
	private int totalDaysProcessed = 0;
	private String sourceDatabaseServerIpAddress;
	private String sourceTableName;
	private String destinationDatabaseServerIpAddress;
	private String destinationTableName;

	public TransferTargetUrlHtmlDaily() {
		TransferUtils.getInstance();
	}

	public static void main(String[] args) throws Exception {
		new TransferTargetUrlHtmlDaily().process(args);
	}

	private void process(String[] args) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("process() begins.");

		if (args == null || args.length == 0) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters are required.");
			return;
		}

		// runtime parameter 1: source database server IP address
		if (args.length >= 1) {
			sourceDatabaseServerIpAddress = args[0];
		}
		if (StringUtils.isBlank(sourceDatabaseServerIpAddress)) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 1: source database server IP address is required.");
			return;
		}
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 1: source database server IP address=" + sourceDatabaseServerIpAddress);

		// runtime parameter 2: source table name
		if (args.length >= 2) {
			sourceTableName = args[1];
		}
		if (StringUtils.isBlank(sourceTableName)) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 2: source table name is required.");
			return;
		}
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 2: source table name=" + sourceTableName);

		// runtime parameter 3: destination database server IP address
		if (args.length >= 3) {
			destinationDatabaseServerIpAddress = args[2];
		}
		if (StringUtils.isBlank(destinationDatabaseServerIpAddress)) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 3: destination database server IP address is required.");
			return;
		}
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 3: destination database server IP address=" + destinationDatabaseServerIpAddress);

		// runtime parameter 4: destination table name
		if (args.length >= 4) {
			destinationTableName = args[3];
		}
		if (StringUtils.isBlank(destinationTableName)) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 4: destination table name is required.");
			return;
		}
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 4: destination table name=" + destinationTableName);

		sourceTargetUrlHtmlDailyTransferClickHouseDAO = TransferUtils.getInstance().getTargetUrlHtmlDailyTransferClickHouseDAO(sourceDatabaseServerIpAddress);

		if (sourceTargetUrlHtmlDailyTransferClickHouseDAO == null) {
			FormatUtils.getInstance()
					.logMemoryUsage("process() sourceTargetUrlHtmlDailyTransferClickHouseDAO cannot be initiated using IP address=" + sourceDatabaseServerIpAddress);
			return;
		}

		destinationTargetUrlHtmlDailyTransferClickHouseDAO = TransferUtils.getInstance().getTargetUrlHtmlDailyTransferClickHouseDAO(destinationDatabaseServerIpAddress);

		if (destinationTargetUrlHtmlDailyTransferClickHouseDAO == null) {
			FormatUtils.getInstance().logMemoryUsage(
					"process() destinationTargetUrlHtmlDailyTransferClickHouseDAO cannot be initiated using IP address=" + destinationDatabaseServerIpAddress);
			return;
		}

		transferAll();

		FormatUtils.getInstance()
				.logMemoryUsage("process() ends. totalDaysProcessed=" + totalDaysProcessed + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private void transferAll() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("transferAll() begins.");
		Date testDate = sourceTargetUrlHtmlDailyTransferClickHouseDAO.getEarliestDailyDataCreationDate(sourceTableName);
		Date startDailyDataCreationDate = DateUtils.truncate(testDate, Calendar.DAY_OF_MONTH);
		FormatUtils.getInstance().logMemoryUsage("transferAll() startDailyDataCreationDate=" + startDailyDataCreationDate);
		testDate = sourceTargetUrlHtmlDailyTransferClickHouseDAO.getLatestDailyDataCreationDate(sourceTableName);
		Date endDailyDataCreationDate = DateUtils.truncate(testDate, Calendar.DAY_OF_MONTH);
		//Date endDailyDataCreationDate = DateUtils.addDays(testDate, -1);
		FormatUtils.getInstance().logMemoryUsage("transferAll() endDailyDataCreationDate=" + endDailyDataCreationDate);
		Date currentDailyDataCreationDate = startDailyDataCreationDate;
		//Date testEndDailyDataCreationDate = null;
		while (!currentDailyDataCreationDate.after(endDailyDataCreationDate)) {

			// transfer current track date data from source database to destination database
			transferOneDay(currentDailyDataCreationDate);

			//testDate = sourceTargetUrlHtmlDailyTransferClickHouseDAO.getLatestDailyDataCreationDate(sourceTableName);
			//testEndDailyDataCreationDate = DateUtils.truncate(testDate, Calendar.DAY_OF_MONTH);
			//if (testEndDailyDataCreationDate.after(endDailyDataCreationDate)) {
			//	endDailyDataCreationDate = testEndDailyDataCreationDate;
			//	FormatUtils.getInstance().logMemoryUsage("transferAll() endDailyDataCreationDate=" + endDailyDataCreationDate);
			//}

			// calculate next track date one day later
			currentDailyDataCreationDate = DateUtils.addDays(currentDailyDataCreationDate, +1);
			totalDaysProcessed++;
		}

		FormatUtils.getInstance().logMemoryUsage("transferAll() ends. elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private void transferOneDay(Date dailyDataCreationDate) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String dailyDataCreationDateString = DateFormatUtils.format(dailyDataCreationDate, IConstants.DATE_FORMAT_YYYY_MM_DD);
		destinationTargetUrlHtmlDailyTransferClickHouseDAO.transferData(sourceDatabaseServerIpAddress, sourceTableName, destinationTableName,
				dailyDataCreationDateString);

		// verify record counts on both source and destination server
		int sourceRecordCount = sourceTargetUrlHtmlDailyTransferClickHouseDAO.getRecordCount(dailyDataCreationDate, sourceTableName);
		int destinationRecordCount = destinationTargetUrlHtmlDailyTransferClickHouseDAO.getRecordCount(dailyDataCreationDate, destinationTableName);
		if (sourceRecordCount == destinationRecordCount) {
			FormatUtils.getInstance().logMemoryUsage("transferOneDay() dailyDataCreationDate=" + dailyDataCreationDate + ",record counts matched=" + sourceRecordCount);
		} else {
			FormatUtils.getInstance().logMemoryUsage("transferOneDay() dailyDataCreationDate=" + dailyDataCreationDate + ",record counts mismatched: sourceRecordCount="
					+ sourceRecordCount + ",destinationRecordCount=" + destinationRecordCount);
		}
	}
}
