package com.actonia.process;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.dao.SharedCountsClickHouseDAO;
import com.actonia.entity.SharedCountsEntity;
import com.actonia.utils.ClickHouseUtils;
import com.actonia.utils.FormatUtils;

public class SharedCountsUpdate {

	private SharedCountsClickHouseDAO sharedCountsClickHouseDAO;

	private String sharedCountsTableName = null;

	public SharedCountsUpdate() throws Exception {

		// instantiate clickhouse DAO based on clickhouse.properties
		this.sharedCountsClickHouseDAO = ClickHouseUtils.getSharedCountsClickHouseDAO();
	}

	public static void main(String[] args) throws Exception {
		new SharedCountsUpdate().process(args);
	}

	private void process(String[] args) throws Exception {
		String trackDateString = null;
		Date testDate = null;
		if (args != null && args.length >= 1) {
			trackDateString = args[0];
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameter 1: trackDateString=" + trackDateString);
			testDate = DateUtils.parseDate(trackDateString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
		} else {
			testDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
		}
		Date lastSundayDate = ClickHouseUtils.getLastSundayDate(testDate);
		FormatUtils.getInstance().logMemoryUsage("process() last Sunday date=" + DateFormatUtils.format(lastSundayDate, IConstants.DATE_FORMAT_YYYY_MM_DD));

		Integer limit = null;
		String limitString = null;
		if (args != null && args.length >= 2) {
			limitString = args[1];
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameter 2: limit=" + limitString);
			limit = NumberUtils.toInt(limitString);
		}

		FormatUtils.getInstance().logMemoryUsage("process() limit=" + limit);

		String ip = "1";
		String queueName = "testQueue";
		int totalRecordsUpdated = 0;
		int modulus = 0;

		List<SharedCountsEntity> sharedCountsEntityListToBeCreated = new ArrayList<SharedCountsEntity>();
		
		String rootDomain = "jp.co.amazon"; //debug
		String domain = "jp.co.amazon.www"; //debug

		List<SharedCountsEntity> sharedCountsEntityList = sharedCountsClickHouseDAO.getList(lastSundayDate, limit, rootDomain, domain, sharedCountsTableName);
		if (sharedCountsEntityList != null && sharedCountsEntityList.size() > 0) {
			for (SharedCountsEntity sharedCountsEntity : sharedCountsEntityList) {
				sharedCountsEntity.setSign(IConstants.CLICKHOUSE_SIGN_NEGATIVE_1);
				sharedCountsEntityListToBeCreated.add(sharedCountsEntity);
				totalRecordsUpdated++;
				modulus = totalRecordsUpdated % 1000;
				if (modulus == 0) {
					FormatUtils.getInstance().logMemoryUsage("process() totalRecordsUpdated=" + totalRecordsUpdated);
				}
				if (sharedCountsEntityListToBeCreated != null
						&& sharedCountsEntityListToBeCreated.size() >= sharedCountsClickHouseDAO.getBatchCreationSize()) {
					sharedCountsClickHouseDAO.createBatch(sharedCountsEntityListToBeCreated, ip, queueName, sharedCountsTableName);
					sharedCountsEntityListToBeCreated = new ArrayList<SharedCountsEntity>();
				}
			}
			if (sharedCountsEntityListToBeCreated != null && sharedCountsEntityListToBeCreated.size() > 0) {
				sharedCountsClickHouseDAO.createBatch(sharedCountsEntityListToBeCreated, ip, queueName, sharedCountsTableName);
				sharedCountsEntityListToBeCreated = new ArrayList<SharedCountsEntity>();
			}
		}
		FormatUtils.getInstance().logMemoryUsage("process() ends. totalRecordsUpdated=" + totalRecordsUpdated);
	}

}
