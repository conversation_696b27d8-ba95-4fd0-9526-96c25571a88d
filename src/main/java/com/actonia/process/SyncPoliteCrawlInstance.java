package com.actonia.process;

import com.actonia.IConstants;
import com.actonia.dao.*;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.entity.PoliteCrawlDomainSetting;
import com.actonia.entity.PoliteCrawlInstance;
import com.actonia.entity.TargetUrlDailyCrawlTrackingEntity;
import com.actonia.utils.SQSUtils;
import com.actonia.utils.SpringBeanFactory;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.actonia.entity.PoliteCrawlDomainSetting.EnabledStatus.ENABLED;

public class SyncPoliteCrawlInstance {

    private final Logger logger = LogManager.getLogger(SyncPoliteCrawlInstance.class);
    private final PoliteCrawlInstanceDAO politeCrawlInstanceDAO = SpringBeanFactory.getBean("politeCrawlInstanceDAO");
    private final TargetUrlDailyCrawlTrackingEntityDAO dailyCrawlTrackingEntityDAO = SpringBeanFactory.getBean("targetUrlDailyCrawlTrackingEntityDAO");
    private final OwnDomainEntityDAO ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
    private final OwnDomainSettingEntityDAO ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
    private final PoliteCrawlDomainSettingDAO politeCrawlDomainSettingDAO = SpringBeanFactory.getBean("politeCrawlDomainSettingDAO");

    public static void main(String[] args) throws Exception {
        SyncPoliteCrawlInstance syncPoliteCrawlInstance = new SyncPoliteCrawlInstance();
        syncPoliteCrawlInstance.syncTargetUrlDomainSettings();
        syncPoliteCrawlInstance.syncCrawlStatus();
    }

    /**
     * sync enabled/disabled domains to polite_crawl_domain_setting
     */
    private void syncTargetUrlDomainSettings() {
        logger.info("start sync polite_crawl_domain_setting");
        final List<OwnDomainEntity> allDomains = this.ownDomainEntityDAO.getAllDomains();
        logger.info("find all domains size = {}", allDomains.size());
        final Map<Integer, PoliteCrawlDomainSetting> politeSettingMap = this.politeCrawlDomainSettingDAO.findAll().stream().collect(Collectors.toMap(PoliteCrawlDomainSetting::getOwnDomainId, v -> v));
        final int previousSize = politeSettingMap.size();
        logger.info("find all polite_crawl_domain_setting size = {}", previousSize);
        // find domains in t_own_domain but not in polite_crawl_domain_setting
        // need to add to polite_crawl_domain_setting and set enabled to true
        for (OwnDomainEntity domain : allDomains) {
            final Integer domainStatus = domain.getStatus();
            final Integer domainId = domain.getId();
            final PoliteCrawlDomainSetting.EnabledStatus enabledStatusInDomainEntity = PoliteCrawlDomainSetting.EnabledStatus.convertByDomainStatus(domainStatus);
            PoliteCrawlDomainSetting politeCrawlDomainSetting = politeSettingMap.get(domainId);
            if (politeCrawlDomainSetting == null) {
                // only new active domain need to add to polite_crawl_domain_setting
                if (enabledStatusInDomainEntity == ENABLED) {
                    politeCrawlDomainSetting = new PoliteCrawlDomainSetting();
                    politeCrawlDomainSetting.setOwnDomainId(domainId);
                    politeCrawlDomainSetting.setEnabled(enabledStatusInDomainEntity);
                    politeCrawlDomainSetting.setCreateDate(LocalDateTime.now());
                    politeCrawlDomainSetting.setFrequence(1);
                    politeCrawlDomainSetting.setEnableRankingUrl(1);
                    politeCrawlDomainSetting.setTopxRankingUrl(20);
                    politeCrawlDomainSetting = politeCrawlDomainSettingDAO.addPoliteCrawlDomainSetting(politeCrawlDomainSetting);
                    logger.info("new [OID: {} ] {} add to polite_crawl_domain_setting", domainId, enabledStatusInDomainEntity);
                    politeSettingMap.put(domainId, politeCrawlDomainSetting);
                }
            } else {
                // only update polite_crawl_domain_setting if domain status has changed
                final PoliteCrawlDomainSetting.EnabledStatus enabledStatusInSetting = politeCrawlDomainSetting.getEnabled();
                if (enabledStatusInSetting != ENABLED) {
                    // don't update if previous status is disabled
                    continue;
                }
                // if the previous enabled status is enabled,
                // need to check domain status in ownDomainEntity is no longer not enabled
                final boolean domainCurrentNotEnabled = enabledStatusInDomainEntity != ENABLED;
                if (domainCurrentNotEnabled) {
                    // domain status has changed, update polite_crawl_domain_setting status
                    politeCrawlDomainSetting.setEnabled(enabledStatusInDomainEntity);
                    politeCrawlDomainSettingDAO.update(politeCrawlDomainSetting);
                    logger.info("[OID: {} ] update disabled", domainId);
                }
            }
        }
        logger.info("after process all ownDomainEntity polite_crawl_domain_setting size = {} (previous size = {})", politeSettingMap.size(), previousSize);
        logger.info("sync polite_crawl_domain_setting done");
    }

    private void syncCrawlStatus() throws Exception {
        logger.info("start sync crawl status");
        final List<OwnDomainEntity> domainsWithTargetUrl = this.ownDomainEntityDAO.findDomainsWithTargetUrl();
        // filter domains by polite_crawl_domain_setting
        final Map<Integer, PoliteCrawlDomainSetting> politeCrawlDomainSettingMap = this.politeCrawlDomainSettingDAO.findByEnabledStatus(ENABLED).stream().collect(Collectors.toMap(PoliteCrawlDomainSetting::getOwnDomainId, v -> v));
        logger.info("find domainsWithTargetUrl size = {}", domainsWithTargetUrl.size());
        final Map<Integer, PoliteCrawlInstance> politeCrawlInstanceMap = this.findLatestPoliteCrawlInstance();
        Integer today = Integer.valueOf(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        for (OwnDomainEntity domain : domainsWithTargetUrl) {
            final Integer domainId = domain.getId();
            final PoliteCrawlDomainSetting politeCrawlDomainSetting = politeCrawlDomainSettingMap.get(domainId);
            if (politeCrawlDomainSetting == null) {
                // skip this domain is not enabled
                continue;
            }
            Integer trackDate = null;
            try {
                final TargetUrlDailyCrawlTrackingEntity targetUrlDailyCrawlTrackingEntity = this.dailyCrawlTrackingEntityDAO.get(IConstants.PROCESS_TYPE_NUMBER_CLICKHOUSE, domainId);
                trackDate = targetUrlDailyCrawlTrackingEntity.getTrackDate();
                PoliteCrawlInstance politeCrawlInstance = politeCrawlInstanceMap.get(domainId);
                final String queueName = IConstants.QUEUE_NAME_TARGET_URL_DAILY_HTML_PREFIX + domain.getLanguage().toUpperCase() + IConstants.UNDERSCORE + domainId;
                final String queueUrl = SQSUtils.getInstance().createQueue(queueName);
                final LocalDateTime now = LocalDateTime.now();
                // check if latest instance of domain is older than 14 days
                if (politeCrawlInstance == null) {
                    // create new instance has been sent and insert into table before check attributes in queue
                    politeCrawlInstance = syncInstanceFromTrackingTable(targetUrlDailyCrawlTrackingEntity, queueName);
                }
                final PoliteCrawlInstance.CrawlStatusEnum crawlStatus = politeCrawlInstance.getCrawlStatus();
                final Integer instanceCrawlDate = politeCrawlInstance.getCrawlDate();
                if (instanceCrawlDate < trackDate) {
                    // crawlDate is behind trackDate sync data from tracking table
                    politeCrawlInstance = syncInstanceFromTrackingTable(targetUrlDailyCrawlTrackingEntity, queueName);
                }
                final boolean alreadyCrawlCompleted = today.equals(instanceCrawlDate) && crawlStatus == PoliteCrawlInstance.CrawlStatusEnum.CRAWL_COMPLETED;
                if (alreadyCrawlCompleted) {
                    // already crawl completed today
//                    logger.info("[OID: {} ] already crawl completed", domainId);
                    continue;
                }
                // check attributes in queue, update crawl status
                final Integer[] approximateNumberOfMessagesAndInflight = SQSUtils.getInstance().getApproximateNumberOfMessagesAndInflightByQueueUrl(queueUrl);
                final Integer numberOfMessages = approximateNumberOfMessagesAndInflight[0];
                final Integer numberOfInflight = approximateNumberOfMessagesAndInflight[1];
                if (numberOfInflight > 0) {
                    // this domain is crawling now
                    if (crawlStatus == PoliteCrawlInstance.CrawlStatusEnum.CRAWLING) {
                        // keep crawling status, no need to update crawlStatus again
                        continue;
                    }
                    politeCrawlInstance.setCrawlStatus(PoliteCrawlInstance.CrawlStatusEnum.CRAWLING);
                    politeCrawlInstance.setStartCrawlDate(now);
                    politeCrawlInstanceDAO.update(politeCrawlInstance);
                    logger.info("[OID: {} ] update crawl status to CRAWLING, numberOfMessages = {}", domainId, numberOfMessages);
                } else if (numberOfMessages == 0) {
                    // numberOfMessages and inflight both 0
                    if (instanceCrawlDate < today && crawlStatus == PoliteCrawlInstance.CrawlStatusEnum.CRAWL_COMPLETED) {
                        // this domain has not sent on today yet, wait domain to send
                        continue;
                    }
                    // This domain has no messages in the queue, update politeCrawlInstance crawlStatus to CRAWL_COMPLETED
                    politeCrawlInstance.setCrawlStatus(PoliteCrawlInstance.CrawlStatusEnum.CRAWL_COMPLETED);
                    politeCrawlInstance.setEndCrawlDate(now);
                    politeCrawlInstanceDAO.updateCrawlStatusToCompleted(politeCrawlInstance);
                    logger.info("[OID: {} ] update crawl status to CRAWL_COMPLETED", domainId);
                }
            } catch (Exception e) {
                logger.info("[OID: {} ] sync error: \n{}", domainId, e.getMessage(), e);
            }
        }
        logger.info("sync crawl status done");
    }

    private PoliteCrawlInstance syncInstanceFromTrackingTable(TargetUrlDailyCrawlTrackingEntity targetUrlDailyCrawlTrackingEntity, String queueName) {
        PoliteCrawlInstance politeCrawlInstance = new PoliteCrawlInstance();
        politeCrawlInstance.setOwnDomainId(targetUrlDailyCrawlTrackingEntity.getDomainId());
        politeCrawlInstance.setCrawlDate(targetUrlDailyCrawlTrackingEntity.getTrackDate());
        politeCrawlInstance.setCrawlType(PoliteCrawlInstance.CrawlTypeEnum.TARGET_URL);
        final LocalDateTime now = LocalDateTime.now();
        politeCrawlInstance.setEndSendDate(now);
        politeCrawlInstance.setSendStatus(PoliteCrawlInstance.SendStatusEnum.SEND_COMPLETED);
        politeCrawlInstance.setQueueName(queueName);
        politeCrawlInstance.setTotalSendCount(targetUrlDailyCrawlTrackingEntity.getTotalUrls());
        politeCrawlInstance.setCreateDate(now);
        politeCrawlInstance.setCrawlStatus(PoliteCrawlInstance.CrawlStatusEnum.NOT_STARTED);
        politeCrawlInstanceDAO.insert(politeCrawlInstance);
        logger.info("[OID: {} ] create new instance and insert into table, crawlDate = {}", politeCrawlInstance.getOwnDomainId(), politeCrawlInstance.getCrawlDate());
        return politeCrawlInstance;
    }

    private Map<Integer, PoliteCrawlInstance> findLatestPoliteCrawlInstance() {
        final int twoWeeksAgo = Integer.parseInt(LocalDate.now().minusDays(14).format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        final List<PoliteCrawlInstance> politeCrawlInstances = politeCrawlInstanceDAO.findLatestCrawlDateInstance(twoWeeksAgo);
        logger.info("find latest polite crawl instance size={}, twoWeeksAgo={}", politeCrawlInstances.size(), twoWeeksAgo);
        return politeCrawlInstances.stream()
                .collect(Collectors.toMap(PoliteCrawlInstance::getOwnDomainId, politeCrawlInstance -> politeCrawlInstance));
    }

}
