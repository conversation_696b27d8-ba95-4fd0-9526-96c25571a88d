package com.actonia.process;

import com.actonia.IConstants;
import com.actonia.content.guard.change.ChangeIndicatorEnum;
import com.actonia.content.guard.change.IndicatorStrategy;
import com.actonia.dao.HtmlBigDataClickHouseDAO;
import com.actonia.dao.HtmlChangeClickHouseDAO;
import com.actonia.dao.TargetUrlChangeIndClickHouseDAO;
import com.actonia.dao.TargetUrlHtmlClickHouseDAO;
import com.actonia.entity.HtmlBigData;
import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
import com.actonia.utils.CrawlerUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.StopWatch;

import java.text.ParseException;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import static com.actonia.IConstants.DATE_FORMAT_YYYY_MM_DD;

public class CreateHtmlBigData {

	private static final Logger log = LogManager.getLogger(CreateHtmlBigData.class);
	private static final int mod = 40;
	private final TargetUrlChangeIndClickHouseDAO targetUrlChangeIndClickHouseDAO;
	private final TargetUrlHtmlClickHouseDAO targetUrlHtmlClickHouseDAO;
	private final List<String> fieldNames;
	private final LocalDate startDate;
	private final LocalDate endDate;
	private final HtmlBigDataClickHouseDAO htmlBigDataClickHouseDAO;
	private final Date firstDate = DateUtils.parseDate("2023-09-01", new String[] { DATE_FORMAT_YYYY_MM_DD });
	final ConcurrentLinkedQueue<HtmlBigData> htmlBigDataQueue = new ConcurrentLinkedQueue<>();
	private static ExecutorService executorService = Executors.newFixedThreadPool(3);
	private static final int batchSize = 20000;


	public CreateHtmlBigData(LocalDate startDate, LocalDate endDate) throws ParseException {
		this.startDate = startDate;
		this.endDate = endDate;
		try {
			fieldNames = new ArrayList<>();
			fieldNames.add(IConstants.DOMAIN_ID);
			fieldNames.add(IConstants.URL);
			fieldNames.add(IConstants.TRACK_DATE);
			fieldNames.add(IConstants.CUSTOM_DATA);
			fieldNames.add(IConstants.RESPONSE_HEADERS);
			fieldNames.add(IConstants.STRUCTURED_DATA_CHG_IND);
			targetUrlChangeIndClickHouseDAO = TargetUrlChangeIndClickHouseDAO.getInstance();
			targetUrlHtmlClickHouseDAO = TargetUrlHtmlClickHouseDAO.getInstance();
			htmlBigDataClickHouseDAO = HtmlBigDataClickHouseDAO.getInstance();
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	public static void main(String[] args) throws Exception {
		try {
			LocalDate startDate = LocalDate.of(2023, 9, 3);
			LocalDate endDate = LocalDate.of(2024, 1, 31);
			CreateHtmlBigData transferHtmlChange = new CreateHtmlBigData(startDate, endDate);
			LocalDate trackDate = transferHtmlChange.endDate;
			StopWatch stopWatch = new StopWatch("transfer");
			while (!trackDate.isBefore(transferHtmlChange.startDate)) {
				log.info("start transfer: {}", trackDate);
				stopWatch.start("transfer: " + trackDate);
				transferHtmlChange.transfer(trackDate.toString());
				stopWatch.stop();
				log.info("end transfer: {}, cost time: {} min", trackDate, stopWatch.getLastTaskTimeMillis() / 1000 / 60);
				trackDate = trackDate.minusDays(1);
			}
			if (!transferHtmlChange.htmlBigDataQueue.isEmpty()) {
				transferHtmlChange.htmlBigDataClickHouseDAO.createBatch(new ArrayList<>(transferHtmlChange.htmlBigDataQueue));
			}
			log.info("all finished\n{}", stopWatch.prettyPrint());
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		executorService.shutdown();
	}

	private void transfer(String trackDate) throws Exception {
		for (int i = 0; i < mod; i++) {
			StopWatch stopWatch = new StopWatch("mod-" + i);
			stopWatch.start("queryList");
			final List<TargetUrlChangeIndClickHouseEntity> targetUrlChangeIndClickHouseEntities = targetUrlChangeIndClickHouseDAO.queryListByTrackPeriod(trackDate, mod, i, fieldNames);
			stopWatch.stop();
			log.info("trackDate: {}, index = {}, queryList size: {}, cost time: {}", trackDate, i, targetUrlChangeIndClickHouseEntities.size(), stopWatch.getTotalTimeSeconds());
			if (targetUrlChangeIndClickHouseEntities.isEmpty()) {
				log.info("trackDate: {}, index = {}, queryList is empty", trackDate, i);
				continue;
			}
			try {
				stopWatch.start("createBigDataList");
				final List<HtmlBigData> currentBigDataList = targetUrlChangeIndClickHouseEntities.parallelStream()
						.map(this::currentBigData)
						.filter(Objects::nonNull)
						.collect(Collectors.toList());
				htmlBigDataQueue.addAll(currentBigDataList);
				final List<HtmlBigData> previousDataList = targetUrlChangeIndClickHouseEntities.parallelStream()
						.filter(changeIndClickHouseEntity -> DateUtils.truncatedCompareTo(changeIndClickHouseEntity.getPreviousCrawlTimestamp(), firstDate, Calendar.DAY_OF_MONTH) < 0)
						.map(this::previousBigData)
						.filter(Objects::nonNull)
						.collect(Collectors.toList());
				stopWatch.stop();
				htmlBigDataQueue.addAll(previousDataList);
				log.info("trackDate: {}, currentBigDataList size: {}, previousDataList size: {}, cost time: {} ms", trackDate, currentBigDataList.size(), previousDataList.size(), stopWatch.getLastTaskTimeMillis());

				if (htmlBigDataQueue.size() >= batchSize) {
					executorService.submit(() -> {
						stopWatch.start("createBatch");
						final List<HtmlBigData> htmlBigDataList = new ArrayList<>(batchSize);
						for (int j = 0; j < batchSize; j++) {
							final HtmlBigData htmlBigData = htmlBigDataQueue.poll();
							if (htmlBigData != null) {
								htmlBigDataList.add(htmlBigData);
							}
						}
						htmlBigDataClickHouseDAO.createBatch(htmlBigDataList);
						stopWatch.stop();
						log.info("trackDate: {}, createBatch cost time: {} ms", trackDate, stopWatch.getLastTaskTimeMillis());
					});
				}
				Thread.sleep(1000);
			} catch (Exception e) {
				log.error(e.getMessage());
				throw new RuntimeException(e);
			}
		}

	}

	private HtmlBigData currentBigData(TargetUrlChangeIndClickHouseEntity changeIndEntity) {
		final ChangeIndicatorEnum indicatorEnum = ChangeIndicatorEnum.fromIndicator(changeIndEntity.getChangeIndicator());
		final IndicatorStrategy strategy = indicatorEnum.getStrategy();
		final String md5Curr = strategy.convertCurrValue(changeIndEntity);
		if (md5Curr == null) {
			return null;
		}
		final HtmlBigData htmlBigData = new HtmlBigData();
		htmlBigData.setTrackDate(changeIndEntity.getTrackDate());
		htmlBigData.setUrl(changeIndEntity.getUrl());
		htmlBigData.setMd5(md5Curr);
		htmlBigData.setRawData(strategy.rawCurrValue(changeIndEntity));
		return htmlBigData;
	}

	private HtmlBigData previousBigData(TargetUrlChangeIndClickHouseEntity changeIndEntity) {
		final ChangeIndicatorEnum indicatorEnum = ChangeIndicatorEnum.fromIndicator(changeIndEntity.getChangeIndicator());
		final IndicatorStrategy strategy = indicatorEnum.getStrategy();
		final String md5Prev = strategy.convertPrevValue(changeIndEntity);
		if (md5Prev == null) {
			return null;
		}
		final HtmlBigData htmlBigData = new HtmlBigData();
		final int i = DateUtils.truncatedCompareTo(changeIndEntity.getPreviousCrawlTimestamp(), changeIndEntity.getCurrentCrawlTimestamp(), Calendar.DAY_OF_MONTH);
		htmlBigData.setTrackDate(DateUtils.truncate(changeIndEntity.getPreviousCrawlTimestamp(), Calendar.DAY_OF_MONTH));
		htmlBigData.setUrl(changeIndEntity.getUrl());
		htmlBigData.setMd5(md5Prev);
		htmlBigData.setRawData(strategy.rawPrevValue(changeIndEntity));
		return htmlBigData;
	}

}
