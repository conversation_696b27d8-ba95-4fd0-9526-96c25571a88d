package com.actonia.process;

import java.io.File;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.dao.SharedCountsClickHouseDAO;
import com.actonia.entity.SharedCountsEntity;
import com.actonia.utils.ClickHouseUtils;
import com.actonia.utils.FormatUtils;

public class SharedCountsExtract {

	private SharedCountsClickHouseDAO sharedCountsClickHouseDAO;
	private static final String OUTPUT_FILE_PATH_1 = "/home/<USER>/source/polite-crawl-put-messages/output/";
	private static final String OUTPUT_FILE_PATH_2 = ".txt";
	private static final String DELIMITER = "\t";
	private String sharedCountsTableName = null;

	public SharedCountsExtract() throws Exception {

		// instantiate clickhouse DAO based on clickhouse.properties
		this.sharedCountsClickHouseDAO = ClickHouseUtils.getSharedCountsClickHouseDAO();
	}

	public static void main(String[] args) throws Exception {
		new SharedCountsExtract().process(args);
	}

	private void process(String[] args) throws Exception {
		String trackDateString = null;
		Date testDate = null;
		if (args != null && args.length >= 1) {
			trackDateString = args[0];
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameter 1: trackDateString=" + trackDateString);
			testDate = DateUtils.parseDate(trackDateString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
		} else {
			testDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
		}
		Date lastSundayDate = ClickHouseUtils.getLastSundayDate(testDate);
		FormatUtils.getInstance().logMemoryUsage("process() last Sunday date=" + DateFormatUtils.format(lastSundayDate, IConstants.DATE_FORMAT_YYYY_MM_DD));

		Integer limit = null;
		String limitString = null;
		if (args != null && args.length >= 2) {
			limitString = args[1];
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameter 2: limit=" + limitString);
			limit = NumberUtils.toInt(limitString);
		}

		FormatUtils.getInstance().logMemoryUsage("process() limit=" + limit);

		List<String> outputLineList = new ArrayList<String>();

		String outputLine = null;
		
		String rootDomain = "jp.co.amazon"; //debug
		String domain = "jp.co.amazon.www"; //debug

		List<SharedCountsEntity> sharedCountsEntityList = sharedCountsClickHouseDAO.getList(lastSundayDate, limit, rootDomain, domain, sharedCountsTableName);
		//List<SharedCountsEntity> sharedCountsEntityList = sharedCountsClickHouseDAO.getNonZeroSharedCountList(lastSundayDate, limit);
		if (sharedCountsEntityList != null && sharedCountsEntityList.size() > 0) {

			// heading
			outputLine = "TrackDate" + DELIMITER + "Domain" + DELIMITER + "RootDomain" + DELIMITER + "Url" + DELIMITER + "Protocol" + DELIMITER
					+ "Uri" + DELIMITER + "Folder1" + DELIMITER + "Folder2" + DELIMITER + "CityHashUrl" + DELIMITER + "CityHashUri" + DELIMITER
					+ "CityHashFolder1" + DELIMITER + "CityHashFolder2" + DELIMITER + "FacebookSharedCount" + DELIMITER + "LinkedInSharedCount"
					+ DELIMITER + "TwitterSharedCount" + DELIMITER + "GooglePlusSharedCount" + DELIMITER + "PinterestSharedCount"
					 + DELIMITER + "StumbleuponSharedCount"
					;
			outputLineList.add(outputLine);

			for (SharedCountsEntity sharedCountsEntity : sharedCountsEntityList) {
				outputLine = sharedCountsEntity.getTrackDate() 
						+ DELIMITER + sharedCountsEntity.getDomain() 
						+ DELIMITER	+ sharedCountsEntity.getRootDomain() 
						+ DELIMITER + sharedCountsEntity.getUrl() 
						+ DELIMITER + sharedCountsEntity.getProtocol() 
						+ DELIMITER + sharedCountsEntity.getUri() 
						+ DELIMITER + sharedCountsEntity.getFolder1() 
						+ DELIMITER + sharedCountsEntity.getFolder2() 
						+ DELIMITER + "'" + sharedCountsEntity.getUrlHash()						
						+ DELIMITER + "'" + sharedCountsEntity.getUriHash() 
						+ DELIMITER	+ "'" + sharedCountsEntity.getFolder1Hash() 
						+ DELIMITER + "'" + sharedCountsEntity.getFolder2Hash() 
						+ DELIMITER + sharedCountsEntity.getSharedCountsValueObject().getFacebook().getShare_count() 
						+ DELIMITER + sharedCountsEntity.getSharedCountsValueObject().getLinkedin()
						+ DELIMITER + sharedCountsEntity.getSharedCountsValueObject().getTwitter() 
						+ DELIMITER + sharedCountsEntity.getSharedCountsValueObject().getGoogleplusone()
						+ DELIMITER + sharedCountsEntity.getSharedCountsValueObject().getPinterest()
						+ DELIMITER + sharedCountsEntity.getSharedCountsValueObject().getStumbleupon()
						;
				outputLineList.add(outputLine);
				FormatUtils.getInstance().logMemoryUsage("process() outputLine=" + outputLine);
			}

			// create output
			//File outputFile = new File(OUTPUT_FILE_PATH_1 + "social_detail_"
			//		+ DateFormatUtils.getInstance().format(new Date(), IConstants.DATE_FORMAT_YYYYMMDDHHMMSS) + OUTPUT_FILE_PATH_2);

			File outputFile = new File(OUTPUT_FILE_PATH_1 + "social_detail" + OUTPUT_FILE_PATH_2);

			try {
				FileUtils.writeLines(outputFile, outputLineList);
			} catch (Exception e) {
				e.printStackTrace();
			}
		} else {
			FormatUtils.getInstance().logMemoryUsage("process() sharedCountsEntityList is empty.");			
		}
	}

}
