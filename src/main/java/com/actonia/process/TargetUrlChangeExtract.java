package com.actonia.process;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;

import com.actonia.IConstants;
import com.actonia.dao.TargetUrlChangeIndClickHouseDAO;
import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
import com.actonia.utils.CrawlerUtils;
import com.actonia.utils.FormatUtils;
import com.google.gson.Gson;

public class TargetUrlChangeExtract {
	private static final String DELIMITER = "\t";

	public TargetUrlChangeExtract() {
	}

	public static void main(String[] args) {
		try {
			new TargetUrlChangeExtract().process(args);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void process(String[] args) throws Exception {
		String outputFilePathLocation = null;
		String domainIdString = null;
		String urlString = null;
		int domainId = 0;
		String crawlTimestampString = null;

		if (args != null && args.length > 0) {

			// runtime parameter 1: output file location path
			if (args.length >= 1) {
				outputFilePathLocation = args[0];
				FormatUtils.getInstance().logMemoryUsage("process() runtime parameter 1 outputFilePathLocation=" + outputFilePathLocation);
			}

			// runtime parameter 2: domain ID(s)
			if (args.length >= 2) {
				domainIdString = args[1];
				FormatUtils.getInstance().logMemoryUsage("process() runtime parameter 2:domain ID=" + domainIdString);
				if (StringUtils.isNotBlank(domainIdString)) {
					domainId = NumberUtils.toInt(domainIdString);
				}
				if (domainId == 0) {
					FormatUtils.getInstance().logMemoryUsage("process() runtime parameter 2 domain ID is required.");
					return;
				}
			}

			// runtime parameter 3: URL string
			if (args.length >= 3) {
				urlString = args[2];
				FormatUtils.getInstance().logMemoryUsage("process() runtime parameter 3:urlString=" + urlString);
			}

			// runtime parameter 4: crawl timestamp (YYYY-MM-DD HH:MM:SS)
			if (args.length >= 4) {
				crawlTimestampString = args[3];
				FormatUtils.getInstance().logMemoryUsage("process() runtime parameter 4:crawlTimestampString=" + crawlTimestampString);
			}
		} else {
			FormatUtils.getInstance().logMemoryUsage("process() error--runtime parameters are required.");
			return;
		}

		try {
			TargetUrlChangeIndClickHouseDAO.getInstance();
			extract(outputFilePathLocation, domainId, urlString, crawlTimestampString);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void extract(String outputFilePathLocation, int domainId, String urlString, String crawlTimestampString) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("extract() begins. outputFilePathLocation=" + outputFilePathLocation + ",domainId=" + domainId + ",urlString="
				+ urlString + ",crawlTimestampString=" + crawlTimestampString);
		List<String> outputLineList = new ArrayList<String>();
		List<String> testOutputLineList = null;
		Gson gson = new Gson();
		String json = null;

		List<String> databaseFields = CrawlerUtils.getInstance().getTargetUrlChangeIndTableAllFieldNames();
		TargetUrlChangeIndClickHouseEntity targetUrlChangeClickHouseEntity = TargetUrlChangeIndClickHouseDAO.getInstance().get(domainId, urlString,
				crawlTimestampString, databaseFields, IConstants.CLICKHOUSE_TABLE_NAME_NULL);
		if (targetUrlChangeClickHouseEntity != null) {
			//testOutputLineList = CrawlerUtils.getInstance().getTargetUrlChangeExtractOutputList(targetUrlChangeClickHouseEntity, databaseFields, DELIMITER);
			outputLineList.addAll(testOutputLineList);
		}

		// create output
		File outputFile = new File(outputFilePathLocation);

		try {
			FileUtils.writeLines(outputFile, IConstants.UTF_8, outputLineList);
		} catch (Exception e) {
			e.printStackTrace();
		}
		FormatUtils.getInstance().logMemoryUsage("extract() ends. outputFilePathLocation=" + outputFilePathLocation + ",domainId=" + domainId + ",urlString="
				+ urlString + ",crawlTimestampString=" + crawlTimestampString);
	}
}
