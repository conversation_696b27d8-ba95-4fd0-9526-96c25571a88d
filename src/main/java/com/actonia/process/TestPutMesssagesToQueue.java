package com.actonia.process;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;

import com.actonia.IConstants;
import com.actonia.dao.OwnDomainEntityDAO;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.entity.UrlMetricsEntityV3;
import com.actonia.utils.SQSUtils;
import com.actonia.utils.SpringBeanFactory;
import com.google.gson.Gson;

public class TestPutMesssagesToQueue {

	private OwnDomainEntityDAO ownDomainEntityDAO;

	public TestPutMesssagesToQueue() {
		this.ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
	}

	public static void main(String[] args) {
		try {
			new TestPutMesssagesToQueue().process(args);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void process(String[] args) throws Exception {
		System.out.println("process() begins.");
		List<String> urlList = null;
		String queueName = null;
		OwnDomainEntity ownDomainEntity = null;
		String domainName = null;
		String language = null;
		SQSUtils.getInstance();
		String fileLocation = args[0];
		System.out.println("process() runtime parameter: fileLocation=" + fileLocation);

		//map key: domain ID
		//map value: test queue name
		Map<Integer, String> domainIdTestQueueNameMap = getDomainIdTestQueueNameMap();

		//map key: domain ID
		//map value: list of URLs
		Map<Integer, List<String>> domainIdUrlListMap = getDomainIdUrlListMap(fileLocation);

		for (Integer domainId : domainIdTestQueueNameMap.keySet()) {
			ownDomainEntity = ownDomainEntityDAO.getById(domainId);
			if (ownDomainEntity != null) {
				domainName = ownDomainEntity.getDomain();
				language = ownDomainEntity.getLanguage();
				if (StringUtils.endsWithIgnoreCase(domainName, "hyatt.com") == false && StringUtils.endsWithIgnoreCase(domainName, "apple.com") == false) {
					queueName = domainIdTestQueueNameMap.get(domainId);
					if (domainIdUrlListMap.containsKey(domainId)) {
						urlList = domainIdUrlListMap.get(domainId);
						System.out.println("process() domainId=" + domainId + ",queueName=" + queueName + ",urlList.size()=" + urlList.size());
						putMessagesToQueue(domainId, queueName, urlList, language);
					} else {
						System.out
								.println("process() error--domainId=" + domainId + ",domainName=" + domainName + ",queueName=" + queueName + ",urlList not available.");
					}
				}
			}
		}
		System.out.println("process() end.");
	}

	private void putMessagesToQueue(Integer domainId, String queueName, List<String> urlList, String language) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		System.out.println(
				"putMessagesToQueue() begins. domainId=" + domainId + ",queueName=" + queueName + ",urlList.size()=" + urlList.size() + ",language=" + language);
		UrlMetricsEntityV3 urlMetricsEntityV3 = null;
		String messageId = null;
		String messageBody = null;
		Gson gson = new Gson();
		String queueUrl = SQSUtils.getInstance().createQueue(queueName);
		SQSUtils.getInstance().purgeQueue(queueUrl);
		Map<String, String> messages = new HashMap<String, String>();
		for (String urlString : urlList) {
			urlMetricsEntityV3 = new UrlMetricsEntityV3();
			urlMetricsEntityV3.setUrl(urlString);
			urlMetricsEntityV3.setLanguageCode(language);
			messageId = String.valueOf(System.nanoTime());
			messageBody = gson.toJson(urlMetricsEntityV3);
			System.out.println("putMessagesToQueue() domainId=" + domainId + ",queueName=" + queueName + ",messageId=" + messageId + ",messageBody=" + messageBody);
			messages.put(messageId, messageBody);
		}
		SQSUtils.getInstance().sendBatchMessageToQueue(queueUrl, messages);
		System.out.println(
				"putMessagesToQueue() ends. domainId=" + domainId + ",queueName=" + queueName + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private Map<Integer, List<String>> getDomainIdUrlListMap(String fileLocation) throws Exception {
		Map<Integer, List<String>> domainIdUrlListMap = new HashMap<Integer, List<String>>();
		List<String> urlList = null;
		Integer domainId = null;
		String urlString = null;
		List<String[]> stringArrayList = getStringArrayList(fileLocation);
		for (String[] stringArray : stringArrayList) {
			//System.out.println("getDomainIdUrlListMap() stringArray[0]=" + stringArray[0] + ",stringArray[2]=" + stringArray[2]);
			domainId = NumberUtils.toInt(stringArray[0]);
			urlString = stringArray[2];
			if (domainIdUrlListMap.containsKey(domainId)) {
				urlList = domainIdUrlListMap.get(domainId);
			} else {
				urlList = new ArrayList<String>();
			}
			urlList.add(urlString);
			domainIdUrlListMap.put(domainId, urlList);
		}
		//for (Integer testDomainId : domainIdUrlListMap.keySet()) {
		//	urlList = domainIdUrlListMap.get(testDomainId);
		//	for (String testUrlString : urlList) {
		//		System.out.println("getDomainIdUrlListMap() testDomainId=" + testDomainId + ",testUrlString=" + testUrlString);
		//	}
		//}
		return domainIdUrlListMap;
	}

	private List<String[]> getStringArrayList(String fileLocation) throws Exception {
		List<String[]> stringArrayList = new ArrayList<String[]>();
		String[] stringArray = null;
		List<String> dataList = FileUtils.readLines(new File(fileLocation), IConstants.UTF_8);
		for (String dataString : dataList) {
			stringArray = dataString.split(IConstants.TAB);
			//System.out.println("getStringArrayList() stringArray[0]="+stringArray[0]+",stringArray[2]="+stringArray[2]);
			stringArrayList.add(stringArray);
		}
		return stringArrayList;
	}

	private Map<Integer, String> getDomainIdTestQueueNameMap() {
		Map<Integer, String> domainIdTestQueueNameMap = new HashMap<Integer, String>();
		domainIdTestQueueNameMap.put(16, "TEST_TARGET_URL_HTML_EN_16");
		domainIdTestQueueNameMap.put(185, "TEST_TARGET_URL_HTML_EN_185");
		domainIdTestQueueNameMap.put(212, "TEST_TARGET_URL_HTML_EN_212");
		domainIdTestQueueNameMap.put(220, "TEST_TARGET_URL_HTML_EN_220");
		domainIdTestQueueNameMap.put(475, "TEST_TARGET_URL_HTML_EN_475");
		domainIdTestQueueNameMap.put(476, "TEST_TARGET_URL_HTML_EN_476");
		domainIdTestQueueNameMap.put(564, "TEST_TARGET_URL_HTML_EN_564");
		domainIdTestQueueNameMap.put(568, "TEST_TARGET_URL_HTML_EN_568");
		domainIdTestQueueNameMap.put(569, "TEST_TARGET_URL_HTML_EN_569");
		domainIdTestQueueNameMap.put(570, "TEST_TARGET_URL_HTML_EN_570");
		domainIdTestQueueNameMap.put(571, "TEST_TARGET_URL_HTML_EN_571");
		domainIdTestQueueNameMap.put(591, "TEST_TARGET_URL_HTML_EN_591");
		domainIdTestQueueNameMap.put(615, "TEST_TARGET_URL_HTML_EN_615");
		domainIdTestQueueNameMap.put(635, "TEST_TARGET_URL_HTML_EN_635");
		domainIdTestQueueNameMap.put(658, "TEST_TARGET_URL_HTML_ZH-TW_658");
		domainIdTestQueueNameMap.put(659, "TEST_TARGET_URL_HTML_ZH_659");
		domainIdTestQueueNameMap.put(765, "TEST_TARGET_URL_HTML_EN_765");
		domainIdTestQueueNameMap.put(820, "TEST_TARGET_URL_HTML_EN_820");
		domainIdTestQueueNameMap.put(884, "TEST_TARGET_URL_HTML_PT_884");
		domainIdTestQueueNameMap.put(970, "TEST_TARGET_URL_HTML_EN_970");
		domainIdTestQueueNameMap.put(1053, "TEST_TARGET_URL_HTML_EN_1053");
		domainIdTestQueueNameMap.put(1054, "TEST_TARGET_URL_HTML_EN_1054");
		domainIdTestQueueNameMap.put(1126, "TEST_TARGET_URL_HTML_EN_1126");
		domainIdTestQueueNameMap.put(1241, "TEST_TARGET_URL_HTML_EN_1241");
		domainIdTestQueueNameMap.put(1295, "TEST_TARGET_URL_HTML_EN_1295");
		domainIdTestQueueNameMap.put(1310, "TEST_TARGET_URL_HTML_EN_1310");
		domainIdTestQueueNameMap.put(1313, "TEST_TARGET_URL_HTML_EN_1313");
		domainIdTestQueueNameMap.put(1354, "TEST_TARGET_URL_HTML_EN_1354");
		domainIdTestQueueNameMap.put(1391, "TEST_TARGET_URL_HTML_EN_1391");
		domainIdTestQueueNameMap.put(1407, "TEST_TARGET_URL_HTML_EN_1407");
		domainIdTestQueueNameMap.put(1498, "TEST_TARGET_URL_HTML_EN_1498");
		domainIdTestQueueNameMap.put(1508, "TEST_TARGET_URL_HTML_JA_1508");
		domainIdTestQueueNameMap.put(1611, "TEST_TARGET_URL_HTML_EN_1611");
		domainIdTestQueueNameMap.put(1614, "TEST_TARGET_URL_HTML_JA_1614");
		domainIdTestQueueNameMap.put(1632, "TEST_TARGET_URL_HTML_JA_1632");
		domainIdTestQueueNameMap.put(1701, "TEST_TARGET_URL_HTML_EN_1701");
		domainIdTestQueueNameMap.put(1831, "TEST_TARGET_URL_HTML_EN_1831");
		domainIdTestQueueNameMap.put(1839, "TEST_TARGET_URL_HTML_EN_1839");
		domainIdTestQueueNameMap.put(1845, "TEST_TARGET_URL_HTML_DE_1845");
		domainIdTestQueueNameMap.put(1846, "TEST_TARGET_URL_HTML_ES_1846");
		domainIdTestQueueNameMap.put(1847, "TEST_TARGET_URL_HTML_IT_1847");
		domainIdTestQueueNameMap.put(1848, "TEST_TARGET_URL_HTML_FR_1848");
		domainIdTestQueueNameMap.put(1849, "TEST_TARGET_URL_HTML_EN_1849");
		domainIdTestQueueNameMap.put(1852, "TEST_TARGET_URL_HTML_EN_1852");
		domainIdTestQueueNameMap.put(1860, "TEST_TARGET_URL_HTML_JA_1860");
		domainIdTestQueueNameMap.put(1884, "TEST_TARGET_URL_HTML_EN_1884");
		domainIdTestQueueNameMap.put(1895, "TEST_TARGET_URL_HTML_JA_1895");
		domainIdTestQueueNameMap.put(1897, "TEST_TARGET_URL_HTML_EN_1897");
		domainIdTestQueueNameMap.put(1901, "TEST_TARGET_URL_HTML_PT_1901");
		domainIdTestQueueNameMap.put(1928, "TEST_TARGET_URL_HTML_JA_1928");
		domainIdTestQueueNameMap.put(1936, "TEST_TARGET_URL_HTML_EN_1936");
		domainIdTestQueueNameMap.put(1978, "TEST_TARGET_URL_HTML_EN_1978");
		domainIdTestQueueNameMap.put(1979, "TEST_TARGET_URL_HTML_DE_1979");
		domainIdTestQueueNameMap.put(1980, "TEST_TARGET_URL_HTML_FR_1980");
		domainIdTestQueueNameMap.put(1981, "TEST_TARGET_URL_HTML_IT_1981");
		domainIdTestQueueNameMap.put(1982, "TEST_TARGET_URL_HTML_ES_1982");
		domainIdTestQueueNameMap.put(1983, "TEST_TARGET_URL_HTML_DE_1983");
		domainIdTestQueueNameMap.put(1984, "TEST_TARGET_URL_HTML_JA_1984");
		domainIdTestQueueNameMap.put(2026, "TEST_TARGET_URL_HTML_ZH-TW_2026");
		domainIdTestQueueNameMap.put(2028, "TEST_TARGET_URL_HTML_ZH-TW_2028");
		domainIdTestQueueNameMap.put(2047, "TEST_TARGET_URL_HTML_EN_2047");
		domainIdTestQueueNameMap.put(2082, "TEST_TARGET_URL_HTML_EN_2082");
		domainIdTestQueueNameMap.put(2119, "TEST_TARGET_URL_HTML_EN_2119");
		domainIdTestQueueNameMap.put(2121, "TEST_TARGET_URL_HTML_EN_2121");
		domainIdTestQueueNameMap.put(2137, "TEST_TARGET_URL_HTML_EN_2137");
		domainIdTestQueueNameMap.put(2143, "TEST_TARGET_URL_HTML_JA_2143");
		domainIdTestQueueNameMap.put(2169, "TEST_TARGET_URL_HTML_EN_2169");
		domainIdTestQueueNameMap.put(2172, "TEST_TARGET_URL_HTML_EN_2172");
		domainIdTestQueueNameMap.put(2175, "TEST_TARGET_URL_HTML_EN_2175");
		domainIdTestQueueNameMap.put(2176, "TEST_TARGET_URL_HTML_EN_2176");
		domainIdTestQueueNameMap.put(2177, "TEST_TARGET_URL_HTML_EN_2177");
		domainIdTestQueueNameMap.put(2180, "TEST_TARGET_URL_HTML_EN_2180");
		domainIdTestQueueNameMap.put(2181, "TEST_TARGET_URL_HTML_EN_2181");
		domainIdTestQueueNameMap.put(2185, "TEST_TARGET_URL_HTML_EN_2185");
		domainIdTestQueueNameMap.put(2186, "TEST_TARGET_URL_HTML_EN_2186");
		domainIdTestQueueNameMap.put(2188, "TEST_TARGET_URL_HTML_EN_2188");
		domainIdTestQueueNameMap.put(2189, "TEST_TARGET_URL_HTML_EN_2189");
		domainIdTestQueueNameMap.put(2190, "TEST_TARGET_URL_HTML_EN_2190");
		domainIdTestQueueNameMap.put(2191, "TEST_TARGET_URL_HTML_EN_2191");
		domainIdTestQueueNameMap.put(2193, "TEST_TARGET_URL_HTML_EN_2193");
		domainIdTestQueueNameMap.put(2194, "TEST_TARGET_URL_HTML_EN_2194");
		domainIdTestQueueNameMap.put(2196, "TEST_TARGET_URL_HTML_EN_2196");
		domainIdTestQueueNameMap.put(2197, "TEST_TARGET_URL_HTML_EN_2197");
		domainIdTestQueueNameMap.put(2198, "TEST_TARGET_URL_HTML_EN_2198");
		domainIdTestQueueNameMap.put(2199, "TEST_TARGET_URL_HTML_EN_2199");
		domainIdTestQueueNameMap.put(2200, "TEST_TARGET_URL_HTML_EN_2200");
		domainIdTestQueueNameMap.put(2201, "TEST_TARGET_URL_HTML_EN_2201");
		domainIdTestQueueNameMap.put(2202, "TEST_TARGET_URL_HTML_EN_2202");
		domainIdTestQueueNameMap.put(2204, "TEST_TARGET_URL_HTML_EN_2204");
		domainIdTestQueueNameMap.put(2205, "TEST_TARGET_URL_HTML_EN_2205");
		domainIdTestQueueNameMap.put(2208, "TEST_TARGET_URL_HTML_EN_2208");
		domainIdTestQueueNameMap.put(2209, "TEST_TARGET_URL_HTML_EN_2209");
		domainIdTestQueueNameMap.put(2210, "TEST_TARGET_URL_HTML_EN_2210");
		domainIdTestQueueNameMap.put(2211, "TEST_TARGET_URL_HTML_EN_2211");
		domainIdTestQueueNameMap.put(2212, "TEST_TARGET_URL_HTML_EN_2212");
		domainIdTestQueueNameMap.put(2213, "TEST_TARGET_URL_HTML_EN_2213");
		domainIdTestQueueNameMap.put(2214, "TEST_TARGET_URL_HTML_EN_2214");
		domainIdTestQueueNameMap.put(2216, "TEST_TARGET_URL_HTML_EN_2216");
		domainIdTestQueueNameMap.put(2217, "TEST_TARGET_URL_HTML_EN_2217");
		domainIdTestQueueNameMap.put(2219, "TEST_TARGET_URL_HTML_EN_2219");
		domainIdTestQueueNameMap.put(2221, "TEST_TARGET_URL_HTML_EN_2221");
		domainIdTestQueueNameMap.put(2223, "TEST_TARGET_URL_HTML_EN_2223");
		domainIdTestQueueNameMap.put(2224, "TEST_TARGET_URL_HTML_EN_2224");
		domainIdTestQueueNameMap.put(2226, "TEST_TARGET_URL_HTML_EN_2226");
		domainIdTestQueueNameMap.put(2233, "TEST_TARGET_URL_HTML_JA_2233");
		domainIdTestQueueNameMap.put(2234, "TEST_TARGET_URL_HTML_JA_2234");
		domainIdTestQueueNameMap.put(2270, "TEST_TARGET_URL_HTML_EN_2270");
		domainIdTestQueueNameMap.put(2287, "TEST_TARGET_URL_HTML_EN_2287");
		domainIdTestQueueNameMap.put(2299, "TEST_TARGET_URL_HTML_EN_2299");
		domainIdTestQueueNameMap.put(2317, "TEST_TARGET_URL_HTML_EN_2317");
		domainIdTestQueueNameMap.put(2323, "TEST_TARGET_URL_HTML_EN_2323");
		domainIdTestQueueNameMap.put(2325, "TEST_TARGET_URL_HTML_EN_2325");
		domainIdTestQueueNameMap.put(2329, "TEST_TARGET_URL_HTML_EN_2329");
		domainIdTestQueueNameMap.put(2338, "TEST_TARGET_URL_HTML_EN_2338");
		domainIdTestQueueNameMap.put(2339, "TEST_TARGET_URL_HTML_EN_2339");
		domainIdTestQueueNameMap.put(2341, "TEST_TARGET_URL_HTML_EN_2341");
		domainIdTestQueueNameMap.put(2342, "TEST_TARGET_URL_HTML_EN_2342");
		domainIdTestQueueNameMap.put(2349, "TEST_TARGET_URL_HTML_EN_2349");
		domainIdTestQueueNameMap.put(2379, "TEST_TARGET_URL_HTML_EN_2379");
		domainIdTestQueueNameMap.put(2394, "TEST_TARGET_URL_HTML_EN_2394");
		domainIdTestQueueNameMap.put(2420, "TEST_TARGET_URL_HTML_EN_2420");
		domainIdTestQueueNameMap.put(2424, "TEST_TARGET_URL_HTML_EN_2424");
		domainIdTestQueueNameMap.put(2432, "TEST_TARGET_URL_HTML_EN_2432");
		domainIdTestQueueNameMap.put(2466, "TEST_TARGET_URL_HTML_EN_2466");
		domainIdTestQueueNameMap.put(2481, "TEST_TARGET_URL_HTML_PT_2481");
		domainIdTestQueueNameMap.put(2486, "TEST_TARGET_URL_HTML_FR_2486");
		domainIdTestQueueNameMap.put(2487, "TEST_TARGET_URL_HTML_EN_2487");
		domainIdTestQueueNameMap.put(2493, "TEST_TARGET_URL_HTML_DE_2493");
		domainIdTestQueueNameMap.put(2494, "TEST_TARGET_URL_HTML_DE_2494");
		domainIdTestQueueNameMap.put(2495, "TEST_TARGET_URL_HTML_DE_2495");
		domainIdTestQueueNameMap.put(2498, "TEST_TARGET_URL_HTML_ES_2498");
		domainIdTestQueueNameMap.put(2502, "TEST_TARGET_URL_HTML_ES_2502");
		domainIdTestQueueNameMap.put(2503, "TEST_TARGET_URL_HTML_ES_2503");
		domainIdTestQueueNameMap.put(2504, "TEST_TARGET_URL_HTML_ES_2504");
		domainIdTestQueueNameMap.put(2505, "TEST_TARGET_URL_HTML_ES_2505");
		domainIdTestQueueNameMap.put(2506, "TEST_TARGET_URL_HTML_ES_2506");
		domainIdTestQueueNameMap.put(2508, "TEST_TARGET_URL_HTML_EN_2508");
		domainIdTestQueueNameMap.put(2509, "TEST_TARGET_URL_HTML_IT_2509");
		domainIdTestQueueNameMap.put(2510, "TEST_TARGET_URL_HTML_IT_2510");
		domainIdTestQueueNameMap.put(2528, "TEST_TARGET_URL_HTML_SV_2528");
		domainIdTestQueueNameMap.put(2529, "TEST_TARGET_URL_HTML_SV_2529");
		domainIdTestQueueNameMap.put(2534, "TEST_TARGET_URL_HTML_EN_2534");
		domainIdTestQueueNameMap.put(2536, "TEST_TARGET_URL_HTML_EN_2536");
		domainIdTestQueueNameMap.put(2538, "TEST_TARGET_URL_HTML_EN_2538");
		domainIdTestQueueNameMap.put(2541, "TEST_TARGET_URL_HTML_EN_2541");
		domainIdTestQueueNameMap.put(2548, "TEST_TARGET_URL_HTML_EN_2548");
		domainIdTestQueueNameMap.put(2553, "TEST_TARGET_URL_HTML_EN_2553");
		domainIdTestQueueNameMap.put(2554, "TEST_TARGET_URL_HTML_EN_2554");
		domainIdTestQueueNameMap.put(2567, "TEST_TARGET_URL_HTML_JA_2567");
		domainIdTestQueueNameMap.put(2623, "TEST_TARGET_URL_HTML_EN_2623");
		domainIdTestQueueNameMap.put(2632, "TEST_TARGET_URL_HTML_ES_2632");
		domainIdTestQueueNameMap.put(2654, "TEST_TARGET_URL_HTML_EN_2654");
		domainIdTestQueueNameMap.put(2657, "TEST_TARGET_URL_HTML_JA_2657");
		domainIdTestQueueNameMap.put(2838, "TEST_TARGET_URL_HTML_EN_2838");
		domainIdTestQueueNameMap.put(3431, "TEST_TARGET_URL_HTML_EN_3431");
		domainIdTestQueueNameMap.put(3458, "TEST_TARGET_URL_HTML_EN_3458");
		domainIdTestQueueNameMap.put(3534, "TEST_TARGET_URL_HTML_EN_3534");
		domainIdTestQueueNameMap.put(3591, "TEST_TARGET_URL_HTML_EN_3591");
		domainIdTestQueueNameMap.put(3595, "TEST_TARGET_URL_HTML_EN_3595");
		domainIdTestQueueNameMap.put(3681, "TEST_TARGET_URL_HTML_EN_3681");
		domainIdTestQueueNameMap.put(3683, "TEST_TARGET_URL_HTML_ES_3683");
		domainIdTestQueueNameMap.put(3763, "TEST_TARGET_URL_HTML_DA_3763");
		domainIdTestQueueNameMap.put(3764, "TEST_TARGET_URL_HTML_DA_3764");
		domainIdTestQueueNameMap.put(3765, "TEST_TARGET_URL_HTML_DA_3765");
		domainIdTestQueueNameMap.put(3766, "TEST_TARGET_URL_HTML_DA_3766");
		domainIdTestQueueNameMap.put(3845, "TEST_TARGET_URL_HTML_JA_3845");
		domainIdTestQueueNameMap.put(3898, "TEST_TARGET_URL_HTML_JA_3898");
		domainIdTestQueueNameMap.put(3903, "TEST_TARGET_URL_HTML_JA_3903");
		domainIdTestQueueNameMap.put(3904, "TEST_TARGET_URL_HTML_JA_3904");
		domainIdTestQueueNameMap.put(3909, "TEST_TARGET_URL_HTML_EN_3909");
		domainIdTestQueueNameMap.put(3976, "TEST_TARGET_URL_HTML_EN_3976");
		domainIdTestQueueNameMap.put(4005, "TEST_TARGET_URL_HTML_EN_4005");
		domainIdTestQueueNameMap.put(4006, "TEST_TARGET_URL_HTML_PT_4006");
		domainIdTestQueueNameMap.put(4007, "TEST_TARGET_URL_HTML_FR_4007");
		domainIdTestQueueNameMap.put(4008, "TEST_TARGET_URL_HTML_EN_4008");
		domainIdTestQueueNameMap.put(4026, "TEST_TARGET_URL_HTML_EN_4026");
		domainIdTestQueueNameMap.put(4071, "TEST_TARGET_URL_HTML_EN_4071");
		domainIdTestQueueNameMap.put(4327, "TEST_TARGET_URL_HTML_IT_4327");
		domainIdTestQueueNameMap.put(4328, "TEST_TARGET_URL_HTML_EN_4328");
		domainIdTestQueueNameMap.put(4344, "TEST_TARGET_URL_HTML_DE_4344");
		domainIdTestQueueNameMap.put(4345, "TEST_TARGET_URL_HTML_ES_4345");
		domainIdTestQueueNameMap.put(4346, "TEST_TARGET_URL_HTML_IT_4346");
		domainIdTestQueueNameMap.put(4347, "TEST_TARGET_URL_HTML_FR_4347");
		domainIdTestQueueNameMap.put(4348, "TEST_TARGET_URL_HTML_EN_4348");
		domainIdTestQueueNameMap.put(4354, "TEST_TARGET_URL_HTML_EN_4354");
		domainIdTestQueueNameMap.put(4355, "TEST_TARGET_URL_HTML_EN_4355");
		domainIdTestQueueNameMap.put(4392, "TEST_TARGET_URL_HTML_EN_4392");
		domainIdTestQueueNameMap.put(4398, "TEST_TARGET_URL_HTML_EN_4398");
		domainIdTestQueueNameMap.put(4399, "TEST_TARGET_URL_HTML_EN_4399");
		domainIdTestQueueNameMap.put(4404, "TEST_TARGET_URL_HTML_EN_4404");
		domainIdTestQueueNameMap.put(4405, "TEST_TARGET_URL_HTML_EN_4405");
		domainIdTestQueueNameMap.put(4408, "TEST_TARGET_URL_HTML_SV_4408");
		domainIdTestQueueNameMap.put(4409, "TEST_TARGET_URL_HTML_EN_4409");
		domainIdTestQueueNameMap.put(4413, "TEST_TARGET_URL_HTML_EN_4413");
		domainIdTestQueueNameMap.put(4417, "TEST_TARGET_URL_HTML_EN_4417");
		domainIdTestQueueNameMap.put(4419, "TEST_TARGET_URL_HTML_ES_4419");
		domainIdTestQueueNameMap.put(4425, "TEST_TARGET_URL_HTML_PT_4425");
		domainIdTestQueueNameMap.put(4512, "TEST_TARGET_URL_HTML_EN_4512");
		domainIdTestQueueNameMap.put(4514, "TEST_TARGET_URL_HTML_EN_4514");
		domainIdTestQueueNameMap.put(4533, "TEST_TARGET_URL_HTML_JA_4533");
		domainIdTestQueueNameMap.put(4609, "TEST_TARGET_URL_HTML_EN_4609");
		domainIdTestQueueNameMap.put(4662, "TEST_TARGET_URL_HTML_JA_4662");
		domainIdTestQueueNameMap.put(4663, "TEST_TARGET_URL_HTML_DE_4663");
		domainIdTestQueueNameMap.put(4665, "TEST_TARGET_URL_HTML_EN_4665");
		domainIdTestQueueNameMap.put(4712, "TEST_TARGET_URL_HTML_JA_4712");
		domainIdTestQueueNameMap.put(4721, "TEST_TARGET_URL_HTML_JA_4721");
		domainIdTestQueueNameMap.put(4726, "TEST_TARGET_URL_HTML_EN_4726");
		domainIdTestQueueNameMap.put(4766, "TEST_TARGET_URL_HTML_EN_4766");
		domainIdTestQueueNameMap.put(5064, "TEST_TARGET_URL_HTML_EN_5064");
		domainIdTestQueueNameMap.put(5072, "TEST_TARGET_URL_HTML_EN_5072");
		domainIdTestQueueNameMap.put(5078, "TEST_TARGET_URL_HTML_EN_5078");
		domainIdTestQueueNameMap.put(5088, "TEST_TARGET_URL_HTML_EN_5088");
		domainIdTestQueueNameMap.put(5089, "TEST_TARGET_URL_HTML_EN_5089");
		domainIdTestQueueNameMap.put(5090, "TEST_TARGET_URL_HTML_EN_5090");
		domainIdTestQueueNameMap.put(5129, "TEST_TARGET_URL_HTML_JA_5129");
		domainIdTestQueueNameMap.put(5151, "TEST_TARGET_URL_HTML_EN_5151");
		domainIdTestQueueNameMap.put(5186, "TEST_TARGET_URL_HTML_RO_5186");
		domainIdTestQueueNameMap.put(5187, "TEST_TARGET_URL_HTML_RO_5187");
		domainIdTestQueueNameMap.put(5195, "TEST_TARGET_URL_HTML_DE_5195");
		domainIdTestQueueNameMap.put(5206, "TEST_TARGET_URL_HTML_EN_5206");
		domainIdTestQueueNameMap.put(5255, "TEST_TARGET_URL_HTML_EN_5255");
		domainIdTestQueueNameMap.put(5303, "TEST_TARGET_URL_HTML_JA_5303");
		domainIdTestQueueNameMap.put(5313, "TEST_TARGET_URL_HTML_DE_5313");
		domainIdTestQueueNameMap.put(5367, "TEST_TARGET_URL_HTML_EN_5367");
		domainIdTestQueueNameMap.put(5374, "TEST_TARGET_URL_HTML_JA_5374");
		domainIdTestQueueNameMap.put(5375, "TEST_TARGET_URL_HTML_EN_5375");
		domainIdTestQueueNameMap.put(5427, "TEST_TARGET_URL_HTML_JA_5427");
		domainIdTestQueueNameMap.put(5428, "TEST_TARGET_URL_HTML_JA_5428");
		domainIdTestQueueNameMap.put(5444, "TEST_TARGET_URL_HTML_EN_5444");
		domainIdTestQueueNameMap.put(5457, "TEST_TARGET_URL_HTML_EN_5457");
		domainIdTestQueueNameMap.put(5461, "TEST_TARGET_URL_HTML_JA_5461");
		domainIdTestQueueNameMap.put(5468, "TEST_TARGET_URL_HTML_EN_5468");
		domainIdTestQueueNameMap.put(5484, "TEST_TARGET_URL_HTML_JA_5484");
		domainIdTestQueueNameMap.put(5538, "TEST_TARGET_URL_HTML_KO_5538");
		domainIdTestQueueNameMap.put(5556, "TEST_TARGET_URL_HTML_EN_5556");
		domainIdTestQueueNameMap.put(5559, "TEST_TARGET_URL_HTML_EN_5559");
		domainIdTestQueueNameMap.put(5563, "TEST_TARGET_URL_HTML_JA_5563");
		domainIdTestQueueNameMap.put(5566, "TEST_TARGET_URL_HTML_EN_5566");
		domainIdTestQueueNameMap.put(5647, "TEST_TARGET_URL_HTML_JA_5647");
		domainIdTestQueueNameMap.put(5671, "TEST_TARGET_URL_HTML_EN_5671");
		domainIdTestQueueNameMap.put(5682, "TEST_TARGET_URL_HTML_EN_5682");
		domainIdTestQueueNameMap.put(5719, "TEST_TARGET_URL_HTML_JA_5719");
		domainIdTestQueueNameMap.put(5720, "TEST_TARGET_URL_HTML_JA_5720");
		domainIdTestQueueNameMap.put(5723, "TEST_TARGET_URL_HTML_JA_5723");
		domainIdTestQueueNameMap.put(5725, "TEST_TARGET_URL_HTML_EN_5725");
		domainIdTestQueueNameMap.put(5732, "TEST_TARGET_URL_HTML_EN_5732");
		domainIdTestQueueNameMap.put(5739, "TEST_TARGET_URL_HTML_EN_5739");
		domainIdTestQueueNameMap.put(5740, "TEST_TARGET_URL_HTML_EN_5740");
		domainIdTestQueueNameMap.put(5741, "TEST_TARGET_URL_HTML_DE_5741");
		domainIdTestQueueNameMap.put(5742, "TEST_TARGET_URL_HTML_EN_5742");
		domainIdTestQueueNameMap.put(5743, "TEST_TARGET_URL_HTML_NL_5743");
		domainIdTestQueueNameMap.put(5744, "TEST_TARGET_URL_HTML_PT_5744");
		domainIdTestQueueNameMap.put(5745, "TEST_TARGET_URL_HTML_EN_5745");
		domainIdTestQueueNameMap.put(5746, "TEST_TARGET_URL_HTML_FR_5746");
		domainIdTestQueueNameMap.put(5747, "TEST_TARGET_URL_HTML_DE_5747");
		domainIdTestQueueNameMap.put(5748, "TEST_TARGET_URL_HTML_ZH_5748");
		domainIdTestQueueNameMap.put(5749, "TEST_TARGET_URL_HTML_CS_5749");
		domainIdTestQueueNameMap.put(5750, "TEST_TARGET_URL_HTML_DE_5750");
		domainIdTestQueueNameMap.put(5751, "TEST_TARGET_URL_HTML_DA_5751");
		domainIdTestQueueNameMap.put(5752, "TEST_TARGET_URL_HTML_ES_5752");
		domainIdTestQueueNameMap.put(5753, "TEST_TARGET_URL_HTML_FI_5753");
		domainIdTestQueueNameMap.put(5754, "TEST_TARGET_URL_HTML_FR_5754");
		domainIdTestQueueNameMap.put(5755, "TEST_TARGET_URL_HTML_ZH_5755");
		domainIdTestQueueNameMap.put(5756, "TEST_TARGET_URL_HTML_HU_5756");
		domainIdTestQueueNameMap.put(5757, "TEST_TARGET_URL_HTML_EN_5757");
		domainIdTestQueueNameMap.put(5758, "TEST_TARGET_URL_HTML_EN_5758");
		domainIdTestQueueNameMap.put(5759, "TEST_TARGET_URL_HTML_IT_5759");
		domainIdTestQueueNameMap.put(5760, "TEST_TARGET_URL_HTML_JA_5760");
		domainIdTestQueueNameMap.put(5761, "TEST_TARGET_URL_HTML_KO_5761");
		domainIdTestQueueNameMap.put(5762, "TEST_TARGET_URL_HTML_ES_5762");
		domainIdTestQueueNameMap.put(5763, "TEST_TARGET_URL_HTML_EN_5763");
		domainIdTestQueueNameMap.put(5764, "TEST_TARGET_URL_HTML_NL_5764");
		domainIdTestQueueNameMap.put(5765, "TEST_TARGET_URL_HTML_NO_5765");
		domainIdTestQueueNameMap.put(5766, "TEST_TARGET_URL_HTML_EN_5766");
		domainIdTestQueueNameMap.put(5767, "TEST_TARGET_URL_HTML_EN_5767");
		domainIdTestQueueNameMap.put(5768, "TEST_TARGET_URL_HTML_PL_5768");
		domainIdTestQueueNameMap.put(5769, "TEST_TARGET_URL_HTML_PT_5769");
		domainIdTestQueueNameMap.put(5770, "TEST_TARGET_URL_HTML_RU_5770");
		domainIdTestQueueNameMap.put(5771, "TEST_TARGET_URL_HTML_SV_5771");
		domainIdTestQueueNameMap.put(5772, "TEST_TARGET_URL_HTML_EN_5772");
		domainIdTestQueueNameMap.put(5773, "TEST_TARGET_URL_HTML_TH_5773");
		domainIdTestQueueNameMap.put(5774, "TEST_TARGET_URL_HTML_TR_5774");
		domainIdTestQueueNameMap.put(5775, "TEST_TARGET_URL_HTML_ZH-TW_5775");
		domainIdTestQueueNameMap.put(5776, "TEST_TARGET_URL_HTML_EN_5776");
		domainIdTestQueueNameMap.put(5781, "TEST_TARGET_URL_HTML_EN_5781");
		domainIdTestQueueNameMap.put(5784, "TEST_TARGET_URL_HTML_FR_5784");
		domainIdTestQueueNameMap.put(5785, "TEST_TARGET_URL_HTML_FR_5785");
		domainIdTestQueueNameMap.put(5786, "TEST_TARGET_URL_HTML_EN_5786");
		domainIdTestQueueNameMap.put(5787, "TEST_TARGET_URL_HTML_EN_5787");
		domainIdTestQueueNameMap.put(5792, "TEST_TARGET_URL_HTML_JA_5792");
		domainIdTestQueueNameMap.put(5794, "TEST_TARGET_URL_HTML_JA_5794");
		domainIdTestQueueNameMap.put(5806, "TEST_TARGET_URL_HTML_EN_5806");
		domainIdTestQueueNameMap.put(5824, "TEST_TARGET_URL_HTML_JA_5824");
		domainIdTestQueueNameMap.put(5843, "TEST_TARGET_URL_HTML_FR_5843");
		domainIdTestQueueNameMap.put(5867, "TEST_TARGET_URL_HTML_EN_5867");
		domainIdTestQueueNameMap.put(5903, "TEST_TARGET_URL_HTML_JA_5903");
		domainIdTestQueueNameMap.put(5906, "TEST_TARGET_URL_HTML_JA_5906");
		domainIdTestQueueNameMap.put(5914, "TEST_TARGET_URL_HTML_EN_5914");
		domainIdTestQueueNameMap.put(5916, "TEST_TARGET_URL_HTML_EN_5916");
		domainIdTestQueueNameMap.put(5920, "TEST_TARGET_URL_HTML_JA_5920");
		domainIdTestQueueNameMap.put(5921, "TEST_TARGET_URL_HTML_JA_5921");
		domainIdTestQueueNameMap.put(5922, "TEST_TARGET_URL_HTML_JA_5922");
		domainIdTestQueueNameMap.put(5936, "TEST_TARGET_URL_HTML_EN_5936");
		domainIdTestQueueNameMap.put(5942, "TEST_TARGET_URL_HTML_EN_5942");
		domainIdTestQueueNameMap.put(5943, "TEST_TARGET_URL_HTML_EN_5943");
		domainIdTestQueueNameMap.put(5944, "TEST_TARGET_URL_HTML_JA_5944");
		domainIdTestQueueNameMap.put(5945, "TEST_TARGET_URL_HTML_JA_5945");
		domainIdTestQueueNameMap.put(5983, "TEST_TARGET_URL_HTML_JA_5983");
		domainIdTestQueueNameMap.put(5994, "TEST_TARGET_URL_HTML_EN_5994");
		domainIdTestQueueNameMap.put(6041, "TEST_TARGET_URL_HTML_EN_6041");
		domainIdTestQueueNameMap.put(6045, "TEST_TARGET_URL_HTML_EN_6045");
		domainIdTestQueueNameMap.put(6059, "TEST_TARGET_URL_HTML_EN_6059");
		domainIdTestQueueNameMap.put(6077, "TEST_TARGET_URL_HTML_EN_6077");
		domainIdTestQueueNameMap.put(6082, "TEST_TARGET_URL_HTML_EN_6082");
		domainIdTestQueueNameMap.put(6101, "TEST_TARGET_URL_HTML_EN_6101");
		domainIdTestQueueNameMap.put(6102, "TEST_TARGET_URL_HTML_EN_6102");
		domainIdTestQueueNameMap.put(6111, "TEST_TARGET_URL_HTML_JA_6111");
		domainIdTestQueueNameMap.put(6122, "TEST_TARGET_URL_HTML_EN_6122");
		domainIdTestQueueNameMap.put(6127, "TEST_TARGET_URL_HTML_EN_6127");
		domainIdTestQueueNameMap.put(6129, "TEST_TARGET_URL_HTML_EN_6129");
		domainIdTestQueueNameMap.put(6132, "TEST_TARGET_URL_HTML_EN_6132");
		domainIdTestQueueNameMap.put(6133, "TEST_TARGET_URL_HTML_EN_6133");
		domainIdTestQueueNameMap.put(6139, "TEST_TARGET_URL_HTML_EN_6139");
		domainIdTestQueueNameMap.put(6142, "TEST_TARGET_URL_HTML_EN_6142");
		domainIdTestQueueNameMap.put(6143, "TEST_TARGET_URL_HTML_EN_6143");
		domainIdTestQueueNameMap.put(6145, "TEST_TARGET_URL_HTML_EN_6145");
		domainIdTestQueueNameMap.put(6146, "TEST_TARGET_URL_HTML_EN_6146");
		domainIdTestQueueNameMap.put(6149, "TEST_TARGET_URL_HTML_EN_6149");
		domainIdTestQueueNameMap.put(6150, "TEST_TARGET_URL_HTML_EN_6150");
		domainIdTestQueueNameMap.put(6155, "TEST_TARGET_URL_HTML_EN_6155");
		domainIdTestQueueNameMap.put(6159, "TEST_TARGET_URL_HTML_EN_6159");
		domainIdTestQueueNameMap.put(6161, "TEST_TARGET_URL_HTML_EN_6161");
		domainIdTestQueueNameMap.put(6165, "TEST_TARGET_URL_HTML_EN_6165");
		domainIdTestQueueNameMap.put(6168, "TEST_TARGET_URL_HTML_EN_6168");
		domainIdTestQueueNameMap.put(6171, "TEST_TARGET_URL_HTML_EN_6171");
		domainIdTestQueueNameMap.put(6172, "TEST_TARGET_URL_HTML_EN_6172");
		domainIdTestQueueNameMap.put(6174, "TEST_TARGET_URL_HTML_EN_6174");
		domainIdTestQueueNameMap.put(6175, "TEST_TARGET_URL_HTML_EN_6175");
		domainIdTestQueueNameMap.put(6183, "TEST_TARGET_URL_HTML_EN_6183");
		domainIdTestQueueNameMap.put(6185, "TEST_TARGET_URL_HTML_EN_6185");
		domainIdTestQueueNameMap.put(6190, "TEST_TARGET_URL_HTML_EN_6190");
		domainIdTestQueueNameMap.put(6192, "TEST_TARGET_URL_HTML_EN_6192");
		domainIdTestQueueNameMap.put(6194, "TEST_TARGET_URL_HTML_EN_6194");
		domainIdTestQueueNameMap.put(6195, "TEST_TARGET_URL_HTML_EN_6195");
		domainIdTestQueueNameMap.put(6196, "TEST_TARGET_URL_HTML_EN_6196");
		domainIdTestQueueNameMap.put(6199, "TEST_TARGET_URL_HTML_EN_6199");
		domainIdTestQueueNameMap.put(6202, "TEST_TARGET_URL_HTML_EN_6202");
		domainIdTestQueueNameMap.put(6203, "TEST_TARGET_URL_HTML_EN_6203");
		domainIdTestQueueNameMap.put(6206, "TEST_TARGET_URL_HTML_EN_6206");
		domainIdTestQueueNameMap.put(6207, "TEST_TARGET_URL_HTML_EN_6207");
		domainIdTestQueueNameMap.put(6208, "TEST_TARGET_URL_HTML_EN_6208");
		domainIdTestQueueNameMap.put(6212, "TEST_TARGET_URL_HTML_EN_6212");
		domainIdTestQueueNameMap.put(6214, "TEST_TARGET_URL_HTML_EN_6214");
		domainIdTestQueueNameMap.put(6215, "TEST_TARGET_URL_HTML_EN_6215");
		domainIdTestQueueNameMap.put(6218, "TEST_TARGET_URL_HTML_EN_6218");
		domainIdTestQueueNameMap.put(6219, "TEST_TARGET_URL_HTML_EN_6219");
		domainIdTestQueueNameMap.put(6220, "TEST_TARGET_URL_HTML_EN_6220");
		domainIdTestQueueNameMap.put(6223, "TEST_TARGET_URL_HTML_EN_6223");
		domainIdTestQueueNameMap.put(6229, "TEST_TARGET_URL_HTML_EN_6229");
		domainIdTestQueueNameMap.put(6230, "TEST_TARGET_URL_HTML_EN_6230");
		domainIdTestQueueNameMap.put(6233, "TEST_TARGET_URL_HTML_EN_6233");
		domainIdTestQueueNameMap.put(6235, "TEST_TARGET_URL_HTML_EN_6235");
		domainIdTestQueueNameMap.put(6239, "TEST_TARGET_URL_HTML_EN_6239");
		domainIdTestQueueNameMap.put(6243, "TEST_TARGET_URL_HTML_EN_6243");
		domainIdTestQueueNameMap.put(6244, "TEST_TARGET_URL_HTML_EN_6244");
		domainIdTestQueueNameMap.put(6245, "TEST_TARGET_URL_HTML_EN_6245");
		domainIdTestQueueNameMap.put(6246, "TEST_TARGET_URL_HTML_EN_6246");
		domainIdTestQueueNameMap.put(6249, "TEST_TARGET_URL_HTML_EN_6249");
		domainIdTestQueueNameMap.put(6250, "TEST_TARGET_URL_HTML_EN_6250");
		domainIdTestQueueNameMap.put(6261, "TEST_TARGET_URL_HTML_EN_6261");
		domainIdTestQueueNameMap.put(6264, "TEST_TARGET_URL_HTML_EN_6264");
		domainIdTestQueueNameMap.put(6265, "TEST_TARGET_URL_HTML_EN_6265");
		domainIdTestQueueNameMap.put(6266, "TEST_TARGET_URL_HTML_EN_6266");
		domainIdTestQueueNameMap.put(6268, "TEST_TARGET_URL_HTML_EN_6268");
		domainIdTestQueueNameMap.put(6269, "TEST_TARGET_URL_HTML_EN_6269");
		domainIdTestQueueNameMap.put(6270, "TEST_TARGET_URL_HTML_EN_6270");
		domainIdTestQueueNameMap.put(6273, "TEST_TARGET_URL_HTML_EN_6273");
		domainIdTestQueueNameMap.put(6274, "TEST_TARGET_URL_HTML_EN_6274");
		domainIdTestQueueNameMap.put(6275, "TEST_TARGET_URL_HTML_EN_6275");
		domainIdTestQueueNameMap.put(6277, "TEST_TARGET_URL_HTML_EN_6277");
		domainIdTestQueueNameMap.put(6278, "TEST_TARGET_URL_HTML_EN_6278");
		domainIdTestQueueNameMap.put(6279, "TEST_TARGET_URL_HTML_EN_6279");
		domainIdTestQueueNameMap.put(6282, "TEST_TARGET_URL_HTML_EN_6282");
		domainIdTestQueueNameMap.put(6284, "TEST_TARGET_URL_HTML_EN_6284");
		domainIdTestQueueNameMap.put(6288, "TEST_TARGET_URL_HTML_EN_6288");
		domainIdTestQueueNameMap.put(6289, "TEST_TARGET_URL_HTML_EN_6289");
		domainIdTestQueueNameMap.put(6299, "TEST_TARGET_URL_HTML_EN_6299");
		domainIdTestQueueNameMap.put(6303, "TEST_TARGET_URL_HTML_EN_6303");
		domainIdTestQueueNameMap.put(6308, "TEST_TARGET_URL_HTML_EN_6308");
		domainIdTestQueueNameMap.put(6313, "TEST_TARGET_URL_HTML_EN_6313");
		domainIdTestQueueNameMap.put(6314, "TEST_TARGET_URL_HTML_EN_6314");
		domainIdTestQueueNameMap.put(6315, "TEST_TARGET_URL_HTML_EN_6315");
		domainIdTestQueueNameMap.put(6316, "TEST_TARGET_URL_HTML_EN_6316");
		domainIdTestQueueNameMap.put(6317, "TEST_TARGET_URL_HTML_EN_6317");
		domainIdTestQueueNameMap.put(6318, "TEST_TARGET_URL_HTML_EN_6318");
		domainIdTestQueueNameMap.put(6319, "TEST_TARGET_URL_HTML_EN_6319");
		domainIdTestQueueNameMap.put(6320, "TEST_TARGET_URL_HTML_EN_6320");
		domainIdTestQueueNameMap.put(6321, "TEST_TARGET_URL_HTML_EN_6321");
		domainIdTestQueueNameMap.put(6322, "TEST_TARGET_URL_HTML_EN_6322");
		domainIdTestQueueNameMap.put(6323, "TEST_TARGET_URL_HTML_EN_6323");
		domainIdTestQueueNameMap.put(6325, "TEST_TARGET_URL_HTML_EN_6325");
		domainIdTestQueueNameMap.put(6330, "TEST_TARGET_URL_HTML_EN_6330");
		domainIdTestQueueNameMap.put(6331, "TEST_TARGET_URL_HTML_EN_6331");
		domainIdTestQueueNameMap.put(6333, "TEST_TARGET_URL_HTML_EN_6333");
		domainIdTestQueueNameMap.put(6372, "TEST_TARGET_URL_HTML_EN_6372");
		domainIdTestQueueNameMap.put(6379, "TEST_TARGET_URL_HTML_EN_6379");
		domainIdTestQueueNameMap.put(6380, "TEST_TARGET_URL_HTML_EN_6380");
		domainIdTestQueueNameMap.put(6381, "TEST_TARGET_URL_HTML_EN_6381");
		domainIdTestQueueNameMap.put(6382, "TEST_TARGET_URL_HTML_EN_6382");
		domainIdTestQueueNameMap.put(6383, "TEST_TARGET_URL_HTML_EN_6383");
		domainIdTestQueueNameMap.put(6430, "TEST_TARGET_URL_HTML_EN_6430");
		domainIdTestQueueNameMap.put(6435, "TEST_TARGET_URL_HTML_EN_6435");
		domainIdTestQueueNameMap.put(6436, "TEST_TARGET_URL_HTML_EN_6436");
		domainIdTestQueueNameMap.put(6437, "TEST_TARGET_URL_HTML_EN_6437");
		domainIdTestQueueNameMap.put(6440, "TEST_TARGET_URL_HTML_EN_6440");
		domainIdTestQueueNameMap.put(6442, "TEST_TARGET_URL_HTML_IT_6442");
		domainIdTestQueueNameMap.put(6446, "TEST_TARGET_URL_HTML_PT_6446");
		domainIdTestQueueNameMap.put(6447, "TEST_TARGET_URL_HTML_PT_6447");
		domainIdTestQueueNameMap.put(6456, "TEST_TARGET_URL_HTML_JA_6456");
		domainIdTestQueueNameMap.put(6457, "TEST_TARGET_URL_HTML_EN_6457");
		domainIdTestQueueNameMap.put(6497, "TEST_TARGET_URL_HTML_EN_6497");
		domainIdTestQueueNameMap.put(6504, "TEST_TARGET_URL_HTML_EN_6504");
		domainIdTestQueueNameMap.put(6507, "TEST_TARGET_URL_HTML_EN_6507");
		domainIdTestQueueNameMap.put(6508, "TEST_TARGET_URL_HTML_EN_6508");
		domainIdTestQueueNameMap.put(6509, "TEST_TARGET_URL_HTML_EN_6509");
		domainIdTestQueueNameMap.put(6510, "TEST_TARGET_URL_HTML_EN_6510");
		domainIdTestQueueNameMap.put(6511, "TEST_TARGET_URL_HTML_EN_6511");
		domainIdTestQueueNameMap.put(6512, "TEST_TARGET_URL_HTML_EN_6512");
		domainIdTestQueueNameMap.put(6513, "TEST_TARGET_URL_HTML_EN_6513");
		domainIdTestQueueNameMap.put(6514, "TEST_TARGET_URL_HTML_EN_6514");
		domainIdTestQueueNameMap.put(6515, "TEST_TARGET_URL_HTML_EN_6515");
		domainIdTestQueueNameMap.put(6516, "TEST_TARGET_URL_HTML_EN_6516");
		domainIdTestQueueNameMap.put(6517, "TEST_TARGET_URL_HTML_EN_6517");
		domainIdTestQueueNameMap.put(6518, "TEST_TARGET_URL_HTML_EN_6518");
		domainIdTestQueueNameMap.put(6519, "TEST_TARGET_URL_HTML_EN_6519");
		domainIdTestQueueNameMap.put(6520, "TEST_TARGET_URL_HTML_EN_6520");
		domainIdTestQueueNameMap.put(6521, "TEST_TARGET_URL_HTML_EN_6521");
		domainIdTestQueueNameMap.put(6531, "TEST_TARGET_URL_HTML_EN_6531");
		domainIdTestQueueNameMap.put(6588, "TEST_TARGET_URL_HTML_FR_6588");
		domainIdTestQueueNameMap.put(6603, "TEST_TARGET_URL_HTML_EN_6603");
		domainIdTestQueueNameMap.put(6606, "TEST_TARGET_URL_HTML_EN_6606");
		domainIdTestQueueNameMap.put(6607, "TEST_TARGET_URL_HTML_EN_6607");
		domainIdTestQueueNameMap.put(6609, "TEST_TARGET_URL_HTML_EN_6609");
		domainIdTestQueueNameMap.put(6610, "TEST_TARGET_URL_HTML_AR_6610");
		domainIdTestQueueNameMap.put(6611, "TEST_TARGET_URL_HTML_EN_6611");
		domainIdTestQueueNameMap.put(6613, "TEST_TARGET_URL_HTML_EN_6613");
		domainIdTestQueueNameMap.put(6616, "TEST_TARGET_URL_HTML_EN_6616");
		domainIdTestQueueNameMap.put(6617, "TEST_TARGET_URL_HTML_EN_6617");
		domainIdTestQueueNameMap.put(6618, "TEST_TARGET_URL_HTML_EN_6618");
		domainIdTestQueueNameMap.put(6619, "TEST_TARGET_URL_HTML_EN_6619");
		domainIdTestQueueNameMap.put(6620, "TEST_TARGET_URL_HTML_EN_6620");
		domainIdTestQueueNameMap.put(6621, "TEST_TARGET_URL_HTML_DE_6621");
		domainIdTestQueueNameMap.put(6622, "TEST_TARGET_URL_HTML_FR_6622");
		domainIdTestQueueNameMap.put(6623, "TEST_TARGET_URL_HTML_IT_6623");
		domainIdTestQueueNameMap.put(6624, "TEST_TARGET_URL_HTML_ES_6624");
		domainIdTestQueueNameMap.put(6625, "TEST_TARGET_URL_HTML_EN_6625");
		domainIdTestQueueNameMap.put(6626, "TEST_TARGET_URL_HTML_JA_6626");
		domainIdTestQueueNameMap.put(6627, "TEST_TARGET_URL_HTML_EN_6627");
		domainIdTestQueueNameMap.put(6629, "TEST_TARGET_URL_HTML_EN_6629");
		domainIdTestQueueNameMap.put(6631, "TEST_TARGET_URL_HTML_EN_6631");
		domainIdTestQueueNameMap.put(6636, "TEST_TARGET_URL_HTML_EN_6636");
		domainIdTestQueueNameMap.put(6637, "TEST_TARGET_URL_HTML_EN_6637");
		domainIdTestQueueNameMap.put(6638, "TEST_TARGET_URL_HTML_EN_6638");
		domainIdTestQueueNameMap.put(6669, "TEST_TARGET_URL_HTML_ES_6669");
		domainIdTestQueueNameMap.put(6674, "TEST_TARGET_URL_HTML_EN_6674");
		domainIdTestQueueNameMap.put(6675, "TEST_TARGET_URL_HTML_EN_6675");
		domainIdTestQueueNameMap.put(6678, "TEST_TARGET_URL_HTML_EN_6678");
		domainIdTestQueueNameMap.put(6679, "TEST_TARGET_URL_HTML_EN_6679");
		domainIdTestQueueNameMap.put(6687, "TEST_TARGET_URL_HTML_EN_6687");
		domainIdTestQueueNameMap.put(6689, "TEST_TARGET_URL_HTML_EN_6689");
		domainIdTestQueueNameMap.put(6691, "TEST_TARGET_URL_HTML_FR_6691");
		domainIdTestQueueNameMap.put(6693, "TEST_TARGET_URL_HTML_DE_6693");
		domainIdTestQueueNameMap.put(6695, "TEST_TARGET_URL_HTML_ZH_6695");
		domainIdTestQueueNameMap.put(6697, "TEST_TARGET_URL_HTML_JA_6697");
		domainIdTestQueueNameMap.put(6698, "TEST_TARGET_URL_HTML_EN_6698");
		domainIdTestQueueNameMap.put(6700, "TEST_TARGET_URL_HTML_EN_6700");
		domainIdTestQueueNameMap.put(6706, "TEST_TARGET_URL_HTML_EN_6706");
		domainIdTestQueueNameMap.put(6708, "TEST_TARGET_URL_HTML_EN_6708");
		domainIdTestQueueNameMap.put(6771, "TEST_TARGET_URL_HTML_EN_6771");
		domainIdTestQueueNameMap.put(6778, "TEST_TARGET_URL_HTML_EN_6778");
		domainIdTestQueueNameMap.put(6799, "TEST_TARGET_URL_HTML_EN_6799");
		domainIdTestQueueNameMap.put(6818, "TEST_TARGET_URL_HTML_JA_6818");
		domainIdTestQueueNameMap.put(6917, "TEST_TARGET_URL_HTML_EN_6917");
		domainIdTestQueueNameMap.put(6919, "TEST_TARGET_URL_HTML_JA_6919");
		domainIdTestQueueNameMap.put(6920, "TEST_TARGET_URL_HTML_EN_6920");
		domainIdTestQueueNameMap.put(6935, "TEST_TARGET_URL_HTML_EN_6935");
		domainIdTestQueueNameMap.put(6936, "TEST_TARGET_URL_HTML_EN_6936");
		domainIdTestQueueNameMap.put(6969, "TEST_TARGET_URL_HTML_EN_6969");
		domainIdTestQueueNameMap.put(6971, "TEST_TARGET_URL_HTML_EN_6971");
		domainIdTestQueueNameMap.put(6994, "TEST_TARGET_URL_HTML_EN_6994");
		domainIdTestQueueNameMap.put(6995, "TEST_TARGET_URL_HTML_EN_6995");
		domainIdTestQueueNameMap.put(6997, "TEST_TARGET_URL_HTML_EN_6997");
		domainIdTestQueueNameMap.put(6998, "TEST_TARGET_URL_HTML_EN_6998");
		domainIdTestQueueNameMap.put(7004, "TEST_TARGET_URL_HTML_EN_7004");
		domainIdTestQueueNameMap.put(7005, "TEST_TARGET_URL_HTML_EN_7005");
		domainIdTestQueueNameMap.put(7006, "TEST_TARGET_URL_HTML_EN_7006");
		domainIdTestQueueNameMap.put(7013, "TEST_TARGET_URL_HTML_EN_7013");
		domainIdTestQueueNameMap.put(7016, "TEST_TARGET_URL_HTML_EN_7016");
		domainIdTestQueueNameMap.put(7048, "TEST_TARGET_URL_HTML_EN_7048");
		domainIdTestQueueNameMap.put(7049, "TEST_TARGET_URL_HTML_TR_7049");
		domainIdTestQueueNameMap.put(7055, "TEST_TARGET_URL_HTML_EN_7055");
		domainIdTestQueueNameMap.put(7061, "TEST_TARGET_URL_HTML_EN_7061");
		domainIdTestQueueNameMap.put(7073, "TEST_TARGET_URL_HTML_EN_7073");
		domainIdTestQueueNameMap.put(7079, "TEST_TARGET_URL_HTML_EN_7079");
		domainIdTestQueueNameMap.put(7082, "TEST_TARGET_URL_HTML_JA_7082");
		domainIdTestQueueNameMap.put(7121, "TEST_TARGET_URL_HTML_EN_7121");
		domainIdTestQueueNameMap.put(7122, "TEST_TARGET_URL_HTML_EN_7122");
		domainIdTestQueueNameMap.put(7129, "TEST_TARGET_URL_HTML_TR_7129");
		domainIdTestQueueNameMap.put(7136, "TEST_TARGET_URL_HTML_JA_7136");
		domainIdTestQueueNameMap.put(7141, "TEST_TARGET_URL_HTML_EN_7141");
		domainIdTestQueueNameMap.put(7145, "TEST_TARGET_URL_HTML_EN_7145");
		domainIdTestQueueNameMap.put(7148, "TEST_TARGET_URL_HTML_EN_7148");
		domainIdTestQueueNameMap.put(7186, "TEST_TARGET_URL_HTML_EN_7186");
		domainIdTestQueueNameMap.put(7189, "TEST_TARGET_URL_HTML_FR_7189");
		domainIdTestQueueNameMap.put(7193, "TEST_TARGET_URL_HTML_EN_7193");
		domainIdTestQueueNameMap.put(7292, "TEST_TARGET_URL_HTML_EN_7292");
		domainIdTestQueueNameMap.put(7293, "TEST_TARGET_URL_HTML_EN_7293");
		domainIdTestQueueNameMap.put(7300, "TEST_TARGET_URL_HTML_EN_7300");
		domainIdTestQueueNameMap.put(7304, "TEST_TARGET_URL_HTML_EN_7304");
		domainIdTestQueueNameMap.put(7328, "TEST_TARGET_URL_HTML_EN_7328");
		domainIdTestQueueNameMap.put(7401, "TEST_TARGET_URL_HTML_JA_7401");
		domainIdTestQueueNameMap.put(7402, "TEST_TARGET_URL_HTML_JA_7402");
		domainIdTestQueueNameMap.put(7404, "TEST_TARGET_URL_HTML_EN_7404");
		domainIdTestQueueNameMap.put(7407, "TEST_TARGET_URL_HTML_EN_7407");
		domainIdTestQueueNameMap.put(7426, "TEST_TARGET_URL_HTML_EN_7426");
		domainIdTestQueueNameMap.put(7435, "TEST_TARGET_URL_HTML_EN_7435");
		domainIdTestQueueNameMap.put(7436, "TEST_TARGET_URL_HTML_EN_7436");
		domainIdTestQueueNameMap.put(7437, "TEST_TARGET_URL_HTML_EN_7437");
		domainIdTestQueueNameMap.put(7440, "TEST_TARGET_URL_HTML_EN_7440");
		domainIdTestQueueNameMap.put(7441, "TEST_TARGET_URL_HTML_EN_7441");
		domainIdTestQueueNameMap.put(7442, "TEST_TARGET_URL_HTML_EN_7442");
		domainIdTestQueueNameMap.put(7443, "TEST_TARGET_URL_HTML_EN_7443");
		domainIdTestQueueNameMap.put(7444, "TEST_TARGET_URL_HTML_EN_7444");
		domainIdTestQueueNameMap.put(7445, "TEST_TARGET_URL_HTML_EN_7445");
		domainIdTestQueueNameMap.put(7446, "TEST_TARGET_URL_HTML_EN_7446");
		domainIdTestQueueNameMap.put(7447, "TEST_TARGET_URL_HTML_EN_7447");
		domainIdTestQueueNameMap.put(7448, "TEST_TARGET_URL_HTML_EN_7448");
		domainIdTestQueueNameMap.put(7449, "TEST_TARGET_URL_HTML_EN_7449");
		domainIdTestQueueNameMap.put(7451, "TEST_TARGET_URL_HTML_EN_7451");
		domainIdTestQueueNameMap.put(7460, "TEST_TARGET_URL_HTML_EN_7460");
		domainIdTestQueueNameMap.put(7461, "TEST_TARGET_URL_HTML_EN_7461");
		domainIdTestQueueNameMap.put(7465, "TEST_TARGET_URL_HTML_EN_7465");
		domainIdTestQueueNameMap.put(7468, "TEST_TARGET_URL_HTML_JA_7468");
		domainIdTestQueueNameMap.put(7475, "TEST_TARGET_URL_HTML_EN_7475");
		domainIdTestQueueNameMap.put(7478, "TEST_TARGET_URL_HTML_EN_7478");
		domainIdTestQueueNameMap.put(7491, "TEST_TARGET_URL_HTML_EN_7491");
		domainIdTestQueueNameMap.put(7496, "TEST_TARGET_URL_HTML_JA_7496");
		domainIdTestQueueNameMap.put(7505, "TEST_TARGET_URL_HTML_EN_7505");
		domainIdTestQueueNameMap.put(7520, "TEST_TARGET_URL_HTML_EN_7520");
		domainIdTestQueueNameMap.put(7521, "TEST_TARGET_URL_HTML_EN_7521");
		domainIdTestQueueNameMap.put(7523, "TEST_TARGET_URL_HTML_EN_7523");
		domainIdTestQueueNameMap.put(7529, "TEST_TARGET_URL_HTML_EN_7529");
		domainIdTestQueueNameMap.put(7542, "TEST_TARGET_URL_HTML_EN_7542");
		domainIdTestQueueNameMap.put(7543, "TEST_TARGET_URL_HTML_EN_7543");
		domainIdTestQueueNameMap.put(7545, "TEST_TARGET_URL_HTML_EN_7545");
		domainIdTestQueueNameMap.put(7554, "TEST_TARGET_URL_HTML_JA_7554");
		domainIdTestQueueNameMap.put(7560, "TEST_TARGET_URL_HTML_EN_7560");
		domainIdTestQueueNameMap.put(7561, "TEST_TARGET_URL_HTML_EN_7561");
		domainIdTestQueueNameMap.put(7562, "TEST_TARGET_URL_HTML_EN_7562");
		domainIdTestQueueNameMap.put(7573, "TEST_TARGET_URL_HTML_EN_7573");
		domainIdTestQueueNameMap.put(7583, "TEST_TARGET_URL_HTML_EN_7583");
		domainIdTestQueueNameMap.put(7585, "TEST_TARGET_URL_HTML_EN_7585");
		domainIdTestQueueNameMap.put(7595, "TEST_TARGET_URL_HTML_EN_7595");
		domainIdTestQueueNameMap.put(7598, "TEST_TARGET_URL_HTML_EN_7598");
		domainIdTestQueueNameMap.put(7599, "TEST_TARGET_URL_HTML_ZH_7599");
		domainIdTestQueueNameMap.put(7600, "TEST_TARGET_URL_HTML_EN_7600");
		domainIdTestQueueNameMap.put(7601, "TEST_TARGET_URL_HTML_EN_7601");
		domainIdTestQueueNameMap.put(7603, "TEST_TARGET_URL_HTML_EN_7603");
		domainIdTestQueueNameMap.put(7605, "TEST_TARGET_URL_HTML_EN_7605");
		domainIdTestQueueNameMap.put(7607, "TEST_TARGET_URL_HTML_EN_7607");
		domainIdTestQueueNameMap.put(7614, "TEST_TARGET_URL_HTML_EN_7614");
		domainIdTestQueueNameMap.put(7620, "TEST_TARGET_URL_HTML_EN_7620");
		domainIdTestQueueNameMap.put(7650, "TEST_TARGET_URL_HTML_EN_7650");
		domainIdTestQueueNameMap.put(7656, "TEST_TARGET_URL_HTML_EN_7656");
		domainIdTestQueueNameMap.put(7657, "TEST_TARGET_URL_HTML_EN_7657");
		domainIdTestQueueNameMap.put(7658, "TEST_TARGET_URL_HTML_EN_7658");
		domainIdTestQueueNameMap.put(7662, "TEST_TARGET_URL_HTML_EN_7662");
		domainIdTestQueueNameMap.put(7663, "TEST_TARGET_URL_HTML_EN_7663");
		domainIdTestQueueNameMap.put(7680, "TEST_TARGET_URL_HTML_EN_7680");
		domainIdTestQueueNameMap.put(7682, "TEST_TARGET_URL_HTML_EN_7682");
		domainIdTestQueueNameMap.put(7683, "TEST_TARGET_URL_HTML_EN_7683");
		domainIdTestQueueNameMap.put(7689, "TEST_TARGET_URL_HTML_PT_7689");
		domainIdTestQueueNameMap.put(7730, "TEST_TARGET_URL_HTML_EN_7730");
		domainIdTestQueueNameMap.put(7733, "TEST_TARGET_URL_HTML_EN_7733");
		domainIdTestQueueNameMap.put(7741, "TEST_TARGET_URL_HTML_EN_7741");
		domainIdTestQueueNameMap.put(7742, "TEST_TARGET_URL_HTML_AR_7742");
		domainIdTestQueueNameMap.put(7744, "TEST_TARGET_URL_HTML_AR_7744");
		domainIdTestQueueNameMap.put(7745, "TEST_TARGET_URL_HTML_AR_7745");
		domainIdTestQueueNameMap.put(7746, "TEST_TARGET_URL_HTML_AR_7746");
		domainIdTestQueueNameMap.put(7748, "TEST_TARGET_URL_HTML_AR_7748");
		domainIdTestQueueNameMap.put(7749, "TEST_TARGET_URL_HTML_AR_7749");
		domainIdTestQueueNameMap.put(7751, "TEST_TARGET_URL_HTML_AR_7751");
		domainIdTestQueueNameMap.put(7752, "TEST_TARGET_URL_HTML_AR_7752");
		domainIdTestQueueNameMap.put(7756, "TEST_TARGET_URL_HTML_DE_7756");
		domainIdTestQueueNameMap.put(7763, "TEST_TARGET_URL_HTML_PT_7763");
		domainIdTestQueueNameMap.put(7778, "TEST_TARGET_URL_HTML_ZH_7778");
		domainIdTestQueueNameMap.put(7837, "TEST_TARGET_URL_HTML_ES_7837");
		domainIdTestQueueNameMap.put(7839, "TEST_TARGET_URL_HTML_DE_7839");
		domainIdTestQueueNameMap.put(7841, "TEST_TARGET_URL_HTML_DE_7841");
		domainIdTestQueueNameMap.put(7842, "TEST_TARGET_URL_HTML_DE_7842");
		domainIdTestQueueNameMap.put(7843, "TEST_TARGET_URL_HTML_DE_7843");
		domainIdTestQueueNameMap.put(7844, "TEST_TARGET_URL_HTML_DE_7844");
		domainIdTestQueueNameMap.put(7845, "TEST_TARGET_URL_HTML_DE_7845");
		domainIdTestQueueNameMap.put(7849, "TEST_TARGET_URL_HTML_ES_7849");
		domainIdTestQueueNameMap.put(7850, "TEST_TARGET_URL_HTML_ES_7850");
		domainIdTestQueueNameMap.put(7851, "TEST_TARGET_URL_HTML_FR_7851");
		domainIdTestQueueNameMap.put(7852, "TEST_TARGET_URL_HTML_FR_7852");
		domainIdTestQueueNameMap.put(7853, "TEST_TARGET_URL_HTML_FR_7853");
		domainIdTestQueueNameMap.put(7855, "TEST_TARGET_URL_HTML_FR_7855");
		domainIdTestQueueNameMap.put(7857, "TEST_TARGET_URL_HTML_FR_7857");
		domainIdTestQueueNameMap.put(7858, "TEST_TARGET_URL_HTML_FR_7858");
		domainIdTestQueueNameMap.put(7859, "TEST_TARGET_URL_HTML_FR_7859");
		domainIdTestQueueNameMap.put(7860, "TEST_TARGET_URL_HTML_FR_7860");
		domainIdTestQueueNameMap.put(7861, "TEST_TARGET_URL_HTML_FR_7861");
		domainIdTestQueueNameMap.put(7864, "TEST_TARGET_URL_HTML_EN_7864");
		domainIdTestQueueNameMap.put(7865, "TEST_TARGET_URL_HTML_EN_7865");
		domainIdTestQueueNameMap.put(7867, "TEST_TARGET_URL_HTML_EN_7867");
		domainIdTestQueueNameMap.put(7868, "TEST_TARGET_URL_HTML_EN_7868");
		domainIdTestQueueNameMap.put(7869, "TEST_TARGET_URL_HTML_EN_7869");
		domainIdTestQueueNameMap.put(7870, "TEST_TARGET_URL_HTML_EN_7870");
		domainIdTestQueueNameMap.put(7871, "TEST_TARGET_URL_HTML_EN_7871");
		domainIdTestQueueNameMap.put(7873, "TEST_TARGET_URL_HTML_EN_7873");
		domainIdTestQueueNameMap.put(7875, "TEST_TARGET_URL_HTML_EN_7875");
		domainIdTestQueueNameMap.put(7876, "TEST_TARGET_URL_HTML_EN_7876");
		domainIdTestQueueNameMap.put(7882, "TEST_TARGET_URL_HTML_EN_7882");
		domainIdTestQueueNameMap.put(7888, "TEST_TARGET_URL_HTML_EN_7888");
		domainIdTestQueueNameMap.put(7890, "TEST_TARGET_URL_HTML_EN_7890");
		domainIdTestQueueNameMap.put(7891, "TEST_TARGET_URL_HTML_EN_7891");
		domainIdTestQueueNameMap.put(7892, "TEST_TARGET_URL_HTML_EN_7892");
		domainIdTestQueueNameMap.put(7897, "TEST_TARGET_URL_HTML_IT_7897");
		domainIdTestQueueNameMap.put(7898, "TEST_TARGET_URL_HTML_IT_7898");
		domainIdTestQueueNameMap.put(7899, "TEST_TARGET_URL_HTML_JA_7899");
		domainIdTestQueueNameMap.put(7900, "TEST_TARGET_URL_HTML_JA_7900");
		domainIdTestQueueNameMap.put(7902, "TEST_TARGET_URL_HTML_JA_7902");
		domainIdTestQueueNameMap.put(7905, "TEST_TARGET_URL_HTML_JA_7905");
		domainIdTestQueueNameMap.put(7906, "TEST_TARGET_URL_HTML_JA_7906");
		domainIdTestQueueNameMap.put(7908, "TEST_TARGET_URL_HTML_JA_7908");
		domainIdTestQueueNameMap.put(7909, "TEST_TARGET_URL_HTML_JA_7909");
		domainIdTestQueueNameMap.put(7923, "TEST_TARGET_URL_HTML_ES_7923");
		domainIdTestQueueNameMap.put(7925, "TEST_TARGET_URL_HTML_EN_7925");
		domainIdTestQueueNameMap.put(7926, "TEST_TARGET_URL_HTML_EN_7926");
		domainIdTestQueueNameMap.put(7928, "TEST_TARGET_URL_HTML_EN_7928");
		domainIdTestQueueNameMap.put(7929, "TEST_TARGET_URL_HTML_EN_7929");
		domainIdTestQueueNameMap.put(7930, "TEST_TARGET_URL_HTML_NL_7930");
		domainIdTestQueueNameMap.put(7931, "TEST_TARGET_URL_HTML_NL_7931");
		domainIdTestQueueNameMap.put(7938, "TEST_TARGET_URL_HTML_RU_7938");
		domainIdTestQueueNameMap.put(7939, "TEST_TARGET_URL_HTML_RU_7939");
		domainIdTestQueueNameMap.put(7940, "TEST_TARGET_URL_HTML_AR_7940");
		domainIdTestQueueNameMap.put(7945, "TEST_TARGET_URL_HTML_AR_7945");
		domainIdTestQueueNameMap.put(7947, "TEST_TARGET_URL_HTML_EN_7947");
		domainIdTestQueueNameMap.put(7948, "TEST_TARGET_URL_HTML_EN_7948");
		domainIdTestQueueNameMap.put(7949, "TEST_TARGET_URL_HTML_TH_7949");
		domainIdTestQueueNameMap.put(7951, "TEST_TARGET_URL_HTML_TH_7951");
		domainIdTestQueueNameMap.put(7953, "TEST_TARGET_URL_HTML_TH_7953");
		domainIdTestQueueNameMap.put(7954, "TEST_TARGET_URL_HTML_TH_7954");
		domainIdTestQueueNameMap.put(7956, "TEST_TARGET_URL_HTML_TR_7956");
		domainIdTestQueueNameMap.put(7961, "TEST_TARGET_URL_HTML_RU_7961");
		domainIdTestQueueNameMap.put(7962, "TEST_TARGET_URL_HTML_EN_7962");
		domainIdTestQueueNameMap.put(7963, "TEST_TARGET_URL_HTML_EN_7963");
		domainIdTestQueueNameMap.put(7964, "TEST_TARGET_URL_HTML_EN_7964");
		domainIdTestQueueNameMap.put(7965, "TEST_TARGET_URL_HTML_EN_7965");
		domainIdTestQueueNameMap.put(7966, "TEST_TARGET_URL_HTML_EN_7966");
		domainIdTestQueueNameMap.put(7978, "TEST_TARGET_URL_HTML_EN_7978");
		domainIdTestQueueNameMap.put(8015, "TEST_TARGET_URL_HTML_EN_8015");
		domainIdTestQueueNameMap.put(8034, "TEST_TARGET_URL_HTML_EN_8034");
		domainIdTestQueueNameMap.put(8231, "TEST_TARGET_URL_HTML_EN_8231");
		domainIdTestQueueNameMap.put(8249, "TEST_TARGET_URL_HTML_EN_8249");
		domainIdTestQueueNameMap.put(8354, "TEST_TARGET_URL_HTML_EN_8354");
		domainIdTestQueueNameMap.put(8358, "TEST_TARGET_URL_HTML_EN_8358");
		domainIdTestQueueNameMap.put(8359, "TEST_TARGET_URL_HTML_EN_8359");
		domainIdTestQueueNameMap.put(8363, "TEST_TARGET_URL_HTML_EN_8363");
		domainIdTestQueueNameMap.put(8384, "TEST_TARGET_URL_HTML_SR_8384");
		domainIdTestQueueNameMap.put(8385, "TEST_TARGET_URL_HTML_UZ_8385");
		domainIdTestQueueNameMap.put(8387, "TEST_TARGET_URL_HTML_EN_8387");
		domainIdTestQueueNameMap.put(8389, "TEST_TARGET_URL_HTML_AZ_8389");
		domainIdTestQueueNameMap.put(8390, "TEST_TARGET_URL_HTML_KM_8390");
		domainIdTestQueueNameMap.put(8393, "TEST_TARGET_URL_HTML_AM_8393");
		domainIdTestQueueNameMap.put(8397, "TEST_TARGET_URL_HTML_NE_8397");
		domainIdTestQueueNameMap.put(8400, "TEST_TARGET_URL_HTML_SW_8400");
		domainIdTestQueueNameMap.put(8403, "TEST_TARGET_URL_HTML_EN_8403");
		domainIdTestQueueNameMap.put(8404, "TEST_TARGET_URL_HTML_EN_8404");
		domainIdTestQueueNameMap.put(8414, "TEST_TARGET_URL_HTML_ES_8414");
		domainIdTestQueueNameMap.put(8422, "TEST_TARGET_URL_HTML_EN_8422");
		domainIdTestQueueNameMap.put(8443, "TEST_TARGET_URL_HTML_EN_8443");
		domainIdTestQueueNameMap.put(8471, "TEST_TARGET_URL_HTML_EN_8471");
		domainIdTestQueueNameMap.put(8473, "TEST_TARGET_URL_HTML_EN_8473");
		domainIdTestQueueNameMap.put(8475, "TEST_TARGET_URL_HTML_EN_8475");
		domainIdTestQueueNameMap.put(8476, "TEST_TARGET_URL_HTML_EN_8476");
		domainIdTestQueueNameMap.put(8477, "TEST_TARGET_URL_HTML_JA_8477");
		domainIdTestQueueNameMap.put(8486, "TEST_TARGET_URL_HTML_DE_8486");
		domainIdTestQueueNameMap.put(8489, "TEST_TARGET_URL_HTML_EN_8489");
		domainIdTestQueueNameMap.put(8499, "TEST_TARGET_URL_HTML_EN_8499");
		domainIdTestQueueNameMap.put(8500, "TEST_TARGET_URL_HTML_EN_8500");
		domainIdTestQueueNameMap.put(8508, "TEST_TARGET_URL_HTML_EN_8508");
		domainIdTestQueueNameMap.put(8517, "TEST_TARGET_URL_HTML_EN_8517");
		domainIdTestQueueNameMap.put(8560, "TEST_TARGET_URL_HTML_EN_8560");
		domainIdTestQueueNameMap.put(8564, "TEST_TARGET_URL_HTML_RU_8564");
		domainIdTestQueueNameMap.put(8565, "TEST_TARGET_URL_HTML_RU_8565");
		domainIdTestQueueNameMap.put(8574, "TEST_TARGET_URL_HTML_ES_8574");
		domainIdTestQueueNameMap.put(8575, "TEST_TARGET_URL_HTML_ES_8575");
		domainIdTestQueueNameMap.put(8576, "TEST_TARGET_URL_HTML_ES_8576");
		domainIdTestQueueNameMap.put(8577, "TEST_TARGET_URL_HTML_ES_8577");
		domainIdTestQueueNameMap.put(8578, "TEST_TARGET_URL_HTML_ES_8578");
		domainIdTestQueueNameMap.put(8580, "TEST_TARGET_URL_HTML_JA_8580");
		domainIdTestQueueNameMap.put(8582, "TEST_TARGET_URL_HTML_JA_8582");
		domainIdTestQueueNameMap.put(8586, "TEST_TARGET_URL_HTML_ES_8586");
		domainIdTestQueueNameMap.put(8595, "TEST_TARGET_URL_HTML_ES_8595");
		domainIdTestQueueNameMap.put(8596, "TEST_TARGET_URL_HTML_EN_8596");
		domainIdTestQueueNameMap.put(8600, "TEST_TARGET_URL_HTML_ZH_8600");
		domainIdTestQueueNameMap.put(8608, "TEST_TARGET_URL_HTML_EN_8608");
		domainIdTestQueueNameMap.put(8622, "TEST_TARGET_URL_HTML_EN_8622");
		domainIdTestQueueNameMap.put(8644, "TEST_TARGET_URL_HTML_EN_8644");
		domainIdTestQueueNameMap.put(8655, "TEST_TARGET_URL_HTML_EN_8655");
		domainIdTestQueueNameMap.put(8656, "TEST_TARGET_URL_HTML_EN_8656");
		domainIdTestQueueNameMap.put(8659, "TEST_TARGET_URL_HTML_JA_8659");
		domainIdTestQueueNameMap.put(8667, "TEST_TARGET_URL_HTML_EN_8667");
		domainIdTestQueueNameMap.put(8701, "TEST_TARGET_URL_HTML_EN_8701");
		domainIdTestQueueNameMap.put(8702, "TEST_TARGET_URL_HTML_EN_8702");
		domainIdTestQueueNameMap.put(8703, "TEST_TARGET_URL_HTML_EN_8703");
		domainIdTestQueueNameMap.put(8704, "TEST_TARGET_URL_HTML_NO_8704");
		domainIdTestQueueNameMap.put(8706, "TEST_TARGET_URL_HTML_DA_8706");
		domainIdTestQueueNameMap.put(8710, "TEST_TARGET_URL_HTML_SV_8710");
		domainIdTestQueueNameMap.put(8711, "TEST_TARGET_URL_HTML_EN_8711");
		domainIdTestQueueNameMap.put(8715, "TEST_TARGET_URL_HTML_EN_8715");
		domainIdTestQueueNameMap.put(8716, "TEST_TARGET_URL_HTML_EN_8716");
		domainIdTestQueueNameMap.put(8717, "TEST_TARGET_URL_HTML_EN_8717");
		domainIdTestQueueNameMap.put(8718, "TEST_TARGET_URL_HTML_EN_8718");
		domainIdTestQueueNameMap.put(8719, "TEST_TARGET_URL_HTML_ES_8719");
		domainIdTestQueueNameMap.put(8720, "TEST_TARGET_URL_HTML_ES_8720");
		domainIdTestQueueNameMap.put(8721, "TEST_TARGET_URL_HTML_ES_8721");
		domainIdTestQueueNameMap.put(8722, "TEST_TARGET_URL_HTML_EN_8722");
		domainIdTestQueueNameMap.put(8733, "TEST_TARGET_URL_HTML_EN_8733");
		domainIdTestQueueNameMap.put(8735, "TEST_TARGET_URL_HTML_EN_8735");
		domainIdTestQueueNameMap.put(8754, "TEST_TARGET_URL_HTML_EN_8754");
		domainIdTestQueueNameMap.put(8755, "TEST_TARGET_URL_HTML_PT_8755");
		domainIdTestQueueNameMap.put(8756, "TEST_TARGET_URL_HTML_EN_8756");
		domainIdTestQueueNameMap.put(8757, "TEST_TARGET_URL_HTML_FR_8757");
		domainIdTestQueueNameMap.put(8758, "TEST_TARGET_URL_HTML_DE_8758");
		domainIdTestQueueNameMap.put(8759, "TEST_TARGET_URL_HTML_FR_8759");
		domainIdTestQueueNameMap.put(8760, "TEST_TARGET_URL_HTML_FI_8760");
		domainIdTestQueueNameMap.put(8761, "TEST_TARGET_URL_HTML_EN_8761");
		domainIdTestQueueNameMap.put(8762, "TEST_TARGET_URL_HTML_EN_8762");
		domainIdTestQueueNameMap.put(8763, "TEST_TARGET_URL_HTML_JA_8763");
		domainIdTestQueueNameMap.put(8764, "TEST_TARGET_URL_HTML_ES_8764");
		domainIdTestQueueNameMap.put(8765, "TEST_TARGET_URL_HTML_NL_8765");
		domainIdTestQueueNameMap.put(8773, "TEST_TARGET_URL_HTML_EN_8773");
		domainIdTestQueueNameMap.put(8777, "TEST_TARGET_URL_HTML_EN_8777");
		domainIdTestQueueNameMap.put(8781, "TEST_TARGET_URL_HTML_EN_8781");
		domainIdTestQueueNameMap.put(8785, "TEST_TARGET_URL_HTML_JA_8785");
		domainIdTestQueueNameMap.put(8787, "TEST_TARGET_URL_HTML_DE_8787");
		domainIdTestQueueNameMap.put(8788, "TEST_TARGET_URL_HTML_FR_8788");
		domainIdTestQueueNameMap.put(8789, "TEST_TARGET_URL_HTML_NL_8789");
		domainIdTestQueueNameMap.put(8802, "TEST_TARGET_URL_HTML_EN_8802");
		domainIdTestQueueNameMap.put(8809, "TEST_TARGET_URL_HTML_EN_8809");
		domainIdTestQueueNameMap.put(8810, "TEST_TARGET_URL_HTML_EN_8810");
		domainIdTestQueueNameMap.put(8811, "TEST_TARGET_URL_HTML_FR_8811");
		domainIdTestQueueNameMap.put(8812, "TEST_TARGET_URL_HTML_ES_8812");
		domainIdTestQueueNameMap.put(8813, "TEST_TARGET_URL_HTML_EN_8813");
		domainIdTestQueueNameMap.put(8826, "TEST_TARGET_URL_HTML_EN_8826");
		domainIdTestQueueNameMap.put(8857, "TEST_TARGET_URL_HTML_EN_8857");
		domainIdTestQueueNameMap.put(8858, "TEST_TARGET_URL_HTML_EN_8858");
		domainIdTestQueueNameMap.put(8859, "TEST_TARGET_URL_HTML_EN_8859");
		domainIdTestQueueNameMap.put(8860, "TEST_TARGET_URL_HTML_EN_8860");
		domainIdTestQueueNameMap.put(8863, "TEST_TARGET_URL_HTML_AR_8863");
		domainIdTestQueueNameMap.put(8864, "TEST_TARGET_URL_HTML_DE_8864");
		domainIdTestQueueNameMap.put(8865, "TEST_TARGET_URL_HTML_ES_8865");
		domainIdTestQueueNameMap.put(8866, "TEST_TARGET_URL_HTML_FR_8866");
		domainIdTestQueueNameMap.put(8867, "TEST_TARGET_URL_HTML_ID_8867");
		domainIdTestQueueNameMap.put(8868, "TEST_TARGET_URL_HTML_IT_8868");
		domainIdTestQueueNameMap.put(8869, "TEST_TARGET_URL_HTML_JA_8869");
		domainIdTestQueueNameMap.put(8870, "TEST_TARGET_URL_HTML_KO_8870");
		domainIdTestQueueNameMap.put(8871, "TEST_TARGET_URL_HTML_PT_8871");
		domainIdTestQueueNameMap.put(8872, "TEST_TARGET_URL_HTML_RU_8872");
		domainIdTestQueueNameMap.put(8873, "TEST_TARGET_URL_HTML_TH_8873");
		domainIdTestQueueNameMap.put(8874, "TEST_TARGET_URL_HTML_TR_8874");
		domainIdTestQueueNameMap.put(8875, "TEST_TARGET_URL_HTML_ZH_8875");
		domainIdTestQueueNameMap.put(8876, "TEST_TARGET_URL_HTML_ZH-TW_8876");
		domainIdTestQueueNameMap.put(8881, "TEST_TARGET_URL_HTML_EN_8881");
		domainIdTestQueueNameMap.put(8882, "TEST_TARGET_URL_HTML_EN_8882");
		domainIdTestQueueNameMap.put(8883, "TEST_TARGET_URL_HTML_DE_8883");
		domainIdTestQueueNameMap.put(8884, "TEST_TARGET_URL_HTML_JA_8884");
		domainIdTestQueueNameMap.put(8885, "TEST_TARGET_URL_HTML_EN_8885");
		domainIdTestQueueNameMap.put(8886, "TEST_TARGET_URL_HTML_FR_8886");
		domainIdTestQueueNameMap.put(8887, "TEST_TARGET_URL_HTML_EN_8887");
		domainIdTestQueueNameMap.put(8888, "TEST_TARGET_URL_HTML_EN_8888");
		domainIdTestQueueNameMap.put(8889, "TEST_TARGET_URL_HTML_FR_8889");
		domainIdTestQueueNameMap.put(8890, "TEST_TARGET_URL_HTML_PT_8890");
		domainIdTestQueueNameMap.put(8891, "TEST_TARGET_URL_HTML_EN_8891");
		domainIdTestQueueNameMap.put(8892, "TEST_TARGET_URL_HTML_FR_8892");
		domainIdTestQueueNameMap.put(8893, "TEST_TARGET_URL_HTML_EN_8893");
		domainIdTestQueueNameMap.put(8894, "TEST_TARGET_URL_HTML_IT_8894");
		domainIdTestQueueNameMap.put(8895, "TEST_TARGET_URL_HTML_ES_8895");
		domainIdTestQueueNameMap.put(8897, "TEST_TARGET_URL_HTML_EN_8897");
		domainIdTestQueueNameMap.put(8898, "TEST_TARGET_URL_HTML_KO_8898");
		domainIdTestQueueNameMap.put(8899, "TEST_TARGET_URL_HTML_ES_8899");
		domainIdTestQueueNameMap.put(8900, "TEST_TARGET_URL_HTML_DE_8900");
		domainIdTestQueueNameMap.put(8901, "TEST_TARGET_URL_HTML_FR_8901");
		domainIdTestQueueNameMap.put(8904, "TEST_TARGET_URL_HTML_DE_8904");
		domainIdTestQueueNameMap.put(8906, "TEST_TARGET_URL_HTML_EN_8906");
		domainIdTestQueueNameMap.put(8907, "TEST_TARGET_URL_HTML_EN_8907");
		domainIdTestQueueNameMap.put(8908, "TEST_TARGET_URL_HTML_EN_8908");
		domainIdTestQueueNameMap.put(8909, "TEST_TARGET_URL_HTML_EN_8909");
		domainIdTestQueueNameMap.put(8910, "TEST_TARGET_URL_HTML_EN_8910");
		domainIdTestQueueNameMap.put(8911, "TEST_TARGET_URL_HTML_EN_8911");
		domainIdTestQueueNameMap.put(8912, "TEST_TARGET_URL_HTML_EN_8912");
		domainIdTestQueueNameMap.put(8913, "TEST_TARGET_URL_HTML_EN_8913");
		domainIdTestQueueNameMap.put(8914, "TEST_TARGET_URL_HTML_EN_8914");
		domainIdTestQueueNameMap.put(8915, "TEST_TARGET_URL_HTML_EN_8915");
		domainIdTestQueueNameMap.put(8916, "TEST_TARGET_URL_HTML_RU_8916");
		domainIdTestQueueNameMap.put(8917, "TEST_TARGET_URL_HTML_ZH_8917");
		domainIdTestQueueNameMap.put(8918, "TEST_TARGET_URL_HTML_EN_8918");
		domainIdTestQueueNameMap.put(8920, "TEST_TARGET_URL_HTML_EN_8920");
		domainIdTestQueueNameMap.put(8921, "TEST_TARGET_URL_HTML_EN_8921");
		domainIdTestQueueNameMap.put(8922, "TEST_TARGET_URL_HTML_DE_8922");
		domainIdTestQueueNameMap.put(8928, "TEST_TARGET_URL_HTML_SQ_8928");
		domainIdTestQueueNameMap.put(8929, "TEST_TARGET_URL_HTML_DE_8929");
		domainIdTestQueueNameMap.put(8930, "TEST_TARGET_URL_HTML_NL_8930");
		domainIdTestQueueNameMap.put(8931, "TEST_TARGET_URL_HTML_BG_8931");
		domainIdTestQueueNameMap.put(8932, "TEST_TARGET_URL_HTML_DE_8932");
		domainIdTestQueueNameMap.put(8933, "TEST_TARGET_URL_HTML_FR_8933");
		domainIdTestQueueNameMap.put(8934, "TEST_TARGET_URL_HTML_CS_8934");
		domainIdTestQueueNameMap.put(8935, "TEST_TARGET_URL_HTML_DA_8935");
		domainIdTestQueueNameMap.put(8936, "TEST_TARGET_URL_HTML_ET_8936");
		domainIdTestQueueNameMap.put(8937, "TEST_TARGET_URL_HTML_FI_8937");
		domainIdTestQueueNameMap.put(8938, "TEST_TARGET_URL_HTML_EL_8938");
		domainIdTestQueueNameMap.put(8939, "TEST_TARGET_URL_HTML_HR_8939");
		domainIdTestQueueNameMap.put(8940, "TEST_TARGET_URL_HTML_HU_8940");
		domainIdTestQueueNameMap.put(8941, "TEST_TARGET_URL_HTML_EN_8941");
		domainIdTestQueueNameMap.put(8942, "TEST_TARGET_URL_HTML_IT_8942");
		domainIdTestQueueNameMap.put(8943, "TEST_TARGET_URL_HTML_LT_8943");
		domainIdTestQueueNameMap.put(8944, "TEST_TARGET_URL_HTML_LV_8944");
		domainIdTestQueueNameMap.put(8945, "TEST_TARGET_URL_HTML_NO_8945");
		domainIdTestQueueNameMap.put(8946, "TEST_TARGET_URL_HTML_PL_8946");
		domainIdTestQueueNameMap.put(8947, "TEST_TARGET_URL_HTML_PT_8947");
		domainIdTestQueueNameMap.put(8948, "TEST_TARGET_URL_HTML_RO_8948");
		domainIdTestQueueNameMap.put(8949, "TEST_TARGET_URL_HTML_SR_8949");
		domainIdTestQueueNameMap.put(8950, "TEST_TARGET_URL_HTML_SV_8950");
		domainIdTestQueueNameMap.put(8951, "TEST_TARGET_URL_HTML_SK_8951");
		domainIdTestQueueNameMap.put(8958, "TEST_TARGET_URL_HTML_EN_8958");
		domainIdTestQueueNameMap.put(8959, "TEST_TARGET_URL_HTML_SL_8959");
		domainIdTestQueueNameMap.put(8960, "TEST_TARGET_URL_HTML_MK_8960");
		domainIdTestQueueNameMap.put(8978, "TEST_TARGET_URL_HTML_EN_8978");
		domainIdTestQueueNameMap.put(8980, "TEST_TARGET_URL_HTML_EN_8980");
		domainIdTestQueueNameMap.put(8981, "TEST_TARGET_URL_HTML_EN_8981");
		domainIdTestQueueNameMap.put(8982, "TEST_TARGET_URL_HTML_EN_8982");
		domainIdTestQueueNameMap.put(8985, "TEST_TARGET_URL_HTML_EN_8985");
		domainIdTestQueueNameMap.put(8986, "TEST_TARGET_URL_HTML_EN_8986");
		domainIdTestQueueNameMap.put(8987, "TEST_TARGET_URL_HTML_EN_8987");
		domainIdTestQueueNameMap.put(8999, "TEST_TARGET_URL_HTML_EN_8999");
		domainIdTestQueueNameMap.put(9005, "TEST_TARGET_URL_HTML_EN_9005");
		domainIdTestQueueNameMap.put(9010, "TEST_TARGET_URL_HTML_NL_9010");
		domainIdTestQueueNameMap.put(9011, "TEST_TARGET_URL_HTML_SV_9011");
		domainIdTestQueueNameMap.put(9012, "TEST_TARGET_URL_HTML_TR_9012");
		domainIdTestQueueNameMap.put(9030, "TEST_TARGET_URL_HTML_ES_9030");
		domainIdTestQueueNameMap.put(9031, "TEST_TARGET_URL_HTML_EN_9031");
		domainIdTestQueueNameMap.put(9032, "TEST_TARGET_URL_HTML_EN_9032");
		domainIdTestQueueNameMap.put(9033, "TEST_TARGET_URL_HTML_EN_9033");
		domainIdTestQueueNameMap.put(9037, "TEST_TARGET_URL_HTML_EN_9037");
		domainIdTestQueueNameMap.put(9038, "TEST_TARGET_URL_HTML_EN_9038");
		domainIdTestQueueNameMap.put(9039, "TEST_TARGET_URL_HTML_EN_9039");
		domainIdTestQueueNameMap.put(9040, "TEST_TARGET_URL_HTML_EN_9040");
		domainIdTestQueueNameMap.put(9041, "TEST_TARGET_URL_HTML_EN_9041");
		domainIdTestQueueNameMap.put(9042, "TEST_TARGET_URL_HTML_EN_9042");
		domainIdTestQueueNameMap.put(9043, "TEST_TARGET_URL_HTML_EN_9043");
		domainIdTestQueueNameMap.put(9048, "TEST_TARGET_URL_HTML_EN_9048");
		domainIdTestQueueNameMap.put(9057, "TEST_TARGET_URL_HTML_JA_9057");
		domainIdTestQueueNameMap.put(9060, "TEST_TARGET_URL_HTML_EN_9060");
		domainIdTestQueueNameMap.put(9075, "TEST_TARGET_URL_HTML_PL_9075");
		domainIdTestQueueNameMap.put(9079, "TEST_TARGET_URL_HTML_EN_9079");
		domainIdTestQueueNameMap.put(9089, "TEST_TARGET_URL_HTML_EN_9089");
		domainIdTestQueueNameMap.put(9090, "TEST_TARGET_URL_HTML_EN_9090");
		domainIdTestQueueNameMap.put(9093, "TEST_TARGET_URL_HTML_EN_9093");
		domainIdTestQueueNameMap.put(9094, "TEST_TARGET_URL_HTML_EN_9094");
		domainIdTestQueueNameMap.put(9099, "TEST_TARGET_URL_HTML_EN_9099");
		domainIdTestQueueNameMap.put(9103, "TEST_TARGET_URL_HTML_EN_9103");
		domainIdTestQueueNameMap.put(9106, "TEST_TARGET_URL_HTML_EN_9106");
		domainIdTestQueueNameMap.put(9112, "TEST_TARGET_URL_HTML_EN_9112");
		domainIdTestQueueNameMap.put(9113, "TEST_TARGET_URL_HTML_EN_9113");
		domainIdTestQueueNameMap.put(9114, "TEST_TARGET_URL_HTML_EN_9114");
		domainIdTestQueueNameMap.put(9116, "TEST_TARGET_URL_HTML_EN_9116");
		domainIdTestQueueNameMap.put(9117, "TEST_TARGET_URL_HTML_EN_9117");
		domainIdTestQueueNameMap.put(9118, "TEST_TARGET_URL_HTML_EN_9118");
		domainIdTestQueueNameMap.put(9119, "TEST_TARGET_URL_HTML_EN_9119");
		domainIdTestQueueNameMap.put(9120, "TEST_TARGET_URL_HTML_EN_9120");
		domainIdTestQueueNameMap.put(9121, "TEST_TARGET_URL_HTML_EN_9121");
		domainIdTestQueueNameMap.put(9122, "TEST_TARGET_URL_HTML_EN_9122");
		domainIdTestQueueNameMap.put(9123, "TEST_TARGET_URL_HTML_EN_9123");
		domainIdTestQueueNameMap.put(9124, "TEST_TARGET_URL_HTML_EN_9124");
		domainIdTestQueueNameMap.put(9126, "TEST_TARGET_URL_HTML_EN_9126");
		domainIdTestQueueNameMap.put(9128, "TEST_TARGET_URL_HTML_EN_9128");
		domainIdTestQueueNameMap.put(9129, "TEST_TARGET_URL_HTML_EN_9129");
		domainIdTestQueueNameMap.put(9132, "TEST_TARGET_URL_HTML_EN_9132");
		domainIdTestQueueNameMap.put(9134, "TEST_TARGET_URL_HTML_EN_9134");
		domainIdTestQueueNameMap.put(9135, "TEST_TARGET_URL_HTML_EN_9135");
		domainIdTestQueueNameMap.put(9140, "TEST_TARGET_URL_HTML_EN_9140");
		domainIdTestQueueNameMap.put(9152, "TEST_TARGET_URL_HTML_EN_9152");
		domainIdTestQueueNameMap.put(9153, "TEST_TARGET_URL_HTML_EN_9153");
		domainIdTestQueueNameMap.put(9157, "TEST_TARGET_URL_HTML_EN_9157");
		domainIdTestQueueNameMap.put(9165, "TEST_TARGET_URL_HTML_EN_9165");
		domainIdTestQueueNameMap.put(9167, "TEST_TARGET_URL_HTML_EN_9167");
		domainIdTestQueueNameMap.put(9168, "TEST_TARGET_URL_HTML_EN_9168");
		domainIdTestQueueNameMap.put(9170, "TEST_TARGET_URL_HTML_EN_9170");
		domainIdTestQueueNameMap.put(9171, "TEST_TARGET_URL_HTML_EN_9171");
		domainIdTestQueueNameMap.put(9172, "TEST_TARGET_URL_HTML_EN_9172");
		domainIdTestQueueNameMap.put(9174, "TEST_TARGET_URL_HTML_EN_9174");
		domainIdTestQueueNameMap.put(9177, "TEST_TARGET_URL_HTML_EN_9177");
		domainIdTestQueueNameMap.put(9178, "TEST_TARGET_URL_HTML_EN_9178");
		domainIdTestQueueNameMap.put(9183, "TEST_TARGET_URL_HTML_PT_9183");
		domainIdTestQueueNameMap.put(9184, "TEST_TARGET_URL_HTML_PT_9184");
		domainIdTestQueueNameMap.put(9185, "TEST_TARGET_URL_HTML_PT_9185");
		domainIdTestQueueNameMap.put(9186, "TEST_TARGET_URL_HTML_PT_9186");
		domainIdTestQueueNameMap.put(9188, "TEST_TARGET_URL_HTML_EN_9188");
		domainIdTestQueueNameMap.put(9189, "TEST_TARGET_URL_HTML_EN_9189");
		domainIdTestQueueNameMap.put(9203, "TEST_TARGET_URL_HTML_NL_9203");
		domainIdTestQueueNameMap.put(9248, "TEST_TARGET_URL_HTML_EN_9248");
		domainIdTestQueueNameMap.put(9259, "TEST_TARGET_URL_HTML_JA_9259");
		domainIdTestQueueNameMap.put(9262, "TEST_TARGET_URL_HTML_ZH-TW_9262");
		domainIdTestQueueNameMap.put(9294, "TEST_TARGET_URL_HTML_EN_9294");
		domainIdTestQueueNameMap.put(9297, "TEST_TARGET_URL_HTML_JA_9297");
		domainIdTestQueueNameMap.put(9315, "TEST_TARGET_URL_HTML_EN_9315");
		domainIdTestQueueNameMap.put(9339, "TEST_TARGET_URL_HTML_EN_9339");
		domainIdTestQueueNameMap.put(9340, "TEST_TARGET_URL_HTML_EN_9340");
		domainIdTestQueueNameMap.put(9341, "TEST_TARGET_URL_HTML_EN_9341");
		domainIdTestQueueNameMap.put(9342, "TEST_TARGET_URL_HTML_EN_9342");
		domainIdTestQueueNameMap.put(9343, "TEST_TARGET_URL_HTML_EN_9343");
		domainIdTestQueueNameMap.put(9344, "TEST_TARGET_URL_HTML_EN_9344");
		domainIdTestQueueNameMap.put(9345, "TEST_TARGET_URL_HTML_EN_9345");
		domainIdTestQueueNameMap.put(9346, "TEST_TARGET_URL_HTML_EN_9346");
		domainIdTestQueueNameMap.put(9347, "TEST_TARGET_URL_HTML_EN_9347");
		domainIdTestQueueNameMap.put(9348, "TEST_TARGET_URL_HTML_EN_9348");
		domainIdTestQueueNameMap.put(9350, "TEST_TARGET_URL_HTML_EN_9350");
		domainIdTestQueueNameMap.put(9351, "TEST_TARGET_URL_HTML_EN_9351");
		domainIdTestQueueNameMap.put(9371, "TEST_TARGET_URL_HTML_EN_9371");
		domainIdTestQueueNameMap.put(9372, "TEST_TARGET_URL_HTML_EN_9372");
		domainIdTestQueueNameMap.put(9375, "TEST_TARGET_URL_HTML_EN_9375");
		domainIdTestQueueNameMap.put(9377, "TEST_TARGET_URL_HTML_EN_9377");
		domainIdTestQueueNameMap.put(9378, "TEST_TARGET_URL_HTML_EN_9378");
		domainIdTestQueueNameMap.put(9394, "TEST_TARGET_URL_HTML_ES_9394");
		domainIdTestQueueNameMap.put(9404, "TEST_TARGET_URL_HTML_EN_9404");
		domainIdTestQueueNameMap.put(9409, "TEST_TARGET_URL_HTML_EN_9409");
		domainIdTestQueueNameMap.put(9410, "TEST_TARGET_URL_HTML_EN_9410");
		domainIdTestQueueNameMap.put(9411, "TEST_TARGET_URL_HTML_EN_9411");
		domainIdTestQueueNameMap.put(9414, "TEST_TARGET_URL_HTML_EN_9414");
		domainIdTestQueueNameMap.put(9415, "TEST_TARGET_URL_HTML_EN_9415");
		domainIdTestQueueNameMap.put(9416, "TEST_TARGET_URL_HTML_EN_9416");
		domainIdTestQueueNameMap.put(9421, "TEST_TARGET_URL_HTML_FR_9421");
		domainIdTestQueueNameMap.put(9422, "TEST_TARGET_URL_HTML_EN_9422");
		domainIdTestQueueNameMap.put(9438, "TEST_TARGET_URL_HTML_EN_9438");
		domainIdTestQueueNameMap.put(9439, "TEST_TARGET_URL_HTML_EN_9439");
		domainIdTestQueueNameMap.put(9440, "TEST_TARGET_URL_HTML_EN_9440");
		domainIdTestQueueNameMap.put(9455, "TEST_TARGET_URL_HTML_EN_9455");
		domainIdTestQueueNameMap.put(9456, "TEST_TARGET_URL_HTML_EN_9456");
		domainIdTestQueueNameMap.put(9460, "TEST_TARGET_URL_HTML_EN_9460");
		domainIdTestQueueNameMap.put(9485, "TEST_TARGET_URL_HTML_EN_9485");
		domainIdTestQueueNameMap.put(9486, "TEST_TARGET_URL_HTML_EN_9486");
		domainIdTestQueueNameMap.put(9488, "TEST_TARGET_URL_HTML_EN_9488");
		domainIdTestQueueNameMap.put(9495, "TEST_TARGET_URL_HTML_EN_9495");
		domainIdTestQueueNameMap.put(9500, "TEST_TARGET_URL_HTML_EN_9500");
		domainIdTestQueueNameMap.put(9503, "TEST_TARGET_URL_HTML_EN_9503");
		domainIdTestQueueNameMap.put(9504, "TEST_TARGET_URL_HTML_EN_9504");
		domainIdTestQueueNameMap.put(9505, "TEST_TARGET_URL_HTML_EN_9505");
		domainIdTestQueueNameMap.put(9506, "TEST_TARGET_URL_HTML_EN_9506");
		domainIdTestQueueNameMap.put(9507, "TEST_TARGET_URL_HTML_EN_9507");
		domainIdTestQueueNameMap.put(9511, "TEST_TARGET_URL_HTML_EN_9511");
		domainIdTestQueueNameMap.put(9517, "TEST_TARGET_URL_HTML_EN_9517");
		domainIdTestQueueNameMap.put(9547, "TEST_TARGET_URL_HTML_NL_9547");
		domainIdTestQueueNameMap.put(9552, "TEST_TARGET_URL_HTML_EN_9552");
		domainIdTestQueueNameMap.put(9553, "TEST_TARGET_URL_HTML_EN_9553");
		domainIdTestQueueNameMap.put(9559, "TEST_TARGET_URL_HTML_EN_9559");
		domainIdTestQueueNameMap.put(9565, "TEST_TARGET_URL_HTML_EN_9565");
		domainIdTestQueueNameMap.put(9566, "TEST_TARGET_URL_HTML_EN_9566");
		domainIdTestQueueNameMap.put(9569, "TEST_TARGET_URL_HTML_AR_9569");
		domainIdTestQueueNameMap.put(9570, "TEST_TARGET_URL_HTML_AR_9570");
		domainIdTestQueueNameMap.put(9582, "TEST_TARGET_URL_HTML_EN_9582");
		domainIdTestQueueNameMap.put(9583, "TEST_TARGET_URL_HTML_VI_9583");
		domainIdTestQueueNameMap.put(9592, "TEST_TARGET_URL_HTML_EN_9592");
		domainIdTestQueueNameMap.put(9622, "TEST_TARGET_URL_HTML_EN_9622");
		domainIdTestQueueNameMap.put(9632, "TEST_TARGET_URL_HTML_EN_9632");
		domainIdTestQueueNameMap.put(9654, "TEST_TARGET_URL_HTML_EN_9654");
		domainIdTestQueueNameMap.put(9661, "TEST_TARGET_URL_HTML_BS_9661");
		domainIdTestQueueNameMap.put(9667, "TEST_TARGET_URL_HTML_EN_9667");
		domainIdTestQueueNameMap.put(9668, "TEST_TARGET_URL_HTML_EN_9668");
		domainIdTestQueueNameMap.put(9676, "TEST_TARGET_URL_HTML_EN_9676");
		domainIdTestQueueNameMap.put(9677, "TEST_TARGET_URL_HTML_EN_9677");
		domainIdTestQueueNameMap.put(9678, "TEST_TARGET_URL_HTML_EN_9678");
		domainIdTestQueueNameMap.put(9691, "TEST_TARGET_URL_HTML_EN_9691");
		domainIdTestQueueNameMap.put(9693, "TEST_TARGET_URL_HTML_EN_9693");
		domainIdTestQueueNameMap.put(9695, "TEST_TARGET_URL_HTML_EN_9695");
		domainIdTestQueueNameMap.put(9712, "TEST_TARGET_URL_HTML_NL_9712");
		domainIdTestQueueNameMap.put(9715, "TEST_TARGET_URL_HTML_EN_9715");
		domainIdTestQueueNameMap.put(9716, "TEST_TARGET_URL_HTML_EN_9716");
		domainIdTestQueueNameMap.put(9717, "TEST_TARGET_URL_HTML_EN_9717");
		domainIdTestQueueNameMap.put(9718, "TEST_TARGET_URL_HTML_EN_9718");
		domainIdTestQueueNameMap.put(9723, "TEST_TARGET_URL_HTML_EN_9723");
		domainIdTestQueueNameMap.put(9725, "TEST_TARGET_URL_HTML_EN_9725");
		domainIdTestQueueNameMap.put(9729, "TEST_TARGET_URL_HTML_EN_9729");
		domainIdTestQueueNameMap.put(9731, "TEST_TARGET_URL_HTML_EN_9731");
		domainIdTestQueueNameMap.put(9732, "TEST_TARGET_URL_HTML_EN_9732");
		domainIdTestQueueNameMap.put(9736, "TEST_TARGET_URL_HTML_EN_9736");
		domainIdTestQueueNameMap.put(9738, "TEST_TARGET_URL_HTML_EN_9738");
		domainIdTestQueueNameMap.put(9739, "TEST_TARGET_URL_HTML_EN_9739");
		domainIdTestQueueNameMap.put(9740, "TEST_TARGET_URL_HTML_EN_9740");
		domainIdTestQueueNameMap.put(9742, "TEST_TARGET_URL_HTML_EN_9742");
		domainIdTestQueueNameMap.put(9743, "TEST_TARGET_URL_HTML_EN_9743");
		domainIdTestQueueNameMap.put(9752, "TEST_TARGET_URL_HTML_EN_9752");
		domainIdTestQueueNameMap.put(9756, "TEST_TARGET_URL_HTML_EN_9756");
		domainIdTestQueueNameMap.put(9757, "TEST_TARGET_URL_HTML_EN_9757");
		domainIdTestQueueNameMap.put(9758, "TEST_TARGET_URL_HTML_EN_9758");
		domainIdTestQueueNameMap.put(9761, "TEST_TARGET_URL_HTML_EN_9761");
		domainIdTestQueueNameMap.put(9762, "TEST_TARGET_URL_HTML_EN_9762");
		domainIdTestQueueNameMap.put(9763, "TEST_TARGET_URL_HTML_EN_9763");
		domainIdTestQueueNameMap.put(9764, "TEST_TARGET_URL_HTML_EN_9764");
		domainIdTestQueueNameMap.put(9765, "TEST_TARGET_URL_HTML_EN_9765");
		domainIdTestQueueNameMap.put(9766, "TEST_TARGET_URL_HTML_EN_9766");
		domainIdTestQueueNameMap.put(9768, "TEST_TARGET_URL_HTML_EN_9768");
		domainIdTestQueueNameMap.put(9769, "TEST_TARGET_URL_HTML_PT_9769");
		domainIdTestQueueNameMap.put(9770, "TEST_TARGET_URL_HTML_EN_9770");
		domainIdTestQueueNameMap.put(9771, "TEST_TARGET_URL_HTML_DE_9771");
		domainIdTestQueueNameMap.put(9773, "TEST_TARGET_URL_HTML_FR_9773");
		domainIdTestQueueNameMap.put(9774, "TEST_TARGET_URL_HTML_IT_9774");
		domainIdTestQueueNameMap.put(9775, "TEST_TARGET_URL_HTML_ZH-TW_9775");
		domainIdTestQueueNameMap.put(9776, "TEST_TARGET_URL_HTML_EN_9776");
		domainIdTestQueueNameMap.put(9777, "TEST_TARGET_URL_HTML_JA_9777");
		domainIdTestQueueNameMap.put(9779, "TEST_TARGET_URL_HTML_DE_9779");
		domainIdTestQueueNameMap.put(9780, "TEST_TARGET_URL_HTML_DE_9780");
		domainIdTestQueueNameMap.put(9783, "TEST_TARGET_URL_HTML_EN_9783");
		domainIdTestQueueNameMap.put(9784, "TEST_TARGET_URL_HTML_ES_9784");
		domainIdTestQueueNameMap.put(9785, "TEST_TARGET_URL_HTML_NL_9785");
		domainIdTestQueueNameMap.put(9788, "TEST_TARGET_URL_HTML_EN_9788");
		domainIdTestQueueNameMap.put(9790, "TEST_TARGET_URL_HTML_DE_9790");
		domainIdTestQueueNameMap.put(9791, "TEST_TARGET_URL_HTML_EN_9791");
		domainIdTestQueueNameMap.put(9792, "TEST_TARGET_URL_HTML_EN_9792");
		domainIdTestQueueNameMap.put(9793, "TEST_TARGET_URL_HTML_EN_9793");
		domainIdTestQueueNameMap.put(9794, "TEST_TARGET_URL_HTML_EN_9794");
		domainIdTestQueueNameMap.put(9795, "TEST_TARGET_URL_HTML_EN_9795");
		domainIdTestQueueNameMap.put(9796, "TEST_TARGET_URL_HTML_EN_9796");
		domainIdTestQueueNameMap.put(9797, "TEST_TARGET_URL_HTML_EN_9797");
		domainIdTestQueueNameMap.put(9798, "TEST_TARGET_URL_HTML_EN_9798");
		domainIdTestQueueNameMap.put(9803, "TEST_TARGET_URL_HTML_EN_9803");
		domainIdTestQueueNameMap.put(9807, "TEST_TARGET_URL_HTML_EN_9807");
		domainIdTestQueueNameMap.put(9808, "TEST_TARGET_URL_HTML_EN_9808");
		domainIdTestQueueNameMap.put(9809, "TEST_TARGET_URL_HTML_EN_9809");
		domainIdTestQueueNameMap.put(9810, "TEST_TARGET_URL_HTML_EN_9810");
		domainIdTestQueueNameMap.put(9811, "TEST_TARGET_URL_HTML_EN_9811");
		domainIdTestQueueNameMap.put(9812, "TEST_TARGET_URL_HTML_EN_9812");
		domainIdTestQueueNameMap.put(9813, "TEST_TARGET_URL_HTML_EN_9813");
		domainIdTestQueueNameMap.put(9819, "TEST_TARGET_URL_HTML_EN_9819");
		domainIdTestQueueNameMap.put(9820, "TEST_TARGET_URL_HTML_EN_9820");
		domainIdTestQueueNameMap.put(9824, "TEST_TARGET_URL_HTML_EN_9824");
		domainIdTestQueueNameMap.put(9829, "TEST_TARGET_URL_HTML_EN_9829");
		domainIdTestQueueNameMap.put(9830, "TEST_TARGET_URL_HTML_EN_9830");
		domainIdTestQueueNameMap.put(9832, "TEST_TARGET_URL_HTML_EN_9832");
		domainIdTestQueueNameMap.put(9834, "TEST_TARGET_URL_HTML_EN_9834");
		domainIdTestQueueNameMap.put(9835, "TEST_TARGET_URL_HTML_FR_9835");
		domainIdTestQueueNameMap.put(9836, "TEST_TARGET_URL_HTML_DE_9836");
		domainIdTestQueueNameMap.put(9837, "TEST_TARGET_URL_HTML_IT_9837");
		domainIdTestQueueNameMap.put(9838, "TEST_TARGET_URL_HTML_ES_9838");
		domainIdTestQueueNameMap.put(9839, "TEST_TARGET_URL_HTML_RU_9839");
		domainIdTestQueueNameMap.put(9840, "TEST_TARGET_URL_HTML_SV_9840");
		domainIdTestQueueNameMap.put(9841, "TEST_TARGET_URL_HTML_DA_9841");
		domainIdTestQueueNameMap.put(9842, "TEST_TARGET_URL_HTML_NL_9842");
		domainIdTestQueueNameMap.put(9860, "TEST_TARGET_URL_HTML_RO_9860");
		domainIdTestQueueNameMap.put(9866, "TEST_TARGET_URL_HTML_EN_9866");
		domainIdTestQueueNameMap.put(9867, "TEST_TARGET_URL_HTML_EN_9867");
		domainIdTestQueueNameMap.put(9869, "TEST_TARGET_URL_HTML_EN_9869");
		domainIdTestQueueNameMap.put(9872, "TEST_TARGET_URL_HTML_EN_9872");
		domainIdTestQueueNameMap.put(9876, "TEST_TARGET_URL_HTML_EN_9876");
		domainIdTestQueueNameMap.put(9879, "TEST_TARGET_URL_HTML_EN_9879");
		domainIdTestQueueNameMap.put(9884, "TEST_TARGET_URL_HTML_EN_9884");
		domainIdTestQueueNameMap.put(9887, "TEST_TARGET_URL_HTML_EN_9887");
		domainIdTestQueueNameMap.put(9888, "TEST_TARGET_URL_HTML_EN_9888");
		domainIdTestQueueNameMap.put(9890, "TEST_TARGET_URL_HTML_EN_9890");
		domainIdTestQueueNameMap.put(9891, "TEST_TARGET_URL_HTML_FR_9891");
		domainIdTestQueueNameMap.put(9892, "TEST_TARGET_URL_HTML_DE_9892");
		domainIdTestQueueNameMap.put(9893, "TEST_TARGET_URL_HTML_ES_9893");
		domainIdTestQueueNameMap.put(9894, "TEST_TARGET_URL_HTML_EN_9894");
		domainIdTestQueueNameMap.put(9912, "TEST_TARGET_URL_HTML_EN_9912");
		domainIdTestQueueNameMap.put(9913, "TEST_TARGET_URL_HTML_EN_9913");
		domainIdTestQueueNameMap.put(9920, "TEST_TARGET_URL_HTML_JA_9920");
		domainIdTestQueueNameMap.put(9921, "TEST_TARGET_URL_HTML_ZH_9921");
		domainIdTestQueueNameMap.put(9922, "TEST_TARGET_URL_HTML_KO_9922");
		domainIdTestQueueNameMap.put(9932, "TEST_TARGET_URL_HTML_VI_9932");
		domainIdTestQueueNameMap.put(9933, "TEST_TARGET_URL_HTML_EN_9933");
		domainIdTestQueueNameMap.put(9934, "TEST_TARGET_URL_HTML_EN_9934");
		domainIdTestQueueNameMap.put(9935, "TEST_TARGET_URL_HTML_EN_9935");
		domainIdTestQueueNameMap.put(9936, "TEST_TARGET_URL_HTML_EN_9936");
		domainIdTestQueueNameMap.put(9937, "TEST_TARGET_URL_HTML_EN_9937");
		domainIdTestQueueNameMap.put(9938, "TEST_TARGET_URL_HTML_EN_9938");
		domainIdTestQueueNameMap.put(9939, "TEST_TARGET_URL_HTML_EN_9939");
		domainIdTestQueueNameMap.put(9940, "TEST_TARGET_URL_HTML_EN_9940");
		domainIdTestQueueNameMap.put(9947, "TEST_TARGET_URL_HTML_EN_9947");
		domainIdTestQueueNameMap.put(9962, "TEST_TARGET_URL_HTML_EN_9962");
		domainIdTestQueueNameMap.put(9964, "TEST_TARGET_URL_HTML_EN_9964");
		domainIdTestQueueNameMap.put(9966, "TEST_TARGET_URL_HTML_EN_9966");
		domainIdTestQueueNameMap.put(9969, "TEST_TARGET_URL_HTML_EN_9969");
		domainIdTestQueueNameMap.put(9972, "TEST_TARGET_URL_HTML_EN_9972");
		domainIdTestQueueNameMap.put(9976, "TEST_TARGET_URL_HTML_EN_9976");
		domainIdTestQueueNameMap.put(9977, "TEST_TARGET_URL_HTML_EN_9977");
		domainIdTestQueueNameMap.put(9991, "TEST_TARGET_URL_HTML_EN_9991");
		domainIdTestQueueNameMap.put(9992, "TEST_TARGET_URL_HTML_EN_9992");
		domainIdTestQueueNameMap.put(9993, "TEST_TARGET_URL_HTML_EN_9993");
		domainIdTestQueueNameMap.put(9997, "TEST_TARGET_URL_HTML_EN_9997");
		domainIdTestQueueNameMap.put(9998, "TEST_TARGET_URL_HTML_EN_9998");
		domainIdTestQueueNameMap.put(9999, "TEST_TARGET_URL_HTML_EN_9999");
		domainIdTestQueueNameMap.put(10010, "TEST_TARGET_URL_HTML_EN_10010");
		domainIdTestQueueNameMap.put(10067, "TEST_TARGET_URL_HTML_EN_10067");
		domainIdTestQueueNameMap.put(10074, "TEST_TARGET_URL_HTML_EN_10074");
		domainIdTestQueueNameMap.put(10075, "TEST_TARGET_URL_HTML_EN_10075");
		domainIdTestQueueNameMap.put(10079, "TEST_TARGET_URL_HTML_EN_10079");
		domainIdTestQueueNameMap.put(10080, "TEST_TARGET_URL_HTML_ZH_10080");
		domainIdTestQueueNameMap.put(10081, "TEST_TARGET_URL_HTML_EN_10081");
		domainIdTestQueueNameMap.put(10085, "TEST_TARGET_URL_HTML_EN_10085");
		domainIdTestQueueNameMap.put(10086, "TEST_TARGET_URL_HTML_EN_10086");
		domainIdTestQueueNameMap.put(10087, "TEST_TARGET_URL_HTML_EN_10087");
		domainIdTestQueueNameMap.put(10088, "TEST_TARGET_URL_HTML_EN_10088");
		domainIdTestQueueNameMap.put(10100, "TEST_TARGET_URL_HTML_EN_10100");
		domainIdTestQueueNameMap.put(10104, "TEST_TARGET_URL_HTML_FI_10104");
		domainIdTestQueueNameMap.put(10108, "TEST_TARGET_URL_HTML_EN_10108");
		domainIdTestQueueNameMap.put(10109, "TEST_TARGET_URL_HTML_EN_10109");
		domainIdTestQueueNameMap.put(10110, "TEST_TARGET_URL_HTML_EN_10110");
		domainIdTestQueueNameMap.put(10111, "TEST_TARGET_URL_HTML_EN_10111");
		domainIdTestQueueNameMap.put(10114, "TEST_TARGET_URL_HTML_EN_10114");
		domainIdTestQueueNameMap.put(10121, "TEST_TARGET_URL_HTML_EN_10121");
		domainIdTestQueueNameMap.put(10123, "TEST_TARGET_URL_HTML_EN_10123");
		domainIdTestQueueNameMap.put(10128, "TEST_TARGET_URL_HTML_EN_10128");
		domainIdTestQueueNameMap.put(10134, "TEST_TARGET_URL_HTML_EN_10134");
		domainIdTestQueueNameMap.put(10136, "TEST_TARGET_URL_HTML_JA_10136");
		domainIdTestQueueNameMap.put(10141, "TEST_TARGET_URL_HTML_EN_10141");
		domainIdTestQueueNameMap.put(10163, "TEST_TARGET_URL_HTML_AR_10163");
		domainIdTestQueueNameMap.put(10165, "TEST_TARGET_URL_HTML_EN_10165");
		domainIdTestQueueNameMap.put(10167, "TEST_TARGET_URL_HTML_JA_10167");
		domainIdTestQueueNameMap.put(10169, "TEST_TARGET_URL_HTML_EN_10169");
		domainIdTestQueueNameMap.put(10170, "TEST_TARGET_URL_HTML_EN_10170");
		domainIdTestQueueNameMap.put(10171, "TEST_TARGET_URL_HTML_EN_10171");
		domainIdTestQueueNameMap.put(10175, "TEST_TARGET_URL_HTML_EN_10175");
		domainIdTestQueueNameMap.put(10176, "TEST_TARGET_URL_HTML_EN_10176");
		domainIdTestQueueNameMap.put(10178, "TEST_TARGET_URL_HTML_EN_10178");
		domainIdTestQueueNameMap.put(10179, "TEST_TARGET_URL_HTML_EN_10179");
		domainIdTestQueueNameMap.put(10180, "TEST_TARGET_URL_HTML_EN_10180");
		domainIdTestQueueNameMap.put(10232, "TEST_TARGET_URL_HTML_JA_10232");
		domainIdTestQueueNameMap.put(10234, "TEST_TARGET_URL_HTML_EN_10234");
		domainIdTestQueueNameMap.put(10237, "TEST_TARGET_URL_HTML_EN_10237");
		domainIdTestQueueNameMap.put(10239, "TEST_TARGET_URL_HTML_EN_10239");
		domainIdTestQueueNameMap.put(10242, "TEST_TARGET_URL_HTML_EN_10242");
		domainIdTestQueueNameMap.put(10243, "TEST_TARGET_URL_HTML_EN_10243");
		domainIdTestQueueNameMap.put(10244, "TEST_TARGET_URL_HTML_EN_10244");
		domainIdTestQueueNameMap.put(10245, "TEST_TARGET_URL_HTML_EN_10245");
		domainIdTestQueueNameMap.put(10246, "TEST_TARGET_URL_HTML_EN_10246");
		domainIdTestQueueNameMap.put(10247, "TEST_TARGET_URL_HTML_EN_10247");
		domainIdTestQueueNameMap.put(10248, "TEST_TARGET_URL_HTML_EN_10248");
		domainIdTestQueueNameMap.put(10249, "TEST_TARGET_URL_HTML_EN_10249");
		domainIdTestQueueNameMap.put(10250, "TEST_TARGET_URL_HTML_EN_10250");
		domainIdTestQueueNameMap.put(10259, "TEST_TARGET_URL_HTML_EN_10259");
		domainIdTestQueueNameMap.put(10260, "TEST_TARGET_URL_HTML_EN_10260");
		domainIdTestQueueNameMap.put(10261, "TEST_TARGET_URL_HTML_FR_10261");
		domainIdTestQueueNameMap.put(10267, "TEST_TARGET_URL_HTML_EN_10267");
		domainIdTestQueueNameMap.put(10268, "TEST_TARGET_URL_HTML_EN_10268");
		domainIdTestQueueNameMap.put(10269, "TEST_TARGET_URL_HTML_EN_10269");
		domainIdTestQueueNameMap.put(10273, "TEST_TARGET_URL_HTML_EN_10273");
		domainIdTestQueueNameMap.put(10277, "TEST_TARGET_URL_HTML_EN_10277");
		domainIdTestQueueNameMap.put(10280, "TEST_TARGET_URL_HTML_EN_10280");
		domainIdTestQueueNameMap.put(10281, "TEST_TARGET_URL_HTML_EN_10281");
		domainIdTestQueueNameMap.put(10284, "TEST_TARGET_URL_HTML_EN_10284");
		domainIdTestQueueNameMap.put(10285, "TEST_TARGET_URL_HTML_EN_10285");
		domainIdTestQueueNameMap.put(10286, "TEST_TARGET_URL_HTML_EN_10286");
		domainIdTestQueueNameMap.put(10287, "TEST_TARGET_URL_HTML_EN_10287");
		domainIdTestQueueNameMap.put(10293, "TEST_TARGET_URL_HTML_EN_10293");
		domainIdTestQueueNameMap.put(10297, "TEST_TARGET_URL_HTML_EN_10297");
		domainIdTestQueueNameMap.put(10298, "TEST_TARGET_URL_HTML_EN_10298");
		domainIdTestQueueNameMap.put(10307, "TEST_TARGET_URL_HTML_EL_10307");
		domainIdTestQueueNameMap.put(10309, "TEST_TARGET_URL_HTML_PT_10309");
		domainIdTestQueueNameMap.put(10310, "TEST_TARGET_URL_HTML_EN_10310");
		domainIdTestQueueNameMap.put(10311, "TEST_TARGET_URL_HTML_EN_10311");
		domainIdTestQueueNameMap.put(10312, "TEST_TARGET_URL_HTML_EN_10312");
		domainIdTestQueueNameMap.put(10313, "TEST_TARGET_URL_HTML_EN_10313");
		domainIdTestQueueNameMap.put(10314, "TEST_TARGET_URL_HTML_EN_10314");
		domainIdTestQueueNameMap.put(10316, "TEST_TARGET_URL_HTML_FR_10316");
		domainIdTestQueueNameMap.put(10323, "TEST_TARGET_URL_HTML_EN_10323");
		domainIdTestQueueNameMap.put(10324, "TEST_TARGET_URL_HTML_EN_10324");
		domainIdTestQueueNameMap.put(10339, "TEST_TARGET_URL_HTML_EN_10339");
		domainIdTestQueueNameMap.put(10340, "TEST_TARGET_URL_HTML_PL_10340");
		domainIdTestQueueNameMap.put(10344, "TEST_TARGET_URL_HTML_EN_10344");
		domainIdTestQueueNameMap.put(10349, "TEST_TARGET_URL_HTML_JA_10349");
		domainIdTestQueueNameMap.put(10350, "TEST_TARGET_URL_HTML_EN_10350");
		domainIdTestQueueNameMap.put(10351, "TEST_TARGET_URL_HTML_EN_10351");
		domainIdTestQueueNameMap.put(10353, "TEST_TARGET_URL_HTML_EN_10353");
		domainIdTestQueueNameMap.put(10354, "TEST_TARGET_URL_HTML_EN_10354");
		domainIdTestQueueNameMap.put(10355, "TEST_TARGET_URL_HTML_EN_10355");
		domainIdTestQueueNameMap.put(10356, "TEST_TARGET_URL_HTML_EN_10356");
		domainIdTestQueueNameMap.put(10357, "TEST_TARGET_URL_HTML_EN_10357");
		domainIdTestQueueNameMap.put(10358, "TEST_TARGET_URL_HTML_EN_10358");
		domainIdTestQueueNameMap.put(10360, "TEST_TARGET_URL_HTML_EN_10360");
		domainIdTestQueueNameMap.put(10361, "TEST_TARGET_URL_HTML_EN_10361");
		domainIdTestQueueNameMap.put(10362, "TEST_TARGET_URL_HTML_EN_10362");
		domainIdTestQueueNameMap.put(10363, "TEST_TARGET_URL_HTML_EN_10363");
		domainIdTestQueueNameMap.put(10364, "TEST_TARGET_URL_HTML_EN_10364");
		domainIdTestQueueNameMap.put(10365, "TEST_TARGET_URL_HTML_EN_10365");
		domainIdTestQueueNameMap.put(10366, "TEST_TARGET_URL_HTML_EN_10366");
		domainIdTestQueueNameMap.put(10367, "TEST_TARGET_URL_HTML_EN_10367");
		domainIdTestQueueNameMap.put(10373, "TEST_TARGET_URL_HTML_EN_10373");
		domainIdTestQueueNameMap.put(10374, "TEST_TARGET_URL_HTML_FR_10374");
		domainIdTestQueueNameMap.put(10376, "TEST_TARGET_URL_HTML_FR_10376");
		domainIdTestQueueNameMap.put(10377, "TEST_TARGET_URL_HTML_EN_10377");
		domainIdTestQueueNameMap.put(10378, "TEST_TARGET_URL_HTML_FR_10378");
		domainIdTestQueueNameMap.put(10379, "TEST_TARGET_URL_HTML_EN_10379");
		domainIdTestQueueNameMap.put(10384, "TEST_TARGET_URL_HTML_EN_10384");
		domainIdTestQueueNameMap.put(10385, "TEST_TARGET_URL_HTML_EN_10385");
		domainIdTestQueueNameMap.put(10386, "TEST_TARGET_URL_HTML_ES_10386");
		domainIdTestQueueNameMap.put(10387, "TEST_TARGET_URL_HTML_DE_10387");
		domainIdTestQueueNameMap.put(10388, "TEST_TARGET_URL_HTML_IT_10388");
		domainIdTestQueueNameMap.put(10389, "TEST_TARGET_URL_HTML_SV_10389");
		domainIdTestQueueNameMap.put(10390, "TEST_TARGET_URL_HTML_FR_10390");
		domainIdTestQueueNameMap.put(10391, "TEST_TARGET_URL_HTML_EN_10391");
		domainIdTestQueueNameMap.put(10392, "TEST_TARGET_URL_HTML_EN_10392");
		domainIdTestQueueNameMap.put(10394, "TEST_TARGET_URL_HTML_EN_10394");
		domainIdTestQueueNameMap.put(10395, "TEST_TARGET_URL_HTML_EN_10395");
		domainIdTestQueueNameMap.put(10396, "TEST_TARGET_URL_HTML_EN_10396");
		domainIdTestQueueNameMap.put(10397, "TEST_TARGET_URL_HTML_EN_10397");
		domainIdTestQueueNameMap.put(10398, "TEST_TARGET_URL_HTML_EN_10398");
		domainIdTestQueueNameMap.put(10401, "TEST_TARGET_URL_HTML_EN_10401");
		domainIdTestQueueNameMap.put(10419, "TEST_TARGET_URL_HTML_EN_10419");
		domainIdTestQueueNameMap.put(10420, "TEST_TARGET_URL_HTML_EN_10420");
		domainIdTestQueueNameMap.put(10432, "TEST_TARGET_URL_HTML_EN_10432");
		domainIdTestQueueNameMap.put(10443, "TEST_TARGET_URL_HTML_EN_10443");
		domainIdTestQueueNameMap.put(10448, "TEST_TARGET_URL_HTML_EN_10448");
		domainIdTestQueueNameMap.put(10449, "TEST_TARGET_URL_HTML_EN_10449");
		domainIdTestQueueNameMap.put(10451, "TEST_TARGET_URL_HTML_EN_10451");
		domainIdTestQueueNameMap.put(10452, "TEST_TARGET_URL_HTML_EN_10452");
		domainIdTestQueueNameMap.put(10468, "TEST_TARGET_URL_HTML_EN_10468");
		domainIdTestQueueNameMap.put(10469, "TEST_TARGET_URL_HTML_DE_10469");
		domainIdTestQueueNameMap.put(10470, "TEST_TARGET_URL_HTML_EN_10470");
		domainIdTestQueueNameMap.put(10471, "TEST_TARGET_URL_HTML_EN_10471");
		domainIdTestQueueNameMap.put(10480, "TEST_TARGET_URL_HTML_EN_10480");
		domainIdTestQueueNameMap.put(10481, "TEST_TARGET_URL_HTML_ZH_10481");
		domainIdTestQueueNameMap.put(10482, "TEST_TARGET_URL_HTML_EN_10482");
		domainIdTestQueueNameMap.put(10492, "TEST_TARGET_URL_HTML_EN_10492");
		domainIdTestQueueNameMap.put(10497, "TEST_TARGET_URL_HTML_EN_10497");
		domainIdTestQueueNameMap.put(10502, "TEST_TARGET_URL_HTML_EN_10502");
		domainIdTestQueueNameMap.put(10507, "TEST_TARGET_URL_HTML_EN_10507");
		domainIdTestQueueNameMap.put(10538, "TEST_TARGET_URL_HTML_JA_10538");
		domainIdTestQueueNameMap.put(10542, "TEST_TARGET_URL_HTML_EN_10542");
		domainIdTestQueueNameMap.put(10544, "TEST_TARGET_URL_HTML_EN_10544");
		domainIdTestQueueNameMap.put(10545, "TEST_TARGET_URL_HTML_EN_10545");
		domainIdTestQueueNameMap.put(10546, "TEST_TARGET_URL_HTML_EN_10546");
		domainIdTestQueueNameMap.put(10547, "TEST_TARGET_URL_HTML_EN_10547");
		domainIdTestQueueNameMap.put(10548, "TEST_TARGET_URL_HTML_EN_10548");
		domainIdTestQueueNameMap.put(10549, "TEST_TARGET_URL_HTML_EN_10549");
		domainIdTestQueueNameMap.put(10551, "TEST_TARGET_URL_HTML_EN_10551");
		domainIdTestQueueNameMap.put(10585, "TEST_TARGET_URL_HTML_EN_10585");
		domainIdTestQueueNameMap.put(10589, "TEST_TARGET_URL_HTML_EN_10589");
		domainIdTestQueueNameMap.put(10594, "TEST_TARGET_URL_HTML_EN_10594");
		domainIdTestQueueNameMap.put(10595, "TEST_TARGET_URL_HTML_DE_10595");
		domainIdTestQueueNameMap.put(10596, "TEST_TARGET_URL_HTML_EN_10596");
		domainIdTestQueueNameMap.put(10602, "TEST_TARGET_URL_HTML_EN_10602");
		domainIdTestQueueNameMap.put(10614, "TEST_TARGET_URL_HTML_EN_10614");
		domainIdTestQueueNameMap.put(10634, "TEST_TARGET_URL_HTML_EN_10634");
		domainIdTestQueueNameMap.put(10636, "TEST_TARGET_URL_HTML_EN_10636");
		domainIdTestQueueNameMap.put(10637, "TEST_TARGET_URL_HTML_EN_10637");
		domainIdTestQueueNameMap.put(10638, "TEST_TARGET_URL_HTML_EN_10638");
		domainIdTestQueueNameMap.put(10645, "TEST_TARGET_URL_HTML_EN_10645");
		domainIdTestQueueNameMap.put(10647, "TEST_TARGET_URL_HTML_EN_10647");
		domainIdTestQueueNameMap.put(10648, "TEST_TARGET_URL_HTML_EN_10648");
		domainIdTestQueueNameMap.put(10650, "TEST_TARGET_URL_HTML_EN_10650");
		domainIdTestQueueNameMap.put(10651, "TEST_TARGET_URL_HTML_EN_10651");
		domainIdTestQueueNameMap.put(10654, "TEST_TARGET_URL_HTML_EN_10654");
		domainIdTestQueueNameMap.put(10656, "TEST_TARGET_URL_HTML_EN_10656");
		domainIdTestQueueNameMap.put(10657, "TEST_TARGET_URL_HTML_FR_10657");
		domainIdTestQueueNameMap.put(10692, "TEST_TARGET_URL_HTML_JA_10692");
		domainIdTestQueueNameMap.put(10701, "TEST_TARGET_URL_HTML_EN_10701");
		domainIdTestQueueNameMap.put(10702, "TEST_TARGET_URL_HTML_EN_10702");
		domainIdTestQueueNameMap.put(10712, "TEST_TARGET_URL_HTML_EN_10712");
		domainIdTestQueueNameMap.put(10715, "TEST_TARGET_URL_HTML_EN_10715");
		domainIdTestQueueNameMap.put(10716, "TEST_TARGET_URL_HTML_EN_10716");
		domainIdTestQueueNameMap.put(10717, "TEST_TARGET_URL_HTML_EN_10717");
		domainIdTestQueueNameMap.put(10718, "TEST_TARGET_URL_HTML_EN_10718");
		domainIdTestQueueNameMap.put(10719, "TEST_TARGET_URL_HTML_EN_10719");
		domainIdTestQueueNameMap.put(10721, "TEST_TARGET_URL_HTML_EN_10721");
		domainIdTestQueueNameMap.put(10722, "TEST_TARGET_URL_HTML_EN_10722");
		domainIdTestQueueNameMap.put(10727, "TEST_TARGET_URL_HTML_PT_10727");
		domainIdTestQueueNameMap.put(10732, "TEST_TARGET_URL_HTML_EN_10732");
		domainIdTestQueueNameMap.put(10735, "TEST_TARGET_URL_HTML_EN_10735");
		domainIdTestQueueNameMap.put(10736, "TEST_TARGET_URL_HTML_EN_10736");
		domainIdTestQueueNameMap.put(10744, "TEST_TARGET_URL_HTML_EN_10744");
		domainIdTestQueueNameMap.put(10745, "TEST_TARGET_URL_HTML_ES_10745");
		domainIdTestQueueNameMap.put(10751, "TEST_TARGET_URL_HTML_EN_10751");
		domainIdTestQueueNameMap.put(10752, "TEST_TARGET_URL_HTML_EN_10752");
		domainIdTestQueueNameMap.put(10754, "TEST_TARGET_URL_HTML_EN_10754");
		domainIdTestQueueNameMap.put(10755, "TEST_TARGET_URL_HTML_EN_10755");
		domainIdTestQueueNameMap.put(10756, "TEST_TARGET_URL_HTML_EN_10756");
		domainIdTestQueueNameMap.put(10757, "TEST_TARGET_URL_HTML_EN_10757");
		domainIdTestQueueNameMap.put(10758, "TEST_TARGET_URL_HTML_EN_10758");
		domainIdTestQueueNameMap.put(10760, "TEST_TARGET_URL_HTML_EN_10760");
		domainIdTestQueueNameMap.put(10761, "TEST_TARGET_URL_HTML_EN_10761");
		domainIdTestQueueNameMap.put(10763, "TEST_TARGET_URL_HTML_EN_10763");
		domainIdTestQueueNameMap.put(10764, "TEST_TARGET_URL_HTML_EN_10764");
		domainIdTestQueueNameMap.put(10766, "TEST_TARGET_URL_HTML_EN_10766");
		domainIdTestQueueNameMap.put(10767, "TEST_TARGET_URL_HTML_EN_10767");
		domainIdTestQueueNameMap.put(10769, "TEST_TARGET_URL_HTML_EN_10769");
		domainIdTestQueueNameMap.put(10770, "TEST_TARGET_URL_HTML_EN_10770");
		domainIdTestQueueNameMap.put(10771, "TEST_TARGET_URL_HTML_EN_10771");
		domainIdTestQueueNameMap.put(10772, "TEST_TARGET_URL_HTML_EN_10772");
		domainIdTestQueueNameMap.put(10773, "TEST_TARGET_URL_HTML_EN_10773");
		domainIdTestQueueNameMap.put(10777, "TEST_TARGET_URL_HTML_EN_10777");
		domainIdTestQueueNameMap.put(10778, "TEST_TARGET_URL_HTML_EN_10778");
		domainIdTestQueueNameMap.put(10780, "TEST_TARGET_URL_HTML_EN_10780");
		domainIdTestQueueNameMap.put(10781, "TEST_TARGET_URL_HTML_EN_10781");
		domainIdTestQueueNameMap.put(10786, "TEST_TARGET_URL_HTML_NO_10786");
		domainIdTestQueueNameMap.put(10787, "TEST_TARGET_URL_HTML_DE_10787");
		domainIdTestQueueNameMap.put(10788, "TEST_TARGET_URL_HTML_SV_10788");
		domainIdTestQueueNameMap.put(10789, "TEST_TARGET_URL_HTML_FI_10789");
		domainIdTestQueueNameMap.put(10790, "TEST_TARGET_URL_HTML_EN_10790");
		domainIdTestQueueNameMap.put(10795, "TEST_TARGET_URL_HTML_FR_10795");
		domainIdTestQueueNameMap.put(10796, "TEST_TARGET_URL_HTML_EN_10796");
		domainIdTestQueueNameMap.put(10802, "TEST_TARGET_URL_HTML_EN_10802");
		domainIdTestQueueNameMap.put(10804, "TEST_TARGET_URL_HTML_EN_10804");
		domainIdTestQueueNameMap.put(10813, "TEST_TARGET_URL_HTML_EN_10813");
		domainIdTestQueueNameMap.put(10815, "TEST_TARGET_URL_HTML_EN_10815");
		domainIdTestQueueNameMap.put(10822, "TEST_TARGET_URL_HTML_EN_10822");
		domainIdTestQueueNameMap.put(10825, "TEST_TARGET_URL_HTML_EN_10825");
		domainIdTestQueueNameMap.put(10828, "TEST_TARGET_URL_HTML_EN_10828");
		domainIdTestQueueNameMap.put(10829, "TEST_TARGET_URL_HTML_JA_10829");
		domainIdTestQueueNameMap.put(10830, "TEST_TARGET_URL_HTML_EN_10830");
		domainIdTestQueueNameMap.put(10861, "TEST_TARGET_URL_HTML_EN_10861");
		domainIdTestQueueNameMap.put(10865, "TEST_TARGET_URL_HTML_EN_10865");
		domainIdTestQueueNameMap.put(10873, "TEST_TARGET_URL_HTML_JA_10873");
		domainIdTestQueueNameMap.put(10874, "TEST_TARGET_URL_HTML_EN_10874");
		domainIdTestQueueNameMap.put(10875, "TEST_TARGET_URL_HTML_EN_10875");
		domainIdTestQueueNameMap.put(10881, "TEST_TARGET_URL_HTML_EN_10881");
		domainIdTestQueueNameMap.put(10882, "TEST_TARGET_URL_HTML_EN_10882");
		domainIdTestQueueNameMap.put(10883, "TEST_TARGET_URL_HTML_EN_10883");
		domainIdTestQueueNameMap.put(10884, "TEST_TARGET_URL_HTML_EN_10884");
		domainIdTestQueueNameMap.put(10885, "TEST_TARGET_URL_HTML_EN_10885");
		domainIdTestQueueNameMap.put(10886, "TEST_TARGET_URL_HTML_EN_10886");
		domainIdTestQueueNameMap.put(10887, "TEST_TARGET_URL_HTML_EN_10887");
		domainIdTestQueueNameMap.put(10888, "TEST_TARGET_URL_HTML_EN_10888");
		domainIdTestQueueNameMap.put(10889, "TEST_TARGET_URL_HTML_EN_10889");
		domainIdTestQueueNameMap.put(10896, "TEST_TARGET_URL_HTML_NL_10896");
		domainIdTestQueueNameMap.put(10906, "TEST_TARGET_URL_HTML_EN_10906");
		domainIdTestQueueNameMap.put(10907, "TEST_TARGET_URL_HTML_EN_10907");
		domainIdTestQueueNameMap.put(10909, "TEST_TARGET_URL_HTML_EN_10909");
		domainIdTestQueueNameMap.put(10910, "TEST_TARGET_URL_HTML_JA_10910");
		domainIdTestQueueNameMap.put(10915, "TEST_TARGET_URL_HTML_EN_10915");
		domainIdTestQueueNameMap.put(10918, "TEST_TARGET_URL_HTML_EN_10918");
		domainIdTestQueueNameMap.put(10919, "TEST_TARGET_URL_HTML_EN_10919");
		domainIdTestQueueNameMap.put(10925, "TEST_TARGET_URL_HTML_EN_10925");
		domainIdTestQueueNameMap.put(10926, "TEST_TARGET_URL_HTML_EN_10926");
		domainIdTestQueueNameMap.put(10932, "TEST_TARGET_URL_HTML_EN_10932");
		domainIdTestQueueNameMap.put(10933, "TEST_TARGET_URL_HTML_EN_10933");
		domainIdTestQueueNameMap.put(10935, "TEST_TARGET_URL_HTML_EN_10935");
		domainIdTestQueueNameMap.put(10936, "TEST_TARGET_URL_HTML_EN_10936");
		domainIdTestQueueNameMap.put(10937, "TEST_TARGET_URL_HTML_RU_10937");
		domainIdTestQueueNameMap.put(10940, "TEST_TARGET_URL_HTML_EN_10940");
		domainIdTestQueueNameMap.put(10942, "TEST_TARGET_URL_HTML_EN_10942");
		domainIdTestQueueNameMap.put(10943, "TEST_TARGET_URL_HTML_EN_10943");
		domainIdTestQueueNameMap.put(10944, "TEST_TARGET_URL_HTML_EN_10944");
		domainIdTestQueueNameMap.put(10945, "TEST_TARGET_URL_HTML_EN_10945");
		domainIdTestQueueNameMap.put(10946, "TEST_TARGET_URL_HTML_EN_10946");
		domainIdTestQueueNameMap.put(10947, "TEST_TARGET_URL_HTML_EN_10947");
		domainIdTestQueueNameMap.put(10948, "TEST_TARGET_URL_HTML_EN_10948");
		domainIdTestQueueNameMap.put(10950, "TEST_TARGET_URL_HTML_EN_10950");
		domainIdTestQueueNameMap.put(10951, "TEST_TARGET_URL_HTML_EN_10951");
		domainIdTestQueueNameMap.put(10952, "TEST_TARGET_URL_HTML_EN_10952");
		domainIdTestQueueNameMap.put(10953, "TEST_TARGET_URL_HTML_EN_10953");
		domainIdTestQueueNameMap.put(10954, "TEST_TARGET_URL_HTML_EN_10954");
		domainIdTestQueueNameMap.put(10956, "TEST_TARGET_URL_HTML_EN_10956");
		domainIdTestQueueNameMap.put(10959, "TEST_TARGET_URL_HTML_EN_10959");
		domainIdTestQueueNameMap.put(10966, "TEST_TARGET_URL_HTML_EN_10966");
		domainIdTestQueueNameMap.put(10968, "TEST_TARGET_URL_HTML_EN_10968");
		domainIdTestQueueNameMap.put(10970, "TEST_TARGET_URL_HTML_FR_10970");
		domainIdTestQueueNameMap.put(10971, "TEST_TARGET_URL_HTML_EN_10971");
		domainIdTestQueueNameMap.put(10973, "TEST_TARGET_URL_HTML_EN_10973");
		domainIdTestQueueNameMap.put(10974, "TEST_TARGET_URL_HTML_EN_10974");
		domainIdTestQueueNameMap.put(10975, "TEST_TARGET_URL_HTML_EN_10975");
		domainIdTestQueueNameMap.put(10976, "TEST_TARGET_URL_HTML_EN_10976");
		domainIdTestQueueNameMap.put(10981, "TEST_TARGET_URL_HTML_EN_10981");
		domainIdTestQueueNameMap.put(10993, "TEST_TARGET_URL_HTML_EN_10993");
		domainIdTestQueueNameMap.put(10995, "TEST_TARGET_URL_HTML_EN_10995");
		domainIdTestQueueNameMap.put(10996, "TEST_TARGET_URL_HTML_EN_10996");
		domainIdTestQueueNameMap.put(10997, "TEST_TARGET_URL_HTML_EN_10997");
		domainIdTestQueueNameMap.put(11000, "TEST_TARGET_URL_HTML_EN_11000");
		domainIdTestQueueNameMap.put(11004, "TEST_TARGET_URL_HTML_EN_11004");
		domainIdTestQueueNameMap.put(11005, "TEST_TARGET_URL_HTML_EN_11005");
		domainIdTestQueueNameMap.put(11006, "TEST_TARGET_URL_HTML_EN_11006");
		domainIdTestQueueNameMap.put(11007, "TEST_TARGET_URL_HTML_EN_11007");
		domainIdTestQueueNameMap.put(11029, "TEST_TARGET_URL_HTML_EN_11029");
		domainIdTestQueueNameMap.put(11032, "TEST_TARGET_URL_HTML_EN_11032");
		domainIdTestQueueNameMap.put(11033, "TEST_TARGET_URL_HTML_DE_11033");
		domainIdTestQueueNameMap.put(11034, "TEST_TARGET_URL_HTML_ES_11034");
		domainIdTestQueueNameMap.put(11035, "TEST_TARGET_URL_HTML_FR_11035");
		domainIdTestQueueNameMap.put(11036, "TEST_TARGET_URL_HTML_IT_11036");
		domainIdTestQueueNameMap.put(11037, "TEST_TARGET_URL_HTML_PL_11037");
		domainIdTestQueueNameMap.put(11038, "TEST_TARGET_URL_HTML_EN_11038");
		domainIdTestQueueNameMap.put(11039, "TEST_TARGET_URL_HTML_EN_11039");
		domainIdTestQueueNameMap.put(11041, "TEST_TARGET_URL_HTML_EN_11041");
		domainIdTestQueueNameMap.put(11042, "TEST_TARGET_URL_HTML_EN_11042");
		domainIdTestQueueNameMap.put(11044, "TEST_TARGET_URL_HTML_EN_11044");
		domainIdTestQueueNameMap.put(11047, "TEST_TARGET_URL_HTML_EN_11047");
		domainIdTestQueueNameMap.put(11048, "TEST_TARGET_URL_HTML_EN_11048");
		domainIdTestQueueNameMap.put(11050, "TEST_TARGET_URL_HTML_EN_11050");
		domainIdTestQueueNameMap.put(11052, "TEST_TARGET_URL_HTML_EN_11052");
		domainIdTestQueueNameMap.put(11054, "TEST_TARGET_URL_HTML_EN_11054");
		domainIdTestQueueNameMap.put(11055, "TEST_TARGET_URL_HTML_EN_11055");
		domainIdTestQueueNameMap.put(11056, "TEST_TARGET_URL_HTML_EN_11056");
		domainIdTestQueueNameMap.put(11057, "TEST_TARGET_URL_HTML_EN_11057");
		domainIdTestQueueNameMap.put(11058, "TEST_TARGET_URL_HTML_EN_11058");
		domainIdTestQueueNameMap.put(11059, "TEST_TARGET_URL_HTML_EN_11059");
		domainIdTestQueueNameMap.put(11060, "TEST_TARGET_URL_HTML_EN_11060");
		domainIdTestQueueNameMap.put(11061, "TEST_TARGET_URL_HTML_EN_11061");
		domainIdTestQueueNameMap.put(11062, "TEST_TARGET_URL_HTML_EN_11062");
		domainIdTestQueueNameMap.put(11063, "TEST_TARGET_URL_HTML_EN_11063");
		domainIdTestQueueNameMap.put(11064, "TEST_TARGET_URL_HTML_EN_11064");
		domainIdTestQueueNameMap.put(11065, "TEST_TARGET_URL_HTML_PT_11065");
		domainIdTestQueueNameMap.put(11066, "TEST_TARGET_URL_HTML_EN_11066");
		domainIdTestQueueNameMap.put(11067, "TEST_TARGET_URL_HTML_EN_11067");
		domainIdTestQueueNameMap.put(11069, "TEST_TARGET_URL_HTML_EN_11069");
		domainIdTestQueueNameMap.put(11070, "TEST_TARGET_URL_HTML_EN_11070");
		domainIdTestQueueNameMap.put(11073, "TEST_TARGET_URL_HTML_EN_11073");
		domainIdTestQueueNameMap.put(11075, "TEST_TARGET_URL_HTML_EN_11075");
		domainIdTestQueueNameMap.put(11078, "TEST_TARGET_URL_HTML_EN_11078");
		domainIdTestQueueNameMap.put(11079, "TEST_TARGET_URL_HTML_EN_11079");
		domainIdTestQueueNameMap.put(11081, "TEST_TARGET_URL_HTML_EN_11081");
		domainIdTestQueueNameMap.put(11083, "TEST_TARGET_URL_HTML_EN_11083");
		domainIdTestQueueNameMap.put(11086, "TEST_TARGET_URL_HTML_EN_11086");
		domainIdTestQueueNameMap.put(11088, "TEST_TARGET_URL_HTML_EN_11088");
		domainIdTestQueueNameMap.put(11089, "TEST_TARGET_URL_HTML_EN_11089");
		domainIdTestQueueNameMap.put(11090, "TEST_TARGET_URL_HTML_PT_11090");
		domainIdTestQueueNameMap.put(11091, "TEST_TARGET_URL_HTML_ES_11091");
		domainIdTestQueueNameMap.put(11092, "TEST_TARGET_URL_HTML_ES_11092");
		domainIdTestQueueNameMap.put(11093, "TEST_TARGET_URL_HTML_ES_11093");
		domainIdTestQueueNameMap.put(11095, "TEST_TARGET_URL_HTML_EN_11095");
		domainIdTestQueueNameMap.put(11096, "TEST_TARGET_URL_HTML_EN_11096");
		domainIdTestQueueNameMap.put(11103, "TEST_TARGET_URL_HTML_EN_11103");
		domainIdTestQueueNameMap.put(11104, "TEST_TARGET_URL_HTML_ZH_11104");
		domainIdTestQueueNameMap.put(11105, "TEST_TARGET_URL_HTML_MS_11105");
		domainIdTestQueueNameMap.put(11106, "TEST_TARGET_URL_HTML_EN_11106");
		domainIdTestQueueNameMap.put(11107, "TEST_TARGET_URL_HTML_EN_11107");
		domainIdTestQueueNameMap.put(11108, "TEST_TARGET_URL_HTML_ID_11108");
		domainIdTestQueueNameMap.put(11109, "TEST_TARGET_URL_HTML_ZH_11109");
		domainIdTestQueueNameMap.put(11110, "TEST_TARGET_URL_HTML_KM_11110");
		domainIdTestQueueNameMap.put(11111, "TEST_TARGET_URL_HTML_KO_11111");
		domainIdTestQueueNameMap.put(11112, "TEST_TARGET_URL_HTML_EN_11112");
		domainIdTestQueueNameMap.put(11113, "TEST_TARGET_URL_HTML_TH_11113");
		domainIdTestQueueNameMap.put(11114, "TEST_TARGET_URL_HTML_VI_11114");
		domainIdTestQueueNameMap.put(11117, "TEST_TARGET_URL_HTML_EN_11117");
		domainIdTestQueueNameMap.put(11122, "TEST_TARGET_URL_HTML_ZH-TW_11122");
		domainIdTestQueueNameMap.put(11125, "TEST_TARGET_URL_HTML_EN_11125");
		domainIdTestQueueNameMap.put(11126, "TEST_TARGET_URL_HTML_EN_11126");
		domainIdTestQueueNameMap.put(11130, "TEST_TARGET_URL_HTML_EN_11130");
		domainIdTestQueueNameMap.put(11131, "TEST_TARGET_URL_HTML_EN_11131");
		domainIdTestQueueNameMap.put(11135, "TEST_TARGET_URL_HTML_EN_11135");
		domainIdTestQueueNameMap.put(11136, "TEST_TARGET_URL_HTML_EN_11136");
		domainIdTestQueueNameMap.put(11141, "TEST_TARGET_URL_HTML_EN_11141");
		domainIdTestQueueNameMap.put(11142, "TEST_TARGET_URL_HTML_EN_11142");
		domainIdTestQueueNameMap.put(11166, "TEST_TARGET_URL_HTML_EN_11166");
		domainIdTestQueueNameMap.put(11169, "TEST_TARGET_URL_HTML_EN_11169");
		domainIdTestQueueNameMap.put(11170, "TEST_TARGET_URL_HTML_EN_11170");
		domainIdTestQueueNameMap.put(11173, "TEST_TARGET_URL_HTML_EN_11173");
		domainIdTestQueueNameMap.put(11174, "TEST_TARGET_URL_HTML_EN_11174");
		domainIdTestQueueNameMap.put(11185, "TEST_TARGET_URL_HTML_EN_11185");
		domainIdTestQueueNameMap.put(11186, "TEST_TARGET_URL_HTML_EN_11186");
		domainIdTestQueueNameMap.put(11187, "TEST_TARGET_URL_HTML_EN_11187");
		domainIdTestQueueNameMap.put(11192, "TEST_TARGET_URL_HTML_EN_11192");
		domainIdTestQueueNameMap.put(11193, "TEST_TARGET_URL_HTML_EN_11193");
		domainIdTestQueueNameMap.put(11194, "TEST_TARGET_URL_HTML_EN_11194");
		domainIdTestQueueNameMap.put(11195, "TEST_TARGET_URL_HTML_EN_11195");
		domainIdTestQueueNameMap.put(11196, "TEST_TARGET_URL_HTML_EN_11196");
		domainIdTestQueueNameMap.put(11197, "TEST_TARGET_URL_HTML_EN_11197");
		domainIdTestQueueNameMap.put(11198, "TEST_TARGET_URL_HTML_EN_11198");
		domainIdTestQueueNameMap.put(11199, "TEST_TARGET_URL_HTML_EN_11199");
		domainIdTestQueueNameMap.put(11200, "TEST_TARGET_URL_HTML_EN_11200");
		domainIdTestQueueNameMap.put(11201, "TEST_TARGET_URL_HTML_EN_11201");
		domainIdTestQueueNameMap.put(11204, "TEST_TARGET_URL_HTML_EN_11204");
		domainIdTestQueueNameMap.put(11205, "TEST_TARGET_URL_HTML_EN_11205");
		domainIdTestQueueNameMap.put(11206, "TEST_TARGET_URL_HTML_EN_11206");
		domainIdTestQueueNameMap.put(11207, "TEST_TARGET_URL_HTML_EN_11207");
		domainIdTestQueueNameMap.put(11212, "TEST_TARGET_URL_HTML_EN_11212");
		domainIdTestQueueNameMap.put(11213, "TEST_TARGET_URL_HTML_EN_11213");
		domainIdTestQueueNameMap.put(11217, "TEST_TARGET_URL_HTML_EN_11217");
		domainIdTestQueueNameMap.put(11223, "TEST_TARGET_URL_HTML_EN_11223");
		domainIdTestQueueNameMap.put(11224, "TEST_TARGET_URL_HTML_EN_11224");
		domainIdTestQueueNameMap.put(11228, "TEST_TARGET_URL_HTML_EN_11228");
		domainIdTestQueueNameMap.put(11232, "TEST_TARGET_URL_HTML_EN_11232");
		domainIdTestQueueNameMap.put(11233, "TEST_TARGET_URL_HTML_EN_11233");
		domainIdTestQueueNameMap.put(11235, "TEST_TARGET_URL_HTML_EN_11235");
		domainIdTestQueueNameMap.put(11250, "TEST_TARGET_URL_HTML_DE_11250");
		domainIdTestQueueNameMap.put(11251, "TEST_TARGET_URL_HTML_EN_11251");
		domainIdTestQueueNameMap.put(11254, "TEST_TARGET_URL_HTML_EN_11254");
		domainIdTestQueueNameMap.put(11255, "TEST_TARGET_URL_HTML_EN_11255");
		domainIdTestQueueNameMap.put(11256, "TEST_TARGET_URL_HTML_EN_11256");
		domainIdTestQueueNameMap.put(11257, "TEST_TARGET_URL_HTML_EN_11257");
		domainIdTestQueueNameMap.put(11259, "TEST_TARGET_URL_HTML_EN_11259");
		domainIdTestQueueNameMap.put(11260, "TEST_TARGET_URL_HTML_EN_11260");
		domainIdTestQueueNameMap.put(11262, "TEST_TARGET_URL_HTML_FR_11262");
		domainIdTestQueueNameMap.put(11264, "TEST_TARGET_URL_HTML_EN_11264");
		domainIdTestQueueNameMap.put(11267, "TEST_TARGET_URL_HTML_PT_11267");
		domainIdTestQueueNameMap.put(11268, "TEST_TARGET_URL_HTML_EN_11268");
		domainIdTestQueueNameMap.put(11275, "TEST_TARGET_URL_HTML_EN_11275");
		domainIdTestQueueNameMap.put(11276, "TEST_TARGET_URL_HTML_EN_11276");
		domainIdTestQueueNameMap.put(11281, "TEST_TARGET_URL_HTML_EN_11281");
		domainIdTestQueueNameMap.put(11282, "TEST_TARGET_URL_HTML_PL_11282");
		domainIdTestQueueNameMap.put(11283, "TEST_TARGET_URL_HTML_FR_11283");
		domainIdTestQueueNameMap.put(11284, "TEST_TARGET_URL_HTML_PT_11284");
		domainIdTestQueueNameMap.put(11285, "TEST_TARGET_URL_HTML_FR_11285");
		domainIdTestQueueNameMap.put(11286, "TEST_TARGET_URL_HTML_EN_11286");
		domainIdTestQueueNameMap.put(11287, "TEST_TARGET_URL_HTML_DE_11287");
		domainIdTestQueueNameMap.put(11288, "TEST_TARGET_URL_HTML_DE_11288");
		domainIdTestQueueNameMap.put(11289, "TEST_TARGET_URL_HTML_ES_11289");
		domainIdTestQueueNameMap.put(11290, "TEST_TARGET_URL_HTML_FR_11290");
		domainIdTestQueueNameMap.put(11291, "TEST_TARGET_URL_HTML_EN_11291");
		domainIdTestQueueNameMap.put(11292, "TEST_TARGET_URL_HTML_EN_11292");
		domainIdTestQueueNameMap.put(11293, "TEST_TARGET_URL_HTML_EN_11293");
		domainIdTestQueueNameMap.put(11294, "TEST_TARGET_URL_HTML_EN_11294");
		domainIdTestQueueNameMap.put(11295, "TEST_TARGET_URL_HTML_IT_11295");
		domainIdTestQueueNameMap.put(11296, "TEST_TARGET_URL_HTML_ES_11296");
		domainIdTestQueueNameMap.put(11297, "TEST_TARGET_URL_HTML_SV_11297");
		domainIdTestQueueNameMap.put(11298, "TEST_TARGET_URL_HTML_EN_11298");
		domainIdTestQueueNameMap.put(11300, "TEST_TARGET_URL_HTML_EN_11300");
		domainIdTestQueueNameMap.put(11310, "TEST_TARGET_URL_HTML_EN_11310");
		domainIdTestQueueNameMap.put(11311, "TEST_TARGET_URL_HTML_EN_11311");
		domainIdTestQueueNameMap.put(11313, "TEST_TARGET_URL_HTML_ES_11313");
		domainIdTestQueueNameMap.put(11318, "TEST_TARGET_URL_HTML_PT_11318");
		domainIdTestQueueNameMap.put(11319, "TEST_TARGET_URL_HTML_EN_11319");
		domainIdTestQueueNameMap.put(11333, "TEST_TARGET_URL_HTML_EN_11333");
		domainIdTestQueueNameMap.put(11337, "TEST_TARGET_URL_HTML_EN_11337");
		domainIdTestQueueNameMap.put(11338, "TEST_TARGET_URL_HTML_EN_11338");
		domainIdTestQueueNameMap.put(11342, "TEST_TARGET_URL_HTML_PT_11342");
		domainIdTestQueueNameMap.put(11343, "TEST_TARGET_URL_HTML_NL_11343");
		domainIdTestQueueNameMap.put(11344, "TEST_TARGET_URL_HTML_ZH-TW_11344");
		domainIdTestQueueNameMap.put(11345, "TEST_TARGET_URL_HTML_EN_11345");
		domainIdTestQueueNameMap.put(11346, "TEST_TARGET_URL_HTML_EN_11346");
		domainIdTestQueueNameMap.put(11347, "TEST_TARGET_URL_HTML_EN_11347");
		domainIdTestQueueNameMap.put(11351, "TEST_TARGET_URL_HTML_EN_11351");
		domainIdTestQueueNameMap.put(11355, "TEST_TARGET_URL_HTML_EN_11355");
		domainIdTestQueueNameMap.put(11356, "TEST_TARGET_URL_HTML_EN_11356");
		domainIdTestQueueNameMap.put(11364, "TEST_TARGET_URL_HTML_EN_11364");
		domainIdTestQueueNameMap.put(11365, "TEST_TARGET_URL_HTML_EN_11365");
		domainIdTestQueueNameMap.put(11366, "TEST_TARGET_URL_HTML_EN_11366");
		domainIdTestQueueNameMap.put(11368, "TEST_TARGET_URL_HTML_EN_11368");
		domainIdTestQueueNameMap.put(11370, "TEST_TARGET_URL_HTML_EN_11370");
		domainIdTestQueueNameMap.put(11371, "TEST_TARGET_URL_HTML_JA_11371");
		domainIdTestQueueNameMap.put(11372, "TEST_TARGET_URL_HTML_EN_11372");
		domainIdTestQueueNameMap.put(11373, "TEST_TARGET_URL_HTML_EN_11373");
		domainIdTestQueueNameMap.put(11374, "TEST_TARGET_URL_HTML_EN_11374");
		domainIdTestQueueNameMap.put(11375, "TEST_TARGET_URL_HTML_EN_11375");
		domainIdTestQueueNameMap.put(11376, "TEST_TARGET_URL_HTML_EN_11376");
		domainIdTestQueueNameMap.put(11377, "TEST_TARGET_URL_HTML_EN_11377");
		domainIdTestQueueNameMap.put(11383, "TEST_TARGET_URL_HTML_NL_11383");
		domainIdTestQueueNameMap.put(11384, "TEST_TARGET_URL_HTML_EN_11384");
		domainIdTestQueueNameMap.put(11385, "TEST_TARGET_URL_HTML_EN_11385");
		domainIdTestQueueNameMap.put(11391, "TEST_TARGET_URL_HTML_EN_11391");
		domainIdTestQueueNameMap.put(11392, "TEST_TARGET_URL_HTML_EN_11392");
		domainIdTestQueueNameMap.put(11393, "TEST_TARGET_URL_HTML_EN_11393");
		domainIdTestQueueNameMap.put(11394, "TEST_TARGET_URL_HTML_EN_11394");
		domainIdTestQueueNameMap.put(11395, "TEST_TARGET_URL_HTML_EN_11395");
		domainIdTestQueueNameMap.put(11399, "TEST_TARGET_URL_HTML_EN_11399");
		domainIdTestQueueNameMap.put(11402, "TEST_TARGET_URL_HTML_EN_11402");
		domainIdTestQueueNameMap.put(11403, "TEST_TARGET_URL_HTML_EN_11403");
		domainIdTestQueueNameMap.put(11405, "TEST_TARGET_URL_HTML_EN_11405");
		domainIdTestQueueNameMap.put(11417, "TEST_TARGET_URL_HTML_EN_11417");
		domainIdTestQueueNameMap.put(11420, "TEST_TARGET_URL_HTML_FR_11420");
		domainIdTestQueueNameMap.put(11425, "TEST_TARGET_URL_HTML_EN_11425");
		domainIdTestQueueNameMap.put(11426, "TEST_TARGET_URL_HTML_EN_11426");
		domainIdTestQueueNameMap.put(11428, "TEST_TARGET_URL_HTML_EN_11428");
		domainIdTestQueueNameMap.put(11439, "TEST_TARGET_URL_HTML_EN_11439");
		domainIdTestQueueNameMap.put(11478, "TEST_TARGET_URL_HTML_EN_11478");
		domainIdTestQueueNameMap.put(11479, "TEST_TARGET_URL_HTML_EN_11479");
		domainIdTestQueueNameMap.put(11496, "TEST_TARGET_URL_HTML_EN_11496");
		domainIdTestQueueNameMap.put(11497, "TEST_TARGET_URL_HTML_EN_11497");
		domainIdTestQueueNameMap.put(11498, "TEST_TARGET_URL_HTML_EN_11498");
		domainIdTestQueueNameMap.put(11501, "TEST_TARGET_URL_HTML_EN_11501");
		domainIdTestQueueNameMap.put(11502, "TEST_TARGET_URL_HTML_EN_11502");
		domainIdTestQueueNameMap.put(11503, "TEST_TARGET_URL_HTML_EN_11503");
		domainIdTestQueueNameMap.put(11504, "TEST_TARGET_URL_HTML_EN_11504");
		domainIdTestQueueNameMap.put(11506, "TEST_TARGET_URL_HTML_EN_11506");
		domainIdTestQueueNameMap.put(11508, "TEST_TARGET_URL_HTML_EN_11508");
		domainIdTestQueueNameMap.put(11509, "TEST_TARGET_URL_HTML_EN_11509");
		domainIdTestQueueNameMap.put(11510, "TEST_TARGET_URL_HTML_EN_11510");
		domainIdTestQueueNameMap.put(11511, "TEST_TARGET_URL_HTML_EN_11511");
		domainIdTestQueueNameMap.put(11512, "TEST_TARGET_URL_HTML_EN_11512");
		domainIdTestQueueNameMap.put(11513, "TEST_TARGET_URL_HTML_JA_11513");
		domainIdTestQueueNameMap.put(11514, "TEST_TARGET_URL_HTML_EN_11514");
		domainIdTestQueueNameMap.put(11516, "TEST_TARGET_URL_HTML_JA_11516");
		domainIdTestQueueNameMap.put(11517, "TEST_TARGET_URL_HTML_FR_11517");
		domainIdTestQueueNameMap.put(11518, "TEST_TARGET_URL_HTML_NL_11518");
		domainIdTestQueueNameMap.put(11519, "TEST_TARGET_URL_HTML_NL_11519");
		domainIdTestQueueNameMap.put(11520, "TEST_TARGET_URL_HTML_EN_11520");
		domainIdTestQueueNameMap.put(11521, "TEST_TARGET_URL_HTML_EN_11521");
		domainIdTestQueueNameMap.put(11522, "TEST_TARGET_URL_HTML_EN_11522");
		domainIdTestQueueNameMap.put(11523, "TEST_TARGET_URL_HTML_EN_11523");
		domainIdTestQueueNameMap.put(11526, "TEST_TARGET_URL_HTML_EN_11526");
		domainIdTestQueueNameMap.put(11527, "TEST_TARGET_URL_HTML_EN_11527");
		domainIdTestQueueNameMap.put(11528, "TEST_TARGET_URL_HTML_ZH_11528");
		domainIdTestQueueNameMap.put(11530, "TEST_TARGET_URL_HTML_EN_11530");
		domainIdTestQueueNameMap.put(11531, "TEST_TARGET_URL_HTML_JA_11531");
		domainIdTestQueueNameMap.put(11533, "TEST_TARGET_URL_HTML_EN_11533");
		domainIdTestQueueNameMap.put(11534, "TEST_TARGET_URL_HTML_EN_11534");
		domainIdTestQueueNameMap.put(11536, "TEST_TARGET_URL_HTML_EN_11536");
		domainIdTestQueueNameMap.put(11537, "TEST_TARGET_URL_HTML_EN_11537");
		domainIdTestQueueNameMap.put(11538, "TEST_TARGET_URL_HTML_EN_11538");
		domainIdTestQueueNameMap.put(11539, "TEST_TARGET_URL_HTML_EN_11539");
		domainIdTestQueueNameMap.put(11540, "TEST_TARGET_URL_HTML_EN_11540");
		domainIdTestQueueNameMap.put(11541, "TEST_TARGET_URL_HTML_EN_11541");
		domainIdTestQueueNameMap.put(11542, "TEST_TARGET_URL_HTML_EN_11542");
		domainIdTestQueueNameMap.put(11543, "TEST_TARGET_URL_HTML_EN_11543");
		domainIdTestQueueNameMap.put(11544, "TEST_TARGET_URL_HTML_EN_11544");
		domainIdTestQueueNameMap.put(11545, "TEST_TARGET_URL_HTML_EN_11545");
		domainIdTestQueueNameMap.put(11546, "TEST_TARGET_URL_HTML_EN_11546");
		domainIdTestQueueNameMap.put(11547, "TEST_TARGET_URL_HTML_EN_11547");
		domainIdTestQueueNameMap.put(11548, "TEST_TARGET_URL_HTML_EN_11548");
		domainIdTestQueueNameMap.put(11549, "TEST_TARGET_URL_HTML_PT_11549");
		domainIdTestQueueNameMap.put(11550, "TEST_TARGET_URL_HTML_DE_11550");
		domainIdTestQueueNameMap.put(11551, "TEST_TARGET_URL_HTML_JA_11551");
		domainIdTestQueueNameMap.put(11552, "TEST_TARGET_URL_HTML_RU_11552");
		domainIdTestQueueNameMap.put(11553, "TEST_TARGET_URL_HTML_EN_11553");
		domainIdTestQueueNameMap.put(11554, "TEST_TARGET_URL_HTML_ES_11554");
		domainIdTestQueueNameMap.put(11555, "TEST_TARGET_URL_HTML_EN_11555");
		domainIdTestQueueNameMap.put(11556, "TEST_TARGET_URL_HTML_EN_11556");
		domainIdTestQueueNameMap.put(11557, "TEST_TARGET_URL_HTML_IT_11557");
		domainIdTestQueueNameMap.put(11558, "TEST_TARGET_URL_HTML_NL_11558");
		domainIdTestQueueNameMap.put(11559, "TEST_TARGET_URL_HTML_PL_11559");
		domainIdTestQueueNameMap.put(11560, "TEST_TARGET_URL_HTML_TR_11560");
		domainIdTestQueueNameMap.put(11561, "TEST_TARGET_URL_HTML_FR_11561");
		domainIdTestQueueNameMap.put(11562, "TEST_TARGET_URL_HTML_ES_11562");
		domainIdTestQueueNameMap.put(11563, "TEST_TARGET_URL_HTML_KO_11563");
		domainIdTestQueueNameMap.put(11564, "TEST_TARGET_URL_HTML_EN_11564");
		domainIdTestQueueNameMap.put(11565, "TEST_TARGET_URL_HTML_EN_11565");
		domainIdTestQueueNameMap.put(11566, "TEST_TARGET_URL_HTML_EN_11566");
		domainIdTestQueueNameMap.put(11567, "TEST_TARGET_URL_HTML_EN_11567");
		domainIdTestQueueNameMap.put(11568, "TEST_TARGET_URL_HTML_PT_11568");
		domainIdTestQueueNameMap.put(11569, "TEST_TARGET_URL_HTML_DE_11569");
		domainIdTestQueueNameMap.put(11570, "TEST_TARGET_URL_HTML_JA_11570");
		domainIdTestQueueNameMap.put(11571, "TEST_TARGET_URL_HTML_RU_11571");
		domainIdTestQueueNameMap.put(11572, "TEST_TARGET_URL_HTML_EN_11572");
		domainIdTestQueueNameMap.put(11573, "TEST_TARGET_URL_HTML_ES_11573");
		domainIdTestQueueNameMap.put(11574, "TEST_TARGET_URL_HTML_EN_11574");
		domainIdTestQueueNameMap.put(11575, "TEST_TARGET_URL_HTML_EN_11575");
		domainIdTestQueueNameMap.put(11576, "TEST_TARGET_URL_HTML_IT_11576");
		domainIdTestQueueNameMap.put(11577, "TEST_TARGET_URL_HTML_NL_11577");
		domainIdTestQueueNameMap.put(11578, "TEST_TARGET_URL_HTML_PL_11578");
		domainIdTestQueueNameMap.put(11579, "TEST_TARGET_URL_HTML_TR_11579");
		domainIdTestQueueNameMap.put(11580, "TEST_TARGET_URL_HTML_FR_11580");
		domainIdTestQueueNameMap.put(11581, "TEST_TARGET_URL_HTML_ES_11581");
		domainIdTestQueueNameMap.put(11582, "TEST_TARGET_URL_HTML_KO_11582");
		domainIdTestQueueNameMap.put(11583, "TEST_TARGET_URL_HTML_EN_11583");
		domainIdTestQueueNameMap.put(11584, "TEST_TARGET_URL_HTML_EN_11584");
		domainIdTestQueueNameMap.put(11585, "TEST_TARGET_URL_HTML_EN_11585");
		domainIdTestQueueNameMap.put(11586, "TEST_TARGET_URL_HTML_EN_11586");
		domainIdTestQueueNameMap.put(11587, "TEST_TARGET_URL_HTML_PT_11587");
		domainIdTestQueueNameMap.put(11588, "TEST_TARGET_URL_HTML_DE_11588");
		domainIdTestQueueNameMap.put(11589, "TEST_TARGET_URL_HTML_JA_11589");
		domainIdTestQueueNameMap.put(11590, "TEST_TARGET_URL_HTML_RU_11590");
		domainIdTestQueueNameMap.put(11591, "TEST_TARGET_URL_HTML_EN_11591");
		domainIdTestQueueNameMap.put(11592, "TEST_TARGET_URL_HTML_ES_11592");
		domainIdTestQueueNameMap.put(11593, "TEST_TARGET_URL_HTML_EN_11593");
		domainIdTestQueueNameMap.put(11594, "TEST_TARGET_URL_HTML_EN_11594");
		domainIdTestQueueNameMap.put(11595, "TEST_TARGET_URL_HTML_IT_11595");
		domainIdTestQueueNameMap.put(11596, "TEST_TARGET_URL_HTML_NL_11596");
		domainIdTestQueueNameMap.put(11597, "TEST_TARGET_URL_HTML_PL_11597");
		domainIdTestQueueNameMap.put(11598, "TEST_TARGET_URL_HTML_TR_11598");
		domainIdTestQueueNameMap.put(11599, "TEST_TARGET_URL_HTML_FR_11599");
		domainIdTestQueueNameMap.put(11600, "TEST_TARGET_URL_HTML_ES_11600");
		domainIdTestQueueNameMap.put(11601, "TEST_TARGET_URL_HTML_KO_11601");
		domainIdTestQueueNameMap.put(11602, "TEST_TARGET_URL_HTML_EN_11602");
		domainIdTestQueueNameMap.put(11605, "TEST_TARGET_URL_HTML_EN_11605");
		domainIdTestQueueNameMap.put(11606, "TEST_TARGET_URL_HTML_EN_11606");
		domainIdTestQueueNameMap.put(11608, "TEST_TARGET_URL_HTML_EN_11608");
		domainIdTestQueueNameMap.put(11609, "TEST_TARGET_URL_HTML_EN_11609");
		domainIdTestQueueNameMap.put(11614, "TEST_TARGET_URL_HTML_EN_11614");
		domainIdTestQueueNameMap.put(11615, "TEST_TARGET_URL_HTML_EN_11615");
		domainIdTestQueueNameMap.put(11616, "TEST_TARGET_URL_HTML_EN_11616");
		domainIdTestQueueNameMap.put(11617, "TEST_TARGET_URL_HTML_EN_11617");
		domainIdTestQueueNameMap.put(11618, "TEST_TARGET_URL_HTML_EN_11618");
		domainIdTestQueueNameMap.put(11619, "TEST_TARGET_URL_HTML_EN_11619");
		domainIdTestQueueNameMap.put(11620, "TEST_TARGET_URL_HTML_EN_11620");
		domainIdTestQueueNameMap.put(11621, "TEST_TARGET_URL_HTML_EN_11621");
		domainIdTestQueueNameMap.put(11622, "TEST_TARGET_URL_HTML_EN_11622");
		domainIdTestQueueNameMap.put(11623, "TEST_TARGET_URL_HTML_EN_11623");
		domainIdTestQueueNameMap.put(11624, "TEST_TARGET_URL_HTML_EN_11624");
		domainIdTestQueueNameMap.put(11625, "TEST_TARGET_URL_HTML_EN_11625");
		domainIdTestQueueNameMap.put(11626, "TEST_TARGET_URL_HTML_EN_11626");
		domainIdTestQueueNameMap.put(11628, "TEST_TARGET_URL_HTML_FR_11628");
		domainIdTestQueueNameMap.put(11629, "TEST_TARGET_URL_HTML_EN_11629");
		domainIdTestQueueNameMap.put(11630, "TEST_TARGET_URL_HTML_EN_11630");
		domainIdTestQueueNameMap.put(11631, "TEST_TARGET_URL_HTML_EN_11631");
		domainIdTestQueueNameMap.put(11632, "TEST_TARGET_URL_HTML_EN_11632");
		domainIdTestQueueNameMap.put(11633, "TEST_TARGET_URL_HTML_EN_11633");
		domainIdTestQueueNameMap.put(11634, "TEST_TARGET_URL_HTML_EN_11634");
		domainIdTestQueueNameMap.put(11635, "TEST_TARGET_URL_HTML_FR_11635");
		domainIdTestQueueNameMap.put(11636, "TEST_TARGET_URL_HTML_ES_11636");
		domainIdTestQueueNameMap.put(11637, "TEST_TARGET_URL_HTML_DE_11637");
		domainIdTestQueueNameMap.put(11638, "TEST_TARGET_URL_HTML_EN_11638");
		domainIdTestQueueNameMap.put(11639, "TEST_TARGET_URL_HTML_NL_11639");
		domainIdTestQueueNameMap.put(11640, "TEST_TARGET_URL_HTML_EN_11640");
		domainIdTestQueueNameMap.put(11641, "TEST_TARGET_URL_HTML_EN_11641");
		domainIdTestQueueNameMap.put(11642, "TEST_TARGET_URL_HTML_EN_11642");
		domainIdTestQueueNameMap.put(11643, "TEST_TARGET_URL_HTML_EN_11643");
		domainIdTestQueueNameMap.put(11644, "TEST_TARGET_URL_HTML_EN_11644");
		domainIdTestQueueNameMap.put(11645, "TEST_TARGET_URL_HTML_EN_11645");
		domainIdTestQueueNameMap.put(11646, "TEST_TARGET_URL_HTML_EN_11646");
		domainIdTestQueueNameMap.put(11647, "TEST_TARGET_URL_HTML_EN_11647");
		domainIdTestQueueNameMap.put(11648, "TEST_TARGET_URL_HTML_EN_11648");
		domainIdTestQueueNameMap.put(11649, "TEST_TARGET_URL_HTML_EN_11649");
		domainIdTestQueueNameMap.put(11650, "TEST_TARGET_URL_HTML_EN_11650");
		domainIdTestQueueNameMap.put(11651, "TEST_TARGET_URL_HTML_EN_11651");
		domainIdTestQueueNameMap.put(11652, "TEST_TARGET_URL_HTML_EN_11652");
		domainIdTestQueueNameMap.put(11653, "TEST_TARGET_URL_HTML_EN_11653");
		domainIdTestQueueNameMap.put(11654, "TEST_TARGET_URL_HTML_EN_11654");
		domainIdTestQueueNameMap.put(11655, "TEST_TARGET_URL_HTML_EN_11655");
		domainIdTestQueueNameMap.put(11656, "TEST_TARGET_URL_HTML_JA_11656");
		domainIdTestQueueNameMap.put(11657, "TEST_TARGET_URL_HTML_EN_11657");
		domainIdTestQueueNameMap.put(11658, "TEST_TARGET_URL_HTML_EN_11658");
		domainIdTestQueueNameMap.put(11660, "TEST_TARGET_URL_HTML_EN_11660");
		domainIdTestQueueNameMap.put(11661, "TEST_TARGET_URL_HTML_EN_11661");
		domainIdTestQueueNameMap.put(11662, "TEST_TARGET_URL_HTML_EN_11662");
		domainIdTestQueueNameMap.put(11663, "TEST_TARGET_URL_HTML_EN_11663");
		domainIdTestQueueNameMap.put(11664, "TEST_TARGET_URL_HTML_FR_11664");
		domainIdTestQueueNameMap.put(11665, "TEST_TARGET_URL_HTML_EN_11665");
		domainIdTestQueueNameMap.put(11666, "TEST_TARGET_URL_HTML_EN_11666");
		domainIdTestQueueNameMap.put(11667, "TEST_TARGET_URL_HTML_SV_11667");
		domainIdTestQueueNameMap.put(11668, "TEST_TARGET_URL_HTML_EN_11668");
		domainIdTestQueueNameMap.put(11669, "TEST_TARGET_URL_HTML_EN_11669");
		domainIdTestQueueNameMap.put(11670, "TEST_TARGET_URL_HTML_EN_11670");
		domainIdTestQueueNameMap.put(11671, "TEST_TARGET_URL_HTML_EN_11671");
		domainIdTestQueueNameMap.put(11672, "TEST_TARGET_URL_HTML_EN_11672");
		domainIdTestQueueNameMap.put(11673, "TEST_TARGET_URL_HTML_EN_11673");
		return domainIdTestQueueNameMap;
	}
}
