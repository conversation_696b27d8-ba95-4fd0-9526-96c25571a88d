package com.actonia.process;

import java.util.Calendar;
import java.util.Date;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.dao.TargetUrlHtmlDailyBackupClickHouseDAO;
import com.actonia.utils.BackupUtils;
import com.actonia.utils.FormatUtils;

public class TargetUrlHtmlDailyBackupToS3 {

	private static final String S3_URI_PREFIX = "https://s3.us-east-1.amazonaws.com/";
	private TargetUrlHtmlDailyBackupClickHouseDAO sourceTargetUrlHtmlDailyBackupClickHouseDAO;
	private String s3FolderURI;
	private String sourceTableName;

	public TargetUrlHtmlDailyBackupToS3() {
		BackupUtils.getInstance();
	}

	public static void main(String[] args) throws Exception {
		new TargetUrlHtmlDailyBackupToS3().process(args);
	}

	private void process(String[] args) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("process() begins.");
		String sourceDatabaseServerIpAddress = null;
		Date testDate = null;
		Date startDailyDataCreationDate = null;
		Date endDailyDataCreationDate = null;
		Date currentDailyDataCreationDate = null;
		String s3BucketName = null;
		String s3PartialPrefix = null;
		String backupDateString = null;
		int totalDaysBackedUp = 0;

		if (args == null || args.length == 0) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters are required.");
			return;
		}

		// runtime paramter 1: source database server IP address
		if (args.length >= 1) {
			sourceDatabaseServerIpAddress = args[0];
		}
		if (StringUtils.isBlank(sourceDatabaseServerIpAddress)) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 1: source database server IP address is required.");
			return;
		}
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 1: source database server IP address=" + sourceDatabaseServerIpAddress);

		// runtime paramter 2: source table name
		if (args.length >= 2) {
			sourceTableName = args[1];
		}
		if (StringUtils.isBlank(sourceTableName)) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 2: source table name is required.");
			return;
		}
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 2: source table name=" + sourceTableName);

		// runtime paramter 3: S3 bucket name
		if (args.length >= 3) {
			s3BucketName = args[2];
		}
		if (StringUtils.isBlank(s3BucketName)) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 3: S3 bucket name is required.");
			return;
		}
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 3: S3 bucket name=" + s3BucketName);

		// runtime paramter 4: S3 prefix (partial)
		if (args.length >= 4) {
			s3PartialPrefix = args[3];
		}
		if (StringUtils.isBlank(s3PartialPrefix)) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 4: S3 prefix (partial) is required.");
			return;
		}
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 4: S3 prefix (partial)=" + s3PartialPrefix);

		Date todayDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
		backupDateString = DateFormatUtils.format(todayDate, IConstants.DATE_FORMAT_YYYY_MM_DD);
		FormatUtils.getInstance().logMemoryUsage("process() backupDateString=" + backupDateString);

		String s3FullPrefix = s3PartialPrefix + backupDateString;
		FormatUtils.getInstance().logMemoryUsage("process() s3FullPrefix=" + s3FullPrefix);

		s3FolderURI = S3_URI_PREFIX + s3BucketName + IConstants.SLASH + s3FullPrefix + IConstants.SLASH;
		FormatUtils.getInstance().logMemoryUsage("process() s3FolderURI=" + s3FolderURI);

		sourceTargetUrlHtmlDailyBackupClickHouseDAO = BackupUtils.getInstance().getTargetUrlHtmlDailyBackupClickHouseDAO(sourceDatabaseServerIpAddress);
		if (sourceTargetUrlHtmlDailyBackupClickHouseDAO == null) {
			FormatUtils.getInstance()
					.logMemoryUsage("process() sourceTargetUrlHtmlDailyBackupClickHouseDAO cannot be initiated using IP address=" + sourceDatabaseServerIpAddress);
			return;
		}

		testDate = sourceTargetUrlHtmlDailyBackupClickHouseDAO.getEarliestDailyDataCreationDate(sourceTableName);
		startDailyDataCreationDate = DateUtils.truncate(testDate, Calendar.DAY_OF_MONTH);
		FormatUtils.getInstance().logMemoryUsage("process() startDailyDataCreationDate=" + startDailyDataCreationDate);
		testDate = sourceTargetUrlHtmlDailyBackupClickHouseDAO.getLatestDailyDataCreationDate(sourceTableName);
		endDailyDataCreationDate = DateUtils.truncate(testDate, Calendar.DAY_OF_MONTH);
		FormatUtils.getInstance().logMemoryUsage("process() endDailyDataCreationDate=" + endDailyDataCreationDate);
		currentDailyDataCreationDate = startDailyDataCreationDate;
		while (!currentDailyDataCreationDate.after(endDailyDataCreationDate)) {

			// backup current track date data from source database to Amazon S3
			backup(currentDailyDataCreationDate);

			// calculate next track date one day later
			currentDailyDataCreationDate = DateUtils.addDays(currentDailyDataCreationDate, +1);
			totalDaysBackedUp++;
		}

		BackupUtils.getInstance().maintainBackupAuditTrailAndS3(sourceTableName, backupDateString, s3BucketName, s3PartialPrefix, startDailyDataCreationDate,
				endDailyDataCreationDate);

		FormatUtils.getInstance()
				.logMemoryUsage("process() ends. totalDaysBackedUp=" + totalDaysBackedUp + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private void backup(Date dailyDataCreationDate) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String dailyDataCreationDateString = DateFormatUtils.format(dailyDataCreationDate, IConstants.DATE_FORMAT_YYYY_MM_DD);
		FormatUtils.getInstance().logMemoryUsage("backup() begins. dailyDataCreationDateString=" + dailyDataCreationDateString);

		// https://s3.us-east-1.amazonaws.com/seoclarity-cdb/ck-cdb-123/backlink/local_backlink_data/2020-05-08.bin.zstd
		String s3ObjectURI = s3FolderURI + dailyDataCreationDateString + IConstants.BACKUP_FILE_EXT;
		sourceTargetUrlHtmlDailyBackupClickHouseDAO.backupToS3(s3ObjectURI, sourceTableName, dailyDataCreationDateString);
		FormatUtils.getInstance().logMemoryUsage(
				"backup() ends. dailyDataCreationDate=" + dailyDataCreationDate + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}
}
