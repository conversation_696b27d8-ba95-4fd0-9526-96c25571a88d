package com.actonia.process;

import java.util.Date;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.dao.SharedCountsClickHouseDAO;
import com.actonia.utils.Configurations;
import com.actonia.utils.FormatUtils;

public class SharedCountsReindex {

	private String sharedCountsTableName = null;

	public SharedCountsReindex() {
		// TODO Auto-generated constructor stub
	}

	public static void main(String[] args) throws Exception {
		new SharedCountsReindex().process(args);
	}

	private void process(String[] args) throws Exception {

		String sourceDatabaseConnectionUrl = null;
		String destinationDatabaseConnectionUrl = null;
		String testString = null;
		Date startTrackDate = null;
		Date endTrackDate = null;
		boolean isSourceConnectionUrl = false;
		Date currentTrackDate = null;

		if (args == null || args.length == 0) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters are required.");
			return;
		}

		// runtime paramter 1: source database server connection URL
		if (args.length >= 1) {
			sourceDatabaseConnectionUrl = args[0];
			if (StringUtils.isBlank(sourceDatabaseConnectionUrl)) {
				FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 1: source database connection URL is required.");
				return;
			}
		}
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 1: source database connection URL=" + sourceDatabaseConnectionUrl);

		// runtime paramter 2: destination database server connection URL
		if (args.length >= 2) {
			destinationDatabaseConnectionUrl = args[1];
			if (StringUtils.isBlank(destinationDatabaseConnectionUrl)) {
				FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 2: destination database connection URL is required.");
				return;
			}
		}
		FormatUtils.getInstance().logMemoryUsage("runtime parameters 2: destination database connection URL=" + destinationDatabaseConnectionUrl);

		// runtime paramter 3: start track date
		if (args.length >= 3) {
			testString = args[2];
			if (StringUtils.isBlank(testString)) {
				FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 3: start track date is required.");
				return;
			}
			startTrackDate = DateUtils.parseDate(testString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
		}
		FormatUtils.getInstance()
				.logMemoryUsage("process() runtime parameters 3: start track date=" + DateFormatUtils.format(startTrackDate, IConstants.DATE_FORMAT_YYYY_MM_DD));

		// runtime paramter 4: end track date
		if (args.length >= 4) {
			testString = args[3];
			if (StringUtils.isBlank(testString)) {
				FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 4: end track date is required.");
				return;
			}
			endTrackDate = DateUtils.parseDate(testString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
		}
		FormatUtils.getInstance()
				.logMemoryUsage("process() runtime parameters 4: end track date=" + DateFormatUtils.format(endTrackDate, IConstants.DATE_FORMAT_YYYY_MM_DD));

		isSourceConnectionUrl = true;
		SharedCountsClickHouseDAO sourceSharedCountsClickHouseDAO = getSharedCountsClickHouseDAO(sourceDatabaseConnectionUrl, isSourceConnectionUrl);
		if (sourceSharedCountsClickHouseDAO == null) {
			FormatUtils.getInstance()
					.logMemoryUsage("process() sourceSharedCountsClickHouseDAO cannot be initiated using connection URL=" + sourceDatabaseConnectionUrl);
			return;
		}

		isSourceConnectionUrl = false;
		SharedCountsClickHouseDAO destinationSharedCountsClickHouseDAO = getSharedCountsClickHouseDAO(destinationDatabaseConnectionUrl, isSourceConnectionUrl);
		if (destinationSharedCountsClickHouseDAO == null) {
			FormatUtils.getInstance()
					.logMemoryUsage("process() destinationSharedCountsClickHouseDAO cannot be initiated using connection URL=" + destinationSharedCountsClickHouseDAO);
			return;
		}

		currentTrackDate = startTrackDate;
		while (!currentTrackDate.after(endTrackDate)) {

			// re-index current track date data from source database to destination database
			reindex(currentTrackDate, sourceSharedCountsClickHouseDAO, destinationSharedCountsClickHouseDAO);

			// calculate next track date seven days later
			currentTrackDate = DateUtils.addDays(currentTrackDate, +7);
		}

	}

	private int reindex(Date trackDate, SharedCountsClickHouseDAO sourceSharedCountsClickHouseDAO, SharedCountsClickHouseDAO destinationSharedCountsClickHouseDAO)
			throws Exception {
		FormatUtils.getInstance().logMemoryUsage("reindex() begins. trackDate=" + trackDate);
		int totalRecordsReIndexed = sourceSharedCountsClickHouseDAO.transferFromSourceToDestination(trackDate, sharedCountsTableName,
				destinationSharedCountsClickHouseDAO);
		FormatUtils.getInstance().logMemoryUsage("reindex() ends. trackDate=" + trackDate + ",totalRecordsReIndexed=" + totalRecordsReIndexed);
		return totalRecordsReIndexed;
	}

	private SharedCountsClickHouseDAO getSharedCountsClickHouseDAO(String databaseConnectionUrl, boolean isSourceConnectionUrl) {
		SharedCountsClickHouseDAO sharedCountsClickHouseDAO = null;
		String[] clickHouseDatabaseHostnameArray = new String[] { databaseConnectionUrl };
		String clickHouseDatabasePort = Configurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_PORT);
		String clickHouseDatabaseName = Configurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_NAME);
		String clickHouseDatabaseUser = null;
		String clickHouseDatabasePassword = null;
		// when connection URL is source
		if (isSourceConnectionUrl == true) {
			clickHouseDatabaseUser = Configurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_SOURCE_USER);
			clickHouseDatabasePassword = Configurations
					.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_SOURCE_PASSWORD);
		}
		// when connection URL is destination
		else {
			clickHouseDatabaseUser = Configurations
					.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DESTINATION_USER);
			clickHouseDatabasePassword = Configurations
					.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DESTINATION_PASSWORD);
		}
		int clickHouseBatchCreationSize = Configurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_BATCH_CREATION_SIZE, 100);
		int clickHouseconnectionTimeoutInMilliseconds = Configurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_CONNECTION_TIMEOUT_MILLISECONDS, 88888);
		int clickHouseMaximumRetryCounts = Configurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_MAX_RETRY_COUNTS, 8);
		int clickHouseRetryWaitMilliseconds = Configurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_RETRY_WAIT_MILLISECONDS, 1000);
		try {
			sharedCountsClickHouseDAO = new SharedCountsClickHouseDAO(clickHouseDatabaseHostnameArray, clickHouseDatabasePort, clickHouseDatabaseName,
					clickHouseBatchCreationSize, clickHouseDatabaseUser, clickHouseDatabasePassword, clickHouseconnectionTimeoutInMilliseconds,
					clickHouseMaximumRetryCounts, clickHouseRetryWaitMilliseconds);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return sharedCountsClickHouseDAO;
	}
}
