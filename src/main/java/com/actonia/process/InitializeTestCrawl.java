package com.actonia.process;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.io.FileUtils;

import com.actonia.IConstants;
import com.actonia.utils.Md5Util;
import com.actonia.utils.SQSUtils;

public class InitializeTestCrawl {

	private static final int SQS_MESSAGE_BATCH_SIZE = 10;

	public static void main(String[] args) {
		try {
			new InitializeTestCrawl().process();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void process() throws Exception {
		System.out.println("process() begins.");
		SQSUtils.getInstance();

		// detail queues
		purgeTestDetailQueues();
		putMessagesToTestDetailQueues();

		// control queue
		String controlQueueName = "TEST_COMPETITOR_URL_HTML_QUEUE_NAMES";
		String controlQueueUrl = SQSUtils.getInstance().createQueue(controlQueueName);
		System.out.println("process() controlQueueName=" + controlQueueName + ",controlQueueUrl=" + controlQueueUrl);
		SQSUtils.getInstance().purgeQueue(controlQueueUrl);
		put(Arrays.asList(getControlQueueMessageArray()), controlQueueUrl, 1);
		System.out.println("process() ends.");

		// stop crawl queue message: [{"type":"stopCrawl","data":"false"}]
		String stopCrawlQueueName = "TEST_COMPETITOR_URL_HTML_STOP_CRAWL_QUEUE";
		String stopCrawlQueueUrl = SQSUtils.getInstance().createQueue(stopCrawlQueueName);
		SQSUtils.getInstance().purgeQueue(stopCrawlQueueUrl);
		List<String> jsonMessageList = new ArrayList<String>();
		jsonMessageList.add("[{\"type\":\"stopCrawl\",\"data\":\"false\"}]");
		put(jsonMessageList, stopCrawlQueueUrl, 1);
	}

	private void purgeTestDetailQueues() {
		System.out.println("purgeTestDetailQueues() begins.");
		String queueNamePrefix = "TEST_COMPETITOR_URL_HTML_";
		String queueName = null;
		int endQueueNumber = 25;
		String queueUrl = null;
		try {
			for (int i = 0; i < endQueueNumber; i++) {
				queueName = queueNamePrefix + (i + 1);
				queueUrl = SQSUtils.getInstance().createQueue(queueName);
				System.out.println("purgeTestDetailQueues() processing queueName=" + queueName + ",queueUrl=" + queueUrl);
				SQSUtils.getInstance().purgeQueue(queueUrl);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		System.out.println("purgeTestDetailQueues() ends.");
	}

	private void putMessagesToTestDetailQueues() throws Exception {
		System.out.println("putMessagesToTestDetailQueues() begins.");
		String folderLocation = "/home/<USER>/source/test_crawl/data/";
		String[] queueNameArray = getQueueNameArray();
		String queueUrl = null;
		String fileLocation = null;
		List<String> jsonMessageList = null;
		for (String queueName : queueNameArray) {
			queueUrl = SQSUtils.getInstance().createQueue(queueName);
			fileLocation = folderLocation + queueName + ".txt";
			System.out.println("putMessagesToTestDetailQueues() queueName=" + queueName + ",queueUrl=" + queueUrl + ",fileLocation=" + fileLocation);
			jsonMessageList = FileUtils.readLines(new File(fileLocation), IConstants.UTF_8);
			put(jsonMessageList, queueUrl, SQS_MESSAGE_BATCH_SIZE);
		}
		System.out.println("putMessagesToTestDetailQueues() ends.");
	}

	private String[] getQueueNameArray() {
		//		return new String[] { "TEST_COMPETITOR_URL_HTML_1", "TEST_COMPETITOR_URL_HTML_2", "TEST_COMPETITOR_URL_HTML_3", "TEST_COMPETITOR_URL_HTML_4",
		//				"TEST_COMPETITOR_URL_HTML_5", "TEST_COMPETITOR_URL_HTML_6", "TEST_COMPETITOR_URL_HTML_7", "TEST_COMPETITOR_URL_HTML_8", "TEST_COMPETITOR_URL_HTML_9",
		//				"TEST_COMPETITOR_URL_HTML_10", "TEST_COMPETITOR_URL_HTML_11", "TEST_COMPETITOR_URL_HTML_12", "TEST_COMPETITOR_URL_HTML_13",
		//				"TEST_COMPETITOR_URL_HTML_14", "TEST_COMPETITOR_URL_HTML_15", "TEST_COMPETITOR_URL_HTML_16", "TEST_COMPETITOR_URL_HTML_17",
		//				"TEST_COMPETITOR_URL_HTML_18", "TEST_COMPETITOR_URL_HTML_19", "TEST_COMPETITOR_URL_HTML_20", "TEST_COMPETITOR_URL_HTML_21",
		//				"TEST_COMPETITOR_URL_HTML_22", "TEST_COMPETITOR_URL_HTML_23", };

		return new String[] { "TEST_COMPETITOR_URL_HTML_7", "TEST_COMPETITOR_URL_HTML_13", "TEST_COMPETITOR_URL_HTML_15", "TEST_COMPETITOR_URL_HTML_17",
				"TEST_COMPETITOR_URL_HTML_21", };
	}

	private void put(List<String> jsonMessageList, String queueUrl, int batchSize) throws Exception {
		System.out.println("put() begins. queueUrl=" + queueUrl + ",batchSize=" + batchSize);
		String messageIdString = null;
		String messageBody = null;
		int totalMessages = 0;
		Map<String, String> messages = new HashMap<String, String>();
		if (jsonMessageList != null && jsonMessageList.size() > 0) {
			for (String jsonMessage : jsonMessageList) {
				//System.out.println("put() jsonMessage=" + jsonMessage);
				messageIdString = Md5Util.Md5(jsonMessage);
				messageBody = jsonMessage;
				try {
					messages.put(messageIdString, messageBody);
					totalMessages++;
					if (messages.size() >= batchSize) {
						SQSUtils.getInstance().sendBatchMessageToQueue(queueUrl, messages);
						if (totalMessages % 1000 == 0) {
							System.out.println("put() total message sent=" + totalMessages + ",queueUrl=" + queueUrl + ",batchSize=" + batchSize);
						}
						messages = new HashMap<String, String>();
					}
				} catch (Exception e) {
					System.out.println("put() error--queueUrl=" + queueUrl + ",batchSize=" + batchSize + ",exception message=" + e.getMessage());
					e.printStackTrace();
					throw e;
				}
			}
		}
		if (messages != null && messages.size() > 0) {
			SQSUtils.getInstance().sendBatchMessageToQueue(queueUrl, messages);
			messages = new HashMap<String, String>();
		}
		System.out.println("put() final total message sent=" + totalMessages + ",queueUrl=" + queueUrl + ",batchSize=" + batchSize);
		System.out.println("put() ends. queueUrl=" + queueUrl);
	}

	private String[] getControlQueueMessageArray() {
		//		return new String[] {
		//				"[{\"type\":\"queueName\",\"data\":\"TEST_COMPETITOR_URL_HTML_1\"},{\"type\":\"userAgent\",\"data\":\"Mozilla/5.0 (compatible; ClarityBot/9.0; +https://www.seoclarity.net/bot.html)\"},{\"type\":\"region\",\"data\":\"New York\"},{\"type\":\"autoAssociateTargetUrls\",\"data\":\"false\"},{\"type\":\"associateKeywordWithRankingPage\",\"data\":\"false\"},{\"type\":\"enableJavascriptCrawl\",\"data\":\"true\"},{\"type\":\"javascriptTimeoutInSecond\",\"data\":\"10\"},{\"type\":\"enableScrapyCrawl\",\"data\":\"false\"}]",
		//				"[{\"type\":\"queueName\",\"data\":\"TEST_COMPETITOR_URL_HTML_2\"},{\"type\":\"delayInSeconds\",\"data\":\"0\"},{\"type\":\"autoAssociateTargetUrls\",\"data\":\"false\"},{\"type\":\"enableJavascriptCrawl\",\"data\":\"true\"},{\"type\":\"enableScrapyCrawl\",\"data\":\"false\"}]",
		//				"[{\"type\":\"queueName\",\"data\":\"TEST_COMPETITOR_URL_HTML_3\"},{\"type\":\"delayInSeconds\",\"data\":\"0\"},{\"type\":\"autoAssociateTargetUrls\",\"data\":\"false\"},{\"type\":\"enableJavascriptCrawl\",\"data\":\"true\"}]",
		//				"[{\"type\":\"queueName\",\"data\":\"TEST_COMPETITOR_URL_HTML_4\"},{\"type\":\"maxConcurrentThreads\",\"data\":\"1\"},{\"type\":\"delayInSeconds\",\"data\":\"0\"},{\"type\":\"autoAssociateTargetUrls\",\"data\":\"false\"},{\"type\":\"associateKeywordWithRankingPage\",\"data\":\"false\"},{\"type\":\"enableJavascriptCrawl\",\"data\":\"true\"},{\"type\":\"enableScrapyCrawl\",\"data\":\"false\"}]",
		//				"[{\"type\":\"queueName\",\"data\":\"TEST_COMPETITOR_URL_HTML_5\"},{\"type\":\"region\",\"data\":\"New York\"},{\"type\":\"autoAssociateTargetUrls\",\"data\":\"true\"},{\"type\":\"associateKeywordWithRankingPage\",\"data\":\"false\"},{\"type\":\"enableJavascriptCrawl\",\"data\":\"true\"},{\"type\":\"enableScrapyCrawl\",\"data\":\"false\"}]",
		//				"[{\"type\":\"queueName\",\"data\":\"TEST_COMPETITOR_URL_HTML_6\"},{\"type\":\"userAgent\",\"data\":\"Mozilla/5.0 (compatible; ClarityBot/9.0; +https://www.seoclarity.net/bot.html)\"},{\"type\":\"region\",\"data\":\"New York\"},{\"type\":\"autoAssociateTargetUrls\",\"data\":\"false\"},{\"type\":\"associateKeywordWithRankingPage\",\"data\":\"false\"},{\"type\":\"enableJavascriptCrawl\",\"data\":\"true\"},{\"type\":\"javascriptTimeoutInSecond\",\"data\":\"10\"},{\"type\":\"contentFusionTimeoutInSecond\",\"data\":\"10\"},{\"type\":\"enableScrapyCrawl\",\"data\":\"false\"}]",
		//				"[{\"type\":\"queueName\",\"data\":\"TEST_COMPETITOR_URL_HTML_7\"},{\"type\":\"maxConcurrentThreads\",\"data\":\"1\"},{\"type\":\"userAgent\",\"data\":\"Mozilla/5.0 (compatible; ClarityBot/9.0; +https://www.seoclarity.net/bot.html)\"},{\"type\":\"region\",\"data\":\"New York\"},{\"type\":\"autoAssociateTargetUrls\",\"data\":\"false\"},{\"type\":\"associateKeywordWithRankingPage\",\"data\":\"false\"},{\"type\":\"enableJavascriptCrawl\",\"data\":\"true\"},{\"type\":\"javascriptTimeoutInSecond\",\"data\":\"30\"},{\"type\":\"enableScrapyCrawl\",\"data\":\"false\"}]",
		//				"[{\"type\":\"queueName\",\"data\":\"TEST_COMPETITOR_URL_HTML_8\"},{\"type\":\"userAgent\",\"data\":\"Mozilla/5.0 (Linux; Android 9; SM-G960F Build/PPR1.180610.011; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/74.0.3729.157 Mobile Safari/537.36 (compatible; ClarityBot/9.0; +https://www.seoclarity.net/bot.html)\"},{\"type\":\"region\",\"data\":\"New York\"},{\"type\":\"autoAssociateTargetUrls\",\"data\":\"false\"},{\"type\":\"associateKeywordWithRankingPage\",\"data\":\"false\"},{\"type\":\"enableJavascriptCrawl\",\"data\":\"true\"},{\"type\":\"enableScrapyCrawl\",\"data\":\"false\"}]",
		//				"[{\"type\":\"queueName\",\"data\":\"TEST_COMPETITOR_URL_HTML_9\"},{\"type\":\"region\",\"data\":\"New York\"},{\"type\":\"autoAssociateTargetUrls\",\"data\":\"false\"},{\"type\":\"associateKeywordWithRankingPage\",\"data\":\"false\"},{\"type\":\"enableJavascriptCrawl\",\"data\":\"true\"},{\"type\":\"javascriptTimeoutInSecond\",\"data\":\"10\"},{\"type\":\"enableScrapyCrawl\",\"data\":\"false\"}]",
		//				"[{\"type\":\"queueName\",\"data\":\"TEST_COMPETITOR_URL_HTML_10\"},{\"type\":\"autoAssociateTargetUrls\",\"data\":\"true\"},{\"type\":\"associateKeywordWithRankingPage\",\"data\":\"true\"},{\"type\":\"enableJavascriptCrawl\",\"data\":\"true\"},{\"type\":\"enableScrapyCrawl\",\"data\":\"true\"}]",
		//				"[{\"type\":\"queueName\",\"data\":\"TEST_COMPETITOR_URL_HTML_11\"},{\"type\":\"autoAssociateTargetUrls\",\"data\":\"false\"},{\"type\":\"associateKeywordWithRankingPage\",\"data\":\"false\"},{\"type\":\"enableJavascriptCrawl\",\"data\":\"true\"},{\"type\":\"enableScrapyCrawl\",\"data\":\"false\"}]",
		//				"[{\"type\":\"queueName\",\"data\":\"TEST_COMPETITOR_URL_HTML_12\"},{\"type\":\"autoAssociateTargetUrls\",\"data\":\"false\"},{\"type\":\"associateKeywordWithRankingPage\",\"data\":\"false\"},{\"type\":\"enableJavascriptCrawl\",\"data\":\"true\"},{\"type\":\"enableScrapyCrawl\",\"data\":\"false\"}]",
		//				"[{\"type\":\"queueName\",\"data\":\"TEST_COMPETITOR_URL_HTML_13\"},{\"type\":\"maxConcurrentThreads\",\"data\":\"1\"},{\"type\":\"userAgent\",\"data\":\"Mozilla/5.0 (Linux; Android 9; SM-G960F Build/PPR1.180610.011; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/74.0.3729.157 Mobile Safari/537.36 (compatible; ClarityBot/9.0; +https://www.seoclarity.net/bot.html)\"},{\"type\":\"region\",\"data\":\"New York\"},{\"type\":\"autoAssociateTargetUrls\",\"data\":\"true\"},{\"type\":\"associateKeywordWithRankingPage\",\"data\":\"false\"},{\"type\":\"enableJavascriptCrawl\",\"data\":\"true\"},{\"type\":\"javascriptTimeoutInSecond\",\"data\":\"30\"},{\"type\":\"enableScrapyCrawl\",\"data\":\"false\"}]",
		//				"[{\"type\":\"queueName\",\"data\":\"TEST_COMPETITOR_URL_HTML_14\"},{\"type\":\"region\",\"data\":\"New York\"},{\"type\":\"autoAssociateTargetUrls\",\"data\":\"false\"},{\"type\":\"associateKeywordWithRankingPage\",\"data\":\"false\"},{\"type\":\"enableJavascriptCrawl\",\"data\":\"true\"},{\"type\":\"javascriptTimeoutInSecond\",\"data\":\"15\"},{\"type\":\"contentFusionTimeoutInSecond\",\"data\":\"15\"},{\"type\":\"enableScrapyCrawl\",\"data\":\"false\"}]",
		//				"[{\"type\":\"queueName\",\"data\":\"TEST_COMPETITOR_URL_HTML_15\"},{\"type\":\"userAgent\",\"data\":\"Mozilla/5.0 (compatible; ClarityBot/9.0; +https://www.seoclarity.net/bot.html)\"},{\"type\":\"region\",\"data\":\"New York\"},{\"type\":\"autoAssociateTargetUrls\",\"data\":\"false\"},{\"type\":\"associateKeywordWithRankingPage\",\"data\":\"false\"},{\"type\":\"enableJavascriptCrawl\",\"data\":\"true\"},{\"type\":\"javascriptTimeoutInSecond\",\"data\":\"45\"},{\"type\":\"contentFusionTimeoutInSecond\",\"data\":\"45\"},{\"type\":\"enableScrapyCrawl\",\"data\":\"false\"}]",
		//				"[{\"type\":\"queueName\",\"data\":\"TEST_COMPETITOR_URL_HTML_16\"},{\"type\":\"region\",\"data\":\"New York\"},{\"type\":\"autoAssociateTargetUrls\",\"data\":\"false\"},{\"type\":\"associateKeywordWithRankingPage\",\"data\":\"false\"},{\"type\":\"enableJavascriptCrawl\",\"data\":\"true\"},{\"type\":\"enableScrapyCrawl\",\"data\":\"false\"}]",
		//				"[{\"type\":\"queueName\",\"data\":\"TEST_COMPETITOR_URL_HTML_17\"},{\"type\":\"region\",\"data\":\"New York\"},{\"type\":\"autoAssociateTargetUrls\",\"data\":\"false\"},{\"type\":\"associateKeywordWithRankingPage\",\"data\":\"false\"},{\"type\":\"enableJavascriptCrawl\",\"data\":\"true\"},{\"type\":\"javascriptTimeoutInSecond\",\"data\":\"10\"},{\"type\":\"enableScrapyCrawl\",\"data\":\"false\"}]",
		//				"[{\"type\":\"queueName\",\"data\":\"TEST_COMPETITOR_URL_HTML_18\"},{\"type\":\"region\",\"data\":\"New York\"},{\"type\":\"autoAssociateTargetUrls\",\"data\":\"false\"},{\"type\":\"associateKeywordWithRankingPage\",\"data\":\"false\"},{\"type\":\"enableJavascriptCrawl\",\"data\":\"true\"},{\"type\":\"javascriptTimeoutInSecond\",\"data\":\"10\"},{\"type\":\"enableScrapyCrawl\",\"data\":\"false\"}]",
		//				"[{\"type\":\"queueName\",\"data\":\"TEST_COMPETITOR_URL_HTML_19\"},{\"type\":\"region\",\"data\":\"New York\"},{\"type\":\"autoAssociateTargetUrls\",\"data\":\"false\"},{\"type\":\"associateKeywordWithRankingPage\",\"data\":\"false\"},{\"type\":\"enableJavascriptCrawl\",\"data\":\"true\"},{\"type\":\"javascriptTimeoutInSecond\",\"data\":\"10\"},{\"type\":\"enableScrapyCrawl\",\"data\":\"false\"}]",
		//				"[{\"type\":\"queueName\",\"data\":\"TEST_COMPETITOR_URL_HTML_20\"},{\"type\":\"userAgent\",\"data\":\"Mozilla/5.0 (compatible; ClarityBot/9.0; +https://www.seoclarity.net/bot.html)\"},{\"type\":\"region\",\"data\":\"New York\"},{\"type\":\"autoAssociateTargetUrls\",\"data\":\"false\"},{\"type\":\"associateKeywordWithRankingPage\",\"data\":\"false\"},{\"type\":\"enableJavascriptCrawl\",\"data\":\"true\"},{\"type\":\"javascriptTimeoutInSecond\",\"data\":\"30\"},{\"type\":\"contentFusionTimeoutInSecond\",\"data\":\"30\"},{\"type\":\"enableScrapyCrawl\",\"data\":\"false\"}]",
		//				"[{\"type\":\"queueName\",\"data\":\"TEST_COMPETITOR_URL_HTML_21\"},{\"type\":\"userAgent\",\"data\":\"Mozilla/5.0 (compatible; ClarityBot/9.0; +https://www.seoclarity.net/bot.html)\"},{\"type\":\"region\",\"data\":\"New York\"},{\"type\":\"autoAssociateTargetUrls\",\"data\":\"true\"},{\"type\":\"associateKeywordWithRankingPage\",\"data\":\"false\"},{\"type\":\"enableJavascriptCrawl\",\"data\":\"true\"},{\"type\":\"javascriptTimeoutInSecond\",\"data\":\"15\"},{\"type\":\"contentFusionTimeoutInSecond\",\"data\":\"15\"},{\"type\":\"enableScrapyCrawl\",\"data\":\"false\"}]",
		//				"[{\"type\":\"queueName\",\"data\":\"TEST_COMPETITOR_URL_HTML_22\"},{\"type\":\"userAgent\",\"data\":\"Mozilla/5.0 (compatible; ClarityBot/9.0; +https://www.seoclarity.net/bot.html)\"},{\"type\":\"region\",\"data\":\"London\"},{\"type\":\"autoAssociateTargetUrls\",\"data\":\"false\"},{\"type\":\"associateKeywordWithRankingPage\",\"data\":\"false\"},{\"type\":\"enableJavascriptCrawl\",\"data\":\"true\"},{\"type\":\"javascriptTimeoutInSecond\",\"data\":\"30\"},{\"type\":\"contentFusionTimeoutInSecond\",\"data\":\"30\"},{\"type\":\"enableScrapyCrawl\",\"data\":\"false\"}]",
		//				"[{\"type\":\"queueName\",\"data\":\"TEST_COMPETITOR_URL_HTML_23\"},{\"type\":\"userAgent\",\"data\":\"claritybot\"},{\"type\":\"region\",\"data\":\"New York\"},{\"type\":\"autoAssociateTargetUrls\",\"data\":\"false\"},{\"type\":\"associateKeywordWithRankingPage\",\"data\":\"false\"},{\"type\":\"enableJavascriptCrawl\",\"data\":\"true\"},{\"type\":\"javascriptTimeoutInSecond\",\"data\":\"25\"},{\"type\":\"contentFusionTimeoutInSecond\",\"data\":\"25\"},{\"type\":\"enableScrapyCrawl\",\"data\":\"false\"}]", };

		return new String[] {
				"[{\"type\":\"queueName\",\"data\":\"TEST_COMPETITOR_URL_HTML_7\"},{\"type\":\"maxConcurrentThreads\",\"data\":\"1\"},{\"type\":\"userAgent\",\"data\":\"Mozilla/5.0 (compatible; ClarityBot/9.0; +https://www.seoclarity.net/bot.html)\"},{\"type\":\"region\",\"data\":\"New York\"},{\"type\":\"autoAssociateTargetUrls\",\"data\":\"false\"},{\"type\":\"associateKeywordWithRankingPage\",\"data\":\"false\"},{\"type\":\"enableJavascriptCrawl\",\"data\":\"true\"},{\"type\":\"javascriptTimeoutInSecond\",\"data\":\"30\"},{\"type\":\"enableScrapyCrawl\",\"data\":\"false\"}]",
				"[{\"type\":\"queueName\",\"data\":\"TEST_COMPETITOR_URL_HTML_13\"},{\"type\":\"maxConcurrentThreads\",\"data\":\"1\"},{\"type\":\"userAgent\",\"data\":\"Mozilla/5.0 (Linux; Android 9; SM-G960F Build/PPR1.180610.011; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/74.0.3729.157 Mobile Safari/537.36 (compatible; ClarityBot/9.0; +https://www.seoclarity.net/bot.html)\"},{\"type\":\"region\",\"data\":\"New York\"},{\"type\":\"autoAssociateTargetUrls\",\"data\":\"true\"},{\"type\":\"associateKeywordWithRankingPage\",\"data\":\"false\"},{\"type\":\"enableJavascriptCrawl\",\"data\":\"true\"},{\"type\":\"javascriptTimeoutInSecond\",\"data\":\"30\"},{\"type\":\"enableScrapyCrawl\",\"data\":\"false\"}]",
				"[{\"type\":\"queueName\",\"data\":\"TEST_COMPETITOR_URL_HTML_15\"},{\"type\":\"userAgent\",\"data\":\"Mozilla/5.0 (compatible; ClarityBot/9.0; +https://www.seoclarity.net/bot.html)\"},{\"type\":\"region\",\"data\":\"New York\"},{\"type\":\"autoAssociateTargetUrls\",\"data\":\"false\"},{\"type\":\"associateKeywordWithRankingPage\",\"data\":\"false\"},{\"type\":\"enableJavascriptCrawl\",\"data\":\"true\"},{\"type\":\"javascriptTimeoutInSecond\",\"data\":\"45\"},{\"type\":\"contentFusionTimeoutInSecond\",\"data\":\"45\"},{\"type\":\"enableScrapyCrawl\",\"data\":\"false\"}]",
				"[{\"type\":\"queueName\",\"data\":\"TEST_COMPETITOR_URL_HTML_17\"},{\"type\":\"region\",\"data\":\"New York\"},{\"type\":\"autoAssociateTargetUrls\",\"data\":\"false\"},{\"type\":\"associateKeywordWithRankingPage\",\"data\":\"false\"},{\"type\":\"enableJavascriptCrawl\",\"data\":\"true\"},{\"type\":\"javascriptTimeoutInSecond\",\"data\":\"10\"},{\"type\":\"enableScrapyCrawl\",\"data\":\"false\"}]",
				"[{\"type\":\"queueName\",\"data\":\"TEST_COMPETITOR_URL_HTML_21\"},{\"type\":\"userAgent\",\"data\":\"Mozilla/5.0 (compatible; ClarityBot/9.0; +https://www.seoclarity.net/bot.html)\"},{\"type\":\"region\",\"data\":\"New York\"},{\"type\":\"autoAssociateTargetUrls\",\"data\":\"true\"},{\"type\":\"associateKeywordWithRankingPage\",\"data\":\"false\"},{\"type\":\"enableJavascriptCrawl\",\"data\":\"true\"},{\"type\":\"javascriptTimeoutInSecond\",\"data\":\"15\"},{\"type\":\"contentFusionTimeoutInSecond\",\"data\":\"15\"},{\"type\":\"enableScrapyCrawl\",\"data\":\"false\"}]", };
	}

}
