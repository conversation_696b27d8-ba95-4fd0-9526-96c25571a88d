package com.actonia.process;

import java.util.Calendar;
import java.util.Date;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.dao.TargetUrlHtmlFileNameTransferClickHouseDAO;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.TransferUtils;

public class TransferTargetUrlHtmlFileName {
	private TargetUrlHtmlFileNameTransferClickHouseDAO sourceTargetUrlHtmlFileNameTransferClickHouseDAO;
	private TargetUrlHtmlFileNameTransferClickHouseDAO destinationTargetUrlHtmlFileNameTransferClickHouseDAO;
	private int totalDaysProcessed = 0;
	private String sourceDatabaseServerIpAddress;
	private String sourceTableName;
	private String destinationDatabaseServerIpAddress;
	private String destinationTableName;

	public TransferTargetUrlHtmlFileName() {
		TransferUtils.getInstance();
	}

	public static void main(String[] args) throws Exception {
		new TransferTargetUrlHtmlFileName().process(args);
	}

	private void process(String[] args) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("process() begins.");

		if (args == null || args.length == 0) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters are required.");
			return;
		}

		// runtime parameter 1: source database server IP address
		if (args.length >= 1) {
			sourceDatabaseServerIpAddress = args[0];
		}
		if (StringUtils.isBlank(sourceDatabaseServerIpAddress)) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 1: source database server IP address is required.");
			return;
		}
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 1: source database server IP address=" + sourceDatabaseServerIpAddress);

		// runtime parameter 2: source table name
		if (args.length >= 2) {
			sourceTableName = args[1];
		}
		if (StringUtils.isBlank(sourceTableName)) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 2: source table name is required.");
			return;
		}
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 2: source table name=" + sourceTableName);

		// runtime parameter 3: destination database server IP address
		if (args.length >= 3) {
			destinationDatabaseServerIpAddress = args[2];
		}
		if (StringUtils.isBlank(destinationDatabaseServerIpAddress)) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 3: destination database server IP address is required.");
			return;
		}
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 3: destination database server IP address=" + destinationDatabaseServerIpAddress);

		// runtime parameter 4: destination table name
		if (args.length >= 4) {
			destinationTableName = args[3];
		}
		if (StringUtils.isBlank(destinationTableName)) {
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 4: destination table name is required.");
			return;
		}
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 4: destination table name=" + destinationTableName);

		sourceTargetUrlHtmlFileNameTransferClickHouseDAO = TransferUtils.getInstance().getTargetUrlHtmlFileNameTransferClickHouseDAO(sourceDatabaseServerIpAddress);

		if (sourceTargetUrlHtmlFileNameTransferClickHouseDAO == null) {
			FormatUtils.getInstance()
					.logMemoryUsage("process() sourceTargetUrlHtmlFileNameTransferClickHouseDAO cannot be initiated using IP address=" + sourceDatabaseServerIpAddress);
			return;
		}

		destinationTargetUrlHtmlFileNameTransferClickHouseDAO = TransferUtils.getInstance()
				.getTargetUrlHtmlFileNameTransferClickHouseDAO(destinationDatabaseServerIpAddress);

		if (destinationTargetUrlHtmlFileNameTransferClickHouseDAO == null) {
			FormatUtils.getInstance().logMemoryUsage(
					"process() destinationTargetUrlHtmlFileNameTransferClickHouseDAO cannot be initiated using IP address=" + destinationDatabaseServerIpAddress);
			return;
		}

		transferAll();

		FormatUtils.getInstance()
				.logMemoryUsage("process() ends. totalDaysProcessed=" + totalDaysProcessed + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private void transferAll() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("transferAll() begins.");
		Date testDate = sourceTargetUrlHtmlFileNameTransferClickHouseDAO.getEarliestTrackDate(sourceTableName);
		Date startTrackDate = DateUtils.truncate(testDate, Calendar.DAY_OF_MONTH);
		FormatUtils.getInstance().logMemoryUsage("transferAll() startTrackDate=" + startTrackDate);
		testDate = sourceTargetUrlHtmlFileNameTransferClickHouseDAO.getLatestTrackDate(sourceTableName);
		Date endTrackDate = DateUtils.truncate(testDate, Calendar.DAY_OF_MONTH);
		//Date endTrackDate = DateUtils.addDays(testDate, -1);
		FormatUtils.getInstance().logMemoryUsage("transferAll() endTrackDate=" + endTrackDate);
		Date currentTrackDate = startTrackDate;
		//Date testEndTrackDate = null;
		while (!currentTrackDate.after(endTrackDate)) {

			// transfer current track date data from source database to destination database
			transferOneDay(currentTrackDate);

			//testDate = sourceTargetUrlHtmlFileNameTransferClickHouseDAO.getLatestTrackDate(sourceTableName);
			//testEndTrackDate = DateUtils.truncate(testDate, Calendar.DAY_OF_MONTH);
			//if (testEndTrackDate.after(endTrackDate)) {
			//	endTrackDate = testEndTrackDate;
			//	FormatUtils.getInstance().logMemoryUsage("transferAll() endTrackDate=" + endTrackDate);
			//}

			// calculate next track date one day later
			currentTrackDate = DateUtils.addDays(currentTrackDate, +1);
			totalDaysProcessed++;
		}

		FormatUtils.getInstance().logMemoryUsage("transferAll() ends. elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private void transferOneDay(Date trackDate) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String trackDateString = DateFormatUtils.format(trackDate, IConstants.DATE_FORMAT_YYYY_MM_DD);
		destinationTargetUrlHtmlFileNameTransferClickHouseDAO.transferData(sourceDatabaseServerIpAddress, sourceTableName, destinationTableName, trackDateString);

		// verify record counts on both source and destination server
		int sourceRecordCount = sourceTargetUrlHtmlFileNameTransferClickHouseDAO.getRecordCount(trackDate, sourceTableName);
		int destinationRecordCount = destinationTargetUrlHtmlFileNameTransferClickHouseDAO.getRecordCount(trackDate, destinationTableName);
		if (sourceRecordCount == destinationRecordCount) {
			FormatUtils.getInstance().logMemoryUsage("transferOneDay() trackDate=" + trackDate + ",record counts matched=" + sourceRecordCount);
		} else {
			FormatUtils.getInstance().logMemoryUsage("transferOneDay() trackDate=" + trackDate + ",record counts mismatched: sourceRecordCount=" + sourceRecordCount
					+ ",destinationRecordCount=" + destinationRecordCount);
		}
	}
}
