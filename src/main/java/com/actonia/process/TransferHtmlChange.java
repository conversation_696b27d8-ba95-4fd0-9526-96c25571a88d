package com.actonia.process;

import com.actonia.dao.HtmlChangeClickHouseDAO;
import com.actonia.dao.TargetUrlChangeIndClickHouseDAO;
import com.actonia.entity.HtmlChange;
import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
import com.actonia.utils.CrawlerUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.StopWatch;

import java.text.ParseException;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

public class TransferHtmlChange {

	private static final Logger log = LogManager.getLogger(TransferHtmlChange.class);
	private static final int MOD = 256;
	private static final int batchSize = 50000;
	private static final ExecutorService executorService = Executors.newFixedThreadPool(8);
	private final LinkedBlockingQueue<HtmlChange> htmlChangeQueue = new LinkedBlockingQueue<>();
	private final TargetUrlChangeIndClickHouseDAO targetUrlChangeIndClickHouseDAO;
	private final HtmlChangeClickHouseDAO htmlChangeClickHouseDAO;
	private final List<String> fieldNames;
	private final LocalDate startDate;
	private final LocalDate endDate;
	private final String dataFieldName;
	private final int mod;
	public static List<Future<?>> futures = new ArrayList<>();


	public TransferHtmlChange(LocalDate startDate, LocalDate endDate, int mod) throws ParseException {
		this.startDate = startDate;
		this.endDate = endDate;
		this.mod = mod;
		try {
			fieldNames = CrawlerUtils.getInstance().getTargetUrlChangeIndTableAllFieldNames();
//			fieldNames.add("robot_txt_previous");
//			fieldNames.add("robot_txt_current");
			dataFieldName = String.join(", ", fieldNames);
			targetUrlChangeIndClickHouseDAO = TargetUrlChangeIndClickHouseDAO.getInstance();
			htmlChangeClickHouseDAO = HtmlChangeClickHouseDAO.getInstance();
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	public static void main(String[] args) throws Exception {
		try {
			final LocalDate startDate = args.length > 0 ? LocalDate.parse(args[0]) : LocalDate.now();
			final LocalDate endDate = args.length > 1 ? LocalDate.parse(args[1]) : LocalDate.now();
			final int mod = args.length > 2? Integer.parseInt(args[2]) : MOD;
			TransferHtmlChange transferHtmlChange = new TransferHtmlChange(startDate, endDate, mod);
			LocalDate trackDate = transferHtmlChange.endDate;
			StopWatch stopWatch = new StopWatch("transfer");
			while (!trackDate.isBefore(transferHtmlChange.startDate)) {
				log.info("start transfer: {}", trackDate);
				stopWatch.start("transfer: " + trackDate);
				transferHtmlChange.transfer(trackDate.toString());
				stopWatch.stop();
				trackDate = trackDate.minusDays(1);
				log.info("wait for futures");
				while (futures.stream().anyMatch(future -> !future.isDone())) {
					Thread.sleep(1000L);
				}
				log.info("futures finished");
				if (!transferHtmlChange.htmlChangeQueue.isEmpty()) {
					log.info("save remaining data, queue size: {}, counter: {}", transferHtmlChange.htmlChangeQueue.size(), counter.get());
					transferHtmlChange.htmlChangeClickHouseDAO.createBatch(new ArrayList<>(transferHtmlChange.htmlChangeQueue));
				}
				log.info("end transfer: {}, cost time: {} min", trackDate, stopWatch.getLastTaskTimeMillis() / 1000 / 60);
			}
			log.info("all finished\n{}", stopWatch.prettyPrint());
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		executorService.shutdown();
	}

	private void transfer(String trackDate) throws Exception {
		for (int i = 0; i < this.mod; i++) {
			StopWatch stopWatch = new StopWatch("mod-" + i);
			stopWatch.start("queryList");
			final List<TargetUrlChangeIndClickHouseEntity> targetUrlChangeIndClickHouseEntities = targetUrlChangeIndClickHouseDAO.queryListByTrackPeriod(trackDate, mod, i, fieldNames);
			if (targetUrlChangeIndClickHouseEntities.isEmpty()) {
				log.info("trackDate: {}, index = {}, queryList is empty", trackDate, i);
				continue;
			}
			stopWatch.stop();
			log.info("trackDate: {}, index = {}, queryList size = {}, queue size = {}, cost time: {}", trackDate, i, targetUrlChangeIndClickHouseEntities.size(), htmlChangeQueue.size(), stopWatch.getTotalTimeSeconds());
			try {
				final Future<?> createFromChangeInd = executorService.submit(() -> {
					stopWatch.start("createFromChangeInd");
					final Collection<HtmlChange> htmlChangeList = targetUrlChangeIndClickHouseEntities.parallelStream()
							.map(HtmlChange::createFromChangeInd)
							.collect(Collectors.toMap(htmlChange -> htmlChange.getDomainId() + "::" + htmlChange.getUrl_hash() + "::" + htmlChange.getChgId(), htmlChange -> htmlChange, (old, newOne) -> old))
							.values();
					htmlChangeQueue.addAll(htmlChangeList);
					stopWatch.stop();
					if (htmlChangeQueue.size() >= batchSize) {
						log.info("htmlChangeQueue full, size: {}, counter: {}", htmlChangeQueue.size(), counter.get());
						synchronized (htmlChangeQueue) {
							log.info("start saveBatch, queue size: {}", htmlChangeQueue.size());
							final Future<?> submit = executorService.submit(this::saveBatch);
							futures.add(submit);
						}
					}
				});
				futures.add(createFromChangeInd);
			} catch (Exception e) {
				log.error(e.getMessage());
				throw new RuntimeException(e);
			}
		}

	}
public final static AtomicInteger counter = new AtomicInteger(0);
	private void saveBatch() {
		synchronized (htmlChangeQueue) {
			if (htmlChangeQueue.size() < batchSize) {
				log.info("saveBatch, htmlChangeQueue size is less than batchSize, size: {}, counter: {}", htmlChangeQueue.size(), counter.get());
				return;
			}
		}
		StopWatch stopWatch = new StopWatch("saveBatch");
		stopWatch.start("saveBatch");
		List<HtmlChange> htmlChangeList = new ArrayList<>(batchSize);
		log.info("counter: {}", counter.get());
		for (int j = 0; j < batchSize; j++) {
			final HtmlChange poll = htmlChangeQueue.poll();
			if (poll != null) {
				htmlChangeList.add(poll);
				counter.incrementAndGet();
			}
		}
		this.htmlChangeClickHouseDAO.createBatch(htmlChangeList);
		stopWatch.stop();
		log.info("saveBatch cost time: {}, queue size: {}, counter: {}", stopWatch.getTotalTimeSeconds(), htmlChangeQueue.size(), counter.get());
	}
}

