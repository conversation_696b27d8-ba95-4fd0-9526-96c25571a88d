package com.actonia.extract.raw.html;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Properties;
import java.util.Set;

import org.apache.commons.lang.StringUtils;

import com.actonia.dao.OwnDomainEntityDAO;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.utils.CommonUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.PutMessageUtils;
import com.actonia.utils.SpringBeanFactory;

/***************************************************************************/
/** this must run on the FTP server in order to access the upload folders **/
/***************************************************************************/

public class StoreUploadedUrlsInDatabase {

	private OwnDomainEntityDAO ownDomainEntityDAO;

	private static final String ROOT_FOLDER = "/home/<USER>/";
	private static final String UPLOAD_FOLDER = "/html_source_extraction/";

	public StoreUploadedUrlsInDatabase() {
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
	}

	public static void main(String args[]) {
		try {
			new StoreUploadedUrlsInDatabase().process(args);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void process(String args[]) throws Exception {

		Boolean isExecDomainIdsInd = null;
		Set<Integer> runtimeDomainSet = null;

		Properties prop = new Properties();
		try {
			prop.load(StoreUploadedUrlsInDatabase.class.getResourceAsStream("/domain.properties"));
		} catch (Exception e) {
			System.out.println("no properties file found");
		}

		String execDomainIds = prop.getProperty("exec.domain");
		String notExecDomainIds = prop.getProperty("notexec.domain");
		System.out.println("execDomainIds=" + execDomainIds);
		System.out.println("notExecDomainIds=" + notExecDomainIds);

		List<OwnDomainEntity> allOwnDomainEntityList = ownDomainEntityDAO.queryForAll();

		// process specific domains
		if (StringUtils.isNotBlank(execDomainIds)) {
			runtimeDomainSet = CommonUtils.getDomainSet(execDomainIds);
			isExecDomainIdsInd = true;
		}
		// do not process specific domains
		else if (StringUtils.isNotBlank(notExecDomainIds)) {
			runtimeDomainSet = CommonUtils.getDomainSet(notExecDomainIds);
			isExecDomainIdsInd = false;
		}
		// process all domains
		else {
			runtimeDomainSet = null;
			isExecDomainIdsInd = null;
		}

		List<OwnDomainEntity> filteredOwnDomainEntityList = PutMessageUtils.getInstance().filterDomains(allOwnDomainEntityList, isExecDomainIdsInd, runtimeDomainSet);
		if (filteredOwnDomainEntityList != null && filteredOwnDomainEntityList.size() > 0) {
			for (OwnDomainEntity ownDomainEntity : filteredOwnDomainEntityList) {
				processUploadedUrls(ownDomainEntity);
			}
		}
	}

	private void processUploadedUrls(OwnDomainEntity ownDomainEntity) {
		FormatUtils.getInstance().logMemoryUsage("processUploadedUrls() begins. domainId=" + ownDomainEntity.getId() + ",domainName=" + ownDomainEntity.getDomain());
		int domainId = ownDomainEntity.getId();
		String domainName = ownDomainEntity.getDomain();
		String domainSpecificUploadFolder = ROOT_FOLDER + domainId + UPLOAD_FOLDER;
		Path path = Paths.get(domainSpecificUploadFolder);
		boolean isFolderExist = Files.isDirectory(path);
		FormatUtils.getInstance().logMemoryUsage("processUploadedUrls() ends. domainId=" + ownDomainEntity.getId() + ",domainName=" + ownDomainEntity.getDomain()
				+ ",domainSpecificUploadFolder=" + domainSpecificUploadFolder + ",isFolderExist=" + isFolderExist);
	}
}
