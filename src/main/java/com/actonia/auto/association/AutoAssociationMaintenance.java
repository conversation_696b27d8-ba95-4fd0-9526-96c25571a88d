package com.actonia.auto.association;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import org.apache.commons.lang.StringUtils;

import com.actonia.dao.AssociatedCompetitorUrlDAO;
import com.actonia.dao.CompetitorUrlEntityDAO;
import com.actonia.dao.CompetitorUrlMd5EntityDAO;
import com.actonia.dao.KeywordCompetitorUrlEntityDao;
import com.actonia.dao.OwnDomainEntityDAO;
import com.actonia.entity.AssociatedCompetitorUrlEntity;
import com.actonia.entity.CompetitorUrlMd5Entity;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.utils.CommonUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.PutMessageUtils;
import com.actonia.utils.SpringBeanFactory;

public class AutoAssociationMaintenance {
	private OwnDomainEntityDAO ownDomainEntityDAO;
	private KeywordCompetitorUrlEntityDao keywordCompetitorUrlEntityDao;
	private AssociatedCompetitorUrlDAO associatedCompetitorUrlDAO;
	private CompetitorUrlEntityDAO competitorUrlEntityDAO;
	private CompetitorUrlMd5EntityDAO competitorUrlMd5EntityDAO;

	public AutoAssociationMaintenance() {
		this.ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		this.keywordCompetitorUrlEntityDao = SpringBeanFactory.getBean("keywordCompetitorUrlEntityDao");
		this.associatedCompetitorUrlDAO = SpringBeanFactory.getBean("associatedCompetitorUrlDAO");
		this.competitorUrlEntityDAO = SpringBeanFactory.getBean("competitorUrlEntityDAO");
		this.competitorUrlMd5EntityDAO = SpringBeanFactory.getBean("competitorUrlMd5EntityDAO");
	}

	public static void main(String args[]) {
		System.out.println("main() begins.");
		long startTimestamp = System.currentTimeMillis();
		try {
			new AutoAssociationMaintenance().process(args);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
		}
		System.out.println("main() ends. elapsed time in sec.:" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private void process(String args[]) throws Exception {

		Boolean isExecDomainIdsInd = null;
		Set<Integer> runtimeDomainSet = null;
		int domainId = 0;

		Properties domainProperties = new Properties();
		try {
			domainProperties.load(AutoAssociationMaintenance.class.getResourceAsStream("/domain.properties"));
		} catch (Exception e) {
			System.out.println("process() no domain.properties file found");
			System.exit(-1);
		}

		String execDomainIds = domainProperties.getProperty("exec.domain");
		String notExecDomainIds = domainProperties.getProperty("notexec.domain");
		System.out.println("process() execDomainIds=" + execDomainIds);
		System.out.println("process() notExecDomainIds=" + notExecDomainIds);

		List<OwnDomainEntity> allOwnDomainEntityList = new ArrayList<OwnDomainEntity>();

		allOwnDomainEntityList = ownDomainEntityDAO.queryForAll();

		// process specific domains
		if (StringUtils.isNotBlank(execDomainIds)) {
			runtimeDomainSet = CommonUtils.getDomainSet(execDomainIds);
			isExecDomainIdsInd = true;
		}
		// do not process specific domains
		else if (StringUtils.isNotBlank(notExecDomainIds)) {
			runtimeDomainSet = CommonUtils.getDomainSet(notExecDomainIds);
			isExecDomainIdsInd = false;
		}
		// process all domains
		else {
			runtimeDomainSet = null;
			isExecDomainIdsInd = null;
		}

		List<OwnDomainEntity> filteredOwnDomainEntityList = PutMessageUtils.getInstance().filterDomains(allOwnDomainEntityList, isExecDomainIdsInd, runtimeDomainSet);
		if (filteredOwnDomainEntityList == null || filteredOwnDomainEntityList.size() == 0) {
			FormatUtils.getInstance().logMemoryUsage("process() filteredOwnDomainEntityList is empty.");
			return;
		}

		for (OwnDomainEntity ownDomainEntity : filteredOwnDomainEntityList) {
			domainId = ownDomainEntity.getId();
			maintainKeywordCompetitorUrl(domainId);
			//maintainAssociatedCompetitorUrl(domainId);
		}
	}

	// step 1: delete 't_keyword_competitorurl' when 't_keyword_competitorurl.competitor_url_id' is not in 't_competitor_url'
	private void maintainKeywordCompetitorUrl(int domainId) {
		FormatUtils.getInstance().logMemoryUsage("maintainKeywordCompetitorUrl() begins. domainId=" + domainId);
		List<Integer> recordIdList = keywordCompetitorUrlEntityDao.getCompetitorUrlIdNotExistList(domainId);
		int totalRecordsDeleted = 0;
		if (recordIdList != null && recordIdList.size() > 0) {
			FormatUtils.getInstance().logMemoryUsage("maintainKeywordCompetitorUrl() domainId=" + domainId + ",recordIdList.size()=" + recordIdList.size());
			for (Integer recordId : recordIdList) {
				keywordCompetitorUrlEntityDao.deleteByRecordId(recordId);
				totalRecordsDeleted++;
				if (totalRecordsDeleted % 1000 == 0) {
					FormatUtils.getInstance().logMemoryUsage("maintainKeywordCompetitorUrl() domainId=" + domainId + ",totalRecordsDeleted=" + totalRecordsDeleted);
				}
			}
		} else {
			FormatUtils.getInstance().logMemoryUsage("maintainKeywordCompetitorUrl() domainId=" + domainId + ",recordIdList is empty.");
		}
		FormatUtils.getInstance().logMemoryUsage("maintainKeywordCompetitorUrl() ends. domainId=" + domainId + ",totalRecordsDeleted=" + totalRecordsDeleted);
	}

	// step 2: delete 'competitor_url_md5' when 'competitor_url_md5.competitor_url_id' is not in 't_competitor_url'
	//	private void maintainCompetitorUrlMd5(int domainId) {
	//		FormatUtils.getInstance().logMemoryUsage("maintainCompetitorUrlMd5() begins. domainId=" + domainId);
	//
	//		int totalCompetitorUrlMd5Deleted = 0;
	//
	//		Set<Integer> competitorUrlIdSet = competitorUrlEntityDAO.getIdSet(domainId);
	//		if (competitorUrlIdSet != null && competitorUrlIdSet.size() > 0) {
	//			FormatUtils.getInstance().logMemoryUsage("maintainCompetitorUrlMd5() domainId=" + domainId + ",competitorUrlIdSet.size()=" + competitorUrlIdSet.size());
	//		} else {
	//			competitorUrlIdSet = new HashSet<Integer>();
	//			FormatUtils.getInstance().logMemoryUsage("maintainCompetitorUrlMd5() domainId=" + domainId + ",competitorUrlIdSet is empty.");
	//		}
	//
	//		List<CompetitorUrlMd5Entity> competitorUrlMd5EntityList = competitorUrlMd5EntityDAO.getList(domainId);
	//		if (competitorUrlMd5EntityList != null && competitorUrlMd5EntityList.size() > 0) {
	//			FormatUtils.getInstance()
	//					.logMemoryUsage("maintainCompetitorUrlMd5() domainId=" + domainId + ",competitorUrlMd5EntityList.size()=" + competitorUrlMd5EntityList.size());
	//			for (CompetitorUrlMd5Entity competitorUrlMd5Entity : competitorUrlMd5EntityList) {
	//				if (competitorUrlIdSet.contains(new Integer(competitorUrlMd5Entity.getCompetitorUrlId())) == false) {
	//					competitorUrlMd5EntityDAO.delete(competitorUrlMd5Entity.getDomainId(), competitorUrlMd5Entity.getHashCode());
	//					totalCompetitorUrlMd5Deleted++;
	//				}
	//			}
	//		} else {
	//			FormatUtils.getInstance().logMemoryUsage("maintainCompetitorUrlMd5() domainId=" + domainId + ",competitorUrlMd5EntityList is empty.");
	//		}
	//		FormatUtils.getInstance()
	//				.logMemoryUsage("maintainCompetitorUrlMd5() ends. domainId=" + domainId + ",totalCompetitorUrlMd5Deleted=" + totalCompetitorUrlMd5Deleted);
	//	}

	private void maintainAssociatedCompetitorUrl(int domainId) {
		FormatUtils.getInstance().logMemoryUsage("maintainAssociatedCompetitorUrl() begins. domainId=" + domainId);

		int totalDeleted1 = 0;
		Long keywordId = null;
		String competitorUrlHashCode = null;
		Map<String, CompetitorUrlMd5Entity> hashCodeCompetitorUrlMd5EntityMap = null;
		List<AssociatedCompetitorUrlEntity> associatedCompetitorUrlEntityList = null;
		Set<Integer> competitorUrlIdSet = null;
		int competitorUrlId = 0;
		CompetitorUrlMd5Entity competitorUrlMd5Entity = null;
		int totalDeleted2 = 0;

		// delete 'associated_competitor_url' when 'associated_competitor_url.competitor_url_hash_code' is not in 'competitor_url_md5'
		associatedCompetitorUrlEntityList = associatedCompetitorUrlDAO.getNotInCompetitorUrlMd5List(domainId);
		if (associatedCompetitorUrlEntityList != null && associatedCompetitorUrlEntityList.size() > 0) {
			FormatUtils.getInstance().logMemoryUsage("maintainAssociatedCompetitorUrl() domainId=" + domainId + ",associatedCompetitorUrlEntityList1.size()="
					+ associatedCompetitorUrlEntityList.size());
			for (AssociatedCompetitorUrlEntity associatedCompetitorUrlEntity : associatedCompetitorUrlEntityList) {
				keywordId = associatedCompetitorUrlEntity.getKeywordId();
				competitorUrlHashCode = associatedCompetitorUrlEntity.getCompetitorUrlHashCode();
				associatedCompetitorUrlDAO.delete(domainId, keywordId, competitorUrlHashCode);
				totalDeleted1++;
			}
		} else {
			FormatUtils.getInstance().logMemoryUsage("maintainAssociatedCompetitorUrl() domainId=" + domainId + ",associatedCompetitorUrlEntityList1 is empty.");
		}

		associatedCompetitorUrlEntityList = associatedCompetitorUrlDAO.getInCompetitorUrlMd5List(domainId);
		if (associatedCompetitorUrlEntityList != null && associatedCompetitorUrlEntityList.size() > 0) {
			FormatUtils.getInstance().logMemoryUsage("maintainAssociatedCompetitorUrl() domainId=" + domainId + ",associatedCompetitorUrlEntityList2.size()="
					+ associatedCompetitorUrlEntityList.size());
			hashCodeCompetitorUrlMd5EntityMap = competitorUrlMd5EntityDAO.getByDomain(domainId);
			if (hashCodeCompetitorUrlMd5EntityMap != null && hashCodeCompetitorUrlMd5EntityMap.size() > 0) {
				FormatUtils.getInstance().logMemoryUsage(
						"maintainCompetitorUrlMd5() domainId=" + domainId + ",hashCodeCompetitorUrlMd5EntityMap.size()=" + hashCodeCompetitorUrlMd5EntityMap.size());
			} else {
				FormatUtils.getInstance().logMemoryUsage("maintainCompetitorUrlMd5() domainId=" + domainId + ",hashCodeCompetitorUrlMd5EntityMap is empty.");
			}
			competitorUrlIdSet = competitorUrlEntityDAO.getIdSet(domainId);
			if (competitorUrlIdSet != null && competitorUrlIdSet.size() > 0) {
				FormatUtils.getInstance().logMemoryUsage("maintainCompetitorUrlMd5() domainId=" + domainId + ",competitorUrlIdSet.size()=" + competitorUrlIdSet.size());
			} else {
				competitorUrlIdSet = new HashSet<Integer>();
				FormatUtils.getInstance().logMemoryUsage("maintainCompetitorUrlMd5() domainId=" + domainId + ",competitorUrlIdSet is empty.");
			}
			for (AssociatedCompetitorUrlEntity associatedCompetitorUrlEntity : associatedCompetitorUrlEntityList) {
				keywordId = associatedCompetitorUrlEntity.getKeywordId();
				competitorUrlHashCode = associatedCompetitorUrlEntity.getCompetitorUrlHashCode();
				if (hashCodeCompetitorUrlMd5EntityMap.containsKey(competitorUrlHashCode)) {
					competitorUrlMd5Entity = hashCodeCompetitorUrlMd5EntityMap.get(competitorUrlHashCode);
					competitorUrlId = competitorUrlMd5Entity.getCompetitorUrlId();
					if (competitorUrlIdSet.contains(competitorUrlId) == false) {
						associatedCompetitorUrlDAO.delete(domainId, keywordId, competitorUrlHashCode);
						totalDeleted2++;
					}
				}
			}
		} else {
			FormatUtils.getInstance().logMemoryUsage("maintainAssociatedCompetitorUrl() domainId=" + domainId + ",associatedCompetitorUrlEntityList2 is empty.");
		}

		FormatUtils.getInstance().logMemoryUsage("maintainAssociatedCompetitorUrl() ends. domainId=" + domainId + ",totalDeleted1=" + totalDeleted1 + ",totalDeleted2=" + totalDeleted2);
	}
}
