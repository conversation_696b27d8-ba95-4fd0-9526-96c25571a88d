package com.actonia.auto.association;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import com.actonia.IConstants;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.concurrency.ThreadPoolService;
import com.actonia.dao.AssociatedTargetUrlDAO;
import com.actonia.dao.OwnDomainEntityDAO;
import com.actonia.dao.OwnDomainSettingEntityDAO;
import com.actonia.entity.AgencyInfoEntity;
import com.actonia.entity.AssociatedTargetUrlEntity;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.entity.OwnDomainSettingEntity;
import com.actonia.service.AgencyInfoService;
import com.actonia.utils.CommonUtils;
import com.actonia.utils.EmailSenderComponent;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.PutMessageUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.AssociateTopRankedTargetUrlSummaryValueObject;
import com.actonia.value.object.UrlCrawlParametersVO;
import com.google.gson.Gson;

/**
 * 
 * process every day for two days ago's ranking data
 *  
 */
public class AssociateTopRankedTargetUrls {

	private static boolean isDebug = false; //set to true for testing on local workstation

	private static ThreadPoolService threadPool = ThreadPoolService.getInstance();

	private OwnDomainEntityDAO ownDomainEntityDAO;

	private Date rankDate;

	private AssociatedTargetUrlDAO associatedTargetUrlDAO;

	private Date startProcessingTimestamp = new Date();

	private AgencyInfoService agencyInfoService;

	private EmailSenderComponent emailSenderComponent;

	private Date processTimestamp = new Date();

	private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;

	public AssociateTopRankedTargetUrls() {
		this.ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		this.associatedTargetUrlDAO = SpringBeanFactory.getBean("associatedTargetUrlDAO");
		this.agencyInfoService = SpringBeanFactory.getBean("agencyInfoService");
		this.emailSenderComponent = SpringBeanFactory.getBean("emailSenderComponent");
		this.ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
	}

	public static void main(String args[]) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("main() begins.");
		long startTimestamp = System.currentTimeMillis();
		threadPool.init();

		int numberOfThreads = 1;
		FormatUtils.getInstance().logMemoryUsage("main() numberOfThreads: " + numberOfThreads);
		CommonUtils.initThreads(numberOfThreads);
		AssociateTopRankedTargetUrls associateTopRankedTargetUrls = new AssociateTopRankedTargetUrls();
		associateTopRankedTargetUrls.initialize();
		associateTopRankedTargetUrls.process(args);
		threadPool.destroy();
		FormatUtils.getInstance().logMemoryUsage("main() ends. elapsed time in sec.:" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private void initialize() {
		cleanup();
	}

	// cleanup 'associated_target_url' and 'target_url_md5' records that are no longer needed
	private void cleanup() {

		FormatUtils.getInstance().logMemoryUsage("cleanup() begins.");

		if (isDebug == true) {
			FormatUtils.getInstance().logMemoryUsage("cleanup() ends.");
			return;
		}

		long startTimestamp = 0L;

		// cleanup 'associated_target_url' records that could not be associated during the last run...
		startTimestamp = System.currentTimeMillis();
		associatedTargetUrlDAO.resetNotAssociated();
		FormatUtils.getInstance().logMemoryUsage("cleanup() reset not associated, elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);

		startTimestamp = System.currentTimeMillis();
		int domainId = 0;
		AssociatedTargetUrlEntity associatedTargetUrlEntity = null;
		List<OwnDomainEntity> ownDomainEntityList = ownDomainEntityDAO.getSuspendOwnDomains();
		if (ownDomainEntityList != null && ownDomainEntityList.size() > 0) {
			for (OwnDomainEntity ownDomainEntity : ownDomainEntityList) {
				domainId = ownDomainEntity.getId();
				associatedTargetUrlEntity = associatedTargetUrlDAO.getOneByDomainId(domainId);
				if (associatedTargetUrlEntity != null) {
					associatedTargetUrlDAO.delete(domainId);
					FormatUtils.getInstance().logMemoryUsage("cleanup() data cleaned up for domainId=" + domainId + ",domainName=" + ownDomainEntity.getDomain());
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("cleanup() ends. elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private void process(String args[]) throws Exception {

		FormatUtils.getInstance().logMemoryUsage("process() processTimestamp=" + DateFormatUtils.format(processTimestamp, IConstants.DATE_FORMAT_YYYY_MM_DD_HHMMSS));

		// the rank date for processing is always two days ago
		Date todayDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
		FormatUtils.getInstance().logMemoryUsage("process() today's date=" + DateFormatUtils.format(todayDate, IConstants.DATE_FORMAT_YYYY_MM_DD));
		Date twoDaysAgoDate = DateUtils.addDays(todayDate, -2);
		FormatUtils.getInstance().logMemoryUsage("process() two days ago's date=" + DateFormatUtils.format(twoDaysAgoDate, IConstants.DATE_FORMAT_YYYY_MM_DD));
		rankDate = twoDaysAgoDate;
		FormatUtils.getInstance().logMemoryUsage("process() rankDate=" + DateFormatUtils.format(rankDate, IConstants.DATE_FORMAT_YYYY_MM_DD));

		Boolean isExecDomainIdsInd = null;
		Set<Integer> runtimeDomainSet = null;

		Properties domainProperties = new Properties();
		try {
			domainProperties.load(AssociateTopRankedTargetUrls.class.getResourceAsStream("/domain.properties"));
		} catch (Exception e) {
			FormatUtils.getInstance().logMemoryUsage("process() no domain.properties file found");
			System.exit(-1);
		}

		String execDomainIds = domainProperties.getProperty("exec.domain");
		String notExecDomainIds = domainProperties.getProperty("notexec.domain");
		FormatUtils.getInstance().logMemoryUsage("process() execDomainIds=" + execDomainIds);
		FormatUtils.getInstance().logMemoryUsage("process() notExecDomainIds=" + notExecDomainIds);
		List<OwnDomainEntity> allOwnDomainEntityList = ownDomainEntityDAO.queryForAll();

		// process specific domains
		if (StringUtils.isNotBlank(execDomainIds)) {
			runtimeDomainSet = CommonUtils.getDomainSet(execDomainIds);
			isExecDomainIdsInd = true;
		}
		// do not process specific domains
		else if (StringUtils.isNotBlank(notExecDomainIds)) {
			runtimeDomainSet = CommonUtils.getDomainSet(notExecDomainIds);
			isExecDomainIdsInd = false;
		}
		// process all domains
		else {
			runtimeDomainSet = null;
			isExecDomainIdsInd = null;
		}

		List<OwnDomainEntity> filteredOwnDomainEntityList = PutMessageUtils.getInstance().filterDomains(allOwnDomainEntityList, isExecDomainIdsInd, runtimeDomainSet);
		if (filteredOwnDomainEntityList != null && filteredOwnDomainEntityList.size() > 0) {
			processDomainsConcurrently(filteredOwnDomainEntityList, isExecDomainIdsInd, runtimeDomainSet);
		}
		sendSummary();
	}

	private void processDomainsConcurrently(List<OwnDomainEntity> allOwnDomainEntityList, Boolean isExecDomainIdsInd, Set<Integer> runtimeDomainSet)
			throws IOException {

		CacheModleFactory.getInstance().setAssociateTopRankedTargetUrlSummaryValueObjectList(new ArrayList<AssociateTopRankedTargetUrlSummaryValueObject>());

		int totalNumberOfDomains = allOwnDomainEntityList.size();
		long startTimestamp = System.currentTimeMillis();
		String ipAddress = null;
		OwnDomainEntity ownDomainEntity = null;
		AssociateTopRankedTargetUrlsCommand associateTopRankedTargetUrlsCommand = null;
		int numberOfDomainsProcessed = 0;
		OwnDomainSettingEntity ownDomainSettingEntity = null;
		int domainId = 0;
		String urlCrawlParameters = null;
		nextClientDomain: do {
			ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
			if (ipAddress == null) {
				continue;
			}
			ownDomainEntity = allOwnDomainEntityList.get(numberOfDomainsProcessed++);
			domainId = ownDomainEntity.getId();
			urlCrawlParameters = ownDomainEntity.getUrlCrawlParameters();
			ownDomainSettingEntity = ownDomainSettingEntityDAO.getSettingByDomainId(ownDomainEntity.getId());
			if (ownDomainSettingEntity != null) {
				if (StringUtils.equalsIgnoreCase(ownDomainSettingEntity.getCompanyName(), IConstants.COMPANY_NAME_HOMEAWAY)) {
					CacheModleFactory.getInstance().setAliveIpAddress(ipAddress);
					FormatUtils.getInstance().logMemoryUsage("processDomainsConcurrently() ipAddress released=" + ipAddress + ",domain=" + ownDomainEntity.getId()
							+ " - " + ownDomainEntity.getDomain() + ", skip companyName=" + ownDomainSettingEntity.getCompanyName());
					continue nextClientDomain;
				}
			}
			// when client domain requires target URLs auto-association
			if (checkIfAutoAssociateTargetUrlsRequired(domainId, urlCrawlParameters) == true) {
				associateTopRankedTargetUrlsCommand = getAssociateTopRankedTargetUrlsCommand(ipAddress, ownDomainEntity);
				if (associateTopRankedTargetUrlsCommand != null) {
					try {
						threadPool.execute(associateTopRankedTargetUrlsCommand);
					} catch (Exception e) {
						e.printStackTrace();
					}
				} else {
					CacheModleFactory.getInstance().setAliveIpAddress(ipAddress);
				}
			} else {
				CacheModleFactory.getInstance().setAliveIpAddress(ipAddress);
			}
		} while (numberOfDomainsProcessed < totalNumberOfDomains);

		do {
			try {
				Thread.sleep(1000);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		} while (threadPool.getThreadPool().getActiveCount() > 0);
		FormatUtils.getInstance().logMemoryUsage("processDomainsConcurrently() end. elapsed time in seconds=" + ((System.currentTimeMillis() - startTimestamp) / 1000));
	}

	private AssociateTopRankedTargetUrlsCommand getAssociateTopRankedTargetUrlsCommand(String ip, OwnDomainEntity ownDomainEntity) {
		AssociateTopRankedTargetUrlsCommand associateTopRankedTargetUrlsCommand = new AssociateTopRankedTargetUrlsCommand(ip, ownDomainEntity, rankDate,
				processTimestamp);
		associateTopRankedTargetUrlsCommand.setStatus(true);
		return associateTopRankedTargetUrlsCommand;
	}

	private void sendSummary() throws Exception {
		FormatUtils.getInstance().logMemoryUsage("sendSummary() begins.");
		int totalDomainProcessed = 0;
		List<AssociateTopRankedTargetUrlSummaryValueObject> associateTopRankedTargetUrlSummaryValueObjectList = CacheModleFactory.getInstance()
				.getAssociateTopRankedTargetUrlSummaryValueObjectList();
		if (associateTopRankedTargetUrlSummaryValueObjectList != null && associateTopRankedTargetUrlSummaryValueObjectList.size() > 0) {
			totalDomainProcessed = associateTopRankedTargetUrlSummaryValueObjectList.size();
		}

		String[] emailAddressArray = new String[] { "<EMAIL>" };
		int retryCount = 0;
		Map<String, Object> map = new HashMap<String, Object>();
		String processTimestampString = DateFormatUtils.format(processTimestamp, IConstants.DATE_FORMAT_YYYY_MM_DD_HHMMSS);
		String emailSubject = "Associate Top Ranked Target URLs Summary - " + processTimestampString;
		String startDateString = DateFormatUtils.format(startProcessingTimestamp, IConstants.DATE_FORMAT_YYYY_MM_DD_HHMMSS);
		String endDateString = DateFormatUtils.format(new Date(), IConstants.DATE_FORMAT_YYYY_MM_DD_HHMMSS);
		map.put("processTimestamp", processTimestampString);
		map.put("rankDate", DateFormatUtils.format(rankDate, IConstants.DATE_FORMAT_YYYY_MM_DD));
		map.put("associateTopRankedTargetUrlSummaryValueObjectList", associateTopRankedTargetUrlSummaryValueObjectList);
		map.put("totalDomainProcessed", totalDomainProcessed);
		map.put("startDateString", startDateString);
		map.put("endDateString", endDateString);

		AgencyInfoEntity agencyInfoEntity = agencyInfoService.getByDomainId(1701);
		while (retryCount < IConstants.MAX_SEND_EMAIL_RETRY_COUNT) {
			try {
				emailSenderComponent.sendMimeMultiPartZeptoMailAndBcc(IConstants.NOTIFICATION_EMAIL_ADDRESS, emailAddressArray, emailSubject,
						"mail_associate_top_ranked_target_urls_summary.txt", "mail_associate_top_ranked_target_urls_summary.html", map, agencyInfoEntity);
				retryCount = IConstants.MAX_SEND_EMAIL_RETRY_COUNT;
			} catch (Exception e) {
				e.printStackTrace();
				retryCount++;
				if (retryCount >= IConstants.MAX_SEND_EMAIL_RETRY_COUNT) {
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("sendSummary() retryCount=" + retryCount);
					try {
						Thread.sleep(IConstants.RETRY_WAIT_TIME_IN_MILLISECONDS);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("sendSummary() ends.");
	}

	// check if client domain requires target URLs auto-association
	private boolean checkIfAutoAssociateTargetUrlsRequired(int domainId, String urlCrawlParameters) {
		boolean output = false;
		UrlCrawlParametersVO[] urlCrawlParametersVoArray = null;
		UrlCrawlParametersVO urlCrawlParametersVO = null;

		// by default, auto-associate target URLs is off (ie. when t_own_domain's 'url_crawl_parameters' is null  
		// auto-associate target URLs is off when 'url_crawl_parameters' contains {"type":"autoAssociateTargetUrls","data":"false"}  
		// auto-associate target URLs is on when 'url_crawl_parameters' contains {"type":"autoAssociateTargetUrls","data":"true"}

		if (StringUtils.isNotBlank(urlCrawlParameters)) {
			urlCrawlParametersVoArray = new Gson().fromJson(urlCrawlParameters, UrlCrawlParametersVO[].class);
			for (int idx = 0; idx < urlCrawlParametersVoArray.length; idx++) {
				urlCrawlParametersVO = urlCrawlParametersVoArray[idx];
				// auto associate target URLs
				if (urlCrawlParametersVO.getType().equalsIgnoreCase(IConstants.AUTO_ASSOCIATE_TARGET_URLS)) {
					output = BooleanUtils.toBoolean(urlCrawlParametersVO.getData(), "true", "false");
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("checkIfAutoAssociateTargetUrlsRequired() domainId=" + domainId + ",output=" + output);
		return output;
	}
}
