package com.actonia.auto.association;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import com.actonia.IConstants;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.concurrency.ThreadPoolService;
import com.actonia.dao.AssociatedCompetitorUrlAuditTrailDAO;
import com.actonia.dao.OwnDomainEntityDAO;
import com.actonia.dao.OwnDomainSettingEntityDAO;
import com.actonia.dao.RankingDetailClickHouseDAO;
import com.actonia.entity.AssociatedCompetitorUrlAuditTrailEntity;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.entity.OwnDomainSettingEntity;
import com.actonia.utils.CommonUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.SpringBeanFactory;

/**
 * 
 * process every day for two days ago's ranking data
 *  
 */
public class AssociateTopRankedCompetitorUrls {

	public static final Log logger = LogFactory.getLog(AssociateTopRankedCompetitorUrls.class);

	private static ThreadPoolService threadPool = ThreadPoolService.getInstance();

	private OwnDomainEntityDAO ownDomainEntityDAO;

	private Date rankDate;

	private int topRankedPositionsToBeProcessed = 0;

	private int runDateNumber = 0;

	private Date createDate = new Date();

	private AssociatedCompetitorUrlAuditTrailDAO associatedCompetitorUrlAuditTrailDAO;

	private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;

	private static ConcurrentMap<String, String> competitorUrlSharedCountsHashCodeMap = new ConcurrentHashMap<String, String>();

	public AssociateTopRankedCompetitorUrls() {
		this.ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		this.associatedCompetitorUrlAuditTrailDAO = SpringBeanFactory.getBean("associatedCompetitorUrlAuditTrailDAO");
		this.ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
	}

	public static void main(String args[]) {
		System.out.println("main() begins.");
		long startTimestamp = System.currentTimeMillis();
		try {
			threadPool.init();
			CommonUtils.initThreads(8);
			new AssociateTopRankedCompetitorUrls().process(args);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			threadPool.destroy();
		}
		System.out.println("main() ends. elapsed time in sec.:" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private void process(String args[]) throws Exception {

		// runtime parameter 1 (optional): the requested top ranked positions
		topRankedPositionsToBeProcessed = 5; //  by default, process top five ranked competitor URLs
		if (args != null && args.length >= 1) {
			String topRankedPositionsToBeProcessedString = args[0];
			System.out.println("process() runtime parameter 1: top ranked positions to be processed override=" + topRankedPositionsToBeProcessedString);
			if (StringUtils.isNotBlank(topRankedPositionsToBeProcessedString)) {
				if (NumberUtils.toInt(topRankedPositionsToBeProcessedString) != 0) {
					topRankedPositionsToBeProcessed = NumberUtils.toInt(topRankedPositionsToBeProcessedString);
				}
			}
		}
		System.out.println("process() top ranked positions to be processed=" + topRankedPositionsToBeProcessed);

		// runtime parameter 2 (optional): process date override for back-processing (by default, process date is two days ago's date) (YYYY-MM-DD)
		// by default, process date is two days ago (for Sunday's ranking data) when running on Tuesdays
		Date todayDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
		System.out.println("process() today's date=" + DateFormatUtils.format(todayDate, IConstants.DATE_FORMAT_YYYY_MM_DD));
		Date twoDaysAgoDate = DateUtils.addDays(todayDate, -2);
		System.out.println("process() two days ago's date=" + DateFormatUtils.format(twoDaysAgoDate, IConstants.DATE_FORMAT_YYYY_MM_DD));
		rankDate = twoDaysAgoDate;
		if (args != null && args.length >= 2) {
			String processDateOverrideString = args[1];
			System.out.println("process() runtime parameter 2: process date override=" + processDateOverrideString);
			if (StringUtils.isNotBlank(processDateOverrideString)) {
				try {
					Date rankDateOverride = DateUtils.parseDate(processDateOverrideString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
					rankDate = rankDateOverride;
				} catch (ParseException e) {
					System.out.println("process() rankDateOverride invalid, exception message=" + e.getMessage());
					System.exit(-1);
				}
			}
		}
		System.out.println("process() rankDate=" + DateFormatUtils.format(rankDate, IConstants.DATE_FORMAT_YYYY_MM_DD));
		runDateNumber = NumberUtils.toInt(DateFormatUtils.format(new Date(), IConstants.DATE_FORMAT_YYYYMMDD));
		System.out.println("process() runDateNumber=" + runDateNumber);

		Boolean isExecDomainIdsInd = null;
		Set<Integer> runtimeDomainSet = null;

		Properties domainProperties = new Properties();
		try {
			domainProperties.load(AssociateTopRankedCompetitorUrls.class.getResourceAsStream("/domain.properties"));
		} catch (Exception e) {
			System.out.println("process() no domain.properties file found");
			System.exit(-1);
		}

		String execDomainIds = domainProperties.getProperty("exec.domain");
		String notExecDomainIds = domainProperties.getProperty("notexec.domain");
		System.out.println("process() execDomainIds=" + execDomainIds);
		System.out.println("process() notExecDomainIds=" + notExecDomainIds);

		List<OwnDomainEntity> allOwnDomainEntityList = new ArrayList<OwnDomainEntity>();

		allOwnDomainEntityList = ownDomainEntityDAO.queryForAll();

		// process specific domains
		if (StringUtils.isNotBlank(execDomainIds)) {
			runtimeDomainSet = CommonUtils.getDomainSet(execDomainIds);
			isExecDomainIdsInd = true;
		}
		// do not process specific domains
		else if (StringUtils.isNotBlank(notExecDomainIds)) {
			runtimeDomainSet = CommonUtils.getDomainSet(notExecDomainIds);
			isExecDomainIdsInd = false;
		}
		// process all domains
		else {
			runtimeDomainSet = null;
			isExecDomainIdsInd = null;
		}

		RankingDetailClickHouseDAO.getInstance();

		processConcurrently(allOwnDomainEntityList, isExecDomainIdsInd, runtimeDomainSet);
	}

	private void processConcurrently(List<OwnDomainEntity> allOwnDomainEntityList, Boolean isExecDomainIdsInd, Set<Integer> runtimeDomainSet) throws Exception {
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat(IConstants.DATE_FORMAT_YYYY_MM_DD_HHMMSS);
		String formattedCreateDate = simpleDateFormat.format(createDate);
		System.out.println("processConcurrently() data creation date for this batch run=" + formattedCreateDate);

		// update 'associate_competitor_url_audit_trail' for the current batch run date and the batch run update timestamp for dissociation 90 days afterwards.
		int batchRunDateNumber = NumberUtils.toInt(DateFormatUtils.format(createDate, IConstants.DATE_FORMAT_YYYYMMDD));
		List<AssociatedCompetitorUrlAuditTrailEntity> associatedCompetitorUrlAuditTrailEntityList = new ArrayList<AssociatedCompetitorUrlAuditTrailEntity>();
		AssociatedCompetitorUrlAuditTrailEntity associatedCompetitorUrlAuditTrailEntity = new AssociatedCompetitorUrlAuditTrailEntity();
		associatedCompetitorUrlAuditTrailEntity.setBatchRunDate(batchRunDateNumber);
		associatedCompetitorUrlAuditTrailEntity.setBatchRunUpdateTimestamp(createDate);

		// check if audit record already exist
		AssociatedCompetitorUrlAuditTrailEntity associatedCompetitorUrlAuditTrailEntityExisting = associatedCompetitorUrlAuditTrailDAO.get(batchRunDateNumber);
		if (associatedCompetitorUrlAuditTrailEntityExisting == null) {
			associatedCompetitorUrlAuditTrailEntityList.add(associatedCompetitorUrlAuditTrailEntity);
			associatedCompetitorUrlAuditTrailDAO.create(associatedCompetitorUrlAuditTrailEntityList);
		}

		int totalNumberOfDomains = allOwnDomainEntityList.size();
		long startTimestamp = System.currentTimeMillis();
		String ipAddress = null;
		OwnDomainEntity ownDomainEntity = null;
		AssociateTopRankedCompetitorUrlsCommand associateTopRankedCompetitorUrlsCommand = null;
		int numberOfDomainsProcessed = 0;
		OwnDomainSettingEntity ownDomainSettingEntity = null;
		do {
			ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
			if (ipAddress == null) {
				continue;
			}
			ownDomainEntity = allOwnDomainEntityList.get(numberOfDomainsProcessed++);

			associateTopRankedCompetitorUrlsCommand = null;
			if (isExecDomainIdsInd == null) {
				associateTopRankedCompetitorUrlsCommand = getAssociateTopRankedCompetitorUrlsCommand(ipAddress, ownDomainEntity);
			} else if (isExecDomainIdsInd == true) {
				if (runtimeDomainSet.contains(ownDomainEntity.getId())) {
					associateTopRankedCompetitorUrlsCommand = getAssociateTopRankedCompetitorUrlsCommand(ipAddress, ownDomainEntity);
				}
			} else if (isExecDomainIdsInd == false) {
				if (!runtimeDomainSet.contains(ownDomainEntity.getId())) {
					associateTopRankedCompetitorUrlsCommand = getAssociateTopRankedCompetitorUrlsCommand(ipAddress, ownDomainEntity);
				}
			}
			if (associateTopRankedCompetitorUrlsCommand != null) {
				try {
					ownDomainSettingEntity = ownDomainSettingEntityDAO.getSettingByDomainId(ownDomainEntity.getId());
					// when 'auto_associate_competitor_url' field is 0, do not auto associate top ranked competitor URLS
					if (ownDomainSettingEntity != null && ownDomainSettingEntity.getAutoAssociateCompetitorUrl() != IConstants.DATABASE_NUMERIC_NULL_FIELD_PROXY_VALUE
							&& ownDomainSettingEntity.getAutoAssociateCompetitorUrl().intValue() == IConstants.FALSE_NUMERIC) {
						CacheModleFactory.getInstance().setAliveIpAddress(ipAddress);
						FormatUtils.getInstance()
								.logMemoryUsage("processConcurrently() ipAddress released=" + ipAddress + ",domain=" + ownDomainEntity.getId() + " - "
										+ ownDomainEntity.getDomain()
										+ ", top ranked competitor URLs will not be auto-associated, ownDomainSettingEntity.getAutoAssociateCompetitorUrl()="
										+ ownDomainSettingEntity.getAutoAssociateCompetitorUrl());
					}
					// when 't_own_domain_setting' not available, or 'auto_associate_competitor_url' field is null or 1, auto associate top ranked competitor URLS
					else {
						System.out.println(
								"processConcurrently() ipAddress acquired=" + ipAddress + ",domain=" + ownDomainEntity.getId() + " - " + ownDomainEntity.getDomain());
						threadPool.execute(associateTopRankedCompetitorUrlsCommand);
					}
				} catch (Exception e) {
					e.printStackTrace();
				}
			} else {
				CacheModleFactory.getInstance().setAliveIpAddress(ipAddress);
			}
		} while (numberOfDomainsProcessed < totalNumberOfDomains);

		do {
			try {
				Thread.sleep(1000);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		} while (threadPool.getThreadPool().getActiveCount() > 0);
		System.out.println("processConcurrently() end. elapsed time in seconds=" + ((System.currentTimeMillis() - startTimestamp) / 1000));
	}

	private AssociateTopRankedCompetitorUrlsCommand getAssociateTopRankedCompetitorUrlsCommand(String ip, OwnDomainEntity ownDomainEntity) {
		AssociateTopRankedCompetitorUrlsCommand associateTopRankedCompetitorUrlsCommand = new AssociateTopRankedCompetitorUrlsCommand(ip, ownDomainEntity, rankDate,
				topRankedPositionsToBeProcessed, runDateNumber, createDate);
		associateTopRankedCompetitorUrlsCommand.setStatus(true);
		return associateTopRankedCompetitorUrlsCommand;
	}

	public static ConcurrentMap<String, String> getCompetitorUrlSharedCountsHashCodeMap() {
		return competitorUrlSharedCountsHashCodeMap;
	}

	public static void setCompetitorUrlSharedCountsHashCodeMap(ConcurrentMap<String, String> competitorUrlSharedCountsHashCodeMap) {
		AssociateTopRankedCompetitorUrls.competitorUrlSharedCountsHashCodeMap = competitorUrlSharedCountsHashCodeMap;
	}
}
