package com.actonia.auto.association;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;

import com.actonia.IConstants;
import com.actonia.concurrency.BaseThreadCommand;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.dao.AssociatedCompetitorUrlDAO;
import com.actonia.dao.CompetitorEntityDAO;
import com.actonia.dao.CompetitorUrlEntityDAO;
import com.actonia.dao.CompetitorUrlMd5EntityDAO;
import com.actonia.dao.KeywordCompetitorUrlEntityDao;
import com.actonia.dao.KeywordEntityDAO;
import com.actonia.entity.AssociatedCompetitorUrlAuditTrailEntity;
import com.actonia.entity.AssociatedCompetitorUrlEntity;
import com.actonia.entity.CompetitorEntity;
import com.actonia.entity.CompetitorUrlEntity;
import com.actonia.entity.CompetitorUrlMd5Entity;
import com.actonia.entity.KeywordCompetitorUrlEntity;
import com.actonia.entity.KeywordEntity;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.service.ScKeywordRankService;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.RankCheckUtils;
import com.actonia.utils.SolrUtils;
import com.actonia.utils.SpringBeanFactory;

public class DisassociateTopRankedCompetitorUrlsCommand extends BaseThreadCommand {
	//private boolean isDebug = false;
	private String ip;
	private int domainId;
	private String domainName;
	private CompetitorUrlEntityDAO competitorUrlEntityDAO;
	private CompetitorUrlMd5EntityDAO competitorUrlMd5EntityDAO;
	private KeywordEntityDAO keywordEntityDAO;
	private Date rankDate;
	private int topRankedPositions;

	private CompetitorEntityDAO competitorEntityDAO;

	private KeywordCompetitorUrlEntityDao keywordCompetitorUrlEntityDao;

	private static final int CREATE_ASSOCIATION_ONLY_IND_FALSE = 0;

	private AssociatedCompetitorUrlDAO associatedCompetitorUrlDAO = null;

	private int searchEngineId = 0;

	private int searchLanguageId = 0;

	private Date startProcessDate = null;

	private Date endProcessDate = null;

	private int totalKeywordCompetitorUrlDisassociated = 0;
	private int totalCompetitorUrlDisassociated = 0;
	private int totalCompetitorDisassociated = 0;
	private int totalAssociatedCompetitorUrlDisassociated = 0;

	private OwnDomainEntity ownDomainEntity;
	private Map<String, CompetitorUrlMd5Entity> hashCodeCompetitorUrlMd5EntityMap = new HashMap<String, CompetitorUrlMd5Entity>();

	public DisassociateTopRankedCompetitorUrlsCommand(String ip, OwnDomainEntity ownDomainEntity, Date rankDate, int topRankedPositions, Date startProcessDate,
			Date endProcessDate) {
		super();
		this.ip = ip;
		this.ownDomainEntity = ownDomainEntity;
		this.domainId = ownDomainEntity.getId();
		this.domainName = ownDomainEntity.getDomain();
		this.rankDate = rankDate;
		this.competitorUrlEntityDAO = SpringBeanFactory.getBean("competitorUrlEntityDAO");
		this.competitorUrlMd5EntityDAO = SpringBeanFactory.getBean("competitorUrlMd5EntityDAO");
		this.keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
		this.topRankedPositions = topRankedPositions;
		this.competitorEntityDAO = SpringBeanFactory.getBean("competitorEntityDAO");
		this.keywordCompetitorUrlEntityDao = SpringBeanFactory.getBean("keywordCompetitorUrlEntityDao");
		this.associatedCompetitorUrlDAO = SpringBeanFactory.getBean("associatedCompetitorUrlDAO");
		this.startProcessDate = startProcessDate;
		this.endProcessDate = endProcessDate;
		this.searchEngineId = ScKeywordRankService.getSearchEngineId(ownDomainEntity);
		this.searchLanguageId = ScKeywordRankService.getSearchLanguageId(ownDomainEntity);
	}

	@Override
	protected void execute() {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance()
				.logMemoryUsage("execute() begins. ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",rankDate="
						+ DateFormatUtils.format(rankDate, IConstants.DATE_FORMAT_YYYY_MM_DD) + ",startProcessDate="
						+ DateFormatUtils.format(startProcessDate, IConstants.DATE_FORMAT_YYYY_MM_DD) + ",endProcessDate="
						+ DateFormatUtils.format(endProcessDate, IConstants.DATE_FORMAT_YYYY_MM_DD) + ",searchEngineId=" + searchEngineId + ",searchLanguageId="
						+ searchLanguageId);
		try {
			if (searchEngineId == ScKeywordRankService.ENGINE_LANGUAGE_ID_NOT_FOUND) {
				FormatUtils.getInstance()
						.logMemoryUsage("execute() error--search engine ID not found. ip=" + ip + ", domainId=" + domainId + ", domainName=" + domainName);
			} else if (searchEngineId == IConstants.SEARCH_ENGINE_ID_BING) {
				FormatUtils.getInstance().logMemoryUsage("execute() skip--search engine ID 255. ip=" + ip + ", domainId=" + domainId + ", domainName=" + domainName);
			} else if (searchLanguageId == ScKeywordRankService.ENGINE_LANGUAGE_ID_NOT_FOUND) {
				FormatUtils.getInstance()
						.logMemoryUsage("execute() error--search language ID not found. ip=" + ip + ", domainId=" + domainId + ", domainName=" + domainName);
			} else {
				process();
			}
		} catch (Exception e) {
			FormatUtils.getInstance()
					.logMemoryUsage("execute() ip=" + ip + ",domainId=" + domainId + " ,domainName=" + domainName + ",exception message=" + e.getMessage());
			e.printStackTrace();
		} finally {
			CacheModleFactory.getInstance().setAliveIpAddress(ip);
			FormatUtils.getInstance()
					.logMemoryUsage("execute() ends. ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",totalAssociatedCompetitorUrlDisassociated="
							+ totalAssociatedCompetitorUrlDisassociated + " ,totalKeywordCompetitorUrlDisassociated=" + totalKeywordCompetitorUrlDisassociated
							+ " ,totalCompetitorUrlDisassociated=" + totalCompetitorUrlDisassociated + " ,totalCompetitorDisassociated=" + totalCompetitorDisassociated
							+ ",elapsed time in sec.=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		}
	}

	private void process() throws Exception {

		// step 1: update 'competitor_url_md5' table with the 't_competitor_url' records that are not already in this table.
		// at end of this method, there should be one 'competitor_url_md5' record for each 't_competitor_url' record.
		FormatUtils.getInstance().logMemoryUsage("process() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",b4 processCompetitorUrlHashCode()");
		processCompetitorUrlHashCode();
		FormatUtils.getInstance().logMemoryUsage("process() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",af processCompetitorUrlHashCode()");

		// step 2: determine unique process dates in the 'associated_competitor_url' table earlier than the cutoff date
		Map<String, List<String>> keywordCompetitorUrlListMap = RankCheckUtils.getRankedKeywordCompetitorUrlListMap(ip, ownDomainEntity, rankDate, topRankedPositions);
		if (keywordCompetitorUrlListMap == null || keywordCompetitorUrlListMap.size() == 0) {
			FormatUtils.getInstance()
					.logMemoryUsage("process() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",keywordCompetitorUrlListMap is empty.");
			return;
		}

		// step 3: determine unique process dates in the 'associated_competitor_url' table earlier than the cutoff date		
		int startProcessDateNumber = NumberUtils.toInt(DateFormatUtils.format(startProcessDate, IConstants.DATE_FORMAT_YYYYMMDD));
		int endProcessDateNumber = NumberUtils.toInt(DateFormatUtils.format(endProcessDate, IConstants.DATE_FORMAT_YYYYMMDD));
		List<AssociatedCompetitorUrlEntity> uniqueProcessDateList = associatedCompetitorUrlDAO.getDistinctProcessDates(domainId, startProcessDateNumber,
				endProcessDateNumber);
		// when there are process dates earlier than the cutoff date
		if (uniqueProcessDateList != null && uniqueProcessDateList.size() > 0) {

			// step 3: for each unique process date, retrieve all 'associated_competitor_url' records
			disassociateByProcessDate(uniqueProcessDateList, keywordCompetitorUrlListMap);
		}
	}

	// update 'competitor_url_md5' table with the 't_competitor_url' records that are not already in this table.
	// at end of this method, there should be one 'competitor_url_md5' record for each 't_competitor_url' record. 
	private void processCompetitorUrlHashCode() {
		String urlString = null;
		String hashCode = null;
		int competitorUrlId = 0;
		CompetitorUrlMd5Entity competitorUrlMd5EntityNew = null;
		CompetitorUrlMd5Entity competitorUrlMd5EntityExisting = null;
		int totalCompetitorUrlMd5Created = 0;
		int totalCompetitorUrlMd5AlreadyExist = 0;

		//competitorUrlMd5EntityDAO.cleanupInvalidCompetitorUrl(domainId);

		hashCodeCompetitorUrlMd5EntityMap = competitorUrlMd5EntityDAO.getByDomain(domainId);

		List<CompetitorUrlEntity> competitorUrlEntityList = competitorUrlEntityDAO.getByDomainId(domainId);
		if (competitorUrlEntityList != null && competitorUrlEntityList.size() > 0) {
			FormatUtils.getInstance().logMemoryUsage("processCompetitorUrlHashCode() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
					+ ",competitorUrlEntityList.size()=" + competitorUrlEntityList.size());
			forLoop1: for (CompetitorUrlEntity competitorUrlEntity : competitorUrlEntityList) {
				competitorUrlId = competitorUrlEntity.getId();
				// assumption: competitor URL string in 't_competitor_url' table is encoded
				urlString = competitorUrlEntity.getUrl();
				if (StringUtils.isNotBlank(urlString)) {

					// calculate hash code based on normalized URL string with the assumption that the URL string is encoded
					hashCode = SolrUtils.getNormalizedUrlStringMd5HashCode(urlString);
					if (StringUtils.isNotBlank(hashCode)) {
						if (hashCodeCompetitorUrlMd5EntityMap.containsKey(hashCode) == true) {
							competitorUrlMd5EntityExisting = hashCodeCompetitorUrlMd5EntityMap.get(hashCode);
						} else {
							competitorUrlMd5EntityExisting = null;
						}
						if (competitorUrlMd5EntityExisting == null) {
							competitorUrlMd5EntityNew = new CompetitorUrlMd5Entity();
							competitorUrlMd5EntityNew.setDomainId(domainId);
							competitorUrlMd5EntityNew.setHashCode(hashCode);
							competitorUrlMd5EntityNew.setCompetitorUrlId(competitorUrlId);
							competitorUrlMd5EntityDAO.create(competitorUrlMd5EntityNew);
							hashCodeCompetitorUrlMd5EntityMap.put(hashCode, competitorUrlMd5EntityNew);
							totalCompetitorUrlMd5Created++;
						} else {
							totalCompetitorUrlMd5AlreadyExist++;
							if (competitorUrlMd5EntityExisting.getCompetitorUrlId() == 0) {
								competitorUrlMd5EntityDAO.updateCompetitorUrlId(domainId, hashCode, competitorUrlId);
								competitorUrlMd5EntityExisting.setCompetitorUrlId(competitorUrlId);
								hashCodeCompetitorUrlMd5EntityMap.put(hashCode, competitorUrlMd5EntityExisting);
							}
						}
					}
					// error when hash code cannot be determined.
					else {
						FormatUtils.getInstance().logMemoryUsage("processCompetitorUrlHashCode() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
								+ ",skip urlString=" + urlString + ",error--hash code cannot be determined.");
						continue forLoop1;
					}
				}
			}
		}
		FormatUtils.getInstance()
				.logMemoryUsage("processCompetitorUrlHashCode() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
						+ ",total 'competitor_url_md5' created to sync up with 't_competitor_url'=" + totalCompetitorUrlMd5Created
						+ ",total 'competitor_url_md5' already exist=" + totalCompetitorUrlMd5AlreadyExist);
	}

	// step 2: for each unique process date, retrieve all 'associated_competitor_url' records for the auto-associations created before.
	private void disassociateByProcessDate(List<AssociatedCompetitorUrlEntity> uniqueProcessDateList, Map<String, List<String>> keywordCompetitorUrlListMap) {
		boolean isDisassociationRequired = false;
		List<AssociatedCompetitorUrlEntity> associatedCompetitorUrlEntityList = null;
		for (AssociatedCompetitorUrlEntity uniqueProcessDateEntity : uniqueProcessDateList) {
			associatedCompetitorUrlEntityList = associatedCompetitorUrlDAO.getByProcessDate(domainId, uniqueProcessDateEntity.getProcessDate());
			if (associatedCompetitorUrlEntityList != null && associatedCompetitorUrlEntityList.size() > 0) {
				//logMemoryUsage("disassociateByProcessDate()", "process date=" + uniqueProcessDateEntity.getProcessDate() + ",number of records="
				//		+ associatedCompetitorUrlEntityList.size());
				for (AssociatedCompetitorUrlEntity associatedCompetitorUrlEntity : associatedCompetitorUrlEntityList) {

					// step 3: for each 'associated_competitor_url' record, determine if disassociation is required.
					isDisassociationRequired = checkIfDisassociationRequired(associatedCompetitorUrlEntity, keywordCompetitorUrlListMap);

					// step 4: when disassociation is required
					if (isDisassociationRequired == true) {
						disassociate(associatedCompetitorUrlEntity);
					}
				}
			}
			//else {
			//	FormatUtils.getInstance().logMemoryUsage("disassociateByProcessDate() ip=" + ip + ",domainId=" + domainId + " ,domainName=" + domainName
			//			+ ",associatedCompetitorUrlEntityList is empty.");
			//}
		}

	}

	private boolean checkIfDisassociationRequired(AssociatedCompetitorUrlEntity associatedCompetitorUrlEntity, Map<String, List<String>> keywordCompetitorUrlListMap) {
		//System.out
		//		.println("checkIfDisassociationRequired() begins. ip=" + ip + ",domainId=" + domainId + " ,domainName=" + domainName
		//				+ ",associatedCompetitorUrlEntity: keywordId=" + associatedCompetitorUrlEntity.getKeywordId() + ",createAssociationOnlyInd="
		//				+ associatedCompetitorUrlEntity.getCreateAssociationOnlyInd() + ",competitorUrl="
		//				+ associatedCompetitorUrlEntity.getCompetitorUrl() + ",keywordName=" + associatedCompetitorUrlEntity.getKeywordName()
		//				+ ",processDate=" + associatedCompetitorUrlEntity.getProcessDate());
		boolean isDisassociationRequired = true;
		Integer rankCheckIndicator = null;
		boolean isTopRanked = false;
		String encodedKeyword = null;
		String competitorUrlString = null;

		// check 1: is keyword still in 't_keyword' table?
		Long keywordId = associatedCompetitorUrlEntity.getKeywordId();
		KeywordEntity keywordEntity = keywordEntityDAO.getById(keywordId, domainId);
		if (keywordEntity != null) {

			// check 2: is 't_keyword' record still needs to be rank check?
			rankCheckIndicator = keywordEntity.getRankCheck();
			if (rankCheckIndicator != null && rankCheckIndicator.intValue() == KeywordEntity.RANK_CHECK_ACTIVE) {

				// check 3: is the associated competitor URL still top-ranked for the keyword?
				encodedKeyword = associatedCompetitorUrlEntity.getKeywordName();
				competitorUrlString = associatedCompetitorUrlEntity.getCompetitorUrl();
				isTopRanked = checkIfCompetitorUrlStillTopRankedForKeyword(encodedKeyword, competitorUrlString, keywordCompetitorUrlListMap);
				if (isTopRanked == true) {
					isDisassociationRequired = false;
				}
			} else {
				//FormatUtils.getInstance().logMemoryUsage("checkIfDisassociationRequired() ip=" + ip + ",domainId=" + domainId + " ,domainName=" + domainName
				//		+ ",keyword no longer rank-check="+keywordEntity.getKeywordName());
			}
		} else {
			//FormatUtils.getInstance().logMemoryUsage("checkIfDisassociationRequired() ip=" + ip + ",domainId=" + domainId + " ,domainName=" + domainName
			//		+ ",keywordEntity is empty, keywordId="+keywordId);
		}
		//FormatUtils.getInstance().logMemoryUsage("checkIfDisassociationRequired() ends. ip=" + ip + ",domainId=" + domainId + " ,domainName=" + domainName
		//		+ ",isDisassociationRequired=" + isDisassociationRequired);
		return isDisassociationRequired;
	}

	private boolean checkIfCompetitorUrlStillTopRankedForKeyword(String encodedKeyword, String competitorUrlString,
			Map<String, List<String>> keywordCompetitorUrlListMap) {
		boolean isTopRanked = false;
		String topRankedCompetitorUrlHashCode = null;
		String competitorUrlHashCode = SolrUtils.getNormalizedUrlStringMd5HashCode(competitorUrlString);
		List<String> topRankedCompetitorUrlList = RankCheckUtils.getTopRankedCompetitorUrls(ip, encodedKeyword, keywordCompetitorUrlListMap);
		if (topRankedCompetitorUrlList != null && topRankedCompetitorUrlList.size() > 0) {
			forLoop1: for (String topRankedCompetitorUrl : topRankedCompetitorUrlList) {
				topRankedCompetitorUrlHashCode = SolrUtils.getNormalizedUrlStringMd5HashCode(topRankedCompetitorUrl);
				if (StringUtils.equalsIgnoreCase(competitorUrlHashCode, topRankedCompetitorUrlHashCode)) {
					isTopRanked = true;
					break forLoop1;
				}
			}
		}
		//FormatUtils.getInstance().logMemoryUsage("checkIfCompetitorUrlStillTopRankedForKeyword() ip=" + ip + ",domainId=" + domainId + " ,domainName=" + domainName
		//		+ ",encodedKeyword=" + encodedKeyword + ",competitorUrlString=" + competitorUrlString + ",isTopRanked=" + isTopRanked);
		return isTopRanked;
	}

	private void disassociate(AssociatedCompetitorUrlEntity associatedCompetitorUrlEntity) {
		//FormatUtils.getInstance().logMemoryUsage("disassociate() begins. ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
		//		+ ",associatedCompetitorUrlEntity=" + associatedCompetitorUrlEntity.toString());

		CompetitorUrlMd5Entity competitorUrlMd5Entity = null;
		List<KeywordCompetitorUrlEntity> keywordCompetitorUrlEntityList = null;
		KeywordCompetitorUrlEntity keywordCompetitorUrlEntity = null;
		Long keywordId = null;
		int competitorUrlId = 0;
		CompetitorUrlEntity competitorUrlEntity = null;
		int competitorId = 0;
		CompetitorEntity competitorEntity = null;
		Date competitorCreationDate = null;
		int totalCompetitorUrls = 0;
		boolean isCompetitorAutoAssociated = false;

		// step 1: retrieve competitor URL ID in the 't_competitor_url_md5' record by the MD5 hash code of the 'associated_competitor_url' record
		String competitorUrlMd5HashCode = associatedCompetitorUrlEntity.getCompetitorUrlHashCode();
		if (StringUtils.isNotBlank(competitorUrlMd5HashCode)) {
			if (hashCodeCompetitorUrlMd5EntityMap.containsKey(competitorUrlMd5HashCode) == true) {
				competitorUrlMd5Entity = hashCodeCompetitorUrlMd5EntityMap.get(competitorUrlMd5HashCode);
			} else {
				competitorUrlMd5Entity = null;
			}
			if (competitorUrlMd5Entity != null) {
				if (competitorUrlMd5Entity.getCompetitorUrlId() > 0) {
					// step 2: retrieve 't_keyword_competitorurl' record for the auto-associated keyword ID and competitor URL ID
					keywordId = associatedCompetitorUrlEntity.getKeywordId();
					competitorUrlId = competitorUrlMd5Entity.getCompetitorUrlId();
					keywordCompetitorUrlEntityList = keywordCompetitorUrlEntityDao.getByKeywordIdCompetitorUrlId(keywordId, competitorUrlId);
					if (keywordCompetitorUrlEntityList != null && keywordCompetitorUrlEntityList.size() == 1) {
						keywordCompetitorUrlEntity = keywordCompetitorUrlEntityList.get(0);
						//FormatUtils.getInstance().logMemoryUsage("disassociate() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
						//		+ ",associatedCompetitorUrlEntity=" + associatedCompetitorUrlEntity.toString() + ",competitorUrlMd5Entity="
						//		+ competitorUrlMd5Entity.toString() + ",keywordCompetitorUrlEntity=" + keywordCompetitorUrlEntity.toString());

						// step 3: dis-associate 't_keyword_competitorurl' record
						keywordCompetitorUrlEntityDao.deleteByKeywordIdAndCompetitorUrl(keywordId, competitorUrlId);
						totalKeywordCompetitorUrlDisassociated++;
						//FormatUtils.getInstance().logMemoryUsage("disassociate() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
						//		+ ",disassociated keywordCompetitorUrlEntity=" + keywordCompetitorUrlEntity.toString());
					}
					// it is possible that user disassociated the keyword and competitor URL
					else {
						//FormatUtils.getInstance()
						//		.logMemoryUsage("disassociate() warning--keywordCompetitorUrlEntity not available, ip=" + ip + ",domainId=" + domainId + ",domainName="
						//				+ domainName + ",associatedCompetitorUrlEntity=" + associatedCompetitorUrlEntity.toString() + ",competitorUrlMd5Entity="
						//				+ competitorUrlMd5Entity.toString());
					}

					// step 4: dis-associate 't_competitor_url' record when 't_competitor_url' was auto-associated before (ie. create_association_only_ind = 0)
					if (associatedCompetitorUrlEntity.getCreateAssociationOnlyInd() == CREATE_ASSOCIATION_ONLY_IND_FALSE) {

						// determine the competitor ID of the competitor URL
						competitorUrlEntity = competitorUrlEntityDAO.getById(competitorUrlId);
						if (competitorUrlEntity != null) {
							competitorId = competitorUrlEntity.getCompetitorId();
							competitorUrlEntityDAO.deleteById(competitorUrlId);
							totalCompetitorUrlDisassociated++;
							//FormatUtils.getInstance().logMemoryUsage("disassociate() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
							//		+ ",disassociated competitorUrlEntity=" + competitorUrlEntity.toString());

							// step 5: dis-associate 't_competitor' when there is no longer any 't_competitor_url' for the competitor and was auto-associated before

							// determine if there is any 't_competitor_url' records for the 't_competitor'
							totalCompetitorUrls = competitorUrlEntityDAO.calcTotalByCompetitorDomainId(competitorId);
							//FormatUtils.getInstance().logMemoryUsage("disassociate() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
							//		+ ",competitorId=" + competitorId + ",total competitor URLs=" + totalCompetitorUrls);

							// when there is no longer any 't_competitor_url' records for the 't_competitor'
							if (totalCompetitorUrls == 0) {
								competitorEntity = competitorEntityDAO.findById(competitorId);
								if (competitorEntity != null) {
									competitorCreationDate = competitorEntity.getCreateDate();
									if (competitorCreationDate != null) {
										isCompetitorAutoAssociated = false;
										// determine if the 't_competitor' was auto-associated before when there is no longer any 't_competitor_url'
										// by retrieving all the auto-associated creation date from the cached 'associate_competitor_url_audit_trail' table
										forLoop1: for (AssociatedCompetitorUrlAuditTrailEntity associatedCompetitorUrlAuditTrailEntity : CacheModleFactory.getInstance()
												.getAssociatedCompetitorUrlAuditTrailEntityList()) {
											// dis-associate 't_competitor' when 't_competitor' creation date is one of the audit trails
											if (competitorCreationDate.equals(associatedCompetitorUrlAuditTrailEntity.getBatchRunUpdateTimestamp())) {
												competitorEntityDAO.delete(competitorId);
												totalCompetitorDisassociated++;
												//FormatUtils.getInstance().logMemoryUsage("disassociate() ip=" + ip + ",domainId=" + domainId + ",domainName="
												//		+ domainName + ",disassociated competitorEntity=" + competitorEntity.toString());
												isCompetitorAutoAssociated = true;
												break forLoop1;
											}
										}
										//FormatUtils.getInstance().logMemoryUsage("disassociate() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
										//		+ ",competitor was auto-associated=" + isCompetitorAutoAssociated);
									}
									// error when 't_competitor' create_date is not available
									else {
										FormatUtils.getInstance()
												.logMemoryUsage("disassociate() error--competitorCreationDate not available, ip=" + ip + ",domainId=" + domainId
														+ ",domainName=" + domainName + ",associatedCompetitorUrlEntity=" + associatedCompetitorUrlEntity.toString()
														+ ",competitorUrlMd5Entity=" + competitorUrlMd5Entity.toString() + ",competitorUrlEntity="
														+ competitorUrlEntity.toString() + ",competitorEntity=" + competitorEntity.toString());
									}
								}
								// it is possible that user removed the competitor
								else {
									FormatUtils.getInstance()
											.logMemoryUsage("disassociate() warning--competitorEntity not available, ip=" + ip + ",domainId=" + domainId
													+ ",domainName=" + domainName + ",associatedCompetitorUrlEntity=" + associatedCompetitorUrlEntity.toString()
													+ ",competitorUrlMd5Entity=" + competitorUrlMd5Entity.toString() + ",competitorUrlEntity="
													+ competitorUrlEntity.toString());
								}
							}
						}
						// it is possible that user removed the competitor URL
						else {
							FormatUtils.getInstance()
									.logMemoryUsage("disassociate() warning--competitorUrlEntity not available, ip=" + ip + ",domainId=" + domainId + ",domainName="
											+ domainName + ",associatedCompetitorUrlEntity=" + associatedCompetitorUrlEntity.toString() + ",competitorUrlMd5Entity="
											+ competitorUrlMd5Entity.toString());
						}
					}
				}
				// error when 'competitor_url_md5' record's competitor URL ID is zero
				else {
					FormatUtils.getInstance()
							.logMemoryUsage("disassociate() error--competitorUrlId is zero in competitorUrlMd5Entity, ip=" + ip + ",domainId=" + domainId
									+ ",domainName=" + domainName + ",associatedCompetitorUrlEntity=" + associatedCompetitorUrlEntity.toString()
									+ ",competitorUrlMd5Entity=" + competitorUrlMd5Entity.toString());
				}
			}
			// it is possible that user has removed the competitor URL
			else {
				//FormatUtils.getInstance().logMemoryUsage("disassociate() warning--competitorUrlMd5Entity not available, ip=" + ip + ",domainId=" + domainId
				//		+ ",domainName=" + domainName + ",associatedCompetitorUrlEntity=" + associatedCompetitorUrlEntity.toString());
			}

			// remove the 'associated_competitor_url' record
			associatedCompetitorUrlDAO.delete(domainId, keywordId, competitorUrlMd5HashCode);
			//FormatUtils.getInstance().logMemoryUsage("disassociate() ip=" + ip + ",disassociated associated competitor URL: domainId=" + domainId + ",keywordId="
			//		+ keywordId + ",competitorUrlMd5HashCode=" + competitorUrlMd5HashCode);

			totalAssociatedCompetitorUrlDisassociated++;
		}
		// error when 'associated_competitor_url' record's 'competitor_url_hash_code' is not available
		else {
			FormatUtils.getInstance().logMemoryUsage("disassociate() error--competitorUrlMd5HashCode of associated_competitor_url not available, ip=" + ip
					+ ",domainId=" + domainId + ",domainName=" + domainName + ",associatedCompetitorUrlEntity=" + associatedCompetitorUrlEntity.toString());
		}

		//FormatUtils.getInstance().logMemoryUsage("disassociate() ends. ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
		//		+ ",associatedCompetitorUrlEntity=" + associatedCompetitorUrlEntity.toString());
	}

	@Override
	protected void undo() throws Exception {
		// TODO Auto-generated method stub
	}
}
