package com.actonia.auto.association;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Properties;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import com.actonia.IConstants;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.concurrency.ThreadPoolService;
import com.actonia.dao.AssociatedCompetitorUrlAuditTrailDAO;
import com.actonia.dao.AssociatedCompetitorUrlDAO;
import com.actonia.dao.CompetitorUrlMd5EntityDAO;
import com.actonia.dao.OwnDomainEntityDAO;
import com.actonia.dao.RankingDetailClickHouseDAO;
import com.actonia.entity.AssociatedCompetitorUrlAuditTrailEntity;
import com.actonia.entity.AssociatedCompetitorUrlEntity;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.utils.CommonUtils;
import com.actonia.utils.SpringBeanFactory;

/**
 * 
 * dis-associate competitor URLs auto-associated 90 days agos when no longer top-ranked 
 *  
 */
public class DisassociateTopRankedCompetitorUrls {

	public static final Log logger = LogFactory.getLog(DisassociateTopRankedCompetitorUrls.class);

	private static ThreadPoolService threadPool = ThreadPoolService.getInstance();

	private OwnDomainEntityDAO ownDomainEntityDAO;

	private Date rankDate;

	private int topRankedPositionsToBeProcessed = 0;

	private Date startProcessDate;

	private Date endProcessDate;

	private AssociatedCompetitorUrlAuditTrailDAO associatedCompetitorUrlAuditTrailDAO;

	private AssociatedCompetitorUrlDAO associatedCompetitorUrlDAO;

	private CompetitorUrlMd5EntityDAO competitorUrlMd5EntityDAO;

	public DisassociateTopRankedCompetitorUrls() {
		this.ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		this.associatedCompetitorUrlAuditTrailDAO = SpringBeanFactory.getBean("associatedCompetitorUrlAuditTrailDAO");
		this.associatedCompetitorUrlDAO = SpringBeanFactory.getBean("associatedCompetitorUrlDAO");
		this.competitorUrlMd5EntityDAO = SpringBeanFactory.getBean("competitorUrlMd5EntityDAO");
	}

	public static void main(String args[]) {
		System.out.println("main() begins.");
		long startTimestamp = System.currentTimeMillis();
		DisassociateTopRankedCompetitorUrls disassociateTopRankedCompetitorUrls = null;
		try {
			threadPool.init();
			CommonUtils.initThreads(8);
			disassociateTopRankedCompetitorUrls = new DisassociateTopRankedCompetitorUrls();
			disassociateTopRankedCompetitorUrls.initialize();
			disassociateTopRankedCompetitorUrls.process(args);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			threadPool.destroy();
		}
		System.out.println("main() ends. elapsed time in sec.:" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private void initialize() throws Exception {
		System.out.println("initialize() begins.");

		RankingDetailClickHouseDAO.getInstance();

		cleanup();

		System.out.println("initialize() ends.");
	}

	// cleanup 'associated_competitor_url' and 'competitor_url_md5' records that are no longer needed
	private void cleanup() {
		System.out.println("cleanup() begins.");
		long startTimestamp = System.currentTimeMillis();
		int domainId = 0;
		AssociatedCompetitorUrlEntity associatedCompetitorUrlEntity = null;
		List<OwnDomainEntity> ownDomainEntityList = ownDomainEntityDAO.getSuspendOwnDomains();
		if (ownDomainEntityList != null && ownDomainEntityList.size() > 0) {
			for (OwnDomainEntity ownDomainEntity : ownDomainEntityList) {
				domainId = ownDomainEntity.getId();
				associatedCompetitorUrlEntity = associatedCompetitorUrlDAO.getOneByDomainId(domainId);
				if (associatedCompetitorUrlEntity != null) {
					associatedCompetitorUrlDAO.delete(domainId);
					competitorUrlMd5EntityDAO.cleanup(domainId);
					System.out.println("cleanup() data cleaned up for domainId=" + domainId + ",domainName=" + ownDomainEntity.getDomain());
				}
			}
		}
		System.out.println("cleanup() ends. elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private void process(String args[]) throws Exception {

		// runtime parameter 1 (optional): the requested top ranked positions
		topRankedPositionsToBeProcessed = 5; //  by default, process top five ranked competitor URLs
		if (args != null && args.length >= 1) {
			String topRankedPositionsToBeProcessedString = args[0];
			System.out.println("process() runtime parameter 1: top ranked positions to be processed override=" + topRankedPositionsToBeProcessedString);
			if (StringUtils.isNotBlank(topRankedPositionsToBeProcessedString)) {
				if (NumberUtils.toInt(topRankedPositionsToBeProcessedString) != 0) {
					topRankedPositionsToBeProcessed = NumberUtils.toInt(topRankedPositionsToBeProcessedString);
				}
			}
		}
		System.out.println("process() top ranked positions to be processed=" + topRankedPositionsToBeProcessed);

		// runtime parameter 3 and 4 (optional): start process date and end process date (YYYY-MM-DD)
		// by default, start process date is 2016-03-07 and end process date is 90 days ago
		Date todayDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
		System.out.println("process() today's date=" + DateFormatUtils.format(todayDate, IConstants.DATE_FORMAT_YYYY_MM_DD));

		endProcessDate = DateUtils.addDays(todayDate, -90);
		startProcessDate = DateUtils.parseDate("2016-03-07", new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
		System.out.println("process() default start process date=" + DateFormatUtils.format(startProcessDate, IConstants.DATE_FORMAT_YYYY_MM_DD));
		System.out.println("process() default end process date=" + DateFormatUtils.format(endProcessDate, IConstants.DATE_FORMAT_YYYY_MM_DD));

		if (args != null && args.length >= 4) {
			String startProcessDateOverrideString = args[2];
			String endProcessDateOverrideString = args[3];
			System.out.println("process() runtime parameter 3: start process date override=" + startProcessDateOverrideString);
			System.out.println("process() runtime parameter 4: end process date override=" + endProcessDateOverrideString);
			if (StringUtils.isNotBlank(startProcessDateOverrideString)) {
				startProcessDate = DateUtils.parseDate(startProcessDateOverrideString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
			}
			if (StringUtils.isNotBlank(endProcessDateOverrideString)) {
				endProcessDate = DateUtils.parseDate(endProcessDateOverrideString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
			}
		}
		System.out.println("process() final startProcessDate=" + DateFormatUtils.format(startProcessDate, IConstants.DATE_FORMAT_YYYY_MM_DD));
		System.out.println("process() final endProcessDate=" + DateFormatUtils.format(endProcessDate, IConstants.DATE_FORMAT_YYYY_MM_DD));

		// rank date is always two days ago		
		rankDate = DateUtils.addDays(todayDate, -2);
		System.out.println("process() rankDate=" + DateFormatUtils.format(rankDate, IConstants.DATE_FORMAT_YYYY_MM_DD));

		Boolean isExecDomainIdsInd = null;
		Set<Integer> runtimeDomainSet = null;

		Properties domainProperties = new Properties();
		try {
			domainProperties.load(DisassociateTopRankedCompetitorUrls.class.getResourceAsStream("/domain.properties"));
		} catch (Exception e) {
			System.out.println("process() no domain.properties file found");
			System.exit(-1);
		}

		String execDomainIds = domainProperties.getProperty("exec.domain");
		String notExecDomainIds = domainProperties.getProperty("notexec.domain");
		System.out.println("process() execDomainIds=" + execDomainIds);
		System.out.println("process() notExecDomainIds=" + notExecDomainIds);

		List<OwnDomainEntity> allOwnDomainEntityList = new ArrayList<OwnDomainEntity>();

		allOwnDomainEntityList = ownDomainEntityDAO.queryForAll();

		// process specific domains
		if (StringUtils.isNotBlank(execDomainIds)) {
			runtimeDomainSet = CommonUtils.getDomainSet(execDomainIds);
			isExecDomainIdsInd = true;
		}
		// do not process specific domains
		else if (StringUtils.isNotBlank(notExecDomainIds)) {
			runtimeDomainSet = CommonUtils.getDomainSet(notExecDomainIds);
			isExecDomainIdsInd = false;
		}
		// process all domains
		else {
			runtimeDomainSet = null;
			isExecDomainIdsInd = null;
		}

		//System.out.println("process() competitorEntityDAO.calcTotal() b4=" + competitorEntityDAO.calcTotal());
		//System.out.println("process() competitorUrlEntityDAO.calcTotal() b4=" + competitorUrlEntityDAO.calcTotal());
		//System.out.println("process() keywordCompetitorUrlEntityDao.calcTotal() b4=" + keywordCompetitorUrlEntityDao.calcTotal());

		performConcurrentProcessing(allOwnDomainEntityList, isExecDomainIdsInd, runtimeDomainSet);

		//System.out.println("process() competitorEntityDAO.calcTotal() af=" + competitorEntityDAO.calcTotal());
		//System.out.println("process() competitorUrlEntityDAO.calcTotal() af=" + competitorUrlEntityDAO.calcTotal());
		//System.out.println("process() keywordCompetitorUrlEntityDao.calcTotal() af=" + keywordCompetitorUrlEntityDao.calcTotal());
	}

	private void performConcurrentProcessing(List<OwnDomainEntity> allOwnDomainEntityList, Boolean isExecDomainIdsInd, Set<Integer> runtimeDomainSet) throws Exception {

		int totalNumberOfDomains = allOwnDomainEntityList.size();
		long startTimestamp = System.currentTimeMillis();
		String ipAddress = null;
		OwnDomainEntity ownDomainEntity = null;
		DisassociateTopRankedCompetitorUrlsCommand disassociateTopRankedCompetitorUrlsCommand = null;
		int numberOfDomainsProcessed = 0;

		// cache all the 'associate_competitor_url_audit_trail' records
		List<AssociatedCompetitorUrlAuditTrailEntity> associatedCompetitorUrlAuditTrailEntityList = retrieveAssociatedCompetitorUrlAuditTrails();
		CacheModleFactory.getInstance().setAssociatedCompetitorUrlAuditTrailEntityList(associatedCompetitorUrlAuditTrailEntityList);

		do {
			ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
			if (ipAddress == null) {
				continue;
			}
			ownDomainEntity = allOwnDomainEntityList.get(numberOfDomainsProcessed++);
			disassociateTopRankedCompetitorUrlsCommand = null;
			if (isExecDomainIdsInd == null) {
				disassociateTopRankedCompetitorUrlsCommand = getDisassociateTopRankedCompetitorUrlsCommand(ipAddress, ownDomainEntity);
			} else if (isExecDomainIdsInd == true) {
				if (runtimeDomainSet.contains(ownDomainEntity.getId())) {
					disassociateTopRankedCompetitorUrlsCommand = getDisassociateTopRankedCompetitorUrlsCommand(ipAddress, ownDomainEntity);
				}
			} else if (isExecDomainIdsInd == false) {
				if (!runtimeDomainSet.contains(ownDomainEntity.getId())) {
					disassociateTopRankedCompetitorUrlsCommand = getDisassociateTopRankedCompetitorUrlsCommand(ipAddress, ownDomainEntity);
				}
			}
			if (ownDomainEntity.getId().intValue() == 6382) {
				disassociateTopRankedCompetitorUrlsCommand = null;
			}
			if (disassociateTopRankedCompetitorUrlsCommand != null) {
				try {
					//System.out.println("performConcurrentProcessing() ipAddress acquired=" + ipAddress + ",domain=" + ownDomainEntity.getId()
					//		+ " - " + ownDomainEntity.getDomain());
					threadPool.execute(disassociateTopRankedCompetitorUrlsCommand);
				} catch (Exception e) {
					e.printStackTrace();
				}
			} else {
				CacheModleFactory.getInstance().setAliveIpAddress(ipAddress);
			}
		} while (numberOfDomainsProcessed < totalNumberOfDomains);

		do {
			try {
				Thread.sleep(1000);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		} while (threadPool.getThreadPool().getActiveCount() > 0);
		System.out.println("performConcurrentProcessing() end. elapsed time in seconds=" + ((System.currentTimeMillis() - startTimestamp) / 1000));
	}

	private List<AssociatedCompetitorUrlAuditTrailEntity> retrieveAssociatedCompetitorUrlAuditTrails() {
		List<AssociatedCompetitorUrlAuditTrailEntity> associatedCompetitorUrlAuditTrailEntityList = associatedCompetitorUrlAuditTrailDAO.getAll();
		return associatedCompetitorUrlAuditTrailEntityList;
	}

	private DisassociateTopRankedCompetitorUrlsCommand getDisassociateTopRankedCompetitorUrlsCommand(String ip, OwnDomainEntity ownDomainEntity) {
		DisassociateTopRankedCompetitorUrlsCommand disassociateTopRankedCompetitorUrlsCommand = new DisassociateTopRankedCompetitorUrlsCommand(ip, ownDomainEntity,
				rankDate, topRankedPositionsToBeProcessed, startProcessDate, endProcessDate);
		disassociateTopRankedCompetitorUrlsCommand.setStatus(true);
		return disassociateTopRankedCompetitorUrlsCommand;
	}
}
