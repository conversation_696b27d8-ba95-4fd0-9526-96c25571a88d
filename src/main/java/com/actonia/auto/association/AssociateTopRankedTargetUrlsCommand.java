package com.actonia.auto.association;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;

import com.actonia.IConstants;
import com.actonia.concurrency.BaseThreadCommand;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.dao.AssociatedTargetUrlDAO;
import com.actonia.dao.RankingDetailClickHouseDAO;
import com.actonia.dao.TargetUrlEntityDAO;
import com.actonia.entity.AssociatedTargetUrlEntity;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.entity.TargetUrlEntity;
import com.actonia.service.ScKeywordRankService;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.Md5Util;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.AssociateTopRankedTargetUrlSummaryValueObject;

public class AssociateTopRankedTargetUrlsCommand extends BaseThreadCommand {
	private boolean isDebug = false;
	private String ip;
	private int domainId;
	private String domainName;
	private TargetUrlEntityDAO targetUrlEntityDAO;
	private Date rankDate;

	private static final int RECORDS_PER_BATCH_UPDATE = 100;
	private int searchEngineId = 0;
	private int searchLanguageId = 0;

	// yyyymmdd
	private int rankDateNumber = 0;

	private AssociatedTargetUrlDAO associatedTargetUrlDAO;

	private int totalTargetUrlsUpdatedWithoutUrl = 0;

	private int totalTargetUrlsUpdatedWithUrl = 0;

	private int totalTargetUrlsAlreadyType1 = 0;

	private int totalTargetUrlsUpdatedTotal = 0;

	private int totalTargetUrlsRanked = 0;

	private int totalAssociatedTargetUrlsCreated = 0;

	private String exceptionMessage = null;

	private Date processTimestamp = null;

	private int totalTargetUrlsAssociatedBefore = 0;

	private int totalTargetUrlErrors = 0;

	private List<AssociatedTargetUrlEntity> associatedTargetUrlEntityList = new ArrayList<AssociatedTargetUrlEntity>();

	private static final int ASSOCIATION_IND_TRUE = 1;

	private static final int ASSOCIATION_IND_FALSE = 0;

	// map key = target URL ID
	// map value = association indicator
	private Map<Long, Integer> targetUrlIdAssociationIndicatorMap = new HashMap<Long, Integer>();

	private static final String SINGLE_FORWARD_SLASHES = "/";

	private String subFolder = null;

	private int totalRankedUrlsHaveTargetUrlId = 0;

	private int totalRankedUrlsDoNotHaveTargetUrlId = 0;

	public AssociateTopRankedTargetUrlsCommand(String ip, OwnDomainEntity ownDomainEntity, Date rankDate, Date processTimestamp) {
		super();
		this.ip = ip;
		this.searchEngineId = ScKeywordRankService.getSearchEngineId(ownDomainEntity);
		this.searchLanguageId = ScKeywordRankService.getSearchLanguageId(ownDomainEntity);
		this.domainId = ownDomainEntity.getId();
		this.domainName = ownDomainEntity.getDomain();
		this.subFolder = ownDomainEntity.getSubFolder();
		this.rankDate = rankDate;
		this.targetUrlEntityDAO = SpringBeanFactory.getBean("targetUrlEntityDAO");
		this.associatedTargetUrlDAO = SpringBeanFactory.getBean("associatedTargetUrlDAO");
		this.rankDateNumber = NumberUtils.toInt(DateFormatUtils.format(rankDate, IConstants.DATE_FORMAT_YYYYMMDD));
		this.processTimestamp = processTimestamp;
	}

	@Override
	protected void execute() {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance()
				.logMemoryUsage("execute() begins. ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",rankDate="
						+ DateFormatUtils.format(rankDate, IConstants.DATE_FORMAT_YYYY_MM_DD) + ",searchEngineId=" + searchEngineId + ",searchLanguageId="
						+ searchLanguageId + ",subFolder=" + subFolder);
		try {
			process();
		} catch (Exception e) {
			FormatUtils.getInstance()
					.logMemoryUsage("execute() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",exception message=" + e.getMessage());
			e.printStackTrace();

			StringWriter stringWriter = new StringWriter();
			e.printStackTrace(new PrintWriter(stringWriter));
			exceptionMessage = stringWriter.toString();

		} finally {

			// update the 'AssociateTopRankedTargetUrlSummaryValueObject' in
			// cache for sending summary email
			updateSummary();

			CacheModleFactory.getInstance().setAliveIpAddress(ip);
			FormatUtils.getInstance().logMemoryUsage("execute() ends. ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",elapsed time in sec.="
					+ (System.currentTimeMillis() - startTimestamp) / 1000);
		}
	}

	private void process() throws Exception {

		// cache all the existing 'associated_target_url' record of this client
		// domain
		cacheAssociatedTargetUrlIds();

		// map key = target URL hash code
		// map value = target URL Id
		Map<String, Long> targetUrlHashCodeIdMap = targetUrlEntityDAO.getType1(domainId);
		if (targetUrlHashCodeIdMap != null && targetUrlHashCodeIdMap.size() > 0) {
			FormatUtils.getInstance().logMemoryUsage(
					"process() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",targetUrlHashCodeIdMap.size()=" + targetUrlHashCodeIdMap.size());

			// sync up the 'associated_target_url' with the 't_target_url' records with type 1
			syncAssociatedTargetUrlWithExistingType1(targetUrlHashCodeIdMap.values());
		} else {
			FormatUtils.getInstance()
					.logMemoryUsage("process() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",targetUrlHashCodeIdMap is empty.");
		}

		// associate the ranked URLs of the client domain for the ranked date
		associateTargetUrls(targetUrlHashCodeIdMap);
	}

	// cache all the existing 'associated_target_url' record of this client
	// domain.
	// output is 'targetUrlIdAssociationIndicatorMap'
	private void cacheAssociatedTargetUrlIds() {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("cacheAssociatedTargetUrlIds() begins. ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName);
		List<AssociatedTargetUrlEntity> testAssociatedTargetUrlEntityList = associatedTargetUrlDAO.getTargetUrlIds(domainId);
		if (testAssociatedTargetUrlEntityList != null && testAssociatedTargetUrlEntityList.size() > 0) {
			FormatUtils.getInstance().logMemoryUsage("cacheAssociatedTargetUrlIds() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
					+ ",testAssociatedTargetUrlEntityList.size()=" + testAssociatedTargetUrlEntityList.size());
			// create a 'target URL ID' to 'association indicator' map
			for (AssociatedTargetUrlEntity testAssociatedTargetUrlEntity : testAssociatedTargetUrlEntityList) {
				targetUrlIdAssociationIndicatorMap.put(testAssociatedTargetUrlEntity.getTargetUrlId(), testAssociatedTargetUrlEntity.getAssociationInd());
			}
		} else {
			FormatUtils.getInstance().logMemoryUsage(
					"cacheAssociatedTargetUrlIds() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",testAssociatedTargetUrlEntityList is empty.");
		}
		FormatUtils.getInstance()
				.logMemoryUsage("cacheAssociatedTargetUrlIds() ends. ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
						+ ",targetUrlIdAssociationIndicatorMap.size()=" + targetUrlIdAssociationIndicatorMap.size() + ",elapsed(s.)="
						+ (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	// check if all existing type 1 't_target_url' records are stored in 'associated_target_url' table...
	// mainly to sync up 'associated_target_url' table with 't_target_url' table
	// for those records created by users (type 1). input is 'targetUrlIdAssociationIndicatorMap'
	private void syncAssociatedTargetUrlWithExistingType1(Collection<Long> targetUrlIdCollection) {
		long startTimestamp = System.currentTimeMillis();
		Integer associationIndicator = null;
		AssociatedTargetUrlEntity associatedTargetUrlEntity = null;
		associatedTargetUrlEntityList = new ArrayList<AssociatedTargetUrlEntity>();

		// check if all type 1 are stored in 'associated_target_url'
		for (Long targetUrlId : targetUrlIdCollection) {

			// when target URL ID is in 'associated_target_url'
			if (targetUrlIdAssociationIndicatorMap.containsKey(targetUrlId)) {

				associationIndicator = targetUrlIdAssociationIndicatorMap.get(targetUrlId);

				// update 'association_ind' to '1' if not
				if (associationIndicator.intValue() == ASSOCIATION_IND_FALSE) {
					associatedTargetUrlEntity = getAssociatedTargetUrlEntityAlreadyType1(targetUrlId);
					associatedTargetUrlDAO.update(associatedTargetUrlEntity);
				}
			}
			// when not already created, create a 'associated_target_url' record
			else {
				associatedTargetUrlEntity = getAssociatedTargetUrlEntityAlreadyType1(targetUrlId);
				associatedTargetUrlEntityList.add(associatedTargetUrlEntity);
				if (associatedTargetUrlEntityList != null && associatedTargetUrlEntityList.size() == RECORDS_PER_BATCH_UPDATE) {
					associatedTargetUrlDAO.insertMultiRowsBatch(associatedTargetUrlEntityList);
					associatedTargetUrlEntityList = new ArrayList<AssociatedTargetUrlEntity>();
				}
			}

			// update 'targetUrlIdAssociationIndicatorMap'
			targetUrlIdAssociationIndicatorMap.put(targetUrlId, ASSOCIATION_IND_TRUE);
		}
		if (associatedTargetUrlEntityList != null && associatedTargetUrlEntityList.size() > 0) {
			associatedTargetUrlDAO.insertMultiRowsBatch(associatedTargetUrlEntityList);
			associatedTargetUrlEntityList = new ArrayList<AssociatedTargetUrlEntity>();
		}
		FormatUtils.getInstance()
				.logMemoryUsage("syncAssociatedTargetUrlWithExistingType1() ends. ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
						+ ",targetUrlIdAssociationIndicatorMap.size()=" + targetUrlIdAssociationIndicatorMap.size() + ",elapsed(s.)="
						+ (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private AssociatedTargetUrlEntity getAssociatedTargetUrlEntityAlreadyType1(long targetUrlId) {
		AssociatedTargetUrlEntity associatedTargetUrlEntity = new AssociatedTargetUrlEntity();
		associatedTargetUrlEntity.setAssociationComment("by user");
		associatedTargetUrlEntity.setAssociationInd(ASSOCIATION_IND_TRUE);
		associatedTargetUrlEntity.setDomainId(domainId);
		associatedTargetUrlEntity.setLastUpdateTimestamp(new Date());
		associatedTargetUrlEntity.setRankedUrl(null);
		associatedTargetUrlEntity.setRankedUrlHttpStatusCode(null);
		associatedTargetUrlEntity.setTargetUrlHttpStatusCode(null);
		associatedTargetUrlEntity.setTargetUrlId(targetUrlId);
		associatedTargetUrlEntity.setTargetUrlPreviousStatus(null);
		associatedTargetUrlEntity.setTargetUrlPreviousType(null);
		associatedTargetUrlEntity.setTargetUrlPreviousUrl(null);
		return associatedTargetUrlEntity;
	}

	// update 't_target_url' records for the ranked URLs.
	private void associateTargetUrls(Map<String, Long> targetUrlHashCodeIdMap) throws Exception {

		String rootDomainReverse = FormatUtils.getInstance().getReversedRootDomainName(StringUtils.trim(domainName));
		String domainReverse = FormatUtils.getInstance().getReversedDomainName(StringUtils.trim(domainName));

		FormatUtils.getInstance()
				.logMemoryUsage("associateTargetUrls() begins. ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",rankDateNumber=" + rankDateNumber
						+ ",searchEngineId=" + searchEngineId + ",searchLanguageId=" + searchLanguageId + ",rootDomainReverse=" + rootDomainReverse + ",domainReverse="
						+ domainReverse + ",targetUrlHashCodeIdMap.size()=" + targetUrlHashCodeIdMap.size() + ",processTimestamp="
						+ DateFormatUtils.format(processTimestamp, IConstants.DATE_FORMAT_YYYY_MM_DD_HHMMSS));
		long startTimestamp = System.currentTimeMillis();
		TargetUrlEntity targetUrlEntity = null;
		Integer targetUrlStatus = null;
		Integer targetUrlType = null;
		AssociatedTargetUrlEntity associatedTargetUrlEntity = null;
		Integer associationIndicator = null;
		String rankedUrlHashCode = null;
		Long rankedUrlStringId = null;

		// determine the corrected sub-folder of the client domain
		String correctedSubFolder = null;
		if (StringUtils.isNotBlank(subFolder)) {
			correctedSubFolder = getCorrectedSubFolder();
		}

		List<String> rankedUrlList = RankingDetailClickHouseDAO.getInstance().getUrlList(ip, domainId, searchEngineId, searchLanguageId, rankDate, rootDomainReverse,
				domainReverse);
		// when there are ranked URLs on the rank date for the client domain
		if (rankedUrlList != null && rankedUrlList.size() > 0) {
			FormatUtils.getInstance().logMemoryUsage(
					"associateTargetUrls() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",rankedUrlList.size()=" + rankedUrlList.size());
			totalTargetUrlsRanked = rankedUrlList.size();
			nextRankedUrl: for (String rankedUrlString : rankedUrlList) {
				if (isDebug == true) {
					FormatUtils.getInstance().logMemoryUsage(
							"associateTargetUrls() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",rankedUrlString=" + rankedUrlString);
				}

				// when client domain has sub-folder and ranked URL does not belong to sub-folder, skip...
				if (StringUtils.isNotBlank(correctedSubFolder)) {
					if (StringUtils.contains(rankedUrlString, correctedSubFolder) == false) {
						if (isDebug == true) {
							FormatUtils.getInstance().logMemoryUsage("associateTargetUrls() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
									+ ",correctedSubFolder=" + correctedSubFolder + ",skip rankedUrlString=" + rankedUrlString);
						}
						continue nextRankedUrl;
					}
					if (isDebug == true) {
						FormatUtils.getInstance().logMemoryUsage("associateTargetUrls() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
								+ ",correctedSubFolder=" + correctedSubFolder + ",process rankedUrlString=" + rankedUrlString);
					}
				}

				rankedUrlHashCode = Md5Util.Md5(StringUtils.trim(rankedUrlString));
				if (targetUrlHashCodeIdMap.containsKey(rankedUrlHashCode) == true) {
					rankedUrlStringId = targetUrlHashCodeIdMap.get(rankedUrlHashCode);
					totalRankedUrlsHaveTargetUrlId++;
				} else {
					//FormatUtils.getInstance()
					//		.logMemoryUsage("associateTargetUrls() error--skipped ranked URL, target URL ID cannot be determined. ip=" + ip + ",domainId=" + domainId
					//				+ ",domainName=" + domainName + ",correctedSubFolder=" + correctedSubFolder + ",skip rankedUrlString=" + rankedUrlString);
					totalRankedUrlsDoNotHaveTargetUrlId++;
					continue nextRankedUrl;
				}

				// when the target URL was auto-associated before, do not process again in case the target URL was changed back by other processes
				if (targetUrlIdAssociationIndicatorMap.containsKey(rankedUrlStringId)) {
					associationIndicator = targetUrlIdAssociationIndicatorMap.get(rankedUrlStringId);
					if (associationIndicator != null && associationIndicator.intValue() == ASSOCIATION_IND_TRUE) {
						totalTargetUrlsAssociatedBefore++;
						continue nextRankedUrl;
					}
				}

				// retrieve the 't_target_url' record with the ranked URL ID
				targetUrlEntity = targetUrlEntityDAO.getById(rankedUrlStringId);
				// when there is a corresponding 't_target_url' with the 'rankedUrlStringId'
				if (targetUrlEntity != null) {
					targetUrlStatus = targetUrlEntity.getStatus();
					targetUrlType = targetUrlEntity.getType();
					// when the 't_target_url' record's status is already 1
					// and type is already 1
					if (targetUrlStatus != null && targetUrlStatus.intValue() == 1 && targetUrlType != null && targetUrlType.intValue() == 1) {
						if (isDebug == true) {
							FormatUtils.getInstance()
									.logMemoryUsage("associateTargetUrls() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
											+ ",t_target_url already status 1 type 1. t_target_url.id=" + targetUrlEntity.getId() + ",t_target_url.url="
											+ targetUrlEntity.getUrl());
						}
						totalTargetUrlsAlreadyType1++;
					}
					// when the 't_target_url' record's status is not 1 and
					// type is not 1, to be associated
					else {
						associateWithoutUpdatingTargetUrl(targetUrlEntity, rankedUrlStringId);
					}
				}
				// when there is no corresponding 't_target_url' with the
				// 'rankedUrlStringId', error
				else {
					FormatUtils.getInstance().logMemoryUsage("associateTargetUrls() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
							+ ",error--'t_target_url' record not available for rankedUrlStringId=" + rankedUrlStringId);

					// create an audit trail 'associated_target_url' record
					// for this error
					associatedTargetUrlEntity = new AssociatedTargetUrlEntity();
					associatedTargetUrlEntity.setDomainId(domainId);
					associatedTargetUrlEntity.setTargetUrlId(rankedUrlStringId);
					associatedTargetUrlEntity.setAssociationInd(ASSOCIATION_IND_FALSE);
					associatedTargetUrlEntity.setAssociationComment("t_target_url record not available for ranked URL Id " + rankedUrlStringId);
					associatedTargetUrlEntity.setRankedUrl(rankedUrlString);
					associatedTargetUrlEntity.setLastUpdateTimestamp(processTimestamp);
					totalTargetUrlErrors++;
					if (targetUrlIdAssociationIndicatorMap.containsKey(associatedTargetUrlEntity.getTargetUrlId())) {
						FormatUtils.getInstance().logMemoryUsage("associateTargetUrls() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
								+ ",associatedTargetUrlEntity already created=" + associatedTargetUrlEntity.toString());
					} else {
						targetUrlIdAssociationIndicatorMap.put(associatedTargetUrlEntity.getTargetUrlId(), associatedTargetUrlEntity.getAssociationInd());
						associatedTargetUrlEntityList.add(associatedTargetUrlEntity);
					}
					if (associatedTargetUrlEntityList != null && associatedTargetUrlEntityList.size() == RECORDS_PER_BATCH_UPDATE) {
						associatedTargetUrlDAO.insertMultiRowsBatch(associatedTargetUrlEntityList);
						associatedTargetUrlEntityList = new ArrayList<AssociatedTargetUrlEntity>();
					}
				}
			}

			if (associatedTargetUrlEntityList != null && associatedTargetUrlEntityList.size() > 0) {
				associatedTargetUrlDAO.insertMultiRowsBatch(associatedTargetUrlEntityList);
				associatedTargetUrlEntityList = new ArrayList<AssociatedTargetUrlEntity>();
			}
		}

		FormatUtils.getInstance()
				.logMemoryUsage("associateTargetUrls() ends. ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",totalTargetUrlsRanked="
						+ totalTargetUrlsRanked + ",totalTargetUrlsAlreadyType1=" + totalTargetUrlsAlreadyType1 + ",totalRankedUrlsHaveTargetUrlId="
						+ totalRankedUrlsHaveTargetUrlId + ",totalRankedUrlsDoNotHaveTargetUrlId=" + totalRankedUrlsDoNotHaveTargetUrlId
						+ ",totalTargetUrlsUpdatedWithoutUrl=" + totalTargetUrlsUpdatedWithoutUrl + ",totalAssociatedTargetUrlsCreated="
						+ totalAssociatedTargetUrlsCreated + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);

	}

	private void associateWithoutUpdatingTargetUrl(TargetUrlEntity targetUrlEntity, Long rankedUrlStringId) {

		AssociatedTargetUrlEntity associatedTargetUrlEntity = null;
		Integer associationIndicator = null;

		// update the 't_target_url' record to status = 1 and type = 1
		targetUrlEntityDAO.updateStatusType(domainId, rankedUrlStringId, IConstants.TARGET_URL_STATUS_ACTIVE, IConstants.TARGET_URL_TYPE_ADDED_BY_USER);
		if (isDebug == true) {
			FormatUtils.getInstance().logMemoryUsage("associateWithoutUpdatingTargetUrl() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
					+ ",t_target_url updated. t_target_url.id=" + targetUrlEntity.getId() + ",t_target_url.url=" + targetUrlEntity.getUrl());
		}
		totalTargetUrlsUpdatedWithoutUrl++;
		totalTargetUrlsUpdatedTotal++;

		// create an audit trail in 'associated_target_url' table for the
		// 't_target_url' just updated
		associatedTargetUrlEntity = new AssociatedTargetUrlEntity();
		associatedTargetUrlEntity.setDomainId(domainId);
		associatedTargetUrlEntity.setTargetUrlId(rankedUrlStringId);
		associatedTargetUrlEntity.setTargetUrlPreviousStatus(targetUrlEntity.getStatus());
		associatedTargetUrlEntity.setTargetUrlPreviousType(targetUrlEntity.getType());
		associatedTargetUrlEntity.setAssociationInd(ASSOCIATION_IND_TRUE);
		associatedTargetUrlEntity.setLastUpdateTimestamp(processTimestamp);
		if (targetUrlIdAssociationIndicatorMap.containsKey(associatedTargetUrlEntity.getTargetUrlId())) {
			associationIndicator = targetUrlIdAssociationIndicatorMap.get(associatedTargetUrlEntity.getTargetUrlId());
			// when audit trail with association indicator = false was created
			// before
			if (associationIndicator != null && associationIndicator.intValue() == ASSOCIATION_IND_FALSE) {
				// update the audit trail to association indicator = true
				associatedTargetUrlDAO.update(associatedTargetUrlEntity);
				FormatUtils.getInstance().logMemoryUsage("associateWithoutUpdatingTargetUrl() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
						+ ",association indicator was false before, associatedTargetUrlEntity updated=" + associatedTargetUrlEntity.toString());
			}
			// skip when audit trail with association indicator = true was
			// created before
			else {
				FormatUtils.getInstance().logMemoryUsage("associateWithoutUpdatingTargetUrl() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
						+ ",associatedTargetUrlEntity already created=" + associatedTargetUrlEntity.toString());
			}
		} else {
			associatedTargetUrlEntityList.add(associatedTargetUrlEntity);
		}
		targetUrlIdAssociationIndicatorMap.put(associatedTargetUrlEntity.getTargetUrlId(), associatedTargetUrlEntity.getAssociationInd());
		if (isDebug == true) {
			FormatUtils.getInstance()
					.logMemoryUsage("associateWithoutUpdatingTargetUrl() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
							+ ",creating associated_target_url for target URL=" + targetUrlEntity.getUrl() + ",associatedTargetUrlEntity="
							+ associatedTargetUrlEntity.toString());
		}
		totalAssociatedTargetUrlsCreated++;
		if (associatedTargetUrlEntityList != null && associatedTargetUrlEntityList.size() == RECORDS_PER_BATCH_UPDATE) {
			associatedTargetUrlDAO.insertMultiRowsBatch(associatedTargetUrlEntityList);
			associatedTargetUrlEntityList = new ArrayList<AssociatedTargetUrlEntity>();
		}
	}

	// update the 'AssociateTopRankedTargetUrlSummaryValueObject' in cache for
	// sending summary email
	private void updateSummary() {

		if (totalTargetUrlsRanked == 0 && StringUtils.isBlank(exceptionMessage)) {
			return;
		}

		AssociateTopRankedTargetUrlSummaryValueObject associateTopRankedTargetUrlSummaryValueObject = new AssociateTopRankedTargetUrlSummaryValueObject();
		associateTopRankedTargetUrlSummaryValueObject.setDomainId(domainId);
		associateTopRankedTargetUrlSummaryValueObject.setDomainName(domainName);
		associateTopRankedTargetUrlSummaryValueObject.setTotalTargetUrlsRanked(totalTargetUrlsRanked);
		associateTopRankedTargetUrlSummaryValueObject.setTotalRankedUrlsHaveTargetUrlId(totalRankedUrlsHaveTargetUrlId);
		associateTopRankedTargetUrlSummaryValueObject.setTotalRankedUrlsDoNotHaveTargetUrlId(totalRankedUrlsDoNotHaveTargetUrlId);
		associateTopRankedTargetUrlSummaryValueObject.setTotalTargetUrlsAssociatedBefore(totalTargetUrlsAssociatedBefore);
		associateTopRankedTargetUrlSummaryValueObject.setTotalTargetUrlsAlreadyType1(totalTargetUrlsAlreadyType1);
		associateTopRankedTargetUrlSummaryValueObject.setTotalTargetUrlsUpdatedWithoutUrl(totalTargetUrlsUpdatedWithoutUrl);
		associateTopRankedTargetUrlSummaryValueObject.setTotalTargetUrlsUpdatedWithUrl(totalTargetUrlsUpdatedWithUrl);
		associateTopRankedTargetUrlSummaryValueObject.setTotalTargetUrlsUpdatedTotal(totalTargetUrlsUpdatedTotal);
		associateTopRankedTargetUrlSummaryValueObject.setTotalTargetUrlErrors(totalTargetUrlErrors);
		associateTopRankedTargetUrlSummaryValueObject.setExceptionMessage(exceptionMessage);		
		CacheModleFactory.getInstance().getAssociateTopRankedTargetUrlSummaryValueObjectList().add(associateTopRankedTargetUrlSummaryValueObject);
	}

	private String getCorrectedSubFolder() {
		String correctedSubFolder = StringUtils.lowerCase(subFolder);
		if (StringUtils.startsWith(subFolder, SINGLE_FORWARD_SLASHES)) {
			correctedSubFolder = domainName + StringUtils.lowerCase(subFolder);
		}
		return correctedSubFolder;
	}

	@Override
	protected void undo() throws Exception {
		// TODO Auto-generated method stub
	}
}
