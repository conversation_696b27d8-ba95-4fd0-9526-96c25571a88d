package com.actonia.auto.association;

import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.web.util.UriUtils;

import com.actonia.IConstants;
import com.actonia.concurrency.BaseThreadCommand;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.dao.AssociatedCompetitorUrlDAO;
import com.actonia.dao.CompetitorEntityDAO;
import com.actonia.dao.CompetitorUrlEntityDAO;
import com.actonia.dao.CompetitorUrlMd5EntityDAO;
import com.actonia.dao.KeywordCompetitorUrlEntityDao;
import com.actonia.dao.KeywordEntityDAO;
import com.actonia.entity.AssociatedCompetitorUrlEntity;
import com.actonia.entity.CompetitorEntity;
import com.actonia.entity.CompetitorUrlEntity;
import com.actonia.entity.CompetitorUrlMd5Entity;
import com.actonia.entity.KeywordCompetitorUrlEntity;
import com.actonia.entity.KeywordEntity;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.service.ScKeywordRankService;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.RankCheckUtils;
import com.actonia.utils.SolrUtils;
import com.actonia.utils.SpringBeanFactory;

public class AssociateTopRankedCompetitorUrlsCommand extends BaseThreadCommand {
	private boolean isDebug = false;
	private String ip;
	private int domainId;
	private String domainName;
	private CompetitorUrlEntityDAO competitorUrlEntityDAO;
	private CompetitorUrlMd5EntityDAO competitorUrlMd5EntityDAO;
	private KeywordEntityDAO keywordEntityDAO;
	private OwnDomainEntity ownDomainEntity;
	private Date rankDate;

	private int runDateNumber;
	private int topRankedPositions;

	// map key = hash code of competitor URLs already in database
	// map value = list of keyword ID associated
	private Map<String, HashSet<Long>> competitorUrlHashCodeKeywordIdSetMap = new HashMap<>();

	private CompetitorEntityDAO competitorEntityDAO;

	private KeywordCompetitorUrlEntityDao keywordCompetitorUrlEntityDao;

	private static final int MAX_ALLOWABLE_URL_LENGTH = 2000;

	private Date createDate = null;
	private int associatedCompetitorUrlCreated = 0;

	// for re-runnability, only create new 'associated_competitor_url' record when the 'associatedCompetitorUrlKeyCacheSet' contains the value  
	// value = keyword_id, competitor_url_hash_code and create_association_only_ind
	private Set<String> associatedCompetitorUrlKeyCacheSet = new HashSet<String>();

	private static final int CREATE_ASSOCIATION_ONLY_IND_FALSE = 0;
	private static final int CREATE_ASSOCIATION_ONLY_IND_TRUE = 1;

	private static final int RECORDS_PER_BATCH_UPDATE = 100;

	private List<AssociatedCompetitorUrlEntity> associatedCompetitorUrlEntityToBeCreatedList = null;

	private int associatedCompetitorUrlAlreadyExist = 0;
	private int totalCreateAssociationOnly = 0;

	private AssociatedCompetitorUrlDAO associatedCompetitorUrlDAO = null;
	private int searchEngineId = 0;
	private int searchLanguageId = 0;
	private Map<String, CompetitorUrlMd5Entity> hashCodeCompetitorUrlMd5EntityMap = new HashMap<String, CompetitorUrlMd5Entity>();

	public AssociateTopRankedCompetitorUrlsCommand(String ip, OwnDomainEntity ownDomainEntity, Date rankDate, int topRankedPositions, int runDateNumber,
			Date createDate) {
		super();
		this.ip = ip;
		this.ownDomainEntity = ownDomainEntity;
		this.domainId = ownDomainEntity.getId();
		this.domainName = ownDomainEntity.getDomain();
		this.rankDate = rankDate;
		this.competitorUrlEntityDAO = SpringBeanFactory.getBean("competitorUrlEntityDAO");
		this.competitorUrlMd5EntityDAO = SpringBeanFactory.getBean("competitorUrlMd5EntityDAO");
		this.keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
		this.topRankedPositions = topRankedPositions;
		this.runDateNumber = runDateNumber;
		this.competitorEntityDAO = SpringBeanFactory.getBean("competitorEntityDAO");
		this.keywordCompetitorUrlEntityDao = SpringBeanFactory.getBean("keywordCompetitorUrlEntityDao");
		this.associatedCompetitorUrlDAO = SpringBeanFactory.getBean("associatedCompetitorUrlDAO");
		this.createDate = createDate;
		this.searchEngineId = ScKeywordRankService.getSearchEngineId(ownDomainEntity);
		this.searchLanguageId = ScKeywordRankService.getSearchLanguageId(ownDomainEntity);
	}

	@Override
	protected void execute() {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("execute() begins. ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",rankDate="
				+ DateFormatUtils.format(rankDate, IConstants.DATE_FORMAT_YYYY_MM_DD) + ",searchEngineId=" + searchEngineId + ",searchLanguageId=" + searchLanguageId);
		try {
			if (searchEngineId == ScKeywordRankService.ENGINE_LANGUAGE_ID_NOT_FOUND) {
				FormatUtils.getInstance()
						.logMemoryUsage("execute() error--search engine ID not found. ip=" + ip + ", domainId=" + domainId + ", domainName=" + domainName);
			} else if (searchEngineId == IConstants.SEARCH_ENGINE_ID_BING) {
				FormatUtils.getInstance().logMemoryUsage("execute() skip--search engine ID 255. ip=" + ip + ", domainId=" + domainId + ", domainName=" + domainName);
			} else if (searchLanguageId == ScKeywordRankService.ENGINE_LANGUAGE_ID_NOT_FOUND) {
				FormatUtils.getInstance()
						.logMemoryUsage("execute() error--search language ID not found. ip=" + ip + ", domainId=" + domainId + ", domainName=" + domainName);
			} else {
				process();
			}
		} catch (Exception e) {
			FormatUtils.getInstance()
					.logMemoryUsage("execute() begins. ip=" + ip + ",domainId=" + domainId + " ,domainName=" + domainName + ",exception message=" + e.getMessage());
			e.printStackTrace();
		} finally {
			CacheModleFactory.getInstance().setAliveIpAddress(ip);
			FormatUtils.getInstance().logMemoryUsage("execute() ends. ip=" + ip + ",domainId=" + domainId + " ,domainName=" + domainName + ",searchEngineId="
					+ searchEngineId + ",searchLanguageId=" + searchLanguageId + ",elapsed time in sec.=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		}
	}

	private void process() throws Exception {

		// step one: update 'competitor_url_md5' table with the 't_competitor_url' records that are not already in this table.
		// at end of this method, there should be one 'competitor_url_md5' record for each 't_competitor_url' record.
		FormatUtils.getInstance().logMemoryUsage("process() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",b4 processCompetitorUrlHashCode()");
		processCompetitorUrlHashCode();
		FormatUtils.getInstance().logMemoryUsage("process() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",af processCompetitorUrlHashCode()");

		// step two: retrieve ranking data for each of the keyword to be ranked on the rank date 
		// and determine which ranked competitor URLs to be created
		processRankingData();
		FormatUtils.getInstance().logMemoryUsage("process() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",af processRankingData()");

		// step three: update 't_competitor', 't_competitor_url' and 't_keyword_competitorurl' table in the 'actonia' MySQL database
		// with the ranked competitor URL data determined in step two (ie. there are new 'associated_competitor_url' record created for the current run date).
		if (associatedCompetitorUrlCreated > 0) {
			updateCompetitorDataInDatabase();
			FormatUtils.getInstance()
					.logMemoryUsage("process() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",af updateCompetitorDataInDatabase()");
		} else {
			FormatUtils.getInstance().logMemoryUsage("process() ip=" + ip + ",domainId=" + domainId + " ,domainName=" + domainName
					+ ",skip updateCompetitorDataInDatabase() due to associatedCompetitorUrlCreated = 0");
		}

	}

	// update 'competitor_url_md5' table with the 't_competitor_url' records that are not already in this table.
	// at end of this method, there should be one 'competitor_url_md5' record for each 't_competitor_url' record. 
	private void processCompetitorUrlHashCode() {
		String urlString = null;
		String hashCode = null;
		int competitorUrlId = 0;
		CompetitorUrlMd5Entity competitorUrlMd5EntityNew = null;
		int totalCompetitorUrlMd5Created = 0;
		int totalCompetitorUrlMd5AlreadyExist = 0;
		CompetitorUrlMd5Entity competitorUrlMd5EntityExisting = null;

		FormatUtils.getInstance().logMemoryUsage("processCompetitorUrlHashCode() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
				+ ",b4 instantiating competitorUrlMd5EntityExistingList");

		//Integer invalidCompetitorUrlCount = competitorUrlMd5EntityDAO.getInvalidCompetitorUrlCount(domainId);
		//if (invalidCompetitorUrlCount != null && invalidCompetitorUrlCount.intValue() == 1) {
		//	competitorUrlMd5EntityDAO.cleanupInvalidCompetitorUrl(domainId);
		//}

		// cache all existing keyword to competitor URL associations
		competitorUrlHashCodeKeywordIdSetMap = getCompetitorUrlHashCodeKeywordIdListMap(domainId);

		hashCodeCompetitorUrlMd5EntityMap = competitorUrlMd5EntityDAO.getByDomain(domainId);

		List<CompetitorUrlEntity> competitorUrlEntityList = competitorUrlEntityDAO.getByDomainId(domainId);
		if (competitorUrlEntityList != null && competitorUrlEntityList.size() > 0) {

			FormatUtils.getInstance().logMemoryUsage("processCompetitorUrlHashCode() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
					+ ",af instantiating competitorUrlEntityList.size()=" + competitorUrlEntityList.size());

			nextCompetitorUrlEntity: for (CompetitorUrlEntity competitorUrlEntity : competitorUrlEntityList) {
				competitorUrlId = competitorUrlEntity.getId();
				// assumption: competitor URL string in 't_competitor_url' table is encoded
				urlString = competitorUrlEntity.getUrl();
				if (StringUtils.isNotBlank(urlString)) {

					// calculate hash code based on normalized URL string with the assumption that the URL string is encoded
					hashCode = SolrUtils.getNormalizedUrlStringMd5HashCode(urlString);
					if (StringUtils.isNotBlank(hashCode)) {
						competitorUrlMd5EntityExisting = hashCodeCompetitorUrlMd5EntityMap.getOrDefault(hashCode, null);
						if (competitorUrlMd5EntityExisting == null) {
							competitorUrlMd5EntityNew = new CompetitorUrlMd5Entity();
							competitorUrlMd5EntityNew.setDomainId(domainId);
							competitorUrlMd5EntityNew.setHashCode(hashCode);
							competitorUrlMd5EntityNew.setCompetitorUrlId(competitorUrlId);
							competitorUrlMd5EntityDAO.create(competitorUrlMd5EntityNew);
							hashCodeCompetitorUrlMd5EntityMap.put(hashCode, competitorUrlMd5EntityNew);
							totalCompetitorUrlMd5Created++;
						} else {
							totalCompetitorUrlMd5AlreadyExist++;
							if (competitorUrlMd5EntityExisting.getCompetitorUrlId() == 0) {
								competitorUrlMd5EntityDAO.updateCompetitorUrlId(domainId, hashCode, competitorUrlId);
								competitorUrlMd5EntityExisting.setCompetitorUrlId(competitorUrlId);
								hashCodeCompetitorUrlMd5EntityMap.put(hashCode, competitorUrlMd5EntityExisting);
							}
						}
					}
					// error when hash code cannot be determined.
					else {
						FormatUtils.getInstance().logMemoryUsage("processCompetitorUrlHashCode() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
								+ ",skip urlString=" + urlString + ",error--hash code cannot be determined.");
						continue nextCompetitorUrlEntity;
					}
				}
			}
		}
		FormatUtils.getInstance()
				.logMemoryUsage("processCompetitorUrlHashCode() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
						+ ",total 'competitor_url_md5' created to sync up with 't_competitor_url'=" + totalCompetitorUrlMd5Created
						+ ",total 'competitor_url_md5' already exist=" + totalCompetitorUrlMd5AlreadyExist + ",competitorUrlHashCodeKeywordIdListMap.size()="
						+ competitorUrlHashCodeKeywordIdSetMap.size());
	}

	private Map<String, HashSet<Long>> getCompetitorUrlHashCodeKeywordIdListMap(int domainId) {
		Map<String, HashSet<Long>> competitorUrlHashCodeKeywordIdListMap = new HashMap<>();
		String competitorUrlString = null;
		String hashCode = null;

		List<KeywordCompetitorUrlEntity> keywordCompetitorUrlEntityList = keywordCompetitorUrlEntityDao.getByDomainId(domainId);
		if (keywordCompetitorUrlEntityList != null && !keywordCompetitorUrlEntityList.isEmpty()) {
			HashSet<Long> keywordIds;
			for (KeywordCompetitorUrlEntity keywordCompetitorUrlEntity : keywordCompetitorUrlEntityList) {
				competitorUrlString = keywordCompetitorUrlEntity.getCompetitorUrl();
				if (StringUtils.isNotBlank(competitorUrlString)) {
					hashCode = SolrUtils.getNormalizedUrlStringMd5HashCode(competitorUrlString);
					if (StringUtils.isNotBlank(hashCode)) {
						keywordIds = competitorUrlHashCodeKeywordIdSetMap.getOrDefault(hashCode, new HashSet<>());
						keywordIds.add(keywordCompetitorUrlEntity.getKeywordId());
						competitorUrlHashCodeKeywordIdListMap.putIfAbsent(hashCode, keywordIds);
					}
				}
			}
		}
		return competitorUrlHashCodeKeywordIdListMap;
	}

	// process ranking data for each of the keyword to be ranked on the rank date
	private void processRankingData() throws Exception {
		List<String> competitorUrlList = null;
		String keywordName = null;
		associatedCompetitorUrlEntityToBeCreatedList = new ArrayList<AssociatedCompetitorUrlEntity>();
		String hashCode = null;
		CompetitorUrlMd5Entity competitorUrlMd5EntityNew = null;
		int competitorUrlMd5AlreadyExistForRankedCompetitorUrl = 0;
		int competitorUrlMd5CreatedForRankedCompetitorUrl = 0;
		int keywordProcessed = 0;
		int keywordsWithRequestedRankedData = 0;
		int keywordsWithoutRequestedRankedData = 0;
		boolean isCreateAssociationOnly = false;
		CompetitorUrlMd5Entity competitorUrlMd5EntityExisting = null;

		FormatUtils.getInstance()
				.logMemoryUsage("processRankingData() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",b4 retrieve all keywords to be ranked");
		List<KeywordEntity> keywordEntityList = keywordEntityDAO.getIdLowercaseName(domainId, KeywordEntity.RANK_CHECK_ACTIVE);
		if (keywordEntityList == null || keywordEntityList.size() == 0) {
			FormatUtils.getInstance()
					.logMemoryUsage("processRankingData() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",keywordEntityList is empty.");
			return;
		}
		FormatUtils.getInstance().logMemoryUsage("processRankingData() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
				+ ",af retrieve all keywords to be ranked, total number of keywords=" + keywordEntityList.size());
		FormatUtils.getInstance().logMemoryUsage("processRankingData() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName);
		int modulus = 0;
		String encodedUrlString = null;
		Long keywordId = null;
		int totalEncodedUrlTooLong = 0;
		HashSet<Long> keywordIds = null;
		boolean isKeywordCompetitorUrlProcessedBefore = false;

		Map<String, List<String>> keywordCompetitorUrlListMap = RankCheckUtils.getRankedKeywordCompetitorUrlListMap(ip, ownDomainEntity, rankDate, topRankedPositions);
		if (keywordCompetitorUrlListMap == null || keywordCompetitorUrlListMap.size() == 0) {
			FormatUtils.getInstance()
					.logMemoryUsage("processRankingData() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",keywordCompetitorUrlListMap is empty.");
			return;
		}

		// retrieve top ranking pages of each keyword
		forLoop2: for (KeywordEntity keywordEntity : keywordEntityList) {

			if (isDebug == true) {
				if (keywordProcessed >= 89) {
					break forLoop2;
				}
			}

			keywordProcessed++;

			if (isDebug == true) {
				modulus = keywordProcessed % 100;
			} else {
				modulus = keywordProcessed % 1000;
			}
			if (modulus == 0) {
				FormatUtils.getInstance().logMemoryUsage(
						"processRankingData() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",keywordProcessed=" + keywordProcessed);
			}
			keywordId = keywordEntity.getId();
			keywordName = keywordEntity.getKeywordName();
			competitorUrlList = RankCheckUtils.getTopRankedCompetitorUrls(ip, keywordName, keywordCompetitorUrlListMap);
			if (competitorUrlList != null && competitorUrlList.size() > 0) {
				keywordsWithRequestedRankedData++;
				nextCompetitorUrl: for (String competitorUrl : competitorUrlList) {
					try {
						// encode the URL string
						encodedUrlString = UriUtils.encodeHttpUrl(competitorUrl, IConstants.UTF_8);
					} catch (Exception e) {
						// when the ranked URL when it cannot be encoded, use the un-encoded form
						if (isDebug == true) {
							FormatUtils.getInstance().logMemoryUsage("processRankingData() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
									+ ",keywordName=" + keywordName + ",competitorUrl=" + competitorUrl + " cannot be encoded due to " + e.getMessage());
						}
						encodedUrlString = competitorUrl;
					}
					if (StringUtils.isNotBlank(encodedUrlString) && encodedUrlString.length() > MAX_ALLOWABLE_URL_LENGTH) {
						FormatUtils.getInstance().logMemoryUsage("processRankingData() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
								+ ",keywordName=" + keywordName + ",competitorUrl=" + competitorUrl + ",encoded URL string too long=" + encodedUrlString);
						totalEncodedUrlTooLong++;
						continue nextCompetitorUrl;
					}

					if (isDebug == true) {
						if (StringUtils.equalsIgnoreCase(encodedUrlString, competitorUrl) == false) {
							FormatUtils.getInstance().logMemoryUsage("processRankingData() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
									+ ",keywordName=" + keywordName + ",competitorUrl=" + competitorUrl + ",encoded URL string=" + encodedUrlString);
						}
					}

					// calculate hash code based on encoded ranked competitor URL string
					hashCode = SolrUtils.getNormalizedUrlStringMd5HashCode(encodedUrlString);

					// determine if the ranked competitor URL already in database by checking the competitor URL MD5 hash code table
					if (StringUtils.isNotBlank(hashCode)) {
						isKeywordCompetitorUrlProcessedBefore = checkIfKeywordCompetitorUrlProcessedBefore(keywordId, hashCode);
						if (isKeywordCompetitorUrlProcessedBefore == true) {
							if (isDebug == true) {
								FormatUtils.getInstance().logMemoryUsage("processRankingData() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
										+ ",keywordName=" + keywordName + ",competitorUrl=" + competitorUrl + ",skipped, was processed before by this process.");
							}
							continue nextCompetitorUrl;
						}

						isCreateAssociationOnly = false;
						keywordIds = competitorUrlHashCodeKeywordIdSetMap.getOrDefault(hashCode, new HashSet<>());

						// when the association has not been created..
						if (!keywordIds.contains(keywordId)) {

							// three possible scenarios:
							// 1) 't_competitor', 't_competitor_url' and 't_keyword_competitorurl' need to be created
							// 2) 't_competitor_url' and 't_keyword_competitorurl' need to be created
							// 3) 't_keyword_competitorurl' needs to be created

							// when the ranked competitor URL already in 'competitor_url_md5' table...
							competitorUrlMd5EntityExisting = hashCodeCompetitorUrlMd5EntityMap.getOrDefault(hashCode, null);
							if (competitorUrlMd5EntityExisting != null) {
								competitorUrlMd5AlreadyExistForRankedCompetitorUrl++;
								isCreateAssociationOnly = true;
								createAssociatedCompetitorUrl(keywordId, hashCode, encodedUrlString, keywordName, isCreateAssociationOnly);
							}
							// when the ranked competitor URL not already in 'competitor_url_md5' table...
							else {
								// create a 'competitor_url_md5' record for the ranked competitor URL
								competitorUrlMd5EntityNew = new CompetitorUrlMd5Entity();
								competitorUrlMd5EntityNew.setDomainId(domainId);
								competitorUrlMd5EntityNew.setHashCode(hashCode);
								competitorUrlMd5EntityNew.setCompetitorUrlId(0);
								competitorUrlMd5EntityDAO.create(competitorUrlMd5EntityNew);
								hashCodeCompetitorUrlMd5EntityMap.put(hashCode, competitorUrlMd5EntityNew);
								competitorUrlMd5CreatedForRankedCompetitorUrl++;
								createAssociatedCompetitorUrl(keywordId, hashCode, encodedUrlString, keywordName, isCreateAssociationOnly);
							}

							keywordIds.add(keywordId);
							competitorUrlHashCodeKeywordIdSetMap.put(hashCode, keywordIds);
						}
						// when the association has already been created
						else {
							if (isDebug == true) {
								FormatUtils.getInstance().logMemoryUsage("processRankingData() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
										+ ",keywordName=" + keywordName + ",competitorUrl=" + competitorUrl + ",association has already been created.");
							}
							continue nextCompetitorUrl;
						}
					}
					// error when hash code cannot be determined.
					else {
						FormatUtils.getInstance().logMemoryUsage("processRankingData() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
								+ ",keywordName=" + keywordName + ",competitorUrl=" + competitorUrl + ",error--hash code cannot be determined.");
						continue nextCompetitorUrl;
					}
				}
			} else {
				keywordsWithoutRequestedRankedData++;
			}
		}
		if (associatedCompetitorUrlEntityToBeCreatedList.size() > 0) {
			associatedCompetitorUrlDAO.insertMultiRowsBatch(associatedCompetitorUrlEntityToBeCreatedList);
			// reset for GC
			associatedCompetitorUrlEntityToBeCreatedList = null;
		}
		FormatUtils.getInstance()
				.logMemoryUsage("processRankingData() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",keywordProcessed=" + keywordProcessed);
		FormatUtils.getInstance().logMemoryUsage("processRankingData() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
				+ ",keywordsWithRequestedRankedData=" + keywordsWithRequestedRankedData);
		FormatUtils.getInstance().logMemoryUsage("processRankingData() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
				+ ",keywordsWithoutRequestedRankedData=" + keywordsWithoutRequestedRankedData);
		FormatUtils.getInstance().logMemoryUsage("processRankingData() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
				+ ",competitorUrlMd5CreatedForRankedCompetitorUrl=" + competitorUrlMd5CreatedForRankedCompetitorUrl);
		FormatUtils.getInstance().logMemoryUsage(
				"processRankingData() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",totalEncodedUrlTooLong=" + totalEncodedUrlTooLong);
		FormatUtils.getInstance().logMemoryUsage("processRankingData() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
				+ ",competitorUrlMd5AlreadyExistForRankedCompetitorUrl=" + competitorUrlMd5AlreadyExistForRankedCompetitorUrl);
		FormatUtils.getInstance().logMemoryUsage(
				"processRankingData() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",totalCreateAssociationOnly=" + totalCreateAssociationOnly);
		FormatUtils.getInstance().logMemoryUsage("processRankingData() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
				+ ",associatedCompetitorUrlAlreadyExist=" + associatedCompetitorUrlAlreadyExist);
		FormatUtils.getInstance().logMemoryUsage("processRankingData() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
				+ ",associatedCompetitorUrlCreated=" + associatedCompetitorUrlCreated);
		FormatUtils.getInstance().logMemoryUsage(
				"processRankingData() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",af all keywords to be ranked have been processed.");
	}

	private boolean checkIfKeywordCompetitorUrlProcessedBefore(Long keywordId, String hashCode) throws Exception {
		boolean isKeywordCompetitorUrlProcessedBefore = false;
		AssociatedCompetitorUrlEntity associatedCompetitorUrlEntity = associatedCompetitorUrlDAO.get(domainId, keywordId, hashCode);
		if (associatedCompetitorUrlEntity != null) {
			isKeywordCompetitorUrlProcessedBefore = true;
		}
		return isKeywordCompetitorUrlProcessedBefore;
	}

	private void createAssociatedCompetitorUrl(Long keywordId, String hashCode, String encodedUrlString, String keywordName, boolean isCreateAssociationOnly)
			throws Exception {
		HashSet<Long> keywordIds = null;

		// create 'associated_competitor_url' record when not already in 'associated_competitor_url' table
		AssociatedCompetitorUrlEntity associatedCompetitorUrlEntity = new AssociatedCompetitorUrlEntity();
		associatedCompetitorUrlEntity.setDomainId(domainId);
		associatedCompetitorUrlEntity.setProcessDate(runDateNumber);
		associatedCompetitorUrlEntity.setKeywordId(keywordId);
		associatedCompetitorUrlEntity.setCompetitorUrlHashCode(hashCode);
		associatedCompetitorUrlEntity.setCompetitorUrl(encodedUrlString);
		associatedCompetitorUrlEntity.setKeywordName(keywordName);

		// when competitor URL is ranked but association has not been created between keyword and competitor URL, 
		// create association only in this scenario
		if (isCreateAssociationOnly) {
			associatedCompetitorUrlEntity.setCreateAssociationOnlyInd(CREATE_ASSOCIATION_ONLY_IND_TRUE);
		} else {
			associatedCompetitorUrlEntity.setCreateAssociationOnlyInd(CREATE_ASSOCIATION_ONLY_IND_FALSE);
		}
		String documentId = getAssociatedCompetitorUrlDocumentId(associatedCompetitorUrlEntity);

		// create 'associated_competitor_url' record when the ranking competitor URL has not been processed (check data in 'associated_competitor_url' table)
		if (!associatedCompetitorUrlKeyCacheSet.contains(documentId)) {
			if (associatedCompetitorUrlEntity.getCreateAssociationOnlyInd() == CREATE_ASSOCIATION_ONLY_IND_TRUE) {
				totalCreateAssociationOnly++;
			}
			associatedCompetitorUrlKeyCacheSet.add(documentId);
			associatedCompetitorUrlEntityToBeCreatedList.add(associatedCompetitorUrlEntity);
			associatedCompetitorUrlCreated++;
			keywordIds = competitorUrlHashCodeKeywordIdSetMap.getOrDefault(hashCode, new HashSet<>());
			keywordIds.add(keywordId);
			competitorUrlHashCodeKeywordIdSetMap.putIfAbsent(hashCode, keywordIds);

			if (isDebug == true) {
				FormatUtils.getInstance().logMemoryUsage("createAssociatedCompetitorUrl() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
						+ ",associatedCompetitorUrlEntity=" + associatedCompetitorUrlEntity.toString());
			}
			if (associatedCompetitorUrlEntityToBeCreatedList.size() == RECORDS_PER_BATCH_UPDATE) {
				associatedCompetitorUrlDAO.insertMultiRowsBatch(associatedCompetitorUrlEntityToBeCreatedList);
				associatedCompetitorUrlEntityToBeCreatedList = new ArrayList<AssociatedCompetitorUrlEntity>();
			}
		} else {
			associatedCompetitorUrlAlreadyExist++;
			if (isDebug == true) {
				FormatUtils.getInstance()
						.logMemoryUsage("createAssociatedCompetitorUrl() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
								+ ",skip associatedCompetitorUrlEntity=" + associatedCompetitorUrlEntity.toString()
								+ ", already processed by this process during this batch run.");
			}
		}
	}

	private void updateCompetitorDataInDatabase() throws Exception {

		List<AssociatedCompetitorUrlEntity> associatedCompetitorUrlEntityList = new ArrayList<AssociatedCompetitorUrlEntity>();

		// step 1: retrieve all new competitor URLs for the current run date
		FormatUtils.getInstance().logMemoryUsage("updateCompetitorDataInDatabase() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
				+ ",b4 retrieving associatedCompetitorUrlEntityList by run date.");
		associatedCompetitorUrlEntityList = associatedCompetitorUrlDAO.getByDomainIdProcessDate(domainId, runDateNumber);
		FormatUtils.getInstance().logMemoryUsage("updateCompetitorDataInDatabase() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
				+ ",af retrieving associatedCompetitorUrlEntityList by run date, associatedCompetitorUrlEntityList.size()=" + associatedCompetitorUrlEntityList.size());

		// when there is no new competitor URLs for the current run date
		if (associatedCompetitorUrlEntityList == null || associatedCompetitorUrlEntityList.size() == 0) {
			return;
		}

		// step 2: create new competitor domains
		Map<String, Integer> competitorDomainIdMap = createCompetitorDomains(associatedCompetitorUrlEntityList);
		FormatUtils.getInstance().logMemoryUsage("updateCompetitorDataInDatabase() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
				+ ",af createCompetitorDomains(),competitorDomainIdMap.size()=" + competitorDomainIdMap.size());

		// step 3: create new competitor URLs
		createCompetitorUrls(competitorDomainIdMap, associatedCompetitorUrlEntityList);
		FormatUtils.getInstance()
				.logMemoryUsage("updateCompetitorDataInDatabase() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",af createCompetitorUrls()");

		// step 4: create new keyword to competitor URLs associations
		Set<Integer> competitorUrlIdSet = createKeywordCompetitorUrlsAssociations(associatedCompetitorUrlEntityList);
		FormatUtils.getInstance().logMemoryUsage(
				"updateCompetitorDataInDatabase() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",af createKeywordCompetitorUrlsAssociations()");

		// step 5: update competitor URL type to 'added by user'
		if (competitorUrlIdSet != null && competitorUrlIdSet.size() > 0) {
			updateCompetitorUrlIdSet(competitorUrlIdSet);
		}

	}

	// output is a map of (competitor domains + domain ID) and competitor domain IDs just created or already exists in 't_competitor' table.
	private Map<String, Integer> createCompetitorDomains(List<AssociatedCompetitorUrlEntity> associatedCompetitorUrlEntityList) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		Map<String, Integer> competitorDomainIdMap = new HashMap<String, Integer>();
		String competitorUrlString = null;
		URL url = null;
		String hostname = null;
		int totalCompetitorDomainsCreated = 0;
		int totalCompetitorDomainsAlreadyExists = 0;
		int totalCompetitorDomainsCreatedBefore = 0;

		// determine the unique ranked competitor domains set
		Set<String> rankedCompetitorDomainSet = new HashSet<String>();
		for (AssociatedCompetitorUrlEntity associatedCompetitorUrlEntity : associatedCompetitorUrlEntityList) {
			if (StringUtils.isNotBlank(associatedCompetitorUrlEntity.getCompetitorUrl())) {
				competitorUrlString = associatedCompetitorUrlEntity.getCompetitorUrl();
				try {
					url = new URL(competitorUrlString);
					hostname = url.getHost();
					rankedCompetitorDomainSet.add(hostname);
				} catch (Exception e) {
					// skip when competitor URL format is invalid					
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage(
				"createCompetitorDomains() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",af determing unique set of new competitor domains.");
		if (rankedCompetitorDomainSet != null && rankedCompetitorDomainSet.size() > 0) {
			FormatUtils.getInstance().logMemoryUsage("createCompetitorDomains() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
					+ ",total ranked competitor domains=" + rankedCompetitorDomainSet.size());
		} else {
			FormatUtils.getInstance().logMemoryUsage(
					"createCompetitorDomains() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",total ranked competitor domains=0");
		}

		// retrieve a list of existing 't_competitor'
		List<CompetitorEntity> competitorEntityExistingList = competitorEntityDAO.queryForAllByDomainId(domainId);
		FormatUtils.getInstance().logMemoryUsage(
				"createCompetitorDomains() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",af retrieving competitorEntityExistingList.");
		if (competitorEntityExistingList != null && competitorEntityExistingList.size() > 0) {
			FormatUtils.getInstance().logMemoryUsage("createCompetitorDomains() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
					+ ",total existing competitor domains=" + competitorEntityExistingList.size());
		} else {
			FormatUtils.getInstance().logMemoryUsage(
					"createCompetitorDomains() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",total existing competitor domains=0");
		}

		CompetitorEntity competitorEntityToBeCreated = null;
		boolean isToBeCreated = false;
		int recordId = 0;

		// create 't_competitor' record for the ranked competitor domain when it is not already in 't_competitor' 
		// and not already in 'ranked_competitor' audit trail table.
		if (rankedCompetitorDomainSet != null && rankedCompetitorDomainSet.size() > 0) {
			forLoop1: for (String rankedCompetitorDomain : rankedCompetitorDomainSet) {
				isToBeCreated = true;
				if (competitorEntityExistingList != null && competitorEntityExistingList.size() > 0) {
					for (CompetitorEntity existingCompetitorEntity : competitorEntityExistingList) {
						// when ranked competitor domain already in 't_competitor' table...
						if (StringUtils.equalsIgnoreCase(existingCompetitorEntity.getDomain(), rankedCompetitorDomain)) {
							isToBeCreated = false;
							competitorDomainIdMap.put(rankedCompetitorDomain, existingCompetitorEntity.getId());
							totalCompetitorDomainsAlreadyExists++;
							if (isDebug == true) {
								FormatUtils.getInstance().logMemoryUsage(
										"createCompetitorDomains() rankedCompetitorDomain=" + rankedCompetitorDomain + ", already in 't_competitor' table.");
							}
							continue forLoop1;
						}
					}
				}

				if (isDebug == true) {
					FormatUtils.getInstance()
							.logMemoryUsage("createCompetitorDomains() rankedCompetitorDomain=" + rankedCompetitorDomain + ",isToBeCreated=" + isToBeCreated);
				}

				if (isToBeCreated == true) {
					competitorEntityToBeCreated = new CompetitorEntity();
					competitorEntityToBeCreated.setDomain(rankedCompetitorDomain);
					competitorEntityToBeCreated.setName(rankedCompetitorDomain);
					competitorEntityToBeCreated.setCreateDate(createDate);
					competitorEntityToBeCreated.setOwndomainId(domainId);
					competitorEntityToBeCreated.setAddBy(CompetitorEntity.ADD_BY_USER);
					if (isDebug == true) {
						FormatUtils.getInstance().logMemoryUsage("createCompetitorDomains() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
								+ ",competitorEntityToBeCreated=" + competitorEntityToBeCreated.toString());
					}
					recordId = competitorEntityDAO.insertWithCreateDate(competitorEntityToBeCreated);

					if (recordId > 0) {
						competitorDomainIdMap.put(rankedCompetitorDomain, recordId);
						totalCompetitorDomainsCreated++;
						competitorEntityExistingList.add(competitorEntityToBeCreated);
					} else {
						FormatUtils.getInstance().logMemoryUsage("createCompetitorDomains() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
								+ ",error--competitor entity cannot be created=" + competitorEntityToBeCreated.toString());
					}
				}
			}
		}

		if (competitorDomainIdMap != null && competitorDomainIdMap.size() > 0) {
			if (isDebug == true) {
				for (String key : competitorDomainIdMap.keySet()) {
					FormatUtils.getInstance().logMemoryUsage("createCompetitorDomains() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
							+ ",competitorDomain=" + key + ",competitorDomainId=" + competitorDomainIdMap.get(key));
				}
			}
		} else {
			FormatUtils.getInstance().logMemoryUsage(
					"createCompetitorDomains() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",error--competitorDomainIdMap is empty.");
		}

		FormatUtils.getInstance()
				.logMemoryUsage("createCompetitorDomains() ends. ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",totalCompetitorDomainsCreated="
						+ totalCompetitorDomainsCreated + ",totalCompetitorDomainsAlreadyExists=" + totalCompetitorDomainsAlreadyExists
						+ ",totalCompetitorDomainsCreatedBefore=" + totalCompetitorDomainsCreatedBefore + ",elapsed time in sec.="
						+ (System.currentTimeMillis() - startTimestamp) / 1000);
		return competitorDomainIdMap;
	}

	private void createCompetitorUrls(Map<String, Integer> competitorDomainIdMap, List<AssociatedCompetitorUrlEntity> associatedCompetitorUrlEntityList) {
		long startTimestamp = System.currentTimeMillis();
		String competitorUrlString = null;
		URL url = null;
		String hostname = null;
		int competitorId = 0;
		CompetitorUrlEntity competitorUrlEntity = null;
		int competitorUrlId = 0;
		int totalRecordsUpdated = 0;
		int totalCompetitorUrlMd5EntityUpdated = 0;
		String documentId = null;
		int totalCompetitorUrlsCreated = 0;
		CompetitorUrlMd5Entity competitorUrlMd5EntityExisting = null;

		forLoop1: for (AssociatedCompetitorUrlEntity associatedCompetitorUrlEntity : associatedCompetitorUrlEntityList) {

			// when 'associatedCompetitorUrlKeyCacheSet' contains the keyword ID and competitor URL MD5 hash code of the 'associated_competitor_url' record,
			// then the 'associated_competitor_url' record was created during this batch run and therefore 't_competitor_url' record needs to be created.
			documentId = getAssociatedCompetitorUrlDocumentId(associatedCompetitorUrlEntity);

			// for the scenario where 't_keyword' and 't_competitor_url' are in database,
			// but 't_keyword_competitorurl' not created yet and now t_competitor_url is top ranked competitor URL,
			// therefore, only 't_keyword_competitorurl' need to be created but not 't_competitor_url'.
			if (associatedCompetitorUrlEntity.getCreateAssociationOnlyInd() == CREATE_ASSOCIATION_ONLY_IND_TRUE) {
				if (isDebug == true) {
					FormatUtils.getInstance().logMemoryUsage("createCompetitorUrls() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",documentId="
							+ documentId + ",skipped create association only.");
				}
				continue forLoop1;
			}
			if (associatedCompetitorUrlKeyCacheSet.contains(documentId)) {
				// determine the competitor domain ID
				competitorUrlString = associatedCompetitorUrlEntity.getCompetitorUrl();
				if (StringUtils.isNotBlank(competitorUrlString)) {
					try {
						url = new URL(competitorUrlString);
						hostname = url.getHost();
						if (competitorDomainIdMap.containsKey(hostname)) {
							competitorId = competitorDomainIdMap.get(hostname);
							if (isDebug == true) {
								FormatUtils.getInstance().logMemoryUsage("createCompetitorUrls() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
										+ ",competitorUrlString=" + competitorUrlString + ",hostname=" + hostname + ",competitor ID=" + competitorId);
							}
						}
						// error when competitor domain ID cannot be determined. 
						else {
							FormatUtils.getInstance().logMemoryUsage("createCompetitorUrls() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
									+ ",competitorUrlString=" + competitorUrlString + ",hostname=" + hostname + ",error---competitor domain ID cannot be determined.");
							continue forLoop1; // process next 'associated_competitor_url'... 
						}
					}
					// error when competitor URL format is invalid
					catch (Exception e) {
						FormatUtils.getInstance().logMemoryUsage("createCompetitorUrls() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
								+ ",competitorUrlString=" + competitorUrlString + ",error--exception message=" + e.getMessage());
						continue forLoop1; // process next 'associated_competitor_url'... 
					}
					competitorUrlEntity = new CompetitorUrlEntity();
					competitorUrlEntity.setCompetitorId(competitorId);
					competitorUrlEntity.setUrl(associatedCompetitorUrlEntity.getCompetitorUrl());
					competitorUrlEntity.setCreateDate(createDate);
					competitorUrlEntity.setAddBy(CompetitorEntity.ADD_BY_USER);
					competitorUrlEntity.setOwnDomainId(domainId);
					if (isDebug == true) {
						FormatUtils.getInstance().logMemoryUsage("createCompetitorUrls() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
								+ ",competitorUrlEntity to be created=" + competitorUrlEntity.toString());
					}
					competitorUrlId = competitorUrlEntityDAO.insert(competitorUrlEntity);
					// when t_competitor_url created 
					if (competitorUrlId > 0) {

						totalCompetitorUrlsCreated++;

						// update 'competitor_url_md5' record 'competitor_url_id' field with the competitor URL ID of the record just created
						totalRecordsUpdated = competitorUrlMd5EntityDAO.updateCompetitorUrlId(domainId, associatedCompetitorUrlEntity.getCompetitorUrlHashCode(),
								competitorUrlId);

						competitorUrlMd5EntityExisting = new CompetitorUrlMd5Entity();
						competitorUrlMd5EntityExisting.setDomainId(domainId);
						competitorUrlMd5EntityExisting.setHashCode(associatedCompetitorUrlEntity.getCompetitorUrlHashCode());
						competitorUrlMd5EntityExisting.setCompetitorUrlId(competitorUrlId);
						hashCodeCompetitorUrlMd5EntityMap.put(associatedCompetitorUrlEntity.getCompetitorUrlHashCode(), competitorUrlMd5EntityExisting);

						if (totalRecordsUpdated == 1) {
							totalCompetitorUrlMd5EntityUpdated++;
						} else {
							FormatUtils.getInstance()
									.logMemoryUsage("createCompetitorUrls() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
											+ ",error--competitor URL MD5 entity cannot be updated, hashCode="
											+ associatedCompetitorUrlEntity.getCompetitorUrlHashCode() + ",competitorUrlId=" + competitorUrlId);
						}
						associatedCompetitorUrlEntity.setCompetitorUrlId(competitorUrlId);
					}
					// when t_competitor_url cannot be created 
					else {
						FormatUtils.getInstance().logMemoryUsage("createCompetitorUrls() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
								+ ",error--competitor URL entity cannot be created=" + competitorUrlEntity.toString());
					}
				}
			} else {
				if (isDebug == true) {
					FormatUtils.getInstance().logMemoryUsage("createCompetitorUrls() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",documentId="
							+ documentId + ",skipped not in associatedCompetitorUrlKeyCacheSet");
				}
			}
		}
		FormatUtils.getInstance()
				.logMemoryUsage("createCompetitorUrls() ends. ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",totalCompetitorUrlsCreated="
						+ totalCompetitorUrlsCreated + ",totalCompetitorUrlMd5EntityUpdated=" + totalCompetitorUrlMd5EntityUpdated + ",elapsed time in sec.="
						+ (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private Set<Integer> createKeywordCompetitorUrlsAssociations(List<AssociatedCompetitorUrlEntity> associatedCompetitorUrlEntityList) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		Set<Integer> competitorUrlIdToBeUpdatedSet = new HashSet<Integer>();
		List<KeywordCompetitorUrlEntity> keywordCompetitorUrlEntityToBeCreatedList = new ArrayList<KeywordCompetitorUrlEntity>();
		KeywordCompetitorUrlEntity keywordCompetitorUrlEntity = null;
		String documentId = null;
		int totalKeywordCompetitorUrlsAssociationsCreated = 0;
		CompetitorUrlMd5Entity competitorUrlMd5Entity = null;
		List<KeywordCompetitorUrlEntity> keywordCompetitorUrlEntityExistingList = null;
		CompetitorUrlMd5Entity competitorUrlMd5EntityExisting = null;

		forLoop1: for (AssociatedCompetitorUrlEntity associatedCompetitorUrlEntity : associatedCompetitorUrlEntityList) {

			// when 'associatedCompetitorUrlKeyCacheSet' contains the keyword ID and competitor URL MD5 hash code of the 'associated_competitor_url' record,
			// then the 'associated_competitor_url' record was created during this batch run and therefore 't_competitor_url' record needs to be created.
			documentId = getAssociatedCompetitorUrlDocumentId(associatedCompetitorUrlEntity);

			if (associatedCompetitorUrlKeyCacheSet.contains(documentId)) {

				if (isDebug == true) {
					FormatUtils.getInstance().logMemoryUsage("createKeywordCompetitorUrlsAssociations() ip=" + ip + ",domainId=" + domainId + ",domainName="
							+ domainName + ",associatedCompetitorUrlEntity to be created=" + associatedCompetitorUrlEntity.toString());
				}

				if (associatedCompetitorUrlEntity.getCreateAssociationOnlyInd() == CREATE_ASSOCIATION_ONLY_IND_TRUE) {
					if (hashCodeCompetitorUrlMd5EntityMap.containsKey(associatedCompetitorUrlEntity.getCompetitorUrlHashCode()) == true) {
						competitorUrlMd5Entity = hashCodeCompetitorUrlMd5EntityMap.get(associatedCompetitorUrlEntity.getCompetitorUrlHashCode());
					} else {
						competitorUrlMd5Entity = null;
					}
					if (competitorUrlMd5Entity != null && competitorUrlMd5Entity.getCompetitorUrlId() > 0) {
						competitorUrlIdToBeUpdatedSet.add(competitorUrlMd5Entity.getCompetitorUrlId());
					} else {
						FormatUtils.getInstance()
								.logMemoryUsage("createKeywordCompetitorUrlsAssociations() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
										+ ",error--unable to determine competitor URL ID for associatedCompetitorUrlEntity="
										+ associatedCompetitorUrlEntity.toString());
						continue forLoop1;
					}
				}
				keywordCompetitorUrlEntity = new KeywordCompetitorUrlEntity();
				keywordCompetitorUrlEntity.setKeywordId(associatedCompetitorUrlEntity.getKeywordId());
				if (associatedCompetitorUrlEntity.getCreateAssociationOnlyInd() == CREATE_ASSOCIATION_ONLY_IND_TRUE) {
					keywordCompetitorUrlEntity.setCompetitorurlId(competitorUrlMd5Entity.getCompetitorUrlId());

					// update 'competitor_url_md5' record 'competitor_url_id' field with the competitor URL ID of the record just created
					competitorUrlMd5EntityDAO.updateCompetitorUrlId(domainId, associatedCompetitorUrlEntity.getCompetitorUrlHashCode(),
							competitorUrlMd5Entity.getCompetitorUrlId());

					competitorUrlMd5EntityExisting = new CompetitorUrlMd5Entity();
					competitorUrlMd5EntityExisting.setDomainId(domainId);
					competitorUrlMd5EntityExisting.setHashCode(associatedCompetitorUrlEntity.getCompetitorUrlHashCode());
					competitorUrlMd5EntityExisting.setCompetitorUrlId(competitorUrlMd5Entity.getCompetitorUrlId());
					hashCodeCompetitorUrlMd5EntityMap.put(associatedCompetitorUrlEntity.getCompetitorUrlHashCode(), competitorUrlMd5EntityExisting);

				} else {
					keywordCompetitorUrlEntity.setCompetitorurlId(associatedCompetitorUrlEntity.getCompetitorUrlId());
				}
				keywordCompetitorUrlEntity.setAddBy(KeywordCompetitorUrlEntity.ADD_BY_AUTOMAIC_ASSOCIATIONS);
				if (isDebug == true) {
					FormatUtils.getInstance()
							.logMemoryUsage("createKeywordCompetitorUrlsAssociations() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
									+ ",keywordCompetitorUrlEntity=" + keywordCompetitorUrlEntity.toString() + ",createAssociationOnlyInd="
									+ associatedCompetitorUrlEntity.getCreateAssociationOnlyInd());
				}
				keywordCompetitorUrlEntityExistingList = keywordCompetitorUrlEntityDao.getByKeywordIdCompetitorUrlId(keywordCompetitorUrlEntity.getKeywordId(),
						keywordCompetitorUrlEntity.getCompetitorurlId());
				if (keywordCompetitorUrlEntityExistingList == null || keywordCompetitorUrlEntityExistingList.size() == 0) {
					keywordCompetitorUrlEntityToBeCreatedList.add(keywordCompetitorUrlEntity);
					totalKeywordCompetitorUrlsAssociationsCreated++;
				} else {
					//FormatUtils.getInstance().logMemoryUsage("createKeywordCompetitorUrlsAssociations() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
					//		+ ",error--already created keywordCompetitorUrlEntity=" + keywordCompetitorUrlEntity.toString()
					//		+ ",createAssociationOnlyInd=" + associatedCompetitorUrlEntity.getCreateAssociationOnlyInd());
				}
				if (keywordCompetitorUrlEntityToBeCreatedList != null && keywordCompetitorUrlEntityToBeCreatedList.size() == RECORDS_PER_BATCH_UPDATE) {
					keywordCompetitorUrlEntityDao.insertMultiRowsBatch(keywordCompetitorUrlEntityToBeCreatedList);
					keywordCompetitorUrlEntityToBeCreatedList = new ArrayList<KeywordCompetitorUrlEntity>();
				}
			}
		}
		if (keywordCompetitorUrlEntityToBeCreatedList != null && keywordCompetitorUrlEntityToBeCreatedList.size() > 0) {
			keywordCompetitorUrlEntityDao.insertMultiRowsBatch(keywordCompetitorUrlEntityToBeCreatedList);
			keywordCompetitorUrlEntityToBeCreatedList = new ArrayList<KeywordCompetitorUrlEntity>();
		}
		FormatUtils.getInstance()
				.logMemoryUsage("createKeywordCompetitorUrlsAssociations() ends. ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
						+ ",totalKeywordCompetitorUrlsAssociationsCreated=" + totalKeywordCompetitorUrlsAssociationsCreated + ",elapsed time in sec.="
						+ (System.currentTimeMillis() - startTimestamp) / 1000);
		return competitorUrlIdToBeUpdatedSet;
	}

	// record key of the 'associated_competitor_url' record: domain_id,keyword_id,competitor_url_hash_code
	private String getAssociatedCompetitorUrlDocumentId(AssociatedCompetitorUrlEntity associatedCompetitorUrlEntity) {
		return associatedCompetitorUrlEntity.getDomainId() + IConstants.COMMA + associatedCompetitorUrlEntity.getKeywordId() + IConstants.COMMA
				+ associatedCompetitorUrlEntity.getCompetitorUrlHashCode();
	}

	private void updateCompetitorUrlIdSet(Set<Integer> competitorUrlIdToBeUpdatedSet) {

		List<Integer> testCompetitorUrlIdToBeUpdatedList = new ArrayList<Integer>();

		// break into smaller batches
		for (Integer competitorUrlId : competitorUrlIdToBeUpdatedSet) {
			testCompetitorUrlIdToBeUpdatedList.add(competitorUrlId);
			if (testCompetitorUrlIdToBeUpdatedList.size() >= RECORDS_PER_BATCH_UPDATE) {
				competitorUrlEntityDAO.updateToAddedByUser(testCompetitorUrlIdToBeUpdatedList);
				testCompetitorUrlIdToBeUpdatedList = new ArrayList<Integer>();
			}
		}
		if (testCompetitorUrlIdToBeUpdatedList.size() > 0) {
			competitorUrlEntityDAO.updateToAddedByUser(testCompetitorUrlIdToBeUpdatedList);
			testCompetitorUrlIdToBeUpdatedList = new ArrayList<Integer>();
		}
	}

	@Override
	protected void undo() throws Exception {
		// TODO Auto-generated method stub
	}
}
