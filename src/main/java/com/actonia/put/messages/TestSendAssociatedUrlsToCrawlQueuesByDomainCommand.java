package com.actonia.put.messages;

import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.concurrency.BaseThreadCommand;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.dao.AssociatedUrlEntityDAO;
import com.actonia.dao.CompetitorMsgClickHouseDAO;
import com.actonia.dao.CompetitorUrlWeeklyCrawlTrackingEntityDAO;
import com.actonia.entity.AssociatedUrlEntity;
import com.actonia.entity.CompetitorMsgClickHouseEntity;
import com.actonia.entity.CompetitorUrlWeeklyCrawlTrackingEntity;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.PutMessageUtils;
import com.actonia.utils.SQSUtils;
import com.actonia.utils.SpringBeanFactory;
import com.amazonaws.services.sqs.model.BatchRequestTooLongException;

public class TestSendAssociatedUrlsToCrawlQueuesByDomainCommand extends BaseThreadCommand {
	private Boolean isDebug = true; //debug
	private String ip;
	private String[] hostnames;
	private String queueUrl;
	private AssociatedUrlEntityDAO associatedUrlEntityDAO;

	private static final Integer MESSAGE_BATCH_SIZE = 10;
	private int trackDateNumber = 0;
	private static final int MAXIMUM_RETRY_COUNT = 5;

	// when SQS returns error, sleep for 88,888 milliseconds
	private static final int SQS_RETRY_SLEEP_TIME = 88888;

	private int totalMessagesSent = 0;

	private String processType;

	private CompetitorUrlWeeklyCrawlTrackingEntityDAO competitorUrlWeeklyCrawlTrackingEntityDAO;

	public TestSendAssociatedUrlsToCrawlQueuesByDomainCommand(String ip, String[] hostnames, String queueUrl, int trackDateNumber, String processType) {
		super();
		this.ip = ip;
		this.hostnames = hostnames;
		this.queueUrl = queueUrl;
		this.associatedUrlEntityDAO = SpringBeanFactory.getBean("associatedUrlEntityDAO");
		this.trackDateNumber = trackDateNumber;
		this.processType = processType;
		this.competitorUrlWeeklyCrawlTrackingEntityDAO = SpringBeanFactory.getBean("competitorUrlWeeklyCrawlTrackingEntityDAO");
	}

	@Override
	protected void execute() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		//FormatUtils.getInstance().logMemoryUsage(
		//		"execute() begins. ip=" + ip + ",hostnames=" + Arrays.toString(hostnames) + ",queueUrl=" + queueUrl + ",trackDateNumber=" + trackDateNumber);
		List<AssociatedUrlEntity> associatedUrlEntityList = new ArrayList<AssociatedUrlEntity>();
		List<AssociatedUrlEntity> testAssociatedUrlEntityList = null;

		List<CompetitorMsgClickHouseEntity> competitorMsgClickHouseEntityList = new ArrayList<CompetitorMsgClickHouseEntity>();
		List<CompetitorMsgClickHouseEntity> testCompetitorMsgClickHouseEntityList = null;
		Date trackDate = DateUtils.parseDate(String.valueOf(trackDateNumber), new String[] { IConstants.DATE_FORMAT_YYYYMMDD });

		try {
			for (String hostname : hostnames) {
				if (PutMessageUtils.getInstance().isProcessTypeSolr(processType) == true) {
					testAssociatedUrlEntityList = associatedUrlEntityDAO.getUrlsByHostname(hostname, trackDateNumber);
					if (testAssociatedUrlEntityList != null && testAssociatedUrlEntityList.size() > 0) {
						associatedUrlEntityList.addAll(testAssociatedUrlEntityList);
					}
				} else {
					testCompetitorMsgClickHouseEntityList = CompetitorMsgClickHouseDAO.getInstance().getMsgsByDomain(trackDate, hostname, null);
					if (testCompetitorMsgClickHouseEntityList != null && testCompetitorMsgClickHouseEntityList.size() > 0) {
						competitorMsgClickHouseEntityList.addAll(testCompetitorMsgClickHouseEntityList);
					}
				}
			}

			createMessagesForClickHouseCrawl(competitorMsgClickHouseEntityList);

		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			//FormatUtils.getInstance().logMemoryUsage("execute() ends. ip=" + ip + ",hostnames=" + Arrays.toString(hostnames) + ",queueUrl=" + queueUrl
			//		+ ",trackDateNumber=" + trackDateNumber + ",elapsed time in sec.:" + (System.currentTimeMillis() - startTimestamp) / 1000);
			CacheModleFactory.getInstance().setAliveIpAddress(ip);
		}
	}

	@Override
	protected void undo() throws Exception {
		// TODO Auto-generated method stub

	}

	private void createMessagesForClickHouseCrawl(List<CompetitorMsgClickHouseEntity> competitorMsgClickHouseEntityList) throws Exception {
		//FormatUtils.getInstance().logMemoryUsage("createMessagesForClickHouseCrawl() begins. ip=" + ip + ",hostnames=" + Arrays.toString(hostnames) + ",queueUrl="
		//		+ queueUrl + ",competitorMsgClickHouseEntityList.size()=" + competitorMsgClickHouseEntityList.size());
		//long startTimestamp = System.currentTimeMillis();
		String messageIdString = null;
		String messageBody = null;
		int currentRetryCount = 0;
		int modulus = 0;
		CompetitorUrlWeeklyCrawlTrackingEntity competitorUrlWeeklyCrawlTrackingEntity = null;
		List<CompetitorUrlWeeklyCrawlTrackingEntity> competitorUrlWeeklyCrawlTrackingEntityList = null;
		String hostname = null;

		Map<String, String> messages = new HashMap<String, String>();

		Map<String, String> testMessages = new HashMap<String, String>();
		String value = null;
		String host = null;
		URL url = null;

		if (competitorMsgClickHouseEntityList != null && competitorMsgClickHouseEntityList.size() > 0) {

			if (isDebug == false) {
				// create 'competitor_url_weekly_crawl_tracking' record when total competitor URLs of hostname exceeds tracking threshold
				if (competitorMsgClickHouseEntityList.size() >= IConstants.COMPETITOR_WEEKLY_CRAWL_TRACKING_THRESHOLD) {
					hostname = hostnames[0];
					competitorUrlWeeklyCrawlTrackingEntity = competitorUrlWeeklyCrawlTrackingEntityDAO.get(IConstants.PROCESS_TYPE_NUMBER_CLICKHOUSE,
							StringUtils.substringAfterLast(queueUrl, IConstants.SLASH));
					if (competitorUrlWeeklyCrawlTrackingEntity == null) {
						competitorUrlWeeklyCrawlTrackingEntity = new CompetitorUrlWeeklyCrawlTrackingEntity();
						competitorUrlWeeklyCrawlTrackingEntity.setProcessType(IConstants.PROCESS_TYPE_NUMBER_CLICKHOUSE);
						competitorUrlWeeklyCrawlTrackingEntity.setQueueName(StringUtils.substringAfterLast(queueUrl, IConstants.SLASH));
						competitorUrlWeeklyCrawlTrackingEntity.setCompetitorDomain(hostname);
						competitorUrlWeeklyCrawlTrackingEntity.setTotalUrls(competitorMsgClickHouseEntityList.size());
						competitorUrlWeeklyCrawlTrackingEntity.setConcurrentRequests(1);
						competitorUrlWeeklyCrawlTrackingEntity.setLastUpdateTimestamp(new Date());
						competitorUrlWeeklyCrawlTrackingEntityList = new ArrayList<CompetitorUrlWeeklyCrawlTrackingEntity>();
						competitorUrlWeeklyCrawlTrackingEntityList.add(competitorUrlWeeklyCrawlTrackingEntity);
						competitorUrlWeeklyCrawlTrackingEntityDAO.insertMultiRowsBatch(competitorUrlWeeklyCrawlTrackingEntityList);
					}
				}
			}

			nextCompetitorMsgClickHouseEntity: for (CompetitorMsgClickHouseEntity competitorMsgClickHouseEntity : competitorMsgClickHouseEntityList) {

				try {
					host = null;
					url = new URL(competitorMsgClickHouseEntity.getUrl());
					host = url.getHost();
				} catch (Exception e) {
				}

				// do not process any of the 'Expedia' and 'CarRentals.com' URLs
				if (StringUtils.isNotBlank(host)) {
					if (StringUtils.containsIgnoreCase(host, ".expedia.") || StringUtils.containsIgnoreCase(host, ".carrentals.")
							|| StringUtils.containsIgnoreCase(host, ".hotels.") || StringUtils.containsIgnoreCase(host, ".hoteles.")
							|| StringUtils.containsIgnoreCase(host, ".hoteis.")) {
						continue nextCompetitorMsgClickHouseEntity;
					} else {
						for (String expediaDomain : SendAssociatedUrlsToCrawlQueuesByDomain.getExpediaDomainList()) {
							if (StringUtils.containsIgnoreCase(host, expediaDomain)) {
								continue nextCompetitorMsgClickHouseEntity;
							}
						}
					}
				}

				messageBody = competitorMsgClickHouseEntity.getMsg();
				messageIdString = String.valueOf(System.nanoTime());
				try {
					messages.put(messageIdString, messageBody);
					if (messages.size() >= MESSAGE_BATCH_SIZE) {
						// put messages to queue
						currentRetryCount = 0;
						whileLoop1: while (currentRetryCount < MAXIMUM_RETRY_COUNT) {
							try {
								SQSUtils.getInstance().sendBatchMessageToQueueThrowsException(queueUrl, messages);
								totalMessagesSent = totalMessagesSent + messages.size();
								modulus = totalMessagesSent % 10000;
								if (modulus == 0) {
									//FormatUtils.getInstance().logMemoryUsage("createMessagesForClickHouseCrawl() ip=" + ip + ",hostnames=" + Arrays.toString(hostnames)
									//		+ ",queueUrl=" + queueUrl + ",interim totalMessagesSent=" + totalMessagesSent);
								}
								currentRetryCount = MAXIMUM_RETRY_COUNT;
								break whileLoop1;
							} catch (BatchRequestTooLongException e) {
								for (String key : messages.keySet()) {
									testMessages = new HashMap<String, String>();
									value = messages.get(key);
									testMessages.put(key, value);
									try {
										//FormatUtils.getInstance().logMemoryUsage("createMessagesForClickHouseCrawl() ip=" + ip + ",queueUrl=" + queueUrl
										//		+ ",BatchRequestTooLongException re-sending message=" + value);
										SQSUtils.getInstance().sendBatchMessageToQueueThrowsException(queueUrl, testMessages);
										//FormatUtils.getInstance().logMemoryUsage("createMessagesForClickHouseCrawl() ip=" + ip + ",queueUrl=" + queueUrl
										//		+ ",BatchRequestTooLongException resent message=" + value);
									} catch (Exception e2) {
										FormatUtils.getInstance().logMemoryUsage("createMessagesForClickHouseCrawl() ip=" + ip + ",queueUrl=" + queueUrl
												+ ",BatchRequestTooLongException exception encountered while resending message=" + value);
										e.printStackTrace();
									}
								}
								totalMessagesSent = totalMessagesSent + messages.size();
								messages = new HashMap<String, String>();
								currentRetryCount = MAXIMUM_RETRY_COUNT;
								break whileLoop1;
							} catch (Exception e) {
								e.printStackTrace();
								currentRetryCount++;
								FormatUtils.getInstance().logMemoryUsage("createMessagesForClickHouseCrawl() ip=" + ip + ",queueUrl=" + queueUrl
										+ ", error for AmazonSQS, sleep and try again, currentRetryCount=" + currentRetryCount + ",current timestamp=" + new Date());
								Thread.sleep(SQS_RETRY_SLEEP_TIME);
								continue whileLoop1;
							}
						}
						messages = new HashMap<String, String>();
					}
					Thread.sleep(100);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
		if (messages != null && messages.size() > 0) {

			// put messages to queue
			currentRetryCount = 0;
			whileLoop1: while (currentRetryCount < MAXIMUM_RETRY_COUNT) {
				try {
					SQSUtils.getInstance().sendBatchMessageToQueueThrowsException(queueUrl, messages);
					totalMessagesSent = totalMessagesSent + messages.size();
					modulus = totalMessagesSent % 1000;
					if (modulus == 0) {
						//FormatUtils.getInstance().logMemoryUsage("createMessagesForClickHouseCrawl() ip=" + ip + ",hostnames=" + Arrays.toString(hostnames)
						//		+ ",queueUrl=" + queueUrl + ",totalMessagesSent=" + totalMessagesSent);
					}
					currentRetryCount = MAXIMUM_RETRY_COUNT;
					break whileLoop1;
				} catch (BatchRequestTooLongException e) {
					for (String key : messages.keySet()) {
						testMessages = new HashMap<String, String>();
						value = messages.get(key);
						testMessages.put(key, value);
						try {
							//FormatUtils.getInstance().logMemoryUsage("createMessagesForClickHouseCrawl() ip=" + ip + ",queueUrl=" + queueUrl
							//		+ ",BatchRequestTooLongException re-sending message=" + value);
							SQSUtils.getInstance().sendBatchMessageToQueueThrowsException(queueUrl, testMessages);
							//FormatUtils.getInstance().logMemoryUsage(
							//		"createMessagesForClickHouseCrawl() ip=" + ip + ",queueUrl=" + queueUrl + ",BatchRequestTooLongException resent message=" + value);
						} catch (Exception e2) {
							FormatUtils.getInstance().logMemoryUsage("createMessagesForClickHouseCrawl() ip=" + ip + ",queueUrl=" + queueUrl
									+ ",BatchRequestTooLongException exception encountered while resending message=" + value);
							e.printStackTrace();
						}
					}
					totalMessagesSent = totalMessagesSent + messages.size();
					messages = new HashMap<String, String>();
					currentRetryCount = MAXIMUM_RETRY_COUNT;
					break whileLoop1;
				} catch (Exception e) {
					e.printStackTrace();
					currentRetryCount++;
					FormatUtils.getInstance().logMemoryUsage("createMessagesForClickHouseCrawl() ip=" + ip + ",queueUrl=" + queueUrl
							+ ", error for AmazonSQS, sleep and try again, currentRetryCount=" + currentRetryCount + ",current timestamp=" + new Date());
					try {
						Thread.sleep(SQS_RETRY_SLEEP_TIME);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
					continue whileLoop1;
				}
			}
		}
		//FormatUtils.getInstance().logMemoryUsage("createMessagesForClickHouseCrawl() ends. ip=" + ip + ",hostnames=" + Arrays.toString(hostnames) + ",queueUrl="
		//		+ queueUrl + ",totalMessagesSent=" + totalMessagesSent + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}
}
