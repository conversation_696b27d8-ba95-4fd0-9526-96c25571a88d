package com.actonia.put.messages;

import java.util.*;
import java.util.stream.Collectors;

import com.actonia.dao.*;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.concurrency.ThreadPoolService;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.entity.TargetUrlDailyCrawlTrackingEntity;
import com.actonia.utils.CommonUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.PutMessageUtils;
import com.actonia.utils.SQSUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.UrlCrawlParametersVO;
import com.google.gson.Gson;

/***
 * 
 * put target URLs to daily HTML crawl queue by domain.
 *
 */
@Log4j2
public class SendUrlsToDailyCrawlQueuesByDomain {

	//private Boolean isDebug = false;

	private static ThreadPoolService threadPoolService = ThreadPoolService.getInstance();

	private OwnDomainEntityDAO ownDomainEntityDAO;
	private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
	private TargetUrlDailyCrawlTrackingEntityDAO targetUrlDailyCrawlTrackingEntityDAO;
	private static final int LIMIT = 30000;

	public SendUrlsToDailyCrawlQueuesByDomain() {
		this.ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		this.ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
		this.targetUrlDailyCrawlTrackingEntityDAO = SpringBeanFactory.getBean("targetUrlDailyCrawlTrackingEntityDAO");
	}

	public static void main(String args[]) {
		FormatUtils.getInstance().logMemoryUsage("main() begins.");
		long startTimestamp = System.currentTimeMillis();
		try {
			threadPoolService.init();
			CommonUtils.initThreads(218);
			new SendUrlsToDailyCrawlQueuesByDomain().process(args);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			threadPoolService.destroy();
		}
		FormatUtils.getInstance().logMemoryUsage("main() ends. elapsed time in sec.:" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private void process(String args[]) throws Exception {
		String domainIdsString = null;
		String controllerQueueName = null;
		String processType = null;
		boolean isProcessDomainsWithTenThousandUrlsOrLess = false;
		boolean isProcessSkyscannerDomains = false;
		List<OwnDomainEntity> allOwnDomainEntityList = null;
		SQSUtils.getInstance();

		if (args != null) {

			// runtime parameter 1: for the process type of crawl results (required)
			if (args.length >= 1) {
				processType = args[0];
				if (StringUtils.isNotBlank(processType)) {
					if (!StringUtils.equalsIgnoreCase(processType, IConstants.PROCESS_TYPE_CLICKHOUSE)
							&& !StringUtils.equalsIgnoreCase(processType, IConstants.PROCESS_TYPE_TEST)
							&& !StringUtils.equalsIgnoreCase(processType, IConstants.PROCESS_TYPE_CONTROL)) {
						FormatUtils.getInstance().logMemoryUsage("process() Must specify process type in runtime parameters: 'clickhouse', 'test' or 'control'.");
						return;
					}
				} else {
					FormatUtils.getInstance().logMemoryUsage("process() Must specify process type in runtime parameters: 'clickhouse', 'test' or 'control'.");
					return;
				}
			}

			// runtime parameter 2: (required if process type not 'control'): is process average domains with 10,000 or less target URLs 
			if (args.length >= 2) {
				if (StringUtils.isNotBlank(args[1]) && StringUtils.equalsIgnoreCase(args[1], IConstants.DASH) == false) {
					isProcessDomainsWithTenThousandUrlsOrLess = BooleanUtils.toBoolean(args[1]);
				}
			}

			// runtime parameter 3: (optional): list of domain IDs)
			if (args.length >= 3) {
				if (StringUtils.isNotBlank(args[2]) && StringUtils.equalsIgnoreCase(args[2], IConstants.DASH) == false) {
					domainIdsString = args[2];
				}
			}

			// runtime parameter 4: (optional): process Skyscanner domains (true/false)
			if (args.length >= 4) {
				if (StringUtils.isNotBlank(args[3]) && StringUtils.equalsIgnoreCase(args[3], IConstants.DASH) == false) {
					isProcessSkyscannerDomains = BooleanUtils.toBoolean(args[3]);
				}
			}
		} else {
			FormatUtils.getInstance().logMemoryUsage("process() Must specify runtime parameters.");
			return;
		}
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameter:process type=" + processType);
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameter:isProcessDomainsWithTenThousandUrlsOrLess=" + isProcessDomainsWithTenThousandUrlsOrLess);
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameter:list of domain IDs=" + domainIdsString);
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameter:isProcessSkyscannerDomains=" + isProcessSkyscannerDomains);

		Properties subserverProperties = new Properties();
		try {
			subserverProperties.load(SendUrlsToDailyCrawlQueuesByDomain.class.getResourceAsStream("/subserver.properties"));
		} catch (Exception e) {
			FormatUtils.getInstance().logMemoryUsage("process() no subserver.properties file found");
			return;
		}

		Boolean isExecDomainIdsInd = null;
		Set<Integer> runtimeDomainSet = null;

		Properties domainProperties = new Properties();
		try {
			domainProperties.load(SendUrlsToDailyCrawlQueuesByDomain.class.getResourceAsStream("/domain.properties"));
		} catch (Exception e) {
			FormatUtils.getInstance().logMemoryUsage("process() no domain.properties file found");
			return;
		}

		String execDomainIds = domainProperties.getProperty("exec.domain");
		String notExecDomainIds = domainProperties.getProperty("notexec.domain");
		if (StringUtils.isNotBlank(domainIdsString)) {
			execDomainIds = domainIdsString;
			notExecDomainIds = null;
		}
		FormatUtils.getInstance().logMemoryUsage("process() execDomainIds=" + execDomainIds);
		FormatUtils.getInstance().logMemoryUsage("process() notExecDomainIds=" + notExecDomainIds);

		// when process type is 'control'...
		if (PutMessageUtils.getInstance().isProcessTypeControl(processType) == true) {
			allOwnDomainEntityList = ownDomainEntityDAO.getDomainsWithTargetUrls();
		}
		// when process type is 'clickhouse' or 'test'...
		else {
			if (isProcessDomainsWithTenThousandUrlsOrLess == true) {
				allOwnDomainEntityList = ownDomainEntityDAO.getDomainsWithUrlsNotMoreThanLimit(LIMIT);
			} else {
				allOwnDomainEntityList = ownDomainEntityDAO.getDomainsWithUrlsMoreThanLimit(LIMIT);
			}
		}
		if (allOwnDomainEntityList == null || allOwnDomainEntityList.size() == 0) {
			FormatUtils.getInstance().logMemoryUsage("process() allOwnDomainEntityList is empty.");
			return;
		}

		// process specific domains
		if (StringUtils.isNotBlank(execDomainIds)) {
			runtimeDomainSet = CommonUtils.getDomainSet(execDomainIds);
			isExecDomainIdsInd = true;
		}
		// do not process specific domains
		else if (StringUtils.isNotBlank(notExecDomainIds)) {
			runtimeDomainSet = CommonUtils.getDomainSet(notExecDomainIds);
			isExecDomainIdsInd = false;
		}
		// process all domains
		else {
			runtimeDomainSet = null;
			isExecDomainIdsInd = null;
		}

		List<OwnDomainEntity> filteredOwnDomainEntityList = PutMessageUtils.getInstance().filterDomains(allOwnDomainEntityList, isExecDomainIdsInd, runtimeDomainSet);
		if (filteredOwnDomainEntityList == null || filteredOwnDomainEntityList.size() == 0) {
			FormatUtils.getInstance().logMemoryUsage("process() filteredOwnDomainEntityList is empty.");
			return;
		}
		FormatUtils.getInstance().logMemoryUsage("process() filteredOwnDomainEntityList.size()=" + filteredOwnDomainEntityList.size());

		List<OwnDomainEntity> filteredOwnDomainEntityByMessagesInQueueList = null;

		if (PutMessageUtils.getInstance().isProcessTypeControl(processType) == false) {
			if (PutMessageUtils.getInstance().isProcessTypeTest(processType) == true) {
				filteredOwnDomainEntityByMessagesInQueueList = filteredOwnDomainEntityList;
			} else {
				filteredOwnDomainEntityByMessagesInQueueList = filterDomainsByMessagesInQueue(processType, filteredOwnDomainEntityList);
			}
			if (filteredOwnDomainEntityByMessagesInQueueList == null || filteredOwnDomainEntityByMessagesInQueueList.size() == 0) {
				FormatUtils.getInstance().logMemoryUsage("process() filteredOwnDomainEntityByMessagesInQueueList is empty.");
				return;
			}
			TargetUrlHtmlClickHouseDAO.getInstance();
			TargetUrlToCrawlClickHouseDAO.getInstance();
			try {
				RankingDetailClickHouseDAO.getInstance();
			} catch (Exception e) {
				FormatUtils.getInstance().logMemoryUsage("process() RankingDetailClickHouseDAO.getInstance() failed.");
			}
			processDomainsConcurrently(filteredOwnDomainEntityByMessagesInQueueList, processType);
		}

		if (PutMessageUtils.getInstance().isProcessTypeControl(processType) == true || PutMessageUtils.getInstance().isProcessTypeTest(processType) == true) {
			// determine controller queue name based on process type	
			if (PutMessageUtils.getInstance().isProcessTypeTest(processType) == true) {
				controllerQueueName = IConstants.QUEUE_NAME_TEST_TARGET_URL_HTML_QUEUE_NAMES;
			}
			// when process type is ClickHouse or control, language codes queue is not applicable...
			else {
				// https://www.wrike.com/open.htm?id=1014964756
				//controllerQueueName = IConstants.QUEUE_NAME_TARGET_URL_DAILY_HTML_QUEUE_NAMES;

				// https://www.wrike.com/open.htm?id=1022499784
				if (BooleanUtils.isTrue(isProcessSkyscannerDomains)) {
					controllerQueueName = IConstants.QUEUE_NAME_TARGET_URL_DAILY_HTML_SKYSCANNER_QUEUE_NAMES_FIFO;
				} else {
					controllerQueueName = IConstants.QUEUE_NAME_TARGET_URL_DAILY_HTML_QUEUE_NAMES_FIFO;
				}
			}
			sendDomainCrawlDataToHtmlControllerQueue(filteredOwnDomainEntityList, controllerQueueName, processType, isProcessSkyscannerDomains);
		}
	}

	private void processDomainsConcurrently(List<OwnDomainEntity> ownDomainEntityList, String processType) throws Exception {
		FormatUtils.getInstance()
				.logMemoryUsage("processDomainsConcurrently() begins. ownDomainEntityList.size()=" + ownDomainEntityList.size() + ",processType=" + processType);

		if (StringUtils.equalsIgnoreCase(processType, IConstants.PROCESS_TYPE_CONTROL)) {
			FormatUtils.getInstance().logMemoryUsage("processDomainsConcurrently() skip putting target URLs to queues, update controller queue only.");
			return;
		}

		int totalNumberOfDomains = ownDomainEntityList.size();
		long startTimestamp = System.currentTimeMillis();
		String ipAddress = null;
		OwnDomainEntity ownDomainEntity = null;
		SendUrlsToDailyCrawlQueuesByDomainCommand sendUrlsToDailyCrawlQueuesByDomainCommand = null;
		int numberOfDomainsProcessed = 0;
		int domainId = 0;
		String urlCrawlParameters = null;
		boolean isAutoAssociateTargetUrlsRequired = false;

		do {
			ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
			if (ipAddress == null) {
				continue;
			}
			ownDomainEntity = ownDomainEntityList.get(numberOfDomainsProcessed++);
			domainId = ownDomainEntity.getId();
			urlCrawlParameters = ownDomainEntity.getUrlCrawlParameters();
			isAutoAssociateTargetUrlsRequired = PutMessageUtils.getInstance().checkIfAutoAssociateTargetUrlsRequired(domainId, urlCrawlParameters);
			sendUrlsToDailyCrawlQueuesByDomainCommand = getSendUrlsToDailyCrawlQueuesByDomainCommand(ipAddress, ownDomainEntity, processType,
					isAutoAssociateTargetUrlsRequired);
			if (sendUrlsToDailyCrawlQueuesByDomainCommand != null) {
				try {
					FormatUtils.getInstance().logMemoryUsage("processDomainsConcurrently() ip=" + ipAddress + ",domainId=" + ownDomainEntity.getId() + ",domainName="
							+ ownDomainEntity.getDomain() + ",totalUrls=" + ownDomainEntity.getTotalUrls());
					threadPoolService.execute(sendUrlsToDailyCrawlQueuesByDomainCommand);
				} catch (Exception e) {
					e.printStackTrace();
				}
			} else {
				CacheModleFactory.getInstance().setAliveIpAddress(ipAddress);
			}
		} while (numberOfDomainsProcessed < totalNumberOfDomains);

		do {
			try {
				Thread.sleep(1000);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		} while (threadPoolService.getThreadPool().getActiveCount() > 0);
		FormatUtils.getInstance().logMemoryUsage("processDomainsConcurrently() end. elapsed time in seconds=" + ((System.currentTimeMillis() - startTimestamp) / 1000));
	}

	private SendUrlsToDailyCrawlQueuesByDomainCommand getSendUrlsToDailyCrawlQueuesByDomainCommand(String ip, OwnDomainEntity ownDomainEntity, String processType,
			boolean isAutoAssociateTargetUrlsRequired) {
		SendUrlsToDailyCrawlQueuesByDomainCommand sendUrlsToDailyCrawlQueuesByDomainCommand = new SendUrlsToDailyCrawlQueuesByDomainCommand(ip, ownDomainEntity,
				processType, isAutoAssociateTargetUrlsRequired);
		sendUrlsToDailyCrawlQueuesByDomainCommand.setStatus(true);
		return sendUrlsToDailyCrawlQueuesByDomainCommand;
	}

	// create one message in the HTML controller queue for each domain
	private void sendDomainCrawlDataToHtmlControllerQueue(List<OwnDomainEntity> domainsToBeProcessedList, String controllerQueueName, String processType,
			boolean isProcessSkyscannerDomains) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("sendDomainCrawlDataToHtmlControllerQueue() begins. controllerQueueName=" + controllerQueueName + ",processType="
				+ processType + ",isProcessSkyscannerDomains=" + isProcessSkyscannerDomains);
		long startTimestamp = System.currentTimeMillis();
		String queueUrl = null;
		String domainSpecificQueueName = null;
		String domainIdString = null;
		String languageCode = null;
		Map<String, String> messages = new HashMap<String, String>();
		int totalMessages = 0;
		int maxConcurrentCrawlThreads = 0;
		String urlCrawlParameters = null;
		UrlCrawlParametersVO[] urlCrawlParametersVoArray = null;
		Gson gson = new Gson();
		int delayInSecondsPerHttpRequest = 0;
		UrlCrawlParametersVO urlCrawlParametersVo = null;
		String messageBodyInJsonFormat = null;
		String specificUserAgent = null;
		String queueNamePrefix = null;
		Boolean enableJavascriptCrawl = null;
		Boolean enableScrapyCrawl = null;
		int domainId = 0;
		String region = null;
		Integer javascriptTimeoutInSecond = null;
		String messageId = null;
		List<Integer> skyscannerDomainIdList = null;
		List<OwnDomainEntity> testOwnDomainEntityList = null;
		boolean toBeProcessed = false;
		String domainName = null;

		// initialize the queue
		try {
			queueUrl = SQSUtils.getInstance().createFifoQueue(controllerQueueName);
			SQSUtils.getInstance().purgeQueue(queueUrl);
		} catch (Exception e) {
			e.printStackTrace();
			return;
		}

		if (PutMessageUtils.getInstance().isProcessTypeTest(processType) == true) {
			queueNamePrefix = IConstants.QUEUE_NAME_TEST_TARGET_URL_HTML_PREFIX;
		} else {
			queueNamePrefix = IConstants.QUEUE_NAME_TARGET_URL_DAILY_HTML_PREFIX;
		}

		List<OwnDomainEntity> ownDomainEntityList = new ArrayList<OwnDomainEntity>();

		if (PutMessageUtils.getInstance().isProcessTypeTest(processType) == true) {
			ownDomainEntityList = domainsToBeProcessedList;
		} else {
			// https://www.wrike.com/open.htm?id=496837510
			List<OwnDomainEntity> ownDomainEntityCrawlTrackDateList = ownDomainEntityDAO.getByCrawlTrackingDate();
			final Set<Integer> domainsToBeProcessedSet = domainsToBeProcessedList.stream()
					.map(OwnDomainEntity::getId).collect(Collectors.toSet());

			if (ownDomainEntityCrawlTrackDateList != null && !ownDomainEntityCrawlTrackDateList.isEmpty()) {
				Map<Integer, OwnDomainEntity> domainIdsWithUrlCrawlParameters = this.ownDomainEntityDAO.getDomainsWithUrlCrawlParameters()
						.stream()
						.collect(Collectors.toMap(OwnDomainEntity::getId, ownDomainEntity -> ownDomainEntity));

				ownDomainEntityList = ownDomainEntityCrawlTrackDateList.stream()
						.filter(ownDomainEntity -> domainsToBeProcessedSet.contains(ownDomainEntity.getId()))
						.peek(ownDomainEntity -> {
							OwnDomainEntity domainWithUrlCrawlParameters = domainIdsWithUrlCrawlParameters.get(ownDomainEntity.getId());
							ownDomainEntity.setDomain(domainWithUrlCrawlParameters.getDomain());
							ownDomainEntity.setLanguage(domainWithUrlCrawlParameters.getLanguage());
							ownDomainEntity.setUrlCrawlParameters(domainWithUrlCrawlParameters.getUrlCrawlParameters());
                        })
						.collect(Collectors.toList());
			} else {
				ownDomainEntityList = domainsToBeProcessedList;
			}
		}

		if (PutMessageUtils.getInstance().isProcessTypeControl(processType) == true) {
			skyscannerDomainIdList = ownDomainEntityDAO.getDomainIdsByCompanyName(IConstants.COMPANY_NAME_SKYSCANNER);
			if (skyscannerDomainIdList != null && skyscannerDomainIdList.size() > 0) {
				testOwnDomainEntityList = new ArrayList<OwnDomainEntity>();
				if (BooleanUtils.isTrue(isProcessSkyscannerDomains)) {
					for (OwnDomainEntity ownDomainEntity : ownDomainEntityList) {
						toBeProcessed = false;
						nextSkyscannerDomainId: for (Integer skyscannerDomainId : skyscannerDomainIdList) {
							if (ownDomainEntity.getId().intValue() == skyscannerDomainId.intValue()) {
								toBeProcessed = true;
								break nextSkyscannerDomainId;
							}
						}
						if (toBeProcessed == true) {
							testOwnDomainEntityList.add(ownDomainEntity);
						}
					}
				} else {
					for (OwnDomainEntity ownDomainEntity : ownDomainEntityList) {
						toBeProcessed = true;
						nextSkyscannerDomainId: for (Integer skyscannerDomainId : skyscannerDomainIdList) {
							if (ownDomainEntity.getId().intValue() == skyscannerDomainId.intValue()) {
								toBeProcessed = false;
								break nextSkyscannerDomainId;
							}
						}
						if (toBeProcessed == true) {
							testOwnDomainEntityList.add(ownDomainEntity);
						}
					}
				}
				ownDomainEntityList = testOwnDomainEntityList;
			}
		}

		for (OwnDomainEntity ownDomainEntity : ownDomainEntityList) {
			domainId = ownDomainEntity.getId();
			domainName = ownDomainEntity.getDomain();
			enableScrapyCrawl = null; // by default, do not enable Scrapy crawl
			enableJavascriptCrawl = null; // by default, do not enable Javascript crawl
			maxConcurrentCrawlThreads = 1; // by default, 1 thread per domain
			delayInSecondsPerHttpRequest = 0; // by default, 0 seconds delay between each HTTP request
			specificUserAgent = null; // by default, use standard user agent
			javascriptTimeoutInSecond = null; // by default, use 10 seconds

			// by default, use Page Crawl API standard endpoint http://***********/crawl
			// When region is 'London', use Page Crawl API London endpoint http://**************/crawl
			region = null;

			if (StringUtils.isNotBlank(ownDomainEntity.getUrlCrawlParameters())) {
				urlCrawlParameters = ownDomainEntity.getUrlCrawlParameters();
				urlCrawlParametersVoArray = gson.fromJson(urlCrawlParameters, UrlCrawlParametersVO[].class);
				for (int idx = 0; idx < urlCrawlParametersVoArray.length; idx++) {
					urlCrawlParametersVo = urlCrawlParametersVoArray[idx];
					// enable Javascript crawl
					if (urlCrawlParametersVo.getType().equalsIgnoreCase(IConstants.ENABLE_JAVASCRIPT_CRAWL)) {
						enableJavascriptCrawl = BooleanUtils.toBoolean(urlCrawlParametersVo.getData(), "true", "false");
					}
					// maximum number of concurrent threads per domain
					else if (urlCrawlParametersVo.getType().equalsIgnoreCase(IConstants.MAX_CONCURRENT_THREADS)) {
						maxConcurrentCrawlThreads = Integer.parseInt(urlCrawlParametersVo.getData());
					}
					// delay in seconds between HTTP requests
					else if (urlCrawlParametersVo.getType().equalsIgnoreCase(IConstants.DELAY_IN_SECONDS)) {
						delayInSecondsPerHttpRequest = Integer.parseInt(urlCrawlParametersVo.getData());
					}
					// user agent name for sending HTTP requests
					else if (urlCrawlParametersVo.getType().equalsIgnoreCase(IConstants.USER_AGENT)) {
						if (StringUtils.isNotBlank(urlCrawlParametersVo.getData())) {
							specificUserAgent = urlCrawlParametersVo.getData();
						}
					}
					// region
					else if (urlCrawlParametersVo.getType().equalsIgnoreCase(IConstants.REGION)) {
						region = urlCrawlParametersVo.getData();
					}
					// javascriptTimeoutInSecond
					else if (urlCrawlParametersVo.getType().equalsIgnoreCase(IConstants.JAVASCRIPT_TIMEOUT_IN_SECOND)) {
						javascriptTimeoutInSecond = Integer.parseInt(urlCrawlParametersVo.getData());
					}
				}
			}

			domainIdString = String.valueOf(domainId);
			if (StringUtils.isNotBlank(ownDomainEntity.getLanguage())) {
				languageCode = ownDomainEntity.getLanguage().toUpperCase();
				domainSpecificQueueName = languageCode.concat(IConstants.UNDERSCORE).concat(domainIdString);
			} else {
				domainSpecificQueueName = domainIdString;
			}
			domainSpecificQueueName = queueNamePrefix.concat(domainSpecificQueueName);
			if (StringUtils.isNotBlank(domainSpecificQueueName)) {
				try {
					// create message body in JSON format with the following elements: 
					// 1) domain specific queue name, 
					// 2) delay in seconds per HTTP request (optional, by default 0 second)
					// 3) maximum number of threads per queue (optional, by default 1 thread per domain)
					// 4) domain-specific user agent (optional, by default use the user-agent specified in crawler.properties)
					messageBodyInJsonFormat = PutMessageUtils.getInstance().createControllerMessageBodyInJsonFormat(domainId, domainSpecificQueueName,
							delayInSecondsPerHttpRequest, maxConcurrentCrawlThreads, specificUserAgent, enableJavascriptCrawl, enableScrapyCrawl, region,
							javascriptTimeoutInSecond);
					messageId = String.valueOf(System.nanoTime());
					messages.put(messageId, messageBodyInJsonFormat);
					FormatUtils.getInstance().logMemoryUsage("sendDomainCrawlDataToHtmlControllerQueue() domainId=" + domainIdString + "domainName=" + domainName
							+ ",totalUrls=" + ownDomainEntity.getTotalUrls() + ",messageBodyInJsonFormat=" + messageBodyInJsonFormat);
					totalMessages++;
					if (messages.size() >= 1) {
						SQSUtils.getInstance().sendBatchMessageToFifoQueue(queueUrl, messages);
						messages = new HashMap<String, String>();
					}
					Thread.sleep(100);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
		if (messages != null && messages.size() > 0) {
			SQSUtils.getInstance().sendBatchMessageToFifoQueue(queueUrl, messages);
		}
		FormatUtils.getInstance().logMemoryUsage("sendDomainCrawlDataToHtmlControllerQueue() ends. controllerQueueName=" + controllerQueueName + ",totalMessages="
				+ totalMessages + ",total elapsed time in sec.=" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	// return a list of domains to put messages to queue
	private List<OwnDomainEntity> filterDomainsByMessagesInQueue(String processType, List<OwnDomainEntity> ownDomainEntityInputList) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("filterDomainsByMessagesInQueue() begins.");

		List<OwnDomainEntity> ownDomainEntityOutputList = new ArrayList<OwnDomainEntity>();
		String htmlQueueNamePrefix = null;
		String htmlQueueName = null;
		String languageCode = null;
		Integer domainId = null;
		Integer[] numberOfMessagesArray = null;
		String domainName = null;
		int messagesInQueue = 0;
		int messagesInFlight = 0;
		String companyName = null;
		TargetUrlDailyCrawlTrackingEntity targetUrlDailyCrawlTrackingEntity = null;
		Date todayDate = null;
		String todayDateString = null;
		int todayDateNumber = 0;

		if (ownDomainEntityInputList != null && ownDomainEntityInputList.size() > 0) {

			// determine the queue name prefix
			if (PutMessageUtils.getInstance().isProcessTypeTest(processType) == true) {
				htmlQueueNamePrefix = IConstants.QUEUE_NAME_TEST_TARGET_URL_HTML_PREFIX;
			} else {
				htmlQueueNamePrefix = IConstants.QUEUE_NAME_TARGET_URL_DAILY_HTML_PREFIX;
			}

			nextOwnDomainEntity: for (OwnDomainEntity ownDomainEntity : ownDomainEntityInputList) {
				domainId = ownDomainEntity.getId();
				domainName = ownDomainEntity.getDomain();
				companyName = ownDomainSettingEntityDAO.getCompanyNameByDomainId(domainId);
				if (StringUtils.equalsIgnoreCase(companyName, IConstants.COMPANY_NAME_CARRENTALS_COM)) {
					log.warn("skip putting message for OID= {},domainName= {},companyName= {}", domainId, domainName, companyName);
					continue nextOwnDomainEntity;
				}

				// skip when domain already processed today in the 'target_url_daily_crawl_tracking' MySQL table
				targetUrlDailyCrawlTrackingEntity = targetUrlDailyCrawlTrackingEntityDAO.get(IConstants.PROCESS_TYPE_NUMBER_CLICKHOUSE, domainId);
				if (targetUrlDailyCrawlTrackingEntity != null) {
					todayDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
					todayDateString = DateFormatUtils.format(todayDate, IConstants.DATE_FORMAT_YYYYMMDD);
					todayDateNumber = NumberUtils.toInt(todayDateString);
					if (targetUrlDailyCrawlTrackingEntity.getTrackDate() == todayDateNumber) {
						FormatUtils.getInstance().logMemoryUsage("filterDomainsByMessagesInQueue() skip putting message for domainId=" + domainId + ",domainName="
								+ domainName + ",already processed today.");
						continue nextOwnDomainEntity;
					}
				}

				// determine domain's number of messages in queue and number of messages in flight
				languageCode = ownDomainEntity.getLanguage();
				htmlQueueName = htmlQueueNamePrefix + languageCode.toUpperCase() + IConstants.UNDERSCORE + ownDomainEntity.getId();
				numberOfMessagesArray = SQSUtils.getInstance().getApproximateNumberOfMessagesAndInflight(htmlQueueName);
				if (numberOfMessagesArray != null && numberOfMessagesArray.length == 2) {
					messagesInQueue = numberOfMessagesArray[0];
					messagesInFlight = numberOfMessagesArray[1];
					// when all messages in queue have been processed, proceed to put messages to queue for this domain
					if (messagesInQueue == 0 && messagesInFlight == 0) {
						ownDomainEntityOutputList.add(ownDomainEntity);
						FormatUtils.getInstance().logMemoryUsage("filterDomainsByMessagesInQueue() proceed to put messages for domainId=" + domainId + ",domainName="
								+ domainName + ",htmlQueueName=" + htmlQueueName + ",messagesInQueue=" + messagesInQueue + ",messagesInFlight=" + messagesInFlight);
					}
					// when there are still messages in queue
					else {
						FormatUtils.getInstance().logMemoryUsage("filterDomainsByMessagesInQueue() skip putting message for domainId=" + domainId + ",domainName="
								+ domainName + ",htmlQueueName=" + htmlQueueName + ",messagesInQueue=" + messagesInQueue + ",messagesInFlight=" + messagesInFlight);
					}
				} else {
					FormatUtils.getInstance().logMemoryUsage("filterDomainsByMessagesInQueue() error--numberOfMessagesArray is empty,domainId=" + domainId
							+ ",domainName=" + domainName + ",htmlQueueName=" + htmlQueueName);
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("filterDomainsByMessagesInQueue() ends.");
		return ownDomainEntityOutputList;
	}
}
