package com.actonia.put.messages;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

import com.actonia.IConstants;
import com.actonia.concurrency.BaseThreadCommand;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.dao.TargetUrlEntityDAO;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.Md5Util;
import com.actonia.utils.SpringBeanFactory;

public class FindTargetUrlsDifferByTrailingSlashCommand extends BaseThreadCommand {
	//private Boolean isDebugMode = false; //debug
	private String ip;
	private TargetUrlEntityDAO targetUrlEntityDAO;
	private int domainId;
	private String domainName;

	public FindTargetUrlsDifferByTrailingSlashCommand(String ip, OwnDomainEntity ownDomainEntity) {
		super();
		this.ip = ip;
		this.domainId = ownDomainEntity.getId();
		this.domainName = ownDomainEntity.getDomain();
		this.targetUrlEntityDAO = SpringBeanFactory.getBean("targetUrlEntityDAO");
	}

	@Override
	protected void execute() throws Exception {
		FormatUtils.getInstance().logMemoryUsage("execute() begins. ip=" + ip + ", domainId=" + domainId + " , domainName=" + domainName);

		long startTimestamp = System.currentTimeMillis();

		process();

		FormatUtils.getInstance().logMemoryUsage("execute() ends. ip=" + ip + ", domainId=" + domainId + ", domainName=" + domainName + ",elapsed(s.)="
				+ (System.currentTimeMillis() - startTimestamp) / 1000);

		CacheModleFactory.getInstance().setAliveIpAddress(ip);
	}

	private void process() throws Exception {

		// map key = MD5 hash without trailing slash
		// map value = list of target URL ID
		Map<String, List<Long>> hashCodeIdListMap = new HashMap<String, List<Long>>();

		// map key = MD5 hash without trailing slash
		// map value = list of target URL String
		Map<String, List<String>> hashCodeUrlListMap = new HashMap<String, List<String>>();

		String hashCode = null;
		String targetUrlString = null;
		String targetUrlStringWithoutTrailingSlash = null;
		List<Long> targetUrlIdList = null;
		List<String> targetUrlStringList = null;
		Long targetUrlId = null;
		int index = 0;

		// retrieve target URL ID, and URL string
		FormatUtils.getInstance().logMemoryUsage("process() ip=" + ip + ", domainId=" + domainId + " , domainName=" + domainName + ",retrieving targetUrl...");
		Map<Long, String> targetUrlIdUrlMap = targetUrlEntityDAO.getIdUrlMap(domainId);
		if (targetUrlIdUrlMap != null && targetUrlIdUrlMap.size() > 0) {
			FormatUtils.getInstance().logMemoryUsage(
					"process() ip=" + ip + ", domainId=" + domainId + " , domainName=" + domainName + ",targetUrlIdUrlMap.size()=" + targetUrlIdUrlMap.size());
			for (Long testTargetUrlId : targetUrlIdUrlMap.keySet()) {
				targetUrlString = targetUrlIdUrlMap.get(testTargetUrlId);
				targetUrlStringWithoutTrailingSlash = StringUtils.removeEndIgnoreCase(targetUrlString, IConstants.SLASH);
				hashCode = Md5Util.Md5(targetUrlStringWithoutTrailingSlash);
				if (hashCodeIdListMap.containsKey(hashCode) == false) {
					targetUrlIdList = new ArrayList<Long>();
					targetUrlStringList = new ArrayList<String>();
				} else {
					targetUrlIdList = hashCodeIdListMap.get(hashCode);
					targetUrlStringList = hashCodeUrlListMap.get(hashCode);
				}
				targetUrlIdList.add(testTargetUrlId);
				targetUrlStringList.add(targetUrlString);
				hashCodeIdListMap.put(hashCode, targetUrlIdList);
				hashCodeUrlListMap.put(hashCode, targetUrlStringList);
			}
			FormatUtils.getInstance().logMemoryUsage("process() ip=" + ip + ", domainId=" + domainId + " , domainName=" + domainName + ",hashCodeIdListMap.size()="
					+ hashCodeIdListMap.size() + ",hashCodeUrlListMap.size()=" + hashCodeUrlListMap.size());
			for (String testHashCode : hashCodeIdListMap.keySet()) {
				targetUrlIdList = hashCodeIdListMap.get(testHashCode);
				if (targetUrlIdList.size() > 1) {
					index++;
					targetUrlStringList = hashCodeUrlListMap.get(testHashCode);
					for (int i = 0; i < targetUrlIdList.size(); i++) {
						targetUrlId = targetUrlIdList.get(i);
						targetUrlString = targetUrlStringList.get(i);
						FormatUtils.getInstance().logMemoryUsage("process() ip=" + ip + "`domainId`" + domainId + "`domainName`" + domainName + "`index`" + index
								+ "`targetUrlId`" + targetUrlId + "`targetUrlString`" + targetUrlString +"`");
					}
				}
			}

		} else {
			FormatUtils.getInstance().logMemoryUsage("process() ip=" + ip + ", domainId=" + domainId + " , domainName=" + domainName + ",targetUrlIdUrlMap is empty.");
		}
	}

	@Override
	protected void undo() throws Exception {
		// TODO Auto-generated method stub

	}
}
