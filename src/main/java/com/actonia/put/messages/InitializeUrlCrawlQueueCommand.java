package com.actonia.put.messages;

import org.apache.commons.lang.StringUtils;

import com.actonia.IConstants;
import com.actonia.concurrency.BaseThreadCommand;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.utils.CrawlerUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.SQSUtils;
import com.amazonaws.services.sqs.AmazonSQS;

public class InitializeUrlCrawlQueueCommand extends BaseThreadCommand {
	private String ip;
	private String queueName;
	private Boolean isDeleteQueueOnly;
	private Boolean isCreateQueue;

	public InitializeUrlCrawlQueueCommand(String ip, String queueName, Boolean isDeleteQueueOnly, Boolean isCreateQueue) {
		super();
		this.ip = ip;
		this.queueName = queueName;
		this.isDeleteQueueOnly = isDeleteQueueOnly;
		this.isCreateQueue = isCreateQueue;
	}

	@Override
	protected void execute() throws Exception {
		FormatUtils.getInstance().logMemoryUsage("InitializeUrlCrawlQueueCommand.execute() ip=" + ip + ", queueName=" + queueName + ",isDeleteQueueOnly="
				+ isDeleteQueueOnly + ",isCreateQueue=" + isCreateQueue + " begins.");
		long startTimestamp = System.currentTimeMillis();
		String queueUrl = null;

		// initialize the queue
		try {
			queueUrl = SQSUtils.getInstance().createQueue(queueName);
			if (isCreateQueue == true) {
				SQSUtils.getInstance().purgeQueue(queueUrl);
			}
			if (isDeleteQueueOnly != null && isDeleteQueueOnly == true) {
				FormatUtils.getInstance().logMemoryUsage("InitializeUrlCrawlQueueCommand.execute() ip=" + ip + ", queue name=" + queueName + " deleted.");
				queueUrl = null;
			} else {
				if (StringUtils.isNotBlank(queueUrl)) {
					CacheModleFactory.getInstance().getUrlCrawlQueueList().add(queueUrl);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		FormatUtils.getInstance().logMemoryUsage("InitializeUrlCrawlQueueCommand.execute() ip " + ip + " ipAddress released, queueName=" + queueName + ",queueUrl="
				+ queueUrl + ",isDeleteQueueOnly=" + isDeleteQueueOnly + ", elapsed time in sec.:" + (System.currentTimeMillis() - startTimestamp) / 1000);
		CacheModleFactory.getInstance().setAliveIpAddress(ip);
	}

	@Override
	protected void undo() throws Exception {
		// TODO Auto-generated method stub

	}
}
