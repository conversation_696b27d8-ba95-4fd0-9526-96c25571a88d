package com.actonia.put.messages;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import com.google.gson.Gson;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.concurrency.ThreadPoolService;
import com.actonia.dao.CommonParamDAO;
import com.actonia.dao.ContentGuardClickHouseDAO;
import com.actonia.dao.ContentGuardCrawlTrackingDAO;
import com.actonia.dao.ContentGuardGroupDAO;
import com.actonia.dao.ContentGuardUrlDAO;
import com.actonia.dao.ContentGuardUsageDAO;
import com.actonia.dao.OwnDomainEntityDAO;
import com.actonia.entity.ContentGuardCrawlTrackingEntity;
import com.actonia.entity.ContentGuardUrlEntity;
import com.actonia.entity.ContentGuardUsageEntity;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.entity.UrlMetricsEntityV3;
import com.actonia.utils.CommonUtils;
import com.actonia.utils.CrawlerUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.PutMessageUtils;
import com.actonia.utils.SQSUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.UrlCrawlParametersVO;

/***
 * 
 * put Content Guard target URLs to hourly HTML crawl queue by domain.
 *
 */
public class SendContentGuardCrawlQueuesByDomain {

	//private Boolean isDebug = false;

	private static ThreadPoolService threadPoolService = ThreadPoolService.getInstance();

	private OwnDomainEntityDAO ownDomainEntityDAO;
	private CommonParamDAO commonParamDAO;
	private ContentGuardGroupDAO contentGuardGroupDAO;
	private ContentGuardUrlDAO contentGuardUrlDAO;
	private ContentGuardCrawlTrackingDAO contentGuardCrawlTrackingDAO;
	private static final int NUMBER_OF_DAILY_QUEUES_PER_DOMAIN = 1;
	private static final int NUMBER_OF_HOURLY_QUEUES_PER_DOMAIN = 10;
	private static final int NUMBER_OF_UPLOADED_QUEUES_PER_DOMAIN = 1;
	private int totalUrlsWithHistoricalCrawlData = 0;
	private int totalUrlsWithoutHistoricalCrawlData = 0;
	private int totalMessages = 0;
	private ContentGuardUsageDAO contentGuardUsageDAO;

	public SendContentGuardCrawlQueuesByDomain() {
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		commonParamDAO = SpringBeanFactory.getBean("commonParamDAO");
		contentGuardGroupDAO = SpringBeanFactory.getBean("contentGuardGroupDAO");
		contentGuardUrlDAO = SpringBeanFactory.getBean("contentGuardUrlDAO");
		contentGuardCrawlTrackingDAO = SpringBeanFactory.getBean("contentGuardCrawlTrackingDAO");
		contentGuardUsageDAO = SpringBeanFactory.getBean("contentGuardUsageDAO");
	}

	public static void main(String args[]) {
		FormatUtils.getInstance().logMemoryUsage("main() begins.");
		long startTimestamp = System.currentTimeMillis();
		try {
			threadPoolService.init();
			CommonUtils.initThreads(10);
			new SendContentGuardCrawlQueuesByDomain().process(args);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			threadPoolService.destroy();
		}
		FormatUtils.getInstance().logMemoryUsage("main() ends. elapsed time in sec.:" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private void process(String args[]) throws Exception {
		String domainIdsString = null;
		String processType = null;
		String testString = null;
		int crawlFrequencyType = 0;
		boolean isDomainProcessed = false;
		List<OwnDomainEntity> processedOwnDomainEntityList = new ArrayList<OwnDomainEntity>();
		SQSUtils.getInstance();

		if (args != null) {

			// runtime parameter 1 (required) for the process type of crawl results
			if (args.length >= 1) {
				processType = args[0];
				if (StringUtils.isNotBlank(processType)) {
					if (StringUtils.equalsIgnoreCase(processType, IConstants.PROCESS_TYPE_CLICKHOUSE) == false
							&& StringUtils.equalsIgnoreCase(processType, IConstants.PROCESS_TYPE_TEST) == false
							&& StringUtils.equalsIgnoreCase(processType, IConstants.PROCESS_TYPE_CONTROL) == false) {
						FormatUtils.getInstance().logMemoryUsage("process() Must specify process type in runtime parameters: 'clickhouse', 'test' or 'control'.");
						return;
					}
				} else {
					FormatUtils.getInstance().logMemoryUsage("process() Must specify process type in runtime parameters: 'clickhouse', 'test' or 'control'.");
					return;
				}
			} else {
				FormatUtils.getInstance().logMemoryUsage("process() Must specify process type in runtime parameters: 'clickhouse', 'test' or 'control'.");
				return;
			}

			// runtime parameter 2 (required) for the crawl frequency type
			if (args.length >= 2) {
				testString = args[1];
				if (StringUtils.isNotBlank(testString)) {
					crawlFrequencyType = NumberUtils.toInt(testString);
					if (crawlFrequencyType != IConstants.FREQUENCY_TYPE_DAILY && crawlFrequencyType != IConstants.FREQUENCY_TYPE_HOURLY
							&& crawlFrequencyType != IConstants.FREQUENCY_TYPE_UPLOADED) {
						FormatUtils.getInstance().logMemoryUsage("process() Must specify crawl frequency type in runtime parameters: 1=daily, 2=hourly or 3=uploaded.");
						return;
					}
				} else {
					FormatUtils.getInstance().logMemoryUsage("process() Must specify crawl frequency type in runtime parameters: 1=daily, 2=hourly or 3=uploaded.");
					return;
				}
			} else {
				FormatUtils.getInstance().logMemoryUsage("process() Must specify crawl frequency type in runtime parameters: 1=daily, 2=hourly or 3=uploaded.");
				return;
			}

			// runtime parameter (optional): list of domain IDs)
			if (args.length >= 3) {
				domainIdsString = args[2];
			}
		} else {
			FormatUtils.getInstance().logMemoryUsage("process() Must specify runtime parameters.");
			return;
		}
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameter 1:process type=" + processType);
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameter 2:crawl frequency type=" + crawlFrequencyType);
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameter 3:list of domain IDs=" + domainIdsString);

		Properties subserverProperties = new Properties();
		try {
			subserverProperties.load(SendContentGuardCrawlQueuesByDomain.class.getResourceAsStream("/subserver.properties"));
		} catch (Exception e) {
			FormatUtils.getInstance().logMemoryUsage("process() no subserver.properties file found");
			return;
		}

		Boolean isExecDomainIdsInd = null;
		Set<Integer> runtimeDomainSet = null;

		Properties domainProperties = new Properties();
		try {
			domainProperties.load(SendContentGuardCrawlQueuesByDomain.class.getResourceAsStream("/domain.properties"));
		} catch (Exception e) {
			FormatUtils.getInstance().logMemoryUsage("process() no domain.properties file found");
			return;
		}

		String execDomainIds = domainProperties.getProperty("exec.domain");
		String notExecDomainIds = domainProperties.getProperty("notexec.domain");
		if (StringUtils.isNotBlank(domainIdsString)) {
			execDomainIds = domainIdsString;
			notExecDomainIds = null;
		} else {
			// when process type is 'ClickHouse' and domainIdsString is not available, do not process domain ID 4765
			if (PutMessageUtils.getInstance().isProcessTypeClickHouse(processType) == true) {
				if (StringUtils.isNotBlank(notExecDomainIds)) {
					notExecDomainIds = notExecDomainIds + IConstants.COMMA + IConstants.DOMAIN_ID_4765;
				} else {
					notExecDomainIds = String.valueOf(IConstants.DOMAIN_ID_4765);
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("process() execDomainIds=" + execDomainIds);
		FormatUtils.getInstance().logMemoryUsage("process() notExecDomainIds=" + notExecDomainIds);

		// get domains enable "Content Guard Plus" function
		List<OwnDomainEntity> domainsFilterByFunctionEntityTitle = ownDomainEntityDAO.getDomainsFilteredByFunctionEntityTitle("Content Guard Plus", 3, "true");
		if (domainsFilterByFunctionEntityTitle == null || domainsFilterByFunctionEntityTitle.size() == 0) {
			FormatUtils.getInstance().logMemoryUsage("process() domainsFilterByFunctionEntityTitle is empty.");
			return;
		}

		// process specific domains
		if (StringUtils.isNotBlank(execDomainIds)) {
			runtimeDomainSet = CommonUtils.getDomainSet(execDomainIds);
			isExecDomainIdsInd = true;
		}
		// do not process specific domains
		else if (StringUtils.isNotBlank(notExecDomainIds)) {
			runtimeDomainSet = CommonUtils.getDomainSet(notExecDomainIds);
			isExecDomainIdsInd = false;
		}
		// process all domains
		else {
			runtimeDomainSet = null;
			isExecDomainIdsInd = null;
		}

		List<OwnDomainEntity> testFilteredOwnDomainEntityList = PutMessageUtils.getInstance().filterDomains(domainsFilterByFunctionEntityTitle, isExecDomainIdsInd,
				runtimeDomainSet);
		if (testFilteredOwnDomainEntityList == null || testFilteredOwnDomainEntityList.size() == 0) {
			FormatUtils.getInstance().logMemoryUsage("process() testFilteredOwnDomainEntityList is empty.");
			return;
		}

		List<Integer> contentGuardDomainIdList = contentGuardGroupDAO.getDomainIdsByCrawlFrequency(crawlFrequencyType);
		if (contentGuardDomainIdList == null || contentGuardDomainIdList.size() == 0) {
			FormatUtils.getInstance().logMemoryUsage("process() contentGuardDomainIdList is empty.");
			return;
		}

		List<OwnDomainEntity> filteredOwnDomainEntityList = new ArrayList<OwnDomainEntity>();
		for (Integer contentGuardDomainId : contentGuardDomainIdList) {
			for (OwnDomainEntity testOwnDomainEntity : testFilteredOwnDomainEntityList) {
				if (contentGuardDomainId.intValue() == testOwnDomainEntity.getId().intValue()) {
					filteredOwnDomainEntityList.add(testOwnDomainEntity);
				}
			}
		}
		if (filteredOwnDomainEntityList == null || filteredOwnDomainEntityList.size() == 0) {
			FormatUtils.getInstance().logMemoryUsage("process() filteredOwnDomainEntityList is empty.");
			return;
		}

		List<OwnDomainEntity> filteredOwnDomainEntityByMessagesInQueueList = filterDomainsByMessagesInQueue(processType, filteredOwnDomainEntityList,
				crawlFrequencyType);

		if (PutMessageUtils.getInstance().isProcessTypeControl(processType) == false) {
			ContentGuardClickHouseDAO.getInstance();
			for (OwnDomainEntity ownDomainEntity : filteredOwnDomainEntityByMessagesInQueueList) {
				isDomainProcessed = processOneDomain(ownDomainEntity, processType, crawlFrequencyType);
				if (isDomainProcessed == true) {
					processedOwnDomainEntityList.add(ownDomainEntity);
				}
			}
			FormatUtils.getInstance().logMemoryUsage("process() processedOwnDomainEntityList.size()=" + processedOwnDomainEntityList.size());
		}

		if (filteredOwnDomainEntityList != null && filteredOwnDomainEntityList.size() > 0) {
			sendDomainCrawlDataToHtmlControllerQueue(filteredOwnDomainEntityList, processType, crawlFrequencyType);
		}
	}

	private boolean processOneDomain(OwnDomainEntity ownDomainEntity, String processType, int crawlFrequencyType) throws Exception {

		boolean isDomainProcessed = false;
		long startTimestamp = System.currentTimeMillis();
		int domainId = ownDomainEntity.getId();
		String domainName = ownDomainEntity.getDomain();
		totalUrlsWithHistoricalCrawlData = 0;
		totalUrlsWithoutHistoricalCrawlData = 0;
		totalMessages = 0;
		int groupIdTotalUrls = 0;

		// map key = group ID
		// map value = total URLs
		Map<Long, Integer> groupIdTotalUrlsMap = new HashMap<Long, Integer>();

		FormatUtils.getInstance().logMemoryUsage("processOneDomain() begins. domainId=" + domainId + ", domainName=" + domainName + ",processType=" + processType
				+ ",crawlFrequencyType=" + crawlFrequencyType);

		// map key = target URL MD5 hash code
		// map value = target URL's daily HtmlClickHouseEntity
		Map<String, HtmlClickHouseEntity> htmlClickHouseEntityMap = new HashMap<String, HtmlClickHouseEntity>();

		ContentGuardCrawlTrackingEntity contentGuardCrawlTrackingEntityExisting = null;
		ContentGuardUsageEntity contentGuardUsageEntityExisting = null;
		ContentGuardUsageEntity contentGuardUsageEntityToBeCreated = null;
		List<ContentGuardUsageEntity> contentGuardUsageEntityToBeCreatedList = null;
		int totalContentGuardUrls = 0;
		String ipAddress = null;
		int totalContentGuardUrlSubsets = 0;
		List<String> dataQueueUrlList = null;
		int totalContentGuardUrlSubsetsProcessed = 0;
		int contentGuardUrlSubsetToProcess = 0;
		int dataQueueUrlListIndex = 0;
		String dataQueueUrl = null;
		SendContentGuardCrawlQueuesByDomainCommand sendContentGuardCrawlQueuesByDomainCommand = null;
		List<ContentGuardUrlEntity> contentGuardUrlEntityList = null;
		Date todayDate = null;
		int todayDateNumber = 0;
		int totalContentGuardUsageUpdated = 0;
		int totalContentGuardUsageCreated = 0;

		List<String> fieldNames = PutMessageUtils.getInstance().getHistoricalHtmlFieldNames();

		if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_DAILY || crawlFrequencyType == IConstants.FREQUENCY_TYPE_HOURLY) {
			contentGuardUrlEntityList = contentGuardUrlDAO.getListByCrawlFrequency(domainId, crawlFrequencyType, null);
			if (contentGuardUrlEntityList == null || contentGuardUrlEntityList.size() == 0) {
				FormatUtils.getInstance()
						.logMemoryUsage("processOneDomain() skip domainId=" + domainId + ", domainName=" + domainName + ",contentGuardUrlEntityList is empty.");
				return isDomainProcessed;
			}

			// track the total URLs to be processed by group ID
			for (ContentGuardUrlEntity contentGuardUrlEntity : contentGuardUrlEntityList) {
				if (groupIdTotalUrlsMap.containsKey(contentGuardUrlEntity.getGroupId())) {
					groupIdTotalUrls = groupIdTotalUrlsMap.get(contentGuardUrlEntity.getGroupId());
				} else {
					groupIdTotalUrls = 0;
				}
				groupIdTotalUrls++;
				groupIdTotalUrlsMap.put(contentGuardUrlEntity.getGroupId(), groupIdTotalUrls);
			}
			for (Long groupId : groupIdTotalUrlsMap.keySet()) {
				groupIdTotalUrls = groupIdTotalUrlsMap.get(groupId);
				FormatUtils.getInstance().logMemoryUsage("processOneDomain() groupId=" + groupId + ",totalUrls=" + groupIdTotalUrls);
			}
		} else if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_UPLOADED) {

			// check if uploaded URL that have yet to be crawled
			contentGuardUrlEntityList = contentGuardUrlDAO.getListByCrawlFrequency(domainId, crawlFrequencyType, IConstants.CRAWL_STATUS_NOT_CRAWLED);
			if (contentGuardUrlEntityList == null || contentGuardUrlEntityList.size() == 0) {
				FormatUtils.getInstance().logMemoryUsage(
						"processOneDomain() skip domainId=" + domainId + ", domainName=" + domainName + ",not crawled contentGuardUrlEntityList is empty.");
				return isDomainProcessed;
			}
		}

		FormatUtils.getInstance().logMemoryUsage(
				"processOneDomain() domainId=" + domainId + ", domainName=" + domainName + ",contentGuardUrlEntityList.size()=" + contentGuardUrlEntityList.size());

		// retrieve domain's latest fields data from historical 'content_guard' ClarityDB table
		htmlClickHouseEntityMap = PutMessageUtils.getInstance().getContentGuardClickHouseEntityMap(domainId, fieldNames);

		dataQueueUrlList = createDataQueues(domainId, processType, crawlFrequencyType);

		totalContentGuardUrls = contentGuardUrlEntityList.size();

		totalContentGuardUrlSubsets = calculateNumberOfContentGuardUrlSubsets(totalContentGuardUrls);

		totalContentGuardUrlSubsetsProcessed = 1;

		dataQueueUrlListIndex = -1;

		do {
			ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
			if (ipAddress == null) {
				continue;
			}
			contentGuardUrlSubsetToProcess = totalContentGuardUrlSubsetsProcessed++;

			dataQueueUrlListIndex++;
			if ((dataQueueUrlListIndex + 1) > dataQueueUrlList.size()) {
				dataQueueUrlListIndex = 0;
			}
			dataQueueUrl = dataQueueUrlList.get(dataQueueUrlListIndex);

			sendContentGuardCrawlQueuesByDomainCommand = getSendContentGuardCrawlQueuesByDomainCommand(ipAddress, ownDomainEntity, processType,
					contentGuardUrlSubsetToProcess, dataQueueUrl, contentGuardUrlEntityList, htmlClickHouseEntityMap, crawlFrequencyType);
			if (sendContentGuardCrawlQueuesByDomainCommand != null) {
				try {
					FormatUtils.getInstance().logMemoryUsage("processDomainsConcurrently() ipAddress acquired=" + ipAddress + ",domain=" + ownDomainEntity.getId()
							+ " - " + ownDomainEntity.getDomain());
					threadPoolService.execute(sendContentGuardCrawlQueuesByDomainCommand);
					isDomainProcessed = true;
				} catch (Exception e) {
					e.printStackTrace();
				}
			} else {
				CacheModleFactory.getInstance().setAliveIpAddress(ipAddress);
			}
		} while (totalContentGuardUrlSubsetsProcessed <= totalContentGuardUrlSubsets);

		do {
			try {
				Thread.sleep(1000);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		} while (threadPoolService.getThreadPool().getActiveCount() > 0);

		if (PutMessageUtils.getInstance().isProcessTypeClickHouse(processType)) {
			contentGuardCrawlTrackingEntityExisting = contentGuardCrawlTrackingDAO.get(IConstants.PROCESS_TYPE_NUMBER_CLICKHOUSE, domainId, crawlFrequencyType);
			if (contentGuardCrawlTrackingEntityExisting == null) {
				createContentGuardCrawlTracking(domainId, crawlFrequencyType, domainName, totalMessages);
			} else {
				updateContentGuardCrawlTracking(domainId, crawlFrequencyType, domainName, totalMessages, new Date());
			}

			// update 'content_guard_usage' MySQL table
			if (groupIdTotalUrlsMap != null && groupIdTotalUrlsMap.size() > 0) {
				todayDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
				todayDateNumber = NumberUtils.toInt(DateFormatUtils.format(todayDate, IConstants.DATE_FORMAT_YYYYMMDD));
				contentGuardUsageEntityToBeCreatedList = new ArrayList<ContentGuardUsageEntity>();
				for (Long groupId : groupIdTotalUrlsMap.keySet()) {
					groupIdTotalUrls = groupIdTotalUrlsMap.get(groupId);
					contentGuardUsageEntityExisting = contentGuardUsageDAO.get(domainId, todayDateNumber, groupId);
					if (contentGuardUsageEntityExisting != null) {
						groupIdTotalUrls = groupIdTotalUrls + contentGuardUsageEntityExisting.getTotalUrls();
						contentGuardUsageDAO.update(domainId, todayDateNumber, groupId, groupIdTotalUrls);
						totalContentGuardUsageUpdated++;
					} else {
						contentGuardUsageEntityToBeCreated = new ContentGuardUsageEntity();
						contentGuardUsageEntityToBeCreated.setDomainId(domainId);
						contentGuardUsageEntityToBeCreated.setUsageDate(todayDateNumber);
						contentGuardUsageEntityToBeCreated.setGroupId(groupId);
						contentGuardUsageEntityToBeCreated.setTotalUrls(groupIdTotalUrls);
						contentGuardUsageEntityToBeCreatedList.add(contentGuardUsageEntityToBeCreated);
						totalContentGuardUsageCreated++;
					}
				}
				if (contentGuardUsageEntityToBeCreatedList != null && contentGuardUsageEntityToBeCreatedList.size() > 0) {
					contentGuardUsageDAO.insertMultiRowsBatch(contentGuardUsageEntityToBeCreatedList);
					contentGuardUsageEntityToBeCreatedList = null;
				}
			}

		}

		FormatUtils.getInstance()
				.logMemoryUsage("processOneDomain() ends. domainId=" + domainId + ", domainName=" + domainName + ",processType=" + processType + ",crawlFrequencyType="
						+ crawlFrequencyType + ",totalContentGuardUrls=" + totalContentGuardUrls + ",totalUrlsWithHistoricalCrawlData="
						+ totalUrlsWithHistoricalCrawlData + ",totalUrlsWithoutHistoricalCrawlData=" + totalUrlsWithoutHistoricalCrawlData + ",total messages="
						+ totalMessages + ",totalContentGuardUsageUpdated=" + totalContentGuardUsageUpdated + ",totalContentGuardUsageCreated="
						+ totalContentGuardUsageCreated + " elapsed time in sec.:" + (System.currentTimeMillis() - startTimestamp) / 1000);
		return isDomainProcessed;

	}

	private List<String> createDataQueues(int domainId, String processType, int crawlFrequencyType) throws Exception {
		FormatUtils.getInstance()
				.logMemoryUsage("createDataQueues() begins. domainId=" + domainId + ",processType=" + processType + ",crawlFrequencyType=" + crawlFrequencyType);

		List<String> dataQueueUrlList = new ArrayList<String>();

		String dataQueueNamePrefix = null;
		int queueSuffix = 0;
		String dataQueueName = null;
		String dataQueueUrl = null;
		int totalNumberOfQueues = 0;

		// initialize the daily queue for the client domain
		if (PutMessageUtils.getInstance().isProcessTypeTest(processType) == true) {
			if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_DAILY) {
				dataQueueNamePrefix = IConstants.QUEUE_NAME_TEST_CONTENT_GUARD_DAILY_PREFIX;
			} else if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_HOURLY) {
				dataQueueNamePrefix = IConstants.QUEUE_NAME_TEST_CONTENT_GUARD_HOURLY_PREFIX;
			} else if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_UPLOADED) {
				dataQueueNamePrefix = IConstants.QUEUE_NAME_TEST_CONTENT_GUARD_UPLOADED_PREFIX;
			}
		} else {
			if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_DAILY) {
				dataQueueNamePrefix = IConstants.QUEUE_NAME_CONTENT_GUARD_DAILY_PREFIX;
			} else if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_HOURLY) {
				dataQueueNamePrefix = IConstants.QUEUE_NAME_CONTENT_GUARD_HOURLY_PREFIX;
			} else if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_UPLOADED) {
				dataQueueNamePrefix = IConstants.QUEUE_NAME_CONTENT_GUARD_UPLOADED_PREFIX;
			}
		}

		totalNumberOfQueues = 0;
		if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_DAILY) {
			totalNumberOfQueues = NUMBER_OF_DAILY_QUEUES_PER_DOMAIN;
		} else if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_HOURLY) {
			totalNumberOfQueues = NUMBER_OF_HOURLY_QUEUES_PER_DOMAIN;
		} else if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_UPLOADED) {
			totalNumberOfQueues = NUMBER_OF_UPLOADED_QUEUES_PER_DOMAIN;
		}

		for (int i = 0; i < totalNumberOfQueues; i++) {
			queueSuffix = i + 1;
			dataQueueName = dataQueueNamePrefix + domainId + IConstants.UNDERSCORE + queueSuffix;
			dataQueueUrl = SQSUtils.getInstance().createQueue(dataQueueName);
			dataQueueUrlList.add(dataQueueUrl);
		}

		FormatUtils.getInstance()
				.logMemoryUsage("createDataQueues() ends. domainId=" + domainId + ",processType=" + processType + ",dataQueueUrlList=" + dataQueueUrlList.toString());

		return dataQueueUrlList;
	}

	private int calculateNumberOfContentGuardUrlSubsets(int totalContentGuardUrls) {
		FormatUtils.getInstance().logMemoryUsage("calculateNumberOfContentGuardUrlSubsets() begins. totalContentGuardUrls=" + totalContentGuardUrls);
		int modulus = totalContentGuardUrls % 10;
		int numberOfSubsets = totalContentGuardUrls / 10;
		if (modulus > 0) {
			numberOfSubsets = numberOfSubsets + 1;
		}
		FormatUtils.getInstance().logMemoryUsage("calculateNumberOfContentGuardUrlSubsets() ends. numberOfSubsets=" + numberOfSubsets);
		return numberOfSubsets;
	}

	private SendContentGuardCrawlQueuesByDomainCommand getSendContentGuardCrawlQueuesByDomainCommand(String ip, OwnDomainEntity ownDomainEntity, String processType,
			int contentGuardUrlSubset, String dataQueueUrl, List<ContentGuardUrlEntity> contentGuardUrlEntityList,
			Map<String, HtmlClickHouseEntity> htmlClickHouseEntityMap, int crawlFrequencyType) throws Exception {
		SendContentGuardCrawlQueuesByDomainCommand sendContentGuardCrawlQueuesByDomainCommand = null;
		int domainId = ownDomainEntity.getId();
		String domainName = ownDomainEntity.getDomain();
		List<UrlMetricsEntityV3> urlMetricsEntityV3List = getContentGuardUrlSubsetData(domainId, domainName, contentGuardUrlSubset, contentGuardUrlEntityList,
				htmlClickHouseEntityMap, crawlFrequencyType);
		if (urlMetricsEntityV3List != null && urlMetricsEntityV3List.size() > 0) {
			totalMessages = totalMessages + urlMetricsEntityV3List.size();
			sendContentGuardCrawlQueuesByDomainCommand = new SendContentGuardCrawlQueuesByDomainCommand(ip, ownDomainEntity, processType, dataQueueUrl,
					urlMetricsEntityV3List);
			sendContentGuardCrawlQueuesByDomainCommand.setStatus(true);
		}
		return sendContentGuardCrawlQueuesByDomainCommand;
	}

	private List<UrlMetricsEntityV3> getContentGuardUrlSubsetData(int domainId, String domainName, int contentGuardUrlSubset,
			List<ContentGuardUrlEntity> contentGuardUrlEntityList, Map<String, HtmlClickHouseEntity> htmlClickHouseEntityMap, int crawlFrequencyType) throws Exception {

		FormatUtils.getInstance().logMemoryUsage(
				"getContentGuardUrlSubsetData() begins. domainId=" + domainId + ",domainName=" + domainName + ",contentGuardUrlSubset=" + contentGuardUrlSubset);

		List<UrlMetricsEntityV3> urlMetricsEntityV3List = new ArrayList<UrlMetricsEntityV3>();

		UrlMetricsEntityV3 urlMetricsEntityV3 = null;
		boolean isHistoricalDataAvailable = false;
		String contentGuardUrlString = null;
		ContentGuardUrlEntity contentGuardUrlEntity = null;
		String s3FileName = null;
		String hashCode = null;
		int totalUrlsUpdated = 0;

		int contentGuardUrlSubsetStartingIndex = calculateContentGuardUrlSubsetStartingIndex(contentGuardUrlSubset);
		int contentGuardUrlSubsetEndingIndex = contentGuardUrlSubsetStartingIndex + 10;
		nextContentGuardUrlSubsetStartingIndex: for (int i = contentGuardUrlSubsetStartingIndex; i < contentGuardUrlSubsetEndingIndex; i++) {

			if ((i + 1) > contentGuardUrlEntityList.size()) {
				continue nextContentGuardUrlSubsetStartingIndex;
			}

			contentGuardUrlEntity = contentGuardUrlEntityList.get(i);

			contentGuardUrlString = contentGuardUrlEntity.getUrl();

			urlMetricsEntityV3 = new UrlMetricsEntityV3();
			urlMetricsEntityV3.setUrl(contentGuardUrlString);
			urlMetricsEntityV3.setContent_guard_crawl_flag(true);
			urlMetricsEntityV3.setCrawl_frequency_type(crawlFrequencyType);
			if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_UPLOADED) {
				urlMetricsEntityV3
						.setCrawl_timestamp(DateFormatUtils.format(contentGuardUrlEntity.getLastUpdateTimestamp(), IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS));
				s3FileName = contentGuardUrlEntity.getS3FileName();
				if (StringUtils.isNotBlank(s3FileName)) {
					urlMetricsEntityV3.setS3_file_name(s3FileName);
				}
				// error when S3 file name is not availble and crawl frequency is uploaded
				else {
					throw new Exception("getContentGuardUrlSubsetData() error--S3 file name not available, domainId=" + domainId + ",domainName=" + domainName
							+ ",contentGuardUrlSubset=" + contentGuardUrlSubset + ",contentGuardUrlEntity=" + contentGuardUrlEntity.toString());
				}
			}

			// update 'urlMetricsEntityV3' (pass by reference) with historical crawled data
			isHistoricalDataAvailable = PutMessageUtils.getInstance().updateUrlMetricsEntityWithHistoricalCrawledData(IConstants.EMPTY_STRING,
					IConstants.CRAWL_TYPE_CONTENT_GUARD, urlMetricsEntityV3, htmlClickHouseEntityMap);
			if (isHistoricalDataAvailable == true) {
				totalUrlsWithHistoricalCrawlData++;
			} else {
				totalUrlsWithoutHistoricalCrawlData++;
			}
			urlMetricsEntityV3List.add(urlMetricsEntityV3);

			if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_UPLOADED) {
				hashCode = CrawlerUtils.getInstance().getMd5HashCode(StringUtils.trim(contentGuardUrlString));
				totalUrlsUpdated = contentGuardUrlDAO.updateCrawlStatus(domainId, hashCode, IConstants.CRAWL_STATUS_TO_BE_CRAWLED);
				if (totalUrlsUpdated == 0) {
					FormatUtils.getInstance().logMemoryUsage("getContentGuardUrlSubsetData() error--content_guard_url not updated. domainId=" + domainId
							+ ",domainName=" + domainName + ",contentGuardUrlString=" + contentGuardUrlString);
				}
			}
		}
		FormatUtils.getInstance()
				.logMemoryUsage("getContentGuardUrlSubsetData() ends.  domainId=" + domainId + ",domainName=" + domainName + ",contentGuardUrlSubset="
						+ contentGuardUrlSubset + ",contentGuardUrlSubsetStartingIndex=" + contentGuardUrlSubsetStartingIndex + ",contentGuardUrlSubsetEndingIndex="
						+ contentGuardUrlSubsetEndingIndex + ",urlMetricsEntityV3List.size()=" + urlMetricsEntityV3List.size());
		return urlMetricsEntityV3List;
	}

	private int calculateContentGuardUrlSubsetStartingIndex(int contentGuardUrlSubset) {
		int contentSubsetUrlStartingIndex = (contentGuardUrlSubset * 10) - 10;
		FormatUtils.getInstance().logMemoryUsage("calculateContentGuardUrlSubsetStartingIndex() contentGuardUrlSubset=" + contentGuardUrlSubset
				+ ",contentSubsetUrlStartingIndex=" + contentSubsetUrlStartingIndex);
		return contentSubsetUrlStartingIndex;
	}

	// create one message in the HTML controller queue for each domain
	private void sendDomainCrawlDataToHtmlControllerQueue(List<OwnDomainEntity> ownDomainEntityList, String processType, int crawlFrequencyType) throws Exception {

		long startTimestamp = System.currentTimeMillis();

		int domainId = 0;
		String domainName = null;
		String controllerQueueName = null;

		// determine controller queue name based on process type	
		if (PutMessageUtils.getInstance().isProcessTypeTest(processType) == true) {
			if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_DAILY) {
				controllerQueueName = IConstants.QUEUE_NAME_TEST_CONTENT_GUARD_DAILY_QUEUE_NAMES;
			} else if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_HOURLY) {
				controllerQueueName = IConstants.QUEUE_NAME_TEST_CONTENT_GUARD_HOURLY_QUEUE_NAMES;
			} else if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_UPLOADED) {
				controllerQueueName = IConstants.QUEUE_NAME_TEST_CONTENT_GUARD_UPLOADED_QUEUE_NAMES;
			}
		} else {
			if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_DAILY) {
				controllerQueueName = IConstants.QUEUE_NAME_CONTENT_GUARD_DAILY_QUEUE_NAMES;
			} else if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_HOURLY) {
				controllerQueueName = IConstants.QUEUE_NAME_CONTENT_GUARD_HOURLY_QUEUE_NAMES;
			} else if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_UPLOADED) {
				controllerQueueName = IConstants.QUEUE_NAME_CONTENT_GUARD_UPLOADED_QUEUE_NAMES;
			}
		}

		FormatUtils.getInstance()
				.logMemoryUsage("sendDomainCrawlDataToHtmlControllerQueue() begins. processType=" + processType + ",controllerQueueName=" + controllerQueueName);

		String controlQueueUrl;
		String domainSpecificQueueName = null;
		String domainIdString = null;
		Map<String, String> messages = new HashMap<String, String>();
		int totalMessages = 0;
		int maxConcurrentCrawlThreads = 0;
		String urlCrawlParameters = null;
		UrlCrawlParametersVO[] urlCrawlParametersVoArray = null;
		Gson gson = new Gson();
		int delayInSecondsPerHttpRequest = 0;
		UrlCrawlParametersVO urlCrawlParametersVo = null;
		String messageBodyInJsonFormat = null;
		String specificUserAgent = null;
		String queueNamePrefix = null;
		Boolean enableJavascriptCrawl = null;
		Boolean enableScrapyCrawl = null;
		int queueSuffix = 0;
		String dataQueueName = null;
		int totalNumberOfQueues = 0;
		String region = null;
		Integer javascriptTimeoutInSecond = null;

		// map key = domain ID
		// map value = queue name
		Map<String, String> domainIdQueueNameMap = new HashMap<String, String>();

		// initialize the queue
		try {
			controlQueueUrl = SQSUtils.getInstance().createQueue(controllerQueueName);
			SQSUtils.getInstance().purgeQueue(controlQueueUrl);
		} catch (Exception e) {
			e.printStackTrace();
			return;
		}

		if (PutMessageUtils.getInstance().isProcessTypeTest(processType) == true) {
			if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_DAILY) {
				queueNamePrefix = IConstants.QUEUE_NAME_TEST_CONTENT_GUARD_DAILY_PREFIX;
			} else if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_HOURLY) {
				queueNamePrefix = IConstants.QUEUE_NAME_TEST_CONTENT_GUARD_HOURLY_PREFIX;
			} else if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_UPLOADED) {
				queueNamePrefix = IConstants.QUEUE_NAME_TEST_CONTENT_GUARD_UPLOADED_PREFIX;
			}
		} else {
			if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_DAILY) {
				queueNamePrefix = IConstants.QUEUE_NAME_CONTENT_GUARD_DAILY_PREFIX;
			} else if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_HOURLY) {
				queueNamePrefix = IConstants.QUEUE_NAME_CONTENT_GUARD_HOURLY_PREFIX;
			} else if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_UPLOADED) {
				queueNamePrefix = IConstants.QUEUE_NAME_CONTENT_GUARD_UPLOADED_PREFIX;
			}
		}

		for (OwnDomainEntity ownDomainEntity : ownDomainEntityList) {
			domainId = ownDomainEntity.getId();
			domainName = ownDomainEntity.getDomain();
			enableScrapyCrawl = null; // by default, do not enable Scrapy crawl
			enableJavascriptCrawl = null; // by default, do not enable Javascript crawl
			if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_DAILY) {
				maxConcurrentCrawlThreads = 1; // by default, 1 thread per daily queue
			} else if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_HOURLY) {
				maxConcurrentCrawlThreads = 3; // by default, 3 threads per daily queue					
			} else if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_UPLOADED) {
				maxConcurrentCrawlThreads = 1; // by default, 1 threads per uploaded queue					
			}
			delayInSecondsPerHttpRequest = 0; // by default, 0 seconds delay between each HTTP request
			specificUserAgent = null; // by default, use standard user agent

			// by default, use Page Crawl API standard endpoint http://***********/crawl
			// When region is 'London', use Page Crawl API London endpoint http://**************/crawl
			region = null;
			javascriptTimeoutInSecond = null;

			if (StringUtils.isNotBlank(ownDomainEntity.getUrlCrawlParameters())) {
				urlCrawlParameters = ownDomainEntity.getUrlCrawlParameters();
				urlCrawlParametersVoArray = gson.fromJson(urlCrawlParameters, UrlCrawlParametersVO[].class);
				for (int idx = 0; idx < urlCrawlParametersVoArray.length; idx++) {
					urlCrawlParametersVo = urlCrawlParametersVoArray[idx];
					// enable Scrapy crawl
					if (urlCrawlParametersVo.getType().equalsIgnoreCase(IConstants.ENABLE_SCRAPY_CRAWL)) {
						enableScrapyCrawl = BooleanUtils.toBoolean(urlCrawlParametersVo.getData(), "true", "false");
					}
					// enable Javascript crawl
					else if (urlCrawlParametersVo.getType().equalsIgnoreCase(IConstants.ENABLE_JAVASCRIPT_CRAWL)) {
						enableJavascriptCrawl = BooleanUtils.toBoolean(urlCrawlParametersVo.getData(), "true", "false");
					}
					// maximum number of concurrent threads per domain
					else if (urlCrawlParametersVo.getType().equalsIgnoreCase(IConstants.MAX_CONCURRENT_THREADS)) {

						maxConcurrentCrawlThreads = Integer.parseInt(urlCrawlParametersVo.getData());

						if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_HOURLY) {
							maxConcurrentCrawlThreads = maxConcurrentCrawlThreads * 3;
						}
					}
					// delay in seconds between HTTP requests
					else if (urlCrawlParametersVo.getType().equalsIgnoreCase(IConstants.DELAY_IN_SECONDS)) {
						delayInSecondsPerHttpRequest = Integer.parseInt(urlCrawlParametersVo.getData());
					}
					// user agent name for sending HTTP requests
					else if (urlCrawlParametersVo.getType().equalsIgnoreCase(IConstants.USER_AGENT)) {
						if (StringUtils.isNotBlank(urlCrawlParametersVo.getData())) {
							specificUserAgent = urlCrawlParametersVo.getData();
						}
					}
					// region
					else if (urlCrawlParametersVo.getType().equalsIgnoreCase(IConstants.REGION)) {
						region = urlCrawlParametersVo.getData();
					}
					// javascriptTimeoutInSecond
					else if (urlCrawlParametersVo.getType().equalsIgnoreCase(IConstants.JAVASCRIPT_TIMEOUT_IN_SECOND)) {
						javascriptTimeoutInSecond = Integer.parseInt(urlCrawlParametersVo.getData());
					}
				}
			}

			domainIdString = String.valueOf(domainId);
			domainSpecificQueueName = domainIdString;
			domainSpecificQueueName = queueNamePrefix.concat(domainSpecificQueueName);
			if (StringUtils.isNotBlank(domainSpecificQueueName)) {
				// purge queues when debug mode
				//if (isDebug == true) {
				//	controlQueueUrl = SQSUtils.getInstance().createQueue(domainSpecificQueueName, getAmazonSQS());
				//	System.out.println("sendDomainCrawlDataToHtmlControllerQueue() purging domainSpecificQueueName=" + domainSpecificQueueName + ",controlQueueUrl=" + controlQueueUrl);
				//	SQSUtils.getInstance().purgeQueue(getAmazonSQS(), controlQueueUrl);
				//	continue nextOwnDomainEntity;
				//}
				try {
					// create message body in JSON format with the following elements: 
					// 1) domain specific queue name, 
					// 2) delay in seconds per HTTP request (optional, by default 0 second)
					// 3) maximum number of threads per queue (optional, by default 1 thread per domain)
					// 4) domain-specific user agent (optional, by default use the user-agent specified in crawler.properties)

					totalNumberOfQueues = 0;
					if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_DAILY) {
						totalNumberOfQueues = NUMBER_OF_DAILY_QUEUES_PER_DOMAIN;
					} else if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_HOURLY) {
						totalNumberOfQueues = NUMBER_OF_HOURLY_QUEUES_PER_DOMAIN;
					} else if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_UPLOADED) {
						totalNumberOfQueues = NUMBER_OF_UPLOADED_QUEUES_PER_DOMAIN;
					}

					for (int i = 0; i < totalNumberOfQueues; i++) {
						queueSuffix = i + 1;
						dataQueueName = domainSpecificQueueName + IConstants.UNDERSCORE + queueSuffix;
						messageBodyInJsonFormat = PutMessageUtils.getInstance().createControllerMessageBodyInJsonFormat(domainId, dataQueueName,
								delayInSecondsPerHttpRequest, maxConcurrentCrawlThreads, specificUserAgent, enableJavascriptCrawl, enableScrapyCrawl, region,
								javascriptTimeoutInSecond);
						messages.put(domainIdString + IConstants.UNDERSCORE + queueSuffix, messageBodyInJsonFormat);
						domainIdQueueNameMap.put(domainIdString + IConstants.UNDERSCORE + queueSuffix, messageBodyInJsonFormat);
						totalMessages++;
						if (messages.size() >= 10) {
							SQSUtils.getInstance().sendBatchMessageToQueue(controlQueueUrl, messages);
							messages = new HashMap<String, String>();
						}
					}
				} catch (Exception e) {
					e.printStackTrace();
				}
			}

		}
		if (messages != null && messages.size() > 0) {
			SQSUtils.getInstance().sendBatchMessageToQueue(controlQueueUrl, messages);
		}

		for (String tempDomainId : domainIdQueueNameMap.keySet()) {
			FormatUtils.getInstance().logMemoryUsage(
					"sendDomainCrawlDataToHtmlControllerQueue() domainId=" + tempDomainId + ",messageBodyInJsonFormat=" + domainIdQueueNameMap.get(tempDomainId));
		}
		FormatUtils.getInstance()
				.logMemoryUsage("sendDomainCrawlDataToHtmlControllerQueue() ends. domainId=" + domainId + ",domainName=" + domainName + ",processType=" + processType
						+ ",controllerQueueName=" + controllerQueueName + ",totalMessages=" + totalMessages + ",total elapsed time in sec.="
						+ (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	// return a list of domains to put messages to queue
	private List<OwnDomainEntity> filterDomainsByMessagesInQueue(String processType, List<OwnDomainEntity> ownDomainEntityInputList, int crawlFrequencyType)
			throws Exception {
		FormatUtils.getInstance().logMemoryUsage("filterDomainsByMessagesInQueue() begins.");

		List<OwnDomainEntity> ownDomainEntityOutputList = new ArrayList<OwnDomainEntity>();
		String dataQueueNamePrefix = null;
		String dataQueueName = null;
		Integer domainId = null;
		Integer[] numberOfMessagesArray = null;
		String domainName = null;
		int messagesInQueue = 0;
		int messagesInFlight = 0;
		ContentGuardCrawlTrackingEntity contentGuardCrawlTrackingEntityExisting = null;
		long differenceInMinutes = 0L;
		int queueSuffix = 0;
		boolean isProcessDomain = false;
		String todayDateString = null;
		int todayDateNumber = 0;
		Date todayDate = null;
		String crawlDateString = null;
		int crawlDateNumber = 0;
		int totalNumberOfQueues = 0;

		if (ownDomainEntityInputList != null && ownDomainEntityInputList.size() > 0) {

			// determine the queue name prefix
			if (PutMessageUtils.getInstance().isProcessTypeTest(processType) == true) {
				if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_DAILY) {
					dataQueueNamePrefix = IConstants.QUEUE_NAME_TEST_CONTENT_GUARD_DAILY_PREFIX;
				} else if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_HOURLY) {
					dataQueueNamePrefix = IConstants.QUEUE_NAME_TEST_CONTENT_GUARD_HOURLY_PREFIX;
				} else if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_UPLOADED) {
					dataQueueNamePrefix = IConstants.QUEUE_NAME_TEST_CONTENT_GUARD_UPLOADED_PREFIX;
				}
			} else {
				if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_DAILY) {
					dataQueueNamePrefix = IConstants.QUEUE_NAME_CONTENT_GUARD_DAILY_PREFIX;
				} else if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_HOURLY) {
					dataQueueNamePrefix = IConstants.QUEUE_NAME_CONTENT_GUARD_HOURLY_PREFIX;
				} else if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_UPLOADED) {
					dataQueueNamePrefix = IConstants.QUEUE_NAME_CONTENT_GUARD_UPLOADED_PREFIX;
				}
			}

			nextOwnDomainEntity: for (OwnDomainEntity ownDomainEntity : ownDomainEntityInputList) {
				domainId = ownDomainEntity.getId();
				domainName = ownDomainEntity.getDomain();

				// check 'content_guard_crawl_tracking' record to see if messages were put in queue for the domain on the same day
				if (PutMessageUtils.getInstance().isProcessTypeClickHouse(processType) || PutMessageUtils.getInstance().isProcessTypeControl(processType)) {
					contentGuardCrawlTrackingEntityExisting = contentGuardCrawlTrackingDAO.get(IConstants.PROCESS_TYPE_NUMBER_CLICKHOUSE, domainId, crawlFrequencyType);
				}
				if (contentGuardCrawlTrackingEntityExisting != null) {
					FormatUtils.getInstance()
							.logMemoryUsage("filterDomainsByMessagesInQueue() domainId=" + domainId + ", domainName=" + domainName + ",crawlFrequencyType="
									+ crawlFrequencyType + ",contentGuardCrawlTrackingEntityExisting=" + contentGuardCrawlTrackingEntityExisting.toString());
					// when crawling daily frequency
					if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_DAILY) {
						todayDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
						todayDateString = DateFormatUtils.format(todayDate, IConstants.DATE_FORMAT_YYYYMMDD);
						todayDateNumber = NumberUtils.toInt(todayDateString);
						crawlDateString = DateFormatUtils.format(contentGuardCrawlTrackingEntityExisting.getCrawlTimestamp(), IConstants.DATE_FORMAT_YYYYMMDD);
						crawlDateNumber = NumberUtils.toInt(crawlDateString);
						if (crawlDateNumber == todayDateNumber) {
							FormatUtils.getInstance().logMemoryUsage("filterDomainsByMessagesInQueue() domainId=" + domainId + ", domainName=" + domainName
									+ ",crawlFrequencyType=" + crawlFrequencyType + ",skipped already processed today.");
							continue nextOwnDomainEntity;
						}
					}
					// when crawling hourly frequency
					else if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_HOURLY) {
						differenceInMinutes = CrawlerUtils.getInstance().calculateDifferenceInMinutes(contentGuardCrawlTrackingEntityExisting.getCrawlTimestamp(),
								new Date());
						if (differenceInMinutes < 60) {
							FormatUtils.getInstance()
									.logMemoryUsage("filterDomainsByMessagesInQueue() domainId=" + domainId + ", domainName=" + domainName + ",crawlFrequencyType="
											+ crawlFrequencyType + ",skipped--already processed at " + contentGuardCrawlTrackingEntityExisting.getCrawlTimestamp());
							continue nextOwnDomainEntity;
						} else {
							FormatUtils.getInstance()
									.logMemoryUsage("filterDomainsByMessagesInQueue() domainId=" + domainId + ", domainName=" + domainName + ",crawlFrequencyType="
											+ crawlFrequencyType + ",proceed--last processed at " + contentGuardCrawlTrackingEntityExisting.getCrawlTimestamp());
						}
					}
				} else {
					FormatUtils.getInstance().logMemoryUsage("filterDomainsByMessagesInQueue() domainId=" + domainId + ", domainName=" + domainName
							+ ",crawlFrequencyType=" + crawlFrequencyType + ",contentGuardCrawlTrackingEntity not available, proceed to put messages to queue.");
				}

				isProcessDomain = true;

				totalNumberOfQueues = 0;
				if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_DAILY) {
					totalNumberOfQueues = NUMBER_OF_DAILY_QUEUES_PER_DOMAIN;
				} else if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_HOURLY) {
					totalNumberOfQueues = NUMBER_OF_HOURLY_QUEUES_PER_DOMAIN;
				} else if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_UPLOADED) {
					totalNumberOfQueues = NUMBER_OF_UPLOADED_QUEUES_PER_DOMAIN;
				}

				for (int i = 0; i < totalNumberOfQueues; i++) {
					queueSuffix = i + 1;
					dataQueueName = dataQueueNamePrefix + domainId + IConstants.UNDERSCORE + queueSuffix;
					numberOfMessagesArray = SQSUtils.getInstance().getApproximateNumberOfMessagesAndInflight(dataQueueName);
					if (numberOfMessagesArray != null && numberOfMessagesArray.length == 2) {
						messagesInQueue = numberOfMessagesArray[0];
						messagesInFlight = numberOfMessagesArray[1];
						// when all messages in queue have been processed, proceed to put messages to queue for this domain
						if (messagesInQueue == 0 && messagesInFlight == 0) {
						}
						// when there are still messages in queue
						else {
							FormatUtils.getInstance()
									.logMemoryUsage("filterDomainsByMessagesInQueue() skip putting message for domainId=" + domainId + ",domainName=" + domainName
											+ ",crawlFrequencyType=" + crawlFrequencyType + ",dataQueueName=" + dataQueueName + ",messagesInQueue=" + messagesInQueue
											+ ",messagesInFlight=" + messagesInFlight);
							isProcessDomain = false;
							//if (isDebug == true) {
							//	SQSUtils.getInstance().purgeQueue(dataQueueName);
							//	FormatUtils.getInstance().logMemoryUsage("filterDomainsByMessagesInQueue() purged queue=" + dataQueueName);
							//}
						}
					} else {
						FormatUtils.getInstance().logMemoryUsage("filterDomainsByMessagesInQueue() error--numberOfMessagesArray is empty,domainId=" + domainId
								+ ",domainName=" + domainName + ",crawlFrequencyType=" + crawlFrequencyType + ",dataQueueName=" + dataQueueName);
						isProcessDomain = false;
					}
				}
				if (isProcessDomain == true) {
					ownDomainEntityOutputList.add(ownDomainEntity);
					FormatUtils.getInstance()
							.logMemoryUsage("filterDomainsByMessagesInQueue() proceed to put messages for domainId=" + domainId + ",domainName=" + domainName
									+ ",crawlFrequencyType=" + crawlFrequencyType + ",dataQueueName=" + dataQueueName + ",messagesInQueue=" + messagesInQueue
									+ ",messagesInFlight=" + messagesInFlight);
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("filterDomainsByMessagesInQueue() ends.");
		//if (isDebug == true) {
		//	for (OwnDomainEntity ownDomainEntityTest : ownDomainEntityOutputList) {
		//		FormatUtils.getInstance().logMemoryUsage("filterDomainsByMessagesInQueue() to be processed...domainId=" + ownDomainEntityTest.getId() + ",domainName="
		//				+ ownDomainEntityTest.getDomain());
		//	}
		//	ownDomainEntityOutputList = null;
		//}
		return ownDomainEntityOutputList;
	}

	private void createContentGuardCrawlTracking(int domainId, int crawlFrequencyType, String domainName, int totalMessages) {
		// create 'content_guard_crawl_tracking' record for the domain processed.
		List<ContentGuardCrawlTrackingEntity> contentGuardCrawlTrackingEntityList = new ArrayList<ContentGuardCrawlTrackingEntity>();
		ContentGuardCrawlTrackingEntity contentGuardCrawlTrackingEntity = new ContentGuardCrawlTrackingEntity();
		contentGuardCrawlTrackingEntity.setProcessType(IConstants.PROCESS_TYPE_NUMBER_CLICKHOUSE);
		contentGuardCrawlTrackingEntity.setDomainId(domainId);
		contentGuardCrawlTrackingEntity.setCrawlFrequencyType(crawlFrequencyType);
		contentGuardCrawlTrackingEntity.setDomainName(domainName);
		contentGuardCrawlTrackingEntity.setCrawlTimestamp(new Date());
		contentGuardCrawlTrackingEntity.setTotalUrls(totalMessages);
		contentGuardCrawlTrackingEntityList.add(contentGuardCrawlTrackingEntity);
		contentGuardCrawlTrackingDAO.insertMultiRowsBatch(contentGuardCrawlTrackingEntityList);
		FormatUtils.getInstance().logMemoryUsage("createContentGuardCrawlTracking() domainId=" + domainId + ", domainName=" + domainName
				+ ",created contentGuardCrawlTrackingEntity=" + contentGuardCrawlTrackingEntity.toString());

	}

	private void updateContentGuardCrawlTracking(int domainId, int crawlFrequencyType, String domainName, int totalMessages, Date crawlTimestamp) {
		// update 'content_guard_crawl_tracking' record for the domain processed.
		contentGuardCrawlTrackingDAO.update(IConstants.PROCESS_TYPE_NUMBER_CLICKHOUSE, domainId, crawlFrequencyType, crawlTimestamp, totalMessages);
		FormatUtils.getInstance().logMemoryUsage("updateContentGuardCrawlTracking() domainId=" + domainId + ", domainName=" + domainName + ",crawlFrequencyType="
				+ crawlFrequencyType + ",crawlTimestamp=" + crawlTimestamp + ",totalMessages=" + totalMessages);

	}
}
