package com.actonia.put.messages;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.actonia.concurrency.BaseThreadCommand;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.entity.UrlMetricsEntityV3;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.SQSUtils;
import com.amazonaws.services.sqs.model.BatchRequestTooLongException;
import com.google.gson.Gson;

public class SendContentGuardCrawlQueuesByDomainCommand extends BaseThreadCommand {
	//private Boolean isDebug = false;
	private String ip;
	private static Integer MESSAGE_BATCH_SIZE = 10;
	private static final int MAXIMUM_RETRY_COUNT = 5;

	// when SQS returns error, sleep for one minute
	private static final int SQS_RETRY_SLEEP_TIME = 60000;

	//private ManagedTargeturlDAOInfoB managedTargeturlDAOInfoB;

	private int totalMessagesSent = 0;

	private int modulus = 0;

	private int domainId;

	private String domainName;

	private String dataQueueUrl;

	private List<UrlMetricsEntityV3> urlMetricsEntityV3List;

	public SendContentGuardCrawlQueuesByDomainCommand(String ip, OwnDomainEntity ownDomainEntity, String processType, String dataQueueUrl,
			List<UrlMetricsEntityV3> urlMetricsEntityV3List) {
		super();
		this.ip = ip;
		this.domainId = ownDomainEntity.getId();
		this.domainName = ownDomainEntity.getDomain();
		this.dataQueueUrl = dataQueueUrl;
		this.urlMetricsEntityV3List = urlMetricsEntityV3List;
	}

	@Override
	protected void execute() {
		try {
			FormatUtils.getInstance().logMemoryUsage("execute() begins. ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",dataQueueUrl="
					+ dataQueueUrl + ",urlMetricsEntityV3List.size()=" + urlMetricsEntityV3List.size());
			long startTimestamp = System.currentTimeMillis();
			createMessagesForCrawl();
			FormatUtils.getInstance()
					.logMemoryUsage("execute() ends. ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",dataQueueUrl=" + dataQueueUrl
							+ ",urlMetricsEntityV3List.size()=" + urlMetricsEntityV3List.size() + " elapsed time in sec.:"
							+ (System.currentTimeMillis() - startTimestamp) / 1000);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			CacheModleFactory.getInstance().setAliveIpAddress(ip);
		}
	}

	private void createMessagesForCrawl() throws Exception {
		String messageBody = null;
		int totalMessages = 0;
		int currentRetryCount = 0;

		// put all target URL strings in queue
		Map<String, String> messages = new HashMap<String, String>();

		Map<String, String> testMessages = new HashMap<String, String>();
		String value = null;

		for (UrlMetricsEntityV3 urlMetricsEntityV3 : urlMetricsEntityV3List) {
			messageBody = new Gson().toJson(urlMetricsEntityV3);
			try {
				messages.put(String.valueOf(System.nanoTime()), messageBody);
				totalMessages = totalMessages + 1;
				if (messages.size() == MESSAGE_BATCH_SIZE) {

					// put messages to queue
					currentRetryCount = 0;
					whileLoop1: while (currentRetryCount < MAXIMUM_RETRY_COUNT) {
						try {
							SQSUtils.getInstance().sendBatchMessageToQueueThrowsException(dataQueueUrl, messages);
							totalMessagesSent = totalMessagesSent + messages.size();
							modulus = totalMessagesSent % 1000;
							if (modulus == 0) {
								FormatUtils.getInstance().logMemoryUsage(
										"createMessagesForCrawl() ip=" + ip + ",dataQueueUrl=" + dataQueueUrl + ",totalMessagesSent=" + totalMessagesSent);
							}
							messages = new HashMap<String, String>();
							currentRetryCount = MAXIMUM_RETRY_COUNT;
							break whileLoop1;
						} catch (BatchRequestTooLongException e) {
							for (String key : messages.keySet()) {
								testMessages = new HashMap<String, String>();
								value = messages.get(key);
								testMessages.put(key, value);
								//if (isDebug == true) {
								FormatUtils.getInstance().logMemoryUsage("createMessagesForCrawl() ip=" + ip + ",dataQueueUrl=" + dataQueueUrl
										+ ",BatchRequestTooLongException re-sending message=" + value);
								//}
								SQSUtils.getInstance().sendBatchMessageToQueueThrowsException(dataQueueUrl, testMessages);
								//if (isDebug == true) {
								FormatUtils.getInstance().logMemoryUsage("createMessagesForCrawl() ip=" + ip + ",dataQueueUrl=" + dataQueueUrl
										+ ",BatchRequestTooLongException resent message=" + value);
								//}
							}
							totalMessagesSent = totalMessagesSent + messages.size();
							messages = new HashMap<String, String>();
							currentRetryCount = MAXIMUM_RETRY_COUNT;
							break whileLoop1;
						} catch (Exception e) {
							e.printStackTrace();
							currentRetryCount++;
							FormatUtils.getInstance()
									.logMemoryUsage("createMessagesForCrawl() ip=" + ip + ", domainId=" + domainId + ", domainName=" + domainName + ",dataQueueUrl="
											+ dataQueueUrl + ", error for AmazonSQS, sleep and try again, currentRetryCount=" + currentRetryCount
											+ ",current timestamp=" + new Date());
							Thread.sleep(SQS_RETRY_SLEEP_TIME);
							continue whileLoop1;
						}
					}

				}
				Thread.sleep(100);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		if (messages != null && messages.size() > 0) {

			// put messages to queue
			currentRetryCount = 0;
			whileLoop1: while (currentRetryCount < MAXIMUM_RETRY_COUNT) {
				try {
					SQSUtils.getInstance().sendBatchMessageToQueueThrowsException(dataQueueUrl, messages);
					totalMessagesSent = totalMessagesSent + messages.size();
					modulus = totalMessagesSent % 1000;
					if (modulus == 0) {
						FormatUtils.getInstance()
								.logMemoryUsage("createMessagesForCrawl() ip=" + ip + ",dataQueueUrl=" + dataQueueUrl + ",totalMessagesSent=" + totalMessagesSent);
					}
					currentRetryCount = MAXIMUM_RETRY_COUNT;
					break whileLoop1;
				} catch (BatchRequestTooLongException e) {
					for (String key : messages.keySet()) {
						testMessages = new HashMap<String, String>();
						value = messages.get(key);
						testMessages.put(key, value);
						//if (isDebug == true) {
						FormatUtils.getInstance().logMemoryUsage(
								"createMessagesForCrawl() ip=" + ip + ",dataQueueUrl=" + dataQueueUrl + ",BatchRequestTooLongException re-sending message=" + value);
						//}
						SQSUtils.getInstance().sendBatchMessageToQueueThrowsException(dataQueueUrl, testMessages);
						//if (isDebug == true) {
						FormatUtils.getInstance().logMemoryUsage(
								"createMessagesForCrawl() ip=" + ip + ",dataQueueUrl=" + dataQueueUrl + ",BatchRequestTooLongException resent message=" + value);
						//}
					}
					totalMessagesSent = totalMessagesSent + messages.size();
					messages = new HashMap<String, String>();
					currentRetryCount = MAXIMUM_RETRY_COUNT;
					break whileLoop1;
				} catch (Exception e) {
					e.printStackTrace();
					currentRetryCount++;
					FormatUtils.getInstance()
							.logMemoryUsage("createMessagesForCrawl() ip=" + ip + ", domainId=" + domainId + ", domainName=" + domainName + ",dataQueueUrl="
									+ dataQueueUrl + ", error for AmazonSQS, sleep and try again, currentRetryCount=" + currentRetryCount + ",current timestamp="
									+ new Date());
					Thread.sleep(SQS_RETRY_SLEEP_TIME);
					continue whileLoop1;
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("createMessagesForCrawl() ends. ip=" + ip + ", domainId=" + domainId + ", domainName=" + domainName + ",dataQueueUrl="
				+ dataQueueUrl + ",total messages=" + totalMessages);
	}

	@Override
	protected void undo() throws Exception {
		// TODO Auto-generated method stub

	}
}
