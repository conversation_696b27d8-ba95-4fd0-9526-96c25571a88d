package com.actonia.put.messages;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.actonia.dao.*;
import com.actonia.entity.*;
import com.amazonaws.services.sqs.model.BatchRequestTooLongException;
import com.google.gson.Gson;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.concurrency.BaseThreadCommand;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.service.ScKeywordRankService;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.Md5Util;
import com.actonia.utils.PutMessageUtils;
import com.actonia.utils.SQSUtils;
import com.actonia.utils.SpringBeanFactory;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class SendUrlsToDailyCrawlQueuesByDomainCommand extends BaseThreadCommand {
	private static final Logger log = LogManager.getLogger(SendUrlsToDailyCrawlQueuesByDomainCommand.class);
	//private Boolean isDebug = false;
	private String ip;
	private TargetUrlEntityDAO targetUrlEntityDAO;
	private final PoliteCrawlDomainSettingDAO politeCrawlDomainSettingDAO;
	private String languageCode;
	private static Integer MESSAGE_BATCH_SIZE = 10;
	private static final String DUPLICATED_WWW_DELTAFAUCET_COM = "www.deltafaucet.comwww.deltafaucet.com";
	private static final String DOUBLE_HTTP = "http://http://";
	private static final int MAXIMUM_RETRY_COUNT = 5;

	// when SQS returns error, sleep for one minute
	private static final int SQS_RETRY_SLEEP_TIME = 60000;

	//private ManagedTargeturlDAOInfoB managedTargeturlDAOInfoB;

	private int totalMessagesSent = 0;

	private int modulus = 0;

	private String processType;

	private TargetUrlDailyCrawlTrackingEntityDAO targetUrlDailyCrawlTrackingEntityDAO;

	private int domainId;

	private String domainName;

	private int totalTargetUrls;

	private int totalUrlsWithHistoricalCrawlData;

	private int totalUrlsWithoutHistoricalCrawlData;

	private static final String DOMAIN_8371_TEST_URL = "https://www.yummly.com/recipes/quick-dinners";
	private static final String DOMAIN_7006_TEST_URL = "https://www.truecar.com/bmw/x5-m/reviews/?page=2";
	private static final String DOMAIN_4739_TEST_URL = "https://www.expedia.co.uk/Compare-Cheap-Car-Hire-In-Solo.d500660.Car-Hire-Guide";
	private static final String DOMAIN_8503_TEST_URL = "https://www.realsimple.com/beauty-fashion/skincare/best-face-scrubs";
	private static final String DOMAIN_9662_TEST_URL = "https://www.bestbuy.com/site/questions/apple-iphone-8-plus-64gb-silver-sprint/6009772/question/b330a617-6ec2-3506-83bc-b55dbcdbd7ce";
	private static final String DOMAIN_4609_TEST_URL = "https://www.carfax.com/cars-for-sale";
	private static final String DOMAIN_256_TEST_URL = "https://www.seoclarity.net/see-it-in-action/";
	private static final String DOMAIN_9632_TEST_URL = "https://test.edgeseo.dev/ai_rule_51.html";

	private boolean isAutoAssociateTargetUrlsRequired = false;
	private OwnDomainEntity ownDomainEntity;

	public SendUrlsToDailyCrawlQueuesByDomainCommand(String ip, OwnDomainEntity ownDomainEntity, String processType, boolean isAutoAssociateTargetUrlsRequired) {
		super();
		this.ip = ip;
		this.domainId = ownDomainEntity.getId();
		this.domainName = ownDomainEntity.getDomain();
		this.languageCode = ownDomainEntity.getLanguage();
		this.targetUrlEntityDAO = SpringBeanFactory.getBean("targetUrlEntityDAO");
		this.processType = processType;
		this.targetUrlDailyCrawlTrackingEntityDAO = SpringBeanFactory.getBean("targetUrlDailyCrawlTrackingEntityDAO");
		this.isAutoAssociateTargetUrlsRequired = isAutoAssociateTargetUrlsRequired;
		this.ownDomainEntity = ownDomainEntity;
        politeCrawlDomainSettingDAO = SpringBeanFactory.getBean("politeCrawlDomainSettingDAO");
    }

	@Override
	protected void undo() throws Exception {
		// TODO Auto-generated method stub
	}

	@Override
	protected void execute() {
		try {
			FormatUtils.getInstance().logMemoryUsage("execute() begins. ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",languageCode="
					+ languageCode + ",isAutoAssociateTargetUrlsRequired=" + isAutoAssociateTargetUrlsRequired);
			long startTimestamp = System.currentTimeMillis();
			createMessagesForCrawl();
			FormatUtils.getInstance()
					.logMemoryUsage("execute() ends. ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",totalTargetUrls=" + totalTargetUrls
							+ ",totalUrlsWithHistoricalCrawlData=" + totalUrlsWithHistoricalCrawlData + ",totalUrlsWithoutHistoricalCrawlData="
							+ totalUrlsWithoutHistoricalCrawlData + " elapsed time in sec.:" + (System.currentTimeMillis() - startTimestamp) / 1000);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			CacheModleFactory.getInstance().setAliveIpAddress(ip);
		}
	}

	private void createMessagesForCrawl() throws Exception {
		String htmlQueueUrl = null;
		String targetUrlIdString = null;
		String targetUrlString = null;
		UrlMetricsEntityV3 urlMetricsEntityV3 = null;
		UrlMetricsEntityV3 testUrlMetricsEntityV3 = null;
		String testUrlString = null;
		String messageBody = null;
		int totalMessages = 0;
		List<TargetUrlEntity> targetUrlEntityList = null;
		List<TargetUrlEntity> rankedTargetUrlEntityList = null;
		int currentRetryCount = 0;
		//List<AssociatedTargetUrlEntity> associatedTargetUrlEntityList = null;
		String htmlQueueName = null;
		String htmlQueueNamePrefix = null;
		String todayDateString = null;
		int todayDateNumber = 0;
		Date todayDate = null;
		boolean isHistoricalDataAvailable = false;
		boolean isProcess = false;

		// put all target URL strings in queue
		Map<String, String> messages = new HashMap<String, String>();

		Map<String, String> testMessages = new HashMap<String, String>();
		String value = null;

		// map key = URL id
		// map value = URL string
		Map<Long, String> targetUrlIdStringMap = new HashMap<Long, String>();

		// map key = target URL MD5 hash code
		// map value = target URL's daily HtmlClickHouseEntity
		Map<String, HtmlClickHouseEntity> htmlClickHouseEntityMap = new HashMap<String, HtmlClickHouseEntity>();

		TargetUrlDailyCrawlTrackingEntity targetUrlDailyCrawlTrackingEntityExisting = null;

		String md5HashCode = null;

		List<TargetUrlEntity> testTargetUrlEntityList = null;
		if (PutMessageUtils.getInstance().isProcessTypeTest(processType) == true) {
			if (domainId == 475) {
				int domainId = 475;
				int groupTagId = 1412118;
				testTargetUrlEntityList = targetUrlEntityDAO.getGroupTagUrlList(domainId, groupTagId);
				if (testTargetUrlEntityList != null && testTargetUrlEntityList.size() > 0) {
					for (TargetUrlEntity testTargetUrlEntity : testTargetUrlEntityList) {
						System.out.println("testTargetUrlEntity.getUrl()=" + testTargetUrlEntity.getUrl());
					}
				} else {
					System.out.println("testTargetUrlEntityList is empty.");
				}
			}
		}

		List<String> targetUrlHtmlFieldNames = PutMessageUtils.getInstance().getHistoricalHtmlFieldNames();

		// check 'target_url_daily_crawl_tracking' record to see if messages were put in queue for the domain on the same day
		if (PutMessageUtils.getInstance().isProcessTypeClickHouse(processType)) {
			targetUrlDailyCrawlTrackingEntityExisting = targetUrlDailyCrawlTrackingEntityDAO.get(IConstants.PROCESS_TYPE_NUMBER_CLICKHOUSE, domainId);
		}
		todayDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
		todayDateString = DateFormatUtils.format(todayDate, IConstants.DATE_FORMAT_YYYYMMDD);
		todayDateNumber = NumberUtils.toInt(todayDateString);
		if (targetUrlDailyCrawlTrackingEntityExisting != null) {
			FormatUtils.getInstance().logMemoryUsage("createMessagesForCrawl() ip=" + ip + ", domainId=" + domainId + ", domainName=" + domainName + ",todayDateNumber="
					+ todayDateNumber + ",targetUrlDailyCrawlTrackingEntityExisting=" + targetUrlDailyCrawlTrackingEntityExisting.toString());
			if (targetUrlDailyCrawlTrackingEntityExisting.getTrackDate() == todayDateNumber) {
				FormatUtils.getInstance().logMemoryUsage(
						"createMessagesForCrawl() ip=" + ip + ", domainId=" + domainId + ", domainName=" + domainName + ",skipped already processed today.");
				return;
			}
		} else {
			FormatUtils.getInstance().logMemoryUsage("createMessagesForCrawl() ip=" + ip + ", domainId=" + domainId + ", domainName=" + domainName
					+ ",targetUrlDailyCrawlTrackingEntity not available, proceed to put messages to queue.");
		}

		targetUrlEntityList = targetUrlEntityDAO.getTargetUrlList(domainId);

        Set<TargetUrlEntity> targetUrlEntitySet = new HashSet<>(targetUrlEntityList);
		// https://www.wrike.com/open.htm?id=526231984
		try {
			// when isAutoAssociateTargetUrlsRequired == true, auto-association will create ranking URLs in 't_target_url' MySQL table with type = 1 and status = 1
			// when isAutoAssociateTargetUrlsRequired == false, retrieve list of ranked target URLs that are not in 't_target_url' MySQL table with type = 1 and status = 1
			// fetch domain setting from 'polite_crawl_domain_setting' table, only retrieve top X ranked target URLs
			final PoliteCrawlDomainSetting politeCrawlDomainSetting = this.politeCrawlDomainSettingDAO.findByOwnDomainId(domainId);
			final int enableRankingUrl = politeCrawlDomainSetting.getEnableRankingUrl();
			final int topxRankingUrl = politeCrawlDomainSetting.getTopxRankingUrl();
			if (enableRankingUrl == 1 && topxRankingUrl > 0) {
				// only get top X ranked target URLs
				rankedTargetUrlEntityList = getRankedTargetUrlEntityList(targetUrlEntityList, topxRankingUrl);
				if (rankedTargetUrlEntityList != null && !rankedTargetUrlEntityList.isEmpty()) {
					// filter out ranked target URLs that are disabled in t_target_url #https://www.wrike.com/open.htm?id=1332460525
					final List<TargetUrlEntity> filterDisabledRankedTargetUrlList = filterDisabledRankedTargetUrlList(rankedTargetUrlEntityList);
					targetUrlEntitySet.addAll(filterDisabledRankedTargetUrlList);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		final int urlSize = targetUrlEntitySet.size();
		if (urlSize > 0) {
			// insert all url into target_url_to_crawl table
			Date finalTodayDate = todayDate;
			final List<TargetUrlToCrawl> targetUrlToCrawlList = targetUrlEntitySet.parallelStream().map(targetUrlEntity -> {
				final TargetUrlToCrawl targetUrlToCrawl = new TargetUrlToCrawl();
				targetUrlToCrawl.setTrackDate(finalTodayDate);
				targetUrlToCrawl.setDomainId(domainId);
				targetUrlToCrawl.setUrl(targetUrlEntity.getUrl());
				return targetUrlToCrawl;
			}).collect(Collectors.toList());
			TargetUrlToCrawlClickHouseDAO.getInstance().createBatch(targetUrlToCrawlList);
			Thread.sleep(1000);
			while (true) {
				final long size = TargetUrlToCrawlClickHouseDAO.getInstance().countByDomainId(domainId);
                if (urlSize <= size) {
	                break;
                } else {
                    log.warn("OID: {}, batch insert targetUrlEntityList size {} is less than targetUrlToCrawlList size {}, sleep for 2 seconds to wait for clickhouse insert", domainId, urlSize, size);
                    Thread.sleep(2000);
                }
			}
            // retrieve domain's latest fields data from historical data table 'target_url_html'
			htmlClickHouseEntityMap = PutMessageUtils.getInstance().getTargetUrlHtmlClickHouseEntityMap(ip, domainId, domainName, targetUrlHtmlFieldNames);
			log.info("OID: {}, htmlClickHouseEntityMap size: {}", domainId, htmlClickHouseEntityMap.size());

			// when client domains' number of targer URLs exceeded daily processing threshold, process client domains only on Monday, Wednesday and Friday
			totalTargetUrls = targetUrlEntityList.size();
			FormatUtils.getInstance().logMemoryUsage(
					"createMessagesForCrawl() begins. ip=" + ip + ", domainId=" + domainId + ", domainName=" + domainName + ",totalTargetUrls=" + totalTargetUrls);

			// initialize the daily queue for the client domain
			if (PutMessageUtils.getInstance().isProcessTypeTest(processType) == true) {
				htmlQueueNamePrefix = IConstants.QUEUE_NAME_TEST_TARGET_URL_HTML_PREFIX;
			} else {
				htmlQueueNamePrefix = IConstants.QUEUE_NAME_TARGET_URL_DAILY_HTML_PREFIX;
			}
			htmlQueueName = htmlQueueNamePrefix + languageCode.toUpperCase() + IConstants.UNDERSCORE + domainId;
			htmlQueueUrl = SQSUtils.getInstance().createQueue(htmlQueueName);
			nextTargetUrl: for (TargetUrlEntity targetUrlEntity : targetUrlEntitySet) {

				targetUrlString = targetUrlEntity.getUrl();
				md5HashCode = Md5Util.Md5(StringUtils.trim(targetUrlString));

				if (PutMessageUtils.getInstance().isProcessTypeTest(processType) == true) {
					if (domainId == 475) {
						isProcess = false;
						if (testTargetUrlEntityList != null && testTargetUrlEntityList.size() > 0) {
							nextTestUrlId: for (TargetUrlEntity testTargetUrlEntity : testTargetUrlEntityList) {
								if (StringUtils.equalsIgnoreCase(md5HashCode, Md5Util.Md5(StringUtils.trim((testTargetUrlEntity.getUrl())))) == true) {
									isProcess = true;
									break nextTestUrlId;
								}
							}
						}
						if (isProcess == false) {
							continue nextTargetUrl;
						} else {
							System.out.println("process targetUrlString=" + targetUrlString);
						}
					} else if (domainId == 8371) {
						if (StringUtils.equalsIgnoreCase(targetUrlString, DOMAIN_8371_TEST_URL) == false) {
							continue nextTargetUrl;
						}
					} else if (domainId == 7006) {
						if (StringUtils.equalsIgnoreCase(targetUrlString, DOMAIN_7006_TEST_URL) == false) {
							continue nextTargetUrl;
						}
					} else if (domainId == 4739) {
						if (StringUtils.equalsIgnoreCase(targetUrlString, DOMAIN_4739_TEST_URL) == false) {
							continue nextTargetUrl;
						}
					} else if (domainId == 8503) {
						if (StringUtils.equalsIgnoreCase(targetUrlString, DOMAIN_8503_TEST_URL) == false) {
							continue nextTargetUrl;
						}
					} else if (domainId == 9662) {
						if (StringUtils.equalsIgnoreCase(targetUrlString, DOMAIN_9662_TEST_URL) == false) {
							continue nextTargetUrl;
						}
					} else if (domainId == 4609) {
						if (StringUtils.equalsIgnoreCase(targetUrlString, DOMAIN_4609_TEST_URL) == false) {
							continue nextTargetUrl;
						}
					} else if (domainId == 256) {
						if (StringUtils.equalsIgnoreCase(targetUrlString, DOMAIN_256_TEST_URL) == false) {
							continue nextTargetUrl;
						}
					} else if (domainId == 9632) {
						if (StringUtils.equalsIgnoreCase(targetUrlString, DOMAIN_9632_TEST_URL) == true) {
							continue nextTargetUrl;
						}
					}
				}

				// skip when target URL string contains 'www.deltafaucet.comwww.deltafaucet.com'....
				if (StringUtils.contains(targetUrlString, DUPLICATED_WWW_DELTAFAUCET_COM)) {
					continue;
				}
				// skip when target URL string contains 'http://http://'....
				if (StringUtils.contains(targetUrlString, DOUBLE_HTTP)) {
					continue;
				}

				urlMetricsEntityV3 = new UrlMetricsEntityV3();
				urlMetricsEntityV3.setUrl(targetUrlString);
				urlMetricsEntityV3.setLanguageCode(languageCode);
				urlMetricsEntityV3.setUrlType(targetUrlEntity.getUrlType() == null ? 1 : targetUrlEntity.getUrlType());
				urlMetricsEntityV3.setSourceType(targetUrlEntity.getSourceType());
				urlMetricsEntityV3.setInitialCrawlOnly(targetUrlEntity.getInitialCrawlOnly());

				// update 'urlMetricsEntityV3' with 'target_url_html_daily' data (pass by reference)
				isHistoricalDataAvailable = PutMessageUtils.getInstance().updateUrlMetricsEntityWithHistoricalCrawledData(ip, IConstants.CRAWL_TYPE_TARGET_URL_HTML,
						urlMetricsEntityV3, htmlClickHouseEntityMap);
				if (isHistoricalDataAvailable == true) {
					totalUrlsWithHistoricalCrawlData++;
				} else {
					totalUrlsWithoutHistoricalCrawlData++;
				}

				messageBody = new Gson().toJson(urlMetricsEntityV3);
				try {
					messages.put(String.valueOf(System.nanoTime()), messageBody);
					totalMessages = totalMessages + 1;
					if (messages.size() == MESSAGE_BATCH_SIZE) {

						// put messages to queue
						currentRetryCount = 0;
                        while (currentRetryCount < MAXIMUM_RETRY_COUNT) {
                            try {
                                SQSUtils.getInstance().sendBatchMessageToQueueThrowsException(htmlQueueUrl, messages);
                                Thread.sleep(100);
                                totalMessagesSent = totalMessagesSent + messages.size();
                                modulus = totalMessagesSent % 1000;
                                if (modulus == 0) {
                                    FormatUtils.getInstance()
                                            .logMemoryUsage("createMessagesForCrawl() ip=" + ip + ",queueUrl=" + htmlQueueUrl + ",totalMessagesSent=" + totalMessagesSent);
                                }
                                messages.clear();
                                break;
                            } catch (BatchRequestTooLongException e) {
                                for (String key : messages.keySet()) {
                                    testMessages = new HashMap<String, String>();
                                    value = messages.get(key);
                                    testMessages.put(key, value);
                                    testUrlString = null;
                                    try {
                                        testUrlMetricsEntityV3 = new Gson().fromJson(value, UrlMetricsEntityV3.class);
                                        testUrlString = testUrlMetricsEntityV3.getUrl();
                                    } catch (Exception e2) {
                                        e2.printStackTrace();
                                    }
                                    FormatUtils.getInstance().logMemoryUsage("createMessagesForCrawl() ip=" + ip + ",queueUrl=" + htmlQueueUrl
                                            + ",BatchRequestTooLongException re-sending message for URL=" + testUrlString);
                                    SQSUtils.getInstance().sendBatchMessageToQueueThrowsException(htmlQueueUrl, testMessages);
                                    FormatUtils.getInstance().logMemoryUsage("createMessagesForCrawl() ip=" + ip + ",queueUrl=" + htmlQueueUrl
                                            + ",BatchRequestTooLongException message resent for URL=" + testUrlString);
                                }
                                totalMessagesSent = totalMessagesSent + messages.size();
                                messages.clear();
                                break;
                            } catch (Exception e) {
                                e.printStackTrace();
                                currentRetryCount++;
                                FormatUtils.getInstance()
                                        .logMemoryUsage("createMessagesForCrawl() ip=" + ip + ", domainId=" + domainId + ", domainName=" + domainName
                                                + ",htmlQueueName=" + htmlQueueName + ", error for AmazonSQS, sleep and try again, currentRetryCount="
                                                + currentRetryCount + ",current timestamp=" + new Date());
                                Thread.sleep(SQS_RETRY_SLEEP_TIME);
                            }
                        }

					}
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
			if (!messages.isEmpty()) {

				// put messages to queue
				currentRetryCount = 0;
				whileLoop1: while (currentRetryCount < MAXIMUM_RETRY_COUNT) {
					try {
						SQSUtils.getInstance().sendBatchMessageToQueueThrowsException(htmlQueueUrl, messages);
						totalMessagesSent = totalMessagesSent + messages.size();
						modulus = totalMessagesSent % 1000;
						if (modulus == 0) {
							FormatUtils.getInstance()
									.logMemoryUsage("createMessagesForCrawl() ip=" + ip + ",queueUrl=" + htmlQueueUrl + ",totalMessagesSent=" + totalMessagesSent);
						}
						break;
					} catch (BatchRequestTooLongException e) {
						for (String key : messages.keySet()) {
							testMessages = new HashMap<String, String>();
							value = messages.get(key);
							testMessages.put(key, value);
							testUrlString = null;
							try {
								testUrlMetricsEntityV3 = new Gson().fromJson(value, UrlMetricsEntityV3.class);
								testUrlString = testUrlMetricsEntityV3.getUrl();
							} catch (Exception e2) {
								e2.printStackTrace();
							}
							FormatUtils.getInstance().logMemoryUsage("createMessagesForCrawl() ip=" + ip + ",queueUrl=" + htmlQueueUrl
									+ ",BatchRequestTooLongException re-sending message for URL=" + testUrlString);
							SQSUtils.getInstance().sendBatchMessageToQueueThrowsException(htmlQueueUrl, testMessages);
							FormatUtils.getInstance().logMemoryUsage("createMessagesForCrawl() ip=" + ip + ",queueUrl=" + htmlQueueUrl
									+ ",BatchRequestTooLongException resent message for URL=" + testUrlString);
						}
						totalMessagesSent = totalMessagesSent + messages.size();
						break;
					} catch (Exception e) {
						e.printStackTrace();
						currentRetryCount++;
						FormatUtils.getInstance()
								.logMemoryUsage("createMessagesForCrawl() ip=" + ip + ", domainId=" + domainId + ", domainName=" + domainName + ",htmlQueueName="
										+ htmlQueueName + ", error for AmazonSQS, sleep and try again, currentRetryCount=" + currentRetryCount + ",current timestamp="
										+ new Date());
						Thread.sleep(SQS_RETRY_SLEEP_TIME);
                    }
				}
			}
		} else {
			FormatUtils.getInstance().logMemoryUsage("createMessagesForCrawl() ip=" + ip + ", domainId=" + domainId + ", domainName=" + domainName + ",htmlQueueName="
					+ htmlQueueName + ",total number of target URLs=0.");
		}
		FormatUtils.getInstance().logMemoryUsage("createMessagesForCrawl() ends. ip=" + ip + ", domainId=" + domainId + ", domainName=" + domainName + ",htmlQueueName="
				+ htmlQueueName + ",total messages=" + totalMessages);

		if (PutMessageUtils.getInstance().isProcessTypeClickHouse(processType)) {
			if (targetUrlDailyCrawlTrackingEntityExisting == null) {
				createTargetUrlDailyCrawlTracking(totalMessages);
			} else {
				updateTargetUrlDailyCrawlTracking(totalMessages);
			}
		}
	}

	private List<TargetUrlEntity> filterDisabledRankedTargetUrlList(List<TargetUrlEntity> rankedTargetUrlEntityList) {
		// 1. get already disabled URLs in mysql table 't_target_url'.
		final HashSet<String> disableCrawlUrlList = this.targetUrlEntityDAO.getDisableCrawlUrlList(this.domainId);
		final int disableCrawlUrlSize = disableCrawlUrlList.size();
		if (disableCrawlUrlSize == 0) {
			// no disabled URL in mysql table 't_target_url'. no need to filter urls in 'rankedTargetUrlEntityList' just return ranked list.
			return rankedTargetUrlEntityList;
		}
		// 2. filter out already disabled URLs.
		final int rankedSize = rankedTargetUrlEntityList.size();
		final List<TargetUrlEntity> filteredRankedTargetUrlEntityList = rankedTargetUrlEntityList.stream().filter(targetUrlEntity -> !disableCrawlUrlList.contains(targetUrlEntity.getUrl())).collect(Collectors.toList());
		final int filteredSize = filteredRankedTargetUrlEntityList.size();
		FormatUtils.getInstance().logMemoryUsage("filterDisabledRankedTargetUrlList() domainId=" + domainId + ", rankedUrlSize=" + rankedSize + ", disableCrawlUrlSize=" + disableCrawlUrlSize + ", after disable crawl filtered return filteredSize=" + filteredSize);
		return filteredRankedTargetUrlEntityList;
	}

	private void createTargetUrlDailyCrawlTracking(int totalMessages) {
		// create 'target_url_daily_crawl_tracking' record for the domain processed.
		List<TargetUrlDailyCrawlTrackingEntity> targetUrlDailyCrawlTrackingEntityList = new ArrayList<TargetUrlDailyCrawlTrackingEntity>();
		TargetUrlDailyCrawlTrackingEntity targetUrlDailyCrawlTrackingEntity = new TargetUrlDailyCrawlTrackingEntity();
		targetUrlDailyCrawlTrackingEntity.setProcessType(IConstants.PROCESS_TYPE_NUMBER_CLICKHOUSE);
		targetUrlDailyCrawlTrackingEntity.setDomainId(domainId);
		targetUrlDailyCrawlTrackingEntity.setDomainName(domainName);
		Date todayDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
		String todayDateString = DateFormatUtils.format(todayDate, IConstants.DATE_FORMAT_YYYYMMDD);
		int todayDateNumber = NumberUtils.toInt(todayDateString);
		targetUrlDailyCrawlTrackingEntity.setTrackDate(todayDateNumber);
		targetUrlDailyCrawlTrackingEntity.setTotalUrls(totalMessages);
		targetUrlDailyCrawlTrackingEntityList.add(targetUrlDailyCrawlTrackingEntity);
		targetUrlDailyCrawlTrackingEntityDAO.insertMultiRowsBatch(targetUrlDailyCrawlTrackingEntityList);
		FormatUtils.getInstance().logMemoryUsage("createTargetUrlDailyCrawlTracking() ip=" + ip + ", domainId=" + domainId + ", domainName=" + domainName
				+ ",created targetUrlDailyCrawlTrackingEntity=" + targetUrlDailyCrawlTrackingEntity.toString());

	}

	private void updateTargetUrlDailyCrawlTracking(int totalMessages) {
		// update 'target_url_daily_crawl_tracking' record for the domain processed.
		Date todayDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
		String todayDateString = DateFormatUtils.format(todayDate, IConstants.DATE_FORMAT_YYYYMMDD);
		int trackDate = NumberUtils.toInt(todayDateString);
		targetUrlDailyCrawlTrackingEntityDAO.update(IConstants.PROCESS_TYPE_NUMBER_CLICKHOUSE, domainId, trackDate, totalMessages);
		FormatUtils.getInstance().logMemoryUsage("updateTargetUrlDailyCrawlTracking() ip=" + ip + ", domainId=" + domainId + ", domainName=" + domainName
				+ ",trackDate=" + trackDate + ",totalMessages=" + totalMessages);

	}

	private List<TargetUrlEntity> getRankedTargetUrlEntityList(List<TargetUrlEntity> targetUrlEntityList, int topxRankingUrl) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		List<TargetUrlEntity> rankedTargetUrlEntityList = new ArrayList<TargetUrlEntity>();
		TargetUrlEntity targetUrlEntity = null;
		int totalRankedUrls = 0;
		int totalSkippedNotSubFolder = 0;
		int totalRankedUrlsAlreadyTargetUrl = 0;
		int totalRankedUrlsToBeCrawled = 0;
		Set<String> existingTargetUrlHashCodeSet = getTargetUrlHashCodeSet(targetUrlEntityList);
		Set<String> rankedTargetUrlHashCodeSet = new HashSet<String>();
		String rankedUrlHashCode = null;

		// the rank date for processing is always two days ago
		Date todayDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
		Date rankDate = DateUtils.addDays(todayDate, -2);

		int searchEngineId = ScKeywordRankService.getSearchEngineId(ownDomainEntity);
		if (searchEngineId == ScKeywordRankService.ENGINE_LANGUAGE_ID_NOT_FOUND) {
			FormatUtils.getInstance().logMemoryUsage(
					"getRankedTargetUrlEntityList() error--search engine ID not found. ip=" + ip + ", domainId=" + domainId + ", domainName=" + domainName);
			return null;
		}

		int searchLanguageId = ScKeywordRankService.getSearchLanguageId(ownDomainEntity);
		if (searchLanguageId == ScKeywordRankService.ENGINE_LANGUAGE_ID_NOT_FOUND) {
			FormatUtils.getInstance().logMemoryUsage(
					"getRankedTargetUrlEntityList() error--search language ID not found. ip=" + ip + ", domainId=" + domainId + ", domainName=" + domainName);
			return null;
		}

		// determine the corrected sub-folder of the client domain
		String subFolder = ownDomainEntity.getSubFolder();
		String correctedSubFolder = null;
		if (StringUtils.isNotBlank(subFolder)) {
			correctedSubFolder = getCorrectedSubFolder(subFolder);
		}

		String rootDomainReverse = FormatUtils.getInstance().getReversedRootDomainName(StringUtils.trim(domainName));
		String domainReverse = FormatUtils.getInstance().getReversedDomainName(StringUtils.trim(domainName));

		final boolean isMobileDomain = ownDomainEntity.isMobileDomain();

		FormatUtils.getInstance()
				.logMemoryUsage("getRankedTargetUrlEntityList() begins. ip=" + ip + ", domainId=" + domainId + ", domainName=" + domainName + ",rankDate=" + rankDate
						+ "isMobileDomain=" + isMobileDomain + ",searchEngineId=" + searchEngineId + ",searchLanguageId=" + searchLanguageId + ",correctedSubFolder=" + correctedSubFolder
						+ ",rootDomainReverse=" + rootDomainReverse + ",domainReverse=" + domainReverse);
		List<String> rankedUrlList = RankingDetailClickHouseDAO.getInstance().getUrlList(ip, domainId, isMobileDomain, searchEngineId, searchLanguageId, rankDate, topxRankingUrl, rootDomainReverse,
				domainReverse);
		// when there are ranked URLs on the rank date for the client domain
		if (rankedUrlList != null && rankedUrlList.size() > 0) {
			FormatUtils.getInstance().logMemoryUsage("getRankedTargetUrlEntityList() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
					+ ",rankedUrlList.size()=" + rankedUrlList.size());
			totalRankedUrls = rankedUrlList.size();
			nextRankedUrl: for (String rankedUrl : rankedUrlList) {

				// when client domain has sub-folder and ranked URL does not belong to sub-folder, skip...
				if (StringUtils.isNotBlank(correctedSubFolder)) {
					if (StringUtils.containsIgnoreCase(rankedUrl, correctedSubFolder) == false) {
						totalSkippedNotSubFolder++;
						continue nextRankedUrl;
					}
				}

				rankedUrlHashCode = Md5Util.Md5(rankedUrl);
				if (existingTargetUrlHashCodeSet.contains(rankedUrlHashCode) == true) {
					totalRankedUrlsAlreadyTargetUrl++;
					continue nextRankedUrl;
				} else {
					if (rankedTargetUrlHashCodeSet.contains(rankedUrlHashCode) == false) {
						targetUrlEntity = new TargetUrlEntity();
						targetUrlEntity.setUrl(rankedUrl);
						// set url type as ranking
						targetUrlEntity.setUrlType(IConstants.URL_TYPE_RANKING);
						rankedTargetUrlEntityList.add(targetUrlEntity);
						rankedTargetUrlHashCodeSet.add(rankedUrlHashCode);
						totalRankedUrlsToBeCrawled++;
					}
				}
			}
		}

		FormatUtils.getInstance()
				.logMemoryUsage("getRankedTargetUrlEntityList() ends. ip=" + ip + ", domainId=" + domainId + ", domainName=" + domainName + ",totalRankedUrls="
						+ totalRankedUrls + ",totalSkippedNotSubFolder=" + totalSkippedNotSubFolder + ",totalRankedUrlsAlreadyTargetUrl="
						+ totalRankedUrlsAlreadyTargetUrl + ",totalRankedUrlsToBeCrawled=" + totalRankedUrlsToBeCrawled + ",elapsed(s.)="
						+ (System.currentTimeMillis() - startTimestamp) / 1000);

		return rankedTargetUrlEntityList;
	}

	private String getCorrectedSubFolder(String subFolder) {
		String correctedSubFolder = StringUtils.lowerCase(subFolder);
		if (StringUtils.startsWith(subFolder, IConstants.SINGLE_FORWARD_SLASHES)) {
			correctedSubFolder = domainName + StringUtils.lowerCase(subFolder);
		}
		return correctedSubFolder;
	}

	private Set<String> getTargetUrlHashCodeSet(List<TargetUrlEntity> targetUrlEntityList) {
		Set<String> hashCodeSet = new HashSet<String>();
		if (targetUrlEntityList != null && targetUrlEntityList.size() > 0) {
			for (TargetUrlEntity targetUrlEntity : targetUrlEntityList) {
				hashCodeSet.add(Md5Util.Md5(StringUtils.trim(targetUrlEntity.getUrl())));
			}
		}
		return hashCodeSet;
	}
}
