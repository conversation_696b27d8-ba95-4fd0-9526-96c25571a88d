package com.actonia.put.messages;

import com.actonia.IConstants;
import com.actonia.dao.*;
import com.actonia.entity.*;
import com.actonia.service.ScKeywordRankService;
import com.actonia.utils.*;
import com.actonia.value.object.UrlCrawlParametersVO;
import com.amazonaws.services.sqs.model.BatchRequestTooLongException;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.StopWatch;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

public class TargetUrlSender {

    private static final Logger logger = LogManager.getLogger(TargetUrlSender.class);

    private static final String DUPLICATED_WWW_DELTAFAUCET_COM = "www.deltafaucet.comwww.deltafaucet.com";
    private static final String DOUBLE_HTTP = "http://http://";
    private static final int defaultThreadCount = 12;
    private static final int TARGET_URL_LIMIT = 30000;
    private final PutMessageUtils putMessageUtils;
    private final OwnDomainEntityDAO ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
    private final int crawlDate;

    private final PoliteCrawlDomainSettingDAO politeCrawlDomainSettingDAO = SpringBeanFactory.getBean("politeCrawlDomainSettingDAO");
    private final PoliteCrawlInstanceDAO politeCrawlInstanceDAO = SpringBeanFactory.getBean("politeCrawlInstanceDAO");

    private final TargetUrlEntityDAO targetUrlEntityDAO = SpringBeanFactory.getBean("targetUrlEntityDAO");
    private final Gson gson = new Gson();

    private final ExecutorService executorService;

    // batch messages size per thread
    private static final int MESSAGE_SIZE_PER_THREAD = 1000;
    private static final int MESSAGE_BATCH_SIZE = 10;
    private final ConcurrentHashMap<Integer, Integer> domainInstanceIdMap = new ConcurrentHashMap<>();
    private static List<String> historicalHtmlFieldNames = null;
    private final Integer domainType;

    private final JsonObject trackDateJsonObject;

    public TargetUrlSender(int threadCount, Integer domainType) throws Exception {
        this.domainType = domainType;
        executorService = Executors.newFixedThreadPool(threadCount);
        putMessageUtils = PutMessageUtils.getInstance();
        historicalHtmlFieldNames = putMessageUtils.getHistoricalHtmlFieldNames();
        final LocalDate today = LocalDate.now();
        crawlDate = Integer.parseInt(today.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        trackDateJsonObject = new JsonObject();
        trackDateJsonObject.addProperty("type", IConstants.TRACK_DATE);
        trackDateJsonObject.addProperty("date", today.toString());
    }

    public static void main(String[] args) throws Exception {
        final Integer domainType = args[0] == null ? null : Integer.parseInt(args[0]);
        final int threadCount = args[1] == null ? defaultThreadCount : Integer.parseInt(args[1]);
        TargetUrlSender targetUrlSender = new TargetUrlSender(threadCount, domainType);
        logger.info("begin sendTargetUrls()");
        targetUrlSender.sendTargetUrls();
        logger.info("end sendTargetUrls()");
    }

    public void sendTargetUrls() {
        // 1. get domain list to send target urls to message queue
        final Collection<OwnDomainEntity> domainEntities;
//        domainEntities = this.getDomainEntities();
//        domainEntities = Collections.singleton(ownDomainEntityDAO.getById(256));
        domainEntities = ownDomainEntityDAO.getByIdArr("4661, 7741, 6060, 8414");
        if (domainEntities.isEmpty()) {
            logger.warn("No domain to send target urls to message queue.");
            return;
        }
        final SQSUtils sqsUtils;
        try {
            sqsUtils = SQSUtils.getInstance();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        // 2. send target urls to message queue for each domain id using thread pool service
        for (final OwnDomainEntity ownDomainEntity : domainEntities) {
            final Integer domainId = ownDomainEntity.getId();
            StopWatch stopWatch = new StopWatch(domainId + " sendTargetUrls");
            // 2.1 get urls for each domain
            stopWatch.start("getTargetUrlCollections");
            final Set<String> targetUrls = this.getTargetUrlCollections(ownDomainEntity);
            stopWatch.stop();
            if (targetUrls.isEmpty()) {
                logger.warn("No target urls for domainId={}", domainId);
                final Integer instanceId = domainInstanceIdMap.get(domainId);
                politeCrawlInstanceDAO.updateSendStatusToNoManagedUrl(instanceId);
                continue;
            }
            // 2.2 send messages to message queue
            stopWatch.start("sendMessages");
            sendMessages(sqsUtils, ownDomainEntity, domainId, targetUrls);
            stopWatch.stop();
            logger.info("OID [{}] sendMessages: {}", domainId, stopWatch.prettyPrint());
            // 2.3 create controller queue message to send
            this.sendControllerQueueMessage(ownDomainEntity);

        }
        this.executorService.shutdown();

    }

    private void sendControllerQueueMessage(OwnDomainEntity ownDomainEntity) {
        final String controllerMessageBodyInJsonFormat;
        final Integer domainId = ownDomainEntity.getId();
        // create controller queue message and send to controller queue
        try {
            final JsonArray controllerMessage = PutMessageUtils.getInstance().createControllerMessageBodyInJsonFormat(ownDomainEntity);
            // add today as track_date to controller message
            controllerMessage.add(trackDateJsonObject);
            controllerMessageBodyInJsonFormat = gson.toJson(controllerMessage);
            final String replace = controllerMessageBodyInJsonFormat.replace("TARGET_URL_HTML_", "TEST_TARGET_URL_HTML_");
            final String queueUrl = SQSUtils.getInstance().createFifoQueue("TEST_TARGET_URL_HTML_QUEUE_NAMES.fifo");
            final Map<String, String> messages = Collections.singletonMap(String.valueOf(System.nanoTime()), controllerMessageBodyInJsonFormat);
            SQSUtils.getInstance().sendBatchMessageToFifoQueue(queueUrl, messages);
            logger.info("[ OID: {} ] sendControllerQueueMessage: {}", domainId, controllerMessageBodyInJsonFormat);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        // save crawlParam to polite_crawl_instance
        final PoliteCrawlInstance instance = new PoliteCrawlInstance();
        instance.setId(domainInstanceIdMap.get(domainId));
        instance.setCrawlParam(controllerMessageBodyInJsonFormat);
        politeCrawlInstanceDAO.updateCrawlParam(instance);
    }

    private void sendMessages(SQSUtils sqsUtils, OwnDomainEntity ownDomainEntity, Integer domainId, Set<String> targetUrls) {
        // prepare messages for sending to message queue
        List<String> targetUrlMessages = this.createMessages(ownDomainEntity, targetUrls);
        // 2.2 send messages to message queue using thread pool service
        List<CompletableFuture<Integer>> futures = new ArrayList<>();

        final String queueName = IConstants.QUEUE_NAME_TARGET_URL_DAILY_HTML_PREFIX + ownDomainEntity.getLanguage().toUpperCase() + IConstants.UNDERSCORE + domainId;
        final String queueUrl;
        try {
            queueUrl = sqsUtils.createQueue(queueName);
            if (logger.isDebugEnabled()) {
                logger.debug("OID [{}] create queueUrl: {}", domainId, queueUrl);
            }
        } catch (Exception e) {
            logger.error("OID [{}] failed to create queue: {}", domainId, queueName);
            throw new RuntimeException(e);
        }

        final PoliteCrawlInstance instance = new PoliteCrawlInstance();
        instance.setId(domainInstanceIdMap.get(domainId));
        final int messageSize = targetUrlMessages.size();
        Integer totalSentCount;
        try {
            // create a subList to send messages to message queue in a thread when messages size is reached batch size, remain messages will be sent in another thread out of the loop
            List<String> subList = new ArrayList<>();
            for (String targetUrlMessage : targetUrlMessages) {
                subList.add(targetUrlMessage);
                if (subList.size() == MESSAGE_SIZE_PER_THREAD) {
                    List<String> finalSubList = subList;
                    CompletableFuture<Integer> future = CompletableFuture.supplyAsync(() -> sendBatchMessages(sqsUtils, queueUrl, finalSubList), executorService);
                    futures.add(future);
                    subList = new ArrayList<>();
                }
            }
            // sending remaining messages if messages size is not reached batch size and there are still has remaining messages
            if (subList.size() > 0) {
                List<String> finalList = subList;
                CompletableFuture<Integer> future = CompletableFuture.supplyAsync(() -> sendBatchMessages(sqsUtils, queueUrl, finalList), executorService);
                futures.add(future);
            }
            // 2.2 wait for all futures to finish
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            totalSentCount = futures.stream().map(CompletableFuture::join).reduce(0, Integer::sum);

            // 3. update MySQL polite_crawl_instance table
            instance.updateSendStatusAndEndSendDate(PoliteCrawlInstance.SendStatusEnum.SEND_COMPLETED, totalSentCount);
            instance.setQueueName(queueName);
            politeCrawlInstanceDAO.updateStatusToSent(instance);
        } catch (Exception e) {
            instance.updateSendStatusAndEndSendDate(PoliteCrawlInstance.SendStatusEnum.SEND_ERROR, messageSize);
            politeCrawlInstanceDAO.updateStatusToSent(instance);
            throw new RuntimeException(e);
        }
        // log the number of messages sent to message queue by each future after completable future is done
        logger.info("OID [{}] totalSentCount={}", domainId, totalSentCount);
    }

    /**
     * get target url list by t_target_url, if isAutoAssociateTargetUrlsRequired == false add list of ranked target URLs that are not in 't_target_url' MySQL table
     *
     * @return target url list
     */
    public Set<String> getTargetUrlCollections(OwnDomainEntity ownDomainEntity) {
        Integer domainId = ownDomainEntity.getId();
        final List<TargetUrlEntity> targetUrlList = this.targetUrlEntityDAO.getTargetUrlList(domainId);
        if (targetUrlList.isEmpty()) {
            logger.warn("OID: [{}] targetUrlList is empty, skip to send url message", domainId);
            return Collections.emptySet();
        }
        // t_target_url of this domain is not empty insert to MySQL polite_crawl_instance table
        PoliteCrawlInstance politeCrawlInstance = PoliteCrawlInstance.targetUrlSending(domainId, crawlDate);
        politeCrawlInstance.setManagedUrlCount(targetUrlList.size());
        final PoliteCrawlInstance instance = politeCrawlInstanceDAO.insert(politeCrawlInstance);
        // cache instanceId for domainId
        this.domainInstanceIdMap.put(domainId, instance.getId());

        // when isAutoAssociateTargetUrlsRequired == true, auto-association will create ranking URLs in 't_target_url' MySQL table with type = 1 and status = 1
        // when isAutoAssociateTargetUrlsRequired == false, retrieve list of ranked target URLs that are not in 't_target_url' MySQL table with type = 1 and status = 1
        if (!checkIsAutoAssociateTargetUrlsRequired(ownDomainEntity)) {
            if (logger.isDebugEnabled()) {
                logger.debug("OID: [{}] checkIsAutoAssociateTargetUrlsRequired is false, get ranked target url list", domainId);
            }
            try {
                Collection<TargetUrlEntity> rankedTargetUrlList = this.getRankedTargetUrls(targetUrlList, ownDomainEntity);
                // add ranked target url list to targetUrlList
                targetUrlList.addAll(rankedTargetUrlList);
            } catch (Exception e) {
                logger.error("OID: [{}] failed to get ranked target url list", domainId);
            }
        }

        // deduplicate target url list and skip when target URL string contains 'www.deltafaucet.comwww.deltafaucet.com'.... or 'http://http://'....
        final Set<String> filteredUrlsToSendMessage = targetUrlList.stream().filter(targetUrlEntity -> {
            final String url = targetUrlEntity.getUrl();
            return !StringUtils.contains(url, DUPLICATED_WWW_DELTAFAUCET_COM) && !StringUtils.contains(url, DOUBLE_HTTP);
        }).map(TargetUrlEntity::getUrl).collect(Collectors.toSet());
        logger.info("OID: [{}] filteredUrlsToSendMessage size: {}", domainId, filteredUrlsToSendMessage.size());
        return filteredUrlsToSendMessage;

    }

    /**
     * check if this domain requires target URLs auto-association
     *
     * @return only return true when 'url_crawl_parameters' contains {"type":"autoAssociateTargetUrls","data":"true"}
     */
    private boolean checkIsAutoAssociateTargetUrlsRequired(OwnDomainEntity ownDomainEntity) {
        final String urlCrawlParameters = ownDomainEntity.getUrlCrawlParameters();
        // by default, auto-associate target URLs is off (i.e. when t_own_domain's 'url_crawl_parameters' is null
        // auto-associate target URLs is off when 'url_crawl_parameters' contains {"type":"autoAssociateTargetUrls","data":"false"}
        // auto-associate target URLs is on when 'url_crawl_parameters' contains {"type":"autoAssociateTargetUrls","data":"true"}
        // only return true when 'url_crawl_parameters' contains {"type":"autoAssociateTargetUrls","data":"true"}
        if (!urlCrawlParameters.isEmpty()) {
            final UrlCrawlParametersVO[] urlCrawlParametersVOS = gson.fromJson(urlCrawlParameters, UrlCrawlParametersVO[].class);
            final String autoAssociateTargetUrlData = Arrays.stream(urlCrawlParametersVOS).filter(urlCrawlParametersVO -> urlCrawlParametersVO.getType().equalsIgnoreCase(IConstants.AUTO_ASSOCIATE_TARGET_URLS)).findAny().map(UrlCrawlParametersVO::getData).orElse("false");
            return BooleanUtils.toBoolean(autoAssociateTargetUrlData, "true", "false");
        }
        return false;
    }

    // create getRankedTargetUrls method
    public Collection<TargetUrlEntity> getRankedTargetUrls(List<TargetUrlEntity> targetUrlList, OwnDomainEntity ownDomainEntity) throws Exception {
        final Set<String> targetUrlHashSet = targetUrlList.stream().map(targetUrlEntity -> Md5Util.Md5(StringUtils.trim(targetUrlEntity.getUrl()))).collect(Collectors.toSet());
        Set<String> rankedTargetUrlHashCodeSet = new HashSet<>();

        // the rank date for processing is always two days ago
        Date todayDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
        Date rankDate = DateUtils.addDays(todayDate, -2);

        int searchEngineId = ScKeywordRankService.getSearchEngineId(ownDomainEntity);
        int domainId = ownDomainEntity.getId();
        if (searchEngineId == ScKeywordRankService.ENGINE_LANGUAGE_ID_NOT_FOUND) {
            logger.error("domainId = [{}] getRankedTargetUrls() error--search engine ID not found", domainId);
            return Collections.emptyList();
        }

        int searchLanguageId = ScKeywordRankService.getSearchLanguageId(ownDomainEntity);
        if (searchLanguageId == ScKeywordRankService.ENGINE_LANGUAGE_ID_NOT_FOUND) {
            logger.error("domainId = [{}] getRankedTargetUrls() error--search language ID not found", domainId);
            return Collections.emptyList();
        }

        String domainName = ownDomainEntity.getName();
        String rootDomainReverse = FormatUtils.getInstance().getReversedRootDomainName(StringUtils.trim(domainName));
        String domainReverse = FormatUtils.getInstance().getReversedDomainName(StringUtils.trim(domainName));
        final boolean isMobileDomain = ownDomainEntity.isMobileDomain();
        final PoliteCrawlDomainSetting politeCrawlDomainSetting = politeCrawlDomainSettingDAO.findByOwnDomainId(domainId);
        if (politeCrawlDomainSetting != null && politeCrawlDomainSetting.getEnableRankingUrl() == 1) {
            final int topxRankingUrl = politeCrawlDomainSetting.getTopxRankingUrl();
            List<String> rankedUrlList = RankingDetailClickHouseDAO.getInstance().getUrlList(null, domainId, isMobileDomain, searchEngineId, searchLanguageId, rankDate, topxRankingUrl, rootDomainReverse, domainReverse);
            // if rankedUrlList is empty, return empty list
            if (rankedUrlList.isEmpty()) {
                if (logger.isDebugEnabled()) {
                    logger.debug("OID: [{}] rankedUrlList is empty", domainId);
                }
                return Collections.emptyList();
            }
            // save ranked URL count
            politeCrawlInstanceDAO.updateRankUrlCountById(domainInstanceIdMap.get(domainId), rankedUrlList.size());

            // determine the corrected sub-folder of the client domain
            String subFolder = ownDomainEntity.getSubFolder();
            String correctedSubFolder = StringUtils.isNotEmpty(subFolder) ? getCorrectedSubFolder(subFolder, domainName) : null;

            List<TargetUrlEntity> rankedTargetUrlList = new ArrayList<>();
            for (String rankedUrl : rankedUrlList) {
                // when client domain has sub-folder and ranked URL does not belong to sub-folder, skip...
                if (StringUtils.isNotBlank(correctedSubFolder) && !StringUtils.containsIgnoreCase(rankedUrl, correctedSubFolder)) {
                    if (logger.isDebugEnabled()) {
                        logger.debug("OID: [{}] rankedUrl = [{}], rankedUrl does not belong to sub-folder", domainId, rankedUrl);
                    }
                    continue;
                }
                final String rankedUrlHash = Md5Util.Md5(rankedUrl);
                if (targetUrlHashSet.contains(rankedUrlHash)) {
                    continue;
                }
                if (rankedTargetUrlHashCodeSet.contains(rankedUrlHash)) {
                    continue;
                }
                final TargetUrlEntity targetUrlEntity = new TargetUrlEntity();
                targetUrlEntity.setUrl(rankedUrl);
                rankedTargetUrlList.add(targetUrlEntity);
                rankedTargetUrlHashCodeSet.add(rankedUrlHash);
            }
            logger.info("OID: [{}] rankedUrlFrom clickHouse dao size={}, final rankedTargetUrl size={}", domainId, rankedUrlList.size(), rankedTargetUrlList.size());
            return rankedTargetUrlList;
        }
        return Collections.emptyList();
    }

    /**
     * create messages list for domain
     * @param domainEntity the domain entity
     * @param targetUrls urls to be sent
     * @return messages
     */
    private List<String> createMessages(OwnDomainEntity domainEntity, Set<String> targetUrls) {
        final Map<String, HtmlClickHouseEntity> htmlClickHouseEntityMap;
        try {
            htmlClickHouseEntityMap = putMessageUtils.getTargetUrlHtmlClickHouseEntityMap(null, domainEntity.getId(), null, historicalHtmlFieldNames);
        } catch (Exception e) {
            logger.error("OID: [{}] getTargetUrlHtmlClickHouseEntityMap error: {}", domainEntity.getId(), e.getMessage());
            throw new RuntimeException(e);
        }
        // create messages collection
        final String domainLanguage = domainEntity.getLanguage();
        List<String> messages = new ArrayList<>(targetUrls.size());
        for (String url : targetUrls) {
            final String md5HashCode = Md5Util.Md5(url.trim());
            final HtmlClickHouseEntity htmlClickHouseEntity = htmlClickHouseEntityMap.get(md5HashCode);
            // create 'urlMetricsEntityV3' with historical crawled data
            final UrlMetricsEntityV3 urlMetrics = putMessageUtils.createUrlMetrics(url, htmlClickHouseEntity);
            urlMetrics.setLanguageCode(domainLanguage);
            final String messageBody = gson.toJson(urlMetrics);
            messages.add(messageBody);
        }
        return messages;
    }

    private Collection<OwnDomainEntity> getDomainEntities() {
        logger.info("find domainEntities from t_own_domain join t_target_url and order by urls count");
//        final List<OwnDomainEntity> domainEntitiesHaveTargetUrl = ownDomainEntityDAO.findDomainsWithTargetUrl();
        final List<OwnDomainEntity> domainEntitiesHaveTargetUrl;
        if (this.domainType == 1) {
            domainEntitiesHaveTargetUrl = ownDomainEntityDAO.getSmallTargetUrlDomainIds(TARGET_URL_LIMIT);
            logger.info("get domainIds from t_own_domain join t_target_url to get smallTargetUrlsDomains, domainEntitiesHaveTargetUrl size={}", domainEntitiesHaveTargetUrl.size());
        } else {
            domainEntitiesHaveTargetUrl = ownDomainEntityDAO.getLargeTargetUrlDomainIds(TARGET_URL_LIMIT);
            logger.info("get domainIds from t_own_domain join t_target_url to get largeTargetUrlsDomains, domainEntitiesHaveTargetUrl size={}", domainEntitiesHaveTargetUrl.size());
        }
        logger.info("start find PoliteCrawlInstance last 7 days to skip domains already sent or in crawling status");
        final List<PoliteCrawlInstance> politeCrawlInstances = politeCrawlInstanceDAO.findLatestCrawlDateInstance(Integer.parseInt(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern("yyyyMMdd"))));
        logger.info("find PoliteCrawlInstance List size={}", politeCrawlInstances.size());
        final Map<Integer, PoliteCrawlInstance> instanceMap = politeCrawlInstances.stream().collect(Collectors.toMap(PoliteCrawlInstance::getOwnDomainId, politeCrawlInstance -> politeCrawlInstance));
        final Set<Integer> domainIdsByNotEnabled = this.politeCrawlDomainSettingDAO.findDomainIdsByEnabledStatus();
        logger.info("find domainIds not enabled from polite_crawl_domain_setting table size={}", domainIdsByNotEnabled.size());
        List<OwnDomainEntity> domainListFilterSetting = new ArrayList<>();
        for (OwnDomainEntity ownDomainEntity : domainEntitiesHaveTargetUrl) {
            final Integer id = ownDomainEntity.getId();
            if (domainIdsByNotEnabled.contains(id)) {
                logger.info("OID: [{}] domain is not enabled, skip", id);
                continue;
            }
            // check if domain is already sent to SQS today or is still in crawling status
            final PoliteCrawlInstance instance = instanceMap.get(id);
            if (instance != null) {
                final PoliteCrawlInstance.CrawlStatusEnum crawlStatus = instance.getCrawlStatus();
                final Integer sendCrawlDate = instance.getCrawlDate();
                if (sendCrawlDate == this.crawlDate) {
                    logger.info("OID: [{}] domain is already sent to SQS today (current status={}), skip", id, crawlStatus.name());
                    continue;
                }
                if (crawlStatus == PoliteCrawlInstance.CrawlStatusEnum.NOT_STARTED) {
                    logger.info("OID: [{}] domain is already sent (crawDate={}) status is not started, skip", id, sendCrawlDate);
                    continue;
                }
                if (crawlStatus == PoliteCrawlInstance.CrawlStatusEnum.CRAWLING) {
                    logger.info("OID: [{}] domain is crawling (crawlDate={}, startCrawlDate={}) don't need to send until crawling is completed, skip", id, sendCrawlDate, instance.getStartCrawlDate());
                    continue;
                }
            }
            domainListFilterSetting.add(ownDomainEntity);
        }
        if (domainListFilterSetting.isEmpty()) {
            logger.warn("no domain need send messages to SQS");
            return Collections.emptyList();
        }
//        domainListFilterSetting = domainListFilterSetting.stream().filter(domainEntity -> domainEntity.getId() == 4 || domainEntity.getId() == 256).collect(Collectors.toList());

        // filter domains is not crawling through GetQueueAttributes from SQS
        logger.info("skip domains is still have messages in queue through GetQueueAttributes from SQS");
        List<OwnDomainEntity> domainListToBeSend = new ArrayList<>();
        final int domainSize = domainListFilterSetting.size();
        for (int i = 0; i < domainSize; i++) {
            final OwnDomainEntity domainEntity = domainListFilterSetting.get(i);
            final Integer domainId = domainEntity.getId();
            final String queueName = IConstants.QUEUE_NAME_TEST_TARGET_URL_HTML_PREFIX + domainEntity.getLanguage().toUpperCase() + IConstants.UNDERSCORE + domainId;
            try {
                final Integer[] approximateNumberOfMessagesAndInflight = SQSUtils.getInstance().getApproximateNumberOfMessagesAndInflight(queueName);
                // confirm no messages in the queue for this domain
                if (approximateNumberOfMessagesAndInflight[0] == 0 && approximateNumberOfMessagesAndInflight[1] == 0) {
                    domainListToBeSend.add(domainEntity);
                } else {
                    logger.info("domainId={}, domainName={}, queueName={} is not empty. skip", domainId, domainEntity.getDomain(), queueName);
                }
            } catch (Exception e) {
                logger.error("Error processing domainEntity with id: {}", domainId, e);
                continue;
            }

            if (i % 100 == 0) {
                logger.info("Processed {}/{} domains of check messages from SQS, {} domains need to send", i, domainSize, domainListToBeSend.size());
            }
        }
        logger.info("get from table domainList domainSize = {}, domainListToBeSend after filtered domainSize={}", domainSize, domainListToBeSend.size());
        return domainListToBeSend;

    }

    // send messages to SQS in a thread
    private int sendBatchMessages(SQSUtils sqsUtils, String queueUrl, List<String> messageList) {
        Map<String, String> messages = new HashMap<>();
        int totalCount = 0;
        for (final String messageBody : messageList) {
            messages.put(String.valueOf(System.nanoTime()), messageBody);
            if (messages.size() == MESSAGE_BATCH_SIZE) {
                sendBatchMessagesWithRetry(sqsUtils, queueUrl, messages);
                totalCount += messages.size();
                messages.clear();
            }
        }
        if (messages.size() > 0) {
            sendBatchMessagesWithRetry(sqsUtils, queueUrl, messages);
            totalCount += messages.size();
            messages.clear();
        }
        logger.info("send {} messages to queue: {} finished", totalCount, queueUrl);
        return totalCount;
    }

    private void sendBatchMessagesWithRetry(SQSUtils sqsUtils, String queueUrl, Map<String, String> messages) {
        int retryCount = 0;
        while (retryCount < 3) {
            try {
                sqsUtils.sendBatchMessageToQueueThrowsException(queueUrl, messages);
                Thread.sleep(100);
                break;
            } catch (BatchRequestTooLongException e) {
                logger.error("send {} messages to queue: {} failed, retry...", messages.size(), queueUrl);
                logger.error("retryCount: {}", retryCount);
                retryCount++;
            } catch (Exception e) {
                retryCount++;
                logger.error("queueUrl = {} error for AmazonSQS, error message: {}", queueUrl, e.getMessage());
                logger.error("sleep and try again... retryCount={}", retryCount);
                try {
                    Thread.sleep(60000);
                } catch (InterruptedException ex) {
                    throw new RuntimeException(ex);
                }
            }
        }
    }

    private String getCorrectedSubFolder(String subFolder, String domainName) {
        if (StringUtils.startsWith(subFolder, IConstants.SINGLE_FORWARD_SLASHES)) {
            return domainName + StringUtils.lowerCase(subFolder);
        }
        return StringUtils.lowerCase(subFolder);
    }


}
