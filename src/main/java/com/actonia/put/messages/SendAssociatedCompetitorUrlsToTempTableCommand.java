package com.actonia.put.messages;

import java.net.URL;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.StringUtils;

import com.actonia.IConstants;
import com.actonia.concurrency.BaseThreadCommand;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.dao.AssociatedUrlEntityDAO;
import com.actonia.dao.CompetitorUrlEntityDAO;
import com.actonia.dao.TempCompetitorUrlsHtmlEntityDAO;
import com.actonia.entity.AssociatedUrlEntity;
import com.actonia.entity.CompetitorUrlEntity;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.Md5Util;
import com.actonia.utils.PutMessageUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.DomainIdLanguageCodeValueObject;
import com.google.gson.Gson;

public class SendAssociatedCompetitorUrlsToTempTableCommand extends BaseThreadCommand {
	private boolean isDebug = false;
	private String ip;
	private OwnDomainEntity ownDomainEntity;
	private CompetitorUrlEntityDAO competitorUrlEntityDAO;
	private AssociatedUrlEntityDAO associatedUrlEntityDAO;
	private TempCompetitorUrlsHtmlEntityDAO tempCompetitorUrlsHtmlEntityDAO;

	private static final String HTTP_COLON_SLASH_SLASH = "http://";
	private static final String HTTPS_COLON_SLASH_SLASH = "https://";
	private static final int MAX_RECORDS_PER_BATCH_UPDATE = 10;
	private static final String MAPS_DOT_GOOGLE_DOT = "maps.google.";
	private static final String NEWS_DOT_GOOGLE_DOT = "news.google.";
	private static final String IMAGES_DOT_GOOGLE_DOT = "images.google.";
	private int trackDateNumber;
	private static final String COUPONCABIN = "couponcabin";
	private boolean isJapaneseDomains;
	private String processType;

	public SendAssociatedCompetitorUrlsToTempTableCommand(String ip, OwnDomainEntity ownDomainEntity, int trackDateNumber, boolean isJapaneseDomains,
			String processType) {
		super();
		this.ip = ip;
		this.ownDomainEntity = ownDomainEntity;
		this.competitorUrlEntityDAO = SpringBeanFactory.getBean("competitorUrlEntityDAO");
		this.associatedUrlEntityDAO = SpringBeanFactory.getBean("associatedUrlEntityDAO");
		this.tempCompetitorUrlsHtmlEntityDAO = SpringBeanFactory.getBean("tempCompetitorUrlsHtmlEntityDAO");
		this.trackDateNumber = trackDateNumber;
		this.isJapaneseDomains = isJapaneseDomains;
		this.processType = processType;
	}

	@Override
	protected void execute() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		int domainId = ownDomainEntity.getId();
		String domainName = ownDomainEntity.getDomain();
		String languageCode = ownDomainEntity.getLanguage();
		FormatUtils.getInstance().logMemoryUsage("SendAssociatedCompetitorUrlsToTempTableCommand.execute() begins. ip=" + ip + ", domain id=" + domainId
				+ " , domain name=" + domainName + ",isJapaneseDomains=" + isJapaneseDomains + ",languageCode=" + languageCode);
		String urlString = null;
		URL url = null;
		String hostname = null;
		String hashCode = null;
		AssociatedUrlEntity associatedUrlEntityExisting = null;
		int testTotalRecordsCreated = 0;
		int testTotalRecordsExisting = 0;
		int totalCompetitorUrlsExisting = 0;
		int totalCompetitorUrlsNew = 0;
		Integer protocolNew = null;
		Integer protocolExisting = null;
		Integer cachedProtocol = null;
		AssociatedUrlEntity associatedUrlEntityNew = null;
		Integer[] domainIds = null;
		String[] languageCodes = null;
		DomainIdLanguageCodeValueObject domainIdLanguageCodeValueObject = null;
		List<AssociatedUrlEntity> associatedUrlEntityToBeCreatedList = new ArrayList<AssociatedUrlEntity>();
		List<CompetitorUrlEntity> competitorUrlEntityList = competitorUrlEntityDAO.getUniqueAssociatedCompetitorUrls(domainId);
		String domainIdLanguageCodeJson = null;
		Gson gson = new Gson();
		if (competitorUrlEntityList != null && competitorUrlEntityList.size() > 0) {
			nextCompetitorUrlEntity: for (CompetitorUrlEntity competitorUrlEntity : competitorUrlEntityList) {
				urlString = competitorUrlEntity.getUrl();

				// URL must be http:// of https://
				if (StringUtils.startsWith(urlString, HTTP_COLON_SLASH_SLASH) || StringUtils.startsWith(urlString, HTTPS_COLON_SLASH_SLASH)) {
					// when URL starts with "http://" or "https://", continue processing....				
				}
				// when URL not starts with "http://" or "https://", skip....
				else {
					//FormatUtils.getInstance().logMemoryUsage("SendAssociatedCompetitorUrlsToTempTableCommand.execute() ip=" + ip + ",domain id=" + domainId
					//		+ ",domain name=" + domainName + ",skip urlString=" + urlString + ",invalid protocol.");
					//continue;
				}

				// skip when URL contains "maps.google.", "news.google." or "images.google."
				if (StringUtils.contains(urlString, MAPS_DOT_GOOGLE_DOT) || StringUtils.contains(urlString, NEWS_DOT_GOOGLE_DOT)
						|| StringUtils.contains(urlString, IMAGES_DOT_GOOGLE_DOT)) {
					//FormatUtils.getInstance().logMemoryUsage("SendAssociatedCompetitorUrlsToTempTableCommand.execute() ip=" + ip + ",domain id=" + domainId
					//		+ ",domain name=" + domainName + ",skip urlString=" + urlString + ", google links...");
					continue nextCompetitorUrlEntity; // proceed with next competitor URL
				}

				try {
					url = new URL(urlString);

					// domain of competitor URL
					hostname = url.getHost();

					// protocol of competitor URL
					protocolNew = null;
					if (StringUtils.equalsIgnoreCase(url.getProtocol(), IConstants.PROTOCOL_HTTPS)) {
						protocolNew = IConstants.TRUE_NUMERIC;
					} else if (StringUtils.equalsIgnoreCase(url.getProtocol(), IConstants.PROTOCOL_HTTP)) {
						protocolNew = IConstants.FALSE_NUMERIC;
					} else {
						continue nextCompetitorUrlEntity; // proceed with next competitor URL						
					}

					// skip 'couponcabin'
					if (StringUtils.containsIgnoreCase(hostname, COUPONCABIN) || StringUtils.containsIgnoreCase(hostname, ".expedia.")
							|| StringUtils.containsIgnoreCase(hostname, ".carrentals.") || StringUtils.containsIgnoreCase(hostname, ".hotels.")
							|| StringUtils.containsIgnoreCase(hostname, ".hoteles.") || StringUtils.containsIgnoreCase(hostname, ".hoteis.")) {
						//FormatUtils.getInstance().logMemoryUsage("SendAssociatedCompetitorUrlsToTempTableCommand.execute() ip=" + ip + ",skipping couponcabin urlString="
						//		+ urlString);
						continue nextCompetitorUrlEntity; // proceed with next competitor URL
					}
					for (String expediaDomain : SendAssociatedUrlsToCrawlQueuesByDomain.getExpediaDomainList()) {
						if (StringUtils.containsIgnoreCase(hostname, expediaDomain)) {
							continue nextCompetitorUrlEntity;
						}
					}
				}
				// when URL is invalid, skip this URL and continue processing...
				catch (Exception exception) {
					//FormatUtils.getInstance().logMemoryUsage("SendAssociatedCompetitorUrlsToTempTableCommand.execute() ip=" + ip + ",domain id=" + domainId
					//		+ ",domain name=" + domainName + ",skip urlString=" + urlString + ",exception=" + exception.getMessage());
					continue nextCompetitorUrlEntity; // proceed with next competitor URL
				}
				hashCode = Md5Util.Md5(urlString);
				if (StringUtils.isBlank(hashCode)) {
					continue nextCompetitorUrlEntity; // proceed with next competitor URL
				}

				// check if the competitor URL has already been created in 'temp_associated_urls_html' table
				if (SendAssociatedUrlsToCrawlQueuesByDomain.getConcurrentMap().containsKey(hashCode)) {
					totalCompetitorUrlsExisting++;
					if (PutMessageUtils.getInstance().isProcessTypeSolr(processType) == true) {
						associatedUrlEntityExisting = associatedUrlEntityDAO.get(hostname, hashCode);
					}
					// when storing data in ClickHouse database, update the existing 'domain_id_language_code_json' field with current domain ID and language code					
					else {
						associatedUrlEntityExisting = tempCompetitorUrlsHtmlEntityDAO.get(hostname, hashCode);
						if (associatedUrlEntityExisting != null) {
							PutMessageUtils.getInstance().updateDomainIdLanguageCodeJson(associatedUrlEntityExisting, domainId, languageCode);
						}
					}
					cachedProtocol = SendAssociatedUrlsToCrawlQueuesByDomain.getConcurrentMap().get(hashCode);
					// when the protocol is not available in cache (ie. in the 'temp_associated_urls_html' record), no need to change anything
					if (cachedProtocol.intValue() == IConstants.UNKNOWN_PROTOCOL.intValue()) {
						// proceed with next competitor URL
					}
					// when the cached protocol is identical to protocol of URL string, no need to change anything
					else if (cachedProtocol != null && protocolNew != null && cachedProtocol.intValue() == protocolNew.intValue()) {
						// proceed with next competitor URL
					}
					// when protocol of URL string is not identical to the cached protocol
					else {
						// when cached protocol is 'https', no need to change anything
						if (cachedProtocol.intValue() == IConstants.TRUE_NUMERIC.intValue()) {
							// proceed with next competitor URL
						}
						// when cached protocol is 'http' and protocol of URL string is 'https'
						else {
							if (associatedUrlEntityExisting != null) {
								protocolExisting = associatedUrlEntityExisting.getProtocol();
								// when existing protocol is 'http' and new protocol is 'https', update url in 'temp_associated_urls_html' record with 'https'
								if (protocolNew != null && protocolExisting != null && protocolNew.intValue() == IConstants.TRUE_NUMERIC
										&& protocolExisting.intValue() == IConstants.FALSE_NUMERIC) {
									if (PutMessageUtils.getInstance().isProcessTypeSolr(processType) == true) {
										associatedUrlEntityDAO.updateUrl(hostname, hashCode, urlString, protocolNew, trackDateNumber);
									} else {
										//tempCompetitorUrlsHtmlEntityDAO.updateUrl(hostname, hashCode, urlString, protocolNew, trackDateNumber);
									}
								}
								SendAssociatedUrlsToCrawlQueuesByDomain.getConcurrentMap().put(hashCode, protocolNew);
								// proceed with next competitor URL
							}
							// when 'temp_associated_urls_html' record is not available (ie. still in 'associatedUrlEntityToBeCreatedList'), , update the 'url' with the URL string with 'https'
							else {
								nextAssociatedUrlEntity: for (AssociatedUrlEntity testAssociatedUrlEntity : associatedUrlEntityToBeCreatedList) {
									if (StringUtils.equalsIgnoreCase(testAssociatedUrlEntity.getHashCode(), hashCode)) {
										testAssociatedUrlEntity.setUrl(urlString);

										// when storing data in ClickHouse database, update the existing 'domain_id_language_code_json' field with current domain ID and language code
										if (PutMessageUtils.getInstance().isProcessTypeSolr(processType) == false) {
											domainIdLanguageCodeJson = PutMessageUtils.getInstance()
													.getRevisedDomainIdLanguageCodeJson(testAssociatedUrlEntity.getDomainIdLanguageCodeJson(), domainId, languageCode);
											if (StringUtils.isNotBlank(domainIdLanguageCodeJson)) {
												testAssociatedUrlEntity.setDomainIdLanguageCodeJson(domainIdLanguageCodeJson);
											}
										}

										break nextAssociatedUrlEntity;
									}
								}
								SendAssociatedUrlsToCrawlQueuesByDomain.getConcurrentMap().put(hashCode, protocolNew);
								// proceed with next competitor URL
							}
						}
					}
					continue nextCompetitorUrlEntity; // proceed with next competitor URL
				}
				SendAssociatedUrlsToCrawlQueuesByDomain.getConcurrentMap().put(hashCode, protocolNew);

				// valid URL string, create temporary table record
				associatedUrlEntityNew = new AssociatedUrlEntity();
				associatedUrlEntityNew.setHashCode(hashCode);
				associatedUrlEntityNew.setHostname(hostname);
				associatedUrlEntityNew.setUrl(urlString);
				associatedUrlEntityNew.setTrackDate(trackDateNumber);
				if (isJapaneseDomains == true) {
					associatedUrlEntityNew.setLanguageCode(IConstants.JA);
				}
				associatedUrlEntityNew.setProtocol(protocolNew);

				// when storing data in ClickHouse database, maintain the 'domain_id_language_code_json' field
				if (PutMessageUtils.getInstance().isProcessTypeSolr(processType) == false) {
					domainIds = new Integer[] { domainId };
					languageCodes = new String[] { languageCode };
					domainIdLanguageCodeValueObject = new DomainIdLanguageCodeValueObject();
					domainIdLanguageCodeValueObject.setDomainIds(domainIds);
					domainIdLanguageCodeValueObject.setLanguageCodes(languageCodes);
					domainIdLanguageCodeJson = gson.toJson(domainIdLanguageCodeValueObject, DomainIdLanguageCodeValueObject.class);
					associatedUrlEntityNew.setDomainIdLanguageCodeJson(domainIdLanguageCodeJson);
				}

				associatedUrlEntityToBeCreatedList.add(associatedUrlEntityNew);
				if (isDebug == true) {
					FormatUtils.getInstance().logMemoryUsage("SendAssociatedCompetitorUrlsToTempTableCommand.execute() ip=" + ip + ",competitor urlString=" + urlString
							+ ",associatedUrlEntity to be created=" + associatedUrlEntityNew.toString());
				}
				if (associatedUrlEntityToBeCreatedList.size() >= MAX_RECORDS_PER_BATCH_UPDATE) {
					testTotalRecordsCreated = PutMessageUtils.getInstance().updateDataStore(domainId, domainName, associatedUrlEntityToBeCreatedList, trackDateNumber,
							ip, processType);
					totalCompetitorUrlsNew = totalCompetitorUrlsNew + testTotalRecordsCreated;
					testTotalRecordsExisting = associatedUrlEntityToBeCreatedList.size() - testTotalRecordsCreated;
					totalCompetitorUrlsExisting = totalCompetitorUrlsExisting + testTotalRecordsExisting;
					associatedUrlEntityToBeCreatedList = new ArrayList<AssociatedUrlEntity>();
				}
			}
			if (associatedUrlEntityToBeCreatedList != null && associatedUrlEntityToBeCreatedList.size() > 0) {
				testTotalRecordsCreated = PutMessageUtils.getInstance().updateDataStore(domainId, domainName, associatedUrlEntityToBeCreatedList, trackDateNumber, ip,
						processType);
				totalCompetitorUrlsNew = totalCompetitorUrlsNew + testTotalRecordsCreated;
				testTotalRecordsExisting = associatedUrlEntityToBeCreatedList.size() - testTotalRecordsCreated;
				totalCompetitorUrlsExisting = totalCompetitorUrlsExisting + testTotalRecordsExisting;
				associatedUrlEntityToBeCreatedList = new ArrayList<AssociatedUrlEntity>();
			}
		}
		FormatUtils.getInstance()
				.logMemoryUsage("SendAssociatedCompetitorUrlsToTempTableCommand.execute() ends. ip=" + ip + ", ipAddress released domain id=" + domainId
						+ ", domain name=" + domainName + ",competitorUrlEntityList.size()=" + competitorUrlEntityList.size() + ",totalCompetitorUrlsNew="
						+ totalCompetitorUrlsNew + ",totalCompetitorUrlsExisting=" + totalCompetitorUrlsExisting + ", elapsed time in sec.:"
						+ (System.currentTimeMillis() - startTimestamp) / 1000);
		CacheModleFactory.getInstance().setAliveIpAddress(ip);
	}

	@Override
	protected void undo() throws Exception {
		// TODO Auto-generated method stub

	}
}
