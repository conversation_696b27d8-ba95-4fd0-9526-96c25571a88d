package com.actonia.put.messages;

import java.util.List;
import java.util.Properties;
import java.util.Set;

import org.apache.commons.lang.StringUtils;

import com.actonia.concurrency.CacheModleFactory;
import com.actonia.concurrency.ThreadPoolService;
import com.actonia.dao.OwnDomainEntityDAO;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.utils.CommonUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.PutMessageUtils;
import com.actonia.utils.SpringBeanFactory;

/***
 * 
 * target URLs in same domain but differ by just the trailing slash
 *
 */
public class FindTargetUrlsDifferByTrailingSlash {

	//private Boolean isDebug = false;

	private static ThreadPoolService threadPoolService = ThreadPoolService.getInstance();

	private OwnDomainEntityDAO ownDomainEntityDAO;

	public FindTargetUrlsDifferByTrailingSlash() {
		this.ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
	}

	public static void main(String args[]) {
		FormatUtils.getInstance().logMemoryUsage("main() begins.");
		long startTimestamp = System.currentTimeMillis();
		try {
			threadPoolService.init();
			CommonUtils.initThreads(30);
			new FindTargetUrlsDifferByTrailingSlash().process(args);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			threadPoolService.destroy();
		}
		FormatUtils.getInstance().logMemoryUsage("main() ends. elapsed time in sec.:" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private void process(String args[]) throws Exception {
		String domainIdsString = null;

		if (args != null) {

			// runtime parameter 1 (optional): list of domain IDs)
			if (args.length >= 1) {
				domainIdsString = args[0];
			}
		} else {
			FormatUtils.getInstance().logMemoryUsage("process() Must specify runtime parameters.");
			return;
		}
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameter:list of domain IDs=" + domainIdsString);

		Properties subserverProperties = new Properties();
		try {
			subserverProperties.load(FindTargetUrlsDifferByTrailingSlash.class.getResourceAsStream("/subserver.properties"));
		} catch (Exception e) {
			FormatUtils.getInstance().logMemoryUsage("process() no subserver.properties file found");
			return;
		}

		Boolean isExecDomainIdsInd = null;
		Set<Integer> runtimeDomainSet = null;

		Properties domainProperties = new Properties();
		try {
			domainProperties.load(FindTargetUrlsDifferByTrailingSlash.class.getResourceAsStream("/domain.properties"));
		} catch (Exception e) {
			FormatUtils.getInstance().logMemoryUsage("process() no domain.properties file found");
			return;
		}

		String execDomainIds = domainProperties.getProperty("exec.domain");
		String notExecDomainIds = domainProperties.getProperty("notexec.domain");
		if (StringUtils.isNotBlank(domainIdsString)) {
			execDomainIds = domainIdsString;
			notExecDomainIds = null;
		}
		FormatUtils.getInstance().logMemoryUsage("process() execDomainIds=" + execDomainIds);
		FormatUtils.getInstance().logMemoryUsage("process() notExecDomainIds=" + notExecDomainIds);

		List<OwnDomainEntity> allOwnDomainEntityList = ownDomainEntityDAO.queryForAll();
		if (allOwnDomainEntityList == null || allOwnDomainEntityList.size() == 0) {
			FormatUtils.getInstance().logMemoryUsage("process() allOwnDomainEntityList is empty.");
			return;
		}

		// process specific domains
		if (StringUtils.isNotBlank(execDomainIds)) {
			runtimeDomainSet = CommonUtils.getDomainSet(execDomainIds);
			isExecDomainIdsInd = true;
		}
		// do not process specific domains
		else if (StringUtils.isNotBlank(notExecDomainIds)) {
			runtimeDomainSet = CommonUtils.getDomainSet(notExecDomainIds);
			isExecDomainIdsInd = false;
		}
		// process all domains
		else {
			runtimeDomainSet = null;
			isExecDomainIdsInd = null;
		}

		List<OwnDomainEntity> filteredOwnDomainEntityList = PutMessageUtils.getInstance().filterDomains(allOwnDomainEntityList, isExecDomainIdsInd, runtimeDomainSet);
		if (filteredOwnDomainEntityList == null || filteredOwnDomainEntityList.size() == 0) {
			FormatUtils.getInstance().logMemoryUsage("process() filteredOwnDomainEntityList is empty.");
			return;
		}

		processDomainsConcurrently(filteredOwnDomainEntityList);
	}

	private void processDomainsConcurrently(List<OwnDomainEntity> ownDomainEntityList) {
		FormatUtils.getInstance().logMemoryUsage("processDomainsConcurrently() begins. ownDomainEntityList.size()=" + ownDomainEntityList.size());

		int totalNumberOfDomains = ownDomainEntityList.size();
		long startTimestamp = System.currentTimeMillis();
		String ipAddress = null;
		OwnDomainEntity ownDomainEntity = null;
		FindTargetUrlsDifferByTrailingSlashCommand findTargetUrlsDifferByTrailingSlashCommand = null;
		int numberOfDomainsProcessed = 0;

		do {
			ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
			if (ipAddress == null) {
				continue;
			}
			ownDomainEntity = ownDomainEntityList.get(numberOfDomainsProcessed++);
			findTargetUrlsDifferByTrailingSlashCommand = getFindTargetUrlsDifferByTrailingSlashCommand(ipAddress, ownDomainEntity);
			if (findTargetUrlsDifferByTrailingSlashCommand != null) {
				try {
					FormatUtils.getInstance().logMemoryUsage("processDomainsConcurrently() ipAddress acquired=" + ipAddress + ",domain=" + ownDomainEntity.getId()
							+ " - " + ownDomainEntity.getDomain());
					threadPoolService.execute(findTargetUrlsDifferByTrailingSlashCommand);
				} catch (Exception e) {
					e.printStackTrace();
				}
			} else {
				CacheModleFactory.getInstance().setAliveIpAddress(ipAddress);
			}
		} while (numberOfDomainsProcessed < totalNumberOfDomains);

		do {
			try {
				Thread.sleep(1000);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		} while (threadPoolService.getThreadPool().getActiveCount() > 0);
		FormatUtils.getInstance().logMemoryUsage("processDomainsConcurrently() end. elapsed time in seconds=" + ((System.currentTimeMillis() - startTimestamp) / 1000));
	}

	private FindTargetUrlsDifferByTrailingSlashCommand getFindTargetUrlsDifferByTrailingSlashCommand(String ip, OwnDomainEntity ownDomainEntity) {
		FindTargetUrlsDifferByTrailingSlashCommand findTargetUrlsDifferByTrailingSlashCommand = new FindTargetUrlsDifferByTrailingSlashCommand(ip, ownDomainEntity);
		findTargetUrlsDifferByTrailingSlashCommand.setStatus(true);
		return findTargetUrlsDifferByTrailingSlashCommand;
	}
}
