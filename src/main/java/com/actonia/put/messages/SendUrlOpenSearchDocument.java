package com.actonia.put.messages;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * for now we need those columns
 * url (Strig text)
 * ownDomainId (int)
 * nextFetchDate (date time)
 * urlId(0 for ranking URL)
 * urlMurmurHash
 * batchDate(yyyyMMdd)
 * processFlg(0: not processed, 2: processed)
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SendUrlOpenSearchDocument {
    private String id;
    private String url;
    private int ownDomainId;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date nextFetchDate = new Date();
    private int urlId = 0;
    private String urlMurmurHash;
    private Date processEndTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date sendTime = new Date();

    // The ID generation method for documents sent to opensearch: org.apache.commons.codec.digest.DigestUtils.sha256Hex(url + domainId); eg: sha256Hex("www.baidu.com" + 4)
    public final String generateId() {
        return org.apache.commons.codec.digest.DigestUtils.sha256Hex(this.url + this.ownDomainId);
    }

}
