package com.actonia.put.messages;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.concurrency.ThreadPoolService;
import com.actonia.dao.AssociatedUrlEntityDAO;
import com.actonia.dao.CompetitorMsgClickHouseDAO;
import com.actonia.dao.CompetitorUrlClickHouseDAO;
import com.actonia.dao.CompetitorUrlHtmlClickHouseDAO;
import com.actonia.dao.CompetitorUrlWeeklyCrawlTrackingEntityDAO;
import com.actonia.dao.LocalCompetitorMsgClickHouseDAO;
import com.actonia.dao.LocalCompetitorUrlClickHouseDAO;
import com.actonia.dao.OwnDomainEntityDAO;
import com.actonia.dao.TempCompetitorUrlsHtmlEntityDAO;
import com.actonia.entity.AssociatedUrlEntity;
import com.actonia.entity.CompetitorMsgClickHouseEntity;
import com.actonia.entity.CompetitorUrlClickHouseEntity;
import com.actonia.entity.CompetitorUrlWeeklyCrawlTrackingEntity;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.entity.UrlMetricsEntityV3;
import com.actonia.utils.CommonUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.PutMessageUtils;
import com.actonia.utils.SQSUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.UrlCrawlParametersVO;
import com.google.gson.Gson;

/***
 *
 * Retrieves associated URLs of each domain and put the URL strings to queues (html and shared counts) by domain.
 *
 */
public class TestSendAssociatedUrlsToCrawlQueuesByDomain {

	private static Boolean isDebug = true; //debug

	private static ThreadPoolService threadPoolService = ThreadPoolService.getInstance();

	private OwnDomainEntityDAO ownDomainEntityDAO;

	private AssociatedUrlEntityDAO associatedUrlEntityDAO;

	private TempCompetitorUrlsHtmlEntityDAO tempCompetitorUrlsHtmlEntityDAO;

	private String processType = null;

	private CompetitorUrlWeeklyCrawlTrackingEntityDAO competitorUrlWeeklyCrawlTrackingEntityDAO;

	private static final int SLEEP_TIME_15_SECONDS = 15 * 1000;

	private static ConcurrentMap<String, Integer> concurrentMap;

	// map key = queue number
	// map value = queue depth
	private Map<Integer, Integer> queueNumberQueueDepthMap = new HashMap<Integer, Integer>();

	private int totalUrlsCreatedInCompetitorUrlClickHouseTable = 0;

	private static final Integer threadsPerQueue = 1;

	private static final Boolean isProcessWeekly = true;

	private static final Boolean isProcessDaily = false;

	private static final Boolean isCreateQueue = true;

	private static final boolean isDeleteQueueOnly = false;

	private static final Integer DECIMAL_PRECISION = 4;
	private static final BigDecimal NINETY_FIVE_PERCENT_IN_DECIMAL = new BigDecimal("95.00").setScale(0, RoundingMode.HALF_UP);

	private static final int MAX_COMPETITOR_URLS_PER_DOMAIN = 1000000;

	private static List<String> expediaDomainList = new ArrayList<String>();

	public TestSendAssociatedUrlsToCrawlQueuesByDomain() {
		concurrentMap = new ConcurrentHashMap<String, Integer>();
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		associatedUrlEntityDAO = SpringBeanFactory.getBean("associatedUrlEntityDAO");
		tempCompetitorUrlsHtmlEntityDAO = SpringBeanFactory.getBean("tempCompetitorUrlsHtmlEntityDAO");
		competitorUrlWeeklyCrawlTrackingEntityDAO = SpringBeanFactory.getBean("competitorUrlWeeklyCrawlTrackingEntityDAO");
	}

	public static void main(String args[]) {
		FormatUtils.getInstance().logMemoryUsage("main() begins.");
		long startTimestamp = System.currentTimeMillis();
		try {
			threadPoolService.init();
			new TestSendAssociatedUrlsToCrawlQueuesByDomain().process(args);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			threadPoolService.destroy();
		}
		FormatUtils.getInstance().logMemoryUsage("main() ends. elapsed time in sec.:" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private void process(String args[]) throws Exception {
		String crawlType = null;
		String totalCrawlQueuesString = null;
		Integer trackDateNumber = null;
		String stepNumberString = null;
		int stepNumber = 0;
		int totalCrawlQueues = 0;

		if (args != null && args.length > 0) {

			// runtime argument: process type (solr/clickhouse)
			if (args.length >= 1) {
				processType = args[0];
				if (StringUtils.isNotBlank(processType)) {
					if (!StringUtils.equalsIgnoreCase(processType, IConstants.PROCESS_TYPE_SOLR)
							&& !StringUtils.equalsIgnoreCase(processType, IConstants.PROCESS_TYPE_CLICKHOUSE)) {
						FormatUtils.getInstance().logMemoryUsage("process() Must specify a valid data store in runtime arguments: 'solr', or 'clickhouse'.");
						return;
					}
				} else {
					FormatUtils.getInstance().logMemoryUsage("process() Must specify a valid data store in runtime arguments: 'solr', or 'clickhouse'.");
					return;
				}
			}

			// runtime argument (required): number of queues
			if (args.length >= 2) {
				totalCrawlQueuesString = args[1];
				if (StringUtils.isNotBlank(totalCrawlQueuesString)) {
					totalCrawlQueues = NumberUtils.toInt(totalCrawlQueuesString);
				}
			}

			// runtime argument (required for 'clickhouse' process type): step number
			if (StringUtils.equalsIgnoreCase(processType, IConstants.PROCESS_TYPE_CLICKHOUSE)) {
				if (args.length >= 3) {
					stepNumberString = args[2];
					if (StringUtils.isNotBlank(stepNumberString)) {
						stepNumber = NumberUtils.toInt(stepNumberString);
						if (stepNumber < 1 || stepNumber > 3) {
							FormatUtils.getInstance().logMemoryUsage("process() Must specify a step number 1, 2, or 3 for clickhouse process type.");
							return;
						}
					}
				} else {
					FormatUtils.getInstance().logMemoryUsage("process() Must specify a step number 1, 2, or 3 for clickhouse process type.");
					return;
				}
			}
		} else {
			FormatUtils.getInstance().logMemoryUsage("process() must specify runtime arguments.");
			System.exit(-1);
		}

		FormatUtils.getInstance().logMemoryUsage("process() runtime argument:processType=" + processType);
		if (PutMessageUtils.getInstance().isProcessTypeSolr(processType) == true) {
			CommonUtils.initThreads(6);
		} else {
			CommonUtils.initThreads(6);
		}
		FormatUtils.getInstance().logMemoryUsage("process() runtime argument:crawl type=" + crawlType);
		FormatUtils.getInstance().logMemoryUsage("process() runtime argument:number of queues=" + totalCrawlQueues);
		FormatUtils.getInstance().logMemoryUsage("process() runtime argument:stepNumber=" + stepNumber);

		// by default, track date is today's date
		trackDateNumber = NumberUtils.toInt(DateFormatUtils.format(new Date(), IConstants.DATE_FORMAT_YYYYMMDD));
		FormatUtils.getInstance().logMemoryUsage("track date=" + trackDateNumber);

		Properties domainProperties = new Properties();
		try {
			domainProperties.load(TestSendAssociatedUrlsToCrawlQueuesByDomain.class.getResourceAsStream("/domain.properties"));
		} catch (Exception e) {
			FormatUtils.getInstance().logMemoryUsage("process() no domain.properties file found");
			System.exit(-1);
		}

		Properties subserverProperties = new Properties();
		try {
			subserverProperties.load(TestSendAssociatedUrlsToCrawlQueuesByDomain.class.getResourceAsStream("/subserver.properties"));
		} catch (Exception e) {
			FormatUtils.getInstance().logMemoryUsage("process() no subserver.properties file found");
			System.exit(-1);
		}

		CompetitorUrlHtmlClickHouseDAO.getInstance();
		PutMessageUtils.getInstance();
		SQSUtils.getInstance();
		expediaDomainList = PutMessageUtils.getInstance().getExpediaDomainNames();

		String execDomainIds = domainProperties.getProperty("exec.domain");
		String notExecDomainIds = domainProperties.getProperty("notexec.domain");
		FormatUtils.getInstance().logMemoryUsage("process() domain.properties:execDomainIds=" + execDomainIds);
		FormatUtils.getInstance().logMemoryUsage("process() domain.properties:notExecDomainIds=" + notExecDomainIds);

		// when 'clickhouse', check if ready to put messages to competitor URLs weekly crawl HTML queues
		boolean isReady = false;
		if (PutMessageUtils.getInstance().isProcessTypeClickHouse(processType) == true) {
			if (isDebug == true) {
				isReady = PutMessageUtils.getInstance().checkIfReadyToPutMessages(IConstants.PROCESS_TYPE_TEST, totalCrawlQueues);
			} else {
				isReady = PutMessageUtils.getInstance().checkIfReadyToPutMessages(processType, totalCrawlQueues);
			}
			if (isReady == false) {
				FormatUtils.getInstance().logMemoryUsage("process() skip processing, processType=" + processType + ",stepNumber=" + stepNumber + ",isReady=" + isReady);
				return;
			}
		}

		// when 'solr' or ('clickhouse' and step 1), then update the temporary table
		if (PutMessageUtils.getInstance().isProcessTypeSolr(processType) == true
				|| (PutMessageUtils.getInstance().isProcessTypeClickHouse(processType) == true && stepNumber == 1)) {
			updateTemporaryTable(trackDateNumber, execDomainIds, notExecDomainIds);
		}

		if (PutMessageUtils.getInstance().isProcessTypeClickHouse(processType) == true) {
			// when 'clickhouse' and step 1
			if (stepNumber == 1) {
				writeCompetitorUrlToClickHouseTable(trackDateNumber);
			} else if (stepNumber == 2) {
				writeCompetitorMsgToClickHouseTable();
			} else if (stepNumber == 3) {
				putMessagesToQueues(trackDateNumber, totalCrawlQueues);
			}
		} else {
			putMessagesToQueues(trackDateNumber, totalCrawlQueues);
		}
	}

	private void updateTemporaryTable(int trackDateNumber, String execDomainIds, String notExecDomainIds) throws Exception {

		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("updateTemporaryTable() begins.");

		Boolean isExecDomainIdsInd = null;
		Set<Integer> runtimeDomainSet = null;
		boolean isJapaneseDomains = false;
		List<OwnDomainEntity> allOwnDomainEntityList = null;

		// process only specific domains
		if (StringUtils.isNotBlank(execDomainIds)) {
			runtimeDomainSet = CommonUtils.getDomainSet(execDomainIds);
			isExecDomainIdsInd = true;
		}
		// do not process specific domains
		else if (StringUtils.isNotBlank(notExecDomainIds)) {
			runtimeDomainSet = CommonUtils.getDomainSet(notExecDomainIds);
			isExecDomainIdsInd = false;
		}
		// process all domains
		else {
			runtimeDomainSet = null;
			isExecDomainIdsInd = null;
		}

		if (isDebug == false) {
			// when weekly processing, initialize the temporary table that store the unique associated URL strings.
			if (isProcessWeekly == true) {
				initializeTemporaryTables();
			}
			// when daily processing, cache all the existing hash codes stored in the temporary table for duplicate checks
			else if (isProcessDaily == true) {
				cacheHashCodesFromTemporaryTables();
			}
		}

		// process Japanese domains
		allOwnDomainEntityList = ownDomainEntityDAO.getDomainsByLanguage(IConstants.JA);
		isJapaneseDomains = true;

		if (allOwnDomainEntityList != null && allOwnDomainEntityList.size() > 0) {
			sendAssociatedCompetitorUrlsToTemporaryTableConcurrently(allOwnDomainEntityList, isExecDomainIdsInd, runtimeDomainSet, trackDateNumber, isJapaneseDomains);
			sendNonAssociatedCompetitorUrlsToTemporaryTable(allOwnDomainEntityList, isExecDomainIdsInd, runtimeDomainSet, trackDateNumber, isJapaneseDomains);
		}

		// process non-Japanese domains
		allOwnDomainEntityList = ownDomainEntityDAO.getDomainsByNotLanguage(IConstants.JA);
		isJapaneseDomains = false;

		if (allOwnDomainEntityList != null && allOwnDomainEntityList.size() > 0) {
			sendAssociatedCompetitorUrlsToTemporaryTableConcurrently(allOwnDomainEntityList, isExecDomainIdsInd, runtimeDomainSet, trackDateNumber, isJapaneseDomains);
			sendNonAssociatedCompetitorUrlsToTemporaryTable(allOwnDomainEntityList, isExecDomainIdsInd, runtimeDomainSet, trackDateNumber, isJapaneseDomains);
		}

		FormatUtils.getInstance().logMemoryUsage("updateTemporaryTable() ends. elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);

	}

	private void writeCompetitorUrlToClickHouseTable(int trackDateNumber) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("writeCompetitorUrlToClickHouseTable() begins.");
		int totalHostnamesToBeProcessed = 0;
		int totalHostnamesProcessed = 0;
		int totalUrls = 0;
		int grandTotalUrls = 0;
		List<String> hostnameList = new ArrayList<String>();
		List<AssociatedUrlEntity> associatedUrlEntityList = tempCompetitorUrlsHtmlEntityDAO.getTotalUrlsByHostname(trackDateNumber);
		if (associatedUrlEntityList == null || associatedUrlEntityList.size() == 0) {
			FormatUtils.getInstance().logMemoryUsage("writeCompetitorUrlToClickHouseTable() no hostname can be retrieved.");
			return;
		}

		LocalCompetitorUrlClickHouseDAO.getInstance().resetTable(null);

		totalHostnamesToBeProcessed = associatedUrlEntityList.size();
		FormatUtils.getInstance().logMemoryUsage("writeCompetitorUrlToClickHouseTable() totalHostnamesToBeProcessed=" + totalHostnamesToBeProcessed);

		for (AssociatedUrlEntity associatedUrlEntity : associatedUrlEntityList) {
			totalHostnamesProcessed++;
			hostnameList.add(associatedUrlEntity.getHostname());
			totalUrls = associatedUrlEntity.getTotalUrls();
			//FormatUtils.getInstance().logMemoryUsage(
			//		"writeCompetitorUrlToClickHouseTable() hostname=" + associatedUrlEntity.getHostname() + ",totalUrls=" + associatedUrlEntity.getTotalUrls());
			grandTotalUrls = grandTotalUrls + totalUrls;
			if (grandTotalUrls >= CompetitorUrlClickHouseDAO.getInstance().getBatchCreationSize()) {
				//FormatUtils.getInstance().logMemoryUsage("writeCompetitorUrlToClickHouseTable() totalHostnamesToBeProcessed=" + totalHostnamesToBeProcessed
				//		+ ",totalHostnamesProcessed=" + totalHostnamesProcessed);
				writeCompetitorUrlInHostNameListToClickHouseTable(trackDateNumber, hostnameList);
				hostnameList = new ArrayList<String>();
			}
		}
		if (hostnameList != null && hostnameList.size() > 0) {
			//FormatUtils.getInstance().logMemoryUsage("writeCompetitorUrlToClickHouseTable() totalHostnamesToBeProcessed=" + totalHostnamesToBeProcessed
			//		+ ",totalHostnamesProcessed=" + totalHostnamesProcessed);
			writeCompetitorUrlInHostNameListToClickHouseTable(trackDateNumber, hostnameList);
			hostnameList = new ArrayList<String>();
		}

		// reset the cache
		tempCompetitorUrlsHtmlEntityDAO.resetCache();

		FormatUtils.getInstance()
				.logMemoryUsage("writeCompetitorUrlToClickHouseTable() end. totalHostnamesProcessed=" + totalHostnamesProcessed
						+ ",totalUrlsCreatedInCompetitorUrlClickHouseTable=" + totalUrlsCreatedInCompetitorUrlClickHouseTable + ",elapsed(s.)="
						+ ((System.currentTimeMillis() - startTimestamp) / 1000));
	}

	private void writeCompetitorUrlInHostNameListToClickHouseTable(int trackDateNumber, List<String> hostnameList) throws Exception {
		//long startTimestamp = System.currentTimeMillis();
		//FormatUtils.getInstance().logMemoryUsage("writeCompetitorUrlInHostNameListToClickHouseTable() begins. hostnameList=" + hostnameList.toString());
		List<CompetitorUrlClickHouseEntity> competitorUrlClickHouseEntityList = new ArrayList<CompetitorUrlClickHouseEntity>();
		CompetitorUrlClickHouseEntity competitorUrlClickHouseEntity = null;
		String urlString = null;
		String reversedUrlDomain = null;
		Date trackDate = DateUtils.parseDate(String.valueOf(trackDateNumber), new String[] { IConstants.DATE_FORMAT_YYYYMMDD });
		List<AssociatedUrlEntity> associatedUrlEntityList = new ArrayList<AssociatedUrlEntity>();
		for (String hostname : hostnameList) {
			associatedUrlEntityList = tempCompetitorUrlsHtmlEntityDAO.getUrlsByHostname(hostname, trackDateNumber);
			//FormatUtils.getInstance()
			//		.logMemoryUsage("writeCompetitorUrlToClickHouseTable() hostname=" + hostname + ",associatedUrlEntityList.size()=" + associatedUrlEntityList.size());
			reversedUrlDomain = PutMessageUtils.getInstance().getReversedDomainName(hostname);
			for (AssociatedUrlEntity associatedUrlEntity : associatedUrlEntityList) {
				urlString = associatedUrlEntity.getUrl();
				totalUrlsCreatedInCompetitorUrlClickHouseTable++;
				if (totalUrlsCreatedInCompetitorUrlClickHouseTable % 100000 == 0) {
					FormatUtils.getInstance().logMemoryUsage("writeCompetitorUrlInHostNameListToClickHouseTable() totalUrlsCreatedInCompetitorUrlClickHouseTable="
							+ totalUrlsCreatedInCompetitorUrlClickHouseTable);
				}
				competitorUrlClickHouseEntity = new CompetitorUrlClickHouseEntity();
				competitorUrlClickHouseEntity.setTrack_date(trackDate);
				competitorUrlClickHouseEntity.setUrl_domain(reversedUrlDomain);
				competitorUrlClickHouseEntity.setUrl(urlString);
				competitorUrlClickHouseEntityList.add(competitorUrlClickHouseEntity);
				if (competitorUrlClickHouseEntityList.size() >= CompetitorUrlClickHouseDAO.getInstance().getBatchCreationSize()) {
					CompetitorUrlClickHouseDAO.getInstance().createBatch(competitorUrlClickHouseEntityList, null);
					competitorUrlClickHouseEntityList = new ArrayList<CompetitorUrlClickHouseEntity>();
				}
			}
		}
		if (competitorUrlClickHouseEntityList.size() > 0) {
			CompetitorUrlClickHouseDAO.getInstance().createBatch(competitorUrlClickHouseEntityList, null);
			competitorUrlClickHouseEntityList = new ArrayList<CompetitorUrlClickHouseEntity>();
		}
		removeFromCache(hostnameList);
		//FormatUtils.getInstance()
		//		.logMemoryUsage("writeCompetitorUrlInHostNameListToClickHouseTable() ends. elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private void writeCompetitorMsgToClickHouseTable() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		Date trackDate = CompetitorUrlClickHouseDAO.getInstance().getTrackDate(null);
		FormatUtils.getInstance()
				.logMemoryUsage("writeCompetitorMsgToClickHouseTable() begins. trackDate=" + DateFormatUtils.format(trackDate, IConstants.DATE_FORMAT_YYYY_MM_DD));
		int totalHostnamesToBeProcessed = 0;
		int totalHostnamesProcessed = 0;

		List<CompetitorUrlClickHouseEntity> domainTotalRecordsList = CompetitorUrlClickHouseDAO.getInstance().getTotalRecordsByDomain(trackDate, null);
		if (domainTotalRecordsList == null || domainTotalRecordsList.size() == 0) {
			FormatUtils.getInstance().logMemoryUsage("writeCompetitorMsgToClickHouseTable() no hostname can be retrieved.");
			return;
		}
		int grandTotalCompetitorUrls = 0;
		List<String> reversedUrlDomainList = new ArrayList<String>();

		LocalCompetitorMsgClickHouseDAO.getInstance().resetTable(null);

		totalHostnamesToBeProcessed = domainTotalRecordsList.size();
		FormatUtils.getInstance().logMemoryUsage("writeCompetitorMsgToClickHouseTable() totalHostnamesToBeProcessed=" + totalHostnamesToBeProcessed);

		nextDomainTotalRecords: for (CompetitorUrlClickHouseEntity domainTotalRecords : domainTotalRecordsList) {
			totalHostnamesProcessed++;
			if (totalHostnamesProcessed % 10000 == 0) {
				FormatUtils.getInstance().logMemoryUsage("writeCompetitorMsgToClickHouseTable() totalHostnamesProcessed=" + totalHostnamesProcessed
						+ ",totalHostnamesToBeProcessed=" + totalHostnamesToBeProcessed);
			}
			if (domainTotalRecords.getTotal_records() > MAX_COMPETITOR_URLS_PER_DOMAIN) {
				FormatUtils.getInstance().logMemoryUsage("writeCompetitorMsgToClickHouseTable() skip " + ",domainTotalRecords.getUrl_domain()="
						+ domainTotalRecords.getUrl_domain() + ",domainTotalRecords.getTotal_records()=" + domainTotalRecords.getTotal_records());
				continue nextDomainTotalRecords;
			}
			reversedUrlDomainList.add(domainTotalRecords.getUrl_domain());
			grandTotalCompetitorUrls = grandTotalCompetitorUrls + domainTotalRecords.getTotal_records();
			if (grandTotalCompetitorUrls < 100000 && reversedUrlDomainList.size() < 500) {
				continue nextDomainTotalRecords;
			}
			FormatUtils.getInstance().logMemoryUsage("writeCompetitorMsgToClickHouseTable() reversedUrlDomainList=" + reversedUrlDomainList.toString()
					+ ",grandTotalCompetitorUrls=" + grandTotalCompetitorUrls);
			processReversedUrlDomains(reversedUrlDomainList, trackDate);
			grandTotalCompetitorUrls = 0;
			reversedUrlDomainList = new ArrayList<String>();
		}
		if (reversedUrlDomainList.size() > 0) {
			processReversedUrlDomains(reversedUrlDomainList, trackDate);
			grandTotalCompetitorUrls = 0;
			reversedUrlDomainList = new ArrayList<String>();
		}
		FormatUtils.getInstance().logMemoryUsage("writeCompetitorMsgToClickHouseTable() end. totalHostnamesProcessed=" + totalHostnamesProcessed + ",elapsed(s.)="
				+ ((System.currentTimeMillis() - startTimestamp) / 1000));
	}

	private void processReversedUrlDomains(List<String> reversedUrlDomainList, Date trackDate) throws Exception {

		// map key = competitor URL MD5 hash code
		// map value = competitor URL's daily HtmlClickHouseEntity
		Map<String, HtmlClickHouseEntity> htmlClickHouseEntityMap = new HashMap<String, HtmlClickHouseEntity>();

		UrlMetricsEntityV3 urlMetricsEntityV3 = null;

		int totalRecordsCreated = 0;

		String messageBody = null;
		String reversedUrlDomain = null;

		CompetitorMsgClickHouseEntity competitorMsgClickHouseEntity = null;

		List<CompetitorMsgClickHouseEntity> competitorMsgClickHouseEntityList = new ArrayList<CompetitorMsgClickHouseEntity>();

		htmlClickHouseEntityMap = PutMessageUtils.getInstance().getCompetitorUrlHtmlClickHouseEntityMap(null, reversedUrlDomainList);
		FormatUtils.getInstance().logMemoryUsage("processReversedUrlDomains() reversedUrlDomainList=" + reversedUrlDomainList.toString()
				+ ",htmlClickHouseEntityMap.size()=" + htmlClickHouseEntityMap.size());
		List<CompetitorUrlClickHouseEntity> competitorUrlClickHouseEntityList = CompetitorUrlClickHouseDAO.getInstance().getUrlsByDomain(trackDate,
				reversedUrlDomainList, null);
		FormatUtils.getInstance().logMemoryUsage("processReversedUrlDomains() reversedUrlDomainList=" + reversedUrlDomainList.toString()
				+ ",competitorUrlClickHouseEntityList.size()=" + competitorUrlClickHouseEntityList.size());
		if (competitorUrlClickHouseEntityList != null && competitorUrlClickHouseEntityList.size() > 0) {
			nextCompetitorUrlClickHouseEntity: for (CompetitorUrlClickHouseEntity competitorUrlClickHouseEntity : competitorUrlClickHouseEntityList) {
				reversedUrlDomain = PutMessageUtils.getInstance().extractReversedUrlDomain(competitorUrlClickHouseEntity.getUrl());
				if (StringUtils.isBlank(reversedUrlDomain)) {
					continue nextCompetitorUrlClickHouseEntity;
				}
				urlMetricsEntityV3 = new UrlMetricsEntityV3();
				urlMetricsEntityV3.setUrl(competitorUrlClickHouseEntity.getUrl());
				// update 'urlMetricsEntityV3' (pass by reference)
				PutMessageUtils.getInstance().updateUrlMetricsEntityWithHistoricalCrawledData(IConstants.EMPTY_STRING, IConstants.CRAWL_TYPE_COMPETITOR_URL_HTML,
						urlMetricsEntityV3, htmlClickHouseEntityMap);
				messageBody = new Gson().toJson(urlMetricsEntityV3);
				competitorMsgClickHouseEntity = new CompetitorMsgClickHouseEntity();
				competitorMsgClickHouseEntity.setTrack_date(trackDate);
				competitorMsgClickHouseEntity.setUrl_domain(reversedUrlDomain);
				competitorMsgClickHouseEntity.setUrl(competitorUrlClickHouseEntity.getUrl());
				competitorMsgClickHouseEntity.setMsg(messageBody);
				competitorMsgClickHouseEntityList.add(competitorMsgClickHouseEntity);
				totalRecordsCreated++;
				if (totalRecordsCreated % 100000 == 0) {
					FormatUtils.getInstance().logMemoryUsage(
							"processReversedUrlDomains() reversedUrlDomainList=" + reversedUrlDomainList.toString() + ",totalRecordsCreated=" + totalRecordsCreated);
				}
				if (competitorMsgClickHouseEntityList.size() >= CompetitorMsgClickHouseDAO.getInstance().getBatchCreationSize()) {
					CompetitorMsgClickHouseDAO.getInstance().createBatch(competitorMsgClickHouseEntityList, null);
					competitorMsgClickHouseEntityList = new ArrayList<CompetitorMsgClickHouseEntity>();
				}
			}
		}
		if (competitorMsgClickHouseEntityList.size() > 0) {
			CompetitorMsgClickHouseDAO.getInstance().createBatch(competitorMsgClickHouseEntityList, null);
			competitorMsgClickHouseEntityList = new ArrayList<CompetitorMsgClickHouseEntity>();
		}
		FormatUtils.getInstance().logMemoryUsage(
				"processReversedUrlDomains() ends. reversedUrlDomainList=" + reversedUrlDomainList.toString() + ",totalRecordsCreated=" + totalRecordsCreated);
	}

	private void removeFromCache(List<String> hostnameList) throws Exception {
		if (hostnameList != null && hostnameList.size() > 0) {
			for (String hostname : hostnameList) {
				if (PutMessageUtils.getInstance().isProcessTypeSolr(processType) == true) {
					associatedUrlEntityDAO.removeFromCache(hostname);
				} else {
					tempCompetitorUrlsHtmlEntityDAO.removeFromCache(hostname);
				}
			}
		}
	}

	private List<String> putMessagesToQueues(int trackDateNumber, int totalCrawlQueues) throws Exception {

		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("putMessagesToQueues() begins.");

		if (isDebug == false) {
			if (isProcessWeekly == true) {
				competitorUrlWeeklyCrawlTrackingEntityDAO.resetTable(IConstants.PROCESS_TYPE_NUMBER_CLICKHOUSE);
			}
		}

		int minimumTotalUrls = 0;
		int maximumTotalUrls = 0;

		reInitializeThreadsPool(8);

		List<String> queueUrlList = initializeQueuesConcurrently(totalCrawlQueues);
		if (queueUrlList != null && queueUrlList.size() > 0) {

			minimumTotalUrls = 0;
			maximumTotalUrls = MAX_COMPETITOR_URLS_PER_DOMAIN;
			putMessagesToPoliteCrawlQueuesConcurrently(queueUrlList, trackDateNumber, minimumTotalUrls, maximumTotalUrls);

			//reInitializeThreadsPool(1);
			//minimumTotalUrls = 88167;
			//maximumTotalUrls = Integer.MAX_VALUE;
			//putMessagesToPoliteCrawlQueuesConcurrently(queueUrlList, trackDateNumber, minimumTotalUrls, maximumTotalUrls);

		} else {
			FormatUtils.getInstance().logMemoryUsage("putMessagesToQueues() crawl queues not initialized..");
		}
		//sendDomainCrawlDataToControllerQueue(totalCrawlQueues);

		FormatUtils.getInstance().logMemoryUsage("putMessagesToQueues() ends. elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		return queueUrlList;
	}

	private void initializeTemporaryTables() throws Exception {
		FormatUtils.getInstance().logMemoryUsage("initializeTemporaryTables() begins.");
		if (PutMessageUtils.getInstance().isProcessTypeSolr(processType) == true) {
			associatedUrlEntityDAO.resetTable();
		} else {
			tempCompetitorUrlsHtmlEntityDAO.resetTable();
		}
		FormatUtils.getInstance().logMemoryUsage("initializeTemporaryTables() ends.");
	}

	private void cacheHashCodesFromTemporaryTables() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("cacheHashCodesFromTemporaryTables() begins.");
		ConcurrentMap<String, Integer> concurrentMap = null;
		if (PutMessageUtils.getInstance().isProcessTypeSolr(processType) == true) {
			concurrentMap = associatedUrlEntityDAO.getAllHashCodes();
		} else {
			concurrentMap = tempCompetitorUrlsHtmlEntityDAO.getAllHashCodes();
		}
		if (concurrentMap != null && concurrentMap.size() > 0) {
			setConcurrentMap(concurrentMap);
		}
		FormatUtils.getInstance()
				.logMemoryUsage("cacheHashCodesFromTemporaryTables() ends. elapsed time in sec.=" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private List<String> initializeQueuesConcurrently(int totalCrawlQueues) throws Exception {
		long startTimestamp = System.currentTimeMillis();

		String queueNamePrefix = null;
		if (isDebug == true) {
			if (isProcessWeekly == true || isProcessDaily == true) {
				queueNamePrefix = IConstants.QUEUE_NAME_TEST_COMPETITOR_URL_HTML_PREFIX;
			}
		} else {
			if (isProcessWeekly == true || isProcessDaily == true) {
				if (PutMessageUtils.getInstance().isProcessTypeSolr(processType) == true) {
					queueNamePrefix = IConstants.QUEUE_NAME_SOLR_COMPETITOR_URL_HTML_PREFIX;
				} else {
					queueNamePrefix = IConstants.QUEUE_NAME_COMPETITOR_URL_HTML_PREFIX;
				}
			}
		}

		FormatUtils.getInstance()
				.logMemoryUsage("initializeQueuesConcurrently() begins. totalCrawlQueues=" + totalCrawlQueues + ", queueNamePrefix=" + queueNamePrefix);
		List<String> output = null;
		String ipAddress = null;
		int queueNumber = 0;
		String queueName = null;
		InitializeUrlCrawlQueueCommand initializeUrlCrawlQueueCommand = null;
		CacheModleFactory.getInstance().setUrlCrawlQueueList(new ArrayList<String>());
		if (totalCrawlQueues > 0) {
			do {
				ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
				if (ipAddress == null) {
					continue;
				}
				queueNumber++;
				queueName = queueNamePrefix + queueNumber;
				initializeUrlCrawlQueueCommand = getInitializeUrlCrawlQueueCommand(ipAddress, queueName);
				if (initializeUrlCrawlQueueCommand != null) {
					try {
						FormatUtils.getInstance().logMemoryUsage("initializeQueuesConcurrently() ipAddress acquired=" + ipAddress + ",queueName=" + queueName);
						threadPoolService.execute(initializeUrlCrawlQueueCommand);
					} catch (Exception e) {
						e.printStackTrace();
					}
				} else {
					CacheModleFactory.getInstance().setAliveIpAddress(ipAddress);
				}
			} while (queueNumber < totalCrawlQueues);

			do {
				try {
					Thread.sleep(1000);
				} catch (InterruptedException e) {
					e.printStackTrace();
				}
			} while (threadPoolService.getThreadPool().getActiveCount() > 0);
			output = CacheModleFactory.getInstance().getUrlCrawlQueueList();
			if (output == null || output.size() == 0) {
				FormatUtils.getInstance().logMemoryUsage("initializeQueuesConcurrently() output is empty.");
			}
		}
		FormatUtils.getInstance()
				.logMemoryUsage("initializeQueuesConcurrently() ends. total elapsed time in seconds.=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		return output;
	}

	// using concurrent threads (one thread per domain) to put associated competitor URL strings in the temporary table
	private void sendAssociatedCompetitorUrlsToTemporaryTableConcurrently(List<OwnDomainEntity> allOwnDomainEntityList, Boolean isExecDomainIdsInd,
			Set<Integer> runtimeDomainSet, int trackDateNumber, boolean isJapaneseDomains) {
		FormatUtils.getInstance().logMemoryUsage("sendAssociatedCompetitorUrlsToTemporaryTableConcurrently() begins. isJapaneseDomains=" + isJapaneseDomains);
		long startTimestamp = System.currentTimeMillis();
		SendAssociatedCompetitorUrlsToTempTableCommand sendAssociatedCompetitorUrlsToTempTableCommand = null;
		int totalNumberOfDomains = allOwnDomainEntityList.size();
		String ipAddress = null;
		OwnDomainEntity ownDomainEntity = null;
		int numberOfDomainsProcessed = 0;

		do {
			ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
			if (ipAddress == null) {
				continue;
			}
			ownDomainEntity = allOwnDomainEntityList.get(numberOfDomainsProcessed++);
			sendAssociatedCompetitorUrlsToTempTableCommand = null;
			if (isExecDomainIdsInd == null) {
				sendAssociatedCompetitorUrlsToTempTableCommand = getSendAssociatedCompetitorUrlsToTempTableCommand(ipAddress, ownDomainEntity, trackDateNumber,
						isJapaneseDomains);
			} else if (isExecDomainIdsInd == true) {
				if (runtimeDomainSet.contains(ownDomainEntity.getId())) {
					sendAssociatedCompetitorUrlsToTempTableCommand = getSendAssociatedCompetitorUrlsToTempTableCommand(ipAddress, ownDomainEntity, trackDateNumber,
							isJapaneseDomains);
				}
			} else if (isExecDomainIdsInd == false) {
				if (!runtimeDomainSet.contains(ownDomainEntity.getId())) {
					sendAssociatedCompetitorUrlsToTempTableCommand = getSendAssociatedCompetitorUrlsToTempTableCommand(ipAddress, ownDomainEntity, trackDateNumber,
							isJapaneseDomains);
				}
			}
			if (sendAssociatedCompetitorUrlsToTempTableCommand != null) {
				try {
					FormatUtils.getInstance().logMemoryUsage("sendAssociatedCompetitorUrlsToTemporaryTableConcurrently() ipAddress acquired=" + ipAddress + ",domain="
							+ ownDomainEntity.getId() + " - " + ownDomainEntity.getDomain());
					threadPoolService.execute(sendAssociatedCompetitorUrlsToTempTableCommand);
				} catch (Exception e) {
					e.printStackTrace();
				}
			} else {
				CacheModleFactory.getInstance().setAliveIpAddress(ipAddress);
			}
		} while (numberOfDomainsProcessed < totalNumberOfDomains);

		do {
			try {
				Thread.sleep(1000);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		} while (threadPoolService.getThreadPool().getActiveCount() > 0);
		FormatUtils.getInstance().logMemoryUsage(
				"sendAssociatedCompetitorUrlsToTemporaryTableConcurrently() end. elapsed time in seconds=" + ((System.currentTimeMillis() - startTimestamp) / 1000));
	}

	// using concurrent threads (one thread per domain) to put non-associated competitor URL strings in the temporary table
	private void sendNonAssociatedCompetitorUrlsToTemporaryTable(List<OwnDomainEntity> allOwnDomainEntityList, Boolean isExecDomainIdsInd,
			Set<Integer> runtimeDomainSet, int trackDateNumber, boolean isJapaneseDomains) {
		FormatUtils.getInstance().logMemoryUsage("sendAssociatedCompetitorUrlsToTemporaryTableConcurrently() begins. isJapaneseDomains=" + isJapaneseDomains);
		long startTimestamp = System.currentTimeMillis();
		SendNonAssociatedCompetitorUrlsToTempTableCommand sendNonAssociatedCompetitorUrlsToTempTableCommand = null;
		int totalNumberOfDomains = allOwnDomainEntityList.size();
		String ipAddress = null;
		OwnDomainEntity ownDomainEntity = null;
		int numberOfDomainsProcessed = 0;

		do {
			ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
			if (ipAddress == null) {
				continue;
			}
			ownDomainEntity = allOwnDomainEntityList.get(numberOfDomainsProcessed++);
			sendNonAssociatedCompetitorUrlsToTempTableCommand = null;
			if (isExecDomainIdsInd == null) {
				sendNonAssociatedCompetitorUrlsToTempTableCommand = getSendNonAssociatedCompetitorUrlsToTempTableCommand(ipAddress, ownDomainEntity, trackDateNumber,
						isJapaneseDomains);
			} else if (isExecDomainIdsInd == true) {
				if (runtimeDomainSet.contains(ownDomainEntity.getId())) {
					sendNonAssociatedCompetitorUrlsToTempTableCommand = getSendNonAssociatedCompetitorUrlsToTempTableCommand(ipAddress, ownDomainEntity,
							trackDateNumber, isJapaneseDomains);
				}
			} else if (isExecDomainIdsInd == false) {
				if (!runtimeDomainSet.contains(ownDomainEntity.getId())) {
					sendNonAssociatedCompetitorUrlsToTempTableCommand = getSendNonAssociatedCompetitorUrlsToTempTableCommand(ipAddress, ownDomainEntity,
							trackDateNumber, isJapaneseDomains);
				}
			}
			if (sendNonAssociatedCompetitorUrlsToTempTableCommand != null) {
				try {
					FormatUtils.getInstance().logMemoryUsage("sendNonAssociatedCompetitorUrlsToTemporaryTable() ipAddress acquired=" + ipAddress + ",domain="
							+ ownDomainEntity.getId() + " - " + ownDomainEntity.getDomain());
					threadPoolService.execute(sendNonAssociatedCompetitorUrlsToTempTableCommand);
				} catch (Exception e) {
					e.printStackTrace();
				}
			} else {
				CacheModleFactory.getInstance().setAliveIpAddress(ipAddress);
			}
		} while (numberOfDomainsProcessed < totalNumberOfDomains);

		do {
			try {
				Thread.sleep(1000);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		} while (threadPoolService.getThreadPool().getActiveCount() > 0);
		FormatUtils.getInstance().logMemoryUsage(
				"sendAssociatedCompetitorUrlsToTemporaryTableConcurrently() end. elapsed time in seconds=" + ((System.currentTimeMillis() - startTimestamp) / 1000));
	}

	private void putMessagesToPoliteCrawlQueuesConcurrently(List<String> queueUrlList, int trackDateNumber, int minimumTotalUrls, int maximumTotalUrls)
			throws Exception {
		FormatUtils.getInstance().logMemoryUsage("putMessagesToPoliteCrawlQueuesConcurrently() begins. trackDateNumber=" + trackDateNumber + ",minimumTotalUrls="
				+ minimumTotalUrls + ",maximumTotalUrls=" + maximumTotalUrls);
		long startTimestamp = System.currentTimeMillis();
		String ipAddress = null;
		TestSendAssociatedUrlsToCrawlQueuesByDomainCommand testSendAssociatedUrlsToCrawlQueuesByDomainCommand = null;
		int numberOfCurrentThreads = CacheModleFactory.getInstance().getIpMap().size();
		int totalHostnamesToBeProcessed = 0;
		int totalHostnamesProcessed = 0;
		String hostname = null;
		AssociatedUrlEntity associatedUrlEntity = null;
		int totalNumberOfQueues = queueUrlList.size();
		String queueUrl = null;
		int totalUrls = 0;
		String[] hostnames = null;
		int grandTotalUrls = 0;
		CompetitorMsgClickHouseEntity competitorMsgClickHouseEntity = null;
		List<String> hostnameList = new ArrayList<String>();
		Date trackDate = null;
		int testTrackDateNumber = 0;
		BigDecimal totalMemory = null;
		BigDecimal maxMemory = null;
		BigDecimal memoryUsageInPercent = null;

		List<AssociatedUrlEntity> associatedUrlEntityList = null;

		List<CompetitorMsgClickHouseEntity> competitorMsgClickHouseEntityList = null;

		if (PutMessageUtils.getInstance().isProcessTypeSolr(processType) == true) {
			testTrackDateNumber = trackDateNumber;
			associatedUrlEntityList = associatedUrlEntityDAO.getTotalUrlsByHostname(testTrackDateNumber);
			if (associatedUrlEntityList == null || associatedUrlEntityList.size() == 0) {
				FormatUtils.getInstance().logMemoryUsage("putMessagesToPoliteCrawlQueuesConcurrently() no hostname can be retrieved.");
				return;
			}
			totalHostnamesToBeProcessed = associatedUrlEntityList.size();
		} else {
			trackDate = CompetitorUrlClickHouseDAO.getInstance().getTrackDate(null);
			testTrackDateNumber = NumberUtils.toInt(DateFormatUtils.format(trackDate, IConstants.DATE_FORMAT_YYYYMMDD));
			competitorMsgClickHouseEntityList = CompetitorMsgClickHouseDAO.getInstance().getTotalRecordsByDomain(trackDate, null);
			if (competitorMsgClickHouseEntityList == null || competitorMsgClickHouseEntityList.size() == 0) {
				FormatUtils.getInstance().logMemoryUsage("putMessagesToPoliteCrawlQueuesConcurrently() no hostname can be retrieved.");
				return;
			}
			totalHostnamesToBeProcessed = competitorMsgClickHouseEntityList.size();
		}
		FormatUtils.getInstance().logMemoryUsage("putMessagesToPoliteCrawlQueuesConcurrently() totalHostnamesToBeProcessed=" + totalHostnamesToBeProcessed
				+ ",numberOfCurrentThreads=" + numberOfCurrentThreads);

		for (int i = 0; i < totalNumberOfQueues; i++) {
			queueNumberQueueDepthMap.put(i, 0);
			if (isDebug == true) {
				FormatUtils.getInstance().logMemoryUsage("putMessagesToPoliteCrawlQueuesConcurrently() queueNumber=" + i + ",queueUrl=" + queueUrlList.get(i));
			}
		}

		do {
			ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
			if (ipAddress == null) {
				continue;
			}

			// pause when memory usage is greater than 95%
			totalMemory = new BigDecimal(Runtime.getRuntime().totalMemory()).setScale(0, RoundingMode.HALF_UP);
			maxMemory = new BigDecimal(Runtime.getRuntime().maxMemory()).setScale(0, RoundingMode.HALF_UP);
			memoryUsageInPercent = totalMemory.divide(maxMemory, DECIMAL_PRECISION, RoundingMode.HALF_UP).multiply(new BigDecimal(100.00));
			if (memoryUsageInPercent.compareTo(NINETY_FIVE_PERCENT_IN_DECIMAL) > 0) {
				FormatUtils.getInstance().logMemoryUsage("putMessagesToPoliteCrawlQueuesConcurrently() paused..memoryUsageInPercent==" + memoryUsageInPercent);
				try {
					Thread.sleep(SLEEP_TIME_15_SECONDS);
				} catch (InterruptedException e) {
					e.printStackTrace();
				}
				CacheModleFactory.getInstance().setAliveIpAddress(ipAddress);
				continue;
			}

			if (PutMessageUtils.getInstance().isProcessTypeSolr(processType) == true) {
				associatedUrlEntity = associatedUrlEntityList.get(totalHostnamesProcessed++);
				hostname = associatedUrlEntity.getHostname();
				totalUrls = associatedUrlEntity.getTotalUrls();
			} else {
				competitorMsgClickHouseEntity = competitorMsgClickHouseEntityList.get(totalHostnamesProcessed++);
				hostname = competitorMsgClickHouseEntity.getUrl_domain();
				totalUrls = competitorMsgClickHouseEntity.getTotal_records();
				FormatUtils.getInstance().logMemoryUsage("putMessagesToPoliteCrawlQueuesConcurrently() hostname=" + hostname + ",totalUrls=" + totalUrls);
				if (totalUrls >= minimumTotalUrls && totalUrls <= maximumTotalUrls) {
					// proceed...
				} else {
					FormatUtils.getInstance().logMemoryUsage("putMessagesToPoliteCrawlQueuesConcurrently() skip hostname=" + hostname + ",totalUrls=" + totalUrls);
					CacheModleFactory.getInstance().setAliveIpAddress(ipAddress);
					continue;
				}
			}

			// maximum 10 messages per put operation			
			if (totalUrls < 10) {
				if (grandTotalUrls == 0) {
					hostnameList = new ArrayList<String>();
				}
				hostnameList.add(hostname);
				grandTotalUrls = grandTotalUrls + totalUrls;
				if (grandTotalUrls >= 10) {
					hostnames = hostnameList.toArray(new String[0]);
					totalUrls = grandTotalUrls;
					grandTotalUrls = 0;
				} else {
					CacheModleFactory.getInstance().setAliveIpAddress(ipAddress);
					continue;
				}
			} else {
				hostnames = new String[] { hostname };
			}

			// determine the queue with smallest queue depth
			queueUrl = getQueueUrlOfSmallestQueueDepth(hostnames, totalUrls, queueUrlList);
			testSendAssociatedUrlsToCrawlQueuesByDomainCommand = getTestSendAssociatedUrlsToCrawlQueuesByDomainCommand(ipAddress, hostnames, queueUrl,
					testTrackDateNumber);
			//FormatUtils.getInstance().logMemoryUsage("putMessagesToPoliteCrawlQueuesConcurrently() executing thread, ip=" + ipAddress + ",totalHostnamesToBeProcessed="
			//		+ totalHostnamesToBeProcessed + ",totalHostnamesProcessed=" + totalHostnamesProcessed + ",queueUrl=" + queueUrl);
			threadPoolService.execute(testSendAssociatedUrlsToCrawlQueuesByDomainCommand);
		} while (totalHostnamesProcessed < totalHostnamesToBeProcessed);

		if (grandTotalUrls > 0) {
			hostnames = hostnameList.toArray(new String[0]);
			totalUrls = grandTotalUrls;
			grandTotalUrls = 0;
			queueUrl = getQueueUrlOfSmallestQueueDepth(hostnames, totalUrls, queueUrlList);
			ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
			if (ipAddress != null) {
				testSendAssociatedUrlsToCrawlQueuesByDomainCommand = getTestSendAssociatedUrlsToCrawlQueuesByDomainCommand(ipAddress, hostnames, queueUrl,
						testTrackDateNumber);
				FormatUtils.getInstance()
						.logMemoryUsage("putMessagesToPoliteCrawlQueuesConcurrently() executing thread for last batch, ip=" + ipAddress
								+ ",totalHostnamesToBeProcessed=" + totalHostnamesToBeProcessed + ",totalHostnamesProcessed=" + totalHostnamesProcessed + ",totalUrls="
								+ totalUrls + ",queueUrl=" + queueUrl);
				threadPoolService.execute(testSendAssociatedUrlsToCrawlQueuesByDomainCommand);
			}
		}

		do {
			//FormatUtils.getInstance()
			//		.logMemoryUsage("putMessagesToPoliteCrawlQueuesConcurrently() active threads=" + threadPoolService.getThreadPool().getActiveCount());
			try {
				Thread.sleep(SLEEP_TIME_15_SECONDS);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		} while (threadPoolService.getThreadPool().getActiveCount() > 0);

		FormatUtils.getInstance()
				.logMemoryUsage("putMessagesToPoliteCrawlQueuesConcurrently() end. totalHostnamesProcessed=" + totalHostnamesProcessed + ",minimumTotalUrls="
						+ minimumTotalUrls + ",maximumTotalUrls=" + maximumTotalUrls + ",elapsed(s.)=" + ((System.currentTimeMillis() - startTimestamp) / 1000));
	}

	// determine the queue with smallest queue depth
	private String getQueueUrlOfSmallestQueueDepth(String[] hostnames, int totalUrls, List<String> queueUrlList) {
		//FormatUtils.getInstance().logMemoryUsage("getQueueUrlOfSmallestQueueDepth() begins. hostnames=" + Arrays.toString(hostnames) + ",totalUrls=" + totalUrls);
		//if (isDebug == true) {
		//	FormatUtils.getInstance().logMemoryUsage("getQueueUrlOfSmallestQueueDepth() begins. hostnames=" + Arrays.toString(hostnames) + ",totalUrls=" + totalUrls);
		//}
		int smallestQueueNumber = 0;
		int smallestQueueDepth = queueNumberQueueDepthMap.get(smallestQueueNumber);
		int totalNumberOfQueues = queueNumberQueueDepthMap.size();
		int currentQueueDepth = 0;
		int currentQueueNumber = 0;
		nextQueue: for (int i = 0; i < totalNumberOfQueues; i++) {
			currentQueueDepth = queueNumberQueueDepthMap.get(i);
			currentQueueNumber = i;

			//if (isDebug == true) {
			//	FormatUtils.getInstance().logMemoryUsage("getQueueUrlOfSmallestQueueDepth() currentQueueNumber=" + currentQueueNumber + ",currentQueueDepth="
			//			+ currentQueueDepth + ",smallestQueueNumber=" + smallestQueueNumber + ",smallestQueueDepth=" + smallestQueueDepth);
			//}

			if (currentQueueDepth == 0) {
				smallestQueueNumber = currentQueueNumber;
				smallestQueueDepth = currentQueueDepth;
				break nextQueue;
			} else if (currentQueueDepth < smallestQueueDepth) {
				smallestQueueNumber = currentQueueNumber;
				smallestQueueDepth = currentQueueDepth;
			}
		}
		String queueUrl = queueUrlList.get(smallestQueueNumber);
		smallestQueueDepth = queueNumberQueueDepthMap.get(smallestQueueNumber);
		smallestQueueDepth = smallestQueueDepth + totalUrls;
		queueNumberQueueDepthMap.put(smallestQueueNumber, smallestQueueDepth);
		//FormatUtils.getInstance().logMemoryUsage("getQueueUrlOfSmallestQueueDepth() ends. smallestQueueNumber=" + smallestQueueNumber + ",smallestQueueDepth="
		//		+ smallestQueueDepth + ",queueUrl=" + queueUrl);
		//if (isDebug == true) {
		//	FormatUtils.getInstance().logMemoryUsage("getQueueUrlOfSmallestQueueDepth() ends. smallestQueueNumber=" + smallestQueueNumber + ",smallestQueueDepth="
		//			+ smallestQueueDepth + ",queueUrl=" + queueUrl);
		//}
		return queueUrl;
	}

	private SendAssociatedCompetitorUrlsToTempTableCommand getSendAssociatedCompetitorUrlsToTempTableCommand(String ip, OwnDomainEntity ownDomainEntity,
			int trackDateNumber, boolean isJapaneseDomains) {
		SendAssociatedCompetitorUrlsToTempTableCommand sendAssociatedCompetitorUrlsToTempTableCommand = new SendAssociatedCompetitorUrlsToTempTableCommand(ip,
				ownDomainEntity, trackDateNumber, isJapaneseDomains, processType);
		sendAssociatedCompetitorUrlsToTempTableCommand.setStatus(true);
		return sendAssociatedCompetitorUrlsToTempTableCommand;
	}

	private SendNonAssociatedCompetitorUrlsToTempTableCommand getSendNonAssociatedCompetitorUrlsToTempTableCommand(String ip, OwnDomainEntity ownDomainEntity,
			int trackDateNumber, boolean isJapaneseDomains) {
		SendNonAssociatedCompetitorUrlsToTempTableCommand sendNonAssociatedCompetitorUrlsToTempTableCommand = new SendNonAssociatedCompetitorUrlsToTempTableCommand(ip,
				ownDomainEntity, trackDateNumber, isJapaneseDomains, processType);
		sendNonAssociatedCompetitorUrlsToTempTableCommand.setStatus(true);
		return sendNonAssociatedCompetitorUrlsToTempTableCommand;
	}

	private TestSendAssociatedUrlsToCrawlQueuesByDomainCommand getTestSendAssociatedUrlsToCrawlQueuesByDomainCommand(String ip, String[] hostnames, String queueUrl,
			int trackDateNumber) {
		TestSendAssociatedUrlsToCrawlQueuesByDomainCommand testSendAssociatedUrlsToCrawlQueuesByDomainCommand = new TestSendAssociatedUrlsToCrawlQueuesByDomainCommand(
				ip, hostnames, queueUrl, trackDateNumber, processType);
		testSendAssociatedUrlsToCrawlQueuesByDomainCommand.setStatus(true);
		return testSendAssociatedUrlsToCrawlQueuesByDomainCommand;
	}

	public String createMessageBodyInJsonFormat(String domainSpecificQueueName, int delayInSecondsPerHttpRequest, int maxConcurrentCrawlThreads,
			String specificUserAgent, String domainNames, String userAgents) {
		String response = null;
		UrlCrawlParametersVO urlCrawlParametersVO = null;
		UrlCrawlParametersVO[] urlCrawlParametersVoArray = null;
		List<UrlCrawlParametersVO> urlCrawlParametersVoList = new ArrayList<UrlCrawlParametersVO>();

		// domain specific queue name
		urlCrawlParametersVO = new UrlCrawlParametersVO();
		urlCrawlParametersVO.setType(IConstants.QUEUE_NAME);
		urlCrawlParametersVO.setData(domainSpecificQueueName);
		urlCrawlParametersVoList.add(urlCrawlParametersVO);

		// delay in seconds per HTTP request
		urlCrawlParametersVO = new UrlCrawlParametersVO();
		urlCrawlParametersVO.setType(IConstants.DELAY_IN_SECONDS);
		urlCrawlParametersVO.setData(String.valueOf(delayInSecondsPerHttpRequest));
		urlCrawlParametersVoList.add(urlCrawlParametersVO);

		// maximum number of current threads per queue
		urlCrawlParametersVO = new UrlCrawlParametersVO();
		urlCrawlParametersVO.setType(IConstants.MAX_CONCURRENT_THREADS);
		//urlCrawlParametersVO.setData(String.valueOf(maxConcurrentCrawlThreads)); //debug
		urlCrawlParametersVO.setData(IConstants.NUMBER_1); //debug
		urlCrawlParametersVoList.add(urlCrawlParametersVO);

		// domain specific user agent
		if (StringUtils.isNotBlank(specificUserAgent)) {
			urlCrawlParametersVO = new UrlCrawlParametersVO();
			urlCrawlParametersVO.setType(IConstants.USER_AGENT);
			urlCrawlParametersVO.setData(specificUserAgent);
			urlCrawlParametersVoList.add(urlCrawlParametersVO);
		}

		// domain names
		if (StringUtils.isNotBlank(domainNames)) {
			urlCrawlParametersVO = new UrlCrawlParametersVO();
			urlCrawlParametersVO.setType(IConstants.DOMAIN_NAMES);
			urlCrawlParametersVO.setData(domainNames);
			urlCrawlParametersVoList.add(urlCrawlParametersVO);
		}

		// user agents
		if (StringUtils.isNotBlank(userAgents)) {
			urlCrawlParametersVO = new UrlCrawlParametersVO();
			urlCrawlParametersVO.setType(IConstants.USER_AGENTS);
			urlCrawlParametersVO.setData(userAgents);
			urlCrawlParametersVoList.add(urlCrawlParametersVO);
		}

		if (urlCrawlParametersVoList != null && urlCrawlParametersVoList.size() > 0) {
			urlCrawlParametersVoArray = urlCrawlParametersVoList.toArray(new UrlCrawlParametersVO[0]);
			response = new Gson().toJson(urlCrawlParametersVoArray, UrlCrawlParametersVO[].class);
		}
		return response;
	}

	public String createMessageBodyInJsonFormat(List<UrlCrawlParametersVO> urlCrawlParametersVoList) {
		String response = null;
		UrlCrawlParametersVO[] urlCrawlParametersVoArray = null;
		if (urlCrawlParametersVoList != null && urlCrawlParametersVoList.size() > 0) {
			urlCrawlParametersVoArray = urlCrawlParametersVoList.toArray(new UrlCrawlParametersVO[0]);
			response = new Gson().toJson(urlCrawlParametersVoArray, UrlCrawlParametersVO[].class);
		}
		return response;
	}

	private InitializeUrlCrawlQueueCommand getInitializeUrlCrawlQueueCommand(String ip, String queueName) {
		InitializeUrlCrawlQueueCommand InitializeUrlCrawlQueueCommand = new InitializeUrlCrawlQueueCommand(ip, queueName, isDeleteQueueOnly, isCreateQueue);
		InitializeUrlCrawlQueueCommand.setStatus(true);
		return InitializeUrlCrawlQueueCommand;
	}

	// create one message in the controller queue for the defined crawl queues
	private void sendDomainCrawlDataToControllerQueue(int totalCrawlQueues) throws Exception {

		long startTimestamp = System.currentTimeMillis();

		String controllerQueueName = null;
		String queueNamePrefix = null;
		if (isDebug == true) {
			if (isProcessWeekly == true || isProcessDaily == true) {
				controllerQueueName = IConstants.QUEUE_NAME_TEST_COMPETITOR_URL_HTML_QUEUE_NAMES;
				queueNamePrefix = IConstants.QUEUE_NAME_TEST_COMPETITOR_URL_HTML_PREFIX;
			}
		} else {
			if (isProcessWeekly == true || isProcessDaily == true) {
				if (PutMessageUtils.getInstance().isProcessTypeSolr(processType) == true) {
					controllerQueueName = IConstants.QUEUE_NAME_SOLR_COMPETITOR_URL_HTML_QUEUE_NAMES;
					queueNamePrefix = IConstants.QUEUE_NAME_SOLR_COMPETITOR_URL_HTML_PREFIX;
				} else {
					controllerQueueName = IConstants.QUEUE_NAME_COMPETITOR_URL_HTML_QUEUE_NAMES;
					queueNamePrefix = IConstants.QUEUE_NAME_COMPETITOR_URL_HTML_PREFIX;
				}
			}
		}

		FormatUtils.getInstance().logMemoryUsage("sendDomainCrawlDataToControllerQueue() begins. controllerQueueName=" + controllerQueueName + ",queueNamePrefix="
				+ queueNamePrefix + ",isDeleteQueueOnly=" + isDeleteQueueOnly);
		String queueUrl;
		Map<String, String> messages = new HashMap<String, String>();
		int totalMessages = 0;
		int maxConcurrentCrawlThreads = 0;
		int delayInSecondsPerHttpRequest = 0;
		String messageBodyInJsonFormat = null;
		String specificUserAgent = null;
		int crawlQueueNumber = 0;
		String crawlQueueName = null;
		String crawlQueueNumberString = null;
		String competitorDomain = null;

		// map key = queue number string
		// map value = queue name
		Map<String, String> queueNumberNameMap = new HashMap<String, String>();

		CompetitorUrlWeeklyCrawlTrackingEntity competitorUrlWeeklyCrawlTrackingEntity = null;
		int totalCompetitorUrls = 0;

		String domainNames = null;
		String userAgents = null;

		// map key = domain name
		// map key = user agent
		Map<String, String> domainNameUserAgentMap = new HashMap<String, String>();
		List<OwnDomainEntity> ownDomainEntityList = ownDomainEntityDAO.queryForAll();
		domainNameUserAgentMap = PutMessageUtils.getInstance().getDomainNameUserAgentMap(ownDomainEntityList);
		domainNames = PutMessageUtils.getInstance().getDomainNames(domainNameUserAgentMap);
		userAgents = PutMessageUtils.getInstance().getUserAgents(domainNameUserAgentMap);

		// initialize the queue
		try {
			queueUrl = SQSUtils.getInstance().createQueue(controllerQueueName);
			SQSUtils.getInstance().purgeQueue(queueUrl);
		} catch (Exception e) {
			e.printStackTrace();
			return;
		}

		for (int i = 0; i < totalCrawlQueues; i++) {
			maxConcurrentCrawlThreads = threadsPerQueue;
			delayInSecondsPerHttpRequest = 0; // by default, 0 seconds delay between each HTTP request
			specificUserAgent = null; // by default, use standard user agent
			crawlQueueNumber = i + 1;
			crawlQueueNumberString = String.valueOf(crawlQueueNumber);
			crawlQueueName = queueNamePrefix + crawlQueueNumber;
			try {
				// check if 'competitor_url_weekly_crawl_tracking' record exists to determine maxConcurrentCrawlThreads and delayInSecondsPerHttpRequest
				competitorUrlWeeklyCrawlTrackingEntity = competitorUrlWeeklyCrawlTrackingEntityDAO.get(IConstants.PROCESS_TYPE_NUMBER_CLICKHOUSE, crawlQueueName);
				if (competitorUrlWeeklyCrawlTrackingEntity != null) {
					competitorDomain = competitorUrlWeeklyCrawlTrackingEntity.getCompetitorDomain();
					totalCompetitorUrls = competitorUrlWeeklyCrawlTrackingEntity.getTotalUrls();
					maxConcurrentCrawlThreads = PutMessageUtils.getInstance().getMaxConcurrentCrawlThreads(totalCompetitorUrls, maxConcurrentCrawlThreads);
					delayInSecondsPerHttpRequest = PutMessageUtils.getInstance().getDelayInSecondsPerHttpRequest(totalCompetitorUrls);
					FormatUtils.getInstance().logMemoryUsage(
							"sendDomainCrawlDataToControllerQueue() updated based on 'competitor_url_weekly_crawl_tracking' record, controllerQueueName="
									+ controllerQueueName + ",competitorDomain=" + competitorDomain + ",totalCompetitorUrls=" + totalCompetitorUrls
									+ ",maxConcurrentCrawlThreads=" + maxConcurrentCrawlThreads + ",delayInSecondsPerHttpRequest=" + delayInSecondsPerHttpRequest);
				}

				// create message body in JSON format with the following elements: 
				// 1) queue name (eg. CLOUD_CRAWL_ASSOCIATED_URLS_SOCIAL_1, CLOUD_CRAWL_DAILY_ASSOCIATED_URLS_SOCIAL_1, etc.), 
				// 2) delay in seconds per HTTP request (optional, by default 0 second)
				// 3) maximum number of threads per queue (optional, by default 3 threads per instance)
				// 4) specific user agent (optional, by default use the user-agent specified in crawler.properties)
				messageBodyInJsonFormat = createMessageBodyInJsonFormat(crawlQueueName, delayInSecondsPerHttpRequest, maxConcurrentCrawlThreads, specificUserAgent,
						domainNames, userAgents);
				messages.put(crawlQueueNumberString, messageBodyInJsonFormat);
				queueNumberNameMap.put(crawlQueueNumberString, messageBodyInJsonFormat);
				totalMessages++;
				if (messages.size() >= 10) {
					SQSUtils.getInstance().sendBatchMessageToQueue(queueUrl, messages);
					messages = new HashMap<String, String>();
				}
				Thread.sleep(100);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		if (messages != null && !messages.isEmpty()) {
			SQSUtils.getInstance().sendBatchMessageToQueue(queueUrl, messages);
		}
		for (String tempQueueNumber : queueNumberNameMap.keySet()) {
			FormatUtils.getInstance().logMemoryUsage("sendDomainCrawlDataToControllerQueue() tempQueueNumber=" + tempQueueNumber + ",messageBodyInJsonFormat="
					+ queueNumberNameMap.get(tempQueueNumber));
		}
		FormatUtils.getInstance().logMemoryUsage("sendDomainCrawlDataToControllerQueue() ends. totalMessages=" + totalMessages + ",total elapsed time in sec.="
				+ (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private void reInitializeThreadsPool(int numberOfConcurrentThreads) {
		threadPoolService.destroy();
		threadPoolService.init();
		CommonUtils.initThreads(numberOfConcurrentThreads);
	}

	public static ConcurrentMap<String, Integer> getConcurrentMap() {
		return concurrentMap;
	}

	public static void setConcurrentMap(ConcurrentMap<String, Integer> concurrentMap) {
		TestSendAssociatedUrlsToCrawlQueuesByDomain.concurrentMap = concurrentMap;
	}

	public static List<String> getExpediaDomainList() {
		return expediaDomainList;
	}

	public static void setExpediaDomainList(List<String> expediaDomainList) {
		TestSendAssociatedUrlsToCrawlQueuesByDomain.expediaDomainList = expediaDomainList;
	}
}
