package com.actonia.value.object;

import java.util.Arrays;

public class UrlPageAnalysisResults {
	private String url;
	private String crawl_timestamp;
	private Integer[] rule_array;

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getCrawl_timestamp() {
		return crawl_timestamp;
	}

	public void setCrawl_timestamp(String crawl_timestamp) {
		this.crawl_timestamp = crawl_timestamp;
	}

	public Integer[] getRule_array() {
		return rule_array;
	}

	public void setRule_array(Integer[] rule_array) {
		this.rule_array = rule_array;
	}

	@Override
	public String toString() {
		return "UrlPageAnalysisResults [url=" + url + ", crawl_timestamp=" + crawl_timestamp + ", rule_array=" + Arrays.toString(rule_array) + "]";
	}

}
