package com.actonia.value.object;

public class TargetUrlMetricsUpdateReportValueObject {

	private int totalTargetUrlsRetrieved;
	private int totalTargetUrlsProcessed;
	private int totalTargetUrlsFoundInDatabase;
	private int totalTargetUrlsNotFoundInDatabase;

	private int totalUrlMetricsDataEntityFound;
	private int totalUrlMetricsDataEntityUpdated;
	private int totalUrlMetricsDataEntityNotUpdated;
	private int totalUrlMetricsDataEntityNotFound;
	private int totalUrlMetricsDataEntityCreated;

	private int totalTargetUrlCriteriaFound;
	private int totalTargetUrlCriteriaUpdated;
	private int totalTargetUrlCriteriaNotUpdated;
	private int totalTargetUrlCriteriaNotFound;
	private int totalTargetUrlCriteriaCreated;
	
	private int totalInternalLinkCountCalculated;

	public int getTotalTargetUrlsRetrieved() {
		return totalTargetUrlsRetrieved;
	}

	public void setTotalTargetUrlsRetrieved(int totalTargetUrlsRetrieved) {
		this.totalTargetUrlsRetrieved = totalTargetUrlsRetrieved;
	}

	public int getTotalTargetUrlsProcessed() {
		return totalTargetUrlsProcessed;
	}

	public void setTotalTargetUrlsProcessed(int totalTargetUrlsProcessed) {
		this.totalTargetUrlsProcessed = totalTargetUrlsProcessed;
	}

	public int getTotalTargetUrlsFoundInDatabase() {
		return totalTargetUrlsFoundInDatabase;
	}

	public void setTotalTargetUrlsFoundInDatabase(int totalTargetUrlsFoundInDatabase) {
		this.totalTargetUrlsFoundInDatabase = totalTargetUrlsFoundInDatabase;
	}

	public int getTotalTargetUrlsNotFoundInDatabase() {
		return totalTargetUrlsNotFoundInDatabase;
	}

	public void setTotalTargetUrlsNotFoundInDatabase(int totalTargetUrlsNotFoundInDatabase) {
		this.totalTargetUrlsNotFoundInDatabase = totalTargetUrlsNotFoundInDatabase;
	}

	public int getTotalUrlMetricsDataEntityFound() {
		return totalUrlMetricsDataEntityFound;
	}

	public void setTotalUrlMetricsDataEntityFound(int totalUrlMetricsDataEntityFound) {
		this.totalUrlMetricsDataEntityFound = totalUrlMetricsDataEntityFound;
	}

	public int getTotalUrlMetricsDataEntityUpdated() {
		return totalUrlMetricsDataEntityUpdated;
	}

	public void setTotalUrlMetricsDataEntityUpdated(int totalUrlMetricsDataEntityUpdated) {
		this.totalUrlMetricsDataEntityUpdated = totalUrlMetricsDataEntityUpdated;
	}

	public int getTotalUrlMetricsDataEntityNotUpdated() {
		return totalUrlMetricsDataEntityNotUpdated;
	}

	public void setTotalUrlMetricsDataEntityNotUpdated(int totalUrlMetricsDataEntityNotUpdated) {
		this.totalUrlMetricsDataEntityNotUpdated = totalUrlMetricsDataEntityNotUpdated;
	}

	public int getTotalUrlMetricsDataEntityNotFound() {
		return totalUrlMetricsDataEntityNotFound;
	}

	public void setTotalUrlMetricsDataEntityNotFound(int totalUrlMetricsDataEntityNotFound) {
		this.totalUrlMetricsDataEntityNotFound = totalUrlMetricsDataEntityNotFound;
	}

	public int getTotalUrlMetricsDataEntityCreated() {
		return totalUrlMetricsDataEntityCreated;
	}

	public void setTotalUrlMetricsDataEntityCreated(int totalUrlMetricsDataEntityCreated) {
		this.totalUrlMetricsDataEntityCreated = totalUrlMetricsDataEntityCreated;
	}

	public int getTotalTargetUrlCriteriaFound() {
		return totalTargetUrlCriteriaFound;
	}

	public void setTotalTargetUrlCriteriaFound(int totalTargetUrlCriteriaFound) {
		this.totalTargetUrlCriteriaFound = totalTargetUrlCriteriaFound;
	}

	public int getTotalTargetUrlCriteriaUpdated() {
		return totalTargetUrlCriteriaUpdated;
	}

	public void setTotalTargetUrlCriteriaUpdated(int totalTargetUrlCriteriaUpdated) {
		this.totalTargetUrlCriteriaUpdated = totalTargetUrlCriteriaUpdated;
	}

	public int getTotalTargetUrlCriteriaNotUpdated() {
		return totalTargetUrlCriteriaNotUpdated;
	}

	public void setTotalTargetUrlCriteriaNotUpdated(int totalTargetUrlCriteriaNotUpdated) {
		this.totalTargetUrlCriteriaNotUpdated = totalTargetUrlCriteriaNotUpdated;
	}

	public int getTotalTargetUrlCriteriaNotFound() {
		return totalTargetUrlCriteriaNotFound;
	}

	public void setTotalTargetUrlCriteriaNotFound(int totalTargetUrlCriteriaNotFound) {
		this.totalTargetUrlCriteriaNotFound = totalTargetUrlCriteriaNotFound;
	}

	public int getTotalTargetUrlCriteriaCreated() {
		return totalTargetUrlCriteriaCreated;
	}

	public void setTotalTargetUrlCriteriaCreated(int totalTargetUrlCriteriaCreated) {
		this.totalTargetUrlCriteriaCreated = totalTargetUrlCriteriaCreated;
	}

	public int getTotalInternalLinkCountCalculated() {
		return totalInternalLinkCountCalculated;
	}

	public void setTotalInternalLinkCountCalculated(int totalInternalLinkCountCalculated) {
		this.totalInternalLinkCountCalculated = totalInternalLinkCountCalculated;
	}

	@Override
	public String toString() {
		return "TargetUrlMetricsUpdateReportValueObject [totalTargetUrlsRetrieved=" + totalTargetUrlsRetrieved + ", totalTargetUrlsProcessed="
				+ totalTargetUrlsProcessed + ", totalTargetUrlsFoundInDatabase=" + totalTargetUrlsFoundInDatabase + ", totalTargetUrlsNotFoundInDatabase="
				+ totalTargetUrlsNotFoundInDatabase + ", totalUrlMetricsDataEntityFound=" + totalUrlMetricsDataEntityFound + ", totalUrlMetricsDataEntityUpdated="
				+ totalUrlMetricsDataEntityUpdated + ", totalUrlMetricsDataEntityNotUpdated=" + totalUrlMetricsDataEntityNotUpdated
				+ ", totalUrlMetricsDataEntityNotFound=" + totalUrlMetricsDataEntityNotFound + ", totalUrlMetricsDataEntityCreated=" + totalUrlMetricsDataEntityCreated
				+ ", totalTargetUrlCriteriaFound=" + totalTargetUrlCriteriaFound + ", totalTargetUrlCriteriaUpdated=" + totalTargetUrlCriteriaUpdated
				+ ", totalTargetUrlCriteriaNotUpdated=" + totalTargetUrlCriteriaNotUpdated + ", totalTargetUrlCriteriaNotFound=" + totalTargetUrlCriteriaNotFound
				+ ", totalTargetUrlCriteriaCreated=" + totalTargetUrlCriteriaCreated + ", totalInternalLinkCountCalculated=" + totalInternalLinkCountCalculated + "]";
	}

}
