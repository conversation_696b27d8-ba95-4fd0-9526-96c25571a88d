package com.actonia.value.object;

import java.util.Arrays;

public class TargetUrlChangeSummary {
	private String crawl_date_hour;
	private Integer total_added;
	private Integer total_modified;
	private Integer total_removed;
	private int total_severity_critical;

	private int total_severity_high;

	private int total_severity_medium;

	private int total_severity_low;
	private ChangeIndicatorTotalUrls[] change_indicator_total_urls_array;

	public String getCrawl_date_hour() {
		return crawl_date_hour;
	}

	public void setCrawl_date_hour(String crawl_date_hour) {
		this.crawl_date_hour = crawl_date_hour;
	}

	public Integer getTotal_added() {
		return total_added;
	}

	public void setTotal_added(Integer total_added) {
		this.total_added = total_added;
	}

	public Integer getTotal_modified() {
		return total_modified;
	}

	public void setTotal_modified(Integer total_modified) {
		this.total_modified = total_modified;
	}

	public Integer getTotal_removed() {
		return total_removed;
	}

	public void setTotal_removed(Integer total_removed) {
		this.total_removed = total_removed;
	}

	public ChangeIndicatorTotalUrls[] getChange_indicator_total_urls_array() {
		return change_indicator_total_urls_array;
	}

	public void setChange_indicator_total_urls_array(ChangeIndicatorTotalUrls[] change_indicator_total_urls_array) {
		this.change_indicator_total_urls_array = change_indicator_total_urls_array;
	}

	public int getTotal_severity_critical() {
		return total_severity_critical;
	}

	public void setTotal_severity_critical(int total_severity_critical) {
		this.total_severity_critical = total_severity_critical;
	}

	public int getTotal_severity_high() {
		return total_severity_high;
	}

	public void setTotal_severity_high(int total_severity_high) {
		this.total_severity_high = total_severity_high;
	}

	public int getTotal_severity_medium() {
		return total_severity_medium;
	}

	public void setTotal_severity_medium(int total_severity_medium) {
		this.total_severity_medium = total_severity_medium;
	}

	public int getTotal_severity_low() {
		return total_severity_low;
	}

	public void setTotal_severity_low(int total_severity_low) {
		this.total_severity_low = total_severity_low;
	}

	@Override
	public String toString() {
		return "TargetUrlChangeSummary [crawl_date_hour=" + crawl_date_hour + ", total_added=" + total_added + ", total_modified=" + total_modified + ", total_removed="
				+ total_removed + ", change_indicator_total_urls_array=" + Arrays.toString(change_indicator_total_urls_array)
				+ ", total_severity_critical=" + total_severity_critical + ", total_severity_high=" + total_severity_high + ", total_severity_medium=" + total_severity_medium + ", total_severity_low=" + total_severity_low + "]";
	}

}
