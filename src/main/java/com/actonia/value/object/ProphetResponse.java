package com.actonia.value.object;

import java.util.Arrays;
import java.util.Map;

public class ProphetResponse {
	private Boolean success;
	private WebServiceError error;
	private String[] date_array;
	private double[] trend_array;
	private double[] trend_lower_array;
	private double[] trend_upper_array;
	private double[] forecast_array;
	private double[] forecast_lower_array;
	private double[] forecast_upper_array;

	// map key = days of week (1=Monday, 2=Tuesday, 3=Wednesday, 4=Thursday, 5=Friday, 6=Saturday, 7=Sunday)
	// map value = seasonality value
	private Map<Integer, Double> weekly_seasonality_map;

	// from first day of year to last day of year
	private double[] yearly_seasonality_array;

	public Boolean getSuccess() {
		return success;
	}

	public void setSuccess(Boolean success) {
		this.success = success;
	}

	public WebServiceError getError() {
		return error;
	}

	public void setError(WebServiceError error) {
		this.error = error;
	}

	public String[] getDate_array() {
		return date_array;
	}

	public void setDate_array(String[] date_array) {
		this.date_array = date_array;
	}

	public double[] getTrend_array() {
		return trend_array;
	}

	public void setTrend_array(double[] trend_array) {
		this.trend_array = trend_array;
	}

	public double[] getTrend_lower_array() {
		return trend_lower_array;
	}

	public void setTrend_lower_array(double[] trend_lower_array) {
		this.trend_lower_array = trend_lower_array;
	}

	public double[] getTrend_upper_array() {
		return trend_upper_array;
	}

	public void setTrend_upper_array(double[] trend_upper_array) {
		this.trend_upper_array = trend_upper_array;
	}

	public double[] getForecast_array() {
		return forecast_array;
	}

	public void setForecast_array(double[] forecast_array) {
		this.forecast_array = forecast_array;
	}

	public double[] getForecast_lower_array() {
		return forecast_lower_array;
	}

	public void setForecast_lower_array(double[] forecast_lower_array) {
		this.forecast_lower_array = forecast_lower_array;
	}

	public double[] getForecast_upper_array() {
		return forecast_upper_array;
	}

	public void setForecast_upper_array(double[] forecast_upper_array) {
		this.forecast_upper_array = forecast_upper_array;
	}

	public Map<Integer, Double> getWeekly_seasonality_map() {
		return weekly_seasonality_map;
	}

	public void setWeekly_seasonality_map(Map<Integer, Double> weekly_seasonality_map) {
		this.weekly_seasonality_map = weekly_seasonality_map;
	}

	public double[] getYearly_seasonality_array() {
		return yearly_seasonality_array;
	}

	public void setYearly_seasonality_array(double[] yearly_seasonality_array) {
		this.yearly_seasonality_array = yearly_seasonality_array;
	}

	@Override
	public String toString() {
		return "ProphetResponse [success=" + success + ", error=" + error + ", date_array=" + Arrays.toString(date_array) + ", trend_array="
				+ Arrays.toString(trend_array) + ", trend_lower_array=" + Arrays.toString(trend_lower_array) + ", trend_upper_array="
				+ Arrays.toString(trend_upper_array) + ", forecast_array=" + Arrays.toString(forecast_array) + ", forecast_lower_array="
				+ Arrays.toString(forecast_lower_array) + ", forecast_upper_array=" + Arrays.toString(forecast_upper_array) + ", weekly_seasonality_map="
				+ weekly_seasonality_map + ", yearly_seasonality_array=" + Arrays.toString(yearly_seasonality_array) + "]";
	}

}
