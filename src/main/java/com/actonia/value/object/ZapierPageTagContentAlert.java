package com.actonia.value.object;

public class ZapierPageTagContentAlert {
	private String id;
	private String alert_timestamp;
	private Integer domain_id;
	private String domain_name;
	private Integer page_tag_id;
	private String page_tag_name;
	private String content_change_date;
	private String content_change_threshold;
	private String page_tag_level_change_threshold;
	private Integer total_urls_changed;
	private Integer total_urls_in_tag;
	private String percent_of_urls_changed;
	private String changes;
	private String error_code;
	private String error_message;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getAlert_timestamp() {
		return alert_timestamp;
	}

	public void setAlert_timestamp(String alert_timestamp) {
		this.alert_timestamp = alert_timestamp;
	}

	public Integer getDomain_id() {
		return domain_id;
	}

	public void setDomain_id(Integer domain_id) {
		this.domain_id = domain_id;
	}

	public String getDomain_name() {
		return domain_name;
	}

	public void setDomain_name(String domain_name) {
		this.domain_name = domain_name;
	}

	public Integer getPage_tag_id() {
		return page_tag_id;
	}

	public void setPage_tag_id(Integer page_tag_id) {
		this.page_tag_id = page_tag_id;
	}

	public String getPage_tag_name() {
		return page_tag_name;
	}

	public void setPage_tag_name(String page_tag_name) {
		this.page_tag_name = page_tag_name;
	}

	public String getContent_change_date() {
		return content_change_date;
	}

	public void setContent_change_date(String content_change_date) {
		this.content_change_date = content_change_date;
	}

	public String getContent_change_threshold() {
		return content_change_threshold;
	}

	public void setContent_change_threshold(String content_change_threshold) {
		this.content_change_threshold = content_change_threshold;
	}

	public String getPage_tag_level_change_threshold() {
		return page_tag_level_change_threshold;
	}

	public void setPage_tag_level_change_threshold(String page_tag_level_change_threshold) {
		this.page_tag_level_change_threshold = page_tag_level_change_threshold;
	}

	public Integer getTotal_urls_changed() {
		return total_urls_changed;
	}

	public void setTotal_urls_changed(Integer total_urls_changed) {
		this.total_urls_changed = total_urls_changed;
	}

	public Integer getTotal_urls_in_tag() {
		return total_urls_in_tag;
	}

	public void setTotal_urls_in_tag(Integer total_urls_in_tag) {
		this.total_urls_in_tag = total_urls_in_tag;
	}

	public String getPercent_of_urls_changed() {
		return percent_of_urls_changed;
	}

	public void setPercent_of_urls_changed(String percent_of_urls_changed) {
		this.percent_of_urls_changed = percent_of_urls_changed;
	}

	public String getChanges() {
		return changes;
	}

	public void setChanges(String changes) {
		this.changes = changes;
	}

	public String getError_code() {
		return error_code;
	}

	public void setError_code(String error_code) {
		this.error_code = error_code;
	}

	public String getError_message() {
		return error_message;
	}

	public void setError_message(String error_message) {
		this.error_message = error_message;
	}

	@Override
	public String toString() {
		return "ZapierPageTagContentAlert [id=" + id + ", alert_timestamp=" + alert_timestamp + ", domain_id=" + domain_id + ", domain_name=" + domain_name
				+ ", page_tag_id=" + page_tag_id + ", page_tag_name=" + page_tag_name + ", content_change_date=" + content_change_date + ", content_change_threshold="
				+ content_change_threshold + ", page_tag_level_change_threshold=" + page_tag_level_change_threshold + ", total_urls_changed=" + total_urls_changed
				+ ", total_urls_in_tag=" + total_urls_in_tag + ", percent_of_urls_changed=" + percent_of_urls_changed + ", changes=" + changes + ", error_code="
				+ error_code + ", error_message=" + error_message + "]";
	}

}
