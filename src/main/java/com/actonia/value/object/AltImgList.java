package com.actonia.value.object;

public class AltImgList {
	private String anchor_text;
	private String destination_url;
	private String height;
	private String width;

	public String getAnchor_text() {
		return anchor_text;
	}

	public void setAnchor_text(String anchor_text) {
		this.anchor_text = anchor_text;
	}

	public String getDestination_url() {
		return destination_url;
	}

	public void setDestination_url(String destination_url) {
		this.destination_url = destination_url;
	}

	public String getHeight() {
		return height;
	}

	public void setHeight(String height) {
		this.height = height;
	}

	public String getWidth() {
		return width;
	}

	public void setWidth(String width) {
		this.width = width;
	}

	@Override
	public String toString() {
		return "AltImgList [anchor_text=" + anchor_text + ", destination_url=" + destination_url + ", height=" + height + ", width=" + width + "]";
	}

}
