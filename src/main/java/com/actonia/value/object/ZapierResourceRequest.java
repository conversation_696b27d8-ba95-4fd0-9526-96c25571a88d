package com.actonia.value.object;

public class ZapierResourceRequest {
	private String access_token;
	private Integer domain_id;
	private String user_email;
	private String callback_url;
	private String group_name;
	private String page_tag_name;

	public String getAccess_token() {
		return access_token;
	}

	public void setAccess_token(String access_token) {
		this.access_token = access_token;
	}

	public Integer getDomain_id() {
		return domain_id;
	}

	public void setDomain_id(Integer domain_id) {
		this.domain_id = domain_id;
	}

	public String getUser_email() {
		return user_email;
	}

	public void setUser_email(String user_email) {
		this.user_email = user_email;
	}

	public String getCallback_url() {
		return callback_url;
	}

	public void setCallback_url(String callback_url) {
		this.callback_url = callback_url;
	}

	public String getGroup_name() {
		return group_name;
	}

	public void setGroup_name(String group_name) {
		this.group_name = group_name;
	}

	public String getPage_tag_name() {
		return page_tag_name;
	}

	public void setPage_tag_name(String page_tag_name) {
		this.page_tag_name = page_tag_name;
	}

	@Override
	public String toString() {
		return "ZapierResourceRequest [access_token=" + access_token + ", domain_id=" + domain_id + ", user_email=" + user_email + ", callback_url=" + callback_url
				+ ", group_name=" + group_name + ", page_tag_name=" + page_tag_name + "]";
	}

}
