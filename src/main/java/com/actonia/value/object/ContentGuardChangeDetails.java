package com.actonia.value.object;

import java.util.Arrays;

public class ContentGuardChangeDetails {
	private String change_type;
	private String change_indicator;
	private Integer severity;
	private String change_field;
	private String previous_content;
	private String current_content;

	// when the 'change_field' element is 'alternate_links'
	private AlternateLinks[] alternate_links_previous_array;
	private AlternateLinks[] alternate_links_current_array;

	// when the 'change_field' element is 'custom_data'
	private CustomData[] custom_data_previous_array;
	private CustomData[] custom_data_current_array;

	// when the 'change_field' element is 'h1'
	private String[] h1_previous_array;
	private String[] h1_current_array;

	// when the 'change_field' element is 'h2'
	private String[] h2_previous_array;
	private String[] h2_current_array;

	// when the 'change_field' element is 'hreflang_errors'
	private HreflangErrors hreflang_errors_previous;
	private HreflangErrors hreflang_errors_current;

	// when the 'change_field' element is 'hreflang_links'
	private HreflangLinks[] hreflang_links_previous_array;
	private HreflangLinks[] hreflang_links_current_array;

	// when the 'change_field' element is 'insecure_resources'
	private String[] insecure_resources_previous_array;
	private String[] insecure_resources_current_array;

	// when the 'change_field' element is 'og_markup'
	private OgMarkup[] og_markup_previous_array;
	private OgMarkup[] og_markup_current_array;

	// when the 'change_field' element is 'page_analysis_results_chg_ind_json'
	private PageAnalysisResultChgInd[] page_analysis_result_chg_ind_array;

	// when the 'change_field' element is 'page_link'
	private PageLink[] page_link_previous_array;
	private PageLink[] page_link_current_array;

	// when the 'change_field' element is 'redirect_chain'
	private RedirectChain[] redirect_chain_previous_array;
	private RedirectChain[] redirect_chain_current_array;

	// when the 'change_field' element is 'response_headers'
	private String[] response_header_previous_array;
	private String[] response_header_current_array;

	// when the 'change_field' element is 'structured_data'
	private StructuredData structured_data_previous;
	private StructuredData structured_data_current;

	public String getChange_type() {
		return change_type;
	}

	public void setChange_type(String change_type) {
		this.change_type = change_type;
	}

	public String getChange_indicator() {
		return change_indicator;
	}

	public void setChange_indicator(String change_indicator) {
		this.change_indicator = change_indicator;
	}

	public Integer getSeverity() {
		return severity;
	}

	public void setSeverity(Integer severity) {
		this.severity = severity;
	}

	public String getChange_field() {
		return change_field;
	}

	public void setChange_field(String change_field) {
		this.change_field = change_field;
	}

	public String getPrevious_content() {
		return previous_content;
	}

	public void setPrevious_content(String previous_content) {
		this.previous_content = previous_content;
	}

	public String getCurrent_content() {
		return current_content;
	}

	public void setCurrent_content(String current_content) {
		this.current_content = current_content;
	}

	public AlternateLinks[] getAlternate_links_previous_array() {
		return alternate_links_previous_array;
	}

	public void setAlternate_links_previous_array(AlternateLinks[] alternate_links_previous_array) {
		this.alternate_links_previous_array = alternate_links_previous_array;
	}

	public AlternateLinks[] getAlternate_links_current_array() {
		return alternate_links_current_array;
	}

	public void setAlternate_links_current_array(AlternateLinks[] alternate_links_current_array) {
		this.alternate_links_current_array = alternate_links_current_array;
	}

	public CustomData[] getCustom_data_previous_array() {
		return custom_data_previous_array;
	}

	public void setCustom_data_previous_array(CustomData[] custom_data_previous_array) {
		this.custom_data_previous_array = custom_data_previous_array;
	}

	public CustomData[] getCustom_data_current_array() {
		return custom_data_current_array;
	}

	public void setCustom_data_current_array(CustomData[] custom_data_current_array) {
		this.custom_data_current_array = custom_data_current_array;
	}

	public String[] getH1_previous_array() {
		return h1_previous_array;
	}

	public void setH1_previous_array(String[] h1_previous_array) {
		this.h1_previous_array = h1_previous_array;
	}

	public String[] getH1_current_array() {
		return h1_current_array;
	}

	public void setH1_current_array(String[] h1_current_array) {
		this.h1_current_array = h1_current_array;
	}

	public String[] getH2_previous_array() {
		return h2_previous_array;
	}

	public void setH2_previous_array(String[] h2_previous_array) {
		this.h2_previous_array = h2_previous_array;
	}

	public String[] getH2_current_array() {
		return h2_current_array;
	}

	public void setH2_current_array(String[] h2_current_array) {
		this.h2_current_array = h2_current_array;
	}

	public HreflangErrors getHreflang_errors_previous() {
		return hreflang_errors_previous;
	}

	public void setHreflang_errors_previous(HreflangErrors hreflang_errors_previous) {
		this.hreflang_errors_previous = hreflang_errors_previous;
	}

	public HreflangErrors getHreflang_errors_current() {
		return hreflang_errors_current;
	}

	public void setHreflang_errors_current(HreflangErrors hreflang_errors_current) {
		this.hreflang_errors_current = hreflang_errors_current;
	}

	public HreflangLinks[] getHreflang_links_previous_array() {
		return hreflang_links_previous_array;
	}

	public void setHreflang_links_previous_array(HreflangLinks[] hreflang_links_previous_array) {
		this.hreflang_links_previous_array = hreflang_links_previous_array;
	}

	public HreflangLinks[] getHreflang_links_current_array() {
		return hreflang_links_current_array;
	}

	public void setHreflang_links_current_array(HreflangLinks[] hreflang_links_current_array) {
		this.hreflang_links_current_array = hreflang_links_current_array;
	}

	public String[] getInsecure_resources_previous_array() {
		return insecure_resources_previous_array;
	}

	public void setInsecure_resources_previous_array(String[] insecure_resources_previous_array) {
		this.insecure_resources_previous_array = insecure_resources_previous_array;
	}

	public String[] getInsecure_resources_current_array() {
		return insecure_resources_current_array;
	}

	public void setInsecure_resources_current_array(String[] insecure_resources_current_array) {
		this.insecure_resources_current_array = insecure_resources_current_array;
	}

	public OgMarkup[] getOg_markup_previous_array() {
		return og_markup_previous_array;
	}

	public void setOg_markup_previous_array(OgMarkup[] og_markup_previous_array) {
		this.og_markup_previous_array = og_markup_previous_array;
	}

	public OgMarkup[] getOg_markup_current_array() {
		return og_markup_current_array;
	}

	public void setOg_markup_current_array(OgMarkup[] og_markup_current_array) {
		this.og_markup_current_array = og_markup_current_array;
	}

	public PageAnalysisResultChgInd[] getPage_analysis_result_chg_ind_array() {
		return page_analysis_result_chg_ind_array;
	}

	public void setPage_analysis_result_chg_ind_array(PageAnalysisResultChgInd[] page_analysis_result_chg_ind_array) {
		this.page_analysis_result_chg_ind_array = page_analysis_result_chg_ind_array;
	}

	public PageLink[] getPage_link_previous_array() {
		return page_link_previous_array;
	}

	public void setPage_link_previous_array(PageLink[] page_link_previous_array) {
		this.page_link_previous_array = page_link_previous_array;
	}

	public PageLink[] getPage_link_current_array() {
		return page_link_current_array;
	}

	public void setPage_link_current_array(PageLink[] page_link_current_array) {
		this.page_link_current_array = page_link_current_array;
	}

	public RedirectChain[] getRedirect_chain_previous_array() {
		return redirect_chain_previous_array;
	}

	public void setRedirect_chain_previous_array(RedirectChain[] redirect_chain_previous_array) {
		this.redirect_chain_previous_array = redirect_chain_previous_array;
	}

	public RedirectChain[] getRedirect_chain_current_array() {
		return redirect_chain_current_array;
	}

	public void setRedirect_chain_current_array(RedirectChain[] redirect_chain_current_array) {
		this.redirect_chain_current_array = redirect_chain_current_array;
	}

	public String[] getResponse_header_previous_array() {
		return response_header_previous_array;
	}

	public void setResponse_header_previous_array(String[] response_header_previous_array) {
		this.response_header_previous_array = response_header_previous_array;
	}

	public String[] getResponse_header_current_array() {
		return response_header_current_array;
	}

	public void setResponse_header_current_array(String[] response_header_current_array) {
		this.response_header_current_array = response_header_current_array;
	}

	public StructuredData getStructured_data_previous() {
		return structured_data_previous;
	}

	public void setStructured_data_previous(StructuredData structured_data_previous) {
		this.structured_data_previous = structured_data_previous;
	}

	public StructuredData getStructured_data_current() {
		return structured_data_current;
	}

	public void setStructured_data_current(StructuredData structured_data_current) {
		this.structured_data_current = structured_data_current;
	}

	@Override
	public String toString() {
		return "ContentGuardChangeDetails [change_type=" + change_type + ", change_indicator=" + change_indicator + ", severity=" + severity + ", change_field="
				+ change_field + ", previous_content=" + previous_content + ", current_content=" + current_content + ", alternate_links_previous_array="
				+ Arrays.toString(alternate_links_previous_array) + ", alternate_links_current_array=" + Arrays.toString(alternate_links_current_array)
				+ ", custom_data_previous_array=" + Arrays.toString(custom_data_previous_array) + ", custom_data_current_array="
				+ Arrays.toString(custom_data_current_array) + ", h1_previous_array=" + Arrays.toString(h1_previous_array) + ", h1_current_array="
				+ Arrays.toString(h1_current_array) + ", h2_previous_array=" + Arrays.toString(h2_previous_array) + ", h2_current_array="
				+ Arrays.toString(h2_current_array) + ", hreflang_errors_previous=" + hreflang_errors_previous + ", hreflang_errors_current=" + hreflang_errors_current
				+ ", hreflang_links_previous_array=" + Arrays.toString(hreflang_links_previous_array) + ", hreflang_links_current_array="
				+ Arrays.toString(hreflang_links_current_array) + ", insecure_resources_previous_array=" + Arrays.toString(insecure_resources_previous_array)
				+ ", insecure_resources_current_array=" + Arrays.toString(insecure_resources_current_array) + ", og_markup_previous_array="
				+ Arrays.toString(og_markup_previous_array) + ", og_markup_current_array=" + Arrays.toString(og_markup_current_array)
				+ ", page_analysis_result_chg_ind_array=" + Arrays.toString(page_analysis_result_chg_ind_array) + ", page_link_previous_array="
				+ Arrays.toString(page_link_previous_array) + ", page_link_current_array=" + Arrays.toString(page_link_current_array)
				+ ", redirect_chain_previous_array=" + Arrays.toString(redirect_chain_previous_array) + ", redirect_chain_current_array="
				+ Arrays.toString(redirect_chain_current_array) + ", response_header_previous_array=" + Arrays.toString(response_header_previous_array)
				+ ", response_header_current_array=" + Arrays.toString(response_header_current_array) + ", structured_data_previous=" + structured_data_previous
				+ ", structured_data_current=" + structured_data_current + "]";
	}

}
