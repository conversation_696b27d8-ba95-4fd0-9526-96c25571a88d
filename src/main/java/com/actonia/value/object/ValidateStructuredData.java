package com.actonia.value.object;

import java.util.Arrays;

public class ValidateStructuredData {
	private String data_type;
	private String encoding;
	private Errors[] errors;

	public String getData_type() {
		return data_type;
	}

	public void setData_type(String data_type) {
		this.data_type = data_type;
	}

	public String getEncoding() {
		return encoding;
	}

	public void setEncoding(String encoding) {
		this.encoding = encoding;
	}

	public Errors[] getErrors() {
		return errors;
	}

	public void setErrors(Errors[] errors) {
		this.errors = errors;
	}

	@Override
	public String toString() {
		return "ValidateStructuredData [data_type=" + data_type + ", encoding=" + encoding + ", errors=" + Arrays.toString(errors) + "]";
	}

}
