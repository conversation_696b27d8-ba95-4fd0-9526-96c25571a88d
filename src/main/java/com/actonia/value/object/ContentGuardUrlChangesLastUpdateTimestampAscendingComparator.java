package com.actonia.value.object;

import java.util.Comparator;

public class ContentGuardUrlChangesLastUpdateTimestampAscendingComparator implements Comparator<ContentGuardUrlChanges> {

	@Override
	public int compare(ContentGuardUrlChanges arg0, ContentGuardUrlChanges arg1) {

		// sort by:
		// 1) last_update_timestamp (ascending order)

		int response = 0;
		if (arg0 != null && arg1 != null) {
			// last_update_timestamp
			if (arg0.getLast_update_timestamp() == null && arg1.getLast_update_timestamp() == null) { // when arg0 and arg1 are both null
				response = 0;
			} else if (arg0.getLast_update_timestamp() != null && arg1.getLast_update_timestamp() == null) { // when arg0 is greater than arg1
				response = +1;
			} else if (arg0.getLast_update_timestamp() == null && arg1.getLast_update_timestamp() != null) { // when arg0 is smaller than arg1
				response = -1;
			} else if (arg0.getLast_update_timestamp().compareTo(arg1.getLast_update_timestamp()) > 0) { // when arg0 is greater than arg1
				response = +1;
			} else if (arg0.getLast_update_timestamp().compareTo(arg1.getLast_update_timestamp()) < 0) { // when arg0 is smaller than arg1
				response = -1;
			}
			if (response == 0) {
				// url in ascending order
				if (arg0.getUrl().compareTo(arg1.getUrl()) > 0) { // when arg0 is greater than arg1
					response = +1;
				} else if (arg0.getUrl().compareTo(arg1.getUrl()) < 0) { // when arg0 is smaller than arg1
					response = -1;
				}
			}
		} else if (arg0 != null && arg1 == null) { // when arg0 is greater than arg1
			response = +1;
		} else if (arg0 == null && arg1 != null) { // when arg0 is smaller than arg1
			response = -1;
		}
		return response;
	}

}
