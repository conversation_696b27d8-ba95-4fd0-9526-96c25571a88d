package com.actonia.value.object;

import java.util.Arrays;

public class CustomData {
	private String[] content;
	private int index;
	private Links[] links;
	private Boolean match_found;
	private String selector;
	private String selector_type;
	private int word_count;
	private Long target_url_crawl_additional_content_id;

	public String[] getContent() {
		return content;
	}

	public void setContent(String[] content) {
		this.content = content;
	}

	public int getIndex() {
		return index;
	}

	public void setIndex(int index) {
		this.index = index;
	}

	public Links[] getLinks() {
		return links;
	}

	public void setLinks(Links[] links) {
		this.links = links;
	}

	public Boolean getMatch_found() {
		return match_found;
	}

	public void setMatch_found(Boolean match_found) {
		this.match_found = match_found;
	}

	public String getSelector() {
		return selector;
	}

	public void setSelector(String selector) {
		this.selector = selector;
	}

	public String getSelector_type() {
		return selector_type;
	}

	public void setSelector_type(String selector_type) {
		this.selector_type = selector_type;
	}

	public int getWord_count() {
		return word_count;
	}

	public void setWord_count(int word_count) {
		this.word_count = word_count;
	}

	public Long getTarget_url_crawl_additional_content_id() {
		return target_url_crawl_additional_content_id;
	}

	public void setTarget_url_crawl_additional_content_id(Long target_url_crawl_additional_content_id) {
		this.target_url_crawl_additional_content_id = target_url_crawl_additional_content_id;
	}

	@Override
	public String toString() {
		return "CustomData [content=" + Arrays.toString(content) + ", index=" + index + ", links=" + Arrays.toString(links) + ", match_found=" + match_found
				+ ", selector=" + selector + ", selector_type=" + selector_type + ", word_count=" + word_count + ", target_url_crawl_additional_content_id="
				+ target_url_crawl_additional_content_id + "]";
	}

}
