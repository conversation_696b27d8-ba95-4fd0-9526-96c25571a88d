package com.actonia.value.object;

public class JavascriptCrawlerRequest {
	private Headers headers;
	private Integer pageLoadTimeout;
	private Float waitAfterLastRequest;
	private boolean followRedirects;
	private String url;

	public Headers getHeaders() {
		return headers;
	}

	public void setHeaders(Headers headers) {
		this.headers = headers;
	}

	public Integer getPageLoadTimeout() {
		return pageLoadTimeout;
	}

	public void setPageLoadTimeout(Integer pageLoadTimeout) {
		this.pageLoadTimeout = pageLoadTimeout;
	}

	public Float getWaitAfterLastRequest() {
		return waitAfterLastRequest;
	}

	public void setWaitAfterLastRequest(Float waitAfterLastRequest) {
		this.waitAfterLastRequest = waitAfterLastRequest;
	}

	public boolean isFollowRedirects() {
		return followRedirects;
	}

	public void setFollowRedirects(boolean followRedirects) {
		this.followRedirects = followRedirects;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	@Override
	public String toString() {
		return "JavascriptCrawlerRequest [headers=" + headers + ", pageLoadTimeout=" + pageLoadTimeout + ", waitAfterLastRequest=" + waitAfterLastRequest
				+ ", followRedirects=" + followRedirects + ", url=" + url + "]";
	}

}
