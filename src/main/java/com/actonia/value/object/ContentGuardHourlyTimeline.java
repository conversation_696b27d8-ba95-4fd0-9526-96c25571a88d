package com.actonia.value.object;

public class ContentGuardHourlyTimeline {
	private Integer crawl_hour;
	private Integer total_changes;

	public Integer getCrawl_hour() {
		return crawl_hour;
	}

	public void setCrawl_hour(Integer crawl_hour) {
		this.crawl_hour = crawl_hour;
	}

	public Integer getTotal_changes() {
		return total_changes;
	}

	public void setTotal_changes(Integer total_changes) {
		this.total_changes = total_changes;
	}

	@Override
	public String toString() {
		return "HourlyTimeline [crawl_hour=" + crawl_hour + ", total_changes=" + total_changes + "]";
	}

}
