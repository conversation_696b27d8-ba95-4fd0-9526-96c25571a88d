package com.actonia.value.object;

import java.util.Comparator;

public class ContentGuardUrlChangesRemovedDescendingComparator implements Comparator<ContentGuardUrlChanges> {

	@Override
	public int compare(ContentGuardUrlChanges arg0, ContentGuardUrlChanges arg1) {

		// sort by:
		// 1) removed (ascending order)

		int response = 0;
		if (arg0 != null && arg1 != null) {
			// removed
			if (arg0.getRemoved() != null && arg1.getRemoved() == null) { // when arg0 is greater than arg1
				response = -1;
			} else if (arg0.getRemoved() == null && arg1.getRemoved() != null) { // when arg0 is smaller than arg1
				response = +1;
			} else if (arg0.getRemoved().intValue() > arg1.getRemoved().intValue()) { // when arg0 is greater than arg1
				response = -1;
			} else if (arg0.getRemoved().intValue() < arg1.getRemoved().intValue()) { // when arg0 is smaller than arg1
				response = +1;
			}
			if (response == 0) {
				// url in ascending order
				if (arg0.getUrl().compareTo(arg1.getUrl()) > 0) { // when arg0 is greater than arg1
					response = +1;
				} else if (arg0.getUrl().compareTo(arg1.getUrl()) < 0) { // when arg0 is smaller than arg1
					response = -1;
				}
			}
		} else if (arg0 != null && arg1 == null) { // when arg0 is greater than arg1
			response = -1;
		} else if (arg0 == null && arg1 != null) { // when arg0 is smaller than arg1
			response = +1;
		}
		return response;
	}

}
