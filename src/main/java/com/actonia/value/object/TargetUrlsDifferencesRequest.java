package com.actonia.value.object;

public class TargetUrlsDifferencesRequest {
	private String sequence;
	private String crawl_timestamp_1;
	private String domain_id_1;
	private String url_1;
	private String crawl_timestamp_2;
	private String domain_id_2;
	private String url_2;

	// optional, default is false
	private String url_skip_domain_name_flg;

	// optional, default is false
	private String text_case_insensitive_flg;

	public String getSequence() {
		return sequence;
	}

	public void setSequence(String sequence) {
		this.sequence = sequence;
	}

	public String getCrawl_timestamp_1() {
		return crawl_timestamp_1;
	}

	public void setCrawl_timestamp_1(String crawl_timestamp_1) {
		this.crawl_timestamp_1 = crawl_timestamp_1;
	}

	public String getDomain_id_1() {
		return domain_id_1;
	}

	public void setDomain_id_1(String domain_id_1) {
		this.domain_id_1 = domain_id_1;
	}

	public String getUrl_1() {
		return url_1;
	}

	public void setUrl_1(String url_1) {
		this.url_1 = url_1;
	}

	public String getCrawl_timestamp_2() {
		return crawl_timestamp_2;
	}

	public void setCrawl_timestamp_2(String crawl_timestamp_2) {
		this.crawl_timestamp_2 = crawl_timestamp_2;
	}

	public String getDomain_id_2() {
		return domain_id_2;
	}

	public void setDomain_id_2(String domain_id_2) {
		this.domain_id_2 = domain_id_2;
	}

	public String getUrl_2() {
		return url_2;
	}

	public void setUrl_2(String url_2) {
		this.url_2 = url_2;
	}

	public String getUrl_skip_domain_name_flg() {
		return url_skip_domain_name_flg;
	}

	public void setUrl_skip_domain_name_flg(String url_skip_domain_name_flg) {
		this.url_skip_domain_name_flg = url_skip_domain_name_flg;
	}

	public String getText_case_insensitive_flg() {
		return text_case_insensitive_flg;
	}

	public void setText_case_insensitive_flg(String text_case_insensitive_flg) {
		this.text_case_insensitive_flg = text_case_insensitive_flg;
	}

	@Override
	public String toString() {
		return "TargetUrlsDifferencesRequest [sequence=" + sequence + ", crawl_timestamp_1=" + crawl_timestamp_1 + ", domain_id_1=" + domain_id_1 + ", url_1=" + url_1
				+ ", crawl_timestamp_2=" + crawl_timestamp_2 + ", domain_id_2=" + domain_id_2 + ", url_2=" + url_2 + ", url_skip_domain_name_flg="
				+ url_skip_domain_name_flg + ", text_case_insensitive_flg=" + text_case_insensitive_flg + "]";
	}

}
