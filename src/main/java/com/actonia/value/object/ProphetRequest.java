package com.actonia.value.object;

import java.util.Arrays;

public class ProphetRequest {
	private String access_token;
	private Integer forecast_days;
	private String[] date_array;
	private Double[] value_array;

	public String getAccess_token() {
		return access_token;
	}

	public void setAccess_token(String access_token) {
		this.access_token = access_token;
	}

	public Integer getForecast_days() {
		return forecast_days;
	}

	public void setForecast_days(Integer forecast_days) {
		this.forecast_days = forecast_days;
	}

	public String[] getDate_array() {
		return date_array;
	}

	public void setDate_array(String[] date_array) {
		this.date_array = date_array;
	}

	public Double[] getValue_array() {
		return value_array;
	}

	public void setValue_array(Double[] value_array) {
		this.value_array = value_array;
	}

	@Override
	public String toString() {
		return "ProphetRequest [access_token=" + access_token + ", forecast_days=" + forecast_days + ", date_array=" + Arrays.toString(date_array) + ", value_array="
				+ Arrays.toString(value_array) + "]";
	}

}
