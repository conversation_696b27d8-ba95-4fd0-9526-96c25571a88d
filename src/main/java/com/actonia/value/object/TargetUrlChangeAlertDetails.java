package com.actonia.value.object;

import java.util.List;

public class TargetUrlChangeAlertDetails {
	private String url;
	private Integer totalChanges;
	private List<ChangeIndicatorPreviousCurrent> changeIndicatorPreviousCurrentList;

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public Integer getTotalChanges() {
		return totalChanges;
	}

	public void setTotalChanges(Integer totalChanges) {
		this.totalChanges = totalChanges;
	}

	public List<ChangeIndicatorPreviousCurrent> getChangeIndicatorPreviousCurrentList() {
		return changeIndicatorPreviousCurrentList;
	}

	public void setChangeIndicatorPreviousCurrentList(List<ChangeIndicatorPreviousCurrent> changeIndicatorPreviousCurrentList) {
		this.changeIndicatorPreviousCurrentList = changeIndicatorPreviousCurrentList;
	}

	@Override
	public String toString() {
		return "TargetUrlChangeAlertDetails [url=" + url + ", totalChanges=" + totalChanges + ", changeIndicatorPreviousCurrentList="
				+ changeIndicatorPreviousCurrentList + "]";
	}

}
