package com.actonia.value.object;

import com.actonia.IConstants;
import com.actonia.utils.TargetUrlChangeUtils;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.StringJoiner;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
public class TargetUrlChangeRequest {
	private String access_token;
	private Integer domain_id;
	private String start_crawl_timestamp; // 2020-10-27 12:34:56
	private String end_crawl_timestamp; // 2020-10-27 13:45:23
	private Integer page_number;
	private Integer rows_per_page;
	private Integer sort_by;
	private String[] change_indicators;
	private Integer[] page_tag_ids;
	private TargetUrlChangeContentType[] content_types;
	private UrlFilter[] urls;
	private ResponseCodeFilter[] response_codes;
	private Boolean debug_ind;
	private String summary;
	private Boolean download_all_ind;

	private Map<String, Integer> changeIndicatorIdMap; // <indicator, id>

	public String getFilterCondition() {
		StringBuilder stringBuilder = new StringBuilder();
		String pageTagIdsPredicate = getPageTagIdsPredicate(this.domain_id, this.page_tag_ids);
		if (StringUtils.isNotBlank(pageTagIdsPredicate)) {
			stringBuilder.append(pageTagIdsPredicate);
		}
		String urlPredicate = TargetUrlChangeUtils.getUrlPredicate(this.domain_id, this.urls);
		if (StringUtils.isNotBlank(urlPredicate) && !" and url like '%%'".equals(urlPredicate)) {
			stringBuilder.append(urlPredicate);
		}
		String contentTypePredicate = TargetUrlChangeUtils.getContentTypePredicate(this.domain_id, this.content_types);
		if (StringUtils.isNotBlank(contentTypePredicate)) {
			stringBuilder.append(" and ").append(contentTypePredicate);
		}
		String responseCodes = getResponseCodePredicate(this.response_codes);
		final String changeIndicatorsPredicate = getChangeIndicatorsPredicate(this.change_indicators, responseCodes);
		if (StringUtils.isNotBlank(changeIndicatorsPredicate)) {
			stringBuilder.append(changeIndicatorsPredicate);
		}
		return stringBuilder.toString();
	}

	public String getChangeIndicatorsCondition() {
		if (change_indicators == null || change_indicators.length == 0) return IConstants.EMPTY_STRING;
		return "'" + Arrays.stream(this.change_indicators).map(indicator -> this.changeIndicatorIdMap.get(indicator).toString())
				.collect(Collectors.joining("', '")) + "'";
	}

	private String getChangeIndicatorsPredicate(String[] changeIndicatorArray, String responseCodes) {
		if (changeIndicatorArray == null || changeIndicatorArray.length == 0) return IConstants.EMPTY_STRING;
		StringJoiner joiner = new StringJoiner(", ");
		for (String indicator : changeIndicatorArray) {
			final Integer id = this.changeIndicatorIdMap.get(indicator);
			joiner.add("'" + id + "'");
		}
		final String join = joiner.toString();
		return responseCodes + " and chg_id in (" + join + ")";
	}

	private static String getResponseCodePredicate(ResponseCodeFilter[] responseCodesArray) {
		if (responseCodesArray == null || responseCodesArray.length == 0) return IConstants.EMPTY_STRING;
		final String responseCodePredicate = Arrays.stream(responseCodesArray).map(responseCodeFilter -> "(prev_response_code = " + responseCodeFilter.getResponse_code_previous() + " and curr_response_code = " + responseCodeFilter.getResponse_code_current() + ")")
				.collect(Collectors.joining(" or "));
		return " and (chg_id = 80 and (" + responseCodePredicate + "))";
	}

	private static String getPageTagIdsPredicate(int domainId, Integer[] pageTagIdArray) {
		if (pageTagIdArray == null || pageTagIdArray.length == 0) return IConstants.EMPTY_STRING;
		final String pageTagIdsPredicate = Arrays.stream(pageTagIdArray)
				.map(pageTagId -> "(dictGetUInt64('file_dic_page_tag_murmur3hash', 'target_url_id', (toUInt64(" + domainId + "), toUInt64(" + pageTagId + "), murmurHash3_64(url))) > 0)")
				.collect(Collectors.joining(" or "));
		return " and (" + pageTagIdsPredicate + ") ";
	}
}
