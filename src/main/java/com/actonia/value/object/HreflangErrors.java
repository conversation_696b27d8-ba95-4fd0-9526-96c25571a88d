package com.actonia.value.object;

public class HreflangErrors {
	AlternateUrl alternate_url;
	HasMultipleDefaults has_multiple_defaults;
	HreflangKeysWithMultipleEntries hreflang_keys_with_multiple_entries;
	InvalidLanguages invalid_languages;
	InvalidRegions invalid_regions;
	LanguageMissingEntries language_missing_entries;
	LanguagesMissingStandaloneEntries languages_missing_standalone_entries;
	NoReturnTagPages no_return_tag_pages;
	RegionMissingEntries region_missing_entries;
	Boolean is_default;
	Boolean is_self_reference;

	public AlternateUrl getAlternate_url() {
		return alternate_url;
	}

	public void setAlternate_url(AlternateUrl alternate_url) {
		this.alternate_url = alternate_url;
	}

	public HasMultipleDefaults getHas_multiple_defaults() {
		return has_multiple_defaults;
	}

	public void setHas_multiple_defaults(HasMultipleDefaults has_multiple_defaults) {
		this.has_multiple_defaults = has_multiple_defaults;
	}

	public HreflangKeysWithMultipleEntries getHreflang_keys_with_multiple_entries() {
		return hreflang_keys_with_multiple_entries;
	}

	public void setHreflang_keys_with_multiple_entries(HreflangKeysWithMultipleEntries hreflang_keys_with_multiple_entries) {
		this.hreflang_keys_with_multiple_entries = hreflang_keys_with_multiple_entries;
	}

	public InvalidLanguages getInvalid_languages() {
		return invalid_languages;
	}

	public void setInvalid_languages(InvalidLanguages invalid_languages) {
		this.invalid_languages = invalid_languages;
	}

	public InvalidRegions getInvalid_regions() {
		return invalid_regions;
	}

	public void setInvalid_regions(InvalidRegions invalid_regions) {
		this.invalid_regions = invalid_regions;
	}

	public LanguageMissingEntries getLanguage_missing_entries() {
		return language_missing_entries;
	}

	public void setLanguage_missing_entries(LanguageMissingEntries language_missing_entries) {
		this.language_missing_entries = language_missing_entries;
	}

	public LanguagesMissingStandaloneEntries getLanguages_missing_standalone_entries() {
		return languages_missing_standalone_entries;
	}

	public void setLanguages_missing_standalone_entries(LanguagesMissingStandaloneEntries languages_missing_standalone_entries) {
		this.languages_missing_standalone_entries = languages_missing_standalone_entries;
	}

	public NoReturnTagPages getNo_return_tag_pages() {
		return no_return_tag_pages;
	}

	public void setNo_return_tag_pages(NoReturnTagPages no_return_tag_pages) {
		this.no_return_tag_pages = no_return_tag_pages;
	}

	public RegionMissingEntries getRegion_missing_entries() {
		return region_missing_entries;
	}

	public void setRegion_missing_entries(RegionMissingEntries region_missing_entries) {
		this.region_missing_entries = region_missing_entries;
	}

	public Boolean getIs_default() {
		return is_default;
	}

	public void setIs_default(Boolean is_default) {
		this.is_default = is_default;
	}

	public Boolean getIs_self_reference() {
		return is_self_reference;
	}

	public void setIs_self_reference(Boolean is_self_reference) {
		this.is_self_reference = is_self_reference;
	}

	@Override
	public String toString() {
		return "HreflangErrors [alternate_url=" + alternate_url + ", has_multiple_defaults=" + has_multiple_defaults + ", hreflang_keys_with_multiple_entries="
				+ hreflang_keys_with_multiple_entries + ", invalid_languages=" + invalid_languages + ", invalid_regions=" + invalid_regions
				+ ", language_missing_entries=" + language_missing_entries + ", languages_missing_standalone_entries=" + languages_missing_standalone_entries
				+ ", no_return_tag_pages=" + no_return_tag_pages + ", region_missing_entries=" + region_missing_entries + ", is_default=" + is_default
				+ ", is_self_reference=" + is_self_reference + "]";
	}

}
