package com.actonia.value.object;

import java.math.BigInteger;
import java.util.Arrays;
import java.util.Date;

public class SharedCountsCrossReferenceValueObject {
	private Date trackDate;
	private String url;
	private BigInteger urlHash;
	private Object[] domainIds;
	private Integer sign;
	private String connectionUrl;

	public Date getTrackDate() {
		return trackDate;
	}

	public void setTrackDate(Date trackDate) {
		this.trackDate = trackDate;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public Object[] getDomainIds() {
		return domainIds;
	}

	public void setDomainIds(Object[] domainIds) {
		this.domainIds = domainIds;
	}

	public Integer getSign() {
		return sign;
	}

	public void setSign(Integer sign) {
		this.sign = sign;
	}

	public String getConnectionUrl() {
		return connectionUrl;
	}

	public void setConnectionUrl(String connectionUrl) {
		this.connectionUrl = connectionUrl;
	}

	public BigInteger getUrlHash() {
		return urlHash;
	}

	public void setUrlHash(BigInteger urlHash) {
		this.urlHash = urlHash;
	}

	@Override
	public String toString() {
		return "SharedCountsCrossReferenceValueObject [trackDate=" + trackDate + ", url=" + url + ", urlHash=" + urlHash + ", domainIds="
				+ Arrays.toString(domainIds) + "]";
	}

}
