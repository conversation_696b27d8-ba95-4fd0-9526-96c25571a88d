package com.actonia.value.object;

import java.util.Arrays;

public class MarketMatchingRequest {
	private String access_token;
	private String test_market;
	private String pre_period_start_date;
	private String pre_period_end_date;
	private String post_period_start_date;
	private String post_period_end_date;
	private Integer number_of_best_matches;
	private AreaDateValue[] area_date_value_array;

	public String getAccess_token() {
		return access_token;
	}

	public void setAccess_token(String access_token) {
		this.access_token = access_token;
	}

	public String getTest_market() {
		return test_market;
	}

	public void setTest_market(String test_market) {
		this.test_market = test_market;
	}

	public String getPre_period_start_date() {
		return pre_period_start_date;
	}

	public void setPre_period_start_date(String pre_period_start_date) {
		this.pre_period_start_date = pre_period_start_date;
	}

	public String getPre_period_end_date() {
		return pre_period_end_date;
	}

	public void setPre_period_end_date(String pre_period_end_date) {
		this.pre_period_end_date = pre_period_end_date;
	}

	public String getPost_period_start_date() {
		return post_period_start_date;
	}

	public void setPost_period_start_date(String post_period_start_date) {
		this.post_period_start_date = post_period_start_date;
	}

	public String getPost_period_end_date() {
		return post_period_end_date;
	}

	public void setPost_period_end_date(String post_period_end_date) {
		this.post_period_end_date = post_period_end_date;
	}

	public Integer getNumber_of_best_matches() {
		return number_of_best_matches;
	}

	public void setNumber_of_best_matches(Integer number_of_best_matches) {
		this.number_of_best_matches = number_of_best_matches;
	}

	public AreaDateValue[] getArea_date_value_array() {
		return area_date_value_array;
	}

	public void setArea_date_value_array(AreaDateValue[] area_date_value_array) {
		this.area_date_value_array = area_date_value_array;
	}

	@Override
	public String toString() {
		return "MarketMatchingRequest [access_token=" + access_token + ", test_market=" + test_market + ", pre_period_start_date=" + pre_period_start_date
				+ ", pre_period_end_date=" + pre_period_end_date + ", post_period_start_date=" + post_period_start_date + ", post_period_end_date="
				+ post_period_end_date + ", number_of_best_matches=" + number_of_best_matches + ", area_date_value_array=" + Arrays.toString(area_date_value_array)
				+ "]";
	}

}
