package com.actonia.value.object;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class ScrapyCrawlerRequest {
	private List<String> url;
	private Boolean response_as_html;
	private Boolean html_in_json;
	@SerializedName("scrapy_settings")
	private ScrapySettings scrapySettings;
	private SiteclaritySettings siteclarity_settings;
	@SerializedName("domain_id_i")
	private String domainId;

	@Data
	public static class ScrapySettings {

		@SerializedName("RETRY_ENABLED")
		private boolean retryEnabled = true;

		@SerializedName("REDIRECT_MAX_TIMES")
		private int redirectMaxTimes = 20;

		@SerializedName("RETRY_TIMES")
		private int retryTimes = 5;

		@SerializedName("DEFAULT_REQUEST_HEADERS")
		private Map<String, String> defaultRequestHeaders;

		@SerializedName("REDIRECT_ENABLED")
		private boolean redirectEnabled = true;

		@SerializedName("ROBOTSTXT_OBEY")
		private boolean robotstxtObey = true;

		@SerializedName("USER_AGENT")
		private String userAgent;

		@SerializedName("SPLASH_URL")
		private String splashUrl;

	}
}
