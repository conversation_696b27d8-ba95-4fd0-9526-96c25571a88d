package com.actonia.value.object;

public class PosteriorInference {
	private double actual;
	private double alpha; // determine the size of the confidence intervals, default is 0.05 for 95% CI, if 0.1, 90% CI
	private double prediction;
	private double prediction_lower; // lower bound of confidence interval
	private double prediction_upper; // upper bound of confidence interval
	private double prediction_standard_derivation;
	private double absolute_effect;
	private double absolute_effect_lower; // lower bound of confidence interval
	private double absolute_effect_upper; // upper bound of confidence interval
	private double absolute_effect_standard_derivation;
	private double relative_effect;
	private double relative_effect_lower; // lower bound of confidence interval
	private double relative_effect_upper; // upper bound of confidence interval
	private double relative_effect_standard_derivation;
	private double tail_area_probability; // Bayesian one-sided tail-area probability

	public double getActual() {
		return actual;
	}

	public void setActual(double actual) {
		this.actual = actual;
	}

	public double getAlpha() {
		return alpha;
	}

	public void setAlpha(double alpha) {
		this.alpha = alpha;
	}

	public double getPrediction() {
		return prediction;
	}

	public void setPrediction(double prediction) {
		this.prediction = prediction;
	}

	public double getPrediction_lower() {
		return prediction_lower;
	}

	public void setPrediction_lower(double prediction_lower) {
		this.prediction_lower = prediction_lower;
	}

	public double getPrediction_upper() {
		return prediction_upper;
	}

	public void setPrediction_upper(double prediction_upper) {
		this.prediction_upper = prediction_upper;
	}

	public double getPrediction_standard_derivation() {
		return prediction_standard_derivation;
	}

	public void setPrediction_standard_derivation(double prediction_standard_derivation) {
		this.prediction_standard_derivation = prediction_standard_derivation;
	}

	public double getAbsolute_effect() {
		return absolute_effect;
	}

	public void setAbsolute_effect(double absolute_effect) {
		this.absolute_effect = absolute_effect;
	}

	public double getAbsolute_effect_lower() {
		return absolute_effect_lower;
	}

	public void setAbsolute_effect_lower(double absolute_effect_lower) {
		this.absolute_effect_lower = absolute_effect_lower;
	}

	public double getAbsolute_effect_upper() {
		return absolute_effect_upper;
	}

	public void setAbsolute_effect_upper(double absolute_effect_upper) {
		this.absolute_effect_upper = absolute_effect_upper;
	}

	public double getAbsolute_effect_standard_derivation() {
		return absolute_effect_standard_derivation;
	}

	public void setAbsolute_effect_standard_derivation(double absolute_effect_standard_derivation) {
		this.absolute_effect_standard_derivation = absolute_effect_standard_derivation;
	}

	public double getRelative_effect() {
		return relative_effect;
	}

	public void setRelative_effect(double relative_effect) {
		this.relative_effect = relative_effect;
	}

	public double getRelative_effect_lower() {
		return relative_effect_lower;
	}

	public void setRelative_effect_lower(double relative_effect_lower) {
		this.relative_effect_lower = relative_effect_lower;
	}

	public double getRelative_effect_upper() {
		return relative_effect_upper;
	}

	public void setRelative_effect_upper(double relative_effect_upper) {
		this.relative_effect_upper = relative_effect_upper;
	}

	public double getRelative_effect_standard_derivation() {
		return relative_effect_standard_derivation;
	}

	public void setRelative_effect_standard_derivation(double relative_effect_standard_derivation) {
		this.relative_effect_standard_derivation = relative_effect_standard_derivation;
	}

	public double getTail_area_probability() {
		return tail_area_probability;
	}

	public void setTail_area_probability(double tail_area_probability) {
		this.tail_area_probability = tail_area_probability;
	}

	@Override
	public String toString() {
		return "PosteriorInference [actual=" + actual + ", alpha=" + alpha + ", prediction=" + prediction + ", prediction_lower=" + prediction_lower
				+ ", prediction_upper=" + prediction_upper + ", prediction_standard_derivation=" + prediction_standard_derivation + ", absolute_effect="
				+ absolute_effect + ", absolute_effect_lower=" + absolute_effect_lower + ", absolute_effect_upper=" + absolute_effect_upper
				+ ", absolute_effect_standard_derivation=" + absolute_effect_standard_derivation + ", relative_effect=" + relative_effect + ", relative_effect_lower="
				+ relative_effect_lower + ", relative_effect_upper=" + relative_effect_upper + ", relative_effect_standard_derivation="
				+ relative_effect_standard_derivation + ", tail_area_probability=" + tail_area_probability + "]";
	}

}
