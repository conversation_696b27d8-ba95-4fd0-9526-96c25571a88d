package com.actonia.value.object;

public class ContentGuardResourceRequest {
	private String access_token;
	private Integer domain_id;
	private Long group_id;

	// timeline
	private String start_crawl_date; // 2020-10-27 = October 27th, 2020
	private String end_crawl_date; // 2020-10-27 = October 27th, 2020

	// domain change summary
	private String crawl_date; // 2020-10-27 = October 27th, 2020
	private Integer crawl_hour; // 00 = 12:00 am, 01 = 1:00 am, ..., 22 = 10:00 pm, 23 = 11:00 pm
	private Integer page_number;
	private Integer rows_per_page;
	private Integer sort_by;
	private Boolean return_details;
	private String filter_url;

	// URL change details
	private String url;
	private String crawl_timestamp;

	// Indicator URL list
	private String filter_change_indicator;

	// usage, format "yyyy-mm-dd"
	private String start_usage_date;
	private String end_usage_date;

	private Boolean useCustomIndicators;

	public String getAccess_token() {
		return access_token;
	}

	public void setAccess_token(String access_token) {
		this.access_token = access_token;
	}

	public Integer getDomain_id() {
		return domain_id;
	}

	public void setDomain_id(Integer domain_id) {
		this.domain_id = domain_id;
	}

	public Long getGroup_id() {
		return group_id;
	}

	public void setGroup_id(Long group_id) {
		this.group_id = group_id;
	}

	public String getStart_crawl_date() {
		return start_crawl_date;
	}

	public void setStart_crawl_date(String start_crawl_date) {
		this.start_crawl_date = start_crawl_date;
	}

	public String getEnd_crawl_date() {
		return end_crawl_date;
	}

	public void setEnd_crawl_date(String end_crawl_date) {
		this.end_crawl_date = end_crawl_date;
	}

	public String getCrawl_date() {
		return crawl_date;
	}

	public void setCrawl_date(String crawl_date) {
		this.crawl_date = crawl_date;
	}

	public Integer getCrawl_hour() {
		return crawl_hour;
	}

	public void setCrawl_hour(Integer crawl_hour) {
		this.crawl_hour = crawl_hour;
	}

	public Integer getPage_number() {
		return page_number;
	}

	public void setPage_number(Integer page_number) {
		this.page_number = page_number;
	}

	public Integer getRows_per_page() {
		return rows_per_page;
	}

	public void setRows_per_page(Integer rows_per_page) {
		this.rows_per_page = rows_per_page;
	}

	public Integer getSort_by() {
		return sort_by;
	}

	public void setSort_by(Integer sort_by) {
		this.sort_by = sort_by;
	}

	public Boolean getReturn_details() {
		return return_details;
	}

	public void setReturn_details(Boolean return_details) {
		this.return_details = return_details;
	}

	public String getFilter_url() {
		return filter_url;
	}

	public void setFilter_url(String filter_url) {
		this.filter_url = filter_url;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getCrawl_timestamp() {
		return crawl_timestamp;
	}

	public void setCrawl_timestamp(String crawl_timestamp) {
		this.crawl_timestamp = crawl_timestamp;
	}

	public String getFilter_change_indicator() {
		return filter_change_indicator;
	}

	public void setFilter_change_indicator(String filter_change_indicator) {
		this.filter_change_indicator = filter_change_indicator;
	}

	public String getStart_usage_date() {
		return start_usage_date;
	}

	public void setStart_usage_date(String start_usage_date) {
		this.start_usage_date = start_usage_date;
	}

	public String getEnd_usage_date() {
		return end_usage_date;
	}

	public void setEnd_usage_date(String end_usage_date) {
		this.end_usage_date = end_usage_date;
	}

	public Boolean getUseCustomIndicators() {
		return useCustomIndicators;
	}

	@Override
	public String toString() {
		return "ContentGuardResourceRequest [access_token=" + access_token + ", domain_id=" + domain_id + ", group_id=" + group_id + ", start_crawl_date="
				+ start_crawl_date + ", end_crawl_date=" + end_crawl_date + ", crawl_date=" + crawl_date + ", crawl_hour=" + crawl_hour + ", page_number=" + page_number
				+ ", rows_per_page=" + rows_per_page + ", sort_by=" + sort_by + ", return_details=" + return_details + ", filter_url=" + filter_url + ", url=" + url
				+ ", crawl_timestamp=" + crawl_timestamp + ", filter_change_indicator=" + filter_change_indicator + ", start_usage_date=" + start_usage_date
				+ ", end_usage_date=" + end_usage_date + ", useCustomIndicators=" + useCustomIndicators + "]";
	}

}
