package com.actonia.value.object;

public class TargetUrlChangeTotal {
	private int totalRows;
	private boolean isLastPage;

	public int getTotalRows() {
		return totalRows;
	}

	public void setTotalRows(int totalRows) {
		this.totalRows = totalRows;
	}

	public boolean isLastPage() {
		return isLastPage;
	}

	public void setLastPage(boolean isLastPage) {
		this.isLastPage = isLastPage;
	}

	@Override
	public String toString() {
		return "TargetUrlChangeTotal [totalRows=" + totalRows + ", isLastPage=" + isLastPage + "]";
	}

}
