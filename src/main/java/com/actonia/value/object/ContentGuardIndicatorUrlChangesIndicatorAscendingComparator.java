package com.actonia.value.object;

import java.util.Comparator;

public class ContentGuardIndicatorUrlChangesIndicatorAscendingComparator implements Comparator<ContentGuardIndicatorUrlChanges> {

	@Override
	public int compare(ContentGuardIndicatorUrlChanges arg0, ContentGuardIndicatorUrlChanges arg1) {

		// sort by:
		// 1) indicator (ascending order)
		// 2) url (ascending order)

		int response = 0;
		if (arg0 != null && arg1 != null) {
			// indicator
			if (arg0.getChange_indicator() != null && arg1.getChange_indicator() == null) { // when arg0 is greater than arg1
				response = +1;
			} else if (arg0.getChange_indicator() == null && arg1.getChange_indicator() != null) { // when arg0 is smaller than arg1
				response = -1;
			} else if (arg0.getChange_indicator().compareTo(arg1.getChange_indicator()) > 0) { // when arg0 is greater than arg1
				response = +1;
			} else if (arg0.getChange_indicator().compareTo(arg1.getChange_indicator()) < 0) { // when arg0 is smaller than arg1
				response = -1;
			}
			if (response == 0) {
				// url in ascending order
				if (arg0.getUrl().compareTo(arg1.getUrl()) > 0) { // when arg0 is greater than arg1
					response = +1;
				} else if (arg0.getUrl().compareTo(arg1.getUrl()) < 0) { // when arg0 is smaller than arg1
					response = -1;
				}
			}
		} else if (arg0 != null && arg1 == null) { // when arg0 is greater than arg1
			response = +1;
		} else if (arg0 == null && arg1 != null) { // when arg0 is smaller than arg1
			response = -1;
		}
		return response;
	}

}
