package com.actonia.value.object;

import java.util.Arrays;

public class CrawlerResponse implements Cloneable {

	@Override
	public CrawlerResponse clone() throws CloneNotSupportedException {
		return (CrawlerResponse) super.clone();
	}

	private AltImgList[] alt_img_list;
	private AlternateLinks[] alternate_links;
	private Boolean amphtml_flag;
	private String amphtml_href;
	private String analyzed_url_flg_s;
	private String analyzed_url_s;
	private String archive_flg;
	private String archive_flg_x_tag;
	private String blocked_by_robots;
	private String canonical;
	private String canonical_flg;
	private Boolean canonical_header_flag;
	private String canonical_header_type;
	private String canonical_type;
	private String canonical_url_is_consistent;
	private String content_type;
	private Integer count_of_objects;
	private CustomData[] custom_data;
	private String description;
	private String description_flg;
	private Integer description_length;
	private String description_simhash;
	private Integer document_size;
	private String download_latency;
	private String download_time;
	private String error_message;
	private Integer final_response_code;
	private String folder_level_1;
	private String folder_level_2;
	private String folder_level_3;
	private Integer folder_level_count;
	private String follow_flg;
	private String follow_flg_x_tag;
	private String[] h1;
	private Integer h1_count;
	private String h1_flg;
	private Integer h1_length;
	private String[] h1_md5;
	private String h1_simhash;
	private String[] h2;
	private String h2_simhash;
	private Boolean header_noarchive;
	private Boolean header_nofollow;
	private Boolean header_noindex;
	private Boolean header_noodp;
	private Boolean header_nosnippet;
	private Boolean header_noydir;
	private HreflangErrors hreflang_errors;
	private HreflangLinks[] hreflang_links;
	private Integer hreflang_links_out_count;
	private Integer hreflang_url_count;
	private String index_flg;
	private String index_flg_x_tag;
	private Boolean indexable;
	private String[] insecure_resources;
	private Boolean insecure_resources_flag;
	private Boolean long_redirect;
	private String meta_charset;
	private String meta_content_type;
	private Boolean meta_disabled_sitelinks;
	private Boolean meta_noodp;
	private Boolean meta_nosnippet;
	private Boolean meta_noydir;
	private Boolean meta_redirect;
	private Boolean mixed_redirects;
	private Boolean mobile_rel_alternate_url_is_consistent;
	private Boolean noodp;
	private Boolean nosnippet;
	private Boolean noydir;
	private OgMarkup[] og_markup;
	private Boolean og_markup_flag;
	private Integer og_markup_length;
	private Integer outlink_count;
	private Boolean page_1;
	private PageAnalysisRule page_analysis_rule_10_b;
	private PageAnalysisRule page_analysis_rule_11_b;
	private PageAnalysisRule page_analysis_rule_12_b;
	private PageAnalysisRule page_analysis_rule_13_b;
	private PageAnalysisRule page_analysis_rule_14_b;
	private PageAnalysisRule page_analysis_rule_15_b;
	private PageAnalysisRule page_analysis_rule_16_b;
	private PageAnalysisRule page_analysis_rule_17_b;
	private PageAnalysisRule page_analysis_rule_18_b;
	private PageAnalysisRule page_analysis_rule_19_b;
	private PageAnalysisRule page_analysis_rule_1_b;
	private PageAnalysisRule page_analysis_rule_20_b;
	private PageAnalysisRule page_analysis_rule_21_b;
	private PageAnalysisRule page_analysis_rule_22_b;
	private PageAnalysisRule page_analysis_rule_23_b;
	private PageAnalysisRule page_analysis_rule_24_b;
	private PageAnalysisRule page_analysis_rule_25_b;
	private PageAnalysisRule page_analysis_rule_26_b;
	private PageAnalysisRule page_analysis_rule_27_b;
	private PageAnalysisRule page_analysis_rule_28_b;
	private PageAnalysisRule page_analysis_rule_29_b;
	private PageAnalysisRule page_analysis_rule_2_b;
	private PageAnalysisRule page_analysis_rule_30_b;
	private PageAnalysisRule page_analysis_rule_31_b;
	private PageAnalysisRule page_analysis_rule_32_b;
	private PageAnalysisRule page_analysis_rule_33_b;
	private PageAnalysisRule page_analysis_rule_34_b;
	private PageAnalysisRule page_analysis_rule_35_b;
	private PageAnalysisRule page_analysis_rule_36_b;
	private PageAnalysisRule page_analysis_rule_37_b;
	private PageAnalysisRule page_analysis_rule_38_b;
	private PageAnalysisRule page_analysis_rule_39_b;
	private PageAnalysisRule page_analysis_rule_3_b;
	private PageAnalysisRule page_analysis_rule_40_b;
	private PageAnalysisRule page_analysis_rule_41_b;
	private PageAnalysisRule page_analysis_rule_42_b;
	private PageAnalysisRule page_analysis_rule_43_b;
	private PageAnalysisRule page_analysis_rule_44_b;
	private PageAnalysisRule page_analysis_rule_45_b;
	private PageAnalysisRule page_analysis_rule_46_b;
	private PageAnalysisRule page_analysis_rule_47_b;
	private PageAnalysisRule page_analysis_rule_48_b;
	private PageAnalysisRule page_analysis_rule_49_b;
	private PageAnalysisRule page_analysis_rule_4_b;
	private PageAnalysisRule page_analysis_rule_50_b;
	private PageAnalysisRule page_analysis_rule_51_b;
	private PageAnalysisRule page_analysis_rule_52_b;
	private PageAnalysisRule page_analysis_rule_53_b;
	private PageAnalysisRule page_analysis_rule_54_b;
	private PageAnalysisRule page_analysis_rule_55_b;
	private PageAnalysisRule page_analysis_rule_56_b;
	private PageAnalysisRule page_analysis_rule_57_b;
	private PageAnalysisRule page_analysis_rule_58_b;
	private PageAnalysisRule page_analysis_rule_59_b;
	private PageAnalysisRule page_analysis_rule_5_b;
	private PageAnalysisRule page_analysis_rule_60_b;
	private PageAnalysisRule page_analysis_rule_61_b;
	private PageAnalysisRule page_analysis_rule_62_b;
	private PageAnalysisRule page_analysis_rule_63_b;
	private PageAnalysisRule page_analysis_rule_64_b;
	private PageAnalysisRule page_analysis_rule_65_b;
	private PageAnalysisRule page_analysis_rule_6_b;
	private PageAnalysisRule page_analysis_rule_7_b;
	private PageAnalysisRule page_analysis_rule_8_b;
	private PageAnalysisRule page_analysis_rule_9_b;
	private PageLink[] page_link;
	private Boolean page_timeout_flag;
	private Boolean paginated;
	private PaginationLinks[] pagination_links;
	private String protocol;
	private Boolean redirect_blocked;
	private String redirect_blocked_reason;
	private RedirectChain[] redirect_chain;
	private String redirect_final_url;
	private Boolean redirect_flg;
	private Integer redirect_times;
	private String rel_next_html_url;
	private Boolean rel_next_url_is_consistent;
	private Boolean rel_prev_url_is_consistent;
	private String request_headers;
	private String request_time;
	private String response_code;
	private ResponseHeaders[] response_headers;
	private Boolean retry_attempted;
	private String robots;
	private String robots_contents;
	private Boolean robots_contents_x_tag;
	private String robots_flg;
	private String robots_flg_x_tag;
	private String server_response_time;
	private String[] source_url;
	private String splash_took;
	private StructuredData structured_data;
	private String title;
	private String title_flg;
	private Integer title_length;
	private String[] title_md5;
	private String title_simhash;
	private Integer twitter_description_length;
	private TwitterMarkup[] twitter_markup;
	private Boolean twitter_markup_flag;
	private Integer twitter_markup_length;
	private Integer url_length;
	private String valid_twitter_card;
	private String viewport_content;
	private Boolean viewport_flag;
	private PageAnalysisRule page_analysis_rule_66_b;
	private PageAnalysisRule page_analysis_rule_67_b;
	private PageAnalysisRule page_analysis_rule_68_b;
	private PageAnalysisRule page_analysis_rule_69_b;
	private PageAnalysisRule page_analysis_rule_70_b;
	private PageAnalysisRule page_analysis_rule_71_b;
	private PageAnalysisRule page_analysis_rule_72_b;
	private PageAnalysisRule page_analysis_rule_73_b;
	private PageAnalysisRule page_analysis_rule_74_b;
	private PageAnalysisRule page_analysis_rule_75_b;
	private PageAnalysisRule page_analysis_rule_76_b;
	private PageAnalysisRule page_analysis_rule_77_b;
	private PageAnalysisRule page_analysis_rule_78_b;
	private PageAnalysisRule page_analysis_rule_79_b;
	private PageAnalysisRule page_analysis_rule_80_b;
	private PageAnalysisRule page_analysis_rule_81_b;
	private PageAnalysisRule page_analysis_rule_82_b;
	private PageAnalysisRule page_analysis_rule_83_b;
	private PageAnalysisRule page_analysis_rule_84_b;
	private PageAnalysisRule page_analysis_rule_85_b;
	private PageAnalysisRule page_analysis_rule_86_b;
	private PageAnalysisRule page_analysis_rule_87_b;
	private PageAnalysisRule page_analysis_rule_88_b;
	private PageAnalysisRule page_analysis_rule_89_b;
	private PageAnalysisRule page_analysis_rule_90_b;
	private PageAnalysisRule page_analysis_rule_91_b;
	private PageAnalysisRule page_analysis_rule_92_b;
	private PageAnalysisRule page_analysis_rule_93_b;
	private PageAnalysisRule page_analysis_rule_94_b;
	private PageAnalysisRule page_analysis_rule_95_b;
	private PageAnalysisRule page_analysis_rule_96_b;
	private PageAnalysisRule page_analysis_rule_97_b;
	private PageAnalysisRule page_analysis_rule_98_b;
	private PageAnalysisRule page_analysis_rule_99_b;
	private PageAnalysisRule page_analysis_rule_100_b;
	private PageAnalysisRule page_analysis_rule_101_b;
	private PageAnalysisRule page_analysis_rule_102_b;
	private PageAnalysisRule page_analysis_rule_103_b;
	private PageAnalysisRule page_analysis_rule_104_b;
	private PageAnalysisRule page_analysis_rule_105_b;
	private String file_name;
	private String base_tag;
	private Boolean base_tag_flag;
	private String base_tag_target;

	public AltImgList[] getAlt_img_list() {
		return alt_img_list;
	}

	public void setAlt_img_list(AltImgList[] alt_img_list) {
		this.alt_img_list = alt_img_list;
	}

	public AlternateLinks[] getAlternate_links() {
		return alternate_links;
	}

	public void setAlternate_links(AlternateLinks[] alternate_links) {
		this.alternate_links = alternate_links;
	}

	public Boolean getAmphtml_flag() {
		return amphtml_flag;
	}

	public void setAmphtml_flag(Boolean amphtml_flag) {
		this.amphtml_flag = amphtml_flag;
	}

	public String getAmphtml_href() {
		return amphtml_href;
	}

	public void setAmphtml_href(String amphtml_href) {
		this.amphtml_href = amphtml_href;
	}

	public String getAnalyzed_url_flg_s() {
		return analyzed_url_flg_s;
	}

	public void setAnalyzed_url_flg_s(String analyzed_url_flg_s) {
		this.analyzed_url_flg_s = analyzed_url_flg_s;
	}

	public String getAnalyzed_url_s() {
		return analyzed_url_s;
	}

	public void setAnalyzed_url_s(String analyzed_url_s) {
		this.analyzed_url_s = analyzed_url_s;
	}

	public String getArchive_flg() {
		return archive_flg;
	}

	public void setArchive_flg(String archive_flg) {
		this.archive_flg = archive_flg;
	}

	public String getArchive_flg_x_tag() {
		return archive_flg_x_tag;
	}

	public void setArchive_flg_x_tag(String archive_flg_x_tag) {
		this.archive_flg_x_tag = archive_flg_x_tag;
	}

	public String getBlocked_by_robots() {
		return blocked_by_robots;
	}

	public void setBlocked_by_robots(String blocked_by_robots) {
		this.blocked_by_robots = blocked_by_robots;
	}

	public String getCanonical() {
		return canonical;
	}

	public void setCanonical(String canonical) {
		this.canonical = canonical;
	}

	public String getCanonical_flg() {
		return canonical_flg;
	}

	public void setCanonical_flg(String canonical_flg) {
		this.canonical_flg = canonical_flg;
	}

	public Boolean getCanonical_header_flag() {
		return canonical_header_flag;
	}

	public void setCanonical_header_flag(Boolean canonical_header_flag) {
		this.canonical_header_flag = canonical_header_flag;
	}

	public String getCanonical_header_type() {
		return canonical_header_type;
	}

	public void setCanonical_header_type(String canonical_header_type) {
		this.canonical_header_type = canonical_header_type;
	}

	public String getCanonical_type() {
		return canonical_type;
	}

	public void setCanonical_type(String canonical_type) {
		this.canonical_type = canonical_type;
	}

	public String getCanonical_url_is_consistent() {
		return canonical_url_is_consistent;
	}

	public void setCanonical_url_is_consistent(String canonical_url_is_consistent) {
		this.canonical_url_is_consistent = canonical_url_is_consistent;
	}

	public String getContent_type() {
		return content_type;
	}

	public void setContent_type(String content_type) {
		this.content_type = content_type;
	}

	public Integer getCount_of_objects() {
		return count_of_objects;
	}

	public void setCount_of_objects(Integer count_of_objects) {
		this.count_of_objects = count_of_objects;
	}

	public CustomData[] getCustom_data() {
		return custom_data;
	}

	public void setCustom_data(CustomData[] custom_data) {
		this.custom_data = custom_data;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getDescription_flg() {
		return description_flg;
	}

	public void setDescription_flg(String description_flg) {
		this.description_flg = description_flg;
	}

	public Integer getDescription_length() {
		return description_length;
	}

	public void setDescription_length(Integer description_length) {
		this.description_length = description_length;
	}

	public String getDescription_simhash() {
		return description_simhash;
	}

	public void setDescription_simhash(String description_simhash) {
		this.description_simhash = description_simhash;
	}

	public Integer getDocument_size() {
		return document_size;
	}

	public void setDocument_size(Integer document_size) {
		this.document_size = document_size;
	}

	public String getDownload_latency() {
		return download_latency;
	}

	public void setDownload_latency(String download_latency) {
		this.download_latency = download_latency;
	}

	public String getDownload_time() {
		return download_time;
	}

	public void setDownload_time(String download_time) {
		this.download_time = download_time;
	}

	public String getError_message() {
		return error_message;
	}

	public void setError_message(String error_message) {
		this.error_message = error_message;
	}

	public Integer getFinal_response_code() {
		return final_response_code;
	}

	public void setFinal_response_code(Integer final_response_code) {
		this.final_response_code = final_response_code;
	}

	public String getFolder_level_1() {
		return folder_level_1;
	}

	public void setFolder_level_1(String folder_level_1) {
		this.folder_level_1 = folder_level_1;
	}

	public String getFolder_level_2() {
		return folder_level_2;
	}

	public void setFolder_level_2(String folder_level_2) {
		this.folder_level_2 = folder_level_2;
	}

	public String getFolder_level_3() {
		return folder_level_3;
	}

	public void setFolder_level_3(String folder_level_3) {
		this.folder_level_3 = folder_level_3;
	}

	public Integer getFolder_level_count() {
		return folder_level_count;
	}

	public void setFolder_level_count(Integer folder_level_count) {
		this.folder_level_count = folder_level_count;
	}

	public String getFollow_flg() {
		return follow_flg;
	}

	public void setFollow_flg(String follow_flg) {
		this.follow_flg = follow_flg;
	}

	public String getFollow_flg_x_tag() {
		return follow_flg_x_tag;
	}

	public void setFollow_flg_x_tag(String follow_flg_x_tag) {
		this.follow_flg_x_tag = follow_flg_x_tag;
	}

	public String[] getH1() {
		return h1;
	}

	public void setH1(String[] h1) {
		this.h1 = h1;
	}

	public Integer getH1_count() {
		return h1_count;
	}

	public void setH1_count(Integer h1_count) {
		this.h1_count = h1_count;
	}

	public String getH1_flg() {
		return h1_flg;
	}

	public void setH1_flg(String h1_flg) {
		this.h1_flg = h1_flg;
	}

	public Integer getH1_length() {
		return h1_length;
	}

	public void setH1_length(Integer h1_length) {
		this.h1_length = h1_length;
	}

	public String[] getH1_md5() {
		return h1_md5;
	}

	public void setH1_md5(String[] h1_md5) {
		this.h1_md5 = h1_md5;
	}

	public String getH1_simhash() {
		return h1_simhash;
	}

	public void setH1_simhash(String h1_simhash) {
		this.h1_simhash = h1_simhash;
	}

	public String[] getH2() {
		return h2;
	}

	public void setH2(String[] h2) {
		this.h2 = h2;
	}

	public String getH2_simhash() {
		return h2_simhash;
	}

	public void setH2_simhash(String h2_simhash) {
		this.h2_simhash = h2_simhash;
	}

	public Boolean getHeader_noarchive() {
		return header_noarchive;
	}

	public void setHeader_noarchive(Boolean header_noarchive) {
		this.header_noarchive = header_noarchive;
	}

	public Boolean getHeader_nofollow() {
		return header_nofollow;
	}

	public void setHeader_nofollow(Boolean header_nofollow) {
		this.header_nofollow = header_nofollow;
	}

	public Boolean getHeader_noindex() {
		return header_noindex;
	}

	public void setHeader_noindex(Boolean header_noindex) {
		this.header_noindex = header_noindex;
	}

	public Boolean getHeader_noodp() {
		return header_noodp;
	}

	public void setHeader_noodp(Boolean header_noodp) {
		this.header_noodp = header_noodp;
	}

	public Boolean getHeader_nosnippet() {
		return header_nosnippet;
	}

	public void setHeader_nosnippet(Boolean header_nosnippet) {
		this.header_nosnippet = header_nosnippet;
	}

	public Boolean getHeader_noydir() {
		return header_noydir;
	}

	public void setHeader_noydir(Boolean header_noydir) {
		this.header_noydir = header_noydir;
	}

	public HreflangErrors getHreflang_errors() {
		return hreflang_errors;
	}

	public void setHreflang_errors(HreflangErrors hreflang_errors) {
		this.hreflang_errors = hreflang_errors;
	}

	public HreflangLinks[] getHreflang_links() {
		return hreflang_links;
	}

	public void setHreflang_links(HreflangLinks[] hreflang_links) {
		this.hreflang_links = hreflang_links;
	}

	public Integer getHreflang_links_out_count() {
		return hreflang_links_out_count;
	}

	public void setHreflang_links_out_count(Integer hreflang_links_out_count) {
		this.hreflang_links_out_count = hreflang_links_out_count;
	}

	public Integer getHreflang_url_count() {
		return hreflang_url_count;
	}

	public void setHreflang_url_count(Integer hreflang_url_count) {
		this.hreflang_url_count = hreflang_url_count;
	}

	public String getIndex_flg() {
		return index_flg;
	}

	public void setIndex_flg(String index_flg) {
		this.index_flg = index_flg;
	}

	public String getIndex_flg_x_tag() {
		return index_flg_x_tag;
	}

	public void setIndex_flg_x_tag(String index_flg_x_tag) {
		this.index_flg_x_tag = index_flg_x_tag;
	}

	public Boolean getIndexable() {
		return indexable;
	}

	public void setIndexable(Boolean indexable) {
		this.indexable = indexable;
	}

	public String[] getInsecure_resources() {
		return insecure_resources;
	}

	public void setInsecure_resources(String[] insecure_resources) {
		this.insecure_resources = insecure_resources;
	}

	public Boolean getInsecure_resources_flag() {
		return insecure_resources_flag;
	}

	public void setInsecure_resources_flag(Boolean insecure_resources_flag) {
		this.insecure_resources_flag = insecure_resources_flag;
	}

	public Boolean getLong_redirect() {
		return long_redirect;
	}

	public void setLong_redirect(Boolean long_redirect) {
		this.long_redirect = long_redirect;
	}

	public String getMeta_charset() {
		return meta_charset;
	}

	public void setMeta_charset(String meta_charset) {
		this.meta_charset = meta_charset;
	}

	public String getMeta_content_type() {
		return meta_content_type;
	}

	public void setMeta_content_type(String meta_content_type) {
		this.meta_content_type = meta_content_type;
	}

	public Boolean getMeta_disabled_sitelinks() {
		return meta_disabled_sitelinks;
	}

	public void setMeta_disabled_sitelinks(Boolean meta_disabled_sitelinks) {
		this.meta_disabled_sitelinks = meta_disabled_sitelinks;
	}

	public Boolean getMeta_noodp() {
		return meta_noodp;
	}

	public void setMeta_noodp(Boolean meta_noodp) {
		this.meta_noodp = meta_noodp;
	}

	public Boolean getMeta_nosnippet() {
		return meta_nosnippet;
	}

	public void setMeta_nosnippet(Boolean meta_nosnippet) {
		this.meta_nosnippet = meta_nosnippet;
	}

	public Boolean getMeta_noydir() {
		return meta_noydir;
	}

	public void setMeta_noydir(Boolean meta_noydir) {
		this.meta_noydir = meta_noydir;
	}

	public Boolean getMeta_redirect() {
		return meta_redirect;
	}

	public void setMeta_redirect(Boolean meta_redirect) {
		this.meta_redirect = meta_redirect;
	}

	public Boolean getMixed_redirects() {
		return mixed_redirects;
	}

	public void setMixed_redirects(Boolean mixed_redirects) {
		this.mixed_redirects = mixed_redirects;
	}

	public Boolean getMobile_rel_alternate_url_is_consistent() {
		return mobile_rel_alternate_url_is_consistent;
	}

	public void setMobile_rel_alternate_url_is_consistent(Boolean mobile_rel_alternate_url_is_consistent) {
		this.mobile_rel_alternate_url_is_consistent = mobile_rel_alternate_url_is_consistent;
	}

	public Boolean getNoodp() {
		return noodp;
	}

	public void setNoodp(Boolean noodp) {
		this.noodp = noodp;
	}

	public Boolean getNosnippet() {
		return nosnippet;
	}

	public void setNosnippet(Boolean nosnippet) {
		this.nosnippet = nosnippet;
	}

	public Boolean getNoydir() {
		return noydir;
	}

	public void setNoydir(Boolean noydir) {
		this.noydir = noydir;
	}

	public OgMarkup[] getOg_markup() {
		return og_markup;
	}

	public void setOg_markup(OgMarkup[] og_markup) {
		this.og_markup = og_markup;
	}

	public Boolean getOg_markup_flag() {
		return og_markup_flag;
	}

	public void setOg_markup_flag(Boolean og_markup_flag) {
		this.og_markup_flag = og_markup_flag;
	}

	public Integer getOg_markup_length() {
		return og_markup_length;
	}

	public void setOg_markup_length(Integer og_markup_length) {
		this.og_markup_length = og_markup_length;
	}

	public Integer getOutlink_count() {
		return outlink_count;
	}

	public void setOutlink_count(Integer outlink_count) {
		this.outlink_count = outlink_count;
	}

	public Boolean getPage_1() {
		return page_1;
	}

	public void setPage_1(Boolean page_1) {
		this.page_1 = page_1;
	}

	public PageAnalysisRule getPage_analysis_rule_10_b() {
		return page_analysis_rule_10_b;
	}

	public void setPage_analysis_rule_10_b(PageAnalysisRule page_analysis_rule_10_b) {
		this.page_analysis_rule_10_b = page_analysis_rule_10_b;
	}

	public PageAnalysisRule getPage_analysis_rule_11_b() {
		return page_analysis_rule_11_b;
	}

	public void setPage_analysis_rule_11_b(PageAnalysisRule page_analysis_rule_11_b) {
		this.page_analysis_rule_11_b = page_analysis_rule_11_b;
	}

	public PageAnalysisRule getPage_analysis_rule_12_b() {
		return page_analysis_rule_12_b;
	}

	public void setPage_analysis_rule_12_b(PageAnalysisRule page_analysis_rule_12_b) {
		this.page_analysis_rule_12_b = page_analysis_rule_12_b;
	}

	public PageAnalysisRule getPage_analysis_rule_13_b() {
		return page_analysis_rule_13_b;
	}

	public void setPage_analysis_rule_13_b(PageAnalysisRule page_analysis_rule_13_b) {
		this.page_analysis_rule_13_b = page_analysis_rule_13_b;
	}

	public PageAnalysisRule getPage_analysis_rule_14_b() {
		return page_analysis_rule_14_b;
	}

	public void setPage_analysis_rule_14_b(PageAnalysisRule page_analysis_rule_14_b) {
		this.page_analysis_rule_14_b = page_analysis_rule_14_b;
	}

	public PageAnalysisRule getPage_analysis_rule_15_b() {
		return page_analysis_rule_15_b;
	}

	public void setPage_analysis_rule_15_b(PageAnalysisRule page_analysis_rule_15_b) {
		this.page_analysis_rule_15_b = page_analysis_rule_15_b;
	}

	public PageAnalysisRule getPage_analysis_rule_16_b() {
		return page_analysis_rule_16_b;
	}

	public void setPage_analysis_rule_16_b(PageAnalysisRule page_analysis_rule_16_b) {
		this.page_analysis_rule_16_b = page_analysis_rule_16_b;
	}

	public PageAnalysisRule getPage_analysis_rule_17_b() {
		return page_analysis_rule_17_b;
	}

	public void setPage_analysis_rule_17_b(PageAnalysisRule page_analysis_rule_17_b) {
		this.page_analysis_rule_17_b = page_analysis_rule_17_b;
	}

	public PageAnalysisRule getPage_analysis_rule_18_b() {
		return page_analysis_rule_18_b;
	}

	public void setPage_analysis_rule_18_b(PageAnalysisRule page_analysis_rule_18_b) {
		this.page_analysis_rule_18_b = page_analysis_rule_18_b;
	}

	public PageAnalysisRule getPage_analysis_rule_19_b() {
		return page_analysis_rule_19_b;
	}

	public void setPage_analysis_rule_19_b(PageAnalysisRule page_analysis_rule_19_b) {
		this.page_analysis_rule_19_b = page_analysis_rule_19_b;
	}

	public PageAnalysisRule getPage_analysis_rule_1_b() {
		return page_analysis_rule_1_b;
	}

	public void setPage_analysis_rule_1_b(PageAnalysisRule page_analysis_rule_1_b) {
		this.page_analysis_rule_1_b = page_analysis_rule_1_b;
	}

	public PageAnalysisRule getPage_analysis_rule_20_b() {
		return page_analysis_rule_20_b;
	}

	public void setPage_analysis_rule_20_b(PageAnalysisRule page_analysis_rule_20_b) {
		this.page_analysis_rule_20_b = page_analysis_rule_20_b;
	}

	public PageAnalysisRule getPage_analysis_rule_21_b() {
		return page_analysis_rule_21_b;
	}

	public void setPage_analysis_rule_21_b(PageAnalysisRule page_analysis_rule_21_b) {
		this.page_analysis_rule_21_b = page_analysis_rule_21_b;
	}

	public PageAnalysisRule getPage_analysis_rule_22_b() {
		return page_analysis_rule_22_b;
	}

	public void setPage_analysis_rule_22_b(PageAnalysisRule page_analysis_rule_22_b) {
		this.page_analysis_rule_22_b = page_analysis_rule_22_b;
	}

	public PageAnalysisRule getPage_analysis_rule_23_b() {
		return page_analysis_rule_23_b;
	}

	public void setPage_analysis_rule_23_b(PageAnalysisRule page_analysis_rule_23_b) {
		this.page_analysis_rule_23_b = page_analysis_rule_23_b;
	}

	public PageAnalysisRule getPage_analysis_rule_24_b() {
		return page_analysis_rule_24_b;
	}

	public void setPage_analysis_rule_24_b(PageAnalysisRule page_analysis_rule_24_b) {
		this.page_analysis_rule_24_b = page_analysis_rule_24_b;
	}

	public PageAnalysisRule getPage_analysis_rule_25_b() {
		return page_analysis_rule_25_b;
	}

	public void setPage_analysis_rule_25_b(PageAnalysisRule page_analysis_rule_25_b) {
		this.page_analysis_rule_25_b = page_analysis_rule_25_b;
	}

	public PageAnalysisRule getPage_analysis_rule_26_b() {
		return page_analysis_rule_26_b;
	}

	public void setPage_analysis_rule_26_b(PageAnalysisRule page_analysis_rule_26_b) {
		this.page_analysis_rule_26_b = page_analysis_rule_26_b;
	}

	public PageAnalysisRule getPage_analysis_rule_27_b() {
		return page_analysis_rule_27_b;
	}

	public void setPage_analysis_rule_27_b(PageAnalysisRule page_analysis_rule_27_b) {
		this.page_analysis_rule_27_b = page_analysis_rule_27_b;
	}

	public PageAnalysisRule getPage_analysis_rule_28_b() {
		return page_analysis_rule_28_b;
	}

	public void setPage_analysis_rule_28_b(PageAnalysisRule page_analysis_rule_28_b) {
		this.page_analysis_rule_28_b = page_analysis_rule_28_b;
	}

	public PageAnalysisRule getPage_analysis_rule_29_b() {
		return page_analysis_rule_29_b;
	}

	public void setPage_analysis_rule_29_b(PageAnalysisRule page_analysis_rule_29_b) {
		this.page_analysis_rule_29_b = page_analysis_rule_29_b;
	}

	public PageAnalysisRule getPage_analysis_rule_2_b() {
		return page_analysis_rule_2_b;
	}

	public void setPage_analysis_rule_2_b(PageAnalysisRule page_analysis_rule_2_b) {
		this.page_analysis_rule_2_b = page_analysis_rule_2_b;
	}

	public PageAnalysisRule getPage_analysis_rule_30_b() {
		return page_analysis_rule_30_b;
	}

	public void setPage_analysis_rule_30_b(PageAnalysisRule page_analysis_rule_30_b) {
		this.page_analysis_rule_30_b = page_analysis_rule_30_b;
	}

	public PageAnalysisRule getPage_analysis_rule_31_b() {
		return page_analysis_rule_31_b;
	}

	public void setPage_analysis_rule_31_b(PageAnalysisRule page_analysis_rule_31_b) {
		this.page_analysis_rule_31_b = page_analysis_rule_31_b;
	}

	public PageAnalysisRule getPage_analysis_rule_32_b() {
		return page_analysis_rule_32_b;
	}

	public void setPage_analysis_rule_32_b(PageAnalysisRule page_analysis_rule_32_b) {
		this.page_analysis_rule_32_b = page_analysis_rule_32_b;
	}

	public PageAnalysisRule getPage_analysis_rule_33_b() {
		return page_analysis_rule_33_b;
	}

	public void setPage_analysis_rule_33_b(PageAnalysisRule page_analysis_rule_33_b) {
		this.page_analysis_rule_33_b = page_analysis_rule_33_b;
	}

	public PageAnalysisRule getPage_analysis_rule_34_b() {
		return page_analysis_rule_34_b;
	}

	public void setPage_analysis_rule_34_b(PageAnalysisRule page_analysis_rule_34_b) {
		this.page_analysis_rule_34_b = page_analysis_rule_34_b;
	}

	public PageAnalysisRule getPage_analysis_rule_35_b() {
		return page_analysis_rule_35_b;
	}

	public void setPage_analysis_rule_35_b(PageAnalysisRule page_analysis_rule_35_b) {
		this.page_analysis_rule_35_b = page_analysis_rule_35_b;
	}

	public PageAnalysisRule getPage_analysis_rule_36_b() {
		return page_analysis_rule_36_b;
	}

	public void setPage_analysis_rule_36_b(PageAnalysisRule page_analysis_rule_36_b) {
		this.page_analysis_rule_36_b = page_analysis_rule_36_b;
	}

	public PageAnalysisRule getPage_analysis_rule_37_b() {
		return page_analysis_rule_37_b;
	}

	public void setPage_analysis_rule_37_b(PageAnalysisRule page_analysis_rule_37_b) {
		this.page_analysis_rule_37_b = page_analysis_rule_37_b;
	}

	public PageAnalysisRule getPage_analysis_rule_38_b() {
		return page_analysis_rule_38_b;
	}

	public void setPage_analysis_rule_38_b(PageAnalysisRule page_analysis_rule_38_b) {
		this.page_analysis_rule_38_b = page_analysis_rule_38_b;
	}

	public PageAnalysisRule getPage_analysis_rule_39_b() {
		return page_analysis_rule_39_b;
	}

	public void setPage_analysis_rule_39_b(PageAnalysisRule page_analysis_rule_39_b) {
		this.page_analysis_rule_39_b = page_analysis_rule_39_b;
	}

	public PageAnalysisRule getPage_analysis_rule_3_b() {
		return page_analysis_rule_3_b;
	}

	public void setPage_analysis_rule_3_b(PageAnalysisRule page_analysis_rule_3_b) {
		this.page_analysis_rule_3_b = page_analysis_rule_3_b;
	}

	public PageAnalysisRule getPage_analysis_rule_40_b() {
		return page_analysis_rule_40_b;
	}

	public void setPage_analysis_rule_40_b(PageAnalysisRule page_analysis_rule_40_b) {
		this.page_analysis_rule_40_b = page_analysis_rule_40_b;
	}

	public PageAnalysisRule getPage_analysis_rule_41_b() {
		return page_analysis_rule_41_b;
	}

	public void setPage_analysis_rule_41_b(PageAnalysisRule page_analysis_rule_41_b) {
		this.page_analysis_rule_41_b = page_analysis_rule_41_b;
	}

	public PageAnalysisRule getPage_analysis_rule_42_b() {
		return page_analysis_rule_42_b;
	}

	public void setPage_analysis_rule_42_b(PageAnalysisRule page_analysis_rule_42_b) {
		this.page_analysis_rule_42_b = page_analysis_rule_42_b;
	}

	public PageAnalysisRule getPage_analysis_rule_43_b() {
		return page_analysis_rule_43_b;
	}

	public void setPage_analysis_rule_43_b(PageAnalysisRule page_analysis_rule_43_b) {
		this.page_analysis_rule_43_b = page_analysis_rule_43_b;
	}

	public PageAnalysisRule getPage_analysis_rule_44_b() {
		return page_analysis_rule_44_b;
	}

	public void setPage_analysis_rule_44_b(PageAnalysisRule page_analysis_rule_44_b) {
		this.page_analysis_rule_44_b = page_analysis_rule_44_b;
	}

	public PageAnalysisRule getPage_analysis_rule_45_b() {
		return page_analysis_rule_45_b;
	}

	public void setPage_analysis_rule_45_b(PageAnalysisRule page_analysis_rule_45_b) {
		this.page_analysis_rule_45_b = page_analysis_rule_45_b;
	}

	public PageAnalysisRule getPage_analysis_rule_46_b() {
		return page_analysis_rule_46_b;
	}

	public void setPage_analysis_rule_46_b(PageAnalysisRule page_analysis_rule_46_b) {
		this.page_analysis_rule_46_b = page_analysis_rule_46_b;
	}

	public PageAnalysisRule getPage_analysis_rule_47_b() {
		return page_analysis_rule_47_b;
	}

	public void setPage_analysis_rule_47_b(PageAnalysisRule page_analysis_rule_47_b) {
		this.page_analysis_rule_47_b = page_analysis_rule_47_b;
	}

	public PageAnalysisRule getPage_analysis_rule_48_b() {
		return page_analysis_rule_48_b;
	}

	public void setPage_analysis_rule_48_b(PageAnalysisRule page_analysis_rule_48_b) {
		this.page_analysis_rule_48_b = page_analysis_rule_48_b;
	}

	public PageAnalysisRule getPage_analysis_rule_49_b() {
		return page_analysis_rule_49_b;
	}

	public void setPage_analysis_rule_49_b(PageAnalysisRule page_analysis_rule_49_b) {
		this.page_analysis_rule_49_b = page_analysis_rule_49_b;
	}

	public PageAnalysisRule getPage_analysis_rule_4_b() {
		return page_analysis_rule_4_b;
	}

	public void setPage_analysis_rule_4_b(PageAnalysisRule page_analysis_rule_4_b) {
		this.page_analysis_rule_4_b = page_analysis_rule_4_b;
	}

	public PageAnalysisRule getPage_analysis_rule_50_b() {
		return page_analysis_rule_50_b;
	}

	public void setPage_analysis_rule_50_b(PageAnalysisRule page_analysis_rule_50_b) {
		this.page_analysis_rule_50_b = page_analysis_rule_50_b;
	}

	public PageAnalysisRule getPage_analysis_rule_51_b() {
		return page_analysis_rule_51_b;
	}

	public void setPage_analysis_rule_51_b(PageAnalysisRule page_analysis_rule_51_b) {
		this.page_analysis_rule_51_b = page_analysis_rule_51_b;
	}

	public PageAnalysisRule getPage_analysis_rule_52_b() {
		return page_analysis_rule_52_b;
	}

	public void setPage_analysis_rule_52_b(PageAnalysisRule page_analysis_rule_52_b) {
		this.page_analysis_rule_52_b = page_analysis_rule_52_b;
	}

	public PageAnalysisRule getPage_analysis_rule_53_b() {
		return page_analysis_rule_53_b;
	}

	public void setPage_analysis_rule_53_b(PageAnalysisRule page_analysis_rule_53_b) {
		this.page_analysis_rule_53_b = page_analysis_rule_53_b;
	}

	public PageAnalysisRule getPage_analysis_rule_54_b() {
		return page_analysis_rule_54_b;
	}

	public void setPage_analysis_rule_54_b(PageAnalysisRule page_analysis_rule_54_b) {
		this.page_analysis_rule_54_b = page_analysis_rule_54_b;
	}

	public PageAnalysisRule getPage_analysis_rule_55_b() {
		return page_analysis_rule_55_b;
	}

	public void setPage_analysis_rule_55_b(PageAnalysisRule page_analysis_rule_55_b) {
		this.page_analysis_rule_55_b = page_analysis_rule_55_b;
	}

	public PageAnalysisRule getPage_analysis_rule_56_b() {
		return page_analysis_rule_56_b;
	}

	public void setPage_analysis_rule_56_b(PageAnalysisRule page_analysis_rule_56_b) {
		this.page_analysis_rule_56_b = page_analysis_rule_56_b;
	}

	public PageAnalysisRule getPage_analysis_rule_57_b() {
		return page_analysis_rule_57_b;
	}

	public void setPage_analysis_rule_57_b(PageAnalysisRule page_analysis_rule_57_b) {
		this.page_analysis_rule_57_b = page_analysis_rule_57_b;
	}

	public PageAnalysisRule getPage_analysis_rule_58_b() {
		return page_analysis_rule_58_b;
	}

	public void setPage_analysis_rule_58_b(PageAnalysisRule page_analysis_rule_58_b) {
		this.page_analysis_rule_58_b = page_analysis_rule_58_b;
	}

	public PageAnalysisRule getPage_analysis_rule_59_b() {
		return page_analysis_rule_59_b;
	}

	public void setPage_analysis_rule_59_b(PageAnalysisRule page_analysis_rule_59_b) {
		this.page_analysis_rule_59_b = page_analysis_rule_59_b;
	}

	public PageAnalysisRule getPage_analysis_rule_5_b() {
		return page_analysis_rule_5_b;
	}

	public void setPage_analysis_rule_5_b(PageAnalysisRule page_analysis_rule_5_b) {
		this.page_analysis_rule_5_b = page_analysis_rule_5_b;
	}

	public PageAnalysisRule getPage_analysis_rule_60_b() {
		return page_analysis_rule_60_b;
	}

	public void setPage_analysis_rule_60_b(PageAnalysisRule page_analysis_rule_60_b) {
		this.page_analysis_rule_60_b = page_analysis_rule_60_b;
	}

	public PageAnalysisRule getPage_analysis_rule_61_b() {
		return page_analysis_rule_61_b;
	}

	public void setPage_analysis_rule_61_b(PageAnalysisRule page_analysis_rule_61_b) {
		this.page_analysis_rule_61_b = page_analysis_rule_61_b;
	}

	public PageAnalysisRule getPage_analysis_rule_62_b() {
		return page_analysis_rule_62_b;
	}

	public void setPage_analysis_rule_62_b(PageAnalysisRule page_analysis_rule_62_b) {
		this.page_analysis_rule_62_b = page_analysis_rule_62_b;
	}

	public PageAnalysisRule getPage_analysis_rule_63_b() {
		return page_analysis_rule_63_b;
	}

	public void setPage_analysis_rule_63_b(PageAnalysisRule page_analysis_rule_63_b) {
		this.page_analysis_rule_63_b = page_analysis_rule_63_b;
	}

	public PageAnalysisRule getPage_analysis_rule_64_b() {
		return page_analysis_rule_64_b;
	}

	public void setPage_analysis_rule_64_b(PageAnalysisRule page_analysis_rule_64_b) {
		this.page_analysis_rule_64_b = page_analysis_rule_64_b;
	}

	public PageAnalysisRule getPage_analysis_rule_65_b() {
		return page_analysis_rule_65_b;
	}

	public void setPage_analysis_rule_65_b(PageAnalysisRule page_analysis_rule_65_b) {
		this.page_analysis_rule_65_b = page_analysis_rule_65_b;
	}

	public PageAnalysisRule getPage_analysis_rule_6_b() {
		return page_analysis_rule_6_b;
	}

	public void setPage_analysis_rule_6_b(PageAnalysisRule page_analysis_rule_6_b) {
		this.page_analysis_rule_6_b = page_analysis_rule_6_b;
	}

	public PageAnalysisRule getPage_analysis_rule_7_b() {
		return page_analysis_rule_7_b;
	}

	public void setPage_analysis_rule_7_b(PageAnalysisRule page_analysis_rule_7_b) {
		this.page_analysis_rule_7_b = page_analysis_rule_7_b;
	}

	public PageAnalysisRule getPage_analysis_rule_8_b() {
		return page_analysis_rule_8_b;
	}

	public void setPage_analysis_rule_8_b(PageAnalysisRule page_analysis_rule_8_b) {
		this.page_analysis_rule_8_b = page_analysis_rule_8_b;
	}

	public PageAnalysisRule getPage_analysis_rule_9_b() {
		return page_analysis_rule_9_b;
	}

	public void setPage_analysis_rule_9_b(PageAnalysisRule page_analysis_rule_9_b) {
		this.page_analysis_rule_9_b = page_analysis_rule_9_b;
	}

	public PageLink[] getPage_link() {
		return page_link;
	}

	public void setPage_link(PageLink[] page_link) {
		this.page_link = page_link;
	}

	public Boolean getPage_timeout_flag() {
		return page_timeout_flag;
	}

	public void setPage_timeout_flag(Boolean page_timeout_flag) {
		this.page_timeout_flag = page_timeout_flag;
	}

	public Boolean getPaginated() {
		return paginated;
	}

	public void setPaginated(Boolean paginated) {
		this.paginated = paginated;
	}

	public PaginationLinks[] getPagination_links() {
		return pagination_links;
	}

	public void setPagination_links(PaginationLinks[] pagination_links) {
		this.pagination_links = pagination_links;
	}

	public String getProtocol() {
		return protocol;
	}

	public void setProtocol(String protocol) {
		this.protocol = protocol;
	}

	public Boolean getRedirect_blocked() {
		return redirect_blocked;
	}

	public void setRedirect_blocked(Boolean redirect_blocked) {
		this.redirect_blocked = redirect_blocked;
	}

	public String getRedirect_blocked_reason() {
		return redirect_blocked_reason;
	}

	public void setRedirect_blocked_reason(String redirect_blocked_reason) {
		this.redirect_blocked_reason = redirect_blocked_reason;
	}

	public RedirectChain[] getRedirect_chain() {
		return redirect_chain;
	}

	public void setRedirect_chain(RedirectChain[] redirect_chain) {
		this.redirect_chain = redirect_chain;
	}

	public String getRedirect_final_url() {
		return redirect_final_url;
	}

	public void setRedirect_final_url(String redirect_final_url) {
		this.redirect_final_url = redirect_final_url;
	}

	public Boolean getRedirect_flg() {
		return redirect_flg;
	}

	public void setRedirect_flg(Boolean redirect_flg) {
		this.redirect_flg = redirect_flg;
	}

	public Integer getRedirect_times() {
		return redirect_times;
	}

	public void setRedirect_times(Integer redirect_times) {
		this.redirect_times = redirect_times;
	}

	public String getRel_next_html_url() {
		return rel_next_html_url;
	}

	public void setRel_next_html_url(String rel_next_html_url) {
		this.rel_next_html_url = rel_next_html_url;
	}

	public Boolean getRel_next_url_is_consistent() {
		return rel_next_url_is_consistent;
	}

	public void setRel_next_url_is_consistent(Boolean rel_next_url_is_consistent) {
		this.rel_next_url_is_consistent = rel_next_url_is_consistent;
	}

	public Boolean getRel_prev_url_is_consistent() {
		return rel_prev_url_is_consistent;
	}

	public void setRel_prev_url_is_consistent(Boolean rel_prev_url_is_consistent) {
		this.rel_prev_url_is_consistent = rel_prev_url_is_consistent;
	}

	public String getRequest_headers() {
		return request_headers;
	}

	public void setRequest_headers(String request_headers) {
		this.request_headers = request_headers;
	}

	public String getRequest_time() {
		return request_time;
	}

	public void setRequest_time(String request_time) {
		this.request_time = request_time;
	}

	public String getResponse_code() {
		return response_code;
	}

	public void setResponse_code(String response_code) {
		this.response_code = response_code;
	}

	public ResponseHeaders[] getResponse_headers() {
		return response_headers;
	}

	public void setResponse_headers(ResponseHeaders[] response_headers) {
		this.response_headers = response_headers;
	}

	public Boolean getRetry_attempted() {
		return retry_attempted;
	}

	public void setRetry_attempted(Boolean retry_attempted) {
		this.retry_attempted = retry_attempted;
	}

	public String getRobots() {
		return robots;
	}

	public void setRobots(String robots) {
		this.robots = robots;
	}

	public String getRobots_contents() {
		return robots_contents;
	}

	public void setRobots_contents(String robots_contents) {
		this.robots_contents = robots_contents;
	}

	public Boolean getRobots_contents_x_tag() {
		return robots_contents_x_tag;
	}

	public void setRobots_contents_x_tag(Boolean robots_contents_x_tag) {
		this.robots_contents_x_tag = robots_contents_x_tag;
	}

	public String getRobots_flg() {
		return robots_flg;
	}

	public void setRobots_flg(String robots_flg) {
		this.robots_flg = robots_flg;
	}

	public String getRobots_flg_x_tag() {
		return robots_flg_x_tag;
	}

	public void setRobots_flg_x_tag(String robots_flg_x_tag) {
		this.robots_flg_x_tag = robots_flg_x_tag;
	}

	public String getServer_response_time() {
		return server_response_time;
	}

	public void setServer_response_time(String server_response_time) {
		this.server_response_time = server_response_time;
	}

	public String[] getSource_url() {
		return source_url;
	}

	public void setSource_url(String[] source_url) {
		this.source_url = source_url;
	}

	public String getSplash_took() {
		return splash_took;
	}

	public void setSplash_took(String splash_took) {
		this.splash_took = splash_took;
	}

	public StructuredData getStructured_data() {
		return structured_data;
	}

	public void setStructured_data(StructuredData structured_data) {
		this.structured_data = structured_data;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getTitle_flg() {
		return title_flg;
	}

	public void setTitle_flg(String title_flg) {
		this.title_flg = title_flg;
	}

	public Integer getTitle_length() {
		return title_length;
	}

	public void setTitle_length(Integer title_length) {
		this.title_length = title_length;
	}

	public String[] getTitle_md5() {
		return title_md5;
	}

	public void setTitle_md5(String[] title_md5) {
		this.title_md5 = title_md5;
	}

	public String getTitle_simhash() {
		return title_simhash;
	}

	public void setTitle_simhash(String title_simhash) {
		this.title_simhash = title_simhash;
	}

	public Integer getTwitter_description_length() {
		return twitter_description_length;
	}

	public void setTwitter_description_length(Integer twitter_description_length) {
		this.twitter_description_length = twitter_description_length;
	}

	public TwitterMarkup[] getTwitter_markup() {
		return twitter_markup;
	}

	public void setTwitter_markup(TwitterMarkup[] twitter_markup) {
		this.twitter_markup = twitter_markup;
	}

	public Boolean getTwitter_markup_flag() {
		return twitter_markup_flag;
	}

	public void setTwitter_markup_flag(Boolean twitter_markup_flag) {
		this.twitter_markup_flag = twitter_markup_flag;
	}

	public Integer getTwitter_markup_length() {
		return twitter_markup_length;
	}

	public void setTwitter_markup_length(Integer twitter_markup_length) {
		this.twitter_markup_length = twitter_markup_length;
	}

	public Integer getUrl_length() {
		return url_length;
	}

	public void setUrl_length(Integer url_length) {
		this.url_length = url_length;
	}

	public String getValid_twitter_card() {
		return valid_twitter_card;
	}

	public void setValid_twitter_card(String valid_twitter_card) {
		this.valid_twitter_card = valid_twitter_card;
	}

	public String getViewport_content() {
		return viewport_content;
	}

	public void setViewport_content(String viewport_content) {
		this.viewport_content = viewport_content;
	}

	public Boolean getViewport_flag() {
		return viewport_flag;
	}

	public void setViewport_flag(Boolean viewport_flag) {
		this.viewport_flag = viewport_flag;
	}

	public PageAnalysisRule getPage_analysis_rule_66_b() {
		return page_analysis_rule_66_b;
	}

	public void setPage_analysis_rule_66_b(PageAnalysisRule page_analysis_rule_66_b) {
		this.page_analysis_rule_66_b = page_analysis_rule_66_b;
	}

	public PageAnalysisRule getPage_analysis_rule_67_b() {
		return page_analysis_rule_67_b;
	}

	public void setPage_analysis_rule_67_b(PageAnalysisRule page_analysis_rule_67_b) {
		this.page_analysis_rule_67_b = page_analysis_rule_67_b;
	}

	public PageAnalysisRule getPage_analysis_rule_68_b() {
		return page_analysis_rule_68_b;
	}

	public void setPage_analysis_rule_68_b(PageAnalysisRule page_analysis_rule_68_b) {
		this.page_analysis_rule_68_b = page_analysis_rule_68_b;
	}

	public PageAnalysisRule getPage_analysis_rule_69_b() {
		return page_analysis_rule_69_b;
	}

	public void setPage_analysis_rule_69_b(PageAnalysisRule page_analysis_rule_69_b) {
		this.page_analysis_rule_69_b = page_analysis_rule_69_b;
	}

	public PageAnalysisRule getPage_analysis_rule_70_b() {
		return page_analysis_rule_70_b;
	}

	public void setPage_analysis_rule_70_b(PageAnalysisRule page_analysis_rule_70_b) {
		this.page_analysis_rule_70_b = page_analysis_rule_70_b;
	}

	public PageAnalysisRule getPage_analysis_rule_71_b() {
		return page_analysis_rule_71_b;
	}

	public void setPage_analysis_rule_71_b(PageAnalysisRule page_analysis_rule_71_b) {
		this.page_analysis_rule_71_b = page_analysis_rule_71_b;
	}

	public PageAnalysisRule getPage_analysis_rule_72_b() {
		return page_analysis_rule_72_b;
	}

	public void setPage_analysis_rule_72_b(PageAnalysisRule page_analysis_rule_72_b) {
		this.page_analysis_rule_72_b = page_analysis_rule_72_b;
	}

	public PageAnalysisRule getPage_analysis_rule_73_b() {
		return page_analysis_rule_73_b;
	}

	public void setPage_analysis_rule_73_b(PageAnalysisRule page_analysis_rule_73_b) {
		this.page_analysis_rule_73_b = page_analysis_rule_73_b;
	}

	public PageAnalysisRule getPage_analysis_rule_74_b() {
		return page_analysis_rule_74_b;
	}

	public void setPage_analysis_rule_74_b(PageAnalysisRule page_analysis_rule_74_b) {
		this.page_analysis_rule_74_b = page_analysis_rule_74_b;
	}

	public PageAnalysisRule getPage_analysis_rule_75_b() {
		return page_analysis_rule_75_b;
	}

	public void setPage_analysis_rule_75_b(PageAnalysisRule page_analysis_rule_75_b) {
		this.page_analysis_rule_75_b = page_analysis_rule_75_b;
	}

	public PageAnalysisRule getPage_analysis_rule_76_b() {
		return page_analysis_rule_76_b;
	}

	public void setPage_analysis_rule_76_b(PageAnalysisRule page_analysis_rule_76_b) {
		this.page_analysis_rule_76_b = page_analysis_rule_76_b;
	}

	public PageAnalysisRule getPage_analysis_rule_77_b() {
		return page_analysis_rule_77_b;
	}

	public void setPage_analysis_rule_77_b(PageAnalysisRule page_analysis_rule_77_b) {
		this.page_analysis_rule_77_b = page_analysis_rule_77_b;
	}

	public PageAnalysisRule getPage_analysis_rule_78_b() {
		return page_analysis_rule_78_b;
	}

	public void setPage_analysis_rule_78_b(PageAnalysisRule page_analysis_rule_78_b) {
		this.page_analysis_rule_78_b = page_analysis_rule_78_b;
	}

	public PageAnalysisRule getPage_analysis_rule_79_b() {
		return page_analysis_rule_79_b;
	}

	public void setPage_analysis_rule_79_b(PageAnalysisRule page_analysis_rule_79_b) {
		this.page_analysis_rule_79_b = page_analysis_rule_79_b;
	}

	public PageAnalysisRule getPage_analysis_rule_80_b() {
		return page_analysis_rule_80_b;
	}

	public void setPage_analysis_rule_80_b(PageAnalysisRule page_analysis_rule_80_b) {
		this.page_analysis_rule_80_b = page_analysis_rule_80_b;
	}

	public PageAnalysisRule getPage_analysis_rule_81_b() {
		return page_analysis_rule_81_b;
	}

	public void setPage_analysis_rule_81_b(PageAnalysisRule page_analysis_rule_81_b) {
		this.page_analysis_rule_81_b = page_analysis_rule_81_b;
	}

	public PageAnalysisRule getPage_analysis_rule_82_b() {
		return page_analysis_rule_82_b;
	}

	public void setPage_analysis_rule_82_b(PageAnalysisRule page_analysis_rule_82_b) {
		this.page_analysis_rule_82_b = page_analysis_rule_82_b;
	}

	public PageAnalysisRule getPage_analysis_rule_83_b() {
		return page_analysis_rule_83_b;
	}

	public void setPage_analysis_rule_83_b(PageAnalysisRule page_analysis_rule_83_b) {
		this.page_analysis_rule_83_b = page_analysis_rule_83_b;
	}

	public PageAnalysisRule getPage_analysis_rule_84_b() {
		return page_analysis_rule_84_b;
	}

	public void setPage_analysis_rule_84_b(PageAnalysisRule page_analysis_rule_84_b) {
		this.page_analysis_rule_84_b = page_analysis_rule_84_b;
	}

	public PageAnalysisRule getPage_analysis_rule_85_b() {
		return page_analysis_rule_85_b;
	}

	public void setPage_analysis_rule_85_b(PageAnalysisRule page_analysis_rule_85_b) {
		this.page_analysis_rule_85_b = page_analysis_rule_85_b;
	}

	public PageAnalysisRule getPage_analysis_rule_86_b() {
		return page_analysis_rule_86_b;
	}

	public void setPage_analysis_rule_86_b(PageAnalysisRule page_analysis_rule_86_b) {
		this.page_analysis_rule_86_b = page_analysis_rule_86_b;
	}

	public PageAnalysisRule getPage_analysis_rule_87_b() {
		return page_analysis_rule_87_b;
	}

	public void setPage_analysis_rule_87_b(PageAnalysisRule page_analysis_rule_87_b) {
		this.page_analysis_rule_87_b = page_analysis_rule_87_b;
	}

	public PageAnalysisRule getPage_analysis_rule_88_b() {
		return page_analysis_rule_88_b;
	}

	public void setPage_analysis_rule_88_b(PageAnalysisRule page_analysis_rule_88_b) {
		this.page_analysis_rule_88_b = page_analysis_rule_88_b;
	}

	public PageAnalysisRule getPage_analysis_rule_89_b() {
		return page_analysis_rule_89_b;
	}

	public void setPage_analysis_rule_89_b(PageAnalysisRule page_analysis_rule_89_b) {
		this.page_analysis_rule_89_b = page_analysis_rule_89_b;
	}

	public PageAnalysisRule getPage_analysis_rule_90_b() {
		return page_analysis_rule_90_b;
	}

	public void setPage_analysis_rule_90_b(PageAnalysisRule page_analysis_rule_90_b) {
		this.page_analysis_rule_90_b = page_analysis_rule_90_b;
	}

	public PageAnalysisRule getPage_analysis_rule_91_b() {
		return page_analysis_rule_91_b;
	}

	public void setPage_analysis_rule_91_b(PageAnalysisRule page_analysis_rule_91_b) {
		this.page_analysis_rule_91_b = page_analysis_rule_91_b;
	}

	public PageAnalysisRule getPage_analysis_rule_92_b() {
		return page_analysis_rule_92_b;
	}

	public void setPage_analysis_rule_92_b(PageAnalysisRule page_analysis_rule_92_b) {
		this.page_analysis_rule_92_b = page_analysis_rule_92_b;
	}

	public PageAnalysisRule getPage_analysis_rule_93_b() {
		return page_analysis_rule_93_b;
	}

	public void setPage_analysis_rule_93_b(PageAnalysisRule page_analysis_rule_93_b) {
		this.page_analysis_rule_93_b = page_analysis_rule_93_b;
	}

	public PageAnalysisRule getPage_analysis_rule_94_b() {
		return page_analysis_rule_94_b;
	}

	public void setPage_analysis_rule_94_b(PageAnalysisRule page_analysis_rule_94_b) {
		this.page_analysis_rule_94_b = page_analysis_rule_94_b;
	}

	public PageAnalysisRule getPage_analysis_rule_95_b() {
		return page_analysis_rule_95_b;
	}

	public void setPage_analysis_rule_95_b(PageAnalysisRule page_analysis_rule_95_b) {
		this.page_analysis_rule_95_b = page_analysis_rule_95_b;
	}

	public PageAnalysisRule getPage_analysis_rule_96_b() {
		return page_analysis_rule_96_b;
	}

	public void setPage_analysis_rule_96_b(PageAnalysisRule page_analysis_rule_96_b) {
		this.page_analysis_rule_96_b = page_analysis_rule_96_b;
	}

	public PageAnalysisRule getPage_analysis_rule_97_b() {
		return page_analysis_rule_97_b;
	}

	public void setPage_analysis_rule_97_b(PageAnalysisRule page_analysis_rule_97_b) {
		this.page_analysis_rule_97_b = page_analysis_rule_97_b;
	}

	public PageAnalysisRule getPage_analysis_rule_98_b() {
		return page_analysis_rule_98_b;
	}

	public void setPage_analysis_rule_98_b(PageAnalysisRule page_analysis_rule_98_b) {
		this.page_analysis_rule_98_b = page_analysis_rule_98_b;
	}

	public PageAnalysisRule getPage_analysis_rule_99_b() {
		return page_analysis_rule_99_b;
	}

	public void setPage_analysis_rule_99_b(PageAnalysisRule page_analysis_rule_99_b) {
		this.page_analysis_rule_99_b = page_analysis_rule_99_b;
	}

	public PageAnalysisRule getPage_analysis_rule_100_b() {
		return page_analysis_rule_100_b;
	}

	public void setPage_analysis_rule_100_b(PageAnalysisRule page_analysis_rule_100_b) {
		this.page_analysis_rule_100_b = page_analysis_rule_100_b;
	}

	public PageAnalysisRule getPage_analysis_rule_101_b() {
		return page_analysis_rule_101_b;
	}

	public void setPage_analysis_rule_101_b(PageAnalysisRule page_analysis_rule_101_b) {
		this.page_analysis_rule_101_b = page_analysis_rule_101_b;
	}

	public PageAnalysisRule getPage_analysis_rule_102_b() {
		return page_analysis_rule_102_b;
	}

	public void setPage_analysis_rule_102_b(PageAnalysisRule page_analysis_rule_102_b) {
		this.page_analysis_rule_102_b = page_analysis_rule_102_b;
	}

	public PageAnalysisRule getPage_analysis_rule_103_b() {
		return page_analysis_rule_103_b;
	}

	public void setPage_analysis_rule_103_b(PageAnalysisRule page_analysis_rule_103_b) {
		this.page_analysis_rule_103_b = page_analysis_rule_103_b;
	}

	public PageAnalysisRule getPage_analysis_rule_104_b() {
		return page_analysis_rule_104_b;
	}

	public void setPage_analysis_rule_104_b(PageAnalysisRule page_analysis_rule_104_b) {
		this.page_analysis_rule_104_b = page_analysis_rule_104_b;
	}

	public PageAnalysisRule getPage_analysis_rule_105_b() {
		return page_analysis_rule_105_b;
	}

	public void setPage_analysis_rule_105_b(PageAnalysisRule page_analysis_rule_105_b) {
		this.page_analysis_rule_105_b = page_analysis_rule_105_b;
	}

	public String getFile_name() {
		return file_name;
	}

	public void setFile_name(String file_name) {
		this.file_name = file_name;
	}

	public String getBase_tag() {
		return base_tag;
	}

	public void setBase_tag(String base_tag) {
		this.base_tag = base_tag;
	}

	public Boolean getBase_tag_flag() {
		return base_tag_flag;
	}

	public void setBase_tag_flag(Boolean base_tag_flag) {
		this.base_tag_flag = base_tag_flag;
	}

	public String getBase_tag_target() {
		return base_tag_target;
	}

	public void setBase_tag_target(String base_tag_target) {
		this.base_tag_target = base_tag_target;
	}

	@Override
	public String toString() {
		return "CrawlerResponse [alt_img_list=" + Arrays.toString(alt_img_list) + ", alternate_links=" + Arrays.toString(alternate_links) + ", amphtml_flag="
				+ amphtml_flag + ", amphtml_href=" + amphtml_href + ", analyzed_url_flg_s=" + analyzed_url_flg_s + ", analyzed_url_s=" + analyzed_url_s
				+ ", archive_flg=" + archive_flg + ", archive_flg_x_tag=" + archive_flg_x_tag + ", blocked_by_robots=" + blocked_by_robots + ", canonical=" + canonical
				+ ", canonical_flg=" + canonical_flg + ", canonical_header_flag=" + canonical_header_flag + ", canonical_header_type=" + canonical_header_type
				+ ", canonical_type=" + canonical_type + ", canonical_url_is_consistent=" + canonical_url_is_consistent + ", content_type=" + content_type
				+ ", count_of_objects=" + count_of_objects + ", custom_data=" + Arrays.toString(custom_data) + ", description=" + description + ", description_flg="
				+ description_flg + ", description_length=" + description_length + ", description_simhash=" + description_simhash + ", document_size=" + document_size
				+ ", download_latency=" + download_latency + ", download_time=" + download_time + ", error_message=" + error_message + ", final_response_code="
				+ final_response_code + ", folder_level_1=" + folder_level_1 + ", folder_level_2=" + folder_level_2 + ", folder_level_3=" + folder_level_3
				+ ", folder_level_count=" + folder_level_count + ", follow_flg=" + follow_flg + ", follow_flg_x_tag=" + follow_flg_x_tag + ", h1=" + Arrays.toString(h1)
				+ ", h1_count=" + h1_count + ", h1_flg=" + h1_flg + ", h1_length=" + h1_length + ", h1_md5=" + Arrays.toString(h1_md5) + ", h1_simhash=" + h1_simhash
				+ ", h2=" + Arrays.toString(h2) + ", h2_simhash=" + h2_simhash + ", header_noarchive=" + header_noarchive + ", header_nofollow=" + header_nofollow
				+ ", header_noindex=" + header_noindex + ", header_noodp=" + header_noodp + ", header_nosnippet=" + header_nosnippet + ", header_noydir="
				+ header_noydir + ", hreflang_errors=" + hreflang_errors + ", hreflang_links=" + Arrays.toString(hreflang_links) + ", hreflang_links_out_count="
				+ hreflang_links_out_count + ", hreflang_url_count=" + hreflang_url_count + ", index_flg=" + index_flg + ", index_flg_x_tag=" + index_flg_x_tag
				+ ", indexable=" + indexable + ", insecure_resources=" + Arrays.toString(insecure_resources) + ", insecure_resources_flag=" + insecure_resources_flag
				+ ", long_redirect=" + long_redirect + ", meta_charset=" + meta_charset + ", meta_content_type=" + meta_content_type + ", meta_disabled_sitelinks="
				+ meta_disabled_sitelinks + ", meta_noodp=" + meta_noodp + ", meta_nosnippet=" + meta_nosnippet + ", meta_noydir=" + meta_noydir + ", meta_redirect="
				+ meta_redirect + ", mixed_redirects=" + mixed_redirects + ", mobile_rel_alternate_url_is_consistent=" + mobile_rel_alternate_url_is_consistent
				+ ", noodp=" + noodp + ", nosnippet=" + nosnippet + ", noydir=" + noydir + ", og_markup=" + Arrays.toString(og_markup) + ", og_markup_flag="
				+ og_markup_flag + ", og_markup_length=" + og_markup_length + ", outlink_count=" + outlink_count + ", page_1=" + page_1 + ", page_analysis_rule_10_b="
				+ page_analysis_rule_10_b + ", page_analysis_rule_11_b=" + page_analysis_rule_11_b + ", page_analysis_rule_12_b=" + page_analysis_rule_12_b
				+ ", page_analysis_rule_13_b=" + page_analysis_rule_13_b + ", page_analysis_rule_14_b=" + page_analysis_rule_14_b + ", page_analysis_rule_15_b="
				+ page_analysis_rule_15_b + ", page_analysis_rule_16_b=" + page_analysis_rule_16_b + ", page_analysis_rule_17_b=" + page_analysis_rule_17_b
				+ ", page_analysis_rule_18_b=" + page_analysis_rule_18_b + ", page_analysis_rule_19_b=" + page_analysis_rule_19_b + ", page_analysis_rule_1_b="
				+ page_analysis_rule_1_b + ", page_analysis_rule_20_b=" + page_analysis_rule_20_b + ", page_analysis_rule_21_b=" + page_analysis_rule_21_b
				+ ", page_analysis_rule_22_b=" + page_analysis_rule_22_b + ", page_analysis_rule_23_b=" + page_analysis_rule_23_b + ", page_analysis_rule_24_b="
				+ page_analysis_rule_24_b + ", page_analysis_rule_25_b=" + page_analysis_rule_25_b + ", page_analysis_rule_26_b=" + page_analysis_rule_26_b
				+ ", page_analysis_rule_27_b=" + page_analysis_rule_27_b + ", page_analysis_rule_28_b=" + page_analysis_rule_28_b + ", page_analysis_rule_29_b="
				+ page_analysis_rule_29_b + ", page_analysis_rule_2_b=" + page_analysis_rule_2_b + ", page_analysis_rule_30_b=" + page_analysis_rule_30_b
				+ ", page_analysis_rule_31_b=" + page_analysis_rule_31_b + ", page_analysis_rule_32_b=" + page_analysis_rule_32_b + ", page_analysis_rule_33_b="
				+ page_analysis_rule_33_b + ", page_analysis_rule_34_b=" + page_analysis_rule_34_b + ", page_analysis_rule_35_b=" + page_analysis_rule_35_b
				+ ", page_analysis_rule_36_b=" + page_analysis_rule_36_b + ", page_analysis_rule_37_b=" + page_analysis_rule_37_b + ", page_analysis_rule_38_b="
				+ page_analysis_rule_38_b + ", page_analysis_rule_39_b=" + page_analysis_rule_39_b + ", page_analysis_rule_3_b=" + page_analysis_rule_3_b
				+ ", page_analysis_rule_40_b=" + page_analysis_rule_40_b + ", page_analysis_rule_41_b=" + page_analysis_rule_41_b + ", page_analysis_rule_42_b="
				+ page_analysis_rule_42_b + ", page_analysis_rule_43_b=" + page_analysis_rule_43_b + ", page_analysis_rule_44_b=" + page_analysis_rule_44_b
				+ ", page_analysis_rule_45_b=" + page_analysis_rule_45_b + ", page_analysis_rule_46_b=" + page_analysis_rule_46_b + ", page_analysis_rule_47_b="
				+ page_analysis_rule_47_b + ", page_analysis_rule_48_b=" + page_analysis_rule_48_b + ", page_analysis_rule_49_b=" + page_analysis_rule_49_b
				+ ", page_analysis_rule_4_b=" + page_analysis_rule_4_b + ", page_analysis_rule_50_b=" + page_analysis_rule_50_b + ", page_analysis_rule_51_b="
				+ page_analysis_rule_51_b + ", page_analysis_rule_52_b=" + page_analysis_rule_52_b + ", page_analysis_rule_53_b=" + page_analysis_rule_53_b
				+ ", page_analysis_rule_54_b=" + page_analysis_rule_54_b + ", page_analysis_rule_55_b=" + page_analysis_rule_55_b + ", page_analysis_rule_56_b="
				+ page_analysis_rule_56_b + ", page_analysis_rule_57_b=" + page_analysis_rule_57_b + ", page_analysis_rule_58_b=" + page_analysis_rule_58_b
				+ ", page_analysis_rule_59_b=" + page_analysis_rule_59_b + ", page_analysis_rule_5_b=" + page_analysis_rule_5_b + ", page_analysis_rule_60_b="
				+ page_analysis_rule_60_b + ", page_analysis_rule_61_b=" + page_analysis_rule_61_b + ", page_analysis_rule_62_b=" + page_analysis_rule_62_b
				+ ", page_analysis_rule_63_b=" + page_analysis_rule_63_b + ", page_analysis_rule_64_b=" + page_analysis_rule_64_b + ", page_analysis_rule_65_b="
				+ page_analysis_rule_65_b + ", page_analysis_rule_6_b=" + page_analysis_rule_6_b + ", page_analysis_rule_7_b=" + page_analysis_rule_7_b
				+ ", page_analysis_rule_8_b=" + page_analysis_rule_8_b + ", page_analysis_rule_9_b=" + page_analysis_rule_9_b 
				//+ ", page_link=" + Arrays.toString(page_link) 
				+ ", page_timeout_flag=" + page_timeout_flag + ", paginated=" + paginated + ", pagination_links="
				+ Arrays.toString(pagination_links) + ", protocol=" + protocol + ", redirect_blocked=" + redirect_blocked + ", redirect_blocked_reason="
				+ redirect_blocked_reason + ", redirect_chain=" + Arrays.toString(redirect_chain) + ", redirect_final_url=" + redirect_final_url + ", redirect_flg="
				+ redirect_flg + ", redirect_times=" + redirect_times + ", rel_next_html_url=" + rel_next_html_url + ", rel_next_url_is_consistent="
				+ rel_next_url_is_consistent + ", rel_prev_url_is_consistent=" + rel_prev_url_is_consistent + ", request_headers=" + request_headers + ", request_time="
				+ request_time + ", response_code=" + response_code + ", response_headers=" + Arrays.toString(response_headers) + ", retry_attempted=" + retry_attempted
				+ ", robots=" + robots + ", robots_contents=" + robots_contents + ", robots_contents_x_tag=" + robots_contents_x_tag + ", robots_flg=" + robots_flg
				+ ", robots_flg_x_tag=" + robots_flg_x_tag + ", server_response_time=" + server_response_time + ", source_url=" + Arrays.toString(source_url)
				+ ", splash_took=" + splash_took + ", structured_data=" + structured_data + ", title=" + title + ", title_flg=" + title_flg + ", title_length="
				+ title_length + ", title_md5=" + Arrays.toString(title_md5) + ", title_simhash=" + title_simhash + ", twitter_description_length="
				+ twitter_description_length + ", twitter_markup=" + Arrays.toString(twitter_markup) + ", twitter_markup_flag=" + twitter_markup_flag
				+ ", twitter_markup_length=" + twitter_markup_length + ", url_length=" + url_length + ", valid_twitter_card=" + valid_twitter_card
				+ ", viewport_content=" + viewport_content + ", viewport_flag=" + viewport_flag + ", page_analysis_rule_66_b=" + page_analysis_rule_66_b
				+ ", page_analysis_rule_67_b=" + page_analysis_rule_67_b + ", page_analysis_rule_68_b=" + page_analysis_rule_68_b + ", page_analysis_rule_69_b="
				+ page_analysis_rule_69_b + ", page_analysis_rule_70_b=" + page_analysis_rule_70_b + ", page_analysis_rule_71_b=" + page_analysis_rule_71_b
				+ ", page_analysis_rule_72_b=" + page_analysis_rule_72_b + ", page_analysis_rule_73_b=" + page_analysis_rule_73_b + ", page_analysis_rule_74_b="
				+ page_analysis_rule_74_b + ", page_analysis_rule_75_b=" + page_analysis_rule_75_b + ", page_analysis_rule_76_b=" + page_analysis_rule_76_b
				+ ", page_analysis_rule_77_b=" + page_analysis_rule_77_b + ", page_analysis_rule_78_b=" + page_analysis_rule_78_b + ", page_analysis_rule_79_b="
				+ page_analysis_rule_79_b + ", page_analysis_rule_80_b=" + page_analysis_rule_80_b + ", page_analysis_rule_81_b=" + page_analysis_rule_81_b
				+ ", page_analysis_rule_82_b=" + page_analysis_rule_82_b + ", page_analysis_rule_83_b=" + page_analysis_rule_83_b + ", page_analysis_rule_84_b="
				+ page_analysis_rule_84_b + ", page_analysis_rule_85_b=" + page_analysis_rule_85_b + ", page_analysis_rule_86_b=" + page_analysis_rule_86_b
				+ ", page_analysis_rule_87_b=" + page_analysis_rule_87_b + ", page_analysis_rule_88_b=" + page_analysis_rule_88_b + ", page_analysis_rule_89_b="
				+ page_analysis_rule_89_b + ", page_analysis_rule_90_b=" + page_analysis_rule_90_b + ", page_analysis_rule_91_b=" + page_analysis_rule_91_b
				+ ", page_analysis_rule_92_b=" + page_analysis_rule_92_b + ", page_analysis_rule_93_b=" + page_analysis_rule_93_b + ", page_analysis_rule_94_b="
				+ page_analysis_rule_94_b + ", page_analysis_rule_95_b=" + page_analysis_rule_95_b + ", page_analysis_rule_96_b=" + page_analysis_rule_96_b
				+ ", page_analysis_rule_97_b=" + page_analysis_rule_97_b + ", page_analysis_rule_98_b=" + page_analysis_rule_98_b + ", page_analysis_rule_99_b="
				+ page_analysis_rule_99_b + ", page_analysis_rule_100_b=" + page_analysis_rule_100_b + ", page_analysis_rule_101_b=" + page_analysis_rule_101_b
				+ ", page_analysis_rule_102_b=" + page_analysis_rule_102_b + ", page_analysis_rule_103_b=" + page_analysis_rule_103_b + ", page_analysis_rule_104_b="
				+ page_analysis_rule_104_b + ", page_analysis_rule_105_b=" + page_analysis_rule_105_b + ", file_name=" + file_name + ", base_tag=" + base_tag
				+ ", base_tag_flag=" + base_tag_flag + ", base_tag_target=" + base_tag_target + "]";
	}

}
