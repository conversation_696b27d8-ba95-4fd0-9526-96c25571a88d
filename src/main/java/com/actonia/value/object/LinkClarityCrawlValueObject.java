package com.actonia.value.object;

import javax.persistence.Transient;

public class LinkClarityCrawlValueObject {
	private String hashCode;
	private String hostname;
	private String url;
	private int totalUrls;
	private String domainIdLanguageCodeJson;

	public String getHashCode() {
		return hashCode;
	}

	public void setHashCode(String hashCode) {
		this.hashCode = hashCode;
	}

	public String getHostname() {
		return hostname;
	}

	public void setHostname(String hostname) {
		this.hostname = hostname;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getDomainIdLanguageCodeJson() {
		return domainIdLanguageCodeJson;
	}

	public void setDomainIdLanguageCodeJson(String domainIdLanguageCodeJson) {
		this.domainIdLanguageCodeJson = domainIdLanguageCodeJson;
	}

	@Override
	public String toString() {
		return "LinkClarityCrawlValueObject [hashCode=" + hashCode + ", hostname=" + hostname + ", url=" + url + ", domainIdLanguageCodeJson="
				+ domainIdLanguageCodeJson + "]";
	}

	@Transient
	public int getTotalUrls() {
		return totalUrls;
	}

	@Transient
	public void setTotalUrls(int totalUrls) {
		this.totalUrls = totalUrls;
	}

}