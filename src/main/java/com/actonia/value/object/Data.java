package com.actonia.value.object;

import java.util.Arrays;

public class Data {
	private CheckStructuredData[] check_structured_data;
	private ValidateStructuredData[] validate_structured_data;

	public CheckStructuredData[] getCheck_structured_data() {
		return check_structured_data;
	}

	public void setCheck_structured_data(CheckStructuredData[] check_structured_data) {
		this.check_structured_data = check_structured_data;
	}

	public ValidateStructuredData[] getValidate_structured_data() {
		return validate_structured_data;
	}

	public void setValidate_structured_data(ValidateStructuredData[] validate_structured_data) {
		this.validate_structured_data = validate_structured_data;
	}

	@Override
	public String toString() {
		return "Data [check_structured_data=" + Arrays.toString(check_structured_data) + ", validate_structured_data=" + Arrays.toString(validate_structured_data)
				+ "]";
	}

}
