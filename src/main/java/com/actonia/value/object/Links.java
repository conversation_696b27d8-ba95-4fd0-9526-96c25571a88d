package com.actonia.value.object;

public class Links {
	private String anchor_text;
	private String destination_url;
	private String rel_attribute_contents;
	public String getAnchor_text() {
		return anchor_text;
	}
	public void setAnchor_text(String anchor_text) {
		this.anchor_text = anchor_text;
	}
	public String getDestination_url() {
		return destination_url;
	}
	public void setDestination_url(String destination_url) {
		this.destination_url = destination_url;
	}
	public String getRel_attribute_contents() {
		return rel_attribute_contents;
	}
	public void setRel_attribute_contents(String rel_attribute_contents) {
		this.rel_attribute_contents = rel_attribute_contents;
	}
	@Override
	public String toString() {
		return "Links [anchor_text=" + anchor_text + ", destination_url=" + destination_url + ", rel_attribute_contents=" + rel_attribute_contents + "]";
	}

}
