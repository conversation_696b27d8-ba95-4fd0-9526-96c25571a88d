package com.actonia.value.object;

import java.util.Date;

public class TargetUrlHtmlDataRemovalValueObject {

	private Date crawlTimestamp;
	private Integer domainId;
	private String targetUrl;
	private String sourceUrl;

	public Date getCrawlTimestamp() {
		return crawlTimestamp;
	}

	public void setCrawlTimestamp(Date crawlTimestamp) {
		this.crawlTimestamp = crawlTimestamp;
	}

	public Integer getDomainId() {
		return domainId;
	}

	public void setDomainId(Integer domainId) {
		this.domainId = domainId;
	}

	public String getTargetUrl() {
		return targetUrl;
	}

	public void setTargetUrl(String targetUrl) {
		this.targetUrl = targetUrl;
	}

	public String getSourceUrl() {
		return sourceUrl;
	}

	public void setSourceUrl(String sourceUrl) {
		this.sourceUrl = sourceUrl;
	}

	@Override
	public String toString() {
		return "DataRemovalValueObject [crawlTimestamp=" + crawlTimestamp + ", domainId=" + domainId + ", targetUrl=" + targetUrl + ", sourceUrl=" + sourceUrl + "]";
	}

}
