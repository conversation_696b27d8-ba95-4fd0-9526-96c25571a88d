package com.actonia.value.object;

public class PoliteCrawlSummaryValueObject implements Cloneable {
	private int domainId;
	private String domainName;
	private int domainType;
	private int totalUrls;
	private int httpStatusCode;
	private int totalUrlsWithResponseCode;

	@Override
	public PoliteCrawlSummaryValueObject clone() throws CloneNotSupportedException {
		return (PoliteCrawlSummaryValueObject) super.clone();
	}

	public int getDomainId() {
		return domainId;
	}

	public void setDomainId(int domainId) {
		this.domainId = domainId;
	}

	public String getDomainName() {
		return domainName;
	}

	public void setDomainName(String domainName) {
		this.domainName = domainName;
	}

	public int getTotalUrls() {
		return totalUrls;
	}

	public void setTotalUrls(int totalUrls) {
		this.totalUrls = totalUrls;
	}

	public int getHttpStatusCode() {
		return httpStatusCode;
	}

	public void setHttpStatusCode(int httpStatusCode) {
		this.httpStatusCode = httpStatusCode;
	}

	public int getTotalUrlsWithResponseCode() {
		return totalUrlsWithResponseCode;
	}

	public void setTotalUrlsWithResponseCode(int totalUrlsWithResponseCode) {
		this.totalUrlsWithResponseCode = totalUrlsWithResponseCode;
	}

	public int getDomainType() {
		return domainType;
	}

	public void setDomainType(int domainType) {
		this.domainType = domainType;
	}

	@Override
	public String toString() {
		return "PoliteCrawlSummaryValueObject [domainId=" + domainId + ", domainName=" + domainName + ", domainType=" + domainType + ", totalUrls=" + totalUrls
				+ ", httpStatusCode=" + httpStatusCode + ", totalUrlsWithResponseCode=" + totalUrlsWithResponseCode + "]";
	}

}