package com.actonia.value.object;

public class ResponseCodeTotalUrls {
	private String response_code_previous;
	private String response_code_current;
	private Integer total_urls = 0;

	public String getResponse_code_previous() {
		return response_code_previous;
	}

	public void setResponse_code_previous(String response_code_previous) {
		this.response_code_previous = response_code_previous;
	}

	public String getResponse_code_current() {
		return response_code_current;
	}

	public void setResponse_code_current(String response_code_current) {
		this.response_code_current = response_code_current;
	}

	public Integer getTotal_urls() {
		return total_urls;
	}

	public void setTotal_urls(Integer total_urls) {
		this.total_urls = total_urls;
	}

	@Override
	public String toString() {
		return "ResponseCodeTotalUrls [response_code_previous=" + response_code_previous + ", response_code_current=" + response_code_current + ", total_urls="
				+ total_urls + "]";
	}

}
