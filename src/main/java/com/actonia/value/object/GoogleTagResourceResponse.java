package com.actonia.value.object;

public class GoogleTagResourceResponse {
	private Boolean success;
	private WebServiceError error;
	private String data;

	private String triggerId; // not to be sent back to invoking app
	private String tagId; // not to be sent back to invoking app
	private String containerVersionId; // not to be sent back to invoking app

	public Boolean getSuccess() {
		return success;
	}

	public void setSuccess(Boolean success) {
		this.success = success;
	}

	public WebServiceError getError() {
		return error;
	}

	public void setError(WebServiceError error) {
		this.error = error;
	}

	public String getData() {
		return data;
	}

	public void setData(String data) {
		this.data = data;
	}

	public String getTriggerId() {
		return triggerId;
	}

	public void setTriggerId(String triggerId) {
		this.triggerId = triggerId;
	}

	public String getTagId() {
		return tagId;
	}

	public void setTagId(String tagId) {
		this.tagId = tagId;
	}

	public String getContainerVersionId() {
		return containerVersionId;
	}

	public void setContainerVersionId(String containerVersionId) {
		this.containerVersionId = containerVersionId;
	}

	@Override
	public String toString() {
		return "GoogleTagResourceResponse [success=" + success + ", error=" + error + ", data=" + data + ", triggerId=" + triggerId + ", tagId=" + tagId
				+ ", containerVersionId=" + containerVersionId + "]";
	}

}
