package com.actonia.value.object;

import java.util.Arrays;

public class ContentGuardHourlySeverity {
	private Integer crawl_hour;
	private ContentGuardSeverity[] severity_list;

	public Integer getCrawl_hour() {
		return crawl_hour;
	}

	public void setCrawl_hour(Integer crawl_hour) {
		this.crawl_hour = crawl_hour;
	}

	public ContentGuardSeverity[] getSeverity_list() {
		return severity_list;
	}

	public void setSeverity_list(ContentGuardSeverity[] severity_list) {
		this.severity_list = severity_list;
	}

	@Override
	public String toString() {
		return "HourlySeverity [crawl_hour=" + crawl_hour + ", severity_list=" + Arrays.toString(severity_list) + "]";
	}

}
