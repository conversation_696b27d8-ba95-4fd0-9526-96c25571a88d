package com.actonia.value.object;

import java.util.Arrays;

public class TargetUrlChangeDailySummary {
	private String crawl_date;
	private Integer total_added = 0;
	private Integer total_modified = 0;
	private Integer total_removed = 0;
	private int total_severity_critical = 0;
	private int total_severity_high = 0;
	private int total_severity_medium = 0;
	private int total_severity_low = 0;
	private ChangeIndicatorTotalUrls[] change_indicator_total_urls_array;
	private ResponseCodeTotalUrls[] response_code_total_urls_array;

	public String getCrawl_date() {
		return crawl_date;
	}

	public void setCrawl_date(String crawl_date) {
		this.crawl_date = crawl_date;
	}

	public Integer getTotal_added() {
		return total_added;
	}

	public void setTotal_added(Integer total_added) {
		this.total_added = total_added;
	}

	public Integer getTotal_modified() {
		return total_modified;
	}

	public void setTotal_modified(Integer total_modified) {
		this.total_modified = total_modified;
	}

	public Integer getTotal_removed() {
		return total_removed;
	}

	public void setTotal_removed(Integer total_removed) {
		this.total_removed = total_removed;
	}

	public ChangeIndicatorTotalUrls[] getChange_indicator_total_urls_array() {
		return change_indicator_total_urls_array;
	}

	public void setChange_indicator_total_urls_array(ChangeIndicatorTotalUrls[] change_indicator_total_urls_array) {
		this.change_indicator_total_urls_array = change_indicator_total_urls_array;
	}

	public ResponseCodeTotalUrls[] getResponse_code_total_urls_array() {
		return response_code_total_urls_array;
	}

	public void setResponse_code_total_urls_array(ResponseCodeTotalUrls[] response_code_total_urls_array) {
		this.response_code_total_urls_array = response_code_total_urls_array;
	}

	public int getTotal_severity_critical() {
		return total_severity_critical;
	}

	public void setTotal_severity_critical(int total_severity_critical) {
		this.total_severity_critical = total_severity_critical;
	}

	public int getTotal_severity_high() {
		return total_severity_high;
	}

	public void setTotal_severity_high(int total_severity_high) {
		this.total_severity_high = total_severity_high;
	}

	public int getTotal_severity_medium() {
		return total_severity_medium;
	}

	public void setTotal_severity_medium(int total_severity_medium) {
		this.total_severity_medium = total_severity_medium;
	}

	public int getTotal_severity_low() {
		return total_severity_low;
	}

	public void setTotal_severity_low(int total_severity_low) {
		this.total_severity_low = total_severity_low;
	}

	@Override
	public String toString() {
		return "TargetUrlChangeDailySummary [crawl_date=" + crawl_date + ", total_added=" + total_added + ", total_modified=" + total_modified + ", total_removed="
				+ total_removed + ", change_indicator_total_urls_array=" + Arrays.toString(change_indicator_total_urls_array) + ", response_code_total_urls_array="
				+ Arrays.toString(response_code_total_urls_array) + ", total_severity_critical=" + total_severity_critical + ", total_severity_high="
				+ total_severity_high + ", total_severity_medium=" + total_severity_medium + ", total_severity_low=" + total_severity_low + "]";
	}

}
