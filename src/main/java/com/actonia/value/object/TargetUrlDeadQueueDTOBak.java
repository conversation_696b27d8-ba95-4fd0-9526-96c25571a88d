package com.actonia.value.object;

import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.UrlMetricsEntityV3;

public class TargetUrlDeadQueueDTOBak {
    private String sendMessageTime;
    private int domainId;
    private DomainCrawlParameters domainCrawlParameters;
    private UrlMetricsEntityV3 urlMetricsEntityV3;
    private HtmlClickHouseEntity htmlClickHouseEntityPrevious;
    private String receiptHandle;
    private ScrapyCrawlerRequest scrapyCrawlerRequest;

    // create instance in politeCrawlCommand
    public static TargetUrlDeadQueueDTOBak createByUrlMetricsAndHtmlClickHousePrevious(UrlMetricsEntityV3 urlMetricsEntityV3, HtmlClickHouseEntity htmlClickHouseEntityPrevious, ScrapyCrawlerRequest scrapyCrawlerRequest) {
        TargetUrlDeadQueueDTOBak targetUrlDeadQueueDTO = new TargetUrlDeadQueueDTOBak();
        targetUrlDeadQueueDTO.setUrlMetricsEntityV3(urlMetricsEntityV3);
        targetUrlDeadQueueDTO.setHtmlClickHouseEntityPrevious(htmlClickHouseEntityPrevious);
        targetUrlDeadQueueDTO.setScrapyCrawlerRequest(scrapyCrawlerRequest);
        return targetUrlDeadQueueDTO;
    }

    public String getSendMessageTime() {
        return sendMessageTime;
    }

    public void setSendMessageTime(String sendMessageTime) {
        this.sendMessageTime = sendMessageTime;
    }

    public int getDomainId() {
        return domainId;
    }

    public void setDomainId(int domainId) {
        this.domainId = domainId;
    }

    public DomainCrawlParameters getDomainCrawlParameters() {
        return domainCrawlParameters;
    }

    public void setDomainCrawlParameters(DomainCrawlParameters domainCrawlParameters) {
        this.domainCrawlParameters = domainCrawlParameters;
    }

    public UrlMetricsEntityV3 getUrlMetricsEntityV3() {
        return urlMetricsEntityV3;
    }

    public void setUrlMetricsEntityV3(UrlMetricsEntityV3 urlMetricsEntityV3) {
        this.urlMetricsEntityV3 = urlMetricsEntityV3;
    }

    public String getReceiptHandle() {
        return receiptHandle;
    }

    public void setReceiptHandle(String receiptHandle) {
        this.receiptHandle = receiptHandle;
    }

    public HtmlClickHouseEntity getHtmlClickHouseEntityPrevious() {
        return htmlClickHouseEntityPrevious;
    }

    public void setHtmlClickHouseEntityPrevious(HtmlClickHouseEntity htmlClickHouseEntityPrevious) {
        this.htmlClickHouseEntityPrevious = htmlClickHouseEntityPrevious;
    }

    public ScrapyCrawlerRequest getScrapyCrawlerRequest() {
        return scrapyCrawlerRequest;
    }

    public void setScrapyCrawlerRequest(ScrapyCrawlerRequest scrapyCrawlerRequest) {
        this.scrapyCrawlerRequest = scrapyCrawlerRequest;
    }
}
