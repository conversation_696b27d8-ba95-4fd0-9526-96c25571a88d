package com.actonia.value.object;

public class ExtractHtmlValueObject {
	private String urlString;
	private String responseCode;
	private String htmlFileName;

	public String getUrlString() {
		return urlString;
	}

	public void setUrlString(String urlString) {
		this.urlString = urlString;
	}

	public String getResponseCode() {
		return responseCode;
	}

	public void setResponseCode(String responseCode) {
		this.responseCode = responseCode;
	}

	public String getHtmlFileName() {
		return htmlFileName;
	}

	public void setHtmlFileName(String htmlFileName) {
		this.htmlFileName = htmlFileName;
	}

	@Override
	public String toString() {
		return "ExtractHtmlValueObject [urlString=" + urlString + ", responseCode=" + responseCode + ", htmlFileName=" + htmlFileName + "]";
	}

}
