package com.actonia.value.object;

import java.util.Map;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class JavascriptSettings {
	private Float wait;
	private Float timeout;
	private Float page_load_timeout;
	private Float wait_after_last_request;

	// https://www.wrike.com/open.htm?id=837498886
	private boolean audit_resources;

	@SerializedName("headers")
	@Expose
	private Map<String, String> headers;

	public Float getWait() {
		return wait;
	}

	public void setWait(Float wait) {
		this.wait = wait;
	}

	public Float getTimeout() {
		return timeout;
	}

	public void setTimeout(Float timeout) {
		this.timeout = timeout;
	}

	public Float getPage_load_timeout() {
		return page_load_timeout;
	}

	public void setPage_load_timeout(Float page_load_timeout) {
		this.page_load_timeout = page_load_timeout;
	}

	public Float getWait_after_last_request() {
		return wait_after_last_request;
	}

	public void setWait_after_last_request(Float wait_after_last_request) {
		this.wait_after_last_request = wait_after_last_request;
	}

	public Map<String, String> getHeaders() {
		return headers;
	}

	public void setHeaders(Map<String, String> headers) {
		this.headers = headers;
	}

	public boolean isAudit_resources() {
		return audit_resources;
	}

	public void setAudit_resources(boolean audit_resources) {
		this.audit_resources = audit_resources;
	}

	@Override
	public String toString() {
		return "JavascriptSettings [wait=" + wait + ", timeout=" + timeout + ", page_load_timeout=" + page_load_timeout + ", wait_after_last_request="
				+ wait_after_last_request + ", audit_resources=" + audit_resources + ", headers=" + headers + "]";
	}

}
