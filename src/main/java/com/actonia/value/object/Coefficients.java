package com.actonia.value.object;

import java.util.Arrays;

public class Coefficients {
	private String[] market_array;
	private Double[] average_beta;

	public String[] getMarket_array() {
		return market_array;
	}

	public void setMarket_array(String[] market_array) {
		this.market_array = market_array;
	}

	public Double[] getAverage_beta() {
		return average_beta;
	}

	public void setAverage_beta(Double[] average_beta) {
		this.average_beta = average_beta;
	}

	@Override
	public String toString() {
		return "Coefficients [market_array=" + Arrays.toString(market_array) + ", average_beta=" + Arrays.toString(average_beta) + "]";
	}

}
