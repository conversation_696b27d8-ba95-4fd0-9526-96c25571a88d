package com.actonia.value.object;

import java.util.Arrays;
import java.util.List;

public class CausalImpactRequest {

	private String access_token;

	// attributes for
	// 1) causal impact analysis
	private String pre_period_start_date;
	private String pre_period_end_date;
	private String post_period_start_date;
	private String post_period_end_date;

	// attributes for
	// 1) causal impact analysis
	// 2) calculating correlation coefficients
	private double[] test_time_series;
	private List<String> control_name_list;
	private List<double[]> control_time_series_list;
	private Integer version;

	public String getAccess_token() {
		return access_token;
	}

	public void setAccess_token(String access_token) {
		this.access_token = access_token;
	}

	public String getPre_period_start_date() {
		return pre_period_start_date;
	}

	public void setPre_period_start_date(String pre_period_start_date) {
		this.pre_period_start_date = pre_period_start_date;
	}

	public String getPre_period_end_date() {
		return pre_period_end_date;
	}

	public void setPre_period_end_date(String pre_period_end_date) {
		this.pre_period_end_date = pre_period_end_date;
	}

	public String getPost_period_start_date() {
		return post_period_start_date;
	}

	public void setPost_period_start_date(String post_period_start_date) {
		this.post_period_start_date = post_period_start_date;
	}

	public String getPost_period_end_date() {
		return post_period_end_date;
	}

	public void setPost_period_end_date(String post_period_end_date) {
		this.post_period_end_date = post_period_end_date;
	}

	public double[] getTest_time_series() {
		return test_time_series;
	}

	public void setTest_time_series(double[] test_time_series) {
		this.test_time_series = test_time_series;
	}

	public List<String> getControl_name_list() {
		return control_name_list;
	}

	public void setControl_name_list(List<String> control_name_list) {
		this.control_name_list = control_name_list;
	}

	public List<double[]> getControl_time_series_list() {
		return control_time_series_list;
	}

	public void setControl_time_series_list(List<double[]> control_time_series_list) {
		this.control_time_series_list = control_time_series_list;
	}

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    @Override
	public String toString() {
		return "CausalImpactRequest [access_token=" + access_token + ", pre_period_start_date=" + pre_period_start_date + ", pre_period_end_date=" + pre_period_end_date
				+ ", post_period_start_date=" + post_period_start_date + ", post_period_end_date=" + post_period_end_date + ", test_time_series="
				+ Arrays.toString(test_time_series) + ", control_name_list=" + control_name_list + ", control_time_series_list=" + control_time_series_list + " version=" + version + "]";
	}

}
