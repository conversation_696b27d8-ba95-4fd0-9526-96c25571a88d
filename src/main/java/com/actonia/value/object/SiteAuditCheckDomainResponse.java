package com.actonia.value.object;

import java.util.Arrays;

public class SiteAuditCheckDomainResponse {
	private Response[] response;
	private String[] messages;
	private String startTime;
	private String endTime;
	private String message;
	private String finalURL;
	private String url;
	private String processingTime;
	private Integer status;

	public Response[] getResponse() {
		return response;
	}

	public void setResponse(Response[] response) {
		this.response = response;
	}

	public String[] getMessages() {
		return messages;
	}

	public void setMessages(String[] messages) {
		this.messages = messages;
	}

	public String getStartTime() {
		return startTime;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public String getFinalURL() {
		return finalURL;
	}

	public void setFinalURL(String finalURL) {
		this.finalURL = finalURL;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getProcessingTime() {
		return processingTime;
	}

	public void setProcessingTime(String processingTime) {
		this.processingTime = processingTime;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	@Override
	public String toString() {
		return "SiteAuditCheckDomainResponse [response=" + Arrays.toString(response) + ", messages=" + Arrays.toString(messages) + ", startTime=" + startTime
				+ ", endTime=" + endTime + ", message=" + message + ", finalURL=" + finalURL + ", url=" + url + ", processingTime=" + processingTime + ", status="
				+ status + "]";
	}

}
