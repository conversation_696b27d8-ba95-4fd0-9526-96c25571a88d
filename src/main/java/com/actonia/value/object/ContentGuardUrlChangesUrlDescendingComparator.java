package com.actonia.value.object;

import java.util.Comparator;

public class ContentGuardUrlChangesUrlDescendingComparator implements Comparator<ContentGuardUrlChanges> {

	@Override
	public int compare(ContentGuardUrlChanges arg0, ContentGuardUrlChanges arg1) {

		// sort by:
		// 1) url (descending order)

		int response = 0;
		if (arg0 != null && arg1 != null) {
			// url
			if (arg0.getUrl().compareTo(arg1.getUrl()) > 0) { // when arg0 is greater than arg1
				response = -1;
			} else if (arg0.getUrl().compareTo(arg1.getUrl()) < 0) { // when arg0 is smaller than arg1
				response = +1;
			}
		} else if (arg0 != null && arg1 == null) { // when arg0 is greater than arg1
			response = -1;
		} else if (arg0 == null && arg1 != null) { // when arg0 is smaller than arg1
			response = +1;
		}
		return response;
	}

}
