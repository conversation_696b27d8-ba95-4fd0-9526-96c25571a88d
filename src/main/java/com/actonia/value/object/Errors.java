package com.actonia.value.object;

import java.util.Arrays;

public class Errors {
	private String message;
	private String type;
	private String[] path;

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String[] getPath() {
		return path;
	}

	public void setPath(String[] path) {
		this.path = path;
	}

	@Override
	public String toString() {
		return "Errors [message=" + message + ", type=" + type + ", path=" + Arrays.toString(path) + "]";
	}

}
