package com.actonia.value.object;

public class AssociateTopRankedTargetUrlSummaryValueObject {
	private int domainId;
	private String domainName;
	private int totalTargetUrlsRanked;
	private int totalRankedUrlsHaveTargetUrlId;
	private int totalRankedUrlsDoNotHaveTargetUrlId;
	private int totalTargetUrlsAssociatedBefore;
	private int totalTargetUrlsAlreadyType1;
	private int totalTargetUrlsUpdatedWithoutUrl;
	private int totalTargetUrlsUpdatedWithUrl;
	private int totalTargetUrlsUpdatedTotal;
	private int totalTargetUrlErrors;
	private String exceptionMessage;

	public int getDomainId() {
		return domainId;
	}

	public void setDomainId(int domainId) {
		this.domainId = domainId;
	}

	public String getDomainName() {
		return domainName;
	}

	public void setDomainName(String domainName) {
		this.domainName = domainName;
	}

	public int getTotalTargetUrlsRanked() {
		return totalTargetUrlsRanked;
	}

	public void setTotalTargetUrlsRanked(int totalTargetUrlsRanked) {
		this.totalTargetUrlsRanked = totalTargetUrlsRanked;
	}

	public int getTotalTargetUrlsAssociatedBefore() {
		return totalTargetUrlsAssociatedBefore;
	}

	public void setTotalTargetUrlsAssociatedBefore(int totalTargetUrlsAssociatedBefore) {
		this.totalTargetUrlsAssociatedBefore = totalTargetUrlsAssociatedBefore;
	}

	public int getTotalTargetUrlsAlreadyType1() {
		return totalTargetUrlsAlreadyType1;
	}

	public void setTotalTargetUrlsAlreadyType1(int totalTargetUrlsAlreadyType1) {
		this.totalTargetUrlsAlreadyType1 = totalTargetUrlsAlreadyType1;
	}

	public int getTotalTargetUrlsUpdatedWithoutUrl() {
		return totalTargetUrlsUpdatedWithoutUrl;
	}

	public void setTotalTargetUrlsUpdatedWithoutUrl(int totalTargetUrlsUpdatedWithoutUrl) {
		this.totalTargetUrlsUpdatedWithoutUrl = totalTargetUrlsUpdatedWithoutUrl;
	}

	public int getTotalTargetUrlsUpdatedWithUrl() {
		return totalTargetUrlsUpdatedWithUrl;
	}

	public void setTotalTargetUrlsUpdatedWithUrl(int totalTargetUrlsUpdatedWithUrl) {
		this.totalTargetUrlsUpdatedWithUrl = totalTargetUrlsUpdatedWithUrl;
	}

	public int getTotalTargetUrlsUpdatedTotal() {
		return totalTargetUrlsUpdatedTotal;
	}

	public void setTotalTargetUrlsUpdatedTotal(int totalTargetUrlsUpdatedTotal) {
		this.totalTargetUrlsUpdatedTotal = totalTargetUrlsUpdatedTotal;
	}

	public int getTotalTargetUrlErrors() {
		return totalTargetUrlErrors;
	}

	public void setTotalTargetUrlErrors(int totalTargetUrlErrors) {
		this.totalTargetUrlErrors = totalTargetUrlErrors;
	}

	public String getExceptionMessage() {
		return exceptionMessage;
	}

	public void setExceptionMessage(String exceptionMessage) {
		this.exceptionMessage = exceptionMessage;
	}

	public int getTotalRankedUrlsDoNotHaveTargetUrlId() {
		return totalRankedUrlsDoNotHaveTargetUrlId;
	}

	public void setTotalRankedUrlsDoNotHaveTargetUrlId(int totalRankedUrlsDoNotHaveTargetUrlId) {
		this.totalRankedUrlsDoNotHaveTargetUrlId = totalRankedUrlsDoNotHaveTargetUrlId;
	}

	public int getTotalRankedUrlsHaveTargetUrlId() {
		return totalRankedUrlsHaveTargetUrlId;
	}

	public void setTotalRankedUrlsHaveTargetUrlId(int totalRankedUrlsHaveTargetUrlId) {
		this.totalRankedUrlsHaveTargetUrlId = totalRankedUrlsHaveTargetUrlId;
	}

	@Override
	public String toString() {
		return "AssociateTopRankedTargetUrlSummaryValueObject [domainId=" + domainId + ", domainName=" + domainName + ", totalTargetUrlsRanked=" + totalTargetUrlsRanked
				+ ", totalTargetUrlsAssociatedBefore=" + totalTargetUrlsAssociatedBefore + ", totalTargetUrlsAlreadyType1=" + totalTargetUrlsAlreadyType1
				+ ", totalTargetUrlsUpdatedWithoutUrl=" + totalTargetUrlsUpdatedWithoutUrl + ", totalTargetUrlsUpdatedWithUrl=" + totalTargetUrlsUpdatedWithUrl
				+ ", totalTargetUrlsUpdatedTotal=" + totalTargetUrlsUpdatedTotal + ", totalTargetUrlErrors=" + totalTargetUrlErrors + ", exceptionMessage="
				+ exceptionMessage + ", totalRankedUrlsDoNotHaveTargetUrlId=" + totalRankedUrlsDoNotHaveTargetUrlId + ", totalRankedUrlsHaveTargetUrlId="
				+ totalRankedUrlsHaveTargetUrlId + "]";
	}

}
