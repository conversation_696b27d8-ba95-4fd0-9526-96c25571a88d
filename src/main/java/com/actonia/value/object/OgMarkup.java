package com.actonia.value.object;

public class OgMarkup {
	private Integer index;
	private String property;
	private String content;

	public Integer getIndex() {
		return index;
	}

	public void setIndex(Integer index) {
		this.index = index;
	}

	public String getProperty() {
		return property;
	}

	public void setProperty(String property) {
		this.property = property;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	@Override
	public String toString() {
		return "OgMarkup [index=" + index + ", property=" + property + ", content=" + content + "]";
	}

}
