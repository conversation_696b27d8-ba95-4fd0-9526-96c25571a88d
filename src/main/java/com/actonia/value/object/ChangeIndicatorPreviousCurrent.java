package com.actonia.value.object;

public class ChangeIndicatorPreviousCurrent {
	private String changeIndicatorDesc;
	private String severity;
	private String previous;
	private String current;
	private String url;
	private String currentCrawlTimestamp;

	public String getChangeIndicatorDesc() {
		return changeIndicatorDesc;
	}

	public void setChangeIndicatorDesc(String changeIndicatorDesc) {
		this.changeIndicatorDesc = changeIndicatorDesc;
	}

	public String getSeverity() {
		return severity;
	}

	public void setSeverity(String severity) {
		this.severity = severity;
	}

	public String getPrevious() {
		return previous;
	}

	public void setPrevious(String previous) {
		this.previous = previous;
	}

	public String getCurrent() {
		return current;
	}

	public void setCurrent(String current) {
		this.current = current;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	@Override
	public String toString() {
		return "ChangeIndicatorPreviousCurrent [changeIndicatorDesc=" + changeIndicatorDesc + ", severity=" + severity + ", previous=" + previous + ", current="
				+ current + ", url=" + url + ", currentCrawlTimestamp=" + currentCrawlTimestamp + "]";
	}

	public void setCurrentCrawlTimestamp(String currentCrawlTimestamp) {
		this.currentCrawlTimestamp = currentCrawlTimestamp;
	}

	public String getCurrentCrawlTimestamp() {
		return currentCrawlTimestamp;
	}
}
