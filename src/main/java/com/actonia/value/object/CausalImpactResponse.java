package com.actonia.value.object;

import java.util.Arrays;
import java.util.Map;

public class CausalImpactResponse {
	private Boolean success;
	private WebServiceError error;
	private Map<String, Double> control_name_corr_coef_map; // correlation coefficient by control name
	private String pre_period_start_date;
	private String pre_period_end_date;
	private String post_period_start_date;
	private String post_period_end_date;
	private String summary;
	private PosteriorInference posterior_inference_average;
	private PosteriorInference posterior_inference_cumulative;
	private double[] test_time_series;
	private double[] counter_factual_prediction_time_series;
	private double[] counter_factual_prediction_time_series_lower;
	private double[] counter_factual_prediction_time_series_upper;
	private double[] pointwise_causal_effect_time_series; // difference between observed data (ie. 'test_time_series' in request) and counter factual prediction
	private double[] pointwise_causal_effect_time_series_lower;
	private double[] pointwise_causal_effect_time_series_upper;
	private double[] cumulative_effect_time_series;
	private double[] cumulative_effect_time_series_lower;
	private double[] cumulative_effect_time_series_upper;

	public Boolean getSuccess() {
		return success;
	}

	public void setSuccess(Boolean success) {
		this.success = success;
	}

	public WebServiceError getError() {
		return error;
	}

	public void setError(WebServiceError error) {
		this.error = error;
	}

	public Map<String, Double> getControl_name_corr_coef_map() {
		return control_name_corr_coef_map;
	}

	public void setControl_name_corr_coef_map(Map<String, Double> control_name_corr_coef_map) {
		this.control_name_corr_coef_map = control_name_corr_coef_map;
	}

	public String getPre_period_start_date() {
		return pre_period_start_date;
	}

	public void setPre_period_start_date(String pre_period_start_date) {
		this.pre_period_start_date = pre_period_start_date;
	}

	public String getPre_period_end_date() {
		return pre_period_end_date;
	}

	public void setPre_period_end_date(String pre_period_end_date) {
		this.pre_period_end_date = pre_period_end_date;
	}

	public String getPost_period_start_date() {
		return post_period_start_date;
	}

	public void setPost_period_start_date(String post_period_start_date) {
		this.post_period_start_date = post_period_start_date;
	}

	public String getPost_period_end_date() {
		return post_period_end_date;
	}

	public void setPost_period_end_date(String post_period_end_date) {
		this.post_period_end_date = post_period_end_date;
	}

	public String getSummary() {
		return summary;
	}

	public void setSummary(String summary) {
		this.summary = summary;
	}

	public PosteriorInference getPosterior_inference_average() {
		return posterior_inference_average;
	}

	public void setPosterior_inference_average(PosteriorInference posterior_inference_average) {
		this.posterior_inference_average = posterior_inference_average;
	}

	public PosteriorInference getPosterior_inference_cumulative() {
		return posterior_inference_cumulative;
	}

	public void setPosterior_inference_cumulative(PosteriorInference posterior_inference_cumulative) {
		this.posterior_inference_cumulative = posterior_inference_cumulative;
	}

	public double[] getTest_time_series() {
		return test_time_series;
	}

	public void setTest_time_series(double[] test_time_series) {
		this.test_time_series = test_time_series;
	}

	public double[] getCounter_factual_prediction_time_series() {
		return counter_factual_prediction_time_series;
	}

	public void setCounter_factual_prediction_time_series(double[] counter_factual_prediction_time_series) {
		this.counter_factual_prediction_time_series = counter_factual_prediction_time_series;
	}

	public double[] getCounter_factual_prediction_time_series_lower() {
		return counter_factual_prediction_time_series_lower;
	}

	public void setCounter_factual_prediction_time_series_lower(double[] counter_factual_prediction_time_series_lower) {
		this.counter_factual_prediction_time_series_lower = counter_factual_prediction_time_series_lower;
	}

	public double[] getCounter_factual_prediction_time_series_upper() {
		return counter_factual_prediction_time_series_upper;
	}

	public void setCounter_factual_prediction_time_series_upper(double[] counter_factual_prediction_time_series_upper) {
		this.counter_factual_prediction_time_series_upper = counter_factual_prediction_time_series_upper;
	}

	public double[] getPointwise_causal_effect_time_series() {
		return pointwise_causal_effect_time_series;
	}

	public void setPointwise_causal_effect_time_series(double[] pointwise_causal_effect_time_series) {
		this.pointwise_causal_effect_time_series = pointwise_causal_effect_time_series;
	}

	public double[] getPointwise_causal_effect_time_series_lower() {
		return pointwise_causal_effect_time_series_lower;
	}

	public void setPointwise_causal_effect_time_series_lower(double[] pointwise_causal_effect_time_series_lower) {
		this.pointwise_causal_effect_time_series_lower = pointwise_causal_effect_time_series_lower;
	}

	public double[] getPointwise_causal_effect_time_series_upper() {
		return pointwise_causal_effect_time_series_upper;
	}

	public void setPointwise_causal_effect_time_series_upper(double[] pointwise_causal_effect_time_series_upper) {
		this.pointwise_causal_effect_time_series_upper = pointwise_causal_effect_time_series_upper;
	}

	public double[] getCumulative_effect_time_series() {
		return cumulative_effect_time_series;
	}

	public void setCumulative_effect_time_series(double[] cumulative_effect_time_series) {
		this.cumulative_effect_time_series = cumulative_effect_time_series;
	}

	public double[] getCumulative_effect_time_series_lower() {
		return cumulative_effect_time_series_lower;
	}

	public void setCumulative_effect_time_series_lower(double[] cumulative_effect_time_series_lower) {
		this.cumulative_effect_time_series_lower = cumulative_effect_time_series_lower;
	}

	public double[] getCumulative_effect_time_series_upper() {
		return cumulative_effect_time_series_upper;
	}

	public void setCumulative_effect_time_series_upper(double[] cumulative_effect_time_series_upper) {
		this.cumulative_effect_time_series_upper = cumulative_effect_time_series_upper;
	}

	@Override
	public String toString() {
		return "CausalImpactResponse [success=" + success + ", error=" + error + ", control_name_corr_coef_map=" + control_name_corr_coef_map
				+ ", pre_period_start_date=" + pre_period_start_date + ", pre_period_end_date=" + pre_period_end_date + ", post_period_start_date="
				+ post_period_start_date + ", post_period_end_date=" + post_period_end_date + ", summary=" + summary + ", posterior_inference_average="
				+ posterior_inference_average + ", posterior_inference_cumulative=" + posterior_inference_cumulative + ", test_time_series="
				+ Arrays.toString(test_time_series) + ", counter_factual_prediction_time_series=" + Arrays.toString(counter_factual_prediction_time_series)
				+ ", counter_factual_prediction_time_series_lower=" + Arrays.toString(counter_factual_prediction_time_series_lower)
				+ ", counter_factual_prediction_time_series_upper=" + Arrays.toString(counter_factual_prediction_time_series_upper)
				+ ", pointwise_causal_effect_time_series=" + Arrays.toString(pointwise_causal_effect_time_series) + ", pointwise_causal_effect_time_series_lower="
				+ Arrays.toString(pointwise_causal_effect_time_series_lower) + ", pointwise_causal_effect_time_series_upper="
				+ Arrays.toString(pointwise_causal_effect_time_series_upper) + ", cumulative_effect_time_series=" + Arrays.toString(cumulative_effect_time_series)
				+ ", cumulative_effect_time_series_lower=" + Arrays.toString(cumulative_effect_time_series_lower) + ", cumulative_effect_time_series_upper="
				+ Arrays.toString(cumulative_effect_time_series_upper) + "]";
	}

}
