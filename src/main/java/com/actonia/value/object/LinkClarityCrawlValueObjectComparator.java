package com.actonia.value.object;

import java.util.Comparator;

public class LinkClarityCrawlValueObjectComparator implements Comparator<LinkClarityCrawlValueObject> {

	@Override
	public int compare(LinkClarityCrawlValueObject arg0, LinkClarityCrawlValueObject arg1) {

		// sort by:
		// 1) totalUrls (descending order)
		// 2) hostname (ascending order)

		int response = 0;
		if (arg0 != null && arg1 != null) {

			// totalUrls
			if (arg0.getTotalUrls() > arg1.getTotalUrls()) { // when arg0 is greater than arg1
				response = -1;
			} else if (arg0.getTotalUrls() < arg1.getTotalUrls()) { // when arg0 is smaller than arg1
				response = +1;
			}

			// hostname
			if (response == 0) {
				if (arg0.getHostname().compareToIgnoreCase(arg1.getHostname()) > 0) { // when arg0 is greater than arg1
					response = +1;
				} else if (arg0.getHostname().compareToIgnoreCase(arg1.getHostname()) < 0) { // when arg0 is smaller than arg1
					response = -1;
				}
			}
		} else if (arg0 != null && arg1 == null) { // when arg0 is greater than arg1
			response = +1;
		} else if (arg0 == null && arg1 != null) { // when arg0 is smaller than arg1
			response = -1;
		}
		return response;
	}

}
