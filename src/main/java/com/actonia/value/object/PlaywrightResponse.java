package com.actonia.value.object;

public class PlaywrightResponse {
	private String requestedUrl;
	private String userAgent;
	private String responsedUrl;
	private int status;
	private String html;
	private long elapsedInSeconds;
	private String errorMessage;

	public String getRequestedUrl() {
		return requestedUrl;
	}

	public void setRequestedUrl(String requestedUrl) {
		this.requestedUrl = requestedUrl;
	}

	public String getUserAgent() {
		return userAgent;
	}

	public void setUserAgent(String userAgent) {
		this.userAgent = userAgent;
	}

	public String getResponsedUrl() {
		return responsedUrl;
	}

	public void setResponsedUrl(String responsedUrl) {
		this.responsedUrl = responsedUrl;
	}

	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	public String getHtml() {
		return html;
	}

	public void setHtml(String html) {
		this.html = html;
	}

	public long getElapsedInSeconds() {
		return elapsedInSeconds;
	}

	public void setElapsedInSeconds(long elapsedInSeconds) {
		this.elapsedInSeconds = elapsedInSeconds;
	}

	public String getErrorMessage() {
		return errorMessage;
	}

	public void setErrorMessage(String errorMessage) {
		this.errorMessage = errorMessage;
	}

	@Override
	public String toString() {
		return "PlaywrightResponse [requestedUrl=" + requestedUrl + ", userAgent=" + userAgent + ", responsedUrl=" + responsedUrl + ", status=" + status + ", html="
				+ html + ", elapsedInSeconds=" + elapsedInSeconds + ", errorMessage=" + errorMessage + "]";
	}

}
