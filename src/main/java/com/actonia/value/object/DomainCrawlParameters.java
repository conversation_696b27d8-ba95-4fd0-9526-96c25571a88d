package com.actonia.value.object;

import com.actonia.entity.AdditionalContentEntity;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public class DomainCrawlParameters implements Serializable {
    private static final long serialVersionUID = 1L;
    private Integer domainId;
    private String domain;
    private String userAgent;
    private String queueName;
    private String region;
    private Integer messagesPerIteration = 10;
    private Integer delayInSecondsPerHttpRequest;
    private Boolean enableJavascriptCrawl;
    private Integer javascriptTimeoutInSecond = 10;
    private Map<String, String> crawlerRequestHeaders;
    private int maxConcurrentThreads = 1;
    private String trackDate;
    private List<AdditionalContentEntity> additionalContentEntityList;
    private String endpoint;

    public String getTrackDate() {
        return trackDate;
    }

    public void setTrackDate(String trackDate) {
        this.trackDate = trackDate;
    }

    public List<AdditionalContentEntity> getAdditionalContentEntityList() {
        return additionalContentEntityList;
    }

    public void setAdditionalContentEntityList(List<AdditionalContentEntity> additionalContentEntityList) {
        this.additionalContentEntityList = additionalContentEntityList;
    }

    public Integer getDomainId() {
        return domainId;
    }

    public void setDomainId(Integer domainId) {
        this.domainId = domainId;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getQueueName() {
        return queueName;
    }

    public void setQueueName(String queueName) {
        this.queueName = queueName;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public Integer getMessagesPerIteration() {
        return messagesPerIteration;
    }

    public void setMessagesPerIteration(Integer messagesPerIteration) {
        this.messagesPerIteration = messagesPerIteration;
    }

    public Integer getDelayInSecondsPerHttpRequest() {
        return delayInSecondsPerHttpRequest;
    }

    public void setDelayInSecondsPerHttpRequest(Integer delayInSecondsPerHttpRequest) {
        this.delayInSecondsPerHttpRequest = delayInSecondsPerHttpRequest;
    }

    public Boolean getEnableJavascriptCrawl() {
        return enableJavascriptCrawl;
    }

    public void setEnableJavascriptCrawl(Boolean enableJavascriptCrawl) {
        this.enableJavascriptCrawl = enableJavascriptCrawl;
    }

    public Integer getJavascriptTimeoutInSecond() {
        return javascriptTimeoutInSecond;
    }

    public void setJavascriptTimeoutInSecond(Integer javascriptTimeoutInSecond) {
        this.javascriptTimeoutInSecond = javascriptTimeoutInSecond;
    }

    public Map<String, String> getCrawlerRequestHeaders() {
        return crawlerRequestHeaders;
    }

    public void setCrawlerRequestHeaders(Map<String, String> crawlerRequestHeaders) {
        this.crawlerRequestHeaders = crawlerRequestHeaders;
    }

    public int getMaxConcurrentThreads() {
        return maxConcurrentThreads;
    }

    public void setMaxConcurrentThreads(int maxConcurrentThreads) {
        this.maxConcurrentThreads = maxConcurrentThreads;
    }

    public String getEndpoint() {
        return endpoint;
    }

    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }
}
