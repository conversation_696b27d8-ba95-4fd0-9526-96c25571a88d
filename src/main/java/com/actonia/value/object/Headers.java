package com.actonia.value.object;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class Headers {
	@SerializedName("User-Agent")
	@Expose
	private String userAgent;

	public String getUserAgent() {
		return userAgent;
	}

	public void setUserAgent(String userAgent) {
		this.userAgent = userAgent;
	}

	@Override
	public String toString() {
		return "Headers [userAgent=" + userAgent + "]";
	}

}
