package com.actonia.value.object;

import java.util.List;

public class PageTagAlertEmailValueObject {
	private int groupTagId;
	private String groupTagName;
	private int totalTargetUrlsRequireAlert;
	private int totalTargetUrlsInGroupTag;
	private String percentTargetUrlsInGroupTag;
	private List<PageTagContentChangeAlertEmailValueObject> contentChangeAlertEmailList;
	private List<PageTagResponseCodeChangeAlertEmailValueObject> responseCodeAlertEmailList;
	private List<PageTagAdditionalContentChangeAlertEmailValueObject> additionalContentChangeAlertEmailList;

	public int getGroupTagId() {
		return groupTagId;
	}

	public void setGroupTagId(int groupTagId) {
		this.groupTagId = groupTagId;
	}

	public String getGroupTagName() {
		return groupTagName;
	}

	public void setGroupTagName(String groupTagName) {
		this.groupTagName = groupTagName;
	}

	public List<PageTagContentChangeAlertEmailValueObject> getContentChangeAlertEmailList() {
		return contentChangeAlertEmailList;
	}

	public void setContentChangeAlertEmailList(List<PageTagContentChangeAlertEmailValueObject> contentChangeAlertEmailList) {
		this.contentChangeAlertEmailList = contentChangeAlertEmailList;
	}

	public List<PageTagResponseCodeChangeAlertEmailValueObject> getResponseCodeAlertEmailList() {
		return responseCodeAlertEmailList;
	}

	public void setResponseCodeAlertEmailList(List<PageTagResponseCodeChangeAlertEmailValueObject> responseCodeAlertEmailList) {
		this.responseCodeAlertEmailList = responseCodeAlertEmailList;
	}

	public int getTotalTargetUrlsRequireAlert() {
		return totalTargetUrlsRequireAlert;
	}

	public void setTotalTargetUrlsRequireAlert(int totalTargetUrlsRequireAlert) {
		this.totalTargetUrlsRequireAlert = totalTargetUrlsRequireAlert;
	}

	public int getTotalTargetUrlsInGroupTag() {
		return totalTargetUrlsInGroupTag;
	}

	public void setTotalTargetUrlsInGroupTag(int totalTargetUrlsInGroupTag) {
		this.totalTargetUrlsInGroupTag = totalTargetUrlsInGroupTag;
	}

	public String getPercentTargetUrlsInGroupTag() {
		return percentTargetUrlsInGroupTag;
	}

	public void setPercentTargetUrlsInGroupTag(String percentTargetUrlsInGroupTag) {
		this.percentTargetUrlsInGroupTag = percentTargetUrlsInGroupTag;
	}

	public List<PageTagAdditionalContentChangeAlertEmailValueObject> getAdditionalContentChangeAlertEmailList() {
		return additionalContentChangeAlertEmailList;
	}

	public void setAdditionalContentChangeAlertEmailList(List<PageTagAdditionalContentChangeAlertEmailValueObject> additionalContentChangeAlertEmailList) {
		this.additionalContentChangeAlertEmailList = additionalContentChangeAlertEmailList;
	}

	@Override
	public String toString() {
		return "PageTagAlertEmailValueObject [groupTagId=" + groupTagId + ", groupTagName=" + groupTagName + ", totalTargetUrlsRequireAlert="
				+ totalTargetUrlsRequireAlert + ", totalTargetUrlsInGroupTag=" + totalTargetUrlsInGroupTag + ", percentTargetUrlsInGroupTag="
				+ percentTargetUrlsInGroupTag + ", contentChangeAlertEmailList=" + contentChangeAlertEmailList + ", responseCodeAlertEmailList="
				+ responseCodeAlertEmailList + ", additionalContentChangeAlertEmailList=" + additionalContentChangeAlertEmailList + "]";
	}

}
