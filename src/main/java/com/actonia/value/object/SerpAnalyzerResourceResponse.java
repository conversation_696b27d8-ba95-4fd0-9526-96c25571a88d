package com.actonia.value.object;

public class SerpAnalyzerResourceResponse {
	private Boolean success;
	private WebServiceError error;
	private String json;

	public Boolean getSuccess() {
		return success;
	}

	public void setSuccess(Boolean success) {
		this.success = success;
	}

	public WebServiceError getError() {
		return error;
	}

	public void setError(WebServiceError error) {
		this.error = error;
	}

	public String getJson() {
		return json;
	}

	public void setJson(String json) {
		this.json = json;
	}

	@Override
	public String toString() {
		return "SerpAnalyzerResourceResponse [success=" + success + ", error=" + error + ", json=" + json + "]";
	}

}
