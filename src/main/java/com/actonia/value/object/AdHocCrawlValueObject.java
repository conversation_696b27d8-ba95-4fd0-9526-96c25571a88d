package com.actonia.value.object;

public class AdHocCrawlValueObject {
	private String urlString;
	private String responseCode;
	private String title;
	private String existsInClarityDB;

	public String getUrlString() {
		return urlString;
	}

	public void setUrlString(String urlString) {
		this.urlString = urlString;
	}

	public String getResponseCode() {
		return responseCode;
	}

	public void setResponseCode(String responseCode) {
		this.responseCode = responseCode;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getExistsInClarityDB() {
		return existsInClarityDB;
	}

	public void setExistsInClarityDB(String existsInClarityDB) {
		this.existsInClarityDB = existsInClarityDB;
	}

	@Override
	public String toString() {
		return "AdHocCrawlValueObject [urlString=" + urlString + ", responseCode=" + responseCode + ", title=" + title + ", existsInClarityDB=" + existsInClarityDB
				+ "]";
	}

}
