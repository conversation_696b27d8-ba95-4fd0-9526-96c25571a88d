package com.actonia.value.object;

import java.util.List;

public class ContentGuardUrlChanges {
	private String url;
	private String previous_crawl_timestamp;
	private String current_crawl_timestamp;
	private Integer total_changes;
	private Integer added;
	private Integer modified;
	private Integer removed;
	private List<ContentGuardChangeDetails> change_details_list;
	private String hash_cd;

	// https://www.wrike.com/open.htm?id=780167179
	private String response_code;
	private String last_update_timestamp;

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getPrevious_crawl_timestamp() {
		return previous_crawl_timestamp;
	}

	public void setPrevious_crawl_timestamp(String previous_crawl_timestamp) {
		this.previous_crawl_timestamp = previous_crawl_timestamp;
	}

	public String getCurrent_crawl_timestamp() {
		return current_crawl_timestamp;
	}

	public void setCurrent_crawl_timestamp(String current_crawl_timestamp) {
		this.current_crawl_timestamp = current_crawl_timestamp;
	}

	public Integer getTotal_changes() {
		return total_changes;
	}

	public void setTotal_changes(Integer total_changes) {
		this.total_changes = total_changes;
	}

	public Integer getAdded() {
		return added;
	}

	public void setAdded(Integer added) {
		this.added = added;
	}

	public Integer getModified() {
		return modified;
	}

	public void setModified(Integer modified) {
		this.modified = modified;
	}

	public Integer getRemoved() {
		return removed;
	}

	public void setRemoved(Integer removed) {
		this.removed = removed;
	}

	public List<ContentGuardChangeDetails> getChange_details_list() {
		return change_details_list;
	}

	public void setChange_details_list(List<ContentGuardChangeDetails> change_details_list) {
		this.change_details_list = change_details_list;
	}

	public String getHash_cd() {
		return hash_cd;
	}

	public void setHash_cd(String hash_cd) {
		this.hash_cd = hash_cd;
	}

	public String getResponse_code() {
		return response_code;
	}

	public void setResponse_code(String response_code) {
		this.response_code = response_code;
	}

	public String getLast_update_timestamp() {
		return last_update_timestamp;
	}

	public void setLast_update_timestamp(String last_update_timestamp) {
		this.last_update_timestamp = last_update_timestamp;
	}

	@Override
	public String toString() {
		return "ContentGuardUrlChanges [url=" + url + ", previous_crawl_timestamp=" + previous_crawl_timestamp + ", current_crawl_timestamp=" + current_crawl_timestamp
				+ ", total_changes=" + total_changes + ", added=" + added + ", modified=" + modified + ", removed=" + removed + ", change_details_list="
				+ change_details_list + ", hash_cd=" + hash_cd + ", response_code=" + response_code + ", last_update_timestamp=" + last_update_timestamp + "]";
	}

}
