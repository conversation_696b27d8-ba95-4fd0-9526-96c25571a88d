package com.actonia.value.object;

public class UrlFilter {
	private String action; // ct=contains, nct=does not contain, ew=ends with, eq=is, neq=is not, pt=regex, npt=regex not matched, sw=starts with 
	private String value;

	public String getAction() {
		return action;
	}

	public void setAction(String action) {
		this.action = action;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	@Override
	public String toString() {
		return "UrlFilter [action=" + action + ", value=" + value + "]";
	}

}
