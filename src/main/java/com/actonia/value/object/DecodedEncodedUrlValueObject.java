package com.actonia.value.object;

public class DecodedEncodedUrlValueObject {
	private String originalUrlString;
	private String decodedUrlString;
	private String encodedUrlString;
	private boolean errorIndicator;

	public String getOriginalUrlString() {
		return originalUrlString;
	}

	public void setOriginalUrlString(String originalUrlString) {
		this.originalUrlString = originalUrlString;
	}

	public String getDecodedUrlString() {
		return decodedUrlString;
	}

	public void setDecodedUrlString(String decodedUrlString) {
		this.decodedUrlString = decodedUrlString;
	}

	public String getEncodedUrlString() {
		return encodedUrlString;
	}

	public void setEncodedUrlString(String encodedUrlString) {
		this.encodedUrlString = encodedUrlString;
	}

	public boolean getErrorIndicator() {
		return errorIndicator;
	}

	public void setErrorIndicator(boolean errorIndicator) {
		this.errorIndicator = errorIndicator;
	}

	@Override
	public String toString() {
		return "DecodedEncodedUrlValueObject [originalUrlString=" + originalUrlString + ", decodedUrlString=" + decodedUrlString + ", encodedUrlString="
				+ encodedUrlString + ", errorIndicator=" + errorIndicator + "]";
	}

}
