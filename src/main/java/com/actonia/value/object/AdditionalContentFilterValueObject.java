package com.actonia.value.object;

import java.util.List;

public class AdditionalContentFilterValueObject {
	private int domainId;
	private String pattern;
	private String attribute;
	private String displayName;
	private String xpath;
	private List<String> valueList;

	public int getDomainId() {
		return domainId;
	}

	public void setDomainId(int domainId) {
		this.domainId = domainId;
	}

	public String getPattern() {
		return pattern;
	}

	public void setPattern(String pattern) {
		this.pattern = pattern;
	}

	public String getAttribute() {
		return attribute;
	}

	public void setAttribute(String attribute) {
		this.attribute = attribute;
	}

	public String getDisplayName() {
		return displayName;
	}

	public void setDisplayName(String displayName) {
		this.displayName = displayName;
	}

	public List<String> getValueList() {
		return valueList;
	}

	public void setValueList(List<String> valueList) {
		this.valueList = valueList;
	}

	public String getXpath() {
		return xpath;
	}

	public void setXpath(String xpath) {
		this.xpath = xpath;
	}

	@Override
	public String toString() {
		return "AdditionalContentFilterValueObject [domainId=" + domainId + ", pattern=" + pattern + ", attribute=" + attribute + ", displayName=" + displayName
				+ ", xpath=" + xpath + ", valueList=" + valueList + "]";
	}
	
}
