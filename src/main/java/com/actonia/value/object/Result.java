package com.actonia.value.object;

import java.util.Arrays;

public class Result {
	private UserAgents userAgents;
	private AllUserAgents[] allUserAgents;
	private Boolean isAllBotsBlocked;
	private Boolean isAllPagesBlocked;

	public UserAgents getUserAgents() {
		return userAgents;
	}

	public void setUserAgents(UserAgents userAgents) {
		this.userAgents = userAgents;
	}

	public AllUserAgents[] getAllUserAgents() {
		return allUserAgents;
	}

	public void setAllUserAgents(AllUserAgents[] allUserAgents) {
		this.allUserAgents = allUserAgents;
	}

	public Boolean getIsAllBotsBlocked() {
		return isAllBotsBlocked;
	}

	public void setIsAllBotsBlocked(Boolean isAllBotsBlocked) {
		this.isAllBotsBlocked = isAllBotsBlocked;
	}

	public Boolean getIsAllPagesBlocked() {
		return isAllPagesBlocked;
	}

	public void setIsAllPagesBlocked(Boolean isAllPagesBlocked) {
		this.isAllPagesBlocked = isAllPagesBlocked;
	}

	@Override
	public String toString() {
		return "Result [userAgents=" + userAgents 
				+ ", allUserAgents=" + Arrays.toString(allUserAgents) 
				+ ", isAllBotsBlocked=" + isAllBotsBlocked
				+ ", isAllPagesBlocked=" + isAllPagesBlocked + "]";
	}

}
