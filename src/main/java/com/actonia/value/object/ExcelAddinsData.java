package com.actonia.value.object;

public class ExcelAddinsData {

	// crawl data
	private Integer domain_id;
	private String project_name;
	private String crawl_request_date;
	private Integer crawl_request_id;
	private String type;
	private String description;
	private String importance;
	private Integer count;

	// search engine
	private Integer engine_id;
	private Integer language_id;
	private String device;
	private String engine_name;

	// domain_name
	private String domain_name;

	public Integer getDomain_id() {
		return domain_id;
	}

	public void setDomain_id(Integer domain_id) {
		this.domain_id = domain_id;
	}

	public String getProject_name() {
		return project_name;
	}

	public void setProject_name(String project_name) {
		this.project_name = project_name;
	}

	public String getCrawl_request_date() {
		return crawl_request_date;
	}

	public void setCrawl_request_date(String crawl_request_date) {
		this.crawl_request_date = crawl_request_date;
	}

	public Integer getCrawl_request_id() {
		return crawl_request_id;
	}

	public void setCrawl_request_id(Integer crawl_request_id) {
		this.crawl_request_id = crawl_request_id;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getImportance() {
		return importance;
	}

	public void setImportance(String importance) {
		this.importance = importance;
	}

	public Integer getCount() {
		return count;
	}

	public void setCount(Integer count) {
		this.count = count;
	}

	public Integer getEngine_id() {
		return engine_id;
	}

	public void setEngine_id(Integer engine_id) {
		this.engine_id = engine_id;
	}

	public Integer getLanguage_id() {
		return language_id;
	}

	public void setLanguage_id(Integer language_id) {
		this.language_id = language_id;
	}

	public String getDevice() {
		return device;
	}

	public void setDevice(String device) {
		this.device = device;
	}

	public String getEngine_name() {
		return engine_name;
	}

	public void setEngine_name(String engine_name) {
		this.engine_name = engine_name;
	}

	public String getDomain_name() {
		return domain_name;
	}

	public void setDomain_name(String domain_name) {
		this.domain_name = domain_name;
	}

	@Override
	public String toString() {
		return "ExcelAddinsData [domain_id=" + domain_id + ", project_name=" + project_name + ", crawl_request_date=" + crawl_request_date + ", crawl_request_id="
				+ crawl_request_id + ", type=" + type + ", description=" + description + ", importance=" + importance + ", count=" + count + ", engine_id=" + engine_id
				+ ", language_id=" + language_id + ", device=" + device + ", engine_name=" + engine_name + ", domain_name=" + domain_name + "]";
	}

}
