package com.actonia.value.object;

import java.util.Date;

public class PageClarityUpdateSummaryValueObject {
	private int domainId;
	private String domainName;
	private int totalTargetUrls;
	private Date lastUpdateTimestamp;

	public int getDomainId() {
		return domainId;
	}

	public void setDomainId(int domainId) {
		this.domainId = domainId;
	}

	public String getDomainName() {
		return domainName;
	}

	public void setDomainName(String domainName) {
		this.domainName = domainName;
	}

	public int getTotalTargetUrls() {
		return totalTargetUrls;
	}

	public void setTotalTargetUrls(int totalTargetUrls) {
		this.totalTargetUrls = totalTargetUrls;
	}

	public Date getLastUpdateTimestamp() {
		return lastUpdateTimestamp;
	}

	public void setLastUpdateTimestamp(Date lastUpdateTimestamp) {
		this.lastUpdateTimestamp = lastUpdateTimestamp;
	}

	@Override
	public String toString() {
		return "PageClarityUpdateSummaryValueObject [domainId=" + domainId + ", domainName=" + domainName + ", totalTargetUrls=" + totalTargetUrls
				+ ", lastUpdateTimestamp=" + lastUpdateTimestamp + "]";
	}

}
