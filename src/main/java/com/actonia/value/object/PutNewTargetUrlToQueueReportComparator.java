package com.actonia.value.object;

import java.util.Comparator;

public class PutNewTargetUrlToQueueReportComparator implements Comparator<PutNewTargetUrlToQueueReportValueObject> {

	@Override
	public int compare(PutNewTargetUrlToQueueReportValueObject arg0, PutNewTargetUrlToQueueReportValueObject arg1) {

		// sort by:
		// 1) crawlQueueLastUpdateDate (ascending order)
		// 2) totalUrlsWithoutData (descending order)

		int response = 0;
		if (arg0 != null && arg1 != null) {

			// crawlQueueLastUpdateDate
			if (arg0.getCrawlQueueLastUpdateDate() > arg1.getCrawlQueueLastUpdateDate()) { // when arg0 is greater than arg1
				response = +1;
			} else if (arg0.getCrawlQueueLastUpdateDate() < arg1.getCrawlQueueLastUpdateDate()) { // when arg0 is smaller than arg1
				response = -1;
			}
			
			if (response == 0) {
				// totalUrlsWithoutData
				if (arg0.getTotalUrlsWithoutData() > arg1.getTotalUrlsWithoutData()) { // when arg0 is greater than arg1
					response = -1;
				} else if (arg0.getTotalUrlsWithoutData() < arg1.getTotalUrlsWithoutData()) { // when arg0 is smaller than arg1
					response = +1;
				}
			}
		} else if (arg0 != null && arg1 == null) { // when arg0 is greater than arg1
			response = +1;
		} else if (arg0 == null && arg1 != null) { // when arg0 is smaller than arg1
			response = -1;
		}
		return response;
	}

}
