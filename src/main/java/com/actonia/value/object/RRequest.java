package com.actonia.value.object;

public class RRequest {
	private int rPackage;
	private CausalImpactRequest causalImpactRequest;
	private String command;
	private ProphetRequest prophetRequest;
	private MarketMatchingRequest marketMatchingRequest;

	public int getrPackage() {
		return rPackage;
	}

	public void setrPackage(int rPackage) {
		this.rPackage = rPackage;
	}

	public CausalImpactRequest getCausalImpactRequest() {
		return causalImpactRequest;
	}

	public void setCausalImpactRequest(CausalImpactRequest causalImpactRequest) {
		this.causalImpactRequest = causalImpactRequest;
	}

	public String getCommand() {
		return command;
	}

	public void setCommand(String command) {
		this.command = command;
	}

	public ProphetRequest getProphetRequest() {
		return prophetRequest;
	}

	public void setProphetRequest(ProphetRequest prophetRequest) {
		this.prophetRequest = prophetRequest;
	}

	public MarketMatchingRequest getMarketMatchingRequest() {
		return marketMatchingRequest;
	}

	public void setMarketMatchingRequest(MarketMatchingRequest marketMatchingRequest) {
		this.marketMatchingRequest = marketMatchingRequest;
	}

	@Override
	public String toString() {
		return "RRequest [rPackage=" + rPackage + ", causalImpactRequest=" + causalImpactRequest + ", command=" + command + ", prophetRequest=" + prophetRequest
				+ ", marketMatchingRequest=" + marketMatchingRequest + "]";
	}

}
