package com.actonia.value.object;

import java.util.List;

public class TargetUrlChangeDetail {
	private String url;
	private String current_crawl_timestamp;
	private String previous_crawl_timestamp;
	private List<TargetUrlChangeIndicatorDetail> change_indicator_list;

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getCurrent_crawl_timestamp() {
		return current_crawl_timestamp;
	}

	public void setCurrent_crawl_timestamp(String current_crawl_timestamp) {
		this.current_crawl_timestamp = current_crawl_timestamp;
	}

	public String getPrevious_crawl_timestamp() {
		return previous_crawl_timestamp;
	}

	public void setPrevious_crawl_timestamp(String previous_crawl_timestamp) {
		this.previous_crawl_timestamp = previous_crawl_timestamp;
	}

	public List<TargetUrlChangeIndicatorDetail> getChange_indicator_list() {
		return change_indicator_list;
	}

	public void setChange_indicator_list(List<TargetUrlChangeIndicatorDetail> change_indicator_list) {
		this.change_indicator_list = change_indicator_list;
	}

	@Override
	public String toString() {
		return "TargetUrlChangeDetail [url=" + url + ", current_crawl_timestamp=" + current_crawl_timestamp + ", previous_crawl_timestamp=" + previous_crawl_timestamp
				+ ", change_indicator_list=" + change_indicator_list + "]";
	}

}
