package com.actonia.value.object;

public class PuppeteerRequest {
	private PuppeteerHeaders headers;
	private int pageLoadTimeout;
	private String url;

	public PuppeteerHeaders getHeaders() {
		return headers;
	}

	public void setHeaders(PuppeteerHeaders headers) {
		this.headers = headers;
	}

	public int getPageLoadTimeout() {
		return pageLoadTimeout;
	}

	public void setPageLoadTimeout(int pageLoadTimeout) {
		this.pageLoadTimeout = pageLoadTimeout;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	@Override
	public String toString() {
		return "PuppeteerRequest [headers=" + headers + ", pageLoadTimeout=" + pageLoadTimeout + ", url=" + url + "]";
	}

}
