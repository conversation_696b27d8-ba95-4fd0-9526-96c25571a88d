package com.actonia.value.object;

import java.util.Map;

import com.actonia.utils.FormatUtils;

public class ScrapyCrawlerResponse {
	private String url;
	private String html;
	private int status; // HTTP status code of the URL
	//private Map<String, String[]> response_headers;
	private String exceptionMessage;
	private CrawlerResponse crawlerResponse;

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getHtml() {
		return html;
	}

	public void setHtml(String html) {
		this.html = html;
	}

	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	//	public Map<String, String[]> getResponse_headers() {
	//		return response_headers;
	//	}
	//
	//	public void setResponse_headers(Map<String, String[]> response_headers) {
	//		this.response_headers = response_headers;
	//	}

	public String getExceptionMessage() {
		return exceptionMessage;
	}

	public void setExceptionMessage(String exceptionMessage) {
		this.exceptionMessage = exceptionMessage;
	}

	public void debugResponseHeaders(Map<String, String[]> response_headers) {
		String[] mapValues = null;
		if (response_headers != null && response_headers.size() > 0) {
			for (String mapKey : response_headers.keySet()) {
				mapValues = response_headers.get(mapKey);
				for (String mapValue : mapValues) {
					FormatUtils.getInstance().logMemoryUsage("debugResponseHeaders() mapKey=" + mapKey + ",mapValue=" + mapValue);
				}
			}
		}
	}

	public CrawlerResponse getCrawlerResponse() {
		return crawlerResponse;
	}

	public void setCrawlerResponse(CrawlerResponse crawlerResponse) {
		this.crawlerResponse = crawlerResponse;
	}

	@Override
	public String toString() {
		return "ScrapyCrawlerResponse [url=" + url + ", html=" + html + ", status=" + status
		//+ ", response_headers=" + response_headers 
				+ ", exceptionMessage=" + exceptionMessage + ", crawlerResponse=" + crawlerResponse + "]";
	}
}
