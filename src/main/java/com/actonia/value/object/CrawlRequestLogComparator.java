package com.actonia.value.object;

import java.util.Comparator;

import com.actonia.entity.CrawlRequestLog;

public class CrawlRequestLogComparator implements Comparator<CrawlRequestLog> {

	@Override
	public int compare(CrawlRequestLog arg0, CrawlRequestLog arg1) {

		// sort by:
		// 1) crawl_request_date (ascending order)

		int response = 0;
		if (arg0 != null && arg1 != null) {
			// crawl_request_date
			if (arg0.getCrawlRequestDate() != null && arg1.getCrawlRequestDate() == null) { // when arg0 is greater than arg1
				response = +1;
			} else if (arg0.getCrawlRequestDate() == null && arg1.getCrawlRequestDate() != null) { // when arg0 is smaller than arg1
				response = -1;
			} else if (arg0.getCrawlRequestDate().intValue() > arg1.getCrawlRequestDate().intValue()) { // when arg0 is greater than arg1
				response = +1;
			} else if (arg0.getCrawlRequestDate().intValue() < arg1.getCrawlRequestDate().intValue()) { // when arg0 is smaller than arg1
				response = -1;
			}
		} else if (arg0 != null && arg1 == null) { // when arg0 is greater than arg1
			response = +1;
		} else if (arg0 == null && arg1 != null) { // when arg0 is smaller than arg1
			response = -1;
		}
		return response;
	}

}
