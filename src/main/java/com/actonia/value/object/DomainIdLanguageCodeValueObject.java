package com.actonia.value.object;

import java.util.Arrays;

public class DomainIdLanguageCodeValueObject {
	private Integer[] domainIds;
	private String[] languageCodes;

	public Integer[] getDomainIds() {
		return domainIds;
	}

	public void setDomainIds(Integer[] domainIds) {
		this.domainIds = domainIds;
	}

	public String[] getLanguageCodes() {
		return languageCodes;
	}

	public void setLanguageCodes(String[] languageCodes) {
		this.languageCodes = languageCodes;
	}

	@Override
	public String toString() {
		return "DomainIdLanguageCodeValueObject [domainIds=" + Arrays.toString(domainIds) + ", languageCodes=" + Arrays.toString(languageCodes) + "]";
	}

}
