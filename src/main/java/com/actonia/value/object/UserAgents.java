package com.actonia.value.object;

public class UserAgents {
	private String baiduspider;
	private String bingbot;
	private String googlebot;
	private String yahoo;

	public String getBaiduspider() {
		return baiduspider;
	}

	public void setBaiduspider(String baiduspider) {
		this.baiduspider = baiduspider;
	}

	public String getBingbot() {
		return bingbot;
	}

	public void setBingbot(String bingbot) {
		this.bingbot = bingbot;
	}

	public String getGooglebot() {
		return googlebot;
	}

	public void setGooglebot(String googlebot) {
		this.googlebot = googlebot;
	}

	public String getYahoo() {
		return yahoo;
	}

	public void setYahoo(String yahoo) {
		this.yahoo = yahoo;
	}

	@Override
	public String toString() {
		return "UserAgents [baiduspider=" + baiduspider + ", bingbot=" + bingbot + ", googlebot=" + googlebot + ", yahoo=" + yahoo + "]";
	}

}
