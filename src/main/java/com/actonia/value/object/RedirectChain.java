package com.actonia.value.object;

public class RedirectChain {
	private Integer index;
	private String redirected_url;
	private String url;
	private float response_code;

	public Integer getIndex() {
		return index;
	}

	public void setIndex(Integer index) {
		this.index = index;
	}

	public String getRedirected_url() {
		return redirected_url;
	}

	public void setRedirected_url(String redirected_url) {
		this.redirected_url = redirected_url;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public float getResponse_code() {
		return response_code;
	}

	public void setResponse_code(float response_code) {
		this.response_code = response_code;
	}

	@Override
	public String toString() {
		return "RedirectChain [index=" + index + ", redirected_url=" + redirected_url + ", url=" + url + ", response_code=" + response_code + "]";
	}

}
