package com.actonia.value.object;

// this value object maps to the output of an API service
public class SharedCountsValueObject {
	private Integer stumbleupon;
	private Facebook facebook;
	private Integer googleplusone;
	private Integer pinterest;
	private Integer linkedin;
	private Integer twitter;

	public Integer getStumbleupon() {
		return stumbleupon;
	}

	public void setStumbleupon(Integer stumbleupon) {
		this.stumbleupon = stumbleupon;
	}

	public Facebook getFacebook() {
		return facebook;
	}

	public void setFacebook(Facebook facebook) {
		this.facebook = facebook;
	}

	public Integer getGoogleplusone() {
		return googleplusone;
	}

	public void setGoogleplusone(Integer googleplusone) {
		this.googleplusone = googleplusone;
	}

	public Integer getPinterest() {
		return pinterest;
	}

	public void setPinterest(Integer pinterest) {
		this.pinterest = pinterest;
	}

	public Integer getLinkedin() {
		return linkedin;
	}

	public void setLinkedin(Integer linkedin) {
		this.linkedin = linkedin;
	}

	public Integer getTwitter() {
		return twitter;
	}

	public void setTwitter(Integer twitter) {
		this.twitter = twitter;
	}

	@Override
	public String toString() {
		return "SharedCountsValueObject [stumbleupon=" + stumbleupon + ",facebook=" + facebook + ",googleplusone=" + googleplusone
				+ ",pinterest=" + pinterest + ",linkedin=" + linkedin + ",twitter=" + twitter + "]";
	}

}
