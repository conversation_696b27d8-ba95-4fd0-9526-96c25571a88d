package com.actonia.value.object;

import java.util.Arrays;

public class CausalImpactObject {
	private String report;
	private double[] test_time_series;
	private double[] counter_factual_prediction_time_series;
	private double[] counter_factual_prediction_time_series_lower;
	private double[] counter_factual_prediction_time_series_upper;
	private double[] pointwise_causal_effect_time_series;
	private double[] pointwise_causal_effect_time_series_lower;
	private double[] pointwise_causal_effect_time_series_upper;
	private double[] cumulative_effect_time_series;
	private double[] cumulative_effect_time_series_lower;
	private double[] cumulative_effect_time_series_upper;

	public String getReport() {
		return report;
	}

	public void setReport(String report) {
		this.report = report;
	}

	public double[] getTest_time_series() {
		return test_time_series;
	}

	public void setTest_time_series(double[] test_time_series) {
		this.test_time_series = test_time_series;
	}

	public double[] getCounter_factual_prediction_time_series() {
		return counter_factual_prediction_time_series;
	}

	public void setCounter_factual_prediction_time_series(double[] counter_factual_prediction_time_series) {
		this.counter_factual_prediction_time_series = counter_factual_prediction_time_series;
	}

	public double[] getCounter_factual_prediction_time_series_lower() {
		return counter_factual_prediction_time_series_lower;
	}

	public void setCounter_factual_prediction_time_series_lower(double[] counter_factual_prediction_time_series_lower) {
		this.counter_factual_prediction_time_series_lower = counter_factual_prediction_time_series_lower;
	}

	public double[] getCounter_factual_prediction_time_series_upper() {
		return counter_factual_prediction_time_series_upper;
	}

	public void setCounter_factual_prediction_time_series_upper(double[] counter_factual_prediction_time_series_upper) {
		this.counter_factual_prediction_time_series_upper = counter_factual_prediction_time_series_upper;
	}

	public double[] getPointwise_causal_effect_time_series() {
		return pointwise_causal_effect_time_series;
	}

	public void setPointwise_causal_effect_time_series(double[] pointwise_causal_effect_time_series) {
		this.pointwise_causal_effect_time_series = pointwise_causal_effect_time_series;
	}

	public double[] getPointwise_causal_effect_time_series_lower() {
		return pointwise_causal_effect_time_series_lower;
	}

	public void setPointwise_causal_effect_time_series_lower(double[] pointwise_causal_effect_time_series_lower) {
		this.pointwise_causal_effect_time_series_lower = pointwise_causal_effect_time_series_lower;
	}

	public double[] getPointwise_causal_effect_time_series_upper() {
		return pointwise_causal_effect_time_series_upper;
	}

	public void setPointwise_causal_effect_time_series_upper(double[] pointwise_causal_effect_time_series_upper) {
		this.pointwise_causal_effect_time_series_upper = pointwise_causal_effect_time_series_upper;
	}

	public double[] getCumulative_effect_time_series() {
		return cumulative_effect_time_series;
	}

	public void setCumulative_effect_time_series(double[] cumulative_effect_time_series) {
		this.cumulative_effect_time_series = cumulative_effect_time_series;
	}

	public double[] getCumulative_effect_time_series_lower() {
		return cumulative_effect_time_series_lower;
	}

	public void setCumulative_effect_time_series_lower(double[] cumulative_effect_time_series_lower) {
		this.cumulative_effect_time_series_lower = cumulative_effect_time_series_lower;
	}

	public double[] getCumulative_effect_time_series_upper() {
		return cumulative_effect_time_series_upper;
	}

	public void setCumulative_effect_time_series_upper(double[] cumulative_effect_time_series_upper) {
		this.cumulative_effect_time_series_upper = cumulative_effect_time_series_upper;
	}

	@Override
	public String toString() {
		return "CausalImpactObject [report=" + report + ", test_time_series=" + Arrays.toString(test_time_series) + ", counter_factual_prediction_time_series="
				+ Arrays.toString(counter_factual_prediction_time_series) + ", counter_factual_prediction_time_series_lower="
				+ Arrays.toString(counter_factual_prediction_time_series_lower) + ", counter_factual_prediction_time_series_upper="
				+ Arrays.toString(counter_factual_prediction_time_series_upper) + ", pointwise_causal_effect_time_series="
				+ Arrays.toString(pointwise_causal_effect_time_series) + ", pointwise_causal_effect_time_series_lower="
				+ Arrays.toString(pointwise_causal_effect_time_series_lower) + ", pointwise_causal_effect_time_series_upper="
				+ Arrays.toString(pointwise_causal_effect_time_series_upper) + ", cumulative_effect_time_series=" + Arrays.toString(cumulative_effect_time_series)
				+ ", cumulative_effect_time_series_lower=" + Arrays.toString(cumulative_effect_time_series_lower) + ", cumulative_effect_time_series_upper="
				+ Arrays.toString(cumulative_effect_time_series_upper) + "]";
	}

}
