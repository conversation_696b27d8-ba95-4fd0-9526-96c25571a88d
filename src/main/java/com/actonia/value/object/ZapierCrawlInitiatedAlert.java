package com.actonia.value.object;

public class ZapierCrawlInitiatedAlert {
	private String id;
	private Integer domain_id;
	private String domain_name;
	private Integer request_id;
	private String crawl_start_timestamp;
	private String requester_email;
	private String project;
	private String language;
	private String what_to_crawl;
	private String starting_url;
	private String crawl_type;
	private Integer crawl_speed;
	private Integer crawl_depth;
	private String description;
	private String user_agent;
	private Boolean obey_robots_txt;
	private String store_blocked_links;
	private Boolean enable_cookies;
	private String region;
	private String url_parameters_to_remove;
	private Boolean internal_links_analysis;
	private Boolean hreflang_crawl;
	private Boolean canonical_crawl;
	private String allow_domains;
	private String deny_domains;
	private Boolean follow_nofollow_links;
	private String url_patterns_to_allow;
	private String url_patterns_to_disallow;
	private String urls_to_crawl_but_not_index;
	private String urls_to_index_but_not_crawl;
	private String restrict_to_xpath;
	private String restrict_to_css;
	private String additional_content;
	private String custom_search;
	private String error_code;
	private String error_message;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getCrawl_start_timestamp() {
		return crawl_start_timestamp;
	}

	public void setCrawl_start_timestamp(String crawl_start_timestamp) {
		this.crawl_start_timestamp = crawl_start_timestamp;
	}

	public Integer getDomain_id() {
		return domain_id;
	}

	public void setDomain_id(Integer domain_id) {
		this.domain_id = domain_id;
	}

	public String getDomain_name() {
		return domain_name;
	}

	public void setDomain_name(String domain_name) {
		this.domain_name = domain_name;
	}

	public Integer getRequest_id() {
		return request_id;
	}

	public void setRequest_id(Integer request_id) {
		this.request_id = request_id;
	}

	public String getRequester_email() {
		return requester_email;
	}

	public void setRequester_email(String requester_email) {
		this.requester_email = requester_email;
	}

	public String getProject() {
		return project;
	}

	public void setProject(String project) {
		this.project = project;
	}

	public String getLanguage() {
		return language;
	}

	public void setLanguage(String language) {
		this.language = language;
	}

	public String getWhat_to_crawl() {
		return what_to_crawl;
	}

	public void setWhat_to_crawl(String what_to_crawl) {
		this.what_to_crawl = what_to_crawl;
	}

	public String getStarting_url() {
		return starting_url;
	}

	public void setStarting_url(String starting_url) {
		this.starting_url = starting_url;
	}

	public String getCrawl_type() {
		return crawl_type;
	}

	public void setCrawl_type(String crawl_type) {
		this.crawl_type = crawl_type;
	}

	public Integer getCrawl_speed() {
		return crawl_speed;
	}

	public void setCrawl_speed(Integer crawl_speed) {
		this.crawl_speed = crawl_speed;
	}

	public Integer getCrawl_depth() {
		return crawl_depth;
	}

	public void setCrawl_depth(Integer crawl_depth) {
		this.crawl_depth = crawl_depth;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getUser_agent() {
		return user_agent;
	}

	public void setUser_agent(String user_agent) {
		this.user_agent = user_agent;
	}

	public Boolean getObey_robots_txt() {
		return obey_robots_txt;
	}

	public void setObey_robots_txt(Boolean obey_robots_txt) {
		this.obey_robots_txt = obey_robots_txt;
	}

	public String getStore_blocked_links() {
		return store_blocked_links;
	}

	public void setStore_blocked_links(String store_blocked_links) {
		this.store_blocked_links = store_blocked_links;
	}

	public Boolean getEnable_cookies() {
		return enable_cookies;
	}

	public void setEnable_cookies(Boolean enable_cookies) {
		this.enable_cookies = enable_cookies;
	}

	public String getRegion() {
		return region;
	}

	public void setRegion(String region) {
		this.region = region;
	}

	public String getUrl_parameters_to_remove() {
		return url_parameters_to_remove;
	}

	public void setUrl_parameters_to_remove(String url_parameters_to_remove) {
		this.url_parameters_to_remove = url_parameters_to_remove;
	}

	public Boolean getInternal_links_analysis() {
		return internal_links_analysis;
	}

	public void setInternal_links_analysis(Boolean internal_links_analysis) {
		this.internal_links_analysis = internal_links_analysis;
	}

	public Boolean getHreflang_crawl() {
		return hreflang_crawl;
	}

	public void setHreflang_crawl(Boolean hreflang_crawl) {
		this.hreflang_crawl = hreflang_crawl;
	}

	public Boolean getCanonical_crawl() {
		return canonical_crawl;
	}

	public void setCanonical_crawl(Boolean canonical_crawl) {
		this.canonical_crawl = canonical_crawl;
	}

	public String getAllow_domains() {
		return allow_domains;
	}

	public void setAllow_domains(String allow_domains) {
		this.allow_domains = allow_domains;
	}

	public String getDeny_domains() {
		return deny_domains;
	}

	public void setDeny_domains(String deny_domains) {
		this.deny_domains = deny_domains;
	}

	public Boolean getFollow_nofollow_links() {
		return follow_nofollow_links;
	}

	public void setFollow_nofollow_links(Boolean follow_nofollow_links) {
		this.follow_nofollow_links = follow_nofollow_links;
	}

	public String getUrl_patterns_to_allow() {
		return url_patterns_to_allow;
	}

	public void setUrl_patterns_to_allow(String url_patterns_to_allow) {
		this.url_patterns_to_allow = url_patterns_to_allow;
	}

	public String getUrl_patterns_to_disallow() {
		return url_patterns_to_disallow;
	}

	public void setUrl_patterns_to_disallow(String url_patterns_to_disallow) {
		this.url_patterns_to_disallow = url_patterns_to_disallow;
	}

	public String getUrls_to_crawl_but_not_index() {
		return urls_to_crawl_but_not_index;
	}

	public void setUrls_to_crawl_but_not_index(String urls_to_crawl_but_not_index) {
		this.urls_to_crawl_but_not_index = urls_to_crawl_but_not_index;
	}

	public String getUrls_to_index_but_not_crawl() {
		return urls_to_index_but_not_crawl;
	}

	public void setUrls_to_index_but_not_crawl(String urls_to_index_but_not_crawl) {
		this.urls_to_index_but_not_crawl = urls_to_index_but_not_crawl;
	}

	public String getRestrict_to_xpath() {
		return restrict_to_xpath;
	}

	public void setRestrict_to_xpath(String restrict_to_xpath) {
		this.restrict_to_xpath = restrict_to_xpath;
	}

	public String getRestrict_to_css() {
		return restrict_to_css;
	}

	public void setRestrict_to_css(String restrict_to_css) {
		this.restrict_to_css = restrict_to_css;
	}

	public String getAdditional_content() {
		return additional_content;
	}

	public void setAdditional_content(String additional_content) {
		this.additional_content = additional_content;
	}

	public String getCustom_search() {
		return custom_search;
	}

	public void setCustom_search(String custom_search) {
		this.custom_search = custom_search;
	}

	public String getError_code() {
		return error_code;
	}

	public void setError_code(String error_code) {
		this.error_code = error_code;
	}

	public String getError_message() {
		return error_message;
	}

	public void setError_message(String error_message) {
		this.error_message = error_message;
	}

	@Override
	public String toString() {
		return "ZapierCrawlInitiatedAlert [id=" + id + ", crawl_start_timestamp=" + crawl_start_timestamp + ", domain_id=" + domain_id + ", domain_name=" + domain_name
				+ ", request_id=" + request_id + ", requester_email=" + requester_email + ", project=" + project + ", language=" + language + ", what_to_crawl="
				+ what_to_crawl + ", starting_url=" + starting_url + ", crawl_type=" + crawl_type + ", crawl_speed=" + crawl_speed + ", crawl_depth=" + crawl_depth
				+ ", description=" + description + ", user_agent=" + user_agent + ", obey_robots_txt=" + obey_robots_txt + ", store_blocked_links="
				+ store_blocked_links + ", enable_cookies=" + enable_cookies + ", region=" + region + ", url_parameters_to_remove=" + url_parameters_to_remove
				+ ", internal_links_analysis=" + internal_links_analysis + ", hreflang_crawl=" + hreflang_crawl + ", canonical_crawl=" + canonical_crawl
				+ ", allow_domains=" + allow_domains + ", deny_domains=" + deny_domains + ", follow_nofollow_links=" + follow_nofollow_links
				+ ", url_patterns_to_allow=" + url_patterns_to_allow + ", url_patterns_to_disallow=" + url_patterns_to_disallow + ", urls_to_crawl_but_not_index="
				+ urls_to_crawl_but_not_index + ", urls_to_index_but_not_crawl=" + urls_to_index_but_not_crawl + ", restrict_to_xpath=" + restrict_to_xpath
				+ ", restrict_to_css=" + restrict_to_css + ", additional_content=" + additional_content + ", custom_search=" + custom_search + ", error_code="
				+ error_code + ", error_message=" + error_message + "]";
	}

}
