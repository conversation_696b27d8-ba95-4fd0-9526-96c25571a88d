package com.actonia.value.object;

public class RResponse {
	private CausalImpactResponse causalImpactResponse;
	private ProphetResponse prophetResponse;
	private MarketMatchingResponse marketMatchingResponse;

	public CausalImpactResponse getCausalImpactResponse() {
		return causalImpactResponse;
	}

	public void setCausalImpactResponse(CausalImpactResponse causalImpactResponse) {
		this.causalImpactResponse = causalImpactResponse;
	}

	public ProphetResponse getProphetResponse() {
		return prophetResponse;
	}

	public void setProphetResponse(ProphetResponse prophetResponse) {
		this.prophetResponse = prophetResponse;
	}

	public MarketMatchingResponse getMarketMatchingResponse() {
		return marketMatchingResponse;
	}

	public void setMarketMatchingResponse(MarketMatchingResponse marketMatchingResponse) {
		this.marketMatchingResponse = marketMatchingResponse;
	}

	@Override
	public String toString() {
		return "RResponse [causalImpactResponse=" + causalImpactResponse + ", prophetResponse=" + prophetResponse + ", marketMatchingResponse=" + marketMatchingResponse
				+ "]";
	}

}
