package com.actonia.value.object;

public class AlternateLinks {
	private Integer index;
	private String href;

	public Integer getIndex() {
		return index;
	}

	public void setIndex(Integer index) {
		this.index = index;
	}

	public String getHref() {
		return href;
	}

	public void setHref(String href) {
		this.href = href;
	}

	@Override
	public String toString() {
		return "AlternateLinks [index=" + index + ", href=" + href + "]";
	}

}
