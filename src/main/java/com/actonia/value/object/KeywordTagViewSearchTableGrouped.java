package com.actonia.value.object;

import java.util.Arrays;

public class KeywordTagViewSearchTableGrouped {
	private String keyword_name;
	private String avg_search_volume;
	private int rank;
	private String location_id;
	private String[] groupTags;
	private String ranking_url;

	public String getKeyword_name() {
		return keyword_name;
	}

	public void setKeyword_name(String keyword_name) {
		this.keyword_name = keyword_name;
	}

	public String getAvg_search_volume() {
		return avg_search_volume;
	}

	public void setAvg_search_volume(String avg_search_volume) {
		this.avg_search_volume = avg_search_volume;
	}

	public int getRank() {
		return rank;
	}

	public void setRank(int rank) {
		this.rank = rank;
	}

	public String getLocation_id() {
		return location_id;
	}

	public void setLocation_id(String location_id) {
		this.location_id = location_id;
	}

	public String[] getGroupTags() {
		return groupTags;
	}

	public void setGroupTags(String[] groupTags) {
		this.groupTags = groupTags;
	}

	public String getRanking_url() {
		return ranking_url;
	}

	public void setRanking_url(String ranking_url) {
		this.ranking_url = ranking_url;
	}

	@Override
	public String toString() {
		return "KeywordTagViewSearchTableGrouped [keyword_name=" + keyword_name + ", avg_search_volume=" + avg_search_volume + ", rank=" + rank + ", location_id="
				+ location_id + ", groupTags=" + Arrays.toString(groupTags) + ", ranking_url=" + ranking_url + "]";
	}

}
