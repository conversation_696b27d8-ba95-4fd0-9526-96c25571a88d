package com.actonia.value.object;

public class TwitterMarkup {
	private String property;
	private String content;
	public String getProperty() {
		return property;
	}
	public void setProperty(String property) {
		this.property = property;
	}
	public String getContent() {
		return content;
	}
	public void setContent(String content) {
		this.content = content;
	}
	@Override
	public String toString() {
		return "TwitterMarkup [property=" + property + ", content=" + content + "]";
	}

}
