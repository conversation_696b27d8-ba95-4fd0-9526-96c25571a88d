package com.actonia.value.object;

public class PuppeteerTestValueObject {
	private String timestamp;
	private String threadNumber;
	private String urlString;
	private String responseCode;
	private String elapsedTimeInSecond;

	public String getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(String timestamp) {
		this.timestamp = timestamp;
	}

	public String getThreadNumber() {
		return threadNumber;
	}

	public void setThreadNumber(String threadNumber) {
		this.threadNumber = threadNumber;
	}

	public String getUrlString() {
		return urlString;
	}

	public void setUrlString(String urlString) {
		this.urlString = urlString;
	}

	public String getResponseCode() {
		return responseCode;
	}

	public void setResponseCode(String responseCode) {
		this.responseCode = responseCode;
	}

	public String getElapsedTimeInSecond() {
		return elapsedTimeInSecond;
	}

	public void setElapsedTimeInSecond(String elapsedTimeInSecond) {
		this.elapsedTimeInSecond = elapsedTimeInSecond;
	}

	@Override
	public String toString() {
		return "PuppeteerTestValueObject [timestamp=" + timestamp + ", threadNumber=" + threadNumber + ", urlString=" + urlString + ", responseCode=" + responseCode
				+ ", elapsedTimeInSecond=" + elapsedTimeInSecond + "]";
	}

}
