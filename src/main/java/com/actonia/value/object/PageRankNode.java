package com.actonia.value.object;

import java.math.BigDecimal;
import java.math.RoundingMode;

import javax.persistence.Transient;

public class PageRankNode {
	private int nodeId;
	private BigDecimal nodeWeight = new BigDecimal(0.25000).setScale(5, RoundingMode.HALF_UP);
	private int nodeCount = 0;
	private boolean nodeConverged = false;

	@Transient
	private BigDecimal transferredNodeWeight;

	public int getNodeId() {
		return nodeId;
	}

	public void setNodeId(int nodeId) {
		this.nodeId = nodeId;
	}

	public BigDecimal getNodeWeight() {
		return nodeWeight;
	}

	public void setNodeWeight(BigDecimal nodeWeight) {
		this.nodeWeight = nodeWeight;
	}

	public int getNodeCount() {
		return nodeCount;
	}

	public void setNodeCount(int nodeCount) {
		this.nodeCount = nodeCount;
	}

	public boolean isNodeConverged() {
		return nodeConverged;
	}

	public void setNodeConverged(boolean nodeConverged) {
		this.nodeConverged = nodeConverged;
	}

	public BigDecimal getTransferredNodeWeight() {
		return transferredNodeWeight;
	}

	public void setTransferredNodeWeight(BigDecimal transferredNodeWeight) {
		this.transferredNodeWeight = transferredNodeWeight;
	}

	@Override
	public String toString() {
		return "PageRankNode [nodeId=" + nodeId + ", nodeWeight=" + nodeWeight + ", nodeCount=" + nodeCount + ", nodeConverged=" + nodeConverged
				+ ", transferredNodeWeight=" + transferredNodeWeight + "]";
	}

}