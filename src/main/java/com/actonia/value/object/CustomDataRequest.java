package com.actonia.value.object;

public class CustomDataRequest {
	private String selector_type;
	private String selector;
	private FieldsRequired fields_required;
	private boolean enable_multi_match;
	private int index;

	public String getSelector_type() {
		return selector_type;
	}

	public void setSelector_type(String selector_type) {
		this.selector_type = selector_type;
	}

	public String getSelector() {
		return selector;
	}

	public void setSelector(String selector) {
		this.selector = selector;
	}

	public FieldsRequired getFields_required() {
		return fields_required;
	}

	public void setFields_required(FieldsRequired fields_required) {
		this.fields_required = fields_required;
	}

	public int getIndex() {
		return index;
	}

	public void setIndex(int index) {
		this.index = index;
	}

	public boolean isEnable_multi_match() {
		return enable_multi_match;
	}

	public void setEnable_multi_match(boolean enable_multi_match) {
		this.enable_multi_match = enable_multi_match;
	}

	@Override
	public String toString() {
		return "CustomDataRequest [selector_type=" + selector_type + ", selector=" + selector + ", fields_required=" + fields_required + ", enable_multi_match="
				+ enable_multi_match + ", index=" + index + "]";
	}

}
