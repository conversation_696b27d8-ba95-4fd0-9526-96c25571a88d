package com.actonia.value.object;

public class LogValueObject {
	private int domainId;
	private String url;
	private int responseCode;
	private int elapsedInSeconds;

	public int getDomainId() {
		return domainId;
	}

	public void setDomainId(int domainId) {
		this.domainId = domainId;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public int getResponseCode() {
		return responseCode;
	}

	public void setResponseCode(int responseCode) {
		this.responseCode = responseCode;
	}

	public int getElapsedInSeconds() {
		return elapsedInSeconds;
	}

	public void setElapsedInSeconds(int elapsedInSeconds) {
		this.elapsedInSeconds = elapsedInSeconds;
	}

	@Override
	public String toString() {
		return "LogValueObject [domainId=" + domainId + ", url=" + url + ", responseCode=" + responseCode + ", elapsedInSeconds=" + elapsedInSeconds + "]";
	}

}
