package com.actonia.value.object;

public class SiteMapUrls {
	private String siteMapName;
	private String validity;
	private String message;
	private Integer validityStatusCode;
	private String type;

	public String getSiteMapName() {
		return siteMapName;
	}

	public void setSiteMapName(String siteMapName) {
		this.siteMapName = siteMapName;
	}

	public String getValidity() {
		return validity;
	}

	public void setValidity(String validity) {
		this.validity = validity;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public Integer getValidityStatusCode() {
		return validityStatusCode;
	}

	public void setValidityStatusCode(Integer validityStatusCode) {
		this.validityStatusCode = validityStatusCode;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	@Override
	public String toString() {
		return "SiteMapUrls [siteMapName=" + siteMapName + ", validity=" + validity + ", message=" + message + ", validityStatusCode=" + validityStatusCode + ", type="
				+ type + "]";
	}

}
