package com.actonia.value.object;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.actonia.IConstants;
import com.actonia.utils.FormatUtils;

public class UrlMetricsUpdateValueObject {
	private String politeCrawlDocumentId;
	private Integer politeCrawlTrackDate;
	private Integer politeCrawlResponseCode;
	private Integer politeCrawlInternalLinks;
	private Integer politeCrawlOutboundLinks;
	private Integer politeCrawlPageRank;
	private Float politeCrawlPageAuthority;
	private Integer urlMetricsId;
	private Integer urlMetricsResponseCode;
	private Integer urlMetricsInternalLinks;
	private Integer urlMetricsOutboundLinks;
	private Integer urlMetricsPageRank;
	private Float urlMetricsPageAuthority;
	private Date respcodeUpdateDate;
	private String title;
	private String metaKeywords;
	private String metaDesc;
	private String metaRobots;
	private List<String> h1List;
	private List<String> h2List;
	private Integer targetUrlWeekEntrances;

	// percent_mobile_entrances
	private BigDecimal percentMobileEntrancesExisting;
	private BigDecimal percentMobileEntrancesNew;

	// associated_keywords
	private Integer associatedKeywordsExisting;
	private Integer associatedKeywordsNew;

	// associated_keywords_ranked
	private Integer associatedKeywordsRankedExisting;
	private Integer associatedKeywordsRankedNew;

	// links_acquired_organically
	private Integer linksAcquiredOrganicallyExisting;
	private Integer linksAcquiredOrganicallyNew;

	// links_acquired_manually
	private Integer linksAcquiredManuallyExisting;
	private Integer linksAcquiredManuallyNew;

	// associated_competitors
	private Integer associatedCompetitorsExisting;
	private Integer associatedCompetitorsNew;

	// target URL string
	private String targetUrlString;

	public Integer getUrlMetricsResponseCode() {
		return urlMetricsResponseCode;
	}

	public void setUrlMetricsResponseCode(Integer urlMetricsResponseCode) {
		this.urlMetricsResponseCode = urlMetricsResponseCode;
	}

	public Integer getPoliteCrawlResponseCode() {
		return politeCrawlResponseCode;
	}

	public void setPoliteCrawlResponseCode(Integer politeCrawlResponseCode) {
		this.politeCrawlResponseCode = politeCrawlResponseCode;
	}

	public Integer getPoliteCrawlInternalLinks() {
		return politeCrawlInternalLinks;
	}

	public void setPoliteCrawlInternalLinks(Integer politeCrawlInternalLinks) {
		this.politeCrawlInternalLinks = politeCrawlInternalLinks;
	}

	public Integer getPoliteCrawlOutboundLinks() {
		return politeCrawlOutboundLinks;
	}

	public void setPoliteCrawlOutboundLinks(Integer politeCrawlOutboundLinks) {
		this.politeCrawlOutboundLinks = politeCrawlOutboundLinks;
	}

	public Integer getUrlMetricsInternalLinks() {
		return urlMetricsInternalLinks;
	}

	public void setUrlMetricsInternalLinks(Integer urlMetricsInternalLinks) {
		this.urlMetricsInternalLinks = urlMetricsInternalLinks;
	}

	public Integer getUrlMetricsOutboundLinks() {
		return urlMetricsOutboundLinks;
	}

	public void setUrlMetricsOutboundLinks(Integer urlMetricsOutboundLinks) {
		this.urlMetricsOutboundLinks = urlMetricsOutboundLinks;
	}

	public Integer getPoliteCrawlPageRank() {
		return politeCrawlPageRank;
	}

	public void setPoliteCrawlPageRank(Integer politeCrawlPageRank) {
		this.politeCrawlPageRank = politeCrawlPageRank;
	}

	public Integer getUrlMetricsPageRank() {
		return urlMetricsPageRank;
	}

	public void setUrlMetricsPageRank(Integer urlMetricsPageRank) {
		this.urlMetricsPageRank = urlMetricsPageRank;
	}

	public Float getPoliteCrawlPageAuthority() {
		return politeCrawlPageAuthority;
	}

	public void setPoliteCrawlPageAuthority(Float politeCrawlPageAuthority) {
		this.politeCrawlPageAuthority = politeCrawlPageAuthority;
	}

	public Integer getPoliteCrawlTrackDate() {
		return politeCrawlTrackDate;
	}

	public void setPoliteCrawlTrackDate(Integer politeCrawlTrackDate) {
		this.politeCrawlTrackDate = politeCrawlTrackDate;
		this.respcodeUpdateDate = FormatUtils.getInstance().toDate(String.valueOf(politeCrawlTrackDate), IConstants.DATE_FORMAT_YYYYMMDD);
	}

	public Integer getUrlMetricsId() {
		return urlMetricsId;
	}

	public void setUrlMetricsId(Integer urlMetricsId) {
		this.urlMetricsId = urlMetricsId;
	}

	public Date getRespcodeUpdateDate() {
		return respcodeUpdateDate;
	}

	public void setRespcodeUpdateDate(Date respcodeUpdateDate) {
		this.respcodeUpdateDate = respcodeUpdateDate;
	}

	public Float getUrlMetricsPageAuthority() {
		return urlMetricsPageAuthority;
	}

	public void setUrlMetricsPageAuthority(Float urlMetricsPageAuthority) {
		this.urlMetricsPageAuthority = urlMetricsPageAuthority;
	}

	public String getPoliteCrawlDocumentId() {
		return politeCrawlDocumentId;
	}

	public void setPoliteCrawlDocumentId(String politeCrawlDocumentId) {
		this.politeCrawlDocumentId = politeCrawlDocumentId;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getMetaKeywords() {
		return metaKeywords;
	}

	public void setMetaKeywords(String metaKeywords) {
		this.metaKeywords = metaKeywords;
	}

	public String getMetaDesc() {
		return metaDesc;
	}

	public void setMetaDesc(String metaDesc) {
		this.metaDesc = metaDesc;
	}

	public String getMetaRobots() {
		return metaRobots;
	}

	public void setMetaRobots(String metaRobots) {
		this.metaRobots = metaRobots;
	}

	public List<String> getH1List() {
		return h1List;
	}

	public void setH1List(List<String> h1List) {
		this.h1List = h1List;
	}

	public List<String> getH2List() {
		return h2List;
	}

	public void setH2List(List<String> h2List) {
		this.h2List = h2List;
	}

	public BigDecimal getPercentMobileEntrancesExisting() {
		return percentMobileEntrancesExisting;
	}

	public void setPercentMobileEntrancesExisting(BigDecimal percentMobileEntrancesExisting) {
		this.percentMobileEntrancesExisting = percentMobileEntrancesExisting;
	}

	public BigDecimal getPercentMobileEntrancesNew() {
		return percentMobileEntrancesNew;
	}

	public void setPercentMobileEntrancesNew(BigDecimal percentMobileEntrancesNew) {
		this.percentMobileEntrancesNew = percentMobileEntrancesNew;
	}

	public Integer getAssociatedKeywordsExisting() {
		return associatedKeywordsExisting;
	}

	public void setAssociatedKeywordsExisting(Integer associatedKeywordsExisting) {
		this.associatedKeywordsExisting = associatedKeywordsExisting;
	}

	public Integer getAssociatedKeywordsNew() {
		return associatedKeywordsNew;
	}

	public void setAssociatedKeywordsNew(Integer associatedKeywordsNew) {
		this.associatedKeywordsNew = associatedKeywordsNew;
	}

	public Integer getAssociatedKeywordsRankedExisting() {
		return associatedKeywordsRankedExisting;
	}

	public void setAssociatedKeywordsRankedExisting(Integer associatedKeywordsRankedExisting) {
		this.associatedKeywordsRankedExisting = associatedKeywordsRankedExisting;
	}

	public Integer getAssociatedKeywordsRankedNew() {
		return associatedKeywordsRankedNew;
	}

	public void setAssociatedKeywordsRankedNew(Integer associatedKeywordsRankedNew) {
		this.associatedKeywordsRankedNew = associatedKeywordsRankedNew;
	}

	public Integer getLinksAcquiredOrganicallyExisting() {
		return linksAcquiredOrganicallyExisting;
	}

	public void setLinksAcquiredOrganicallyExisting(Integer linksAcquiredOrganicallyExisting) {
		this.linksAcquiredOrganicallyExisting = linksAcquiredOrganicallyExisting;
	}

	public Integer getLinksAcquiredOrganicallyNew() {
		return linksAcquiredOrganicallyNew;
	}

	public void setLinksAcquiredOrganicallyNew(Integer linksAcquiredOrganicallyNew) {
		this.linksAcquiredOrganicallyNew = linksAcquiredOrganicallyNew;
	}

	public Integer getLinksAcquiredManuallyExisting() {
		return linksAcquiredManuallyExisting;
	}

	public void setLinksAcquiredManuallyExisting(Integer linksAcquiredManuallyExisting) {
		this.linksAcquiredManuallyExisting = linksAcquiredManuallyExisting;
	}

	public Integer getLinksAcquiredManuallyNew() {
		return linksAcquiredManuallyNew;
	}

	public void setLinksAcquiredManuallyNew(Integer linksAcquiredManuallyNew) {
		this.linksAcquiredManuallyNew = linksAcquiredManuallyNew;
	}

	public Integer getAssociatedCompetitorsExisting() {
		return associatedCompetitorsExisting;
	}

	public void setAssociatedCompetitorsExisting(Integer associatedCompetitorsExisting) {
		this.associatedCompetitorsExisting = associatedCompetitorsExisting;
	}

	public Integer getAssociatedCompetitorsNew() {
		return associatedCompetitorsNew;
	}

	public void setAssociatedCompetitorsNew(Integer associatedCompetitorsNew) {
		this.associatedCompetitorsNew = associatedCompetitorsNew;
	}

	public Integer getTargetUrlWeekEntrances() {
		return targetUrlWeekEntrances;
	}

	public void setTargetUrlWeekEntrances(Integer targetUrlWeekEntrances) {
		this.targetUrlWeekEntrances = targetUrlWeekEntrances;
	}

	public String getTargetUrlString() {
		return targetUrlString;
	}

	public void setTargetUrlString(String targetUrlString) {
		this.targetUrlString = targetUrlString;
	}

	@Override
	public String toString() {
		return " UrlMetricsUpdateValueObject [politeCrawlDocumentId=" + politeCrawlDocumentId + ", politeCrawlTrackDate=" + politeCrawlTrackDate
				+ ", politeCrawlResponseCode=" + politeCrawlResponseCode + ", politeCrawlInternalLinks=" + politeCrawlInternalLinks + ", politeCrawlOutboundLinks="
				+ politeCrawlOutboundLinks + ", politeCrawlPageRank=" + politeCrawlPageRank + ", politeCrawlPageAuthority=" + politeCrawlPageAuthority
				+ ", urlMetricsId=" + urlMetricsId + ", urlMetricsResponseCode=" + urlMetricsResponseCode + ", urlMetricsInternalLinks=" + urlMetricsInternalLinks
				+ ", urlMetricsOutboundLinks=" + urlMetricsOutboundLinks + ", urlMetricsPageRank=" + urlMetricsPageRank + ", urlMetricsPageAuthority="
				+ urlMetricsPageAuthority + ", respcodeUpdateDate=" + respcodeUpdateDate + ", title=" + title + ", metaKeywords=" + metaKeywords + ", metaDesc="
				+ metaDesc + ", metaRobots=" + metaRobots + ", h1List=" + h1List + ", h2List=" + h2List + ", targetUrlWeekEntrances=" + targetUrlWeekEntrances
				+ ", percentMobileEntrancesExisting=" + percentMobileEntrancesExisting + ", percentMobileEntrancesNew=" + percentMobileEntrancesNew
				+ ", associatedKeywordsExisting=" + associatedKeywordsExisting + ", associatedKeywordsNew=" + associatedKeywordsNew
				+ ", associatedKeywordsRankedExisting=" + associatedKeywordsRankedExisting + ", associatedKeywordsRankedNew=" + associatedKeywordsRankedNew
				+ ", linksAcquiredOrganicallyExisting=" + linksAcquiredOrganicallyExisting + ", linksAcquiredOrganicallyNew=" + linksAcquiredOrganicallyNew
				+ ", linksAcquiredManuallyExisting=" + linksAcquiredManuallyExisting + ", linksAcquiredManuallyNew=" + linksAcquiredManuallyNew
				+ ", associatedCompetitorsExisting=" + associatedCompetitorsExisting + ", associatedCompetitorsNew=" + associatedCompetitorsNew + ", targetUrlString="
				+ targetUrlString + "]";
	}

}
