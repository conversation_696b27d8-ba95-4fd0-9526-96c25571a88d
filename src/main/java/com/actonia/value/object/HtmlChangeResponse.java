package com.actonia.value.object;

import com.actonia.entity.HtmlChange;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
public class HtmlChangeResponse {
	private Boolean success;
	private WebServiceError error;
	private Integer page_number;
	private Integer rows_per_page;
	private Integer sort_by;
	private Integer total_rows;
	private Boolean end_of_detail_list_flag;

	// Change indicator List
	private List<TargetUrlChangeSummary> summary_list;
	private List<TargetUrlChangeDetail> detail_list;
	private List<TargetUrlChangeIndicatorDetail> change_indicator_list;
	private List<ResponseCodeSummary> response_code_summary_list;
	private List<TargetUrlChangeDailySummary> daily_summary_list;

	// URL Summary
	private List<UrlSummary> url_summary_list;

	// change indicator total URLs list
	private List<ChangeIndicatorTotalUrls> change_indicator_total_urls_list;

	private String download_all_link;

	private Long elapsed_millisecond;

	@Data
	@EqualsAndHashCode(callSuper = true)
	public static class HtmlChangeAggregateModel extends HtmlChange {
		private String crawlDateHour;
		private int chgId;
		private String changeIndicator;
		private int total;
		private String changeType;
		private int criticalFlg;
	}
}
