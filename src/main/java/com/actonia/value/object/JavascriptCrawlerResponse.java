package com.actonia.value.object;

import java.util.Map;

public class JavascriptCrawlerResponse {
	private String html;
	private int httpStatusCode;
	private Map<String, String> responseHeaders;

	public String getHtml() {
		return html;
	}

	public void setHtml(String html) {
		this.html = html;
	}

	public int getHttpStatusCode() {
		return httpStatusCode;
	}

	public void setHttpStatusCode(int httpStatusCode) {
		this.httpStatusCode = httpStatusCode;
	}

	public Map<String, String> getResponseHeaders() {
		return responseHeaders;
	}

	public void setResponseHeaders(Map<String, String> responseHeaders) {
		this.responseHeaders = responseHeaders;
	}

	@Override
	public String toString() {
		return "JavascriptCrawlerResponse [html=" + html + ", httpStatusCode=" + httpStatusCode + ", responseHeaders=" + responseHeaders + "]";
	}

}
