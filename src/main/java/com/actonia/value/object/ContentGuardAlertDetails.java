package com.actonia.value.object;

public class ContentGuardAlertDetails {
	private String url;
	private String previousContent;
	private String currentContent;

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getPreviousContent() {
		return previousContent;
	}

	public void setPreviousContent(String previousContent) {
		this.previousContent = previousContent;
	}

	public String getCurrentContent() {
		return currentContent;
	}

	public void setCurrentContent(String currentContent) {
		this.currentContent = currentContent;
	}

	@Override
	public String toString() {
		return "ContentGuardAlertDetails [url=" + url + ", previousContent=" + previousContent + ", currentContent=" + currentContent + "]";
	}

}
