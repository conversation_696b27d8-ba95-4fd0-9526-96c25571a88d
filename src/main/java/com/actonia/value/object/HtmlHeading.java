package com.actonia.value.object;

public class HtmlHeading {
	private Integer index;
	private String value;

	public Integer getIndex() {
		return index;
	}

	public void setIndex(Integer index) {
		this.index = index;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	@Override
	public String toString() {
		return "HtmlHeading [index=" + index + ", value=" + value + "]";
	}

}
