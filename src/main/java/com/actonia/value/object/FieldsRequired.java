package com.actonia.value.object;

public class FieldsRequired {
	private Boolean link;
	private Boolean content;
	private Boolean word_count;

	public Boolean getLink() {
		return link;
	}

	public void setLink(Boolean link) {
		this.link = link;
	}

	public Boolean getContent() {
		return content;
	}

	public void setContent(Boolean content) {
		this.content = content;
	}

	public Boolean getWord_count() {
		return word_count;
	}

	public void setWord_count(Boolean word_count) {
		this.word_count = word_count;
	}

	@Override
	public String toString() {
		return "FieldsRequired [link=" + link + ", content=" + content + ", word_count=" + word_count + "]";
	}

}
