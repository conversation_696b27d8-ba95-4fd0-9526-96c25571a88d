package com.actonia.value.object;

public class SelectorValueObject {
	private String selectorTypeText;
	private String selector;

	public String getSelectorTypeText() {
		return selectorTypeText;
	}

	public void setSelectorTypeText(String selectorTypeText) {
		this.selectorTypeText = selectorTypeText;
	}

	public String getSelector() {
		return selector;
	}

	public void setSelector(String selector) {
		this.selector = selector;
	}

	@Override
	public String toString() {
		return "SelectorValueObject [selectorTypeText=" + selectorTypeText + ", selector=" + selector + "]";
	}

}
