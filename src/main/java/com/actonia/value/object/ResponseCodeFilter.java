package com.actonia.value.object;

public class ResponseCodeFilter {
	private String response_code_previous;
	private String response_code_current;

	public String getResponse_code_previous() {
		return response_code_previous;
	}

	public void setResponse_code_previous(String response_code_previous) {
		this.response_code_previous = response_code_previous;
	}

	public String getResponse_code_current() {
		return response_code_current;
	}

	public void setResponse_code_current(String response_code_current) {
		this.response_code_current = response_code_current;
	}

	@Override
	public String toString() {
		return "ResponseCodeFilter [response_code_previous=" + response_code_previous + ", response_code_current=" + response_code_current + "]";
	}

}
