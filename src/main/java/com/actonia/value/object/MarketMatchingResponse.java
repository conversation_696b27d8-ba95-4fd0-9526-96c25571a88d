package com.actonia.value.object;

import java.util.Arrays;

public class MarketMatchingResponse {
	private Boolean success;
	private WebServiceError error;
	private String test_market;
	private String pre_period_start_date;
	private String pre_period_end_date;
	private String post_period_start_date;
	private String post_period_end_date;
	private Integer number_of_best_matches;
	private BestMatch[] best_match_array;
	private Inference inference;
	private CausalImpactObject causal_impact_object;
	private Coefficients coefficients;

	public Boolean getSuccess() {
		return success;
	}

	public void setSuccess(Boolean success) {
		this.success = success;
	}

	public WebServiceError getError() {
		return error;
	}

	public void setError(WebServiceError error) {
		this.error = error;
	}

	public String getTest_market() {
		return test_market;
	}

	public void setTest_market(String test_market) {
		this.test_market = test_market;
	}

	public String getPre_period_start_date() {
		return pre_period_start_date;
	}

	public void setPre_period_start_date(String pre_period_start_date) {
		this.pre_period_start_date = pre_period_start_date;
	}

	public String getPre_period_end_date() {
		return pre_period_end_date;
	}

	public void setPre_period_end_date(String pre_period_end_date) {
		this.pre_period_end_date = pre_period_end_date;
	}

	public String getPost_period_start_date() {
		return post_period_start_date;
	}

	public void setPost_period_start_date(String post_period_start_date) {
		this.post_period_start_date = post_period_start_date;
	}

	public String getPost_period_end_date() {
		return post_period_end_date;
	}

	public void setPost_period_end_date(String post_period_end_date) {
		this.post_period_end_date = post_period_end_date;
	}

	public Integer getNumber_of_best_matches() {
		return number_of_best_matches;
	}

	public void setNumber_of_best_matches(Integer number_of_best_matches) {
		this.number_of_best_matches = number_of_best_matches;
	}

	public BestMatch[] getBest_match_array() {
		return best_match_array;
	}

	public void setBest_match_array(BestMatch[] best_match_array) {
		this.best_match_array = best_match_array;
	}

	public Inference getInference() {
		return inference;
	}

	public void setInference(Inference inference) {
		this.inference = inference;
	}

	public CausalImpactObject getCausal_impact_object() {
		return causal_impact_object;
	}

	public void setCausal_impact_object(CausalImpactObject causal_impact_object) {
		this.causal_impact_object = causal_impact_object;
	}

	public Coefficients getCoefficients() {
		return coefficients;
	}

	public void setCoefficients(Coefficients coefficients) {
		this.coefficients = coefficients;
	}

	@Override
	public String toString() {
		return "MarketMatchingResponse [success=" + success + ", error=" + error + ", test_market=" + test_market + ", pre_period_start_date=" + pre_period_start_date
				+ ", pre_period_end_date=" + pre_period_end_date + ", post_period_start_date=" + post_period_start_date + ", post_period_end_date="
				+ post_period_end_date + ", number_of_best_matches=" + number_of_best_matches + ", best_match_array=" + Arrays.toString(best_match_array)
				+ ", inference=" + inference + ", causal_impact_object=" + causal_impact_object + ", coefficients=" + coefficients + "]";
	}

}
