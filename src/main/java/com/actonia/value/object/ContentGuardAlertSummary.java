package com.actonia.value.object;

import java.util.List;

public class ContentGuardAlertSummary {
	private String changeIndicatorDesc;
	private List<ContentGuardAlertDetails> contentGuardAlertDetailsList;

	public String getChangeIndicatorDesc() {
		return changeIndicatorDesc;
	}

	public void setChangeIndicatorDesc(String changeIndicatorDesc) {
		this.changeIndicatorDesc = changeIndicatorDesc;
	}

	public List<ContentGuardAlertDetails> getContentGuardAlertDetailsList() {
		return contentGuardAlertDetailsList;
	}

	public void setContentGuardAlertDetailsList(List<ContentGuardAlertDetails> contentGuardAlertDetailsList) {
		this.contentGuardAlertDetailsList = contentGuardAlertDetailsList;
	}

	@Override
	public String toString() {
		return "ContentGuardAlertSummary [changeIndicatorDesc=" + changeIndicatorDesc + "]";
	}

}
