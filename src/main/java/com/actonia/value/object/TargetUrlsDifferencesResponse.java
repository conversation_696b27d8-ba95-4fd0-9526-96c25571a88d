package com.actonia.value.object;

import java.util.Arrays;

public class TargetUrlsDifferencesResponse extends PoliteCrawlServiceResponse {
	private String sequence;
	private String crawl_timestamp_1;
	private String domain_id_1;
	private String url_1;
	private String crawl_timestamp_2;
	private String domain_id_2;
	private String url_2;
	private Boolean is_different;
	private Boolean alternate_links_chg_ind;
	private Boolean amphtml_href_chg_ind;
	private Boolean analyzed_url_s_chg_ind;
	private Boolean archive_flg_chg_ind;
	private Boolean base_tag_added_ind;
	private Boolean base_tag_chg_ind;
	private Boolean base_tag_removed_ind;
	private Boolean base_tag_target_chg_ind;
	private Boolean blocked_by_robots_chg_ind;
	private Boolean canonical_added_ind;
	private Boolean canonical_chg_ind;
	private Boolean canonical_header_flag_chg_ind;
	private Boolean canonical_header_type_chg_ind;
	private Boolean canonical_removed_ind;
	private Boolean canonical_type_chg_ind;
	private Boolean canonical_url_is_consistent_chg_ind;
	private Boolean content_type_chg_ind;
	private Boolean custom_data_added_ind;
	private Boolean custom_data_chg_ind;
	private Boolean custom_data_removed_ind;
	private Boolean description_added_ind;
	private Boolean description_chg_ind;
	private Boolean description_length_chg_ind;
	private Boolean description_removed_ind;
	private Boolean error_message_chg_ind;
	private Boolean final_response_code_chg_ind;
	private Boolean follow_flg_chg_ind;
	private Boolean h1_added_ind;
	private Boolean h1_chg_ind;
	private Boolean h1_count_chg_ind;
	private Boolean h1_length_chg_ind;
	private Boolean h1_removed_ind;
	private Boolean h2_added_ind;
	private Boolean h2_chg_ind;
	private Boolean h2_removed_ind;
	private Boolean header_noarchive_chg_ind;
	private Boolean header_nofollow_chg_ind;
	private Boolean header_noindex_chg_ind;
	private Boolean header_noodp_chg_ind;
	private Boolean header_nosnippet_chg_ind;
	private Boolean header_noydir_chg_ind;
	private Boolean hreflang_errors_chg_ind;
	private Boolean hreflang_links_added_ind;
	private Boolean hreflang_links_chg_ind;
	private Boolean hreflang_links_out_count_chg_ind;
	private Boolean hreflang_links_removed_ind;
	private Boolean hreflang_url_count_chg_ind;
	private Boolean indexable_chg_ind;
	private Boolean index_flg_chg_ind;
	private Boolean insecure_resources_chg_ind;
	private Boolean meta_charset_chg_ind;
	private Boolean meta_content_type_chg_ind;
	private Boolean meta_disabled_sitelinks_chg_ind;
	private Boolean meta_noodp_chg_ind;
	private Boolean meta_nosnippet_chg_ind;
	private Boolean meta_noydir_chg_ind;
	private Boolean meta_redirect_chg_ind;
	private Boolean mixed_redirects_chg_ind;
	private Boolean mobile_rel_alternate_url_is_consistent_chg_ind;
	private Boolean noodp_chg_ind;
	private Boolean nosnippet_chg_ind;
	private Boolean noydir_chg_ind;
	private Boolean og_markup_chg_ind;
	private Boolean og_markup_length_chg_ind;
	private Boolean open_graph_added_ind;
	private Boolean open_graph_removed_ind;
	private Boolean outlink_count_chg_ind;
	private Boolean page_analysis_fragments_chg_ind;
	private Boolean page_link_chg_ind;
	private Boolean redirect_301_detected_ind;
	private Boolean redirect_301_removed_ind;
	private Boolean redirect_302_detected_ind;
	private Boolean redirect_302_removed_ind;
	private Boolean redirect_blocked_chg_ind;
	private Boolean redirect_blocked_reason_chg_ind;
	private Boolean redirect_chain_chg_ind;
	private Boolean redirect_diff_code_ind;
	private Boolean redirect_final_url_chg_ind;
	private Boolean redirect_times_chg_ind;
	private Boolean response_code_chg_ind;
	private Boolean response_headers_added_ind;
	private Boolean response_headers_removed_ind;
	private Boolean robots_added_ind;
	private Boolean robots_chg_ind;
	private Boolean robots_contents_chg_ind;
	private Boolean robots_removed_ind;
	private Boolean structured_data_chg_ind;
	private Boolean title_added_ind;
	private Boolean title_chg_ind;
	private Boolean title_length_chg_ind;
	private Boolean title_removed_ind;
	private Boolean viewport_added_ind;
	private Boolean viewport_content_chg_ind;
	private Boolean viewport_removed_ind;

	// special processing for 'page_analysis_results_chg_ind_json'
	private String page_analysis_results_chg_ind_json;

	private AlternateLinks[] alternate_links_1;
	private String amphtml_href_1;
	private String analyzed_url_s_1;
	private String archive_flg_1;
	private String base_tag_1;
	private Boolean base_tag_flag_1;
	private String base_tag_target_1;
	private String blocked_by_robots_1;
	private String canonical_1;
	private String canonical_flg_1;
	private Boolean canonical_header_flag_1;
	private String canonical_header_type_1;
	private String canonical_type_1;
	private String canonical_url_is_consistent_1;
	private String content_type_1;
	private CustomData[] custom_data_1;
	private String description_1;
	private String description_flg_1;
	private Integer description_length_1;
	private String error_message_1;
	private Integer final_response_code_1;
	private String follow_flg_1;
	private String[] h1_1;
	private Integer h1_count_1;
	private Integer h1_length_1;
	private String[] h2_1;
	private Boolean header_noarchive_1;
	private Boolean header_nofollow_1;
	private Boolean header_noindex_1;
	private Boolean header_noodp_1;
	private Boolean header_nosnippet_1;
	private Boolean header_noydir_1;
	private HreflangErrors hreflang_errors_1;
	private HreflangLinks[] hreflang_links_1;
	private Integer hreflang_links_out_count_1;
	private Integer hreflang_url_count_1;
	private String index_flg_1;
	private Boolean indexable_1;
	private String[] insecure_resources_1;
	private String meta_charset_1;
	private String meta_content_type_1;
	private Boolean meta_disabled_sitelinks_1;
	private Boolean meta_noodp_1;
	private Boolean meta_nosnippet_1;
	private Boolean meta_noydir_1;
	private Boolean meta_redirect_1;
	private Boolean mixed_redirects_1;
	private Boolean mobile_rel_alternate_url_is_consistent_1;
	private Boolean noodp_1;
	private Boolean nosnippet_1;
	private Boolean noydir_1;
	private OgMarkup[] og_markup_1;
	private Boolean og_markup_flag_1;
	private Integer og_markup_length_1;
	private Integer outlink_count_1;
	private PageAnalysisFragments[] page_analysis_fragments_1;
	private PageLink[] page_link_1;
	private Boolean redirect_blocked_1;
	private String redirect_blocked_reason_1;
	private RedirectChain[] redirect_chain_1;
	private String redirect_final_url_1;
	private Integer redirect_times_1;
	private String response_code_1;
	private String[] response_header_names_1;
	private String robots_1;
	private String robots_contents_1;
	private String robots_flg_1;
	private StructuredData structured_data_1;
	private String title_1;
	private String title_flg_1;
	private Integer title_length_1;
	private String viewport_content_1;
	private Boolean viewport_flag_1;

	private AlternateLinks[] alternate_links_2;
	private String amphtml_href_2;
	private String analyzed_url_s_2;
	private String archive_flg_2;
	private String base_tag_2;
	private Boolean base_tag_flag_2;
	private String base_tag_target_2;
	private String blocked_by_robots_2;
	private String canonical_2;
	private String canonical_flg_2;
	private Boolean canonical_header_flag_2;
	private String canonical_header_type_2;
	private String canonical_type_2;
	private String canonical_url_is_consistent_2;
	private String content_type_2;
	private CustomData[] custom_data_2;
	private String description_2;
	private String description_flg_2;
	private Integer description_length_2;
	private String error_message_2;
	private Integer final_response_code_2;
	private String follow_flg_2;
	private String[] h1_2;
	private Integer h1_count_2;
	private Integer h1_length_2;
	private String[] h2_2;
	private Boolean header_noarchive_2;
	private Boolean header_nofollow_2;
	private Boolean header_noindex_2;
	private Boolean header_noodp_2;
	private Boolean header_nosnippet_2;
	private Boolean header_noydir_2;
	private HreflangErrors hreflang_errors_2;
	private HreflangLinks[] hreflang_links_2;
	private Integer hreflang_links_out_count_2;
	private Integer hreflang_url_count_2;
	private String index_flg_2;
	private Boolean indexable_2;
	private String[] insecure_resources_2;
	private String meta_charset_2;
	private String meta_content_type_2;
	private Boolean meta_disabled_sitelinks_2;
	private Boolean meta_noodp_2;
	private Boolean meta_nosnippet_2;
	private Boolean meta_noydir_2;
	private Boolean meta_redirect_2;
	private Boolean mixed_redirects_2;
	private Boolean mobile_rel_alternate_url_is_consistent_2;
	private Boolean noodp_2;
	private Boolean nosnippet_2;
	private Boolean noydir_2;
	private OgMarkup[] og_markup_2;
	private Boolean og_markup_flag_2;
	private Integer og_markup_length_2;
	private Integer outlink_count_2;
	private PageAnalysisFragments[] page_analysis_fragments_2;
	private PageLink[] page_link_2;
	private Boolean redirect_blocked_2;
	private String redirect_blocked_reason_2;
	private RedirectChain[] redirect_chain_2;
	private String redirect_final_url_2;
	private Integer redirect_times_2;
	private String response_code_2;
	private String[] response_header_names_2;
	private String robots_2;
	private String robots_contents_2;
	private String robots_flg_2;
	private StructuredData structured_data_2;
	private String title_2;
	private String title_flg_2;
	private Integer title_length_2;
	private String viewport_content_2;
	private Boolean viewport_flag_2;

	public String getSequence() {
		return sequence;
	}

	public void setSequence(String sequence) {
		this.sequence = sequence;
	}

	public String getCrawl_timestamp_1() {
		return crawl_timestamp_1;
	}

	public void setCrawl_timestamp_1(String crawl_timestamp_1) {
		this.crawl_timestamp_1 = crawl_timestamp_1;
	}

	public String getDomain_id_1() {
		return domain_id_1;
	}

	public void setDomain_id_1(String domain_id_1) {
		this.domain_id_1 = domain_id_1;
	}

	public String getUrl_1() {
		return url_1;
	}

	public void setUrl_1(String url_1) {
		this.url_1 = url_1;
	}

	public String getCrawl_timestamp_2() {
		return crawl_timestamp_2;
	}

	public void setCrawl_timestamp_2(String crawl_timestamp_2) {
		this.crawl_timestamp_2 = crawl_timestamp_2;
	}

	public String getDomain_id_2() {
		return domain_id_2;
	}

	public void setDomain_id_2(String domain_id_2) {
		this.domain_id_2 = domain_id_2;
	}

	public String getUrl_2() {
		return url_2;
	}

	public void setUrl_2(String url_2) {
		this.url_2 = url_2;
	}

	public Boolean getIs_different() {
		return is_different;
	}

	public void setIs_different(Boolean is_different) {
		this.is_different = is_different;
	}

	public Boolean getAlternate_links_chg_ind() {
		return alternate_links_chg_ind;
	}

	public void setAlternate_links_chg_ind(Boolean alternate_links_chg_ind) {
		this.alternate_links_chg_ind = alternate_links_chg_ind;
	}

	public Boolean getAmphtml_href_chg_ind() {
		return amphtml_href_chg_ind;
	}

	public void setAmphtml_href_chg_ind(Boolean amphtml_href_chg_ind) {
		this.amphtml_href_chg_ind = amphtml_href_chg_ind;
	}

	public Boolean getAnalyzed_url_s_chg_ind() {
		return analyzed_url_s_chg_ind;
	}

	public void setAnalyzed_url_s_chg_ind(Boolean analyzed_url_s_chg_ind) {
		this.analyzed_url_s_chg_ind = analyzed_url_s_chg_ind;
	}

	public Boolean getArchive_flg_chg_ind() {
		return archive_flg_chg_ind;
	}

	public void setArchive_flg_chg_ind(Boolean archive_flg_chg_ind) {
		this.archive_flg_chg_ind = archive_flg_chg_ind;
	}

	public Boolean getBase_tag_added_ind() {
		return base_tag_added_ind;
	}

	public void setBase_tag_added_ind(Boolean base_tag_added_ind) {
		this.base_tag_added_ind = base_tag_added_ind;
	}

	public Boolean getBase_tag_chg_ind() {
		return base_tag_chg_ind;
	}

	public void setBase_tag_chg_ind(Boolean base_tag_chg_ind) {
		this.base_tag_chg_ind = base_tag_chg_ind;
	}

	public Boolean getBase_tag_removed_ind() {
		return base_tag_removed_ind;
	}

	public void setBase_tag_removed_ind(Boolean base_tag_removed_ind) {
		this.base_tag_removed_ind = base_tag_removed_ind;
	}

	public Boolean getBase_tag_target_chg_ind() {
		return base_tag_target_chg_ind;
	}

	public void setBase_tag_target_chg_ind(Boolean base_tag_target_chg_ind) {
		this.base_tag_target_chg_ind = base_tag_target_chg_ind;
	}

	public Boolean getBlocked_by_robots_chg_ind() {
		return blocked_by_robots_chg_ind;
	}

	public void setBlocked_by_robots_chg_ind(Boolean blocked_by_robots_chg_ind) {
		this.blocked_by_robots_chg_ind = blocked_by_robots_chg_ind;
	}

	public Boolean getCanonical_added_ind() {
		return canonical_added_ind;
	}

	public void setCanonical_added_ind(Boolean canonical_added_ind) {
		this.canonical_added_ind = canonical_added_ind;
	}

	public Boolean getCanonical_chg_ind() {
		return canonical_chg_ind;
	}

	public void setCanonical_chg_ind(Boolean canonical_chg_ind) {
		this.canonical_chg_ind = canonical_chg_ind;
	}

	public Boolean getCanonical_header_flag_chg_ind() {
		return canonical_header_flag_chg_ind;
	}

	public void setCanonical_header_flag_chg_ind(Boolean canonical_header_flag_chg_ind) {
		this.canonical_header_flag_chg_ind = canonical_header_flag_chg_ind;
	}

	public Boolean getCanonical_header_type_chg_ind() {
		return canonical_header_type_chg_ind;
	}

	public void setCanonical_header_type_chg_ind(Boolean canonical_header_type_chg_ind) {
		this.canonical_header_type_chg_ind = canonical_header_type_chg_ind;
	}

	public Boolean getCanonical_removed_ind() {
		return canonical_removed_ind;
	}

	public void setCanonical_removed_ind(Boolean canonical_removed_ind) {
		this.canonical_removed_ind = canonical_removed_ind;
	}

	public Boolean getCanonical_type_chg_ind() {
		return canonical_type_chg_ind;
	}

	public void setCanonical_type_chg_ind(Boolean canonical_type_chg_ind) {
		this.canonical_type_chg_ind = canonical_type_chg_ind;
	}

	public Boolean getCanonical_url_is_consistent_chg_ind() {
		return canonical_url_is_consistent_chg_ind;
	}

	public void setCanonical_url_is_consistent_chg_ind(Boolean canonical_url_is_consistent_chg_ind) {
		this.canonical_url_is_consistent_chg_ind = canonical_url_is_consistent_chg_ind;
	}

	public Boolean getContent_type_chg_ind() {
		return content_type_chg_ind;
	}

	public void setContent_type_chg_ind(Boolean content_type_chg_ind) {
		this.content_type_chg_ind = content_type_chg_ind;
	}

	public Boolean getCustom_data_added_ind() {
		return custom_data_added_ind;
	}

	public void setCustom_data_added_ind(Boolean custom_data_added_ind) {
		this.custom_data_added_ind = custom_data_added_ind;
	}

	public Boolean getCustom_data_chg_ind() {
		return custom_data_chg_ind;
	}

	public void setCustom_data_chg_ind(Boolean custom_data_chg_ind) {
		this.custom_data_chg_ind = custom_data_chg_ind;
	}

	public Boolean getCustom_data_removed_ind() {
		return custom_data_removed_ind;
	}

	public void setCustom_data_removed_ind(Boolean custom_data_removed_ind) {
		this.custom_data_removed_ind = custom_data_removed_ind;
	}

	public Boolean getDescription_added_ind() {
		return description_added_ind;
	}

	public void setDescription_added_ind(Boolean description_added_ind) {
		this.description_added_ind = description_added_ind;
	}

	public Boolean getDescription_chg_ind() {
		return description_chg_ind;
	}

	public void setDescription_chg_ind(Boolean description_chg_ind) {
		this.description_chg_ind = description_chg_ind;
	}

	public Boolean getDescription_length_chg_ind() {
		return description_length_chg_ind;
	}

	public void setDescription_length_chg_ind(Boolean description_length_chg_ind) {
		this.description_length_chg_ind = description_length_chg_ind;
	}

	public Boolean getDescription_removed_ind() {
		return description_removed_ind;
	}

	public void setDescription_removed_ind(Boolean description_removed_ind) {
		this.description_removed_ind = description_removed_ind;
	}

	public Boolean getError_message_chg_ind() {
		return error_message_chg_ind;
	}

	public void setError_message_chg_ind(Boolean error_message_chg_ind) {
		this.error_message_chg_ind = error_message_chg_ind;
	}

	public Boolean getFinal_response_code_chg_ind() {
		return final_response_code_chg_ind;
	}

	public void setFinal_response_code_chg_ind(Boolean final_response_code_chg_ind) {
		this.final_response_code_chg_ind = final_response_code_chg_ind;
	}

	public Boolean getFollow_flg_chg_ind() {
		return follow_flg_chg_ind;
	}

	public void setFollow_flg_chg_ind(Boolean follow_flg_chg_ind) {
		this.follow_flg_chg_ind = follow_flg_chg_ind;
	}

	public Boolean getH1_added_ind() {
		return h1_added_ind;
	}

	public void setH1_added_ind(Boolean h1_added_ind) {
		this.h1_added_ind = h1_added_ind;
	}

	public Boolean getH1_chg_ind() {
		return h1_chg_ind;
	}

	public void setH1_chg_ind(Boolean h1_chg_ind) {
		this.h1_chg_ind = h1_chg_ind;
	}

	public Boolean getH1_count_chg_ind() {
		return h1_count_chg_ind;
	}

	public void setH1_count_chg_ind(Boolean h1_count_chg_ind) {
		this.h1_count_chg_ind = h1_count_chg_ind;
	}

	public Boolean getH1_length_chg_ind() {
		return h1_length_chg_ind;
	}

	public void setH1_length_chg_ind(Boolean h1_length_chg_ind) {
		this.h1_length_chg_ind = h1_length_chg_ind;
	}

	public Boolean getH1_removed_ind() {
		return h1_removed_ind;
	}

	public void setH1_removed_ind(Boolean h1_removed_ind) {
		this.h1_removed_ind = h1_removed_ind;
	}

	public Boolean getH2_added_ind() {
		return h2_added_ind;
	}

	public void setH2_added_ind(Boolean h2_added_ind) {
		this.h2_added_ind = h2_added_ind;
	}

	public Boolean getH2_chg_ind() {
		return h2_chg_ind;
	}

	public void setH2_chg_ind(Boolean h2_chg_ind) {
		this.h2_chg_ind = h2_chg_ind;
	}

	public Boolean getH2_removed_ind() {
		return h2_removed_ind;
	}

	public void setH2_removed_ind(Boolean h2_removed_ind) {
		this.h2_removed_ind = h2_removed_ind;
	}

	public Boolean getHeader_noarchive_chg_ind() {
		return header_noarchive_chg_ind;
	}

	public void setHeader_noarchive_chg_ind(Boolean header_noarchive_chg_ind) {
		this.header_noarchive_chg_ind = header_noarchive_chg_ind;
	}

	public Boolean getHeader_nofollow_chg_ind() {
		return header_nofollow_chg_ind;
	}

	public void setHeader_nofollow_chg_ind(Boolean header_nofollow_chg_ind) {
		this.header_nofollow_chg_ind = header_nofollow_chg_ind;
	}

	public Boolean getHeader_noindex_chg_ind() {
		return header_noindex_chg_ind;
	}

	public void setHeader_noindex_chg_ind(Boolean header_noindex_chg_ind) {
		this.header_noindex_chg_ind = header_noindex_chg_ind;
	}

	public Boolean getHeader_noodp_chg_ind() {
		return header_noodp_chg_ind;
	}

	public void setHeader_noodp_chg_ind(Boolean header_noodp_chg_ind) {
		this.header_noodp_chg_ind = header_noodp_chg_ind;
	}

	public Boolean getHeader_nosnippet_chg_ind() {
		return header_nosnippet_chg_ind;
	}

	public void setHeader_nosnippet_chg_ind(Boolean header_nosnippet_chg_ind) {
		this.header_nosnippet_chg_ind = header_nosnippet_chg_ind;
	}

	public Boolean getHeader_noydir_chg_ind() {
		return header_noydir_chg_ind;
	}

	public void setHeader_noydir_chg_ind(Boolean header_noydir_chg_ind) {
		this.header_noydir_chg_ind = header_noydir_chg_ind;
	}

	public Boolean getHreflang_errors_chg_ind() {
		return hreflang_errors_chg_ind;
	}

	public void setHreflang_errors_chg_ind(Boolean hreflang_errors_chg_ind) {
		this.hreflang_errors_chg_ind = hreflang_errors_chg_ind;
	}

	public Boolean getHreflang_links_added_ind() {
		return hreflang_links_added_ind;
	}

	public void setHreflang_links_added_ind(Boolean hreflang_links_added_ind) {
		this.hreflang_links_added_ind = hreflang_links_added_ind;
	}

	public Boolean getHreflang_links_chg_ind() {
		return hreflang_links_chg_ind;
	}

	public void setHreflang_links_chg_ind(Boolean hreflang_links_chg_ind) {
		this.hreflang_links_chg_ind = hreflang_links_chg_ind;
	}

	public Boolean getHreflang_links_out_count_chg_ind() {
		return hreflang_links_out_count_chg_ind;
	}

	public void setHreflang_links_out_count_chg_ind(Boolean hreflang_links_out_count_chg_ind) {
		this.hreflang_links_out_count_chg_ind = hreflang_links_out_count_chg_ind;
	}

	public Boolean getHreflang_links_removed_ind() {
		return hreflang_links_removed_ind;
	}

	public void setHreflang_links_removed_ind(Boolean hreflang_links_removed_ind) {
		this.hreflang_links_removed_ind = hreflang_links_removed_ind;
	}

	public Boolean getHreflang_url_count_chg_ind() {
		return hreflang_url_count_chg_ind;
	}

	public void setHreflang_url_count_chg_ind(Boolean hreflang_url_count_chg_ind) {
		this.hreflang_url_count_chg_ind = hreflang_url_count_chg_ind;
	}

	public Boolean getIndexable_chg_ind() {
		return indexable_chg_ind;
	}

	public void setIndexable_chg_ind(Boolean indexable_chg_ind) {
		this.indexable_chg_ind = indexable_chg_ind;
	}

	public Boolean getIndex_flg_chg_ind() {
		return index_flg_chg_ind;
	}

	public void setIndex_flg_chg_ind(Boolean index_flg_chg_ind) {
		this.index_flg_chg_ind = index_flg_chg_ind;
	}

	public Boolean getInsecure_resources_chg_ind() {
		return insecure_resources_chg_ind;
	}

	public void setInsecure_resources_chg_ind(Boolean insecure_resources_chg_ind) {
		this.insecure_resources_chg_ind = insecure_resources_chg_ind;
	}

	public Boolean getMeta_charset_chg_ind() {
		return meta_charset_chg_ind;
	}

	public void setMeta_charset_chg_ind(Boolean meta_charset_chg_ind) {
		this.meta_charset_chg_ind = meta_charset_chg_ind;
	}

	public Boolean getMeta_content_type_chg_ind() {
		return meta_content_type_chg_ind;
	}

	public void setMeta_content_type_chg_ind(Boolean meta_content_type_chg_ind) {
		this.meta_content_type_chg_ind = meta_content_type_chg_ind;
	}

	public Boolean getMeta_disabled_sitelinks_chg_ind() {
		return meta_disabled_sitelinks_chg_ind;
	}

	public void setMeta_disabled_sitelinks_chg_ind(Boolean meta_disabled_sitelinks_chg_ind) {
		this.meta_disabled_sitelinks_chg_ind = meta_disabled_sitelinks_chg_ind;
	}

	public Boolean getMeta_noodp_chg_ind() {
		return meta_noodp_chg_ind;
	}

	public void setMeta_noodp_chg_ind(Boolean meta_noodp_chg_ind) {
		this.meta_noodp_chg_ind = meta_noodp_chg_ind;
	}

	public Boolean getMeta_nosnippet_chg_ind() {
		return meta_nosnippet_chg_ind;
	}

	public void setMeta_nosnippet_chg_ind(Boolean meta_nosnippet_chg_ind) {
		this.meta_nosnippet_chg_ind = meta_nosnippet_chg_ind;
	}

	public Boolean getMeta_noydir_chg_ind() {
		return meta_noydir_chg_ind;
	}

	public void setMeta_noydir_chg_ind(Boolean meta_noydir_chg_ind) {
		this.meta_noydir_chg_ind = meta_noydir_chg_ind;
	}

	public Boolean getMeta_redirect_chg_ind() {
		return meta_redirect_chg_ind;
	}

	public void setMeta_redirect_chg_ind(Boolean meta_redirect_chg_ind) {
		this.meta_redirect_chg_ind = meta_redirect_chg_ind;
	}

	public Boolean getMixed_redirects_chg_ind() {
		return mixed_redirects_chg_ind;
	}

	public void setMixed_redirects_chg_ind(Boolean mixed_redirects_chg_ind) {
		this.mixed_redirects_chg_ind = mixed_redirects_chg_ind;
	}

	public Boolean getMobile_rel_alternate_url_is_consistent_chg_ind() {
		return mobile_rel_alternate_url_is_consistent_chg_ind;
	}

	public void setMobile_rel_alternate_url_is_consistent_chg_ind(Boolean mobile_rel_alternate_url_is_consistent_chg_ind) {
		this.mobile_rel_alternate_url_is_consistent_chg_ind = mobile_rel_alternate_url_is_consistent_chg_ind;
	}

	public Boolean getNoodp_chg_ind() {
		return noodp_chg_ind;
	}

	public void setNoodp_chg_ind(Boolean noodp_chg_ind) {
		this.noodp_chg_ind = noodp_chg_ind;
	}

	public Boolean getNosnippet_chg_ind() {
		return nosnippet_chg_ind;
	}

	public void setNosnippet_chg_ind(Boolean nosnippet_chg_ind) {
		this.nosnippet_chg_ind = nosnippet_chg_ind;
	}

	public Boolean getNoydir_chg_ind() {
		return noydir_chg_ind;
	}

	public void setNoydir_chg_ind(Boolean noydir_chg_ind) {
		this.noydir_chg_ind = noydir_chg_ind;
	}

	public Boolean getOg_markup_chg_ind() {
		return og_markup_chg_ind;
	}

	public void setOg_markup_chg_ind(Boolean og_markup_chg_ind) {
		this.og_markup_chg_ind = og_markup_chg_ind;
	}

	public Boolean getOg_markup_length_chg_ind() {
		return og_markup_length_chg_ind;
	}

	public void setOg_markup_length_chg_ind(Boolean og_markup_length_chg_ind) {
		this.og_markup_length_chg_ind = og_markup_length_chg_ind;
	}

	public Boolean getOpen_graph_added_ind() {
		return open_graph_added_ind;
	}

	public void setOpen_graph_added_ind(Boolean open_graph_added_ind) {
		this.open_graph_added_ind = open_graph_added_ind;
	}

	public Boolean getOpen_graph_removed_ind() {
		return open_graph_removed_ind;
	}

	public void setOpen_graph_removed_ind(Boolean open_graph_removed_ind) {
		this.open_graph_removed_ind = open_graph_removed_ind;
	}

	public Boolean getOutlink_count_chg_ind() {
		return outlink_count_chg_ind;
	}

	public void setOutlink_count_chg_ind(Boolean outlink_count_chg_ind) {
		this.outlink_count_chg_ind = outlink_count_chg_ind;
	}

	public Boolean getPage_analysis_fragments_chg_ind() {
		return page_analysis_fragments_chg_ind;
	}

	public void setPage_analysis_fragments_chg_ind(Boolean page_analysis_fragments_chg_ind) {
		this.page_analysis_fragments_chg_ind = page_analysis_fragments_chg_ind;
	}

	public Boolean getPage_link_chg_ind() {
		return page_link_chg_ind;
	}

	public void setPage_link_chg_ind(Boolean page_link_chg_ind) {
		this.page_link_chg_ind = page_link_chg_ind;
	}

	public Boolean getRedirect_301_detected_ind() {
		return redirect_301_detected_ind;
	}

	public void setRedirect_301_detected_ind(Boolean redirect_301_detected_ind) {
		this.redirect_301_detected_ind = redirect_301_detected_ind;
	}

	public Boolean getRedirect_301_removed_ind() {
		return redirect_301_removed_ind;
	}

	public void setRedirect_301_removed_ind(Boolean redirect_301_removed_ind) {
		this.redirect_301_removed_ind = redirect_301_removed_ind;
	}

	public Boolean getRedirect_302_detected_ind() {
		return redirect_302_detected_ind;
	}

	public void setRedirect_302_detected_ind(Boolean redirect_302_detected_ind) {
		this.redirect_302_detected_ind = redirect_302_detected_ind;
	}

	public Boolean getRedirect_302_removed_ind() {
		return redirect_302_removed_ind;
	}

	public void setRedirect_302_removed_ind(Boolean redirect_302_removed_ind) {
		this.redirect_302_removed_ind = redirect_302_removed_ind;
	}

	public Boolean getRedirect_blocked_chg_ind() {
		return redirect_blocked_chg_ind;
	}

	public void setRedirect_blocked_chg_ind(Boolean redirect_blocked_chg_ind) {
		this.redirect_blocked_chg_ind = redirect_blocked_chg_ind;
	}

	public Boolean getRedirect_blocked_reason_chg_ind() {
		return redirect_blocked_reason_chg_ind;
	}

	public void setRedirect_blocked_reason_chg_ind(Boolean redirect_blocked_reason_chg_ind) {
		this.redirect_blocked_reason_chg_ind = redirect_blocked_reason_chg_ind;
	}

	public Boolean getRedirect_chain_chg_ind() {
		return redirect_chain_chg_ind;
	}

	public void setRedirect_chain_chg_ind(Boolean redirect_chain_chg_ind) {
		this.redirect_chain_chg_ind = redirect_chain_chg_ind;
	}

	public Boolean getRedirect_diff_code_ind() {
		return redirect_diff_code_ind;
	}

	public void setRedirect_diff_code_ind(Boolean redirect_diff_code_ind) {
		this.redirect_diff_code_ind = redirect_diff_code_ind;
	}

	public Boolean getRedirect_final_url_chg_ind() {
		return redirect_final_url_chg_ind;
	}

	public void setRedirect_final_url_chg_ind(Boolean redirect_final_url_chg_ind) {
		this.redirect_final_url_chg_ind = redirect_final_url_chg_ind;
	}

	public Boolean getRedirect_times_chg_ind() {
		return redirect_times_chg_ind;
	}

	public void setRedirect_times_chg_ind(Boolean redirect_times_chg_ind) {
		this.redirect_times_chg_ind = redirect_times_chg_ind;
	}

	public Boolean getResponse_code_chg_ind() {
		return response_code_chg_ind;
	}

	public void setResponse_code_chg_ind(Boolean response_code_chg_ind) {
		this.response_code_chg_ind = response_code_chg_ind;
	}

	public Boolean getResponse_headers_added_ind() {
		return response_headers_added_ind;
	}

	public void setResponse_headers_added_ind(Boolean response_headers_added_ind) {
		this.response_headers_added_ind = response_headers_added_ind;
	}

	public Boolean getResponse_headers_removed_ind() {
		return response_headers_removed_ind;
	}

	public void setResponse_headers_removed_ind(Boolean response_headers_removed_ind) {
		this.response_headers_removed_ind = response_headers_removed_ind;
	}

	public Boolean getRobots_added_ind() {
		return robots_added_ind;
	}

	public void setRobots_added_ind(Boolean robots_added_ind) {
		this.robots_added_ind = robots_added_ind;
	}

	public Boolean getRobots_chg_ind() {
		return robots_chg_ind;
	}

	public void setRobots_chg_ind(Boolean robots_chg_ind) {
		this.robots_chg_ind = robots_chg_ind;
	}

	public Boolean getRobots_contents_chg_ind() {
		return robots_contents_chg_ind;
	}

	public void setRobots_contents_chg_ind(Boolean robots_contents_chg_ind) {
		this.robots_contents_chg_ind = robots_contents_chg_ind;
	}

	public Boolean getRobots_removed_ind() {
		return robots_removed_ind;
	}

	public void setRobots_removed_ind(Boolean robots_removed_ind) {
		this.robots_removed_ind = robots_removed_ind;
	}

	public Boolean getStructured_data_chg_ind() {
		return structured_data_chg_ind;
	}

	public void setStructured_data_chg_ind(Boolean structured_data_chg_ind) {
		this.structured_data_chg_ind = structured_data_chg_ind;
	}

	public Boolean getTitle_added_ind() {
		return title_added_ind;
	}

	public void setTitle_added_ind(Boolean title_added_ind) {
		this.title_added_ind = title_added_ind;
	}

	public Boolean getTitle_chg_ind() {
		return title_chg_ind;
	}

	public void setTitle_chg_ind(Boolean title_chg_ind) {
		this.title_chg_ind = title_chg_ind;
	}

	public Boolean getTitle_length_chg_ind() {
		return title_length_chg_ind;
	}

	public void setTitle_length_chg_ind(Boolean title_length_chg_ind) {
		this.title_length_chg_ind = title_length_chg_ind;
	}

	public Boolean getTitle_removed_ind() {
		return title_removed_ind;
	}

	public void setTitle_removed_ind(Boolean title_removed_ind) {
		this.title_removed_ind = title_removed_ind;
	}

	public Boolean getViewport_added_ind() {
		return viewport_added_ind;
	}

	public void setViewport_added_ind(Boolean viewport_added_ind) {
		this.viewport_added_ind = viewport_added_ind;
	}

	public Boolean getViewport_content_chg_ind() {
		return viewport_content_chg_ind;
	}

	public void setViewport_content_chg_ind(Boolean viewport_content_chg_ind) {
		this.viewport_content_chg_ind = viewport_content_chg_ind;
	}

	public Boolean getViewport_removed_ind() {
		return viewport_removed_ind;
	}

	public void setViewport_removed_ind(Boolean viewport_removed_ind) {
		this.viewport_removed_ind = viewport_removed_ind;
	}

	public String getPage_analysis_results_chg_ind_json() {
		return page_analysis_results_chg_ind_json;
	}

	public void setPage_analysis_results_chg_ind_json(String page_analysis_results_chg_ind_json) {
		this.page_analysis_results_chg_ind_json = page_analysis_results_chg_ind_json;
	}

	public AlternateLinks[] getAlternate_links_1() {
		return alternate_links_1;
	}

	public void setAlternate_links_1(AlternateLinks[] alternate_links_1) {
		this.alternate_links_1 = alternate_links_1;
	}

	public String getAmphtml_href_1() {
		return amphtml_href_1;
	}

	public void setAmphtml_href_1(String amphtml_href_1) {
		this.amphtml_href_1 = amphtml_href_1;
	}

	public String getAnalyzed_url_s_1() {
		return analyzed_url_s_1;
	}

	public void setAnalyzed_url_s_1(String analyzed_url_s_1) {
		this.analyzed_url_s_1 = analyzed_url_s_1;
	}

	public String getArchive_flg_1() {
		return archive_flg_1;
	}

	public void setArchive_flg_1(String archive_flg_1) {
		this.archive_flg_1 = archive_flg_1;
	}

	public String getBase_tag_1() {
		return base_tag_1;
	}

	public void setBase_tag_1(String base_tag_1) {
		this.base_tag_1 = base_tag_1;
	}

	public Boolean getBase_tag_flag_1() {
		return base_tag_flag_1;
	}

	public void setBase_tag_flag_1(Boolean base_tag_flag_1) {
		this.base_tag_flag_1 = base_tag_flag_1;
	}

	public String getBase_tag_target_1() {
		return base_tag_target_1;
	}

	public void setBase_tag_target_1(String base_tag_target_1) {
		this.base_tag_target_1 = base_tag_target_1;
	}

	public String getBlocked_by_robots_1() {
		return blocked_by_robots_1;
	}

	public void setBlocked_by_robots_1(String blocked_by_robots_1) {
		this.blocked_by_robots_1 = blocked_by_robots_1;
	}

	public String getCanonical_1() {
		return canonical_1;
	}

	public void setCanonical_1(String canonical_1) {
		this.canonical_1 = canonical_1;
	}

	public String getCanonical_flg_1() {
		return canonical_flg_1;
	}

	public void setCanonical_flg_1(String canonical_flg_1) {
		this.canonical_flg_1 = canonical_flg_1;
	}

	public Boolean getCanonical_header_flag_1() {
		return canonical_header_flag_1;
	}

	public void setCanonical_header_flag_1(Boolean canonical_header_flag_1) {
		this.canonical_header_flag_1 = canonical_header_flag_1;
	}

	public String getCanonical_header_type_1() {
		return canonical_header_type_1;
	}

	public void setCanonical_header_type_1(String canonical_header_type_1) {
		this.canonical_header_type_1 = canonical_header_type_1;
	}

	public String getCanonical_type_1() {
		return canonical_type_1;
	}

	public void setCanonical_type_1(String canonical_type_1) {
		this.canonical_type_1 = canonical_type_1;
	}

	public String getCanonical_url_is_consistent_1() {
		return canonical_url_is_consistent_1;
	}

	public void setCanonical_url_is_consistent_1(String canonical_url_is_consistent_1) {
		this.canonical_url_is_consistent_1 = canonical_url_is_consistent_1;
	}

	public String getContent_type_1() {
		return content_type_1;
	}

	public void setContent_type_1(String content_type_1) {
		this.content_type_1 = content_type_1;
	}

	public CustomData[] getCustom_data_1() {
		return custom_data_1;
	}

	public void setCustom_data_1(CustomData[] custom_data_1) {
		this.custom_data_1 = custom_data_1;
	}

	public String getDescription_1() {
		return description_1;
	}

	public void setDescription_1(String description_1) {
		this.description_1 = description_1;
	}

	public String getDescription_flg_1() {
		return description_flg_1;
	}

	public void setDescription_flg_1(String description_flg_1) {
		this.description_flg_1 = description_flg_1;
	}

	public Integer getDescription_length_1() {
		return description_length_1;
	}

	public void setDescription_length_1(Integer description_length_1) {
		this.description_length_1 = description_length_1;
	}

	public String getError_message_1() {
		return error_message_1;
	}

	public void setError_message_1(String error_message_1) {
		this.error_message_1 = error_message_1;
	}

	public Integer getFinal_response_code_1() {
		return final_response_code_1;
	}

	public void setFinal_response_code_1(Integer final_response_code_1) {
		this.final_response_code_1 = final_response_code_1;
	}

	public String getFollow_flg_1() {
		return follow_flg_1;
	}

	public void setFollow_flg_1(String follow_flg_1) {
		this.follow_flg_1 = follow_flg_1;
	}

	public String[] getH1_1() {
		return h1_1;
	}

	public void setH1_1(String[] h1_1) {
		this.h1_1 = h1_1;
	}

	public Integer getH1_count_1() {
		return h1_count_1;
	}

	public void setH1_count_1(Integer h1_count_1) {
		this.h1_count_1 = h1_count_1;
	}

	public Integer getH1_length_1() {
		return h1_length_1;
	}

	public void setH1_length_1(Integer h1_length_1) {
		this.h1_length_1 = h1_length_1;
	}

	public String[] getH2_1() {
		return h2_1;
	}

	public void setH2_1(String[] h2_1) {
		this.h2_1 = h2_1;
	}

	public Boolean getHeader_noarchive_1() {
		return header_noarchive_1;
	}

	public void setHeader_noarchive_1(Boolean header_noarchive_1) {
		this.header_noarchive_1 = header_noarchive_1;
	}

	public Boolean getHeader_nofollow_1() {
		return header_nofollow_1;
	}

	public void setHeader_nofollow_1(Boolean header_nofollow_1) {
		this.header_nofollow_1 = header_nofollow_1;
	}

	public Boolean getHeader_noindex_1() {
		return header_noindex_1;
	}

	public void setHeader_noindex_1(Boolean header_noindex_1) {
		this.header_noindex_1 = header_noindex_1;
	}

	public Boolean getHeader_noodp_1() {
		return header_noodp_1;
	}

	public void setHeader_noodp_1(Boolean header_noodp_1) {
		this.header_noodp_1 = header_noodp_1;
	}

	public Boolean getHeader_nosnippet_1() {
		return header_nosnippet_1;
	}

	public void setHeader_nosnippet_1(Boolean header_nosnippet_1) {
		this.header_nosnippet_1 = header_nosnippet_1;
	}

	public Boolean getHeader_noydir_1() {
		return header_noydir_1;
	}

	public void setHeader_noydir_1(Boolean header_noydir_1) {
		this.header_noydir_1 = header_noydir_1;
	}

	public HreflangErrors getHreflang_errors_1() {
		return hreflang_errors_1;
	}

	public void setHreflang_errors_1(HreflangErrors hreflang_errors_1) {
		this.hreflang_errors_1 = hreflang_errors_1;
	}

	public HreflangLinks[] getHreflang_links_1() {
		return hreflang_links_1;
	}

	public void setHreflang_links_1(HreflangLinks[] hreflang_links_1) {
		this.hreflang_links_1 = hreflang_links_1;
	}

	public Integer getHreflang_links_out_count_1() {
		return hreflang_links_out_count_1;
	}

	public void setHreflang_links_out_count_1(Integer hreflang_links_out_count_1) {
		this.hreflang_links_out_count_1 = hreflang_links_out_count_1;
	}

	public Integer getHreflang_url_count_1() {
		return hreflang_url_count_1;
	}

	public void setHreflang_url_count_1(Integer hreflang_url_count_1) {
		this.hreflang_url_count_1 = hreflang_url_count_1;
	}

	public String getIndex_flg_1() {
		return index_flg_1;
	}

	public void setIndex_flg_1(String index_flg_1) {
		this.index_flg_1 = index_flg_1;
	}

	public Boolean getIndexable_1() {
		return indexable_1;
	}

	public void setIndexable_1(Boolean indexable_1) {
		this.indexable_1 = indexable_1;
	}

	public String[] getInsecure_resources_1() {
		return insecure_resources_1;
	}

	public void setInsecure_resources_1(String[] insecure_resources_1) {
		this.insecure_resources_1 = insecure_resources_1;
	}

	public String getMeta_charset_1() {
		return meta_charset_1;
	}

	public void setMeta_charset_1(String meta_charset_1) {
		this.meta_charset_1 = meta_charset_1;
	}

	public String getMeta_content_type_1() {
		return meta_content_type_1;
	}

	public void setMeta_content_type_1(String meta_content_type_1) {
		this.meta_content_type_1 = meta_content_type_1;
	}

	public Boolean getMeta_disabled_sitelinks_1() {
		return meta_disabled_sitelinks_1;
	}

	public void setMeta_disabled_sitelinks_1(Boolean meta_disabled_sitelinks_1) {
		this.meta_disabled_sitelinks_1 = meta_disabled_sitelinks_1;
	}

	public Boolean getMeta_noodp_1() {
		return meta_noodp_1;
	}

	public void setMeta_noodp_1(Boolean meta_noodp_1) {
		this.meta_noodp_1 = meta_noodp_1;
	}

	public Boolean getMeta_nosnippet_1() {
		return meta_nosnippet_1;
	}

	public void setMeta_nosnippet_1(Boolean meta_nosnippet_1) {
		this.meta_nosnippet_1 = meta_nosnippet_1;
	}

	public Boolean getMeta_noydir_1() {
		return meta_noydir_1;
	}

	public void setMeta_noydir_1(Boolean meta_noydir_1) {
		this.meta_noydir_1 = meta_noydir_1;
	}

	public Boolean getMeta_redirect_1() {
		return meta_redirect_1;
	}

	public void setMeta_redirect_1(Boolean meta_redirect_1) {
		this.meta_redirect_1 = meta_redirect_1;
	}

	public Boolean getMixed_redirects_1() {
		return mixed_redirects_1;
	}

	public void setMixed_redirects_1(Boolean mixed_redirects_1) {
		this.mixed_redirects_1 = mixed_redirects_1;
	}

	public Boolean getMobile_rel_alternate_url_is_consistent_1() {
		return mobile_rel_alternate_url_is_consistent_1;
	}

	public void setMobile_rel_alternate_url_is_consistent_1(Boolean mobile_rel_alternate_url_is_consistent_1) {
		this.mobile_rel_alternate_url_is_consistent_1 = mobile_rel_alternate_url_is_consistent_1;
	}

	public Boolean getNoodp_1() {
		return noodp_1;
	}

	public void setNoodp_1(Boolean noodp_1) {
		this.noodp_1 = noodp_1;
	}

	public Boolean getNosnippet_1() {
		return nosnippet_1;
	}

	public void setNosnippet_1(Boolean nosnippet_1) {
		this.nosnippet_1 = nosnippet_1;
	}

	public Boolean getNoydir_1() {
		return noydir_1;
	}

	public void setNoydir_1(Boolean noydir_1) {
		this.noydir_1 = noydir_1;
	}

	public OgMarkup[] getOg_markup_1() {
		return og_markup_1;
	}

	public void setOg_markup_1(OgMarkup[] og_markup_1) {
		this.og_markup_1 = og_markup_1;
	}

	public Boolean getOg_markup_flag_1() {
		return og_markup_flag_1;
	}

	public void setOg_markup_flag_1(Boolean og_markup_flag_1) {
		this.og_markup_flag_1 = og_markup_flag_1;
	}

	public Integer getOg_markup_length_1() {
		return og_markup_length_1;
	}

	public void setOg_markup_length_1(Integer og_markup_length_1) {
		this.og_markup_length_1 = og_markup_length_1;
	}

	public Integer getOutlink_count_1() {
		return outlink_count_1;
	}

	public void setOutlink_count_1(Integer outlink_count_1) {
		this.outlink_count_1 = outlink_count_1;
	}

	public PageAnalysisFragments[] getPage_analysis_fragments_1() {
		return page_analysis_fragments_1;
	}

	public void setPage_analysis_fragments_1(PageAnalysisFragments[] page_analysis_fragments_1) {
		this.page_analysis_fragments_1 = page_analysis_fragments_1;
	}

	public PageLink[] getPage_link_1() {
		return page_link_1;
	}

	public void setPage_link_1(PageLink[] page_link_1) {
		this.page_link_1 = page_link_1;
	}

	public Boolean getRedirect_blocked_1() {
		return redirect_blocked_1;
	}

	public void setRedirect_blocked_1(Boolean redirect_blocked_1) {
		this.redirect_blocked_1 = redirect_blocked_1;
	}

	public String getRedirect_blocked_reason_1() {
		return redirect_blocked_reason_1;
	}

	public void setRedirect_blocked_reason_1(String redirect_blocked_reason_1) {
		this.redirect_blocked_reason_1 = redirect_blocked_reason_1;
	}

	public RedirectChain[] getRedirect_chain_1() {
		return redirect_chain_1;
	}

	public void setRedirect_chain_1(RedirectChain[] redirect_chain_1) {
		this.redirect_chain_1 = redirect_chain_1;
	}

	public String getRedirect_final_url_1() {
		return redirect_final_url_1;
	}

	public void setRedirect_final_url_1(String redirect_final_url_1) {
		this.redirect_final_url_1 = redirect_final_url_1;
	}

	public Integer getRedirect_times_1() {
		return redirect_times_1;
	}

	public void setRedirect_times_1(Integer redirect_times_1) {
		this.redirect_times_1 = redirect_times_1;
	}

	public String getResponse_code_1() {
		return response_code_1;
	}

	public void setResponse_code_1(String response_code_1) {
		this.response_code_1 = response_code_1;
	}

	public String[] getResponse_header_names_1() {
		return response_header_names_1;
	}

	public void setResponse_header_names_1(String[] response_header_names_1) {
		this.response_header_names_1 = response_header_names_1;
	}

	public String getRobots_1() {
		return robots_1;
	}

	public void setRobots_1(String robots_1) {
		this.robots_1 = robots_1;
	}

	public String getRobots_contents_1() {
		return robots_contents_1;
	}

	public void setRobots_contents_1(String robots_contents_1) {
		this.robots_contents_1 = robots_contents_1;
	}

	public String getRobots_flg_1() {
		return robots_flg_1;
	}

	public void setRobots_flg_1(String robots_flg_1) {
		this.robots_flg_1 = robots_flg_1;
	}

	public StructuredData getStructured_data_1() {
		return structured_data_1;
	}

	public void setStructured_data_1(StructuredData structured_data_1) {
		this.structured_data_1 = structured_data_1;
	}

	public String getTitle_1() {
		return title_1;
	}

	public void setTitle_1(String title_1) {
		this.title_1 = title_1;
	}

	public String getTitle_flg_1() {
		return title_flg_1;
	}

	public void setTitle_flg_1(String title_flg_1) {
		this.title_flg_1 = title_flg_1;
	}

	public Integer getTitle_length_1() {
		return title_length_1;
	}

	public void setTitle_length_1(Integer title_length_1) {
		this.title_length_1 = title_length_1;
	}

	public String getViewport_content_1() {
		return viewport_content_1;
	}

	public void setViewport_content_1(String viewport_content_1) {
		this.viewport_content_1 = viewport_content_1;
	}

	public Boolean getViewport_flag_1() {
		return viewport_flag_1;
	}

	public void setViewport_flag_1(Boolean viewport_flag_1) {
		this.viewport_flag_1 = viewport_flag_1;
	}

	public AlternateLinks[] getAlternate_links_2() {
		return alternate_links_2;
	}

	public void setAlternate_links_2(AlternateLinks[] alternate_links_2) {
		this.alternate_links_2 = alternate_links_2;
	}

	public String getAmphtml_href_2() {
		return amphtml_href_2;
	}

	public void setAmphtml_href_2(String amphtml_href_2) {
		this.amphtml_href_2 = amphtml_href_2;
	}

	public String getAnalyzed_url_s_2() {
		return analyzed_url_s_2;
	}

	public void setAnalyzed_url_s_2(String analyzed_url_s_2) {
		this.analyzed_url_s_2 = analyzed_url_s_2;
	}

	public String getArchive_flg_2() {
		return archive_flg_2;
	}

	public void setArchive_flg_2(String archive_flg_2) {
		this.archive_flg_2 = archive_flg_2;
	}

	public String getBase_tag_2() {
		return base_tag_2;
	}

	public void setBase_tag_2(String base_tag_2) {
		this.base_tag_2 = base_tag_2;
	}

	public Boolean getBase_tag_flag_2() {
		return base_tag_flag_2;
	}

	public void setBase_tag_flag_2(Boolean base_tag_flag_2) {
		this.base_tag_flag_2 = base_tag_flag_2;
	}

	public String getBase_tag_target_2() {
		return base_tag_target_2;
	}

	public void setBase_tag_target_2(String base_tag_target_2) {
		this.base_tag_target_2 = base_tag_target_2;
	}

	public String getBlocked_by_robots_2() {
		return blocked_by_robots_2;
	}

	public void setBlocked_by_robots_2(String blocked_by_robots_2) {
		this.blocked_by_robots_2 = blocked_by_robots_2;
	}

	public String getCanonical_2() {
		return canonical_2;
	}

	public void setCanonical_2(String canonical_2) {
		this.canonical_2 = canonical_2;
	}

	public String getCanonical_flg_2() {
		return canonical_flg_2;
	}

	public void setCanonical_flg_2(String canonical_flg_2) {
		this.canonical_flg_2 = canonical_flg_2;
	}

	public Boolean getCanonical_header_flag_2() {
		return canonical_header_flag_2;
	}

	public void setCanonical_header_flag_2(Boolean canonical_header_flag_2) {
		this.canonical_header_flag_2 = canonical_header_flag_2;
	}

	public String getCanonical_header_type_2() {
		return canonical_header_type_2;
	}

	public void setCanonical_header_type_2(String canonical_header_type_2) {
		this.canonical_header_type_2 = canonical_header_type_2;
	}

	public String getCanonical_type_2() {
		return canonical_type_2;
	}

	public void setCanonical_type_2(String canonical_type_2) {
		this.canonical_type_2 = canonical_type_2;
	}

	public String getCanonical_url_is_consistent_2() {
		return canonical_url_is_consistent_2;
	}

	public void setCanonical_url_is_consistent_2(String canonical_url_is_consistent_2) {
		this.canonical_url_is_consistent_2 = canonical_url_is_consistent_2;
	}

	public String getContent_type_2() {
		return content_type_2;
	}

	public void setContent_type_2(String content_type_2) {
		this.content_type_2 = content_type_2;
	}

	public CustomData[] getCustom_data_2() {
		return custom_data_2;
	}

	public void setCustom_data_2(CustomData[] custom_data_2) {
		this.custom_data_2 = custom_data_2;
	}

	public String getDescription_2() {
		return description_2;
	}

	public void setDescription_2(String description_2) {
		this.description_2 = description_2;
	}

	public String getDescription_flg_2() {
		return description_flg_2;
	}

	public void setDescription_flg_2(String description_flg_2) {
		this.description_flg_2 = description_flg_2;
	}

	public Integer getDescription_length_2() {
		return description_length_2;
	}

	public void setDescription_length_2(Integer description_length_2) {
		this.description_length_2 = description_length_2;
	}

	public String getError_message_2() {
		return error_message_2;
	}

	public void setError_message_2(String error_message_2) {
		this.error_message_2 = error_message_2;
	}

	public Integer getFinal_response_code_2() {
		return final_response_code_2;
	}

	public void setFinal_response_code_2(Integer final_response_code_2) {
		this.final_response_code_2 = final_response_code_2;
	}

	public String getFollow_flg_2() {
		return follow_flg_2;
	}

	public void setFollow_flg_2(String follow_flg_2) {
		this.follow_flg_2 = follow_flg_2;
	}

	public String[] getH1_2() {
		return h1_2;
	}

	public void setH1_2(String[] h1_2) {
		this.h1_2 = h1_2;
	}

	public Integer getH1_count_2() {
		return h1_count_2;
	}

	public void setH1_count_2(Integer h1_count_2) {
		this.h1_count_2 = h1_count_2;
	}

	public Integer getH1_length_2() {
		return h1_length_2;
	}

	public void setH1_length_2(Integer h1_length_2) {
		this.h1_length_2 = h1_length_2;
	}

	public String[] getH2_2() {
		return h2_2;
	}

	public void setH2_2(String[] h2_2) {
		this.h2_2 = h2_2;
	}

	public Boolean getHeader_noarchive_2() {
		return header_noarchive_2;
	}

	public void setHeader_noarchive_2(Boolean header_noarchive_2) {
		this.header_noarchive_2 = header_noarchive_2;
	}

	public Boolean getHeader_nofollow_2() {
		return header_nofollow_2;
	}

	public void setHeader_nofollow_2(Boolean header_nofollow_2) {
		this.header_nofollow_2 = header_nofollow_2;
	}

	public Boolean getHeader_noindex_2() {
		return header_noindex_2;
	}

	public void setHeader_noindex_2(Boolean header_noindex_2) {
		this.header_noindex_2 = header_noindex_2;
	}

	public Boolean getHeader_noodp_2() {
		return header_noodp_2;
	}

	public void setHeader_noodp_2(Boolean header_noodp_2) {
		this.header_noodp_2 = header_noodp_2;
	}

	public Boolean getHeader_nosnippet_2() {
		return header_nosnippet_2;
	}

	public void setHeader_nosnippet_2(Boolean header_nosnippet_2) {
		this.header_nosnippet_2 = header_nosnippet_2;
	}

	public Boolean getHeader_noydir_2() {
		return header_noydir_2;
	}

	public void setHeader_noydir_2(Boolean header_noydir_2) {
		this.header_noydir_2 = header_noydir_2;
	}

	public HreflangErrors getHreflang_errors_2() {
		return hreflang_errors_2;
	}

	public void setHreflang_errors_2(HreflangErrors hreflang_errors_2) {
		this.hreflang_errors_2 = hreflang_errors_2;
	}

	public HreflangLinks[] getHreflang_links_2() {
		return hreflang_links_2;
	}

	public void setHreflang_links_2(HreflangLinks[] hreflang_links_2) {
		this.hreflang_links_2 = hreflang_links_2;
	}

	public Integer getHreflang_links_out_count_2() {
		return hreflang_links_out_count_2;
	}

	public void setHreflang_links_out_count_2(Integer hreflang_links_out_count_2) {
		this.hreflang_links_out_count_2 = hreflang_links_out_count_2;
	}

	public Integer getHreflang_url_count_2() {
		return hreflang_url_count_2;
	}

	public void setHreflang_url_count_2(Integer hreflang_url_count_2) {
		this.hreflang_url_count_2 = hreflang_url_count_2;
	}

	public String getIndex_flg_2() {
		return index_flg_2;
	}

	public void setIndex_flg_2(String index_flg_2) {
		this.index_flg_2 = index_flg_2;
	}

	public Boolean getIndexable_2() {
		return indexable_2;
	}

	public void setIndexable_2(Boolean indexable_2) {
		this.indexable_2 = indexable_2;
	}

	public String[] getInsecure_resources_2() {
		return insecure_resources_2;
	}

	public void setInsecure_resources_2(String[] insecure_resources_2) {
		this.insecure_resources_2 = insecure_resources_2;
	}

	public String getMeta_charset_2() {
		return meta_charset_2;
	}

	public void setMeta_charset_2(String meta_charset_2) {
		this.meta_charset_2 = meta_charset_2;
	}

	public String getMeta_content_type_2() {
		return meta_content_type_2;
	}

	public void setMeta_content_type_2(String meta_content_type_2) {
		this.meta_content_type_2 = meta_content_type_2;
	}

	public Boolean getMeta_disabled_sitelinks_2() {
		return meta_disabled_sitelinks_2;
	}

	public void setMeta_disabled_sitelinks_2(Boolean meta_disabled_sitelinks_2) {
		this.meta_disabled_sitelinks_2 = meta_disabled_sitelinks_2;
	}

	public Boolean getMeta_noodp_2() {
		return meta_noodp_2;
	}

	public void setMeta_noodp_2(Boolean meta_noodp_2) {
		this.meta_noodp_2 = meta_noodp_2;
	}

	public Boolean getMeta_nosnippet_2() {
		return meta_nosnippet_2;
	}

	public void setMeta_nosnippet_2(Boolean meta_nosnippet_2) {
		this.meta_nosnippet_2 = meta_nosnippet_2;
	}

	public Boolean getMeta_noydir_2() {
		return meta_noydir_2;
	}

	public void setMeta_noydir_2(Boolean meta_noydir_2) {
		this.meta_noydir_2 = meta_noydir_2;
	}

	public Boolean getMeta_redirect_2() {
		return meta_redirect_2;
	}

	public void setMeta_redirect_2(Boolean meta_redirect_2) {
		this.meta_redirect_2 = meta_redirect_2;
	}

	public Boolean getMixed_redirects_2() {
		return mixed_redirects_2;
	}

	public void setMixed_redirects_2(Boolean mixed_redirects_2) {
		this.mixed_redirects_2 = mixed_redirects_2;
	}

	public Boolean getMobile_rel_alternate_url_is_consistent_2() {
		return mobile_rel_alternate_url_is_consistent_2;
	}

	public void setMobile_rel_alternate_url_is_consistent_2(Boolean mobile_rel_alternate_url_is_consistent_2) {
		this.mobile_rel_alternate_url_is_consistent_2 = mobile_rel_alternate_url_is_consistent_2;
	}

	public Boolean getNoodp_2() {
		return noodp_2;
	}

	public void setNoodp_2(Boolean noodp_2) {
		this.noodp_2 = noodp_2;
	}

	public Boolean getNosnippet_2() {
		return nosnippet_2;
	}

	public void setNosnippet_2(Boolean nosnippet_2) {
		this.nosnippet_2 = nosnippet_2;
	}

	public Boolean getNoydir_2() {
		return noydir_2;
	}

	public void setNoydir_2(Boolean noydir_2) {
		this.noydir_2 = noydir_2;
	}

	public OgMarkup[] getOg_markup_2() {
		return og_markup_2;
	}

	public void setOg_markup_2(OgMarkup[] og_markup_2) {
		this.og_markup_2 = og_markup_2;
	}

	public Boolean getOg_markup_flag_2() {
		return og_markup_flag_2;
	}

	public void setOg_markup_flag_2(Boolean og_markup_flag_2) {
		this.og_markup_flag_2 = og_markup_flag_2;
	}

	public Integer getOg_markup_length_2() {
		return og_markup_length_2;
	}

	public void setOg_markup_length_2(Integer og_markup_length_2) {
		this.og_markup_length_2 = og_markup_length_2;
	}

	public Integer getOutlink_count_2() {
		return outlink_count_2;
	}

	public void setOutlink_count_2(Integer outlink_count_2) {
		this.outlink_count_2 = outlink_count_2;
	}

	public PageAnalysisFragments[] getPage_analysis_fragments_2() {
		return page_analysis_fragments_2;
	}

	public void setPage_analysis_fragments_2(PageAnalysisFragments[] page_analysis_fragments_2) {
		this.page_analysis_fragments_2 = page_analysis_fragments_2;
	}

	public PageLink[] getPage_link_2() {
		return page_link_2;
	}

	public void setPage_link_2(PageLink[] page_link_2) {
		this.page_link_2 = page_link_2;
	}

	public Boolean getRedirect_blocked_2() {
		return redirect_blocked_2;
	}

	public void setRedirect_blocked_2(Boolean redirect_blocked_2) {
		this.redirect_blocked_2 = redirect_blocked_2;
	}

	public String getRedirect_blocked_reason_2() {
		return redirect_blocked_reason_2;
	}

	public void setRedirect_blocked_reason_2(String redirect_blocked_reason_2) {
		this.redirect_blocked_reason_2 = redirect_blocked_reason_2;
	}

	public RedirectChain[] getRedirect_chain_2() {
		return redirect_chain_2;
	}

	public void setRedirect_chain_2(RedirectChain[] redirect_chain_2) {
		this.redirect_chain_2 = redirect_chain_2;
	}

	public String getRedirect_final_url_2() {
		return redirect_final_url_2;
	}

	public void setRedirect_final_url_2(String redirect_final_url_2) {
		this.redirect_final_url_2 = redirect_final_url_2;
	}

	public Integer getRedirect_times_2() {
		return redirect_times_2;
	}

	public void setRedirect_times_2(Integer redirect_times_2) {
		this.redirect_times_2 = redirect_times_2;
	}

	public String getResponse_code_2() {
		return response_code_2;
	}

	public void setResponse_code_2(String response_code_2) {
		this.response_code_2 = response_code_2;
	}

	public String[] getResponse_header_names_2() {
		return response_header_names_2;
	}

	public void setResponse_header_names_2(String[] response_header_names_2) {
		this.response_header_names_2 = response_header_names_2;
	}

	public String getRobots_2() {
		return robots_2;
	}

	public void setRobots_2(String robots_2) {
		this.robots_2 = robots_2;
	}

	public String getRobots_contents_2() {
		return robots_contents_2;
	}

	public void setRobots_contents_2(String robots_contents_2) {
		this.robots_contents_2 = robots_contents_2;
	}

	public String getRobots_flg_2() {
		return robots_flg_2;
	}

	public void setRobots_flg_2(String robots_flg_2) {
		this.robots_flg_2 = robots_flg_2;
	}

	public StructuredData getStructured_data_2() {
		return structured_data_2;
	}

	public void setStructured_data_2(StructuredData structured_data_2) {
		this.structured_data_2 = structured_data_2;
	}

	public String getTitle_2() {
		return title_2;
	}

	public void setTitle_2(String title_2) {
		this.title_2 = title_2;
	}

	public String getTitle_flg_2() {
		return title_flg_2;
	}

	public void setTitle_flg_2(String title_flg_2) {
		this.title_flg_2 = title_flg_2;
	}

	public Integer getTitle_length_2() {
		return title_length_2;
	}

	public void setTitle_length_2(Integer title_length_2) {
		this.title_length_2 = title_length_2;
	}

	public String getViewport_content_2() {
		return viewport_content_2;
	}

	public void setViewport_content_2(String viewport_content_2) {
		this.viewport_content_2 = viewport_content_2;
	}

	public Boolean getViewport_flag_2() {
		return viewport_flag_2;
	}

	public void setViewport_flag_2(Boolean viewport_flag_2) {
		this.viewport_flag_2 = viewport_flag_2;
	}

	@Override
	public String toString() {
		return "TargetUrlsDifferencesResponse [sequence=" + sequence + ", crawl_timestamp_1=" + crawl_timestamp_1 + ", domain_id_1=" + domain_id_1 + ", url_1=" + url_1
				+ ", crawl_timestamp_2=" + crawl_timestamp_2 + ", domain_id_2=" + domain_id_2 + ", url_2=" + url_2 + ", is_different=" + is_different
				+ ", alternate_links_chg_ind=" + alternate_links_chg_ind + ", amphtml_href_chg_ind=" + amphtml_href_chg_ind + ", analyzed_url_s_chg_ind="
				+ analyzed_url_s_chg_ind + ", archive_flg_chg_ind=" + archive_flg_chg_ind + ", base_tag_added_ind=" + base_tag_added_ind + ", base_tag_chg_ind="
				+ base_tag_chg_ind + ", base_tag_removed_ind=" + base_tag_removed_ind + ", base_tag_target_chg_ind=" + base_tag_target_chg_ind
				+ ", blocked_by_robots_chg_ind=" + blocked_by_robots_chg_ind + ", canonical_added_ind=" + canonical_added_ind + ", canonical_chg_ind="
				+ canonical_chg_ind + ", canonical_header_flag_chg_ind=" + canonical_header_flag_chg_ind + ", canonical_header_type_chg_ind="
				+ canonical_header_type_chg_ind + ", canonical_removed_ind=" + canonical_removed_ind + ", canonical_type_chg_ind=" + canonical_type_chg_ind
				+ ", canonical_url_is_consistent_chg_ind=" + canonical_url_is_consistent_chg_ind + ", content_type_chg_ind=" + content_type_chg_ind
				+ ", custom_data_added_ind=" + custom_data_added_ind + ", custom_data_chg_ind=" + custom_data_chg_ind + ", custom_data_removed_ind="
				+ custom_data_removed_ind + ", description_added_ind=" + description_added_ind + ", description_chg_ind=" + description_chg_ind
				+ ", description_length_chg_ind=" + description_length_chg_ind + ", description_removed_ind=" + description_removed_ind + ", error_message_chg_ind="
				+ error_message_chg_ind + ", final_response_code_chg_ind=" + final_response_code_chg_ind + ", follow_flg_chg_ind=" + follow_flg_chg_ind
				+ ", h1_added_ind=" + h1_added_ind + ", h1_chg_ind=" + h1_chg_ind + ", h1_count_chg_ind=" + h1_count_chg_ind + ", h1_length_chg_ind="
				+ h1_length_chg_ind + ", h1_removed_ind=" + h1_removed_ind + ", h2_added_ind=" + h2_added_ind + ", h2_chg_ind=" + h2_chg_ind + ", h2_removed_ind="
				+ h2_removed_ind + ", header_noarchive_chg_ind=" + header_noarchive_chg_ind + ", header_nofollow_chg_ind=" + header_nofollow_chg_ind
				+ ", header_noindex_chg_ind=" + header_noindex_chg_ind + ", header_noodp_chg_ind=" + header_noodp_chg_ind + ", header_nosnippet_chg_ind="
				+ header_nosnippet_chg_ind + ", header_noydir_chg_ind=" + header_noydir_chg_ind + ", hreflang_errors_chg_ind=" + hreflang_errors_chg_ind
				+ ", hreflang_links_added_ind=" + hreflang_links_added_ind + ", hreflang_links_chg_ind=" + hreflang_links_chg_ind
				+ ", hreflang_links_out_count_chg_ind=" + hreflang_links_out_count_chg_ind + ", hreflang_links_removed_ind=" + hreflang_links_removed_ind
				+ ", hreflang_url_count_chg_ind=" + hreflang_url_count_chg_ind + ", indexable_chg_ind=" + indexable_chg_ind + ", index_flg_chg_ind=" + index_flg_chg_ind
				+ ", insecure_resources_chg_ind=" + insecure_resources_chg_ind + ", meta_charset_chg_ind=" + meta_charset_chg_ind + ", meta_content_type_chg_ind="
				+ meta_content_type_chg_ind + ", meta_disabled_sitelinks_chg_ind=" + meta_disabled_sitelinks_chg_ind + ", meta_noodp_chg_ind=" + meta_noodp_chg_ind
				+ ", meta_nosnippet_chg_ind=" + meta_nosnippet_chg_ind + ", meta_noydir_chg_ind=" + meta_noydir_chg_ind + ", meta_redirect_chg_ind="
				+ meta_redirect_chg_ind + ", mixed_redirects_chg_ind=" + mixed_redirects_chg_ind + ", mobile_rel_alternate_url_is_consistent_chg_ind="
				+ mobile_rel_alternate_url_is_consistent_chg_ind + ", noodp_chg_ind=" + noodp_chg_ind + ", nosnippet_chg_ind=" + nosnippet_chg_ind + ", noydir_chg_ind="
				+ noydir_chg_ind + ", og_markup_chg_ind=" + og_markup_chg_ind + ", og_markup_length_chg_ind=" + og_markup_length_chg_ind + ", open_graph_added_ind="
				+ open_graph_added_ind + ", open_graph_removed_ind=" + open_graph_removed_ind + ", outlink_count_chg_ind=" + outlink_count_chg_ind
				+ ", page_analysis_fragments_chg_ind=" + page_analysis_fragments_chg_ind + ", page_link_chg_ind=" + page_link_chg_ind + ", redirect_301_detected_ind="
				+ redirect_301_detected_ind + ", redirect_301_removed_ind=" + redirect_301_removed_ind + ", redirect_302_detected_ind=" + redirect_302_detected_ind
				+ ", redirect_302_removed_ind=" + redirect_302_removed_ind + ", redirect_blocked_chg_ind=" + redirect_blocked_chg_ind
				+ ", redirect_blocked_reason_chg_ind=" + redirect_blocked_reason_chg_ind + ", redirect_chain_chg_ind=" + redirect_chain_chg_ind
				+ ", redirect_diff_code_ind=" + redirect_diff_code_ind + ", redirect_final_url_chg_ind=" + redirect_final_url_chg_ind + ", redirect_times_chg_ind="
				+ redirect_times_chg_ind + ", response_code_chg_ind=" + response_code_chg_ind + ", response_headers_added_ind=" + response_headers_added_ind
				+ ", response_headers_removed_ind=" + response_headers_removed_ind + ", robots_added_ind=" + robots_added_ind + ", robots_chg_ind=" + robots_chg_ind
				+ ", robots_contents_chg_ind=" + robots_contents_chg_ind + ", robots_removed_ind=" + robots_removed_ind + ", structured_data_chg_ind="
				+ structured_data_chg_ind + ", title_added_ind=" + title_added_ind + ", title_chg_ind=" + title_chg_ind + ", title_length_chg_ind="
				+ title_length_chg_ind + ", title_removed_ind=" + title_removed_ind + ", viewport_added_ind=" + viewport_added_ind + ", viewport_content_chg_ind="
				+ viewport_content_chg_ind + ", viewport_removed_ind=" + viewport_removed_ind + ", page_analysis_results_chg_ind_json="
				+ page_analysis_results_chg_ind_json + ", alternate_links_1=" + Arrays.toString(alternate_links_1) + ", amphtml_href_1=" + amphtml_href_1
				+ ", analyzed_url_s_1=" + analyzed_url_s_1 + ", archive_flg_1=" + archive_flg_1 + ", base_tag_1=" + base_tag_1 + ", base_tag_flag_1=" + base_tag_flag_1
				+ ", base_tag_target_1=" + base_tag_target_1 + ", blocked_by_robots_1=" + blocked_by_robots_1 + ", canonical_1=" + canonical_1 + ", canonical_flg_1="
				+ canonical_flg_1 + ", canonical_header_flag_1=" + canonical_header_flag_1 + ", canonical_header_type_1=" + canonical_header_type_1
				+ ", canonical_type_1=" + canonical_type_1 + ", canonical_url_is_consistent_1=" + canonical_url_is_consistent_1 + ", content_type_1=" + content_type_1
				+ ", custom_data_1=" + Arrays.toString(custom_data_1) + ", description_1=" + description_1 + ", description_flg_1=" + description_flg_1
				+ ", description_length_1=" + description_length_1 + ", error_message_1=" + error_message_1 + ", final_response_code_1=" + final_response_code_1
				+ ", follow_flg_1=" + follow_flg_1 + ", h1_1=" + Arrays.toString(h1_1) + ", h1_count_1=" + h1_count_1 + ", h1_length_1=" + h1_length_1 + ", h2_1="
				+ Arrays.toString(h2_1) + ", header_noarchive_1=" + header_noarchive_1 + ", header_nofollow_1=" + header_nofollow_1 + ", header_noindex_1="
				+ header_noindex_1 + ", header_noodp_1=" + header_noodp_1 + ", header_nosnippet_1=" + header_nosnippet_1 + ", header_noydir_1=" + header_noydir_1
				+ ", hreflang_errors_1=" + hreflang_errors_1 + ", hreflang_links_1=" + Arrays.toString(hreflang_links_1) + ", hreflang_links_out_count_1="
				+ hreflang_links_out_count_1 + ", hreflang_url_count_1=" + hreflang_url_count_1 + ", index_flg_1=" + index_flg_1 + ", indexable_1=" + indexable_1
				+ ", insecure_resources_1=" + Arrays.toString(insecure_resources_1) + ", meta_charset_1=" + meta_charset_1 + ", meta_content_type_1="
				+ meta_content_type_1 + ", meta_disabled_sitelinks_1=" + meta_disabled_sitelinks_1 + ", meta_noodp_1=" + meta_noodp_1 + ", meta_nosnippet_1="
				+ meta_nosnippet_1 + ", meta_noydir_1=" + meta_noydir_1 + ", meta_redirect_1=" + meta_redirect_1 + ", mixed_redirects_1=" + mixed_redirects_1
				+ ", mobile_rel_alternate_url_is_consistent_1=" + mobile_rel_alternate_url_is_consistent_1 + ", noodp_1=" + noodp_1 + ", nosnippet_1=" + nosnippet_1
				+ ", noydir_1=" + noydir_1 + ", og_markup_1=" + Arrays.toString(og_markup_1) + ", og_markup_flag_1=" + og_markup_flag_1 + ", og_markup_length_1="
				+ og_markup_length_1 + ", outlink_count_1=" + outlink_count_1 + ", page_analysis_fragments_1=" + Arrays.toString(page_analysis_fragments_1)
				+ ", page_link_1=" + Arrays.toString(page_link_1) + ", redirect_blocked_1=" + redirect_blocked_1 + ", redirect_blocked_reason_1="
				+ redirect_blocked_reason_1 + ", redirect_chain_1=" + Arrays.toString(redirect_chain_1) + ", redirect_final_url_1=" + redirect_final_url_1
				+ ", redirect_times_1=" + redirect_times_1 + ", response_code_1=" + response_code_1 + ", response_header_names_1="
				+ Arrays.toString(response_header_names_1) + ", robots_1=" + robots_1 + ", robots_contents_1=" + robots_contents_1 + ", robots_flg_1=" + robots_flg_1
				+ ", structured_data_1=" + structured_data_1 + ", title_1=" + title_1 + ", title_flg_1=" + title_flg_1 + ", title_length_1=" + title_length_1
				+ ", viewport_content_1=" + viewport_content_1 + ", viewport_flag_1=" + viewport_flag_1 + ", alternate_links_2=" + Arrays.toString(alternate_links_2)
				+ ", amphtml_href_2=" + amphtml_href_2 + ", analyzed_url_s_2=" + analyzed_url_s_2 + ", archive_flg_2=" + archive_flg_2 + ", base_tag_2=" + base_tag_2
				+ ", base_tag_flag_2=" + base_tag_flag_2 + ", base_tag_target_2=" + base_tag_target_2 + ", blocked_by_robots_2=" + blocked_by_robots_2
				+ ", canonical_2=" + canonical_2 + ", canonical_flg_2=" + canonical_flg_2 + ", canonical_header_flag_2=" + canonical_header_flag_2
				+ ", canonical_header_type_2=" + canonical_header_type_2 + ", canonical_type_2=" + canonical_type_2 + ", canonical_url_is_consistent_2="
				+ canonical_url_is_consistent_2 + ", content_type_2=" + content_type_2 + ", custom_data_2=" + Arrays.toString(custom_data_2) + ", description_2="
				+ description_2 + ", description_flg_2=" + description_flg_2 + ", description_length_2=" + description_length_2 + ", error_message_2=" + error_message_2
				+ ", final_response_code_2=" + final_response_code_2 + ", follow_flg_2=" + follow_flg_2 + ", h1_2=" + Arrays.toString(h1_2) + ", h1_count_2="
				+ h1_count_2 + ", h1_length_2=" + h1_length_2 + ", h2_2=" + Arrays.toString(h2_2) + ", header_noarchive_2=" + header_noarchive_2
				+ ", header_nofollow_2=" + header_nofollow_2 + ", header_noindex_2=" + header_noindex_2 + ", header_noodp_2=" + header_noodp_2 + ", header_nosnippet_2="
				+ header_nosnippet_2 + ", header_noydir_2=" + header_noydir_2 + ", hreflang_errors_2=" + hreflang_errors_2 + ", hreflang_links_2="
				+ Arrays.toString(hreflang_links_2) + ", hreflang_links_out_count_2=" + hreflang_links_out_count_2 + ", hreflang_url_count_2=" + hreflang_url_count_2
				+ ", index_flg_2=" + index_flg_2 + ", indexable_2=" + indexable_2 + ", insecure_resources_2=" + Arrays.toString(insecure_resources_2)
				+ ", meta_charset_2=" + meta_charset_2 + ", meta_content_type_2=" + meta_content_type_2 + ", meta_disabled_sitelinks_2=" + meta_disabled_sitelinks_2
				+ ", meta_noodp_2=" + meta_noodp_2 + ", meta_nosnippet_2=" + meta_nosnippet_2 + ", meta_noydir_2=" + meta_noydir_2 + ", meta_redirect_2="
				+ meta_redirect_2 + ", mixed_redirects_2=" + mixed_redirects_2 + ", mobile_rel_alternate_url_is_consistent_2="
				+ mobile_rel_alternate_url_is_consistent_2 + ", noodp_2=" + noodp_2 + ", nosnippet_2=" + nosnippet_2 + ", noydir_2=" + noydir_2 + ", og_markup_2="
				+ Arrays.toString(og_markup_2) + ", og_markup_flag_2=" + og_markup_flag_2 + ", og_markup_length_2=" + og_markup_length_2 + ", outlink_count_2="
				+ outlink_count_2 + ", page_analysis_fragments_2=" + Arrays.toString(page_analysis_fragments_2) + ", page_link_2=" + Arrays.toString(page_link_2)
				+ ", redirect_blocked_2=" + redirect_blocked_2 + ", redirect_blocked_reason_2=" + redirect_blocked_reason_2 + ", redirect_chain_2="
				+ Arrays.toString(redirect_chain_2) + ", redirect_final_url_2=" + redirect_final_url_2 + ", redirect_times_2=" + redirect_times_2 + ", response_code_2="
				+ response_code_2 + ", response_header_names_2=" + Arrays.toString(response_header_names_2) + ", robots_2=" + robots_2 + ", robots_contents_2="
				+ robots_contents_2 + ", robots_flg_2=" + robots_flg_2 + ", structured_data_2=" + structured_data_2 + ", title_2=" + title_2 + ", title_flg_2="
				+ title_flg_2 + ", title_length_2=" + title_length_2 + ", viewport_content_2=" + viewport_content_2 + ", viewport_flag_2=" + viewport_flag_2 + "]";
	}

}
