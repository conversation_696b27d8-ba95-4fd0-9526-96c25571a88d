package com.actonia.value.object;

import java.util.Arrays;

public class SiteclaritySettings {
	private Boolean javascript_enabled;
	private JavascriptSettings javascript_settings;
	private CustomDataRequest[] custom_data_requests;
	private Boolean disable_page_size_check;
	private Boolean extractStructuredData;
	private Boolean includeStructuredDataValidationErrors;
	private Boolean includeStructuredDataValidationData;
	private Boolean store_html;
	private Integer domain_id;
	private Boolean enable_custom_source;
	private CustomSourceInfo custom_source_info;
	
	// https://www.wrike.com/open.htm?id=837498886
	private String splash_request_url;
	private Boolean generate_page_links;
	private Boolean validate_structured_schema;

	public Boolean getJavascript_enabled() {
		return javascript_enabled;
	}

	public void setJavascript_enabled(Boolean javascript_enabled) {
		this.javascript_enabled = javascript_enabled;
	}

	public JavascriptSettings getJavascript_settings() {
		return javascript_settings;
	}

	public void setJavascript_settings(JavascriptSettings javascript_settings) {
		this.javascript_settings = javascript_settings;
	}

	public CustomDataRequest[] getCustom_data_requests() {
		return custom_data_requests;
	}

	public void setCustom_data_requests(CustomDataRequest[] custom_data_requests) {
		this.custom_data_requests = custom_data_requests;
	}

	public Boolean getExtractStructuredData() {
		return extractStructuredData;
	}

	public void setExtractStructuredData(Boolean extractStructuredData) {
		this.extractStructuredData = extractStructuredData;
	}

	public Boolean getIncludeStructuredDataValidationErrors() {
		return includeStructuredDataValidationErrors;
	}

	public void setIncludeStructuredDataValidationErrors(Boolean includeStructuredDataValidationErrors) {
		this.includeStructuredDataValidationErrors = includeStructuredDataValidationErrors;
	}

	public Boolean getIncludeStructuredDataValidationData() {
		return includeStructuredDataValidationData;
	}

	public void setIncludeStructuredDataValidationData(Boolean includeStructuredDataValidationData) {
		this.includeStructuredDataValidationData = includeStructuredDataValidationData;
	}

	public Boolean getStore_html() {
		return store_html;
	}

	public void setStore_html(Boolean store_html) {
		this.store_html = store_html;
	}

	public Integer getDomain_id() {
		return domain_id;
	}

	public void setDomain_id(Integer domain_id) {
		this.domain_id = domain_id;
	}

	public Boolean getEnable_custom_source() {
		return enable_custom_source;
	}

	public void setEnable_custom_source(Boolean enable_custom_source) {
		this.enable_custom_source = enable_custom_source;
	}

	public CustomSourceInfo getCustom_source_info() {
		return custom_source_info;
	}

	public void setCustom_source_info(CustomSourceInfo custom_source_info) {
		this.custom_source_info = custom_source_info;
	}

	public Boolean getDisable_page_size_check() {
		return disable_page_size_check;
	}

	public void setDisable_page_size_check(Boolean disable_page_size_check) {
		this.disable_page_size_check = disable_page_size_check;
	}

	public String getSplash_request_url() {
		return splash_request_url;
	}

	public void setSplash_request_url(String splash_request_url) {
		this.splash_request_url = splash_request_url;
	}

	public Boolean getGenerate_page_links() {
		return generate_page_links;
	}

	public void setGenerate_page_links(Boolean generate_page_links) {
		this.generate_page_links = generate_page_links;
	}

	public Boolean getValidate_structured_schema() {
		return validate_structured_schema;
	}

	public void setValidate_structured_schema(Boolean validate_structured_schema) {
		this.validate_structured_schema = validate_structured_schema;
	}

	@Override
	public String toString() {
		return "SiteclaritySettings [javascript_enabled=" + javascript_enabled + ", javascript_settings=" + javascript_settings + ", custom_data_requests="
				+ Arrays.toString(custom_data_requests) + ", disable_page_size_check=" + disable_page_size_check + ", extractStructuredData=" + extractStructuredData
				+ ", includeStructuredDataValidationErrors=" + includeStructuredDataValidationErrors + ", includeStructuredDataValidationData="
				+ includeStructuredDataValidationData + ", store_html=" + store_html + ", domain_id=" + domain_id + ", enable_custom_source=" + enable_custom_source
				+ ", custom_source_info=" + custom_source_info + ", splash_request_url=" + splash_request_url + ", generate_page_links=" + generate_page_links
				+ ", validate_structured_schema=" + validate_structured_schema + "]";
	}

}
