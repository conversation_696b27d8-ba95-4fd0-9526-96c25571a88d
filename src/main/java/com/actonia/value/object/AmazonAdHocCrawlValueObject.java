package com.actonia.value.object;

import java.util.Arrays;

import com.actonia.IConstants;

public class AmazonAdHocCrawlValueObject {
	private String urlString;
	private String responseCode;
	private String title;
	private String metaDescription;
	private String[] schemaTypeArray;
	private int totalPageLinks;
	private String content;
	private String contentDetected;
	private int totalContentOccurrences;

	public String getUrlString() {
		return urlString;
	}

	public void setUrlString(String urlString) {
		this.urlString = urlString;
	}

	public String getResponseCode() {
		return responseCode;
	}

	public void setResponseCode(String responseCode) {
		this.responseCode = responseCode;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getMetaDescription() {
		return metaDescription;
	}

	public void setMetaDescription(String metaDescription) {
		this.metaDescription = metaDescription;
	}

	public String[] getSchemaTypeArray() {
		return schemaTypeArray;
	}

	public void setSchemaTypeArray(String[] schemaTypeArray) {
		this.schemaTypeArray = schemaTypeArray;
	}

	public int getTotalPageLinks() {
		return totalPageLinks;
	}

	public void setTotalPageLinks(int totalPageLinks) {
		this.totalPageLinks = totalPageLinks;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getContentDetected() {
		return contentDetected;
	}

	public void setContentDetected(String contentDetected) {
		this.contentDetected = contentDetected;
	}

	public int getTotalContentOccurrences() {
		return totalContentOccurrences;
	}

	public void setTotalContentOccurrences(int totalContentOccurrences) {
		this.totalContentOccurrences = totalContentOccurrences;
	}

	@Override
	public String toString() {
		return "AmazonAdHocCrawlValueObject [urlString=" + urlString + ", responseCode=" + responseCode + ", title=" + title + ", metaDescription=" + metaDescription
				+ ", schemaTypeArray=" + Arrays.toString(schemaTypeArray) + ", totalPageLinks=" + totalPageLinks + ", content=" + content + ", contentDetected="
				+ contentDetected + ", totalContentOccurrences=" + totalContentOccurrences + "]";
	}

}
