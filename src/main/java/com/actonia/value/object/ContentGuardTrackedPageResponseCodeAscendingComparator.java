package com.actonia.value.object;

import java.util.Comparator;

import com.actonia.web.service.ContentGuardTrackedPage;

public class ContentGuardTrackedPageResponseCodeAscendingComparator implements Comparator<ContentGuardTrackedPage> {

	@Override
	public int compare(ContentGuardTrackedPage arg0, ContentGuardTrackedPage arg1) {

		// sort by:
		// 1) response_code (ascending order)

		int response = 0;
		if (arg0 != null && arg1 != null) {
			// response_code
			if (arg0.getResponse_code() == null && arg1.getResponse_code() == null) { // when arg0 and arg1 are both null
				response = 0;
			} else if (arg0.getResponse_code() != null && arg1.getResponse_code() == null) { // when arg0 is greater than arg1
				response = +1;
			} else if (arg0.getResponse_code() == null && arg1.getResponse_code() != null) { // when arg0 is smaller than arg1
				response = -1;
			} else if (arg0.getResponse_code().compareTo(arg1.getResponse_code()) > 0) { // when arg0 is greater than arg1
				response = +1;
			} else if (arg0.getResponse_code().compareTo(arg1.getResponse_code()) < 0) { // when arg0 is smaller than arg1
				response = -1;
			}
			if (response == 0) {
				// url in ascending order
				if (arg0.getUrl().compareTo(arg1.getUrl()) > 0) { // when arg0 is greater than arg1
					response = +1;
				} else if (arg0.getUrl().compareTo(arg1.getUrl()) < 0) { // when arg0 is smaller than arg1
					response = -1;
				}
			}
		} else if (arg0 != null && arg1 == null) { // when arg0 is greater than arg1
			response = +1;
		} else if (arg0 == null && arg1 != null) { // when arg0 is smaller than arg1
			response = -1;
		}
		return response;
	}

}
