package com.actonia.value.object;

public class PutNewTargetUrlToQueueReportValueObject implements Cloneable {
	private Boolean putMessagesInd;
	private int domainId;
	private String domainName;
	private Integer totalUrls;
	private Integer crawlQueueLastUpdateDate;
	private Integer totalUrlsWithoutData;
	private Integer messagesInQueue;
	private Integer messagesInFlight;
	private String notes;

	public Boolean getPutMessagesInd() {
		return putMessagesInd;
	}

	public void setPutMessagesInd(Boolean putMessagesInd) {
		this.putMessagesInd = putMessagesInd;
	}

	public int getDomainId() {
		return domainId;
	}

	public void setDomainId(int domainId) {
		this.domainId = domainId;
	}

	public String getDomainName() {
		return domainName;
	}

	public void setDomainName(String domainName) {
		this.domainName = domainName;
	}

	public Integer getTotalUrls() {
		return totalUrls;
	}

	public void setTotalUrls(Integer totalUrls) {
		this.totalUrls = totalUrls;
	}

	public Integer getCrawlQueueLastUpdateDate() {
		return crawlQueueLastUpdateDate;
	}

	public void setCrawlQueueLastUpdateDate(Integer crawlQueueLastUpdateDate) {
		this.crawlQueueLastUpdateDate = crawlQueueLastUpdateDate;
	}

	public Integer getTotalUrlsWithoutData() {
		return totalUrlsWithoutData;
	}

	public void setTotalUrlsWithoutData(Integer totalUrlsWithoutData) {
		this.totalUrlsWithoutData = totalUrlsWithoutData;
	}

	public Integer getMessagesInQueue() {
		return messagesInQueue;
	}

	public void setMessagesInQueue(Integer messagesInQueue) {
		this.messagesInQueue = messagesInQueue;
	}

	public Integer getMessagesInFlight() {
		return messagesInFlight;
	}

	public void setMessagesInFlight(Integer messagesInFlight) {
		this.messagesInFlight = messagesInFlight;
	}

	public String getNotes() {
		return notes;
	}

	public void setNotes(String notes) {
		this.notes = notes;
	}

	@Override
	public String toString() {
		return "PutNewTargetUrlToQueueReportValueObject [putMessagesInd=" + putMessagesInd + ", domainId=" + domainId + ", domainName=" + domainName + ", totalUrls="
				+ totalUrls + ", crawlQueueLastUpdateDate=" + crawlQueueLastUpdateDate + ", totalUrlsWithoutData=" + totalUrlsWithoutData + ", messagesInQueue="
				+ messagesInQueue + ", messagesInFlight=" + messagesInFlight + ", notes=" + notes + "]";
	}

}