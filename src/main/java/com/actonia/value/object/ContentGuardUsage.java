package com.actonia.value.object;

public class ContentGuardUsage {
	private String usage_date;
	private Integer total_urls;

	public String getUsage_date() {
		return usage_date;
	}

	public void setUsage_date(String usage_date) {
		this.usage_date = usage_date;
	}

	public Integer getTotal_urls() {
		return total_urls;
	}

	public void setTotal_urls(Integer total_urls) {
		this.total_urls = total_urls;
	}

	@Override
	public String toString() {
		return "ContentGuardUsage [usage_date=" + usage_date + ", total_urls=" + total_urls + "]";
	}

}
