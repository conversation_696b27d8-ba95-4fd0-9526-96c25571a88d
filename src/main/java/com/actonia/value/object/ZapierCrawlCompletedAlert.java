package com.actonia.value.object;

public class ZapierCrawlCompletedAlert {
	private String id;
	private Integer domain_id;
	private String domain_name;
	private Integer request_id;
	private String crawl_start_timestamp;
	private String crawl_end_timestamp;
	private String requester_email;
	private String project;
	private String language;
	private String what_to_crawl;
	private String starting_url;
	private String crawl_type;
	private Integer crawl_speed;
	private Integer crawl_depth;
	private String crawl_starting_url_status_code;
	private String crawl_starting_url_status_message;
	private Integer total_pages_crawled;
	private String error_code;
	private String error_message;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Integer getDomain_id() {
		return domain_id;
	}

	public void setDomain_id(Integer domain_id) {
		this.domain_id = domain_id;
	}

	public String getDomain_name() {
		return domain_name;
	}

	public void setDomain_name(String domain_name) {
		this.domain_name = domain_name;
	}

	public Integer getRequest_id() {
		return request_id;
	}

	public void setRequest_id(Integer request_id) {
		this.request_id = request_id;
	}

	public String getCrawl_start_timestamp() {
		return crawl_start_timestamp;
	}

	public void setCrawl_start_timestamp(String crawl_start_timestamp) {
		this.crawl_start_timestamp = crawl_start_timestamp;
	}

	public String getCrawl_end_timestamp() {
		return crawl_end_timestamp;
	}

	public void setCrawl_end_timestamp(String crawl_end_timestamp) {
		this.crawl_end_timestamp = crawl_end_timestamp;
	}

	public String getRequester_email() {
		return requester_email;
	}

	public void setRequester_email(String requester_email) {
		this.requester_email = requester_email;
	}

	public String getProject() {
		return project;
	}

	public void setProject(String project) {
		this.project = project;
	}

	public String getLanguage() {
		return language;
	}

	public void setLanguage(String language) {
		this.language = language;
	}

	public String getWhat_to_crawl() {
		return what_to_crawl;
	}

	public void setWhat_to_crawl(String what_to_crawl) {
		this.what_to_crawl = what_to_crawl;
	}

	public String getStarting_url() {
		return starting_url;
	}

	public void setStarting_url(String starting_url) {
		this.starting_url = starting_url;
	}

	public String getCrawl_type() {
		return crawl_type;
	}

	public void setCrawl_type(String crawl_type) {
		this.crawl_type = crawl_type;
	}

	public Integer getCrawl_speed() {
		return crawl_speed;
	}

	public void setCrawl_speed(Integer crawl_speed) {
		this.crawl_speed = crawl_speed;
	}

	public Integer getCrawl_depth() {
		return crawl_depth;
	}

	public void setCrawl_depth(Integer crawl_depth) {
		this.crawl_depth = crawl_depth;
	}

	public String getCrawl_starting_url_status_code() {
		return crawl_starting_url_status_code;
	}

	public void setCrawl_starting_url_status_code(String crawl_starting_url_status_code) {
		this.crawl_starting_url_status_code = crawl_starting_url_status_code;
	}

	public String getCrawl_starting_url_status_message() {
		return crawl_starting_url_status_message;
	}

	public void setCrawl_starting_url_status_message(String crawl_starting_url_status_message) {
		this.crawl_starting_url_status_message = crawl_starting_url_status_message;
	}

	public Integer getTotal_pages_crawled() {
		return total_pages_crawled;
	}

	public void setTotal_pages_crawled(Integer total_pages_crawled) {
		this.total_pages_crawled = total_pages_crawled;
	}

	public String getError_code() {
		return error_code;
	}

	public void setError_code(String error_code) {
		this.error_code = error_code;
	}

	public String getError_message() {
		return error_message;
	}

	public void setError_message(String error_message) {
		this.error_message = error_message;
	}

	@Override
	public String toString() {
		return "ZapierCrawlCompletedAlert [id=" + id + ", domain_id=" + domain_id + ", domain_name=" + domain_name + ", request_id=" + request_id
				+ ", crawl_start_timestamp=" + crawl_start_timestamp + ", crawl_end_timestamp=" + crawl_end_timestamp + ", requester_email=" + requester_email
				+ ", project=" + project + ", language=" + language + ", what_to_crawl=" + what_to_crawl + ", starting_url=" + starting_url + ", crawl_type="
				+ crawl_type + ", crawl_speed=" + crawl_speed + ", crawl_depth=" + crawl_depth + ", crawl_starting_url_status_code=" + crawl_starting_url_status_code
				+ ", crawl_starting_url_status_message=" + crawl_starting_url_status_message + ", total_pages_crawled=" + total_pages_crawled + ", error_code="
				+ error_code + ", error_message=" + error_message + "]";
	}

}
