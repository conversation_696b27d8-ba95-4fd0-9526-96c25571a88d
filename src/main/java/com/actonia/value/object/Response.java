package com.actonia.value.object;

import java.util.Arrays;

public class Response implements Cloneable {
	private Result result;
	private SiteMapUrls[] siteMapUrls;
	private String rule;
	private String message;
	private String robotsTxtContent;
	private String status;
	private Integer statusCode;

	@Override
	public Response clone() throws CloneNotSupportedException {
		// TODO Auto-generated method stub
		return (Response) super.clone();
	}

	public Result getResult() {
		return result;
	}

	public void setResult(Result result) {
		this.result = result;
	}

	public String getRule() {
		return rule;
	}

	public void setRule(String rule) {
		this.rule = rule;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public String getRobotsTxtContent() {
		return robotsTxtContent;
	}

	public void setRobotsTxtContent(String robotsTxtContent) {
		this.robotsTxtContent = robotsTxtContent;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Integer getStatusCode() {
		return statusCode;
	}

	public void setStatusCode(Integer statusCode) {
		this.statusCode = statusCode;
	}

	public SiteMapUrls[] getSiteMapUrls() {
		return siteMapUrls;
	}

	public void setSiteMapUrls(SiteMapUrls[] siteMapUrls) {
		this.siteMapUrls = siteMapUrls;
	}

	@Override
	public String toString() {
		return "Response [result=" + result 
				+ ", siteMapUrls=" + Arrays.toString(siteMapUrls) 
				+ ", rule=" + rule + ", message=" + message + ", robotsTxtContent="
				+ robotsTxtContent + ", status=" + status + ", statusCode=" + statusCode + "]";
	}

}
