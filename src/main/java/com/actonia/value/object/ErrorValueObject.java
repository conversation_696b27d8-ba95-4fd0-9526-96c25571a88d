package com.actonia.value.object;

public class ErrorValueObject {
	private String sequence;
	private String error_code;
	private String error_message;
	private String crawl_timestamp;
	private String domain_id;
	private String url;

	public String getSequence() {
		return sequence;
	}

	public void setSequence(String sequence) {
		this.sequence = sequence;
	}

	public String getError_code() {
		return error_code;
	}

	public void setError_code(String error_code) {
		this.error_code = error_code;
	}

	public String getError_message() {
		return error_message;
	}

	public void setError_message(String error_message) {
		this.error_message = error_message;
	}

	public String getCrawl_timestamp() {
		return crawl_timestamp;
	}

	public void setCrawl_timestamp(String crawl_timestamp) {
		this.crawl_timestamp = crawl_timestamp;
	}

	public String getDomain_id() {
		return domain_id;
	}

	public void setDomain_id(String domain_id) {
		this.domain_id = domain_id;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	@Override
	public String toString() {
		return "ErrorValueObject [sequence=" + sequence + ", error_code=" + error_code + ", error_message=" + error_message + ", crawl_timestamp=" + crawl_timestamp
				+ ", domain_id=" + domain_id + ", url=" + url + "]";
	}

}
