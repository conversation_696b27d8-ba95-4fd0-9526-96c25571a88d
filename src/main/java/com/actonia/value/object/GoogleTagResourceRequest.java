package com.actonia.value.object;

import java.util.Arrays;

public class GoogleTagResourceRequest {
	private String access_token;
	private String gtm_edit_containers_access_token;
	private String gtm_edit_container_versions_access_token;
	private String gtm_publish_access_token;
	private String account_id;
	private String container_id;
	private String workspace_name;

	// structured data commands specific
	private String url;
	private String schema_type;
	private String data;
	// structured data commands specific

	// javascript commands specific
	private String javascript_name;
	private String script_content;
	private String[] create_url_array;
	private String[] delete_url_array;
	private Integer domain_id;
	private Integer user_id;
	// javascript commands specific

	public String getAccess_token() {
		return access_token;
	}

	public void setAccess_token(String access_token) {
		this.access_token = access_token;
	}

	public String getGtm_edit_containers_access_token() {
		return gtm_edit_containers_access_token;
	}

	public void setGtm_edit_containers_access_token(String gtm_edit_containers_access_token) {
		this.gtm_edit_containers_access_token = gtm_edit_containers_access_token;
	}

	public String getGtm_edit_container_versions_access_token() {
		return gtm_edit_container_versions_access_token;
	}

	public void setGtm_edit_container_versions_access_token(String gtm_edit_container_versions_access_token) {
		this.gtm_edit_container_versions_access_token = gtm_edit_container_versions_access_token;
	}

	public String getGtm_publish_access_token() {
		return gtm_publish_access_token;
	}

	public void setGtm_publish_access_token(String gtm_publish_access_token) {
		this.gtm_publish_access_token = gtm_publish_access_token;
	}

	public String getAccount_id() {
		return account_id;
	}

	public void setAccount_id(String account_id) {
		this.account_id = account_id;
	}

	public String getContainer_id() {
		return container_id;
	}

	public void setContainer_id(String container_id) {
		this.container_id = container_id;
	}

	public String getWorkspace_name() {
		return workspace_name;
	}

	public void setWorkspace_name(String workspace_name) {
		this.workspace_name = workspace_name;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getSchema_type() {
		return schema_type;
	}

	public void setSchema_type(String schema_type) {
		this.schema_type = schema_type;
	}

	public String getData() {
		return data;
	}

	public void setData(String data) {
		this.data = data;
	}

	public String getJavascript_name() {
		return javascript_name;
	}

	public void setJavascript_name(String javascript_name) {
		this.javascript_name = javascript_name;
	}

	public String getScript_content() {
		return script_content;
	}

	public void setScript_content(String script_content) {
		this.script_content = script_content;
	}

	public String[] getCreate_url_array() {
		return create_url_array;
	}

	public void setCreate_url_array(String[] create_url_array) {
		this.create_url_array = create_url_array;
	}

	public String[] getDelete_url_array() {
		return delete_url_array;
	}

	public void setDelete_url_array(String[] delete_url_array) {
		this.delete_url_array = delete_url_array;
	}

	public Integer getDomain_id() {
		return domain_id;
	}

	public void setDomain_id(Integer domain_id) {
		this.domain_id = domain_id;
	}

	public Integer getUser_id() {
		return user_id;
	}

	public void setUser_id(Integer user_id) {
		this.user_id = user_id;
	}

	@Override
	public String toString() {
		return "GoogleTagResourceRequest [access_token=" + access_token + ", gtm_edit_containers_access_token=" + gtm_edit_containers_access_token
				+ ", gtm_edit_container_versions_access_token=" + gtm_edit_container_versions_access_token + ", gtm_publish_access_token=" + gtm_publish_access_token
				+ ", account_id=" + account_id + ", container_id=" + container_id + ", workspace_name=" + workspace_name + ", url=" + url + ", schema_type="
				+ schema_type + ", data=" + data + ", javascript_name=" + javascript_name + ", script_content=" + script_content + ", create_url_array="
				+ Arrays.toString(create_url_array) + ", delete_url_array=" + Arrays.toString(delete_url_array) + ", domain_id=" + domain_id + ", user_id=" + user_id
				+ "]";
	}

}
