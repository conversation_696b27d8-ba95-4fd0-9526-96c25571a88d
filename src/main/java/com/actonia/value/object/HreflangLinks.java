package com.actonia.value.object;

public class HreflangLinks {
	private Integer index;
	private String href;
	private String lang;
	private String type;

	public Integer getIndex() {
		return index;
	}

	public void setIndex(Integer index) {
		this.index = index;
	}

	public String getHref() {
		return href;
	}

	public void setHref(String href) {
		this.href = href;
	}

	public String getLang() {
		return lang;
	}

	public void setLang(String lang) {
		this.lang = lang;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	@Override
	public String toString() {
		return "HreflangLinks [index=" + index + ", href=" + href + ", lang=" + lang + ", type=" + type + "]";
	}

}
