package com.actonia.value.object;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PageTagContentChangeResults {

	private int groupTagId;

	private String groupTagName;

	private int totalTargetUrlsInGroupTag;

	private int totalTargetUrlsRequireAlert;

	private BigDecimal percentOfTargetUrlsRequireAlert;

	// map key = alert name
	// map value = List of target URL ID
	private Map<String, List<Long>> alertNameUrlIdListMap = new HashMap<String, List<Long>>();

	// map key = from response code,to response code
	// map value = List of target URL ID
	private Map<String, List<Long>> responseCodesUrlIdListMap = new HashMap<String, List<Long>>();

	// map key = change type
	// map value = List of target URL ID
	private Map<Integer, List<Long>> changeTypeUrlIdListMap = new HashMap<Integer, List<Long>>();

	// map key = selector type + selector
	// map value = List of target URL ID
	private Map<String, List<Long>> additionalContentUrlIdListMap = new HashMap<String, List<Long>>();

	public int getGroupTagId() {
		return groupTagId;
	}

	public void setGroupTagId(int groupTagId) {
		this.groupTagId = groupTagId;
	}

	public String getGroupTagName() {
		return groupTagName;
	}

	public void setGroupTagName(String groupTagName) {
		this.groupTagName = groupTagName;
	}

	public int getTotalTargetUrlsInGroupTag() {
		return totalTargetUrlsInGroupTag;
	}

	public void setTotalTargetUrlsInGroupTag(int totalTargetUrlsInGroupTag) {
		this.totalTargetUrlsInGroupTag = totalTargetUrlsInGroupTag;
	}

	public int getTotalTargetUrlsRequireAlert() {
		return totalTargetUrlsRequireAlert;
	}

	public void setTotalTargetUrlsRequireAlert(int totalTargetUrlsRequireAlert) {
		this.totalTargetUrlsRequireAlert = totalTargetUrlsRequireAlert;
	}

	public BigDecimal getPercentOfTargetUrlsRequireAlert() {
		return percentOfTargetUrlsRequireAlert;
	}

	public void setPercentOfTargetUrlsRequireAlert(BigDecimal percentOfTargetUrlsRequireAlert) {
		this.percentOfTargetUrlsRequireAlert = percentOfTargetUrlsRequireAlert;
	}

	public Map<String, List<Long>> getAlertNameUrlIdListMap() {
		return alertNameUrlIdListMap;
	}

	public void setAlertNameUrlIdListMap(Map<String, List<Long>> alertNameUrlIdListMap) {
		this.alertNameUrlIdListMap = alertNameUrlIdListMap;
	}

	public Map<String, List<Long>> getResponseCodesUrlIdListMap() {
		return responseCodesUrlIdListMap;
	}

	public void setResponseCodesUrlIdListMap(Map<String, List<Long>> responseCodesUrlIdListMap) {
		this.responseCodesUrlIdListMap = responseCodesUrlIdListMap;
	}

	public Map<Integer, List<Long>> getChangeTypeUrlIdListMap() {
		return changeTypeUrlIdListMap;
	}

	public void setChangeTypeUrlIdListMap(Map<Integer, List<Long>> changeTypeUrlIdListMap) {
		this.changeTypeUrlIdListMap = changeTypeUrlIdListMap;
	}

	public Map<String, List<Long>> getAdditionalContentUrlIdListMap() {
		return additionalContentUrlIdListMap;
	}

	public void setAdditionalContentUrlIdListMap(Map<String, List<Long>> additionalContentUrlIdListMap) {
		this.additionalContentUrlIdListMap = additionalContentUrlIdListMap;
	}

	@Override
	public String toString() {
		return "PageTagContentChangeResults [groupTagId=" + groupTagId + ", groupTagName=" + groupTagName + ", totalTargetUrlsInGroupTag=" + totalTargetUrlsInGroupTag
				+ ", totalTargetUrlsRequireAlert=" + totalTargetUrlsRequireAlert + ", percentOfTargetUrlsRequireAlert=" + percentOfTargetUrlsRequireAlert
				+ ", alertNameUrlIdListMap=" + alertNameUrlIdListMap + ", responseCodesUrlIdListMap=" + responseCodesUrlIdListMap + ", changeTypeUrlIdListMap="
				+ changeTypeUrlIdListMap + ", additionalContentUrlIdListMap=" + additionalContentUrlIdListMap + "]";
	}

}
