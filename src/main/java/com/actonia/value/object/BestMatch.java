package com.actonia.value.object;

public class BestMatch {
	private String area;
	private String best_control;
	private Double relative_distance;
	private Double correlation;
	private Double length;
	private Double sum_test;
	private Double sum_cntl;
	private Double raw_dist;
	private Double correlation_of_logs;
	private Double rank;
	private Double norm_dist;

	public String getArea() {
		return area;
	}

	public void setArea(String area) {
		this.area = area;
	}

	public String getBest_control() {
		return best_control;
	}

	public void setBest_control(String best_control) {
		this.best_control = best_control;
	}

	public Double getRelative_distance() {
		return relative_distance;
	}

	public void setRelative_distance(Double relative_distance) {
		this.relative_distance = relative_distance;
	}

	public Double getCorrelation() {
		return correlation;
	}

	public void setCorrelation(Double correlation) {
		this.correlation = correlation;
	}

	public Double getLength() {
		return length;
	}

	public void setLength(Double length) {
		this.length = length;
	}

	public Double getSum_test() {
		return sum_test;
	}

	public void setSum_test(Double sum_test) {
		this.sum_test = sum_test;
	}

	public Double getSum_cntl() {
		return sum_cntl;
	}

	public void setSum_cntl(Double sum_cntl) {
		this.sum_cntl = sum_cntl;
	}

	public Double getRaw_dist() {
		return raw_dist;
	}

	public void setRaw_dist(Double raw_dist) {
		this.raw_dist = raw_dist;
	}

	public Double getCorrelation_of_logs() {
		return correlation_of_logs;
	}

	public void setCorrelation_of_logs(Double correlation_of_logs) {
		this.correlation_of_logs = correlation_of_logs;
	}

	public Double getRank() {
		return rank;
	}

	public void setRank(Double rank) {
		this.rank = rank;
	}

	public Double getNorm_dist() {
		return norm_dist;
	}

	public void setNorm_dist(Double norm_dist) {
		this.norm_dist = norm_dist;
	}

	@Override
	public String toString() {
		return "BestMatch [area=" + area + ", best_control=" + best_control + ", relative_distance=" + relative_distance + ", correlation=" + correlation + ", length="
				+ length + ", sum_test=" + sum_test + ", sum_cntl=" + sum_cntl + ", raw_dist=" + raw_dist + ", correlation_of_logs=" + correlation_of_logs + ", rank="
				+ rank + ", norm_dist=" + norm_dist + "]";
	}

}
