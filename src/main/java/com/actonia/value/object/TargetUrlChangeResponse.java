package com.actonia.value.object;

import com.actonia.entity.HtmlChange;

import java.util.List;

public class TargetUrlChangeResponse {
	private Boolean success;
	private WebServiceError error;
	private Integer page_number;
	private Integer rows_per_page;
	private Integer sort_by;
	private Integer total_rows;
	private Boolean end_of_detail_list_flag;

	// Change indicator List
	private List<TargetUrlChangeSummary> summary_list;
	private List<TargetUrlChangeDetail> detail_list;
	private List<TargetUrlChangeIndicatorDetail> change_indicator_list;

	public List<HtmlChange> getChange_indicator_list1() {
		return change_indicator_list1;
	}

	public void setChange_indicator_list1(List<HtmlChange> change_indicator_list1) {
		this.change_indicator_list1 = change_indicator_list1;
	}

	private List<HtmlChange> change_indicator_list1;
	private List<ResponseCodeSummary> response_code_summary_list;
	private List<TargetUrlChangeDailySummary> daily_summary_list;

	// URL Summary
	private List<UrlSummary> url_summary_list;

	// change indicator total URLs list
	private List<ChangeIndicatorTotalUrls> change_indicator_total_urls_list;

	private String download_all_link;

	private Long elapsed_millisecond;

	public Boolean getSuccess() {
		return success;
	}

	public void setSuccess(Boolean success) {
		this.success = success;
	}

	public WebServiceError getError() {
		return error;
	}

	public void setError(WebServiceError error) {
		this.error = error;
	}

	public Integer getPage_number() {
		return page_number;
	}

	public void setPage_number(Integer page_number) {
		this.page_number = page_number;
	}

	public Integer getRows_per_page() {
		return rows_per_page;
	}

	public void setRows_per_page(Integer rows_per_page) {
		this.rows_per_page = rows_per_page;
	}

	public Integer getSort_by() {
		return sort_by;
	}

	public void setSort_by(Integer sort_by) {
		this.sort_by = sort_by;
	}

	public Integer getTotal_rows() {
		return total_rows;
	}

	public void setTotal_rows(Integer total_rows) {
		this.total_rows = total_rows;
	}

	public Boolean getEnd_of_detail_list_flag() {
		return end_of_detail_list_flag;
	}

	public void setEnd_of_detail_list_flag(Boolean end_of_detail_list_flag) {
		this.end_of_detail_list_flag = end_of_detail_list_flag;
	}

	public List<TargetUrlChangeSummary> getSummary_list() {
		return summary_list;
	}

	public void setSummary_list(List<TargetUrlChangeSummary> summary_list) {
		this.summary_list = summary_list;
	}

	public List<TargetUrlChangeDetail> getDetail_list() {
		return detail_list;
	}

	public void setDetail_list(List<TargetUrlChangeDetail> detail_list) {
		this.detail_list = detail_list;
	}

	public List<TargetUrlChangeIndicatorDetail> getChange_indicator_list() {
		return change_indicator_list;
	}

	public void setChange_indicator_list(List<TargetUrlChangeIndicatorDetail> change_indicator_list) {
		this.change_indicator_list = change_indicator_list;
	}

	public List<ResponseCodeSummary> getResponse_code_summary_list() {
		return response_code_summary_list;
	}

	public void setResponse_code_summary_list(List<ResponseCodeSummary> response_code_summary_list) {
		this.response_code_summary_list = response_code_summary_list;
	}

	public List<TargetUrlChangeDailySummary> getDaily_summary_list() {
		return daily_summary_list;
	}

	public void setDaily_summary_list(List<TargetUrlChangeDailySummary> daily_summary_list) {
		this.daily_summary_list = daily_summary_list;
	}

	public List<UrlSummary> getUrl_summary_list() {
		return url_summary_list;
	}

	public void setUrl_summary_list(List<UrlSummary> url_summary_list) {
		this.url_summary_list = url_summary_list;
	}

	public List<ChangeIndicatorTotalUrls> getChange_indicator_total_urls_list() {
		return change_indicator_total_urls_list;
	}

	public void setChange_indicator_total_urls_list(List<ChangeIndicatorTotalUrls> change_indicator_total_urls_list) {
		this.change_indicator_total_urls_list = change_indicator_total_urls_list;
	}

	public String getDownload_all_link() {
		return download_all_link;
	}

	public void setDownload_all_link(String download_all_link) {
		this.download_all_link = download_all_link;
	}

	public Long getElapsed_millisecond() {
		return elapsed_millisecond;
	}

	public void setElapsed_millisecond(Long elapsed_millisecond) {
		this.elapsed_millisecond = elapsed_millisecond;
	}

	@Override
	public String toString() {
		return "TargetUrlChangeResponse [success=" + success + ", error=" + error + ", page_number=" + page_number + ", rows_per_page=" + rows_per_page + ", sort_by="
				+ sort_by + ", total_rows=" + total_rows + ", end_of_detail_list_flag=" + end_of_detail_list_flag + ", summary_list=" + summary_list + ", detail_list="
				+ detail_list + ", change_indicator_list=" + change_indicator_list + ", response_code_summary_list=" + response_code_summary_list
				+ ", daily_summary_list=" + daily_summary_list + ", url_summary_list=" + url_summary_list + ", change_indicator_total_urls_list="
				+ change_indicator_total_urls_list + ", download_all_link=" + download_all_link + ", elapsed_millisecond=" + elapsed_millisecond + "]";
	}

}
