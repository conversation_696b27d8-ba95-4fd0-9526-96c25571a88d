package com.actonia.value.object;

public class TargetUrlResponseCodeHourlyTotals {
	private String crawlDateHour;
	private String responseCodePrevious;
	private String responseCodeCurrent;
	private Integer totalUrls;

	public String getCrawlDateHour() {
		return crawlDateHour;
	}

	public void setCrawlDateHour(String crawlDateHour) {
		this.crawlDateHour = crawlDateHour;
	}

	public String getResponseCodePrevious() {
		return responseCodePrevious;
	}

	public void setResponseCodePrevious(String responseCodePrevious) {
		this.responseCodePrevious = responseCodePrevious;
	}

	public String getResponseCodeCurrent() {
		return responseCodeCurrent;
	}

	public void setResponseCodeCurrent(String responseCodeCurrent) {
		this.responseCodeCurrent = responseCodeCurrent;
	}

	public Integer getTotalUrls() {
		return totalUrls;
	}

	public void setTotalUrls(Integer totalUrls) {
		this.totalUrls = totalUrls;
	}

	@Override
	public String toString() {
		return "TargetUrlResponseCodeHourlyTotals [crawlDateHour=" + crawlDateHour + ", responseCodePrevious=" + responseCodePrevious + ", responseCodeCurrent="
				+ responseCodeCurrent + ", totalUrls=" + totalUrls + "]";
	}

}
