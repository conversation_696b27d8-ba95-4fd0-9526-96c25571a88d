
package com.actonia.value.object;

import java.util.Arrays;

import org.apache.commons.lang.StringUtils;

public class TargetUrlChangeContentType {
	private Boolean leaf;
	private String action;
	private String value;
	private Integer level;
	private String cond;
	private TargetUrlChangeContentType[] items;
	private boolean caseSensitive;
	private String predicate;

	public boolean isCaseSensitive() {
		return caseSensitive;
	}

	public void setCaseSensitive(boolean caseSensitive) {
		this.caseSensitive = caseSensitive;
	}

	public String getAction() {
		return action;
	}

	public void setAction(String action) {
		this.action = action;
	}

	public String getValue() {
		if (StringUtils.equals(action, "pt") || StringUtils.equals(action, "npt") || isCaseSensitive()) {
			return value;
		} else {
			return StringUtils.lowerCase(value);
		}
	}

	public void setValue(String value) {
		this.value = value;
	}

	public Boolean getLeaf() {
		return leaf;
	}

	public void setLeaf(Boolean leaf) {
		this.leaf = leaf;
	}

	public TargetUrlChangeContentType[] getItems() {
		return items;
	}

	public void setItems(TargetUrlChangeContentType[] items) {
		this.items = items;
	}

	public String getCond() {
		return cond;
	}

	public void setCond(String cond) {
		this.cond = cond;
	}

	public Integer getLevel() {
		return level;
	}

	public void setLevel(Integer level) {
		this.level = level;
	}

	public String getPredicate() {
		return predicate;
	}

	public void setPredicate(String predicate) {
		this.predicate = predicate;
	}

	@Override
	public String toString() {
		return "TargetUrlChangeContentType [leaf=" + leaf + ", action=" + action + ", value=" + value + ", level=" + level + ", cond=" + cond + ", items="
				+ Arrays.toString(items) + ", caseSensitive=" + caseSensitive + ", predicate=" + predicate + "]";
	}

}
