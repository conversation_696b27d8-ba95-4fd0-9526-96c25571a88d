package com.actonia.value.object;

public class ZapierContentGuardAlert {
	private String id;
	private String alert_timestamp;
	private Integer domain_id;
	private String domain_name;
	private Long group_id;
	private String group_name;
	private String url;
	private String change_desc;
	private String change_severity;
	private String previous_crawl_timestamp;
	private String current_crawl_timestamp;
	private String previous_content;
	private String current_content;
	private String error_code;
	private String error_message;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getAlert_timestamp() {
		return alert_timestamp;
	}

	public void setAlert_timestamp(String alert_timestamp) {
		this.alert_timestamp = alert_timestamp;
	}

	public Integer getDomain_id() {
		return domain_id;
	}

	public void setDomain_id(Integer domain_id) {
		this.domain_id = domain_id;
	}

	public String getDomain_name() {
		return domain_name;
	}

	public void setDomain_name(String domain_name) {
		this.domain_name = domain_name;
	}

	public Long getGroup_id() {
		return group_id;
	}

	public void setGroup_id(Long group_id) {
		this.group_id = group_id;
	}

	public String getGroup_name() {
		return group_name;
	}

	public void setGroup_name(String group_name) {
		this.group_name = group_name;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getChange_desc() {
		return change_desc;
	}

	public void setChange_desc(String change_desc) {
		this.change_desc = change_desc;
	}

	public String getChange_severity() {
		return change_severity;
	}

	public void setChange_severity(String change_severity) {
		this.change_severity = change_severity;
	}

	public String getPrevious_crawl_timestamp() {
		return previous_crawl_timestamp;
	}

	public void setPrevious_crawl_timestamp(String previous_crawl_timestamp) {
		this.previous_crawl_timestamp = previous_crawl_timestamp;
	}

	public String getCurrent_crawl_timestamp() {
		return current_crawl_timestamp;
	}

	public void setCurrent_crawl_timestamp(String current_crawl_timestamp) {
		this.current_crawl_timestamp = current_crawl_timestamp;
	}

	public String getPrevious_content() {
		return previous_content;
	}

	public void setPrevious_content(String previous_content) {
		this.previous_content = previous_content;
	}

	public String getCurrent_content() {
		return current_content;
	}

	public void setCurrent_content(String current_content) {
		this.current_content = current_content;
	}

	public String getError_code() {
		return error_code;
	}

	public void setError_code(String error_code) {
		this.error_code = error_code;
	}

	public String getError_message() {
		return error_message;
	}

	public void setError_message(String error_message) {
		this.error_message = error_message;
	}

	@Override
	public String toString() {
		return "ZapierContentGuardAlertQueueMessage [id=" + id + ", alert_timestamp=" + alert_timestamp + ", domain_id=" + domain_id + ", domain_name=" + domain_name
				+ ", group_id=" + group_id + ", group_name=" + group_name + ", url=" + url + ", change_desc=" + change_desc + ", change_severity=" + change_severity
				+ ", previous_crawl_timestamp=" + previous_crawl_timestamp + ", current_crawl_timestamp=" + current_crawl_timestamp + ", previous_content="
				+ previous_content + ", current_content=" + current_content + ", error_code=" + error_code + ", error_message=" + error_message + "]";
	}

}
