package com.actonia.value.object;

import java.util.Arrays;

public class ResponseCodeSummary {
	private String crawl_date_hour;
	private ResponseCodeTotalUrls[] response_code_total_urls_array;

	public String getCrawl_date_hour() {
		return crawl_date_hour;
	}

	public void setCrawl_date_hour(String crawl_date_hour) {
		this.crawl_date_hour = crawl_date_hour;
	}

	public ResponseCodeTotalUrls[] getResponse_code_total_urls_array() {
		return response_code_total_urls_array;
	}

	public void setResponse_code_total_urls_array(ResponseCodeTotalUrls[] response_code_total_urls_array) {
		this.response_code_total_urls_array = response_code_total_urls_array;
	}

	@Override
	public String toString() {
		return "FromResponseCodeToResponseCodeSummary [crawl_date_hour=" + crawl_date_hour + ", response_code_total_urls_array="
				+ Arrays.toString(response_code_total_urls_array) + "]";
	}

}
