package com.actonia.value.object;

import java.util.List;

public class PageTagResponseCodeChangeAlertEmailValueObject {

	private int processDateNumber;
	private int groupTagId;
	private String groupTagName;
	private String responseCodeChange;
	private int totalTargetUrlsWithContentChanges;
	private int totalTargetUrlsInGroupTag;
	private String percentOfTargetUrlsWithContentChanges;
	private List<Long> targetUrlIdList;
	private int changeType;

	public int getProcessDateNumber() {
		return processDateNumber;
	}

	public void setProcessDateNumber(int processDateNumber) {
		this.processDateNumber = processDateNumber;
	}

	public int getGroupTagId() {
		return groupTagId;
	}

	public void setGroupTagId(int groupTagId) {
		this.groupTagId = groupTagId;
	}

	public String getGroupTagName() {
		return groupTagName;
	}

	public void setGroupTagName(String groupTagName) {
		this.groupTagName = groupTagName;
	}

	public String getResponseCodeChange() {
		return responseCodeChange;
	}

	public void setResponseCodeChange(String responseCodeChange) {
		this.responseCodeChange = responseCodeChange;
	}

	public int getTotalTargetUrlsWithContentChanges() {
		return totalTargetUrlsWithContentChanges;
	}

	public void setTotalTargetUrlsWithContentChanges(int totalTargetUrlsWithContentChanges) {
		this.totalTargetUrlsWithContentChanges = totalTargetUrlsWithContentChanges;
	}

	public int getTotalTargetUrlsInGroupTag() {
		return totalTargetUrlsInGroupTag;
	}

	public void setTotalTargetUrlsInGroupTag(int totalTargetUrlsInGroupTag) {
		this.totalTargetUrlsInGroupTag = totalTargetUrlsInGroupTag;
	}

	public String getPercentOfTargetUrlsWithContentChanges() {
		return percentOfTargetUrlsWithContentChanges;
	}

	public void setPercentOfTargetUrlsWithContentChanges(String percentOfTargetUrlsWithContentChanges) {
		this.percentOfTargetUrlsWithContentChanges = percentOfTargetUrlsWithContentChanges;
	}

	public List<Long> getTargetUrlIdList() {
		return targetUrlIdList;
	}

	public void setTargetUrlIdList(List<Long> targetUrlIdList) {
		this.targetUrlIdList = targetUrlIdList;
	}

	public int getChangeType() {
		return changeType;
	}

	public void setChangeType(int changeType) {
		this.changeType = changeType;
	}

	@Override
	public String toString() {
		return " PageTagResponseCodeChangeAlertEmailValueObject [processDateNumber=" + processDateNumber + ", groupTagId=" + groupTagId
				+ ", groupTagName=" + groupTagName + ", responseCodeChange=" + responseCodeChange + ", totalTargetUrlsWithContentChanges="
				+ totalTargetUrlsWithContentChanges + ", totalTargetUrlsInGroupTag=" + totalTargetUrlsInGroupTag
				+ ", percentOfTargetUrlsWithContentChanges=" + percentOfTargetUrlsWithContentChanges + ", targetUrlIdList=" + targetUrlIdList
				+ ", changeType=" + changeType + "]";
	}

}
