package com.actonia.value.object;

public class PageLink {
	private Integer index;
	private String anchor_text;
	private String destination_url;
	private Boolean nofollow_link;
	private Boolean header_link;
	private Boolean footer_link;
	private Boolean hidden_link;
	private Boolean image_link;

	public Integer getIndex() {
		return index;
	}

	public void setIndex(Integer index) {
		this.index = index;
	}

	public String getAnchor_text() {
		return anchor_text;
	}

	public void setAnchor_text(String anchor_text) {
		this.anchor_text = anchor_text;
	}

	public String getDestination_url() {
		return destination_url;
	}

	public void setDestination_url(String destination_url) {
		this.destination_url = destination_url;
	}

	public Boolean getNofollow_link() {
		return nofollow_link;
	}

	public void setNofollow_link(Boolean nofollow_link) {
		this.nofollow_link = nofollow_link;
	}

	public Boolean getHeader_link() {
		return header_link;
	}

	public void setHeader_link(Boolean header_link) {
		this.header_link = header_link;
	}

	public Boolean getFooter_link() {
		return footer_link;
	}

	public void setFooter_link(Boolean footer_link) {
		this.footer_link = footer_link;
	}

	public Boolean getHidden_link() {
		return hidden_link;
	}

	public void setHidden_link(Boolean hidden_link) {
		this.hidden_link = hidden_link;
	}

	public Boolean getImage_link() {
		return image_link;
	}

	public void setImage_link(Boolean image_link) {
		this.image_link = image_link;
	}

	@Override
	public String toString() {
		return "PageLink [index=" + index + ", anchor_text=" + anchor_text + ", destination_url=" + destination_url + ", nofollow_link=" + nofollow_link
				+ ", header_link=" + header_link + ", footer_link=" + footer_link + ", hidden_link=" + hidden_link + ", image_link=" + image_link + "]";
	}

}
