package com.actonia.value.object;

public class ContentGuardSeverity {
	private Integer critical_flag;
	private Integer total_changes;

	public Integer getCritical_flag() {
		return critical_flag;
	}

	public void setCritical_flag(Integer critical_flag) {
		this.critical_flag = critical_flag;
	}

	public Integer getTotal_changes() {
		return total_changes;
	}

	public void setTotal_changes(Integer total_changes) {
		this.total_changes = total_changes;
	}

	@Override
	public String toString() {
		return "Severity [critical_flag=" + critical_flag + ", total_changes=" + total_changes + "]";
	}

}
