package com.actonia.value.object;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import com.actonia.web.service.ContentGuardTrackedGroup;

public class ContentGuardResourceResponse {

	private Boolean success;
	private WebServiceError error;

	private Long group_id;

	private Integer page_number;
	private Integer rows_per_page;
	private Integer sort_by;
	private Boolean end_of_url_changes_list_flag;
	private Boolean end_of_indicator_url_list_flag;
	private Boolean end_of_url_crawl_history_flag;
	private Boolean end_of_tracked_pages_flag;
	private String filter_change_indicator;
	private String filter_url;
	private Integer filter_url_totals;

	private String start_crawl_timestamp;
	private String end_crawl_timestamp;
	private Integer total_pages_tracked;
	private Integer total_pages_changed;
	private Integer total_changes;
	private Integer total_changes_added;
	private Integer total_changes_modified;
	private Integer total_changes_removed;

	//*****************//
	// timeline begins //
	//*****************//

	private ContentGuardDailyGroupTimeline[] daily_group_timeline_list;
	private ContentGuardDailyGroupSeverity[] daily_group_severity_list;

	private ContentGuardHourlyGroupTimeline[] hourly_group_timeline_list;
	private ContentGuardHourlyGroupSeverity[] hourly_group_severity_list;

	//***************//
	// timeline ends //
	//***************//

	//***********************//
	// domain_summary begins //
	//***********************//

	// map key - change tracking indicator
	// map value - total number of pages
	private Map<String, Integer> change_tracking_indicator_totals_map;

	// map key - severity
	// map value - totals changes in group
	private Map<Integer, Integer> change_severity;

	private List<ContentGuardUrlChanges> url_changes_list;

	//*********************//
	// domain_summary ends //
	//*********************//

	//***************************//
	// indicator_url_list begins //
	//***************************//

	private List<ContentGuardIndicatorUrlChanges> indicator_url_list;

	//*************************//
	// indicator_url_list ends //
	//*************************//

	//********************//
	// url_details begins //
	//********************//

	private String url;
	private String previous_crawl_timestamp;
	private String current_crawl_timestamp;
	private List<ContentGuardChangeDetails> url_change_details_list = null;

	//******************//
	// url_details ends //
	//******************//

	//************************//
	// url_all_details begins //
	//************************//

	private Integer total_crawl_timestamps;
	private ContentGuardUrlCrawlHistory[] url_crawl_history;

	//**********************//
	// url_all_details ends //
	//**********************//

	//***********************************//
	// page_analysis_issues_count begins //
	//***********************************//

	private Integer total_page_analysis_issues;
	private UrlPageAnalysisResults[] url_page_analysis_results_list;

	//*********************************//
	// page_analysis_issues_count ends //
	//*********************************//

	private Long elapsed_millisecond;

	private ContentGuardTrackedGroup[] tracked_group_list;

	private ContentGuardGroupUsage[] group_usage_list;

	public Boolean getSuccess() {
		return success;
	}

	public void setSuccess(Boolean success) {
		this.success = success;
	}

	public WebServiceError getError() {
		return error;
	}

	public void setError(WebServiceError error) {
		this.error = error;
	}

	public Long getGroup_id() {
		return group_id;
	}

	public void setGroup_id(Long group_id) {
		this.group_id = group_id;
	}

	public Integer getPage_number() {
		return page_number;
	}

	public void setPage_number(Integer page_number) {
		this.page_number = page_number;
	}

	public Integer getRows_per_page() {
		return rows_per_page;
	}

	public void setRows_per_page(Integer rows_per_page) {
		this.rows_per_page = rows_per_page;
	}

	public Integer getSort_by() {
		return sort_by;
	}

	public void setSort_by(Integer sort_by) {
		this.sort_by = sort_by;
	}

	public Boolean getEnd_of_url_changes_list_flag() {
		return end_of_url_changes_list_flag;
	}

	public void setEnd_of_url_changes_list_flag(Boolean end_of_url_changes_list_flag) {
		this.end_of_url_changes_list_flag = end_of_url_changes_list_flag;
	}

	public Boolean getEnd_of_indicator_url_list_flag() {
		return end_of_indicator_url_list_flag;
	}

	public void setEnd_of_indicator_url_list_flag(Boolean end_of_indicator_url_list_flag) {
		this.end_of_indicator_url_list_flag = end_of_indicator_url_list_flag;
	}

	public Boolean getEnd_of_url_crawl_history_flag() {
		return end_of_url_crawl_history_flag;
	}

	public void setEnd_of_url_crawl_history_flag(Boolean end_of_url_crawl_history_flag) {
		this.end_of_url_crawl_history_flag = end_of_url_crawl_history_flag;
	}

	public Boolean getEnd_of_tracked_pages_flag() {
		return end_of_tracked_pages_flag;
	}

	public void setEnd_of_tracked_pages_flag(Boolean end_of_tracked_pages_flag) {
		this.end_of_tracked_pages_flag = end_of_tracked_pages_flag;
	}

	public String getFilter_change_indicator() {
		return filter_change_indicator;
	}

	public void setFilter_change_indicator(String filter_change_indicator) {
		this.filter_change_indicator = filter_change_indicator;
	}

	public String getFilter_url() {
		return filter_url;
	}

	public void setFilter_url(String filter_url) {
		this.filter_url = filter_url;
	}

	public Integer getFilter_url_totals() {
		return filter_url_totals;
	}

	public void setFilter_url_totals(Integer filter_url_totals) {
		this.filter_url_totals = filter_url_totals;
	}

	public String getStart_crawl_timestamp() {
		return start_crawl_timestamp;
	}

	public void setStart_crawl_timestamp(String start_crawl_timestamp) {
		this.start_crawl_timestamp = start_crawl_timestamp;
	}

	public String getEnd_crawl_timestamp() {
		return end_crawl_timestamp;
	}

	public void setEnd_crawl_timestamp(String end_crawl_timestamp) {
		this.end_crawl_timestamp = end_crawl_timestamp;
	}

	public Integer getTotal_pages_tracked() {
		return total_pages_tracked;
	}

	public void setTotal_pages_tracked(Integer total_pages_tracked) {
		this.total_pages_tracked = total_pages_tracked;
	}

	public Integer getTotal_pages_changed() {
		return total_pages_changed;
	}

	public void setTotal_pages_changed(Integer total_pages_changed) {
		this.total_pages_changed = total_pages_changed;
	}

	public Integer getTotal_changes() {
		return total_changes;
	}

	public void setTotal_changes(Integer total_changes) {
		this.total_changes = total_changes;
	}

	public Integer getTotal_changes_added() {
		return total_changes_added;
	}

	public void setTotal_changes_added(Integer total_changes_added) {
		this.total_changes_added = total_changes_added;
	}

	public Integer getTotal_changes_modified() {
		return total_changes_modified;
	}

	public void setTotal_changes_modified(Integer total_changes_modified) {
		this.total_changes_modified = total_changes_modified;
	}

	public Integer getTotal_changes_removed() {
		return total_changes_removed;
	}

	public void setTotal_changes_removed(Integer total_changes_removed) {
		this.total_changes_removed = total_changes_removed;
	}

	public ContentGuardDailyGroupTimeline[] getDaily_group_timeline_list() {
		return daily_group_timeline_list;
	}

	public void setDaily_group_timeline_list(ContentGuardDailyGroupTimeline[] daily_group_timeline_list) {
		this.daily_group_timeline_list = daily_group_timeline_list;
	}

	public ContentGuardDailyGroupSeverity[] getDaily_group_severity_list() {
		return daily_group_severity_list;
	}

	public void setDaily_group_severity_list(ContentGuardDailyGroupSeverity[] daily_group_severity_list) {
		this.daily_group_severity_list = daily_group_severity_list;
	}

	public ContentGuardHourlyGroupTimeline[] getHourly_group_timeline_list() {
		return hourly_group_timeline_list;
	}

	public void setHourly_group_timeline_list(ContentGuardHourlyGroupTimeline[] hourly_group_timeline_list) {
		this.hourly_group_timeline_list = hourly_group_timeline_list;
	}

	public ContentGuardHourlyGroupSeverity[] getHourly_group_severity_list() {
		return hourly_group_severity_list;
	}

	public void setHourly_group_severity_list(ContentGuardHourlyGroupSeverity[] hourly_group_severity_list) {
		this.hourly_group_severity_list = hourly_group_severity_list;
	}

	public Map<String, Integer> getChange_tracking_indicator_totals_map() {
		return change_tracking_indicator_totals_map;
	}

	public void setChange_tracking_indicator_totals_map(Map<String, Integer> change_tracking_indicator_totals_map) {
		this.change_tracking_indicator_totals_map = change_tracking_indicator_totals_map;
	}

	public Map<Integer, Integer> getChange_severity() {
		return change_severity;
	}

	public void setChange_severity(Map<Integer, Integer> change_severity) {
		this.change_severity = change_severity;
	}

	public List<ContentGuardUrlChanges> getUrl_changes_list() {
		return url_changes_list;
	}

	public void setUrl_changes_list(List<ContentGuardUrlChanges> url_changes_list) {
		this.url_changes_list = url_changes_list;
	}

	public List<ContentGuardIndicatorUrlChanges> getIndicator_url_list() {
		return indicator_url_list;
	}

	public void setIndicator_url_list(List<ContentGuardIndicatorUrlChanges> indicator_url_list) {
		this.indicator_url_list = indicator_url_list;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getPrevious_crawl_timestamp() {
		return previous_crawl_timestamp;
	}

	public void setPrevious_crawl_timestamp(String previous_crawl_timestamp) {
		this.previous_crawl_timestamp = previous_crawl_timestamp;
	}

	public String getCurrent_crawl_timestamp() {
		return current_crawl_timestamp;
	}

	public void setCurrent_crawl_timestamp(String current_crawl_timestamp) {
		this.current_crawl_timestamp = current_crawl_timestamp;
	}

	public List<ContentGuardChangeDetails> getUrl_change_details_list() {
		return url_change_details_list;
	}

	public void setUrl_change_details_list(List<ContentGuardChangeDetails> url_change_details_list) {
		this.url_change_details_list = url_change_details_list;
	}

	public Integer getTotal_crawl_timestamps() {
		return total_crawl_timestamps;
	}

	public void setTotal_crawl_timestamps(Integer total_crawl_timestamps) {
		this.total_crawl_timestamps = total_crawl_timestamps;
	}

	public ContentGuardUrlCrawlHistory[] getUrl_crawl_history() {
		return url_crawl_history;
	}

	public void setUrl_crawl_history(ContentGuardUrlCrawlHistory[] url_crawl_history) {
		this.url_crawl_history = url_crawl_history;
	}

	public Integer getTotal_page_analysis_issues() {
		return total_page_analysis_issues;
	}

	public void setTotal_page_analysis_issues(Integer total_page_analysis_issues) {
		this.total_page_analysis_issues = total_page_analysis_issues;
	}

	public UrlPageAnalysisResults[] getUrl_page_analysis_results_list() {
		return url_page_analysis_results_list;
	}

	public void setUrl_page_analysis_results_list(UrlPageAnalysisResults[] url_page_analysis_results_list) {
		this.url_page_analysis_results_list = url_page_analysis_results_list;
	}

	public Long getElapsed_millisecond() {
		return elapsed_millisecond;
	}

	public void setElapsed_millisecond(Long elapsed_millisecond) {
		this.elapsed_millisecond = elapsed_millisecond;
	}

	public ContentGuardTrackedGroup[] getTracked_group_list() {
		return tracked_group_list;
	}

	public void setTracked_group_list(ContentGuardTrackedGroup[] tracked_group_list) {
		this.tracked_group_list = tracked_group_list;
	}

	public ContentGuardGroupUsage[] getGroup_usage_list() {
		return group_usage_list;
	}

	public void setGroup_usage_list(ContentGuardGroupUsage[] group_usage_list) {
		this.group_usage_list = group_usage_list;
	}

	@Override
	public String toString() {
		return "ContentGuardResourceResponse [success=" + success + ", error=" + error + ", group_id=" + group_id + ", page_number=" + page_number + ", rows_per_page="
				+ rows_per_page + ", sort_by=" + sort_by + ", end_of_url_changes_list_flag=" + end_of_url_changes_list_flag + ", end_of_indicator_url_list_flag="
				+ end_of_indicator_url_list_flag + ", end_of_url_crawl_history_flag=" + end_of_url_crawl_history_flag + ", end_of_tracked_pages_flag="
				+ end_of_tracked_pages_flag + ", filter_change_indicator=" + filter_change_indicator + ", filter_url=" + filter_url + ", filter_url_totals="
				+ filter_url_totals + ", start_crawl_timestamp=" + start_crawl_timestamp + ", end_crawl_timestamp=" + end_crawl_timestamp + ", total_pages_tracked="
				+ total_pages_tracked + ", total_pages_changed=" + total_pages_changed + ", total_changes=" + total_changes + ", total_changes_added="
				+ total_changes_added + ", total_changes_modified=" + total_changes_modified + ", total_changes_removed=" + total_changes_removed
				+ ", daily_group_timeline_list=" + Arrays.toString(daily_group_timeline_list) + ", daily_group_severity_list="
				+ Arrays.toString(daily_group_severity_list) + ", hourly_group_timeline_list=" + Arrays.toString(hourly_group_timeline_list)
				+ ", hourly_group_severity_list=" + Arrays.toString(hourly_group_severity_list) + ", change_tracking_indicator_totals_map="
				+ change_tracking_indicator_totals_map + ", change_severity=" + change_severity + ", url_changes_list=" + url_changes_list + ", indicator_url_list="
				+ indicator_url_list + ", url=" + url + ", previous_crawl_timestamp=" + previous_crawl_timestamp + ", current_crawl_timestamp="
				+ current_crawl_timestamp + ", url_change_details_list=" + url_change_details_list + ", total_crawl_timestamps=" + total_crawl_timestamps
				+ ", url_crawl_history=" + Arrays.toString(url_crawl_history) + ", total_page_analysis_issues=" + total_page_analysis_issues
				+ ", url_page_analysis_results_list=" + Arrays.toString(url_page_analysis_results_list) + ", elapsed_millisecond=" + elapsed_millisecond
				+ ", tracked_group_list=" + Arrays.toString(tracked_group_list) + ", group_usage_list=" + Arrays.toString(group_usage_list) + "]";
	}

}
