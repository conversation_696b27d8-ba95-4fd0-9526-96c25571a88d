package com.actonia.value.object;

import java.util.List;

public class TargetUrlChangeAlertSummary {
	private int pageTagId;
	private String pageTagName;
	private List<ChangeIndicatorTotalChanges> changeIndicatorTotalChangesAddedList;
	private List<ChangeIndicatorTotalChanges> changeIndicatorTotalChangesModifiedList;
	private List<ChangeIndicatorTotalChanges> changeIndicatorTotalChangesRemovedList;

	public int getPageTagId() {
		return pageTagId;
	}

	public void setPageTagId(int pageTagId) {
		this.pageTagId = pageTagId;
	}

	public String getPageTagName() {
		return pageTagName;
	}

	public void setPageTagName(String pageTagName) {
		this.pageTagName = pageTagName;
	}

	public List<ChangeIndicatorTotalChanges> getChangeIndicatorTotalChangesAddedList() {
		return changeIndicatorTotalChangesAddedList;
	}

	public void setChangeIndicatorTotalChangesAddedList(List<ChangeIndicatorTotalChanges> changeIndicatorTotalChangesAddedList) {
		this.changeIndicatorTotalChangesAddedList = changeIndicatorTotalChangesAddedList;
	}

	public List<ChangeIndicatorTotalChanges> getChangeIndicatorTotalChangesModifiedList() {
		return changeIndicatorTotalChangesModifiedList;
	}

	public void setChangeIndicatorTotalChangesModifiedList(List<ChangeIndicatorTotalChanges> changeIndicatorTotalChangesModifiedList) {
		this.changeIndicatorTotalChangesModifiedList = changeIndicatorTotalChangesModifiedList;
	}

	public List<ChangeIndicatorTotalChanges> getChangeIndicatorTotalChangesRemovedList() {
		return changeIndicatorTotalChangesRemovedList;
	}

	public void setChangeIndicatorTotalChangesRemovedList(List<ChangeIndicatorTotalChanges> changeIndicatorTotalChangesRemovedList) {
		this.changeIndicatorTotalChangesRemovedList = changeIndicatorTotalChangesRemovedList;
	}

	@Override
	public String toString() {
		return "TargetUrlChangeAlertSummary [pageTagId=" + pageTagId + ", pageTagName=" + pageTagName + ", changeIndicatorTotalChangesAddedList="
				+ changeIndicatorTotalChangesAddedList + ", changeIndicatorTotalChangesModifiedList=" + changeIndicatorTotalChangesModifiedList
				+ ", changeIndicatorTotalChangesRemovedList=" + changeIndicatorTotalChangesRemovedList + "]";
	}

}
