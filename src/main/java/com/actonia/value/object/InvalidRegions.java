package com.actonia.value.object;

import java.util.Arrays;

public class InvalidRegions {
	private Entries[] entries;
	private boolean status;
	public Entries[] getEntries() {
		return entries;
	}
	public void setEntries(Entries[] entries) {
		this.entries = entries;
	}
	public boolean isStatus() {
		return status;
	}
	public void setStatus(boolean status) {
		this.status = status;
	}
	@Override
	public String toString() {
		return "InvalidRegions [entries=" + Arrays.toString(entries) + ", status=" + status + "]";
	}

}
