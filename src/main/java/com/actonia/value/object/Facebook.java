package com.actonia.value.object;

public class Facebook {
	private Integer total_count;
	private Integer comment_count;
	private Integer comment_plugin_count;
	private Integer share_count;
	private Integer reaction_count;

	public Integer getTotal_count() {
		return total_count;
	}

	public void setTotal_count(Integer total_count) {
		this.total_count = total_count;
	}

	public Integer getComment_count() {
		return comment_count;
	}

	public void setComment_count(Integer comment_count) {
		this.comment_count = comment_count;
	}

	public Integer getComment_plugin_count() {
		return comment_plugin_count;
	}

	public void setComment_plugin_count(Integer comment_plugin_count) {
		this.comment_plugin_count = comment_plugin_count;
	}

	public Integer getShare_count() {
		return share_count;
	}

	public void setShare_count(Integer share_count) {
		this.share_count = share_count;
	}

	public Integer getReaction_count() {
		return reaction_count;
	}

	public void setReaction_count(Integer reaction_count) {
		this.reaction_count = reaction_count;
	}

	@Override
	public String toString() {
		return "Facebook [total_count=" + total_count + ", comment_count=" + comment_count + ", comment_plugin_count=" + comment_plugin_count
				+ ", share_count=" + share_count + ", reaction_count=" + reaction_count + "]";
	}

}
