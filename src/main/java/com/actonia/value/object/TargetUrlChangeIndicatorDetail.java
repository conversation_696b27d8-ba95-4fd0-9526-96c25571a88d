package com.actonia.value.object;

import lombok.Data;

@Data
public class TargetUrlChangeIndicatorDetail {
	private String url;
	private String url_hash;
	private String url_murmur_hash;
	private String current_crawl_timestamp;
	private String previous_crawl_timestamp;
	private String change_indicator;
	private String change_type;
	private Integer critical_ind;
	private String severity;
	private AlternateLinks[] alternate_links_current;
	private AlternateLinks[] alternate_links_previous;
	private String amphtml_href_current;
	private String amphtml_href_previous;
	private String analyzed_url_s_current;
	private String analyzed_url_s_previous;
	private String archive_flg_current;
	private String archive_flg_previous;
	private String base_tag_current;
	private String base_tag_previous;
	private String base_tag_target_current;
	private String base_tag_target_previous;
	private String blocked_by_robots_current;
	private String blocked_by_robots_previous;
	private String canonical_current;
	private String canonical_previous;
	private Boolean canonical_header_flag_current;
	private Boolean canonical_header_flag_previous;
	private String canonical_header_type_current;
	private String canonical_header_type_previous;
	private String canonical_type_current;
	private String canonical_type_previous;
	private String canonical_url_is_consistent_current;
	private String canonical_url_is_consistent_previous;
	private String content_type_current;
	private String content_type_previous;
	private CustomData[] custom_data_current;
	private CustomData[] custom_data_previous;
	private String description_current;
	private String description_previous;
	private Integer description_length_current;
	private Integer description_length_previous;
	private String error_message_current;
	private String error_message_previous;
	private Integer final_response_code_current;
	private Integer final_response_code_previous;
	private String follow_flg_current;
	private String follow_flg_previous;
	private HtmlHeading[] h1_current;
	private HtmlHeading[] h1_previous;
	private Integer h1_count_current;
	private Integer h1_count_previous;
	private Integer h1_length_current;
	private Integer h1_length_previous;
	private HtmlHeading[] h2_current;
	private HtmlHeading[] h2_previous;
	private Boolean header_noarchive_current;
	private Boolean header_noarchive_previous;
	private Boolean header_nofollow_current;
	private Boolean header_nofollow_previous;
	private Boolean header_noindex_current;
	private Boolean header_noindex_previous;
	private Boolean header_noodp_current;
	private Boolean header_noodp_previous;
	private Boolean header_nosnippet_current;
	private Boolean header_nosnippet_previous;
	private Boolean header_noydir_current;
	private Boolean header_noydir_previous;
	private HreflangErrors hreflang_errors_current;
	private HreflangErrors hreflang_errors_previous;
	private HreflangLinks[] hreflang_links_current;
	private HreflangLinks[] hreflang_links_previous;
	private Integer hreflang_links_out_count_current;
	private Integer hreflang_links_out_count_previous;
	private Integer hreflang_url_count_current;
	private Integer hreflang_url_count_previous;
	private String index_flg_current;
	private String index_flg_previous;
	private Boolean indexable_current;
	private Boolean indexable_previous;
	private String[] insecure_resources_current;
	private String[] insecure_resources_previous;
	private String meta_charset_current;
	private String meta_charset_previous;
	private String meta_content_type_current;
	private String meta_content_type_previous;
	private Boolean meta_disabled_sitelinks_current;
	private Boolean meta_disabled_sitelinks_previous;
	private Boolean meta_noodp_current;
	private Boolean meta_noodp_previous;
	private Boolean meta_nosnippet_current;
	private Boolean meta_nosnippet_previous;
	private Boolean meta_noydir_current;
	private Boolean meta_noydir_previous;
	private Boolean meta_redirect_current;
	private Boolean meta_redirect_previous;
	private Boolean mixed_redirects_current;
	private Boolean mixed_redirects_previous;
	private Boolean mobile_rel_alternate_url_is_consistent_current;
	private Boolean mobile_rel_alternate_url_is_consistent_previous;
	private Boolean noodp_current;
	private Boolean noodp_previous;
	private Boolean nosnippet_current;
	private Boolean nosnippet_previous;
	private Boolean noydir_current;
	private Boolean noydir_previous;
	private OgMarkup[] og_markup_current;
	private OgMarkup[] og_markup_previous;
	private Integer og_markup_length_current;
	private Integer og_markup_length_previous;
	private Integer outlink_count_current;
	private Integer outlink_count_previous;
	private String pageAnalysisResultsChgIndJson;
	private String page_analysis_results_chg_ind_json;
	private PageLink[] page_link_current;
	private PageLink[] page_link_previous;
	private Boolean redirect_blocked_current;
	private Boolean redirect_blocked_previous;
	private String redirect_blocked_reason_current;
	private String redirect_blocked_reason_previous;
	private RedirectChain[] redirect_chain_current;
	private RedirectChain[] redirect_chain_previous;
	private String redirect_final_url_current;
	private String redirect_final_url_previous;
	private Integer redirect_times_current;
	private Integer redirect_times_previous;
	private String response_code_current;
	private String response_code_previous;
	private ResponseHeaders[] response_headers_current;
	private ResponseHeaders[] response_headers_previous;
	private String robots_contents_current;
	private String robots_contents_previous;
	private StructuredData structured_data_current;
	private StructuredData structured_data_previous;
	private String title_current;
	private String title_previous;
	private Integer title_length_current;
	private Integer title_length_previous;
	private String viewport_content_current;
	private String viewport_content_previous;
	private String robots_txt_current;
	private String robots_txt_previous;
}
