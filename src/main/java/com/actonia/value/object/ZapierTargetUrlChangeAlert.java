package com.actonia.value.object;

public class ZapierTargetUrlChangeAlert {
	private String id;
	private Integer domain_id;
	private String domain_name;
	private Integer page_tag_id;
	private String page_tag_name;
	private String message;
	private String change_description;
	private Integer total_urls;
	private String error_code;
	private String error_message;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Integer getDomain_id() {
		return domain_id;
	}

	public void setDomain_id(Integer domain_id) {
		this.domain_id = domain_id;
	}

	public String getDomain_name() {
		return domain_name;
	}

	public void setDomain_name(String domain_name) {
		this.domain_name = domain_name;
	}

	public Integer getPage_tag_id() {
		return page_tag_id;
	}

	public void setPage_tag_id(Integer page_tag_id) {
		this.page_tag_id = page_tag_id;
	}

	public String getPage_tag_name() {
		return page_tag_name;
	}

	public void setPage_tag_name(String page_tag_name) {
		this.page_tag_name = page_tag_name;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public String getChange_description() {
		return change_description;
	}

	public void setChange_description(String change_description) {
		this.change_description = change_description;
	}

	public Integer getTotal_urls() {
		return total_urls;
	}

	public void setTotal_urls(Integer total_urls) {
		this.total_urls = total_urls;
	}

	public String getError_code() {
		return error_code;
	}

	public void setError_code(String error_code) {
		this.error_code = error_code;
	}

	public String getError_message() {
		return error_message;
	}

	public void setError_message(String error_message) {
		this.error_message = error_message;
	}

	@Override
	public String toString() {
		return "ZapierTargetUrlChangeAlert [id=" + id + ", domain_id=" + domain_id + ", domain_name=" + domain_name + ", page_tag_id=" + page_tag_id
				+ ", page_tag_name=" + page_tag_name + ", message=" + message + ", change_description=" + change_description + ", total_urls=" + total_urls
				+ ", error_code=" + error_code + ", error_message=" + error_message + "]";
	}

}
