package com.actonia.value.object;

import java.util.List;

public class ContentGuardUrlCrawlHistory {
	private String previous_crawl_timestamp;
	private String current_crawl_timestamp;
	private List<ContentGuardChangeDetails> url_change_details_List;

	public String getPrevious_crawl_timestamp() {
		return previous_crawl_timestamp;
	}

	public void setPrevious_crawl_timestamp(String previous_crawl_timestamp) {
		this.previous_crawl_timestamp = previous_crawl_timestamp;
	}

	public String getCurrent_crawl_timestamp() {
		return current_crawl_timestamp;
	}

	public void setCurrent_crawl_timestamp(String current_crawl_timestamp) {
		this.current_crawl_timestamp = current_crawl_timestamp;
	}

	public List<ContentGuardChangeDetails> getUrl_change_details_List() {
		return url_change_details_List;
	}

	public void setUrl_change_details_List(List<ContentGuardChangeDetails> url_change_details_List) {
		this.url_change_details_List = url_change_details_List;
	}

	@Override
	public String toString() {
		return "ContentGuardUrlCrawlHistory [previous_crawl_timestamp=" + previous_crawl_timestamp + ", current_crawl_timestamp=" + current_crawl_timestamp
				+ ", url_change_details_List=" + url_change_details_List + "]";
	}

}
