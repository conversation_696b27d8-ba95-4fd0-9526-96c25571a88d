package com.actonia.value.object;

import java.util.Date;

public class LinkClarityDataRemovalValueObject {

	private Date trackDate;
	private Integer domainId;
	private String targetUrl;
	private String sourceUrl;

	public Date getTrackDate() {
		return trackDate;
	}

	public void setTrackDate(Date trackDate) {
		this.trackDate = trackDate;
	}

	public Integer getDomainId() {
		return domainId;
	}

	public void setDomainId(Integer domainId) {
		this.domainId = domainId;
	}

	public String getTargetUrl() {
		return targetUrl;
	}

	public void setTargetUrl(String targetUrl) {
		this.targetUrl = targetUrl;
	}

	public String getSourceUrl() {
		return sourceUrl;
	}

	public void setSourceUrl(String sourceUrl) {
		this.sourceUrl = sourceUrl;
	}

	@Override
	public String toString() {
		return "LinkClarityDataRemovalValueObject [trackDate=" + trackDate + ", domainId=" + domainId + ", targetUrl=" + targetUrl + ", sourceUrl=" + sourceUrl + "]";
	}

}
