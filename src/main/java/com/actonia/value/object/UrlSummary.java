package com.actonia.value.object;

public class UrlSummary {
	private String url;
	private Integer total_changes;
	private Integer total_severity_critical;
	private Integer total_severity_high;
	private Integer total_severity_medium;
	private Integer total_severity_low;
	private Integer total_change_type_added;
	private Integer total_change_type_modified;
	private Integer total_change_type_removed;
	private String url_murmur_hash;

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public Integer getTotal_changes() {
		return total_changes;
	}

	public void setTotal_changes(Integer total_changes) {
		this.total_changes = total_changes;
	}

	public Integer getTotal_severity_critical() {
		return total_severity_critical;
	}

	public void setTotal_severity_critical(Integer total_severity_critical) {
		this.total_severity_critical = total_severity_critical;
	}

	public Integer getTotal_severity_high() {
		return total_severity_high;
	}

	public void setTotal_severity_high(Integer total_severity_high) {
		this.total_severity_high = total_severity_high;
	}

	public Integer getTotal_severity_medium() {
		return total_severity_medium;
	}

	public void setTotal_severity_medium(Integer total_severity_medium) {
		this.total_severity_medium = total_severity_medium;
	}

	public Integer getTotal_severity_low() {
		return total_severity_low;
	}

	public void setTotal_severity_low(Integer total_severity_low) {
		this.total_severity_low = total_severity_low;
	}

	public Integer getTotal_change_type_added() {
		return total_change_type_added;
	}

	public void setTotal_change_type_added(Integer total_change_type_added) {
		this.total_change_type_added = total_change_type_added;
	}

	public Integer getTotal_change_type_modified() {
		return total_change_type_modified;
	}

	public void setTotal_change_type_modified(Integer total_change_type_modified) {
		this.total_change_type_modified = total_change_type_modified;
	}

	public Integer getTotal_change_type_removed() {
		return total_change_type_removed;
	}

	public void setTotal_change_type_removed(Integer total_change_type_removed) {
		this.total_change_type_removed = total_change_type_removed;
	}

	public String getUrl_murmur_hash() {
		return url_murmur_hash;
	}

	public void setUrl_murmur_hash(String url_murmur_hash) {
		this.url_murmur_hash = url_murmur_hash;
	}

	@Override
	public String toString() {
		return "UrlSummary [url=" + url + ", total_changes=" + total_changes + ", total_severity_critical=" + total_severity_critical + ", total_severity_high="
				+ total_severity_high + ", total_severity_medium=" + total_severity_medium + ", total_severity_low=" + total_severity_low + ", total_change_type_added="
				+ total_change_type_added + ", total_change_type_modified=" + total_change_type_modified + ", total_change_type_removed=" + total_change_type_removed
				+ ", url_murmur_hash=" + url_murmur_hash + "]";
	}

}
