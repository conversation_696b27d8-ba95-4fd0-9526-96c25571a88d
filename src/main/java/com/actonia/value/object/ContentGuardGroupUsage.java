package com.actonia.value.object;

import java.util.Arrays;

public class ContentGuardGroupUsage {
	private Long group_id;
	private String group_name;
	private Integer crawl_frequency;
	private ContentGuardUsage[] usage_list;

	public Long getGroup_id() {
		return group_id;
	}

	public void setGroup_id(Long group_id) {
		this.group_id = group_id;
	}

	public String getGroup_name() {
		return group_name;
	}

	public void setGroup_name(String group_name) {
		this.group_name = group_name;
	}

	public Integer getCrawl_frequency() {
		return crawl_frequency;
	}

	public void setCrawl_frequency(Integer crawl_frequency) {
		this.crawl_frequency = crawl_frequency;
	}

	public ContentGuardUsage[] getUsage_list() {
		return usage_list;
	}

	public void setUsage_list(ContentGuardUsage[] usage_list) {
		this.usage_list = usage_list;
	}

	@Override
	public String toString() {
		return "ContentGuardGroupUsage [group_id=" + group_id + ", group_name=" + group_name + ", crawl_frequency=" + crawl_frequency + ", usage_list="
				+ Arrays.toString(usage_list) + "]";
	}

}
