package com.actonia.value.object;

public class ContentGuardIndicatorUrlChanges extends ContentGuardChangeDetails {
	private String url;
	private String previous_crawl_timestamp;
	private String current_crawl_timestamp;
	private String hash_cd;

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getPrevious_crawl_timestamp() {
		return previous_crawl_timestamp;
	}

	public void setPrevious_crawl_timestamp(String previous_crawl_timestamp) {
		this.previous_crawl_timestamp = previous_crawl_timestamp;
	}

	public String getCurrent_crawl_timestamp() {
		return current_crawl_timestamp;
	}

	public void setCurrent_crawl_timestamp(String current_crawl_timestamp) {
		this.current_crawl_timestamp = current_crawl_timestamp;
	}

	public String getHash_cd() {
		return hash_cd;
	}

	public void setHash_cd(String hash_cd) {
		this.hash_cd = hash_cd;
	}

	@Override
	public String toString() {
		return "ContentGuardIndicatorUrlChanges [url=" + url + ", previous_crawl_timestamp=" + previous_crawl_timestamp + ", current_crawl_timestamp="
				+ current_crawl_timestamp + ", hash_cd=" + hash_cd + ", toString()=" + super.toString() + "]";
	}
	
}
