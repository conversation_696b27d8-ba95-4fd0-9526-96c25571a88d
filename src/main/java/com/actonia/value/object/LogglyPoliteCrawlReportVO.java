package com.actonia.value.object;

public class LogglyPoliteCrawlReportVO {
	private String dt; // run date number (ie. mmdd)
	private String proc; // invoking process
	private String ip; // IP of server where the batch process is running
	private String typ; // html
	private String q; // message queue name
	private Integer msg; // number of messages
	private String elapsed; // elapsed time (xdxhxmxs)

	public String getDt() {
		return dt;
	}

	public void setDt(String dt) {
		this.dt = dt;
	}

	public String getProc() {
		return proc;
	}

	public void setProc(String proc) {
		this.proc = proc;
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

	public String getTyp() {
		return typ;
	}

	public void setTyp(String typ) {
		this.typ = typ;
	}

	public Integer getMsg() {
		return msg;
	}

	public void setMsg(Integer msg) {
		this.msg = msg;
	}

	public String getElapsed() {
		return elapsed;
	}

	public void setElapsed(String elapsed) {
		this.elapsed = elapsed;
	}

	public String getQ() {
		return q;
	}

	public void setQ(String q) {
		this.q = q;
	}
}
