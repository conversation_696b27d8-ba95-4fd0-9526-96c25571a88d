package com.actonia.value.object;

public class PageContentDetectionValueObject {
	private String urlString;
	private String responseCode;
	private String content;
	private String detected;

	public String getUrlString() {
		return urlString;
	}

	public void setUrlString(String urlString) {
		this.urlString = urlString;
	}

	public String getResponseCode() {
		return responseCode;
	}

	public void setResponseCode(String responseCode) {
		this.responseCode = responseCode;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getDetected() {
		return detected;
	}

	public void setDetected(String detected) {
		this.detected = detected;
	}

	@Override
	public String toString() {
		return "PageContentDetectionValueObject [urlString=" + urlString + ", responseCode=" + responseCode + ", content=" + content + ", detected=" + detected + "]";
	}

}
