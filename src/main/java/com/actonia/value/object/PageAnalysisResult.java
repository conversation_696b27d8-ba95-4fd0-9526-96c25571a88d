package com.actonia.value.object;

public class PageAnalysisResult implements Cloneable {
	private int rule;
	private int result;

	public int getRule() {
		return rule;
	}

	public void setRule(int rule) {
		this.rule = rule;
	}

	public int getResult() {
		return result;
	}

	public void setResult(int result) {
		this.result = result;
	}

	@Override
	public String toString() {
		return " PageAnalysisResult [rule=" + rule + ", result=" + result + "]";
	}

	@Override
	public PageAnalysisResult clone() throws CloneNotSupportedException {
		return (PageAnalysisResult) super.clone();
	}

}
