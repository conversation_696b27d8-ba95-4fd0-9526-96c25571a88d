package com.actonia.value.object;

public class Inference {
	private Double absolute_effect;
	private Double absolute_effect_lower;
	private Double absolute_effect_upper;
	private Double relative_effect;
	private Double relative_effect_lower;
	private Double relative_effect_upper;
	private Double tail_prob;
	private Double pre_period_mape;
	private Double dw;

	public Double getAbsolute_effect() {
		return absolute_effect;
	}

	public void setAbsolute_effect(Double absolute_effect) {
		this.absolute_effect = absolute_effect;
	}

	public Double getAbsolute_effect_lower() {
		return absolute_effect_lower;
	}

	public void setAbsolute_effect_lower(Double absolute_effect_lower) {
		this.absolute_effect_lower = absolute_effect_lower;
	}

	public Double getAbsolute_effect_upper() {
		return absolute_effect_upper;
	}

	public void setAbsolute_effect_upper(Double absolute_effect_upper) {
		this.absolute_effect_upper = absolute_effect_upper;
	}

	public Double getRelative_effect() {
		return relative_effect;
	}

	public void setRelative_effect(Double relative_effect) {
		this.relative_effect = relative_effect;
	}

	public Double getRelative_effect_lower() {
		return relative_effect_lower;
	}

	public void setRelative_effect_lower(Double relative_effect_lower) {
		this.relative_effect_lower = relative_effect_lower;
	}

	public Double getRelative_effect_upper() {
		return relative_effect_upper;
	}

	public void setRelative_effect_upper(Double relative_effect_upper) {
		this.relative_effect_upper = relative_effect_upper;
	}

	public Double getTail_prob() {
		return tail_prob;
	}

	public void setTail_prob(Double tail_prob) {
		this.tail_prob = tail_prob;
	}

	public Double getPre_period_mape() {
		return pre_period_mape;
	}

	public void setPre_period_mape(Double pre_period_mape) {
		this.pre_period_mape = pre_period_mape;
	}

	public Double getDw() {
		return dw;
	}

	public void setDw(Double dw) {
		this.dw = dw;
	}

	@Override
	public String toString() {
		return "Inference [absolute_effect=" + absolute_effect + ", absolute_effect_lower=" + absolute_effect_lower + ", absolute_effect_upper=" + absolute_effect_upper
				+ ", relative_effect=" + relative_effect + ", relative_effect_lower=" + relative_effect_lower + ", relative_effect_upper=" + relative_effect_upper
				+ ", tail_prob=" + tail_prob + ", pre_period_mape=" + pre_period_mape + ", dw=" + dw + "]";
	}

}
