package com.actonia.value.object;

public class ChangeIndicatorTotalChanges {
	private String changeIndicatorDesc;
	private Integer totalChanges;
	private String totalChangesWithWebAppLink;

	public String getChangeIndicatorDesc() {
		return changeIndicatorDesc;
	}

	public void setChangeIndicatorDesc(String changeIndicatorDesc) {
		this.changeIndicatorDesc = changeIndicatorDesc;
	}

	public Integer getTotalChanges() {
		return totalChanges;
	}

	public void setTotalChanges(Integer totalChanges) {
		this.totalChanges = totalChanges;
	}

	public String getTotalChangesWithWebAppLink() {
		return totalChangesWithWebAppLink;
	}

	public void setTotalChangesWithWebAppLink(String totalChangesWithWebAppLink) {
		this.totalChangesWithWebAppLink = totalChangesWithWebAppLink;
	}

	@Override
	public String toString() {
		return "TargetUrlChangeAlertDetails [changeIndicatorDesc=" + changeIndicatorDesc + ", totalChanges=" + totalChanges + ", totalChangesWithWebAppLink="
				+ totalChangesWithWebAppLink + "]";
	}

}
