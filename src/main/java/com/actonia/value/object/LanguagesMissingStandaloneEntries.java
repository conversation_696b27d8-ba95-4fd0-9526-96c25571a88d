package com.actonia.value.object;

import java.util.Arrays;

public class LanguagesMissingStandaloneEntries {
	private boolean status;
	private String[] urls;

	public boolean isStatus() {
		return status;
	}

	public void setStatus(boolean status) {
		this.status = status;
	}

	public String[] getUrls() {
		return urls;
	}

	public void setUrls(String[] urls) {
		this.urls = urls;
	}

	@Override
	public String toString() {
		return "LanguagesMissingStandaloneEntries [status=" + status + ", urls=" + Arrays.toString(urls) + "]";
	}

}
