package com.actonia;

import java.math.BigDecimal;
import java.math.RoundingMode;

public interface IConstants {

	// save file to remote type: 1. FTP; 2. SEAGATE
	int SAVE_FILE_TO_REMOTE_TYPE = 2;
	// save file to FTP
	int SAVE_FILE_TO_FTP = 1;
	// save file to SEAGATE
	int SAVE_FILE_TO_SEAGATE = 2;

	//****************************************
	// polite crawl web service message codes
	//****************************************
	public String MSG_CD_MSG_CD_NOT_PROVIDED = "PCWS-000001";
	public String MSG_CD_INVALID_ACCESS = "PCWS-000002";
	public String MSG_CD_WEB_SERVICE_METHOD_EXCEPTION = "PCWS-000003";
	public String MSG_CD_REQUEST_PARM_CRAWL_TIMESTAMP_1_IS_REQUIRED = "PCWS-000004";
	public String MSG_CD_REQUEST_PARM_DOMAIN_ID_1_IS_REQUIRED = "PCWS-000005";
	public String MSG_CD_REQUEST_PARM_URL_1_IS_REQUIRED = "PCWS-000006";
	public String MSG_CD_REQUEST_PARM_TRACK_DATE_2_IS_REQUIRED = "PCWS-000007";
	public String MSG_CD_REQUEST_PARM_DOMAIN_ID_2_IS_REQUIRED = "PCWS-000008";
	public String MSG_CD_REQUEST_PARM_URL_2_IS_REQUIRED = "PCWS-000009";
	public String MSG_CD_REQUEST_PARM_CRAWL_TIMESTAMP_1_IS_INVALID = "PCWS-000010";
	public String MSG_CD_REQUEST_PARM_DOMAIN_ID_1_IS_INVALID = "PCWS-000011";
	public String MSG_CD_REQUEST_PARM_URL_1_IS_INVALID = "PCWS-000012";
	public String MSG_CD_REQUEST_PARM_CRAWL_TIMESTAMP_2_IS_INVALID = "PCWS-000013";
	public String MSG_CD_REQUEST_PARM_DOMAIN_ID_2_IS_INVALID = "PCWS-000014";
	public String MSG_CD_REQUEST_PARM_URL_2_IS_INVALID = "PCWS-000015";
	public String MSG_CD_QUERY_RECORD_NOT_AVAILABLE_IN_CLARITYDB = "PCWS-000016";
	public String MSG_CD_REQUEST_JSON_REQUIRED = "PCWS-000017";
	public String MSG_CD_TARGET_URL_1_CRAWL_TIMESTAMP_CANNOT_BE_DETERMINED = "PCWS-000018";
	public String MSG_CD_TARGET_URL_2_CRAWL_TIMESTAMP_CANNOT_BE_DETERMINED = "PCWS-000019";
	public String MSG_CD_URL_SKIP_DOMAIN_NAME_FLG_INVALID = "PCWS-000029";
	public String MSG_CD_URL_TEXT_CASE_INSENSITIVE_FLG_INVALID = "PCWS-000030";
	//****************************************
	// polite crawl web service message codes
	//****************************************

	//****************************************
	// content guard web service message codes
	//****************************************
	public String MSG_CD_CONTENT_GUARD_REQUEST_JSON_REQUIRED = "00001";
	public String MSG_CD_CONTENT_GUARD_REQUEST_PARM_ACCESS_TOKEN_INVALID = "00002";
	public String MSG_CD_CONTENT_GUARD_WEB_SERVICE_METHOD_EXCEPTION = "00003";
	public String MSG_CD_CONTENT_GUARD_REQUEST_COMMAND_INVALID = "00004";
	public String MSG_CD_CONTENT_GUARD_REQUEST_PARM_DOMAIN_ID_REQUIRED = "00005";
	public String MSG_CD_CONTENT_GUARD_REQUEST_PARM_URL_REQUIRED = "00007";
	public String MSG_CD_CONTENT_GUARD_REQUEST_PARM_CRAWL_TIMESTAMP_REQUIRED = "00008";
	public String MSG_CD_CONTENT_GUARD_REQUEST_PARM_CRAWL_DATE_REQUIRED = "00009";
	public String MSG_CD_CONTENT_GUARD_REQUEST_PARM_CRAWL_TIMESTAMP_INVALID = "00010";
	public String MSG_CD_CONTENT_GUARD_REQUEST_PARM_CRAWL_DATE_INVALID = "00011";
	public String MSG_CD_CONTENT_GUARD_REQUEST_PARM_CRAWL_HOUR_INVALID = "00012";
	public String MSG_CD_CONTENT_GUARD_REQUEST_PARM_ACCESS_TOKEN_REQUIRED = "00013";
	public String MSG_CD_CONTENT_GUARD_REQUEST_PARM_START_CRAWL_DATE_INVALID = "00014";
	public String MSG_CD_CONTENT_GUARD_REQUEST_PARM_END_CRAWL_DATE_INVALID = "00015";
	public String MSG_CD_CONTENT_GUARD_REQUEST_PARM_START_CRAWL_DATE_REQUIRED = "00016";
	public String MSG_CD_CONTENT_GUARD_REQUEST_PARM_END_CRAWL_DATE_REQUIRED = "00017";
	public String MSG_CD_CONTENT_GUARD_REQUEST_PARM_START_CRAWL_DATE_LATER_THAN_END_CRAWL_DATE = "00018";
	public String MSG_CD_CONTENT_GUARD_REQUEST_PARM_GROUP_ID_INVALID = "00019";
	public String MSG_CD_CONTENT_GUARD_REQUEST_PARM_GROUP_ID_DOES_NOT_HAVE_URLS = "00020";
	public String MSG_CD_CONTENT_GUARD_REQUEST_PARM_GROUP_ID_REQUIRED = "00021";
	public String MSG_CD_CONTENT_GUARD_REQUEST_PARM_CHANGE_INDICATOR_REQUIRED = "00022";
	public String MSG_CD_CONTENT_GUARD_REQUEST_PARM_FILTER_CHANGE_INDICATOR_INVALID = "00023";
	public String MSG_CD_CONTENT_GUARD_REQUEST_PARM_PAGE_NUMBER_INVALID = "00024";
	public String MSG_CD_CONTENT_GUARD_REQUEST_PARM_ROWS_PER_PAGE_INVALID = "00025";
	public String MSG_CD_CONTENT_GUARD_REQUEST_PARM_SORT_BY_INVALID = "00026";
	public String MSG_CD_CONTENT_GUARD_REQUEST_PARM_PAGE_NUMBER_REQUIRED = "00027";
	public String MSG_CD_CONTENT_GUARD_REQUEST_PARM_ROWS_PER_PAGE_REQUIRED = "00028";
	public String MSG_CD_CONTENT_GUARD_REQUEST_PARM_SORT_BY_REQUIRED = "00029";
	public String MSG_CD_CONTENT_GUARD_REQUEST_PARM_OUTSIDE_DATA_RANGE = "00030";
	public String MSG_CD_CONTENT_GUARD_REQUEST_PARM_RETURN_DETAILS_REQUIRED = "00031";
	public String MSG_CD_CONTENT_GUARD_CRAWL_TIMESTAMP_HISTORY_NOT_AVAILABLE = "00032";
	public String MSG_CD_CONTENT_GUARD_REQUEST_PARM_CRAWL_DATE_NOT_ALLOWED = "00033";
	public String MSG_CD_CONTENT_GUARD_REQUEST_PARM_CRAWL_HOUR_NOT_ALLOWED = "00034";
	public String MSG_CD_CONTENT_GUARD_REQUEST_PARM_START_CRAWL_DATE_NOT_ALLOWED = "00035";
	public String MSG_CD_CONTENT_GUARD_REQUEST_PARM_END_CRAWL_DATE_NOT_ALLOWED = "00036";
	public String MSG_CD_CONTENT_GUARD_REQUEST_PARM_CRAWL_TIMESTAMP_NOT_ALLOWED = "00037";
	public String MSG_CD_CONTENT_GUARD_REQUEST_PARM_START_USAGE_DATE_REQUIRED = "00038";
	public String MSG_CD_CONTENT_GUARD_REQUEST_PARM_START_USAGE_DATE_INVALID = "00039";
	public String MSG_CD_CONTENT_GUARD_REQUEST_PARM_END_USAGE_DATE_REQUIRED = "00040";
	public String MSG_CD_CONTENT_GUARD_REQUEST_PARM_END_USAGE_DATE_INVALID = "00041";
	public String MSG_CD_CONTENT_GUARD_REQUEST_PARM_START_USAGE_DATE_LATER_THAN_END_USAGE_DATE = "00042";
	//****************************************
	// content guard web service message codes
	//****************************************

	//*********************************
	// zapier web service message codes
	//*********************************
	public String MSG_CD_ZAPIER_REQUEST_DATA_REQUIRED = "00001";
	public String MSG_CD_ZAPIER_REQUEST_PARM_ACCESS_TOKEN_REQUIRED = "00002";
	public String MSG_CD_ZAPIER_REQUEST_PARM_ACCESS_TOKEN_INVALID = "00003";
	public String MSG_CD_ZAPIER_REQUEST_PARM_DOMAIN_ID_REQUIRED = "00004";
	public String MSG_CD_ZAPIER_REQUEST_PARM_DOMAIN_ID_INVALID = "00005";
	public String MSG_CD_ZAPIER_REQUEST_PARM_USER_EMAIL_REQUIRED = "00006";
	public String MSG_CD_ZAPIER_REQUEST_PARM_USER_EMAIL_INVALID = "00007";
	public String MSG_CD_ZAPIER_REQUEST_PARM_CALLBACK_URL_REQUIRED = "00008";
	public String MSG_CD_ZAPIER_REQUEST_PARM_CALLBACK_URL_INVALID = "00009";
	public String MSG_CD_ZAPIER_SUBSCRIPTION_ALREADY_EXIST = "00010";
	public String MSG_CD_ZAPIER_SUBSCRIPTION_CANNOT_BE_CREATED = "00011";
	public String MSG_CD_ZAPIER_REQUEST_PARM_ID_REQUIRED = "00012";
	public String MSG_CD_ZAPIER_REQUEST_PARM_ID_INVALID = "00013";
	public String MSG_CD_ZAPIER_REQUEST_PARM_DOMAIN_ID_GROUP_NAME_NOT_CONFIGURED_TO_HAVE_ALERT = "00014";
	public String MSG_CD_ZAPIER_REQUEST_PARM_GROUP_NAME_REQUIRED = "00015";
	public String MSG_CD_ZAPIER_WEB_SERVICE_METHOD_EXCEPTION = "00016";
	public String MSG_CD_ZAPIER_REQUEST_COMMAND_INVALID = "00017";
	public String MSG_CD_ZAPIER_REQUEST_PARM_PAGE_TAG_NAME_REQUIRED = "00018";
	public String MSG_CD_ZAPIER_REQUEST_PARM_DOMAIN_ID_PAGE_TAG_NAME_NOT_CONFIGURED_TO_HAVE_ALERT = "00019";
	//*********************************
	// zapier web service message codes
	//*********************************

	//****************************************
	// causal impact web service message codes
	//****************************************
	public String MSG_CD_CAUSAL_IMPACT_REQUEST_JSON_REQUIRED = "00001";
	public String MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_ACCESS_TOKEN_INVALID = "00002";
	public String MSG_CD_CAUSAL_IMPACT_WEB_SERVICE_METHOD_EXCEPTION = "00003";
	public String MSG_CD_CAUSAL_IMPACT_REQUEST_COMMAND_INVALID = "00004";
	public String MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_ACCESS_TOKEN_REQUIRED = "00005";
	public String MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_TEST_DATA_POINTS_REQUIRED = "00006";
	public String MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_CONTROL_NAME_LIST_REQUIRED = "00007";
	public String MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_CONTROL_DATA_POINTS_LIST_REQUIRED = "00008";
	public String MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_DATA_POINTS_INCONSISTENT = "00009";
	public String MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_CONTROL_NAMES_INCONSISTENT = "00010";
	public String MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_PRE_PERIOD_START_DATE_REQUIRED = "00011";
	public String MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_PRE_PERIOD_END_DATE_REQUIRED = "00012";
	public String MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_POST_PERIOD_START_DATE_REQUIRED = "00013";
	public String MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_POST_PERIOD_END_DATE_REQUIRED = "00014";
	public String MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_PRE_PERIOD_START_DATE_INVALID = "00015";
	public String MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_PRE_PERIOD_END_DATE_INVALID = "00016";
	public String MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_POST_PERIOD_START_DATE_INVALID = "00017";
	public String MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_POST_PERIOD_END_DATE_INVALID = "00018";
	public String MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_PRE_PERIOD_START_END_DATE_INVALID = "00019";
	public String MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_POST_PERIOD_START_END_DATE_INVALID = "00020";
	public String MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_CONTROL_NAMES_CANNOT_BE_BLANK = "00021";
	public String MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_CONTROL_NAMES_MUST_BE_UNIQUE = "00022";
	public String MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_PRE_AND_POST_PERIOD_DATES_INCONSISTENT = "00023";
	public String MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_TEST_DATA_POINTS_PRE_AND_POST_PERIOD_DATES_INCONSISTENT = "00024";
	//****************************************
	// causal impact web service message codes
	//****************************************

	//**********************************
	// prophet web service message codes
	//**********************************
	public String MSG_CD_PROPHET_REQUEST_JSON_REQUIRED = "00001";
	public String MSG_CD_PROPHET_REQUEST_PARM_ACCESS_TOKEN_INVALID = "00002";
	public String MSG_CD_PROPHET_WEB_SERVICE_METHOD_EXCEPTION = "00003";
	public String MSG_CD_PROPHET_REQUEST_PARM_ACCESS_TOKEN_REQUIRED = "00004";
	public String MSG_CD_PROPHET_REQUEST_PARM_FORECAST_DAYS_REQUIRED = "00005";
	public String MSG_CD_PROPHET_REQUEST_PARM_DATE_ARRAY_REQUIRED = "00006";
	public String MSG_CD_PROPHET_REQUEST_PARM_VALUE_ARRAY_REQUIRED = "00007";
	public String MSG_CD_PROPHET_REQUEST_PARM_DATE_ARRAY_INVALID = "00008";
	public String MSG_CD_PROPHET_REQUEST_PARM_ARRAY_LENGTH_MUST_BE_SAME = "00009";
	//**********************************
	// prophet web service message codes
	//**********************************

	//**********************************
	// market matching web service message codes
	//**********************************
	public String MSG_CD_MARKET_MATCHING_REQUEST_JSON_REQUIRED = "00001";
	public String MSG_CD_MARKET_MATCHING_REQUEST_PARM_ACCESS_TOKEN_INVALID = "00002";
	public String MSG_CD_MARKET_MATCHING_WEB_SERVICE_METHOD_EXCEPTION = "00003";
	public String MSG_CD_MARKET_MATCHING_REQUEST_PARM_ACCESS_TOKEN_REQUIRED = "00004";
	public String MSG_CD_MARKET_MATCHING_REQUEST_PARM_TEST_MARKET_REQUIRED = "00005";
	public String MSG_CD_MARKET_MATCHING_REQUEST_PARM_PRE_PERIOD_START_DATE_REQUIRED = "00006";
	public String MSG_CD_MARKET_MATCHING_REQUEST_PARM_PRE_PERIOD_END_DATE_REQUIRED = "00007";
	public String MSG_CD_MARKET_MATCHING_REQUEST_PARM_POST_PERIOD_START_DATE_REQUIRED = "00008";
	public String MSG_CD_MARKET_MATCHING_REQUEST_PARM_POST_PERIOD_END_DATE_REQUIRED = "00009";
	public String MSG_CD_MARKET_MATCHING_REQUEST_PARM_NUMBER_OF_BEST_MATCHES_REQUIRED = "00010";
	public String MSG_CD_MARKET_MATCHING_REQUEST_PARM_AREA_DATE_VALUE_ARRAY_REQUIRED = "00011";
	public String MSG_CD_MARKET_MATCHING_REQUEST_PARM_TEST_MARKET_INVALID = "00012";
	public String MSG_CD_MARKET_MATCHING_REQUEST_PARM_PRE_PERIOD_START_DATE_INVALID = "00013";
	public String MSG_CD_MARKET_MATCHING_REQUEST_PARM_PRE_PERIOD_END_DATE_INVALID = "00014";
	public String MSG_CD_MARKET_MATCHING_REQUEST_PARM_POST_PERIOD_START_DATE_INVALID = "00015";
	public String MSG_CD_MARKET_MATCHING_REQUEST_PARM_POST_PERIOD_END_DATE_INVALID = "00016";
	public String MSG_CD_MARKET_MATCHING_REQUEST_PARM_PRE_PERIOD_START_END_DATE_INVALID = "00017";
	public String MSG_CD_MARKET_MATCHING_REQUEST_PARM_POST_PERIOD_START_DATE_MUST_BE_ONE_DAY_AFTER = "00018";
	public String MSG_CD_MARKET_MATCHING_REQUEST_PARM_POST_PERIOD_START_END_DATE_INVALID = "00019";
	public String MSG_CD_MARKET_MATCHING_REQUEST_PARM_AREA_ARRAY_REQUIRED = "00020";
	public String MSG_CD_MARKET_MATCHING_REQUEST_PARM_AREA_ARRAY_INVALID = "00021";
	public String MSG_CD_MARKET_MATCHING_REQUEST_PARM_DATE_ARRAY_REQUIRED = "00022";
	public String MSG_CD_MARKET_MATCHING_REQUEST_PARM_DATE_ARRAY_INVALID = "00023";
	public String MSG_CD_MARKET_MATCHING_REQUEST_PARM_VALUE_ARRAY_REQUIRED = "00024";
	public String MSG_CD_MARKET_MATCHING_REQUEST_PARM_VALUE_ARRAY_INVALID = "00025";
	public String MSG_CD_MARKET_MATCHING_REQUEST_PARM_AREA_DATE_VALUE_ARRAY_NOT_MATCH_START_END_DATE = "00026";
	public String MSG_CD_MARKET_MATCHING_REQUEST_PARM_NUMBER_OF_BEST_MATCHES_INVALID = "00027";
	//**********************************
	// market matching web service message codes
	//**********************************

	//********************************************
	// target URL change web service message codes
	//********************************************
	public String MSG_CD_TARGET_URL_CHANGE_REQUEST_JSON_REQUIRED = "00001";
	public String MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_ACCESS_TOKEN_INVALID = "00002";
	public String MSG_CD_TARGET_URL_CHANGE_WEB_SERVICE_METHOD_EXCEPTION = "00003";
	public String MSG_CD_TARGET_URL_CHANGE_REQUEST_COMMAND_INVALID = "00004";
	public String MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_DOMAIN_ID_REQUIRED = "00005";
	public String MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_ACCESS_TOKEN_REQUIRED = "00006";
	public String MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_START_CRAWL_TIMESTAMP_REQUIRED = "00007";
	public String MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_START_CRAWL_TIMESTAMP_INVALID = "00008";
	public String MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_END_CRAWL_TIMESTAMP_REQUIRED = "00009";
	public String MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_END_CRAWL_TIMESTAMP_INVALID = "00010";
	public String MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_START_CRAWL_TIMESTAMP_LATER_THAN_END_CRAWL_TIMESTAMP = "00011";
	public String MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_PAGE_NUMBER_REQUIRED = "00012";
	public String MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_PAGE_NUMBER_INVALID = "00013";
	public String MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_ROWS_PER_PAGE_REQUIRED = "00014";
	public String MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_ROWS_PER_PAGE_INVALID = "00015";
	public String MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_SORT_BY_REQUIRED = "00016";
	public String MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_SORT_BY_INVALID = "00017";
	public String MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_CHANGE_INDICATORS_INVALID = "00018";
	public String MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_OUTSIDE_DATA_RANGE = "00019";
	public String MSG_CD_TARGET_URL_CHANGE_REQUEST_DATA_NOT_AVAILABLE = "00020";
	public String MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_CONTENT_TYPES_INVALID = "00021";
	public String MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_RESPONSE_CODES_INVALID = "00022";
	public String MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_MUST_INCLUDE_RESP_CD_CHG_IND = "00023";
	public String MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_SUMMARY_INVALID = "00024";

	//********************************************
	// target URL change web service message codes
	//********************************************

	//*******************
	// polite crawl types
	//*******************

	public int CRAWL_TYPE_TARGET_URL_HTML = 2;
	public int CRAWL_TYPE_SHARED_COUNTS = 3;
	public int CRAWL_TYPE_COMPETITOR_URL_HTML = 4;
	public int CRAWL_TYPE_CONTENT_GUARD = 6;

	//*******************
	// polite crawl types
	//*******************	

	public String[] COMPETITOR_HOSTS_WITH_RESP_CODE_999_ARRAY = new String[] { "www.americanas.com.br", "www.staples.com", "www.jcpenney.com", "health.usnews.com",
			"www.cooks.com", "www.mylife.com", "www.cars.com", "www.careerjet.com", "www.neckermann-reisen.de", "www.dhgate.com", "www.makemytrip.com",
			"www.submarino.com.br", "www.hotelclub.com", "www.goibibo.com", "www.argos.co.uk", "www.dickssportinggoods.com", "www.radissonhotels.com", "www.united.com",
			"www.zomato.com", "www.yatra.com", "www.yatra.com", "us.venere.com", "www.easyjet.com", "www.oreillyauto.com", "www.upcitemdb.com", "www.eventim.de",
			"traveloka.com", "www.themine.com", };

	public String COMMA = ",";
	public int CRAWL_ADDITIONAL_DOMAINS_TRUE = 1;
	public int CRAWL_COMPLETED = 3;
	public int CRAWL_ERRORED = 4;
	public int CRAWL_IN_QUEUE = 0;
	public int CRAWL_PROCESSING = 5;
	public int CRAWL_STARTED = 2;
	public int CRAWL_HOLD = 10;
	public Integer DOMAIN_STATUS_ACTIVE = 1;
	public String ELASTIC_SEARCH_INDEX_PREFIX = "seoclarity";
	public Integer FALSE_NUMERIC = 0;
	public Integer TRUE_NUMERIC = 1;
	public String FALSE_STRING = "0";
	public String TRUE_STRING = "1";
	public int UNTAGGED_FLAG = -9;
	public String DATE_FORMAT_YYYY_MM_DD = "yyyy-MM-dd";
	public String DATE_FORMAT_MM_DD_YYYY = "MM/dd/yyyy";
	public String DATE_FORMAT_YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
	public String DATE_FORMAT_YYYYMMDDHHMMSS = "yyyyMMddHHmmss";
	public String DATE_FORMAT_YYYYMMDD = "yyyyMMdd";
	public String HTTP_COLON_SLASH_SLASH = "http://";
	public int SITE_CRAWL_SOLR_SERVERS_CUTOFF_DATE_NUMBER = 20160626;
	public String AD_HOC_REQUEST = "ad_hoc_request";
	public int MAX_DATABASE_BATCH_UPDATE_SIZE = 100;
	public String PREVIOUS_CRAWL_REQ_LOG_ID = "previousCrawlRequestLogId";
	public String EMPTY_STRING = "";
	public String TAB = "\t";
	public int MAX_ELASTICSEARCH_RETRY_COUNT = 3;
	public int ELASTICSEARCH_RETRY_WAIT_TIME_IN_MILLISECONDS = 88888;
	public String TWO_DOUBLE_QUOTES = "\"\"";
	public String ONE_DOUBLE_QUOTES = "\"";
	public String LINKS_SS = "Links_ss";
	public String CONTENT_S = "Content_s";
	public String CONTENT_COUNT_I = "ContentCount_i";
	public String IP_STATUS_ALIVE = "alive";
	public String IP_STATUS_BLOCK = "block";
	public String IP_STATUS_WORKING = "working";
	public String UTF_8 = "utf-8";
	public String NEWLINE = "\n";
	public String NO_BREAK_SPACE = "\u00A0";
	public String ANY_SPACES = " +";
	public String ONE_SPACE = " ";
	public int RECORDS_PER_SQL_STATEMENT = 10;
	public int SQL_STATEMENTS_PER_JDBC_CALL = 10;
	public String SEMI_COLON = ";";
	public String WEEKLY = "weekly";
	public String DAILY = "daily";
	public String QUEUE_NAME_CRAWL_URLS_DAILY_HTML = "CRAWL_URLS_DAILY_HTML_";
	public String QUEUE_NAME_CRAWL_PRIORITY_URLS_DAILY_HTML = "CRAWL_PRIORITY_URLS_DAILY_HTML_";
	public String QUEUE_NAME_CLOUD_CRAWL_URLS_DAILY_HTML = "CLOUD_CRAWL_URLS_DAILY_HTML_";
	public String QUEUE_NAME_CLOUD_CRAWL_PRIORITY_URLS_DAILY_HTML = "CLOUD_CRAWL_PRIORITY_URLS_DAILY_HTML_";
	public String QUEUE_NAME_CRAWL_URLS_DAILY_HTML_QUEUE_NAMES = "CRAWL_URLS_DAILY_HTML_QUEUE_NAMES";
	public String QUEUE_NAME_CRAWL_PRIORITY_URLS_DAILY_HTML_QUEUE_NAMES = "CRAWL_PRIORITY_URLS_DAILY_HTML_QUEUE_NAMES";
	public String QUEUE_NAME_CRAWL_URLS_DAILY_HTML_LANGUAGES = "CRAWL_URLS_DAILY_HTML_LANGUAGES";
	public String QUEUE_NAME_CRAWL_PRIORITY_URLS_DAILY_HTML_LANGUAGES = "CRAWL_PRIORITY_URLS_DAILY_HTML_LANGUAGES";
	public String QUEUE_NAME_CLOUD_CRAWL_URLS_DAILY_HTML_QUEUE_NAMES = "CLOUD_CRAWL_URLS_DAILY_HTML_QUEUE_NAMES";
	public String QUEUE_NAME_CLOUD_CRAWL_PRIORITY_URLS_DAILY_HTML_QUEUE_NAMES = "CLOUD_CRAWL_PRIORITY_URLS_DAILY_HTML_QUEUE_NAMES";
	public String QUEUE_NAME_CLOUD_CRAWL_URLS_DAILY_HTML_LANGUAGES = "CLOUD_CRAWL_URLS_DAILY_HTML_LANGUAGES";
	public String QUEUE_NAME_CLOUD_CRAWL_PRIORITY_URLS_DAILY_HTML_LANGUAGES = "CLOUD_CRAWL_PRIORITY_URLS_DAILY_HTML_LANGUAGES";
	public String QUEUE_NAME_CLOUD_CRAWL_URLS_DAILY_HTML_STOP_CRAWL = "CLOUD_CRAWL_URLS_DAILY_HTML_STOP_CRAWL";
	public String QUEUE_NAME_CLOUD_CRAWL_PRIORITY_URLS_DAILY_HTML_STOP_CRAWL = "CLOUD_CRAWL_PRIORITY_URLS_DAILY_HTML_STOP_CRAWL";
	public String QUEUE_NAME_CLOUD_CRAWL_URLS_DAILY_SOCIAL_QUEUE_NAMES = "CLOUD_CRAWL_URLS_DAILY_SOCIAL_QUEUE_NAMES";
	public String QUEUE_NAME_CLOUD_CRAWL_PRIORITY_URLS_DAILY_SOCIAL_QUEUE_NAMES = "CLOUD_CRAWL_PRIORITY_URLS_DAILY_SOCIAL_QUEUE_NAMES";
	public String UNDERSCORE = "_";
	public String SQS_LOCATION_OREGON = "sqs.us-west-2.amazonaws.com";
	public int CRAWL_HTML = 1;
	public int CRAWL_SHARED_COUNTS = 2;
	public String ALL = "all";
	public String HTML = "html";
	public String SHARED_COUNTS = "shared counts";
	public String QUEUE_NAME = "queueName";
	public String MAX_CONCURRENT_THREADS = "maxConcurrentThreads";
	public String DELAY_IN_SECONDS = "delayInSeconds";
	public String USER_AGENT = "userAgent";
	public String LANGUAGE_CODE = "languageCode";
	public String STOP_CRAWL = "stopCrawl";
	public String QUEUE_NAME_CRAWL_COMPETITOR_URLS_HTML = "CRAWL_COMPETITOR_URLS_HTML_";
	public String QUEUE_NAME_CRAWL_COMPETITOR_URLS_SOCIAL = "CRAWL_COMPETITOR_URLS_SOCIAL_";
	public String QUEUE_NAME_CRAWL_COMPETITOR_URLS_HTML_QUEUE_NAMES = "CRAWL_COMPETITOR_URLS_HTML_QUEUE_NAMES";
	public String QUEUE_NAME_CRAWL_COMPETITOR_URLS_HTML_LANGUAGES = "CRAWL_COMPETITOR_URLS_HTML_LANGUAGES";
	public String QUEUE_NAME_CRAWL_COMPETITOR_URLS_SOCIAL_QUEUE_NAMES = "CRAWL_COMPETITOR_URLS_SOCIAL_QUEUE_NAMES";
	public String AWS_CREDENTIALS_PROPERTIES_FILE_NAME = "/AwsCredentials.properties";
	public String DATE_FORMAT_MMDD = "MMdd";
	public String JA = "ja";
	public String LOGGLY_TAG_POLITE_CRAWL = "politecrawl";

	public String QUEUE_NAME_CLOUD_CRAWL_URLS_DAILY_SOCIAL = "CLOUD_CRAWL_URLS_DAILY_SOCIAL_";
	public String QUEUE_NAME_CLOUD_CRAWL_PRIORITY_URLS_DAILY_SOCIAL = "CLOUD_CRAWL_PRIORITY_URLS_DAILY_SOCIAL_";
	public String QUEUE_NAME_CLOUD_CRAWL_URLS_DAILY_SOCIAL_STOP_CRAWL = "CLOUD_CRAWL_URLS_DAILY_SOCIAL_STOP_CRAWL";
	public String QUEUE_NAME_CLOUD_CRAWL_PRIORITY_URLS_DAILY_SOCIAL_STOP_CRAWL = "CLOUD_CRAWL_PRIORITY_URLS_DAILY_SOCIAL_STOP_CRAWL";

	public String QUEUE_NAME_TEST_CRAWL_PRIORITY_URLS_DAILY_HTML_QUEUE_NAMES = "TEST_CRAWL_PRIORITY_URLS_DAILY_HTML_QUEUE_NAMES";
	public String QUEUE_NAME_TEST_CRAWL_URLS_DAILY_HTML_LANGUAGES = "TEST_CRAWL_URLS_DAILY_HTML_LANGUAGES";
	public String QUEUE_NAME_TEST_CRAWL_PRIORITY_URLS_DAILY_HTML_LANGUAGES = "TEST_CRAWL_PRIORITY_URLS_DAILY_HTML_LANGUAGES";
	public String QUEUE_NAME_TEST_CRAWL_URLS_DAILY_HTML = "TEST_CRAWL_URLS_DAILY_HTML_";
	public String QUEUE_NAME_TEST_CRAWL_PRIORITY_URLS_DAILY_HTML = "TEST_CRAWL_PRIORITY_URLS_DAILY_HTML_";
	public String QUEUE_NAME_TEST_CLOUD_CRAWL_URLS_DAILY_SOCIAL = "TEST_CLOUD_CRAWL_URLS_DAILY_SOCIAL_";
	public String QUEUE_NAME_TEST_CLOUD_CRAWL_PRIORITY_URLS_DAILY_SOCIAL = "TEST_CLOUD_CRAWL_PRIORITY_URLS_DAILY_SOCIAL_";
	public String QUEUE_NAME_TEST_CLOUD_CRAWL_URLS_DAILY_SOCIAL_QUEUE_NAMES = "TEST_CLOUD_CRAWL_URLS_DAILY_SOCIAL_QUEUE_NAMES";
	public String QUEUE_NAME_TEST_CLOUD_CRAWL_PRIORITY_URLS_DAILY_SOCIAL_QUEUE_NAMES = "TEST_CLOUD_CRAWL_PRIORITY_URLS_DAILY_SOCIAL_QUEUE_NAMES";

	// for data store SOLR
	public String QUEUE_NAME_SOLR_COMPETITOR_URL_HTML_PREFIX = "CRAWL_ASSOCIATED_URLS_HTML_";
	public String QUEUE_NAME_SOLR_COMPETITOR_URL_HTML_QUEUE_NAMES = "CRAWL_ASSOCIATED_URLS_HTML_QUEUE_NAMES";

	// for data store ClickHouse
	public String QUEUE_NAME_COMPETITOR_URL_HTML_PREFIX = "COMPETITOR_URL_HTML_";
	public String QUEUE_NAME_COMPETITOR_URL_HTML_QUEUE_NAMES = "COMPETITOR_URL_HTML_QUEUE_NAMES";

	// for weekly associated URLs crawl
	public String QUEUE_NAME_CLOUD_CRAWL_ASSOCIATED_URLS_HTML_QUEUE_NAMES = "CLOUD_CRAWL_ASSOCIATED_URLS_HTML_QUEUE_NAMES";
	public String QUEUE_NAME_CLOUD_CRAWL_ASSOCIATED_URLS_SOCIAL_QUEUE_NAMES = "CLOUD_CRAWL_ASSOCIATED_URLS_SOCIAL_QUEUE_NAMES";
	public String QUEUE_NAME_CRAWL_ASSOCIATED_URLS_SOCIAL_QUEUE_NAMES = "CRAWL_ASSOCIATED_URLS_SOCIAL_QUEUE_NAMES";
	public String QUEUE_NAME_CLOUD_CRAWL_ASSOCIATED_URLS_HTML = "CLOUD_CRAWL_ASSOCIATED_URLS_HTML_";
	public String QUEUE_NAME_CRAWL_ASSOCIATED_URLS_SOCIAL = "CRAWL_ASSOCIATED_URLS_SOCIAL_";
	public String QUEUE_NAME_CLOUD_CRAWL_ASSOCIATED_URLS_SOCIAL = "CLOUD_CRAWL_ASSOCIATED_URLS_SOCIAL_";
	public String QUEUE_NAME_CLOUD_CRAWL_ASSOCIATED_URLS_HTML_STOP_CRAWL = "CLOUD_CRAWL_ASSOCIATED_URLS_HTML_STOP_CRAWL";
	public String QUEUE_NAME_CLOUD_CRAWL_ASSOCIATED_URLS_SOCIAL_STOP_CRAWL = "CLOUD_CRAWL_ASSOCIATED_URLS_SOCIAL_STOP_CRAWL";

	public String QUEUE_NAME_TEST_CLOUD_CRAWL_ASSOCIATED_URLS_HTML = "TEST_CLOUD_CRAWL_ASSOCIATED_URLS_HTML_";
	//public String QUEUE_NAME_TEST_CRAWL_ASSOCIATED_URLS_HTML = "TEST_CRAWL_ASSOCIATED_URLS_HTML_";
	public String QUEUE_NAME_TEST_COMPETITOR_URL_HTML_PREFIX = "TEST_COMPETITOR_URL_HTML_";
	public String QUEUE_NAME_TEST_CRAWL_ASSOCIATED_URLS_SOCIAL = "TEST_CRAWL_ASSOCIATED_URLS_SOCIAL_";
	public String QUEUE_NAME_TEST_CLOUD_CRAWL_ASSOCIATED_URLS_HTML_STOP_CRAWL = "TEST_CLOUD_CRAWL_ASSOCIATED_URLS_HTML_STOP_CRAWL";
	public String QUEUE_NAME_TEST_CLOUD_CRAWL_ASSOCIATED_URLS_SOCIAL_STOP_CRAWL = "TEST_CLOUD_CRAWL_ASSOCIATED_URLS_SOCIAL_STOP_CRAWL";
	public String QUEUE_NAME_TEST_CLOUD_CRAWL_ASSOCIATED_URLS_HTML_QUEUE_NAMES = "TEST_CLOUD_CRAWL_ASSOCIATED_URLS_HTML_QUEUE_NAMES";
	//public String QUEUE_NAME_TEST_CRAWL_ASSOCIATED_URLS_HTML_QUEUE_NAMES = "TEST_CRAWL_ASSOCIATED_URLS_HTML_QUEUE_NAMES";
	public String QUEUE_NAME_TEST_COMPETITOR_URL_HTML_QUEUE_NAMES = "TEST_COMPETITOR_URL_HTML_QUEUE_NAMES";
	public String QUEUE_NAME_TEST_CLOUD_CRAWL_ASSOCIATED_URLS_SOCIAL_QUEUE_NAMES = "TEST_CLOUD_CRAWL_ASSOCIATED_URLS_SOCIAL_QUEUE_NAMES";
	public String QUEUE_NAME_TEST_CRAWL_ASSOCIATED_URLS_SOCIAL_QUEUE_NAMES = "TEST_CRAWL_ASSOCIATED_URLS_SOCIAL_QUEUE_NAMES";
	public int COMPETITORURL_ADD_BY_USER = 1;
	public String HTTPS_COLON_SLASH_SLASH = "https://";
	public String SLASH_POUND_SLASH = "/#/";
	public String SLASH = "/";
	public int TARGET_URL_STATUS_ACTIVE = 1;
	public int TARGET_URL_STATUS_INACTIVE = 2;
	int TARGET_URL_DISABLE_CRAWL_ACTIVE = 1;
	int TARGET_URL_DISABLE_CRAWL_INACTIVE = 0;
	int TARGET_URL_SOURCE_TYPE_RSS = 2;
	int TARGET_URL_INITIAL_CRAWL_ONLY_YES = 1;
	public int TARGET_URL_TYPE_ADDED_BY_USER = 1;
	public String FORWARD_SLASH = "/";
	public String COLON = ":";

	public String CLICKHOUSE_JDBC_CONNECTION_URL = "jdbc:clickhouse://";

	public String RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_HOSTNAMES = "crawler.v3.clickhouse.db.hostnames";
	public String RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_PORT = "crawler.v3.clickhouse.db.port";
	public String RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_NAME = "crawler.v3.clickhouse.db.name";
	public String RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_BATCH_CREATION_SIZE = "crawler.v3.clickhouse.batch.creation.size";
	public String RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_USER = "crawler.v3.clickhouse.user";
	public String RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_PASSWORD = "crawler.v3.clickhouse.password";
	public String RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_CONNECTION_TIMEOUT_MILLISECONDS = "crawler.v3.clickhouse.connection.timeout.milliseconds";
	public String RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_MAX_RETRY_COUNTS = "crawler.v3.clickhouse.maximum.retry.counts";
	public String RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_RETRY_WAIT_MILLISECONDS = "crawler.v3.clickhouse.retry.wait.milliseconds";

	public int CLICKHOUSE_SIGN_POSITIVE_1 = 1;
	public int CLICKHOUSE_SIGN_NEGATIVE_1 = -1;
	public char DOT_CHAR = '.';
	public String DOT_STRING = ".";

	public String CLICKHOUSE_FLD_TRACK_DATE = "track_date";
	public String CLICKHOUSE_FLD_DOMAIN = "domain";
	public String CLICKHOUSE_FLD_ROOT_DOMAIN = "root_domain";
	public String CLICKHOUSE_FLD_URL = "url";
	public String CLICKHOUSE_FLD_PROTOCOL = "protocol";
	public String CLICKHOUSE_FLD_URI = "uri";
	public String CLICKHOUSE_FLD_FOLDER1 = "folder1";
	public String CLICKHOUSE_FLD_FOLDER2 = "folder2";
	public String CLICKHOUSE_FLD_URL_HASH = "url_hash";
	public String CLICKHOUSE_FLD_URI_HASH = "uri_hash";
	public String CLICKHOUSE_FLD_FOLDER1_HASH = "folder1_hash";
	public String CLICKHOUSE_FLD_FOLDER2_HASH = "folder2_hash";
	public String CLICKHOUSE_FLD_FACEBOOK_SHARED_COUNT = "facebook_shared_count";
	public String CLICKHOUSE_FLD_LINKEDIN_SHARED_COUNT = "linkedin_shared_count";
	public String CLICKHOUSE_FLD_TWITTER_SHARED_COUNT = "twitter_shared_count";
	public String CLICKHOUSE_FLD_GOOGLE_PLUS_SHARED_COUNT = "google_plus_shared_count";
	public String CLICKHOUSE_FLD_PINTEREST_SHARED_COUNT = "pinterest_shared_count";
	public String CLICKHOUSE_FLD_STUMBLEUPON_SHARED_COUNT = "stumbleupon_shared_count";
	public String CLICKHOUSE_FLD_SIGN = "sign";
	public String CLICKHOUSE_FLD_DOMAIN_IDS = "domain_ids";
	public String QUEUE_NAME_TEST_CLOUD_CRAWL_PRIORITY_ASSOCIATED_URLS_SOCIAL = "TEST_CLOUD_CRAWL_PRIORITY_ASSOCIATED_URLS_SOCIAL_";
	public String QUEUE_NAME_CLOUD_CRAWL_PRIORITY_ASSOCIATED_URLS_SOCIAL = "CLOUD_CRAWL_PRIORITY_ASSOCIATED_URLS_SOCIAL_";
	public String QUEUE_NAME_CRAWL_PRIORITY_ASSOCIATED_URLS_SOCIAL = "CRAWL_PRIORITY_ASSOCIATED_URLS_SOCIAL_";
	public String QUEUE_NAME_TEST_CRAWL_PRIORITY_ASSOCIATED_URLS_SOCIAL = "TEST_CRAWL_PRIORITY_ASSOCIATED_URLS_SOCIAL_";
	public int MAX_SOLR_RETRY_COUNT = 5;
	public String NORMALIZED_URL_HASH_CD = "normalized_url_hash_cd";
	public String TRACK_DATE = "track_date";
	public String WEEK_OF_YEAR = "week_of_year";
	public String SOLR_CORE_NAME_ASSOCIATED_URLS_HTML = "associated_urls_html";
	public String SUBSERVER_SOLR_CONNECTION_URL = "subserver.solr.connection.url";
	public String SUBSERVER_SOLR_CONNECTION_URL_READONLY = "subserver.solr.connection.url.read.only";
	public String SOLR_CONNECTION_TEST_HASH_CODE = "abcde";
	public String ID = "id";
	public String[] SOLR_OUTPUT_FIELD_NAME_ARRAY_CONNECTION_TEST = new String[] { ID };
	public String DATE_FORMAT_YYYYWW = "YYYYww";
	public String NOT_AVAILABLE = "n/a";
	public String PROTOCOL_HTTP = "http";
	public String PROTOCOL_HTTPS = "https";
	public Integer UNKNOWN_PROTOCOL = 888;
	public String DOT_WWW = ".www";
	public String PROCESS_TYPE_SOLR = "solr";
	public String PROCESS_TYPE_CLICKHOUSE = "clickhouse";
	public int PROCESS_TYPE_NUMBER_SOLR = 1;
	public int PROCESS_TYPE_NUMBER_CLICKHOUSE = 2;
	public int PROCESS_TYPE_NUMBER_TARGET_URL_CHANGE = 0;
	public String PROCESS_TYPE_TEST = "test";
	public String PROCESS_TYPE_CONTROL = "control";

	public String QUEUE_NAME_SOLR_TARGET_URL_DAILY_HTML_PREFIX = "CLOUD_CRAWL_URLS_DAILY_HTML_";
	public String QUEUE_NAME_SOLR_TARGET_URL_DAILY_HTML_QUEUE_NAMES = "CLOUD_CRAWL_URLS_DAILY_HTML_QUEUE_NAMES";
	public String QUEUE_NAME_SOLR_TARGET_URL_DAILY_HTML_LANGUAGES = "CLOUD_CRAWL_URLS_DAILY_HTML_LANGUAGES";
	public String QUEUE_NAME_SOLR_TARGET_URL_DAILY_HTML_STOP_CRAWL = "CLOUD_CRAWL_URLS_DAILY_HTML_STOP_CRAWL";

	public String QUEUE_NAME_TARGET_URL_DAILY_HTML_PREFIX = "TARGET_URL_HTML_";
	String QUEUE_NAME_TARGET_URL_DEAD_QUEUES_PREFIX = "TARGET_URL_DEAD_QUEUES_";

	// https://www.wrike.com/open.htm?id=1014964756
	//public String QUEUE_NAME_TARGET_URL_DAILY_HTML_QUEUE_NAMES = "TARGET_URL_HTML_QUEUE_NAMES";
	public String QUEUE_NAME_TARGET_URL_DAILY_HTML_QUEUE_NAMES_FIFO = "TARGET_URL_HTML_QUEUE_NAMES.fifo";
	public String QUEUE_NAME_TARGET_URL_DAILY_HTML_SKYSCANNER_QUEUE_NAMES_FIFO = "TARGET_URL_HTML_SKYSCANNER_QUEUE_NAMES.fifo";

	public String QUEUE_NAME_NEW_URL_DAILY_HTML_PREFIX = "NEW_URL_HTML_";
	//public String QUEUE_NAME_NEW_URL_DAILY_HTML_QUEUE_NAMES = "NEW_URL_HTML_QUEUE_NAMES";
	public String QUEUE_NAME_NEW_URL_DAILY_HTML_QUEUE_NAMES_FIFO = "NEW_URL_HTML_QUEUE_NAMES.fifo";

	public String QUEUE_NAME_LINK_CLARITY_PREFIX = "LINK_CLARITY_";
	public String QUEUE_NAME_LINK_CLARITY_QUEUE_NAMES = "LINK_CLARITY_QUEUE_NAMES";

	// for tracking additional content
	public String DOMAIN_ID = "domain_id";
	public String PATTERN = "pattern";
	public String ATTRIBUTE = "attribute";
	public String DISPLAY_NAME = "display_name";

	public String SOLR_CONNECTION_TEST_DOC_ID = "1";

	public String QUEUE_NAME_CRAWL_URLS_DAILY_HTML_PREFIX = "CRAWL_URLS_DAILY_HTML_";

	public String ENABLE_JAVASCRIPT_CRAWL = "enableJavascriptCrawl";
	public String ENABLE_SCRAPY_CRAWL = "enableScrapyCrawl";

	public String FIELD_NAME_DOMAIN_ID = "domain_id";
	public String FIELD_NAME_TARGET_URL_CRAWLER_ID = "target_url_crawler_id";
	public String FIELD_NAME_PATTERN = "pattern";
	public String FIELD_NAME_ATTRIBUTE = "attribute";
	public String FIELD_NAME_DISPLAY_NAME = "display_name";
	public String FIELD_NAME_URL_TYPE = "url_type";
	public String FIELD_NAME_METRICS_NAME = "metrics_name";

	public String CLICKHOUSE_CRAWL_DATABASE_PROPERTIES = "clickhouse.crawl.database.properties";
	public String CLICKHOUSE_GA_DATABASE_PROPERTIES = "clickhouse.GA.database.properties";

	public String[] CLICKHOUSE_ADDITIONAL_CONTENT_PATTERN_TO_BE_SKIPPED_ARRAY = new String[] { "canonical", "robots" };

	public String DATE_FORMAT_YYYY_MM_DD_HHMMSS = "yyyy-MM-dd HH:mm:ss";
	public String QUEUE_NAME_TEST_TARGET_URL_HTML_QUEUE_NAMES = "TEST_TARGET_URL_HTML_QUEUE_NAMES";
	public String QUEUE_NAME_TEST_TARGET_URL_HTML_PREFIX = "TEST_TARGET_URL_HTML_";
	public int COMPETITOR_WEEKLY_CRAWL_TRACKING_THRESHOLD = 100000;
	public String URL = "url";
	public String ALT_IMG_LIST = "alt_img_list";
	public String AMPHTML_FLAG = "amphtml_flag";
	public String AMPHTML_HREF = "amphtml_href";
	public String ANALYZED_URL_FLG_S = "analyzed_url_flg_s";
	public String ANALYZED_URL_S = "analyzed_url_s";

	public String ARCHIVE_FLG = "archive_flg";
	public String ARCHIVE_FLG_X_TAG = "archive_flg_x_tag";
	public String CONTENT_TYPE = "content_type";
	public String DESCRIPTION = "description";
	public String DESCRIPTION_FLG = "description_flg";
	public String DESCRIPTION_LENGTH = "description_length";
	public String DESCRIPTION_SIMHASH = "description_simhash";
	public String ERROR_MESSAGE = "error_message";
	public String FINAL_RESPONSE_CODE = "final_response_code";
	public String FOLDER_LEVEL_1 = "folder_level_1";
	public String FOLDER_LEVEL_2 = "folder_level_2";
	public String FOLDER_LEVEL_3 = "folder_level_3";
	public String FOLDER_LEVEL_COUNT = "folder_level_count";
	public String FOLLOW_FLG = "follow_flg";
	public String FOLLOW_FLG_X_TAG = "follow_flg_x_tag";
	public String H1_FLG = "h1_flg";
	public String H1_SIMHASH = "h1_simhash";
	public String H2_SIMHASH = "h2_simhash";
	public String HEADER_NOARCHIVE = "header_noarchive";
	public String HEADER_NOFOLLOW = "header_nofollow";
	public String HEADER_NOINDEX = "header_noindex";
	public String HEADER_NOODP = "header_noodp";
	public String HEADER_NOSNIPPET = "header_nosnippet";
	public String HEADER_NOYDIR = "header_noydir";
	public String HREFLANG_ERRORS = "hreflang_errors";
	public String INSECURE_RESOURCES = "insecure_resources";
	public String INSECURE_RESOURCES_FLAG = "insecure_resources_flag";
	public String LONG_REDIRECT = "long_redirect";
	public String META_CONTENT_TYPE = "meta_content_type";
	public String META_DISABLED_SITELINKS = "meta_disabled_sitelinks";
	public String META_NOODP = "meta_noodp";
	public String META_NOSNIPPET = "meta_nosnippet";
	public String META_NOYDIR = "meta_noydir";
	public String META_REDIRECT = "meta_redirect";
	public String MIXED_REDIRECTS = "mixed_redirects";
	public String MOBILE_REL_ALTERNATE_URL_IS_CONSISTENT = "mobile_rel_alternate_url_is_consistent";
	public String NOODP = "noodp";
	public String NOSNIPPET = "nosnippet";
	public String NOYDIR = "noydir";
	public String REDIRECT_FLG = "redirect_flg";
	public String REDIRECT_TIMES = "redirect_times";
	public String REL_NEXT_HTML_URL = "rel_next_html_url";
	public String REL_NEXT_URL_IS_CONSISTENT = "rel_next_url_is_consistent";
	public String REL_PREV_URL_IS_CONSISTENT = "rel_prev_url_is_consistent";
	public String REQUEST_HEADERS = "request_headers";
	public String REQUEST_TIME = "request_time";
	public String RETRY_ATTEMPTED = "retry_attempted";
	public String ROBOTS = "robots";
	public String ROBOTS_FLG = "robots_flg";
	public String ROBOTS_FLG_X_TAG = "robots_flg_x_tag";
	public String SERVER_RESPONSE_TIME = "server_response_time";
	public String SOURCE_URL = "source_url";
	public String SPLASH_TOOK = "splash_took";
	public String TITLE_SIMHASH = "title_simhash";
	public String VIEWPORT_FLAG = "viewport_flag";
	public String BLOCKED_BY_ROBOTS = "blocked_by_robots";
	public String CANONICAL = "canonical";
	public String CANONICAL_FLG = "canonical_flg";
	public String CANONICAL_HEADER_FLAG = "canonical_header_flag";
	public String CANONICAL_HEADER_TYPE = "canonical_header_type";
	public String CANONICAL_TYPE = "canonical_type";
	public String CANONICAL_URL_IS_CONSISTENT = "canonical_url_is_consistent";
	public String CUSTOM_DATA = "custom_data";
	public String DOCUMENT_SIZE = "document_size";
	public String DOWNLOAD_LATENCY = "download_latency";
	public String DOWNLOAD_TIME = "download_time";
	public String H1_LENGTH = "h1_length";
	public String H1_MD5 = "h1_md5";
	public String HREFLANG_LINKS = "hreflang_links";
	public String HREFLANG_LINKS_OUT_COUNT = "hreflang_links_out_count";
	public String HREFLANG_URL_COUNT = "hreflang_url_count";
	public String INDEX_FLG = "index_flg";
	public String INDEX_FLG_X_TAG = "index_flg_x_tag";
	public String INDEXABLE = "indexable";
	public String OG_MARKUP = "og_markup";
	public String OG_MARKUP_FLAG = "og_markup_flag";
	public String OG_MARKUP_LENGTH = "og_markup_length";
	public String OUTLINK_COUNT = "outlink_count";
	public String PAGE_1 = "page_1";
	public String ALTERNATE_LINKS = "alternate_links";
	public String PAGE_LINK = "page_link";
	public String PAGE_TIMEOUT_FLAG = "page_timeout_flag";
	public String PAGINATED = "paginated";
	public String PAGINATION_LINKS = "pagination_links";
	public String PROTOCOL = "protocol";
	public String REDIRECT_BLOCKED = "redirect_blocked";
	public String REDIRECT_BLOCKED_REASON = "redirect_blocked_reason";
	public String REDIRECT_CHAIN = "redirect_chain";
	public String RESPONSE_HEADERS = "response_headers";
	public String ROBOTS_CONTENTS = "robots_contents";
	public String ROBOTS_CONTENTS_X_TAG = "robots_contents_x_tag";
	public String TITLE_FLG = "title_flg";
	public String TITLE_LENGTH = "title_length";
	public String TITLE_MD5 = "title_md5";
	public String TWITTER_DESCRIPTION_LENGTH = "twitter_description_length";
	public String TWITTER_MARKUP = "twitter_markup";
	public String TWITTER_MARKUP_FLAG = "twitter_markup_flag";
	public String TWITTER_MARKUP_LENGTH = "twitter_markup_length";
	public String URL_LENGTH = "url_length";
	public String VALID_TWITTER_CARD = "valid_twitter_card";
	public String VIEWPORT_CONTENT = "viewport_content";
	public String COUNT_OF_OBJECTS = "count_of_objects";
	public String PAGE_ANALYSIS_RESULTS = "page_analysis_results";
	public String CHANGE_TRACKING_HASH = "change_tracking_hash";
	public String LOWER_CASE_URL_HASH = "lower_case_url_hash";
	public String URL_MURMUR_HASH = "url_murmur_hash";
	public String H1 = "h1";
	public String H1_COUNT = "h1_count";
	public String H2 = "h2";
	public String META_CHARSET = "meta_charset";
	public String REDIRECT_FINAL_URL = "redirect_final_url";
	public String RESPONSE_CODE = "response_code";
	public String CRAWL_TIMESTAMP = "crawl_timestamp";
	public String URL_HASH = "url_hash";
	public String TITLE = "title";
	public String TABLE_NAME_TARGET_URL_HTML_DAILY = "dis_target_url_html_daily";
	//public String TABLE_NAME_TARGET_URL_HTML_DAILY = "test_target_url_html_daily";	
	public String TABLE_NAME_SUFFIX_TEST = "_test";
	public String DATE_FORMAT_YYYYMMDDHHMM = "yyyyMMddHHmm";
	public String HTTP_STATUS_0 = "0";
	public String FIELD_NAME_XPATH = "xpath";
	public String TABLE_NAME_USER_BACKLINK = "usr_backlink_";
	public String TABLE_NAME_USER_PARTNER_DOMAIN = "usr_partner_domain_";
	public String PROCESS_TYPE_LINK_CLARITY_FULL = "link_clarity_full";
	public String PROCESS_TYPE_LINK_CLARITY_CONTROL = "link_clarity_ctrl";
	public String QUEUE_NAME_TEST_LINK_CLARITY_PREFIX = "TEST_LINK_CLARITY_";
	public String QUEUE_NAME_TEST_LINK_CLARITY_QUEUE_NAMES = "TEST_LINK_CLARITY_QUEUE_NAMES";
	public String DOMAIN_NAMES = "domainNames";
	public String USER_AGENTS = "userAgents";
	public String INT32 = "Int32";
	public String TABLE_NAME_LINK_CLARITY_DAILY = "dis_link_clarity_daily";
	public String DOMAIN_IDS = "domain_ids";
	//public String FILE_EXTENSION_TXT = ".txt";
	public int HTTP_STATUS_CODE_200 = 200;
	public String GAIN_LOSS_DATE = "gain_loss_date";
	public String GAIN_LOSS_FLAG = "gain_loss_flag";
	public String TARGET_URL_HTTP_STATUS_CODE = "target_url_http_status_code";
	public String TARGET_URL = "target_url";
	public String ANCHOR_TEXT = "anchor_text";
	public String LINK_TYPE = "link_type";
	public String CONTENT_DASH_TYPE = "Content-type";
	public String APPLICATION_SLASH_JSON = "application/json";
	public String CACHE_CONTROL = "cache-control";
	public String NO_CACHE = "no-cache";
	public String WWW_WALGREENS_COM = "www.walgreens.com";
	public String AUTOTRADER_COM = "autotrader.com";
	public String WWW_COSTUMESUPERCENTER_COM = "www.costumesupercenter.com";
	public String NEWLINE_UPPER_CASE = "%0D%0A";
	public String NEWLINE_LOWER_CASE = "%0d%0a";
	public int RETRY_SCRAPY_API_WAIT_MILLISECONDS = 888;
	public int MAX_SCRAPY_API_RETRY_COUNT = 3;
	public String MOBILE_SITE_HOSTNAME_PREFIX = "m.";
	public String PLUS_SIGN = "+";
	public String RUNTIME_PROPERTY_NAME_SCRAPY_MOBILE_USER_AGENT = "scrapy.mobile.user.agent";
	public String RUNTIME_PROPERTY_NAME_SCRAPY_CRAWLER_ENDPOINTS = "scrapy.crawler.endpoints";
	public String RUNTIME_PROPERTY_NAME_SCRAPY_CRAWLER_LONDON_ENDPOINTS = "scrapy.crawler.london.endpoints";
	public String RUNTIME_PROPERTY_NAME_SCRAPY_CRAWLER_ACCESS_KEY = "scrapy.crawler.access.key";
	public String RUNTIME_PROPERTY_NAME_LINK_GAIN_LOSS_DEFAULT_USER_AGENT = "link.gain.loss.default.user.agent";
	public String DATE_FORMAT_YYYYMM = "yyyyMM";
	public String EARLIEST_FIRST_TRACK_DATE_STRING = "2019-05-01";
	public int GAIN_LOSS_FLAG_GAINED = 1;
	public int GAIN_LOSS_FLAG_LOST = 2;
	public int LINK_TYPE_TEXT = 1;
	public int LINK_TYPE_IMAGE = 2;
	public String ACCESS_KEY = "Access-Key";
	public String UTF8 = "UTF8";
	public String SINGLE_QUOTE = "'";
	public String STRUCTURED_DATA = "structured_data";
	public int HTTP_STATUS_CODE_403 = 403;
	public int MAX_HTTP_STATUS_CODE_403_ALLOWED = 3;
	public String RUNTIME_PROPERTY_NAME_CLICKHOUSE_CLUSTER_SERVERS = "clickhouse.cluster.servers";
	public String TOTAL_RECORDS = "total_records";
	public String CHANGE_TRACKING_HASH_CD_JSON = "change_tracking_hash_cd_json";
	public String RESPONSE_CODE_200 = "200";
	public String PAGE_ANALYSIS_RESULTS_CHG_IND_JSON = "page_analysis_results_chg_ind_json";
	public int DOMAIN_ID_4765 = 4765;
	public String URL_DOMAIN = "url_domain";
	public String URL_WITH_TOO_MANY_H2 = "http://www.martinstabe.com/links/";

	public String ALTERNATE_LINKS_CHG_IND = "alternate_links_chg_ind";
	public String AMPHTML_FLAG_CHG_IND = "amphtml_flag_chg_ind";
	public String AMPHTML_HREF_CHG_IND = "amphtml_href_chg_ind";
	public String ANALYZED_URL_FLG_S_CHG_IND = "analyzed_url_flg_s_chg_ind";
	public String ANALYZED_URL_S_CHG_IND = "analyzed_url_s_chg_ind";
	public String ARCHIVE_FLG_CHG_IND = "archive_flg_chg_ind";
	public String ARCHIVE_FLG_X_TAG_CHG_IND = "archive_flg_x_tag_chg_ind";
	public String BLOCKED_BY_ROBOTS_CHG_IND = "blocked_by_robots_chg_ind";
	public String CANONICAL_CHG_IND = "canonical_chg_ind";
	public String CANONICAL_FLG_CHG_IND = "canonical_flg_chg_ind";
	public String CANONICAL_HEADER_FLAG_CHG_IND = "canonical_header_flag_chg_ind";
	public String CANONICAL_HEADER_TYPE_CHG_IND = "canonical_header_type_chg_ind";
	public String CANONICAL_TYPE_CHG_IND = "canonical_type_chg_ind";
	public String CANONICAL_URL_IS_CONSISTENT_CHG_IND = "canonical_url_is_consistent_chg_ind";
	public String CONTENT_TYPE_CHG_IND = "content_type_chg_ind";
	public String DESCRIPTION_CHG_IND = "description_chg_ind";
	public String DESCRIPTION_FLG_CHG_IND = "description_flg_chg_ind";
	public String DESCRIPTION_LENGTH_CHG_IND = "description_length_chg_ind";
	public String DESCRIPTION_SIMHASH_CHG_IND = "description_simhash_chg_ind";
	public String ERROR_MESSAGE_CHG_IND = "error_message_chg_ind";
	public String FINAL_RESPONSE_CODE_CHG_IND = "final_response_code_chg_ind";
	public String FOLLOW_FLG_CHG_IND = "follow_flg_chg_ind";
	public String FOLLOW_FLG_X_TAG_CHG_IND = "follow_flg_x_tag_chg_ind";
	public String H1_CHG_IND = "h1_chg_ind";
	public String H1_COUNT_CHG_IND = "h1_count_chg_ind";
	public String H1_FLG_CHG_IND = "h1_flg_chg_ind";
	public String H1_LENGTH_CHG_IND = "h1_length_chg_ind";
	public String H1_MD5_CHG_IND = "h1_md5_chg_ind";
	public String HEADER_NOARCHIVE_CHG_IND = "header_noarchive_chg_ind";
	public String HEADER_NOFOLLOW_CHG_IND = "header_nofollow_chg_ind";
	public String HEADER_NOINDEX_CHG_IND = "header_noindex_chg_ind";
	public String HEADER_NOODP_CHG_IND = "header_noodp_chg_ind";
	public String HEADER_NOSNIPPET_CHG_IND = "header_nosnippet_chg_ind";
	public String HEADER_NOYDIR_CHG_IND = "header_noydir_chg_ind";
	public String HREFLANG_ERRORS_CHG_IND = "hreflang_errors_chg_ind";
	public String HREFLANG_LINKS_CHG_IND = "hreflang_links_chg_ind";
	public String HREFLANG_LINKS_OUT_COUNT_CHG_IND = "hreflang_links_out_count_chg_ind";
	public String HREFLANG_URL_COUNT_CHG_IND = "hreflang_url_count_chg_ind";
	public String INDEX_FLG_CHG_IND = "index_flg_chg_ind";
	public String INDEX_FLG_X_TAG_CHG_IND = "index_flg_x_tag_chg_ind";
	public String INDEXABLE_CHG_IND = "indexable_chg_ind";
	public String INSECURE_RESOURCES_CHG_IND = "insecure_resources_chg_ind";
	public String INSECURE_RESOURCES_FLAG_CHG_IND = "insecure_resources_flag_chg_ind";
	public String META_CHARSET_CHG_IND = "meta_charset_chg_ind";
	public String META_CONTENT_TYPE_CHG_IND = "meta_content_type_chg_ind";
	public String META_DISABLED_SITELINKS_CHG_IND = "meta_disabled_sitelinks_chg_ind";
	public String META_NOODP_CHG_IND = "meta_noodp_chg_ind";
	public String META_NOSNIPPET_CHG_IND = "meta_nosnippet_chg_ind";
	public String META_NOYDIR_CHG_IND = "meta_noydir_chg_ind";
	public String META_REDIRECT_CHG_IND = "meta_redirect_chg_ind";
	public String MIXED_REDIRECTS_CHG_IND = "mixed_redirects_chg_ind";
	public String MOBILE_REL_ALTERNATE_URL_IS_CONSISTENT_CHG_IND = "mobile_rel_alternate_url_is_consistent_chg_ind";
	public String NOODP_CHG_IND = "noodp_chg_ind";
	public String NOSNIPPET_CHG_IND = "nosnippet_chg_ind";
	public String NOYDIR_CHG_IND = "noydir_chg_ind";
	public String OG_MARKUP_CHG_IND = "og_markup_chg_ind";
	public String OG_MARKUP_FLAG_CHG_IND = "og_markup_flag_chg_ind";
	public String OG_MARKUP_LENGTH_CHG_IND = "og_markup_length_chg_ind";
	public String OUTLINK_COUNT_CHG_IND = "outlink_count_chg_ind";
	public String PAGE_LINK_CHG_IND = "page_link_chg_ind";
	public String REDIRECT_BLOCKED_CHG_IND = "redirect_blocked_chg_ind";
	public String REDIRECT_BLOCKED_REASON_CHG_IND = "redirect_blocked_reason_chg_ind";
	public String REDIRECT_CHAIN_CHG_IND = "redirect_chain_chg_ind";
	public String REDIRECT_FINAL_URL_CHG_IND = "redirect_final_url_chg_ind";
	public String REDIRECT_FLG_CHG_IND = "redirect_flg_chg_ind";
	public String REDIRECT_TIMES_CHG_IND = "redirect_times_chg_ind";
	public String RESPONSE_CODE_CHG_IND = "response_code_chg_ind";
	public String ROBOTS_CHG_IND = "robots_chg_ind";
	public String ROBOTS_CONTENTS_CHG_IND = "robots_contents_chg_ind";
	public String ROBOTS_CONTENTS_X_TAG_CHG_IND = "robots_contents_x_tag_chg_ind";
	public String ROBOTS_FLG_CHG_IND = "robots_flg_chg_ind";
	public String ROBOTS_FLG_X_TAG_CHG_IND = "robots_flg_x_tag_chg_ind";
	public String STRUCTURED_DATA_CHG_IND = "structured_data_chg_ind";
	public String TITLE_CHG_IND = "title_chg_ind";
	public String TITLE_FLG_CHG_IND = "title_flg_chg_ind";
	public String TITLE_LENGTH_CHG_IND = "title_length_chg_ind";
	public String TITLE_MD5_CHG_IND = "title_md5_chg_ind";
	public String TITLE_SIMHASH_CHG_IND = "title_simhash_chg_ind";
	public String VIEWPORT_CONTENT_CHG_IND = "viewport_content_chg_ind";
	public String VIEWPORT_FLAG_CHG_IND = "viewport_flag_chg_ind";

	public String CANONICAL_ADDED_IND = "canonical_added_ind";
	public String CANONICAL_REMOVED_IND = "canonical_removed_ind";
	public String DESCRIPTION_ADDED_IND = "description_added_ind";
	public String DESCRIPTION_REMOVED_IND = "description_removed_ind";
	public String H1_ADDED_IND = "h1_added_ind";
	public String H1_REMOVED_IND = "h1_removed_ind";
	public String H2_ADDED_IND = "h2_added_ind";
	public String H2_CHG_IND = "h2_chg_ind";
	public String H2_REMOVED_IND = "h2_removed_ind";
	public String HREFLANG_LINKS_ADDED_IND = "hreflang_links_added_ind";
	public String HREFLANG_LINKS_REMOVED_IND = "hreflang_links_removed_ind";
	public String OPEN_GRAPH_ADDED_IND = "open_graph_added_ind";
	public String OPEN_GRAPH_REMOVED_IND = "open_graph_removed_ind";
	public String REDIRECT_301_CHG_IND = "redirect_301_chg_ind";
	public String REDIRECT_301_DETECTED_IND = "redirect_301_detected_ind";
	public String REDIRECT_301_REMOVED_IND = "redirect_301_removed_ind";
	public String REDIRECT_302_CHG_IND = "redirect_302_chg_ind";
	public String REDIRECT_302_DETECTED_IND = "redirect_302_detected_ind";
	public String REDIRECT_302_REMOVED_IND = "redirect_302_removed_ind";
	public String REDIRECT_DIFF_CODE_IND = "redirect_diff_code_ind";
	public String ROBOTS_ADDED_IND = "robots_added_ind";
	public String ROBOTS_REMOVED_IND = "robots_removed_ind";
	public String TITLE_ADDED_IND = "title_added_ind";
	public String TITLE_REMOVED_IND = "title_removed_ind";
	public String VIEWPORT_ADDED_IND = "viewport_added_ind";
	public String VIEWPORT_REMOVED_IND = "viewport_removed_ind";
	String RESPONSE_CODE_404_DETECTED_IND = "404_detected_ind";
	String RESPONSE_CODE_404_REMOVED_IND = "404_removed_ind";
	public String FAILED_TO_RESPOND = "failed to respond";
	public String SIGN = "sign";
	public String MSG = "msg";
	public String RUNTIME_PROPERTY_NAME_MAIN_WEB_SERVICE_ENDPOINT = "main.web.service.endpoint";
	public int MAX_RETRY_COUNT = 2;
	public String QUESTION_MARK = "?";
	public String RESPONSE = "response";
	public String MESSAGES = "messages";
	public String START_TIME = "start_time";
	public String END_TIME = "end_time";
	public String MESSAGE = "message";
	public String FINAL_URL = "final_url";
	public String PROCESSING_TIME = "processing_time";
	public String STATUS = "status";
	public String DOMAIN_NAME = "domain_name";
	public String OPEN_CLOSE_BRACKET = "[]";
	public String TRACK_TIME = "track_time";
	public String ROBOTS_TEXT_CHECK_MESSAGE_CHG_IND = "robots_text_check_message_chg_ind";
	public String ROBOTS_TEXT_CHECK_ROBOTS_TXT_CONTENT_CHG_IND = "robots_text_check_robots_txt_content_chg_ind";
	public String ROBOTS_TEXT_CHECK_STATUS_CHG_IND = "robots_text_check_status_chg_ind";
	public String ROBOTS_BLOCK_CHECK_BAIDUSPIDER_CHG_IND = "robots_block_check_baiduspider_chg_ind";
	public String ROBOTS_BLOCK_CHECK_BINGBOT_CHG_IND = "robots_block_check_bingbot_chg_ind";
	public String ROBOTS_BLOCK_CHECK_GOOGLEBOT_CHG_IND = "robots_block_check_googlebot_chg_ind";
	public String ROBOTS_BLOCK_CHECK_YAHOO_CHG_IND = "robots_block_check_yahoo_chg_ind";
	public String ROBOTS_BLOCK_CHECK_ALL_USER_AGENTS_CHG_IND = "robots_block_check_all_user_agents_chg_ind";
	public String ROBOTS_BLOCK_CHECK_ALL_BOTS_BLOCKED_CHG_IND = "robots_block_check_all_bots_blocked_chg_ind";
	public String ROBOTS_BLOCK_CHECK_ALL_PAGES_BLOCKED_CHG_IND = "robots_block_check_all_pages_blocked_chg_ind";
	public String ROBOTS_BLOCK_CHECK_MESSAGE_CHG_IND = "robots_block_check_message_chg_ind";
	public String ROBOTS_BLOCK_CHECK_STATUS_CHG_IND = "robots_block_check_status_chg_ind";
	public String SITE_MAP_CHECK_MESSAGE_CHG_IND = "site_map_check_message_chg_ind";
	public String SITE_MAP_CHECK_STATUS_CHG_IND = "site_map_check_status_chg_ind";
	public String SITE_MAP_VALIDATION_CHECK_SITE_MAP_URLS_CHG_IND = "site_map_validation_check_site_map_urls_chg_ind";
	public String SITE_MAP_VALIDATION_CHECK_MESSAGE_CHG_IND = "site_map_validation_check_message_chg_ind";
	public String SITE_MAP_VALIDATION_CHECK_STATUS_CHG_IND = "site_map_validation_check_status_chg_ind";
	public String TRAILING_SLASH_CHECK_MESSAGE_CHG_IND = "trailing_slash_check_message_chg_ind";
	public String TRAILING_SLASH_CHECK_STATUS_CHG_IND = "trailing_slash_check_status_chg_ind";
	public String PROTOCOL_CHECK_MESSAGE_CHG_IND = "protocol_check_message_chg_ind";
	public String PROTOCOL_CHECK_STATUS_CHG_IND = "protocol_check_status_chg_ind";
	public String ERROR_PAGE_CHECK_MESSAGE_CHG_IND = "error_page_check_message_chg_ind";
	public String ERROR_PAGE_CHECK_STATUS_CHG_IND = "error_page_check_status_chg_ind";
	public String CANONICAL_CHECK_MESSAGE_CHG_IND = "canonical_check_message_chg_ind";
	public String CANONICAL_CHECK_STATUS_CHG_IND = "canonical_check_status_chg_ind";
	public String ALTERNATE_TAG_CHECK_MESSAGE_CHG_IND = "alternate_tag_check_message_chg_ind";
	public String ALTERNATE_TAG_CHECK_STATUS_CHG_IND = "alternate_tag_check_status_chg_ind";
	public String MESSAGES_CHG_IND = "messages_chg_ind";
	public String MESSAGE_CHG_IND = "message_chg_ind";
	public String FINAL_URL_CHG_IND = "final_url_chg_ind";
	public String URL_CHG_IND = "url_chg_ind";
	public String STATUS_CHG_IND = "status_chg_ind";
	public String RULE_NAME_ROBOTS_TEXT_CHECK = "robotsTextCheck";
	public String RULE_NAME_ROBOTS_BLOCK_CHECK = "robotsBlockCheck";
	public String RULE_NAME_SITE_MAP_CHECK = "siteMapCheck";
	public String RULE_NAME_SITE_MAP_VALIDATION_CHECK = "siteMapValidationCheck";
	public String RULE_NAME_TRAILING_SLASH_CHECK = "trailingSlashCheck";
	public String RULE_NAME_PROTOCOL_CHECK = "protocolCheck";
	public String RULE_NAME_ERROR_PAGE_CHECK = "errorPageCheck";
	public String RULE_NAME_CANONICAL_CHECK = "canonicalCheck";
	public String RULE_NAME_ALTERNATE_TAG_CHECK = "alternateTagCheck";

	//public String ESCAPED_ENCODED_META_CHAR = "\\\\"; //debug
	public String ESCAPED_ENCODED_META_CHAR = "\\"; //debug
	public String META_CHAR_BACKSLASH = "\\";
	public String META_CHAR_CARET = "^";
	public String META_CHAR_DOLLAR_SIGN = "$";
	public String META_CHAR_DOT = ".";
	public String META_CHAR_PIPE = "|";
	public String META_CHAR_QUESTION_MARK = "?";
	public String META_CHAR_ASTERISK = "*";
	public String META_CHAR_PLUS_SIGN = "+";
	public String META_CHAR_OPENING_PARENTHESIS = "(";
	public String CLOSING_PARENTHESIS = ")";
	public String OPEN_BRACKET = "[";
	public String META_CHAR_OPENING_CURLY_BRACE = "{";
	public String SQL_QUERY_REGULAR_EXPRESSION_BEGIN_WITH = "^";
	public String INTERNAL_LINKS = "internal_links";
	public String OUTBOUND_LINKS = "outbound_links";
	public String PAGE_RANK = "page_rank";
	public String PAGE_AUTHORITY = "page_authority";
	public String META_KEYWORDS = "meta_keywords";
	public String META_DESC = "meta_desc";
	public String META_ROBOTS = "meta_robots";
	public String[] SOLR_OUTPUT_FIELD_NAME_ARRAY_HTML_SUBSET_5 = new String[] { ID, TRACK_DATE, RESPONSE_CODE, INTERNAL_LINKS, OUTBOUND_LINKS, PAGE_RANK,
			PAGE_AUTHORITY, TITLE, META_KEYWORDS, META_DESC, H1, H2, META_ROBOTS };
	public String FLOAT_FORMAT_ONE_DECIMAL_PLACE = "%.01f";
	public String SOLR_SELECTION_CRITERON_ALL = "*:*";
	public String CLOSE_BRACKET = "]";
	public String AND_TRACK_DATE_ASTERISK_TO = " AND track_date:[* TO ";
	public String COMPANY_NAME_HOMEAWAY = "HomeAway";

	public String USER_AGENT_NAME_BAIDUSPIDER = "baiduspider";
	public String USER_AGENT_NAME_BINGBOT = "bingbot";
	public String USER_AGENT_NAME_GOOGLEBOT = "googlebot";
	public String USER_AGENT_NAME_YAHOO = "yahoo";
	public String INTERNAL_LINK_COUNT = "internal_link_count";
	public String RUNTIME_PROPERTY_NAME_DOMAIN_IDS_WITH_RESTRICTED_CRAWL_TIME = "domain.ids.with.restricted.crawl.time";
	public String STRING_ZERO = "0";
	public String POSTMAN_TOKEN = "postman-token";
	public String MD5 = "MD5";
	public String USER_AGENT_WWW_HPE_COM = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.88 Safari/537.36";
	public String DOMAIN_NAME_WWW_HPE_COM = "www.hpe.com";
	public String DOMAIN_NAME_STORE_HP_COM = "store.hp.com";
	int HTTP_STATUS_CODE_400 = 400;
	int HTTP_STATUS_CODE_500 = 500;
	int HTTP_STATUS_CODE_600 = 600;
	public int HTTP_STATUS_CODE_502 = 502;
	public int HTTP_STATUS_CODE_504 = 504;
	public int HTTP_STATUS_CODE_503 = 503;
	public String ERR_MSG_504_GATEWAY_TIME_OUT = "504 Gateway Time-out";
	public String ERR_MSG_502_BAD_GATEWAY = "502 Bad Gateway";
	public String ERR_MSG_503_SERVICE_UNAVAILABLE = "503 Service Unavailable";
	public String HTML_SOURCE_PREFIX = "<html>";
	public String GATEWAY_EXCEPTION_MSG = "GatewayException";
	int HTTP_STATUS_CODE_998 = 998;
	public int HTTP_STATUS_CODE_999 = 999;
	public int SELECTOR_TYPE_NUMBER_XPATH = 1;
	public int SELECTOR_TYPE_NUMBER_CSS = 2;
	public int SELECTOR_TYPE_NUMBER_DIV_ID = 3;
	public int SELECTOR_TYPE_NUMBER_DIV_CLASS = 4;
	public String SELECTOR_TYPE_TEXT_DIV_CLASS = "DIV_CLASS";
	public String SELECTOR_TYPE_TEXT_DIV_ID = "DIV_ID";
	public String SELECTOR_TYPE_TEXT_XPATH = "XPATH";
	public String SELECTOR_TYPE_TEXT_CSS = "CSS";
	public int URL_SELECTOR_TYPE_ALL = 1;
	public int URL_SELECTOR_TYPE_CONTAINS = 2;
	public int URL_SELECTOR_TYPE_EQUALS = 3;
	public int URL_SELECTOR_TYPE_REGEXP = 4;
	public String NAME = "name";
	public String VALUE = "value";
	public int MAX_WEB_PAGE_CONTENT_LENGTH = 8 * 1024 * 1024;
	public String BRIGHTEDGE_COM = "brightedge.com";
	public int POLITE_CRAWL_V2 = 2;
	public int MAX_5XX_STATUS_CODES_PER_DOMAIN_COMPETITOR_URL = 3;
	public int MAX_4XX_STATUS_CODES_PER_DOMAIN_COMPETITOR_URL = 3;
	public int MAX_5XX_STATUS_CODES_PER_DOMAIN_TARGET_URL = 8;
	public int MAX_4XX_STATUS_CODES_PER_DOMAIN_TARGET_URL = 8;
	public String[] SKIP_TARGET_URL_STRING_ARRAY = new String[] { ".couponcabin.com", "uat3www.academy.com" };
	public String BRADFORDEXCHANGE_COM = "bradfordexchange.com";
	public String SPOKEO_COM = "spokeo.com";
	public String DHGATE_COM = "dhgate.com";
	public String DOT_ZIP = ".zip";
	public String RUNTIME_PROPERTY_NAME_CRAWLER_V3_USER_AGENT = "crawler.v3.user.agent";
	public String RUNTIME_PROPERTY_NAME_CRAWLER_V3_MESSAGES_PER_ITERATION = "crawler.v3.messages_per_iteration";
	public String RUNTIME_PROPERTY_NAME_CRAWLER_V3_CONTROLLER_QUEUE = "crawler.v3.controller.queue";
	public String IS_INVOKE_SHARED_COUNTS_API = "IS_INVOKE_SHARED_COUNTS_API";
	public int SQS_MSG_TIMEOUT_IN_2000_SEC = 2000;
	public String START_CRAWL_TIME = "startCrawlTime";
	public String END_CRAWL_TIME = "endCrawlTime";
	public int MAXIMUM_RETRY_THREE_TIMES_COUNT = 3;
	public String RUNTIME_PROPERTY_NAME_CRAWLER_V3_CRAWL_TYPE = "crawler.v3.crawltype";
	public String FIELD_NAME_SELECTOR_TYPE = "selector_type";
	public String FIELD_NAME_SELECTOR = "selector";
	public String FIELD_NAME_URL_SELECTOR_TYPE = "url_selector_type";
	public String FIELD_NAME_URL_SELECTOR = "url_selector";
	public String DATE_FORMAT_YYYYMMDD_HH = "yyyyMMdd_HH";
	public String TABLE_NAME_UNIT_TEST_TARGET_URL_HTML = "unit_test_target_url_html";
	public String ALT_IMG_DETAIL = "alt_img_detail";
	public String CONTENT_EXTRACTED_COUNT = "content_extracted_count";
	public String CONTENT_EXTRACTED_FLG = "content_extracted_flg";
	public String CONTENT_FLG = "content_flg";
	public String CONTENT_MD5 = "content_md5";
	public String CONTENT_WORD_COUNT = "content_word_count";
	public String CONTENT_WORDS = "content_words";
	public String CONV_CRAWL_DATE = "conv_crawl_date";
	public String CRAWL_DATE_LONG = "crawl_date_long";
	public String SQS_REGION_OREGON = "sqs.us-west-2.amazonaws.com";
	public int SQS_MSG_TIMEOUT_IN_SEC = 0;
	public String RUNTIME_PROPERTY_NAME_CRAWLER_V3_STOP_CRAWL_QUEUE = "crawler.v3.stop.crawl.queue";
	public String RESPONSE_CODE_0 = "0";
	public String AWS_CREDENTIALS_PROP_FILE_NAME = "/AwsCredentials.properties";
	public String HTTP_STATUS_CODE_301_STRING = "301";
	public String HTTP_STATUS_CODE_302_STRING = "302";
	public String YES = "YES";
	public String NO = "NO";
	public String STOP_CRAWL_MESSAGE = "[{\"type\":\"stopCrawl\",\"data\":\"true\"}]";
	public String TARGET_URL_HTML_STOP_CRAWL_QUEUE = "TARGET_URL_HTML_STOP_CRAWL_QUEUE";
	public String CONTENT_GUARD_STOP_CRAWL_QUEUE = "CONTENT_GUARD_STOP_CRAWL_QUEUE";
	public String COMPETITOR_URL_HTML_STOP_CRAWL_QUEUE = "COMPETITOR_URL_HTML_STOP_CRAWL_QUEUE";
	public String LINK_CLARITY_STOP_CRAWL_QUEUE = "LINK_CLARITY_STOP_CRAWL_QUEUE";
	public String DOT_CSV = ".csv";
	public String DOT_PDF = ".pdf";
	public String DOT_PPT = ".ppt";
	public String DOT_PPTX = ".pptx";
	public String DOT_DOC = ".doc";
	public String DOT_DOCX = ".docx";
	public String DOT_XML = ".xml";
	public String DOT_GZ = ".gz";
	public String DOT_GZIP = ".gzip";
	public String DOT_XLSX = ".xlsx";
	public String DOT_XLS = ".xls";
	public String DOT_JPG = ".jpg";
	public String DOT_GIF = ".gif";
	public String DOT_PNG = ".png";
	public String DOT_TIF = ".tif";
	public String HTTP_STATUS_301 = "301";
	public String HTTP_STATUS_429 = "429";
	public String HTTP_STATUS_403 = "403";
	public String HTTP_STATUS_901 = "901";
	public String HTTP_STATUS_999 = "999";
	public int MAX_MESSAGES_BEFORE_CHECKING_STOP_QUEUE = 10;
	public int SQS_MSG_TIMEOUT_IN_43200_SECONDS = 43200;
	public int HTTP_STATUS_CODE_900 = 900;
	public int HTTP_STATUS_CODE_901 = 901;
	public int HTTP_STATUS_CODE_599 = 599;
	public String[] SKIP_PAUSE_COMP_DOMAINS = new String[] { "www.redfin.com", };
	public int RETRY_HTTP_STATUS_403_5XX_WAIT_MILLISECONDS_TARGET_URL = 1000;
	public int RETRY_HTTP_STATUS_403_5XX_WAIT_MILLISECONDS_COMPETITOR_URL = 0;
	public int RETRY_HTTP_STATUS_403_5XX_WAIT_MILLISECONDS_LINK_CLARITY = 0;
	public String ILLEGAL_STATE_EXCEPTION_MSG = "IllegalStateException";
	public String PAGE_CRAWLER_API_EXCEPTION_MSG = "PageCrawlerApiException";
	public int WEB_SERVICE_PORT_NUMBER = 16819;
	public String INTERNAL_KEY = "89it3op5-39n0-583x-mg61-0gd8g3471l5b";
//	public String INTERNAL_KEY = "20ac1cb6-15e1-493a-ab43-5eb3b9747f3a";
	public String INTERNAL_TYK_HEADER = "seoclarity_tky_internal_authorization";
	public String RUNTIME_PROPERTY_NAME_POLITE_CRAWL_WEB_SERVICE_ENDPOINT = "polite.crawl.web.service.endpoint";
	public String NUMBER_1 = "1";
	public String TWO_SPACES = "  ";
	public String RUNTIME_PROPERTY_NAME_HTML_SOURCE_FILES_FOLDER = "html.source.files.folder";
	public String COLON_SLASH_SLASH = "://";
	public String RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_SOURCE_USER = "crawler.v3.clickhouse.source.user";
	public String RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_SOURCE_PASSWORD = "crawler.v3.clickhouse.source.password";
	public String RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DESTINATION_USER = "crawler.v3.clickhouse.destination.user";
	public String RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DESTINATION_PASSWORD = "crawler.v3.clickhouse.destination.password";
	public String SELECTOR_TYPE_COLON = "Selector Type:";
	public String SELECTOR_COLON = ", Selector:";
	public String SELECTOR_TYPE = "selector_type";
	public String SELECTOR = "selector";
	public String URL_SELECTOR_TYPE = "url_selector_type";
	public String URL_SELECTOR = "url_selector";
	public String URL_SELECTOR_TYPE_TEXT_ALL = "ALL";
	public String URL_SELECTOR_TYPE_TEXT_CONTAINS = "CONTAINS";
	public String URL_SELECTOR_TYPE_TEXT_EQUALS = "EQUALS";
	public String URL_SELECTOR_TYPE_TEXT_REGEXP = "REGEXP";
	public String HTTP_STATUS_CODE_EQUALS_524 = "httpStatusCode=524";
	public int HTTP_STATUS_CODE_524 = 524;
	public String ORIGIN_TIME_OUT = "Origin Time-out";
	public String PAGE_CRAWLER_API_REQUEST_HEADER = "pageCrawlerApiRequestHeader";
	public String FUNC_NAME_SITE_CRAWL_HEADERS = "siteCrawlHeaders";
	public String TITLE_DEFAULT_REQUEST_HEADERS = "DEFAULT_REQUEST_HEADERS";
	public String DASH = "-";
	public String CUSTOM_DATA_CHG_IND = "custom_data_chg_ind";
	public String SLASH_UTF8_ENCODED = "%2F";
	public String FILE_NAME = "file_name";
	public String QUEUE_NAME_TEST_CONTENT_GUARD_DAILY_PREFIX = "TEST_CONTENT_GUARD_DAILY_";
	public String QUEUE_NAME_TEST_CONTENT_GUARD_DAILY_QUEUE_NAMES = "TEST_CONTENT_GUARD_DAILY_QUEUE_NAMES";
	public String QUEUE_NAME_CONTENT_GUARD_DAILY_PREFIX = "CONTENT_GUARD_DAILY_";
	public String QUEUE_NAME_CONTENT_GUARD_DAILY_QUEUE_NAMES = "CONTENT_GUARD_DAILY_QUEUE_NAMES";
	public String S3_LOCATION_PREFIX = "s3://fw-crawl/page_crawler/";
	public String TRUE = "true";
	public String FALSE = "false";
	public int FREQUENCY_TYPE_DAILY = 1;
	public int FREQUENCY_TYPE_HOURLY = 2;
	public int FREQUENCY_TYPE_UPLOADED = 3;
	public String QUEUE_NAME_TEST_CONTENT_GUARD_HOURLY_PREFIX = "TEST_CONTENT_GUARD_HOURLY_";
	public String QUEUE_NAME_TEST_CONTENT_GUARD_HOURLY_QUEUE_NAMES = "TEST_CONTENT_GUARD_HOURLY_QUEUE_NAMES";
	public String QUEUE_NAME_CONTENT_GUARD_HOURLY_PREFIX = "CONTENT_GUARD_HOURLY_";
	public String QUEUE_NAME_CONTENT_GUARD_HOURLY_QUEUE_NAMES = "CONTENT_GUARD_HOURLY_QUEUE_NAMES";
	public String QUEUE_NAME_TEST_CONTENT_GUARD_UPLOADED_PREFIX = "TEST_CONTENT_GUARD_UPLOADED_";
	public String QUEUE_NAME_TEST_CONTENT_GUARD_UPLOADED_QUEUE_NAMES = "TEST_CONTENT_GUARD_UPLOADED_QUEUE_NAMES";
	public String QUEUE_NAME_CONTENT_GUARD_UPLOADED_PREFIX = "CONTENT_GUARD_UPLOADED_";
	public String QUEUE_NAME_CONTENT_GUARD_UPLOADED_QUEUE_NAMES = "CONTENT_GUARD_UPLOADED_QUEUE_NAMES";
	public int CRAWL_STATUS_NOT_CRAWLED = 0;
	public int CRAWL_STATUS_TO_BE_CRAWLED = 1;
	public int CRAWL_STATUS_CRAWLED = 2;
	public String QUEUE_NAME_CONTENT_GUARD = "CONTENT_GUARD";
	public String RESOURCE_TYPE_WARC = "WARC";
	public String RESOURCE_LOCATION_TYPE_S3 = "S3";
	public String GRAINGER_COM = "grainger.com";
	public String M_GRAINGER_COM = "m.grainger.com";
	public String SLASH_MOBILE_SLASH = "/mobile/";
	public String WWW_GRAINGER_COM_MOBILE_USER_AGENT = "Mozilla/5.0 (Linux; Android 6.0.1; Nexus 5X Build/MMB29P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2272.96 Mobile Safari/537.36 (compatible; ClarityBot/9.0; +https://www.seoclarity.net/bot.html)";
	public String AUTO_ASSOCIATE_TARGET_URLS = "autoAssociateTargetUrls";
	public String SINGLE_FORWARD_SLASHES = "/";
	public String PAGE_ANALYSIS_RESULTS_REVERSE = "page_analysis_results_reverse";
	public String CLICKHOUSE_SEO_DAILY_RANKING_DATABASE_PROPERTIES = "clickhouse.seo_daily_ranking.database.properties";
	public String COMPANY_NAME_EXPEDIA = "Expedia";
	public String COMPANY_NAME_CARRENTALS_COM = "CarRentals.com";
	public String COMPANY_NAME_COUPON_CABIN = "Coupon Cabin";
	public String COMPANY_NAME_RETAIL_ME_NOT_COM = "RetailMeNot.com";
	public int MAX_SEND_EMAIL_RETRY_COUNT = 5;
	public int RETRY_WAIT_TIME_IN_MILLISECONDS = 8888;
	public String BASE_TAG = "base_tag";
	public String BASE_TAG_FLAG = "base_tag_flag";
	public String BASE_TAG_TARGET = "base_tag_target";
	public String BASE_TAG_CHG_IND = "base_tag_chg_ind";
	public String BASE_TAG_FLAG_CHG_IND = "base_tag_flag_chg_ind";
	public String BASE_TAG_TARGET_CHG_IND = "base_tag_target_chg_ind";
	public String BASE_TAG_ADDED_IND = "base_tag_added_ind";
	public String BASE_TAG_REMOVED_IND = "base_tag_removed_ind";
	public String REGION = "region";
	public String LONDON = "London";
	public String PAGE_ANALYSIS_FRAGMENTS = "page_analysis_fragments";
	public String PAGE_ANALYSIS_FRAGMENTS_CHG_IND = "page_analysis_fragments_chg_ind";
	public String HTTPS_WWW_REMAX_COM = "https://www.remax.com";
	public String CHANGE_TYPE_ADDED = "added";
	public String CHANGE_TYPE_MODIFIED = "modified";
	public String CHANGE_TYPE_REMOVED = "removed";
	public String CRAWL_HOUR_00 = "00";
	public String CRAWL_HOUR_23 = "23";
	public String CRAWL_TIMESTAMP_START_OF_HOUR = ":00:00";
	public String CRAWL_TIMESTAMP_END_OF_HOUR = ":59:59";
	public String UNDERSCORE_TO_UNDERSCORE = "_to_";
	public String CUSTOM_DATA_ADDED_IND = "custom_data_added_ind";
	public String CUSTOM_DATA_REMOVED_IND = "custom_data_removed_ind";
	public String COMMAND_TIMELINE = "timeline";
	public String COMMAND_DOMAIN_SUMMARY = "domain_summary";
	public String COMMAND_URL_DETAILS = "url_details";
	public String COMMAND_INDICATOR_URL_LIST = "indicator_url_list";
	public String COMMAND_URL_ALL_DETAILS = "url_all_details";
	public String COMMAND_PAGE_ANALYSIS_ISSUES = "page_analysis_issues";
	public String STRING_WITH_ONE_LEADING_ZERO = "%02d";
	public String FANATICS_COM = "fanatics.com";
	public String RESPONSE_HEADERS_ADDED_IND = "response_headers_added_ind";
	public String RESPONSE_HEADERS_REMOVED_IND = "response_headers_removed_ind";
	public int INDICATOR_FLAG_ALL = 1;
	public int INDICATOR_FLAG_CRITICAL = 2;
	public int INDICATOR_FLAG_CUSTOM = 3;
	public String NOTIFICATION_EMAIL_ADDRESS = "<EMAIL>";
	public String DEV_EMAIL_ADDRESS = "<EMAIL>";
	public int RESOURCE_TYPE_TARGET_URL = 1;
	public String CLICKHOUSE_TABLE_NAME_NULL = null;
	public String KEYWORD_RANKCHECK_ID = "keyword_rankcheck_id";
	public int SEARCH_ENGINE_ID_BING = 255;
	public int DATABASE_NUMERIC_NULL_FIELD_PROXY_VALUE = Integer.MAX_VALUE;
	public int WEBBOOK_TYPE_SLACK = 1;
	public int WEBBOOK_ALERT_TYPE_CONTENT_GUARD = 1;
	public String HTML_ESCAPED_QUOTE = "&quot;";
	public String LESS_THAN_SIGN = "<";
	public String GREATER_THAN_SIGN = ">";
	public int SORT_BY_URL_ASC = 1;
	public int SORT_BY_URL_DESC = 2;
	public int SORT_BY_CRAWL_TIMESTAMP_ASC = 3;
	public int SORT_BY_CRAWL_TIMESTAMP_DESC = 4;
	public int SORT_BY_TOTAL_CHANGES_ASC = 5;
	public int SORT_BY_TOTAL_CHANGES_DESC = 6;
	public int SORT_BY_ADDED_ASC = 7;
	public int SORT_BY_ADDED_DESC = 8;
	public int SORT_BY_MODIFIED_ASC = 9;
	public int SORT_BY_MODIFIED_DESC = 10;
	public int SORT_BY_REMOVED_ASC = 11;
	public int SORT_BY_REMOVED_DESC = 12;
	public int SORT_BY_RESPONSE_CODE_ASC = 13;
	public int SORT_BY_RESPONSE_CODE_DESC = 14;
	public int SORT_BY_LAST_UPDATE_TIMESTAMP_ASC = 15;
	public int SORT_BY_LAST_UPDATE_TIMESTAMP_DESC = 16;
	public int SORT_BY_CHANGE_INDICATOR_ASC = 17;
	public int SORT_BY_CHANGE_INDICATOR_DESC = 18;
	public int SORT_BY_CHANGE_TYPE_ASC = 19; // added, modified, removed
	public int SORT_BY_CHANGE_TYPE_DESC = 20; // removed, modified, added
	public int SORT_BY_SEVERITY_ASC = 21; // critical, high, medium, low
	public int SORT_BY_SEVERITY_DESC = 22; // low, medium, high, critical

	public int SORT_BY_TOTAL_SEVERITY_CRITICAL_ASC = 23;
	public int SORT_BY_TOTAL_SEVERITY_CRITICAL_DESC = 24;
	public int SORT_BY_TOTAL_SEVERITY_HIGH_ASC = 25;
	public int SORT_BY_TOTAL_SEVERITY_HIGH_DESC = 26;
	public int SORT_BY_TOTAL_SEVERITY_MEDIUM_ASC = 27;
	public int SORT_BY_TOTAL_SEVERITY_MEDIUM_DESC = 28;
	public int SORT_BY_TOTAL_SEVERITY_LOW_ASC = 29;
	public int SORT_BY_TOTAL_SEVERITY_LOW_DESC = 30;
	public int SORT_BY_TOTAL_CHANGE_TYPE_ADDED_ASC = 31;
	public int SORT_BY_TOTAL_CHANGE_TYPE_ADDED_DESC = 32;
	public int SORT_BY_TOTAL_CHANGE_TYPE_MODIFIED_ASC = 33;
	public int SORT_BY_TOTAL_CHANGE_TYPE_MODIFIED_DESC = 34;
	public int SORT_BY_TOTAL_CHANGE_TYPE_REMOVED_ASC = 35;
	public int SORT_BY_TOTAL_CHANGE_TYPE_REMOVED_DESC = 36;

	public String COMMAND_SLACK = "slack";
	public String QUEUE_NAME_TEST_ZAPIER_CONTENT_GUARD_ALERT_PREFIX = "TEST_ZAPIER_CONTENT_GUARD_ALERT_";
	public String QUEUE_NAME_ZAPIER_CONTENT_GUARD_ALERT_PREFIX = "ZAPIER_CONTENT_GUARD_ALERT_";

	// when SQS returns error, sleep for thirty seconds
	public int SQS_RETRY_SLEEP_TIME = 30000;

	public int CONTENT_GUARD_CHANGE_SEVERITY_CRITICAL = 1;
	public int CONTENT_GUARD_CHANGE_SEVERITY_HIGH = 2;
	public int CONTENT_GUARD_CHANGE_SEVERITY_MEDIUM = 3;
	public int CONTENT_GUARD_CHANGE_SEVERITY_LOW = 4;

	public String CONTENT_GUARD_CHANGE_SEVERITY_CRITICAL_DESC = "Critical";
	public String CONTENT_GUARD_CHANGE_SEVERITY_HIGH_DESC = "High";
	public String CONTENT_GUARD_CHANGE_SEVERITY_MEDIUM_DESC = "Medium";
	public String CONTENT_GUARD_CHANGE_SEVERITY_LOW_DESC = "Low";

	public int ZAPIER_TRIGGER_TYPE_CONTENT_GUARD_ALERT = 1;
	public int ZAPIER_TRIGGER_TYPE_CRAWL_INITIATED_ALERT = 2;
	public int ZAPIER_TRIGGER_TYPE_CRAWL_COMPLETED_ALERT = 3;
	public int ZAPIER_TRIGGER_TYPE_PAGE_TAG_CONTENT_ALERT = 4;
	public int ZAPIER_TRIGGER_TYPE_TARGET_URL_CHANGE_ALERT = 5;

	public String API000011 = "API-000011";
	public String API000012 = "API-000012";
	public String API000036 = "API-000036";
	public String COMMAND_CONTENT_GUARD_ALERT = "content_guard_alert";
	public String COMMAND_CRAWL_INITIATED_ALERT = "crawl_initiated_alert";
	public String COMMAND_CRAWL_COMPLETED_ALERT = "crawl_completed_alert";
	public String COMMAND_PAGE_TAG_CONTENT_ALERT = "page_tag_content_alert";
	public String COMMAND_TARGET_URL_CHANGE_ALERT = "target_url_change_alert";

	public String COMMAND_UPDATE_STRUCTURED_DATA = "update_structured_data";
	public String COMMAND_RETRIEVE_STRUCTURED_DATA = "retrieve_structured_data";
	public String COMMAND_DELETE_STRUCTURED_DATA = "delete_structured_data";
	public String COMMAND_UPDATE_ACTIONABLE_INSIGHT = "update_actionable_insight";
	public String COMMAND_RETRIEVE_ACTIONABLE_INSIGHT = "retrieve_actionable_insight";
	public String COMMAND_DELETE_ACTIONABLE_INSIGHT = "delete_actionable_insight";
	public String RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_BASE_ENDPOINT = "google.tag.api.service.endpoint";
	public String RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN = "google.tag.api.edit.containers.access.token";
	public String RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN = "google.tag.api.edit.container.versions.access.token";
	public String RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN = "google.tag.api.publish.access.token";

	//public String[] CLIENT_DOMAINS_HTTP_STATUS_CODE_999 = new String[] { "www.tractorsupply.com" };
	public String DIV_START_TAG_STYLE_COLOR_BLUE = "<div style='color:blue;'>";
	public String DIV_START_TAG_STYLE_COLOR_RED = "<div style='color:red;'>";
	public String DIV_END_TAG = "</div>";

	public String COMMAND_CREATE_JAVASCRIPT = "create_javascript";
	public String COMMAND_RETRIEVE_JAVASCRIPT = "retrieve_javascript";
	public String COMMAND_UPDATE_JAVASCRIPT = "update_javascript";
	public String COMMAND_DELETE_JAVASCRIPT = "delete_javascript";
	public String COMMAND_UPDATE_JAVASCRIPT_URL = "update_javascript_url";
	public String COMMAND_RETRIEVE_JAVASCRIPT_URL = "retrieve_javascript_url";
	public String TARGET_COM = "target.com";
	public String COMMAND_LIST_JAVASCRIPTS = "list_javascripts";
	public int BIG_DECIMAL_SCALE = 5;
	public BigDecimal DAMPING_FACTOR = new BigDecimal(0.85).setScale(BIG_DECIMAL_SCALE, RoundingMode.HALF_UP);
	public BigDecimal MARGIN_OF_ERROR = new BigDecimal(0.001).setScale(BIG_DECIMAL_SCALE, RoundingMode.HALF_UP);
	public BigDecimal BIG_DECIMAL_ONE = new BigDecimal(1).setScale(BIG_DECIMAL_SCALE, RoundingMode.HALF_UP);
	public BigDecimal BIG_DECIMAL_ZERO = new BigDecimal(0).setScale(BIG_DECIMAL_SCALE, RoundingMode.HALF_UP);
	public BigDecimal BIG_DECIMAL_NEGATIVE_ONE = new BigDecimal(-1).setScale(BIG_DECIMAL_SCALE, RoundingMode.HALF_UP);
	public String BACKTICK = "`";
	public Long LONG_ONE = new Long(1);
	public String ACTUAL = "Actual";
	public String PRED = "Pred";
	public String PRED_LOWER = "Pred_lower";
	public String PRED_UPPER = "Pred_upper";
	public String PRED_SD = "Pred_sd";
	public String ABS_EFFECT = "AbsEffect";
	public String ABS_EFFECT_LOWER = "AbsEffect_lower";
	public String ABS_EFFECT_UPPER = "AbsEffect_upper";
	public String ABS_EFFECT_SD = "AbsEffect_sd";
	public String REL_EFFECT = "RelEffect";
	public String REL_EFFECT_LOWER = "RelEffect_lower";
	public String REL_EFFECT_UPPER = "RelEffect_upper";
	public String REL_EFFECT_SD = "RelEffect_sd";
	public String ALPHA = "alpha";
	public String P = "p";
	public int TEST_DATA_POINTS_POSITION = 0;
	public int COUNTER_FACTUAL_PREDICTION_DATA_POINTS_POSITION = 2;
	public int COUNTER_FACTUAL_PREDICTION_DATA_POINTS_POSITION_LOWER = 3;
	public int COUNTER_FACTUAL_PREDICTION_DATA_POINTS_POSITION_UPPER = 4;
	public int POINTWISE_CAUSAL_EFFECT_DATA_POINTS_POSITION = 8;
	public int POINTWISE_CAUSAL_EFFECT_DATA_POINTS_POSITION_LOWER = 9;
	public int POINTWISE_CAUSAL_EFFECT_DATA_POINTS_POSITION_UPPER = 10;
	public int CUMULATIVE_EFFECT_DATA_POINTS_POSITION = 11;
	public int CUMULATIVE_EFFECT_DATA_POINTS_POSITION_LOWER = 12;
	public int CUMULATIVE_EFFECT_DATA_POINTS_POSITION_UPPER = 13;
	public String COMMAND_CALCULATE_CORRELATION_COEFFICIENT = "calculate_correlation_coefficient";
	public String COMMAND_ANALYZE = "analyze";
	public String COMMAND_ANALYZE_WITHOUT_CONTROL_SERIES = "analyze_without_control_series";
	public String TREND = "trend";
	public String TREND_LOWER = "trend_lower";
	public String TREND_UPPER = "trend_upper";
	public String YHAT = "yhat";
	public String YHAT_LOWER = "yhat_lower";
	public String YHAT_UPPER = "yhat_upper";
	public String YEARLY = "yearly";
	public String SUNDAY = "Sunday";
	public String MONDAY = "Monday";
	public String TUESDAY = "Tuesday";
	public String WEDNESDAY = "Wednesday";
	public String THURSDAY = "Thursday";
	public String FRIDAY = "Friday";
	public String SATURDAY = "Saturday";
	public int NON_LEAP_YEAR = 2019;
	public int R_PACKAGE_CAUSAL_IMPACT = 1;
	public int R_PACKAGE_PROPHET = 2;
	public String APPLICATION_SLASH_X_WWW_FORM_URLENCODED = "application/x-www-form-urlencoded";
	public String ACCESS_TOKEN = "access_token";
	public String OWN_DOMAIN_ID = "ownDomainId";
	public String INDEX_LIST = "indexList";
	public String DOMAIN_LIST = "domainList";
	public String LOCATION_ID_LIST = "locationIdList";
	public String INCLUDE_101 = "include101";
	public String MOBILE = "mobile";
	public String ENGINE_ID = "engineId";
	public String LANGUAGE_ID = "languageId";
	public String TRUE_RANK = "trueRank";
	public String SEPARATE_GEO_TYPE = "separateGeoType";
	public String TAG_ID_LIST = "tagIdList";
	public String DWN = "dwm";
	public String OFFSET = "offset";
	public String TOP = "top";
	public int CLARITY_SUMMARY_MAXIMUM_DEPTH = 99;
	public int CRAWL_STATUS_COMPLETED = 3;
	public int CRAWL_STATE_ACTIVE = 1;
	public String AUDITED_PAGES = "Audited Pages";
	public String SUMMARY = "Summary";
	public String CRAWLABILITY_ISSUES = "Crawlability Issues";
	public String REDIRECTED_3XX = "Redirected (3xx)";
	public String BROKEN_4XX = "Broken (4xx)";
	public String ERROR_5XX = "Error (5xx)";
	public String OTHER = "Other";
	public String PAGE_ANALYSIS_ISSUES = "Page Analysis Issue";
	public String JSON = "json";
	public String API000000 = "API-000000";
	public String COMMAND_SITE_HEALTH_SUMMARY = "site_health_summary";
	public String COMMAND_CRAWL_PROJECT = "crawl_project";
	public String COMMAND_SEARCH_ENGINE = "search_engine";
	public String COMMAND_TRACKED_PAGES = "tracked_pages";
	public String COMMAND_USAGE = "usage";

	public String AREA = "Area";
	public String BEST_CONTROL = "BestControl";
	public String RELATIVE_DISTANCE = "RelativeDistance";
	public String CORRELATION = "Correlation";
	public String LENGTH = "Length";
	public String SUM_TEST = "SUMTEST";
	public String SUM_CNTL = "SUMCNTL";
	public String RAW_DIST = "RAWDIST";
	public String CORRELATION_OF_LOGS = "Correlation_of_logs";
	public String RANK = "rank";
	public String NORM_DIST = "NORMDIST";
	public String TEST_DATA = "TestData";
	public String ZOO_DATA = "ZooData";
	public String TEST_NAME = "TestName";
	public String CONTROL_NAME = "ControlName";
	public String ABSOLUTE_EFFECT = "AbsoluteEffect";
	public String ABSOLUTE_EFFECT_LOWER = "AbsoluteEffectLower";
	public String ABSOLUTE_EFFECT_UPPER = "AbsoluteEffectUpper";
	public String RELATIVE_EFFECT = "RelativeEffect";
	public String RELATIVE_EFFECT_LOWER = "RelativeEffectLower";
	public String RELATIVE_EFFECT_UPPER = "RelativeEffectUpper";
	public String TAIL_PROB = "TailProb";
	public String PRE_PERIOD_MAPE = "PrePeriodMAPE";
	public String DW = "DW";
	public String PREDICTED = "Predicted";
	public String LOWER_BOUND = "lower_bound";
	public String UPPER_BOUND = "upper_bound";
	public String MARKET = "Market";
	public String AVERAGE_BETA = "AverageBeta";
	public String REPORT = "report";
	public String SERIES = "series";
	public int R_PACKAGE_MARKET_MATCHING = 3;
	public String JAVASCRIPT_TIMEOUT_IN_SECOND = "javascriptTimeoutInSecond";
	public String RUNTIME_PROPERTY_NAME_SERP_ANALYZER_API_ENDPOINT = "serp.analyzer.api.endpoint";
	public String BACKUP_FILE_EXT = ".bin.zstd";
	public String BACKUP_TYPE_INITIAL = "initial";
	public String BACKUP_TYPE_INCREMENTAL = "incremental";
	public String REGION_JAVASCRIPT = "Javascript";
	public int HTTP_STATUS_CODE_997 = 997;
	public int MAX_HTTP_STATUS_CODE_997_RETRY_COUNT = 1;
	public String UNDERSCORE_CURRENT = "_current";
	public String UNDERSCORE_PREVIOUS = "_previous";
	public String UPDATE_TIMESTAMP = "update_timestamp";
	public String CURRENT_CRAWL_TIMESTAMP = "current_crawl_timestamp";
	public String PREVIOUS_CRAWL_TIMESTAMP = "previous_crawl_timestamp";
	public String TOTAL_ALTERNATE_LINKS_CHG_IND = "total_alternate_links_chg_ind";
	public String TOTAL_AMPHTML_HREF_CHG_IND = "total_amphtml_href_chg_ind";
	public String TOTAL_ANALYZED_URL_S_CHG_IND = "total_analyzed_url_s_chg_ind";
	public String TOTAL_ARCHIVE_FLG_CHG_IND = "total_archive_flg_chg_ind";
	public String TOTAL_BASE_TAG_ADDED_IND = "total_base_tag_added_ind";
	public String TOTAL_BASE_TAG_CHG_IND = "total_base_tag_chg_ind";
	public String TOTAL_BASE_TAG_REMOVED_IND = "total_base_tag_removed_ind";
	public String TOTAL_BASE_TAG_TARGET_CHG_IND = "total_base_tag_target_chg_ind";
	public String TOTAL_BLOCKED_BY_ROBOTS_CHG_IND = "total_blocked_by_robots_chg_ind";
	public String TOTAL_CANONICAL_ADDED_IND = "total_canonical_added_ind";
	public String TOTAL_CANONICAL_CHG_IND = "total_canonical_chg_ind";
	public String TOTAL_CANONICAL_HEADER_FLAG_CHG_IND = "total_canonical_header_flag_chg_ind";
	public String TOTAL_CANONICAL_HEADER_TYPE_CHG_IND = "total_canonical_header_type_chg_ind";
	public String TOTAL_CANONICAL_REMOVED_IND = "total_canonical_removed_ind";
	public String TOTAL_CANONICAL_TYPE_CHG_IND = "total_canonical_type_chg_ind";
	public String TOTAL_CANONICAL_URL_IS_CONSISTENT_CHG_IND = "total_canonical_url_is_consistent_chg_ind";
	public String TOTAL_CONTENT_TYPE_CHG_IND = "total_content_type_chg_ind";
	public String TOTAL_CUSTOM_DATA_ADDED_IND = "total_custom_data_added_ind";
	public String TOTAL_CUSTOM_DATA_CHG_IND = "total_custom_data_chg_ind";
	public String TOTAL_CUSTOM_DATA_REMOVED_IND = "total_custom_data_removed_ind";
	public String TOTAL_DESCRIPTION_ADDED_IND = "total_description_added_ind";
	public String TOTAL_DESCRIPTION_CHG_IND = "total_description_chg_ind";
	public String TOTAL_DESCRIPTION_LENGTH_CHG_IND = "total_description_length_chg_ind";
	public String TOTAL_DESCRIPTION_REMOVED_IND = "total_description_removed_ind";
	public String TOTAL_ERROR_MESSAGE_CHG_IND = "total_error_message_chg_ind";
	public String TOTAL_FINAL_RESPONSE_CODE_CHG_IND = "total_final_response_code_chg_ind";
	public String TOTAL_FOLLOW_FLG_CHG_IND = "total_follow_flg_chg_ind";
	public String TOTAL_H1_ADDED_IND = "total_h1_added_ind";
	public String TOTAL_H1_CHG_IND = "total_h1_chg_ind";
	public String TOTAL_H1_COUNT_CHG_IND = "total_h1_count_chg_ind";
	public String TOTAL_H1_LENGTH_CHG_IND = "total_h1_length_chg_ind";
	public String TOTAL_H1_REMOVED_IND = "total_h1_removed_ind";
	public String TOTAL_H2_ADDED_IND = "total_h2_added_ind";
	public String TOTAL_H2_CHG_IND = "total_h2_chg_ind";
	public String TOTAL_H2_REMOVED_IND = "total_h2_removed_ind";
	public String TOTAL_HEADER_NOARCHIVE_CHG_IND = "total_header_noarchive_chg_ind";
	public String TOTAL_HEADER_NOFOLLOW_CHG_IND = "total_header_nofollow_chg_ind";
	public String TOTAL_HEADER_NOINDEX_CHG_IND = "total_header_noindex_chg_ind";
	public String TOTAL_HEADER_NOODP_CHG_IND = "total_header_noodp_chg_ind";
	public String TOTAL_HEADER_NOSNIPPET_CHG_IND = "total_header_nosnippet_chg_ind";
	public String TOTAL_HEADER_NOYDIR_CHG_IND = "total_header_noydir_chg_ind";
	public String TOTAL_HREFLANG_ERRORS_CHG_IND = "total_hreflang_errors_chg_ind";
	public String TOTAL_HREFLANG_LINKS_ADDED_IND = "total_hreflang_links_added_ind";
	public String TOTAL_HREFLANG_LINKS_CHG_IND = "total_hreflang_links_chg_ind";
	public String TOTAL_HREFLANG_LINKS_OUT_COUNT_CHG_IND = "total_hreflang_links_out_count_chg_ind";
	public String TOTAL_HREFLANG_LINKS_REMOVED_IND = "total_hreflang_links_removed_ind";
	public String TOTAL_HREFLANG_URL_COUNT_CHG_IND = "total_hreflang_url_count_chg_ind";
	public String TOTAL_INDEX_FLG_CHG_IND = "total_index_flg_chg_ind";
	public String TOTAL_INDEXABLE_CHG_IND = "total_indexable_chg_ind";
	public String TOTAL_INSECURE_RESOURCES_CHG_IND = "total_insecure_resources_chg_ind";
	public String TOTAL_META_CHARSET_CHG_IND = "total_meta_charset_chg_ind";
	public String TOTAL_META_CONTENT_TYPE_CHG_IND = "total_meta_content_type_chg_ind";
	public String TOTAL_META_DISABLED_SITELINKS_CHG_IND = "total_meta_disabled_sitelinks_chg_ind";
	public String TOTAL_META_NOODP_CHG_IND = "total_meta_noodp_chg_ind";
	public String TOTAL_META_NOSNIPPET_CHG_IND = "total_meta_nosnippet_chg_ind";
	public String TOTAL_META_NOYDIR_CHG_IND = "total_meta_noydir_chg_ind";
	public String TOTAL_META_REDIRECT_CHG_IND = "total_meta_redirect_chg_ind";
	public String TOTAL_MIXED_REDIRECTS_CHG_IND = "total_mixed_redirects_chg_ind";
	public String TOTAL_MOBILE_REL_ALTERNATE_URL_IS_CONSISTENT_CHG_IND = "total_mobile_rel_alternate_url_is_consistent_chg_ind";
	public String TOTAL_NOODP_CHG_IND = "total_noodp_chg_ind";
	public String TOTAL_NOSNIPPET_CHG_IND = "total_nosnippet_chg_ind";
	public String TOTAL_NOYDIR_CHG_IND = "total_noydir_chg_ind";
	public String TOTAL_OG_MARKUP_CHG_IND = "total_og_markup_chg_ind";
	public String TOTAL_OG_MARKUP_LENGTH_CHG_IND = "total_og_markup_length_chg_ind";
	public String TOTAL_OPEN_GRAPH_ADDED_IND = "total_open_graph_added_ind";
	public String TOTAL_OPEN_GRAPH_REMOVED_IND = "total_open_graph_removed_ind";
	public String TOTAL_OUTLINK_COUNT_CHG_IND = "total_outlink_count_chg_ind";
	public String TOTAL_PAGE_ANALYSIS_RESULTS_CHG_IND_JSON = "total_page_analysis_results_chg_ind_json";
	public String TOTAL_PAGE_LINK_CHG_IND = "total_page_link_chg_ind";
	public String TOTAL_REDIRECT_301_DETECTED_IND = "total_redirect_301_detected_ind";
	public String TOTAL_REDIRECT_301_REMOVED_IND = "total_redirect_301_removed_ind";
	public String TOTAL_REDIRECT_302_DETECTED_IND = "total_redirect_302_detected_ind";
	public String TOTAL_REDIRECT_302_REMOVED_IND = "total_redirect_302_removed_ind";
	public String TOTAL_REDIRECT_BLOCKED_CHG_IND = "total_redirect_blocked_chg_ind";
	public String TOTAL_REDIRECT_BLOCKED_REASON_CHG_IND = "total_redirect_blocked_reason_chg_ind";
	public String TOTAL_REDIRECT_CHAIN_CHG_IND = "total_redirect_chain_chg_ind";
	public String TOTAL_REDIRECT_DIFF_CODE_IND = "total_redirect_diff_code_ind";
	public String TOTAL_REDIRECT_FINAL_URL_CHG_IND = "total_redirect_final_url_chg_ind";
	public String TOTAL_REDIRECT_TIMES_CHG_IND = "total_redirect_times_chg_ind";
	public String TOTAL_RESPONSE_CODE_CHG_IND = "total_response_code_chg_ind";
	public String TOTAL_RESPONSE_HEADERS_ADDED_IND = "total_response_headers_added_ind";
	public String TOTAL_RESPONSE_HEADERS_REMOVED_IND = "total_response_headers_removed_ind";
	public String TOTAL_ROBOTS_ADDED_IND = "total_robots_added_ind";
	public String TOTAL_ROBOTS_CONTENTS_CHG_IND = "total_robots_contents_chg_ind";
	public String TOTAL_ROBOTS_REMOVED_IND = "total_robots_removed_ind";
	public String TOTAL_STRUCTURED_DATA_CHG_IND = "total_structured_data_chg_ind";
	public String TOTAL_TITLE_ADDED_IND = "total_title_added_ind";
	public String TOTAL_TITLE_CHG_IND = "total_title_chg_ind";
	public String TOTAL_TITLE_LENGTH_CHG_IND = "total_title_length_chg_ind";
	public String TOTAL_TITLE_REMOVED_IND = "total_title_removed_ind";
	public String TOTAL_VIEWPORT_ADDED_IND = "total_viewport_added_ind";
	public String TOTAL_VIEWPORT_CONTENT_CHG_IND = "total_viewport_content_chg_ind";
	public String TOTAL_VIEWPORT_REMOVED_IND = "total_viewport_removed_ind";
	public String HOURLY = "hourly";
	public String TOTAL = "total";
	public String SPACE_OR_SPACE = " or ";
	public String SPACE_EQUAL_SPACE_ONE = " = 1";
	public String SPACE_GREATER_SPACE_SINGLE_QUOTES = " > ''";
	public String CRAWL_DATE_HOUR = "crawl_date_hour";
	public String URL_TEXT_TYPE = "UrlTextType";
	public String CONTENT_TYPE_ACTION_CONTAINS = "ct";
	public String CONTENT_TYPE_ACTION_DOES_NOT_CONTAIN = "nct";
	public String CONTENT_TYPE_ACTION_ENDS_WITH = "ew";
	public String CONTENT_TYPE_ACTION_IS = "eq";
	public String CONTENT_TYPE_ACTION_IS_NOT = "neq";
	public String CONTENT_TYPE_ACTION_REGEXP = "pt";
	public String CONTENT_TYPE_ACTION_REGEXP_NOT_MATCH = "npt";
	public String CONTENT_TYPE_ACTION_STARTS_WITH = "sw";
	public String CANNOT_COMPILE_REGEXP = "CANNOT_COMPILE_REGEXP";
	public String AND = "and";
	public String OR = "or";
	public String RESPONSE_CODE_CHG_IND_TO = "response_code_chg_ind_to";
	public String ZERO_MM_SS = "00:00";
	public String CHANGE_INDICATOR = "change_indicator";
	public String CHANGE_TYPE = "change_type";
	public String CRITICAL_IND = "critical_ind";
	public int CHANGE_TYPE_NUMBER_ADDED = 1;
	public int CHANGE_TYPE_NUMBER_MODIFIED = 2;
	public int CHANGE_TYPE_NUMBER_REMOVED = 3;
	public String COMMAND_LIST_CHANGE_INDICATORS = "list_change_indicators";
	public String COMMAND_GET_URL_SUMMARY = "get_url_summary";
	public String SEVERITY_CRITICAL = "critical";
	public String SEVERITY_HIGH = "high";
	public String SEVERITY_MEDIUM = "medium";
	public String SEVERITY_LOW = "low";
	public String TOTAL_CHANGES = "total_changes";
	public String TOTAL_SEVERITY_CRITICAL = "total_severity_critical";
	public String TOTAL_SEVERITY_HIGH = "total_severity_high";
	public String TOTAL_SEVERITY_MEDIUM = "total_severity_medium";
	public String TOTAL_SEVERITY_LOW = "total_severity_low";
	public String TOTAL_CHANGE_TYPE_ADDED = "total_change_type_added";
	public String TOTAL_CHANGE_TYPE_MODIFIED = "total_change_type_modified";
	public String TOTAL_CHANGE_TYPE_REMOVED = "total_change_type_removed";
	public String ROBOTS_TXT = "robots.txt";
	public String CONTENT = "content";
	public String DATE_FORMAT_YYYYMMDD_HHMMSS_SSS = "yyyyMMdd_HHmmssSSS";
	public String HEADER_COLUMN_DATE = "DATE";
	public String HEADER_COLUMN_CHANGE_ELEMENT = "CHANGE ELEMENT";
	public String HEADER_COLUMN_CHANGE_SEVERITY = "CHANGE SEVERITY";
	public String HEADER_COLUMN_URL = "URL";
	public String HEADER_COLUMN_PREVIOUS = "PREVIOUS";
	public String HEADER_COLUMN_CURRENT = "CURRENT";
	public String FILE_EXTENSION_CSV = ".csv";
	public String CONTENT_GUARD_DOWNLOAD_ALL_FILE_NAME = "content_guard_download_all";
	public String DOWNLOAD_ALL_DELIMITER = ",";
	public String ROBOTS_TXT_CHG_IND = "robots_txt_chg_ind";
	public String ROBOT_TXT = "robot_txt";
	public String JSON_CLEANSED_1 = "[ {";
	public String JSON_CLEANSED_2 = "} ]";
	public String JSON_CLEANSED_3 = "[ ";
	public String JSON_CLEANSED_4 = " ]";
	public String JSON_CLEANSED_5 = "}, {";
	public String JSON_CLEANSED_6 = "{";
	public String JSON_CLEANSED_7 = "}";
	public String HTML_LINE_BREAK = "<br>";
	public String HTML_SPACE = "&nbsp;";
	public String NEW_DEFAULT_USER_AGENT = "Mozilla/5.0 (compatible; ClarityBot/9.0; +https://www.seoclarity.net/bot.html)";
	public String COMPANY_NAME_SKYSCANNER = "Skyscanner";
	public int SKYSCANNER_DOMAIN_ID_FOR_POLITE_CRAWL_CONFIG = 11545;
	// url type
	Integer URL_TYPE_MANAGED = 1;
	Integer URL_TYPE_RANKING = 2;
	String MULTI_STRING_SEPARATOR = "!_!";
}
