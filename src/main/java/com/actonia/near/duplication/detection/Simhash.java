package com.actonia.near.duplication.detection;

//import java.io.File;
//import java.io.FileNotFoundException;
//import java.io.FilenameFilter;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Scanner;
import java.util.Set;

import com.actonia.utils.SimhashUtils;

public class Simhash {

	// map key = key string
	// map value = value string
	Map<String, String> keyValueMap = new HashMap<String, String>();

	private static final int HASHLENGTH = 64;
	//private final String FOLDER;
	private HashMap<String, Integer> dictionary = new HashMap<String, Integer>();
	public String[] keys;
	HashMap<String, List<Word>> vectors = new HashMap<String, List<Word>>();

	public Simhash(Map<String, String> keyValueMap) {
		this.keyValueMap = keyValueMap;
		//this.FOLDER = folder;
		createDictionary();
	}

	private void createDictionary() {
		keys = allKeys();
		int count = 0;
		//System.out.print("Loading data..");
		for (int i = 0; i < keys.length; i++) {
			//if (i % 100 == 0)
			//	System.out.println(".");
			vectors.put(keys[i], new ArrayList<Word>());
			HashMap<String, Integer> words = getAllWords(keys[i]);
			for (String w : words.keySet()) {
				if (!dictionary.containsKey(w)) {
					dictionary.put(w, count);
					count++;
				}
				vectors.get(keys[i]).add(new Word(dictionary.get(w), words.get(w)));
			}
		}
		// System.out.println(dictionary);
	}

	public String[] allKeys() {
		String[] keys = null;
		Set<String> keySet = keyValueMap.keySet();
		keys = keySet.toArray(new String[0]);
		return keys;
	}

	//	public String[] allDocs() {
	//		File[] files = getAllFiles(FOLDER);
	//		List<String> results = new ArrayList<String>();
	//		for (File file : files) {
	//			if (file.isFile()) {
	//				results.add(file.getName());
	//			}
	//		}
	//		String[] fileNameArray = new String[results.size()];
	//		fileNameArray = results.toArray(fileNameArray);
	//		if (fileNameArray.length < 1) {
	//			System.out.println("Error: No file in the specified directory.");
	//			System.exit(0);
	//		}
	//		return fileNameArray;
	//	}

	// Get all the files from the given directory path
	//	private File[] getAllFiles(String path) {
	//		File[] allFiles = new File(path).listFiles(new FilenameFilter() {
	//			@Override
	//			public boolean accept(File dir, String name) {
	//				return !name.equals(".DS_Store");
	//			}
	//		});
	//		return allFiles;
	//	}

	// Returns array of processed words in lowercase from the given file
	private HashMap<String, Integer> getAllWords(String key) {
		//System.out.println("getAllWords() key=" + key);
		Scanner s;
		HashMap<String, Integer> words = new HashMap<String, Integer>();

		try {
			//s = new Scanner(new File(FOLDER + "/" + file), "ISO-8859-1");
			s = new Scanner(keyValueMap.get(key));
			while (s.hasNext()) {

				String w = s.next().replaceAll("[.,:;’']", "").toLowerCase();
				//System.out.println("getAllWords() w=" + w);
				if (w.length() < 3 || w.equals("the"))
					continue;
				if (!words.containsKey(w)) {
					words.put(w, 1);
				} else {
					words.put(w, words.get(w) + 1);
				}
			}
			s.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return words;
	}

	public double exactJaccard(String key1, String key2) {
		double exactJaccard;
		//System.out.println(file1 + " " + file2);
		List<Integer> wordsFile1 = new ArrayList<Integer>();
		List<Integer> wordsFile2 = new ArrayList<Integer>();
		List<Word> words1 = vectors.get(key1);
		List<Word> words2 = vectors.get(key2);
		//System.out.println("he " + words1);
		for (Word w : words1) {
			wordsFile1.add(w.getWord());
			//System.out.print("hi " + w.getWord() + " ");
		}
		for (Word w : words2) {
			wordsFile2.add(w.getWord());
		}
		Set<Integer> unionSet = new HashSet<Integer>(); // remove duplicates
		unionSet.addAll(wordsFile1);
		unionSet.addAll(wordsFile2);
		exactJaccard = (double) (wordsFile1.size() + wordsFile2.size() - unionSet.size()) / unionSet.size();
		return exactJaccard;
	}

	public double exactCosine(String key1, String key2) {
		double exactCosine;
		List<Integer> wordsFile1 = new ArrayList<Integer>();
		List<Integer> wordsFile2 = new ArrayList<Integer>();
		List<Word> words1 = vectors.get(key1);
		List<Word> words2 = vectors.get(key2);
		for (Word w : words1) {
			wordsFile1.add(w.getWord());
		}
		for (Word w : words2) {
			wordsFile2.add(w.getWord());
		}
		Set<Integer> unionSet = new HashSet<Integer>(); // remove duplicates
		unionSet.addAll(wordsFile1);
		unionSet.addAll(wordsFile2);
		exactCosine = (double) (wordsFile1.size() + wordsFile2.size() - unionSet.size()) / Math.sqrt(wordsFile1.size() * wordsFile2.size());
		return exactCosine;
	}

	public char[] simhashSignature(String key) {
		char[] sig = new char[HASHLENGTH];
		int[] temp = new int[HASHLENGTH];

		for (Word w : vectors.get(key)) {
			int wrd = w.getWord();
			int freq = w.getfrequency();
			long wordHash = MurmurHash.hash64(Integer.toString(wrd));
			char[] wh = String.format("%064d", new BigInteger(Long.toBinaryString(wordHash))).toCharArray();

			for (int i = 0; i < wh.length; i++) {
				if (wh[i] == '1')
					temp[i] += freq;
				else
					temp[i] -= freq;
			}
		}
		for (int i = 0; i < HASHLENGTH; i++) {
			if (temp[i] < 0)
				sig[i] = '0';
			else
				sig[i] = '1';
		}
		// System.out.println(sig.size);
		return sig;
	}

	// approximateCosine
	public double approximateCosine(String key1, String key2) {
		char[] sig1 = simhashSignature(key1);
		char[] sig2 = simhashSignature(key2);
		int distance = hammingDistance(sig1, sig2);
		double similarity = 1 - ((double) distance / HASHLENGTH);
		return similarity;
	}

	public int hammingDistance(char[] sig1, char[] sig2) {
		int count = 0;
		for (int i = 0; i < sig1.length; i++) {
			if (sig1[i] != sig2[i])
				count++;
		}
		return count;
	}

	public List<char[]> fingerprint() {
		//HashMap<String, char[]> table = new HashMap<String, char[]>();
		List<char[]> table = new ArrayList<char[]>();
		for (int i = 0; i < keys.length; i++) {
			char[] sig = simhashSignature(keys[i]);
			table.add(sig);
			//System.out.println(sig);
		}
		return table;
	}

	public int numTerms() {
		return dictionary.size();
	}

	public static void main(String[] args) throws Exception {
		//Simhash simhash = new Simhash("/Users/<USER>/IOWA STATE UNIVERSITY/Fall 17/CPR E 528/Project/Dataset/Space");
		//Simhash simhash = new Simhash("C:\\dev\\eclipse-workspace\\NearDuplicateDetection\\Dataset\\small");

		//Map<String, String> testKeyValueMap = new HashMap<String, String>();

		//testKeyValueMap.put("space-771.txt","Path: cantaloupe.srv.cs.cmu.edu!magnesium.club.cc.cmu.edu!pitt.edu!gatech!howland.reston.ans.net!darwin.sura.net!haven.umd.edu!cs.umd.edu!afterlife!blackbird.afit.af.mil!tkelso From: <EMAIL> (TS Kelso) Newsgroups: sci.space Subject: Two-Line Orbital Element Set:  Space Shuttle Keywords: Space Shuttle, Orbital Elements, Keplerian Message-ID: <<EMAIL>> Date: 29 Apr 93 22:27:19 GMT Sender: <EMAIL> Organization: Air Force Institute of Technology Lines: 18 Nntp-Posting-Host: scgraph.afit.af.mil  The most current orbital elements from the NORAD two-line element sets are carried on the Celestial BBS, (*************, and are updated daily (when possible).  Documentation and tracking software are also available on this system.  As a service to the satellite user community, the most current elements for the current shuttle mission are provided below.  The Celestial BBS may be accessed 24 hours/day at 300, 1200, 2400, 4800, or 9600 bps using 8 data bits, 1 stop bit, no parity.  Element sets (also updated daily), shuttle elements, and some documentation and software are also available via anonymous ftp from archive.afit.af.mil (***********) in the directory pub/space.  STS 55      1 22640U 93 27  A 93119.24999999  .00041555  00000-0  12437-3 0    90 2 22640  28.4657 249.3697 0008512 260.9747 152.1416 15.90732913   425 --  Dr TS Kelso                           Assistant Professor of <NAME_EMAIL>                    Air Force Institute of Technology ");

		//testKeyValueMap.put("space-753.txt","Newsgroups: sci.space Path: cantaloupe.srv.cs.cmu.edu!das-news.harvard.edu!noc.near.net!uunet!iris.mbvlab.wpafb.af.mil!blackbird.afit.af.mil!tkelso From: <EMAIL> (TS Kelso) Subject: Two-Line Orbital Element Set:  Space Shuttle Message-ID: <<EMAIL>> Keywords: Space Shuttle, Orbital Elements, Keplerian Sender: <EMAIL> Nntp-Posting-Host: scgraph.afit.af.mil Organization: Air Force Institute of Technology Date: Wed, 28 Apr 1993 21:06:41 GMT Lines: 18  The most current orbital elements from the NORAD two-line element sets are carried on the Celestial BBS, (*************, and are updated daily (when possible).  Documentation and tracking software are also available on this system.  As a service to the satellite user community, the most current elements for the current shuttle mission are provided below.  The Celestial BBS may be accessed 24 hours/day at 300, 1200, 2400, 4800, or 9600 bps using 8 data bits, 1 stop bit, no parity.  Element sets (also updated daily), shuttle elements, and some documentation and software are also available via anonymous ftp from archive.afit.af.mil (***********) in the directory pub/space.  STS 55      1 22640U 93 27  A 93117.91666666  .00044808  00000-0  13489-3 0    63 2 22640  28.4614 259.3429 0005169 259.6342  61.8074 15.90673799   201 --  Dr TS Kelso                           Assistant Professor of <NAME_EMAIL>                    Air Force Institute of Technology ");

		Map<String, String> testKeyValueMap = SimhashUtils.getInstance().getKeyValueMap("C:\\dev\\eclipse-workspace\\NearDuplicateDetection\\Dataset\\small");
		Simhash simhash = new Simhash(testKeyValueMap);
		System.out.println(simhash.exactJaccard("space-771.txt", "space-753.txt"));
		System.out.println(simhash.exactCosine("space-771.txt", "space-753.txt"));
		System.out.println(simhash.approximateCosine("space-771.txt", "space-753.txt"));
		//simhash.fingerprint();       
	}
}

class Word {
	private int word, frequency;

	public Word(int word, int frequency) {
		this.word = word;
		this.frequency = frequency;
	}

	public int getWord() {
		return word;
	}

	public int getfrequency() {
		return frequency;
	}
}
