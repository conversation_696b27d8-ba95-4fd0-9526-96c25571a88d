package com.actonia.near.duplication.detection;

import java.io.FileNotFoundException;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.actonia.utils.SimhashUtils;

public class Corelation {
	//private String folder;
	private double error;
	private List<Double> exactCosine = new ArrayList<Double>();
	private List<Double> approximateCosine = new ArrayList<Double>();
	private int count = 0;
	double time = 0;

	// map key = key string
	// map value = value string
	Map<String, String> keyValueMap = new HashMap<String, String>();

	public Corelation(Map<String, String> keyValueMap, double error) {
		this.keyValueMap = keyValueMap;
		this.error = error;
	}

	private void Accuracy() {
		long startTime = 0, stopTime = 0;
		startTime = System.nanoTime();

		Simhash simhash = new Simhash(keyValueMap);
		for (int i = 0; i < simhash.keys.length; i++) {
			for (int j = i + 1; j < simhash.keys.length; j++) {
				double exact = simhash.exactCosine(simhash.keys[i], simhash.keys[j]);
				//				if (exact < 0.85)
				//					continue;
				double approx = simhash.approximateCosine(simhash.keys[i], simhash.keys[j]);
				exactCosine.add(exact);
				approximateCosine.add(approx);
				System.out.println(simhash.keys[i] + " " + simhash.keys[j] + " " + exact + " " + approx);
				if (approx < 0.8 && exact > 0.90)
					count++;
				//				if (Math.abs(exact - approx) > error)
				//					count++;
			}
		}
		stopTime = System.nanoTime();
		time = (double) (stopTime - startTime) / 1000000000.0;
		System.out.println("Time: " + time + " sec.");
	}

	public static void main(String[] args) throws Exception {
		//Corelation corelation = new Corelation("/Users/<USER>/IOWA STATE UNIVERSITY/Fall 17/CPR E 528/Project/Dataset/space", 0.1);
		Map<String, String> testKeyValueMap = SimhashUtils.getInstance().getKeyValueMap("C:\\dev\\eclipse-workspace\\NearDuplicateDetection\\Dataset\\small");
		Corelation corelation = new Corelation(testKeyValueMap, 0.1);
		corelation.Accuracy();
		//System.out.println(corelation.exactCosine);
		//System.out.println(corelation.approximateCosine);
		System.out.println(corelation.count);

		PrintWriter printer;
		try {
			printer = new PrintWriter("corelation-F17PA2-85.csv", "UTF-8");
			for (int i = 0; i < corelation.exactCosine.size(); i++) {
				printer.println(corelation.exactCosine.get(i) + ", " + corelation.approximateCosine.get(i));
			}
			printer.close();
		} catch (FileNotFoundException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (UnsupportedEncodingException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

}
