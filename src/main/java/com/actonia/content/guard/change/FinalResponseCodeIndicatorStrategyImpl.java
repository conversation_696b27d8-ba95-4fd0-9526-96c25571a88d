package com.actonia.content.guard.change;

import com.actonia.entity.HtmlChange;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
import com.actonia.entity.UrlMetricsEntityV3;
import org.apache.commons.lang3.BooleanUtils;
public class FinalResponseCodeIndicatorStrategyImpl implements IndicatorStrategy {

	@Override
	public String convertPrevValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return convert(changeIndClickHouseEntity.getFinalResponseCodePrevious());
	}

	@Override
	public String convertCurrValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return convert(changeIndClickHouseEntity.getFinalResponseCodeCurrent());
	}

	@Override
	public boolean checkIndicatorChanged(HtmlClickHouseEntity htmlClickHouseEntity) {
		return BooleanUtils.isTrue(htmlClickHouseEntity.getFinalResponseCodeChgInd());
	}

	@Override
	public HtmlChange createHtmlChange(HtmlChange temp, UrlMetricsEntityV3 previous, HtmlClickHouseEntity current) {
		temp.setChgId(ChangeIndicatorEnum.FINAL_RESPONSE_CODE_CHG_IND.id);
		temp.setPrevValue(convert(previous.getFinal_response_code()));
		temp.setCurrValue(convert(current.getCrawlerResponse().getFinal_response_code()));
		return temp;
	}

	@Override
	public TargetUrlChangeIndClickHouseEntity convertToTargetUrlChangeIndClickHouseEntity(HtmlChange htmlChange) {
		TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = IndicatorStrategy.super.convertToTargetUrlChangeIndClickHouseEntity(htmlChange);
		targetUrlChangeIndClickHouseEntity.setChangeIndicator(ChangeIndicatorEnum.FINAL_RESPONSE_CODE_CHG_IND.getIndicator());
		final int currValue = Integer.parseInt(htmlChange.getCurrValue());
		if (currValue != -1) {
			targetUrlChangeIndClickHouseEntity.setFinalResponseCodeCurrent(currValue);
		}
		final int prevValue = Integer.parseInt(htmlChange.getPrevValue());
		if (prevValue != -1) {
			targetUrlChangeIndClickHouseEntity.setFinalResponseCodePrevious(prevValue);
		}
		return targetUrlChangeIndClickHouseEntity;
	}

}
