package com.actonia.content.guard.change;

import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
public abstract class CanonicalIndicatorStrategyImpl implements IndicatorStrategy {

	@Override
	public String convertPrevValue(TargetUrlChangeIndClickHouseEntity entity) {
		return entity.getCanonicalPrevious();
	}

	@Override
	public String convertCurrValue(TargetUrlChangeIndClickHouseEntity entity) {
		return entity.getCanonicalCurrent();
	}

}
