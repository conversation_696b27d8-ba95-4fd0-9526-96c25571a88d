package com.actonia.content.guard.change;

import com.actonia.IConstants;
import com.actonia.entity.HtmlChange;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
import com.actonia.entity.UrlMetricsEntityV3;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

public class InsecureResourcesIndicatorStrategyImpl implements IndicatorStrategy {
	@Override
	public String rawPrevValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return rawValue(changeIndClickHouseEntity.getInsecureResourcesPrevious());
	}

	@Override
	public String rawCurrValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return rawValue(changeIndClickHouseEntity.getInsecureResourcesCurrent());
	}

	@Override
	public String convertPrevValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return convert(changeIndClickHouseEntity.getInsecureResourcesPrevious());
	}

	@Override
	public String convertCurrValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return convert(changeIndClickHouseEntity.getInsecureResourcesCurrent());
	}

	@Override
	public boolean checkIndicatorChanged(HtmlClickHouseEntity htmlClickHouseEntity) {
		return BooleanUtils.isTrue(htmlClickHouseEntity.getInsecureResourcesChgInd());
	}

	@Override
	public HtmlChange createHtmlChange(HtmlChange temp, UrlMetricsEntityV3 previous, HtmlClickHouseEntity current) {
		temp.setChgId(ChangeIndicatorEnum.INSECURE_RESOURCES_CHG_IND.id);
		temp.setPrevValue(temp.getPreviousChangeTrackingHashCdJsonMap().get(IConstants.INSECURE_RESOURCES));
		temp.setCurrValue(convert(current.getCrawlerResponse().getInsecure_resources()));
		return temp;
	}

	@Override
	public TargetUrlChangeIndClickHouseEntity convertToTargetUrlChangeIndClickHouseEntity(HtmlChange htmlChange) {
		TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = IndicatorStrategy.super.convertToTargetUrlChangeIndClickHouseEntity(htmlChange);
		targetUrlChangeIndClickHouseEntity.setChangeIndicator(ChangeIndicatorEnum.INSECURE_RESOURCES_CHG_IND.getIndicator());
		final String currValue = htmlChange.getCurrValue();
		if (StringUtils.isNotBlank(currValue)) {
			targetUrlChangeIndClickHouseEntity.setInsecureResourcesCurrent(gson.fromJson(currValue, String[].class));
		}
		final String prevValue = htmlChange.getPrevValue();
		if (StringUtils.isNotBlank(prevValue)) {
			targetUrlChangeIndClickHouseEntity.setInsecureResourcesPrevious(gson.fromJson(currValue, String[].class));
		}
		return targetUrlChangeIndClickHouseEntity;
	}

}
