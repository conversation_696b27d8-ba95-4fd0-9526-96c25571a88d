package com.actonia.content.guard.change;

import com.actonia.entity.HtmlChange;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
import com.actonia.entity.UrlMetricsEntityV3;
import org.apache.commons.lang3.BooleanUtils;
public class NoodpIndicatorStrategyImpl implements IndicatorStrategy {

	@Override
	public String convertPrevValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return convert(changeIndClickHouseEntity.getNoodpPrevious());
	}

	@Override
	public String convertCurrValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return convert(changeIndClickHouseEntity.getNoodpCurrent());
	}

	@Override
	public boolean checkIndicatorChanged(HtmlClickHouseEntity htmlClickHouseEntity) {
		return BooleanUtils.isTrue(htmlClickHouseEntity.getNoodpChgInd());
	}

	@Override
	public HtmlChange createHtmlChange(HtmlChange temp, UrlMetricsEntityV3 previous, HtmlClickHouseEntity current) {
		temp.setChgId(ChangeIndicatorEnum.NOODP_CHG_IND.id);
		temp.setPrevValue(convert(previous.getNoodp()));
		temp.setCurrValue(convert(current.getCrawlerResponse().getNoodp()));
		return temp;
	}

	@Override
	public TargetUrlChangeIndClickHouseEntity convertToTargetUrlChangeIndClickHouseEntity(HtmlChange htmlChange) {
		TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = IndicatorStrategy.super.convertToTargetUrlChangeIndClickHouseEntity(htmlChange);
		targetUrlChangeIndClickHouseEntity.setChangeIndicator(ChangeIndicatorEnum.NOODP_CHG_IND.getIndicator());
		targetUrlChangeIndClickHouseEntity.setNoodpCurrent(convertBoolean(htmlChange.getCurrValue()));
		targetUrlChangeIndClickHouseEntity.setNoodpPrevious(convertBoolean(htmlChange.getPrevValue()));
		return targetUrlChangeIndClickHouseEntity;
	}

}
