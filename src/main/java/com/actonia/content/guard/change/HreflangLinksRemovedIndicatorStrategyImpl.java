package com.actonia.content.guard.change;

import com.actonia.IConstants;
import com.actonia.entity.HtmlChange;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
import com.actonia.entity.UrlMetricsEntityV3;
import com.actonia.value.object.HreflangLinks;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

public class HreflangLinksRemovedIndicatorStrategyImpl extends HreflangLinksIndicatorStrategyImpl {
	@Override
	public boolean checkIndicatorChanged(HtmlClickHouseEntity htmlClickHouseEntity) {
		return BooleanUtils.isTrue(htmlClickHouseEntity.getHreflangLinksRemovedInd());
	}

	@Override
	public HtmlChange createHtmlChange(HtmlChange temp, UrlMetricsEntityV3 previous, HtmlClickHouseEntity current) {
		temp.setChgId(ChangeIndicatorEnum.HREFLANG_LINKS_REMOVED_IND.id);
		temp.setPrevValue(temp.getPreviousChangeTrackingHashCdJsonMap().get(IConstants.HREFLANG_LINKS));
		temp.setCurrValue(convert(current.getCrawlerResponse().getHreflang_links()));
		return temp;
	}

	@Override
	public TargetUrlChangeIndClickHouseEntity convertToTargetUrlChangeIndClickHouseEntity(HtmlChange htmlChange) {
		TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = super.convertToTargetUrlChangeIndClickHouseEntity(htmlChange);
		targetUrlChangeIndClickHouseEntity.setChangeIndicator(ChangeIndicatorEnum.HREFLANG_LINKS_REMOVED_IND.getIndicator());
		final String currValue = htmlChange.getCurrValue();
		if (StringUtils.isNotBlank(currValue)) {
			targetUrlChangeIndClickHouseEntity.setHreflangLinksCurrent(gson.fromJson(currValue, HreflangLinks[].class));
		}
		final String prevValue = htmlChange.getPrevValue();
		if (StringUtils.isNotBlank(prevValue)) {
			targetUrlChangeIndClickHouseEntity.setHreflangLinksPrevious(gson.fromJson(currValue, HreflangLinks[].class));
		}
		return targetUrlChangeIndClickHouseEntity;
	}

}
