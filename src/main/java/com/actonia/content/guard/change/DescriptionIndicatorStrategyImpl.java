package com.actonia.content.guard.change;

import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;

import org.apache.commons.lang3.BooleanUtils;
public abstract class DescriptionIndicatorStrategyImpl implements IndicatorStrategy {

	@Override
	public String convertPrevValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return changeIndClickHouseEntity.getDescriptionPrevious();
	}

	@Override
	public String convertCurrValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return changeIndClickHouseEntity.getDescriptionCurrent();
	}
}
