package com.actonia.content.guard.change;

import com.actonia.entity.HtmlChange;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
import com.actonia.entity.UrlMetricsEntityV3;
import org.apache.commons.lang3.BooleanUtils;
public class TitleLengthIndicatorStrategyImpl implements IndicatorStrategy {

	@Override
	public String convertPrevValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return convert(changeIndClickHouseEntity.getTitleLengthPrevious());
	}

	@Override
	public String convertCurrValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return convert(changeIndClickHouseEntity.getTitleLengthCurrent());
	}

	@Override
	public boolean checkIndicatorChanged(HtmlClickHouseEntity htmlClickHouseEntity) {
		return BooleanUtils.isTrue(htmlClickHouseEntity.getTitleLengthChgInd());
	}

	@Override
	public HtmlChange createHtmlChange(HtmlChange temp, UrlMetricsEntityV3 previous, HtmlClickHouseEntity current) {
		temp.setChgId(ChangeIndicatorEnum.TITLE_LENGTH_CHG_IND.id);
		temp.setPrevValue(convert(previous.getTitle_length()));
		temp.setCurrValue(convert(current.getCrawlerResponse().getTitle_length()));
		return temp;
	}

	@Override
	public TargetUrlChangeIndClickHouseEntity convertToTargetUrlChangeIndClickHouseEntity(HtmlChange htmlChange) {
		TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = IndicatorStrategy.super.convertToTargetUrlChangeIndClickHouseEntity(htmlChange);
		targetUrlChangeIndClickHouseEntity.setChangeIndicator(ChangeIndicatorEnum.TITLE_LENGTH_CHG_IND.getIndicator());
		final int currValue = Integer.parseInt(htmlChange.getCurrValue());
		if (currValue != -1) {
			targetUrlChangeIndClickHouseEntity.setTitleLengthCurrent(currValue);
		}
		final int prevValue = Integer.parseInt(htmlChange.getPrevValue());
		if (prevValue != -1) {
			targetUrlChangeIndClickHouseEntity.setTitleLengthPrevious(prevValue);
		}
		return targetUrlChangeIndClickHouseEntity;
	}

}
