package com.actonia.content.guard.change;

import com.actonia.entity.HtmlChange;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
import com.actonia.entity.UrlMetricsEntityV3;
import org.apache.commons.lang3.BooleanUtils;
public class BlockedByRobotsIndicatorStrategyImpl implements IndicatorStrategy {

	@Override
	public String convertPrevValue(TargetUrlChangeIndClickHouseEntity entity) {
		return entity.getBlockedByRobotsPrevious();
	}

	@Override
	public String convertCurrValue(TargetUrlChangeIndClickHouseEntity entity) {
		return entity.getBlockedByRobotsCurrent();
	}

	@Override
	public boolean checkIndicatorChanged(HtmlClickHouseEntity htmlClickHouseEntity) {
		return BooleanUtils.isTrue(htmlClickHouseEntity.getBlockedByRobotsChgInd());
	}

	@Override
	public HtmlChange createHtmlChange(HtmlChange temp, UrlMetricsEntityV3 previous, HtmlClickHouseEntity current) {
		temp.setChgId(ChangeIndicatorEnum.BLOCKED_BY_ROBOTS_CHG_IND.id);
		temp.setPrevValue(previous.getBlocked_by_robots());
		temp.setCurrValue(current.getCrawlerResponse().getBlocked_by_robots());
		return temp;
	}

	@Override
	public TargetUrlChangeIndClickHouseEntity convertToTargetUrlChangeIndClickHouseEntity(HtmlChange htmlChange) {
		TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = IndicatorStrategy.super.convertToTargetUrlChangeIndClickHouseEntity(htmlChange);
		targetUrlChangeIndClickHouseEntity.setChangeIndicator(ChangeIndicatorEnum.BLOCKED_BY_ROBOTS_CHG_IND.getIndicator());
		targetUrlChangeIndClickHouseEntity.setBlockedByRobotsCurrent(htmlChange.getCurrValue());
		targetUrlChangeIndClickHouseEntity.setBlockedByRobotsPrevious(htmlChange.getPrevValue());
		return targetUrlChangeIndClickHouseEntity;
	}

}
