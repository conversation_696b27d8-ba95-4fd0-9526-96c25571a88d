package com.actonia.content.guard.change;

import com.actonia.entity.HtmlChange;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
import com.actonia.entity.UrlMetricsEntityV3;
import org.apache.commons.lang3.BooleanUtils;

public class RobotsContentsChgIndicatorStrategyImpl extends RobotsContentsIndicatorStrategyImpl {
	@Override
	public boolean checkIndicatorChanged(HtmlClickHouseEntity htmlClickHouseEntity) {
		return BooleanUtils.isTrue(htmlClickHouseEntity.getRobotsContentsChgInd());
	}

	@Override
	public HtmlChange createHtmlChange(HtmlChange temp, UrlMetricsEntityV3 previous, HtmlClickHouseEntity current) {
		temp.setChgId(ChangeIndicatorEnum.ROBOTS_CONTENTS_CHG_IND.id);
		temp.setPrevValue(previous.getRobots_contents());
		temp.setCurrValue(current.getCrawlerResponse().getRobots_contents());
		return temp;
	}

	@Override
	public TargetUrlChangeIndClickHouseEntity convertToTargetUrlChangeIndClickHouseEntity(HtmlChange htmlChange) {
		TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = super.convertToTargetUrlChangeIndClickHouseEntity(htmlChange);
		targetUrlChangeIndClickHouseEntity.setChangeIndicator(ChangeIndicatorEnum.ROBOTS_CONTENTS_CHG_IND.getIndicator());
		targetUrlChangeIndClickHouseEntity.setRobotsContentsCurrent(htmlChange.getCurrValue());
		targetUrlChangeIndClickHouseEntity.setRobotsContentsPrevious(htmlChange.getPrevValue());
		return targetUrlChangeIndClickHouseEntity;
	}

}
