package com.actonia.content.guard.change;

import com.actonia.IConstants;
import com.actonia.entity.HtmlChange;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
import com.actonia.entity.UrlMetricsEntityV3;
import org.apache.commons.lang3.BooleanUtils;

public class Redirect301DetectedIndicatorStrategyImpl extends ResponseCodeIndicatorStrategyImpl {
	@Override
	public boolean checkIndicatorChanged(HtmlClickHouseEntity htmlClickHouseEntity) {
		return BooleanUtils.isTrue(htmlClickHouseEntity.getRedirect301DetectedInd());
	}

	@Override
	public HtmlChange createHtmlChange(HtmlChange temp, UrlMetricsEntityV3 previous, HtmlClickHouseEntity current) {
		temp.setChgId(ChangeIndicatorEnum.REDIRECT_301_DETECTED_IND.id);
		temp.setPrevValue(previous.getResponse_code() + IConstants.MULTI_STRING_SEPARATOR + previous.getRedirectFinalUrl());
		temp.setCurrValue(current.getCrawlerResponse().getResponse_code() + IConstants.MULTI_STRING_SEPARATOR + current.getCrawlerResponse().getRedirect_final_url());
		return temp;
	}

	@Override
	public TargetUrlChangeIndClickHouseEntity convertToTargetUrlChangeIndClickHouseEntity(HtmlChange htmlChange) {
		TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = super.convertToTargetUrlChangeIndClickHouseEntity(htmlChange);
		targetUrlChangeIndClickHouseEntity.setChangeIndicator(ChangeIndicatorEnum.REDIRECT_301_DETECTED_IND.getIndicator());
		String[] currValue = htmlChange.getCurrValue().split(IConstants.MULTI_STRING_SEPARATOR);
		String[] preValue = htmlChange.getPrevValue().split(IConstants.MULTI_STRING_SEPARATOR);
		targetUrlChangeIndClickHouseEntity.setResponseCodeCurrent(currValue[0]);
		targetUrlChangeIndClickHouseEntity.setResponseCodePrevious(preValue[0]);
		if (preValue.length > 1) {
			targetUrlChangeIndClickHouseEntity.setRedirectFinalUrlCurrent(currValue[1]);
		}
		if (currValue.length > 1) {
			targetUrlChangeIndClickHouseEntity.setRedirectFinalUrlPrevious(preValue[1]);
		}
		return targetUrlChangeIndClickHouseEntity;
	}

}
