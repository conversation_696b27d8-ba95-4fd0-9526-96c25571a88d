package com.actonia.content.guard.change;

import com.actonia.entity.HtmlChange;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
import com.actonia.entity.UrlMetricsEntityV3;
import org.apache.commons.lang3.StringUtils;

public class PageAnalysisResultsChgIndJsonIndicatorStrategyImpl implements IndicatorStrategy {

	@Override
	public String convertPrevValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return changeIndClickHouseEntity.getPageAnalysisResultsChgIndJson();
	}

	@Override
	public String convertCurrValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return changeIndClickHouseEntity.getPageAnalysisResultsChgIndJson();
	}

	@Override
	public boolean checkIndicatorChanged(HtmlClickHouseEntity htmlClickHouseEntity) {
		return StringUtils.isNotBlank(htmlClickHouseEntity.getPageAnalysisResultsChgIndJson());
	}

	@Override
	public HtmlChange createHtmlChange(HtmlChange temp, UrlMetricsEntityV3 previous, HtmlClickHouseEntity current) {
		temp.setChgId(ChangeIndicatorEnum.PAGE_ANALYSIS_RESULTS_CHG_IND_JSON.id);
		temp.setCurrValue(current.getPageAnalysisResultsChgIndJson());
		return temp;
	}

	@Override
	public TargetUrlChangeIndClickHouseEntity convertToTargetUrlChangeIndClickHouseEntity(HtmlChange htmlChange) {
		TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = IndicatorStrategy.super.convertToTargetUrlChangeIndClickHouseEntity(htmlChange);
		targetUrlChangeIndClickHouseEntity.setChangeIndicator(ChangeIndicatorEnum.PAGE_ANALYSIS_RESULTS_CHG_IND_JSON.getIndicator());
		targetUrlChangeIndClickHouseEntity.setPageAnalysisResultsChgIndJson(htmlChange.getCurrValue());
		return targetUrlChangeIndClickHouseEntity;
	}

}
