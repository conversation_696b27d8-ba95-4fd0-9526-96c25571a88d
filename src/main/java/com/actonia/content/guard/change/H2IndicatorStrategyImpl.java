package com.actonia.content.guard.change;

import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;

import org.apache.commons.lang3.BooleanUtils;
public abstract class H2IndicatorStrategyImpl implements IndicatorStrategy {
	@Override
	public String rawPrevValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return rawValue(changeIndClickHouseEntity.getH2Previous());
	}

	@Override
	public String rawCurrValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return rawValue(changeIndClickHouseEntity.getH2Current());
	}

	@Override
	public String convertPrevValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		final String[] h2Previous = changeIndClickHouseEntity.getH2Previous();
		if (h2Previous == null || h2Previous.length == 0) return null;
		return gson.toJson(h2Previous);
	}

	@Override
	public String convertCurrValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		final String[] h2Current = changeIndClickHouseEntity.getH2Current();
		if (h2Current == null || h2Current.length == 0) return null;
		return gson.toJson(h2Current);
	}
}
