package com.actonia.content.guard.change;

import com.actonia.IConstants;
import com.actonia.entity.HtmlChange;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
import com.actonia.entity.UrlMetricsEntityV3;
import com.actonia.value.object.CustomData;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

public class CustomDataChgIndicatorStrategyImpl extends CustomDataIndicatorStrategyImpl {
	@Override
	public boolean checkIndicatorChanged(HtmlClickHouseEntity htmlClickHouseEntity) {
		return BooleanUtils.isTrue(htmlClickHouseEntity.getCustomDataChgInd());
	}

	@Override
	public HtmlChange createHtmlChange(HtmlChange temp, UrlMetricsEntityV3 previous, HtmlClickHouseEntity current) {
		temp.setChgId(ChangeIndicatorEnum.CUSTOM_DATA_CHG_IND.id);
		final String prevValue = temp.getPreviousChangeTrackingHashCdJsonMap().get(IConstants.CUSTOM_DATA);
		temp.setPrevValue(prevValue);
		temp.setCurrValue(convert(current.getCrawlerResponse().getCustom_data()));
		return temp;
	}

	@Override
	public TargetUrlChangeIndClickHouseEntity convertToTargetUrlChangeIndClickHouseEntity(HtmlChange htmlChange) {
		TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = super.convertToTargetUrlChangeIndClickHouseEntity(htmlChange);
		targetUrlChangeIndClickHouseEntity.setChangeIndicator(ChangeIndicatorEnum.CUSTOM_DATA_CHG_IND.getIndicator());
		final String currValue = htmlChange.getCurrValue();
		if (StringUtils.isNotBlank(currValue)) {
			targetUrlChangeIndClickHouseEntity.setCustomDataCurrent(gson.fromJson(currValue, CustomData[].class));
		}
		final String prevValue = htmlChange.getPrevValue();
		if (StringUtils.isNotBlank(prevValue)) {
			targetUrlChangeIndClickHouseEntity.setCustomDataPrevious(gson.fromJson(prevValue, CustomData[].class));
		}
		return targetUrlChangeIndClickHouseEntity;
	}

}
