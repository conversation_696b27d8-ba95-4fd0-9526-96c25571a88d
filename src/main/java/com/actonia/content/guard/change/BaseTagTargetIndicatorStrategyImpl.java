package com.actonia.content.guard.change;

import com.actonia.entity.HtmlChange;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
import com.actonia.entity.UrlMetricsEntityV3;
import org.apache.commons.lang3.BooleanUtils;
public class BaseTagTargetIndicatorStrategyImpl implements IndicatorStrategy {

	@Override
	public String convertPrevValue(TargetUrlChangeIndClickHouseEntity entity) {
		return entity.getBaseTagTargetPrevious();
	}

	@Override
	public String convertCurrValue(TargetUrlChangeIndClickHouseEntity entity) {
		return entity.getBaseTagTargetCurrent();
	}

	@Override
	public boolean checkIndicatorChanged(HtmlClickHouseEntity htmlClickHouseEntity) {
		return BooleanUtils.isTrue(htmlClickHouseEntity.getBaseTagTargetChgInd());
	}

	@Override
	public HtmlChange createHtmlChange(HtmlChange temp, UrlMetricsEntityV3 previous, HtmlClickHouseEntity current) {
		temp.setChgId(ChangeIndicatorEnum.BASE_TAG_TARGET_CHG_IND.id);
		temp.setPrevValue(previous.getBase_tag_target());
		temp.setCurrValue(current.getCrawlerResponse().getBase_tag_target());
		return temp;
	}

	@Override
	public TargetUrlChangeIndClickHouseEntity convertToTargetUrlChangeIndClickHouseEntity(HtmlChange htmlChange) {
		TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = IndicatorStrategy.super.convertToTargetUrlChangeIndClickHouseEntity(htmlChange);
		targetUrlChangeIndClickHouseEntity.setChangeIndicator(ChangeIndicatorEnum.BASE_TAG_TARGET_CHG_IND.getIndicator());
		targetUrlChangeIndClickHouseEntity.setBaseTagTargetCurrent(htmlChange.getCurrValue());
		targetUrlChangeIndClickHouseEntity.setBaseTagTargetPrevious(htmlChange.getPrevValue());
		return targetUrlChangeIndClickHouseEntity;
	}

}
