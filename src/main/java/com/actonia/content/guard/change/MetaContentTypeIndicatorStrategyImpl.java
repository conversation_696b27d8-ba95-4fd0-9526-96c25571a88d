package com.actonia.content.guard.change;

import com.actonia.entity.HtmlChange;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
import com.actonia.entity.UrlMetricsEntityV3;
import org.apache.commons.lang3.BooleanUtils;
public class MetaContentTypeIndicatorStrategyImpl implements IndicatorStrategy {

	@Override
	public String convertPrevValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return changeIndClickHouseEntity.getMetaContentTypePrevious();
	}

	@Override
	public String convertCurrValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return changeIndClickHouseEntity.getMetaContentTypeCurrent();
	}

	@Override
	public boolean checkIndicatorChanged(HtmlClickHouseEntity htmlClickHouseEntity) {
		return BooleanUtils.isTrue(htmlClickHouseEntity.getMetaContentTypeChgInd());
	}

	@Override
	public HtmlChange createHtmlChange(HtmlChange temp, UrlMetricsEntityV3 previous, HtmlClickHouseEntity current) {
		temp.setChgId(ChangeIndicatorEnum.META_CONTENT_TYPE_CHG_IND.id);
		temp.setPrevValue(previous.getMeta_content_type());
		temp.setCurrValue(current.getCrawlerResponse().getMeta_content_type());
		return temp;
	}

	@Override
	public TargetUrlChangeIndClickHouseEntity convertToTargetUrlChangeIndClickHouseEntity(HtmlChange htmlChange) {
		TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = IndicatorStrategy.super.convertToTargetUrlChangeIndClickHouseEntity(htmlChange);
		targetUrlChangeIndClickHouseEntity.setChangeIndicator(ChangeIndicatorEnum.META_CONTENT_TYPE_CHG_IND.getIndicator());
		targetUrlChangeIndClickHouseEntity.setMetaContentTypeCurrent(htmlChange.getCurrValue());
		targetUrlChangeIndClickHouseEntity.setMetaContentTypePrevious(htmlChange.getPrevValue());
		return targetUrlChangeIndClickHouseEntity;
	}

}
