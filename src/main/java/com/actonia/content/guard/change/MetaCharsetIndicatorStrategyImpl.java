package com.actonia.content.guard.change;

import com.actonia.entity.HtmlChange;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
import com.actonia.entity.UrlMetricsEntityV3;
import org.apache.commons.lang3.BooleanUtils;

public class MetaCharsetIndicatorStrategyImpl implements IndicatorStrategy {
	@Override
	public String convertPrevValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return changeIndClickHouseEntity.getMetaCharsetPrevious();
	}

	@Override
	public String convertCurrValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return changeIndClickHouseEntity.getMetaCharsetCurrent();
	}

	@Override
	public boolean checkIndicatorChanged(HtmlClickHouseEntity htmlClickHouseEntity) {
		return BooleanUtils.isTrue(htmlClickHouseEntity.getMetaCharsetChgInd());
	}

	@Override
	public HtmlChange createHtmlChange(HtmlChange temp, UrlMetricsEntityV3 previous, HtmlClickHouseEntity current) {
		temp.setChgId(ChangeIndicatorEnum.META_CHARSET_CHG_IND.id);
		temp.setPrevValue(previous.getMeta_charset());
		temp.setCurrValue(current.getCrawlerResponse().getMeta_charset());
		return temp;
	}

	@Override
	public TargetUrlChangeIndClickHouseEntity convertToTargetUrlChangeIndClickHouseEntity(HtmlChange htmlChange) {
		TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = IndicatorStrategy.super.convertToTargetUrlChangeIndClickHouseEntity(htmlChange);
		targetUrlChangeIndClickHouseEntity.setChangeIndicator(ChangeIndicatorEnum.META_CHARSET_CHG_IND.getIndicator());
		targetUrlChangeIndClickHouseEntity.setMetaCharsetCurrent(htmlChange.getCurrValue());
		targetUrlChangeIndClickHouseEntity.setMetaCharsetPrevious(htmlChange.getPrevValue());
		return targetUrlChangeIndClickHouseEntity;
	}

}
