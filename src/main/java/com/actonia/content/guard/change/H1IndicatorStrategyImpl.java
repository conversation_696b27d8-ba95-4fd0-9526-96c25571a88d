package com.actonia.content.guard.change;

import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;

import org.apache.commons.lang3.BooleanUtils;
public abstract class H1IndicatorStrategyImpl implements IndicatorStrategy {
	@Override
	public String rawPrevValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return rawValue(changeIndClickHouseEntity.getH1Previous());
	}

	@Override
	public String rawCurrValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return rawValue(changeIndClickHouseEntity.getH1Current());
	}

	@Override
	public String convertPrevValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		final String[] h1Previous = changeIndClickHouseEntity.getH1Previous();
		if (h1Previous == null || h1Previous.length == 0) return null;
		return gson.toJson(h1Previous);
	}

	@Override
	public String convertCurrValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		final String[] h1Current = changeIndClickHouseEntity.getH1Current();
		if (h1Current == null || h1Current.length == 0) return null;
		return gson.toJson(h1Current);
	}
}
