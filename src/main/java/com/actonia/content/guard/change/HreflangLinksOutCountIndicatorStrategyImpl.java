package com.actonia.content.guard.change;

import com.actonia.entity.HtmlChange;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
import com.actonia.entity.UrlMetricsEntityV3;
import org.apache.commons.lang3.BooleanUtils;

public class HreflangLinksOutCountIndicatorStrategyImpl implements IndicatorStrategy {

	@Override
	public String convertPrevValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return convert(changeIndClickHouseEntity.getHreflangLinksOutCountPrevious());
	}

	@Override
	public String convertCurrValue(TargetUrlChangeIndClickHouseEntity changeIndClickHouseEntity) {
		return convert(changeIndClickHouseEntity.getHreflangLinksOutCountCurrent());
	}

	@Override
	public boolean checkIndicatorChanged(HtmlClickHouseEntity htmlClickHouseEntity) {
		return BooleanUtils.isTrue(htmlClickHouseEntity.getHreflangLinksOutCountChgInd());
	}

	@Override
	public HtmlChange createHtmlChange(HtmlChange temp, UrlMetricsEntityV3 previous, HtmlClickHouseEntity current) {
		temp.setChgId(ChangeIndicatorEnum.HREFLANG_LINKS_OUT_COUNT_CHG_IND.id);
		temp.setPrevValue(convert(previous.getHreflang_links_out_count()));
		temp.setCurrValue(convert(current.getCrawlerResponse().getHreflang_links_out_count()));
		return temp;
	}

	@Override
	public TargetUrlChangeIndClickHouseEntity convertToTargetUrlChangeIndClickHouseEntity(HtmlChange htmlChange) {
		TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = IndicatorStrategy.super.convertToTargetUrlChangeIndClickHouseEntity(htmlChange);
		targetUrlChangeIndClickHouseEntity.setChangeIndicator(ChangeIndicatorEnum.HREFLANG_LINKS_OUT_COUNT_CHG_IND.getIndicator());
		final int currValue = Integer.parseInt(htmlChange.getCurrValue());
		if (currValue != -1) {
			targetUrlChangeIndClickHouseEntity.setHreflangLinksOutCountCurrent(currValue);
		}
		final int prevValue = Integer.parseInt(htmlChange.getPrevValue());
		if (prevValue != -1) {
			targetUrlChangeIndClickHouseEntity.setHreflangLinksOutCountCurrent(prevValue);
		}
		return targetUrlChangeIndClickHouseEntity;
	}

}
