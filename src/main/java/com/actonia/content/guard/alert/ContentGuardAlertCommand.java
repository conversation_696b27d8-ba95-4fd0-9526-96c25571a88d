package com.actonia.content.guard.alert;

import com.actonia.IConstants;
import com.actonia.concurrency.BaseThreadCommand;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.dao.ContentGuardAlertDAO;
import com.actonia.dao.ContentGuardGroupDAO;
import com.actonia.dao.ZapierWebhookDAO;
import com.actonia.entity.AgencyInfoEntity;
import com.actonia.entity.ContentGuardAlertEntity;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.entity.ZapierWebhookEntity;
import com.actonia.service.AccessTokenService;
import com.actonia.service.AgencyInfoService;
import com.actonia.service.MainWebServiceClientService;
import com.actonia.utils.ContentGuardUtils;
import com.actonia.utils.EmailSenderComponent;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.*;
import com.google.gson.Gson;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.text.MessageFormat;
import java.text.ParseException;
import java.util.*;

public class ContentGuardAlertCommand extends BaseThreadCommand {
	private String ip;
	private int domainId;
	private String domainName;
	private int crawlFrequencyType;
	private ContentGuardAlertDAO contentGuardAlertDAO;
	private String crawlDateOverride;
	private Integer crawlHourOverride;
	private MainWebServiceClientService mainWebServiceClientService;
	private AgencyInfoService agencyInfoService;
	private EmailSenderComponent emailSenderComponent;
	private ContentGuardGroupDAO contentGuardGroupDAO;
	private boolean isDebug = false;
	private ZapierWebhookDAO zapierWebhookDAO;
	private static final String DAILY_GROUP_WEB_APP_LINK_TEMPLATE = "<a href=\"https://app.seoclarity.net/contentguard.do?groupId={0}&groupName={1}&oid={2}&crawl_date_time={3}\">";
	private static final String HOURLY_GROUP_WEB_APP_LINK_TEMPLATE = "<a href=\"https://app.seoclarity.net/contentguard.do?groupId={0}&groupName={1}&oid={2}&crawl_date_time={3}-{4}\">";

	public ContentGuardAlertCommand(String ip, OwnDomainEntity ownDomainEntity, int crawlFrequencyType, String crawlDateOverride, Integer crawlHourOverride,
			boolean isDebug) {
		super();
		this.ip = ip;
		this.domainId = ownDomainEntity.getId();
		this.domainName = ownDomainEntity.getDomain();
		this.crawlFrequencyType = crawlFrequencyType;
		this.crawlDateOverride = crawlDateOverride;
		this.crawlHourOverride = crawlHourOverride;
		this.contentGuardAlertDAO = SpringBeanFactory.getBean("contentGuardAlertDAO");
		this.mainWebServiceClientService = SpringBeanFactory.getBean("mainWebServiceClientService");
		this.agencyInfoService = SpringBeanFactory.getBean("agencyInfoService");
		this.emailSenderComponent = SpringBeanFactory.getBean("emailSenderComponent");
		this.contentGuardGroupDAO = SpringBeanFactory.getBean("contentGuardGroupDAO");
		this.zapierWebhookDAO = SpringBeanFactory.getBean("zapierWebhookDAO");
		this.isDebug = isDebug;
	}

	@Override
	protected void execute() {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("execute() begins. ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",crawlFrequencyType="
				+ crawlFrequencyType + ",crawlDateOverride=" + crawlDateOverride + ",crawlHourOverride=" + crawlHourOverride + ",isDebug=" + isDebug);
		try {
			process();
		} catch (Exception e) {
			e.printStackTrace();

			StringWriter stringWriter = new StringWriter();
			e.printStackTrace(new PrintWriter(stringWriter));

			// send support alert
			String message = stringWriter.toString();
		} finally {
			CacheModleFactory.getInstance().setAliveIpAddress(ip);
		}
		FormatUtils.getInstance()
				.logMemoryUsage("execute() ends. ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",crawlFrequencyType=" + crawlFrequencyType
						+ ",crawlDateOverride=" + crawlDateOverride + ",crawlHourOverride=" + crawlHourOverride + ",isDebug=" + isDebug + " elapsed(s.)="
						+ (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	@Override
	protected void undo() throws Exception {
		// TODO Auto-generated method stub
	}

	private void process() throws Exception {

		Date todayDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
		Date yesterdayDate = DateUtils.addDays(todayDate, -1);
		String crawlDate = null;
		Calendar calendar = null;
		int currentHour = 0;
		Integer crawlHour = null;
		Long groupId = null;
		String groupName = null;
		ContentGuardResourceResponse contentGuardResourceResponse = null;
		List<ContentGuardIndicatorUrlChanges> contentGuardIndicatorUrlChangesList = null;
		String emails = null;

		String groupInformation1 = null;
		String groupInformation2 = null;
		String groupInformation3 = null;
		Integer totalUrls = null;
		List<ContentGuardAlertSummary> contentGuardAlertSummaryList = null;
		List<ZapierContentGuardAlert> zapierContentGuardAlertList = null;

		if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_DAILY) {
			if (StringUtils.isNotBlank(crawlDateOverride)) {
				crawlDate = crawlDateOverride;
			} else {
				crawlDate = DateFormatUtils.format(yesterdayDate, IConstants.DATE_FORMAT_YYYY_MM_DD);
			}
		} else if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_HOURLY) {
			if (crawlHourOverride != null && StringUtils.isNotBlank(crawlDateOverride)) {
				crawlHour = crawlHourOverride.intValue();
				crawlDate = crawlDateOverride;
			} else {
				calendar = GregorianCalendar.getInstance();
				currentHour = calendar.get(Calendar.HOUR_OF_DAY);
				if (currentHour == 0) {
					crawlHour = 23;
					crawlDate = DateFormatUtils.format(yesterdayDate, IConstants.DATE_FORMAT_YYYY_MM_DD);
				} else {
					crawlHour = currentHour - 1;
					crawlDate = DateFormatUtils.format(todayDate, IConstants.DATE_FORMAT_YYYY_MM_DD);
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("process() ip=" + ip + ",domainId=" + domainId + ",crawlDate=" + crawlDate + ",crawlHour=" + crawlHour);

		// retrieve groups that require alerts for the crawl frequency type
		List<ContentGuardAlertEntity> contentGuardAlertEntityList = contentGuardAlertDAO.getList(domainId, crawlFrequencyType);
		if (contentGuardAlertEntityList != null && contentGuardAlertEntityList.size() > 0) {
			for (ContentGuardAlertEntity contentGuardAlertEntity : contentGuardAlertEntityList) {
				FormatUtils.getInstance()
						.logMemoryUsage("process() ip=" + ip + ",domainId=" + domainId + ",contentGuardAlertEntity=" + contentGuardAlertEntity.toString());

				groupId = contentGuardAlertEntity.getGroupId();
				groupName = contentGuardAlertEntity.getGroupName();
				emails = contentGuardAlertEntity.getEmails();

				// retrieve domain change summary by domain ID, group ID, start crawl time stamp and end crawl time stamp.
				contentGuardResourceResponse = getContentGuardResourceResponse(crawlDate, crawlHour, groupId);
				if (contentGuardResourceResponse != null && BooleanUtils.isTrue(contentGuardResourceResponse.getSuccess())) {
					contentGuardIndicatorUrlChangesList = filterIndicatorUrlListByAlertIndicators(groupId, contentGuardResourceResponse, contentGuardAlertEntity);
					if (contentGuardIndicatorUrlChangesList != null && contentGuardIndicatorUrlChangesList.size() > 0) {

						groupInformation1 = null;
						groupInformation2 = null;
						groupInformation3 = null;
						totalUrls = contentGuardGroupDAO.getTotalUrls(domainId, groupId);

						groupInformation1 = getGroupInformation1(contentGuardAlertEntity.getIndicatorFlag(), groupName, groupId, crawlDate, crawlHour);

						if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_DAILY) {
							groupInformation2 = "The " + totalUrls + " URLs in this group are crawled daily";
							groupInformation3 = "The changes were found on " + crawlDate;
						} else if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_HOURLY) {
							groupInformation2 = "The " + totalUrls + " URLs in this group are crawled hourly";
							groupInformation3 = "The changes were found on " + crawlDate + " from " + crawlHour + ":00 to " + crawlHour + ":59 Central Time (US)";
						}

						contentGuardAlertSummaryList = getContentGuardAlertSummaryList(contentGuardIndicatorUrlChangesList);
						if (contentGuardAlertSummaryList != null && contentGuardAlertSummaryList.size() > 0) {
							sendEmailAlert(emails, groupName, groupInformation1, groupInformation2, groupInformation3, contentGuardAlertSummaryList);
						} else {
							throw new Exception("process() error--ip=" + ip + ",domainId=" + domainId + ",groupId=" + groupId + ",groupName=" + groupName
									+ ",contentGuardAlertSummaryList is empty.");
						}

						zapierContentGuardAlertList = getZapierContentGuardAlertList(groupId, groupName, contentGuardIndicatorUrlChangesList);
						if (zapierContentGuardAlertList != null && zapierContentGuardAlertList.size() > 0) {
							ContentGuardUtils.getInstance().sendZapierContentGuardAlert(ip, domainId, groupId, zapierContentGuardAlertList);
						}
					}
				} else {
					FormatUtils.getInstance().logMemoryUsage("process() ip=" + ip + ",domainId=" + domainId + ",contentGuardResourceResponse is empty.");
				}
			}
		} else {
			FormatUtils.getInstance().logMemoryUsage("process() ip=" + ip + ",domainId=" + domainId + ",contentGuardAlertEntityList is empty.");
		}
	}

	private String getGroupInformation1(int contentGuardAlertIndicatorFlag, String groupName, Long groupId, String crawlDateString, Integer crawlHour)
			throws ParseException {
		String groupInformation1 = null;

		FormatUtils.getInstance().logMemoryUsage(
				"getGroupInformation1() begins. ip=" + ip + ",domainId=" + domainId + ",contentGuardAlertIndicatorFlag=" + contentGuardAlertIndicatorFlag
						+ ",groupName=" + groupName + ",groupId=" + groupId + ",crawlDateString=" + crawlDateString + ",crawlHour=" + crawlHour);

		Date crawlDate = DateUtils.parseDate(crawlDateString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
		if (isDebug == true) {
			System.out.println("getGroupInformation1() begins. ip=" + ip + ",domainId=" + domainId + ",crawlDate=" + crawlDate);
		}
		String webAppLinkCrawlDate = DateFormatUtils.format(crawlDate, IConstants.DATE_FORMAT_MM_DD_YYYY);
		if (isDebug == true) {
			System.out.println("getGroupInformation1() begins. ip=" + ip + ",domainId=" + domainId + ",webAppLinkCrawlDate=" + webAppLinkCrawlDate);
		}

		// int crawlFrequencyType
		String webAppLink = null;
		if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_DAILY) {
			webAppLink = MessageFormat.format(DAILY_GROUP_WEB_APP_LINK_TEMPLATE, groupId, groupName, String.valueOf(domainId), webAppLinkCrawlDate);
		} else if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_HOURLY) {
			webAppLink = MessageFormat.format(HOURLY_GROUP_WEB_APP_LINK_TEMPLATE, groupId, groupName, String.valueOf(domainId), webAppLinkCrawlDate, crawlHour);
		}
		if (isDebug == true) {
			System.out.println("getGroupInformation1() begins. ip=" + ip + ",domainId=" + domainId + ",webAppLink=" + webAppLink);
		}

		if (contentGuardAlertIndicatorFlag == IConstants.INDICATOR_FLAG_ALL) {
			groupInformation1 = "The following alerts were generated for all the changes in the '" + webAppLink + groupName + "</a>' group";
		} else if (contentGuardAlertIndicatorFlag == IConstants.INDICATOR_FLAG_CRITICAL) {
			groupInformation1 = "The following alerts were generated for the critical changes in the '" + webAppLink + groupName + "</a>' group";
		} else if (contentGuardAlertIndicatorFlag == IConstants.INDICATOR_FLAG_CUSTOM) {
			groupInformation1 = "The following alerts were generated for the custom changes in the '" + webAppLink + groupName + "</a>' group";
		}

		FormatUtils.getInstance()
				.logMemoryUsage("getGroupInformation1() ends. ip=" + ip + ",domainId=" + domainId + ",contentGuardAlertIndicatorFlag=" + contentGuardAlertIndicatorFlag
						+ ",groupName=" + groupName + ",groupId=" + groupId + ",crawlDateString=" + crawlDateString + ",crawlHour=" + crawlHour + ",groupInformation1="
						+ groupInformation1);
		return groupInformation1;
	}

	private List<ZapierContentGuardAlert> getZapierContentGuardAlertList(Long groupId, String groupName,
			List<ContentGuardIndicatorUrlChanges> contentGuardIndicatorUrlChangesList) throws Exception {
		List<ZapierContentGuardAlert> zapierContentGuardAlertList = new ArrayList<ZapierContentGuardAlert>();
		ZapierContentGuardAlert zapierContentGuardAlert = null;
		String changeDescription = null;
		String changeSeverity = null;
		StringBuilder stringBuilder = null;
		long startingId = System.currentTimeMillis();

		boolean isZapierWebhookRequired = false;
		Long groupIdToBeTested = null;

		List<ZapierWebhookEntity> zapierWebhookEntityList = zapierWebhookDAO.getByDomainIdTriggerType(domainId, IConstants.ZAPIER_TRIGGER_TYPE_CONTENT_GUARD_ALERT);
		if (zapierWebhookEntityList != null && zapierWebhookEntityList.size() > 0) {
			nextZapierWebhookEntity: for (ZapierWebhookEntity zapierWebhookEntity : zapierWebhookEntityList) {
				// the 'sub_type' of the 'zapier_webhook' MySQL record is the group ID
				groupIdToBeTested = new Long(zapierWebhookEntity.getSubType());
				// when the content guard group requires Zapier trigger webhook
				if (groupId.compareTo(groupIdToBeTested) == 0) {
					isZapierWebhookRequired = true;
					continue nextZapierWebhookEntity;
				}
			}
		} else {
			FormatUtils.getInstance().logMemoryUsage("getZapierContentGuardAlertList() ip=" + ip + ",domainId=" + domainId + ",zapierWebhookEntityList is empty.");
		}

		if (isZapierWebhookRequired == false) {
			FormatUtils.getInstance().logMemoryUsage(
					"getZapierContentGuardAlertList() exiting...ip=" + ip + ",domainId=" + domainId + ",isZapierWebhookRequired=" + isZapierWebhookRequired);
			return null;
		}

		for (ContentGuardIndicatorUrlChanges contentGuardIndicatorUrlChanges : contentGuardIndicatorUrlChangesList) {
			zapierContentGuardAlert = new ZapierContentGuardAlert();
			zapierContentGuardAlert.setId(String.valueOf(startingId++));
			zapierContentGuardAlert.setAlert_timestamp(DateFormatUtils.format(new Date(), IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS));
			zapierContentGuardAlert.setDomain_id(domainId);
			zapierContentGuardAlert.setDomain_name(domainName);
			zapierContentGuardAlert.setGroup_id(groupId);
			zapierContentGuardAlert.setGroup_name(groupName);
			zapierContentGuardAlert.setUrl(contentGuardIndicatorUrlChanges.getUrl());
			changeDescription = ContentGuardUtils.getInstance().getChangeIndicatorDescription(contentGuardIndicatorUrlChanges.getChange_indicator());
			if (StringUtils.isBlank(changeDescription)) {
				throw new Exception("getZapierContentGuardAlertList() error--ip=" + ip + ",domainId=" + domainId + ",groupId=" + groupId + ",groupName=" + groupName
						+ ",contentGuardIndicatorUrlChanges.getChange_indicator()=" + contentGuardIndicatorUrlChanges.getChange_indicator()
						+ ",changeDescription is empty.");
			}
			zapierContentGuardAlert.setChange_desc(changeDescription);
			changeSeverity = ContentGuardUtils.getInstance().getChangeIndicatorSeverity(contentGuardIndicatorUrlChanges.getChange_indicator());
			if (changeSeverity == null) {
				throw new Exception("getZapierContentGuardAlertList() error--ip=" + ip + ",domainId=" + domainId + ",groupId=" + groupId + ",groupName=" + groupName
						+ ",contentGuardIndicatorUrlChanges.getChange_indicator()=" + contentGuardIndicatorUrlChanges.getChange_indicator()
						+ ",changeSeverity is null.");
			}
			zapierContentGuardAlert.setChange_severity(changeSeverity);
			zapierContentGuardAlert.setPrevious_crawl_timestamp(contentGuardIndicatorUrlChanges.getPrevious_crawl_timestamp());
			zapierContentGuardAlert.setCurrent_crawl_timestamp(contentGuardIndicatorUrlChanges.getCurrent_crawl_timestamp());

			if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.ALTERNATE_LINKS)
					|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.CUSTOM_DATA)
					|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.HREFLANG_ERRORS)
					|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.HREFLANG_LINKS)
					|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.OG_MARKUP)
					|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.PAGE_ANALYSIS_RESULTS_CHG_IND_JSON)
					|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.PAGE_LINK)
					|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.REDIRECT_CHAIN)
					|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.STRUCTURED_DATA)) {
				zapierContentGuardAlert.setPrevious_content(IConstants.EMPTY_STRING);
				zapierContentGuardAlert.setCurrent_content(IConstants.EMPTY_STRING);
			} else if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.H1)) {
				if (contentGuardIndicatorUrlChanges.getH1_previous_array() != null && contentGuardIndicatorUrlChanges.getH1_previous_array().length > 0) {
					stringBuilder = new StringBuilder();
					for (String h1Previous : contentGuardIndicatorUrlChanges.getH1_previous_array()) {
						stringBuilder.append(StringEscapeUtils.escapeHtml(h1Previous)).append(IConstants.ONE_SPACE);
					}
					zapierContentGuardAlert.setPrevious_content(stringBuilder.toString());
				}
				if (contentGuardIndicatorUrlChanges.getH1_current_array() != null && contentGuardIndicatorUrlChanges.getH1_current_array().length > 0) {
					stringBuilder = new StringBuilder();
					for (String h1Current : contentGuardIndicatorUrlChanges.getH1_current_array()) {
						stringBuilder.append(StringEscapeUtils.escapeHtml(h1Current)).append(IConstants.ONE_SPACE);
					}
					zapierContentGuardAlert.setCurrent_content(stringBuilder.toString());
				}
			} else if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.H2)) {
				if (contentGuardIndicatorUrlChanges.getH2_previous_array() != null && contentGuardIndicatorUrlChanges.getH2_previous_array().length > 0) {
					stringBuilder = new StringBuilder();
					for (String h2Previous : contentGuardIndicatorUrlChanges.getH2_previous_array()) {
						stringBuilder.append(StringEscapeUtils.escapeHtml(h2Previous)).append(IConstants.ONE_SPACE);
					}
					zapierContentGuardAlert.setPrevious_content(stringBuilder.toString());
				}
				if (contentGuardIndicatorUrlChanges.getH2_current_array() != null && contentGuardIndicatorUrlChanges.getH2_current_array().length > 0) {
					stringBuilder = new StringBuilder();
					for (String h2Current : contentGuardIndicatorUrlChanges.getH2_current_array()) {
						stringBuilder.append(StringEscapeUtils.escapeHtml(h2Current)).append(IConstants.ONE_SPACE);
					}
					zapierContentGuardAlert.setCurrent_content(stringBuilder.toString());
				}
			} else if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.INSECURE_RESOURCES)) {
				if (contentGuardIndicatorUrlChanges.getInsecure_resources_previous_array() != null
						&& contentGuardIndicatorUrlChanges.getInsecure_resources_previous_array().length > 0) {
					stringBuilder = new StringBuilder();
					for (String insecurePrevious : contentGuardIndicatorUrlChanges.getInsecure_resources_previous_array()) {
						stringBuilder.append(StringEscapeUtils.escapeHtml(insecurePrevious)).append(IConstants.ONE_SPACE);
					}
					zapierContentGuardAlert.setPrevious_content(stringBuilder.toString());
				}
				if (contentGuardIndicatorUrlChanges.getInsecure_resources_current_array() != null
						&& contentGuardIndicatorUrlChanges.getInsecure_resources_current_array().length > 0) {
					stringBuilder = new StringBuilder();
					for (String insecureCurrent : contentGuardIndicatorUrlChanges.getInsecure_resources_current_array()) {
						stringBuilder.append(StringEscapeUtils.escapeHtml(insecureCurrent)).append(IConstants.ONE_SPACE);
					}
					zapierContentGuardAlert.setCurrent_content(stringBuilder.toString());
				}
			} else if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.RESPONSE_HEADERS)) {
				if (contentGuardIndicatorUrlChanges.getResponse_header_previous_array() != null
						&& contentGuardIndicatorUrlChanges.getResponse_header_previous_array().length > 0) {
					stringBuilder = new StringBuilder();
					for (String headerPrevious : contentGuardIndicatorUrlChanges.getResponse_header_previous_array()) {
						stringBuilder.append(StringEscapeUtils.escapeHtml(headerPrevious)).append(IConstants.ONE_SPACE);
					}
					zapierContentGuardAlert.setPrevious_content(stringBuilder.toString());
				}
				if (contentGuardIndicatorUrlChanges.getResponse_header_current_array() != null
						&& contentGuardIndicatorUrlChanges.getResponse_header_current_array().length > 0) {
					stringBuilder = new StringBuilder();
					for (String headerCurrent : contentGuardIndicatorUrlChanges.getResponse_header_current_array()) {
						stringBuilder.append(StringEscapeUtils.escapeHtml(headerCurrent)).append(IConstants.ONE_SPACE);
					}
					zapierContentGuardAlert.setCurrent_content(stringBuilder.toString());
				}
			} else {
				zapierContentGuardAlert.setPrevious_content(contentGuardIndicatorUrlChanges.getPrevious_content());
				zapierContentGuardAlert.setCurrent_content(contentGuardIndicatorUrlChanges.getCurrent_content());
			}
			if (isDebug == true) {
				System.out.println("getZapierContentGuardAlertList() ip=" + ip + ",domainId=" + domainId + ",groupId=" + groupId + ",zapierContentGuardAlert="
						+ zapierContentGuardAlert.toString());
			}
			zapierContentGuardAlertList.add(zapierContentGuardAlert);
		}
		return zapierContentGuardAlertList;
	}

	private List<ContentGuardAlertSummary> getContentGuardAlertSummaryList(List<ContentGuardIndicatorUrlChanges> contentGuardIndicatorUrlChangesList) throws Exception {

		FormatUtils.getInstance().logMemoryUsage("getContentGuardAlertSummaryList() begins. ip=" + ip + ",domainId=" + domainId
				+ ",contentGuardIndicatorUrlChangesList.size()=" + contentGuardIndicatorUrlChangesList.size());

		List<ContentGuardAlertSummary> contentGuardAlertSummaryList = new ArrayList<ContentGuardAlertSummary>();
		ContentGuardAlertSummary contentGuardAlertSummary = null;

		// map key = change indicator description
		// map value = list of ContentGuardAlertDetails
		Map<String, List<ContentGuardAlertDetails>> changeIndicatorContentGuardAlertDetailsListMap = new TreeMap<String, List<ContentGuardAlertDetails>>();

		List<ContentGuardAlertDetails> contentGuardAlertDetailsList = null;
		ContentGuardAlertDetails contentGuardAlertDetails = null;
		String changeIndicatorDesc = null;
		StringBuilder stringBuilder = null;
		Set<String> h2AddedSet = null;
		Set<Integer> h2ChangePositionSet = null;
		Set<String> h2RemovedSet = null;

		// map key = URL string
		// map value = h2AddedSet
		Map<String, Set<String>> urlH2AddedSetMap = new HashMap<String, Set<String>>();

		// map key = URL string
		// map value = h2ChangePositionSet
		Map<String, Set<Integer>> urlH2ChangePositionSetMap = new HashMap<String, Set<Integer>>();

		// map key = URL string
		// map value = h2RemovedSet
		Map<String, Set<String>> urlH2RemovedSetMap = new HashMap<String, Set<String>>();

		int h2PreviousIndex = 0;
		int h2CurrentIndex = 0;

		Set<String> responseHeaderAddedSet = null;
		Set<String> responseHeaderRemovedSet = null;

		// map key = URL string
		// map value = responseHeaderAddedSet
		Map<String, Set<String>> urlResponseHeaderAddedSetMap = new HashMap<String, Set<String>>();

		// map key = URL string
		// map value = responseHeaderRemovedSet
		Map<String, Set<String>> urlResponseHeaderRemovedSetMap = new HashMap<String, Set<String>>();

		for (ContentGuardIndicatorUrlChanges contentGuardIndicatorUrlChanges : contentGuardIndicatorUrlChangesList) {
			if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_indicator(), IConstants.H2_ADDED_IND)) {
				h2AddedSet = getH2AddedSet(contentGuardIndicatorUrlChanges.getH2_previous_array(), contentGuardIndicatorUrlChanges.getH2_current_array());
				if (h2AddedSet != null && h2AddedSet.size() > 0) {
					urlH2AddedSetMap.put(contentGuardIndicatorUrlChanges.getUrl(), h2AddedSet);
				}
				if (isDebug == true) {
					if (h2AddedSet != null && h2AddedSet.size() > 0) {
						for (String h2Added : h2AddedSet) {
							System.out.println("getContentGuardAlertSummaryList() debug url=" + contentGuardIndicatorUrlChanges.getUrl() + ",h2Added=" + h2Added);
						}
					} else {
						System.out.println("getContentGuardAlertSummaryList() error--debug url=" + contentGuardIndicatorUrlChanges.getUrl() + ",h2AddedSet is empty.");
					}
				}
			} else if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_indicator(), IConstants.H2_CHG_IND)) {
				h2ChangePositionSet = getH2ChangePositionSet(contentGuardIndicatorUrlChanges.getH2_previous_array(),
						contentGuardIndicatorUrlChanges.getH2_current_array());
				if (h2ChangePositionSet != null && h2ChangePositionSet.size() > 0) {
					urlH2ChangePositionSetMap.put(contentGuardIndicatorUrlChanges.getUrl(), h2ChangePositionSet);
				}
				if (isDebug == true) {
					if (h2ChangePositionSet != null && h2ChangePositionSet.size() > 0) {
						for (Integer h2ChangePosition : h2ChangePositionSet) {
							System.out.println("getContentGuardAlertSummaryList() debug url=" + contentGuardIndicatorUrlChanges.getUrl() + ",h2ChangePosition="
									+ h2ChangePosition);
						}
					} else {
						System.out.println(
								"getContentGuardAlertSummaryList() error--debug url=" + contentGuardIndicatorUrlChanges.getUrl() + ",h2ChangePositionSet is empty.");
					}
				}
			} else if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_indicator(), IConstants.H2_REMOVED_IND)) {
				h2RemovedSet = getH2RemovedSet(contentGuardIndicatorUrlChanges.getH2_previous_array(), contentGuardIndicatorUrlChanges.getH2_current_array());
				if (h2RemovedSet != null && h2RemovedSet.size() > 0) {
					urlH2RemovedSetMap.put(contentGuardIndicatorUrlChanges.getUrl(), h2RemovedSet);
				}
				if (isDebug == true) {
					if (h2RemovedSet != null && h2RemovedSet.size() > 0) {
						for (String h2Removed : h2RemovedSet) {
							System.out.println("getContentGuardAlertSummaryList() debug url=" + contentGuardIndicatorUrlChanges.getUrl() + ",h2Removed=" + h2Removed);
						}
					} else {
						System.out.println("getContentGuardAlertSummaryList() error--debug url=" + contentGuardIndicatorUrlChanges.getUrl() + ",h2Removed is empty.");
					}
				}
			} else if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_indicator(), IConstants.RESPONSE_HEADERS_ADDED_IND)) {
				responseHeaderAddedSet = getResponseHeaderAddedSet(contentGuardIndicatorUrlChanges.getResponse_header_previous_array(),
						contentGuardIndicatorUrlChanges.getResponse_header_current_array());
				if (responseHeaderAddedSet != null && responseHeaderAddedSet.size() > 0) {
					urlResponseHeaderAddedSetMap.put(contentGuardIndicatorUrlChanges.getUrl(), responseHeaderAddedSet);
				}
				if (isDebug == true) {
					if (responseHeaderAddedSet != null && responseHeaderAddedSet.size() > 0) {
						for (String responseHeaderAdded : responseHeaderAddedSet) {
							System.out.println("getContentGuardAlertSummaryList() debug url=" + contentGuardIndicatorUrlChanges.getUrl() + ",responseHeaderAdded="
									+ responseHeaderAdded);
						}
					} else {
						System.out.println(
								"getContentGuardAlertSummaryList() error--debug url=" + contentGuardIndicatorUrlChanges.getUrl() + ",responseHeaderAddedSet is empty.");
					}
				}
			} else if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_indicator(), IConstants.RESPONSE_HEADERS_REMOVED_IND)) {
				responseHeaderRemovedSet = getResponseHeaderRemovedSet(contentGuardIndicatorUrlChanges.getResponse_header_previous_array(),
						contentGuardIndicatorUrlChanges.getResponse_header_current_array());
				if (responseHeaderRemovedSet != null && responseHeaderRemovedSet.size() > 0) {
					urlResponseHeaderRemovedSetMap.put(contentGuardIndicatorUrlChanges.getUrl(), responseHeaderRemovedSet);
				}
				if (isDebug == true) {
					if (responseHeaderRemovedSet != null && responseHeaderRemovedSet.size() > 0) {
						for (String responseHeaderRemoved : responseHeaderRemovedSet) {
							System.out.println("getContentGuardAlertSummaryList() debug url=" + contentGuardIndicatorUrlChanges.getUrl() + ",responseHeaderRemoved="
									+ responseHeaderRemoved);
						}
					} else {
						System.out.println("getContentGuardAlertSummaryList() error--debug url=" + contentGuardIndicatorUrlChanges.getUrl()
								+ ",responseHeaderRemovedSet is empty.");
					}
				}
			}
		}

		for (ContentGuardIndicatorUrlChanges contentGuardIndicatorUrlChanges : contentGuardIndicatorUrlChangesList) {
			//			if (isDebug == true) {
			//				FormatUtils.getInstance().logMemoryUsage("getContentGuardAlertSummaryList() ip=" + ip + ",domainId=" + domainId + ",contentGuardIndicatorUrlChanges="
			//						+ contentGuardIndicatorUrlChanges.toString());
			//			}
			changeIndicatorDesc = ContentGuardUtils.getInstance().getChangeIndicatorDescription(contentGuardIndicatorUrlChanges.getChange_indicator());
			if (StringUtils.isBlank(changeIndicatorDesc)) {
				throw new Exception("getContentGuardAlertSummaryList() error--ip=" + ip + ",domainId=" + domainId + ",url=" + contentGuardIndicatorUrlChanges.getUrl()
						+ ",changeIndicator=" + contentGuardIndicatorUrlChanges.getChange_indicator() + ",changeIndicatorDesc is blank.");
			}

			if (changeIndicatorContentGuardAlertDetailsListMap.containsKey(changeIndicatorDesc)) {
				contentGuardAlertDetailsList = changeIndicatorContentGuardAlertDetailsListMap.get(changeIndicatorDesc);
			} else {
				contentGuardAlertDetailsList = new ArrayList<ContentGuardAlertDetails>();
			}
			contentGuardAlertDetails = new ContentGuardAlertDetails();
			contentGuardAlertDetails.setUrl(StringEscapeUtils.escapeHtml(contentGuardIndicatorUrlChanges.getUrl()));

			if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.ALTERNATE_LINKS)
					|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.CUSTOM_DATA)
					|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.HREFLANG_ERRORS)
					|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.HREFLANG_LINKS)
					|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.OG_MARKUP)
					|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.PAGE_ANALYSIS_RESULTS_CHG_IND_JSON)
					|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.PAGE_LINK)
					|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.REDIRECT_CHAIN)
					|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.STRUCTURED_DATA)) {
				contentGuardAlertDetails.setPreviousContent(IConstants.EMPTY_STRING);
				contentGuardAlertDetails.setCurrentContent(IConstants.EMPTY_STRING);
			} else if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.H1)) {
				if (contentGuardIndicatorUrlChanges.getH1_previous_array() != null && contentGuardIndicatorUrlChanges.getH1_previous_array().length > 0) {
					stringBuilder = new StringBuilder();
					for (String h1Previous : contentGuardIndicatorUrlChanges.getH1_previous_array()) {
						stringBuilder.append(StringEscapeUtils.escapeHtml(h1Previous));
						stringBuilder.append(IConstants.HTML_LINE_BREAK);
					}
					contentGuardAlertDetails.setPreviousContent(stringBuilder.toString());
				}
				if (contentGuardIndicatorUrlChanges.getH1_current_array() != null && contentGuardIndicatorUrlChanges.getH1_current_array().length > 0) {
					stringBuilder = new StringBuilder();
					for (String h1Current : contentGuardIndicatorUrlChanges.getH1_current_array()) {
						stringBuilder.append(StringEscapeUtils.escapeHtml(h1Current));
						stringBuilder.append(IConstants.HTML_LINE_BREAK);
					}
					contentGuardAlertDetails.setCurrentContent(stringBuilder.toString());
				}
			} else if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.H2)) {
				if (urlH2AddedSetMap.containsKey(contentGuardIndicatorUrlChanges.getUrl())) {
					h2AddedSet = urlH2AddedSetMap.get(contentGuardIndicatorUrlChanges.getUrl());
				} else {
					h2AddedSet = null;
					//throw new Exception(
					//		"getContentGuardAlertSummaryList() error--ip=" + ip + ",domainId=" + domainId + ",url=" + contentGuardIndicatorUrlChanges.getUrl()
					//				+ ",changeIndicator=" + contentGuardIndicatorUrlChanges.getChange_indicator() + ",h2AddedSet is null.");
				}
				if (urlH2ChangePositionSetMap.containsKey(contentGuardIndicatorUrlChanges.getUrl())) {
					h2ChangePositionSet = urlH2ChangePositionSetMap.get(contentGuardIndicatorUrlChanges.getUrl());
				} else {
					h2ChangePositionSet = null;
					//throw new Exception(
					//		"getContentGuardAlertSummaryList() error--ip=" + ip + ",domainId=" + domainId + ",url=" + contentGuardIndicatorUrlChanges.getUrl()
					//				+ ",changeIndicator=" + contentGuardIndicatorUrlChanges.getChange_indicator() + ",h2ChangePositionSet is null.");
				}
				if (urlH2RemovedSetMap.containsKey(contentGuardIndicatorUrlChanges.getUrl())) {
					h2RemovedSet = urlH2RemovedSetMap.get(contentGuardIndicatorUrlChanges.getUrl());
				} else {
					h2RemovedSet = null;
					//throw new Exception(
					//		"getContentGuardAlertSummaryList() error--ip=" + ip + ",domainId=" + domainId + ",url=" + contentGuardIndicatorUrlChanges.getUrl()
					//				+ ",changeIndicator=" + contentGuardIndicatorUrlChanges.getChange_indicator() + ",h2RemovedSet is null.");
				}
				if (contentGuardIndicatorUrlChanges.getH2_previous_array() != null && contentGuardIndicatorUrlChanges.getH2_previous_array().length > 0) {
					stringBuilder = new StringBuilder();
					h2PreviousIndex = 0;
					for (String h2Previous : contentGuardIndicatorUrlChanges.getH2_previous_array()) {
						// when change indicator is 'h2 removed'
						if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_indicator(), IConstants.H2_REMOVED_IND)) {
							if (isDebug == true) {
								System.out.println("getContentGuardAlertSummaryList() debug h2Previous=" + h2Previous);
							}
							if (h2RemovedSet != null && h2RemovedSet.contains(h2Previous)) {
								if (isDebug == true) {
									System.out.println("getContentGuardAlertSummaryList() debug h2Previous is removed=" + h2Previous);
								}
								stringBuilder.append(IConstants.DIV_START_TAG_STYLE_COLOR_RED);
								stringBuilder.append(StringEscapeUtils.escapeHtml(h2Previous));
								stringBuilder.append(IConstants.DIV_END_TAG);
							} else {
								stringBuilder.append(StringEscapeUtils.escapeHtml(h2Previous));
								stringBuilder.append(IConstants.HTML_LINE_BREAK);
							}
						}
						// when change indicator is 'h2 changed'
						else if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_indicator(), IConstants.H2_CHG_IND)) {
							// when h2 removed
							if (h2RemovedSet != null && h2RemovedSet.contains(h2Previous)) {
								if (isDebug == true) {
									System.out.println(
											"getContentGuardAlertSummaryList() debug h2Previous is removed=" + h2Previous + ",h2PreviousIndex=" + h2PreviousIndex);
								}
								stringBuilder.append(IConstants.DIV_START_TAG_STYLE_COLOR_RED);
								stringBuilder.append(StringEscapeUtils.escapeHtml(h2Previous));
								stringBuilder.append(IConstants.DIV_END_TAG);
							}
							// when h2 is not removed and there are no other changes
							else if (h2ChangePositionSet == null || h2ChangePositionSet.size() == 0) {
								if (isDebug == true) {
									System.out.println("getContentGuardAlertSummaryList() debug h2ChangePositionSet is null, h2Previous is not changed=" + h2Previous
											+ ",h2PreviousIndex=" + h2PreviousIndex);
								}
								stringBuilder.append(StringEscapeUtils.escapeHtml(h2Previous));
								stringBuilder.append(IConstants.HTML_LINE_BREAK);
							}
							// when h2 is not removed and there are other changes
							else {
								if (h2ChangePositionSet != null && h2ChangePositionSet.contains(h2PreviousIndex)) {
									if (isDebug == true) {
										System.out.println("getContentGuardAlertSummaryList() debug h2ChangePositionSet is not null, h2Previous is changed="
												+ h2Previous + ",h2PreviousIndex=" + h2PreviousIndex);
									}
									stringBuilder.append(IConstants.DIV_START_TAG_STYLE_COLOR_RED);
									stringBuilder.append(StringEscapeUtils.escapeHtml(h2Previous));
									stringBuilder.append(IConstants.DIV_END_TAG);
								} else {
									if (isDebug == true) {
										System.out.println("getContentGuardAlertSummaryList() debug h2ChangePositionSet is not null, h2Previous is not changed="
												+ h2Previous + ",h2PreviousIndex=" + h2PreviousIndex);
									}
									stringBuilder.append(StringEscapeUtils.escapeHtml(h2Previous));
									stringBuilder.append(IConstants.HTML_LINE_BREAK);
								}
							}
						}
						// when change indicator is not 'h2 removed' and not 'h2 changed'
						else {
							stringBuilder.append(StringEscapeUtils.escapeHtml(h2Previous));
							stringBuilder.append(IConstants.HTML_LINE_BREAK);
						}
						h2PreviousIndex++;
					}
					contentGuardAlertDetails.setPreviousContent(stringBuilder.toString());
				}
				if (contentGuardIndicatorUrlChanges.getH2_current_array() != null && contentGuardIndicatorUrlChanges.getH2_current_array().length > 0) {
					stringBuilder = new StringBuilder();
					h2CurrentIndex = 0;
					for (String h2Current : contentGuardIndicatorUrlChanges.getH2_current_array()) {
						// when change indicator is 'h2 added'
						if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_indicator(), IConstants.H2_ADDED_IND)) {
							if (isDebug == true) {
								System.out.println("getContentGuardAlertSummaryList() debug h2Current=" + h2Current);
							}
							if (h2AddedSet != null && h2AddedSet.contains(h2Current)) {
								if (isDebug == true) {
									System.out.println("getContentGuardAlertSummaryList() debug h2Current is added=" + h2Current);
								}
								stringBuilder.append(IConstants.DIV_START_TAG_STYLE_COLOR_BLUE);
								stringBuilder.append(StringEscapeUtils.escapeHtml(h2Current));
								stringBuilder.append(IConstants.DIV_END_TAG);
							} else {
								stringBuilder.append(StringEscapeUtils.escapeHtml(h2Current));
								stringBuilder.append(IConstants.HTML_LINE_BREAK);
							}
						}
						// when change indicator is 'h2 changed'
						else if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_indicator(), IConstants.H2_CHG_IND)) {
							// when h2 added
							if (h2AddedSet != null && h2AddedSet.contains(h2Current)) {
								if (isDebug == true) {
									System.out.println("getContentGuardAlertSummaryList() debug h2Current is added=" + h2Current + ",h2CurrentIndex=" + h2CurrentIndex);
								}
								stringBuilder.append(IConstants.DIV_START_TAG_STYLE_COLOR_BLUE);
								stringBuilder.append(StringEscapeUtils.escapeHtml(h2Current));
								stringBuilder.append(IConstants.DIV_END_TAG);
							}
							// when h2 is not added and there are no other changes
							else if (h2ChangePositionSet == null || h2ChangePositionSet.size() == 0) {
								if (isDebug == true) {
									System.out.println("getContentGuardAlertSummaryList() debug h2ChangePositionSet is null,h2Current is not changed=" + h2Current
											+ ",h2CurrentIndex=" + h2CurrentIndex);
								}
								stringBuilder.append(StringEscapeUtils.escapeHtml(h2Current));
								stringBuilder.append(IConstants.HTML_LINE_BREAK);
							}
							// when h2 is not added and there are other changes
							else {
								if (h2ChangePositionSet != null && h2ChangePositionSet.contains(h2CurrentIndex)) {
									if (isDebug == true) {
										System.out.println("getContentGuardAlertSummaryList() debug h2ChangePositionSet is not null, h2Current is changed=" + h2Current
												+ ",h2CurrentIndex=" + h2CurrentIndex);
									}
									stringBuilder.append(IConstants.DIV_START_TAG_STYLE_COLOR_BLUE);
									stringBuilder.append(StringEscapeUtils.escapeHtml(h2Current));
									stringBuilder.append(IConstants.DIV_END_TAG);
								} else {
									if (isDebug == true) {
										System.out.println("getContentGuardAlertSummaryList() debug h2ChangePositionSet is not null, h2Current is not changed="
												+ h2Current + ",h2CurrentIndex=" + h2CurrentIndex);
									}
									stringBuilder.append(StringEscapeUtils.escapeHtml(h2Current));
									stringBuilder.append(IConstants.HTML_LINE_BREAK);
								}
							}
						}
						// when change indicator is not 'h2 added' and not 'h2 changed'
						else {
							stringBuilder.append(StringEscapeUtils.escapeHtml(h2Current));
							stringBuilder.append(IConstants.HTML_LINE_BREAK);
						}
						h2CurrentIndex++;
					}
					contentGuardAlertDetails.setCurrentContent(stringBuilder.toString());
				}
			} else if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.INSECURE_RESOURCES)) {
				if (contentGuardIndicatorUrlChanges.getInsecure_resources_previous_array() != null
						&& contentGuardIndicatorUrlChanges.getInsecure_resources_previous_array().length > 0) {
					stringBuilder = new StringBuilder();
					for (String insecurePrevious : contentGuardIndicatorUrlChanges.getInsecure_resources_previous_array()) {
						stringBuilder.append(StringEscapeUtils.escapeHtml(insecurePrevious));
						stringBuilder.append(IConstants.HTML_LINE_BREAK);
					}
					contentGuardAlertDetails.setPreviousContent(stringBuilder.toString());
				}
				if (contentGuardIndicatorUrlChanges.getInsecure_resources_current_array() != null
						&& contentGuardIndicatorUrlChanges.getInsecure_resources_current_array().length > 0) {
					stringBuilder = new StringBuilder();
					for (String insecureCurrent : contentGuardIndicatorUrlChanges.getInsecure_resources_current_array()) {
						stringBuilder.append(StringEscapeUtils.escapeHtml(insecureCurrent));
						stringBuilder.append(IConstants.HTML_LINE_BREAK);
					}
					contentGuardAlertDetails.setCurrentContent(stringBuilder.toString());
				}
			} else if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.RESPONSE_HEADERS)) {
				if (urlResponseHeaderAddedSetMap.containsKey(contentGuardIndicatorUrlChanges.getUrl())) {
					responseHeaderAddedSet = urlResponseHeaderAddedSetMap.get(contentGuardIndicatorUrlChanges.getUrl());
				} else {
					responseHeaderAddedSet = null;
				}
				if (urlResponseHeaderRemovedSetMap.containsKey(contentGuardIndicatorUrlChanges.getUrl())) {
					responseHeaderRemovedSet = urlResponseHeaderRemovedSetMap.get(contentGuardIndicatorUrlChanges.getUrl());
				} else {
					responseHeaderRemovedSet = null;
				}
				if (contentGuardIndicatorUrlChanges.getResponse_header_previous_array() != null
						&& contentGuardIndicatorUrlChanges.getResponse_header_previous_array().length > 0) {
					stringBuilder = new StringBuilder();
					for (String headerPrevious : contentGuardIndicatorUrlChanges.getResponse_header_previous_array()) {
						// when change indicator is 'response headers removed'
						if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_indicator(), IConstants.RESPONSE_HEADERS_REMOVED_IND)) {
							if (isDebug == true) {
								System.out.println("getContentGuardAlertSummaryList() debug headerPrevious=" + headerPrevious);
							}
							if (responseHeaderRemovedSet != null && responseHeaderRemovedSet.contains(headerPrevious)) {
								if (isDebug == true) {
									System.out.println("getContentGuardAlertSummaryList() debug headerPrevious is removed=" + headerPrevious);
								}
								stringBuilder.append(IConstants.DIV_START_TAG_STYLE_COLOR_RED);
								stringBuilder.append(StringEscapeUtils.escapeHtml(headerPrevious));
								stringBuilder.append(IConstants.DIV_END_TAG);
							} else {
								stringBuilder.append(StringEscapeUtils.escapeHtml(headerPrevious));
								stringBuilder.append(IConstants.HTML_LINE_BREAK);
							}
						}
						// when change indicator is not 'response header removed'
						else {
							stringBuilder.append(StringEscapeUtils.escapeHtml(headerPrevious));
							stringBuilder.append(IConstants.HTML_LINE_BREAK);
						}
					}
					contentGuardAlertDetails.setPreviousContent(stringBuilder.toString());
				}
				if (contentGuardIndicatorUrlChanges.getResponse_header_current_array() != null
						&& contentGuardIndicatorUrlChanges.getResponse_header_current_array().length > 0) {
					stringBuilder = new StringBuilder();
					for (String headerCurrent : contentGuardIndicatorUrlChanges.getResponse_header_current_array()) {
						// when change indicator is 'response headers added'
						if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_indicator(), IConstants.RESPONSE_HEADERS_ADDED_IND)) {
							if (isDebug == true) {
								System.out.println("getContentGuardAlertSummaryList() debug headerCurrent=" + headerCurrent);
							}
							if (responseHeaderAddedSet != null && responseHeaderAddedSet.contains(headerCurrent)) {
								if (isDebug == true) {
									System.out.println("getContentGuardAlertSummaryList() debug headerCurrent is added=" + headerCurrent);
								}
								stringBuilder.append(IConstants.DIV_START_TAG_STYLE_COLOR_BLUE);
								stringBuilder.append(StringEscapeUtils.escapeHtml(headerCurrent));
								stringBuilder.append(IConstants.DIV_END_TAG);
							} else {
								stringBuilder.append(StringEscapeUtils.escapeHtml(headerCurrent));
								stringBuilder.append(IConstants.HTML_LINE_BREAK);
							}
						}
						// when change indicator is not 'response header added'
						else {
							stringBuilder.append(StringEscapeUtils.escapeHtml(headerCurrent));
							stringBuilder.append(IConstants.HTML_LINE_BREAK);
						}
					}
					contentGuardAlertDetails.setCurrentContent(stringBuilder.toString());
				}
			} else {
				contentGuardAlertDetails.setPreviousContent(StringEscapeUtils.escapeHtml(contentGuardIndicatorUrlChanges.getPrevious_content()));
				contentGuardAlertDetails.setCurrentContent(StringEscapeUtils.escapeHtml(contentGuardIndicatorUrlChanges.getCurrent_content()));
			}
			//			if (isDebug == true) {
			//				FormatUtils.getInstance().logMemoryUsage(
			//						"getContentGuardAlertSummaryList() ip=" + ip + ",domainId=" + domainId + ",contentGuardAlertDetails=" + contentGuardAlertDetails.toString());
			//			}
			contentGuardAlertDetailsList.add(contentGuardAlertDetails);
			changeIndicatorContentGuardAlertDetailsListMap.put(changeIndicatorDesc, contentGuardAlertDetailsList);
		}

		for (String testChangeIndicatorDesc : changeIndicatorContentGuardAlertDetailsListMap.keySet()) {
			contentGuardAlertSummary = new ContentGuardAlertSummary();
			contentGuardAlertSummary.setChangeIndicatorDesc(testChangeIndicatorDesc);
			contentGuardAlertSummary.setContentGuardAlertDetailsList(changeIndicatorContentGuardAlertDetailsListMap.get(testChangeIndicatorDesc));
			contentGuardAlertSummaryList.add(contentGuardAlertSummary);
		}

		FormatUtils.getInstance().logMemoryUsage("getContentGuardAlertSummaryList() ends. ip=" + ip + ",domainId=" + domainId + ",contentGuardAlertSummaryList.size()="
				+ contentGuardAlertSummaryList.size());

		return contentGuardAlertSummaryList;
	}

	private Set<String> getH2AddedSet(String[] h2PreviousArray, String[] h2CurrentArray) {
		Set<String> h2AddedSet = new HashSet<String>();
		boolean isH2Added = false;
		// when the current H2 is not in previous H2 array, then the current H2 is added
		for (String h2Current : h2CurrentArray) {
			isH2Added = true;
			if (h2PreviousArray != null && h2PreviousArray.length > 0) {
				nextH2Previous: for (String h2Previous : h2PreviousArray) {
					if (StringUtils.equals(h2Current, h2Previous)) {
						isH2Added = false;
						break nextH2Previous;
					}
				}
			}
			if (isH2Added == true) {
				h2AddedSet.add(h2Current);
			}
		}
		return h2AddedSet;
	}

	private Set<Integer> getH2ChangePositionSet(String[] h2PreviousArray, String[] h2CurrentArray) {
		Set<Integer> h2ChangePositionSet = new HashSet<Integer>();
		String h2Previous = null;
		String h2Current = null;
		if (h2PreviousArray.length == h2CurrentArray.length) {
			if (h2PreviousArray != null && h2PreviousArray.length > 0) {
				for (int i = 0; i < h2PreviousArray.length; i++) {
					h2Previous = h2PreviousArray[i];
					h2Current = h2CurrentArray[i];
					if (StringUtils.equals(h2Previous, h2Current) == false) {
						h2ChangePositionSet.add(i);
					}
				}
			}
		}
		return h2ChangePositionSet;
	}

	private Set<String> getH2RemovedSet(String[] h2PreviousArray, String[] h2CurrentArray) {
		Set<String> h2RemovedSet = new HashSet<String>();
		boolean isH2Removed = false;
		// when the previous H2 is not in current H2 array, then the previous H2 is removed
		for (String h2Previous : h2PreviousArray) {
			isH2Removed = true;
			if (h2CurrentArray != null && h2CurrentArray.length > 0) {
				nextH2Current: for (String h2Current : h2CurrentArray) {
					if (StringUtils.equals(h2Previous, h2Current)) {
						isH2Removed = false;
						break nextH2Current;
					}
				}
			}
			if (isH2Removed == true) {
				h2RemovedSet.add(h2Previous);
			}
		}
		return h2RemovedSet;
	}

	private Set<String> getResponseHeaderAddedSet(String[] responseHeaderPreviousArray, String[] responseHeaderCurrentArray) {
		Set<String> responseHeaderAddedSet = new HashSet<String>();
		boolean isResponseHeaderAdded = false;
		// when the current ResponseHeader is not in previous ResponseHeader array, then the current ResponseHeader is added
		for (String responseHeaderCurrent : responseHeaderCurrentArray) {
			isResponseHeaderAdded = true;
			if (responseHeaderPreviousArray != null && responseHeaderPreviousArray.length > 0) {
				nextResponseHeaderPrevious: for (String responseHeaderPrevious : responseHeaderPreviousArray) {
					if (StringUtils.equals(responseHeaderCurrent, responseHeaderPrevious)) {
						isResponseHeaderAdded = false;
						break nextResponseHeaderPrevious;
					}
				}
			}
			if (isResponseHeaderAdded == true) {
				responseHeaderAddedSet.add(responseHeaderCurrent);
			}
		}
		return responseHeaderAddedSet;
	}

	private Set<String> getResponseHeaderRemovedSet(String[] responseHeaderPreviousArray, String[] responseHeaderCurrentArray) {
		Set<String> responseHeaderRemovedSet = new HashSet<String>();
		boolean isResponseHeaderRemoved = false;
		// when the previous ResponseHeader is not in current ResponseHeader array, then the previous ResponseHeader is removed
		for (String responseHeaderPrevious : responseHeaderPreviousArray) {
			isResponseHeaderRemoved = true;
			nextResponseHeaderCurrent: for (String responseHeaderCurrent : responseHeaderCurrentArray) {
				if (StringUtils.equals(responseHeaderPrevious, responseHeaderCurrent)) {
					isResponseHeaderRemoved = false;
					break nextResponseHeaderCurrent;
				}
			}
			if (isResponseHeaderRemoved == true) {
				responseHeaderRemovedSet.add(responseHeaderPrevious);
			}
		}
		return responseHeaderRemovedSet;
	}

	private Set<String> getInsecureResourceAddedSet(String[] insecureResourcePreviousArray, String[] insecureResourceCurrentArray) {
		Set<String> insecureResourceAddedSet = new HashSet<String>();
		boolean isInsecureResourceAdded = false;
		// when the current InsecureResource is not in previous InsecureResource array, then the current InsecureResource is added
		for (String insecureResourceCurrent : insecureResourceCurrentArray) {
			isInsecureResourceAdded = true;
			nextInsecureResourcePrevious: for (String insecureResourcePrevious : insecureResourcePreviousArray) {
				if (StringUtils.equals(insecureResourceCurrent, insecureResourcePrevious)) {
					isInsecureResourceAdded = false;
					break nextInsecureResourcePrevious;
				}
			}
			if (isInsecureResourceAdded == true) {
				insecureResourceAddedSet.add(insecureResourceCurrent);
			}
		}
		return insecureResourceAddedSet;
	}

	private Set<String> getInsecureResourceRemovedSet(String[] insecureResourcePreviousArray, String[] insecureResourceCurrentArray) {
		Set<String> insecureResourceRemovedSet = new HashSet<String>();
		boolean isInsecureResourceRemoved = false;
		// when the previous InsecureResource is not in current InsecureResource array, then the previous InsecureResource is removed
		for (String insecureResourcePrevious : insecureResourcePreviousArray) {
			isInsecureResourceRemoved = true;
			nextInsecureResourceCurrent: for (String insecureResourceCurrent : insecureResourceCurrentArray) {
				if (StringUtils.equals(insecureResourcePrevious, insecureResourceCurrent)) {
					isInsecureResourceRemoved = false;
					break nextInsecureResourceCurrent;
				}
			}
			if (isInsecureResourceRemoved == true) {
				insecureResourceRemovedSet.add(insecureResourcePrevious);
			}
		}
		return insecureResourceRemovedSet;
	}

	private ContentGuardResourceResponse getContentGuardResourceResponse(String crawlDate, Integer crawlHour, Long groupId) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("getContentGuardResourceResponse() begins. ip=" + ip + ",domainId=" + domainId + ",groupId=" + groupId + ",crawlDate="
				+ crawlDate + ",crawlHour=" + crawlHour);
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_INDICATOR_URL_LIST;
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(domainId);
		if (StringUtils.isNotBlank(crawlDate)) {
			contentGuardResourceRequest.setCrawl_date(crawlDate);
		}
		if (crawlHour != null) {
			contentGuardResourceRequest.setCrawl_hour(crawlHour);
		}
		contentGuardResourceRequest.setGroup_id(groupId);
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(Integer.MAX_VALUE);
		contentGuardResourceRequest.setReturn_details(true);
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		FormatUtils.getInstance().logMemoryUsage("getContentGuardResourceResponse() ip=" + ip + ",domainId=" + domainId + ",groupId=" + groupId + ",requestUrl="
				+ requestUrl + ",requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = mainWebServiceClientService.contentGuard(requestUrl, requestParameters, domainId, ip);

		//		if (isDebug == true) {
		//			if (BooleanUtils.isTrue(contentGuardResourceResponse.getSuccess())) {
		//				Map<String, Integer> changeTrackingIndicatorTotalsMap = contentGuardResourceResponse.getChange_tracking_indicator_totals_map();
		//				if (changeTrackingIndicatorTotalsMap != null && changeTrackingIndicatorTotalsMap.size() > 0) {
		//					for (String changeTrackingIndicator : changeTrackingIndicatorTotalsMap.keySet()) {
		//						int totalChanges = changeTrackingIndicatorTotalsMap.get(changeTrackingIndicator);
		//						FormatUtils.getInstance().logMemoryUsage("getContentGuardResourceResponse() ip=" + ip + ",domainId=" + domainId + ",groupId=" + groupId
		//								+ ",changeTrackingIndicator=" + changeTrackingIndicator + ",totalChanges=" + totalChanges);
		//					}
		//				}
		//				List<ContentGuardIndicatorUrlChanges> contentGuardIndicatorUrlChangesList = contentGuardResourceResponse.getIndicator_url_list();
		//				if (contentGuardIndicatorUrlChangesList != null && contentGuardIndicatorUrlChangesList.size() > 0) {
		//					for (ContentGuardIndicatorUrlChanges contentGuardIndicatorUrlChanges : contentGuardIndicatorUrlChangesList) {
		//						if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.ALTERNATE_LINKS)
		//								|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.CUSTOM_DATA)
		//								|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.HREFLANG_ERRORS)
		//								|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.HREFLANG_LINKS)
		//								|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.INSECURE_RESOURCES)
		//								|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.OG_MARKUP)
		//								|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.PAGE_ANALYSIS_RESULTS_CHG_IND_JSON)
		//								|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.PAGE_LINK)
		//								|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.REDIRECT_CHAIN)
		//								|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.RESPONSE_HEADERS)
		//								|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.STRUCTURED_DATA)) {
		//						} else if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.H1)
		//								|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.H2)) {
		//							FormatUtils.getInstance()
		//									.logMemoryUsage("getContentGuardResourceResponse() ip=" + ip + ",domainId=" + domainId + ",groupId=" + groupId + ",changeIndicator="
		//											+ contentGuardIndicatorUrlChanges.getChange_indicator() + ",changeField="
		//											+ contentGuardIndicatorUrlChanges.getChange_field() + ",changeType=" + contentGuardIndicatorUrlChanges.getChange_type()
		//											+ ",url=" + contentGuardIndicatorUrlChanges.getUrl() + ",previousCrawlTimestamp="
		//											+ contentGuardIndicatorUrlChanges.getPrevious_crawl_timestamp() + ",currentCrawlTimestamp="
		//											+ contentGuardIndicatorUrlChanges.getCurrent_crawl_timestamp() + "previous H2="
		//											+ (contentGuardIndicatorUrlChanges.getH2_previous_array() == null ? ""
		//													: Arrays.asList(contentGuardIndicatorUrlChanges.getH2_previous_array()))
		//											+ ",current H2=" + (contentGuardIndicatorUrlChanges.getH2_current_array() == null ? ""
		//													: Arrays.asList(contentGuardIndicatorUrlChanges.getH2_current_array())));
		//						} else {
		//							FormatUtils.getInstance()
		//									.logMemoryUsage("getContentGuardResourceResponse() ip=" + ip + ",domainId=" + domainId + ",groupId=" + groupId + ",changeIndicator="
		//											+ contentGuardIndicatorUrlChanges.getChange_indicator() + ",changeField="
		//											+ contentGuardIndicatorUrlChanges.getChange_field() + ",changeType=" + contentGuardIndicatorUrlChanges.getChange_type()
		//											+ ",url=" + contentGuardIndicatorUrlChanges.getUrl() + ",previousCrawlTimestamp="
		//											+ contentGuardIndicatorUrlChanges.getPrevious_crawl_timestamp() + ",currentCrawlTimestamp="
		//											+ contentGuardIndicatorUrlChanges.getCurrent_crawl_timestamp() + "previous content="
		//											+ contentGuardIndicatorUrlChanges.getPrevious_content() + ",current content="
		//											+ contentGuardIndicatorUrlChanges.getCurrent_content());
		//						}
		//
		//					}
		//				}
		//			}
		//		}

		FormatUtils.getInstance()
				.logMemoryUsage("getContentGuardResourceResponse() ends. ip=" + ip + ",domainId=" + domainId + ",groupId=" + groupId + ",crawlDate=" + crawlDate
						+ ",crawlHour=" + crawlHour + ",contentGuardResourceResponse.getSuccess()=" + contentGuardResourceResponse.getSuccess() + ",elapsed(ms.)="
						+ (System.currentTimeMillis() - startTimestamp));
		//		if (isDebug == true) {
		//			System.out.println("getContentGuardResourceResponse() ip=" + ip + ",domainId=" + domainId + ",groupId=" + groupId + ",contentGuardResourceResponse="
		//					+ contentGuardResourceResponse.toString());
		//		}
		return contentGuardResourceResponse;
	}

	private List<ContentGuardIndicatorUrlChanges> filterIndicatorUrlListByAlertIndicators(Long groupId, ContentGuardResourceResponse contentGuardResourceResponse,
			ContentGuardAlertEntity contentGuardAlertEntity) {
		List<ContentGuardIndicatorUrlChanges> filteredContentGuardIndicatorUrlList = new ArrayList<ContentGuardIndicatorUrlChanges>();
		Map<String, String> criticalIndicatorTypeMap = null;
		Map<String, String> customIndicatorTypeMap = null;
		String[] customIndicatorArray = null;

		//		if (isDebug == true) {
		//			FormatUtils.getInstance().logMemoryUsage("filterIndicatorUrlListByAlertIndicators() ip=" + ip + ",domainId=" + domainId + ",groupId=" + groupId
		//					+ ",contentGuardAlertEntity=" + contentGuardAlertEntity.toString());
		//		}

		if (contentGuardResourceResponse.getIndicator_url_list() != null && contentGuardResourceResponse.getIndicator_url_list().size() > 0) {
			if (contentGuardAlertEntity.getIndicatorFlag() == IConstants.INDICATOR_FLAG_ALL) {
				filteredContentGuardIndicatorUrlList = contentGuardResourceResponse.getIndicator_url_list();
			} else if (contentGuardAlertEntity.getIndicatorFlag() == IConstants.INDICATOR_FLAG_CRITICAL) {
				criticalIndicatorTypeMap = ContentGuardUtils.getInstance().getCriticalIndicatorTypeMap();
				if (criticalIndicatorTypeMap != null && criticalIndicatorTypeMap.size() > 0) {
					for (String criticalIndicator : criticalIndicatorTypeMap.keySet()) {
						for (ContentGuardIndicatorUrlChanges contentGuardIndicatorUrlChanges : contentGuardResourceResponse.getIndicator_url_list()) {
							if (StringUtils.equalsIgnoreCase(criticalIndicator, contentGuardIndicatorUrlChanges.getChange_indicator())) {
								filteredContentGuardIndicatorUrlList.add(contentGuardIndicatorUrlChanges);
							}
						}
					}
				} else {
					FormatUtils.getInstance().logMemoryUsage("filterIndicatorUrlListByAlertIndicators() error--ip=" + ip + ",domainId=" + domainId + ",groupId="
							+ groupId + ",criticalIndicatorTypeMap is empty.");
				}
			} else if (contentGuardAlertEntity.getIndicatorFlag() == IConstants.INDICATOR_FLAG_CUSTOM) {
				customIndicatorArray = new Gson().fromJson(contentGuardAlertEntity.getCustomIndicators(), String[].class);
				if (customIndicatorArray != null && customIndicatorArray.length > 0) {
					customIndicatorTypeMap = ContentGuardUtils.getInstance().getCustomIndicatorTypeMap(customIndicatorArray);
					if (customIndicatorTypeMap != null && customIndicatorTypeMap.size() > 0) {
						for (String customIndicator : customIndicatorTypeMap.keySet()) {
							for (ContentGuardIndicatorUrlChanges contentGuardIndicatorUrlChanges : contentGuardResourceResponse.getIndicator_url_list()) {
								if (StringUtils.equalsIgnoreCase(customIndicator, contentGuardIndicatorUrlChanges.getChange_indicator())) {
									filteredContentGuardIndicatorUrlList.add(contentGuardIndicatorUrlChanges);
								}
							}
						}
					} else {
						FormatUtils.getInstance().logMemoryUsage("filterIndicatorUrlListByAlertIndicators() error--ip=" + ip + ",domainId=" + domainId + ",groupId="
								+ groupId + ",customIndicatorTypeMap is empty.");
					}
				}
			}
		}
		if (filteredContentGuardIndicatorUrlList != null && filteredContentGuardIndicatorUrlList.size() > 0) {
			FormatUtils.getInstance().logMemoryUsage("filterIndicatorUrlListByAlertIndicators() ip=" + ip + ",domainId=" + domainId + ",groupId=" + groupId
					+ ",filteredContentGuardIndicatorUrlList.size()=" + filteredContentGuardIndicatorUrlList.size());
			//			if (isDebug == true) {
			//				for (ContentGuardIndicatorUrlChanges contentGuardIndicatorUrlChanges : filteredContentGuardIndicatorUrlList) {
			//					System.out.println("filterIndicatorUrlListByAlertIndicators() ip=" + ip + ",domainId=" + domainId + ",groupId=" + groupId
			//							+ ",contentGuardIndicatorUrlChanges=" + contentGuardIndicatorUrlChanges.toString());
			//				}
			//			}
		} else {
			FormatUtils.getInstance().logMemoryUsage("filterIndicatorUrlListByAlertIndicators() ip=" + ip + ",domainId=" + domainId + ",groupId=" + groupId
					+ ",filteredContentGuardIndicatorUrlList is empty.");
		}
		return filteredContentGuardIndicatorUrlList;
	}

	private void sendEmailAlert(String emails, String groupName, String groupInformation1, String groupInformation2, String groupInformation3,
			List<ContentGuardAlertSummary> contentGuardAlertSummaryList) {

		FormatUtils.getInstance().logMemoryUsage("sendAlert() begins. ip=" + ip + ",domainId=" + domainId + ",groupName=" + groupName);

		//WebhookEntity webhookEntity = null;
		String[] emailAddressArray = null;

		if (isDebug == true) {
			emailAddressArray = new String[] { "<EMAIL>", };
		} else {
			emailAddressArray = new Gson().fromJson(emails, String[].class);
		}

		int retryCount = 0;
		Map<String, Object> map = new HashMap<String, Object>();
		String emailSubject = "Content Guard Alerts for " + domainName + " (" + groupName + ") as of "
				+ DateFormatUtils.format(new Date(), IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
		map.put("domainId", domainId);
		map.put("groupInformation1", groupInformation1);
		map.put("groupInformation2", groupInformation2);
		map.put("groupInformation3", groupInformation3);
		map.put("contentGuardAlertSummaryList", contentGuardAlertSummaryList);

		//		if (isDebug == true) {
		//			System.out.println("sendEmailAlert() emailSubject=" + emailSubject);
		//			System.out.println("sendEmailAlert() groupInformation1=" + groupInformation1);
		//			System.out.println("sendEmailAlert() groupInformation2=" + groupInformation2);
		//			System.out.println("sendEmailAlert() groupInformation3=" + groupInformation3);
		//			for (String emailAddress : emailAddressArray) {
		//				System.out.println("sendEmailAlert() emailAddress=" + emailAddress);
		//			}
		//			if (contentGuardAlertSummaryList != null && contentGuardAlertSummaryList.size() > 0) {
		//				for (ContentGuardAlertSummary contentGuardAlertSummary : contentGuardAlertSummaryList) {
		//					System.out.println("sendEmailAlert() summary changeIndicatorDesc=" + contentGuardAlertSummary.getChangeIndicatorDesc());
		//					for (ContentGuardAlertDetails contentGuardAlertDetails : contentGuardAlertSummary.getContentGuardAlertDetailsList()) {
		//						System.out.println("sendEmailAlert() details url=" + contentGuardAlertDetails.getUrl());
		//						if (StringUtils.isNotBlank(contentGuardAlertDetails.getPreviousContent())) {
		//							System.out.println("sendEmailAlert() details previous content=" + contentGuardAlertDetails.getPreviousContent());
		//						}
		//						if (StringUtils.isNotBlank(contentGuardAlertDetails.getCurrentContent())) {
		//							System.out.println("sendEmailAlert() details current content=" + contentGuardAlertDetails.getCurrentContent());
		//						}
		//					}
		//				}
		//			}
		//		}

		AgencyInfoEntity agencyInfoEntity = agencyInfoService.getByDomainId(1701);
		while (retryCount < IConstants.MAX_SEND_EMAIL_RETRY_COUNT) {
			try {
				emailSenderComponent.sendMimeMultiPartZeptoMailAndBcc(emailAddressArray, null, emailSubject, "mail_content_guard_alert.txt",
						"mail_content_guard_alert.html", map, agencyInfoEntity);
				retryCount = IConstants.MAX_SEND_EMAIL_RETRY_COUNT;
			} catch (Exception e) {
				e.printStackTrace();
				retryCount++;
				if (retryCount >= IConstants.MAX_SEND_EMAIL_RETRY_COUNT) {
					throw e;
				} else {
					FormatUtils.getInstance()
							.logMemoryUsage("sendEmailAlert() ip=" + ip + ",domainId=" + domainId + ",groupName=" + groupName + ",retryCount=" + retryCount);
					try {
						Thread.sleep(IConstants.RETRY_WAIT_TIME_IN_MILLISECONDS);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}

		//		try {
		//			// when webhook to 'slack' is required
		//			webhookEntity = webhookDAO.get(domainId, IConstants.WEBBOOK_TYPE_SLACK, IConstants.WEBBOOK_ALERT_TYPE_CONTENT_GUARD);
		//			if (webhookEntity != null) {
		//				slackService.sendEmailAlert(domainId, emailSubject, groupInformation1, groupInformation2, groupInformation3, contentGuardAlertSummaryList,
		//						webhookEntity.getEndpoint());
		//			}
		//		} catch (Exception e) {
		//			FormatUtils.getInstance().logMemoryUsage("sendEmailAlert() error--ip=" + ip + ",domainId=" + domainId + ",groupName=" + groupName
		//					+ ",slackService.getSendContentGuardAlert() exception Message=" + e.getMessage());
		//			e.printStackTrace();
		//		}

		FormatUtils.getInstance().logMemoryUsage("sendEmailAlert() ends. ip=" + ip + ",domainId=" + domainId + ",groupName=" + groupName);

	}
}
