package com.actonia.entity;

public class CompetitorUrlMd5Entity {
	private int domainId;
	private String hashCode;
	private int competitorUrlId;

	public int getDomainId() {
		return domainId;
	}

	public void setDomainId(int domainId) {
		this.domainId = domainId;
	}

	public String getHashCode() {
		return hashCode;
	}

	public void setHashCode(String hashCode) {
		this.hashCode = hashCode;
	}

	public int getCompetitorUrlId() {
		return competitorUrlId;
	}

	public void setCompetitorUrlId(int competitorUrlId) {
		this.competitorUrlId = competitorUrlId;
	}

	@Override
	public String toString() {
		return " CompetitorUrlMd5Entity [domainId=" + domainId + ", hashCode=" + hashCode + ", competitorUrlId=" + competitorUrlId + "]";
	}

}