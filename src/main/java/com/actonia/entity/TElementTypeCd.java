/**
 * 
 */
package com.actonia.entity;

import javax.persistence.Column;
import javax.persistence.Id;

/**
 * com.actonia.subserver.entity.TElementTypeCd.java
 * 
 * <AUTHOR>
 * 
 * @version $Revision: 125069 $ $Author: wangcee $
 */
public class TElementTypeCd {

    public static final int KEYWORD = 1;

    public static final int TRAGET_URL = 2;

    public static final int PARTNER = 3;

    public static final int PARTNER_URL = 4;

    public static final int COMPETITOR = 5;

    public static final int COMPETITOR_URL = 6;

    public static final int TAG = 7;

    public static final int DOMAIN = 8;

    public static final int KEYWORD_URL_ASSOCIATED = 9;

    public static final int AnchorText = 13;

    public static final int SEMRUSH_DETAIL_ID = 30;

    public static final int GOOGLE_WEB_MASTER_KEYWORD = 31;
    
    //Cee - https://www.wrike.com/open.htm?id=216175840
    public static final int AI_RULE = 32;

    public static final int SEMRUSH_KEYWORD_STR = 250;

    public static final int BACKLINK_ANCHOR_STR = 251;

    private static final long serialVersionUID = 1L;
    private int id;
    private String elementName;
    private String elementShortDesc;

    public TElementTypeCd() {
    }

    @Id
    public int getId() {
        return this.id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Column(name = "element_name")
    public String getElementName() {
        return this.elementName;
    }

    public void setElementName(String elementName) {
        this.elementName = elementName;
    }

    @Column(name = "element_short_desc")
    public String getElementShortDesc() {
        return this.elementShortDesc;
    }

    public void setElementShortDesc(String elementShortDesc) {
        this.elementShortDesc = elementShortDesc;
    }

}
