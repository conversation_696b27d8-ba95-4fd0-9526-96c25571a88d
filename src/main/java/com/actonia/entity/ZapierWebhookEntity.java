package com.actonia.entity;

import java.util.Date;

public class ZapierWebhookEntity {
	private Long id;
	private int domainId;
	private int triggerType;
	private int userId;
	private String subTypeHashCd;
	private String subType;
	private String callbackUrl;
	private Date createDate;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public int getDomainId() {
		return domainId;
	}

	public void setDomainId(int domainId) {
		this.domainId = domainId;
	}

	public int getTriggerType() {
		return triggerType;
	}

	public void setTriggerType(int triggerType) {
		this.triggerType = triggerType;
	}

	public int getUserId() {
		return userId;
	}

	public void setUserId(int userId) {
		this.userId = userId;
	}

	public String getSubTypeHashCd() {
		return subTypeHashCd;
	}

	public void setSubTypeHashCd(String subTypeHashCd) {
		this.subTypeHashCd = subTypeHashCd;
	}

	public String getSubType() {
		return subType;
	}

	public void setSubType(String subType) {
		this.subType = subType;
	}

	public String getCallbackUrl() {
		return callbackUrl;
	}

	public void setCallbackUrl(String callbackUrl) {
		this.callbackUrl = callbackUrl;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Override
	public String toString() {
		return "ZapierWebhookEntity [id=" + id + ", domainId=" + domainId + ", triggerType=" + triggerType + ", userId=" + userId + ", subTypeHashCd=" + subTypeHashCd
				+ ", subType=" + subType + ", callbackUrl=" + callbackUrl + ", createDate=" + createDate + "]";
	}

}