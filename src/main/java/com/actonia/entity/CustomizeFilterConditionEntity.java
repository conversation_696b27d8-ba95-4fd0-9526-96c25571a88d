package com.actonia.entity;

// Generated Dec 18, 2013 10:38:41 AM by Hibernate Tools 3.4.0.CR1

import static javax.persistence.GenerationType.IDENTITY;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;

import com.actonia.utils.FormatUtils;
import com.actonia.utils.RankCheckUtils;

/**
 * CustomizeFilterConditionEntity generated by hbm2java
 */
@Entity
@Table(name = "customize_filter_condition")
public class CustomizeFilterConditionEntity implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 8746398814621030741L;

	public static final int SQL_OPT_AND = 1;
	public static final int SQL_OPT_OR = 2;

	public static final int CONDITION_OPT_NUM_EQUAL = 1;
	public static final int CONDITION_OPT_NUM_NOT_EQUAL = 2;
	public static final int CONDITION_OPT_NUM_LESS = 3;
	public static final int CONDITION_OPT_NUM_GREATER = 4;

	// for "comparison" filtering conditions 
	public static final int CONDITION_OPT_VALUE_DECREASES_BY_MORE_THAN = 5;
	public static final int CONDITION_OPT_VALUE_INCREASES_BY_MORE_THAN = 6;
	public static final int CONDITION_OPT_PERCENT_DECREASES_BY_MORE_THAN = 7;
	public static final int CONDITION_OPT_PERCENT_INCREASES_BY_MORE_THAN = 8;

	public static final int CONDITION_OPT_STR_EQUAL = 10;
	public static final int CONDITION_OPT_STR_BEGINS_WITH = 11;
	public static final int CONDITION_OPT_STR_NOT_EQUAL = 12;
	public static final int CONDITION_OPT_STR_CONTAIN = 13;
	public static final int CONDITION_OPT_STR_NOT_CONTAIN = 14;

	private Integer id;
	private int ownDomainId;
	private int filterId;
	private int criteriaId;
	private Integer conditionOperator;
	private String conditionValue;
	private Integer conditionOrder;
	private Integer linkSqlOperator;
	private Integer searchEngineId;
	private Integer periodIndicator;

	public CustomizeFilterConditionEntity() {
	}

	public CustomizeFilterConditionEntity(int ownDomainId, int filterId, int criteriaId) {
		this.ownDomainId = ownDomainId;
		this.filterId = filterId;
		this.criteriaId = criteriaId;
	}

	public CustomizeFilterConditionEntity(int ownDomainId, int filterId, int criteriaId, Integer conditionOperator, String conditionValue, Integer conditionOrder,
			Integer linkSqlOperator, Integer searchEngineId, Integer periodIndicator) {
		this.ownDomainId = ownDomainId;
		this.filterId = filterId;
		this.criteriaId = criteriaId;
		this.conditionOperator = conditionOperator;
		this.conditionValue = conditionValue;
		this.conditionOrder = conditionOrder;
		this.linkSqlOperator = linkSqlOperator;
		this.searchEngineId = searchEngineId;
		this.periodIndicator = periodIndicator;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "own_domain_id", nullable = false)
	public int getOwnDomainId() {
		return this.ownDomainId;
	}

	public void setOwnDomainId(int ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	@Column(name = "filter_id", nullable = false)
	public int getFilterId() {
		return this.filterId;
	}

	public void setFilterId(int filterId) {
		this.filterId = filterId;
	}

	@Column(name = "criteria_id", nullable = false)
	public int getCriteriaId() {
		return this.criteriaId;
	}

	public void setCriteriaId(int criteriaId) {
		this.criteriaId = criteriaId;
	}

	@Column(name = "condition_operator")
	public Integer getConditionOperator() {
		return this.conditionOperator;
	}

	public void setConditionOperator(Integer conditionOperator) {
		this.conditionOperator = conditionOperator;
	}

	@Column(name = "condition_value", length = 512)
	public String getConditionValue() {
		return this.conditionValue;
	}

	public void setConditionValue(String conditionValue) {
		this.conditionValue = conditionValue;
	}

	@Column(name = "condition_order")
	public Integer getConditionOrder() {
		return this.conditionOrder;
	}

	public void setConditionOrder(Integer conditionOrder) {
		this.conditionOrder = conditionOrder;
	}

	@Column(name = "link_sql_operator")
	public Integer getLinkSqlOperator() {
		return this.linkSqlOperator;
	}

	public void setLinkSqlOperator(Integer linkSqlOperator) {
		this.linkSqlOperator = linkSqlOperator;
	}

	@Column(name = "search_engine_id")
	public Integer getSearchEngineId() {
		return this.searchEngineId;
	}

	public void setSearchEngineId(Integer searchEngineId) {
		this.searchEngineId = searchEngineId;
	}

	@Column(name = "period_indicator")
	public Integer getPeriodIndicator() {
		return this.periodIndicator;
	}

	public void setPeriodIndicator(Integer periodIndicator) {
		this.periodIndicator = periodIndicator;
	}

	@Transient
	public static String getConditionOpt(Integer conditionOption) {
		if (conditionOption == null) {
			return null;
		}
		if (conditionOption.intValue() == CONDITION_OPT_NUM_EQUAL) {
			return " = ? ";
		}
		if (conditionOption.intValue() == CONDITION_OPT_NUM_NOT_EQUAL) {
			return " != ? ";
		}
		if (conditionOption.intValue() == CONDITION_OPT_NUM_LESS) {
			return " < ? ";
		}
		if (conditionOption.intValue() == CONDITION_OPT_NUM_GREATER) {
			return " > ? ";
		}
		if (conditionOption.intValue() == CONDITION_OPT_STR_EQUAL) {
			return " = ? ";
		}
		if (conditionOption.intValue() == CONDITION_OPT_STR_BEGINS_WITH) {
			return " like ? ";
		}
		if (conditionOption.intValue() == CONDITION_OPT_STR_CONTAIN) {
			return " like ? ";
		}
		if (conditionOption.intValue() == CONDITION_OPT_STR_NOT_CONTAIN) {
			return " not like ? ";
		}
		if (conditionOption.intValue() == CONDITION_OPT_STR_NOT_EQUAL) {
			return " != ? ";
		}

		return null;
	}

	@Transient
	public static String getConditionOptDisplay(Integer conditionOption) {
		if (conditionOption == null) {
			return "";
		}
		if (conditionOption.intValue() == CONDITION_OPT_NUM_EQUAL) {
			return " = ? ";
		}
		if (conditionOption.intValue() == CONDITION_OPT_NUM_NOT_EQUAL) {
			return " != ? ";
		}
		if (conditionOption.intValue() == CONDITION_OPT_NUM_LESS) {
			return " < ? ";
		}
		if (conditionOption.intValue() == CONDITION_OPT_NUM_GREATER) {
			return " > ? ";
		}
		// comparison: decreases by more than
		if (conditionOption.intValue() == CONDITION_OPT_VALUE_DECREASES_BY_MORE_THAN) {
			return " decreases by more than ";
		}
		// comparison: increases by more than
		if (conditionOption.intValue() == CONDITION_OPT_VALUE_INCREASES_BY_MORE_THAN) {
			return " increases by more than ";
		}
		// comparison: % decreases by more than
		if (conditionOption.intValue() == CONDITION_OPT_PERCENT_DECREASES_BY_MORE_THAN) {
			return " % decreases by more than ";
		}
		// comparison: % increases by more than
		if (conditionOption.intValue() == CONDITION_OPT_PERCENT_INCREASES_BY_MORE_THAN) {
			return " % increases by more than ";
		}
		if (conditionOption.intValue() == CONDITION_OPT_STR_EQUAL) {
			return " equals ? ";
		}
		if (conditionOption.intValue() == CONDITION_OPT_STR_BEGINS_WITH) {
			return " starts with ? ";
		}
		if (conditionOption.intValue() == CONDITION_OPT_STR_CONTAIN) {
			return " contains ? ";
		}
		if (conditionOption.intValue() == CONDITION_OPT_STR_NOT_CONTAIN) {
			return " does not contain ? ";
		}
		if (conditionOption.intValue() == CONDITION_OPT_STR_NOT_EQUAL) {
			return " does not equal ? ";
		}
		return "";
	}

	@Transient
	public Object getConditionValue(Integer criteriaValueType, int elementType) {
		if (criteriaValueType == null) {
			return null;
		}
		if (criteriaValueType.intValue() == CustomizeFilterCriteriaEntity.VALUE_INT) {
			return NumberUtils.toInt(conditionValue);
		}
		if (criteriaValueType.intValue() == CustomizeFilterCriteriaEntity.VALUE_LONG) {
			return NumberUtils.toLong(conditionValue);
		}
		if (criteriaValueType.intValue() == CustomizeFilterCriteriaEntity.VALUE_FLOAT) {
			return NumberUtils.toFloat(conditionValue);
		}
		if (criteriaValueType.intValue() == CustomizeFilterCriteriaEntity.VALUE_PERCENTAGE) {
			return NumberUtils.toFloat(conditionValue);
		}
		if (criteriaValueType.intValue() == CustomizeFilterCriteriaEntity.VALUE_STRING) {
			if (conditionOperator.intValue() == CONDITION_OPT_STR_EQUAL) {
				return conditionValue;
			}
			if (conditionOperator.intValue() == CONDITION_OPT_STR_BEGINS_WITH) {
				if (elementType == TElementTypeCd.KEYWORD) {
					return encodeKeyword(conditionValue) + "%";
				} else {
					return conditionValue + "%";
				}
			}
			if (conditionOperator.intValue() == CONDITION_OPT_STR_CONTAIN || conditionOperator.intValue() == CONDITION_OPT_STR_NOT_CONTAIN) {

				if (elementType == TElementTypeCd.KEYWORD) {
					return "%" + encodeKeyword(conditionValue) + "%";
				} else {
					return "%" + conditionValue + "%";
				}
			}
		}

		//filter by Tag names contains
		if (criteriaValueType.intValue() == CustomizeFilterCriteriaEntity.VALUE_MULTI_STRING) {
			if (conditionOperator.intValue() == CONDITION_OPT_STR_EQUAL) {
				return "%" + CustomizeFilterCriteriaEntity.MULTI_STRING_SEPARATOR + conditionValue + CustomizeFilterCriteriaEntity.MULTI_STRING_SEPARATOR + "%";
			}
			if (conditionOperator.intValue() == CONDITION_OPT_STR_BEGINS_WITH) {
				return CustomizeFilterCriteriaEntity.MULTI_STRING_SEPARATOR + conditionValue + "%";
			}
			if (conditionOperator.intValue() == CONDITION_OPT_STR_CONTAIN || conditionOperator.intValue() == CONDITION_OPT_STR_NOT_CONTAIN) {

				return "%" + conditionValue + "%";
			}
		}

		if (criteriaValueType.intValue() == CustomizeFilterCriteriaEntity.VALUE_DATE) {
			//TODO
			return new Date();
		}
		return null;
	}

	public static String encodeKeyword(String keyword) {
		if (StringUtils.isBlank(keyword)) {
			return "";
		}
		try {
			return URLEncoder.encode(StringUtils.trim(keyword), "UTF-8");
		} catch (UnsupportedEncodingException e) {
		}
		return StringUtils.replace(keyword, " ", "+");
	}

	public static Object getConditionValue(Integer criteriaValueType, int elementType, Integer conditionOperator, String conditionValue) {
		if (criteriaValueType == null) {
			return null;
		}
		if (criteriaValueType.intValue() == CustomizeFilterCriteriaEntity.VALUE_INT) {
			return NumberUtils.toInt(conditionValue);
		}
		if (criteriaValueType.intValue() == CustomizeFilterCriteriaEntity.VALUE_LONG) {
			return NumberUtils.toLong(conditionValue);
		}
		if (criteriaValueType.intValue() == CustomizeFilterCriteriaEntity.VALUE_FLOAT) {
			return NumberUtils.toFloat(conditionValue);
		}
		if (criteriaValueType.intValue() == CustomizeFilterCriteriaEntity.VALUE_STRING) {
			if (conditionOperator.intValue() == CONDITION_OPT_STR_EQUAL) {
				return conditionValue;
			}
			if (conditionOperator.intValue() == CONDITION_OPT_STR_BEGINS_WITH) {
				if (elementType == TElementTypeCd.KEYWORD) { //Keyword
					return RankCheckUtils.encodeKeyword(conditionValue) + "%";
				} else {
					return conditionValue + "%";
				}
			}
			if (conditionOperator.intValue() == CONDITION_OPT_STR_CONTAIN || conditionOperator.intValue() == CONDITION_OPT_STR_NOT_CONTAIN) {

				if (elementType == TElementTypeCd.KEYWORD) {
					return "%" + RankCheckUtils.encodeKeyword(conditionValue) + "%";
				} else {
					return "%" + conditionValue + "%";
				}
			}
		}
		if (criteriaValueType.intValue() == CustomizeFilterCriteriaEntity.VALUE_DATE) {
			//TODO
			return new Date();
		}
		return null;
	}

	@Transient
	public Object getConditionValue(Integer criteriaValueType, int elementType, String columnName) {
		if (criteriaValueType == null) {
			return null;
		}
		if (criteriaValueType.intValue() == CustomizeFilterCriteriaEntity.VALUE_INT) {
			return NumberUtils.toInt(conditionValue);
		}
		if (criteriaValueType.intValue() == CustomizeFilterCriteriaEntity.VALUE_LONG) {
			return NumberUtils.toLong(conditionValue);
		}
		if (criteriaValueType.intValue() == CustomizeFilterCriteriaEntity.VALUE_FLOAT) {
			return NumberUtils.toFloat(conditionValue);
		}
		if (criteriaValueType.intValue() == CustomizeFilterCriteriaEntity.VALUE_PERCENTAGE) {
			return NumberUtils.toFloat(conditionValue);
		}
		if (criteriaValueType.intValue() == CustomizeFilterCriteriaEntity.VALUE_STRING) {
			if (conditionOperator.intValue() == CONDITION_OPT_STR_EQUAL) {
				if (elementType == TElementTypeCd.KEYWORD) {
					return RankCheckUtils.encodeKeyword(conditionValue);
				} else if (elementType == TElementTypeCd.KEYWORD_URL_ASSOCIATED && StringUtils.equalsIgnoreCase("keyword_name", columnName)) {
					//https://www.wrike.com/open.htm?id=6905145
					return RankCheckUtils.encodeKeyword(conditionValue);
				} else {
					return conditionValue;
				}
			}
			if (conditionOperator.intValue() == CONDITION_OPT_STR_BEGINS_WITH) {
				if (elementType == TElementTypeCd.KEYWORD) {
					return RankCheckUtils.encodeKeyword(conditionValue) + "%";
				} else if (elementType == TElementTypeCd.KEYWORD_URL_ASSOCIATED && StringUtils.equalsIgnoreCase("keyword_name", columnName)) {
					//https://www.wrike.com/open.htm?id=6905145
					return RankCheckUtils.encodeKeyword(conditionValue) + "%";
				} else {
					return conditionValue + "%";
				}
			}
			if (conditionOperator.intValue() == CONDITION_OPT_STR_CONTAIN || conditionOperator.intValue() == CONDITION_OPT_STR_NOT_CONTAIN) {

				if (elementType == TElementTypeCd.KEYWORD) {
					return "%" + RankCheckUtils.encodeKeyword(conditionValue) + "%";
				} else if (elementType == TElementTypeCd.KEYWORD_URL_ASSOCIATED && StringUtils.equalsIgnoreCase("keyword_name", columnName)) {
					//https://www.wrike.com/open.htm?id=6905145
					return "%" + RankCheckUtils.encodeKeyword(conditionValue) + "%";
				} else {
					return "%" + conditionValue + "%";
				}
			}
		}

		//filter by Tag names contains
		if (criteriaValueType.intValue() == CustomizeFilterCriteriaEntity.VALUE_MULTI_STRING) {
			if (conditionOperator.intValue() == CONDITION_OPT_STR_EQUAL) {
				return "%" + CustomizeFilterCriteriaEntity.MULTI_STRING_SEPARATOR + conditionValue + CustomizeFilterCriteriaEntity.MULTI_STRING_SEPARATOR + "%";
			}
			if (conditionOperator.intValue() == CONDITION_OPT_STR_BEGINS_WITH) {
				return CustomizeFilterCriteriaEntity.MULTI_STRING_SEPARATOR + conditionValue + "%";
			}
			if (conditionOperator.intValue() == CONDITION_OPT_STR_CONTAIN || conditionOperator.intValue() == CONDITION_OPT_STR_NOT_CONTAIN) {

				return "%" + conditionValue + "%";
			}
		}

		if (criteriaValueType.intValue() == CustomizeFilterCriteriaEntity.VALUE_DATE) {
			//TODO
			return new Date();
		}
		return null;
	}

	@Override
	public String toString() {
		return " CustomizeFilterConditionEntity [id=" + id + ", ownDomainId=" + ownDomainId + ", filterId=" + filterId + ", criteriaId=" + criteriaId
				+ ", conditionOperator=" + conditionOperator + ", conditionValue=" + conditionValue + ", conditionOrder=" + conditionOrder + ", linkSqlOperator="
				+ linkSqlOperator + ", searchEngineId=" + searchEngineId + ", periodIndicator=" + periodIndicator + "]";
	}

	@Transient
	public static String getConditionOptionRlike(Integer conditionOption) {
		if (conditionOption == null) {
			return null;
		}
		if (conditionOption.intValue() == CONDITION_OPT_NUM_EQUAL) {
			return " = ? ";
		}
		if (conditionOption.intValue() == CONDITION_OPT_NUM_NOT_EQUAL) {
			return " != ? ";
		}
		if (conditionOption.intValue() == CONDITION_OPT_NUM_LESS) {
			return " < ? ";
		}
		if (conditionOption.intValue() == CONDITION_OPT_NUM_GREATER) {
			return " > ? ";
		}
		if (conditionOption.intValue() == CONDITION_OPT_STR_EQUAL) {
			return " = ? ";
		}
		if (conditionOption.intValue() == CONDITION_OPT_STR_BEGINS_WITH) {
			return " RLIKE ? ";
		}
		if (conditionOption.intValue() == CONDITION_OPT_STR_CONTAIN) {
			return " RLIKE ? ";
		}
		if (conditionOption.intValue() == CONDITION_OPT_STR_NOT_CONTAIN) {
			return " NOT RLIKE ? ";
		}
		if (conditionOption.intValue() == CONDITION_OPT_STR_NOT_EQUAL) {
			return " != ? ";
		}

		return null;
	}

	@Transient
	public Object getConditionValueRlike(Integer criteriaValueType, int elementType, String columnName) {
		if (criteriaValueType == null) {
			return null;
		}
		if (criteriaValueType.intValue() == CustomizeFilterCriteriaEntity.VALUE_INT) {
			return NumberUtils.toInt(conditionValue);
		}
		if (criteriaValueType.intValue() == CustomizeFilterCriteriaEntity.VALUE_LONG) {
			return NumberUtils.toLong(conditionValue);
		}
		if (criteriaValueType.intValue() == CustomizeFilterCriteriaEntity.VALUE_FLOAT) {
			return NumberUtils.toFloat(conditionValue);
		}
		if (criteriaValueType.intValue() == CustomizeFilterCriteriaEntity.VALUE_PERCENTAGE) {
			return NumberUtils.toFloat(conditionValue);
		}
		if (criteriaValueType.intValue() == CustomizeFilterCriteriaEntity.VALUE_STRING) {
			if (conditionOperator.intValue() == CONDITION_OPT_STR_EQUAL) {
				if (elementType == TElementTypeCd.PARTNER_URL) {
					if (StringUtils.equalsIgnoreCase(columnName, "usr_backlink.anchor_txt")) {
						return RankCheckUtils.encodeKeyword(conditionValue);
					} else {
						return conditionValue;
					}
				} else if (elementType == TElementTypeCd.KEYWORD) {
					return RankCheckUtils.encodeKeyword(conditionValue);
				} else if (elementType == TElementTypeCd.KEYWORD_URL_ASSOCIATED && StringUtils.equalsIgnoreCase("keyword_name", columnName)) {
					//https://www.wrike.com/open.htm?id=6905145
					return RankCheckUtils.encodeKeyword(conditionValue);
				} else {
					return conditionValue;
				}
			}
			if (conditionOperator.intValue() == CONDITION_OPT_STR_BEGINS_WITH) {
				if (elementType == TElementTypeCd.PARTNER_URL) {
					if (StringUtils.equalsIgnoreCase(columnName, "usr_backlink.anchor_txt")) {
						return "^" + FormatUtils.getInstance().escapeMetaCharacters(RankCheckUtils.encodeKeyword(conditionValue));
					} else {
						return "^" + conditionValue;
					}
				} else if (elementType == TElementTypeCd.KEYWORD) {
					return "^" + RankCheckUtils.encodeKeyword(conditionValue);
				} else if (elementType == TElementTypeCd.KEYWORD_URL_ASSOCIATED && StringUtils.equalsIgnoreCase("keyword_name", columnName)) {
					//https://www.wrike.com/open.htm?id=6905145
					return "^" + RankCheckUtils.encodeKeyword(conditionValue);
				} else {
					return "^" + conditionValue;
				}
			}
			if (conditionOperator.intValue() == CONDITION_OPT_STR_CONTAIN || conditionOperator.intValue() == CONDITION_OPT_STR_NOT_CONTAIN) {
				if (elementType == TElementTypeCd.PARTNER_URL) {
					if (StringUtils.equalsIgnoreCase(columnName, "usr_backlink.anchor_txt")) {
						return FormatUtils.getInstance().escapeMetaCharacters(RankCheckUtils.encodeKeyword(conditionValue));
					} else {
						return conditionValue;
					}
				} else if (elementType == TElementTypeCd.KEYWORD) {
					return RankCheckUtils.encodeKeyword(conditionValue);
				} else if (elementType == TElementTypeCd.KEYWORD_URL_ASSOCIATED && StringUtils.equalsIgnoreCase("keyword_name", columnName)) {
					//https://www.wrike.com/open.htm?id=6905145
					return RankCheckUtils.encodeKeyword(conditionValue);
				} else {
					return conditionValue;
				}
			}
		}

		if (criteriaValueType.intValue() == CustomizeFilterCriteriaEntity.VALUE_DATE) {
			//TODO
			return new Date();
		}
		return null;
	}
}
