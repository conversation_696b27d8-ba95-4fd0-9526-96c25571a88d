/**
 * 
 */
package com.actonia.entity;

import static javax.persistence.GenerationType.IDENTITY;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

/**
 * com.actonia.saas.model.ElementMappingPatternEntity.java
 *
 * <AUTHOR>
 *
 * @version $Revision: 6823 $
 *          $Author: wangc@SHINETECHCHINA $
 */
@Entity
@Table(name = "element_mapping_pattern_entity")
public class ElementMappingPatternEntity {
	
	public static final int SCRIPT_PARTNER_URL_CRAWLER = 2;
	
	public static final int SCRIPT_CRAWL_STIE = 4;
	
	public static final int SCRIPT_ANALYTICS_SCRIPT = 5;
	
	public static final int SCRIPT_PPC_SCRIPT = 6;
	
	public static final int SCRIPT_PATTERN_MATCH = 8; // https://www.wrike.com/open.htm?id=39468129

	public static final int PROCESS_TYPE_IGNORE = 1;
	
	public static final int PROCESS_TYPE_STRIP = 2;
	
	public static final int PROCESS_TYPE_MATCH = 3;
	
	public static final int REGULAR_EXP_STRING = 0;
	
	public static final int REGULAR_EXP_JAVA_REGEX = 1;
	
	public static final int REGULAR_EXP_JS_REGEX = 2;
	
	public static final int CASE_SENSITIVE_YES = 1;
	
	public static final int CASE_SENSITIVE_NO = 0;

	private Integer id;
	
	private Integer ownDomainId;
	
	private Integer elementType;
	
	private Integer processType;
	
	private Integer processScript;
	
	private String patternStr; 
	
	private Integer regularExpression;
	
	private Integer caseSensitive;
	
	private Date createDate;
	
	private Integer createUser;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "own_domain_id")
	public Integer getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	@Column(name = "element_type")
	public Integer getElementType() {
		return elementType;
	}

	public void setElementType(Integer elementType) {
		this.elementType = elementType;
	}

	@Column(name = "process_type")
	public Integer getProcessType() {
		return processType;
	}

	public void setProcessType(Integer processType) {
		this.processType = processType;
	}

	@Column(name = "process_script")
	public Integer getProcessScript() {
		return processScript;
	}

	public void setProcessScript(Integer processScript) {
		this.processScript = processScript;
	}

	@Column(name = "pattern_str")
	public String getPatternStr() {
		return patternStr;
	}

	public void setPatternStr(String patternStr) {
		this.patternStr = patternStr;
	}

	@Column(name = "regular_expression")
	public Integer getRegularExpression() {
		return regularExpression;
	}

	public void setRegularExpression(Integer regularExpression) {
		this.regularExpression = regularExpression;
	}

	@Column(name = "case_sensitive")
	public Integer getCaseSensitive() {
		return caseSensitive;
	}

	public void setCaseSensitive(Integer caseSensitive) {
		this.caseSensitive = caseSensitive;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "create_date")
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "create_user")
	public Integer getCreateUser() {
		return createUser;
	}

	public void setCreateUser(Integer createUser) {
		this.createUser = createUser;
	}
	
	@Transient
	public boolean isRegularExpressionJavaRegex() {
		if (regularExpression == null) {
			return false;
		}
		return regularExpression.intValue() == REGULAR_EXP_JAVA_REGEX;
	}
	
	@Transient
	public boolean isRegularExpressionJsRegex() {
		if (regularExpression == null) {
			return false;
		}
		return regularExpression.intValue() == REGULAR_EXP_JS_REGEX;
	}
	
	@Transient
	public boolean isCaseSensitiveYes() {
		if (caseSensitive == null) {
			return false;
		}
		return caseSensitive.intValue() == CASE_SENSITIVE_YES;
	}

	@Override
	public String toString() {
		return "ElementMappingPatternEntity [id=" + id + ", ownDomainId=" + ownDomainId + ", elementType=" + elementType + ", processType=" + processType
				+ ", processScript=" + processScript + ", patternStr=" + patternStr + ", regularExpression=" + regularExpression + ", caseSensitive=" + caseSensitive
				+ ", createDate=" + createDate + ", createUser=" + createUser + "]";
	}

}
