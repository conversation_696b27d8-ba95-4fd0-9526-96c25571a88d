package com.actonia.entity;

import lombok.Data;

import java.util.Date;

@Data
public class TargetUrlChangeAlertEntity implements Cloneable {
	private Long id;
	private int domainId;
	/**
	 * 1-daily,2-hourly
	 */
	private int alertFrequency;
	/**
	 * 1-all,2-critical,3-custom
	 */
	private int indicatorFlag;
	private String customIndicators;
	private String emails;
	private String pageTagIds;
	private Date updateTimestamp;
	/**
	 * 1-detail,2-summary
	 */
	private Integer detailInd;
	private String friendlyName;

	@Override
	public TargetUrlChangeAlertEntity clone() throws CloneNotSupportedException {
		return (TargetUrlChangeAlertEntity) super.clone();
	}

}