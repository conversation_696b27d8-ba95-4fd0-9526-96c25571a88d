package com.actonia.entity;

public class AssociatedCompetitorUrlEntity {
	private int domainId;
	private Long keywordId;
	private String competitorUrlHashCode;
	private int createAssociationOnlyInd;
	private String competitorUrl;
	private String keywordName;
	private int processDate;
	private int competitorUrlId;

	public int getDomainId() {
		return domainId;
	}

	public void setDomainId(int domainId) {
		this.domainId = domainId;
	}

	public Long getKeywordId() {
		return keywordId;
	}

	public void setKeywordId(Long keywordId) {
		this.keywordId = keywordId;
	}

	public String getCompetitorUrlHashCode() {
		return competitorUrlHashCode;
	}

	public void setCompetitorUrlHashCode(String competitorUrlHashCode) {
		this.competitorUrlHashCode = competitorUrlHashCode;
	}

	public int getCreateAssociationOnlyInd() {
		return createAssociationOnlyInd;
	}

	public void setCreateAssociationOnlyInd(int createAssociationOnlyInd) {
		this.createAssociationOnlyInd = createAssociationOnlyInd;
	}

	public String getCompetitorUrl() {
		return competitorUrl;
	}

	public void setCompetitorUrl(String competitorUrl) {
		this.competitorUrl = competitorUrl;
	}

	public String getKeywordName() {
		return keywordName;
	}

	public void setKeywordName(String keywordName) {
		this.keywordName = keywordName;
	}

	public int getCompetitorUrlId() {
		return competitorUrlId;
	}

	public void setCompetitorUrlId(int competitorUrlId) {
		this.competitorUrlId = competitorUrlId;
	}

	public int getProcessDate() {
		return processDate;
	}

	public void setProcessDate(int processDate) {
		this.processDate = processDate;
	}

	@Override
	public String toString() {
		return " AssociatedCompetitorUrlEntity [domainId=" + domainId + ", keywordId=" + keywordId + ", competitorUrlHashCode="
				+ competitorUrlHashCode + ", createAssociationOnlyInd=" + createAssociationOnlyInd + ", competitorUrl=" + competitorUrl
				+ ", keywordName=" + keywordName + ", processDate=" + processDate + ", competitorUrlId=" + competitorUrlId
				+ "]";
	}

}