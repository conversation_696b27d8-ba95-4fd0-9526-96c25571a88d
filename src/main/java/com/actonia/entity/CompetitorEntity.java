package com.actonia.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CompetitorEntity {
	public static final int UN_CHECKED_GOOGEL = 0;
	public static final int CHECKED_GOOGEL = 1;

	public static final int ADD_BY_USER = 1;

	public static final int ADD_BY_TOP_COMPETITOR = 10;
	
	public static final int ADD_BY_RANKED_COMPETITOR_URL_ASSOCIATION = 88;

	private int id;

	private String domain;

	private int owndomainId;

	private Integer universalCompetitor;

	private Date createDate;

	private Integer addBy;

	private Date lastMajesticDomainProcessDate;

	private Date lastMajesticUrlProcessDate;
	
	private List<Integer> groupTagIds;
	
	private String name;

	private Integer checkGoogle;
	
	public List<Integer> getGroupTagIds() {
		if(groupTagIds == null) {
			groupTagIds = new ArrayList<Integer>();
		}
		return groupTagIds;
	}

	public void setGroupTagIds(List<Integer> groupTagIds) {
		this.groupTagIds = groupTagIds;
	}

	public Integer getUniversalCompetitor() {
		return universalCompetitor;
	}

	public void setUniversalCompetitor(Integer universalCompetitor) {
		this.universalCompetitor = universalCompetitor;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getDomain() {
		return domain;
	}

	public void setDomain(String domain) {
		this.domain = domain;
	}

	public int getOwndomainId() {
		return owndomainId;
	}

	public void setOwndomainId(int owndomainId) {
		this.owndomainId = owndomainId;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getAddBy() {
		return addBy;
	}

	public void setAddBy(Integer addBy) {
		this.addBy = addBy;
	}

    @Override
    public String toString() {
        return "CompetitorEntity [id=" + id
        		+ ", domain=" + domain
        		+ ", name=" + name
                + ", createDate=" + createDate
                + ", owndomainId=" + owndomainId
                + ", universalCompetitor=" + universalCompetitor
                + ", checkGoogle=" + checkGoogle        		
                + ", addBy=" + addBy
                + "]";
    }

	public Date getLastMajesticDomainProcessDate() {
		return lastMajesticDomainProcessDate;
	}

	public void setLastMajesticDomainProcessDate(Date lastMajesticDomainProcessDate) {
		this.lastMajesticDomainProcessDate = lastMajesticDomainProcessDate;
	}

	public Date getLastMajesticUrlProcessDate() {
		return lastMajesticUrlProcessDate;
	}

	public void setLastMajesticUrlProcessDate(Date lastMajesticUrlProcessDate) {
		this.lastMajesticUrlProcessDate = lastMajesticUrlProcessDate;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Integer getCheckGoogle() {
		return checkGoogle;
	}

	public void setCheckGoogle(Integer checkGoogle) {
		this.checkGoogle = checkGoogle;
	}
}
