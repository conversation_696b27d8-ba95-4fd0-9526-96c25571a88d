package com.actonia.entity;

import java.util.Date;

public class CompetitorUrlWeeklyCrawlTrackingEntity {
	private int processType;
	private String queueName;
	private String competitorDomain;
	private int totalUrls;
	private int concurrentRequests;
	private Date lastUpdateTimestamp;

	public int getProcessType() {
		return processType;
	}

	public void setProcessType(int processType) {
		this.processType = processType;
	}

	public String getCompetitorDomain() {
		return competitorDomain;
	}

	public void setCompetitorDomain(String competitorDomain) {
		this.competitorDomain = competitorDomain;
	}

	public String getQueueName() {
		return queueName;
	}

	public void setQueueName(String queueName) {
		this.queueName = queueName;
	}

	public int getTotalUrls() {
		return totalUrls;
	}

	public void setTotalUrls(int totalUrls) {
		this.totalUrls = totalUrls;
	}

	public int getConcurrentRequests() {
		return concurrentRequests;
	}

	public void setConcurrentRequests(int concurrentRequests) {
		this.concurrentRequests = concurrentRequests;
	}

	public Date getLastUpdateTimestamp() {
		return lastUpdateTimestamp;
	}

	public void setLastUpdateTimestamp(Date lastUpdateTimestamp) {
		this.lastUpdateTimestamp = lastUpdateTimestamp;
	}

	@Override
	public String toString() {
		return "CompetitorUrlWeeklyCrawlTrackingEntity [processType=" + processType + ", competitorDomain=" + competitorDomain + ", queueName=" + queueName
				+ ", totalUrls=" + totalUrls + ", concurrentRequests=" + concurrentRequests + ", lastUpdateTimestamp=" + lastUpdateTimestamp + "]";
	}

}