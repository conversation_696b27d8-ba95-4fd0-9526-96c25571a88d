package com.actonia.entity;

import java.time.LocalDateTime;

public class PoliteCrawlDomainSetting {
    private Integer id;
    private Integer ownDomainId;
    /**
     * 'to enable/disable polite crawl(0: disabled, 1: enabled, 2: paused)
     */
    private EnabledStatus enabled;
    private Integer frequence;
    private int enableRankingUrl;
    private int topxRankingUrl;
    private LocalDateTime createDate;

    public PoliteCrawlDomainSetting(Integer id, Integer ownDomainId, EnabledStatus enabled, Integer frequence, LocalDateTime createDate) {
        this.id = id;
        this.ownDomainId = ownDomainId;
        this.enabled = enabled;
        this.frequence = frequence;
        this.createDate = createDate;
    }

    public PoliteCrawlDomainSetting() {
        // no-argument constructor
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOwnDomainId() {
        return ownDomainId;
    }

    public void setOwnDomainId(Integer ownDomainId) {
        this.ownDomainId = ownDomainId;
    }

    public EnabledStatus getEnabled() {
        return enabled;
    }

    public void setEnabled(EnabledStatus enabled) {
        this.enabled = enabled;
    }

    public Integer getFrequence() {
        return frequence;
    }

    public void setFrequence(Integer frequence) {
        this.frequence = frequence;
    }

    public int getEnableRankingUrl() {
        return enableRankingUrl;
    }

    public void setEnableRankingUrl(int enableRankingUrl) {
        this.enableRankingUrl = enableRankingUrl;
    }

    public int getTopxRankingUrl() {
        return topxRankingUrl;
    }

    public void setTopxRankingUrl(int topxRankingUrl) {
        this.topxRankingUrl = topxRankingUrl;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public enum EnabledStatus {
        DISABLED(0),
        ENABLED(1),
        PAUSED(2);

        private final Integer value;

        EnabledStatus(Integer value) {
            this.value = value;
        }

        public static EnabledStatus convertByDomainStatus(Integer value) {
            switch (value) {
                case 1:
                    return ENABLED;
                case 2:
                    return PAUSED;
                case 0:
                default:
                    return DISABLED;
            }
        }

        public Integer getValue() {
            return value;
        }

        public EnabledStatus getEnum(Integer value) {
            for (EnabledStatus e : values()) {
                if (e.getValue().equals(value)) {
                    return e;
                }
            }
            return null;
        }
    }
}
