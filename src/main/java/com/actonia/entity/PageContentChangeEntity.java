package com.actonia.entity;

public class PageContentChangeEntity {
	private int domainId;
	private int changeDate;
	private Long targetUrlId;

	// 1 = title
	// 2 = meta desc
	// 3 = meta keywords
	// 4 = number of h1
	// 5 = content of h1
	// 6 = number of h2
	// 7 = content of h2
	// 8 = div id content
	// 9 = div class content
	// 10 = response code
	private int changeType;

	private Integer diff;
	private String respCodeChange;

	private Integer groupTagId;
	private Integer selectorType;
	private String selector;

	public int getDomainId() {
		return domainId;
	}

	public void setDomainId(int domainId) {
		this.domainId = domainId;
	}

	public int getChangeDate() {
		return changeDate;
	}

	public void setChangeDate(int changeDate) {
		this.changeDate = changeDate;
	}

	public Long getTargetUrlId() {
		return targetUrlId;
	}

	public void setTargetUrlId(Long targetUrlId) {
		this.targetUrlId = targetUrlId;
	}

	public int getChangeType() {
		return changeType;
	}

	public void setChangeType(int changeType) {
		this.changeType = changeType;
	}

	public Integer getDiff() {
		return diff;
	}

	public void setDiff(Integer diff) {
		this.diff = diff;
	}

	public String getRespCodeChange() {
		return respCodeChange;
	}

	public void setRespCodeChange(String respCodeChange) {
		this.respCodeChange = respCodeChange;
	}

	public Integer getGroupTagId() {
		return groupTagId;
	}

	public void setGroupTagId(Integer groupTagId) {
		this.groupTagId = groupTagId;
	}

	public Integer getSelectorType() {
		return selectorType;
	}

	public void setSelectorType(Integer selectorType) {
		this.selectorType = selectorType;
	}

	public String getSelector() {
		return selector;
	}

	public void setSelector(String selector) {
		this.selector = selector;
	}

	@Override
	public String toString() {
		return "PageContentChangeEntity [domainId=" + domainId + ", changeDate=" + changeDate + ", targetUrlId=" + targetUrlId + ", changeType=" + changeType
				+ ", diff=" + diff + ", respCodeChange=" + respCodeChange + ", groupTagId=" + groupTagId + ", selectorType=" + selectorType + ", selector=" + selector
				+ "]";
	}

}