package com.actonia.entity;

import java.util.Date;

public class GoogleTagJavascriptUrlEntity {
	private Long id;
	private Long javascriptId;
	private String urlHashCd;
	private String url;
	private Date lastUpdateTimestamp;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getJavascriptId() {
		return javascriptId;
	}

	public void setJavascriptId(Long javascriptId) {
		this.javascriptId = javascriptId;
	}

	public String getUrlHashCd() {
		return urlHashCd;
	}

	public void setUrlHashCd(String urlHashCd) {
		this.urlHashCd = urlHashCd;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public Date getLastUpdateTimestamp() {
		return lastUpdateTimestamp;
	}

	public void setLastUpdateTimestamp(Date lastUpdateTimestamp) {
		this.lastUpdateTimestamp = lastUpdateTimestamp;
	}

	@Override
	public String toString() {
		return "GoogleTagJavascriptUrlEntity [id=" + id + ", javascriptId=" + javascriptId + ", urlHashCd=" + urlHashCd + ", url=" + url + ", lastUpdateTimestamp="
				+ lastUpdateTimestamp + "]";
	}

}
