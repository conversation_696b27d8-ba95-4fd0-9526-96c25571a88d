package com.actonia.entity;

import java.util.LinkedHashMap;
import java.util.Map;

public class ScLanguageEntity {

	public static final int SC_LANGUAGE_US_EN = 1;
	public static final int SC_LANGUAGE_US_ES = 2;
	public static final int SC_LANGUAGE_CA_EN = 3;
	public static final int SC_LANGUAGE_CA_FR = 4;
	public static final int SC_LANGUAGE_AU_EN = 5;
	public static final int SC_LANGUAGE_CN_ZH = 6;
	public static final int SC_LANGUAGE_FR_FR = 7;
	public static final int SC_LANGUAGE_UK_EN = 8;
	
	public static final int LANGUAGE_IT_IT = 9;
	public static final int LANGUAGE_DK_DA = 10;
	public static final int LANGUAGE_FI_FI = 11;
	public static final int LANGUAGE_MX_ES = 12;
	public static final int LANGUAGE_NO_NO = 13;
	public static final int LANGUAGE_SE_SV = 14;
	public static final int LANGUAGE_DE_DE = 15;
	public static final int LANGUAGE_BR_PT = 16;
	public static final int LANGUAGE_ES_ES = 17;
	public static final int LANGUAGE_NL_NL = 18;
	public static final int LANGUAGE_JP_JA = 19;
	public static final int LANGUAGE_PT_PT = 20;
	public static final int LANGUAGE_IE_EN = 21;
	public static final int LANGUAGE_BE_NL = 22;
	public static final int LANGUAGE_CH_DE = 23;
	public static final int LANGUAGE_KR_KO = 24;
	public static final int LANGUAGE_IN_EN = 25;
	public static final int LANGUAGE_AR_ES = 26;
	public static final int LANGUAGE_CL_ES = 27;
	public static final int LANGUAGE_CO_ES = 28;
	public static final int LANGUAGE_PR_ES = 29;
	public static final int LANGUAGE_AT_DE = 30;
	public static final int LANGUAGE_ID_ID = 31;
	public static final int LANGUAGE_MY_EN = 32;
	public static final int LANGUAGE_PH_TL = 33;
	public static final int LANGUAGE_TW_ZH = 34;
	public static final int LANGUAGE_TH_TH = 35;
	public static final int LANGUAGE_VN_VI = 36;
	public static final int LANGUAGE_SG_EN = 37;
	public static final int LANGUAGE_NZ_EN = 38;
	public static final int LANGUAGE_RU_RU = 39;
	public static final int LANGUAGE_CZ_CS = 40;
	public static final int LANGUAGE_HU_HU = 41;
	public static final int LANGUAGE_PL_PL = 42;
	public static final int LANGUAGE_SA_AR = 43;
	public static final int LANGUAGE_PE_ES = 44;
	public static final int LANGUAGE_AE_AR = 45;
	public static final int LANGUAGE_AE_EN = 46;
	public static final int LANGUAGE_TR_TR = 47;
	public static final int LANGUAGE_ZA_EN = 48;
	public static final int LANGUAGE_KE_EN = 49;
	public static final int LANGUAGE_SK_SK = 50;
	public static final int LANGUAGE_HE_IL = 51;
	public static final int LANGUAGE_ES_EC = 52;
	public static final int LANGUAGE_ES_VE = 53;
	public static final int LANGUAGE_ES_CR = 54;
	public static final int LANGUAGE_UK_UA = 55;
	public static final int LANGUAGE_ES_UY = 56;
	public static final int LANGUAGE_MV_EN = 57;
	public static final int LANGUAGE_DB_BN = 58;
	public static final int LANGUAGE_KH_KM = 59;
	public static final int LANGUAGE_FJ_EN = 60;
	public static final int LANGUAGE_WS_EN = 61;
	public static final int LANGUAGE_GR_EL = 62;
	public static final int LANGUAGE_NG_EN = 63;
	public static final int LANGUAGE_DO_ES = 64;
	//Tony - https://www.wrike.com/open.htm?id=123551271
	public static final int LANGUAGE_PK_PK = 65;
    public static final int LANGUAGE_LB_AR = 66;
	
    //Cee - https://www.wrike.com/open.htm?id=214451779
    public static final int LANGUAGE_PA_ES = 68;
	public static final int LANGUAGE_AR_JO = 69;
	public static final int LANGUAGE_RO_RO = 72;
	public static final int LANGUAGE_AL_SQ = 73;
	public static final int LANGUAGE_AZ_AZ = 74;
	public static final int LANGUAGE_BG_BG = 75;
	
	
	public static final int LANGUAGE_AR_BH = 76;
    public static final int LANGUAGE_EL_CY = 77;
    public static final int LANGUAGE_FR_DJ = 78;
    public static final int LANGUAGE_AR_EG = 79;
    public static final int LANGUAGE_AM_ET = 80;
    public static final int LANGUAGE_FR_GA = 81;
    public static final int LANGUAGE_KA_GE = 82;
    public static final int LANGUAGE_AR_KW = 83;
    public static final int LANGUAGE_AR_MA = 84;
    public static final int LANGUAGE_FR_MC = 85;
    public static final int LANGUAGE_MT_MT = 86;
    public static final int LANGUAGE_EN_MU = 87;
    public static final int LANGUAGE_AR_OM = 88;
    public static final int LANGUAGE_SR_RS = 89;
    public static final int LANGUAGE_FR_SC = 90;
    public static final int LANGUAGE_TG_TJ = 91;
    public static final int LANGUAGE_AR_TN = 92;
    public static final int LANGUAGE_SW_UG = 93;
    public static final int LANGUAGE_EN_SC = 94;
    //Leo - https://www.wrike.com/open.htm?id=151468121
    public static final int LANGUAGE_ES_SV = 95;
    //Leo - https://www.wrike.com/open.htm?id=162045003
    public static final int LANGUAGE_BE_FR = 96;
    public static final int LANGUAGE_CH_FR = 97;
    public static final int LANGUAGE_CN_EN = 98;
    public static final int LANGUAGE_TH_EN = 99;
    
    public static final int LANGUAGE_LU_LB = 100;
    
    //Cee - https://www.wrike.com/open.htm?id=163324308
    public static final int LANGUAGE_PH_EN = 101;
    
    //scott - https://www.wrike.com/open.htm?id=179555118
//  public static final int LANGUAGE_FI_SV = 102;
  	
  	//scott - https://www.wrike.com/open.htm?id=179555118
  	public static final int LANGUAGE_LU_FR = 103;
  	
  	//Sunny - https://www.wrike.com/open.htm?id=187691124
    public static final int LANGUAGE_RU_UK = 104;
    
    //Sunny - https://www.wrike.com/open.htm?id=194110395
    public static final int LANGUAGE_LT_LT = 105;
    
    //Cee - https://www.wrike.com/open.htm?id=203789852
    public static final int LANGUAGE_EN_KW = 106;
    public static final int LANGUAGE_EN_BH = 107;
    
    //Cee - https://www.wrike.com/open.htm?id=151728885
    public static final int LANGUAGE_MM_MY = 108;
    
  	
    //Cee - https://www.wrike.com/open.htm?id=228047120
    public static final int LANGUAGE_MY_MS = 109;

	// Edwin - https://www.wrike.com/open.htm?id=260889612
	public static final int LANGUAGE_EE_ET = 112;
	public static final int LANGUAGE_LV_LV = 113;
    
	//Cee - https://www.wrike.com/open.htm?id=349497633
	public static final int LANGUAGE_EE_RU = 116;
	public static final int LANGUAGE_LV_RU = 117;
	
  	//Cee - https://www.wrike.com/open.htm?id=180398013
  	public static final int LANGUAGE_GA_GA = 81;
	
  	//Mark - https://www.wrike.com/open.htm?id=172144671
  	public static final int LANGUAGE_BH_BH = 76;
  	//Mark - https://www.wrike.com/open.htm?id=180397949
  	public static final int LANGUAGE_MU_MU = 87;
  	//Mark - https://www.wrike.com/open.htm?id=180398675
  	public static final int LANGUAGE_PR_PR = 29;
  	//Mark - https://www.wrike.com/open.htm?id=172144814
  	public static final int LANGUAGE_DZ_DZ = 71;
	//Mark - https://www.wrike.com/open.htm?id=172144455
  	public static final int LANGUAGE_QA_QA = 67;
  	//Mark - https://www.wrike.com/open.htm?id=180398816
  	public static final int LANGUAGE_HR_HR = 70;
  	//Mark - https://www.wrike.com/open.htm?id=173591140
//	public static final int LANGUAGE_SK_SK = 50;
  	
  	//Cee - https://www.wrike.com/open.htm?id=205556043
  	public static Map<Integer, String> LANGUAGE_ALIAS_MAP = new LinkedHashMap<Integer, String>();
	static {
		LANGUAGE_ALIAS_MAP.put(LANGUAGE_TW_ZH, "TC");
		LANGUAGE_ALIAS_MAP.put(LANGUAGE_AE_AR, "AR");
		LANGUAGE_ALIAS_MAP.put(LANGUAGE_AR_KW, "AR");
		LANGUAGE_ALIAS_MAP.put(LANGUAGE_AR_BH, "AR");
		LANGUAGE_ALIAS_MAP.put(LANGUAGE_RU_RU, "RU");
		//Cee - https://www.wrike.com/open.htm?id=349497633
		LANGUAGE_ALIAS_MAP.put(LANGUAGE_EE_RU, "RU");
		LANGUAGE_ALIAS_MAP.put(LANGUAGE_LV_RU, "RU");
		LANGUAGE_ALIAS_MAP.put(LANGUAGE_EE_ET, "ET");
		LANGUAGE_ALIAS_MAP.put(LANGUAGE_LV_LV, "LV");
		
		LANGUAGE_ALIAS_MAP.put(SC_LANGUAGE_FR_FR, "FR");
		LANGUAGE_ALIAS_MAP.put(SC_LANGUAGE_UK_EN, "UK");
	}
  	
  	private Integer id;
	
	private String language;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getLanguage() {
		return language;
	}

	public void setLanguage(String language) {
		this.language = language;
	}
	
}
