package com.actonia.entity;

import java.util.Date;

public class TargetUrlClickHouseEntity {
	private Date track_date;
	private Integer domain_id;
	private String url;
	private String url_hash;
	private String url_murmur_hash;

	public Date getTrack_date() {
		return track_date;
	}

	public void setTrack_date(Date track_date) {
		this.track_date = track_date;
	}

	public Integer getDomain_id() {
		return domain_id;
	}

	public void setDomain_id(Integer domain_id) {
		this.domain_id = domain_id;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getUrl_hash() {
		return url_hash;
	}

	public void setUrl_hash(String url_hash) {
		this.url_hash = url_hash;
	}

	public String getUrl_murmur_hash() {
		return url_murmur_hash;
	}

	public void setUrl_murmur_hash(String url_murmur_hash) {
		this.url_murmur_hash = url_murmur_hash;
	}

	@Override
	public String toString() {
		return "TargetUrlClickHouseEntity [track_date=" + track_date + ", domain_id=" + domain_id + ", url=" + url + ", url_hash=" + url_hash + ", url_murmur_hash="
				+ url_murmur_hash + "]";
	}

}
