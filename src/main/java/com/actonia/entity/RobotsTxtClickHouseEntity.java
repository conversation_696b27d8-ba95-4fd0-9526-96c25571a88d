package com.actonia.entity;

import java.util.Date;

public class RobotsTxtClickHouseEntity {
	private Integer domainId;
	private String url;
	private Date trackDate;
	private String urlHash;
	private String urlMurmurHash;
	private Date crawlTimestamp;
	private String responseCode;
	private String content;
	private Date previousCrawlTimestamp;
	private Integer sign;

	public Integer getDomainId() {
		return domainId;
	}

	public void setDomainId(Integer domainId) {
		this.domainId = domainId;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public Date getTrackDate() {
		return trackDate;
	}

	public void setTrackDate(Date trackDate) {
		this.trackDate = trackDate;
	}

	public String getUrlHash() {
		return urlHash;
	}

	public void setUrlHash(String urlHash) {
		this.urlHash = urlHash;
	}

	public String getUrlMurmurHash() {
		return urlMurmurHash;
	}

	public void setUrlMurmurHash(String urlMurmurHash) {
		this.urlMurmurHash = urlMurmurHash;
	}

	public Date getCrawlTimestamp() {
		return crawlTimestamp;
	}

	public void setCrawlTimestamp(Date crawlTimestamp) {
		this.crawlTimestamp = crawlTimestamp;
	}

	public String getResponseCode() {
		return responseCode;
	}

	public void setResponseCode(String responseCode) {
		this.responseCode = responseCode;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public Date getPreviousCrawlTimestamp() {
		return previousCrawlTimestamp;
	}

	public void setPreviousCrawlTimestamp(Date previousCrawlTimestamp) {
		this.previousCrawlTimestamp = previousCrawlTimestamp;
	}

	public Integer getSign() {
		return sign;
	}

	public void setSign(Integer sign) {
		this.sign = sign;
	}

	@Override
	public String toString() {
		return "RobotsTxtClickHouseEntity [domainId=" + domainId + ", url=" + url + ", trackDate=" + trackDate + ", urlHash=" + urlHash + ", urlMurmurHash="
				+ urlMurmurHash + ", crawlTimestamp=" + crawlTimestamp + ", responseCode=" + responseCode + ", content=" + content + ", previousCrawlTimestamp="
				+ previousCrawlTimestamp + ", sign=" + sign + "]";
	}

}