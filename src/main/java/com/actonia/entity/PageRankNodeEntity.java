package com.actonia.entity;

import java.math.BigDecimal;

import javax.persistence.Transient;

public class PageRankNodeEntity {
	private int nodeId;
	private String nodeUrlHashCode;
	private String nodeUrl;
	private BigDecimal nodeWeight;
	private int nodeCount;
	private boolean nodeConverged;

	@Transient
	private BigDecimal transferredNodeWeight;

	public int getNodeId() {
		return nodeId;
	}

	public void setNodeId(int nodeId) {
		this.nodeId = nodeId;
	}

	public String getNodeUrlHashCode() {
		return nodeUrlHashCode;
	}

	public void setNodeUrlHashCode(String nodeUrlHashCode) {
		this.nodeUrlHashCode = nodeUrlHashCode;
	}

	public String getNodeUrl() {
		return nodeUrl;
	}

	public void setNodeUrl(String nodeUrl) {
		this.nodeUrl = nodeUrl;
	}

	public BigDecimal getNodeWeight() {
		return nodeWeight;
	}

	public void setNodeWeight(BigDecimal nodeWeight) {
		this.nodeWeight = nodeWeight;
	}

	public int getNodeCount() {
		return nodeCount;
	}

	public void setNodeCount(int nodeCount) {
		this.nodeCount = nodeCount;
	}

	public boolean isNodeConverged() {
		return nodeConverged;
	}

	public void setNodeConverged(boolean nodeConverged) {
		this.nodeConverged = nodeConverged;
	}

	public BigDecimal getTransferredNodeWeight() {
		return transferredNodeWeight;
	}

	public void setTransferredNodeWeight(BigDecimal transferredNodeWeight) {
		this.transferredNodeWeight = transferredNodeWeight;
	}

	@Override
	public String toString() {
		return "PageRankNodeEntity [nodeId=" + nodeId + ", nodeUrlHashCode=" + nodeUrlHashCode + ", nodeUrl=" + nodeUrl + ", nodeWeight=" + nodeWeight + ", nodeCount="
				+ nodeCount + ", nodeConverged=" + nodeConverged + ", transferredNodeWeight=" + transferredNodeWeight + "]";
	}

}