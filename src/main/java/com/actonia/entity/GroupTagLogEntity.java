package com.actonia.entity;

import java.util.Date;

public class GroupTagLogEntity {
	
	public static final int OPERATION_TYPE_INSERT = 1;
	public static final int OPERATION_TYPE_UPDATE = 2;
	public static final int OPERATION_TYPE_DELETE = 3;

	private int id;
	private int tagId;	
	private int domainId;
	private String tagName;
	private int tagType;	
	private Integer privateTag;
	private Integer changed;
	private Date updateDate;	
	private int isGroupHierarchy;	
	private Date createDate;	
	private Integer groupId = 0;	
	private int operationType;
	private int logDate;	
	private Date createdAt;
	
	private Integer minId;
	private Integer maxId;
	private int logCnt;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getTagId() {
		return tagId;
	}

	public void setTagId(int tagId) {
		this.tagId = tagId;
	}

	public int getDomainId() {
		return domainId;
	}

	public void setDomainId(int domainId) {
		this.domainId = domainId;
	}

	public String getTagName() {
		return tagName;
	}

	public void setTagName(String tagName) {
		this.tagName = tagName;
	}

	public int getTagType() {
		return tagType;
	}

	public void setTagType(int tagType) {
		this.tagType = tagType;
	}

	public Integer getPrivateTag() {
		return privateTag;
	}

	public void setPrivateTag(Integer privateTag) {
		this.privateTag = privateTag;
	}

	public Integer getChanged() {
		return changed;
	}

	public void setChanged(Integer changed) {
		this.changed = changed;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public int getIsGroupHierarchy() {
		return isGroupHierarchy;
	}

	public void setIsGroupHierarchy(int isGroupHierarchy) {
		this.isGroupHierarchy = isGroupHierarchy;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getGroupId() {
		return groupId;
	}

	public void setGroupId(Integer groupId) {
		this.groupId = groupId;
	}

	public int getOperationType() {
		return operationType;
	}

	public void setOperationType(int operationType) {
		this.operationType = operationType;
	}

	public int getLogDate() {
		return logDate;
	}

	public void setLogDate(int logDate) {
		this.logDate = logDate;
	}

	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}
	
	public Integer getMinId() {
		return minId;
	}

	public void setMinId(Integer minId) {
		this.minId = minId;
	}

	public Integer getMaxId() {
		return maxId;
	}

	public void setMaxId(Integer maxId) {
		this.maxId = maxId;
	}
	
	public int getLogCnt() {
		return logCnt;
	}

	public void setLogCnt(int logCnt) {
		this.logCnt = logCnt;
	}
}