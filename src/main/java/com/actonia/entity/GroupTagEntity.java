package com.actonia.entity;

import java.util.Date;
import java.util.Objects;

/**
 * 
 * com.actonia.subserver.entity.GroupTagEntity.java
 * 
 * <AUTHOR>
 * 
 * @version $Revision:$ $Author:$
 */
public class GroupTagEntity {

	public static final int STATUS_ACTIVE = 1;
	public static final int STATUS_INACTIVE = 2;

	public static final int TYPE_ADDBY_USER = 1;

	public static final int TAG_TYPE_TARGET_URL = 1;
	public static final int TAG_TYPE_KEYWORD = 2;

	public static final int IS_HIERARCHY_TAG = 1;
	public static final int NOT_HIERARCHY_TAG = 0;

	public static final int PRIVATE_TAG_FLG = 1;
	public static final int PUBLIC_TAG_FLG = 0;

	public static final int TAG_CHANGED = 1;

	public static final int DISABLED_KEYWORD_TRACE = 0;
	public static final int ENABLED_KEYWORD_TRACE = 1;

	public static final int TAG_CATEGORY_KEYWORD = 1;
	public static final int TAG_CATEGORY_GEO = 2;
	public static final int TAG_CATEGORY_KEYWORD_GEO = 3;

	private Integer id;
	private Integer domainId;
	private String tagName;
	private Integer tagType;
	private Integer tagCategory;
	private Integer privateTag;
	private Integer changed;
	private Date updateDate;
	private Integer isGroupHierarchy;
	private Date createDate;
	private Integer groupId = 0;
	private Integer logId;
	private Integer enableKeywordTrace;

	public Integer getEnableKeywordTrace() {
		return enableKeywordTrace;
	}

	public void setEnableKeywordTrace(Integer enableKeywordTrace) {
		this.enableKeywordTrace = enableKeywordTrace;
	}

	public boolean isKeywordTraceEnabled() {
		return enableKeywordTrace != null && enableKeywordTrace == ENABLED_KEYWORD_TRACE;
	}

	public Integer getIsGroupHierarchy() {
		return isGroupHierarchy;
	}

	public void setIsGroupHierarchy(Integer isGroupHierarchy) {
		this.isGroupHierarchy = isGroupHierarchy;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public Integer getChanged() {
		return changed;
	}

	public void setChanged(Integer changed) {
		this.changed = changed;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getDomainId() {
		return domainId;
	}

	public void setDomainId(Integer domainId) {
		this.domainId = domainId;
	}

	public String getTagName() {
		return tagName;
	}

	public void setTagName(String tagName) {
		this.tagName = tagName;
	}

	public Integer getTagType() {
		return tagType;
	}

	public void setTagType(Integer tagType) {
		this.tagType = tagType;
	}

	public Integer getTagCategory() {
		return tagCategory;
	}

	public void setTagCategory(Integer tagCategory) {
		this.tagCategory = tagCategory;
	}

	public Integer getPrivateTag() {
		return privateTag;
	}

	public void setPrivateTag(Integer privateTag) {
		this.privateTag = privateTag;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

//	public boolean isTargetGroupTag() {
//		if (this.getTagType() != null && this.getTagType().intValue() == TAG_TYPE_TARGET_URL) {
//			return true;
//		}
//		return false;
//	}

	public Integer getGroupId() {
		return groupId;
	}

	public void setGroupId(Integer groupId) {
		this.groupId = groupId;
	}

	public Integer getLogId() {
		return logId;
	}

	public void setLogId(Integer logId) {
		this.logId = logId;
	}

	@Override
	public String toString() {
		return "GroupTagEntity [id=" + id + ", domainId=" + domainId + ", tagName=" + tagName + ", tagType=" + tagType + ", privateTag=" + privateTag + ", changed="
				+ changed + ", updateDate=" + updateDate + ", isGroupHierarchy=" + isGroupHierarchy + ", createDate=" + createDate + ", groupId=" + groupId + "]";
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (!(o instanceof GroupTagEntity))
			return false;
		GroupTagEntity that = (GroupTagEntity) o;
		return Objects.equals(domainId, that.domainId) && Objects.equals(tagName, that.tagName) && Objects.equals(tagType, that.tagType);
	}

	@Override
	public int hashCode() {
		return Objects.hash(domainId, tagName, tagType);
	}
}
