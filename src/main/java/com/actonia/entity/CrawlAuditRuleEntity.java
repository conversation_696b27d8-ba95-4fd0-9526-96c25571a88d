package com.actonia.entity;

public class CrawlAuditRuleEntity {
	private String ruleId;
	private String group;
	private String severity;
	private String title;
	private String why;
	private String solution;

	public String getRuleId() {
		return ruleId;
	}

	public void setRuleId(String ruleId) {
		this.ruleId = ruleId;
	}

	public String getGroup() {
		return group;
	}

	public void setGroup(String group) {
		this.group = group;
	}

	public String getSeverity() {
		return severity;
	}

	public void setSeverity(String severity) {
		this.severity = severity;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getWhy() {
		return why;
	}

	public void setWhy(String why) {
		this.why = why;
	}

	public String getSolution() {
		return solution;
	}

	public void setSolution(String solution) {
		this.solution = solution;
	}

	@Override
	public String toString() {
		return "CrawlAuditRuleEntity [ruleId=" + ruleId + ", group=" + group + ", severity=" + severity + ", title=" + title + ", why=" + why + ", solution=" + solution
				+ "]";
	}

}
