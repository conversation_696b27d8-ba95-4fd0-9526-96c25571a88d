/**
 * 
 */
package com.actonia.entity;

import javax.persistence.Transient;

import org.apache.commons.lang.StringUtils;


public class UrlMetricsDataHistoryEntity extends UrlMetricsDataEntity {
	private int yearWeek;
	private String friendlyName;
	private Integer weekBounces;
	private TargetURLCriteria targetURLCriteria;
	private Integer weekEntrances;
	
	@Transient
	private int domainId;

	public int getYearWeek() {
		return yearWeek;
	}

	public void setYearWeek(int yearWeek) {
		this.yearWeek = yearWeek;
	}

	public String getFriendlyName() {
		return friendlyName;
	}

	public void setFriendlyName(String friendlyName) {
		this.friendlyName = friendlyName;
	}

	public Integer getWeekBounces() {
		return weekBounces;
	}

	public void setWeekBounces(Integer weekBounces) {
		this.weekBounces = weekBounces;
	}

	public TargetURLCriteria getTargetURLCriteria() {
		return targetURLCriteria;
	}

	public void setTargetURLCriteria(TargetURLCriteria targetURLCriteria) {
		this.targetURLCriteria = targetURLCriteria;
	}

	@Override
	public String toString() {
		StringBuilder stringBuilder = new StringBuilder();
		String testString = super.toString();
		StringUtils.replace(testString, "]", ",yearWeek=" + yearWeek + ",friendlyName=" + friendlyName + ",weekBounces=" + weekBounces + "]");
		stringBuilder.append(testString);
		if (targetURLCriteria != null) {
			stringBuilder.append(targetURLCriteria.toString());
		}
		return stringBuilder.toString();
	}

	@Transient
	public int getDomainId() {
		return domainId;
	}
	
	@Transient
	public void setDomainId(int domainId) {
		this.domainId = domainId;
	}

	@Transient
	public Integer getWeekEntrances() {
		return weekEntrances;
	}

	@Transient
	public void setWeekEntrances(Integer weekEntrances) {
		this.weekEntrances = weekEntrances;
	}
}
