/**
 * 
 */
package com.actonia.entity;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Date;

import javax.persistence.Transient;

import org.apache.commons.lang.StringEscapeUtils;

import com.actonia.utils.FormatUtils;
import com.actonia.utils.RankCheckUtils;

/**
 * com.actonia.subserver.entity.KeywordEntity.java
 * 
 * @version $Revision: 80474 $ $Author: miaopeng<PERSON>i@SHINETECHCHINA $
 */
public class KeywordEntity {

	public static final int TYPE_ADD_BY_USER = 1;

	public static final int TYPE_ADD_BY_GA = 2;
	
	public static final int TYPE_ADD_BY_MOBILE = 3;

	public static final int TYPE_ADD_BY_PAID_GA = 100;
	public static final int TYPE_ADD_BY_MACYS = 99;
	public static final int TYPE_ADD_BY_UPLOAD_GA = 101;

	public static final int RANK_CHECK_ACTIVE = 1;
	public static final int RANK_CHECK_INACTIVE = 0;
	public static final int RANK_CHECK_SUSPEND_BY_ADMIN = -1;
	
	public static final int FREQUENCY_DAILY = 1;
	public static final int FREQUENCY_WEEKLY = 2;
	public static final int FREQUENCY_MONTHLY = 3;

	private Long id;

	private String keywordName;

	private String keywordValue;

	private Integer type;

	private String description;

	private Integer ownDomainId;

	private Integer rankCheck;

	private Integer topRankTargeturlId;
	/**
	 * yesterday rank value
	 */
	private Integer rank1;

	/**
	 * the day before yesterday rank value
	 */
	private Integer rank2;

	private Integer yesterdayEntrances;

	// 7 days entrances
	private Integer weekEntrances;

	private Float currentCtr;

	private Integer monthlySearchVolume;

	private Integer trafficIncrease;

	private Integer rankImprovement;

	private Integer rank3;

	private Integer bing_rank1;

	private Integer bing_rank2;

	private Date rankUpdateDate;

	private Integer avgMonthlySearchVolume;

	private Date createDate;

	private Integer toprankcount;

	private Float wtdavgrank;
	
	private Integer frequency;

	private String searchEngine;

	private String language;

	private String searchEngineCountry;

	public Integer getFrequency() {
		return frequency;
	}

	public void setFrequency(Integer frequency) {
		this.frequency = frequency;
	}

	public Integer getToprankcount() {
		return toprankcount;
	}

	public void setToprankcount(Integer toprankcount) {
		this.toprankcount = toprankcount;
	}

	public Float getWtdavgrank() {
		return wtdavgrank;
	}

	public void setWtdavgrank(Float wtdavgrank) {
		this.wtdavgrank = wtdavgrank;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getBing_rank1() {
		return bing_rank1;
	}

	public void setBing_rank1(Integer bingRank1) {
		bing_rank1 = bingRank1;
	}

	public Integer getBing_rank2() {
		return bing_rank2;
	}

	public void setBing_rank2(Integer bingRank2) {
		bing_rank2 = bingRank2;
	}

	public Date getRankUpdateDate() {
		return rankUpdateDate;
	}

	public void setRankUpdateDate(Date rankUpdateDate) {
		this.rankUpdateDate = rankUpdateDate;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getKeywordName() {
		return keywordName;
	}
	
	public String getKeywordNameForEvent(){
		if (KeywordNameRelEntity.isLongKeywordNameReplaced(this.getLongKeywordNameReplaced())) {
			return "[Keyword Too Long] - (Keyword id:" + this.getId() + ")";
		}
		return getKeywordName();
	}

	public void setKeywordName(String keywordName) {
		this.keywordName = keywordName;
	}

	public String getKeywordValue() {
		return keywordValue;
	}

	public void setKeywordValue(String keywordValue) {
		this.keywordValue = keywordValue;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Integer getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public Integer getRankCheck() {
		return rankCheck;
	}

	public void setRankCheck(Integer rankCheck) {
		this.rankCheck = rankCheck;
	}

	public Integer getRank1() {
		return rank1;
	}

	public void setRank1(Integer rank1) {
		this.rank1 = rank1;
	}

	public Integer getRank2() {
		return rank2;
	}

	public void setRank2(Integer rank2) {
		this.rank2 = rank2;
	}

	public Integer getYesterdayEntrances() {
		return yesterdayEntrances;
	}

	public void setYesterdayEntrances(Integer yesterdayEntrances) {
		this.yesterdayEntrances = yesterdayEntrances;
	}

	public Integer getWeekEntrances() {
		return weekEntrances;
	}

	public void setWeekEntrances(Integer weekEntrances) {
		this.weekEntrances = weekEntrances;
	}

	public Float getCurrentCtr() {
		return currentCtr;
	}

	public void setCurrentCtr(Float currentCtr) {
		this.currentCtr = currentCtr;
	}

	public Integer getMonthlySearchVolume() {
		return monthlySearchVolume;
	}

	public void setMonthlySearchVolume(Integer monthlySearchVolume) {
		this.monthlySearchVolume = monthlySearchVolume;
	}

	public Integer getTrafficIncrease() {
		return trafficIncrease;
	}

	public void setTrafficIncrease(Integer trafficIncrease) {
		this.trafficIncrease = trafficIncrease;
	}

	public Integer getRankImprovement() {
		return rankImprovement;
	}

	public void setRankImprovement(Integer rankImprovement) {
		this.rankImprovement = rankImprovement;
	}

	public Integer getRank3() {
		return rank3;
	}

	public void setRank3(Integer rank3) {
		this.rank3 = rank3;
	}

	public String getKeywordForHtml() {
		return getKeywordForHtml(keywordName);
	}
	
	public String getKeywordForHtml(String keywordName) {
		boolean isLongKeyword = KeywordNameRelEntity.isLongKeywordNameReplaced(keywordName);
		try {
			return isLongKeyword ? keywordName : getFormatKeyword(keywordName);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
	public static String getFormatKeyword(String keywordName) {
		String res = null;
		try {
			res = StringEscapeUtils.escapeHtml(URLDecoder.decode(keywordName, "UTF-8"));
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		return res;
	}

	@Transient
	public String getKeywordForHtmlNew() {
		return decodeEscapeHtml(this.keywordValue);
	}
	
	@Transient
	public static final String decodeEscapeHtml(String value) {
		return FormatUtils.getInstance().decodeAndEscapeHtml(value);
	}
	
	public String getDecoderKeyword() {
		return RankCheckUtils.decoderString(keywordName);
	}
	
	public String getDecoderKeyword(String keywordName) {
		return RankCheckUtils.decoderString(keywordName);
	}

	public Integer getTopRankTargeturlId() {
		return topRankTargeturlId;
	}

	public void setTopRankTargeturlId(Integer topRankTargeturlId) {
		this.topRankTargeturlId = topRankTargeturlId;
	}

	public Integer getAvgMonthlySearchVolume() {
		return avgMonthlySearchVolume;
	}

	public void setAvgMonthlySearchVolume(Integer avgMonthlySearchVolume) {
		this.avgMonthlySearchVolume = avgMonthlySearchVolume;
	}

	@Transient
	public boolean isManaged() {
		if (type == null || type.intValue() != TYPE_ADD_BY_USER) {
			return false;
		}
		if (rankCheck == null || rankCheck.intValue() != RANK_CHECK_ACTIVE) {
			return false;
		}
		return true;
	}
	
	/**
	 * @return replace keywordName if the  length of keywordName is more than 255 
	 * */
	public String getLongKeywordNameReplaced() {
		return KeywordNameRelEntity.getLongKeywordNameReplaced(keywordName);
	}
	
    @Override
    public String toString() {
        return "KeywordEntity{" +
                "avgMonthlySearchVolume=" + avgMonthlySearchVolume +
                ", id=" + id +
                ", keywordName='" + keywordName + '\'' +
                ", keywordValue='" + keywordValue + '\'' +
                ", type=" + type +
                ", description='" + description + '\'' +
                ", ownDomainId=" + ownDomainId +
                ", rankCheck=" + rankCheck +
                ", topRankTargeturlId=" + topRankTargeturlId +
                ", rank1=" + rank1 +
                ", rank2=" + rank2 +
                ", yesterdayEntrances=" + yesterdayEntrances +
                ", weekEntrances=" + weekEntrances +
                ", currentCtr=" + currentCtr +
                ", monthlySearchVolume=" + monthlySearchVolume +
                ", trafficIncrease=" + trafficIncrease +
                ", rankImprovement=" + rankImprovement +
                ", rank3=" + rank3 +
                ", bing_rank1=" + bing_rank1 +
                ", bing_rank2=" + bing_rank2 +
                ", rankUpdateDate=" + rankUpdateDate +
                ", createDate=" + createDate +
                ", toprankcount=" + toprankcount +
                ", wtdavgrank=" + wtdavgrank +
                ", frequency=" + frequency +
                '}';
    }

	@Transient
	public String getSearchEngine() {
		return searchEngine;
	}

	@Transient
	public void setSearchEngine(String searchEngine) {
		this.searchEngine = searchEngine;
	}

	@Transient
	public String getLanguage() {
		return language;
	}

	@Transient
	public void setLanguage(String language) {
		this.language = language;
	}

	@Transient
	public String getSearchEngineCountry() {
		return searchEngineCountry;
	}

	@Transient
	public void setSearchEngineCountry(String searchEngineCountry) {
		this.searchEngineCountry = searchEngineCountry;
	}
	
	@Transient
	public String getRankToDisplay() {
		int rank = getRankValueToCalculate(rank1);
		if (rank > 100) {
			return "100+";
		}
		return String.valueOf(rank);
	}
	
	@Transient
	public int getRankChanged() {
		int rankValue2 = getRankValueToCalculate(rank2);
		int rankValue1 = getRankValueToCalculate(rank1);
		
		return rankValue2 - rankValue1;
	}
	
	@Transient
	private int getRankValueToCalculate(Integer rank) {
		if (rank == null) {
			return 101;
		}
		if (rank > 100) {
			return 101;
		}
		if (rank <= 0) {
			return 101;
		}
		return rank;
	}
}
