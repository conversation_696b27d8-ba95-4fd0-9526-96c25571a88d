package com.actonia.entity;

import com.actonia.value.object.*;
import lombok.Data;

import java.beans.Transient;
import java.util.List;
import java.util.Map;

@Data
public class UrlMetricsEntityV3 extends UrlMetricsEntity {
	private List<String> redirectPathUrlList;
	private List<Integer> redirectPathUrlStatusCodeList;
	private String redirectFinalUrl;
	private Integer redirectFinalUrlStatusCode;
	private Integer outboundLinks;
	private Integer retryCount;
	private List<OptionEntity> optionEntityList;
	//private List<String> imageAltTextList;
	private String documentId;
	private String content;
	private String httpStreamContent;
	private String coreName;
	private String languageCode;
	private Integer internalLinks;
	private List<String> linkList;

	// map key = div ID
	// map value = list of div content
	private Map<String, List<String>> divIdContentListMap;

	// map key = div class
	// map value = list of div content
	private Map<String, List<String>> divClassContentListMap;

	private List<AdditionalContentFilterValueObject> additionalContentFilterValueObjectList;

	private String headerVary;

	private List<String> internalLinkList = null;

	// map key = rule number (1-based)
	// map value = true or false
	private List<PageAnalysisResult> pageAnalysisResultList;

	private String normalizedUrlString = null;

	private String normalizedUrlHashCode = null;

	private String sharedCountsCrossReferenceTrackDate;

	private DomainIdLanguageCodeValueObject domainIdLanguageCodeValueObject;

	//***************************************** Polite Crawl v2.0 begins *********************************************//

	// track_date
	private String targetUrlHtmlTrackDate;

	// amphtml_href
	private String amphtml_href;

	// amphtml_flag
	private Boolean amphtml_flag;

	// analyzed_url_flg_s
	private String analyzed_url_flg_s;

	// analyzed_url_s
	private String analyzed_url_s;

	// archive_flg
	private String archive_flg;

	// archive_flg_x_tag
	private String archive_flg_x_tag;

	// blocked_by_robots
	private String blocked_by_robots;

	// canonical_flg
	private String canonical_flg;

	// canonical_header_flag
	private Boolean canonical_header_flag;

	// canonical_header_type
	private String canonical_header_type;

	// canonical_type
	private String canonical_type;

	// canonical_url_is_consistent
	private String canonical_url_is_consistent;

	// content_type
	private String content_type;

	// custom_data
	private CustomData[] custom_data;

	// description
	private String description;

	// description_flg
	private String description_flg;

	// description_length
	private Integer description_length;

	// description_simhash
	private String description_simhash;

	// error_message
	private String error_message;

	// final_response_code
	private Integer final_response_code;

	// follow_flg
	private String follow_flg;

	// follow_flg_x_tag
	private String follow_flg_x_tag;

	// h1
	private String[] h1_array;

	// h1_count
	private Integer h1_count;

	// h1_flg
	private String h1_flg;

	// h1_length
	private Integer h1_length;

	// h1_md5
	private String[] h1_md5;

	// h2
	private String[] h2_array;
	private String h2_hash;
	private Integer h2_total;

	// header_noarchive
	private Boolean header_noarchive;

	// header_nofollow
	private Boolean header_nofollow;

	// header_noindex
	private Boolean header_noindex;

	// header_noodp
	private Boolean header_noodp;

	// header_nosnippet
	private Boolean header_nosnippet;

	// header_noydir
	private Boolean header_noydir;

	// hreflang_links_out_count
	private Integer hreflang_links_out_count;

	// hreflang_url_count
	private Integer hreflang_url_count;

	// index_flg
	private String index_flg;

	// index_flg_x_tag
	private String index_flg_x_tag;

	// indexable
	private Boolean indexable;

	// insecure_resources_flag
	private Boolean insecure_resources_flag;

	// meta_charset
	private String meta_charset;

	// meta_content_type
	private String meta_content_type;

	// meta_disabled_sitelinks
	private Boolean meta_disabled_sitelinks;

	// meta_noodp
	private Boolean meta_noodp;

	// meta_nosnippet
	private Boolean meta_nosnippet;

	// meta_noydir
	private Boolean meta_noydir;

	// meta_redirect
	private Boolean meta_redirect;

	// mixed_redirects
	private Boolean mixed_redirects;

	// mobile_rel_alternate_url_is_consistent
	private Boolean mobile_rel_alternate_url_is_consistent;

	// noodp
	private Boolean noodp;

	// nosnippet
	private Boolean nosnippet;

	// noydir
	private Boolean noydir;

	// og_markup
	private OgMarkup[] og_markup;

	// og_markup_flag
	private Boolean og_markup_flag;

	// og_markup_length
	private Integer og_markup_length;

	// outlink_count
	private Integer outlink_count;

	// page_link
	private PageLink[] page_link;

	// redirect_blocked
	private Boolean redirect_blocked;

	// redirect_blocked_reason
	private String redirect_blocked_reason;

	// redirect_flg
	private Boolean redirect_flg;

	// redirect_times
	private Integer redirect_times;

	// response_code
	private String response_code;

	// robots
	private String robots;

	// robots_contents
	private String robots_contents;

	// robots_contents_x_tag
	private Boolean robots_contents_x_tag;

	// robots_flg
	private String robots_flg;

	// robots_flg_x_tag
	private String robots_flg_x_tag;

	// title_flg
	private String title_flg;

	// title_length
	private Integer title_length;

	// title_md5
	private String[] title_md5;

	// title_simhash
	private String title_simhash;

	// viewport_flag
	private Boolean viewport_flag;

	// page_analysis_results
	private PageAnalysisResult[] pageAnalysisResultArray;

	private Boolean pageAnalysisResultInd;

	// change_tracking_hash_cd_json
	private ChangeTrackingHashCdJson[] changeTrackingHashCdJsonArray;

	private boolean targetUrlHtmlDailyDataInd;

	private Boolean content_guard_crawl_flag;

	private String s3_file_name;

	private String crawl_timestamp;

	private Integer crawl_frequency_type;

	// base_tag
	private String base_tag;

	// base_tag_flag
	private Boolean base_tag_flag;

	// base_tag_target
	private String base_tag_target;

	// response_headers (names)
	private String[] response_header_names;

	// response_headers (name, value)
	private ResponseHeaders[] response_headers;

	// url type: from t_target_url or ranking_detail_
	private Integer urlType;

	// # https://www.wrike.com/open.htm?id=1228696780
	/**
	 *  DEFAULT '0' COMMENT '0 or null: crawl normally as other URLs for URLs seeded from RSS, 1: Disable Page Crawling after initial success crawl for URLs seeded from RSS
	 */
	private Integer sourceType;

	private String viewportContent;

	/**
	 * DEFAULT '0' COMMENT '0 or null: crawl normally as other URLs for URLs seeded from RSS, 1: Disable Page Crawling after initial success crawl for URLs seeded from RSS'
	 */
	private Integer initialCrawlOnly;
	private HreflangErrors hreflang_errors;

	//***************************************** Polite Crawl v2.0 ends *********************************************//

	@Transient
	public Object[] getDomainIdObjects() {
		Object[] domainIdObjects = null;
		Integer[] domainIds = null;
		if (getDomainIdLanguageCodeValueObject() != null) {
			domainIds = getDomainIdLanguageCodeValueObject().getDomainIds();
			if (domainIds != null && domainIds.length > 0) {
				domainIdObjects = new Object[domainIds.length];
				for (int i = 0; i < domainIds.length; i++) {
					domainIdObjects[i] = domainIds[i];
				}
			}
		}
		return domainIdObjects;
	}

}
