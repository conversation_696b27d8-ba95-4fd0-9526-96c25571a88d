package com.actonia.entity;

import java.util.Date;

public class TargetURLCriteria {
    public static final int ROBOT_INDEX_NOTEXIST = 0;
    public static final int ROBOT_INDEX_INDEX = 1;
    public static final int ROBOT_INDEX_NOINDEX = 2;
    
    public static final int ROBOT_FOLLOW_NOTEXIST = 0;
    public static final int ROBOT_FOLLOW_FOLLOW = 1;
    public static final int ROBOT_FOLLOW_NOTFOLLOW = 2;

    private Integer id;

    private Integer ownDomainId;

    private Long targeturlId;

    private String url;

    private Date cachedDate;

    private Integer pagerank;

    private Integer respCode;

    private Integer entrancesLast7days;

    private Integer entrancesLast30days;

    private Integer entrancesLastMonth;

    private Integer entrancesWeekOverWeek;

    private Integer entrancesMonthOverMonth;

    private String pageTitle;

    private String pageMetaKeyword;

    private String pageMetaDesc;

    private String pageH1;

    private String pageH2;

    private Integer pageTitleLength;

    private Integer pageMetaKeywordLength;

    private Integer pageMetaDescLength;

    private Integer pageH1Count;

    private Integer pageH2Count;

    private Integer score;

    private Integer countAssociatedKeyword;

    private Float wtdAvgRank;

    private Integer googleSearchVolume;

    private Integer metaRobotIndex;

    private Integer metaRobotFollow;

    public Float getWtdAvgRank() {
        return wtdAvgRank;
    }

    public void setWtdAvgRank(Float wtdAvgRank) {
        this.wtdAvgRank = wtdAvgRank;
    }

    public Integer getGoogleSearchVolume() {
        return googleSearchVolume;
    }

    public void setGoogleSearchVolume(Integer googleSearchVolume) {
        this.googleSearchVolume = googleSearchVolume;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOwnDomainId() {
        return ownDomainId;
    }

    public void setOwnDomainId(Integer ownDomainId) {
        this.ownDomainId = ownDomainId;
    }

    public Long getTargeturlId() {
        return targeturlId;
    }

    public void setTargeturlId(Long targeturlId) {
        this.targeturlId = targeturlId;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Date getCachedDate() {
        return cachedDate;
    }

    public void setCachedDate(Date cachedDate) {
        this.cachedDate = cachedDate;
    }

    public Integer getPagerank() {
        return pagerank;
    }

    public void setPagerank(Integer pagerank) {
        this.pagerank = pagerank;
    }

    public Integer getRespCode() {
        return respCode;
    }

    public void setRespCode(Integer respCode) {
        this.respCode = respCode;
    }

    public Integer getEntrancesLast7days() {
        return entrancesLast7days;
    }

    public void setEntrancesLast7days(Integer entrancesLast7days) {
        this.entrancesLast7days = entrancesLast7days;
    }

    public Integer getEntrancesLast30days() {
        return entrancesLast30days;
    }

    public void setEntrancesLast30days(Integer entrancesLast30days) {
        this.entrancesLast30days = entrancesLast30days;
    }

    public Integer getEntrancesLastMonth() {
        return entrancesLastMonth;
    }

    public void setEntrancesLastMonth(Integer entrancesLastMonth) {
        this.entrancesLastMonth = entrancesLastMonth;
    }

    public Integer getEntrancesWeekOverWeek() {
        return entrancesWeekOverWeek;
    }

    public void setEntrancesWeekOverWeek(Integer entrancesWeekOverWeek) {
        this.entrancesWeekOverWeek = entrancesWeekOverWeek;
    }

    public Integer getEntrancesMonthOverMonth() {
        return entrancesMonthOverMonth;
    }

    public void setEntrancesMonthOverMonth(Integer entrancesMonthOverMonth) {
        this.entrancesMonthOverMonth = entrancesMonthOverMonth;
    }

    public String getPageTitle() {
        return pageTitle;
    }

    public void setPageTitle(String pageTitle) {
        this.pageTitle = pageTitle;
    }

    public String getPageMetaKeyword() {
        return pageMetaKeyword;
    }

    public void setPageMetaKeyword(String pageMetaKeyword) {
        this.pageMetaKeyword = pageMetaKeyword;
    }

    public String getPageMetaDesc() {
        return pageMetaDesc;
    }

    public void setPageMetaDesc(String pageMetaDesc) {
        this.pageMetaDesc = pageMetaDesc;
    }

    public String getPageH1() {
        return pageH1;
    }

    public void setPageH1(String pageH1) {
        this.pageH1 = pageH1;
    }

    public String getPageH2() {
        return pageH2;
    }

    public void setPageH2(String pageH2) {
        this.pageH2 = pageH2;
    }

    public Integer getPageTitleLength() {
        return pageTitleLength;
    }

    public void setPageTitleLength(Integer pageTitleLength) {
        this.pageTitleLength = pageTitleLength;
    }

    public Integer getPageMetaKeywordLength() {
        return pageMetaKeywordLength;
    }

    public void setPageMetaKeywordLength(Integer pageMetaKeywordLength) {
        this.pageMetaKeywordLength = pageMetaKeywordLength;
    }

    public Integer getPageMetaDescLength() {
        return pageMetaDescLength;
    }

    public void setPageMetaDescLength(Integer pageMetaDescLength) {
        this.pageMetaDescLength = pageMetaDescLength;
    }

    public Integer getPageH1Count() {
        return pageH1Count;
    }

    public void setPageH1Count(Integer pageH1Count) {
        this.pageH1Count = pageH1Count;
    }

    public Integer getPageH2Count() {
        return pageH2Count;
    }

    public void setPageH2Count(Integer pageH2Count) {
        this.pageH2Count = pageH2Count;
    }

    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }

    public Integer getCountAssociatedKeyword() {
        return countAssociatedKeyword;
    }

    public void setCountAssociatedKeyword(Integer countAssociatedKeyword) {
        this.countAssociatedKeyword = countAssociatedKeyword;
    }

    public Integer getMetaRobotIndex() {
        return metaRobotIndex;
    }

    public void setMetaRobotIndex(Integer metaRobotIndex) {
        this.metaRobotIndex = metaRobotIndex;
    }

    public Integer getMetaRobotFollow() {
        return metaRobotFollow;
    }

    public void setMetaRobotFollow(Integer metaRobotFollow) {
        this.metaRobotFollow = metaRobotFollow;
    }

	@Override
	public String toString() {
		return " TargetURLCriteria [id=" + id + ", ownDomainId=" + ownDomainId + ", targeturlId=" + targeturlId + ", url=" + url + ", cachedDate="
				+ cachedDate + ", pagerank=" + pagerank + ", respCode=" + respCode + ", entrancesLast7days=" + entrancesLast7days
				+ ", entrancesLast30days=" + entrancesLast30days + ", entrancesLastMonth=" + entrancesLastMonth + ", entrancesWeekOverWeek="
				+ entrancesWeekOverWeek + ", entrancesMonthOverMonth=" + entrancesMonthOverMonth + ", pageTitle=" + pageTitle + ", pageMetaKeyword="
				+ pageMetaKeyword + ", pageMetaDesc=" + pageMetaDesc + ", pageH1=" + pageH1 + ", pageH2=" + pageH2 + ", pageTitleLength="
				+ pageTitleLength + ", pageMetaKeywordLength=" + pageMetaKeywordLength + ", pageMetaDescLength=" + pageMetaDescLength
				+ ", pageH1Count=" + pageH1Count + ", pageH2Count=" + pageH2Count + ", score=" + score + ", countAssociatedKeyword="
				+ countAssociatedKeyword + ", wtdAvgRank=" + wtdAvgRank + ", googleSearchVolume=" + googleSearchVolume + ", metaRobotIndex="
				+ metaRobotIndex + ", metaRobotFollow=" + metaRobotFollow + "]";
	}

}
