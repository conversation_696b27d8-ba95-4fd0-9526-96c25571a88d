package com.actonia.entity;

import java.util.Date;

public class PageClarityTrackingEntity {
	private int domainId;
	private int userId; // when domain level, userId is 0
	private Integer startYearWeek;
	private Integer endYearWeek;
	private Integer showUrl;
	private Integer showTitle;
	private Integer showPageRank;
	private Integer showPageAuthority;
	private Integer showWeekEntrances;
	private Integer showPercentMobileEnhances;
	private Integer showWeekBounces;
	private Integer showWeightedAverageRank;
	private Integer showAssociatedKeywords;
	private Integer showAssociatedKeywordsRanked;
	private Integer showOutboundLinks;
	private Integer showInternalLinks;
	private Integer showPageScore;
	private Integer showLinksAcquiredManually;
	private Integer showLinksAcquiredOrganically;
	private Integer showAssociatedCompetitorUrls;
	private Date lastUpdateTimestamp;

	public int getDomainId() {
		return domainId;
	}

	public void setDomainId(int domainId) {
		this.domainId = domainId;
	}

	public int getUserId() {
		return userId;
	}

	public void setUserId(int userId) {
		this.userId = userId;
	}

	public Integer getStartYearWeek() {
		return startYearWeek;
	}

	public void setStartYearWeek(Integer startYearWeek) {
		this.startYearWeek = startYearWeek;
	}

	public Integer getEndYearWeek() {
		return endYearWeek;
	}

	public void setEndYearWeek(Integer endYearWeek) {
		this.endYearWeek = endYearWeek;
	}

	public Integer getShowUrl() {
		return showUrl;
	}

	public void setShowUrl(Integer showUrl) {
		this.showUrl = showUrl;
	}

	public Integer getShowTitle() {
		return showTitle;
	}

	public void setShowTitle(Integer showTitle) {
		this.showTitle = showTitle;
	}

	public Integer getShowPageRank() {
		return showPageRank;
	}

	public void setShowPageRank(Integer showPageRank) {
		this.showPageRank = showPageRank;
	}

	public Integer getShowPageAuthority() {
		return showPageAuthority;
	}

	public void setShowPageAuthority(Integer showPageAuthority) {
		this.showPageAuthority = showPageAuthority;
	}

	public Integer getShowWeekEntrances() {
		return showWeekEntrances;
	}

	public void setShowWeekEntrances(Integer showWeekEntrances) {
		this.showWeekEntrances = showWeekEntrances;
	}

	public Integer getShowPercentMobileEnhances() {
		return showPercentMobileEnhances;
	}

	public void setShowPercentMobileEnhances(Integer showPercentMobileEnhances) {
		this.showPercentMobileEnhances = showPercentMobileEnhances;
	}

	public Integer getShowWeekBounces() {
		return showWeekBounces;
	}

	public void setShowWeekBounces(Integer showWeekBounces) {
		this.showWeekBounces = showWeekBounces;
	}

	public Integer getShowWeightedAverageRank() {
		return showWeightedAverageRank;
	}

	public void setShowWeightedAverageRank(Integer showWeightedAverageRank) {
		this.showWeightedAverageRank = showWeightedAverageRank;
	}

	public Integer getShowAssociatedKeywords() {
		return showAssociatedKeywords;
	}

	public void setShowAssociatedKeywords(Integer showAssociatedKeywords) {
		this.showAssociatedKeywords = showAssociatedKeywords;
	}

	public Integer getShowAssociatedKeywordsRanked() {
		return showAssociatedKeywordsRanked;
	}

	public void setShowAssociatedKeywordsRanked(Integer showAssociatedKeywordsRanked) {
		this.showAssociatedKeywordsRanked = showAssociatedKeywordsRanked;
	}

	public Integer getShowOutboundLinks() {
		return showOutboundLinks;
	}

	public void setShowOutboundLinks(Integer showOutboundLinks) {
		this.showOutboundLinks = showOutboundLinks;
	}

	public Integer getShowInternalLinks() {
		return showInternalLinks;
	}

	public void setShowInternalLinks(Integer showInternalLinks) {
		this.showInternalLinks = showInternalLinks;
	}

	public Integer getShowPageScore() {
		return showPageScore;
	}

	public void setShowPageScore(Integer showPageScore) {
		this.showPageScore = showPageScore;
	}

	public Integer getShowLinksAcquiredManually() {
		return showLinksAcquiredManually;
	}

	public void setShowLinksAcquiredManually(Integer showLinksAcquiredManually) {
		this.showLinksAcquiredManually = showLinksAcquiredManually;
	}

	public Integer getShowLinksAcquiredOrganically() {
		return showLinksAcquiredOrganically;
	}

	public void setShowLinksAcquiredOrganically(Integer showLinksAcquiredOrganically) {
		this.showLinksAcquiredOrganically = showLinksAcquiredOrganically;
	}

	public Integer getShowAssociatedCompetitorUrls() {
		return showAssociatedCompetitorUrls;
	}

	public void setShowAssociatedCompetitorUrls(Integer showAssociatedCompetitorUrls) {
		this.showAssociatedCompetitorUrls = showAssociatedCompetitorUrls;
	}

	public Date getLastUpdateTimestamp() {
		return lastUpdateTimestamp;
	}

	public void setLastUpdateTimestamp(Date lastUpdateTimestamp) {
		this.lastUpdateTimestamp = lastUpdateTimestamp;
	}

	@Override
	public String toString() {
		return " PageClarityTrackingEntity [domainId=" + domainId + ", userId=" + userId + ", startYearWeek=" + startYearWeek + ", endYearWeek="
				+ endYearWeek + ", showUrl=" + showUrl + ", showTitle=" + showTitle + ", showPageRank=" + showPageRank + ", showPageAuthority="
				+ showPageAuthority + ", showWeekEntrances=" + showWeekEntrances + ", showPercentMobileEnhances=" + showPercentMobileEnhances
				+ ", showWeekBounces=" + showWeekBounces + ", showWeightedAverageRank=" + showWeightedAverageRank + ", showAssociatedKeywords="
				+ showAssociatedKeywords + ", showAssociatedKeywordsRanked=" + showAssociatedKeywordsRanked + ", showOutboundLinks="
				+ showOutboundLinks + ", showInternalLinks=" + showInternalLinks + ", showPageScore=" + showPageScore
				+ ", showLinksAcquiredManually=" + showLinksAcquiredManually + ", showLinksAcquiredOrganically=" + showLinksAcquiredOrganically
				+ ", showAssociatedCompetitorUrls=" + showAssociatedCompetitorUrls + ", lastUpdateTimestamp=" + lastUpdateTimestamp + "]";
	}

}