package com.actonia.entity;

public class AdditionalContentEntity {
	private long id;
	private int domainId;
	private int selectorType;
	private String selector;
	private int urlSelectorType;
	private String urlSelector;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public int getDomainId() {
		return domainId;
	}

	public void setDomainId(int domainId) {
		this.domainId = domainId;
	}

	public int getSelectorType() {
		return selectorType;
	}

	public void setSelectorType(int selectorType) {
		this.selectorType = selectorType;
	}

	public String getSelector() {
		return selector;
	}

	public void setSelector(String selector) {
		this.selector = selector;
	}

	public int getUrlSelectorType() {
		return urlSelectorType;
	}

	public void setUrlSelectorType(int urlSelectorType) {
		this.urlSelectorType = urlSelectorType;
	}

	public String getUrlSelector() {
		return urlSelector;
	}

	public void setUrlSelector(String urlSelector) {
		this.urlSelector = urlSelector;
	}

	@Override
	public String toString() {
		return "AdditionalContentEntity [id=" + id + ", domainId=" + domainId + ", selectorType=" + selectorType + ", selector=" + selector
				+ ", urlSelectorType=" + urlSelectorType + ", urlSelector=" + urlSelector + "]";
	}

}
