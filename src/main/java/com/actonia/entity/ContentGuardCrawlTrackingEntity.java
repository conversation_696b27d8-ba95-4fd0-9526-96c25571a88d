package com.actonia.entity;

import java.util.Date;

public class ContentGuardCrawlTrackingEntity {
	private int processType;
	private int domainId;
	private int crawlFrequencyType;
	private String domainName;
	private Date crawlTimestamp;
	private int totalUrls;

	public int getProcessType() {
		return processType;
	}

	public void setProcessType(int processType) {
		this.processType = processType;
	}

	public int getDomainId() {
		return domainId;
	}

	public void setDomainId(int domainId) {
		this.domainId = domainId;
	}

	public int getCrawlFrequencyType() {
		return crawlFrequencyType;
	}

	public void setCrawlFrequencyType(int crawlFrequencyType) {
		this.crawlFrequencyType = crawlFrequencyType;
	}

	public String getDomainName() {
		return domainName;
	}

	public void setDomainName(String domainName) {
		this.domainName = domainName;
	}

	public Date getCrawlTimestamp() {
		return crawlTimestamp;
	}

	public void setCrawlTimestamp(Date crawlTimestamp) {
		this.crawlTimestamp = crawlTimestamp;
	}

	public int getTotalUrls() {
		return totalUrls;
	}

	public void setTotalUrls(int totalUrls) {
		this.totalUrls = totalUrls;
	}

	@Override
	public String toString() {
		return "ContentGuardCrawlTrackingEntity [processType=" + processType + ", domainId=" + domainId + ", crawlFrequencyType=" + crawlFrequencyType + ", domainName="
				+ domainName + ", crawlTimestamp=" + crawlTimestamp + ", totalUrls=" + totalUrls + "]";
	}

}