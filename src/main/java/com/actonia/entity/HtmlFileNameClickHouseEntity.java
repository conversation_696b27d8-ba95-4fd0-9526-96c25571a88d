package com.actonia.entity;

import java.util.Date;

public class HtmlFileNameClickHouseEntity implements Cloneable {

	private Integer domainId;
	private String url;
	private Date trackDate;
	private Date crawlTimestamp;
	private String fileName;
	private Integer sign;

	@Override
	public HtmlFileNameClickHouseEntity clone() throws CloneNotSupportedException {
		return (HtmlFileNameClickHouseEntity) super.clone();
	}

	public Integer getDomainId() {
		return domainId;
	}

	public void setDomainId(Integer domainId) {
		this.domainId = domainId;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public Date getTrackDate() {
		return trackDate;
	}

	public void setTrackDate(Date trackDate) {
		this.trackDate = trackDate;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public Integer getSign() {
		return sign;
	}

	public void setSign(Integer sign) {
		this.sign = sign;
	}

	public Date getCrawlTimestamp() {
		return crawlTimestamp;
	}

	public void setCrawlTimestamp(Date crawlTimestamp) {
		this.crawlTimestamp = crawlTimestamp;
	}

	@Override
	public String toString() {
		return "HtmlFileNameClickHouseEntity [domainId=" + domainId + ", url=" + url + ", trackDate=" + trackDate + ", crawlTimestamp=" + crawlTimestamp + ", fileName="
				+ fileName + ", sign=" + sign + "]";
	}

}
