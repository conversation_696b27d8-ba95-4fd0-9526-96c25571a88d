package com.actonia.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ChangeIndMaster {
    @JsonProperty("chg_id")
    private int chgId;

    @JsonProperty("chg_indicator")
    private String chgIndicator;

    @JsonProperty("chg_field")
    private String chgField;

    @JsonProperty("chg_type")
    private String chgType;

    private int bigDataFlg;
    private int criticalFlg;

    // Getters and Setters
    public int getChgId() {
        return chgId;
    }

    public void setChgId(int chgId) {
        this.chgId = chgId;
    }

    public String getChgIndicator() {
        return chgIndicator;
    }

    public void setChgIndicator(String chgIndicator) {
        this.chgIndicator = chgIndicator;
    }

    public String getChgField() {
        return chgField;
    }

    public void setChgField(String chgField) {
        this.chgField = chgField;
    }

    public String getChgType() {
        return chgType;
    }

    public void setChgType(String chgType) {
        this.chgType = chgType;
    }

    public int getBigDataFlg() {
        return bigDataFlg;
    }

    public void setBigDataFlg(int bigDataFlg) {
        this.bigDataFlg = bigDataFlg;
    }

    public int getCriticalFlg() {
        return criticalFlg;
    }

    public void setCriticalFlg(int criticalFlg) {
        this.criticalFlg = criticalFlg;
    }

    @Override
    public String toString() {
        return "ChangeIndMaster{" +
                "chgId=" + chgId +
                ", chgIndicator='" + chgIndicator + '\'' +
                ", chgField='" + chgField + '\'' +
                ", chgType='" + chgType + '\'' +
                ", bigDataFlg=" + bigDataFlg +
                ", criticalFlg=" + criticalFlg +
                '}';
    }
}