package com.actonia.entity;

import java.math.BigDecimal;

public class PageTagAlertConfigEntity {
	private int id;
	private int domainId;
	private int createdByUserId;
	private int dataContentChangeThreshold;
	private Integer tagContentChangeThreshold;
	private String tagContentChangeTagIds;
	private String tagContentChangeAlertEmailAddresses;
	private String alertMetricTypes;
	private BigDecimal tagContentChangeThresholdPercent;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getDomainId() {
		return domainId;
	}

	public void setDomainId(int domainId) {
		this.domainId = domainId;
	}

	public int getCreatedByUserId() {
		return createdByUserId;
	}

	public void setCreatedByUserId(int createdByUserId) {
		this.createdByUserId = createdByUserId;
	}

	public int getDataContentChangeThreshold() {
		return dataContentChangeThreshold;
	}

	public void setDataContentChangeThreshold(int dataContentChangeThreshold) {
		this.dataContentChangeThreshold = dataContentChangeThreshold;
	}

	public Integer getTagContentChangeThreshold() {
		return tagContentChangeThreshold;
	}

	public void setTagContentChangeThreshold(Integer tagContentChangeThreshold) {
		this.tagContentChangeThreshold = tagContentChangeThreshold;
	}

	public String getTagContentChangeTagIds() {
		return tagContentChangeTagIds;
	}

	public void setTagContentChangeTagIds(String tagContentChangeTagIds) {
		this.tagContentChangeTagIds = tagContentChangeTagIds;
	}

	public String getTagContentChangeAlertEmailAddresses() {
		return tagContentChangeAlertEmailAddresses;
	}

	public void setTagContentChangeAlertEmailAddresses(String tagContentChangeAlertEmailAddresses) {
		this.tagContentChangeAlertEmailAddresses = tagContentChangeAlertEmailAddresses;
	}

	public String getAlertMetricTypes() {
		return alertMetricTypes;
	}

	public void setAlertMetricTypes(String alertMetricTypes) {
		this.alertMetricTypes = alertMetricTypes;
	}

	public BigDecimal getTagContentChangeThresholdPercent() {
		return tagContentChangeThresholdPercent;
	}

	public void setTagContentChangeThresholdPercent(BigDecimal tagContentChangeThresholdPercent) {
		this.tagContentChangeThresholdPercent = tagContentChangeThresholdPercent;
	}

	@Override
	public String toString() {
		return " PageTagAlertConfigEntity [id=" + id + ", domainId=" + domainId + ", createdByUserId=" + createdByUserId
				+ ", dataContentChangeThreshold=" + dataContentChangeThreshold + ", tagContentChangeThreshold=" + tagContentChangeThreshold
				+ ", tagContentChangeTagIds=" + tagContentChangeTagIds + ", tagContentChangeAlertEmailAddresses="
				+ tagContentChangeAlertEmailAddresses + ", alertMetricTypes=" + alertMetricTypes + ", tagContentChangeThresholdPercent="
				+ tagContentChangeThresholdPercent + "]";
	}

}