package com.actonia.entity;

public class PageTagContentChangeEntity {
	private int domainId;
	private int groupTagId;
	private int changeDate;

	// 1 = Title
	// 2 = Meta Desc
	// 3 = Meta Keywords
	// 4 = Number of H1
	// 5 = Content of H1
	// 6 = Number of H2
	// 7 = Content of H2
	// 8 = DIV ID Content
	// 9 = DIV Class Content
	// 10 = Response Code (ie. 301 to 200, 200 to 404, etc.)
	private int changeType;

	private Long targetUrlId;

	private String respCodeChange;

	private String additionalContentChange;

	private String divContentChange;

	public int getDomainId() {
		return domainId;
	}

	public void setDomainId(int domainId) {
		this.domainId = domainId;
	}

	public int getGroupTagId() {
		return groupTagId;
	}

	public void setGroupTagId(int groupTagId) {
		this.groupTagId = groupTagId;
	}

	public int getChangeDate() {
		return changeDate;
	}

	public void setChangeDate(int changeDate) {
		this.changeDate = changeDate;
	}

	public int getChangeType() {
		return changeType;
	}

	public void setChangeType(int changeType) {
		this.changeType = changeType;
	}

	public Long getTargetUrlId() {
		return targetUrlId;
	}

	public void setTargetUrlId(Long targetUrlId) {
		this.targetUrlId = targetUrlId;
	}

	public String getRespCodeChange() {
		return respCodeChange;
	}

	public void setRespCodeChange(String respCodeChange) {
		this.respCodeChange = respCodeChange;
	}

	public String getAdditionalContentChange() {
		return additionalContentChange;
	}

	public void setAdditionalContentChange(String additionalContentChange) {
		this.additionalContentChange = additionalContentChange;
	}

	public String getDivContentChange() {
		return divContentChange;
	}

	public void setDivContentChange(String divContentChange) {
		this.divContentChange = divContentChange;
	}

	@Override
	public String toString() {
		return " PageTagContentChangeEntity [domainId=" + domainId + ", groupTagId=" + groupTagId + ", changeDate=" + changeDate + ", changeType="
				+ changeType + ", targetUrlId=" + targetUrlId + ", respCodeChange=" + respCodeChange + ", additionalContentChange="
				+ additionalContentChange + ", divContentChange=" + divContentChange + "]";
	}

}