package com.actonia.entity;

import java.util.List;
import java.util.Map;

import com.amazonaws.services.sqs.model.Message;

public class UrlMetricsEntity {
	private String url;
	private Integer trackDate;
	private Integer fbShare;
	private Integer fbLike;
	private Integer fbComment;
	private Integer fbTotal;
	private Integer fbClick;
	private Integer twTweets;
	private Integer googlePlus;
	private Integer pinterestCount;
	private Integer linkedInCount;
	private Integer responseCode;
	private String openGraph;

	// addition merics
	private String title;
	private String metaKeyword;
	private String metaDesc;
	private String metaRobots;
	private List<String> h1;
	private List<String> h2;
	private String canonical;
	private List<String> authorLinks;

	// majesticseo
	private String majGetIndexItemInfoData;

	private Message message;
	private String metaViewport;

	private Integer pageRank;
	private Float pageAuthority;

	private Integer stumbleUpon;

	private Map<String, String> httpResponseHeaders;

	private Boolean blockedByRobotsTxt;
	
	private String imgAltTextJson;
	
	private String pageLinkJson;

	public Message getMessage() {
		return message;
	}

	public void setMessage(Message message) {
		this.message = message;
	}

	public String getMajGetIndexItemInfoData() {
		return majGetIndexItemInfoData;
	}

	public void setMajGetIndexItemInfoData(String majGetIndexItemInfoData) {
		this.majGetIndexItemInfoData = majGetIndexItemInfoData;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public Integer getTrackDate() {
		return trackDate;
	}

	public void setTrackDate(Integer trackDate) {
		this.trackDate = trackDate;
	}

	public Integer getFbShare() {
		return fbShare;
	}

	public void setFbShare(Integer fbShare) {
		this.fbShare = fbShare;
	}

	public Integer getFbLike() {
		return fbLike;
	}

	public void setFbLike(Integer fbLike) {
		this.fbLike = fbLike;
	}

	public Integer getFbComment() {
		return fbComment;
	}

	public void setFbComment(Integer fbComment) {
		this.fbComment = fbComment;
	}

	public Integer getFbTotal() {
		return fbTotal;
	}

	public void setFbTotal(Integer fbTotal) {
		this.fbTotal = fbTotal;
	}

	public Integer getFbClick() {
		return fbClick;
	}

	public void setFbClick(Integer fbClick) {
		this.fbClick = fbClick;
	}

	public Integer getTwTweets() {
		return twTweets;
	}

	public void setTwTweets(Integer twTweets) {
		this.twTweets = twTweets;
	}

	public Integer getGooglePlus() {
		return googlePlus;
	}

	public void setGooglePlus(Integer googlePlus) {
		this.googlePlus = googlePlus;
	}

	public Integer getPinterestCount() {
		return pinterestCount;
	}

	public void setPinterestCount(Integer pinterestCount) {
		this.pinterestCount = pinterestCount;
	}

	public Integer getResponseCode() {
		return responseCode;
	}

	public void setResponseCode(Integer responseCode) {
		this.responseCode = responseCode;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getMetaKeyword() {
		return metaKeyword;
	}

	public void setMetaKeyword(String metaKeyword) {
		this.metaKeyword = metaKeyword;
	}

	public String getMetaDesc() {
		return metaDesc;
	}

	public void setMetaDesc(String metaDesc) {
		this.metaDesc = metaDesc;
	}

	public List<String> getH1() {
		return h1;
	}

	public void setH1(List<String> h1) {
		this.h1 = h1;
	}

	public List<String> getH2() {
		return h2;
	}

	public void setH2(List<String> h2) {
		this.h2 = h2;
	}

	public String getMetaRobots() {
		return metaRobots;
	}

	public void setMetaRobots(String metaRobots) {
		this.metaRobots = metaRobots;
	}

	public Integer getLinkedInCount() {
		return linkedInCount;
	}

	public void setLinkedInCount(Integer linkedInCount) {
		this.linkedInCount = linkedInCount;
	}

	public String getOpenGraph() {
		return openGraph;
	}

	public void setOpenGraph(String openGraph) {
		this.openGraph = openGraph;
	}

	public String getCanonical() {
		return canonical;
	}

	public void setCanonical(String canonical) {
		this.canonical = canonical;
	}

	public List<String> getAuthorLinks() {
		return authorLinks;
	}

	public void setAuthorLinks(List<String> authorLinks) {
		this.authorLinks = authorLinks;
	}

	public String getMetaViewport() {
		return metaViewport;
	}

	public void setMetaViewport(String metaViewport) {
		this.metaViewport = metaViewport;
	}

	public Integer getPageRank() {
		return pageRank;
	}

	public void setPageRank(Integer pageRank) {
		this.pageRank = pageRank;
	}

	public Float getPageAuthority() {
		return pageAuthority;
	}

	public void setPageAuthority(Float pageAuthority) {
		this.pageAuthority = pageAuthority;
	}

	public Integer getStumbleUpon() {
		return stumbleUpon;
	}

	public void setStumbleUpon(Integer stumbleUpon) {
		this.stumbleUpon = stumbleUpon;
	}

	public Map<String, String> getHttpResponseHeaders() {
		return httpResponseHeaders;
	}

	public void setHttpResponseHeaders(Map<String, String> httpResponseHeaders) {
		this.httpResponseHeaders = httpResponseHeaders;
	}

	@Override
	public String toString() {
		return "UrlMetricsEntity [url=" + url + ", trackDate=" + trackDate + ", fbShare=" + fbShare + ", fbLike=" + fbLike + ", fbComment="
				+ fbComment + ", fbTotal=" + fbTotal + ", fbClick=" + fbClick + ", twTweets=" + twTweets + ", googlePlus=" + googlePlus + ", pinterestCount="
				+ pinterestCount + ", linkedInCount=" + linkedInCount + ", responseCode=" + responseCode + ", openGraph=" + openGraph + ", title=" + title
				+ ", metaKeyword=" + metaKeyword + ", metaDesc=" + metaDesc + ", metaRobots=" + metaRobots + ", h1=" + h1 + ", h2=" + h2 + ", canonical=" + canonical
				+ ", authorLinks=" + authorLinks + ", majGetIndexItemInfoData=" + majGetIndexItemInfoData + ", message=" + message + ", metaViewport=" + metaViewport
				+ ", pageRank=" + pageRank + ", pageAuthority=" + pageAuthority + ", stumbleUpon=" + stumbleUpon + ", httpResponseHeaders=" + httpResponseHeaders
				+ ", blockedByRobotsTxt=" + blockedByRobotsTxt + ", imgAltTextJson=" + imgAltTextJson + ", pageLinkJson=" + pageLinkJson + "]";
	}

}
