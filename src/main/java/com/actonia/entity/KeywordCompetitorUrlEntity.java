package com.actonia.entity;

import java.net.URLDecoder;

import org.apache.commons.lang.StringEscapeUtils;

public class KeywordCompetitorUrlEntity {
	public static final Integer ADD_BY_AUTOMAIC_ASSOCIATIONS = 10;
	public static final int USAGE_NO = 0;
	
	public static final int USAGE_YES = 1;
	
	private Integer id;
	private Long keywordId;
	private Integer competitorurlId;
	private String competitorUrl;
	private String keywordName;
	private Integer addBy;
	private String urlHash;
	private Short usage;

	public String getCompetitorUrl() {
		return competitorUrl;
	}

	public void setCompetitorUrl(String competitorUrl) {
		this.competitorUrl = competitorUrl;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Long getKeywordId() {
		return keywordId;
	}

	public void setKeywordId(Long keywordId) {
		this.keywordId = keywordId;
	}

	public Integer getCompetitorurlId() {
		return competitorurlId;
	}

	public void setCompetitorurlId(Integer competitorurlId) {
		this.competitorurlId = competitorurlId;
	}

	public Integer getAddBy() {
		return addBy;
	}

	public void setAddBy(Integer addBy) {
		this.addBy = addBy;
	}

	public String getKeywordName() {
    	return keywordName;
    }

	public void setKeywordName(String keywordName) {
    	this.keywordName = keywordName;
    }

	public String getKeywordForHtml() {
		try{
			return StringEscapeUtils.escapeHtml(URLDecoder.decode(this.keywordName, "UTF-8"));
		}catch(Exception e){
			e.printStackTrace();
		}
		return null;
	}

	public String getUrlHash() {
		return urlHash;
	}

	public void setUrlHash(String urlHash) {
		this.urlHash = urlHash;
	}

	public Short getUsage() {
		return usage;
	}

	public void setUsage(Short usage) {
		this.usage = usage;
	}

	@Override
	public String toString() {
		return " KeywordCompetitorUrlEntity [id=" + id + ",keywordId=" + keywordId + ", competitorurlId=" + competitorurlId + ",usage=" + usage
				+ ", addBy=" + addBy + "]";
	}
}
