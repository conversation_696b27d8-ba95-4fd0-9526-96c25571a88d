package com.actonia.entity;

public class WebhookEntity {
	private Long id;
	private int domainId;
	private int type;
	private int alertType;
	private String endpoint;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public int getDomainId() {
		return domainId;
	}

	public void setDomainId(int domainId) {
		this.domainId = domainId;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	public int getAlertType() {
		return alertType;
	}

	public void setAlertType(int alertType) {
		this.alertType = alertType;
	}

	public String getEndpoint() {
		return endpoint;
	}

	public void setEndpoint(String endpoint) {
		this.endpoint = endpoint;
	}

	@Override
	public String toString() {
		return "WebhookEntity [id=" + id + ", domainId=" + domainId + ", type=" + type + ", alertType=" + alertType + ", endpoint=" + endpoint + "]";
	}

}