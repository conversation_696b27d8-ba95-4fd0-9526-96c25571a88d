package com.actonia.entity;

import java.util.Date;

import javax.persistence.Transient;

public class CrawlRequestLog {

	private Integer id;

	private Integer crawlStatus;

	private Integer ownDomainId;

	private Integer userId;

	private Integer crawlRequestDate;

	private Integer crawlSpeed;

	private Integer depth;

	private String startingUrl;

	private String crawlDescription;

	private String crawlerIpAddress;

	private Date lastUpdateTime;

	private String exclusions;

	private String customDivInfo;

	private String recrawlInfo;

	private String urlIgnoreFieldsNoSessionId;

	private Integer recrawlPeriod;

	private String userAgent;

	private String urlIgnoreFields;

	private Date creationTimestamp;

	private Integer followInternalNofollowInd;

	private Integer followRedirectsInd;

	private Long projectId;

	private String projectName;

	private Long recurringCrawlId;

	private Integer totalRecords;

	private Date alertLastSentTimestamp;

	private Integer alertSentCount;

	private Integer crawlChildOnly;

	private Integer bypassRobotFile;

	private Integer useScrapyCrawler;

	private Integer enableJavascript;

	private Integer maxPagesToCrawl;

	private Integer javascriptTimeoutInSeconds;

	private String allowDomains;

	private String denyDomains;

	private String urlPatternRegexAllow;

	private String urlPatternRegexDeny;

	private String urlsForCrawlOnly;

	private String urlsForIndexOnly;

	private String linkDiscoveryRestrictXpath;

	private String linkDiscoveryRestrictCss;

	private Integer dontCrawlLinksFound;

	private Integer version;

	private Integer launchLinodeInstance;

	private String ftpFileLocationPath;

	private Integer shareCloudServer;

	private Integer downloadHtml;

	private Integer generatePageLinks;

	private Integer crawlHreflang;

	private Integer crawlCanonical;

	private Integer extractHreflang;

	private Integer crawlPagination;

	private Integer extractPagination;

	private String cloudServerRegionCode;

	private Integer disableAlert;

	private Integer doNotLaunch;

	private String launchSpecificVersion;

	private Integer dailyCrawlLimit;

	private Integer crawlDelayInMilliseconds;

	private Integer dontRecordBlockedSites;

	private Integer cookiesEnabled;

	private String contentWordsExtractorIncludeJson;

	private String contentWordsExtractorExcludeJson;

	private String contentWordsExtractorSearchStringsJson;

	private String crawlRequestTime;
	
	private String alertEmails;

	private Integer crawlExternalLinks;

	private Integer enableClarityDb;

	private Integer state;
	
	private String reusableServer;

	private String language;

	private String ignoreResources;

	@Transient
	private String domainName;

	private String serverIpAddress;
	
	private int internalLinksPageRankFlg;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getCrawlStatus() {
		return crawlStatus;
	}

	public void setCrawlStatus(Integer crawlStatus) {
		this.crawlStatus = crawlStatus;
	}

	public Integer getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	public Integer getCrawlRequestDate() {
		return crawlRequestDate;
	}

	public void setCrawlRequestDate(Integer crawlRequestDate) {
		this.crawlRequestDate = crawlRequestDate;
	}

	public Integer getCrawlSpeed() {
		return crawlSpeed;
	}

	public void setCrawlSpeed(Integer crawlSpeed) {
		this.crawlSpeed = crawlSpeed;
	}

	public Integer getDepth() {
		return depth;
	}

	public void setDepth(Integer depth) {
		this.depth = depth;
	}

	public String getStartingUrl() {
		return startingUrl;
	}

	public void setStartingUrl(String startingUrl) {
		this.startingUrl = startingUrl;
	}

	public String getCrawlDescription() {
		return crawlDescription;
	}

	public void setCrawlDescription(String crawlDescription) {
		this.crawlDescription = crawlDescription;
	}

	public String getCrawlerIpAddress() {
		return crawlerIpAddress;
	}

	public void setCrawlerIpAddress(String crawlerIpAddress) {
		this.crawlerIpAddress = crawlerIpAddress;
	}

	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	public String getExclusions() {
		return exclusions;
	}

	public void setExclusions(String exclusions) {
		this.exclusions = exclusions;
	}

	public String getCustomDivInfo() {
		return customDivInfo;
	}

	public void setCustomDivInfo(String customDivInfo) {
		this.customDivInfo = customDivInfo;
	}

	public String getRecrawlInfo() {
		return recrawlInfo;
	}

	public void setRecrawlInfo(String recrawlInfo) {
		this.recrawlInfo = recrawlInfo;
	}

	public String getUrlIgnoreFieldsNoSessionId() {
		return urlIgnoreFieldsNoSessionId;
	}

	public void setUrlIgnoreFieldsNoSessionId(String urlIgnoreFieldsNoSessionId) {
		this.urlIgnoreFieldsNoSessionId = urlIgnoreFieldsNoSessionId;
	}

	public Integer getRecrawlPeriod() {
		return recrawlPeriod;
	}

	public void setRecrawlPeriod(Integer recrawlPeriod) {
		this.recrawlPeriod = recrawlPeriod;
	}

	public String getUserAgent() {
		return userAgent;
	}

	public void setUserAgent(String userAgent) {
		this.userAgent = userAgent;
	}

	public String getUrlIgnoreFields() {
		return urlIgnoreFields;
	}

	public void setUrlIgnoreFields(String urlIgnoreFields) {
		this.urlIgnoreFields = urlIgnoreFields;
	}

	public Date getCreationTimestamp() {
		return creationTimestamp;
	}

	public void setCreationTimestamp(Date creationTimestamp) {
		this.creationTimestamp = creationTimestamp;
	}

	public Integer getFollowInternalNofollowInd() {
		return followInternalNofollowInd;
	}

	public void setFollowInternalNofollowInd(Integer followInternalNofollowInd) {
		this.followInternalNofollowInd = followInternalNofollowInd;
	}

	public Integer getFollowRedirectsInd() {
		return followRedirectsInd;
	}

	public void setFollowRedirectsInd(Integer followRedirectsInd) {
		this.followRedirectsInd = followRedirectsInd;
	}

	public Long getProjectId() {
		return projectId;
	}

	public void setProjectId(Long projectId) {
		this.projectId = projectId;
	}

	public String getProjectName() {
		return projectName;
	}

	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}

	public Long getRecurringCrawlId() {
		return recurringCrawlId;
	}

	public void setRecurringCrawlId(Long recurringCrawlId) {
		this.recurringCrawlId = recurringCrawlId;
	}

	@Transient
	public Integer getTotalRecords() {
		return totalRecords;
	}

	@Transient
	public void setTotalRecords(Integer totalRecords) {
		this.totalRecords = totalRecords;
	}

	public Date getAlertLastSentTimestamp() {
		return alertLastSentTimestamp;
	}

	public void setAlertLastSentTimestamp(Date alertLastSentTimestamp) {
		this.alertLastSentTimestamp = alertLastSentTimestamp;
	}

	public Integer getAlertSentCount() {
		return alertSentCount;
	}

	public void setAlertSentCount(Integer alertSentCount) {
		this.alertSentCount = alertSentCount;
	}

	public Integer getCrawlChildOnly() {
		return crawlChildOnly;
	}

	public void setCrawlChildOnly(Integer crawlChildOnly) {
		this.crawlChildOnly = crawlChildOnly;
	}

	public Integer getBypassRobotFile() {
		return bypassRobotFile;
	}

	public void setBypassRobotFile(Integer bypassRobotFile) {
		this.bypassRobotFile = bypassRobotFile;
	}

	public Integer getUseScrapyCrawler() {
		return useScrapyCrawler;
	}

	public void setUseScrapyCrawler(Integer useScrapyCrawler) {
		this.useScrapyCrawler = useScrapyCrawler;
	}

	public Integer getEnableJavascript() {
		return enableJavascript;
	}

	public void setEnableJavascript(Integer enableJavascript) {
		this.enableJavascript = enableJavascript;
	}

	public Integer getMaxPagesToCrawl() {
		return maxPagesToCrawl;
	}

	public void setMaxPagesToCrawl(Integer maxPagesToCrawl) {
		this.maxPagesToCrawl = maxPagesToCrawl;
	}

	public Integer getJavascriptTimeoutInSeconds() {
		return javascriptTimeoutInSeconds;
	}

	public void setJavascriptTimeoutInSeconds(Integer javascriptTimeoutInSeconds) {
		this.javascriptTimeoutInSeconds = javascriptTimeoutInSeconds;
	}

	public String getAllowDomains() {
		return allowDomains;
	}

	public void setAllowDomains(String allowDomains) {
		this.allowDomains = allowDomains;
	}

	public String getDenyDomains() {
		return denyDomains;
	}

	public void setDenyDomains(String denyDomains) {
		this.denyDomains = denyDomains;
	}

	public String getUrlPatternRegexAllow() {
		return urlPatternRegexAllow;
	}

	public void setUrlPatternRegexAllow(String urlPatternRegexAllow) {
		this.urlPatternRegexAllow = urlPatternRegexAllow;
	}

	public String getUrlPatternRegexDeny() {
		return urlPatternRegexDeny;
	}

	public void setUrlPatternRegexDeny(String urlPatternRegexDeny) {
		this.urlPatternRegexDeny = urlPatternRegexDeny;
	}

	public String getUrlsForCrawlOnly() {
		return urlsForCrawlOnly;
	}

	public void setUrlsForCrawlOnly(String urlsForCrawlOnly) {
		this.urlsForCrawlOnly = urlsForCrawlOnly;
	}

	public String getUrlsForIndexOnly() {
		return urlsForIndexOnly;
	}

	public void setUrlsForIndexOnly(String urlsForIndexOnly) {
		this.urlsForIndexOnly = urlsForIndexOnly;
	}

	public String getLinkDiscoveryRestrictXpath() {
		return linkDiscoveryRestrictXpath;
	}

	public void setLinkDiscoveryRestrictXpath(String linkDiscoveryRestrictXpath) {
		this.linkDiscoveryRestrictXpath = linkDiscoveryRestrictXpath;
	}

	public String getLinkDiscoveryRestrictCss() {
		return linkDiscoveryRestrictCss;
	}

	public void setLinkDiscoveryRestrictCss(String linkDiscoveryRestrictCss) {
		this.linkDiscoveryRestrictCss = linkDiscoveryRestrictCss;
	}

	public Integer getDontCrawlLinksFound() {
		return dontCrawlLinksFound;
	}

	public void setDontCrawlLinksFound(Integer dontCrawlLinksFound) {
		this.dontCrawlLinksFound = dontCrawlLinksFound;
	}

	public Integer getVersion() {
		return version;
	}

	public void setVersion(Integer version) {
		this.version = version;
	}

	public Integer getLaunchLinodeInstance() {
		return launchLinodeInstance;
	}

	public void setLaunchLinodeInstance(Integer launchLinodeInstance) {
		this.launchLinodeInstance = launchLinodeInstance;
	}

	public String getFtpFileLocationPath() {
		return ftpFileLocationPath;
	}

	public void setFtpFileLocationPath(String ftpFileLocationPath) {
		this.ftpFileLocationPath = ftpFileLocationPath;
	}

	public Integer getShareCloudServer() {
		return shareCloudServer;
	}

	public void setShareCloudServer(Integer shareCloudServer) {
		this.shareCloudServer = shareCloudServer;
	}

	public Integer getDownloadHtml() {
		return downloadHtml;
	}

	public void setDownloadHtml(Integer downloadHtml) {
		this.downloadHtml = downloadHtml;
	}

	public Integer getGeneratePageLinks() {
		return generatePageLinks;
	}

	public void setGeneratePageLinks(Integer generatePageLinks) {
		this.generatePageLinks = generatePageLinks;
	}

	public Integer getCrawlHreflang() {
		return crawlHreflang;
	}

	public void setCrawlHreflang(Integer crawlHreflang) {
		this.crawlHreflang = crawlHreflang;
	}

	public Integer getCrawlCanonical() {
		return crawlCanonical;
	}

	public void setCrawlCanonical(Integer crawlCanonical) {
		this.crawlCanonical = crawlCanonical;
	}

	public Integer getExtractHreflang() {
		return extractHreflang;
	}

	public void setExtractHreflang(Integer extractHreflang) {
		this.extractHreflang = extractHreflang;
	}

	public Integer getCrawlPagination() {
		return crawlPagination;
	}

	public void setCrawlPagination(Integer crawlPagination) {
		this.crawlPagination = crawlPagination;
	}

	public Integer getExtractPagination() {
		return extractPagination;
	}

	public void setExtractPagination(Integer extractPagination) {
		this.extractPagination = extractPagination;
	}

	public String getCloudServerRegionCode() {
		return cloudServerRegionCode;
	}

	public void setCloudServerRegionCode(String cloudServerRegionCode) {
		this.cloudServerRegionCode = cloudServerRegionCode;
	}

	public Integer getDisableAlert() {
		return disableAlert;
	}

	public void setDisableAlert(Integer disableAlert) {
		this.disableAlert = disableAlert;
	}

	public Integer getDoNotLaunch() {
		return doNotLaunch;
	}

	public void setDoNotLaunch(Integer doNotLaunch) {
		this.doNotLaunch = doNotLaunch;
	}

	public String getLaunchSpecificVersion() {
		return launchSpecificVersion;
	}

	public void setLaunchSpecificVersion(String launchSpecificVersion) {
		this.launchSpecificVersion = launchSpecificVersion;
	}

	public Integer getDailyCrawlLimit() {
		return dailyCrawlLimit;
	}

	public void setDailyCrawlLimit(Integer dailyCrawlLimit) {
		this.dailyCrawlLimit = dailyCrawlLimit;
	}

	public Integer getCrawlDelayInMilliseconds() {
		return crawlDelayInMilliseconds;
	}

	public void setCrawlDelayInMilliseconds(Integer crawlDelayInMilliseconds) {
		this.crawlDelayInMilliseconds = crawlDelayInMilliseconds;
	}

	public Integer getDontRecordBlockedSites() {
		return dontRecordBlockedSites;
	}

	public void setDontRecordBlockedSites(Integer dontRecordBlockedSites) {
		this.dontRecordBlockedSites = dontRecordBlockedSites;
	}

	public Integer getCookiesEnabled() {
		return cookiesEnabled;
	}

	public void setCookiesEnabled(Integer cookiesEnabled) {
		this.cookiesEnabled = cookiesEnabled;
	}

	public String getContentWordsExtractorIncludeJson() {
		return contentWordsExtractorIncludeJson;
	}

	public void setContentWordsExtractorIncludeJson(String contentWordsExtractorIncludeJson) {
		this.contentWordsExtractorIncludeJson = contentWordsExtractorIncludeJson;
	}

	public String getContentWordsExtractorExcludeJson() {
		return contentWordsExtractorExcludeJson;
	}

	public void setContentWordsExtractorExcludeJson(String contentWordsExtractorExcludeJson) {
		this.contentWordsExtractorExcludeJson = contentWordsExtractorExcludeJson;
	}

	public String getContentWordsExtractorSearchStringsJson() {
		return contentWordsExtractorSearchStringsJson;
	}

	public void setContentWordsExtractorSearchStringsJson(String contentWordsExtractorSearchStringsJson) {
		this.contentWordsExtractorSearchStringsJson = contentWordsExtractorSearchStringsJson;
	}

	public String getCrawlRequestTime() {
		return crawlRequestTime;
	}

	public void setCrawlRequestTime(String crawlRequestTime) {
		this.crawlRequestTime = crawlRequestTime;
	}

	public String getAlertEmails() {
		return alertEmails;
	}

	public void setAlertEmails(String alertEmails) {
		this.alertEmails = alertEmails;
	}

	public Integer getCrawlExternalLinks() {
		return crawlExternalLinks;
	}

	public void setCrawlExternalLinks(Integer crawlExternalLinks) {
		this.crawlExternalLinks = crawlExternalLinks;
	}

	public Integer getEnableClarityDb() {
		return enableClarityDb;
	}

	public void setEnableClarityDb(Integer enableClarityDb) {
		this.enableClarityDb = enableClarityDb;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public String getReusableServer() {
		return reusableServer;
	}

	public void setReusableServer(String reusableServer) {
		this.reusableServer = reusableServer;
	}

	public String getLanguage() {
		return language;
	}

	public void setLanguage(String language) {
		this.language = language;
	}

	public String getIgnoreResources() {
		return ignoreResources;
	}

	public void setIgnoreResources(String ignoreResources) {
		this.ignoreResources = ignoreResources;
	}

	@Transient
	public String getDomainName() {
		return domainName;
	}

	@Transient
	public void setDomainName(String domainName) {
		this.domainName = domainName;
	}

	public String getServerIpAddress() {
		return serverIpAddress;
	}

	public void setServerIpAddress(String serverIpAddress) {
		this.serverIpAddress = serverIpAddress;
	}

	public int getInternalLinksPageRankFlg() {
		return internalLinksPageRankFlg;
	}

	public void setInternalLinksPageRankFlg(int internalLinksPageRankFlg) {
		this.internalLinksPageRankFlg = internalLinksPageRankFlg;
	}

	@Override
	public String toString() {
		return "CrawlRequestLog [id=" + id + ", crawlStatus=" + crawlStatus + ", ownDomainId=" + ownDomainId + ", userId=" + userId + ", crawlRequestDate="
				+ crawlRequestDate + ", crawlSpeed=" + crawlSpeed + ", depth=" + depth + ", startingUrl=" + startingUrl + ", crawlDescription=" + crawlDescription
				+ ", crawlerIpAddress=" + crawlerIpAddress + ", lastUpdateTime=" + lastUpdateTime + ", exclusions=" + exclusions + ", customDivInfo=" + customDivInfo
				+ ", recrawlInfo=" + recrawlInfo + ", urlIgnoreFieldsNoSessionId=" + urlIgnoreFieldsNoSessionId + ", recrawlPeriod=" + recrawlPeriod + ", userAgent="
				+ userAgent + ", urlIgnoreFields=" + urlIgnoreFields + ", creationTimestamp=" + creationTimestamp + ", followInternalNofollowInd="
				+ followInternalNofollowInd + ", followRedirectsInd=" + followRedirectsInd + ", projectId=" + projectId + ", projectName=" + projectName
				+ ", recurringCrawlId=" + recurringCrawlId + ", totalRecords=" + totalRecords + ", alertLastSentTimestamp=" + alertLastSentTimestamp
				+ ", alertSentCount=" + alertSentCount + ", crawlChildOnly=" + crawlChildOnly + ", bypassRobotFile=" + bypassRobotFile + ", useScrapyCrawler="
				+ useScrapyCrawler + ", enableJavascript=" + enableJavascript + ", maxPagesToCrawl=" + maxPagesToCrawl + ", javascriptTimeoutInSeconds="
				+ javascriptTimeoutInSeconds + ", allowDomains=" + allowDomains + ", denyDomains=" + denyDomains + ", urlPatternRegexAllow=" + urlPatternRegexAllow
				+ ", urlPatternRegexDeny=" + urlPatternRegexDeny + ", urlsForCrawlOnly=" + urlsForCrawlOnly + ", urlsForIndexOnly=" + urlsForIndexOnly
				+ ", linkDiscoveryRestrictXpath=" + linkDiscoveryRestrictXpath + ", linkDiscoveryRestrictCss=" + linkDiscoveryRestrictCss + ", dontCrawlLinksFound="
				+ dontCrawlLinksFound + ", version=" + version + ", launchLinodeInstance=" + launchLinodeInstance + ", ftpFileLocationPath=" + ftpFileLocationPath
				+ ", shareCloudServer=" + shareCloudServer + ", downloadHtml=" + downloadHtml + ", generatePageLinks=" + generatePageLinks + ", crawlHreflang="
				+ crawlHreflang + ", crawlCanonical=" + crawlCanonical + ", extractHreflang=" + extractHreflang + ", crawlPagination=" + crawlPagination
				+ ", extractPagination=" + extractPagination + ", cloudServerRegionCode=" + cloudServerRegionCode + ", disableAlert=" + disableAlert + ", doNotLaunch="
				+ doNotLaunch + ", launchSpecificVersion=" + launchSpecificVersion + ", dailyCrawlLimit=" + dailyCrawlLimit + ", crawlDelayInMilliseconds="
				+ crawlDelayInMilliseconds + ", dontRecordBlockedSites=" + dontRecordBlockedSites + ", cookiesEnabled=" + cookiesEnabled
				+ ", contentWordsExtractorIncludeJson=" + contentWordsExtractorIncludeJson + ", contentWordsExtractorExcludeJson=" + contentWordsExtractorExcludeJson
				+ ", contentWordsExtractorSearchStringsJson=" + contentWordsExtractorSearchStringsJson + ", crawlRequestTime=" + crawlRequestTime + ", alertEmails="
				+ alertEmails + ", crawlExternalLinks=" + crawlExternalLinks + ", enableClarityDb=" + enableClarityDb + ", state=" + state + ", reusableServer="
				+ reusableServer + ", language=" + language + ", ignoreResources=" + ignoreResources + ", domainName=" + domainName + ", serverIpAddress="
				+ serverIpAddress + ", internalLinksPageRankFlg=" + internalLinksPageRankFlg + "]";
	}

}
