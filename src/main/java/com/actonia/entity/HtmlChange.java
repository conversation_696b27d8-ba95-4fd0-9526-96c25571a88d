package com.actonia.entity;

import com.actonia.IConstants;
import com.actonia.content.guard.change.ChangeIndicatorEnum;
import com.actonia.content.guard.change.IndicatorStrategy;
import com.actonia.value.object.ChangeTrackingHashCdJson;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.Gson;
import lombok.Data;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

import static com.actonia.IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS;
import static com.actonia.IConstants.URL_TYPE_MANAGED;

@Data
public class HtmlChange {
	private static final Logger log = LogManager.getLogger(HtmlChange.class);

	@JsonIgnore
	private int orderIndex;
	private static final Gson gson = new Gson();
	@JsonFormat(pattern = DATE_FORMAT_YYYY_MM_DD_HH_MM_SS)
	private Date trackDate;
	private int urlType;
	private int domainId;
	private String url;
	private String url_hash;
	private String url_murmur_hash;
	private int chgId;
	private String change_indicator;
	private String change_type;
	private String severity;
	private int criticalFlg;
	@JsonIgnore
	private int prevResponseCode;
	@JsonIgnore
	private int currResponseCode;
	private String response_code_current;
	private String response_code_previous;
	private String prevValue;
	private String currValue;
	private String current_crawl_timestamp;
	private String previous_crawl_timestamp;
	@JsonIgnore
	private Date prevCrawlTimestamp;
	@JsonIgnore
	private Date currCrawlTimestamp;
	@JsonIgnore
	private Date createTimestamp;
	@JsonIgnore
	private Map<String, String> previousChangeTrackingHashCdJsonMap;
	@JsonIgnore
	private List<HtmlBigData> htmlBigData;

	public void setSeverityString() {
		if (this.criticalFlg == IConstants.CONTENT_GUARD_CHANGE_SEVERITY_CRITICAL) {
			this.severity = IConstants.SEVERITY_CRITICAL;
		} else if (this.criticalFlg == IConstants.CONTENT_GUARD_CHANGE_SEVERITY_HIGH) {
			this.severity = IConstants.SEVERITY_HIGH;
		} else if (this.criticalFlg == IConstants.CONTENT_GUARD_CHANGE_SEVERITY_MEDIUM) {
			this.severity = IConstants.SEVERITY_MEDIUM;
		} else if (this.criticalFlg == IConstants.CONTENT_GUARD_CHANGE_SEVERITY_LOW) {
			this.severity = IConstants.SEVERITY_LOW;
		}
	}

	public static List<HtmlChange> buildHtmlChange(UrlMetricsEntityV3 previous, HtmlClickHouseEntity current) throws Exception {
		final Date trackDate = current.getTrackDate();
		final Integer domainId = current.getDomainId();

		final ChangeTrackingHashCdJson[] changeTrackingHashCdJsonArray = previous.getChangeTrackingHashCdJsonArray();
		final Map<String, String> previousChangeTrackingHashCdJsonMap = new HashMap<>();
		for (ChangeTrackingHashCdJson changeTrackingHashCdJson : changeTrackingHashCdJsonArray == null ? new ChangeTrackingHashCdJson[0] : changeTrackingHashCdJsonArray) {
			if (previousChangeTrackingHashCdJsonMap.put(changeTrackingHashCdJson.getName(), changeTrackingHashCdJson.getValue()) != null) {
				throw new IllegalStateException("Duplicate key");
			}
		}
		final Date previousCrawlTimestamp = DateUtils.parseDate(previous.getCrawl_timestamp(), new String[]{DATE_FORMAT_YYYY_MM_DD_HH_MM_SS});
		final Date currentCrawlTimestamp = current.getCrawlTimestamp();
		final Date createTimestamp = new Date();
		final HtmlChange temp = new HtmlChange();
		temp.setUrlType(previous.getUrlType());
		temp.setCurrCrawlTimestamp(currentCrawlTimestamp);
		temp.setPrevCrawlTimestamp(previousCrawlTimestamp);
		final String previousResponseCode = previous.getResponse_code();
		temp.setPrevResponseCode(Integer.parseInt(previousResponseCode));
		temp.setCurrResponseCode(current.getHttpStatusCode());
		temp.setCreateTimestamp(createTimestamp);
		temp.setUrl(current.getUrl());
		temp.setTrackDate(trackDate);
		temp.setDomainId(domainId);
		temp.setPreviousChangeTrackingHashCdJsonMap(previousChangeTrackingHashCdJsonMap);
		List<HtmlChange> htmlChangeList = new ArrayList<>();
		try {
			for (ChangeIndicatorEnum changeIndicatorEnum : ChangeIndicatorEnum.values()) {
				final IndicatorStrategy strategy = changeIndicatorEnum.getStrategy();
				if (strategy.checkIndicatorChanged(current)) {
					final HtmlChange htmlChange = strategy.createHtmlChange(createHtmlChangeFromTemp(temp), previous, current);
					if (htmlChange == null) {
						continue;
					}
					htmlChange.setChgId(changeIndicatorEnum.getId());
					htmlChangeList.add(htmlChange);
				}
			}
		} catch (Exception e) {
			log.error("url: {}, error: {}\nprevious: {}, \ncurrent: {}", current.getUrl(), e.getMessage(), gson.toJson(previous), gson.toJson(current), e);
			throw new RuntimeException(e);
		}
		log.info("url: {}, htmlChangeList size: {}", current.getUrl(), htmlChangeList.size());

		return htmlChangeList;
	}

	private static HtmlChange createHtmlChangeFromTemp(HtmlChange temp) {
		final HtmlChange htmlChange = new HtmlChange();
		htmlChange.setTrackDate(temp.getTrackDate());
		htmlChange.setUrlType(temp.getUrlType());
		htmlChange.setDomainId(temp.getDomainId());
		htmlChange.setUrl_hash(temp.getUrl_hash());
		htmlChange.setUrl(temp.getUrl());
		htmlChange.setPrevResponseCode(temp.getPrevResponseCode());
		htmlChange.setCurrResponseCode(temp.getCurrResponseCode());
		htmlChange.setPrevCrawlTimestamp(temp.getPrevCrawlTimestamp());
		htmlChange.setCurrCrawlTimestamp(temp.getCurrCrawlTimestamp());
		htmlChange.setCreateTimestamp(temp.getCreateTimestamp());
		htmlChange.setPreviousChangeTrackingHashCdJsonMap(temp.getPreviousChangeTrackingHashCdJsonMap());
		return htmlChange;
	}

	public static HtmlChange createFromChangeInd(TargetUrlChangeIndClickHouseEntity changeIndEntity) {
		HtmlChange htmlChange = new HtmlChange();
		try {
			final Date trackDate = changeIndEntity.getTrackDate();
			final Integer domainId = changeIndEntity.getDomainId();
			final String url = changeIndEntity.getUrl();
			final String urlHash = changeIndEntity.getUrlHash();
			htmlChange.setTrackDate(trackDate);
			htmlChange.setUrlType(URL_TYPE_MANAGED);
			htmlChange.setDomainId(domainId);
			htmlChange.setUrl(url);
			htmlChange.setUrl_hash(urlHash);
			final ChangeIndicatorEnum indicatorEnum = ChangeIndicatorEnum.fromIndicator(changeIndEntity.getChangeIndicator());
			htmlChange.setChgId(indicatorEnum.getId());
			final String md5Prev = indicatorEnum.getStrategy().convertPrevValue(changeIndEntity);
			final String md5Curr = indicatorEnum.getStrategy().convertCurrValue(changeIndEntity);
			htmlChange.setPrevValue(md5Prev);
			htmlChange.setCurrValue(md5Curr);
			final String responseCodePrevious = changeIndEntity.getResponseCodePrevious();
			if (StringUtils.isNotBlank(responseCodePrevious)) {
				try {
					htmlChange.setPrevResponseCode(Integer.parseInt(responseCodePrevious));
				} catch (NumberFormatException e) {
					log.error("changeInd: {} set prevResponseCode failed.\nHtmlChange: {}\n error: {}", changeIndEntity, htmlChange, e.getMessage(), e);
				}
			}
			final String responseCodeCurrent = changeIndEntity.getResponseCodeCurrent();
			if (StringUtils.isNotBlank(responseCodeCurrent)) {
				try {
					htmlChange.setCurrResponseCode(Integer.parseInt(responseCodeCurrent));
				} catch (NumberFormatException e) {
					log.error("changeInd: {} set currResponseCode failed.\nHtmlChange: {}\n error: {}", changeIndEntity, htmlChange, e.getMessage(), e);
				}
			}
			htmlChange.setPrevCrawlTimestamp(changeIndEntity.getPreviousCrawlTimestamp());
			htmlChange.setCurrCrawlTimestamp(changeIndEntity.getCurrentCrawlTimestamp());
			htmlChange.setCreateTimestamp(new Date());
		} catch (Exception e) {
			log.error("changeInd: {}\nHtmlChange: {}\n error: {}", changeIndEntity.getChangeIndicator(), htmlChange, e.getMessage(), e);
			throw new RuntimeException(e);
		}
		return htmlChange;
	}
}