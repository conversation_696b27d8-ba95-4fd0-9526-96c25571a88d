package com.actonia.entity;

import javax.persistence.Transient;

public class AssociatedUrlEntity {
	private String hashCode;
	private String hostname;
	private String url;
	private int totalUrls;
	private int trackDate;
	private String languageCode; // solr
	private String domainIdLanguageCodeJson; // clickhouse
	private Integer protocol;

	public String getHashCode() {
		return hashCode;
	}

	public void setHashCode(String hashCode) {
		this.hashCode = hashCode;
	}

	public String getHostname() {
		return hostname;
	}

	public void setHostname(String hostname) {
		this.hostname = hostname;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	@Transient
	public int getTotalUrls() {
		return totalUrls;
	}

	@Transient
	public void setTotalUrls(int totalUrls) {
		this.totalUrls = totalUrls;
	}

	public int getTrackDate() {
		return trackDate;
	}

	public void setTrackDate(int trackDate) {
		this.trackDate = trackDate;
	}

	public String getLanguageCode() {
		return languageCode;
	}

	public void setLanguageCode(String languageCode) {
		this.languageCode = languageCode;
	}

	public Integer getProtocol() {
		return protocol;
	}

	public void setProtocol(Integer protocol) {
		this.protocol = protocol;
	}

	public String getDomainIdLanguageCodeJson() {
		return domainIdLanguageCodeJson;
	}

	public void setDomainIdLanguageCodeJson(String domainIdLanguageCodeJson) {
		this.domainIdLanguageCodeJson = domainIdLanguageCodeJson;
	}

	@Override
	public String toString() {
		return "AssociatedUrlEntity [hashCode=" + hashCode + ", hostname=" + hostname + ", url=" + url + ", totalUrls=" + totalUrls
				+ ", trackDate=" + trackDate + ", languageCode=" + languageCode + ", domainIdLanguageCodeJson=" + domainIdLanguageCodeJson + ", protocol=" + protocol
				+ "]";
	}

}