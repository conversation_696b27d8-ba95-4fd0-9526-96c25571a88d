package com.actonia.entity;

import com.actonia.IConstants;
import com.actonia.utils.CrawlerUtils;
import com.actonia.value.object.*;
import com.google.gson.Gson;
import lombok.Data;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.Transient;
import java.util.*;

@Data
public class NewHtmlClickHouseEntity implements Cloneable {
	List<HtmlBigData> htmlBigDataList;
	Map<String, String> bigDataMap;
	private Integer domainId;
	private String urlDomain;
	private String url;
	private Date trackDate;
	private PageAnalysisResult[] pageAnalysisResultArray;
	private Integer weekOfYear;
	private String urlHash;
	private String lowerCaseUrlHash;
	private CrawlerResponse crawlerResponse;
	private String changeTrackingHash;
	private Date crawlTimestamp;
	private String urlMurmurHash;
	private Date dailyDataCreationDate;
	private ChangeTrackingHashCdJson[] changeTrackingHashCdJsonArray;
	private String pageAnalysisResultsChgIndJson;
	private Integer totalRecords;
	private Date crawlRequestDate;
	private Integer internalLinkCount;
	private String pageAnalysisResultsReverse;
	private PageAnalysisFragments[] pageAnalysisFragmentsArray;
	private Boolean responseCodeChgInd;
	private Boolean titleChgInd;
	private Boolean titleAddedInd;
	private Boolean titleRemovedInd;
	private Boolean descriptionChgInd;
	private Boolean descriptionAddedInd;
	private Boolean descriptionRemovedInd;
	private Boolean h1ChgInd;
	private Boolean h1AddedInd;
	private Boolean h1RemovedInd;
	private Boolean h2ChgInd;
	private Boolean h2AddedInd;
	private Boolean h2RemovedInd;
	private Boolean robotsContentsChgInd;
	private Boolean robotsAddedInd;
	private Boolean robotsRemovedInd;
	private Boolean canonicalChgInd;
	private Boolean canonicalAddedInd;
	private Boolean canonicalRemovedInd;
	private Boolean viewportContentChgInd;
	private Boolean viewportAddedInd;
	private Boolean viewportRemovedInd;
	private Boolean customDataChgInd;
	private Boolean customDataAddedInd;
	private Boolean customDataRemovedInd;
	private int hasCustomData;
	private Date createTime;
	private Date previousCrawlTimestamp;
	// big data
	private String altImgList;
	private String alternateLinks;
	private String customData;
	private String h1;
	private String h2;
	private String hreflangLinks;
	private String insecureResources;
	private String ogMarkup;
	private String pageLink;
	private String redirectChain;
	private String responseHeaders;
	private String structuredData;
	private String pageAnalysisFragments;

	public static NewHtmlClickHouseEntity createFromHtmlClickHouseEntity(HtmlClickHouseEntity htmlClickHouseEntityCurrent) {
		NewHtmlClickHouseEntity newHtmlClickHouseEntity = new NewHtmlClickHouseEntity();
		HashMap<String, String> bigDataMap = new HashMap<>();
		List<HtmlBigData> htmlBigDataList = new ArrayList<>();
		final Gson gson = new Gson();
		final CrawlerResponse crawlerResponse = htmlClickHouseEntityCurrent.getCrawlerResponse();
		// create big data list
		// alt_img_list
		final AltImgList[] altImgList = crawlerResponse.getAlt_img_list();
		if (altImgList != null && altImgList.length > 0) {
			final String altImgListJson = gson.toJson(altImgList);
			if (StringUtils.isNotBlank(altImgListJson) && altImgListJson.length() > 2) {
				final String md5 = CrawlerUtils.getSortedCharactersHashCode(altImgListJson);
				newHtmlClickHouseEntity.setAltImgList(md5);
				final HtmlBigData htmlBigData = createHtmlBigData(htmlClickHouseEntityCurrent, md5, altImgListJson);
				htmlBigDataList.add(htmlBigData);
				bigDataMap.put(IConstants.ALT_IMG_LIST, md5);
			}
		}
		// alternate_links
		final AlternateLinks[] alternateLinks = crawlerResponse.getAlternate_links();
		if (alternateLinks != null && alternateLinks.length > 0) {
			final String alternateLinksJson = gson.toJson(alternateLinks);
			if (StringUtils.isNotBlank(alternateLinksJson) && alternateLinksJson.length() > 2) {
				final String md5 = CrawlerUtils.getSortedCharactersHashCode(alternateLinksJson);
				newHtmlClickHouseEntity.setAlternateLinks(md5);
				final HtmlBigData htmlBigData = createHtmlBigData(htmlClickHouseEntityCurrent, md5, alternateLinksJson);
				htmlBigDataList.add(htmlBigData);
				bigDataMap.put(IConstants.ALTERNATE_LINKS, md5);
			}
		}
		// custom_data
		final CustomData[] customData = crawlerResponse.getCustom_data();
		if (customData != null && customData.length > 0) {
			final String customDataJson = gson.toJson(customData);
			if (StringUtils.isNotBlank(customDataJson) && customDataJson.length() > 2) {
				final String md5 = CrawlerUtils.getSortedCharactersHashCode(customDataJson);
				newHtmlClickHouseEntity.setHasCustomData(1);
				final HtmlBigData htmlBigData = createHtmlBigData(htmlClickHouseEntityCurrent, md5, customDataJson);
				htmlBigDataList.add(htmlBigData);
				bigDataMap.put(IConstants.CUSTOM_DATA, md5);
			}
		}
//		// h1
//		final String[] h1 = crawlerResponse.getH1();
//		if (h1 != null && h1.length > 0) {
//			final String h1Json = gson.toJson(h1);
//			final String md5 = CrawlerUtils.getSortedCharactersHashCode(h1Json);
//			newHtmlClickHouseEntity.setH1(md5);
//			final HtmlBigData htmlBigData = createHtmlBigData(htmlClickHouseEntityCurrent, md5, h1Json);
//			htmlBigDataList.add(htmlBigData);
//			bigDataMap.put(IConstants.H1, md5);
//		}
//		// h2
//		final String[] h2 = crawlerResponse.getH2();
//		if (h2 != null && h2.length > 0) {
//			final String h2Json = gson.toJson(h2);
//			final String md5 = CrawlerUtils.getSortedCharactersHashCode(h2Json);
//			newHtmlClickHouseEntity.setH2(md5);
//			final HtmlBigData htmlBigData = createHtmlBigData(htmlClickHouseEntityCurrent, md5, h2Json);
//			htmlBigDataList.add(htmlBigData);
//			bigDataMap.put(IConstants.H2, md5);
//		}
		// hreflang_error
		final HreflangErrors hreflangErrors = crawlerResponse.getHreflang_errors();
		if (hreflangErrors!= null) {
			final String hreflangErrorsJson = gson.toJson(hreflangErrors);
			if (StringUtils.isNotBlank(hreflangErrorsJson) && hreflangErrorsJson.length() > 2) {
				final String md5 = CrawlerUtils.getSortedCharactersHashCode(hreflangErrorsJson);
				newHtmlClickHouseEntity.setHreflangLinks(md5);
				final HtmlBigData htmlBigData = createHtmlBigData(htmlClickHouseEntityCurrent, md5, hreflangErrorsJson);
				htmlBigDataList.add(htmlBigData);
				bigDataMap.put(IConstants.HREFLANG_ERRORS, md5);
			}
		}
		//hreflang_links
		final HreflangLinks[] hreflangLinks = crawlerResponse.getHreflang_links();
		if (hreflangLinks != null && hreflangLinks.length > 0) {
			final String hreflangLinksJson = gson.toJson(hreflangLinks);
			if (StringUtils.isNotBlank(hreflangLinksJson) && hreflangLinksJson.length() > 2) {
				final String md5 = CrawlerUtils.getSortedCharactersHashCode(hreflangLinksJson);
				newHtmlClickHouseEntity.setHreflangLinks(md5);
				final HtmlBigData htmlBigData = createHtmlBigData(htmlClickHouseEntityCurrent, md5, hreflangLinksJson);
				htmlBigDataList.add(htmlBigData);
				bigDataMap.put(IConstants.HREFLANG_LINKS, md5);
			}
		}
		// insecure_resources
		final String[] inSecureResources = crawlerResponse.getInsecure_resources();
		if (inSecureResources != null && inSecureResources.length > 0) {
			final String insecureResourcesJson = gson.toJson(inSecureResources);
			if (StringUtils.isNotBlank(insecureResourcesJson) && insecureResourcesJson.length() > 2) {
				final String md5 = CrawlerUtils.getSortedCharactersHashCode(insecureResourcesJson);
				newHtmlClickHouseEntity.setInsecureResources(md5);
				final HtmlBigData htmlBigData = createHtmlBigData(htmlClickHouseEntityCurrent, md5, insecureResourcesJson);
				htmlBigDataList.add(htmlBigData);
				bigDataMap.put(IConstants.INSECURE_RESOURCES, md5);
			}
		}

		// og_markup
		final OgMarkup[] ogMarkup = crawlerResponse.getOg_markup();
		if (ogMarkup != null && ogMarkup.length > 0) {
			final String ogMarkupJson = gson.toJson(ogMarkup);
			if (StringUtils.isNotBlank(ogMarkupJson) && ogMarkupJson.length() > 2) {
				final String md5 = CrawlerUtils.getSortedCharactersHashCode(ogMarkupJson);
				newHtmlClickHouseEntity.setOgMarkup(md5);
				final HtmlBigData htmlBigData = createHtmlBigData(htmlClickHouseEntityCurrent, md5, ogMarkupJson);
				htmlBigDataList.add(htmlBigData);
				bigDataMap.put(IConstants.OG_MARKUP, md5);
			}
		}

		// page_link
		final PageLink[] pageLink = crawlerResponse.getPage_link();
		if (pageLink != null && pageLink.length > 0) {
			final String pageLinkJson = gson.toJson(pageLink);
			if (StringUtils.isNotBlank(pageLinkJson) && pageLinkJson.length() > 2) {
				final String md5 = CrawlerUtils.getSortedCharactersHashCode(pageLinkJson);
				newHtmlClickHouseEntity.setPageLink(md5);
				final HtmlBigData htmlBigData = createHtmlBigData(htmlClickHouseEntityCurrent, md5, pageLinkJson);
				htmlBigDataList.add(htmlBigData);
				bigDataMap.put(IConstants.PAGE_LINK, md5);
			}
		}
		// redirect_chain
		final RedirectChain[] redirectChain = crawlerResponse.getRedirect_chain();
		if (redirectChain != null && redirectChain.length > 0) {
			final String redirectChainJson = gson.toJson(redirectChain);
			if (StringUtils.isNotBlank(redirectChainJson) && redirectChainJson.length() > 2) {
				final String md5 = CrawlerUtils.getSortedCharactersHashCode(redirectChainJson);
				newHtmlClickHouseEntity.setRedirectChain(md5);
				final HtmlBigData htmlBigData = createHtmlBigData(htmlClickHouseEntityCurrent, md5, redirectChainJson);
				htmlBigDataList.add(htmlBigData);
				bigDataMap.put(IConstants.REDIRECT_CHAIN, md5);
			}
		}
		// response_headers
		final ResponseHeaders[] responseHeaders = crawlerResponse.getResponse_headers();
		if (responseHeaders != null && responseHeaders.length > 0) {
			final String responseHeadersJson = gson.toJson(responseHeaders);
			if (StringUtils.isNotBlank(responseHeadersJson) && responseHeadersJson.length() > 2) {
				final String md5 = CrawlerUtils.getSortedCharactersHashCode(responseHeadersJson);
				newHtmlClickHouseEntity.setResponseHeaders(md5);
				final HtmlBigData htmlBigData = createHtmlBigData(htmlClickHouseEntityCurrent, md5, responseHeadersJson);
				htmlBigDataList.add(htmlBigData);
				bigDataMap.put(IConstants.RESPONSE_HEADERS, md5);
			}
		}
		// structured_data
		final StructuredData structuredData = crawlerResponse.getStructured_data();
		if (structuredData != null) {
			final String structuredDataJson = gson.toJson(structuredData);
			if (StringUtils.isNotBlank(structuredDataJson) && structuredDataJson.length() > 2) {
				final String md5 = CrawlerUtils.getSortedCharactersHashCode(structuredDataJson);
				newHtmlClickHouseEntity.setStructuredData(md5);
				final HtmlBigData htmlBigData = createHtmlBigData(htmlClickHouseEntityCurrent, md5, structuredDataJson);
				htmlBigDataList.add(htmlBigData);
			}
		}
		// page_analysis_fragments
		final PageAnalysisFragments[] pageAnalysisFragmentsArray = htmlClickHouseEntityCurrent.getPageAnalysisFragmentsArray();
		if (pageAnalysisFragmentsArray != null && pageAnalysisFragmentsArray.length > 0) {
			final String pageAnalysisFragmentsJson = gson.toJson(pageAnalysisFragmentsArray);
			final String md5 = CrawlerUtils.getSortedCharactersHashCode(pageAnalysisFragmentsJson);
			newHtmlClickHouseEntity.setPageAnalysisFragments(md5);
			final HtmlBigData htmlBigData = createHtmlBigData(htmlClickHouseEntityCurrent, md5, pageAnalysisFragmentsJson);
			htmlBigDataList.add(htmlBigData);
			bigDataMap.put(IConstants.PAGE_ANALYSIS_FRAGMENTS, md5);
		}

		newHtmlClickHouseEntity.setDomainId(htmlClickHouseEntityCurrent.getDomainId());
		newHtmlClickHouseEntity.setUrl(htmlClickHouseEntityCurrent.getUrl());
		newHtmlClickHouseEntity.setUrlMurmurHash(htmlClickHouseEntityCurrent.getUrlMurmurHash());
		newHtmlClickHouseEntity.setTrackDate(htmlClickHouseEntityCurrent.getTrackDate());
		newHtmlClickHouseEntity.setCrawlerResponse(crawlerResponse);
		newHtmlClickHouseEntity.setCrawlTimestamp(htmlClickHouseEntityCurrent.getCrawlTimestamp());
		newHtmlClickHouseEntity.setUrlMurmurHash(htmlClickHouseEntityCurrent.getUrlMurmurHash());
		newHtmlClickHouseEntity.setChangeTrackingHashCdJsonArray(htmlClickHouseEntityCurrent.getChangeTrackingHashCdJsonArray());
		newHtmlClickHouseEntity.setPageAnalysisResultsChgIndJson(htmlClickHouseEntityCurrent.getPageAnalysisResultsChgIndJson());
		newHtmlClickHouseEntity.setPageAnalysisResultsReverse(htmlClickHouseEntityCurrent.getPageAnalysisResultsReverse());
		newHtmlClickHouseEntity.setPageAnalysisFragmentsArray(htmlClickHouseEntityCurrent.getPageAnalysisFragmentsArray());
		newHtmlClickHouseEntity.setPageAnalysisFragmentsArray(pageAnalysisFragmentsArray);
		newHtmlClickHouseEntity.setInternalLinkCount(htmlClickHouseEntityCurrent.getInternalLinkCount());
		newHtmlClickHouseEntity.setPreviousCrawlTimestamp(htmlClickHouseEntityCurrent.getPreviousCrawlTimestamp());
		newHtmlClickHouseEntity.setWeekOfYear(htmlClickHouseEntityCurrent.getWeekOfYear());
		// big data list
		newHtmlClickHouseEntity.setHtmlBigDataList(htmlBigDataList);
		newHtmlClickHouseEntity.setBigDataMap(bigDataMap);
		return newHtmlClickHouseEntity;
	}

	public static HtmlBigData createHtmlBigData(HtmlClickHouseEntity htmlClickHouseEntity, String md5, String value) {
		final HtmlBigData htmlBigData = new HtmlBigData();
		htmlBigData.setUrl(htmlClickHouseEntity.getUrl());
		htmlBigData.setTrackDate(htmlClickHouseEntity.getTrackDate());
		htmlBigData.setUrlMurmurHash(htmlClickHouseEntity.getUrlMurmurHash());
		htmlBigData.setMd5(md5);
		htmlBigData.setRawData(value);
		return htmlBigData;
	}

	@Override
	public NewHtmlClickHouseEntity clone() throws CloneNotSupportedException {
		return (NewHtmlClickHouseEntity) super.clone();
	}

	@Transient
	public Integer getHttpStatusCode() {
		Integer httpStatusCode = null;
		if (crawlerResponse != null && NumberUtils.isNumber(crawlerResponse.getResponse_code())) {
			httpStatusCode = NumberUtils.toInt(crawlerResponse.getResponse_code());
		}
		return httpStatusCode;
	}
}
