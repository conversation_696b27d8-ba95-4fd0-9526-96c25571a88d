package com.actonia.entity;

import java.math.BigDecimal;

public class UserBacklink {
	
	public static int PARTNER_URL_RELATIONSHIP_STATUS_POTENTIAL = 2;
	
	private Long id;
	private String md5Hash;
	private int competitorId;
	private Byte targetProtocol;
	private String targetDomain;
	private byte partnerProtocol;
	private String partnerDomain;
	private String topLevelPartnerDomain;
	private String anchorTxt;
	private Byte acRank;
	private Integer lastCrawlDate;
	private Byte flagRedirect;
	private Byte flagFrame;
	private Byte flagNoFollow;
	private Byte flagImages;
	private Byte flagDeleted;
	private Byte flagAltText;
	private Byte flagMention;
	private Integer firstIndexedDate;
	private Integer lastSeenDate;
	private Integer dateLost;
	private String reasonLost;
	private Byte linkType;
	private Byte linkSubType;
	private Byte targetCitationFlow;
	private Byte targetTrustFlow;
	private Byte partnerCitationFlow;
	private Byte partnerTrustFlow;
	private Integer clarityLastCrawlDate;
	private Short partnerUrlStatusCd;
	private Short targetUrlStatusCd;
	private Integer partnerUrlInboundLinksCount;
	private Integer partnerUrlOutboundLinksCount;
	private Byte ageOfDomain;
	private Byte srcInd;
	private Integer semrushKeywordCount;
	private Byte seomozPageAuthority;
	private Byte pageRank;
	private Byte seomozMozRank;
	private int creationDate;
	private Integer partnerUrlStatusUpdtDt;
	private Integer targetUrlStatusUpdtDt;
	private Byte partnerUrlRelationshipStatus;
	private Byte partnerUrlLinkStatus;
	private Byte partnerUrlAcquisitionSrc;
	private BigDecimal partnerUrlRating;
	private Integer partnerUrlCostInterval;
	private BigDecimal partnerUrlAcquisitionCost;
	private Integer partnerUrlAcquisitionType;
	private String partnerUrlAcquisitionOther;
	private Integer partnerUrlDateStartPayment;
	private Integer partnerUrlWarningPrvalue;
	private Integer partnerUrlSiteWideLink;
	private Integer partnerUrlMozInbounds;
	private BigDecimal partnerUrlMozRank;
	private BigDecimal partnerUrlPageAuthority;
	private long lastUpdateTimestamp;
	private String targetUrlMd5Hash;
	private Byte targetHomepageInd;
	private String targetUrl;
	private Byte partnerHomepageInd;
	private String partnerUrl;
	private Integer partnerUrlAssignedTo;
	private Integer googleWebmaster;
	private String partnerUrlContactInfo;
	private String partnerUrlMd5Hash;
	private String recommendedAnchorTxt;
	private String recommendedTargetUrl;

	public UserBacklink() {
	}

	public UserBacklink(String md5Hash, int competitorId, byte partnerProtocol, String partnerDomain, String topLevelPartnerDomain, int creationDate,
			long lastUpdateTimestamp) {
		this.md5Hash = md5Hash;
		this.competitorId = competitorId;
		this.partnerProtocol = partnerProtocol;
		this.partnerDomain = partnerDomain;
		this.topLevelPartnerDomain = topLevelPartnerDomain;
		this.creationDate = creationDate;
		this.lastUpdateTimestamp = lastUpdateTimestamp;
	}

	public UserBacklink(String md5Hash, int competitorId, Byte targetProtocol, String targetDomain, byte partnerProtocol, String partnerDomain,
			String topLevelPartnerDomain, String anchorTxt, Byte acRank, Integer lastCrawlDate, Byte flagRedirect, Byte flagFrame, Byte flagNoFollow,
			Byte flagImages, Byte flagDeleted, Byte flagAltText, Byte flagMention, Integer firstIndexedDate, Integer lastSeenDate, Integer dateLost,
			String reasonLost, Byte linkType, Byte linkSubType, Byte targetCitationFlow, Byte targetTrustFlow, Byte partnerCitationFlow,
			Byte partnerTrustFlow, Integer clarityLastCrawlDate, Short partnerUrlStatusCd, Short targetUrlStatusCd,
			Integer partnerUrlInboundLinksCount, Integer partnerUrlOutboundLinksCount, Byte ageOfDomain, Byte srcInd, Integer semrushKeywordCount,
			Byte seomozPageAuthority, Byte pageRank, Byte seomozMozRank, int creationDate, Integer partnerUrlStatusUpdtDt,
			Integer targetUrlStatusUpdtDt, Byte partnerUrlRelationshipStatus, Byte partnerUrlLinkStatus, Byte partnerUrlAcquisitionSrc,
			BigDecimal partnerUrlRating, Integer partnerUrlCostInterval, BigDecimal partnerUrlAcquisitionCost, Integer partnerUrlAcquisitionType,
			String partnerUrlAcquisitionOther, Integer partnerUrlDateStartPayment, Integer partnerUrlWarningPrvalue, Integer partnerUrlSiteWideLink,
			Integer partnerUrlMozInbounds, BigDecimal partnerUrlMozRank, BigDecimal partnerUrlPageAuthority, long lastUpdateTimestamp,
			String targetUrlMd5Hash, Byte targetHomepageInd, String targetUrl, Byte partnerHomepageInd, String partnerUrl,
			Integer partnerUrlAssignedTo, Integer googleWebmaster, String partnerUrlContactInfo, String partnerUrlMd5Hash,
			String recommendedAnchorTxt, String recommendedTargetUrl) {
		this.md5Hash = md5Hash;
		this.competitorId = competitorId;
		this.targetProtocol = targetProtocol;
		this.targetDomain = targetDomain;
		this.partnerProtocol = partnerProtocol;
		this.partnerDomain = partnerDomain;
		this.topLevelPartnerDomain = topLevelPartnerDomain;
		this.anchorTxt = anchorTxt;
		this.acRank = acRank;
		this.lastCrawlDate = lastCrawlDate;
		this.flagRedirect = flagRedirect;
		this.flagFrame = flagFrame;
		this.flagNoFollow = flagNoFollow;
		this.flagImages = flagImages;
		this.flagDeleted = flagDeleted;
		this.flagAltText = flagAltText;
		this.flagMention = flagMention;
		this.firstIndexedDate = firstIndexedDate;
		this.lastSeenDate = lastSeenDate;
		this.dateLost = dateLost;
		this.reasonLost = reasonLost;
		this.linkType = linkType;
		this.linkSubType = linkSubType;
		this.targetCitationFlow = targetCitationFlow;
		this.targetTrustFlow = targetTrustFlow;
		this.partnerCitationFlow = partnerCitationFlow;
		this.partnerTrustFlow = partnerTrustFlow;
		this.clarityLastCrawlDate = clarityLastCrawlDate;
		this.partnerUrlStatusCd = partnerUrlStatusCd;
		this.targetUrlStatusCd = targetUrlStatusCd;
		this.partnerUrlInboundLinksCount = partnerUrlInboundLinksCount;
		this.partnerUrlOutboundLinksCount = partnerUrlOutboundLinksCount;
		this.ageOfDomain = ageOfDomain;
		this.srcInd = srcInd;
		this.semrushKeywordCount = semrushKeywordCount;
		this.seomozPageAuthority = seomozPageAuthority;
		this.pageRank = pageRank;
		this.seomozMozRank = seomozMozRank;
		this.creationDate = creationDate;
		this.partnerUrlStatusUpdtDt = partnerUrlStatusUpdtDt;
		this.targetUrlStatusUpdtDt = targetUrlStatusUpdtDt;
		this.partnerUrlRelationshipStatus = partnerUrlRelationshipStatus;
		this.partnerUrlLinkStatus = partnerUrlLinkStatus;
		this.partnerUrlAcquisitionSrc = partnerUrlAcquisitionSrc;
		this.partnerUrlRating = partnerUrlRating;
		this.partnerUrlCostInterval = partnerUrlCostInterval;
		this.partnerUrlAcquisitionCost = partnerUrlAcquisitionCost;
		this.partnerUrlAcquisitionType = partnerUrlAcquisitionType;
		this.partnerUrlAcquisitionOther = partnerUrlAcquisitionOther;
		this.partnerUrlDateStartPayment = partnerUrlDateStartPayment;
		this.partnerUrlWarningPrvalue = partnerUrlWarningPrvalue;
		this.partnerUrlSiteWideLink = partnerUrlSiteWideLink;
		this.partnerUrlMozInbounds = partnerUrlMozInbounds;
		this.partnerUrlMozRank = partnerUrlMozRank;
		this.partnerUrlPageAuthority = partnerUrlPageAuthority;
		this.lastUpdateTimestamp = lastUpdateTimestamp;
		this.targetUrlMd5Hash = targetUrlMd5Hash;
		this.targetHomepageInd = targetHomepageInd;
		this.targetUrl = targetUrl;
		this.partnerHomepageInd = partnerHomepageInd;
		this.partnerUrl = partnerUrl;
		this.partnerUrlAssignedTo = partnerUrlAssignedTo;
		this.googleWebmaster = googleWebmaster;
		this.partnerUrlContactInfo = partnerUrlContactInfo;
		this.partnerUrlMd5Hash = partnerUrlMd5Hash;
		this.recommendedAnchorTxt = recommendedAnchorTxt;
		this.recommendedTargetUrl = recommendedTargetUrl;
	}

	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getMd5Hash() {
		return this.md5Hash;
	}

	public void setMd5Hash(String md5Hash) {
		this.md5Hash = md5Hash;
	}

	public int getCompetitorId() {
		return this.competitorId;
	}

	public void setCompetitorId(int competitorId) {
		this.competitorId = competitorId;
	}

	public Byte getTargetProtocol() {
		return this.targetProtocol;
	}

	public void setTargetProtocol(Byte targetProtocol) {
		this.targetProtocol = targetProtocol;
	}

	public String getTargetDomain() {
		return this.targetDomain;
	}

	public void setTargetDomain(String targetDomain) {
		this.targetDomain = targetDomain;
	}

	public byte getPartnerProtocol() {
		return this.partnerProtocol;
	}

	public void setPartnerProtocol(byte partnerProtocol) {
		this.partnerProtocol = partnerProtocol;
	}

	public String getPartnerDomain() {
		return this.partnerDomain;
	}

	public void setPartnerDomain(String partnerDomain) {
		this.partnerDomain = partnerDomain;
	}

	public String getTopLevelPartnerDomain() {
		return this.topLevelPartnerDomain;
	}

	public void setTopLevelPartnerDomain(String topLevelPartnerDomain) {
		this.topLevelPartnerDomain = topLevelPartnerDomain;
	}

	public String getAnchorTxt() {
		return this.anchorTxt;
	}

	public void setAnchorTxt(String anchorTxt) {
		this.anchorTxt = anchorTxt;
	}

	public Byte getAcRank() {
		return this.acRank;
	}

	public void setAcRank(Byte acRank) {
		this.acRank = acRank;
	}

	public Integer getLastCrawlDate() {
		return this.lastCrawlDate;
	}

	public void setLastCrawlDate(Integer lastCrawlDate) {
		this.lastCrawlDate = lastCrawlDate;
	}

	public Byte getFlagRedirect() {
		return this.flagRedirect;
	}

	public void setFlagRedirect(Byte flagRedirect) {
		this.flagRedirect = flagRedirect;
	}

	public Byte getFlagFrame() {
		return this.flagFrame;
	}

	public void setFlagFrame(Byte flagFrame) {
		this.flagFrame = flagFrame;
	}

	public Byte getFlagNoFollow() {
		return this.flagNoFollow;
	}

	public void setFlagNoFollow(Byte flagNoFollow) {
		this.flagNoFollow = flagNoFollow;
	}

	public Byte getFlagImages() {
		return this.flagImages;
	}

	public void setFlagImages(Byte flagImages) {
		this.flagImages = flagImages;
	}

	public Byte getFlagDeleted() {
		return this.flagDeleted;
	}

	public void setFlagDeleted(Byte flagDeleted) {
		this.flagDeleted = flagDeleted;
	}

	public Byte getFlagAltText() {
		return this.flagAltText;
	}

	public void setFlagAltText(Byte flagAltText) {
		this.flagAltText = flagAltText;
	}

	public Byte getFlagMention() {
		return this.flagMention;
	}

	public void setFlagMention(Byte flagMention) {
		this.flagMention = flagMention;
	}

	public Integer getFirstIndexedDate() {
		return this.firstIndexedDate;
	}

	public void setFirstIndexedDate(Integer firstIndexedDate) {
		this.firstIndexedDate = firstIndexedDate;
	}

	public Integer getLastSeenDate() {
		return this.lastSeenDate;
	}

	public void setLastSeenDate(Integer lastSeenDate) {
		this.lastSeenDate = lastSeenDate;
	}

	public Integer getDateLost() {
		return this.dateLost;
	}

	public void setDateLost(Integer dateLost) {
		this.dateLost = dateLost;
	}

	public String getReasonLost() {
		return this.reasonLost;
	}

	public void setReasonLost(String reasonLost) {
		this.reasonLost = reasonLost;
	}

	public Byte getLinkType() {
		return this.linkType;
	}

	public void setLinkType(Byte linkType) {
		this.linkType = linkType;
	}

	public Byte getLinkSubType() {
		return this.linkSubType;
	}

	public void setLinkSubType(Byte linkSubType) {
		this.linkSubType = linkSubType;
	}

	public Byte getTargetCitationFlow() {
		return this.targetCitationFlow;
	}

	public void setTargetCitationFlow(Byte targetCitationFlow) {
		this.targetCitationFlow = targetCitationFlow;
	}

	public Byte getTargetTrustFlow() {
		return this.targetTrustFlow;
	}

	public void setTargetTrustFlow(Byte targetTrustFlow) {
		this.targetTrustFlow = targetTrustFlow;
	}

	public Byte getPartnerCitationFlow() {
		return this.partnerCitationFlow;
	}

	public void setPartnerCitationFlow(Byte partnerCitationFlow) {
		this.partnerCitationFlow = partnerCitationFlow;
	}

	public Byte getPartnerTrustFlow() {
		return this.partnerTrustFlow;
	}

	public void setPartnerTrustFlow(Byte partnerTrustFlow) {
		this.partnerTrustFlow = partnerTrustFlow;
	}

	public Integer getClarityLastCrawlDate() {
		return this.clarityLastCrawlDate;
	}

	public void setClarityLastCrawlDate(Integer clarityLastCrawlDate) {
		this.clarityLastCrawlDate = clarityLastCrawlDate;
	}

	public Short getPartnerUrlStatusCd() {
		return this.partnerUrlStatusCd;
	}

	public void setPartnerUrlStatusCd(Short partnerUrlStatusCd) {
		this.partnerUrlStatusCd = partnerUrlStatusCd;
	}

	public Short getTargetUrlStatusCd() {
		return this.targetUrlStatusCd;
	}

	public void setTargetUrlStatusCd(Short targetUrlStatusCd) {
		this.targetUrlStatusCd = targetUrlStatusCd;
	}

	public Integer getPartnerUrlInboundLinksCount() {
		return this.partnerUrlInboundLinksCount;
	}

	public void setPartnerUrlInboundLinksCount(Integer partnerUrlInboundLinksCount) {
		this.partnerUrlInboundLinksCount = partnerUrlInboundLinksCount;
	}

	public Integer getPartnerUrlOutboundLinksCount() {
		return this.partnerUrlOutboundLinksCount;
	}

	public void setPartnerUrlOutboundLinksCount(Integer partnerUrlOutboundLinksCount) {
		this.partnerUrlOutboundLinksCount = partnerUrlOutboundLinksCount;
	}

	public Byte getAgeOfDomain() {
		return this.ageOfDomain;
	}

	public void setAgeOfDomain(Byte ageOfDomain) {
		this.ageOfDomain = ageOfDomain;
	}

	public Byte getSrcInd() {
		return this.srcInd;
	}

	public void setSrcInd(Byte srcInd) {
		this.srcInd = srcInd;
	}

	public Integer getSemrushKeywordCount() {
		return this.semrushKeywordCount;
	}

	public void setSemrushKeywordCount(Integer semrushKeywordCount) {
		this.semrushKeywordCount = semrushKeywordCount;
	}

	public Byte getSeomozPageAuthority() {
		return this.seomozPageAuthority;
	}

	public void setSeomozPageAuthority(Byte seomozPageAuthority) {
		this.seomozPageAuthority = seomozPageAuthority;
	}

	public Byte getPageRank() {
		return this.pageRank;
	}

	public void setPageRank(Byte pageRank) {
		this.pageRank = pageRank;
	}

	public Byte getSeomozMozRank() {
		return this.seomozMozRank;
	}

	public void setSeomozMozRank(Byte seomozMozRank) {
		this.seomozMozRank = seomozMozRank;
	}

	public int getCreationDate() {
		return this.creationDate;
	}

	public void setCreationDate(int creationDate) {
		this.creationDate = creationDate;
	}

	public Integer getPartnerUrlStatusUpdtDt() {
		return this.partnerUrlStatusUpdtDt;
	}

	public void setPartnerUrlStatusUpdtDt(Integer partnerUrlStatusUpdtDt) {
		this.partnerUrlStatusUpdtDt = partnerUrlStatusUpdtDt;
	}

	public Integer getTargetUrlStatusUpdtDt() {
		return this.targetUrlStatusUpdtDt;
	}

	public void setTargetUrlStatusUpdtDt(Integer targetUrlStatusUpdtDt) {
		this.targetUrlStatusUpdtDt = targetUrlStatusUpdtDt;
	}

	public Byte getPartnerUrlRelationshipStatus() {
		return this.partnerUrlRelationshipStatus;
	}

	public void setPartnerUrlRelationshipStatus(Byte partnerUrlRelationshipStatus) {
		this.partnerUrlRelationshipStatus = partnerUrlRelationshipStatus;
	}

	public Byte getPartnerUrlLinkStatus() {
		return this.partnerUrlLinkStatus;
	}

	public void setPartnerUrlLinkStatus(Byte partnerUrlLinkStatus) {
		this.partnerUrlLinkStatus = partnerUrlLinkStatus;
	}

	public Byte getPartnerUrlAcquisitionSrc() {
		return this.partnerUrlAcquisitionSrc;
	}

	public void setPartnerUrlAcquisitionSrc(Byte partnerUrlAcquisitionSrc) {
		this.partnerUrlAcquisitionSrc = partnerUrlAcquisitionSrc;
	}

	public BigDecimal getPartnerUrlRating() {
		return this.partnerUrlRating;
	}

	public void setPartnerUrlRating(BigDecimal partnerUrlRating) {
		this.partnerUrlRating = partnerUrlRating;
	}

	public Integer getPartnerUrlCostInterval() {
		return this.partnerUrlCostInterval;
	}

	public void setPartnerUrlCostInterval(Integer partnerUrlCostInterval) {
		this.partnerUrlCostInterval = partnerUrlCostInterval;
	}

	public BigDecimal getPartnerUrlAcquisitionCost() {
		return this.partnerUrlAcquisitionCost;
	}

	public void setPartnerUrlAcquisitionCost(BigDecimal partnerUrlAcquisitionCost) {
		this.partnerUrlAcquisitionCost = partnerUrlAcquisitionCost;
	}

	public Integer getPartnerUrlAcquisitionType() {
		return this.partnerUrlAcquisitionType;
	}

	public void setPartnerUrlAcquisitionType(Integer partnerUrlAcquisitionType) {
		this.partnerUrlAcquisitionType = partnerUrlAcquisitionType;
	}

	public String getPartnerUrlAcquisitionOther() {
		return this.partnerUrlAcquisitionOther;
	}

	public void setPartnerUrlAcquisitionOther(String partnerUrlAcquisitionOther) {
		this.partnerUrlAcquisitionOther = partnerUrlAcquisitionOther;
	}

	public Integer getPartnerUrlDateStartPayment() {
		return this.partnerUrlDateStartPayment;
	}

	public void setPartnerUrlDateStartPayment(Integer partnerUrlDateStartPayment) {
		this.partnerUrlDateStartPayment = partnerUrlDateStartPayment;
	}

	public Integer getPartnerUrlWarningPrvalue() {
		return this.partnerUrlWarningPrvalue;
	}

	public void setPartnerUrlWarningPrvalue(Integer partnerUrlWarningPrvalue) {
		this.partnerUrlWarningPrvalue = partnerUrlWarningPrvalue;
	}

	public Integer getPartnerUrlSiteWideLink() {
		return this.partnerUrlSiteWideLink;
	}

	public void setPartnerUrlSiteWideLink(Integer partnerUrlSiteWideLink) {
		this.partnerUrlSiteWideLink = partnerUrlSiteWideLink;
	}

	public Integer getPartnerUrlMozInbounds() {
		return this.partnerUrlMozInbounds;
	}

	public void setPartnerUrlMozInbounds(Integer partnerUrlMozInbounds) {
		this.partnerUrlMozInbounds = partnerUrlMozInbounds;
	}

	public BigDecimal getPartnerUrlMozRank() {
		return this.partnerUrlMozRank;
	}

	public void setPartnerUrlMozRank(BigDecimal partnerUrlMozRank) {
		this.partnerUrlMozRank = partnerUrlMozRank;
	}

	public BigDecimal getPartnerUrlPageAuthority() {
		return this.partnerUrlPageAuthority;
	}

	public void setPartnerUrlPageAuthority(BigDecimal partnerUrlPageAuthority) {
		this.partnerUrlPageAuthority = partnerUrlPageAuthority;
	}

	public long getLastUpdateTimestamp() {
		return this.lastUpdateTimestamp;
	}

	public void setLastUpdateTimestamp(long lastUpdateTimestamp) {
		this.lastUpdateTimestamp = lastUpdateTimestamp;
	}

	public String getTargetUrlMd5Hash() {
		return this.targetUrlMd5Hash;
	}

	public void setTargetUrlMd5Hash(String targetUrlMd5Hash) {
		this.targetUrlMd5Hash = targetUrlMd5Hash;
	}

	public Byte getTargetHomepageInd() {
		return this.targetHomepageInd;
	}

	public void setTargetHomepageInd(Byte targetHomepageInd) {
		this.targetHomepageInd = targetHomepageInd;
	}

	public String getTargetUrl() {
		return this.targetUrl;
	}

	public void setTargetUrl(String targetUrl) {
		this.targetUrl = targetUrl;
	}

	public Byte getPartnerHomepageInd() {
		return this.partnerHomepageInd;
	}

	public void setPartnerHomepageInd(Byte partnerHomepageInd) {
		this.partnerHomepageInd = partnerHomepageInd;
	}

	public String getPartnerUrl() {
		return this.partnerUrl;
	}

	public void setPartnerUrl(String partnerUrl) {
		this.partnerUrl = partnerUrl;
	}

	public Integer getPartnerUrlAssignedTo() {
		return this.partnerUrlAssignedTo;
	}

	public void setPartnerUrlAssignedTo(Integer partnerUrlAssignedTo) {
		this.partnerUrlAssignedTo = partnerUrlAssignedTo;
	}

	public Integer getGoogleWebmaster() {
		return this.googleWebmaster;
	}

	public void setGoogleWebmaster(Integer googleWebmaster) {
		this.googleWebmaster = googleWebmaster;
	}

	public String getPartnerUrlContactInfo() {
		return this.partnerUrlContactInfo;
	}

	public void setPartnerUrlContactInfo(String partnerUrlContactInfo) {
		this.partnerUrlContactInfo = partnerUrlContactInfo;
	}

	public String getPartnerUrlMd5Hash() {
		return this.partnerUrlMd5Hash;
	}

	public void setPartnerUrlMd5Hash(String partnerUrlMd5Hash) {
		this.partnerUrlMd5Hash = partnerUrlMd5Hash;
	}

	public String getRecommendedAnchorTxt() {
		return this.recommendedAnchorTxt;
	}

	public void setRecommendedAnchorTxt(String recommendedAnchorTxt) {
		this.recommendedAnchorTxt = recommendedAnchorTxt;
	}

	public String getRecommendedTargetUrl() {
		return this.recommendedTargetUrl;
	}

	public void setRecommendedTargetUrl(String recommendedTargetUrl) {
		this.recommendedTargetUrl = recommendedTargetUrl;
	}

}
