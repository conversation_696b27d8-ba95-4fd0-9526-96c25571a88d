package com.actonia.entity;

import java.util.Date;

public class LinkGainLossClickHouseEntity implements Cloneable {
	private Integer domainId;
	private Date gainLossDate;
	private int gainLossFlag;
	private String sourceUrl;
	private String targetUrl;
	private String anchorText;
	private int linkType;
	private int targetUrlHttpStatusCode;

	@Override
	public LinkGainLossClickHouseEntity clone() throws CloneNotSupportedException {
		return (LinkGainLossClickHouseEntity) super.clone();
	}

	public Integer getDomainId() {
		return domainId;
	}

	public void setDomainId(Integer domainId) {
		this.domainId = domainId;
	}

	public Date getGainLossDate() {
		return gainLossDate;
	}

	public void setGainLossDate(Date gainLossDate) {
		this.gainLossDate = gainLossDate;
	}

	public int getGainLossFlag() {
		return gainLossFlag;
	}

	public void setGainLossFlag(int gainLossFlag) {
		this.gainLossFlag = gainLossFlag;
	}

	public String getSourceUrl() {
		return sourceUrl;
	}

	public void setSourceUrl(String sourceUrl) {
		this.sourceUrl = sourceUrl;
	}

	public String getTargetUrl() {
		return targetUrl;
	}

	public void setTargetUrl(String targetUrl) {
		this.targetUrl = targetUrl;
	}

	public int getTargetUrlHttpStatusCode() {
		return targetUrlHttpStatusCode;
	}

	public void setTargetUrlHttpStatusCode(int targetUrlHttpStatusCode) {
		this.targetUrlHttpStatusCode = targetUrlHttpStatusCode;
	}

	public String getAnchorText() {
		return anchorText;
	}

	public void setAnchorText(String anchorText) {
		this.anchorText = anchorText;
	}

	public int getLinkType() {
		return linkType;
	}

	public void setLinkType(int linkType) {
		this.linkType = linkType;
	}

	@Override
	public String toString() {
		return "LinkGainLossClickHouseEntity [domainId=" + domainId + ", gainLossDate=" + gainLossDate + ", gainLossFlag=" + gainLossFlag + ", sourceUrl=" + sourceUrl
				+ ", targetUrl=" + targetUrl + ", anchorText=" + anchorText + ", linkType=" + linkType + ", targetUrlHttpStatusCode=" + targetUrlHttpStatusCode + "]";
	}

}
