/**
 * 
 */
package com.actonia.entity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * com.actonia.subserver.entity.UrlMetricsDataEntity.java
 * 
 * @version $Revision: 93045 $ $Author: <PERSON>@SHINETECHCHINA $
 */
public class UrlMetricsDataEntity {

	public static final Integer TYPE_TARGET_URL = 1;

	public static final Integer TYPE_PARTNER_URL = 2;

	public static final Integer TYPE_COMPETITOR_URL = 3;

	public static final Integer TYPE_COMPETITOR_URL_BACKLINKS = 4;

	public static final Integer TYPE_PARTNER = 5;

	public static final Integer ROBOT_META_NOT_EXIST = 0;

	public static final Integer ROBOT_MEAT_EXIST = 1;

	private Integer id;

	private String url;

	private Long urlId;

	private Integer type;

	private Integer pagerank;

	private Integer inbounds;

	private Date cachedDate;

	private Integer respCode;

	private String ipaddress;

	private Date lastUpdate;

	private Date prUpdateDate;

	private Date yahooUpdateDate;

	private Date cacheDateUpdateDate;

	private Date htmlContentUpdateDate;

	private Integer outbounds;

	private Date semrushUpdateDate;

	private Integer internalLinks;

	private Integer googleIndex;

	private String mozRank;

	private Float pageAuthority;

	private Date seomozUpdateDate;

	private Integer mozInbounds;

	private Integer robotMeta;

	private Integer links;

	private String domainAuthority;

	private Date majesticsUpdateDate;

	private Integer acRank;

	private Date respcodeUpdateDate;

	private Date pagespeedUpdateDate;

	private BigDecimal percentMobileEntrances;

	private Integer associatedKeywords;

	private Integer associatedKeywordsRanked;

	private Integer linksAcquiredOrganically;

	private Integer linksAcquiredManually;

	private Integer associatedCompetitors;

	public Date getMajesticsUpdateDate() {
		return majesticsUpdateDate;
	}

	public void setMajesticsUpdateDate(Date majesticsUpdateDate) {
		this.majesticsUpdateDate = majesticsUpdateDate;
	}

	public Integer getAcRank() {
		return acRank;
	}

	public void setAcRank(Integer acRank) {
		this.acRank = acRank;
	}

	public Integer getInternalLinks() {
		return internalLinks;
	}

	public void setInternalLinks(Integer internalLinks) {
		this.internalLinks = internalLinks;
	}

	public Integer getRobotMeta() {
		return robotMeta;
	}

	public void setRobotMeta(Integer robotMeta) {
		this.robotMeta = robotMeta;
	}

	public Integer getMozInbounds() {
		return mozInbounds;
	}

	public void setMozInbounds(Integer mozInbounds) {
		this.mozInbounds = mozInbounds;
	}

	public String getMozRank() {
		return mozRank;
	}

	public void setMozRank(String mozRank) {
		this.mozRank = mozRank;
	}

	public Date getSeomozUpdateDate() {
		return seomozUpdateDate;
	}

	public void setSeomozUpdateDate(Date seomozUpdateDate) {
		this.seomozUpdateDate = seomozUpdateDate;
	}

	public Integer getGoogleIndex() {
		return googleIndex;
	}

	public void setGoogleIndex(Integer googleIndex) {
		this.googleIndex = googleIndex;
	}

	public Date getSemrushUpdateDate() {
		return semrushUpdateDate;
	}

	public void setSemrushUpdateDate(Date semrushUpdateDate) {
		this.semrushUpdateDate = semrushUpdateDate;
	}

	public Integer getOutbounds() {
		return outbounds;
	}

	public void setOutbounds(Integer outbounds) {
		this.outbounds = outbounds;
	}

	public Date getHtmlContentUpdateDate() {
		return htmlContentUpdateDate;
	}

	public void setHtmlContentUpdateDate(Date htmlContentUpdateDate) {
		this.htmlContentUpdateDate = htmlContentUpdateDate;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public Long getUrlId() {
		return urlId;
	}

	public void setUrlId(Long urlId) {
		this.urlId = urlId;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Integer getPagerank() {
		return pagerank;
	}

	public void setPagerank(Integer pagerank) {
		this.pagerank = pagerank;
	}

	public Integer getInbounds() {
		return inbounds;
	}

	public void setInbounds(Integer inbounds) {
		this.inbounds = inbounds;
	}

	public Date getCachedDate() {
		return cachedDate;
	}

	public void setCachedDate(Date cachedDate) {
		this.cachedDate = cachedDate;
	}

	public Integer getRespCode() {
		return respCode;
	}

	public void setRespCode(Integer respCode) {
		this.respCode = respCode;
	}

	public String getIpaddress() {
		return ipaddress;
	}

	public void setIpaddress(String ipaddress) {
		this.ipaddress = ipaddress;
	}

	public Date getLastUpdate() {
		return lastUpdate;
	}

	public void setLastUpdate(Date lastUpdate) {
		this.lastUpdate = lastUpdate;
	}

	public Date getPrUpdateDate() {
		return prUpdateDate;
	}

	public void setPrUpdateDate(Date prUpdateDate) {
		this.prUpdateDate = prUpdateDate;
	}

	public Date getYahooUpdateDate() {
		return yahooUpdateDate;
	}

	public void setYahooUpdateDate(Date yahooUpdateDate) {
		this.yahooUpdateDate = yahooUpdateDate;
	}

	public Date getCacheDateUpdateDate() {
		return cacheDateUpdateDate;
	}

	public void setCacheDateUpdateDate(Date cacheDateUpdateDate) {
		this.cacheDateUpdateDate = cacheDateUpdateDate;
	}

	public Integer getLinks() {
		return links;
	}

	public void setLinks(Integer links) {
		this.links = links;
	}

	public String getDomainAuthority() {
		return domainAuthority;
	}

	public void setDomainAuthority(String domainAuthority) {
		this.domainAuthority = domainAuthority;
	}

	public Date getRespcodeUpdateDate() {
		return respcodeUpdateDate;
	}

	public void setRespcodeUpdateDate(Date respcodeUpdateDate) {
		this.respcodeUpdateDate = respcodeUpdateDate;
	}

	public Date getPagespeedUpdateDate() {
		return pagespeedUpdateDate;
	}

	public void setPagespeedUpdateDate(Date pagespeedUpdateDate) {
		this.pagespeedUpdateDate = pagespeedUpdateDate;
	}

	public Float getPageAuthority() {
		return pageAuthority;
	}

	public void setPageAuthority(Float pageAuthority) {
		this.pageAuthority = pageAuthority;
	}

	public BigDecimal getPercentMobileEntrances() {
		return percentMobileEntrances;
	}

	public void setPercentMobileEntrances(BigDecimal percentMobileEntrances) {
		this.percentMobileEntrances = percentMobileEntrances;
	}

	public Integer getAssociatedKeywords() {
		return associatedKeywords;
	}

	public void setAssociatedKeywords(Integer associatedKeywords) {
		this.associatedKeywords = associatedKeywords;
	}

	public Integer getAssociatedKeywordsRanked() {
		return associatedKeywordsRanked;
	}

	public void setAssociatedKeywordsRanked(Integer associatedKeywordsRanked) {
		this.associatedKeywordsRanked = associatedKeywordsRanked;
	}

	public Integer getLinksAcquiredOrganically() {
		return linksAcquiredOrganically;
	}

	public void setLinksAcquiredOrganically(Integer linksAcquiredOrganically) {
		this.linksAcquiredOrganically = linksAcquiredOrganically;
	}

	public Integer getLinksAcquiredManually() {
		return linksAcquiredManually;
	}

	public void setLinksAcquiredManually(Integer linksAcquiredManually) {
		this.linksAcquiredManually = linksAcquiredManually;
	}

	public Integer getAssociatedCompetitors() {
		return associatedCompetitors;
	}

	public void setAssociatedCompetitors(Integer associatedCompetitors) {
		this.associatedCompetitors = associatedCompetitors;
	}

	@Override
	public String toString() {
		return " UrlMetricsDataEntity [id=" + id + ", url=" + url + ", urlId=" + urlId + ", type=" + type + ", pagerank=" + pagerank + ", inbounds="
				+ inbounds + ", cachedDate=" + cachedDate + ", respCode=" + respCode + ", ipaddress=" + ipaddress + ", lastUpdate=" + lastUpdate
				+ ", prUpdateDate=" + prUpdateDate + ", yahooUpdateDate=" + yahooUpdateDate + ", cacheDateUpdateDate=" + cacheDateUpdateDate
				+ ", htmlContentUpdateDate=" + htmlContentUpdateDate + ", outbounds=" + outbounds + ", semrushUpdateDate=" + semrushUpdateDate
				+ ", internalLinks=" + internalLinks + ", googleIndex=" + googleIndex + ", mozRank=" + mozRank + ", pageAuthority=" + pageAuthority
				+ ", seomozUpdateDate=" + seomozUpdateDate + ", mozInbounds=" + mozInbounds + ", robotMeta=" + robotMeta + ", links=" + links
				+ ", domainAuthority=" + domainAuthority + ", majesticsUpdateDate=" + majesticsUpdateDate + ", acRank=" + acRank
				+ ", respcodeUpdateDate=" + respcodeUpdateDate + ", pagespeedUpdateDate=" + pagespeedUpdateDate + ", percentMobileEntrances="
				+ percentMobileEntrances + ", associatedKeywords=" + associatedKeywords + ", associatedKeywordsRanked=" + associatedKeywordsRanked
				+ ", linksAcquiredOrganically=" + linksAcquiredOrganically + ", linksAcquiredManually=" + linksAcquiredManually
				+ ", associatedCompetitors=" + associatedCompetitors + "]";
	}

}
