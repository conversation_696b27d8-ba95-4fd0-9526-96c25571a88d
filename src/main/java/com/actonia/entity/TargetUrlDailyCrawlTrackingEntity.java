package com.actonia.entity;

public class TargetUrlDailyCrawlTrackingEntity {
	private int processType;
	private int domainId;
	private String domainName;
	private int trackDate;
	private int totalUrls;
	private String updateTargetUrlChangeTimestamp;

	public int getProcessType() {
		return processType;
	}

	public void setProcessType(int processType) {
		this.processType = processType;
	}

	public int getDomainId() {
		return domainId;
	}

	public void setDomainId(int domainId) {
		this.domainId = domainId;
	}

	public String getDomainName() {
		return domainName;
	}

	public void setDomainName(String domainName) {
		this.domainName = domainName;
	}

	public int getTrackDate() {
		return trackDate;
	}

	public void setTrackDate(int trackDate) {
		this.trackDate = trackDate;
	}

	public int getTotalUrls() {
		return totalUrls;
	}

	public void setTotalUrls(int totalUrls) {
		this.totalUrls = totalUrls;
	}

	public String getUpdateTargetUrlChangeTimestamp() {
		return updateTargetUrlChangeTimestamp;
	}

	public void setUpdateTargetUrlChangeTimestamp(String updateTargetUrlChangeTimestamp) {
		this.updateTargetUrlChangeTimestamp = updateTargetUrlChangeTimestamp;
	}

	@Override
	public String toString() {
		return "TargetUrlDailyCrawlTrackingEntity [processType=" + processType + ", domainId=" + domainId + ", domainName=" + domainName + ", trackDate=" + trackDate
				+ ", totalUrls=" + totalUrls + ", updateTargetUrlChangeTimestamp=" + updateTargetUrlChangeTimestamp + "]";
	}

}