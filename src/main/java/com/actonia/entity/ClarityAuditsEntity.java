package com.actonia.entity;

import java.util.Date;

public class ClarityAuditsEntity {
	private long id;
	private Integer ownDomainId;
	private Date crawlDate;
	private long crawlDateLong;
	private Integer totalPage;
	private Integer duplicateTitle;
	private Integer duplicateMetaDesc;
	private Integer duplicateH1;
	private Integer duplicateContent;
	private Integer response200;
	private Integer response404;
	private Integer response301;
	private Integer response302;
	private Integer response503;
	private Integer responseOther;
	private Integer detailDataExists;
	private Integer depth;
	private Integer crawlRequestLogId;
	private Long projectId;
	private Integer respCode2xx;
	private Integer respCode3xx;
	private Integer respCode4xx;
	private Integer respCode5xx;
	private Integer respCodeXxx;
	private String hreflangPostProcessingRulesJson;
	private String canonicalPostProcessingRulesJson;
	private String pageAnalysisRulesJson;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public Integer getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public Date getCrawlDate() {
		return crawlDate;
	}

	public void setCrawlDate(Date crawlDate) {
		this.crawlDate = crawlDate;
	}

	public long getCrawlDateLong() {
		return crawlDateLong;
	}

	public void setCrawlDateLong(long crawlDateLong) {
		this.crawlDateLong = crawlDateLong;
	}

	public Integer getTotalPage() {
		return totalPage;
	}

	public void setTotalPage(Integer totalPage) {
		this.totalPage = totalPage;
	}

	public Integer getDuplicateTitle() {
		return duplicateTitle;
	}

	public void setDuplicateTitle(Integer duplicateTitle) {
		this.duplicateTitle = duplicateTitle;
	}

	public Integer getDuplicateMetaDesc() {
		return duplicateMetaDesc;
	}

	public void setDuplicateMetaDesc(Integer duplicateMetaDesc) {
		this.duplicateMetaDesc = duplicateMetaDesc;
	}

	public Integer getDuplicateH1() {
		return duplicateH1;
	}

	public void setDuplicateH1(Integer duplicateH1) {
		this.duplicateH1 = duplicateH1;
	}

	public Integer getDuplicateContent() {
		return duplicateContent;
	}

	public void setDuplicateContent(Integer duplicateContent) {
		this.duplicateContent = duplicateContent;
	}

	public Integer getResponse200() {
		return response200;
	}

	public void setResponse200(Integer response200) {
		this.response200 = response200;
	}

	public Integer getResponse404() {
		return response404;
	}

	public void setResponse404(Integer response404) {
		this.response404 = response404;
	}

	public Integer getResponse301() {
		return response301;
	}

	public void setResponse301(Integer response301) {
		this.response301 = response301;
	}

	public Integer getResponse302() {
		return response302;
	}

	public void setResponse302(Integer response302) {
		this.response302 = response302;
	}

	public Integer getResponse503() {
		return response503;
	}

	public void setResponse503(Integer response503) {
		this.response503 = response503;
	}

	public Integer getResponseOther() {
		return responseOther;
	}

	public void setResponseOther(Integer responseOther) {
		this.responseOther = responseOther;
	}

	public Integer getDetailDataExists() {
		return detailDataExists;
	}

	public void setDetailDataExists(Integer detailDataExists) {
		this.detailDataExists = detailDataExists;
	}

	public Integer getDepth() {
		return depth;
	}

	public void setDepth(Integer depth) {
		this.depth = depth;
	}

	public Integer getCrawlRequestLogId() {
		return crawlRequestLogId;
	}

	public void setCrawlRequestLogId(Integer crawlRequestLogId) {
		this.crawlRequestLogId = crawlRequestLogId;
	}

	public Long getProjectId() {
		return projectId;
	}

	public void setProjectId(Long projectId) {
		this.projectId = projectId;
	}

	public Integer getRespCode2xx() {
		return respCode2xx;
	}

	public void setRespCode2xx(Integer respCode2xx) {
		this.respCode2xx = respCode2xx;
	}

	public Integer getRespCode3xx() {
		return respCode3xx;
	}

	public void setRespCode3xx(Integer respCode3xx) {
		this.respCode3xx = respCode3xx;
	}

	public Integer getRespCode4xx() {
		return respCode4xx;
	}

	public void setRespCode4xx(Integer respCode4xx) {
		this.respCode4xx = respCode4xx;
	}

	public Integer getRespCode5xx() {
		return respCode5xx;
	}

	public void setRespCode5xx(Integer respCode5xx) {
		this.respCode5xx = respCode5xx;
	}

	public Integer getRespCodeXxx() {
		return respCodeXxx;
	}

	public void setRespCodeXxx(Integer respCodeXxx) {
		this.respCodeXxx = respCodeXxx;
	}

	public String getHreflangPostProcessingRulesJson() {
		return hreflangPostProcessingRulesJson;
	}

	public void setHreflangPostProcessingRulesJson(String hreflangPostProcessingRulesJson) {
		this.hreflangPostProcessingRulesJson = hreflangPostProcessingRulesJson;
	}

	public String getCanonicalPostProcessingRulesJson() {
		return canonicalPostProcessingRulesJson;
	}

	public void setCanonicalPostProcessingRulesJson(String canonicalPostProcessingRulesJson) {
		this.canonicalPostProcessingRulesJson = canonicalPostProcessingRulesJson;
	}

	public String getPageAnalysisRulesJson() {
		return pageAnalysisRulesJson;
	}

	public void setPageAnalysisRulesJson(String pageAnalysisRulesJson) {
		this.pageAnalysisRulesJson = pageAnalysisRulesJson;
	}

	@Override
	public String toString() {
		return "ClarityAuditsEntity [id=" + id + ", ownDomainId=" + ownDomainId + ", crawlDate=" + crawlDate + ", crawlDateLong=" + crawlDateLong + ", totalPage="
				+ totalPage + ", duplicateTitle=" + duplicateTitle + ", duplicateMetaDesc=" + duplicateMetaDesc + ", duplicateH1=" + duplicateH1 + ", duplicateContent="
				+ duplicateContent + ", response200=" + response200 + ", response404=" + response404 + ", response301=" + response301 + ", response302=" + response302
				+ ", response503=" + response503 + ", responseOther=" + responseOther + ", detailDataExists=" + detailDataExists + ", depth=" + depth
				+ ", crawlRequestLogId=" + crawlRequestLogId + ", projectId=" + projectId + ", respCode2xx=" + respCode2xx + ", respCode3xx=" + respCode3xx
				+ ", respCode4xx=" + respCode4xx + ", respCode5xx=" + respCode5xx + ", respCodeXxx=" + respCodeXxx + ", hreflangPostProcessingRulesJson="
				+ hreflangPostProcessingRulesJson + ", canonicalPostProcessingRulesJson=" + canonicalPostProcessingRulesJson + ", pageAnalysisRulesJson="
				+ pageAnalysisRulesJson + "]";
	}

}
