package com.actonia.entity;

import java.util.Arrays;
import java.util.Date;

import javax.persistence.Transient;

import lombok.Data;
import org.apache.commons.lang.math.NumberUtils;

import com.actonia.value.object.ChangeTrackingHashCdJson;
import com.actonia.value.object.CrawlerResponse;
import com.actonia.value.object.PageAnalysisFragments;
import com.actonia.value.object.PageAnalysisResult;

@Data
public class HtmlClickHouseEntity implements Cloneable {
	private Integer domainId;
	private String urlDomain;
	private String url;
	private Date trackDate;
	private PageAnalysisResult[] pageAnalysisResultArray;
	private Integer weekOfYear;
	private String urlHash;
	private String lowerCaseUrlHash;
	private CrawlerResponse crawlerResponse;
	private String changeTrackingHash;
	private Date crawlTimestamp;
	private String urlMurmurHash;
	private Date dailyDataCreationDate;
	private Boolean alternateLinksChgInd;
	private Boolean amphtmlFlagChgInd;
	private Boolean amphtmlHrefChgInd;
	private Boolean analyzedUrlFlgSChgInd;
	private Boolean analyzedUrlSChgInd;
	private Boolean archiveFlgChgInd;
	private Boolean archiveFlgXTagChgInd;
	private Boolean blockedByRobotsChgInd;
	private Boolean canonicalChgInd;
	private Boolean canonicalFlgChgInd;
	private Boolean canonicalHeaderFlagChgInd;
	private Boolean canonicalHeaderTypeChgInd;
	private Boolean canonicalTypeChgInd;
	private Boolean canonicalUrlIsConsistentChgInd;
	private Boolean contentTypeChgInd;
	private Boolean descriptionChgInd;
	private Boolean descriptionFlgChgInd;
	private Boolean descriptionLengthChgInd;
	private Boolean descriptionSimhashChgInd;
	private Boolean errorMessageChgInd;
	private Boolean finalResponseCodeChgInd;
	private Boolean followFlgChgInd;
	private Boolean followFlgXTagChgInd;
	private Boolean h1ChgInd;
	private Boolean h1CountChgInd;
	private Boolean h1FlgChgInd;
	private Boolean h1LengthChgInd;
	private Boolean h1Md5ChgInd;
	private Boolean headerNoarchiveChgInd;
	private Boolean headerNofollowChgInd;
	private Boolean headerNoindexChgInd;
	private Boolean headerNoodpChgInd;
	private Boolean headerNosnippetChgInd;
	private Boolean headerNoydirChgInd;
	private Boolean hreflangErrorsChgInd;
	private Boolean hreflangLinksChgInd;
	private Boolean hreflangLinksOutCountChgInd;
	private Boolean hreflangUrlCountChgInd;
	private Boolean indexFlgChgInd;
	private Boolean indexFlgXTagChgInd;
	private Boolean indexableChgInd;
	private Boolean insecureResourcesChgInd;
	private Boolean insecureResourcesFlagChgInd;
	private Boolean metaCharsetChgInd;
	private Boolean metaContentTypeChgInd;
	private Boolean metaDisabledSitelinksChgInd;
	private Boolean metaNoodpChgInd;
	private Boolean metaNosnippetChgInd;
	private Boolean metaNoydirChgInd;
	private Boolean metaRedirectChgInd;
	private Boolean mixedRedirectsChgInd;
	private Boolean mobileRelAlternateUrlIsConsistentChgInd;
	private Boolean noodpChgInd;
	private Boolean nosnippetChgInd;
	private Boolean noydirChgInd;
	private Boolean ogMarkupChgInd;
	private Boolean ogMarkupFlagChgInd;
	private Boolean ogMarkupLengthChgInd;
	private Boolean outlinkCountChgInd;
	private Boolean pageLinkChgInd;
	private Boolean redirectBlockedChgInd;
	private Boolean redirectBlockedReasonChgInd;
	private Boolean redirectChainChgInd;
	private Boolean redirectFinalUrlChgInd;
	private Boolean redirectFlgChgInd;
	private Boolean redirectTimesChgInd;
	private Boolean responseCodeChgInd;
	private Boolean robotsChgInd;
	private Boolean robotsContentsChgInd;
	private Boolean robotsContentsXTagChgInd;
	private Boolean robotsFlgChgInd;
	private Boolean robotsFlgXTagChgInd;
	private Boolean structuredDataChgInd;
	private Boolean titleChgInd;
	private Boolean titleFlgChgInd;
	private Boolean titleLengthChgInd;
	private Boolean titleMd5ChgInd;
	private Boolean titleSimhashChgInd;
	private Boolean viewportContentChgInd;
	private Boolean viewportFlagChgInd;
	private ChangeTrackingHashCdJson[] changeTrackingHashCdJsonArray;
	private Integer sign;
	private String pageAnalysisResultsChgIndJson;
	private Integer totalRecords;
	private Date crawlRequestDate;

	private Boolean canonicalAddedInd;
	private Boolean canonicalRemovedInd;
	private Boolean descriptionAddedInd;
	private Boolean descriptionRemovedInd;
	private Boolean h1AddedInd;
	private Boolean h1RemovedInd;
	private Boolean h2AddedInd;
	private Boolean h2ChgInd;
	private Boolean h2RemovedInd;
	private Boolean hreflangLinksAddedInd;
	private Boolean hreflangLinksRemovedInd;
	private Boolean openGraphAddedInd;
	private Boolean openGraphRemovedInd;
	private Boolean responseCode404DetectedInd;
	private Boolean responseCode404RemovedInd;
	private Boolean redirect301ChgInd;
	private Boolean redirect301DetectedInd;
	private Boolean redirect301RemovedInd;
	private Boolean redirect302ChgInd;
	private Boolean redirect302DetectedInd;
	private Boolean redirect302RemovedInd;
	private Boolean redirectDiffCodeInd;
	private Boolean robotsAddedInd;
	private Boolean robotsRemovedInd;
	private Boolean titleAddedInd;
	private Boolean titleRemovedInd;
	private Boolean viewportAddedInd;
	private Boolean viewportRemovedInd;
	private Integer internalLinkCount;
	private Boolean customDataChgInd;
	private String pageAnalysisResultsReverse;
	private Boolean baseTagChgInd;
	private Boolean baseTagFlagChgInd;
	private Boolean baseTagTargetChgInd;
	private Boolean baseTagAddedInd;
	private Boolean baseTagRemovedInd;
	private PageAnalysisFragments[] pageAnalysisFragmentsArray;
	private Boolean pageAnalysisFragmentsChgInd;
	private Boolean contentGuardInd;
	private Boolean customDataAddedInd;
	private Boolean customDataRemovedInd;
	private Boolean responseHeadersAddedInd;
	private Boolean responseHeadersRemovedInd;
	private Date previousCrawlTimestamp;

	@Override
	public HtmlClickHouseEntity clone() throws CloneNotSupportedException {
		return (HtmlClickHouseEntity) super.clone();
	}

	@Transient
	public Integer getHttpStatusCode() {
		Integer httpStatusCode = null;
		if (crawlerResponse != null && NumberUtils.isNumber(crawlerResponse.getResponse_code())) {
			httpStatusCode = NumberUtils.toInt(crawlerResponse.getResponse_code());
		}
		return httpStatusCode;
	}

	@Transient
	public Integer getTotalRecords() {
		return totalRecords;
	}

	@Transient
	public void setTotalRecords(Integer totalRecords) {
		this.totalRecords = totalRecords;
	}


}
