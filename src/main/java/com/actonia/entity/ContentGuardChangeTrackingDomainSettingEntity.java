package com.actonia.entity;

import java.util.Date;

public class ContentGuardChangeTrackingDomainSettingEntity {
	private int id;
	private int ownDomainId;
	private String indicator;
	private Integer createUser;
	private Date createDate;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(int ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public String getIndicator() {
		return indicator;
	}

	public void setIndicator(String indicator) {
		this.indicator = indicator;
	}

	public Integer getCreateUser() {
		return createUser;
	}

	public void setCreateUser(Integer createUser) {
		this.createUser = createUser;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Override
	public String toString() {
		return "ContentGuardChangeTrackingDomainSettingEntity [id=" + id + ", ownDomainId=" + ownDomainId + ", indicator=" + indicator + ", createUser=" + createUser
				+ ", createDate=" + createDate + "]";
	}

}
