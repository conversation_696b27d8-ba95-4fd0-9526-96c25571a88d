package com.actonia.entity;

import java.util.Date;

public class AssociatedTargetUrlEntity {
	
	public final static int ASSOCIATION_IND_TRUE = 1;
	public final static int ASSOCIATION_IND_FALSE = 0;
	
	private int domainId;
	private Long targetUrlId;
	private int associationInd;
	private Integer targetUrlPreviousStatus;
	private Integer targetUrlPreviousType;
	private String targetUrlPreviousUrl;
	private Integer targetUrlHttpStatusCode;
	private String associationComment;
	private String rankedUrl;
	private Integer rankedUrlHttpStatusCode;
	private Date lastUpdateTimestamp;

	public int getDomainId() {
		return domainId;
	}

	public void setDomainId(int domainId) {
		this.domainId = domainId;
	}

	public Long getTargetUrlId() {
		return targetUrlId;
	}

	public void setTargetUrlId(Long targetUrlId) {
		this.targetUrlId = targetUrlId;
	}

	public int getAssociationInd() {
		return associationInd;
	}

	public void setAssociationInd(int associationInd) {
		this.associationInd = associationInd;
	}

	public Integer getTargetUrlPreviousStatus() {
		return targetUrlPreviousStatus;
	}

	public void setTargetUrlPreviousStatus(Integer targetUrlPreviousStatus) {
		this.targetUrlPreviousStatus = targetUrlPreviousStatus;
	}

	public Integer getTargetUrlPreviousType() {
		return targetUrlPreviousType;
	}

	public void setTargetUrlPreviousType(Integer targetUrlPreviousType) {
		this.targetUrlPreviousType = targetUrlPreviousType;
	}

	public String getTargetUrlPreviousUrl() {
		return targetUrlPreviousUrl;
	}

	public void setTargetUrlPreviousUrl(String targetUrlPreviousUrl) {
		this.targetUrlPreviousUrl = targetUrlPreviousUrl;
	}

	public Integer getTargetUrlHttpStatusCode() {
		return targetUrlHttpStatusCode;
	}

	public void setTargetUrlHttpStatusCode(Integer targetUrlHttpStatusCode) {
		this.targetUrlHttpStatusCode = targetUrlHttpStatusCode;
	}

	public String getAssociationComment() {
		return associationComment;
	}

	public void setAssociationComment(String associationComment) {
		this.associationComment = associationComment;
	}

	public String getRankedUrl() {
		return rankedUrl;
	}

	public void setRankedUrl(String rankedUrl) {
		this.rankedUrl = rankedUrl;
	}

	public Integer getRankedUrlHttpStatusCode() {
		return rankedUrlHttpStatusCode;
	}

	public void setRankedUrlHttpStatusCode(Integer rankedUrlHttpStatusCode) {
		this.rankedUrlHttpStatusCode = rankedUrlHttpStatusCode;
	}

	public Date getLastUpdateTimestamp() {
		return lastUpdateTimestamp;
	}

	public void setLastUpdateTimestamp(Date lastUpdateTimestamp) {
		this.lastUpdateTimestamp = lastUpdateTimestamp;
	}

	@Override
	public String toString() {
		return " AssociatedTargetUrlEntity [domainId=" + domainId + ", targetUrlId=" + targetUrlId + ", associationInd=" + associationInd
				+ ", targetUrlPreviousStatus=" + targetUrlPreviousStatus + ", targetUrlPreviousType=" + targetUrlPreviousType
				+ ", targetUrlPreviousUrl=" + targetUrlPreviousUrl + ", targetUrlHttpStatusCode=" + targetUrlHttpStatusCode + ", associationComment="
				+ associationComment + ", rankedUrl=" + rankedUrl + ", rankedUrlHttpStatusCode=" + rankedUrlHttpStatusCode + ", lastUpdateTimestamp="
				+ lastUpdateTimestamp + "]";
	}

}