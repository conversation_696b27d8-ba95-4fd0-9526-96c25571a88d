package com.actonia.entity;

import java.util.Arrays;
import java.util.Date;

import javax.persistence.Transient;

import com.actonia.value.object.AlternateLinks;
import com.actonia.value.object.CustomData;
import com.actonia.value.object.HreflangErrors;
import com.actonia.value.object.HreflangLinks;
import com.actonia.value.object.OgMarkup;
import com.actonia.value.object.PageLink;
import com.actonia.value.object.RedirectChain;
import com.actonia.value.object.ResponseHeaders;
import com.actonia.value.object.StructuredData;

public class TargetUrlChangeClickHouseEntity implements Cloneable {
	private Integer domainId;
	private String url;
	private Date trackDate;
	private String urlHash;
	private String urlMurmurHash;
	private Date currentCrawlTimestamp;
	private Date previousCrawlTimestamp;
	private Date updateTimestamp;
	private Boolean alternateLinksChgInd;
	private AlternateLinks[] alternateLinksCurrent;
	private AlternateLinks[] alternateLinksPrevious;
	private Boolean amphtmlHrefChgInd;
	private String amphtmlHrefCurrent;
	private String amphtmlHrefPrevious;
	private Boolean analyzedUrlSChgInd;
	private String analyzedUrlSCurrent;
	private String analyzedUrlSPrevious;
	private Boolean archiveFlgChgInd;
	private String archiveFlgCurrent;
	private String archiveFlgPrevious;
	private Boolean baseTagAddedInd;
	private Boolean baseTagChgInd;
	private Boolean baseTagRemovedInd;
	private String baseTagCurrent;
	private String baseTagPrevious;
	private Boolean baseTagTargetChgInd;
	private String baseTagTargetCurrent;
	private String baseTagTargetPrevious;
	private Boolean blockedByRobotsChgInd;
	private String blockedByRobotsCurrent;
	private String blockedByRobotsPrevious;
	private Boolean canonicalAddedInd;
	private Boolean canonicalChgInd;
	private Boolean canonicalRemovedInd;
	private String canonicalCurrent;
	private String canonicalPrevious;
	private Boolean canonicalHeaderFlagChgInd;
	private Boolean canonicalHeaderFlagCurrent;
	private Boolean canonicalHeaderFlagPrevious;
	private Boolean canonicalHeaderTypeChgInd;
	private String canonicalHeaderTypeCurrent;
	private String canonicalHeaderTypePrevious;
	private Boolean canonicalTypeChgInd;
	private String canonicalTypeCurrent;
	private String canonicalTypePrevious;
	private Boolean canonicalUrlIsConsistentChgInd;
	private String canonicalUrlIsConsistentCurrent;
	private String canonicalUrlIsConsistentPrevious;
	private Boolean contentTypeChgInd;
	private String contentTypeCurrent;
	private String contentTypePrevious;
	private Boolean customDataAddedInd;
	private Boolean customDataChgInd;
	private Boolean customDataRemovedInd;
	private CustomData[] customDataCurrent;
	private CustomData[] customDataPrevious;
	private Boolean descriptionAddedInd;
	private Boolean descriptionChgInd;
	private Boolean descriptionRemovedInd;
	private String descriptionCurrent;
	private String descriptionPrevious;
	private Boolean descriptionLengthChgInd;
	private Integer descriptionLengthCurrent;
	private Integer descriptionLengthPrevious;
	private Boolean errorMessageChgInd;
	private String errorMessageCurrent;
	private String errorMessagePrevious;
	private Boolean finalResponseCodeChgInd;
	private Integer finalResponseCodeCurrent;
	private Integer finalResponseCodePrevious;
	private Boolean followFlgChgInd;
	private String followFlgCurrent;
	private String followFlgPrevious;
	private Boolean h1AddedInd;
	private Boolean h1ChgInd;
	private Boolean h1RemovedInd;
	private String[] h1Current;
	private String[] h1Previous;
	private Boolean h1CountChgInd;
	private Integer h1CountCurrent;
	private Integer h1CountPrevious;
	private Boolean h1LengthChgInd;
	private Integer h1LengthCurrent;
	private Integer h1LengthPrevious;
	private Boolean h2AddedInd;
	private Boolean h2ChgInd;
	private Boolean h2RemovedInd;
	private String[] h2Current;
	private String[] h2Previous;
	private Boolean headerNoarchiveChgInd;
	private Boolean headerNoarchiveCurrent;
	private Boolean headerNoarchivePrevious;
	private Boolean headerNofollowChgInd;
	private Boolean headerNofollowCurrent;
	private Boolean headerNofollowPrevious;
	private Boolean headerNoindexChgInd;
	private Boolean headerNoindexCurrent;
	private Boolean headerNoindexPrevious;
	private Boolean headerNoodpChgInd;
	private Boolean headerNoodpCurrent;
	private Boolean headerNoodpPrevious;
	private Boolean headerNosnippetChgInd;
	private Boolean headerNosnippetCurrent;
	private Boolean headerNosnippetPrevious;
	private Boolean headerNoydirChgInd;
	private Boolean headerNoydirCurrent;
	private Boolean headerNoydirPrevious;
	private Boolean hreflangErrorsChgInd;
	private HreflangErrors hreflangErrorsCurrent;
	private HreflangErrors hreflangErrorsPrevious;
	private Boolean hreflangLinksChgInd;
	private HreflangLinks[] hreflangLinksCurrent;
	private HreflangLinks[] hreflangLinksPrevious;
	private Boolean hreflangLinksOutCountChgInd;
	private Integer hreflangLinksOutCountCurrent;
	private Integer hreflangLinksOutCountPrevious;
	private Boolean hreflangLinksAddedInd;
	private Boolean hreflangUrlCountChgInd;
	private Boolean hreflangLinksRemovedInd;
	private Integer hreflangUrlCountCurrent;
	private Integer hreflangUrlCountPrevious;
	private Boolean indexFlgChgInd;
	private String indexFlgCurrent;
	private String indexFlgPrevious;
	private Boolean indexableChgInd;
	private Boolean indexableCurrent;
	private Boolean indexablePrevious;
	private Boolean insecureResourcesChgInd;
	private String[] insecureResourcesCurrent;
	private String[] insecureResourcesPrevious;
	private Boolean metaCharsetChgInd;
	private String metaCharsetCurrent;
	private String metaCharsetPrevious;
	private Boolean metaContentTypeChgInd;
	private String metaContentTypeCurrent;
	private String metaContentTypePrevious;
	private Boolean metaDisabledSitelinksChgInd;
	private Boolean metaDisabledSitelinksCurrent;
	private Boolean metaDisabledSitelinksPrevious;
	private Boolean metaNoodpChgInd;
	private Boolean metaNoodpCurrent;
	private Boolean metaNoodpPrevious;
	private Boolean metaNosnippetChgInd;
	private Boolean metaNosnippetCurrent;
	private Boolean metaNosnippetPrevious;
	private Boolean metaNoydirChgInd;
	private Boolean metaNoydirCurrent;
	private Boolean metaNoydirPrevious;
	private Boolean metaRedirectChgInd;
	private Boolean metaRedirectCurrent;
	private Boolean metaRedirectPrevious;
	private Boolean mixedRedirectsChgInd;
	private Boolean mixedRedirectsCurrent;
	private Boolean mixedRedirectsPrevious;
	private Boolean mobileRelAlternateUrlIsConsistentChgInd;
	private Boolean mobileRelAlternateUrlIsConsistentCurrent;
	private Boolean mobileRelAlternateUrlIsConsistentPrevious;
	private Boolean noodpChgInd;
	private Boolean noodpCurrent;
	private Boolean noodpPrevious;
	private Boolean nosnippetChgInd;
	private Boolean nosnippetCurrent;
	private Boolean nosnippetPrevious;
	private Boolean noydirChgInd;
	private Boolean noydirCurrent;
	private Boolean noydirPrevious;
	private Boolean openGraphAddedInd;
	private Boolean ogMarkupChgInd;
	private Boolean openGraphRemovedInd;
	private OgMarkup[] ogMarkupCurrent;
	private OgMarkup[] ogMarkupPrevious;
	private Boolean ogMarkupLengthChgInd;
	private Integer ogMarkupLengthCurrent;
	private Integer ogMarkupLengthPrevious;
	private Boolean outlinkCountChgInd;
	private Integer outlinkCountCurrent;
	private Integer outlinkCountPrevious;
	private String pageAnalysisResultsChgIndJson;
	private Boolean pageLinkChgInd;
	private PageLink[] pageLinkCurrent;
	private PageLink[] pageLinkPrevious;
	private Boolean redirectBlockedChgInd;
	private Boolean redirectBlockedCurrent;
	private Boolean redirectBlockedPrevious;
	private Boolean redirectBlockedReasonChgInd;
	private String redirectBlockedReasonCurrent;
	private String redirectBlockedReasonPrevious;
	private Boolean redirectChainChgInd;
	private RedirectChain[] redirectChainCurrent;
	private RedirectChain[] redirectChainPrevious;
	private Boolean redirectFinalUrlChgInd;
	private String redirectFinalUrlCurrent;
	private String redirectFinalUrlPrevious;
	private Boolean redirectTimesChgInd;
	private Integer redirectTimesCurrent;
	private Integer redirectTimesPrevious;
	private Boolean responseCodeChgInd;
	private Boolean redirect301DetectedInd;
	private Boolean redirect301RemovedInd;
	private Boolean redirect302DetectedInd;
	private Boolean redirect302RemovedInd;
	private Boolean redirectDiffCodeInd;
	private String responseCodeCurrent;
	private String responseCodePrevious;
	private Boolean responseHeadersAddedInd;
	private Boolean responseHeadersRemovedInd;
	private ResponseHeaders[] responseHeadersCurrent;
	private ResponseHeaders[] responseHeadersPrevious;
	private Boolean robotsAddedInd;
	private Boolean robotsContentsChgInd;
	private Boolean robotsRemovedInd;
	private String robotsContentsCurrent;
	private String robotsContentsPrevious;
	private Boolean structuredDataChgInd;
	private StructuredData structuredDataCurrent;
	private StructuredData structuredDataPrevious;
	private Boolean titleAddedInd;
	private Boolean titleChgInd;
	private Boolean titleRemovedInd;
	private String titleCurrent;
	private String titlePrevious;
	private Boolean titleLengthChgInd;
	private Integer titleLengthCurrent;
	private Integer titleLengthPrevious;
	private Boolean viewportAddedInd;
	private Boolean viewportContentChgInd;
	private Boolean viewportRemovedInd;
	private String viewportContentCurrent;
	private String viewportContentPrevious;
	private Integer sign;

	@Transient
	private Integer total;

	public Integer getDomainId() {
		return domainId;
	}

	public void setDomainId(Integer domainId) {
		this.domainId = domainId;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public Date getTrackDate() {
		return trackDate;
	}

	public void setTrackDate(Date trackDate) {
		this.trackDate = trackDate;
	}

	public String getUrlHash() {
		return urlHash;
	}

	public void setUrlHash(String urlHash) {
		this.urlHash = urlHash;
	}

	public String getUrlMurmurHash() {
		return urlMurmurHash;
	}

	public void setUrlMurmurHash(String urlMurmurHash) {
		this.urlMurmurHash = urlMurmurHash;
	}

	public Date getCurrentCrawlTimestamp() {
		return currentCrawlTimestamp;
	}

	public void setCurrentCrawlTimestamp(Date currentCrawlTimestamp) {
		this.currentCrawlTimestamp = currentCrawlTimestamp;
	}

	public Date getPreviousCrawlTimestamp() {
		return previousCrawlTimestamp;
	}

	public void setPreviousCrawlTimestamp(Date previousCrawlTimestamp) {
		this.previousCrawlTimestamp = previousCrawlTimestamp;
	}

	public Date getUpdateTimestamp() {
		return updateTimestamp;
	}

	public void setUpdateTimestamp(Date updateTimestamp) {
		this.updateTimestamp = updateTimestamp;
	}

	public Boolean getAlternateLinksChgInd() {
		return alternateLinksChgInd;
	}

	public void setAlternateLinksChgInd(Boolean alternateLinksChgInd) {
		this.alternateLinksChgInd = alternateLinksChgInd;
	}

	public AlternateLinks[] getAlternateLinksCurrent() {
		return alternateLinksCurrent;
	}

	public void setAlternateLinksCurrent(AlternateLinks[] alternateLinksCurrent) {
		this.alternateLinksCurrent = alternateLinksCurrent;
	}

	public AlternateLinks[] getAlternateLinksPrevious() {
		return alternateLinksPrevious;
	}

	public void setAlternateLinksPrevious(AlternateLinks[] alternateLinksPrevious) {
		this.alternateLinksPrevious = alternateLinksPrevious;
	}

	public Boolean getAmphtmlHrefChgInd() {
		return amphtmlHrefChgInd;
	}

	public void setAmphtmlHrefChgInd(Boolean amphtmlHrefChgInd) {
		this.amphtmlHrefChgInd = amphtmlHrefChgInd;
	}

	public String getAmphtmlHrefCurrent() {
		return amphtmlHrefCurrent;
	}

	public void setAmphtmlHrefCurrent(String amphtmlHrefCurrent) {
		this.amphtmlHrefCurrent = amphtmlHrefCurrent;
	}

	public String getAmphtmlHrefPrevious() {
		return amphtmlHrefPrevious;
	}

	public void setAmphtmlHrefPrevious(String amphtmlHrefPrevious) {
		this.amphtmlHrefPrevious = amphtmlHrefPrevious;
	}

	public Boolean getAnalyzedUrlSChgInd() {
		return analyzedUrlSChgInd;
	}

	public void setAnalyzedUrlSChgInd(Boolean analyzedUrlSChgInd) {
		this.analyzedUrlSChgInd = analyzedUrlSChgInd;
	}

	public String getAnalyzedUrlSCurrent() {
		return analyzedUrlSCurrent;
	}

	public void setAnalyzedUrlSCurrent(String analyzedUrlSCurrent) {
		this.analyzedUrlSCurrent = analyzedUrlSCurrent;
	}

	public String getAnalyzedUrlSPrevious() {
		return analyzedUrlSPrevious;
	}

	public void setAnalyzedUrlSPrevious(String analyzedUrlSPrevious) {
		this.analyzedUrlSPrevious = analyzedUrlSPrevious;
	}

	public Boolean getArchiveFlgChgInd() {
		return archiveFlgChgInd;
	}

	public void setArchiveFlgChgInd(Boolean archiveFlgChgInd) {
		this.archiveFlgChgInd = archiveFlgChgInd;
	}

	public String getArchiveFlgCurrent() {
		return archiveFlgCurrent;
	}

	public void setArchiveFlgCurrent(String archiveFlgCurrent) {
		this.archiveFlgCurrent = archiveFlgCurrent;
	}

	public String getArchiveFlgPrevious() {
		return archiveFlgPrevious;
	}

	public void setArchiveFlgPrevious(String archiveFlgPrevious) {
		this.archiveFlgPrevious = archiveFlgPrevious;
	}

	public Boolean getBaseTagAddedInd() {
		return baseTagAddedInd;
	}

	public void setBaseTagAddedInd(Boolean baseTagAddedInd) {
		this.baseTagAddedInd = baseTagAddedInd;
	}

	public Boolean getBaseTagChgInd() {
		return baseTagChgInd;
	}

	public void setBaseTagChgInd(Boolean baseTagChgInd) {
		this.baseTagChgInd = baseTagChgInd;
	}

	public Boolean getBaseTagRemovedInd() {
		return baseTagRemovedInd;
	}

	public void setBaseTagRemovedInd(Boolean baseTagRemovedInd) {
		this.baseTagRemovedInd = baseTagRemovedInd;
	}

	public String getBaseTagCurrent() {
		return baseTagCurrent;
	}

	public void setBaseTagCurrent(String baseTagCurrent) {
		this.baseTagCurrent = baseTagCurrent;
	}

	public String getBaseTagPrevious() {
		return baseTagPrevious;
	}

	public void setBaseTagPrevious(String baseTagPrevious) {
		this.baseTagPrevious = baseTagPrevious;
	}

	public Boolean getBaseTagTargetChgInd() {
		return baseTagTargetChgInd;
	}

	public void setBaseTagTargetChgInd(Boolean baseTagTargetChgInd) {
		this.baseTagTargetChgInd = baseTagTargetChgInd;
	}

	public String getBaseTagTargetCurrent() {
		return baseTagTargetCurrent;
	}

	public void setBaseTagTargetCurrent(String baseTagTargetCurrent) {
		this.baseTagTargetCurrent = baseTagTargetCurrent;
	}

	public String getBaseTagTargetPrevious() {
		return baseTagTargetPrevious;
	}

	public void setBaseTagTargetPrevious(String baseTagTargetPrevious) {
		this.baseTagTargetPrevious = baseTagTargetPrevious;
	}

	public Boolean getBlockedByRobotsChgInd() {
		return blockedByRobotsChgInd;
	}

	public void setBlockedByRobotsChgInd(Boolean blockedByRobotsChgInd) {
		this.blockedByRobotsChgInd = blockedByRobotsChgInd;
	}

	public String getBlockedByRobotsCurrent() {
		return blockedByRobotsCurrent;
	}

	public void setBlockedByRobotsCurrent(String blockedByRobotsCurrent) {
		this.blockedByRobotsCurrent = blockedByRobotsCurrent;
	}

	public String getBlockedByRobotsPrevious() {
		return blockedByRobotsPrevious;
	}

	public void setBlockedByRobotsPrevious(String blockedByRobotsPrevious) {
		this.blockedByRobotsPrevious = blockedByRobotsPrevious;
	}

	public Boolean getCanonicalAddedInd() {
		return canonicalAddedInd;
	}

	public void setCanonicalAddedInd(Boolean canonicalAddedInd) {
		this.canonicalAddedInd = canonicalAddedInd;
	}

	public Boolean getCanonicalChgInd() {
		return canonicalChgInd;
	}

	public void setCanonicalChgInd(Boolean canonicalChgInd) {
		this.canonicalChgInd = canonicalChgInd;
	}

	public Boolean getCanonicalRemovedInd() {
		return canonicalRemovedInd;
	}

	public void setCanonicalRemovedInd(Boolean canonicalRemovedInd) {
		this.canonicalRemovedInd = canonicalRemovedInd;
	}

	public String getCanonicalCurrent() {
		return canonicalCurrent;
	}

	public void setCanonicalCurrent(String canonicalCurrent) {
		this.canonicalCurrent = canonicalCurrent;
	}

	public String getCanonicalPrevious() {
		return canonicalPrevious;
	}

	public void setCanonicalPrevious(String canonicalPrevious) {
		this.canonicalPrevious = canonicalPrevious;
	}

	public Boolean getCanonicalHeaderFlagChgInd() {
		return canonicalHeaderFlagChgInd;
	}

	public void setCanonicalHeaderFlagChgInd(Boolean canonicalHeaderFlagChgInd) {
		this.canonicalHeaderFlagChgInd = canonicalHeaderFlagChgInd;
	}

	public Boolean getCanonicalHeaderFlagCurrent() {
		return canonicalHeaderFlagCurrent;
	}

	public void setCanonicalHeaderFlagCurrent(Boolean canonicalHeaderFlagCurrent) {
		this.canonicalHeaderFlagCurrent = canonicalHeaderFlagCurrent;
	}

	public Boolean getCanonicalHeaderFlagPrevious() {
		return canonicalHeaderFlagPrevious;
	}

	public void setCanonicalHeaderFlagPrevious(Boolean canonicalHeaderFlagPrevious) {
		this.canonicalHeaderFlagPrevious = canonicalHeaderFlagPrevious;
	}

	public Boolean getCanonicalHeaderTypeChgInd() {
		return canonicalHeaderTypeChgInd;
	}

	public void setCanonicalHeaderTypeChgInd(Boolean canonicalHeaderTypeChgInd) {
		this.canonicalHeaderTypeChgInd = canonicalHeaderTypeChgInd;
	}

	public String getCanonicalHeaderTypeCurrent() {
		return canonicalHeaderTypeCurrent;
	}

	public void setCanonicalHeaderTypeCurrent(String canonicalHeaderTypeCurrent) {
		this.canonicalHeaderTypeCurrent = canonicalHeaderTypeCurrent;
	}

	public String getCanonicalHeaderTypePrevious() {
		return canonicalHeaderTypePrevious;
	}

	public void setCanonicalHeaderTypePrevious(String canonicalHeaderTypePrevious) {
		this.canonicalHeaderTypePrevious = canonicalHeaderTypePrevious;
	}

	public Boolean getCanonicalTypeChgInd() {
		return canonicalTypeChgInd;
	}

	public void setCanonicalTypeChgInd(Boolean canonicalTypeChgInd) {
		this.canonicalTypeChgInd = canonicalTypeChgInd;
	}

	public String getCanonicalTypeCurrent() {
		return canonicalTypeCurrent;
	}

	public void setCanonicalTypeCurrent(String canonicalTypeCurrent) {
		this.canonicalTypeCurrent = canonicalTypeCurrent;
	}

	public String getCanonicalTypePrevious() {
		return canonicalTypePrevious;
	}

	public void setCanonicalTypePrevious(String canonicalTypePrevious) {
		this.canonicalTypePrevious = canonicalTypePrevious;
	}

	public Boolean getCanonicalUrlIsConsistentChgInd() {
		return canonicalUrlIsConsistentChgInd;
	}

	public void setCanonicalUrlIsConsistentChgInd(Boolean canonicalUrlIsConsistentChgInd) {
		this.canonicalUrlIsConsistentChgInd = canonicalUrlIsConsistentChgInd;
	}

	public String getCanonicalUrlIsConsistentCurrent() {
		return canonicalUrlIsConsistentCurrent;
	}

	public void setCanonicalUrlIsConsistentCurrent(String canonicalUrlIsConsistentCurrent) {
		this.canonicalUrlIsConsistentCurrent = canonicalUrlIsConsistentCurrent;
	}

	public String getCanonicalUrlIsConsistentPrevious() {
		return canonicalUrlIsConsistentPrevious;
	}

	public void setCanonicalUrlIsConsistentPrevious(String canonicalUrlIsConsistentPrevious) {
		this.canonicalUrlIsConsistentPrevious = canonicalUrlIsConsistentPrevious;
	}

	public Boolean getContentTypeChgInd() {
		return contentTypeChgInd;
	}

	public void setContentTypeChgInd(Boolean contentTypeChgInd) {
		this.contentTypeChgInd = contentTypeChgInd;
	}

	public String getContentTypeCurrent() {
		return contentTypeCurrent;
	}

	public void setContentTypeCurrent(String contentTypeCurrent) {
		this.contentTypeCurrent = contentTypeCurrent;
	}

	public String getContentTypePrevious() {
		return contentTypePrevious;
	}

	public void setContentTypePrevious(String contentTypePrevious) {
		this.contentTypePrevious = contentTypePrevious;
	}

	public Boolean getCustomDataAddedInd() {
		return customDataAddedInd;
	}

	public void setCustomDataAddedInd(Boolean customDataAddedInd) {
		this.customDataAddedInd = customDataAddedInd;
	}

	public Boolean getCustomDataChgInd() {
		return customDataChgInd;
	}

	public void setCustomDataChgInd(Boolean customDataChgInd) {
		this.customDataChgInd = customDataChgInd;
	}

	public Boolean getCustomDataRemovedInd() {
		return customDataRemovedInd;
	}

	public void setCustomDataRemovedInd(Boolean customDataRemovedInd) {
		this.customDataRemovedInd = customDataRemovedInd;
	}

	public CustomData[] getCustomDataCurrent() {
		return customDataCurrent;
	}

	public void setCustomDataCurrent(CustomData[] customDataCurrent) {
		this.customDataCurrent = customDataCurrent;
	}

	public CustomData[] getCustomDataPrevious() {
		return customDataPrevious;
	}

	public void setCustomDataPrevious(CustomData[] customDataPrevious) {
		this.customDataPrevious = customDataPrevious;
	}

	public Boolean getDescriptionAddedInd() {
		return descriptionAddedInd;
	}

	public void setDescriptionAddedInd(Boolean descriptionAddedInd) {
		this.descriptionAddedInd = descriptionAddedInd;
	}

	public Boolean getDescriptionChgInd() {
		return descriptionChgInd;
	}

	public void setDescriptionChgInd(Boolean descriptionChgInd) {
		this.descriptionChgInd = descriptionChgInd;
	}

	public Boolean getDescriptionRemovedInd() {
		return descriptionRemovedInd;
	}

	public void setDescriptionRemovedInd(Boolean descriptionRemovedInd) {
		this.descriptionRemovedInd = descriptionRemovedInd;
	}

	public String getDescriptionCurrent() {
		return descriptionCurrent;
	}

	public void setDescriptionCurrent(String descriptionCurrent) {
		this.descriptionCurrent = descriptionCurrent;
	}

	public String getDescriptionPrevious() {
		return descriptionPrevious;
	}

	public void setDescriptionPrevious(String descriptionPrevious) {
		this.descriptionPrevious = descriptionPrevious;
	}

	public Boolean getDescriptionLengthChgInd() {
		return descriptionLengthChgInd;
	}

	public void setDescriptionLengthChgInd(Boolean descriptionLengthChgInd) {
		this.descriptionLengthChgInd = descriptionLengthChgInd;
	}

	public Integer getDescriptionLengthCurrent() {
		return descriptionLengthCurrent;
	}

	public void setDescriptionLengthCurrent(Integer descriptionLengthCurrent) {
		this.descriptionLengthCurrent = descriptionLengthCurrent;
	}

	public Integer getDescriptionLengthPrevious() {
		return descriptionLengthPrevious;
	}

	public void setDescriptionLengthPrevious(Integer descriptionLengthPrevious) {
		this.descriptionLengthPrevious = descriptionLengthPrevious;
	}

	public Boolean getErrorMessageChgInd() {
		return errorMessageChgInd;
	}

	public void setErrorMessageChgInd(Boolean errorMessageChgInd) {
		this.errorMessageChgInd = errorMessageChgInd;
	}

	public String getErrorMessageCurrent() {
		return errorMessageCurrent;
	}

	public void setErrorMessageCurrent(String errorMessageCurrent) {
		this.errorMessageCurrent = errorMessageCurrent;
	}

	public String getErrorMessagePrevious() {
		return errorMessagePrevious;
	}

	public void setErrorMessagePrevious(String errorMessagePrevious) {
		this.errorMessagePrevious = errorMessagePrevious;
	}

	public Boolean getFinalResponseCodeChgInd() {
		return finalResponseCodeChgInd;
	}

	public void setFinalResponseCodeChgInd(Boolean finalResponseCodeChgInd) {
		this.finalResponseCodeChgInd = finalResponseCodeChgInd;
	}

	public Integer getFinalResponseCodeCurrent() {
		return finalResponseCodeCurrent;
	}

	public void setFinalResponseCodeCurrent(Integer finalResponseCodeCurrent) {
		this.finalResponseCodeCurrent = finalResponseCodeCurrent;
	}

	public Integer getFinalResponseCodePrevious() {
		return finalResponseCodePrevious;
	}

	public void setFinalResponseCodePrevious(Integer finalResponseCodePrevious) {
		this.finalResponseCodePrevious = finalResponseCodePrevious;
	}

	public Boolean getFollowFlgChgInd() {
		return followFlgChgInd;
	}

	public void setFollowFlgChgInd(Boolean followFlgChgInd) {
		this.followFlgChgInd = followFlgChgInd;
	}

	public String getFollowFlgCurrent() {
		return followFlgCurrent;
	}

	public void setFollowFlgCurrent(String followFlgCurrent) {
		this.followFlgCurrent = followFlgCurrent;
	}

	public String getFollowFlgPrevious() {
		return followFlgPrevious;
	}

	public void setFollowFlgPrevious(String followFlgPrevious) {
		this.followFlgPrevious = followFlgPrevious;
	}

	public Boolean getH1AddedInd() {
		return h1AddedInd;
	}

	public void setH1AddedInd(Boolean h1AddedInd) {
		this.h1AddedInd = h1AddedInd;
	}

	public Boolean getH1ChgInd() {
		return h1ChgInd;
	}

	public void setH1ChgInd(Boolean h1ChgInd) {
		this.h1ChgInd = h1ChgInd;
	}

	public Boolean getH1RemovedInd() {
		return h1RemovedInd;
	}

	public void setH1RemovedInd(Boolean h1RemovedInd) {
		this.h1RemovedInd = h1RemovedInd;
	}

	public String[] getH1Current() {
		return h1Current;
	}

	public void setH1Current(String[] h1Current) {
		this.h1Current = h1Current;
	}

	public String[] getH1Previous() {
		return h1Previous;
	}

	public void setH1Previous(String[] h1Previous) {
		this.h1Previous = h1Previous;
	}

	public Boolean getH1CountChgInd() {
		return h1CountChgInd;
	}

	public void setH1CountChgInd(Boolean h1CountChgInd) {
		this.h1CountChgInd = h1CountChgInd;
	}

	public Integer getH1CountCurrent() {
		return h1CountCurrent;
	}

	public void setH1CountCurrent(Integer h1CountCurrent) {
		this.h1CountCurrent = h1CountCurrent;
	}

	public Integer getH1CountPrevious() {
		return h1CountPrevious;
	}

	public void setH1CountPrevious(Integer h1CountPrevious) {
		this.h1CountPrevious = h1CountPrevious;
	}

	public Boolean getH1LengthChgInd() {
		return h1LengthChgInd;
	}

	public void setH1LengthChgInd(Boolean h1LengthChgInd) {
		this.h1LengthChgInd = h1LengthChgInd;
	}

	public Integer getH1LengthCurrent() {
		return h1LengthCurrent;
	}

	public void setH1LengthCurrent(Integer h1LengthCurrent) {
		this.h1LengthCurrent = h1LengthCurrent;
	}

	public Integer getH1LengthPrevious() {
		return h1LengthPrevious;
	}

	public void setH1LengthPrevious(Integer h1LengthPrevious) {
		this.h1LengthPrevious = h1LengthPrevious;
	}

	public Boolean getH2AddedInd() {
		return h2AddedInd;
	}

	public void setH2AddedInd(Boolean h2AddedInd) {
		this.h2AddedInd = h2AddedInd;
	}

	public Boolean getH2ChgInd() {
		return h2ChgInd;
	}

	public void setH2ChgInd(Boolean h2ChgInd) {
		this.h2ChgInd = h2ChgInd;
	}

	public Boolean getH2RemovedInd() {
		return h2RemovedInd;
	}

	public void setH2RemovedInd(Boolean h2RemovedInd) {
		this.h2RemovedInd = h2RemovedInd;
	}

	public String[] getH2Current() {
		return h2Current;
	}

	public void setH2Current(String[] h2Current) {
		this.h2Current = h2Current;
	}

	public String[] getH2Previous() {
		return h2Previous;
	}

	public void setH2Previous(String[] h2Previous) {
		this.h2Previous = h2Previous;
	}

	public Boolean getHeaderNoarchiveChgInd() {
		return headerNoarchiveChgInd;
	}

	public void setHeaderNoarchiveChgInd(Boolean headerNoarchiveChgInd) {
		this.headerNoarchiveChgInd = headerNoarchiveChgInd;
	}

	public Boolean getHeaderNoarchiveCurrent() {
		return headerNoarchiveCurrent;
	}

	public void setHeaderNoarchiveCurrent(Boolean headerNoarchiveCurrent) {
		this.headerNoarchiveCurrent = headerNoarchiveCurrent;
	}

	public Boolean getHeaderNoarchivePrevious() {
		return headerNoarchivePrevious;
	}

	public void setHeaderNoarchivePrevious(Boolean headerNoarchivePrevious) {
		this.headerNoarchivePrevious = headerNoarchivePrevious;
	}

	public Boolean getHeaderNofollowChgInd() {
		return headerNofollowChgInd;
	}

	public void setHeaderNofollowChgInd(Boolean headerNofollowChgInd) {
		this.headerNofollowChgInd = headerNofollowChgInd;
	}

	public Boolean getHeaderNofollowCurrent() {
		return headerNofollowCurrent;
	}

	public void setHeaderNofollowCurrent(Boolean headerNofollowCurrent) {
		this.headerNofollowCurrent = headerNofollowCurrent;
	}

	public Boolean getHeaderNofollowPrevious() {
		return headerNofollowPrevious;
	}

	public void setHeaderNofollowPrevious(Boolean headerNofollowPrevious) {
		this.headerNofollowPrevious = headerNofollowPrevious;
	}

	public Boolean getHeaderNoindexChgInd() {
		return headerNoindexChgInd;
	}

	public void setHeaderNoindexChgInd(Boolean headerNoindexChgInd) {
		this.headerNoindexChgInd = headerNoindexChgInd;
	}

	public Boolean getHeaderNoindexCurrent() {
		return headerNoindexCurrent;
	}

	public void setHeaderNoindexCurrent(Boolean headerNoindexCurrent) {
		this.headerNoindexCurrent = headerNoindexCurrent;
	}

	public Boolean getHeaderNoindexPrevious() {
		return headerNoindexPrevious;
	}

	public void setHeaderNoindexPrevious(Boolean headerNoindexPrevious) {
		this.headerNoindexPrevious = headerNoindexPrevious;
	}

	public Boolean getHeaderNoodpChgInd() {
		return headerNoodpChgInd;
	}

	public void setHeaderNoodpChgInd(Boolean headerNoodpChgInd) {
		this.headerNoodpChgInd = headerNoodpChgInd;
	}

	public Boolean getHeaderNoodpCurrent() {
		return headerNoodpCurrent;
	}

	public void setHeaderNoodpCurrent(Boolean headerNoodpCurrent) {
		this.headerNoodpCurrent = headerNoodpCurrent;
	}

	public Boolean getHeaderNoodpPrevious() {
		return headerNoodpPrevious;
	}

	public void setHeaderNoodpPrevious(Boolean headerNoodpPrevious) {
		this.headerNoodpPrevious = headerNoodpPrevious;
	}

	public Boolean getHeaderNosnippetChgInd() {
		return headerNosnippetChgInd;
	}

	public void setHeaderNosnippetChgInd(Boolean headerNosnippetChgInd) {
		this.headerNosnippetChgInd = headerNosnippetChgInd;
	}

	public Boolean getHeaderNosnippetCurrent() {
		return headerNosnippetCurrent;
	}

	public void setHeaderNosnippetCurrent(Boolean headerNosnippetCurrent) {
		this.headerNosnippetCurrent = headerNosnippetCurrent;
	}

	public Boolean getHeaderNosnippetPrevious() {
		return headerNosnippetPrevious;
	}

	public void setHeaderNosnippetPrevious(Boolean headerNosnippetPrevious) {
		this.headerNosnippetPrevious = headerNosnippetPrevious;
	}

	public Boolean getHeaderNoydirChgInd() {
		return headerNoydirChgInd;
	}

	public void setHeaderNoydirChgInd(Boolean headerNoydirChgInd) {
		this.headerNoydirChgInd = headerNoydirChgInd;
	}

	public Boolean getHeaderNoydirCurrent() {
		return headerNoydirCurrent;
	}

	public void setHeaderNoydirCurrent(Boolean headerNoydirCurrent) {
		this.headerNoydirCurrent = headerNoydirCurrent;
	}

	public Boolean getHeaderNoydirPrevious() {
		return headerNoydirPrevious;
	}

	public void setHeaderNoydirPrevious(Boolean headerNoydirPrevious) {
		this.headerNoydirPrevious = headerNoydirPrevious;
	}

	public Boolean getHreflangErrorsChgInd() {
		return hreflangErrorsChgInd;
	}

	public void setHreflangErrorsChgInd(Boolean hreflangErrorsChgInd) {
		this.hreflangErrorsChgInd = hreflangErrorsChgInd;
	}

	public HreflangErrors getHreflangErrorsCurrent() {
		return hreflangErrorsCurrent;
	}

	public void setHreflangErrorsCurrent(HreflangErrors hreflangErrorsCurrent) {
		this.hreflangErrorsCurrent = hreflangErrorsCurrent;
	}

	public HreflangErrors getHreflangErrorsPrevious() {
		return hreflangErrorsPrevious;
	}

	public void setHreflangErrorsPrevious(HreflangErrors hreflangErrorsPrevious) {
		this.hreflangErrorsPrevious = hreflangErrorsPrevious;
	}

	public Boolean getHreflangLinksChgInd() {
		return hreflangLinksChgInd;
	}

	public void setHreflangLinksChgInd(Boolean hreflangLinksChgInd) {
		this.hreflangLinksChgInd = hreflangLinksChgInd;
	}

	public HreflangLinks[] getHreflangLinksCurrent() {
		return hreflangLinksCurrent;
	}

	public void setHreflangLinksCurrent(HreflangLinks[] hreflangLinksCurrent) {
		this.hreflangLinksCurrent = hreflangLinksCurrent;
	}

	public HreflangLinks[] getHreflangLinksPrevious() {
		return hreflangLinksPrevious;
	}

	public void setHreflangLinksPrevious(HreflangLinks[] hreflangLinksPrevious) {
		this.hreflangLinksPrevious = hreflangLinksPrevious;
	}

	public Boolean getHreflangLinksOutCountChgInd() {
		return hreflangLinksOutCountChgInd;
	}

	public void setHreflangLinksOutCountChgInd(Boolean hreflangLinksOutCountChgInd) {
		this.hreflangLinksOutCountChgInd = hreflangLinksOutCountChgInd;
	}

	public Integer getHreflangLinksOutCountCurrent() {
		return hreflangLinksOutCountCurrent;
	}

	public void setHreflangLinksOutCountCurrent(Integer hreflangLinksOutCountCurrent) {
		this.hreflangLinksOutCountCurrent = hreflangLinksOutCountCurrent;
	}

	public Integer getHreflangLinksOutCountPrevious() {
		return hreflangLinksOutCountPrevious;
	}

	public void setHreflangLinksOutCountPrevious(Integer hreflangLinksOutCountPrevious) {
		this.hreflangLinksOutCountPrevious = hreflangLinksOutCountPrevious;
	}

	public Boolean getHreflangLinksAddedInd() {
		return hreflangLinksAddedInd;
	}

	public void setHreflangLinksAddedInd(Boolean hreflangLinksAddedInd) {
		this.hreflangLinksAddedInd = hreflangLinksAddedInd;
	}

	public Boolean getHreflangUrlCountChgInd() {
		return hreflangUrlCountChgInd;
	}

	public void setHreflangUrlCountChgInd(Boolean hreflangUrlCountChgInd) {
		this.hreflangUrlCountChgInd = hreflangUrlCountChgInd;
	}

	public Boolean getHreflangLinksRemovedInd() {
		return hreflangLinksRemovedInd;
	}

	public void setHreflangLinksRemovedInd(Boolean hreflangLinksRemovedInd) {
		this.hreflangLinksRemovedInd = hreflangLinksRemovedInd;
	}

	public Integer getHreflangUrlCountCurrent() {
		return hreflangUrlCountCurrent;
	}

	public void setHreflangUrlCountCurrent(Integer hreflangUrlCountCurrent) {
		this.hreflangUrlCountCurrent = hreflangUrlCountCurrent;
	}

	public Integer getHreflangUrlCountPrevious() {
		return hreflangUrlCountPrevious;
	}

	public void setHreflangUrlCountPrevious(Integer hreflangUrlCountPrevious) {
		this.hreflangUrlCountPrevious = hreflangUrlCountPrevious;
	}

	public Boolean getIndexFlgChgInd() {
		return indexFlgChgInd;
	}

	public void setIndexFlgChgInd(Boolean indexFlgChgInd) {
		this.indexFlgChgInd = indexFlgChgInd;
	}

	public String getIndexFlgCurrent() {
		return indexFlgCurrent;
	}

	public void setIndexFlgCurrent(String indexFlgCurrent) {
		this.indexFlgCurrent = indexFlgCurrent;
	}

	public String getIndexFlgPrevious() {
		return indexFlgPrevious;
	}

	public void setIndexFlgPrevious(String indexFlgPrevious) {
		this.indexFlgPrevious = indexFlgPrevious;
	}

	public Boolean getIndexableChgInd() {
		return indexableChgInd;
	}

	public void setIndexableChgInd(Boolean indexableChgInd) {
		this.indexableChgInd = indexableChgInd;
	}

	public Boolean getIndexableCurrent() {
		return indexableCurrent;
	}

	public void setIndexableCurrent(Boolean indexableCurrent) {
		this.indexableCurrent = indexableCurrent;
	}

	public Boolean getIndexablePrevious() {
		return indexablePrevious;
	}

	public void setIndexablePrevious(Boolean indexablePrevious) {
		this.indexablePrevious = indexablePrevious;
	}

	public Boolean getInsecureResourcesChgInd() {
		return insecureResourcesChgInd;
	}

	public void setInsecureResourcesChgInd(Boolean insecureResourcesChgInd) {
		this.insecureResourcesChgInd = insecureResourcesChgInd;
	}

	public String[] getInsecureResourcesCurrent() {
		return insecureResourcesCurrent;
	}

	public void setInsecureResourcesCurrent(String[] insecureResourcesCurrent) {
		this.insecureResourcesCurrent = insecureResourcesCurrent;
	}

	public String[] getInsecureResourcesPrevious() {
		return insecureResourcesPrevious;
	}

	public void setInsecureResourcesPrevious(String[] insecureResourcesPrevious) {
		this.insecureResourcesPrevious = insecureResourcesPrevious;
	}

	public Boolean getMetaCharsetChgInd() {
		return metaCharsetChgInd;
	}

	public void setMetaCharsetChgInd(Boolean metaCharsetChgInd) {
		this.metaCharsetChgInd = metaCharsetChgInd;
	}

	public String getMetaCharsetCurrent() {
		return metaCharsetCurrent;
	}

	public void setMetaCharsetCurrent(String metaCharsetCurrent) {
		this.metaCharsetCurrent = metaCharsetCurrent;
	}

	public String getMetaCharsetPrevious() {
		return metaCharsetPrevious;
	}

	public void setMetaCharsetPrevious(String metaCharsetPrevious) {
		this.metaCharsetPrevious = metaCharsetPrevious;
	}

	public Boolean getMetaContentTypeChgInd() {
		return metaContentTypeChgInd;
	}

	public void setMetaContentTypeChgInd(Boolean metaContentTypeChgInd) {
		this.metaContentTypeChgInd = metaContentTypeChgInd;
	}

	public String getMetaContentTypeCurrent() {
		return metaContentTypeCurrent;
	}

	public void setMetaContentTypeCurrent(String metaContentTypeCurrent) {
		this.metaContentTypeCurrent = metaContentTypeCurrent;
	}

	public String getMetaContentTypePrevious() {
		return metaContentTypePrevious;
	}

	public void setMetaContentTypePrevious(String metaContentTypePrevious) {
		this.metaContentTypePrevious = metaContentTypePrevious;
	}

	public Boolean getMetaDisabledSitelinksChgInd() {
		return metaDisabledSitelinksChgInd;
	}

	public void setMetaDisabledSitelinksChgInd(Boolean metaDisabledSitelinksChgInd) {
		this.metaDisabledSitelinksChgInd = metaDisabledSitelinksChgInd;
	}

	public Boolean getMetaDisabledSitelinksCurrent() {
		return metaDisabledSitelinksCurrent;
	}

	public void setMetaDisabledSitelinksCurrent(Boolean metaDisabledSitelinksCurrent) {
		this.metaDisabledSitelinksCurrent = metaDisabledSitelinksCurrent;
	}

	public Boolean getMetaDisabledSitelinksPrevious() {
		return metaDisabledSitelinksPrevious;
	}

	public void setMetaDisabledSitelinksPrevious(Boolean metaDisabledSitelinksPrevious) {
		this.metaDisabledSitelinksPrevious = metaDisabledSitelinksPrevious;
	}

	public Boolean getMetaNoodpChgInd() {
		return metaNoodpChgInd;
	}

	public void setMetaNoodpChgInd(Boolean metaNoodpChgInd) {
		this.metaNoodpChgInd = metaNoodpChgInd;
	}

	public Boolean getMetaNoodpCurrent() {
		return metaNoodpCurrent;
	}

	public void setMetaNoodpCurrent(Boolean metaNoodpCurrent) {
		this.metaNoodpCurrent = metaNoodpCurrent;
	}

	public Boolean getMetaNoodpPrevious() {
		return metaNoodpPrevious;
	}

	public void setMetaNoodpPrevious(Boolean metaNoodpPrevious) {
		this.metaNoodpPrevious = metaNoodpPrevious;
	}

	public Boolean getMetaNosnippetChgInd() {
		return metaNosnippetChgInd;
	}

	public void setMetaNosnippetChgInd(Boolean metaNosnippetChgInd) {
		this.metaNosnippetChgInd = metaNosnippetChgInd;
	}

	public Boolean getMetaNosnippetCurrent() {
		return metaNosnippetCurrent;
	}

	public void setMetaNosnippetCurrent(Boolean metaNosnippetCurrent) {
		this.metaNosnippetCurrent = metaNosnippetCurrent;
	}

	public Boolean getMetaNosnippetPrevious() {
		return metaNosnippetPrevious;
	}

	public void setMetaNosnippetPrevious(Boolean metaNosnippetPrevious) {
		this.metaNosnippetPrevious = metaNosnippetPrevious;
	}

	public Boolean getMetaNoydirChgInd() {
		return metaNoydirChgInd;
	}

	public void setMetaNoydirChgInd(Boolean metaNoydirChgInd) {
		this.metaNoydirChgInd = metaNoydirChgInd;
	}

	public Boolean getMetaNoydirCurrent() {
		return metaNoydirCurrent;
	}

	public void setMetaNoydirCurrent(Boolean metaNoydirCurrent) {
		this.metaNoydirCurrent = metaNoydirCurrent;
	}

	public Boolean getMetaNoydirPrevious() {
		return metaNoydirPrevious;
	}

	public void setMetaNoydirPrevious(Boolean metaNoydirPrevious) {
		this.metaNoydirPrevious = metaNoydirPrevious;
	}

	public Boolean getMetaRedirectChgInd() {
		return metaRedirectChgInd;
	}

	public void setMetaRedirectChgInd(Boolean metaRedirectChgInd) {
		this.metaRedirectChgInd = metaRedirectChgInd;
	}

	public Boolean getMetaRedirectCurrent() {
		return metaRedirectCurrent;
	}

	public void setMetaRedirectCurrent(Boolean metaRedirectCurrent) {
		this.metaRedirectCurrent = metaRedirectCurrent;
	}

	public Boolean getMetaRedirectPrevious() {
		return metaRedirectPrevious;
	}

	public void setMetaRedirectPrevious(Boolean metaRedirectPrevious) {
		this.metaRedirectPrevious = metaRedirectPrevious;
	}

	public Boolean getMixedRedirectsChgInd() {
		return mixedRedirectsChgInd;
	}

	public void setMixedRedirectsChgInd(Boolean mixedRedirectsChgInd) {
		this.mixedRedirectsChgInd = mixedRedirectsChgInd;
	}

	public Boolean getMixedRedirectsCurrent() {
		return mixedRedirectsCurrent;
	}

	public void setMixedRedirectsCurrent(Boolean mixedRedirectsCurrent) {
		this.mixedRedirectsCurrent = mixedRedirectsCurrent;
	}

	public Boolean getMixedRedirectsPrevious() {
		return mixedRedirectsPrevious;
	}

	public void setMixedRedirectsPrevious(Boolean mixedRedirectsPrevious) {
		this.mixedRedirectsPrevious = mixedRedirectsPrevious;
	}

	public Boolean getMobileRelAlternateUrlIsConsistentChgInd() {
		return mobileRelAlternateUrlIsConsistentChgInd;
	}

	public void setMobileRelAlternateUrlIsConsistentChgInd(Boolean mobileRelAlternateUrlIsConsistentChgInd) {
		this.mobileRelAlternateUrlIsConsistentChgInd = mobileRelAlternateUrlIsConsistentChgInd;
	}

	public Boolean getMobileRelAlternateUrlIsConsistentCurrent() {
		return mobileRelAlternateUrlIsConsistentCurrent;
	}

	public void setMobileRelAlternateUrlIsConsistentCurrent(Boolean mobileRelAlternateUrlIsConsistentCurrent) {
		this.mobileRelAlternateUrlIsConsistentCurrent = mobileRelAlternateUrlIsConsistentCurrent;
	}

	public Boolean getMobileRelAlternateUrlIsConsistentPrevious() {
		return mobileRelAlternateUrlIsConsistentPrevious;
	}

	public void setMobileRelAlternateUrlIsConsistentPrevious(Boolean mobileRelAlternateUrlIsConsistentPrevious) {
		this.mobileRelAlternateUrlIsConsistentPrevious = mobileRelAlternateUrlIsConsistentPrevious;
	}

	public Boolean getNoodpChgInd() {
		return noodpChgInd;
	}

	public void setNoodpChgInd(Boolean noodpChgInd) {
		this.noodpChgInd = noodpChgInd;
	}

	public Boolean getNoodpCurrent() {
		return noodpCurrent;
	}

	public void setNoodpCurrent(Boolean noodpCurrent) {
		this.noodpCurrent = noodpCurrent;
	}

	public Boolean getNoodpPrevious() {
		return noodpPrevious;
	}

	public void setNoodpPrevious(Boolean noodpPrevious) {
		this.noodpPrevious = noodpPrevious;
	}

	public Boolean getNosnippetChgInd() {
		return nosnippetChgInd;
	}

	public void setNosnippetChgInd(Boolean nosnippetChgInd) {
		this.nosnippetChgInd = nosnippetChgInd;
	}

	public Boolean getNosnippetCurrent() {
		return nosnippetCurrent;
	}

	public void setNosnippetCurrent(Boolean nosnippetCurrent) {
		this.nosnippetCurrent = nosnippetCurrent;
	}

	public Boolean getNosnippetPrevious() {
		return nosnippetPrevious;
	}

	public void setNosnippetPrevious(Boolean nosnippetPrevious) {
		this.nosnippetPrevious = nosnippetPrevious;
	}

	public Boolean getNoydirChgInd() {
		return noydirChgInd;
	}

	public void setNoydirChgInd(Boolean noydirChgInd) {
		this.noydirChgInd = noydirChgInd;
	}

	public Boolean getNoydirCurrent() {
		return noydirCurrent;
	}

	public void setNoydirCurrent(Boolean noydirCurrent) {
		this.noydirCurrent = noydirCurrent;
	}

	public Boolean getNoydirPrevious() {
		return noydirPrevious;
	}

	public void setNoydirPrevious(Boolean noydirPrevious) {
		this.noydirPrevious = noydirPrevious;
	}

	public Boolean getOpenGraphAddedInd() {
		return openGraphAddedInd;
	}

	public void setOpenGraphAddedInd(Boolean openGraphAddedInd) {
		this.openGraphAddedInd = openGraphAddedInd;
	}

	public Boolean getOgMarkupChgInd() {
		return ogMarkupChgInd;
	}

	public void setOgMarkupChgInd(Boolean ogMarkupChgInd) {
		this.ogMarkupChgInd = ogMarkupChgInd;
	}

	public Boolean getOpenGraphRemovedInd() {
		return openGraphRemovedInd;
	}

	public void setOpenGraphRemovedInd(Boolean openGraphRemovedInd) {
		this.openGraphRemovedInd = openGraphRemovedInd;
	}

	public OgMarkup[] getOgMarkupCurrent() {
		return ogMarkupCurrent;
	}

	public void setOgMarkupCurrent(OgMarkup[] ogMarkupCurrent) {
		this.ogMarkupCurrent = ogMarkupCurrent;
	}

	public OgMarkup[] getOgMarkupPrevious() {
		return ogMarkupPrevious;
	}

	public void setOgMarkupPrevious(OgMarkup[] ogMarkupPrevious) {
		this.ogMarkupPrevious = ogMarkupPrevious;
	}

	public Boolean getOgMarkupLengthChgInd() {
		return ogMarkupLengthChgInd;
	}

	public void setOgMarkupLengthChgInd(Boolean ogMarkupLengthChgInd) {
		this.ogMarkupLengthChgInd = ogMarkupLengthChgInd;
	}

	public Integer getOgMarkupLengthCurrent() {
		return ogMarkupLengthCurrent;
	}

	public void setOgMarkupLengthCurrent(Integer ogMarkupLengthCurrent) {
		this.ogMarkupLengthCurrent = ogMarkupLengthCurrent;
	}

	public Integer getOgMarkupLengthPrevious() {
		return ogMarkupLengthPrevious;
	}

	public void setOgMarkupLengthPrevious(Integer ogMarkupLengthPrevious) {
		this.ogMarkupLengthPrevious = ogMarkupLengthPrevious;
	}

	public Boolean getOutlinkCountChgInd() {
		return outlinkCountChgInd;
	}

	public void setOutlinkCountChgInd(Boolean outlinkCountChgInd) {
		this.outlinkCountChgInd = outlinkCountChgInd;
	}

	public Integer getOutlinkCountCurrent() {
		return outlinkCountCurrent;
	}

	public void setOutlinkCountCurrent(Integer outlinkCountCurrent) {
		this.outlinkCountCurrent = outlinkCountCurrent;
	}

	public Integer getOutlinkCountPrevious() {
		return outlinkCountPrevious;
	}

	public void setOutlinkCountPrevious(Integer outlinkCountPrevious) {
		this.outlinkCountPrevious = outlinkCountPrevious;
	}

	public String getPageAnalysisResultsChgIndJson() {
		return pageAnalysisResultsChgIndJson;
	}

	public void setPageAnalysisResultsChgIndJson(String pageAnalysisResultsChgIndJson) {
		this.pageAnalysisResultsChgIndJson = pageAnalysisResultsChgIndJson;
	}

	public Boolean getPageLinkChgInd() {
		return pageLinkChgInd;
	}

	public void setPageLinkChgInd(Boolean pageLinkChgInd) {
		this.pageLinkChgInd = pageLinkChgInd;
	}

	public PageLink[] getPageLinkCurrent() {
		return pageLinkCurrent;
	}

	public void setPageLinkCurrent(PageLink[] pageLinkCurrent) {
		this.pageLinkCurrent = pageLinkCurrent;
	}

	public PageLink[] getPageLinkPrevious() {
		return pageLinkPrevious;
	}

	public void setPageLinkPrevious(PageLink[] pageLinkPrevious) {
		this.pageLinkPrevious = pageLinkPrevious;
	}

	public Boolean getRedirectBlockedChgInd() {
		return redirectBlockedChgInd;
	}

	public void setRedirectBlockedChgInd(Boolean redirectBlockedChgInd) {
		this.redirectBlockedChgInd = redirectBlockedChgInd;
	}

	public Boolean getRedirectBlockedCurrent() {
		return redirectBlockedCurrent;
	}

	public void setRedirectBlockedCurrent(Boolean redirectBlockedCurrent) {
		this.redirectBlockedCurrent = redirectBlockedCurrent;
	}

	public Boolean getRedirectBlockedPrevious() {
		return redirectBlockedPrevious;
	}

	public void setRedirectBlockedPrevious(Boolean redirectBlockedPrevious) {
		this.redirectBlockedPrevious = redirectBlockedPrevious;
	}

	public Boolean getRedirectBlockedReasonChgInd() {
		return redirectBlockedReasonChgInd;
	}

	public void setRedirectBlockedReasonChgInd(Boolean redirectBlockedReasonChgInd) {
		this.redirectBlockedReasonChgInd = redirectBlockedReasonChgInd;
	}

	public String getRedirectBlockedReasonCurrent() {
		return redirectBlockedReasonCurrent;
	}

	public void setRedirectBlockedReasonCurrent(String redirectBlockedReasonCurrent) {
		this.redirectBlockedReasonCurrent = redirectBlockedReasonCurrent;
	}

	public String getRedirectBlockedReasonPrevious() {
		return redirectBlockedReasonPrevious;
	}

	public void setRedirectBlockedReasonPrevious(String redirectBlockedReasonPrevious) {
		this.redirectBlockedReasonPrevious = redirectBlockedReasonPrevious;
	}

	public Boolean getRedirectChainChgInd() {
		return redirectChainChgInd;
	}

	public void setRedirectChainChgInd(Boolean redirectChainChgInd) {
		this.redirectChainChgInd = redirectChainChgInd;
	}

	public RedirectChain[] getRedirectChainCurrent() {
		return redirectChainCurrent;
	}

	public void setRedirectChainCurrent(RedirectChain[] redirectChainCurrent) {
		this.redirectChainCurrent = redirectChainCurrent;
	}

	public RedirectChain[] getRedirectChainPrevious() {
		return redirectChainPrevious;
	}

	public void setRedirectChainPrevious(RedirectChain[] redirectChainPrevious) {
		this.redirectChainPrevious = redirectChainPrevious;
	}

	public Boolean getRedirectFinalUrlChgInd() {
		return redirectFinalUrlChgInd;
	}

	public void setRedirectFinalUrlChgInd(Boolean redirectFinalUrlChgInd) {
		this.redirectFinalUrlChgInd = redirectFinalUrlChgInd;
	}

	public String getRedirectFinalUrlCurrent() {
		return redirectFinalUrlCurrent;
	}

	public void setRedirectFinalUrlCurrent(String redirectFinalUrlCurrent) {
		this.redirectFinalUrlCurrent = redirectFinalUrlCurrent;
	}

	public String getRedirectFinalUrlPrevious() {
		return redirectFinalUrlPrevious;
	}

	public void setRedirectFinalUrlPrevious(String redirectFinalUrlPrevious) {
		this.redirectFinalUrlPrevious = redirectFinalUrlPrevious;
	}

	public Boolean getRedirectTimesChgInd() {
		return redirectTimesChgInd;
	}

	public void setRedirectTimesChgInd(Boolean redirectTimesChgInd) {
		this.redirectTimesChgInd = redirectTimesChgInd;
	}

	public Integer getRedirectTimesCurrent() {
		return redirectTimesCurrent;
	}

	public void setRedirectTimesCurrent(Integer redirectTimesCurrent) {
		this.redirectTimesCurrent = redirectTimesCurrent;
	}

	public Integer getRedirectTimesPrevious() {
		return redirectTimesPrevious;
	}

	public void setRedirectTimesPrevious(Integer redirectTimesPrevious) {
		this.redirectTimesPrevious = redirectTimesPrevious;
	}

	public Boolean getResponseCodeChgInd() {
		return responseCodeChgInd;
	}

	public void setResponseCodeChgInd(Boolean responseCodeChgInd) {
		this.responseCodeChgInd = responseCodeChgInd;
	}

	public Boolean getRedirect301DetectedInd() {
		return redirect301DetectedInd;
	}

	public void setRedirect301DetectedInd(Boolean redirect301DetectedInd) {
		this.redirect301DetectedInd = redirect301DetectedInd;
	}

	public Boolean getRedirect301RemovedInd() {
		return redirect301RemovedInd;
	}

	public void setRedirect301RemovedInd(Boolean redirect301RemovedInd) {
		this.redirect301RemovedInd = redirect301RemovedInd;
	}

	public Boolean getRedirect302DetectedInd() {
		return redirect302DetectedInd;
	}

	public void setRedirect302DetectedInd(Boolean redirect302DetectedInd) {
		this.redirect302DetectedInd = redirect302DetectedInd;
	}

	public Boolean getRedirect302RemovedInd() {
		return redirect302RemovedInd;
	}

	public void setRedirect302RemovedInd(Boolean redirect302RemovedInd) {
		this.redirect302RemovedInd = redirect302RemovedInd;
	}

	public Boolean getRedirectDiffCodeInd() {
		return redirectDiffCodeInd;
	}

	public void setRedirectDiffCodeInd(Boolean redirectDiffCodeInd) {
		this.redirectDiffCodeInd = redirectDiffCodeInd;
	}

	public String getResponseCodeCurrent() {
		return responseCodeCurrent;
	}

	public void setResponseCodeCurrent(String responseCodeCurrent) {
		this.responseCodeCurrent = responseCodeCurrent;
	}

	public String getResponseCodePrevious() {
		return responseCodePrevious;
	}

	public void setResponseCodePrevious(String responseCodePrevious) {
		this.responseCodePrevious = responseCodePrevious;
	}

	public Boolean getResponseHeadersAddedInd() {
		return responseHeadersAddedInd;
	}

	public void setResponseHeadersAddedInd(Boolean responseHeadersAddedInd) {
		this.responseHeadersAddedInd = responseHeadersAddedInd;
	}

	public Boolean getResponseHeadersRemovedInd() {
		return responseHeadersRemovedInd;
	}

	public void setResponseHeadersRemovedInd(Boolean responseHeadersRemovedInd) {
		this.responseHeadersRemovedInd = responseHeadersRemovedInd;
	}

	public ResponseHeaders[] getResponseHeadersCurrent() {
		return responseHeadersCurrent;
	}

	public void setResponseHeadersCurrent(ResponseHeaders[] responseHeadersCurrent) {
		this.responseHeadersCurrent = responseHeadersCurrent;
	}

	public ResponseHeaders[] getResponseHeadersPrevious() {
		return responseHeadersPrevious;
	}

	public void setResponseHeadersPrevious(ResponseHeaders[] responseHeadersPrevious) {
		this.responseHeadersPrevious = responseHeadersPrevious;
	}

	public Boolean getRobotsAddedInd() {
		return robotsAddedInd;
	}

	public void setRobotsAddedInd(Boolean robotsAddedInd) {
		this.robotsAddedInd = robotsAddedInd;
	}

	public Boolean getRobotsContentsChgInd() {
		return robotsContentsChgInd;
	}

	public void setRobotsContentsChgInd(Boolean robotsContentsChgInd) {
		this.robotsContentsChgInd = robotsContentsChgInd;
	}

	public Boolean getRobotsRemovedInd() {
		return robotsRemovedInd;
	}

	public void setRobotsRemovedInd(Boolean robotsRemovedInd) {
		this.robotsRemovedInd = robotsRemovedInd;
	}

	public String getRobotsContentsCurrent() {
		return robotsContentsCurrent;
	}

	public void setRobotsContentsCurrent(String robotsContentsCurrent) {
		this.robotsContentsCurrent = robotsContentsCurrent;
	}

	public String getRobotsContentsPrevious() {
		return robotsContentsPrevious;
	}

	public void setRobotsContentsPrevious(String robotsContentsPrevious) {
		this.robotsContentsPrevious = robotsContentsPrevious;
	}

	public Boolean getStructuredDataChgInd() {
		return structuredDataChgInd;
	}

	public void setStructuredDataChgInd(Boolean structuredDataChgInd) {
		this.structuredDataChgInd = structuredDataChgInd;
	}

	public StructuredData getStructuredDataCurrent() {
		return structuredDataCurrent;
	}

	public void setStructuredDataCurrent(StructuredData structuredDataCurrent) {
		this.structuredDataCurrent = structuredDataCurrent;
	}

	public StructuredData getStructuredDataPrevious() {
		return structuredDataPrevious;
	}

	public void setStructuredDataPrevious(StructuredData structuredDataPrevious) {
		this.structuredDataPrevious = structuredDataPrevious;
	}

	public Boolean getTitleAddedInd() {
		return titleAddedInd;
	}

	public void setTitleAddedInd(Boolean titleAddedInd) {
		this.titleAddedInd = titleAddedInd;
	}

	public Boolean getTitleChgInd() {
		return titleChgInd;
	}

	public void setTitleChgInd(Boolean titleChgInd) {
		this.titleChgInd = titleChgInd;
	}

	public Boolean getTitleRemovedInd() {
		return titleRemovedInd;
	}

	public void setTitleRemovedInd(Boolean titleRemovedInd) {
		this.titleRemovedInd = titleRemovedInd;
	}

	public String getTitleCurrent() {
		return titleCurrent;
	}

	public void setTitleCurrent(String titleCurrent) {
		this.titleCurrent = titleCurrent;
	}

	public String getTitlePrevious() {
		return titlePrevious;
	}

	public void setTitlePrevious(String titlePrevious) {
		this.titlePrevious = titlePrevious;
	}

	public Boolean getTitleLengthChgInd() {
		return titleLengthChgInd;
	}

	public void setTitleLengthChgInd(Boolean titleLengthChgInd) {
		this.titleLengthChgInd = titleLengthChgInd;
	}

	public Integer getTitleLengthCurrent() {
		return titleLengthCurrent;
	}

	public void setTitleLengthCurrent(Integer titleLengthCurrent) {
		this.titleLengthCurrent = titleLengthCurrent;
	}

	public Integer getTitleLengthPrevious() {
		return titleLengthPrevious;
	}

	public void setTitleLengthPrevious(Integer titleLengthPrevious) {
		this.titleLengthPrevious = titleLengthPrevious;
	}

	public Boolean getViewportAddedInd() {
		return viewportAddedInd;
	}

	public void setViewportAddedInd(Boolean viewportAddedInd) {
		this.viewportAddedInd = viewportAddedInd;
	}

	public Boolean getViewportContentChgInd() {
		return viewportContentChgInd;
	}

	public void setViewportContentChgInd(Boolean viewportContentChgInd) {
		this.viewportContentChgInd = viewportContentChgInd;
	}

	public Boolean getViewportRemovedInd() {
		return viewportRemovedInd;
	}

	public void setViewportRemovedInd(Boolean viewportRemovedInd) {
		this.viewportRemovedInd = viewportRemovedInd;
	}

	public String getViewportContentCurrent() {
		return viewportContentCurrent;
	}

	public void setViewportContentCurrent(String viewportContentCurrent) {
		this.viewportContentCurrent = viewportContentCurrent;
	}

	public String getViewportContentPrevious() {
		return viewportContentPrevious;
	}

	public void setViewportContentPrevious(String viewportContentPrevious) {
		this.viewportContentPrevious = viewportContentPrevious;
	}

	public Integer getSign() {
		return sign;
	}

	public void setSign(Integer sign) {
		this.sign = sign;
	}

	public Integer getTotal() {
		return total;
	}

	public void setTotal(Integer total) {
		this.total = total;
	}

	@Override
	public String toString() {
		return "TargetUrlChangeClickHouseEntity [domainId=" + domainId + ", url=" + url + ", trackDate=" + trackDate + ", urlHash=" + urlHash + ", urlMurmurHash="
				+ urlMurmurHash + ", currentCrawlTimestamp=" + currentCrawlTimestamp + ", previousCrawlTimestamp=" + previousCrawlTimestamp + ", updateTimestamp="
				+ updateTimestamp + ", alternateLinksChgInd=" + alternateLinksChgInd + ", alternateLinksCurrent=" + Arrays.toString(alternateLinksCurrent)
				+ ", alternateLinksPrevious=" + Arrays.toString(alternateLinksPrevious) + ", amphtmlHrefChgInd=" + amphtmlHrefChgInd + ", amphtmlHrefCurrent="
				+ amphtmlHrefCurrent + ", amphtmlHrefPrevious=" + amphtmlHrefPrevious + ", analyzedUrlSChgInd=" + analyzedUrlSChgInd + ", analyzedUrlSCurrent="
				+ analyzedUrlSCurrent + ", analyzedUrlSPrevious=" + analyzedUrlSPrevious + ", archiveFlgChgInd=" + archiveFlgChgInd + ", archiveFlgCurrent="
				+ archiveFlgCurrent + ", archiveFlgPrevious=" + archiveFlgPrevious + ", baseTagAddedInd=" + baseTagAddedInd + ", baseTagChgInd=" + baseTagChgInd
				+ ", baseTagRemovedInd=" + baseTagRemovedInd + ", baseTagCurrent=" + baseTagCurrent + ", baseTagPrevious=" + baseTagPrevious + ", baseTagTargetChgInd="
				+ baseTagTargetChgInd + ", baseTagTargetCurrent=" + baseTagTargetCurrent + ", baseTagTargetPrevious=" + baseTagTargetPrevious
				+ ", blockedByRobotsChgInd=" + blockedByRobotsChgInd + ", blockedByRobotsCurrent=" + blockedByRobotsCurrent + ", blockedByRobotsPrevious="
				+ blockedByRobotsPrevious + ", canonicalAddedInd=" + canonicalAddedInd + ", canonicalChgInd=" + canonicalChgInd + ", canonicalRemovedInd="
				+ canonicalRemovedInd + ", canonicalCurrent=" + canonicalCurrent + ", canonicalPrevious=" + canonicalPrevious + ", canonicalHeaderFlagChgInd="
				+ canonicalHeaderFlagChgInd + ", canonicalHeaderFlagCurrent=" + canonicalHeaderFlagCurrent + ", canonicalHeaderFlagPrevious="
				+ canonicalHeaderFlagPrevious + ", canonicalHeaderTypeChgInd=" + canonicalHeaderTypeChgInd + ", canonicalHeaderTypeCurrent="
				+ canonicalHeaderTypeCurrent + ", canonicalHeaderTypePrevious=" + canonicalHeaderTypePrevious + ", canonicalTypeChgInd=" + canonicalTypeChgInd
				+ ", canonicalTypeCurrent=" + canonicalTypeCurrent + ", canonicalTypePrevious=" + canonicalTypePrevious + ", canonicalUrlIsConsistentChgInd="
				+ canonicalUrlIsConsistentChgInd + ", canonicalUrlIsConsistentCurrent=" + canonicalUrlIsConsistentCurrent + ", canonicalUrlIsConsistentPrevious="
				+ canonicalUrlIsConsistentPrevious + ", contentTypeChgInd=" + contentTypeChgInd + ", contentTypeCurrent=" + contentTypeCurrent
				+ ", contentTypePrevious=" + contentTypePrevious + ", customDataAddedInd=" + customDataAddedInd + ", customDataChgInd=" + customDataChgInd
				+ ", customDataRemovedInd=" + customDataRemovedInd + ", customDataCurrent=" + Arrays.toString(customDataCurrent) + ", customDataPrevious="
				+ Arrays.toString(customDataPrevious) + ", descriptionAddedInd=" + descriptionAddedInd + ", descriptionChgInd=" + descriptionChgInd
				+ ", descriptionRemovedInd=" + descriptionRemovedInd + ", descriptionCurrent=" + descriptionCurrent + ", descriptionPrevious=" + descriptionPrevious
				+ ", descriptionLengthChgInd=" + descriptionLengthChgInd + ", descriptionLengthCurrent=" + descriptionLengthCurrent + ", descriptionLengthPrevious="
				+ descriptionLengthPrevious + ", errorMessageChgInd=" + errorMessageChgInd + ", errorMessageCurrent=" + errorMessageCurrent + ", errorMessagePrevious="
				+ errorMessagePrevious + ", finalResponseCodeChgInd=" + finalResponseCodeChgInd + ", finalResponseCodeCurrent=" + finalResponseCodeCurrent
				+ ", finalResponseCodePrevious=" + finalResponseCodePrevious + ", followFlgChgInd=" + followFlgChgInd + ", followFlgCurrent=" + followFlgCurrent
				+ ", followFlgPrevious=" + followFlgPrevious + ", h1AddedInd=" + h1AddedInd + ", h1ChgInd=" + h1ChgInd + ", h1RemovedInd=" + h1RemovedInd
				+ ", h1Current=" + Arrays.toString(h1Current) + ", h1Previous=" + Arrays.toString(h1Previous) + ", h1CountChgInd=" + h1CountChgInd + ", h1CountCurrent="
				+ h1CountCurrent + ", h1CountPrevious=" + h1CountPrevious + ", h1LengthChgInd=" + h1LengthChgInd + ", h1LengthCurrent=" + h1LengthCurrent
				+ ", h1LengthPrevious=" + h1LengthPrevious + ", h2AddedInd=" + h2AddedInd + ", h2ChgInd=" + h2ChgInd + ", h2RemovedInd=" + h2RemovedInd + ", h2Current="
				+ Arrays.toString(h2Current) + ", h2Previous=" + Arrays.toString(h2Previous) + ", headerNoarchiveChgInd=" + headerNoarchiveChgInd
				+ ", headerNoarchiveCurrent=" + headerNoarchiveCurrent + ", headerNoarchivePrevious=" + headerNoarchivePrevious + ", headerNofollowChgInd="
				+ headerNofollowChgInd + ", headerNofollowCurrent=" + headerNofollowCurrent + ", headerNofollowPrevious=" + headerNofollowPrevious
				+ ", headerNoindexChgInd=" + headerNoindexChgInd + ", headerNoindexCurrent=" + headerNoindexCurrent + ", headerNoindexPrevious=" + headerNoindexPrevious
				+ ", headerNoodpChgInd=" + headerNoodpChgInd + ", headerNoodpCurrent=" + headerNoodpCurrent + ", headerNoodpPrevious=" + headerNoodpPrevious
				+ ", headerNosnippetChgInd=" + headerNosnippetChgInd + ", headerNosnippetCurrent=" + headerNosnippetCurrent + ", headerNosnippetPrevious="
				+ headerNosnippetPrevious + ", headerNoydirChgInd=" + headerNoydirChgInd + ", headerNoydirCurrent=" + headerNoydirCurrent + ", headerNoydirPrevious="
				+ headerNoydirPrevious + ", hreflangErrorsChgInd=" + hreflangErrorsChgInd + ", hreflangErrorsCurrent=" + hreflangErrorsCurrent
				+ ", hreflangErrorsPrevious=" + hreflangErrorsPrevious + ", hreflangLinksChgInd=" + hreflangLinksChgInd + ", hreflangLinksCurrent="
				+ Arrays.toString(hreflangLinksCurrent) + ", hreflangLinksPrevious=" + Arrays.toString(hreflangLinksPrevious) + ", hreflangLinksOutCountChgInd="
				+ hreflangLinksOutCountChgInd + ", hreflangLinksOutCountCurrent=" + hreflangLinksOutCountCurrent + ", hreflangLinksOutCountPrevious="
				+ hreflangLinksOutCountPrevious + ", hreflangLinksAddedInd=" + hreflangLinksAddedInd + ", hreflangUrlCountChgInd=" + hreflangUrlCountChgInd
				+ ", hreflangLinksRemovedInd=" + hreflangLinksRemovedInd + ", hreflangUrlCountCurrent=" + hreflangUrlCountCurrent + ", hreflangUrlCountPrevious="
				+ hreflangUrlCountPrevious + ", indexFlgChgInd=" + indexFlgChgInd + ", indexFlgCurrent=" + indexFlgCurrent + ", indexFlgPrevious=" + indexFlgPrevious
				+ ", indexableChgInd=" + indexableChgInd + ", indexableCurrent=" + indexableCurrent + ", indexablePrevious=" + indexablePrevious
				+ ", insecureResourcesChgInd=" + insecureResourcesChgInd + ", insecureResourcesCurrent=" + Arrays.toString(insecureResourcesCurrent)
				+ ", insecureResourcesPrevious=" + Arrays.toString(insecureResourcesPrevious) + ", metaCharsetChgInd=" + metaCharsetChgInd + ", metaCharsetCurrent="
				+ metaCharsetCurrent + ", metaCharsetPrevious=" + metaCharsetPrevious + ", metaContentTypeChgInd=" + metaContentTypeChgInd + ", metaContentTypeCurrent="
				+ metaContentTypeCurrent + ", metaContentTypePrevious=" + metaContentTypePrevious + ", metaDisabledSitelinksChgInd=" + metaDisabledSitelinksChgInd
				+ ", metaDisabledSitelinksCurrent=" + metaDisabledSitelinksCurrent + ", metaDisabledSitelinksPrevious=" + metaDisabledSitelinksPrevious
				+ ", metaNoodpChgInd=" + metaNoodpChgInd + ", metaNoodpCurrent=" + metaNoodpCurrent + ", metaNoodpPrevious=" + metaNoodpPrevious
				+ ", metaNosnippetChgInd=" + metaNosnippetChgInd + ", metaNosnippetCurrent=" + metaNosnippetCurrent + ", metaNosnippetPrevious=" + metaNosnippetPrevious
				+ ", metaNoydirChgInd=" + metaNoydirChgInd + ", metaNoydirCurrent=" + metaNoydirCurrent + ", metaNoydirPrevious=" + metaNoydirPrevious
				+ ", metaRedirectChgInd=" + metaRedirectChgInd + ", metaRedirectCurrent=" + metaRedirectCurrent + ", metaRedirectPrevious=" + metaRedirectPrevious
				+ ", mixedRedirectsChgInd=" + mixedRedirectsChgInd + ", mixedRedirectsCurrent=" + mixedRedirectsCurrent + ", mixedRedirectsPrevious="
				+ mixedRedirectsPrevious + ", mobileRelAlternateUrlIsConsistentChgInd=" + mobileRelAlternateUrlIsConsistentChgInd
				+ ", mobileRelAlternateUrlIsConsistentCurrent=" + mobileRelAlternateUrlIsConsistentCurrent + ", mobileRelAlternateUrlIsConsistentPrevious="
				+ mobileRelAlternateUrlIsConsistentPrevious + ", noodpChgInd=" + noodpChgInd + ", noodpCurrent=" + noodpCurrent + ", noodpPrevious=" + noodpPrevious
				+ ", nosnippetChgInd=" + nosnippetChgInd + ", nosnippetCurrent=" + nosnippetCurrent + ", nosnippetPrevious=" + nosnippetPrevious + ", noydirChgInd="
				+ noydirChgInd + ", noydirCurrent=" + noydirCurrent + ", noydirPrevious=" + noydirPrevious + ", openGraphAddedInd=" + openGraphAddedInd
				+ ", ogMarkupChgInd=" + ogMarkupChgInd + ", openGraphRemovedInd=" + openGraphRemovedInd + ", ogMarkupCurrent=" + Arrays.toString(ogMarkupCurrent)
				+ ", ogMarkupPrevious=" + Arrays.toString(ogMarkupPrevious) + ", ogMarkupLengthChgInd=" + ogMarkupLengthChgInd + ", ogMarkupLengthCurrent="
				+ ogMarkupLengthCurrent + ", ogMarkupLengthPrevious=" + ogMarkupLengthPrevious + ", outlinkCountChgInd=" + outlinkCountChgInd + ", outlinkCountCurrent="
				+ outlinkCountCurrent + ", outlinkCountPrevious=" + outlinkCountPrevious + ", pageAnalysisResultsChgIndJson=" + pageAnalysisResultsChgIndJson
				+ ", pageLinkChgInd=" + pageLinkChgInd + ", pageLinkCurrent=" + Arrays.toString(pageLinkCurrent) + ", pageLinkPrevious="
				+ Arrays.toString(pageLinkPrevious) + ", redirectBlockedChgInd=" + redirectBlockedChgInd + ", redirectBlockedCurrent=" + redirectBlockedCurrent
				+ ", redirectBlockedPrevious=" + redirectBlockedPrevious + ", redirectBlockedReasonChgInd=" + redirectBlockedReasonChgInd
				+ ", redirectBlockedReasonCurrent=" + redirectBlockedReasonCurrent + ", redirectBlockedReasonPrevious=" + redirectBlockedReasonPrevious
				+ ", redirectChainChgInd=" + redirectChainChgInd + ", redirectChainCurrent=" + Arrays.toString(redirectChainCurrent) + ", redirectChainPrevious="
				+ Arrays.toString(redirectChainPrevious) + ", redirectFinalUrlChgInd=" + redirectFinalUrlChgInd + ", redirectFinalUrlCurrent=" + redirectFinalUrlCurrent
				+ ", redirectFinalUrlPrevious=" + redirectFinalUrlPrevious + ", redirectTimesChgInd=" + redirectTimesChgInd + ", redirectTimesCurrent="
				+ redirectTimesCurrent + ", redirectTimesPrevious=" + redirectTimesPrevious + ", responseCodeChgInd=" + responseCodeChgInd + ", redirect301DetectedInd="
				+ redirect301DetectedInd + ", redirect301RemovedInd=" + redirect301RemovedInd + ", redirect302DetectedInd=" + redirect302DetectedInd
				+ ", redirect302RemovedInd=" + redirect302RemovedInd + ", redirectDiffCodeInd=" + redirectDiffCodeInd + ", responseCodeCurrent=" + responseCodeCurrent
				+ ", responseCodePrevious=" + responseCodePrevious + ", responseHeadersAddedInd=" + responseHeadersAddedInd + ", responseHeadersRemovedInd="
				+ responseHeadersRemovedInd + ", responseHeadersCurrent=" + Arrays.toString(responseHeadersCurrent) + ", responseHeadersPrevious="
				+ Arrays.toString(responseHeadersPrevious) + ", robotsAddedInd=" + robotsAddedInd + ", robotsContentsChgInd=" + robotsContentsChgInd
				+ ", robotsRemovedInd=" + robotsRemovedInd + ", robotsContentsCurrent=" + robotsContentsCurrent + ", robotsContentsPrevious=" + robotsContentsPrevious
				+ ", structuredDataChgInd=" + structuredDataChgInd + ", structuredDataCurrent=" + structuredDataCurrent + ", structuredDataPrevious="
				+ structuredDataPrevious + ", titleAddedInd=" + titleAddedInd + ", titleChgInd=" + titleChgInd + ", titleRemovedInd=" + titleRemovedInd
				+ ", titleCurrent=" + titleCurrent + ", titlePrevious=" + titlePrevious + ", titleLengthChgInd=" + titleLengthChgInd + ", titleLengthCurrent="
				+ titleLengthCurrent + ", titleLengthPrevious=" + titleLengthPrevious + ", viewportAddedInd=" + viewportAddedInd + ", viewportContentChgInd="
				+ viewportContentChgInd + ", viewportRemovedInd=" + viewportRemovedInd + ", viewportContentCurrent=" + viewportContentCurrent
				+ ", viewportContentPrevious=" + viewportContentPrevious + ", sign=" + sign + ", total=" + total + "]";
	}

}
