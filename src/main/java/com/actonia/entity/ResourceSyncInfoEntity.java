package com.actonia.entity;

import java.util.Date;

public class ResourceSyncInfoEntity {
	public static final int RESOURCE_TYPE_KEYWORD = 1;
	public static final int RESOURCE_TYPE_KEYWORD_TAG = 2;
	public static final int RESOURCE_TYPE_KEYWORD_TAG_RELATION = 3;
	// https://www.wrike.com/open.htm?id=377174775
	public static final int RESOURCE_TYPE_KEYWORD_TAG_HISTORY = 101;
	public static final int RESOURCE_TYPE_KEYWORD_TAG_RELATION_HISTORY = 102;
	
	public static final int RESOURCE_TYPE_SYNC_DELETED_KEYWORD_TO_KP = 201; // https://www.wrike.com/open.htm?id=491311534
	public static final int RESOURCE_TYPE_SYNC_ADDED_KEYWORD_TO_KP = 202; // https://www.wrike.com/open.htm?id=491755867
	
	public static final int RESOURCE_TYPE_SYNC_KEYWORD_SEARCH_ENGINE_DOMAIN_REL_LOG = 301; // https://www.wrike.com/open.htm?id=525418227
	
	public static final int RESOURCE_TYPE_SYNC_CDB_TRACKED_KEYWORD_LOG = 401; // https://www.wrike.com/open.htm?id=549677363
	public static final int RESOURCE_TYPE_SYNC_KEYWORD_TAG_LOG = 402;
	
	public static final int RESOURCE_TYPE_SYNC_CDB_TRACKED_PAGE_LOG = 501; // https://www.wrike.com/open.htm?id=661255918
	public static final int RESOURCE_TYPE_SYNC_PAGE_TAG_LOG = 502;
	public static final int RESOURCE_TYPE_SYNC_PAGE_TAG_RELATION_LOG = 503;
	
	public static final int RESOURCE_TYPE_TAG_HISTORY = 101;
	public static final int RESOURCE_TYPE_TAG_RELATION_HISTORY = 102;

	public static final int RESOURCE_TYPE_NATIONAL_SV = 601;
	public static final int RESOURCE_TYPE_GEO_SV = 602;

	public static final int RESOURCE_TYPE_POLITE_CRAWL_STATE_LOG = 801;

	public static final int STATUS_PROCESSING = 1;
	public static final int STATUS_PROCESS_FINISHED_WITHOUT_ERROR = 2;
	public static final int STATUS_PROCESS_FINISHED_WITH_ERROR = 3;
	public static final int STATUS_NO_NEED_REPROCESS = 4;
	
	private int id;
	private int resourceType;
	private int resourceAddDate;
	private long fromLogId;
	private long toLogId;
	private int status;
	private Integer logCnt;
	private Integer loadedCnt;
	private Date processEndDate;
	private Date createDate;
	
	public int getId() {
		return id;
	}
	
	public void setId(int id) {
		this.id = id;
	}
	
	public int getResourceType() {
		return resourceType;
	}
	
	public void setResourceType(int resourceType) {
		this.resourceType = resourceType;
	}
	
	public int getResourceAddDate() {
		return resourceAddDate;
	}
	
	public void setResourceAddDate(int resourceAddDate) {
		this.resourceAddDate = resourceAddDate;
	}
	
	public long getFromLogId() {
		return fromLogId;
	}
	
	public void setFromLogId(long fromLogId) {
		this.fromLogId = fromLogId;
	}
	
	public long getToLogId() {
		return toLogId;
	}
	
	public void setToLogId(long toLogId) {
		this.toLogId = toLogId;
	}
	
	public int getStatus() {
		return status;
	}
	
	public void setStatus(int status) {
		this.status = status;
	}
	
	public Integer getLogCnt() {
		return logCnt;
	}

	public void setLogCnt(Integer logCnt) {
		this.logCnt = logCnt;
	}

	public Integer getLoadedCnt() {
		return loadedCnt;
	}

	public void setLoadedCnt(Integer loadedCnt) {
		this.loadedCnt = loadedCnt;
	}
	
	public Date getProcessEndDate() {
		return processEndDate;
	}
	
	public void setProcessEndDate(Date processEndDate) {
		this.processEndDate = processEndDate;
	}
	
	public Date getCreateDate() {
		return createDate;
	}
	
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public static ResourceSyncInfoEntity createPoliteCrawlStateLogInfo(long fromLogId) {
		ResourceSyncInfoEntity entity = new ResourceSyncInfoEntity();
		entity.setResourceType(ResourceSyncInfoEntity.RESOURCE_TYPE_POLITE_CRAWL_STATE_LOG);
		entity.setCreateDate(new Date());
		entity.setFromLogId(fromLogId);
		return entity;
	}

	@Override
	public String toString() {
		return "ResourceSyncInfoEntity{" +
				"id=" + id +
				", resourceType=" + resourceType +
				", resourceAddDate=" + resourceAddDate +
				", fromLogId=" + fromLogId +
				", toLogId=" + toLogId +
				", status=" + status +
				", logCnt=" + logCnt +
				", loadedCnt=" + loadedCnt +
				", processEndDate=" + processEndDate +
				", createDate=" + createDate +
				'}';
	}
}
