package com.actonia.entity;

import java.time.LocalDateTime;

public class PoliteCrawlInstance {

    private Integer id;

    private Integer ownDomainId;
    /**
     * Type of crawl being performed, either target URL or competitor URL.
     * 1: target URL, 2: competitor URL
     */
    private CrawlTypeEnum crawlType;
    /**
     * Date of the crawl in YYYYMMDD format.
     */
    private Integer crawlDate;
    /**
     * Status of the sending process for the URLs being crawled.
     * 1: sending, 2: send completed, 3: send error, 4: no managed URL
     */
    private SendStatusEnum sendStatus;
    /**
     * End date for sending URLs to SQS.
     */
    private LocalDateTime endSendDate;
    /**
     * Count of URLs managed in the t_target_url table for the target URL.
     */
    private Integer managedUrlCount;

    /**
     * ranking URL count from RI
     */
    private Integer rankUrlCount;
    /**
     * Total count of URLs sent.
     */
    private Integer totalSendCount;

    /**
     * domain specific SQS name'
     */
    private String queueName;

    /**
     * domain level parameters/settings for crawl
     */
    private String crawlParam;

    /**
     * Status of the crawling process.
     * 0: not started, 1: crawling, 2: crawl completed
     */
    private CrawlStatusEnum crawlStatus;
    /**
     * Start date for the crawling process.
     */
    private LocalDateTime startCrawlDate;
    /**
     * End date for the crawling process.
     */
    private LocalDateTime endCrawlDate;
    /**
     * Count of URLs skipped from being saved to dis_target_url_html due to no change.
     */
    private Integer noChangeCount;
    /**
     * Count of URLs saved to dis_target_url_html/dis_target_url_html_daily.
     */
    private Integer saveToChCount;
    /**
     * Date and time the crawl instance was created.
     */
    private LocalDateTime createDate;

    // create a new instance before targetUrl of this ownDomainId is sending
    public static PoliteCrawlInstance targetUrlSending(Integer ownDomainId, Integer crawlDate) {
        final PoliteCrawlInstance politeCrawlInstance = new PoliteCrawlInstance();
        politeCrawlInstance.setOwnDomainId(ownDomainId);
        // set crawl type to target url
        politeCrawlInstance.setCrawlType(CrawlTypeEnum.TARGET_URL);
        politeCrawlInstance.setCrawlDate(crawlDate);
        // set send status to sending
        politeCrawlInstance.setSendStatus(SendStatusEnum.SENDING);
        // set crawl status to not started
        politeCrawlInstance.setCrawlStatus(CrawlStatusEnum.NOT_STARTED);
        // set create date to current time
        politeCrawlInstance.setCreateDate(LocalDateTime.now());
        return politeCrawlInstance;
    }

    public void updateSendStatusAndEndSendDate(SendStatusEnum sendStatus, Integer totalSendCount) {
        this.sendStatus = sendStatus;
        if (sendStatus == SendStatusEnum.SEND_COMPLETED) {
            this.totalSendCount = totalSendCount;
        }
        this.endSendDate = LocalDateTime.now();
    }


    // getters and setters


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOwnDomainId() {
        return ownDomainId;
    }

    public void setOwnDomainId(Integer ownDomainId) {
        this.ownDomainId = ownDomainId;
    }

    public CrawlTypeEnum getCrawlType() {
        return crawlType;
    }

    public void setCrawlType(CrawlTypeEnum crawlType) {
        this.crawlType = crawlType;
    }

    public Integer getCrawlDate() {
        return crawlDate;
    }

    public void setCrawlDate(Integer crawlDate) {
        this.crawlDate = crawlDate;
    }

    public SendStatusEnum getSendStatus() {
        return sendStatus;
    }

    public void setSendStatus(SendStatusEnum sendStatus) {
        this.sendStatus = sendStatus;
    }

    public LocalDateTime getEndSendDate() {
        return endSendDate;
    }

    public void setEndSendDate(LocalDateTime endSendDate) {
        this.endSendDate = endSendDate;
    }

    public Integer getManagedUrlCount() {
        return managedUrlCount;
    }

    public void setManagedUrlCount(Integer managedUrlCount) {
        this.managedUrlCount = managedUrlCount;
    }

    public Integer getRankUrlCount() {
        return rankUrlCount;
    }

    public void setRankUrlCount(Integer rankUrlCount) {
        this.rankUrlCount = rankUrlCount;
    }

    public Integer getTotalSendCount() {
        return totalSendCount;
    }

    public void setTotalSendCount(Integer totalSendCount) {
        this.totalSendCount = totalSendCount;
    }

    public String getQueueName() {
        return queueName;
    }

    public void setQueueName(String queueName) {
        this.queueName = queueName;
    }

    public String getCrawlParam() {
        return crawlParam;
    }

    public void setCrawlParam(String crawlParam) {
        this.crawlParam = crawlParam;
    }

    public CrawlStatusEnum getCrawlStatus() {
        return crawlStatus;
    }

    public void setCrawlStatus(CrawlStatusEnum crawlStatus) {
        this.crawlStatus = crawlStatus;
    }

    public LocalDateTime getStartCrawlDate() {
        return startCrawlDate;
    }

    public void setStartCrawlDate(LocalDateTime startCrawlDate) {
        this.startCrawlDate = startCrawlDate;
    }

    public LocalDateTime getEndCrawlDate() {
        return endCrawlDate;
    }

    public void setEndCrawlDate(LocalDateTime endCrawlDate) {
        this.endCrawlDate = endCrawlDate;
    }

    public Integer getNoChangeCount() {
        return noChangeCount;
    }

    public void setNoChangeCount(Integer noChangeCount) {
        this.noChangeCount = noChangeCount;
    }

    public Integer getSaveToChCount() {
        return saveToChCount;
    }

    public void setSaveToChCount(Integer saveToChCount) {
        this.saveToChCount = saveToChCount;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public enum CrawlTypeEnum {
        TARGET_URL(1), COMPETITOR_URL(2);

        private final Integer value;

        CrawlTypeEnum(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }
    }

    public enum SendStatusEnum {
        SENDING(1), SEND_COMPLETED(2), SEND_ERROR(3), NO_MANAGED_URL(4);

        private final Integer value;

        SendStatusEnum(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }
    }

    public enum CrawlStatusEnum {
        NOT_STARTED(0), CRAWLING(1), CRAWL_COMPLETED(2);

        private final Integer value;

        CrawlStatusEnum(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }
    }

    @Override
    public String toString() {
        return "PoliteCrawlInstance{" +
                "id=" + id +
                ", ownDomainId=" + ownDomainId +
                ", crawlType=" + crawlType +
                ", crawlDate=" + crawlDate +
                ", sendStatus=" + sendStatus +
                ", endSendDate=" + endSendDate +
                ", managedUrlCount=" + managedUrlCount +
                ", rankUrlCount=" + rankUrlCount +
                ", totalSendCount=" + totalSendCount +
                ", crawlParam='" + crawlParam + '\'' +
                ", crawlStatus=" + crawlStatus +
                ", startCrawlDate=" + startCrawlDate +
                ", endCrawlDate=" + endCrawlDate +
                ", noChangeCount=" + noChangeCount +
                ", saveToChCount=" + saveToChCount +
                ", createDate=" + createDate +
                '}';
    }
}
