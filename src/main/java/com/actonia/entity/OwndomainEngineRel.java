package com.actonia.entity;

import java.util.Date;

import javax.persistence.Transient;

import org.apache.commons.lang.StringUtils;

public class OwndomainEngineRel {
	
	public static final String DEVICE_MOBILE = "m";

	private Long id;
	private Integer ownDomainId;
	private Boolean enabled;
	private Boolean primaryFlag;
	private String device;
	private Integer frequency;
	private Date updateDate;
	private Integer updateUser;
	
	private String countryQueryName;
	private String countryDisplayName;
	
	private String languageQueryName;
	private String languageDisplayName;
	private Integer languageId;
	
	private String engineQueryName;
	private String engineDisplayName;
	private Integer engineId;
	
	private String virtualEngineQueryName;
	private String virtualEngineDisplayName;
	private Integer virtualEngineId;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Integer getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public Boolean getEnabled() {
		return enabled;
	}

	public void setEnabled(Boolean enabled) {
		this.enabled = enabled;
	}

	public Boolean getPrimaryFlag() {
		if (primaryFlag == null) {
			return false;
		}
		return primaryFlag;
	}

	public void setPrimaryFlag(Boolean primaryFlag) {
		this.primaryFlag = primaryFlag;
	}

	public String getDevice() {
		return device;
	}

	public void setDevice(String device) {
		this.device = device;
	}

	public Integer getFrequency() {
		return frequency;
	}

	public void setFrequency(Integer frequency) {
		this.frequency = frequency;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public Integer getUpdateUser() {
		return updateUser;
	}

	public void setUpdateUser(Integer updateUser) {
		this.updateUser = updateUser;
	}

	public String getCountryQueryName() {
		return countryQueryName;
	}

	public void setCountryQueryName(String countryQueryName) {
		this.countryQueryName = countryQueryName;
	}

	public String getCountryDisplayName() {
		return countryDisplayName;
	}

	public void setCountryDisplayName(String countryDisplayName) {
		this.countryDisplayName = countryDisplayName;
	}

	public String getLanguageQueryName() {
		return languageQueryName;
	}

	public void setLanguageQueryName(String languageQueryName) {
		this.languageQueryName = languageQueryName;
	}

	public String getLanguageDisplayName() {
		return languageDisplayName;
	}

	public void setLanguageDisplayName(String languageDisplayName) {
		this.languageDisplayName = languageDisplayName;
	}

	public Integer getLanguageId() {
		return languageId;
	}

	public void setLanguageId(Integer languageId) {
		this.languageId = languageId;
	}

	public String getEngineQueryName() {
		return engineQueryName;
	}

	public void setEngineQueryName(String engineQueryName) {
		this.engineQueryName = engineQueryName;
	}

	public String getEngineDisplayName() {
		return engineDisplayName;
	}

	public void setEngineDisplayName(String engineDisplayName) {
		this.engineDisplayName = engineDisplayName;
	}

	public Integer getEngineId() {
		return engineId;
	}

	public void setEngineId(Integer engineId) {
		this.engineId = engineId;
	}

	public String getVirtualEngineQueryName() {
		return virtualEngineQueryName;
	}

	public void setVirtualEngineQueryName(String virtualEngineQueryName) {
		this.virtualEngineQueryName = virtualEngineQueryName;
	}

	public String getVirtualEngineDisplayName() {
		return virtualEngineDisplayName;
	}

	public void setVirtualEngineDisplayName(String virtualEngineDisplayName) {
		this.virtualEngineDisplayName = virtualEngineDisplayName;
	}

	public Integer getVirtualEngineId() {
		if (virtualEngineId != null && virtualEngineId > 0) {
			return virtualEngineId;
		}
		return getEngineId();
	}

	public void setVirtualEngineId(Integer virtualEngineId) {
		this.virtualEngineId = virtualEngineId;
	}
	
	@Transient
	public boolean isMobile() {
		return StringUtils.equals(device, DEVICE_MOBILE);
	}
	
	@Transient
	public boolean hasVirtualEngine() {
		return virtualEngineId != null &&
				StringUtils.isNotBlank(virtualEngineQueryName) && 
				StringUtils.isNotBlank(virtualEngineDisplayName);
	}

}
