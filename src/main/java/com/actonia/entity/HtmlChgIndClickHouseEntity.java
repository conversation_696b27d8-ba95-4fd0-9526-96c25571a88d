package com.actonia.entity;

import com.actonia.value.object.*;
import lombok.Data;

import javax.persistence.Transient;
import java.util.Date;

@Data
public class HtmlChgIndClickHouseEntity implements Cloneable {
	private Integer domainId;
	private String url;
	private Date trackDate;
	private String urlHash;
	private String urlMurmurHash;
	private Date currentCrawlTimestamp;
	private String changeIndicator;
	private Date previousCrawlTimestamp;
	private Date updateTimestamp;
	private Integer changeType; // 1 = added, 2 = modified, 3 = removed
	private Integer criticalInd;
	private String alternateLinksCurrentMd5;
	private String alternateLinksPreviousMd5;
	private AlternateLinks[] alternateLinksCurrent;
	private AlternateLinks[] alternateLinksPrevious;
	private String amphtmlHrefCurrent;
	private String amphtmlHrefPrevious;
	private String analyzedUrlSCurrent;
	private String analyzedUrlSPrevious;
	private String archiveFlgCurrent;
	private String archiveFlgPrevious;
	private String baseTagCurrent;
	private String baseTagPrevious;
	private String baseTagTargetCurrent;
	private String baseTagTargetPrevious;
	private String blockedByRobotsCurrent;
	private String blockedByRobotsPrevious;
	private String canonicalCurrent;
	private String canonicalPrevious;
	private Boolean canonicalHeaderFlagCurrent;
	private Boolean canonicalHeaderFlagPrevious;
	private String canonicalHeaderTypeCurrent;
	private String canonicalHeaderTypePrevious;
	private String canonicalTypeCurrent;
	private String canonicalTypePrevious;
	private String canonicalUrlIsConsistentCurrent;
	private String canonicalUrlIsConsistentPrevious;
	private String contentTypeCurrent;
	private String contentTypePrevious;
	private String customDataCurrentMd5;
	private String customDataPreviousMd5;
	private CustomData[] customDataCurrent;
	private CustomData[] customDataPrevious;
	private String descriptionCurrent;
	private String descriptionPrevious;
	private Integer descriptionLengthCurrent;
	private Integer descriptionLengthPrevious;
	private String errorMessageCurrent;
	private String errorMessagePrevious;
	private Integer finalResponseCodeCurrent;
	private Integer finalResponseCodePrevious;
	private String followFlgCurrent;
	private String followFlgPrevious;
	private String h1CurrentMd5;
	private String h1PreviousMd5;
	private String[] h1Current;
	private String[] h1Previous;
	private Integer h1CountCurrent;
	private Integer h1CountPrevious;
	private Integer h1LengthCurrent;
	private Integer h1LengthPrevious;
	private String h2CurrentMd5;
	private String h2PreviousMd5;
	private String[] h2Current;
	private String[] h2Previous;
	private Boolean headerNoarchiveCurrent;
	private Boolean headerNoarchivePrevious;
	private Boolean headerNofollowCurrent;
	private Boolean headerNofollowPrevious;
	private Boolean headerNoindexCurrent;
	private Boolean headerNoindexPrevious;
	private Boolean headerNoodpCurrent;
	private Boolean headerNoodpPrevious;
	private Boolean headerNosnippetCurrent;
	private Boolean headerNosnippetPrevious;
	private Boolean headerNoydirCurrent;
	private Boolean headerNoydirPrevious;
	private HreflangErrors hreflangErrorsCurrent;
	private HreflangErrors hreflangErrorsPrevious;
	private String hreflangLinksCurrentMd5;
	private String hreflangLinksPreviousMd5;
	private HreflangLinks[] hreflangLinksCurrent;
	private HreflangLinks[] hreflangLinksPrevious;
	private Integer hreflangLinksOutCountCurrent;
	private Integer hreflangLinksOutCountPrevious;
	private Integer hreflangUrlCountCurrent;
	private Integer hreflangUrlCountPrevious;
	private String indexFlgCurrent;
	private String indexFlgPrevious;
	private Boolean indexableCurrent;
	private Boolean indexablePrevious;
	private String insecureResourcesCurrentMd5;
	private String insecureResourcesPreviousMd5;
	private String[] insecureResourcesCurrent;
	private String[] insecureResourcesPrevious;
	private String metaCharsetCurrent;
	private String metaCharsetPrevious;
	private String metaContentTypeCurrent;
	private String metaContentTypePrevious;
	private Boolean metaDisabledSitelinksCurrent;
	private Boolean metaDisabledSitelinksPrevious;
	private Boolean metaNoodpCurrent;
	private Boolean metaNoodpPrevious;
	private Boolean metaNosnippetCurrent;
	private Boolean metaNosnippetPrevious;
	private Boolean metaNoydirCurrent;
	private Boolean metaNoydirPrevious;
	private Boolean metaRedirectCurrent;
	private Boolean metaRedirectPrevious;
	private Boolean mixedRedirectsCurrent;
	private Boolean mixedRedirectsPrevious;
	private Boolean mobileRelAlternateUrlIsConsistentCurrent;
	private Boolean mobileRelAlternateUrlIsConsistentPrevious;
	private Boolean noodpCurrent;
	private Boolean noodpPrevious;
	private Boolean nosnippetCurrent;
	private Boolean nosnippetPrevious;
	private Boolean noydirCurrent;
	private Boolean noydirPrevious;
	private String ogMarkupCurrentMd5;
	private String ogMarkupPreviousMd5;
	private OgMarkup[] ogMarkupCurrent;
	private OgMarkup[] ogMarkupPrevious;
	private Integer ogMarkupLengthCurrent;
	private Integer ogMarkupLengthPrevious;
	private Integer outlinkCountCurrent;
	private Integer outlinkCountPrevious;
	private String pageAnalysisResultsChgIndJson;
	private String pageLinkCurrent;
	private String pageLinkPrevious;
	private Boolean redirectBlockedCurrent;
	private Boolean redirectBlockedPrevious;
	private String redirectBlockedReasonCurrent;
	private String redirectBlockedReasonPrevious;
	private String redirectChainCurrentMd5;
	private String redirectChainPreviousMd5;
	private RedirectChain[] redirectChainCurrent;
	private RedirectChain[] redirectChainPrevious;
	private String redirectFinalUrlCurrent;
	private String redirectFinalUrlPrevious;
	private Integer redirectTimesCurrent;
	private Integer redirectTimesPrevious;
	private Integer responseCodeCurrent;
	private Integer responseCodePrevious;
	private ResponseHeaders[] responseHeadersCurrent;
	private ResponseHeaders[] responseHeadersPrevious;
	private String robotsContentsCurrent;
	private String robotsContentsPrevious;
	private String structuredDataCurrentMd5;
	private String structuredDataPreviousMd5;
	private StructuredData structuredDataCurrent;
	private StructuredData structuredDataPrevious;
	private String titleCurrent;
	private String titlePrevious;
	private Integer titleLengthCurrent;
	private Integer titleLengthPrevious;
	private String viewportContentCurrent;
	private String viewportContentPrevious;
	private Integer sign;
	private String robotTxtCurrent;
	private String robotTxtPrevious;
	private Date createTime;

	@Transient
	private Integer total;

	@Transient
	private Integer totalChanges;

	@Transient
	private Integer totalSeverityCritical;

	@Transient
	private Integer totalSeverityHigh;

	@Transient
	private Integer totalSeverityMedium;

	@Transient
	private Integer totalSeverityLow;

	@Transient
	private Integer totalChangeTypeAdded;

	@Transient
	private Integer totalChangeTypeModified;

	@Transient
	private Integer totalChangeTypeRemoved;

}
