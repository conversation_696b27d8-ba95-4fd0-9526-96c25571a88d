/**
 * 
 */
package com.actonia.entity;

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.*;

import org.apache.commons.lang.StringUtils;

/**
 * #715
 * com.actonia.saas.model.DomainSearchEngineRelEntity.java
 *
 * <AUTHOR>
 *
 * @version $Revision: 218417 $
 *          $Author: wangcee $
 */
@Entity
@Table(name = "domain_search_engine_rel")
public class DomainSearchEngineRelEntity {

	//Cee - https://www.wrike.com/open.htm?id=696909015
	public static final int PAUSE_FLG_NOT = 0; //normal engine
	public static final int PAUSE_FLG_YES = 1; //ignore engine - has Paused
	public static final int PAUSE_FLG_RESUME = 2; //normal engine - re-enabled Engine from Pause
	public static final int PAUSE_STATUS_SKIP = 2; //means QueueBase has finished processing

	public static final String DEVICE_DESKTOP = "d";
	public static final String DEVICE_MOBILE = "m";
	public static final String DEVICE_MOBILEIMAGE = "mimg";
	
    //Cee - https://www.wrike.com/open.htm?id=186771083
    //default value is 0, means to use ownDomain's language ID
    public static final int SEARCH_ENGINE_LANGUAGEID_DEFAULT = 0;
    
	private Integer id;
	
	private Integer ownDomainId;
	
	private String searchEngine;
	
	private Integer searchEngineId;
	
	//Cee - https://www.wrike.com/open.htm?id=184068835
	private Integer searchEngineLanguageid;
	
	//Cee - https://www.wrike.com/open.htm?id=378283327
	private String device;

	//Cee - https://www.wrike.com/open.htm?id=378283327
	private String deviceForUI;
	
	//Cee - https://www.wrike.com/open.htm?id=423184188
	private String searchEngineDisplayName;
	
	//Cee - https://www.wrike.com/open.htm?id=522558033
	private Boolean enableVisibiltyShareV2;

	//Cee - https://www.wrike.com/open.htm?id=696909015
	private Integer pauseFlg;
	private Integer pauseStatus;
	
	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "own_domain_id")
	public Integer getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	@Column(name = "search_engine")
	public String getSearchEngine() {
		return searchEngine;
	}

	public void setSearchEngine(String searchEngine) {
		this.searchEngine = searchEngine;
	}

	@Column(name = "search_engine_id")
	public Integer getSearchEngineId() {
		return searchEngineId;
	}

	public void setSearchEngineId(Integer searchEngineId) {
		this.searchEngineId = searchEngineId;
	}

	@Column(name = "search_engine_languageid")
    public Integer getSearchEngineLanguageid() {
        return searchEngineLanguageid;
    }

    public void setSearchEngineLanguageid(Integer searchEngineLanguageid) {
        this.searchEngineLanguageid = searchEngineLanguageid;
    }

    @Column(name = "device")
	public String getDevice() {
    	if (StringUtils.isBlank(device)) {
    		return "d";
    	}
		return device;
	}

	public void setDevice(String device) {
		this.device = device;
	}

	@Column(name = "deviceForUI")
	public String getDeviceForUI() {
		return deviceForUI;
	}

	public void setDeviceForUI(String deviceForUI) {
		this.deviceForUI = deviceForUI;
	}

	@Column(name = "searchEngineDisplayName")
	public String getSearchEngineDisplayName() {
		return searchEngineDisplayName;
	}

	public void setSearchEngineDisplayName(String searchEngineDisplayName) {
		this.searchEngineDisplayName = searchEngineDisplayName;
	}

	@Column(name = "enableVisibiltyShareV2")
	public Boolean getEnableVisibiltyShareV2() {
		return enableVisibiltyShareV2;
	}

	public void setEnableVisibiltyShareV2(Boolean enableVisibiltyShareV2) {
		this.enableVisibiltyShareV2 = enableVisibiltyShareV2;
	}

	@Column(name = "pause_flg")
	public Integer getPauseFlg() {
		return pauseFlg;
	}

	public void setPauseFlg(Integer pauseFlg) {
		this.pauseFlg = pauseFlg;
	}

	@Column(name = "pause_status")
	public Integer getPauseStatus() {
		return pauseStatus;
	}

	public void setPauseStatus(Integer pauseStatus) {
		this.pauseStatus = pauseStatus;
	}

	//Cee - https://www.wrike.com/open.htm?id=698658417
	@Transient
	public boolean isPausedRanking() {
		if (pauseFlg == null || pauseFlg == PAUSE_FLG_NOT) {
			return false;
		}
		if (pauseFlg == PAUSE_FLG_RESUME &&
				pauseStatus == PAUSE_STATUS_SKIP) {
			return false;
		}
		return true;
	}

}
