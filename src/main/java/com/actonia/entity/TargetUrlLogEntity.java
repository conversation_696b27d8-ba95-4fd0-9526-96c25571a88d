package com.actonia.entity;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class TargetUrlLogEntity {

	public static final int OPERATION_INSERT = 1;
	public static final int OPERATION_UPDATE = 2;
	public static final int OPERATION_DELETE = 3;

	/**
	 * Unique identifier for the target URL log.
	 */
	private Long id;

	/**
	 * The ID of the target URL (references t_target_url.id).
	 */
	private Long targetUrlId;

	/**
	 * The ID of the domain that owns this target URL log.
	 */
	private Integer ownDomainId;

	/**
	 * The actual URL being logged.
	 */
	private String url;

	/**
	 * The Murmur3 hash of the URL.
	 */
	private String urlMurmur3Hash;

	/**
	 * The custom Murmur3 hash of the URL.
	 */
	private String customUrlMurmur3Hash;

	/**
	 * The hash of the URL.
	 */
	private String urlHash;

	/**
	 * The hash of the URI.
	 */
	private String uriHash;

	/**
	 * The Murmur3 hash of the URI.
	 */
	private String uriMurmur3Hash;

	/**
	 * The source type of the log entry.
	 */
	private int sourceType;

	/**
	 * The type of the log entry.
	 */
	private Integer type;

	/**
	 * The status of the log entry.
	 */
	private Integer status;

	/**
	 * Indicates if the log entry is for initial crawl only.
	 */
	private int initialCrawlOnly;

	/**
	 * Indicates if crawling is disabled for this log entry.
	 */
	private int disableCrawl;

	/**
	 * Indicates if the log entry is disabled by a specific reason.
	 */
	private int disableBy;

	/**
	 * The date and time when this log entry was created.
	 */
	private LocalDateTime createDate;

	/**
	 * A friendly name for the target URL.
	 */
	private String friendlyName;

	/**
	 * The ID of the section name associated with this log entry.
	 */
	private Integer sectionNameId;

	/**
	 * The number of entrances in the last week.
	 */
	private Integer weekEntrances;

	/**
	 * The number of bounces in the last week.
	 */
	private Integer weekBounces;

	/**
	 * The ID of the canonical URL associated with this log entry.
	 */
	private Long canonicalUrlId;

	/**
	 * The operation type (1: insert, 2: update, 3: delete).
	 */
	private int operationType;

	/**
	 * The log date in the format yyyyMMdd.
	 */
	private int logDate;

	/**
	 * The timestamp when this log entry was created.
	 */
	private LocalDateTime createdAt;

	private int maxLogDate;
}