/**
 * 
 */
package com.actonia.entity;

import static javax.persistence.GenerationType.IDENTITY;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

/**
 * com.actonia.subserver.entity.CustomizeFilterEntity.java
 *
 * <AUTHOR>
 *
 * @version $Revision: 90733 $
 *          $Author: <PERSON>@SHINETECHCHINA $
 */
public class CustomizeFilterEntity {
	
	public static final int TEMPORARY_YES = 1;
	public static final int TEMPORARY_NO = 0;
	
	public static final int SEND_ALERT_NO = 0;
	public static final int SEND_ALERT_SELECTED_USER = 1;
	public static final int SEND_ALERT_ALL_USER = 2;
	
	public static final int GROUPTAG_LEVEL_FILTER = 1;
	public static final int GLOBAL_LEVEL_FILTER = 0;
	
	public static final int EMAIL_FREQUENCY_DAILY = 1;
	public static final int EMAIL_FREQUENCY_WEEKLY = 2;
	public static final int EMAIL_FREQUENCY_MONTHLY = 3;
	
	private Integer id;
	
	private String name;
	
	private Integer ownDomainId;
	
	private Integer userId;
	
	private Integer elementType;
	
	private Date updateTime;
	
	private Integer temporary;
	
	private Integer sendAlert;
	
	//https://www.wrike.com/open.htm?id=8820075
	private Integer grouptagLevel;
	
	//https://www.wrike.com/open.htm?id=19885216
	//by cee
	//1: daily, 7: weekly, 30: monthly
	private int emailFrequency;
	
	private Integer emailFrequencyDay; 

	@Transient
	private int filterConditionId;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "name")
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@Column(name = "own_domain_id")
	public Integer getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	@Column(name = "user_id")
	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	@Column(name = "element_type")
	public Integer getElementType() {
		return elementType;
	}

	public void setElementType(Integer elementType) {
		this.elementType = elementType;
	}

	@Temporal(TemporalType.DATE)
	@Column(name ="update_time", length = 10)
	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	/**
	 * 0: no,
	 * 1: yes, it's temp
	 * @return
	 */
	@Column(name = "temporary")
	public Integer getTemporary() {
		return temporary;
	}

	public void setTemporary(Integer temporary) {
		this.temporary = temporary;
	}

	@Column(name = "send_alert")
	public Integer getSendAlert() {
		return sendAlert;
	}

	public void setSendAlert(Integer sendAlert) {
		this.sendAlert = sendAlert;
	}

	@Column(name = "grouptag_level")
	public Integer getGrouptagLevel() {
		return grouptagLevel;
	}

	public void setGrouptagLevel(Integer grouptagLevel) {
		this.grouptagLevel = grouptagLevel;
	}

	@Transient
	public boolean isGroupTagLevelFilter() {
		if (grouptagLevel != null && grouptagLevel.intValue() == GROUPTAG_LEVEL_FILTER) {
			return true;
		}
		return false;
	}

	@Transient
	public int getFilterConditionId() {
		return filterConditionId;
	}

	@Transient
	public void setFilterConditionId(int filterConditionId) {
		this.filterConditionId = filterConditionId;
	}

	@Column(name = "email_frequency")
	public int getEmailFrequency() {
		return emailFrequency;
	}

	public void setEmailFrequency(int emailFrequency) {
		this.emailFrequency = emailFrequency;
	}

	@Column(name = "email_frequency_day")
	public Integer getEmailFrequencyDay() {
		return emailFrequencyDay;
	}

	public void setEmailFrequencyDay(Integer emailFrequencyDay) {
		this.emailFrequencyDay = emailFrequencyDay;
	}

	@Override
	public String toString() {
		return " CustomizeFilterEntity [id=" + id + ", name=" + name + ", ownDomainId=" + ownDomainId + ", userId=" + userId + ", elementType="
				+ elementType + ", updateTime=" + updateTime + ", temporary=" + temporary + ", sendAlert=" + sendAlert + ", grouptagLevel="
				+ grouptagLevel + ", emailFrequency=" + emailFrequency + ", emailFrequencyDay=" + emailFrequencyDay + ", filterConditionId="
				+ filterConditionId + "]";
	}
	
}
