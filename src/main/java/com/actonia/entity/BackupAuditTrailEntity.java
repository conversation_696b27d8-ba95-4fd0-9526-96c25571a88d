package com.actonia.entity;

public class BackupAuditTrailEntity {
	private String tableName;
	private String bucketName; // S3 bucket name
	private String partialPrefix; // S3 prefix (partial)
	private String backupDate;
	private String startDate;
	private String endDate;

	public String getTableName() {
		return tableName;
	}

	public void setTableName(String tableName) {
		this.tableName = tableName;
	}

	public String getBucketName() {
		return bucketName;
	}

	public void setBucketName(String bucketName) {
		this.bucketName = bucketName;
	}

	public String getPartialPrefix() {
		return partialPrefix;
	}

	public void setPartialPrefix(String partialPrefix) {
		this.partialPrefix = partialPrefix;
	}

	public String getBackupDate() {
		return backupDate;
	}

	public void setBackupDate(String backupDate) {
		this.backupDate = backupDate;
	}

	public String getStartDate() {
		return startDate;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	@Override
	public String toString() {
		return "BackupAuditTrailEntity [tableName=" + tableName + ", bucketName=" + bucketName + ", partialPrefix=" + partialPrefix + ", backupDate="
				+ backupDate + ", startDate=" + startDate + ", endDate=" + endDate + "]";
	}

}