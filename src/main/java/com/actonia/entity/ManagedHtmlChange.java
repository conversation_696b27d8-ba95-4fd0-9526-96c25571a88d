package com.actonia.entity;

import lombok.Data;

import java.util.Date;

@Data
public class ManagedHtmlChange {
    private Date trackDate;
    private Integer domainId;
    private String url;
    private String urlHash;
    private String urlMurmurHash;
    private Integer chgId;
    private String prevValue;
    private String currValue;
    private Date prevCrawlTimestamp;
    private Date currCrawlTimestamp;
    private Date createTimestamp;
    private Integer urlMurmurHashMod;
    private Date createTime;
    private Integer prevResponseCode;
    private Integer currResponseCode;

    public static ManagedHtmlChange createFromHtmlChange(HtmlChange htmlChange) {
        ManagedHtmlChange managedHtmlChange = new ManagedHtmlChange();
        managedHtmlChange.setTrackDate(htmlChange.getTrackDate());
        managedHtmlChange.setDomainId(htmlChange.getDomainId());
        managedHtmlChange.setUrl(htmlChange.getUrl());
        managedHtmlChange.setChgId(htmlChange.getChgId());
        managedHtmlChange.setPrevCrawlTimestamp(htmlChange.getPrevCrawlTimestamp());
        managedHtmlChange.setCurrCrawlTimestamp(htmlChange.getCurrCrawlTimestamp());
        managedHtmlChange.setCreateTimestamp(htmlChange.getCreateTimestamp());
        managedHtmlChange.setCurrResponseCode(htmlChange.getCurrResponseCode());
        managedHtmlChange.setPrevResponseCode(htmlChange.getPrevResponseCode());
        return managedHtmlChange;
    }

    public static ManagedHtmlChange createFromChangeInd(TargetUrlChangeIndClickHouseEntity changeInd, Integer chgId) {
        ManagedHtmlChange managedHtmlChange = new ManagedHtmlChange();
        managedHtmlChange.setTrackDate(changeInd.getTrackDate());
        managedHtmlChange.setDomainId(changeInd.getDomainId());
        managedHtmlChange.setUrl(changeInd.getUrl());
        managedHtmlChange.setUrlHash(changeInd.getUrlHash());
        managedHtmlChange.setUrlMurmurHash(changeInd.getUrlMurmurHash());
        managedHtmlChange.setChgId(chgId);
        managedHtmlChange.setPrevCrawlTimestamp(changeInd.getPreviousCrawlTimestamp());
        managedHtmlChange.setCurrCrawlTimestamp(changeInd.getCurrentCrawlTimestamp());
        managedHtmlChange.setCreateTimestamp(changeInd.getUpdateTimestamp());
        return managedHtmlChange;
    }

}
