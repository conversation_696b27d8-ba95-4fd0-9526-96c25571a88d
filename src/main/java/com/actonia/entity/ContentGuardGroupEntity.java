package com.actonia.entity;

import javax.persistence.Transient;

public class ContentGuardGroupEntity {
	private Long id;
	private int domainId;
	private String groupName;
	private int crawlFrequencyType;
	
	@Transient
	private String url;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public int getDomainId() {
		return domainId;
	}

	public void setDomainId(int domainId) {
		this.domainId = domainId;
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	public int getCrawlFrequencyType() {
		return crawlFrequencyType;
	}

	public void setCrawlFrequencyType(int crawlFrequencyType) {
		this.crawlFrequencyType = crawlFrequencyType;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	@Override
	public String toString() {
		return "ContentGuardGroupEntity [id=" + id + ", domainId=" + domainId + ", groupName=" + groupName + ", crawlFrequencyType=" + crawlFrequencyType + ", url="
				+ url + "]";
	}

}