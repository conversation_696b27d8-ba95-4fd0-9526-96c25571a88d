/**
 * 
 */
package com.actonia.entity;

import java.util.Map;

import javax.persistence.Transient;

import org.apache.commons.lang.StringUtils;

import com.actonia.utils.JsonMapper;

/**
 * com.actonia.subserver.entity.AgencyInfoEntity.java
 * 
 * https://www.wrike.com/open.htm?id=59034165
 *
 * <AUTHOR>
 *
 * @version $Revision:$
 *          $Author:$
 */
public class AgencyInfoEntity {
public static final String DEFAULT_COMPANY_NAME = "default";
	
	public static AgencyInfoEntity DEFAULT_ENTITY = null;
	static {
		DEFAULT_ENTITY = new AgencyInfoEntity();
	}
	
	private Long id;
	
	private String companyName;
	
	private String companyTitle;
	
	private String websiteDomain;
	
	private String logoImage;
	
	private String emailFrom;
	
	private String emailReplyto;
	
	private String emailNickname;
	
	private String emailHeaderJson;
	
	private String emailFooterJson;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public String getCompanyTitle() {
		return companyTitle;
	}

	public void setCompanyTitle(String companyTitle) {
		this.companyTitle = companyTitle;
	}

	public String getWebsiteDomain() {
		return websiteDomain;
	}

	public void setWebsiteDomain(String websiteDomain) {
		this.websiteDomain = websiteDomain;
	}

	public String getLogoImage() {
		return logoImage;
	}

	public void setLogoImage(String logoImage) {
		this.logoImage = logoImage;
	}

	public String getEmailFrom() {
		return emailFrom;
	}

	public void setEmailFrom(String emailFrom) {
		this.emailFrom = emailFrom;
	}

	public String getEmailReplyto() {
		return emailReplyto;
	}

	public void setEmailReplyto(String emailReplyto) {
		this.emailReplyto = emailReplyto;
	}

	public String getEmailNickname() {
		return emailNickname;
	}

	public void setEmailNickname(String emailNickname) {
		this.emailNickname = emailNickname;
	}

	public String getEmailHeaderJson() {
		return emailHeaderJson;
	}

	public void setEmailHeaderJson(String emailHeaderJson) {
		this.emailHeaderJson = emailHeaderJson;
	}

	public String getEmailFooterJson() {
		return emailFooterJson;
	}

	public void setEmailFooterJson(String emailFooterJson) {
		this.emailFooterJson = emailFooterJson;
	}
	
	@Transient
	public Map getEmailFooter() {
		if (StringUtils.isBlank(emailFooterJson)) {
			return null;
		}
		JsonMapper mapper = new JsonMapper();
		return mapper.fromJson(emailFooterJson, Map.class);
	}
	
	@Transient
	public boolean isDefaultAgency() {
		return StringUtils.equals(companyName, DEFAULT_COMPANY_NAME);
	}
	
}
