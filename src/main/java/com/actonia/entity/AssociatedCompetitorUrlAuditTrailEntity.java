package com.actonia.entity;

import java.util.Date;

public class AssociatedCompetitorUrlAuditTrailEntity {
	private int batchRunDate;
	private Date batchRunUpdateTimestamp;

	public int getBatchRunDate() {
		return batchRunDate;
	}

	public void setBatchRunDate(int batchRunDate) {
		this.batchRunDate = batchRunDate;
	}

	public Date getBatchRunUpdateTimestamp() {
		return batchRunUpdateTimestamp;
	}

	public void setBatchRunUpdateTimestamp(Date batchRunUpdateTimestamp) {
		this.batchRunUpdateTimestamp = batchRunUpdateTimestamp;
	}

	@Override
	public String toString() {
		return " AssociatedCompetitorUrlAuditTrailEntity [batchRunDate=" + batchRunDate + ", batchRunUpdateTimestamp=" + batchRunUpdateTimestamp
				+ "]";
	}

}