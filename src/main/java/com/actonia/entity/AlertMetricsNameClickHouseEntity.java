package com.actonia.entity;

public class AlertMetricsNameClickHouseEntity {
	private Integer domain_id;
	private Integer url_type;
	private String metrics_name;

	public Integer getDomain_id() {
		return domain_id;
	}

	public void setDomain_id(Integer domain_id) {
		this.domain_id = domain_id;
	}

	public Integer getUrl_type() {
		return url_type;
	}

	public void setUrl_type(Integer url_type) {
		this.url_type = url_type;
	}

	public String getMetrics_name() {
		return metrics_name;
	}

	public void setMetrics_name(String metrics_name) {
		this.metrics_name = metrics_name;
	}

	@Override
	public String toString() {
		return "AlertMetricsNameClickHouseEntity [domain_id=" + domain_id + ", url_type=" + url_type + ", metrics_name=" + metrics_name + "]";
	}

}
