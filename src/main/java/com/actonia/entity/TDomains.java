package com.actonia.entity;

public class TDomains {
	private Integer ownDomainId;
	private Integer tokensAllowed;
	private Integer totalUsed;

	public Integer getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public Integer getTokensAllowed() {
		if (tokensAllowed == null) {
			return 0;
		}
		return tokensAllowed;
	}

	public void setTokensAllowed(Integer tokensAllowed) {
		this.tokensAllowed = tokensAllowed;
	}

	public Integer getTotalUsed() {
		return totalUsed;
	}

	public void setTotalUsed(Integer totalUsed) {
		this.totalUsed = totalUsed;
	}

}
