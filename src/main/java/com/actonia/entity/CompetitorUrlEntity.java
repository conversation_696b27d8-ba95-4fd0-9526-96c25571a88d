/**
 * 
 */
package com.actonia.entity;

import java.util.Date;
import java.util.List;


/**
 * com.actonia.subserver.entity.CompetitorUrlEntity.java
 * 
 * @version $Revision: 86337 $ $Author: <PERSON>@SHINETECHCHINA $
 */
public class CompetitorUrlEntity {
	
	public static final int ADD_BY_USER = 1;
	public static final int ADD_BY_IMPORT_URL = 10;
	public static final int ADD_BY_RANKING = 40;
	public static final int ADD_BY_AUTOMATIC_ASSOCIATIONS = 20;
	public static final int ADD_BY_SYSTEM = 30;
	public static final int ADD_BY_DAILY_IEE = 99;

	private Integer id;

	private Integer competitorId;

	private String url;

	private Date createDate;

	private int ownDomainId;

	private Integer addBy;
	
	private Integer urlType;
	private Integer urlId;
	private Integer oid;
	private Integer responseCode;
	private Integer needUpdateUrl;
	private String queryDate;
	private Integer status;
	private String urlHash;
	private Short type;

	private List<CommonCrawlUrlHtmlSourceEntity> srcs;
	
	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getUrlType() {
		return urlType;
	}

	public void setUrlType(Integer urlType) {
		this.urlType = urlType;
	}

	public Integer getUrlId() {
		return urlId;
	}

	public void setUrlId(Integer urlId) {
		this.urlId = urlId;
	}

	public Integer getOid() {
		return oid;
	}

	public void setOid(Integer oid) {
		this.oid = oid;
	}

	public Integer getResponseCode() {
		return responseCode;
	}

	public void setResponseCode(Integer responseCode) {
		this.responseCode = responseCode;
	}

	public Integer getNeedUpdateUrl() {
		return needUpdateUrl;
	}

	public void setNeedUpdateUrl(Integer needUpdateUrl) {
		this.needUpdateUrl = needUpdateUrl;
	}

	public String getQueryDate() {
		return queryDate;
	}

	public void setQueryDate(String queryDate) {
		this.queryDate = queryDate;
	}

	public List<CommonCrawlUrlHtmlSourceEntity> getSrcs() {
		return srcs;
	}

	public void setSrcs(List<CommonCrawlUrlHtmlSourceEntity> srcs) {
		this.srcs = srcs;
	}

	public Integer getAddBy() {
		return addBy;
	}

	public void setAddBy(Integer addBy) {
		this.addBy = addBy;
	}

	public int getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(int ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getCompetitorId() {
		return competitorId;
	}

	public void setCompetitorId(Integer competitorId) {
		this.competitorId = competitorId;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public String getUrlHash() {
		return urlHash;
	}

	public void setUrlHash(String urlHash) {
		this.urlHash = urlHash;
	}

	public Short getType() {
		return type;
	}

	public void setType(Short type) {
		this.type = type;
	}

	@Override
	public String toString() {
		return " CompetitorUrlEntity [id=" + id + ",competitorId=" + competitorId + ", url=" + url + ", createDate=" + createDate + ", addBy="
				+ addBy + ", ownDomainId=" + ownDomainId + ", type=" + type + "]";
	}
	
}
