package com.actonia.entity;

import java.util.Arrays;
import java.util.Date;

import javax.persistence.Transient;

import com.actonia.value.object.AlternateLinks;
import com.actonia.value.object.CustomData;
import com.actonia.value.object.HreflangErrors;
import com.actonia.value.object.HreflangLinks;
import com.actonia.value.object.OgMarkup;
import com.actonia.value.object.PageLink;
import com.actonia.value.object.RedirectChain;
import com.actonia.value.object.ResponseHeaders;
import com.actonia.value.object.StructuredData;

public class TargetUrlChangeIndClickHouseEntity implements Cloneable {
	private Integer domainId;
	private String url;
	private Date trackDate;
	private String urlHash;
	private String urlMurmurHash;
	private Date currentCrawlTimestamp;
	private String changeIndicator;
	private Date previousCrawlTimestamp;
	private Date updateTimestamp;
	private Integer changeType; // 1 = added, 2 = modified, 3 = removed
	private Integer criticalInd;
	private AlternateLinks[] alternateLinksCurrent;
	private AlternateLinks[] alternateLinksPrevious;
	private String amphtmlHrefCurrent;
	private String amphtmlHrefPrevious;
	private String analyzedUrlSCurrent;
	private String analyzedUrlSPrevious;
	private String archiveFlgCurrent;
	private String archiveFlgPrevious;
	private String baseTagCurrent;
	private String baseTagPrevious;
	private String baseTagTargetCurrent;
	private String baseTagTargetPrevious;
	private String blockedByRobotsCurrent;
	private String blockedByRobotsPrevious;
	private String canonicalCurrent;
	private String canonicalPrevious;
	private Boolean canonicalHeaderFlagCurrent;
	private Boolean canonicalHeaderFlagPrevious;
	private String canonicalHeaderTypeCurrent;
	private String canonicalHeaderTypePrevious;
	private String canonicalTypeCurrent;
	private String canonicalTypePrevious;
	private String canonicalUrlIsConsistentCurrent;
	private String canonicalUrlIsConsistentPrevious;
	private String contentTypeCurrent;
	private String contentTypePrevious;
	private CustomData[] customDataCurrent;
	private CustomData[] customDataPrevious;
	private String descriptionCurrent;
	private String descriptionPrevious;
	private Integer descriptionLengthCurrent;
	private Integer descriptionLengthPrevious;
	private String errorMessageCurrent;
	private String errorMessagePrevious;
	private Integer finalResponseCodeCurrent;
	private Integer finalResponseCodePrevious;
	private String followFlgCurrent;
	private String followFlgPrevious;
	private String[] h1Current;
	private String[] h1Previous;
	private Integer h1CountCurrent;
	private Integer h1CountPrevious;
	private Integer h1LengthCurrent;
	private Integer h1LengthPrevious;
	private String[] h2Current;
	private String[] h2Previous;
	private Boolean headerNoarchiveCurrent;
	private Boolean headerNoarchivePrevious;
	private Boolean headerNofollowCurrent;
	private Boolean headerNofollowPrevious;
	private Boolean headerNoindexCurrent;
	private Boolean headerNoindexPrevious;
	private Boolean headerNoodpCurrent;
	private Boolean headerNoodpPrevious;
	private Boolean headerNosnippetCurrent;
	private Boolean headerNosnippetPrevious;
	private Boolean headerNoydirCurrent;
	private Boolean headerNoydirPrevious;
	private HreflangErrors hreflangErrorsCurrent;
	private HreflangErrors hreflangErrorsPrevious;
	private HreflangLinks[] hreflangLinksCurrent;
	private HreflangLinks[] hreflangLinksPrevious;
	private Integer hreflangLinksOutCountCurrent;
	private Integer hreflangLinksOutCountPrevious;
	private Integer hreflangUrlCountCurrent;
	private Integer hreflangUrlCountPrevious;
	private String indexFlgCurrent;
	private String indexFlgPrevious;
	private Boolean indexableCurrent;
	private Boolean indexablePrevious;
	private String[] insecureResourcesCurrent;
	private String[] insecureResourcesPrevious;
	private String metaCharsetCurrent;
	private String metaCharsetPrevious;
	private String metaContentTypeCurrent;
	private String metaContentTypePrevious;
	private Boolean metaDisabledSitelinksCurrent;
	private Boolean metaDisabledSitelinksPrevious;
	private Boolean metaNoodpCurrent;
	private Boolean metaNoodpPrevious;
	private Boolean metaNosnippetCurrent;
	private Boolean metaNosnippetPrevious;
	private Boolean metaNoydirCurrent;
	private Boolean metaNoydirPrevious;
	private Boolean metaRedirectCurrent;
	private Boolean metaRedirectPrevious;
	private Boolean mixedRedirectsCurrent;
	private Boolean mixedRedirectsPrevious;
	private Boolean mobileRelAlternateUrlIsConsistentCurrent;
	private Boolean mobileRelAlternateUrlIsConsistentPrevious;
	private Boolean noodpCurrent;
	private Boolean noodpPrevious;
	private Boolean nosnippetCurrent;
	private Boolean nosnippetPrevious;
	private Boolean noydirCurrent;
	private Boolean noydirPrevious;
	private OgMarkup[] ogMarkupCurrent;
	private OgMarkup[] ogMarkupPrevious;
	private Integer ogMarkupLengthCurrent;
	private Integer ogMarkupLengthPrevious;
	private Integer outlinkCountCurrent;
	private Integer outlinkCountPrevious;
	private String pageAnalysisResultsChgIndJson;
	private PageLink[] pageLinkCurrent;
	private PageLink[] pageLinkPrevious;
	private Boolean redirectBlockedCurrent;
	private Boolean redirectBlockedPrevious;
	private String redirectBlockedReasonCurrent;
	private String redirectBlockedReasonPrevious;
	private RedirectChain[] redirectChainCurrent;
	private RedirectChain[] redirectChainPrevious;
	private String redirectFinalUrlCurrent;
	private String redirectFinalUrlPrevious;
	private Integer redirectTimesCurrent;
	private Integer redirectTimesPrevious;
	private String responseCodeCurrent;
	private String responseCodePrevious;
	private ResponseHeaders[] responseHeadersCurrent;
	private ResponseHeaders[] responseHeadersPrevious;
	private String robotsContentsCurrent;
	private String robotsContentsPrevious;
	private StructuredData structuredDataCurrent;
	private StructuredData structuredDataPrevious;
	private String titleCurrent;
	private String titlePrevious;
	private Integer titleLengthCurrent;
	private Integer titleLengthPrevious;
	private String viewportContentCurrent;
	private String viewportContentPrevious;
	private Integer sign;
	private String robotTxtCurrent;
	private String robotTxtPrevious;

	@Transient
	private Integer total;

	@Transient
	private Integer totalChanges;

	@Transient
	private Integer totalSeverityCritical;

	@Transient
	private Integer totalSeverityHigh;

	@Transient
	private Integer totalSeverityMedium;

	@Transient
	private Integer totalSeverityLow;

	@Transient
	private Integer totalChangeTypeAdded;

	@Transient
	private Integer totalChangeTypeModified;

	@Transient
	private Integer totalChangeTypeRemoved;

	public Integer getDomainId() {
		return domainId;
	}

	public void setDomainId(Integer domainId) {
		this.domainId = domainId;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public Date getTrackDate() {
		return trackDate;
	}

	public void setTrackDate(Date trackDate) {
		this.trackDate = trackDate;
	}

	public String getUrlHash() {
		return urlHash;
	}

	public void setUrlHash(String urlHash) {
		this.urlHash = urlHash;
	}

	public String getUrlMurmurHash() {
		return urlMurmurHash;
	}

	public void setUrlMurmurHash(String urlMurmurHash) {
		this.urlMurmurHash = urlMurmurHash;
	}

	public Date getCurrentCrawlTimestamp() {
		return currentCrawlTimestamp;
	}

	public void setCurrentCrawlTimestamp(Date currentCrawlTimestamp) {
		this.currentCrawlTimestamp = currentCrawlTimestamp;
	}

	public String getChangeIndicator() {
		return changeIndicator;
	}

	public void setChangeIndicator(String changeIndicator) {
		this.changeIndicator = changeIndicator;
	}

	public Date getPreviousCrawlTimestamp() {
		return previousCrawlTimestamp;
	}

	public void setPreviousCrawlTimestamp(Date previousCrawlTimestamp) {
		this.previousCrawlTimestamp = previousCrawlTimestamp;
	}

	public Date getUpdateTimestamp() {
		return updateTimestamp;
	}

	public void setUpdateTimestamp(Date updateTimestamp) {
		this.updateTimestamp = updateTimestamp;
	}

	public Integer getChangeType() {
		return changeType;
	}

	public void setChangeType(Integer changeType) {
		this.changeType = changeType;
	}

	public Integer getCriticalInd() {
		return criticalInd;
	}

	public void setCriticalInd(Integer criticalInd) {
		this.criticalInd = criticalInd;
	}

	public AlternateLinks[] getAlternateLinksCurrent() {
		return alternateLinksCurrent;
	}

	public void setAlternateLinksCurrent(AlternateLinks[] alternateLinksCurrent) {
		this.alternateLinksCurrent = alternateLinksCurrent;
	}

	public AlternateLinks[] getAlternateLinksPrevious() {
		return alternateLinksPrevious;
	}

	public void setAlternateLinksPrevious(AlternateLinks[] alternateLinksPrevious) {
		this.alternateLinksPrevious = alternateLinksPrevious;
	}

	public String getAmphtmlHrefCurrent() {
		return amphtmlHrefCurrent;
	}

	public void setAmphtmlHrefCurrent(String amphtmlHrefCurrent) {
		this.amphtmlHrefCurrent = amphtmlHrefCurrent;
	}

	public String getAmphtmlHrefPrevious() {
		return amphtmlHrefPrevious;
	}

	public void setAmphtmlHrefPrevious(String amphtmlHrefPrevious) {
		this.amphtmlHrefPrevious = amphtmlHrefPrevious;
	}

	public String getAnalyzedUrlSCurrent() {
		return analyzedUrlSCurrent;
	}

	public void setAnalyzedUrlSCurrent(String analyzedUrlSCurrent) {
		this.analyzedUrlSCurrent = analyzedUrlSCurrent;
	}

	public String getAnalyzedUrlSPrevious() {
		return analyzedUrlSPrevious;
	}

	public void setAnalyzedUrlSPrevious(String analyzedUrlSPrevious) {
		this.analyzedUrlSPrevious = analyzedUrlSPrevious;
	}

	public String getArchiveFlgCurrent() {
		return archiveFlgCurrent;
	}

	public void setArchiveFlgCurrent(String archiveFlgCurrent) {
		this.archiveFlgCurrent = archiveFlgCurrent;
	}

	public String getArchiveFlgPrevious() {
		return archiveFlgPrevious;
	}

	public void setArchiveFlgPrevious(String archiveFlgPrevious) {
		this.archiveFlgPrevious = archiveFlgPrevious;
	}

	public String getBaseTagCurrent() {
		return baseTagCurrent;
	}

	public void setBaseTagCurrent(String baseTagCurrent) {
		this.baseTagCurrent = baseTagCurrent;
	}

	public String getBaseTagPrevious() {
		return baseTagPrevious;
	}

	public void setBaseTagPrevious(String baseTagPrevious) {
		this.baseTagPrevious = baseTagPrevious;
	}

	public String getBaseTagTargetCurrent() {
		return baseTagTargetCurrent;
	}

	public void setBaseTagTargetCurrent(String baseTagTargetCurrent) {
		this.baseTagTargetCurrent = baseTagTargetCurrent;
	}

	public String getBaseTagTargetPrevious() {
		return baseTagTargetPrevious;
	}

	public void setBaseTagTargetPrevious(String baseTagTargetPrevious) {
		this.baseTagTargetPrevious = baseTagTargetPrevious;
	}

	public String getBlockedByRobotsCurrent() {
		return blockedByRobotsCurrent;
	}

	public void setBlockedByRobotsCurrent(String blockedByRobotsCurrent) {
		this.blockedByRobotsCurrent = blockedByRobotsCurrent;
	}

	public String getBlockedByRobotsPrevious() {
		return blockedByRobotsPrevious;
	}

	public void setBlockedByRobotsPrevious(String blockedByRobotsPrevious) {
		this.blockedByRobotsPrevious = blockedByRobotsPrevious;
	}

	public String getCanonicalCurrent() {
		return canonicalCurrent;
	}

	public void setCanonicalCurrent(String canonicalCurrent) {
		this.canonicalCurrent = canonicalCurrent;
	}

	public String getCanonicalPrevious() {
		return canonicalPrevious;
	}

	public void setCanonicalPrevious(String canonicalPrevious) {
		this.canonicalPrevious = canonicalPrevious;
	}

	public Boolean getCanonicalHeaderFlagCurrent() {
		return canonicalHeaderFlagCurrent;
	}

	public void setCanonicalHeaderFlagCurrent(Boolean canonicalHeaderFlagCurrent) {
		this.canonicalHeaderFlagCurrent = canonicalHeaderFlagCurrent;
	}

	public Boolean getCanonicalHeaderFlagPrevious() {
		return canonicalHeaderFlagPrevious;
	}

	public void setCanonicalHeaderFlagPrevious(Boolean canonicalHeaderFlagPrevious) {
		this.canonicalHeaderFlagPrevious = canonicalHeaderFlagPrevious;
	}

	public String getCanonicalHeaderTypeCurrent() {
		return canonicalHeaderTypeCurrent;
	}

	public void setCanonicalHeaderTypeCurrent(String canonicalHeaderTypeCurrent) {
		this.canonicalHeaderTypeCurrent = canonicalHeaderTypeCurrent;
	}

	public String getCanonicalHeaderTypePrevious() {
		return canonicalHeaderTypePrevious;
	}

	public void setCanonicalHeaderTypePrevious(String canonicalHeaderTypePrevious) {
		this.canonicalHeaderTypePrevious = canonicalHeaderTypePrevious;
	}

	public String getCanonicalTypeCurrent() {
		return canonicalTypeCurrent;
	}

	public void setCanonicalTypeCurrent(String canonicalTypeCurrent) {
		this.canonicalTypeCurrent = canonicalTypeCurrent;
	}

	public String getCanonicalTypePrevious() {
		return canonicalTypePrevious;
	}

	public void setCanonicalTypePrevious(String canonicalTypePrevious) {
		this.canonicalTypePrevious = canonicalTypePrevious;
	}

	public String getCanonicalUrlIsConsistentCurrent() {
		return canonicalUrlIsConsistentCurrent;
	}

	public void setCanonicalUrlIsConsistentCurrent(String canonicalUrlIsConsistentCurrent) {
		this.canonicalUrlIsConsistentCurrent = canonicalUrlIsConsistentCurrent;
	}

	public String getCanonicalUrlIsConsistentPrevious() {
		return canonicalUrlIsConsistentPrevious;
	}

	public void setCanonicalUrlIsConsistentPrevious(String canonicalUrlIsConsistentPrevious) {
		this.canonicalUrlIsConsistentPrevious = canonicalUrlIsConsistentPrevious;
	}

	public String getContentTypeCurrent() {
		return contentTypeCurrent;
	}

	public void setContentTypeCurrent(String contentTypeCurrent) {
		this.contentTypeCurrent = contentTypeCurrent;
	}

	public String getContentTypePrevious() {
		return contentTypePrevious;
	}

	public void setContentTypePrevious(String contentTypePrevious) {
		this.contentTypePrevious = contentTypePrevious;
	}

	public CustomData[] getCustomDataCurrent() {
		return customDataCurrent;
	}

	public void setCustomDataCurrent(CustomData[] customDataCurrent) {
		this.customDataCurrent = customDataCurrent;
	}

	public CustomData[] getCustomDataPrevious() {
		return customDataPrevious;
	}

	public void setCustomDataPrevious(CustomData[] customDataPrevious) {
		this.customDataPrevious = customDataPrevious;
	}

	public String getDescriptionCurrent() {
		return descriptionCurrent;
	}

	public void setDescriptionCurrent(String descriptionCurrent) {
		this.descriptionCurrent = descriptionCurrent;
	}

	public String getDescriptionPrevious() {
		return descriptionPrevious;
	}

	public void setDescriptionPrevious(String descriptionPrevious) {
		this.descriptionPrevious = descriptionPrevious;
	}

	public Integer getDescriptionLengthCurrent() {
		return descriptionLengthCurrent;
	}

	public void setDescriptionLengthCurrent(Integer descriptionLengthCurrent) {
		this.descriptionLengthCurrent = descriptionLengthCurrent;
	}

	public Integer getDescriptionLengthPrevious() {
		return descriptionLengthPrevious;
	}

	public void setDescriptionLengthPrevious(Integer descriptionLengthPrevious) {
		this.descriptionLengthPrevious = descriptionLengthPrevious;
	}

	public String getErrorMessageCurrent() {
		return errorMessageCurrent;
	}

	public void setErrorMessageCurrent(String errorMessageCurrent) {
		this.errorMessageCurrent = errorMessageCurrent;
	}

	public String getErrorMessagePrevious() {
		return errorMessagePrevious;
	}

	public void setErrorMessagePrevious(String errorMessagePrevious) {
		this.errorMessagePrevious = errorMessagePrevious;
	}

	public Integer getFinalResponseCodeCurrent() {
		return finalResponseCodeCurrent;
	}

	public void setFinalResponseCodeCurrent(Integer finalResponseCodeCurrent) {
		this.finalResponseCodeCurrent = finalResponseCodeCurrent;
	}

	public Integer getFinalResponseCodePrevious() {
		return finalResponseCodePrevious;
	}

	public void setFinalResponseCodePrevious(Integer finalResponseCodePrevious) {
		this.finalResponseCodePrevious = finalResponseCodePrevious;
	}

	public String getFollowFlgCurrent() {
		return followFlgCurrent;
	}

	public void setFollowFlgCurrent(String followFlgCurrent) {
		this.followFlgCurrent = followFlgCurrent;
	}

	public String getFollowFlgPrevious() {
		return followFlgPrevious;
	}

	public void setFollowFlgPrevious(String followFlgPrevious) {
		this.followFlgPrevious = followFlgPrevious;
	}

	public String[] getH1Current() {
		return h1Current;
	}

	public void setH1Current(String[] h1Current) {
		this.h1Current = h1Current;
	}

	public String[] getH1Previous() {
		return h1Previous;
	}

	public void setH1Previous(String[] h1Previous) {
		this.h1Previous = h1Previous;
	}

	public Integer getH1CountCurrent() {
		return h1CountCurrent;
	}

	public void setH1CountCurrent(Integer h1CountCurrent) {
		this.h1CountCurrent = h1CountCurrent;
	}

	public Integer getH1CountPrevious() {
		return h1CountPrevious;
	}

	public void setH1CountPrevious(Integer h1CountPrevious) {
		this.h1CountPrevious = h1CountPrevious;
	}

	public Integer getH1LengthCurrent() {
		return h1LengthCurrent;
	}

	public void setH1LengthCurrent(Integer h1LengthCurrent) {
		this.h1LengthCurrent = h1LengthCurrent;
	}

	public Integer getH1LengthPrevious() {
		return h1LengthPrevious;
	}

	public void setH1LengthPrevious(Integer h1LengthPrevious) {
		this.h1LengthPrevious = h1LengthPrevious;
	}

	public String[] getH2Current() {
		return h2Current;
	}

	public void setH2Current(String[] h2Current) {
		this.h2Current = h2Current;
	}

	public String[] getH2Previous() {
		return h2Previous;
	}

	public void setH2Previous(String[] h2Previous) {
		this.h2Previous = h2Previous;
	}

	public Boolean getHeaderNoarchiveCurrent() {
		return headerNoarchiveCurrent;
	}

	public void setHeaderNoarchiveCurrent(Boolean headerNoarchiveCurrent) {
		this.headerNoarchiveCurrent = headerNoarchiveCurrent;
	}

	public Boolean getHeaderNoarchivePrevious() {
		return headerNoarchivePrevious;
	}

	public void setHeaderNoarchivePrevious(Boolean headerNoarchivePrevious) {
		this.headerNoarchivePrevious = headerNoarchivePrevious;
	}

	public Boolean getHeaderNofollowCurrent() {
		return headerNofollowCurrent;
	}

	public void setHeaderNofollowCurrent(Boolean headerNofollowCurrent) {
		this.headerNofollowCurrent = headerNofollowCurrent;
	}

	public Boolean getHeaderNofollowPrevious() {
		return headerNofollowPrevious;
	}

	public void setHeaderNofollowPrevious(Boolean headerNofollowPrevious) {
		this.headerNofollowPrevious = headerNofollowPrevious;
	}

	public Boolean getHeaderNoindexCurrent() {
		return headerNoindexCurrent;
	}

	public void setHeaderNoindexCurrent(Boolean headerNoindexCurrent) {
		this.headerNoindexCurrent = headerNoindexCurrent;
	}

	public Boolean getHeaderNoindexPrevious() {
		return headerNoindexPrevious;
	}

	public void setHeaderNoindexPrevious(Boolean headerNoindexPrevious) {
		this.headerNoindexPrevious = headerNoindexPrevious;
	}

	public Boolean getHeaderNoodpCurrent() {
		return headerNoodpCurrent;
	}

	public void setHeaderNoodpCurrent(Boolean headerNoodpCurrent) {
		this.headerNoodpCurrent = headerNoodpCurrent;
	}

	public Boolean getHeaderNoodpPrevious() {
		return headerNoodpPrevious;
	}

	public void setHeaderNoodpPrevious(Boolean headerNoodpPrevious) {
		this.headerNoodpPrevious = headerNoodpPrevious;
	}

	public Boolean getHeaderNosnippetCurrent() {
		return headerNosnippetCurrent;
	}

	public void setHeaderNosnippetCurrent(Boolean headerNosnippetCurrent) {
		this.headerNosnippetCurrent = headerNosnippetCurrent;
	}

	public Boolean getHeaderNosnippetPrevious() {
		return headerNosnippetPrevious;
	}

	public void setHeaderNosnippetPrevious(Boolean headerNosnippetPrevious) {
		this.headerNosnippetPrevious = headerNosnippetPrevious;
	}

	public Boolean getHeaderNoydirCurrent() {
		return headerNoydirCurrent;
	}

	public void setHeaderNoydirCurrent(Boolean headerNoydirCurrent) {
		this.headerNoydirCurrent = headerNoydirCurrent;
	}

	public Boolean getHeaderNoydirPrevious() {
		return headerNoydirPrevious;
	}

	public void setHeaderNoydirPrevious(Boolean headerNoydirPrevious) {
		this.headerNoydirPrevious = headerNoydirPrevious;
	}

	public HreflangErrors getHreflangErrorsCurrent() {
		return hreflangErrorsCurrent;
	}

	public void setHreflangErrorsCurrent(HreflangErrors hreflangErrorsCurrent) {
		this.hreflangErrorsCurrent = hreflangErrorsCurrent;
	}

	public HreflangErrors getHreflangErrorsPrevious() {
		return hreflangErrorsPrevious;
	}

	public void setHreflangErrorsPrevious(HreflangErrors hreflangErrorsPrevious) {
		this.hreflangErrorsPrevious = hreflangErrorsPrevious;
	}

	public HreflangLinks[] getHreflangLinksCurrent() {
		return hreflangLinksCurrent;
	}

	public void setHreflangLinksCurrent(HreflangLinks[] hreflangLinksCurrent) {
		this.hreflangLinksCurrent = hreflangLinksCurrent;
	}

	public HreflangLinks[] getHreflangLinksPrevious() {
		return hreflangLinksPrevious;
	}

	public void setHreflangLinksPrevious(HreflangLinks[] hreflangLinksPrevious) {
		this.hreflangLinksPrevious = hreflangLinksPrevious;
	}

	public Integer getHreflangLinksOutCountCurrent() {
		return hreflangLinksOutCountCurrent;
	}

	public void setHreflangLinksOutCountCurrent(Integer hreflangLinksOutCountCurrent) {
		this.hreflangLinksOutCountCurrent = hreflangLinksOutCountCurrent;
	}

	public Integer getHreflangLinksOutCountPrevious() {
		return hreflangLinksOutCountPrevious;
	}

	public void setHreflangLinksOutCountPrevious(Integer hreflangLinksOutCountPrevious) {
		this.hreflangLinksOutCountPrevious = hreflangLinksOutCountPrevious;
	}

	public Integer getHreflangUrlCountCurrent() {
		return hreflangUrlCountCurrent;
	}

	public void setHreflangUrlCountCurrent(Integer hreflangUrlCountCurrent) {
		this.hreflangUrlCountCurrent = hreflangUrlCountCurrent;
	}

	public Integer getHreflangUrlCountPrevious() {
		return hreflangUrlCountPrevious;
	}

	public void setHreflangUrlCountPrevious(Integer hreflangUrlCountPrevious) {
		this.hreflangUrlCountPrevious = hreflangUrlCountPrevious;
	}

	public String getIndexFlgCurrent() {
		return indexFlgCurrent;
	}

	public void setIndexFlgCurrent(String indexFlgCurrent) {
		this.indexFlgCurrent = indexFlgCurrent;
	}

	public String getIndexFlgPrevious() {
		return indexFlgPrevious;
	}

	public void setIndexFlgPrevious(String indexFlgPrevious) {
		this.indexFlgPrevious = indexFlgPrevious;
	}

	public Boolean getIndexableCurrent() {
		return indexableCurrent;
	}

	public void setIndexableCurrent(Boolean indexableCurrent) {
		this.indexableCurrent = indexableCurrent;
	}

	public Boolean getIndexablePrevious() {
		return indexablePrevious;
	}

	public void setIndexablePrevious(Boolean indexablePrevious) {
		this.indexablePrevious = indexablePrevious;
	}

	public String[] getInsecureResourcesCurrent() {
		return insecureResourcesCurrent;
	}

	public void setInsecureResourcesCurrent(String[] insecureResourcesCurrent) {
		this.insecureResourcesCurrent = insecureResourcesCurrent;
	}

	public String[] getInsecureResourcesPrevious() {
		return insecureResourcesPrevious;
	}

	public void setInsecureResourcesPrevious(String[] insecureResourcesPrevious) {
		this.insecureResourcesPrevious = insecureResourcesPrevious;
	}

	public String getMetaCharsetCurrent() {
		return metaCharsetCurrent;
	}

	public void setMetaCharsetCurrent(String metaCharsetCurrent) {
		this.metaCharsetCurrent = metaCharsetCurrent;
	}

	public String getMetaCharsetPrevious() {
		return metaCharsetPrevious;
	}

	public void setMetaCharsetPrevious(String metaCharsetPrevious) {
		this.metaCharsetPrevious = metaCharsetPrevious;
	}

	public String getMetaContentTypeCurrent() {
		return metaContentTypeCurrent;
	}

	public void setMetaContentTypeCurrent(String metaContentTypeCurrent) {
		this.metaContentTypeCurrent = metaContentTypeCurrent;
	}

	public String getMetaContentTypePrevious() {
		return metaContentTypePrevious;
	}

	public void setMetaContentTypePrevious(String metaContentTypePrevious) {
		this.metaContentTypePrevious = metaContentTypePrevious;
	}

	public Boolean getMetaDisabledSitelinksCurrent() {
		return metaDisabledSitelinksCurrent;
	}

	public void setMetaDisabledSitelinksCurrent(Boolean metaDisabledSitelinksCurrent) {
		this.metaDisabledSitelinksCurrent = metaDisabledSitelinksCurrent;
	}

	public Boolean getMetaDisabledSitelinksPrevious() {
		return metaDisabledSitelinksPrevious;
	}

	public void setMetaDisabledSitelinksPrevious(Boolean metaDisabledSitelinksPrevious) {
		this.metaDisabledSitelinksPrevious = metaDisabledSitelinksPrevious;
	}

	public Boolean getMetaNoodpCurrent() {
		return metaNoodpCurrent;
	}

	public void setMetaNoodpCurrent(Boolean metaNoodpCurrent) {
		this.metaNoodpCurrent = metaNoodpCurrent;
	}

	public Boolean getMetaNoodpPrevious() {
		return metaNoodpPrevious;
	}

	public void setMetaNoodpPrevious(Boolean metaNoodpPrevious) {
		this.metaNoodpPrevious = metaNoodpPrevious;
	}

	public Boolean getMetaNosnippetCurrent() {
		return metaNosnippetCurrent;
	}

	public void setMetaNosnippetCurrent(Boolean metaNosnippetCurrent) {
		this.metaNosnippetCurrent = metaNosnippetCurrent;
	}

	public Boolean getMetaNosnippetPrevious() {
		return metaNosnippetPrevious;
	}

	public void setMetaNosnippetPrevious(Boolean metaNosnippetPrevious) {
		this.metaNosnippetPrevious = metaNosnippetPrevious;
	}

	public Boolean getMetaNoydirCurrent() {
		return metaNoydirCurrent;
	}

	public void setMetaNoydirCurrent(Boolean metaNoydirCurrent) {
		this.metaNoydirCurrent = metaNoydirCurrent;
	}

	public Boolean getMetaNoydirPrevious() {
		return metaNoydirPrevious;
	}

	public void setMetaNoydirPrevious(Boolean metaNoydirPrevious) {
		this.metaNoydirPrevious = metaNoydirPrevious;
	}

	public Boolean getMetaRedirectCurrent() {
		return metaRedirectCurrent;
	}

	public void setMetaRedirectCurrent(Boolean metaRedirectCurrent) {
		this.metaRedirectCurrent = metaRedirectCurrent;
	}

	public Boolean getMetaRedirectPrevious() {
		return metaRedirectPrevious;
	}

	public void setMetaRedirectPrevious(Boolean metaRedirectPrevious) {
		this.metaRedirectPrevious = metaRedirectPrevious;
	}

	public Boolean getMixedRedirectsCurrent() {
		return mixedRedirectsCurrent;
	}

	public void setMixedRedirectsCurrent(Boolean mixedRedirectsCurrent) {
		this.mixedRedirectsCurrent = mixedRedirectsCurrent;
	}

	public Boolean getMixedRedirectsPrevious() {
		return mixedRedirectsPrevious;
	}

	public void setMixedRedirectsPrevious(Boolean mixedRedirectsPrevious) {
		this.mixedRedirectsPrevious = mixedRedirectsPrevious;
	}

	public Boolean getMobileRelAlternateUrlIsConsistentCurrent() {
		return mobileRelAlternateUrlIsConsistentCurrent;
	}

	public void setMobileRelAlternateUrlIsConsistentCurrent(Boolean mobileRelAlternateUrlIsConsistentCurrent) {
		this.mobileRelAlternateUrlIsConsistentCurrent = mobileRelAlternateUrlIsConsistentCurrent;
	}

	public Boolean getMobileRelAlternateUrlIsConsistentPrevious() {
		return mobileRelAlternateUrlIsConsistentPrevious;
	}

	public void setMobileRelAlternateUrlIsConsistentPrevious(Boolean mobileRelAlternateUrlIsConsistentPrevious) {
		this.mobileRelAlternateUrlIsConsistentPrevious = mobileRelAlternateUrlIsConsistentPrevious;
	}

	public Boolean getNoodpCurrent() {
		return noodpCurrent;
	}

	public void setNoodpCurrent(Boolean noodpCurrent) {
		this.noodpCurrent = noodpCurrent;
	}

	public Boolean getNoodpPrevious() {
		return noodpPrevious;
	}

	public void setNoodpPrevious(Boolean noodpPrevious) {
		this.noodpPrevious = noodpPrevious;
	}

	public Boolean getNosnippetCurrent() {
		return nosnippetCurrent;
	}

	public void setNosnippetCurrent(Boolean nosnippetCurrent) {
		this.nosnippetCurrent = nosnippetCurrent;
	}

	public Boolean getNosnippetPrevious() {
		return nosnippetPrevious;
	}

	public void setNosnippetPrevious(Boolean nosnippetPrevious) {
		this.nosnippetPrevious = nosnippetPrevious;
	}

	public Boolean getNoydirCurrent() {
		return noydirCurrent;
	}

	public void setNoydirCurrent(Boolean noydirCurrent) {
		this.noydirCurrent = noydirCurrent;
	}

	public Boolean getNoydirPrevious() {
		return noydirPrevious;
	}

	public void setNoydirPrevious(Boolean noydirPrevious) {
		this.noydirPrevious = noydirPrevious;
	}

	public OgMarkup[] getOgMarkupCurrent() {
		return ogMarkupCurrent;
	}

	public void setOgMarkupCurrent(OgMarkup[] ogMarkupCurrent) {
		this.ogMarkupCurrent = ogMarkupCurrent;
	}

	public OgMarkup[] getOgMarkupPrevious() {
		return ogMarkupPrevious;
	}

	public void setOgMarkupPrevious(OgMarkup[] ogMarkupPrevious) {
		this.ogMarkupPrevious = ogMarkupPrevious;
	}

	public Integer getOgMarkupLengthCurrent() {
		return ogMarkupLengthCurrent;
	}

	public void setOgMarkupLengthCurrent(Integer ogMarkupLengthCurrent) {
		this.ogMarkupLengthCurrent = ogMarkupLengthCurrent;
	}

	public Integer getOgMarkupLengthPrevious() {
		return ogMarkupLengthPrevious;
	}

	public void setOgMarkupLengthPrevious(Integer ogMarkupLengthPrevious) {
		this.ogMarkupLengthPrevious = ogMarkupLengthPrevious;
	}

	public Integer getOutlinkCountCurrent() {
		return outlinkCountCurrent;
	}

	public void setOutlinkCountCurrent(Integer outlinkCountCurrent) {
		this.outlinkCountCurrent = outlinkCountCurrent;
	}

	public Integer getOutlinkCountPrevious() {
		return outlinkCountPrevious;
	}

	public void setOutlinkCountPrevious(Integer outlinkCountPrevious) {
		this.outlinkCountPrevious = outlinkCountPrevious;
	}

	public String getPageAnalysisResultsChgIndJson() {
		return pageAnalysisResultsChgIndJson;
	}

	public void setPageAnalysisResultsChgIndJson(String pageAnalysisResultsChgIndJson) {
		this.pageAnalysisResultsChgIndJson = pageAnalysisResultsChgIndJson;
	}

	public PageLink[] getPageLinkCurrent() {
		return pageLinkCurrent;
	}

	public void setPageLinkCurrent(PageLink[] pageLinkCurrent) {
		this.pageLinkCurrent = pageLinkCurrent;
	}

	public PageLink[] getPageLinkPrevious() {
		return pageLinkPrevious;
	}

	public void setPageLinkPrevious(PageLink[] pageLinkPrevious) {
		this.pageLinkPrevious = pageLinkPrevious;
	}

	public Boolean getRedirectBlockedCurrent() {
		return redirectBlockedCurrent;
	}

	public void setRedirectBlockedCurrent(Boolean redirectBlockedCurrent) {
		this.redirectBlockedCurrent = redirectBlockedCurrent;
	}

	public Boolean getRedirectBlockedPrevious() {
		return redirectBlockedPrevious;
	}

	public void setRedirectBlockedPrevious(Boolean redirectBlockedPrevious) {
		this.redirectBlockedPrevious = redirectBlockedPrevious;
	}

	public String getRedirectBlockedReasonCurrent() {
		return redirectBlockedReasonCurrent;
	}

	public void setRedirectBlockedReasonCurrent(String redirectBlockedReasonCurrent) {
		this.redirectBlockedReasonCurrent = redirectBlockedReasonCurrent;
	}

	public String getRedirectBlockedReasonPrevious() {
		return redirectBlockedReasonPrevious;
	}

	public void setRedirectBlockedReasonPrevious(String redirectBlockedReasonPrevious) {
		this.redirectBlockedReasonPrevious = redirectBlockedReasonPrevious;
	}

	public RedirectChain[] getRedirectChainCurrent() {
		return redirectChainCurrent;
	}

	public void setRedirectChainCurrent(RedirectChain[] redirectChainCurrent) {
		this.redirectChainCurrent = redirectChainCurrent;
	}

	public RedirectChain[] getRedirectChainPrevious() {
		return redirectChainPrevious;
	}

	public void setRedirectChainPrevious(RedirectChain[] redirectChainPrevious) {
		this.redirectChainPrevious = redirectChainPrevious;
	}

	public String getRedirectFinalUrlCurrent() {
		return redirectFinalUrlCurrent;
	}

	public void setRedirectFinalUrlCurrent(String redirectFinalUrlCurrent) {
		this.redirectFinalUrlCurrent = redirectFinalUrlCurrent;
	}

	public String getRedirectFinalUrlPrevious() {
		return redirectFinalUrlPrevious;
	}

	public void setRedirectFinalUrlPrevious(String redirectFinalUrlPrevious) {
		this.redirectFinalUrlPrevious = redirectFinalUrlPrevious;
	}

	public Integer getRedirectTimesCurrent() {
		return redirectTimesCurrent;
	}

	public void setRedirectTimesCurrent(Integer redirectTimesCurrent) {
		this.redirectTimesCurrent = redirectTimesCurrent;
	}

	public Integer getRedirectTimesPrevious() {
		return redirectTimesPrevious;
	}

	public void setRedirectTimesPrevious(Integer redirectTimesPrevious) {
		this.redirectTimesPrevious = redirectTimesPrevious;
	}

	public String getResponseCodeCurrent() {
		return responseCodeCurrent;
	}

	public void setResponseCodeCurrent(String responseCodeCurrent) {
		this.responseCodeCurrent = responseCodeCurrent;
	}

	public String getResponseCodePrevious() {
		return responseCodePrevious;
	}

	public void setResponseCodePrevious(String responseCodePrevious) {
		this.responseCodePrevious = responseCodePrevious;
	}

	public ResponseHeaders[] getResponseHeadersCurrent() {
		return responseHeadersCurrent;
	}

	public void setResponseHeadersCurrent(ResponseHeaders[] responseHeadersCurrent) {
		this.responseHeadersCurrent = responseHeadersCurrent;
	}

	public ResponseHeaders[] getResponseHeadersPrevious() {
		return responseHeadersPrevious;
	}

	public void setResponseHeadersPrevious(ResponseHeaders[] responseHeadersPrevious) {
		this.responseHeadersPrevious = responseHeadersPrevious;
	}

	public String getRobotsContentsCurrent() {
		return robotsContentsCurrent;
	}

	public void setRobotsContentsCurrent(String robotsContentsCurrent) {
		this.robotsContentsCurrent = robotsContentsCurrent;
	}

	public String getRobotsContentsPrevious() {
		return robotsContentsPrevious;
	}

	public void setRobotsContentsPrevious(String robotsContentsPrevious) {
		this.robotsContentsPrevious = robotsContentsPrevious;
	}

	public StructuredData getStructuredDataCurrent() {
		return structuredDataCurrent;
	}

	public void setStructuredDataCurrent(StructuredData structuredDataCurrent) {
		this.structuredDataCurrent = structuredDataCurrent;
	}

	public StructuredData getStructuredDataPrevious() {
		return structuredDataPrevious;
	}

	public void setStructuredDataPrevious(StructuredData structuredDataPrevious) {
		this.structuredDataPrevious = structuredDataPrevious;
	}

	public String getTitleCurrent() {
		return titleCurrent;
	}

	public void setTitleCurrent(String titleCurrent) {
		this.titleCurrent = titleCurrent;
	}

	public String getTitlePrevious() {
		return titlePrevious;
	}

	public void setTitlePrevious(String titlePrevious) {
		this.titlePrevious = titlePrevious;
	}

	public Integer getTitleLengthCurrent() {
		return titleLengthCurrent;
	}

	public void setTitleLengthCurrent(Integer titleLengthCurrent) {
		this.titleLengthCurrent = titleLengthCurrent;
	}

	public Integer getTitleLengthPrevious() {
		return titleLengthPrevious;
	}

	public void setTitleLengthPrevious(Integer titleLengthPrevious) {
		this.titleLengthPrevious = titleLengthPrevious;
	}

	public String getViewportContentCurrent() {
		return viewportContentCurrent;
	}

	public void setViewportContentCurrent(String viewportContentCurrent) {
		this.viewportContentCurrent = viewportContentCurrent;
	}

	public String getViewportContentPrevious() {
		return viewportContentPrevious;
	}

	public void setViewportContentPrevious(String viewportContentPrevious) {
		this.viewportContentPrevious = viewportContentPrevious;
	}

	public Integer getSign() {
		return sign;
	}

	public void setSign(Integer sign) {
		this.sign = sign;
	}

	public Integer getTotal() {
		return total;
	}

	public void setTotal(Integer total) {
		this.total = total;
	}

	public Integer getTotalChanges() {
		return totalChanges;
	}

	public void setTotalChanges(Integer totalChanges) {
		this.totalChanges = totalChanges;
	}

	public Integer getTotalSeverityCritical() {
		return totalSeverityCritical;
	}

	public void setTotalSeverityCritical(Integer totalSeverityCritical) {
		this.totalSeverityCritical = totalSeverityCritical;
	}

	public Integer getTotalSeverityHigh() {
		return totalSeverityHigh;
	}

	public void setTotalSeverityHigh(Integer totalSeverityHigh) {
		this.totalSeverityHigh = totalSeverityHigh;
	}

	public Integer getTotalSeverityMedium() {
		return totalSeverityMedium;
	}

	public void setTotalSeverityMedium(Integer totalSeverityMedium) {
		this.totalSeverityMedium = totalSeverityMedium;
	}

	public Integer getTotalSeverityLow() {
		return totalSeverityLow;
	}

	public void setTotalSeverityLow(Integer totalSeverityLow) {
		this.totalSeverityLow = totalSeverityLow;
	}

	public Integer getTotalChangeTypeAdded() {
		return totalChangeTypeAdded;
	}

	public void setTotalChangeTypeAdded(Integer totalChangeTypeAdded) {
		this.totalChangeTypeAdded = totalChangeTypeAdded;
	}

	public Integer getTotalChangeTypeModified() {
		return totalChangeTypeModified;
	}

	public void setTotalChangeTypeModified(Integer totalChangeTypeModified) {
		this.totalChangeTypeModified = totalChangeTypeModified;
	}

	public Integer getTotalChangeTypeRemoved() {
		return totalChangeTypeRemoved;
	}

	public void setTotalChangeTypeRemoved(Integer totalChangeTypeRemoved) {
		this.totalChangeTypeRemoved = totalChangeTypeRemoved;
	}

	public String getRobotTxtCurrent() {
		return robotTxtCurrent;
	}

	public void setRobotTxtCurrent(String robotTxtCurrent) {
		this.robotTxtCurrent = robotTxtCurrent;
	}

	public String getRobotTxtPrevious() {
		return robotTxtPrevious;
	}

	public void setRobotTxtPrevious(String robotTxtPrevious) {
		this.robotTxtPrevious = robotTxtPrevious;
	}

	@Override
	public String toString() {
		return "TargetUrlChangeIndClickHouseEntity [domainId=" + domainId + ", url=" + url + ", trackDate=" + trackDate + ", urlHash=" + urlHash + ", urlMurmurHash="
				+ urlMurmurHash + ", currentCrawlTimestamp=" + currentCrawlTimestamp + ", changeIndicator=" + changeIndicator + ", previousCrawlTimestamp="
				+ previousCrawlTimestamp + ", updateTimestamp=" + updateTimestamp + ", changeType=" + changeType + ", criticalInd=" + criticalInd
				+ ", alternateLinksCurrent=" + Arrays.toString(alternateLinksCurrent) + ", alternateLinksPrevious=" + Arrays.toString(alternateLinksPrevious)
				+ ", amphtmlHrefCurrent=" + amphtmlHrefCurrent + ", amphtmlHrefPrevious=" + amphtmlHrefPrevious + ", analyzedUrlSCurrent=" + analyzedUrlSCurrent
				+ ", analyzedUrlSPrevious=" + analyzedUrlSPrevious + ", archiveFlgCurrent=" + archiveFlgCurrent + ", archiveFlgPrevious=" + archiveFlgPrevious
				+ ", baseTagCurrent=" + baseTagCurrent + ", baseTagPrevious=" + baseTagPrevious + ", baseTagTargetCurrent=" + baseTagTargetCurrent
				+ ", baseTagTargetPrevious=" + baseTagTargetPrevious + ", blockedByRobotsCurrent=" + blockedByRobotsCurrent + ", blockedByRobotsPrevious="
				+ blockedByRobotsPrevious + ", canonicalCurrent=" + canonicalCurrent + ", canonicalPrevious=" + canonicalPrevious + ", canonicalHeaderFlagCurrent="
				+ canonicalHeaderFlagCurrent + ", canonicalHeaderFlagPrevious=" + canonicalHeaderFlagPrevious + ", canonicalHeaderTypeCurrent="
				+ canonicalHeaderTypeCurrent + ", canonicalHeaderTypePrevious=" + canonicalHeaderTypePrevious + ", canonicalTypeCurrent=" + canonicalTypeCurrent
				+ ", canonicalTypePrevious=" + canonicalTypePrevious + ", canonicalUrlIsConsistentCurrent=" + canonicalUrlIsConsistentCurrent
				+ ", canonicalUrlIsConsistentPrevious=" + canonicalUrlIsConsistentPrevious + ", contentTypeCurrent=" + contentTypeCurrent + ", contentTypePrevious="
				+ contentTypePrevious + ", customDataCurrent=" + Arrays.toString(customDataCurrent) + ", customDataPrevious=" + Arrays.toString(customDataPrevious)
				+ ", descriptionCurrent=" + descriptionCurrent + ", descriptionPrevious=" + descriptionPrevious + ", descriptionLengthCurrent="
				+ descriptionLengthCurrent + ", descriptionLengthPrevious=" + descriptionLengthPrevious + ", errorMessageCurrent=" + errorMessageCurrent
				+ ", errorMessagePrevious=" + errorMessagePrevious + ", finalResponseCodeCurrent=" + finalResponseCodeCurrent + ", finalResponseCodePrevious="
				+ finalResponseCodePrevious + ", followFlgCurrent=" + followFlgCurrent + ", followFlgPrevious=" + followFlgPrevious + ", h1Current="
				+ Arrays.toString(h1Current) + ", h1Previous=" + Arrays.toString(h1Previous) + ", h1CountCurrent=" + h1CountCurrent + ", h1CountPrevious="
				+ h1CountPrevious + ", h1LengthCurrent=" + h1LengthCurrent + ", h1LengthPrevious=" + h1LengthPrevious + ", h2Current=" + Arrays.toString(h2Current)
				+ ", h2Previous=" + Arrays.toString(h2Previous) + ", headerNoarchiveCurrent=" + headerNoarchiveCurrent + ", headerNoarchivePrevious="
				+ headerNoarchivePrevious + ", headerNofollowCurrent=" + headerNofollowCurrent + ", headerNofollowPrevious=" + headerNofollowPrevious
				+ ", headerNoindexCurrent=" + headerNoindexCurrent + ", headerNoindexPrevious=" + headerNoindexPrevious + ", headerNoodpCurrent=" + headerNoodpCurrent
				+ ", headerNoodpPrevious=" + headerNoodpPrevious + ", headerNosnippetCurrent=" + headerNosnippetCurrent + ", headerNosnippetPrevious="
				+ headerNosnippetPrevious + ", headerNoydirCurrent=" + headerNoydirCurrent + ", headerNoydirPrevious=" + headerNoydirPrevious
				+ ", hreflangErrorsCurrent=" + hreflangErrorsCurrent + ", hreflangErrorsPrevious=" + hreflangErrorsPrevious + ", hreflangLinksCurrent="
				+ Arrays.toString(hreflangLinksCurrent) + ", hreflangLinksPrevious=" + Arrays.toString(hreflangLinksPrevious) + ", hreflangLinksOutCountCurrent="
				+ hreflangLinksOutCountCurrent + ", hreflangLinksOutCountPrevious=" + hreflangLinksOutCountPrevious + ", hreflangUrlCountCurrent="
				+ hreflangUrlCountCurrent + ", hreflangUrlCountPrevious=" + hreflangUrlCountPrevious + ", indexFlgCurrent=" + indexFlgCurrent + ", indexFlgPrevious="
				+ indexFlgPrevious + ", indexableCurrent=" + indexableCurrent + ", indexablePrevious=" + indexablePrevious + ", insecureResourcesCurrent="
				+ Arrays.toString(insecureResourcesCurrent) + ", insecureResourcesPrevious=" + Arrays.toString(insecureResourcesPrevious) + ", metaCharsetCurrent="
				+ metaCharsetCurrent + ", metaCharsetPrevious=" + metaCharsetPrevious + ", metaContentTypeCurrent=" + metaContentTypeCurrent
				+ ", metaContentTypePrevious=" + metaContentTypePrevious + ", metaDisabledSitelinksCurrent=" + metaDisabledSitelinksCurrent
				+ ", metaDisabledSitelinksPrevious=" + metaDisabledSitelinksPrevious + ", metaNoodpCurrent=" + metaNoodpCurrent + ", metaNoodpPrevious="
				+ metaNoodpPrevious + ", metaNosnippetCurrent=" + metaNosnippetCurrent + ", metaNosnippetPrevious=" + metaNosnippetPrevious + ", metaNoydirCurrent="
				+ metaNoydirCurrent + ", metaNoydirPrevious=" + metaNoydirPrevious + ", metaRedirectCurrent=" + metaRedirectCurrent + ", metaRedirectPrevious="
				+ metaRedirectPrevious + ", mixedRedirectsCurrent=" + mixedRedirectsCurrent + ", mixedRedirectsPrevious=" + mixedRedirectsPrevious
				+ ", mobileRelAlternateUrlIsConsistentCurrent=" + mobileRelAlternateUrlIsConsistentCurrent + ", mobileRelAlternateUrlIsConsistentPrevious="
				+ mobileRelAlternateUrlIsConsistentPrevious + ", noodpCurrent=" + noodpCurrent + ", noodpPrevious=" + noodpPrevious + ", nosnippetCurrent="
				+ nosnippetCurrent + ", nosnippetPrevious=" + nosnippetPrevious + ", noydirCurrent=" + noydirCurrent + ", noydirPrevious=" + noydirPrevious
				+ ", ogMarkupCurrent=" + Arrays.toString(ogMarkupCurrent) + ", ogMarkupPrevious=" + Arrays.toString(ogMarkupPrevious) + ", ogMarkupLengthCurrent="
				+ ogMarkupLengthCurrent + ", ogMarkupLengthPrevious=" + ogMarkupLengthPrevious + ", outlinkCountCurrent=" + outlinkCountCurrent
				+ ", outlinkCountPrevious=" + outlinkCountPrevious + ", pageAnalysisResultsChgIndJson=" + pageAnalysisResultsChgIndJson + ", pageLinkCurrent="
				+ Arrays.toString(pageLinkCurrent) + ", pageLinkPrevious=" + Arrays.toString(pageLinkPrevious) + ", redirectBlockedCurrent=" + redirectBlockedCurrent
				+ ", redirectBlockedPrevious=" + redirectBlockedPrevious + ", redirectBlockedReasonCurrent=" + redirectBlockedReasonCurrent
				+ ", redirectBlockedReasonPrevious=" + redirectBlockedReasonPrevious + ", redirectChainCurrent=" + Arrays.toString(redirectChainCurrent)
				+ ", redirectChainPrevious=" + Arrays.toString(redirectChainPrevious) + ", redirectFinalUrlCurrent=" + redirectFinalUrlCurrent
				+ ", redirectFinalUrlPrevious=" + redirectFinalUrlPrevious + ", redirectTimesCurrent=" + redirectTimesCurrent + ", redirectTimesPrevious="
				+ redirectTimesPrevious + ", responseCodeCurrent=" + responseCodeCurrent + ", responseCodePrevious=" + responseCodePrevious
				+ ", responseHeadersCurrent=" + Arrays.toString(responseHeadersCurrent) + ", responseHeadersPrevious=" + Arrays.toString(responseHeadersPrevious)
				+ ", robotsContentsCurrent=" + robotsContentsCurrent + ", robotsContentsPrevious=" + robotsContentsPrevious + ", structuredDataCurrent="
				+ structuredDataCurrent + ", structuredDataPrevious=" + structuredDataPrevious + ", titleCurrent=" + titleCurrent + ", titlePrevious=" + titlePrevious
				+ ", titleLengthCurrent=" + titleLengthCurrent + ", titleLengthPrevious=" + titleLengthPrevious + ", viewportContentCurrent=" + viewportContentCurrent
				+ ", viewportContentPrevious=" + viewportContentPrevious + ", sign=" + sign + ", robotTxtCurrent=" + robotTxtCurrent + ", robotTxtPrevious="
				+ robotTxtPrevious + ", total=" + total + ", totalChanges=" + totalChanges + ", totalSeverityCritical=" + totalSeverityCritical + ", totalSeverityHigh="
				+ totalSeverityHigh + ", totalSeverityMedium=" + totalSeverityMedium + ", totalSeverityLow=" + totalSeverityLow + ", totalChangeTypeAdded="
				+ totalChangeTypeAdded + ", totalChangeTypeModified=" + totalChangeTypeModified + ", totalChangeTypeRemoved=" + totalChangeTypeRemoved + "]";
	}

}
