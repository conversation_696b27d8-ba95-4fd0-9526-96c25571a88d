package com.actonia.entity;

import java.util.Date;

public class TargetUrlCustomDataClickHouseEntity {
	private Integer domain_id;
	private String url;
	private String custom_data;
	private Date crawl_timestamp;
	private Date daily_data_creation_date;
	private String url_hash;
	private String url_murmur_hash;

	public Integer getDomain_id() {
		return domain_id;
	}

	public void setDomain_id(Integer domain_id) {
		this.domain_id = domain_id;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getCustom_data() {
		return custom_data;
	}

	public void setCustom_data(String custom_data) {
		this.custom_data = custom_data;
	}

	public Date getCrawl_timestamp() {
		return crawl_timestamp;
	}

	public void setCrawl_timestamp(Date crawl_timestamp) {
		this.crawl_timestamp = crawl_timestamp;
	}

	public Date getDaily_data_creation_date() {
		return daily_data_creation_date;
	}

	public void setDaily_data_creation_date(Date daily_data_creation_date) {
		this.daily_data_creation_date = daily_data_creation_date;
	}

	public String getUrl_hash() {
		return url_hash;
	}

	public void setUrl_hash(String url_hash) {
		this.url_hash = url_hash;
	}

	public String getUrl_murmur_hash() {
		return url_murmur_hash;
	}

	public void setUrl_murmur_hash(String url_murmur_hash) {
		this.url_murmur_hash = url_murmur_hash;
	}

	@Override
	public String toString() {
		return "TargetUrlCustomDataClickHouseEntity [domain_id=" + domain_id + ", url=" + url + ", custom_data=" + custom_data + ", crawl_timestamp=" + crawl_timestamp
				+ ", daily_data_creation_date=" + daily_data_creation_date + ", url_hash=" + url_hash + ", url_murmur_hash=" + url_murmur_hash + "]";
	}

}
