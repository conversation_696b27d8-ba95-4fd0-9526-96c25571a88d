package com.actonia.entity;

import java.util.Date;

public class GoogleTagJavascriptEntity {
	private Long id;
	private String accountId;
	private String containerId;
	private String workspaceName;
	private String javascriptName;
	private String tagId;
	private String triggerId;
	private String variableId;
	private String scriptContent;
	private Date lastUpdateTimestamp;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getAccountId() {
		return accountId;
	}

	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}

	public String getContainerId() {
		return containerId;
	}

	public void setContainerId(String containerId) {
		this.containerId = containerId;
	}

	public String getWorkspaceName() {
		return workspaceName;
	}

	public void setWorkspaceName(String workspaceName) {
		this.workspaceName = workspaceName;
	}

	public String getJavascriptName() {
		return javascriptName;
	}

	public void setJavascriptName(String javascriptName) {
		this.javascriptName = javascriptName;
	}

	public String getTagId() {
		return tagId;
	}

	public void setTagId(String tagId) {
		this.tagId = tagId;
	}

	public String getTriggerId() {
		return triggerId;
	}

	public void setTriggerId(String triggerId) {
		this.triggerId = triggerId;
	}

	public String getVariableId() {
		return variableId;
	}

	public void setVariableId(String variableId) {
		this.variableId = variableId;
	}

	public String getScriptContent() {
		return scriptContent;
	}

	public void setScriptContent(String scriptContent) {
		this.scriptContent = scriptContent;
	}

	public Date getLastUpdateTimestamp() {
		return lastUpdateTimestamp;
	}

	public void setLastUpdateTimestamp(Date lastUpdateTimestamp) {
		this.lastUpdateTimestamp = lastUpdateTimestamp;
	}

	@Override
	public String toString() {
		return "GoogleTagJavascriptEntity [id=" + id + ", accountId=" + accountId + ", containerId=" + containerId + ", workspaceName=" + workspaceName
				+ ", javascriptName=" + javascriptName + ", tagId=" + tagId + ", triggerId=" + triggerId + ", variableId=" + variableId + ", scriptContent="
				+ scriptContent + ", lastUpdateTimestamp=" + lastUpdateTimestamp + "]";
	}

}
