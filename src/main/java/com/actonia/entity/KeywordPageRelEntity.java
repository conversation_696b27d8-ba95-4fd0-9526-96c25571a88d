package com.actonia.entity;

public class KeywordPageRelEntity {

	public static final int DEFAULT_SOURCE_TYPE = 10;

	private long id;
	private Integer ownDomainId;
	private long keywordId;
	private String keywordHash;
	private long pageId;
	private String urlHash;
	private String urlMurmur3Hash;
	private String customUrlMurmur3Hash;
	private Integer sourceType;
	
	
	public long getId() {
		return id;
	}
	public void setId(long id) {
		this.id = id;
	}
	public Integer getOwnDomainId() {
		return ownDomainId;
	}
	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}
	public long getKeywordId() {
		return keywordId;
	}
	public void setKeywordId(long keywordId) {
		this.keywordId = keywordId;
	}
	public String getKeywordHash() {
		return keywordHash;
	}
	public void setKeywordHash(String keywordHash) {
		this.keywordHash = keywordHash;
	}
	public long getPageId() {
		return pageId;
	}
	public void setPageId(long pageId) {
		this.pageId = pageId;
	}
	public String getUrlHash() {
		return urlHash;
	}
	public void setUrlHash(String urlHash) {
		this.urlHash = urlHash;
	}
	public String getUrlMurmur3Hash() {
		return urlMurmur3Hash;
	}
	public void setUrlMurmur3Hash(String urlMurmur3Hash) {
		this.urlMurmur3Hash = urlMurmur3Hash;
	}
	public String getCustomUrlMurmur3Hash() {
		return customUrlMurmur3Hash;
	}
	public void setCustomUrlMurmur3Hash(String customUrlMurmur3Hash) {
		this.customUrlMurmur3Hash = customUrlMurmur3Hash;
	}
	public Integer getSourceType() {
		return sourceType;
	}
	public void setSourceType(Integer sourceType) {
		this.sourceType = sourceType;
	}
	
	
}
