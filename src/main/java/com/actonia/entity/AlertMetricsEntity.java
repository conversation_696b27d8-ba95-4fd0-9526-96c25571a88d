package com.actonia.entity;

public class AlertMetricsEntity {
	
	public static final int URL_TYPE_TARGET = 1;
	public static final int URL_TYPE_COMPETITOR = 2;
	public static final int URL_TYPE_PARTNER = 3;
	
	public static final int STATUS_DISABLED = 0;
	public static final int STATUS_ENABLED = 1;

	private int id;
	private int ownDomainId;
	private int urlType;
	private int metricsId;
	private int status;
	private int createDate;
	private int lastUpdateDate;
	
	public int getId() {
		return id;
	}
	
	public void setId(int id) {
		this.id = id;
	}
	
	public int getOwnDomainId() {
		return ownDomainId;
	}
	
	public void setOwnDomainId(int ownDomainId) {
		this.ownDomainId = ownDomainId;
	}
	
	public int getUrlType() {
		return urlType;
	}
	
	public void setUrlType(int urlType) {
		this.urlType = urlType;
	}
	
	public int getMetricsId() {
		return metricsId;
	}

	public void setMetricsId(int metricsId) {
		this.metricsId = metricsId;
	}
	
	public int getStatus() {
		return status;
	}
	
	public void setStatus(int status) {
		this.status = status;
	}
	
	public int getCreateDate() {
		return createDate;
	}
	
	public void setCreateDate(int createDate) {
		this.createDate = createDate;
	}
	
	public int getLastUpdateDate() {
		return lastUpdateDate;
	}
	
	public void setLastUpdateDate(int lastUpdateDate) {
		this.lastUpdateDate = lastUpdateDate;
	}
}