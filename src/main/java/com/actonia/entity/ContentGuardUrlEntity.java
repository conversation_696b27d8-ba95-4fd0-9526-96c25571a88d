package com.actonia.entity;

import java.util.Date;

public class ContentGuardUrlEntity {
	private Long id;
	private int domainId;
	private String hashCd;
	private String url;
	private Long groupId;
	private String urlHashString;
	private Long murmurHash;
	private String s3FileName;
	private Date lastUpdateTimestamp;
	private Integer crawlStatus;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public int getDomainId() {
		return domainId;
	}

	public void setDomainId(int domainId) {
		this.domainId = domainId;
	}

	public String getHashCd() {
		return hashCd;
	}

	public void setHashCd(String hashCd) {
		this.hashCd = hashCd;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public Long getGroupId() {
		return groupId;
	}

	public void setGroupId(Long groupId) {
		this.groupId = groupId;
	}

	public String getUrlHashString() {
		return urlHashString;
	}

	public void setUrlHashString(String urlHashString) {
		this.urlHashString = urlHashString;
	}

	public Long getMurmurHash() {
		return murmurHash;
	}

	public void setMurmurHash(Long murmurHash) {
		this.murmurHash = murmurHash;
	}

	public String getS3FileName() {
		return s3FileName;
	}

	public void setS3FileName(String s3FileName) {
		this.s3FileName = s3FileName;
	}

	public Date getLastUpdateTimestamp() {
		return lastUpdateTimestamp;
	}

	public void setLastUpdateTimestamp(Date lastUpdateTimestamp) {
		this.lastUpdateTimestamp = lastUpdateTimestamp;
	}

	public Integer getCrawlStatus() {
		return crawlStatus;
	}

	public void setCrawlStatus(Integer crawlStatus) {
		this.crawlStatus = crawlStatus;
	}

	@Override
	public String toString() {
		return "ContentGuardUrlEntity [id=" + id + ", domainId=" + domainId + ", hashCd=" + hashCd + ", url=" + url + ", groupId=" + groupId + ", urlHashString="
				+ urlHashString + ", murmurHash=" + murmurHash + ", s3FileName=" + s3FileName + ", lastUpdateTimestamp=" + lastUpdateTimestamp + ", crawlStatus="
				+ crawlStatus + "]";
	}

}