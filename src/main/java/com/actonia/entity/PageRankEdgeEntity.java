package com.actonia.entity;

import javax.persistence.Transient;

public class PageRankEdgeEntity {
	private int sourceNodeId;
	private int targetNodeId;

	@Transient
	private Integer targetNodeCount;

	public int getSourceNodeId() {
		return sourceNodeId;
	}

	public void setSourceNodeId(int sourceNodeId) {
		this.sourceNodeId = sourceNodeId;
	}

	public int getTargetNodeId() {
		return targetNodeId;
	}

	public void setTargetNodeId(int targetNodeId) {
		this.targetNodeId = targetNodeId;
	}

	public Integer getTargetNodeCount() {
		return targetNodeCount;
	}

	public void setTargetNodeCount(Integer targetNodeCount) {
		this.targetNodeCount = targetNodeCount;
	}

	@Override
	public String toString() {
		return "PageRankEdgeEntity [sourceNodeId=" + sourceNodeId + ", targetNodeId=" + targetNodeId + ", targetNodeCount=" + targetNodeCount + "]";
	}

}