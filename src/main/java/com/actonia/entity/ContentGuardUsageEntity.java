package com.actonia.entity;

import javax.persistence.Transient;

public class ContentGuardUsageEntity {
	private int domainId;
	private int usageDate;
	private Long groupId;
	private int totalUrls;

	@Transient
	private String groupName;

	@Transient
	private Integer crawlFrequencyType;

	public int getDomainId() {
		return domainId;
	}

	public void setDomainId(int domainId) {
		this.domainId = domainId;
	}

	public int getUsageDate() {
		return usageDate;
	}

	public void setUsageDate(int usageDate) {
		this.usageDate = usageDate;
	}

	public Long getGroupId() {
		return groupId;
	}

	public void setGroupId(Long groupId) {
		this.groupId = groupId;
	}

	public int getTotalUrls() {
		return totalUrls;
	}

	public void setTotalUrls(int totalUrls) {
		this.totalUrls = totalUrls;
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	public Integer getCrawlFrequencyType() {
		return crawlFrequencyType;
	}

	public void setCrawlFrequencyType(Integer crawlFrequencyType) {
		this.crawlFrequencyType = crawlFrequencyType;
	}

	@Override
	public String toString() {
		return "ContentGuardUsageEntity [domainId=" + domainId + ", usageDate=" + usageDate + ", groupId=" + groupId + ", totalUrls=" + totalUrls + ", groupName="
				+ groupName + ", crawlFrequencyType=" + crawlFrequencyType + "]";
	}

}