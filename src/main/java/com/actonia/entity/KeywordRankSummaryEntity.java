package com.actonia.entity;

public class KeywordRankSummaryEntity {
	
	public static final int SUMMARY_TYPE_WEEK = 7;
	public static final int SUMMARY_TYPE_MONTH = 30;
	
	public static final int BEST_RANK = 1;
	public static final int EXCLUDE_101_RANK = 2;
	public static final int INCLUDE_101_RANK = 3;
	public static final int MODE_RANK = 4;
	
	private Long id;
	private Integer keywordId;
	private Integer ownDomainId;
	private Integer logDate;
	private Integer searchEngineId;
	private Long highestTargeturlId;
	private Integer summaryType;
	private Integer yearAndMonth;
	private Integer weekInYear;
	private Integer weekInMonth;

	private Float trueBestRank;
	private Float trueModeRank;
	private Float trueAvgExclude101;
	private Float trueAvgInclude101;

	private Float webBestRank;
	private Float webModeRank;
	private Float webAvgExclude101;
	private Float webAvgInclude101;

	private String keywordName;
	private String highestRankUrl;
	private Integer avgSearchVolume;

	public String getKeywordName() {
		return keywordName;
	}

	public void setKeywordName(String keywordName) {
		this.keywordName = keywordName;
	}

	public String getHighestRankUrl() {
		return highestRankUrl;
	}

	public void setHighestRankUrl(String highestRankUrl) {
		this.highestRankUrl = highestRankUrl;
	}

	public Integer getAvgSearchVolume() {
		return avgSearchVolume;
	}

	public void setAvgSearchVolume(Integer avgSearchVolume) {
		this.avgSearchVolume = avgSearchVolume;
	}

	public Float getTrueBestRank() {
		return trueBestRank;
	}

	public void setTrueBestRank(Float trueBestRank) {
		this.trueBestRank = trueBestRank;
	}

	public Float getTrueModeRank() {
		return trueModeRank;
	}

	public void setTrueModeRank(Float trueModeRank) {
		this.trueModeRank = trueModeRank;
	}

	public Float getTrueAvgExclude101() {
		return trueAvgExclude101;
	}

	public void setTrueAvgExclude101(Float trueAvgExclude101) {
		this.trueAvgExclude101 = trueAvgExclude101;
	}

	public Float getTrueAvgInclude101() {
		return trueAvgInclude101;
	}

	public void setTrueAvgInclude101(Float trueAvgInclude101) {
		this.trueAvgInclude101 = trueAvgInclude101;
	}

	public Float getWebBestRank() {
		return webBestRank;
	}

	public void setWebBestRank(Float webBestRank) {
		this.webBestRank = webBestRank;
	}

	public Float getWebModeRank() {
		return webModeRank;
	}

	public void setWebModeRank(Float webModeRank) {
		this.webModeRank = webModeRank;
	}

	public Float getWebAvgExclude101() {
		return webAvgExclude101;
	}

	public void setWebAvgExclude101(Float webAvgExclude101) {
		this.webAvgExclude101 = webAvgExclude101;
	}

	public Float getWebAvgInclude101() {
		return webAvgInclude101;
	}

	public void setWebAvgInclude101(Float webAvgInclude101) {
		this.webAvgInclude101 = webAvgInclude101;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Integer getKeywordId() {
		return keywordId;
	}

	public void setKeywordId(Integer keywordId) {
		this.keywordId = keywordId;
	}

	public Integer getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public Integer getLogDate() {
		return logDate;
	}

	public void setLogDate(Integer logDate) {
		this.logDate = logDate;
	}

	public Integer getSearchEngineId() {
		return searchEngineId;
	}

	public void setSearchEngineId(Integer searchEngineId) {
		this.searchEngineId = searchEngineId;
	}

	public Long getHighestTargeturlId() {
		return highestTargeturlId;
	}

	public void setHighestTargeturlId(Long highestTargeturlId) {
		this.highestTargeturlId = highestTargeturlId;
	}

	public Integer getSummaryType() {
		return summaryType;
	}

	public void setSummaryType(Integer summaryType) {
		this.summaryType = summaryType;
	}

	public Integer getYearAndMonth() {
		return yearAndMonth;
	}

	public void setYearAndMonth(Integer yearAndMonth) {
		this.yearAndMonth = yearAndMonth;
	}

	public Integer getWeekInYear() {
		return weekInYear;
	}

	public void setWeekInYear(Integer weekInYear) {
		this.weekInYear = weekInYear;
	}

	public Integer getWeekInMonth() {
		return weekInMonth;
	}

	public void setWeekInMonth(Integer weekInMonth) {
		this.weekInMonth = weekInMonth;
	}
	
	
	public static String getAvgTrueColumn(int weeklyAvgEankCalculation) {
		
		if (weeklyAvgEankCalculation == BEST_RANK) {
			return "true_best_rank";
		} else if (weeklyAvgEankCalculation == EXCLUDE_101_RANK) {
			return "true_avg_exclude101";
		} else if (weeklyAvgEankCalculation == INCLUDE_101_RANK) {
			return "true_avg_include101";
		} else if (weeklyAvgEankCalculation == MODE_RANK) {
			return "true_mode_rank";
		} else {
			return "true_mode_rank";
		}

	}
	
    public static String getAvgWebColumn(int weeklyAvgEankCalculation) {
		
		if (weeklyAvgEankCalculation == BEST_RANK) {
			return "web_best_rank";
		} else if (weeklyAvgEankCalculation == EXCLUDE_101_RANK) {
			return "web_avg_exclude101";
		} else if (weeklyAvgEankCalculation == INCLUDE_101_RANK) {
			return "web_avg_include101";
		} else if (weeklyAvgEankCalculation == MODE_RANK) {
			return "web_mode_rank";
		} else {
			return "web_mode_rank";
		}

	}
    
    //https://www.wrike.com/open.htm?id=65164249
    //by sunny
    public Float findAvgRankByCalSetting(int weeklyAvgEankCalculation, boolean isWebRank) {
    	
    	if (weeklyAvgEankCalculation == BEST_RANK) {
    		if (isWebRank) {
    			return webBestRank;
    		} else {
    			return trueBestRank;
    		}
		} else if (weeklyAvgEankCalculation == EXCLUDE_101_RANK) {
			if (isWebRank) {
				return webAvgExclude101;
			} else {
				return trueAvgExclude101;
			}
		} else if (weeklyAvgEankCalculation == INCLUDE_101_RANK) {
			if (isWebRank) {
				return webAvgInclude101;
			} else {
				return trueAvgInclude101;
			}
		} else if (weeklyAvgEankCalculation == MODE_RANK) {
			if (isWebRank) {
				return webModeRank;
			} else {
				return trueModeRank;
			}
		} else {
			if (isWebRank) {
				return webModeRank;
			} else {
				return trueModeRank;
			}
		}
    	
    }

	@Override
	public String toString() {
		return " KeywordRankSummaryEntity [id=" + id + ", keywordId=" + keywordId + ", ownDomainId=" + ownDomainId + ", logDate=" + logDate
				+ ", searchEngineId=" + searchEngineId + ", highestTargeturlId=" + highestTargeturlId + ", summaryType=" + summaryType
				+ ", yearAndMonth=" + yearAndMonth + ", weekInYear=" + weekInYear + ", weekInMonth=" + weekInMonth + ", trueBestRank=" + trueBestRank
				+ ", trueModeRank=" + trueModeRank + ", trueAvgExclude101=" + trueAvgExclude101 + ", trueAvgInclude101=" + trueAvgInclude101
				+ ", webBestRank=" + webBestRank + ", webModeRank=" + webModeRank + ", webAvgExclude101=" + webAvgExclude101 + ", webAvgInclude101="
				+ webAvgInclude101 + ", keywordName=" + keywordName + ", highestRankUrl=" + highestRankUrl + ", avgSearchVolume=" + avgSearchVolume
				+ "]";
	}
    
}
