package com.actonia.entity;

import java.math.BigInteger;
import java.util.Date;

import com.actonia.value.object.SharedCountsValueObject;

// this entity bean maps to a database table structure
public class SharedCountsEntity {
	private Date trackDate;
	private String domain;
	private String rootDomain;
	private String url;
	private String protocol;
	private String uri;
	private String folder1;
	private String folder2;
	private BigInteger urlHash;
	private BigInteger uriHash;
	private BigInteger folder1Hash;
	private BigInteger folder2Hash;
	private Integer sign;
	private String connectionUrl;
	private SharedCountsValueObject sharedCountsValueObject;

	public Date getTrackDate() {
		return trackDate;
	}

	public void setTrackDate(Date trackDate) {
		this.trackDate = trackDate;
	}

	public String getDomain() {
		return domain;
	}

	public void setDomain(String domain) {
		this.domain = domain;
	}

	public String getRootDomain() {
		return rootDomain;
	}

	public void setRootDomain(String rootDomain) {
		this.rootDomain = rootDomain;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getProtocol() {
		return protocol;
	}

	public void setProtocol(String protocol) {
		this.protocol = protocol;
	}

	public String getUri() {
		return uri;
	}

	public void setUri(String uri) {
		this.uri = uri;
	}

	public String getFolder1() {
		return folder1;
	}

	public void setFolder1(String folder1) {
		this.folder1 = folder1;
	}

	public String getFolder2() {
		return folder2;
	}

	public void setFolder2(String folder2) {
		this.folder2 = folder2;
	}

	public BigInteger getUrlHash() {
		return urlHash;
	}

	public void setUrlHash(BigInteger urlHash) {
		this.urlHash = urlHash;
	}

	public BigInteger getUriHash() {
		return uriHash;
	}

	public void setUriHash(BigInteger uriHash) {
		this.uriHash = uriHash;
	}

	public BigInteger getFolder1Hash() {
		return folder1Hash;
	}

	public void setFolder1Hash(BigInteger folder1Hash) {
		this.folder1Hash = folder1Hash;
	}

	public BigInteger getFolder2Hash() {
		return folder2Hash;
	}

	public void setFolder2Hash(BigInteger folder2Hash) {
		this.folder2Hash = folder2Hash;
	}

	public Integer getSign() {
		return sign;
	}

	public void setSign(Integer sign) {
		this.sign = sign;
	}

	public String getConnectionUrl() {
		return connectionUrl;
	}

	public void setConnectionUrl(String connectionUrl) {
		this.connectionUrl = connectionUrl;
	}

	public SharedCountsValueObject getSharedCountsValueObject() {
		return sharedCountsValueObject;
	}

	public void setSharedCountsValueObject(SharedCountsValueObject sharedCountsValueObject) {
		this.sharedCountsValueObject = sharedCountsValueObject;
	}

	@Override
	public String toString() {
		return "SharedCountsEntity [trackDate=" + trackDate + ", domain=" + domain + ", rootDomain=" + rootDomain + ", url=" + url + ", protocol=" + protocol + ", uri="
				+ uri + ", folder1=" + folder1 + ", folder2=" + folder2 + ", urlHash=" + urlHash + ", uriHash=" + uriHash + ", folder1Hash=" + folder1Hash
				+ ", folder2Hash=" + folder2Hash + ", sign=" + sign + ", connectionUrl=" + connectionUrl + ", sharedCountsValueObject=" + sharedCountsValueObject + "]";
	}

}
