package com.actonia.entity;

import java.util.Date;

import javax.persistence.Transient;

import lombok.Data;

@Data
public class OwnDomainSettingEntity {

	public static final int SEARCH_VOLUMN_TOP_KEYWORDS = 0;
	public static final float GWT_CONVERSION_RATE = 0f;
	public static final float GWT_ORDER_AVG_VALUE = 0f;
	public static final int ASSOCIATE_COMPLTITIOR_TOPX = 3;

	public static final int TARGET_URL_CRAWL_FREQUENCY_DAILY = 0;
	public static final int TARGET_URL_CRAWL_FREQUENCY_WEEKLY = 1;

	public static final int YOY_SUMMARY_DISABLED = 0;
	public static final int YOY_SUMMARY_WEEKLY = 7;
	public static final int YOY_SUMMARY_MONTHLY = 30;
	public static final int YOY_SUMMARY_WEEKLY_AND_MONTHLY = 37; // default

	public static final int MOBILE_ENABLEED = 1;

	public static final int SUSPEND_PROCESS_STATUS_NEWLY_ADDED = 0;
	public static final int SUSPEND_PROCESS_STATUS_PROCESSING = 1;
	public static final int SUSPEND_PROCESS_STATUS_PROCESS_FINISHED = 2;
	public static final int SUSPEND_PROCESS_STATUS_PROCESS_ERROR = 3;

	public static final int REMOVE_USER_STATUS_NEWLY_ADDED = 0;
	public static final int REMOVE_USER_STATUS_PROCESSING = 1;
	public static final int REMOVE_USER_STATUS_PROCESS_FINISHED = 2;
	public static final int REMOVE_USER_STATUS_PROCESS_ERROR = 3;

	public static final int GWT_GROUP_DEFAULT = 0;

	public static final int ENABLE_TRANSLATION = 1;
	public static final int DISABLE_TRANSLATION = 0;

	//Leo - https://www.wrike.com/open.htm?id=90292328
	public static final int GWM_PROFILE_ALL_UNSELECTED = 0;

	//Sunny - https://www.wrike.com/open.htm?id=*********
	public static final int GEO_FREQUENCE_DAILY = 1;
	public static final int GEO_FREQUENCE_WEEKLY = 7;

	public static final int GEO_RANK_ENABLEED = 1; // https://www.wrike.com/open.htm?id=379229286

	public static final int GA_REMOVE_PREFIX_NO = 0;
	public static final int GA_REMOVE_DOMAIN_ONLY = 1;
	public static final int GA_REMOVE_DOMAIN_AND_START_SLASH = 2;

	//added for google jobs
	public static final int ENABLE_GOOGLE_JOB = 1;
	public static final int DISABLE_GOOGLE_JOB = 0;
	public static final int GOOGLE_JOB_FREQUENCE_DAILY = 1;
	public static final int GOOGLE_JOB_FREQUENCE_WEEKLY = 7;
	public static final int GOOGLE_JOB_DEFAULT_FREQUENCE = GOOGLE_JOB_FREQUENCE_WEEKLY;

	public static final int ASSOCIATE_HRU_FREQUENCE_WEEKLY = 7;
	public static final int ASSOCIATE_HRU_FREQUENCE_MONTHLY = 30;

	public static final int DISABLED_DIFFERENT_KS = 0;
	public static final int ENABLED_DIFFERENT_KS = 1;

	public static final int DISABLED_DOMAIN_LEVEL_KEYWORD_TRACE = 0;
	public static final int ENABLED_KEYWORD_TRACE_FOR_SPECIFIC_TAGS = 1;
	public static final int ENABLED_KEYWORD_TRACE_FOR_ALL_TAGS = 2;

	public static final int ENABLED_DIFFERENT_VS_KEYWORD_SET = 1;
	public static final int DISABLED_DIFFERENT_VS_KEYWORD_SET = 0;

	public static final int DISABLE_SEPARATE_GEO = 0;
	public static final int ENABLE_SEPARATE_GEO = 1;

	public static final int PAUSE_RANKING_FLAG_DEFAULT = 0;
	public static final int PAUSE_RANKING_FLAG_PAUSE = 1;
	public static final int PAUSE_RANKING_FLAG_RESUME = 2;

	public static final int PAUSE_RANKING_STATUS_DEFAULT = 0;
	public static final int PAUSE_RANKING_STATUS_PROCESSING = 1;
	public static final int PAUSE_RANKING_STATUS_FINISH_WITHOUT_ERROR = 2;
	public static final int PAUSE_RANKING_STATUS_FINISH_WITH_ERROR = 3;
	public static final int PAUSE_RANKING_STATUS_FINISH_WITH_ERROR_PARAM = 4;

	private Integer id;
	private Integer ownDomainId;
	private Integer searchVolumeTopKeywords;
	private Integer targeturlCrawlFrequency;
	private Integer yoySummaryFlg;
	// https://www.wrike.com/open.htm?id=24347814
	private Integer mgdNewRankMaxDate;
	private Integer mgdCompetitorRankMaxDate;

	private Float gwtConversionRate;
	private Float gwtOrderAvgValue;

	// https://www.wrike.com/open.htm?id=********
	// by cee
	private String analyticsReportFromFtp;
	private String analyticsReportFromEmail;
	private String analyticsReportFromOther;

	private Integer associateHruQueuebaseId;
	private Integer associateHruFrequence;

	// https://www.wrike.com/open.htm?id=********
	// Dong
	private Integer keywordTranslation;

	// local rank
	private Integer enableLocalBusinessRank;

	private String cloudflareApiToken;
	private String cloudflareAccountId;
	private String cloudflareZoneId;

	//Cee - https://www.wrike.com/open.htm?id=*********
	public static final int PAUSE_RANKING_FLG_NOT = 0; //normal engine
	public static final int PAUSE_RANKING_FLG_YES = 1; //ignore engine - has Paused
	public static final int PAUSE_RANKING_FLG_RESUME = 2; //normal engine - re-enabled Engine from Pause
	public static final int PAUSE_RANKING_STATUS_SKIP = 2; //means QueueBase has finished processing;

	// https://www.wrike.com/open.htm?id=********
	private String gwtAccount;
	private String gwtPassword;
	private Integer gwtGroup;

	// https://www.wrike.com/open.htm?id=********
	private Integer monthlyResetPwd;

	private Integer weeklyAverageRankCalculation;

	private Integer monthlyAverageRankCalculation;

	// https://www.wrike.com/open.htm?id=********
	private Integer domainType;
	private Integer clientTier;
	private String companyName;

	private String gaTrafficType;
	private String gaSegment;

	private String secondaryGaDomainName;
	private String secondaryGaAnalyticsId;
	private String secondary_ga_oauth2_access_token;
	private String secondary_ga_oauth2_refresh_token;

	private String ahrefsEmailAddress;

	private String ahrefsPassword;

	private String ahrefsApiAccessToken;

	// https://www.wrike.com/open.htm?id=********  --by floyd
	private String plaCampaign;

	// https://www.wrike.com/open.htm?id=********
	private Integer gaCombiningFilter;
	private String gaFilter;

	//https://www.wrike.com/open.htm?id=********
	//by sunny
	private Integer dellWeeklyEmailLastDate;

	//by jimmy 
	private Integer regionId;

	private String cityName;

	private Integer ppcSov;

	private Integer crawlAdditonalDomains;

	//Leo - https://www.wrike.com/open.htm?id=90292328
	private Integer gwmProfileAll;

	private Integer autoAssociateCompetitorUrl;

	//Sunny - https://www.wrike.com/open.htm?id=*********
	private Integer geoFrequence;

	//https://www.wrike.com/open.htm?id=*********
	//Sunny
	private String regionalRelateIdList;

	private Boolean enableGeoMobile;

	private Integer enableCityRank;

	private Integer enableEncodeCityName;

	// https://www.wrike.com/open.htm?id=*********
	private Integer gaRemoveprefix;

	private String zohoCompanyId;

	private String zohoPlatformDomainId;

	private String menuControl;

	private String domain;

	private Integer trafficType;

	//added for google jobs
	private Integer enableGoogleJobDesktop;
	private Integer enableGoogleJobMobile;
	private Integer googleJobFrequence;

	//https://www.wrike.com/open.htm?id=*********
	private Integer enableDifferentSes;

	// https://www.wrike.com/open.htm?id=*********
	private Integer enableDomainLevelKeywordTrace;

	private Integer enableDifferentVsKeywordSet; // https://www.wrike.com/open.htm?id=*********

	private Integer separateGeo; // https://www.wrike.com/open.htm?id=*********

	//https://www.wrike.com/open.htm?id=********* ewain 20201026
	private Integer pauseRankingFlg;
	private Integer pauseRankingStatus;
	private String pauseRankingSearchengines;
	private Date pauseRankingProcessDate;

	private String siteCrawlDefaultUserAgent;

	private Integer targetUrlHtmlDailyDate;

	// https://www.wrike.com/open.htm?id=23105458
	// by sunny
	private Integer associateCompetitorTopx;

	private Integer enableMoblie;

	private Integer suspendProcessStatus;

	private Integer domainSuspendDate;

	private Integer removeUserStatus;

	private Integer removeUserDate;

	private String semRushSolr;

	private Integer targetUrlCustomDataDate;
	private Integer deleteNon2xxUrlsInDays;

	//Cee - https://www.wrike.com/open.htm?id=698658417
	@Transient
	public boolean isPausedRanking() {
		if (pauseRankingFlg == null || pauseRankingFlg == PAUSE_RANKING_FLG_NOT) {
			return false;
		}
		if (pauseRankingFlg == PAUSE_RANKING_FLG_RESUME &&
				pauseRankingStatus == PAUSE_RANKING_STATUS_SKIP) {
			return false;
		}
		return true;
	}
}