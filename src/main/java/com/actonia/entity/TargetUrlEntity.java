/**
 *
 */
package com.actonia.entity;

import lombok.Data;

import javax.persistence.Transient;
import java.util.Date;
import java.util.List;

/**
 * com.actonia.subserver.entity.TargetUrlEntity.java
 *
 * @version $Revision: 79748 $ $Author: <PERSON>@SHINETECHCHINA $
 */
@Data
public class TargetUrlEntity {

	public static final int TYPE_ADD_BY_USER = 1;

	public static final int TYPE_ADDBY_GA = 3;

	public static final int STATUS_ACTIVE = 1;

	public static final int STATUS_INACTIVE = 2;

	private Long id;

	private Integer ownDomainId;

	private String url;

	private String urlHash;

	private String urlMurmur3Hash;

	private String friendlyName;

	/**
	 * 1: add from QueueBase, 2: add from RSS feed, 3: add from highest ranking URL
	 */
	private Integer sourceType;

	private Integer type;

	private Integer status;

	/**
	 *  '0' or null: crawl normally as other URLs for URLs seeded from RSS, 1: Disable Page Crawling after initial success crawl for URLs seeded from RSS
	 */
	private Integer initialCrawlOnly;

	/**
	 * DEFAULT '1' COMMENT '1: disable/enable by UI, 2: by Queuebase, 3: by PoliteCrawler'.
	 */
	private Integer disableBy;

	private Integer disableCrawl;

	private Integer crawlFailFromDate;

	private Integer crawlFailTimes;

	private Date createDate;

	private Integer section_name_id;

	private Integer weekEntrances;

	private Integer weekBounces;

	private List<OptionEntity> optionEntity;

	private Integer canonicalUrlId;

	@Transient
	private int totalUrls;

	@Transient
	private int groupTagId;

	@Transient
	private Integer urlType;


	@Transient
	public boolean isManaged() {
		if (type == null || type != TYPE_ADD_BY_USER) {
			return false;
		}
		return status != null && status == STATUS_ACTIVE;
	}

}
