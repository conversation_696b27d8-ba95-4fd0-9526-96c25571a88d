package com.actonia.entity;

import java.math.BigDecimal;

public class OptionEntity {

	private Integer id;
	private Integer optionType;
	private Integer userId;
	private Integer ownDomainId;
	private Integer optionValue;
	private String optionText;
	private Integer urlId;
	private BigDecimal optionWeight;
	private Integer optionLength;
	
	private String url; // transient

	public Integer getOptionLength() {
		return optionLength;
	}

	public void setOptionLength(Integer optionLength) {
		this.optionLength = optionLength;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getOptionType() {
		return optionType;
	}

	public void setOptionType(Integer optionType) {
		this.optionType = optionType;
	}

	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	public Integer getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public Integer getOptionValue() {
		return optionValue;
	}

	public void setOptionValue(Integer optionValue) {
		this.optionValue = optionValue;
	}

	public String getOptionText() {
		return optionText;
	}

	public void setOptionText(String optionText) {
		this.optionText = optionText;
	}

	public Integer getUrlId() {
		return urlId;
	}

	public void setUrlId(Integer urlId) {
		this.urlId = urlId;
	}

	public BigDecimal getOptionWeight() {
		return optionWeight;
	}

	public void setOptionWeight(BigDecimal optionWeight) {
		this.optionWeight = optionWeight;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	@Override
	public String toString() {
		return " OptionEntity [id=" + id + ", optionType=" + optionType + ", userId=" + userId + ", ownDomainId=" + ownDomainId + ", optionValue="
				+ optionValue + ", optionText=" + optionText + ", urlId=" + urlId + ", optionWeight=" + optionWeight + ", optionLength="
				+ optionLength + ", url=" + url + "]";
	}

}
