package com.actonia.entity;

import lombok.Data;

import java.util.Date;
import java.util.Objects;

@Data
public class ResourceBatchDetailEntity {

    public static final int STATUS_CREATED = 0;
    public static final int STATUS_FINISHED = 2;
    public static final int STATUS_ERROR = 3;
    public static final int STATUS_INVALID = 4;

    private Long id;
    private Integer actionType;
    private Long infoId;
    private Integer ownDomainId;
    private String resourceMain;
    private String resourceSubordinate;
    private String resourceAdditional;
    private Integer resourceCategory;
    private String resourceSearchengines;
    private Long resourceId;
    private Long resourceSubId;
    private String resourceMd5;
    private Integer createDate;
    private Integer status;
    private Date processDate;
    private String errorMessage;
    private Integer statusRank;
    private Date processDateRank;
    private String errorMessageRank;
    private Integer statusSync;
    private Date processDateSync;
    private String errorMessageSync;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ResourceBatchDetailEntity)) return false;
        ResourceBatchDetailEntity that = (ResourceBatchDetailEntity) o;
        return Objects.equals(actionType, that.actionType) &&
                Objects.equals(infoId, that.infoId) &&
                Objects.equals(ownDomainId, that.ownDomainId) &&
                Objects.equals(resourceMain, that.resourceMain) &&
                Objects.equals(resourceSubordinate, that.resourceSubordinate) &&
                Objects.equals(resourceAdditional, that.resourceAdditional) &&
                Objects.equals(resourceCategory, that.resourceCategory) &&
                Objects.equals(resourceSearchengines, that.resourceSearchengines) &&
                Objects.equals(resourceId, that.resourceId) &&
                Objects.equals(resourceSubId, that.resourceSubId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(actionType, infoId, ownDomainId, resourceMain, resourceSubordinate, resourceAdditional, resourceCategory, resourceSearchengines, resourceId, resourceSubId);
    }
}
