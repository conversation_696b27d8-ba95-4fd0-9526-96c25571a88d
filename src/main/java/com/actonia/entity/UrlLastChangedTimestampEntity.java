package com.actonia.entity;

import lombok.Data;

import java.util.Date;

@Data
public class UrlLastChangedTimestampEntity {
    private Date trackDate;
    private Integer domainId;
    private Integer urlType;
    private String urlHash;
    private String urlMurmurHash;
    private Date lastChangedCrawlTimestamp;

	public static UrlLastChangedTimestampEntity fromHtmlClickHouseEntity(HtmlClickHouseEntity htmlClickHouseEntity) {
        UrlLastChangedTimestampEntity urlLastChangedTimestampEntity = new UrlLastChangedTimestampEntity();
        urlLastChangedTimestampEntity.setDomainId(htmlClickHouseEntity.getDomainId());
        urlLastChangedTimestampEntity.setUrlHash(htmlClickHouseEntity.getUrlHash());
        urlLastChangedTimestampEntity.setUrlMurmurHash(htmlClickHouseEntity.getUrlMurmurHash());
        urlLastChangedTimestampEntity.setLastChangedCrawlTimestamp(htmlClickHouseEntity.getCrawlTimestamp());
        return urlLastChangedTimestampEntity;
    }

}
