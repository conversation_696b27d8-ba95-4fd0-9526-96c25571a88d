/**
 * 
 */
package com.actonia.entity;

import java.util.Date;

public class EngineCountryLanguageMappingEntity {
	
	public static final Integer RANK_FROM_DEFAULT = 0;

	public static final int DISABLED = 0;
	public static final int ENABLED = 1;
	
	public static final int RANK_FROM_ALL = 0;
	public static final int RANK_FROM_SPECIAL_COUNTRY = 1;

	private Integer id;
	private Integer enabled;
	private String countryQueryName;
	private String countryDisplayName;
	private String engineQueryName;
	private String engineDisplayName;
	private String languageQueryName;
	private String languageDisplayName;
	private Integer rankFrom;
	private Integer engineId;
	private Integer languageId;
	private String remark;
	private Date createDate;
	
	public Integer getId() {
		return id;
	}
	
	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getEnabled() {
		return enabled;
	}
	
	public void setEnabled(Integer enabled) {
		this.enabled = enabled;
	}
	
	public String getCountryQueryName() {
		return countryQueryName;
	}
	
	public void setCountryQueryName(String countryQueryName) {
		this.countryQueryName = countryQueryName;
	}
	
	public String getCountryDisplayName() {
		return countryDisplayName;
	}
	
	public void setCountryDisplayName(String countryDisplayName) {
		this.countryDisplayName = countryDisplayName;
	}
	
	public String getEngineQueryName() {
		return engineQueryName;
	}
	
	public void setEngineQueryName(String engineQueryName) {
		this.engineQueryName = engineQueryName;
	}
	
	public String getEngineDisplayName() {
		return engineDisplayName;
	}
	
	public void setEngineDisplayName(String engineDisplayName) {
		this.engineDisplayName = engineDisplayName;
	}
	
	public String getLanguageQueryName() {
		return languageQueryName;
	}
	
	public void setLanguageQueryName(String languageQueryName) {
		this.languageQueryName = languageQueryName;
	}
	
	public String getLanguageDisplayName() {
		return languageDisplayName;
	}
	
	public void setLanguageDisplayName(String languageDisplayName) {
		this.languageDisplayName = languageDisplayName;
	}
	
	public Integer getRankFrom() {
		return rankFrom;
	}
	
	public void setRankFrom(Integer rankFrom) {
		this.rankFrom = rankFrom;
	}
	
	public Integer getEngineId() {
		return engineId;
	}
	
	public void setEngineId(Integer engineId) {
		this.engineId = engineId;
	}
	
	public Integer getLanguageId() {
		return languageId;
	}
	
	public void setLanguageId(Integer languageId) {
		this.languageId = languageId;
	}
	
	public String getRemark() {
		return remark;
	}
	
	public void setRemark(String remark) {
		this.remark = remark;
	}
	
	public Date getCreateDate() {
		return createDate;
	}
	
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
}