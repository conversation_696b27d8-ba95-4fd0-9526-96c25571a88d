package com.actonia.entity;

import lombok.Data;

import java.sql.Date;

@Data
public class OwnDomainTracking {

    /**
     * id
     */
    private int id;

    /**
     * t_own_domain.id
     */
    private int ownDomainId;

    /**
     * replace t_own_domain_setting.target_url_html_daily_date, format: yyyyMMdd
     */
    private Integer targetUrlLatestDate;

    /**
     * replace t_own_domain_setting.gsc_latest_final_date
     */
    private Date gscLatestFinalDate;

    /**
     * replace t_own_domain_setting.gsc_latest_fresh_date
     */
    private Date gscLatestFreshDate;

    /**
     * For Bing webmaster
     */
    private Date bwmLatestFinalDate;

    /**
     * For Bing webmaster
     */
    private Date bwmLatestFreshDate;

    /**
     * DEFAULT CURRENT_TIMESTAMP
     */
    private Date createDate;
}