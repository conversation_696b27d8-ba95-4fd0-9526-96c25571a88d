package com.actonia.entity;

public class TargeturlCrawler {
	private Integer id;

	private Integer ownDomainId;

	private String attribute;

	private String pattern;

	private String displayName;

	private String xpath;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getAttribute() {
		return attribute;
	}

	public void setAttribute(String attribute) {
		this.attribute = attribute;
	}

	public Integer getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public String getPattern() {
		return pattern;
	}

	public void setPattern(String pattern) {
		this.pattern = pattern;
	}

	public String getDisplayName() {
		return displayName;
	}

	public void setDisplayName(String displayName) {
		this.displayName = displayName;
	}

	public String getXpath() {
		return xpath;
	}

	public void setXpath(String xpath) {
		this.xpath = xpath;
	}

	@Override
	public String toString() {
		return "TargeturlCrawler [id=" + id + ", ownDomainId=" + ownDomainId + ", attribute=" + attribute + ", pattern=" + pattern + ", displayName=" + displayName
				+ ", xpath=" + xpath + "]";
	}

}
