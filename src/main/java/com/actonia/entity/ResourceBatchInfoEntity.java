package com.actonia.entity;

import lombok.Data;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

@Data
public class ResourceBatchInfoEntity {

	/**
	 * kw:  10000 ~ 11999
	 * url: 12000 ~ 12999
	 * tag: 13000 ~ 13999
	 * <p>
	 * other: 14000 ~ 15999
	 * <p>
	 * special:
	 * keyword list: 16000 ~ 16010
	 */
	public static final int OPERATION_TYPE_BATCH_KW_AND_URL_MIN = 10000;
	public static final int OPERATION_TYPE_BATCH_KW_AND_URL_MAX = 13999;

	//--------------------------------------------------- URL ---------------------------------------------------------
	public static final int OPERATION_TYPE_BATCH_URL_MIN = 12000;
	public static final int OPERATION_TYPE_BATCH_URL_MAX = 12999;

	/**--------------------------------------- pure url ------------------------------------------**/
	/**
	 * info:
	 * customFlag: customFlag: crawl status (enable/disable)
	 * detail:
	 * resourceMain: url
	 */
	public static final int OPERATION_TYPE_BATCH_ADD_TARGET_URL = 12010;
	/**
	 * info:
	 * customFlag: migrate keyword and tag relations
	 * detail:
	 * resourceId: urlId
	 * resourceMain: url
	 * resourceSubordinate: urlMurmur3_64Hash(table: customUrlMurmur3Hash)
	 */
	public static final int OPERATION_TYPE_BATCH_DELETE_TARGET_URL = 12011;

	/**--------------------------------------- url + tag ------------------------------------------**/
	/**
	 * info:
	 * customFlag: customFlag: crawl status (enable/disable)
	 * detail:
	 * resourceMain: url
	 * resourceSubordinate: tagName
	 * resourceAdditional: tagFlag
	 */
	public static final int OPERATION_TYPE_BATCH_ADD_TARGET_URL_TAG = 12020;
	/**
	 * resourceMain: url
	 * resourceId: tagId
	 */
	public static final int OPERATION_TYPE_BATCH_ADD_TARGET_URL_TAG_ID = 12023;
	/**
	 * info:
	 * customFlag: crawl status (enable/disable)
	 * detail:
	 * resourceMain: url
	 * resourceSubordinate: tagName
	 * resourceAdditional: tagFlag
	 */
	public static final int OPERATION_TYPE_BATCH_ADD_TARGET_URL_OPTIONAL_TAG = 12030;

	/**
	 * info:
	 * customFlag:  migrate keyword and tag relations
	 * detail:
	 * resourceId: tagId
	 */
	public static final int OPERATION_TYPE_BATCH_DELETE_TARGET_URL_BY_TAG_ID = 12024;// delete url and url rel

	/**
	 * resourceId: urlId
	 * resourceSubordinate: tagName
	 */
	public static final int OPERATION_TYPE_BATCH_ASSOCIATE_TARGET_URL_TAG_REL = 12027;

	/**
	 * resourceId: tagId
	 * resourceSubId: maxRelationId(optional)
	 */
	public static final int OPERATION_TYPE_BATCH_DISASSOCIATE_URL_TAG_REL_BY_TAG_ID = 12025; //delete url rel only

	/**
	 * resourceId: urlId
	 * resourceSubId: tagId
	 */
	public static final int OPERATION_TYPE_BATCH_DISASSOCIATE_URL_TAG_REL_BY_URL_ID_AND_TAG_ID = 12021;

	/**
	 * resourceId: urlId
	 */
	public static final int OPERATION_TYPE_BATCH_DISASSOCIATE_URL_TAG_REL_BY_URL_ID = 12022;

	/**
	 * resourceId: tagId
	 * resourceSubId: crawl status
	 * STATUS_CRAWL_DISABLE = 1
	 * STATUS_CRAWL_ENABLE = 0 (default val)
	 */
	public static final int OPERATION_TYPE_BATCH_UPDATE_URL_CRAWL_STATUS_BY_TAG_ID = 12026;

	/**---------------------------------------parentUrl childUrl rel------------------------------------------**/
	/**
	 * info:
	 * customFlag: carry over the keyword and page tag associations to the new parent URL
	 * 0: not all
	 * 1: tag only
	 * 2: associate kw only
	 * 3: both
	 * detail:
	 * resourceMain: childUrl
	 * resourceSubordinate: parentUrl
	 */
	public static final int OPERATION_TYPE_BATCH_ADD_URL_PARENT_CHILD_REL = 12040;

	/**
	 * info:
	 * customFlag: carry over the keyword and page tag associations to the new parent URL
	 * 0: not all
	 * 1: tag only
	 * 2: associate kw only
	 * 3: both
	 * resourceMain: childUrl
	 * resourceSubordinate: parentUrl
	 */
	public static final int OPERATION_TYPE_BATCH_ADD_URL_PARENT_CHILD_REL_V2 = 12045;

	/**
	 * info:
	 * customFlag
	 * detail:
	 * resourceId: childId
	 * resourceSubId: parentId
	 */
	public static final int OPERATION_TYPE_BATCH_DELETE_PAGE_ID_PARENT_CHILD_REL = 12041;

	/**
	 * info:
	 * customFlag
	 * detail:
	 * resourceMain: childUrl
	 * resourceSubordinate: parentUrl
	 */
	public static final int OPERATION_TYPE_BATCH_DELETE_PAGE_URL_PARENT_CHILD_REL = 12042;

	/**
	 * info:
	 * customFlag
	 * detail:
	 * resourceMain: child urlMurmur3_64Hash(table: customUrlMurmur3Hash)
	 * resourceSubordinate: parent urlMurmur3_64Hash(table: customUrlMurmur3Hash)
	 */
	public static final int OPERATION_TYPE_BATCH_DELETE_PAGE_MURMURHASH_PARENT_CHILD_REL = 12043;

	/**
	 * Process for 765
	 * resourceMain: special url
	 */
	public static final int OPERATION_TYPE_BATCH_EXCISE_SPECIFIED_URL_FROM_REL = 12044;

	/**
	 * --------------------------------------- tag ------------------------------------------
	 **/
	public static final int OPERATION_TYPE_BATCH_TAG_MIN = 13000;
	public static final int OPERATION_TYPE_BATCH_TAG_MAX = 13999;

	/**
	 * resourceMain: tagName
	 * resourceSubordinate: tagType (1: urlTag; 2:keywordTag)
	 * resourceCategory: tagCategory
	 */
	public static final int OPERATION_TYPE_BATCH_ADD_TAG = 13010;

	/**
	 * info:
	 * customFlag
	 * NULL or 1:
	 * delete from UI;
	 * 2:
	 * from backend task
	 * note:
	 * We do not allow clients to delete dynamic tags on the UI side.
	 * If del dynamic tag(t_group_tag.dynamic_flg = 1) at scripts, please set customFlag values to `2`.
	 * dynamic_flg   0/null    -> customFlag: any
	 * dynamic_flg   1         -> customFlag: 2
	 * detail:
	 * resourceId: tagId
	 * resourceCategory: tagCategory
	 * 1: national
	 * 2: geo
	 * 3: national + geo
	 * null(default): national + geo
	 */
	public static final int OPERATION_TYPE_BATCH_DELETE_TAG_BY_ID = 13011;

	/**
	 * info:
	 * customFlag: NULL or 1: from UI; 2: from backend task( for delete Dynamic tag)
	 * detail:
	 * resourceId: tagId
	 * resourceSubordinate: new tagName
	 */
	public static final int OPERATION_TYPE_BATCH_UPDATE_TAG_NAME = 13012;

	/**
	 * info:
	 * customFlag: tagType(1:url; 2Keyword)
	 * detail:
	 * resourceMain: childTag
	 * resourceSubordinate: parentTag
	 */
	public static final int OPERATION_TYPE_BATCH_ADD_TAG_PARENT_CHILD_REL = 13013;

//    public static final int OPERATION_TYPE_BATCH_ADD_CITY = 20160;
//    public static final int OPERATION_TYPE_BATCH_DELETE_CITY = 20160;

	//-------------------------------------------------- KEYWORD --------------------------------------------------------
	public static final int OPERATION_TYPE_BATCH_KW_MIN = 10000;
	public static final int OPERATION_TYPE_BATCH_KW_MAX = 11999;

	/**--------------------------------------- keyword ------------------------------------------**/
	//only kw
	/**
	 * info:
	 * engineLanguageDevice: search engines and search engine language and device, split with `-`, eg: 1-1-d,1-1-m
	 * detail:
	 * resourceMain: keywordName (raw keyword name,  please don't encode!!)
	 * resourceSearchengines(detail): engine-language-device
	 */
	public static final int OPERATION_TYPE_BATCH_ADD_KEYWORD = 10111;
	/**
	 * 注意:
	 * 1. 同一批 detail中的 keywordId值不可以重复. 不同的 keywordId可以对应不同的 Ses和 CityIds
	 * 2. 对于支持 diffSes的 domain, 如果提供了 Ses, 那么将按照提供的 Ses删除数据, 如果未提供, 将删除全部数据.
	 * 3. 对于不支持 diffSes的 domain, 如果提供了 Ses, 程序将其判定为无效数据, 并删除全部 Ses的数据.
	 * 4. 如果客户提供了 cityId, 那么将删除对应的 city关联数据, 如果未提供, 将删除全部数据
	 * 5. 如果 resCategory值为 national. 那么客户提供的 cityId无效
	 * <p>
	 * resourceId: keywordId
	 * resourceSearchengines: search engines and search engine language and device, split with `-`, eg: 1-1-d,1-1-m / NULL: all
	 * resourceCategory(keywordType):
	 * val = 1:del national
	 * resourceAdditional: invalid
	 * val = 2: del geo
	 * resourceAdditional: ["cityId1","cityId2"]: delete from cityId1,cityId2  / NULL: delete from all cities
	 * val = 3: del national+geo
	 * resourceAdditional: ["cityId1","cityId2"]: delete form all national + delete from cityId1,cityId2
	 * resourceAdditional: NULL: delete form all national + delete from all cities
	 */
	public static final int OPERATION_TYPE_BATCH_DELETE_KEYWORD = 10112;

	/**--------------------------------------- keyword and tag ------------------------------------------**/
	//kw and tag
	/**
	 * info:
	 * engineLanguageDevice: search engines and search engine language and device, split with `-`, eg: 1-1-d,1-1-m
	 * customFlag: create by  0:ui/1:qbs/2:scripts/3:ai
	 * detail:
	 * resourceMain: keywordName (raw keyword name,  please don't encode!!)
	 * resourceSubordinate: tagName
	 * resourceSearchengines(detail): engine-language-device
	 * resourceId: content fusion flag
	 */
	public static final int OPERATION_TYPE_BATCH_ADD_KEYWORD_TAG = 10121;

	/**
	 * info:
	 * engineLanguageDevice: search engines and search engine language and device, split with `-`, eg: 1-1-d,1-1-m
	 * customFlag: create by  0:ui/1:qbs/2:scripts/3:ai
	 * detail:
	 * resourceMain: keywordName (raw keyword name,  please don't encode!!)
	 * resourceId: tagId
	 * resourceSearchengines(detail): engine-language-device
	 */
	public static final int OPERATION_TYPE_BATCH_ADD_KEYWORD_TAG_ID = 10122;

	/**
	 * info:
	 * engineLanguageDevice: search engines and search engine language and device, split with `-`, eg: 1-1-d,1-1-m
	 * customFlag: create by  0:ui/1:qbs/2:scripts/3:ai
	 * detail:
	 * resourceMain: keywordName (raw keyword name,  please don't encode!!)
	 * resourceSubordinate: tagName
	 * resourceSearchengines(detail): engine-language-device
	 */
	public static final int OPERATION_TYPE_BATCH_ADD_KEYWORD_OPTIONAL_TAG = 10123;

	/**
	 * info:
	 * customFlag: create by  0:ui/1:qbs/2:scripts/3:ai
	 * detail:
	 * resourceMain: raw_keyword_name (raw keyword name,  please don't encode!!)
	 * resourceSubordinate: cdb_keyword_hash
	 * resourceId: t_keyword_id
	 * resourceSubId: tagId
	 * resourceAdditional: tagName
	 * resourceCategory: keyword/tag category, national(1) OR geo(2) OR national+geo(3)
	 * <p>
	 * Keyword: Please provide at least one keyword parameter.
	 * If you provide multiple parameters, cdb_keyword_hash is given priority, followed by raw_keyword_name.
	 * If multiple keyword entities are obtained using cdb_keyword_hash, they will be filtered using raw_keyword_name.
	 * <p>
	 * tag: Please provide at least one tag parameter.
	 * tagId is given priority, followed by tagName.
	 */
	public static final int OPERATION_TYPE_BATCH_ASSOCIATE_MANAGED_KEYWORD_TO_TAG = 10211;

	/**
	 * info:
	 * engineLanguageDevice: search engines and search engine language and device, split with `-`, eg: 1-1-d,1-1-m
	 * customFlag: create by  0:ui/1:qbs/2:scripts/3:ai
	 * detail:
	 * resourceMain: keywordName (raw keyword name,  please don't encode!!)
	 * resourceId: tagId
	 * resourceSearchengines(detail): engine-language-device
	 */
	public static final int OPERATION_TYPE_BATCH_ASSIGN_POWER_TOOLS_KEYWORD_TAG_REL_BY_KEYWORD_ID_AND_TAG_ID = 10124;//todo

	/**
	 * resourceId: keywordId
	 * resourceSubId: tagId
	 * resourceCategory: keyword relation type (national / geo / all)
	 * ResourceBatchDetailEntity: RESOURCE_CATEGORY_NATIONAL_KEYWORD/RESOURCE_CATEGORY_GEO_KEYWORD/RESOURCE_CATEGORY_NATIONAL_GEO_KEYWORD
	 */
	public static final int OPERATION_TYPE_BATCH_DISASSOCIATE_KEYWORD_TAG_REL_BY_KEYWORD_ID_AND_TAG_ID = 10125; //RESOURCE_TYPE_DISASSOCIATE_KEYWORD_FROM_TAG

	/**
	 * resourceId: keywordId
	 * resourceSubId: tagId
	 * resourceCategory: keyword relation type (national / geo / all)
	 * ResourceBatchDetailEntity: RESOURCE_CATEGORY_NATIONAL_KEYWORD/RESOURCE_CATEGORY_GEO_KEYWORD/RESOURCE_CATEGORY_NATIONAL_GEO_KEYWORD
	 */
	public static final int OPERATION_TYPE_BATCH_DISASSOCIATE_POWER_TOOLS_KEYWORD_TAG_REL_BY_KEYWORD_ID_AND_TAG_ID = 10126; //RESOURCE_TYPE_DISASSOCIATE_KEYWORD_ID_FROM_TAG_ID_POWER_TOOLS

	/**
	 * resourceId: tagId
	 * resourceCategory: keyword tag relation type (national / geo / all)
	 * ResourceBatchDetailEntity: RESOURCE_CATEGORY_NATIONAL_KEYWORD/RESOURCE_CATEGORY_GEO_KEYWORD/RESOURCE_CATEGORY_NATIONAL_GEO_KEYWORD
	 */
	public static final int OPERATION_TYPE_BATCH_DISASSOCIATE_ALL_KEYWORD_TAG_REL_BY_TAG_ID = 10127;// RESOURCE_TYPE_DISASSOCIATE_ALL_KEYWORD_FROM_TAG

	/**
	 * resourceId: keywordId
	 * resourceCategory: keyword category
	 * ResourceBatchDetailEntity: RESOURCE_CATEGORY_NATIONAL_KEYWORD/RESOURCE_CATEGORY_GEO_KEYWORD/RESOURCE_CATEGORY_NATIONAL_GEO_KEYWORD
	 */
	public static final int OPERATION_TYPE_BATCH_DISASSOCIATE_ALL_KEYWORD_TAG_REL_BY_KEYWORD_ID = 10128; //RESOURCE_TYPE_DISASSOCIATE_KEYWORD_FROM_ALL_TAGS

	/**
	 * resourceId: tagId
	 * resourceSearchengines:  engie-language-device / NULL: all
	 * resourceCategory: keyword relation type (national / geo / all)
	 * resourceSubordinate:
	 * resourceCategory = 1: del national => invalid
	 * resourceCategory = 2: del geo => ['cityId1','cityId2'] / NULL: all
	 * resourceCategory = 3: del national+geo => ['cityId1','cityId2']
	 * <p>
	 * <p>
	 * eg:
	 * +----------+------------+-----------------------+------------------+---------------------+
	 * | id    | resourceId | resourceSearchengines | resourceCategory | resourceSubordinate |
	 * +----------+------------+-----------------------+------------------+---------------------+
	 * | 60016937 |  1378255 | 16-160-m       |        2 | ["309177","304523"] |
	 * | 60016936 |  1386342 | 2-5-d         |        2 | NULL        |
	 * | 60016935 |  6898161 | 11-12-m        |        1 | NULL        |
	 * | 60016934 |  6883943 | NULL         |       NULL | NULL        |
	 * +----------+------------+-----------------------+------------------+---------------------+
	 */
	public static final int OPERATION_TYPE_BATCH_DELETE_ALL_KEYWORD_BY_TAG_ID = 10129; //RESOURCE_TYPE_DELETE_ALL_KEYWORD_FROM_TAG

	/**
	 * delete desktop/mobile keyword from geo
	 * resourceMain: device (eg: 'd')
	 * note:
	 * only one detail under each info!
	 * devices d and m cannot coexist in the same info!
	 */
	public static final int OPERATION_TYPE_BATCH_DELETE_ALL_KEYWORD_BY_DEVICE_FROM_GEO = 10130;

	/**
	 * we need set engine value to info and detail!
	 * info:
	 * engineLanguageDevice: search engines and search engine language and device, split with `-`, eg: 1-1-d,1-1-m
	 * detail:
	 * resourceMain: keywordName (raw keyword name,  please don't encode!!)
	 * resourceSubordinate: tagName (optional)
	 * resourceAdditional: cityId
	 * resource_searchengines: search engines and search engine language and device, split with `-`, eg: 1-1-d,1-1-m
	 */
	public static final int OPERATION_TYPE_BATCH_ADD_KEYWORD_TAG_GEO = 10142;

	/**
	 * resourceId: keywordId
	 * resourceSubId: cityId
	 * resource_searchengines(detail, optional): search engines and search engine language and device, split with `-`, eg: 1-1-d,1-1-m
	 */
	public static final int OPERATION_TYPE_BATCH_DISASSOCIATE_KEYWORD_CITY_REL_BY_KID_AND_CITY_ID = 10143;

	//kw and city
	@Deprecated
	public static final int OPERATION_TYPE_BATCH_ADD_KEYWORD_CITY = 10144;

	/**--------------------------------------- keyword expand ------------------------------------------**/

	//kw translate
	/**
	 * info:
	 * customFlag
	 * 3: create from AI
	 * detail:
	 * resourceMain: keywordName
	 * resourceSubordinate: translateString
	 * resource_searchengines(detail): must provide diffSes(engine-language-device)
	 * resourceId: AI actions id
	 */
	public static final int OPERATION_TYPE_BATCH_ADD_KEYWORD_TRANSLATION = 10141;

	/**
	 * Keep managed keywords and only delete the translations.
	 * detail:
	 * resourceSubId: keywordId
	 */
	public static final int OPERATION_TYPE_BATCH_DELETE_KEYWORD_TRANSLATION = 10145;

	/**
	 * info:
	 * customFlag
	 * 3: create from AI
	 * engineLanguageDevice: engine list in the detail is combined, using comma separator. 1-1,2-3,
	 * detail:
	 * resourceMain: keywordName
	 * resourceSubordinate: translateString
	 * resource_searchengines(detail):
	 * if support diffSes:
	 * Need provide engine, 1-1
	 * Each detail stores a different field
	 * if not support diffSes:
	 * Need provide domain's all engine, 1-1
	 * Each detail stores a different field
	 * resourceId: AI actions id
	 */
	public static final int OPERATION_TYPE_BATCH_ADD_DIFFERENT_SEARCH_ENGINES_KEYWORD_TRANSLATION = 10146;

	/**
	 * Keep managed keywords and only delete the translations.
	 * detail:
	 * resource_searchengines: engine-language (1-1,1-8)
	 * resourceId: keywordId
	 * if support diffSes:
	 * if provide engine, delete by engine, keywordId
	 * if not provide engine, delete by keywordId
	 * if not support diffSes:
	 * delete by keywordId from all engines
	 */
	public static final int OPERATION_TYPE_BATCH_DELETE_KEYWORD_TRANSLATION_V2 = 10147;

	//kw api
	/**
	 * info:
	 * engineLanguageDevice: search engines and search engine language and device, split with `-`, eg: 1-1-d,1-1-m
	 * detail:
	 * resourceMain: keywordName (raw keyword name,  please don't encode!!)
	 * resourceSearchengines(detail): engine-language-device
	 */
	public static final int OPERATION_TYPE_BATCH_ADD_KEYWORD_API_CALL = 10151;

	/**
	 * info:
	 * engineLanguageDevice: search engines and search engine language and device, split with `-`, eg: 1-1-d,1-1-m
	 * Note: **** local rank rankcheck uses the engine in ownDomainEntity.
	 * detail:
	 * resourceMain: keywordName (raw keyword name,  please don't encode!!)
	 * resourceSearchengines(detail): engine-language-device
	 * resourceSubordinate: cityId(optional)
	 */
	public static final int OPERATION_TYPE_BATCH_ENABLE_LOCAL_RANK_KEYWORD = 10152;

	/**
	 * resourceId: keywordId
	 * resourceSubId: cityId(optional, if cityId is null, will del the data that city id is 0)
	 */
	public static final int OPERATION_TYPE_BATCH_DISABLE_LOCAL_RANK_BY_KEYWORD_ID_AND_CITY_ID = 10153;

	/**
	 * resourceId: cityId
	 */
	public static final int OPERATION_TYPE_BATCH_DISABLE_LOCAL_RANK_BY_CITY_ID = 10154;

	/**
	 * info:
	 * engineLanguageDevice: search engines and search engine language and device and frequency for visibility share keyword
	 * split with `-`, eg: 1-1-d-7,1-1-m-7
	 * detail:
	 * resourceMain: visibility share keyword, not managed keyword.
	 * resourceId: cityId (optional)
	 * resourceSearchengines(detail): engine-language-device
	 */
	public static final int OPERATION_TYPE_BATCH_ADD_VISIBILITY_SHARE_KEYWORD_FOR_DIFFERENT_SET = 10155;

	/**
	 * detail:
	 * resourceCategory and cityId
	 * resourceCategory = 1: national
	 * city id is invalid, will del all national data
	 * resourceCategory = 2: geo
	 * if provide city, will delete data associated with the city.
	 * else will del all geo data
	 * resourceCategory= 3 or null: national + geo
	 * for national： city id is invalid, will del all national data
	 * for geo： if provide city,  will delete data associated with the city, else del all geo data
	 * <p>
	 * <p>
	 * resourceId: keywordId
	 * resourceSubordinate: urlHash(lowers(keyword))
	 * note: if both resourceId and resourceSubordinate are provided simultaneously, we will prioritize the use of resourceId !!!!!
	 * resourceSubId: cityId (optional)
	 * resourceSearchengines(detail): engine-language-device
	 * resourceCategory(important): keyword relation type (national / geo / all)
	 */
	public static final int OPERATION_TYPE_BATCH_DEL_VISIBILITY_SHARE_KEYWORD_FOR_DIFFERENT_SET = 10157;

	/**
	 * info:
	 * customFlag:
	 * 1: All keyword (resourceId, resourceSearchengines are invalid)
	 * 2: Selected keyword (resourceMain is invalid)
	 * detail:
	 * if customFlag == 1(ALL KW)
	 * use
	 * * *          resourceMain: search engines and search engine language, split with `-`, eg: 1-1
	 * NOTE: each detail is only allowed to store one engine!!!
	 * <p>
	 * if customFlag == 2(Selected keyword)
	 * use
	 * * *          resourceId: keywordId
	 * * *          resourceSearchengines: search engines and search engine language, split with `-`, eg: 1-1,1-2
	 */
	public static final int OPERATION_TYPE_BATCH_RETRIEVE_CUSTOM_SEARCH_VOLUME = 10156;

	// google job
	/**
	 * detail:
	 * resourceId: keywordId
	 * resourceSubId: cityId
	 */
	@Deprecated
	public static final int OPERATION_TYPE_BATCH_ENABLE_GOOGLE_JOB_KEYWORD_FROM_KW_ID = 10171;

	/**
	 * If separate SEs are not enabled, only keywords the client selected from ri will be added to Google jobs
	 * When the client enabled separate SEs, then we can allow any keywords to be added into Google Job
	 * <p>
	 * info:
	 * engineLanguageDevice: search engines (119-1-d,119-1-m)
	 * detail:
	 * resourceMain: keywordName
	 * resourceSubId: cityId
	 * resourceSearchengines: resourceSearchengines
	 */
	public static final int OPERATION_TYPE_BATCH_ENABLE_GOOGLE_JOB_KEYWORD_FROM_KW_NAME = 10172;

	//待开发
	public static final int OPERATION_TYPE_BATCH_DISABLE_GOOGLE_JOB_KEYWORD = 10173;

	//待开发
	//engine language device
	public static final int OPERATION_TYPE_BATCH_DISASSOCIATE_KEYWORD_BY_ENGINE = 10181;//todo

	/**
	 * resourceSearchengines: search engines and search engine language and device, split with `-`, eg: 1-1-d,1-1-m
	 */
	public static final int OPERATION_TYPE_BATCH_DISABLE_SECONDARY_ENGINE = 10182;//todo


	/**--------------------------------------- keyword and url ------------------------------------------**/
	/**
	 * Only one field between resourceId and resourceMain can be set, and the same applies to resourceSubId and resourceSubordinate.
	 * resourceId: keywordId
	 * resourceMain: keywordName (raw keyword name,  please don't encode!!)
	 * resourceSubId: targetUrlId
	 * resourceSubordinate: targetUrl
	 * resourceSearchengines: resourceSearchengines
	 */
	public static final int OPERATION_TYPE_BATCH_ADD_KEYWORD_TARGET_URL = 10131;

	/**
	 * resourceId: keywordId
	 * resourceSubId: targetUrlId
	 */
	public static final int OPERATION_TYPE_BATCH_DISASSOCIATE_KEYWORD_URL_REL_BY_KEYWORD_ID_AND_URL_ID = 10132; //RESOURCE_TYPE_DISASSOCIATE_KEYWORD_FROM_TARGET_URL


	//kw competitor 待开发
	/**
	 * resourceSubordinate: url
	 */
	public static final int OPERATION_TYPE_BATCH_ADD_KEYWORD_COMPETITOR_URL = 10133;

	//待开发
	public static final int OPERATION_TYPE_BATCH_DISASSOCIATE_KEYWORD_COMPETITOR_URL_REL_BY_KID_AND_URL_ID = 10134;// RESOURCE_TYPE_DISASSOCIATE_KEYWORD_FROM_COMPETITOR_URL

	/**
	 * --------------------------------------- special ------------------------------------------
	 **/


	//-------------------------------------------------- KEYWORD LIST --------------------------------------------------------
	public static final int OPERATION_TYPE_BATCH_KW_LIST_MIN = 16000;
	public static final int OPERATION_TYPE_BATCH_KW_LIST_MAX = 16019;

	/**
	 * info:
	 * customFlag: append/rebuild => CdbListEntity.LIST_APPEND/CdbListEntity.LIST_REBUILD
	 * engineLanguageDevice: cdb list name
	 * detail:
	 * resourceMain: keywordString
	 * note:
	 * trigger by workers
	 */
	public static final int OPERATION_TYPE_BATCH_ADD_KEYWORD_LIST = 16001;

	/**
	 * note: trigger by workers
	 * resourceId: cdb list id
	 */
	public static final int OPERATION_TYPE_BATCH_DELETE_KEYWORD_LIST_BY_ID = 16002;
	/**
	 * note: trigger by workers
	 * resourceId: cdb list detail id
	 */
	public static final int OPERATION_TYPE_BATCH_DELETE_KEYWORD_LIST_DETAIL_BY_ID = 16003;

	/**
	 * note: trigger by workers
	 * resourceMain: new cdb list name
	 * resourceId: existing cdb list id
	 */
	public static final int OPERATION_TYPE_BATCH_EDIT_KEYWORD_LIST_NAME_BY_ID = 16004;

	//-------------------------------------------------- URL LIST --------------------------------------------------------
	public static final int OPERATION_TYPE_BATCH_URL_LIST_MIN = 16020;
	public static final int OPERATION_TYPE_BATCH_URL_LIST_MAX = 16039;

	/**
	 * info:
	 * engineLanguageDevice: url list name
	 * customFlag: (if list not exist, will create a new list)
	 * 1 or null: append
	 * 2: re-build
	 * detail:
	 * resourceSubordinate: url
	 */
	public static final int OPERATION_TYPE_BATCH_ADD_URL_LIST = 16021;

	/**
	 * info:
	 * engineLanguageDevice: url list name
	 * customFlag: (if list not exist, will create a new list)
	 * 1 or null: append
	 * 2: re-build
	 * detail:
	 * resourceSubordinate: ftp file path
	 */
	public static final int OPERATION_TYPE_BATCH_ADD_URL_LIST_FROM_FILE = 16022;

	/**
	 * resourceId: list id
	 */
	public static final int OPERATION_TYPE_BATCH_DELETE_URL_LIST_BY_ID = 16023;


	public static final Set<Integer> add_keywordOperationSet = new HashSet<>();
	public static final Set<Integer> del_keywordOperationSet = new HashSet<>();
	public static final Set<Integer> urlOperationSet = new HashSet<>();
	public static final Set<Integer> add_tagOperationSet = new HashSet<>();
	public static final Set<Integer> del_tagOperationSet = new HashSet<>();
	public static final Set<Integer> upd_tagOperationSet = new HashSet<>();
	public static final Set<Integer> add_keywordTagRelOperationSet = new HashSet<>();
	public static final Set<Integer> del_keywordTagRelOperationSet = new HashSet<>();
	public static final Set<Integer> urlTagOperationSet = new HashSet<>();
	public static final Set<Integer> urlTagRelOperationSet = new HashSet<>();
	public static final Set<Integer> keywordUrlRelOperationSet = new HashSet<>();

	public static final Set<Integer> allOperationSet = new HashSet<>();
	public static final Set<Integer> v2TagMatchRuleRelOperationSet = new HashSet<>();

	public static final Set<Integer> searchengine_in_detail_table = new HashSet<>();
	public static final Set<Integer> subscription_type_set = new HashSet<>();
	public static final Set<Integer> subscription_geo_related_set = new HashSet<>();

	public static final Set<Integer> add_keywordListOperationSet = new HashSet<>();
	public static final Set<Integer> del_keywordListOperationSet = new HashSet<>();
	public static final Set<Integer> edit_keywordListOperationSet = new HashSet<>();

	public static final Set<Integer> add_vsKeywordListOperationSet = new HashSet<>();
	public static final Set<Integer> del_vsKeywordListOperationSet = new HashSet<>();

	static {
		add_keywordOperationSet.add(OPERATION_TYPE_BATCH_ADD_KEYWORD);
		add_keywordOperationSet.add(OPERATION_TYPE_BATCH_ADD_KEYWORD_CITY);
		add_keywordOperationSet.add(OPERATION_TYPE_BATCH_ADD_KEYWORD_TAG_GEO);
		add_keywordOperationSet.add(OPERATION_TYPE_BATCH_ADD_KEYWORD_API_CALL);
		add_keywordOperationSet.add(OPERATION_TYPE_BATCH_ENABLE_LOCAL_RANK_KEYWORD);
		add_keywordOperationSet.add(OPERATION_TYPE_BATCH_ENABLE_GOOGLE_JOB_KEYWORD_FROM_KW_ID);
		add_keywordOperationSet.add(OPERATION_TYPE_BATCH_ENABLE_GOOGLE_JOB_KEYWORD_FROM_KW_NAME);
		add_keywordOperationSet.add(OPERATION_TYPE_BATCH_RETRIEVE_CUSTOM_SEARCH_VOLUME);
	}

	static {
		searchengine_in_detail_table.add(OPERATION_TYPE_BATCH_ADD_KEYWORD_TARGET_URL);
	}

	static {
		del_keywordOperationSet.add(OPERATION_TYPE_BATCH_DELETE_KEYWORD);
		del_keywordOperationSet.add(OPERATION_TYPE_BATCH_DISASSOCIATE_KEYWORD_CITY_REL_BY_KID_AND_CITY_ID);
		del_keywordOperationSet.add(OPERATION_TYPE_BATCH_DISASSOCIATE_KEYWORD_COMPETITOR_URL_REL_BY_KID_AND_URL_ID);
		del_keywordOperationSet.add(OPERATION_TYPE_BATCH_DISASSOCIATE_KEYWORD_BY_ENGINE);
		del_keywordOperationSet.add(OPERATION_TYPE_BATCH_DELETE_ALL_KEYWORD_BY_DEVICE_FROM_GEO);
		del_keywordOperationSet.add(OPERATION_TYPE_BATCH_DISABLE_SECONDARY_ENGINE);
		del_keywordOperationSet.add(OPERATION_TYPE_BATCH_DISABLE_GOOGLE_JOB_KEYWORD);
		del_keywordOperationSet.add(OPERATION_TYPE_BATCH_DELETE_KEYWORD_TRANSLATION);
		del_keywordOperationSet.add(OPERATION_TYPE_BATCH_DISABLE_LOCAL_RANK_BY_KEYWORD_ID_AND_CITY_ID);
		del_keywordOperationSet.add(OPERATION_TYPE_BATCH_DISABLE_LOCAL_RANK_BY_CITY_ID);
	}

	static {
		urlOperationSet.add(OPERATION_TYPE_BATCH_ADD_TARGET_URL);
		urlOperationSet.add(OPERATION_TYPE_BATCH_DELETE_TARGET_URL);

		urlOperationSet.add(OPERATION_TYPE_BATCH_ADD_URL_PARENT_CHILD_REL);
		urlOperationSet.add(OPERATION_TYPE_BATCH_ADD_URL_PARENT_CHILD_REL_V2);
		urlOperationSet.add(OPERATION_TYPE_BATCH_DELETE_PAGE_ID_PARENT_CHILD_REL);
		urlOperationSet.add(OPERATION_TYPE_BATCH_DELETE_PAGE_URL_PARENT_CHILD_REL);
		urlOperationSet.add(OPERATION_TYPE_BATCH_DELETE_PAGE_MURMURHASH_PARENT_CHILD_REL);
	}

	static {
		add_tagOperationSet.add(OPERATION_TYPE_BATCH_ADD_TAG);
		del_tagOperationSet.add(OPERATION_TYPE_BATCH_DELETE_TAG_BY_ID);
		upd_tagOperationSet.add(OPERATION_TYPE_BATCH_UPDATE_TAG_NAME);
	}

	static {
		add_keywordTagRelOperationSet.add(OPERATION_TYPE_BATCH_ADD_KEYWORD_TAG);
		add_keywordTagRelOperationSet.add(OPERATION_TYPE_BATCH_ADD_KEYWORD_TAG_ID);
		add_keywordTagRelOperationSet.add(OPERATION_TYPE_BATCH_ADD_KEYWORD_OPTIONAL_TAG);
		add_keywordTagRelOperationSet.add(OPERATION_TYPE_BATCH_ASSIGN_POWER_TOOLS_KEYWORD_TAG_REL_BY_KEYWORD_ID_AND_TAG_ID);
	}

	static {
		del_keywordTagRelOperationSet.add(OPERATION_TYPE_BATCH_DISASSOCIATE_KEYWORD_TAG_REL_BY_KEYWORD_ID_AND_TAG_ID);
		del_keywordTagRelOperationSet.add(OPERATION_TYPE_BATCH_DISASSOCIATE_POWER_TOOLS_KEYWORD_TAG_REL_BY_KEYWORD_ID_AND_TAG_ID);
		del_keywordTagRelOperationSet.add(OPERATION_TYPE_BATCH_DISASSOCIATE_ALL_KEYWORD_TAG_REL_BY_TAG_ID);
		del_keywordTagRelOperationSet.add(OPERATION_TYPE_BATCH_DISASSOCIATE_ALL_KEYWORD_TAG_REL_BY_KEYWORD_ID);
		del_keywordTagRelOperationSet.add(OPERATION_TYPE_BATCH_DELETE_ALL_KEYWORD_BY_TAG_ID);
	}

	static {
		urlTagOperationSet.add(OPERATION_TYPE_BATCH_ADD_TARGET_URL_TAG);
		urlTagOperationSet.add(OPERATION_TYPE_BATCH_ADD_TARGET_URL_TAG_ID);
		urlTagOperationSet.add(OPERATION_TYPE_BATCH_ADD_TARGET_URL_OPTIONAL_TAG);
		urlTagOperationSet.add(OPERATION_TYPE_BATCH_DELETE_TARGET_URL_BY_TAG_ID);
		urlTagRelOperationSet.add(OPERATION_TYPE_BATCH_DISASSOCIATE_URL_TAG_REL_BY_TAG_ID);
		urlTagRelOperationSet.add(OPERATION_TYPE_BATCH_DISASSOCIATE_URL_TAG_REL_BY_URL_ID_AND_TAG_ID);
		urlTagRelOperationSet.add(OPERATION_TYPE_BATCH_DISASSOCIATE_URL_TAG_REL_BY_URL_ID);
		urlTagRelOperationSet.add(OPERATION_TYPE_BATCH_ASSOCIATE_TARGET_URL_TAG_REL);
	}

	static {
		keywordUrlRelOperationSet.add(OPERATION_TYPE_BATCH_ADD_KEYWORD_TARGET_URL);
		add_keywordOperationSet.add(OPERATION_TYPE_BATCH_ADD_KEYWORD_COMPETITOR_URL);
		keywordUrlRelOperationSet.add(OPERATION_TYPE_BATCH_DISASSOCIATE_KEYWORD_URL_REL_BY_KEYWORD_ID_AND_URL_ID);
	}

	static {
		allOperationSet.addAll(add_keywordOperationSet);
		allOperationSet.addAll(del_keywordOperationSet);
		allOperationSet.addAll(urlOperationSet);
		allOperationSet.addAll(add_tagOperationSet);
		allOperationSet.addAll(del_tagOperationSet);
		allOperationSet.addAll(upd_tagOperationSet);
		allOperationSet.addAll(add_keywordTagRelOperationSet);
		allOperationSet.addAll(del_keywordTagRelOperationSet);
		allOperationSet.addAll(urlTagOperationSet);
		allOperationSet.addAll(urlTagRelOperationSet);
		allOperationSet.addAll(keywordUrlRelOperationSet);

	}

	static {
		v2TagMatchRuleRelOperationSet.addAll(add_keywordOperationSet);
		v2TagMatchRuleRelOperationSet.addAll(add_keywordTagRelOperationSet);
		v2TagMatchRuleRelOperationSet.addAll(keywordUrlRelOperationSet);
		v2TagMatchRuleRelOperationSet.addAll(urlOperationSet);
		v2TagMatchRuleRelOperationSet.addAll(urlTagOperationSet);
	}

	static {
		subscription_geo_related_set.add(OPERATION_TYPE_BATCH_ADD_KEYWORD_TAG_GEO);
		subscription_geo_related_set.add(OPERATION_TYPE_BATCH_ADD_KEYWORD_CITY);
	}

	static {
		subscription_type_set.addAll(add_keywordOperationSet);
		subscription_type_set.addAll(add_keywordTagRelOperationSet);
		subscription_type_set.add(OPERATION_TYPE_BATCH_ADD_KEYWORD_TARGET_URL);
	}

	static {
		add_keywordListOperationSet.add(OPERATION_TYPE_BATCH_ADD_KEYWORD_LIST);
		del_keywordListOperationSet.add(OPERATION_TYPE_BATCH_DELETE_KEYWORD_LIST_BY_ID);
		del_keywordListOperationSet.add(OPERATION_TYPE_BATCH_DELETE_KEYWORD_LIST_DETAIL_BY_ID);
		edit_keywordListOperationSet.add(OPERATION_TYPE_BATCH_EDIT_KEYWORD_LIST_NAME_BY_ID);
	}

	static {
		add_vsKeywordListOperationSet.add(OPERATION_TYPE_BATCH_ADD_VISIBILITY_SHARE_KEYWORD_FOR_DIFFERENT_SET);
		del_vsKeywordListOperationSet.add(OPERATION_TYPE_BATCH_DEL_VISIBILITY_SHARE_KEYWORD_FOR_DIFFERENT_SET);
	}


	/**
	 * todo: 完成 queueBaseV2之后需要对此方法瘦身优化,合并相同返回值的类型
	 * <p>
	 * 返回一个互斥的操作类型集, QBase会在执行当前任务时寻找是否存在互斥的未完成任务.
	 *
	 * @param operationType
	 * @return
	 */
	public static Set<Integer> getOperationSet(int operationType) {
		Set<Integer> set = new HashSet<>();
		if (operationType == OPERATION_TYPE_BATCH_ADD_TARGET_URL) {
			set.addAll(urlOperationSet);
			set.addAll(urlTagOperationSet);
			set.addAll(keywordUrlRelOperationSet);
		} else if (operationType == OPERATION_TYPE_BATCH_DELETE_TARGET_URL) {
			set.addAll(urlOperationSet);
			set.addAll(urlTagOperationSet);
			set.addAll(keywordUrlRelOperationSet);
		} else if (operationType == OPERATION_TYPE_BATCH_ADD_TARGET_URL_TAG) {
			set.addAll(urlOperationSet);
			set.addAll(urlTagRelOperationSet);
			set.addAll(keywordUrlRelOperationSet);

			set.addAll(del_tagOperationSet);
			set.addAll(upd_tagOperationSet);
		} else if (operationType == OPERATION_TYPE_BATCH_ADD_TARGET_URL_TAG_ID) {
			set.addAll(urlOperationSet);
			set.addAll(urlTagRelOperationSet);
			set.addAll(keywordUrlRelOperationSet);

			set.addAll(del_tagOperationSet);
		} else if (operationType == OPERATION_TYPE_BATCH_ADD_TARGET_URL_OPTIONAL_TAG) {
			set.addAll(urlOperationSet);
			set.addAll(urlTagRelOperationSet);
			set.addAll(keywordUrlRelOperationSet);

			set.addAll(del_tagOperationSet);
			set.addAll(upd_tagOperationSet);
		} else if (operationType == OPERATION_TYPE_BATCH_DELETE_TARGET_URL_BY_TAG_ID) {
			set.addAll(urlOperationSet);
			set.addAll(urlTagRelOperationSet);
			set.addAll(keywordUrlRelOperationSet);
			set.addAll(del_tagOperationSet);
		} else if (operationType == OPERATION_TYPE_BATCH_DISASSOCIATE_URL_TAG_REL_BY_TAG_ID) {
			set.addAll(urlOperationSet);//父子关系,删除url
			set.addAll(urlTagOperationSet);
			set.addAll(urlTagRelOperationSet);
		} else if (operationType == OPERATION_TYPE_BATCH_DISASSOCIATE_URL_TAG_REL_BY_URL_ID_AND_TAG_ID) {
			set.addAll(urlOperationSet);//父子关系,删除url
			set.addAll(urlTagOperationSet);
			set.addAll(urlTagRelOperationSet);
		} else if (operationType == OPERATION_TYPE_BATCH_DISASSOCIATE_URL_TAG_REL_BY_URL_ID) {
			set.addAll(urlOperationSet);//父子关系,删除url
			set.addAll(urlTagOperationSet);
			set.addAll(urlTagRelOperationSet);
		} else if (operationType == OPERATION_TYPE_BATCH_ADD_URL_PARENT_CHILD_REL) {
			set.addAll(urlOperationSet);
			set.addAll(urlTagOperationSet);
			set.addAll(urlTagRelOperationSet);
			set.addAll(keywordUrlRelOperationSet);
		} else if (operationType == OPERATION_TYPE_BATCH_ADD_URL_PARENT_CHILD_REL_V2) {
			set.addAll(urlOperationSet);
			set.addAll(urlTagOperationSet);
			set.addAll(urlTagRelOperationSet);
			set.addAll(keywordUrlRelOperationSet);
		} else if (operationType == OPERATION_TYPE_BATCH_DELETE_PAGE_MURMURHASH_PARENT_CHILD_REL) {
			set.addAll(urlOperationSet);
			set.addAll(urlTagOperationSet);
			set.addAll(urlTagRelOperationSet);
			set.addAll(keywordUrlRelOperationSet);
		} else if (operationType == OPERATION_TYPE_BATCH_DELETE_PAGE_ID_PARENT_CHILD_REL) {
			set.addAll(urlOperationSet);
			set.addAll(urlTagOperationSet);
			set.addAll(urlTagRelOperationSet);
			set.addAll(keywordUrlRelOperationSet);
		} else if (operationType == OPERATION_TYPE_BATCH_DELETE_PAGE_URL_PARENT_CHILD_REL) {
			set.addAll(urlOperationSet);
			set.addAll(urlTagOperationSet);
			set.addAll(urlTagRelOperationSet);
			set.addAll(keywordUrlRelOperationSet);
		} else if (operationType == OPERATION_TYPE_BATCH_ADD_TAG) {
			set.addAll(del_tagOperationSet);
			set.addAll(upd_tagOperationSet);
			set.addAll(urlTagRelOperationSet);
		} else if (operationType == OPERATION_TYPE_BATCH_DELETE_TAG_BY_ID) {
			set.addAll(add_tagOperationSet);
			set.addAll(urlTagOperationSet);
			set.addAll(urlTagRelOperationSet);
			set.addAll(add_keywordTagRelOperationSet);
			set.addAll(del_keywordTagRelOperationSet);
			set.addAll(upd_tagOperationSet);
		} else if (operationType == OPERATION_TYPE_BATCH_UPDATE_TAG_NAME) {
			set.addAll(add_tagOperationSet);
			set.addAll(del_tagOperationSet);
			set.addAll(upd_tagOperationSet);
		} else if (operationType == OPERATION_TYPE_BATCH_ADD_TAG_PARENT_CHILD_REL) {
			set.addAll(del_tagOperationSet);
		} else if (operationType == OPERATION_TYPE_BATCH_ADD_KEYWORD
				|| operationType == OPERATION_TYPE_BATCH_ADD_KEYWORD_TRANSLATION
				|| operationType == OPERATION_TYPE_BATCH_ADD_KEYWORD_API_CALL
				|| operationType == OPERATION_TYPE_BATCH_ENABLE_LOCAL_RANK_KEYWORD
				|| operationType == OPERATION_TYPE_BATCH_ENABLE_GOOGLE_JOB_KEYWORD_FROM_KW_ID
				|| operationType == OPERATION_TYPE_BATCH_ENABLE_GOOGLE_JOB_KEYWORD_FROM_KW_NAME) {
			set.addAll(add_keywordOperationSet);
			set.addAll(del_keywordOperationSet);
			set.addAll(add_keywordTagRelOperationSet);
			set.addAll(del_keywordTagRelOperationSet);
			set.addAll(keywordUrlRelOperationSet);
		} else if (operationType == OPERATION_TYPE_BATCH_ADD_DIFFERENT_SEARCH_ENGINES_KEYWORD_TRANSLATION) {
			set.add(OPERATION_TYPE_BATCH_DELETE_KEYWORD_TRANSLATION);
		} else if (operationType == OPERATION_TYPE_BATCH_DELETE_KEYWORD
				|| operationType == OPERATION_TYPE_BATCH_DELETE_ALL_KEYWORD_BY_DEVICE_FROM_GEO
				|| operationType == OPERATION_TYPE_BATCH_DISABLE_SECONDARY_ENGINE
				|| operationType == OPERATION_TYPE_BATCH_DISABLE_GOOGLE_JOB_KEYWORD
				|| operationType == OPERATION_TYPE_BATCH_DELETE_KEYWORD_TRANSLATION) {
			set.addAll(add_keywordOperationSet);
			set.addAll(del_keywordOperationSet);
			set.addAll(add_keywordTagRelOperationSet);
			set.addAll(del_keywordTagRelOperationSet);
			set.addAll(keywordUrlRelOperationSet);
			set.add(OPERATION_TYPE_BATCH_ADD_DIFFERENT_SEARCH_ENGINES_KEYWORD_TRANSLATION);
		} else if (operationType == OPERATION_TYPE_BATCH_ADD_KEYWORD_TAG) {
			set.addAll(add_keywordOperationSet);
			set.addAll(del_keywordOperationSet);
			set.addAll(del_tagOperationSet);
			set.addAll(add_keywordTagRelOperationSet);
			set.addAll(del_keywordTagRelOperationSet);
			set.addAll(keywordUrlRelOperationSet);
		} else if (operationType == OPERATION_TYPE_BATCH_ADD_KEYWORD_TAG_ID) {
			set.addAll(add_keywordOperationSet);
			set.addAll(del_keywordOperationSet);
			set.addAll(del_tagOperationSet);
			set.addAll(add_keywordTagRelOperationSet);
			set.addAll(del_keywordTagRelOperationSet);
			set.addAll(keywordUrlRelOperationSet);
		} else if (operationType == OPERATION_TYPE_BATCH_ADD_KEYWORD_OPTIONAL_TAG) {
			set.addAll(add_keywordOperationSet);
			set.addAll(del_keywordOperationSet);
			set.addAll(del_tagOperationSet);
			set.addAll(add_keywordTagRelOperationSet);
			set.addAll(del_keywordTagRelOperationSet);
			set.addAll(keywordUrlRelOperationSet);
		} else if (operationType == OPERATION_TYPE_BATCH_DISASSOCIATE_KEYWORD_TAG_REL_BY_KEYWORD_ID_AND_TAG_ID) {
			set.addAll(del_keywordOperationSet);
			set.addAll(del_tagOperationSet);
			set.addAll(add_keywordTagRelOperationSet);
			set.addAll(del_keywordTagRelOperationSet);
		} else if (operationType == OPERATION_TYPE_BATCH_ADD_KEYWORD_TARGET_URL) {
			set.addAll(add_keywordOperationSet);
			set.addAll(del_keywordOperationSet);
			set.addAll(add_keywordTagRelOperationSet);
			set.addAll(del_keywordTagRelOperationSet);
			set.addAll(urlOperationSet);
			set.addAll(urlTagOperationSet);
			set.addAll(urlTagRelOperationSet);
			set.addAll(del_keywordTagRelOperationSet);
		} else if (operationType == OPERATION_TYPE_BATCH_DISASSOCIATE_KEYWORD_URL_REL_BY_KEYWORD_ID_AND_URL_ID) {
			set.addAll(urlOperationSet);
			set.addAll(urlTagOperationSet);
			set.addAll(keywordUrlRelOperationSet);
		} else if (operationType == OPERATION_TYPE_BATCH_DISASSOCIATE_POWER_TOOLS_KEYWORD_TAG_REL_BY_KEYWORD_ID_AND_TAG_ID) {
			set.addAll(del_keywordOperationSet);
			set.addAll(del_tagOperationSet);
			set.addAll(add_keywordTagRelOperationSet);
			set.addAll(del_keywordTagRelOperationSet);
		} else if (operationType == OPERATION_TYPE_BATCH_DISASSOCIATE_ALL_KEYWORD_TAG_REL_BY_TAG_ID) {
			set.addAll(del_keywordOperationSet);
			set.addAll(del_tagOperationSet);
			set.addAll(add_keywordTagRelOperationSet);
			set.addAll(del_keywordTagRelOperationSet);
		} else if (operationType == OPERATION_TYPE_BATCH_DISASSOCIATE_ALL_KEYWORD_TAG_REL_BY_KEYWORD_ID) {
			set.addAll(del_keywordOperationSet);
			set.addAll(del_tagOperationSet);
			set.addAll(add_keywordTagRelOperationSet);
			set.addAll(del_keywordTagRelOperationSet);
		} else if (operationType == OPERATION_TYPE_BATCH_DELETE_ALL_KEYWORD_BY_TAG_ID) {
			set.addAll(add_keywordOperationSet);
			set.addAll(del_keywordOperationSet);
			set.addAll(add_keywordTagRelOperationSet);
			set.addAll(del_keywordTagRelOperationSet);
			set.addAll(keywordUrlRelOperationSet);
		} else if (operationType == OPERATION_TYPE_BATCH_ADD_KEYWORD_TAG_GEO) {
			set.addAll(add_keywordOperationSet);
			set.addAll(del_keywordOperationSet);
			set.addAll(add_keywordTagRelOperationSet);
			set.addAll(del_keywordTagRelOperationSet);
			set.addAll(keywordUrlRelOperationSet);
			set.addAll(del_tagOperationSet);
			set.addAll(upd_tagOperationSet);
		} else if (operationType == OPERATION_TYPE_BATCH_DISASSOCIATE_KEYWORD_CITY_REL_BY_KID_AND_CITY_ID) {
			set.addAll(add_keywordOperationSet);
			set.addAll(add_keywordTagRelOperationSet);
			set.addAll(keywordUrlRelOperationSet);
		} else if (operationType == OPERATION_TYPE_BATCH_ADD_KEYWORD_LIST) {
			set.addAll(del_keywordListOperationSet);
		} else if (operationType == OPERATION_TYPE_BATCH_DELETE_KEYWORD_LIST_BY_ID) {
			set.addAll(add_keywordListOperationSet);
		} else if (operationType == OPERATION_TYPE_BATCH_DELETE_KEYWORD_LIST_DETAIL_BY_ID) {
			set.addAll(add_keywordListOperationSet);
		} else if (operationType == OPERATION_TYPE_BATCH_EDIT_KEYWORD_LIST_NAME_BY_ID) {
			set.addAll(add_keywordListOperationSet);
			set.addAll(del_keywordListOperationSet);
			set.addAll(edit_keywordListOperationSet);
		} else if (operationType == OPERATION_TYPE_BATCH_ADD_VISIBILITY_SHARE_KEYWORD_FOR_DIFFERENT_SET) {
			set.addAll(del_vsKeywordListOperationSet);
		} else if (operationType == OPERATION_TYPE_BATCH_DEL_VISIBILITY_SHARE_KEYWORD_FOR_DIFFERENT_SET) {
			set.addAll(add_vsKeywordListOperationSet);
		} else if (operationType == OPERATION_TYPE_BATCH_ASSOCIATE_TARGET_URL_TAG_REL) {
			set.addAll(urlOperationSet);
		} else {
			return allOperationSet;
		}
		return set;
	}

	public static final Set<Integer> disassociateType = new HashSet<>();

	static {
		disassociateType.add(OPERATION_TYPE_BATCH_DISASSOCIATE_URL_TAG_REL_BY_TAG_ID);
		disassociateType.add(OPERATION_TYPE_BATCH_DISASSOCIATE_URL_TAG_REL_BY_URL_ID_AND_TAG_ID);
		disassociateType.add(OPERATION_TYPE_BATCH_DISASSOCIATE_URL_TAG_REL_BY_URL_ID);
		disassociateType.add(OPERATION_TYPE_BATCH_DISASSOCIATE_KEYWORD_TAG_REL_BY_KEYWORD_ID_AND_TAG_ID);
		disassociateType.add(OPERATION_TYPE_BATCH_DISASSOCIATE_POWER_TOOLS_KEYWORD_TAG_REL_BY_KEYWORD_ID_AND_TAG_ID);
		disassociateType.add(OPERATION_TYPE_BATCH_DISASSOCIATE_ALL_KEYWORD_TAG_REL_BY_TAG_ID);
		disassociateType.add(OPERATION_TYPE_BATCH_DISASSOCIATE_ALL_KEYWORD_TAG_REL_BY_KEYWORD_ID);
		disassociateType.add(OPERATION_TYPE_BATCH_DISASSOCIATE_KEYWORD_CITY_REL_BY_KID_AND_CITY_ID);
		disassociateType.add(OPERATION_TYPE_BATCH_DISASSOCIATE_KEYWORD_BY_ENGINE);
		disassociateType.add(OPERATION_TYPE_BATCH_DISASSOCIATE_KEYWORD_URL_REL_BY_KEYWORD_ID_AND_URL_ID);
		disassociateType.add(OPERATION_TYPE_BATCH_DISASSOCIATE_KEYWORD_COMPETITOR_URL_REL_BY_KID_AND_URL_ID);
	}


	public static final int STATUS_CREATED = 0;
	public static final int STATUS_PROCESSING = 1;
	public static final int STATUS_FINISH = 2;
	public static final int STATUS_ERROR = 3;
	public static final int STATUS_SEQ_OVER_LIMIT = 4;
	public static final int STATUS_SEQ_SUBSCRIPTION_OVER_LIMIT = 5;

	public static final int TYPE_ADD = 1;
	public static final int TYPE_DELETE = 0;
	public static final int TYPE_UPDATE = 2; // 20230905 for update url crawl status

	public static final int CREATE_TYPE_UI = 0;
	public static final int CREATE_TYPE_QBS = 1;
	public static final int CREATE_TYPE_SCRIPTS = 2;
	public static final int CREATE_TYPE_AI = 3; // for keyword translation

	public static final int RETRIEVE_CUSTOM_SV_ALL_KEYWORD = 1;
	public static final int RETRIEVE_CUSTOM_SV_SELECTED_KEYWORD = 2;

	private Long id;
	private Integer actionType;
	private Integer operationType;
	private Integer customFlag;
	private Integer ownDomainId;
	private Integer userId;
	private Integer status;
	private Integer statusSync;
	private Integer seq;
	private String serverInfo;
	private String engineLanguageDevice;
	private Date createDate;
	private Date processDate;
	private Date endDate;
	private int detailCount;

	/**
	 *
	 */
	private Integer attrExpand1;
	private String attrExpand2;

	/**
	 * for email
	 */
	private int successCnt;
	private int invalidCnt;
	private int errorCnt;
	private Date processCompleteDate;
	private String description;

	/**
	 * 调用方式
	 * 0: 默认 (前台)
	 * 1: 由其他queuebase任务创建
	 * 2: 脚本创建
	 */
	private int createType = CREATE_TYPE_UI;

}
