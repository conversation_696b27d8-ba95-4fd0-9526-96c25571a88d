package com.actonia.entity;

public class PageTagAlertConfigAdditionalContentEntity {
	private int id;
	private int pageTagAlertConfigId;
	private Integer selectorType;
	private String selector;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getPageTagAlertConfigId() {
		return pageTagAlertConfigId;
	}

	public void setPageTagAlertConfigId(int pageTagAlertConfigId) {
		this.pageTagAlertConfigId = pageTagAlertConfigId;
	}

	public Integer getSelectorType() {
		return selectorType;
	}

	public void setSelectorType(Integer selectorType) {
		this.selectorType = selectorType;
	}

	public String getSelector() {
		return selector;
	}

	public void setSelector(String selector) {
		this.selector = selector;
	}

	@Override
	public String toString() {
		return "PageTagAlertConfigAdditionalContentEntity [id=" + id + ", pageTagAlertConfigId=" + pageTagAlertConfigId + ", selectorType=" + selectorType
				+ ", selector=" + selector + "]";
	}

}