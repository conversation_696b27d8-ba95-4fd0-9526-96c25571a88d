package com.actonia.entity;

import lombok.Data;

@Data
public class ContentGuardChangeTrackingEntity implements Cloneable {
	private int id;
	private String indicator;
	private String field;
	private String type;
	private String description;
	private int bigDataFlg;
	private Integer criticalFlag;

	@Override
	public ContentGuardChangeTrackingEntity clone() throws CloneNotSupportedException {
		return (ContentGuardChangeTrackingEntity) super.clone();
	}


}