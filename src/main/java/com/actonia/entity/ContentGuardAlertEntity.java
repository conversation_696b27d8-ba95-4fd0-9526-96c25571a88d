package com.actonia.entity;

import javax.persistence.Transient;

public class ContentGuardAlertEntity {
	private Long id;
	private int domainId;
	private Long groupId;
	private int indicatorFlag;
	private String customIndicators;
	private String emails;

	@Transient
	private String groupName;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public int getDomainId() {
		return domainId;
	}

	public void setDomainId(int domainId) {
		this.domainId = domainId;
	}

	public Long getGroupId() {
		return groupId;
	}

	public void setGroupId(Long groupId) {
		this.groupId = groupId;
	}

	public int getIndicatorFlag() {
		return indicatorFlag;
	}

	public void setIndicatorFlag(int indicatorFlag) {
		this.indicatorFlag = indicatorFlag;
	}

	public String getCustomIndicators() {
		return customIndicators;
	}

	public void setCustomIndicators(String customIndicators) {
		this.customIndicators = customIndicators;
	}

	public String getEmails() {
		return emails;
	}

	public void setEmails(String emails) {
		this.emails = emails;
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	@Override
	public String toString() {
		return "ContentGuardAlertEntity [id=" + id + ", domainId=" + domainId + ", groupId=" + groupId + ", indicatorFlag=" + indicatorFlag + ", customIndicators="
				+ customIndicators + ", emails=" + emails + ", groupName=" + groupName + "]";
	}

}