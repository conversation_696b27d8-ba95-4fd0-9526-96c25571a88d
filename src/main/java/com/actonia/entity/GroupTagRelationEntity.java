package com.actonia.entity;

import java.beans.Transient;
import java.util.Date;

public class GroupTagRelationEntity {
	public static final int RESOURCE_TYPE_TARGETURL = 1;

	public static final int RESOURCE_TYPE_KEYWORD = 2;
//	public static final int RESOURCE_TYPE_PAGE = 9;

	public static final int VERSION10 = 10;
	public static final int VERSION20 = 20;
	public static final int VERSION30 = 30;

	private Integer id;

	private Integer domainId;

	private Integer groupTagId;

	private Long resourceId;

	private Integer resourceType;

	private Long keywordId;

	private Long targetUrlId;

	private Integer competitorurlId;
	
	private Date createDate;
	
	private Integer logId;

	private Integer rankcheckId;
	
	private String keywordName;
	
	private String tagName;

	private Integer tagVersion;

	private Long resourceIdV2;

	private Long detailId;

	public Integer getTagVersion() {
		return tagVersion;
	}

	public void setTagVersion(Integer tagVersion) {
		this.tagVersion = tagVersion;
	}

	public Long getResourceIdV2() {
		return resourceIdV2;
	}

	public void setResourceIdV2(Long resourceIdV2) {
		this.resourceIdV2 = resourceIdV2;
	}

	public Integer getRankcheckId() {
		return rankcheckId;
	}

	public void setRankcheckId(Integer rankcheckId) {
		this.rankcheckId = rankcheckId;
	}

	public Integer getCompetitorurlId() {
		return competitorurlId;
	}

	public void setCompetitorurlId(Integer competitorurlId) {
		this.competitorurlId = competitorurlId;
	}

	public Long getKeywordId() {
		return keywordId;
	}

	public void setKeywordId(Long keywordId) {
		this.keywordId = keywordId;
	}

	public Long getTargetUrlId() {
		return targetUrlId;
	}

	public void setTargetUrlId(Long targetUrlId) {
		this.targetUrlId = targetUrlId;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getDomainId() {
		return domainId;
	}

	public void setDomainId(Integer domainId) {
		this.domainId = domainId;
	}

	public Integer getGroupTagId() {
		return groupTagId;
	}

	public void setGroupTagId(Integer groupTagId) {
		this.groupTagId = groupTagId;
	}

	public Long getResourceId() {
		return resourceId;
	}

	public void setResourceId(Long resourceId) {
		this.resourceId = resourceId;
	}

	public Integer getResourceType() {
		return resourceType;
	}

	public void setResourceType(Integer resourceType) {
		this.resourceType = resourceType;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	
	public Integer getLogId() {
		return logId;
	}

	public void setLogId(Integer logId) {
		this.logId = logId;
	}
	
	
	@Transient
	public String getKeywordName() {
		return keywordName;
	}

	public void setKeywordName(String keywordName) {
		this.keywordName = keywordName;
	}
	
	public String getTagName() {
		return tagName;
	}

	public void setTagName(String tagName) {
		this.tagName = tagName;
	}

	public Long getDetailId() {
		return detailId;
	}

	public void setDetailId(Long detailId) {
		this.detailId = detailId;
	}

	@Override
	public String toString() {
		return "GroupTagRelationEntity [id=" + id + ", domainId=" + domainId + ", groupTagId=" + groupTagId + ", resourceId=" + resourceId + ", resourceType="
				+ resourceType + ", keywordId=" + keywordId + ", targetUrlId=" + targetUrlId + ", competitorurlId=" + competitorurlId + ", createDate=" + createDate
				+ "]";
	}

}
