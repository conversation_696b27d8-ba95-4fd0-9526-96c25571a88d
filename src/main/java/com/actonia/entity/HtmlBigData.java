package com.actonia.entity;

import com.actonia.IConstants;
import com.actonia.utils.CrawlerUtils;
import com.actonia.value.object.ChangeTrackingHashCdJson;
import com.actonia.value.object.CrawlerResponse;
import com.actonia.value.object.PageAnalysisResult;
import com.google.gson.Gson;
import lombok.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;
import java.util.stream.Collectors;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HtmlBigData {
    private static final Logger log = LogManager.getLogger(HtmlBigData.class);
    private String url;
    private Date trackDate;
    private String urlHash;
    private String urlMurmurHash;
    private int urlMurmurHashMod;
    private String md5;
    private String rawData;

    public static final List<String> changeTrackingHashCdJsonNameList = Arrays.asList(IConstants.ALTERNATE_LINKS,
            IConstants.AMPHTML_HREF,
            IConstants.ANALYZED_URL_S,
            IConstants.CUSTOM_DATA,
            IConstants.H1,
            IConstants.HREFLANG_ERRORS,
            IConstants.HREFLANG_LINKS,
            IConstants.INSECURE_RESOURCES,
            IConstants.OG_MARKUP,
            IConstants.REDIRECT_CHAIN,
            IConstants.STRUCTURED_DATA,
            IConstants.PAGE_LINK,
            IConstants.TITLE);

    public static List<HtmlBigData> create(HtmlClickHouseEntity htmlClickHouseEntity) {
        final CrawlerResponse crawlerResponse = htmlClickHouseEntity.getCrawlerResponse();
        final Gson gson = new Gson();
        final ChangeTrackingHashCdJson[] changeTrackingHashCdJsonArray = htmlClickHouseEntity.getChangeTrackingHashCdJsonArray();
        final Map<String, String> hashMap = Arrays.stream(changeTrackingHashCdJsonArray).parallel().collect(Collectors.toMap(ChangeTrackingHashCdJson::getName, ChangeTrackingHashCdJson::getValue));
        final String url = htmlClickHouseEntity.getUrl();
        final Date trackDate = htmlClickHouseEntity.getTrackDate();
        final List<HtmlBigData> bigDataList = changeTrackingHashCdJsonNameList.parallelStream().filter(hashMap::containsKey).map(name -> {
            final String value = hashMap.get(name);
            final HtmlBigData htmlBigData = new HtmlBigData();
            htmlBigData.setTrackDate(trackDate);
            htmlBigData.setUrl(url);
            htmlBigData.setMd5(value);
            switch (name) {
                case IConstants.ALTERNATE_LINKS:
                    htmlBigData.setRawData(gson.toJson(crawlerResponse.getAlternate_links()));
                    break;
                case IConstants.AMPHTML_HREF:
                    htmlBigData.setRawData(crawlerResponse.getAmphtml_href());
                    break;
                case IConstants.CUSTOM_DATA:
                    htmlBigData.setRawData(gson.toJson(crawlerResponse.getCustom_data()));
                    break;
                case IConstants.HREFLANG_ERRORS:
                    htmlBigData.setRawData(gson.toJson(crawlerResponse.getHreflang_errors()));
                    break;
                case IConstants.HREFLANG_LINKS:
                    htmlBigData.setRawData(gson.toJson(crawlerResponse.getHreflang_links()));
                    break;
                case IConstants.INSECURE_RESOURCES:
                    htmlBigData.setRawData(gson.toJson(crawlerResponse.getInsecure_resources()));
                    break;
                case IConstants.REDIRECT_CHAIN:
                    htmlBigData.setRawData(gson.toJson(crawlerResponse.getRedirect_chain()));
                    break;
                case IConstants.STRUCTURED_DATA:
                    htmlBigData.setRawData(gson.toJson(crawlerResponse.getStructured_data()));
                    break;
                case IConstants.PAGE_LINK:
                    htmlBigData.setRawData(gson.toJson(crawlerResponse.getPage_link()));
                    break;
                case IConstants.PAGE_ANALYSIS_FRAGMENTS:
                    final PageAnalysisResult[] pageAnalysisResultArray = htmlClickHouseEntity.getPageAnalysisResultArray();
                    if (pageAnalysisResultArray != null && pageAnalysisResultArray.length > 0) {
                        htmlBigData.setRawData(CrawlerUtils.getSortedCharactersHashCode(gson.toJson(pageAnalysisResultArray)));
                    }
                    break;
                case IConstants.OG_MARKUP:
                    htmlBigData.setRawData(gson.toJson(crawlerResponse.getOg_markup()));
                    break;
            }
            return htmlBigData;
        }).collect(Collectors.toList());
        // h2
        if (crawlerResponse.getH2() != null) {
            final String h2Json = gson.toJson(crawlerResponse.getH2());
            final HtmlBigData htmlBigData = HtmlBigData.builder()
                    .url(url)
                    .trackDate(trackDate)
                    .md5(CrawlerUtils.getSortedCharactersHashCode(h2Json))
                    .rawData(h2Json)
                    .build();
            bigDataList.add(htmlBigData);
        }
        // h1
        if (crawlerResponse.getH1()!= null) {
            final String h1Json = gson.toJson(crawlerResponse.getH1());
            final HtmlBigData htmlBigData = HtmlBigData.builder()
                   .url(url)
                   .trackDate(trackDate)
                   .md5(CrawlerUtils.getSortedCharactersHashCode(h1Json))
                .rawData(h1Json)
                .build();
            bigDataList.add(htmlBigData);
        }


        log.info("HtmlBigData list created for url: {}, size: {}", url, bigDataList.size());
        return bigDataList;
    }

}