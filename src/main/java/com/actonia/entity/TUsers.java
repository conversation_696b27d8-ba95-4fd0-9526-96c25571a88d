package com.actonia.entity;

public class TUsers {
	private Integer userId;
	private Integer ownDomainId;
	private String accessToken;
	private Integer tokensAllowed;
	private Integer totalUsed;

	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	public Integer getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public String getAccessToken() {
		return accessToken;
	}

	public void setAccessToken(String accessToken) {
		this.accessToken = accessToken;
	}

	public Integer getTokensAllowed() {
		return tokensAllowed;
	}

	public void setTokensAllowed(Integer tokensAllowed) {
		this.tokensAllowed = tokensAllowed;
	}

	public Integer getTotalUsed() {
		if (totalUsed == null) {
			return 0;
		}
		return totalUsed;
	}

	public void setTotalUsed(Integer totalUsed) {
		this.totalUsed = totalUsed;
	}

}
