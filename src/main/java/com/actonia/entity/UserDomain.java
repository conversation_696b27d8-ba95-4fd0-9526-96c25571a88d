package com.actonia.entity;

import java.util.Date;

public class UserDomain {
	
	public static final int STATE_ACTIVE = 1;
	
	private Integer id;
	private Integer userId;
	private Integer domainId;
	private Integer state;
	private Integer role;
	private Date createDate;
	private Integer suppressAlertEmail;
	private String roles;
	private Integer hiddenDefaultDashboard;
	private Integer hiddenDomainDashboard;
	private Integer grantAccessAllTags;
	private Integer isStickyCompetitorAdmin;
	
	private String email;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	public Integer getDomainId() {
		return domainId;
	}

	public void setDomainId(Integer domainId) {
		this.domainId = domainId;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public Integer getRole() {
		return role;
	}

	public void setRole(Integer role) {
		this.role = role;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getSuppressAlertEmail() {
		return suppressAlertEmail;
	}

	public void setSuppressAlertEmail(Integer suppressAlertEmail) {
		this.suppressAlertEmail = suppressAlertEmail;
	}

	public String getRoles() {
		return roles;
	}

	public void setRoles(String roles) {
		this.roles = roles;
	}

	public Integer getHiddenDefaultDashboard() {
		return hiddenDefaultDashboard;
	}

	public void setHiddenDefaultDashboard(Integer hiddenDefaultDashboard) {
		this.hiddenDefaultDashboard = hiddenDefaultDashboard;
	}

	public Integer getHiddenDomainDashboard() {
		return hiddenDomainDashboard;
	}

	public void setHiddenDomainDashboard(Integer hiddenDomainDashboard) {
		this.hiddenDomainDashboard = hiddenDomainDashboard;
	}

	public Integer getGrantAccessAllTags() {
		return grantAccessAllTags;
	}

	public void setGrantAccessAllTags(Integer grantAccessAllTags) {
		this.grantAccessAllTags = grantAccessAllTags;
	}

	public Integer getIsStickyCompetitorAdmin() {
		return isStickyCompetitorAdmin;
	}

	public void setIsStickyCompetitorAdmin(Integer isStickyCompetitorAdmin) {
		this.isStickyCompetitorAdmin = isStickyCompetitorAdmin;
	}
	
	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}
}
