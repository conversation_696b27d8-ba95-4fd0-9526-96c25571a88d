package com.actonia.entity;

public class ZapierErrorCodeEntity {
	private String errorCode;
	private String errorMessage;
	private int httpStatusCode;

	public String getErrorCode() {
		return errorCode;
	}

	public void setErrorCode(String errorCode) {
		this.errorCode = errorCode;
	}

	public String getErrorMessage() {
		return errorMessage;
	}

	public void setErrorMessage(String errorMessage) {
		this.errorMessage = errorMessage;
	}

	public int getHttpStatusCode() {
		return httpStatusCode;
	}

	public void setHttpStatusCode(int httpStatusCode) {
		this.httpStatusCode = httpStatusCode;
	}

	@Override
	public String toString() {
		return "ZapierErrorCodeEntity [errorCode=" + errorCode + ", errorMessage=" + errorMessage + ", httpStatusCode=" + httpStatusCode + "]";
	}

}