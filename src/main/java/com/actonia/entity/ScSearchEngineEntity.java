/**
 * 
 */
package com.actonia.entity;

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * com.actonia.saas.model.rankcheck.ScSearchEngineEntity.java
 *
 * <AUTHOR>
 *
 * @version $Revision: 137937 $
 *          $Author: wangcee $
 */
@Entity
@Table(name = "search_engine_entity")
public class ScSearchEngineEntity {
	
	public static final int GOOGLE_COM = 1;
	public static final int GOOGLE_COM_AU = 2;
	public static final int GOOGLE_CA = 3;
	public static final int GOOGLE = 99;
	public static final int YAHOO = 100;
//	public static final int MSN = 200;
	
	public static final int SEARCH_ENGINE_GOOGLE_FR = 4;
	public static final int SEARCH_ENGINE_GOOGLE_UK = 5;
	public static final int SEARCH_ENGINE_GOOGLE_UK_ALL = 6;
	public static final int SEARCH_ENGINE_GOOGLE_FR_ALL = 7;
	public static final int SEARCH_ENGINE_BAIDU_CN = 150;
	public static final int SEARCH_ENGINE_HAOSOU_CN = 151;
	public static final int SEARCH_ENGINE_GOOGLE_IT = 8;
	public static final int SEARCH_ENGINE_GOOGLE_DK = 9;
	public static final int SEARCH_ENGINE_GOOGLE_FI = 10;
	public static final int SEARCH_ENGINE_GOOGLE_COM_MX = 11;
	public static final int SEARCH_ENGINE_GOOGLE_NO = 12;
	public static final int SEARCH_ENGINE_GOOGLE_SE = 13;
	public static final int SEARCH_ENGINE_GOOGLE_DE = 14;
	public static final int SEARCH_ENGINE_GOOGLE_COM_BR = 15;
	public static final int SEARCH_ENGINE_GOOGLE_ES = 16;
	public static final int SEARCH_ENGINE_GOOGLE_NL = 17;
	public static final int SEARCH_ENGINE_GOOGLE_JP = 18;
	public static final int SEARCH_ENGINE_GOOGLE_PT = 19;
	public static final int SEARCH_ENGINE_GOOGLE_IE = 20;
	public static final int SEARCH_ENGINE_GOOGLE_BE = 21;
	public static final int SEARCH_ENGINE_GOOGLE_CH = 22;
	public static final int SEARCH_ENGINE_GOOGLE_CO_KR = 23;
	public static final int SEARCH_ENGINE_GOOGLE_CO_IN = 24;
	public static final int SEARCH_ENGINE_GOOGLE_COM_AR = 25;
	public static final int SEARCH_ENGINE_GOOGLE_CL = 26;
	public static final int SEARCH_ENGINE_GOOGLE_COM_CO = 27;
	public static final int SEARCH_ENGINE_GOOGLE_COM_PR = 28;
	public static final int SEARCH_ENGINE_GOOGLE_AT = 29;
	public static final int SEARCH_ENGINE_GOOGLE_COM_HK = 30;
	public static final int SEARCH_ENGINE_GOOGLE_CO_ID = 31;
	public static final int SEARCH_ENGINE_GOOGLE_COM_MY = 32;
	public static final int SEARCH_ENGINE_GOOGLE_COM_PH = 33;
	public static final int SEARCH_ENGINE_GOOGLE_COM_TW = 34;
	public static final int SEARCH_ENGINE_GOOGLE_CO_TH = 35;
	public static final int SEARCH_ENGINE_GOOGLE_COM_VN = 36;
	public static final int SEARCH_ENGINE_GOOGLE_COM_SG = 37;
	public static final int SEARCH_ENGINE_GOOGLE_CO_NZ = 38;
	public static final int SEARCH_ENGINE_GOOGLE_RU = 39;
	public static final int SEARCH_ENGINE_GOOGLE_CZ = 40;
	public static final int SEARCH_ENGINE_GOOGLE_HU = 41;
	public static final int SEARCH_ENGINE_GOOGLE_PL = 42;
	public static final int SEARCH_ENGINE_GOOGLE_COM_SA = 43;
	public static final int SEARCH_ENGINE_GOOGLE_COM_PE = 44;
	public static final int SEARCH_ENGINE_GOOGLE_AE = 45;
	public static final int SEARCH_ENGINE_GOOGLE_COM_TR = 46;
	public static final int SEARCH_ENGINE_GOOGLE_CO_ZA = 47;
	public static final int SEARCH_ENGINE_GOOGLE_CO_KE = 48;
	public static final int SEARCH_ENGINE_GOOGLE_SK = 49;
	public static final int SEARCH_ENGINE_GOOGLE_CO_IL = 50;
	public static final int SEARCH_ENGINE_GOOGLE_COM_EC = 51;
	public static final int SEARCH_ENGINE_GOOGLE_CO_VE = 52;
	public static final int SEARCH_ENGINE_GOOGLE_CO_CR = 53;
	public static final int SEARCH_ENGINE_GOOGLE_COM_UA = 54;
	public static final int SEARCH_ENGINE_GOOGLE_COM_UY = 55;
	public static final int SEARCH_ENGINE_GOOGLE_MV = 56;
	public static final int SEARCH_ENGINE_GOOGLE_COM_BD = 57;
	public static final int SEARCH_ENGINE_GOOGLE_COM_KH = 58;
	public static final int SEARCH_ENGINE_GOOGLE_COM_FJ = 59;
	public static final int SEARCH_ENGINE_GOOGLE_WS = 60;
	public static final int SEARCH_ENGINE_GOOGLE_GR = 61;
	public static final int SEARCH_ENGINE_GOOGLE_COM_NG = 62;
	public static final int SEARCH_ENGINE_GOOGLE_COM_DO = 63;
	//Tony - https://www.wrike.com/open.htm?id=123551271
	public static final int SEARCH_ENGINE_GOOGLE_PK = 64;
	
	//Cee - https://www.wrike.com/open.htm?id=194155574
	public static final int SEARCH_ENGINE_GOOGLE_COM_LB = 65;
	
	public static final int SEARCH_ENGINE_GOOGLE_PA = 67;
	//Cee - https://www.wrike.com/open.htm?id=185406452
	public static final int SEARCH_ENGINE_GOOGLE_JO = 68;
	

	
	//Mark - https://www.wrike.com/open.htm?id=180398675
	public static final int SEARCH_ENGINE_GOOGLE_PR = 28;
	
	//Mark - https://www.wrike.com/open.htm?id=172144455
	public static final int SEARCH_ENGINE_GOOGLE_QA = 66;	
	
	//Mark - https://www.wrike.com/open.htm?id=180398816
	public static final int SEARCH_ENGINE_GOOGLE_HR = 69;
	
	//Mark - https://www.wrike.com/open.htm?id=172144814
	public static final int SEARCH_ENGINE_GOOGLE_DZ = 70;
	public static final int SEARCH_ENGINE_GOOGLE_RO = 71;
	public static final int SEARCH_ENGINE_GOOGLE_AL = 72;
	public static final int SEARCH_ENGINE_GOOGLE_AZ = 73;
	public static final int SEARCH_ENGINE_GOOGLE_BG = 74;
	//Mark - https://www.wrike.com/open.htm?id=172144671
	public static final int SEARCH_ENGINE_GOOGLE_COM_BH = 75;
	public static final int SEARCH_ENGINE_GOOGLE_COM_CY = 76;
	public static final int SEARCH_ENGINE_GOOGLE_DJ = 77;
	//Cee - https://www.wrike.com/open.htm?id=172393192
	public static final int SEARCH_ENGINE_GOOGLE_COM_EG = 78;
	public static final int SEARCH_ENGINE_GOOGLE_COM_ET = 79;
	
	//Cee - https://www.wrike.com/open.htm?id=180398013
	public static final int SEARCH_ENGINE_GOOGLE_GA = 80;
	
	//Cee - https://www.wrike.com/open.htm?id=185405601
	public static final int SEARCH_ENGINE_GOOGLE_GE = 81;
	//Cee - https://www.wrike.com/open.htm?id=189907293
	public static final int SEARCH_ENGINE_GOOGLE_COM_KW = 82;
	//Cee - https://www.wrike.com/open.htm?id=185406162
	public static final int SEARCH_ENGINE_GOOGLE_CO_MA = 83;
    public static final int SEARCH_ENGINE_GOOGLE_MC = 84;
	//Cee - https://www.wrike.com/open.htm?id=189340877
	public static final int SEARCH_ENGINE_GOOGLE_COM_MT = 85;
	//Mark - https://www.wrike.com/open.htm?id=180397949
	public static final int SEARCH_ENGINE_GOOGLE_MU = 86;
	public static final int SEARCH_ENGINE_GOOGLE_COM_OM = 87;
	public static final int SEARCH_ENGINE_GOOGLE_RS = 88;
	public static final int SEARCH_ENGINE_GOOGLE_SC = 89;
	public static final int SEARCH_ENGINE_GOOGLE_COM_TJ = 90;
	public static final int SEARCH_ENGINE_GOOGLE_TN = 91;
	public static final int SEARCH_ENGINE_GOOGLE_CO_UG = 92;
	//Leo - https://www.wrike.com/open.htm?id=151468121
    public static final int SEARCH_ENGINE_GOOGLE_COM_SV = 93;
    //Leo - https://www.wrike.com/open.htm?id=162032768
    public static final int SEARCH_ENGINE_GOOGLE_LU = 94;
	//Sunny - https://www.wrike.com/open.htm?id=194110395
    public static final int SEARCH_ENGINE_GOOGLE_LT = 95;
	// Edwin - https://www.wrike.com/open.htm?id=260889612
	public static final int SEARCH_ENGINE_GOOGLE_EE = 97;
	public static final int SEARCH_ENGINE_GOOGLE_LV = 98;

    //Cee - https://www.wrike.com/open.htm?id=151728885
    public static final int SEARCH_ENGINE_GOOGLE_MM = 96;
	
	
	public static final int SEARCH_BING = 255;
	public static final int SEARCH_YAHOO = 100;
	//Cee - https://www.wrike.com/open.htm?id=230201764
	public static final int SEARCH_APPLE = 110;
	public static final int SEARCH_ENGINE_360_SEARCH = 170;
	public static final int SEARCH_ENGINE_SOGOU = 180;
	public static final int SEARCH_NAVER = 160;
	public static final int SEARCH_YANDEX = 120;

	private Integer id;
	
	private String searchEngine;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "search_engine")
	public String getSearchEngine() {
		return searchEngine;
	}

	public void setSearchEngine(String searchEngine) {
		this.searchEngine = searchEngine;
	}
	
}
