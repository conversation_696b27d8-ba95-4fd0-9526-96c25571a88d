package com.actonia.entity;

// Generated Dec 18, 2013 10:47:55 AM by Hibernate Tools 3.4.0.CR1

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import static javax.persistence.GenerationType.IDENTITY;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Version;

/**
 * CustomizeFilterCriteriaEntity generated by hbm2java
 */
@Entity
@Table(name = "customize_filter_criteria")
public class CustomizeFilterCriteriaEntity implements java.io.Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 2980433110589685373L;
	public static final String MULTI_STRING_SEPARATOR = "!_!";	
	public static final int VALUE_STRING = 1;
	public static final int VALUE_MULTI_STRING = 4;
	public static final int VALUE_DATE = 9;
	public static final int VALUE_INT = 10;
	public static final int VALUE_LONG = 11;
	public static final int VALUE_FLOAT = 12;
	public static final int VALUE_PERCENTAGE = 13;

	private Integer id;
	private Integer version;
	private String criteriaName;
	private int elementType;
	private Integer valueType;
	private String columnName;

	public CustomizeFilterCriteriaEntity() {
	}

	public CustomizeFilterCriteriaEntity(String criteriaName, int elementType) {
		this.criteriaName = criteriaName;
		this.elementType = elementType;
	}

	public CustomizeFilterCriteriaEntity(String criteriaName, int elementType, Integer valueType, String columnName) {
		this.criteriaName = criteriaName;
		this.elementType = elementType;
		this.valueType = valueType;
		this.columnName = columnName;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Version
	@Column(name = "version")
	public Integer getVersion() {
		return this.version;
	}

	public void setVersion(Integer version) {
		this.version = version;
	}

	@Column(name = "criteria_name", nullable = false)
	public String getCriteriaName() {
		return this.criteriaName;
	}

	public void setCriteriaName(String criteriaName) {
		this.criteriaName = criteriaName;
	}

	@Column(name = "element_type", nullable = false)
	public int getElementType() {
		return this.elementType;
	}

	public void setElementType(int elementType) {
		this.elementType = elementType;
	}

	@Column(name = "value_type")
	public Integer getValueType() {
		return this.valueType;
	}

	public void setValueType(Integer valueType) {
		this.valueType = valueType;
	}

	@Column(name = "column_name")
	public String getColumnName() {
		return this.columnName;
	}

	public void setColumnName(String columnName) {
		this.columnName = columnName;
	}

}
