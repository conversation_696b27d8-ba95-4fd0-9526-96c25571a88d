package com.actonia.entity;

public class ContentGuardSkipUrlEntity {
	private long id;
	private int domainId;
	private Long groupId;
	private String indicator;
	private int urlSelectorType;
	private String urlSelector;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public int getDomainId() {
		return domainId;
	}

	public void setDomainId(int domainId) {
		this.domainId = domainId;
	}

	public Long getGroupId() {
		return groupId;
	}

	public void setGroupId(Long groupId) {
		this.groupId = groupId;
	}

	public String getIndicator() {
		return indicator;
	}

	public void setIndicator(String indicator) {
		this.indicator = indicator;
	}

	public int getUrlSelectorType() {
		return urlSelectorType;
	}

	public void setUrlSelectorType(int urlSelectorType) {
		this.urlSelectorType = urlSelectorType;
	}

	public String getUrlSelector() {
		return urlSelector;
	}

	public void setUrlSelector(String urlSelector) {
		this.urlSelector = urlSelector;
	}

	@Override
	public String toString() {
		return "ContentGuardSkipUrlEntity [id=" + id + ", domainId=" + domainId + ", groupId=" + groupId + ", indicator=" + indicator + ", urlSelectorType="
				+ urlSelectorType + ", urlSelector=" + urlSelector + "]";
	}

}