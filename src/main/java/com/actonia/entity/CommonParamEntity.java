/**
 * 
 */
package com.actonia.entity;

import java.util.Date;

/**
 * com.actonia.subserver.entity.CommonParamEntity.java
 *
 * <AUTHOR>
 *
 * @version $Revision:$ $Author:$
 */
public class CommonParamEntity {

    public enum ParamFuncName {
        KwPort, ReGridKw, AnsBox, SetFtp, // Cee -
                                          // https://www.wrike.com/open.htm?id=170342403
        SetAuditIssues // Cee - https://www.wrike.com/open.htm?id=169915697
    }

    private Long id;
    private Integer ownDomainId;
    private String funcName;
    private String title;
    private Integer version;
    private String paramJson;
    private String uiJson;
    private Integer createUser;
    private Date createDate;
    private Integer updateUser;
    private Date updateDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOwnDomainId() {
        return ownDomainId;
    }

    public void setOwnDomainId(Integer ownDomainId) {
        this.ownDomainId = ownDomainId;
    }

    public String getFuncName() {
        return funcName;
    }

    public void setFuncName(String funcName) {
        this.funcName = funcName;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getParamJson() {
        return paramJson;
    }

    public void setParamJson(String paramJson) {
        this.paramJson = paramJson;
    }

    public Integer getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Integer createUser) {
        this.createUser = createUser;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Integer updateUser) {
        this.updateUser = updateUser;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getUiJson() {
        return uiJson;
    }

    public void setUiJson(String uiJson) {
        this.uiJson = uiJson;
    }

}
