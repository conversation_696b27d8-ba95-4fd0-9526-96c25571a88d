package com.actonia.entity;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@Table(name = "polite_crawl_state_log")
public class PoliteCrawlStateLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "ownDomainId", nullable = false)
    private Integer ownDomainId;

    @Column(name = "urlMurmur3Hash", nullable = false, columnDefinition = "bigint(20) COMMENT 'murmurHash3_64(url). t_target_url.urlMurmur3Hash'")
    private String urlMurmur3Hash;

    @Column(name = "crawlDate", nullable = false, columnDefinition = "date not null")
    private Date crawlDate;

    @Column(name = "responseCode", nullable = false, columnDefinition = "smallint(3) COMMENT '200,301,404,503'")
    private Integer responseCode;

    @Column(name = "urlType", nullable = false, columnDefinition = "tinyint(1) DEFAULT '1' COMMENT '1: managed URL, 2: ranking URL'")
    private Integer urlType;

    @Column(name = "createDate", nullable = false, columnDefinition = "timestamp DEFAULT CURRENT_TIMESTAMP COMMENT 'Timestamp of creation'")
    private Date createDate;

    public static PoliteCrawlStateLog fromHtmlClickHouseEntity(HtmlClickHouseEntity htmlClickHouseEntityCurrent) {
        final PoliteCrawlStateLog politeCrawlStateLog = new PoliteCrawlStateLog();
        politeCrawlStateLog.setOwnDomainId(htmlClickHouseEntityCurrent.getDomainId());
        politeCrawlStateLog.setUrlMurmur3Hash(htmlClickHouseEntityCurrent.getUrlMurmurHash());
        politeCrawlStateLog.setCrawlDate(htmlClickHouseEntityCurrent.getTrackDate());
        politeCrawlStateLog.setResponseCode(htmlClickHouseEntityCurrent.getHttpStatusCode());
        politeCrawlStateLog.setUrlType(1);
        return politeCrawlStateLog;
    }

    // Constructors, getters, and setters

    @Override
    public String toString() {
        return "PoliteCrawlStateLog{" +
                "id=" + id +
                ", ownDomainId=" + ownDomainId +
                ", urlMurmur3Hash=" + urlMurmur3Hash +
                ", crawlDate=" + crawlDate +
                ", responseCode=" + responseCode +
                ", urlType=" + urlType +
                ", createDate=" + createDate +
                '}';
    }
}
