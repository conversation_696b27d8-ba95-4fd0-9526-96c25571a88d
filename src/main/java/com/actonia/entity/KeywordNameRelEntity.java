package com.actonia.entity;

import org.apache.commons.lang.StringUtils;

import com.actonia.utils.Md5Util;

public class KeywordNameRelEntity {

	public static final int LONG_KEYWORD_NAME_LEN = 254;
	
	public static final int MAX_LONG_KEYWORD_NAME_LEN = 600;
	
	public static final String KEYWORD_NAME_TOO_LONG_STR = "unrecognized+keyword+";

	private Integer id;

	private Integer keywordId;

	private String keywordName;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getKeywordName() {
		return keywordName;
	}

	public void setKeywordName(String keywordName) {
		this.keywordName = keywordName;
	}

	public Integer getKeywordId() {
		return keywordId;
	}

	public void setKeywordId(Integer keywordId) {
		this.keywordId = keywordId;
	}
	
	public static String getLongKeywordNameReplaced(String keywordName){
		if (StringUtils.isNotBlank(keywordName) && keywordName.length() > LONG_KEYWORD_NAME_LEN) {
			return KEYWORD_NAME_TOO_LONG_STR + Md5Util.Md5(keywordName) ;
		} else {
			return keywordName;
		}
	}
	
	/**
	 * check keywordName is the long keywordName replaced
	 * 
	 * @return True means this is a replaced string, it is not the real keywordName. False means it is a real keyword.
	 * */
	public static boolean isLongKeywordNameReplaced(String keywordName) {
		if (!StringUtils.isBlank(keywordName)){
			return keywordName.length() == (KEYWORD_NAME_TOO_LONG_STR.length() + 32) && 
					keywordName.trim().startsWith(KEYWORD_NAME_TOO_LONG_STR);
		} else {
			return false;
		}
	}

}
