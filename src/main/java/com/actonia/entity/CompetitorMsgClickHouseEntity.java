package com.actonia.entity;

import java.util.Date;

import javax.persistence.Transient;

public class CompetitorMsgClickHouseEntity {
	private Date track_date;
	private String url_domain;
	private String url;
	private String url_hash;
	private String url_murmur_hash;
	private String msg;
	
	@Transient
	private int total_records;

	public Date getTrack_date() {
		return track_date;
	}

	public void setTrack_date(Date track_date) {
		this.track_date = track_date;
	}

	public String getUrl_domain() {
		return url_domain;
	}

	public void setUrl_domain(String url_domain) {
		this.url_domain = url_domain;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getUrl_hash() {
		return url_hash;
	}

	public void setUrl_hash(String url_hash) {
		this.url_hash = url_hash;
	}

	public String getUrl_murmur_hash() {
		return url_murmur_hash;
	}

	public void setUrl_murmur_hash(String url_murmur_hash) {
		this.url_murmur_hash = url_murmur_hash;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	@Override
	public String toString() {
		return "CompetitorMsgClickHouseEntity [track_date=" + track_date + ", url_domain=" + url_domain + ", url=" + url + ", url_hash=" + url_hash
				+ ", url_murmur_hash=" + url_murmur_hash + ", msg=" + msg + "]";
	}

	@Transient
	public int getTotal_records() {
		return total_records;
	}

	@Transient
	public void setTotal_records(int total_records) {
		this.total_records = total_records;
	}

}
