package com.actonia.robots.txt;

import java.util.Calendar;
import java.util.Date;

import com.actonia.entity.*;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.concurrency.BaseThreadCommand;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.dao.RobotsTxtClickHouseDAO;
import com.actonia.dao.TargetUrlEntityDAO;
import com.actonia.utils.CrawlerUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.SpringBeanFactory;

@Log4j2
public class RobotsTxtCrawlCommand extends BaseThreadCommand {

	//private Boolean isDebug = false;
	private String ip;
	private static final String TABLE_NAME = null;
	private int domainId;
	private String domainName;
	private TargetUrlEntityDAO targetUrlEntityDAO;

	public RobotsTxtCrawlCommand(String ip, OwnDomainEntity ownDomainEntity) {
		super();
		this.ip = ip;
		this.domainId = ownDomainEntity.getId();
		this.domainName = ownDomainEntity.getDomain();
		this.targetUrlEntityDAO = SpringBeanFactory.getBean("targetUrlEntityDAO");
	}

	@Override
	protected void execute() throws Exception {
		try {
			process();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			CacheModleFactory.getInstance().setAliveIpAddress(ip);
		}
	}

	@Override
	protected void undo() throws Exception {
		// TODO Auto-generated method stub

	}

	private void process() throws Exception {
		boolean isChanged = false;
		RobotsTxtClickHouseEntity robotsTxtClickHouseEntityCurrent = null;
		RobotsTxtClickHouseEntity robotsTxtClickHouseEntityPrevious = null;
		String requestUrl = null;
		String responseString = null;
		TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = null;
		TargetUrlEntity targetUrlEntity = targetUrlEntityDAO.getRobotsTxtUrl(domainId);
		if (targetUrlEntity != null && StringUtils.isNotBlank(targetUrlEntity.getUrl()) && CrawlerUtils.getInstance().isValidRobotTxtUrl(targetUrlEntity.getUrl())) {
			FormatUtils.getInstance().logMemoryUsage(
					"process() begins. ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",robots.txt URL=" + targetUrlEntity.getUrl());
			requestUrl = targetUrlEntity.getUrl();

			// current robots.txt
			robotsTxtClickHouseEntityCurrent = new RobotsTxtClickHouseEntity();
			robotsTxtClickHouseEntityCurrent.setDomainId(domainId);
			robotsTxtClickHouseEntityCurrent.setUrl(requestUrl);
			robotsTxtClickHouseEntityCurrent.setTrackDate(DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH));
			robotsTxtClickHouseEntityCurrent.setCrawlTimestamp(new Date());
			robotsTxtClickHouseEntityCurrent.setSign(IConstants.CLICKHOUSE_SIGN_POSITIVE_1);
			try {
				responseString = CrawlerUtils.getInstance().getHttpGetResponseString(ip, requestUrl);
				robotsTxtClickHouseEntityCurrent.setResponseCode(IConstants.RESPONSE_CODE_200);
				robotsTxtClickHouseEntityCurrent.setContent(responseString);
			} catch (Exception e) {
				robotsTxtClickHouseEntityCurrent.setResponseCode(e.getMessage());
			}

			// previous robots.txt
			robotsTxtClickHouseEntityPrevious = RobotsTxtClickHouseDAO.getInstance().getPrevious(domainId, TABLE_NAME);

			// when last record is available...
			if (robotsTxtClickHouseEntityPrevious != null) {
				isChanged = checkIfChanged(robotsTxtClickHouseEntityPrevious, robotsTxtClickHouseEntityCurrent);
				if (isChanged == true) {
					FormatUtils.getInstance().logMemoryUsage(
							"process() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",current crawl results different from last crawl results.");
					robotsTxtClickHouseEntityCurrent.setPreviousCrawlTimestamp(robotsTxtClickHouseEntityPrevious.getCrawlTimestamp());
					CacheModleFactory.getInstance().getRobotsTxtClickHouseEntityList().add(robotsTxtClickHouseEntityCurrent);

					// create the record to be created in the 'target_url_change_ind' ClarityDB table
					targetUrlChangeIndClickHouseEntity = getTargetUrlChangeIndClickHouseEntity(robotsTxtClickHouseEntityPrevious, robotsTxtClickHouseEntityCurrent);
					CacheModleFactory.getInstance().getTargetUrlChangeIndClickHouseEntityList().add(targetUrlChangeIndClickHouseEntity);

					try {
						final HtmlChange htmlChange = createHtmlChange(robotsTxtClickHouseEntityPrevious, robotsTxtClickHouseEntityCurrent);
						RobotsTxtCrawl.htmlChangeQueue.add(htmlChange);
					} catch (Exception e) {
						log.error("createHtmlChange() error. ip={},domainId={},domainName={},error={}", ip, domainId, domainName, e.getMessage());
					}
				} else {
					FormatUtils.getInstance().logMemoryUsage("process() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
							+ ",current crawl results not different from last crawl results.");
				}
			}
			// when last record is not available, create first record
			else {
				robotsTxtClickHouseEntityCurrent.setPreviousCrawlTimestamp(new Date());
				CacheModleFactory.getInstance().getRobotsTxtClickHouseEntityList().add(robotsTxtClickHouseEntityCurrent);
			}
			FormatUtils.getInstance().logMemoryUsage("process() ends. ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName);
		} else {
			//FormatUtils.getInstance().logMemoryUsage("process() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",robots.txt URL not available.");
		}
	}

	private HtmlChange createHtmlChange(RobotsTxtClickHouseEntity robotsTxtClickHouseEntityPrevious, RobotsTxtClickHouseEntity robotsTxtClickHouseEntityCurrent) {
		HtmlChange htmlChange = new HtmlChange();

		// domain_id
		htmlChange.setDomainId(robotsTxtClickHouseEntityCurrent.getDomainId());

		// url_type
		htmlChange.setUrlType(IConstants.URL_TYPE_MANAGED);

		// url
		htmlChange.setUrl(robotsTxtClickHouseEntityCurrent.getUrl());

		// url_murmur_hash
		htmlChange.setUrl_murmur_hash(robotsTxtClickHouseEntityCurrent.getUrlMurmurHash());

		// track_date
		htmlChange.setTrackDate(robotsTxtClickHouseEntityCurrent.getTrackDate());

		// current_crawl_timestamp
		htmlChange.setCurrCrawlTimestamp(robotsTxtClickHouseEntityCurrent.getCrawlTimestamp());

		// change_indicator
		htmlChange.setChgId(86);

		// previous_crawl_timestamp
		htmlChange.setPrevCrawlTimestamp(robotsTxtClickHouseEntityCurrent.getPreviousCrawlTimestamp());

		// prev_response_code
		final String responseCodePrevious = robotsTxtClickHouseEntityPrevious.getResponseCode();
		try {
			final int responseCodePreviousNumber = Integer.parseInt(responseCodePrevious);
			htmlChange.setPrevResponseCode(responseCodePreviousNumber);
		} catch (NumberFormatException e) {
			log.error("process() domainId= {},domainName={},previous response code is not a number. responseCode= {}", domainId, domainName, responseCodePrevious);
		}

		// curr_response_code
		final String responseCodeCurrent = robotsTxtClickHouseEntityCurrent.getResponseCode();
		try {
			final int responseCodeCurrentNumber = Integer.parseInt(responseCodeCurrent);
			htmlChange.setCurrResponseCode(responseCodeCurrentNumber);
		} catch (NumberFormatException e) {
			log.error("process() domainId= {},domainName={},current response code is not a number. responseCode= {}", domainId, domainName, responseCodeCurrent);
		}

		// content
		htmlChange.setCurrValue(responseCodeCurrent + IConstants.MULTI_STRING_SEPARATOR + robotsTxtClickHouseEntityCurrent.getContent());

		// previous_content
		htmlChange.setPrevValue(responseCodeCurrent + IConstants.MULTI_STRING_SEPARATOR + robotsTxtClickHouseEntityPrevious.getContent());

		// create_timestamp
		htmlChange.setCreateTimestamp(new Date());

		return htmlChange;
	}

	private TargetUrlChangeIndClickHouseEntity getTargetUrlChangeIndClickHouseEntity(RobotsTxtClickHouseEntity robotsTxtClickHouseEntityPrevious,
			RobotsTxtClickHouseEntity robotsTxtClickHouseEntityCurrent) {
		TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = new TargetUrlChangeIndClickHouseEntity();

		// domain_id
		targetUrlChangeIndClickHouseEntity.setDomainId(robotsTxtClickHouseEntityCurrent.getDomainId());

		// url
		targetUrlChangeIndClickHouseEntity.setUrl(robotsTxtClickHouseEntityCurrent.getUrl());

		// track_date
		targetUrlChangeIndClickHouseEntity.setTrackDate(robotsTxtClickHouseEntityCurrent.getTrackDate());

		// current_crawl_timestamp
		targetUrlChangeIndClickHouseEntity.setCurrentCrawlTimestamp(robotsTxtClickHouseEntityCurrent.getCrawlTimestamp());

		// change_indicator
		targetUrlChangeIndClickHouseEntity.setChangeIndicator(IConstants.ROBOTS_TXT_CHG_IND);

		// previous_crawl_timestamp
		targetUrlChangeIndClickHouseEntity.setPreviousCrawlTimestamp(robotsTxtClickHouseEntityCurrent.getPreviousCrawlTimestamp());

		// change_type
		targetUrlChangeIndClickHouseEntity.setChangeType(IConstants.CHANGE_TYPE_NUMBER_MODIFIED);

		// critical_ind
		targetUrlChangeIndClickHouseEntity.setCriticalInd(IConstants.CONTENT_GUARD_CHANGE_SEVERITY_CRITICAL);

		// robot_txt_current
		targetUrlChangeIndClickHouseEntity.setRobotTxtCurrent(robotsTxtClickHouseEntityCurrent.getContent());

		// robot_txt_previous
		targetUrlChangeIndClickHouseEntity.setRobotTxtPrevious(robotsTxtClickHouseEntityPrevious.getContent());

		// response_code_current
		targetUrlChangeIndClickHouseEntity.setResponseCodeCurrent(robotsTxtClickHouseEntityCurrent.getResponseCode());

		// response_code_previous
		targetUrlChangeIndClickHouseEntity.setResponseCodePrevious(robotsTxtClickHouseEntityPrevious.getResponseCode());

		return targetUrlChangeIndClickHouseEntity;
	}

	private boolean checkIfChanged(RobotsTxtClickHouseEntity robotsTxtClickHouseEntityPrevious, RobotsTxtClickHouseEntity robotsTxtClickHouseEntityCurrent) {
		boolean isChanged = false;

		Date todayDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);

		// when previous track date is today, robots.txt already processed today, no need track changes again
		if (robotsTxtClickHouseEntityPrevious.getTrackDate().equals(todayDate)) {
			isChanged = false;
		}
		// when response_code is different between previous and current robots.txt
		else if (StringUtils.equalsIgnoreCase(robotsTxtClickHouseEntityPrevious.getResponseCode(), robotsTxtClickHouseEntityCurrent.getResponseCode()) == false) {
			isChanged = true;
		}
		// when content is different between previous and current robots.txt
		else if (checkContentIsChanged(robotsTxtClickHouseEntityPrevious.getContent(), robotsTxtClickHouseEntityCurrent.getContent())) {
			isChanged = true;
		}
		FormatUtils.getInstance().logMemoryUsage("checkIfChanged() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",isChanged=" + isChanged);
		return isChanged;
	}

	/**
	 * Check if the content has been changed.
	 *
	 * @param  previousContent  the previous content
	 * @param  currentContent   the current content
	 * @return                  true if the content has been changed, false otherwise
	 */
	private boolean checkContentIsChanged(String previousContent, String currentContent) {
		// Check if both strings are blank
		if (StringUtils.isBlank(previousContent) && StringUtils.isBlank(currentContent)) {
			return false;
		}

		// Check if both strings are non-null and not equal
		return !StringUtils.equalsIgnoreCase(previousContent, currentContent);
	}
}
