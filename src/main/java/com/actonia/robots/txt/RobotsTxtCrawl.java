package com.actonia.robots.txt;

import java.util.ArrayList;
import java.util.List;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.ConcurrentLinkedQueue;

import com.actonia.dao.HtmlChangeClickHouseDAO;
import com.actonia.entity.HtmlChange;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;

import com.actonia.concurrency.CacheModleFactory;
import com.actonia.concurrency.ThreadPoolService;
import com.actonia.dao.OwnDomainEntityDAO;
import com.actonia.dao.RobotsTxtClickHouseDAO;
import com.actonia.dao.TargetUrlChangeIndClickHouseDAO;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.utils.CommonUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.PutMessageUtils;
import com.actonia.utils.SpringBeanFactory;

@Log4j2
public class RobotsTxtCrawl {

	//private Boolean isDebug = false;

	private static ThreadPoolService threadPoolService = ThreadPoolService.getInstance();
	private static final int DEFAULT_NUMBER_OF_THREADS = 8;
	private static final String TABLE_NAME = null;
	protected static final ConcurrentLinkedQueue<HtmlChange> htmlChangeQueue = new ConcurrentLinkedQueue<>();

	private OwnDomainEntityDAO ownDomainEntityDAO;

	public RobotsTxtCrawl() {
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
	}

	public static void main(String args[]) {
		FormatUtils.getInstance().logMemoryUsage("main() begins.");
		long startTimestamp = System.currentTimeMillis();
		try {
			new RobotsTxtCrawl().process(args);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			threadPoolService.destroy();
		}
		FormatUtils.getInstance().logMemoryUsage("main() ends. elapsed time in sec.:" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private void process(String args[]) throws Exception {

		String testString = null;
		int numberOfConcurrentThreads = 0;
		String domainIdsString = null;

		// runtime parameter 1 (optional): number of concurrent threads
		if (args != null && args.length >= 1) {
			testString = args[0];
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameter 1: number of concurrent threads =" + testString);
			if (StringUtils.isNotBlank(testString)) {
				numberOfConcurrentThreads = NumberUtils.toInt(testString);
			}
		}
		if (numberOfConcurrentThreads == 0) {
			numberOfConcurrentThreads = DEFAULT_NUMBER_OF_THREADS;
		}
		FormatUtils.getInstance().logMemoryUsage("process() numberOfConcurrentThreads=" + numberOfConcurrentThreads);
		threadPoolService.init();
		CommonUtils.initThreads(numberOfConcurrentThreads);

		// runtime parameter 2 (optional): list of domain IDs
		if (args != null && args.length >= 2) {
			domainIdsString = args[1];
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameter 2:list of domain IDs=" + domainIdsString);
		}
		FormatUtils.getInstance().logMemoryUsage("process() domainIdsString=" + domainIdsString);

		Boolean isExecDomainIdsInd = null;
		Set<Integer> runtimeDomainSet = null;

		Properties domainProperties = new Properties();
		try {
			domainProperties.load(RobotsTxtCrawl.class.getResourceAsStream("/domain.properties"));
		} catch (Exception e) {
			FormatUtils.getInstance().logMemoryUsage("process() no domain.properties file found");
			return;
		}

		String execDomainIds = domainProperties.getProperty("exec.domain");
		String notExecDomainIds = domainProperties.getProperty("notexec.domain");
		if (StringUtils.isNotBlank(domainIdsString)) {
			execDomainIds = domainIdsString;
			notExecDomainIds = null;
		}
		FormatUtils.getInstance().logMemoryUsage("process() execDomainIds=" + execDomainIds);
		FormatUtils.getInstance().logMemoryUsage("process() notExecDomainIds=" + notExecDomainIds);

		List<OwnDomainEntity> allOwnDomainEntityList = ownDomainEntityDAO.queryForAll();
		if (allOwnDomainEntityList == null || allOwnDomainEntityList.size() == 0) {
			FormatUtils.getInstance().logMemoryUsage("process() allOwnDomainEntityList is empty.");
			return;
		}

		// process specific domains
		if (StringUtils.isNotBlank(execDomainIds)) {
			runtimeDomainSet = CommonUtils.getDomainSet(execDomainIds);
			isExecDomainIdsInd = true;
		}
		// do not process specific domains
		else if (StringUtils.isNotBlank(notExecDomainIds)) {
			runtimeDomainSet = CommonUtils.getDomainSet(notExecDomainIds);
			isExecDomainIdsInd = false;
		}
		// process all domains
		else {
			runtimeDomainSet = null;
			isExecDomainIdsInd = null;
		}

		List<OwnDomainEntity> filteredOwnDomainEntityList = PutMessageUtils.getInstance().filterDomains(allOwnDomainEntityList, isExecDomainIdsInd, runtimeDomainSet);
		if (filteredOwnDomainEntityList == null || filteredOwnDomainEntityList.size() == 0) {
			FormatUtils.getInstance().logMemoryUsage("process() filteredOwnDomainEntityList is empty.");
			return;
		}

		// initialize the cached DAOs
		RobotsTxtClickHouseDAO.getInstance();
		PutMessageUtils.getInstance();
		CacheModleFactory.getInstance();
		HtmlChangeClickHouseDAO.getInstance();

		// for all domains' target URLs, update the 'test_target_url_html_daily' table with the latest crawl results
		processDomainsConcurrently(filteredOwnDomainEntityList);

		if (CacheModleFactory.getInstance().getRobotsTxtClickHouseEntityList() != null
				&& CacheModleFactory.getInstance().getRobotsTxtClickHouseEntityList().size() > 0) {
			FormatUtils.getInstance().logMemoryUsage("process() CacheModleFactory.getInstance().getRobotsTxtClickHouseEntityList().size()="
					+ CacheModleFactory.getInstance().getRobotsTxtClickHouseEntityList().size());
			RobotsTxtClickHouseDAO.getInstance().createBatch(CacheModleFactory.getInstance().getRobotsTxtClickHouseEntityList(), TABLE_NAME);
		} else {
			FormatUtils.getInstance().logMemoryUsage("process() CacheModleFactory.getInstance().getRobotsTxtClickHouseEntityList() is empty.");
		}

		if (CacheModleFactory.getInstance().getTargetUrlChangeIndClickHouseEntityList() != null
				&& CacheModleFactory.getInstance().getTargetUrlChangeIndClickHouseEntityList().size() > 0) {
			FormatUtils.getInstance().logMemoryUsage("process() CacheModleFactory.getInstance().getTargetUrlChangeIndClickHouseEntityList().size()="
					+ CacheModleFactory.getInstance().getTargetUrlChangeIndClickHouseEntityList().size());
			TargetUrlChangeIndClickHouseDAO.getInstance().createBatch(null, CacheModleFactory.getInstance().getTargetUrlChangeIndClickHouseEntityList(), TABLE_NAME);
			final int size = htmlChangeQueue.size();
			List<HtmlChange> htmlChangeList = new ArrayList<>(size);
			try {
				for (int i = 0; i < size; i++) {
					htmlChangeList.add(htmlChangeQueue.poll());
				}
				HtmlChangeClickHouseDAO.getInstance().createBatch(htmlChangeList);
			} catch (Exception e) {
				log.error("process() HtmlChangeClickHouseDAO.getInstance().createBatch() error", e);
			}
		} else {
			FormatUtils.getInstance().logMemoryUsage("process() CacheModleFactory.getInstance().getTargetUrlChangeIndClickHouseEntityList() is empty.");
		}
	}

	private void processDomainsConcurrently(List<OwnDomainEntity> ownDomainEntityList) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("processDomainsConcurrently() begins. ownDomainEntityList.size()=" + ownDomainEntityList.size());

		int totalNumberOfDomains = ownDomainEntityList.size();
		long startTimestamp = System.currentTimeMillis();
		String ip = null;
		OwnDomainEntity ownDomainEntity = null;
		RobotsTxtCrawlCommand robotsTxtCrawlCommand = null;
		int numberOfDomainsProcessed = 0;

		do {
			ip = CacheModleFactory.getInstance().getAliveIpAddress();
			if (ip == null) {
				continue;
			}
			ownDomainEntity = ownDomainEntityList.get(numberOfDomainsProcessed++);
			robotsTxtCrawlCommand = getRobotsTxtCrawlCommand(ip, ownDomainEntity);
			threadPoolService.execute(robotsTxtCrawlCommand);
		} while (numberOfDomainsProcessed < totalNumberOfDomains);

		do {
			try {
				Thread.sleep(15000);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
			FormatUtils.getInstance().logMemoryUsage("processDomainsConcurrently() active threads=" + threadPoolService.getThreadPool().getActiveCount());
		} while (threadPoolService.getThreadPool().getActiveCount() > 0);

		FormatUtils.getInstance().logMemoryUsage("processDomainsConcurrently() end. elapsed time in seconds=" + ((System.currentTimeMillis() - startTimestamp) / 1000));
	}

	private RobotsTxtCrawlCommand getRobotsTxtCrawlCommand(String ip, OwnDomainEntity ownDomainEntity) {
		RobotsTxtCrawlCommand robotsTxtCrawlCommand = new RobotsTxtCrawlCommand(ip, ownDomainEntity);
		robotsTxtCrawlCommand.setStatus(true);
		return robotsTxtCrawlCommand;
	}
}
