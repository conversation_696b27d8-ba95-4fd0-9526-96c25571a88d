package com.actonia.polite.crawl;

import com.actonia.IConstants;
import com.actonia.dao.*;
import com.actonia.entity.*;
import com.actonia.utils.*;
import com.actonia.value.object.*;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.sqs.model.ListQueuesResult;
import com.amazonaws.services.sqs.model.Message;
import com.google.gson.Gson;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.text.ParseException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


public class PoliteCrawlRetryDeadQueue {

    private static final Logger log = LogManager.getLogger(PoliteCrawlRetryDeadQueue.class);
    private static final int CLICKHOUSE_BATCH_INSERT_SIZE = ClickHouseConfigurations.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_BATCH_CREATION_SIZE, 10000);
    private static final int CONCURRENT_THREADS = 128;
    private static final int BATCH_UPDATE_DISABLE_CRAWL_SIZE = 200;
    private final ThreadPoolExecutor threadPoolExecutor;
    private final Gson gson = new Gson();
    private final TargetUrlHtmlClickHouseDAO targetUrlHtmlClickHouseDAO;
    private final Queue<HtmlClickHouseEntity> htmlClickHouseEntities = new LinkedBlockingQueue<>();
    private final SQSUtils sqsUtils;
    private final Queue<PoliteCrawlStateLog> politeCrawlStateLogs = new LinkedBlockingQueue<>();
    private final PoliteCrawlStateLogDAO politeCrawlStateLogDAO;
    private final Queue<TargetUrlEntity> disableCrawlInitialOnlyTargetUrlEntities = new LinkedBlockingQueue<>();
    private final HtmlBigDataClickHouseDAO bigDataClickHouseDAO;
    private final Set<Integer> bigDataIndicatorIdSet;
    private final HtmlClickHouseDAO htmlClickHouseDAO;
    private final TargetUrlEntityDAO targetUrlEntityDAO;
    private final AmazonS3 s3client = CrawlHtmlSeagateUtils.s3client;
    private boolean shutdown = false;
    private final Queue<HtmlFileNameClickHouseEntity> htmlFileNameClickHouseEntityQueue = new LinkedBlockingQueue<>();
    private final TargetUrlHtmlFileNameClickHouseDAO htmlFileNameClickHouseDAO;
    private final List<Future<?>> futures = new ArrayList<>();
    private final ConcurrentHashMap<Integer, Map<String, Date>> domainIdUrlHashCodeLastCrawledMap = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<Integer, Set<String>> politeCrawlStateLogTodayCrawledMap = new ConcurrentHashMap<>();
    public static final List<String> previousFieldNameList = getPreviousFieldNames();

    public PoliteCrawlRetryDeadQueue(int concurrentThreads) {
        try {
            targetUrlHtmlClickHouseDAO = TargetUrlHtmlClickHouseDAO.getInstance();
            bigDataClickHouseDAO = HtmlBigDataClickHouseDAO.getInstance();
            final Set<ChangeIndMaster> bigDataIndicatorSet = ChangeIndMasterClickHouseDAO.getInstance().bigDataIndicatorSet;
            bigDataIndicatorIdSet = bigDataIndicatorSet.stream().map(ChangeIndMaster::getChgId).collect(Collectors.toSet());
            htmlClickHouseDAO = HtmlClickHouseDAO.getInstance();
            htmlFileNameClickHouseDAO = TargetUrlHtmlFileNameClickHouseDAO.getInstance();
            sqsUtils = SQSUtils.getInstance();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        politeCrawlStateLogDAO = SpringBeanFactory.getBean("politeCrawlStateLogDAO");
        threadPoolExecutor = new ThreadPoolExecutor(concurrentThreads, concurrentThreads * 2, 120L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(concurrentThreads * 2), new ThreadPoolExecutor.DiscardPolicy());
        targetUrlEntityDAO = SpringBeanFactory.getBean("targetUrlEntityDAO");
    }

    public static void main(String[] args) throws Exception {
        int concurrentThreads = CONCURRENT_THREADS;
        if (args.length > 0) {
            concurrentThreads = NumberUtils.toInt(args[0]);
        }
        final PoliteCrawlRetryDeadQueue politeCrawlRetryDeadQueue = new PoliteCrawlRetryDeadQueue(concurrentThreads);
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            final int size = politeCrawlRetryDeadQueue.threadPoolExecutor.getQueue().size();
            int activeCount = politeCrawlRetryDeadQueue.threadPoolExecutor.getActiveCount();
            System.out.println("Received SIGTERM signal. threadPoolExecutor still has " + size + " tasks " + activeCount + " active");
            politeCrawlRetryDeadQueue.shutdown = true;
        }));
        politeCrawlRetryDeadQueue.run();
        log.info("PoliteCrawlRetryDeadQueue finished.");
    }

    private void run() throws InterruptedException {
        final List<String> deadQueueList = getQueueListByQueueNamePrefix();
        if (deadQueueList.isEmpty()) {
            log.warn("no queue found exiting...");
            return;
        }
        // order deadQueueList by message number
        final List<QueueMessages> messages = deadQueueList.parallelStream().map(queueUrl -> {
            try {
                final Integer[] approximateNumberOfMessagesAndInflightByQueueUrl = sqsUtils.getApproximateNumberOfMessagesAndInflightByQueueUrl(queueUrl);
                final QueueMessages queueMessages = new QueueMessages();
                queueMessages.setMessages(approximateNumberOfMessagesAndInflightByQueueUrl[0]);
                queueMessages.setQueueUrl(queueUrl);
                queueMessages.setInFlight(approximateNumberOfMessagesAndInflightByQueueUrl[1]);
                return queueMessages;
            } catch (Exception e) {
                log.error("queueUrl: {}, error: {}", queueUrl, e.getMessage());
                return null;
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());
        LinkedBlockingQueue<String> queueUrlStringLinkedBlockingQueue = messages.stream()
                .parallel()
                .sorted(Comparator.comparingInt(QueueMessages::getMessages).reversed())
                .map(QueueMessages::getQueueUrl).collect(Collectors.toCollection(LinkedBlockingQueue::new));
        log.info("Submitting all queue URLs for processing. Total URLs: {}", queueUrlStringLinkedBlockingQueue.size());

        // Submit all queue URLs for processing at once
        while (!queueUrlStringLinkedBlockingQueue.isEmpty()) {
            final String queueUrl = queueUrlStringLinkedBlockingQueue.poll();
            if (queueUrl == null) {
                break;
            }

            // Check if we need to wait for some capacity in the thread pool
            while (threadPoolExecutor.getQueue().remainingCapacity() <= 0) {
                log.info("Thread pool queue is full. Waiting for capacity...");
                try {
                    Thread.sleep(5000); // Wait 5 seconds before checking again
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("Interrupted while waiting for thread pool capacity");
                    break;
                }

                // Check if we should stop processing
                if (shutdown) {
                    log.warn("Shutdown signal received while waiting for thread pool capacity");
                    break;
                }
            }

            // If we should stop processing, break out of the loop
            if (shutdown) {
                break;
            }

            // Submit the task
            final Future<?> submit = threadPoolExecutor.submit(() -> processDeadQueueMessages(queueUrl));
            futures.add(submit);
            log.info("Submitted queue URL: {}", queueUrl);
        }

        log.info("All queue URLs have been submitted or processing was interrupted. Total submitted: {}", futures.size());
        shutdown = true;

        // Wait for all tasks to complete
        while (futures.stream().anyMatch(future -> !future.isDone())) {
            try {
                Thread.sleep(1000L);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("Interrupted while waiting for futures to complete");
                break;
            }
        }

        // Gracefully shutdown the executor
        log.info("Attempting to shut down thread pool executor...");
        threadPoolExecutor.shutdown(); // Disable new tasks from being submitted
        log.info("Waiting for all tasks to complete (up to 55 minutes)...");
        try {
            // Wait a reasonable time for existing tasks to terminate
            if (!threadPoolExecutor.awaitTermination(55, TimeUnit.MINUTES)) {
                log.warn("Thread pool did not terminate in 55 minutes. Forcing shutdown...");
                List<Runnable> droppedTasks = threadPoolExecutor.shutdownNow(); // Cancel currently executing tasks
                log.warn("shutdownNow() was called. {} tasks were awaiting execution.", droppedTasks.size());
                // Wait a little more for tasks to respond to being cancelled
                if (!threadPoolExecutor.awaitTermination(5, TimeUnit.MINUTES)) {
                    log.error("Thread pool did not terminate even after forcing shutdownNow().");
                }
            } else {
                log.info("All tasks completed successfully.");
            }
        } catch (InterruptedException ie) {
            log.warn("Shutdown await was interrupted. Forcing shutdownNow()...");
            threadPoolExecutor.shutdownNow();
            Thread.currentThread().interrupt(); // Preserve interrupt status
        } finally {
            log.info("Thread pool executor shutdown process completed.");

            // Perform final data persistence after all tasks are processed and executor is shutdown
            synchronized (PoliteCrawlRetryDeadQueue.class) {
                log.info("insertRemainUnsavedData after executor shutdown.");
                insertRemainUnsavedData();
            }
        }
        log.info("PoliteCrawlRetryDeadQueue.run finished.");
    }

    /**
     * Retrieves all queue URLs by queue name prefix.
     *
     * @return A list of queue URLs.
     */
    private List<String> getQueueListByQueueNamePrefix() {
        final List<String> result = new ArrayList<>();
        String nextToken = null;
        while (true) {
            final ListQueuesResult queueListByQueueNamePrefix;
            try {
                queueListByQueueNamePrefix = sqsUtils.getQueueListByQueueNamePrefix(IConstants.QUEUE_NAME_TARGET_URL_DEAD_QUEUES_PREFIX, 1000, nextToken);
                result.addAll(queueListByQueueNamePrefix.getQueueUrls());
                nextToken = queueListByQueueNamePrefix.getNextToken();
                if (nextToken == null) {
                    log.info("no new queue found return {} queueUrls", result.size());
//                    Collections.reverse(result);
                    return result;
                }
                log.info("getQueueListByQueueNamePrefix nextToken: {}", nextToken);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * Retrieves all the messages from a dead message queue that are older than 5 hours.
     *
     * @return a list of messages from the dead message queue that are older than 5 hours
     */
    private List<TargetUrlDeadQueueDTO> getDeadQueueMessages(String queueUrl) {
        final LocalDateTime now = LocalDateTime.now();
        while (true) {
            try {
                final List<Message> messageFromQueue = sqsUtils.getMessageFromQueue(queueUrl, 10, 18000);
                if (messageFromQueue.isEmpty()) {
                    return Collections.emptyList();
                }
                // return messages that were sent more than 5 hours ago
                List<TargetUrlDeadQueueDTO> messages = filterMessages(messageFromQueue, now);
                if (!messages.isEmpty()) {
                    log.info("get {} messages from queueUrl: {}", messages.size(), queueUrl);
                    return messages;
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }

    private List<TargetUrlDeadQueueDTO> filterMessages(List<Message> messageFromQueue, LocalDateTime now) {
        return messageFromQueue.stream()
                .map(message -> {
                    final String body = message.getBody();
                    // replace java.sql.Date format trackDate to java.util.Date format String
                    final String json = body.replaceAll("(\"trackDate\":\"\\w+ \\d+, \\d+)\"", "$1 12:00:00 AM\"");
                    final TargetUrlDeadQueueDTO targetUrlDeadQueueDTO = gson.fromJson(json, TargetUrlDeadQueueDTO.class);
                    targetUrlDeadQueueDTO.setReceiptHandle(message.getReceiptHandle());
                    if (log.isDebugEnabled()) {
                        log.debug("targetUrlDeadQueueDTO: {}", targetUrlDeadQueueDTO);
                    }
                    return targetUrlDeadQueueDTO;
                })
                .filter(targetUrlDeadQueueDTO -> {
                    final LocalDateTime sendMessageTime = LocalDateTime.parse(targetUrlDeadQueueDTO.getSendMessageTime(), DateTimeFormatter.ofPattern(IConstants.DATE_FORMAT_YYYYMMDDHHMMSS));
                    // return true if the message was sent more than 5 hours ago, but for domainId is 4765 only return 1 hour ago messages
                    if (targetUrlDeadQueueDTO.getDomainId() == 4765) {
                        return sendMessageTime.plusHours(1).isBefore(now);
                    }
                    return sendMessageTime.plusHours(5).isBefore(now);
                }).collect(Collectors.toList());
    }

    private void processDeadQueueMessages(String queueUrl) {
        final Integer domainId = getDomainIdByQueueUrl(queueUrl);
        Integer messageCount = 0;
        StopWatch stopWatch = new StopWatch(String.valueOf(domainId));
        stopWatch.start();
        AtomicInteger processedCount = new AtomicInteger(0);
        boolean isEndThread = false;
        Map<String, Date> urlLastCrawledMap = domainIdUrlHashCodeLastCrawledMap.computeIfAbsent(domainId, k -> new ConcurrentHashMap<>());
        Set<String> todayCrawledUrls = politeCrawlStateLogTodayCrawledMap.computeIfAbsent(domainId, k -> new HashSet<>());
        try {
            if (urlLastCrawledMap.isEmpty()) {
                List<HtmlClickHouseEntity> htmlClickHouseEntityList = TargetUrlHtmlClickHouseDAO.getInstance().getLatestFromHistorical(domainId, previousFieldNameList);
                urlLastCrawledMap = htmlClickHouseEntityList.parallelStream().collect(Collectors.toMap(HtmlClickHouseEntity::getUrlMurmurHash, HtmlClickHouseEntity::getTrackDate, (s, s2) -> s2));
            }
            if (todayCrawledUrls.isEmpty()) {
                log.info("OID: {} polite_crawl_state_log set is empty, querying from table", domainId);
                final List<String> todayUrlMurmur3HashListByDomainId = politeCrawlStateLogDAO.findTodayUrlMurmur3HashListByDomainId(domainId);
                if (!CollectionUtils.isEmpty(todayUrlMurmur3HashListByDomainId)) {
                    todayCrawledUrls = new HashSet<>(todayUrlMurmur3HashListByDomainId);
                }
                log.info("todayCrawledUrls size: {}, domainId: {}", todayCrawledUrls.size(), domainId);
            }
        } catch (Exception e) {
            log.error("domainId = {} error: {}", domainId, e.getMessage(), e);
            throw new RuntimeException(e);
        }
        while (!shutdown && !isEndThread) {
            try {
                final List<TargetUrlDeadQueueDTO> targetUrlDeadQueueDTOList = getDeadQueueMessages(queueUrl);
                if (targetUrlDeadQueueDTOList.isEmpty()) {
                    log.warn("domainId = {} No messages in dead queue.", domainId);
                    break;
                }
                final List<Message> messages = new ArrayList<>();
                for (TargetUrlDeadQueueDTO urlDeadQueueDTO : targetUrlDeadQueueDTOList) {
                    final Message message = new Message();
                    final String receiptHandle = urlDeadQueueDTO.getReceiptHandle();
                    message.setReceiptHandle(receiptHandle);
                    processedCount.getAndIncrement();
                    messages.add(message);
                    messageCount++;
                    final String url = urlDeadQueueDTO.getUrlMetricsEntityV3().getUrl();
                    final String urlMurmurHash = MurmurHashUtils.getMurmurHash3_64(url);
                    final Date lastCrawlDate = urlLastCrawledMap.get(urlMurmurHash);
                    if (lastCrawlDate != null && !lastCrawlDate.before(DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH))) {
                        // skip message if url is already processed
                        log.warn("domainId = {} url = {} is already crawled in dis_html, skip processed message.", domainId, url);
                        continue;
                    }
                    if (todayCrawledUrls.contains(urlMurmurHash)) {
                        // skip message if url is already processed
                        log.warn("domainId = {} url = {} is already processed in polite_crawl_state_log today, skip message.", domainId, url);
                        continue;
                    }
                    final PoliteCrawlStateLog politeCrawlStateLog = handleMessage(urlDeadQueueDTO);
                    final Integer responseCode = politeCrawlStateLog.getResponseCode();
                    politeCrawlStateLogs.add(politeCrawlStateLog);
                    if (responseCode == 429) {
                        log.info("domain {} crawl need stop", domainId);
                        try {
                            log.info("domainId = {} Delete from dead queue. cause: 429 responseCode, queueUrl = {}, messages: {}", domainId, queueUrl, gson.toJson(messages));
                            sqsUtils.deleteMessagesFromQueue(queueUrl, messages, 10);
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                        isEndThread = true;
                        log.warn("isEndThread = true, break processDeadQueueMessages thread, domainId: {}", domainId);
                        break;
                    }
                    if (shutdown) {
                        log.warn("shutdown is true after, exit handleMessage, domainId: {}", domainId);
                        break;
                    }
                }
                // delete messages after all messages are processed
                sqsUtils.deleteMessagesFromQueue(queueUrl, messages, 10);
                log.info("domainId = {} Delete {} messages from dead queue. queueUrl = {}", domainId, messages.size(), queueUrl);
                messages.clear();
            } catch (Exception e) {
                log.error("processDeadQueueMessages error", e);
                break;
            }
            if (shutdown) {
                log.warn("shutdown is true, exit processDeadQueueMessages thread, domainId: {}", domainId);
                break;
            } else {
                log.info("shutdown is false, processDeadQueueMessages thread is keep running, domainId: {}, already processed {} messages", domainId, processedCount.get());
            }
        }
        this.cleanDeadQueue(queueUrl);

        stopWatch.stop();
        // print domain messages count
        log.info("queueUrl = {} finished, messageCount = {}, processedCount = {}, time seconds = {}", queueUrl, messageCount, processedCount.get(), stopWatch.getTotalTimeSeconds());
    }

    private void cleanDeadQueue(String queueUrl) {
        try {
            // 1. check still have messages in dead queue
            final Integer[] approximateNumberOfMessagesAndInflightByQueueUrl = sqsUtils.getApproximateNumberOfMessagesAndInflightByQueueUrl(queueUrl);
            // 2. if no messages in dead queue, delete queue
            final Integer numberOfMessages = approximateNumberOfMessagesAndInflightByQueueUrl[0];
            final Integer numberOfInflightMessages = approximateNumberOfMessagesAndInflightByQueueUrl[1];
            if (numberOfMessages == 0 && numberOfInflightMessages == 0) {
                sqsUtils.deleteQueueByUrl(queueUrl);
                log.warn("delete queue by queueUrl = {}", queueUrl);
            } else {
                log.info("queueUrl = {} still have {} messages in dead queue. {} inflight messages", queueUrl, numberOfMessages, numberOfInflightMessages);
            }
        } catch (Exception e) {
            log.error("get ApproximateNumberOfMessagesAndInflightByQueueUrl error", e);
        }
    }

    private Integer getDomainIdByQueueUrl(String queueUrl) {
        final String regex = IConstants.QUEUE_NAME_TARGET_URL_DEAD_QUEUES_PREFIX + "(\\d+)$";
        final Pattern pattern = Pattern.compile(regex);
        final Matcher matcher = pattern.matcher(queueUrl);
        if (matcher.find()) {
            final String domainIdString = matcher.group(1);
            return Integer.parseInt(domainIdString);
        }
        return 0;
    }

    private PoliteCrawlStateLog handleMessage(TargetUrlDeadQueueDTO targetUrlDeadQueueDTO) {
        final int domainId = targetUrlDeadQueueDTO.getDomainId();
        final DomainCrawlParameters domainCrawlParameters = targetUrlDeadQueueDTO.getDomainCrawlParameters();
        final int delayInSecondsPerHttpRequest = domainCrawlParameters.getDelayInSecondsPerHttpRequest();
        if (delayInSecondsPerHttpRequest > 0) {
            // this domain need to sleep for a while before crawl each url
            log.info("domainId = {}, delayInSecondsPerHttpRequest = {}", domainId, delayInSecondsPerHttpRequest);
            try {
                Thread.sleep(delayInSecondsPerHttpRequest * 1000L);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        return crawlHtml(targetUrlDeadQueueDTO);
    }

    private void insertRemainUnsavedData() {
        if (!this.htmlClickHouseEntities.isEmpty()) {
            log.info("reminding {} htmlClickHouseEntities need to insert before finished", this.htmlClickHouseEntities.size());
            this.batchInsertHtmlClickHouseList();
        }
        if (!this.disableCrawlInitialOnlyTargetUrlEntities.isEmpty()) {
            log.info("reminding {} disableCrawlInitialOnlyTargetUrlEntities need to update before finished", this.disableCrawlInitialOnlyTargetUrlEntities.size());
            this.batchUpdateDisableCrawlInitialOnlyTargetUrl();
        }
        if (!PoliteCrawl.getHtmlBigDataQueue().isEmpty()) {
            log.info("reminding {} htmlBigDataQueue need to insert before finished", PoliteCrawl.getHtmlBigDataQueue().size());
            this.batchInsertHtmlBigDataList();
        }
        if (!PoliteCrawl.getHtmlChangeQueue().isEmpty()) {
            log.info("reminding {} htmlChangeQueue need to insert before finished", PoliteCrawl.getHtmlChangeQueue().size());
            this.batchInsertHtmlChangeList();
        }
        // check crawl status code last time when the crawl is finished
        if (!this.politeCrawlStateLogs.isEmpty()) {
            log.info("reminding {} politeCrawlStateLogs need to insert before finished", this.politeCrawlStateLogs.size());
            this.batchInsertStateLog();
        }
    }

    private void batchInsertHtmlBigDataList() {
        List<HtmlBigData> htmlBigDataList = new ArrayList<>();
        final ConcurrentLinkedQueue<HtmlBigData> htmlBigDataQueue = PoliteCrawl.getHtmlBigDataQueue();
        int htmlBigDataQueueSize = htmlBigDataQueue.size();
        for (int i = 0; i < htmlBigDataQueueSize; i++) {
            final HtmlBigData poll = htmlBigDataQueue.poll();
            if (poll != null) {
                htmlBigDataList.add(poll);
            }
        }
        final List<HtmlBigData> htmlBigDataListToCreate = filterExistBigData(htmlBigDataList);
        try {
            HtmlBigDataClickHouseDAO.getInstance().createBatch(htmlBigDataListToCreate);
            log.info("htmlBigDataList.size = {}, {} dis_html_big_data created, still have {} records in queue", htmlBigDataList.size(), htmlBigDataListToCreate.size(), htmlBigDataQueue.size());
        } catch (Exception e) {
            log.error("batchInsertHtmlBigDataList error", e);
            throw new RuntimeException(e);
        }
    }

    private PoliteCrawlStateLog crawlHtml(TargetUrlDeadQueueDTO targetUrlDeadQueueDTO) {
        final DomainCrawlParameters domainCrawlParameters = targetUrlDeadQueueDTO.getDomainCrawlParameters();
        final int domainId = targetUrlDeadQueueDTO.getDomainId();
        int previousResponseCodeNumber;
        boolean isDifferent;
        Boolean isJavascriptCrawler = domainCrawlParameters.getEnableJavascriptCrawl();
        final String region = domainCrawlParameters.getRegion();
        final StopWatch stopWatch = new StopWatch();

        stopWatch.start();

        // for target URLs crawl, the queue message contains previous crawl result of the change tracking fields or their MD5 hash codes in the 'target_url_html_daily' table
        final UrlMetricsEntityV3 urlMetricsEntityV3 = targetUrlDeadQueueDTO.getUrlMetricsEntityV3();
        String urlString = StringUtils.trim(urlMetricsEntityV3.getUrl());

        Date previousCrawlTimestamp = null;
        if (urlMetricsEntityV3.getCrawl_timestamp() != null) {
            try {
                previousCrawlTimestamp = DateUtils.parseDate(urlMetricsEntityV3.getCrawl_timestamp(), new String[]{IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS});
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
        }

        // retrieve previous crawled results from ClickHouse database that matches the domain ID and URL string.
        PoliteCrawlStateLog politeCrawlStateLog = null;
        try {
            Date currentTrackDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);

            HtmlClickHouseEntity htmlClickHouseEntityPrevious = targetUrlDeadQueueDTO.getHtmlClickHouseEntityPrevious();

            HtmlClickHouseEntity htmlClickHouseEntityCurrent = invokePageCrawlerApi(urlString, domainCrawlParameters, currentTrackDate, region);

            final String urlMurmurHash = MurmurHashUtils.getMurmurHash3_64(urlString);
            politeCrawlStateLog = new PoliteCrawlStateLog();
            politeCrawlStateLog.setCrawlDate(currentTrackDate);
            politeCrawlStateLog.setUrlMurmur3Hash(urlMurmurHash);
            politeCrawlStateLog.setUrlType(urlMetricsEntityV3.getUrlType());
            politeCrawlStateLog.setOwnDomainId(domainId);
            final Set<String> stateLogs = politeCrawlStateLogTodayCrawledMap.computeIfAbsent(domainId, k -> new HashSet<>());
            stateLogs.add(urlMurmurHash);

            // when the URL cannot be crawled.....
            if (htmlClickHouseEntityCurrent == null) {
                log.error("domainId: {}, crawlHtml() error--URL cannot be crawled, url={}", domainId, urlString);
                politeCrawlStateLog.setResponseCode(999);
                return politeCrawlStateLog;
            }
            htmlClickHouseEntityCurrent.setUrlMurmurHash(urlMurmurHash);

            // when URL can be crawled...
            final CrawlerResponse crawlerResponse = htmlClickHouseEntityCurrent.getCrawlerResponse();
            if (crawlerResponse == null) {
                log.error("domainId: {}, crawlHtml() error--crawlerResponse is null, url={}", domainId, urlString);
                politeCrawlStateLog.setResponseCode(999);
                return politeCrawlStateLog;
            }
            final Integer httpStatusCode = htmlClickHouseEntityCurrent.getHttpStatusCode();
            politeCrawlStateLog.setResponseCode(httpStatusCode);
            Map<String, Date> urlLastCrawledMap = domainIdUrlHashCodeLastCrawledMap.computeIfAbsent(domainId, k -> new ConcurrentHashMap<>());
            urlLastCrawledMap.putIfAbsent(urlString, currentTrackDate);

            // store 3xx,4xx,5xx htmls into seagate.
            // {oid}/{date}/{urlmurmur3hash}
            if (httpStatusCode > 200) {
                threadPoolExecutor.execute(() -> {
                    int retry = 0;
                    while (retry < 3) {
                        try {
                            final String s3Key = String.format("%s/%s/%s", domainId, NumberUtils.toInt(DateFormatUtils.format(currentTrackDate, IConstants.DATE_FORMAT_YYYYMMDD)), urlMurmurHash);
                            s3client.putObject(CrawlHtmlSeagateUtils.bucketName, s3Key, gson.toJson(htmlClickHouseEntityCurrent));
                            break;
                        } catch (Exception e) {
                            log.error("store 3xx,4xx,5xx htmls into seagate error--domainId = {}, currentTrackDate = {}, urlMurmurHash = {}, url = {}", domainId, currentTrackDate, urlMurmurHash, urlString, e);
                            try {
                                Thread.sleep(500);
                            } catch (InterruptedException ex) {
                                throw new RuntimeException(ex);
                            }
                            retry++;
                        }
                    }
                });
            }

            final boolean isFromRSSUrl = urlMetricsEntityV3.getSourceType() != null && urlMetricsEntityV3.getSourceType() == IConstants.TARGET_URL_SOURCE_TYPE_RSS;
            final boolean isInitialCrawlOnly = urlMetricsEntityV3.getInitialCrawlOnly() != null && urlMetricsEntityV3.getInitialCrawlOnly() == IConstants.TARGET_URL_INITIAL_CRAWL_ONLY_YES;
            final NewHtmlClickHouseEntity newHtmlClickhouseEntity = NewHtmlClickHouseEntity.createFromHtmlClickHouseEntity(htmlClickHouseEntityCurrent);
            if (isFromRSSUrl && isInitialCrawlOnly) {
                log.info("InitialCrawlOnlyUrl from RSS status code = {}, OID= {} , url = {}", httpStatusCode, domainId, urlString);
                if (httpStatusCode == 200) {
                    // disable crawl this url when it is from RSS, initial crawl only and HTTP status code 200
                    htmlClickHouseEntities.add(htmlClickHouseEntityCurrent);
                    PoliteCrawl.newHtmlClickHouseEntityQueue.add(newHtmlClickhouseEntity);
                    final List<HtmlBigData> htmlBigDataList = newHtmlClickhouseEntity.getHtmlBigDataList();
                    PoliteCrawl.getHtmlBigDataQueue().addAll(htmlBigDataList);
                    if (htmlClickHouseEntities.size() >= CLICKHOUSE_BATCH_INSERT_SIZE) {
                        this.batchInsertHtmlClickHouseList();
                    }
                    TargetUrlEntity targetUrl = new TargetUrlEntity();
                    targetUrl.setOwnDomainId(domainId);
                    targetUrl.setUrl(urlString);
                    disableCrawlInitialOnlyTargetUrlEntities.add(targetUrl);
                    if (disableCrawlInitialOnlyTargetUrlEntities.size() == BATCH_UPDATE_DISABLE_CRAWL_SIZE) {
                        this.batchUpdateDisableCrawlInitialOnlyTargetUrl();
                    }
                    log.info("InitialCrawlOnlyUrl from RSS status code is 200, OID= {} , url = {}", domainId, urlString);
                } else {
                    log.warn("InitialCrawlOnlyUrl from RSS status code is not 200, OID= {} , url = {}", domainId, urlString);
                }
                return politeCrawlStateLog;
            }

            if (previousCrawlTimestamp != null) {
                htmlClickHouseEntityCurrent.setPreviousCrawlTimestamp(previousCrawlTimestamp);
            }

            if (httpStatusCode >= IConstants.HTTP_STATUS_CODE_900) {
                // proceed when HTTP status code is 900, 901 or 999
                log.warn("status code > 900, domainId = {} url = {}", domainId, urlString);
            }
            // skip when HTTP status code > 599
            else if (httpStatusCode > IConstants.HTTP_STATUS_CODE_599) {
                log.error("domainId: {}, crawlHtml() skip url={}, HTTP status code is {}", domainId, urlString, httpStatusCode);
                return politeCrawlStateLog;
            }

            // when HTTP status code 429 and target URLs daily crawl
            String previousResponseCodeString;
            if (httpStatusCode == 429) {
                log.warn("domainId: {}, crawlHtml() HTTP status code 429, url= {}", domainId, urlString);
                // skip remains urls of this domain for a while retry next time of 5 hours when HTTP status code 429
                // the domain crawl need to be paused
                return politeCrawlStateLog;
            }
            // when the current status code is 408, 403 ,5xx, or 9xx and previous status code is 2xx, 3xx, or 404, skip this URL
            else if (httpStatusCode == 408 || httpStatusCode == 403 || (httpStatusCode >= 500 && httpStatusCode <= 599) || httpStatusCode == 900
                    || httpStatusCode == 999) {

                // when previous version was created
                if (htmlClickHouseEntityPrevious != null) {
                    previousResponseCodeString = htmlClickHouseEntityPrevious.getCrawlerResponse().getResponse_code();
                    previousResponseCodeNumber = NumberUtils.toInt(previousResponseCodeString);
                    // when previous response code <= 399 or 404, skip this URL
                    if ((previousResponseCodeNumber > 0 && previousResponseCodeNumber <= 399) || previousResponseCodeNumber == 404) {
                        if ((httpStatusCode >= 500 && httpStatusCode <= 599) || httpStatusCode == 408) {
                            log.warn("domainId: {}, crawlHtml() skip url={}, current response code={}, previous response code={}", domainId, urlString, httpStatusCode, previousResponseCodeNumber);
                            pause(urlString, httpStatusCode);
                            return politeCrawlStateLog;
                        }
                    }
                    // pause
                    else if ((httpStatusCode >= 500 && httpStatusCode <= 599) || httpStatusCode == 408) {
                        pause(urlString, httpStatusCode);
                    }
                }
            }

            // create a new record for the current crawled HTML content:
            // 1) when there is no previous crawled results, or
            // 2) when there is a previous crawled results that matches the URL and crawl results have changed since last crawl.
            ChangeTrackingHashCdJson[] changeTrackingHashCdJsonArray = CrawlerUtils.getInstance().getChangeTrackingHashCdJsonArray(htmlClickHouseEntityCurrent);
            if (changeTrackingHashCdJsonArray != null && changeTrackingHashCdJsonArray.length > 0) {
                htmlClickHouseEntityCurrent.setChangeTrackingHashCdJsonArray(changeTrackingHashCdJsonArray);
            }
            // update 'htmlClickHouseEntityCurrent' (pass by reference)
            if (htmlClickHouseEntityPrevious != null) {
                previousResponseCodeString = htmlClickHouseEntityPrevious.getCrawlerResponse().getResponse_code();
                isDifferent = CrawlerUtils.getInstance().trackChanges(null, htmlClickHouseEntityCurrent, urlMetricsEntityV3, changeTrackingHashCdJsonArray,
                        previousResponseCodeString, htmlClickHouseEntityPrevious.getTrackDate(), IConstants.CRAWL_TYPE_TARGET_URL_HTML, null, null);
            } else {
                isDifferent = true;
            }

            if (isDifferent) {
                htmlClickHouseEntities.add(htmlClickHouseEntityCurrent);
                // store big data for the current HTML content
                NewHtmlClickHouseEntity newHtmlClickHouseEntity = null;
                try {
                    newHtmlClickHouseEntity = newHtmlClickhouseEntity;
                } catch (Exception e) {
                    log.error("create newHtmlClickHouseEntity failed", e);
                }
                PoliteCrawl.newHtmlClickHouseEntityQueue.add(newHtmlClickHouseEntity);
                final List<HtmlBigData> bigDataList;
                try {
                    bigDataList = newHtmlClickHouseEntity.getHtmlBigDataList();
                    if (bigDataList != null && !bigDataList.isEmpty()) {
                        final ConcurrentLinkedQueue<HtmlBigData> htmlBigDataQueue = PoliteCrawl.getHtmlBigDataQueue();
                        htmlBigDataQueue.addAll(bigDataList);
                    }
                } catch (Exception e) {
                    log.error("create dis_html_big_data failed", e);
                }
                if (htmlClickHouseEntityPrevious != null) {
                    // if there is a previous HTML record, add it to the list of html_change table
                    List<HtmlChange> htmlChanges;
                    try {
                        htmlChanges = HtmlChange.buildHtmlChange(urlMetricsEntityV3, htmlClickHouseEntityCurrent);
                        if (!htmlChanges.isEmpty()) {
                            final ConcurrentLinkedQueue<HtmlChange> htmlChangeQueue = PoliteCrawl.getHtmlChangeQueue();
                            htmlChangeQueue.addAll(htmlChanges);
                            if (IConstants.URL_TYPE_MANAGED.equals(urlMetricsEntityV3.getUrlType())) {
                                // store htmlChanges to managedHtmlChangeQueue for GA/GSC
                                final List<ManagedHtmlChange> managedHtmlChangeList = htmlChanges.stream()
                                        .filter(htmlChange -> !bigDataIndicatorIdSet.contains(htmlChange.getChgId()))
                                        .map(ManagedHtmlChange::createFromHtmlChange).collect(Collectors.toList());
                                if (!managedHtmlChangeList.isEmpty()) {
                                    PoliteCrawl.managedHtmlChangeQueue.addAll(managedHtmlChangeList);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("Failed to build HtmlChange, url= {} \nprevious = {} \ncurrent = {}", urlString, gson.toJson(urlMetricsEntityV3), gson.toJson(htmlClickHouseEntityCurrent), e);
                    }
                }

                if (htmlClickHouseEntityCurrent.getCrawlerResponse() != null
                        && StringUtils.isNotBlank(htmlClickHouseEntityCurrent.getCrawlerResponse().getFile_name())) {
                    HtmlFileNameClickHouseEntity htmlFileNameClickHouseEntity = new HtmlFileNameClickHouseEntity();
                    htmlFileNameClickHouseEntity.setDomainId(htmlClickHouseEntityCurrent.getDomainId());
                    htmlFileNameClickHouseEntity.setUrl(htmlClickHouseEntityCurrent.getUrl());
                    htmlFileNameClickHouseEntity.setTrackDate(htmlClickHouseEntityCurrent.getTrackDate());
                    htmlFileNameClickHouseEntity.setCrawlTimestamp(htmlClickHouseEntityCurrent.getCrawlTimestamp());
                    htmlFileNameClickHouseEntity.setFileName(htmlClickHouseEntityCurrent.getCrawlerResponse().getFile_name());
                    htmlFileNameClickHouseEntity.setSign(IConstants.CLICKHOUSE_SIGN_POSITIVE_1);
                    htmlFileNameClickHouseEntityQueue.add(htmlFileNameClickHouseEntity);
                }
            }

            log.info("domainId: {}, crawlHtml() final results. url={} httpStatusCode={} region={} isJavascriptCrawler={} elapsed(s)={}",
                    domainId, urlString, httpStatusCode, region, isJavascriptCrawler, stopWatch.getTotalTimeSeconds());
        } catch (Exception e) {
            log.error("domainId: {}, crawlHtml() error", domainId, e);
        }
        return politeCrawlStateLog;
    }

    private synchronized void batchInsertHtmlChangeList() {
        List<HtmlChange> htmlChangeList = new ArrayList<>();
        final ConcurrentLinkedQueue<HtmlChange> htmlChangeQueue = PoliteCrawl.getHtmlChangeQueue();
        int htmlChangeQueueSize = htmlChangeQueue.size();
        for (int i = 0; i < htmlChangeQueueSize; i++) {
            final HtmlChange poll = htmlChangeQueue.poll();
            if (poll != null) {
                htmlChangeList.add(poll);
            }
        }
        try {
            HtmlChangeClickHouseDAO.getInstance().createBatch(htmlChangeList);
            log.info("{} dis_html_change created, still have {} records in queue", htmlChangeList.size(), htmlChangeQueue.size());
        } catch (Exception e) {
            log.error("batchInsertHtmlChangeList() error", e);
            throw new RuntimeException(e);
        }
    }

    private synchronized void batchInsertHtmlClickHouseList() {
        final List<HtmlClickHouseEntity> htmlClickHouseBatchInsertList = new ArrayList<>(CLICKHOUSE_BATCH_INSERT_SIZE);
        final List<NewHtmlClickHouseEntity> newHtmlClickHouseBatchInsertList = new ArrayList<>(CLICKHOUSE_BATCH_INSERT_SIZE);
        final List<HtmlFileNameClickHouseEntity> htmlFileNameClickHouseEntityList = new ArrayList<>(CLICKHOUSE_BATCH_INSERT_SIZE);
        final int listSize = htmlClickHouseEntities.size();
        final int size = Math.min(listSize, CLICKHOUSE_BATCH_INSERT_SIZE);
        for (int i = 0; i < size; i++) {
            final HtmlClickHouseEntity htmlClickHouseEntity = htmlClickHouseEntities.poll();
            if (htmlClickHouseEntity != null) {
                htmlClickHouseBatchInsertList.add(htmlClickHouseEntity);
            }
            final NewHtmlClickHouseEntity newHtmlClickHouseEntity = PoliteCrawl.newHtmlClickHouseEntityQueue.poll();
            if (newHtmlClickHouseEntity != null) {
                newHtmlClickHouseBatchInsertList.add(newHtmlClickHouseEntity);
            }

            final HtmlFileNameClickHouseEntity htmlFileNameClickHouseEntity = htmlFileNameClickHouseEntityQueue.poll();
            if (htmlFileNameClickHouseEntity != null) {
                htmlFileNameClickHouseEntityList.add(htmlFileNameClickHouseEntity);
            }
        }

        try {
            targetUrlHtmlClickHouseDAO.createBatch(null, null, htmlClickHouseBatchInsertList, null);
            log.info("insert dis_target_url_html ends. {} records.", htmlClickHouseBatchInsertList.size());
            htmlClickHouseDAO.createBatch(newHtmlClickHouseBatchInsertList, null);
            log.info("insert dis_html ends. {} records.", newHtmlClickHouseBatchInsertList.size());
            htmlFileNameClickHouseDAO.createBatch(null, null, htmlFileNameClickHouseEntityList, null);
            log.info("insert dis_target_url_html_file ends. {} records.", htmlFileNameClickHouseEntityList.size());
            this.batchInsertHtmlChangeList();
            this.batchInsertHtmlBigDataList();
            this.batchInsertStateLog();
        } catch (Exception e) {
            log.error("batchInsertHtmlClickHouseList() error", e);
            throw new RuntimeException(e);
        }
    }

    private void pause(String urlString, Integer httpStatusCode) {
        if (httpStatusCode < 900) {
            int pauseTimeInMilliseconds = IConstants.RETRY_HTTP_STATUS_403_5XX_WAIT_MILLISECONDS_TARGET_URL;
            log.warn("pause url={}, current response code={}, pause for (ms.)={}", urlString, httpStatusCode, pauseTimeInMilliseconds);
            try {
                Thread.sleep(pauseTimeInMilliseconds);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
    }

    private HtmlClickHouseEntity invokePageCrawlerApi(String urlString, DomainCrawlParameters domainCrawlParameters,
                                                      Date currentTrackDate, String region) {
        boolean isRequiredJavaUrlEncoder = false;
        final HtmlClickHouseEntity htmlClickHouseEntity = new HtmlClickHouseEntity();
        final Integer domainId = domainCrawlParameters.getDomainId();

        try {
            String trimmedUrlString = StringUtils.trimToEmpty(urlString);
            CrawlerUtils.getInstance();
            DecodedEncodedUrlValueObject decodedEncodedUrlValueObject = CrawlerUtils.getDecodedAndEncodedUrlString(trimmedUrlString, isRequiredJavaUrlEncoder);
            final boolean enableJavascriptCrawl = domainCrawlParameters.getEnableJavascriptCrawl() != null && domainCrawlParameters.getEnableJavascriptCrawl();
            ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, domainCrawlParameters.getQueueName(), decodedEncodedUrlValueObject.getEncodedUrlString(),
                    domainCrawlParameters.getUserAgent(), domainCrawlParameters.getAdditionalContentEntityList(), enableJavascriptCrawl, domainCrawlParameters.getJavascriptTimeoutInSecond(),
                    IConstants.CRAWL_TYPE_TARGET_URL_HTML, domainCrawlParameters.getCrawlerRequestHeaders(), true, null, false, region);
            if (scrapyCrawlerResponse == null) {
                log.error("invokePageCrawlerApi() error--domainId = {}, urlString={}, scrapyCrawlerResponse is null.", domainId, urlString);
                return htmlClickHouseEntity;
            }
            CrawlerResponse crawlerResponse = scrapyCrawlerResponse.getCrawlerResponse();
            if (crawlerResponse == null) {
                log.error("invokePageCrawlerApi() error--domainId = {}, urlString={}, crawlerResponse is null.", domainId, urlString);
                return htmlClickHouseEntity;
            }
            htmlClickHouseEntity.setDomainId(domainId);
            htmlClickHouseEntity.setTrackDate(currentTrackDate);
            htmlClickHouseEntity.setUrl(urlString);
            htmlClickHouseEntity.setCrawlerResponse(crawlerResponse);
            htmlClickHouseEntity.setCrawlTimestamp(new Date());
            htmlClickHouseEntity.setInternalLinkCount(CrawlerUtils.getInstance().getInternalLinkCount(trimmedUrlString, crawlerResponse));
            htmlClickHouseEntity.setSign(IConstants.CLICKHOUSE_SIGN_POSITIVE_1);
            final Integer httpStatusCode = htmlClickHouseEntity.getHttpStatusCode();
            if (httpStatusCode == null) {
                log.error("invokePageCrawlerApi() error--domainId = {}, urlString={}, htmlClickHouseEntity.getHttpStatusCode() is null.", domainId, urlString);
                return null;
            }
            if (httpStatusCode == 200) {
                // aggregate page analysis results
                PageAnalysisResult[] pageAnalysisResultArray = CrawlerUtils.getInstance().getPageAnalysisResultArray(crawlerResponse);
                if (pageAnalysisResultArray != null && pageAnalysisResultArray.length > 0) {
                    htmlClickHouseEntity.setPageAnalysisResultArray(pageAnalysisResultArray);
                    htmlClickHouseEntity
                            .setPageAnalysisResultsReverse(CrawlerUtils.getInstance().reversePageAnalysisResults(pageAnalysisResultArray));
                    htmlClickHouseEntity.setPageAnalysisFragmentsArray(CrawlerUtils.getInstance().getPageAnalysisFragmentsArray(crawlerResponse));
                }
            }
            htmlClickHouseEntity.setWeekOfYear(CommonUtils.calculateWeekOfYear(currentTrackDate));
            return htmlClickHouseEntity;
        } catch (Exception e) {
            log.error("invokePageCrawlerApi() error--domainId = {}, urlString={}, error message={}", domainId, urlString, e.getMessage());
            return htmlClickHouseEntity;
        }
    }


    /**
     * Batch inserts the URL status into the database.
     */
    private synchronized void batchInsertStateLog() {
        final int size = politeCrawlStateLogs.size();
        if (size == 0) {
            log.info("batchInsertStateLog() size is 0.");
            return;
        }
        StopWatch stopWatch = new StopWatch();
        final List<PoliteCrawlStateLog> batchInsertList = new ArrayList<>(size);
        stopWatch.start("addStateLogs");
        for (int i = 0; i < size; i++) {
            final PoliteCrawlStateLog politeCrawlStateLog = politeCrawlStateLogs.poll();
            if (politeCrawlStateLog != null) {
                batchInsertList.add(politeCrawlStateLog);
            } else {
                log.info("politeCrawlStateLogs.poll returns null.");
            }
        }
        stopWatch.stop();
        try {
            log.info("batchInsertStateLog() total={} urls.", batchInsertList.size());
            stopWatch.start("batchInsertStateLog");
            this.politeCrawlStateLogDAO.batchInsert(batchInsertList);
            stopWatch.stop();
            log.info("batchInsertStateLog() total={} urls. still have {} records in queue\n{}", batchInsertList.size(), politeCrawlStateLogs.size(), stopWatch.prettyPrint());
        } catch (Exception e) {
            log.error("batchInsertStateLog() error.", e);
        }
    }

    /**
     * Batch updates the disable crawl initial only target URL.
     */
    private synchronized void batchUpdateDisableCrawlInitialOnlyTargetUrl() {
        List<TargetUrlEntity> targetUrlEntityList = new ArrayList<>(BATCH_UPDATE_DISABLE_CRAWL_SIZE);
        final int size = Math.min(disableCrawlInitialOnlyTargetUrlEntities.size(), BATCH_UPDATE_DISABLE_CRAWL_SIZE);
        for (int i = 0; i < size; i++) {
            TargetUrlEntity targetUrlEntity = disableCrawlInitialOnlyTargetUrlEntities.poll();
            if (targetUrlEntity != null) {
                targetUrlEntityList.add(targetUrlEntity);
            }
        }
        log.info("CrawlInitialOnlyTargetUrl need to batch update disable crawl size={}", targetUrlEntityList.size());
        targetUrlEntityList.stream().collect(Collectors.groupingBy(TargetUrlEntity::getOwnDomainId)).forEach((domainId, targetUrls) -> {
            // batch update target url disable crawl for each domain
            final List<String> urlHashList = targetUrls.stream().map(targetUrl -> MurmurHashUtils.getMurmurHash3_64(targetUrl.getUrl())).collect(Collectors.toList());
            targetUrlEntityDAO.batchUpdateUrlDisableCrawl(domainId, urlHashList);
            log.info("domainId: {}, batchUpdateDisableCrawlInitialOnlyTargetUrl() success urlHashList.size={}", domainId, urlHashList.size());
        });
    }

    private List<HtmlBigData> filterExistBigData(List<HtmlBigData> htmlBigDataList) {
        List<HtmlBigData> htmlBigDataListToCreate = new ArrayList<>(htmlBigDataList.size());
        // need to filter bigData md5 already exist in dis_html_big_data table to avoid duplicate records on track_date
        final Map<Date, List<HtmlBigData>> groupByTrackDate = htmlBigDataList.stream().collect(Collectors.groupingBy(HtmlBigData::getTrackDate, Collectors.toList()));
        for (Map.Entry<Date, List<HtmlBigData>> e : groupByTrackDate.entrySet()) {
            final List<HtmlBigData> bigDataListByTrackDate = e.getValue();
            if (!bigDataListByTrackDate.isEmpty()) {
                final String trackDate = DateFormatUtils.format(e.getKey(), IConstants.DATE_FORMAT_YYYY_MM_DD);
                final List<String> urlMurmurHashList = bigDataListByTrackDate.parallelStream().map(HtmlBigData::getUrlMurmurHash)
                        .distinct().collect(Collectors.toList());
                // batch query to check if the md5 already exist in dis_html_big_data table, size = 1000
                ArrayList<HtmlBigData> htmlBigDataListExist = new ArrayList<>();
                int queryBatchSize = 1024;
                List<String> queryList = new ArrayList<>(queryBatchSize);
                for (String urlMurmurHash : urlMurmurHashList) {
                    queryList.add(urlMurmurHash);
                    if (queryList.size() == queryBatchSize) {
                        htmlBigDataListExist.addAll(getHtmlBigDataList(trackDate, queryList));
                        queryList = new ArrayList<>(queryBatchSize);
                    }
                }
                if (!queryList.isEmpty()) {
                    htmlBigDataListExist.addAll(getHtmlBigDataList(trackDate, queryList));
                }
                final Set<String> existMd5Set = htmlBigDataListExist.parallelStream().map(htmlBigData1 -> htmlBigData1.getUrlMurmurHash() + "::" + htmlBigData1.getMd5()).collect(Collectors.toSet());
                final List<HtmlBigData> bigDataList = bigDataListByTrackDate.parallelStream().filter(bigData -> !existMd5Set.contains(bigData.getUrlMurmurHash() + "::" + bigData.getMd5())).collect(Collectors.toList());
                htmlBigDataListToCreate.addAll(bigDataList);
                log.info("filterExistBigData: trackDate= {}, existMd5Set.size= {}, after filter bigDataList.size= {}", trackDate, existMd5Set.size(), bigDataList.size());
            }
        }
        return htmlBigDataListToCreate;
    }

    private List<HtmlBigData> getHtmlBigDataList(String trackDate, List<String> queryList) {
        List<HtmlBigData> htmlBigDataList = new ArrayList<>();
        try {
            htmlBigDataList = bigDataClickHouseDAO.queryMurmurHashAndMd5ByTrackDateInUrlList(trackDate, queryList);
        } catch (Exception e) {
            log.error("getHtmlBigDataList error", e);
            log.error("trackDate={}, queryList={}", trackDate, gson.toJson(queryList));
        }
        return htmlBigDataList;
    }

    public static List<String> getPreviousFieldNames() {
        List<String> previousFieldNameList = new ArrayList<>();
        previousFieldNameList.add(IConstants.URL_MURMUR_HASH);
        previousFieldNameList.add(IConstants.TRACK_DATE);
        previousFieldNameList.add(IConstants.RESPONSE_CODE);
        return previousFieldNameList;
    }

    @Data
    private static class QueueMessages {
        int messages;

        int inFlight;
        String queueUrl;

    }
}



