package com.actonia.polite.crawl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.apache.commons.lang.StringUtils;

import com.actonia.IConstants;
import com.actonia.concurrency.BaseThreadCommand;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.entity.AdditionalContentEntity;
import com.actonia.utils.CrawlerUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.value.object.AmazonAdHocCrawlValueObject;
import com.actonia.value.object.CheckStructuredData;
import com.actonia.value.object.CustomData;
import com.actonia.value.object.PageLink;
import com.actonia.value.object.ScrapyCrawlerResponse;
import com.actonia.value.object.StructuredData;

public class AmazonAdHocCrawlCommand extends BaseThreadCommand {

	//private boolean isDebug = false;
	private boolean isResponseAsHtml = false;
	private static final String userAgentGoogleBot = "Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)";
	private static final String NOT_AVAILABLE = "n/a";
	private static final String YES = "Yes";
	private static final String NO = "No";
	private static final String CONTENT_ADD_TO_CART = "Add to Cart";
	private int totalResponseCode999 = 0;
	private static final String DOT_AMAZON_DOT = ".amazon.";

	private String ip;
	private String domainName;

	public AmazonAdHocCrawlCommand(String ip, String domainName) {
		super();
		this.ip = ip;
		this.domainName = domainName;
	}

	@Override
	protected void undo() throws Exception {
		// TODO Auto-generated method stub
	}

	@Override
	protected void execute() {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("execute() begins. ip=" + ip + ",domainName=" + domainName);
		try {
			process();
		} catch (Exception e) {
			FormatUtils.getInstance().logMemoryUsage("execute() ip=" + ip + ",domainName=" + domainName + ",exception message=" + e.getMessage());
			e.printStackTrace();
		} finally {
			CacheModleFactory.getInstance().setAliveIpAddress(ip);
			FormatUtils.getInstance().logMemoryUsage(
					"execute() ends. ip=" + ip + ",domainName=" + domainName + ",elapsed time in sec.=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		}
	}

	private void process() throws Exception {
		AmazonAdHocCrawlValueObject amazonAdHocCrawlValueObject = null;
		String urlString = null;
		String outputLine = null;
		List<String> outputLineList = new ArrayList<String>();
		Set<String> urlHashCodeSet = null;
		if (AmazonAdHocCrawl.getDomainNameUrlHashSetMap().containsKey(domainName)) {
			urlHashCodeSet = AmazonAdHocCrawl.getDomainNameUrlHashSetMap().get(domainName);
			if (urlHashCodeSet != null && urlHashCodeSet.size() > 0) {
				FormatUtils.getInstance().logMemoryUsage("process() ip=" + ip + ",domainName=" + domainName + ",urlHashCodeSet.size()=" + urlHashCodeSet.size());
				nextUrlHashCode: for (String urlHashCode : urlHashCodeSet) {
					urlString = AmazonAdHocCrawl.getUrlHashCodeUrlStringMap().get(urlHashCode);
					amazonAdHocCrawlValueObject = invokePageCrawler(urlString);
					if (StringUtils.equalsIgnoreCase(amazonAdHocCrawlValueObject.getResponseCode(), "999")) {
						totalResponseCode999++;
					}
					outputLine = getOutputLine(amazonAdHocCrawlValueObject);
					outputLineList.add(outputLine);
					//if (isDebug == true) {
					//	System.out.println("process() amazonAdHocCrawlValueObject=" + amazonAdHocCrawlValueObject.toString());
					//	if (outputLineList.size() >= 1) {
					//		break nextUrlHashCode;
					//	}
					//}
				}
			}
			FormatUtils.getInstance().logMemoryUsage("process() ip=" + ip + ",domainName=" + domainName + ",outputLineList.size()=" + outputLineList.size());
			if (outputLineList != null && outputLineList.size() > 0) {
				AmazonAdHocCrawl.getOutputList().addAll(outputLineList);
			}
		}
	}

	private AmazonAdHocCrawlValueObject invokePageCrawler(String urlString) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		AmazonAdHocCrawlValueObject amazonAdHocCrawlValueObject = new AmazonAdHocCrawlValueObject();
		AdditionalContentEntity additionalContentEntity = null;
		String queueName = "";
		int domainId = 168;
		boolean isJavascriptCrawler = false;
		boolean isStoreHtml = false;
		String responseCode = null;
		List<AdditionalContentEntity> additionalContentEntityList = null;
		Integer javascriptTimeoutInSecond = 10;

		String crawledUrl = urlString;
		if (totalResponseCode999 >= 3) {
			responseCode = "999";
			amazonAdHocCrawlValueObject = getAmazonAdHocCrawlValueObject(crawledUrl, responseCode);
		} else {
			additionalContentEntityList = new ArrayList<AdditionalContentEntity>();

			// first selector - XPATH
			additionalContentEntity = new AdditionalContentEntity();
			additionalContentEntity.setDomainId(domainId);
			additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_XPATH);
			additionalContentEntity.setSelector("/html/body//*[text()[contains(.,'" + CONTENT_ADD_TO_CART + "')]]");
			additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_ALL);
			additionalContentEntityList.add(additionalContentEntity);

			ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(ip, queueName, urlString, userAgentGoogleBot,
					additionalContentEntityList, isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, null, isStoreHtml, null,
					isResponseAsHtml, null);
			if (scrapyCrawlerResponse != null && scrapyCrawlerResponse.getCrawlerResponse() != null) {
				amazonAdHocCrawlValueObject = getAmazonAdHocCrawlValueObject(scrapyCrawlerResponse, crawledUrl);
			} else {
				responseCode = NOT_AVAILABLE;
				amazonAdHocCrawlValueObject = getAmazonAdHocCrawlValueObject(crawledUrl, responseCode);
			}
		}
		FormatUtils.getInstance().logMemoryUsage("invokePageCrawler() ip=" + ip + ",domainName=" + domainName + ",urlString=" + urlString + ",responseCode="
				+ amazonAdHocCrawlValueObject.getResponseCode() + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		if (StringUtils.containsIgnoreCase(domainName, DOT_AMAZON_DOT)) {
			FormatUtils.getInstance()
					.logMemoryUsage("invokePageCrawler() ip=" + ip + ",domainName=" + domainName + ",urlString=" + urlString + ",pause eight seconds for amazon URL.");
			Thread.sleep(8000);
		}
		return amazonAdHocCrawlValueObject;
	}

	private String getOutputLine(AmazonAdHocCrawlValueObject amazonAdHocCrawlValueObject) {
		StringBuilder stringBuilder = new StringBuilder();
		String outputLine = null;
		stringBuilder = new StringBuilder();
		stringBuilder.append(amazonAdHocCrawlValueObject.getUrlString());
		stringBuilder.append(IConstants.BACKTICK);
		stringBuilder.append(amazonAdHocCrawlValueObject.getResponseCode());
		stringBuilder.append(IConstants.BACKTICK);

		if (StringUtils.equalsIgnoreCase(amazonAdHocCrawlValueObject.getResponseCode(), "200")) {
			stringBuilder.append(amazonAdHocCrawlValueObject.getTitle());
			stringBuilder.append(IConstants.BACKTICK);
			stringBuilder.append(amazonAdHocCrawlValueObject.getMetaDescription());
			stringBuilder.append(IConstants.BACKTICK);
			if (amazonAdHocCrawlValueObject.getSchemaTypeArray() != null && amazonAdHocCrawlValueObject.getSchemaTypeArray().length > 0) {
				stringBuilder.append(getSchemaTypes(amazonAdHocCrawlValueObject.getSchemaTypeArray()));
			} else {
				stringBuilder.append(IConstants.EMPTY_STRING);
			}
			stringBuilder.append(IConstants.BACKTICK);
			stringBuilder.append(amazonAdHocCrawlValueObject.getTotalPageLinks());
			stringBuilder.append(IConstants.BACKTICK);
			stringBuilder.append(amazonAdHocCrawlValueObject.getContentDetected());
			stringBuilder.append(IConstants.BACKTICK);
			stringBuilder.append(amazonAdHocCrawlValueObject.getTotalContentOccurrences());
		} else {
			stringBuilder.append(IConstants.EMPTY_STRING);
			stringBuilder.append(IConstants.BACKTICK);
			stringBuilder.append(IConstants.EMPTY_STRING);
			stringBuilder.append(IConstants.BACKTICK);
			stringBuilder.append(IConstants.EMPTY_STRING);
			stringBuilder.append(IConstants.BACKTICK);
			stringBuilder.append(IConstants.EMPTY_STRING);
			stringBuilder.append(IConstants.BACKTICK);
			stringBuilder.append(IConstants.EMPTY_STRING);
			stringBuilder.append(IConstants.BACKTICK);
			stringBuilder.append(IConstants.EMPTY_STRING);
		}

		outputLine = stringBuilder.toString();
		//System.out.println("o=" + outputLine);
		return outputLine;
	}

	private String getSchemaTypes(String[] schemaTypeArray) {
		StringBuilder stringBuilder = null;
		Set<String> schemaTypeSet = new HashSet<String>();
		for (String schemaType : schemaTypeArray) {
			schemaTypeSet.add(schemaType);
		}
		for (String schemaType : schemaTypeSet) {
			if (stringBuilder == null) {
				stringBuilder = new StringBuilder();
			} else {
				stringBuilder.append(IConstants.COMMA);
			}
			stringBuilder.append(schemaType);
		}
		return stringBuilder.toString();
	}

	private AmazonAdHocCrawlValueObject getAmazonAdHocCrawlValueObject(String crawledUrl, String responseCode) {
		AmazonAdHocCrawlValueObject amazonAdHocCrawlValueObject = new AmazonAdHocCrawlValueObject();
		amazonAdHocCrawlValueObject.setUrlString(crawledUrl);
		amazonAdHocCrawlValueObject.setResponseCode(responseCode);
		return amazonAdHocCrawlValueObject;
	}

	private AmazonAdHocCrawlValueObject getAmazonAdHocCrawlValueObject(ScrapyCrawlerResponse scrapyCrawlerResponse, String crawledUrl) {

		AmazonAdHocCrawlValueObject amazonAdHocCrawlValueObject = new AmazonAdHocCrawlValueObject();

		CustomData[] customDataArray = null;
		String customDataSelector = null;
		int totalContentOccurrences = 0;
		StructuredData structuredData = null;
		Set<String> schemaTypeSet = new HashSet<String>();
		List<String> schemaTypeList = new ArrayList<String>();
		PageLink[] pageLinkArray = null;
		String contentString = null;

		String responseCode = scrapyCrawlerResponse.getCrawlerResponse().getResponse_code();

		// URL
		amazonAdHocCrawlValueObject.setUrlString(crawledUrl);

		// response code		
		amazonAdHocCrawlValueObject.setResponseCode(responseCode);

		if (StringUtils.equalsIgnoreCase(responseCode, "200")) {

			// title
			if (StringUtils.isNotBlank(scrapyCrawlerResponse.getCrawlerResponse().getTitle())) {
				amazonAdHocCrawlValueObject.setTitle(FormatUtils.getInstance().trimText(scrapyCrawlerResponse.getCrawlerResponse().getTitle()));
			} else {
				amazonAdHocCrawlValueObject.setTitle(IConstants.EMPTY_STRING);
			}

			// meta description
			if (StringUtils.isNotBlank(scrapyCrawlerResponse.getCrawlerResponse().getDescription())) {
				amazonAdHocCrawlValueObject.setMetaDescription(FormatUtils.getInstance().trimText(scrapyCrawlerResponse.getCrawlerResponse().getDescription()));
			} else {
				amazonAdHocCrawlValueObject.setMetaDescription(IConstants.EMPTY_STRING);
			}

			// schema type array
			structuredData = scrapyCrawlerResponse.getCrawlerResponse().getStructured_data();
			if (structuredData != null && structuredData.getData() != null && structuredData.getData().getCheck_structured_data() != null
					&& structuredData.getData().getCheck_structured_data().length > 0) {
				for (CheckStructuredData checkStructuredData : structuredData.getData().getCheck_structured_data()) {
					if (StringUtils.isNotBlank(checkStructuredData.getData_type())) {
						schemaTypeSet.add(checkStructuredData.getData_type());
					}
				}
				if (schemaTypeSet != null && schemaTypeSet.size() > 0) {
					schemaTypeList = new ArrayList<String>(schemaTypeSet);
					amazonAdHocCrawlValueObject.setSchemaTypeArray(schemaTypeList.toArray(new String[0]));
				}
			}

			// total page links
			pageLinkArray = scrapyCrawlerResponse.getCrawlerResponse().getPage_link();
			if (pageLinkArray != null && pageLinkArray.length > 0) {
				amazonAdHocCrawlValueObject.setTotalPageLinks(pageLinkArray.length);
			}

			// content
			amazonAdHocCrawlValueObject.setContent(CONTENT_ADD_TO_CART);

			// contentDetected
			amazonAdHocCrawlValueObject.setContentDetected(NO);

			if (scrapyCrawlerResponse.getCrawlerResponse().getCustom_data() != null && scrapyCrawlerResponse.getCrawlerResponse().getCustom_data().length > 0) {
				customDataArray = scrapyCrawlerResponse.getCrawlerResponse().getCustom_data();
				for (CustomData customData : customDataArray) {
					customDataSelector = getCustomDataSelector(customData.getSelector());
					if (StringUtils.equalsIgnoreCase(customDataSelector, CONTENT_ADD_TO_CART) == true) {
						if (customData.getWord_count() > 0) {
							contentString = Arrays.toString(customData.getContent());
							totalContentOccurrences = StringUtils.countMatches(contentString, CONTENT_ADD_TO_CART);
							if (totalContentOccurrences > 0) {
								amazonAdHocCrawlValueObject.setContentDetected(YES);
							}
							//if (isDebug == true) {
							//	System.out.println("getAmazonAdHocCrawlValueObject() customData.getWord_count()=" + customData.getWord_count() + ",contentString="
							//			+ contentString + ",totalContentOccurrences=" + totalContentOccurrences);
							//}
							amazonAdHocCrawlValueObject.setTotalContentOccurrences(totalContentOccurrences);
						}
					}
				}
			}
		}

		return amazonAdHocCrawlValueObject;

	}

	private String getCustomDataSelector(String inputString) {
		String outputString = StringUtils.removeStart(inputString, "/html/body//*[text()[contains(.,'");
		return StringUtils.removeEnd(outputString, "')]]");

	}
}
