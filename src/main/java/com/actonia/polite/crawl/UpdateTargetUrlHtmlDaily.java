package com.actonia.polite.crawl;

import com.actonia.IConstants;
import com.actonia.dao.*;
import com.actonia.entity.*;
import com.actonia.utils.CommonUtils;
import com.actonia.utils.PutMessageUtils;
import com.actonia.utils.SpringBeanFactory;
import lombok.Getter;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.text.ParseException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/***
 *
 * update target URL daily crawl data with historical crawl data.
 *
 */
public class UpdateTargetUrlHtmlDaily {

    private static final Logger log = LogManager.getLogger(UpdateTargetUrlHtmlDaily.class);
    private final String sourceTableName = TargetUrlHtmlClickHouseDAO.TABLE_NAME;
    private final String destinationTableName = IConstants.TABLE_NAME_TARGET_URL_HTML_DAILY;
    private final TargetUrlHtmlClickHouseDAO targetUrlHtmlClickHouseDAO;
    private final TargetUrlClickHouseDAO targetUrlClickHouseDAO ;
    private final UrlLastChangedTimestampDAO urlLastChangedTimestampDAO;

    //private Boolean isDebug = false;

    private final OwnDomainEntityDAO ownDomainEntityDAO;
    private final TargetUrlEntityDAO targetUrlEntityDAO;
    private final OwnDomainTrackingDAO ownDomainTrackingDAO;
    // map key = yyyymmddhhmm
    // map value = list of HtmlClickHouseEntity to be persisted

    private final Date dailyDataCreationDate;
    private final String dailyDateString;

    public UpdateTargetUrlHtmlDaily(Date dailyDataCreationDate) {
        this.dailyDataCreationDate = dailyDataCreationDate;
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        targetUrlEntityDAO = SpringBeanFactory.getBean("targetUrlEntityDAO");
        ownDomainTrackingDAO = SpringBeanFactory.getBean("ownDomainTrackingDAO");
        try {
            urlLastChangedTimestampDAO = UrlLastChangedTimestampDAO.getInstance();
            targetUrlHtmlClickHouseDAO = TargetUrlHtmlClickHouseDAO.getInstance();
            targetUrlClickHouseDAO = TargetUrlClickHouseDAO.getInstance();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        this.dailyDateString = DateFormatUtils.format(this.dailyDataCreationDate, "yyyy-MM-dd");
    }

    public static void main(String args[]) throws ParseException {
        log.info("main() begins. java version={}, args:{}", System.getProperty("java.version"), StringUtils.join(args, ","));
        long startTimestamp = System.currentTimeMillis();
        Date dailyDataCreationDate = new Date();
        if (args.length > 0) {
            dailyDataCreationDate =  DateUtils.parseDateStrictly(args[0], new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
        }
        try {
            new UpdateTargetUrlHtmlDaily(dailyDataCreationDate).process(args);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        log.info("main() ends. elapsed time in sec.:{}", (System.currentTimeMillis() - startTimestamp) / 1000);
    }

    private void process(String args[]) throws Exception {
        long totalStarttimestamp = System.currentTimeMillis();
        long startTimestamp = 0L;
        int domainId = 0;
        String domainName = null;
        List<TargetUrlEntity> targetUrlEntityList = null;
        //List<HtmlClickHouseEntity> htmlClickHouseEntityList = null;
        //List<HtmlClickHouseEntity> testHtmlClickHouseEntityToBeCreatedList = null;
        //List<HtmlClickHouseEntity> htmlClickHouseEntityToBeCreatedList = new ArrayList<HtmlClickHouseEntity>();
        //Map<String, String> hashCodeTargetUrlClickHouseMap = null;
        //Map<String, String> hashCodeTargetUrlMySqlMap = null;
        int totalTargetUrlMySql = 0;
        String trimmedUrlString = null;

        String domainIdsString = null;

        if (args != null && args.length >= 2) {
            // runtime parameter (optional): list of domain IDs)
            domainIdsString = args[1];
        }
        log.info("process() runtime parameter:list of domain IDs={}", domainIdsString);


        Boolean isExecDomainIdsInd = null;
        Set<Integer> runtimeDomainSet = null;

        Properties domainProperties = new Properties();
        try {
            domainProperties.load(UpdateTargetUrlHtmlDaily.class.getResourceAsStream("/domain.properties"));
        } catch (Exception e) {
            log.error("process() no domain.properties file found");
            return;
        }

        String execDomainIds = domainProperties.getProperty("exec.domain");
        String notExecDomainIds = domainProperties.getProperty("notexec.domain");
        if (StringUtils.isNotBlank(domainIdsString)) {
            execDomainIds = domainIdsString;
            notExecDomainIds = null;
        }
        log.info("process() execDomainIds={}, notExecDomainIds={}", execDomainIds, notExecDomainIds);

        List<OwnDomainEntity> allOwnDomainEntityList = ownDomainEntityDAO.queryForAll();
        if (allOwnDomainEntityList == null || allOwnDomainEntityList.isEmpty()) {
            log.warn("process() allOwnDomainEntityList is empty");
            return;
        }

        // process specific domains
        if (StringUtils.isNotBlank(execDomainIds)) {
            runtimeDomainSet = CommonUtils.getDomainSet(execDomainIds);
            isExecDomainIdsInd = true;
        }
        // do not process specific domains
        else if (StringUtils.isNotBlank(notExecDomainIds)) {
            runtimeDomainSet = CommonUtils.getDomainSet(notExecDomainIds);
            isExecDomainIdsInd = false;
        }
        // process all domains
        else {
            runtimeDomainSet = null;
            isExecDomainIdsInd = null;
        }

        List<OwnDomainEntity> filteredOwnDomainEntityList = PutMessageUtils.getInstance().filterDomains(allOwnDomainEntityList, isExecDomainIdsInd, runtimeDomainSet);
        if (filteredOwnDomainEntityList == null || filteredOwnDomainEntityList.isEmpty()) {
            log.warn("process() filteredOwnDomainEntityList is empty");
            return;
        }

        log.info("filteredOwnDomainEntityList first id: {}", filteredOwnDomainEntityList.get(0).getId());

        // sort by domain id desc
        filteredOwnDomainEntityList.sort(Comparator.comparing(OwnDomainEntity::getId).reversed());

        log.info("filteredOwnDomainEntityList first id: {}", filteredOwnDomainEntityList.get(0).getId());

        // initialize the cached DAOs
        PutMessageUtils.getInstance();

        // reset the 'target_url' ClickHouse table
//        LocalTargetUrlClickHouseDAO.getInstance().resetTable(null);
//        // create the 'target_url' ClickHouse table for each domain
//        List<TargetUrlClickHouseEntity> targetUrlClickHouseEntityToBeCreatedList = new ArrayList<TargetUrlClickHouseEntity>();
//        TargetUrlClickHouseEntity targetUrlClickHouseEntity = null;
//        Integer targetUrlHtmlDailyDateNumber = NumberUtils.toInt(DateFormatUtils.format(this.dailyDataCreationDate, IConstants.DATE_FORMAT_YYYYMMDD));
//        log.info("process() targetUrlHtmlDailyDateNumber={},targetUrlHtmlDailyDate={}", targetUrlHtmlDailyDateNumber, this.dailyDataCreationDate);

//        String trackDateForNewUrl = DateFormatUtils.format(DateUtils.addDays(this.dailyDataCreationDate, -1), IConstants.DATE_FORMAT_YYYY_MM_DD);
        Map<Integer, Integer> domainIdTargetUrlCountMap = new HashMap<>();
        for (OwnDomainEntity ownDomainEntity : filteredOwnDomainEntityList) {
            domainId = ownDomainEntity.getId();
            targetUrlEntityList = targetUrlEntityDAO.getTargetUrlListWithoutDisableCrawl(domainId);
            final int urlSize = targetUrlEntityList.size();
            domainIdTargetUrlCountMap.put(domainId, urlSize);
//            if (urlSize > 0) {
//                totalTargetUrlMySql = urlSize;
//                targetUrlClickHouseEntityToBeCreatedList = new ArrayList<>();
//                for (TargetUrlEntity testTargetUrlEntity : targetUrlEntityList) {
//                    trimmedUrlString = StringUtils.trimToEmpty(testTargetUrlEntity.getUrl());
//                    targetUrlClickHouseEntity = new TargetUrlClickHouseEntity();
//                    targetUrlClickHouseEntity.setTrack_date(dailyDataCreationDate);
//                    targetUrlClickHouseEntity.setDomain_id(domainId);
//                    targetUrlClickHouseEntity.setUrl(trimmedUrlString);
//                    targetUrlClickHouseEntityToBeCreatedList.add(targetUrlClickHouseEntity);
//                }
//            }
            log.info("create dis_target_url: domainId={}, {} URLs in 't_target_url' table.", domainId, totalTargetUrlMySql);
//            // create data in 'target_url' Clickhouse table with domain ID and target URL string
//            targetUrlClickHouseDAO.createBatch(targetUrlClickHouseEntityToBeCreatedList, null);
//            targetUrlClickHouseEntityToBeCreatedList = new ArrayList<>();
        }
        log.info("create dis_target_url finished");
        // domain level processing
        for (OwnDomainEntity ownDomainEntity : filteredOwnDomainEntityList) {
            domainId = ownDomainEntity.getId();
            domainName = ownDomainEntity.getDomain();

            //hashCodeTargetUrlClickHouseMap = new HashMap<String, String>();
            //hashCodeTargetUrlMySqlMap = new HashMap<String, String>();
            //uniqueTargetUrlMySqlList = new ArrayList<String>();

            log.info("process() domainId={},domainName={}", domainId, domainName);

            // populate daily table (daily_data_creation_date = today's date) with historical data
            startTimestamp = System.currentTimeMillis();
            int urlSize = domainIdTargetUrlCountMap.get(domainId);
            if (urlSize == 0) {
                log.warn("domainId={}, totalHistoricalRecords == 0", domainId);
                continue;
            }

            try {
                // insert 'dis_target_url_html_daily' table
                update(domainId, urlSize);
                // insert the 'target_url_html_daily' table for the urls not in the 'dis_target_url_html_daily' table at previous step
//                targetUrlHtmlClickHouseDAO.createDailyDataForNewUrl(domainId, sourceTableName, destinationTableName, trackDateForNewUrl, this.dailyDateString);
                // update 't_own_domain_tracking' table, check if the record of the domain exists
                OwnDomainTracking ownDomainTracking = this.ownDomainTrackingDAO.queryByOwnDomainId(domainId);
                if (ownDomainTracking == null) {
                    this.ownDomainTrackingDAO.insertByOwnDomainId(domainId);
                }
                // update 't_own_domain_tracking' table, update the 'target_url_latest_date' field
//                this.ownDomainTrackingDAO.updateTargetUrlLatestDateByOwnDomainId(domainId, targetUrlHtmlDailyDateNumber);
            } catch (Exception e) {
                log.error("createDailyDataFromHistoricalData() error domainId={}, domainName={}, sourceTableName={}, destinationTableName={}, exception: ", domainId, domainName, sourceTableName, destinationTableName, e);
                continue;
            }
            log.info("process() domainId={},domainName={} ends,elapsed(s.)={}", domainId, domainName, (System.currentTimeMillis() - startTimestamp) / 1000);
        }
        // detach all partitions before two days' ago
        detachDailyPartitions();

        //createSummary(politeCrawlSummaryValueObjectList, IConstants.TABLE_NAME_TARGET_URL_HTML_DAILY, targetUrlHtmlDailyDate);
        log.info("process() ends. elapsed(s.)={}", (System.currentTimeMillis() - totalStarttimestamp) / 1000);
    }

    private void update(int domainId, int urlSize) throws Exception {
        final List<Date> allLastChangedCrawlTimestamp = this.urlLastChangedTimestampDAO.getAllLastChangedCrawlTimestamp(null, domainId, this.dailyDataCreationDate, 1);
        if (allLastChangedCrawlTimestamp == null || allLastChangedCrawlTimestamp.isEmpty()) {
            log.warn("domainId={}, allLastChangedCrawlTimestamp is null or empty", domainId);
            return;
        }
        final int size = allLastChangedCrawlTimestamp.size();
        int intervalMonth = getIntervalMonth(urlSize);
        log.info("domainId= {}, urlSize= {}, intervalMonth= {}", domainId, urlSize, intervalMonth);
        final Date now = new Date();
        Date minTrackDate = allLastChangedCrawlTimestamp.get(0);
        Date maxTrackDate;
        for (int i = 0; i < size; i++) {
            Date lastChangedCrawlTimestamp = allLastChangedCrawlTimestamp.get(i);
            if (minTrackDate.after(lastChangedCrawlTimestamp)) {
                // skip if minTrackDate is after lastChangedCrawlTimestamp
                continue;
            } else {
                minTrackDate = lastChangedCrawlTimestamp;
            }
//          if (i > 1 && maxTrackDate.before(lastChangedCrawlTimestamp)) {
//              minTrackDate = lastChangedCrawlTimestamp;
//              continue;
//          }
            maxTrackDate = DateUtils.addMonths(minTrackDate, intervalMonth);
            final Calendar calendar = DateUtils.toCalendar(maxTrackDate);
            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
            maxTrackDate = calendar.getTime();
            // maxTrackDate compares with the next lastChangedCrawlTimestamp, so if maxTrackDate is before the next lastChangedCrawlTimestamp, set maxTrackDate to the next lastChangedCrawlTimestamp
            if (i < size - 1) {
                Date nextLastChangedCrawlTimestamp = allLastChangedCrawlTimestamp.get(i + 1);
                if (maxTrackDate.before(nextLastChangedCrawlTimestamp)) {
                    maxTrackDate = nextLastChangedCrawlTimestamp;
                }
            }
            if (maxTrackDate.after(now)) {
                maxTrackDate = now;
            }
            final String minTrackDateString = DateFormatUtils.format(DateUtils.addDays(minTrackDate, -1), IConstants.DATE_FORMAT_YYYY_MM_DD);
            final String maxTrackDateString = DateFormatUtils.format(DateUtils.addDays(maxTrackDate, 1), IConstants.DATE_FORMAT_YYYY_MM_DD);
            targetUrlHtmlClickHouseDAO.createDailyDataFromHistoricalDataByTrackDate(domainId, sourceTableName, destinationTableName, minTrackDateString, maxTrackDateString, this.dailyDateString, i);
            Thread.sleep(500);
            if (!maxTrackDate.before(now)) {
                log.warn("domainId={},maxTrackDate={} is after now={}, no need to get next lastChangedCrawlTimestamp", domainId, maxTrackDate, now);
                return;
            }
            minTrackDate = DateUtils.addDays(maxTrackDate, 1);
        }
    }

    private int getIntervalMonth(int size) {
        if (size <= 500) {
            return 14;
        }
        if (size <= 2000) {
            return 7;
        }
        if (size <= 10000) {
            return 4;
        }
        if (size <= 50000) {
            return 3;
        }
        if (size <= 100000) {
            return 2;
        }
        return 2;
        }

    // drop data partitions older than eight days old on all cluster servers
    private void detachDailyPartitions() throws Exception {
        log.info("detachDailyPartitions() begins.");
        Date todayDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
        Date yesterdayDate = DateUtils.addDays(todayDate, -1);
        Date twoDaysBeforeYesterdayDate = DateUtils.addDays(yesterdayDate, -2);
        Date fiveDaysBeforeYesterdayDate = DateUtils.addDays(yesterdayDate, -5);
        LocalTargetUrlHtmlDailyClickHouseDAO.getInstance().detachDailyPartitions(fiveDaysBeforeYesterdayDate, twoDaysBeforeYesterdayDate);
        log.info("detachDailyPartitions() ends.");
    }
}