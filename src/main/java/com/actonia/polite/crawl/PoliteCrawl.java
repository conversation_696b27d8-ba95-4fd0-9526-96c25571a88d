package com.actonia.polite.crawl;

import com.actonia.IConstants;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.concurrency.ThreadPoolService;
import com.actonia.dao.*;
import com.actonia.entity.*;
import com.actonia.utils.*;
import com.actonia.value.object.UrlCrawlParametersVO;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.sqs.model.Message;
import com.google.gson.Gson;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;

/***
 *
 * To initiate URLs metric crawl by domain, retrieves each domain's queue name
 * from the controller queue and create one thread per domain queue name.
 *
 */
public class PoliteCrawl {
	//private boolean isDebug = false;
	private static final Logger log = LogManager.getLogger(PoliteCrawl.class);
	private static final ThreadPoolService threadPoolService = ThreadPoolService.getInstance();
	private Integer crawlType;
	public static String defaultUserAgent;

	// map key = yyyymmddhhmm
	// map value = list of HtmlClickHouseEntity to be persisted
	private static ConcurrentMap<String, List<HtmlClickHouseEntity>> timestampHtmlClickHouseEntityListMap = new ConcurrentHashMap<>();

	// map key = yyyymmddhhmm
	// map value = list of HtmlFileNameClickHouseEntity to be persisted
	private static ConcurrentMap<String, List<HtmlFileNameClickHouseEntity>> timestampHtmlFileNameClickHouseEntityListMap = new ConcurrentHashMap<>();

	// map key = yyyymmddhhmm
	// map value = Map of queue URL and list of Messages to be removed
	private static ConcurrentMap<String, Map<String, List<Message>>> timestampQueueUrlMessageListMapMap = new ConcurrentHashMap<>();

	private final TargetUrlCrawlAdditionalContentEntityDAO targetUrlCrawlAdditionalContentEntityDAO;

	private final OwnDomainEntityDAO ownDomainEntityDAO;

	private List<String> previousFieldNameList;

	private static boolean isEndCrawl = false;

	private static List<String> expediaDomainList = new ArrayList<>();

	private final ContentGuardAdditionalContentEntityDAO contentGuardAdditionalContentEntityDAO;

	private static final int MAX_JAVASCRIPT_CONCURRENT_REQUESTS = 80;

	public static ConcurrentLinkedQueue<UrlLastChangedTimestampEntity> urlLastChangedTimestampQueue;
	private static final ConcurrentLinkedQueue<HtmlChange> htmlChangeQueue = new ConcurrentLinkedQueue<>();
	private static final ConcurrentLinkedQueue<HtmlBigData> htmlBigDataQueue = new ConcurrentLinkedQueue<>();
	public static final ConcurrentLinkedQueue<ManagedHtmlChange> managedHtmlChangeQueue = new ConcurrentLinkedQueue<>();
	public static final ConcurrentLinkedQueue<NewHtmlClickHouseEntity> newHtmlClickHouseEntityQueue = new ConcurrentLinkedQueue<>();
	protected static final ExecutorService executorService = Executors.newFixedThreadPool(4);
	protected static final AmazonS3 s3client = CrawlHtmlSeagateUtils.s3client;

	public PoliteCrawl() {
		targetUrlCrawlAdditionalContentEntityDAO = SpringBeanFactory.getBean("targetUrlCrawlAdditionalContentEntityDAO");
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		contentGuardAdditionalContentEntityDAO = SpringBeanFactory.getBean("contentGuardAdditionalContentEntityDAO");
	}

	public static void main(String[] args) throws Exception {
		log.info("main() begins. java version={}, args:{}", System.getProperty("java.version"), StringUtils.join(args, ","));
		long startTimestamp = System.currentTimeMillis();
		try {
			PoliteCrawl politeCrawl = new PoliteCrawl();
			politeCrawl.initialize();
			politeCrawl.processControllerQueueMessagesConcurrently(args);

			log.info("main() ends. elapsed time in sec.:{}", (System.currentTimeMillis() - startTimestamp) / 1000);
		} catch (Exception exception) {
			log.error("Exception: ", exception);
		} finally {
			log.info("Shutting down threadPoolService...");
			threadPoolService.destroy();
			log.info("threadPoolService shutdown initiated.");

			log.info("Shutting down executorService...");
			executorService.shutdown(); // Disable new tasks from being submitted
			try {
				// Wait a while for existing tasks to terminate
				if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
					log.warn("executorService did not terminate in 60 seconds. Forcing shutdown...");
					executorService.shutdownNow(); // Cancel currently executing tasks
					// Wait a while for tasks to respond to being cancelled
					if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
						log.error("executorService did not terminate even after forcing.");
					} else {
						log.info("executorService terminated successfully after forcing.");
					}
				} else {
					log.info("executorService terminated successfully.");
				}
			} catch (InterruptedException ie) {
				log.warn("Interrupted while waiting for executorService to terminate. Forcing shutdown...");
				// (Re-)Cancel if current thread also interrupted
				executorService.shutdownNow();
				// Preserve interrupt status
				Thread.currentThread().interrupt();
			}
			log.info("All services shut down. Main thread exiting.");
		}
	}

	private void processControllerQueueMessagesConcurrently(String[] args) throws Exception {
		long startTimestamp = System.currentTimeMillis();

		String ipAddress = null;
		PoliteCrawlCommand politeCrawlCommand = null;
		String crawlQueueName = null;
		int currentRetryCount = 0;
		List<Message> messageList;
		Message message = null;
		UrlCrawlParametersVO[] urlCrawlParametersVoArray = null;
		UrlCrawlParametersVO urlCrawlParametersVO = null;
		Gson gson = new Gson();
		String delayInSecondsPerHttpRequestString = null;
		int delayInSecondsPerHttpRequest = 0;
		String maximumThreadsPerQueueString = null;
		String domainSpecificUserAgent = null;
		String region = null;
		int maximumThreadsPerQueue = 0;
		Boolean enableJavascriptCrawl = null;
		Integer javascriptTimeoutInSecond = null;
		Boolean enableScrapyCrawl = null;
		String startCrawlTimeString = null;
		String endCrawlTimeString = null;
		String testString = null;
		int numberOfConcurrentThreads = 168;
		int messagesPerIteration = PoliteCrawlConfigurations.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_MESSAGES_PER_ITERATION, 10);
		defaultUserAgent = PoliteCrawlConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_USER_AGENT);
		String controllerQueueName = PoliteCrawlConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CONTROLLER_QUEUE);
		OwnDomainEntity ownDomainEntity = null;
		String urlCrawlParameters = null;
		UrlCrawlParametersVO urlCrawlParametersVo = null;
		int testInteger = 0;

		// runtime parameter 1: number of concurrent threads
		if (args != null && args.length >= 1) {
			testString = args[0];
			numberOfConcurrentThreads = NumberUtils.toInt(testString);
			log.info("processControllerQueueMessagesConcurrently() runtime parameters 1 (number of concurrent threads)={}", numberOfConcurrentThreads);
		}

		// https://www.wrike.com/open.htm?id=1022499784
		if (StringUtils.equalsIgnoreCase(controllerQueueName, IConstants.QUEUE_NAME_TARGET_URL_DAILY_HTML_SKYSCANNER_QUEUE_NAMES_FIFO)) {
			ownDomainEntity = ownDomainEntityDAO.getById(IConstants.SKYSCANNER_DOMAIN_ID_FOR_POLITE_CRAWL_CONFIG);
			if (ownDomainEntity != null) {
				urlCrawlParameters = ownDomainEntity.getUrlCrawlParameters();
				urlCrawlParametersVoArray = gson.fromJson(urlCrawlParameters, UrlCrawlParametersVO[].class);
				for (UrlCrawlParametersVO crawlParametersVO : urlCrawlParametersVoArray) {
					urlCrawlParametersVo = crawlParametersVO;
					// maximum number of concurrent threads per domain
					if (urlCrawlParametersVo.getType().equalsIgnoreCase(IConstants.MAX_CONCURRENT_THREADS)) {
						testInteger = Integer.parseInt(urlCrawlParametersVo.getData());
						if (testInteger > 0) {
							numberOfConcurrentThreads = testInteger;
							log.info("processControllerQueueMessagesConcurrently() switch to use domain 11545's 'maxConcurrentThreads' in 'url_crawl_parameters' field={}", numberOfConcurrentThreads);
						}
					}
				}
			}
		}

		threadPoolService.init();
		CommonUtils.initThreads(numberOfConcurrentThreads);
		log.info("processControllerQueueMessagesConcurrently() total concurrent threads initialized={}", numberOfConcurrentThreads);

		String controllerQueueUrl = null;
		if (crawlType == IConstants.CRAWL_TYPE_TARGET_URL_HTML) {
			controllerQueueUrl = SQSUtils.getInstance().createFifoQueue(controllerQueueName);
		} else {
			controllerQueueUrl = SQSUtils.getInstance().createQueue(controllerQueueName);
		}

		ConcurrentMap<String, Boolean> concurrentBooleanMap = new ConcurrentHashMap<String, Boolean>();
		concurrentBooleanMap.put(IConstants.IS_INVOKE_SHARED_COUNTS_API, true);
		CacheModleFactory.getInstance().setConcurrentBooleanMap(concurrentBooleanMap);

		String userAgentForCrawl = null;

		String controllerMessageBody = null;

		String domainNames = null;
		String[] domainNameArray = null;
		String userAgents = null;
		String[] userAgentArray = null;

		// map key = domain name
		// map key = user agent
		Map<String, String> domainNameUserAgentMap = new HashMap<String, String>();

		// map key = header name
		// map value = header value
		Map<String, String> pageCrawlerApiRequestHeaders = new HashMap<String, String>();

		String pageCrawlerApiRequestHeaderName = null;

		int domainId = 0;
		int messagesInQueue = 0;
		boolean isSkipDomain = false;

		// map key = target URL MD5 hash code
		// map value = target URL's daily HtmlClickHouseEntity
		Map<String, HtmlClickHouseEntity> hashCodeHtmlClickHouseEntityMap = null;

		// get minTrackDate for each domain before two days ago
		// map key = domain name
		// map value = minTrackDate
		Map<Integer, String> domainNameMinTrackDateMap = UrlLastChangedTimestampDAO.getInstance().getMinLastChangedCrawlTimestamp(null, LocalDate.now().minusDays(1).toString());

		Integer[] numberOfMessagesArray = null;

		List<String> previousFieldNames = getPreviousFieldNames();

		log.info("processControllerQueueMessagesConcurrently() begins. controllerQueueName={},controllerQueueUrl={},messagesPerIteration={},defaultUserAgent={},crawlType={}", controllerQueueName, controllerQueueUrl, messagesPerIteration, defaultUserAgent, crawlType);
		boolean isContinueProcessing = true;
		do {
			ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
			if (ipAddress == null) {
				continue;
			}
			try {
				messageList = SQSUtils.getInstance().getMessageFromQueue(controllerQueueUrl, 1, IConstants.SQS_MSG_TIMEOUT_IN_2000_SEC);
				if (messageList == null || messageList.size() <= 0) {
					log.info("processControllerQueueMessagesConcurrently() end processing.");
					break;
				}
				crawlQueueName = null;
				delayInSecondsPerHttpRequest = 0;
				maximumThreadsPerQueue = 0;
				domainSpecificUserAgent = null;
				region = null;
				enableJavascriptCrawl = null;
				enableScrapyCrawl = null;
				startCrawlTimeString = null;
				endCrawlTimeString = null;
				domainNames = null;
				userAgents = null;
				message = messageList.get(0);
				pageCrawlerApiRequestHeaders = new HashMap<String, String>();
				controllerMessageBody = message.getBody();
				log.info("processControllerQueueMessagesConcurrently() controller queue message={}", controllerMessageBody);
				urlCrawlParametersVoArray = gson.fromJson(controllerMessageBody, UrlCrawlParametersVO[].class);
				if (urlCrawlParametersVoArray != null) {
					for (UrlCrawlParametersVO crawlParametersVO : urlCrawlParametersVoArray) {
						urlCrawlParametersVO = crawlParametersVO;
						// queue name
						if (urlCrawlParametersVO.getType().equalsIgnoreCase(IConstants.QUEUE_NAME)) {
							crawlQueueName = urlCrawlParametersVO.getData();
						}
						// delay in seconds per HTTP request
						else if (urlCrawlParametersVO.getType().equalsIgnoreCase(IConstants.DELAY_IN_SECONDS)) {
							delayInSecondsPerHttpRequestString = urlCrawlParametersVO.getData();
							delayInSecondsPerHttpRequest = Integer.parseInt(delayInSecondsPerHttpRequestString);
						}
						// maximum number of threads per queue
						else if (urlCrawlParametersVO.getType().equalsIgnoreCase(IConstants.MAX_CONCURRENT_THREADS)) {
							maximumThreadsPerQueueString = urlCrawlParametersVO.getData();
							maximumThreadsPerQueue = Integer.parseInt(maximumThreadsPerQueueString);
						}
						// domain specific user agent
						else if (urlCrawlParametersVO.getType().equalsIgnoreCase(IConstants.USER_AGENT)) {
							domainSpecificUserAgent = urlCrawlParametersVO.getData();
						}
						// enable Javascript crawl
						else if (urlCrawlParametersVO.getType().equalsIgnoreCase(IConstants.ENABLE_JAVASCRIPT_CRAWL)) {
							enableJavascriptCrawl = BooleanUtils.toBooleanObject(urlCrawlParametersVO.getData());
						}
						// javascriptTimeoutInSecond
						else if (urlCrawlParametersVO.getType().equalsIgnoreCase(IConstants.JAVASCRIPT_TIMEOUT_IN_SECOND)) {
							javascriptTimeoutInSecond = NumberUtils.toInt(urlCrawlParametersVO.getData());
						}
						// enable Scrapy crawl
						else if (urlCrawlParametersVO.getType().equalsIgnoreCase(IConstants.ENABLE_SCRAPY_CRAWL)) {
							enableScrapyCrawl = BooleanUtils.toBooleanObject(urlCrawlParametersVO.getData());
						}
						// region
						else if (urlCrawlParametersVO.getType().equalsIgnoreCase(IConstants.REGION)) {
							region = urlCrawlParametersVO.getData();
						}
						// start crawl time
						else if (urlCrawlParametersVO.getType().equalsIgnoreCase(IConstants.START_CRAWL_TIME)) {
							startCrawlTimeString = urlCrawlParametersVO.getData();
						}
						// end crawl time
						else if (urlCrawlParametersVO.getType().equalsIgnoreCase(IConstants.END_CRAWL_TIME)) {
							endCrawlTimeString = urlCrawlParametersVO.getData();
						}
						// domain names (for competitor URL crawl and Link Clarity crawl)
						else if (urlCrawlParametersVO.getType().equalsIgnoreCase(IConstants.DOMAIN_NAMES)) {
							domainNames = urlCrawlParametersVO.getData();
						}
						// user agents (for competitor URL crawl and Link Clarity crawl)
						else if (urlCrawlParametersVO.getType().equalsIgnoreCase(IConstants.USER_AGENTS)) {
							userAgents = urlCrawlParametersVO.getData();
						}
						// Page Crawl API request header
						else if (StringUtils.startsWithIgnoreCase(urlCrawlParametersVO.getType(), IConstants.PAGE_CRAWLER_API_REQUEST_HEADER)) {
							pageCrawlerApiRequestHeaderName = StringUtils.substringAfter(urlCrawlParametersVO.getType(),
									IConstants.PAGE_CRAWLER_API_REQUEST_HEADER + IConstants.DASH);
							pageCrawlerApiRequestHeaders.put(pageCrawlerApiRequestHeaderName, urlCrawlParametersVO.getData());
						}
					}
				}

				if (crawlType == IConstants.CRAWL_TYPE_TARGET_URL_HTML || crawlType == IConstants.CRAWL_TYPE_CONTENT_GUARD) {
					domainId = CrawlerUtils.getInstance().getDomainIdFromQueueName(crawlQueueName);
//					if (domainId != 4765) {
//						log.info("processControllerQueueMessagesConcurrently() skip domainId={},queueName={}", domainId, crawlQueueName);
//						CacheModleFactory.getInstance().setAliveIpAddress(ipAddress);
//					}
				}

				// remove message from controller queue after required information has been retrieved...
				SQSUtils.getInstance().deleteMessageFromQueue(controllerQueueUrl, message);

				// https://www.wrike.com/open.htm?id=1022499784
				if (StringUtils.equalsIgnoreCase(controllerQueueName, IConstants.QUEUE_NAME_TARGET_URL_DAILY_HTML_SKYSCANNER_QUEUE_NAMES_FIFO)) {
					maximumThreadsPerQueue = 1;
				}

				if (StringUtils.isNotBlank(crawlQueueName)) {
					hashCodeHtmlClickHouseEntityMap = new HashMap<>();
					if (crawlType == IConstants.CRAWL_TYPE_TARGET_URL_HTML || crawlType == IConstants.CRAWL_TYPE_CONTENT_GUARD) {
						isSkipDomain = false;
						numberOfMessagesArray = SQSUtils.getInstance().getApproximateNumberOfMessagesAndInflight(crawlQueueName);
						if (numberOfMessagesArray != null && numberOfMessagesArray.length == 2) {
							messagesInQueue = numberOfMessagesArray[0];
							// when there are messages in daily crawl queue
							if (messagesInQueue != 0) {
								// retrieve domain's latest fields data (track_date, response_code) from historical data table 'target_url_html'
								if (crawlType == IConstants.CRAWL_TYPE_TARGET_URL_HTML) {
									final String minTrackDate = domainNameMinTrackDateMap.get(domainId);
									if (StringUtils.isBlank(minTrackDate)) {
										// get the latest track date from historical data table 'target_url_html'
										hashCodeHtmlClickHouseEntityMap = PutMessageUtils.getInstance().getTargetUrlHtmlClickHouseEntityMap(null, domainId, null, previousFieldNames);
									} else {
										String lastChangedTrackDate = LocalDate.now().minusDays(1).toString();
										final List<HtmlClickHouseEntity> latestFromHistoricalAfterTrackDate = TargetUrlHtmlClickHouseDAO.getInstance().getLatestFromHistoricalAfterTrackDate(domainId, previousFieldNames, minTrackDate, lastChangedTrackDate);
										if (latestFromHistoricalAfterTrackDate != null && !latestFromHistoricalAfterTrackDate.isEmpty()) {
											for (HtmlClickHouseEntity htmlClickHouseEntity : latestFromHistoricalAfterTrackDate) {
												final String url = htmlClickHouseEntity.getUrl();
												if (StringUtils.isNotBlank(url)) {
													htmlClickHouseEntity.setUrl(null);
													hashCodeHtmlClickHouseEntityMap.putIfAbsent(Md5Util.Md5(StringUtils.trim(url)), htmlClickHouseEntity);
												}
											}
										}
									}
									log.info("found {} records from historical data table 'target_url_html' for domainId:{}", hashCodeHtmlClickHouseEntityMap.size(), domainId);
								} else if (crawlType == IConstants.CRAWL_TYPE_CONTENT_GUARD) {
									hashCodeHtmlClickHouseEntityMap = PutMessageUtils.getInstance().getContentGuardClickHouseEntityMap(domainId, previousFieldNames);
								}
							} else {
								isSkipDomain = true;
							}
						}
					}

					if (StringUtils.isNotBlank(domainSpecificUserAgent)) {
						userAgentForCrawl = domainSpecificUserAgent;
					} else {
						userAgentForCrawl = defaultUserAgent;
					}

					if (StringUtils.isNotBlank(domainNames) && StringUtils.isNotBlank(userAgents)) {
						domainNameArray = domainNames.split(IConstants.COMMA);
						userAgentArray = userAgents.split(IConstants.COMMA);
						for (int i = 0; i < domainNameArray.length; i++) {
							domainNameUserAgentMap.put(domainNameArray[i], userAgentArray[i]);
						}
					}

					if ((crawlType == IConstants.CRAWL_TYPE_TARGET_URL_HTML || crawlType == IConstants.CRAWL_TYPE_CONTENT_GUARD) && isSkipDomain) {
						politeCrawlCommand = null;
						CacheModleFactory.getInstance().setAliveIpAddress(ipAddress);
						log.info("processControllerQueueMessagesConcurrently() skip crawlQueueName= {},queue is empty.", crawlQueueName);
					} else {
						politeCrawlCommand = getPoliteCrawlCommand(ipAddress, crawlQueueName, messagesPerIteration, crawlType, userAgentForCrawl,
								delayInSecondsPerHttpRequest, enableJavascriptCrawl, javascriptTimeoutInSecond, enableScrapyCrawl, startCrawlTimeString,
								endCrawlTimeString, controllerMessageBody, controllerQueueUrl, domainNameUserAgentMap, pageCrawlerApiRequestHeaders,
								hashCodeHtmlClickHouseEntityMap, region, maximumThreadsPerQueue);
					}

					if (politeCrawlCommand != null) {

						log.info("processControllerQueueMessagesConcurrently() crawlQueueName={} executing thread, maximumThreadsPerQueue={},ip={}", crawlQueueName, maximumThreadsPerQueue, ipAddress);
						threadPoolService.execute(politeCrawlCommand);
						maximumThreadsPerQueue--;
						currentRetryCount = 0;

						// when the domain requires many threads to process....
						while (maximumThreadsPerQueue > 0) {
							ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
							if (ipAddress == null) {
								continue;
							}
							politeCrawlCommand = getPoliteCrawlCommand(ipAddress, crawlQueueName, messagesPerIteration, crawlType, userAgentForCrawl,
									delayInSecondsPerHttpRequest, enableJavascriptCrawl, javascriptTimeoutInSecond, enableScrapyCrawl, startCrawlTimeString,
									endCrawlTimeString, controllerMessageBody, controllerQueueUrl, domainNameUserAgentMap, pageCrawlerApiRequestHeaders,
									hashCodeHtmlClickHouseEntityMap, region, maximumThreadsPerQueue);
							if (politeCrawlCommand != null) {
								threadPoolService.execute(politeCrawlCommand);
							} else {
								CacheModleFactory.getInstance().setAliveIpAddress(ipAddress);
							}
							maximumThreadsPerQueue--;
						}
					} else {
						CacheModleFactory.getInstance().setAliveIpAddress(ipAddress);
					}
				} else {
					CacheModleFactory.getInstance().setAliveIpAddress(ipAddress);
				}
			} catch (Exception e) {
				CacheModleFactory.getInstance().setAliveIpAddress(ipAddress);
				currentRetryCount++;
				log.error("processControllerQueueMessagesConcurrently() currentRetryCount={},e.getMessage()={}", currentRetryCount, e.getMessage());
				// end processing condition - error retry count > maximum retry count allowable
				if (currentRetryCount > IConstants.MAXIMUM_RETRY_THREE_TIMES_COUNT) {
					isContinueProcessing = false;
					setEndCrawl(true);
					break;
				} else {
					Thread.sleep(5000);
					continue;
				}
			}
			if (isEndCrawl()) {
				isContinueProcessing = false;
				break;
			}
		} while (isContinueProcessing);

		int threadSleepTimeMilliseconds = 15000;
		do {
			try {
				//FormatUtils.getInstance().logMemoryUsage("processControllerQueueMessagesConcurrently() active threads="
				//      + threadPoolService.getThreadPool().getActiveCount() + ",current datetime=" + new Date());
				if (StringUtils.containsIgnoreCase(controllerQueueName, IConstants.QUEUE_NAME_CONTENT_GUARD)) {
					threadSleepTimeMilliseconds = 1000;
				}
				//if (isDebug == true) {
				//  threadSleepTimeMilliseconds = 0;
				//}
				Thread.sleep(threadSleepTimeMilliseconds);
			} catch (InterruptedException e) {
				log.error("Exception: ", e);
			}
		} while (threadPoolService.getThreadPool().getActiveCount() > 0);

		CrawlerUtils.getInstance().maintainDataStores(IConstants.EMPTY_STRING, IConstants.EMPTY_STRING, crawlType, IConstants.EMPTY_STRING, messagesPerIteration);

		//sendDomainForbiddenAlert();

		log.info("processControllerQueueMessagesConcurrently() ends. elapsed time in seconds={}", (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	//  private void sendDomainForbiddenAlert() {
	//
	//      FormatUtils.getInstance().logMemoryUsage("sendDomainForbiddenAlert() begins.");
	//
	//      if (CacheModleFactory.getInstance().getDomainIdTotalForbiddenMap() == null || CacheModleFactory.getInstance().getDomainIdTotalForbiddenMap().size() == 0) {
	//          FormatUtils.getInstance().logMemoryUsage("sendDomainForbiddenAlert() ends. CacheModleFactory.getInstance().getDomainIdTotalForbiddenMap() is empty.");
	//          return;
	//      }
	//
	//      Date processTimestamp = new Date();
	//      String domainName = null;
	//      OwnDomainEntity ownDomainEntity = null;
	//      DomainTotalForbidden domainTotalForbidden = null;
	//      List<DomainTotalForbidden> domainTotalForbiddenList = new ArrayList<DomainTotalForbidden>();
	//      for (Integer domainId : CacheModleFactory.getInstance().getDomainIdTotalForbiddenMap().keySet()) {
	//          ownDomainEntity = ownDomainEntityDAO.getById(domainId);
	//          if (ownDomainEntity != null) {
	//              domainName = ownDomainEntity.getDomain();
	//          } else {
	//              domainName = IConstants.EMPTY_STRING;
	//          }
	//          domainTotalForbidden = new DomainTotalForbidden();
	//          domainTotalForbidden.setDomainId(domainId);
	//          domainTotalForbidden.setDomainName(domainName);
	//          domainTotalForbidden.setTotalForbidden(CacheModleFactory.getInstance().getDomainIdTotalForbiddenMap().get(domainId));
	//          domainTotalForbiddenList.add(domainTotalForbidden);
	//      }
	//
	//      String[] emailAddressArray = new String[] { "<EMAIL>" };
	//      int retryCount = 0;
	//      Map<String, Object> map = new HashMap<String, Object>();
	//      String processTimestampString = DateFormatUtils.format(processTimestamp, IConstants.DATE_FORMAT_YYYY_MM_DD_HHMMSS);
	//      String emailSubject = null;
	//      if (crawlType == IConstants.CRAWL_TYPE_TARGET_URL_HTML) {
	//          emailSubject = "Target URL Polite Crawl Total Forbidden Summary - " + processTimestampString;
	//      } else if (crawlType == IConstants.CRAWL_TYPE_CONTENT_GUARD) {
	//          emailSubject = "Content Guard Polite Crawl Total Forbidden Summary - " + processTimestampString;
	//      }
	//      map.put("domainTotalForbiddenList", domainTotalForbiddenList);
	//
	//      AgencyInfoEntity agencyInfoEntity = agencyInfoService.getByDomainId(1701);
	//      while (retryCount < IConstants.MAX_SEND_EMAIL_RETRY_COUNT) {
	//          try {
	//              emailSenderComponent.sendMimeMultiPartZeptoMail(IConstants.NOTIFICATION_EMAIL_ADDRESS, emailAddressArray, emailSubject, "mail_domain_forbidden_report.txt",
	//                      "mail_domain_forbidden_report.html", map, agencyInfoEntity);
	//              retryCount = IConstants.MAX_SEND_EMAIL_RETRY_COUNT;
	//          } catch (Exception e) {
	//              e.printStackTrace();
	//              retryCount++;
	//              if (retryCount >= IConstants.MAX_SEND_EMAIL_RETRY_COUNT) {
	//                  throw e;
	//              } else {
	//                  FormatUtils.getInstance().logMemoryUsage("sendDomainForbiddenAlert() retryCount=" + retryCount);
	//                  try {
	//                      Thread.sleep(IConstants.RETRY_WAIT_TIME_IN_MILLISECONDS);
	//                  } catch (InterruptedException e1) {
	//                      e1.printStackTrace();
	//                  }
	//              }
	//          }
	//      }
	//      FormatUtils.getInstance().logMemoryUsage("sendDomainForbiddenAlert() ends. CacheModleFactory.getInstance().getDomainIdTotalForbiddenMap().size()="
	//              + CacheModleFactory.getInstance().getDomainIdTotalForbiddenMap().size());
	//
	//  }

	private void initialize() throws Exception {
		long startTimestamp = System.currentTimeMillis();

		this.crawlType = PoliteCrawlConfigurations.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CRAWL_TYPE, 0);
		log.info("initialize() begins. crawlType={}", crawlType);

		PutMessageUtils.getInstance();
		CrawlerUtils.getInstance();
		SQSUtils.getInstance();

		if (crawlType == IConstants.CRAWL_TYPE_TARGET_URL_HTML) {

			// initialize the cached instances of TargetUrlHtmlClickHouseDAO
			TargetUrlHtmlClickHouseDAO.getInstance();
			HtmlClickHouseDAO.getInstance();
			ChangeIndMasterClickHouseDAO.getInstance();

		} else if (crawlType == IConstants.CRAWL_TYPE_COMPETITOR_URL_HTML) {

			// initialize the cached instances of CompetitorUrlHtmlClickHouseDAO
			CompetitorUrlHtmlClickHouseDAO.getInstance();

		} else if (crawlType == IConstants.CRAWL_TYPE_CONTENT_GUARD) {

			// initialize the cached instances of ContentGuardClickHouseDAO
			ContentGuardClickHouseDAO.getInstance();

		}

		expediaDomainList = PutMessageUtils.getInstance().getExpediaDomainNames();

		log.info("initialize() ends. ,total elapsed time in sec.={}", (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private PoliteCrawlCommand getPoliteCrawlCommand(String ip, String queueName, int messagesPerIteration, int crawlType, String userAgentForCrawl,
	                                                 int delayInSecondsPerHttpRequest, Boolean enableJavascriptCrawl, Integer javascriptTimeoutInSecond, Boolean enableScrapyCrawl, String startCrawlTimeString,
	                                                 String endCrawlTimeString, String controllerMessageBody, String controllerQueueUrl, Map<String, String> domainNameUserAgentMap,
	                                                 Map<String, String> pageCrawlerApiRequestHeaders, Map<String, HtmlClickHouseEntity> hashCodeHtmlClickHouseEntityMap, String region, int maximumThreadsPerQueue) throws Exception {

		//if (BooleanUtils.isTrue(enableJavascriptCrawl)) {
		//  FormatUtils.getInstance().logMemoryUsage("getPoliteCrawlCommand() skip Javascript crawl until API is ready. ip=" + ip + ",queueName=" + queueName);
		//  return null;
		//}

		boolean isStartCrawlBasedOnStartEndCrawlTime = false;
		List<AdditionalContentEntity> additionalContentEntityList = null;
		OwnDomainEntity ownDomainEntity = null;
		Integer domainId = null;
		String domainName = null;
		List<ElementMappingPatternEntity> elementMappingPatternEntities = null;

		// when start and end crawl time are specified in the domain's message in the controller queue,
		// only start the crawl for the domain when current time is in range of start and end crawl time
		if (StringUtils.isNotBlank(startCrawlTimeString) && StringUtils.isNotBlank(endCrawlTimeString)) {
			isStartCrawlBasedOnStartEndCrawlTime = CrawlerUtils.getInstance().isStartCrawlBasedOnStartEndCrawlTime(queueName, startCrawlTimeString, endCrawlTimeString);
			if (!isStartCrawlBasedOnStartEndCrawlTime) {
				return null;
			}
		}

		// when crawling target URLs, retrieve 'target_url_crawl_additional_content' records to crawl additional content
		if (crawlType == IConstants.CRAWL_TYPE_TARGET_URL_HTML) {
			domainId = CrawlerUtils.getInstance().getDomainIdFromQueueName(queueName);
			additionalContentEntityList = targetUrlCrawlAdditionalContentEntityDAO.getByDomainId(domainId);
		}
		// when crawling content guard URLs, retrieve 'target_url_crawl_additional_content' records to crawl additional content
		else if (crawlType == IConstants.CRAWL_TYPE_CONTENT_GUARD) {
			domainId = CrawlerUtils.getInstance().getDomainIdFromQueueName(queueName);
			additionalContentEntityList = contentGuardAdditionalContentEntityDAO.getByDomainId(domainId);
			ownDomainEntity = ownDomainEntityDAO.getById(domainId);
			if (ownDomainEntity != null) {
				domainName = ownDomainEntity.getDomain();
			} else {
				log.warn("getPoliteCrawlCommand() skipped domainId={},domainName cannot be determined.", domainId);
				return null;
			}
		}

		PoliteCrawlCommand politeCrawlCommand = new PoliteCrawlCommand(ip, queueName, messagesPerIteration, crawlType, userAgentForCrawl, delayInSecondsPerHttpRequest,
				enableJavascriptCrawl, javascriptTimeoutInSecond, controllerMessageBody, controllerQueueUrl, domainNameUserAgentMap, pageCrawlerApiRequestHeaders,
				additionalContentEntityList, domainId, domainName, elementMappingPatternEntities, hashCodeHtmlClickHouseEntityMap, region, maximumThreadsPerQueue);
		politeCrawlCommand.setStatus(true);

		int totalJavascriptCrawlerThreads = 0;
		if (BooleanUtils.isTrue(enableJavascriptCrawl)) {
			totalJavascriptCrawlerThreads = CrawlerUtils.getInstance().getTotalJavascriptCrawlerThreads() + 1;
			// when total number of Javascript crawler threads, skip the queue
			if (totalJavascriptCrawlerThreads > MAX_JAVASCRIPT_CONCURRENT_REQUESTS) {
				log.warn("getPoliteCrawlCommand() skipped when total number of Javascript crawler threads. totalJavascriptCrawlerThreads={},queueName={}", totalJavascriptCrawlerThreads, queueName);
				politeCrawlCommand = null;
			} else {
				CrawlerUtils.getInstance().setTotalJavascriptCrawlerThreads(totalJavascriptCrawlerThreads);
				log.info("getPoliteCrawlCommand() proceed. totalJavascriptCrawlerThreads={},queueName={},delayInSecondsPerHttpRequest={},userAgentForCrawl={},enableJavascriptCrawl={},javascriptTimeoutInSecond={},enableScrapyCrawl={},startCrawlTimeString={},endCrawlTimeString={},domainId={},region={}",
						totalJavascriptCrawlerThreads, queueName, delayInSecondsPerHttpRequest, userAgentForCrawl, enableJavascriptCrawl, javascriptTimeoutInSecond, enableScrapyCrawl, startCrawlTimeString, endCrawlTimeString, domainId, region);
			}
		}

		return politeCrawlCommand;
	}

	public static ConcurrentMap<String, List<HtmlClickHouseEntity>> getTimestampHtmlClickHouseEntityListMap() {
		return timestampHtmlClickHouseEntityListMap;
	}

	public static void setTimestampHtmlClickHouseEntityListMap(ConcurrentMap<String, List<HtmlClickHouseEntity>> timestampHtmlClickHouseEntityListMap) {
		PoliteCrawl.timestampHtmlClickHouseEntityListMap = timestampHtmlClickHouseEntityListMap;
	}

	public static ConcurrentMap<String, Map<String, List<Message>>> getTimestampQueueUrlMessageListMapMap() {
		return timestampQueueUrlMessageListMapMap;
	}

	public static void setTimestampQueueUrlMessageListMapMap(ConcurrentMap<String, Map<String, List<Message>>> timestampQueueUrlMessageListMapMap) {
		PoliteCrawl.timestampQueueUrlMessageListMapMap = timestampQueueUrlMessageListMapMap;
	}

	public static ConcurrentMap<String, List<HtmlFileNameClickHouseEntity>> getTimestampHtmlFileNameClickHouseEntityListMap() {
		return timestampHtmlFileNameClickHouseEntityListMap;
	}

	public static void setTimestampHtmlFileNameClickHouseEntityListMap(
			ConcurrentMap<String, List<HtmlFileNameClickHouseEntity>> timestampHtmlFileNameClickHouseEntityListMap) {
		PoliteCrawl.timestampHtmlFileNameClickHouseEntityListMap = timestampHtmlFileNameClickHouseEntityListMap;
	}

	public List<String> getPreviousFieldNames() {
		if (previousFieldNameList == null || previousFieldNameList.isEmpty()) {
			previousFieldNameList = new ArrayList<>();
			previousFieldNameList.add(IConstants.URL);
			previousFieldNameList.add(IConstants.TRACK_DATE);
			previousFieldNameList.add(IConstants.RESPONSE_CODE);
		}
		return previousFieldNameList;
	}

	public static boolean isEndCrawl() {
		return isEndCrawl;
	}

	public static void setEndCrawl(boolean isEndCrawl) {
		PoliteCrawl.isEndCrawl = isEndCrawl;
	}

	public static List<String> getExpediaDomainList() {
		return expediaDomainList;
	}

	public static void setExpediaDomainList(List<String> expediaDomainList) {
		PoliteCrawl.expediaDomainList = expediaDomainList;
	}

	public static ConcurrentLinkedQueue<HtmlChange> getHtmlChangeQueue() {
		return htmlChangeQueue;
	}

	public static ConcurrentLinkedQueue<HtmlBigData> getHtmlBigDataQueue() {
		return htmlBigDataQueue;
	}
}

