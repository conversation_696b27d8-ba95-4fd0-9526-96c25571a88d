package com.actonia.polite.crawl;

import java.util.Date;

import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.http.StatusLine;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;

import com.actonia.IConstants;
import com.actonia.concurrency.BaseThreadCommand;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.HttpUtils;
import com.actonia.value.object.PuppeteerHeaders;
import com.actonia.value.object.PuppeteerRequest;
import com.actonia.value.object.PuppeteerTestValueObject;
import com.google.gson.Gson;

public class PuppeteerTestCommand extends BaseThreadCommand {

	//private boolean isDebug = false;

	private String ip;
	private String urlString;
	private static final String requestUrl = "http://**************:5000/execute";
	private static final boolean isSendGetRequest = false;

	public PuppeteerTestCommand(String ip, String urlString) {
		super();
		this.ip = ip;
		this.urlString = urlString;
	}

	@Override
	protected void undo() throws Exception {
		// TODO Auto-generated method stub
	}

	@Override
	protected void execute() {
		long startTimestamp = System.currentTimeMillis();
		//FormatUtils.getInstance().logMemoryUsage("execute() begins. ip=" + ip + ",urlString=" + urlString);
		try {
			process();
		} catch (Exception e) {
			FormatUtils.getInstance().logMemoryUsage("execute() ip=" + ip + ",urlString=" + urlString + ",exception message=" + e.getMessage());
			e.printStackTrace();
		} finally {
			CacheModleFactory.getInstance().setAliveIpAddress(ip);
			//FormatUtils.getInstance().logMemoryUsage(
			//		"execute() ends. ip=" + ip + ",urlString=" + urlString + ",elapsed time in sec.=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		}
	}

	private void process() throws Exception {
		FormatUtils.getInstance().logMemoryUsage("process() ip=" + ip + ",urlString=" + urlString);
		PuppeteerRequest puppeteerRequest = new PuppeteerRequest();
		PuppeteerHeaders puppeteerHeaders = new PuppeteerHeaders();
		puppeteerHeaders.setUserAgent("Mozilla/5.0 (compatible; ClarityBot/9.0; +https://www.seoclarity.net/bot.html)");
		puppeteerRequest.setHeaders(puppeteerHeaders);
		puppeteerRequest.setPageLoadTimeout(15);
		puppeteerRequest.setUrl(urlString);
		String requestParameters = new Gson().toJson(puppeteerRequest, PuppeteerRequest.class);
		PuppeteerTestValueObject puppeteerTestValueObject = getPuppeteerTestValueObject(requestUrl, isSendGetRequest, requestParameters);
		String outputLine = getOutputLine(puppeteerTestValueObject);
		PuppeteerTest.getOutputList().add(outputLine);
	}

	private String getOutputLine(PuppeteerTestValueObject puppeteerTestValueObject) {
		StringBuilder stringBuilder = new StringBuilder();
		String outputLine = null;
		stringBuilder = new StringBuilder();
		stringBuilder.append(puppeteerTestValueObject.getTimestamp());
		stringBuilder.append(IConstants.BACKTICK);
		stringBuilder.append(puppeteerTestValueObject.getThreadNumber());
		stringBuilder.append(IConstants.BACKTICK);
		stringBuilder.append(puppeteerTestValueObject.getUrlString());
		stringBuilder.append(IConstants.BACKTICK);
		stringBuilder.append(puppeteerTestValueObject.getResponseCode());
		stringBuilder.append(IConstants.BACKTICK);
		stringBuilder.append(puppeteerTestValueObject.getElapsedTimeInSecond());
		stringBuilder.append(IConstants.BACKTICK);
		outputLine = stringBuilder.toString();
		return outputLine;
	}

	public PuppeteerTestValueObject getPuppeteerTestValueObject(String requestUrl, boolean isSendGetRequest, String requestParameters) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("getPuppeteerTestValueObject() begins. ip=" + ip + ",requestParameters=" + requestParameters);
		long startTimestamp = System.currentTimeMillis();
		PuppeteerTestValueObject puppeteerTestValueObject = new PuppeteerTestValueObject();
		puppeteerTestValueObject.setThreadNumber(ip);
		puppeteerTestValueObject.setUrlString(urlString);

		//FormatUtils.getInstance().logMemoryUsage("getHttpResponseAsString() begins. requestUrl=" + requestUrl + ",timeOutInSeconds=" + timeOutInSeconds
		//		+ ",isSendGetRequest=" + isSendGetRequest + ",requestParameters=" + requestParameters);
		//long startTimestamp = System.currentTimeMillis();
		String responseString = null;
		StatusLine statusLine = null;
		int httpStatusCode = 0;
		String httpReasonPhrase = null;
		CloseableHttpClient httpClient = null;
		HttpGet httpGet = null;
		HttpPost httpPost = null;
		CloseableHttpResponse httpResponse = null;
		int retryCount = 0;
		nextRetry: while (retryCount < IConstants.MAX_RETRY_COUNT) {
			try {
				httpClient = HttpUtils.getInstance().getHttpClient();

				// when sending HTTP GET request.....
				if (isSendGetRequest == true) {

					httpGet = HttpUtils.getInstance().getHttpGet(requestUrl);

					// when httpGet object cannot be instantiated due to "IllegalArgumentException" 
					if (httpGet == null) {
						FormatUtils.getInstance().logMemoryUsage(
								"getPuppeteerTestValueObject puppeteerTestValueObject() httpGet cannot be instantiated assigning HTTP status code 999, requestUrl="
										+ requestUrl);
						throw new Exception(String.valueOf(999));
					}

					httpResponse = httpClient.execute(httpGet);
				}
				// when sending HTTP POST request.....
				else {
					puppeteerTestValueObject.setTimestamp(DateFormatUtils.format(new Date(), IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS));
					httpPost = HttpUtils.getInstance().getHttpPost(requestUrl);
					httpPost.setEntity(new StringEntity(requestParameters, IConstants.UTF_8));
					httpPost.setHeader(IConstants.CACHE_CONTROL, IConstants.NO_CACHE);
					httpPost.setHeader(IConstants.CONTENT_DASH_TYPE, IConstants.APPLICATION_SLASH_JSON);
					httpPost.setHeader("Access-key", "cHVwcmVuZGVyX2ZhY3R3ZWF2ZXJz");
					httpResponse = httpClient.execute(httpPost);
				}
				if (httpResponse != null) {

					// HTTP status code
					statusLine = httpResponse.getStatusLine();
					if (statusLine != null) {
						httpStatusCode = statusLine.getStatusCode();
						puppeteerTestValueObject.setResponseCode(String.valueOf(httpStatusCode));
						httpReasonPhrase = statusLine.getReasonPhrase();
						// HTTP content
						if (httpStatusCode == 200) {
							responseString = HttpUtils.getInstance().getHttpResponseAsString(httpResponse);
						} else {
							//FormatUtils.getInstance().logMemoryUsage("getPuppeteerTestValueObject puppeteerTestValueObject() httpStatusCode=" + httpStatusCode
							//		+ ",httpReasonPhrase=" + httpReasonPhrase + ", requestUrl=" + requestUrl + ",requestParameters=" + requestParameters);
							//throw new Exception(String.valueOf(httpStatusCode));
						}
					}
				}
				retryCount = IConstants.MAX_RETRY_COUNT;
				break nextRetry;
			} catch (Exception e) {
				throw e;
			} finally {
				if (httpResponse != null) {
					try {
						httpResponse.close();
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
				if (httpClient != null) {
					try {
						httpClient.close();
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			}
		}
		//FormatUtils.getInstance().logMemoryUsage("getPuppeteerTestValueObject puppeteerTestValueObject() ends. requestUrl=" + requestUrl + ",responseString=" + responseString + ",elapsed(s.)="
		//		+ (System.currentTimeMillis() - startTimestamp) / 1000);
		puppeteerTestValueObject.setElapsedTimeInSecond(String.valueOf(((System.currentTimeMillis() - startTimestamp) / 1000)));
		FormatUtils.getInstance().logMemoryUsage("getPuppeteerTestValueObject() ends. ip=" + ip + ",puppeteerTestValueObject=" + puppeteerTestValueObject.toString());
		return puppeteerTestValueObject;
	}
}
