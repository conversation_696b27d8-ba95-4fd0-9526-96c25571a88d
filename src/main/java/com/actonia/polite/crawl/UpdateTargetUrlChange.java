package com.actonia.polite.crawl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.concurrency.ThreadPoolService;
import com.actonia.dao.OwnDomainEntityDAO;
import com.actonia.dao.TargetUrlChangeIndClickHouseDAO;
import com.actonia.dao.TargetUrlDailyCrawlTrackingEntityDAO;
import com.actonia.dao.TargetUrlHtmlClickHouseDAO;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
import com.actonia.entity.TargetUrlDailyCrawlTrackingEntity;
import com.actonia.utils.CommonUtils;
import com.actonia.utils.ContentGuardUtils;
import com.actonia.utils.CrawlerUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.PutMessageUtils;
import com.actonia.utils.SpringBeanFactory;

public class UpdateTargetUrlChange {

	private static ThreadPoolService threadPoolService = ThreadPoolService.getInstance();

	//private Boolean isDebug = false;
	private OwnDomainEntityDAO ownDomainEntityDAO;
	private TargetUrlDailyCrawlTrackingEntityDAO targetUrlDailyCrawlTrackingEntityDAO;

	// map key = yyyymmddhhmm
	// map value = list of TargetUrlChangeIndClickHouseEntity to be persisted
	private static ConcurrentMap<String, List<TargetUrlChangeIndClickHouseEntity>> timestampTargetUrlChangeIndClickHouseEntityListMap = new ConcurrentHashMap<String, List<TargetUrlChangeIndClickHouseEntity>>();

	private boolean isStartEndTimestampOverride = false;

	public UpdateTargetUrlChange() {
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		targetUrlDailyCrawlTrackingEntityDAO = SpringBeanFactory.getBean("targetUrlDailyCrawlTrackingEntityDAO");
	}

	public static void main(String args[]) {
		try {
			new UpdateTargetUrlChange().process(args);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void process(String args[]) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("process() begins.");
		long startTimestamp = System.currentTimeMillis();
		try {
			threadPoolService.init();
			CommonUtils.initThreads(6);
			FormatUtils.getInstance().logMemoryUsage("process() initialized 6 threads.");

			// pause for five minutes
			try {
				FormatUtils.getInstance().logMemoryUsage("process() pausing for five minutes begins.");
				Thread.sleep(300000);
				FormatUtils.getInstance().logMemoryUsage("process() pausing for five minutes ends.");
			} catch (InterruptedException e) {
				e.printStackTrace();
			}

			while (true) {
				updateLoop(args);
				if (isStartEndTimestampOverride == true) {
					break;
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			FormatUtils.getInstance().logMemoryUsage("process() b4 threadPoolService.destroy().");
			threadPoolService.destroy();
			FormatUtils.getInstance().logMemoryUsage("process() af threadPoolService.destroy().");
		}
		FormatUtils.getInstance().logMemoryUsage("process() ends. elapsed time in sec.:" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private void updateLoop(String args[]) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("updateLoop() begins.");
		String domainIdsString = null;
		long startTimestamp = System.currentTimeMillis();
		String startCrawlTimestampString = null;
		String startTrackDateString = null;
		Boolean isExecDomainIdsInd = null;
		Set<Integer> runtimeDomainSet = null;
		Properties domainProperties = new Properties();
		String execDomainIds = null;
		String notExecDomainIds = null;
		List<Integer> skipDomainIdList = null;
		List<Integer> testDomainIdList = null;
		StringBuilder stringBuilder = null;
		List<OwnDomainEntity> allOwnDomainEntityList = null;
		List<OwnDomainEntity> filteredOwnDomainEntityList = null;
		List<String> changeTrackingIndicatorList = null;
		String endCrawlTimestampString = null;
		String endTrackDateString = null;

		TargetUrlHtmlClickHouseDAO.getInstance();
		TargetUrlChangeIndClickHouseDAO.getInstance();
		PutMessageUtils.getInstance();

		if (args != null && args.length > 0) {
			// runtime parameter 1 (optional): start crawl timestamp
			if (args.length >= 1) {
				startCrawlTimestampString = args[0];
				FormatUtils.getInstance().logMemoryUsage("updateLoop() runtime parameter 1:start crawl timestamp string=" + startCrawlTimestampString);
			}

			// runtime parameter 2 (optional): end crawl timestamp
			if (args.length >= 2) {
				endCrawlTimestampString = args[1];
				FormatUtils.getInstance().logMemoryUsage("updateLoop() runtime parameter 2:end crawl timestamp string=" + endCrawlTimestampString);
			}

			// runtime parameter 3 (optional): list of domain IDs
			if (args.length >= 3) {
				domainIdsString = args[2];
				FormatUtils.getInstance().logMemoryUsage("updateLoop() runtime parameter 3:list of domain IDs=" + domainIdsString);
			}
		}

		try {
			domainProperties.load(UpdateTargetUrlChange.class.getResourceAsStream("/domain.properties"));
		} catch (Exception e) {
			FormatUtils.getInstance().logMemoryUsage("updateLoop() no domain.properties file found");
			return;
		}

		if (StringUtils.isNotBlank(domainIdsString)) {
			execDomainIds = domainIdsString;
			notExecDomainIds = null;
		} else {
			execDomainIds = domainProperties.getProperty("exec.domain");
			notExecDomainIds = domainProperties.getProperty("notexec.domain");
			skipDomainIdList = new ArrayList<Integer>();
			if (StringUtils.isNotBlank(notExecDomainIds)) {
				runtimeDomainSet = CommonUtils.getDomainSet(notExecDomainIds);
				for (Integer notExecDomainId : runtimeDomainSet) {
					skipDomainIdList.add(notExecDomainId);
				}
			}
			// skip 'Expedia'
			testDomainIdList = ownDomainEntityDAO.getDomainIdsByCompanyName(IConstants.COMPANY_NAME_EXPEDIA);
			if (testDomainIdList != null && testDomainIdList.size() > 0) {
				skipDomainIdList.addAll(testDomainIdList);
			}
			// skip 'CarRentals.com'
			testDomainIdList = ownDomainEntityDAO.getDomainIdsByCompanyName(IConstants.COMPANY_NAME_CARRENTALS_COM);
			if (testDomainIdList != null && testDomainIdList.size() > 0) {
				skipDomainIdList.addAll(testDomainIdList);
			}
			// skip 'Coupon Cabin'
			testDomainIdList = ownDomainEntityDAO.getDomainIdsByCompanyName(IConstants.COMPANY_NAME_COUPON_CABIN);
			if (testDomainIdList != null && testDomainIdList.size() > 0) {
				skipDomainIdList.addAll(testDomainIdList);
			}
			// skip 'RetailMeNot.com'
			testDomainIdList = ownDomainEntityDAO.getDomainIdsByCompanyName(IConstants.COMPANY_NAME_RETAIL_ME_NOT_COM);
			if (testDomainIdList != null && testDomainIdList.size() > 0) {
				skipDomainIdList.addAll(testDomainIdList);
			}
			stringBuilder = null;
			if (skipDomainIdList != null && skipDomainIdList.size() > 0) {
				for (Integer skipDomainId : skipDomainIdList) {
					if (stringBuilder == null) {
						stringBuilder = new StringBuilder();
					} else {
						stringBuilder.append(IConstants.COMMA);
					}
					stringBuilder.append(skipDomainId);
				}
				notExecDomainIds = stringBuilder.toString();
				// https://www.wrike.com/open.htm?id=*********
				notExecDomainIds = StringUtils.remove(notExecDomainIds, ",4765");
			}
		}
		FormatUtils.getInstance().logMemoryUsage("updateLoop() execDomainIds=" + execDomainIds);
		FormatUtils.getInstance().logMemoryUsage("updateLoop() notExecDomainIds=" + notExecDomainIds);

		if (StringUtils.isBlank(domainIdsString)) {
			allOwnDomainEntityList = ownDomainEntityDAO.queryForDistinctDomainIds();
		} else {
			allOwnDomainEntityList = new ArrayList<OwnDomainEntity>();
			allOwnDomainEntityList.add(ownDomainEntityDAO.getOwnDomainEntityById(NumberUtils.toInt(domainIdsString)));
		}
		if (allOwnDomainEntityList == null || allOwnDomainEntityList.size() == 0) {
			FormatUtils.getInstance().logMemoryUsage("updateLoop() allOwnDomainEntityList is empty.");
			return;
		}

		// process specific domains
		if (StringUtils.isNotBlank(execDomainIds)) {
			runtimeDomainSet = CommonUtils.getDomainSet(execDomainIds);
			isExecDomainIdsInd = true;
		}
		// do not process specific domains
		else if (StringUtils.isNotBlank(notExecDomainIds)) {
			runtimeDomainSet = CommonUtils.getDomainSet(notExecDomainIds);
			isExecDomainIdsInd = false;
		}
		// process all domains
		else {
			runtimeDomainSet = null;
			isExecDomainIdsInd = null;
		}

		filteredOwnDomainEntityList = PutMessageUtils.getInstance().filterDomains(allOwnDomainEntityList, isExecDomainIdsInd, runtimeDomainSet);
		if (filteredOwnDomainEntityList == null || filteredOwnDomainEntityList.size() == 0) {
			FormatUtils.getInstance().logMemoryUsage("updateLoop() filteredOwnDomainEntityList is empty.");
			return;
		}

		changeTrackingIndicatorList = ContentGuardUtils.getInstance().getChangeTrackingIndicatorList();
		if (changeTrackingIndicatorList == null || changeTrackingIndicatorList.size() == 0) {
			FormatUtils.getInstance().logMemoryUsage("updateLoop() changeTrackingIndicatorList is empty.");
			return;
		}
		changeTrackingIndicatorList.remove(IConstants.ROBOTS_TXT_CHG_IND);

		if (StringUtils.isBlank(startCrawlTimestampString) && StringUtils.isBlank(endCrawlTimestampString)) {
			startCrawlTimestampString = getStartCrawlTimestampString();
			endCrawlTimestampString = DateFormatUtils.format(DateUtils.addMinutes(new Date(), -5), IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
			isStartEndTimestampOverride = false;
		} else {
			isStartEndTimestampOverride = true;
		}
		startTrackDateString = StringUtils.substringBefore(startCrawlTimestampString, IConstants.ONE_SPACE);
		endTrackDateString = StringUtils.substringBefore(endCrawlTimestampString, IConstants.ONE_SPACE);
		FormatUtils.getInstance()
				.logMemoryUsage("updateLoop() startTrackDateString=" + startTrackDateString + ",startCrawlTimestampString=" + startCrawlTimestampString
						+ ",endTrackDateString=" + endTrackDateString + ",endCrawlTimestampString=" + endCrawlTimestampString + ",isStartEndTimestampOverride="
						+ isStartEndTimestampOverride);
		processDomainsConcurrently(filteredOwnDomainEntityList, startTrackDateString, startCrawlTimestampString, endTrackDateString, endCrawlTimestampString,
				changeTrackingIndicatorList);
		if (isStartEndTimestampOverride == false) {
			maintainTrackingTimestamp(endCrawlTimestampString);
		}

		FormatUtils.getInstance().logMemoryUsage("updateLoop() ends. elapsed (ms.)=" + (System.currentTimeMillis() - startTimestamp));

		try {
			Thread.sleep(30000);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private String getStartCrawlTimestampString() {
		String startCrawlTimestampString = null;
		int domainId = 0;
		Date currentTimetamp = null;
		Date fiveMinutesAgo = null;
		TargetUrlDailyCrawlTrackingEntity targetUrlDailyCrawlTrackingEntity = targetUrlDailyCrawlTrackingEntityDAO.get(IConstants.PROCESS_TYPE_NUMBER_TARGET_URL_CHANGE,
				domainId);
		if (targetUrlDailyCrawlTrackingEntity != null && StringUtils.isNotBlank(targetUrlDailyCrawlTrackingEntity.getUpdateTargetUrlChangeTimestamp())) {
			FormatUtils.getInstance().logMemoryUsage("getStartCrawlTimestampString() targetUrlDailyCrawlTrackingEntity.getUpdateTargetUrlChangeTimestamp()="
					+ targetUrlDailyCrawlTrackingEntity.getUpdateTargetUrlChangeTimestamp());
			startCrawlTimestampString = targetUrlDailyCrawlTrackingEntity.getUpdateTargetUrlChangeTimestamp();
		} else {
			currentTimetamp = new Date();
			fiveMinutesAgo = DateUtils.addMinutes(currentTimetamp, -5);
			FormatUtils.getInstance().logMemoryUsage("getStartCrawlTimestampString() fiveMinutesAgo=" + fiveMinutesAgo);
			startCrawlTimestampString = DateFormatUtils.format(fiveMinutesAgo, IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
		}

		FormatUtils.getInstance().logMemoryUsage("getStartCrawlTimestampString() startCrawlTimestampString=" + startCrawlTimestampString);
		return startCrawlTimestampString;
	}

	private void processDomainsConcurrently(List<OwnDomainEntity> ownDomainEntityList, String startTrackDateString, String startCrawlTimestampString,
			String endTrackDateString, String endCrawlTimestampString, List<String> changeTrackingIndicatorList) throws Exception {
		FormatUtils.getInstance()
				.logMemoryUsage("processDomainsConcurrently() begins. ownDomainEntityList.size()=" + ownDomainEntityList.size() + ",startTrackDateString="
						+ startTrackDateString + ",startCrawlTimestampString=" + startCrawlTimestampString + ",endTrackDateString=" + endTrackDateString
						+ ",endCrawlTimestampString=" + endCrawlTimestampString);

		int totalNumberOfDomains = ownDomainEntityList.size();
		long startTimestamp = System.currentTimeMillis();
		String ip = null;
		OwnDomainEntity ownDomainEntity = null;
		UpdateTargetUrlChangeCommand updateTargetUrlChangeCommand = null;
		int numberOfDomainsProcessed = 0;

		do {
			ip = CacheModleFactory.getInstance().getAliveIpAddress();
			if (ip == null) {
				continue;
			}
			ownDomainEntity = ownDomainEntityList.get(numberOfDomainsProcessed++);
			updateTargetUrlChangeCommand = getUpdateTargetUrlChangeCommand(ip, ownDomainEntity.getId(), startTrackDateString, startCrawlTimestampString,
					endTrackDateString, endCrawlTimestampString, changeTrackingIndicatorList);
			threadPoolService.execute(updateTargetUrlChangeCommand);
		} while (numberOfDomainsProcessed < totalNumberOfDomains);

		do {
			try {
				Thread.sleep(1000);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		} while (threadPoolService.getThreadPool().getActiveCount() > 0);

		CrawlerUtils.getInstance().maintainTargetUrlChangeIndDataStore(ip, IConstants.EMPTY_STRING);

		FormatUtils.getInstance()
				.logMemoryUsage("processDomainsConcurrently() ends. startTrackDateString=" + startTrackDateString + ",startCrawlTimestampString="
						+ startCrawlTimestampString + ",endTrackDateString=" + endTrackDateString + ",endCrawlTimestampString=" + endCrawlTimestampString
						+ "elapsed time in seconds=" + ((System.currentTimeMillis() - startTimestamp) / 1000));
	}

	private UpdateTargetUrlChangeCommand getUpdateTargetUrlChangeCommand(String ip, int domainId, String startTrackDateString, String startCrawlTimestampString,
			String endTrackDateString, String endCrawlTimestampString, List<String> changeTrackingIndicatorList) {
		UpdateTargetUrlChangeCommand updateTargetUrlChangeCommand = new UpdateTargetUrlChangeCommand(ip, domainId, startTrackDateString, startCrawlTimestampString,
				endTrackDateString, endCrawlTimestampString, changeTrackingIndicatorList);
		updateTargetUrlChangeCommand.setStatus(true);
		return updateTargetUrlChangeCommand;
	}

	private void maintainTrackingTimestamp(String updateTargetUrlChangeTimestamp) {
		int domainId = 0;
		TargetUrlDailyCrawlTrackingEntity targetUrlDailyCrawlTrackingEntity = targetUrlDailyCrawlTrackingEntityDAO.get(IConstants.PROCESS_TYPE_NUMBER_TARGET_URL_CHANGE,
				domainId);
		if (targetUrlDailyCrawlTrackingEntity != null) {
			targetUrlDailyCrawlTrackingEntity.setUpdateTargetUrlChangeTimestamp(updateTargetUrlChangeTimestamp);
			targetUrlDailyCrawlTrackingEntityDAO.update(targetUrlDailyCrawlTrackingEntity.getProcessType(), targetUrlDailyCrawlTrackingEntity.getDomainId(),
					targetUrlDailyCrawlTrackingEntity.getUpdateTargetUrlChangeTimestamp());
			FormatUtils.getInstance().logMemoryUsage("maintainTrackingTimestamp() updated processType=" + IConstants.PROCESS_TYPE_NUMBER_TARGET_URL_CHANGE
					+ ",domainId=" + domainId + ",updateTargetUrlChangeTimestamp=" + targetUrlDailyCrawlTrackingEntity.getUpdateTargetUrlChangeTimestamp());
		} else {
			targetUrlDailyCrawlTrackingEntity = new TargetUrlDailyCrawlTrackingEntity();
			targetUrlDailyCrawlTrackingEntity.setProcessType(IConstants.PROCESS_TYPE_NUMBER_TARGET_URL_CHANGE);
			targetUrlDailyCrawlTrackingEntity.setDomainId(domainId);
			targetUrlDailyCrawlTrackingEntity.setDomainName(IConstants.EMPTY_STRING);
			targetUrlDailyCrawlTrackingEntity.setTrackDate(0);
			targetUrlDailyCrawlTrackingEntity.setTotalUrls(0);
			targetUrlDailyCrawlTrackingEntity.setUpdateTargetUrlChangeTimestamp(updateTargetUrlChangeTimestamp);
			targetUrlDailyCrawlTrackingEntityDAO.create(targetUrlDailyCrawlTrackingEntity);
			FormatUtils.getInstance().logMemoryUsage("maintainTrackingTimestamp() created processType=" + IConstants.PROCESS_TYPE_NUMBER_TARGET_URL_CHANGE
					+ ",domainId=" + domainId + ",updateTargetUrlChangeTimestamp=" + targetUrlDailyCrawlTrackingEntity.getUpdateTargetUrlChangeTimestamp());
		}
	}

	public static ConcurrentMap<String, List<TargetUrlChangeIndClickHouseEntity>> getTimestampTargetUrlChangeIndClickHouseEntityListMap() {
		return timestampTargetUrlChangeIndClickHouseEntityListMap;
	}

	public static void setTimestampTargetUrlChangeIndClickHouseEntityListMap(
			ConcurrentMap<String, List<TargetUrlChangeIndClickHouseEntity>> timestampTargetUrlChangeIndClickHouseEntityListMap) {
		UpdateTargetUrlChange.timestampTargetUrlChangeIndClickHouseEntityListMap = timestampTargetUrlChangeIndClickHouseEntityListMap;
	}
}
