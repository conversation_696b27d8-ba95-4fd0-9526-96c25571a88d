package com.actonia.polite.crawl;

import com.actonia.concurrency.BaseThreadCommand;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.utils.FormatUtils;

public class NearDuplicateDetectionCommand extends BaseThreadCommand {

	//private Boolean isDebug = false;
	private String ip;
	private int domainId;
	private String competitorUrlString;

	public NearDuplicateDetectionCommand(String ip, int domainId, String competitorUrlString) {
		super();
		this.ip = ip;
		this.domainId = domainId;
		this.competitorUrlString = competitorUrlString;
	}

	@Override
	protected void execute() throws Exception {
		try {
			retrieveCompetitorUrlData();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			CacheModleFactory.getInstance().setAliveIpAddress(ip);
		}
	}

	@Override
	protected void undo() throws Exception {
		// TODO Auto-generated method stub

	}

	private void retrieveCompetitorUrlData() throws Exception {
		//long startTimestamp = System.currentTimeMillis();
		//FormatUtils.getInstance().logMemoryUsage("retrieveCompetitorUrlData() ip=" + ip + " begins. domainId=" + domainId);
		FormatUtils.getInstance().logMemoryUsage("retrieveCompetitorUrlData() ip=" + ip + ",domainId=" + domainId + ",competitorUrlString=" + competitorUrlString);
		//FormatUtils.getInstance()
		//		.logMemoryUsage("retrieveCompetitorUrlData() ip=" + ip + " ends. domainId=" + domainId + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}
}
