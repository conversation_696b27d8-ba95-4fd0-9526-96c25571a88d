package com.actonia.polite.crawl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang.StringUtils;

import com.actonia.IConstants;
import com.actonia.concurrency.BaseThreadCommand;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.dao.CompetitorUrlHtmlClickHouseDAO;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.Md5Util;
import com.actonia.utils.PutMessageUtils;
import com.actonia.value.object.AdHocCrawlValueObject;

public class AdHocCrawlCommand extends BaseThreadCommand {

	//private boolean isDebug = false;

	private String ip;
	private String domainName;

	public AdHocCrawlCommand(String ip, String domainName) {
		super();
		this.ip = ip;
		this.domainName = domainName;
	}

	@Override
	protected void undo() throws Exception {
		// TODO Auto-generated method stub
	}

	@Override
	protected void execute() {
		long startTimestamp = System.currentTimeMillis();
		//FormatUtils.getInstance().logMemoryUsage("execute() begins. ip=" + ip + ",domainName=" + domainName);
		try {
			process();
		} catch (Exception e) {
			FormatUtils.getInstance().logMemoryUsage("execute() ip=" + ip + ",domainName=" + domainName + ",exception message=" + e.getMessage());
			e.printStackTrace();
		} finally {
			CacheModleFactory.getInstance().setAliveIpAddress(ip);
			//FormatUtils.getInstance().logMemoryUsage(
			//		"execute() ends. ip=" + ip + ",domainName=" + domainName + ",elapsed time in sec.=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		}
	}

	private void process() throws Exception {
		AdHocCrawlValueObject adHocCrawlValueObject = null;
		String urlString = null;
		String outputLine = null;
		List<String> outputLineList = new ArrayList<String>();
		Set<String> urlHashCodeSet = null;
		String reversedUrlDomain = null;
		List<String> reversedUrlDomainList = null;
		HtmlClickHouseEntity htmlClickHouseEntity = null;

		// map key = competitor URL MD5 hash code
		// map value = competitor URL's latest historical crawled data
		Map<String, HtmlClickHouseEntity> competitorUrlHtmlClickHouseEntityMap = null;

		if (AdHocCrawl.getDomainNameUrlHashSetMap().containsKey(domainName)) {
			reversedUrlDomain = PutMessageUtils.getInstance().getReversedDomainName(domainName);
			reversedUrlDomainList = new ArrayList<String>();
			reversedUrlDomainList.add(reversedUrlDomain);
			competitorUrlHtmlClickHouseEntityMap = getCompetitorUrlHtmlClickHouseEntityMap(reversedUrlDomainList);
			urlHashCodeSet = AdHocCrawl.getDomainNameUrlHashSetMap().get(domainName);
			if (urlHashCodeSet != null && urlHashCodeSet.size() > 0) {
				//FormatUtils.getInstance().logMemoryUsage("process() ip=" + ip + ",domainName=" + domainName + ",urlHashCodeSet.size()=" + urlHashCodeSet.size());
				nextUrlHashCode: for (String urlHashCode : urlHashCodeSet) {
					urlString = AdHocCrawl.getUrlHashCodeUrlStringMap().get(urlHashCode);
					adHocCrawlValueObject = new AdHocCrawlValueObject();
					adHocCrawlValueObject.setUrlString(urlString);
					if (competitorUrlHtmlClickHouseEntityMap.containsKey(urlHashCode)) {
						htmlClickHouseEntity = competitorUrlHtmlClickHouseEntityMap.get(urlHashCode);
						adHocCrawlValueObject.setResponseCode(htmlClickHouseEntity.getCrawlerResponse().getResponse_code());
						adHocCrawlValueObject.setTitle(FormatUtils.getInstance().trimText(htmlClickHouseEntity.getCrawlerResponse().getTitle()));
						adHocCrawlValueObject.setExistsInClarityDB(IConstants.TRUE);
					} else {
						adHocCrawlValueObject.setResponseCode(IConstants.EMPTY_STRING);
						adHocCrawlValueObject.setTitle(IConstants.EMPTY_STRING);
						adHocCrawlValueObject.setExistsInClarityDB(IConstants.FALSE);
					}
					outputLine = getOutputLine(adHocCrawlValueObject);
					outputLineList.add(outputLine);
					//if (isDebug == true) {
					//	System.out.println("process() adHocCrawlValueObject=" + adHocCrawlValueObject.toString());
					//	if (outputLineList.size() >= 1) {
					//		break nextUrlHashCode;
					//	}
					//}
				}
			}
			FormatUtils.getInstance().logMemoryUsage("process() ip=" + ip + ",domainName=" + domainName + ",outputLineList.size()=" + outputLineList.size());
			if (outputLineList != null && outputLineList.size() > 0) {
				AdHocCrawl.getOutputList().addAll(outputLineList);
			}
		}
	}

	// map key = competitor URL MD5 hash code
	// map value = competitor URL's latest historical crawled data
	public Map<String, HtmlClickHouseEntity> getCompetitorUrlHtmlClickHouseEntityMap(List<String> reversedUrlDomainList) throws Exception {
		long startTimestamp = System.currentTimeMillis();

		// map key = competitor URL MD5 hash code
		// map value = competitor URL's daily HtmlClickHouseEntity
		Map<String, HtmlClickHouseEntity> competitorUrlHtmlClickHouseEntityMap = new HashMap<String, HtmlClickHouseEntity>();

		String competitorUrlString = null;
		String competitorUrlMd5HashCode = null;

		List<String> competitorUrlHtmlFieldNames = new ArrayList<String>();
		competitorUrlHtmlFieldNames.add(IConstants.URL);
		competitorUrlHtmlFieldNames.add(IConstants.RESPONSE_CODE);
		competitorUrlHtmlFieldNames.add(IConstants.TITLE);

		boolean filterByResponseCode = false;

		List<HtmlClickHouseEntity> htmlClickHouseEntityList = CompetitorUrlHtmlClickHouseDAO.getInstance().getLatestFromHistorical(ip, reversedUrlDomainList,
				competitorUrlHtmlFieldNames, null, filterByResponseCode);
		if (htmlClickHouseEntityList != null && htmlClickHouseEntityList.size() > 0) {
			for (HtmlClickHouseEntity htmlClickHouseEntity : htmlClickHouseEntityList) {
				competitorUrlString = htmlClickHouseEntity.getUrl();
				htmlClickHouseEntity.setUrl(null);
				if (StringUtils.isNotBlank(competitorUrlString) == true) {
					competitorUrlMd5HashCode = Md5Util.Md5(StringUtils.trim(competitorUrlString));
					competitorUrlHtmlClickHouseEntityMap.put(competitorUrlMd5HashCode, htmlClickHouseEntity);
				}
			}
		}
		//FormatUtils.getInstance()
		//		.logMemoryUsage("getCompetitorUrlHtmlClickHouseEntityMap() ip=" + ip + ",reversedUrlDomainList.size()=" + reversedUrlDomainList.size()
		//				+ ",competitorUrlHtmlClickHouseEntityMap.size()=" + competitorUrlHtmlClickHouseEntityMap.size() + ",elapsed(ms.)="
		//				+ (System.currentTimeMillis() - startTimestamp));
		return competitorUrlHtmlClickHouseEntityMap;
	}

	private String getOutputLine(AdHocCrawlValueObject adHocCrawlValueObject) {
		StringBuilder stringBuilder = new StringBuilder();
		String outputLine = null;
		stringBuilder = new StringBuilder();
		stringBuilder.append(adHocCrawlValueObject.getUrlString());
		stringBuilder.append(IConstants.BACKTICK);
		stringBuilder.append(adHocCrawlValueObject.getExistsInClarityDB());
		stringBuilder.append(IConstants.BACKTICK);
		stringBuilder.append(adHocCrawlValueObject.getResponseCode());
		stringBuilder.append(IConstants.BACKTICK);
		if (StringUtils.equalsIgnoreCase(adHocCrawlValueObject.getResponseCode(), "200")) {
			stringBuilder.append(adHocCrawlValueObject.getTitle());
		} else {
			stringBuilder.append(IConstants.EMPTY_STRING);
		}
		outputLine = stringBuilder.toString();
		return outputLine;
	}
}
