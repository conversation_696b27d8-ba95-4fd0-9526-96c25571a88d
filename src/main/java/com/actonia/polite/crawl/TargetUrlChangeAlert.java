package com.actonia.polite.crawl;

import java.util.*;

import com.actonia.dao.*;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;

import com.actonia.IConstants;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.concurrency.ThreadPoolService;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.utils.CommonUtils;
import com.actonia.utils.ContentGuardUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.PutMessageUtils;
import com.actonia.utils.SpringBeanFactory;

public class TargetUrlChangeAlert {

	//private Boolean isDebug = false;

	private static ThreadPoolService threadPoolService = ThreadPoolService.getInstance();

	private OwnDomainEntityDAO ownDomainEntityDAO;
	private TargetUrlChangeAlertDAO targetUrlChangeAlertDAO;
	private final Map<String, Integer> changeIndicatorIdMap;

	public TargetUrlChangeAlert() {
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		targetUrlChangeAlertDAO = SpringBeanFactory.getBean("targetUrlChangeAlertDAO");
		try {
			changeIndicatorIdMap = ChangeIndMasterClickHouseDAO.getInstance().indicatorReverseIdMap;
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	public static void main(String args[]) {
		FormatUtils.getInstance().logMemoryUsage("main() begins.");
		long startTimestamp = System.currentTimeMillis();
		new TargetUrlChangeAlert().process(args);
		FormatUtils.getInstance().logMemoryUsage("main() ends. elapsed time in sec.:" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	public void process(String args[]) {
		long startTimestamp = System.currentTimeMillis();
		int alertFrequencyType = 0;
		String testString = null;
		String domainIdsOverride = null;
		String crawlDateOverride = null;
		Integer crawlHourOverride = null;
		boolean isDebug = false;
		try {
			threadPoolService.init();
			CommonUtils.initThreads(8);

			if (args != null && args.length > 0) {

				// runtime parameter 1 (required) for the alert frequency type
				if (args.length >= 1) {
					testString = args[0];
					if (StringUtils.isNotBlank(testString)) {
						alertFrequencyType = NumberUtils.toInt(testString);
						if (alertFrequencyType != IConstants.FREQUENCY_TYPE_DAILY && alertFrequencyType != IConstants.FREQUENCY_TYPE_HOURLY) {
							FormatUtils.getInstance().logMemoryUsage("process() Must specify alert frequency type in runtime parameters: 1=daily, or 2=hourly.");
							return;
						}
						FormatUtils.getInstance().logMemoryUsage("process() runtime parameter 1: alert frequency type=" + alertFrequencyType);
					} else {
						FormatUtils.getInstance().logMemoryUsage("process() Must specify alert frequency type in runtime parameters: 1=daily, or 2=hourly.");
						return;
					}
				} else {
					FormatUtils.getInstance().logMemoryUsage("process() Must specify alert frequency type in runtime parameters: 1=daily, or 2=hourly.");
					return;
				}

				// runtime parameter (optional): is test? true/false
				if (args.length >= 2) {
					isDebug = BooleanUtils.toBooleanObject(args[1]);
					FormatUtils.getInstance().logMemoryUsage("process() runtime parameter 2: isDebug=" + isDebug);
				}

				// runtime parameter (optional): domain IDs
				if (args.length >= 3) {
					domainIdsOverride = args[2];
					FormatUtils.getInstance().logMemoryUsage("process() runtime parameter 3: domain IDs override=" + domainIdsOverride);
				}

				// runtime parameter (optional): crawl date override YYYY-MM-DD
				if (args.length >= 4) {
					crawlDateOverride = args[3];
					FormatUtils.getInstance().logMemoryUsage("process() runtime parameter 4: crawl date override=" + crawlDateOverride);
				}

				// runtime parameter (optional): crawl hour override 00 - 23
				if (args.length >= 5) {
					testString = args[4];
					if (StringUtils.isNotBlank(testString)) {
						crawlHourOverride = NumberUtils.toInt(testString);
						FormatUtils.getInstance().logMemoryUsage("process() runtime parameter 5: crawl hour override=" + crawlHourOverride);
					}
				}
			} else {
				FormatUtils.getInstance().logMemoryUsage("process() Must specify runtime parameters.");
				return;
			}

			Properties subserverProperties = new Properties();
			try {
				subserverProperties.load(TargetUrlChangeAlert.class.getResourceAsStream("/subserver.properties"));
			} catch (Exception e) {
				FormatUtils.getInstance().logMemoryUsage("process() no subserver.properties file found");
				return;
			}

			Boolean isExecDomainIdsInd = null;
			Set<Integer> runtimeDomainSet = null;

			Properties domainProperties = new Properties();
			try {
				domainProperties.load(TargetUrlChangeAlert.class.getResourceAsStream("/domain.properties"));
			} catch (Exception e) {
				FormatUtils.getInstance().logMemoryUsage("process() no domain.properties file found");
				return;
			}

			String execDomainIds = domainProperties.getProperty("exec.domain");
			String notExecDomainIds = domainProperties.getProperty("notexec.domain");
			if (StringUtils.isNotBlank(domainIdsOverride)) {
				execDomainIds = domainIdsOverride;
				notExecDomainIds = null;
			}
			FormatUtils.getInstance().logMemoryUsage("process() execDomainIds=" + execDomainIds);
			FormatUtils.getInstance().logMemoryUsage("process() notExecDomainIds=" + notExecDomainIds);

			List<OwnDomainEntity> allOwnDomainEntityList = ownDomainEntityDAO.queryForAll();
			if (allOwnDomainEntityList == null || allOwnDomainEntityList.size() == 0) {
				FormatUtils.getInstance().logMemoryUsage("process() allOwnDomainEntityList is empty.");
				return;
			}

			// process specific domains
			if (StringUtils.isNotBlank(execDomainIds)) {
				runtimeDomainSet = CommonUtils.getDomainSet(execDomainIds);
				isExecDomainIdsInd = true;
			}
			// do not process specific domains
			else if (StringUtils.isNotBlank(notExecDomainIds)) {
				runtimeDomainSet = CommonUtils.getDomainSet(notExecDomainIds);
				isExecDomainIdsInd = false;
			}
			// process all domains
			else {
				runtimeDomainSet = null;
				isExecDomainIdsInd = null;
			}

			List<OwnDomainEntity> testFilteredOwnDomainEntityList = PutMessageUtils.getInstance().filterDomains(allOwnDomainEntityList, isExecDomainIdsInd,
					runtimeDomainSet);
			if (testFilteredOwnDomainEntityList == null || testFilteredOwnDomainEntityList.size() == 0) {
				FormatUtils.getInstance().logMemoryUsage("process() testFilteredOwnDomainEntityList is empty.");
				return;
			}

			List<Integer> targetUrlChangeAlertDomainIdList = targetUrlChangeAlertDAO.getDomainIdsByAlertFrequency(alertFrequencyType);
			if (targetUrlChangeAlertDomainIdList == null || targetUrlChangeAlertDomainIdList.size() == 0) {
				FormatUtils.getInstance().logMemoryUsage("process() targetUrlChangeAlertDomainIdList is empty.");
				return;
			}

			List<OwnDomainEntity> filteredOwnDomainEntityList = new ArrayList<OwnDomainEntity>();
			for (Integer targetUrlChangeAlertDomainId : targetUrlChangeAlertDomainIdList) {
				nextOwnDomainEntity: for (OwnDomainEntity testOwnDomainEntity : testFilteredOwnDomainEntityList) {
					if (targetUrlChangeAlertDomainId.intValue() == testOwnDomainEntity.getId().intValue()) {
						filteredOwnDomainEntityList.add(testOwnDomainEntity);
						break nextOwnDomainEntity;
					}
				}
			}
			if (filteredOwnDomainEntityList == null || filteredOwnDomainEntityList.size() == 0) {
				FormatUtils.getInstance().logMemoryUsage("process() filteredOwnDomainEntityList is empty.");
				return;
			}

			HtmlChangeClickHouseDAO.getInstance();
			TargetUrlChangeIndClickHouseDAO.getInstance();
			ContentGuardUtils.getInstance();
			processDomainsConcurrently(filteredOwnDomainEntityList, alertFrequencyType, crawlDateOverride, crawlHourOverride, isDebug);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			threadPoolService.destroy();
			FormatUtils.getInstance().logMemoryUsage("process() ends. elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		}
	}

	private void processDomainsConcurrently(List<OwnDomainEntity> ownDomainEntityList, int alertFrequencyType, String crawlDateOverride, Integer crawlHourOverride,
			boolean isDebug) throws Exception {
		FormatUtils.getInstance()
				.logMemoryUsage("processDomainsConcurrently() begins. ownDomainEntityList.size()=" + ownDomainEntityList.size() + ",alertFrequencyType="
						+ alertFrequencyType + ",crawlDateOverride=" + crawlDateOverride + ",crawlHourOverride=" + crawlHourOverride + ",isDebug=" + isDebug);

		int totalNumberOfDomains = ownDomainEntityList.size();
		long startTimestamp = System.currentTimeMillis();
		String ip = null;
		OwnDomainEntity ownDomainEntity = null;
		TargetUrlChangeAlertCommand targetUrlChangeAlertCommand = null;
		int numberOfDomainsProcessed = 0;

		do {
			ip = CacheModleFactory.getInstance().getAliveIpAddress();
			if (ip == null) {
				continue;
			}
			ownDomainEntity = ownDomainEntityList.get(numberOfDomainsProcessed++);
			targetUrlChangeAlertCommand = getTargetUrlChangeAlertCommand(ip, ownDomainEntity, alertFrequencyType, crawlDateOverride, crawlHourOverride, isDebug);
			if (targetUrlChangeAlertCommand != null) {
				try {
					FormatUtils.getInstance().logMemoryUsage(
							"processDomainsConcurrently() ip acquired=" + ip + ",domain=" + ownDomainEntity.getId() + " - " + ownDomainEntity.getDomain());
					threadPoolService.execute(targetUrlChangeAlertCommand);
				} catch (Exception e) {
					e.printStackTrace();
				}
			} else {
				CacheModleFactory.getInstance().setAliveIpAddress(ip);
			}
		} while (numberOfDomainsProcessed < totalNumberOfDomains);

		do {
			try {
				Thread.sleep(1000);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		} while (threadPoolService.getThreadPool().getActiveCount() > 0);

		FormatUtils.getInstance().logMemoryUsage("processDomainsConcurrently() end. elapsed time in seconds=" + ((System.currentTimeMillis() - startTimestamp) / 1000));
	}

	private TargetUrlChangeAlertCommand getTargetUrlChangeAlertCommand(String ip, OwnDomainEntity ownDomainEntity, int alertFrequencyType, String crawlDateOverride,
			Integer crawlHourOverride, boolean isDebug) {
		TargetUrlChangeAlertCommand targetUrlChangeAlertCommand = new TargetUrlChangeAlertCommand(ip, ownDomainEntity, alertFrequencyType, crawlDateOverride,
				crawlHourOverride, isDebug, this.changeIndicatorIdMap);
		targetUrlChangeAlertCommand.setStatus(true);
		return targetUrlChangeAlertCommand;
	}
}
