package com.actonia.polite.crawl;

import java.io.File;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;

import com.actonia.IConstants;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.concurrency.ThreadPoolService;
import com.actonia.utils.CommonUtils;
import com.actonia.utils.CrawlerUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.Md5Util;

public class AdHocCrawl {

	private boolean isDebug = false;

	private static ThreadPoolService threadPool = ThreadPoolService.getInstance();

	// map key = URL string hash code
	// map value = URL string
	private static Map<String, String> urlHashCodeUrlStringMap = new HashMap<String, String>();

	// map key = domain name
	// map value = URL hash set
	private static Map<String, Set<String>> domainNameUrlHashSetMap = new HashMap<String, Set<String>>();

	private static List<String> outputList = new ArrayList<String>();

	public AdHocCrawl() {
	}

	public static void main(String args[]) throws Exception {
		try {
			threadPool.init();
			CommonUtils.initThreads(18);
			new AdHocCrawl().process(args);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			threadPool.destroy();
		}
	}

	private void process(String[] args) throws Exception {

		List<String> domainNameList = new ArrayList<String>();
		String urlString = null;
		String urlHashCode = null;
		String domainName = null;
		URL url = null;
		Set<String> urlHashCodeSet = null;
		int index = 0;

		// runtime parameter 1: input file location path
		String inputFileLocationPath = args[0];
		FormatUtils.getInstance().logMemoryUsage("process() inputFileLocationPath=" + inputFileLocationPath);

		// runtime parameter 2: output file location path
		String outputFileLocationPath = args[1];
		FormatUtils.getInstance().logMemoryUsage("process() outputFileLocationPath=" + outputFileLocationPath);

		getOutputList().add(getHeading());

		File inputFile = new File(inputFileLocationPath);
		List<String> inputList = FileUtils.readLines(inputFile, IConstants.UTF_8);
		if (inputList != null && inputList.size() > 0) {
			nextInputString: for (String inputString : inputList) {
				index++;

				if (StringUtils.isBlank(inputString)) {
					//FormatUtils.getInstance().logMemoryUsage("process() error--skip www.google.com URL=" + urlString);
					continue nextInputString;
				}

				urlString = StringUtils.removeEnd(StringUtils.removeStart(StringUtils.trim(inputString), IConstants.ONE_DOUBLE_QUOTES), IConstants.ONE_DOUBLE_QUOTES);
				try {
					url = new URL(urlString);
					domainName = url.getHost();
					if (StringUtils.endsWithIgnoreCase(domainName, ".google.com")) {
						continue nextInputString;
					}
				} catch (Exception e) {
					FormatUtils.getInstance().logMemoryUsage("process() error--domain name cannot be determined for index=" + index + " ,urlString=" + urlString
							+ ",exception message=" + e.getMessage());
					e.printStackTrace();
					continue nextInputString;
				}
				urlHashCode = Md5Util.Md5(urlString);
				urlHashCodeUrlStringMap.put(urlHashCode, urlString);

				if (domainNameUrlHashSetMap.containsKey(domainName)) {
					urlHashCodeSet = domainNameUrlHashSetMap.get(domainName);
				} else {
					urlHashCodeSet = new HashSet<String>();
				}
				urlHashCodeSet.add(urlHashCode);
				domainNameUrlHashSetMap.put(domainName, urlHashCodeSet);
			}
		}

		if (domainNameUrlHashSetMap != null && domainNameUrlHashSetMap.size() > 0) {
			domainNameList = new ArrayList<String>(domainNameUrlHashSetMap.keySet());
		}

		CrawlerUtils.getInstance();

		processDomainsConcurrently(domainNameList);

		List<String> outputList = getOutputList();
		//Collections.sort(outputList);
		File outputFile = new File(outputFileLocationPath);
		FileUtils.writeLines(outputFile, IConstants.UTF_8, outputList);

	}

	private String getHeading() {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("URL");
		stringBuilder.append(IConstants.BACKTICK);
		stringBuilder.append("IN CLARITYDB?");
		stringBuilder.append(IConstants.BACKTICK);
		stringBuilder.append("HTTP STATUS");
		stringBuilder.append(IConstants.BACKTICK);
		stringBuilder.append("TITLE");
		return stringBuilder.toString();
	}

	private void processDomainsConcurrently(List<String> domainNameList) {
		FormatUtils.getInstance().logMemoryUsage("processDomainsConcurrently() begins. domainNameList.size()=" + domainNameList.size());
		long startTimestamp = System.currentTimeMillis();
		int totalNumberOfDomains = domainNameList.size();
		String ipAddress = null;
		String domainName = null;
		AdHocCrawlCommand adHocCrawlCommand = null;
		int numberOfDomainsProcessed = 0;
		do {
			ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
			if (ipAddress == null) {
				continue;
			}
			domainName = domainNameList.get(numberOfDomainsProcessed++);
			//FormatUtils.getInstance().logMemoryUsage("processDomainsConcurrently() processing domainName=" + domainName);
			adHocCrawlCommand = getAdHocCrawlCommand(ipAddress, domainName);
			try {
				threadPool.execute(adHocCrawlCommand);
			} catch (Exception e) {
				e.printStackTrace();
			}

			if (isDebug == true) {
				if (numberOfDomainsProcessed >= 168) {
					break;
				}
			}

		} while (numberOfDomainsProcessed < totalNumberOfDomains);

		do {
			try {
				Thread.sleep(1000);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		} while (threadPool.getThreadPool().getActiveCount() > 0);
		FormatUtils.getInstance().logMemoryUsage("processDomainsConcurrently() end. elapsed time in seconds=" + ((System.currentTimeMillis() - startTimestamp) / 1000));
	}

	private AdHocCrawlCommand getAdHocCrawlCommand(String ip, String domainName) {
		AdHocCrawlCommand adHocCrawlCommand = new AdHocCrawlCommand(ip, domainName);
		adHocCrawlCommand.setStatus(true);
		return adHocCrawlCommand;
	}

	protected synchronized static List<String> getOutputList() {
		return outputList;
	}

	protected static Map<String, Set<String>> getDomainNameUrlHashSetMap() {
		return domainNameUrlHashSetMap;
	}

	protected static Map<String, String> getUrlHashCodeUrlStringMap() {
		return urlHashCodeUrlStringMap;
	}
}
