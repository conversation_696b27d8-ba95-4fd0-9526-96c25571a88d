package com.actonia.polite.crawl;

import com.actonia.IConstants;
import com.actonia.dao.OwnDomainEntityDAO;
import com.actonia.dao.TargetUrlEntityDAO;
import com.actonia.dao.TargetUrlHtmlClickHouseDAO;
import com.actonia.dao.UrlLastChangedTimestampDAO;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.entity.TargetUrlEntity;
import com.actonia.entity.UrlLastChangedTimestampEntity;
import com.actonia.utils.SpringBeanFactory;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.logging.log4j.LogManager;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class UpdateUrlLastChangedTimestamp {

    private static final org.apache.logging.log4j.Logger log = LogManager.getLogger(UpdateUrlLastChangedTimestamp.class);

    private final OwnDomainEntityDAO ownDomainEntityDAO;
    private final TargetUrlEntityDAO targetUrlEntityDAO;
    private final Date trackDate;
    private UrlLastChangedTimestampDAO urlLastChangedTimestampDAO;
    private TargetUrlHtmlClickHouseDAO targetUrlHtmlClickHouseDAO;

    public UpdateUrlLastChangedTimestamp(OwnDomainEntityDAO ownDomainEntityDAO, TargetUrlEntityDAO targetUrlEntityDAO, Date trackDate) {
        this.ownDomainEntityDAO = ownDomainEntityDAO;
        this.targetUrlEntityDAO = targetUrlEntityDAO;
        this.trackDate = trackDate;
        log.info("UpdateUrlLastChangedTimestamp constructor. trackDate: {}", trackDate);
        int retry = 0;
        while (retry < 3) {
            try {
                targetUrlHtmlClickHouseDAO = TargetUrlHtmlClickHouseDAO.getInstance();
                urlLastChangedTimestampDAO = UrlLastChangedTimestampDAO.getInstance();
                break;
            } catch (Exception e) {
                retry++;
                log.error("UpdateUrlLastChangedTimestamp constructor. retry: {}", retry, e);
            }
        }
    }

    public static void main(String[] args) throws Exception {
        Date trackDate = new Date();
        if (args.length > 0) {
            if (StringUtils.isNotBlank(args[0])) {
                trackDate = DateUtils.parseDate(args[0], new String[]{IConstants.DATE_FORMAT_YYYY_MM_DD});
            }
        }
        OwnDomainEntityDAO ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        TargetUrlEntityDAO targetUrlEntityDAO = SpringBeanFactory.getBean("targetUrlEntityDAO");
        UpdateUrlLastChangedTimestamp updateUrlLastChangedTimestamp = new UpdateUrlLastChangedTimestamp(ownDomainEntityDAO, targetUrlEntityDAO, trackDate);
        updateUrlLastChangedTimestamp.process(args);
    }

    private List<String> getFieldNames() {
        List<String> fieldNames = new ArrayList<>();
        fieldNames.add("url");
        fieldNames.add("url_hash");
        fieldNames.add("url_murmur_hash");
        fieldNames.add("crawl_timestamp");
        return fieldNames;
    }

    private void process(String[] args) throws Exception {

        final List<OwnDomainEntity> domainsWithTargetUrls = ownDomainEntityDAO.findDomainsWithTargetUrl();
        log.info("Got domains with target urls: {}", domainsWithTargetUrls.size());

        List<String> targetUrlHtmlFieldNames = getFieldNames();
        log.info("Got historical html field names: {}", targetUrlHtmlFieldNames.size());

        for (OwnDomainEntity ownDomainEntity : domainsWithTargetUrls) {
            final Integer domainId = ownDomainEntity.getId();
            log.info("Processing domainId: {}", domainId);

            final List<TargetUrlEntity> targetUrlEntityList = targetUrlEntityDAO.getTargetUrlListWithoutDisableCrawl(domainId);
            if (targetUrlEntityList == null || targetUrlEntityList.isEmpty()) {
                log.warn("No target urls found for domainId = {}", domainId);
                continue;
            }
            log.info("Got target urls {} for domainId: {}", targetUrlEntityList.size(), domainId);
            final Map<String, TargetUrlEntity> managedUrlMurmur3HashMap = targetUrlEntityList.stream()
                    .collect(Collectors.toMap(TargetUrlEntity::getUrlMurmur3Hash, targetUrlEntity -> targetUrlEntity, (t, t2) -> t));
            final List<HtmlClickHouseEntity> latestFromHistorical = targetUrlHtmlClickHouseDAO.getLatestFromHistorical(domainId, targetUrlHtmlFieldNames, null);
            final List<UrlLastChangedTimestampEntity> urlLastChangedTimestampEntities = latestFromHistorical.parallelStream().map(htmlClickHouseEntity -> {
                final String urlMurmurHash = htmlClickHouseEntity.getUrlMurmurHash();
                final int urlType = managedUrlMurmur3HashMap.containsKey(urlMurmurHash) ? 1 : 2;
                UrlLastChangedTimestampEntity urlLastChangedTimestampEntity = UrlLastChangedTimestampEntity.fromHtmlClickHouseEntity(htmlClickHouseEntity);
                urlLastChangedTimestampEntity.setDomainId(domainId);
                urlLastChangedTimestampEntity.setTrackDate(this.trackDate);
                urlLastChangedTimestampEntity.setUrlType(urlType);
                return urlLastChangedTimestampEntity;
            }).collect(Collectors.toList());
            log.info("Collected {} UrlLastChangedTimestampEntity list for domainId: {}", urlLastChangedTimestampEntities.size(), domainId);


            urlLastChangedTimestampDAO.createBatch(urlLastChangedTimestampEntities, null);
            log.info("Created batch for domainId: {}", domainId);
        }

        log.info("Finished process");
    }
}
