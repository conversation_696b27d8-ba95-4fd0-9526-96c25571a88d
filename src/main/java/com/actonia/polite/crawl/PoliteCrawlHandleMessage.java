package com.actonia.polite.crawl;

import com.actonia.IConstants;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.utils.CommonUtils;
import com.actonia.value.object.ScrapyCrawlerResponse;

import java.util.Date;

public class PoliteCrawlHandleMessage {


    private HtmlClickHouseEntity createFromResponse(ScrapyCrawlerResponse response) {
        final int status = response.getStatus();
        final String url = response.getUrl();
        return null;
    }
    
    private HtmlClickHouseEntity createDefaultEntity(String url, Date trackDate) {
        final HtmlClickHouseEntity htmlClickHouseEntity = new HtmlClickHouseEntity();
        htmlClickHouseEntity.setTrackDate(trackDate);
        htmlClickHouseEntity.setUrl(url);
        htmlClickHouseEntity.setSign(IConstants.CLICKHOUSE_SIGN_POSITIVE_1);
        htmlClickHouseEntity.setWeekOfYear(CommonUtils.calculateWeekOfYear(trackDate));
        return htmlClickHouseEntity;
    }
    
}
