package com.actonia.polite.crawl;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.concurrency.ThreadPoolService;
import com.actonia.dao.KeywordEntityDAO;
import com.actonia.dao.OwnDomainEntityDAO;
import com.actonia.dao.TargetUrlHtmlClickHouseDAO;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.KeywordEntity;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.near.duplication.detection.BlockPermutedSimhash;
import com.actonia.utils.CommonUtils;
import com.actonia.utils.CrawlerUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.PutMessageUtils;
import com.actonia.utils.RankCheckUtils;
import com.actonia.utils.SpringBeanFactory;

public class NearDuplicateDetection {

	private static ThreadPoolService threadPoolService = ThreadPoolService.getInstance();

	//private Boolean isDebug = false;
	private OwnDomainEntityDAO ownDomainEntityDAO;
	private KeywordEntityDAO keywordEntityDAO;

	public NearDuplicateDetection() {
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
	}

	public static void main(String args[]) {
		FormatUtils.getInstance().logMemoryUsage("main() begins.");
		long startTimestamp = System.currentTimeMillis();
		try {
			new NearDuplicateDetection().process(args);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
		}
		FormatUtils.getInstance().logMemoryUsage("main() ends. elapsed time in sec.:" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private void process(String args[]) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("process() begins.");
		String domainIdsString = null;
		long startTimestamp = System.currentTimeMillis();
		Boolean isExecDomainIdsInd = null;
		Set<Integer> runtimeDomainSet = null;
		Properties domainProperties = new Properties();
		String execDomainIds = null;
		String notExecDomainIds = null;
		List<Integer> skipDomainIdList = null;
		List<Integer> testDomainIdList = null;
		StringBuilder stringBuilder = null;
		List<OwnDomainEntity> allOwnDomainEntityList = null;
		List<OwnDomainEntity> filteredOwnDomainEntityList = null;
		String testString = null;
		int numberOfThreads = 0;
		Date todayDate = null;
		Date twoDaysAgoDate = null;
		Date rankDate = null;

		try {

			if (args == null || args.length == 0) {
				FormatUtils.getInstance().logMemoryUsage("process() runtime parameter 1 number of concurrent threads is required.");
				return;
			}

			TargetUrlHtmlClickHouseDAO.getInstance();
			PutMessageUtils.getInstance();

			// runtime parameter 1 (required): number of concurrent threads
			if (args.length >= 1) {
				testString = args[0];
				numberOfThreads = NumberUtils.toInt(testString);
				if (numberOfThreads > 0) {
					threadPoolService.init();
					CommonUtils.initThreads(numberOfThreads);
				} else {
					FormatUtils.getInstance().logMemoryUsage("process() runtime parameter 1 number of concurrent threads is invalid.=" + testString);
					return;
				}
			}
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameter 1:numberOfThreads=" + numberOfThreads);

			// runtime parameter 2 (optional): list of domain IDs
			if (args.length >= 2) {
				domainIdsString = args[1];
			}
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameter 2:list of domain IDs=" + domainIdsString);

			try {
				domainProperties.load(NearDuplicateDetection.class.getResourceAsStream("/domain.properties"));
			} catch (Exception e) {
				FormatUtils.getInstance().logMemoryUsage("process() no domain.properties file found");
				return;
			}

			if (StringUtils.isNotBlank(domainIdsString)) {
				execDomainIds = domainIdsString;
				notExecDomainIds = null;
			} else {
				execDomainIds = domainProperties.getProperty("exec.domain");
				notExecDomainIds = domainProperties.getProperty("notexec.domain");
				skipDomainIdList = new ArrayList<Integer>();
				if (StringUtils.isNotBlank(notExecDomainIds)) {
					runtimeDomainSet = CommonUtils.getDomainSet(notExecDomainIds);
					for (Integer notExecDomainId : runtimeDomainSet) {
						skipDomainIdList.add(notExecDomainId);
					}
				}
				// skip 'Expedia'
				testDomainIdList = ownDomainEntityDAO.getDomainIdsByCompanyName(IConstants.COMPANY_NAME_EXPEDIA);
				if (testDomainIdList != null && testDomainIdList.size() > 0) {
					skipDomainIdList.addAll(testDomainIdList);
				}
				// skip 'CarRentals.com'
				testDomainIdList = ownDomainEntityDAO.getDomainIdsByCompanyName(IConstants.COMPANY_NAME_CARRENTALS_COM);
				if (testDomainIdList != null && testDomainIdList.size() > 0) {
					skipDomainIdList.addAll(testDomainIdList);
				}
				// skip 'Coupon Cabin'
				testDomainIdList = ownDomainEntityDAO.getDomainIdsByCompanyName(IConstants.COMPANY_NAME_COUPON_CABIN);
				if (testDomainIdList != null && testDomainIdList.size() > 0) {
					skipDomainIdList.addAll(testDomainIdList);
				}
				// skip 'RetailMeNot.com'
				testDomainIdList = ownDomainEntityDAO.getDomainIdsByCompanyName(IConstants.COMPANY_NAME_RETAIL_ME_NOT_COM);
				if (testDomainIdList != null && testDomainIdList.size() > 0) {
					skipDomainIdList.addAll(testDomainIdList);
				}
				stringBuilder = null;
				if (skipDomainIdList != null && skipDomainIdList.size() > 0) {
					for (Integer skipDomainId : skipDomainIdList) {
						if (stringBuilder == null) {
							stringBuilder = new StringBuilder();
						} else {
							stringBuilder.append(IConstants.COMMA);
						}
						stringBuilder.append(skipDomainId);
					}
					notExecDomainIds = stringBuilder.toString();
				}
			}
			FormatUtils.getInstance().logMemoryUsage("process() execDomainIds=" + execDomainIds);
			FormatUtils.getInstance().logMemoryUsage("process() notExecDomainIds=" + notExecDomainIds);

			if (StringUtils.isBlank(domainIdsString)) {
				allOwnDomainEntityList = ownDomainEntityDAO.queryForAll();
			} else {
				allOwnDomainEntityList = new ArrayList<OwnDomainEntity>();
				allOwnDomainEntityList.add(ownDomainEntityDAO.getOwnDomainEntityById(NumberUtils.toInt(domainIdsString)));
			}
			if (allOwnDomainEntityList == null || allOwnDomainEntityList.size() == 0) {
				FormatUtils.getInstance().logMemoryUsage("process() allOwnDomainEntityList is empty.");
				return;
			}

			// process specific domains
			if (StringUtils.isNotBlank(execDomainIds)) {
				runtimeDomainSet = CommonUtils.getDomainSet(execDomainIds);
				isExecDomainIdsInd = true;
			}
			// do not process specific domains
			else if (StringUtils.isNotBlank(notExecDomainIds)) {
				runtimeDomainSet = CommonUtils.getDomainSet(notExecDomainIds);
				isExecDomainIdsInd = false;
			}
			// process all domains
			else {
				runtimeDomainSet = null;
				isExecDomainIdsInd = null;
			}

			filteredOwnDomainEntityList = PutMessageUtils.getInstance().filterDomains(allOwnDomainEntityList, isExecDomainIdsInd, runtimeDomainSet);
			if (filteredOwnDomainEntityList == null || filteredOwnDomainEntityList.size() == 0) {
				FormatUtils.getInstance().logMemoryUsage("process() filteredOwnDomainEntityList is empty.");
				return;
			}

			todayDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
			FormatUtils.getInstance().logMemoryUsage("process() today's date=" + DateFormatUtils.format(todayDate, IConstants.DATE_FORMAT_YYYY_MM_DD));
			twoDaysAgoDate = DateUtils.addDays(todayDate, -2);
			FormatUtils.getInstance().logMemoryUsage("process() two days ago's date=" + DateFormatUtils.format(twoDaysAgoDate, IConstants.DATE_FORMAT_YYYY_MM_DD));
			rankDate = twoDaysAgoDate;

			for (OwnDomainEntity ownDomainEntity : filteredOwnDomainEntityList) {
				processOneDomain(ownDomainEntity, rankDate);
			}

		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			threadPoolService.destroy();
			FormatUtils.getInstance().logMemoryUsage("process() ends. elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		}
	}

	private void processOneDomain(OwnDomainEntity ownDomainEntity, Date rankDate) throws Exception {

		int domainId = ownDomainEntity.getId();
		String domainName = ownDomainEntity.getDomain();
		FormatUtils.getInstance().logMemoryUsage("processOneDomain() begins. domainId=" + domainId + ",domainName=" + domainName);

		int topRankedPositions = 5;

		List<String> competitorUrlList = getCompetitorUrlList(ownDomainEntity, rankDate, topRankedPositions);
		if (competitorUrlList != null && competitorUrlList.size() > 0) {
			processCompetitorUrlConcurrently(domainId, competitorUrlList);
		}

		// title
		processDataField(domainId, domainName, IConstants.TITLE);

		// meta description
		processDataField(domainId, domainName, IConstants.DESCRIPTION);

		// h1
		processDataField(domainId, domainName, IConstants.H1);

		// h2
		processDataField(domainId, domainName, IConstants.H2);
		FormatUtils.getInstance().logMemoryUsage("processOneDomain() ends. domainId=" + domainId + ",domainName=" + domainName);

	}

	private List<String> getCompetitorUrlList(OwnDomainEntity ownDomainEntity, Date rankDate, int topRankedPositions) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		int domainId = ownDomainEntity.getId();
		String domainName = ownDomainEntity.getDomain();
		FormatUtils.getInstance().logMemoryUsage("getCompetitorUrlList() begins. domainId=" + domainId + ",domainName=" + domainName);
		Set<String> competitorUrlSet = new HashSet<String>();
		List<String> testCompetitorUrlList = null;
		int keywordProcessed = 0;
		int modulus = 0;
		String keywordName = null;
		List<KeywordEntity> keywordEntityList = keywordEntityDAO.getIdLowercaseName(domainId, KeywordEntity.RANK_CHECK_ACTIVE);
		if (keywordEntityList == null || keywordEntityList.size() == 0) {
			FormatUtils.getInstance().logMemoryUsage("getCompetitorUrlList() domainId=" + domainId + ",keywordEntityList is empty.");
			return null;
		}
		FormatUtils.getInstance().logMemoryUsage("getCompetitorUrlList() domainId=" + domainId + ",keywordEntityList.size()=" + keywordEntityList.size());

		Map<String, List<String>> keywordCompetitorUrlListMap = RankCheckUtils.getRankedKeywordCompetitorUrlListMap(null, ownDomainEntity, rankDate,
				topRankedPositions);
		if (keywordCompetitorUrlListMap == null || keywordCompetitorUrlListMap.size() == 0) {
			FormatUtils.getInstance().logMemoryUsage("getCompetitorUrlList() domainId=" + domainId + ",keywordCompetitorUrlListMap is empty.");
			return null;
		}
		FormatUtils.getInstance()
				.logMemoryUsage("getCompetitorUrlList() domainId=" + domainId + ",keywordCompetitorUrlListMap.size()=" + keywordCompetitorUrlListMap.size());

		// retrieve top ranking pages of each keyword
		for (KeywordEntity keywordEntity : keywordEntityList) {
			keywordProcessed++;
			modulus = keywordProcessed % 1000;
			if (modulus == 0) {
				FormatUtils.getInstance().logMemoryUsage("getCompetitorUrlList() domainId=" + domainId + ",keywordProcessed=" + keywordProcessed);
			}
			keywordName = keywordEntity.getKeywordName();
			testCompetitorUrlList = RankCheckUtils.getTopRankedCompetitorUrls(null, keywordName, keywordCompetitorUrlListMap);
			if (testCompetitorUrlList != null && testCompetitorUrlList.size() > 0) {
				for (String competitorUrlString : testCompetitorUrlList) {
					competitorUrlSet.add(competitorUrlString);
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("getCompetitorUrlList() ends. domainId=" + domainId + ",domainName=" + domainName + ",competitorUrlSet.size()="
				+ competitorUrlSet.size() + "elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return new ArrayList<String>(competitorUrlSet);
	}

	private void processCompetitorUrlConcurrently(int domainId, List<String> competitorUrlList) throws Exception {
		FormatUtils.getInstance()
				.logMemoryUsage("processCompetitorUrlConcurrently() begins. domainId=" + domainId + ",competitorUrlList.size()=" + competitorUrlList.size());

		int totalNumberOfCompetitorUrls = competitorUrlList.size();
		long startTimestamp = System.currentTimeMillis();
		String ip = null;
		String competitorUrlString = null;
		NearDuplicateDetectionCommand nearDuplicateDetectionCommand = null;
		int numberOfCompetitorUrlsProcessed = 0;

		do {
			ip = CacheModleFactory.getInstance().getAliveIpAddress();
			if (ip == null) {
				continue;
			}
			competitorUrlString = competitorUrlList.get(numberOfCompetitorUrlsProcessed++);
			nearDuplicateDetectionCommand = getNearDuplicateDetectionCommand(ip, domainId, competitorUrlString);
			try {
				threadPoolService.execute(nearDuplicateDetectionCommand);
			} catch (Exception e) {
				e.printStackTrace();
			}
		} while (numberOfCompetitorUrlsProcessed < totalNumberOfCompetitorUrls);

		do {
			try {
				Thread.sleep(1000);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		} while (threadPoolService.getThreadPool().getActiveCount() > 0);

		FormatUtils.getInstance().logMemoryUsage("processCompetitorUrlConcurrently() ends. domainId=" + domainId + ",competitorUrlList.size()="
				+ competitorUrlList.size() + ",elapsed(s)=" + ((System.currentTimeMillis() - startTimestamp) / 1000));
	}

	private NearDuplicateDetectionCommand getNearDuplicateDetectionCommand(String ip, int domainId, String competitorUrlString) {
		NearDuplicateDetectionCommand nearDuplicateDetectionCommand = new NearDuplicateDetectionCommand(ip, domainId, competitorUrlString);
		nearDuplicateDetectionCommand.setStatus(true);
		return nearDuplicateDetectionCommand;
	}

	private void processDataField(int domainId, String domainName, String dataField) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("processDataField() begins,domainId=" + domainId + ",domainName=" + domainName + ",dataField=" + dataField);
		long startTimestamp = System.currentTimeMillis();
		//FormatUtils.getInstance().logMemoryUsage("processDataField() ip=" + ip + " begins. domainId=" + domainId);
		Map<String, String> keyValueMap = null;
		BlockPermutedSimhash blockPermutedSimhash = null;
		StringBuilder stringBuilder = null;
		String urlString = null;

		List<String> databaseFields = new ArrayList<String>();
		databaseFields.add(IConstants.URL);
		databaseFields.add(dataField);
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = TargetUrlHtmlClickHouseDAO.getInstance().getLatestFromHistorical(domainId, databaseFields,
				IConstants.CLICKHOUSE_TABLE_NAME_NULL);
		if (htmlClickHouseEntityList != null && htmlClickHouseEntityList.size() > 0) {
			FormatUtils.getInstance().logMemoryUsage(
					"processDataField() domainId=" + domainId + ",domainName=" + domainName + ",htmlClickHouseEntityList.size()=" + htmlClickHouseEntityList.size());
			keyValueMap = new HashMap<String, String>();
			for (HtmlClickHouseEntity htmlClickHouseEntity : htmlClickHouseEntityList) {
				urlString = FormatUtils.getInstance().trimText(htmlClickHouseEntity.getUrl());
				if (StringUtils.equalsIgnoreCase(dataField, IConstants.TITLE) && StringUtils.isNotBlank(htmlClickHouseEntity.getCrawlerResponse().getTitle())) {
					keyValueMap.put(urlString, FormatUtils.getInstance().trimText(htmlClickHouseEntity.getCrawlerResponse().getTitle()));
				} else if (StringUtils.equalsIgnoreCase(dataField, IConstants.DESCRIPTION)
						&& StringUtils.isNotBlank(htmlClickHouseEntity.getCrawlerResponse().getDescription())) {
					keyValueMap.put(urlString, FormatUtils.getInstance().trimText(htmlClickHouseEntity.getCrawlerResponse().getDescription()));
				} else if (StringUtils.equalsIgnoreCase(dataField, IConstants.H1) && htmlClickHouseEntity.getCrawlerResponse().getH1() != null
						&& htmlClickHouseEntity.getCrawlerResponse().getH1().length > 0) {
					stringBuilder = new StringBuilder();
					for (String h1String : htmlClickHouseEntity.getCrawlerResponse().getH1()) {
						stringBuilder.append(h1String).append(IConstants.ONE_SPACE);
					}
					keyValueMap.put(urlString, FormatUtils.getInstance().trimText(stringBuilder.toString()));
				} else if (StringUtils.equalsIgnoreCase(dataField, IConstants.H2) && htmlClickHouseEntity.getCrawlerResponse().getH2() != null
						&& htmlClickHouseEntity.getCrawlerResponse().getH2().length > 0) {
					stringBuilder = new StringBuilder();
					for (String h2String : htmlClickHouseEntity.getCrawlerResponse().getH2()) {
						stringBuilder.append(h2String).append(IConstants.ONE_SPACE);
					}
					keyValueMap.put(urlString, FormatUtils.getInstance().trimText(stringBuilder.toString()));
				}
			}
			blockPermutedSimhash = new BlockPermutedSimhash(dataField, keyValueMap);
			blockPermutedSimhash.blockPermutation();
		}
		FormatUtils.getInstance().logMemoryUsage("processDataField() ends,domainId=" + domainId + ",domainName=" + domainName + ",dataField=" + dataField
				+ ",htmlClickHouseEntityList.size()=" + htmlClickHouseEntityList.size() + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}
}
