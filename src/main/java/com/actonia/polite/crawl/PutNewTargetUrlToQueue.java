package com.actonia.polite.crawl;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.actonia.dao.*;
import com.actonia.entity.*;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.service.AgencyInfoService;
import com.actonia.utils.CommonUtils;
import com.actonia.utils.EmailSenderComponent;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.PutMessageUtils;
import com.actonia.utils.SQSUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.PutNewTargetUrlToQueueReportComparator;
import com.actonia.value.object.PutNewTargetUrlToQueueReportValueObject;
import com.actonia.value.object.UrlCrawlParametersVO;
import com.amazonaws.services.sqs.model.BatchRequestTooLongException;
import com.google.gson.Gson;

/***
 * 
 * put new target URLs to daily crawl queues
 *
 */
public class PutNewTargetUrlToQueue {
	private final Date todayDate;

	//private Boolean isDebug = false;

	private OwnDomainEntityDAO ownDomainEntityDAO;

	private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;

	private final OwnDomainTrackingDAO ownDomainTrackingDAO;

	private TargetUrlDailyCrawlTrackingEntityDAO targetUrlDailyCrawlTrackingEntityDAO;

	private TargetUrlEntityDAO targetUrlEntityDAO;

	private AgencyInfoService agencyInfoService;

	private EmailSenderComponent emailSenderComponent;

	private static final Integer MESSAGE_BATCH_SIZE = 10;

	private static final int MAXIMUM_RETRY_COUNT = 5;

	// when SQS returns error, sleep for one minute
	private static final int SQS_RETRY_SLEEP_TIME = 60000;

	private static final int MAX_URLS_WITHOUT_CRAWL_DATA = 1680;

	public PutNewTargetUrlToQueue() {
		this.ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		this.ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
		this.targetUrlDailyCrawlTrackingEntityDAO = SpringBeanFactory.getBean("targetUrlDailyCrawlTrackingEntityDAO");
		this.targetUrlEntityDAO = SpringBeanFactory.getBean("targetUrlEntityDAO");
		this.agencyInfoService = SpringBeanFactory.getBean("agencyInfoService");
		this.emailSenderComponent = SpringBeanFactory.getBean("emailSenderComponent");
		todayDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
        ownDomainTrackingDAO = SpringBeanFactory.getBean("ownDomainTrackingDAO");
    }

	public static void main(String args[]) {
		FormatUtils.getInstance().logMemoryUsage("main() begins.");
		long startTimestamp = System.currentTimeMillis();
		try {
			new PutNewTargetUrlToQueue().process(args);
		} catch (Exception e) {
			e.printStackTrace();
		}
        FormatUtils.getInstance().logMemoryUsage("main() ends. elapsed time in sec.:" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private void process(String args[]) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("process() begins.");
		long totalStarttimestamp = System.currentTimeMillis();
		int domainId = 0;
		String domainName = null;
		String domainIdsString = null;
		Date targetUrlHtmlDailyDate = null;
		List<String> urlList = null;
		String tableName = null;
		TargetUrlDailyCrawlTrackingEntity targetUrlDailyCrawlTrackingEntity = null;
		String domainIdString = null;
		String languageCode = null;
		String domainSpecificQueueName = null;
		Integer[] numberOfMessagesArray = null;
		List<PutNewTargetUrlToQueueReportValueObject> putNewTargetUrlToQueueReportValueObjectList = new ArrayList<PutNewTargetUrlToQueueReportValueObject>();
		PutNewTargetUrlToQueueReportValueObject putNewTargetUrlToQueueReportValueObject = null;
		String notes = null;
		Date crawlQueueLastUpdateDate = null;
		long millsecondsSinceCrawlQueueLastUpdated = 0L;
		long daysSinceCrawlQueueLastUpdated = 0L;
		int totalUrlsWithoutCrawlData = 0;
		List<OwnDomainEntity> domainsToBeProcessedList = new ArrayList<OwnDomainEntity>();

		// runtime parameter (optional): list of domain IDs)
		if (args != null && args.length >= 1) {
			domainIdsString = args[0];
		}
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameter:list of domain IDs=" + domainIdsString);

		Boolean isExecDomainIdsInd = null;
		Set<Integer> runtimeDomainSet = null;
		StringBuilder stringBuilder = null;

		Properties domainProperties = new Properties();
		try {
			domainProperties.load(PutNewTargetUrlToQueue.class.getResourceAsStream("/domain.properties"));
		} catch (Exception e) {
			FormatUtils.getInstance().logMemoryUsage("process() no domain.properties file found");
			return;
		}

		List<Integer> skipDomainIdList = null;
		List<Integer> testDomainIdList = null;
		String execDomainIds = domainProperties.getProperty("exec.domain");
		String notExecDomainIds = domainProperties.getProperty("notexec.domain");
		if (StringUtils.isNotBlank(domainIdsString)) {
			execDomainIds = domainIdsString;
			notExecDomainIds = null;
		} else {
			skipDomainIdList = new ArrayList<Integer>();
			if (StringUtils.isNotBlank(notExecDomainIds)) {
				runtimeDomainSet = CommonUtils.getDomainSet(notExecDomainIds);
				for (Integer notExecDomainId : runtimeDomainSet) {
					skipDomainIdList.add(notExecDomainId);
				}
			}
			testDomainIdList = ownDomainEntityDAO.getDomainIdsByCompanyName(IConstants.COMPANY_NAME_EXPEDIA);
			if (testDomainIdList != null && testDomainIdList.size() > 0) {
				skipDomainIdList.addAll(testDomainIdList);
			}
			testDomainIdList = ownDomainEntityDAO.getDomainIdsByCompanyName(IConstants.COMPANY_NAME_CARRENTALS_COM);
			if (testDomainIdList != null && testDomainIdList.size() > 0) {
				skipDomainIdList.addAll(testDomainIdList);
			}
			stringBuilder = null;
			if (skipDomainIdList != null && skipDomainIdList.size() > 0) {
				for (Integer skipDomainId : skipDomainIdList) {
					if (stringBuilder == null) {
						stringBuilder = new StringBuilder();
					} else {
						stringBuilder.append(IConstants.COMMA);
					}
					stringBuilder.append(skipDomainId);
				}
				notExecDomainIds = stringBuilder.toString();
			}
		}
		FormatUtils.getInstance().logMemoryUsage("process() execDomainIds=" + execDomainIds);
		FormatUtils.getInstance().logMemoryUsage("process() notExecDomainIds=" + notExecDomainIds);

		List<OwnDomainEntity> allOwnDomainEntityList = ownDomainEntityDAO.queryForAll();
		if (allOwnDomainEntityList == null || allOwnDomainEntityList.size() == 0) {
			FormatUtils.getInstance().logMemoryUsage("process() allOwnDomainEntityList is empty.");
			return;
		}

		// process specific domains
		if (StringUtils.isNotBlank(execDomainIds)) {
			runtimeDomainSet = CommonUtils.getDomainSet(execDomainIds);
			isExecDomainIdsInd = true;
		}
		// do not process specific domains
		else if (StringUtils.isNotBlank(notExecDomainIds)) {
			runtimeDomainSet = CommonUtils.getDomainSet(notExecDomainIds);
			isExecDomainIdsInd = false;
		}
		// process all domains
		else {
			runtimeDomainSet = null;
			isExecDomainIdsInd = null;
		}

		List<OwnDomainEntity> filteredOwnDomainEntityList = PutMessageUtils.getInstance().filterDomains(allOwnDomainEntityList, isExecDomainIdsInd, runtimeDomainSet);
		if (filteredOwnDomainEntityList == null || filteredOwnDomainEntityList.size() == 0) {
			FormatUtils.getInstance().logMemoryUsage("process() filteredOwnDomainEntityList is empty.");
			return;
		}

		List<Integer> domainIdsWithTargetUrls = ownDomainEntityDAO.getDomainIdsWithTargetUrls();

		// initialize the cached DAOs
		TargetUrlClickHouseDAO.getInstance();
		SQSUtils.getInstance();

		FormatUtils.getInstance().logMemoryUsage("process() todayDate=" + DateFormatUtils.format(todayDate, IConstants.DATE_FORMAT_YYYY_MM_DD));

		boolean isProcessDomain = false;

		// domain level processing
		nextOwnDomainEntity: for (OwnDomainEntity ownDomainEntity : filteredOwnDomainEntityList) {
			domainId = ownDomainEntity.getId();
			isProcessDomain = false;
			if (domainIdsWithTargetUrls != null && domainIdsWithTargetUrls.size() > 0) {
                for (Integer domainIdWithTargetUrls : domainIdsWithTargetUrls) {
                    if (domainId == domainIdWithTargetUrls) {
                        isProcessDomain = true;
                        break;
                    }
                }
			}
			if (isProcessDomain == false) {
				continue nextOwnDomainEntity;
			}

			domainName = ownDomainEntity.getDomain();
			final Integer targetUrlLatestDate = ownDomainTrackingDAO.getTargetUrlLatestDateOwnDomainIdByDomain(domainId);
			if (targetUrlLatestDate != null && targetUrlLatestDate > 0) {
				targetUrlHtmlDailyDate = DateUtils.parseDate(String.valueOf(targetUrlLatestDate), new String[]{IConstants.DATE_FORMAT_YYYYMMDD});
				urlList = TargetUrlClickHouseDAO.getInstance().getUrlsWithoutCrawlData(targetUrlHtmlDailyDate, domainId, tableName);
				if (urlList != null && urlList.size() > 0) {
					totalUrlsWithoutCrawlData = urlList.size();
					notes = null;
					putNewTargetUrlToQueueReportValueObject = new PutNewTargetUrlToQueueReportValueObject();
					putNewTargetUrlToQueueReportValueObject.setDomainId(domainId);
					putNewTargetUrlToQueueReportValueObject.setDomainName(domainName);
					targetUrlDailyCrawlTrackingEntity = targetUrlDailyCrawlTrackingEntityDAO.get(IConstants.CRAWL_TYPE_TARGET_URL_HTML, domainId);
					if (targetUrlDailyCrawlTrackingEntity != null) {
						domainIdString = String.valueOf(domainId);
						if (StringUtils.isNotBlank(ownDomainEntity.getLanguage())) {
							languageCode = ownDomainEntity.getLanguage().toUpperCase();
							domainSpecificQueueName = languageCode.concat(IConstants.UNDERSCORE).concat(domainIdString);
							domainSpecificQueueName = IConstants.QUEUE_NAME_TARGET_URL_DAILY_HTML_PREFIX.concat(domainSpecificQueueName);
							numberOfMessagesArray = SQSUtils.getInstance().getApproximateNumberOfMessagesAndInflight(domainSpecificQueueName);
							if (numberOfMessagesArray != null && numberOfMessagesArray.length == 2) {
								FormatUtils.getInstance()
										.logMemoryUsage("process() domainId=" + domainId + ",domainName=" + domainName + ",totalUrls="
												+ targetUrlDailyCrawlTrackingEntity.getTotalUrls() + ",crawlQueueLastUpdateDate="
												+ targetUrlDailyCrawlTrackingEntity.getTrackDate() + ",totalUrlsWithoutData=" + totalUrlsWithoutCrawlData
												+ ",messagesInQueue=" + numberOfMessagesArray[0] + ",messagesInFlight=" + numberOfMessagesArray[1]);
								putNewTargetUrlToQueueReportValueObject.setTotalUrls(targetUrlDailyCrawlTrackingEntity.getTotalUrls());
								putNewTargetUrlToQueueReportValueObject.setCrawlQueueLastUpdateDate(targetUrlDailyCrawlTrackingEntity.getTrackDate());
								putNewTargetUrlToQueueReportValueObject.setTotalUrlsWithoutData(totalUrlsWithoutCrawlData);
								putNewTargetUrlToQueueReportValueObject.setMessagesInQueue(numberOfMessagesArray[0]);
								putNewTargetUrlToQueueReportValueObject.setMessagesInFlight(numberOfMessagesArray[1]);

								// put new target URLs to queues when number of days since crawl queue last updated is >= 2 days
								crawlQueueLastUpdateDate = DateUtils.parseDate(String.valueOf(targetUrlDailyCrawlTrackingEntity.getTrackDate()),
										new String[]{IConstants.DATE_FORMAT_YYYYMMDD});
								millsecondsSinceCrawlQueueLastUpdated = Math.abs(todayDate.getTime() - crawlQueueLastUpdateDate.getTime());
								daysSinceCrawlQueueLastUpdated = TimeUnit.DAYS.convert(millsecondsSinceCrawlQueueLastUpdated, TimeUnit.MILLISECONDS);
								if (daysSinceCrawlQueueLastUpdated >= 2) {
									FormatUtils.getInstance().logMemoryUsage("process() domainId=" + domainId + ",domainName=" + domainName
											+ ",daysSinceCrawlQueueLastUpdated=" + daysSinceCrawlQueueLastUpdated + ",putting messages to queue...");
									domainsToBeProcessedList.add(ownDomainEntity);
									if (totalUrlsWithoutCrawlData <= MAX_URLS_WITHOUT_CRAWL_DATA) {
										putNewTargetUrlToQueueReportValueObject.setPutMessagesInd(true);
										createMessagesForCrawl(domainId, domainName, languageCode, urlList);
									} else {
										putNewTargetUrlToQueueReportValueObject.setPutMessagesInd(false);
									}
								}
							} else {
								notes = "numberOfMessagesArray is invalid.";
							}
						} else {
							notes = "ownDomainEntity.getLanguage() is null.";
						}
					} else {
						notes = "targetUrlDailyCrawlTrackingEntity is null.";
					}
					if (StringUtils.isNotBlank(notes)) {
						FormatUtils.getInstance().logMemoryUsage("process() domainId=" + domainId + ",domainName=" + domainName + "," + notes);
						putNewTargetUrlToQueueReportValueObject.setNotes(notes);
					}
					putNewTargetUrlToQueueReportValueObjectList.add(putNewTargetUrlToQueueReportValueObject);
					//if (putNewTargetUrlToQueueReportValueObjectList.size() > 2) {
					//	break;
					//}
				} else {
					//FormatUtils.getInstance().logMemoryUsage("process() domainId=" + domainId + ",domainName=" + domainName + ",urlList is empty.");
				}
			} else {
				//FormatUtils.getInstance().logMemoryUsage(
				//		"process() domainId=" + domainId + ",domainName=" + domainName + ",invalid targetUrlHtmlDailyDateNumber=" + targetUrlHtmlDailyDateNumber);
			}
		}

		if (domainsToBeProcessedList != null && domainsToBeProcessedList.size() > 0) {
			sendDomainCrawlDataToHtmlControllerQueue(domainsToBeProcessedList);
		}

		Collections.sort(putNewTargetUrlToQueueReportValueObjectList, new PutNewTargetUrlToQueueReportComparator());
		sendReport(putNewTargetUrlToQueueReportValueObjectList);

		FormatUtils.getInstance().logMemoryUsage("process() ends. elapsed(s.)=" + (System.currentTimeMillis() - totalStarttimestamp) / 1000);
	}

	private void createMessagesForCrawl(int domainId, String domainName, String languageCode, List<String> urlList) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("createMessagesForCrawl() begins. domainId=" + domainId + ",domainName=" + domainName + ",languageCode=" + languageCode
				+ ",urlList.size()=" + urlList.size());
		String htmlQueueUrl = null;
		UrlMetricsEntityV3 urlMetricsEntityV3 = null;
		String messageBody = null;
		int totalMessages = 0;
		int currentRetryCount = 0;
		String htmlQueueName = null;
		String htmlQueueNamePrefix = null;

		// put all target URL strings in queue
		Map<String, String> messages = new HashMap<>();

		Map<String, String> testMessages;
		String value = null;

		final HashSet<String> urls = new HashSet<>(urlList);

		if (!urls.isEmpty()) {
			htmlQueueNamePrefix = IConstants.QUEUE_NAME_NEW_URL_DAILY_HTML_PREFIX;
			htmlQueueName = htmlQueueNamePrefix + languageCode.toUpperCase() + IConstants.UNDERSCORE + domainId;
			htmlQueueUrl = SQSUtils.getInstance().createQueue(htmlQueueName);
			final Set<String> newInitialCrawlOnlyRSSUrls = this.targetUrlEntityDAO.getNewUrlsByDomainIdAndSourceType(domainId, todayDate);
			for (String url: urls) {
				urlMetricsEntityV3 = new UrlMetricsEntityV3();
                urlMetricsEntityV3.setUrl(url);
                urlMetricsEntityV3.setLanguageCode(languageCode);
				if (newInitialCrawlOnlyRSSUrls.contains(url)) {
					urlMetricsEntityV3.setSourceType(IConstants.TARGET_URL_SOURCE_TYPE_RSS);
					urlMetricsEntityV3.setInitialCrawlOnly(IConstants.TARGET_URL_INITIAL_CRAWL_ONLY_YES);
					FormatUtils.getInstance().logMemoryUsage("New initialCrawlOnly url from RSS" + ",OID= " + domainId + " ,url= " + url);
				}

                messageBody = new Gson().toJson(urlMetricsEntityV3);
                try {
                    messages.put(String.valueOf(System.nanoTime()), messageBody);
                    totalMessages = totalMessages + 1;
                    if (messages.size() == MESSAGE_BATCH_SIZE) {
                        // put messages to queue
                        currentRetryCount = 0;
                        while (currentRetryCount < MAXIMUM_RETRY_COUNT) {
                            try {
                                SQSUtils.getInstance().sendBatchMessageToQueueThrowsException(htmlQueueUrl, messages);
                                // sleep 100ms after each message
                                Thread.sleep(100);
	                            messages.clear();
                                break;
                            } catch (BatchRequestTooLongException e) {
                                for (String key : messages.keySet()) {
                                    testMessages = new HashMap<>();
                                    value = messages.get(key);
                                    testMessages.put(key, value);
                                    FormatUtils.getInstance().logMemoryUsage(
                                            "createMessagesForCrawl() queueUrl=" + htmlQueueUrl + ",BatchRequestTooLongException re-sending message=" + value);
                                    SQSUtils.getInstance().sendBatchMessageToQueueThrowsException(htmlQueueUrl, testMessages);
                                    FormatUtils.getInstance()
                                            .logMemoryUsage("createMessagesForCrawl() queueUrl=" + htmlQueueUrl + ",BatchRequestTooLongException resent message=" + value);
                                }
	                            messages.clear();
                                break;
                            } catch (Exception e) {
                                e.printStackTrace();
                                currentRetryCount++;
                                FormatUtils.getInstance()
                                        .logMemoryUsage("createMessagesForCrawl() domainId=" + domainId + ", domainName=" + domainName + ",htmlQueueName="
                                                + htmlQueueName + ", error for AmazonSQS, sleep and try again, currentRetryCount=" + currentRetryCount
                                                + ",current timestamp=" + new Date());
                                Thread.sleep(SQS_RETRY_SLEEP_TIME);
                            }
                        }

                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
			if (!messages.isEmpty()) {

				// put messages to queue
				currentRetryCount = 0;
                while (currentRetryCount < MAXIMUM_RETRY_COUNT) {
                    try {
                        SQSUtils.getInstance().sendBatchMessageToQueueThrowsException(htmlQueueUrl, messages);
	                    break;
                    } catch (BatchRequestTooLongException e) {
                        for (String key : messages.keySet()) {
                            testMessages = new HashMap<>();
                            value = messages.get(key);
                            testMessages.put(key, value);
                            FormatUtils.getInstance()
                                    .logMemoryUsage("createMessagesForCrawl() queueUrl=" + htmlQueueUrl + ",BatchRequestTooLongException re-sending message=" + value);
                            SQSUtils.getInstance().sendBatchMessageToQueueThrowsException(htmlQueueUrl, testMessages);
                            FormatUtils.getInstance()
                                    .logMemoryUsage("createMessagesForCrawl() queueUrl=" + htmlQueueUrl + ",BatchRequestTooLongException resent message=" + value);
                        }
	                    messages = new HashMap<String, String>();
                        currentRetryCount = MAXIMUM_RETRY_COUNT;
                        break;
                    } catch (Exception e) {
                        e.printStackTrace();
                        currentRetryCount++;
                        FormatUtils.getInstance()
                                .logMemoryUsage("createMessagesForCrawl() domainId=" + domainId + ", domainName=" + domainName + ",htmlQueueName=" + htmlQueueName
                                        + ", error for AmazonSQS, sleep and try again, currentRetryCount=" + currentRetryCount + ",current timestamp=" + new Date());
                        Thread.sleep(SQS_RETRY_SLEEP_TIME);
                    }
                }
			}
		} else {
			FormatUtils.getInstance().logMemoryUsage("createMessagesForCrawl() domainId=" + domainId + ", domainName=" + domainName + ",htmlQueueName=" + htmlQueueName
					+ ",total number of target URLs=0.");
		}
		FormatUtils.getInstance()
				.logMemoryUsage("createMessagesForCrawl() ends. domainId=" + domainId + ", domainName=" + domainName + ",htmlQueueName=" + htmlQueueName
						+ ",total messages=" + totalMessages + ",urlList.size()=" + urlList.size() + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	// create one message in the HTML controller queue for each domain
	private void sendDomainCrawlDataToHtmlControllerQueue(List<OwnDomainEntity> domainsToBeProcessedList) throws Exception {
		String controllerQueueName = IConstants.QUEUE_NAME_NEW_URL_DAILY_HTML_QUEUE_NAMES_FIFO;
		FormatUtils.getInstance().logMemoryUsage("sendDomainCrawlDataToHtmlControllerQueue() begins. controllerQueueName=" + controllerQueueName);

		long startTimestamp = System.currentTimeMillis();
		String queueUrl;
		String domainSpecificQueueName = null;
		String domainIdString = null;
		String languageCode = null;
		Map<String, String> messages = new HashMap<String, String>();
		int totalMessages = 0;
		int maxConcurrentCrawlThreads = 0;
		String urlCrawlParameters = null;
		UrlCrawlParametersVO[] urlCrawlParametersVoArray = null;
		Gson gson = new Gson();
		int delayInSecondsPerHttpRequest = 0;
		UrlCrawlParametersVO urlCrawlParametersVo = null;
		String messageBodyInJsonFormat = null;
		String specificUserAgent = null;
		String queueNamePrefix = null;
		Boolean enableJavascriptCrawl = null;
		Boolean enableScrapyCrawl = null;
		int domainId = 0;
		String region = null;
		Integer javascriptTimeoutInSecond = null;

		// initialize the queue
		try {
			queueUrl = SQSUtils.getInstance().createFifoQueue(controllerQueueName);
			SQSUtils.getInstance().purgeQueue(queueUrl);
		} catch (Exception e) {
			e.printStackTrace();
			return;
		}

		queueNamePrefix = IConstants.QUEUE_NAME_NEW_URL_DAILY_HTML_PREFIX;

		List<OwnDomainEntity> ownDomainEntityList = new ArrayList<OwnDomainEntity>();

		// https://www.wrike.com/open.htm?id=496837510
		List<OwnDomainEntity> ownDomainEntityCrawlTrackDateList = ownDomainEntityDAO.getByCrawlTrackingDate();
		if (ownDomainEntityCrawlTrackDateList != null && !ownDomainEntityCrawlTrackDateList.isEmpty()) {
			final Map<Integer, OwnDomainEntity> domainMapWithUrlCrawlParameters = this.ownDomainEntityDAO.getDomainsWithUrlCrawlParameters()
					.stream().collect(Collectors.toMap(OwnDomainEntity::getId, ownDomainEntity -> ownDomainEntity));
			for (OwnDomainEntity ownDomainEntityCrawlTrackDate : ownDomainEntityCrawlTrackDateList) {
                for (OwnDomainEntity ownDomainEntityToBeProcessed : domainsToBeProcessedList) {
                    if (ownDomainEntityCrawlTrackDate.getId().intValue() == ownDomainEntityToBeProcessed.getId().intValue()) {
                        final OwnDomainEntity domainWithUrlCrawlParameters = domainMapWithUrlCrawlParameters.get(ownDomainEntityCrawlTrackDate.getId());
                        ownDomainEntityCrawlTrackDate.setDomain(domainWithUrlCrawlParameters.getDomain());
                        ownDomainEntityCrawlTrackDate.setLanguage(domainWithUrlCrawlParameters.getLanguage());
                        ownDomainEntityCrawlTrackDate.setUrlCrawlParameters(domainWithUrlCrawlParameters.getUrlCrawlParameters());
                        ownDomainEntityList.add(ownDomainEntityCrawlTrackDate);
                        break;
                    }
                }
			}
		} else {
			ownDomainEntityList = domainsToBeProcessedList;
		}

		for (OwnDomainEntity ownDomainEntity : ownDomainEntityList) {
			domainId = ownDomainEntity.getId();
			enableScrapyCrawl = null; // by default, do not enable Scrapy crawl
			enableJavascriptCrawl = null; // by default, do not enable Javascript crawl
			maxConcurrentCrawlThreads = 1; // by default, 1 thread per domain
			delayInSecondsPerHttpRequest = 0; // by default, 0 seconds delay between each HTTP request
			specificUserAgent = null; // by default, use standard user agent

			// by default, use Page Crawl API standard endpoint http://***********/crawl
			// When region is 'London', use Page Crawl API London endpoint http://**************/crawl
			region = null;
			javascriptTimeoutInSecond = null;

			if (StringUtils.isNotBlank(ownDomainEntity.getUrlCrawlParameters())) {
				urlCrawlParameters = ownDomainEntity.getUrlCrawlParameters();
				urlCrawlParametersVoArray = gson.fromJson(urlCrawlParameters, UrlCrawlParametersVO[].class);
				for (int idx = 0; idx < urlCrawlParametersVoArray.length; idx++) {
					urlCrawlParametersVo = urlCrawlParametersVoArray[idx];
					// enable Javascript crawl
					if (urlCrawlParametersVo.getType().equalsIgnoreCase(IConstants.ENABLE_JAVASCRIPT_CRAWL)) {
						enableJavascriptCrawl = BooleanUtils.toBoolean(urlCrawlParametersVo.getData(), "true", "false");
					}
					// maximum number of concurrent threads per domain
					else if (urlCrawlParametersVo.getType().equalsIgnoreCase(IConstants.MAX_CONCURRENT_THREADS)) {
						maxConcurrentCrawlThreads = Integer.parseInt(urlCrawlParametersVo.getData());
					}
					// delay in seconds between HTTP requests
					else if (urlCrawlParametersVo.getType().equalsIgnoreCase(IConstants.DELAY_IN_SECONDS)) {
						delayInSecondsPerHttpRequest = Integer.parseInt(urlCrawlParametersVo.getData());
					}
					// user agent name for sending HTTP requests
					else if (urlCrawlParametersVo.getType().equalsIgnoreCase(IConstants.USER_AGENT)) {
						if (StringUtils.isNotBlank(urlCrawlParametersVo.getData())) {
							specificUserAgent = urlCrawlParametersVo.getData();
						}
					}
					// region
					else if (urlCrawlParametersVo.getType().equalsIgnoreCase(IConstants.REGION)) {
						region = urlCrawlParametersVo.getData();
					}
					// javascriptTimeoutInSecond
					else if (urlCrawlParametersVo.getType().equalsIgnoreCase(IConstants.JAVASCRIPT_TIMEOUT_IN_SECOND)) {
						javascriptTimeoutInSecond = Integer.parseInt(urlCrawlParametersVo.getData());
					}
				}
			}

			domainIdString = String.valueOf(domainId);
			if (StringUtils.isNotBlank(ownDomainEntity.getLanguage())) {
				languageCode = ownDomainEntity.getLanguage().toUpperCase();
				domainSpecificQueueName = languageCode.concat(IConstants.UNDERSCORE).concat(domainIdString);
			} else {
				domainSpecificQueueName = domainIdString;
			}
			domainSpecificQueueName = queueNamePrefix.concat(domainSpecificQueueName);
			if (StringUtils.isNotBlank(domainSpecificQueueName)) {
				// purge queues when debug mode
				//if (isDebug == true) {
				//	queueUrl = SQSUtils.getInstance().createQueue(domainSpecificQueueName);
				//	System.out.println("sendDomainCrawlDataToHtmlControllerQueue() purging domainSpecificQueueName=" + domainSpecificQueueName + ",queueUrl=" + queueUrl);
				//	SQSUtils.getInstance().purgeQueue(queueUrl);
				//	continue nextOwnDomainEntity;
				//}
				try {
					// create message body in JSON format with the following elements: 
					// 1) domain specific queue name, 
					// 2) delay in seconds per HTTP request (optional, by default 0 second)
					// 3) maximum number of threads per queue (optional, by default 1 thread per domain)
					// 4) domain-specific user agent (optional, by default use the user-agent specified in crawler.properties)
					messageBodyInJsonFormat = PutMessageUtils.getInstance().createControllerMessageBodyInJsonFormat(domainId, domainSpecificQueueName,
							delayInSecondsPerHttpRequest, maxConcurrentCrawlThreads, specificUserAgent, enableJavascriptCrawl, enableScrapyCrawl, region,
							javascriptTimeoutInSecond);
					messages.put(domainIdString, messageBodyInJsonFormat);
					FormatUtils.getInstance().logMemoryUsage(
							"sendDomainCrawlDataToHtmlControllerQueue() domainId=" + domainIdString + ",messageBodyInJsonFormat=" + messageBodyInJsonFormat);
					totalMessages++;
					if (messages.size() >= 10) {
						SQSUtils.getInstance().sendBatchMessageToFifoQueue(queueUrl, messages);
						messages = new HashMap<String, String>();
					}
					Thread.sleep(100);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
		if (messages != null && messages.size() > 0) {
			SQSUtils.getInstance().sendBatchMessageToFifoQueue(queueUrl, messages);
		}
		FormatUtils.getInstance().logMemoryUsage("sendDomainCrawlDataToHtmlControllerQueue() ends. controllerQueueName=" + controllerQueueName + ",totalMessages="
				+ totalMessages + ",total elapsed time in sec.=" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private void sendReport(List<PutNewTargetUrlToQueueReportValueObject> putNewTargetUrlToQueueReportValueObjectList) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("sendReport() begins.");

		String[] emailAddressArray = new String[] { "<EMAIL>" };
		int retryCount = 0;
		Map<String, Object> map = new HashMap<String, Object>();
		String emailSubject = "Put New Target Urls to Queues Report as of " + DateFormatUtils.format(new Date(), IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
		map.put("putNewTargetUrlToQueueReportValueObjectList", putNewTargetUrlToQueueReportValueObjectList);

		AgencyInfoEntity agencyInfoEntity = agencyInfoService.getByDomainId(1701);
		while (retryCount < IConstants.MAX_SEND_EMAIL_RETRY_COUNT) {
			try {
				emailSenderComponent.sendMimeMultiPartZeptoMailAndBcc(IConstants.NOTIFICATION_EMAIL_ADDRESS, emailAddressArray, emailSubject,
						"mail_put_new_targets_to_queues_report.txt", "mail_put_new_targets_to_queues_report.html", map, agencyInfoEntity);
				retryCount = IConstants.MAX_SEND_EMAIL_RETRY_COUNT;
			} catch (Exception e) {
				e.printStackTrace();
				retryCount++;
				if (retryCount >= IConstants.MAX_SEND_EMAIL_RETRY_COUNT) {
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("sendReport() retryCount=" + retryCount);
					try {
						Thread.sleep(IConstants.RETRY_WAIT_TIME_IN_MILLISECONDS);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("sendReport() ends.");
	}
}
