package com.actonia.polite.crawl;

import com.actonia.IConstants;
import com.actonia.concurrency.BaseThreadCommand;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.dao.*;
import com.actonia.entity.*;
import com.actonia.exception.GatewayException;
import com.actonia.utils.*;
import com.actonia.value.object.*;
import com.amazonaws.services.sqs.model.Message;
import com.google.gson.Gson;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.util.EntityUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.File;
import java.net.URL;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

// crawl URL string by invoking Scrapy crawler API and storing crawl results in ClickHouse database
public class PoliteCrawlCommand extends BaseThreadCommand {
	// when SQS returns error, sleep for thirty seconds
	private static final int SQS_RETRY_SLEEP_TIME = 30000;
	private static final boolean isResponseAsHtml = false;
	private static final int CRAWL_FAILED_URLS_LIMIT = 500;
	// map key = Md5 hash code of URL string
	// map value = null string
	private static ConcurrentMap<String, String> concurrentUrlHashCodeMap = new ConcurrentHashMap<String, String>();
	// map key = domain ID
	// map value = Map of ((hash code of (domain Id, URL without any trailing slash)), empty string)
	private static ConcurrentMap<Integer, Map<String, String>> domainIdUrlHashCodeAlreadyCrawledMapMap = new ConcurrentHashMap<Integer, Map<String, String>>();
	public final Gson gson = new Gson();
	public final List<PoliteCrawlStateLog> politeCrawlStateLogList = new ArrayList<>();
	private final Logger log = LogManager.getLogger(PoliteCrawlCommand.class);
	private final Set<Integer> bigDataIndicatorIdSet;
	private final List<TargetUrlDeadQueueDTO> sendDeadQueueTargetUrlList = new ArrayList<>();
	//  private Date runStartTime;
	//  private List<String> urlWith5xxStatusCodeList = new ArrayList<String>();
	//  private List<String> urlWith4xxStatusCodeList = new ArrayList<String>();
	private final DomainCrawlParameters domainCrawlParameters;
	private final List<String> updateDisableUrlMurmurHashList;
	private final TargetUrlEntityDAO targetUrlEntityDAO;
	private final PoliteCrawlStateLogDAO politeCrawlStateLogDAO;
	private final HtmlBigDataClickHouseDAO bigDataClickHouseDAO;
	private final String apiEndpoint;
	//private boolean isDebug = false;
	private String ip;
	private String queueName;
	private Integer messagesPerIteration;
	private int crawlType;
	private String userAgent;
	private int delayInSecondsPerHttpRequest;
	private int totalMessagesRetrieved = 0;
	private int totalClickHouseRecordsCreated = 0;
	private int totalMessagesAlreadyCrawledToday = 0;
	//  private int totalNumberOf5xxAlertStatusCode = 0;
	//  private int totalNumberOf4xxAlertStatusCode = 0;
	private int totalMessagesSkipped = 0;
	private int totalStatusCodeNotAvailable = 0;
	private int totalPrevious4xxCurrent2xx3xx = 0;
	private Integer domainId = null;
	private boolean isEndThread = false;
	private Boolean enableJavascriptCrawl;
	private String queueUrl;
	private String controllerMessageBody;
	private String controllerQueueUrl;
	private Map<String, String> domainNameUserAgentMap = new HashMap<String, String>();
	private int totalMessagesBeforeCheckingStopQueue = 0;
	private Map<String, String> pageCrawlerApiRequestHeaders;
	private Set<String> hostNameHttpStatus429Set = new HashSet<String>();
	// map key = competitor URL hostname
	// map value = number of response code 999
	private Map<String, Integer> hostNameHttpStatus999Map = new HashMap<String, Integer>();
	private List<AdditionalContentEntity> additionalContentEntityList = null;
	private String domainName = null;
	private List<ElementMappingPatternEntity> elementMappingPatternEntities = null;
	private Map<String, HtmlClickHouseEntity> hashCodeHtmlClickHouseEntityMap = new HashMap<String, HtmlClickHouseEntity>();
	private ContentGuardUrlDAO contentGuardUrlDAO;
	private String region;
	private Integer javascriptTimeoutInSecond;
	private final String fileName;
    private final UrlMetricsClickHouseDAO urlMetricsClickHouseDAO;
    private static final String fieldNames = "url,amphtml_flag,analyzed_url_s,analyzed_url_flg_s,archive_flg,archive_flg_x_tag,blocked_by_robots,canonical,canonical_flg,canonical_header_flag,canonical_header_type,canonical_type,canonical_url_is_consistent,content_type,description,description_flg,description_length,follow_flg,h1,h1_count,h1_length,h2,header_noarchive,header_noodp,header_nosnippet,header_noydir,hreflang_errors,hreflang_links_out_count,hreflang_url_count,index_flg,index_flg_x_tag,indexable,insecure_resources_flag,meta_charset,meta_content_type,meta_disabled_sitelinks,meta_noodp,meta_nosnippet,meta_noydir,meta_redirect,mixed_redirects,mobile_rel_alternate_url_is_consistent,noodp,nosnippet,noydir,og_markup_flag,og_markup_length,redirect_final_url,redirect_flg,redirect_times,response_code,response_headers,robots,robots_contents,title,title_length,viewport_content,page_analysis_results,base_tag,base_tag_flag,base_tag_target";
    private static final List<String> historicalFieldNames = Arrays.asList(fieldNames.split(","));
    public PoliteCrawlCommand(String ip, String queueName, int messagesPerIteration, int crawlType, String userAgent, int delayInSecondsPerHttpRequest,
							  Boolean enableJavascriptCrawl, Integer javascriptTimeoutInSecond, String controllerMessageBody, String controllerQueueUrl,
							  Map<String, String> domainNameUserAgentMap, Map<String, String> pageCrawlerApiRequestHeaders, List<AdditionalContentEntity> additionalContentEntityList,
							  Integer domainId, String domainName, List<ElementMappingPatternEntity> elementMappingPatternEntities,
							  Map<String, HtmlClickHouseEntity> hashCodeHtmlClickHouseEntityMap, String region, int maximumThreadsPerQueue) {
		super();
		this.ip = ip;
		this.queueName = queueName;
		this.messagesPerIteration = messagesPerIteration;
		this.crawlType = crawlType;
		this.userAgent = userAgent;
		this.delayInSecondsPerHttpRequest = delayInSecondsPerHttpRequest;
		this.controllerMessageBody = controllerMessageBody;
		this.controllerQueueUrl = controllerQueueUrl;
		if (domainNameUserAgentMap != null && !domainNameUserAgentMap.isEmpty()) {
			this.domainNameUserAgentMap = domainNameUserAgentMap;
		}
		this.enableJavascriptCrawl = enableJavascriptCrawl;
		this.javascriptTimeoutInSecond = javascriptTimeoutInSecond;
		this.pageCrawlerApiRequestHeaders = pageCrawlerApiRequestHeaders;
		this.additionalContentEntityList = additionalContentEntityList;
		this.domainId = domainId;
		this.domainName = domainName;
		this.elementMappingPatternEntities = elementMappingPatternEntities;
		this.hashCodeHtmlClickHouseEntityMap = hashCodeHtmlClickHouseEntityMap;
		this.contentGuardUrlDAO = SpringBeanFactory.getBean("contentGuardUrlDAO");
		this.region = region;
		this.apiEndpoint = StringUtils.equalsIgnoreCase(region, IConstants.LONDON) ? PoliteCrawlConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_SCRAPY_CRAWLER_LONDON_ENDPOINTS)
				: PoliteCrawlConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_SCRAPY_CRAWLER_ENDPOINTS);
		domainCrawlParameters = new DomainCrawlParameters();
		domainCrawlParameters.setDomain(domainName);
		domainCrawlParameters.setUserAgent(userAgent);
		domainCrawlParameters.setEnableJavascriptCrawl(enableJavascriptCrawl);
		domainCrawlParameters.setRegion(region);
		domainCrawlParameters.setJavascriptTimeoutInSecond(javascriptTimeoutInSecond);
		domainCrawlParameters.setDomainId(domainId);
		domainCrawlParameters.setCrawlerRequestHeaders(pageCrawlerApiRequestHeaders);
		domainCrawlParameters.setDelayInSecondsPerHttpRequest(delayInSecondsPerHttpRequest);
		domainCrawlParameters.setMessagesPerIteration(messagesPerIteration);
		domainCrawlParameters.setAdditionalContentEntityList(additionalContentEntityList);
		domainCrawlParameters.setMaxConcurrentThreads(maximumThreadsPerQueue);
		domainCrawlParameters.setQueueName(queueName);
		updateDisableUrlMurmurHashList = new ArrayList<>();
		targetUrlEntityDAO = SpringBeanFactory.getBean("targetUrlEntityDAO");
		politeCrawlStateLogDAO = SpringBeanFactory.getBean("politeCrawlStateLogDAO");
		try {
			final Set<ChangeIndMaster> bigDataIndicatorSet = ChangeIndMasterClickHouseDAO.getInstance().bigDataIndicatorSet;
			bigDataIndicatorIdSet = bigDataIndicatorSet.stream().map(ChangeIndMaster::getChgId).collect(Collectors.toSet());
			bigDataClickHouseDAO = HtmlBigDataClickHouseDAO.getInstance();
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		this.fileName = CrawlerUtils.SCRAPY_CRAWLER_REQUEST_LOG_LOCATION + File.separator + LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE) + File.separator + domainId + ".log";
		// create directory if it does not exist
		CrawlerUtils.createDirectoryIfNotExists(fileName);
        this.urlMetricsClickHouseDAO = SpringBeanFactory.getBean("urlMetricsClickHouseDAO");
	}

	/**
	 * Determines if the given URL is for initial crawl only in an RSS feed.
	 *
	 * @param urlMetricsEntityV3 the URL metrics entity to check
	 * @return true if the URL is for initial crawl only in an RSS feed, false otherwise
	 */
	private static boolean isRssInitialCrawlOnlyUrl(UrlMetricsEntityV3 urlMetricsEntityV3) {
		final Integer sourceType = urlMetricsEntityV3.getSourceType();
		final Integer initialCrawlOnly = urlMetricsEntityV3.getInitialCrawlOnly();
		final boolean isRssUrl = sourceType != null && sourceType == IConstants.TARGET_URL_SOURCE_TYPE_RSS;
		final boolean isInitialCrawlOnly = initialCrawlOnly != null && initialCrawlOnly == IConstants.TARGET_URL_INITIAL_CRAWL_ONLY_YES;
		return isRssUrl && isInitialCrawlOnly;
	}

	@Override
	public void execute() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		log.info("execute() begins ip={}, queueName={}, messagesPerIteration={}, crawlType={}, userAgent={}, delayInSecondsPerHttpRequest={}, enableJavascriptCrawl={}," +
						" javascriptTimeoutInSecond={}, controllerMessageBody={}, controllerQueueUrl={}, domainId={}, domainName={}, hashCodeHtmlClickHouseEntityMap.size()={}, region={}, apiEndpoint={}",
				ip, queueName, messagesPerIteration, crawlType, userAgent, delayInSecondsPerHttpRequest, enableJavascriptCrawl, javascriptTimeoutInSecond, controllerMessageBody, controllerQueueUrl,
				domainId, domainName, hashCodeHtmlClickHouseEntityMap.size(), region, apiEndpoint);
		if (pageCrawlerApiRequestHeaders != null && !pageCrawlerApiRequestHeaders.isEmpty()) {
			for (String key : pageCrawlerApiRequestHeaders.keySet()) {
				log.info("queueName={}, pageCrawlerApiRequestHeaders name={}, value={}", queueName, key, pageCrawlerApiRequestHeaders.get(key));
			}
		}

		if (additionalContentEntityList != null && !additionalContentEntityList.isEmpty()) {
			for (AdditionalContentEntity additionalContentEntity : additionalContentEntityList) {
				log.info("queueName={}, additionalContentEntity={}", queueName, additionalContentEntity);
			}
		}

		if (elementMappingPatternEntities != null && !elementMappingPatternEntities.isEmpty()) {
			for (ElementMappingPatternEntity elementMappingPatternEntity : elementMappingPatternEntities) {
				log.info("queueName={}, elementMappingPatternEntity={}", queueName, elementMappingPatternEntity);
			}
		}

		List<Message> messageList = null;
		long elapsedTimeInSeconds = 0L;
		String formattedElapsedTime = null;
		boolean isSkipRestrictedDomain = false;
		int totalJavascriptCrawlerThreads = 0;

		try {
			// check if crawl the client domain only at specified times
			if (crawlType == IConstants.CRAWL_TYPE_TARGET_URL_HTML || crawlType == IConstants.CRAWL_TYPE_CONTENT_GUARD) {
				isSkipRestrictedDomain = CrawlerUtils.getInstance().checkIfSkipRestrictedDomain(domainId, ip);
				if (isSkipRestrictedDomain) {
					return;
				}

				// https://www.wrike.com/open.htm?id=498881092
				// https://www.wrike.com/open.htm?id=1000993989
				if (domainId == 7480 || domainId == 7479) {
					return;
				}
			}

			whileThereAreMessages:
			while (!isEndThread) {
				messageList = retrieveMessagesFromQueue(getQueueUrl(), messagesPerIteration);
				if (messageList != null && !messageList.isEmpty()) {
					crawlHtml(messageList, getQueueUrl());
				} else {
					log.warn("queueUrl={}, messageList is empty.", getQueueUrl());
					break whileThereAreMessages;
				}
			}
		} catch (Exception e) {

			// end crawl upon exception
			PoliteCrawl.setEndCrawl(true);
			log.error("queueName={}, e.getMessage()={}", queueName, e.getMessage());

			//StringWriter stringWriter = new StringWriter();
			//e.printStackTrace(new PrintWriter(stringWriter));
			//exceptionMessage = stringWriter.toString();
		} finally {
			hashCodeHtmlClickHouseEntityMap = null;
			CacheModleFactory.getInstance().setAliveIpAddress(ip);
			elapsedTimeInSeconds = (System.currentTimeMillis() - startTimestamp) / 1000;
			formattedElapsedTime = CommonUtils.formatElapsedTime(elapsedTimeInSeconds);
			if (BooleanUtils.isTrue(enableJavascriptCrawl)) {
				totalJavascriptCrawlerThreads = CrawlerUtils.getInstance().getTotalJavascriptCrawlerThreads() - 1;
				CrawlerUtils.getInstance().setTotalJavascriptCrawlerThreads(totalJavascriptCrawlerThreads);
			}
			log.info("queueName={}, totalMessagesRetrieved={}, totalClickHouseRecordsCreated={}, totalMessagesAlreadyCrawledToday={}, totalMessagesSkipped={}, totalStatusCodeNotAvailable={}, totalPrevious4xxCurrent2xx3xx={}, isEndThread={}, total elapsed time={}, totalJavascriptCrawlerThreads={}",
					queueName, totalMessagesRetrieved, totalClickHouseRecordsCreated, totalMessagesAlreadyCrawledToday, totalMessagesSkipped, totalStatusCodeNotAvailable, totalPrevious4xxCurrent2xx3xx, isEndThread, formattedElapsedTime, totalJavascriptCrawlerThreads);
			if (!this.politeCrawlStateLogList.isEmpty()) {
				log.info("OID: {} end crawl, send dead queue target url list size: {}", this.domainId, this.sendDeadQueueTargetUrlList.size());
				this.politeCrawlStateLogDAO.batchInsert(politeCrawlStateLogList);
			}
			if (!this.updateDisableUrlMurmurHashList.isEmpty()) {
				log.info("OID: {} end crawl, update disable url murmur hash code size: {}", this.domainId, this.updateDisableUrlMurmurHashList.size());
				this.batchUpdateDisableUrlMurmurHashCodes();
			}
		}
	}

	private void batchUpdateDisableUrlMurmurHashCodes() {
		this.targetUrlEntityDAO.batchUpdateUrlDisableCrawl(this.domainId, this.updateDisableUrlMurmurHashList);
		log.info("batch update OID: {} url disableCrawl, url size: {}", this.domainId, this.updateDisableUrlMurmurHashList.size());
	}

	@Override
	protected void undo() throws Exception {
	}

	private List<Message> retrieveMessagesFromQueue(String queueUrl, int messagesPerIteration) throws Exception {
		List<Message> messageList = new ArrayList<>();

		long startTimestamp = System.currentTimeMillis();

		int maxMessagesBeforeCheckingStopQueue = 0;

		if (PoliteCrawl.isEndCrawl()) {
			log.warn("queueName={}, isEndCrawl is true, exit retrieveMessagesFromQueue, elapsed(ms.)= {}", queueName, System.currentTimeMillis() - startTimestamp);
			return messageList;
		}

		if (delayInSecondsPerHttpRequest > 0) {
			maxMessagesBeforeCheckingStopQueue = IConstants.MAX_MESSAGES_BEFORE_CHECKING_STOP_QUEUE / 10;
		} else {
			maxMessagesBeforeCheckingStopQueue = IConstants.MAX_MESSAGES_BEFORE_CHECKING_STOP_QUEUE;
		}

		if (totalMessagesBeforeCheckingStopQueue >= maxMessagesBeforeCheckingStopQueue) {
			if (CrawlerUtils.getInstance().checkIfStopCrawl()) {
				PoliteCrawl.setEndCrawl(true);
				log.warn("queueName={}, isEndCrawl is true, exit retrieveMessagesFromQueue, elapsed(ms.)= {}", queueName, System.currentTimeMillis() - startTimestamp);
				return messageList;
			}
			log.warn("retrieveMessagesFromQueue: queueName={}, totalMessagesBeforeCheckingStopQueue={}, maxMessagesBeforeCheckingStopQueue={}, PoliteCrawl.isEndCrawl()= {}, elapsed(ms.)= {}", queueName, totalMessagesBeforeCheckingStopQueue, maxMessagesBeforeCheckingStopQueue, PoliteCrawl.isEndCrawl(), System.currentTimeMillis() - startTimestamp);
			totalMessagesBeforeCheckingStopQueue = 0;
		}

		int currentRetryCount = 0;

		// retrieve a list of messages from queue.  number of messages = number of messages per iteration
		while (!PoliteCrawl.isEndCrawl() && currentRetryCount < IConstants.MAXIMUM_RETRY_THREE_TIMES_COUNT) {
			try {
				messageList = SQSUtils.getInstance().getMessageFromQueue(queueUrl, messagesPerIteration, IConstants.SQS_MSG_TIMEOUT_IN_43200_SECONDS);
				if (messageList != null && !messageList.isEmpty()) {
					totalMessagesBeforeCheckingStopQueue = totalMessagesBeforeCheckingStopQueue + messageList.size();
					return messageList;
				}
				break;
			} catch (Exception e) {
				currentRetryCount++;
				log.error("retrieveMessagesFromQueue: queueName={}, error for AmazonSQS, sleep and try again, currentRetryCount={}}", queueName, currentRetryCount);
				Thread.sleep(SQS_RETRY_SLEEP_TIME);
			}
		}
		return messageList;
	}

	private void crawlHtml(List<Message> messageList, String queueUrl) throws Exception {
		long messageStartTimestamp = 0L;
		UrlMetricsEntityV3 urlMetricsEntityV3 = null;
		HtmlClickHouseEntity htmlClickHouseEntityCurrent = null;
		HtmlFileNameClickHouseEntity htmlFileNameClickHouseEntity = null;
		String messageBody = null;
		HtmlClickHouseEntity htmlClickHouseEntityTest = null;
		HtmlClickHouseEntity htmlClickHouseEntityPrevious = null;
		String urlString = null;
		Integer httpStatusCode = null;
		Date previousTrackDate = null;
		Date currentTrackDate = null;
		String previousTrackDateString = null;
		String currentTrackDateString = null;
		boolean isSkipUrl = false;
		String previousResponseCodeString = null;
		int previousResponseCodeNumber = 0;
		String finalUserAgent = null;
		boolean isDifferent = false;
		List<Message> messageToBeRemovedList = new ArrayList<Message>();
		List<HtmlFileNameClickHouseEntity> htmlFileNameClickHouseEntityList = new ArrayList<HtmlFileNameClickHouseEntity>();
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = new ArrayList<HtmlClickHouseEntity>();
		List<NewHtmlClickHouseEntity> newHtmlClickHouseEntityList = new ArrayList<>();
		String urlMd5Hash = null;
		boolean isJavascriptCrawler = false;
		ChangeTrackingHashCdJson[] changeTrackingHashCdJsonArray = null;
		String reversedUrlDomain = null;
		URL url = null;
		String host = null;
		Integer numberOfResponseCode999 = null;
		boolean isStoreHtml = true;
		boolean isDefaultToRespCode999 = false;
		String hashCode = null;
		String s3FileName = null;
		String s3Location = null;
		Boolean isContentGuardCrawl = null;
		Integer crawlFrequencyType = null;
		Date crawlTimestamp = null;
		Date previousCrawlTimestamp = null;
		int totalUrlsUpdated = 0;
		int totalForbidden = 0;

		nextMessage:
		for (Message message : messageList) {

			if (isEndThread) {
				break nextMessage;
			}
			crawlTimestamp = null;
			s3FileName = null;
			s3Location = null;
			messageStartTimestamp = System.currentTimeMillis();

			// pause for the duration specified in "delayInSecondsPerHttpRequest"
			if (delayInSecondsPerHttpRequest > 0) {
				Thread.sleep(delayInSecondsPerHttpRequest * 1000);
			}

			// for target URLs crawl, the queue message contains previous crawl result of the change tracking fields or their MD5 hash codes in the 'target_url_html_daily' table
			messageBody = message.getBody();
			urlMetricsEntityV3 = gson.fromJson(messageBody, UrlMetricsEntityV3.class);
			urlString = StringUtils.trim(urlMetricsEntityV3.getUrl());
			totalMessagesRetrieved++;

			try {
				host = null;
				url = new URL(urlString);
				host = url.getHost();
			} catch (Exception e) {
				log.error("crawlHtml(): urlString=" + urlString + ", error=" + e.getMessage());
			}

			// do not process any of the 'CarRentals.com' URLs
			if (StringUtils.isNotBlank(host)) {
				// https://www.wrike.com/open.htm?id=963555819
				// https://www.wrike.com/open.htm?id=1536074415 revert to resume crawling Expedia URLs
				//if (StringUtils.containsIgnoreCase(host, ".carrentals.")
				if (StringUtils.containsIgnoreCase(host, ".carrentals.")) {
					log.warn("crawlHtml() skipped...host={}, ip={}, queueName={}, urlString={}", host, ip, queueName, urlString);
					messageToBeRemovedList.add(message);
					totalMessagesSkipped++;
					continue;
				}

				// https://www.wrike.com/open.htm?id=963555819
				if (crawlType == IConstants.CRAWL_TYPE_COMPETITOR_URL_HTML) {
					for (String expediaDomain : PoliteCrawl.getExpediaDomainList()) {
						if (StringUtils.containsIgnoreCase(host, expediaDomain)) {
							log.warn("crawlHtml() skipped...Expedia or CarRentals.com URL, ip={}, queueName={}, urlString={}", ip, queueName, urlString);
							messageToBeRemovedList.add(message);
							totalMessagesSkipped++;
							continue nextMessage;
						}
					}
				}
			}

			// https://www.wrike.com/open.htm?id=815089776
			// 2021-12-24 to 2021-12-28
			//if (StringUtils.isNotBlank(host)) {
			//  if (StringUtils.containsIgnoreCase(host, "lowes.ca") || StringUtils.containsIgnoreCase(host, "rona.ca")
			//          || StringUtils.containsIgnoreCase(host, "renodepot.com")) {
			//      FormatUtils.getInstance()
			//              .logMemoryUsage("crawlHtml() skipped...host=" + host + ", ip=" + ip + ",queueName=" + queueName + ",urlString=" + urlString);
			//      messageToBeRemovedList.add(message);
			//      totalMessagesSkipped++;
			//      continue nextMessage;
			//  }
			//}

			// do not process certain URLs
			isSkipUrl = CrawlerUtils.getInstance().checkIfSkipUrl(urlString, crawlType, ip, queueUrl, message, IConstants.POLITE_CRAWL_V2);
			if (isSkipUrl) {
				messageToBeRemovedList.add(message);
				totalMessagesSkipped++;
				continue;
			}

			final boolean contentGuardCrawl = crawlType == IConstants.CRAWL_TYPE_CONTENT_GUARD;
			final boolean targetUrlCrawl = crawlType == IConstants.CRAWL_TYPE_TARGET_URL_HTML;
			if (targetUrlCrawl && urlMetricsEntityV3.getCrawl_timestamp() != null) {
				previousCrawlTimestamp = DateUtils.parseDate(urlMetricsEntityV3.getCrawl_timestamp(), new String[]{IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS});
			} else {
				previousCrawlTimestamp = null;
			}

			// when content guard crawl...
			isContentGuardCrawl = urlMetricsEntityV3.getContent_guard_crawl_flag();
			if (BooleanUtils.isTrue(isContentGuardCrawl)) {
				crawlFrequencyType = urlMetricsEntityV3.getCrawl_frequency_type();
				if (crawlFrequencyType == IConstants.FREQUENCY_TYPE_UPLOADED) {
					isStoreHtml = false; // S3 object already exists
					crawlTimestamp = DateUtils.parseDate(urlMetricsEntityV3.getCrawl_timestamp(), new String[]{IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS});
					// when Content Guard crawl frequency is uploaded and S3 file name is available, determine the S3 location
					if (StringUtils.isNotBlank(urlMetricsEntityV3.getS3_file_name())) {
						s3FileName = urlMetricsEntityV3.getS3_file_name();
						s3Location = CrawlerUtils.getInstance().getS3Location(s3FileName);
						log.info("crawlFrequencyType=uploaded, queueName={}, urlString={}, crawlTimestamp={}, s3FileName={}, s3Location={}", queueName, urlString, crawlTimestamp, s3FileName, s3Location);
						if (StringUtils.isBlank(s3Location)) {
							log.error("crawlFrequencyType=uploaded but S3 location not available, queueName={}, urlString={}", queueName, urlString);
							messageToBeRemovedList.add(message);
							totalMessagesSkipped++;
							continue;
						}
					}
					// error when Content Guard crawl frequency is uploaded but S3 file name is not available
					else {
						log.error("crawlFrequencyType=uploaded but S3 file name not available, queueName={}, urlString={}", queueName, urlString);
						messageToBeRemovedList.add(message);
						totalMessagesSkipped++;
						continue;
					}
				}
				// when URL belongs to client domain, use client domain specific user agent for their URLs
				if (StringUtils.containsIgnoreCase(urlString, domainName)) {
					finalUserAgent = userAgent;
				}
				// when URL does not belong to client domain, use default user agent for non-client domain URLs
				else {
					finalUserAgent = PoliteCrawl.defaultUserAgent;
				}
			} else {
				finalUserAgent = userAgent;
			}
			// when competitor URL or Link Clarity source URL belongs any client domains, use that client domain's special user agent
			if (crawlType == IConstants.CRAWL_TYPE_COMPETITOR_URL_HTML) {
				for (String domainNameUserAgent : domainNameUserAgentMap.keySet()) {
					if (StringUtils.containsIgnoreCase(urlString, domainNameUserAgent)) {
						finalUserAgent = domainNameUserAgentMap.get(domainNameUserAgent);
						log.info("crawlType=competitor url and use client domain's user agent, queueName={}, urlString={}, domainNameUserAgent={}, finalUserAgent={}", queueName, urlString, domainNameUserAgent, finalUserAgent);
						break;
					}
				}
			}

			// retrieve previous crawled results from ClickHouse database that matches the domain ID and URL string.
			try {
				currentTrackDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
				currentTrackDateString = DateFormatUtils.format(currentTrackDate, IConstants.DATE_FORMAT_YYYYMMDD);
				urlMd5Hash = CrawlerUtils.getInstance().getMd5HashCode(urlString);

				if (targetUrlCrawl || contentGuardCrawl) {
					final Map<String, String> urlMd5HashMap = domainIdUrlHashCodeAlreadyCrawledMapMap.computeIfAbsent(domainId, k -> new HashMap<>());
					if (urlMd5HashMap.containsKey(urlMd5Hash)) {
						log.error("queueName={}, crawlType={}, url = {}, skipped with same hash code already crawled today but not yet persisted.", queueName, crawlType, urlString);
						messageToBeRemovedList.add(message);
						totalMessagesAlreadyCrawledToday++;
						continue;
					}
					urlMd5HashMap.put(urlMd5Hash, IConstants.EMPTY_STRING);
				} else {
					if (concurrentUrlHashCodeMap.containsKey(urlMd5Hash)) {
						log.error("queueName={}, crawlType={}, url = {} skipped, competitor url or link clarity url with same hash code already crawled today but not yet persisted.", queueName, crawlType, urlString);
						messageToBeRemovedList.add(message);
						totalMessagesAlreadyCrawledToday++;
						continue;
					} else {
						concurrentUrlHashCodeMap.put(urlMd5Hash, IConstants.EMPTY_STRING);
					}
				}

				if (targetUrlCrawl || contentGuardCrawl) {
					hashCode = CrawlerUtils.getInstance().getMd5HashCode(StringUtils.trim(urlString));
					if (hashCodeHtmlClickHouseEntityMap.containsKey(hashCode)) {
						htmlClickHouseEntityTest = hashCodeHtmlClickHouseEntityMap.get(hashCode);
						htmlClickHouseEntityPrevious = htmlClickHouseEntityTest.clone();
						hashCodeHtmlClickHouseEntityMap.remove(hashCode);
					} else {
						htmlClickHouseEntityPrevious = null;
					}
				} else if (crawlType == IConstants.CRAWL_TYPE_COMPETITOR_URL_HTML) {
					reversedUrlDomain = CrawlerUtils.getInstance().getReversedUrlDomain(ip, queueName, urlString);
					if (StringUtils.isBlank(reversedUrlDomain)) {
						log.error("crawlType=competitor url and reversedUrlDomain cannot be determined, queueName={}, urlString={}", queueName, urlString);
						messageToBeRemovedList.add(message);
						continue;
					}
					htmlClickHouseEntityPrevious = getPreviousHtmlClickHouseEntity(urlMetricsEntityV3);
					//htmlClickHouseEntityPrevious = CompetitorUrlHtmlClickHouseDAO.getInstance().getPrevious(ip, queueName, reversedUrlDomain, urlString,
					//      getPreviousFieldNames(), PoliteCrawl.COMPETITOR_URL_HTML_CLICKHOUSE_TABLE_NAME, null);
				}

				// when Content Guard hourly or uploaded crawl, the URL can be crawled many times a day, and therefore no need to track if already crawled today
				if (BooleanUtils.isTrue(isContentGuardCrawl)
						&& (crawlFrequencyType == IConstants.FREQUENCY_TYPE_HOURLY || crawlFrequencyType == IConstants.FREQUENCY_TYPE_UPLOADED)) {
					// proceed...
				}
				// when not Content Guard hourly crawl, the URL can be crawled only once a day, and therefore need to track if already crawled today
				else if (htmlClickHouseEntityPrevious != null) {
					previousTrackDate = htmlClickHouseEntityPrevious.getTrackDate();
					previousTrackDateString = DateFormatUtils.format(previousTrackDate, IConstants.DATE_FORMAT_YYYYMMDD);
					// when the URL has already been crawled and persisted today, skip...
					if (StringUtils.equalsIgnoreCase(previousTrackDateString, currentTrackDateString)) {
						log.error("queueName={}, crawlType={}, url = {}, skipped with same track date already crawled today but not yet persisted.", queueName, crawlType, urlString);
						messageToBeRemovedList.add(message);
						totalMessagesAlreadyCrawledToday++;
						continue;
					}
				}

				// when Javascript crawler API is enabled...
				isJavascriptCrawler = BooleanUtils.isTrue(enableJavascriptCrawl);

				//if (isJavascriptCrawler == true) { //debug
				//  messageToBeRemovedList.add(message); //debug
				//  continue nextMessage; //debug
				//} //debug

				// when (competitor URL crawl or link clarity crawl)
				if (crawlType == IConstants.CRAWL_TYPE_COMPETITOR_URL_HTML) {
					if (hostNameHttpStatus429Set.contains(host)) {
						htmlClickHouseEntityCurrent = getHtmlClickHouseEntityWithHttpStatusCode429(urlString, currentTrackDate, crawlTimestamp);
					} else {
						isDefaultToRespCode999 = checkIfDefaultToRespCode999(host, htmlClickHouseEntityPrevious);
						if (isDefaultToRespCode999) {
							htmlClickHouseEntityCurrent = getHtmlClickHouseEntityWithHttpStatusCode999(urlString, currentTrackDate, crawlTimestamp);
							//FormatUtils.getInstance()
							//      .logMemoryUsage("crawlHtml() default to resp cd 999, ip=" + ip + ",queueName=" + queueName + ",urlString=" + urlString);
						} else {
//                          final DomainCrawlParameters domainCrawlParameters = ScrapyApiUtils.getInstance().getScrapyCrawlerRequest(domainId, urlString, userAgent, additionalContentEntityList, enableJavascriptCrawl, javascriptTimeoutInSecond, this.crawlType, pageCrawlerApiRequestHeaders, isStoreHtml, s3Location, region);
//                          ScrapyApiUtils.getInstance().getScrapyCrawlerResponse(domainCrawlParameters, crawlType);
							htmlClickHouseEntityCurrent = invokePageCrawlerApi(urlString, additionalContentEntityList, finalUserAgent, currentTrackDate,
									isJavascriptCrawler, javascriptTimeoutInSecond, reversedUrlDomain, isStoreHtml, s3Location, crawlTimestamp, s3FileName, region);
						}
					}
				} else {
					htmlClickHouseEntityCurrent = invokePageCrawlerApi(urlString, additionalContentEntityList, finalUserAgent, currentTrackDate, isJavascriptCrawler,
							javascriptTimeoutInSecond, reversedUrlDomain, isStoreHtml, s3Location, crawlTimestamp, s3FileName, region);
				}

				// when URL can be crawled...
				if (htmlClickHouseEntityCurrent != null) {
					final int urlType = urlMetricsEntityV3.getUrlType() == null ? 0 : urlMetricsEntityV3.getUrlType();
					final boolean isManagedUrl = urlType == 1;
					if (BooleanUtils.isTrue(isContentGuardCrawl)) {
						htmlClickHouseEntityCurrent.setContentGuardInd(true);
					}

					if (targetUrlCrawl && previousCrawlTimestamp != null) {
						htmlClickHouseEntityCurrent.setPreviousCrawlTimestamp(previousCrawlTimestamp);
					}

					httpStatusCode = htmlClickHouseEntityCurrent.getHttpStatusCode();

					final String urlMurmur3Hash = MurmurHashUtils.getMurmurHash3_64(urlString);
					htmlClickHouseEntityCurrent.setUrlMurmurHash(urlMurmur3Hash);

					// if sourceType is RSS and is initial crawl only and status code is 200, then add the HTML to the list ready to save to CDB
					// # https://www.wrike.com/open.htm?id=1228696780
					final boolean isRssInitialCrawlOnlyUrl = isRssInitialCrawlOnlyUrl(urlMetricsEntityV3);
					if (targetUrlCrawl && isRssInitialCrawlOnlyUrl) {
						if (httpStatusCode == IConstants.HTTP_STATUS_CODE_200) {
							// if the crawl is successful, save html and disable crawl for the page.
							updateDisableUrlMurmurHashList.add(urlMurmur3Hash);
							htmlClickHouseEntityList.add(htmlClickHouseEntityCurrent);
							log.info("InitialCrawlOnlyUrl from RSS status code is 200, OID= {}, url = {}", domainId, url);
						} else {
							// not 200, save to polite_crawl_state_log
							this.non2xxHtmlEntity(htmlClickHouseEntityCurrent);
							log.warn("InitialCrawlOnlyUrl from RSS status code not 200, OID= {}, url = {}", domainId, url);
						}
						messageToBeRemovedList.add(message);
						continue;
					}

					// https://www.wrike.com/open.htm?id=411780206
					// https://www.wrike.com/open.htm?id=419142303
					if (httpStatusCode >= IConstants.HTTP_STATUS_CODE_900) {
						// proceed when HTTP status code is 900, 901 or 999
					}
					// skip when HTTP status code > 599
					else if (httpStatusCode > IConstants.HTTP_STATUS_CODE_599) {
						log.error("HTTP status code > 599, queueName = {}, url = {}, status code = {}", queueName, url, httpStatusCode);
						messageToBeRemovedList.add(message);
						continue;
					}
					final boolean urlStatusCode5XX = httpStatusCode >= 500 && httpStatusCode <= 599;

					// when HTTP status code 429 and target URLs daily crawl
					if (httpStatusCode == 429) {
						if (targetUrlCrawl || contentGuardCrawl) {
							messageToBeRemovedList.add(message);
							isEndThread = true;
							log.warn("HTTP status code 429, queueName = {}, url = {}, status code = {}", queueName, url, httpStatusCode);
							break;
						} else {
							hostNameHttpStatus429Set.add(host);
						}
					}
					// when the current status code is 408, 403 ,5xx, or 9xx and previous status code is 2xx, 3xx, or 404, skip this URL
					else if (httpStatusCode == 408 || httpStatusCode == 403 || urlStatusCode5XX || httpStatusCode == 900 || httpStatusCode == 999) {

						if ((targetUrlCrawl || contentGuardCrawl) && httpStatusCode == 403) {
							totalForbidden = CacheModleFactory.getInstance().getDomainIdTotalForbiddenMap().getOrDefault(domainId, 0);
							totalForbidden++;
							CacheModleFactory.getInstance().getDomainIdTotalForbiddenMap().put(domainId, totalForbidden);
						}

						// when previous version was created
						if (htmlClickHouseEntityPrevious != null) {
							previousResponseCodeString = htmlClickHouseEntityPrevious.getCrawlerResponse().getResponse_code();
							previousResponseCodeNumber = NumberUtils.toInt(previousResponseCodeString);

							// skip when previous response code = current response code
							if (httpStatusCode == previousResponseCodeNumber) {
								log.info("skip when previous response code: {} = current response code, queueName = {}, url = {}", httpStatusCode, queueName, url);
								// when (competitor URL crawl or link clarity crawl)
								if (crawlType == IConstants.CRAWL_TYPE_COMPETITOR_URL_HTML) {
									// and current and previous response code is 999
									if (httpStatusCode == IConstants.HTTP_STATUS_CODE_999 || previousResponseCodeNumber == IConstants.HTTP_STATUS_CODE_999) {
										if (hostNameHttpStatus999Map.containsKey(host)) {
											numberOfResponseCode999 = hostNameHttpStatus999Map.get(host);
											if (numberOfResponseCode999 == null) {
												numberOfResponseCode999 = 1;
											} else {
												numberOfResponseCode999++;
											}
										} else {
											numberOfResponseCode999 = 1;
										}
										hostNameHttpStatus999Map.put(host, numberOfResponseCode999);
									}
								}
							}
						} else {
							// do not pause and do not skip when previous document not created
						}
					}

					if (targetUrlCrawl && isManagedUrl) {
						// only don't save to polite_crawl_state_log when previous and current response code both are 2xx
						if (htmlClickHouseEntityPrevious != null) {
							previousResponseCodeNumber = NumberUtils.toInt(previousResponseCodeString);
							final boolean previousStatusNormal = previousResponseCodeNumber != 408 && !(previousResponseCodeNumber >= 500 && previousResponseCodeNumber <= 599);
							final boolean currentStatusNormal = httpStatusCode >= 200 && httpStatusCode <= 399;
							if (!previousStatusNormal || !currentStatusNormal) {
								this.non2xxHtmlEntity(htmlClickHouseEntityCurrent);
							}
						} else {
							this.non2xxHtmlEntity(htmlClickHouseEntityCurrent);
						}
                    }

					// create a new record for the current crawled HTML content:
					// 1) when there is no previous crawled results, or
					// 2) when there is a previous crawled results that matches the URL and crawl results have changed since last crawl.
					changeTrackingHashCdJsonArray = CrawlerUtils.getInstance().getChangeTrackingHashCdJsonArray(htmlClickHouseEntityCurrent);
					if (changeTrackingHashCdJsonArray != null && changeTrackingHashCdJsonArray.length > 0) {
						htmlClickHouseEntityCurrent.setChangeTrackingHashCdJsonArray(changeTrackingHashCdJsonArray);
					}
					// update 'htmlClickHouseEntityCurrent' (pass by reference)
					if (htmlClickHouseEntityPrevious != null) {
						previousResponseCodeString = htmlClickHouseEntityPrevious.getCrawlerResponse().getResponse_code();
						isDifferent = CrawlerUtils.getInstance().trackChanges(ip, htmlClickHouseEntityCurrent, urlMetricsEntityV3, changeTrackingHashCdJsonArray,
								previousResponseCodeString, htmlClickHouseEntityPrevious.getTrackDate(), crawlType, null, null);
					} else {
						isDifferent = true;
					}

					if (isDifferent) {
						htmlClickHouseEntityList.add(htmlClickHouseEntityCurrent);
						// store big data for the current HTML content
                        NewHtmlClickHouseEntity newHtmlClickHouseEntity = null;
                        try {
                            newHtmlClickHouseEntity = NewHtmlClickHouseEntity.createFromHtmlClickHouseEntity(htmlClickHouseEntityCurrent);
                        } catch (Exception e) {
	                        log.error("create newHtmlClickHouseEntity failed", e);
                        }
						PoliteCrawl.newHtmlClickHouseEntityQueue.add(newHtmlClickHouseEntity);
						final List<HtmlBigData> bigDataList = newHtmlClickHouseEntity.getHtmlBigDataList();
						try {
							if (!bigDataList.isEmpty()) {
								final ConcurrentLinkedQueue<HtmlBigData> htmlBigDataQueue = PoliteCrawl.getHtmlBigDataQueue();
								htmlBigDataQueue.addAll(bigDataList);
							}
						} catch (Exception e) {
							log.error("create dis_html_big_data failed", e);
						}
						if (targetUrlCrawl && htmlClickHouseEntityPrevious != null) {
							// if there is a previous HTML record, add it to the list of html_change table
							List<HtmlChange> htmlChanges;
							try {
								htmlChanges = HtmlChange.buildHtmlChange(urlMetricsEntityV3, htmlClickHouseEntityCurrent);
								if (htmlChanges != null && !htmlChanges.isEmpty()) {
									final ConcurrentLinkedQueue<HtmlChange> htmlChangeQueue = PoliteCrawl.getHtmlChangeQueue();
									htmlChangeQueue.addAll(htmlChanges);
									if (isManagedUrl) {
										// store htmlChanges to managedHtmlChangeQueue for GA/GSC
										final List<ManagedHtmlChange> managedHtmlChangeList = htmlChanges.stream()
												.filter(htmlChange -> !bigDataIndicatorIdSet.contains(htmlChange.getChgId()))
												.map(ManagedHtmlChange::createFromHtmlChange).collect(Collectors.toList());
										if (!managedHtmlChangeList.isEmpty()) {
											PoliteCrawl.managedHtmlChangeQueue.addAll(managedHtmlChangeList);
										}
									}
								}
							} catch (Exception e) {
								log.error("Failed to build HtmlChange, url= {} \nprevious = {} \ncurrent = {}", url, gson.toJson(urlMetricsEntityV3), gson.toJson(htmlClickHouseEntityCurrent), e);
							}
						}
					}

					if (htmlClickHouseEntityCurrent.getCrawlerResponse() != null
							&& StringUtils.isNotBlank(htmlClickHouseEntityCurrent.getCrawlerResponse().getFile_name())) {
						htmlFileNameClickHouseEntity = new HtmlFileNameClickHouseEntity();
						htmlFileNameClickHouseEntity.setDomainId(htmlClickHouseEntityCurrent.getDomainId());
						htmlFileNameClickHouseEntity.setUrl(htmlClickHouseEntityCurrent.getUrl());
						htmlFileNameClickHouseEntity.setTrackDate(htmlClickHouseEntityCurrent.getTrackDate());
						htmlFileNameClickHouseEntity.setCrawlTimestamp(htmlClickHouseEntityCurrent.getCrawlTimestamp());
						htmlFileNameClickHouseEntity.setFileName(htmlClickHouseEntityCurrent.getCrawlerResponse().getFile_name());
						htmlFileNameClickHouseEntity.setSign(IConstants.CLICKHOUSE_SIGN_POSITIVE_1);
						htmlFileNameClickHouseEntityList.add(htmlFileNameClickHouseEntity);
					}

					if (BooleanUtils.isTrue(isContentGuardCrawl)) {
						if (htmlClickHouseEntityCurrent.getCrawlerResponse() != null) {
							if (httpStatusCode == 200 && StringUtils.isBlank(htmlClickHouseEntityCurrent.getCrawlerResponse().getFile_name())) {
								log.error("contentGuardCrawl error--S3 filename not available, queueName= {}, url = {}", queueName, url);
							}
						} else {
							log.error("contentGuardCrawl error--crawlerResponse is null, queueName= {}, url = {}", queueName, url);
						}
					}

					messageToBeRemovedList.add(message);

					if (BooleanUtils.isTrue(isContentGuardCrawl) && crawlFrequencyType == IConstants.FREQUENCY_TYPE_UPLOADED) {
						hashCode = CrawlerUtils.getInstance().getMd5HashCode(StringUtils.trim(urlString));
						totalUrlsUpdated = contentGuardUrlDAO.updateCrawlStatus(domainId, hashCode, IConstants.CRAWL_STATUS_CRAWLED);
						if (totalUrlsUpdated == 0) {
							log.error("contentGuardCrawl error--content_guard_url not updated. ip={},queueName={},url= {}", ip, queueName, urlString);
						}
					}

					log.info("crawlHtml final results: domainId= {}, url= {}, httpStatusCode= {}, region= {}, isJavascriptCrawler= {}, message elapsed (sec.)= {}",
							domainId, urlString, httpStatusCode, region, isJavascriptCrawler, (System.currentTimeMillis() - messageStartTimestamp) / 1000);

				}
				// when the URL cannot be crawled.....
				else {
					log.error("crawlHtml error--URL cannot be crawled, queueName= {}, url = {}", queueName, url);
					messageToBeRemovedList.add(message);
				}
			} catch (Exception e) {
				log.error("crawlHtml error--URL cannot be crawled, queueName= {}, url = {}, exception: {}", queueName, url, e.getMessage(), e);
				break;
			}
		}

		prepareToMaintainDataStores(ip, queueName, htmlClickHouseEntityList, queueUrl, messageToBeRemovedList, htmlFileNameClickHouseEntityList);
	}

	private List<HtmlBigData> filterExistBigData(List<HtmlBigData> htmlBigDataList) {
		List<HtmlBigData> htmlBigDataListToCreate = new ArrayList<>(htmlBigDataList.size());
		// need to filter bigData md5 already exist in dis_html_big_data table to avoid duplicate records on track_date
		final Map<Date, List<HtmlBigData>> groupByTrackDate = htmlBigDataList.stream().collect(Collectors.groupingBy(HtmlBigData::getTrackDate, Collectors.toList()));
		for (Map.Entry<Date, List<HtmlBigData>> e : groupByTrackDate.entrySet()) {
			final List<HtmlBigData> bigDataListByTrackDate = e.getValue();
			if (!bigDataListByTrackDate.isEmpty()) {
				final String trackDate = DateFormatUtils.format(e.getKey(), IConstants.DATE_FORMAT_YYYY_MM_DD);
				final List<String> urlMurmurHashList = bigDataListByTrackDate.parallelStream().map(HtmlBigData::getUrlMurmurHash)
						.distinct().collect(Collectors.toList());
				// batch query to check if the md5 already exist in dis_html_big_data table, size = 1000
				ArrayList<HtmlBigData> htmlBigDataListExist = new ArrayList<>();
				int queryBatchSize = 1024;
				List<String> queryList = new ArrayList<>(queryBatchSize);
				for (String urlMurmurHash : urlMurmurHashList) {
					queryList.add(urlMurmurHash);
					if (queryList.size() == queryBatchSize) {
						htmlBigDataListExist.addAll(bigDataClickHouseDAO.queryMurmurHashAndMd5ByTrackDateInUrlList(trackDate, queryList));
						queryList = new ArrayList<>(queryBatchSize);
					}
				}
				if (!queryList.isEmpty()) {
					htmlBigDataListExist.addAll(bigDataClickHouseDAO.queryMurmurHashAndMd5ByTrackDateInUrlList(trackDate, queryList));
				}
				final Set<String> existMd5Set = htmlBigDataListExist.parallelStream().map(htmlBigData1 -> htmlBigData1.getUrlMurmurHash() + "::" + htmlBigData1.getMd5()).collect(Collectors.toSet());
				final List<HtmlBigData> bigDataList = bigDataListByTrackDate.parallelStream().filter(bigData -> !existMd5Set.contains(bigData.getUrlMurmurHash() + "::" + bigData.getMd5())).collect(Collectors.toList());
				htmlBigDataListToCreate.addAll(bigDataList);
				log.info("filterExistBigData: trackDate= {}, existMd5Set.size= {}, after filter bigDataList.size= {}", trackDate, existMd5Set.size(), bigDataList.size());
			}
		}
		return htmlBigDataListToCreate;
	}

	private boolean checkIfDefaultToRespCode999(String host, HtmlClickHouseEntity htmlClickHouseEntityPrevious) {

		boolean isDefaultToRespCode999 = false;
		Integer numberOfResponseCode999 = 0;

		// and there are eight response code 999 or more
		if (hostNameHttpStatus999Map.containsKey(host)) {
			numberOfResponseCode999 = hostNameHttpStatus999Map.get(host);
		} else {
			numberOfResponseCode999 = 0;
		}
		if (numberOfResponseCode999 == null) {
			numberOfResponseCode999 = 0;
		}
		if (numberOfResponseCode999.intValue() >= 8 && htmlClickHouseEntityPrevious != null && htmlClickHouseEntityPrevious.getHttpStatusCode() != null
				&& htmlClickHouseEntityPrevious.getHttpStatusCode().intValue() == IConstants.HTTP_STATUS_CODE_999) {
			isDefaultToRespCode999 = true;
		} else {
			nextCompetitorHostWithRespCode999:
			for (String competitorHostWithRespCode999 : IConstants.COMPETITOR_HOSTS_WITH_RESP_CODE_999_ARRAY) {
				if (StringUtils.equalsIgnoreCase(host, competitorHostWithRespCode999)) {
					isDefaultToRespCode999 = true;
					break;
				}
			}
		}
		return isDefaultToRespCode999;
	}

	private HtmlClickHouseEntity getPreviousHtmlClickHouseEntity(UrlMetricsEntityV3 urlMetricsEntityV3) {
		HtmlClickHouseEntity htmlClickHouseEntity = null;
		CrawlerResponse crawlerResponse = null;
		try {
			if (urlMetricsEntityV3 != null && urlMetricsEntityV3.isTargetUrlHtmlDailyDataInd()) {
				if (StringUtils.isNotBlank(urlMetricsEntityV3.getTargetUrlHtmlTrackDate()) && StringUtils.isNotBlank(urlMetricsEntityV3.getResponse_code())) {
					htmlClickHouseEntity = new HtmlClickHouseEntity();
					htmlClickHouseEntity
							.setTrackDate(DateUtils.parseDate(urlMetricsEntityV3.getTargetUrlHtmlTrackDate(), new String[]{IConstants.DATE_FORMAT_YYYY_MM_DD}));
					crawlerResponse = new CrawlerResponse();
					crawlerResponse.setResponse_code(urlMetricsEntityV3.getResponse_code());
					htmlClickHouseEntity.setCrawlerResponse(crawlerResponse);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return htmlClickHouseEntity;
	}

	private void pause(String urlString, int httpStatusCode) {
		int pauseTimeInMilliseconds = 0;
		boolean isPause = false;
		if (httpStatusCode >= 900) {
			return;
		}
		if (crawlType == IConstants.CRAWL_TYPE_TARGET_URL_HTML || crawlType == IConstants.CRAWL_TYPE_CONTENT_GUARD) {
			pauseTimeInMilliseconds = IConstants.RETRY_HTTP_STATUS_403_5XX_WAIT_MILLISECONDS_TARGET_URL;
		} else if (crawlType == IConstants.CRAWL_TYPE_COMPETITOR_URL_HTML) {
			isPause = true;
			for (String skipPauseCompetitorDomain : IConstants.SKIP_PAUSE_COMP_DOMAINS) {
				if (StringUtils.containsIgnoreCase(urlString, skipPauseCompetitorDomain)) {
					isPause = false;
				}
			}
			if (isPause) {
				pauseTimeInMilliseconds = IConstants.RETRY_HTTP_STATUS_403_5XX_WAIT_MILLISECONDS_COMPETITOR_URL;
			}
		}

		log.warn("pause for {} (ms.),queueName={},skip urlString= {},current response code={}", pauseTimeInMilliseconds, queueName, urlString, httpStatusCode);
		try {
			Thread.sleep(pauseTimeInMilliseconds);
		} catch (InterruptedException e1) {
			e1.printStackTrace();
		}
	}

	private void prepareToMaintainDataStores(String ip, String queueName, List<HtmlClickHouseEntity> htmlClickHouseEntityList, String queueUrl,
	                                         List<Message> messageToBeRemovedList, List<HtmlFileNameClickHouseEntity> htmlFileNameClickHouseEntityList) throws Exception {

		// calculate current timestamp in yyyymmddhhmm format (ie. to the minute)
		String currentTimestampString = DateFormatUtils.format(new Date(), IConstants.DATE_FORMAT_YYYYMMDDHHMM);

		// cache the data to be created and the messages to be deleted for the current timestamp (ie. to the minute)
		CrawlerUtils.getInstance().cacheDataToBeCreatedMessagesToBeDeleted(currentTimestampString, htmlClickHouseEntityList, queueUrl, messageToBeRemovedList,
				htmlFileNameClickHouseEntityList);

		// when all the cached data created before one minute ago exceeded batch creation size
		if (checkIfReadyToMaintainDataStores(ip, queueName, crawlType, currentTimestampString)) {
			CrawlerUtils.getInstance().maintainDataStores(ip, queueName, crawlType, currentTimestampString, messagesPerIteration);
		}
	}

	private Boolean checkIfReadyToMaintainDataStores(String ip, String queueName, int crawlType, String currentTimestampString) throws Exception {
		Boolean isReadyToCreateData = false;
		int batchCreationSize = 0;
		int totalRecordsCached = 0;
		if (crawlType == IConstants.CRAWL_TYPE_TARGET_URL_HTML) {
			batchCreationSize = TargetUrlHtmlClickHouseDAO.getInstance().getBatchCreationSize();
		} else if (crawlType == IConstants.CRAWL_TYPE_CONTENT_GUARD) {
			batchCreationSize = ContentGuardClickHouseDAO.getInstance().getBatchCreationSize();
		} else if (crawlType == IConstants.CRAWL_TYPE_COMPETITOR_URL_HTML) {
			batchCreationSize = CompetitorUrlHtmlClickHouseDAO.getInstance().getBatchCreationSize();
		}

		// calculate total records cached before current timestamp
		for (String cachedTimestampString : PoliteCrawl.getTimestampHtmlClickHouseEntityListMap().keySet()) {
			if (!StringUtils.equalsIgnoreCase(currentTimestampString, cachedTimestampString)) {
				if (PoliteCrawl.getTimestampHtmlClickHouseEntityListMap().get(cachedTimestampString) != null
						&& PoliteCrawl.getTimestampHtmlClickHouseEntityListMap().get(cachedTimestampString).size() > 0) {
					totalRecordsCached = totalRecordsCached + PoliteCrawl.getTimestampHtmlClickHouseEntityListMap().get(cachedTimestampString).size();
				}
			}
		}

		// when total cached records >= batch creation size
		if (totalRecordsCached >= batchCreationSize) {
			isReadyToCreateData = true;
		}
		return isReadyToCreateData;
	}

	private String getQueueUrl() throws Exception {
		if (StringUtils.isBlank(queueUrl)) {
			queueUrl = SQSUtils.getInstance().createQueue(queueName);
		}
		return queueUrl;
	}

	// return either
	// 1) fully instantiated HtmlClickHouseEntity when crawl response can be obtained
	// 2) null when URL cannot be crawled or exception
	// 3) partially instantiated HtmlClickHouseEntity when scrapyCrawlerResponse.getExceptionMessage() ILLEGAL_STATE_EXCEPTION or GATEWAY_EXCEPTION

	private HtmlClickHouseEntity invokePageCrawlerApi(String urlString, List<AdditionalContentEntity> additionalContentEntityList, String userAgent,
	                                                  Date currentTrackDate, boolean isJavascriptCrawler, Integer javascriptTimeoutInSecond, String reversedUrlDomain, boolean isStoreHtml, String s3Location,
	                                                  Date crawlTimestamp, String s3FileName, String region) {
		//FormatUtils.getInstance().logMemoryUsage("invokePageCrawlerApi() begins. ip=" + ip + ",queueName=" + queueName + ",urlString=" + urlString + ",isStoreHtml="
		//      + isStoreHtml + ",s3Location=" + s3Location + ",crawlTimestamp=" + crawlTimestamp + ",s3FileName=" + s3FileName + ",region=" + region);
		HtmlClickHouseEntity htmlClickHouseEntity;
		ScrapyCrawlerResponse scrapyCrawlerResponse;
		CrawlerResponse crawlerResponse;
		boolean isRequiredJavaUrlEncoder = false;

		try {
			// competitor URL starts with 'https://www.remax.com' and , return htmlClickHouseEntity with HTTP status code 301
			if (crawlType == IConstants.CRAWL_TYPE_COMPETITOR_URL_HTML && StringUtils.startsWithIgnoreCase(urlString, IConstants.HTTPS_WWW_REMAX_COM)) {
				log.warn("competitor URL starts with 'https://www.remax.com' invokePageCrawlerApi() skipped, ip={},queueName={},urlString={},assigning HTTP status code 301.", ip, queueName, urlString);
				return getHtmlClickHouseEntityWithHttpStatusCode301(urlString, currentTrackDate, crawlTimestamp);
			}
			// URL ends with '.pdf', '.doc', etc., return htmlClickHouseEntity with HTTP status code 901
			if (!CrawlerUtils.getInstance().isCrawlable(urlString)) {
				log.warn("invokePageCrawlerApi() skipped when URL ends with '.pdf', '.doc', etc., ip={},queueName={},urlString={}", ip, queueName, urlString);
				return getHtmlClickHouseEntityWithHttpStatusCode901(urlString, currentTrackDate, crawlTimestamp);
			}
			// URL not starts with http:// and https://, return htmlClickHouseEntity with HTTP status code 901
			else if ((!StringUtils.startsWith(urlString, IConstants.HTTP_COLON_SLASH_SLASH)
					&& !StringUtils.startsWith(urlString, IConstants.HTTPS_COLON_SLASH_SLASH))) {
				log.warn("invokePageCrawlerApi() skipped when URL not starts with http:// and https://, ip={},queueName={},urlString={}", ip, queueName, urlString);
				return getHtmlClickHouseEntityWithHttpStatusCode901(urlString, currentTrackDate, crawlTimestamp);
			}
			String trimmedUrlString = StringUtils.trimToEmpty(urlString);
			DecodedEncodedUrlValueObject decodedEncodedUrlValueObject = CrawlerUtils.getDecodedAndEncodedUrlString(trimmedUrlString, isRequiredJavaUrlEncoder);
			if (crawlType == IConstants.CRAWL_TYPE_TARGET_URL_HTML) {
				final ScrapyCrawlerRequest scrapyCrawlerRequest = CrawlerUtils.getScrapyCrawlerRequest(domainId, decodedEncodedUrlValueObject.getDecodedUrlString(), userAgent, additionalContentEntityList,
						isJavascriptCrawler, javascriptTimeoutInSecond, pageCrawlerApiRequestHeaders, isStoreHtml, region);
				final String requestJson = gson.toJson(scrapyCrawlerRequest, ScrapyCrawlerRequest.class);
				scrapyCrawlerResponse = this.targetUrlCrawlWithRetry(decodedEncodedUrlValueObject.getEncodedUrlString(), requestJson);
			} else {
				scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(ip, queueName, decodedEncodedUrlValueObject.getEncodedUrlString(),
						userAgent, additionalContentEntityList, isJavascriptCrawler, javascriptTimeoutInSecond, crawlType, pageCrawlerApiRequestHeaders,
						isStoreHtml, s3Location, isResponseAsHtml, region);
			}
			if (scrapyCrawlerResponse == null) {
				log.error("invokePageCrawlerApi() error--ip={},queueName={},urlString={}, scrapyCrawlerResponse is null.", ip, queueName, urlString);
				return getHtmlClickHouseEntityWithHttpStatusCode999(urlString, currentTrackDate, crawlTimestamp);
			}
			if (StringUtils.containsIgnoreCase(scrapyCrawlerResponse.getExceptionMessage(), IConstants.ILLEGAL_STATE_EXCEPTION_MSG)
					|| StringUtils.containsIgnoreCase(scrapyCrawlerResponse.getExceptionMessage(), IConstants.GATEWAY_EXCEPTION_MSG)
					|| StringUtils.containsIgnoreCase(scrapyCrawlerResponse.getExceptionMessage(), IConstants.PAGE_CRAWLER_API_EXCEPTION_MSG)) {
				log.error("invokePageCrawlerApi() error--ip={},queueName={},urlString={}, scrapyCrawlerResponse.getExceptionMessage()={}", ip, queueName, urlString, scrapyCrawlerResponse.getExceptionMessage());
				return getHtmlClickHouseEntityWithHttpStatusCode999(urlString, currentTrackDate, crawlTimestamp);
			}
			crawlerResponse = scrapyCrawlerResponse.getCrawlerResponse();
			if (crawlerResponse == null) {
				log.error("invokePageCrawlerApi() error--ip={},queueName={},urlString={}, crawlerResponse is null.", ip, queueName, urlString);
				return getHtmlClickHouseEntityWithHttpStatusCode999(urlString, currentTrackDate, crawlTimestamp);
			}
			htmlClickHouseEntity = new HtmlClickHouseEntity();
			// crawled data for competitor URLs is stored by domain name
			if (crawlType == IConstants.CRAWL_TYPE_COMPETITOR_URL_HTML) {
				htmlClickHouseEntity.setUrlDomain(reversedUrlDomain);
			}
			// crawled data for target URL or Link Clarity is stored by domain ID
			else {
				htmlClickHouseEntity.setDomainId(domainId);
			}
			htmlClickHouseEntity.setUrl(urlString);
			htmlClickHouseEntity.setTrackDate(currentTrackDate);
			//if (isDebug == true) {
			//  htmlClickHouseEntity.setTrackDate(DateUtils.addDays(new Date(), -1));
			//}
			htmlClickHouseEntity.setCrawlerResponse(crawlerResponse);
			if (crawlTimestamp != null) {
				htmlClickHouseEntity.setTrackDate(DateUtils.truncate(crawlTimestamp, Calendar.DAY_OF_MONTH));
				htmlClickHouseEntity.setCrawlTimestamp(crawlTimestamp);
			} else {
				htmlClickHouseEntity.setCrawlTimestamp(new Date());
				//if (isDebug == true) {
				//  htmlClickHouseEntity.setCrawlTimestamp(DateUtils.addDays(htmlClickHouseEntity.getCrawlTimestamp(), -1));
				//}
			}
			final String fileName = crawlerResponse.getFile_name();
			if (StringUtils.isEmpty(fileName)) {
				htmlClickHouseEntity.getCrawlerResponse().setFile_name(s3FileName);
			}
			htmlClickHouseEntity.setInternalLinkCount(CrawlerUtils.getInstance().getInternalLinkCount(trimmedUrlString, crawlerResponse));
			htmlClickHouseEntity.setSign(IConstants.CLICKHOUSE_SIGN_POSITIVE_1);
			if (htmlClickHouseEntity.getHttpStatusCode() == null) {
				log.error("invokePageCrawlerApi() error--ip={},queueName={},urlString={}, htmlClickHouseEntity.getHttpStatusCode() is null.", ip, queueName, urlString);
				return getHtmlClickHouseEntityWithHttpStatusCode999(urlString, currentTrackDate, crawlTimestamp);
			}
			if (htmlClickHouseEntity.getHttpStatusCode() == 200) {
				// aggregate page analysis results
				PageAnalysisResult[] pageAnalysisResultArray = CrawlerUtils.getInstance().getPageAnalysisResultArray(crawlerResponse);
				if (pageAnalysisResultArray != null && pageAnalysisResultArray.length > 0) {
					htmlClickHouseEntity.setPageAnalysisResultArray(pageAnalysisResultArray);
					htmlClickHouseEntity
							.setPageAnalysisResultsReverse(CrawlerUtils.getInstance().reversePageAnalysisResults(pageAnalysisResultArray));
					htmlClickHouseEntity.setPageAnalysisFragmentsArray(CrawlerUtils.getInstance().getPageAnalysisFragmentsArray(crawlerResponse));
				}
			}
			htmlClickHouseEntity.setWeekOfYear(CommonUtils.calculateWeekOfYear(currentTrackDate));
			return htmlClickHouseEntity;
		} catch (Exception e) {
			log.error("invokePageCrawlerApi() error--ip={},queueName={},urlString={}, e.getMessage()={}", ip, queueName, urlString, e.getMessage());
			return null;
		}
		//FormatUtils.getInstance()
		//      .logMemoryUsage("invokePageCrawlerApi() ends. ip=" + ip + ",queueName=" + queueName + ",urlString=" + urlString + ",user Agent="
		//              + (StringUtils.equalsIgnoreCase(userAgent, PoliteCrawl.defaultUserAgent) == true ? "default" : userAgent) + ",HTTP status code="
		//              + (htmlClickHouseEntity != null ? htmlClickHouseEntity.getHttpStatusCode() : "null") + ",elapsed (sec.)="
		//              + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private ScrapyCrawlerResponse targetUrlCrawlWithRetry(String encodedUrlString, String requestJson) {
		ScrapyCrawlerResponse scrapyCrawlerResponse = new ScrapyCrawlerResponse();
		scrapyCrawlerResponse.setUrl(encodedUrlString);
		int retryCount = 0;
		while (retryCount <= IConstants.MAX_SCRAPY_API_RETRY_COUNT) {
			HttpEntity httpEntity = null;
			try {
				final HttpPost httpPost = new HttpPost(apiEndpoint);
				httpPost.setEntity(new StringEntity(requestJson, IConstants.UTF_8));
				final int apiResponseCode;
				final String httpEntityString;
				try (CloseableHttpResponse response = CrawlerUtils.httpClient.execute(httpPost)) {
					apiResponseCode = response.getStatusLine().getStatusCode();
					httpEntity = response.getEntity();
                    httpEntityString = EntityUtils.toString(httpEntity);
                }
				CrawlerUtils.writeResponseToFileAsync(this.fileName, requestJson, apiResponseCode, retryCount, httpEntityString);
				scrapyCrawlerResponse.setStatus(apiResponseCode);
				final boolean statusCode5xx = apiResponseCode >= IConstants.HTTP_STATUS_CODE_500 && apiResponseCode < IConstants.HTTP_STATUS_CODE_600;
				if (statusCode5xx) {
					log.error("Bad Gateway status code = {}, retryCount = {}", apiResponseCode, retryCount);
					throw new GatewayException(IConstants.GATEWAY_EXCEPTION_MSG);
				}
				CrawlerResponse crawlerResponse;
				if (apiResponseCode == IConstants.HTTP_STATUS_CODE_200) {
					crawlerResponse = gson.fromJson(httpEntityString, CrawlerResponse.class);
					final int urlStatusCode = NumberUtils.toInt(crawlerResponse.getResponse_code());
					// we should apply retry logic for the following response codes:
					//408 (Request Timeout)
					//429 (Too Many Requests)
					//all 5xx (Server Errors) — these could be temporary issues.
					//
					//3 times retry with interval of 1minute, 3minutes, 5 minutes.
					final boolean urlStatusCode5xx = urlStatusCode >= IConstants.HTTP_STATUS_CODE_500 && urlStatusCode < IConstants.HTTP_STATUS_CODE_600;
					if (urlStatusCode5xx || urlStatusCode == 408) {
						if (retryCount < IConstants.MAX_SCRAPY_API_RETRY_COUNT) {
							retryCount++;
							this.sleepWithRetry(retryCount);
							continue;
						}
					}
					scrapyCrawlerResponse.setStatus(urlStatusCode);
					scrapyCrawlerResponse.setCrawlerResponse(crawlerResponse);
					break;
				} else {
					if (StringUtils.startsWithIgnoreCase(httpEntityString, IConstants.HTML_SOURCE_PREFIX)) {
						crawlerResponse = new CrawlerResponse();
						crawlerResponse.setResponse_code(String.valueOf(apiResponseCode));
					} else {
						crawlerResponse = gson.fromJson(httpEntityString, CrawlerResponse.class);
					}
					scrapyCrawlerResponse.setCrawlerResponse(crawlerResponse);
				}
				break;
			} catch (GatewayException e) {
				if (retryCount == IConstants.MAX_SCRAPY_API_RETRY_COUNT) {
					scrapyCrawlerResponse.setExceptionMessage(e.getMessage());
					break;
				} else {
					retryCount++;
					this.sleepWithRetry(retryCount);
				}
			} catch (Exception e) {
				log.error("targetUrlCrawlWithRetry() error--retryCount={}, e.getMessage()={}", retryCount, e.getMessage());
				if (retryCount == IConstants.MAX_SCRAPY_API_RETRY_COUNT) {
					scrapyCrawlerResponse.setExceptionMessage(e.getMessage());
					break;
				} else {
					log.error("catch exception, domainId= {}, encodedUrlString= {}, retryCount={}, e.getMessage()={}", domainId, encodedUrlString, retryCount, e.getMessage());
					retryCount++;
					this.sleepWithRetry(retryCount);
				}
			} finally {
				EntityUtils.consumeQuietly(httpEntity);
			}
		}
		return scrapyCrawlerResponse;
	}

	private void sleepWithRetry(int retryCount) {
		// 3 times retry with interval of 1minute, 3minutes, 5 minutes.
		try {
			int sleepTimeInMilliseconds = 0;
			if (retryCount == 1) {
				sleepTimeInMilliseconds = 60_000;
			} else if (retryCount == 2) {
				sleepTimeInMilliseconds = 180_000;
			} else if (retryCount == 3) {
				sleepTimeInMilliseconds = 300_000;
			}
			log.warn("sleepWithRetry() retryCount={}, sleepTimeInMinutes={}", retryCount, sleepTimeInMilliseconds / 1000 / 60);
			Thread.sleep(sleepTimeInMilliseconds);
		} catch (InterruptedException e) {
			log.error("sleepWithRetry() error--retryCount={}, e.getMessage()={}", retryCount, e.getMessage());
		}
	}

	private HtmlClickHouseEntity getHtmlClickHouseEntityWithHttpStatusCode403(String urlString, Date currentTrackDate, Date crawlTimestamp) {
		return getHtmlClickHouseEntityWithSpecialHttpStatusCode(urlString, currentTrackDate, IConstants.HTTP_STATUS_403, crawlTimestamp);
	}

	private HtmlClickHouseEntity getHtmlClickHouseEntityWithHttpStatusCode301(String urlString, Date currentTrackDate, Date crawlTimestamp) {
		return getHtmlClickHouseEntityWithSpecialHttpStatusCode(urlString, currentTrackDate, IConstants.HTTP_STATUS_301, crawlTimestamp);
	}

	private HtmlClickHouseEntity getHtmlClickHouseEntityWithHttpStatusCode429(String urlString, Date currentTrackDate, Date crawlTimestamp) {
		return getHtmlClickHouseEntityWithSpecialHttpStatusCode(urlString, currentTrackDate, IConstants.HTTP_STATUS_429, crawlTimestamp);
	}

	private HtmlClickHouseEntity getHtmlClickHouseEntityWithHttpStatusCode901(String urlString, Date currentTrackDate, Date crawlTimestamp) {
		return getHtmlClickHouseEntityWithSpecialHttpStatusCode(urlString, currentTrackDate, IConstants.HTTP_STATUS_901, crawlTimestamp);
	}

	private HtmlClickHouseEntity getHtmlClickHouseEntityWithHttpStatusCode999(String urlString, Date currentTrackDate, Date crawlTimestamp) {
		return getHtmlClickHouseEntityWithSpecialHttpStatusCode(urlString, currentTrackDate, IConstants.HTTP_STATUS_999, crawlTimestamp);
	}

	private HtmlClickHouseEntity getHtmlClickHouseEntityWithSpecialHttpStatusCode(String urlString, Date currentTrackDate, String specialHttpStatusCode,
																				  Date crawlTimestamp) {
		HtmlClickHouseEntity htmlClickHouseEntity = null;
		CrawlerResponse crawlerResponse = null;
		String reversedUrlDomain = null;
		try {
			crawlerResponse = new CrawlerResponse();
			crawlerResponse.setResponse_code(specialHttpStatusCode);

			htmlClickHouseEntity = new HtmlClickHouseEntity();

			// crawled data for competitor URLs is stored by domain name
			if (crawlType == IConstants.CRAWL_TYPE_COMPETITOR_URL_HTML) {
				reversedUrlDomain = CrawlerUtils.getInstance().getReversedUrlDomain(ip, queueName, urlString);
				if (StringUtils.isNotBlank(reversedUrlDomain)) {
					htmlClickHouseEntity.setUrlDomain(reversedUrlDomain);
				} else {
					return null;
				}
			}
			// crawled data for target URL or Link Clarity is stored by domain ID
			else {
				htmlClickHouseEntity.setDomainId(domainId);
			}

			htmlClickHouseEntity.setUrl(urlString);
			htmlClickHouseEntity.setTrackDate(currentTrackDate);
			if (crawlTimestamp != null) {
				htmlClickHouseEntity.setTrackDate(DateUtils.truncate(crawlTimestamp, Calendar.DAY_OF_MONTH));
				htmlClickHouseEntity.setCrawlTimestamp(crawlTimestamp);
			} else {
				htmlClickHouseEntity.setCrawlTimestamp(new Date());
			}
			htmlClickHouseEntity.setCrawlerResponse(crawlerResponse);
			htmlClickHouseEntity.setSign(IConstants.CLICKHOUSE_SIGN_POSITIVE_1);
			htmlClickHouseEntity.setWeekOfYear(CommonUtils.calculateWeekOfYear(currentTrackDate));
		} catch (Exception e) {
			log.error("getHtmlClickHouseEntityWithSpecialHttpStatusCode() error, e={}", e.getMessage());
		}
		return htmlClickHouseEntity;
	}

	private void non2xxHtmlEntity(HtmlClickHouseEntity htmlClickHouseEntityCurrent) {
		final Integer httpStatusCode = htmlClickHouseEntityCurrent.getHttpStatusCode();
		if (httpStatusCode > 299) {
			totalPrevious4xxCurrent2xx3xx++;
			final Date trackDate = htmlClickHouseEntityCurrent.getTrackDate();
			PoliteCrawl.executorService.execute(() -> {
				final String urlMurmurHash = htmlClickHouseEntityCurrent.getUrlMurmurHash();
				final String s3Key = String.format("%s/%s/%s", domainId, NumberUtils.toInt(DateFormatUtils.format(trackDate, IConstants.DATE_FORMAT_YYYYMMDD)), urlMurmurHash);
				int retry = 0;
				while (retry < 3) {
					try {
						PoliteCrawl.s3client.putObject(CrawlHtmlSeagateUtils.bucketName, s3Key, gson.toJson(htmlClickHouseEntityCurrent));
						break;
					} catch (Exception e) {
						log.error("store 3xx,4xx,5xx htmls into seagate error--domainId = {}, currentTrackDate = {}, urlMurmurHash = {}, url = {}", domainId, trackDate, urlMurmurHash, htmlClickHouseEntityCurrent.getUrl(), e);
						try {
							Thread.sleep(500L * (retry + 1));
						} catch (InterruptedException ex) {
							throw new RuntimeException(ex);
						}
						retry++;
					}
				}
			});
		}
		final PoliteCrawlStateLog politeCrawlStateLog = PoliteCrawlStateLog.fromHtmlClickHouseEntity(htmlClickHouseEntityCurrent);
		politeCrawlStateLogList.add(politeCrawlStateLog);
		if (politeCrawlStateLogList.size() >= CRAWL_FAILED_URLS_LIMIT) {
			this.politeCrawlStateLogDAO.batchInsert(politeCrawlStateLogList);
		}
	}

}

