package com.actonia.polite.crawl;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import com.actonia.dao.*;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.entity.AdditionalContentEntity;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.entity.TargetUrlCustomDataClickHouseEntity;
import com.actonia.utils.CommonUtils;
import com.actonia.utils.CrawlerUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.PutMessageUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.CustomData;
import com.actonia.value.object.DecodedEncodedUrlValueObject;
import com.google.gson.Gson;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.StopWatch;

/***
 *
 * update target URL daily crawl data with historical crawl data.
 *
 */
public class UpdateTargetUrlCustomData {

	private static final int PAGE_SIZE = 15000;
	private static final Logger log = LogManager.getLogger(UpdateTargetUrlCustomData.class);
	private static final String TABLE_NAME = null;
	private static final int partitionSize = 10;
	//private Boolean isDebug = false;
	private final OwnDomainEntityDAO ownDomainEntityDAO;
	private final OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
	private final TargetUrlCrawlAdditionalContentEntityDAO targetUrlCrawlAdditionalContentEntityDAO;
	private final List<Integer> domainIdWithCustomDataList = new ArrayList<>();
	private final Gson gson = new Gson();
	private final Date targetUrlCustomDataDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
	private final int batchCreationSize;
	private final ExecutorService executorService = Executors.newFixedThreadPool(3);

	public UpdateTargetUrlCustomData() {
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
		targetUrlCrawlAdditionalContentEntityDAO = SpringBeanFactory.getBean("targetUrlCrawlAdditionalContentEntityDAO");
		try {
			batchCreationSize = TargetUrlCustomDataClickHouseDAO.getInstance().getBatchCreationSize();
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	public static void main(String[] args) {
		log.info("main() begins. java version={}, args:{}", System.getProperty("java.version"), StringUtils.join(args, ","));
		long startTimestamp = System.currentTimeMillis();
		try {
			new UpdateTargetUrlCustomData().process(args);
		} catch (Exception e) {
			log.error("Exception: ", e);
		}
		log.info("main() ends. elapsed time in sec.:{}", (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private static List<String> getDatabaseFields() {
		List<String> databaseFields = new ArrayList<>();
		databaseFields.add(IConstants.URL);
		databaseFields.add(IConstants.CRAWL_TIMESTAMP);
		databaseFields.add(IConstants.CUSTOM_DATA);
		return databaseFields;
	}

	private void process(String[] args) throws Exception {
		log.info("process() begins.");
		long totalStartTimestamp = System.currentTimeMillis();
		String domainIdsString = null;

		// initialize the cached DAOs
		TargetUrlCustomDataClickHouseDAO.getInstance();
		PutMessageUtils.getInstance();

		if (args != null && args.length >= 1) {
			// runtime parameter (optional): list of domain IDs)
			domainIdsString = args[0];
		}
		log.info("process() runtime parameter:list of domain IDs={}", domainIdsString);

		Boolean isExecDomainIdsInd = null;
		Set<Integer> runtimeDomainSet = null;

		Properties domainProperties = new Properties();
		try {
			domainProperties.load(UpdateTargetUrlCustomData.class.getResourceAsStream("/domain.properties"));
		} catch (Exception e) {
			FormatUtils.getInstance().logMemoryUsage("process() no domain.properties file found");
			return;
		}

		String execDomainIds = domainProperties.getProperty("exec.domain");
		String notExecDomainIds = domainProperties.getProperty("notexec.domain");
		if (StringUtils.isNotBlank(domainIdsString)) {
			execDomainIds = domainIdsString;
			notExecDomainIds = null;
		}
		log.info("process() execDomainIds={} \n notExecDomainIds={}", execDomainIds, notExecDomainIds);
		List<OwnDomainEntity> allOwnDomainEntityList = ownDomainEntityDAO.queryForAll();
		if (allOwnDomainEntityList == null || allOwnDomainEntityList.isEmpty()) {
			log.warn("process() allOwnDomainEntityList is empty.");
			return;
		}

		// process specific domains
		if (StringUtils.isNotBlank(execDomainIds)) {
			runtimeDomainSet = CommonUtils.getDomainSet(execDomainIds);
			isExecDomainIdsInd = true;
		}
		// do not process specific domains
		else if (StringUtils.isNotBlank(notExecDomainIds)) {
			runtimeDomainSet = CommonUtils.getDomainSet(notExecDomainIds);
			isExecDomainIdsInd = false;
		}
		// process all domains
		else {
			runtimeDomainSet = null;
			isExecDomainIdsInd = null;
		}

		List<OwnDomainEntity> filteredOwnDomainEntityList = PutMessageUtils.getInstance().filterDomains(allOwnDomainEntityList, isExecDomainIdsInd, runtimeDomainSet);
		if (filteredOwnDomainEntityList == null || filteredOwnDomainEntityList.isEmpty()) {
			log.warn("process() filteredOwnDomainEntityList is empty.");
			return;
		}

		Integer targetUrlCustomDataDateNumber = NumberUtils.toInt(DateFormatUtils.format(targetUrlCustomDataDate, IConstants.DATE_FORMAT_YYYYMMDD));
		log.info("process() targetUrlCustomDataDate={}, targetUrlCustomDataDateNumber={}", targetUrlCustomDataDate, targetUrlCustomDataDateNumber);

		// async processing for large data set - 4609
		final Future<?> future = executorService.submit(() -> processLargeData(4609));

		filteredOwnDomainEntityList = filteredOwnDomainEntityList.stream().filter(ownDomainEntity -> ownDomainEntity.getId() != 4609).collect(Collectors.toList());

		// domain level processing
		for (OwnDomainEntity ownDomainEntity : filteredOwnDomainEntityList) {
			processOneDomain(ownDomainEntity.getId());
		}

		// update 'target_url_custom_data_date' field of the 't_own_domain_setting' table
		ownDomainSettingEntityDAO.resetTargetUrlCustomDataDate();
		ownDomainSettingEntityDAO.updateTargetUrlCustomDataDate(domainIdWithCustomDataList, targetUrlCustomDataDateNumber);

		// detach all partitions before two days' ago
		detachDailyPartitions();
		log.info("process() ends. wait for large data async processing to complete. elapsed time in sec.: {}", (System.currentTimeMillis() - totalStartTimestamp) / 1000);
		future.get();
		log.info("large data async process() ends. shutdown executorService. elapsed time in sec.: {}", (System.currentTimeMillis() - totalStartTimestamp) / 1000);
		executorService.shutdown();
		log.info("process() ends. elapsed time in sec.: {}", (System.currentTimeMillis() - totalStartTimestamp) / 1000);
	}

	private void processOneDomain(int domainId) {

		List<AdditionalContentEntity> additionalContentEntityList = targetUrlCrawlAdditionalContentEntityDAO.getByDomainId(domainId);
		if (additionalContentEntityList.isEmpty()) {
			return;
		}
		log.info("processOneDomain() begins. domainId= {} targetUrlCustomDataDate={} additionalContentEntityList.size()={}", domainId, targetUrlCustomDataDate, additionalContentEntityList.size());

		final List<String> databaseFields = getDatabaseFields();
		try {
			List<HtmlClickHouseEntity> htmlClickHouseEntityList = TargetUrlHtmlClickHouseDAO.getInstance().getLatestCustomData(domainId, databaseFields, TABLE_NAME);
			if (!htmlClickHouseEntityList.isEmpty()) {
				domainIdWithCustomDataList.add(domainId);
				transferCustomData(domainId, htmlClickHouseEntityList, additionalContentEntityList);
			}
			log.info("processOneDomain() domainId= {} htmlClickHouseEntityList.size()={}", domainId, htmlClickHouseEntityList.size());
		} catch (Exception e) {
			log.error("processOneDomain() error domainId= {} ,exception: ", domainId, e);
		}

	}

	private void processLargeData(int domainId) {
		List<AdditionalContentEntity> additionalContentEntityList = targetUrlCrawlAdditionalContentEntityDAO.getByDomainId(domainId);
		if (additionalContentEntityList.isEmpty()) {
			return;
		}
		log.info("processLargeData() begins. domainId={} targetUrlCustomDataDate={} additionalContentEntityList.size()={}", domainId, targetUrlCustomDataDate, additionalContentEntityList.size());

		final List<String> databaseFields = getDatabaseFields();
		try {
			batchCreateCustomDataByPage(domainId, databaseFields, additionalContentEntityList);
		} catch (Exception e) {
			log.error("processLargeData() error domainId={} ,exception: ", domainId, e);
		}
	}


	private void batchCreateCustomDataByPage(int domainId, List<String> databaseFields, List<AdditionalContentEntity> additionalContentEntityList) throws Exception {
		for (int i = 0; i < partitionSize; i++) {
			StopWatch stopWatch = new StopWatch("batchCreateCustomDataByPage:" + domainId + "_" + i);
			stopWatch.start("query");
			List<HtmlClickHouseEntity> customDataListByPage = TargetUrlHtmlClickHouseDAO.getInstance().getLatestCustomDataByPartition(domainId, databaseFields, TABLE_NAME, partitionSize, i);
			if (customDataListByPage == null || customDataListByPage.isEmpty()) {
				break;
			}
			stopWatch.stop();
			stopWatch.start("transferCustomData");
			int finalI = i;
			executorService.submit(() -> {
				try {
					transferCustomData(domainId, customDataListByPage, additionalContentEntityList);
					stopWatch.stop();
					log.info("batchCreateCustomDataByPage() domainId= {} index={} customDataListByPage.size()={}\n{}", domainId, finalI, customDataListByPage.size(), stopWatch.prettyPrint());
				} catch (Exception e) {
					throw new RuntimeException(e);
				}
			});
		}
		domainIdWithCustomDataList.add(domainId);
	}

	private void transferCustomData(int domainId, List<HtmlClickHouseEntity> htmlClickHouseEntityList, List<AdditionalContentEntity> additionalContentEntityList) throws Exception {
		Long targetUrlCrawlAdditionalContentId;
		CustomData[] customDataArray;
		Date crawlTimestamp;
		String selector;
		TargetUrlCustomDataClickHouseEntity targetUrlCustomDataClickHouseEntity;
		String urlString;
		String json;
		int totalIdsBySelector = 0;
		int totalIdsByUrlSelector = 0;
		int totalIdsNotDetermined = 0;
		int totalCreated = 0;
		int batchCreationIndex = 1;
		List<TargetUrlCustomDataClickHouseEntity> targetUrlCustomDataClickHouseEntityList = new ArrayList<>(htmlClickHouseEntityList.size());
		for (HtmlClickHouseEntity htmlClickHouseEntity : htmlClickHouseEntityList) {
			urlString = htmlClickHouseEntity.getUrl();
			crawlTimestamp = htmlClickHouseEntity.getCrawlTimestamp();
			customDataArray = htmlClickHouseEntity.getCrawlerResponse().getCustom_data();
			if (customDataArray != null && customDataArray.length > 0) {
				for (CustomData customData : customDataArray) {
					selector = customData.getSelector();
					targetUrlCrawlAdditionalContentId = getTargetUrlCrawlAdditionalContentIdBySelector(selector, additionalContentEntityList);
					if (targetUrlCrawlAdditionalContentId != null) {
						customData.setTarget_url_crawl_additional_content_id(targetUrlCrawlAdditionalContentId);
						totalIdsBySelector++;
					} else {
						targetUrlCrawlAdditionalContentId = getTargetUrlCrawlAdditionalContentIdByUrlSelector(selector, urlString, additionalContentEntityList);
						if (targetUrlCrawlAdditionalContentId != null) {
							customData.setTarget_url_crawl_additional_content_id(targetUrlCrawlAdditionalContentId);
							totalIdsByUrlSelector++;
						} else {
							log.warn("processOneDomain() domainId={} crawlTimestamp={} urlString={} targetUrlCrawlAdditionalContentId cannot be determined.", domainId, crawlTimestamp, urlString);
							totalIdsNotDetermined++;
						}
					}
				}
				json = gson.toJson(customDataArray, CustomData[].class);
				targetUrlCustomDataClickHouseEntity = new TargetUrlCustomDataClickHouseEntity();
				targetUrlCustomDataClickHouseEntity.setDomain_id(domainId);
				targetUrlCustomDataClickHouseEntity.setUrl(urlString);
				targetUrlCustomDataClickHouseEntity.setCustom_data(json);
				targetUrlCustomDataClickHouseEntity.setCrawl_timestamp(crawlTimestamp);
				targetUrlCustomDataClickHouseEntity.setDaily_data_creation_date(targetUrlCustomDataDate);
				targetUrlCustomDataClickHouseEntityList.add(targetUrlCustomDataClickHouseEntity);
				totalCreated++;
				if (targetUrlCustomDataClickHouseEntityList.size() >= batchCreationSize) {
					TargetUrlCustomDataClickHouseDAO.getInstance().createBatch(targetUrlCustomDataClickHouseEntityList, TABLE_NAME);
					targetUrlCustomDataClickHouseEntityList.clear();
				}
			}
		}
		if (!targetUrlCustomDataClickHouseEntityList.isEmpty()) {
			TargetUrlCustomDataClickHouseDAO.getInstance().createBatch(targetUrlCustomDataClickHouseEntityList, TABLE_NAME);
		}

		log.info("processOneDomain() domainId={} totalIdsBySelector={} totalIdsByUrlSelector={} totalIdsNotDetermined={} totalCreated={}", domainId, totalIdsBySelector, totalIdsByUrlSelector, totalIdsNotDetermined, totalCreated);

	}

	private Long getTargetUrlCrawlAdditionalContentIdBySelector(String selector, List<AdditionalContentEntity> additionalContentEntityList) {
		Long targetUrlCrawlAdditionalContentId = null;
		int totalSelectorMatches = 0;
		for (AdditionalContentEntity additionalContentEntity : additionalContentEntityList) {
			if (StringUtils.equalsIgnoreCase(selector, additionalContentEntity.getSelector())) {
				totalSelectorMatches++;
				targetUrlCrawlAdditionalContentId = additionalContentEntity.getId();
			}
		}
		if (totalSelectorMatches == 1) {
			return targetUrlCrawlAdditionalContentId;
		} else {
			return null;
		}
	}

	private Long getTargetUrlCrawlAdditionalContentIdByUrlSelector(String selector, String urlString, List<AdditionalContentEntity> additionalContentEntityList)
			throws Exception {
		Long targetUrlCrawlAdditionalContentId = null;
		boolean isRequiredJavaUrlEncoder = false;
		boolean isUrlSelected;
		String trimmedUrlString = StringUtils.trimToEmpty(urlString);
		DecodedEncodedUrlValueObject decodedEncodedUrlValueObject = CrawlerUtils.getDecodedAndEncodedUrlString(trimmedUrlString, isRequiredJavaUrlEncoder);
		for (AdditionalContentEntity additionalContentEntity : additionalContentEntityList) {
			isUrlSelected = CrawlerUtils.getInstance().checkIfUrlSelected(decodedEncodedUrlValueObject.getEncodedUrlString(), additionalContentEntity);
			if (isUrlSelected) {
				targetUrlCrawlAdditionalContentId = additionalContentEntity.getId();
				break;
			}
		}
		return targetUrlCrawlAdditionalContentId;
	}

	// drop data partitions older than two days old on all cluster servers
	private void detachDailyPartitions() throws Exception {
		FormatUtils.getInstance().logMemoryUsage("detachDailyPartitions() begins.");
		Date todayDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
		Date yesterdayDate = DateUtils.addDays(todayDate, -1);
		Date twoDaysBeforeYesterdayDate = DateUtils.addDays(yesterdayDate, -2);
		Date fiveDaysBeforeYesterdayDate = DateUtils.addDays(yesterdayDate, -5);
		LocalTargetUrlCustomDataClickHouseDAO.getInstance().detachDailyPartitions(fiveDaysBeforeYesterdayDate, twoDaysBeforeYesterdayDate);
		FormatUtils.getInstance().logMemoryUsage("detachDailyPartitions() ends.");
	}
}
