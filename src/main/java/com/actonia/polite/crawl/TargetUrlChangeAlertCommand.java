package com.actonia.polite.crawl;

import com.actonia.IConstants;
import com.actonia.concurrency.BaseThreadCommand;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.content.guard.change.ChangeIndicatorEnum;
import com.actonia.dao.*;
import com.actonia.entity.*;
import com.actonia.service.AgencyInfoService;
import com.actonia.service.HtmlChangeService;
import com.actonia.service.ListChangeIndicatorsService;
import com.actonia.utils.*;
import com.actonia.value.object.*;
import com.google.gson.Gson;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

@Log4j2
public class TargetUrlChangeAlertCommand extends BaseThreadCommand {
	private String ip;
	private int domainId;
	private String domainName;
	private int alertFrequencyType;
	private TargetUrlChangeAlertDAO targetUrlChangeAlertDAO;
	private String trackDateOverride;
	private Integer crawlHourOverride;
	private AgencyInfoService agencyInfoService;
	private EmailSenderComponent emailSenderComponent;
	private boolean isDebug = false;
	private GroupTagEntityDAO groupTagEntityDAO;
	private long startingId = 0L;
	private static final String WEB_APP_LINK_TEMPLATE_WITHOUT_PAGE_TAG = "<a href=\"https://app.seoclarity.net/page-clarity/content-change.do?oid={0}&changeElement={1}&date1={2}&date2={3}\">";
	private static final String WEB_APP_LINK_TEMPLATE_WITH_PAGE_TAG = "<a href=\"https://app.seoclarity.net/page-clarity/content-change.do?oid={0}&changeElement={1}&date1={2}&date2={3}&pageTagIds={4}\">";
	private ListChangeIndicatorsService listChangeIndicatorsService;
	private ContentGuardChangeTrackingDomainSettingDAO contentGuardChangeTrackingDomainSettingDAO;
	private final Map<String, Integer> changeIndicatorIdMap;
	private static final String WEB_APP_LINK_TO_REPORT_TEMPLATE = "<a href=\"https://app.seoclarity.net/page-clarity/content-change.do?oid={0}&date1={1}&date2={2}\">";

	public TargetUrlChangeAlertCommand(String ip, OwnDomainEntity ownDomainEntity, int alertFrequencyType, String trackDateOverride, Integer crawlHourOverride,
	                                   boolean isDebug, Map<String, Integer> changeIndicatorIdMap) {
		super();
		this.ip = ip;
		this.domainId = ownDomainEntity.getId();
		this.domainName = ownDomainEntity.getDomain();
		this.alertFrequencyType = alertFrequencyType;
		this.trackDateOverride = trackDateOverride;
		this.crawlHourOverride = crawlHourOverride;
		this.changeIndicatorIdMap = changeIndicatorIdMap;
		this.targetUrlChangeAlertDAO = SpringBeanFactory.getBean("targetUrlChangeAlertDAO");
		this.agencyInfoService = SpringBeanFactory.getBean("agencyInfoService");
		this.emailSenderComponent = SpringBeanFactory.getBean("emailSenderComponent");
		this.isDebug = isDebug;
		this.groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
		this.startingId = System.currentTimeMillis();
		this.listChangeIndicatorsService = SpringBeanFactory.getBean("listChangeIndicatorsService");
		this.contentGuardChangeTrackingDomainSettingDAO = SpringBeanFactory.getBean("contentGuardChangeTrackingDomainSettingDAO");
	}

	@Override
	protected void execute() {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("execute() begins. ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",alertFrequencyType="
				+ alertFrequencyType + ",trackDateOverride=" + trackDateOverride + ",crawlHourOverride=" + crawlHourOverride + ",isDebug=" + isDebug);
		try {
			process();
		} catch (Exception e) {
			e.printStackTrace();

			StringWriter stringWriter = new StringWriter();
			e.printStackTrace(new PrintWriter(stringWriter));

			// send support alert
			String message = stringWriter.toString();
		} finally {
			CacheModleFactory.getInstance().setAliveIpAddress(ip);
		}
		FormatUtils.getInstance()
				.logMemoryUsage("execute() ends. ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",alertFrequencyType=" + alertFrequencyType
						+ ",trackDateOverride=" + trackDateOverride + ",crawlHourOverride=" + crawlHourOverride + ",isDebug=" + isDebug + " elapsed(s.)="
						+ (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	@Override
	protected void undo() throws Exception {
		// TODO Auto-generated method stub
	}

	private void process() throws Exception {
		Date todayDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
		Date yesterdayDate = DateUtils.addDays(todayDate, -1);
		String trackDateString = null;
		Calendar calendar = null;
		int currentHour = 0;
		Integer crawlHour = null;
		String startCrawlTimestampString = null;
		String endCrawlTimestampString = null;
		String startCrawlHour = null;
		String endCrawlHour = null;

		List<TargetUrlChangeAlertEntity> targetUrlChangeAlertEntityList = targetUrlChangeAlertDAO.getListByDomainAlertFrequency(domainId, alertFrequencyType);
		if (targetUrlChangeAlertEntityList != null && targetUrlChangeAlertEntityList.size() > 0) {
			// https://www.wrike.com/open.htm?id=1017850229
			targetUrlChangeAlertEntityList = transformAlerts(targetUrlChangeAlertEntityList);
			FormatUtils.getInstance()
					.logMemoryUsage("process() ip=" + ip + ",domainId=" + domainId + ",targetUrlChangeAlertEntityList.size()=" + targetUrlChangeAlertEntityList.size());

			if (alertFrequencyType == IConstants.FREQUENCY_TYPE_DAILY) {
				if (StringUtils.isNotBlank(trackDateOverride)) {
					trackDateString = trackDateOverride;
				} else {
					trackDateString = DateFormatUtils.format(yesterdayDate, IConstants.DATE_FORMAT_YYYY_MM_DD);
				}
				startCrawlHour = IConstants.CRAWL_HOUR_00;
				startCrawlTimestampString = trackDateString + IConstants.ONE_SPACE + startCrawlHour + IConstants.CRAWL_TIMESTAMP_START_OF_HOUR;
				endCrawlHour = IConstants.CRAWL_HOUR_23;
				endCrawlTimestampString = trackDateString + IConstants.ONE_SPACE + endCrawlHour + IConstants.CRAWL_TIMESTAMP_END_OF_HOUR;
			} else if (alertFrequencyType == IConstants.FREQUENCY_TYPE_HOURLY) {
				if (crawlHourOverride != null && StringUtils.isNotBlank(trackDateOverride)) {
					crawlHour = crawlHourOverride.intValue();
					trackDateString = trackDateOverride;
				} else {
					calendar = GregorianCalendar.getInstance();
					currentHour = calendar.get(Calendar.HOUR_OF_DAY);
					if (currentHour == 0) {
						crawlHour = 23;
						trackDateString = DateFormatUtils.format(yesterdayDate, IConstants.DATE_FORMAT_YYYY_MM_DD);
					} else {
						crawlHour = currentHour - 1;
						trackDateString = DateFormatUtils.format(todayDate, IConstants.DATE_FORMAT_YYYY_MM_DD);
					}
				}
				startCrawlHour = String.format(IConstants.STRING_WITH_ONE_LEADING_ZERO, crawlHour);
				startCrawlTimestampString = trackDateString + IConstants.ONE_SPACE + startCrawlHour + IConstants.CRAWL_TIMESTAMP_START_OF_HOUR;
				endCrawlHour = String.format(IConstants.STRING_WITH_ONE_LEADING_ZERO, crawlHour);
				endCrawlTimestampString = trackDateString + IConstants.ONE_SPACE + endCrawlHour + IConstants.CRAWL_TIMESTAMP_END_OF_HOUR;
			}
			FormatUtils.getInstance().logMemoryUsage("process() ip=" + ip + ",domainId=" + domainId + ",trackDateString=" + trackDateString
					+ ",startCrawlTimestampString=" + startCrawlTimestampString + ",endCrawlTimestampString=" + endCrawlTimestampString);
			for (TargetUrlChangeAlertEntity targetUrlChangeAlertEntity : targetUrlChangeAlertEntityList) {
				FormatUtils.getInstance()
						.logMemoryUsage("process() ip=" + ip + ",domainId=" + domainId + ",targetUrlChangeAlertEntity=" + targetUrlChangeAlertEntity.toString());

				//if (isDebug == true) {
				//	targetUrlChangeAlertEntity.setDetailInd(IConstants.TRUE_NUMERIC);
				//}

				// detail alert
				if (targetUrlChangeAlertEntity.getDetailInd() != null && targetUrlChangeAlertEntity.getDetailInd().intValue() == IConstants.TRUE_NUMERIC.intValue()) {
					processDetailedAlert(targetUrlChangeAlertEntity, trackDateString, startCrawlTimestampString, endCrawlTimestampString);
				}
				// summary alert
				else {
					processSummaryAlert(targetUrlChangeAlertEntity, trackDateString, startCrawlTimestampString, endCrawlTimestampString);
				}
			}
		} else {
			FormatUtils.getInstance().logMemoryUsage("process() ip=" + ip + ",domainId=" + domainId + ",targetUrlChangeAlertEntityList is empty.");
		}
	}

	private List<TargetUrlChangeAlertEntity> transformAlerts(List<TargetUrlChangeAlertEntity> targetUrlChangeAlertEntityInputList) throws Exception {
		List<TargetUrlChangeAlertEntity> targetUrlChangeAlertEntityOutputList = new ArrayList<TargetUrlChangeAlertEntity>();
		TargetUrlChangeAlertEntity targetUrlChangeAlertEntityOutput = null;
		for (TargetUrlChangeAlertEntity targetUrlChangeAlertEntityInput : targetUrlChangeAlertEntityInputList) {
			// when alert is for custom changes, no need to transform
			// ["header_nofollow_chg_ind","header_noindex_chg_ind"]
			if (targetUrlChangeAlertEntityInput.getIndicatorFlag() == IConstants.INDICATOR_FLAG_CUSTOM) {
				targetUrlChangeAlertEntityOutputList.add(targetUrlChangeAlertEntityInput.clone());
			}
			// when alert is for all changes 
			else if (targetUrlChangeAlertEntityInput.getIndicatorFlag() == IConstants.INDICATOR_FLAG_ALL) {
				targetUrlChangeAlertEntityOutput = transformAllChangesAlert(targetUrlChangeAlertEntityInput);
				if (targetUrlChangeAlertEntityOutput != null) {
					targetUrlChangeAlertEntityOutputList.add(targetUrlChangeAlertEntityOutput);
				}
			}
			// when alert is for critical changes 
			else if (targetUrlChangeAlertEntityInput.getIndicatorFlag() == IConstants.INDICATOR_FLAG_CRITICAL) {
				targetUrlChangeAlertEntityOutput = transformCriticalChangesAlert(targetUrlChangeAlertEntityInput);
				if (targetUrlChangeAlertEntityOutput != null) {
					targetUrlChangeAlertEntityOutputList.add(targetUrlChangeAlertEntityOutput);
				}
			}
		}
		return targetUrlChangeAlertEntityOutputList;
	}

	private TargetUrlChangeAlertEntity transformAllChangesAlert(TargetUrlChangeAlertEntity targetUrlChangeAlertEntityInput) throws Exception {
		FormatUtils.getInstance()
				.logMemoryUsage("transformAllChangesAlert() ip=" + ip + " begins,targetUrlChangeAlertEntityInput=" + targetUrlChangeAlertEntityInput.toString());
		TargetUrlChangeAlertEntity targetUrlChangeAlertEntityOutput = null;
		List<String> customChangeIndicatorList = null;
		String[] stringArray = null;
		String json = null;
		List<String> changeIndicatorToBeSkippedList = getDomainChangeIndicatorsNotInAllChangeIndicators(targetUrlChangeAlertEntityInput.getDomainId());
		if (changeIndicatorToBeSkippedList != null && changeIndicatorToBeSkippedList.size() > 0) {
			customChangeIndicatorList = getCustomChangesFromAllChanges(changeIndicatorToBeSkippedList);
			if (customChangeIndicatorList != null && customChangeIndicatorList.size() > 0) {
				targetUrlChangeAlertEntityOutput = targetUrlChangeAlertEntityInput.clone();
				targetUrlChangeAlertEntityOutput.setIndicatorFlag(IConstants.INDICATOR_FLAG_CUSTOM);
				stringArray = customChangeIndicatorList.toArray(new String[0]);
				json = new Gson().toJson(stringArray, String[].class);
				targetUrlChangeAlertEntityOutput.setCustomIndicators(json);
			}
			// when all changes indicator are skipped
			else {
				targetUrlChangeAlertEntityOutput = null;
			}
		}
		// no need to transform when domain does not skip any change indicators
		else {
			targetUrlChangeAlertEntityOutput = targetUrlChangeAlertEntityInput.clone();
		}
		if (targetUrlChangeAlertEntityOutput != null) {
			FormatUtils.getInstance()
					.logMemoryUsage("transformAllChangesAlert() ip=" + ip + " ends,targetUrlChangeAlertEntityOutput=" + targetUrlChangeAlertEntityOutput.toString());
		} else {
			FormatUtils.getInstance().logMemoryUsage("transformAllChangesAlert() ip=" + ip + " ends,targetUrlChangeAlertEntityOutput is null.");
		}
		return targetUrlChangeAlertEntityOutput;
	}

	private List<String> getDomainChangeIndicatorsNotInAllChangeIndicators(int domainId) {
		FormatUtils.getInstance().logMemoryUsage("getDomainChangeIndicatorsNotInAllChangeIndicators() ip=" + ip + " begins,domainId=" + domainId);
		List<String> changeIndicatorToBeSkippedList = new ArrayList<String>();
		List<String> allChangeTrackingIndicatorList = null;
		boolean isToBeSkipped = false;
		List<ContentGuardChangeTrackingDomainSettingEntity> contentGuardChangeTrackingDomainSettingEntityList = contentGuardChangeTrackingDomainSettingDAO
				.getList(domainId);
		if (contentGuardChangeTrackingDomainSettingEntityList != null && contentGuardChangeTrackingDomainSettingEntityList.size() > 0) {
			allChangeTrackingIndicatorList = ContentGuardUtils.getInstance().getChangeTrackingIndicatorList();
			for (String changeIndicator : allChangeTrackingIndicatorList) {
				isToBeSkipped = true;
				nextContentGuardChangeTrackingDomainSettingEntity: for (ContentGuardChangeTrackingDomainSettingEntity contentGuardChangeTrackingDomainSettingEntity : contentGuardChangeTrackingDomainSettingEntityList) {
					if (StringUtils.equalsIgnoreCase(changeIndicator, contentGuardChangeTrackingDomainSettingEntity.getIndicator())) {
						isToBeSkipped = false;
						break nextContentGuardChangeTrackingDomainSettingEntity;
					}
				}
				if (isToBeSkipped == true) {
					changeIndicatorToBeSkippedList.add(changeIndicator);
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("getDomainChangeIndicatorsNotInAllChangeIndicators() ip=" + ip + " ends,domainId=" + domainId
				+ ",changeIndicatorToBeSkippedList=" + changeIndicatorToBeSkippedList.toString());
		return changeIndicatorToBeSkippedList;
	}

	private List<String> getCustomChangesFromAllChanges(List<String> changeIndicatorToBeSkippedList) {
		List<String> customChangeIndicatorList = new ArrayList<String>();
		boolean isToBeSkipped = false;
		List<String> allChangeTrackingIndicatorList = ContentGuardUtils.getInstance().getChangeTrackingIndicatorList();
		for (String changeIndicator : allChangeTrackingIndicatorList) {
			isToBeSkipped = false;
			nextChangeIndicatorToBeSkipped: for (String changeIndicatorToBeSkipped : changeIndicatorToBeSkippedList) {
				if (StringUtils.equalsIgnoreCase(changeIndicatorToBeSkipped, changeIndicator)) {
					isToBeSkipped = true;
					break nextChangeIndicatorToBeSkipped;
				}
			}
			if (isToBeSkipped == false) {
				customChangeIndicatorList.add(changeIndicator);
			}
		}
		return customChangeIndicatorList;
	}

	private TargetUrlChangeAlertEntity transformCriticalChangesAlert(TargetUrlChangeAlertEntity targetUrlChangeAlertEntityInput) throws Exception {
		FormatUtils.getInstance()
				.logMemoryUsage("transformCriticalChangesAlert() ip=" + ip + " begins,targetUrlChangeAlertEntityInput=" + targetUrlChangeAlertEntityInput.toString());
		TargetUrlChangeAlertEntity targetUrlChangeAlertEntityOutput = null;
		List<String> customChangeIndicatorList = null;
		String[] stringArray = null;
		String json = null;
		List<String> changeIndicatorToBeSkippedList = getDomainChangeIndicatorsNotInCriticalChangeIndicators(targetUrlChangeAlertEntityInput.getDomainId());
		if (changeIndicatorToBeSkippedList != null && changeIndicatorToBeSkippedList.size() > 0) {
			customChangeIndicatorList = getCustomChangesFromCriticalChanges(changeIndicatorToBeSkippedList);
			if (customChangeIndicatorList != null && customChangeIndicatorList.size() > 0) {
				targetUrlChangeAlertEntityOutput = targetUrlChangeAlertEntityInput.clone();
				targetUrlChangeAlertEntityOutput.setIndicatorFlag(IConstants.INDICATOR_FLAG_CUSTOM);
				stringArray = customChangeIndicatorList.toArray(new String[0]);
				json = new Gson().toJson(stringArray, String[].class);
				targetUrlChangeAlertEntityOutput.setCustomIndicators(json);
			}
			// when all critical changes indicator are skipped
			else {
				targetUrlChangeAlertEntityOutput = null;
			}
		}
		// no need to transform when domain does not skip any critical change indicators
		else {
			targetUrlChangeAlertEntityOutput = targetUrlChangeAlertEntityInput.clone();
		}
		if (targetUrlChangeAlertEntityOutput != null) {
			FormatUtils.getInstance().logMemoryUsage(
					"transformCriticalChangesAlert() ip=" + ip + " ends,targetUrlChangeAlertEntityOutput=" + targetUrlChangeAlertEntityOutput.toString());
		} else {
			FormatUtils.getInstance().logMemoryUsage("transformCriticalChangesAlert() ip=" + ip + " ends,targetUrlChangeAlertEntityOutput is null.");
		}
		return targetUrlChangeAlertEntityOutput;
	}

	private List<String> getDomainChangeIndicatorsNotInCriticalChangeIndicators(int domainId) {
		FormatUtils.getInstance().logMemoryUsage("getDomainChangeIndicatorsNotInCriticalChangeIndicators() ip=" + ip + " begins,domainId=" + domainId);
		List<String> changeIndicatorToBeSkippedList = new ArrayList<String>();
		List<String> criticalChangeTrackingIndicatorList = null;
		boolean isToBeSkipped = false;
		Map<String, String> criticalIndicatorTypeMap = null;
		List<ContentGuardChangeTrackingDomainSettingEntity> contentGuardChangeTrackingDomainSettingEntityList = contentGuardChangeTrackingDomainSettingDAO
				.getList(domainId);
		if (contentGuardChangeTrackingDomainSettingEntityList != null && contentGuardChangeTrackingDomainSettingEntityList.size() > 0) {
			criticalIndicatorTypeMap = ContentGuardUtils.getInstance().getCriticalIndicatorTypeMap();
			criticalChangeTrackingIndicatorList = new ArrayList<String>(criticalIndicatorTypeMap.keySet());
			for (String changeIndicator : criticalChangeTrackingIndicatorList) {
				isToBeSkipped = true;
				nextContentGuardChangeTrackingDomainSettingEntity: for (ContentGuardChangeTrackingDomainSettingEntity contentGuardChangeTrackingDomainSettingEntity : contentGuardChangeTrackingDomainSettingEntityList) {
					if (StringUtils.equalsIgnoreCase(changeIndicator, contentGuardChangeTrackingDomainSettingEntity.getIndicator())) {
						isToBeSkipped = false;
						break nextContentGuardChangeTrackingDomainSettingEntity;
					}
				}
				if (isToBeSkipped == true) {
					changeIndicatorToBeSkippedList.add(changeIndicator);
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("getDomainChangeIndicatorsNotInCriticalChangeIndicators() ip=" + ip + " ends,domainId=" + domainId
				+ ",changeIndicatorToBeSkippedList=" + changeIndicatorToBeSkippedList.toString());
		return changeIndicatorToBeSkippedList;
	}

	private List<String> getCustomChangesFromCriticalChanges(List<String> changeIndicatorToBeSkippedList) {
		List<String> customChangeIndicatorList = new ArrayList<String>();
		boolean isToBeSkipped = false;
		Map<String, String> criticalIndicatorTypeMap = ContentGuardUtils.getInstance().getCriticalIndicatorTypeMap();
		List<String> criticalChangeTrackingIndicatorList = new ArrayList<String>(criticalIndicatorTypeMap.keySet());
		for (String changeIndicator : criticalChangeTrackingIndicatorList) {
			isToBeSkipped = false;
			nextChangeIndicatorToBeSkipped: for (String changeIndicatorToBeSkipped : changeIndicatorToBeSkippedList) {
				if (StringUtils.equalsIgnoreCase(changeIndicatorToBeSkipped, changeIndicator)) {
					isToBeSkipped = true;
					break nextChangeIndicatorToBeSkipped;
				}
			}
			if (isToBeSkipped == false) {
				customChangeIndicatorList.add(changeIndicator);
			}
		}
		return customChangeIndicatorList;
	}

	private void processDetailedAlert(TargetUrlChangeAlertEntity targetUrlChangeAlertEntity, String trackDateString, String startCrawlTimestampString,
			String endCrawlTimestampString) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("processDetailedAlert() ip=" + ip + " begins,domainId=" + domainId + ",targetUrlChangeAlertEntity="
				+ targetUrlChangeAlertEntity.toString() + ",trackDateString=" + trackDateString + ",startCrawlTimestampString=" + startCrawlTimestampString);
		String[] customIndicatorArray = null;
		List<String> databaseFields1 = null;
		Integer[] pageTagIdArray = null;
		List<TargetUrlChangeIndClickHouseEntity> targetUrlChangeIndClickHouseEntityList1 = null;
		List<TargetUrlChangeIndClickHouseEntity> targetUrlChangeIndClickHouseEntityList2 = null;
		List<String> databaseFields3 = null;
		List<TargetUrlChangeIndClickHouseEntity> targetUrlChangeIndClickHouseEntityList3 = null;
		List<TargetUrlChangeIndicatorDetail> targetUrlChangeIndicatorDetailList;
		String[] stringArray = null;
		String changeIndicatorDescription = null;
		String severity = null;
		String informationalMessage = null;
		List<TargetUrlChangeAlertDetails> targetUrlChangeAlertDetailsList = new ArrayList<>();
		TargetUrlChangeAlertDetails targetUrlChangeAlertDetails = null;
		ChangeIndicatorPreviousCurrent changeIndicatorPreviousCurrent = null;

		// if custom change indicators, validate the JSON
		if (targetUrlChangeAlertEntity.getIndicatorFlag() == IConstants.INDICATOR_FLAG_CUSTOM) {
			try {
				customIndicatorArray = new Gson().fromJson(targetUrlChangeAlertEntity.getCustomIndicators(), String[].class);
			} catch (Exception e) {
				FormatUtils.getInstance().logMemoryUsage("processDetailedAlert() error--ip=" + ip + ",domainId=" + domainId + ",targetUrlChangeAlertEntity="
						+ targetUrlChangeAlertEntity.toString() + ",exception=" + e.getMessage());
				return;
			}
			if (customIndicatorArray == null || customIndicatorArray.length == 0) {
				FormatUtils.getInstance().logMemoryUsage("processDetailedAlert() error--ip=" + ip + ",domainId=" + domainId + ",targetUrlChangeAlertEntity="
						+ targetUrlChangeAlertEntity.toString() + ",customIndicatorArray is empty.");
				return;
			}
		}

		databaseFields1 = getDatabaseFields(targetUrlChangeAlertEntity);

		if (StringUtils.isNotBlank(targetUrlChangeAlertEntity.getPageTagIds())) {
			pageTagIdArray = new Gson().fromJson(targetUrlChangeAlertEntity.getPageTagIds(), Integer[].class);
		}

		// determine top 20 URLs with most changes
		final TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setDomain_id(domainId);
		targetUrlChangeRequest.setPage_tag_ids(pageTagIdArray);
		targetUrlChangeRequest.setStart_crawl_timestamp(startCrawlTimestampString);
		targetUrlChangeRequest.setEnd_crawl_timestamp(endCrawlTimestampString);
		targetUrlChangeRequest.setChangeIndicatorIdMap(this.changeIndicatorIdMap);
		databaseFields1.remove(databaseFields1.size() - 1);
		targetUrlChangeRequest.setChange_indicators(databaseFields1.toArray(new String[0]));
		final HtmlChangeClickHouseDAO changeClickHouseDAO = HtmlChangeClickHouseDAO.getInstance();
		List<HtmlChangeResponse.HtmlChangeAggregateModel> urlsWithMostChanges = changeClickHouseDAO.getUrlsWithMostChanges(targetUrlChangeRequest);
		if (!urlsWithMostChanges.isEmpty()) {
			log.info("processDetailedAlert() ip={},domainId= {},urlsWithMostChanges.size()={}", ip, domainId, urlsWithMostChanges.size());
			final List<TargetUrlChangeAlertDetails> targetUrlChangeAlertDetailsArrayList = new ArrayList<>();

			List<HtmlChange> htmlChanges = new ArrayList<>();
			final TargetUrlChangeAlertDetails newChangeAlertDetails = new TargetUrlChangeAlertDetails();
			for (HtmlChangeResponse.HtmlChangeAggregateModel htmlChangeAggregateModel : urlsWithMostChanges) {
				final String url = htmlChangeAggregateModel.getUrl();
				newChangeAlertDetails.setUrl(url);
				newChangeAlertDetails.setTotalChanges(htmlChangeAggregateModel.getTotal());
				try {
					// start to get change indicators for this url
					final List<HtmlChange> htmlChangeList = changeClickHouseDAO.getHtmlChangeListByUrl(targetUrlChangeRequest, url);
					log.info("processDetailedAlert() ip={},domainId= {},url= {},htmlChangeList.size()={}", ip, domainId, url, htmlChangeList.size());
					if (!htmlChangeList.isEmpty()) {
						List<HtmlChange> bigDataHtmlChangeList = new ArrayList<>();
						for (int i = 0; i < htmlChangeList.size(); i++) {
							HtmlChange htmlChange = htmlChangeList.get(i);
							htmlChange.setOrderIndex(i);
							String change_indicator = htmlChange.getChange_indicator();
							log.info("indicatorFlag= {}, url: {}, change_indicator= {}", targetUrlChangeAlertEntity.getIndicatorFlag(), url, change_indicator);
							if (isSkipChangeIndicator(targetUrlChangeAlertEntity.getIndicatorFlag(), change_indicator)) {
								log.info("skip indicator: url= {}, change_indicator= {}", url, change_indicator);
								continue;
							}
							final ChangeIndicatorEnum changeIndicatorEnum = ChangeIndicatorEnum.fromIndicator(change_indicator);
							if (changeIndicatorEnum.isBigDataFlg()) {
								bigDataHtmlChangeList.add(htmlChange);
							} else {
								if (changeIndicatorEnum == ChangeIndicatorEnum.RESPONSE_CODE_CHG_IND) {
									htmlChange.setPrevValue(htmlChange.getResponse_code_previous());
									htmlChange.setCurrValue(htmlChange.getResponse_code_current());
								}
								htmlChanges.add(htmlChange);
							}
						}
						if (!bigDataHtmlChangeList.isEmpty()) {
							log.info("processDetailedAlert() ip={},domainId= {},url= {},bigDataHtmlChangeList.size()={}", ip, domainId, url, bigDataHtmlChangeList.size());
							final HtmlChangeService htmlChangeService = new HtmlChangeService();
							htmlChangeService.replaceBigData(bigDataHtmlChangeList);
							htmlChanges.addAll(bigDataHtmlChangeList);
							htmlChanges.sort(Comparator.comparingInt(HtmlChange::getOrderIndex));
						}
					}

				} catch (Exception e) {
					throw new RuntimeException(e);
				}
			}
			log.info("htmlChanges size: {}", htmlChanges);
			if (htmlChanges.isEmpty()) {
				log.warn("OID: {} htmlChanges is empty after skip indicator", domainId);
				return;
			}
			final List<TargetUrlChangeIndClickHouseEntity> targetUrlChangeIndClickHouseEntityList = htmlChanges.stream()
					.map(htmlChange -> {
						final ChangeIndicatorEnum changeIndicatorEnum = ChangeIndicatorEnum.fromIndicator(htmlChange.getChange_indicator());
						return changeIndicatorEnum.getStrategy().convertToTargetUrlChangeIndClickHouseEntity(htmlChange);
					}).collect(Collectors.toList());

			// create detailed alert content
			targetUrlChangeIndicatorDetailList = listChangeIndicatorsService
					.convertTargetUrlChangeIndClickHouseEntityList(targetUrlChangeIndClickHouseEntityList);
			List<ChangeIndicatorPreviousCurrent> changeIndicatorPreviousCurrentList = new ArrayList<>();
			for (TargetUrlChangeIndicatorDetail targetUrlChangeIndicatorDetail : targetUrlChangeIndicatorDetailList) {
				final String changeIndicator = targetUrlChangeIndicatorDetail.getChange_indicator();
				changeIndicatorDescription = ContentGuardUtils.getInstance().getChangeIndicatorDescription(changeIndicator);
				severity = ContentGuardUtils.getInstance().getChangeIndicatorSeverity(targetUrlChangeIndicatorDetail.getChange_indicator());
				stringArray = listChangeIndicatorsService.getDownloadAllPreviousCurrent(targetUrlChangeIndicatorDetail);
				changeIndicatorPreviousCurrent = new ChangeIndicatorPreviousCurrent();
				changeIndicatorPreviousCurrent.setChangeIndicatorDesc(changeIndicatorDescription);
				changeIndicatorPreviousCurrent.setCurrentCrawlTimestamp(targetUrlChangeIndicatorDetail.getCurrent_crawl_timestamp());
				changeIndicatorPreviousCurrent.setSeverity(severity);
				changeIndicatorPreviousCurrent.setPrevious(formatForHtml(stringArray[0]));
				changeIndicatorPreviousCurrent.setCurrent(formatForHtml(stringArray[1]));
				changeIndicatorPreviousCurrent.setUrl(targetUrlChangeIndicatorDetail.getUrl());
				changeIndicatorPreviousCurrentList.add(changeIndicatorPreviousCurrent);
			}
			if (changeIndicatorPreviousCurrentList != null && changeIndicatorPreviousCurrentList.size() > 0) {
				newChangeAlertDetails.setChangeIndicatorPreviousCurrentList(changeIndicatorPreviousCurrentList);
				targetUrlChangeAlertDetailsList.add(newChangeAlertDetails);
			}
			targetUrlChangeAlertDetailsArrayList.add(newChangeAlertDetails);
		}

		if (targetUrlChangeAlertDetailsList != null && targetUrlChangeAlertDetailsList.size() > 0) {
			informationalMessage = getInformationalMessageForDetailedAlert(targetUrlChangeAlertEntity.getIndicatorFlag(),
					targetUrlChangeAlertEntity.getAlertFrequency(), trackDateString, startCrawlTimestampString, endCrawlTimestampString);
			sendDetailedAlert(targetUrlChangeAlertEntity.getEmails(), targetUrlChangeAlertDetailsList, targetUrlChangeAlertEntity.getFriendlyName(),
					informationalMessage);
		}

		FormatUtils.getInstance().logMemoryUsage(
				"processDetailedAlert() ip=" + ip + " ends,domainId=" + domainId + ",targetUrlChangeAlertEntity=" + targetUrlChangeAlertEntity.toString());
	}

	private String formatForHtml(String inputString) {
		String testString = StringUtils.replace(inputString, IConstants.NEWLINE, IConstants.HTML_LINE_BREAK);
		testString = StringUtils.replace(testString, IConstants.TAB, IConstants.HTML_SPACE);
		return testString;
	}

	private boolean isSkipChangeIndicator(int alertIndicatorFlag, String changeIndicator) {
		boolean output = false;
		if (alertIndicatorFlag == IConstants.INDICATOR_FLAG_ALL) {

			// description_length_chg_ind
			if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.DESCRIPTION_LENGTH_CHG_IND)) {
				output = true;
			}
			// follow_flg_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.FOLLOW_FLG_CHG_IND)) {
				output = true;
			}
			// h1_length_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.H1_LENGTH_CHG_IND)) {
				output = true;
			}
			// index_flg_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.INDEX_FLG_CHG_IND)) {
				output = true;
			}
			// insecure_resources_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.INSECURE_RESOURCES_CHG_IND)) {
				output = true;
			}
			// og_markup_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.OG_MARKUP_CHG_IND)) {
				output = true;
			}
			// og_markup_length_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.OG_MARKUP_LENGTH_CHG_IND)) {
				output = true;
			}
			// page_analysis_results_chg_ind_json
			else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.PAGE_ANALYSIS_RESULTS_CHG_IND_JSON)) {
				output = true;
			}
			// page_link_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.PAGE_LINK_CHG_IND)) {
				output = true;
			}
			// response_headers_added_ind
			else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.RESPONSE_HEADERS_ADDED_IND)) {
				output = true;
			}
			// response_headers_removed_ind
			else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.RESPONSE_HEADERS_REMOVED_IND)) {
				output = true;
			}
			// title_length_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.PAGE_LINK_CHG_IND)) {
				output = true;
			}
		}
		return output;
	}

	private List<String> getChangeDetailsDatabaseFields(String changeIndicator) throws Exception {
		List<String> databaseFields = new ArrayList<String>();
		databaseFields.add(IConstants.URL);
		databaseFields.add(IConstants.CHANGE_INDICATOR);
		if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.ALTERNATE_LINKS_CHG_IND)) {
			databaseFields.add(IConstants.ALTERNATE_LINKS + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.ALTERNATE_LINKS + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.AMPHTML_HREF_CHG_IND)) {
			databaseFields.add(IConstants.AMPHTML_HREF + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.AMPHTML_HREF + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.ANALYZED_URL_S_CHG_IND)) {
			databaseFields.add(IConstants.ANALYZED_URL_S + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.ANALYZED_URL_S + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.ARCHIVE_FLG_CHG_IND)) {
			databaseFields.add(IConstants.ARCHIVE_FLG + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.ARCHIVE_FLG + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.BASE_TAG_ADDED_IND)) {
			databaseFields.add(IConstants.BASE_TAG + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.BASE_TAG + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.BASE_TAG_CHG_IND)) {
			databaseFields.add(IConstants.BASE_TAG + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.BASE_TAG + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.BASE_TAG_REMOVED_IND)) {
			databaseFields.add(IConstants.BASE_TAG + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.BASE_TAG + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.BASE_TAG_TARGET_CHG_IND)) {
			databaseFields.add(IConstants.BASE_TAG_TARGET + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.BASE_TAG_TARGET + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.BLOCKED_BY_ROBOTS_CHG_IND)) {
			databaseFields.add(IConstants.BLOCKED_BY_ROBOTS + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.BLOCKED_BY_ROBOTS + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.CANONICAL_ADDED_IND)) {
			databaseFields.add(IConstants.CANONICAL + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.CANONICAL + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.CANONICAL_CHG_IND)) {
			databaseFields.add(IConstants.CANONICAL + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.CANONICAL + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.CANONICAL_REMOVED_IND)) {
			databaseFields.add(IConstants.CANONICAL + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.CANONICAL + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.CANONICAL_HEADER_FLAG_CHG_IND)) {
			databaseFields.add(IConstants.CANONICAL_HEADER_FLAG + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.CANONICAL_HEADER_FLAG + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.CANONICAL_HEADER_TYPE_CHG_IND)) {
			databaseFields.add(IConstants.CANONICAL_HEADER_TYPE + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.CANONICAL_HEADER_TYPE + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.CANONICAL_TYPE_CHG_IND)) {
			databaseFields.add(IConstants.CANONICAL_TYPE + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.CANONICAL_TYPE + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.CANONICAL_URL_IS_CONSISTENT_CHG_IND)) {
			databaseFields.add(IConstants.CANONICAL_URL_IS_CONSISTENT + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.CANONICAL_URL_IS_CONSISTENT + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.CONTENT_TYPE_CHG_IND)) {
			databaseFields.add(IConstants.CONTENT_TYPE + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.CONTENT_TYPE + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.CUSTOM_DATA_ADDED_IND)) {
			databaseFields.add(IConstants.CUSTOM_DATA + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.CUSTOM_DATA + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.CUSTOM_DATA_CHG_IND)) {
			databaseFields.add(IConstants.CUSTOM_DATA + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.CUSTOM_DATA + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.CUSTOM_DATA_REMOVED_IND)) {
			databaseFields.add(IConstants.CUSTOM_DATA + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.CUSTOM_DATA + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.DESCRIPTION_ADDED_IND)) {
			databaseFields.add(IConstants.DESCRIPTION + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.DESCRIPTION + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.DESCRIPTION_CHG_IND)) {
			databaseFields.add(IConstants.DESCRIPTION + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.DESCRIPTION + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.DESCRIPTION_REMOVED_IND)) {
			databaseFields.add(IConstants.DESCRIPTION + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.DESCRIPTION + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.DESCRIPTION_LENGTH_CHG_IND)) {
			databaseFields.add(IConstants.DESCRIPTION_LENGTH + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.DESCRIPTION_LENGTH + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.ERROR_MESSAGE_CHG_IND)) {
			databaseFields.add(IConstants.ERROR_MESSAGE + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.ERROR_MESSAGE + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.FINAL_RESPONSE_CODE_CHG_IND)) {
			databaseFields.add(IConstants.FINAL_RESPONSE_CODE + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.FINAL_RESPONSE_CODE + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.FOLLOW_FLG_CHG_IND)) {
			databaseFields.add(IConstants.FOLLOW_FLG + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.FOLLOW_FLG + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.H1_ADDED_IND)) {
			databaseFields.add(IConstants.H1 + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.H1 + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.H1_CHG_IND)) {
			databaseFields.add(IConstants.H1 + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.H1 + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.H1_REMOVED_IND)) {
			databaseFields.add(IConstants.H1 + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.H1 + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.H1_COUNT_CHG_IND)) {
			databaseFields.add(IConstants.H1_COUNT + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.H1_COUNT + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.H1_LENGTH_CHG_IND)) {
			databaseFields.add(IConstants.H1_LENGTH + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.H1_LENGTH + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.H2_ADDED_IND)) {
			databaseFields.add(IConstants.H2 + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.H2 + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.H2_CHG_IND)) {
			databaseFields.add(IConstants.H2 + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.H2 + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.H2_REMOVED_IND)) {
			databaseFields.add(IConstants.H2 + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.H2 + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.HEADER_NOARCHIVE_CHG_IND)) {
			databaseFields.add(IConstants.HEADER_NOARCHIVE + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.HEADER_NOARCHIVE + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.HEADER_NOFOLLOW_CHG_IND)) {
			databaseFields.add(IConstants.HEADER_NOFOLLOW + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.HEADER_NOFOLLOW + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.HEADER_NOINDEX_CHG_IND)) {
			databaseFields.add(IConstants.HEADER_NOINDEX + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.HEADER_NOINDEX + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.HEADER_NOODP_CHG_IND)) {
			databaseFields.add(IConstants.HEADER_NOODP + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.HEADER_NOODP + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.HEADER_NOSNIPPET_CHG_IND)) {
			databaseFields.add(IConstants.HEADER_NOSNIPPET + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.HEADER_NOSNIPPET + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.HEADER_NOYDIR_CHG_IND)) {
			databaseFields.add(IConstants.HEADER_NOYDIR + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.HEADER_NOYDIR + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.HREFLANG_ERRORS_CHG_IND)) {
			databaseFields.add(IConstants.HREFLANG_ERRORS + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.HREFLANG_ERRORS + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.HREFLANG_LINKS_CHG_IND)) {
			databaseFields.add(IConstants.HREFLANG_URL_COUNT + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.HREFLANG_URL_COUNT + IConstants.UNDERSCORE_PREVIOUS);
			databaseFields.add(IConstants.HREFLANG_LINKS + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.HREFLANG_LINKS + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.HREFLANG_LINKS_OUT_COUNT_CHG_IND)) {
			databaseFields.add(IConstants.HREFLANG_LINKS_OUT_COUNT + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.HREFLANG_LINKS_OUT_COUNT + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.HREFLANG_LINKS_ADDED_IND)) {
			databaseFields.add(IConstants.HREFLANG_URL_COUNT + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.HREFLANG_URL_COUNT + IConstants.UNDERSCORE_PREVIOUS);
			databaseFields.add(IConstants.HREFLANG_LINKS + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.HREFLANG_LINKS + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.HREFLANG_URL_COUNT_CHG_IND)) {
			databaseFields.add(IConstants.HREFLANG_URL_COUNT + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.HREFLANG_URL_COUNT + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.HREFLANG_LINKS_REMOVED_IND)) {
			databaseFields.add(IConstants.HREFLANG_URL_COUNT + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.HREFLANG_URL_COUNT + IConstants.UNDERSCORE_PREVIOUS);
			databaseFields.add(IConstants.HREFLANG_LINKS + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.HREFLANG_LINKS + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.INDEX_FLG_CHG_IND)) {
			databaseFields.add(IConstants.INDEX_FLG + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.INDEX_FLG + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.INDEXABLE_CHG_IND)) {
			databaseFields.add(IConstants.INDEXABLE + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.INDEXABLE + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.INSECURE_RESOURCES_CHG_IND)) {
			databaseFields.add(IConstants.INSECURE_RESOURCES + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.INSECURE_RESOURCES + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.META_CHARSET_CHG_IND)) {
			databaseFields.add(IConstants.META_CHARSET + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.META_CHARSET + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.META_CONTENT_TYPE_CHG_IND)) {
			databaseFields.add(IConstants.META_CONTENT_TYPE + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.META_CONTENT_TYPE + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.META_DISABLED_SITELINKS_CHG_IND)) {
			databaseFields.add(IConstants.META_DISABLED_SITELINKS + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.META_DISABLED_SITELINKS + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.META_NOODP_CHG_IND)) {
			databaseFields.add(IConstants.META_NOODP + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.META_NOODP + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.META_NOSNIPPET_CHG_IND)) {
			databaseFields.add(IConstants.META_NOSNIPPET + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.META_NOSNIPPET + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.META_NOYDIR_CHG_IND)) {
			databaseFields.add(IConstants.META_NOYDIR + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.META_NOYDIR + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.META_REDIRECT_CHG_IND)) {
			databaseFields.add(IConstants.META_REDIRECT + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.META_REDIRECT + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.MIXED_REDIRECTS_CHG_IND)) {
			databaseFields.add(IConstants.MIXED_REDIRECTS + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.MIXED_REDIRECTS + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.MOBILE_REL_ALTERNATE_URL_IS_CONSISTENT_CHG_IND)) {
			databaseFields.add(IConstants.MOBILE_REL_ALTERNATE_URL_IS_CONSISTENT + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.MOBILE_REL_ALTERNATE_URL_IS_CONSISTENT + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.NOODP_CHG_IND)) {
			databaseFields.add(IConstants.NOODP + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.NOODP + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.NOSNIPPET_CHG_IND)) {
			databaseFields.add(IConstants.NOSNIPPET + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.NOSNIPPET + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.NOYDIR_CHG_IND)) {
			databaseFields.add(IConstants.NOYDIR + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.NOYDIR + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.OPEN_GRAPH_ADDED_IND)) {
			databaseFields.add(IConstants.OG_MARKUP + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.OG_MARKUP + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.OG_MARKUP_CHG_IND)) {
			databaseFields.add(IConstants.OG_MARKUP + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.OG_MARKUP + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.OPEN_GRAPH_REMOVED_IND)) {
			databaseFields.add(IConstants.OG_MARKUP + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.OG_MARKUP + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.OG_MARKUP_LENGTH_CHG_IND)) {
			databaseFields.add(IConstants.OG_MARKUP_LENGTH + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.OG_MARKUP_LENGTH + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.OUTLINK_COUNT_CHG_IND)) {
			databaseFields.add(IConstants.OUTLINK_COUNT + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.OUTLINK_COUNT + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.PAGE_ANALYSIS_RESULTS_CHG_IND_JSON)) {
			databaseFields.add(IConstants.PAGE_ANALYSIS_RESULTS_CHG_IND_JSON);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.PAGE_LINK_CHG_IND)) {
			databaseFields.add(IConstants.PAGE_LINK + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.PAGE_LINK + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.REDIRECT_BLOCKED_CHG_IND)) {
			databaseFields.add(IConstants.REDIRECT_BLOCKED + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.REDIRECT_BLOCKED + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.REDIRECT_BLOCKED_REASON_CHG_IND)) {
			databaseFields.add(IConstants.REDIRECT_BLOCKED_REASON + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.REDIRECT_BLOCKED_REASON + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.REDIRECT_CHAIN_CHG_IND)) {
			databaseFields.add(IConstants.REDIRECT_CHAIN + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.REDIRECT_CHAIN + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.REDIRECT_FINAL_URL_CHG_IND)) {
			databaseFields.add(IConstants.REDIRECT_FINAL_URL + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.REDIRECT_FINAL_URL + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.REDIRECT_TIMES_CHG_IND)) {
			databaseFields.add(IConstants.REDIRECT_TIMES + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.REDIRECT_TIMES + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.RESPONSE_CODE_CHG_IND)) {
			databaseFields.add(IConstants.RESPONSE_CODE + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.RESPONSE_CODE + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.REDIRECT_301_DETECTED_IND)) {
			databaseFields.add(IConstants.RESPONSE_CODE + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.RESPONSE_CODE + IConstants.UNDERSCORE_PREVIOUS);
			databaseFields.add(IConstants.REDIRECT_FINAL_URL + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.REDIRECT_FINAL_URL + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.REDIRECT_301_REMOVED_IND)) {
			databaseFields.add(IConstants.RESPONSE_CODE + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.RESPONSE_CODE + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.REDIRECT_302_DETECTED_IND)) {
			databaseFields.add(IConstants.RESPONSE_CODE + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.RESPONSE_CODE + IConstants.UNDERSCORE_PREVIOUS);
			databaseFields.add(IConstants.REDIRECT_FINAL_URL + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.REDIRECT_FINAL_URL + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.REDIRECT_302_REMOVED_IND)) {
			databaseFields.add(IConstants.RESPONSE_CODE + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.RESPONSE_CODE + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.REDIRECT_DIFF_CODE_IND)) {
			databaseFields.add(IConstants.RESPONSE_CODE + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.RESPONSE_CODE + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.RESPONSE_HEADERS_ADDED_IND)) {
			databaseFields.add(IConstants.RESPONSE_HEADERS + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.RESPONSE_HEADERS + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.RESPONSE_HEADERS_REMOVED_IND)) {
			databaseFields.add(IConstants.RESPONSE_HEADERS + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.RESPONSE_HEADERS + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.ROBOTS_ADDED_IND)) {
			databaseFields.add(IConstants.ROBOTS_CONTENTS + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.ROBOTS_CONTENTS + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.ROBOTS_CONTENTS_CHG_IND)) {
			databaseFields.add(IConstants.ROBOTS_CONTENTS + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.ROBOTS_CONTENTS + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.ROBOTS_REMOVED_IND)) {
			databaseFields.add(IConstants.ROBOTS_CONTENTS + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.ROBOTS_CONTENTS + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.STRUCTURED_DATA_CHG_IND)) {
			databaseFields.add(IConstants.STRUCTURED_DATA + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.STRUCTURED_DATA + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.TITLE_ADDED_IND)) {
			databaseFields.add(IConstants.TITLE + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.TITLE + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.TITLE_CHG_IND)) {
			databaseFields.add(IConstants.TITLE + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.TITLE + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.TITLE_REMOVED_IND)) {
			databaseFields.add(IConstants.TITLE + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.TITLE + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.TITLE_LENGTH_CHG_IND)) {
			databaseFields.add(IConstants.TITLE_LENGTH + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.TITLE_LENGTH + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.VIEWPORT_ADDED_IND)) {
			databaseFields.add(IConstants.VIEWPORT_CONTENT + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.VIEWPORT_CONTENT + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.VIEWPORT_CONTENT_CHG_IND)) {
			databaseFields.add(IConstants.VIEWPORT_CONTENT + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.VIEWPORT_CONTENT + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.VIEWPORT_REMOVED_IND)) {
			databaseFields.add(IConstants.VIEWPORT_CONTENT + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.VIEWPORT_CONTENT + IConstants.UNDERSCORE_PREVIOUS);
		} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.ROBOTS_TXT_CHG_IND)) {
			databaseFields.add(IConstants.ROBOT_TXT + IConstants.UNDERSCORE_CURRENT);
			databaseFields.add(IConstants.ROBOT_TXT + IConstants.UNDERSCORE_PREVIOUS);
		}
		return databaseFields;
	}

	private void processSummaryAlert(TargetUrlChangeAlertEntity targetUrlChangeAlertEntity, String trackDateString, String startCrawlTimestampString,
			String endCrawlTimestampString) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("processSummaryAlert() ip=" + ip + " begins,domainId=" + domainId + ",targetUrlChangeAlertEntity="
				+ targetUrlChangeAlertEntity.toString() + ",trackDateString=" + trackDateString + ",startCrawlTimestampString=" + startCrawlTimestampString);
		Integer[] pageTagIdArray = null;
		String[] customIndicatorArray = null;

		// if custom change indicators, validate the JSON
		if (targetUrlChangeAlertEntity.getIndicatorFlag() == IConstants.INDICATOR_FLAG_CUSTOM) {
			try {
				customIndicatorArray = new Gson().fromJson(targetUrlChangeAlertEntity.getCustomIndicators(), String[].class);
			} catch (Exception e) {
				FormatUtils.getInstance().logMemoryUsage("processSummaryAlert() error--ip=" + ip + ",domainId=" + domainId + ",targetUrlChangeAlertEntity="
						+ targetUrlChangeAlertEntity.toString() + ",exception=" + e.getMessage());
				return;
			}
			if (customIndicatorArray == null || customIndicatorArray.length == 0) {
				FormatUtils.getInstance().logMemoryUsage("processSummaryAlert() error--ip=" + ip + ",domainId=" + domainId + ",targetUrlChangeAlertEntity="
						+ targetUrlChangeAlertEntity.toString() + ",customIndicatorArray is empty.");
				return;
			}
		}

		final TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setDomain_id(domainId);
		targetUrlChangeRequest.setStart_crawl_timestamp(startCrawlTimestampString);
		targetUrlChangeRequest.setEnd_crawl_timestamp(endCrawlTimestampString);
		targetUrlChangeRequest.setChangeIndicatorIdMap(this.changeIndicatorIdMap);
		final List<String> databaseFields = getDatabaseFields(targetUrlChangeAlertEntity);
		databaseFields.remove(databaseFields.size() - 1);
		targetUrlChangeRequest.setChange_indicators(databaseFields.toArray(new String[0]));
		if (StringUtils.isNotBlank(targetUrlChangeAlertEntity.getPageTagIds())) {
			pageTagIdArray = new Gson().fromJson(targetUrlChangeAlertEntity.getPageTagIds(), Integer[].class);
			if (pageTagIdArray != null && pageTagIdArray.length > 0) {
				targetUrlChangeRequest.setPage_tag_ids(pageTagIdArray);
				processSummaryAlertByGroupTag(targetUrlChangeAlertEntity, trackDateString, startCrawlTimestampString, endCrawlTimestampString, targetUrlChangeRequest);
			} else {
				processSummaryAlertByDomain(targetUrlChangeAlertEntity, trackDateString, startCrawlTimestampString, endCrawlTimestampString, targetUrlChangeRequest);
			}
		} else {
			processSummaryAlertByDomain(targetUrlChangeAlertEntity, trackDateString, startCrawlTimestampString, endCrawlTimestampString, targetUrlChangeRequest);
		}
		FormatUtils.getInstance().logMemoryUsage(
				"processSummaryAlert() ip=" + ip + " ends,domainId=" + domainId + ",targetUrlChangeAlertEntity=" + targetUrlChangeAlertEntity.toString());
	}

	private void processSummaryAlertByGroupTag(TargetUrlChangeAlertEntity targetUrlChangeAlertEntity, String trackDateString, String startCrawlTimestampString,
	                                           String endCrawlTimestampString, TargetUrlChangeRequest targetUrlChangeRequest) throws Exception {

		// map key = group tag ID
		// map value = group tag name
		Map<Integer, String> pageTagIdNameMap = null;

		// map key = change indicator (filtered)
		// map value = total number of changes 
		Map<String, Integer> filteredChangeIndicatorTotalMap = null;

		// map key = change indicator
		// map value = total number of changes
		Map<String, Integer> changeIndicatorTotalMap = null;

		String pageTagName = null;
		String informationalMessage = null;
		String informationalMessage3 = null;
		List<ChangeIndicatorTotalChanges> changeIndicatorTotalChangesAddedList = null;
		List<ChangeIndicatorTotalChanges> changeIndicatorTotalChangesModifiedList = null;
		List<ChangeIndicatorTotalChanges> changeIndicatorTotalChangesRemovedList = null;
		TargetUrlChangeAlertSummary targetUrlChangeAlertSummary = null;
		List<TargetUrlChangeIndClickHouseEntity> targetUrlChangeIndClickHouseEntityList = null;
		List<List<ChangeIndicatorTotalChanges>> changeIndicatorTotalChangesList = null;
		List<ZapierTargetUrlChangeAlert> zapierTargetUrlChangeAlertList = null;

		List<String> databaseFields = getDatabaseFields(targetUrlChangeAlertEntity);

		Integer[] pageTagIdArray = new Gson().fromJson(targetUrlChangeAlertEntity.getPageTagIds(), Integer[].class);

		List<TargetUrlChangeAlertSummary> targetUrlChangeAlertSummaryList = new ArrayList<TargetUrlChangeAlertSummary>();

		pageTagIdNameMap = getPageTagIdNameMap(pageTagIdArray);
		nextGroupTagId: for (Integer pageTagId : pageTagIdArray) {
			targetUrlChangeRequest.setPage_tag_ids(new Integer[] { pageTagId });
			pageTagName = pageTagIdNameMap.get(pageTagId);
			FormatUtils.getInstance()
					.logMemoryUsage("processSummaryAlertByGroupTag() ip=" + ip + ",domainId=" + domainId + ",pageTagId=" + pageTagId + ",pageTagName=" + pageTagName);
			final List<HtmlChangeResponse.HtmlChangeAggregateModel> alertSummaryList = HtmlChangeClickHouseDAO.getInstance().getAlertSummaryList(targetUrlChangeRequest);
//			targetUrlChangeIndClickHouseEntityList = TargetUrlChangeIndClickHouseDAO.getInstance().getAlertList(ip, domainId, trackDateString, startCrawlTimestampString, endCrawlTimestampString, databaseFields, pageTagId);
			targetUrlChangeIndClickHouseEntityList = alertSummaryList.stream().map(htmlChangeAggregateModel -> {
				final TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = new TargetUrlChangeIndClickHouseEntity();
				targetUrlChangeIndClickHouseEntity.setChangeIndicator(htmlChangeAggregateModel.getChangeIndicator());
				targetUrlChangeIndClickHouseEntity.setTotal(htmlChangeAggregateModel.getTotal());
				return targetUrlChangeIndClickHouseEntity;
			}).collect(Collectors.toList());
			if (targetUrlChangeIndClickHouseEntityList == null || targetUrlChangeIndClickHouseEntityList.size() == 0) {
				FormatUtils.getInstance().logMemoryUsage("processSummaryAlertByGroupTag() ip=" + ip + " skipped,domainId=" + domainId + ",pageTagId=" + pageTagId
						+ ",pageTagName=" + pageTagName + ",targetUrlChangeIndClickHouseEntityList is empty.");
				continue nextGroupTagId;
			} else {
				FormatUtils.getInstance().logMemoryUsage("processSummaryAlertByGroupTag() ip=" + ip + ",domainId=" + domainId + ",pageTagId=" + pageTagId
						+ ",pageTagName=" + pageTagName + ",targetUrlChangeIndClickHouseEntityList.size()=" + targetUrlChangeIndClickHouseEntityList.size());
			}

			changeIndicatorTotalMap = getChangeIndicatorTotalMap(targetUrlChangeIndClickHouseEntityList);
			filteredChangeIndicatorTotalMap = filterChangeIndicatorTotalMap(targetUrlChangeAlertEntity, changeIndicatorTotalMap);
			if (filteredChangeIndicatorTotalMap != null && filteredChangeIndicatorTotalMap.size() > 0) {
				changeIndicatorTotalChangesList = getChangeIndicatorTotalChangesList(targetUrlChangeAlertEntity.getDomainId(), startCrawlTimestampString,
						endCrawlTimestampString, pageTagId, filteredChangeIndicatorTotalMap);
				changeIndicatorTotalChangesAddedList = changeIndicatorTotalChangesList.get(0);
				changeIndicatorTotalChangesModifiedList = changeIndicatorTotalChangesList.get(1);
				changeIndicatorTotalChangesRemovedList = changeIndicatorTotalChangesList.get(2);
				if (changeIndicatorTotalChangesAddedList.size() > 0 || changeIndicatorTotalChangesModifiedList.size() > 0
						|| changeIndicatorTotalChangesRemovedList.size() > 0) {
					targetUrlChangeAlertSummary = new TargetUrlChangeAlertSummary();
					targetUrlChangeAlertSummary.setPageTagId(pageTagId);
					targetUrlChangeAlertSummary.setPageTagName(pageTagName);
					targetUrlChangeAlertSummary.setChangeIndicatorTotalChangesAddedList(changeIndicatorTotalChangesAddedList);
					targetUrlChangeAlertSummary.setChangeIndicatorTotalChangesModifiedList(changeIndicatorTotalChangesModifiedList);
					targetUrlChangeAlertSummary.setChangeIndicatorTotalChangesRemovedList(changeIndicatorTotalChangesRemovedList);
					targetUrlChangeAlertSummaryList.add(targetUrlChangeAlertSummary);
				}
			}
			informationalMessage3 = getInformationalMessage3(trackDateString, startCrawlTimestampString, endCrawlTimestampString);
			zapierTargetUrlChangeAlertList = getZapierTargetUrlChangeAlertList(targetUrlChangeAlertSummaryList, informationalMessage3);
			if (zapierTargetUrlChangeAlertList != null && zapierTargetUrlChangeAlertList.size() > 0) {
				TargetUrlChangeUtils.getInstance().sendZapierTargetUrlChangeAlert(ip, domainId, zapierTargetUrlChangeAlertList, pageTagId);
			}
		}
		if (targetUrlChangeAlertSummaryList != null && targetUrlChangeAlertSummaryList.size() > 0) {
			informationalMessage = getInformationalMessageForGroupTagAlert(targetUrlChangeAlertEntity.getIndicatorFlag(),
					targetUrlChangeAlertEntity.getAlertFrequency(), trackDateString, startCrawlTimestampString, endCrawlTimestampString);
			sendGroupTagSummaryAlert(targetUrlChangeAlertEntity.getEmails(), targetUrlChangeAlertSummaryList, targetUrlChangeAlertEntity.getFriendlyName(),
					informationalMessage);
		}
	}

	private void processSummaryAlertByDomain(TargetUrlChangeAlertEntity targetUrlChangeAlertEntity, String trackDateString, String startCrawlTimestampString,
	                                         String endCrawlTimestampString, TargetUrlChangeRequest targetUrlChangeRequest) throws Exception {

		List<ChangeIndicatorTotalChanges> changeIndicatorTotalChangesAddedList = null;
		List<ChangeIndicatorTotalChanges> changeIndicatorTotalChangesModifiedList = null;
		List<ChangeIndicatorTotalChanges> changeIndicatorTotalChangesRemovedList = null;
		TargetUrlChangeAlertSummary targetUrlChangeAlertSummary = null;
		String informationalMessage = null;
		String informationalMessage3 = null;
		List<ZapierTargetUrlChangeAlert> zapierTargetUrlChangeAlertList = null;

		// map key = change indicator
		// map value = total number of changes
		Map<String, Integer> changeIndicatorTotalMap = null;

		// map key = change indicator (filtered)
		// map value = total number of changes 
		Map<String, Integer> filteredChangeIndicatorTotalMap = null;

		List<List<ChangeIndicatorTotalChanges>> changeIndicatorTotalChangesList = null;

		List<String> databaseFields = getDatabaseFields(targetUrlChangeAlertEntity);
//		List<TargetUrlChangeIndClickHouseEntity> targetUrlChangeIndClickHouseEntityList = TargetUrlChangeIndClickHouseDAO.getInstance().getAlertList(ip, domainId, trackDateString, startCrawlTimestampString, endCrawlTimestampString, databaseFields, null);
		final List<HtmlChangeResponse.HtmlChangeAggregateModel> alertSummaryList = HtmlChangeClickHouseDAO.getInstance().getAlertSummaryList(targetUrlChangeRequest);
		List<TargetUrlChangeIndClickHouseEntity> targetUrlChangeIndClickHouseEntityList = alertSummaryList.stream().map(htmlChangeAggregateModel -> {
			final TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = new TargetUrlChangeIndClickHouseEntity();
			targetUrlChangeIndClickHouseEntity.setChangeIndicator(htmlChangeAggregateModel.getChangeIndicator());
			targetUrlChangeIndClickHouseEntity.setTotal(htmlChangeAggregateModel.getTotal());
			return targetUrlChangeIndClickHouseEntity;
		}).collect(Collectors.toList());
		if (targetUrlChangeIndClickHouseEntityList == null || targetUrlChangeIndClickHouseEntityList.size() == 0) {
			FormatUtils.getInstance().logMemoryUsage("processSummaryAlertByDomain() ip=" + ip + " skipped,domainId=" + domainId + ",targetUrlChangeAlertEntity="
					+ targetUrlChangeAlertEntity.toString() + ",targetUrlChangeIndClickHouseEntityList is empty.");
			return;
		}

		changeIndicatorTotalMap = getChangeIndicatorTotalMap(targetUrlChangeIndClickHouseEntityList);
		filteredChangeIndicatorTotalMap = filterChangeIndicatorTotalMap(targetUrlChangeAlertEntity, changeIndicatorTotalMap);
		if (filteredChangeIndicatorTotalMap != null && filteredChangeIndicatorTotalMap.size() > 0) {
			changeIndicatorTotalChangesList = getChangeIndicatorTotalChangesList(targetUrlChangeAlertEntity.getDomainId(), startCrawlTimestampString,
					endCrawlTimestampString, null, filteredChangeIndicatorTotalMap);
			changeIndicatorTotalChangesAddedList = changeIndicatorTotalChangesList.get(0);
			changeIndicatorTotalChangesModifiedList = changeIndicatorTotalChangesList.get(1);
			changeIndicatorTotalChangesRemovedList = changeIndicatorTotalChangesList.get(2);
			if (changeIndicatorTotalChangesAddedList.size() > 0 || changeIndicatorTotalChangesModifiedList.size() > 0
					|| changeIndicatorTotalChangesRemovedList.size() > 0) {
				targetUrlChangeAlertSummary = new TargetUrlChangeAlertSummary();
				targetUrlChangeAlertSummary.setChangeIndicatorTotalChangesAddedList(changeIndicatorTotalChangesAddedList);
				targetUrlChangeAlertSummary.setChangeIndicatorTotalChangesModifiedList(changeIndicatorTotalChangesModifiedList);
				targetUrlChangeAlertSummary.setChangeIndicatorTotalChangesRemovedList(changeIndicatorTotalChangesRemovedList);
				informationalMessage3 = getInformationalMessage3(trackDateString, startCrawlTimestampString, endCrawlTimestampString);
				informationalMessage = getInformationalMessageForDomainAlert(targetUrlChangeAlertEntity.getIndicatorFlag(),
						targetUrlChangeAlertEntity.getAlertFrequency(), trackDateString, startCrawlTimestampString, endCrawlTimestampString);
				sendDomainSummaryAlert(targetUrlChangeAlertEntity.getEmails(), targetUrlChangeAlertSummary, targetUrlChangeAlertEntity.getFriendlyName(),
						informationalMessage);
				zapierTargetUrlChangeAlertList = getZapierTargetUrlChangeAlertList(targetUrlChangeAlertSummary, informationalMessage3);
				if (zapierTargetUrlChangeAlertList != null && zapierTargetUrlChangeAlertList.size() > 0) {
					TargetUrlChangeUtils.getInstance().sendZapierTargetUrlChangeAlert(ip, domainId, zapierTargetUrlChangeAlertList, null);
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("processSummaryAlertByDomain() ip=" + ip + ",domainId=" + domainId + ",targetUrlChangeAlertEntity="
				+ targetUrlChangeAlertEntity.toString() + ",targetUrlChangeIndClickHouseEntityList.size()=" + targetUrlChangeIndClickHouseEntityList.size());
	}

	private List<ZapierTargetUrlChangeAlert> getZapierTargetUrlChangeAlertList(List<TargetUrlChangeAlertSummary> targetUrlChangeAlertSummaryList,
			String informationalMessage3) {
		List<ZapierTargetUrlChangeAlert> zapierTargetUrlChangeAlertList = new ArrayList<ZapierTargetUrlChangeAlert>();
		List<ZapierTargetUrlChangeAlert> testZapierTargetUrlChangeAlertList = null;
		for (TargetUrlChangeAlertSummary targetUrlChangeAlertSummary : targetUrlChangeAlertSummaryList) {
			testZapierTargetUrlChangeAlertList = getZapierTargetUrlChangeAlertList(targetUrlChangeAlertSummary, informationalMessage3);
			if (testZapierTargetUrlChangeAlertList != null && testZapierTargetUrlChangeAlertList.size() > 0) {
				zapierTargetUrlChangeAlertList.addAll(testZapierTargetUrlChangeAlertList);
			}
		}
		return zapierTargetUrlChangeAlertList;
	}

	private List<ZapierTargetUrlChangeAlert> getZapierTargetUrlChangeAlertList(TargetUrlChangeAlertSummary targetUrlChangeAlertSummary, String informationalMessage3) {
		List<ZapierTargetUrlChangeAlert> zapierTargetUrlChangeAlertList = new ArrayList<ZapierTargetUrlChangeAlert>();
		ZapierTargetUrlChangeAlert zapierTargetUrlChangeAlert = null;
		if (targetUrlChangeAlertSummary.getChangeIndicatorTotalChangesAddedList() != null
				&& targetUrlChangeAlertSummary.getChangeIndicatorTotalChangesAddedList().size() > 0) {
			for (ChangeIndicatorTotalChanges changeIndicatorTotalChanges : targetUrlChangeAlertSummary.getChangeIndicatorTotalChangesAddedList()) {
				zapierTargetUrlChangeAlert = instantiateZapierTargetUrlChangeAlert(targetUrlChangeAlertSummary, informationalMessage3);
				zapierTargetUrlChangeAlert.setChange_description(changeIndicatorTotalChanges.getChangeIndicatorDesc());
				zapierTargetUrlChangeAlert.setTotal_urls(changeIndicatorTotalChanges.getTotalChanges());
				zapierTargetUrlChangeAlertList.add(zapierTargetUrlChangeAlert);
			}
		}
		if (targetUrlChangeAlertSummary.getChangeIndicatorTotalChangesModifiedList() != null
				&& targetUrlChangeAlertSummary.getChangeIndicatorTotalChangesModifiedList().size() > 0) {
			for (ChangeIndicatorTotalChanges changeIndicatorTotalChanges : targetUrlChangeAlertSummary.getChangeIndicatorTotalChangesModifiedList()) {
				zapierTargetUrlChangeAlert = instantiateZapierTargetUrlChangeAlert(targetUrlChangeAlertSummary, informationalMessage3);
				zapierTargetUrlChangeAlert.setChange_description(changeIndicatorTotalChanges.getChangeIndicatorDesc());
				zapierTargetUrlChangeAlert.setTotal_urls(changeIndicatorTotalChanges.getTotalChanges());
				zapierTargetUrlChangeAlertList.add(zapierTargetUrlChangeAlert);
			}
		}
		if (targetUrlChangeAlertSummary.getChangeIndicatorTotalChangesRemovedList() != null
				&& targetUrlChangeAlertSummary.getChangeIndicatorTotalChangesRemovedList().size() > 0) {
			for (ChangeIndicatorTotalChanges changeIndicatorTotalChanges : targetUrlChangeAlertSummary.getChangeIndicatorTotalChangesRemovedList()) {
				zapierTargetUrlChangeAlert = instantiateZapierTargetUrlChangeAlert(targetUrlChangeAlertSummary, informationalMessage3);
				zapierTargetUrlChangeAlert.setChange_description(changeIndicatorTotalChanges.getChangeIndicatorDesc());
				zapierTargetUrlChangeAlert.setTotal_urls(changeIndicatorTotalChanges.getTotalChanges());
				zapierTargetUrlChangeAlertList.add(zapierTargetUrlChangeAlert);
			}
		}
		return zapierTargetUrlChangeAlertList;
	}

	private ZapierTargetUrlChangeAlert instantiateZapierTargetUrlChangeAlert(TargetUrlChangeAlertSummary targetUrlChangeAlertSummary, String informationalMessage3) {
		ZapierTargetUrlChangeAlert zapierTargetUrlChangeAlert = new ZapierTargetUrlChangeAlert();
		zapierTargetUrlChangeAlert.setId(String.valueOf(startingId++));
		zapierTargetUrlChangeAlert.setDomain_id(domainId);
		zapierTargetUrlChangeAlert.setDomain_name(domainName);
		zapierTargetUrlChangeAlert.setPage_tag_id(targetUrlChangeAlertSummary.getPageTagId());
		zapierTargetUrlChangeAlert.setPage_tag_name(targetUrlChangeAlertSummary.getPageTagName());
		zapierTargetUrlChangeAlert.setMessage(informationalMessage3);
		return zapierTargetUrlChangeAlert;
	}

	private List<List<ChangeIndicatorTotalChanges>> getChangeIndicatorTotalChangesList(int domainId, String startCrawlTimestampString, String endCrawlTimestampString,
			Integer pageTagId, Map<String, Integer> filteredChangeIndicatorTotalMap) {
		List<List<ChangeIndicatorTotalChanges>> changeIndicatorTotalChangesList = new ArrayList<List<ChangeIndicatorTotalChanges>>();
		List<ChangeIndicatorTotalChanges> changeIndicatorTotalChangesAddedList = new ArrayList<ChangeIndicatorTotalChanges>();
		List<ChangeIndicatorTotalChanges> changeIndicatorTotalChangesModifiedList = new ArrayList<ChangeIndicatorTotalChanges>();
		List<ChangeIndicatorTotalChanges> changeIndicatorTotalChangesRemovedList = new ArrayList<ChangeIndicatorTotalChanges>();
		ContentGuardChangeTrackingEntity contentGuardChangeTrackingEntity = null;
		String changeIndicatorDesc = null;
		String changeType = null;
		String webAppLink = null;
		int totalChanges = 0;
		String totalChangesWithWebAppLink = null;
		ChangeIndicatorTotalChanges changeIndicatorTotalChanges = null;
		for (String changeIndicator : filteredChangeIndicatorTotalMap.keySet()) {
			contentGuardChangeTrackingEntity = ContentGuardUtils.getInstance().getContentGuardChangeTrackingEntity(changeIndicator);
			if (contentGuardChangeTrackingEntity != null) {
				changeIndicatorDesc = contentGuardChangeTrackingEntity.getDescription();
				changeType = contentGuardChangeTrackingEntity.getType();
				if (StringUtils.isNotBlank(changeIndicatorDesc) && StringUtils.isNotBlank(changeType)) {
					changeIndicatorTotalChanges = new ChangeIndicatorTotalChanges();
					changeIndicatorTotalChanges.setChangeIndicatorDesc(changeIndicatorDesc);
					totalChanges = filteredChangeIndicatorTotalMap.get(changeIndicator);
					changeIndicatorTotalChanges.setTotalChanges(totalChanges);
					totalChangesWithWebAppLink = getTotalChangesWithWebAppLink(domainId, startCrawlTimestampString, endCrawlTimestampString, pageTagId, changeIndicator,
							totalChanges);
					changeIndicatorTotalChanges.setTotalChangesWithWebAppLink(totalChangesWithWebAppLink);
					if (StringUtils.equalsIgnoreCase(changeType, IConstants.CHANGE_TYPE_ADDED)) {
						changeIndicatorTotalChangesAddedList.add(changeIndicatorTotalChanges);
					} else if (StringUtils.equalsIgnoreCase(changeType, IConstants.CHANGE_TYPE_MODIFIED)) {
						changeIndicatorTotalChangesModifiedList.add(changeIndicatorTotalChanges);
					} else if (StringUtils.equalsIgnoreCase(changeType, IConstants.CHANGE_TYPE_REMOVED)) {
						changeIndicatorTotalChangesRemovedList.add(changeIndicatorTotalChanges);
					} else {
						FormatUtils.getInstance().logMemoryUsage("getChangeIndicatorTotalChangesList() error--ip=" + ip + ",domainId=" + domainId + ",changeType "
								+ changeType + " is invalid for changeIndicator=" + changeIndicator);
					}
				} else {
					FormatUtils.getInstance().logMemoryUsage("getChangeIndicatorTotalChangesList() error--ip=" + ip + ",domainId=" + domainId
							+ ",changeIndicatorDesc is empty for changeIndicator=" + changeIndicator);
				}
			} else {
				FormatUtils.getInstance().logMemoryUsage("getChangeIndicatorTotalChangesList() error--ip=" + ip + ",domainId=" + domainId
						+ ",contentGuardChangeTrackingEntity is null for changeIndicator=" + changeIndicator);
			}
		}
		changeIndicatorTotalChangesList.add(changeIndicatorTotalChangesAddedList);
		changeIndicatorTotalChangesList.add(changeIndicatorTotalChangesModifiedList);
		changeIndicatorTotalChangesList.add(changeIndicatorTotalChangesRemovedList);
		return changeIndicatorTotalChangesList;
	}

	// private static final String WEB_APP_LINK_TEMPLATE_WITHOUT_PAGE_TAG = "<a href=\"https://app.seoclarity.net/page-clarity/content-change.do?oid={0}&changeElement={1}&date1={2}&date2={3}\">";
	// private static final String WEB_APP_LINK_TEMPLATE_WITH_PAGE_TAG = "<a href=\"https://app.seoclarity.net/page-clarity/content-change.do?oid={0}&changeElement={1}&date1={2}&date2={3}&pageTagIds={4}\">";
	private String getTotalChangesWithWebAppLink(int domainId, String startCrawlTimestampString, String endCrawlTimestampString, Integer pageTagId,
			String changeIndicator, int totalChanges) {
		FormatUtils.getInstance()
				.logMemoryUsage("getWebAppLink() begins. ip=" + ip + ",domainId=" + domainId + ",startCrawlTimestampString=" + startCrawlTimestampString
						+ ",endCrawlTimestampString=" + endCrawlTimestampString + ",pageTagId=" + pageTagId + ",changeIndicator=" + changeIndicator + ",totalChanges="
						+ totalChanges);
		String totalChangesWithWebAppLink = null;
		String webAppLink = null;
		String webAppLinkStartDateString = getWebAppLinkDate(startCrawlTimestampString);
		String webAppLinkEndDateString = getWebAppLinkDate(endCrawlTimestampString);
		if (pageTagId != null) {
			webAppLink = MessageFormat.format(WEB_APP_LINK_TEMPLATE_WITH_PAGE_TAG, String.valueOf(domainId), changeIndicator, webAppLinkStartDateString,
					webAppLinkEndDateString, String.valueOf(pageTagId));
		} else {
			webAppLink = MessageFormat.format(WEB_APP_LINK_TEMPLATE_WITHOUT_PAGE_TAG, String.valueOf(domainId), changeIndicator, webAppLinkStartDateString,
					webAppLinkEndDateString);
		}

		totalChangesWithWebAppLink = webAppLink + totalChanges + "</a>";

		FormatUtils.getInstance()
				.logMemoryUsage("getWebAppLink() ends. ip=" + ip + ",webAppLink=" + webAppLink + ",totalChangesWithWebAppLink=" + totalChangesWithWebAppLink);
		return totalChangesWithWebAppLink;
	}

	private String getWebAppLinkDate(String inputDateString) {
		String testCrawlDateYYYYMMDD = StringUtils.substringBefore(inputDateString, IConstants.ONE_SPACE);
		return StringUtils.substring(testCrawlDateYYYYMMDD, 5, 7) + IConstants.SLASH + StringUtils.substring(testCrawlDateYYYYMMDD, 8, 10) + IConstants.SLASH
				+ StringUtils.substring(testCrawlDateYYYYMMDD, 0, 4);

	}

	private List<String> getDatabaseFields(TargetUrlChangeAlertEntity targetUrlChangeAlertEntity) throws Exception {
		List<String> databaseFields = new ArrayList<String>();
		Map<String, String> criticalIndicatorTypeMap = null;
		String[] customIndicatorArray = null;
		List<String> changeTrackingIndicatorList = null;
		if (targetUrlChangeAlertEntity.getIndicatorFlag() == IConstants.INDICATOR_FLAG_ALL) {
			databaseFields = CrawlerUtils.getInstance().getChangeIndicatorDatabaseFields();
		} else if (targetUrlChangeAlertEntity.getIndicatorFlag() == IConstants.INDICATOR_FLAG_CRITICAL) {
			criticalIndicatorTypeMap = ContentGuardUtils.getInstance().getCriticalIndicatorTypeMap();
			if (criticalIndicatorTypeMap != null && criticalIndicatorTypeMap.size() > 0) {
				for (String criticalIndicator : criticalIndicatorTypeMap.keySet()) {
					databaseFields.add(criticalIndicator);
				}
			} else {
				FormatUtils.getInstance().logMemoryUsage("getDatabaseFields() error--ip=" + ip + ",domainId=" + domainId + ",criticalIndicatorTypeMap is empty.");
			}
		} else if (targetUrlChangeAlertEntity.getIndicatorFlag() == IConstants.INDICATOR_FLAG_CUSTOM) {
			changeTrackingIndicatorList = ContentGuardUtils.getInstance().getChangeTrackingIndicatorList();
			customIndicatorArray = new Gson().fromJson(targetUrlChangeAlertEntity.getCustomIndicators(), String[].class);
			if (customIndicatorArray != null && customIndicatorArray.length > 0) {
				for (String customIndicator : customIndicatorArray) {
					nextChangeTrackingIndicator: for (String changeTrackingIndicator : changeTrackingIndicatorList) {
						if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, customIndicator)) {
							databaseFields.add(customIndicator);
							break nextChangeTrackingIndicator;
						}
					}
				}
			} else {
				FormatUtils.getInstance().logMemoryUsage("getDatabaseFields() error--ip=" + ip + ",domainId=" + domainId + ",customIndicatorArray is empty.");
			}
		}
		databaseFields.add(IConstants.URL);
		return databaseFields;
	}

	private Map<Integer, String> getPageTagIdNameMap(Integer[] pageTagIdArray) {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("getPageTagIdNameMap() ip=" + ip + " begins,domainId=" + domainId + ",pageTagIdArray.length=" + pageTagIdArray.length);
		Map<Integer, String> pageTagIdNameMap = new HashMap<Integer, String>();
		List<GroupTagEntity> groupTagEntityList = groupTagEntityDAO.getList(domainId, GroupTagEntity.TAG_TYPE_TARGET_URL, pageTagIdArray);
		if (groupTagEntityList != null && groupTagEntityList.size() > 0) {
			for (GroupTagEntity groupTagEntity : groupTagEntityList) {
				pageTagIdNameMap.put(groupTagEntity.getId(), groupTagEntity.getTagName());
			}
		}
		FormatUtils.getInstance().logMemoryUsage("getPageTagIdNameMap() ip=" + ip + " begins,domainId=" + domainId + ",pageTagIdNameMap.size()="
				+ pageTagIdNameMap.size() + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return pageTagIdNameMap;
	}

	private Map<String, Integer> getChangeIndicatorTotalMap(List<TargetUrlChangeIndClickHouseEntity> targetUrlChangeIndClickHouseEntityList) {

		// map key = change indicator
		// map value = total number of changes
		Map<String, Integer> changeIndicatorTotalMap = new HashMap<String, Integer>();

		if (targetUrlChangeIndClickHouseEntityList != null && targetUrlChangeIndClickHouseEntityList.size() > 0) {
			for (TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity : targetUrlChangeIndClickHouseEntityList) {
				if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.ALTERNATE_LINKS_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.ALTERNATE_LINKS_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.ALTERNATE_LINKS_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.ALTERNATE_LINKS_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.AMPHTML_HREF_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.AMPHTML_HREF_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.ANALYZED_URL_S_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.ANALYZED_URL_S_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.ARCHIVE_FLG_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.ARCHIVE_FLG_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.BASE_TAG_ADDED_IND)) {
					changeIndicatorTotalMap.put(IConstants.BASE_TAG_ADDED_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.BASE_TAG_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.BASE_TAG_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.BASE_TAG_REMOVED_IND)) {
					changeIndicatorTotalMap.put(IConstants.BASE_TAG_REMOVED_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.BASE_TAG_TARGET_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.BASE_TAG_TARGET_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.BLOCKED_BY_ROBOTS_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.BLOCKED_BY_ROBOTS_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.CANONICAL_ADDED_IND)) {
					changeIndicatorTotalMap.put(IConstants.CANONICAL_ADDED_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.CANONICAL_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.CANONICAL_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.CANONICAL_HEADER_FLAG_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.CANONICAL_HEADER_FLAG_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.CANONICAL_HEADER_TYPE_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.CANONICAL_HEADER_TYPE_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.CANONICAL_REMOVED_IND)) {
					changeIndicatorTotalMap.put(IConstants.CANONICAL_REMOVED_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.CANONICAL_TYPE_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.CANONICAL_TYPE_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.CANONICAL_URL_IS_CONSISTENT_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.CANONICAL_URL_IS_CONSISTENT_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.CONTENT_TYPE_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.CONTENT_TYPE_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.CUSTOM_DATA_ADDED_IND)) {
					changeIndicatorTotalMap.put(IConstants.CUSTOM_DATA_ADDED_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.CUSTOM_DATA_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.CUSTOM_DATA_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.CUSTOM_DATA_REMOVED_IND)) {
					changeIndicatorTotalMap.put(IConstants.CUSTOM_DATA_REMOVED_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.DESCRIPTION_ADDED_IND)) {
					changeIndicatorTotalMap.put(IConstants.DESCRIPTION_ADDED_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.DESCRIPTION_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.DESCRIPTION_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.DESCRIPTION_LENGTH_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.DESCRIPTION_LENGTH_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.DESCRIPTION_REMOVED_IND)) {
					changeIndicatorTotalMap.put(IConstants.DESCRIPTION_REMOVED_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.ERROR_MESSAGE_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.ERROR_MESSAGE_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.FINAL_RESPONSE_CODE_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.FINAL_RESPONSE_CODE_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.FOLLOW_FLG_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.FOLLOW_FLG_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.H1_ADDED_IND)) {
					changeIndicatorTotalMap.put(IConstants.H1_ADDED_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.H1_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.H1_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.H1_COUNT_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.H1_COUNT_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.H1_LENGTH_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.H1_LENGTH_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.H1_REMOVED_IND)) {
					changeIndicatorTotalMap.put(IConstants.H1_REMOVED_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.H2_ADDED_IND)) {
					changeIndicatorTotalMap.put(IConstants.H2_ADDED_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.H2_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.H2_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.H2_REMOVED_IND)) {
					changeIndicatorTotalMap.put(IConstants.H2_REMOVED_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.HEADER_NOARCHIVE_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.HEADER_NOARCHIVE_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.HEADER_NOFOLLOW_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.HEADER_NOFOLLOW_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.HEADER_NOINDEX_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.HEADER_NOINDEX_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.HEADER_NOODP_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.HEADER_NOODP_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.HEADER_NOSNIPPET_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.HEADER_NOSNIPPET_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.HEADER_NOYDIR_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.HEADER_NOYDIR_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.HREFLANG_ERRORS_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.HREFLANG_ERRORS_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.HREFLANG_LINKS_ADDED_IND)) {
					changeIndicatorTotalMap.put(IConstants.HREFLANG_LINKS_ADDED_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.HREFLANG_LINKS_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.HREFLANG_LINKS_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.HREFLANG_LINKS_OUT_COUNT_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.HREFLANG_LINKS_OUT_COUNT_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.HREFLANG_LINKS_REMOVED_IND)) {
					changeIndicatorTotalMap.put(IConstants.HREFLANG_LINKS_REMOVED_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.HREFLANG_URL_COUNT_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.HREFLANG_URL_COUNT_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.INDEX_FLG_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.INDEX_FLG_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.INDEXABLE_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.INDEXABLE_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.INSECURE_RESOURCES_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.INSECURE_RESOURCES_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.META_CHARSET_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.META_CHARSET_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.META_CONTENT_TYPE_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.META_CONTENT_TYPE_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.META_DISABLED_SITELINKS_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.META_DISABLED_SITELINKS_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.META_NOODP_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.META_NOODP_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.META_NOSNIPPET_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.META_NOSNIPPET_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.META_NOYDIR_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.META_NOYDIR_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.META_REDIRECT_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.META_REDIRECT_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.MIXED_REDIRECTS_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.MIXED_REDIRECTS_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(),
						IConstants.MOBILE_REL_ALTERNATE_URL_IS_CONSISTENT_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.MOBILE_REL_ALTERNATE_URL_IS_CONSISTENT_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.NOODP_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.NOODP_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.NOSNIPPET_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.NOSNIPPET_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.NOYDIR_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.NOYDIR_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.OG_MARKUP_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.OG_MARKUP_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.OG_MARKUP_LENGTH_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.OG_MARKUP_LENGTH_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.OPEN_GRAPH_ADDED_IND)) {
					changeIndicatorTotalMap.put(IConstants.OPEN_GRAPH_ADDED_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.OPEN_GRAPH_REMOVED_IND)) {
					changeIndicatorTotalMap.put(IConstants.OPEN_GRAPH_REMOVED_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.OUTLINK_COUNT_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.OUTLINK_COUNT_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.PAGE_ANALYSIS_RESULTS_CHG_IND_JSON)) {
					changeIndicatorTotalMap.put(IConstants.PAGE_ANALYSIS_RESULTS_CHG_IND_JSON, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.PAGE_LINK_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.PAGE_LINK_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.REDIRECT_301_DETECTED_IND)) {
					changeIndicatorTotalMap.put(IConstants.REDIRECT_301_DETECTED_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.REDIRECT_301_REMOVED_IND)) {
					changeIndicatorTotalMap.put(IConstants.REDIRECT_301_REMOVED_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.REDIRECT_302_DETECTED_IND)) {
					changeIndicatorTotalMap.put(IConstants.REDIRECT_302_DETECTED_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.REDIRECT_302_REMOVED_IND)) {
					changeIndicatorTotalMap.put(IConstants.REDIRECT_302_REMOVED_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.REDIRECT_BLOCKED_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.REDIRECT_BLOCKED_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.REDIRECT_BLOCKED_REASON_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.REDIRECT_BLOCKED_REASON_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.REDIRECT_CHAIN_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.REDIRECT_CHAIN_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.REDIRECT_DIFF_CODE_IND)) {
					changeIndicatorTotalMap.put(IConstants.REDIRECT_DIFF_CODE_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.REDIRECT_FINAL_URL_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.REDIRECT_FINAL_URL_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.REDIRECT_TIMES_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.REDIRECT_TIMES_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.RESPONSE_CODE_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.RESPONSE_CODE_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.RESPONSE_HEADERS_ADDED_IND)) {
					changeIndicatorTotalMap.put(IConstants.RESPONSE_HEADERS_ADDED_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.RESPONSE_HEADERS_REMOVED_IND)) {
					changeIndicatorTotalMap.put(IConstants.RESPONSE_HEADERS_REMOVED_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.ROBOTS_ADDED_IND)) {
					changeIndicatorTotalMap.put(IConstants.ROBOTS_ADDED_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.ROBOTS_CONTENTS_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.ROBOTS_CONTENTS_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.ROBOTS_REMOVED_IND)) {
					changeIndicatorTotalMap.put(IConstants.ROBOTS_REMOVED_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.STRUCTURED_DATA_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.STRUCTURED_DATA_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.TITLE_ADDED_IND)) {
					changeIndicatorTotalMap.put(IConstants.TITLE_ADDED_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.TITLE_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.TITLE_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.TITLE_LENGTH_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.TITLE_LENGTH_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.TITLE_REMOVED_IND)) {
					changeIndicatorTotalMap.put(IConstants.TITLE_REMOVED_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.VIEWPORT_ADDED_IND)) {
					changeIndicatorTotalMap.put(IConstants.VIEWPORT_ADDED_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.VIEWPORT_CONTENT_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.VIEWPORT_CONTENT_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.VIEWPORT_REMOVED_IND)) {
					changeIndicatorTotalMap.put(IConstants.VIEWPORT_REMOVED_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.ROBOTS_TXT_CHG_IND)) {
					changeIndicatorTotalMap.put(IConstants.ROBOTS_TXT_CHG_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(IConstants.RESPONSE_CODE_404_REMOVED_IND, targetUrlChangeIndClickHouseEntity.getChangeIndicator())) {
					changeIndicatorTotalMap.put(IConstants.RESPONSE_CODE_404_REMOVED_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				} else if (StringUtils.equalsIgnoreCase(IConstants.RESPONSE_CODE_404_DETECTED_IND, targetUrlChangeIndClickHouseEntity.getChangeIndicator())) {
					changeIndicatorTotalMap.put(IConstants.RESPONSE_CODE_404_DETECTED_IND, targetUrlChangeIndClickHouseEntity.getTotal());
				}
			}
		}
		return changeIndicatorTotalMap;
	}

	private Map<String, Integer> filterChangeIndicatorTotalMap(TargetUrlChangeAlertEntity targetUrlChangeAlertEntity,
			Map<String, Integer> changeIndicatorTotalMapInput) {

		// map key = change indicator
		// map value = total number of changes
		Map<String, Integer> changeIndicatorTotalMapOutput = new HashMap<String, Integer>();
		Map<String, String> criticalIndicatorTypeMap = null;
		Map<String, String> customIndicatorTypeMap = null;
		String[] customIndicatorArray = null;

		if (changeIndicatorTotalMapInput != null && changeIndicatorTotalMapInput.size() > 0) {
			if (targetUrlChangeAlertEntity.getIndicatorFlag() == IConstants.INDICATOR_FLAG_ALL) {
				changeIndicatorTotalMapOutput = changeIndicatorTotalMapInput;
			} else if (targetUrlChangeAlertEntity.getIndicatorFlag() == IConstants.INDICATOR_FLAG_CRITICAL) {
				criticalIndicatorTypeMap = ContentGuardUtils.getInstance().getCriticalIndicatorTypeMap();
				if (criticalIndicatorTypeMap != null && criticalIndicatorTypeMap.size() > 0) {
					for (String criticalIndicator : criticalIndicatorTypeMap.keySet()) {
						nextChangeIndicatorInput: for (String changeIndicatorInput : changeIndicatorTotalMapInput.keySet()) {
							if (StringUtils.equalsIgnoreCase(criticalIndicator, changeIndicatorInput)) {
								changeIndicatorTotalMapOutput.put(changeIndicatorInput, changeIndicatorTotalMapInput.get(changeIndicatorInput));
								break nextChangeIndicatorInput;
							}
						}
					}
				} else {
					FormatUtils.getInstance()
							.logMemoryUsage("filterChangeIndicatorTotalMap() error--ip=" + ip + ",domainId=" + domainId + ",criticalIndicatorTypeMap is empty.");
				}
			} else if (targetUrlChangeAlertEntity.getIndicatorFlag() == IConstants.INDICATOR_FLAG_CUSTOM) {
				customIndicatorArray = new Gson().fromJson(targetUrlChangeAlertEntity.getCustomIndicators(), String[].class);
				if (customIndicatorArray != null && customIndicatorArray.length > 0) {
					customIndicatorTypeMap = ContentGuardUtils.getInstance().getCustomIndicatorTypeMap(customIndicatorArray);
					if (customIndicatorTypeMap != null && customIndicatorTypeMap.size() > 0) {
						for (String customIndicator : customIndicatorTypeMap.keySet()) {
							nextChangeIndicatorInput: for (String changeIndicatorInput : changeIndicatorTotalMapInput.keySet()) {
								if (StringUtils.equalsIgnoreCase(customIndicator, changeIndicatorInput)) {
									changeIndicatorTotalMapOutput.put(changeIndicatorInput, changeIndicatorTotalMapInput.get(changeIndicatorInput));
									break nextChangeIndicatorInput;
								}
							}
						}
					} else {
						FormatUtils.getInstance()
								.logMemoryUsage("filterChangeIndicatorTotalMap() error--ip=" + ip + ",domainId=" + domainId + ",customIndicatorTypeMap is empty.");
					}
				}
			}
		}
		return changeIndicatorTotalMapOutput;
	}

	private String getInformationalMessageForGroupTagAlert(int targetUrlChangeAlertIndicatorFlag, int alertFrequency, String trackDateString,
			String startCrawlTimestampString, String endCrawlTimestampString) {
		FormatUtils.getInstance().logMemoryUsage("getInformationalMessageForGroupTagAlert() ip=" + ip + " begins,domainId=" + domainId);
		String frequencyDesc = null;
		if (alertFrequency == IConstants.FREQUENCY_TYPE_DAILY) {
			frequencyDesc = IConstants.DAILY;
		} else if (alertFrequency == IConstants.FREQUENCY_TYPE_HOURLY) {
			frequencyDesc = IConstants.HOURLY;
		}
		String changeType = null;
		if (targetUrlChangeAlertIndicatorFlag == IConstants.INDICATOR_FLAG_ALL) {
			changeType = "all";
		} else if (targetUrlChangeAlertIndicatorFlag == IConstants.INDICATOR_FLAG_CRITICAL) {
			changeType = "critical";
		} else if (targetUrlChangeAlertIndicatorFlag == IConstants.INDICATOR_FLAG_CUSTOM) {
			changeType = "custom";
		}
		String timestamp = null;
		if (alertFrequencyType == IConstants.FREQUENCY_TYPE_DAILY) {
			timestamp = trackDateString + ".";
		} else if (alertFrequencyType == IConstants.FREQUENCY_TYPE_HOURLY) {
			timestamp = trackDateString + " from " + StringUtils.substring(startCrawlTimestampString, 11) + " to " + StringUtils.substring(endCrawlTimestampString, 11)
					+ " Central Time (US).";
		}
		String informationalMessage = "The following " + frequencyDesc + " alerts were generated for the following group tags of domain ID " + domainId
				+ " based on the changes that were found on " + timestamp;

		FormatUtils.getInstance()
				.logMemoryUsage("getInformationalMessageForGroupTagAlert() ip=" + ip + " ends,domainId=" + domainId + ",informationalMessage=" + informationalMessage);
		return informationalMessage;
	}

	private String getInformationalMessageForDomainAlert(int targetUrlChangeAlertIndicatorFlag, int alertFrequency, String trackDateString,
			String startCrawlTimestampString, String endCrawlTimestampString) {
		FormatUtils.getInstance().logMemoryUsage("getInformationalMessageForDomainAlert() ip=" + ip + " begins,domainId=" + domainId);
		String frequencyDesc = null;
		if (alertFrequency == IConstants.FREQUENCY_TYPE_DAILY) {
			frequencyDesc = IConstants.DAILY;
		} else if (alertFrequency == IConstants.FREQUENCY_TYPE_HOURLY) {
			frequencyDesc = IConstants.HOURLY;
		}
		String changeType = null;
		if (targetUrlChangeAlertIndicatorFlag == IConstants.INDICATOR_FLAG_ALL) {
			changeType = "all";
		} else if (targetUrlChangeAlertIndicatorFlag == IConstants.INDICATOR_FLAG_CRITICAL) {
			changeType = "critical";
		} else if (targetUrlChangeAlertIndicatorFlag == IConstants.INDICATOR_FLAG_CUSTOM) {
			changeType = "custom";
		}
		String timestamp = null;
		if (alertFrequencyType == IConstants.FREQUENCY_TYPE_DAILY) {
			timestamp = trackDateString + ".";
		} else if (alertFrequencyType == IConstants.FREQUENCY_TYPE_HOURLY) {
			timestamp = trackDateString + " from " + StringUtils.substring(startCrawlTimestampString, 11) + " to " + StringUtils.substring(endCrawlTimestampString, 11)
					+ " Central Time (US).";
		}
		String informationalMessage = "The following " + frequencyDesc + " alerts were generated for the changes in all the target URLs of domain ID " + domainId
				+ " based on the changes that were found on " + timestamp;

		FormatUtils.getInstance()
				.logMemoryUsage("getInformationalMessageForDomainAlert() ip=" + ip + " ends,domainId=" + domainId + ",informationalMessage=" + informationalMessage);
		return informationalMessage;
	}

	private String getInformationalMessageForDetailedAlert(int targetUrlChangeAlertIndicatorFlag, int alertFrequency, String trackDateString,
			String startCrawlTimestampString, String endCrawlTimestampString) {
		FormatUtils.getInstance().logMemoryUsage("getInformationalMessageForDetailedAlert() ip=" + ip + " begins,domainId=" + domainId);
		String frequencyDesc = null;
		if (alertFrequency == IConstants.FREQUENCY_TYPE_DAILY) {
			frequencyDesc = IConstants.DAILY;
		} else if (alertFrequency == IConstants.FREQUENCY_TYPE_HOURLY) {
			frequencyDesc = IConstants.HOURLY;
		}
		String changeType = null;
		if (targetUrlChangeAlertIndicatorFlag == IConstants.INDICATOR_FLAG_ALL) {
			changeType = "all";
		} else if (targetUrlChangeAlertIndicatorFlag == IConstants.INDICATOR_FLAG_CRITICAL) {
			changeType = "critical";
		} else if (targetUrlChangeAlertIndicatorFlag == IConstants.INDICATOR_FLAG_CUSTOM) {
			changeType = "custom";
		}
		String timestamp = null;
		if (alertFrequencyType == IConstants.FREQUENCY_TYPE_DAILY) {
			timestamp = trackDateString + ".";
		} else if (alertFrequencyType == IConstants.FREQUENCY_TYPE_HOURLY) {
			timestamp = trackDateString + " from " + StringUtils.substring(startCrawlTimestampString, 11) + " to " + StringUtils.substring(endCrawlTimestampString, 11)
					+ " Central Time (US).";
		}
		String webAppLinkStartDateString = getWebAppLinkDate(startCrawlTimestampString);
		String webAppLinkEndDateString = getWebAppLinkDate(endCrawlTimestampString);
		String webAppLink = MessageFormat.format(WEB_APP_LINK_TO_REPORT_TEMPLATE, String.valueOf(domainId), webAppLinkStartDateString, webAppLinkEndDateString);
		String informationalMessage = "The following detailed " + frequencyDesc + " alerts were generated for domain ID " + domainId
				+ " based on the changes that were found on " + timestamp + " " + webAppLink + "Click here to go to the report.</a>";

		FormatUtils.getInstance()
				.logMemoryUsage("getInformationalMessageForDetailedAlert() ip=" + ip + " ends,domainId=" + domainId + ",informationalMessage=" + informationalMessage);
		return informationalMessage;
	}

	private String getInformationalMessage3(String trackDateString, String startCrawlTimestampString, String endCrawlTimestampString) {
		String informationalMessage3 = null;
		if (alertFrequencyType == IConstants.FREQUENCY_TYPE_DAILY) {
			informationalMessage3 = "The changes were found on " + trackDateString + ".";
		} else if (alertFrequencyType == IConstants.FREQUENCY_TYPE_HOURLY) {
			informationalMessage3 = "The changes were found on " + trackDateString + " from " + StringUtils.substring(startCrawlTimestampString, 11) + " to "
					+ StringUtils.substring(endCrawlTimestampString, 11) + " Central Time (US).";
		}
		return informationalMessage3;
	}

	private void sendDomainSummaryAlert(String emails, TargetUrlChangeAlertSummary targetUrlChangeAlertSummary, String alertName, String informationalMessage) {
		FormatUtils.getInstance().logMemoryUsage("sendDomainSummaryAlert() ip=" + ip + " begins,domainId=" + domainId + ",emails=" + emails + ",alertName=" + alertName
				+ ",informationalMessage=" + informationalMessage);

		String[] emailAddressArray = null;

		if (isDebug == true) {
			emailAddressArray = new String[] { "<EMAIL>" };
		} else {
			emailAddressArray = new Gson().fromJson(emails, String[].class);
		}

		int retryCount = 0;
		Map<String, Object> map = new HashMap<String, Object>();
		String emailSubject = alertName + " for " + domainName + " as of " + DateFormatUtils.format(new Date(), IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
		map.put("informationalMessage", informationalMessage);
		map.put("targetUrlChangeAlertOneSummary", targetUrlChangeAlertSummary);

		AgencyInfoEntity agencyInfoEntity = agencyInfoService.getByDomainId(1701);
		while (retryCount < IConstants.MAX_SEND_EMAIL_RETRY_COUNT) {
			try {
				emailSenderComponent.sendMimeMultiPartZeptoMailAndBcc(emailAddressArray, null, emailSubject,
						"mail_target_url_change_alert.txt", "mail_target_url_change_alert.html", map, agencyInfoEntity);
				retryCount = IConstants.MAX_SEND_EMAIL_RETRY_COUNT;
			} catch (Exception e) {
				e.printStackTrace();
				retryCount++;
				if (retryCount >= IConstants.MAX_SEND_EMAIL_RETRY_COUNT) {
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("sendDomainSummaryAlert() ip=" + ip + ",domainId=" + domainId + ",retryCount=" + retryCount);
					try {
						Thread.sleep(IConstants.RETRY_WAIT_TIME_IN_MILLISECONDS);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}

		FormatUtils.getInstance().logMemoryUsage("sendDomainSummaryAlert() ip=" + ip + " ends,domainId=" + domainId + ",emails=" + emails);
	}

	private void sendGroupTagSummaryAlert(String emails, List<TargetUrlChangeAlertSummary> targetUrlChangeAlertSummaryList, String alertName,
			String informationalMessage) {
		FormatUtils.getInstance().logMemoryUsage(
				"sendGroupTagSummaryAlert() ip=" + ip + " begins,domainId=" + domainId + ",emails=" + emails + ",targetUrlChangeAlertSummaryList.size()="
						+ targetUrlChangeAlertSummaryList.size() + ",alertName=" + alertName + ",informationalMessage=" + informationalMessage);

		String[] emailAddressArray = null;

		if (isDebug == true) {
			emailAddressArray = new String[] { "<EMAIL>", };
		} else {
			emailAddressArray = new Gson().fromJson(emails, String[].class);
		}

		int retryCount = 0;
		Map<String, Object> map = new HashMap<String, Object>();
		String emailSubject = alertName + " for " + domainName + " as of " + DateFormatUtils.format(new Date(), IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
		map.put("informationalMessage", informationalMessage);
		map.put("targetUrlChangeAlertSummaryList", targetUrlChangeAlertSummaryList);

		AgencyInfoEntity agencyInfoEntity = agencyInfoService.getByDomainId(1701);
		while (retryCount < IConstants.MAX_SEND_EMAIL_RETRY_COUNT) {
			try {
				emailSenderComponent.sendMimeMultiPartZeptoMailAndBcc(emailAddressArray, null, emailSubject,
						"mail_target_url_change_alert.txt", "mail_target_url_change_alert.html", map, agencyInfoEntity);
				retryCount = IConstants.MAX_SEND_EMAIL_RETRY_COUNT;
			} catch (Exception e) {
				e.printStackTrace();
				retryCount++;
				if (retryCount >= IConstants.MAX_SEND_EMAIL_RETRY_COUNT) {
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("sendGroupTagSummaryAlert() ip=" + ip + ",domainId=" + domainId + ",retryCount=" + retryCount);
					try {
						Thread.sleep(IConstants.RETRY_WAIT_TIME_IN_MILLISECONDS);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}

		FormatUtils.getInstance().logMemoryUsage("sendGroupTagSummaryAlert() ip=" + ip + " ends,domainId=" + domainId + ",emails=" + emails
				+ ",targetUrlChangeAlertSummaryList.size()=" + targetUrlChangeAlertSummaryList.size());
	}

	private void sendDetailedAlert(String emails, List<TargetUrlChangeAlertDetails> targetUrlChangeAlertDetailsList, String alertName, String informationalMessage) {
		FormatUtils.getInstance()
				.logMemoryUsage("sendDetailedAlert() ip=" + ip + " begins,domainId=" + domainId + ",emails=" + emails + ",targetUrlChangeAlertDetailsList.size()="
						+ targetUrlChangeAlertDetailsList.size() + ",alertName=" + alertName + ",informationalMessage=" + informationalMessage);

		String[] emailAddressArray = null;

		if (isDebug == true) {
			emailAddressArray = new String[] { "<EMAIL>" };
		} else {
			emailAddressArray = new Gson().fromJson(emails, String[].class);
		}

		int retryCount = 0;
		Map<String, Object> map = new HashMap<String, Object>();
		String emailSubject = alertName + " for " + domainName + " as of " + DateFormatUtils.format(new Date(), IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
		map.put("informationalMessage", informationalMessage);
		map.put("targetUrlChangeAlertDetailsList", targetUrlChangeAlertDetailsList);

		AgencyInfoEntity agencyInfoEntity = agencyInfoService.getByDomainId(1701);
		while (retryCount < IConstants.MAX_SEND_EMAIL_RETRY_COUNT) {
			try {
				emailSenderComponent.sendMimeMultiPartZeptoMailAndBcc(emailAddressArray, null, emailSubject,
						"mail_target_url_change_detailed_alert.txt", "mail_target_url_change_detailed_alert.html", map, agencyInfoEntity);
				retryCount = IConstants.MAX_SEND_EMAIL_RETRY_COUNT;
			} catch (Exception e) {
				e.printStackTrace();
				retryCount++;
				if (retryCount >= IConstants.MAX_SEND_EMAIL_RETRY_COUNT) {
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("sendDetailedAlert() ip=" + ip + ",domainId=" + domainId + ",retryCount=" + retryCount);
					try {
						Thread.sleep(IConstants.RETRY_WAIT_TIME_IN_MILLISECONDS);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}

		FormatUtils.getInstance().logMemoryUsage("sendDetailedAlert() ip=" + ip + " ends,domainId=" + domainId + ",emails=" + emails
				+ ",targetUrlChangeAlertDetailsList.size()=" + targetUrlChangeAlertDetailsList.size());
	}
}
