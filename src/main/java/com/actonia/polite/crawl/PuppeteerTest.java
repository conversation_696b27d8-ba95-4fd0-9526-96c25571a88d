package com.actonia.polite.crawl;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.math.NumberUtils;

import com.actonia.IConstants;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.concurrency.ThreadPoolService;
import com.actonia.utils.CommonUtils;
import com.actonia.utils.CrawlerUtils;
import com.actonia.utils.FormatUtils;

public class PuppeteerTest {

	private static ThreadPoolService threadPool = ThreadPoolService.getInstance();

	private static List<String> outputList = new ArrayList<String>();

	public PuppeteerTest() {
	}

	public static void main(String args[]) {
		try {
			new PuppeteerTest().process(args);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			threadPool.destroy();
		}
	}

	private void process(String[] args) throws Exception {

		// runtime parameter 1: input file location path
		String numberOfThreadsString = args[0];
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 1: numberOfThreadsString=" + numberOfThreadsString);
		int numberOfThreads = NumberUtils.toInt(numberOfThreadsString);
		threadPool.init();
		CommonUtils.initThreads(numberOfThreads);

		// runtime parameter 2: input file location path
		String urlFileLocationPath = args[1];
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 2: urlFileLocationPath=" + urlFileLocationPath);

		// runtime parameter 3: output file location path
		String outputFileLocationPath = args[2];
		FormatUtils.getInstance().logMemoryUsage("process() runtime parameters 3: outputFileLocationPath=" + outputFileLocationPath);

		getOutputList().add(getHeading());

		File urlFile = new File(urlFileLocationPath);
		List<String> urlList = FileUtils.readLines(urlFile, IConstants.UTF_8);

		CrawlerUtils.getInstance();

		processUrlsConcurrently(urlList);

		List<String> outputList = getOutputList();
		File outputFile = new File(outputFileLocationPath);
		FileUtils.writeLines(outputFile, IConstants.UTF_8, outputList);

	}

	private String getHeading() {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("TIMESTAMP");
		stringBuilder.append(IConstants.BACKTICK);
		stringBuilder.append("THREAD");
		stringBuilder.append(IConstants.BACKTICK);
		stringBuilder.append("URL");
		stringBuilder.append(IConstants.BACKTICK);
		stringBuilder.append("HTTP STATUS");
		stringBuilder.append(IConstants.BACKTICK);
		stringBuilder.append("ELAPSED(S.)");
		return stringBuilder.toString();
	}

	private void processUrlsConcurrently(List<String> urlList) {
		FormatUtils.getInstance().logMemoryUsage("processUrlsConcurrently() begins. urlList.size()=" + urlList.size());
		long startTimestamp = System.currentTimeMillis();
		int totalNumberOfUrls = urlList.size();
		String ipAddress = null;
		String urlString = null;
		PuppeteerTestCommand puppeteerTestCommand = null;
		int numberOfUrlsProcessed = 0;
		do {
			ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
			if (ipAddress == null) {
				continue;
			}
			urlString = urlList.get(numberOfUrlsProcessed++);
			puppeteerTestCommand = getPuppeteerTestCommand(ipAddress, urlString);
			try {
				threadPool.execute(puppeteerTestCommand);
			} catch (Exception e) {
				e.printStackTrace();
			}

		} while (numberOfUrlsProcessed < totalNumberOfUrls);

		do {
			try {
				Thread.sleep(1000);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		} while (threadPool.getThreadPool().getActiveCount() > 0);
		FormatUtils.getInstance().logMemoryUsage("processUrlsConcurrently() end. elapsed time in seconds=" + ((System.currentTimeMillis() - startTimestamp) / 1000));
	}

	private PuppeteerTestCommand getPuppeteerTestCommand(String ip, String urlString) {
		PuppeteerTestCommand puppeteerTestCommand = new PuppeteerTestCommand(ip, urlString);
		puppeteerTestCommand.setStatus(true);
		return puppeteerTestCommand;
	}

	protected synchronized static List<String> getOutputList() {
		return outputList;
	}
}
