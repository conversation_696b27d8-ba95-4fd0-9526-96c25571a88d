package com.actonia.polite.crawl;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Set;

import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.concurrency.BaseThreadCommand;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.dao.TargetUrlChangeIndClickHouseDAO;
import com.actonia.dao.TargetUrlEntityDAO;
import com.actonia.dao.TargetUrlHtmlClickHouseDAO;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.TargetUrlChangeClickHouseEntity;
import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
import com.actonia.utils.ContentGuardUtils;
import com.actonia.utils.CrawlerUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.Md5Util;
import com.actonia.utils.SpringBeanFactory;

public class UpdateTargetUrlChangeCommand extends BaseThreadCommand {

	//private Boolean isDebug = false;
	private String ip;
	private int domainId;
	private String startTrackDateString;
	private String startCrawlTimestampString;
	private String endTrackDateString;
	private String endCrawlTimestampString;
	private List<String> changeTrackingIndicatorList;
	private TargetUrlEntityDAO targetUrlEntityDAO;

	public UpdateTargetUrlChangeCommand(String ip, int domainId, String startTrackDateString, String startCrawlTimestampString, String endTrackDateString,
			String endCrawlTimestampString, List<String> changeTrackingIndicatorList) {
		super();
		this.ip = ip;
		this.domainId = domainId;
		this.startTrackDateString = startTrackDateString;
		this.startCrawlTimestampString = startCrawlTimestampString;
		this.endTrackDateString = endTrackDateString;
		this.endCrawlTimestampString = endCrawlTimestampString;
		this.changeTrackingIndicatorList = changeTrackingIndicatorList;
		this.targetUrlEntityDAO = SpringBeanFactory.getBean("targetUrlEntityDAO");
	}

	@Override
	protected void execute() throws Exception {
		try {
			process();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			CacheModleFactory.getInstance().setAliveIpAddress(ip);
		}
	}

	@Override
	protected void undo() throws Exception {
		// TODO Auto-generated method stub

	}

	private void process() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		List<String> databaseFields = null;
		List<HtmlClickHouseEntity> testHtmlClickHouseEntityList = null;
		HtmlClickHouseEntity htmlClickHouseEntityCurrent = null;
		HtmlClickHouseEntity htmlClickHouseEntityPrevious = null;
		TargetUrlChangeClickHouseEntity targetUrlChangeClickHouseEntity = null;
		List<TargetUrlChangeIndClickHouseEntity> testTargetUrlChangeIndClickHouseEntityList = null;
		Set<String> targetUrlHashCodeSet = null;
		String hashCode = null;
		int totalNonManagedURLs = 0;
		List<TargetUrlChangeIndClickHouseEntity> targetUrlChangeIndClickHouseEntityList = new ArrayList<TargetUrlChangeIndClickHouseEntity>();
		List<HtmlClickHouseEntity> htmlClickHouseEntityIndicatorsList = TargetUrlHtmlClickHouseDAO.getInstance().getChangeTrackingSummaryList(domainId,
				startTrackDateString, endTrackDateString, startCrawlTimestampString, endCrawlTimestampString, changeTrackingIndicatorList);
		if (htmlClickHouseEntityIndicatorsList != null && htmlClickHouseEntityIndicatorsList.size() > 0) {
			targetUrlHashCodeSet = targetUrlEntityDAO.getHashCodeSet(domainId);
			FormatUtils.getInstance().logMemoryUsage("process() begins. ip=" + ip + ",domainId=" + domainId + ",startTimestamp=" + startCrawlTimestampString
					+ ",endTimestamp=" + endCrawlTimestampString + ",targetUrlHashCodeSet.size()=" + targetUrlHashCodeSet.size());
			nextHtmlClickHouseEntity: for (HtmlClickHouseEntity htmlClickHouseEntityIndicators : htmlClickHouseEntityIndicatorsList) {
				//if (isDebug == true) {
				//	if (StringUtils.equalsIgnoreCase(htmlClickHouseEntityIndicators.getUrl(), "https://www.belmond.com/ideas/articles/a-balmy-british-summer") == false) {
				//		continue nextHtmlClickHouseEntity;
				//	}
				//}
				hashCode = Md5Util.Md5(htmlClickHouseEntityIndicators.getUrl());
				if (targetUrlHashCodeSet.contains(hashCode) == false) {
					totalNonManagedURLs++;
					continue nextHtmlClickHouseEntity;
				}
				databaseFields = ContentGuardUtils.getInstance().getDatabaseFields(htmlClickHouseEntityIndicators);
				final Date previousCrawlTimestamp = htmlClickHouseEntityIndicators.getPreviousCrawlTimestamp();
                final Date crawlTimestamp = htmlClickHouseEntityIndicators.getCrawlTimestamp();
                // get today string date format from previousCrawlTimestamp and currentCrawlTimestamp
                final String previousTrackDate = DateFormatUtils.format(previousCrawlTimestamp, IConstants.DATE_FORMAT_YYYY_MM_DD);
                final String currentTrackDate = DateFormatUtils.format(crawlTimestamp, IConstants.DATE_FORMAT_YYYY_MM_DD);
				testHtmlClickHouseEntityList = TargetUrlHtmlClickHouseDAO.getInstance().getChangeTrackingFields(domainId, htmlClickHouseEntityIndicators.getUrl(),
						previousTrackDate, currentTrackDate, crawlTimestamp, previousCrawlTimestamp, databaseFields);
				if (testHtmlClickHouseEntityList != null && testHtmlClickHouseEntityList.size() == 2) {
					htmlClickHouseEntityCurrent = getHtmlClickHouseEntity(crawlTimestamp, testHtmlClickHouseEntityList);
					htmlClickHouseEntityPrevious = getHtmlClickHouseEntity(previousCrawlTimestamp, testHtmlClickHouseEntityList);
					if (htmlClickHouseEntityCurrent != null && htmlClickHouseEntityPrevious != null) {
						targetUrlChangeClickHouseEntity = getTargetUrlChangeClickHouseEntity(htmlClickHouseEntityIndicators, htmlClickHouseEntityCurrent,
								htmlClickHouseEntityPrevious);
						testTargetUrlChangeIndClickHouseEntityList = CrawlerUtils.getInstance()
								.getTargetUrlChangeIndClickHouseEntityList(targetUrlChangeClickHouseEntity);
						if (testTargetUrlChangeIndClickHouseEntityList != null && testTargetUrlChangeIndClickHouseEntityList.size() > 0) {
							// save response code previous and current to all TargetUrlChangeIndClickHouseEntity
							for (TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity : testTargetUrlChangeIndClickHouseEntityList) {
								targetUrlChangeIndClickHouseEntity.setResponseCodeCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getResponse_code());
								targetUrlChangeIndClickHouseEntity.setResponseCodePrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getResponse_code());
							}
							targetUrlChangeIndClickHouseEntityList.addAll(testTargetUrlChangeIndClickHouseEntityList);
						}
					} else {
						FormatUtils.getInstance().logMemoryUsage("process() error--ip=" + ip + ",domainId=" + domainId
								+ ",either htmlClickHouseEntityCurrent or htmlClickHouseEntityPrevious is null for url=" + htmlClickHouseEntityIndicators.getUrl());
					}
				}
			}
			prepareToMaintainDataStores(ip, targetUrlChangeIndClickHouseEntityList);
			FormatUtils.getInstance()
					.logMemoryUsage("process() ends. ip=" + ip + ",domainId=" + domainId + ",startTimestamp=" + startCrawlTimestampString + ",endTimestamp="
							+ endCrawlTimestampString + ",htmlClickHouseEntityIndicatorsList.size()=" + htmlClickHouseEntityIndicatorsList.size()
							+ ",totalNonManagedURLs=" + totalNonManagedURLs + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		}
	}

	private HtmlClickHouseEntity getHtmlClickHouseEntity(Date crawlTimestamp, List<HtmlClickHouseEntity> htmlClickHouseEntityList) {
		HtmlClickHouseEntity htmlClickHouseEntityOutput = null;
		nextHtmlClickHouseEntity: for (HtmlClickHouseEntity htmlClickHouseEntityInput : htmlClickHouseEntityList) {
			if (htmlClickHouseEntityInput.getCrawlTimestamp().compareTo(crawlTimestamp) == 0) {
				htmlClickHouseEntityOutput = htmlClickHouseEntityInput;
				break nextHtmlClickHouseEntity;
			}
		}
		return htmlClickHouseEntityOutput;
	}

	private TargetUrlChangeClickHouseEntity getTargetUrlChangeClickHouseEntity(HtmlClickHouseEntity htmlClickHouseEntityIndicators,
			HtmlClickHouseEntity htmlClickHouseEntityCurrent, HtmlClickHouseEntity htmlClickHouseEntityPrevious) {
		TargetUrlChangeClickHouseEntity targetUrlChangeClickHouseEntity = new TargetUrlChangeClickHouseEntity();
		targetUrlChangeClickHouseEntity.setDomainId(domainId);
		targetUrlChangeClickHouseEntity.setUrl(htmlClickHouseEntityIndicators.getUrl());
		targetUrlChangeClickHouseEntity.setTrackDate(DateUtils.truncate(htmlClickHouseEntityIndicators.getCrawlTimestamp(), Calendar.DAY_OF_MONTH));
		targetUrlChangeClickHouseEntity.setCurrentCrawlTimestamp(htmlClickHouseEntityIndicators.getCrawlTimestamp());
		targetUrlChangeClickHouseEntity.setPreviousCrawlTimestamp(htmlClickHouseEntityIndicators.getPreviousCrawlTimestamp());
		for (String changeTrackingIndicator : changeTrackingIndicatorList) {
			// alternate_links_chg_ind
			if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.ALTERNATE_LINKS_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getAlternateLinksChgInd())) {
					targetUrlChangeClickHouseEntity.setAlternateLinksChgInd(true);
					targetUrlChangeClickHouseEntity.setAlternateLinksCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getAlternate_links());
					targetUrlChangeClickHouseEntity.setAlternateLinksPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getAlternate_links());
				}
			}
			// amphtml_href_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.AMPHTML_HREF_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getAmphtmlHrefChgInd())) {
					targetUrlChangeClickHouseEntity.setAmphtmlHrefChgInd(true);
					targetUrlChangeClickHouseEntity.setAmphtmlHrefCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getAmphtml_href());
					targetUrlChangeClickHouseEntity.setAmphtmlHrefPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getAmphtml_href());
				}
			}
			// analyzed_url_s_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.ANALYZED_URL_S_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getAnalyzedUrlSChgInd())) {
					targetUrlChangeClickHouseEntity.setAnalyzedUrlSChgInd(true);
					targetUrlChangeClickHouseEntity.setAnalyzedUrlSCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getAnalyzed_url_s());
					targetUrlChangeClickHouseEntity.setAnalyzedUrlSPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getAnalyzed_url_s());
				}
			}
			// archive_flg_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.ARCHIVE_FLG_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getArchiveFlgChgInd())) {
					targetUrlChangeClickHouseEntity.setArchiveFlgChgInd(true);
					targetUrlChangeClickHouseEntity.setArchiveFlgCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getArchive_flg());
					targetUrlChangeClickHouseEntity.setArchiveFlgPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getArchive_flg());
				}
			}
			// base_tag_added_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.BASE_TAG_ADDED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getBaseTagAddedInd())) {
					targetUrlChangeClickHouseEntity.setBaseTagAddedInd(true);
					targetUrlChangeClickHouseEntity.setBaseTagCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getBase_tag());
					targetUrlChangeClickHouseEntity.setBaseTagPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getBase_tag());
				}
			}
			// base_tag_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.BASE_TAG_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getBaseTagChgInd())) {
					targetUrlChangeClickHouseEntity.setBaseTagChgInd(true);
					targetUrlChangeClickHouseEntity.setBaseTagCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getBase_tag());
					targetUrlChangeClickHouseEntity.setBaseTagPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getBase_tag());
				}
			}
			// base_tag_removed_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.BASE_TAG_REMOVED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getBaseTagRemovedInd())) {
					targetUrlChangeClickHouseEntity.setBaseTagRemovedInd(true);
					targetUrlChangeClickHouseEntity.setBaseTagCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getBase_tag());
					targetUrlChangeClickHouseEntity.setBaseTagPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getBase_tag());
				}
			}
			// base_tag_target_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.BASE_TAG_TARGET_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getBaseTagTargetChgInd())) {
					targetUrlChangeClickHouseEntity.setBaseTagTargetChgInd(true);
					targetUrlChangeClickHouseEntity.setBaseTagTargetCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getBase_tag_target());
					targetUrlChangeClickHouseEntity.setBaseTagTargetPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getBase_tag_target());
				}
			}
			// blocked_by_robots_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.BLOCKED_BY_ROBOTS_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getBlockedByRobotsChgInd())) {
					targetUrlChangeClickHouseEntity.setBlockedByRobotsChgInd(true);
					targetUrlChangeClickHouseEntity.setBlockedByRobotsCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getBlocked_by_robots());
					targetUrlChangeClickHouseEntity.setBlockedByRobotsPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getBlocked_by_robots());
				}
			}
			// canonical_added_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.CANONICAL_ADDED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getCanonicalAddedInd())) {
					targetUrlChangeClickHouseEntity.setCanonicalAddedInd(true);
					targetUrlChangeClickHouseEntity.setCanonicalCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getCanonical());
					targetUrlChangeClickHouseEntity.setCanonicalPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getCanonical());
				}
			}
			// canonical_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.CANONICAL_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getCanonicalChgInd())) {
					targetUrlChangeClickHouseEntity.setCanonicalChgInd(true);
					targetUrlChangeClickHouseEntity.setCanonicalCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getCanonical());
					targetUrlChangeClickHouseEntity.setCanonicalPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getCanonical());
				}
			}
			// canonical_removed_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.CANONICAL_REMOVED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getCanonicalRemovedInd())) {
					targetUrlChangeClickHouseEntity.setCanonicalRemovedInd(true);
					targetUrlChangeClickHouseEntity.setCanonicalCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getCanonical());
					targetUrlChangeClickHouseEntity.setCanonicalPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getCanonical());
				}
			}
			// canonical_header_flag_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.CANONICAL_HEADER_FLAG_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getCanonicalHeaderFlagChgInd())) {
					targetUrlChangeClickHouseEntity.setCanonicalHeaderFlagChgInd(true);
					targetUrlChangeClickHouseEntity.setCanonicalHeaderFlagCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getCanonical_header_flag());
					targetUrlChangeClickHouseEntity.setCanonicalHeaderFlagPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getCanonical_header_flag());
				}
			}
			// canonical_header_type_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.CANONICAL_HEADER_TYPE_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getCanonicalHeaderTypeChgInd())) {
					targetUrlChangeClickHouseEntity.setCanonicalHeaderTypeChgInd(true);
					targetUrlChangeClickHouseEntity.setCanonicalHeaderTypeCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getCanonical_header_type());
					targetUrlChangeClickHouseEntity.setCanonicalHeaderTypePrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getCanonical_header_type());
				}
			}
			// canonical_type_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.CANONICAL_TYPE_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getCanonicalTypeChgInd())) {
					targetUrlChangeClickHouseEntity.setCanonicalTypeChgInd(true);
					targetUrlChangeClickHouseEntity.setCanonicalTypeCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getCanonical_type());
					targetUrlChangeClickHouseEntity.setCanonicalTypePrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getCanonical_type());
				}
			}
			// canonical_url_is_consistent_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.CANONICAL_URL_IS_CONSISTENT_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getCanonicalUrlIsConsistentChgInd())) {
					targetUrlChangeClickHouseEntity.setCanonicalUrlIsConsistentChgInd(true);
					targetUrlChangeClickHouseEntity
							.setCanonicalUrlIsConsistentCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getCanonical_url_is_consistent());
					targetUrlChangeClickHouseEntity
							.setCanonicalUrlIsConsistentPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getCanonical_url_is_consistent());
				}
			}
			// content_type_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.CONTENT_TYPE_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getContentTypeChgInd())) {
					targetUrlChangeClickHouseEntity.setContentTypeChgInd(true);
					targetUrlChangeClickHouseEntity.setContentTypeCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getContent_type());
					targetUrlChangeClickHouseEntity.setContentTypePrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getContent_type());
				}
			}
			// custom_data_added_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.CUSTOM_DATA_ADDED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getCustomDataAddedInd())) {
					targetUrlChangeClickHouseEntity.setCustomDataAddedInd(true);
					targetUrlChangeClickHouseEntity.setCustomDataCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getCustom_data());
					targetUrlChangeClickHouseEntity.setCustomDataPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getCustom_data());
				}
			}
			// custom_data_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.CUSTOM_DATA_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getCustomDataChgInd())) {
					targetUrlChangeClickHouseEntity.setCustomDataChgInd(true);
					targetUrlChangeClickHouseEntity.setCustomDataCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getCustom_data());
					targetUrlChangeClickHouseEntity.setCustomDataPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getCustom_data());
				}
			}
			// custom_data_removed_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.CUSTOM_DATA_REMOVED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getCustomDataRemovedInd())) {
					targetUrlChangeClickHouseEntity.setCustomDataRemovedInd(true);
					targetUrlChangeClickHouseEntity.setCustomDataCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getCustom_data());
					targetUrlChangeClickHouseEntity.setCustomDataPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getCustom_data());
				}
			}
			// description_added_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.DESCRIPTION_ADDED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getDescriptionAddedInd())) {
					targetUrlChangeClickHouseEntity.setDescriptionAddedInd(true);
					targetUrlChangeClickHouseEntity.setDescriptionCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getDescription());
					targetUrlChangeClickHouseEntity.setDescriptionPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getDescription());
				}
			}
			// description_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.DESCRIPTION_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getDescriptionChgInd())) {
					targetUrlChangeClickHouseEntity.setDescriptionChgInd(true);
					targetUrlChangeClickHouseEntity.setDescriptionCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getDescription());
					targetUrlChangeClickHouseEntity.setDescriptionPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getDescription());
				}
			}
			// description_removed_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.DESCRIPTION_REMOVED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getDescriptionRemovedInd())) {
					targetUrlChangeClickHouseEntity.setDescriptionRemovedInd(true);
					targetUrlChangeClickHouseEntity.setDescriptionCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getDescription());
					targetUrlChangeClickHouseEntity.setDescriptionPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getDescription());
				}
			}
			// description_length_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.DESCRIPTION_LENGTH_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getDescriptionLengthChgInd())) {
					targetUrlChangeClickHouseEntity.setDescriptionLengthChgInd(true);
					targetUrlChangeClickHouseEntity.setDescriptionLengthCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getDescription_length());
					targetUrlChangeClickHouseEntity.setDescriptionLengthPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getDescription_length());
				}
			}
			// error_message_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.ERROR_MESSAGE_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getErrorMessageChgInd())) {
					targetUrlChangeClickHouseEntity.setErrorMessageChgInd(true);
					targetUrlChangeClickHouseEntity.setErrorMessageCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getError_message());
					targetUrlChangeClickHouseEntity.setErrorMessagePrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getError_message());
				}
			}
			// final_response_code_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.FINAL_RESPONSE_CODE_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getFinalResponseCodeChgInd())) {
					targetUrlChangeClickHouseEntity.setFinalResponseCodeChgInd(true);
					targetUrlChangeClickHouseEntity.setFinalResponseCodeCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getFinal_response_code());
					targetUrlChangeClickHouseEntity.setFinalResponseCodePrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getFinal_response_code());
				}
			}
			// follow_flg_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.FOLLOW_FLG_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getFollowFlgChgInd())) {
					targetUrlChangeClickHouseEntity.setFollowFlgChgInd(true);
					targetUrlChangeClickHouseEntity.setFollowFlgCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getFollow_flg());
					targetUrlChangeClickHouseEntity.setFollowFlgPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getFollow_flg());
				}
			}
			// h1_added_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.H1_ADDED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getH1AddedInd())) {
					targetUrlChangeClickHouseEntity.setH1AddedInd(true);
					targetUrlChangeClickHouseEntity.setH1Current(htmlClickHouseEntityCurrent.getCrawlerResponse().getH1());
					targetUrlChangeClickHouseEntity.setH1Previous(htmlClickHouseEntityPrevious.getCrawlerResponse().getH1());
				}
			}
			// h1_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.H1_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getH1ChgInd())) {
					targetUrlChangeClickHouseEntity.setH1ChgInd(true);
					targetUrlChangeClickHouseEntity.setH1Current(htmlClickHouseEntityCurrent.getCrawlerResponse().getH1());
					targetUrlChangeClickHouseEntity.setH1Previous(htmlClickHouseEntityPrevious.getCrawlerResponse().getH1());
				}
			}
			// h1_removed_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.H1_REMOVED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getH1RemovedInd())) {
					targetUrlChangeClickHouseEntity.setH1RemovedInd(true);
					targetUrlChangeClickHouseEntity.setH1Current(htmlClickHouseEntityCurrent.getCrawlerResponse().getH1());
					targetUrlChangeClickHouseEntity.setH1Previous(htmlClickHouseEntityPrevious.getCrawlerResponse().getH1());
				}
			}
			// h1_count_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.H1_COUNT_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getH1CountChgInd())) {
					targetUrlChangeClickHouseEntity.setH1CountChgInd(true);
					targetUrlChangeClickHouseEntity.setH1CountCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getH1_count());
					targetUrlChangeClickHouseEntity.setH1CountPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getH1_count());
				}
			}
			// h1_length_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.H1_LENGTH_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getH1LengthChgInd())) {
					targetUrlChangeClickHouseEntity.setH1LengthChgInd(true);
					targetUrlChangeClickHouseEntity.setH1LengthCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getH1_length());
					targetUrlChangeClickHouseEntity.setH1LengthPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getH1_length());
				}
			}
			// h2_added_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.H2_ADDED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getH2AddedInd())) {
					targetUrlChangeClickHouseEntity.setH2AddedInd(true);
					targetUrlChangeClickHouseEntity.setH2Current(htmlClickHouseEntityCurrent.getCrawlerResponse().getH2());
					targetUrlChangeClickHouseEntity.setH2Previous(htmlClickHouseEntityPrevious.getCrawlerResponse().getH2());
				}
			}
			// h2_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.H2_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getH2ChgInd())) {
					targetUrlChangeClickHouseEntity.setH2ChgInd(true);
					targetUrlChangeClickHouseEntity.setH2Current(htmlClickHouseEntityCurrent.getCrawlerResponse().getH2());
					targetUrlChangeClickHouseEntity.setH2Previous(htmlClickHouseEntityPrevious.getCrawlerResponse().getH2());
				}
			}
			// h2_removed_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.H2_REMOVED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getH2RemovedInd())) {
					targetUrlChangeClickHouseEntity.setH2RemovedInd(true);
					targetUrlChangeClickHouseEntity.setH2Current(htmlClickHouseEntityCurrent.getCrawlerResponse().getH2());
					targetUrlChangeClickHouseEntity.setH2Previous(htmlClickHouseEntityPrevious.getCrawlerResponse().getH2());
				}
			}
			// header_noarchive_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.HEADER_NOARCHIVE_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getHeaderNoarchiveChgInd())) {
					targetUrlChangeClickHouseEntity.setHeaderNoarchiveChgInd(true);
					targetUrlChangeClickHouseEntity.setHeaderNoarchiveCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getHeader_noarchive());
					targetUrlChangeClickHouseEntity.setHeaderNoarchivePrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getHeader_noarchive());
				}
			}
			// header_nofollow_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.HEADER_NOFOLLOW_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getHeaderNofollowChgInd())) {
					targetUrlChangeClickHouseEntity.setHeaderNofollowChgInd(true);
					targetUrlChangeClickHouseEntity.setHeaderNofollowCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getHeader_nofollow());
					targetUrlChangeClickHouseEntity.setHeaderNofollowPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getHeader_nofollow());
				}
			}
			// header_noindex_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.HEADER_NOINDEX_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getHeaderNoindexChgInd())) {
					targetUrlChangeClickHouseEntity.setHeaderNoindexChgInd(true);
					targetUrlChangeClickHouseEntity.setHeaderNoindexCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getHeader_noindex());
					targetUrlChangeClickHouseEntity.setHeaderNoindexPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getHeader_noindex());
				}
			}
			// header_noodp_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.HEADER_NOODP_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getHeaderNoodpChgInd())) {
					targetUrlChangeClickHouseEntity.setHeaderNoodpChgInd(true);
					targetUrlChangeClickHouseEntity.setHeaderNoodpCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getHeader_noodp());
					targetUrlChangeClickHouseEntity.setHeaderNoodpPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getHeader_noodp());
				}
			}
			// header_nosnippet_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.HEADER_NOSNIPPET_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getHeaderNosnippetChgInd())) {
					targetUrlChangeClickHouseEntity.setHeaderNosnippetChgInd(true);
					targetUrlChangeClickHouseEntity.setHeaderNosnippetCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getHeader_nosnippet());
					targetUrlChangeClickHouseEntity.setHeaderNosnippetPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getHeader_nosnippet());
				}
			}
			// header_noydir_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.HEADER_NOYDIR_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getHeaderNoydirChgInd())) {
					targetUrlChangeClickHouseEntity.setHeaderNoydirChgInd(true);
					targetUrlChangeClickHouseEntity.setHeaderNoydirCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getHeader_noydir());
					targetUrlChangeClickHouseEntity.setHeaderNoydirPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getHeader_noydir());
				}
			}
			// hreflang_errors_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.HREFLANG_ERRORS_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getHreflangErrorsChgInd())) {
					targetUrlChangeClickHouseEntity.setHreflangErrorsChgInd(true);
					targetUrlChangeClickHouseEntity.setHreflangErrorsCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getHreflang_errors());
					targetUrlChangeClickHouseEntity.setHreflangErrorsPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getHreflang_errors());
				}
			}
			// hreflang_links_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.HREFLANG_LINKS_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getHreflangLinksChgInd())) {
					targetUrlChangeClickHouseEntity.setHreflangLinksChgInd(true);
					targetUrlChangeClickHouseEntity.setHreflangLinksCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getHreflang_links());
					targetUrlChangeClickHouseEntity.setHreflangLinksPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getHreflang_links());
					targetUrlChangeClickHouseEntity.setHreflangUrlCountCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getHreflang_url_count());
					targetUrlChangeClickHouseEntity.setHreflangUrlCountPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getHreflang_url_count());
				}

			}
			// hreflang_links_out_count_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.HREFLANG_LINKS_OUT_COUNT_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getHreflangLinksOutCountChgInd())) {
					targetUrlChangeClickHouseEntity.setHreflangLinksOutCountChgInd(true);
					targetUrlChangeClickHouseEntity.setHreflangLinksOutCountCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getHreflang_links_out_count());
					targetUrlChangeClickHouseEntity.setHreflangLinksOutCountPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getHreflang_links_out_count());
				}
			}
			// hreflang_links_added_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.HREFLANG_LINKS_ADDED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getHreflangLinksAddedInd())) {
					targetUrlChangeClickHouseEntity.setHreflangLinksAddedInd(true);
					targetUrlChangeClickHouseEntity.setHreflangUrlCountCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getHreflang_url_count());
					targetUrlChangeClickHouseEntity.setHreflangUrlCountPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getHreflang_url_count());
					targetUrlChangeClickHouseEntity.setHreflangLinksCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getHreflang_links());
					targetUrlChangeClickHouseEntity.setHreflangLinksPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getHreflang_links());
				}
			}
			// hreflang_url_count_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.HREFLANG_URL_COUNT_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getHreflangUrlCountChgInd())) {
					targetUrlChangeClickHouseEntity.setHreflangUrlCountChgInd(true);
					targetUrlChangeClickHouseEntity.setHreflangUrlCountCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getHreflang_url_count());
					targetUrlChangeClickHouseEntity.setHreflangUrlCountPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getHreflang_url_count());
				}
			}
			// hreflang_links_removed_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.HREFLANG_LINKS_REMOVED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getHreflangLinksRemovedInd())) {
					targetUrlChangeClickHouseEntity.setHreflangLinksRemovedInd(true);
					targetUrlChangeClickHouseEntity.setHreflangUrlCountCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getHreflang_url_count());
					targetUrlChangeClickHouseEntity.setHreflangUrlCountPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getHreflang_url_count());
					targetUrlChangeClickHouseEntity.setHreflangLinksCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getHreflang_links());
					targetUrlChangeClickHouseEntity.setHreflangLinksPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getHreflang_links());
				}
			}
			// index_flg_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.INDEX_FLG_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getIndexFlgChgInd())) {
					targetUrlChangeClickHouseEntity.setIndexFlgChgInd(true);
					targetUrlChangeClickHouseEntity.setIndexFlgCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getIndex_flg());
					targetUrlChangeClickHouseEntity.setIndexFlgPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getIndex_flg());
				}
			}
			// indexable_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.INDEXABLE_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getIndexableChgInd())) {
					targetUrlChangeClickHouseEntity.setIndexableChgInd(true);
					targetUrlChangeClickHouseEntity.setIndexableCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getIndexable());
					targetUrlChangeClickHouseEntity.setIndexablePrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getIndexable());
				}
			}
			// insecure_resources_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.INSECURE_RESOURCES_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getInsecureResourcesChgInd())) {
					targetUrlChangeClickHouseEntity.setInsecureResourcesChgInd(true);
					targetUrlChangeClickHouseEntity.setInsecureResourcesCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getInsecure_resources());
					targetUrlChangeClickHouseEntity.setInsecureResourcesPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getInsecure_resources());
				}
			}
			// meta_charset_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.META_CHARSET_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getMetaCharsetChgInd())) {
					targetUrlChangeClickHouseEntity.setMetaCharsetChgInd(true);
					targetUrlChangeClickHouseEntity.setMetaCharsetCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getMeta_charset());
					targetUrlChangeClickHouseEntity.setMetaCharsetPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getMeta_charset());
				}
			}
			// meta_content_type_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.META_CONTENT_TYPE_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getMetaContentTypeChgInd())) {
					targetUrlChangeClickHouseEntity.setMetaContentTypeChgInd(true);
					targetUrlChangeClickHouseEntity.setMetaContentTypeCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getMeta_content_type());
					targetUrlChangeClickHouseEntity.setMetaContentTypePrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getMeta_content_type());
				}
			}
			// meta_disabled_sitelinks_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.META_DISABLED_SITELINKS_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getMetaDisabledSitelinksChgInd())) {
					targetUrlChangeClickHouseEntity.setMetaDisabledSitelinksChgInd(true);
					targetUrlChangeClickHouseEntity.setMetaDisabledSitelinksCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getMeta_disabled_sitelinks());
					targetUrlChangeClickHouseEntity.setMetaDisabledSitelinksPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getMeta_disabled_sitelinks());
				}
			}
			// meta_noodp_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.META_NOODP_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getMetaNoodpChgInd())) {
					targetUrlChangeClickHouseEntity.setMetaNoodpChgInd(true);
					targetUrlChangeClickHouseEntity.setMetaNoodpCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getMeta_noodp());
					targetUrlChangeClickHouseEntity.setMetaNoodpPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getMeta_noodp());
				}
			}
			// meta_nosnippet_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.META_NOSNIPPET_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getMetaNosnippetChgInd())) {
					targetUrlChangeClickHouseEntity.setMetaNosnippetChgInd(true);
					targetUrlChangeClickHouseEntity.setMetaNosnippetCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getMeta_nosnippet());
					targetUrlChangeClickHouseEntity.setMetaNosnippetPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getMeta_nosnippet());
				}
			}
			// meta_noydir_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.META_NOYDIR_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getMetaNoydirChgInd())) {
					targetUrlChangeClickHouseEntity.setMetaNoydirChgInd(true);
					targetUrlChangeClickHouseEntity.setMetaNoydirCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getMeta_noydir());
					targetUrlChangeClickHouseEntity.setMetaNoydirPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getMeta_noydir());
				}
			}
			// meta_redirect_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.META_REDIRECT_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getMetaRedirectChgInd())) {
					targetUrlChangeClickHouseEntity.setMetaRedirectChgInd(true);
					targetUrlChangeClickHouseEntity.setMetaRedirectCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getMeta_redirect());
					targetUrlChangeClickHouseEntity.setMetaRedirectPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getMeta_redirect());
				}
			}
			// mixed_redirects_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.MIXED_REDIRECTS_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getMixedRedirectsChgInd())) {
					targetUrlChangeClickHouseEntity.setMixedRedirectsChgInd(true);
					targetUrlChangeClickHouseEntity.setMixedRedirectsCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getMixed_redirects());
					targetUrlChangeClickHouseEntity.setMixedRedirectsPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getMixed_redirects());
				}
			}
			// mobile_rel_alternate_url_is_consistent_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.MOBILE_REL_ALTERNATE_URL_IS_CONSISTENT_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getMobileRelAlternateUrlIsConsistentChgInd())) {
					targetUrlChangeClickHouseEntity.setMobileRelAlternateUrlIsConsistentChgInd(true);
					targetUrlChangeClickHouseEntity
							.setMobileRelAlternateUrlIsConsistentCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getMobile_rel_alternate_url_is_consistent());
					targetUrlChangeClickHouseEntity.setMobileRelAlternateUrlIsConsistentPrevious(
							htmlClickHouseEntityPrevious.getCrawlerResponse().getMobile_rel_alternate_url_is_consistent());
				}
			}
			// noodp_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.NOODP_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getNoodpChgInd())) {
					targetUrlChangeClickHouseEntity.setNoodpChgInd(true);
					targetUrlChangeClickHouseEntity.setNoodpCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getNoodp());
					targetUrlChangeClickHouseEntity.setNoodpPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getNoodp());
				}
			}
			// nosnippet_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.NOSNIPPET_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getNosnippetChgInd())) {
					targetUrlChangeClickHouseEntity.setNosnippetChgInd(true);
					targetUrlChangeClickHouseEntity.setNosnippetCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getNosnippet());
					targetUrlChangeClickHouseEntity.setNosnippetPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getNosnippet());
				}
			}
			// noydir_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.NOYDIR_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getNoydirChgInd())) {
					targetUrlChangeClickHouseEntity.setNoydirChgInd(true);
					targetUrlChangeClickHouseEntity.setNoydirCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getNoydir());
					targetUrlChangeClickHouseEntity.setNoydirPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getNoydir());
				}
			}
			// open_graph_added_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.OPEN_GRAPH_ADDED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getOpenGraphAddedInd())) {
					targetUrlChangeClickHouseEntity.setOpenGraphAddedInd(true);
					targetUrlChangeClickHouseEntity.setOgMarkupCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getOg_markup());
					targetUrlChangeClickHouseEntity.setOgMarkupPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getOg_markup());
				}
			}
			// og_markup_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.OG_MARKUP_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getOgMarkupChgInd())) {
					targetUrlChangeClickHouseEntity.setOgMarkupChgInd(true);
					targetUrlChangeClickHouseEntity.setOgMarkupCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getOg_markup());
					targetUrlChangeClickHouseEntity.setOgMarkupPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getOg_markup());
				}
			}
			// open_graph_removed_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.OPEN_GRAPH_REMOVED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getOpenGraphRemovedInd())) {
					targetUrlChangeClickHouseEntity.setOpenGraphRemovedInd(true);
					targetUrlChangeClickHouseEntity.setOgMarkupCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getOg_markup());
					targetUrlChangeClickHouseEntity.setOgMarkupPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getOg_markup());
				}
			}
			// og_markup_length_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.OG_MARKUP_LENGTH_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getOgMarkupLengthChgInd())) {
					targetUrlChangeClickHouseEntity.setOgMarkupLengthChgInd(true);
					targetUrlChangeClickHouseEntity.setOgMarkupLengthCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getOg_markup_length());
					targetUrlChangeClickHouseEntity.setOgMarkupLengthPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getOg_markup_length());
				}
			}
			// outlink_count_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.OUTLINK_COUNT_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getOutlinkCountChgInd())) {
					targetUrlChangeClickHouseEntity.setOutlinkCountChgInd(true);
					targetUrlChangeClickHouseEntity.setOutlinkCountCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getOutlink_count());
					targetUrlChangeClickHouseEntity.setOutlinkCountPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getOutlink_count());
				}
			}
			// page_analysis_results_chg_ind_json
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.PAGE_ANALYSIS_RESULTS_CHG_IND_JSON)) {
				if (StringUtils.isNotBlank(htmlClickHouseEntityIndicators.getPageAnalysisResultsChgIndJson())) {
					targetUrlChangeClickHouseEntity.setPageAnalysisResultsChgIndJson(htmlClickHouseEntityIndicators.getPageAnalysisResultsChgIndJson());
				}
			}
			// page_link_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.PAGE_LINK_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getPageLinkChgInd())) {
					targetUrlChangeClickHouseEntity.setPageLinkChgInd(true);
					targetUrlChangeClickHouseEntity.setPageLinkCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getPage_link());
					targetUrlChangeClickHouseEntity.setPageLinkPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getPage_link());
				}
			}
			// redirect_blocked_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.REDIRECT_BLOCKED_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getRedirectBlockedChgInd())) {
					targetUrlChangeClickHouseEntity.setRedirectBlockedChgInd(true);
					targetUrlChangeClickHouseEntity.setRedirectBlockedCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getRedirect_blocked());
					targetUrlChangeClickHouseEntity.setRedirectBlockedPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getRedirect_blocked());
				}
			}
			// redirect_blocked_reason_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.REDIRECT_BLOCKED_REASON_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getRedirectBlockedReasonChgInd())) {
					targetUrlChangeClickHouseEntity.setRedirectBlockedReasonChgInd(true);
					targetUrlChangeClickHouseEntity.setRedirectBlockedReasonCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getRedirect_blocked_reason());
					targetUrlChangeClickHouseEntity.setRedirectBlockedReasonPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getRedirect_blocked_reason());
				}
			}
			// redirect_chain_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.REDIRECT_CHAIN_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getRedirectChainChgInd())) {
					targetUrlChangeClickHouseEntity.setRedirectChainChgInd(true);
					targetUrlChangeClickHouseEntity.setRedirectChainCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getRedirect_chain());
					targetUrlChangeClickHouseEntity.setRedirectChainPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getRedirect_chain());
				}
			}
			// redirect_final_url_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.REDIRECT_FINAL_URL_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getRedirectFinalUrlChgInd())) {
					targetUrlChangeClickHouseEntity.setRedirectFinalUrlChgInd(true);
					targetUrlChangeClickHouseEntity.setRedirectFinalUrlCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getRedirect_final_url());
					targetUrlChangeClickHouseEntity.setRedirectFinalUrlPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getRedirect_final_url());
				}
			}
			// redirect_times_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.REDIRECT_TIMES_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getRedirectTimesChgInd())) {
					targetUrlChangeClickHouseEntity.setRedirectTimesChgInd(true);
					targetUrlChangeClickHouseEntity.setRedirectTimesCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getRedirect_times());
					targetUrlChangeClickHouseEntity.setRedirectTimesPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getRedirect_times());
				}
			}
			// response_code_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.RESPONSE_CODE_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getResponseCodeChgInd())) {
					final String previousResponseCode = htmlClickHouseEntityPrevious.getCrawlerResponse().getResponse_code();
					final boolean isPreviousResponseCode9XX = Integer.parseInt(previousResponseCode) >= IConstants.HTTP_STATUS_CODE_900;
					// skip previous status code was 9XX #https://www.wrike.com/open.htm?id=1214002353
					if (isPreviousResponseCode9XX) {
						continue;
					}
					targetUrlChangeClickHouseEntity.setResponseCodeChgInd(true);
					targetUrlChangeClickHouseEntity.setResponseCodeCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getResponse_code());
					targetUrlChangeClickHouseEntity.setResponseCodePrevious(previousResponseCode);
				}
			}
			// redirect_301_detected_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.REDIRECT_301_DETECTED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getRedirect301DetectedInd())) {
					targetUrlChangeClickHouseEntity.setRedirect301DetectedInd(true);
					targetUrlChangeClickHouseEntity.setResponseCodeCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getResponse_code());
					targetUrlChangeClickHouseEntity.setResponseCodePrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getResponse_code());
					targetUrlChangeClickHouseEntity.setRedirectFinalUrlCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getRedirect_final_url());
					targetUrlChangeClickHouseEntity.setRedirectFinalUrlPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getRedirect_final_url());
				}
			}
			// redirect_301_removed_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.REDIRECT_301_REMOVED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getRedirect301RemovedInd())) {
					targetUrlChangeClickHouseEntity.setRedirect301RemovedInd(true);
					targetUrlChangeClickHouseEntity.setResponseCodeCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getResponse_code());
					targetUrlChangeClickHouseEntity.setResponseCodePrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getResponse_code());
				}
			}
			// redirect_302_detected_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.REDIRECT_302_DETECTED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getRedirect302DetectedInd())) {
					targetUrlChangeClickHouseEntity.setRedirect302DetectedInd(true);
					targetUrlChangeClickHouseEntity.setResponseCodeCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getResponse_code());
					targetUrlChangeClickHouseEntity.setResponseCodePrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getResponse_code());
					targetUrlChangeClickHouseEntity.setRedirectFinalUrlCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getRedirect_final_url());
					targetUrlChangeClickHouseEntity.setRedirectFinalUrlPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getRedirect_final_url());
				}
			}
			// redirect_302_removed_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.REDIRECT_302_REMOVED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getRedirect302RemovedInd())) {
					targetUrlChangeClickHouseEntity.setRedirect302RemovedInd(true);
					targetUrlChangeClickHouseEntity.setResponseCodeCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getResponse_code());
					targetUrlChangeClickHouseEntity.setResponseCodePrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getResponse_code());
				}
			}
			// redirect_diff_code_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.REDIRECT_DIFF_CODE_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getRedirectDiffCodeInd())) {
					targetUrlChangeClickHouseEntity.setRedirectDiffCodeInd(true);
					targetUrlChangeClickHouseEntity.setResponseCodeCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getResponse_code());
					targetUrlChangeClickHouseEntity.setResponseCodePrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getResponse_code());
				}
			}
			// response_headers_added_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.RESPONSE_HEADERS_ADDED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getResponseHeadersAddedInd())) {
					targetUrlChangeClickHouseEntity.setResponseHeadersAddedInd(true);
					targetUrlChangeClickHouseEntity.setResponseHeadersCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getResponse_headers());
					targetUrlChangeClickHouseEntity.setResponseHeadersPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getResponse_headers());
				}
			}
			// response_headers_removed_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.RESPONSE_HEADERS_REMOVED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getResponseHeadersRemovedInd())) {
					targetUrlChangeClickHouseEntity.setResponseHeadersRemovedInd(true);
					targetUrlChangeClickHouseEntity.setResponseHeadersCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getResponse_headers());
					targetUrlChangeClickHouseEntity.setResponseHeadersPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getResponse_headers());
				}
			}
			// robots_added_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.ROBOTS_ADDED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getRobotsAddedInd())) {
					targetUrlChangeClickHouseEntity.setRobotsAddedInd(true);
					targetUrlChangeClickHouseEntity.setRobotsContentsCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getRobots_contents());
					targetUrlChangeClickHouseEntity.setRobotsContentsPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getRobots_contents());
				}
			}
			// robots_contents_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.ROBOTS_CONTENTS_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getRobotsContentsChgInd())) {
					targetUrlChangeClickHouseEntity.setRobotsContentsChgInd(true);
					targetUrlChangeClickHouseEntity.setRobotsContentsCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getRobots_contents());
					targetUrlChangeClickHouseEntity.setRobotsContentsPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getRobots_contents());
				}
			}
			// robots_removed_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.ROBOTS_REMOVED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getRobotsRemovedInd())) {
					targetUrlChangeClickHouseEntity.setRobotsRemovedInd(true);
					targetUrlChangeClickHouseEntity.setRobotsContentsCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getRobots_contents());
					targetUrlChangeClickHouseEntity.setRobotsContentsPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getRobots_contents());
				}
			}
			// structured_data_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.STRUCTURED_DATA_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getStructuredDataChgInd())) {
					targetUrlChangeClickHouseEntity.setStructuredDataChgInd(true);
					targetUrlChangeClickHouseEntity.setStructuredDataCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getStructured_data());
					targetUrlChangeClickHouseEntity.setStructuredDataPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getStructured_data());
				}
			}
			// title_added_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.TITLE_ADDED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getTitleAddedInd())) {
					targetUrlChangeClickHouseEntity.setTitleAddedInd(true);
					targetUrlChangeClickHouseEntity.setTitleCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getTitle());
					targetUrlChangeClickHouseEntity.setTitlePrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getTitle());
				}
			}
			// title_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.TITLE_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getTitleChgInd())) {
					targetUrlChangeClickHouseEntity.setTitleChgInd(true);
					targetUrlChangeClickHouseEntity.setTitleCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getTitle());
					targetUrlChangeClickHouseEntity.setTitlePrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getTitle());
				}
			}
			// title_removed_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.TITLE_REMOVED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getTitleRemovedInd())) {
					targetUrlChangeClickHouseEntity.setTitleRemovedInd(true);
					targetUrlChangeClickHouseEntity.setTitleCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getTitle());
					targetUrlChangeClickHouseEntity.setTitlePrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getTitle());
				}
			}
			// title_length_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.TITLE_LENGTH_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getTitleLengthChgInd())) {
					targetUrlChangeClickHouseEntity.setTitleLengthChgInd(true);
					targetUrlChangeClickHouseEntity.setTitleLengthCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getTitle_length());
					targetUrlChangeClickHouseEntity.setTitleLengthPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getTitle_length());
				}
			}
			// viewport_added_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.VIEWPORT_ADDED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getViewportAddedInd())) {
					targetUrlChangeClickHouseEntity.setViewportAddedInd(true);
					targetUrlChangeClickHouseEntity.setViewportContentCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getViewport_content());
					targetUrlChangeClickHouseEntity.setViewportContentPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getViewport_content());
				}
			}
			// viewport_content_chg_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.VIEWPORT_CONTENT_CHG_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getViewportContentChgInd())) {
					targetUrlChangeClickHouseEntity.setViewportContentChgInd(true);
					targetUrlChangeClickHouseEntity.setViewportContentCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getViewport_content());
					targetUrlChangeClickHouseEntity.setViewportContentPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getViewport_content());
				}
			}
			// viewport_removed_ind
			else if (StringUtils.equalsIgnoreCase(changeTrackingIndicator, IConstants.VIEWPORT_REMOVED_IND)) {
				if (BooleanUtils.isTrue(htmlClickHouseEntityIndicators.getViewportRemovedInd())) {
					targetUrlChangeClickHouseEntity.setViewportRemovedInd(true);
					targetUrlChangeClickHouseEntity.setViewportContentCurrent(htmlClickHouseEntityCurrent.getCrawlerResponse().getViewport_content());
					targetUrlChangeClickHouseEntity.setViewportContentPrevious(htmlClickHouseEntityPrevious.getCrawlerResponse().getViewport_content());
				}
			}
		}

		return targetUrlChangeClickHouseEntity;
	}

	private void prepareToMaintainDataStores(String ip, List<TargetUrlChangeIndClickHouseEntity> targetUrlChangeIndClickHouseEntityList) throws Exception {

		// calculate current timestamp in yyyymmddhhmm format (ie. to the minute)
		String currentTimestampString = DateFormatUtils.format(new Date(), IConstants.DATE_FORMAT_YYYYMMDDHHMM);

		// cache the data to be created and the messages to be deleted for the current timestamp (ie. to the minute)
		CrawlerUtils.getInstance().cacheTargetUrlChangeIndDataToBeCreated(currentTimestampString, targetUrlChangeIndClickHouseEntityList);

		// when all the cached data created before one minute ago exceeded batch creation size
		if (checkIfReadyToMaintainDataStores(ip, currentTimestampString) == true) {
			CrawlerUtils.getInstance().maintainTargetUrlChangeIndDataStore(ip, currentTimestampString);
		}
	}

	private Boolean checkIfReadyToMaintainDataStores(String ip, String currentTimestampString) throws Exception {
		Boolean isReadyToCreateData = false;
		int totalRecordsCached = 0;
		int batchCreationSize = TargetUrlChangeIndClickHouseDAO.getInstance().getBatchCreationSize();

		// calculate total records cached before current timestamp
		for (String cachedTimestampString : UpdateTargetUrlChange.getTimestampTargetUrlChangeIndClickHouseEntityListMap().keySet()) {
			if (StringUtils.equalsIgnoreCase(currentTimestampString, cachedTimestampString) == false) {
				if (UpdateTargetUrlChange.getTimestampTargetUrlChangeIndClickHouseEntityListMap().get(cachedTimestampString) != null
						&& UpdateTargetUrlChange.getTimestampTargetUrlChangeIndClickHouseEntityListMap().get(cachedTimestampString).size() > 0) {
					totalRecordsCached = totalRecordsCached
							+ UpdateTargetUrlChange.getTimestampTargetUrlChangeIndClickHouseEntityListMap().get(cachedTimestampString).size();
				}
			}
		}

		// when total cached records >= batch creation size
		if (totalRecordsCached >= batchCreationSize) {
			isReadyToCreateData = true;
		}
		return isReadyToCreateData;
	}
}
