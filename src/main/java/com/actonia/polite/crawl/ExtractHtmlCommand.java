package com.actonia.polite.crawl;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;

import com.actonia.IConstants;
import com.actonia.concurrency.BaseThreadCommand;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.entity.AdditionalContentEntity;
import com.actonia.utils.CrawlerUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.value.object.ExtractHtmlValueObject;
import com.actonia.value.object.ScrapyCrawlerResponse;

public class ExtractHtmlCommand extends BaseThreadCommand {

	//private boolean isDebug = false;
	private boolean isResponseAsHtml = true;
	private static final String userAgentGoogleBot = "Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)";
	private static final String NOT_AVAILABLE = "n/a";
	private int totalResponseCode999 = 0;
	private static final String HTML_FILE_NAME_PREFIX = "htmlFile";
	private static final String HTML_FILE_NAME_SUFFIX = ".txt";

	private String ip;
	private String domainName;
	private String htmlFolderLocation;

	public ExtractHtmlCommand(String ip, String domainName, String htmlFolderLocation) {
		super();
		this.ip = ip;
		this.domainName = domainName;
		this.htmlFolderLocation = htmlFolderLocation;
	}

	@Override
	protected void undo() throws Exception {
		// TODO Auto-generated method stub
	}

	@Override
	protected void execute() {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("execute() begins. ip=" + ip + ",domainName=" + domainName);
		try {
			process();
		} catch (Exception e) {
			FormatUtils.getInstance().logMemoryUsage("execute() ip=" + ip + ",domainName=" + domainName + ",exception message=" + e.getMessage());
			e.printStackTrace();
		} finally {
			CacheModleFactory.getInstance().setAliveIpAddress(ip);
			FormatUtils.getInstance().logMemoryUsage(
					"execute() ends. ip=" + ip + ",domainName=" + domainName + ",elapsed time in sec.=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		}
	}

	private void process() throws Exception {
		ExtractHtmlValueObject extractHtmlValueObject = null;
		String urlString = null;
		String outputLine = null;
		List<String> outputLineList = new ArrayList<String>();
		Set<String> urlHashCodeSet = null;
		if (ExtractHtml.getDomainNameUrlHashSetMap().containsKey(domainName)) {
			urlHashCodeSet = ExtractHtml.getDomainNameUrlHashSetMap().get(domainName);
			if (urlHashCodeSet != null && urlHashCodeSet.size() > 0) {
				FormatUtils.getInstance().logMemoryUsage("process() ip=" + ip + ",domainName=" + domainName + ",urlHashCodeSet.size()=" + urlHashCodeSet.size());
				nextUrlHashCode: for (String urlHashCode : urlHashCodeSet) {
					urlString = ExtractHtml.getUrlHashCodeUrlStringMap().get(urlHashCode);
					extractHtmlValueObject = invokePageCrawler(urlString);
					if (StringUtils.equalsIgnoreCase(extractHtmlValueObject.getResponseCode(), "999")) {
						totalResponseCode999++;
					}
					outputLine = getOutputLine(extractHtmlValueObject);
					outputLineList.add(outputLine);
					//if (isDebug == true) {
					//	System.out.println("process() extractHtmlValueObject=" + extractHtmlValueObject.toString());
					//	if (outputLineList.size() >= 1) {
					//		break nextUrlHashCode;
					//	}
					//}
				}
			}
			FormatUtils.getInstance().logMemoryUsage("process() ip=" + ip + ",domainName=" + domainName + ",outputLineList.size()=" + outputLineList.size());
			if (outputLineList != null && outputLineList.size() > 0) {
				ExtractHtml.getOutputList().addAll(outputLineList);
			}
		}
	}

	private ExtractHtmlValueObject invokePageCrawler(String urlString) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		ExtractHtmlValueObject extractHtmlValueObject = new ExtractHtmlValueObject();
		String queueName = "";
		boolean isJavascriptCrawler = false;
		boolean isStoreHtml = false;
		String responseCode = null;
		List<AdditionalContentEntity> additionalContentEntityList = null;
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = null;

		String crawledUrl = urlString;
		if (totalResponseCode999 >= 3) {
			responseCode = "999";
			extractHtmlValueObject = getExtractHtmlValueObject(crawledUrl, responseCode);
		} else {
			scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(ip, queueName, urlString, userAgentGoogleBot, additionalContentEntityList,
					isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, null, isStoreHtml, null, isResponseAsHtml, null);
			if (scrapyCrawlerResponse != null) {
				extractHtmlValueObject = getExtractHtmlValueObject(scrapyCrawlerResponse, crawledUrl);
			} else {
				responseCode = NOT_AVAILABLE;
				extractHtmlValueObject = getExtractHtmlValueObject(crawledUrl, responseCode);
			}
		}
		FormatUtils.getInstance().logMemoryUsage("invokePageCrawler() ip=" + ip + ",domainName=" + domainName + ",urlString=" + urlString + ",responseCode="
				+ extractHtmlValueObject.getResponseCode() + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return extractHtmlValueObject;
	}

	private String getOutputLine(ExtractHtmlValueObject extractHtmlValueObject) {
		StringBuilder stringBuilder = new StringBuilder();
		String outputLine = null;
		stringBuilder = new StringBuilder();
		stringBuilder.append(extractHtmlValueObject.getUrlString());
		stringBuilder.append(IConstants.BACKTICK);
		stringBuilder.append(extractHtmlValueObject.getResponseCode());
		stringBuilder.append(IConstants.BACKTICK);

		if (StringUtils.equalsIgnoreCase(extractHtmlValueObject.getResponseCode(), "200")) {
			stringBuilder.append(extractHtmlValueObject.getHtmlFileName());
		} else {
			stringBuilder.append(IConstants.EMPTY_STRING);
		}

		outputLine = stringBuilder.toString();
		//System.out.println("o=" + outputLine);
		return outputLine;
	}

	private ExtractHtmlValueObject getExtractHtmlValueObject(String crawledUrl, String responseCode) {
		ExtractHtmlValueObject extractHtmlValueObject = new ExtractHtmlValueObject();
		extractHtmlValueObject.setUrlString(crawledUrl);
		extractHtmlValueObject.setResponseCode(responseCode);
		extractHtmlValueObject.setHtmlFileName(IConstants.EMPTY_STRING);
		return extractHtmlValueObject;
	}

	private ExtractHtmlValueObject getExtractHtmlValueObject(ScrapyCrawlerResponse scrapyCrawlerResponse, String crawledUrl) throws Exception {

		File file = null;
		String htmlFileName = null;

		ExtractHtmlValueObject extractHtmlValueObject = new ExtractHtmlValueObject();

		String responseCode = String.valueOf(scrapyCrawlerResponse.getStatus());

		// URL
		extractHtmlValueObject.setUrlString(crawledUrl);

		// response code
		extractHtmlValueObject.setResponseCode(responseCode);

		if (StringUtils.equalsIgnoreCase(responseCode, "200")) {
			htmlFileName = getHtmlFileName();
			file = new File(htmlFolderLocation + htmlFileName);
			FileUtils.writeStringToFile(file, scrapyCrawlerResponse.getHtml(), StandardCharsets.UTF_8);
			// HTML file name
			extractHtmlValueObject.setHtmlFileName(htmlFileName);
		} else {
			// HTML file name
			extractHtmlValueObject.setHtmlFileName(IConstants.EMPTY_STRING);
		}

		return extractHtmlValueObject;

	}

	private String getHtmlFileName() {
		int nextFileNumber = ExtractHtml.getNextFileNumber();
		return HTML_FILE_NAME_PREFIX + String.valueOf(nextFileNumber) + HTML_FILE_NAME_SUFFIX;
	}
}
