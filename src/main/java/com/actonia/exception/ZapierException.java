package com.actonia.exception;

public class ZapierException extends Exception {
	private static final long serialVersionUID = -1946719140252127322L;
	private int httpStatusCode;
	private String httpReasonPhrase;

	public ZapierException(int httpStatusCode, String httpReasonPhrase) {
		super();
		this.httpStatusCode = httpStatusCode;
		this.httpReasonPhrase = httpReasonPhrase;
	}

	public int getHttpStatusCode() {
		return httpStatusCode;
	}

	public void setHttpStatusCode(int httpStatusCode) {
		this.httpStatusCode = httpStatusCode;
	}

	public String getHttpReasonPhrase() {
		return httpReasonPhrase;
	}

	public void setHttpReasonPhrase(String httpReasonPhrase) {
		this.httpReasonPhrase = httpReasonPhrase;
	}

	@Override
	public String toString() {
		return "ZapierException [httpStatusCode=" + httpStatusCode + ", httpReasonPhrase=" + httpReasonPhrase + "]";
	}

}
