package com.actonia.exception;

public class SendStatusCodeAlertException extends Exception {

	private static final long serialVersionUID = 1863784369275829475L;

	private String errorMessage;
	
	private boolean stopIndicator = false;
	
	private int httpStatusCode = 0;

	public SendStatusCodeAlertException() {
		super();
	}

	public SendStatusCodeAlertException(String errorMessage) {
		super();
		this.errorMessage = errorMessage;
	}

	public SendStatusCodeAlertException(int httpStatusCode) {
		super();
		this.httpStatusCode = httpStatusCode;
	}

	public String getErrorMessage() {
		return errorMessage;
	}

	public void setErrorMessage(String errorMessage) {
		this.errorMessage = errorMessage;
	}

	@Override
	public String getMessage() {
		return errorMessage;
	}

	public boolean isStopIndicator() {
		return stopIndicator;
	}

	public void setStopIndicator(boolean stopIndicator) {
		this.stopIndicator = stopIndicator;
	}

	public int getHttpStatusCode() {
		return httpStatusCode;
	}

	public void setHttpStatusCode(int httpStatusCode) {
		this.httpStatusCode = httpStatusCode;
	}

}
