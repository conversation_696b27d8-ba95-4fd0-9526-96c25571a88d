package com.actonia.exception;

public class ExceededTotalRowsException extends Exception {

	private static final long serialVersionUID = 1284493934852925861L;
	private int totalRows;

	public ExceededTotalRowsException(int totalRows) {
		this.totalRows = totalRows;
	}

	public int getTotalRows() {
		return totalRows;
	}

	public void setTotalRows(int totalRows) {
		this.totalRows = totalRows;
	}

	@Override
	public String toString() {
		return "ExceededTotalRowsException [totalRows=" + totalRows + "]";
	}

}
