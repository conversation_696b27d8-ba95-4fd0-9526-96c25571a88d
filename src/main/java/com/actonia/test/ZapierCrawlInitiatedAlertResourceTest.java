package com.actonia.test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import com.actonia.IConstants;
import com.actonia.dao.ZapierWebhookDAO;
import com.actonia.entity.ZapierWebhookEntity;
import com.actonia.service.AccessTokenService;
import com.actonia.service.PoliteCrawlWebServiceClientService;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.ZapierCrawlInitiatedAlert;
import com.actonia.value.object.ZapierResourceRequest;
import com.actonia.value.object.ZapierResourceResponse;
import com.google.gson.Gson;

public class ZapierCrawlInitiatedAlertResourceTest {

	private boolean isUnitTest = true;
	private PoliteCrawlWebServiceClientService politeCrawlWebServiceClientService;
	private ZapierWebhookDAO zapierWebhookDAO;

	public ZapierCrawlInitiatedAlertResourceTest() {
		super();
		this.politeCrawlWebServiceClientService = SpringBeanFactory.getBean("politeCrawlWebServiceClientService");
		this.zapierWebhookDAO = SpringBeanFactory.getBean("zapierWebhookDAO");
	}

	public static void main(String[] args) throws Exception {
		new ZapierCrawlInitiatedAlertResourceTest().runTests();
	}

	public void runTests() throws Exception {

		testCase1();
		testCase2();
		if (isUnitTest == true) {
			testCase3();
		}
		testCase4();
		testCase5();
		testCase8();
		testCase9();
		testCase12();
		testCase13();
		testCase14();
		testCase15();
		testCase16();
		testCase17();
		testCase18();
		testCase19();
		testCase20();
		testCase21();
		testCase22();
		testCase23();
	}

	private void testCase1() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint()
				+ PoliteCrawlWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_INITIATED_ALERT;
		System.out.println("testCase1() requestUrl=" + requestUrl);
		String requestParameters = IConstants.EMPTY_STRING;

		ZapierResourceResponse zapierResourceResponse = politeCrawlWebServiceClientService.zapierPost(requestUrl, requestParameters);
		assertNotNull("testCase1() zapierResourceResponse should not be null.", zapierResourceResponse);
		String json = new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class);
		assertEquals("testCase1() json is incorrect", "{\"error_code\":\"00001\",\"error_message\":\"Request data is required.\"}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase1() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase2() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint()
				+ PoliteCrawlWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_INITIATED_ALERT;
		System.out.println("testCase2() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierCrawlInitiatedAlertResourceRequest = new ZapierResourceRequest();
		String requestParameters = new Gson().toJson(zapierCrawlInitiatedAlertResourceRequest, ZapierResourceRequest.class);

		ZapierResourceResponse zapierResourceResponse = politeCrawlWebServiceClientService.zapierPost(requestUrl, requestParameters);
		assertNotNull("testCase2() zapierResourceResponse should not be null.", zapierResourceResponse);
		String json = new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class);
		assertEquals("testCase2() json is incorrect", "{\"error_code\":\"00002\",\"error_message\":\"Request parameter access_token is required.\"}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase2() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase3() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint()
				+ PoliteCrawlWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_INITIATED_ALERT;
		System.out.println("testCase3() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierCrawlInitiatedAlertResourceRequest = new ZapierResourceRequest();
		zapierCrawlInitiatedAlertResourceRequest.setAccess_token("testaccesstoken");
		String requestParameters = new Gson().toJson(zapierCrawlInitiatedAlertResourceRequest, ZapierResourceRequest.class);

		ZapierResourceResponse zapierResourceResponse = politeCrawlWebServiceClientService.zapierPost(requestUrl, requestParameters);
		assertNotNull("testCase3() zapierResourceResponse should not be null.", zapierResourceResponse);
		String json = new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class);
		assertEquals("testCase3() json is incorrect", "{\"error_code\":\"00003\",\"error_message\":\"Request parameter access_token testaccesstoken is invalid.\"}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase3() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase4() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint()
				+ PoliteCrawlWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_INITIATED_ALERT;
		System.out.println("testCase4() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierCrawlInitiatedAlertResourceRequest = new ZapierResourceRequest();
		zapierCrawlInitiatedAlertResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		String requestParameters = new Gson().toJson(zapierCrawlInitiatedAlertResourceRequest, ZapierResourceRequest.class);

		ZapierResourceResponse zapierResourceResponse = politeCrawlWebServiceClientService.zapierPost(requestUrl, requestParameters);
		assertNotNull("testCase4() zapierResourceResponse should not be null.", zapierResourceResponse);
		String json = new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class);
		assertEquals("testCase4() json is incorrect", "{\"error_code\":\"00004\",\"error_message\":\"Request parameter domain_id is required.\"}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase4() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase5() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint()
				+ PoliteCrawlWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_INITIATED_ALERT;
		System.out.println("testCase5() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierCrawlInitiatedAlertResourceRequest = new ZapierResourceRequest();
		zapierCrawlInitiatedAlertResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		zapierCrawlInitiatedAlertResourceRequest.setDomain_id(1);
		String requestParameters = new Gson().toJson(zapierCrawlInitiatedAlertResourceRequest, ZapierResourceRequest.class);

		ZapierResourceResponse zapierResourceResponse = politeCrawlWebServiceClientService.zapierPost(requestUrl, requestParameters);
		assertNotNull("testCase5() zapierResourceResponse should not be null.", zapierResourceResponse);
		String json = new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class);
		assertEquals("testCase5() json is incorrect", "{\"error_code\":\"00005\",\"error_message\":\"Request parameter domain_id 1 is invalid.\"}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase5() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase8() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint()
				+ PoliteCrawlWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_INITIATED_ALERT;
		System.out.println("testCase8() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierCrawlInitiatedAlertResourceRequest = new ZapierResourceRequest();
		zapierCrawlInitiatedAlertResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		zapierCrawlInitiatedAlertResourceRequest.setDomain_id(1701);
		String requestParameters = new Gson().toJson(zapierCrawlInitiatedAlertResourceRequest, ZapierResourceRequest.class);

		ZapierResourceResponse zapierResourceResponse = politeCrawlWebServiceClientService.zapierPost(requestUrl, requestParameters);
		assertNotNull("testCase8() zapierResourceResponse should not be null.", zapierResourceResponse);
		String json = new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class);
		assertEquals("testCase8() json is incorrect", "{\"error_code\":\"00006\",\"error_message\":\"Request parameter user_email is required.\"}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase8() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase9() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint()
				+ PoliteCrawlWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_INITIATED_ALERT;
		System.out.println("testCase9() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierCrawlInitiatedAlertResourceRequest = new ZapierResourceRequest();
		zapierCrawlInitiatedAlertResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		zapierCrawlInitiatedAlertResourceRequest.setDomain_id(1701);
		zapierCrawlInitiatedAlertResourceRequest.setUser_email("<EMAIL>");
		String requestParameters = new Gson().toJson(zapierCrawlInitiatedAlertResourceRequest, ZapierResourceRequest.class);

		ZapierResourceResponse zapierResourceResponse = politeCrawlWebServiceClientService.zapierPost(requestUrl, requestParameters);
		assertNotNull("testCase9() zapierResourceResponse should not be null.", zapierResourceResponse);
		String json = new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class);
		assertEquals("testCase9() json is incorrect", "{\"error_code\":\"00007\",\"error_message\":\"Request parameter user_email <EMAIL> is invalid.\"}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase9() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase12() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint()
				+ PoliteCrawlWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_INITIATED_ALERT;
		System.out.println("testCase12() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierCrawlInitiatedAlertResourceRequest = new ZapierResourceRequest();
		zapierCrawlInitiatedAlertResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		zapierCrawlInitiatedAlertResourceRequest.setDomain_id(1701);
		zapierCrawlInitiatedAlertResourceRequest.setUser_email("<EMAIL>");
		String requestParameters = new Gson().toJson(zapierCrawlInitiatedAlertResourceRequest, ZapierResourceRequest.class);

		ZapierResourceResponse zapierResourceResponse = politeCrawlWebServiceClientService.zapierPost(requestUrl, requestParameters);
		assertNotNull("testCase12() zapierResourceResponse should not be null.", zapierResourceResponse);
		String json = new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class);
		assertEquals("testCase12() json is incorrect", "{\"error_code\":\"00008\",\"error_message\":\"Request parameter callback_url is required.\"}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase12() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase13() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint()
				+ PoliteCrawlWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_INITIATED_ALERT;
		System.out.println("testCase13() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierCrawlInitiatedAlertResourceRequest = new ZapierResourceRequest();
		zapierCrawlInitiatedAlertResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		zapierCrawlInitiatedAlertResourceRequest.setDomain_id(1701);
		zapierCrawlInitiatedAlertResourceRequest.setUser_email("<EMAIL>");
		zapierCrawlInitiatedAlertResourceRequest.setCallback_url("test");
		String requestParameters = new Gson().toJson(zapierCrawlInitiatedAlertResourceRequest, ZapierResourceRequest.class);

		ZapierResourceResponse zapierResourceResponse = politeCrawlWebServiceClientService.zapierPost(requestUrl, requestParameters);
		assertNotNull("testCase13() zapierResourceResponse should not be null.", zapierResourceResponse);
		String json = new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class);
		assertEquals("testCase13() json is incorrect", "{\"error_code\":\"00009\",\"error_message\":\"Request parameter callback_url test is invalid.\"}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase13() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase14() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		ZapierWebhookEntity zapierWebhookEntity = null;
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint()
				+ PoliteCrawlWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_INITIATED_ALERT;
		System.out.println("testCase14() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierCrawlInitiatedAlertResourceRequest = new ZapierResourceRequest();
		zapierCrawlInitiatedAlertResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		zapierCrawlInitiatedAlertResourceRequest.setDomain_id(1701);
		zapierCrawlInitiatedAlertResourceRequest.setUser_email("<EMAIL>");
		zapierCrawlInitiatedAlertResourceRequest.setCallback_url("https://hook.zapier.com/callback_url_1");
		String requestParameters = new Gson().toJson(zapierCrawlInitiatedAlertResourceRequest, ZapierResourceRequest.class);

		ZapierResourceResponse zapierResourceResponse = politeCrawlWebServiceClientService.zapierPost(requestUrl, requestParameters);
		assertNotNull("testCase14() zapierResourceResponse should not be null.", zapierResourceResponse);
		assertNotNull("testCase14() zapierResourceResponse.getId() should not be null.", zapierResourceResponse.getId());
		if (isUnitTest == true) {
			zapierWebhookEntity = zapierWebhookDAO.get(zapierResourceResponse.getId());
			assertNotNull("testCase14() zapierWebhookEntity should not be null.", zapierWebhookEntity);
			assertEquals("testCase14() zapierWebhookEntity.getDomainId() is incorrect.", "1701", String.valueOf(zapierWebhookEntity.getDomainId()));
			assertEquals("testCase14() zapierWebhookEntity.getUserId() is incorrect.", "214", String.valueOf(zapierWebhookEntity.getUserId()));
			assertEquals("testCase14() zapierWebhookEntity.getTriggerType() is incorrect.", "2", String.valueOf(zapierWebhookEntity.getTriggerType()));
			assertEquals("testCase14() zapierWebhookEntity.getCallbackUrl() is incorrect.", "https://hook.zapier.com/callback_url_1",
					String.valueOf(zapierWebhookEntity.getCallbackUrl()));
			zapierWebhookDAO.delete(zapierResourceResponse.getId());
		}
		FormatUtils.getInstance().logMemoryUsage("testCase14() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase15() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint()
				+ PoliteCrawlWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_INITIATED_ALERT;
		System.out.println("testCase15() requestUrl=" + requestUrl);
		ZapierResourceResponse zapierResourceResponse = politeCrawlWebServiceClientService.zapierDelete(requestUrl);
		assertNotNull("testCase15() zapierResourceResponse should not be null.", zapierResourceResponse);
		String json = new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class);
		assertEquals("testCase15() json is incorrect", "{\"error_code\":\"00002\",\"error_message\":\"Request parameter access_token is required.\"}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase15() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase16() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint()
				+ PoliteCrawlWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_INITIATED_ALERT + "?access_token=testaccesstoken";
		System.out.println("testCase16() requestUrl=" + requestUrl);
		ZapierResourceResponse zapierResourceResponse = politeCrawlWebServiceClientService.zapierDelete(requestUrl);
		assertNotNull("testCase16() zapierResourceResponse should not be null.", zapierResourceResponse);
		String json = new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class);
		assertEquals("testCase16() json is incorrect", "{\"error_code\":\"00003\",\"error_message\":\"Request parameter access_token testaccesstoken is invalid.\"}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase16() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase17() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint()
				+ PoliteCrawlWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_INITIATED_ALERT + "?access_token=" + AccessTokenService.INTERNAL_KEY;
		System.out.println("testCase17() requestUrl=" + requestUrl);
		ZapierResourceResponse zapierResourceResponse = politeCrawlWebServiceClientService.zapierDelete(requestUrl);
		assertNotNull("testCase17() zapierResourceResponse should not be null.", zapierResourceResponse);
		String json = new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class);
		assertEquals("testCase17() json is incorrect", "{\"error_code\":\"00012\",\"error_message\":\"Request parameter id is required.\"}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase17() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase18() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint()
				+ PoliteCrawlWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_INITIATED_ALERT + "?access_token=" + AccessTokenService.INTERNAL_KEY + "&id=abcd";
		System.out.println("testCase18() requestUrl=" + requestUrl);
		ZapierResourceResponse zapierResourceResponse = politeCrawlWebServiceClientService.zapierDelete(requestUrl);
		assertNotNull("testCase18() zapierResourceResponse should not be null.", zapierResourceResponse);
		String json = new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class);
		assertEquals("testCase18() json is incorrect", "{\"error_code\":\"00013\",\"error_message\":\"Request parameter id abcd is invalid.\"}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase18() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase19() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint()
				+ PoliteCrawlWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_INITIATED_ALERT + "?access_token=" + AccessTokenService.INTERNAL_KEY + "&id=168168";
		System.out.println("testCase19() requestUrl=" + requestUrl);
		ZapierResourceResponse zapierResourceResponse = politeCrawlWebServiceClientService.zapierDelete(requestUrl);
		assertNotNull("testCase19() zapierResourceResponse should not be null.", zapierResourceResponse);
		String json = new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class);
		assertEquals("testCase19() json is incorrect", "{\"error_code\":\"00013\",\"error_message\":\"Request parameter id 168168 is invalid.\"}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase19() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase20() throws Exception {
		long startTimestamp = System.currentTimeMillis();

		// step 1: create subscription
		ZapierWebhookEntity zapierWebhookEntity = null;
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint()
				+ PoliteCrawlWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_INITIATED_ALERT;
		System.out.println("testCase20() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierCrawlInitiatedAlertResourceRequest = new ZapierResourceRequest();
		zapierCrawlInitiatedAlertResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		zapierCrawlInitiatedAlertResourceRequest.setDomain_id(1701);
		zapierCrawlInitiatedAlertResourceRequest.setUser_email("<EMAIL>");
		zapierCrawlInitiatedAlertResourceRequest.setCallback_url("https://hook.zapier.com/callback_url_1");
		String requestParameters = new Gson().toJson(zapierCrawlInitiatedAlertResourceRequest, ZapierResourceRequest.class);

		ZapierResourceResponse zapierResourceResponse = politeCrawlWebServiceClientService.zapierPost(requestUrl, requestParameters);
		assertNotNull("testCase20() zapierResourceResponse should not be null.", zapierResourceResponse);
		assertNotNull("testCase20() zapierResourceResponse.getId() should not be null.", zapierResourceResponse.getId());
		if (isUnitTest == true) {
			zapierWebhookEntity = zapierWebhookDAO.get(zapierResourceResponse.getId());
			assertNotNull("testCase20() zapierWebhookEntity should not be null.", zapierWebhookEntity);
			assertEquals("testCase20() zapierWebhookEntity.getDomainId() is incorrect.", "1701", String.valueOf(zapierWebhookEntity.getDomainId()));
			assertEquals("testCase20() zapierWebhookEntity.getUserId() is incorrect.", "214", String.valueOf(zapierWebhookEntity.getUserId()));
			assertEquals("testCase20() zapierWebhookEntity.getTriggerType() is incorrect.", "2", String.valueOf(zapierWebhookEntity.getTriggerType()));
			assertEquals("testCase20() zapierWebhookEntity.getCallbackUrl() is incorrect.", "https://hook.zapier.com/callback_url_1",
					String.valueOf(zapierWebhookEntity.getCallbackUrl()));
		}

		// step 2: delete subscription		
		requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_INITIATED_ALERT
				+ "?access_token=" + AccessTokenService.INTERNAL_KEY + "&id=" + zapierResourceResponse.getId();
		System.out.println("testCase20() requestUrl=" + requestUrl);
		zapierResourceResponse = politeCrawlWebServiceClientService.zapierDelete(requestUrl);
		assertNotNull("testCase20() zapierResourceResponse should not be null.", zapierResourceResponse);
		String json = new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class);
		System.out.println("testCase20() json=" + json);
		FormatUtils.getInstance().logMemoryUsage("testCase20() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase21() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint()
				+ PoliteCrawlWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_INITIATED_ALERT;
		System.out.println("testCase21() requestUrl=" + requestUrl);
		ZapierCrawlInitiatedAlert[] zapierCrawlInitiatedAlertArray = politeCrawlWebServiceClientService.zapierCrawlInitiatedAlertGet(requestUrl);
		assertNotNull("testCase21() zapierCrawlInitiatedAlertArray should not be null.", zapierCrawlInitiatedAlertArray);
		String json = new Gson().toJson(zapierCrawlInitiatedAlertArray, ZapierCrawlInitiatedAlert[].class);
		assertEquals("testCase21() json is incorrect", "[{\"error_code\":\"00002\",\"error_message\":\"Request parameter access_token is required.\"}]", json);
		FormatUtils.getInstance().logMemoryUsage("testCase21() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase22() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint()
				+ PoliteCrawlWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_INITIATED_ALERT + "?access_token=testaccesstoken";
		System.out.println("testCase22() requestUrl=" + requestUrl);
		ZapierCrawlInitiatedAlert[] zapierCrawlInitiatedAlertArray = politeCrawlWebServiceClientService.zapierCrawlInitiatedAlertGet(requestUrl);
		assertNotNull("testCase22() zapierCrawlInitiatedAlertArray should not be null.", zapierCrawlInitiatedAlertArray);
		String json = new Gson().toJson(zapierCrawlInitiatedAlertArray, ZapierCrawlInitiatedAlert[].class);
		assertEquals("testCase22() json is incorrect", "[{\"error_code\":\"00003\",\"error_message\":\"Request parameter access_token testaccesstoken is invalid.\"}]",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase22() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase23() throws Exception {
		long startTimestamp = System.currentTimeMillis();

		// retrieve sample content guard alert messages
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint()
				+ PoliteCrawlWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_INITIATED_ALERT + "?access_token=" + AccessTokenService.INTERNAL_KEY;
		System.out.println("testCase23() requestUrl=" + requestUrl);
		ZapierCrawlInitiatedAlert[] zapierCrawlInitiatedAlertArray = politeCrawlWebServiceClientService.zapierCrawlInitiatedAlertGet(requestUrl);
		assertNotNull("testCase23() zapierCrawlInitiatedAlertArray should not be null.", zapierCrawlInitiatedAlertArray);

		// first alert
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[0].getId() is incorrect.", "1614180689836", zapierCrawlInitiatedAlertArray[0].getId());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[0].getCrawl_start_timestamp() is incorrect.", "2021-02-24 09:28:03",
				zapierCrawlInitiatedAlertArray[0].getCrawl_start_timestamp());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[0].getDomain_id() is incorrect.", "123456",
				String.valueOf(zapierCrawlInitiatedAlertArray[0].getDomain_id()));
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[0].getDomain_name() is incorrect.", "www.testdomain1.com",
				zapierCrawlInitiatedAlertArray[0].getDomain_name());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[0].getRequest_id() is incorrect.", "234567",
				String.valueOf(zapierCrawlInitiatedAlertArray[0].getRequest_id()));
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[0].getRequester_email() is incorrect.", "<EMAIL>",
				zapierCrawlInitiatedAlertArray[0].getRequester_email());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[0].getProject() is incorrect.", "project name 1", zapierCrawlInitiatedAlertArray[0].getProject());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[0].getLanguage() is incorrect.", "English", zapierCrawlInitiatedAlertArray[0].getLanguage());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[0].getWhat_to_crawl() is incorrect.", "URL", zapierCrawlInitiatedAlertArray[0].getWhat_to_crawl());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[0].getStarting_url() is incorrect.", "https://www.testdomain1.com",
				zapierCrawlInitiatedAlertArray[0].getStarting_url());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[0].getCrawl_type() is incorrect.", "non-Javascript",
				zapierCrawlInitiatedAlertArray[0].getCrawl_type());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[0].getCrawl_speed() is incorrect.", "3",
				String.valueOf(zapierCrawlInitiatedAlertArray[0].getCrawl_speed()));
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[0].getCrawl_depth() is incorrect.", "8",
				String.valueOf(zapierCrawlInitiatedAlertArray[0].getCrawl_depth()));
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[0].getDescription() is incorrect.", "crawl description 1",
				zapierCrawlInitiatedAlertArray[0].getDescription());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[0].getUser_agent() is incorrect.", "user agent 1", zapierCrawlInitiatedAlertArray[0].getUser_agent());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[0].getObey_robots_txt() is incorrect.", true, zapierCrawlInitiatedAlertArray[0].getObey_robots_txt());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[0].getStore_blocked_links() is incorrect.", "store blocked links 1",
				zapierCrawlInitiatedAlertArray[0].getStore_blocked_links());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[0].getEnable_cookies() is incorrect.", true, zapierCrawlInitiatedAlertArray[0].getEnable_cookies());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[0].getRegion() is incorrect.", "New York 3", zapierCrawlInitiatedAlertArray[0].getRegion());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[0].getUrl_parameters_to_remove() is incorrect.", "url parameters to remove 1",
				zapierCrawlInitiatedAlertArray[0].getUrl_parameters_to_remove());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[0].getInternal_links_analysis() is incorrect.", true,
				zapierCrawlInitiatedAlertArray[0].getInternal_links_analysis());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[0].getHreflang_crawl() is incorrect.", true, zapierCrawlInitiatedAlertArray[0].getHreflang_crawl());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[0].getCanonical_crawl() is incorrect.", false,
				zapierCrawlInitiatedAlertArray[0].getCanonical_crawl());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[0].getAllow_domains() is incorrect.", "allow domains 1",
				zapierCrawlInitiatedAlertArray[0].getAllow_domains());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[0].getDeny_domains() is incorrect.", "deny domains 1",
				zapierCrawlInitiatedAlertArray[0].getDeny_domains());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[0].getFollow_nofollow_links() is incorrect.", true,
				zapierCrawlInitiatedAlertArray[0].getFollow_nofollow_links());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[0].getUrl_patterns_to_allow() is incorrect.", "url patterns to allow 1",
				zapierCrawlInitiatedAlertArray[0].getUrl_patterns_to_allow());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[0].getUrl_patterns_to_disallow() is incorrect.", "url patterns to disallow 1",
				zapierCrawlInitiatedAlertArray[0].getUrl_patterns_to_disallow());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[0].getUrls_to_crawl_but_not_index() is incorrect.", "urls to crawl but not index 1",
				zapierCrawlInitiatedAlertArray[0].getUrls_to_crawl_but_not_index());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[0].getUrls_to_index_but_not_crawl() is incorrect.", "urls to index but not crawl 1",
				zapierCrawlInitiatedAlertArray[0].getUrls_to_index_but_not_crawl());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[0].getRestrict_to_xpath() is incorrect.", "restrict to xpath 1",
				zapierCrawlInitiatedAlertArray[0].getRestrict_to_xpath());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[0].getRestrict_to_css() is incorrect.", "restrict to css 1",
				zapierCrawlInitiatedAlertArray[0].getRestrict_to_css());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[0].getAdditional_content() is incorrect.", "additional content 1",
				zapierCrawlInitiatedAlertArray[0].getAdditional_content());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[0].getCustom_search() is incorrect.", "custom search 1",
				zapierCrawlInitiatedAlertArray[0].getCustom_search());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[0].getError_code() is incorrect.", "", zapierCrawlInitiatedAlertArray[0].getError_code());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[0].getError_message() is incorrect.", "", zapierCrawlInitiatedAlertArray[0].getError_message());

		// second alert
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[1].getId() is incorrect.", "1614234567898", zapierCrawlInitiatedAlertArray[1].getId());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[1].getCrawl_start_timestamp() is incorrect.", "2021-02-25 16:33:08",
				zapierCrawlInitiatedAlertArray[1].getCrawl_start_timestamp());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[1].getDomain_id() is incorrect.", "345678",
				String.valueOf(zapierCrawlInitiatedAlertArray[1].getDomain_id()));
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[1].getDomain_name() is incorrect.", "www.testdomain2.com",
				zapierCrawlInitiatedAlertArray[1].getDomain_name());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[1].getRequest_id() is incorrect.", "567890",
				String.valueOf(zapierCrawlInitiatedAlertArray[1].getRequest_id()));
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[1].getRequester_email() is incorrect.", "<EMAIL>",
				zapierCrawlInitiatedAlertArray[1].getRequester_email());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[1].getProject() is incorrect.", "project name 2", zapierCrawlInitiatedAlertArray[1].getProject());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[1].getLanguage() is incorrect.", "French", zapierCrawlInitiatedAlertArray[1].getLanguage());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[1].getWhat_to_crawl() is incorrect.", "URL", zapierCrawlInitiatedAlertArray[1].getWhat_to_crawl());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[1].getStarting_url() is incorrect.", "https://www.testdomain2.com",
				zapierCrawlInitiatedAlertArray[1].getStarting_url());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[1].getCrawl_type() is incorrect.", "Javascript", zapierCrawlInitiatedAlertArray[1].getCrawl_type());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[1].getCrawl_speed() is incorrect.", "2",
				String.valueOf(zapierCrawlInitiatedAlertArray[1].getCrawl_speed()));
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[1].getCrawl_depth() is incorrect.", "6",
				String.valueOf(zapierCrawlInitiatedAlertArray[1].getCrawl_depth()));
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[1].getDescription() is incorrect.", "crawl description 2",
				zapierCrawlInitiatedAlertArray[1].getDescription());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[1].getUser_agent() is incorrect.", "user agent 2", zapierCrawlInitiatedAlertArray[1].getUser_agent());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[1].getObey_robots_txt() is incorrect.", false,
				zapierCrawlInitiatedAlertArray[1].getObey_robots_txt());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[1].getStore_blocked_links() is incorrect.", "store blocked links 2",
				zapierCrawlInitiatedAlertArray[1].getStore_blocked_links());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[1].getEnable_cookies() is incorrect.", false, zapierCrawlInitiatedAlertArray[1].getEnable_cookies());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[1].getRegion() is incorrect.", "London 1", zapierCrawlInitiatedAlertArray[1].getRegion());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[1].getUrl_parameters_to_remove() is incorrect.", "url parameters to remove 2",
				zapierCrawlInitiatedAlertArray[1].getUrl_parameters_to_remove());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[1].getInternal_links_analysis() is incorrect.", false,
				zapierCrawlInitiatedAlertArray[1].getInternal_links_analysis());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[1].getHreflang_crawl() is incorrect.", false, zapierCrawlInitiatedAlertArray[1].getHreflang_crawl());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[1].getCanonical_crawl() is incorrect.", true, zapierCrawlInitiatedAlertArray[1].getCanonical_crawl());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[1].getAllow_domains() is incorrect.", "allow domains 2",
				zapierCrawlInitiatedAlertArray[1].getAllow_domains());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[1].getDeny_domains() is incorrect.", "deny domains 2",
				zapierCrawlInitiatedAlertArray[1].getDeny_domains());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[1].getFollow_nofollow_links() is incorrect.", false,
				zapierCrawlInitiatedAlertArray[1].getFollow_nofollow_links());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[1].getUrl_patterns_to_allow() is incorrect.", "url patterns to allow 2",
				zapierCrawlInitiatedAlertArray[1].getUrl_patterns_to_allow());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[1].getUrl_patterns_to_disallow() is incorrect.", "url patterns to disallow 2",
				zapierCrawlInitiatedAlertArray[1].getUrl_patterns_to_disallow());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[1].getUrls_to_crawl_but_not_index() is incorrect.", "urls to crawl but not index 2",
				zapierCrawlInitiatedAlertArray[1].getUrls_to_crawl_but_not_index());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[1].getUrls_to_index_but_not_crawl() is incorrect.", "urls to index but not crawl 2",
				zapierCrawlInitiatedAlertArray[1].getUrls_to_index_but_not_crawl());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[1].getRestrict_to_xpath() is incorrect.", "restrict to xpath 2",
				zapierCrawlInitiatedAlertArray[1].getRestrict_to_xpath());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[1].getRestrict_to_css() is incorrect.", "restrict to css 2",
				zapierCrawlInitiatedAlertArray[1].getRestrict_to_css());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[1].getAdditional_content() is incorrect.", "additional content 2",
				zapierCrawlInitiatedAlertArray[1].getAdditional_content());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[1].getCustom_search() is incorrect.", "custom search 2",
				zapierCrawlInitiatedAlertArray[1].getCustom_search());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[1].getError_code() is incorrect.", "", zapierCrawlInitiatedAlertArray[1].getError_code());
		assertEquals("testCase23() zapierCrawlInitiatedAlertArray[1].getError_message() is incorrect.", "", zapierCrawlInitiatedAlertArray[1].getError_message());

		FormatUtils.getInstance().logMemoryUsage("testCase23() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

}