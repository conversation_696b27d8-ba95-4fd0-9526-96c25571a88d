package com.actonia.test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import java.io.File;
import java.math.BigInteger;
import java.net.URL;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.dao.AlertMetricsNameClickHouseDAO;
import com.actonia.dao.CompetitorUrlMd5EntityDAO;
import com.actonia.dao.ContentGuardClickHouseDAO;
import com.actonia.dao.ContentGuardUsageDAO;
import com.actonia.dao.LocalTargetUrlHtmlDailyClickHouseDAO;
import com.actonia.dao.OwnDomainEntityDAO;
import com.actonia.dao.RankingDetailClickHouseDAO;
import com.actonia.dao.TargetUrlChangeIndClickHouseDAO;
import com.actonia.dao.TargetUrlCrawlAdditionalContentEntityDAO;
import com.actonia.dao.TargetUrlHtmlClickHouseDAO;
import com.actonia.entity.AlertMetricsNameClickHouseEntity;
import com.actonia.entity.ContentGuardUsageEntity;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.OptionEntity;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
import com.actonia.entity.TargetUrlEntity;
import com.actonia.entity.UrlMetricsEntityV3;
import com.actonia.service.AlertMetricsNameClickHouseService;
import com.actonia.service.ScKeywordRankService;
import com.actonia.utils.CommonUtils;
import com.actonia.utils.Configurations;
import com.actonia.utils.CrawlerUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.Md5Util;
import com.actonia.utils.PutMessageUtils;
import com.actonia.utils.RankCheckUtils;
import com.actonia.utils.SQSUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.utils.UrlMetricsUtil;
import com.actonia.value.object.AdditionalContentFilterValueObject;
import com.actonia.value.object.AlternateLinks;
import com.actonia.value.object.CheckStructuredData;
import com.actonia.value.object.ContentGuardChangeDetails;
import com.actonia.value.object.ContentGuardDailyGroupSeverity;
import com.actonia.value.object.ContentGuardDailyGroupTimeline;
import com.actonia.value.object.ContentGuardHourlyGroupSeverity;
import com.actonia.value.object.ContentGuardHourlyGroupTimeline;
import com.actonia.value.object.ContentGuardHourlySeverity;
import com.actonia.value.object.ContentGuardHourlyTimeline;
import com.actonia.value.object.ContentGuardResourceResponse;
import com.actonia.value.object.ContentGuardSeverity;
import com.actonia.value.object.ContentGuardUrlChanges;
import com.actonia.value.object.ContentGuardUrlChangesAddedAscendingComparator;
import com.actonia.value.object.ContentGuardUrlChangesAddedDescendingComparator;
import com.actonia.value.object.ContentGuardUrlChangesCrawlTimestampAscendingComparator;
import com.actonia.value.object.ContentGuardUrlChangesCrawlTimestampDescendingComparator;
import com.actonia.value.object.ContentGuardUrlChangesModifiedAscendingComparator;
import com.actonia.value.object.ContentGuardUrlChangesModifiedDescendingComparator;
import com.actonia.value.object.ContentGuardUrlChangesRemovedAscendingComparator;
import com.actonia.value.object.ContentGuardUrlChangesRemovedDescendingComparator;
import com.actonia.value.object.ContentGuardUrlChangesTotalChangesAscendingComparator;
import com.actonia.value.object.ContentGuardUrlChangesTotalChangesDescendingComparator;
import com.actonia.value.object.ContentGuardUrlChangesUrlAscendingComparator;
import com.actonia.value.object.ContentGuardUrlChangesUrlDescendingComparator;
import com.actonia.value.object.CrawlerResponse;
import com.actonia.value.object.CustomData;
import com.actonia.value.object.Data;
import com.actonia.value.object.Entries;
import com.actonia.value.object.Errors;
import com.actonia.value.object.Headers;
import com.actonia.value.object.HreflangErrors;
import com.actonia.value.object.HreflangLinks;
import com.actonia.value.object.HtmlHeading;
import com.actonia.value.object.JavascriptCrawlerRequest;
import com.actonia.value.object.Links;
import com.actonia.value.object.LogValueObject;
import com.actonia.value.object.OgMarkup;
import com.actonia.value.object.PageAnalysisResult;
import com.actonia.value.object.PageAnalysisResultChgInd;
import com.actonia.value.object.PageLink;
import com.actonia.value.object.RedirectChain;
import com.actonia.value.object.ResponseCodeFilter;
import com.actonia.value.object.ScrapyCrawlerResponse;
import com.actonia.value.object.SlackBlock;
import com.actonia.value.object.SlackText;
import com.actonia.value.object.StructuredData;
import com.actonia.value.object.UrlFilter;
import com.actonia.value.object.ValidateStructuredData;
import com.amazonaws.services.sqs.model.Message;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

public class CrawlerTest {

	private static String htmlSourceFileFolderLocation = null;
	private OwnDomainEntityDAO ownDomainEntityDAO;
	private ContentGuardUsageDAO contentGuardUsageDAO;
	private TargetUrlCrawlAdditionalContentEntityDAO targetUrlCrawlAdditionalContentEntityDAO;
	private CompetitorUrlMd5EntityDAO competitorUrlMd5EntityDAO;

	public CrawlerTest() {
		super();
		this.ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		this.contentGuardUsageDAO = SpringBeanFactory.getBean("contentGuardUsageDAO");
		this.targetUrlCrawlAdditionalContentEntityDAO = SpringBeanFactory.getBean("targetUrlCrawlAdditionalContentEntityDAO");
		this.competitorUrlMd5EntityDAO = SpringBeanFactory.getBean("competitorUrlMd5EntityDAO");
	}

	public static void main(String[] args) throws Exception {
		new CrawlerTest().runTests(args);
	}

	private void runTests(String[] args) throws Exception {
		//testGetCurrentPreviousByChangeIndicator();
		//testGetChangeIndicatorsByUrl();
		//testGetUrlsWithMostChanges();
		//testGetDefaultRequestHeaders();
		//testPrettyPrintJson();
		//testDateFormat();
		//testMaxUrlHash();
		//testGetRankedTargetUrlEntityList();
		//testConvertTimestamp();
		//testCrawlDateHourSummary();
		//testHtmlHeading();
		//testSplitString();
		//testLastPageStartRow();
		//		testMinifyHtml();
		//		testGetScrapyResponse();
		//		testDeserializeJsonToCrawlerResponse();
		//		testCreateTargetUrlHtmlClickHouseRecord();
		//		testGetAlertMetricsNameClickHouseList();
		//		testMetricsNameJson();
		//testGetLastTargetUrlHtmlNotAvailable();
		//		testIsNumber();
		//		testCalculateNumberOfHeadings();
		//		testCompareStrings();
		//		testHtmlDataFilePath();
		//		testSelectDataFilesBasedOnTimestamp();
		//		testGetSortedCharacters();
		//		testGetMessages();
		//		testMarshallUrlMetricEntityV3();
		//		testDeserializeJsonToHreflangErrors();
		//		testEncodeUrlString();
		//		testExtractPageCrawlerApiRequestHeaderName();
		//		testSplitTabDelimitedString();
		//		testDifferentInSeconds();
		//		testCalculateNumberOfContentGuardUrlGroups();
		//		testGroupStartingIndex();
		//		testGetDomainIdFromQueueName();
		//		testGetS3Location();
		//		testRemoveDomainNameFromUrl();
		//		testMinusOneDay();
		//		testReversePageAnalysisResults();
		//		testExtractQueueNameFromQueueUrl();
		//		testGetCustomDataSelector();
		//		testGetSortedCharactersHashCode();
		//		testIsCrawlable();
		//		testBooleanToString();
		//		testGetEnglishText();
		//		testSimhashBetweenTwoStrings();
		//		testSimhashBetweenTwoFiles();
		//		testExtractDateHourFromCrawlTimestamp();
		//		testCheckIfClientDomainUrl();
		//		testConvertIntSetToString();
		//		testConvertSlackValueObjectToJson();
		//		testContentGuardUrlChangesUrlAscendingComparator();
		//		testContentGuardUrlChangesUrlDescendingComparator();
		//		testContentGuardUrlChangesCrawlTimestampAscendingComparator();
		//		testContentGuardUrlChangesCrawlTimestampDescendingComparator();
		//		testContentGuardUrlChangesTotalChangesAscendingComparator();
		//		testContentGuardUrlChangesTotalChangesDescendingComparator();
		//		testContentGuardUrlChangesAddedAscendingComparator();
		//		testContentGuardUrlChangesAddedDescendingComparator();
		//		testContentGuardUrlChangesModifiedAscendingComparator();
		//		testContentGuardUrlChangesModifiedDescendingComparator();
		//		testContentGuardUrlChangesRemovedAscendingComparator();
		//		testContentGuardUrlChangesRemovedDescendingComparator();
		//		testCalculateFromToIndexForPageNumberRowsPerPage();
		//		testParseAlternateLinks();
		//testCalculateMd5HashForTestUrls();
		//		testDailyGroupTimelineSeverity();
		//		testHourlyGroupTimelineSeverity();
		//testTimestampMilliseconds();
		//testDetachDailyPartitions();
		//testJavascriptCrawlerRequest();
		//testRankCheckUtilsGetRankedKeywordCompetitorUrlListMap();
		//testCreateContentGuardUsage();
		//testGetContentGuardUsage();
		//testUpdateContentGuardUsage();
		//testFormatDateNumberToString();
		//testParseCustomData();
		//testTargetUrlCrawlAdditionalContentUrlSelector();
		//testGetInvalidCompetitorUrlCount();
		//testStringArrayLength();
	}

	private void testGetCurrentPreviousByChangeIndicator() throws Exception {
		String ip = "8";
		int domainId = 2119;
		String trackDateString = "2022-09-05";
		String startCrawlTimestampString = "2022-09-05 00:00:00";
		String endCrawlTimestampString = "2022-09-05 23:59:59";
		//List<String> databaseFields = CrawlerUtils.getInstance().getChangeIndicatorDatabaseFields();
		List<String> databaseFields = new ArrayList<String>();
		databaseFields.add(IConstants.URL);
		databaseFields.add(IConstants.CHANGE_INDICATOR);
		databaseFields.add(IConstants.CANONICAL + IConstants.UNDERSCORE_CURRENT);
		databaseFields.add(IConstants.CANONICAL + IConstants.UNDERSCORE_PREVIOUS);
		//Integer[] pageTagIdArray = new Integer[] { 1145817, 1378083 };
		Integer[] pageTagIdArray = null;
		String urlString = "https://www.888casino.com/blackjack/";
		String changeIndicator = "canonical_chg_ind";
		List<TargetUrlChangeIndClickHouseEntity> targetUrlChangeIndClickHouseEntityList = TargetUrlChangeIndClickHouseDAO.getInstance()
				.getCurrentPreviousByChangeIndicator(ip, domainId, trackDateString, startCrawlTimestampString, endCrawlTimestampString, databaseFields, pageTagIdArray,
						urlString, changeIndicator);
		if (targetUrlChangeIndClickHouseEntityList != null && targetUrlChangeIndClickHouseEntityList.size() > 0) {
			for (TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity : targetUrlChangeIndClickHouseEntityList) {
				System.out.println("url=" + targetUrlChangeIndClickHouseEntity.getUrl() + ",changeIndicator=" + targetUrlChangeIndClickHouseEntity.getChangeIndicator()
						+ ",canonicalCurrent=" + targetUrlChangeIndClickHouseEntity.getCanonicalCurrent() + ",canonicalPrevious="
						+ targetUrlChangeIndClickHouseEntity.getCanonicalPrevious());
			}
		} else {
			System.out.println("targetUrlChangeIndClickHouseEntityList is empty.");
		}
	}

	private void testGetChangeIndicatorsByUrl() throws Exception {
		String ip = "8";
		int domainId = 2119;
		String trackDateString = "2022-09-05";
		String startCrawlTimestampString = "2022-09-05 00:00:00";
		String endCrawlTimestampString = "2022-09-05 23:59:59";
		//List<String> databaseFields = CrawlerUtils.getInstance().getChangeIndicatorDatabaseFields();
		List<String> databaseFields = getDomain2119CustomChangeIndicators();
		//Integer[] pageTagIdArray = new Integer[] { 1145817, 1378083 };
		Integer[] pageTagIdArray = null;
		String urlString = "https://www.888casino.com/blackjack/";
		List<TargetUrlChangeIndClickHouseEntity> targetUrlChangeIndClickHouseEntityList = TargetUrlChangeIndClickHouseDAO.getInstance().getChangeIndicatorsByUrl(ip,
				domainId, trackDateString, startCrawlTimestampString, endCrawlTimestampString, databaseFields, pageTagIdArray, urlString);
		if (targetUrlChangeIndClickHouseEntityList != null && targetUrlChangeIndClickHouseEntityList.size() > 0) {
			for (TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity : targetUrlChangeIndClickHouseEntityList) {
				System.out.println("changeIndicator=" + targetUrlChangeIndClickHouseEntity.getChangeIndicator());
			}
		} else {
			System.out.println("targetUrlChangeIndClickHouseEntityList is empty.");
		}
	}

	private void testGetUrlsWithMostChanges() throws Exception {
		String ip = "8";
		int domainId = 6457;
		String trackDateString = "2022-10-18";
		String startCrawlTimestampString = "2022-10-18 00:00:00";
		String endCrawlTimestampString = "2022-10-18 23:59:59";
		//List<String> databaseFields = CrawlerUtils.getInstance().getChangeIndicatorDatabaseFields();
		List<String> databaseFields = getDomain6457CustomChangeIndicators();
		Integer[] pageTagIdArray = new Integer[] { 1145817, 1378083 };
		List<TargetUrlChangeIndClickHouseEntity> targetUrlChangeIndClickHouseEntityList = TargetUrlChangeIndClickHouseDAO.getInstance().getUrlsWithMostChanges(ip,
				domainId, trackDateString, startCrawlTimestampString, endCrawlTimestampString, databaseFields, pageTagIdArray);
		if (targetUrlChangeIndClickHouseEntityList != null && targetUrlChangeIndClickHouseEntityList.size() > 0) {
			for (TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity : targetUrlChangeIndClickHouseEntityList) {
				System.out.println("url=" + targetUrlChangeIndClickHouseEntity.getUrl() + ",totalChanges=" + targetUrlChangeIndClickHouseEntity.getTotalChanges());
			}
		} else {
			System.out.println("targetUrlChangeIndClickHouseEntityList is empty.");
		}
	}

	private List<String> getDomain2119CustomChangeIndicators() {
		List<String> databaseFields = new ArrayList<String>();
		databaseFields.add(IConstants.CANONICAL_CHG_IND);
		databaseFields.add(IConstants.CANONICAL_HEADER_FLAG_CHG_IND);
		databaseFields.add(IConstants.CANONICAL_HEADER_TYPE_CHG_IND);
		databaseFields.add(IConstants.CANONICAL_REMOVED_IND);
		databaseFields.add(IConstants.HEADER_NOFOLLOW_CHG_IND);
		databaseFields.add(IConstants.HEADER_NOINDEX_CHG_IND);
		databaseFields.add(IConstants.INDEXABLE_CHG_IND);
		databaseFields.add(IConstants.FINAL_RESPONSE_CODE_CHG_IND);
		databaseFields.add(IConstants.RESPONSE_CODE_CHG_IND);
		databaseFields.add(IConstants.TITLE_REMOVED_IND);
		return databaseFields;
	}

	private List<String> getDomain6457CustomChangeIndicators() {
		List<String> databaseFields = new ArrayList<String>();
		databaseFields.add(IConstants.REDIRECT_301_REMOVED_IND);
		databaseFields.add(IConstants.REDIRECT_302_REMOVED_IND);
		databaseFields.add(IConstants.BASE_TAG_REMOVED_IND);
		databaseFields.add(IConstants.BLOCKED_BY_ROBOTS_CHG_IND);
		databaseFields.add(IConstants.CANONICAL_ADDED_IND);
		databaseFields.add(IConstants.CANONICAL_CHG_IND);
		databaseFields.add(IConstants.CANONICAL_HEADER_FLAG_CHG_IND);
		databaseFields.add(IConstants.CANONICAL_HEADER_TYPE_CHG_IND);
		databaseFields.add(IConstants.CANONICAL_REMOVED_IND);
		databaseFields.add(IConstants.CANONICAL_TYPE_CHG_IND);
		databaseFields.add(IConstants.CANONICAL_URL_IS_CONSISTENT_CHG_IND);
		databaseFields.add(IConstants.CUSTOM_DATA_ADDED_IND);
		databaseFields.add(IConstants.CUSTOM_DATA_CHG_IND);
		databaseFields.add(IConstants.CUSTOM_DATA_REMOVED_IND);
		databaseFields.add(IConstants.DESCRIPTION_REMOVED_IND);
		databaseFields.add(IConstants.H1_REMOVED_IND);
		databaseFields.add(IConstants.HEADER_NOFOLLOW_CHG_IND);
		databaseFields.add(IConstants.HEADER_NOINDEX_CHG_IND);
		databaseFields.add(IConstants.HREFLANG_LINKS_REMOVED_IND);
		databaseFields.add(IConstants.INDEXABLE_CHG_IND);
		databaseFields.add(IConstants.FINAL_RESPONSE_CODE_CHG_IND);
		databaseFields.add(IConstants.RESPONSE_CODE_CHG_IND);
		databaseFields.add(IConstants.TITLE_REMOVED_IND);
		databaseFields.add(IConstants.VIEWPORT_REMOVED_IND);
		return databaseFields;
	}

	private void testGetDefaultRequestHeaders() throws Exception {
		Map<String, String> testMap = PutMessageUtils.getInstance().getDefaultRequestHeaders(4765);
		String value = null;
		for (String key : testMap.keySet()) {
			value = testMap.get(key);
			System.out.println("key=" + key + ",value=" + value);
		}
	}

	private void testPrettyPrintJson() throws JsonProcessingException {
		String input = "[{\"content\":[\"Home\n                     / \n                \n            \n                \n                \n                    Road Trips and Driving Guides\n                     / \n                \n            \n                \n                \n                    Destinations\n                     / \n                \n            \n                \n                    New York Road Trips\"],\"index\":1,\"links\":[{\"anchor_text\":\"Home\",\"destination_url\":\"https://www.enterprise.com/en/home.html\",\"rel_attribute_contents\":\"\"},{\"anchor_text\":\"Road Trips and Driving Guides\",\"destination_url\":\"https://www.enterprise.com/en/road-trips.html\",\"rel_attribute_contents\":\"\"},{\"anchor_text\":\"Destinations\",\"destination_url\":\"https://www.enterprise.com/en/road-trips/destinations.html\",\"rel_attribute_contents\":\"\"},{\"anchor_text\":\"New York Road Trips\",\"destination_url\":\"https://www.enterprise.com/en/road-trips/destinations/new-york.html\",\"rel_attribute_contents\":\"\"}],\"selector\":\"/html/body/div[3]/div[1]/div/nav/div\",\"selector_type\":\"XPATH\",\"word_count\":28}]";
		ObjectMapper mapper = new ObjectMapper();
		CustomData[] customDataArray = new Gson().fromJson(input, CustomData[].class);
		String json = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(customDataArray);
		json = StringUtils.replace(json, "[ {", "");
		json = StringUtils.replace(json, "} ]", "");
		json = StringUtils.replace(json, "[ ", "");
		json = StringUtils.replace(json, " ]", "");
		json = StringUtils.replace(json, "}, {", "");
		json = StringUtils.replace(json, ",", "");
		System.out.println(json);
	}

	private void testHtmlHeading() throws Exception {
		HtmlHeading[] h1_current = null;
		HtmlHeading htmlHeading = null;
		List<HtmlHeading> htmlHeadingList = new ArrayList<HtmlHeading>();

		// first previous H1 heading
		htmlHeading = new HtmlHeading();
		htmlHeading.setIndex(1);
		htmlHeading.setValue("current first h1");
		htmlHeadingList.add(htmlHeading);

		// second previous H1 heading
		htmlHeading = new HtmlHeading();
		htmlHeading.setIndex(2);
		htmlHeading.setValue("second h1");
		htmlHeadingList.add(htmlHeading);

		h1_current = htmlHeadingList.toArray(new HtmlHeading[0]);

		String json = new Gson().toJson(h1_current, HtmlHeading[].class);
		System.out.println("json=" + json);

	}

	private void testMinifyHtml() throws Exception {
		String inputFileLocationPath = "/home/<USER>/source/test_polite_crawl_maven/test/test.html";
		File inputFile = new File(inputFileLocationPath);
		StringBuilder stringBuilder = null;
		String inputString = null;
		String outputString = null;
		List<String> stringList = FileUtils.readLines(inputFile, IConstants.UTF_8);
		if (stringList != null && stringList.size() > 0) {
			stringBuilder = new StringBuilder();
			for (String testString : stringList) {
				stringBuilder.append(testString);
			}
			inputString = stringBuilder.toString();
			outputString = CrawlerUtils.getInstance().trimText(inputString);
			System.out.println("outputString=" + outputString);
		} else {
			System.out.println("stringList is empty.");
		}
	}

	private void testGetScrapyResponse() {
		String ip = null;
		String queueName = null;
		String urlString = "https://www.orbitz.com/Green-Bay-Hotels-Baymont-By-Wyndham-Green-Bay.h40154.Hotel-Information";
		String userAgent = "ClarityBot-Expedia";
		boolean isResponseAsHtml = false;
		boolean htmlInJson = false;
		ScrapyCrawlerResponse scrapyCrawlerResponse = null;
		try {

			// XPATH only
			//scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyCrawlerResponse(ip, queueName, urlString, userAgent, isResponseAsHtml, htmlInJson, getAdditionalContentFilterValueObjectList(), null);

			// DIV ID and DIV CLASS only
			//scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyCrawlerResponse(ip, queueName, urlString, userAgent, isResponseAsHtml, htmlInJson, null, getOptionEntityList());

			// XPATH, DIV ID and DIV CLASS			
			//scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyCrawlerResponse(ip, queueName, urlString, userAgent, isResponseAsHtml, htmlInJson, getAdditionalContentFilterValueObjectList(), getOptionEntityList());

			System.out.println("scrapyCrawlerResponse=" + scrapyCrawlerResponse.toString());
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private List<OptionEntity> getOptionEntityList() {
		List<OptionEntity> optionEntityList = new ArrayList<OptionEntity>();

		OptionEntity optionEntity = null;

		// <div id="other-links-header"
		optionEntity = new OptionEntity();
		optionEntity.setOptionType(3); // type 3 = DIV ID
		optionEntity.setOptionText("other-links-header");
		optionEntityList.add(optionEntity);

		// <div class="message">
		optionEntity = new OptionEntity();
		optionEntity.setOptionType(4); // type 4 = DIV CLASS
		optionEntity.setOptionText("message");
		optionEntityList.add(optionEntity);

		return optionEntityList;
	}

	private List<AdditionalContentFilterValueObject> getAdditionalContentFilterValueObjectList() {
		AdditionalContentFilterValueObject additionalContentFilterValueObject = null;
		List<AdditionalContentFilterValueObject> additionalContentFilterValueObjectList = new ArrayList<AdditionalContentFilterValueObject>();

		// //a[contains(@rel,'author')]/@href
		additionalContentFilterValueObject = new AdditionalContentFilterValueObject();
		additionalContentFilterValueObject.setXpath("//a[contains(@rel,'author')]/@href");
		additionalContentFilterValueObjectList.add(additionalContentFilterValueObject);

		// //a[contains(@rel,'next')]/@href
		additionalContentFilterValueObject = new AdditionalContentFilterValueObject();
		additionalContentFilterValueObject.setXpath("//a[contains(@rel,'next')]/@href");
		additionalContentFilterValueObjectList.add(additionalContentFilterValueObject);

		// //a[contains(@rel,'prev')]/@href
		additionalContentFilterValueObject = new AdditionalContentFilterValueObject();
		additionalContentFilterValueObject.setXpath("//a[contains(@rel,'prev')]/@href");
		additionalContentFilterValueObjectList.add(additionalContentFilterValueObject);

		// //h3/text()
		additionalContentFilterValueObject = new AdditionalContentFilterValueObject();
		additionalContentFilterValueObject.setXpath("//h3/text()");
		additionalContentFilterValueObjectList.add(additionalContentFilterValueObject);

		// //h4/text()
		additionalContentFilterValueObject = new AdditionalContentFilterValueObject();
		additionalContentFilterValueObject.setXpath("//h4/text()");
		additionalContentFilterValueObjectList.add(additionalContentFilterValueObject);

		// //meta[@name='WDCSext.FType']/@content
		additionalContentFilterValueObject = new AdditionalContentFilterValueObject();
		additionalContentFilterValueObject.setXpath("//meta[@name='WDCSext.FType']/@content");
		additionalContentFilterValueObjectList.add(additionalContentFilterValueObject);

		// //meta[@name='WT.ti']/@content
		additionalContentFilterValueObject = new AdditionalContentFilterValueObject();
		additionalContentFilterValueObject.setXpath("//meta[@name='WT.ti']/@content");
		additionalContentFilterValueObjectList.add(additionalContentFilterValueObject);

		return additionalContentFilterValueObjectList;
	}

	private void testDeserializeJsonToCrawlerResponse() {
		try {
			//File inputFile = new File("C://dev//eclipse_workspace_svn_1.6//polite-crawl-maven//src//main//resources//json//scrapy_formatted_response.json");
			//File inputFile = new File("C://dev//eclipse_workspace_svn_1.6//polite-crawl-maven//src//main//resources//json//structured_data_test_case_4.json");			
			//File inputFile = new File("C://dev//eclipse_workspace_svn_1.6//polite-crawl-maven//src//main//resources//json//structured_data_test_case_5.json");			
			File inputFile = new File("C://dev//eclipse_workspace_svn_1.6//polite-crawl-maven//src//main//resources//json//structured_data_not_available.json");
			List<String> testStringList = FileUtils.readLines(inputFile, IConstants.UTF_8);
			String json = testStringList.get(0);
			System.out.println("json b4=" + json);
			Gson gson = new Gson();
			CrawlerResponse crawlerResponse = gson.fromJson(json, CrawlerResponse.class);
			if (crawlerResponse != null) {
				System.out.println("crawlerResponse=" + crawlerResponse.toString());
			} else {
				System.out.println("crawlerResponse is null.");
			}
			//System.out.println("crawlerResponse.getPage_link().toString()="+crawlerResponse.getPage_link().toString());
			//String testString = new Gson().toJson(crawlerResponse.getPage_link(), PageLink[].class);

		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void testCreateTargetUrlHtmlClickHouseRecord() {

		String tableName = "target_url_html_test";
		String urlString = "https://petvacationhomes.com";
		int domainId = 4;
		String ip = "";
		String queueName = "";
		String userAgent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 (compatible; PoliteCrawl.v2.0)";
		Date currentTrackDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);

		HtmlClickHouseEntity htmlClickHouseEntity = null;
		ScrapyCrawlerResponse scrapyCrawlerResponse = null;
		CrawlerResponse crawlerResponse = null;
		PageAnalysisResult[] pageAnalysisResultArray = null;
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = null;
		boolean isJavascriptCrawler = false;
		boolean isStoreHtml = false;
		boolean isResponseAsHtml = false;
		Integer javascriptTimeoutInSecond = 10;

		try {
			scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(ip, queueName, urlString, userAgent, null, isJavascriptCrawler,
					javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, null, isStoreHtml, null, isResponseAsHtml, null);
			if (scrapyCrawlerResponse != null) {
				crawlerResponse = scrapyCrawlerResponse.getCrawlerResponse();
				if (crawlerResponse != null) {
					htmlClickHouseEntity = new HtmlClickHouseEntity();
					htmlClickHouseEntity.setDomainId(domainId);
					htmlClickHouseEntity.setUrl(urlString);
					htmlClickHouseEntity.setTrackDate(currentTrackDate);
					htmlClickHouseEntity.setCrawlTimestamp(new Date());
					htmlClickHouseEntity.setCrawlerResponse(crawlerResponse);
					if (htmlClickHouseEntity.getHttpStatusCode() != null && htmlClickHouseEntity.getHttpStatusCode().intValue() == 200) {

						// aggregate page analysis results
						pageAnalysisResultArray = CrawlerUtils.getInstance().getPageAnalysisResultArray(crawlerResponse);
						htmlClickHouseEntity.setPageAnalysisResultArray(pageAnalysisResultArray);
					}
					htmlClickHouseEntity.setWeekOfYear(CommonUtils.calculateWeekOfYear(currentTrackDate));
					htmlClickHouseEntityList = new ArrayList<HtmlClickHouseEntity>();
					htmlClickHouseEntityList.add(htmlClickHouseEntity);
					TargetUrlHtmlClickHouseDAO.getInstance().createBatch(null, null, htmlClickHouseEntityList, tableName);

				} else {
					// return null htmlClickHouseEntity
				}
			} else {
				// return null htmlClickHouseEntity
			}
		} catch (Exception e) {
			htmlClickHouseEntity = null;
			FormatUtils.getInstance().logMemoryUsage(
					"retrieveScrapyHtmlData() ends. ip=" + ip + ",queueName=" + queueName + ",urlString=" + urlString + ",exception message=" + e.getMessage());
			e.printStackTrace();
		} finally {
		}
	}

	private void testGetAlertMetricsNameClickHouseList() {
		List<AlertMetricsNameClickHouseEntity> alertMetricsNameClickHouseEntityList = null;
		try {
			alertMetricsNameClickHouseEntityList = AlertMetricsNameClickHouseService.getInstance().getList(AlertMetricsNameClickHouseDAO.TABLE_NAME);
			assertNotNull("alertMetricsNameClickHouseEntityList should not be null.", alertMetricsNameClickHouseEntityList);
			for (AlertMetricsNameClickHouseEntity alertMetricsNameClickHouseEntity : alertMetricsNameClickHouseEntityList) {
				FormatUtils.getInstance().logMemoryUsage("alertMetricsNameClickHouseEntity=" + alertMetricsNameClickHouseEntity.toString());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void testMetricsNameJson() {
		String jsonString = "[\"title\",\"metaDescription\",\"metaKeyword\",\"h1\",\"h1Content\",\"Additional Content\"]";
		List<String> metricsNameList = null;
		Gson gson = new Gson();
		metricsNameList = gson.fromJson(jsonString, List.class);
		if (metricsNameList != null && metricsNameList.size() > 0) {
			for (String metricsName : metricsNameList) {
				System.out.println("metricsName=" + metricsName);
			}
		}
	}

	// C:/dev/eclipse-workspace/polite-crawl-put-messages/src/main/resources/simhash_test_data/test.html
	//	private void testGetEnglishText() throws Exception {
	//		String[] inputFileLocationPathArray = new String[] {
	//				"C:/dev/eclipse-workspace/polite-crawl-put-messages/src/main/resources/simhash_test_data/simhash_test1.html",
	//				"C:/dev/eclipse-workspace/polite-crawl-put-messages/src/main/resources/simhash_test_data/simhash_test2.html" };
	//		String textString = null;
	//		File inputFile = null;
	//		List<String> testStringList = null;
	//		String htmlSource = null;
	//		StringBuilder stringBuilder = null;
	//		Long simhash = null;
	//		String binaryString = null;
	//		byte[] byteArray1 = null;
	//		byte[] byteArray2 = null;
	//		int totalDifferentBits = 0;
	//		Byte byte1 = null;
	//		Byte byte2 = null;
	//		String inputFileLocationPath = null;
	//		if (isUseRealWebPage == true) {
	//			for (int i = 0; i < inputFileLocationPathArray.length; i++) {
	//				inputFileLocationPath = inputFileLocationPathArray[i];
	//				// retrieve the HTML source
	//				inputFile = new File(inputFileLocationPath);
	//				//System.out.println("inputFileLocationPath=" + inputFileLocationPath);
	//				testStringList = FileUtils.readLines(inputFile, IConstants.UTF_8);
	//				if (testStringList != null && testStringList.size() > 0) {
	//					stringBuilder = new StringBuilder();
	//					for (String testString : testStringList) {
	//						stringBuilder.append(testString);
	//					}
	//					htmlSource = stringBuilder.toString();
	//
	//					// extract the content text to remove HTML elements
	//					textString = CrawlerUtils.getInstance().extractBodyContent(htmlSource);
	//
	//					// trim the content text to remove line feed, carriage return, tabs, etc.
	//					//textString = CrawlerUtils.getInstance().trimText(textString);
	//					System.out.println("textString=" + textString);
	//
	//					simhash = SimHash.computeOptimizedSimHashForString(textString);
	//					System.out.println("simhash=" + simhash);
	//
	//					binaryString = Long.toBinaryString(simhash);
	//					System.out.println("binaryString=" + binaryString);
	//					if (i == 0) {
	//						byteArray1 = binaryString.getBytes();
	//					} else if (i == 1) {
	//						byteArray2 = binaryString.getBytes();
	//					}
	//				}
	//			}
	//			for (int j = 0; j < byteArray1.length; j++) {
	//				byte1 = byteArray1[j];
	//				byte2 = byteArray2[j];
	//				if (Byte.compare(byte1, byte2) != 0) {
	//					totalDifferentBits++;
	//				}
	//			}
	//			System.out.println("totalDifferentBits=" + totalDifferentBits);
	//		} else {
	//			stringBuilder = new StringBuilder();
	//			stringBuilder.append("Article 26").append(IConstants.TWO_SPACES);
	//			stringBuilder.append(
	//					"1. Everyone has the right to education. Education shall be free, at least in the elementary and fundamental stages. Elementary education shall be compulsory. Technical and professional education shall be made generally available and higher education shall be equally accessible to all on the basis of merit.")
	//					.append(IConstants.TWO_SPACES);
	//			stringBuilder.append(
	//					"2. Education shall be directed to the full development of the human personality and to the strengthening of respect for human rights and fundamental freedoms. It shall promote understanding, tolerance and friendship among all nations, racial or religious groups, and shall further the activities of the United Nations for the maintenance of peace.")
	//					.append(IConstants.TWO_SPACES);
	//			stringBuilder.append("3. Parents have a prior right to choose the kind of education that shall be given to their children.").append(IConstants.TWO_SPACES);
	//			stringBuilder.append("Article 27").append(IConstants.TWO_SPACES);
	//			stringBuilder.append(
	//					"1. Everyone has the right freely to participate in the cultural life of the community, to enjoy the arts and to share in scientific advancement and its benefits.")
	//					.append(IConstants.TWO_SPACES);
	//			stringBuilder.append(
	//					"2. Everyone has the right to the protection of the moral and material interests resulting from any scientific, literary or artistic production of which he is the author.")
	//					.append(IConstants.TWO_SPACES);
	//			textString = stringBuilder.toString();
	//		}
	//	}

	private String getHtmlSourceFileFolderLocation() {
		if (StringUtils.isBlank(htmlSourceFileFolderLocation)) {
			htmlSourceFileFolderLocation = Configurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_HTML_SOURCE_FILES_FOLDER);
		}
		System.out.println("htmlSourceFileFolderLocation=" + htmlSourceFileFolderLocation);
		return htmlSourceFileFolderLocation;
	}

	private void testGetLastTargetUrlHtmlNotAvailable() throws Exception {
		String tableName = null;
		int domainId = 4;
		String urlString = "http://www.petvacationhomes.com/";
		List<String> databaseFields = CrawlerUtils.getInstance().getTargetUrlHtmlTableAllFieldNames();
		HtmlClickHouseEntity htmlClickHouseEntity = TargetUrlHtmlClickHouseDAO.getInstance().getPrevious(null, null, domainId, urlString, databaseFields, tableName,
				null, null);
		assertNotNull("htmlClickHouseEntity should be null.", htmlClickHouseEntity);
		System.out.println("testGetLastTargetUrlHtmlNotAvailable() passed.");
	}

	private void testIsNumber() {
		String responseCode = "200";
		boolean isNumber = NumberUtils.isNumber(responseCode);
		System.out.println("responseCode=" + responseCode + ",isNumber=" + isNumber);
	}

	private void testCalculateNumberOfHeadings() {
		int numberOfHeadings = 1;
		//String inputText = "[經濟研究報告]";
		String inputText = "[經濟研究報告, 交易策略部署, 全球期貨行情 (點擊產品編碼，以下所有列出產品均有中文產品介紹頁面，內含產品合約及數據）, 市场数据]";
		int fromIndex = 0;
		while (fromIndex != -1) {
			fromIndex = inputText.indexOf(IConstants.COMMA, fromIndex);
			if (fromIndex != -1) {
				numberOfHeadings++;
				fromIndex++;
			}
			System.out.println("fromIndex=" + fromIndex + ",numberOfHeadings=" + numberOfHeadings);
		}
	}

	private void testCustomData() {
		Object sourceValue = getSourceValue();
		List<Map<String, Object>> customDataMapList = new ArrayList<Map<String, Object>>();
		List<Object> objectList = (List<Object>) sourceValue;
		for (Object object : objectList) {
			if (object instanceof Map) {
				customDataMapList.add((Map<String, Object>) object);
			}
		}
	}

	private Object getSourceValue() {
		String textString = "[{word_count=0, index=1, selector_type=DIV_ID, selector=searchBox, links=[], content=}, {word_count=571, index=2, selector_type=DIV_CLASS, selector=container, links=[{rel_attribute_contents=, anchor_text=SPECIALS • SAVE NOW!, destination_url=https://petvacationhomes.com/rentals-available-now}, {rel_attribute_contents=, anchor_text=LAST MINUTE SPECIALS • SAVE NOW!, destination_url=https://petvacationhomes.com/rentals-available-now}, {rel_attribute_contents=, anchor_text=, destination_url=https://petvacationhomes.com/}, {rel_attribute_contents=, anchor_text=Pet Vacation Homes, destination_url=https://petvacationhomes.com/}, {rel_attribute_contents=, anchor_text=Browse All Rentals, destination_url=https://petvacationhomes.com/browse-all-rentals}, {rel_attribute_contents=, anchor_text=About Us, destination_url=https://petvacationhomes.com/about-us}, {rel_attribute_contents=, anchor_text=<EMAIL>, destination_url=mailto:<EMAIL>}, {rel_attribute_contents=, anchor_text=Pet Vacation Homes, destination_url=https://petvacationhomes.com/}, {rel_attribute_contents=, anchor_text=About Us, destination_url=https://petvacationhomes.com/about-us}, {rel_attribute_contents=, anchor_text=Contact Us, destination_url=https://petvacationhomes.com/contact-us}, {rel_attribute_contents=, anchor_text=Site Map, destination_url=https://petvacationhomes.com/sitemap}, {rel_attribute_contents=, anchor_text=Rental Management, destination_url=https://petvacationhomes.com/property-management}, {rel_attribute_contents=, anchor_text=Jobs, destination_url=https://petvacationhomes.com/jobs}, {rel_attribute_contents=, anchor_text=Owner Portal, destination_url=https://owner.instantsoftware.com/dist/index.html#/login}, {rel_attribute_contents=, anchor_text=Owners, destination_url=https://petvacationhomes.com/owners-prospective}, {rel_attribute_contents=, anchor_text=Privacy Policy, destination_url=https://petvacationhomes.com/privacy-policy}, {rel_attribute_contents=, anchor_text=Terms of Use, destination_url=https://petvacationhomes.com/terms-of-use}], content=Sign Up TODAY for Vacation News &amp; Money Saving Offers! SPECIALS • SAVE NOW! LAST MINUTE SPECIALS • SAVE NOW! SIGN UP NOW!}]";
		Object object = new Object();
		return object;
	}

	private void testCompareStrings() throws Exception {
		String currentDesc = "Document Moved";
		String previousDesc = "Document Moved";
		boolean isDifferent = CrawlerUtils.getInstance().isDifferent(currentDesc, previousDesc);
		System.out.println("currentDesc=" + currentDesc);
		System.out.println("previousDesc=" + previousDesc);
		System.out.println("isDifferent=" + isDifferent);
	}

	private void testHtmlDataFilePath() {
		String timestampString = DateFormatUtils.format(new Date(), IConstants.DATE_FORMAT_YYYYMMDD_HH);
		System.out.println("timestampString=" + timestampString);
	}

	private void testSelectDataFilesBasedOnTimestamp() {
		String creationDateString = null;
		String creationHourString = null;
		String creationMinuteString = null;
		String currentDateString = null;
		String currentHourString = null;
		String currentMinuteString = null;
		int creationDateNumber = 0;
		int creationHourNumber = 0;
		int creationMinuteNumber = 0;
		int currentDateNumber = 0;
		int currentHourNumber = 0;
		int currentMinuteNumber = 0;
		Date currentDate = null;
		String testString = null;
		String[] currentDateSegmentArray = null;
		boolean isProcessDataFile = false;

		String htmlDataFileName = "target_url_html_20190429_15_51_00.txt";
		String[] htmlDataFileNameSegmentArray = htmlDataFileName.split(IConstants.UNDERSCORE);
		if (htmlDataFileNameSegmentArray != null && htmlDataFileNameSegmentArray.length > 0) {
			creationDateString = htmlDataFileNameSegmentArray[3];
			creationDateNumber = NumberUtils.toInt(creationDateString);
			creationHourString = htmlDataFileNameSegmentArray[4];
			creationHourNumber = NumberUtils.toInt(creationHourString);
			creationMinuteString = htmlDataFileNameSegmentArray[5];
			creationMinuteNumber = NumberUtils.toInt(creationMinuteString);
		}
		System.out.println("htmlDataFileName=" + htmlDataFileName);
		for (String htmlDataFileNameSegment : htmlDataFileNameSegmentArray) {
			System.out.println("htmlDataFileNameSegment=" + htmlDataFileNameSegment);
		}
		System.out.println("creationDateNumber=" + creationDateNumber);
		System.out.println("creationHourNumber=" + creationHourNumber);
		System.out.println("creationMinuteNumber=" + creationMinuteNumber);

		currentDate = new Date();
		testString = DateFormatUtils.format(currentDate, IConstants.DATE_FORMAT_YYYYMMDD_HH);
		currentDateSegmentArray = testString.split(IConstants.UNDERSCORE);
		for (String currentDateSegment : currentDateSegmentArray) {
			System.out.println("currentDateSegment=" + currentDateSegment);
		}
		currentDateString = currentDateSegmentArray[0];
		currentDateNumber = NumberUtils.toInt(currentDateString);
		currentHourString = currentDateSegmentArray[1];
		currentHourNumber = NumberUtils.toInt(currentHourString);
		currentMinuteString = currentDateSegmentArray[2];
		currentMinuteNumber = NumberUtils.toInt(currentMinuteString);
		System.out.println("currentDateNumber=" + currentDateNumber);
		System.out.println("currentHourNumber=" + currentHourNumber);
		System.out.println("currentMinuteNumber=" + currentMinuteNumber);

		// when data file was created before today
		if (currentDateNumber > creationDateNumber) {
			isProcessDataFile = true;
		}
		// when data file was created today but before current hour
		else if (currentHourNumber > creationHourNumber) {
			isProcessDataFile = true;
		}
		// when data file was created today and current hour but two minutes ago
		else if (currentMinuteNumber - creationMinuteNumber >= 2) {
			isProcessDataFile = true;
		}
		System.out.println("isProcessDataFile=" + isProcessDataFile);
	}

	private void testGetSortedCharacters() {

		try {

			String unsortedTextString1 = "[{anchor_text=Juicy Couture Beauty, height=, width=, destination_url=https://www.juicycouturebeauty.com/on/demandware.static/Sites-JuicyCoutureBeauty-Site/-/default/dw4b6b7be8/images/Juicy_Couture_3D.png}, {anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, height=, width=, destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwf9306987/images/VIVA_LA_JUICY_GOLD_COUTURE_DVIM40004_1.jpg?sw=535&sh=535&sm=fit}, {anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, height=, width=, destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwf9306987/images/VIVA_LA_JUICY_GOLD_COUTURE_DVIM40004_1.jpg?sw=276&sh=276&sm=fit}, {anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, height=, width=, destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwf9306987/images/VIVA_LA_JUICY_GOLD_COUTURE_DVIM40004_1.jpg?sw=600&sh=600&sm=fit}, {anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, height=, width=, destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwf9306987/images/VIVA_LA_JUICY_GOLD_COUTURE_DVIM40004_1.jpg?sw=76&sh=76}, {anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, height=, width=, destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwf9306987/images/VIVA_LA_JUICY_GOLD_COUTURE_DVIM40004_1.jpg?sw=180&sh=180&sm=fit}, {anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, height=, width=, destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwf9306987/images/VIVA_LA_JUICY_GOLD_COUTURE_DVIM40004_1.jpg}, {anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, height=, width=, destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw165ae870/images/VIVA_LA_JUICY_GOLD_COUTURE_DVIM40004_2.jpg?sw=76&sh=76}, {anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, height=, width=, destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw165ae870/images/VIVA_LA_JUICY_GOLD_COUTURE_DVIM40004_2.jpg?sw=180&sh=180&sm=fit}, {anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, height=, width=, destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw165ae870/images/VIVA_LA_JUICY_GOLD_COUTURE_DVIM40004_2.jpg}, {anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, height=, width=, destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw99e50b18/images/Gold_Hero_2104_72dpi_ScentNotes.jpg?sw=76&sh=76}, {anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, height=, width=, destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw99e50b18/images/Gold_Hero_2104_72dpi_ScentNotes.jpg?sw=180&sh=180&sm=fit}, {anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, height=, width=, destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw99e50b18/images/Gold_Hero_2104_72dpi_ScentNotes.jpg}, {anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, height=, width=, destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw5b381179/images/ingredients/2104x2104_VLJ_Gold_Fragrance_Notes.jpg?sw=76&sh=76}, {anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, height=, width=, destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw5b381179/images/ingredients/2104x2104_VLJ_Gold_Fragrance_Notes.jpg?sw=180&sh=180&sm=fit}, {anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, height=, width=, destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw5b381179/images/ingredients/2104x2104_VLJ_Gold_Fragrance_Notes.jpg}, {anchor_text=Wild Berries, Gardenia, Muguet, height=, width=, destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw2d2ffbfa/images/ingredients/Gold_Top_Scent_Note.jpg}, {anchor_text=Vanilla Orchid, Honeysuckle, Jasmin Sambac, height=, width=, destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw873c949b/images/ingredients/Gold_Mid_Scent_Note.jpg}, {anchor_text=Melted Caramel, Vanilla Cream, Praline, Sandalwood, height=, width=, destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwb6bdad0d/images/ingredients/Gold_Base_Scent_Note.jpg}, {anchor_text=OUI Juicy Couture Eau de Parfum Spray, height=, width=, destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwfe7a2746/images/OUI_JUICY_COUTURE_A0115017_1.jpg?sw=550&sh=550&sm=fit}, {anchor_text=OUI Juicy Couture Eau de Parfum Spray, height=, width=, destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwfe7a2746/images/OUI_JUICY_COUTURE_A0115017_1.jpg?sw=550&sh=550&sm=fit}, {anchor_text=OUI Juicy Couture Eau de Parfum Spray, height=, width=, destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwfe7a2746/images/OUI_JUICY_COUTURE_A0115017_1.jpg?sw=135&sh=136&sm=fit}, {anchor_text=OUI Juicy Couture Eau de Parfum 3 Piece Gift Set, height=, width=, destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw1c2d4d4e/images/2104_A0117733_3.4oz_Set.jpg?sw=550&sh=550&sm=fit}, {anchor_text=OUI Juicy Couture Eau de Parfum 3 Piece Gift Set, height=, width=, destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw1c2d4d4e/images/2104_A0117733_3.4oz_Set.jpg?sw=550&sh=550&sm=fit}, {anchor_text=OUI Juicy Couture Eau de Parfum 3 Piece Gift Set, height=, width=, destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw1c2d4d4e/images/2104_A0117733_3.4oz_Set.jpg?sw=135&sh=136&sm=fit}, {anchor_text=Juicy Couture Lip + Eye Topper, height=, width=, destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw3575214d/images/A0116888_026_a4_G7_CAM.jpg?sw=550&sh=550&sm=fit}, {anchor_text=Juicy Couture Lip + Eye Topper, height=, width=, destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw3575214d/images/A0116888_026_a4_G7_CAM.jpg?sw=550&sh=550&sm=fit}, {anchor_text=Juicy Couture Lip + Eye Topper, height=, width=, destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw3575214d/images/A0116888_026_a4_G7_CAM.jpg?sw=135&sh=136&sm=fit}, {anchor_text=Juicy Couture Glitter Velour Lipstick, height=, width=, destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw7fdfe2da/images/A0116870_GirlStuff_a6_G7_CAM.jpg?sw=550&sh=550&sm=fit}, {anchor_text=Juicy Couture Glitter Velour Lipstick, height=, width=, destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw7fdfe2da/images/A0116870_GirlStuff_a6_G7_CAM.jpg?sw=550&sh=550&sm=fit}, {anchor_text=Juicy Couture Glitter Velour Lipstick, height=, width=, destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw7fdfe2da/images/A0116870_GirlStuff_a6_G7_CAM.jpg?sw=135&sh=136&sm=fit}, {anchor_text=Juicy Couture Glitter Velour Lipstick, height=, width=, destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwe814f031/images/432475_LipstickUVDarling_a7_sRGB.jpg}, {anchor_text=Juicy Couture Glitter Velour Lipstick, height=, width=, destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwe814f031/images/432475_LipstickUVDarling_a7_sRGB.jpg}, {anchor_text=Juicy Couture Glitter Velour Lipstick, height=, width=, destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwe814f031/images/432475_LipstickUVDarling_a7_sRGB.jpg}, {anchor_text=Juicy Couture Glitter Velour Lipstick, height=, width=, destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw61b96daf/images/432475_Ped&Zipped_a6_sRGB.jpg}, {anchor_text=Juicy Couture Glitter Velour Lipstick, height=, width=, destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw61b96daf/images/432475_Ped&Zipped_a6_sRGB.jpg}, {anchor_text=Juicy Couture Glitter Velour Lipstick, height=, width=, destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw61b96daf/images/432475_Ped&Zipped_a6_sRGB.jpg}, {anchor_text=Juicy Couture Glitter Velour Lipstick, height=, width=, destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwb00bdaac/images/432475_NotYourBabe_a6_sRGB.jpg}, {anchor_text=Juicy Couture Glitter Velour Lipstick, height=, width=, destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwb00bdaac/images/432475_NotYourBabe_a6_sRGB.jpg}, {anchor_text=Juicy Couture Glitter Velour Lipstick, height=, width=, destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwb00bdaac/images/432475_NotYourBabe_a6_sRGB.jpg}, {anchor_text=Juicy Couture Glitter Velour Lipstick, height=, width=, destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw225f8997/images/432475_LipstickHappily_108_a7_sRGB.jpg}, {anchor_text=Juicy Couture Glitter Velour Lipstick, height=, width=, destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw225f8997/images/432475_LipstickHappily_108_a7_sRGB.jpg}, {anchor_text=Juicy Couture Glitter Velour Lipstick, height=, width=, destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw225f8997/images/432475_LipstickHappily_108_a7_sRGB.jpg}, {anchor_text=Juicy Couture Glitter Velour Lipstick, height=, width=, destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw52efc995/images/432475_GirlstuffLip_090_a6_sRGB.jpg}, {anchor_text=Juicy Couture Glitter Velour Lipstick, height=, width=, destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw52efc995/images/432475_GirlstuffLip_090_a6_sRGB.jpg}, {anchor_text=Juicy Couture Glitter Velour Lipstick, height=, width=, destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw52efc995/images/432475_GirlstuffLip_090_a6_sRGB.jpg}, {anchor_text=Juicy Couture Lip Luster, height=, width=, destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwe22e1c55/images/Med-Light_Pink_a4_G7_CAM.jpg?sw=550&sh=550&sm=fit}, {anchor_text=Juicy Couture Lip Luster, height=, width=, destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwe22e1c55/images/Med-Light_Pink_a4_G7_CAM.jpg?sw=550&sh=550&sm=fit}, {anchor_text=Juicy Couture Lip Luster, height=, width=, destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwe22e1c55/images/Med-Light_Pink_a4_G7_CAM.jpg?sw=135&sh=136&sm=fit}, {anchor_text=Juicy Couture Lip Luster, height=, width=, destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwb3f85724/images/432475_LipVinylPink_a2a_sRGB.jpg}, {anchor_text=Juicy Couture Lip Luster, height=, width=, destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwb3f85724/images/432475_LipVinylPink_a2a_sRGB.jpg}, {anchor_text=Juicy Couture Lip Luster, height=, width=, destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwb3f85724/images/432475_LipVinylPink_a2a_sRGB.jpg}, {anchor_text=Juicy Couture Lip Luster, height=, width=, destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw9908ab97/images/432475_LipLustre_101_a2a_sRGB.jpg}, {anchor_text=Juicy Couture Lip Luster, height=, width=, destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw9908ab97/images/432475_LipLustre_101_a2a_sRGB.jpg}, {anchor_text=Juicy Couture Lip Luster, height=, width=, destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw9908ab97/images/432475_LipLustre_101_a2a_sRGB.jpg}, {anchor_text=Juicy Couture Lip Luster, height=, width=, destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw2762b0c6/images/432475_LipVinylRed_a4a_sRGB.jpg}, {anchor_text=Juicy Couture Lip Luster, height=, width=, destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw2762b0c6/images/432475_LipVinylRed_a4a_sRGB.jpg}, {anchor_text=Juicy Couture Lip Luster, height=, width=, destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw2762b0c6/images/432475_LipVinylRed_a4a_sRGB.jpg}, {anchor_text=Viva La Juicy Eau de Parfum Spray, height=, width=, destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwff183cc5/images/VIVA_LA_JUICY_JY2F00003_2.jpg?sw=550&sh=550&sm=fit}, {anchor_text=Viva La Juicy Eau de Parfum Spray, height=, width=, destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwff183cc5/images/VIVA_LA_JUICY_JY2F00003_2.jpg?sw=550&sh=550&sm=fit}, {anchor_text=Viva La Juicy Eau de Parfum Spray, height=, width=, destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwff183cc5/images/VIVA_LA_JUICY_JY2F00003_2.jpg?sw=135&sh=136&sm=fit}, {anchor_text=Juicy Couture Eau de Parfum Spray, height=, width=, destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwd9621392/images/JUICY_COUTURE_CLASSIC_JUIF00005_1.jpg?sw=550&sh=550&sm=fit}, {anchor_text=Juicy Couture Eau de Parfum Spray, height=, width=, destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwd9621392/images/JUICY_COUTURE_CLASSIC_JUIF00005_1.jpg?sw=550&sh=550&sm=fit}, {anchor_text=Juicy Couture Eau de Parfum Spray, height=, width=, destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwd9621392/images/JUICY_COUTURE_CLASSIC_JUIF00005_1.jpg?sw=135&sh=136&sm=fit}, {anchor_text=, height=, width=, destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeauty-Library/default/dw5bf493f3/images/signupimage.jpg}, {anchor_text=, height=1, width=1, destination_url=https://www.googleadservices.com/pagead/conversion/1036670823/?label=54exCJuQrQEQ566p7gM&guid=ON&script=0}]";
			//String unsortedTextString2 = "[{destination_url=https://www.juicycouturebeauty.com/on/demandware.static/Sites-JuicyCoutureBeauty-Site/-/default/dw4b6b7be8/images/Juicy_Couture_3D.png, anchor_text=Juicy Couture Beauty, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwf9306987/images/VIVA_LA_JUICY_GOLD_COUTURE_DVIM40004_1.jpg?sw=535&sh=535&sm=fit, anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwf9306987/images/VIVA_LA_JUICY_GOLD_COUTURE_DVIM40004_1.jpg?sw=276&sh=276&sm=fit, anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwf9306987/images/VIVA_LA_JUICY_GOLD_COUTURE_DVIM40004_1.jpg?sw=600&sh=600&sm=fit, anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwf9306987/images/VIVA_LA_JUICY_GOLD_COUTURE_DVIM40004_1.jpg?sw=76&sh=76, anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwf9306987/images/VIVA_LA_JUICY_GOLD_COUTURE_DVIM40004_1.jpg?sw=180&sh=180&sm=fit, anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwf9306987/images/VIVA_LA_JUICY_GOLD_COUTURE_DVIM40004_1.jpg, anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw165ae870/images/VIVA_LA_JUICY_GOLD_COUTURE_DVIM40004_2.jpg?sw=76&sh=76, anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw165ae870/images/VIVA_LA_JUICY_GOLD_COUTURE_DVIM40004_2.jpg?sw=180&sh=180&sm=fit, anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw165ae870/images/VIVA_LA_JUICY_GOLD_COUTURE_DVIM40004_2.jpg, anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw99e50b18/images/Gold_Hero_2104_72dpi_ScentNotes.jpg?sw=76&sh=76, anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw99e50b18/images/Gold_Hero_2104_72dpi_ScentNotes.jpg?sw=180&sh=180&sm=fit, anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw99e50b18/images/Gold_Hero_2104_72dpi_ScentNotes.jpg, anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw5b381179/images/ingredients/2104x2104_VLJ_Gold_Fragrance_Notes.jpg?sw=76&sh=76, anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw5b381179/images/ingredients/2104x2104_VLJ_Gold_Fragrance_Notes.jpg?sw=180&sh=180&sm=fit, anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw5b381179/images/ingredients/2104x2104_VLJ_Gold_Fragrance_Notes.jpg, anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw2d2ffbfa/images/ingredients/Gold_Top_Scent_Note.jpg, anchor_text=Wild Berries, Gardenia, Muguet, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw873c949b/images/ingredients/Gold_Mid_Scent_Note.jpg, anchor_text=Vanilla Orchid, Honeysuckle, Jasmin Sambac, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwb6bdad0d/images/ingredients/Gold_Base_Scent_Note.jpg, anchor_text=Melted Caramel, Vanilla Cream, Praline, Sandalwood, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwfe7a2746/images/OUI_JUICY_COUTURE_A0115017_1.jpg?sw=550&sh=550&sm=fit, anchor_text=OUI Juicy Couture Eau de Parfum Spray, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwfe7a2746/images/OUI_JUICY_COUTURE_A0115017_1.jpg?sw=550&sh=550&sm=fit, anchor_text=OUI Juicy Couture Eau de Parfum Spray, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwfe7a2746/images/OUI_JUICY_COUTURE_A0115017_1.jpg?sw=135&sh=136&sm=fit, anchor_text=OUI Juicy Couture Eau de Parfum Spray, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw1c2d4d4e/images/2104_A0117733_3.4oz_Set.jpg?sw=550&sh=550&sm=fit, anchor_text=OUI Juicy Couture Eau de Parfum 3 Piece Gift Set, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw1c2d4d4e/images/2104_A0117733_3.4oz_Set.jpg?sw=550&sh=550&sm=fit, anchor_text=OUI Juicy Couture Eau de Parfum 3 Piece Gift Set, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw1c2d4d4e/images/2104_A0117733_3.4oz_Set.jpg?sw=135&sh=136&sm=fit, anchor_text=OUI Juicy Couture Eau de Parfum 3 Piece Gift Set, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw3575214d/images/A0116888_026_a4_G7_CAM.jpg?sw=550&sh=550&sm=fit, anchor_text=Juicy Couture Lip + Eye Topper, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw3575214d/images/A0116888_026_a4_G7_CAM.jpg?sw=550&sh=550&sm=fit, anchor_text=Juicy Couture Lip + Eye Topper, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw3575214d/images/A0116888_026_a4_G7_CAM.jpg?sw=135&sh=136&sm=fit, anchor_text=Juicy Couture Lip + Eye Topper, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw7fdfe2da/images/A0116870_GirlStuff_a6_G7_CAM.jpg?sw=550&sh=550&sm=fit, anchor_text=Juicy Couture Glitter Velour Lipstick, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw7fdfe2da/images/A0116870_GirlStuff_a6_G7_CAM.jpg?sw=550&sh=550&sm=fit, anchor_text=Juicy Couture Glitter Velour Lipstick, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw7fdfe2da/images/A0116870_GirlStuff_a6_G7_CAM.jpg?sw=135&sh=136&sm=fit, anchor_text=Juicy Couture Glitter Velour Lipstick, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwe814f031/images/432475_LipstickUVDarling_a7_sRGB.jpg, anchor_text=Juicy Couture Glitter Velour Lipstick, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwe814f031/images/432475_LipstickUVDarling_a7_sRGB.jpg, anchor_text=Juicy Couture Glitter Velour Lipstick, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwe814f031/images/432475_LipstickUVDarling_a7_sRGB.jpg, anchor_text=Juicy Couture Glitter Velour Lipstick, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw61b96daf/images/432475_Ped&Zipped_a6_sRGB.jpg, anchor_text=Juicy Couture Glitter Velour Lipstick, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw61b96daf/images/432475_Ped&Zipped_a6_sRGB.jpg, anchor_text=Juicy Couture Glitter Velour Lipstick, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw61b96daf/images/432475_Ped&Zipped_a6_sRGB.jpg, anchor_text=Juicy Couture Glitter Velour Lipstick, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwb00bdaac/images/432475_NotYourBabe_a6_sRGB.jpg, anchor_text=Juicy Couture Glitter Velour Lipstick, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwb00bdaac/images/432475_NotYourBabe_a6_sRGB.jpg, anchor_text=Juicy Couture Glitter Velour Lipstick, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwb00bdaac/images/432475_NotYourBabe_a6_sRGB.jpg, anchor_text=Juicy Couture Glitter Velour Lipstick, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw225f8997/images/432475_LipstickHappily_108_a7_sRGB.jpg, anchor_text=Juicy Couture Glitter Velour Lipstick, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw225f8997/images/432475_LipstickHappily_108_a7_sRGB.jpg, anchor_text=Juicy Couture Glitter Velour Lipstick, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw225f8997/images/432475_LipstickHappily_108_a7_sRGB.jpg, anchor_text=Juicy Couture Glitter Velour Lipstick, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw52efc995/images/432475_GirlstuffLip_090_a6_sRGB.jpg, anchor_text=Juicy Couture Glitter Velour Lipstick, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw52efc995/images/432475_GirlstuffLip_090_a6_sRGB.jpg, anchor_text=Juicy Couture Glitter Velour Lipstick, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw52efc995/images/432475_GirlstuffLip_090_a6_sRGB.jpg, anchor_text=Juicy Couture Glitter Velour Lipstick, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwe22e1c55/images/Med-Light_Pink_a4_G7_CAM.jpg?sw=550&sh=550&sm=fit, anchor_text=Juicy Couture Lip Luster, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwe22e1c55/images/Med-Light_Pink_a4_G7_CAM.jpg?sw=550&sh=550&sm=fit, anchor_text=Juicy Couture Lip Luster, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwe22e1c55/images/Med-Light_Pink_a4_G7_CAM.jpg?sw=135&sh=136&sm=fit, anchor_text=Juicy Couture Lip Luster, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwb3f85724/images/432475_LipVinylPink_a2a_sRGB.jpg, anchor_text=Juicy Couture Lip Luster, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwb3f85724/images/432475_LipVinylPink_a2a_sRGB.jpg, anchor_text=Juicy Couture Lip Luster, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwb3f85724/images/432475_LipVinylPink_a2a_sRGB.jpg, anchor_text=Juicy Couture Lip Luster, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw9908ab97/images/432475_LipLustre_101_a2a_sRGB.jpg, anchor_text=Juicy Couture Lip Luster, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw9908ab97/images/432475_LipLustre_101_a2a_sRGB.jpg, anchor_text=Juicy Couture Lip Luster, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw9908ab97/images/432475_LipLustre_101_a2a_sRGB.jpg, anchor_text=Juicy Couture Lip Luster, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw2762b0c6/images/432475_LipVinylRed_a4a_sRGB.jpg, anchor_text=Juicy Couture Lip Luster, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw2762b0c6/images/432475_LipVinylRed_a4a_sRGB.jpg, anchor_text=Juicy Couture Lip Luster, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw2762b0c6/images/432475_LipVinylRed_a4a_sRGB.jpg, anchor_text=Juicy Couture Lip Luster, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwff183cc5/images/VIVA_LA_JUICY_JY2F00003_2.jpg?sw=550&sh=550&sm=fit, anchor_text=Viva La Juicy Eau de Parfum Spray, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwff183cc5/images/VIVA_LA_JUICY_JY2F00003_2.jpg?sw=550&sh=550&sm=fit, anchor_text=Viva La Juicy Eau de Parfum Spray, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwff183cc5/images/VIVA_LA_JUICY_JY2F00003_2.jpg?sw=135&sh=136&sm=fit, anchor_text=Viva La Juicy Eau de Parfum Spray, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwd9621392/images/JUICY_COUTURE_CLASSIC_JUIF00005_1.jpg?sw=550&sh=550&sm=fit, anchor_text=Juicy Couture Eau de Parfum Spray, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwd9621392/images/JUICY_COUTURE_CLASSIC_JUIF00005_1.jpg?sw=550&sh=550&sm=fit, anchor_text=Juicy Couture Eau de Parfum Spray, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwd9621392/images/JUICY_COUTURE_CLASSIC_JUIF00005_1.jpg?sw=135&sh=136&sm=fit, anchor_text=Juicy Couture Eau de Parfum Spray, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeauty-Library/default/dw5bf493f3/images/signupimage.jpg, anchor_text=, width=, height=}, {destination_url=https://www.googleadservices.com/pagead/conversion/1036670823/?label=54exCJuQrQEQ566p7gM&guid=ON&script=0, anchor_text=, width=1, height=1}]";
			String unsortedTextString2 = "[{destination_url=https://www.juicycouturebeauty.com/on/demandware.static/Sites-JuicyCoutureBeauty-Site/-/default/dw4b6b7be8/images/Juicy_Couture_3D.png, anchor_text=Juicy Couture Beauty, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwf9306987/images/VIVA_LA_JUICY_GOLD_COUTURE_DVIM40004_1.jpg?sw=535&sh=535&sm=fit, anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwf9306987/images/VIVA_LA_JUICY_GOLD_COUTURE_DVIM40004_1.jpg?sw=276&sh=276&sm=fit, anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwf9306987/images/VIVA_LA_JUICY_GOLD_COUTURE_DVIM40004_1.jpg?sw=600&sh=600&sm=fit, anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwf9306987/images/VIVA_LA_JUICY_GOLD_COUTURE_DVIM40004_1.jpg?sw=76&sh=76, anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwf9306987/images/VIVA_LA_JUICY_GOLD_COUTURE_DVIM40004_1.jpg?sw=180&sh=180&sm=fit, anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwf9306987/images/VIVA_LA_JUICY_GOLD_COUTURE_DVIM40004_1.jpg, anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw165ae870/images/VIVA_LA_JUICY_GOLD_COUTURE_DVIM40004_2.jpg?sw=76&sh=76, anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw165ae870/images/VIVA_LA_JUICY_GOLD_COUTURE_DVIM40004_2.jpg?sw=180&sh=180&sm=fit, anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw165ae870/images/VIVA_LA_JUICY_GOLD_COUTURE_DVIM40004_2.jpg, anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw99e50b18/images/Gold_Hero_2104_72dpi_ScentNotes.jpg?sw=76&sh=76, anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw99e50b18/images/Gold_Hero_2104_72dpi_ScentNotes.jpg?sw=180&sh=180&sm=fit, anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw99e50b18/images/Gold_Hero_2104_72dpi_ScentNotes.jpg, anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw5b381179/images/ingredients/2104x2104_VLJ_Gold_Fragrance_Notes.jpg?sw=76&sh=76, anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw5b381179/images/ingredients/2104x2104_VLJ_Gold_Fragrance_Notes.jpg?sw=180&sh=180&sm=fit, anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw5b381179/images/ingredients/2104x2104_VLJ_Gold_Fragrance_Notes.jpg, anchor_text=Viva La Juicy Gold Couture Eau de Parfum Dual Rollerball, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw2d2ffbfa/images/ingredients/Gold_Top_Scent_Note.jpg, anchor_text=Wild Berries, Gardenia, Muguet, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw873c949b/images/ingredients/Gold_Mid_Scent_Note.jpg, anchor_text=Vanilla Orchid, Honeysuckle, Jasmin Sambac, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwb6bdad0d/images/ingredients/Gold_Base_Scent_Note.jpg, anchor_text=Melted Caramel, Vanilla Cream, Praline, Sandalwood, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwfe7a2746/images/OUI_JUICY_COUTURE_A0115017_1.jpg?sw=550&sh=550&sm=fit, anchor_text=OUI Juicy Couture Eau de Parfum Spray, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwfe7a2746/images/OUI_JUICY_COUTURE_A0115017_1.jpg?sw=550&sh=550&sm=fit, anchor_text=OUI Juicy Couture Eau de Parfum Spray, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwfe7a2746/images/OUI_JUICY_COUTURE_A0115017_1.jpg?sw=135&sh=136&sm=fit, anchor_text=OUI Juicy Couture Eau de Parfum Spray, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw1c2d4d4e/images/2104_A0117733_3.4oz_Set.jpg?sw=550&sh=550&sm=fit, anchor_text=OUI Juicy Couture Eau de Parfum 3 Piece Gift Set, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw1c2d4d4e/images/2104_A0117733_3.4oz_Set.jpg?sw=550&sh=550&sm=fit, anchor_text=OUI Juicy Couture Eau de Parfum 3 Piece Gift Set, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw1c2d4d4e/images/2104_A0117733_3.4oz_Set.jpg?sw=135&sh=136&sm=fit, anchor_text=OUI Juicy Couture Eau de Parfum 3 Piece Gift Set, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw3575214d/images/A0116888_026_a4_G7_CAM.jpg?sw=550&sh=550&sm=fit, anchor_text=Juicy Couture Lip + Eye Topper, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw3575214d/images/A0116888_026_a4_G7_CAM.jpg?sw=550&sh=550&sm=fit, anchor_text=Juicy Couture Lip + Eye Topper, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw3575214d/images/A0116888_026_a4_G7_CAM.jpg?sw=135&sh=136&sm=fit, anchor_text=Juicy Couture Lip + Eye Topper, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw7fdfe2da/images/A0116870_GirlStuff_a6_G7_CAM.jpg?sw=550&sh=550&sm=fit, anchor_text=Juicy Couture Glitter Velour Lipstick, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw7fdfe2da/images/A0116870_GirlStuff_a6_G7_CAM.jpg?sw=550&sh=550&sm=fit, anchor_text=Juicy Couture Glitter Velour Lipstick, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw7fdfe2da/images/A0116870_GirlStuff_a6_G7_CAM.jpg?sw=135&sh=136&sm=fit, anchor_text=Juicy Couture Glitter Velour Lipstick, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwe814f031/images/432475_LipstickUVDarling_a7_sRGB.jpg, anchor_text=Juicy Couture Glitter Velour Lipstick, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwe814f031/images/432475_LipstickUVDarling_a7_sRGB.jpg, anchor_text=Juicy Couture Glitter Velour Lipstick, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwe814f031/images/432475_LipstickUVDarling_a7_sRGB.jpg, anchor_text=Juicy Couture Glitter Velour Lipstick, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw61b96daf/images/432475_Ped&Zipped_a6_sRGB.jpg, anchor_text=Juicy Couture Glitter Velour Lipstick, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw61b96daf/images/432475_Ped&Zipped_a6_sRGB.jpg, anchor_text=Juicy Couture Glitter Velour Lipstick, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw61b96daf/images/432475_Ped&Zipped_a6_sRGB.jpg, anchor_text=Juicy Couture Glitter Velour Lipstick, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwb00bdaac/images/432475_NotYourBabe_a6_sRGB.jpg, anchor_text=Juicy Couture Glitter Velour Lipstick, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwb00bdaac/images/432475_NotYourBabe_a6_sRGB.jpg, anchor_text=Juicy Couture Glitter Velour Lipstick, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwb00bdaac/images/432475_NotYourBabe_a6_sRGB.jpg, anchor_text=Juicy Couture Glitter Velour Lipstick, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw225f8997/images/432475_LipstickHappily_108_a7_sRGB.jpg, anchor_text=Juicy Couture Glitter Velour Lipstick, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw225f8997/images/432475_LipstickHappily_108_a7_sRGB.jpg, anchor_text=Juicy Couture Glitter Velour Lipstick, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw225f8997/images/432475_LipstickHappily_108_a7_sRGB.jpg, anchor_text=Juicy Couture Glitter Velour Lipstick, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw52efc995/images/432475_GirlstuffLip_090_a6_sRGB.jpg, anchor_text=Juicy Couture Glitter Velour Lipstick, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw52efc995/images/432475_GirlstuffLip_090_a6_sRGB.jpg, anchor_text=Juicy Couture Glitter Velour Lipstick, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw52efc995/images/432475_GirlstuffLip_090_a6_sRGB.jpg, anchor_text=Juicy Couture Glitter Velour Lipstick, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwe22e1c55/images/Med-Light_Pink_a4_G7_CAM.jpg?sw=550&sh=550&sm=fit, anchor_text=Juicy Couture Lip Luster, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwe22e1c55/images/Med-Light_Pink_a4_G7_CAM.jpg?sw=550&sh=550&sm=fit, anchor_text=Juicy Couture Lip Luster, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwe22e1c55/images/Med-Light_Pink_a4_G7_CAM.jpg?sw=135&sh=136&sm=fit, anchor_text=Juicy Couture Lip Luster, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwb3f85724/images/432475_LipVinylPink_a2a_sRGB.jpg, anchor_text=Juicy Couture Lip Luster, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwb3f85724/images/432475_LipVinylPink_a2a_sRGB.jpg, anchor_text=Juicy Couture Lip Luster, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwb3f85724/images/432475_LipVinylPink_a2a_sRGB.jpg, anchor_text=Juicy Couture Lip Luster, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw9908ab97/images/432475_LipLustre_101_a2a_sRGB.jpg, anchor_text=Juicy Couture Lip Luster, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw9908ab97/images/432475_LipLustre_101_a2a_sRGB.jpg, anchor_text=Juicy Couture Lip Luster, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw9908ab97/images/432475_LipLustre_101_a2a_sRGB.jpg, anchor_text=Juicy Couture Lip Luster, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw2762b0c6/images/432475_LipVinylRed_a4a_sRGB.jpg, anchor_text=Juicy Couture Lip Luster, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw2762b0c6/images/432475_LipVinylRed_a4a_sRGB.jpg, anchor_text=Juicy Couture Lip Luster, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dw2762b0c6/images/432475_LipVinylRed_a4a_sRGB.jpg, anchor_text=Juicy Couture Lip Luster, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwff183cc5/images/VIVA_LA_JUICY_JY2F00003_2.jpg?sw=550&sh=550&sm=fit, anchor_text=Viva La Juicy Eau de Parfum Spray, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwff183cc5/images/VIVA_LA_JUICY_JY2F00003_2.jpg?sw=550&sh=550&sm=fit, anchor_text=Viva La Juicy Eau de Parfum Spray, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwff183cc5/images/VIVA_LA_JUICY_JY2F00003_2.jpg?sw=135&sh=136&sm=fit, anchor_text=Viva La Juicy Eau de Parfum Spray, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwd9621392/images/JUICY_COUTURE_CLASSIC_JUIF00005_1.jpg?sw=550&sh=550&sm=fit, anchor_text=Juicy Couture Eau de Parfum Spray, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwd9621392/images/JUICY_COUTURE_CLASSIC_JUIF00005_1.jpg?sw=550&sh=550&sm=fit, anchor_text=Juicy Couture Eau de Parfum Spray, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/dw/image/v2/AAHP_PRD/on/demandware.static/-/Sites-JuicyCoutureBeautyMaster/default/dwd9621392/images/JUICY_COUTURE_CLASSIC_JUIF00005_1.jpg?sw=135&sh=136&sm=fit, anchor_text=Juicy Couture Eau de Parfum Spray, width=, height=}, {destination_url=https://www.juicycouturebeauty.com/on/demandware.static/-/Sites-JuicyCoutureBeauty-Library/default/dw5bf493f3/images/signupimage.jpg, anchor_text=, width=, height=}, {destination_url=https://www.googleadservices.com/pagead/conversion/1036670823/?label=54exCJuQrQEQ566p7gM&guid=ON&script=0, anchor_text=, width=1, height=2}]";

			byte[] unsortedByteArray1 = unsortedTextString1.getBytes("UTF8");
			byte[] unsortedByteArray2 = unsortedTextString2.getBytes("UTF8");

			List<Byte> byteList1 = new ArrayList<Byte>();
			for (Byte byte1 : unsortedByteArray1) {
				byteList1.add(byte1);
			}

			List<Byte> byteList2 = new ArrayList<Byte>();
			for (Byte byte2 : unsortedByteArray2) {
				byteList2.add(byte2);
			}

			Collections.sort(byteList1);
			Collections.sort(byteList2);

			String sortedCharactersString1 = byteList1.toString();
			String sortedCharactersString2 = byteList2.toString();

			System.out.println("sortedCharactersString1=" + sortedCharactersString1);
			System.out.println("sortedCharactersString2=" + sortedCharactersString2);

			String md5HashCode1 = CrawlerUtils.getInstance().getMd5HashCode(sortedCharactersString1);
			String md5HashCode2 = CrawlerUtils.getInstance().getMd5HashCode(sortedCharactersString2);

			System.out.println("md5HashCode1=" + md5HashCode1);
			System.out.println("md5HashCode2=" + md5HashCode2);

		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void testGetMessages() {
		try {
			int SQS_MSG_TIMEOUT_IN_30_SEC = 30;
			String queueName = "TARGET_URL_HTML_EN_6643";
			String queueUrl = SQSUtils.getInstance().createQueue(queueName);
			List<Message> messageList = null;
			int messagesPerIteration = 10;
			messageList = SQSUtils.getInstance().getMessageFromQueue(queueUrl, messagesPerIteration, SQS_MSG_TIMEOUT_IN_30_SEC);
			if (messageList != null && messageList.size() > 0) {
				System.out.println("testGetMessages() messageList.size()=" + messageList.size());
				for (Message message : messageList) {
					System.out.println("testGetMessages() message.getBody()=" + message.getBody());
				}
			} else {
				System.out.println("testGetMessages() messageList is empty.");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void testMarshallUrlMetricEntityV3() {
		String messageBody = "{\"targetUrlIndicator\":false,\"domainIdLanguageCodeValueObject\":{\"domainIds\":[7196],\"languageCodes\":[\"es\"]},\"url\":\"https://www.yelp.com/biz/khan-japanese-kitchen-and-bar-annandale-4\"}";
		//String messageBody = "{\"languageCode\":\"en\",\"targetUrlIndicator\":false,\"urlId\":2097263465,\"url\":\"https://www.orbitz.com/Kandestederne-Hotels.d6231075.Travel-Guide-Hotels\"}";
		UrlMetricsEntityV3 messageUrlMetricsEntityV3 = new Gson().fromJson(messageBody, UrlMetricsEntityV3.class);
		System.out.println("messageUrlMetricsEntityV3=" + messageUrlMetricsEntityV3.toString());
	}

	private void testDeserializeJsonToHreflangErrors() {

		// all elements available
		try {
			File inputFile = new File("C://dev//eclipse_workspace_svn_1.6//polite-crawl-maven//src//main//resources//json//hreflang_errors1.json");
			List<String> testStringList = FileUtils.readLines(inputFile, IConstants.UTF_8);
			String json = testStringList.get(0);
			System.out.println("json1=" + json);
			Gson gson = new Gson();
			HreflangErrors hreflangErrors = gson.fromJson(json, HreflangErrors.class);
			if (hreflangErrors != null) {
				System.out.println("deserialzied hreflangErrors1=" + hreflangErrors.toString());
			} else {
				System.out.println("error--hreflangErrors1 is null.");
			}
			//System.out.println("crawlerResponse.getPage_link().toString()="+crawlerResponse.getPage_link().toString());
			String testString = new Gson().toJson(hreflangErrors, HreflangErrors.class);
			System.out.println("serialized hreflangErrors1=" + testString);

		} catch (Exception e) {
			e.printStackTrace();
		}

		// some elements missing
		try {
			File inputFile = new File("C://dev//eclipse_workspace_svn_1.6//polite-crawl-maven//src//main//resources//json//hreflang_errors2.json");
			List<String> testStringList = FileUtils.readLines(inputFile, IConstants.UTF_8);
			String json = testStringList.get(0);
			System.out.println("json2=" + json);
			Gson gson = new Gson();
			HreflangErrors hreflangErrors = gson.fromJson(json, HreflangErrors.class);
			if (hreflangErrors != null) {
				System.out.println("deserialzied hreflangErrors2=" + hreflangErrors.toString());
			} else {
				System.out.println("error--hreflangErrors2 is null.");
			}
			//System.out.println("crawlerResponse.getPage_link().toString()="+crawlerResponse.getPage_link().toString());
			String testString = new Gson().toJson(hreflangErrors, HreflangErrors.class);
			System.out.println("serialized hreflangErrors2=" + testString);

		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void testEncodeUrlString() {
		String decodedUrlString = "https://www.amazon.com/b?ie=UTF8&node=11971391";
		System.out.println("decodedUrlString=" + decodedUrlString);
		String encodedUrlString = null;
		try {
			//encodedUrlString = UriUtils.encodeHttpUrl(decodedUrlString, IConstants.UTF_8);
			encodedUrlString = URLEncoder.encode(decodedUrlString, IConstants.UTF_8);
			System.out.println("encodedUrlString=" + encodedUrlString);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void testExtractPageCrawlerApiRequestHeaderName() {
		String inputString = "pageCrawlerApiRequestHeader-X-Crawl-Request";
		System.out.println("inputString=" + inputString);
		String outputString = StringUtils.substringAfter(inputString, IConstants.PAGE_CRAWLER_API_REQUEST_HEADER + IConstants.DASH);
		System.out.println("outputString=" + outputString);
	}

	private void testSplitTabDelimitedString() {
		String testString = "2020-04-20	1701	http://downloads.seoclarity.net/extract/7934_W_Monterosa_St_Control.htm";
		String[] stringArray = testString.split(IConstants.TAB);
		if (stringArray != null && stringArray.length > 0) {
			System.out.println("stringArray.length=" + stringArray.length);
			for (String string : stringArray) {
				System.out.println("string=" + string);
			}
		} else {
			System.out.println("stringArray is empty.");

		}
	}

	private void testDifferentInSeconds() throws Exception {
		Date currentTimestamp = new Date();
		System.out.println("currentTimestamp=" + currentTimestamp);
		Date crawlTimestamp = DateUtils.parseDate("2020-05-19 23:01:02", new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS });
		System.out.println("crawlTimestamp=" + crawlTimestamp);
		long diffInMilliseconds = currentTimestamp.getTime() - crawlTimestamp.getTime();
		long diffInSeconds = diffInMilliseconds / 1000;
		long diffInMinutes = diffInSeconds / 60;
		System.out.println("diffInMilliseconds=" + diffInMilliseconds);
		System.out.println("diffInSeconds=" + diffInSeconds);
		System.out.println("diffInMinutes=" + diffInMinutes);
	}

	private void testCalculateNumberOfContentGuardUrlGroups() {
		System.out.println("number of Urls per group=" + 10);

		//
		System.out.println("--------------------------------------");
		int totalContentGuardUrls = 1;
		System.out.println("totalContentGuardUrls=" + totalContentGuardUrls);
		int modulus = totalContentGuardUrls % 10;
		//System.out.println("modulus="+modulus);
		int numberOfGroups = totalContentGuardUrls / 10;
		if (modulus > 0) {
			numberOfGroups = numberOfGroups + 1;
		}
		System.out.println("numberOfGroups=" + numberOfGroups);

		//
		System.out.println("--------------------------------------");
		totalContentGuardUrls = 9;
		System.out.println("totalContentGuardUrls=" + totalContentGuardUrls);
		modulus = totalContentGuardUrls % 10;
		//System.out.println("modulus="+modulus);
		numberOfGroups = totalContentGuardUrls / 10;
		if (modulus > 0) {
			numberOfGroups = numberOfGroups + 1;
		}
		System.out.println("numberOfGroups=" + numberOfGroups);

		//
		System.out.println("--------------------------------------");
		totalContentGuardUrls = 10;
		System.out.println("totalContentGuardUrls=" + totalContentGuardUrls);
		modulus = totalContentGuardUrls % 10;
		//System.out.println("modulus="+modulus);
		numberOfGroups = totalContentGuardUrls / 10;
		if (modulus > 0) {
			numberOfGroups = numberOfGroups + 1;
		}
		System.out.println("numberOfGroups=" + numberOfGroups);

		//
		System.out.println("--------------------------------------");
		totalContentGuardUrls = 11;
		System.out.println("totalContentGuardUrls=" + totalContentGuardUrls);
		modulus = totalContentGuardUrls % 10;
		//System.out.println("modulus="+modulus);
		numberOfGroups = totalContentGuardUrls / 10;
		if (modulus > 0) {
			numberOfGroups = numberOfGroups + 1;
		}
		System.out.println("numberOfGroups=" + numberOfGroups);

		//
		System.out.println("--------------------------------------");
		totalContentGuardUrls = 19;
		System.out.println("totalContentGuardUrls=" + totalContentGuardUrls);
		modulus = totalContentGuardUrls % 10;
		//System.out.println("modulus="+modulus);
		numberOfGroups = totalContentGuardUrls / 10;
		if (modulus > 0) {
			numberOfGroups = numberOfGroups + 1;
		}
		System.out.println("numberOfGroups=" + numberOfGroups);

		//
		System.out.println("--------------------------------------");
		totalContentGuardUrls = 20;
		System.out.println("totalContentGuardUrls=" + totalContentGuardUrls);
		modulus = totalContentGuardUrls % 10;
		//System.out.println("modulus="+modulus);
		numberOfGroups = totalContentGuardUrls / 10;
		if (modulus > 0) {
			numberOfGroups = numberOfGroups + 1;
		}
		System.out.println("numberOfGroups=" + numberOfGroups);

		//
		System.out.println("--------------------------------------");
		totalContentGuardUrls = 21;
		System.out.println("totalContentGuardUrls=" + totalContentGuardUrls);
		modulus = totalContentGuardUrls % 10;
		//System.out.println("modulus="+modulus);
		numberOfGroups = totalContentGuardUrls / 10;
		if (modulus > 0) {
			numberOfGroups = numberOfGroups + 1;
		}
		System.out.println("numberOfGroups=" + numberOfGroups);
	}

	private void testGroupStartingIndex() {
		int modulus = 0;
		int contentGroupUrlNumber = 0;
		int contentGroupUrlStartingIndex = 0;

		System.out.println("------------------------------------------");
		contentGroupUrlNumber = 1;
		System.out.println("contentGroupUrlNumber=" + contentGroupUrlNumber);
		contentGroupUrlStartingIndex = (contentGroupUrlNumber * 10) - 10;
		System.out.println("contentGroupUrlStartingIndex=" + contentGroupUrlStartingIndex);

		System.out.println("------------------------------------------");
		contentGroupUrlNumber = 2;
		System.out.println("contentGroupUrlNumber=" + contentGroupUrlNumber);
		contentGroupUrlStartingIndex = (contentGroupUrlNumber * 10) - 10;
		System.out.println("contentGroupUrlStartingIndex=" + contentGroupUrlStartingIndex);

		System.out.println("------------------------------------------");
		contentGroupUrlNumber = 3;
		System.out.println("contentGroupUrlNumber=" + contentGroupUrlNumber);
		contentGroupUrlStartingIndex = (contentGroupUrlNumber * 10) - 10;
		System.out.println("contentGroupUrlStartingIndex=" + contentGroupUrlStartingIndex);

	}

	private void testGetDomainIdFromQueueName() throws Exception {
		String queueName = null;
		int domainId = 0;

		// CONTENT_GUARD_1701_1
		System.out.println("-----------------------------");
		queueName = "CONTENT_GUARD_1701_1";
		System.out.println("queueName=" + queueName);
		domainId = CrawlerUtils.getInstance().getDomainIdFromQueueName(queueName);
		System.out.println("domainId=" + domainId);

		// TARGET_URL_HTML_EN_1897
		System.out.println("-----------------------------");
		queueName = "TARGET_URL_HTML_EN_1897";
		System.out.println("queueName=" + queueName);
		domainId = CrawlerUtils.getInstance().getDomainIdFromQueueName(queueName);
		System.out.println("domainId=" + domainId);

		// LINK_CLARITY_EN_7028
		System.out.println("-----------------------------");
		queueName = "LINK_CLARITY_EN_7028";
		System.out.println("queueName=" + queueName);
		domainId = CrawlerUtils.getInstance().getDomainIdFromQueueName(queueName);
		System.out.println("domainId=" + domainId);

	}

	private void testGetS3Location() throws Exception {
		String s3FileName = "1701_d9c062092bb1c063dc0a5591166710c5.warc";
		System.out.println("s3FileName=" + s3FileName);
		String s3Location = CrawlerUtils.getInstance().getS3Location(s3FileName);
		System.out.println("s3Location=" + s3Location);
	}

	private void testRemoveDomainNameFromUrl() {
		String outputString = null;
		String inputString = null;
		URL url = null;
		String protocol = null;
		String host = null;
		int port = 0;
		String urlPrefix = null;
		String portString = null;

		try {
			//inputString = "https://www.test.com/index.html";
			//inputString = "https://www.test.com:80/index.html";
			//inputString = "/index.html";
			//inputString = "https://www.test.com";
			//inputString = "https://www.test.com/";
			//inputString = "zillow_fb:home";
			inputString = "https://www.zillow.com:443/homedetails/164-22nd-Ave-Seattle-WA-98122/49146632_zpid/";
			outputString = inputString;
			if (StringUtils.isNotBlank(inputString)) {
				url = new URL(inputString);
				protocol = url.getProtocol();
				host = url.getHost();
				port = url.getPort();
				if (port != -1) {
					portString = ":" + port;
				} else {
					portString = "";
				}
				urlPrefix = protocol + "://" + host + portString;
				outputString = StringUtils.substringAfter(inputString, urlPrefix);
			}
		} catch (Exception e) {
		}
		System.out.println("inputString=" + inputString);
		System.out.println("protocol=" + protocol);
		System.out.println("host=" + host);
		System.out.println("port=" + port);
		System.out.println("urlPrefix=" + urlPrefix);
		System.out.println("outputString=" + outputString);
	}

	private void testMinusOneDay() {
		Date todayTimestamp = new Date();
		System.out.println("todayTimestamp=" + todayTimestamp);
		System.out.println("yesterdayTimestamp=" + DateUtils.addDays(todayTimestamp, -1));

		Date currentTrackDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
		System.out.println("currentTrackDate=" + currentTrackDate);
		System.out.println("yesterdayTrackDate=" + DateUtils.addDays(currentTrackDate, -1));
	}

	private void testReversePageAnalysisResults() throws Exception {
		List<PageAnalysisResult> pageAnalysisResultList = new ArrayList<PageAnalysisResult>();
		PageAnalysisResult[] pageAnalysisResultArray = null;
		PageAnalysisResult pageAnalysisResult = null;

		// 1st true
		pageAnalysisResult = new PageAnalysisResult();
		pageAnalysisResult.setRule(1);
		pageAnalysisResult.setResult(0);
		pageAnalysisResultList.add(pageAnalysisResult);

		// 2nd true
		pageAnalysisResult = new PageAnalysisResult();
		pageAnalysisResult.setRule(2);
		pageAnalysisResult.setResult(0);
		pageAnalysisResultList.add(pageAnalysisResult);

		// 3rd false
		pageAnalysisResult = new PageAnalysisResult();
		pageAnalysisResult.setRule(3);
		pageAnalysisResult.setResult(0);
		pageAnalysisResultList.add(pageAnalysisResult);

		pageAnalysisResultArray = pageAnalysisResultList.toArray(new PageAnalysisResult[0]);
		String pageAnalysisResultsReversed = CrawlerUtils.getInstance().reversePageAnalysisResults(pageAnalysisResultArray);
		System.out.println("pageAnalysisResultsReversed=" + pageAnalysisResultsReversed);
	}

	private void testExtractQueueNameFromQueueUrl() {
		String queueName = null;
		String queueUrl = "https://sqs.us-west-2.amazonaws.com/397485469449/LINK_CLARITY_DA_3764";
		System.out.println("queueUrl=" + queueUrl);
		String[] stringArray = queueUrl.split(IConstants.SLASH);
		if (stringArray != null && stringArray.length > 0) {
			queueName = stringArray[stringArray.length - 1];
		}
		System.out.println("queueName=" + queueName);
	}

	private void testGetCustomDataSelector() {
		String inputString = "/html/body//text()[contains(translate(., 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'),'mandalay bay')]";
		String outputString = StringUtils.removeStart(inputString,
				"/html/body//text()[contains(translate(., 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'),'");
		System.out.println("outputString=" + StringUtils.removeEnd(outputString, "')]"));

	}

	private void testGetSortedCharactersHashCode() throws Exception {
		String json1 = "{\"data\":{\"check_structured_data\":[{\"data_type\":\"WebPage\",\"encoding\":\"microdata\"},{\"data_type\":\"Article\",\"encoding\":\"microdata\"},{\"data_type\":\"Organization\",\"encoding\":\"json-ld\"},{\"data_type\":\"BreadcrumbList\",\"encoding\":\"json-ld\"}],\"validate_structured_data\":[{\"data_type\":\"WebPage\",\"encoding\":\"microdata\",\"errors\":[]},{\"data_type\":\"Article\",\"encoding\":\"microdata\",\"errors\":[{\"message\":\"\u0027ratingValue\u0027 is a required property\",\"type\":\"missingField\",\"path\":[\"aggregateRating\"]},{\"message\":\"\u0027ratingCount\u0027 is a required property\",\"type\":\"missingField\",\"path\":[\"aggregateRating\"]},{\"message\":\"\u0027reviewCount\u0027 is a required property\",\"type\":\"missingField\",\"path\":[\"aggregateRating\"]},{\"message\":\"\u0027description\u0027 is a required property\",\"type\":\"missingField\",\"path\":[]}]},{\"data_type\":\"Organization\",\"encoding\":\"json-ld\",\"errors\":[]},{\"data_type\":\"BreadcrumbList\",\"encoding\":\"json-ld\",\"errors\":[{\"message\":\"url is a required property\",\"type\":\"badField\",\"path\":[\"itemListElement\",\"0\"]},{\"message\":\"name is a required property\",\"type\":\"badField\",\"path\":[\"itemListElement\",\"1\"]},{\"message\":\"name is a required property\",\"type\":\"badField\",\"path\":[\"itemListElement\",\"2\"]}]}]}}";
		String hashCode1 = CrawlerUtils.getInstance().getSortedCharactersHashCode(json1);
		System.out.println("hashCode1=" + hashCode1);
		String json2 = "{\"data\":{\"check_structured_data\":[{\"data_type\":\"Organization\",\"encoding\":\"json-ld\"},{\"data_type\":\"BreadcrumbList\",\"encoding\":\"json-ld\"},{\"data_type\":\"WebPage\",\"encoding\":\"microdata\"},{\"data_type\":\"Article\",\"encoding\":\"microdata\"}],\"validate_structured_data\":[{\"data_type\":\"Organization\",\"encoding\":\"json-ld\",\"errors\":[]},{\"data_type\":\"BreadcrumbList\",\"encoding\":\"json-ld\",\"errors\":[{\"message\":\"name is a required property\",\"type\":\"badField\",\"path\":[\"itemListElement\",\"0\"]},{\"message\":\"name is a required property\",\"type\":\"badField\",\"path\":[\"itemListElement\",\"1\"]},{\"message\":\"name is a required property\",\"type\":\"badField\",\"path\":[\"itemListElement\",\"2\"]}]},{\"data_type\":\"WebPage\",\"encoding\":\"microdata\",\"errors\":[]},{\"data_type\":\"Article\",\"encoding\":\"microdata\",\"errors\":[{\"message\":\"\u0027ratingValue\u0027 is a required property\",\"type\":\"missingField\",\"path\":[\"aggregateRating\"]},{\"message\":\"\u0027ratingCount\u0027 is a required property\",\"type\":\"missingField\",\"path\":[\"aggregateRating\"]},{\"message\":\"\u0027reviewCount\u0027 is a required property\",\"type\":\"missingField\",\"path\":[\"aggregateRating\"]},{\"message\":\"\u0027description\u0027 is a required property\",\"type\":\"missingField\",\"path\":[]}]}]}}";
		String hashCode2 = CrawlerUtils.getInstance().getSortedCharactersHashCode(json2);
		System.out.println("hashCode2=" + hashCode2);
	}

	private void testIsCrawlable() throws Exception {
		String urlString = "https://www.seoclarity.net/hubfs/CS_PM%20Digital.pdf?";
		System.out.println("urlString=" + urlString);
		Boolean isCrawlable = CrawlerUtils.getInstance().isCrawlable(urlString);
		System.out.println("isCrawlable=" + isCrawlable);
	}

	private void testBooleanToString() {
		Boolean testBoolean = false;
		System.out.println("testBoolean=" + testBoolean);
		if (testBoolean != null) {
			System.out.println("testBoolean.toString()=" + testBoolean.toString());
		}
	}

	private void testExtractDateHourFromCrawlTimestamp() {
		String crawlTimestamp = "2020-06-24 15:22:35";
		//System.out.println("crawlTimestamp=" + crawlTimestamp);
		String crawlDate = StringUtils.substring(crawlTimestamp, 0, 10);
		//System.out.println("crawlDate=" + crawlDate);
		Integer crawlHour = NumberUtils.toInt(StringUtils.substring(crawlTimestamp, 11, 13));
		//System.out.println("crawlHour=" + crawlHour);
		String inputJson = "[\"<EMAIL>\",\"<EMAIL>\"]";
		String[] emailAddressArray = new Gson().fromJson(inputJson, String[].class);
		//for (String emailAddress : emailAddressArray) {
		//	System.out.println("emailAddress="+emailAddress);
		//}
		int hour = 23;
		String startCrawlHour = String.format("%02d", hour);
		System.out.println("startCrawlHour=" + startCrawlHour);
	}

	// C:/dev/eclipse-workspace/polite-crawl-put-messages/src/main/resources/simhash_test_data/test.html
	//	private void testSimhashBetweenTwoFiles() throws Exception {
	//		String[] inputFileLocationPathArray = new String[] {
	//				"C:/dev/eclipse-workspace/polite-crawl-put-messages/src/main/resources/simhash_test_data/simhash_test1.html",
	//				"C:/dev/eclipse-workspace/polite-crawl-put-messages/src/main/resources/simhash_test_data/simhash_test3.html" };
	//		String textString = null;
	//		File inputFile = null;
	//		List<String> testStringList = null;
	//		String htmlSource = null;
	//		StringBuilder stringBuilder = null;
	//		Long simhash = null;
	//		String binaryString = null;
	//		byte[] byteArray1 = null;
	//		byte[] byteArray2 = null;
	//		int totalDifferentBits = 0;
	//		Byte byte1 = null;
	//		Byte byte2 = null;
	//		String inputFileLocationPath = null;
	//		for (int i = 0; i < inputFileLocationPathArray.length; i++) {
	//			inputFileLocationPath = inputFileLocationPathArray[i];
	//			// retrieve the HTML source
	//			inputFile = new File(inputFileLocationPath);
	//			//System.out.println("inputFileLocationPath=" + inputFileLocationPath);
	//			testStringList = FileUtils.readLines(inputFile, IConstants.UTF_8);
	//			if (testStringList != null && testStringList.size() > 0) {
	//				stringBuilder = new StringBuilder();
	//				for (String testString : testStringList) {
	//					stringBuilder.append(testString);
	//				}
	//				htmlSource = stringBuilder.toString();
	//
	//				// extract the content text to remove HTML elements
	//				textString = CrawlerUtils.getInstance().extractBodyContent(htmlSource);
	//
	//				// trim the content text to remove line feed, carriage return, tabs, etc.
	//				//textString = CrawlerUtils.getInstance().trimText(textString);
	//				System.out.println("textString=" + textString);
	//
	//				simhash = SimHash.computeOptimizedSimHashForString(textString);
	//				System.out.println("simhash=" + simhash);
	//
	//				binaryString = Long.toBinaryString(simhash);
	//				System.out.println("binaryString=" + binaryString);
	//				if (i == 0) {
	//					byteArray1 = binaryString.getBytes();
	//				} else if (i == 1) {
	//					byteArray2 = binaryString.getBytes();
	//				}
	//			}
	//		}
	//		for (int j = 0; j < byteArray1.length; j++) {
	//			byte1 = byteArray1[j];
	//			byte2 = byteArray2[j];
	//			if (Byte.compare(byte1, byte2) != 0) {
	//				totalDifferentBits++;
	//			}
	//		}
	//		System.out.println("totalDifferentBits=" + totalDifferentBits);
	//	}

	// C:/dev/eclipse-workspace/polite-crawl-put-messages/src/main/resources/simhash_test_data/test.html
	//	private void testSimhashBetweenTwoStrings() throws Exception {
	//		String[] inputStringArray = new String[] { "the cat sat on the mat", "the cat sat on a mat" };
	//		String textString = null;
	//		String htmlSource = null;
	//		Long simhash = null;
	//		String binaryString = null;
	//		byte[] byteArray1 = null;
	//		byte[] byteArray2 = null;
	//		int totalDifferentBits = 0;
	//		Byte byte1 = null;
	//		Byte byte2 = null;
	//		for (int i = 0; i < inputStringArray.length; i++) {
	//			htmlSource = inputStringArray[i];
	//
	//			// extract the content text to remove HTML elements
	//			textString = CrawlerUtils.getInstance().extractBodyContent(htmlSource);
	//
	//			// trim the content text to remove line feed, carriage return, tabs, etc.
	//			//textString = CrawlerUtils.getInstance().trimText(textString);
	//			System.out.println("textString=" + textString);
	//
	//			simhash = SimHash.computeOptimizedSimHashForString(textString);
	//			System.out.println("simhash=" + simhash);
	//
	//			binaryString = Long.toBinaryString(simhash);
	//			System.out.println("binaryString=" + binaryString);
	//			if (i == 0) {
	//				byteArray1 = binaryString.getBytes();
	//			} else if (i == 1) {
	//				byteArray2 = binaryString.getBytes();
	//			}
	//		}
	//		for (int j = 0; j < byteArray1.length; j++) {
	//			byte1 = byteArray1[j];
	//			byte2 = byteArray2[j];
	//			if (Byte.compare(byte1, byte2) != 0) {
	//				totalDifferentBits++;
	//			}
	//		}
	//		System.out.println("totalDifferentBits=" + totalDifferentBits);
	//	}

	private void testCalculateMd5HashForTestUrls() throws Exception {
		String md5Hash = null;
		HtmlClickHouseEntity htmlClickHouseEntity = null;
		for (String testUrl : getTestUrls()) {
			md5Hash = Md5Util.Md5(testUrl);
			htmlClickHouseEntity = ContentGuardClickHouseDAO.getInstance().getHashCodes(testUrl);
			if (htmlClickHouseEntity != null) {
				System.out.println("md5Hash`" + md5Hash + "`url`" + testUrl + "`urlHash`" + htmlClickHouseEntity.getUrlHash() + "`urlMurmurHash`"
						+ htmlClickHouseEntity.getUrlMurmurHash());
			}
		}
	}

	private void testCheckIfClientDomainUrl() {
		String urlStringToBeValidated = "https://www.actonia.com/test1.html";
		//String urlStringToBeValidated = "https://www.seoclarity.net/test1.html";
		System.out.println("urlStringToBeValidated=" + urlStringToBeValidated);
		String clientDomainName = "www.seoclarity.net";
		System.out.println("clientDomainName=" + clientDomainName);
		boolean isBroadMatch = true;
		System.out.println("isBroadMatch=" + isBroadMatch);
		boolean isClientDomainUrl = UrlMetricsUtil.checkIfClientDomainUrl(urlStringToBeValidated, clientDomainName, isBroadMatch);
		System.out.println("isClientDomainUrl=" + isClientDomainUrl);
	}

	private String[] getTestUrls() {
		return new String[] { "https://test.edgeseo.dev/681358337/page_14.html", };
	}

	private void testConvertIntSetToString() {
		Set<Integer> inputSet = new HashSet<Integer>();
		inputSet.add(1);
		inputSet.add(2);
		inputSet.add(3);
		inputSet.add(4);
		inputSet.add(5);
		inputSet.add(6);
		inputSet.add(7);
		inputSet.add(8);
		String outputString = FormatUtils.getInstance().convertIntSetToString(inputSet);
		System.out.println("outputString=" + outputString);
	}

	private void testConvertSlackValueObjectToJson() {
		SlackRequest slackRequest = null;
		SlackBlock[] blocks = null;
		SlackBlock slackBlock = null;
		SlackText slackText = null;
		String json = null;

		List<SlackBlock> slackBlockList = new ArrayList<SlackBlock>();

		// emailSubject
		slackText = new SlackText();
		slackText.setType("mrkdwn");
		slackText.setText("*Content Guard Alerts for www.ahs.com (Daily Group 1) as of 2021-01-22 00:01:10*");
		slackBlock = new SlackBlock();
		slackBlock.setType("section");
		slackBlock.setText(slackText);
		slackBlockList.add(slackBlock);

		// groupInformation1, groupInformation2, groupInformation3, domainId
		slackText = new SlackText();
		slackText.setType("mrkdwn");
		slackText.setText(
				"_The following alerts were generated for all the changes in the 'Daily Group 1' group._\n_The 3 URLs in this group are crawled daily._\n_The changes were found on 2021-01-21._\n_The domain ID is 9678._");
		slackBlock = new SlackBlock();
		slackBlock.setType("section");
		slackBlock.setText(slackText);
		slackBlockList.add(slackBlock);

		// top changes detected
		slackText = new SlackText();
		slackText.setType("mrkdwn");
		slackText.setText("*Top Changes Detected - Count*\nCustom data changed - 168\nPage links changed - 138\nResponse headers added - 28");
		slackBlock = new SlackBlock();
		slackBlock.setType("section");
		slackBlock.setText(slackText);
		slackBlockList.add(slackBlock);

		// page analysis rules
		slackText = new SlackText();
		slackText.setType("mrkdwn");
		slackText.setText(
				"*Page Analysis Rule - Total New - Total Resolved*\nTitle tag &lt;title&gt; missing - 178 - 28\nHreflang with duplicate language/region combinations - 138 - 18\nMeta description tag &lt;meta name=\"description\"&gt; too long - 118 - 8");
		slackBlock = new SlackBlock();
		slackBlock.setType("section");
		slackBlock.setText(slackText);
		slackBlockList.add(slackBlock);

		// top pages
		slackText = new SlackText();
		slackText.setType("mrkdwn");
		slackText.setText(
				"*Top Pages - Changes*\nhttps://www.choicehomewarranty.com/user-agreement/ - 168\nhttps://www.totalhomeprotection.com/service_agreement - 68\nhttps://selecthomewarranty.com/termsconditions/ - 8");
		slackBlock = new SlackBlock();
		slackBlock.setType("section");
		slackBlock.setText(slackText);
		slackBlockList.add(slackBlock);

		blocks = slackBlockList.toArray(new SlackBlock[0]);

		slackRequest = new SlackRequest();
		slackRequest.setBlocks(blocks);

		json = new Gson().toJson(slackRequest, SlackRequest.class);

		System.out.println("json=" + json);

	}

	private void testContentGuardUrlChangesUrlAscendingComparator() {
		List<ContentGuardUrlChanges> contentGuardUrlChangesList = new ArrayList<ContentGuardUrlChanges>();
		ContentGuardUrlChanges contentGuardUrlChanges = null;

		// 1st object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_5.html");
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 2nd object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_1.html");
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 3rd object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_9.html");
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		for (ContentGuardUrlChanges testContentGuardUrlChanges : contentGuardUrlChangesList) {
			System.out.println("b4 sort url=" + testContentGuardUrlChanges.getUrl());
		}

		Collections.sort(contentGuardUrlChangesList, new ContentGuardUrlChangesUrlAscendingComparator());

		for (ContentGuardUrlChanges testContentGuardUrlChanges : contentGuardUrlChangesList) {
			System.out.println("af sort url=" + testContentGuardUrlChanges.getUrl());
		}
	}

	private void testContentGuardUrlChangesUrlDescendingComparator() {
		List<ContentGuardUrlChanges> contentGuardUrlChangesList = new ArrayList<ContentGuardUrlChanges>();
		ContentGuardUrlChanges contentGuardUrlChanges = null;

		// 1st object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_5.html");
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 2nd object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_1.html");
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 3rd object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_9.html");
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		for (ContentGuardUrlChanges testContentGuardUrlChanges : contentGuardUrlChangesList) {
			System.out.println("b4 sort url=" + testContentGuardUrlChanges.getUrl());
		}

		Collections.sort(contentGuardUrlChangesList, new ContentGuardUrlChangesUrlDescendingComparator());

		for (ContentGuardUrlChanges testContentGuardUrlChanges : contentGuardUrlChangesList) {
			System.out.println("af sort url=" + testContentGuardUrlChanges.getUrl());
		}
	}

	private void testContentGuardUrlChangesCrawlTimestampAscendingComparator() {
		List<ContentGuardUrlChanges> contentGuardUrlChangesList = new ArrayList<ContentGuardUrlChanges>();
		ContentGuardUrlChanges contentGuardUrlChanges = null;

		// 6th object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_6.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:30");
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 1st object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_5.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:32");
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 2nd object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_1.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:30");
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 3rd object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_9.html");
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 5th object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_2.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:34");
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		for (ContentGuardUrlChanges testContentGuardUrlChanges : contentGuardUrlChangesList) {
			System.out.println("b4 crawl timestamp=" + testContentGuardUrlChanges.getCurrent_crawl_timestamp() + ",url=" + testContentGuardUrlChanges.getUrl());
		}

		Collections.sort(contentGuardUrlChangesList, new ContentGuardUrlChangesCrawlTimestampAscendingComparator());

		for (ContentGuardUrlChanges testContentGuardUrlChanges : contentGuardUrlChangesList) {
			System.out.println("af crawl timestamp=" + testContentGuardUrlChanges.getCurrent_crawl_timestamp() + ",url=" + testContentGuardUrlChanges.getUrl());
		}
	}

	private void testContentGuardUrlChangesCrawlTimestampDescendingComparator() {
		List<ContentGuardUrlChanges> contentGuardUrlChangesList = new ArrayList<ContentGuardUrlChanges>();
		ContentGuardUrlChanges contentGuardUrlChanges = null;

		// 6th object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_6.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:30");
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 1st object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_5.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:32");
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 2nd object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_1.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:30");
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 3rd object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_9.html");
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 5th object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_2.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:34");
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		for (ContentGuardUrlChanges testContentGuardUrlChanges : contentGuardUrlChangesList) {
			System.out.println("b4 crawl timestamp=" + testContentGuardUrlChanges.getCurrent_crawl_timestamp() + ",url=" + testContentGuardUrlChanges.getUrl());
		}

		Collections.sort(contentGuardUrlChangesList, new ContentGuardUrlChangesCrawlTimestampDescendingComparator());

		for (ContentGuardUrlChanges testContentGuardUrlChanges : contentGuardUrlChangesList) {
			System.out.println("af crawl timestamp=" + testContentGuardUrlChanges.getCurrent_crawl_timestamp() + ",url=" + testContentGuardUrlChanges.getUrl());
		}
	}

	private void testContentGuardUrlChangesTotalChangesAscendingComparator() {
		List<ContentGuardUrlChanges> contentGuardUrlChangesList = new ArrayList<ContentGuardUrlChanges>();
		ContentGuardUrlChanges contentGuardUrlChanges = null;

		// 6th object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_6.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:30");
		contentGuardUrlChanges.setTotal_changes(6);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 1st object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_5.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:32");
		contentGuardUrlChanges.setTotal_changes(5);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 2nd object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_1.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:30");
		contentGuardUrlChanges.setTotal_changes(6);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 3rd object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_9.html");
		contentGuardUrlChanges.setTotal_changes(0);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 5th object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_2.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:34");
		contentGuardUrlChanges.setTotal_changes(2);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		for (ContentGuardUrlChanges testContentGuardUrlChanges : contentGuardUrlChangesList) {
			System.out.println("b4 total changes=" + testContentGuardUrlChanges.getTotal_changes() + ",url=" + testContentGuardUrlChanges.getUrl());
		}

		Collections.sort(contentGuardUrlChangesList, new ContentGuardUrlChangesTotalChangesAscendingComparator());

		for (ContentGuardUrlChanges testContentGuardUrlChanges : contentGuardUrlChangesList) {
			System.out.println("af total changes=" + testContentGuardUrlChanges.getTotal_changes() + ",url=" + testContentGuardUrlChanges.getUrl());
		}
	}

	private void testContentGuardUrlChangesTotalChangesDescendingComparator() {
		List<ContentGuardUrlChanges> contentGuardUrlChangesList = new ArrayList<ContentGuardUrlChanges>();
		ContentGuardUrlChanges contentGuardUrlChanges = null;

		// 6th object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_6.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:30");
		contentGuardUrlChanges.setTotal_changes(6);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 1st object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_5.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:32");
		contentGuardUrlChanges.setTotal_changes(5);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 2nd object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_1.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:30");
		contentGuardUrlChanges.setTotal_changes(6);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 3rd object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_9.html");
		contentGuardUrlChanges.setTotal_changes(0);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 5th object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_2.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:34");
		contentGuardUrlChanges.setTotal_changes(2);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		for (ContentGuardUrlChanges testContentGuardUrlChanges : contentGuardUrlChangesList) {
			System.out.println("b4 total changes=" + testContentGuardUrlChanges.getTotal_changes() + ",url=" + testContentGuardUrlChanges.getUrl());
		}

		Collections.sort(contentGuardUrlChangesList, new ContentGuardUrlChangesTotalChangesDescendingComparator());

		for (ContentGuardUrlChanges testContentGuardUrlChanges : contentGuardUrlChangesList) {
			System.out.println("af total changes=" + testContentGuardUrlChanges.getTotal_changes() + ",url=" + testContentGuardUrlChanges.getUrl());
		}
	}

	private void testContentGuardUrlChangesAddedAscendingComparator() {
		List<ContentGuardUrlChanges> contentGuardUrlChangesList = new ArrayList<ContentGuardUrlChanges>();
		ContentGuardUrlChanges contentGuardUrlChanges = null;

		// 6th object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_6.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:30");
		contentGuardUrlChanges.setTotal_changes(6);
		contentGuardUrlChanges.setAdded(32);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 1st object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_5.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:32");
		contentGuardUrlChanges.setTotal_changes(5);
		contentGuardUrlChanges.setAdded(1);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 2nd object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_1.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:30");
		contentGuardUrlChanges.setTotal_changes(6);
		contentGuardUrlChanges.setAdded(2);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 3rd object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_9.html");
		contentGuardUrlChanges.setTotal_changes(0);
		contentGuardUrlChanges.setAdded(0);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 5th object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_2.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:34");
		contentGuardUrlChanges.setTotal_changes(2);
		contentGuardUrlChanges.setAdded(32);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		for (ContentGuardUrlChanges testContentGuardUrlChanges : contentGuardUrlChangesList) {
			System.out.println("b4 added=" + testContentGuardUrlChanges.getAdded() + ",url=" + testContentGuardUrlChanges.getUrl());
		}

		Collections.sort(contentGuardUrlChangesList, new ContentGuardUrlChangesAddedAscendingComparator());

		for (ContentGuardUrlChanges testContentGuardUrlChanges : contentGuardUrlChangesList) {
			System.out.println("af added=" + testContentGuardUrlChanges.getAdded() + ",url=" + testContentGuardUrlChanges.getUrl());
		}
	}

	private void testContentGuardUrlChangesAddedDescendingComparator() {
		List<ContentGuardUrlChanges> contentGuardUrlChangesList = new ArrayList<ContentGuardUrlChanges>();
		ContentGuardUrlChanges contentGuardUrlChanges = null;

		// 6th object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_6.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:30");
		contentGuardUrlChanges.setTotal_changes(6);
		contentGuardUrlChanges.setAdded(32);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 1st object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_5.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:32");
		contentGuardUrlChanges.setTotal_changes(5);
		contentGuardUrlChanges.setAdded(1);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 2nd object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_1.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:30");
		contentGuardUrlChanges.setTotal_changes(6);
		contentGuardUrlChanges.setAdded(2);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 3rd object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_9.html");
		contentGuardUrlChanges.setTotal_changes(0);
		contentGuardUrlChanges.setAdded(0);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 5th object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_2.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:34");
		contentGuardUrlChanges.setTotal_changes(2);
		contentGuardUrlChanges.setAdded(32);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		for (ContentGuardUrlChanges testContentGuardUrlChanges : contentGuardUrlChangesList) {
			System.out.println("b4 added=" + testContentGuardUrlChanges.getAdded() + ",url=" + testContentGuardUrlChanges.getUrl());
		}

		Collections.sort(contentGuardUrlChangesList, new ContentGuardUrlChangesAddedDescendingComparator());

		for (ContentGuardUrlChanges testContentGuardUrlChanges : contentGuardUrlChangesList) {
			System.out.println("af added=" + testContentGuardUrlChanges.getAdded() + ",url=" + testContentGuardUrlChanges.getUrl());
		}
	}

	private void testContentGuardUrlChangesModifiedAscendingComparator() {
		List<ContentGuardUrlChanges> contentGuardUrlChangesList = new ArrayList<ContentGuardUrlChanges>();
		ContentGuardUrlChanges contentGuardUrlChanges = null;

		// 6th object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_6.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:30");
		contentGuardUrlChanges.setTotal_changes(6);
		contentGuardUrlChanges.setModified(123);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 1st object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_5.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:32");
		contentGuardUrlChanges.setTotal_changes(5);
		contentGuardUrlChanges.setModified(123);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 2nd object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_1.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:30");
		contentGuardUrlChanges.setTotal_changes(6);
		contentGuardUrlChanges.setModified(238);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 3rd object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_9.html");
		contentGuardUrlChanges.setTotal_changes(0);
		contentGuardUrlChanges.setModified(0);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 5th object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_2.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:34");
		contentGuardUrlChanges.setTotal_changes(2);
		contentGuardUrlChanges.setModified(321);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		for (ContentGuardUrlChanges testContentGuardUrlChanges : contentGuardUrlChangesList) {
			System.out.println("b4 modified=" + testContentGuardUrlChanges.getModified() + ",url=" + testContentGuardUrlChanges.getUrl());
		}

		Collections.sort(contentGuardUrlChangesList, new ContentGuardUrlChangesModifiedAscendingComparator());

		for (ContentGuardUrlChanges testContentGuardUrlChanges : contentGuardUrlChangesList) {
			System.out.println("af modified=" + testContentGuardUrlChanges.getModified() + ",url=" + testContentGuardUrlChanges.getUrl());
		}
	}

	private void testContentGuardUrlChangesModifiedDescendingComparator() {
		List<ContentGuardUrlChanges> contentGuardUrlChangesList = new ArrayList<ContentGuardUrlChanges>();
		ContentGuardUrlChanges contentGuardUrlChanges = null;

		// 6th object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_6.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:30");
		contentGuardUrlChanges.setTotal_changes(6);
		contentGuardUrlChanges.setModified(123);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 1st object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_5.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:32");
		contentGuardUrlChanges.setTotal_changes(5);
		contentGuardUrlChanges.setModified(123);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 2nd object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_1.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:30");
		contentGuardUrlChanges.setTotal_changes(6);
		contentGuardUrlChanges.setModified(238);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 3rd object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_9.html");
		contentGuardUrlChanges.setTotal_changes(0);
		contentGuardUrlChanges.setModified(0);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 5th object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_2.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:34");
		contentGuardUrlChanges.setTotal_changes(2);
		contentGuardUrlChanges.setModified(321);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		for (ContentGuardUrlChanges testContentGuardUrlChanges : contentGuardUrlChangesList) {
			System.out.println("b4 modified=" + testContentGuardUrlChanges.getModified() + ",url=" + testContentGuardUrlChanges.getUrl());
		}

		Collections.sort(contentGuardUrlChangesList, new ContentGuardUrlChangesModifiedDescendingComparator());

		for (ContentGuardUrlChanges testContentGuardUrlChanges : contentGuardUrlChangesList) {
			System.out.println("af modified=" + testContentGuardUrlChanges.getModified() + ",url=" + testContentGuardUrlChanges.getUrl());
		}
	}

	private void testContentGuardUrlChangesRemovedAscendingComparator() {
		List<ContentGuardUrlChanges> contentGuardUrlChangesList = new ArrayList<ContentGuardUrlChanges>();
		ContentGuardUrlChanges contentGuardUrlChanges = null;

		// 6th object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_6.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:30");
		contentGuardUrlChanges.setTotal_changes(6);
		contentGuardUrlChanges.setRemoved(1);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 1st object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_5.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:32");
		contentGuardUrlChanges.setTotal_changes(5);
		contentGuardUrlChanges.setRemoved(33);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 2nd object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_1.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:30");
		contentGuardUrlChanges.setTotal_changes(6);
		contentGuardUrlChanges.setRemoved(56);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 3rd object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_9.html");
		contentGuardUrlChanges.setTotal_changes(0);
		contentGuardUrlChanges.setRemoved(0);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 5th object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_2.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:34");
		contentGuardUrlChanges.setTotal_changes(2);
		contentGuardUrlChanges.setRemoved(1);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		for (ContentGuardUrlChanges testContentGuardUrlChanges : contentGuardUrlChangesList) {
			System.out.println("b4 removed=" + testContentGuardUrlChanges.getRemoved() + ",url=" + testContentGuardUrlChanges.getUrl());
		}

		Collections.sort(contentGuardUrlChangesList, new ContentGuardUrlChangesRemovedAscendingComparator());

		for (ContentGuardUrlChanges testContentGuardUrlChanges : contentGuardUrlChangesList) {
			System.out.println("af removed=" + testContentGuardUrlChanges.getRemoved() + ",url=" + testContentGuardUrlChanges.getUrl());
		}
	}

	private void testContentGuardUrlChangesRemovedDescendingComparator() {
		List<ContentGuardUrlChanges> contentGuardUrlChangesList = new ArrayList<ContentGuardUrlChanges>();
		ContentGuardUrlChanges contentGuardUrlChanges = null;

		// 6th object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_6.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:30");
		contentGuardUrlChanges.setTotal_changes(6);
		contentGuardUrlChanges.setRemoved(1);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 1st object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_5.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:32");
		contentGuardUrlChanges.setTotal_changes(5);
		contentGuardUrlChanges.setRemoved(33);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 2nd object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_1.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:30");
		contentGuardUrlChanges.setTotal_changes(6);
		contentGuardUrlChanges.setRemoved(56);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 3rd object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_9.html");
		contentGuardUrlChanges.setTotal_changes(0);
		contentGuardUrlChanges.setRemoved(0);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		// 5th object
		contentGuardUrlChanges = new ContentGuardUrlChanges();
		contentGuardUrlChanges.setUrl("https://www.test.com/page_2.html");
		contentGuardUrlChanges.setCurrent_crawl_timestamp("2021-01-05 13:45:34");
		contentGuardUrlChanges.setTotal_changes(2);
		contentGuardUrlChanges.setRemoved(1);
		contentGuardUrlChangesList.add(contentGuardUrlChanges);

		for (ContentGuardUrlChanges testContentGuardUrlChanges : contentGuardUrlChangesList) {
			System.out.println("b4 removed=" + testContentGuardUrlChanges.getRemoved() + ",url=" + testContentGuardUrlChanges.getUrl());
		}

		Collections.sort(contentGuardUrlChangesList, new ContentGuardUrlChangesRemovedDescendingComparator());

		for (ContentGuardUrlChanges testContentGuardUrlChanges : contentGuardUrlChangesList) {
			System.out.println("af removed=" + testContentGuardUrlChanges.getRemoved() + ",url=" + testContentGuardUrlChanges.getUrl());
		}
	}

	private void testCalculateFromToIndexForPageNumberRowsPerPage() {
		int pageNumber = 3;
		int rowsPerPage = 50;
		boolean endOfData = false;

		List<String> stringList = new ArrayList<String>();
		for (int i = 1; i <= 200; i++) {
			stringList.add(String.valueOf(i));
		}
		//for (String testString : stringList) {
		//	System.out.println("testString="+testString);
		//}
		System.out.println("stringList.size()=" + stringList.size());

		int fromIndex = (pageNumber - 1) * rowsPerPage;
		System.out.println("fromIndex=" + fromIndex);

		int toIndex = pageNumber * rowsPerPage;
		System.out.println("toIndex=" + toIndex);

		List<String> subList = null;

		if (fromIndex <= (stringList.size() - 1)) {
			if (toIndex > stringList.size()) {
				toIndex = stringList.size();
				System.out.println("revised toIndex=" + toIndex);
			}
			subList = stringList.subList(fromIndex, toIndex);
			for (String subString : subList) {
				System.out.println("subString=" + subString);
			}
			if (toIndex == stringList.size()) {
				endOfData = true;
			} else {
				endOfData = false;
			}
			System.out.println("endOfData=" + endOfData);
		} else {
			System.out.println("outside of the data range");
		}
	}

	private void testParseAlternateLinks() {
		String testString = "[{\"href\":\"https://www.overstock.com/Electronics/Marine-GPS/On-Sale,/sale,/28217/subcat.html\"},{\"href\":\"https://www.overstock.ca/Electronics/Marine-GPS/On-Sale,/sale,/28217/subcat.html\"}]";
		AlternateLinks[] alternateLinksArray = new Gson().fromJson(testString, AlternateLinks[].class);
		for (AlternateLinks alternateLinks : alternateLinksArray) {
			System.out.println("alternateLinks.getHref()=" + alternateLinks.getHref());
		}

		ContentGuardChangeDetails contentGuardChangeDetails = new ContentGuardChangeDetails();

		CustomData[] customDataPreviousArray = new Gson().fromJson(contentGuardChangeDetails.getPrevious_content(), CustomData[].class);
		CustomData[] customDataCurrentArray = new Gson().fromJson(contentGuardChangeDetails.getCurrent_content(), CustomData[].class);

		for (CustomData customDataPrevious : customDataPreviousArray) {
			System.out.println("customDataPrevious.getIndex()=" + customDataPrevious.getIndex());
			System.out.println("customDataPrevious.getSelector_type()=" + customDataPrevious.getSelector_type());
			System.out.println("customDataPrevious.getSelector()=" + customDataPrevious.getSelector());
			System.out.println("customDataPrevious.getWord_count()=" + customDataPrevious.getWord_count());
			for (String contentPrevious : customDataPrevious.getContent()) {
				System.out.println("contentPrevious=" + contentPrevious);
			}
			for (Links linksPrevious : customDataPrevious.getLinks()) {
				System.out.println("linksPrevious.getAnchor_text()=" + linksPrevious.getAnchor_text());
				System.out.println("linksPrevious.getDestination_url()=" + linksPrevious.getDestination_url());
				System.out.println("linksPrevious.getRel_attribute_contents()=" + linksPrevious.getRel_attribute_contents());
			}
		}

		for (CustomData customDataCurrent : customDataCurrentArray) {
			System.out.println("customDataCurrent.getIndex()=" + customDataCurrent.getIndex());
			System.out.println("customDataCurrent.getSelector_type()=" + customDataCurrent.getSelector_type());
			System.out.println("customDataCurrent.getSelector()=" + customDataCurrent.getSelector());
			System.out.println("customDataCurrent.getWord_count()=" + customDataCurrent.getWord_count());
			for (String contentCurrent : customDataCurrent.getContent()) {
				System.out.println("contentCurrent=" + contentCurrent);
			}
			for (Links linksCurrent : customDataCurrent.getLinks()) {
				System.out.println("linksCurrent.getAnchor_text()=" + linksCurrent.getAnchor_text());
				System.out.println("linksCurrent.getDestination_url()=" + linksCurrent.getDestination_url());
				System.out.println("linksCurrent.getRel_attribute_contents()=" + linksCurrent.getRel_attribute_contents());
			}
		}

		String[] h2PreviousArray = new Gson().fromJson(contentGuardChangeDetails.getPrevious_content(), String[].class);
		String[] h2CurrentArray = new Gson().fromJson(contentGuardChangeDetails.getCurrent_content(), String[].class);

		for (String h2Previous : h2PreviousArray) {
			System.out.println("h2Previous=" + h2Previous);
		}

		for (String h2Current : h2CurrentArray) {
			System.out.println("h2Current=" + h2Current);
		}

		HreflangErrors hreflangErrorsPrevious = new Gson().fromJson(contentGuardChangeDetails.getPrevious_content(), HreflangErrors.class);
		HreflangErrors hreflangErrorsCurrent = new Gson().fromJson(contentGuardChangeDetails.getCurrent_content(), HreflangErrors.class);

		if (hreflangErrorsPrevious != null) {
			if (hreflangErrorsPrevious.getAlternate_url() != null) {
				System.out.println("hreflangErrorsPrevious.getAlternate_url().isStatus()=" + hreflangErrorsPrevious.getAlternate_url().isStatus());
				System.out.println("hreflangErrorsPrevious.getAlternate_url().getUrl()=" + hreflangErrorsPrevious.getAlternate_url().getUrl());
			}

			if (hreflangErrorsPrevious.getHas_multiple_defaults() != null) {
				System.out.println("hreflangErrorsPrevious.getHas_multiple_defaults().isStatus()=" + hreflangErrorsPrevious.getHas_multiple_defaults().isStatus());
				if (hreflangErrorsPrevious.getHas_multiple_defaults().getUrls() != null && hreflangErrorsPrevious.getHas_multiple_defaults().getUrls().length > 0) {
					for (String testUrl : hreflangErrorsPrevious.getHas_multiple_defaults().getUrls()) {
						System.out.println("hreflangErrorsPrevious.getHas_multiple_defaults().getUrls()=" + testUrl);
					}
				}
			}

			if (hreflangErrorsPrevious.getHreflang_keys_with_multiple_entries() != null) {
				System.out.println("hreflangErrorsPrevious.getHreflang_keys_with_multiple_entries().isStatus()="
						+ hreflangErrorsPrevious.getHreflang_keys_with_multiple_entries().isStatus());
				if (hreflangErrorsPrevious.getHreflang_keys_with_multiple_entries().getUrls() != null
						&& hreflangErrorsPrevious.getHreflang_keys_with_multiple_entries().getUrls().length > 0) {
					for (String testUrl : hreflangErrorsPrevious.getHreflang_keys_with_multiple_entries().getUrls()) {
						System.out.println("hreflangErrorsPrevious.getHreflang_keys_with_multiple_entries().getUrls()=" + testUrl);
					}
				}
			}

			if (hreflangErrorsPrevious.getInvalid_languages() != null) {
				System.out.println("hreflangErrorsPrevious.getInvalid_languages().isStatus()=" + hreflangErrorsPrevious.getInvalid_languages().isStatus());
				if (hreflangErrorsPrevious.getInvalid_languages().getEntries() != null && hreflangErrorsPrevious.getInvalid_languages().getEntries().length > 0) {
					for (Entries entries : hreflangErrorsPrevious.getInvalid_languages().getEntries()) {
						System.out.println("hreflangErrorsPrevious.getInvalid_languages().getEntries().getLang()=" + entries.getLang());
						System.out.println("hreflangErrorsPrevious.getInvalid_languages().getEntries().getHref()=" + entries.getHref());
					}
				}
			}

			if (hreflangErrorsPrevious.getInvalid_regions() != null) {
				System.out.println("hreflangErrorsPrevious.getInvalid_regions().isStatus()=" + hreflangErrorsPrevious.getInvalid_regions().isStatus());
				if (hreflangErrorsPrevious.getInvalid_regions().getEntries() != null && hreflangErrorsPrevious.getInvalid_regions().getEntries().length > 0) {
					for (Entries entries : hreflangErrorsPrevious.getInvalid_regions().getEntries()) {
						System.out.println("hreflangErrorsPrevious.getInvalid_regions().getEntries().getLang()=" + entries.getLang());
						System.out.println("hreflangErrorsPrevious.getInvalid_regions().getEntries().getHref()=" + entries.getHref());
					}
				}
			}

			if (hreflangErrorsPrevious.getLanguage_missing_entries() != null) {
				System.out
						.println("hreflangErrorsPrevious.getLanguage_missing_entries().isStatus()=" + hreflangErrorsPrevious.getLanguage_missing_entries().isStatus());
				if (hreflangErrorsPrevious.getLanguage_missing_entries().getEntries() != null
						&& hreflangErrorsPrevious.getLanguage_missing_entries().getEntries().length > 0) {
					for (Entries entries : hreflangErrorsPrevious.getLanguage_missing_entries().getEntries()) {
						System.out.println("hreflangErrorsPrevious.getLanguage_missing_entries().getEntries().getLang()=" + entries.getLang());
						System.out.println("hreflangErrorsPrevious.getLanguage_missing_entries().getEntries().getHref()=" + entries.getHref());
					}
				}
			}

			if (hreflangErrorsPrevious.getLanguages_missing_standalone_entries() != null) {
				System.out.println("hreflangErrorsPrevious.getLanguages_missing_standalone_entries().isStatus()="
						+ hreflangErrorsPrevious.getLanguages_missing_standalone_entries().isStatus());
				if (hreflangErrorsPrevious.getLanguages_missing_standalone_entries().getUrls() != null
						&& hreflangErrorsPrevious.getLanguages_missing_standalone_entries().getUrls().length > 0) {
					for (String testUrl : hreflangErrorsPrevious.getLanguages_missing_standalone_entries().getUrls()) {
						System.out.println("hreflangErrorsPrevious.getLanguages_missing_standalone_entries().getUrls()=" + testUrl);
					}
				}
			}

			if (hreflangErrorsPrevious.getNo_return_tag_pages() != null) {
				System.out.println("hreflangErrorsPrevious.getNo_return_tag_pages().isStatus()=" + hreflangErrorsPrevious.getNo_return_tag_pages().isStatus());
				System.out.println("hreflangErrorsPrevious.getNo_return_tag_pages().getUrl()=" + hreflangErrorsPrevious.getNo_return_tag_pages().getUrl());
			}

			if (hreflangErrorsPrevious.getRegion_missing_entries() != null) {
				System.out.println("hreflangErrorsPrevious.getRegion_missing_entries().isStatus()=" + hreflangErrorsPrevious.getRegion_missing_entries().isStatus());
				if (hreflangErrorsPrevious.getRegion_missing_entries().getEntries() != null
						&& hreflangErrorsPrevious.getRegion_missing_entries().getEntries().length > 0) {
					for (Entries entries : hreflangErrorsPrevious.getRegion_missing_entries().getEntries()) {
						System.out.println("hreflangErrorsPrevious.getRegion_missing_entries().getEntries().getLang()=" + entries.getLang());
						System.out.println("hreflangErrorsPrevious.getRegion_missing_entries().getEntries().getHref()=" + entries.getHref());
					}
				}
			}
			System.out.println("hreflangErrorsPrevious.getIs_default()=" + hreflangErrorsPrevious.getIs_default());
			System.out.println("hreflangErrorsPrevious.getIs_self_reference()=" + hreflangErrorsPrevious.getIs_self_reference());
		}

		if (hreflangErrorsCurrent != null) {
			if (hreflangErrorsCurrent.getAlternate_url() != null) {
				System.out.println("hreflangErrorsCurrent.getAlternate_url().isStatus()=" + hreflangErrorsCurrent.getAlternate_url().isStatus());
				System.out.println("hreflangErrorsCurrent.getAlternate_url().getUrl()=" + hreflangErrorsCurrent.getAlternate_url().getUrl());
			}

			if (hreflangErrorsCurrent.getHas_multiple_defaults() != null) {
				System.out.println("hreflangErrorsCurrent.getHas_multiple_defaults().isStatus()=" + hreflangErrorsCurrent.getHas_multiple_defaults().isStatus());
				if (hreflangErrorsCurrent.getHas_multiple_defaults().getUrls() != null && hreflangErrorsCurrent.getHas_multiple_defaults().getUrls().length > 0) {
					for (String testUrl : hreflangErrorsCurrent.getHas_multiple_defaults().getUrls()) {
						System.out.println("hreflangErrorsCurrent.getHas_multiple_defaults().getUrls()=" + testUrl);
					}
				}
			}

			if (hreflangErrorsCurrent.getHreflang_keys_with_multiple_entries() != null) {
				System.out.println("hreflangErrorsCurrent.getHreflang_keys_with_multiple_entries().isStatus()="
						+ hreflangErrorsCurrent.getHreflang_keys_with_multiple_entries().isStatus());
				if (hreflangErrorsCurrent.getHreflang_keys_with_multiple_entries().getUrls() != null
						&& hreflangErrorsCurrent.getHreflang_keys_with_multiple_entries().getUrls().length > 0) {
					for (String testUrl : hreflangErrorsCurrent.getHreflang_keys_with_multiple_entries().getUrls()) {
						System.out.println("hreflangErrorsCurrent.getHreflang_keys_with_multiple_entries().getUrls()=" + testUrl);
					}
				}
			}

			if (hreflangErrorsCurrent.getInvalid_languages() != null) {
				System.out.println("hreflangErrorsCurrent.getInvalid_languages().isStatus()=" + hreflangErrorsCurrent.getInvalid_languages().isStatus());
				if (hreflangErrorsCurrent.getInvalid_languages().getEntries() != null && hreflangErrorsCurrent.getInvalid_languages().getEntries().length > 0) {
					for (Entries entries : hreflangErrorsCurrent.getInvalid_languages().getEntries()) {
						System.out.println("hreflangErrorsCurrent.getInvalid_languages().getEntries().getLang()=" + entries.getLang());
						System.out.println("hreflangErrorsCurrent.getInvalid_languages().getEntries().getHref()=" + entries.getHref());
					}
				}
			}

			if (hreflangErrorsCurrent.getInvalid_regions() != null) {
				System.out.println("hreflangErrorsCurrent.getInvalid_regions().isStatus()=" + hreflangErrorsCurrent.getInvalid_regions().isStatus());
				if (hreflangErrorsCurrent.getInvalid_regions().getEntries() != null && hreflangErrorsCurrent.getInvalid_regions().getEntries().length > 0) {
					for (Entries entries : hreflangErrorsCurrent.getInvalid_regions().getEntries()) {
						System.out.println("hreflangErrorsCurrent.getInvalid_regions().getEntries().getLang()=" + entries.getLang());
						System.out.println("hreflangErrorsCurrent.getInvalid_regions().getEntries().getHref()=" + entries.getHref());
					}
				}
			}

			if (hreflangErrorsCurrent.getLanguage_missing_entries() != null) {
				System.out.println("hreflangErrorsCurrent.getLanguage_missing_entries().isStatus()=" + hreflangErrorsCurrent.getLanguage_missing_entries().isStatus());
				if (hreflangErrorsCurrent.getLanguage_missing_entries().getEntries() != null
						&& hreflangErrorsCurrent.getLanguage_missing_entries().getEntries().length > 0) {
					for (Entries entries : hreflangErrorsCurrent.getLanguage_missing_entries().getEntries()) {
						System.out.println("hreflangErrorsCurrent.getLanguage_missing_entries().getEntries().getLang()=" + entries.getLang());
						System.out.println("hreflangErrorsCurrent.getLanguage_missing_entries().getEntries().getHref()=" + entries.getHref());
					}
				}
			}

			if (hreflangErrorsCurrent.getLanguages_missing_standalone_entries() != null) {
				System.out.println("hreflangErrorsCurrent.getLanguages_missing_standalone_entries().isStatus()="
						+ hreflangErrorsCurrent.getLanguages_missing_standalone_entries().isStatus());
				if (hreflangErrorsCurrent.getLanguages_missing_standalone_entries().getUrls() != null
						&& hreflangErrorsCurrent.getLanguages_missing_standalone_entries().getUrls().length > 0) {
					for (String testUrl : hreflangErrorsCurrent.getLanguages_missing_standalone_entries().getUrls()) {
						System.out.println("hreflangErrorsCurrent.getLanguages_missing_standalone_entries().getUrls()=" + testUrl);
					}
				}
			}

			if (hreflangErrorsCurrent.getNo_return_tag_pages() != null) {
				System.out.println("hreflangErrorsCurrent.getNo_return_tag_pages().isStatus()=" + hreflangErrorsCurrent.getNo_return_tag_pages().isStatus());
				System.out.println("hreflangErrorsCurrent.getNo_return_tag_pages().getUrl()=" + hreflangErrorsCurrent.getNo_return_tag_pages().getUrl());
			}

			if (hreflangErrorsCurrent.getRegion_missing_entries() != null) {
				System.out.println("hreflangErrorsCurrent.getRegion_missing_entries().isStatus()=" + hreflangErrorsCurrent.getRegion_missing_entries().isStatus());
				if (hreflangErrorsCurrent.getRegion_missing_entries().getEntries() != null
						&& hreflangErrorsCurrent.getRegion_missing_entries().getEntries().length > 0) {
					for (Entries entries : hreflangErrorsCurrent.getRegion_missing_entries().getEntries()) {
						System.out.println("hreflangErrorsCurrent.getRegion_missing_entries().getEntries().getLang()=" + entries.getLang());
						System.out.println("hreflangErrorsCurrent.getRegion_missing_entries().getEntries().getHref()=" + entries.getHref());
					}
				}
			}
			System.out.println("hreflangErrorsCurrent.getIs_default()=" + hreflangErrorsCurrent.getIs_default());
			System.out.println("hreflangErrorsCurrent.getIs_self_reference()=" + hreflangErrorsCurrent.getIs_self_reference());
		}

		HreflangLinks[] hreflangLinksPreviousArray = new Gson().fromJson(contentGuardChangeDetails.getPrevious_content(), HreflangLinks[].class);
		HreflangLinks[] hreflangLinksCurrentArray = new Gson().fromJson(contentGuardChangeDetails.getCurrent_content(), HreflangLinks[].class);

		for (HreflangLinks hreflangLinksPrevious : hreflangLinksPreviousArray) {
			System.out.println("hreflangLinksPrevious.getType()=" + hreflangLinksPrevious.getType());
			System.out.println("hreflangLinksPrevious.getHref()=" + hreflangLinksPrevious.getHref());
			System.out.println("hreflangLinksPrevious.getLang()=" + hreflangLinksPrevious.getLang());
		}

		for (HreflangLinks hreflangLinksCurrent : hreflangLinksCurrentArray) {
			System.out.println("hreflangLinksCurrent.getType()=" + hreflangLinksCurrent.getType());
			System.out.println("hreflangLinksCurrent.getHref()=" + hreflangLinksCurrent.getHref());
			System.out.println("hreflangLinksCurrent.getLang()=" + hreflangLinksCurrent.getLang());
		}

		String[] insecureResourcesPrevious = new Gson().fromJson(contentGuardChangeDetails.getPrevious_content(), String[].class);
		String[] insecureResourcesCurrent = new Gson().fromJson(contentGuardChangeDetails.getCurrent_content(), String[].class);
		for (String insecureResourcePrevious : insecureResourcesPrevious) {
			System.out.println("insecureResourcePrevious =" + insecureResourcePrevious);
		}

		for (String insecureResourceCurrent : insecureResourcesCurrent) {
			System.out.println("insecureResourceCurrent =" + insecureResourceCurrent);
		}

		OgMarkup[] ogMarkupPreviousArray = new Gson().fromJson(contentGuardChangeDetails.getPrevious_content(), OgMarkup[].class);
		OgMarkup[] ogMarkupCurrentArray = new Gson().fromJson(contentGuardChangeDetails.getCurrent_content(), OgMarkup[].class);

		if (ogMarkupPreviousArray != null && ogMarkupPreviousArray.length > 0) {
			for (OgMarkup ogMarkupPrevious : ogMarkupPreviousArray) {
				System.out.println("ogMarkupPrevious.getContent()=" + ogMarkupPrevious.getContent());
				System.out.println("ogMarkupPrevious.getProperty()=" + ogMarkupPrevious.getProperty());
			}
		}

		if (ogMarkupCurrentArray != null && ogMarkupCurrentArray.length > 0) {
			for (OgMarkup ogMarkupCurrent : ogMarkupCurrentArray) {
				System.out.println("ogMarkupCurrent.getContent()=" + ogMarkupCurrent.getContent());
				System.out.println("ogMarkupCurrent.getProperty()=" + ogMarkupCurrent.getProperty());
			}
		}

		PageAnalysisResultChgInd[] pageAnalysisResultChgIndArray = null;
		JsonObject rootObject = null;
		PageAnalysisResultChgInd pageAnalysisResultChgInd = null;
		int index = 0;
		JsonParser parser = new JsonParser();
		JsonElement jsonElement = parser.parse(contentGuardChangeDetails.getCurrent_content());
		if (jsonElement != null) {
			rootObject = jsonElement.getAsJsonObject();
			if (rootObject != null) {
				pageAnalysisResultChgIndArray = new PageAnalysisResultChgInd[rootObject.keySet().size()];
				for (String ruleNbr : rootObject.keySet()) {
					pageAnalysisResultChgInd = new PageAnalysisResultChgInd();
					pageAnalysisResultChgInd.setRule_nbr(NumberUtils.toInt(ruleNbr));
					pageAnalysisResultChgInd.setChg_ind(NumberUtils.toInt(rootObject.get(ruleNbr).getAsString()));
					pageAnalysisResultChgIndArray[index++] = pageAnalysisResultChgInd;
				}
			} else {
				System.out.println("getPageAnalysisResultChgIndArray() error--rootObject is null");
			}
		} else {
			System.out.println("getPageAnalysisResultChgIndArray() error--jsonElement is null");
		}

		if (pageAnalysisResultChgIndArray != null && pageAnalysisResultChgIndArray.length > 0) {
			for (PageAnalysisResultChgInd testPageAnalysisResultChgInd : pageAnalysisResultChgIndArray) {
				// issue created during this crawl
				if (testPageAnalysisResultChgInd.getChg_ind() == 1) {
					System.out.println("page analysis rule number=" + testPageAnalysisResultChgInd.getRule_nbr() + " created.");
				}
				// issue resolved during this crawl
				else if (testPageAnalysisResultChgInd.getChg_ind() == 0) {
					System.out.println("page analysis rule number=" + testPageAnalysisResultChgInd.getRule_nbr() + " resolved.");

				}
			}
		}

		PageLink[] pageLinkPreviousArray = new Gson().fromJson(contentGuardChangeDetails.getPrevious_content(), PageLink[].class);
		PageLink[] pageLinkCurrentArray = new Gson().fromJson(contentGuardChangeDetails.getCurrent_content(), PageLink[].class);

		if (pageLinkPreviousArray != null && pageLinkPreviousArray.length > 0) {
			for (PageLink pageLinkPrevious : pageLinkPreviousArray) {
				System.out.println("pageLinkPrevious.getAnchor_text()=" + pageLinkPrevious.getAnchor_text());
				System.out.println("pageLinkPrevious.getDestination_url()=" + pageLinkPrevious.getDestination_url());
				System.out.println("pageLinkPrevious.getFooter_link()=" + pageLinkPrevious.getFooter_link());
				System.out.println("pageLinkPrevious.getHeader_link()=" + pageLinkPrevious.getHeader_link());
				System.out.println("pageLinkPrevious.getHidden_link()=" + pageLinkPrevious.getHidden_link());
				System.out.println("pageLinkPrevious.getImage_link()=" + pageLinkPrevious.getImage_link());
				System.out.println("pageLinkPrevious.getNofollow_link()=" + pageLinkPrevious.getNofollow_link());
			}
		}

		if (pageLinkCurrentArray != null && pageLinkCurrentArray.length > 0) {
			for (PageLink pageLinkCurrent : pageLinkCurrentArray) {
				System.out.println("pageLinkCurrent.getAnchor_text()=" + pageLinkCurrent.getAnchor_text());
				System.out.println("pageLinkCurrent.getDestination_url()=" + pageLinkCurrent.getDestination_url());
				System.out.println("pageLinkCurrent.getFooter_link()=" + pageLinkCurrent.getFooter_link());
				System.out.println("pageLinkCurrent.getHeader_link()=" + pageLinkCurrent.getHeader_link());
				System.out.println("pageLinkCurrent.getHidden_link()=" + pageLinkCurrent.getHidden_link());
				System.out.println("pageLinkCurrent.getImage_link()=" + pageLinkCurrent.getImage_link());
				System.out.println("pageLinkCurrent.getNofollow_link()=" + pageLinkCurrent.getNofollow_link());
			}
		}

		RedirectChain[] redirectChainPreviousArray = new Gson().fromJson(contentGuardChangeDetails.getPrevious_content(), RedirectChain[].class);
		RedirectChain[] redirectChainCurrentArray = new Gson().fromJson(contentGuardChangeDetails.getCurrent_content(), RedirectChain[].class);

		if (redirectChainPreviousArray != null && redirectChainPreviousArray.length > 0) {
			for (RedirectChain redirectChainPrevious : redirectChainPreviousArray) {
				System.out.println("redirectChainPrevious.getRedirected_url()=" + redirectChainPrevious.getRedirected_url());
				System.out.println("redirectChainPrevious.getResponse_code()=" + redirectChainPrevious.getResponse_code());
				System.out.println("redirectChainPrevious.getUrl()=" + redirectChainPrevious.getUrl());
			}
		}
		if (redirectChainCurrentArray != null && redirectChainCurrentArray.length > 0) {
			for (RedirectChain redirectChainCurrent : redirectChainCurrentArray) {
				System.out.println("redirectChainCurrent.getRedirected_url()=" + redirectChainCurrent.getRedirected_url());
				System.out.println("redirectChainCurrent.getResponse_code()=" + redirectChainCurrent.getResponse_code());
				System.out.println("redirectChainCurrent.getUrl()=" + redirectChainCurrent.getUrl());
			}
		}

		String[] responseHeaderPreviousArray = new Gson().fromJson(contentGuardChangeDetails.getPrevious_content(), String[].class);
		String[] responseHeaderCurrentArray = new Gson().fromJson(contentGuardChangeDetails.getCurrent_content(), String[].class);

		if (responseHeaderPreviousArray != null && responseHeaderPreviousArray.length > 0) {
			for (String responseHeaderPrevious : responseHeaderPreviousArray) {
				System.out.println("responseHeaderPrevious=" + responseHeaderPrevious);
			}
		}

		if (responseHeaderCurrentArray != null && responseHeaderCurrentArray.length > 0) {
			for (String responseHeaderCurrent : responseHeaderCurrentArray) {
				System.out.println("responseHeaderCurrent=" + responseHeaderCurrent);
			}
		}

		StructuredData structuredDataPrevious = new Gson().fromJson(contentGuardChangeDetails.getPrevious_content(), StructuredData.class);
		StructuredData structuredDataCurrent = new Gson().fromJson(contentGuardChangeDetails.getCurrent_content(), StructuredData.class);

		Data data = null;
		if (structuredDataPrevious != null) {
			data = structuredDataPrevious.getData();
			if (data.getCheck_structured_data() != null && data.getCheck_structured_data().length > 0) {
				for (CheckStructuredData checkStructuredData : data.getCheck_structured_data()) {
					System.out.println("previous checkStructuredData.getData_type()=" + checkStructuredData.getData_type());
					System.out.println("previous checkStructuredData.getEncoding()=" + checkStructuredData.getEncoding());
				}
			}
			if (data.getValidate_structured_data() != null && data.getValidate_structured_data().length > 0) {
				for (ValidateStructuredData validateStructuredData : data.getValidate_structured_data()) {
					System.out.println("previous validateStructuredData.getData_type()=" + validateStructuredData.getData_type());
					System.out.println("previous validateStructuredData.getEncoding()=" + validateStructuredData.getEncoding());
					if (validateStructuredData.getErrors() != null && validateStructuredData.getErrors().length > 0) {
						for (Errors errors : validateStructuredData.getErrors()) {
							System.out.println("previous errors.getMessage()=" + errors.getMessage());
							System.out.println("previous errors.getType()=" + errors.getType());
							if (errors.getPath() != null && errors.getPath().length > 0) {
								for (String errorPath : errors.getPath()) {
									System.out.println("previous errorPath=" + errorPath);
								}
							}
						}
					}
				}
			}
		}
		if (structuredDataCurrent != null) {
			data = structuredDataCurrent.getData();
			if (data.getCheck_structured_data() != null && data.getCheck_structured_data().length > 0) {
				for (CheckStructuredData checkStructuredData : data.getCheck_structured_data()) {
					System.out.println("current checkStructuredData.getData_type()=" + checkStructuredData.getData_type());
					System.out.println("current checkStructuredData.getEncoding()=" + checkStructuredData.getEncoding());
				}
			}
			if (data.getValidate_structured_data() != null && data.getValidate_structured_data().length > 0) {
				for (ValidateStructuredData validateStructuredData : data.getValidate_structured_data()) {
					System.out.println("current validateStructuredData.getData_type()=" + validateStructuredData.getData_type());
					System.out.println("current validateStructuredData.getEncoding()=" + validateStructuredData.getEncoding());
					if (validateStructuredData.getErrors() != null && validateStructuredData.getErrors().length > 0) {
						for (Errors errors : validateStructuredData.getErrors()) {
							System.out.println("current errors.getMessage()=" + errors.getMessage());
							System.out.println("current errors.getType()=" + errors.getType());
							if (errors.getPath() != null && errors.getPath().length > 0) {
								for (String errorPath : errors.getPath()) {
									System.out.println("current errorPath=" + errorPath);
								}
							}
						}
					}
				}
			}
		}

	}

	private void testDailyGroupTimelineSeverity() {

		ContentGuardResourceResponse response = new ContentGuardResourceResponse();
		response.setSuccess(true);
		response.setGroup_id(1L);

		// daily_group_timeline_list

		ContentGuardDailyGroupTimeline contentGuardDailyGroupTimeline = null;
		List<ContentGuardDailyGroupTimeline> contentGuardDailyGroupTimelineList = new ArrayList<ContentGuardDailyGroupTimeline>();

		// 
		contentGuardDailyGroupTimeline = new ContentGuardDailyGroupTimeline();
		contentGuardDailyGroupTimeline.setCrawl_date("2021-01-12");
		contentGuardDailyGroupTimeline.setTotal_changes(49);
		contentGuardDailyGroupTimelineList.add(contentGuardDailyGroupTimeline);

		// 
		contentGuardDailyGroupTimeline = new ContentGuardDailyGroupTimeline();
		contentGuardDailyGroupTimeline.setCrawl_date("2021-01-18");
		contentGuardDailyGroupTimeline.setTotal_changes(21);
		contentGuardDailyGroupTimelineList.add(contentGuardDailyGroupTimeline);

		response.setDaily_group_timeline_list(contentGuardDailyGroupTimelineList.toArray(new ContentGuardDailyGroupTimeline[0]));

		// daily_group_severity_list
		ContentGuardDailyGroupSeverity contentGuardDailyGroupSeverity = null;
		List<ContentGuardDailyGroupSeverity> contentGuardDailyGroupSeverityList = new ArrayList<ContentGuardDailyGroupSeverity>();
		List<ContentGuardSeverity> contentGuardSeverityList = null;
		ContentGuardSeverity contentGuardSeverity = null;

		// 
		contentGuardDailyGroupSeverity = new ContentGuardDailyGroupSeverity();
		contentGuardDailyGroupSeverity.setCrawl_date("2021-01-12");

		contentGuardSeverityList = new ArrayList<ContentGuardSeverity>();

		contentGuardSeverity = new ContentGuardSeverity();
		contentGuardSeverity.setCritical_flag(1);
		contentGuardSeverity.setTotal_changes(20);
		contentGuardSeverityList.add(contentGuardSeverity);

		contentGuardSeverity = new ContentGuardSeverity();
		contentGuardSeverity.setCritical_flag(2);
		contentGuardSeverity.setTotal_changes(18);
		contentGuardSeverityList.add(contentGuardSeverity);

		contentGuardSeverity = new ContentGuardSeverity();
		contentGuardSeverity.setCritical_flag(3);
		contentGuardSeverity.setTotal_changes(2);
		contentGuardSeverityList.add(contentGuardSeverity);

		contentGuardSeverity = new ContentGuardSeverity();
		contentGuardSeverity.setCritical_flag(4);
		contentGuardSeverity.setTotal_changes(9);
		contentGuardSeverityList.add(contentGuardSeverity);

		contentGuardDailyGroupSeverity.setSeverity_list(contentGuardSeverityList.toArray(new ContentGuardSeverity[0]));
		contentGuardDailyGroupSeverityList.add(contentGuardDailyGroupSeverity);

		// 
		contentGuardDailyGroupSeverity = new ContentGuardDailyGroupSeverity();
		contentGuardDailyGroupSeverity.setCrawl_date("2021-01-18");

		contentGuardSeverityList = new ArrayList<ContentGuardSeverity>();

		contentGuardSeverity = new ContentGuardSeverity();
		contentGuardSeverity.setCritical_flag(2);
		contentGuardSeverity.setTotal_changes(3);
		contentGuardSeverityList.add(contentGuardSeverity);

		contentGuardSeverity = new ContentGuardSeverity();
		contentGuardSeverity.setCritical_flag(4);
		contentGuardSeverity.setTotal_changes(18);
		contentGuardSeverityList.add(contentGuardSeverity);

		contentGuardDailyGroupSeverity.setSeverity_list(contentGuardSeverityList.toArray(new ContentGuardSeverity[0]));
		contentGuardDailyGroupSeverityList.add(contentGuardDailyGroupSeverity);

		//
		response.setDaily_group_severity_list(contentGuardDailyGroupSeverityList.toArray(new ContentGuardDailyGroupSeverity[0]));

		String json = new Gson().toJson(response, ContentGuardResourceResponse.class);
		System.out.println(json);

	}

	private void testHourlyGroupTimelineSeverity() {

		ContentGuardResourceResponse response = new ContentGuardResourceResponse();
		response.setSuccess(true);
		response.setGroup_id(3L);

		List<ContentGuardHourlyGroupTimeline> contentGuardHourlyGroupTimelineList = new ArrayList<ContentGuardHourlyGroupTimeline>();

		ContentGuardHourlyGroupTimeline contentGuardHourlyGroupTimeline = null;
		ContentGuardHourlyTimeline contentGuardHourlyTimeline = null;
		List<ContentGuardHourlyTimeline> contentGuardHourlyTimelineList = null;

		// hourly_group_timeline_list
		contentGuardHourlyGroupTimeline = new ContentGuardHourlyGroupTimeline();
		contentGuardHourlyGroupTimeline.setCrawl_date("2021-01-12");

		contentGuardHourlyTimelineList = new ArrayList<ContentGuardHourlyTimeline>();

		contentGuardHourlyTimeline = new ContentGuardHourlyTimeline();
		contentGuardHourlyTimeline.setCrawl_hour(8);
		contentGuardHourlyTimeline.setTotal_changes(45);
		contentGuardHourlyTimelineList.add(contentGuardHourlyTimeline);

		contentGuardHourlyTimeline = new ContentGuardHourlyTimeline();
		contentGuardHourlyTimeline.setCrawl_hour(14);
		contentGuardHourlyTimeline.setTotal_changes(11);
		contentGuardHourlyTimelineList.add(contentGuardHourlyTimeline);
		contentGuardHourlyGroupTimeline.setTimeline_list(contentGuardHourlyTimelineList.toArray(new ContentGuardHourlyTimeline[0]));
		contentGuardHourlyGroupTimelineList.add(contentGuardHourlyGroupTimeline);

		// 
		contentGuardHourlyGroupTimeline = new ContentGuardHourlyGroupTimeline();
		contentGuardHourlyGroupTimeline.setCrawl_date("2021-01-13");

		contentGuardHourlyTimelineList = new ArrayList<ContentGuardHourlyTimeline>();

		contentGuardHourlyTimeline = new ContentGuardHourlyTimeline();
		contentGuardHourlyTimeline.setCrawl_hour(3);
		contentGuardHourlyTimeline.setTotal_changes(39);
		contentGuardHourlyTimelineList.add(contentGuardHourlyTimeline);

		contentGuardHourlyTimeline = new ContentGuardHourlyTimeline();
		contentGuardHourlyTimeline.setCrawl_hour(22);
		contentGuardHourlyTimeline.setTotal_changes(194);
		contentGuardHourlyTimelineList.add(contentGuardHourlyTimeline);
		contentGuardHourlyGroupTimeline.setTimeline_list(contentGuardHourlyTimelineList.toArray(new ContentGuardHourlyTimeline[0]));
		contentGuardHourlyGroupTimelineList.add(contentGuardHourlyGroupTimeline);

		response.setHourly_group_timeline_list(contentGuardHourlyGroupTimelineList.toArray(new ContentGuardHourlyGroupTimeline[0]));

		// hourly_group_severity_list
		ContentGuardHourlyGroupSeverity contentGuardHourlyGroupSeverity = null;
		List<ContentGuardHourlyGroupSeverity> contentGuardHourlyGroupSeverityList = new ArrayList<ContentGuardHourlyGroupSeverity>();
		List<ContentGuardHourlySeverity> contentGuardHourlySeverityList = null;
		ContentGuardHourlySeverity contentGuardHourlySeverity = null;
		ContentGuardSeverity contentGuardSeverity = null;
		List<ContentGuardSeverity> contentGuardSeverityList = null;

		// 
		contentGuardHourlyGroupSeverity = new ContentGuardHourlyGroupSeverity();
		contentGuardHourlyGroupSeverity.setCrawl_date("2021-01-12");

		contentGuardHourlySeverityList = new ArrayList<ContentGuardHourlySeverity>();

		// 
		contentGuardHourlySeverity = new ContentGuardHourlySeverity();
		contentGuardHourlySeverity.setCrawl_hour(8);
		contentGuardSeverityList = new ArrayList<ContentGuardSeverity>();

		contentGuardSeverity = new ContentGuardSeverity();
		contentGuardSeverity.setCritical_flag(1);
		contentGuardSeverity.setTotal_changes(22);
		contentGuardSeverityList.add(contentGuardSeverity);

		contentGuardSeverity = new ContentGuardSeverity();
		contentGuardSeverity.setCritical_flag(2);
		contentGuardSeverity.setTotal_changes(8);
		contentGuardSeverityList.add(contentGuardSeverity);

		contentGuardSeverity = new ContentGuardSeverity();
		contentGuardSeverity.setCritical_flag(3);
		contentGuardSeverity.setTotal_changes(13);
		contentGuardSeverityList.add(contentGuardSeverity);

		contentGuardSeverity = new ContentGuardSeverity();
		contentGuardSeverity.setCritical_flag(4);
		contentGuardSeverity.setTotal_changes(2);
		contentGuardSeverityList.add(contentGuardSeverity);

		contentGuardHourlySeverity.setSeverity_list(contentGuardSeverityList.toArray(new ContentGuardSeverity[0]));
		contentGuardHourlySeverityList.add(contentGuardHourlySeverity);

		// 
		contentGuardHourlySeverity = new ContentGuardHourlySeverity();
		contentGuardHourlySeverity.setCrawl_hour(14);
		contentGuardSeverityList = new ArrayList<ContentGuardSeverity>();

		contentGuardSeverity = new ContentGuardSeverity();
		contentGuardSeverity.setCritical_flag(1);
		contentGuardSeverity.setTotal_changes(3);
		contentGuardSeverityList.add(contentGuardSeverity);

		contentGuardSeverity = new ContentGuardSeverity();
		contentGuardSeverity.setCritical_flag(3);
		contentGuardSeverity.setTotal_changes(8);
		contentGuardSeverityList.add(contentGuardSeverity);

		contentGuardHourlySeverity.setSeverity_list(contentGuardSeverityList.toArray(new ContentGuardSeverity[0]));
		contentGuardHourlySeverityList.add(contentGuardHourlySeverity);

		contentGuardHourlyGroupSeverity.setSeverity_list(contentGuardHourlySeverityList.toArray(new ContentGuardHourlySeverity[0]));
		contentGuardHourlyGroupSeverityList.add(contentGuardHourlyGroupSeverity);

		// 
		contentGuardHourlyGroupSeverity = new ContentGuardHourlyGroupSeverity();
		contentGuardHourlyGroupSeverity.setCrawl_date("2021-01-13");

		contentGuardHourlySeverityList = new ArrayList<ContentGuardHourlySeverity>();

		// 
		contentGuardHourlySeverity = new ContentGuardHourlySeverity();
		contentGuardHourlySeverity.setCrawl_hour(3);
		contentGuardSeverityList = new ArrayList<ContentGuardSeverity>();

		contentGuardSeverity = new ContentGuardSeverity();
		contentGuardSeverity.setCritical_flag(2);
		contentGuardSeverity.setTotal_changes(28);
		contentGuardSeverityList.add(contentGuardSeverity);

		contentGuardSeverity = new ContentGuardSeverity();
		contentGuardSeverity.setCritical_flag(4);
		contentGuardSeverity.setTotal_changes(1);
		contentGuardSeverityList.add(contentGuardSeverity);

		contentGuardHourlySeverity.setSeverity_list(contentGuardSeverityList.toArray(new ContentGuardSeverity[0]));
		contentGuardHourlySeverityList.add(contentGuardHourlySeverity);

		// 
		contentGuardHourlySeverity = new ContentGuardHourlySeverity();
		contentGuardHourlySeverity.setCrawl_hour(22);
		contentGuardSeverityList = new ArrayList<ContentGuardSeverity>();

		contentGuardSeverity = new ContentGuardSeverity();
		contentGuardSeverity.setCritical_flag(1);
		contentGuardSeverity.setTotal_changes(26);
		contentGuardSeverityList.add(contentGuardSeverity);

		contentGuardSeverity = new ContentGuardSeverity();
		contentGuardSeverity.setCritical_flag(3);
		contentGuardSeverity.setTotal_changes(168);
		contentGuardSeverityList.add(contentGuardSeverity);

		contentGuardHourlySeverity.setSeverity_list(contentGuardSeverityList.toArray(new ContentGuardSeverity[0]));
		contentGuardHourlySeverityList.add(contentGuardHourlySeverity);

		contentGuardHourlyGroupSeverity.setSeverity_list(contentGuardHourlySeverityList.toArray(new ContentGuardHourlySeverity[0]));
		contentGuardHourlyGroupSeverityList.add(contentGuardHourlyGroupSeverity);

		//
		response.setHourly_group_severity_list(contentGuardHourlyGroupSeverityList.toArray(new ContentGuardHourlyGroupSeverity[0]));

		String json = new Gson().toJson(response, ContentGuardResourceResponse.class);
		System.out.println(json);

	}

	private void testTimestampMilliseconds() {
		// 1617128344719
		// 1617128200000
		long currentTimestampInMilliseconds = System.currentTimeMillis();
		//System.out.println("currentTimestampInMilliseconds b4="+currentTimestampInMilliseconds);

		currentTimestampInMilliseconds = new Long("1617129245" + "000");
		System.out.println("currentTimestampInMilliseconds=" + currentTimestampInMilliseconds);
		Date testDate = new Date();
		testDate.setTime(currentTimestampInMilliseconds);
		System.out.println("testDate=" + testDate);
	}

	// drop data partitions older than two days old on all cluster servers
	private void testDetachDailyPartitions() throws Exception {
		FormatUtils.getInstance().logMemoryUsage("detachDailyPartitions() begins.");
		Date todayDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
		Date yesterdayDate = DateUtils.addDays(todayDate, -1);
		Date oneDayBeforeYesterdayDate = DateUtils.addDays(yesterdayDate, -1);
		Date sevenDaysBeforeYesterdayDate = DateUtils.addDays(yesterdayDate, -7);
		LocalTargetUrlHtmlDailyClickHouseDAO.getInstance().detachDailyPartitions(sevenDaysBeforeYesterdayDate, oneDayBeforeYesterdayDate);
		FormatUtils.getInstance().logMemoryUsage("detachDailyPartitions() ends.");
	}

	private void testJavascriptCrawlerRequest() {
		JavascriptCrawlerRequest javascriptCrawlerRequest = new JavascriptCrawlerRequest();

		Headers headers = new Headers();
		headers.setUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.88 Safari/537.36");
		javascriptCrawlerRequest.setHeaders(headers);
		javascriptCrawlerRequest.setPageLoadTimeout(5);
		javascriptCrawlerRequest.setWaitAfterLastRequest(new Float(0.5));
		javascriptCrawlerRequest.setFollowRedirects(false);
		//javascriptCrawlerRequest.setUrl("https://www.renodepot.com/en/lighting-and-ceiling-fans/indoor-lighting-333503/pendant-lighting-333526?int_cmp=search-_-trending_pendantlighting-_-hp");
		javascriptCrawlerRequest.setUrl("https://www.1800cpap.com/philips-respironics-cpap-products/supplies/headgear");

		String json = new Gson().toJson(javascriptCrawlerRequest, JavascriptCrawlerRequest.class);
		System.out.println("testJavascriptCrawlerRequest() json=" + json);
	}

	private void testRankCheckUtilsGetRankedKeywordCompetitorUrlListMap() throws Exception {
		String ip = "168";
		OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(6382);
		Date rankDate = DateUtils.addDays(new Date(), -2);
		int topRankedPositions = 5;
		List<String> stringList = null;
		Map<String, List<String>> keywordCompetitorUrlListMap = RankCheckUtils.getRankedKeywordCompetitorUrlListMap(ip, ownDomainEntity, rankDate, topRankedPositions);
		if (keywordCompetitorUrlListMap != null && keywordCompetitorUrlListMap.size() > 0) {
			for (String key : keywordCompetitorUrlListMap.keySet()) {
				stringList = keywordCompetitorUrlListMap.get(key);
				for (String testString : stringList) {
					System.out.println("key=" + key + ",value=" + testString);
				}
			}
		} else {
			System.out.println("keywordCompetitorUrlListMap is empty.");
		}

	}

	private void testCreateContentGuardUsage() {
		ContentGuardUsageEntity contentGuardUsageEntity = null;
		List<ContentGuardUsageEntity> contentGuardUsageEntityList = new ArrayList<ContentGuardUsageEntity>();

		// first record
		contentGuardUsageEntity = new ContentGuardUsageEntity();
		contentGuardUsageEntity.setDomainId(1);
		contentGuardUsageEntity.setUsageDate(20220221);
		contentGuardUsageEntity.setGroupId(2L);
		contentGuardUsageEntity.setTotalUrls(3);
		contentGuardUsageEntityList.add(contentGuardUsageEntity);

		// second record
		contentGuardUsageEntity = new ContentGuardUsageEntity();
		contentGuardUsageEntity.setDomainId(1);
		contentGuardUsageEntity.setUsageDate(20220221);
		contentGuardUsageEntity.setGroupId(3L);
		contentGuardUsageEntity.setTotalUrls(4);
		contentGuardUsageEntityList.add(contentGuardUsageEntity);

		contentGuardUsageDAO.insertMultiRowsBatch(contentGuardUsageEntityList);

	}

	private void testGetContentGuardUsage() {

		int domainId = 0;
		int crawlDate = 0;
		Long groupId = null;
		ContentGuardUsageEntity contentGuardUsageEntity = null;

		// first record
		domainId = 1;
		crawlDate = 20220221;
		groupId = 2L;
		contentGuardUsageEntity = contentGuardUsageDAO.get(domainId, crawlDate, groupId);
		System.out.println("testGetContentGuardUsage() contentGuardUsageEntity1=" + contentGuardUsageEntity.toString());
		assertEquals("testGetContentGuardUsage() contentGuardUsageEntity1.getTotalUrls() incorrect.", "3", String.valueOf(contentGuardUsageEntity.getTotalUrls()));

		// second record
		domainId = 1;
		crawlDate = 20220221;
		groupId = 3L;
		contentGuardUsageEntity = contentGuardUsageDAO.get(domainId, crawlDate, groupId);
		System.out.println("testGetContentGuardUsage() contentGuardUsageEntity2=" + contentGuardUsageEntity.toString());
		assertEquals("testGetContentGuardUsage() contentGuardUsageEntity2.getTotalUrls() incorrect.", "4", String.valueOf(contentGuardUsageEntity.getTotalUrls()));

		System.out.println("testGetContentGuardUsage() passed.");
	}

	private void testUpdateContentGuardUsage() {

		int domainId = 0;
		int crawlDate = 0;
		Long groupId = null;
		int totalUrls = 0;
		ContentGuardUsageEntity contentGuardUsageEntity = null;

		// first record
		domainId = 1;
		crawlDate = 20220221;
		groupId = 2L;
		totalUrls = 18;
		contentGuardUsageDAO.update(domainId, crawlDate, groupId, totalUrls);
		contentGuardUsageEntity = contentGuardUsageDAO.get(domainId, crawlDate, groupId);
		System.out.println("testUpdateContentGuardUsage() contentGuardUsageEntity1=" + contentGuardUsageEntity.toString());
		assertEquals("testUpdateContentGuardUsage() contentGuardUsageEntity1.getTotalUrls() incorrect.", "18", String.valueOf(contentGuardUsageEntity.getTotalUrls()));

		// second record
		domainId = 1;
		crawlDate = 20220221;
		groupId = 3L;
		totalUrls = 168;
		contentGuardUsageDAO.update(domainId, crawlDate, groupId, totalUrls);
		contentGuardUsageEntity = contentGuardUsageDAO.get(domainId, crawlDate, groupId);
		System.out.println("testUpdateContentGuardUsage() contentGuardUsageEntity2=" + contentGuardUsageEntity.toString());
		assertEquals("testUpdateContentGuardUsage() contentGuardUsageEntity2.getTotalUrls() incorrect.", "168", String.valueOf(contentGuardUsageEntity.getTotalUrls()));

		System.out.println("testUpdateContentGuardUsage() passed.");
	}

	private void testFormatDateNumberToString() {
		int testDateNumber = 20220131;
		String testDateString = String.valueOf(testDateNumber);
		String dateString1 = StringUtils.substring(testDateString, 0, 4);
		System.out.println("dateString1=" + dateString1);
		String dateString2 = StringUtils.substring(testDateString, 4, 6);
		System.out.println("dateString2=" + dateString2);
		String dateString3 = StringUtils.substring(testDateString, 6, 8);
		System.out.println("dateString3=" + dateString3);
		String finalDateString = dateString1 + IConstants.DASH + dateString2 + IConstants.DASH + dateString3;
		System.out.println("finalDateString=" + finalDateString);
	}

	private void testParseCustomData() {
		String json = "[{\"content\":[\"true\"],\"index\":1,\"links\":[],\"selector\":\"pfs-is-logged-in\",\"selector_type\":\"DIV_ID\",\"word_count\":4,\"target_url_crawl_additional_content_id\":157},{\"content\":[\"Facts and Features\",\"Past 12 months\",\"Zillow predicts 85023 home values will rise 6.3% next year, compared to a 7.1% increase for Phoenix as a whole. Among 85023 homes, this home is valued 36.6% more than the midpoint (median) home, but is valued 3.8% less per square foot.\",\"$375,395-- bds2.67 ba1,934 sqft\",\"728 W Thunderbird Rd, Phoenix, AZ 85023\",\"$434,9004 bds3 ba2,709 sqft\",\"712 W Thunderbird Rd, Phoenix, AZ 85023\",\"$352,9583 bds2 ba1,989 sqft\",\"702 W Thunderbird Rd, Phoenix, AZ 85023\",\"$365,0004 bds2 ba2,256 sqft\",\"13237 N 8th Ave, Phoenix, AZ 85029\",\"$370,0004 bds3.5 ba3,553 sqft\",\"736 W Thunderbird Rd, Phoenix, AZ 85023\",\"$310,0004 bds2.5 ba2,616 sqft\",\"13238 N 7th Dr, Phoenix, AZ 85029\",\"$380,0004 bds3 ba2,355 sqft\",\"13238 N 8th Ave, Phoenix, AZ 85029\",\"$362,0884 bds2 ba2,025 sqft\",\"552 W Thunderbird Rd, Phoenix, AZ 85023\",\"$449,0004 bds3 ba2,504 sqft\",\"13229 N 8th Ave, Phoenix, AZ 85029\",\"$471,0455 bds3 ba2,816 sqft\",\"13230 N 7th Dr, Phoenix, AZ 85029\",\"$358,3133 bds2 ba1,853 sqft\",\"13237 N 7th Dr, Phoenix, AZ 85029\",\"$367,878-- bds2 ba2,156 sqft\",\"802 W Pershing Ave, Phoenix, AZ 85029\",\"$275,0004 bds2.5 ba2,353 sqft\",\"813 W Thunderbird Rd, Phoenix, AZ 85023\",\"$395,0004 bds2 ba2,347 sqft\",\"13401 N Coral Gables Dr, Phoenix, AZ 85023\",\"$479,0003 bds3 ba3,100 sqft\",\"13411 N Coral Gables Dr, Phoenix, AZ 85023\",\"Data by GreatSchools.org\",\"About the ratings: GreatSchools ratings are designed to be a starting point to help parents compare schools, and should not be the only factor used in selecting the right school for your family. Zillow recommends that parents tour multiple schools in-person to inform that choice. Historically, GreatSchools ratings have been based solely on a comparison of standardized test results for all schools in a given state. As of September 2017, GreatSchools ratings also incorporate additional information, when available, such as college readiness, academic progress, advanced courses, equity, discipline and attendance data. Learn more\",\"Disclaimer: School attendance zone boundaries are provided by a third party and subject to change. Check with the applicable school district prior to making a decision based on these boundaries. In addition, school data is obtained from a third party vendor and not guaranteed to be accurate, up to date or complete.\",\"$449,0004 bds3 ba2,504 sqft\",\"13229 N 8th Ave, Phoenix, AZ 85029\",\"$470,0004 bds3 ba2,546 sqft\",\"512 W Southern Hills Rd, Phoenix, AZ 85023\",\"$329,0004 bds3 ba2,035 sqft\",\"1515 W Andorra Dr, Phoenix, AZ 85029\",\"$530,0004 bds2 ba2,846 sqft\",\"1211 W Dahlia Dr, Phoenix, AZ 85029\",\"$410,0003 bds2 ba1,836 sqft\",\"12804 N 16th Ave, Phoenix, AZ 85029\",\"$265,0004 bds2 ba1,709 sqft\",\"1458 W Wood Dr, Phoenix, AZ 85029\",\"$430,0004 bds2 ba1,989 sqft\",\"108 E Voltaire Ave, Phoenix, AZ 85022\",\"$425,0003 bds1.75 ba1,846 sqft\",\"210 W Winged Foot Rd, Phoenix, AZ 85023\"],\"index\":2,\"links\":[],\"selector\":\"p\",\"selector_type\":\"CSS\",\"word_count\":2721,\"target_url_crawl_additional_content_id\":158}]";
		CustomData[] customDataArray = new Gson().fromJson(json, CustomData[].class);
		for (CustomData customData : customDataArray) {
			System.out.println("customData=" + customData.toString());
		}
	}

	private void testTargetUrlCrawlAdditionalContentUrlSelector() throws Exception {

		boolean result = false;
		result = Pattern.matches("^https://www.spokeo.com/[A-Z]([a-z]+)?-([A-Za-z ]+)(/[0-9]{1,2})?$", "https://www.spokeo.com/Steven-Mitchell/Nebraska");
		System.out.println("results1=" + result);
		result = Pattern.matches("/([A-Z]+[a-z]+)-([A-Za-z ]+)/[A-Z][A-Z a-z \\\\-\\\\+\\\\s]+(/[0-9]+)?$", "https://www.spokeo.com/Steven-Mitchell/Nebraska");
		System.out.println("results2=" + result);
		result = Pattern.matches("/([A-Z]+[a-z]+)-([A-Za-z ]+)/[A-Z][A-Z a-z \\\\-\\\\+\\\\s]+/[A-Z][A-Z a-z \\\\-\\\\+\\\\s_\\\\.]+",
				"https://www.spokeo.com/Steven-Mitchell/Nebraska");
		System.out.println("results3=" + result);
		result = Pattern.matches("https://www.spokeo.com/(1-)?[0-9]{3}-[0-9]{3}-[0-9]{4}", "https://www.spokeo.com/Steven-Mitchell/Nebraska");
		System.out.println("results4=" + result);
		result = Pattern.matches("/.*addresses", "https://www.spokeo.com/Steven-Mitchell/Nebraska");
		System.out.println("results5=" + result);
		result = Pattern.matches("/en/road-trips/", "https://www.enterprise.com/en/road-trips/destinations/illinois.html");
		System.out.println("results6=" + result);

		//String selector = null;
		//		String urlString = "https://www.spokeo.com/Steven-Mitchell/Nebraska";
		//		int domainId = 8422;
		//		List<AdditionalContentEntity> additionalContentEntityList = targetUrlCrawlAdditionalContentEntityDAO.getByDomainId(domainId);
		//		boolean isRequiredJavaUrlEncoder = false;
		//		boolean isUrlSelected = false;
		//		String trimmedUrlString = StringUtils.trimToEmpty(urlString);
		//		DecodedEncodedUrlValueObject decodedEncodedUrlValueObject = CrawlerUtils.getInstance().getDecodedAndEncodedUrlString(trimmedUrlString,
		//				isRequiredJavaUrlEncoder);
		//		if (decodedEncodedUrlValueObject != null) {
		//			for (AdditionalContentEntity additionalContentEntity : additionalContentEntityList) {
		//				isUrlSelected = CrawlerUtils.getInstance().checkIfUrlSelected(decodedEncodedUrlValueObject.getEncodedUrlString(), additionalContentEntity);
		//				System.out.println("additionalContentEntity="+additionalContentEntity.toString()+",isUrlSelected="+isUrlSelected);
		//			}
		//		}
	}

	private void testGetInvalidCompetitorUrlCount() {
		int domainId = 0;
		Integer invalidCompetitorUrlCount = null;
		List<OwnDomainEntity> ownDomainEntityList = ownDomainEntityDAO.queryForAll();
		for (OwnDomainEntity ownDomainEntity : ownDomainEntityList) {
			domainId = ownDomainEntity.getId();
			invalidCompetitorUrlCount = competitorUrlMd5EntityDAO.getInvalidCompetitorUrlCount(domainId);
			System.out.println("domainId=" + domainId + ",invalidCompetitorUrlCount=" + invalidCompetitorUrlCount);
		}
		//domainId = 123;
		//invalidCompetitorUrlCount = competitorUrlMd5EntityDAO.getInvalidCompetitorUrlCount(domainId);
		//System.out.println("domainId=" + domainId + ",invalidCompetitorUrlCount=" + invalidCompetitorUrlCount);
	}

	private void testStringArrayLength() {
		int total = 1;
		System.out.println("total=" + total);
		String[] previousStringArray = new String[total];
		System.out.println("previousStringArray.length=" + previousStringArray.length);
	}

	private void testLastPageStartRow() {
		int totalRows = 20;
		System.out.println("totalRows=" + totalRows);
		int rowsPerPage = 21;
		System.out.println("rowsPerPage=" + rowsPerPage);
		int lastPageNumber = 0;
		int initialPageNumber = totalRows / rowsPerPage;
		System.out.println("initialPageNumber=" + initialPageNumber);
		int modulus = totalRows % rowsPerPage;
		System.out.println("modulus=" + modulus);
		if (modulus > 0) {
			lastPageNumber = initialPageNumber + 1;
		} else {
			lastPageNumber = initialPageNumber;
		}
		System.out.println("lastPageNumber=" + lastPageNumber);
	}

	private void testSplitString() {
		LogValueObject logValueObject = null;
		//String inputString = "1456:crawlHtml() final results. ip=125,queueName=TARGET_URL_HTML_EN_256,url=https://www.seoclarity.net/blog/how-to-build-backlinks-without-content,httpStatusCode=200,message elapsed (sec.)=9,memory(free/max/total)=6,713,693,360 / 82,022,236,160 / 18,162,384,896 at Fri Jun 17 03:08:57 CDT 2022";
		String inputString = "ip`12`queueName`TEST_COMPETITOR_URL_HTML_6`url`https://www2.hm.com/us/product/16004?article=16004-E`httpStatusCode`404`elapsed`3`memory(free/max/total)=1,255,550,128 / 7,456,948,224 / 1,448,083,456 at Fri Jun 24 13:23:51 CDT 2022";
		String[] stringArray = inputString.split(IConstants.BACKTICK);
		logValueObject = new LogValueObject();
		for (int i = 0; i < stringArray.length; i++) {
			//System.out.println("i=" + i + ",value=" + stringArray[i]);
			if (i == 3) {
				System.out.println("i=" + i + ",queueName=" + stringArray[i]);
				//System.out.println("i=" + i + ",domainID=" + StringUtils.removeStart(stringArray[i], "queueName=TARGET_URL_HTML_EN_"));
				//logValueObject.setDomainId(NumberUtils.toInt(StringUtils.removeStart(stringArray[i], "queueName=TARGET_URL_HTML_EN_")));
			} else if (i == 5) {
				System.out.println("i=" + i + ",url=" + stringArray[i]);
				//logValueObject.setUrl(StringUtils.removeStart(stringArray[i], "url="));
			} else if (i == 7) {
				System.out.println("i=" + i + ",responseCode=" + stringArray[i]);
				//logValueObject.setResponseCode(NumberUtils.toInt(StringUtils.removeStart(stringArray[i], "httpStatusCode=")));
			} else if (i == 9) {
				System.out.println("i=" + i + ",elapsed=" + stringArray[i]);
				//logValueObject.setElapsedInSeconds(NumberUtils.toInt(StringUtils.removeStart(stringArray[i], "message elapsed (sec.)=")));
			}
		}
		System.out.println("logValueObject=" + logValueObject.toString());
	}

	private void testCrawlDateHourSummary() throws Exception {
		Map<String, Map<String, Integer>> crawlDateHourTotalMapMap = new HashMap<String, Map<String, Integer>>();
		Map<String, Integer> crawlHourTotalMap = null;
		String fileLocationPath = "C:\\home\\actonia\\source\\test_polite_crawl_put_messages\\data\\javascript_crawl_requests.txt";
		File file = new File(fileLocationPath);
		int total = 0;
		String[] stringArray = null;
		String crawlDate = null;
		String crawlHour = null;
		List<String> stringList = FileUtils.readLines(file, IConstants.UTF_8);
		if (stringList != null && stringList.size() > 0) {
			for (String testString1 : stringList) {
				stringArray = testString1.split(IConstants.COMMA);
				crawlDate = stringArray[0];
				crawlHour = stringArray[1];
				if (crawlDateHourTotalMapMap.containsKey(crawlDate)) {
					crawlHourTotalMap = crawlDateHourTotalMapMap.get(crawlDate);
				} else {
					crawlHourTotalMap = new HashMap<String, Integer>();
				}
				if (crawlHourTotalMap.containsKey(crawlHour)) {
					total = crawlHourTotalMap.get(crawlHour);
				} else {
					total = 0;
				}
				total = total + 1;
				crawlHourTotalMap.put(crawlHour, total);
				crawlDateHourTotalMapMap.put(crawlDate, crawlHourTotalMap);
			}
		}

		for (String testCrawlDate : crawlDateHourTotalMapMap.keySet()) {
			crawlHourTotalMap = crawlDateHourTotalMapMap.get(testCrawlDate);
			for (String testCrawlHour : crawlHourTotalMap.keySet()) {
				total = crawlHourTotalMap.get(testCrawlHour);
				System.out.println("crawlDate`" + testCrawlDate + "`crawlHour`" + testCrawlHour + "`total`" + total);
			}
		}

	}

	private void testGetRankedTargetUrlEntityList() throws Exception {
		String ip = "1";
		int domainId = 0;
		String domainName = null;
		long startTimestamp = 0L;
		List<String> rankedUrlList = null;
		int subDomainTotals = 0;
		int rootDomainTotals = 0;
		List<OwnDomainEntity> ownDomainEntityList = ownDomainEntityDAO.queryForAll();
		if (ownDomainEntityList != null && ownDomainEntityList.size() > 0) {
			nextOwnDomainEntity: for (OwnDomainEntity ownDomainEntity : ownDomainEntityList) {
				domainId = ownDomainEntity.getId();
				domainName = ownDomainEntity.getDomain();
				startTimestamp = System.currentTimeMillis();
				List<TargetUrlEntity> targetUrlEntityList = new ArrayList<TargetUrlEntity>();
				List<TargetUrlEntity> rankedTargetUrlEntityList = new ArrayList<TargetUrlEntity>();
				TargetUrlEntity targetUrlEntity = null;
				int totalRankedUrls = 0;
				int totalSkippedNotSubFolder = 0;
				int totalRankedUrlsAlreadyTargetUrl = 0;
				int totalRankedUrlsToBeCrawled = 0;
				Set<String> existingTargetUrlHashCodeSet = getTargetUrlHashCodeSet(targetUrlEntityList);
				Set<String> rankedTargetUrlHashCodeSet = new HashSet<String>();
				String rankedUrlHashCode = null;

				// the rank date for processing is always two days ago
				Date todayDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
				Date rankDate = DateUtils.addDays(todayDate, -2);

				int searchEngineId = ScKeywordRankService.getSearchEngineId(ownDomainEntity);
				if (searchEngineId == ScKeywordRankService.ENGINE_LANGUAGE_ID_NOT_FOUND) {
					FormatUtils.getInstance().logMemoryUsage(
							"testGetRankedTargetUrlEntityList() error--search engine ID not found. ip=" + ip + ", domainId=" + domainId + ", domainName=" + domainName);
					continue nextOwnDomainEntity;
				}

				int searchLanguageId = ScKeywordRankService.getSearchLanguageId(ownDomainEntity);
				if (searchLanguageId == ScKeywordRankService.ENGINE_LANGUAGE_ID_NOT_FOUND) {
					FormatUtils.getInstance().logMemoryUsage("testGetRankedTargetUrlEntityList() error--search language ID not found. ip=" + ip + ", domainId="
							+ domainId + ", domainName=" + domainName);
					continue nextOwnDomainEntity;
				}

				// determine the corrected sub-folder of the client domain
				String subFolder = ownDomainEntity.getSubFolder();
				String correctedSubFolder = null;
				if (StringUtils.isNotBlank(subFolder)) {
					correctedSubFolder = getCorrectedSubFolder(subFolder, domainName);
				}

				String rootDomainReverse = FormatUtils.getInstance().getReversedRootDomainName(StringUtils.trim(domainName));
				String domainReverse = FormatUtils.getInstance().getReversedDomainName(StringUtils.trim(domainName));
				rankedUrlList = RankingDetailClickHouseDAO.getInstance().getUrlList(ip, domainId, searchEngineId, searchLanguageId, rankDate, rootDomainReverse,
						domainReverse);
				if (rankedUrlList != null && rankedUrlList.size() > 0) {
					subDomainTotals = rankedUrlList.size();
				} else {
					subDomainTotals = 0;
				}
				String testDomainReverse = rootDomainReverse;
				rankedUrlList = RankingDetailClickHouseDAO.getInstance().getUrlList(ip, domainId, searchEngineId, searchLanguageId, rankDate, rootDomainReverse,
						testDomainReverse);
				if (rankedUrlList != null && rankedUrlList.size() > 0) {
					rootDomainTotals = rankedUrlList.size();
				} else {
					rootDomainTotals = 0;
				}
				System.out.println("domainId`" + domainId + "`domainName`" + domainName + "`rankDate`" + rankDate + "`searchEngineId`" + searchEngineId
						+ "`searchLanguageId`" + searchLanguageId + "`correctedSubFolder`" + correctedSubFolder + "`rootDomainReverse`" + rootDomainReverse
						+ "`domainReverse`" + domainReverse + "`subDomainTotals`" + subDomainTotals + "`rootDomainTotals`" + rootDomainTotals);
			}
		}
	}

	private String getCorrectedSubFolder(String subFolder, String domainName) {
		String correctedSubFolder = StringUtils.lowerCase(subFolder);
		if (StringUtils.startsWith(subFolder, IConstants.SINGLE_FORWARD_SLASHES)) {
			correctedSubFolder = domainName + StringUtils.lowerCase(subFolder);
		}
		return correctedSubFolder;
	}

	private Set<String> getTargetUrlHashCodeSet(List<TargetUrlEntity> targetUrlEntityList) {
		Set<String> hashCodeSet = new HashSet<String>();
		if (targetUrlEntityList != null && targetUrlEntityList.size() > 0) {
			for (TargetUrlEntity targetUrlEntity : targetUrlEntityList) {
				hashCodeSet.add(Md5Util.Md5(StringUtils.trim(targetUrlEntity.getUrl())));
			}
		}
		return hashCodeSet;
	}

	private void testConvertTimestamp() {
		String inputString = "2022-06-18 07:08:09";
		System.out.println("inputString=" + inputString);
		String outputString = StringUtils.substring(inputString, 0, 14) + "00:00";
		System.out.println("outputString=" + outputString);
	}

	private void testMaxUrlHash() {
		String inputString = "18446743603235063094";
		//String inputString = "18446743603235063094";
		System.out.println("inputString=" + inputString);
		BigInteger bigInteger1 = new BigInteger(inputString);
		System.out.println("bigInteger1=" + bigInteger1);

		UrlFilter urlFilter = null;

		List<UrlFilter> urlFilterList = new ArrayList<UrlFilter>();

		urlFilter = new UrlFilter();
		urlFilter.setAction("ct");
		urlFilter.setValue("test");
		urlFilterList.add(urlFilter);

		urlFilter = new UrlFilter();
		urlFilter.setAction("eq");
		urlFilter.setValue("https://www.seoclarity.net");
		urlFilterList.add(urlFilter);

		UrlFilter[] UrlFilters = urlFilterList.toArray(new UrlFilter[0]);
		String json = new Gson().toJson(UrlFilters, UrlFilter[].class);
		System.out.println("json=" + json);

		//-------------------

		ResponseCodeFilter responseCodeFilter = null;
		List<ResponseCodeFilter> responseCodeFilterList = new ArrayList<ResponseCodeFilter>();

		responseCodeFilter = new ResponseCodeFilter();
		responseCodeFilter.setResponse_code_previous("200");
		responseCodeFilter.setResponse_code_current("302");
		responseCodeFilterList.add(responseCodeFilter);

		responseCodeFilter = new ResponseCodeFilter();
		responseCodeFilter.setResponse_code_previous("404");
		responseCodeFilter.setResponse_code_current("200");
		responseCodeFilterList.add(responseCodeFilter);

		ResponseCodeFilter[] responseCodeFilters = responseCodeFilterList.toArray(new ResponseCodeFilter[0]);

		String responseCodeFilterJson = new Gson().toJson(responseCodeFilters, ResponseCodeFilter[].class);
		System.out.println("responseCodeFilterJson=" + responseCodeFilterJson);

	}

	private void testDateFormat() {
		String outputTimestamp = DateFormatUtils.format(new Date(), IConstants.DATE_FORMAT_YYYYMMDD_HHMMSS_SSS);
		System.out.println("outputTimestamp=" + outputTimestamp);

		String currentTimestamp = "2022-07-20 11:01:56";
		System.out.println("currentTimestamp=" + currentTimestamp);
		String dateString = StringUtils.substring(currentTimestamp, 0, 10);
		System.out.println("dateString=" + dateString);
	}
};