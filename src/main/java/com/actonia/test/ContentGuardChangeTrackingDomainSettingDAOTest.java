package com.actonia.test;

import java.util.List;

import com.actonia.dao.ContentGuardChangeTrackingDomainSettingDAO;
import com.actonia.entity.ContentGuardChangeTrackingDomainSettingEntity;
import com.actonia.utils.SpringBeanFactory;

public class ContentGuardChangeTrackingDomainSettingDAOTest {

	private ContentGuardChangeTrackingDomainSettingDAO contentGuardChangeTrackingDomainSettingDAO;

	public ContentGuardChangeTrackingDomainSettingDAOTest() {
		super();
		this.contentGuardChangeTrackingDomainSettingDAO = SpringBeanFactory.getBean("contentGuardChangeTrackingDomainSettingDAO");
	}

	public static void main(String[] args) throws Exception {
		new ContentGuardChangeTrackingDomainSettingDAOTest().runTests();
	}

	private void runTests() throws Exception {
		testGetList();
	}

	private void testGetList() throws Exception {
		int domainId = 5739;
		List<ContentGuardChangeTrackingDomainSettingEntity> contentGuardChangeTrackingDomainSettingEntityList = contentGuardChangeTrackingDomainSettingDAO
				.getList(domainId);
		if (contentGuardChangeTrackingDomainSettingEntityList != null && contentGuardChangeTrackingDomainSettingEntityList.size() > 0) {
			for (ContentGuardChangeTrackingDomainSettingEntity contentGuardChangeTrackingDomainSettingEntity : contentGuardChangeTrackingDomainSettingEntityList) {
				System.out.println("testGetList() indicator=" + contentGuardChangeTrackingDomainSettingEntity.getIndicator());
			}
		} else {
			System.out.println("testGetList() contentGuardChangeTrackingDomainSettingEntityList is empty.");
		}
	}
}
