package com.actonia.test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.fail;

import com.actonia.IConstants;
import com.actonia.dao.ZapierWebhookDAO;
import com.actonia.entity.ZapierWebhookEntity;
import com.actonia.exception.ZapierException;
import com.actonia.service.AccessTokenService;
import com.actonia.service.MainZapierWebServiceClientService;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.ZapierContentGuardAlert;
import com.actonia.value.object.ZapierResourceRequest;
import com.actonia.value.object.ZapierResourceResponse;
import com.google.gson.Gson;

public class MainZapierContentGuardAlertResourceTest {
	private boolean isUnitTest = true;
	private MainZapierWebServiceClientService webServiceClientService;
	private ZapierWebhookDAO zapierWebhookDAO;

	public MainZapierContentGuardAlertResourceTest() {
		super();
		this.webServiceClientService = SpringBeanFactory.getBean("mainZapierWebServiceClientService");
		this.zapierWebhookDAO = SpringBeanFactory.getBean("zapierWebhookDAO");
	}

	public static void main(String[] args) throws Exception {
		new MainZapierContentGuardAlertResourceTest().runTests();
	}

	public void runTests() throws Exception {
		testCase1();
		testCase2();
		if (isUnitTest == true) {
			testCase3();
		}
		testCase4();
		testCase5();
		testCase8();
		testCase9();
		testCase12();
		testCase13();
		testCase14();
		testCase15();
		testCase16();
		testCase17();
		testCase18();
		testCase19();
		testCase20();
		testCase21();
		testCase22();
		testCase23();
		testCase24();
		testCase25();
		testCase26();
		testCase27();
		testCase28();
	}

	private void testCase1() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_CONTENT_GUARD_ALERT;
		System.out.println("testCase1() requestUrl=" + requestUrl);
		String requestParameters = IConstants.EMPTY_STRING;
		ZapierException zapierException = null;
		try {
			webServiceClientService.sendHttpPostRequest(requestUrl, requestParameters);
			fail("testCase1() should throw Exception.");
		} catch (Exception e) {
			if (e instanceof ZapierException == false) {
				fail("testCase1() should throw ZapierException.");
			} else {
				zapierException = (ZapierException) e;
				assertEquals("testCase1() zapierException.getHttpStatusCode() is incorrect.", "801", String.valueOf(zapierException.getHttpStatusCode()));
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase1() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase2() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_CONTENT_GUARD_ALERT;
		System.out.println("testCase2() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierResourceRequest = new ZapierResourceRequest();
		String requestParameters = new Gson().toJson(zapierResourceRequest, ZapierResourceRequest.class);
		ZapierException zapierException = null;
		try {
			webServiceClientService.sendHttpPostRequest(requestUrl, requestParameters);
			fail("testCase2() should throw Exception.");
		} catch (Exception e) {
			if (e instanceof ZapierException == false) {
				fail("testCase2() should throw ZapierException.");
			} else {
				zapierException = (ZapierException) e;
				assertEquals("testCase2() zapierException.getHttpStatusCode() is incorrect.", "802", String.valueOf(zapierException.getHttpStatusCode()));
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase2() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase3() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_CONTENT_GUARD_ALERT;
		System.out.println("testCase3() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierResourceRequest = new ZapierResourceRequest();
		zapierResourceRequest.setAccess_token("testaccesstoken");
		String requestParameters = new Gson().toJson(zapierResourceRequest, ZapierResourceRequest.class);
		ZapierException zapierException = null;
		try {
			webServiceClientService.sendHttpPostRequest(requestUrl, requestParameters);
			fail("testCase3() should throw Exception.");
		} catch (Exception e) {
			if (e instanceof ZapierException == false) {
				fail("testCase3() should throw ZapierException.");
			} else {
				zapierException = (ZapierException) e;
				assertEquals("testCase3() zapierException.getHttpStatusCode() is incorrect.", "803", String.valueOf(zapierException.getHttpStatusCode()));
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase3() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase4() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_CONTENT_GUARD_ALERT;
		System.out.println("testCase4() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierResourceRequest = new ZapierResourceRequest();
		zapierResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		String requestParameters = new Gson().toJson(zapierResourceRequest, ZapierResourceRequest.class);
		ZapierException zapierException = null;
		try {
			webServiceClientService.sendHttpPostRequest(requestUrl, requestParameters);
			fail("testCase4() should throw Exception.");
		} catch (Exception e) {
			if (e instanceof ZapierException == false) {
				fail("testCase4() should throw ZapierException.");
			} else {
				zapierException = (ZapierException) e;
				assertEquals("testCase4() zapierException.getHttpStatusCode() is incorrect.", "804", String.valueOf(zapierException.getHttpStatusCode()));
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase4() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase5() {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_CONTENT_GUARD_ALERT;
		System.out.println("testCase5() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierResourceRequest = new ZapierResourceRequest();
		zapierResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		zapierResourceRequest.setDomain_id(1);
		String requestParameters = new Gson().toJson(zapierResourceRequest, ZapierResourceRequest.class);
		ZapierException zapierException = null;
		try {
			webServiceClientService.sendHttpPostRequest(requestUrl, requestParameters);
			fail("testCase5() should throw Exception.");
		} catch (Exception e) {
			if (e instanceof ZapierException == false) {
				fail("testCase5() should throw ZapierException.");
			} else {
				zapierException = (ZapierException) e;
				assertEquals("testCase5() zapierException.getHttpStatusCode() is incorrect.", "805", String.valueOf(zapierException.getHttpStatusCode()));
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase5() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase8() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_CONTENT_GUARD_ALERT;
		System.out.println("testCase8() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierResourceRequest = new ZapierResourceRequest();
		zapierResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		zapierResourceRequest.setDomain_id(1701);
		zapierResourceRequest.setGroup_name("Daily Test Group 1");
		String requestParameters = new Gson().toJson(zapierResourceRequest, ZapierResourceRequest.class);
		ZapierException zapierException = null;
		try {
			webServiceClientService.sendHttpPostRequest(requestUrl, requestParameters);
			fail("testCase8() should throw Exception.");
		} catch (Exception e) {
			if (e instanceof ZapierException == false) {
				fail("testCase8() should throw ZapierException.");
			} else {
				zapierException = (ZapierException) e;
				assertEquals("testCase8() zapierException.getHttpStatusCode() is incorrect.", "806", String.valueOf(zapierException.getHttpStatusCode()));
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase8() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase9() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_CONTENT_GUARD_ALERT;
		System.out.println("testCase9() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierResourceRequest = new ZapierResourceRequest();
		zapierResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		zapierResourceRequest.setDomain_id(1701);
		zapierResourceRequest.setGroup_name("Daily Test Group 1");
		zapierResourceRequest.setUser_email("<EMAIL>");
		String requestParameters = new Gson().toJson(zapierResourceRequest, ZapierResourceRequest.class);
		ZapierException zapierException = null;
		try {
			webServiceClientService.sendHttpPostRequest(requestUrl, requestParameters);
			fail("testCase9() should throw Exception.");
		} catch (Exception e) {
			if (e instanceof ZapierException == false) {
				fail("testCase9() should throw ZapierException.");
			} else {
				zapierException = (ZapierException) e;
				assertEquals("testCase9() zapierException.getHttpStatusCode() is incorrect.", "807", String.valueOf(zapierException.getHttpStatusCode()));
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase9() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase12() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_CONTENT_GUARD_ALERT;
		System.out.println("testCase12() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierResourceRequest = new ZapierResourceRequest();
		zapierResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		zapierResourceRequest.setDomain_id(1701);
		zapierResourceRequest.setGroup_name("Daily Test Group 1");
		zapierResourceRequest.setUser_email("<EMAIL>");
		String requestParameters = new Gson().toJson(zapierResourceRequest, ZapierResourceRequest.class);
		ZapierException zapierException = null;
		try {
			webServiceClientService.sendHttpPostRequest(requestUrl, requestParameters);
			fail("testCase12() should throw Exception.");
		} catch (Exception e) {
			if (e instanceof ZapierException == false) {
				fail("testCase12() should throw ZapierException.");
			} else {
				zapierException = (ZapierException) e;
				assertEquals("testCase12() zapierException.getHttpStatusCode() is incorrect.", "808", String.valueOf(zapierException.getHttpStatusCode()));
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase12() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase13() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_CONTENT_GUARD_ALERT;
		System.out.println("testCase13() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierResourceRequest = new ZapierResourceRequest();
		zapierResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		zapierResourceRequest.setDomain_id(1701);
		zapierResourceRequest.setGroup_name("Daily Test Group 1");
		zapierResourceRequest.setUser_email("<EMAIL>");
		zapierResourceRequest.setCallback_url("test");
		String requestParameters = new Gson().toJson(zapierResourceRequest, ZapierResourceRequest.class);
		ZapierException zapierException = null;
		try {
			webServiceClientService.sendHttpPostRequest(requestUrl, requestParameters);
			fail("testCase13() should throw Exception.");
		} catch (Exception e) {
			if (e instanceof ZapierException == false) {
				fail("testCase13() should throw ZapierException.");
			} else {
				zapierException = (ZapierException) e;
				assertEquals("testCase13() zapierException.getHttpStatusCode() is incorrect.", "809", String.valueOf(zapierException.getHttpStatusCode()));
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase13() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase14() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		ZapierWebhookEntity zapierWebhookEntity = null;
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_CONTENT_GUARD_ALERT;
		System.out.println("testCase14() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierResourceRequest = new ZapierResourceRequest();
		zapierResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		zapierResourceRequest.setDomain_id(1701);
		zapierResourceRequest.setGroup_name("Daily Test Group 1");
		zapierResourceRequest.setUser_email("<EMAIL>");
		zapierResourceRequest.setCallback_url("https://hook.zapier.com/callback_url_1");
		String requestParameters = new Gson().toJson(zapierResourceRequest, ZapierResourceRequest.class);

		ZapierResourceResponse zapierResourceResponse = webServiceClientService.sendHttpPostRequest(requestUrl, requestParameters);
		assertNotNull("testCase14() zapierResourceResponse should not be null.", zapierResourceResponse);
		assertNotNull("testCase14() zapierResourceResponse.getId() should not be null.", zapierResourceResponse.getId());
		if (isUnitTest == true) {
			zapierWebhookEntity = zapierWebhookDAO.get(zapierResourceResponse.getId());
			assertNotNull("testCase14() zapierWebhookEntity should not be null.", zapierWebhookEntity);
			assertEquals("testCase14() zapierWebhookEntity.getDomainId() is incorrect.", "1701", String.valueOf(zapierWebhookEntity.getDomainId()));
			assertEquals("testCase14() zapierWebhookEntity.getUserId() is incorrect.", "214", String.valueOf(zapierWebhookEntity.getUserId()));
			assertEquals("testCase14() zapierWebhookEntity.getSubType() is incorrect.", "69", zapierWebhookEntity.getSubType());
			assertEquals("testCase14() zapierWebhookEntity.getTriggerType() is incorrect.", "1", String.valueOf(zapierWebhookEntity.getTriggerType()));
			assertEquals("testCase14() zapierWebhookEntity.getCallbackUrl() is incorrect.", "https://hook.zapier.com/callback_url_1",
					String.valueOf(zapierWebhookEntity.getCallbackUrl()));
			zapierWebhookDAO.delete(zapierResourceResponse.getId());
		}
		FormatUtils.getInstance().logMemoryUsage("testCase14() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase15() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_CONTENT_GUARD_ALERT;
		System.out.println("testCase15() requestUrl=" + requestUrl);
		ZapierException zapierException = null;
		try {
			webServiceClientService.sendHttpDeleteRequest(requestUrl);
			fail("testCase15() should throw Exception.");
		} catch (Exception e) {
			if (e instanceof ZapierException == false) {
				fail("testCase15() should throw ZapierException.");
			} else {
				zapierException = (ZapierException) e;
				assertEquals("testCase15() zapierException.getHttpStatusCode() is incorrect.", "802", String.valueOf(zapierException.getHttpStatusCode()));
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase15() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase16() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_CONTENT_GUARD_ALERT + "?access_token=testaccesstoken";
		System.out.println("testCase16() requestUrl=" + requestUrl);
		ZapierException zapierException = null;
		try {
			webServiceClientService.sendHttpDeleteRequest(requestUrl);
			fail("testCase16() should throw Exception.");
		} catch (Exception e) {
			if (e instanceof ZapierException == false) {
				fail("testCase16() should throw ZapierException.");
			} else {
				zapierException = (ZapierException) e;
				assertEquals("testCase16() zapierException.getHttpStatusCode() is incorrect.", "803", String.valueOf(zapierException.getHttpStatusCode()));
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase16() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase17() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_CONTENT_GUARD_ALERT + "?access_token=" + AccessTokenService.INTERNAL_KEY;
		System.out.println("testCase17() requestUrl=" + requestUrl);
		ZapierException zapierException = null;
		try {
			webServiceClientService.sendHttpDeleteRequest(requestUrl);
			fail("testCase17() should throw Exception.");
		} catch (Exception e) {
			if (e instanceof ZapierException == false) {
				fail("testCase17() should throw ZapierException.");
			} else {
				zapierException = (ZapierException) e;
				assertEquals("testCase17() zapierException.getHttpStatusCode() is incorrect.", "812", String.valueOf(zapierException.getHttpStatusCode()));
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase17() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase18() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_CONTENT_GUARD_ALERT + "?access_token=" + AccessTokenService.INTERNAL_KEY + "&id=abcd";
		System.out.println("testCase18() requestUrl=" + requestUrl);
		ZapierException zapierException = null;
		try {
			webServiceClientService.sendHttpDeleteRequest(requestUrl);
			fail("testCase18() should throw Exception.");
		} catch (Exception e) {
			if (e instanceof ZapierException == false) {
				fail("testCase18() should throw ZapierException.");
			} else {
				zapierException = (ZapierException) e;
				assertEquals("testCase18() zapierException.getHttpStatusCode() is incorrect.", "813", String.valueOf(zapierException.getHttpStatusCode()));
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase18() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase19() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_CONTENT_GUARD_ALERT + "?access_token=" + AccessTokenService.INTERNAL_KEY + "&id=168168";
		System.out.println("testCase19() requestUrl=" + requestUrl);
		ZapierException zapierException = null;
		try {
			webServiceClientService.sendHttpDeleteRequest(requestUrl);
			fail("testCase19() should throw Exception.");
		} catch (Exception e) {
			if (e instanceof ZapierException == false) {
				fail("testCase19() should throw ZapierException.");
			} else {
				zapierException = (ZapierException) e;
				assertEquals("testCase19() zapierException.getHttpStatusCode() is incorrect.", "813", String.valueOf(zapierException.getHttpStatusCode()));
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase19() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase20() throws Exception {
		long startTimestamp = System.currentTimeMillis();

		// step 1: create subscription
		ZapierWebhookEntity zapierWebhookEntity = null;
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_CONTENT_GUARD_ALERT;
		System.out.println("testCase20() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierResourceRequest = new ZapierResourceRequest();
		zapierResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		zapierResourceRequest.setDomain_id(1701);
		zapierResourceRequest.setGroup_name("Daily Test Group 1");
		zapierResourceRequest.setUser_email("<EMAIL>");
		zapierResourceRequest.setCallback_url("https://hook.zapier.com/callback_url_1");
		String requestParameters = new Gson().toJson(zapierResourceRequest, ZapierResourceRequest.class);

		ZapierResourceResponse zapierResourceResponse = webServiceClientService.sendHttpPostRequest(requestUrl, requestParameters);
		assertNotNull("testCase20() zapierResourceResponse should not be null.", zapierResourceResponse);
		assertNotNull("testCase20() zapierResourceResponse.getId() should not be null.", zapierResourceResponse.getId());
		if (isUnitTest == true) {
			zapierWebhookEntity = zapierWebhookDAO.get(zapierResourceResponse.getId());
			assertNotNull("testCase20() zapierWebhookEntity should not be null.", zapierWebhookEntity);
			assertEquals("testCase20() zapierWebhookEntity.getDomainId() is incorrect.", "1701", String.valueOf(zapierWebhookEntity.getDomainId()));
			assertEquals("testCase20() zapierWebhookEntity.getUserId() is incorrect.", "214", String.valueOf(zapierWebhookEntity.getUserId()));
			assertEquals("testCase20() zapierWebhookEntity.getSubType() is incorrect.", "69", zapierWebhookEntity.getSubType());
			assertEquals("testCase20() zapierWebhookEntity.getTriggerType() is incorrect.", "1", String.valueOf(zapierWebhookEntity.getTriggerType()));
			assertEquals("testCase20() zapierWebhookEntity.getCallbackUrl() is incorrect.", "https://hook.zapier.com/callback_url_1",
					String.valueOf(zapierWebhookEntity.getCallbackUrl()));
		}

		// step 2: delete subscription		
		requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CONTENT_GUARD_ALERT
				+ "?access_token=" + AccessTokenService.INTERNAL_KEY + "&id=" + zapierResourceResponse.getId();
		System.out.println("testCase20() requestUrl=" + requestUrl);
		zapierResourceResponse = webServiceClientService.sendHttpDeleteRequest(requestUrl);
		assertNotNull("testCase20() zapierResourceResponse should not be null.", zapierResourceResponse);
		String json = new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class);
		System.out.println("testCase20() json=" + json);
		FormatUtils.getInstance().logMemoryUsage("testCase20() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase21() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_CONTENT_GUARD_ALERT;
		System.out.println("testCase21() requestUrl=" + requestUrl);
		ZapierException zapierException = null;
		try {
			webServiceClientService.getZapierContentGuardAlerts(requestUrl);
			fail("testCase21() should throw Exception.");
		} catch (Exception e) {
			if (e instanceof ZapierException == false) {
				fail("testCase21() should throw ZapierException.");
			} else {
				zapierException = (ZapierException) e;
				assertEquals("testCase21() zapierException.getHttpStatusCode() is incorrect.", "802", String.valueOf(zapierException.getHttpStatusCode()));
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase21() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase22() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_CONTENT_GUARD_ALERT + "?access_token=testaccesstoken";
		System.out.println("testCase22() requestUrl=" + requestUrl);
		ZapierException zapierException = null;
		try {
			webServiceClientService.getZapierContentGuardAlerts(requestUrl);
			fail("testCase22() should throw Exception.");
		} catch (Exception e) {
			if (e instanceof ZapierException == false) {
				fail("testCase22() should throw ZapierException.");
			} else {
				zapierException = (ZapierException) e;
				assertEquals("testCase22() zapierException.getHttpStatusCode() is incorrect.", "803", String.valueOf(zapierException.getHttpStatusCode()));
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase22() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase23() throws Exception {
		long startTimestamp = System.currentTimeMillis();

		// retrieve sample content guard alert messages
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_CONTENT_GUARD_ALERT + "?access_token=" + AccessTokenService.INTERNAL_KEY;
		System.out.println("testCase23() requestUrl=" + requestUrl);
		ZapierContentGuardAlert[] zapierContentGuardAlertArray = webServiceClientService.getZapierContentGuardAlerts(requestUrl);
		assertNotNull("testCase23() zapierContentGuardAlertArray should not be null.", zapierContentGuardAlertArray);

		// first alert
		assertEquals("testCase23() zapierContentGuardAlertArray[0].getId() is incorrect.", "1614274302629", zapierContentGuardAlertArray[0].getId());
		assertEquals("testCase23() zapierContentGuardAlertArray[0].getAlert_timestamp() is incorrect.", "2021-02-25 11:31:32",
				zapierContentGuardAlertArray[0].getAlert_timestamp());
		assertEquals("testCase23() zapierContentGuardAlertArray[0].getDomain_id() is incorrect.", "987654",
				String.valueOf(zapierContentGuardAlertArray[0].getDomain_id()));
		assertEquals("testCase23() zapierContentGuardAlertArray[0].getDomain_name() is incorrect.", "www.testdomain.com",
				zapierContentGuardAlertArray[0].getDomain_name());
		assertEquals("testCase23() zapierContentGuardAlertArray[0].getGroup_id() is incorrect.", "876583",
				String.valueOf(zapierContentGuardAlertArray[0].getGroup_id()));
		assertEquals("testCase23() zapierContentGuardAlertArray[0].getGroup_name() is incorrect.", "Daily Test Group 1",
				zapierContentGuardAlertArray[0].getGroup_name());
		assertEquals("testCase23() zapierContentGuardAlertArray[0].getUrl() is incorrect.", "https://www.testdomain.com/page_1.html",
				zapierContentGuardAlertArray[0].getUrl());
		assertEquals("testCase23() zapierContentGuardAlertArray[0].getChange_desc() is incorrect.", "Description changed",
				zapierContentGuardAlertArray[0].getChange_desc());
		assertEquals("testCase23() zapierContentGuardAlertArray[0].getChange_severity() is incorrect.", "Medium", zapierContentGuardAlertArray[0].getChange_severity());
		assertEquals("testCase23() zapierContentGuardAlertArray[0].getPrevious_crawl_timestamp() is incorrect.", "2021-02-10 00:02:21",
				zapierContentGuardAlertArray[0].getPrevious_crawl_timestamp());
		assertEquals("testCase23() zapierContentGuardAlertArray[0].getCurrent_crawl_timestamp() is incorrect.", "2021-02-11 00:02:23",
				zapierContentGuardAlertArray[0].getCurrent_crawl_timestamp());
		assertEquals("testCase23() zapierContentGuardAlertArray[0].getPrevious_content() is incorrect.", "Previous description content.",
				zapierContentGuardAlertArray[0].getPrevious_content());
		assertEquals("testCase23() zapierContentGuardAlertArray[0].getCurrent_content() is incorrect.", "Current description content.",
				zapierContentGuardAlertArray[0].getCurrent_content());
		assertEquals("testCase23() zapierContentGuardAlertArray[0].getError_code() is incorrect.", "", zapierContentGuardAlertArray[0].getError_code());
		assertEquals("testCase23() zapierContentGuardAlertArray[0].getError_message() is incorrect.", "", zapierContentGuardAlertArray[0].getError_message());

		// second alert
		assertEquals("testCase23() zapierContentGuardAlertArray[1].getId() is incorrect.", "1614274302630", zapierContentGuardAlertArray[1].getId());
		assertEquals("testCase23() zapierContentGuardAlertArray[1].getAlert_timestamp() is incorrect.", "2021-02-25 11:31:38",
				zapierContentGuardAlertArray[1].getAlert_timestamp());
		assertEquals("testCase23() zapierContentGuardAlertArray[1].getDomain_id() is incorrect.", "987654",
				String.valueOf(zapierContentGuardAlertArray[1].getDomain_id()));
		assertEquals("testCase23() zapierContentGuardAlertArray[1].getDomain_name() is incorrect.", "www.testdomain.com",
				zapierContentGuardAlertArray[1].getDomain_name());
		assertEquals("testCase23() zapierContentGuardAlertArray[1].getGroup_id() is incorrect.", "876588",
				String.valueOf(zapierContentGuardAlertArray[1].getGroup_id()));
		assertEquals("testCase23() zapierContentGuardAlertArray[1].getGroup_name() is incorrect.", "Hourly Test Group 2",
				zapierContentGuardAlertArray[1].getGroup_name());
		assertEquals("testCase23() zapierContentGuardAlertArray[1].getUrl() is incorrect.", "https://www.testdomain.com/page_2.html",
				zapierContentGuardAlertArray[1].getUrl());
		assertEquals("testCase23() zapierContentGuardAlertArray[1].getChange_desc() is incorrect.", "Title changed", zapierContentGuardAlertArray[1].getChange_desc());
		assertEquals("testCase23() zapierContentGuardAlertArray[1].getChange_severity() is incorrect.", "Critical",
				zapierContentGuardAlertArray[1].getChange_severity());
		assertEquals("testCase23() zapierContentGuardAlertArray[1].getPrevious_crawl_timestamp() is incorrect.", "2021-02-27 18:02:21",
				zapierContentGuardAlertArray[1].getPrevious_crawl_timestamp());
		assertEquals("testCase23() zapierContentGuardAlertArray[1].getCurrent_crawl_timestamp() is incorrect.", "2021-02-28 18:02:23",
				zapierContentGuardAlertArray[1].getCurrent_crawl_timestamp());
		assertEquals("testCase23() zapierContentGuardAlertArray[1].getPrevious_content() is incorrect.", "Previous title content.",
				zapierContentGuardAlertArray[1].getPrevious_content());
		assertEquals("testCase23() zapierContentGuardAlertArray[1].getCurrent_content() is incorrect.", "Current title content.",
				zapierContentGuardAlertArray[1].getCurrent_content());
		assertEquals("testCase23() zapierContentGuardAlertArray[1].getError_code() is incorrect.", "", zapierContentGuardAlertArray[1].getError_code());
		assertEquals("testCase23() zapierContentGuardAlertArray[1].getError_message() is incorrect.", "", zapierContentGuardAlertArray[1].getError_message());

		FormatUtils.getInstance().logMemoryUsage("testCase23() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase24() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_CONTENT_GUARD_ALERT;
		System.out.println("testCase24() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierResourceRequest = new ZapierResourceRequest();
		zapierResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		zapierResourceRequest.setDomain_id(1295);
		zapierResourceRequest.setUser_email("<EMAIL>");
		zapierResourceRequest.setCallback_url("https://hook.zapier.com/callback_url_1");
		String requestParameters = new Gson().toJson(zapierResourceRequest, ZapierResourceRequest.class);
		ZapierException zapierException = null;
		try {
			webServiceClientService.sendHttpPostRequest(requestUrl, requestParameters);
			fail("testCase24() should throw Exception.");
		} catch (Exception e) {
			if (e instanceof ZapierException == false) {
				fail("testCase24() should throw ZapierException.");
			} else {
				zapierException = (ZapierException) e;
				assertEquals("testCase24() zapierException.getHttpStatusCode() is incorrect.", "815", String.valueOf(zapierException.getHttpStatusCode()));
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase24() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase25() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_CONTENT_GUARD_ALERT;
		System.out.println("testCase25() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierResourceRequest = new ZapierResourceRequest();
		zapierResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		zapierResourceRequest.setDomain_id(1295);
		zapierResourceRequest.setUser_email("<EMAIL>");
		zapierResourceRequest.setCallback_url("https://hook.zapier.com/callback_url_1");
		zapierResourceRequest.setGroup_name("invalid group name");
		String requestParameters = new Gson().toJson(zapierResourceRequest, ZapierResourceRequest.class);
		ZapierException zapierException = null;
		try {
			webServiceClientService.sendHttpPostRequest(requestUrl, requestParameters);
			fail("testCase25() should throw Exception.");
		} catch (Exception e) {
			if (e instanceof ZapierException == false) {
				fail("testCase25() should throw ZapierException.");
			} else {
				zapierException = (ZapierException) e;
				assertEquals("testCase25() zapierException.getHttpStatusCode() is incorrect.", "814", String.valueOf(zapierException.getHttpStatusCode()));
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase25() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase26() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER + "test";
		System.out.println("testCase26() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierResourceRequest = new ZapierResourceRequest();
		zapierResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		zapierResourceRequest.setDomain_id(1295);
		zapierResourceRequest.setUser_email("<EMAIL>");
		zapierResourceRequest.setCallback_url("https://hook.zapier.com/callback_url_1");
		zapierResourceRequest.setGroup_name("invalid group name");
		String requestParameters = new Gson().toJson(zapierResourceRequest, ZapierResourceRequest.class);
		ZapierException zapierException = null;
		try {
			webServiceClientService.sendHttpPostRequest(requestUrl, requestParameters);
			fail("testCase26() should throw Exception.");
		} catch (Exception e) {
			if (e instanceof ZapierException == false) {
				fail("testCase26() should throw ZapierException.");
			} else {
				zapierException = (ZapierException) e;
				assertEquals("testCase26() zapierException.getHttpStatusCode() is incorrect.", "817", String.valueOf(zapierException.getHttpStatusCode()));
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase26() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase27() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER + "test" + "?access_token="
				+ AccessTokenService.INTERNAL_KEY + "&id=168168";
		System.out.println("testCase27() requestUrl=" + requestUrl);
		ZapierException zapierException = null;
		try {
			webServiceClientService.sendHttpDeleteRequest(requestUrl);
			fail("testCase27() should throw Exception.");
		} catch (Exception e) {
			if (e instanceof ZapierException == false) {
				fail("testCase27() should throw ZapierException.");
			} else {
				zapierException = (ZapierException) e;
				assertEquals("testCase27() zapierException.getHttpStatusCode() is incorrect.", "817", String.valueOf(zapierException.getHttpStatusCode()));
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase27() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase28() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER + "test" + "?access_token="
				+ AccessTokenService.INTERNAL_KEY;
		System.out.println("testCase28() requestUrl=" + requestUrl);
		ZapierException zapierException = null;
		try {
			webServiceClientService.getZapierContentGuardAlerts(requestUrl);
			fail("testCase28() should throw Exception.");
		} catch (Exception e) {
			if (e instanceof ZapierException == false) {
				fail("testCase28() should throw ZapierException.");
			} else {
				zapierException = (ZapierException) e;
				assertEquals("testCase28() zapierException.getHttpStatusCode() is incorrect.", "817", String.valueOf(zapierException.getHttpStatusCode()));
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase28() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

}