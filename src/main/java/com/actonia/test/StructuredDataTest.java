package com.actonia.test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;

import java.io.File;
import java.util.List;

import org.apache.commons.io.FileUtils;

import com.actonia.IConstants;
import com.actonia.utils.CrawlerUtils;
import com.actonia.value.object.CrawlerResponse;
import com.actonia.value.object.StructuredData;
import com.google.gson.Gson;

public class StructuredDataTest {

	public static void main(String[] args) {
		StructuredDataTest structuredDataTest = null;
		try {
			structuredDataTest = new StructuredDataTest();
			structuredDataTest.testCase1();
			structuredDataTest.testCase2();
			structuredDataTest.testCase3();
			structuredDataTest.testCase6();
			structuredDataTest.testCase7();
			structuredDataTest.testCase8();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void testCase1() throws Exception {
		try {
			File inputFile = new File("C://dev//eclipse_workspace_svn_1.6//polite-crawl-maven//src//main//resources//json//structured_data_test_case_1.json");
			List<String> testStringList = FileUtils.readLines(inputFile, IConstants.UTF_8);
			String json = testStringList.get(0);
			System.out.println("testCase1() json=" + json);
			Gson gson = new Gson();
			CrawlerResponse crawlerResponse = gson.fromJson(json, CrawlerResponse.class);
			if (crawlerResponse != null) {
				System.out.println("testCase1() crawlerResponse.getStructured_data().toString()=" + crawlerResponse.getStructured_data().toString());
				String testString = new Gson().toJson(crawlerResponse.getStructured_data(), StructuredData.class);
				System.out.println("testCase1() de-serialized structured_data=" + testString);
				// {"data":{}}
				assertEquals("testCase1() de-serialized structured_data incorrect", "{\"data\":{}}", testString);
				boolean isStructuredDataAvailable = CrawlerUtils.getInstance().checkIfStructuredDataAvailable(crawlerResponse.getStructured_data());
				assertEquals("testCase1() de-serialized isStructuredDataAvailable incorrect", isStructuredDataAvailable, false);
				System.out.println("testCase1() passed.");
			} else {
				fail("testCase1() crawlerResponse should not be null.");
			}

		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void testCase2() throws Exception {
		try {
			File inputFile = new File("C://dev//eclipse_workspace_svn_1.6//polite-crawl-maven//src//main//resources//json//structured_data_test_case_2.json");
			List<String> testStringList = FileUtils.readLines(inputFile, IConstants.UTF_8);
			String json = testStringList.get(0);
			System.out.println("testCase2() json=" + json);
			Gson gson = new Gson();
			CrawlerResponse crawlerResponse = gson.fromJson(json, CrawlerResponse.class);
			if (crawlerResponse != null) {
				System.out.println("testCase2() crawlerResponse.getStructured_data().toString()=" + crawlerResponse.getStructured_data().toString());
				String testString = new Gson().toJson(crawlerResponse.getStructured_data(), StructuredData.class);
				System.out.println("testCase2() de-serialized structured_data=" + testString);
				// {"data":{"check_structured_data":[{"data_type":"BreadcrumbList","encoding":"json-ld"},{"data_type":"Store","encoding":"microdata"}]}}
				assertEquals("testCase2() de-serialized structured_data incorrect",
						"{\"data\":{\"check_structured_data\":[{\"data_type\":\"BreadcrumbList\",\"encoding\":\"json-ld\"},{\"data_type\":\"Store\",\"encoding\":\"microdata\"}]}}",
						testString);
				boolean isStructuredDataAvailable = CrawlerUtils.getInstance().checkIfStructuredDataAvailable(crawlerResponse.getStructured_data());
				assertEquals("testCase2() de-serialized isStructuredDataAvailable incorrect", isStructuredDataAvailable, true);
				System.out.println("testCase2() passed.");
			} else {
				fail("testCase2() crawlerResponse should not be null.");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void testCase3() throws Exception {
		try {
			File inputFile = new File("C://dev//eclipse_workspace_svn_1.6//polite-crawl-maven//src//main//resources//json//structured_data_test_case_3.json");
			List<String> testStringList = FileUtils.readLines(inputFile, IConstants.UTF_8);
			String json = testStringList.get(0);
			System.out.println("testCase3() json=" + json);
			Gson gson = new Gson();
			CrawlerResponse crawlerResponse = gson.fromJson(json, CrawlerResponse.class);
			if (crawlerResponse != null) {
				System.out.println("testCase3() crawlerResponse.getStructured_data().toString()=" + crawlerResponse.getStructured_data().toString());
				String testString = new Gson().toJson(crawlerResponse.getStructured_data(), StructuredData.class);
				System.out.println("testCase3() de-serialized structured_data=" + testString);
				// {"data":{"check_structured_data":[{"data_type":"BreadcrumbList","encoding":"json-ld"},{"data_type":"Store","encoding":"microdata"}],"validate_structured_data":[{"data_type":"BreadcrumbList","encoding":"json-ld","errors":[{"message":"name is a required property","type":"badField","path":["itemListElement","0"]},{"message":"name is a required property","type":"badField","path":["itemListElement","1"]}]},{"data_type":"Store","encoding":"microdata","errors":[{"message":"'image' is a required property","type":"missingField","path":[]},{"message":"'name' is a required property","type":"missingField","path":[]},{"message":"'address' is a required property","type":"missingField","path":[]},{"message":"'priceRange' is a required property","type":"missingField","path":[]},{"message":"'telephone' is a required property","type":"missingField","path":[]}]}]}}
				//assertEquals("testCase3() de-serialized structured_data incorrect", "{\"data\":{\"check_structured_data\":[{\"data_type\":\"BreadcrumbList\",\"encoding\":\"json-ld\"},{\"data_type\":\"Store\",\"encoding\":\"microdata\"}],\"validate_structured_data\":[{\"data_type\":\"BreadcrumbList\",\"encoding\":\"json-ld\",\"errors\":[{\"message\":\"name is a required property\",\"type\":\"badField\",\"path\":[\"itemListElement\",\"0\"]},{\"message\":\"name is a required property\",\"type\":\"badField\",\"path\":[\"itemListElement\",\"1\"]}]},{\"data_type\":\"Store\",\"encoding\":\"microdata\",\"errors\":[{\"message\":\"\u0027image\u0027 is a required property\",\"type\":\"missingField\",\"path\":[]},{\"message\":\"\u0027name\u0027 is a required property\",\"type\":\"missingField\",\"path\":[]},{\"message\":\"\u0027address\u0027 is a required property\",\"type\":\"missingField\",\"path\":[]},{\"message\":\"\u0027priceRange\u0027 is a required property\",\"type\":\"missingField\",\"path\":[]},{\"message\":\"\u0027telephone\u0027 is a required property\",\"type\":\"missingField\",\"path\":[]}]}]}}", testString);
				boolean isStructuredDataAvailable = CrawlerUtils.getInstance().checkIfStructuredDataAvailable(crawlerResponse.getStructured_data());
				assertEquals("testCase3() de-serialized isStructuredDataAvailable incorrect", isStructuredDataAvailable, true);
				System.out.println("testCase3() passed.");
			} else {
				fail("testCase3() crawlerResponse should not be null.");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void testCase6() throws Exception {
		try {
			File inputFile = new File("C://dev//eclipse_workspace_svn_1.6//polite-crawl-maven//src//main//resources//json//structured_data_test_case_6.json");
			List<String> testStringList = FileUtils.readLines(inputFile, IConstants.UTF_8);
			String json = testStringList.get(0);
			System.out.println("testCase6() json=" + json);
			Gson gson = new Gson();
			CrawlerResponse crawlerResponse = gson.fromJson(json, CrawlerResponse.class);
			if (crawlerResponse != null) {
				System.out.println("testCase6() crawlerResponse.getStructured_data().toString()=" + crawlerResponse.getStructured_data().toString());
				String testString = new Gson().toJson(crawlerResponse.getStructured_data(), StructuredData.class);
				System.out.println("testCase6() de-serialized structured_data=" + testString);
				// {"data":{"check_structured_data":[{"data_type":"Corporation","encoding":"microdata"}],"validate_structured_data":[{"data_type":"Corporation","encoding":"microdata","errors":[]}]}}
				assertEquals("testCase6() de-serialized structured_data incorrect",
						"{\"data\":{\"check_structured_data\":[{\"data_type\":\"Corporation\",\"encoding\":\"microdata\"}],\"validate_structured_data\":[{\"data_type\":\"Corporation\",\"encoding\":\"microdata\",\"errors\":[]}]}}",
						testString);
				boolean isStructuredDataAvailable = CrawlerUtils.getInstance().checkIfStructuredDataAvailable(crawlerResponse.getStructured_data());
				assertEquals("testCase6() de-serialized isStructuredDataAvailable incorrect", isStructuredDataAvailable, true);
				System.out.println("testCase6() passed.");
			} else {
				fail("testCase6() crawlerResponse should not be null.");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void testCase7() throws Exception {
		try {
			File inputFile = new File("C://dev//eclipse_workspace_svn_1.6//polite-crawl-maven//src//main//resources//json//structured_data_test_case_7.json");
			List<String> testStringList = FileUtils.readLines(inputFile, IConstants.UTF_8);
			String json = testStringList.get(0);
			System.out.println("testCase7() json=" + json);
			Gson gson = new Gson();
			CrawlerResponse crawlerResponse = gson.fromJson(json, CrawlerResponse.class);
			if (crawlerResponse != null) {
				System.out.println("testCase7() crawlerResponse.getStructured_data().toString()=" + crawlerResponse.getStructured_data().toString());
				String testString = new Gson().toJson(crawlerResponse.getStructured_data(), StructuredData.class);
				System.out.println("testCase7() de-serialized structured_data=" + testString);
				// {"data":{"check_structured_data":[],"validate_structured_data":[]}}
				assertEquals("testCase7() de-serialized structured_data incorrect", "{\"data\":{\"check_structured_data\":[],\"validate_structured_data\":[]}}",
						testString);
				boolean isStructuredDataAvailable = CrawlerUtils.getInstance().checkIfStructuredDataAvailable(crawlerResponse.getStructured_data());
				assertEquals("testCase7() de-serialized isStructuredDataAvailable incorrect", isStructuredDataAvailable, false);
				System.out.println("testCase7() passed.");
			} else {
				fail("testCase7() crawlerResponse should not be null.");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void testCase8() throws Exception {
		try {
			File inputFile = new File("C://dev//eclipse_workspace_svn_1.6//polite-crawl-maven//src//main//resources//json//structured_data_test_case_8.json");
			List<String> testStringList = FileUtils.readLines(inputFile, IConstants.UTF_8);
			String json = testStringList.get(0);
			System.out.println("testCase8() json=" + json);
			Gson gson = new Gson();
			CrawlerResponse crawlerResponse = gson.fromJson(json, CrawlerResponse.class);
			if (crawlerResponse != null) {
				System.out.println("testCase8() crawlerResponse.getStructured_data().toString()=" + crawlerResponse.getStructured_data().toString());
				String testString = new Gson().toJson(crawlerResponse.getStructured_data(), StructuredData.class);
				System.out.println("testCase8() de-serialized structured_data=" + testString);
				// {"data":{"check_structured_data":[]}}
				assertEquals("testCase8() de-serialized structured_data incorrect", "{\"data\":{\"check_structured_data\":[]}}", testString);
				boolean isStructuredDataAvailable = CrawlerUtils.getInstance().checkIfStructuredDataAvailable(crawlerResponse.getStructured_data());
				assertEquals("testCase8() de-serialized isStructuredDataAvailable incorrect", isStructuredDataAvailable, false);
				System.out.println("testCase8() passed.");
			} else {
				fail("testCase8() crawlerResponse should not be null.");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
