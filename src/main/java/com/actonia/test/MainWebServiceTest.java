package com.actonia.test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

import java.util.ArrayList;
import java.util.List;

import com.actonia.IConstants;
import com.actonia.service.AccessTokenService;
import com.actonia.service.MainWebServiceClientService;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.FindTargetUrlsDifferencesRequest;
import com.actonia.value.object.FindTargetUrlsDifferencesResponse;
import com.actonia.value.object.HreflangErrors;
import com.actonia.value.object.HreflangLinks;
import com.actonia.value.object.TargetUrlsDifferencesRequest;
import com.google.gson.Gson;

public class MainWebServiceTest {

	private MainWebServiceClientService mainWebServiceClientService;

	public MainWebServiceTest() {
		super();
		this.mainWebServiceClientService = SpringBeanFactory.getBean("mainWebServiceClientService");
	}

	public static void main(String[] args) throws Exception {
		MainWebServiceTest mainWebServiceTest = new MainWebServiceTest();
		mainWebServiceTest.testCase1();
		mainWebServiceTest.testCase2();
		mainWebServiceTest.testCase3();
		mainWebServiceTest.testCase4();
		mainWebServiceTest.testCase6();
		mainWebServiceTest.testCase7();
		mainWebServiceTest.testCase8();
		mainWebServiceTest.testCase9();
		mainWebServiceTest.testCase10();
		mainWebServiceTest.testCase11();
		mainWebServiceTest.testCase12();
		mainWebServiceTest.testCase13();
		mainWebServiceTest.testCase14();
		mainWebServiceTest.testCase15();
		mainWebServiceTest.testCase16();
		mainWebServiceTest.testCase18();
		mainWebServiceTest.testCase20();
		mainWebServiceTest.testCase21();
		mainWebServiceTest.testCase22();
		mainWebServiceTest.testCase24();
		mainWebServiceTest.testCase25();
		mainWebServiceTest.testCase26();
		mainWebServiceTest.testCase28();
		mainWebServiceTest.testCase30();
		mainWebServiceTest.testCase31();
		mainWebServiceTest.testCase32();
		mainWebServiceTest.testCase34();
		mainWebServiceTest.testCase36();
		mainWebServiceTest.testCase37();
		mainWebServiceTest.testCase38();
		mainWebServiceTest.testCase39();
		mainWebServiceTest.testCase40();
		mainWebServiceTest.testCase41();
		mainWebServiceTest.testCase42();
		mainWebServiceTest.testCase43();
		mainWebServiceTest.testCase44();
		mainWebServiceTest.testCase45();
		mainWebServiceTest.testCase47();
		mainWebServiceTest.testCase48();
		mainWebServiceTest.testCase49();
		mainWebServiceTest.testCase50();
		mainWebServiceTest.testCase51();
		mainWebServiceTest.testCase52();
		mainWebServiceTest.testCase54();
		mainWebServiceTest.testCase55();
		mainWebServiceTest.testCase56();
		mainWebServiceTest.testCase57();
		mainWebServiceTest.testCase58();
		mainWebServiceTest.testCase60();
		mainWebServiceTest.testCase61();
		mainWebServiceTest.testCase62();
		mainWebServiceTest.testCase64();
		mainWebServiceTest.testCase65();
		mainWebServiceTest.testCase67();
		mainWebServiceTest.testCase70();
		mainWebServiceTest.testCase73();
		mainWebServiceTest.testCase74();
		mainWebServiceTest.testCase75();
		mainWebServiceTest.testCase76();
		mainWebServiceTest.testCase77();
		mainWebServiceTest.testCase78();
		mainWebServiceTest.testCase79();
		mainWebServiceTest.testCase80();
		mainWebServiceTest.testCase81();
		mainWebServiceTest.testCase82();
		mainWebServiceTest.testCase83();
		mainWebServiceTest.testCase84();
		mainWebServiceTest.testCase85();
		mainWebServiceTest.testCase86();
		mainWebServiceTest.testCase87();
		mainWebServiceTest.testCase88();
		mainWebServiceTest.testCase89();
		mainWebServiceTest.testCase90();
		mainWebServiceTest.testCase91();
		mainWebServiceTest.testCase92();
		mainWebServiceTest.testCase93();
		mainWebServiceTest.testCase94();
		mainWebServiceTest.testCase95();
		mainWebServiceTest.testCase96();
		mainWebServiceTest.testCase97();
		mainWebServiceTest.testCase98();
		mainWebServiceTest.testCase99();
		mainWebServiceTest.testCase100();
		mainWebServiceTest.testCase101();
		mainWebServiceTest.testCase102();
		mainWebServiceTest.testCase103();
		mainWebServiceTest.testCase105();
		mainWebServiceTest.testCase106();
		mainWebServiceTest.testCase107();
	}

	private void testCase1() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase1() requestUrl=" + requestUrl);
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;

		// first request
		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token("testaccesstoken");
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase1() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase1() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase1() findTargetUrlsDifferencesResponse json=" + json);
		assertNotNull("testCase1() findTargetUrlsDifferencesResponse.getError() should not be null.", findTargetUrlsDifferencesResponse.getError());
		assertEquals("testCase1() json is incorrect", "{\"error\":{\"error_code\":\"PCWS-000002\",\"error_message\":\"Invalid access.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase1() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase2() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase2() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		//crawlTimestamp1 = "2020-02-17 01:13:59";
		domainId1 = "475";
		decodedUrl1 = "https://www.amazon.com/b?ie=UTF8&node=11971391";
		crawlTimestamp2 = "2020-02-19 12:54:29";
		domainId2 = "475";
		decodedUrl2 = "https://www.amazon.com/b?ie=UTF8&node=11971391";
		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase2() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase2() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase2() findTargetUrlsDifferencesResponse json=" + json);
		assertEquals("testCase2() json is incorrect",
				"{\"responses\":[{\"error\":{\"sequence\":\"1\",\"error_code\":\"PCWS-000004\",\"error_message\":\"Query parameter crawl_timestamp_1 is required.\"}}]}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase2() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase3() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase3() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-02-17 01:13:59";
		//domainId1 = "475";
		decodedUrl1 = "https://www.amazon.com/b?ie=UTF8&node=11971391";
		crawlTimestamp2 = "2020-02-19 12:54:29";
		domainId2 = "475";
		decodedUrl2 = "https://www.amazon.com/b?ie=UTF8&node=11971391";
		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase3() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase3() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase3() findTargetUrlsDifferencesResponse json=" + json);
		assertEquals("testCase3() json is incorrect",
				"{\"responses\":[{\"error\":{\"sequence\":\"1\",\"error_code\":\"PCWS-000005\",\"error_message\":\"Query parameter domain_id_1 is required.\"}}]}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase3() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase4() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase4() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-02-17 01:13:59";
		domainId1 = "475";
		//decodedUrl1 = "https://www.amazon.com/b?ie=UTF8&node=11971391";
		crawlTimestamp2 = "2020-02-19 12:54:29";
		domainId2 = "475";
		decodedUrl2 = "https://www.amazon.com/b?ie=UTF8&node=11971391";
		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase4() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase4() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase4() findTargetUrlsDifferencesResponse json=" + json);
		assertEquals("testCase4() json is incorrect",
				"{\"responses\":[{\"error\":{\"sequence\":\"1\",\"error_code\":\"PCWS-000006\",\"error_message\":\"Query parameter url_1 is required.\"}}]}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase4() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase6() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase6() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-02-17 01:13:59";
		domainId1 = "475";
		decodedUrl1 = "https://www.amazon.com/b?ie=UTF8&node=11971391";
		crawlTimestamp2 = "2020-02-19 12:54:29";
		//domainId2 = "475";
		decodedUrl2 = "https://www.amazon.com/b?ie=UTF8&node=11971391";
		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase6() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase6() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase6() findTargetUrlsDifferencesResponse json=" + json);
		assertEquals("testCase6() json is incorrect",
				"{\"responses\":[{\"error\":{\"sequence\":\"1\",\"error_code\":\"PCWS-000008\",\"error_message\":\"Query parameter domain_id_2 is required.\"}}]}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase6() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase7() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase7() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-02-17 01:13:59";
		domainId1 = "475";
		decodedUrl1 = "https://www.amazon.com/b?ie=UTF8&node=11971391";
		crawlTimestamp2 = "2020-02-19 12:54:29";
		domainId2 = "475";
		//decodedUrl2 = "https://www.amazon.com/b?ie=UTF8&node=11971391";
		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase7() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase7() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase7() findTargetUrlsDifferencesResponse json=" + json);
		assertEquals("testCase7() json is incorrect",
				"{\"responses\":[{\"error\":{\"sequence\":\"1\",\"error_code\":\"PCWS-000009\",\"error_message\":\"Query parameter url_2 is required.\"}}]}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase7() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase8() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase8() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-02-30";
		domainId1 = "475";
		decodedUrl1 = "https://www.amazon.com/b?ie=UTF8&node=11971391";
		crawlTimestamp2 = "2020-02-19 12:54:29";
		domainId2 = "475";
		decodedUrl2 = "https://www.amazon.com/b?ie=UTF8&node=11971391";
		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase8() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase8() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase8() findTargetUrlsDifferencesResponse json=" + json);
		assertEquals("testCase8() json is incorrect",
				"{\"responses\":[{\"error\":{\"sequence\":\"1\",\"error_code\":\"PCWS-000010\",\"error_message\":\"Query parameter crawl_timestamp_1 2020-02-30 is invalid.\"}}]}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase8() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase9() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase9() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-02-17 01:13:59";
		domainId1 = "anc";
		decodedUrl1 = "https://www.amazon.com/b?ie=UTF8&node=11971391";
		crawlTimestamp2 = "2020-02-19 12:54:29";
		domainId2 = "475";
		decodedUrl2 = "https://www.amazon.com/b?ie=UTF8&node=11971391";
		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase9() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase9() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase9() findTargetUrlsDifferencesResponse json=" + json);
		assertEquals("testCase9() json is incorrect",
				"{\"responses\":[{\"error\":{\"sequence\":\"1\",\"error_code\":\"PCWS-000011\",\"error_message\":\"Query parameter domain_id_1 anc is invalid.\"}}]}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase9() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase10() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase10() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-02-17 01:13:59";
		domainId1 = "475";
		decodedUrl1 = "wwwamazoncom/b?ie=UTF8&node=11971391";
		crawlTimestamp2 = "2020-02-19 12:54:29";
		domainId2 = "475";
		decodedUrl2 = "https://www.amazon.com/b?ie=UTF8&node=11971391";
		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase10() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase10() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase10() findTargetUrlsDifferencesResponse json=" + json);
		assertEquals("testCase10() findTargetUrlsDifferencesResponse.getResponses()[0].getError().getError_code() is incorrect", "PCWS-000012",
				findTargetUrlsDifferencesResponse.getResponses()[0].getError().getError_code());
		FormatUtils.getInstance().logMemoryUsage("testCase10() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase11() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase11() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-02-17 01:13:59";
		domainId1 = "475";
		decodedUrl1 = "https://www.amazon.com/b?ie=UTF8&node=11971391";
		crawlTimestamp2 = "20201319";
		domainId2 = "475";
		decodedUrl2 = "https://www.amazon.com/b?ie=UTF8&node=11971391";
		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase11() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase11() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase11() findTargetUrlsDifferencesResponse json=" + json);
		assertEquals("testCase11() findTargetUrlsDifferencesResponse.getResponses()[0].getError().getError_code() is incorrect", "PCWS-000013",
				findTargetUrlsDifferencesResponse.getResponses()[0].getError().getError_code());
		FormatUtils.getInstance().logMemoryUsage("testCase11() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase12() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase12() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-02-17 01:13:59";
		domainId1 = "475";
		decodedUrl1 = "https://www.amazon.com/b?ie=UTF8&node=11971391";
		crawlTimestamp2 = "2020-02-19 12:54:29";
		domainId2 = "bcd";
		decodedUrl2 = "https://www.amazon.com/b?ie=UTF8&node=11971391";
		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase12() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase12() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase12() findTargetUrlsDifferencesResponse json=" + json);
		assertEquals("testCase12() findTargetUrlsDifferencesResponse.getResponses()[0].getError().getError_code() is incorrect", "PCWS-000014",
				findTargetUrlsDifferencesResponse.getResponses()[0].getError().getError_code());
		FormatUtils.getInstance().logMemoryUsage("testCase12() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase13() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase13() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-02-17 01:13:59";
		domainId1 = "475";
		decodedUrl1 = "https://www.amazon.com/b?ie=UTF8&node=11971391";
		crawlTimestamp2 = "2020-02-19 12:54:29";
		domainId2 = "475";
		decodedUrl2 = "www.amazon.comb?ie=UTF8&node=11971391";
		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase13() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase13() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase13() findTargetUrlsDifferencesResponse json=" + json);
		assertEquals("testCase13() findTargetUrlsDifferencesResponse.getResponses()[0].getError().getError_code() is incorrect", "PCWS-000015",
				findTargetUrlsDifferencesResponse.getResponses()[0].getError().getError_code());
		FormatUtils.getInstance().logMemoryUsage("testCase13() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase14() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase14() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-02-17 01:13:59";
		domainId1 = "475";
		decodedUrl1 = "https://www.amazon.com/b?ie=UTF8&node=11971391";
		crawlTimestamp2 = "2020-02-19 12:54:29";
		domainId2 = "475";
		decodedUrl2 = "https://www.amazon.com/b?ie=UTF8&node=11971391";
		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase14() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase14() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase14() findTargetUrlsDifferencesResponse json=" + json);
		assertNull("testCase14() findTargetUrlsDifferencesResponse.getError() should be null.", findTargetUrlsDifferencesResponse.getError());
		assertTrue("testCase14() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertNotNull("testCase14() findTargetUrlsDifferencesResponse.getResponses()[0].getPage_link_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getPage_link_1());
		assertNotNull("testCase14() findTargetUrlsDifferencesResponse.getResponses()[0].getPage_link_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getPage_link_2());
		FormatUtils.getInstance().logMemoryUsage("testCase14() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase15() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase15() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-02-19 12:54:29";
		domainId1 = "475";
		decodedUrl1 = "https://www.amazon.com/b?ie=UTF8&node=11971391";
		crawlTimestamp2 = "2020-02-19 12:54:29";
		domainId2 = "475";
		decodedUrl2 = "https://www.amazon.com/b?ie=UTF8&node=11971391";
		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase15() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase15() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase15() findTargetUrlsDifferencesResponse json=" + json);
		assertFalse("testCase15() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be false.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		FormatUtils.getInstance().logMemoryUsage("testCase15() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase16() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase16() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-01-30 11:58:58";
		domainId1 = "1897";
		decodedUrl1 = "https://www.abebooks.com/products/isbn/9780883940945";
		crawlTimestamp2 = "2020-01-31 23:41:34";
		domainId2 = "1897";
		decodedUrl2 = "https://www.abebooks.com/products/isbn/9780883940945";
		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase16() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase16() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase16() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase16() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase16() findTargetUrlsDifferencesResponse.getResponses()[0].getAlternate_links_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getAlternate_links_chg_ind());
		assertNotNull("testCase16() findTargetUrlsDifferencesResponse.getResponses()[0].getAlternate_links_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getAlternate_links_1());
		assertNotNull("testCase16() findTargetUrlsDifferencesResponse.getResponses()[0].getAlternate_links_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getAlternate_links_2());
		FormatUtils.getInstance().logMemoryUsage("testCase16() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase18() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase18() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2019-10-01 14:55:56";
		domainId1 = "365";
		decodedUrl1 = "https://www.autotrader.com/car-news/revelations-from-daily-driving-a-15-year-old-car-266497";
		crawlTimestamp2 = "2019-10-18 23:24:39";
		domainId2 = "365";
		decodedUrl2 = "https://www.autotrader.com/car-news/revelations-from-daily-driving-a-15-year-old-car-266497";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase18() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase18() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase18() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase18() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase18() findTargetUrlsDifferencesResponse.getResponses()[0].getAmphtml_href_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getAmphtml_href_chg_ind());
		assertNotNull("testCase18() findTargetUrlsDifferencesResponse.getResponses()[0].getAmphtml_href_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getAmphtml_href_1());
		assertNotNull("testCase18() findTargetUrlsDifferencesResponse.getResponses()[0].getAmphtml_href_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getAmphtml_href_2());
		assertEquals("testCase18() findTargetUrlsDifferencesResponse.getResponses()[0].getAmphtml_flag_1() incorrect.",
				"https://content.autotrader.com/content/autotrader/ampArticle./car-news/revelations-from-daily-driving-a-15-year-old-car-266497",
				findTargetUrlsDifferencesResponse.getResponses()[0].getAmphtml_href_1());
		assertEquals("testCase18() findTargetUrlsDifferencesResponse.getResponses()[0].getAmphtml_flag_2() incorrect.",
				"https://content.autotrader.com/content/autotrader/ampArticle./car-news/revelations-daily-driving-15-year-old-car-266497",
				findTargetUrlsDifferencesResponse.getResponses()[0].getAmphtml_href_2());
		FormatUtils.getInstance().logMemoryUsage("testCase18() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase20() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase20() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-02-12 04:22:28";
		domainId1 = "220";
		decodedUrl1 = "https://quickbooks.intuit.com/ca/accounting-software/";
		crawlTimestamp2 = "2020-02-13 05:06:06";
		domainId2 = "220";
		decodedUrl2 = "https://quickbooks.intuit.com/ca/accounting-software/";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase20() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase20() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase20() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase20() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase20() findTargetUrlsDifferencesResponse.getResponses()[0].getAnalyzed_url_s_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getAnalyzed_url_s_chg_ind());
		assertNotNull("testCase20() findTargetUrlsDifferencesResponse.getResponses()[0].getAnalyzed_url_s_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getAnalyzed_url_s_1());
		assertNotNull("testCase20() findTargetUrlsDifferencesResponse.getResponses()[0].getAnalyzed_url_s_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getAnalyzed_url_s_2());
		assertEquals("testCase20() findTargetUrlsDifferencesResponse.getResponses()[0].getAnalyzed_url_s_1() incorrect.",
				"https://quickbooks.intuit.com/ca/oa/qb-accounting-software/", findTargetUrlsDifferencesResponse.getResponses()[0].getAnalyzed_url_s_1());
		assertEquals("testCase20() findTargetUrlsDifferencesResponse.getResponses()[0].getAnalyzed_url_s_2() incorrect.",
				"https://quickbooks.intuit.com/ca/accounting-software/", findTargetUrlsDifferencesResponse.getResponses()[0].getAnalyzed_url_s_2());
		FormatUtils.getInstance().logMemoryUsage("testCase20() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase21() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase21() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2019-12-14 06:56:06";
		domainId1 = "5836";
		decodedUrl1 = "https://www.box.com/resources";

		crawlTimestamp2 = "2019-12-20 15:24:49";
		domainId2 = "5836";
		decodedUrl2 = "https://www.box.com/resources";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase21() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase21() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase21() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase21() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase21() findTargetUrlsDifferencesResponse.getResponses()[0].getArchive_flg_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getArchive_flg_chg_ind());
		assertNotNull("testCase21() findTargetUrlsDifferencesResponse.getResponses()[0].getArchive_flg_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getArchive_flg_1());
		assertNotNull("testCase21() findTargetUrlsDifferencesResponse.getResponses()[0].getArchive_flg_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getArchive_flg_2());
		assertEquals("testCase21() findTargetUrlsDifferencesResponse.getResponses()[0].getArchive_flg_1() incorrect.", "No",
				findTargetUrlsDifferencesResponse.getResponses()[0].getArchive_flg_1());
		assertEquals("testCase21() findTargetUrlsDifferencesResponse.getResponses()[0].getArchive_flg_2() incorrect.", "Yes",
				findTargetUrlsDifferencesResponse.getResponses()[0].getArchive_flg_2());
		FormatUtils.getInstance().logMemoryUsage("testCase21() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase22() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase22() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request	
		crawlTimestamp1 = "2020-02-15 05:06:25";
		domainId1 = "220";
		decodedUrl1 = "https://quickbooks.intuit.com/ca/desktop/premier/";

		crawlTimestamp2 = "2020-02-16 04:28:25";
		domainId2 = "220";
		decodedUrl2 = "https://quickbooks.intuit.com/ca/desktop/premier/";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase22() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase22() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase22() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase22() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase22() findTargetUrlsDifferencesResponse.getResponses()[0].getCanonical_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getCanonical_chg_ind());
		assertNotNull("testCase22() findTargetUrlsDifferencesResponse.getResponses()[0].getCanonical_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getCanonical_1());
		assertNotNull("testCase22() findTargetUrlsDifferencesResponse.getResponses()[0].getCanonical_2() should be not null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getCanonical_2());
		assertEquals("testCase22() findTargetUrlsDifferencesResponse.getResponses()[0].getCanonical_1() incorrect.",
				"https://quickbooks.intuit.com/ca/oa/qb-accounting-software/", findTargetUrlsDifferencesResponse.getResponses()[0].getCanonical_1());
		assertEquals("testCase22() findTargetUrlsDifferencesResponse.getResponses()[0].getCanonical_2() incorrect.", "",
				findTargetUrlsDifferencesResponse.getResponses()[0].getCanonical_2());
		FormatUtils.getInstance().logMemoryUsage("testCase22() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase24() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase24() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-01-30 07:18:14";
		domainId1 = "263";
		decodedUrl1 = "https://www.retailmenot.com/community/";

		crawlTimestamp2 = "2020-01-31 13:42:51";
		domainId2 = "263";
		decodedUrl2 = "https://www.retailmenot.com/community/";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase24() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase24() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase24() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase24() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase24() findTargetUrlsDifferencesResponse.getResponses()[0].getCanonical_type_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getCanonical_type_chg_ind());
		assertNotNull("testCase24() findTargetUrlsDifferencesResponse.getResponses()[0].getCanonical_type_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getCanonical_type_1());
		assertNotNull("testCase24() findTargetUrlsDifferencesResponse.getResponses()[0].getCanonical_type_2() should be not null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getCanonical_type_2());
		assertEquals("testCase24() findTargetUrlsDifferencesResponse.getResponses()[0].getCanonical_type_1() incorrect.", "OTHER",
				findTargetUrlsDifferencesResponse.getResponses()[0].getCanonical_type_1());
		assertEquals("testCase24() findTargetUrlsDifferencesResponse.getResponses()[0].getCanonical_type_2() incorrect.", "SAME",
				findTargetUrlsDifferencesResponse.getResponses()[0].getCanonical_type_2());
		FormatUtils.getInstance().logMemoryUsage("testCase24() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase25() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase25() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-01-25 21:42:47";
		domainId1 = "475";
		decodedUrl1 = "https://www.amazon.com/b?node=3744381";

		crawlTimestamp2 = "2020-01-31 14:53:05";
		domainId2 = "475";
		decodedUrl2 = "https://www.amazon.com/b?node=3744381";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase25() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase25() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase25() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase25() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase25() findTargetUrlsDifferencesResponse.getResponses()[0].getContent_type_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getContent_type_chg_ind());
		assertNotNull("testCase25() findTargetUrlsDifferencesResponse.getResponses()[0].getContent_type_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getContent_type_1());
		assertNotNull("testCase25() findTargetUrlsDifferencesResponse.getResponses()[0].getContent_type_2() should be not null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getContent_type_2());
		assertEquals("testCase25() findTargetUrlsDifferencesResponse.getResponses()[0].getContent_type_1() incorrect.", "text/html",
				findTargetUrlsDifferencesResponse.getResponses()[0].getContent_type_1());
		assertEquals("testCase25() findTargetUrlsDifferencesResponse.getResponses()[0].getContent_type_2() incorrect.", "text/html;charset=UTF-8",
				findTargetUrlsDifferencesResponse.getResponses()[0].getContent_type_2());
		FormatUtils.getInstance().logMemoryUsage("testCase25() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase26() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase26() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2019-10-02 16:33:41";
		domainId1 = "4758";
		decodedUrl1 = "https://www.travelocity.com/Predazzo-Hotels.d6034377.Travel-Guide-Hotels";

		crawlTimestamp2 = "2019-10-20 09:52:04";
		domainId2 = "4758";
		decodedUrl2 = "https://www.travelocity.com/Predazzo-Hotels.d6034377.Travel-Guide-Hotels";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase26() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase26() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase26() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase26() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase26() findTargetUrlsDifferencesResponse.getResponses()[0].getDescription_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getDescription_chg_ind());
		assertNotNull("testCase26() findTargetUrlsDifferencesResponse.getResponses()[0].getDescription_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getDescription_1());
		assertNotNull("testCase26() findTargetUrlsDifferencesResponse.getResponses()[0].getDescription_2() should be not null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getDescription_2());
		assertEquals("testCase26() findTargetUrlsDifferencesResponse.getResponses()[0].getDescription_1() incorrect.",
				"Need a Cheap Hotel in Predazzo? Enjoy 24/7 Social Support & a Price Match Guarantee. Find a Lower Price? We'll Refund the Difference!",
				findTargetUrlsDifferencesResponse.getResponses()[0].getDescription_1());
		assertEquals("testCase26() findTargetUrlsDifferencesResponse.getResponses()[0].getDescription_2() incorrect.", "",
				findTargetUrlsDifferencesResponse.getResponses()[0].getDescription_2());
		FormatUtils.getInstance().logMemoryUsage("testCase26() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase28() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase28() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-02-09 22:02:24";
		domainId1 = "185";
		decodedUrl1 = "https://www.overstock.com/Pet-Supplies/Litter-Boxes/On-Sale,/sale,/3648/cat.html";

		crawlTimestamp2 = "2020-02-11 14:34:30";
		domainId2 = "185";
		decodedUrl2 = "https://www.overstock.com/Pet-Supplies/Litter-Boxes/On-Sale,/sale,/3648/cat.html";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase28() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase28() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase28() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase28() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase28() findTargetUrlsDifferencesResponse.getResponses()[0].getDescription_length_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getDescription_length_chg_ind());
		assertNotNull("testCase28() findTargetUrlsDifferencesResponse.getResponses()[0].getDescription_length_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getDescription_length_1());
		assertNotNull("testCase28() findTargetUrlsDifferencesResponse.getResponses()[0].getDescription_length_2() should be not null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getDescription_length_2());
		assertEquals("testCase28() findTargetUrlsDifferencesResponse.getResponses()[0].getDescription_length_1() incorrect.", "164",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getDescription_length_1()));
		assertEquals("testCase28() findTargetUrlsDifferencesResponse.getResponses()[0].getDescription_length_2() incorrect.", "150",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getDescription_length_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase28() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase30() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase30() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2019-10-05 02:02:24";
		domainId1 = "4758";
		decodedUrl1 = "https://www.travelocity.com/Lana-Hotels.d553248633901372487.Travel-Guide-Hotels";

		crawlTimestamp2 = "2019-10-19 11:24:35";
		domainId2 = "4758";
		decodedUrl2 = "https://www.travelocity.com/Lana-Hotels.d553248633901372487.Travel-Guide-Hotels";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase30() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase30() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase30() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase30() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase30() findTargetUrlsDifferencesResponse.getResponses()[0].getFollow_flg_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getFollow_flg_chg_ind());
		assertNotNull("testCase30() findTargetUrlsDifferencesResponse.getResponses()[0].getFollow_flg_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getFollow_flg_1());
		assertNotNull("testCase30() findTargetUrlsDifferencesResponse.getResponses()[0].getFollow_flg_2() should be not null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getFollow_flg_2());
		assertEquals("testCase30() findTargetUrlsDifferencesResponse.getResponses()[0].getFollow_flg_1() incorrect.", "Yes",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getFollow_flg_1()));
		assertEquals("testCase30() findTargetUrlsDifferencesResponse.getResponses()[0].getFollow_flg_2() incorrect.", "No",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getFollow_flg_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase30() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase31() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase31() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-01-27 07:37:55";
		domainId1 = "185";
		decodedUrl1 = "https://www.overstock.com/Home-Garden/Tailor-Fit-Relaxed-Fit-Cotton-Duck-Ottoman-Slipcover/9517973/product.html";

		crawlTimestamp2 = "2020-01-31 10:02:46";
		domainId2 = "185";
		decodedUrl2 = "https://www.overstock.com/Home-Garden/Tailor-Fit-Relaxed-Fit-Cotton-Duck-Ottoman-Slipcover/9517973/product.html";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase31() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase31() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase31() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase31() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase31() findTargetUrlsDifferencesResponse.getResponses()[0].getH1_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getH1_chg_ind());
		assertNotNull("testCase31() findTargetUrlsDifferencesResponse.getResponses()[0].getH1_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getH1_1());
		assertNotNull("testCase31() findTargetUrlsDifferencesResponse.getResponses()[0].getH1_2() should be not null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getH1_2());
		String h1_1_json = new Gson().toJson(findTargetUrlsDifferencesResponse.getResponses()[0].getH1_1(), String[].class);
		String h1_2_json = new Gson().toJson(findTargetUrlsDifferencesResponse.getResponses()[0].getH1_2(), String[].class);
		assertEquals("testCase31() findTargetUrlsDifferencesResponse.getResponses()[0].getH1_1() incorrect.",
				"[\"Tailor Fit Relaxed Fit Cotton Duck Ottoman Slipcover\"]", String.valueOf(h1_1_json));
		assertEquals("testCase31() findTargetUrlsDifferencesResponse.getResponses()[0].getH1_2() incorrect.",
				"[\"Tailored Solutions Relaxed Fit Cotton Duck Ottoman Slipcover\"]", String.valueOf(h1_2_json));
		FormatUtils.getInstance().logMemoryUsage("testCase31() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase32() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase32() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-02-01 00:41:20";
		domainId1 = "4739";
		decodedUrl1 = "https://www.expedia.co.uk/Cheap-Flights-To-Almhult.d6271638.Travel-Guide-Flights";

		crawlTimestamp2 = "2020-02-03 14:25:42";
		domainId2 = "4739";
		decodedUrl2 = "https://www.expedia.co.uk/Cheap-Flights-To-Almhult.d6271638.Travel-Guide-Flights";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase32() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase32() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase32() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase32() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase32() findTargetUrlsDifferencesResponse.getResponses()[0].getH1_count_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getH1_count_chg_ind());
		assertNotNull("testCase32() findTargetUrlsDifferencesResponse.getResponses()[0].getH1_count_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getH1_count_1());
		assertNotNull("testCase32() findTargetUrlsDifferencesResponse.getResponses()[0].getH1_count_2() should be not null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getH1_count_2());
		assertEquals("testCase32() findTargetUrlsDifferencesResponse.getResponses()[0].getH1_count_1() incorrect.", "2",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getH1_count_1()));
		assertEquals("testCase32() findTargetUrlsDifferencesResponse.getResponses()[0].getH1_count_2() incorrect.", "1",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getH1_count_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase32() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase34() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase34() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-01-27 07:37:55";
		domainId1 = "185";
		decodedUrl1 = "https://www.overstock.com/Home-Garden/Tailor-Fit-Relaxed-Fit-Cotton-Duck-Ottoman-Slipcover/9517973/product.html";

		crawlTimestamp2 = "2020-01-31 10:02:46";
		domainId2 = "185";
		decodedUrl2 = "https://www.overstock.com/Home-Garden/Tailor-Fit-Relaxed-Fit-Cotton-Duck-Ottoman-Slipcover/9517973/product.html";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase34() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase34() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase34() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase34() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase34() findTargetUrlsDifferencesResponse.getResponses()[0].getH1_length_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getH1_length_chg_ind());
		assertNotNull("testCase34() findTargetUrlsDifferencesResponse.getResponses()[0].getH1_length_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getH1_length_1());
		assertNotNull("testCase34() findTargetUrlsDifferencesResponse.getResponses()[0].getH1_length_2() should be not null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getH1_length_2());
		assertEquals("testCase34() findTargetUrlsDifferencesResponse.getResponses()[0].getH1_length_1() incorrect.", "52",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getH1_length_1()));
		assertEquals("testCase34() findTargetUrlsDifferencesResponse.getResponses()[0].getH1_length_2() incorrect.", "60",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getH1_length_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase34() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase36() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase36() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-02-06 19:03:20";
		domainId1 = "185";
		decodedUrl1 = "https://www.overstock.com/Jewelry-Watches/Jewelry/Amber,/gemstone,/13/dept.html";

		crawlTimestamp2 = "2020-02-10 14:06:26";
		domainId2 = "185";
		decodedUrl2 = "https://www.overstock.com/Jewelry-Watches/Jewelry/Amber,/gemstone,/13/dept.html";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase36() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase36() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase36() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase36() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase36() findTargetUrlsDifferencesResponse.getResponses()[0].getH2_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getH2_chg_ind());
		assertNotNull("testCase36() findTargetUrlsDifferencesResponse.getResponses()[0].getH2_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getH2_1());
		assertNotNull("testCase36() findTargetUrlsDifferencesResponse.getResponses()[0].getH2_2() should be not null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getH2_2());
		String h2_1_json = new Gson().toJson(findTargetUrlsDifferencesResponse.getResponses()[0].getH2_1(), String[].class);
		String h2_2_json = new Gson().toJson(findTargetUrlsDifferencesResponse.getResponses()[0].getH2_2(), String[].class);
		assertEquals("testCase36() findTargetUrlsDifferencesResponse.getResponses()[0].getH2_1() incorrect.",
				"[\"Club O\",\"Price\",\"Metals\",\"Stones\",\"Gemstones\",\"Availability\"]", String.valueOf(h2_1_json));
		assertEquals("testCase36() findTargetUrlsDifferencesResponse.getResponses()[0].getH2_2() incorrect.",
				"[\"Price\",\"Metals\",\"Stones\",\"Gemstones\",\"Metal Colors\",\"Availability\"]", String.valueOf(h2_2_json));
		FormatUtils.getInstance().logMemoryUsage("testCase36() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase37() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase37() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2019-11-02 04:41:25";
		domainId1 = "8451";
		decodedUrl1 = "https://www.baqsimi.com/hcp/adult";

		crawlTimestamp2 = "2019-11-13 04:58:21";
		domainId2 = "8451";
		decodedUrl2 = "https://www.baqsimi.com/hcp/adult";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase37() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase37() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase37() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase37() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase37() findTargetUrlsDifferencesResponse.getResponses()[0].getHeader_noarchive_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getHeader_noarchive_chg_ind());
		assertNotNull("testCase37() findTargetUrlsDifferencesResponse.getResponses()[0].getHeader_noarchive_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getHeader_noarchive_1());
		assertNotNull("testCase37() findTargetUrlsDifferencesResponse.getResponses()[0].getHeader_noarchive_2() should be not null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getHeader_noarchive_2());
		assertEquals("testCase37() findTargetUrlsDifferencesResponse.getResponses()[0].getHeader_noarchive_1() incorrect.", "false",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getHeader_noarchive_1()));
		assertEquals("testCase37() findTargetUrlsDifferencesResponse.getResponses()[0].getHeader_noarchive_2() incorrect.", "true",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getHeader_noarchive_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase37() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase38() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase38() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2019-10-20 03:45:16";
		domainId1 = "7165";
		decodedUrl1 = "https://www.ebags.com/category/business-cases/attaches-and-briefcases/b/bosca/g/mens";

		crawlTimestamp2 = "2019-10-24 09:44:04";
		domainId2 = "7165";
		decodedUrl2 = "https://www.ebags.com/category/business-cases/attaches-and-briefcases/b/bosca/g/mens";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase38() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase38() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase38() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase38() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase38() findTargetUrlsDifferencesResponse.getResponses()[0].getHeader_noindex_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getHeader_noindex_chg_ind());
		assertNotNull("testCase38() findTargetUrlsDifferencesResponse.getResponses()[0].getHeader_noindex_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getHeader_noindex_1());
		assertNotNull("testCase38() findTargetUrlsDifferencesResponse.getResponses()[0].getHeader_noindex_2() should be not null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getHeader_noindex_2());
		assertEquals("testCase38() findTargetUrlsDifferencesResponse.getResponses()[0].getHeader_noindex_1() incorrect.", "true",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getHeader_noindex_1()));
		assertEquals("testCase38() findTargetUrlsDifferencesResponse.getResponses()[0].getHeader_noindex_2() incorrect.", "false",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getHeader_noindex_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase38() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase39() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase39() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2019-11-01 20:04:15";
		domainId1 = "765";
		decodedUrl1 = "https://www.grainger.com/category/safety/safety-storage/safety-cans-and-accessories/type-i-and-ii-safety-cans";

		crawlTimestamp2 = "2020-01-31 10:36:15";
		domainId2 = "765";
		decodedUrl2 = "https://www.grainger.com/category/safety/safety-storage/safety-cans-and-accessories/type-i-and-ii-safety-cans";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase39() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase39() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase39() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase39() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase39() findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_errors_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_errors_chg_ind());
		assertNotNull("testCase39() findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_errors_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_errors_1());
		assertNull("testCase39() findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_errors_2() should be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_errors_2());
		String hreflang_errors_1_json = new Gson().toJson(findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_errors_1(), HreflangErrors.class);
		assertEquals("testCase39() findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_errors_1() incorrect.",
				"{\"has_multiple_defaults\":{\"status\":false,\"urls\":[]},\"hreflang_keys_with_multiple_entries\":{\"status\":false,\"urls\":[]},\"invalid_languages\":{\"status\":false},\"invalid_regions\":{\"status\":false},\"languages_missing_standalone_entries\":{\"status\":false,\"urls\":[]},\"is_default\":false,\"is_self_reference\":false}",
				String.valueOf(hreflang_errors_1_json));
		FormatUtils.getInstance().logMemoryUsage("testCase39() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase40() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase40() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-01-28 00:33:09";
		domainId1 = "1897";
		decodedUrl1 = "https://www.abebooks.com/products/isbn/9780434890422";

		crawlTimestamp2 = "2020-01-31 14:33:27";
		domainId2 = "1897";
		decodedUrl2 = "https://www.abebooks.com/products/isbn/9780434890422";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase40() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase40() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase40() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase40() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase40() findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_links_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_links_chg_ind());
		assertNotNull("testCase40() findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_links_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_links_1());
		assertNotNull("testCase40() findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_links_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_links_2());
		String hreflang_errors_1_json = new Gson().toJson(findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_links_1(), HreflangLinks[].class);
		String hreflang_errors_2_json = new Gson().toJson(findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_links_2(), HreflangLinks[].class);
		//System.out.println("testCase40() hreflang_errors_1_json=" + hreflang_errors_1_json);
		//System.out.println("testCase40() hreflang_errors_2_json=" + hreflang_errors_2_json);
		assertEquals("testCase40() hreflang_errors_1_json incorrect.",
				"[{\"href\":\"https://www.abebooks.com/9780434890422/VOYAGER-FLYING-ADVENTURE-LIFETIME-Yeager-0434890421/plp\",\"lang\":\"en\",\"type\":\"ABSOLUTE\"},{\"href\":\"https://www.abebooks.co.uk/9780434890422/VOYAGER-FLYING-ADVENTURE-LIFETIME-Yeager-0434890421/plp\",\"lang\":\"en-GB\",\"type\":\"ABSOLUTE\"},{\"href\":\"https://www.abebooks.co.uk/9780434890422/VOYAGER-FLYING-ADVENTURE-LIFETIME-Yeager-0434890421/plp\",\"lang\":\"en-IE\",\"type\":\"ABSOLUTE\"},{\"href\":\"https://www.abebooks.de/9780434890422/VOYAGER-FLYING-ADVENTURE-LIFETIME-Yeager-0434890421/plp\",\"lang\":\"de\",\"type\":\"ABSOLUTE\"},{\"href\":\"https://www.abebooks.de/9780434890422/VOYAGER-FLYING-ADVENTURE-LIFETIME-Yeager-0434890421/plp\",\"lang\":\"de-DE\",\"type\":\"ABSOLUTE\"},{\"href\":\"https://www.abebooks.de/9780434890422/VOYAGER-FLYING-ADVENTURE-LIFETIME-Yeager-0434890421/plp\",\"lang\":\"de-LU\",\"type\":\"ABSOLUTE\"},{\"href\":\"https://www.abebooks.fr/9780434890422/VOYAGER-FLYING-ADVENTURE-LIFETIME-Yeager-0434890421/plp\",\"lang\":\"fr-FR\",\"type\":\"ABSOLUTE\"},{\"href\":\"https://www.abebooks.fr/9780434890422/VOYAGER-FLYING-ADVENTURE-LIFETIME-Yeager-0434890421/plp\",\"lang\":\"fr-BE\",\"type\":\"ABSOLUTE\"},{\"href\":\"https://www.abebooks.fr/9780434890422/VOYAGER-FLYING-ADVENTURE-LIFETIME-Yeager-0434890421/plp\",\"lang\":\"fr-CH\",\"type\":\"ABSOLUTE\"},{\"href\":\"https://www.abebooks.fr/9780434890422/VOYAGER-FLYING-ADVENTURE-LIFETIME-Yeager-0434890421/plp\",\"lang\":\"fr-LU\",\"type\":\"ABSOLUTE\"},{\"href\":\"https://www.abebooks.it/9780434890422/VOYAGER-FLYING-ADVENTURE-LIFETIME-Yeager-0434890421/plp\",\"lang\":\"it\",\"type\":\"ABSOLUTE\"},{\"href\":\"https://www.iberlibro.com/9780434890422/VOYAGER-FLYING-ADVENTURE-LIFETIME-Yeager-0434890421/plp\",\"lang\":\"es-ES\",\"type\":\"ABSOLUTE\"},{\"href\":\"https://www.iberlibro.com/9780434890422/VOYAGER-FLYING-ADVENTURE-LIFETIME-Yeager-0434890421/plp\",\"lang\":\"es-PE\",\"type\":\"ABSOLUTE\"},{\"href\":\"https://www.iberlibro.com/9780434890422/VOYAGER-FLYING-ADVENTURE-LIFETIME-Yeager-0434890421/plp\",\"lang\":\"es-EC\",\"type\":\"ABSOLUTE\"},{\"href\":\"https://www.iberlibro.com/9780434890422/VOYAGER-FLYING-ADVENTURE-LIFETIME-Yeager-0434890421/plp\",\"lang\":\"es-CL\",\"type\":\"ABSOLUTE\"},{\"href\":\"https://www.iberlibro.com/9780434890422/VOYAGER-FLYING-ADVENTURE-LIFETIME-Yeager-0434890421/plp\",\"lang\":\"es-AR\",\"type\":\"ABSOLUTE\"},{\"href\":\"https://www.iberlibro.com/9780434890422/VOYAGER-FLYING-ADVENTURE-LIFETIME-Yeager-0434890421/plp\",\"lang\":\"es-VE\",\"type\":\"ABSOLUTE\"},{\"href\":\"https://www.iberlibro.com/9780434890422/VOYAGER-FLYING-ADVENTURE-LIFETIME-Yeager-0434890421/plp\",\"lang\":\"es-CO\",\"type\":\"ABSOLUTE\"},{\"href\":\"https://www.iberlibro.com/9780434890422/VOYAGER-FLYING-ADVENTURE-LIFETIME-Yeager-0434890421/plp\",\"lang\":\"es-UY\",\"type\":\"ABSOLUTE\"}]",
				String.valueOf(hreflang_errors_1_json));
		assertEquals("testCase40() hreflang_errors_2_json incorrect.",
				"[{\"href\":\"https://www.abebooks.com/9780434890422/VOYAGER-FLYING-ADVENTURE-LIFETIME-Jeana-0434890421/plp\",\"lang\":\"en\",\"type\":\"ABSOLUTE\"},{\"href\":\"https://www.abebooks.co.uk/9780434890422/VOYAGER-FLYING-ADVENTURE-LIFETIME-Jeana-0434890421/plp\",\"lang\":\"en-GB\",\"type\":\"ABSOLUTE\"},{\"href\":\"https://www.abebooks.co.uk/9780434890422/VOYAGER-FLYING-ADVENTURE-LIFETIME-Jeana-0434890421/plp\",\"lang\":\"en-IE\",\"type\":\"ABSOLUTE\"},{\"href\":\"https://www.abebooks.de/9780434890422/VOYAGER-FLYING-ADVENTURE-LIFETIME-Jeana-0434890421/plp\",\"lang\":\"de\",\"type\":\"ABSOLUTE\"},{\"href\":\"https://www.abebooks.de/9780434890422/VOYAGER-FLYING-ADVENTURE-LIFETIME-Jeana-0434890421/plp\",\"lang\":\"de-DE\",\"type\":\"ABSOLUTE\"},{\"href\":\"https://www.abebooks.de/9780434890422/VOYAGER-FLYING-ADVENTURE-LIFETIME-Jeana-0434890421/plp\",\"lang\":\"de-LU\",\"type\":\"ABSOLUTE\"},{\"href\":\"https://www.abebooks.fr/9780434890422/VOYAGER-FLYING-ADVENTURE-LIFETIME-Jeana-0434890421/plp\",\"lang\":\"fr-FR\",\"type\":\"ABSOLUTE\"},{\"href\":\"https://www.abebooks.fr/9780434890422/VOYAGER-FLYING-ADVENTURE-LIFETIME-Jeana-0434890421/plp\",\"lang\":\"fr-BE\",\"type\":\"ABSOLUTE\"},{\"href\":\"https://www.abebooks.fr/9780434890422/VOYAGER-FLYING-ADVENTURE-LIFETIME-Jeana-0434890421/plp\",\"lang\":\"fr-CH\",\"type\":\"ABSOLUTE\"},{\"href\":\"https://www.abebooks.fr/9780434890422/VOYAGER-FLYING-ADVENTURE-LIFETIME-Jeana-0434890421/plp\",\"lang\":\"fr-LU\",\"type\":\"ABSOLUTE\"},{\"href\":\"https://www.abebooks.it/9780434890422/VOYAGER-FLYING-ADVENTURE-LIFETIME-Jeana-0434890421/plp\",\"lang\":\"it\",\"type\":\"ABSOLUTE\"},{\"href\":\"https://www.iberlibro.com/9780434890422/VOYAGER-FLYING-ADVENTURE-LIFETIME-Jeana-0434890421/plp\",\"lang\":\"es-ES\",\"type\":\"ABSOLUTE\"},{\"href\":\"https://www.iberlibro.com/9780434890422/VOYAGER-FLYING-ADVENTURE-LIFETIME-Jeana-0434890421/plp\",\"lang\":\"es-PE\",\"type\":\"ABSOLUTE\"},{\"href\":\"https://www.iberlibro.com/9780434890422/VOYAGER-FLYING-ADVENTURE-LIFETIME-Jeana-0434890421/plp\",\"lang\":\"es-EC\",\"type\":\"ABSOLUTE\"},{\"href\":\"https://www.iberlibro.com/9780434890422/VOYAGER-FLYING-ADVENTURE-LIFETIME-Jeana-0434890421/plp\",\"lang\":\"es-CL\",\"type\":\"ABSOLUTE\"},{\"href\":\"https://www.iberlibro.com/9780434890422/VOYAGER-FLYING-ADVENTURE-LIFETIME-Jeana-0434890421/plp\",\"lang\":\"es-AR\",\"type\":\"ABSOLUTE\"},{\"href\":\"https://www.iberlibro.com/9780434890422/VOYAGER-FLYING-ADVENTURE-LIFETIME-Jeana-0434890421/plp\",\"lang\":\"es-VE\",\"type\":\"ABSOLUTE\"},{\"href\":\"https://www.iberlibro.com/9780434890422/VOYAGER-FLYING-ADVENTURE-LIFETIME-Jeana-0434890421/plp\",\"lang\":\"es-CO\",\"type\":\"ABSOLUTE\"},{\"href\":\"https://www.iberlibro.com/9780434890422/VOYAGER-FLYING-ADVENTURE-LIFETIME-Jeana-0434890421/plp\",\"lang\":\"es-UY\",\"type\":\"ABSOLUTE\"}]",
				String.valueOf(hreflang_errors_2_json));
		FormatUtils.getInstance().logMemoryUsage("testCase40() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase41() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase41() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-01-30 08:05:03";
		domainId1 = "4729";
		decodedUrl1 = "https://www.expedia.nl/Grou-Hotels-Hotel-Met-WiFi.0-0-d6228628-tHotelmetWiFi.Reisgids-Filter-Hotels";

		crawlTimestamp2 = "2020-01-31 12:24:37";
		domainId2 = "4729";
		decodedUrl2 = "https://www.expedia.nl/Grou-Hotels-Hotel-Met-WiFi.0-0-d6228628-tHotelmetWiFi.Reisgids-Filter-Hotels";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase41() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase41() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase41() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase41() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase41() findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_links_out_count_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_links_out_count_chg_ind());
		assertNotNull("testCase41() findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_links_out_count_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_links_out_count_1());
		assertNotNull("testCase41() findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_links_out_count_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_links_out_count_2());
		assertEquals("testCase41() findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_links_out_count_1() incorrect.", "0",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_links_out_count_1()));
		assertEquals("testCase41() findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_links_out_count_2() incorrect.", "35",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_links_out_count_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase41() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase42() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase42() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2019-10-06 11:35:57";
		domainId1 = "4758";
		decodedUrl1 = "https://www.travelocity.com/Santa-Rita-Hotels.d3000390605.Travel-Guide-Hotels";

		crawlTimestamp2 = "2019-10-19 11:59:58";
		domainId2 = "4758";
		decodedUrl2 = "https://www.travelocity.com/Santa-Rita-Hotels.d3000390605.Travel-Guide-Hotels";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase42() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase42() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase42() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase42() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase42() findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_url_count_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_url_count_chg_ind());
		assertNotNull("testCase42() findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_url_count_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_url_count_1());
		assertNotNull("testCase42() findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_url_count_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_url_count_2());
		assertEquals("testCase42() findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_url_count_1() incorrect.", "1",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_url_count_1()));
		assertEquals("testCase42() findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_url_count_2() incorrect.", "3",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_url_count_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase42() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase43() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase43() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2019-12-14 00:26:52";
		domainId1 = "765";
		decodedUrl1 = "https://www.grainger.com/product/GTO-12ft-Double-Driveway-Gates-13X443";

		crawlTimestamp2 = "2020-01-31 22:55:17";
		domainId2 = "765";
		decodedUrl2 = "https://www.grainger.com/product/GTO-12ft-Double-Driveway-Gates-13X443";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase43() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase43() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase43() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase43() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase43() findTargetUrlsDifferencesResponse.getResponses()[0].getIndex_flg_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIndex_flg_chg_ind());
		assertNotNull("testCase43() findTargetUrlsDifferencesResponse.getResponses()[0].getIndex_flg_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIndex_flg_1());
		assertNotNull("testCase43() findTargetUrlsDifferencesResponse.getResponses()[0].getIndex_flg_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIndex_flg_2());
		assertEquals("testCase43() findTargetUrlsDifferencesResponse.getResponses()[0].getIndex_flg_1() incorrect.", "No",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getIndex_flg_1()));
		assertEquals("testCase43() findTargetUrlsDifferencesResponse.getResponses()[0].getIndex_flg_2() incorrect.", "Yes",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getIndex_flg_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase43() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase44() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase44() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-01-27 00:49:54";
		domainId1 = "475";
		decodedUrl1 = "https://www.amazon.com/s?rh=n%3A13397451%2Cp_89%3AFrigidaire%2Cp_n_special_merchandising_browse-bin%3A544734011";

		crawlTimestamp2 = "2020-01-31 22:22:50";
		domainId2 = "475";
		decodedUrl2 = "https://www.amazon.com/s?rh=n%3A13397451%2Cp_89%3AFrigidaire%2Cp_n_special_merchandising_browse-bin%3A544734011";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase44() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase44() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase44() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase44() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase44() findTargetUrlsDifferencesResponse.getResponses()[0].getIndexable_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIndexable_chg_ind());
		assertNotNull("testCase44() findTargetUrlsDifferencesResponse.getResponses()[0].getIndexable_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIndexable_1());
		assertNotNull("testCase44() findTargetUrlsDifferencesResponse.getResponses()[0].getIndexable_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIndexable_2());
		assertEquals("testCase44() findTargetUrlsDifferencesResponse.getResponses()[0].getIndexable_1() incorrect.", "false",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getIndexable_1()));
		assertEquals("testCase44() findTargetUrlsDifferencesResponse.getResponses()[0].getIndexable_2() incorrect.", "true",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getIndexable_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase44() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase45() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase45() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-01-17 02:54:25";
		domainId1 = "475";
		decodedUrl1 = "https://www.amazon.com/Cosmopolitan-1-year/dp/B000LXHJDM";

		crawlTimestamp2 = "2020-01-31 22:42:28";
		domainId2 = "475";
		decodedUrl2 = "https://www.amazon.com/Cosmopolitan-1-year/dp/B000LXHJDM";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase45() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase45() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase45() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase45() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase45() findTargetUrlsDifferencesResponse.getResponses()[0].getInsecure_resources_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getInsecure_resources_chg_ind());
		assertNotNull("testCase45() findTargetUrlsDifferencesResponse.getResponses()[0].getInsecure_resources_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getInsecure_resources_1());
		assertNull("testCase45() findTargetUrlsDifferencesResponse.getResponses()[0].getInsecure_resources_2() should be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getInsecure_resources_2());
		FormatUtils.getInstance().logMemoryUsage("testCase45() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase47() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase47() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-01-28 00:24:19";
		domainId1 = "475";
		decodedUrl1 = "https://www.amazon.com/Yoga/b?ie=UTF8&node=156485011";

		crawlTimestamp2 = "2020-01-31 23:37:13";
		domainId2 = "475";
		decodedUrl2 = "https://www.amazon.com/Yoga/b?ie=UTF8&node=156485011";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase47() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase47() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase47() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase47() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase47() findTargetUrlsDifferencesResponse.getResponses()[0].getMeta_charset_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getMeta_charset_chg_ind());
		assertNotNull("testCase47() findTargetUrlsDifferencesResponse.getResponses()[0].getMeta_charset_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getMeta_charset_1());
		assertNotNull("testCase47() findTargetUrlsDifferencesResponse.getResponses()[0].getMeta_charset_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getMeta_charset_2());
		assertEquals("testCase47() findTargetUrlsDifferencesResponse.getResponses()[0].getMeta_charset_1() incorrect.", "charset=UTF-8",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getMeta_charset_1()));
		assertEquals("testCase47() findTargetUrlsDifferencesResponse.getResponses()[0].getMeta_charset_2() incorrect.", "",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getMeta_charset_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase47() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase48() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase48() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-01-18 01:02:03";
		domainId1 = "475";
		decodedUrl1 = "https://www.amazon.com/Yoga/";

		crawlTimestamp2 = "2020-01-31 02:03:04";
		domainId2 = "475";
		decodedUrl2 = "https://www.amazon.com/Yoga/";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase48() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase48() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase48() findTargetUrlsDifferencesResponse json=" + json);
		assertEquals("testCase48() json is incorrect",
				"{\"responses\":[{\"sequence\":\"1\",\"crawl_timestamp_1\":\"2020-01-18 01:02:03\",\"domain_id_1\":\"475\",\"url_1\":\"https://www.amazon.com/Yoga/\",\"crawl_timestamp_2\":\"2020-01-31 02:03:04\",\"domain_id_2\":\"475\",\"url_2\":\"https://www.amazon.com/Yoga/\",\"error\":{\"sequence\":\"1\",\"error_code\":\"PCWS-000016\",\"error_message\":\"Record not available in clarityDB.\",\"crawl_timestamp\":\"2020-01-18 01:02:03\",\"domain_id\":\"475\",\"url\":\"https://www.amazon.com/Yoga/\"}}]}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase48() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase49() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase49() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2019-10-07 17:04:48";
		domainId1 = "4731";
		decodedUrl1 = "https://www.wotif.co.nz/Canberra-Hotels-Abode-Narrabundah.h12240384.Hotel-Information";

		crawlTimestamp2 = "2019-10-08 10:32:32";
		domainId2 = "4731";
		decodedUrl2 = "https://www.wotif.co.nz/Canberra-Hotels-Abode-Narrabundah.h12240384.Hotel-Information";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase49() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase49() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase49() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase49() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase49() findTargetUrlsDifferencesResponse.getResponses()[0].getMeta_content_type_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getMeta_content_type_chg_ind());
		assertNotNull("testCase49() findTargetUrlsDifferencesResponse.getResponses()[0].getMeta_content_type_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getMeta_content_type_1());
		assertNotNull("testCase49() findTargetUrlsDifferencesResponse.getResponses()[0].getMeta_content_type_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getMeta_content_type_2());
		assertEquals("testCase49() findTargetUrlsDifferencesResponse.getResponses()[0].getMeta_content_type_1() incorrect.", "text/html",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getMeta_content_type_1()));
		assertEquals("testCase49() findTargetUrlsDifferencesResponse.getResponses()[0].getMeta_content_type_2() incorrect.", "image/jpeg",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getMeta_content_type_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase49() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase50() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase50() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2019-10-04 11:23:02";
		domainId1 = "7691";
		decodedUrl1 = "https://us.boohoo.com/womens/vacation";

		crawlTimestamp2 = "2019-10-05 09:19:51";
		domainId2 = "7691";
		decodedUrl2 = "https://us.boohoo.com/womens/vacation";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase50() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase50() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase50() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase50() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase50() findTargetUrlsDifferencesResponse.getResponses()[0].getMixed_redirects_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getMixed_redirects_chg_ind());
		assertNotNull("testCase50() findTargetUrlsDifferencesResponse.getResponses()[0].getMixed_redirects_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getMixed_redirects_1());
		assertNull("testCase50() findTargetUrlsDifferencesResponse.getResponses()[0].getMixed_redirects_2() should be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getMixed_redirects_2());
		assertEquals("testCase50() findTargetUrlsDifferencesResponse.getResponses()[0].getMixed_redirects_1() incorrect.", "false",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getMixed_redirects_1()));
		FormatUtils.getInstance().logMemoryUsage("testCase50() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase51() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase51() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-01-14 05:36:55";
		domainId1 = "765";
		decodedUrl1 = "https://www.grainger.com/category/brand/CHEMSORB";

		crawlTimestamp2 = "2020-02-11 19:28:32";
		domainId2 = "765";
		decodedUrl2 = "https://www.grainger.com/category/brand/CHEMSORB";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase51() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase51() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase51() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase51() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase51() findTargetUrlsDifferencesResponse.getResponses()[0].getMobile_rel_alternate_url_is_consistent_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getMobile_rel_alternate_url_is_consistent_chg_ind());
		assertNotNull("testCase51() findTargetUrlsDifferencesResponse.getResponses()[0].getMobile_rel_alternate_url_is_consistent_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getMobile_rel_alternate_url_is_consistent_1());
		assertNotNull("testCase51() findTargetUrlsDifferencesResponse.getResponses()[0].getMobile_rel_alternate_url_is_consistent_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getMobile_rel_alternate_url_is_consistent_2());
		assertEquals("testCase51() findTargetUrlsDifferencesResponse.getResponses()[0].getMobile_rel_alternate_url_is_consistent_1() incorrect.", "true",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getMobile_rel_alternate_url_is_consistent_1()));
		assertEquals("testCase51() findTargetUrlsDifferencesResponse.getResponses()[0].getMobile_rel_alternate_url_is_consistent_2() incorrect.", "false",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getMobile_rel_alternate_url_is_consistent_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase51() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase52() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase52() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-01-28 00:24:19";
		domainId1 = "475";
		decodedUrl1 = "https://www.amazon.com/Yoga/b?ie=UTF8&node=156485011";

		crawlTimestamp2 = "2020-01-31 23:37:13";
		domainId2 = "475";
		decodedUrl2 = "https://www.amazon.com/Yoga/b?ie=UTF8&node=156485011";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase52() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase52() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase52() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase52() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase52() findTargetUrlsDifferencesResponse.getResponses()[0].getOg_markup_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getOg_markup_chg_ind());
		assertNotNull("testCase52() findTargetUrlsDifferencesResponse.getResponses()[0].getOg_markup_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getOg_markup_1());
		assertNull("testCase52() findTargetUrlsDifferencesResponse.getResponses()[0].getOg_markup_2() should not null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getOg_markup_2());
		FormatUtils.getInstance().logMemoryUsage("testCase52() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase54() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase54() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-01-30 11:22:42";
		domainId1 = "475";
		decodedUrl1 = "https://www.amazon.com/b?ie=UTF8&node=2237471011";

		crawlTimestamp2 = "2020-01-31 23:57:47";
		domainId2 = "475";
		decodedUrl2 = "https://www.amazon.com/b?ie=UTF8&node=2237471011";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase54() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase54() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase54() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase54() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase54() findTargetUrlsDifferencesResponse.getResponses()[0].getOg_markup_length_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getOg_markup_length_chg_ind());
		assertNotNull("testCase54() findTargetUrlsDifferencesResponse.getResponses()[0].getOg_markup_length_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getOg_markup_length_1());
		assertNotNull("testCase54() findTargetUrlsDifferencesResponse.getResponses()[0].getOg_markup_length_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getOg_markup_length_2());
		assertEquals("testCase54() findTargetUrlsDifferencesResponse.getResponses()[0].getOg_markup_length_1() incorrect.", "2",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getOg_markup_length_1()));
		assertEquals("testCase54() findTargetUrlsDifferencesResponse.getResponses()[0].getOg_markup_length_2() incorrect.", "0",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getOg_markup_length_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase54() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase55() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase55() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2019-12-30 07:57:40";
		domainId1 = "185";
		decodedUrl1 = "https://www.overstock.com/Home-Garden/Cambridge-Casual-Astoria-Outdoor-Shower/11535522/product.html";

		crawlTimestamp2 = "2020-01-17 07:42:15";
		domainId2 = "185";
		decodedUrl2 = "https://www.overstock.com/Home-Garden/Cambridge-Casual-Astoria-Outdoor-Shower/11535522/product.html";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase55() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase55() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase55() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase55() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase55() findTargetUrlsDifferencesResponse.getResponses()[0].getPage_link_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getPage_link_chg_ind());
		assertNotNull("testCase55() findTargetUrlsDifferencesResponse.getResponses()[0].getPage_link_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getPage_link_1());
		assertNotNull("testCase55() findTargetUrlsDifferencesResponse.getResponses()[0].getPage_link_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getPage_link_2());
		FormatUtils.getInstance().logMemoryUsage("testCase55() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase56() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase56() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-01-21 14:48:38";
		domainId1 = "6082";
		decodedUrl1 = "https://slicelife.com/restaurants/de/wilmington/19808/pizza-pro/menu";

		crawlTimestamp2 = "2020-02-01 04:43:16";
		domainId2 = "6082";
		decodedUrl2 = "https://slicelife.com/restaurants/de/wilmington/19808/pizza-pro/menu";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase56() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase56() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase56() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase56() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase56() findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_blocked_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_blocked_chg_ind());
		assertNull("testCase56() findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_blocked_1() should be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_blocked_1());
		assertNotNull("testCase56() findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_blocked_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_blocked_2());
		//assertEquals("testCase56() findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_blocked_1() incorrect.", "2",
		//		String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_blocked_1()));
		assertEquals("testCase56() findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_blocked_2() incorrect.", "false",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_blocked_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase56() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase57() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase57() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-01-28 01:21:51";
		domainId1 = "475";
		decodedUrl1 = "https://www.amazon.com/Fresh-Prepared-Salads/b?ie=UTF8&node=10771039011";

		crawlTimestamp2 = "2020-01-31 22:32:41";
		domainId2 = "475";
		decodedUrl2 = "https://www.amazon.com/Fresh-Prepared-Salads/b?ie=UTF8&node=10771039011";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase57() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase57() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase57() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase57() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase57() findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_chain_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_chain_chg_ind());
		assertNotNull("testCase57() findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_chain_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_chain_1());
		assertNotNull("testCase57() findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_chain_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_chain_2());
		//assertEquals("testCase57() findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_chain_1() incorrect.", "2",
		//		String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_chain_1()));
		//assertEquals("testCase57() findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_chain_2() incorrect.", "0",
		//		String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_chain_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase57() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase58() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase58() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2019-10-01 14:48:59";
		domainId1 = "5168";
		decodedUrl1 = "https://www.underarmour.fr/fr-fr/az";

		crawlTimestamp2 = "2019-11-22 05:43:53";
		domainId2 = "5168";
		decodedUrl2 = "https://www.underarmour.fr/fr-fr/az";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase58() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase58() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase58() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase58() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase58() findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_final_url_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_final_url_chg_ind());
		assertNotNull("testCase58() findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_final_url_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_final_url_1());
		assertNotNull("testCase58() findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_final_url_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_final_url_2());
		assertEquals("testCase58() findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_final_url_1() incorrect.", "https://www.underarmour.fr/fr-fr/az/",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_final_url_1()));
		assertEquals("testCase58() findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_final_url_2() incorrect.", "https://www.underarmour.fr/fr-fr/",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_final_url_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase58() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase60() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase60() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2019-11-28 03:39:27";
		domainId1 = "16";
		decodedUrl1 = "http://www.ross-simons.com/jewelry/watches/movado,ono/navigate.jsp";

		crawlTimestamp2 = "2019-12-21 03:24:06";
		domainId2 = "16";
		decodedUrl2 = "http://www.ross-simons.com/jewelry/watches/movado,ono/navigate.jsp";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase60() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase60() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase60() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase60() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase60() findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_times_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_times_chg_ind());
		assertNotNull("testCase60() findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_times_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_times_1());
		assertNotNull("testCase60() findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_times_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_times_2());
		assertEquals("testCase60() findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_times_1() incorrect.", "3",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_times_1()));
		assertEquals("testCase60() findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_times_2() incorrect.", "2",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_times_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase60() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase61() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase61() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-02-08 18:31:03";
		domainId1 = "185";
		decodedUrl1 = "https://www.overstock.com/Home-Garden/Acapulco-Papasan-Indoor-Outdoor-Patio-Lounge-Chair-Orange/19458972/product.html";

		crawlTimestamp2 = "2020-02-13 08:00:09";
		domainId2 = "185";
		decodedUrl2 = "https://www.overstock.com/Home-Garden/Acapulco-Papasan-Indoor-Outdoor-Patio-Lounge-Chair-Orange/19458972/product.html";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase61() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase61() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase61() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase61() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase61() findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_chg_ind());
		assertNotNull("testCase61() findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_1());
		assertNotNull("testCase61() findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_2());
		assertEquals("testCase61() findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_1() incorrect.", "200",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_1()));
		assertEquals("testCase61() findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_2() incorrect.", "301",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase61() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase62() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase62() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2019-11-26 11:18:06";
		domainId1 = "185";
		decodedUrl1 = "https://www.overstock.com/Electronics/PC-Gaming-Hardware/PlayStation-3,/platform-supported,/21005/subcat.html";

		crawlTimestamp2 = "2019-12-01 07:50:07";
		domainId2 = "185";
		decodedUrl2 = "https://www.overstock.com/Electronics/PC-Gaming-Hardware/PlayStation-3,/platform-supported,/21005/subcat.html";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase62() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase62() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase62() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase62() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase62() findTargetUrlsDifferencesResponse.getResponses()[0].getRobots_contents_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getRobots_contents_chg_ind());
		assertNotNull("testCase62() findTargetUrlsDifferencesResponse.getResponses()[0].getRobots_contents_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getRobots_contents_1());
		assertNotNull("testCase62() findTargetUrlsDifferencesResponse.getResponses()[0].getRobots_contents_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getRobots_contents_2());
		assertEquals("testCase62() findTargetUrlsDifferencesResponse.getResponses()[0].getRobots_contents_1() incorrect.", "noindex,nofollow",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getRobots_contents_1()));
		assertEquals("testCase62() findTargetUrlsDifferencesResponse.getResponses()[0].getRobots_contents_2() incorrect.", "index,follow",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getRobots_contents_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase62() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase64() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase64() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-01-27 03:22:08";
		domainId1 = "212";
		decodedUrl1 = "https://www.balsamhill.com/c/silver-and-gold-decorating-theme";

		crawlTimestamp2 = "2020-01-29 04:14:00";
		domainId2 = "212";
		decodedUrl2 = "https://www.balsamhill.com/c/silver-and-gold-decorating-theme";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase64() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase64() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase64() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase64() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase64() findTargetUrlsDifferencesResponse.getResponses()[0].getStructured_data_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getStructured_data_chg_ind());
		assertNotNull("testCase64() findTargetUrlsDifferencesResponse.getResponses()[0].getStructured_data_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getStructured_data_1());
		assertNotNull("testCase64() findTargetUrlsDifferencesResponse.getResponses()[0].getStructured_data_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getStructured_data_2());
		FormatUtils.getInstance().logMemoryUsage("testCase64() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase65() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase65() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-02-14 03:41:40";
		domainId1 = "220";
		decodedUrl1 = "https://quickbooks.intuit.com/ca/invoicing/";

		crawlTimestamp2 = "2020-02-15 05:00:57";
		domainId2 = "220";
		decodedUrl2 = "https://quickbooks.intuit.com/ca/invoicing/";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase65() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase65() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase65() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase65() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase65() findTargetUrlsDifferencesResponse.getResponses()[0].getTitle_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getTitle_chg_ind());
		assertNotNull("testCase65() findTargetUrlsDifferencesResponse.getResponses()[0].getTitle_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getTitle_1());
		assertNotNull("testCase65() findTargetUrlsDifferencesResponse.getResponses()[0].getTitle_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getTitle_2());
		assertEquals("testCase65() findTargetUrlsDifferencesResponse.getResponses()[0].getTitle_1() incorrect.",
				"Invoicing software that gets you paid | QuickBooks Canada", String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getTitle_1()));
		assertEquals("testCase65() findTargetUrlsDifferencesResponse.getResponses()[0].getTitle_2() incorrect.", "Accounting Software | QuickBooks Online",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getTitle_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase65() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase67() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase67() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2019-10-08 20:07:29";
		domainId1 = "4730";
		decodedUrl1 = "https://www.expedia.co.nz/Flights-To-Aberdeen.d6054175.Travel-Guide-Flights";

		crawlTimestamp2 = "2019-10-19 00:23:57";
		domainId2 = "4730";
		decodedUrl2 = "https://www.expedia.co.nz/Flights-To-Aberdeen.d6054175.Travel-Guide-Flights";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase67() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase67() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase67() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase67() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase67() findTargetUrlsDifferencesResponse.getResponses()[0].getTitle_length_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getTitle_length_chg_ind());
		assertNotNull("testCase67() findTargetUrlsDifferencesResponse.getResponses()[0].getTitle_length_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getTitle_length_1());
		assertNotNull("testCase67() findTargetUrlsDifferencesResponse.getResponses()[0].getTitle_length_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getTitle_length_2());
		assertEquals("testCase67() findTargetUrlsDifferencesResponse.getResponses()[0].getTitle_length_1() incorrect.", "76",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getTitle_length_1()));
		assertEquals("testCase67() findTargetUrlsDifferencesResponse.getResponses()[0].getTitle_length_2() incorrect.", "74",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getTitle_length_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase67() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase70() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase70() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-01-28 00:24:19";
		domainId1 = "475";
		decodedUrl1 = "https://www.amazon.com/Yoga/b?ie=UTF8&node=156485011";

		crawlTimestamp2 = "2020-01-31 23:37:13";
		domainId2 = "475";
		decodedUrl2 = "https://www.amazon.com/Yoga/b?ie=UTF8&node=156485011";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase70() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase70() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase70() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase70() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase70() findTargetUrlsDifferencesResponse.getResponses()[0].getViewport_content_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getViewport_content_chg_ind());
		assertNotNull("testCase70() findTargetUrlsDifferencesResponse.getResponses()[0].getViewport_content_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getViewport_content_1());
		assertNotNull("testCase70() findTargetUrlsDifferencesResponse.getResponses()[0].getViewport_content_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getViewport_content_2());
		assertEquals("testCase70() findTargetUrlsDifferencesResponse.getResponses()[0].getViewport_content_1() incorrect.", "",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getViewport_content_1()));
		assertEquals("testCase70() findTargetUrlsDifferencesResponse.getResponses()[0].getViewport_content_2() incorrect.", "width=device-width",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getViewport_content_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase70() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase72() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase72() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2019-12-25 06:15:35";
		domainId1 = "4762";
		decodedUrl1 = "https://www.expedia.ca/New-York-Hotels-Best-Western-Plus-Hospitality-House.h888641.Hotel-Information";

		crawlTimestamp2 = "2019-12-26 05:21:40";
		domainId2 = "4762";
		decodedUrl2 = "https://www.expedia.ca/New-York-Hotels-Best-Western-Plus-Hospitality-House.h888641.Hotel-Information";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase72() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase72() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase72() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase72() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase72() findTargetUrlsDifferencesResponse.getResponses()[0].getCanonical_added_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getCanonical_added_ind());
		assertNotNull("testCase72() findTargetUrlsDifferencesResponse.getCanonical_flg_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getCanonical_flg_1());
		assertNotNull("testCase72() findTargetUrlsDifferencesResponse.getCanonical_flg_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getCanonical_flg_2());
		assertEquals("testCase72() findTargetUrlsDifferencesResponse.getResponses()[0].getCanonical_flg_1() incorrect.", "No",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getCanonical_flg_1()));
		assertEquals("testCase72() findTargetUrlsDifferencesResponse.getResponses()[0].getCanonical_flg_2() incorrect.", "Yes",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getCanonical_flg_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase72() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase73() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase73() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-01-28 12:39:04";
		domainId1 = "475";
		decodedUrl1 = "https://www.amazon.com/Planes-Blu-ray-DVD-Digital-Copy/dp/B00BEIYMTW";

		crawlTimestamp2 = "2020-02-15 09:53:29";
		domainId2 = "475";
		decodedUrl2 = "https://www.amazon.com/Planes-Blu-ray-DVD-Digital-Copy/dp/B00BEIYMTW";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase73() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase73() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase73() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase73() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase73() findTargetUrlsDifferencesResponse.getResponses()[0].getCanonical_removed_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getCanonical_removed_ind());
		assertNotNull("testCase73() findTargetUrlsDifferencesResponse.getCanonical_flg_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getCanonical_flg_1());
		assertNotNull("testCase73() findTargetUrlsDifferencesResponse.getCanonical_flg_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getCanonical_flg_2());
		assertEquals("testCase73() findTargetUrlsDifferencesResponse.getResponses()[0].getCanonical_flg_1() incorrect.", "Yes",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getCanonical_flg_1()));
		assertEquals("testCase73() findTargetUrlsDifferencesResponse.getResponses()[0].getCanonical_flg_2() incorrect.", "No",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getCanonical_flg_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase73() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase74() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase74() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-02-04 03:35:20";
		domainId1 = "220";
		decodedUrl1 = "https://quickbooks.intuit.com/ca/resources/cash-flow/how-free-affects-your-business/";

		crawlTimestamp2 = "2020-02-06 04:03:21";
		domainId2 = "220";
		decodedUrl2 = "https://quickbooks.intuit.com/ca/resources/cash-flow/how-free-affects-your-business/";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase74() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase74() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase74() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase74() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase74() findTargetUrlsDifferencesResponse.getResponses()[0].getDescription_added_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getDescription_added_ind());
		assertNotNull("testCase74() findTargetUrlsDifferencesResponse.getDescription_flg_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getDescription_flg_1());
		assertNotNull("testCase74() findTargetUrlsDifferencesResponse.getDescription_flg_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getDescription_flg_2());
		assertEquals("testCase74() findTargetUrlsDifferencesResponse.getResponses()[0].getDescription_flg_1() incorrect.", "No",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getDescription_flg_1()));
		assertEquals("testCase74() findTargetUrlsDifferencesResponse.getResponses()[0].getDescription_flg_2() incorrect.", "Yes",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getDescription_flg_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase74() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase75() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase75() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-01-18 09:19:59";
		domainId1 = "263";
		decodedUrl1 = "https://www.retailmenot.com/view/popeyes.com";

		crawlTimestamp2 = "2020-01-19 06:36:46";
		domainId2 = "263";
		decodedUrl2 = "https://www.retailmenot.com/view/popeyes.com";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase75() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase75() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase75() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase75() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase75() findTargetUrlsDifferencesResponse.getResponses()[0].getDescription_removed_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getDescription_removed_ind());
		assertNotNull("testCase75() findTargetUrlsDifferencesResponse.getDescription_flg_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getDescription_flg_1());
		assertNotNull("testCase75() findTargetUrlsDifferencesResponse.getDescription_flg_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getDescription_flg_2());
		assertEquals("testCase75() findTargetUrlsDifferencesResponse.getResponses()[0].getDescription_flg_1() incorrect.", "Yes",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getDescription_flg_1()));
		assertEquals("testCase75() findTargetUrlsDifferencesResponse.getResponses()[0].getDescription_flg_2() incorrect.", "No",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getDescription_flg_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase75() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase76() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase76() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-01-01 04:52:17";
		domainId1 = "185";
		decodedUrl1 = "https://www.overstock.com/Luggage-Bags/Luggage-Sets/Timberland,/brand,/697/cat.html";

		crawlTimestamp2 = "2020-01-10 01:55:33";
		domainId2 = "185";
		decodedUrl2 = "https://www.overstock.com/Luggage-Bags/Luggage-Sets/Timberland,/brand,/697/cat.html";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase76() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase76() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase76() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase76() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase76() findTargetUrlsDifferencesResponse.getResponses()[0].getH1_added_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getH1_added_ind());
		assertNull("testCase76() findTargetUrlsDifferencesResponse.getH1_count_1() should be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getH1_count_1());
		assertNotNull("testCase76() findTargetUrlsDifferencesResponse.getH1_count_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getH1_count_2());
		assertEquals("testCase76() findTargetUrlsDifferencesResponse.getResponses()[0].getH1_count_2() incorrect.", "1",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getH1_count_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase76() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase77() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase77() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-02-07 08:27:56";
		domainId1 = "185";
		decodedUrl1 = "https://www.overstock.com/Sports-Toys/Baby-Toys/Tiny-Love,/brand,/3289/cat.html";

		crawlTimestamp2 = "2020-02-11 00:06:00";
		domainId2 = "185";
		decodedUrl2 = "https://www.overstock.com/Sports-Toys/Baby-Toys/Tiny-Love,/brand,/3289/cat.html";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase77() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase77() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase77() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase77() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase77() findTargetUrlsDifferencesResponse.getResponses()[0].getH1_removed_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getH1_removed_ind());
		assertNotNull("testCase77() findTargetUrlsDifferencesResponse.getH1_count_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getH1_count_1());
		assertNull("testCase77() findTargetUrlsDifferencesResponse.getH1_count_2() should be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getH1_count_2());
		assertEquals("testCase77() findTargetUrlsDifferencesResponse.getResponses()[0].getH1_count_1() incorrect.", "1",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getH1_count_1()));
		FormatUtils.getInstance().logMemoryUsage("testCase77() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase78() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase78() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-02-01 08:02:04";
		domainId1 = "185";
		decodedUrl1 = "https://www.overstock.com/Crafts-Sewing/Projectors/27482/subcat.html";

		crawlTimestamp2 = "2020-02-04 08:19:01";
		domainId2 = "185";
		decodedUrl2 = "https://www.overstock.com/Crafts-Sewing/Projectors/27482/subcat.html";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase78() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase78() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase78() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase78() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase78() findTargetUrlsDifferencesResponse.getResponses()[0].getH2_added_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getH2_added_ind());
		assertNotNull("testCase78() findTargetUrlsDifferencesResponse.getH2_1() should not be null.", findTargetUrlsDifferencesResponse.getResponses()[0].getH2_1());
		assertNotNull("testCase78() findTargetUrlsDifferencesResponse.getH2_2() should not be null.", findTargetUrlsDifferencesResponse.getResponses()[0].getH2_2());
		FormatUtils.getInstance().logMemoryUsage("testCase78() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase79() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase79() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-01-29 02:51:27";
		domainId1 = "185";
		decodedUrl1 = "https://www.overstock.com/Sports-Toys/Other-Collectibles/Corona,/brand,/19643/subcat.html";

		crawlTimestamp2 = "2020-02-01 02:10:48";
		domainId2 = "185";
		decodedUrl2 = "https://www.overstock.com/Sports-Toys/Other-Collectibles/Corona,/brand,/19643/subcat.html";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase79() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase79() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase79() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase79() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase79() findTargetUrlsDifferencesResponse.getResponses()[0].getH2_removed_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getH2_removed_ind());
		assertNotNull("testCase79() findTargetUrlsDifferencesResponse.getH2_1() should not be null.", findTargetUrlsDifferencesResponse.getResponses()[0].getH2_1());
		assertNotNull("testCase79() findTargetUrlsDifferencesResponse.getH2_2() should not be null.", findTargetUrlsDifferencesResponse.getResponses()[0].getH2_2());
		FormatUtils.getInstance().logMemoryUsage("testCase79() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase80() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase80() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-01-08 07:01:06";
		domainId1 = "4739";
		decodedUrl1 = "https://www.expedia.co.uk/Page-Hotels-Best-Western-View-Of-Lake-Powell-Hotel.h6422793.Hotel-Information";

		crawlTimestamp2 = "2020-01-10 07:17:52";
		domainId2 = "4739";
		decodedUrl2 = "https://www.expedia.co.uk/Page-Hotels-Best-Western-View-Of-Lake-Powell-Hotel.h6422793.Hotel-Information";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase80() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase80() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase80() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase80() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase80() findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_links_added_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_links_added_ind());
		assertNotNull("testCase80() findTargetUrlsDifferencesResponse.getHreflang_url_count_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_url_count_1());
		assertNotNull("testCase80() findTargetUrlsDifferencesResponse.getHreflang_url_count_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_url_count_2());
		assertEquals("testCase80() findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_url_count_1() incorrect.", "1",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_url_count_1()));
		assertEquals("testCase80() findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_url_count_2() incorrect.", "44",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_url_count_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase80() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase81() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase81() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2019-12-04 05:24:36";
		domainId1 = "185";
		decodedUrl1 = "https://www.overstock.com/black-friday";

		crawlTimestamp2 = "2019-12-15 02:31:19";
		domainId2 = "185";
		decodedUrl2 = "https://www.overstock.com/black-friday";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase81() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase81() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase81() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase81() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase81() findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_links_removed_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_links_removed_ind());
		assertNotNull("testCase81() findTargetUrlsDifferencesResponse.getHreflang_url_count_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_url_count_1());
		assertNotNull("testCase81() findTargetUrlsDifferencesResponse.getHreflang_url_count_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_url_count_2());
		assertEquals("testCase81() findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_url_count_1() incorrect.", "2",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_url_count_1()));
		assertEquals("testCase81() findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_url_count_2() incorrect.", "1",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getHreflang_url_count_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase81() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase82() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase82() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-01-11 08:48:44";
		domainId1 = "4739";
		decodedUrl1 = "https://www.expedia.co.uk/Zakynthos-Hotels-Lemon-Garden.h13270185.Hotel-Information";

		crawlTimestamp2 = "2020-01-17 16:10:57";
		domainId2 = "4739";
		decodedUrl2 = "https://www.expedia.co.uk/Zakynthos-Hotels-Lemon-Garden.h13270185.Hotel-Information";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase82() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase82() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase82() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase82() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase82() findTargetUrlsDifferencesResponse.getResponses()[0].getOpen_graph_added_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getOpen_graph_added_ind());
		assertNotNull("testCase82() findTargetUrlsDifferencesResponse.getOg_markup_flag_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getOg_markup_flag_1());
		assertNotNull("testCase82() findTargetUrlsDifferencesResponse.getOg_markup_flag_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getOg_markup_flag_2());
		assertEquals("testCase82() findTargetUrlsDifferencesResponse.getResponses()[0].getOg_markup_flag_1() incorrect.", "false",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getOg_markup_flag_1()));
		assertEquals("testCase82() findTargetUrlsDifferencesResponse.getResponses()[0].getOg_markup_flag_2() incorrect.", "true",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getOg_markup_flag_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase82() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase83() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase83() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-01-14 04:17:27";
		domainId1 = "220";
		decodedUrl1 = "https://quickbooks.intuit.com/ca/invoicing/";

		crawlTimestamp2 = "2020-02-01 03:22:27";
		domainId2 = "220";
		decodedUrl2 = "https://quickbooks.intuit.com/ca/invoicing/";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase83() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase83() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase83() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase83() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase83() findTargetUrlsDifferencesResponse.getResponses()[0].getOpen_graph_removed_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getOpen_graph_removed_ind());
		assertNotNull("testCase83() findTargetUrlsDifferencesResponse.getOg_markup_flag_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getOg_markup_flag_1());
		assertNotNull("testCase83() findTargetUrlsDifferencesResponse.getOg_markup_flag_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getOg_markup_flag_2());
		assertEquals("testCase83() findTargetUrlsDifferencesResponse.getResponses()[0].getOg_markup_flag_1() incorrect.", "true",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getOg_markup_flag_1()));
		assertEquals("testCase83() findTargetUrlsDifferencesResponse.getResponses()[0].getOg_markup_flag_2() incorrect.", "false",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getOg_markup_flag_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase83() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase84() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase84() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-01-24 14:01:02";
		domainId1 = "185";
		decodedUrl1 = "https://www.overstock.com/Home-Garden/Jennifer-Taylor-Katherine-Tufted-Accent-Chair/14415342/product.html";

		crawlTimestamp2 = "2020-01-28 00:39:33";
		domainId2 = "185";
		decodedUrl2 = "https://www.overstock.com/Home-Garden/Jennifer-Taylor-Katherine-Tufted-Accent-Chair/14415342/product.html";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase84() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase84() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase84() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase84() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase84() findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_301_detected_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_301_detected_ind());
		assertNotNull("testCase84() findTargetUrlsDifferencesResponse.getResponse_code_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_1());
		assertNotNull("testCase84() findTargetUrlsDifferencesResponse.getResponse_code_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_2());
		assertEquals("testCase84() findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_1() incorrect.", "200",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_1()));
		assertEquals("testCase84() findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_2() incorrect.", "301",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase84() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase85() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase85() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-01-28 05:19:51";
		domainId1 = "220";
		decodedUrl1 = "https://quickbooks.intuit.com/ca/resources/profit-loss/how-to-prepare-an-income-statement/";

		crawlTimestamp2 = "2020-02-04 04:35:56";
		domainId2 = "220";
		decodedUrl2 = "https://quickbooks.intuit.com/ca/resources/profit-loss/how-to-prepare-an-income-statement/";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase85() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase85() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase85() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase85() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase85() findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_301_removed_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_301_removed_ind());
		assertNotNull("testCase85() findTargetUrlsDifferencesResponse.getResponse_code_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_1());
		assertNotNull("testCase85() findTargetUrlsDifferencesResponse.getResponse_code_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_2());
		assertEquals("testCase85() findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_1() incorrect.", "301",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_1()));
		assertEquals("testCase85() findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_2() incorrect.", "200",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase85() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase86() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase86() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2019-12-08 17:19:26";
		domainId1 = "365";
		decodedUrl1 = "https://www.autotrader.com/car-dealers/Westerville+OH-43081/54914055/Roush+Honda+Used+Car+Company";

		crawlTimestamp2 = "2019-12-20 12:11:01";
		domainId2 = "365";
		decodedUrl2 = "https://www.autotrader.com/car-dealers/Westerville+OH-43081/54914055/Roush+Honda+Used+Car+Company";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase86() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase86() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase86() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase86() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase86() findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_302_detected_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_302_detected_ind());
		assertNotNull("testCase86() findTargetUrlsDifferencesResponse.getResponse_code_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_1());
		assertNotNull("testCase86() findTargetUrlsDifferencesResponse.getResponse_code_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_2());
		assertEquals("testCase86() findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_1() incorrect.", "200",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_1()));
		assertEquals("testCase86() findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_2() incorrect.", "302",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase86() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase87() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase87() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2019-10-04 20:38:34";
		domainId1 = "550";
		decodedUrl1 = "https://www.cheaptickets.com/events/performers/dave-matthews-band-tickets?page=3";

		crawlTimestamp2 = "2020-01-23 07:19:16";
		domainId2 = "550";
		decodedUrl2 = "https://www.cheaptickets.com/events/performers/dave-matthews-band-tickets?page=3";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase87() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase87() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase87() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase87() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase87() findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_302_removed_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_302_removed_ind());
		assertNotNull("testCase87() findTargetUrlsDifferencesResponse.getResponse_code_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_1());
		assertNotNull("testCase87() findTargetUrlsDifferencesResponse.getResponse_code_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_2());
		assertEquals("testCase87() findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_1() incorrect.", "302",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_1()));
		assertEquals("testCase87() findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_2() incorrect.", "200",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase87() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase88() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase88() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2019-10-31 13:10:55";
		domainId1 = "185";
		decodedUrl1 = "https://www.overstock.com/23040410/product.html";

		crawlTimestamp2 = "2020-01-08 10:52:16";
		domainId2 = "185";
		decodedUrl2 = "https://www.overstock.com/23040410/product.html";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase88() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase88() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase88() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase88() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase88() findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_diff_code_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getRedirect_diff_code_ind());
		assertNotNull("testCase88() findTargetUrlsDifferencesResponse.getResponse_code_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_1());
		assertNotNull("testCase88() findTargetUrlsDifferencesResponse.getResponse_code_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_2());
		assertEquals("testCase88() findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_1() incorrect.", "301",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_1()));
		assertEquals("testCase88() findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_2() incorrect.", "302",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getResponse_code_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase88() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase89() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase89() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-02-14 03:41:40";
		domainId1 = "220";
		decodedUrl1 = "https://quickbooks.intuit.com/ca/invoicing/";

		crawlTimestamp2 = "2020-02-15 05:00:57";
		domainId2 = "220";
		decodedUrl2 = "https://quickbooks.intuit.com/ca/invoicing/";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase89() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase89() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase89() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase89() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase89() findTargetUrlsDifferencesResponse.getResponses()[0].getRobots_added_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getRobots_added_ind());
		assertNotNull("testCase89() findTargetUrlsDifferencesResponse.getRobots_flg_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getRobots_flg_1());
		assertNotNull("testCase89() findTargetUrlsDifferencesResponse.getRobots_flg_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getRobots_flg_2());
		assertEquals("testCase89() findTargetUrlsDifferencesResponse.getResponses()[0].getRobots_flg_1() incorrect.", "No",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getRobots_flg_1()));
		assertEquals("testCase89() findTargetUrlsDifferencesResponse.getResponses()[0].getRobots_flg_2() incorrect.", "Yes",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getRobots_flg_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase89() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase90() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase90() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2019-12-31 08:16:13";
		domainId1 = "4739";
		decodedUrl1 = "https://www.expedia.co.uk/Sofia-Hotels-Nightingale-Hostel-And-Guesthouse.h18320995.Hotel-Information";

		crawlTimestamp2 = "2020-01-10 10:17:39";
		domainId2 = "4739";
		decodedUrl2 = "https://www.expedia.co.uk/Sofia-Hotels-Nightingale-Hostel-And-Guesthouse.h18320995.Hotel-Information";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase90() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase90() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase90() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase90() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase90() findTargetUrlsDifferencesResponse.getResponses()[0].getRobots_removed_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getRobots_removed_ind());
		assertNotNull("testCase90() findTargetUrlsDifferencesResponse.getRobots_flg_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getRobots_flg_1());
		assertNotNull("testCase90() findTargetUrlsDifferencesResponse.getRobots_flg_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getRobots_flg_2());
		assertEquals("testCase90() findTargetUrlsDifferencesResponse.getResponses()[0].getRobots_flg_1() incorrect.", "Yes",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getRobots_flg_1()));
		assertEquals("testCase90() findTargetUrlsDifferencesResponse.getResponses()[0].getRobots_flg_2() incorrect.", "No",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getRobots_flg_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase90() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase91() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase91() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2019-12-28 04:14:10";
		domainId1 = "1352";
		decodedUrl1 = "https://www.beacontechnologies.com/blog/2013/12/css-only-tabs/";

		crawlTimestamp2 = "2019-12-29 05:11:43";
		domainId2 = "1352";
		decodedUrl2 = "https://www.beacontechnologies.com/blog/2013/12/css-only-tabs/";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase91() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase91() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase91() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase91() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase91() findTargetUrlsDifferencesResponse.getResponses()[0].getTitle_added_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getTitle_added_ind());
		assertNotNull("testCase91() findTargetUrlsDifferencesResponse.getTitle_flg_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getTitle_flg_1());
		assertNotNull("testCase91() findTargetUrlsDifferencesResponse.getTitle_flg_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getTitle_flg_2());
		assertEquals("testCase91() findTargetUrlsDifferencesResponse.getResponses()[0].getTitle_flg_1() incorrect.", "No",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getTitle_flg_1()));
		assertEquals("testCase91() findTargetUrlsDifferencesResponse.getResponses()[0].getTitle_flg_2() incorrect.", "Yes",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getTitle_flg_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase91() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase92() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase92() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-01-18 09:19:59";
		domainId1 = "263";
		decodedUrl1 = "https://www.retailmenot.com/view/popeyes.com";

		crawlTimestamp2 = "2020-01-19 06:36:46";
		domainId2 = "263";
		decodedUrl2 = "https://www.retailmenot.com/view/popeyes.com";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase92() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase92() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase92() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase92() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase92() findTargetUrlsDifferencesResponse.getResponses()[0].getTitle_removed_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getTitle_removed_ind());
		assertNotNull("testCase92() findTargetUrlsDifferencesResponse.getTitle_flg_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getTitle_flg_1());
		assertNotNull("testCase92() findTargetUrlsDifferencesResponse.getTitle_flg_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getTitle_flg_2());
		assertEquals("testCase92() findTargetUrlsDifferencesResponse.getResponses()[0].getTitle_flg_1() incorrect.", "Yes",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getTitle_flg_1()));
		assertEquals("testCase92() findTargetUrlsDifferencesResponse.getResponses()[0].getTitle_flg_2() incorrect.", "No",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getTitle_flg_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase92() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase93() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase93() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2019-12-22 08:38:01";
		domainId1 = "475";
		decodedUrl1 = "https://www.amazon.com/Automotive-Replacement-Air-Conditioning-Core-Repair-Kits/b?ie=UTF8&node=15723551";

		crawlTimestamp2 = "2019-12-30 23:38:07";
		domainId2 = "475";
		decodedUrl2 = "https://www.amazon.com/Automotive-Replacement-Air-Conditioning-Core-Repair-Kits/b?ie=UTF8&node=15723551";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase93() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase93() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase93() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase93() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase93() findTargetUrlsDifferencesResponse.getResponses()[0].getViewport_added_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getViewport_added_ind());
		assertNotNull("testCase93() findTargetUrlsDifferencesResponse.getViewport_flag_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getViewport_flag_1());
		assertNotNull("testCase93() findTargetUrlsDifferencesResponse.getViewport_flag_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getViewport_flag_2());
		assertEquals("testCase93() findTargetUrlsDifferencesResponse.getResponses()[0].getViewport_flag_1() incorrect.", "false",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getViewport_flag_1()));
		assertEquals("testCase93() findTargetUrlsDifferencesResponse.getResponses()[0].getViewport_flag_2() incorrect.", "true",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getViewport_flag_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase93() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase94() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase94() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2019-12-31 04:35:44";
		domainId1 = "4744";
		decodedUrl1 = "https://www.wotif.com/Palo-Alto-Hotels.d9355.Travel-Guide-Hotels";

		crawlTimestamp2 = "2020-01-02 08:03:03";
		domainId2 = "4744";
		decodedUrl2 = "https://www.wotif.com/Palo-Alto-Hotels.d9355.Travel-Guide-Hotels";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase94() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase94() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase94() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase94() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase94() findTargetUrlsDifferencesResponse.getResponses()[0].getViewport_removed_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getViewport_removed_ind());
		assertNotNull("testCase94() findTargetUrlsDifferencesResponse.getViewport_flag_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getViewport_flag_1());
		assertNotNull("testCase94() findTargetUrlsDifferencesResponse.getViewport_flag_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getViewport_flag_2());
		assertEquals("testCase94() findTargetUrlsDifferencesResponse.getResponses()[0].getViewport_flag_1() incorrect.", "true",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getViewport_flag_1()));
		assertEquals("testCase94() findTargetUrlsDifferencesResponse.getResponses()[0].getViewport_flag_2() incorrect.", "false",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getViewport_flag_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase94() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase95() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase95() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-01-17 06:07:56";
		domainId1 = "185";
		decodedUrl1 = "https://www.overstock.com/Home-Garden/Specialty-Bakeware/Wilton,/brand,/6443/subcat.html";

		crawlTimestamp2 = "2020-01-19 05:39:57";
		domainId2 = "185";
		decodedUrl2 = "https://www.overstock.com/Home-Garden/Specialty-Bakeware/Wilton,/brand,/6443/subcat.html";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase95() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase95() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase95() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase95() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertNotNull("testCase95() findTargetUrlsDifferencesResponse.getPage_analysis_results_chg_ind_json() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getPage_analysis_results_chg_ind_json());
		FormatUtils.getInstance().logMemoryUsage("testCase95() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase96() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase96() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl_1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl_2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-02-17 01:13:59";
		domainId1 = "475";
		decodedUrl_1 = "https://www.amazon.com/b?ie=UTF8&node=11971391";
		crawlTimestamp2 = "2020-02-19 12:54:29";
		domainId2 = "475";
		decodedUrl_2 = "https://www.amazon.com/b?ie=UTF8&node=11971391";
		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl_1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl_2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		// second request
		crawlTimestamp1 = "2020-01-30 11:58:58";
		domainId1 = "1897";
		decodedUrl_1 = "https://www.abebooks.com/products/isbn/9780883940945";
		crawlTimestamp2 = "2020-01-31 23:41:34";
		domainId2 = "1897";
		decodedUrl_2 = "https://www.abebooks.com/products/isbn/9780883940945";
		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl_1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl_2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase96() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase96() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase96() findTargetUrlsDifferencesResponse json=" + json);
		assertNull("testCase96() findTargetUrlsDifferencesResponse.getError() should be null.", findTargetUrlsDifferencesResponse.getError());

		assertEquals("testCase96() getResponses()[0].getSequence() should be 1.", "1",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getSequence()));
		assertTrue("testCase96() getResponses()[0].getIs_different() should be true.", findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase96() getResponses()[0].getPage_link_chg_ind() should be true.", findTargetUrlsDifferencesResponse.getResponses()[0].getPage_link_chg_ind());

		assertEquals("testCase96() getResponses()[1].getSequence() should be 2.", "2",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[1].getSequence()));
		assertTrue("testCase96() getResponses()[1].getIs_different() should be true.", findTargetUrlsDifferencesResponse.getResponses()[1].getIs_different());
		assertTrue("testCase96() getResponses()[1].getAlternate_links_chg_ind()should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[1].getAlternate_links_chg_ind());
		FormatUtils.getInstance().logMemoryUsage("testCase96() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase97() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase97() requestUrl=" + requestUrl);
		String requestParameters = IConstants.EMPTY_STRING;
		//System.out.println("testCase97() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase97() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase97() findTargetUrlsDifferencesResponse json=" + json);
		assertNotNull("testCase97() findTargetUrlsDifferencesResponse.getError() should not be null.", findTargetUrlsDifferencesResponse.getError());
		assertEquals("testCase97() json is incorrect.", "{\"error\":{\"error_code\":\"PCWS-000017\",\"error_message\":\"Request JSON is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase97() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase98() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase98() requestUrl=" + requestUrl);
		String requestParameters = "{\"requests\": \"https://www.orbitz.com/Green-Bay-Hotels-Baymont-By-Wyndham-Green-Bay.h40154.Hotel-Information\",\"user_agent\": \"ClarityBot-Expedia\",\"response_as_html\": false,\"html_in_json\": false}";
		//System.out.println("testCase98() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase98() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase98() findTargetUrlsDifferencesResponse json=" + json);
		assertNotNull("testCase98() findTargetUrlsDifferencesResponse.getError() should not be null.", findTargetUrlsDifferencesResponse.getError());
		assertEquals("testCase98() json is incorrect.", "PCWS-000003", findTargetUrlsDifferencesResponse.getError().getError_code());
		FormatUtils.getInstance().logMemoryUsage("testCase98() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase99() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase99() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		//crawlTimestamp1 = "2019-10-07";
		//domainId1 = "4731";
		//decodedUrl1 = "https://www.wotif.co.nz/Canberra-Hotels-Abode-Narrabundah.h12240384.Hotel-Information";

		crawlTimestamp2 = "2019-10-08 10:32:32";
		domainId2 = "4731";
		decodedUrl2 = "https://www.wotif.co.nz/Canberra-Hotels-Abode-Narrabundah.h12240384.Hotel-Information";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		//targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		//targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		//targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase99() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase99() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase99() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase99() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase99() findTargetUrlsDifferencesResponse.getResponses()[0].getMeta_content_type_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getMeta_content_type_chg_ind());
		assertNotNull("testCase99() findTargetUrlsDifferencesResponse.getResponses()[0].getMeta_content_type_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getMeta_content_type_1());
		assertNotNull("testCase99() findTargetUrlsDifferencesResponse.getResponses()[0].getMeta_content_type_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getMeta_content_type_2());
		assertEquals("testCase99() findTargetUrlsDifferencesResponse.getResponses()[0].getMeta_content_type_1() incorrect.", "text/html",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getMeta_content_type_1()));
		assertEquals("testCase99() findTargetUrlsDifferencesResponse.getResponses()[0].getMeta_content_type_2() incorrect.", "image/jpeg",
				String.valueOf(findTargetUrlsDifferencesResponse.getResponses()[0].getMeta_content_type_2()));
		FormatUtils.getInstance().logMemoryUsage("testCase99() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase100() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase100() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		//crawlTimestamp1 = "2019-10-07";
		//domainId1 = "4731";
		//decodedUrl1 = "https://www.wotif.co.nz/Canberra-Hotels-Abode-Narrabundah.h12240384.Hotel-Information";

		crawlTimestamp2 = "2018-10-08 10:32:32";
		domainId2 = "4731";
		decodedUrl2 = "https://www.wotif.co.nz/Canberra-Hotels-Abode-Narrabundah.h12240384.Hotel-Information";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		//targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		//targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		//targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase100() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase100() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase100() findTargetUrlsDifferencesResponse json=" + json);
		assertEquals("testCase100() json incorrect.",
				"{\"responses\":[{\"error\":{\"sequence\":\"1\",\"error_code\":\"PCWS-000018\",\"error_message\":\"Target URL 1 crawl timestamp cannot be determined.\"}}]}",
				String.valueOf(json));
		FormatUtils.getInstance().logMemoryUsage("testCase100() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase101() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase101() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		domainId2 = "2432";
		decodedUrl2 = "https://quickbooks.intuit.com/industry/non-profits/";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		//targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		//targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		//targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		//targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase101() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase101() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase101() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase101() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		//assertTrue("testCase101() findTargetUrlsDifferencesResponse.getResponses()[0].getContent_type_chg_ind() should be true.",
		//		findTargetUrlsDifferencesResponse.getResponses()[0].getContent_type_chg_ind());
		FormatUtils.getInstance().logMemoryUsage("testCase101() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase102() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase102() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		domainId2 = "2432";
		decodedUrl2 = "https://quickbooks.intuit.com/industry/non-profits/test/";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		//targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		//targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		//targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		//targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase102() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase102() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase102() findTargetUrlsDifferencesResponse json=" + json);
		FormatUtils.getInstance().logMemoryUsage("testCase102() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase103() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase103() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-02-17 01:13:59";
		domainId1 = "475";
		decodedUrl1 = "https://www.amazon.com/b?ie=UTF8&node=11971391";
		//crawlTimestamp2 = "2020-02-19 12:54:29";
		domainId2 = "475";
		decodedUrl2 = "https://www.amazon.com/b?ie=UTF8&node=11971391";
		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase103() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase103() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase103() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase14() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		//assertEquals("testCase103() findTargetUrlsDifferencesResponse.getResponses()[0].getCrawl_timestamp_2() is incorrect", "2020-02-28",
		//		findTargetUrlsDifferencesResponse.getResponses()[0].getCrawl_timestamp_2());
		FormatUtils.getInstance().logMemoryUsage("testCase103() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase105() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase105() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-06-03 15:09:54";
		domainId1 = "1701";
		decodedUrl1 = "http://downloads.seoclarity.net/extract/Off_Market_HDP_Legacy_20200603.html";
		crawlTimestamp2 = "2020-06-03 15:10:11";
		domainId2 = "1701";
		decodedUrl2 = "http://downloads.seoclarity.net/extract/Off_Market_HDP_Test2_20200603.html";
		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequest.setUrl_skip_domain_name_flg("test");
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase105() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase105() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase105() findTargetUrlsDifferencesResponse json=" + json);
		assertEquals("testCase105() findTargetUrlsDifferencesResponse.getResponses()[0].getError().getError_code() is incorrect", "PCWS-000029",
				findTargetUrlsDifferencesResponse.getResponses()[0].getError().getError_code());
		FormatUtils.getInstance().logMemoryUsage("testCase105() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase106() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase106() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-06-03 15:09:54";
		domainId1 = "1701";
		decodedUrl1 = "http://downloads.seoclarity.net/extract/Off_Market_HDP_Legacy_20200603.html";
		crawlTimestamp2 = "2020-06-03 15:10:11";
		domainId2 = "1701";
		decodedUrl2 = "http://downloads.seoclarity.net/extract/Off_Market_HDP_Test2_20200603.html";
		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequest.setText_case_insensitive_flg("test");
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase106() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase106() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase106() findTargetUrlsDifferencesResponse json=" + json);
		assertEquals("testCase106() findTargetUrlsDifferencesResponse.getResponses()[0].getError().getError_code() is incorrect", "PCWS-000030",
				findTargetUrlsDifferencesResponse.getResponses()[0].getError().getError_code());
		FormatUtils.getInstance().logMemoryUsage("testCase106() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase107() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_TARGET_URLS_DIFFERENCES;
		//System.out.println("testCase107() requestUrl=" + requestUrl);
		String crawlTimestamp1 = null;
		String domainId1 = null;
		String decodedUrl1 = null;
		String crawlTimestamp2 = null;
		String domainId2 = null;
		String decodedUrl2 = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequestArray = null;
		TargetUrlsDifferencesRequest targetUrlsDifferencesRequest = null;
		List<TargetUrlsDifferencesRequest> targetUrlsDifferencesRequestList = new ArrayList<TargetUrlsDifferencesRequest>();
		int sequence = 0;

		findTargetUrlsDifferencesRequest = new FindTargetUrlsDifferencesRequest();
		findTargetUrlsDifferencesRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		// first request
		crawlTimestamp1 = "2020-10-20 18:37:01";
		domainId1 = "256";
		decodedUrl1 = "https://www.seoclarity.net/see-it-in-action/";

		crawlTimestamp2 = "2020-10-19 17:06:14";
		domainId2 = "256";
		decodedUrl2 = "https://www.seoclarity.net/see-it-in-action/";

		sequence++;
		targetUrlsDifferencesRequest = new TargetUrlsDifferencesRequest();
		targetUrlsDifferencesRequest.setSequence(String.valueOf(sequence));
		targetUrlsDifferencesRequest.setCrawl_timestamp_1(crawlTimestamp1);
		targetUrlsDifferencesRequest.setDomain_id_1(domainId1);
		targetUrlsDifferencesRequest.setUrl_1(decodedUrl1);
		targetUrlsDifferencesRequest.setCrawl_timestamp_2(crawlTimestamp2);
		targetUrlsDifferencesRequest.setDomain_id_2(domainId2);
		targetUrlsDifferencesRequest.setUrl_2(decodedUrl2);
		targetUrlsDifferencesRequestList.add(targetUrlsDifferencesRequest);

		targetUrlsDifferencesRequestArray = targetUrlsDifferencesRequestList.toArray(new TargetUrlsDifferencesRequest[0]);

		findTargetUrlsDifferencesRequest.setRequests(targetUrlsDifferencesRequestArray);
		String requestParameters = new Gson().toJson(findTargetUrlsDifferencesRequest, FindTargetUrlsDifferencesRequest.class);
		//System.out.println("testCase107() requestParameters=" + requestParameters);
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = mainWebServiceClientService.findTargetUrlsDifferences(null, requestUrl,
				requestParameters);
		assertNotNull("testCase107() findTargetUrlsDifferencesResponse should not be null.", findTargetUrlsDifferencesResponse);
		String json = new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class);
		//System.out.println("testCase107() findTargetUrlsDifferencesResponse json=" + json);
		assertTrue("testCase107() findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getIs_different());
		assertTrue("testCase107() findTargetUrlsDifferencesResponse.getResponses()[0].getCustom_data_chg_ind() should be true.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getCustom_data_chg_ind());
		assertNotNull("testCase107() findTargetUrlsDifferencesResponse.getResponses()[0].getCustom_data_1() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getCustom_data_1());
		assertNotNull("testCase107() findTargetUrlsDifferencesResponse.getResponses()[0].getCustom_data_2() should not be null.",
				findTargetUrlsDifferencesResponse.getResponses()[0].getCustom_data_2());
		FormatUtils.getInstance().logMemoryUsage("testCase107() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

}