package com.actonia.test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import java.util.ArrayList;
import java.util.List;

import com.actonia.IConstants;
import com.actonia.service.AccessTokenService;
import com.actonia.service.PoliteCrawlWebServiceClientService;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.AreaDateValue;
import com.actonia.value.object.MarketMatchingRequest;
import com.actonia.value.object.MarketMatchingResponse;
import com.google.gson.Gson;

public class MarketMatchingResourceTest {

	private PoliteCrawlWebServiceClientService politeCrawlWebServiceClientService;

	public MarketMatchingResourceTest() {
		super();
		this.politeCrawlWebServiceClientService = SpringBeanFactory.getBean("politeCrawlWebServiceClientService");
	}

	public static void main(String[] args) throws Exception {
		new MarketMatchingResourceTest().runTests();
	}

	private void runTests() throws Exception {
		testCase1();
		testCase2();
		testCase3();
		testCase4();
		testCase5();
		testCase6();
		testCase7();
		testCase8();
		testCase9();
		testCase10();
		testCase11();
		testCase12();
		testCase13();
		testCase14();
		testCase15();
		testCase16();
		testCase17();
		testCase18();
		testCase19();
		testCase20();
		testCase21();
		testCase22();
		testCase23(); // validateAreaDateValueArray() MSG_CD_MARKET_MATCHING_REQUEST_PARM_DATE_ARRAY_INVALID 1
		testCase24(); // validateAreaDateValueArray() MSG_CD_MARKET_MATCHING_REQUEST_PARM_DATE_ARRAY_INVALID 1
		testCase25(); // validateAreaDateValueArray() MSG_CD_MARKET_MATCHING_REQUEST_PARM_DATE_ARRAY_INVALID 1
		testCase26(); // validateDateArray() area=AREA1,MSG_CD_MARKET_MATCHING_REQUEST_PARM_DATE_ARRAY_INVALID 3
		testCase27(); // validateAreaDateValueArray() MSG_CD_MARKET_MATCHING_REQUEST_PARM_DATE_ARRAY_INVALID 1
		testCase28(); // validateDateArray() area=AREA1,MSG_CD_MARKET_MATCHING_REQUEST_PARM_DATE_ARRAY_INVALID 4
		testCase29(); // validateDateArray() area=AREA1,MSG_CD_MARKET_MATCHING_REQUEST_PARM_DATE_ARRAY_INVALID 3
		testCase30();
		testCase31();
		testCase32();
		testCase33();
		testCase34();
		testCase35();
		testCase36();
	}

	private MarketMatchingRequest getMarketMatchingRequest35() {
		MarketMatchingRequest marketMatchingRequest = new MarketMatchingRequest();
		marketMatchingRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		marketMatchingRequest.setTest_market("CPH");
		marketMatchingRequest.setPre_period_start_date("2014-01-01");
		marketMatchingRequest.setPre_period_end_date("2014-10-01");
		marketMatchingRequest.setPost_period_start_date("2014-10-02");
		marketMatchingRequest.setPost_period_end_date("2014-12-31");
		marketMatchingRequest.setNumber_of_best_matches(5);

		List<AreaDateValue> areaDateValueList = new ArrayList<AreaDateValue>();
		AreaDateValue areaDateValue = null;
		String[] areaArray = getAreaArrayForMarketMatching35();
		String[] dateArray = getDateArrayForMarketMatching35();
		int[] integerArray = getIntArrayForMarketMatching35();
		Double testDouble = null;
		for (int i = 0; i < integerArray.length; i++) {
			areaDateValue = new AreaDateValue();
			areaDateValue.setArea(areaArray[i]);
			areaDateValue.setDate(dateArray[i]);
			testDouble = new Double(integerArray[i]);
			areaDateValue.setValue(testDouble);
			areaDateValueList.add(areaDateValue);
		}
		marketMatchingRequest.setArea_date_value_array(areaDateValueList.toArray(new AreaDateValue[0]));
		return marketMatchingRequest;
	}

	private MarketMatchingRequest getMarketMatchingRequest36() {
		MarketMatchingRequest marketMatchingRequest = new MarketMatchingRequest();
		marketMatchingRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		marketMatchingRequest.setTest_market("CPH");
		marketMatchingRequest.setPre_period_start_date("2014-01-01");
		marketMatchingRequest.setPre_period_end_date("2014-06-30");
		marketMatchingRequest.setPost_period_start_date("2014-07-01");
		marketMatchingRequest.setPost_period_end_date("2014-09-30");
		marketMatchingRequest.setNumber_of_best_matches(5);

		List<AreaDateValue> areaDateValueList = new ArrayList<AreaDateValue>();
		AreaDateValue areaDateValue = null;
		String[] areaArray = getAreaArrayForMarketMatching36();
		String[] dateArray = getDateArrayForMarketMatching36();
		int[] integerArray = getIntArrayForMarketMatching36();
		Double testDouble = null;
		for (int i = 0; i < integerArray.length; i++) {
			areaDateValue = new AreaDateValue();
			areaDateValue.setArea(areaArray[i]);
			areaDateValue.setDate(dateArray[i]);
			testDouble = new Double(integerArray[i]);
			areaDateValue.setValue(testDouble);
			areaDateValueList.add(areaDateValue);
		}
		marketMatchingRequest.setArea_date_value_array(areaDateValueList.toArray(new AreaDateValue[0]));
		return marketMatchingRequest;
	}

	private String[] getAreaArrayForMarketMatching35() {
		return new String[] { "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", };
	}

	private String[] getDateArrayForMarketMatching35() {
		return new String[] { "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04", "2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09",
				"2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14", "2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19",
				"2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24", "2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29",
				"2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03", "2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08",
				"2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13", "2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18",
				"2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23", "2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28",
				"2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05", "2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10",
				"2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15", "2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20",
				"2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25", "2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30",
				"2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04", "2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09",
				"2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14", "2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19",
				"2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24", "2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29",
				"2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04", "2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09",
				"2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14", "2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19",
				"2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24", "2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29",
				"2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03", "2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08",
				"2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13", "2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18",
				"2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23", "2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28",
				"2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03", "2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08",
				"2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13", "2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18",
				"2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23", "2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28",
				"2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02", "2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07",
				"2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12", "2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17",
				"2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22", "2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27",
				"2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01", "2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06",
				"2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11", "2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16",
				"2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21", "2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26",
				"2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01", "2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06",
				"2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11", "2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16",
				"2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21", "2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26",
				"2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31", "2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05",
				"2014-11-06", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10", "2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15",
				"2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-20", "2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25",
				"2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-11-30", "2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05",
				"2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10", "2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15",
				"2014-12-16", "2014-12-17", "2014-12-18", "2014-12-19", "2014-12-20", "2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25",
				"2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30", "2014-12-31", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04",
				"2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09", "2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14",
				"2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19", "2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24",
				"2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29", "2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03",
				"2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08", "2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13",
				"2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18", "2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23",
				"2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28", "2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05",
				"2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10", "2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15",
				"2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20", "2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25",
				"2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30", "2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04",
				"2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09", "2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14",
				"2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19", "2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24",
				"2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29", "2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04",
				"2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09", "2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14",
				"2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19", "2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24",
				"2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29", "2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03",
				"2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08", "2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13",
				"2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18", "2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23",
				"2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28", "2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03",
				"2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08", "2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13",
				"2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18", "2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23",
				"2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28", "2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02",
				"2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07", "2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12",
				"2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17", "2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22",
				"2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27", "2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01",
				"2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06", "2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11",
				"2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16", "2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21",
				"2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26", "2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01",
				"2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06", "2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11",
				"2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16", "2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21",
				"2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26", "2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31",
				"2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05", "2014-11-06", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10",
				"2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15", "2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-20",
				"2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25", "2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-11-30",
				"2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05", "2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10",
				"2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15", "2014-12-16", "2014-12-17", "2014-12-18", "2014-12-19", "2014-12-20",
				"2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25", "2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30",
				"2014-12-31", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04", "2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09",
				"2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14", "2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19",
				"2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24", "2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29",
				"2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03", "2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08",
				"2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13", "2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18",
				"2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23", "2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28",
				"2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05", "2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10",
				"2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15", "2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20",
				"2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25", "2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30",
				"2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04", "2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09",
				"2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14", "2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19",
				"2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24", "2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29",
				"2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04", "2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09",
				"2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14", "2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19",
				"2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24", "2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29",
				"2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03", "2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08",
				"2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13", "2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18",
				"2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23", "2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28",
				"2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03", "2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08",
				"2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13", "2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18",
				"2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23", "2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28",
				"2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02", "2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07",
				"2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12", "2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17",
				"2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22", "2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27",
				"2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01", "2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06",
				"2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11", "2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16",
				"2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21", "2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26",
				"2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01", "2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06",
				"2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11", "2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16",
				"2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21", "2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26",
				"2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31", "2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05",
				"2014-11-06", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10", "2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15",
				"2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-20", "2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25",
				"2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-11-30", "2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05",
				"2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10", "2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15",
				"2014-12-16", "2014-12-17", "2014-12-18", "2014-12-19", "2014-12-20", "2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25",
				"2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30", "2014-12-31", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04",
				"2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09", "2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14",
				"2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19", "2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24",
				"2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29", "2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03",
				"2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08", "2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13",
				"2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18", "2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23",
				"2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28", "2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05",
				"2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10", "2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15",
				"2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20", "2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25",
				"2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30", "2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04",
				"2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09", "2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14",
				"2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19", "2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24",
				"2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29", "2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04",
				"2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09", "2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14",
				"2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19", "2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24",
				"2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29", "2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03",
				"2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08", "2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13",
				"2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18", "2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23",
				"2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28", "2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03",
				"2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08", "2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13",
				"2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18", "2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23",
				"2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28", "2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02",
				"2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07", "2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12",
				"2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17", "2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22",
				"2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27", "2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01",
				"2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06", "2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11",
				"2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16", "2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21",
				"2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26", "2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01",
				"2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06", "2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11",
				"2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16", "2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21",
				"2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26", "2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31",
				"2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05", "2014-11-06", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10",
				"2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15", "2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-20",
				"2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25", "2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-11-30",
				"2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05", "2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10",
				"2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15", "2014-12-16", "2014-12-17", "2014-12-18", "2014-12-19", "2014-12-20",
				"2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25", "2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30",
				"2014-12-31", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04", "2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09",
				"2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14", "2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19",
				"2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24", "2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29",
				"2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03", "2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08",
				"2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13", "2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18",
				"2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23", "2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28",
				"2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05", "2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10",
				"2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15", "2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20",
				"2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25", "2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30",
				"2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04", "2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09",
				"2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14", "2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19",
				"2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24", "2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29",
				"2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04", "2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09",
				"2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14", "2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19",
				"2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24", "2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29",
				"2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03", "2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08",
				"2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13", "2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18",
				"2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23", "2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28",
				"2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03", "2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08",
				"2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13", "2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18",
				"2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23", "2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28",
				"2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02", "2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07",
				"2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12", "2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17",
				"2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22", "2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27",
				"2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01", "2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06",
				"2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11", "2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16",
				"2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21", "2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26",
				"2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01", "2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06",
				"2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11", "2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16",
				"2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21", "2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26",
				"2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31", "2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05",
				"2014-11-06", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10", "2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15",
				"2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-20", "2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25",
				"2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-11-30", "2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05",
				"2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10", "2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15",
				"2014-12-16", "2014-12-17", "2014-12-18", "2014-12-19", "2014-12-20", "2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25",
				"2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30", "2014-12-31", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04",
				"2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09", "2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14",
				"2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19", "2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24",
				"2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29", "2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03",
				"2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08", "2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13",
				"2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18", "2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23",
				"2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28", "2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05",
				"2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10", "2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15",
				"2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20", "2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25",
				"2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30", "2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04",
				"2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09", "2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14",
				"2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19", "2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24",
				"2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29", "2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04",
				"2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09", "2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14",
				"2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19", "2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24",
				"2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29", "2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03",
				"2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08", "2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13",
				"2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18", "2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23",
				"2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28", "2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03",
				"2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08", "2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13",
				"2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18", "2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23",
				"2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28", "2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02",
				"2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07", "2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12",
				"2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17", "2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22",
				"2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27", "2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01",
				"2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06", "2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11",
				"2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16", "2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21",
				"2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26", "2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01",
				"2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06", "2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11",
				"2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16", "2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21",
				"2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26", "2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31",
				"2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05", "2014-11-06", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10",
				"2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15", "2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-20",
				"2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25", "2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-11-30",
				"2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05", "2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10",
				"2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15", "2014-12-16", "2014-12-17", "2014-12-18", "2014-12-19", "2014-12-20",
				"2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25", "2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30",
				"2014-12-31", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04", "2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09",
				"2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14", "2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19",
				"2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24", "2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29",
				"2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03", "2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08",
				"2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13", "2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18",
				"2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23", "2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28",
				"2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05", "2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10",
				"2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15", "2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20",
				"2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25", "2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30",
				"2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04", "2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09",
				"2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14", "2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19",
				"2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24", "2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29",
				"2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04", "2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09",
				"2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14", "2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19",
				"2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24", "2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29",
				"2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03", "2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08",
				"2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13", "2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18",
				"2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23", "2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28",
				"2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03", "2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08",
				"2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13", "2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18",
				"2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23", "2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28",
				"2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02", "2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07",
				"2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12", "2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17",
				"2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22", "2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27",
				"2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01", "2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06",
				"2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11", "2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16",
				"2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21", "2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26",
				"2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01", "2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06",
				"2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11", "2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16",
				"2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21", "2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26",
				"2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31", "2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05",
				"2014-11-06", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10", "2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15",
				"2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-20", "2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25",
				"2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-11-30", "2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05",
				"2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10", "2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15",
				"2014-12-16", "2014-12-17", "2014-12-18", "2014-12-19", "2014-12-20", "2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25",
				"2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30", "2014-12-31", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04",
				"2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09", "2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14",
				"2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19", "2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24",
				"2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29", "2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03",
				"2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08", "2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13",
				"2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18", "2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23",
				"2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28", "2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05",
				"2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10", "2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15",
				"2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20", "2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25",
				"2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30", "2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04",
				"2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09", "2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14",
				"2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19", "2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24",
				"2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29", "2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04",
				"2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09", "2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14",
				"2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19", "2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24",
				"2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29", "2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03",
				"2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08", "2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13",
				"2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18", "2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23",
				"2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28", "2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03",
				"2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08", "2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13",
				"2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18", "2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23",
				"2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28", "2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02",
				"2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07", "2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12",
				"2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17", "2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22",
				"2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27", "2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01",
				"2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06", "2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11",
				"2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16", "2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21",
				"2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26", "2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01",
				"2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06", "2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11",
				"2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16", "2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21",
				"2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26", "2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31",
				"2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05", "2014-11-06", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10",
				"2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15", "2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-20",
				"2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25", "2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-11-30",
				"2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05", "2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10",
				"2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15", "2014-12-16", "2014-12-17", "2014-12-18", "2014-12-19", "2014-12-20",
				"2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25", "2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30",
				"2014-12-31", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04", "2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09",
				"2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14", "2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19",
				"2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24", "2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29",
				"2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03", "2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08",
				"2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13", "2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18",
				"2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23", "2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28",
				"2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05", "2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10",
				"2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15", "2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20",
				"2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25", "2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30",
				"2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04", "2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09",
				"2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14", "2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19",
				"2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24", "2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29",
				"2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04", "2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09",
				"2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14", "2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19",
				"2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24", "2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29",
				"2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03", "2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08",
				"2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13", "2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18",
				"2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23", "2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28",
				"2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03", "2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08",
				"2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13", "2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18",
				"2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23", "2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28",
				"2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02", "2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07",
				"2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12", "2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17",
				"2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22", "2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27",
				"2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01", "2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06",
				"2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11", "2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16",
				"2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21", "2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26",
				"2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01", "2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06",
				"2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11", "2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16",
				"2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21", "2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26",
				"2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31", "2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05",
				"2014-11-06", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10", "2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15",
				"2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-20", "2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25",
				"2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-11-30", "2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05",
				"2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10", "2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15",
				"2014-12-16", "2014-12-17", "2014-12-18", "2014-12-19", "2014-12-20", "2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25",
				"2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30", "2014-12-31", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04",
				"2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09", "2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14",
				"2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19", "2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24",
				"2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29", "2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03",
				"2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08", "2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13",
				"2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18", "2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23",
				"2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28", "2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05",
				"2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10", "2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15",
				"2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20", "2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25",
				"2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30", "2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04",
				"2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09", "2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14",
				"2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19", "2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24",
				"2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29", "2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04",
				"2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09", "2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14",
				"2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19", "2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24",
				"2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29", "2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03",
				"2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08", "2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13",
				"2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18", "2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23",
				"2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28", "2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03",
				"2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08", "2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13",
				"2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18", "2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23",
				"2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28", "2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02",
				"2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07", "2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12",
				"2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17", "2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22",
				"2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27", "2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01",
				"2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06", "2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11",
				"2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16", "2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21",
				"2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26", "2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01",
				"2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06", "2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11",
				"2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16", "2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21",
				"2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26", "2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31",
				"2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05", "2014-11-06", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10",
				"2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15", "2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-20",
				"2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25", "2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-11-30",
				"2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05", "2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10",
				"2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15", "2014-12-16", "2014-12-17", "2014-12-18", "2014-12-19", "2014-12-20",
				"2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25", "2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30",
				"2014-12-31", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04", "2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09",
				"2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14", "2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19",
				"2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24", "2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29",
				"2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03", "2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08",
				"2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13", "2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18",
				"2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23", "2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28",
				"2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05", "2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10",
				"2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15", "2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20",
				"2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25", "2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30",
				"2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04", "2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09",
				"2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14", "2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19",
				"2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24", "2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29",
				"2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04", "2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09",
				"2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14", "2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19",
				"2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24", "2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29",
				"2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03", "2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08",
				"2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13", "2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18",
				"2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23", "2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28",
				"2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03", "2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08",
				"2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13", "2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18",
				"2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23", "2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28",
				"2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02", "2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07",
				"2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12", "2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17",
				"2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22", "2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27",
				"2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01", "2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06",
				"2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11", "2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16",
				"2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21", "2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26",
				"2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01", "2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06",
				"2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11", "2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16",
				"2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21", "2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26",
				"2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31", "2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05",
				"2014-11-06", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10", "2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15",
				"2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-20", "2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25",
				"2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-11-30", "2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05",
				"2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10", "2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15",
				"2014-12-16", "2014-12-17", "2014-12-18", "2014-12-19", "2014-12-20", "2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25",
				"2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30", "2014-12-31", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04",
				"2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09", "2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14",
				"2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19", "2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24",
				"2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29", "2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03",
				"2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08", "2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13",
				"2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18", "2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23",
				"2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28", "2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05",
				"2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10", "2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15",
				"2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20", "2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25",
				"2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30", "2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04",
				"2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09", "2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14",
				"2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19", "2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24",
				"2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29", "2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04",
				"2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09", "2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14",
				"2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19", "2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24",
				"2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29", "2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03",
				"2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08", "2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13",
				"2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18", "2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23",
				"2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28", "2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03",
				"2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08", "2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13",
				"2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18", "2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23",
				"2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28", "2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02",
				"2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07", "2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12",
				"2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17", "2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22",
				"2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27", "2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01",
				"2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06", "2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11",
				"2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16", "2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21",
				"2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26", "2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01",
				"2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06", "2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11",
				"2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16", "2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21",
				"2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26", "2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31",
				"2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05", "2014-11-06", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10",
				"2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15", "2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-20",
				"2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25", "2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-11-30",
				"2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05", "2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10",
				"2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15", "2014-12-16", "2014-12-17", "2014-12-18", "2014-12-19", "2014-12-20",
				"2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25", "2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30",
				"2014-12-31", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04", "2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09",
				"2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14", "2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19",
				"2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24", "2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29",
				"2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03", "2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08",
				"2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13", "2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18",
				"2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23", "2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28",
				"2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05", "2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10",
				"2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15", "2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20",
				"2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25", "2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30",
				"2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04", "2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09",
				"2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14", "2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19",
				"2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24", "2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29",
				"2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04", "2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09",
				"2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14", "2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19",
				"2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24", "2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29",
				"2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03", "2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08",
				"2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13", "2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18",
				"2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23", "2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28",
				"2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03", "2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08",
				"2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13", "2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18",
				"2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23", "2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28",
				"2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02", "2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07",
				"2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12", "2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17",
				"2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22", "2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27",
				"2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01", "2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06",
				"2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11", "2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16",
				"2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21", "2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26",
				"2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01", "2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06",
				"2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11", "2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16",
				"2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21", "2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26",
				"2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31", "2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05",
				"2014-11-06", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10", "2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15",
				"2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-20", "2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25",
				"2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-11-30", "2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05",
				"2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10", "2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15",
				"2014-12-16", "2014-12-17", "2014-12-18", "2014-12-19", "2014-12-20", "2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25",
				"2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30", "2014-12-31", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04",
				"2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09", "2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14",
				"2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19", "2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24",
				"2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29", "2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03",
				"2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08", "2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13",
				"2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18", "2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23",
				"2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28", "2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05",
				"2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10", "2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15",
				"2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20", "2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25",
				"2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30", "2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04",
				"2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09", "2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14",
				"2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19", "2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24",
				"2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29", "2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04",
				"2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09", "2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14",
				"2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19", "2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24",
				"2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29", "2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03",
				"2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08", "2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13",
				"2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18", "2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23",
				"2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28", "2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03",
				"2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08", "2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13",
				"2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18", "2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23",
				"2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28", "2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02",
				"2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07", "2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12",
				"2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17", "2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22",
				"2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27", "2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01",
				"2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06", "2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11",
				"2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16", "2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21",
				"2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26", "2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01",
				"2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06", "2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11",
				"2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16", "2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21",
				"2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26", "2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31",
				"2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05", "2014-11-06", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10",
				"2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15", "2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-20",
				"2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25", "2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-11-30",
				"2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05", "2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10",
				"2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15", "2014-12-16", "2014-12-17", "2014-12-18", "2014-12-19", "2014-12-20",
				"2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25", "2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30",
				"2014-12-31", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04", "2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09",
				"2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14", "2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19",
				"2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24", "2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29",
				"2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03", "2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08",
				"2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13", "2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18",
				"2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23", "2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28",
				"2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05", "2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10",
				"2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15", "2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20",
				"2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25", "2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30",
				"2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04", "2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09",
				"2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14", "2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19",
				"2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24", "2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29",
				"2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04", "2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09",
				"2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14", "2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19",
				"2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24", "2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29",
				"2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03", "2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08",
				"2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13", "2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18",
				"2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23", "2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28",
				"2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03", "2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08",
				"2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13", "2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18",
				"2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23", "2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28",
				"2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02", "2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07",
				"2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12", "2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17",
				"2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22", "2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27",
				"2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01", "2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06",
				"2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11", "2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16",
				"2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21", "2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26",
				"2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01", "2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06",
				"2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11", "2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16",
				"2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21", "2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26",
				"2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31", "2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05",
				"2014-11-06", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10", "2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15",
				"2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-20", "2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25",
				"2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-11-30", "2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05",
				"2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10", "2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15",
				"2014-12-16", "2014-12-17", "2014-12-18", "2014-12-19", "2014-12-20", "2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25",
				"2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30", "2014-12-31", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04",
				"2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09", "2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14",
				"2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19", "2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24",
				"2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29", "2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03",
				"2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08", "2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13",
				"2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18", "2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23",
				"2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28", "2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05",
				"2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10", "2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15",
				"2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20", "2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25",
				"2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30", "2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04",
				"2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09", "2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14",
				"2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19", "2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24",
				"2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29", "2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04",
				"2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09", "2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14",
				"2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19", "2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24",
				"2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29", "2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03",
				"2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08", "2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13",
				"2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18", "2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23",
				"2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28", "2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03",
				"2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08", "2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13",
				"2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18", "2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23",
				"2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28", "2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02",
				"2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07", "2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12",
				"2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17", "2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22",
				"2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27", "2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01",
				"2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06", "2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11",
				"2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16", "2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21",
				"2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26", "2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01",
				"2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06", "2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11",
				"2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16", "2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21",
				"2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26", "2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31",
				"2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05", "2014-11-06", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10",
				"2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15", "2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-20",
				"2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25", "2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-11-30",
				"2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05", "2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10",
				"2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15", "2014-12-16", "2014-12-17", "2014-12-18", "2014-12-19", "2014-12-20",
				"2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25", "2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30",
				"2014-12-31", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04", "2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09",
				"2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14", "2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19",
				"2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24", "2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29",
				"2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03", "2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08",
				"2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13", "2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18",
				"2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23", "2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28",
				"2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05", "2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10",
				"2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15", "2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20",
				"2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25", "2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30",
				"2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04", "2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09",
				"2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14", "2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19",
				"2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24", "2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29",
				"2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04", "2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09",
				"2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14", "2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19",
				"2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24", "2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29",
				"2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03", "2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08",
				"2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13", "2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18",
				"2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23", "2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28",
				"2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03", "2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08",
				"2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13", "2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18",
				"2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23", "2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28",
				"2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02", "2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07",
				"2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12", "2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17",
				"2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22", "2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27",
				"2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01", "2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06",
				"2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11", "2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16",
				"2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21", "2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26",
				"2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01", "2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06",
				"2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11", "2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16",
				"2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21", "2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26",
				"2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31", "2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05",
				"2014-11-06", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10", "2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15",
				"2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-20", "2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25",
				"2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-11-30", "2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05",
				"2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10", "2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15",
				"2014-12-16", "2014-12-17", "2014-12-18", "2014-12-19", "2014-12-20", "2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25",
				"2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30", "2014-12-31", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04",
				"2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09", "2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14",
				"2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19", "2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24",
				"2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29", "2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03",
				"2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08", "2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13",
				"2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18", "2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23",
				"2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28", "2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05",
				"2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10", "2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15",
				"2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20", "2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25",
				"2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30", "2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04",
				"2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09", "2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14",
				"2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19", "2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24",
				"2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29", "2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04",
				"2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09", "2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14",
				"2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19", "2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24",
				"2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29", "2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03",
				"2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08", "2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13",
				"2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18", "2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23",
				"2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28", "2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03",
				"2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08", "2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13",
				"2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18", "2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23",
				"2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28", "2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02",
				"2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07", "2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12",
				"2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17", "2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22",
				"2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27", "2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01",
				"2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06", "2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11",
				"2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16", "2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21",
				"2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26", "2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01",
				"2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06", "2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11",
				"2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16", "2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21",
				"2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26", "2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31",
				"2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05", "2014-11-06", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10",
				"2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15", "2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-20",
				"2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25", "2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-11-30",
				"2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05", "2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10",
				"2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15", "2014-12-16", "2014-12-17", "2014-12-18", "2014-12-19", "2014-12-20",
				"2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25", "2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30",
				"2014-12-31", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04", "2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09",
				"2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14", "2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19",
				"2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24", "2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29",
				"2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03", "2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08",
				"2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13", "2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18",
				"2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23", "2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28",
				"2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05", "2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10",
				"2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15", "2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20",
				"2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25", "2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30",
				"2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04", "2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09",
				"2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14", "2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19",
				"2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24", "2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29",
				"2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04", "2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09",
				"2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14", "2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19",
				"2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24", "2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29",
				"2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03", "2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08",
				"2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13", "2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18",
				"2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23", "2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28",
				"2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03", "2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08",
				"2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13", "2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18",
				"2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23", "2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28",
				"2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02", "2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07",
				"2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12", "2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17",
				"2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22", "2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27",
				"2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01", "2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06",
				"2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11", "2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16",
				"2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21", "2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26",
				"2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01", "2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06",
				"2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11", "2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16",
				"2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21", "2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26",
				"2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31", "2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05",
				"2014-11-06", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10", "2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15",
				"2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-20", "2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25",
				"2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-11-30", "2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05",
				"2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10", "2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15",
				"2014-12-16", "2014-12-17", "2014-12-18", "2014-12-19", "2014-12-20", "2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25",
				"2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30", "2014-12-31", };
	}

	private int[] getIntArrayForMarketMatching35() {
		return new int[] { 49, 53, 54, 55, 55, 52, 54, 56, 56, 54, 52, 53, 55, 58, 61, 60, 56, 55, 55, 55, 56, 55, 57, 55, 57, 57, 54, 59, 59, 55, 54, 51, 53, 51, 50,
				49, 50, 54, 56, 57, 54, 53, 51, 59, 61, 57, 57, 54, 55, 57, 56, 58, 56, 55, 58, 55, 57, 60, 58, 58, 56, 55, 58, 62, 59, 57, 58, 65, 61, 63, 65, 64, 61,
				64, 60, 59, 60, 61, 60, 57, 56, 57, 60, 55, 58, 60, 63, 57, 56, 53, 54, 54, 57, 56, 57, 62, 69, 67, 58, 60, 59, 59, 63, 60, 59, 63, 59, 58, 57, 65, 61,
				58, 59, 61, 63, 55, 59, 61, 70, 75, 74, 64, 61, 63, 61, 60, 58, 60, 60, 60, 63, 68, 76, 77, 71, 64, 62, 63, 62, 64, 64, 62, 62, 61, 63, 64, 61, 63, 66,
				59, 60, 62, 60, 60, 61, 62, 63, 62, 71, 68, 66, 65, 62, 64, 70, 63, 60, 64, 71, 64, 66, 61, 62, 64, 63, 64, 65, 67, 66, 69, 71, 67, 67, 65, 62, 62, 61,
				63, 66, 67, 68, 66, 62, 61, 67, 70, 70, 69, 68, 68, 68, 71, 71, 70, 72, 76, 69, 69, 67, 67, 65, 65, 66, 64, 65, 68, 68, 69, 65, 67, 65, 69, 70, 70, 69,
				67, 67, 66, 65, 65, 68, 68, 68, 68, 66, 67, 68, 67, 67, 67, 66, 72, 69, 72, 67, 68, 68, 68, 66, 64, 63, 65, 69, 69, 67, 68, 67, 70, 72, 72, 72, 69, 71,
				69, 72, 71, 72, 68, 68, 68, 69, 66, 69, 73, 76, 81, 78, 74, 71, 66, 68, 64, 64, 68, 75, 76, 69, 69, 66, 67, 72, 68, 67, 64, 66, 69, 67, 68, 64, 63, 63,
				71, 67, 61, 60, 58, 60, 61, 65, 65, 62, 65, 61, 61, 61, 62, 62, 61, 60, 60, 58, 58, 60, 57, 58, 60, 58, 57, 57, 59, 57, 55, 61, 58, 61, 59, 63, 63, 63,
				63, 59, 62, 60, 62, 59, 55, 52, 55, 58, 57, 55, 56, 57, 59, 61, 60, 60, 55, 52, 52, 49, 50, 50, 51, 50, 29, 26, 14, 17, 36, 37, 13, 17, 28, 31, 46, 43,
				40, 46, 38, 39, 37, 35, 32, 41, 23, 13, 15, 16, 25, 27, 34, 18, 20, 21, 34, 37, 41, 36, 29, 33, 28, 29, 25, 25, 25, 21, 20, 31, 37, 33, 26, 26, 33, 36,
				39, 41, 44, 45, 37, 31, 27, 25, 19, 27, 37, 27, 21, 33, 24, 32, 46, 42, 43, 52, 46, 26, 32, 51, 37, 30, 34, 37, 49, 44, 50, 37, 29, 31, 32, 31, 44, 47,
				48, 45, 47, 43, 51, 43, 49, 47, 44, 57, 54, 46, 58, 59, 56, 59, 45, 41, 41, 41, 54, 48, 48, 54, 54, 54, 51, 57, 54, 56, 48, 49, 62, 61, 58, 60, 60, 60,
				57, 55, 60, 66, 70, 66, 61, 57, 66, 63, 63, 61, 62, 67, 68, 60, 63, 64, 67, 73, 74, 60, 57, 62, 64, 65, 66, 71, 73, 70, 71, 71, 73, 66, 73, 66, 69, 74,
				71, 71, 71, 75, 83, 73, 74, 69, 69, 72, 72, 76, 80, 72, 76, 72, 74, 78, 78, 78, 71, 75, 75, 77, 80, 78, 78, 76, 74, 75, 77, 76, 77, 75, 75, 74, 73, 74,
				76, 77, 78, 75, 75, 78, 77, 72, 72, 74, 77, 71, 72, 76, 78, 78, 77, 75, 76, 78, 76, 76, 77, 73, 69, 70, 76, 73, 72, 73, 73, 73, 70, 72, 75, 75, 77, 76,
				71, 71, 79, 80, 83, 80, 77, 78, 77, 74, 72, 70, 71, 77, 71, 65, 63, 63, 64, 64, 68, 62, 65, 72, 64, 60, 64, 62, 69, 73, 70, 71, 66, 65, 65, 62, 62, 54,
				58, 70, 69, 63, 58, 55, 56, 56, 70, 72, 66, 66, 65, 51, 52, 60, 55, 53, 57, 59, 60, 57, 59, 63, 54, 51, 46, 46, 51, 59, 59, 52, 48, 42, 51, 49, 53, 56,
				44, 41, 37, 38, 48, 36, 30, 40, 33, 35, 49, 60, 55, 44, 38, 35, 36, 49, 52, 42, 45, 42, 43, 47, 38, 32, 43, 38, 36, 37, 41, 42, 41, 42, 49, 41, 36, 33,
				36, 43, 49, 52, 50, 45, 46, 48, 41, 32, 31, 55, 64, 56, 58, 62, 64, 60, 58, 55, 57, 57, 60, 65, 65, 68, 69, 70, 67, 63, 64, 64, 63, 60, 65, 67, 60, 60,
				57, 57, 60, 60, 61, 54, 53, 55, 57, 57, 56, 58, 61, 60, 61, 63, 63, 62, 63, 63, 58, 59, 61, 64, 65, 61, 59, 59, 61, 62, 62, 61, 59, 59, 58, 60, 60, 63,
				62, 65, 73, 67, 66, 68, 62, 60, 64, 70, 64, 64, 64, 63, 62, 62, 60, 62, 62, 62, 59, 60, 63, 62, 58, 57, 56, 57, 58, 59, 62, 69, 69, 65, 59, 63, 61, 60,
				62, 59, 63, 64, 66, 63, 63, 63, 62, 63, 65, 63, 60, 60, 65, 73, 76, 77, 77, 72, 67, 64, 60, 61, 62, 62, 64, 75, 73, 79, 81, 84, 79, 70, 67, 66, 64, 64,
				64, 66, 66, 68, 68, 69, 69, 69, 68, 68, 67, 65, 66, 66, 66, 66, 66, 66, 64, 67, 68, 67, 66, 67, 68, 67, 67, 67, 67, 69, 70, 69, 70, 70, 69, 70, 71, 72,
				72, 71, 71, 70, 71, 72, 75, 74, 73, 72, 72, 71, 70, 71, 72, 72, 72, 72, 71, 71, 69, 72, 71, 72, 73, 74, 76, 71, 71, 74, 73, 75, 72, 71, 72, 73, 72, 68,
				68, 70, 71, 71, 71, 70, 71, 70, 71, 70, 72, 72, 72, 73, 72, 71, 72, 71, 71, 69, 71, 75, 76, 73, 72, 74, 74, 74, 73, 72, 71, 73, 76, 78, 76, 72, 75, 75,
				76, 78, 79, 82, 78, 72, 72, 71, 71, 69, 70, 71, 72, 71, 68, 70, 68, 70, 69, 79, 81, 77, 74, 75, 77, 76, 71, 70, 70, 71, 70, 69, 69, 67, 67, 67, 67, 69,
				69, 72, 69, 70, 69, 68, 66, 66, 70, 68, 67, 61, 59, 63, 63, 68, 75, 74, 70, 65, 64, 64, 63, 61, 65, 63, 64, 66, 65, 62, 61, 63, 62, 68, 68, 67, 67, 72,
				66, 62, 63, 63, 61, 61, 62, 63, 61, 63, 66, 64, 61, 63, 58, 56, 56, 57, 57, 57, 55, 57, 56, 58, 59, 66, 65, 58, 53, 50, 51, 51, 52, 50, 47, 48, 47, 43,
				44, 50, 50, 48, 46, 44, 40, 39, 45, 39, 46, 46, 44, 47, 42, 40, 40, 46, 42, 40, 48, 42, 41, 45, 40, 40, 41, 44, 46, 42, 44, 46, 46, 44, 46, 44, 43, 41,
				42, 42, 46, 46, 43, 44, 46, 44, 49, 44, 46, 50, 51, 46, 45, 45, 41, 42, 43, 42, 42, 44, 48, 52, 52, 54, 47, 47, 48, 50, 49, 54, 55, 49, 50, 52, 50, 50,
				46, 42, 42, 44, 42, 44, 48, 56, 56, 56, 55, 58, 58, 54, 50, 57, 52, 49, 50, 52, 54, 50, 54, 51, 48, 50, 52, 48, 48, 50, 54, 55, 52, 56, 50, 53, 52, 56,
				55, 55, 53, 49, 47, 50, 53, 59, 57, 56, 58, 58, 52, 56, 54, 55, 58, 61, 63, 64, 68, 62, 58, 58, 56, 56, 56, 51, 53, 53, 61, 57, 56, 62, 62, 60, 52, 56,
				60, 66, 66, 68, 64, 62, 65, 67, 65, 61, 60, 62, 64, 62, 63, 65, 66, 68, 66, 61, 62, 65, 60, 60, 60, 63, 64, 68, 68, 66, 64, 61, 60, 64, 64, 62, 70, 68,
				65, 72, 69, 74, 75, 74, 72, 70, 70, 72, 73, 71, 74, 72, 66, 70, 68, 67, 68, 68, 64, 66, 66, 70, 68, 67, 65, 66, 63, 62, 62, 62, 61, 62, 62, 60, 58, 56,
				54, 62, 57, 56, 59, 58, 60, 66, 64, 64, 62, 60, 64, 66, 63, 64, 66, 63, 61, 62, 61, 58, 64, 62, 62, 62, 64, 64, 68, 68, 63, 58, 56, 56, 58, 56, 66, 62,
				67, 60, 64, 64, 66, 62, 55, 50, 50, 52, 54, 58, 56, 56, 50, 54, 55, 56, 58, 61, 64, 63, 58, 52, 50, 56, 56, 52, 56, 60, 57, 55, 60, 63, 60, 55, 46, 46,
				44, 42, 50, 52, 50, 49, 52, 53, 51, 52, 49, 48, 48, 50, 48, 47, 44, 52, 45, 40, 40, 48, 48, 50, 50, 46, 44, 44, 43, 39, 40, 36, 44, 40, 42, 45, 46, 42,
				38, 40, 44, 39, 48, 54, 48, 44, 46, 53, 52, 46, 42, 40, 38, 36, 34, 33, 35, 78, 78, 67, 69, 76, 72, 54, 61, 75, 77, 78, 74, 73, 77, 66, 56, 56, 61, 58,
				65, 67, 59, 60, 66, 65, 67, 72, 72, 72, 66, 75, 77, 76, 79, 78, 80, 78, 76, 77, 74, 74, 77, 74, 67, 61, 68, 65, 69, 71, 74, 77, 79, 79, 77, 78, 78, 79,
				73, 70, 71, 72, 74, 74, 78, 77, 71, 67, 71, 73, 73, 80, 72, 68, 72, 76, 81, 75, 74, 76, 73, 80, 81, 75, 75, 65, 71, 77, 77, 73, 71, 72, 73, 76, 78, 77,
				77, 80, 79, 73, 72, 75, 77, 79, 80, 82, 78, 79, 79, 79, 73, 73, 76, 76, 78, 81, 80, 81, 83, 83, 84, 83, 83, 82, 77, 78, 78, 82, 83, 82, 82, 83, 83, 82,
				82, 78, 75, 77, 79, 80, 80, 79, 80, 82, 83, 82, 82, 83, 83, 81, 81, 83, 82, 78, 79, 77, 80, 82, 85, 83, 81, 80, 83, 82, 80, 81, 82, 84, 79, 80, 80, 81,
				81, 82, 83, 83, 85, 84, 85, 84, 83, 83, 83, 84, 85, 85, 83, 83, 84, 83, 84, 83, 83, 83, 84, 85, 81, 84, 83, 86, 85, 85, 82, 84, 84, 85, 85, 82, 86, 88,
				87, 85, 84, 85, 84, 81, 81, 84, 86, 83, 84, 86, 86, 85, 86, 86, 85, 82, 84, 85, 86, 88, 85, 87, 86, 85, 87, 85, 86, 85, 86, 87, 88, 87, 86, 84, 82, 82,
				85, 84, 83, 84, 84, 82, 83, 80, 81, 82, 85, 84, 85, 83, 81, 82, 81, 82, 78, 81, 82, 83, 84, 84, 83, 84, 86, 85, 86, 84, 79, 79, 79, 81, 83, 82, 83, 83,
				83, 84, 82, 79, 77, 77, 78, 78, 78, 80, 78, 76, 77, 77, 75, 78, 80, 78, 77, 68, 62, 69, 76, 78, 76, 76, 75, 72, 72, 72, 74, 76, 75, 74, 79, 81, 69, 60,
				70, 73, 78, 80, 82, 82, 71, 65, 62, 66, 74, 76, 77, 76, 76, 77, 75, 73, 69, 63, 59, 59, 61, 67, 67, 67, 68, 69, 70, 70, 71, 73, 74, 77, 80, 73, 73, 75,
				74, 76, 76, 76, 47, 51, 50, 51, 49, 48, 50, 45, 51, 49, 49, 49, 48, 49, 51, 51, 52, 50, 51, 51, 52, 48, 52, 59, 57, 51, 51, 59, 58, 57, 50, 48, 44, 50,
				44, 43, 48, 50, 53, 57, 59, 54, 55, 62, 61, 57, 56, 51, 54, 58, 57, 56, 57, 57, 58, 58, 57, 59, 56, 60, 55, 54, 58, 61, 60, 60, 56, 62, 61, 61, 64, 61,
				62, 65, 63, 59, 60, 59, 62, 61, 60, 62, 62, 54, 56, 56, 60, 56, 51, 52, 50, 52, 55, 54, 56, 62, 65, 69, 68, 66, 64, 60, 66, 63, 66, 66, 67, 68, 63, 67,
				65, 59, 57, 62, 55, 55, 59, 61, 71, 72, 74, 70, 65, 63, 63, 65, 63, 61, 66, 61, 68, 70, 72, 76, 77, 75, 69, 68, 64, 63, 69, 71, 72, 76, 76, 75, 73, 69,
				72, 70, 69, 72, 68, 68, 73, 76, 73, 75, 83, 84, 82, 70, 68, 71, 72, 71, 66, 69, 74, 75, 76, 75, 73, 74, 76, 72, 71, 73, 75, 80, 84, 77, 74, 76, 78, 77,
				78, 78, 81, 75, 73, 73, 75, 79, 85, 81, 76, 73, 73, 76, 71, 74, 74, 73, 79, 82, 84, 86, 79, 80, 82, 82, 85, 78, 71, 70, 71, 81, 79, 77, 74, 72, 77, 73,
				73, 76, 78, 78, 75, 73, 71, 70, 74, 74, 74, 75, 72, 80, 79, 80, 76, 80, 79, 81, 77, 77, 77, 75, 75, 74, 70, 72, 76, 80, 82, 83, 80, 78, 76, 76, 76, 74,
				75, 73, 75, 75, 74, 66, 67, 68, 67, 67, 72, 76, 71, 76, 75, 76, 77, 76, 76, 72, 71, 73, 76, 71, 66, 65, 64, 64, 67, 66, 64, 61, 62, 68, 64, 66, 60, 59,
				61, 65, 66, 61, 59, 57, 55, 57, 60, 62, 63, 63, 64, 62, 60, 55, 59, 59, 56, 54, 54, 51, 53, 54, 54, 55, 52, 52, 53, 54, 57, 52, 57, 55, 58, 56, 62, 62,
				61, 61, 58, 56, 55, 55, 57, 49, 51, 50, 53, 56, 53, 55, 53, 56, 61, 56, 55, 53, 51, 48, 44, 46, 43, 47, 46, 38, 40, 42, 43, 40, 40, 45, 44, 44, 42, 42,
				36, 32, 37, 36, 34, 34, 34, 34, 34, 33, 30, 29, 26, 24, 23, 26, 30, 25, 25, 28, 33, 34, 32, 34, 33, 36, 39, 38, 40, 38, 36, 37, 38, 38, 40, 41, 40, 36,
				40, 42, 43, 40, 40, 40, 38, 40, 40, 40, 38, 37, 37, 39, 38, 36, 42, 42, 43, 48, 42, 42, 42, 44, 46, 46, 47, 48, 45, 51, 44, 40, 42, 38, 40, 38, 36, 47,
				44, 43, 43, 40, 42, 44, 40, 40, 46, 52, 53, 45, 45, 46, 42, 48, 45, 46, 43, 44, 44, 50, 55, 56, 56, 51, 46, 53, 52, 55, 52, 54, 52, 48, 46, 46, 47, 47,
				50, 52, 52, 52, 52, 54, 52, 52, 50, 53, 53, 57, 56, 58, 58, 61, 68, 66, 60, 62, 62, 58, 52, 52, 60, 58, 59, 60, 58, 58, 61, 59, 59, 60, 65, 68, 64, 63,
				61, 60, 60, 58, 64, 64, 58, 60, 56, 56, 58, 58, 59, 58, 60, 63, 59, 60, 60, 59, 61, 66, 70, 68, 69, 72, 74, 74, 70, 64, 61, 64, 68, 66, 64, 67, 69, 71,
				72, 73, 73, 74, 74, 74, 72, 74, 76, 70, 69, 69, 74, 72, 69, 70, 66, 68, 68, 68, 65, 66, 64, 64, 64, 64, 60, 57, 59, 60, 60, 59, 59, 60, 58, 57, 57, 62,
				59, 60, 59, 58, 63, 60, 58, 62, 62, 64, 64, 60, 60, 58, 61, 60, 59, 62, 63, 62, 62, 63, 63, 58, 58, 52, 48, 57, 55, 58, 56, 58, 56, 56, 56, 56, 56, 60,
				57, 55, 54, 78, 60, 56, 54, 54, 50, 51, 52, 56, 54, 56, 58, 55, 51, 54, 52, 52, 51, 54, 54, 53, 49, 44, 46, 54, 54, 55, 53, 50, 48, 46, 50, 51, 48, 47,
				50, 50, 50, 49, 47, 46, 44, 41, 42, 44, 43, 46, 46, 42, 41, 44, 40, 38, 38, 35, 36, 36, 39, 40, 38, 36, 39, 36, 40, 39, 42, 41, 38, 40, 38, 38, 44, 44,
				38, 41, 46, 43, 42, 29, 24, 31, 26, 26, 34, 41, 74, 84, 76, 72, 76, 77, 66, 66, 70, 73, 78, 76, 73, 76, 78, 78, 78, 78, 72, 73, 72, 70, 72, 76, 69, 67,
				71, 74, 78, 77, 76, 75, 78, 78, 72, 68, 68, 70, 76, 78, 71, 72, 75, 78, 74, 75, 72, 70, 72, 77, 74, 71, 70, 70, 72, 76, 81, 70, 68, 70, 69, 70, 72, 76,
				71, 74, 76, 74, 77, 75, 74, 70, 74, 74, 75, 70, 72, 70, 74, 75, 76, 76, 68, 70, 72, 70, 70, 70, 72, 70, 72, 72, 74, 68, 68, 66, 66, 68, 68, 68, 73, 66,
				65, 62, 63, 64, 62, 66, 64, 64, 64, 66, 66, 72, 66, 67, 63, 66, 68, 66, 60, 64, 56, 60, 60, 59, 58, 57, 60, 64, 64, 61, 64, 64, 63, 65, 66, 65, 67, 68,
				64, 67, 66, 68, 68, 66, 69, 68, 62, 62, 64, 64, 63, 60, 60, 60, 58, 56, 56, 54, 60, 57, 60, 60, 62, 56, 58, 56, 58, 59, 62, 58, 58, 56, 57, 58, 60, 58,
				62, 56, 54, 54, 54, 54, 58, 55, 54, 56, 56, 56, 54, 57, 54, 50, 50, 56, 59, 58, 54, 52, 55, 54, 54, 55, 57, 58, 59, 56, 57, 64, 65, 67, 57, 51, 50, 54,
				54, 57, 57, 54, 58, 60, 50, 49, 52, 52, 54, 54, 58, 56, 58, 54, 54, 58, 56, 58, 59, 57, 58, 60, 54, 56, 60, 63, 56, 53, 54, 54, 58, 58, 60, 64, 65, 66,
				60, 60, 65, 62, 66, 64, 58, 58, 55, 59, 61, 61, 65, 66, 62, 61, 64, 75, 76, 74, 64, 62, 66, 76, 70, 72, 64, 64, 66, 70, 72, 68, 57, 54, 60, 58, 62, 66,
				62, 62, 65, 70, 73, 70, 76, 78, 73, 66, 74, 74, 82, 62, 63, 68, 70, 64, 66, 68, 66, 68, 64, 66, 70, 82, 68, 72, 67, 69, 68, 77, 82, 72, 79, 78, 72, 72,
				66, 68, 70, 75, 76, 76, 80, 76, 76, 75, 74, 78, 72, 72, 68, 66, 68, 68, 72, 74, 68, 73, 69, 69, 72, 76, 76, 71, 76, 74, 68, 72, 73, 80, 72, 41, 39, 29,
				30, 43, 33, 16, 32, 39, 44, 56, 49, 48, 52, 39, 35, 42, 35, 46, 51, 38, 27, 31, 24, 33, 44, 46, 25, 20, 27, 40, 45, 60, 52, 43, 43, 35, 40, 48, 51, 45,
				38, 32, 37, 44, 41, 47, 52, 57, 63, 67, 55, 52, 57, 54, 51, 41, 38, 42, 51, 60, 47, 39, 47, 42, 48, 56, 60, 60, 64, 56, 44, 50, 59, 56, 49, 43, 53, 58,
				55, 64, 57, 50, 44, 41, 52, 57, 58, 54, 61, 67, 67, 70, 69, 60, 55, 58, 55, 58, 59, 61, 66, 69, 67, 51, 48, 53, 54, 57, 63, 64, 66, 66, 65, 71, 66, 71,
				71, 72, 68, 61, 63, 63, 71, 75, 73, 72, 75, 71, 70, 74, 77, 77, 74, 59, 58, 60, 59, 65, 70, 74, 76, 79, 78, 79, 76, 75, 77, 77, 77, 78, 75, 72, 78, 79,
				77, 76, 78, 77, 79, 78, 75, 75, 75, 76, 80, 81, 82, 83, 83, 82, 79, 79, 80, 78, 79, 79, 79, 79, 82, 80, 81, 83, 79, 74, 77, 78, 81, 80, 82, 79, 78, 81,
				81, 81, 79, 74, 74, 75, 77, 72, 78, 77, 80, 79, 79, 80, 82, 81, 74, 72, 76, 74, 76, 78, 81, 80, 81, 83, 81, 80, 79, 78, 80, 76, 74, 77, 78, 79, 80, 79,
				81, 81, 84, 84, 82, 77, 76, 77, 80, 80, 80, 80, 82, 81, 82, 77, 81, 82, 82, 78, 77, 79, 81, 83, 78, 72, 73, 79, 77, 74, 77, 75, 75, 73, 69, 67, 71, 70,
				72, 70, 69, 75, 75, 74, 72, 57, 58, 65, 70, 76, 78, 76, 78, 75, 74, 66, 62, 62, 65, 66, 60, 62, 65, 59, 58, 62, 62, 71, 72, 68, 62, 57, 51, 43, 46, 52,
				57, 60, 61, 51, 51, 55, 57, 58, 56, 41, 36, 39, 44, 45, 31, 36, 48, 51, 50, 54, 66, 55, 50, 42, 40, 47, 55, 60, 61, 61, 63, 58, 58, 52, 43, 45, 43, 43,
				47, 50, 46, 48, 52, 44, 46, 47, 47, 48, 46, 49, 55, 45, 47, 50, 57, 58, 46, 44, 36, 40, 40, 40, 38, 38, 47, 49, 48, 40, 36, 38, 37, 38, 35, 42, 40, 34,
				34, 37, 38, 38, 38, 34, 36, 37, 35, 35, 29, 30, 32, 36, 37, 34, 38, 32, 43, 43, 40, 39, 34, 38, 37, 41, 40, 45, 42, 42, 39, 42, 44, 39, 38, 42, 40, 42,
				46, 43, 42, 40, 39, 40, 43, 42, 40, 43, 44, 46, 48, 46, 50, 48, 48, 46, 51, 51, 50, 50, 52, 55, 48, 40, 38, 38, 38, 42, 48, 50, 52, 52, 54, 56, 57, 58,
				53, 57, 56, 54, 48, 51, 51, 54, 56, 49, 40, 42, 44, 44, 48, 53, 50, 53, 56, 56, 62, 58, 52, 51, 54, 57, 54, 54, 44, 46, 51, 56, 56, 56, 60, 56, 54, 49,
				50, 52, 49, 53, 52, 55, 58, 62, 64, 68, 61, 59, 60, 59, 59, 57, 56, 50, 51, 56, 59, 60, 58, 56, 62, 69, 74, 77, 78, 72, 72, 69, 61, 59, 62, 61, 62, 63,
				60, 58, 62, 64, 66, 61, 58, 62, 65, 61, 60, 60, 63, 65, 70, 67, 72, 66, 58, 53, 56, 64, 66, 67, 66, 68, 68, 71, 72, 75, 73, 65, 69, 70, 68, 68, 64, 70,
				70, 64, 66, 68, 66, 66, 66, 67, 62, 64, 66, 66, 70, 72, 66, 64, 59, 60, 58, 60, 60, 60, 61, 57, 56, 58, 57, 55, 54, 61, 62, 62, 64, 64, 56, 56, 58, 58,
				61, 64, 67, 64, 67, 66, 60, 56, 54, 57, 62, 60, 62, 62, 66, 65, 66, 62, 49, 49, 51, 54, 57, 59, 59, 62, 62, 60, 58, 56, 52, 56, 54, 56, 62, 66, 60, 60,
				60, 56, 59, 58, 58, 57, 56, 60, 62, 54, 44, 47, 46, 48, 54, 46, 42, 47, 50, 53, 54, 50, 52, 51, 43, 43, 42, 46, 43, 42, 42, 42, 48, 48, 45, 48, 44, 43,
				42, 38, 41, 48, 44, 48, 44, 42, 42, 38, 35, 36, 36, 36, 36, 38, 37, 37, 36, 37, 32, 34, 40, 44, 46, 46, 43, 38, 38, 47, 49, 40, 40, 45, 43, 38, 39, 36,
				32, 20, 18, 30, 32, 48, 46, 44, 44, 42, 44, 44, 47, 44, 38, 38, 42, 42, 40, 39, 39, 44, 42, 40, 40, 42, 40, 42, 45, 46, 49, 38, 47, 46, 52, 50, 44, 48,
				56, 44, 36, 37, 38, 36, 40, 44, 38, 40, 40, 37, 38, 46, 47, 42, 42, 40, 42, 42, 42, 44, 46, 48, 52, 56, 48, 42, 46, 42, 46, 44, 40, 40, 44, 42, 42, 50,
				55, 50, 46, 52, 52, 57, 52, 47, 50, 48, 52, 55, 61, 60, 56, 57, 63, 62, 60, 56, 57, 57, 60, 52, 48, 50, 58, 58, 61, 54, 56, 56, 56, 59, 64, 62, 55, 54,
				50, 56, 58, 60, 60, 63, 64, 63, 65, 64, 60, 68, 68, 70, 66, 64, 58, 60, 64, 68, 65, 68, 66, 66, 70, 65, 70, 68, 68, 68, 70, 63, 65, 63, 67, 72, 70, 70,
				73, 72, 73, 76, 78, 76, 74, 74, 68, 68, 66, 68, 70, 73, 70, 72, 75, 76, 75, 76, 74, 71, 76, 74, 74, 71, 74, 73, 73, 74, 74, 73, 76, 74, 76, 78, 76, 71,
				70, 74, 74, 79, 75, 78, 82, 83, 78, 82, 81, 81, 80, 76, 74, 76, 76, 80, 83, 84, 84, 84, 86, 80, 81, 81, 84, 84, 84, 88, 86, 88, 86, 84, 82, 78, 79, 84,
				78, 78, 80, 84, 82, 81, 84, 86, 86, 84, 86, 82, 82, 78, 76, 72, 70, 73, 72, 74, 70, 75, 74, 76, 80, 80, 72, 70, 73, 74, 70, 72, 74, 73, 74, 76, 74, 72,
				70, 70, 68, 72, 70, 73, 76, 74, 68, 70, 74, 73, 73, 68, 76, 75, 66, 69, 68, 66, 68, 70, 66, 65, 68, 73, 62, 62, 65, 62, 64, 68, 66, 64, 57, 62, 62, 68,
				66, 62, 60, 60, 64, 63, 66, 65, 60, 58, 64, 64, 58, 60, 63, 58, 58, 60, 52, 54, 54, 55, 55, 54, 50, 54, 56, 57, 56, 52, 49, 55, 56, 60, 58, 60, 50, 48,
				52, 50, 42, 43, 46, 48, 46, 53, 50, 47, 40, 42, 42, 42, 40, 40, 44, 47, 45, 45, 48, 46, 42, 42, 40, 42, 45, 45, 30, 30, 20, 22, 24, 20, 24, 31, 22, 19,
				16, 14, 15, 24, 28, 28, 14, 14, 14, 17, 23, 29, 20, 18, 15, 16, 27, 31, 21, 18, 18, 20, 22, 24, 19, 16, 28, 32, 22, 20, 18, 16, 12, 12, 18, 14, 11, 10,
				16, 18, 15, 13, 7, 4, 6, 14, 26, 36, 20, 16, 14, 19, 22, 18, 16, 12, 11, 10, 12, 12, 13, 19, 22, 21, 14, 17, 16, 9, 9, 10, 14, 13, 9, 8, 4, 11, 27, 38,
				28, 22, 17, 13, 18, 18, 14, 12, 12, 16, 26, 36, 26, 28, 19, 13, 16, 18, 18, 11, 10, 14, 13, 19, 21, 22, 22, 32, 36, 24, 21, 29, 38, 40, 42, 38, 32, 30,
				27, 29, 31, 31, 34, 36, 38, 32, 34, 32, 24, 36, 33, 34, 36, 34, 26, 32, 34, 33, 34, 34, 34, 37, 36, 32, 38, 36, 37, 36, 39, 40, 44, 44, 36, 40, 46, 50,
				46, 50, 44, 46, 45, 51, 49, 47, 50, 42, 46, 40, 41, 42, 42, 39, 39, 38, 48, 54, 48, 41, 37, 52, 52, 45, 50, 48, 50, 42, 51, 40, 41, 48, 53, 47, 52, 46,
				52, 55, 52, 50, 53, 46, 48, 56, 55, 52, 48, 44, 46, 46, 46, 45, 48, 46, 52, 46, 50, 49, 46, 44, 49, 52, 48, 36, 47, 48, 50, 46, 46, 46, 48, 44, 44, 47,
				53, 44, 43, 40, 40, 42, 48, 43, 42, 43, 40, 38, 40, 36, 37, 36, 35, 38, 43, 44, 44, 41, 40, 43, 38, 37, 36, 35, 34, 32, 32, 32, 34, 32, 32, 29, 26, 30,
				34, 38, 39, 44, 47, 39, 34, 36, 38, 38, 34, 34, 32, 26, 27, 30, 30, 31, 24, 24, 21, 21, 24, 26, 29, 28, 26, 26, 26, 22, 24, 28, 30, 28, 26, 24, 26, 26,
				26, 32, 31, 36, 30, 26, 31, 30, 30, 30, 35, 28, 22, 22, 21, 18, 16, 12, 21, 24, 20, 21, 32, 22, 23, 21, 16, 14, 20, 22, 15, 22, 26, 16, 10, 14, 12, 10,
				10, 10, 19, 25, 21, 29, 36, 32, 28, 24, 22, 24, 14, 8, 15, 31, 43, 19, 15, 25, 28, 47, 46, 41, 47, 42, 36, 40, 35, 33, 34, 19, 13, 14, 13, 27, 21, 34,
				17, 21, 23, 33, 37, 43, 34, 29, 29, 24, 25, 24, 26, 25, 20, 16, 29, 33, 31, 28, 22, 25, 29, 41, 39, 42, 43, 35, 26, 24, 21, 18, 26, 35, 23, 22, 25, 18,
				25, 41, 37, 36, 47, 42, 21, 28, 48, 30, 22, 27, 34, 45, 40, 43, 33, 25, 30, 30, 34, 48, 46, 40, 38, 41, 42, 44, 41, 46, 47, 49, 54, 50, 47, 59, 59, 54,
				66, 55, 38, 36, 37, 50, 44, 53, 60, 52, 51, 50, 45, 46, 48, 43, 43, 52, 60, 59, 56, 58, 56, 58, 55, 55, 69, 71, 69, 49, 55, 67, 66, 67, 58, 60, 61, 59,
				55, 55, 57, 59, 67, 62, 47, 51, 57, 56, 59, 70, 65, 58, 59, 67, 71, 74, 74, 69, 61, 63, 62, 68, 69, 68, 73, 78, 76, 69, 66, 65, 68, 72, 78, 71, 66, 70,
				74, 77, 79, 82, 81, 70, 70, 76, 79, 83, 81, 74, 68, 71, 77, 79, 78, 73, 74, 71, 68, 67, 67, 75, 82, 70, 73, 75, 73, 75, 72, 71, 72, 74, 67, 66, 71, 76,
				77, 72, 73, 72, 73, 75, 73, 67, 70, 68, 70, 73, 71, 67, 67, 66, 68, 67, 69, 74, 75, 79, 73, 65, 69, 76, 79, 82, 79, 75, 79, 81, 70, 65, 65, 65, 71, 62,
				60, 59, 58, 60, 62, 65, 51, 60, 73, 64, 61, 60, 58, 62, 71, 74, 66, 57, 57, 54, 52, 58, 55, 55, 64, 66, 58, 57, 51, 52, 54, 68, 71, 65, 64, 63, 50, 49,
				56, 53, 51, 49, 55, 53, 55, 53, 63, 53, 50, 44, 38, 45, 51, 57, 49, 44, 42, 48, 48, 53, 53, 44, 39, 35, 36, 43, 36, 31, 40, 32, 34, 51, 54, 55, 42, 35,
				31, 29, 45, 52, 38, 44, 37, 34, 42, 30, 24, 39, 43, 36, 35, 37, 39, 37, 40, 45, 40, 33, 32, 33, 39, 45, 44, 52, 44, 45, 46, 36, 26, 25, 40, 32, 38, 28,
				32, 29, 32, 25, 22, 24, 24, 27, 24, 24, 28, 31, 34, 30, 33, 32, 30, 28, 26, 37, 34, 29, 36, 31, 27, 35, 30, 36, 40, 29, 26, 22, 26, 26, 26, 20, 19, 20,
				26, 28, 26, 29, 32, 34, 30, 30, 28, 30, 35, 35, 38, 36, 40, 40, 34, 40, 40, 32, 39, 34, 34, 32, 41, 38, 36, 42, 43, 40, 50, 52, 52, 53, 50, 47, 54, 55,
				56, 54, 54, 58, 60, 55, 62, 60, 61, 64, 60, 60, 56, 58, 54, 57, 60, 62, 71, 60, 58, 56, 60, 64, 66, 62, 58, 60, 52, 56, 60, 63, 68, 68, 66, 61, 62, 64,
				65, 68, 70, 65, 60, 58, 56, 58, 66, 62, 64, 62, 54, 66, 70, 64, 65, 72, 70, 70, 72, 76, 74, 80, 80, 72, 72, 74, 76, 78, 86, 82, 80, 77, 72, 74, 76, 80,
				70, 72, 74, 72, 71, 74, 80, 78, 76, 78, 78, 72, 76, 74, 74, 74, 74, 75, 77, 76, 82, 81, 82, 82, 84, 78, 74, 80, 84, 80, 82, 82, 84, 80, 84, 82, 84, 82,
				82, 81, 79, 78, 84, 88, 88, 87, 82, 81, 78, 80, 82, 85, 86, 85, 78, 81, 82, 85, 86, 76, 78, 82, 80, 78, 78, 80, 82, 78, 68, 73, 77, 80, 77, 77, 80, 80,
				82, 74, 81, 81, 76, 78, 78, 76, 73, 75, 74, 72, 70, 74, 76, 75, 76, 78, 72, 70, 73, 71, 72, 73, 67, 68, 62, 65, 66, 65, 71, 70, 69, 64, 69, 64, 68, 65,
				65, 65, 55, 55, 58, 58, 58, 60, 55, 58, 60, 61, 63, 64, 58, 50, 54, 57, 53, 57, 60, 62, 57, 50, 50, 54, 56, 56, 56, 48, 48, 50, 54, 55, 50, 50, 49, 46,
				50, 44, 41, 42, 44, 45, 45, 40, 38, 42, 38, 40, 42, 41, 38, 38, 44, 40, 39, 40, 37, 36, 40, 41, 34, 36, 26, 23, 24, 27, 27, 28, 34, 27, 24, 30, 30, 30,
				28, 28, 31, 28, 26, 25, 31, 28, 28, 32, 34, 32, 32, 28, 30, 34, 36, 34, 28, 35, 35, 36, 38, 36, 37, 46, 46, 42, 37, 37, 40, 40, 38, 34, 38, 39, 34, 34,
				38, 38, 37, 33, 32, 30, 37, 34, 32, 28, 28, 32, 33, 36, 38, 34, 34, 44, 36, 34, 37, 37, 38, 37, 38, 38, 44, 40, 40, 36, 40, 43, 40, 40, 42, 38, 40, 42,
				40, 44, 40, 37, 36, 36, 42, 39, 41, 43, 45, 44, 42, 43, 44, 46, 44, 53, 50, 51, 48, 52, 52, 46, 37, 39, 38, 36, 40, 45, 47, 50, 52, 54, 54, 54, 52, 48,
				56, 52, 53, 48, 48, 52, 58, 57, 49, 44, 42, 46, 42, 41, 49, 51, 52, 57, 58, 58, 59, 48, 46, 49, 47, 54, 53, 45, 48, 50, 55, 56, 57, 60, 60, 52, 48, 50,
				48, 48, 50, 53, 54, 61, 60, 63, 67, 58, 58, 60, 60, 56, 58, 58, 56, 52, 56, 60, 57, 58, 58, 62, 69, 71, 75, 76, 71, 74, 68, 64, 63, 62, 63, 62, 64, 64,
				64, 65, 66, 64, 62, 60, 64, 67, 58, 58, 58, 58, 62, 64, 66, 70, 68, 56, 54, 54, 60, 64, 64, 66, 66, 66, 69, 70, 70, 69, 63, 64, 69, 66, 68, 62, 63, 67,
				63, 62, 68, 65, 68, 66, 66, 66, 63, 67, 68, 70, 70, 62, 62, 61, 60, 56, 58, 57, 60, 61, 58, 58, 58, 54, 57, 54, 60, 64, 64, 64, 65, 58, 57, 56, 59, 64,
				66, 65, 65, 66, 66, 64, 58, 51, 59, 61, 60, 62, 62, 62, 65, 66, 62, 54, 50, 51, 54, 53, 56, 58, 62, 60, 63, 58, 56, 54, 58, 56, 56, 60, 62, 62, 60, 58,
				54, 55, 57, 58, 58, 56, 60, 60, 56, 42, 46, 45, 46, 52, 43, 49, 48, 50, 46, 48, 48, 46, 46, 38, 38, 46, 44, 38, 43, 44, 44, 49, 47, 49, 46, 42, 46, 42,
				36, 44, 39, 40, 46, 44, 46, 44, 40, 37, 36, 36, 38, 38, 38, 38, 37, 38, 34, 32, 34, 41, 42, 42, 46, 46, 44, 41, 45, 48, 42, 38, 36, 38, 36, 38, 33, 30,
				25, 14, 26, 24, 46, 49, 48, 47, 46, 46, 50, 49, 47, 46, 47, 44, 48, 48, 48, 53, 52, 52, 56, 56, 57, 53, 52, 52, 55, 42, 34, 42, 46, 34, 38, 41, 47, 48,
				45, 43, 40, 40, 49, 54, 54, 46, 54, 56, 54, 45, 46, 50, 52, 46, 55, 54, 47, 49, 46, 42, 42, 42, 44, 52, 55, 58, 54, 56, 54, 52, 48, 45, 46, 42, 45, 46,
				42, 48, 50, 54, 55, 58, 52, 55, 57, 52, 53, 56, 53, 57, 56, 50, 46, 44, 50, 54, 56, 57, 60, 58, 54, 55, 54, 60, 52, 50, 53, 52, 58, 62, 61, 56, 58, 60,
				59, 66, 66, 65, 60, 58, 60, 58, 60, 56, 59, 60, 62, 66, 62, 60, 58, 60, 56, 56, 60, 64, 66, 69, 66, 66, 65, 63, 66, 67, 69, 69, 68, 66, 68, 70, 72, 72,
				76, 68, 64, 66, 66, 66, 68, 66, 66, 70, 70, 73, 75, 73, 72, 71, 74, 76, 74, 75, 78, 76, 70, 70, 68, 72, 73, 76, 82, 80, 72, 73, 74, 74, 76, 74, 72, 75,
				74, 78, 79, 80, 78, 82, 80, 79, 78, 78, 78, 78, 77, 75, 77, 79, 82, 83, 78, 78, 80, 82, 84, 84, 83, 82, 83, 78, 78, 81, 83, 85, 76, 75, 76, 80, 82, 84,
				82, 84, 80, 86, 78, 75, 78, 78, 78, 82, 83, 80, 79, 79, 78, 78, 78, 78, 78, 76, 76, 80, 77, 80, 78, 76, 72, 74, 76, 74, 74, 76, 78, 76, 74, 70, 65, 68,
				67, 68, 69, 68, 61, 62, 66, 61, 62, 64, 64, 64, 67, 64, 64, 66, 63, 66, 68, 66, 66, 66, 62, 63, 62, 62, 68, 70, 66, 58, 54, 60, 64, 71, 66, 62, 57, 52,
				54, 60, 54, 51, 53, 50, 52, 52, 52, 58, 60, 61, 60, 59, 60, 60, 59, 60, 56, 55, 58, 60, 64, 54, 49, 44, 47, 48, 46, 44, 44, 46, 50, 48, 48, 52, 59, 56,
				51, 53, 58, 54, 54, 46, 50, 50, 50, 48, 48, 48, 55, 53, 49, 50, 48, 44, 50, 52, 56, 54, 45, 42, 46, 35, 38, 75, 80, 79, 78, 75, 72, 76, 80, 76, 72, 67,
				69, 73, 74, 74, 72, 70, 70, 74, 75, 76, 76, 76, 76, 78, 82, 81, 82, 77, 78, 80, 80, 80, 79, 78, 80, 76, 74, 74, 73, 72, 72, 70, 73, 76, 71, 72, 74, 78,
				80, 78, 78, 76, 76, 80, 80, 78, 80, 76, 73, 74, 78, 77, 79, 77, 78, 79, 80, 78, 76, 82, 84, 82, 84, 82, 88, 86, 84, 84, 82, 80, 79, 80, 82, 81, 81, 82,
				83, 85, 82, 81, 82, 83, 89, 84, 82, 81, 82, 82, 80, 81, 79, 80, 78, 79, 82, 82, 83, 82, 84, 91, 90, 89, 87, 84, 86, 84, 86, 84, 87, 84, 84, 86, 86, 86,
				86, 88, 88, 88, 86, 86, 85, 85, 84, 86, 86, 86, 86, 89, 89, 87, 86, 86, 86, 86, 86, 88, 87, 88, 88, 86, 87, 88, 90, 88, 88, 89, 89, 88, 90, 92, 92, 90,
				87, 86, 86, 85, 84, 87, 86, 88, 88, 88, 88, 87, 88, 88, 87, 88, 88, 88, 85, 80, 78, 81, 85, 86, 82, 80, 80, 81, 78, 76, 80, 82, 78, 79, 82, 82, 84, 84,
				83, 83, 81, 82, 84, 84, 80, 78, 80, 80, 78, 81, 81, 81, 80, 81, 80, 81, 80, 81, 82, 83, 82, 84, 82, 81, 82, 84, 82, 82, 82, 82, 81, 82, 82, 82, 80, 80,
				80, 80, 83, 79, 77, 81, 82, 83, 80, 82, 84, 82, 80, 80, 81, 82, 83, 83, 82, 80, 82, 81, 80, 82, 80, 82, 82, 82, 83, 82, 86, 86, 88, 86, 82, 84, 86, 85,
				85, 85, 83, 82, 84, 85, 86, 84, 84, 82, 84, 88, 88, 86, 84, 87, 88, 88, 88, 86, 80, 80, 80, 84, 82, 84, 82, 82, 82, 84, 84, 82, 83, 81, 82, 82, 85, 85,
				84, 86, 84, 82, 84, 86, 85, 86, 86, 82, 80, 80, 82, 82, 82, 79, 80, 80, 79, 78, 80, 77, 78, 82, 80, 77, 78, 78, 79, 78, 80, 80, 74, 67, 76, 78, 77, 78,
				78, 77, 74, 72, 73, 75, 76, 78, 76, 74, 72, 72, 76, 76, 78, 79, 80, 80, 81, 82, 82, 80, 80, 80, 77, 75, 74, 72, 74, 73, 70, 72, 72, 69, 70, 71, 74, 76,
				77, 78, 79, 79, 80, 80, 80, 82, 83, 83, 83, 83, 82, 82, 82, 82, 82, 82, 82, 82, 84, 84, 84, 85, 77, 76, 82, 83, 84, 83, 84, 84, 84, 82, 84, 84, 85, 84,
				85, 84, 86, 85, 85, 86, 86, 86, 86, 88, 86, 88, 88, 88, 88, 88, 80, 85, 86, 87, 88, 88, 88, 86, 89, 88, 88, 88, 90, 84, 84, 84, 89, 90, 88, 90, 86, 86,
				86, 86, 84, 86, 88, 88, 89, 90, 90, 90, 91, 90, 90, 90, 90, 84, 86, 88, 89, 84, 88, 86, 88, 86, 84, 88, 87, 88, 84, 90, 88, 88, 90, 92, 90, 84, 86, 88,
				89, 89, 89, 90, 90, 88, 89, 86, 89, 90, 90, 84, 87, 89, 86, 86, 86, 85, 86, 87, 86, 88, 84, 85, 88, 86, 86, 84, 85, 87, 86, 87, 84, 84, 86, 84, 82, 85,
				88, 90, 84, 83, 87, 88, 89, 89, 88, 87, 85, 85, 84, 87, 88, 86, 82, 84, 84, 86, 84, 82, 84, 86, 86, 86, 84, 86, 85, 84, 80, 86, 84, 84, 85, 84, 84, 84,
				85, 83, 82, 84, 85, 85, 85, 85, 85, 84, 84, 84, 84, 88, 84, 84, 82, 82, 86, 87, 85, 82, 83, 82, 84, 82, 81, 82, 84, 82, 84, 84, 84, 82, 86, 86, 86, 84,
				84, 86, 85, 85, 84, 84, 84, 86, 88, 88, 84, 88, 85, 84, 84, 84, 86, 83, 82, 84, 83, 84, 84, 85, 82, 81, 80, 82, 84, 83, 84, 84, 84, 83, 84, 84, 84, 83,
				82, 84, 84, 83, 82, 83, 84, 84, 84, 84, 85, 85, 84, 82, 84, 82, 82, 85, 84, 84, 85, 85, 87, 86, 84, 82, 83, 84, 82, 81, 79, 80, 80, 82, 84, 84, 84, 86,
				84, 84, 86, 86, 84, 82, 82, 82, 82, 84, 84, 82, 80, 82, 80, 82, 76, 78, 80, 79, 76, 76, 76, 78, 79, 76, 76, 78, 81, 82, 84, 82, 78, 77, 76, 68, 64, 69,
				69, 75, 82, 72, 72, 73, 78, 90, 91, 75, 70, 68, 71, 75, 78, 85, 85, 84, 86, 86, 82, 86, 80, 70, 74, 77, 79, 77, 74, 76, 78, 84, 80, 80, 78, 72, 76, 75,
				77, 80, 82, 78, 75, 72, 78, 80, 80, 84, 81, 84, 82, 76, 78, 73, 78, 80, 83, 80, 74, 76, 76, 79, 86, 74, 66, 69, 77, 83, 78, 68, 61, 64, 69, 74, 77, 76,
				72, 76, 80, 82, 76, 78, 73, 78, 69, 66, 66, 64, 64, 68, 69, 73, 66, 66, 70, 74, 80, 82, 78, 77, 75, 68, 68, 62, 68, 64, 64, 60, 58, 62, 66, 68, 65, 67,
				56, 55, 60, 56, 57, 58, 61, 62, 65, 65, 60, 62, 62, 64, 63, 61, 62, 63, 62, 62, 61, 64, 66, 64, 63, 64, 62, 60, 61, 62, 58, 57, 54, 55, 59, 62, 60, 60,
				64, 63, 62, 62, 66, 56, 50, 52, 49, 48, 52, 58, 54, 57, 56, 60, 58, 52, 47, 50, 52, 54, 50, 49, 50, 60, 63, 58, 48, 54, 58, 59, 57, 52, 48, 50, 54, 54,
				56, 54, 56, 52, 50, 54, 58, 54, 54, 53, 52, 56, 58, 56, 54, 57, 58, 59, 51, 50, 53, 53, 56, 62, 64, 62, 60, 58, 64, 64, 60, 63, 64, 60, 60, 65, 60, 61,
				60, 62, 57, 58, 58, 62, 60, 60, 64, 64, 58, 55, 54, 60, 59, 57, 65, 65, 62, 58, 60, 62, 59, 59, 59, 56, 60, 64, 67, 62, 62, 72, 62, 57, 51, 58, 60, 62,
				60, 61, 62, 62, 69, 70, 60, 62, 59, 56, 57, 57, 64, 60, 58, 59, 65, 68, 74, 72, 66, 66, 60, 60, 61, 74, 64, 64, 62, 64, 62, 71, 73, 64, 64, 68, 62, 62,
				65, 66, 62, 62, 62, 77, 67, 64, 64, 60, 60, 66, 68, 68, 67, 64, 63, 66, 64, 64, 68, 74, 80, 73, 68, 70, 68, 69, 69, 67, 66, 68, 68, 70, 70, 65, 68, 78,
				70, 66, 66, 64, 64, 65, 74, 80, 77, 72, 74, 74, 76, 74, 75, 78, 76, 78, 86, 74, };
	}

	private String[] getAreaArrayForMarketMatching36() {
		return new String[] { "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", };
	}

	private String[] getDateArrayForMarketMatching36() {
		return new String[] { "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04", "2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09",
				"2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14", "2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19",
				"2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24", "2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29",
				"2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03", "2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08",
				"2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13", "2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18",
				"2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23", "2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28",
				"2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05", "2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10",
				"2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15", "2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20",
				"2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25", "2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30",
				"2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04", "2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09",
				"2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14", "2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19",
				"2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24", "2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29",
				"2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04", "2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09",
				"2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14", "2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19",
				"2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24", "2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29",
				"2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03", "2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08",
				"2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13", "2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18",
				"2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23", "2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28",
				"2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03", "2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08",
				"2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13", "2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18",
				"2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23", "2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28",
				"2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02", "2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07",
				"2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12", "2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17",
				"2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22", "2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27",
				"2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01", "2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06",
				"2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11", "2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16",
				"2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21", "2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26",
				"2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04", "2014-01-05", "2014-01-06",
				"2014-01-07", "2014-01-08", "2014-01-09", "2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14", "2014-01-15", "2014-01-16",
				"2014-01-17", "2014-01-18", "2014-01-19", "2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24", "2014-01-25", "2014-01-26",
				"2014-01-27", "2014-01-28", "2014-01-29", "2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03", "2014-02-04", "2014-02-05",
				"2014-02-06", "2014-02-07", "2014-02-08", "2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13", "2014-02-14", "2014-02-15",
				"2014-02-16", "2014-02-17", "2014-02-18", "2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23", "2014-02-24", "2014-02-25",
				"2014-02-26", "2014-02-27", "2014-02-28", "2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05", "2014-03-06", "2014-03-07",
				"2014-03-08", "2014-03-09", "2014-03-10", "2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15", "2014-03-16", "2014-03-17",
				"2014-03-18", "2014-03-19", "2014-03-20", "2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25", "2014-03-26", "2014-03-27",
				"2014-03-28", "2014-03-29", "2014-03-30", "2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04", "2014-04-05", "2014-04-06",
				"2014-04-07", "2014-04-08", "2014-04-09", "2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14", "2014-04-15", "2014-04-16",
				"2014-04-17", "2014-04-18", "2014-04-19", "2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24", "2014-04-25", "2014-04-26",
				"2014-04-27", "2014-04-28", "2014-04-29", "2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04", "2014-05-05", "2014-05-06",
				"2014-05-07", "2014-05-08", "2014-05-09", "2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14", "2014-05-15", "2014-05-16",
				"2014-05-17", "2014-05-18", "2014-05-19", "2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24", "2014-05-25", "2014-05-26",
				"2014-05-27", "2014-05-28", "2014-05-29", "2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03", "2014-06-04", "2014-06-05",
				"2014-06-06", "2014-06-07", "2014-06-08", "2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13", "2014-06-14", "2014-06-15",
				"2014-06-16", "2014-06-17", "2014-06-18", "2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23", "2014-06-24", "2014-06-25",
				"2014-06-26", "2014-06-27", "2014-06-28", "2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03", "2014-07-04", "2014-07-05",
				"2014-07-06", "2014-07-07", "2014-07-08", "2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13", "2014-07-14", "2014-07-15",
				"2014-07-16", "2014-07-17", "2014-07-18", "2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23", "2014-07-24", "2014-07-25",
				"2014-07-26", "2014-07-27", "2014-07-28", "2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02", "2014-08-03", "2014-08-04",
				"2014-08-05", "2014-08-06", "2014-08-07", "2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12", "2014-08-13", "2014-08-14",
				"2014-08-15", "2014-08-16", "2014-08-17", "2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22", "2014-08-23", "2014-08-24",
				"2014-08-25", "2014-08-26", "2014-08-27", "2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01", "2014-09-02", "2014-09-03",
				"2014-09-04", "2014-09-05", "2014-09-06", "2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11", "2014-09-12", "2014-09-13",
				"2014-09-14", "2014-09-15", "2014-09-16", "2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21", "2014-09-22", "2014-09-23",
				"2014-09-24", "2014-09-25", "2014-09-26", "2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-01-01", "2014-01-02", "2014-01-03",
				"2014-01-04", "2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09", "2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13",
				"2014-01-14", "2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19", "2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23",
				"2014-01-24", "2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29", "2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02",
				"2014-02-03", "2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08", "2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12",
				"2014-02-13", "2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18", "2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22",
				"2014-02-23", "2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28", "2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04",
				"2014-03-05", "2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10", "2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14",
				"2014-03-15", "2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20", "2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24",
				"2014-03-25", "2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30", "2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03",
				"2014-04-04", "2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09", "2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13",
				"2014-04-14", "2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19", "2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23",
				"2014-04-24", "2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29", "2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03",
				"2014-05-04", "2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09", "2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13",
				"2014-05-14", "2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19", "2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23",
				"2014-05-24", "2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29", "2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02",
				"2014-06-03", "2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08", "2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12",
				"2014-06-13", "2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18", "2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22",
				"2014-06-23", "2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28", "2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02",
				"2014-07-03", "2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08", "2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12",
				"2014-07-13", "2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18", "2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22",
				"2014-07-23", "2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28", "2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01",
				"2014-08-02", "2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07", "2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11",
				"2014-08-12", "2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17", "2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21",
				"2014-08-22", "2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27", "2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31",
				"2014-09-01", "2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06", "2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10",
				"2014-09-11", "2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16", "2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20",
				"2014-09-21", "2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26", "2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30",
				"2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04", "2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09", "2014-01-10",
				"2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14", "2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19", "2014-01-20",
				"2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24", "2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29", "2014-01-30",
				"2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03", "2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08", "2014-02-09",
				"2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13", "2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18", "2014-02-19",
				"2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23", "2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28", "2014-03-01",
				"2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05", "2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10", "2014-03-11",
				"2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15", "2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20", "2014-03-21",
				"2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25", "2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30", "2014-03-31",
				"2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04", "2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09", "2014-04-10",
				"2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14", "2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19", "2014-04-20",
				"2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24", "2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29", "2014-04-30",
				"2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04", "2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09", "2014-05-10",
				"2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14", "2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19", "2014-05-20",
				"2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24", "2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29", "2014-05-30",
				"2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03", "2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08", "2014-06-09",
				"2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13", "2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18", "2014-06-19",
				"2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23", "2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28", "2014-06-29",
				"2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03", "2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08", "2014-07-09",
				"2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13", "2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18", "2014-07-19",
				"2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23", "2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28", "2014-07-29",
				"2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02", "2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07", "2014-08-08",
				"2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12", "2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17", "2014-08-18",
				"2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22", "2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27", "2014-08-28",
				"2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01", "2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06", "2014-09-07",
				"2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11", "2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16", "2014-09-17",
				"2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21", "2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26", "2014-09-27",
				"2014-09-28", "2014-09-29", "2014-09-30", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04", "2014-01-05", "2014-01-06", "2014-01-07",
				"2014-01-08", "2014-01-09", "2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14", "2014-01-15", "2014-01-16", "2014-01-17",
				"2014-01-18", "2014-01-19", "2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24", "2014-01-25", "2014-01-26", "2014-01-27",
				"2014-01-28", "2014-01-29", "2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03", "2014-02-04", "2014-02-05", "2014-02-06",
				"2014-02-07", "2014-02-08", "2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13", "2014-02-14", "2014-02-15", "2014-02-16",
				"2014-02-17", "2014-02-18", "2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23", "2014-02-24", "2014-02-25", "2014-02-26",
				"2014-02-27", "2014-02-28", "2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05", "2014-03-06", "2014-03-07", "2014-03-08",
				"2014-03-09", "2014-03-10", "2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15", "2014-03-16", "2014-03-17", "2014-03-18",
				"2014-03-19", "2014-03-20", "2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25", "2014-03-26", "2014-03-27", "2014-03-28",
				"2014-03-29", "2014-03-30", "2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04", "2014-04-05", "2014-04-06", "2014-04-07",
				"2014-04-08", "2014-04-09", "2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14", "2014-04-15", "2014-04-16", "2014-04-17",
				"2014-04-18", "2014-04-19", "2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24", "2014-04-25", "2014-04-26", "2014-04-27",
				"2014-04-28", "2014-04-29", "2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04", "2014-05-05", "2014-05-06", "2014-05-07",
				"2014-05-08", "2014-05-09", "2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14", "2014-05-15", "2014-05-16", "2014-05-17",
				"2014-05-18", "2014-05-19", "2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24", "2014-05-25", "2014-05-26", "2014-05-27",
				"2014-05-28", "2014-05-29", "2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03", "2014-06-04", "2014-06-05", "2014-06-06",
				"2014-06-07", "2014-06-08", "2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13", "2014-06-14", "2014-06-15", "2014-06-16",
				"2014-06-17", "2014-06-18", "2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23", "2014-06-24", "2014-06-25", "2014-06-26",
				"2014-06-27", "2014-06-28", "2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03", "2014-07-04", "2014-07-05", "2014-07-06",
				"2014-07-07", "2014-07-08", "2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13", "2014-07-14", "2014-07-15", "2014-07-16",
				"2014-07-17", "2014-07-18", "2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23", "2014-07-24", "2014-07-25", "2014-07-26",
				"2014-07-27", "2014-07-28", "2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02", "2014-08-03", "2014-08-04", "2014-08-05",
				"2014-08-06", "2014-08-07", "2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12", "2014-08-13", "2014-08-14", "2014-08-15",
				"2014-08-16", "2014-08-17", "2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22", "2014-08-23", "2014-08-24", "2014-08-25",
				"2014-08-26", "2014-08-27", "2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01", "2014-09-02", "2014-09-03", "2014-09-04",
				"2014-09-05", "2014-09-06", "2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11", "2014-09-12", "2014-09-13", "2014-09-14",
				"2014-09-15", "2014-09-16", "2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21", "2014-09-22", "2014-09-23", "2014-09-24",
				"2014-09-25", "2014-09-26", "2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04",
				"2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09", "2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14",
				"2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19", "2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24",
				"2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29", "2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03",
				"2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08", "2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13",
				"2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18", "2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23",
				"2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28", "2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05",
				"2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10", "2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15",
				"2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20", "2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25",
				"2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30", "2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04",
				"2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09", "2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14",
				"2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19", "2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24",
				"2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29", "2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04",
				"2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09", "2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14",
				"2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19", "2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24",
				"2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29", "2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03",
				"2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08", "2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13",
				"2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18", "2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23",
				"2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28", "2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03",
				"2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08", "2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13",
				"2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18", "2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23",
				"2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28", "2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02",
				"2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07", "2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12",
				"2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17", "2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22",
				"2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27", "2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01",
				"2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06", "2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11",
				"2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16", "2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21",
				"2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26", "2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-01-01",
				"2014-01-02", "2014-01-03", "2014-01-04", "2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09", "2014-01-10", "2014-01-11",
				"2014-01-12", "2014-01-13", "2014-01-14", "2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19", "2014-01-20", "2014-01-21",
				"2014-01-22", "2014-01-23", "2014-01-24", "2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29", "2014-01-30", "2014-01-31",
				"2014-02-01", "2014-02-02", "2014-02-03", "2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08", "2014-02-09", "2014-02-10",
				"2014-02-11", "2014-02-12", "2014-02-13", "2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18", "2014-02-19", "2014-02-20",
				"2014-02-21", "2014-02-22", "2014-02-23", "2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28", "2014-03-01", "2014-03-02",
				"2014-03-03", "2014-03-04", "2014-03-05", "2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10", "2014-03-11", "2014-03-12",
				"2014-03-13", "2014-03-14", "2014-03-15", "2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20", "2014-03-21", "2014-03-22",
				"2014-03-23", "2014-03-24", "2014-03-25", "2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30", "2014-03-31", "2014-04-01",
				"2014-04-02", "2014-04-03", "2014-04-04", "2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09", "2014-04-10", "2014-04-11",
				"2014-04-12", "2014-04-13", "2014-04-14", "2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19", "2014-04-20", "2014-04-21",
				"2014-04-22", "2014-04-23", "2014-04-24", "2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29", "2014-04-30", "2014-05-01",
				"2014-05-02", "2014-05-03", "2014-05-04", "2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09", "2014-05-10", "2014-05-11",
				"2014-05-12", "2014-05-13", "2014-05-14", "2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19", "2014-05-20", "2014-05-21",
				"2014-05-22", "2014-05-23", "2014-05-24", "2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29", "2014-05-30", "2014-05-31",
				"2014-06-01", "2014-06-02", "2014-06-03", "2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08", "2014-06-09", "2014-06-10",
				"2014-06-11", "2014-06-12", "2014-06-13", "2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18", "2014-06-19", "2014-06-20",
				"2014-06-21", "2014-06-22", "2014-06-23", "2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28", "2014-06-29", "2014-06-30",
				"2014-07-01", "2014-07-02", "2014-07-03", "2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08", "2014-07-09", "2014-07-10",
				"2014-07-11", "2014-07-12", "2014-07-13", "2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18", "2014-07-19", "2014-07-20",
				"2014-07-21", "2014-07-22", "2014-07-23", "2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28", "2014-07-29", "2014-07-30",
				"2014-07-31", "2014-08-01", "2014-08-02", "2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07", "2014-08-08", "2014-08-09",
				"2014-08-10", "2014-08-11", "2014-08-12", "2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17", "2014-08-18", "2014-08-19",
				"2014-08-20", "2014-08-21", "2014-08-22", "2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27", "2014-08-28", "2014-08-29",
				"2014-08-30", "2014-08-31", "2014-09-01", "2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06", "2014-09-07", "2014-09-08",
				"2014-09-09", "2014-09-10", "2014-09-11", "2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16", "2014-09-17", "2014-09-18",
				"2014-09-19", "2014-09-20", "2014-09-21", "2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26", "2014-09-27", "2014-09-28",
				"2014-09-29", "2014-09-30", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04", "2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08",
				"2014-01-09", "2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14", "2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18",
				"2014-01-19", "2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24", "2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28",
				"2014-01-29", "2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03", "2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07",
				"2014-02-08", "2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13", "2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17",
				"2014-02-18", "2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23", "2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27",
				"2014-02-28", "2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05", "2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09",
				"2014-03-10", "2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15", "2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19",
				"2014-03-20", "2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25", "2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29",
				"2014-03-30", "2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04", "2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08",
				"2014-04-09", "2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14", "2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18",
				"2014-04-19", "2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24", "2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28",
				"2014-04-29", "2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04", "2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08",
				"2014-05-09", "2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14", "2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18",
				"2014-05-19", "2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24", "2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28",
				"2014-05-29", "2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03", "2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07",
				"2014-06-08", "2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13", "2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17",
				"2014-06-18", "2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23", "2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27",
				"2014-06-28", "2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03", "2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07",
				"2014-07-08", "2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13", "2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17",
				"2014-07-18", "2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23", "2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27",
				"2014-07-28", "2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02", "2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06",
				"2014-08-07", "2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12", "2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16",
				"2014-08-17", "2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22", "2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26",
				"2014-08-27", "2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01", "2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05",
				"2014-09-06", "2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11", "2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15",
				"2014-09-16", "2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21", "2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25",
				"2014-09-26", "2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04", "2014-01-05",
				"2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09", "2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14", "2014-01-15",
				"2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19", "2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24", "2014-01-25",
				"2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29", "2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03", "2014-02-04",
				"2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08", "2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13", "2014-02-14",
				"2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18", "2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23", "2014-02-24",
				"2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28", "2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05", "2014-03-06",
				"2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10", "2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15", "2014-03-16",
				"2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20", "2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25", "2014-03-26",
				"2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30", "2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04", "2014-04-05",
				"2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09", "2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14", "2014-04-15",
				"2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19", "2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24", "2014-04-25",
				"2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29", "2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04", "2014-05-05",
				"2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09", "2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14", "2014-05-15",
				"2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19", "2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24", "2014-05-25",
				"2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29", "2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03", "2014-06-04",
				"2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08", "2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13", "2014-06-14",
				"2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18", "2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23", "2014-06-24",
				"2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28", "2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03", "2014-07-04",
				"2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08", "2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13", "2014-07-14",
				"2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18", "2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23", "2014-07-24",
				"2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28", "2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02", "2014-08-03",
				"2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07", "2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12", "2014-08-13",
				"2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17", "2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22", "2014-08-23",
				"2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27", "2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01", "2014-09-02",
				"2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06", "2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11", "2014-09-12",
				"2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16", "2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21", "2014-09-22",
				"2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26", "2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-01-01", "2014-01-02",
				"2014-01-03", "2014-01-04", "2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09", "2014-01-10", "2014-01-11", "2014-01-12",
				"2014-01-13", "2014-01-14", "2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19", "2014-01-20", "2014-01-21", "2014-01-22",
				"2014-01-23", "2014-01-24", "2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29", "2014-01-30", "2014-01-31", "2014-02-01",
				"2014-02-02", "2014-02-03", "2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08", "2014-02-09", "2014-02-10", "2014-02-11",
				"2014-02-12", "2014-02-13", "2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18", "2014-02-19", "2014-02-20", "2014-02-21",
				"2014-02-22", "2014-02-23", "2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28", "2014-03-01", "2014-03-02", "2014-03-03",
				"2014-03-04", "2014-03-05", "2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10", "2014-03-11", "2014-03-12", "2014-03-13",
				"2014-03-14", "2014-03-15", "2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20", "2014-03-21", "2014-03-22", "2014-03-23",
				"2014-03-24", "2014-03-25", "2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30", "2014-03-31", "2014-04-01", "2014-04-02",
				"2014-04-03", "2014-04-04", "2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09", "2014-04-10", "2014-04-11", "2014-04-12",
				"2014-04-13", "2014-04-14", "2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19", "2014-04-20", "2014-04-21", "2014-04-22",
				"2014-04-23", "2014-04-24", "2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29", "2014-04-30", "2014-05-01", "2014-05-02",
				"2014-05-03", "2014-05-04", "2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09", "2014-05-10", "2014-05-11", "2014-05-12",
				"2014-05-13", "2014-05-14", "2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19", "2014-05-20", "2014-05-21", "2014-05-22",
				"2014-05-23", "2014-05-24", "2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29", "2014-05-30", "2014-05-31", "2014-06-01",
				"2014-06-02", "2014-06-03", "2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08", "2014-06-09", "2014-06-10", "2014-06-11",
				"2014-06-12", "2014-06-13", "2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18", "2014-06-19", "2014-06-20", "2014-06-21",
				"2014-06-22", "2014-06-23", "2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28", "2014-06-29", "2014-06-30", "2014-07-01",
				"2014-07-02", "2014-07-03", "2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08", "2014-07-09", "2014-07-10", "2014-07-11",
				"2014-07-12", "2014-07-13", "2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18", "2014-07-19", "2014-07-20", "2014-07-21",
				"2014-07-22", "2014-07-23", "2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28", "2014-07-29", "2014-07-30", "2014-07-31",
				"2014-08-01", "2014-08-02", "2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07", "2014-08-08", "2014-08-09", "2014-08-10",
				"2014-08-11", "2014-08-12", "2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17", "2014-08-18", "2014-08-19", "2014-08-20",
				"2014-08-21", "2014-08-22", "2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27", "2014-08-28", "2014-08-29", "2014-08-30",
				"2014-08-31", "2014-09-01", "2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06", "2014-09-07", "2014-09-08", "2014-09-09",
				"2014-09-10", "2014-09-11", "2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16", "2014-09-17", "2014-09-18", "2014-09-19",
				"2014-09-20", "2014-09-21", "2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26", "2014-09-27", "2014-09-28", "2014-09-29",
				"2014-09-30", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04", "2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09",
				"2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14", "2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19",
				"2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24", "2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29",
				"2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03", "2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08",
				"2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13", "2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18",
				"2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23", "2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28",
				"2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05", "2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10",
				"2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15", "2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20",
				"2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25", "2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30",
				"2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04", "2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09",
				"2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14", "2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19",
				"2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24", "2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29",
				"2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04", "2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09",
				"2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14", "2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19",
				"2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24", "2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29",
				"2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03", "2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08",
				"2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13", "2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18",
				"2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23", "2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28",
				"2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03", "2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08",
				"2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13", "2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18",
				"2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23", "2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28",
				"2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02", "2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07",
				"2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12", "2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17",
				"2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22", "2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27",
				"2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01", "2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06",
				"2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11", "2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16",
				"2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21", "2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26",
				"2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04", "2014-01-05", "2014-01-06",
				"2014-01-07", "2014-01-08", "2014-01-09", "2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14", "2014-01-15", "2014-01-16",
				"2014-01-17", "2014-01-18", "2014-01-19", "2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24", "2014-01-25", "2014-01-26",
				"2014-01-27", "2014-01-28", "2014-01-29", "2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03", "2014-02-04", "2014-02-05",
				"2014-02-06", "2014-02-07", "2014-02-08", "2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13", "2014-02-14", "2014-02-15",
				"2014-02-16", "2014-02-17", "2014-02-18", "2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23", "2014-02-24", "2014-02-25",
				"2014-02-26", "2014-02-27", "2014-02-28", "2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05", "2014-03-06", "2014-03-07",
				"2014-03-08", "2014-03-09", "2014-03-10", "2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15", "2014-03-16", "2014-03-17",
				"2014-03-18", "2014-03-19", "2014-03-20", "2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25", "2014-03-26", "2014-03-27",
				"2014-03-28", "2014-03-29", "2014-03-30", "2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04", "2014-04-05", "2014-04-06",
				"2014-04-07", "2014-04-08", "2014-04-09", "2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14", "2014-04-15", "2014-04-16",
				"2014-04-17", "2014-04-18", "2014-04-19", "2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24", "2014-04-25", "2014-04-26",
				"2014-04-27", "2014-04-28", "2014-04-29", "2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04", "2014-05-05", "2014-05-06",
				"2014-05-07", "2014-05-08", "2014-05-09", "2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14", "2014-05-15", "2014-05-16",
				"2014-05-17", "2014-05-18", "2014-05-19", "2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24", "2014-05-25", "2014-05-26",
				"2014-05-27", "2014-05-28", "2014-05-29", "2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03", "2014-06-04", "2014-06-05",
				"2014-06-06", "2014-06-07", "2014-06-08", "2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13", "2014-06-14", "2014-06-15",
				"2014-06-16", "2014-06-17", "2014-06-18", "2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23", "2014-06-24", "2014-06-25",
				"2014-06-26", "2014-06-27", "2014-06-28", "2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03", "2014-07-04", "2014-07-05",
				"2014-07-06", "2014-07-07", "2014-07-08", "2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13", "2014-07-14", "2014-07-15",
				"2014-07-16", "2014-07-17", "2014-07-18", "2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23", "2014-07-24", "2014-07-25",
				"2014-07-26", "2014-07-27", "2014-07-28", "2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02", "2014-08-03", "2014-08-04",
				"2014-08-05", "2014-08-06", "2014-08-07", "2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12", "2014-08-13", "2014-08-14",
				"2014-08-15", "2014-08-16", "2014-08-17", "2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22", "2014-08-23", "2014-08-24",
				"2014-08-25", "2014-08-26", "2014-08-27", "2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01", "2014-09-02", "2014-09-03",
				"2014-09-04", "2014-09-05", "2014-09-06", "2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11", "2014-09-12", "2014-09-13",
				"2014-09-14", "2014-09-15", "2014-09-16", "2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21", "2014-09-22", "2014-09-23",
				"2014-09-24", "2014-09-25", "2014-09-26", "2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-01-01", "2014-01-02", "2014-01-03",
				"2014-01-04", "2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09", "2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13",
				"2014-01-14", "2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19", "2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23",
				"2014-01-24", "2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29", "2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02",
				"2014-02-03", "2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08", "2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12",
				"2014-02-13", "2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18", "2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22",
				"2014-02-23", "2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28", "2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04",
				"2014-03-05", "2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10", "2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14",
				"2014-03-15", "2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20", "2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24",
				"2014-03-25", "2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30", "2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03",
				"2014-04-04", "2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09", "2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13",
				"2014-04-14", "2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19", "2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23",
				"2014-04-24", "2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29", "2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03",
				"2014-05-04", "2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09", "2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13",
				"2014-05-14", "2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19", "2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23",
				"2014-05-24", "2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29", "2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02",
				"2014-06-03", "2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08", "2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12",
				"2014-06-13", "2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18", "2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22",
				"2014-06-23", "2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28", "2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02",
				"2014-07-03", "2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08", "2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12",
				"2014-07-13", "2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18", "2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22",
				"2014-07-23", "2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28", "2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01",
				"2014-08-02", "2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07", "2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11",
				"2014-08-12", "2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17", "2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21",
				"2014-08-22", "2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27", "2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31",
				"2014-09-01", "2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06", "2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10",
				"2014-09-11", "2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16", "2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20",
				"2014-09-21", "2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26", "2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30",
				"2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04", "2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09", "2014-01-10",
				"2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14", "2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19", "2014-01-20",
				"2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24", "2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29", "2014-01-30",
				"2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03", "2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08", "2014-02-09",
				"2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13", "2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18", "2014-02-19",
				"2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23", "2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28", "2014-03-01",
				"2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05", "2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10", "2014-03-11",
				"2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15", "2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20", "2014-03-21",
				"2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25", "2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30", "2014-03-31",
				"2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04", "2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09", "2014-04-10",
				"2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14", "2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19", "2014-04-20",
				"2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24", "2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29", "2014-04-30",
				"2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04", "2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09", "2014-05-10",
				"2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14", "2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19", "2014-05-20",
				"2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24", "2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29", "2014-05-30",
				"2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03", "2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08", "2014-06-09",
				"2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13", "2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18", "2014-06-19",
				"2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23", "2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28", "2014-06-29",
				"2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03", "2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08", "2014-07-09",
				"2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13", "2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18", "2014-07-19",
				"2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23", "2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28", "2014-07-29",
				"2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02", "2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07", "2014-08-08",
				"2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12", "2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17", "2014-08-18",
				"2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22", "2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27", "2014-08-28",
				"2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01", "2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06", "2014-09-07",
				"2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11", "2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16", "2014-09-17",
				"2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21", "2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26", "2014-09-27",
				"2014-09-28", "2014-09-29", "2014-09-30", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04", "2014-01-05", "2014-01-06", "2014-01-07",
				"2014-01-08", "2014-01-09", "2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14", "2014-01-15", "2014-01-16", "2014-01-17",
				"2014-01-18", "2014-01-19", "2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24", "2014-01-25", "2014-01-26", "2014-01-27",
				"2014-01-28", "2014-01-29", "2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03", "2014-02-04", "2014-02-05", "2014-02-06",
				"2014-02-07", "2014-02-08", "2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13", "2014-02-14", "2014-02-15", "2014-02-16",
				"2014-02-17", "2014-02-18", "2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23", "2014-02-24", "2014-02-25", "2014-02-26",
				"2014-02-27", "2014-02-28", "2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05", "2014-03-06", "2014-03-07", "2014-03-08",
				"2014-03-09", "2014-03-10", "2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15", "2014-03-16", "2014-03-17", "2014-03-18",
				"2014-03-19", "2014-03-20", "2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25", "2014-03-26", "2014-03-27", "2014-03-28",
				"2014-03-29", "2014-03-30", "2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04", "2014-04-05", "2014-04-06", "2014-04-07",
				"2014-04-08", "2014-04-09", "2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14", "2014-04-15", "2014-04-16", "2014-04-17",
				"2014-04-18", "2014-04-19", "2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24", "2014-04-25", "2014-04-26", "2014-04-27",
				"2014-04-28", "2014-04-29", "2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04", "2014-05-05", "2014-05-06", "2014-05-07",
				"2014-05-08", "2014-05-09", "2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14", "2014-05-15", "2014-05-16", "2014-05-17",
				"2014-05-18", "2014-05-19", "2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24", "2014-05-25", "2014-05-26", "2014-05-27",
				"2014-05-28", "2014-05-29", "2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03", "2014-06-04", "2014-06-05", "2014-06-06",
				"2014-06-07", "2014-06-08", "2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13", "2014-06-14", "2014-06-15", "2014-06-16",
				"2014-06-17", "2014-06-18", "2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23", "2014-06-24", "2014-06-25", "2014-06-26",
				"2014-06-27", "2014-06-28", "2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03", "2014-07-04", "2014-07-05", "2014-07-06",
				"2014-07-07", "2014-07-08", "2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13", "2014-07-14", "2014-07-15", "2014-07-16",
				"2014-07-17", "2014-07-18", "2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23", "2014-07-24", "2014-07-25", "2014-07-26",
				"2014-07-27", "2014-07-28", "2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02", "2014-08-03", "2014-08-04", "2014-08-05",
				"2014-08-06", "2014-08-07", "2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12", "2014-08-13", "2014-08-14", "2014-08-15",
				"2014-08-16", "2014-08-17", "2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22", "2014-08-23", "2014-08-24", "2014-08-25",
				"2014-08-26", "2014-08-27", "2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01", "2014-09-02", "2014-09-03", "2014-09-04",
				"2014-09-05", "2014-09-06", "2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11", "2014-09-12", "2014-09-13", "2014-09-14",
				"2014-09-15", "2014-09-16", "2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21", "2014-09-22", "2014-09-23", "2014-09-24",
				"2014-09-25", "2014-09-26", "2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04",
				"2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09", "2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14",
				"2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19", "2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24",
				"2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29", "2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03",
				"2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08", "2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13",
				"2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18", "2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23",
				"2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28", "2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05",
				"2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10", "2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15",
				"2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20", "2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25",
				"2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30", "2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04",
				"2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09", "2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14",
				"2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19", "2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24",
				"2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29", "2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04",
				"2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09", "2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14",
				"2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19", "2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24",
				"2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29", "2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03",
				"2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08", "2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13",
				"2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18", "2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23",
				"2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28", "2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03",
				"2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08", "2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13",
				"2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18", "2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23",
				"2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28", "2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02",
				"2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07", "2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12",
				"2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17", "2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22",
				"2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27", "2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01",
				"2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06", "2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11",
				"2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16", "2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21",
				"2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26", "2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-01-01",
				"2014-01-02", "2014-01-03", "2014-01-04", "2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09", "2014-01-10", "2014-01-11",
				"2014-01-12", "2014-01-13", "2014-01-14", "2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19", "2014-01-20", "2014-01-21",
				"2014-01-22", "2014-01-23", "2014-01-24", "2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29", "2014-01-30", "2014-01-31",
				"2014-02-01", "2014-02-02", "2014-02-03", "2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08", "2014-02-09", "2014-02-10",
				"2014-02-11", "2014-02-12", "2014-02-13", "2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18", "2014-02-19", "2014-02-20",
				"2014-02-21", "2014-02-22", "2014-02-23", "2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28", "2014-03-01", "2014-03-02",
				"2014-03-03", "2014-03-04", "2014-03-05", "2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10", "2014-03-11", "2014-03-12",
				"2014-03-13", "2014-03-14", "2014-03-15", "2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20", "2014-03-21", "2014-03-22",
				"2014-03-23", "2014-03-24", "2014-03-25", "2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30", "2014-03-31", "2014-04-01",
				"2014-04-02", "2014-04-03", "2014-04-04", "2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09", "2014-04-10", "2014-04-11",
				"2014-04-12", "2014-04-13", "2014-04-14", "2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19", "2014-04-20", "2014-04-21",
				"2014-04-22", "2014-04-23", "2014-04-24", "2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29", "2014-04-30", "2014-05-01",
				"2014-05-02", "2014-05-03", "2014-05-04", "2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09", "2014-05-10", "2014-05-11",
				"2014-05-12", "2014-05-13", "2014-05-14", "2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19", "2014-05-20", "2014-05-21",
				"2014-05-22", "2014-05-23", "2014-05-24", "2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29", "2014-05-30", "2014-05-31",
				"2014-06-01", "2014-06-02", "2014-06-03", "2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08", "2014-06-09", "2014-06-10",
				"2014-06-11", "2014-06-12", "2014-06-13", "2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18", "2014-06-19", "2014-06-20",
				"2014-06-21", "2014-06-22", "2014-06-23", "2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28", "2014-06-29", "2014-06-30",
				"2014-07-01", "2014-07-02", "2014-07-03", "2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08", "2014-07-09", "2014-07-10",
				"2014-07-11", "2014-07-12", "2014-07-13", "2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18", "2014-07-19", "2014-07-20",
				"2014-07-21", "2014-07-22", "2014-07-23", "2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28", "2014-07-29", "2014-07-30",
				"2014-07-31", "2014-08-01", "2014-08-02", "2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07", "2014-08-08", "2014-08-09",
				"2014-08-10", "2014-08-11", "2014-08-12", "2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17", "2014-08-18", "2014-08-19",
				"2014-08-20", "2014-08-21", "2014-08-22", "2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27", "2014-08-28", "2014-08-29",
				"2014-08-30", "2014-08-31", "2014-09-01", "2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06", "2014-09-07", "2014-09-08",
				"2014-09-09", "2014-09-10", "2014-09-11", "2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16", "2014-09-17", "2014-09-18",
				"2014-09-19", "2014-09-20", "2014-09-21", "2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26", "2014-09-27", "2014-09-28",
				"2014-09-29", "2014-09-30", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04", "2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08",
				"2014-01-09", "2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14", "2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18",
				"2014-01-19", "2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24", "2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28",
				"2014-01-29", "2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03", "2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07",
				"2014-02-08", "2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13", "2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17",
				"2014-02-18", "2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23", "2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27",
				"2014-02-28", "2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05", "2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09",
				"2014-03-10", "2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15", "2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19",
				"2014-03-20", "2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25", "2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29",
				"2014-03-30", "2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04", "2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08",
				"2014-04-09", "2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14", "2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18",
				"2014-04-19", "2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24", "2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28",
				"2014-04-29", "2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04", "2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08",
				"2014-05-09", "2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14", "2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18",
				"2014-05-19", "2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24", "2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28",
				"2014-05-29", "2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03", "2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07",
				"2014-06-08", "2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13", "2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17",
				"2014-06-18", "2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23", "2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27",
				"2014-06-28", "2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03", "2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07",
				"2014-07-08", "2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13", "2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17",
				"2014-07-18", "2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23", "2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27",
				"2014-07-28", "2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02", "2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06",
				"2014-08-07", "2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12", "2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16",
				"2014-08-17", "2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22", "2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26",
				"2014-08-27", "2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01", "2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05",
				"2014-09-06", "2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11", "2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15",
				"2014-09-16", "2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21", "2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25",
				"2014-09-26", "2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04", "2014-01-05",
				"2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09", "2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14", "2014-01-15",
				"2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19", "2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24", "2014-01-25",
				"2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29", "2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03", "2014-02-04",
				"2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08", "2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13", "2014-02-14",
				"2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18", "2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23", "2014-02-24",
				"2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28", "2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05", "2014-03-06",
				"2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10", "2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15", "2014-03-16",
				"2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20", "2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25", "2014-03-26",
				"2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30", "2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04", "2014-04-05",
				"2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09", "2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14", "2014-04-15",
				"2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19", "2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24", "2014-04-25",
				"2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29", "2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04", "2014-05-05",
				"2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09", "2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14", "2014-05-15",
				"2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19", "2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24", "2014-05-25",
				"2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29", "2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03", "2014-06-04",
				"2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08", "2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13", "2014-06-14",
				"2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18", "2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23", "2014-06-24",
				"2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28", "2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03", "2014-07-04",
				"2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08", "2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13", "2014-07-14",
				"2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18", "2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23", "2014-07-24",
				"2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28", "2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02", "2014-08-03",
				"2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07", "2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12", "2014-08-13",
				"2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17", "2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22", "2014-08-23",
				"2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27", "2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01", "2014-09-02",
				"2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06", "2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11", "2014-09-12",
				"2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16", "2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21", "2014-09-22",
				"2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26", "2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", };
	}

	private int[] getIntArrayForMarketMatching36() {
		return new int[] { 49, 53, 54, 55, 55, 52, 54, 56, 56, 54, 52, 53, 55, 58, 61, 60, 56, 55, 55, 55, 56, 55, 57, 55, 57, 57, 54, 59, 59, 55, 54, 51, 53, 51, 50,
				49, 50, 54, 56, 57, 54, 53, 51, 59, 61, 57, 57, 54, 55, 57, 56, 58, 56, 55, 58, 55, 57, 60, 58, 58, 56, 55, 58, 62, 59, 57, 58, 65, 61, 63, 65, 64, 61,
				64, 60, 59, 60, 61, 60, 57, 56, 57, 60, 55, 58, 60, 63, 57, 56, 53, 54, 54, 57, 56, 57, 62, 69, 67, 58, 60, 59, 59, 63, 60, 59, 63, 59, 58, 57, 65, 61,
				58, 59, 61, 63, 55, 59, 61, 70, 75, 74, 64, 61, 63, 61, 60, 58, 60, 60, 60, 63, 68, 76, 77, 71, 64, 62, 63, 62, 64, 64, 62, 62, 61, 63, 64, 61, 63, 66,
				59, 60, 62, 60, 60, 61, 62, 63, 62, 71, 68, 66, 65, 62, 64, 70, 63, 60, 64, 71, 64, 66, 61, 62, 64, 63, 64, 65, 67, 66, 69, 71, 67, 67, 65, 62, 62, 61,
				63, 66, 67, 68, 66, 62, 61, 67, 70, 70, 69, 68, 68, 68, 71, 71, 70, 72, 76, 69, 69, 67, 67, 65, 65, 66, 64, 65, 68, 68, 69, 65, 67, 65, 69, 70, 70, 69,
				67, 67, 66, 65, 65, 68, 68, 68, 68, 66, 67, 68, 67, 67, 67, 66, 72, 69, 72, 67, 68, 68, 68, 66, 64, 63, 65, 69, 69, 67, 68, 67, 70, 72, 72, 72, 69, 71,
				69, 72, 71, 72, 68, 68, 68, 69, 66, 69, 29, 26, 14, 17, 36, 37, 13, 17, 28, 31, 46, 43, 40, 46, 38, 39, 37, 35, 32, 41, 23, 13, 15, 16, 25, 27, 34, 18,
				20, 21, 34, 37, 41, 36, 29, 33, 28, 29, 25, 25, 25, 21, 20, 31, 37, 33, 26, 26, 33, 36, 39, 41, 44, 45, 37, 31, 27, 25, 19, 27, 37, 27, 21, 33, 24, 32,
				46, 42, 43, 52, 46, 26, 32, 51, 37, 30, 34, 37, 49, 44, 50, 37, 29, 31, 32, 31, 44, 47, 48, 45, 47, 43, 51, 43, 49, 47, 44, 57, 54, 46, 58, 59, 56, 59,
				45, 41, 41, 41, 54, 48, 48, 54, 54, 54, 51, 57, 54, 56, 48, 49, 62, 61, 58, 60, 60, 60, 57, 55, 60, 66, 70, 66, 61, 57, 66, 63, 63, 61, 62, 67, 68, 60,
				63, 64, 67, 73, 74, 60, 57, 62, 64, 65, 66, 71, 73, 70, 71, 71, 73, 66, 73, 66, 69, 74, 71, 71, 71, 75, 83, 73, 74, 69, 69, 72, 72, 76, 80, 72, 76, 72,
				74, 78, 78, 78, 71, 75, 75, 77, 80, 78, 78, 76, 74, 75, 77, 76, 77, 75, 75, 74, 73, 74, 76, 77, 78, 75, 75, 78, 77, 72, 72, 74, 77, 71, 72, 76, 78, 78,
				77, 75, 76, 78, 76, 76, 77, 73, 69, 70, 76, 73, 72, 73, 73, 73, 70, 72, 75, 75, 77, 76, 71, 71, 79, 80, 83, 80, 77, 78, 77, 74, 72, 70, 71, 77, 71, 65,
				63, 63, 64, 64, 68, 62, 65, 72, 64, 60, 64, 62, 69, 73, 70, 71, 66, 55, 64, 56, 58, 62, 64, 60, 58, 55, 57, 57, 60, 65, 65, 68, 69, 70, 67, 63, 64, 64,
				63, 60, 65, 67, 60, 60, 57, 57, 60, 60, 61, 54, 53, 55, 57, 57, 56, 58, 61, 60, 61, 63, 63, 62, 63, 63, 58, 59, 61, 64, 65, 61, 59, 59, 61, 62, 62, 61,
				59, 59, 58, 60, 60, 63, 62, 65, 73, 67, 66, 68, 62, 60, 64, 70, 64, 64, 64, 63, 62, 62, 60, 62, 62, 62, 59, 60, 63, 62, 58, 57, 56, 57, 58, 59, 62, 69,
				69, 65, 59, 63, 61, 60, 62, 59, 63, 64, 66, 63, 63, 63, 62, 63, 65, 63, 60, 60, 65, 73, 76, 77, 77, 72, 67, 64, 60, 61, 62, 62, 64, 75, 73, 79, 81, 84,
				79, 70, 67, 66, 64, 64, 64, 66, 66, 68, 68, 69, 69, 69, 68, 68, 67, 65, 66, 66, 66, 66, 66, 66, 64, 67, 68, 67, 66, 67, 68, 67, 67, 67, 67, 69, 70, 69,
				70, 70, 69, 70, 71, 72, 72, 71, 71, 70, 71, 72, 75, 74, 73, 72, 72, 71, 70, 71, 72, 72, 72, 72, 71, 71, 69, 72, 71, 72, 73, 74, 76, 71, 71, 74, 73, 75,
				72, 71, 72, 73, 72, 68, 68, 70, 71, 71, 71, 70, 71, 70, 71, 70, 72, 72, 72, 73, 72, 71, 72, 71, 71, 69, 71, 75, 76, 73, 72, 74, 74, 74, 73, 72, 71, 73,
				76, 78, 76, 72, 75, 75, 76, 78, 79, 82, 78, 72, 72, 71, 71, 69, 70, 71, 72, 71, 68, 70, 68, 70, 47, 48, 47, 43, 44, 50, 50, 48, 46, 44, 40, 39, 45, 39,
				46, 46, 44, 47, 42, 40, 40, 46, 42, 40, 48, 42, 41, 45, 40, 40, 41, 44, 46, 42, 44, 46, 46, 44, 46, 44, 43, 41, 42, 42, 46, 46, 43, 44, 46, 44, 49, 44,
				46, 50, 51, 46, 45, 45, 41, 42, 43, 42, 42, 44, 48, 52, 52, 54, 47, 47, 48, 50, 49, 54, 55, 49, 50, 52, 50, 50, 46, 42, 42, 44, 42, 44, 48, 56, 56, 56,
				55, 58, 58, 54, 50, 57, 52, 49, 50, 52, 54, 50, 54, 51, 48, 50, 52, 48, 48, 50, 54, 55, 52, 56, 50, 53, 52, 56, 55, 55, 53, 49, 47, 50, 53, 59, 57, 56,
				58, 58, 52, 56, 54, 55, 58, 61, 63, 64, 68, 62, 58, 58, 56, 56, 56, 51, 53, 53, 61, 57, 56, 62, 62, 60, 52, 56, 60, 66, 66, 68, 64, 62, 65, 67, 65, 61,
				60, 62, 64, 62, 63, 65, 66, 68, 66, 61, 62, 65, 60, 60, 60, 63, 64, 68, 68, 66, 64, 61, 60, 64, 64, 62, 70, 68, 65, 72, 69, 74, 75, 74, 72, 70, 70, 72,
				73, 71, 74, 72, 66, 70, 68, 67, 68, 68, 64, 66, 66, 70, 68, 67, 65, 66, 63, 62, 62, 62, 61, 62, 62, 60, 58, 56, 54, 62, 57, 56, 59, 58, 60, 66, 64, 64,
				62, 60, 64, 66, 63, 64, 66, 63, 61, 62, 61, 58, 64, 62, 62, 62, 64, 64, 68, 68, 63, 58, 56, 56, 58, 56, 66, 62, 67, 60, 64, 78, 78, 67, 69, 76, 72, 54,
				61, 75, 77, 78, 74, 73, 77, 66, 56, 56, 61, 58, 65, 67, 59, 60, 66, 65, 67, 72, 72, 72, 66, 75, 77, 76, 79, 78, 80, 78, 76, 77, 74, 74, 77, 74, 67, 61,
				68, 65, 69, 71, 74, 77, 79, 79, 77, 78, 78, 79, 73, 70, 71, 72, 74, 74, 78, 77, 71, 67, 71, 73, 73, 80, 72, 68, 72, 76, 81, 75, 74, 76, 73, 80, 81, 75,
				75, 65, 71, 77, 77, 73, 71, 72, 73, 76, 78, 77, 77, 80, 79, 73, 72, 75, 77, 79, 80, 82, 78, 79, 79, 79, 73, 73, 76, 76, 78, 81, 80, 81, 83, 83, 84, 83,
				83, 82, 77, 78, 78, 82, 83, 82, 82, 83, 83, 82, 82, 78, 75, 77, 79, 80, 80, 79, 80, 82, 83, 82, 82, 83, 83, 81, 81, 83, 82, 78, 79, 77, 80, 82, 85, 83,
				81, 80, 83, 82, 80, 81, 82, 84, 79, 80, 80, 81, 81, 82, 83, 83, 85, 84, 85, 84, 83, 83, 83, 84, 85, 85, 83, 83, 84, 83, 84, 83, 83, 83, 84, 85, 81, 84,
				83, 86, 85, 85, 82, 84, 84, 85, 85, 82, 86, 88, 87, 85, 84, 85, 84, 81, 81, 84, 86, 83, 84, 86, 86, 85, 86, 86, 85, 82, 84, 85, 86, 88, 85, 87, 86, 85,
				87, 85, 86, 85, 86, 87, 88, 87, 86, 84, 82, 82, 85, 84, 83, 84, 84, 82, 83, 80, 81, 82, 85, 84, 85, 83, 81, 82, 81, 82, 78, 81, 82, 83, 84, 84, 83, 84,
				47, 51, 50, 51, 49, 48, 50, 45, 51, 49, 49, 49, 48, 49, 51, 51, 52, 50, 51, 51, 52, 48, 52, 59, 57, 51, 51, 59, 58, 57, 50, 48, 44, 50, 44, 43, 48, 50,
				53, 57, 59, 54, 55, 62, 61, 57, 56, 51, 54, 58, 57, 56, 57, 57, 58, 58, 57, 59, 56, 60, 55, 54, 58, 61, 60, 60, 56, 62, 61, 61, 64, 61, 62, 65, 63, 59,
				60, 59, 62, 61, 60, 62, 62, 54, 56, 56, 60, 56, 51, 52, 50, 52, 55, 54, 56, 62, 65, 69, 68, 66, 64, 60, 66, 63, 66, 66, 67, 68, 63, 67, 65, 59, 57, 62,
				55, 55, 59, 61, 71, 72, 74, 70, 65, 63, 63, 65, 63, 61, 66, 61, 68, 70, 72, 76, 77, 75, 69, 68, 64, 63, 69, 71, 72, 76, 76, 75, 73, 69, 72, 70, 69, 72,
				68, 68, 73, 76, 73, 75, 83, 84, 82, 70, 68, 71, 72, 71, 66, 69, 74, 75, 76, 75, 73, 74, 76, 72, 71, 73, 75, 80, 84, 77, 74, 76, 78, 77, 78, 78, 81, 75,
				73, 73, 75, 79, 85, 81, 76, 73, 73, 76, 71, 74, 74, 73, 79, 82, 84, 86, 79, 80, 82, 82, 85, 78, 71, 70, 71, 81, 79, 77, 74, 72, 77, 73, 73, 76, 78, 78,
				75, 73, 71, 70, 74, 74, 74, 75, 72, 80, 79, 80, 76, 80, 79, 81, 77, 77, 77, 75, 75, 74, 70, 72, 76, 80, 82, 83, 80, 78, 76, 76, 76, 74, 75, 73, 75, 75,
				74, 66, 67, 68, 67, 67, 72, 38, 40, 42, 43, 40, 40, 45, 44, 44, 42, 42, 36, 32, 37, 36, 34, 34, 34, 34, 34, 33, 30, 29, 26, 24, 23, 26, 30, 25, 25, 28,
				33, 34, 32, 34, 33, 36, 39, 38, 40, 38, 36, 37, 38, 38, 40, 41, 40, 36, 40, 42, 43, 40, 40, 40, 38, 40, 40, 40, 38, 37, 37, 39, 38, 36, 42, 42, 43, 48,
				42, 42, 42, 44, 46, 46, 47, 48, 45, 51, 44, 40, 42, 38, 40, 38, 36, 47, 44, 43, 43, 40, 42, 44, 40, 40, 46, 52, 53, 45, 45, 46, 42, 48, 45, 46, 43, 44,
				44, 50, 55, 56, 56, 51, 46, 53, 52, 55, 52, 54, 52, 48, 46, 46, 47, 47, 50, 52, 52, 52, 52, 54, 52, 52, 50, 53, 53, 57, 56, 58, 58, 61, 68, 66, 60, 62,
				62, 58, 52, 52, 60, 58, 59, 60, 58, 58, 61, 59, 59, 60, 65, 68, 64, 63, 61, 60, 60, 58, 64, 64, 58, 60, 56, 56, 58, 58, 59, 58, 60, 63, 59, 60, 60, 59,
				61, 66, 70, 68, 69, 72, 74, 74, 70, 64, 61, 64, 68, 66, 64, 67, 69, 71, 72, 73, 73, 74, 74, 74, 72, 74, 76, 70, 69, 69, 74, 72, 69, 70, 66, 68, 68, 68,
				65, 66, 64, 64, 64, 64, 60, 57, 59, 60, 60, 59, 59, 60, 58, 57, 57, 62, 59, 60, 59, 58, 63, 60, 58, 62, 62, 64, 64, 60, 60, 58, 61, 60, 59, 62, 63, 62,
				62, 63, 63, 58, 58, 52, 48, 57, 55, 58, 56, 58, 56, 56, 74, 84, 76, 72, 76, 77, 66, 66, 70, 73, 78, 76, 73, 76, 78, 78, 78, 78, 72, 73, 72, 70, 72, 76,
				69, 67, 71, 74, 78, 77, 76, 75, 78, 78, 72, 68, 68, 70, 76, 78, 71, 72, 75, 78, 74, 75, 72, 70, 72, 77, 74, 71, 70, 70, 72, 76, 81, 70, 68, 70, 69, 70,
				72, 76, 71, 74, 76, 74, 77, 75, 74, 70, 74, 74, 75, 70, 72, 70, 74, 75, 76, 76, 68, 70, 72, 70, 70, 70, 72, 70, 72, 72, 74, 68, 68, 66, 66, 68, 68, 68,
				73, 66, 65, 62, 63, 64, 62, 66, 64, 64, 64, 66, 66, 72, 66, 67, 63, 66, 68, 66, 60, 64, 56, 60, 60, 59, 58, 57, 60, 64, 64, 61, 64, 64, 63, 65, 66, 65,
				67, 68, 64, 67, 66, 68, 68, 66, 69, 68, 62, 62, 64, 64, 63, 60, 60, 60, 58, 56, 56, 54, 60, 57, 60, 60, 62, 56, 58, 56, 58, 59, 62, 58, 58, 56, 57, 58,
				60, 58, 62, 56, 54, 54, 54, 54, 58, 55, 54, 56, 56, 56, 54, 57, 54, 50, 50, 56, 59, 58, 54, 52, 55, 54, 54, 55, 57, 58, 59, 56, 57, 64, 65, 67, 57, 51,
				50, 54, 54, 57, 57, 54, 58, 60, 50, 49, 52, 52, 54, 54, 58, 56, 58, 54, 54, 58, 56, 58, 59, 57, 58, 60, 54, 56, 60, 63, 56, 53, 54, 54, 58, 58, 60, 64,
				65, 66, 60, 60, 65, 62, 66, 64, 58, 58, 55, 59, 61, 61, 65, 66, 62, 61, 64, 75, 76, 41, 39, 29, 30, 43, 33, 16, 32, 39, 44, 56, 49, 48, 52, 39, 35, 42,
				35, 46, 51, 38, 27, 31, 24, 33, 44, 46, 25, 20, 27, 40, 45, 60, 52, 43, 43, 35, 40, 48, 51, 45, 38, 32, 37, 44, 41, 47, 52, 57, 63, 67, 55, 52, 57, 54,
				51, 41, 38, 42, 51, 60, 47, 39, 47, 42, 48, 56, 60, 60, 64, 56, 44, 50, 59, 56, 49, 43, 53, 58, 55, 64, 57, 50, 44, 41, 52, 57, 58, 54, 61, 67, 67, 70,
				69, 60, 55, 58, 55, 58, 59, 61, 66, 69, 67, 51, 48, 53, 54, 57, 63, 64, 66, 66, 65, 71, 66, 71, 71, 72, 68, 61, 63, 63, 71, 75, 73, 72, 75, 71, 70, 74,
				77, 77, 74, 59, 58, 60, 59, 65, 70, 74, 76, 79, 78, 79, 76, 75, 77, 77, 77, 78, 75, 72, 78, 79, 77, 76, 78, 77, 79, 78, 75, 75, 75, 76, 80, 81, 82, 83,
				83, 82, 79, 79, 80, 78, 79, 79, 79, 79, 82, 80, 81, 83, 79, 74, 77, 78, 81, 80, 82, 79, 78, 81, 81, 81, 79, 74, 74, 75, 77, 72, 78, 77, 80, 79, 79, 80,
				82, 81, 74, 72, 76, 74, 76, 78, 81, 80, 81, 83, 81, 80, 79, 78, 80, 76, 74, 77, 78, 79, 80, 79, 81, 81, 84, 84, 82, 77, 76, 77, 80, 80, 80, 80, 82, 81,
				82, 77, 81, 82, 82, 78, 77, 79, 81, 83, 78, 72, 73, 79, 77, 74, 77, 75, 75, 73, 69, 67, 71, 70, 72, 70, 69, 75, 36, 40, 40, 40, 38, 38, 47, 49, 48, 40,
				36, 38, 37, 38, 35, 42, 40, 34, 34, 37, 38, 38, 38, 34, 36, 37, 35, 35, 29, 30, 32, 36, 37, 34, 38, 32, 43, 43, 40, 39, 34, 38, 37, 41, 40, 45, 42, 42,
				39, 42, 44, 39, 38, 42, 40, 42, 46, 43, 42, 40, 39, 40, 43, 42, 40, 43, 44, 46, 48, 46, 50, 48, 48, 46, 51, 51, 50, 50, 52, 55, 48, 40, 38, 38, 38, 42,
				48, 50, 52, 52, 54, 56, 57, 58, 53, 57, 56, 54, 48, 51, 51, 54, 56, 49, 40, 42, 44, 44, 48, 53, 50, 53, 56, 56, 62, 58, 52, 51, 54, 57, 54, 54, 44, 46,
				51, 56, 56, 56, 60, 56, 54, 49, 50, 52, 49, 53, 52, 55, 58, 62, 64, 68, 61, 59, 60, 59, 59, 57, 56, 50, 51, 56, 59, 60, 58, 56, 62, 69, 74, 77, 78, 72,
				72, 69, 61, 59, 62, 61, 62, 63, 60, 58, 62, 64, 66, 61, 58, 62, 65, 61, 60, 60, 63, 65, 70, 67, 72, 66, 58, 53, 56, 64, 66, 67, 66, 68, 68, 71, 72, 75,
				73, 65, 69, 70, 68, 68, 64, 70, 70, 64, 66, 68, 66, 66, 66, 67, 62, 64, 66, 66, 70, 72, 66, 64, 59, 60, 58, 60, 60, 60, 61, 57, 56, 58, 57, 55, 54, 61,
				62, 62, 64, 64, 56, 56, 58, 58, 61, 64, 67, 64, 67, 66, 60, 56, 54, 57, 62, 60, 62, 62, 66, 65, 66, 62, 49, 49, 51, 54, 57, 59, 59, 62, 62, 48, 46, 44,
				44, 42, 44, 44, 47, 44, 38, 38, 42, 42, 40, 39, 39, 44, 42, 40, 40, 42, 40, 42, 45, 46, 49, 38, 47, 46, 52, 50, 44, 48, 56, 44, 36, 37, 38, 36, 40, 44,
				38, 40, 40, 37, 38, 46, 47, 42, 42, 40, 42, 42, 42, 44, 46, 48, 52, 56, 48, 42, 46, 42, 46, 44, 40, 40, 44, 42, 42, 50, 55, 50, 46, 52, 52, 57, 52, 47,
				50, 48, 52, 55, 61, 60, 56, 57, 63, 62, 60, 56, 57, 57, 60, 52, 48, 50, 58, 58, 61, 54, 56, 56, 56, 59, 64, 62, 55, 54, 50, 56, 58, 60, 60, 63, 64, 63,
				65, 64, 60, 68, 68, 70, 66, 64, 58, 60, 64, 68, 65, 68, 66, 66, 70, 65, 70, 68, 68, 68, 70, 63, 65, 63, 67, 72, 70, 70, 73, 72, 73, 76, 78, 76, 74, 74,
				68, 68, 66, 68, 70, 73, 70, 72, 75, 76, 75, 76, 74, 71, 76, 74, 74, 71, 74, 73, 73, 74, 74, 73, 76, 74, 76, 78, 76, 71, 70, 74, 74, 79, 75, 78, 82, 83,
				78, 82, 81, 81, 80, 76, 74, 76, 76, 80, 83, 84, 84, 84, 86, 80, 81, 81, 84, 84, 84, 88, 86, 88, 86, 84, 82, 78, 79, 84, 78, 78, 80, 84, 82, 81, 84, 86,
				86, 84, 86, 82, 82, 78, 76, 72, 70, 73, 72, 74, 70, 75, 74, 76, 80, 80, 72, 70, 73, 74, 70, 72, 74, 73, 74, 76, 74, 72, 70, 70, 68, 72, 70, 73, 76, 74,
				68, 70, 74, 73, 30, 30, 20, 22, 24, 20, 24, 31, 22, 19, 16, 14, 15, 24, 28, 28, 14, 14, 14, 17, 23, 29, 20, 18, 15, 16, 27, 31, 21, 18, 18, 20, 22, 24,
				19, 16, 28, 32, 22, 20, 18, 16, 12, 12, 18, 14, 11, 10, 16, 18, 15, 13, 7, 4, 6, 14, 26, 36, 20, 16, 14, 19, 22, 18, 16, 12, 11, 10, 12, 12, 13, 19, 22,
				21, 14, 17, 16, 9, 9, 10, 14, 13, 9, 8, 4, 11, 27, 38, 28, 22, 17, 13, 18, 18, 14, 12, 12, 16, 26, 36, 26, 28, 19, 13, 16, 18, 18, 11, 10, 14, 13, 19,
				21, 22, 22, 32, 36, 24, 21, 29, 38, 40, 42, 38, 32, 30, 27, 29, 31, 31, 34, 36, 38, 32, 34, 32, 24, 36, 33, 34, 36, 34, 26, 32, 34, 33, 34, 34, 34, 37,
				36, 32, 38, 36, 37, 36, 39, 40, 44, 44, 36, 40, 46, 50, 46, 50, 44, 46, 45, 51, 49, 47, 50, 42, 46, 40, 41, 42, 42, 39, 39, 38, 48, 54, 48, 41, 37, 52,
				52, 45, 50, 48, 50, 42, 51, 40, 41, 48, 53, 47, 52, 46, 52, 55, 52, 50, 53, 46, 48, 56, 55, 52, 48, 44, 46, 46, 46, 45, 48, 46, 52, 46, 50, 49, 46, 44,
				49, 52, 48, 36, 47, 48, 50, 46, 46, 46, 48, 44, 44, 47, 53, 44, 43, 40, 40, 42, 48, 43, 42, 43, 40, 38, 40, 36, 37, 36, 35, 38, 43, 44, 44, 41, 40, 43,
				38, 37, 36, 35, 34, 32, 32, 32, 34, 24, 14, 8, 15, 31, 43, 19, 15, 25, 28, 47, 46, 41, 47, 42, 36, 40, 35, 33, 34, 19, 13, 14, 13, 27, 21, 34, 17, 21,
				23, 33, 37, 43, 34, 29, 29, 24, 25, 24, 26, 25, 20, 16, 29, 33, 31, 28, 22, 25, 29, 41, 39, 42, 43, 35, 26, 24, 21, 18, 26, 35, 23, 22, 25, 18, 25, 41,
				37, 36, 47, 42, 21, 28, 48, 30, 22, 27, 34, 45, 40, 43, 33, 25, 30, 30, 34, 48, 46, 40, 38, 41, 42, 44, 41, 46, 47, 49, 54, 50, 47, 59, 59, 54, 66, 55,
				38, 36, 37, 50, 44, 53, 60, 52, 51, 50, 45, 46, 48, 43, 43, 52, 60, 59, 56, 58, 56, 58, 55, 55, 69, 71, 69, 49, 55, 67, 66, 67, 58, 60, 61, 59, 55, 55,
				57, 59, 67, 62, 47, 51, 57, 56, 59, 70, 65, 58, 59, 67, 71, 74, 74, 69, 61, 63, 62, 68, 69, 68, 73, 78, 76, 69, 66, 65, 68, 72, 78, 71, 66, 70, 74, 77,
				79, 82, 81, 70, 70, 76, 79, 83, 81, 74, 68, 71, 77, 79, 78, 73, 74, 71, 68, 67, 67, 75, 82, 70, 73, 75, 73, 75, 72, 71, 72, 74, 67, 66, 71, 76, 77, 72,
				73, 72, 73, 75, 73, 67, 70, 68, 70, 73, 71, 67, 67, 66, 68, 67, 69, 74, 75, 79, 73, 65, 69, 76, 79, 82, 79, 75, 79, 81, 70, 65, 65, 65, 71, 62, 60, 59,
				58, 60, 62, 65, 51, 60, 73, 64, 61, 60, 58, 62, 71, 74, 66, 57, 40, 32, 38, 28, 32, 29, 32, 25, 22, 24, 24, 27, 24, 24, 28, 31, 34, 30, 33, 32, 30, 28,
				26, 37, 34, 29, 36, 31, 27, 35, 30, 36, 40, 29, 26, 22, 26, 26, 26, 20, 19, 20, 26, 28, 26, 29, 32, 34, 30, 30, 28, 30, 35, 35, 38, 36, 40, 40, 34, 40,
				40, 32, 39, 34, 34, 32, 41, 38, 36, 42, 43, 40, 50, 52, 52, 53, 50, 47, 54, 55, 56, 54, 54, 58, 60, 55, 62, 60, 61, 64, 60, 60, 56, 58, 54, 57, 60, 62,
				71, 60, 58, 56, 60, 64, 66, 62, 58, 60, 52, 56, 60, 63, 68, 68, 66, 61, 62, 64, 65, 68, 70, 65, 60, 58, 56, 58, 66, 62, 64, 62, 54, 66, 70, 64, 65, 72,
				70, 70, 72, 76, 74, 80, 80, 72, 72, 74, 76, 78, 86, 82, 80, 77, 72, 74, 76, 80, 70, 72, 74, 72, 71, 74, 80, 78, 76, 78, 78, 72, 76, 74, 74, 74, 74, 75,
				77, 76, 82, 81, 82, 82, 84, 78, 74, 80, 84, 80, 82, 82, 84, 80, 84, 82, 84, 82, 82, 81, 79, 78, 84, 88, 88, 87, 82, 81, 78, 80, 82, 85, 86, 85, 78, 81,
				82, 85, 86, 76, 78, 82, 80, 78, 78, 80, 82, 78, 68, 73, 77, 80, 77, 77, 80, 80, 82, 74, 81, 81, 76, 78, 78, 76, 73, 75, 74, 72, 70, 74, 76, 75, 76, 78,
				72, 70, 73, 71, 72, 73, 67, 68, 62, 65, 66, 65, 71, 70, 69, 64, 69, 64, 68, 65, 65, 65, 55, 35, 35, 36, 38, 36, 37, 46, 46, 42, 37, 37, 40, 40, 38, 34,
				38, 39, 34, 34, 38, 38, 37, 33, 32, 30, 37, 34, 32, 28, 28, 32, 33, 36, 38, 34, 34, 44, 36, 34, 37, 37, 38, 37, 38, 38, 44, 40, 40, 36, 40, 43, 40, 40,
				42, 38, 40, 42, 40, 44, 40, 37, 36, 36, 42, 39, 41, 43, 45, 44, 42, 43, 44, 46, 44, 53, 50, 51, 48, 52, 52, 46, 37, 39, 38, 36, 40, 45, 47, 50, 52, 54,
				54, 54, 52, 48, 56, 52, 53, 48, 48, 52, 58, 57, 49, 44, 42, 46, 42, 41, 49, 51, 52, 57, 58, 58, 59, 48, 46, 49, 47, 54, 53, 45, 48, 50, 55, 56, 57, 60,
				60, 52, 48, 50, 48, 48, 50, 53, 54, 61, 60, 63, 67, 58, 58, 60, 60, 56, 58, 58, 56, 52, 56, 60, 57, 58, 58, 62, 69, 71, 75, 76, 71, 74, 68, 64, 63, 62,
				63, 62, 64, 64, 64, 65, 66, 64, 62, 60, 64, 67, 58, 58, 58, 58, 62, 64, 66, 70, 68, 56, 54, 54, 60, 64, 64, 66, 66, 66, 69, 70, 70, 69, 63, 64, 69, 66,
				68, 62, 63, 67, 63, 62, 68, 65, 68, 66, 66, 66, 63, 67, 68, 70, 70, 62, 62, 61, 60, 56, 58, 57, 60, 61, 58, 58, 58, 54, 57, 54, 60, 64, 64, 64, 65, 58,
				57, 56, 59, 64, 66, 65, 65, 66, 66, 64, 58, 51, 59, 61, 60, 62, 62, 62, 65, 66, 62, 54, 50, 51, 54, 53, 56, 58, 62, 60, 46, 49, 48, 47, 46, 46, 50, 49,
				47, 46, 47, 44, 48, 48, 48, 53, 52, 52, 56, 56, 57, 53, 52, 52, 55, 42, 34, 42, 46, 34, 38, 41, 47, 48, 45, 43, 40, 40, 49, 54, 54, 46, 54, 56, 54, 45,
				46, 50, 52, 46, 55, 54, 47, 49, 46, 42, 42, 42, 44, 52, 55, 58, 54, 56, 54, 52, 48, 45, 46, 42, 45, 46, 42, 48, 50, 54, 55, 58, 52, 55, 57, 52, 53, 56,
				53, 57, 56, 50, 46, 44, 50, 54, 56, 57, 60, 58, 54, 55, 54, 60, 52, 50, 53, 52, 58, 62, 61, 56, 58, 60, 59, 66, 66, 65, 60, 58, 60, 58, 60, 56, 59, 60,
				62, 66, 62, 60, 58, 60, 56, 56, 60, 64, 66, 69, 66, 66, 65, 63, 66, 67, 69, 69, 68, 66, 68, 70, 72, 72, 76, 68, 64, 66, 66, 66, 68, 66, 66, 70, 70, 73,
				75, 73, 72, 71, 74, 76, 74, 75, 78, 76, 70, 70, 68, 72, 73, 76, 82, 80, 72, 73, 74, 74, 76, 74, 72, 75, 74, 78, 79, 80, 78, 82, 80, 79, 78, 78, 78, 78,
				77, 75, 77, 79, 82, 83, 78, 78, 80, 82, 84, 84, 83, 82, 83, 78, 78, 81, 83, 85, 76, 75, 76, 80, 82, 84, 82, 84, 80, 86, 78, 75, 78, 78, 78, 82, 83, 80,
				79, 79, 78, 78, 78, 78, 78, 76, 76, 80, 77, 80, 78, 76, 72, 74, 76, 74, 74, 76, 78, 76, 74, 70, 65, 68, 67, 68, 69, 68, 61, 62, 66, 61, 62, 64, 64, 75,
				80, 79, 78, 75, 72, 76, 80, 76, 72, 67, 69, 73, 74, 74, 72, 70, 70, 74, 75, 76, 76, 76, 76, 78, 82, 81, 82, 77, 78, 80, 80, 80, 79, 78, 80, 76, 74, 74,
				73, 72, 72, 70, 73, 76, 71, 72, 74, 78, 80, 78, 78, 76, 76, 80, 80, 78, 80, 76, 73, 74, 78, 77, 79, 77, 78, 79, 80, 78, 76, 82, 84, 82, 84, 82, 88, 86,
				84, 84, 82, 80, 79, 80, 82, 81, 81, 82, 83, 85, 82, 81, 82, 83, 89, 84, 82, 81, 82, 82, 80, 81, 79, 80, 78, 79, 82, 82, 83, 82, 84, 91, 90, 89, 87, 84,
				86, 84, 86, 84, 87, 84, 84, 86, 86, 86, 86, 88, 88, 88, 86, 86, 85, 85, 84, 86, 86, 86, 86, 89, 89, 87, 86, 86, 86, 86, 86, 88, 87, 88, 88, 86, 87, 88,
				90, 88, 88, 89, 89, 88, 90, 92, 92, 90, 87, 86, 86, 85, 84, 87, 86, 88, 88, 88, 88, 87, 88, 88, 87, 88, 88, 88, 85, 80, 78, 81, 85, 86, 82, 80, 80, 81,
				78, 76, 80, 82, 78, 79, 82, 82, 84, 84, 83, 83, 81, 82, 84, 84, 80, 78, 80, 80, 78, 81, 81, 81, 80, 81, 80, 81, 80, 81, 82, 83, 82, 84, 82, 81, 82, 84,
				82, 82, 82, 82, 81, 82, 82, 82, 80, 80, 80, 80, 83, 79, 77, 81, 82, 83, 80, 82, 84, 82, 80, 80, 81, 82, 83, 83, 82, 80, 82, 81, 80, 82, 80, 82, 82, 82,
				83, 82, 86, 86, 88, 86, 76, 76, 78, 79, 80, 80, 81, 82, 82, 80, 80, 80, 77, 75, 74, 72, 74, 73, 70, 72, 72, 69, 70, 71, 74, 76, 77, 78, 79, 79, 80, 80,
				80, 82, 83, 83, 83, 83, 82, 82, 82, 82, 82, 82, 82, 82, 84, 84, 84, 85, 77, 76, 82, 83, 84, 83, 84, 84, 84, 82, 84, 84, 85, 84, 85, 84, 86, 85, 85, 86,
				86, 86, 86, 88, 86, 88, 88, 88, 88, 88, 80, 85, 86, 87, 88, 88, 88, 86, 89, 88, 88, 88, 90, 84, 84, 84, 89, 90, 88, 90, 86, 86, 86, 86, 84, 86, 88, 88,
				89, 90, 90, 90, 91, 90, 90, 90, 90, 84, 86, 88, 89, 84, 88, 86, 88, 86, 84, 88, 87, 88, 84, 90, 88, 88, 90, 92, 90, 84, 86, 88, 89, 89, 89, 90, 90, 88,
				89, 86, 89, 90, 90, 84, 87, 89, 86, 86, 86, 85, 86, 87, 86, 88, 84, 85, 88, 86, 86, 84, 85, 87, 86, 87, 84, 84, 86, 84, 82, 85, 88, 90, 84, 83, 87, 88,
				89, 89, 88, 87, 85, 85, 84, 87, 88, 86, 82, 84, 84, 86, 84, 82, 84, 86, 86, 86, 84, 86, 85, 84, 80, 86, 84, 84, 85, 84, 84, 84, 85, 83, 82, 84, 85, 85,
				85, 85, 85, 84, 84, 84, 84, 88, 84, 84, 82, 82, 86, 87, 85, 82, 83, 82, 84, 82, 81, 82, 84, 82, 84, 84, 84, 82, 86, 86, 86, 84, 84, 86, 85, 85, 84, 84,
				84, 86, 88, 88, 84, 88, 85, 84, 84, 84, 86, 83, 82, 68, 64, 69, 69, 75, 82, 72, 72, 73, 78, 90, 91, 75, 70, 68, 71, 75, 78, 85, 85, 84, 86, 86, 82, 86,
				80, 70, 74, 77, 79, 77, 74, 76, 78, 84, 80, 80, 78, 72, 76, 75, 77, 80, 82, 78, 75, 72, 78, 80, 80, 84, 81, 84, 82, 76, 78, 73, 78, 80, 83, 80, 74, 76,
				76, 79, 86, 74, 66, 69, 77, 83, 78, 68, 61, 64, 69, 74, 77, 76, 72, 76, 80, 82, 76, 78, 73, 78, 69, 66, 66, 64, 64, 68, 69, 73, 66, 66, 70, 74, 80, 82,
				78, 77, 75, 68, 68, 62, 68, 64, 64, 60, 58, 62, 66, 68, 65, 67, 56, 55, 60, 56, 57, 58, 61, 62, 65, 65, 60, 62, 62, 64, 63, 61, 62, 63, 62, 62, 61, 64,
				66, 64, 63, 64, 62, 60, 61, 62, 58, 57, 54, 55, 59, 62, 60, 60, 64, 63, 62, 62, 66, 56, 50, 52, 49, 48, 52, 58, 54, 57, 56, 60, 58, 52, 47, 50, 52, 54,
				50, 49, 50, 60, 63, 58, 48, 54, 58, 59, 57, 52, 48, 50, 54, 54, 56, 54, 56, 52, 50, 54, 58, 54, 54, 53, 52, 56, 58, 56, 54, 57, 58, 59, 51, 50, 53, 53,
				56, 62, 64, 62, 60, 58, 64, 64, 60, 63, 64, 60, 60, 65, 60, 61, 60, 62, 57, 58, 58, 62, 60, 60, 64, 64, 58, 55, 54, 60, 59, 57, 65, 65, 62, 58, 60, 62,
				59, 59, 59, 56, 60, 64, 67, 62, 62, 72, 62, 57, 51, 58, 60, 62, 60, 61, 62, 62, };
	}

	private void testCase1() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_MARKET_MATCHING;
		System.out.println("testCase1() requestUrl=" + requestUrl);
		String requestParameters = IConstants.EMPTY_STRING;
		MarketMatchingResponse marketMatchingResponse = politeCrawlWebServiceClientService.marketMatching(requestUrl, requestParameters);
		assertNotNull("testCase1() marketMatchingResponse should not be null.", marketMatchingResponse);
		String json = new Gson().toJson(marketMatchingResponse, MarketMatchingResponse.class);
		assertNotNull("testCase1() marketMatchingResponse.getError() should not be null.", marketMatchingResponse.getError());
		assertEquals("testCase1() json is incorrect", "{\"success\":false,\"error\":{\"error_code\":\"00001\",\"error_message\":\"Request JSON is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase1() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase2() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_MARKET_MATCHING;
		System.out.println("testCase2() requestUrl=" + requestUrl);
		MarketMatchingRequest marketMatchingRequest = new MarketMatchingRequest();
		String requestParameters = new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class);
		MarketMatchingResponse marketMatchingResponse = politeCrawlWebServiceClientService.marketMatching(requestUrl, requestParameters);
		assertNotNull("testCase2() marketMatchingResponse should not be null.", marketMatchingResponse);
		String json = new Gson().toJson(marketMatchingResponse, MarketMatchingResponse.class);
		assertNotNull("testCase2() marketMatchingResponse.getError() should not be null.", marketMatchingResponse.getError());
		assertEquals("testCase2() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00004\",\"error_message\":\"Request parameter access_token is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase2() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase3() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_MARKET_MATCHING;
		System.out.println("testCase3() requestUrl=" + requestUrl);
		MarketMatchingRequest marketMatchingRequest = new MarketMatchingRequest();
		marketMatchingRequest.setAccess_token("testaccesstoken");
		String requestParameters = new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class);
		MarketMatchingResponse marketMatchingResponse = politeCrawlWebServiceClientService.marketMatching(requestUrl, requestParameters);
		assertNotNull("testCase3() marketMatchingResponse should not be null.", marketMatchingResponse);
		String json = new Gson().toJson(marketMatchingResponse, MarketMatchingResponse.class);
		assertNotNull("testCase3() marketMatchingResponse.getError() should not be null.", marketMatchingResponse.getError());
		assertEquals("testCase3() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00002\",\"error_message\":\"Request parameter access_token testaccesstoken is invalid.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase3() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase4() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_MARKET_MATCHING;
		System.out.println("testCase4() requestUrl=" + requestUrl);
		MarketMatchingRequest marketMatchingRequest = new MarketMatchingRequest();
		marketMatchingRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		String requestParameters = new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class);
		MarketMatchingResponse marketMatchingResponse = politeCrawlWebServiceClientService.marketMatching(requestUrl, requestParameters);
		assertNotNull("testCase4() marketMatchingResponse should not be null.", marketMatchingResponse);
		String json = new Gson().toJson(marketMatchingResponse, MarketMatchingResponse.class);
		assertNotNull("testCase4() marketMatchingResponse.getError() should not be null.", marketMatchingResponse.getError());
		assertEquals("testCase4() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00005\",\"error_message\":\"Request parameter test_market is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase4() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase5() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_MARKET_MATCHING;
		System.out.println("testCase5() requestUrl=" + requestUrl);
		MarketMatchingRequest marketMatchingRequest = new MarketMatchingRequest();
		marketMatchingRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		marketMatchingRequest.setTest_market("TEST");
		String requestParameters = new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class);
		MarketMatchingResponse marketMatchingResponse = politeCrawlWebServiceClientService.marketMatching(requestUrl, requestParameters);
		assertNotNull("testCase5() marketMatchingResponse should not be null.", marketMatchingResponse);
		String json = new Gson().toJson(marketMatchingResponse, MarketMatchingResponse.class);
		assertNotNull("testCase5() marketMatchingResponse.getError() should not be null.", marketMatchingResponse.getError());
		assertEquals("testCase5() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00006\",\"error_message\":\"Request parameter pre_period_start_date is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase5() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase6() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_MARKET_MATCHING;
		System.out.println("testCase6() requestUrl=" + requestUrl);
		MarketMatchingRequest marketMatchingRequest = new MarketMatchingRequest();
		marketMatchingRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		marketMatchingRequest.setTest_market("TEST");
		marketMatchingRequest.setPre_period_start_date("TEST");
		String requestParameters = new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class);
		MarketMatchingResponse marketMatchingResponse = politeCrawlWebServiceClientService.marketMatching(requestUrl, requestParameters);
		assertNotNull("testCase6() marketMatchingResponse should not be null.", marketMatchingResponse);
		String json = new Gson().toJson(marketMatchingResponse, MarketMatchingResponse.class);
		assertNotNull("testCase6() marketMatchingResponse.getError() should not be null.", marketMatchingResponse.getError());
		assertEquals("testCase6() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00013\",\"error_message\":\"Request parameter pre_period_start_date TEST is invalid.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase6() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase7() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_MARKET_MATCHING;
		System.out.println("testCase7() requestUrl=" + requestUrl);
		MarketMatchingRequest marketMatchingRequest = new MarketMatchingRequest();
		marketMatchingRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		marketMatchingRequest.setTest_market("TEST");
		marketMatchingRequest.setPre_period_start_date("2021-01-01");
		String requestParameters = new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class);
		MarketMatchingResponse marketMatchingResponse = politeCrawlWebServiceClientService.marketMatching(requestUrl, requestParameters);
		assertNotNull("testCase7() marketMatchingResponse should not be null.", marketMatchingResponse);
		String json = new Gson().toJson(marketMatchingResponse, MarketMatchingResponse.class);
		assertNotNull("testCase7() marketMatchingResponse.getError() should not be null.", marketMatchingResponse.getError());
		assertEquals("testCase7() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00007\",\"error_message\":\"Request parameter pre_period_end_date is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase7() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase8() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_MARKET_MATCHING;
		System.out.println("testCase8() requestUrl=" + requestUrl);
		MarketMatchingRequest marketMatchingRequest = new MarketMatchingRequest();
		marketMatchingRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		marketMatchingRequest.setTest_market("TEST");
		marketMatchingRequest.setPre_period_start_date("2021-01-01");
		marketMatchingRequest.setPre_period_end_date("TEST");
		String requestParameters = new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class);
		MarketMatchingResponse marketMatchingResponse = politeCrawlWebServiceClientService.marketMatching(requestUrl, requestParameters);
		assertNotNull("testCase8() marketMatchingResponse should not be null.", marketMatchingResponse);
		String json = new Gson().toJson(marketMatchingResponse, MarketMatchingResponse.class);
		assertNotNull("testCase8() marketMatchingResponse.getError() should not be null.", marketMatchingResponse.getError());
		assertEquals("testCase8() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00014\",\"error_message\":\"Request parameter pre_period_end_date TEST is invalid.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase8() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase9() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_MARKET_MATCHING;
		System.out.println("testCase9() requestUrl=" + requestUrl);
		MarketMatchingRequest marketMatchingRequest = new MarketMatchingRequest();
		marketMatchingRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		marketMatchingRequest.setTest_market("TEST");
		marketMatchingRequest.setPre_period_start_date("2021-01-01");
		marketMatchingRequest.setPre_period_end_date("2020-12-31");
		String requestParameters = new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class);
		MarketMatchingResponse marketMatchingResponse = politeCrawlWebServiceClientService.marketMatching(requestUrl, requestParameters);
		assertNotNull("testCase9() marketMatchingResponse should not be null.", marketMatchingResponse);
		String json = new Gson().toJson(marketMatchingResponse, MarketMatchingResponse.class);
		assertNotNull("testCase9() marketMatchingResponse.getError() should not be null.", marketMatchingResponse.getError());
		assertEquals("testCase9() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00017\",\"error_message\":\"Request parameter pre_period_start_date and pre_period_end_date combination is invalid.\"}}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase9() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase10() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_MARKET_MATCHING;
		System.out.println("testCase10() requestUrl=" + requestUrl);
		MarketMatchingRequest marketMatchingRequest = new MarketMatchingRequest();
		marketMatchingRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		marketMatchingRequest.setTest_market("TEST");
		marketMatchingRequest.setPre_period_start_date("2021-01-01");
		marketMatchingRequest.setPre_period_end_date("2021-10-02");
		String requestParameters = new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class);
		MarketMatchingResponse marketMatchingResponse = politeCrawlWebServiceClientService.marketMatching(requestUrl, requestParameters);
		assertNotNull("testCase10() marketMatchingResponse should not be null.", marketMatchingResponse);
		String json = new Gson().toJson(marketMatchingResponse, MarketMatchingResponse.class);
		assertNotNull("testCase10() marketMatchingResponse.getError() should not be null.", marketMatchingResponse.getError());
		assertEquals("testCase10() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00008\",\"error_message\":\"Request parameter post_period_start_date is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase10() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase11() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_MARKET_MATCHING;
		System.out.println("testCase11() requestUrl=" + requestUrl);
		MarketMatchingRequest marketMatchingRequest = new MarketMatchingRequest();
		marketMatchingRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		marketMatchingRequest.setTest_market("TEST");
		marketMatchingRequest.setPre_period_start_date("2021-01-01");
		marketMatchingRequest.setPre_period_end_date("2021-10-02");
		marketMatchingRequest.setPost_period_start_date("TEST");
		String requestParameters = new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class);
		MarketMatchingResponse marketMatchingResponse = politeCrawlWebServiceClientService.marketMatching(requestUrl, requestParameters);
		assertNotNull("testCase11() marketMatchingResponse should not be null.", marketMatchingResponse);
		String json = new Gson().toJson(marketMatchingResponse, MarketMatchingResponse.class);
		assertNotNull("testCase11() marketMatchingResponse.getError() should not be null.", marketMatchingResponse.getError());
		assertEquals("testCase11() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00015\",\"error_message\":\"Request parameter post_period_start_date TEST is invalid.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase11() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase12() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_MARKET_MATCHING;
		System.out.println("testCase12() requestUrl=" + requestUrl);
		MarketMatchingRequest marketMatchingRequest = new MarketMatchingRequest();
		marketMatchingRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		marketMatchingRequest.setTest_market("TEST");
		marketMatchingRequest.setPre_period_start_date("2021-01-01");
		marketMatchingRequest.setPre_period_end_date("2021-10-02");
		marketMatchingRequest.setPost_period_start_date("2021-10-04");
		String requestParameters = new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class);
		MarketMatchingResponse marketMatchingResponse = politeCrawlWebServiceClientService.marketMatching(requestUrl, requestParameters);
		assertNotNull("testCase12() marketMatchingResponse should not be null.", marketMatchingResponse);
		String json = new Gson().toJson(marketMatchingResponse, MarketMatchingResponse.class);
		assertNotNull("testCase12() marketMatchingResponse.getError() should not be null.", marketMatchingResponse.getError());
		assertEquals("testCase12() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00009\",\"error_message\":\"Request parameter post_period_end_date is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase12() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase13() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_MARKET_MATCHING;
		System.out.println("testCase13() requestUrl=" + requestUrl);
		MarketMatchingRequest marketMatchingRequest = new MarketMatchingRequest();
		marketMatchingRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		marketMatchingRequest.setTest_market("TEST");
		marketMatchingRequest.setPre_period_start_date("2021-01-01");
		marketMatchingRequest.setPre_period_end_date("2021-10-02");
		marketMatchingRequest.setPost_period_start_date("2021-10-04");
		marketMatchingRequest.setPost_period_end_date("TEST");
		String requestParameters = new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class);
		MarketMatchingResponse marketMatchingResponse = politeCrawlWebServiceClientService.marketMatching(requestUrl, requestParameters);
		assertNotNull("testCase13() marketMatchingResponse should not be null.", marketMatchingResponse);
		String json = new Gson().toJson(marketMatchingResponse, MarketMatchingResponse.class);
		assertNotNull("testCase13() marketMatchingResponse.getError() should not be null.", marketMatchingResponse.getError());
		assertEquals("testCase13() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00016\",\"error_message\":\"Request parameter post_period_end_date TEST is invalid.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase13() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase14() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_MARKET_MATCHING;
		System.out.println("testCase14() requestUrl=" + requestUrl);
		MarketMatchingRequest marketMatchingRequest = new MarketMatchingRequest();
		marketMatchingRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		marketMatchingRequest.setTest_market("TEST");
		marketMatchingRequest.setPre_period_start_date("2021-01-01");
		marketMatchingRequest.setPre_period_end_date("2021-10-02");
		marketMatchingRequest.setPost_period_start_date("2021-10-04");
		marketMatchingRequest.setPost_period_end_date("2021-10-03");
		String requestParameters = new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class);
		MarketMatchingResponse marketMatchingResponse = politeCrawlWebServiceClientService.marketMatching(requestUrl, requestParameters);
		assertNotNull("testCase14() marketMatchingResponse should not be null.", marketMatchingResponse);
		String json = new Gson().toJson(marketMatchingResponse, MarketMatchingResponse.class);
		assertNotNull("testCase14() marketMatchingResponse.getError() should not be null.", marketMatchingResponse.getError());
		assertEquals("testCase14() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00019\",\"error_message\":\"Request parameter post_period_start_date and post_period_end_date combination is invalid.\"}}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase14() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase15() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_MARKET_MATCHING;
		System.out.println("testCase15() requestUrl=" + requestUrl);
		MarketMatchingRequest marketMatchingRequest = new MarketMatchingRequest();
		marketMatchingRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		marketMatchingRequest.setTest_market("TEST");
		marketMatchingRequest.setPre_period_start_date("2021-01-01");
		marketMatchingRequest.setPre_period_end_date("2021-10-02");
		marketMatchingRequest.setPost_period_start_date("2021-10-04");
		marketMatchingRequest.setPost_period_end_date("2021-12-31");
		String requestParameters = new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class);
		MarketMatchingResponse marketMatchingResponse = politeCrawlWebServiceClientService.marketMatching(requestUrl, requestParameters);
		assertNotNull("testCase15() marketMatchingResponse should not be null.", marketMatchingResponse);
		String json = new Gson().toJson(marketMatchingResponse, MarketMatchingResponse.class);
		assertNotNull("testCase15() marketMatchingResponse.getError() should not be null.", marketMatchingResponse.getError());
		assertEquals("testCase15() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00018\",\"error_message\":\"Request parameter post_period_start_date must be one day after pre_period_end_date.\"}}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase15() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase16() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_MARKET_MATCHING;
		System.out.println("testCase16() requestUrl=" + requestUrl);
		MarketMatchingRequest marketMatchingRequest = new MarketMatchingRequest();
		marketMatchingRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		marketMatchingRequest.setTest_market("TEST");
		marketMatchingRequest.setPre_period_start_date("2021-01-01");
		marketMatchingRequest.setPre_period_end_date("2021-10-02");
		marketMatchingRequest.setPost_period_start_date("2021-10-03");
		marketMatchingRequest.setPost_period_end_date("2021-12-31");
		String requestParameters = new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class);
		MarketMatchingResponse marketMatchingResponse = politeCrawlWebServiceClientService.marketMatching(requestUrl, requestParameters);
		assertNotNull("testCase16() marketMatchingResponse should not be null.", marketMatchingResponse);
		String json = new Gson().toJson(marketMatchingResponse, MarketMatchingResponse.class);
		assertNotNull("testCase16() marketMatchingResponse.getError() should not be null.", marketMatchingResponse.getError());
		assertEquals("testCase16() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00010\",\"error_message\":\"Request parameter number_of_best_matches is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase16() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase17() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_MARKET_MATCHING;
		System.out.println("testCase17() requestUrl=" + requestUrl);
		MarketMatchingRequest marketMatchingRequest = new MarketMatchingRequest();
		marketMatchingRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		marketMatchingRequest.setTest_market("TEST");
		marketMatchingRequest.setPre_period_start_date("2021-01-01");
		marketMatchingRequest.setPre_period_end_date("2021-10-02");
		marketMatchingRequest.setPost_period_start_date("2021-10-03");
		marketMatchingRequest.setPost_period_end_date("2021-12-31");
		marketMatchingRequest.setNumber_of_best_matches(0);
		String requestParameters = new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class);
		MarketMatchingResponse marketMatchingResponse = politeCrawlWebServiceClientService.marketMatching(requestUrl, requestParameters);
		assertNotNull("testCase17() marketMatchingResponse should not be null.", marketMatchingResponse);
		String json = new Gson().toJson(marketMatchingResponse, MarketMatchingResponse.class);
		assertNotNull("testCase17() marketMatchingResponse.getError() should not be null.", marketMatchingResponse.getError());
		assertEquals("testCase17() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00010\",\"error_message\":\"Request parameter number_of_best_matches is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase17() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase18() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_MARKET_MATCHING;
		System.out.println("testCase18() requestUrl=" + requestUrl);
		MarketMatchingRequest marketMatchingRequest = new MarketMatchingRequest();
		marketMatchingRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		marketMatchingRequest.setTest_market("TEST");
		marketMatchingRequest.setPre_period_start_date("2021-01-01");
		marketMatchingRequest.setPre_period_end_date("2021-10-02");
		marketMatchingRequest.setPost_period_start_date("2021-10-03");
		marketMatchingRequest.setPost_period_end_date("2021-12-31");
		marketMatchingRequest.setNumber_of_best_matches(5);
		String requestParameters = new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class);
		MarketMatchingResponse marketMatchingResponse = politeCrawlWebServiceClientService.marketMatching(requestUrl, requestParameters);
		assertNotNull("testCase18() marketMatchingResponse should not be null.", marketMatchingResponse);
		String json = new Gson().toJson(marketMatchingResponse, MarketMatchingResponse.class);
		assertNotNull("testCase18() marketMatchingResponse.getError() should not be null.", marketMatchingResponse.getError());
		assertEquals("testCase18() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00011\",\"error_message\":\"Request parameter area_date_value_array is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase18() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase19() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_MARKET_MATCHING;
		System.out.println("testCase19() requestUrl=" + requestUrl);
		MarketMatchingRequest marketMatchingRequest = new MarketMatchingRequest();
		marketMatchingRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		marketMatchingRequest.setTest_market("TEST");
		marketMatchingRequest.setPre_period_start_date("2021-01-01");
		marketMatchingRequest.setPre_period_end_date("2021-10-02");
		marketMatchingRequest.setPost_period_start_date("2021-10-03");
		marketMatchingRequest.setPost_period_end_date("2021-12-31");
		marketMatchingRequest.setNumber_of_best_matches(5);
		AreaDateValue[] areaDateValueArray = new AreaDateValue[1];
		AreaDateValue areaDateValue = new AreaDateValue();
		areaDateValueArray[0] = areaDateValue;
		marketMatchingRequest.setArea_date_value_array(areaDateValueArray);
		String requestParameters = new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class);
		MarketMatchingResponse marketMatchingResponse = politeCrawlWebServiceClientService.marketMatching(requestUrl, requestParameters);
		assertNotNull("testCase19() marketMatchingResponse should not be null.", marketMatchingResponse);
		String json = new Gson().toJson(marketMatchingResponse, MarketMatchingResponse.class);
		assertNotNull("testCase19() marketMatchingResponse.getError() should not be null.", marketMatchingResponse.getError());
		assertEquals("testCase19() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00020\",\"error_message\":\"Request parameter area_date_value_array.area_array is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase19() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase20() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		AreaDateValue areaDateValue = null;
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_MARKET_MATCHING;
		System.out.println("testCase20() requestUrl=" + requestUrl);
		MarketMatchingRequest marketMatchingRequest = new MarketMatchingRequest();
		marketMatchingRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		marketMatchingRequest.setTest_market("TEST");
		marketMatchingRequest.setPre_period_start_date("2021-01-01");
		marketMatchingRequest.setPre_period_end_date("2021-10-02");
		marketMatchingRequest.setPost_period_start_date("2021-10-03");
		marketMatchingRequest.setPost_period_end_date("2021-12-31");
		marketMatchingRequest.setNumber_of_best_matches(1);

		List<AreaDateValue> areaDateValueList = new ArrayList<AreaDateValue>();

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValueList.add(areaDateValue);

		AreaDateValue[] areaDateValueArray = areaDateValueList.toArray(new AreaDateValue[0]);

		marketMatchingRequest.setArea_date_value_array(areaDateValueArray);
		String requestParameters = new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class);
		MarketMatchingResponse marketMatchingResponse = politeCrawlWebServiceClientService.marketMatching(requestUrl, requestParameters);
		assertNotNull("testCase20() marketMatchingResponse should not be null.", marketMatchingResponse);
		String json = new Gson().toJson(marketMatchingResponse, MarketMatchingResponse.class);
		assertNotNull("testCase20() marketMatchingResponse.getError() should not be null.", marketMatchingResponse.getError());
		assertEquals("testCase20() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00026\",\"error_message\":\"Number of dates in area_date_value_array does not match pre_period_start_date to post_period_end_date.\"}}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase20() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase21() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		AreaDateValue areaDateValue = null;
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_MARKET_MATCHING;
		System.out.println("testCase21() requestUrl=" + requestUrl);
		MarketMatchingRequest marketMatchingRequest = new MarketMatchingRequest();
		marketMatchingRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		marketMatchingRequest.setTest_market("TEST");
		marketMatchingRequest.setPre_period_start_date("2021-01-01");
		marketMatchingRequest.setPre_period_end_date("2021-01-02");
		marketMatchingRequest.setPost_period_start_date("2021-01-03");
		marketMatchingRequest.setPost_period_end_date("2021-01-04");
		marketMatchingRequest.setNumber_of_best_matches(1);

		List<AreaDateValue> areaDateValueList = new ArrayList<AreaDateValue>();

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValueList.add(areaDateValue);

		AreaDateValue[] areaDateValueArray = areaDateValueList.toArray(new AreaDateValue[0]);

		marketMatchingRequest.setArea_date_value_array(areaDateValueArray);
		String requestParameters = new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class);
		MarketMatchingResponse marketMatchingResponse = politeCrawlWebServiceClientService.marketMatching(requestUrl, requestParameters);
		assertNotNull("testCase21() marketMatchingResponse should not be null.", marketMatchingResponse);
		String json = new Gson().toJson(marketMatchingResponse, MarketMatchingResponse.class);
		assertNotNull("testCase21() marketMatchingResponse.getError() should not be null.", marketMatchingResponse.getError());
		assertEquals("testCase21() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00021\",\"error_message\":\"Request parameter area_date_value_array.area_array is invalid.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase21() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase22() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		AreaDateValue areaDateValue = null;
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_MARKET_MATCHING;
		System.out.println("testCase22() requestUrl=" + requestUrl);
		MarketMatchingRequest marketMatchingRequest = new MarketMatchingRequest();
		marketMatchingRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		marketMatchingRequest.setTest_market("TEST");
		marketMatchingRequest.setPre_period_start_date("2021-01-01");
		marketMatchingRequest.setPre_period_end_date("2021-01-02");
		marketMatchingRequest.setPost_period_start_date("2021-01-03");
		marketMatchingRequest.setPost_period_end_date("2021-01-04");
		marketMatchingRequest.setNumber_of_best_matches(1);

		List<AreaDateValue> areaDateValueList = new ArrayList<AreaDateValue>();

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValueList.add(areaDateValue);

		AreaDateValue[] areaDateValueArray = areaDateValueList.toArray(new AreaDateValue[0]);

		marketMatchingRequest.setArea_date_value_array(areaDateValueArray);
		String requestParameters = new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class);
		MarketMatchingResponse marketMatchingResponse = politeCrawlWebServiceClientService.marketMatching(requestUrl, requestParameters);
		assertNotNull("testCase22() marketMatchingResponse should not be null.", marketMatchingResponse);
		String json = new Gson().toJson(marketMatchingResponse, MarketMatchingResponse.class);
		assertNotNull("testCase22() marketMatchingResponse.getError() should not be null.", marketMatchingResponse.getError());
		assertEquals("testCase22() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00022\",\"error_message\":\"Request parameter area_date_value_array.date_array is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase22() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase23() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		AreaDateValue areaDateValue = null;
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_MARKET_MATCHING;
		System.out.println("testCase23() requestUrl=" + requestUrl);
		MarketMatchingRequest marketMatchingRequest = new MarketMatchingRequest();
		marketMatchingRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		marketMatchingRequest.setTest_market("TEST");
		marketMatchingRequest.setPre_period_start_date("2021-01-01");
		marketMatchingRequest.setPre_period_end_date("2021-01-02");
		marketMatchingRequest.setPost_period_start_date("2021-01-03");
		marketMatchingRequest.setPost_period_end_date("2021-01-04");
		marketMatchingRequest.setNumber_of_best_matches(1);

		List<AreaDateValue> areaDateValueList = new ArrayList<AreaDateValue>();

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValue.setDate("DATE1");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValueList.add(areaDateValue);

		AreaDateValue[] areaDateValueArray = areaDateValueList.toArray(new AreaDateValue[0]);

		marketMatchingRequest.setArea_date_value_array(areaDateValueArray);
		String requestParameters = new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class);
		MarketMatchingResponse marketMatchingResponse = politeCrawlWebServiceClientService.marketMatching(requestUrl, requestParameters);
		assertNotNull("testCase23() marketMatchingResponse should not be null.", marketMatchingResponse);
		String json = new Gson().toJson(marketMatchingResponse, MarketMatchingResponse.class);
		assertNotNull("testCase23() marketMatchingResponse.getError() should not be null.", marketMatchingResponse.getError());
		assertEquals("testCase23() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00023\",\"error_message\":\"Request parameter area_date_value_array.date_array is invalid.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase23() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase24() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		AreaDateValue areaDateValue = null;
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_MARKET_MATCHING;
		System.out.println("testCase24() requestUrl=" + requestUrl);
		MarketMatchingRequest marketMatchingRequest = new MarketMatchingRequest();
		marketMatchingRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		marketMatchingRequest.setTest_market("TEST");
		marketMatchingRequest.setPre_period_start_date("2021-01-01");
		marketMatchingRequest.setPre_period_end_date("2021-01-02");
		marketMatchingRequest.setPost_period_start_date("2021-01-03");
		marketMatchingRequest.setPost_period_end_date("2021-01-04");
		marketMatchingRequest.setNumber_of_best_matches(1);

		List<AreaDateValue> areaDateValueList = new ArrayList<AreaDateValue>();

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValue.setDate("2021-01-01");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValueList.add(areaDateValue);

		AreaDateValue[] areaDateValueArray = areaDateValueList.toArray(new AreaDateValue[0]);

		marketMatchingRequest.setArea_date_value_array(areaDateValueArray);
		String requestParameters = new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class);
		MarketMatchingResponse marketMatchingResponse = politeCrawlWebServiceClientService.marketMatching(requestUrl, requestParameters);
		assertNotNull("testCase24() marketMatchingResponse should not be null.", marketMatchingResponse);
		String json = new Gson().toJson(marketMatchingResponse, MarketMatchingResponse.class);
		assertNotNull("testCase24() marketMatchingResponse.getError() should not be null.", marketMatchingResponse.getError());
		assertEquals("testCase24() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00023\",\"error_message\":\"Request parameter area_date_value_array.date_array is invalid.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase24() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase25() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		AreaDateValue areaDateValue = null;
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_MARKET_MATCHING;
		System.out.println("testCase25() requestUrl=" + requestUrl);
		MarketMatchingRequest marketMatchingRequest = new MarketMatchingRequest();
		marketMatchingRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		marketMatchingRequest.setTest_market("TEST");
		marketMatchingRequest.setPre_period_start_date("2021-01-01");
		marketMatchingRequest.setPre_period_end_date("2021-01-02");
		marketMatchingRequest.setPost_period_start_date("2021-01-03");
		marketMatchingRequest.setPost_period_end_date("2021-01-04");
		marketMatchingRequest.setNumber_of_best_matches(1);

		List<AreaDateValue> areaDateValueList = new ArrayList<AreaDateValue>();

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValue.setDate("2021-01-01");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValueList.add(areaDateValue);

		AreaDateValue[] areaDateValueArray = areaDateValueList.toArray(new AreaDateValue[0]);

		marketMatchingRequest.setArea_date_value_array(areaDateValueArray);
		String requestParameters = new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class);
		MarketMatchingResponse marketMatchingResponse = politeCrawlWebServiceClientService.marketMatching(requestUrl, requestParameters);
		assertNotNull("testCase25() marketMatchingResponse should not be null.", marketMatchingResponse);
		String json = new Gson().toJson(marketMatchingResponse, MarketMatchingResponse.class);
		assertNotNull("testCase25() marketMatchingResponse.getError() should not be null.", marketMatchingResponse.getError());
		assertEquals("testCase25() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00023\",\"error_message\":\"Request parameter area_date_value_array.date_array is invalid.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase25() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase26() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		AreaDateValue areaDateValue = null;
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_MARKET_MATCHING;
		System.out.println("testCase26() requestUrl=" + requestUrl);
		MarketMatchingRequest marketMatchingRequest = new MarketMatchingRequest();
		marketMatchingRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		marketMatchingRequest.setTest_market("TEST");
		marketMatchingRequest.setPre_period_start_date("2021-01-01");
		marketMatchingRequest.setPre_period_end_date("2021-01-02");
		marketMatchingRequest.setPost_period_start_date("2021-01-03");
		marketMatchingRequest.setPost_period_end_date("2021-01-04");
		marketMatchingRequest.setNumber_of_best_matches(1);

		List<AreaDateValue> areaDateValueList = new ArrayList<AreaDateValue>();

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValue.setDate("2021-01-01");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValue.setDate("2021-01-01");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValue.setDate("2021-01-01");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValue.setDate("2021-01-01");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValue.setDate("2021-01-01");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValue.setDate("2021-01-01");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValue.setDate("2021-01-01");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValue.setDate("2021-01-01");
		areaDateValueList.add(areaDateValue);

		AreaDateValue[] areaDateValueArray = areaDateValueList.toArray(new AreaDateValue[0]);

		marketMatchingRequest.setArea_date_value_array(areaDateValueArray);
		String requestParameters = new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class);
		MarketMatchingResponse marketMatchingResponse = politeCrawlWebServiceClientService.marketMatching(requestUrl, requestParameters);
		assertNotNull("testCase26() marketMatchingResponse should not be null.", marketMatchingResponse);
		String json = new Gson().toJson(marketMatchingResponse, MarketMatchingResponse.class);
		assertNotNull("testCase26() marketMatchingResponse.getError() should not be null.", marketMatchingResponse.getError());
		assertEquals("testCase26() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00023\",\"error_message\":\"Request parameter area_date_value_array.date_array is invalid.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase26() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase27() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		AreaDateValue areaDateValue = null;
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_MARKET_MATCHING;
		System.out.println("testCase27() requestUrl=" + requestUrl);
		MarketMatchingRequest marketMatchingRequest = new MarketMatchingRequest();
		marketMatchingRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		marketMatchingRequest.setTest_market("TEST");
		marketMatchingRequest.setPre_period_start_date("2021-01-01");
		marketMatchingRequest.setPre_period_end_date("2021-01-02");
		marketMatchingRequest.setPost_period_start_date("2021-01-03");
		marketMatchingRequest.setPost_period_end_date("2021-01-04");
		marketMatchingRequest.setNumber_of_best_matches(1);

		List<AreaDateValue> areaDateValueList = new ArrayList<AreaDateValue>();

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValue.setDate("2021-01-01");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValue.setDate("2021-01-01");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValue.setDate("2021-01-01");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValue.setDate("2021-01-01");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValue.setDate("2021-01-01");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValue.setDate("2021-01-04");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValue.setDate("2021-01-01");
		areaDateValueList.add(areaDateValue);

		AreaDateValue[] areaDateValueArray = areaDateValueList.toArray(new AreaDateValue[0]);

		marketMatchingRequest.setArea_date_value_array(areaDateValueArray);
		String requestParameters = new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class);
		MarketMatchingResponse marketMatchingResponse = politeCrawlWebServiceClientService.marketMatching(requestUrl, requestParameters);
		assertNotNull("testCase27() marketMatchingResponse should not be null.", marketMatchingResponse);
		String json = new Gson().toJson(marketMatchingResponse, MarketMatchingResponse.class);
		assertNotNull("testCase27() marketMatchingResponse.getError() should not be null.", marketMatchingResponse.getError());
		assertEquals("testCase27() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00023\",\"error_message\":\"Request parameter area_date_value_array.date_array is invalid.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase27() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase28() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		AreaDateValue areaDateValue = null;
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_MARKET_MATCHING;
		System.out.println("testCase28() requestUrl=" + requestUrl);
		MarketMatchingRequest marketMatchingRequest = new MarketMatchingRequest();
		marketMatchingRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		marketMatchingRequest.setTest_market("TEST");
		marketMatchingRequest.setPre_period_start_date("2021-01-01");
		marketMatchingRequest.setPre_period_end_date("2021-01-02");
		marketMatchingRequest.setPost_period_start_date("2021-01-03");
		marketMatchingRequest.setPost_period_end_date("2021-01-04");
		marketMatchingRequest.setNumber_of_best_matches(1);

		List<AreaDateValue> areaDateValueList = new ArrayList<AreaDateValue>();

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValue.setDate("2021-01-02");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValue.setDate("2021-01-03");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValue.setDate("2021-01-04");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValue.setDate("2021-01-05");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValue.setDate("2021-01-01");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValue.setDate("2021-01-02");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValue.setDate("2021-01-03");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValue.setDate("2021-01-04");
		areaDateValueList.add(areaDateValue);

		AreaDateValue[] areaDateValueArray = areaDateValueList.toArray(new AreaDateValue[0]);

		marketMatchingRequest.setArea_date_value_array(areaDateValueArray);
		String requestParameters = new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class);
		MarketMatchingResponse marketMatchingResponse = politeCrawlWebServiceClientService.marketMatching(requestUrl, requestParameters);
		assertNotNull("testCase28() marketMatchingResponse should not be null.", marketMatchingResponse);
		String json = new Gson().toJson(marketMatchingResponse, MarketMatchingResponse.class);
		assertNotNull("testCase28() marketMatchingResponse.getError() should not be null.", marketMatchingResponse.getError());
		assertEquals("testCase28() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00023\",\"error_message\":\"Request parameter area_date_value_array.date_array is invalid.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase28() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase29() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		AreaDateValue areaDateValue = null;
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_MARKET_MATCHING;
		System.out.println("testCase29() requestUrl=" + requestUrl);
		MarketMatchingRequest marketMatchingRequest = new MarketMatchingRequest();
		marketMatchingRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		marketMatchingRequest.setTest_market("TEST");
		marketMatchingRequest.setPre_period_start_date("2021-01-01");
		marketMatchingRequest.setPre_period_end_date("2021-01-02");
		marketMatchingRequest.setPost_period_start_date("2021-01-03");
		marketMatchingRequest.setPost_period_end_date("2021-01-04");
		marketMatchingRequest.setNumber_of_best_matches(1);

		List<AreaDateValue> areaDateValueList = new ArrayList<AreaDateValue>();

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValue.setDate("2021-01-01");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValue.setDate("2021-01-02");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValue.setDate("2021-01-03");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValue.setDate("2021-01-05");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValue.setDate("2021-01-01");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValue.setDate("2021-01-02");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValue.setDate("2021-01-03");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValue.setDate("2021-01-04");
		areaDateValueList.add(areaDateValue);

		AreaDateValue[] areaDateValueArray = areaDateValueList.toArray(new AreaDateValue[0]);

		marketMatchingRequest.setArea_date_value_array(areaDateValueArray);
		String requestParameters = new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class);
		MarketMatchingResponse marketMatchingResponse = politeCrawlWebServiceClientService.marketMatching(requestUrl, requestParameters);
		assertNotNull("testCase29() marketMatchingResponse should not be null.", marketMatchingResponse);
		String json = new Gson().toJson(marketMatchingResponse, MarketMatchingResponse.class);
		assertNotNull("testCase29() marketMatchingResponse.getError() should not be null.", marketMatchingResponse.getError());
		assertEquals("testCase29() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00023\",\"error_message\":\"Request parameter area_date_value_array.date_array is invalid.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase29() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase30() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		AreaDateValue areaDateValue = null;
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_MARKET_MATCHING;
		System.out.println("testCase30() requestUrl=" + requestUrl);
		MarketMatchingRequest marketMatchingRequest = new MarketMatchingRequest();
		marketMatchingRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		marketMatchingRequest.setTest_market("TEST");
		marketMatchingRequest.setPre_period_start_date("2021-01-01");
		marketMatchingRequest.setPre_period_end_date("2021-01-02");
		marketMatchingRequest.setPost_period_start_date("2021-01-03");
		marketMatchingRequest.setPost_period_end_date("2021-01-04");
		marketMatchingRequest.setNumber_of_best_matches(1);

		List<AreaDateValue> areaDateValueList = new ArrayList<AreaDateValue>();

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValue.setDate("2021-01-01");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValue.setDate("2021-01-02");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValue.setDate("2021-01-03");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValue.setDate("2021-01-04");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValue.setDate("2021-01-01");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValue.setDate("2021-01-02");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValue.setDate("2021-01-03");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValue.setDate("2021-01-04");
		areaDateValueList.add(areaDateValue);

		AreaDateValue[] areaDateValueArray = areaDateValueList.toArray(new AreaDateValue[0]);

		marketMatchingRequest.setArea_date_value_array(areaDateValueArray);
		String requestParameters = new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class);
		MarketMatchingResponse marketMatchingResponse = politeCrawlWebServiceClientService.marketMatching(requestUrl, requestParameters);
		assertNotNull("testCase30() marketMatchingResponse should not be null.", marketMatchingResponse);
		String json = new Gson().toJson(marketMatchingResponse, MarketMatchingResponse.class);
		assertNotNull("testCase30() marketMatchingResponse.getError() should not be null.", marketMatchingResponse.getError());
		assertEquals("testCase30() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00024\",\"error_message\":\"Request parameter area_date_value_array.value_array is required.\"}}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase30() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase31() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		AreaDateValue areaDateValue = null;
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_MARKET_MATCHING;
		System.out.println("testCase31() requestUrl=" + requestUrl);
		MarketMatchingRequest marketMatchingRequest = new MarketMatchingRequest();
		marketMatchingRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		marketMatchingRequest.setTest_market("TEST");
		marketMatchingRequest.setPre_period_start_date("2021-01-01");
		marketMatchingRequest.setPre_period_end_date("2021-01-02");
		marketMatchingRequest.setPost_period_start_date("2021-01-03");
		marketMatchingRequest.setPost_period_end_date("2021-01-04");
		marketMatchingRequest.setNumber_of_best_matches(1);

		List<AreaDateValue> areaDateValueList = new ArrayList<AreaDateValue>();

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValue.setDate("2021-01-01");
		areaDateValue.setValue(1.1D);
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValue.setDate("2021-01-02");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValue.setDate("2021-01-03");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValue.setDate("2021-01-04");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValue.setDate("2021-01-01");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValue.setDate("2021-01-02");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValue.setDate("2021-01-03");
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValue.setDate("2021-01-04");
		areaDateValueList.add(areaDateValue);

		AreaDateValue[] areaDateValueArray = areaDateValueList.toArray(new AreaDateValue[0]);

		marketMatchingRequest.setArea_date_value_array(areaDateValueArray);
		String requestParameters = new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class);
		MarketMatchingResponse marketMatchingResponse = politeCrawlWebServiceClientService.marketMatching(requestUrl, requestParameters);
		assertNotNull("testCase31() marketMatchingResponse should not be null.", marketMatchingResponse);
		String json = new Gson().toJson(marketMatchingResponse, MarketMatchingResponse.class);
		assertNotNull("testCase31() marketMatchingResponse.getError() should not be null.", marketMatchingResponse.getError());
		assertEquals("testCase31() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00025\",\"error_message\":\"Request parameter area_date_value_array.value_array is invalid.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase31() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase32() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		AreaDateValue areaDateValue = null;
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_MARKET_MATCHING;
		System.out.println("testCase32() requestUrl=" + requestUrl);
		MarketMatchingRequest marketMatchingRequest = new MarketMatchingRequest();
		marketMatchingRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		marketMatchingRequest.setTest_market("TEST");
		marketMatchingRequest.setPre_period_start_date("2021-01-01");
		marketMatchingRequest.setPre_period_end_date("2021-01-02");
		marketMatchingRequest.setPost_period_start_date("2021-01-03");
		marketMatchingRequest.setPost_period_end_date("2021-01-04");
		marketMatchingRequest.setNumber_of_best_matches(1);

		List<AreaDateValue> areaDateValueList = new ArrayList<AreaDateValue>();

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValue.setDate("2021-01-01");
		areaDateValue.setValue(1.1D);
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValue.setDate("2021-01-02");
		areaDateValue.setValue(1.1D);
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValue.setDate("2021-01-03");
		areaDateValue.setValue(1.1D);
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValue.setDate("2021-01-04");
		areaDateValue.setValue(1.1D);
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValue.setDate("2021-01-01");
		areaDateValue.setValue(1.1D);
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValue.setDate("2021-01-02");
		areaDateValue.setValue(1.1D);
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValue.setDate("2021-01-03");
		areaDateValue.setValue(1.1D);
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValue.setDate("2021-01-04");
		areaDateValue.setValue(1.1D);
		areaDateValueList.add(areaDateValue);

		AreaDateValue[] areaDateValueArray = areaDateValueList.toArray(new AreaDateValue[0]);

		marketMatchingRequest.setArea_date_value_array(areaDateValueArray);
		String requestParameters = new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class);
		MarketMatchingResponse marketMatchingResponse = politeCrawlWebServiceClientService.marketMatching(requestUrl, requestParameters);
		assertNotNull("testCase32() marketMatchingResponse should not be null.", marketMatchingResponse);
		String json = new Gson().toJson(marketMatchingResponse, MarketMatchingResponse.class);
		assertNotNull("testCase32() marketMatchingResponse.getError() should not be null.", marketMatchingResponse.getError());
		assertEquals("testCase32() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00012\",\"error_message\":\"Request parameter test_market TEST is invalid.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase32() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase33() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		AreaDateValue areaDateValue = null;
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_MARKET_MATCHING;
		System.out.println("testCase33() requestUrl=" + requestUrl);
		MarketMatchingRequest marketMatchingRequest = new MarketMatchingRequest();
		marketMatchingRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		marketMatchingRequest.setTest_market("AREA1");
		marketMatchingRequest.setPre_period_start_date("2021-01-01");
		marketMatchingRequest.setPre_period_end_date("2021-01-02");
		marketMatchingRequest.setPost_period_start_date("2021-01-03");
		marketMatchingRequest.setPost_period_end_date("2021-01-04");
		marketMatchingRequest.setNumber_of_best_matches(5);

		List<AreaDateValue> areaDateValueList = new ArrayList<AreaDateValue>();

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValue.setDate("2021-01-01");
		areaDateValue.setValue(1.1D);
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValue.setDate("2021-01-02");
		areaDateValue.setValue(1.1D);
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValue.setDate("2021-01-03");
		areaDateValue.setValue(1.1D);
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValue.setDate("2021-01-04");
		areaDateValue.setValue(1.1D);
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValue.setDate("2021-01-01");
		areaDateValue.setValue(1.1D);
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValue.setDate("2021-01-02");
		areaDateValue.setValue(1.1D);
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValue.setDate("2021-01-03");
		areaDateValue.setValue(1.1D);
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValue.setDate("2021-01-04");
		areaDateValue.setValue(1.1D);
		areaDateValueList.add(areaDateValue);

		AreaDateValue[] areaDateValueArray = areaDateValueList.toArray(new AreaDateValue[0]);

		marketMatchingRequest.setArea_date_value_array(areaDateValueArray);
		String requestParameters = new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class);
		MarketMatchingResponse marketMatchingResponse = politeCrawlWebServiceClientService.marketMatching(requestUrl, requestParameters);
		assertNotNull("testCase33() marketMatchingResponse should not be null.", marketMatchingResponse);
		String json = new Gson().toJson(marketMatchingResponse, MarketMatchingResponse.class);
		assertNotNull("testCase33() marketMatchingResponse.getError() should not be null.", marketMatchingResponse.getError());
		assertEquals("testCase33() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00027\",\"error_message\":\"Request parameter number_of_best_matches 5 is invalid.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase33() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase34() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		AreaDateValue areaDateValue = null;
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_MARKET_MATCHING;
		System.out.println("testCase34() requestUrl=" + requestUrl);
		MarketMatchingRequest marketMatchingRequest = new MarketMatchingRequest();
		marketMatchingRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		marketMatchingRequest.setTest_market("AREA1");
		marketMatchingRequest.setPre_period_start_date("2021-01-01");
		marketMatchingRequest.setPre_period_end_date("2021-01-02");
		marketMatchingRequest.setPost_period_start_date("2021-01-03");
		marketMatchingRequest.setPost_period_end_date("2021-01-04");
		marketMatchingRequest.setNumber_of_best_matches(1);

		List<AreaDateValue> areaDateValueList = new ArrayList<AreaDateValue>();

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValue.setDate("2021-01-01");
		areaDateValue.setValue(1.1D);
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValue.setDate("2021-01-02");
		areaDateValue.setValue(1.1D);
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValue.setDate("2021-01-03");
		areaDateValue.setValue(1.1D);
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA1");
		areaDateValue.setDate("2021-01-04");
		areaDateValue.setValue(1.1D);
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValue.setDate("2021-01-01");
		areaDateValue.setValue(1.1D);
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValue.setDate("2021-01-02");
		areaDateValue.setValue(1.1D);
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValue.setDate("2021-01-03");
		areaDateValue.setValue(1.1D);
		areaDateValueList.add(areaDateValue);

		//
		areaDateValue = new AreaDateValue();
		areaDateValue.setArea("AREA2");
		areaDateValue.setDate("2021-01-04");
		areaDateValue.setValue(1.1D);
		areaDateValueList.add(areaDateValue);

		AreaDateValue[] areaDateValueArray = areaDateValueList.toArray(new AreaDateValue[0]);

		marketMatchingRequest.setArea_date_value_array(areaDateValueArray);
		String requestParameters = new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class);
		MarketMatchingResponse marketMatchingResponse = politeCrawlWebServiceClientService.marketMatching(requestUrl, requestParameters);
		assertNotNull("testCase34() marketMatchingResponse should not be null.", marketMatchingResponse);
		assertTrue("testCase34() marketMatchingResponse.getSuccess() should be true.", marketMatchingResponse.getSuccess());
		FormatUtils.getInstance().logMemoryUsage("testCase34() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase35() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_MARKET_MATCHING;
		System.out.println("testCase35() requestUrl=" + requestUrl);
		MarketMatchingRequest marketMatchingRequest = getMarketMatchingRequest35();
		String requestParameters = new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class);
		MarketMatchingResponse marketMatchingResponse = politeCrawlWebServiceClientService.marketMatching(requestUrl, requestParameters);
		assertNotNull("testCase35() marketMatchingResponse should not be null.", marketMatchingResponse);
		assertTrue("testCase35() marketMatchingResponse.getSuccess() should be true.", marketMatchingResponse.getSuccess());
		FormatUtils.getInstance().logMemoryUsage("testCase35() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase36() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_MARKET_MATCHING;
		System.out.println("testCase36() requestUrl=" + requestUrl);
		MarketMatchingRequest marketMatchingRequest = getMarketMatchingRequest36();
		String requestParameters = new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class);
		MarketMatchingResponse marketMatchingResponse = politeCrawlWebServiceClientService.marketMatching(requestUrl, requestParameters);
		assertNotNull("testCase36() marketMatchingResponse should not be null.", marketMatchingResponse);
		assertTrue("testCase36() marketMatchingResponse.getSuccess() should be true.", marketMatchingResponse.getSuccess());
		FormatUtils.getInstance().logMemoryUsage("testCase36() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

}