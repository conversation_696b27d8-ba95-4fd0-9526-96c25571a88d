package com.actonia.test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import com.actonia.IConstants;
import com.actonia.dao.ZapierWebhookDAO;
import com.actonia.entity.ZapierWebhookEntity;
import com.actonia.service.AccessTokenService;
import com.actonia.service.PoliteCrawlWebServiceClientService;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.ZapierPageTagContentAlert;
import com.actonia.value.object.ZapierResourceRequest;
import com.actonia.value.object.ZapierResourceResponse;
import com.google.gson.Gson;

public class ZapierPageTagContentAlertResourceTest {

	private boolean isUnitTest = true;
	private PoliteCrawlWebServiceClientService politeCrawlWebServiceClientService;
	private ZapierWebhookDAO zapierWebhookDAO;

	public ZapierPageTagContentAlertResourceTest() {
		super();
		this.politeCrawlWebServiceClientService = SpringBeanFactory.getBean("politeCrawlWebServiceClientService");
		this.zapierWebhookDAO = SpringBeanFactory.getBean("zapierWebhookDAO");
	}

	public static void main(String[] args) throws Exception {
		new ZapierPageTagContentAlertResourceTest().runTests();
	}

	public void runTests() throws Exception {

		testCase1();
		testCase2();
		if (isUnitTest == true) {
			testCase3();
		}
		testCase4();
		testCase5();
		testCase8();
		testCase9();
		testCase12();
		testCase13();
		testCase14();
		testCase15();
		testCase16();
		testCase17();
		testCase18();
		testCase19();
		testCase20();
		testCase21();
		testCase22();
		testCase23();
		testCase24();
		testCase25();
		testCase26();
		testCase27();
		testCase28();
	}

	private void testCase1() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_PAGE_TAG_CONTENT_ALERT;
		System.out.println("testCase1() requestUrl=" + requestUrl);
		String requestParameters = IConstants.EMPTY_STRING;

		ZapierResourceResponse zapierResourceResponse = politeCrawlWebServiceClientService.zapierPost(requestUrl, requestParameters);
		assertNotNull("testCase1() zapierResourceResponse should not be null.", zapierResourceResponse);
		String json = new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class);
		assertEquals("testCase1() json is incorrect", "{\"error_code\":\"00001\",\"error_message\":\"Request data is required.\"}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase1() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase2() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_PAGE_TAG_CONTENT_ALERT;
		System.out.println("testCase2() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierResourceRequest = new ZapierResourceRequest();
		String requestParameters = new Gson().toJson(zapierResourceRequest, ZapierResourceRequest.class);

		ZapierResourceResponse zapierResourceResponse = politeCrawlWebServiceClientService.zapierPost(requestUrl, requestParameters);
		assertNotNull("testCase2() zapierResourceResponse should not be null.", zapierResourceResponse);
		String json = new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class);
		assertEquals("testCase2() json is incorrect", "{\"error_code\":\"00002\",\"error_message\":\"Request parameter access_token is required.\"}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase2() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase3() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_PAGE_TAG_CONTENT_ALERT;
		System.out.println("testCase3() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierResourceRequest = new ZapierResourceRequest();
		zapierResourceRequest.setAccess_token("testaccesstoken");
		String requestParameters = new Gson().toJson(zapierResourceRequest, ZapierResourceRequest.class);

		ZapierResourceResponse zapierResourceResponse = politeCrawlWebServiceClientService.zapierPost(requestUrl, requestParameters);
		assertNotNull("testCase3() zapierResourceResponse should not be null.", zapierResourceResponse);
		String json = new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class);
		assertEquals("testCase3() json is incorrect", "{\"error_code\":\"00003\",\"error_message\":\"Request parameter access_token testaccesstoken is invalid.\"}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase3() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase4() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_PAGE_TAG_CONTENT_ALERT;
		System.out.println("testCase4() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierResourceRequest = new ZapierResourceRequest();
		zapierResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		String requestParameters = new Gson().toJson(zapierResourceRequest, ZapierResourceRequest.class);

		ZapierResourceResponse zapierResourceResponse = politeCrawlWebServiceClientService.zapierPost(requestUrl, requestParameters);
		assertNotNull("testCase4() zapierResourceResponse should not be null.", zapierResourceResponse);
		String json = new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class);
		assertEquals("testCase4() json is incorrect", "{\"error_code\":\"00004\",\"error_message\":\"Request parameter domain_id is required.\"}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase4() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase5() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_PAGE_TAG_CONTENT_ALERT;
		System.out.println("testCase5() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierResourceRequest = new ZapierResourceRequest();
		zapierResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		zapierResourceRequest.setDomain_id(1);
		String requestParameters = new Gson().toJson(zapierResourceRequest, ZapierResourceRequest.class);

		ZapierResourceResponse zapierResourceResponse = politeCrawlWebServiceClientService.zapierPost(requestUrl, requestParameters);
		assertNotNull("testCase5() zapierResourceResponse should not be null.", zapierResourceResponse);
		String json = new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class);
		assertEquals("testCase5() json is incorrect", "{\"error_code\":\"00005\",\"error_message\":\"Request parameter domain_id 1 is invalid.\"}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase5() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase8() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_PAGE_TAG_CONTENT_ALERT;
		System.out.println("testCase8() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierResourceRequest = new ZapierResourceRequest();
		zapierResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		zapierResourceRequest.setDomain_id(1701);
		zapierResourceRequest.setPage_tag_name("Page tag name 1");
		String requestParameters = new Gson().toJson(zapierResourceRequest, ZapierResourceRequest.class);

		ZapierResourceResponse zapierResourceResponse = politeCrawlWebServiceClientService.zapierPost(requestUrl, requestParameters);
		assertNotNull("testCase8() zapierResourceResponse should not be null.", zapierResourceResponse);
		String json = new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class);
		assertEquals("testCase8() json is incorrect", "{\"error_code\":\"00006\",\"error_message\":\"Request parameter user_email is required.\"}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase8() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase9() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_PAGE_TAG_CONTENT_ALERT;
		System.out.println("testCase9() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierResourceRequest = new ZapierResourceRequest();
		zapierResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		zapierResourceRequest.setDomain_id(1701);
		zapierResourceRequest.setPage_tag_name("Page tag name 1");
		zapierResourceRequest.setUser_email("<EMAIL>");
		String requestParameters = new Gson().toJson(zapierResourceRequest, ZapierResourceRequest.class);

		ZapierResourceResponse zapierResourceResponse = politeCrawlWebServiceClientService.zapierPost(requestUrl, requestParameters);
		assertNotNull("testCase9() zapierResourceResponse should not be null.", zapierResourceResponse);
		String json = new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class);
		assertEquals("testCase9() json is incorrect", "{\"error_code\":\"00007\",\"error_message\":\"Request parameter user_email <EMAIL> is invalid.\"}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase9() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase12() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_PAGE_TAG_CONTENT_ALERT;
		System.out.println("testCase12() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierResourceRequest = new ZapierResourceRequest();
		zapierResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		zapierResourceRequest.setDomain_id(1701);
		zapierResourceRequest.setPage_tag_name("Page tag name 1");
		zapierResourceRequest.setUser_email("<EMAIL>");
		String requestParameters = new Gson().toJson(zapierResourceRequest, ZapierResourceRequest.class);

		ZapierResourceResponse zapierResourceResponse = politeCrawlWebServiceClientService.zapierPost(requestUrl, requestParameters);
		assertNotNull("testCase12() zapierResourceResponse should not be null.", zapierResourceResponse);
		String json = new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class);
		assertEquals("testCase12() json is incorrect", "{\"error_code\":\"00008\",\"error_message\":\"Request parameter callback_url is required.\"}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase12() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase13() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_PAGE_TAG_CONTENT_ALERT;
		System.out.println("testCase13() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierResourceRequest = new ZapierResourceRequest();
		zapierResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		zapierResourceRequest.setDomain_id(1701);
		zapierResourceRequest.setPage_tag_name("Page tag name 1");
		zapierResourceRequest.setUser_email("<EMAIL>");
		zapierResourceRequest.setCallback_url("test");
		String requestParameters = new Gson().toJson(zapierResourceRequest, ZapierResourceRequest.class);

		ZapierResourceResponse zapierResourceResponse = politeCrawlWebServiceClientService.zapierPost(requestUrl, requestParameters);
		assertNotNull("testCase13() zapierResourceResponse should not be null.", zapierResourceResponse);
		String json = new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class);
		assertEquals("testCase13() json is incorrect", "{\"error_code\":\"00009\",\"error_message\":\"Request parameter callback_url test is invalid.\"}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase13() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase14() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		ZapierWebhookEntity zapierWebhookEntity = null;
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_PAGE_TAG_CONTENT_ALERT;
		System.out.println("testCase14() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierResourceRequest = new ZapierResourceRequest();
		zapierResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		zapierResourceRequest.setDomain_id(1701);
		zapierResourceRequest.setPage_tag_name("page-tag-1");
		zapierResourceRequest.setUser_email("<EMAIL>");
		zapierResourceRequest.setCallback_url("https://hook.zapier.com/callback_url_1");
		String requestParameters = new Gson().toJson(zapierResourceRequest, ZapierResourceRequest.class);

		ZapierResourceResponse zapierResourceResponse = politeCrawlWebServiceClientService.zapierPost(requestUrl, requestParameters);
		assertNotNull("testCase14() zapierResourceResponse should not be null.", zapierResourceResponse);
		assertNotNull("testCase14() zapierResourceResponse.getId() should not be null.", zapierResourceResponse.getId());
		if (isUnitTest == true) {
			zapierWebhookEntity = zapierWebhookDAO.get(zapierResourceResponse.getId());
			assertNotNull("testCase14() zapierWebhookEntity should not be null.", zapierWebhookEntity);
			assertEquals("testCase14() zapierWebhookEntity.getDomainId() is incorrect.", "1701", String.valueOf(zapierWebhookEntity.getDomainId()));
			assertEquals("testCase14() zapierWebhookEntity.getUserId() is incorrect.", "214", String.valueOf(zapierWebhookEntity.getUserId()));
			assertEquals("testCase14() zapierWebhookEntity.getSubType() is incorrect.", "1354725", zapierWebhookEntity.getSubType());
			assertEquals("testCase14() zapierWebhookEntity.getTriggerType() is incorrect.", "4", String.valueOf(zapierWebhookEntity.getTriggerType()));
			assertEquals("testCase14() zapierWebhookEntity.getCallbackUrl() is incorrect.", "https://hook.zapier.com/callback_url_1",
					String.valueOf(zapierWebhookEntity.getCallbackUrl()));
			zapierWebhookDAO.delete(zapierResourceResponse.getId());
		}
		FormatUtils.getInstance().logMemoryUsage("testCase14() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase15() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_PAGE_TAG_CONTENT_ALERT;
		System.out.println("testCase15() requestUrl=" + requestUrl);
		ZapierResourceResponse zapierResourceResponse = politeCrawlWebServiceClientService.zapierDelete(requestUrl);
		assertNotNull("testCase15() zapierResourceResponse should not be null.", zapierResourceResponse);
		String json = new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class);
		assertEquals("testCase15() json is incorrect", "{\"error_code\":\"00002\",\"error_message\":\"Request parameter access_token is required.\"}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase15() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase16() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_PAGE_TAG_CONTENT_ALERT + "?access_token=testaccesstoken";
		System.out.println("testCase16() requestUrl=" + requestUrl);
		ZapierResourceResponse zapierResourceResponse = politeCrawlWebServiceClientService.zapierDelete(requestUrl);
		assertNotNull("testCase16() zapierResourceResponse should not be null.", zapierResourceResponse);
		String json = new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class);
		assertEquals("testCase16() json is incorrect", "{\"error_code\":\"00003\",\"error_message\":\"Request parameter access_token testaccesstoken is invalid.\"}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase16() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase17() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_PAGE_TAG_CONTENT_ALERT + "?access_token=" + AccessTokenService.INTERNAL_KEY;
		System.out.println("testCase17() requestUrl=" + requestUrl);
		ZapierResourceResponse zapierResourceResponse = politeCrawlWebServiceClientService.zapierDelete(requestUrl);
		assertNotNull("testCase17() zapierResourceResponse should not be null.", zapierResourceResponse);
		String json = new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class);
		assertEquals("testCase17() json is incorrect", "{\"error_code\":\"00012\",\"error_message\":\"Request parameter id is required.\"}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase17() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase18() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_PAGE_TAG_CONTENT_ALERT + "?access_token=" + AccessTokenService.INTERNAL_KEY + "&id=abcd";
		System.out.println("testCase18() requestUrl=" + requestUrl);
		ZapierResourceResponse zapierResourceResponse = politeCrawlWebServiceClientService.zapierDelete(requestUrl);
		assertNotNull("testCase18() zapierResourceResponse should not be null.", zapierResourceResponse);
		String json = new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class);
		assertEquals("testCase18() json is incorrect", "{\"error_code\":\"00013\",\"error_message\":\"Request parameter id abcd is invalid.\"}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase18() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase19() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_PAGE_TAG_CONTENT_ALERT + "?access_token=" + AccessTokenService.INTERNAL_KEY + "&id=168168";
		System.out.println("testCase19() requestUrl=" + requestUrl);
		ZapierResourceResponse zapierResourceResponse = politeCrawlWebServiceClientService.zapierDelete(requestUrl);
		assertNotNull("testCase19() zapierResourceResponse should not be null.", zapierResourceResponse);
		String json = new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class);
		assertEquals("testCase19() json is incorrect", "{\"error_code\":\"00013\",\"error_message\":\"Request parameter id 168168 is invalid.\"}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase19() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase20() throws Exception {
		long startTimestamp = System.currentTimeMillis();

		// step 1: create subscription
		ZapierWebhookEntity zapierWebhookEntity = null;
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_PAGE_TAG_CONTENT_ALERT;
		System.out.println("testCase20() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierResourceRequest = new ZapierResourceRequest();
		zapierResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		zapierResourceRequest.setDomain_id(1701);
		zapierResourceRequest.setPage_tag_name("page-tag-1");
		zapierResourceRequest.setUser_email("<EMAIL>");
		zapierResourceRequest.setCallback_url("https://hook.zapier.com/callback_url_1");
		String requestParameters = new Gson().toJson(zapierResourceRequest, ZapierResourceRequest.class);

		ZapierResourceResponse zapierResourceResponse = politeCrawlWebServiceClientService.zapierPost(requestUrl, requestParameters);
		assertNotNull("testCase20() zapierResourceResponse should not be null.", zapierResourceResponse);
		assertNotNull("testCase20() zapierResourceResponse.getId() should not be null.", zapierResourceResponse.getId());
		if (isUnitTest == true) {
			zapierWebhookEntity = zapierWebhookDAO.get(zapierResourceResponse.getId());
			assertNotNull("testCase20() zapierWebhookEntity should not be null.", zapierWebhookEntity);
			assertEquals("testCase20() zapierWebhookEntity.getDomainId() is incorrect.", "1701", String.valueOf(zapierWebhookEntity.getDomainId()));
			assertEquals("testCase20() zapierWebhookEntity.getUserId() is incorrect.", "214", String.valueOf(zapierWebhookEntity.getUserId()));
			assertEquals("testCase20() zapierWebhookEntity.getSubType() is incorrect.", "1354725", zapierWebhookEntity.getSubType());
			assertEquals("testCase20() zapierWebhookEntity.getTriggerType() is incorrect.", "4", String.valueOf(zapierWebhookEntity.getTriggerType()));
			assertEquals("testCase20() zapierWebhookEntity.getCallbackUrl() is incorrect.", "https://hook.zapier.com/callback_url_1",
					String.valueOf(zapierWebhookEntity.getCallbackUrl()));
		}

		// step 2: delete subscription		
		requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_PAGE_TAG_CONTENT_ALERT + "?access_token=" + AccessTokenService.INTERNAL_KEY + "&id=" + zapierResourceResponse.getId();
		System.out.println("testCase20() requestUrl=" + requestUrl);
		zapierResourceResponse = politeCrawlWebServiceClientService.zapierDelete(requestUrl);
		assertNotNull("testCase20() zapierResourceResponse should not be null.", zapierResourceResponse);
		String json = new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class);
		System.out.println("testCase20() json=" + json);
		FormatUtils.getInstance().logMemoryUsage("testCase20() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase21() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_PAGE_TAG_CONTENT_ALERT;
		System.out.println("testCase21() requestUrl=" + requestUrl);
		ZapierPageTagContentAlert[] zapierPageTagContentAlertArray = politeCrawlWebServiceClientService.zapierPageTagContentAlertGet(requestUrl);
		assertNotNull("testCase21() zapierPageTagContentAlertArray should not be null.", zapierPageTagContentAlertArray);
		String json = new Gson().toJson(zapierPageTagContentAlertArray, ZapierPageTagContentAlert[].class);
		assertEquals("testCase21() json is incorrect", "[{\"error_code\":\"00002\",\"error_message\":\"Request parameter access_token is required.\"}]", json);
		FormatUtils.getInstance().logMemoryUsage("testCase21() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase22() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_PAGE_TAG_CONTENT_ALERT + "?access_token=testaccesstoken";
		System.out.println("testCase22() requestUrl=" + requestUrl);
		ZapierPageTagContentAlert[] zapierPageTagContentAlertArray = politeCrawlWebServiceClientService.zapierPageTagContentAlertGet(requestUrl);
		assertNotNull("testCase22() zapierPageTagContentAlertArray should not be null.", zapierPageTagContentAlertArray);
		String json = new Gson().toJson(zapierPageTagContentAlertArray, ZapierPageTagContentAlert[].class);
		assertEquals("testCase22() json is incorrect", "[{\"error_code\":\"00003\",\"error_message\":\"Request parameter access_token testaccesstoken is invalid.\"}]",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase22() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase23() throws Exception {
		long startTimestamp = System.currentTimeMillis();

		// retrieve sample page tag content alert messages
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_PAGE_TAG_CONTENT_ALERT + "?access_token=" + AccessTokenService.INTERNAL_KEY;
		System.out.println("testCase23() requestUrl=" + requestUrl);
		ZapierPageTagContentAlert[] zapierPageTagContentAlertArray = politeCrawlWebServiceClientService.zapierPageTagContentAlertGet(requestUrl);
		assertNotNull("testCase23() zapierPageTagContentAlertArray should not be null.", zapierPageTagContentAlertArray);

		// first alert
		assertEquals("testCase23() zapierPageTagContentAlertArray[0].getId() is incorrect.", "1614180689836", zapierPageTagContentAlertArray[0].getId());
		assertEquals("testCase23() zapierPageTagContentAlertArray[0].getAlert_timestamp() is incorrect.", "2021-03-12 12:26:13",
				zapierPageTagContentAlertArray[0].getAlert_timestamp());
		assertEquals("testCase23() zapierPageTagContentAlertArray[0].getDomain_id() is incorrect.", "123456",
				String.valueOf(zapierPageTagContentAlertArray[0].getDomain_id()));
		assertEquals("testCase23() zapierPageTagContentAlertArray[0].getDomain_name() is incorrect.", "www.testdomain1.com",
				zapierPageTagContentAlertArray[0].getDomain_name());
		assertEquals("testCase23() zapierPageTagContentAlertArray[0].getPage_tag_id() is incorrect.", "234567",
				String.valueOf(zapierPageTagContentAlertArray[0].getPage_tag_id()));
		assertEquals("testCase23() zapierPageTagContentAlertArray[0].getPage_tag_name() is incorrect.", "page tag name 1",
				String.valueOf(zapierPageTagContentAlertArray[0].getPage_tag_name()));
		assertEquals("testCase23() zapierPageTagContentAlertArray[0].getContent_change_date() is incorrect.", "2021-03-11",
				String.valueOf(zapierPageTagContentAlertArray[0].getContent_change_date()));
		assertEquals("testCase23() zapierPageTagContentAlertArray[0].getContent_change_threshold() is incorrect.", "1 character",
				String.valueOf(zapierPageTagContentAlertArray[0].getContent_change_threshold()));
		assertEquals("testCase23() zapierPageTagContentAlertArray[0].getPage_tag_level_change_threshold() is incorrect.", "2 percents",
				String.valueOf(zapierPageTagContentAlertArray[0].getPage_tag_level_change_threshold()));
		assertEquals("testCase23() zapierPageTagContentAlertArray[0].getTotal_urls_changed() is incorrect.", "8",
				String.valueOf(zapierPageTagContentAlertArray[0].getTotal_urls_changed()));
		assertEquals("testCase23() zapierPageTagContentAlertArray[0].getTotal_urls_in_tag() is incorrect.", "168",
				String.valueOf(zapierPageTagContentAlertArray[0].getTotal_urls_in_tag()));
		assertEquals("testCase23() zapierPageTagContentAlertArray[0].getPercent_of_urls_changed() is incorrect.", "4.76",
				String.valueOf(zapierPageTagContentAlertArray[0].getPercent_of_urls_changed()));
		assertEquals("testCase23() zapierPageTagContentAlertArray[0].getChanges() is incorrect.", "6 URLs with 'Title' changes, 5 URLs with 'Meta Desc' changes",
				String.valueOf(zapierPageTagContentAlertArray[0].getChanges()));
		assertEquals("testCase23() zapierPageTagContentAlertArray[0].getError_code() is incorrect.", "", zapierPageTagContentAlertArray[0].getError_code());
		assertEquals("testCase23() zapierPageTagContentAlertArray[0].getError_message() is incorrect.", "", zapierPageTagContentAlertArray[0].getError_message());

		// second alert
		assertEquals("testCase23() zapierPageTagContentAlertArray[1].getId() is incorrect.", "1614180689837", zapierPageTagContentAlertArray[1].getId());
		assertEquals("testCase23() zapierPageTagContentAlertArray[1].getAlert_timestamp() is incorrect.", "2021-03-13 23:28:08",
				zapierPageTagContentAlertArray[1].getAlert_timestamp());
		assertEquals("testCase23() zapierPageTagContentAlertArray[1].getDomain_id() is incorrect.", "345678",
				String.valueOf(zapierPageTagContentAlertArray[1].getDomain_id()));
		assertEquals("testCase23() zapierPageTagContentAlertArray[1].getDomain_name() is incorrect.", "www.testdomain2.com",
				zapierPageTagContentAlertArray[1].getDomain_name());
		assertEquals("testCase23() zapierPageTagContentAlertArray[1].getPage_tag_id() is incorrect.", "567890",
				String.valueOf(zapierPageTagContentAlertArray[1].getPage_tag_id()));
		assertEquals("testCase23() zapierPageTagContentAlertArray[1].getPage_tag_name() is incorrect.", "page tag name 2",
				String.valueOf(zapierPageTagContentAlertArray[1].getPage_tag_name()));
		assertEquals("testCase23() zapierPageTagContentAlertArray[1].getContent_change_date() is incorrect.", "2021-03-12",
				String.valueOf(zapierPageTagContentAlertArray[1].getContent_change_date()));
		assertEquals("testCase23() zapierPageTagContentAlertArray[1].getContent_change_threshold() is incorrect.", "3 characters",
				String.valueOf(zapierPageTagContentAlertArray[1].getContent_change_threshold()));
		assertEquals("testCase23() zapierPageTagContentAlertArray[1].getPage_tag_level_change_threshold() is incorrect.", "18 percents",
				String.valueOf(zapierPageTagContentAlertArray[1].getPage_tag_level_change_threshold()));
		assertEquals("testCase23() zapierPageTagContentAlertArray[1].getTotal_urls_changed() is incorrect.", "168",
				String.valueOf(zapierPageTagContentAlertArray[1].getTotal_urls_changed()));
		assertEquals("testCase23() zapierPageTagContentAlertArray[1].getTotal_urls_in_tag() is incorrect.", "368",
				String.valueOf(zapierPageTagContentAlertArray[1].getTotal_urls_in_tag()));
		assertEquals("testCase23() zapierPageTagContentAlertArray[1].getPercent_of_urls_changed() is incorrect.", "45.65",
				String.valueOf(zapierPageTagContentAlertArray[1].getPercent_of_urls_changed()));
		assertEquals("testCase23() zapierPageTagContentAlertArray[1].getChanges() is incorrect.",
				"160 URLs with '404 to 200' changes, 18 URLs with 'Canonical' changes", String.valueOf(zapierPageTagContentAlertArray[1].getChanges()));
		assertEquals("testCase23() zapierPageTagContentAlertArray[1].getError_code() is incorrect.", "", zapierPageTagContentAlertArray[1].getError_code());
		assertEquals("testCase23() zapierPageTagContentAlertArray[1].getError_message() is incorrect.", "", zapierPageTagContentAlertArray[1].getError_message());

		FormatUtils.getInstance().logMemoryUsage("testCase23() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase24() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_PAGE_TAG_CONTENT_ALERT;
		System.out.println("testCase24() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierResourceRequest = new ZapierResourceRequest();
		zapierResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		zapierResourceRequest.setDomain_id(1295);
		zapierResourceRequest.setUser_email("<EMAIL>");
		zapierResourceRequest.setCallback_url("https://hook.zapier.com/callback_url_1");
		String requestParameters = new Gson().toJson(zapierResourceRequest, ZapierResourceRequest.class);

		ZapierResourceResponse zapierResourceResponse = politeCrawlWebServiceClientService.zapierPost(requestUrl, requestParameters);
		assertNotNull("testCase24() zapierResourceResponse should not be null.", zapierResourceResponse);
		String json = new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class);
		assertEquals("testCase24() json is incorrect", "{\"error_code\":\"00018\",\"error_message\":\"Request parameter page_tag_name is required.\"}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase24() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase25() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_ZAPIER
				+ IConstants.COMMAND_PAGE_TAG_CONTENT_ALERT;
		System.out.println("testCase25() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierResourceRequest = new ZapierResourceRequest();
		zapierResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		zapierResourceRequest.setDomain_id(1295);
		zapierResourceRequest.setUser_email("<EMAIL>");
		zapierResourceRequest.setCallback_url("https://hook.zapier.com/callback_url_1");
		zapierResourceRequest.setPage_tag_name("invalid group name");
		String requestParameters = new Gson().toJson(zapierResourceRequest, ZapierResourceRequest.class);

		ZapierResourceResponse zapierResourceResponse = politeCrawlWebServiceClientService.zapierPost(requestUrl, requestParameters);
		assertNotNull("testCase25() zapierResourceResponse should not be null.", zapierResourceResponse);
		String json = new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class);
		assertEquals("testCase25() json is incorrect",
				"{\"error_code\":\"00019\",\"error_message\":\"Request parameter domain_id page_tag_name (1295 invalid group name) is not configured to receive page tag content alert.\"}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase25() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase26() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_ZAPIER + "test";
		System.out.println("testCase26() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierResourceRequest = new ZapierResourceRequest();
		zapierResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		zapierResourceRequest.setDomain_id(1295);
		zapierResourceRequest.setUser_email("<EMAIL>");
		zapierResourceRequest.setCallback_url("https://hook.zapier.com/callback_url_1");
		zapierResourceRequest.setPage_tag_name("invalid group name");
		String requestParameters = new Gson().toJson(zapierResourceRequest, ZapierResourceRequest.class);

		ZapierResourceResponse zapierResourceResponse = politeCrawlWebServiceClientService.zapierPost(requestUrl, requestParameters);
		assertNotNull("testCase26() zapierResourceResponse should not be null.", zapierResourceResponse);
		String json = new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class);
		assertEquals("testCase26() json is incorrect", "{\"error_code\":\"00017\",\"error_message\":\"Request command test is invalid.\"}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase26() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase27() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_ZAPIER + "test"
				+ "?access_token=" + AccessTokenService.INTERNAL_KEY + "&id=168168";
		System.out.println("testCase27() requestUrl=" + requestUrl);
		ZapierResourceResponse zapierResourceResponse = politeCrawlWebServiceClientService.zapierDelete(requestUrl);
		assertNotNull("testCase27() zapierResourceResponse should not be null.", zapierResourceResponse);
		String json = new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class);
		assertEquals("testCase27() json is incorrect", "{\"error_code\":\"00017\",\"error_message\":\"Request command test is invalid.\"}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase27() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase28() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_ZAPIER + "test"
				+ "?access_token=a10bef62-aed0-b459-6586-3eb31687af79";
		System.out.println("testCase28() requestUrl=" + requestUrl);
		ZapierPageTagContentAlert[] zapierPageTagContentAlertArray = politeCrawlWebServiceClientService.zapierPageTagContentAlertGet(requestUrl);
		assertNotNull("testCase28() zapierPageTagContentAlertArray should not be null.", zapierPageTagContentAlertArray);
		String json = new Gson().toJson(zapierPageTagContentAlertArray, ZapierPageTagContentAlert[].class);
		assertEquals("testCase28() json is incorrect", "[{\"error_code\":\"00017\",\"error_message\":\"Request command test is invalid.\"}]", json);
		FormatUtils.getInstance().logMemoryUsage("testCase28() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

}