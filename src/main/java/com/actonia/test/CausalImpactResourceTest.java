package com.actonia.test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;

import com.actonia.IConstants;
import com.actonia.service.AccessTokenService;
import com.actonia.service.PoliteCrawlWebServiceClientService;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.CausalImpactRequest;
import com.actonia.value.object.CausalImpactResponse;
import com.google.gson.Gson;

public class CausalImpactResourceTest {

	private PoliteCrawlWebServiceClientService politeCrawlWebServiceClientService;

	public CausalImpactResourceTest() {
		super();
		this.politeCrawlWebServiceClientService = SpringBeanFactory.getBean("politeCrawlWebServiceClientService");
	}

	public static void main(String[] args) throws Exception {
		new CausalImpactResourceTest().runTests();
	}

	private void runTests() throws Exception {
		testCase1();
		testCase2();
		testCase3();
		testCase4();
		testCase5();
		testCase6();
		testCase7();
		testCase8();
		testCase9();
		testCase10();
		testCase11();
		testCase12();
		testCase13();
		testCase14();
		testCase15();
		testCase16();
		testCase17();
		testCase18();
		testCase19();
		testCase20();
		testCase21();
		testCase22();
		testCase23();
		testCase24();
		testCase25();
		testCase26();
		testCase27();
		testCase28();
		testCase29();
		testCase30();
		testCase31();
		testCase32();
	}

	private double[] getDoubleArrayForY() {
		return new double[] { 105.294986741563, 105.894309669798, 106.620875838828, 106.157206715611, 101.281175367611, 101.448430404394, 100.973531295986,
				102.544706623256, 102.196095062485, 106.813823912820, 103.097143547068, 101.582386666749, 101.673556412411, 101.841354437786, 102.237455737321,
				100.784815884160, 100.799475990316, 101.469293747955, 103.323753845151, 102.501752563532, 105.202970606505, 109.529621758391, 106.281388964034,
				107.390781247210, 106.034848798153, 106.484644636029, 104.828440129525, 102.986789724487, 104.130447420883, 107.589625606610, 104.126018233013,
				106.157168423783, 105.719404485374, 101.833157375086, 102.205645807644, 101.952496215371, 100.201140097063, 99.968448170460, 99.885528892698,
				100.180869994240, 100.268671427130, 99.638394336843, 103.404272138826, 104.747438903684, 103.498399511826, 100.196157136733, 98.088133412256,
				100.261950572215, 100.180117923862, 103.356375745830, 101.015791636131, 102.518960350355, 100.687986526053, 102.553632658390, 104.047084239932,
				103.378053360498, 107.071924590776, 104.469947322470, 106.301462055551, 109.496829525290, 108.485277812686, 111.014291469492, 112.867343694573,
				111.538727869305, 112.284408699329, 109.686179697106, 109.521634601159, 106.002957468914, 106.543084370612, 108.555071439643, 117.294312743074,
				116.495691770188, 118.253265267922, 117.975995109054, 120.639716329814, 117.622604506845, 116.215262174588, 117.143538695230, 116.553033903326,
				115.102037228661, 117.017486067743, 117.978480593359, 118.860957062418, 117.747853794938, 118.690190569880, 117.019148380786, 118.602027351201,
				118.129658809177, 121.840240231535, 117.284462260685, 116.016020338404, 118.965519657894, 116.731018002460, 118.203885420013, 115.214840888210,
				114.942928099231, 116.669401819417, 112.580096233226, 111.339439225794, 114.326392437154, };
	}

	private double[] getDoubleArrayForX1() {
		return new double[] { 105.836141287184, 106.148510516934, 105.445452924548, 104.174624934334, 101.675598883830, 101.656041522905, 101.372338529136,
				102.008552534637, 101.377638838788, 103.255779683329, 103.910403745630, 104.135368839852, 102.943195123412, 102.536488797069, 100.958200053214,
				100.353419093033, 100.689176347747, 102.231157386632, 102.048638166882, 103.739780612894, 105.659565709992, 106.844259292871, 106.915370650487,
				108.074732360516, 106.164712254082, 106.340999773123, 105.571828893883, 103.916211782304, 104.895122501999, 105.204349326708, 104.419813122256,
				104.413818240674, 103.435890330798, 101.278563777492, 102.151483900528, 101.763057302727, 100.162249070190, 99.269532787228, 99.995174846545,
				99.688699369268, 101.554391330291, 101.391699619787, 103.155366586517, 103.647997154701, 102.804078261380, 100.948799447533, 100.363500936749,
				101.747782931054, 102.008131140481, 101.783745089183, 100.507386914872, 102.651660431658, 101.857792871126, 102.898020774525, 103.847919878205,
				104.265810052247, 105.840626285985, 105.114701077161, 106.748061127334, 108.184444971677, 108.533147494931, 112.010924653116, 112.135961643253,
				111.790476570709, 112.119071373401, 110.389096157736, 109.012492053947, 107.959737625246, 108.131449479650, 106.942126862978, 106.418696537089,
				106.409460393465, 107.727921968003, 108.022532200572, 108.857643844320, 107.437434828973, 106.255361448164, 105.812997659729, 105.305540406008,
				105.039422413256, 106.121031760117, 107.868870765596, 108.205670254235, 108.168855253221, 108.141218947449, 107.384712422050, 108.487507045951,
				108.309874349626, 109.237501913860, 106.924731961598, 106.204478423473, 107.520928445624, 106.577055429915, 107.721529353336, 105.711658563560,
				104.790707088902, 104.221192239512, 102.463902225578, 102.721718277757, 102.085333680785, };
	}

	private double[] getDoubleArrayForX2() {
		return new double[] { 105.836141287184, 104.148510516934, 107.445452924548, 106.174624934334, 102.675598883830, 101.656041522905, 101.372338529136,
				101.008552534637, 99.377638838788, 104.255779683329, 105.910403745630, 106.135368839852, 100.943195123412, 104.536488797069, 99.958200053214,
				101.353419093033, 98.689176347747, 100.231157386632, 102.048638166882, 101.739780612894, 103.659565709992, 105.844259292871, 104.915370650487,
				108.074732360516, 105.164712254082, 107.340999773123, 106.571828893883, 101.916211782304, 102.895122501999, 103.204349326708, 104.419813122256,
				106.413818240674, 105.435890330798, 102.278563777492, 101.151483900528, 101.763057302727, 101.162249070190, 100.269532787228, 99.995174846545,
				99.688699369268, 100.554391330291, 101.391699619787, 104.155366586517, 102.647997154701, 103.804078261380, 100.948799447533, 98.363500936749,
				100.747782931054, 101.008131140481, 101.783745089183, 100.507386914872, 102.651660431658, 103.857792871126, 100.898020774525, 105.847919878205,
				106.265810052247, 107.840626285985, 107.114701077161, 104.748061127334, 108.184444971677, 109.533147494931, 110.010924653116, 112.135961643253,
				109.790476570709, 114.119071373401, 112.389096157736, 111.012492053947, 109.959737625246, 106.131449479650, 104.942126862978, 104.418696537089,
				108.409460393465, 108.727921968003, 109.022532200572, 110.857643844320, 105.437434828973, 107.255361448164, 105.812997659729, 107.305540406008,
				106.039422413256, 108.121031760117, 107.868870765596, 110.205670254235, 109.168855253221, 107.141218947449, 109.384712422050, 110.487507045951,
				107.309874349626, 110.237501913860, 105.924731961598, 108.204478423473, 107.520928445624, 104.577055429915, 108.721529353336, 106.711658563560,
				104.790707088902, 103.221192239512, 101.463902225578, 100.721718277757, 101.085333680785, };
	}

	private double[] getTestCase26DoubleArrayForY() {
		return new double[] { 2, 4, 6, 1, 1, 1, 5, 3, 3, 3, 1, 0, 1, 0, 3, 3, 2, 3, 0, 1, 3, 2, 1, 2, 2, 1, 0, 5, 2, 5, 2, 2, 0, 0, 0, 4, 2, 3, 4, 1, 2, 7, 10, 3, 1, 5,
				0, 2, 5, 6, 9, 4, 4, 1, 2, 2, 4, 3, 6, 4, 1 };
	}

	private double[] getTestCase26DoubleArrayForX1() {
		return new double[] { 21, 18, 16, 18, 12, 7, 18, 22, 24, 21, 11, 6, 12, 0, 20, 18, 16, 14, 9, 5, 12, 12, 13, 15, 13, 6, 13, 20, 16, 21, 19, 11, 9, 5, 23, 20,
				12, 19, 13, 12, 6, 17, 23, 19, 10, 16, 7, 8, 16, 17, 16, 14, 10, 2, 5, 14, 9, 14, 19, 10, 2 };
	}

	private double[] getTestCase26DoubleArrayForX2() {
		return new double[] { 10, 10, 15, 14, 7, 5, 8, 11, 14, 10, 11, 4, 8, 0, 14, 14, 13, 8, 4, 6, 9, 8, 11, 8, 7, 2, 3, 10, 9, 17, 15, 6, 2, 6, 12, 15, 9, 14, 13, 2,
				5, 15, 10, 9, 7, 7, 3, 6, 12, 6, 10, 9, 10, 2, 3, 8, 11, 14, 8, 7, 4 };
	}

	private double[] getTestCase29DoubleArrayForY() {
		return new double[] { 3.0, 0.0, 8.0, 8.0, 3.0, 4.0, 3.0, 2.0, 1.0, 2.0, 5.0, 5.0, 1.0, 3.0, 1.0, 1.0, 2.0, 6.0, 5.0, 5.0, 1.0, 0.0, 0.0, 0.0, 3.0, 3.0, 6.0,
				4.0, 0.0, 1.0, 3.0, 3.0, 2.0, 1.0, 2.0, 2.0, 0.0, 8.0, 6.0, 3.0, 7.0, 8.0, 3.0, 2.0, 10.0, 2.0, 5.0, 4.0, 2.0, 2.0, 3.0, 1.0, 5.0, 3.0, 4.0, 0.0, 2.0,
				2.0, 2.0, 3.0 };
	}

	private double[] getTestCase29DoubleArrayForX1() {
		return new double[] { 1.0, 0.0, 1.0, 1.0, 0.0, 1.0, 1.0, 0.0, 0.0, 1.0, 0.0, 1.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 1.0, 2.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0,
				0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 0.0, 2.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 3.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0,
				0.0, 0.0, 0.0 };
	}

	private double[] getTestCase29DoubleArrayForX2() {
		return new double[] { 5.0, 0.0, 10.0, 4.0, 2.0, 8.0, 6.0, 4.0, 3.0, 3.0, 8.0, 5.0, 3.0, 6.0, 3.0, 4.0, 9.0, 7.0, 9.0, 7.0, 5.0, 2.0, 3.0, 11.0, 4.0, 8.0, 6.0,
				4.0, 2.0, 2.0, 2.0, 6.0, 7.0, 6.0, 11.0, 1.0, 2.0, 8.0, 8.0, 7.0, 3.0, 5.0, 4.0, 7.0, 13.0, 5.0, 3.0, 7.0, 2.0, 8.0, 3.0, 3.0, 7.0, 6.0, 2.0, 6.0, 3.0,
				3.0, 6.0, 10.0 };
	}

	private double[] getTestCase29DoubleArrayForX3() {
		return new double[] { 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
				0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
				0.0, 0.0, 0.0 };
	}

	private double[] getTestCase30DoubleArrayForY() {
		return new double[] { 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
				0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
				0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
				0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 };
	}

	private double[] getTestCase30DoubleArrayForX1() {
		return new double[] { 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
				0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
				0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
				0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 };
	}

	private double[] getTestCase30DoubleArrayForX2() {
		return new double[] { 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
				0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
				0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
				0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 };
	}

	private double[] getTestCase31DoubleArrayForY() {
		return new double[] { 17118.0, 17264.0, 17002.0, 16314.0, 15321.0, 13441.0, 14138.0, 17527.0, 18012.0, 16032.0, 15693.0, 14832.0, 13580.0, 13659.0, 14578.0,
				16650.0, 16833.0, 16555.0, 15698.0, 13197.0, 14113.0, 17148.0, 16862.0, 17273.0, 16169.0, 15764.0, 13816.0, 14115.0, 17102.0, 16552.0, 16341.0, 15657.0,
				14805.0, 13182.0, 13011.0, 16281.0, 16259.0, 15990.0, 15082.0, 14168.0, 12986.0, 13367.0, 16036.0, 16040.0, 15586.0, 15102.0, 15247.0, 13691.0, 13138.0,
				14950.0, 17546.0, 17728.0, 17611.0, 17555.0, 15225.0, 16008.0, 19229.0, 18624.0, 18153.0, 17515.0, 16736.0, 15386.0, 15923.0, 18793.0, 19365.0, 18122.0,
				17884.0, 17048.0, 15491.0, 16169.0, 18986.0, 18224.0, 17277.0, 17259.0, 16059.0, 13848.0, 14738.0, 16935.0, 16964.0, 17278.0, 17663.0, 17057.0, 15397.0,
				16094.0, 18975.0, 18355.0, 18217.0, 17763.0, 16866.0, 15623.0, 16075.0, 18754.0, 19124.0, 19202.0, 18772.0, 18125.0, 16306.0, 17325.0, 20519.0, 20009.0,
				19993.0, 17830.0, 14251.0, 12715.0, 13261.0, 15777.0, 15025.0, 14890.0, 14398.0, 13650.0, 12384.0, 12854.0, 14085.0, 15658.0, 15713.0, 15642.0, 14006.0,
				12871.0, 13576.0, 15898.0, 15675.0, 15100.0, 15031.0, 14417.0, 12817.0, 13217.0, 16147.0, 15887.0, 15205.0, 14273.0, 12875.0, 11326.0, 11756.0, 14074.0,
				13987.0, 14278.0, 13913.0, 12929.0, 11977.0, 12404.0, 15575.0, 15065.0, 14864.0, 14452.0, 13966.0, 12238.0, 12622.0, 14383.0, 14155.0, 13700.0, 12972.0,
				12460.0, 10969.0, 11326.0, 12933.0, 12821.0, 12183.0, 12347.0, 11868.0, 10592.0, 11258.0, 13183.0, 13023.0, 12592.0, 12469.0, 12041.0, 10618.0, 10428.0,
				12366.0, 12386.0, 11841.0, 11735.0, 11235.0, 10139.0, 10445.0, 11998.0, 12358.0, 12028.0, 11401.0, 11649.0, 10630.0, 11013.0, 12556.0, 12324.0, 12385.0,
				11875.0, 10889.0, 9952.0, 10522.0, 12313.0, 11805.0, 10420.0, 8342.0, 8874.0, 9893.0, 10340.0, 12110.0, 11911.0, 11201.0, 11063.0, 10820.0, 9733.0,
				10106.0, 11939.0, 12223.0, 11772.0, 11856.0, 11381.0, 10642.0, 10965.0, 12887.0, 12582.0, 11741.0, 11499.0, 11109.0, 10325.0, 9546.0, 11218.0, 10841.0,
				10487.0, 9501.0, 7898.0, 6365.0, 8082.0, 10062.0, 10413.0, 9929.0, 9635.0, 8992.0, 8032.0, 9191.0, 10095.0, 9900.0, 10166.0, 9695.0, 9832.0, 8972.0,
				9455.0, 10462.0, 10005.0, 10014.0, 9869.0, 9588.0, 8801.0, 9069.0, 10293.0, 10490.0, 9765.0, 10149.0, 9446.0, 8524.0, 8795.0, 9981.0, 9654.0, 9571.0,
				8988.0, 8491.0, 8269.0, 8481.0, 9626.0, 8594.0, 8433.0, 8199.0, 9203.0, 8392.0, 8465.0, 10458.0, 10358.0, 9287.0, 8609.0, 8460.0, 7852.0, 8151.0,
				8259.0, 9150.0, 8723.0 };
	}

	private void testCase1() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CAUSAL_IMPACT
				+ IConstants.COMMAND_CALCULATE_CORRELATION_COEFFICIENT;
		System.out.println("testCase1() requestUrl=" + requestUrl);
		String requestParameters = IConstants.EMPTY_STRING;
		CausalImpactResponse causalImpactResponse = politeCrawlWebServiceClientService.causalImpact(requestUrl, requestParameters);
		assertNotNull("testCase1() causalImpactResponse should not be null.", causalImpactResponse);
		String json = new Gson().toJson(causalImpactResponse, CausalImpactResponse.class);
		assertNotNull("testCase1() causalImpactResponse.getError() should not be null.", causalImpactResponse.getError());
		assertEquals("testCase1() json is incorrect", "{\"success\":false,\"error\":{\"error_code\":\"00001\",\"error_message\":\"Request JSON is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase1() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase2() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CAUSAL_IMPACT
				+ IConstants.COMMAND_CALCULATE_CORRELATION_COEFFICIENT;
		System.out.println("testCase2() requestUrl=" + requestUrl);
		CausalImpactRequest causalImpactRequest = new CausalImpactRequest();
		String requestParameters = new Gson().toJson(causalImpactRequest, CausalImpactRequest.class);
		CausalImpactResponse causalImpactResponse = politeCrawlWebServiceClientService.causalImpact(requestUrl, requestParameters);
		assertNotNull("testCase2() causalImpactResponse should not be null.", causalImpactResponse);
		String json = new Gson().toJson(causalImpactResponse, CausalImpactResponse.class);
		assertNotNull("testCase2() causalImpactResponse.getError() should not be null.", causalImpactResponse.getError());
		assertEquals("testCase2() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00005\",\"error_message\":\"Request parameter access_token is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase2() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase3() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CAUSAL_IMPACT
				+ IConstants.COMMAND_CALCULATE_CORRELATION_COEFFICIENT;
		System.out.println("testCase3() requestUrl=" + requestUrl);
		CausalImpactRequest causalImpactRequest = new CausalImpactRequest();
		causalImpactRequest.setAccess_token("testaccesstoken");
		String requestParameters = new Gson().toJson(causalImpactRequest, CausalImpactRequest.class);
		CausalImpactResponse causalImpactResponse = politeCrawlWebServiceClientService.causalImpact(requestUrl, requestParameters);
		assertNotNull("testCase3() causalImpactResponse should not be null.", causalImpactResponse);
		String json = new Gson().toJson(causalImpactResponse, CausalImpactResponse.class);
		assertNotNull("testCase3() causalImpactResponse.getError() should not be null.", causalImpactResponse.getError());
		assertEquals("testCase3() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00002\",\"error_message\":\"Request parameter access_token testaccesstoken is invalid.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase3() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase4() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CAUSAL_IMPACT + "test";
		System.out.println("testCase4() requestUrl=" + requestUrl);
		CausalImpactRequest causalImpactRequest = new CausalImpactRequest();
		causalImpactRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		String requestParameters = new Gson().toJson(causalImpactRequest, CausalImpactRequest.class);
		CausalImpactResponse causalImpactResponse = politeCrawlWebServiceClientService.causalImpact(requestUrl, requestParameters);
		assertNotNull("testCase4() causalImpactResponse should not be null.", causalImpactResponse);
		String json = new Gson().toJson(causalImpactResponse, CausalImpactResponse.class);
		assertNotNull("testCase4() causalImpactResponse.getError() should not be null.", causalImpactResponse.getError());
		assertEquals("testCase4() json is incorrect", "{\"success\":false,\"error\":{\"error_code\":\"00004\",\"error_message\":\"Request command test is invalid.\"}}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase4() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase5() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CAUSAL_IMPACT
				+ IConstants.COMMAND_CALCULATE_CORRELATION_COEFFICIENT;
		System.out.println("testCase5() requestUrl=" + requestUrl);
		CausalImpactRequest causalImpactRequest = new CausalImpactRequest();
		causalImpactRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		String requestParameters = new Gson().toJson(causalImpactRequest, CausalImpactRequest.class);
		CausalImpactResponse causalImpactResponse = politeCrawlWebServiceClientService.causalImpact(requestUrl, requestParameters);
		assertNotNull("testCase5() causalImpactResponse should not be null.", causalImpactResponse);
		String json = new Gson().toJson(causalImpactResponse, CausalImpactResponse.class);
		assertNotNull("testCase5() causalImpactResponse.getError() should not be null.", causalImpactResponse.getError());
		assertEquals("testCase5() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00006\",\"error_message\":\"Request parameter test_time_series is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase5() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase6() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CAUSAL_IMPACT
				+ IConstants.COMMAND_CALCULATE_CORRELATION_COEFFICIENT;
		System.out.println("testCase6() requestUrl=" + requestUrl);
		CausalImpactRequest causalImpactRequest = new CausalImpactRequest();
		causalImpactRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		causalImpactRequest.setTest_time_series(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForY()));
		String requestParameters = new Gson().toJson(causalImpactRequest, CausalImpactRequest.class);
		CausalImpactResponse causalImpactResponse = politeCrawlWebServiceClientService.causalImpact(requestUrl, requestParameters);
		assertNotNull("testCase6() causalImpactResponse should not be null.", causalImpactResponse);
		String json = new Gson().toJson(causalImpactResponse, CausalImpactResponse.class);
		assertNotNull("testCase6() causalImpactResponse.getError() should not be null.", causalImpactResponse.getError());
		assertEquals("testCase6() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00007\",\"error_message\":\"Request parameter control_name_list is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase6() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase7() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CAUSAL_IMPACT
				+ IConstants.COMMAND_CALCULATE_CORRELATION_COEFFICIENT;
		System.out.println("testCase7() requestUrl=" + requestUrl);
		CausalImpactRequest causalImpactRequest = new CausalImpactRequest();
		causalImpactRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		causalImpactRequest.setTest_time_series(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForY()));

		List<String> controlNameList = new ArrayList<String>();
		controlNameList.add("control1");
		controlNameList.add("");
		causalImpactRequest.setControl_name_list(controlNameList);

		String requestParameters = new Gson().toJson(causalImpactRequest, CausalImpactRequest.class);
		CausalImpactResponse causalImpactResponse = politeCrawlWebServiceClientService.causalImpact(requestUrl, requestParameters);
		assertNotNull("testCase7() causalImpactResponse should not be null.", causalImpactResponse);
		String json = new Gson().toJson(causalImpactResponse, CausalImpactResponse.class);
		assertNotNull("testCase7() causalImpactResponse.getError() should not be null.", causalImpactResponse.getError());
		assertEquals("testCase7() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00021\",\"error_message\":\"Request parameter control name(s) cannot be blank.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase7() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase8() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CAUSAL_IMPACT
				+ IConstants.COMMAND_CALCULATE_CORRELATION_COEFFICIENT;
		System.out.println("testCase8() requestUrl=" + requestUrl);
		CausalImpactRequest causalImpactRequest = new CausalImpactRequest();
		causalImpactRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		causalImpactRequest.setTest_time_series(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForY()));

		List<String> controlNameList = new ArrayList<String>();
		controlNameList.add("control1");
		controlNameList.add("control1");
		controlNameList.add("control2");
		causalImpactRequest.setControl_name_list(controlNameList);

		String requestParameters = new Gson().toJson(causalImpactRequest, CausalImpactRequest.class);
		CausalImpactResponse causalImpactResponse = politeCrawlWebServiceClientService.causalImpact(requestUrl, requestParameters);
		assertNotNull("testCase8() causalImpactResponse should not be null.", causalImpactResponse);
		String json = new Gson().toJson(causalImpactResponse, CausalImpactResponse.class);
		assertNotNull("testCase8() causalImpactResponse.getError() should not be null.", causalImpactResponse.getError());
		assertEquals("testCase8() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00022\",\"error_message\":\"Request parameter control name(s) must be unique.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase8() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase9() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CAUSAL_IMPACT
				+ IConstants.COMMAND_CALCULATE_CORRELATION_COEFFICIENT;
		System.out.println("testCase9() requestUrl=" + requestUrl);
		CausalImpactRequest causalImpactRequest = new CausalImpactRequest();
		causalImpactRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		causalImpactRequest.setTest_time_series(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForY()));

		List<String> controlNameList = new ArrayList<String>();
		controlNameList.add("control1");
		controlNameList.add("control2");
		causalImpactRequest.setControl_name_list(controlNameList);

		String requestParameters = new Gson().toJson(causalImpactRequest, CausalImpactRequest.class);
		CausalImpactResponse causalImpactResponse = politeCrawlWebServiceClientService.causalImpact(requestUrl, requestParameters);
		assertNotNull("testCase9() causalImpactResponse should not be null.", causalImpactResponse);
		String json = new Gson().toJson(causalImpactResponse, CausalImpactResponse.class);
		assertNotNull("testCase9() causalImpactResponse.getError() should not be null.", causalImpactResponse.getError());
		assertEquals("testCase9() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00008\",\"error_message\":\"Request parameter control_time_series_list is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase9() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase10() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CAUSAL_IMPACT
				+ IConstants.COMMAND_CALCULATE_CORRELATION_COEFFICIENT;
		System.out.println("testCase10() requestUrl=" + requestUrl);
		CausalImpactRequest causalImpactRequest = new CausalImpactRequest();
		causalImpactRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		causalImpactRequest.setTest_time_series(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForY()));

		List<String> controlNameList = new ArrayList<String>();
		controlNameList.add("control1");
		controlNameList.add("control2");
		causalImpactRequest.setControl_name_list(controlNameList);

		List<double[]> controlDataPointsList = new ArrayList<double[]>();
		controlDataPointsList.add(FormatUtils.getInstance().extractDoubleArray(0, 1, getDoubleArrayForX1()));
		controlDataPointsList.add(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForX2()));
		causalImpactRequest.setControl_time_series_list(controlDataPointsList);

		String requestParameters = new Gson().toJson(causalImpactRequest, CausalImpactRequest.class);
		CausalImpactResponse causalImpactResponse = politeCrawlWebServiceClientService.causalImpact(requestUrl, requestParameters);
		assertNotNull("testCase10() causalImpactResponse should not be null.", causalImpactResponse);
		String json = new Gson().toJson(causalImpactResponse, CausalImpactResponse.class);
		assertNotNull("testCase10() causalImpactResponse.getError() should not be null.", causalImpactResponse.getError());
		assertEquals("testCase10() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00009\",\"error_message\":\"Total number of data points inconsistent among time series.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase10() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase11() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CAUSAL_IMPACT
				+ IConstants.COMMAND_CALCULATE_CORRELATION_COEFFICIENT;
		System.out.println("testCase11() requestUrl=" + requestUrl);
		CausalImpactRequest causalImpactRequest = new CausalImpactRequest();
		causalImpactRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		causalImpactRequest.setTest_time_series(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForY()));

		List<String> controlNameList = new ArrayList<String>();
		controlNameList.add("control1");
		controlNameList.add("control2");
		controlNameList.add("control3");
		causalImpactRequest.setControl_name_list(controlNameList);

		List<double[]> controlDataPointsList = new ArrayList<double[]>();
		controlDataPointsList.add(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForX1()));
		controlDataPointsList.add(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForX2()));
		causalImpactRequest.setControl_time_series_list(controlDataPointsList);

		String requestParameters = new Gson().toJson(causalImpactRequest, CausalImpactRequest.class);
		CausalImpactResponse causalImpactResponse = politeCrawlWebServiceClientService.causalImpact(requestUrl, requestParameters);
		assertNotNull("testCase11() causalImpactResponse should not be null.", causalImpactResponse);
		String json = new Gson().toJson(causalImpactResponse, CausalImpactResponse.class);
		assertNotNull("testCase11() causalImpactResponse.getError() should not be null.", causalImpactResponse.getError());
		assertEquals("testCase11() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00010\",\"error_message\":\"Total number of control names must equal total number of control time series.\"}}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase11() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase12() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CAUSAL_IMPACT
				+ IConstants.COMMAND_ANALYZE;
		System.out.println("testCase12() requestUrl=" + requestUrl);
		CausalImpactRequest causalImpactRequest = new CausalImpactRequest();
		causalImpactRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		causalImpactRequest.setTest_time_series(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForY()));

		List<String> controlNameList = new ArrayList<String>();
		controlNameList.add("control1");
		controlNameList.add("control2");
		causalImpactRequest.setControl_name_list(controlNameList);

		List<double[]> controlDataPointsList = new ArrayList<double[]>();
		controlDataPointsList.add(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForX1()));
		controlDataPointsList.add(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForX2()));
		causalImpactRequest.setControl_time_series_list(controlDataPointsList);

		String requestParameters = new Gson().toJson(causalImpactRequest, CausalImpactRequest.class);
		CausalImpactResponse causalImpactResponse = politeCrawlWebServiceClientService.causalImpact(requestUrl, requestParameters);
		assertNotNull("testCase12() causalImpactResponse should not be null.", causalImpactResponse);
		String json = new Gson().toJson(causalImpactResponse, CausalImpactResponse.class);
		assertNotNull("testCase12() causalImpactResponse.getError() should not be null.", causalImpactResponse.getError());
		assertEquals("testCase12() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00011\",\"error_message\":\"Request parameter pre_period_start_date is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase12() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase13() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CAUSAL_IMPACT
				+ IConstants.COMMAND_ANALYZE;
		System.out.println("testCase13() requestUrl=" + requestUrl);
		CausalImpactRequest causalImpactRequest = new CausalImpactRequest();
		causalImpactRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		causalImpactRequest.setPre_period_start_date("pre_period_start_date");

		causalImpactRequest.setTest_time_series(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForY()));

		List<String> controlNameList = new ArrayList<String>();
		controlNameList.add("control1");
		controlNameList.add("control2");
		causalImpactRequest.setControl_name_list(controlNameList);

		List<double[]> controlDataPointsList = new ArrayList<double[]>();
		controlDataPointsList.add(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForX1()));
		controlDataPointsList.add(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForX2()));
		causalImpactRequest.setControl_time_series_list(controlDataPointsList);

		String requestParameters = new Gson().toJson(causalImpactRequest, CausalImpactRequest.class);
		CausalImpactResponse causalImpactResponse = politeCrawlWebServiceClientService.causalImpact(requestUrl, requestParameters);
		assertNotNull("testCase13() causalImpactResponse should not be null.", causalImpactResponse);
		String json = new Gson().toJson(causalImpactResponse, CausalImpactResponse.class);
		assertNotNull("testCase13() causalImpactResponse.getError() should not be null.", causalImpactResponse.getError());
		assertEquals("testCase13() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00012\",\"error_message\":\"Request parameter pre_period_end_date is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase13() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase14() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CAUSAL_IMPACT
				+ IConstants.COMMAND_ANALYZE;
		System.out.println("testCase14() requestUrl=" + requestUrl);
		CausalImpactRequest causalImpactRequest = new CausalImpactRequest();
		causalImpactRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		causalImpactRequest.setPre_period_start_date("pre_period_start_date");
		causalImpactRequest.setPre_period_end_date("pre_period_end_date");

		causalImpactRequest.setTest_time_series(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForY()));

		List<String> controlNameList = new ArrayList<String>();
		controlNameList.add("control1");
		controlNameList.add("control2");
		causalImpactRequest.setControl_name_list(controlNameList);

		List<double[]> controlDataPointsList = new ArrayList<double[]>();
		controlDataPointsList.add(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForX1()));
		controlDataPointsList.add(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForX2()));
		causalImpactRequest.setControl_time_series_list(controlDataPointsList);

		String requestParameters = new Gson().toJson(causalImpactRequest, CausalImpactRequest.class);
		CausalImpactResponse causalImpactResponse = politeCrawlWebServiceClientService.causalImpact(requestUrl, requestParameters);
		assertNotNull("testCase14() causalImpactResponse should not be null.", causalImpactResponse);
		String json = new Gson().toJson(causalImpactResponse, CausalImpactResponse.class);
		assertNotNull("testCase14() causalImpactResponse.getError() should not be null.", causalImpactResponse.getError());
		assertEquals("testCase14() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00013\",\"error_message\":\"Request parameter post_period_start_date is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase14() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase15() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CAUSAL_IMPACT
				+ IConstants.COMMAND_ANALYZE;
		System.out.println("testCase15() requestUrl=" + requestUrl);
		CausalImpactRequest causalImpactRequest = new CausalImpactRequest();
		causalImpactRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		causalImpactRequest.setPre_period_start_date("pre_period_start_date");
		causalImpactRequest.setPre_period_end_date("pre_period_end_date");
		causalImpactRequest.setPost_period_start_date("post_period_start_date");

		causalImpactRequest.setTest_time_series(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForY()));

		List<String> controlNameList = new ArrayList<String>();
		controlNameList.add("control1");
		controlNameList.add("control2");
		causalImpactRequest.setControl_name_list(controlNameList);

		List<double[]> controlDataPointsList = new ArrayList<double[]>();
		controlDataPointsList.add(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForX1()));
		controlDataPointsList.add(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForX2()));
		causalImpactRequest.setControl_time_series_list(controlDataPointsList);

		String requestParameters = new Gson().toJson(causalImpactRequest, CausalImpactRequest.class);
		CausalImpactResponse causalImpactResponse = politeCrawlWebServiceClientService.causalImpact(requestUrl, requestParameters);
		assertNotNull("testCase15() causalImpactResponse should not be null.", causalImpactResponse);
		String json = new Gson().toJson(causalImpactResponse, CausalImpactResponse.class);
		assertNotNull("testCase15() causalImpactResponse.getError() should not be null.", causalImpactResponse.getError());
		assertEquals("testCase15() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00014\",\"error_message\":\"Request parameter post_period_end_date is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase15() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase16() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CAUSAL_IMPACT
				+ IConstants.COMMAND_ANALYZE;
		System.out.println("testCase16() requestUrl=" + requestUrl);
		CausalImpactRequest causalImpactRequest = new CausalImpactRequest();
		causalImpactRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		causalImpactRequest.setPre_period_start_date("08/01/2021");
		causalImpactRequest.setPre_period_end_date("08/02/2021");
		causalImpactRequest.setPost_period_start_date("08/04/2021");
		causalImpactRequest.setPost_period_end_date("08/05/2021");

		causalImpactRequest.setTest_time_series(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForY()));

		List<String> controlNameList = new ArrayList<String>();
		controlNameList.add("control1");
		controlNameList.add("control2");
		causalImpactRequest.setControl_name_list(controlNameList);

		List<double[]> controlDataPointsList = new ArrayList<double[]>();
		controlDataPointsList.add(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForX1()));
		controlDataPointsList.add(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForX2()));
		causalImpactRequest.setControl_time_series_list(controlDataPointsList);

		String requestParameters = new Gson().toJson(causalImpactRequest, CausalImpactRequest.class);
		CausalImpactResponse causalImpactResponse = politeCrawlWebServiceClientService.causalImpact(requestUrl, requestParameters);
		assertNotNull("testCase16() causalImpactResponse should not be null.", causalImpactResponse);
		String json = new Gson().toJson(causalImpactResponse, CausalImpactResponse.class);
		assertNotNull("testCase16() causalImpactResponse.getError() should not be null.", causalImpactResponse.getError());
		assertEquals("testCase16() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00015\",\"error_message\":\"Request parameter pre_period_start_date 08/01/2021 is invalid.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase16() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase17() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CAUSAL_IMPACT
				+ IConstants.COMMAND_ANALYZE;
		System.out.println("testCase17() requestUrl=" + requestUrl);
		CausalImpactRequest causalImpactRequest = new CausalImpactRequest();
		causalImpactRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		causalImpactRequest.setPre_period_start_date("2021-08-01");
		causalImpactRequest.setPre_period_end_date("08/02/2021");
		causalImpactRequest.setPost_period_start_date("08/04/2021");
		causalImpactRequest.setPost_period_end_date("08/05/2021");

		causalImpactRequest.setTest_time_series(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForY()));

		List<String> controlNameList = new ArrayList<String>();
		controlNameList.add("control1");
		controlNameList.add("control2");
		causalImpactRequest.setControl_name_list(controlNameList);

		List<double[]> controlDataPointsList = new ArrayList<double[]>();
		controlDataPointsList.add(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForX1()));
		controlDataPointsList.add(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForX2()));
		causalImpactRequest.setControl_time_series_list(controlDataPointsList);

		String requestParameters = new Gson().toJson(causalImpactRequest, CausalImpactRequest.class);
		CausalImpactResponse causalImpactResponse = politeCrawlWebServiceClientService.causalImpact(requestUrl, requestParameters);
		assertNotNull("testCase17() causalImpactResponse should not be null.", causalImpactResponse);
		String json = new Gson().toJson(causalImpactResponse, CausalImpactResponse.class);
		assertNotNull("testCase17() causalImpactResponse.getError() should not be null.", causalImpactResponse.getError());
		assertEquals("testCase17() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00016\",\"error_message\":\"Request parameter pre_period_end_date 08/02/2021 is invalid.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase17() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase18() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CAUSAL_IMPACT
				+ IConstants.COMMAND_ANALYZE;
		System.out.println("testCase18() requestUrl=" + requestUrl);
		CausalImpactRequest causalImpactRequest = new CausalImpactRequest();
		causalImpactRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		causalImpactRequest.setPre_period_start_date("2021-08-01");
		causalImpactRequest.setPre_period_end_date("2021-08-02");
		causalImpactRequest.setPost_period_start_date("08/04/2021");
		causalImpactRequest.setPost_period_end_date("08/05/2021");

		causalImpactRequest.setTest_time_series(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForY()));

		List<String> controlNameList = new ArrayList<String>();
		controlNameList.add("control1");
		controlNameList.add("control2");
		causalImpactRequest.setControl_name_list(controlNameList);

		List<double[]> controlDataPointsList = new ArrayList<double[]>();
		controlDataPointsList.add(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForX1()));
		controlDataPointsList.add(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForX2()));
		causalImpactRequest.setControl_time_series_list(controlDataPointsList);

		String requestParameters = new Gson().toJson(causalImpactRequest, CausalImpactRequest.class);
		CausalImpactResponse causalImpactResponse = politeCrawlWebServiceClientService.causalImpact(requestUrl, requestParameters);
		assertNotNull("testCase18() causalImpactResponse should not be null.", causalImpactResponse);
		String json = new Gson().toJson(causalImpactResponse, CausalImpactResponse.class);
		assertNotNull("testCase18() causalImpactResponse.getError() should not be null.", causalImpactResponse.getError());
		assertEquals("testCase18() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00017\",\"error_message\":\"Request parameter post_period_start_date 08/04/2021 is invalid.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase18() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase19() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CAUSAL_IMPACT
				+ IConstants.COMMAND_ANALYZE;
		System.out.println("testCase19() requestUrl=" + requestUrl);
		CausalImpactRequest causalImpactRequest = new CausalImpactRequest();
		causalImpactRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		causalImpactRequest.setPre_period_start_date("2021-08-01");
		causalImpactRequest.setPre_period_end_date("2021-08-02");
		causalImpactRequest.setPost_period_start_date("2021-08-04");
		causalImpactRequest.setPost_period_end_date("08/05/2021");

		causalImpactRequest.setTest_time_series(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForY()));

		List<String> controlNameList = new ArrayList<String>();
		controlNameList.add("control1");
		controlNameList.add("control2");
		causalImpactRequest.setControl_name_list(controlNameList);

		List<double[]> controlDataPointsList = new ArrayList<double[]>();
		controlDataPointsList.add(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForX1()));
		controlDataPointsList.add(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForX2()));
		causalImpactRequest.setControl_time_series_list(controlDataPointsList);

		String requestParameters = new Gson().toJson(causalImpactRequest, CausalImpactRequest.class);
		CausalImpactResponse causalImpactResponse = politeCrawlWebServiceClientService.causalImpact(requestUrl, requestParameters);
		assertNotNull("testCase19() causalImpactResponse should not be null.", causalImpactResponse);
		String json = new Gson().toJson(causalImpactResponse, CausalImpactResponse.class);
		assertNotNull("testCase19() causalImpactResponse.getError() should not be null.", causalImpactResponse.getError());
		assertEquals("testCase19() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00018\",\"error_message\":\"Request parameter post_period_end_date 08/05/2021 is invalid.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase19() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase20() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CAUSAL_IMPACT
				+ IConstants.COMMAND_ANALYZE;
		System.out.println("testCase20() requestUrl=" + requestUrl);
		CausalImpactRequest causalImpactRequest = new CausalImpactRequest();
		causalImpactRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		causalImpactRequest.setPre_period_start_date("2021-08-03");
		causalImpactRequest.setPre_period_end_date("2021-08-02");
		causalImpactRequest.setPost_period_start_date("2021-08-04");
		causalImpactRequest.setPost_period_end_date("2021-08-05");

		causalImpactRequest.setTest_time_series(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForY()));

		List<String> controlNameList = new ArrayList<String>();
		controlNameList.add("control1");
		controlNameList.add("control2");
		causalImpactRequest.setControl_name_list(controlNameList);

		List<double[]> controlDataPointsList = new ArrayList<double[]>();
		controlDataPointsList.add(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForX1()));
		controlDataPointsList.add(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForX2()));
		causalImpactRequest.setControl_time_series_list(controlDataPointsList);

		String requestParameters = new Gson().toJson(causalImpactRequest, CausalImpactRequest.class);
		CausalImpactResponse causalImpactResponse = politeCrawlWebServiceClientService.causalImpact(requestUrl, requestParameters);
		assertNotNull("testCase20() causalImpactResponse should not be null.", causalImpactResponse);
		String json = new Gson().toJson(causalImpactResponse, CausalImpactResponse.class);
		assertNotNull("testCase20() causalImpactResponse.getError() should not be null.", causalImpactResponse.getError());
		assertEquals("testCase20() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00019\",\"error_message\":\"Request parameter pre_period_start_date and pre_period_end_date combination is invalid.\"}}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase20() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase21() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CAUSAL_IMPACT
				+ IConstants.COMMAND_ANALYZE;
		System.out.println("testCase21() requestUrl=" + requestUrl);
		CausalImpactRequest causalImpactRequest = new CausalImpactRequest();
		causalImpactRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		causalImpactRequest.setPre_period_start_date("2021-08-01");
		causalImpactRequest.setPre_period_end_date("2021-08-02");
		causalImpactRequest.setPost_period_start_date("2021-08-06");
		causalImpactRequest.setPost_period_end_date("2021-08-05");

		causalImpactRequest.setTest_time_series(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForY()));

		List<String> controlNameList = new ArrayList<String>();
		controlNameList.add("control1");
		controlNameList.add("control2");
		causalImpactRequest.setControl_name_list(controlNameList);

		List<double[]> controlDataPointsList = new ArrayList<double[]>();
		controlDataPointsList.add(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForX1()));
		controlDataPointsList.add(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForX2()));
		causalImpactRequest.setControl_time_series_list(controlDataPointsList);

		String requestParameters = new Gson().toJson(causalImpactRequest, CausalImpactRequest.class);
		CausalImpactResponse causalImpactResponse = politeCrawlWebServiceClientService.causalImpact(requestUrl, requestParameters);
		assertNotNull("testCase21() causalImpactResponse should not be null.", causalImpactResponse);
		String json = new Gson().toJson(causalImpactResponse, CausalImpactResponse.class);
		assertNotNull("testCase21() causalImpactResponse.getError() should not be null.", causalImpactResponse.getError());
		assertEquals("testCase21() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00020\",\"error_message\":\"Request parameter post_period_start_date and post_period_end_date combination is invalid.\"}}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase21() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase22() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CAUSAL_IMPACT
				+ IConstants.COMMAND_ANALYZE;
		System.out.println("testCase22() requestUrl=" + requestUrl);
		CausalImpactRequest causalImpactRequest = new CausalImpactRequest();
		causalImpactRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		causalImpactRequest.setPre_period_start_date("2021-08-03");
		causalImpactRequest.setPre_period_end_date("2021-08-05");
		causalImpactRequest.setPost_period_start_date("2021-08-06");
		causalImpactRequest.setPost_period_end_date("2021-08-08");

		causalImpactRequest.setTest_time_series(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForY()));

		List<String> controlNameList = new ArrayList<String>();
		controlNameList.add("control1");
		controlNameList.add("control2");
		causalImpactRequest.setControl_name_list(controlNameList);

		List<double[]> controlDataPointsList = new ArrayList<double[]>();
		controlDataPointsList.add(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForX1()));
		controlDataPointsList.add(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForX2()));
		causalImpactRequest.setControl_time_series_list(controlDataPointsList);

		String requestParameters = new Gson().toJson(causalImpactRequest, CausalImpactRequest.class);
		CausalImpactResponse causalImpactResponse = politeCrawlWebServiceClientService.causalImpact(requestUrl, requestParameters);
		assertNotNull("testCase22() causalImpactResponse should not be null.", causalImpactResponse);
		String json = new Gson().toJson(causalImpactResponse, CausalImpactResponse.class);
		assertNotNull("testCase22() causalImpactResponse.getError() should not be null.", causalImpactResponse.getError());
		assertEquals("testCase22() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00024\",\"error_message\":\"Days between pre-period start and post-period end must equal total number of test data points.\"}}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase22() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase23() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CAUSAL_IMPACT
				+ IConstants.COMMAND_ANALYZE;
		System.out.println("testCase23() requestUrl=" + requestUrl);
		CausalImpactRequest causalImpactRequest = new CausalImpactRequest();
		causalImpactRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		causalImpactRequest.setPre_period_start_date("2021-08-03");
		causalImpactRequest.setPre_period_end_date("2021-08-05");
		causalImpactRequest.setPost_period_start_date("2021-08-07");
		causalImpactRequest.setPost_period_end_date("2021-08-09");

		causalImpactRequest.setTest_time_series(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForY()));

		List<String> controlNameList = new ArrayList<String>();
		controlNameList.add("control1");
		controlNameList.add("control2");
		causalImpactRequest.setControl_name_list(controlNameList);

		List<double[]> controlDataPointsList = new ArrayList<double[]>();
		controlDataPointsList.add(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForX1()));
		controlDataPointsList.add(FormatUtils.getInstance().extractDoubleArray(0, 2, getDoubleArrayForX2()));
		causalImpactRequest.setControl_time_series_list(controlDataPointsList);

		String requestParameters = new Gson().toJson(causalImpactRequest, CausalImpactRequest.class);
		CausalImpactResponse causalImpactResponse = politeCrawlWebServiceClientService.causalImpact(requestUrl, requestParameters);
		assertNotNull("testCase23() causalImpactResponse should not be null.", causalImpactResponse);
		String json = new Gson().toJson(causalImpactResponse, CausalImpactResponse.class);
		assertNotNull("testCase23() causalImpactResponse.getError() should not be null.", causalImpactResponse.getError());
		assertEquals("testCase23() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00023\",\"error_message\":\"Days between pre-period start and post-period end must equal (days between pre-period start and end + days between post-period start and end).\"}}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase23() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase24() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CAUSAL_IMPACT
				+ IConstants.COMMAND_CALCULATE_CORRELATION_COEFFICIENT;
		System.out.println("testCase24() requestUrl=" + requestUrl);
		CausalImpactRequest causalImpactRequest = new CausalImpactRequest();
		causalImpactRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		causalImpactRequest.setTest_time_series(FormatUtils.getInstance().extractDoubleArray(62, 69, getDoubleArrayForY()));

		List<String> controlNameList = new ArrayList<String>();
		controlNameList.add("control1");
		controlNameList.add("control2");
		causalImpactRequest.setControl_name_list(controlNameList);

		List<double[]> controlDataPointsList = new ArrayList<double[]>();
		controlDataPointsList.add(FormatUtils.getInstance().extractDoubleArray(62, 69, getDoubleArrayForX1()));
		controlDataPointsList.add(FormatUtils.getInstance().extractDoubleArray(62, 69, getDoubleArrayForX2()));
		causalImpactRequest.setControl_time_series_list(controlDataPointsList);

		String requestParameters = new Gson().toJson(causalImpactRequest, CausalImpactRequest.class);
		CausalImpactResponse causalImpactResponse = politeCrawlWebServiceClientService.causalImpact(requestUrl, requestParameters);
		assertNotNull("testCase24() causalImpactResponse should not be null.", causalImpactResponse);
		assertEquals("testCase24() causalImpactResponse.getSuccess() incorrect.", true, causalImpactResponse.getSuccess());
		assertNull("testCase24() causalImpactResponse.getError() should be null.", causalImpactResponse.getError());
		assertEquals("testCase24() causalImpactResponse.getControl_name_corr_coef_map().size() incorrect.", "2",
				String.valueOf(causalImpactResponse.getControl_name_corr_coef_map().size()));
		assertEquals("testCase24() causalImpactResponse.getControl_name_corr_coef_map().get(\"control1\") incorrect.", "0.883724130016765",
				String.valueOf(causalImpactResponse.getControl_name_corr_coef_map().get("control1")));
		assertEquals("testCase24() causalImpactResponse.getControl_name_corr_coef_map().get(\"control2\") incorrect.", "0.62874076773511",
				String.valueOf(causalImpactResponse.getControl_name_corr_coef_map().get("control2")));
		FormatUtils.getInstance().logMemoryUsage("testCase24() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase25() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CAUSAL_IMPACT
				+ IConstants.COMMAND_ANALYZE;
		System.out.println("testCase25() requestUrl=" + requestUrl);
		CausalImpactRequest causalImpactRequest = new CausalImpactRequest();
		causalImpactRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		causalImpactRequest.setPre_period_start_date("2021-08-02");
		causalImpactRequest.setPre_period_end_date("2021-08-09");
		causalImpactRequest.setPost_period_start_date("2021-08-10");
		causalImpactRequest.setPost_period_end_date("2021-08-13");

		causalImpactRequest.setTest_time_series(FormatUtils.getInstance().extractDoubleArray(62, 73, getDoubleArrayForY()));

		List<String> controlNameList = new ArrayList<String>();
		controlNameList.add("control1");
		controlNameList.add("control2");
		causalImpactRequest.setControl_name_list(controlNameList);

		List<double[]> controlDataPointsList = new ArrayList<double[]>();
		controlDataPointsList.add(FormatUtils.getInstance().extractDoubleArray(62, 73, getDoubleArrayForX1()));
		controlDataPointsList.add(FormatUtils.getInstance().extractDoubleArray(62, 73, getDoubleArrayForX2()));
		causalImpactRequest.setControl_time_series_list(controlDataPointsList);

		String requestParameters = new Gson().toJson(causalImpactRequest, CausalImpactRequest.class);
		CausalImpactResponse causalImpactResponse = politeCrawlWebServiceClientService.causalImpact(requestUrl, requestParameters);
		assertNotNull("testCase25() causalImpactResponse should not be null.", causalImpactResponse);
		assertEquals("testCase25() causalImpactResponse.getSuccess() incorrect.", true, causalImpactResponse.getSuccess());
		assertNull("testCase25() causalImpactResponse.getError() should be null.", causalImpactResponse.getError());
		assertEquals("testCase25() causalImpactResponse.getPre_period_start_date() incorrect.", "2021-08-02",
				String.valueOf(causalImpactResponse.getPre_period_start_date()));
		assertEquals("testCase25() causalImpactResponse.getPre_period_end_date() incorrect.", "2021-08-09",
				String.valueOf(causalImpactResponse.getPre_period_end_date()));
		assertEquals("testCase25() causalImpactResponse.getPost_period_start_date() incorrect.", "2021-08-10",
				String.valueOf(causalImpactResponse.getPost_period_start_date()));
		assertEquals("testCase25() causalImpactResponse.getPost_period_end_date() incorrect.", "2021-08-13",
				String.valueOf(causalImpactResponse.getPost_period_end_date()));
		boolean isSummaryTextFound = StringUtils.containsIgnoreCase(causalImpactResponse.getSummary(),
				"This means the causal effect can be considered statistically significant");
		assertEquals("testCase25() isSummaryTextFound incorrect.", true, isSummaryTextFound);
		assertEquals("testCase25() average actual incorrect.", "117.504816222559", String.valueOf(causalImpactResponse.getPosterior_inference_average().getActual()));
		assertEquals("testCase25() average alpha incorrect.", "0.05", String.valueOf(causalImpactResponse.getPosterior_inference_average().getAlpha()));
		//assertEquals("testCase25() average prediction incorrect.", "106.635094514604",
		//		String.valueOf(causalImpactResponse.getPosterior_inference_average().getPrediction()));
		//assertEquals("testCase25() average absolute_effect incorrect.", "10.8697217079551",
		//		String.valueOf(causalImpactResponse.getPosterior_inference_average().getAbsolute_effect()));
		//assertEquals("testCase25() average relative_effect incorrect.", "0.101933812291659",
		//		String.valueOf(causalImpactResponse.getPosterior_inference_average().getRelative_effect()));
		//assertEquals("testCase25() average tail_area_probability incorrect.", "0.00105374077976818",
		//		String.valueOf(causalImpactResponse.getPosterior_inference_average().getTail_area_probability()));

		assertEquals("testCase25() cumulative actual incorrect.", "470.019264890238",
				String.valueOf(causalImpactResponse.getPosterior_inference_cumulative().getActual()));
		assertEquals("testCase25() cumulative alpha incorrect.", "0.05", String.valueOf(causalImpactResponse.getPosterior_inference_cumulative().getAlpha()));
		//assertEquals("testCase25() cumulative prediction incorrect.", "426.540378058418",
		//		String.valueOf(causalImpactResponse.getPosterior_inference_cumulative().getPrediction()));
		//assertEquals("testCase25() cumulative absolute_effect incorrect.", "43.4788868318202",
		//		String.valueOf(causalImpactResponse.getPosterior_inference_cumulative().getAbsolute_effect()));
		//assertEquals("testCase25() cumulative relative_effect incorrect.", "0.101933812291659",
		//		String.valueOf(causalImpactResponse.getPosterior_inference_cumulative().getRelative_effect()));
		//assertEquals("testCase25() cumulative tail_area_probability incorrect.", "0.00105374077976818",
		//		String.valueOf(causalImpactResponse.getPosterior_inference_cumulative().getTail_area_probability()));

		assertEquals("testCase25() causalImpactResponse.getTest_time_series().length incorrect.", "12",
				String.valueOf(causalImpactResponse.getTest_time_series().length));

		assertEquals("testCase25() causalImpactResponse.getCounter_factual_prediction_time_series().length incorrect.", "12",
				String.valueOf(causalImpactResponse.getCounter_factual_prediction_time_series().length));
		//assertEquals("testCase25() causalImpactResponse.getCounter_factual_prediction_time_series()[0] incorrect.", "112.216861378573",
		//		String.valueOf(causalImpactResponse.getCounter_factual_prediction_time_series()[0]));
		//assertEquals("testCase25() causalImpactResponse.getCounter_factual_prediction_time_series()[11] incorrect.", "107.531649591619",
		//		String.valueOf(causalImpactResponse.getCounter_factual_prediction_time_series()[11]));

		assertEquals("testCase25() causalImpactResponse.getCounter_factual_prediction_time_series_lower().length incorrect.", "12",
				String.valueOf(causalImpactResponse.getCounter_factual_prediction_time_series_lower().length));

		assertEquals("testCase25() causalImpactResponse.getCounter_factual_prediction_time_series_upper().length incorrect.", "12",
				String.valueOf(causalImpactResponse.getCounter_factual_prediction_time_series_upper().length));

		assertEquals("testCase25() causalImpactResponse.getPointwise_causal_effect_time_series().length incorrect.", "12",
				String.valueOf(causalImpactResponse.getPointwise_causal_effect_time_series().length));
		//assertEquals("testCase25() causalImpactResponse.getPointwise_causal_effect_time_series()[0] incorrect.", "0.650482315999639",
		//		String.valueOf(causalImpactResponse.getPointwise_causal_effect_time_series()[0]));
		//assertEquals("testCase25() causalImpactResponse.getPointwise_causal_effect_time_series()[11] incorrect.", "10.4443455174353",
		//		String.valueOf(causalImpactResponse.getPointwise_causal_effect_time_series()[11]));

		assertEquals("testCase25() causalImpactResponse.getPointwise_causal_effect_time_series_lower().length incorrect.", "12",
				String.valueOf(causalImpactResponse.getPointwise_causal_effect_time_series_lower().length));

		assertEquals("testCase25() causalImpactResponse.getPointwise_causal_effect_time_series_upper().length incorrect.", "12",
				String.valueOf(causalImpactResponse.getPointwise_causal_effect_time_series_upper().length));

		assertEquals("testCase25() causalImpactResponse.getCumulative_effect_time_series().length incorrect.", "12",
				String.valueOf(causalImpactResponse.getCumulative_effect_time_series().length));
		assertEquals("testCase25() causalImpactResponse.getCumulative_effect_time_series()[0] incorrect.", "0.0",
				String.valueOf(causalImpactResponse.getCumulative_effect_time_series()[0]));
		//assertEquals("testCase25() causalImpactResponse.getCumulative_effect_time_series()[11] incorrect.", "43.4788868318203",
		//		String.valueOf(causalImpactResponse.getCumulative_effect_time_series()[11]));

		assertEquals("testCase25() causalImpactResponse.getCumulative_effect_time_series_lower().length incorrect.", "12",
				String.valueOf(causalImpactResponse.getCumulative_effect_time_series_lower().length));

		assertEquals("testCase25() causalImpactResponse.getCumulative_effect_time_series_upper().length incorrect.", "12",
				String.valueOf(causalImpactResponse.getCumulative_effect_time_series_upper().length));

		FormatUtils.getInstance().logMemoryUsage("testCase25() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void test() throws Exception {
		String requestUrl = "https://api.seoclarity.net/seoClarity/causal_impact/analyze";
		CausalImpactRequest causalImpactRequest = new CausalImpactRequest();
		causalImpactRequest.setAccess_token("xxxx");
		causalImpactRequest.setPre_period_start_date("2021-08-02");
		causalImpactRequest.setPre_period_end_date("2021-08-09");
		causalImpactRequest.setPost_period_start_date("2021-08-10");
		causalImpactRequest.setPost_period_end_date("2021-08-13");

		causalImpactRequest.setTest_time_series(new double[] { 112.867343694573, 111.538727869305, 112.284408699329, 109.686179697106, 109.521634601159,
				106.002957468914, 106.543084370612, 108.555071439643, 117.294312743074, 116.495691770188, 118.253265267922, 117.975995109054 });
		List<String> controlNameList = new ArrayList<String>();
		controlNameList.add("control1");
		controlNameList.add("control2");
		causalImpactRequest.setControl_name_list(controlNameList);
		List<double[]> controlDataPointsList = new ArrayList<double[]>();

		controlDataPointsList.add(new double[] { 112.135961643253, 111.790476570709, 112.119071373401, 110.389096157736, 109.012492053947, 107.959737625246,
				108.13144947965, 106.942126862978, 106.418696537089, 106.409460393465, 107.727921968003, 108.022532200572 });
		controlDataPointsList.add(new double[] { 112.135961643253, 109.790476570709, 114.119071373401, 112.389096157736, 111.012492053947, 109.959737625246,
				106.13144947965, 104.942126862978, 104.418696537089, 108.409460393465, 108.727921968003, 109.022532200572 });

		causalImpactRequest.setControl_time_series_list(controlDataPointsList);
		String requestParameters = new Gson().toJson(causalImpactRequest, CausalImpactRequest.class);
		CausalImpactResponse causalImpactResponse = politeCrawlWebServiceClientService.causalImpact(requestUrl, requestParameters);
		if (BooleanUtils.isTrue(causalImpactResponse.getSuccess())) {
			System.out.println("causalImpactResponse.getPre_period_start_date()=" + causalImpactResponse.getPre_period_start_date());
			System.out.println("causalImpactResponse.getPre_period_end_date()=" + causalImpactResponse.getPre_period_end_date());
			System.out.println("causalImpactResponse.getPost_period_start_date()=" + causalImpactResponse.getPost_period_start_date());
			System.out.println("causalImpactResponse.getPost_period_end_date()=" + causalImpactResponse.getPost_period_end_date());
			System.out
					.println("causalImpactResponse.getPosterior_inference_average().getActual()=" + causalImpactResponse.getPosterior_inference_average().getActual());
			System.out.println("causalImpactResponse.getPosterior_inference_average().getAlpha()=" + causalImpactResponse.getPosterior_inference_average().getAlpha());
			System.out.println(
					"causalImpactResponse.getPosterior_inference_average().getPrediction()=" + causalImpactResponse.getPosterior_inference_average().getPrediction());
			System.out.println("causalImpactResponse.getPosterior_inference_average().getPrediction_lower()="
					+ causalImpactResponse.getPosterior_inference_average().getPrediction_lower());
			System.out.println("causalImpactResponse.getPosterior_inference_average().getPrediction_upper()="
					+ causalImpactResponse.getPosterior_inference_average().getPrediction_upper());
			System.out.println("causalImpactResponse.getPosterior_inference_average().getPrediction_standard_derivation()="
					+ causalImpactResponse.getPosterior_inference_average().getPrediction_standard_derivation());
			System.out.println("causalImpactResponse.getPosterior_inference_average().getAbsolute_effect()="
					+ causalImpactResponse.getPosterior_inference_average().getAbsolute_effect());
			System.out.println("causalImpactResponse.getPosterior_inference_average().getAbsolute_effect_lower()="
					+ causalImpactResponse.getPosterior_inference_average().getAbsolute_effect_lower());
			System.out.println("causalImpactResponse.getPosterior_inference_average().getAbsolute_effect_upper()="
					+ causalImpactResponse.getPosterior_inference_average().getAbsolute_effect_upper());
			System.out.println("causalImpactResponse.getPosterior_inference_average().getAbsolute_effect_standard_derivation()="
					+ causalImpactResponse.getPosterior_inference_average().getAbsolute_effect_standard_derivation());
			System.out.println("causalImpactResponse.getPosterior_inference_average().getRelative_effect()="
					+ causalImpactResponse.getPosterior_inference_average().getRelative_effect());
			System.out.println("causalImpactResponse.getPosterior_inference_average().getRelative_effect_lower()="
					+ causalImpactResponse.getPosterior_inference_average().getRelative_effect_lower());
			System.out.println("causalImpactResponse.getPosterior_inference_average().getRelative_effect_upper()="
					+ causalImpactResponse.getPosterior_inference_average().getRelative_effect_upper());
			System.out.println("causalImpactResponse.getPosterior_inference_average().getRelative_effect_standard_derivation()="
					+ causalImpactResponse.getPosterior_inference_average().getRelative_effect_standard_derivation());
			System.out.println("causalImpactResponse.getPosterior_inference_average().getTail_area_probability()="
					+ causalImpactResponse.getPosterior_inference_average().getTail_area_probability());
			System.out.println(
					"causalImpactResponse.getPosterior_inference_cumulative().getActual()=" + causalImpactResponse.getPosterior_inference_cumulative().getActual());
			System.out.println(
					"causalImpactResponse.getPosterior_inference_cumulative().getAlpha()=" + causalImpactResponse.getPosterior_inference_cumulative().getAlpha());
			System.out.println("causalImpactResponse.getPosterior_inference_cumulative().getPrediction()="
					+ causalImpactResponse.getPosterior_inference_cumulative().getPrediction());
			System.out.println("causalImpactResponse.getPosterior_inference_cumulative().getPrediction_lower()="
					+ causalImpactResponse.getPosterior_inference_cumulative().getPrediction_lower());
			System.out.println("causalImpactResponse.getPosterior_inference_cumulative().getPrediction_upper()="
					+ causalImpactResponse.getPosterior_inference_cumulative().getPrediction_upper());
			System.out.println("causalImpactResponse.getPosterior_inference_cumulative().getPrediction_standard_derivation()="
					+ causalImpactResponse.getPosterior_inference_cumulative().getPrediction_standard_derivation());
			System.out.println("causalImpactResponse.getPosterior_inference_cumulative().getAbsolute_effect()="
					+ causalImpactResponse.getPosterior_inference_cumulative().getAbsolute_effect());
			System.out.println("causalImpactResponse.getPosterior_inference_cumulative().getAbsolute_effect_lower()="
					+ causalImpactResponse.getPosterior_inference_cumulative().getAbsolute_effect_lower());
			System.out.println("causalImpactResponse.getPosterior_inference_cumulative().getAbsolute_effect_upper()="
					+ causalImpactResponse.getPosterior_inference_cumulative().getAbsolute_effect_upper());
			System.out.println("causalImpactResponse.getPosterior_inference_cumulative().getAbsolute_effect_standard_derivation()="
					+ causalImpactResponse.getPosterior_inference_cumulative().getAbsolute_effect_standard_derivation());
			System.out.println("causalImpactResponse.getPosterior_inference_cumulative().getRelative_effect()="
					+ causalImpactResponse.getPosterior_inference_cumulative().getRelative_effect());
			System.out.println("causalImpactResponse.getPosterior_inference_cumulative().getRelative_effect_lower()="
					+ causalImpactResponse.getPosterior_inference_cumulative().getRelative_effect_lower());
			System.out.println("causalImpactResponse.getPosterior_inference_cumulative().getRelative_effect_upper()="
					+ causalImpactResponse.getPosterior_inference_cumulative().getRelative_effect_upper());
			System.out.println("causalImpactResponse.getPosterior_inference_cumulative().getRelative_effect_standard_derivation()="
					+ causalImpactResponse.getPosterior_inference_cumulative().getRelative_effect_standard_derivation());
			System.out.println("causalImpactResponse.getPosterior_inference_cumulative().getTail_area_probability()="
					+ causalImpactResponse.getPosterior_inference_cumulative().getTail_area_probability());
			for (double testData : causalImpactResponse.getTest_time_series()) {
				System.out.println("testData=" + testData);
			}
			for (double counterFactualPredictionData : causalImpactResponse.getCounter_factual_prediction_time_series()) {
				System.out.println("counterFactualPredictionData=" + counterFactualPredictionData);
			}
		}

	}

	private void testCase26() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CAUSAL_IMPACT
				+ IConstants.COMMAND_CALCULATE_CORRELATION_COEFFICIENT;
		System.out.println("testCase26() requestUrl=" + requestUrl);
		CausalImpactRequest causalImpactRequest = new CausalImpactRequest();
		causalImpactRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		causalImpactRequest.setTest_time_series(getTestCase26DoubleArrayForY());

		List<String> controlNameList = new ArrayList<String>();
		controlNameList.add("1415023");
		controlNameList.add("1419474");
		causalImpactRequest.setControl_name_list(controlNameList);

		List<double[]> controlDataPointsList = new ArrayList<double[]>();
		controlDataPointsList.add(getTestCase26DoubleArrayForX1());
		controlDataPointsList.add(getTestCase26DoubleArrayForX2());
		causalImpactRequest.setControl_time_series_list(controlDataPointsList);

		String requestParameters = new Gson().toJson(causalImpactRequest, CausalImpactRequest.class);
		CausalImpactResponse causalImpactResponse = politeCrawlWebServiceClientService.causalImpact(requestUrl, requestParameters);
		assertNotNull("testCase26() causalImpactResponse should not be null.", causalImpactResponse);
		assertEquals("testCase26() causalImpactResponse.getSuccess() incorrect.", true, causalImpactResponse.getSuccess());
		assertNull("testCase26() causalImpactResponse.getError() should be null.", causalImpactResponse.getError());
		FormatUtils.getInstance().logMemoryUsage("testCase26() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase27() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CAUSAL_IMPACT
				+ IConstants.COMMAND_CALCULATE_CORRELATION_COEFFICIENT;
		System.out.println("testCase27() requestUrl=" + requestUrl);
		CausalImpactRequest causalImpactRequest = new CausalImpactRequest();
		causalImpactRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		causalImpactRequest.setTest_time_series(getTestCase26DoubleArrayForY());

		List<String> controlNameList = new ArrayList<String>();
		controlNameList.add("脆弱的 1``");
		controlNameList.add("脆弱的 2%$");
		causalImpactRequest.setControl_name_list(controlNameList);

		List<double[]> controlDataPointsList = new ArrayList<double[]>();
		controlDataPointsList.add(getTestCase26DoubleArrayForX1());
		controlDataPointsList.add(getTestCase26DoubleArrayForX2());
		causalImpactRequest.setControl_time_series_list(controlDataPointsList);

		String requestParameters = new Gson().toJson(causalImpactRequest, CausalImpactRequest.class);
		CausalImpactResponse causalImpactResponse = politeCrawlWebServiceClientService.causalImpact(requestUrl, requestParameters);
		assertNotNull("testCase27() causalImpactResponse should not be null.", causalImpactResponse);
		assertEquals("testCase27() causalImpactResponse.getSuccess() incorrect.", true, causalImpactResponse.getSuccess());
		assertNull("testCase27() causalImpactResponse.getError() should be null.", causalImpactResponse.getError());
		FormatUtils.getInstance().logMemoryUsage("testCase27() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase28() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CAUSAL_IMPACT
				+ IConstants.COMMAND_ANALYZE;
		System.out.println("testCase28() requestUrl=" + requestUrl);
		CausalImpactRequest causalImpactRequest = new CausalImpactRequest();
		causalImpactRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		causalImpactRequest.setPre_period_start_date("2021-08-02");
		causalImpactRequest.setPre_period_end_date("2021-08-09");
		causalImpactRequest.setPost_period_start_date("2021-08-10");
		causalImpactRequest.setPost_period_end_date("2021-08-13");

		causalImpactRequest.setTest_time_series(FormatUtils.getInstance().extractDoubleArray(62, 73, getDoubleArrayForY()));

		List<String> controlNameList = new ArrayList<String>();
		controlNameList.add("脆弱的 1``");
		controlNameList.add("脆弱的 2%$");
		causalImpactRequest.setControl_name_list(controlNameList);

		List<double[]> controlDataPointsList = new ArrayList<double[]>();
		controlDataPointsList.add(FormatUtils.getInstance().extractDoubleArray(62, 73, getDoubleArrayForX1()));
		controlDataPointsList.add(FormatUtils.getInstance().extractDoubleArray(62, 73, getDoubleArrayForX2()));
		causalImpactRequest.setControl_time_series_list(controlDataPointsList);

		String requestParameters = new Gson().toJson(causalImpactRequest, CausalImpactRequest.class);
		CausalImpactResponse causalImpactResponse = politeCrawlWebServiceClientService.causalImpact(requestUrl, requestParameters);
		assertNotNull("testCase28() causalImpactResponse should not be null.", causalImpactResponse);
		assertEquals("testCase28() causalImpactResponse.getSuccess() incorrect.", true, causalImpactResponse.getSuccess());
		assertNull("testCase28() causalImpactResponse.getError() should be null.", causalImpactResponse.getError());
		assertEquals("testCase28() causalImpactResponse.getPre_period_start_date() incorrect.", "2021-08-02",
				String.valueOf(causalImpactResponse.getPre_period_start_date()));
		assertEquals("testCase28() causalImpactResponse.getPre_period_end_date() incorrect.", "2021-08-09",
				String.valueOf(causalImpactResponse.getPre_period_end_date()));
		assertEquals("testCase28() causalImpactResponse.getPost_period_start_date() incorrect.", "2021-08-10",
				String.valueOf(causalImpactResponse.getPost_period_start_date()));
		assertEquals("testCase28() causalImpactResponse.getPost_period_end_date() incorrect.", "2021-08-13",
				String.valueOf(causalImpactResponse.getPost_period_end_date()));
		boolean isSummaryTextFound = StringUtils.containsIgnoreCase(causalImpactResponse.getSummary(),
				"This means the causal effect can be considered statistically significant");
		assertEquals("testCase28() isSummaryTextFound incorrect.", true, isSummaryTextFound);
		assertEquals("testCase28() average actual incorrect.", "117.504816222559", String.valueOf(causalImpactResponse.getPosterior_inference_average().getActual()));
		assertEquals("testCase28() average alpha incorrect.", "0.05", String.valueOf(causalImpactResponse.getPosterior_inference_average().getAlpha()));
		//assertEquals("testCase28() average prediction incorrect.", "106.635094514604",
		//		String.valueOf(causalImpactResponse.getPosterior_inference_average().getPrediction()));
		//assertEquals("testCase28() average absolute_effect incorrect.", "10.8697217079551",
		//		String.valueOf(causalImpactResponse.getPosterior_inference_average().getAbsolute_effect()));
		//assertEquals("testCase28() average relative_effect incorrect.", "0.101933812291659",
		//		String.valueOf(causalImpactResponse.getPosterior_inference_average().getRelative_effect()));
		//assertEquals("testCase28() average tail_area_probability incorrect.", "0.00105374077976818",
		//		String.valueOf(causalImpactResponse.getPosterior_inference_average().getTail_area_probability()));

		assertEquals("testCase28() cumulative actual incorrect.", "470.019264890238",
				String.valueOf(causalImpactResponse.getPosterior_inference_cumulative().getActual()));
		assertEquals("testCase28() cumulative alpha incorrect.", "0.05", String.valueOf(causalImpactResponse.getPosterior_inference_cumulative().getAlpha()));
		//assertEquals("testCase28() cumulative prediction incorrect.", "426.540378058418",
		//		String.valueOf(causalImpactResponse.getPosterior_inference_cumulative().getPrediction()));
		//assertEquals("testCase28() cumulative absolute_effect incorrect.", "43.4788868318202",
		//		String.valueOf(causalImpactResponse.getPosterior_inference_cumulative().getAbsolute_effect()));
		//assertEquals("testCase28() cumulative relative_effect incorrect.", "0.101933812291659",
		//		String.valueOf(causalImpactResponse.getPosterior_inference_cumulative().getRelative_effect()));
		//assertEquals("testCase28() cumulative tail_area_probability incorrect.", "0.00105374077976818",
		//		String.valueOf(causalImpactResponse.getPosterior_inference_cumulative().getTail_area_probability()));

		assertEquals("testCase28() causalImpactResponse.getTest_time_series().length incorrect.", "12",
				String.valueOf(causalImpactResponse.getTest_time_series().length));

		assertEquals("testCase28() causalImpactResponse.getCounter_factual_prediction_time_series().length incorrect.", "12",
				String.valueOf(causalImpactResponse.getCounter_factual_prediction_time_series().length));
		//assertEquals("testCase28() causalImpactResponse.getCounter_factual_prediction_time_series()[0] incorrect.", "112.216861378573",
		//		String.valueOf(causalImpactResponse.getCounter_factual_prediction_time_series()[0]));
		//assertEquals("testCase28() causalImpactResponse.getCounter_factual_prediction_time_series()[11] incorrect.", "107.531649591619",
		//		String.valueOf(causalImpactResponse.getCounter_factual_prediction_time_series()[11]));

		assertEquals("testCase28() causalImpactResponse.getCounter_factual_prediction_time_series_lower().length incorrect.", "12",
				String.valueOf(causalImpactResponse.getCounter_factual_prediction_time_series_lower().length));

		assertEquals("testCase28() causalImpactResponse.getCounter_factual_prediction_time_series_upper().length incorrect.", "12",
				String.valueOf(causalImpactResponse.getCounter_factual_prediction_time_series_upper().length));

		assertEquals("testCase28() causalImpactResponse.getPointwise_causal_effect_time_series().length incorrect.", "12",
				String.valueOf(causalImpactResponse.getPointwise_causal_effect_time_series().length));
		//assertEquals("testCase28() causalImpactResponse.getPointwise_causal_effect_time_series()[0] incorrect.", "0.650482315999639",
		//		String.valueOf(causalImpactResponse.getPointwise_causal_effect_time_series()[0]));
		//assertEquals("testCase28() causalImpactResponse.getPointwise_causal_effect_time_series()[11] incorrect.", "10.4443455174353",
		//		String.valueOf(causalImpactResponse.getPointwise_causal_effect_time_series()[11]));

		assertEquals("testCase28() causalImpactResponse.getPointwise_causal_effect_time_series_lower().length incorrect.", "12",
				String.valueOf(causalImpactResponse.getPointwise_causal_effect_time_series_lower().length));

		assertEquals("testCase28() causalImpactResponse.getPointwise_causal_effect_time_series_upper().length incorrect.", "12",
				String.valueOf(causalImpactResponse.getPointwise_causal_effect_time_series_upper().length));

		assertEquals("testCase28() causalImpactResponse.getCumulative_effect_time_series().length incorrect.", "12",
				String.valueOf(causalImpactResponse.getCumulative_effect_time_series().length));
		assertEquals("testCase28() causalImpactResponse.getCumulative_effect_time_series()[0] incorrect.", "0.0",
				String.valueOf(causalImpactResponse.getCumulative_effect_time_series()[0]));
		//assertEquals("testCase28() causalImpactResponse.getCumulative_effect_time_series()[11] incorrect.", "43.4788868318203",
		//		String.valueOf(causalImpactResponse.getCumulative_effect_time_series()[11]));

		assertEquals("testCase28() causalImpactResponse.getCumulative_effect_time_series_lower().length incorrect.", "12",
				String.valueOf(causalImpactResponse.getCumulative_effect_time_series_lower().length));

		assertEquals("testCase28() causalImpactResponse.getCumulative_effect_time_series_upper().length incorrect.", "12",
				String.valueOf(causalImpactResponse.getCumulative_effect_time_series_upper().length));

		FormatUtils.getInstance().logMemoryUsage("testCase28() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase29() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CAUSAL_IMPACT
				+ IConstants.COMMAND_CALCULATE_CORRELATION_COEFFICIENT;
		System.out.println("testCase29() requestUrl=" + requestUrl);
		CausalImpactRequest causalImpactRequest = new CausalImpactRequest();
		causalImpactRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);

		causalImpactRequest.setTest_time_series(getTestCase29DoubleArrayForY());

		List<String> controlNameList = new ArrayList<String>();
		controlNameList.add("control1");
		controlNameList.add("control2");
		controlNameList.add("control3");
		causalImpactRequest.setControl_name_list(controlNameList);

		List<double[]> controlDataPointsList = new ArrayList<double[]>();
		controlDataPointsList.add(getTestCase29DoubleArrayForX1());
		controlDataPointsList.add(getTestCase29DoubleArrayForX2());
		controlDataPointsList.add(getTestCase29DoubleArrayForX3());
		causalImpactRequest.setControl_time_series_list(controlDataPointsList);

		String requestParameters = new Gson().toJson(causalImpactRequest, CausalImpactRequest.class);
		CausalImpactResponse causalImpactResponse = politeCrawlWebServiceClientService.causalImpact(requestUrl, requestParameters);
		assertNotNull("testCase29() causalImpactResponse should not be null.", causalImpactResponse);
		assertEquals("testCase29() causalImpactResponse.getSuccess() incorrect.", true, causalImpactResponse.getSuccess());
		assertNull("testCase29() causalImpactResponse.getError() should be null.", causalImpactResponse.getError());
		FormatUtils.getInstance().logMemoryUsage("testCase29() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase30() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CAUSAL_IMPACT
				+ IConstants.COMMAND_ANALYZE;
		System.out.println("testCase30() requestUrl=" + requestUrl);
		CausalImpactRequest causalImpactRequest = new CausalImpactRequest();
		causalImpactRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		causalImpactRequest.setPre_period_start_date("2021-06-03");
		causalImpactRequest.setPre_period_end_date("2021-08-02");
		causalImpactRequest.setPost_period_start_date("2021-08-03");
		causalImpactRequest.setPost_period_end_date("2021-09-08");

		causalImpactRequest.setTest_time_series(getTestCase30DoubleArrayForY());

		List<String> controlNameList = new ArrayList<String>();
		controlNameList.add("1415023");
		controlNameList.add("1419474");
		causalImpactRequest.setControl_name_list(controlNameList);

		List<double[]> controlDataPointsList = new ArrayList<double[]>();
		controlDataPointsList.add(getTestCase30DoubleArrayForX1());
		controlDataPointsList.add(getTestCase30DoubleArrayForX2());
		causalImpactRequest.setControl_time_series_list(controlDataPointsList);

		String requestParameters = new Gson().toJson(causalImpactRequest, CausalImpactRequest.class);
		CausalImpactResponse causalImpactResponse = politeCrawlWebServiceClientService.causalImpact(requestUrl, requestParameters);
		assertNotNull("testCase30() causalImpactResponse should not be null.", causalImpactResponse);
		assertEquals("testCase30() causalImpactResponse.getSuccess() incorrect.", false, causalImpactResponse.getSuccess());
		assertNotNull("testCase30() causalImpactResponse.getError() should not be null.", causalImpactResponse.getError());
		String json = new Gson().toJson(causalImpactResponse, CausalImpactResponse.class);
		assertNotNull("testCase30() causalImpactResponse.getError() should not be null.", causalImpactResponse.getError());
		assertEquals("testCase30() json is incorrect", "{\"success\":false,\"error\":{\"error_code\":\"00003\",\"error_message\":\"Data cannot be analyzed.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase30() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase31() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CAUSAL_IMPACT
				+ IConstants.COMMAND_ANALYZE_WITHOUT_CONTROL_SERIES;
		System.out.println("testCase31() requestUrl=" + requestUrl);
		CausalImpactRequest causalImpactRequest = new CausalImpactRequest();
		causalImpactRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		causalImpactRequest.setPre_period_start_date("2021-05-17");
		causalImpactRequest.setPre_period_end_date("2021-09-15");
		causalImpactRequest.setPost_period_start_date("2021-09-16");
		causalImpactRequest.setPost_period_end_date("2022-02-16");

		causalImpactRequest.setTest_time_series(getTestCase31DoubleArrayForY());

		String requestParameters = new Gson().toJson(causalImpactRequest, CausalImpactRequest.class);
		CausalImpactResponse causalImpactResponse = politeCrawlWebServiceClientService.causalImpact(requestUrl, requestParameters);
		assertNotNull("testCase31() causalImpactResponse should not be null.", causalImpactResponse);
		assertEquals("testCase31() causalImpactResponse.getSuccess() incorrect.", true, causalImpactResponse.getSuccess());
		//String json = new Gson().toJson(causalImpactResponse, CausalImpactResponse.class);
		//System.out.println("testCase31() json="+json);
		FormatUtils.getInstance().logMemoryUsage("testCase31() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase32() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CAUSAL_IMPACT
				+ IConstants.COMMAND_ANALYZE;
		System.out.println("testCase32() requestUrl=" + requestUrl);
		CausalImpactRequest causalImpactRequest = new CausalImpactRequest();
		causalImpactRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		causalImpactRequest.setPre_period_start_date("2022-01-14");
		causalImpactRequest.setPre_period_end_date("2022-03-15");
		causalImpactRequest.setPost_period_start_date("2022-03-16");
		causalImpactRequest.setPost_period_end_date("2022-05-14");

		causalImpactRequest.setTest_time_series(getTestCase32TestTimeSeries());

		List<String> controlNameList = new ArrayList<String>();
		controlNameList.add("7352801");
		causalImpactRequest.setControl_name_list(controlNameList);

		List<double[]> controlDataPointsList = new ArrayList<double[]>();
		controlDataPointsList.add(getTestCase32ControlTimeSeries());
		causalImpactRequest.setControl_time_series_list(controlDataPointsList);

		String requestJson = new Gson().toJson(causalImpactRequest, CausalImpactRequest.class);
		System.out.println("testCase32() requestJson=" + requestJson);
		CausalImpactResponse causalImpactResponse = politeCrawlWebServiceClientService.causalImpact(requestUrl, requestJson);
		assertNotNull("testCase32() causalImpactResponse should not be null.", causalImpactResponse);
		assertEquals("testCase32() causalImpactResponse.getSuccess() incorrect.", true, causalImpactResponse.getSuccess());
		String responseJson = new Gson().toJson(causalImpactResponse, CausalImpactResponse.class);
		System.out.println("testCase32() responseJson=" + responseJson);
		FormatUtils.getInstance().logMemoryUsage("testCase32() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private double[] getTestCase32TestTimeSeries() {
		return new double[] { 0.006200000178068876, 0.006899999920278788, 0.009800000116229058, 0.007499999832361937, 0.007400000002235174, 0.006200000178068876,
				0.006399999838322401, 0.007000000216066837, 0.0071000000461936, 0.007400000002235174, 0.006800000090152025, 0.00800000037997961, 0.007199999876320362,
				0.006800000090152025, 0.006599999964237213, 0.008299999870359898, 0.008299999870359898, 0.007499999832361937, 0.007000000216066837,
				0.007300000172108412, 0.006200000178068876, 0.006500000134110451, 0.007899999618530274, 0.009800000116229058, 0.0071000000461936, 0.007400000002235174,
				0.009200000204145909, 0.00839999970048666, 0.006899999920278788, 0.007699999958276749, 0.00800000037997961, 0.008299999870359898, 0.008899999782443047,
				0.006300000008195639, 0.007000000216066837, 0.006099999882280827, 0.00559999980032444, 0.007499999832361937, 0.006899999920278788, 0.00800000037997961,
				0.007699999958276749, 0.007000000216066837, 0.006599999964237213, 0.006500000134110451, 0.0066999997943639759, 0.007400000002235174,
				0.006300000008195639, 0.007300000172108412, 0.006300000008195639, 0.006000000052154064, 0.007300000172108412, 0.007799999788403511, 0.00860000029206276,
				0.007000000216066837, 0.006399999838322401, 0.007499999832361937, 0.00839999970048666, 0.007400000002235174, 0.007799999788403511, 0.009100000374019146,
				0.00860000029206276, 0.008100000210106373, 0.009499999694526196, 0.006500000134110451, 0.009399999864399434, 0.009999999776482582, 0.00930000003427267,
				0.008700000122189522, 0.00860000029206276, 0.007699999958276749, 0.008500000461935997, 0.008200000040233136, 0.009700000286102295, 0.007499999832361937,
				0.009100000374019146, 0.006599999964237213, 0.007799999788403511, 0.006800000090152025, 0.007600000128149986, 0.00930000003427267, 0.006899999920278788,
				0.006500000134110451, 0.007400000002235174, 0.007600000128149986, 0.007499999832361937, 0.007000000216066837, 0.010499999858438969,
				0.007499999832361937, 0.007199999876320362, 0.007300000172108412, 0.006800000090152025, 0.007899999618530274, 0.007699999958276749,
				0.008999999612569809, 0.009499999694526196, 0.008799999952316285, 0.007300000172108412, 0.007799999788403511, 0.007000000216066837,
				0.007300000172108412, 0.00860000029206276, 0.006399999838322401, 0.008899999782443047, 0.007199999876320362, 0.007600000128149986, 0.007400000002235174,
				0.007499999832361937, 0.008500000461935997, 0.009100000374019146, 0.007799999788403511, 0.007600000128149986, 0.007000000216066837,
				0.007600000128149986, 0.006399999838322401, 0.009100000374019146, 0.006800000090152025, 0.00860000029206276, 0.00930000003427267, 0.008799999952316285,
				0.00860000029206276, 0.007400000002235174 };
	}

	private double[] getTestCase32ControlTimeSeries() {
		return new double[] { 0.006599999964237213, 0.006800000090152025, 0.00860000029206276, 0.007499999832361937, 0.00800000037997961, 0.006300000008195639,
				0.006200000178068876, 0.008299999870359898, 0.007699999958276749, 0.00800000037997961, 0.006399999838322401, 0.00800000037997961, 0.006200000178068876,
				0.007699999958276749, 0.006300000008195639, 0.0071000000461936, 0.008700000122189522, 0.007799999788403511, 0.007600000128149986, 0.007300000172108412,
				0.006500000134110451, 0.006399999838322401, 0.007000000216066837, 0.008799999952316285, 0.006899999920278788, 0.0071000000461936, 0.00559999980032444,
				0.007000000216066837, 0.007199999876320362, 0.007300000172108412, 0.00800000037997961, 0.008500000461935997, 0.008100000210106373, 0.006599999964237213,
				0.006399999838322401, 0.004800000227987766, 0.006500000134110451, 0.007000000216066837, 0.00800000037997961, 0.0066999997943639759,
				0.006599999964237213, 0.006399999838322401, 0.005499999970197678, 0.006800000090152025, 0.008299999870359898, 0.007300000172108412,
				0.005799999926239252, 0.006899999920278788, 0.007199999876320362, 0.006300000008195639, 0.006899999920278788, 0.007600000128149986,
				0.008500000461935997, 0.00860000029206276, 0.006599999964237213, 0.006599999964237213, 0.006800000090152025, 0.007799999788403511, 0.007600000128149986,
				0.009399999864399434, 0.007699999958276749, 0.007699999958276749, 0.007300000172108412, 0.007799999788403511, 0.008100000210106373, 0.00860000029206276,
				0.00860000029206276, 0.007699999958276749, 0.008500000461935997, 0.008100000210106373, 0.007400000002235174, 0.007799999788403511, 0.008999999612569809,
				0.007300000172108412, 0.007300000172108412, 0.007199999876320362, 0.008200000040233136, 0.00800000037997961, 0.007799999788403511, 0.008999999612569809,
				0.009700000286102295, 0.006899999920278788, 0.00800000037997961, 0.007600000128149986, 0.008200000040233136, 0.005799999926239252, 0.009399999864399434,
				0.008799999952316285, 0.008700000122189522, 0.007300000172108412, 0.0071000000461936, 0.009100000374019146, 0.006800000090152025, 0.00860000029206276,
				0.008799999952316285, 0.00860000029206276, 0.00800000037997961, 0.008100000210106373, 0.007000000216066837, 0.006599999964237213, 0.00860000029206276,
				0.008500000461935997, 0.008500000461935997, 0.008100000210106373, 0.00800000037997961, 0.006800000090152025, 0.007300000172108412, 0.008999999612569809,
				0.007899999618530274, 0.009100000374019146, 0.007799999788403511, 0.007699999958276749, 0.008500000461935997, 0.007799999788403511,
				0.008899999782443047, 0.009499999694526196, 0.009399999864399434, 0.010200000368058682, 0.009499999694526196, 0.00839999970048666,
				0.007899999618530274 };
	}

}