package com.actonia.test;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Properties;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;

import com.actonia.IConstants;
import com.actonia.dao.OwnDomainEntityDAO;
import com.actonia.dao.TargetUrlCrawlAdditionalContentEntityDAO;
import com.actonia.dao.TargetUrlHtmlClickHouseDAO;
import com.actonia.entity.AdditionalContentEntity;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.utils.CommonUtils;
import com.actonia.utils.CrawlerUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.PutMessageUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.CustomData;
import com.actonia.value.object.DecodedEncodedUrlValueObject;
import com.google.gson.Gson;

public class TargetUrlAdditionalContentTest {

	private OwnDomainEntityDAO ownDomainEntityDAO;
	private TargetUrlCrawlAdditionalContentEntityDAO targetUrlCrawlAdditionalContentEntityDAO;
	private static final String TABLE_NAME = null;
	private List<Integer> domainIdList = new ArrayList<Integer>();

	public TargetUrlAdditionalContentTest() {
		super();
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		targetUrlCrawlAdditionalContentEntityDAO = SpringBeanFactory.getBean("targetUrlCrawlAdditionalContentEntityDAO");
	}

	public static void main(String[] args) throws Exception {
		new TargetUrlAdditionalContentTest().process();
	}

	private void process() throws Exception {

		Boolean isExecDomainIdsInd = null;
		Set<Integer> runtimeDomainSet = null;

		Properties domainProperties = new Properties();
		try {
			domainProperties.load(TargetUrlAdditionalContentTest.class.getResourceAsStream("/domain.properties"));
		} catch (Exception e) {
			FormatUtils.getInstance().logMemoryUsage("process() no domain.properties file found");
			return;
		}

		String execDomainIds = domainProperties.getProperty("exec.domain");
		String notExecDomainIds = domainProperties.getProperty("notexec.domain");
		FormatUtils.getInstance().logMemoryUsage("process() execDomainIds=" + execDomainIds);
		FormatUtils.getInstance().logMemoryUsage("process() notExecDomainIds=" + notExecDomainIds);

		List<OwnDomainEntity> allOwnDomainEntityList = ownDomainEntityDAO.queryForAll();
		if (allOwnDomainEntityList == null || allOwnDomainEntityList.size() == 0) {
			FormatUtils.getInstance().logMemoryUsage("process() allOwnDomainEntityList is empty.");
			return;
		}

		// process specific domains
		if (StringUtils.isNotBlank(execDomainIds)) {
			runtimeDomainSet = CommonUtils.getDomainSet(execDomainIds);
			isExecDomainIdsInd = true;
		}
		// do not process specific domains
		else if (StringUtils.isNotBlank(notExecDomainIds)) {
			runtimeDomainSet = CommonUtils.getDomainSet(notExecDomainIds);
			isExecDomainIdsInd = false;
		}
		// process all domains
		else {
			runtimeDomainSet = null;
			isExecDomainIdsInd = null;
		}

		List<OwnDomainEntity> filteredOwnDomainEntityList = PutMessageUtils.getInstance().filterDomains(allOwnDomainEntityList, isExecDomainIdsInd, runtimeDomainSet);
		if (filteredOwnDomainEntityList == null || filteredOwnDomainEntityList.size() == 0) {
			FormatUtils.getInstance().logMemoryUsage("process() filteredOwnDomainEntityList is empty.");
			return;
		}

		// domain level processing
		for (OwnDomainEntity ownDomainEntity : filteredOwnDomainEntityList) {
			processOneDomain(ownDomainEntity.getId());
		}

		for (Integer domainId : domainIdList) {
			System.out.println("process() domainId with custom data available=" + domainId);
		}
	}

	private void processOneDomain(int domainId) throws Exception {
		CustomData[] customDataArray = null;
		String urlString = null;
		String selector = null;
		Long targetUrlCrawlAdditionalContentId = null;
		int totalIdsBySelector = 0;
		int totalIdsByUrlSelector = 0;
		int totalIdsNotDetermined = 0;
		Date crawlTimestamp = null;
		String json = null;
		Gson gson = new Gson();

		List<AdditionalContentEntity> additionalContentEntityList = targetUrlCrawlAdditionalContentEntityDAO.getByDomainId(domainId);
		if (additionalContentEntityList.size() == 0) {
			return;
		}

		System.out.println("processOneDomain() begins. domainId=" + domainId + ",additionalContentEntityList.size()=" + additionalContentEntityList.size());

		List<String> databaseFields = new ArrayList<String>();
		databaseFields.add(IConstants.URL);
		databaseFields.add(IConstants.CRAWL_TIMESTAMP);
		databaseFields.add(IConstants.CUSTOM_DATA);
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = TargetUrlHtmlClickHouseDAO.getInstance().getLatestCustomData(domainId, databaseFields, TABLE_NAME);
		System.out.println("processOneDomain() domainId=" + domainId + ",htmlClickHouseEntityList.size()=" + htmlClickHouseEntityList.size());

		long startTimestamp = System.currentTimeMillis();

		if (htmlClickHouseEntityList != null && htmlClickHouseEntityList.size() > 0) {
			domainIdList.add(domainId);
			for (HtmlClickHouseEntity htmlClickHouseEntity : htmlClickHouseEntityList) {
				urlString = htmlClickHouseEntity.getUrl();
				crawlTimestamp = htmlClickHouseEntity.getCrawlTimestamp();
				customDataArray = htmlClickHouseEntity.getCrawlerResponse().getCustom_data();
				if (customDataArray != null && customDataArray.length > 0) {
					for (CustomData customData : customDataArray) {
						selector = customData.getSelector();
						targetUrlCrawlAdditionalContentId = getTargetUrlCrawlAdditionalContentIdBySelector(selector, additionalContentEntityList);
						if (targetUrlCrawlAdditionalContentId != null) {
							customData.setTarget_url_crawl_additional_content_id(targetUrlCrawlAdditionalContentId);
							totalIdsBySelector++;
						} else {
							targetUrlCrawlAdditionalContentId = getTargetUrlCrawlAdditionalContentIdByUrlSelector(selector, urlString, additionalContentEntityList);
							if (targetUrlCrawlAdditionalContentId != null) {
								customData.setTarget_url_crawl_additional_content_id(targetUrlCrawlAdditionalContentId);
								totalIdsByUrlSelector++;
							} else {
								System.out.println("processOneDomain() domainId=" + domainId + ",crawlTimestamp="
										+ DateFormatUtils.format(crawlTimestamp, IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS) + ",urlString=" + urlString
										+ ",targetUrlCrawlAdditionalContentId cannot be determined.");
								totalIdsNotDetermined++;
							}
						}
					}
					//json = gson.toJson(customDataArray, CustomData[].class);
					//System.out.println("processOneDomain() domainId=" + domainId + ",crawlTimestamp="
					//		+ DateFormatUtils.format(crawlTimestamp, IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS) + ",urlString=" + urlString + ",json=" + json);
				}
			}
		}

		System.out.println("processOneDomain() ends. domainId=" + domainId + ",totalIdsBySelector=" + totalIdsBySelector + ",totalIdsByUrlSelector="
				+ totalIdsByUrlSelector + ",totalIdsNotDetermined=" + totalIdsNotDetermined + ", elapsed(ms.) =" + (System.currentTimeMillis() - startTimestamp));
	}

	private Long getTargetUrlCrawlAdditionalContentIdBySelector(String selector, List<AdditionalContentEntity> additionalContentEntityList) {
		Long targetUrlCrawlAdditionalContentId = null;
		int totalSelectorMatches = 0;
		for (AdditionalContentEntity additionalContentEntity : additionalContentEntityList) {
			if (StringUtils.equalsIgnoreCase(selector, additionalContentEntity.getSelector()) == true) {
				totalSelectorMatches++;
				targetUrlCrawlAdditionalContentId = additionalContentEntity.getId();
			}
		}
		if (totalSelectorMatches == 1) {
			return targetUrlCrawlAdditionalContentId;
		} else {
			return null;
		}
	}

	private Long getTargetUrlCrawlAdditionalContentIdByUrlSelector(String selector, String urlString, List<AdditionalContentEntity> additionalContentEntityList)
			throws Exception {
		Long targetUrlCrawlAdditionalContentId = null;
		boolean isRequiredJavaUrlEncoder = false;
		boolean isUrlSelected = false;
		String trimmedUrlString = StringUtils.trimToEmpty(urlString);
		DecodedEncodedUrlValueObject decodedEncodedUrlValueObject = CrawlerUtils.getInstance().getDecodedAndEncodedUrlString(trimmedUrlString,
				isRequiredJavaUrlEncoder);
		if (decodedEncodedUrlValueObject != null) {
			nextAdditionalContentEntity: for (AdditionalContentEntity additionalContentEntity : additionalContentEntityList) {
				isUrlSelected = CrawlerUtils.getInstance().checkIfUrlSelected(decodedEncodedUrlValueObject.getEncodedUrlString(), additionalContentEntity);
				if (isUrlSelected == true) {
					targetUrlCrawlAdditionalContentId = additionalContentEntity.getId();
					break nextAdditionalContentEntity;
				}
			}
		}
		return targetUrlCrawlAdditionalContentId;
	}
}
