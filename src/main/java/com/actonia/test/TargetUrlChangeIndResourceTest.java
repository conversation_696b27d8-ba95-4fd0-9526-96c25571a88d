package com.actonia.test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.StringUtils;

import com.actonia.IConstants;
import com.actonia.service.AccessTokenService;
import com.actonia.service.PoliteCrawlWebServiceClientService;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.ResponseCodeFilter;
import com.actonia.value.object.TargetUrlChangeContentType;
import com.actonia.value.object.TargetUrlChangeRequest;
import com.actonia.value.object.TargetUrlChangeResponse;
import com.actonia.value.object.UrlFilter;
import com.google.gson.Gson;

public class TargetUrlChangeIndResourceTest {

	private PoliteCrawlWebServiceClientService politeCrawlWebServiceClientService;

	private String endpointPrefix = null;

	private static final String PRODUCTION_ENDPOINT_PREFIX = "https://api.seoclarity.net/seoClarity";
	private static final String PRODUCTION_CLOUD_ENDPOINT_PREFIX = "http://api-internal-lb-190333-dal13.clb.appdomain.cloud/seoClarity";

	public TargetUrlChangeIndResourceTest() {
		super();
		this.politeCrawlWebServiceClientService = SpringBeanFactory.getBean("politeCrawlWebServiceClientService");
	}

	public static void main(String[] args) {
		try {
			new TargetUrlChangeIndResourceTest().runTests();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void runTests() throws Exception {
		endpointPrefix = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint();
		//endpointPrefix = PRODUCTION_CLOUD_ENDPOINT_PREFIX;
		//		testCase1();
		//		testCase2();
		//		testCase3();
		//		testCase4();
		//		testCase5();
		//		testCase6();
		//		testCase7();
		//		testCase8();
		//		testCase9();
		//		testCase10();
		//		testCase11();
		//		testCase12();
		//		testCase13();
		//		testCase14();
		//		testCase15();
		//		testCase16();
		//		testCase17();
		//		testCase18();
		//		testCase19();
		//		testCase20();
		//		testCase21();
		//		testCase22();
		//		testCase23();
		//		testCase24();
		//		testCase25();
		//		testCase26();
		//		testCase27();
		//		testCase28();
		//		testCase30();
		//		testCase31();
		//		testCase32();
		//		testCase33();
		//		testCase34();
		//		testCase35();
		//		testCase36();
		//		testCase37();
		//		testCase38();
		//		testCase39();
		//		testCase40();
		//		testCase41();
		//		testCase42();
		//		testCase43();
		//		testCase44();
		//		testCase45();
		//		testCase46();
		//		testCase47();
		//		testCase48();
		//testCase49();
		//		testCase50();
		//		testCase51();
		//		testCase52();
		//		testCase53();
		//		testCase54();
		//		testCase55();
		//		testCase56();
		//		testCase57();
		//		testCase58();
		//testCase59();
		//		testCase60();
		//testCase61();
		//testCase62();
		//testCase63();
		testCase64();
		//testCase65();
		//testCase66();
		//testCase67();
		//testCase68();
		//testCase69();
		//testCase70();
		//testCase71();
		//testCase72();
		//testCase73();
		//testCase74();
	}

	private void testCase1() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase1() requestUrl=" + requestUrl);
		String requestParameters = IConstants.EMPTY_STRING;
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase1() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		String json = new Gson().toJson(targetUrlChangeResponse, TargetUrlChangeResponse.class);
		assertNotNull("testCase1() targetUrlChangeResponse.getError() should not be null.", targetUrlChangeResponse.getError());
		if (StringUtils.containsIgnoreCase(requestUrl, "://api")) {
			assertEquals("testCase1() json is incorrect",
					"{\"success\":false,\"error\":{\"error_code\":\"PCWS-000017\",\"error_message\":\"Request JSON is required.\"}}", json);
		} else {
			assertEquals("testCase1() json is incorrect", "{\"success\":false,\"error\":{\"error_code\":\"00001\",\"error_message\":\"Request JSON is required.\"}}",
					json);
		}
		FormatUtils.getInstance().logMemoryUsage("testCase1() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase2() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase2() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase2() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase2() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		String json = new Gson().toJson(targetUrlChangeResponse, TargetUrlChangeResponse.class);
		assertNotNull("testCase2() targetUrlChangeResponse.getError() should not be null.", targetUrlChangeResponse.getError());
		if (StringUtils.containsIgnoreCase(requestUrl, "://api")) {
			assertEquals("testCase1() json is incorrect",
					"{\"success\":false,\"error\":{\"error_code\":\"PCWS-000034\",\"error_message\":\"Request parameter access_token is required.\"}}", json);
		} else {
			assertEquals("testCase2() json is incorrect",
					"{\"success\":false,\"error\":{\"error_code\":\"00006\",\"error_message\":\"Request parameter access_token is required.\"}}", json);
		}
		FormatUtils.getInstance().logMemoryUsage("testCase2() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase3() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase3() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token("testaccesstoken");
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase3() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase3() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		String json = new Gson().toJson(targetUrlChangeResponse, TargetUrlChangeResponse.class);
		assertNotNull("testCase3() targetUrlChangeResponse.getError() should not be null.", targetUrlChangeResponse.getError());
		if (StringUtils.containsIgnoreCase(requestUrl, "://api")) {
			assertEquals("testCase3() json is incorrect",
					"{\"success\":false,\"error\":{\"error_code\":\"PCWS-000033\",\"error_message\":\"Request parameter access_token testaccesstoken is invalid.\"}}",
					json);
		} else {
			assertEquals("testCase3() json is incorrect",
					"{\"success\":false,\"error\":{\"error_code\":\"00002\",\"error_message\":\"Request parameter access_token testaccesstoken is invalid.\"}}", json);
		}
		FormatUtils.getInstance().logMemoryUsage("testCase3() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase4() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + "test";
		System.out.println("testCase4() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase4() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase4() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		String json = new Gson().toJson(targetUrlChangeResponse, TargetUrlChangeResponse.class);
		assertNotNull("testCase4() targetUrlChangeResponse.getError() should not be null.", targetUrlChangeResponse.getError());
		assertEquals("testCase4() json is incorrect", "{\"success\":false,\"error\":{\"error_code\":\"00004\",\"error_message\":\"Request command test is invalid.\"}}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase4() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase5() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase5() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase5() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase5() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		String json = new Gson().toJson(targetUrlChangeResponse, TargetUrlChangeResponse.class);
		assertNotNull("testCase5() targetUrlChangeResponse.getError() should not be null.", targetUrlChangeResponse.getError());
		assertEquals("testCase5() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00005\",\"error_message\":\"Request parameter domain_id is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase5() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase6() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase6() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase6() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase6() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		String json = new Gson().toJson(targetUrlChangeResponse, TargetUrlChangeResponse.class);
		assertNotNull("testCase6() targetUrlChangeResponse.getError() should not be null.", targetUrlChangeResponse.getError());
		assertEquals("testCase6() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00007\",\"error_message\":\"Request parameter start_crawl_timestamp is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase6() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase7() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase7() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("12-32-2022 10:11:12");
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase7() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase7() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		String json = new Gson().toJson(targetUrlChangeResponse, TargetUrlChangeResponse.class);
		assertNotNull("testCase7() targetUrlChangeResponse.getError() should not be null.", targetUrlChangeResponse.getError());
		assertEquals("testCase7() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00008\",\"error_message\":\"Request parameter start_crawl_timestamp 12-32-2022 10:11:12 is invalid.\"}}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase7() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase8() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase8() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 01:02:03");
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase8() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase8() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		String json = new Gson().toJson(targetUrlChangeResponse, TargetUrlChangeResponse.class);
		assertNotNull("testCase8() targetUrlChangeResponse.getError() should not be null.", targetUrlChangeResponse.getError());
		assertEquals("testCase8() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00009\",\"error_message\":\"Request parameter end_crawl_timestamp is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase8() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase9() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase9() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 01:02:03");
		targetUrlChangeRequest.setEnd_crawl_timestamp("06-08-2022 01:02:03");
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase9() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase9() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		String json = new Gson().toJson(targetUrlChangeResponse, TargetUrlChangeResponse.class);
		assertNotNull("testCase9() targetUrlChangeResponse.getError() should not be null.", targetUrlChangeResponse.getError());
		assertEquals("testCase9() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00010\",\"error_message\":\"Request parameter end_crawl_timestamp 06-08-2022 01:02:03 is invalid.\"}}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase9() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase10() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase10() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:01");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-20 00:00:00");
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase10() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase10() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		String json = new Gson().toJson(targetUrlChangeResponse, TargetUrlChangeResponse.class);
		assertNotNull("testCase10() targetUrlChangeResponse.getError() should not be null.", targetUrlChangeResponse.getError());
		assertEquals("testCase10() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00011\",\"error_message\":\"Request parameter start_crawl_timestamp cannot be later end_crawl_timestamp.\"}}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase10() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase11() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase11() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-20 23:59:59");
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase11() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase11() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		String json = new Gson().toJson(targetUrlChangeResponse, TargetUrlChangeResponse.class);
		assertNotNull("testCase11() targetUrlChangeResponse.getError() should not be null.", targetUrlChangeResponse.getError());
		assertEquals("testCase11() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00012\",\"error_message\":\"Request parameter page_number is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase11() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase12() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase12() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-20 23:59:59");
		targetUrlChangeRequest.setPage_number(0);
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase12() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase12() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		String json = new Gson().toJson(targetUrlChangeResponse, TargetUrlChangeResponse.class);
		assertNotNull("testCase12() targetUrlChangeResponse.getError() should not be null.", targetUrlChangeResponse.getError());
		assertEquals("testCase12() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00013\",\"error_message\":\"Request parameter page_number 0 is invalid.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase12() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase13() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase13() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-20 23:59:59");
		targetUrlChangeRequest.setPage_number(1);
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase13() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase13() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		String json = new Gson().toJson(targetUrlChangeResponse, TargetUrlChangeResponse.class);
		assertNotNull("testCase13() targetUrlChangeResponse.getError() should not be null.", targetUrlChangeResponse.getError());
		assertEquals("testCase13() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00014\",\"error_message\":\"Request parameter rows_per_page is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase13() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase14() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase14() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-20 23:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(0);
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase14() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase14() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		String json = new Gson().toJson(targetUrlChangeResponse, TargetUrlChangeResponse.class);
		assertNotNull("testCase14() targetUrlChangeResponse.getError() should not be null.", targetUrlChangeResponse.getError());
		assertEquals("testCase14() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00015\",\"error_message\":\"Request parameter rows_per_page 0 is invalid.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase14() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase15() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase15() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-20 23:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase15() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase15() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		String json = new Gson().toJson(targetUrlChangeResponse, TargetUrlChangeResponse.class);
		assertNotNull("testCase15() targetUrlChangeResponse.getError() should not be null.", targetUrlChangeResponse.getError());
		assertEquals("testCase15() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00016\",\"error_message\":\"Request parameter sort_by is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase15() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase16() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase16() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-20 23:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(0);
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase16() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase16() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		String json = new Gson().toJson(targetUrlChangeResponse, TargetUrlChangeResponse.class);
		assertNotNull("testCase16() targetUrlChangeResponse.getError() should not be null.", targetUrlChangeResponse.getError());
		assertEquals("testCase16() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00017\",\"error_message\":\"Request parameter sort_by 0 is invalid.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase16() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase17() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase17() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-20 23:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);

		String[] changeIndicators = new String[] { "test_change_indicator_1" };
		targetUrlChangeRequest.setChange_indicators(changeIndicators);

		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase17() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase17() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		String json = new Gson().toJson(targetUrlChangeResponse, TargetUrlChangeResponse.class);
		assertNotNull("testCase17() targetUrlChangeResponse.getError() should not be null.", targetUrlChangeResponse.getError());
		assertEquals("testCase17() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00018\",\"error_message\":\"Request parameter change_indicators [test_change_indicator_1] is invalid.\"}}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase17() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase18() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase18() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-20 23:59:59");
		targetUrlChangeRequest.setPage_number(17598);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);

		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase18() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase18() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		String json = new Gson().toJson(targetUrlChangeResponse, TargetUrlChangeResponse.class);
		assertNotNull("testCase18() targetUrlChangeResponse.getError() should not be null.", targetUrlChangeResponse.getError());
		assertEquals("testCase18() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00019\",\"error_message\":\"Request parameter page_number and row_per_page is invalid. There are only 175970 rows.\"}}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase18() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase19() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase19() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-20 23:59:59");
		targetUrlChangeRequest.setPage_number(17597);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);

		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase19() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase19() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertTrue("testCase19() targetUrlChangeResponse.getEnd_of_detail_list_flag() should be true.", targetUrlChangeResponse.getEnd_of_detail_list_flag());
		FormatUtils.getInstance().logMemoryUsage("testCase19() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase20() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase20() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-20 23:59:59");
		targetUrlChangeRequest.setPage_number(17596);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);
		//targetUrlChangeRequest.setDebug_ind(true);

		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase20() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase20() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertFalse("testCase20() targetUrlChangeResponse.getEnd_of_detail_list_flag() should be false.", targetUrlChangeResponse.getEnd_of_detail_list_flag());
		assertEquals("testCase20() targetUrlChangeResponse.getChange_indicator_list().size() incorrect.", "10",
				String.valueOf(targetUrlChangeResponse.getChange_indicator_list().size()));
		FormatUtils.getInstance().logMemoryUsage("testCase20() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase21() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase21() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-20 23:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_URL_ASC);
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase21() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase21() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertFalse("testCase21() targetUrlChangeResponse.getEnd_of_detail_list_flag() should be false.", targetUrlChangeResponse.getEnd_of_detail_list_flag());
		assertEquals("testCase21() targetUrlChangeResponse.getChange_indicator_list().size() incorrect.", "10",
				String.valueOf(targetUrlChangeResponse.getChange_indicator_list().size()));
		//assertEquals("testCase21() targetUrlChangeResponse.getChange_indicator_list().get(0).getUrl() incorrect.", "https://at.indeed.com/Verkäuferin-Jobs-Knittelfeld,-ST",
		//		targetUrlChangeResponse.getChange_indicator_list().get(0).getUrl());
		FormatUtils.getInstance().logMemoryUsage("testCase21() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase22() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase22() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-20 23:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_URL_DESC);
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase22() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase22() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertFalse("testCase22() targetUrlChangeResponse.getEnd_of_detail_list_flag() should be false.", targetUrlChangeResponse.getEnd_of_detail_list_flag());
		assertEquals("testCase22() targetUrlChangeResponse.getChange_indicator_list().size() incorrect.", "10",
				String.valueOf(targetUrlChangeResponse.getChange_indicator_list().size()));
		//assertEquals("testCase22() targetUrlChangeResponse.getChange_indicator_list().get(0).getUrl() incorrect.",
		//		"https://www.indeed.com/salaries/Police-Officer-Salaries-at-Suffolk-County-Police-Department,-New-York-State",
		//		targetUrlChangeResponse.getChange_indicator_list().get(0).getUrl());
		FormatUtils.getInstance().logMemoryUsage("testCase22() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase23() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase23() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-20 23:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_ASC);
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase22() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase23() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertFalse("testCase23() targetUrlChangeResponse.getEnd_of_detail_list_flag() should be false.", targetUrlChangeResponse.getEnd_of_detail_list_flag());
		assertEquals("testCase23() targetUrlChangeResponse.getChange_indicator_list().size() incorrect.", "10",
				String.valueOf(targetUrlChangeResponse.getChange_indicator_list().size()));
		//assertEquals("testCase23() targetUrlChangeResponse.getChange_indicator_list().get(0).getCurrent_crawl_timestamp() incorrect.", "2022-07-20 00:00:13",
		//		targetUrlChangeResponse.getChange_indicator_list().get(0).getCurrent_crawl_timestamp());
		FormatUtils.getInstance().logMemoryUsage("testCase23() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase24() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase24() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-20 23:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase22() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase24() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertFalse("testCase24() targetUrlChangeResponse.getEnd_of_detail_list_flag() should be false.", targetUrlChangeResponse.getEnd_of_detail_list_flag());
		assertEquals("testCase24() targetUrlChangeResponse.getChange_indicator_list().size() incorrect.", "10",
				String.valueOf(targetUrlChangeResponse.getChange_indicator_list().size()));
		//assertEquals("testCase24() targetUrlChangeResponse.getTotal_rows() incorrect.", "175970", String.valueOf(targetUrlChangeResponse.getTotal_rows()));
		//assertEquals("testCase24() targetUrlChangeResponse.getChange_indicator_list().get(0).getCurrent_crawl_timestamp() incorrect.", "2022-07-20 00:04:53",
		//		targetUrlChangeResponse.getChange_indicator_list().get(0).getCurrent_crawl_timestamp());
		FormatUtils.getInstance().logMemoryUsage("testCase24() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase25() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase25() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-20 23:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);
		targetUrlChangeRequest
				.setPage_tag_ids(new Integer[] { 6229331, 1370808, 1365655, 1357660, 1365654, 1215835, 1306297, 1365656, 1377844, 1374728, 7010425, 7010427 });

		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase22() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase25() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertFalse("testCase25() targetUrlChangeResponse.getEnd_of_detail_list_flag() should be false.", targetUrlChangeResponse.getEnd_of_detail_list_flag());
		assertEquals("testCase25() targetUrlChangeResponse.getChange_indicator_list().size() incorrect.", "10",
				String.valueOf(targetUrlChangeResponse.getChange_indicator_list().size()));
		assertEquals("testCase25() targetUrlChangeResponse.getTotal_rows() incorrect.", "64934", String.valueOf(targetUrlChangeResponse.getTotal_rows()));
		//assertEquals("testCase25() targetUrlChangeResponse.getChange_indicator_list().get(0).getCurrent_crawl_timestamp() incorrect.", "2022-07-20 23:59:53",
		//		targetUrlChangeResponse.getChange_indicator_list().get(0).getCurrent_crawl_timestamp());
		FormatUtils.getInstance().logMemoryUsage("testCase25() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase26() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase26() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 23:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-20 23:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(14);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);
		targetUrlChangeRequest
				.setPage_tag_ids(new Integer[] { 6229331, 1370808, 1365655, 1357660, 1365654, 1215835, 1306297, 1365656, 1377844, 1374728, 7010425, 7010427 });
		targetUrlChangeRequest.setChange_indicators(new String[] { "h1_chg_ind", "og_markup_chg_ind" });
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase22() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase26() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertFalse("testCase26() targetUrlChangeResponse.getEnd_of_detail_list_flag() should be false.", targetUrlChangeResponse.getEnd_of_detail_list_flag());
		assertEquals("testCase26() targetUrlChangeResponse.getChange_indicator_list().size() incorrect.", "14",
				String.valueOf(targetUrlChangeResponse.getChange_indicator_list().size()));
		assertEquals("testCase26() targetUrlChangeResponse.getTotal_rows() incorrect.", "15", String.valueOf(targetUrlChangeResponse.getTotal_rows()));
		//assertEquals("testCase26() targetUrlChangeResponse.getChange_indicator_list().get(0).getCurrent_crawl_timestamp() incorrect.", "2022-07-20 18:49:29",
		//		targetUrlChangeResponse.getChange_indicator_list().get(0).getCurrent_crawl_timestamp());
		FormatUtils.getInstance().logMemoryUsage("testCase26() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase27() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase27() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 23:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-20 23:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(15);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);
		targetUrlChangeRequest
				.setPage_tag_ids(new Integer[] { 6229331, 1370808, 1365655, 1357660, 1365654, 1215835, 1306297, 1365656, 1377844, 1374728, 7010425, 7010427 });
		targetUrlChangeRequest.setChange_indicators(new String[] { "h1_chg_ind", "og_markup_chg_ind" });
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase22() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase27() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertTrue("testCase27() targetUrlChangeResponse.getEnd_of_detail_list_flag() should be true.", targetUrlChangeResponse.getEnd_of_detail_list_flag());
		assertEquals("testCase27() targetUrlChangeResponse.getChange_indicator_list().size() incorrect.", "15",
				String.valueOf(targetUrlChangeResponse.getChange_indicator_list().size()));
		assertEquals("testCase27() targetUrlChangeResponse.getTotal_rows() incorrect.", "15", String.valueOf(targetUrlChangeResponse.getTotal_rows()));
		//assertEquals("testCase27() targetUrlChangeResponse.getChange_indicator_list().get(0).getCurrent_crawl_timestamp() incorrect.", "2022-07-20 18:49:29",
		//		targetUrlChangeResponse.getChange_indicator_list().get(0).getCurrent_crawl_timestamp());
		FormatUtils.getInstance().logMemoryUsage("testCase27() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase28() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase28() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 23:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-20 23:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(16);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);
		targetUrlChangeRequest
				.setPage_tag_ids(new Integer[] { 6229331, 1370808, 1365655, 1357660, 1365654, 1215835, 1306297, 1365656, 1377844, 1374728, 7010425, 7010427 });
		targetUrlChangeRequest.setChange_indicators(new String[] { "h1_chg_ind", "og_markup_chg_ind" });
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase22() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase28() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertTrue("testCase28() targetUrlChangeResponse.getEnd_of_detail_list_flag() should be true.", targetUrlChangeResponse.getEnd_of_detail_list_flag());
		assertEquals("testCase28() targetUrlChangeResponse.getChange_indicator_list().size() incorrect.", "15",
				String.valueOf(targetUrlChangeResponse.getChange_indicator_list().size()));
		assertEquals("testCase28() targetUrlChangeResponse.getTotal_rows() incorrect.", "15", String.valueOf(targetUrlChangeResponse.getTotal_rows()));
		//assertEquals("testCase28() targetUrlChangeResponse.getChange_indicator_list().get(0).getCurrent_crawl_timestamp() incorrect.", "2022-07-20 18:49:29",
		//		targetUrlChangeResponse.getChange_indicator_list().get(0).getCurrent_crawl_timestamp());
		FormatUtils.getInstance().logMemoryUsage("testCase28() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase30() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase30() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 23:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-20 23:59:59");
		targetUrlChangeRequest.setPage_number(2);
		targetUrlChangeRequest.setRows_per_page(8);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);
		targetUrlChangeRequest
				.setPage_tag_ids(new Integer[] { 6229331, 1370808, 1365655, 1357660, 1365654, 1215835, 1306297, 1365656, 1377844, 1374728, 7010425, 7010427 });
		targetUrlChangeRequest.setChange_indicators(new String[] { "h1_chg_ind", "og_markup_chg_ind" });
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase22() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase30() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertTrue("testCase30() targetUrlChangeResponse.getEnd_of_detail_list_flag() should be true.", targetUrlChangeResponse.getEnd_of_detail_list_flag());
		assertEquals("testCase30() targetUrlChangeResponse.getChange_indicator_list().size() incorrect.", "7",
				String.valueOf(targetUrlChangeResponse.getChange_indicator_list().size()));
		assertEquals("testCase30() targetUrlChangeResponse.getTotal_rows() incorrect.", "15", String.valueOf(targetUrlChangeResponse.getTotal_rows()));
		//assertEquals("testCase30() targetUrlChangeResponse.getChange_indicator_list().get(0).getCurrent_crawl_timestamp() incorrect.", "2022-07-20 12:24:26",
		//		targetUrlChangeResponse.getChange_indicator_list().get(0).getCurrent_crawl_timestamp());
		FormatUtils.getInstance().logMemoryUsage("testCase30() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase31() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase31() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 23:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-20 23:59:59");
		targetUrlChangeRequest.setPage_number(3);
		targetUrlChangeRequest.setRows_per_page(8);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);
		targetUrlChangeRequest
				.setPage_tag_ids(new Integer[] { 6229331, 1370808, 1365655, 1357660, 1365654, 1215835, 1306297, 1365656, 1377844, 1374728, 7010425, 7010427 });
		targetUrlChangeRequest.setChange_indicators(new String[] { "h1_chg_ind", "og_markup_chg_ind" });
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase22() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase31() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		String json = new Gson().toJson(targetUrlChangeResponse, TargetUrlChangeResponse.class);
		assertNotNull("testCase31() targetUrlChangeResponse.getError() should not be null.", targetUrlChangeResponse.getError());
		assertEquals("testCase31() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00019\",\"error_message\":\"Request parameter page_number and row_per_page is invalid. There are only 15 rows.\"}}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase31() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase32() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase32() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-20 23:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);
		targetUrlChangeRequest.setPage_tag_ids(new Integer[] { 1234, 4567 });
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase22() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase32() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		String json = new Gson().toJson(targetUrlChangeResponse, TargetUrlChangeResponse.class);
		assertNotNull("testCase32() targetUrlChangeResponse.getError() should not be null.", targetUrlChangeResponse.getError());
		assertEquals("testCase32() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00020\",\"error_message\":\"Data not available for the search criteria.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase32() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase33() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase33() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-20 23:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);

		TargetUrlChangeContentType targetUrlChangeContentType = new TargetUrlChangeContentType();
		targetUrlChangeContentType.setLeaf(true);
		targetUrlChangeContentType.setAction("ct");
		targetUrlChangeContentType.setValue("indeed.com/career-advice");
		targetUrlChangeContentType.setLevel(0);
		targetUrlChangeContentType.setCond("and");
		targetUrlChangeContentType.setItems(new TargetUrlChangeContentType[0]);
		targetUrlChangeRequest.setContent_types(new TargetUrlChangeContentType[] { targetUrlChangeContentType });

		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase22() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase33() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertFalse("testCase33() targetUrlChangeResponse.getEnd_of_detail_list_flag() should be false.", targetUrlChangeResponse.getEnd_of_detail_list_flag());
		assertEquals("testCase33() targetUrlChangeResponse.getChange_indicator_list().size() incorrect.", "10",
				String.valueOf(targetUrlChangeResponse.getChange_indicator_list().size()));
		assertEquals("testCase33() targetUrlChangeResponse.getTotal_rows() incorrect.", "20660", String.valueOf(targetUrlChangeResponse.getTotal_rows()));
		//assertEquals("testCase33() targetUrlChangeResponse.getChange_indicator_list().get(0).getUrl() incorrect.",
		//		"https://www.indeed.com/career-advice/starting-new-job/what-to-do-when-you-lose-your-job-at-50",
		//		targetUrlChangeResponse.getChange_indicator_list().get(0).getUrl());
		//assertEquals("testCase33() targetUrlChangeResponse.getChange_indicator_list().get(0).getCurrent_crawl_timestamp() incorrect.", "2022-07-20 23:59:34",
		//		targetUrlChangeResponse.getChange_indicator_list().get(0).getCurrent_crawl_timestamp());
		FormatUtils.getInstance().logMemoryUsage("testCase33() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase34() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase34() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-20 23:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);

		List<TargetUrlChangeContentType> targetUrlChangeContentTypeList = new ArrayList<TargetUrlChangeContentType>();

		TargetUrlChangeContentType targetUrlChangeContentType = null;

		// first content type
		targetUrlChangeContentType = new TargetUrlChangeContentType();
		targetUrlChangeContentType.setLeaf(true);
		targetUrlChangeContentType.setAction("ct");
		targetUrlChangeContentType.setValue("career-advice");
		targetUrlChangeContentType.setLevel(0);
		targetUrlChangeContentType.setCond("and");
		targetUrlChangeContentType.setItems(new TargetUrlChangeContentType[0]);
		targetUrlChangeContentTypeList.add(targetUrlChangeContentType);

		// second content type
		targetUrlChangeContentType = new TargetUrlChangeContentType();
		targetUrlChangeContentType.setLeaf(true);
		targetUrlChangeContentType.setAction("ct");
		targetUrlChangeContentType.setValue("career-development");
		targetUrlChangeContentType.setLevel(0);
		targetUrlChangeContentType.setCond("and");
		targetUrlChangeContentType.setItems(new TargetUrlChangeContentType[0]);
		targetUrlChangeContentTypeList.add(targetUrlChangeContentType);

		targetUrlChangeRequest.setContent_types(targetUrlChangeContentTypeList.toArray(new TargetUrlChangeContentType[0]));

		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase22() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase34() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertFalse("testCase34() targetUrlChangeResponse.getEnd_of_detail_list_flag() should be false.", targetUrlChangeResponse.getEnd_of_detail_list_flag());
		assertEquals("testCase34() targetUrlChangeResponse.getChange_indicator_list().size() incorrect.", "10",
				String.valueOf(targetUrlChangeResponse.getChange_indicator_list().size()));
		assertEquals("testCase34() targetUrlChangeResponse.getTotal_rows() incorrect.", "21554", String.valueOf(targetUrlChangeResponse.getTotal_rows()));
		//assertEquals("testCase34() targetUrlChangeResponse.getChange_indicator_list().get(0).getUrl() incorrect.", "https://www.indeed.com/career/dietitian/career-advice",
		//		targetUrlChangeResponse.getChange_indicator_list().get(0).getUrl());
		//assertEquals("testCase34() targetUrlChangeResponse.getChange_indicator_list().get(0).getCurrent_crawl_timestamp() incorrect.", "2022-07-20 23:59:50",
		//		targetUrlChangeResponse.getChange_indicator_list().get(0).getCurrent_crawl_timestamp());
		FormatUtils.getInstance().logMemoryUsage("testCase34() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase35() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase35() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-20 23:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);

		List<TargetUrlChangeContentType> targetUrlChangeContentTypeList = new ArrayList<TargetUrlChangeContentType>();

		TargetUrlChangeContentType targetUrlChangeContentType = null;

		// first content type
		targetUrlChangeContentType = new TargetUrlChangeContentType();
		targetUrlChangeContentType.setLeaf(true);
		targetUrlChangeContentType.setAction("ct");
		targetUrlChangeContentType.setValue("career-advice");
		targetUrlChangeContentType.setCond("and");
		targetUrlChangeContentType.setItems(new TargetUrlChangeContentType[0]);
		targetUrlChangeContentTypeList.add(targetUrlChangeContentType);

		// second content type
		targetUrlChangeContentType = new TargetUrlChangeContentType();
		targetUrlChangeContentType.setLeaf(true);
		targetUrlChangeContentType.setAction("ct");
		targetUrlChangeContentType.setValue("career-development");
		targetUrlChangeContentType.setCond("and");
		targetUrlChangeContentType.setItems(new TargetUrlChangeContentType[0]);
		targetUrlChangeContentTypeList.add(targetUrlChangeContentType);

		targetUrlChangeRequest.setContent_types(targetUrlChangeContentTypeList.toArray(new TargetUrlChangeContentType[0]));

		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase22() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase35() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		String json = new Gson().toJson(targetUrlChangeResponse, TargetUrlChangeResponse.class);
		assertNotNull("testCase35() targetUrlChangeResponse.getError() should not be null.", targetUrlChangeResponse.getError());
		assertEquals("testCase33() targetUrlChangeResponse.getError().getError_code() incorrect.", "00021", targetUrlChangeResponse.getError().getError_code());
		FormatUtils.getInstance().logMemoryUsage("testCase35() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase36() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase36() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-20 23:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_CHANGE_INDICATOR_ASC);
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase22() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase36() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertFalse("testCase36() targetUrlChangeResponse.getEnd_of_detail_list_flag() should be false.", targetUrlChangeResponse.getEnd_of_detail_list_flag());
		assertEquals("testCase36() targetUrlChangeResponse.getChange_indicator_list().size() incorrect.", "10",
				String.valueOf(targetUrlChangeResponse.getChange_indicator_list().size()));
		//assertEquals("testCase36() targetUrlChangeResponse.getTotal_rows() incorrect.", "175970", String.valueOf(targetUrlChangeResponse.getTotal_rows()));
		//assertEquals("testCase36() targetUrlChangeResponse.getChange_indicator_list().get(0).getCurrent_crawl_timestamp() incorrect.", "2022-07-20 00:04:53",
		//		targetUrlChangeResponse.getChange_indicator_list().get(0).getCurrent_crawl_timestamp());
		FormatUtils.getInstance().logMemoryUsage("testCase36() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase37() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase37() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-20 23:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_CHANGE_INDICATOR_DESC);
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase22() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase37() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertFalse("testCase37() targetUrlChangeResponse.getEnd_of_detail_list_flag() should be false.", targetUrlChangeResponse.getEnd_of_detail_list_flag());
		assertEquals("testCase37() targetUrlChangeResponse.getChange_indicator_list().size() incorrect.", "10",
				String.valueOf(targetUrlChangeResponse.getChange_indicator_list().size()));
		//assertEquals("testCase37() targetUrlChangeResponse.getTotal_rows() incorrect.", "175970", String.valueOf(targetUrlChangeResponse.getTotal_rows()));
		//assertEquals("testCase37() targetUrlChangeResponse.getChange_indicator_list().get(0).getCurrent_crawl_timestamp() incorrect.", "2022-07-20 00:04:53",
		//		targetUrlChangeResponse.getChange_indicator_list().get(0).getCurrent_crawl_timestamp());
		FormatUtils.getInstance().logMemoryUsage("testCase37() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase38() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase38() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-20 23:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_CHANGE_TYPE_ASC);
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase22() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase38() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertFalse("testCase38() targetUrlChangeResponse.getEnd_of_detail_list_flag() should be false.", targetUrlChangeResponse.getEnd_of_detail_list_flag());
		assertEquals("testCase38() targetUrlChangeResponse.getChange_indicator_list().size() incorrect.", "10",
				String.valueOf(targetUrlChangeResponse.getChange_indicator_list().size()));
		//assertEquals("testCase38() targetUrlChangeResponse.getTotal_rows() incorrect.", "175970", String.valueOf(targetUrlChangeResponse.getTotal_rows()));
		//assertEquals("testCase38() targetUrlChangeResponse.getChange_indicator_list().get(0).getCurrent_crawl_timestamp() incorrect.", "2022-07-20 00:04:53",
		//		targetUrlChangeResponse.getChange_indicator_list().get(0).getCurrent_crawl_timestamp());
		FormatUtils.getInstance().logMemoryUsage("testCase38() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase39() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase39() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-20 23:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_CHANGE_TYPE_DESC);
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase22() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase39() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertFalse("testCase39() targetUrlChangeResponse.getEnd_of_detail_list_flag() should be false.", targetUrlChangeResponse.getEnd_of_detail_list_flag());
		assertEquals("testCase39() targetUrlChangeResponse.getChange_indicator_list().size() incorrect.", "10",
				String.valueOf(targetUrlChangeResponse.getChange_indicator_list().size()));
		//assertEquals("testCase39() targetUrlChangeResponse.getTotal_rows() incorrect.", "175970", String.valueOf(targetUrlChangeResponse.getTotal_rows()));
		//assertEquals("testCase39() targetUrlChangeResponse.getChange_indicator_list().get(0).getCurrent_crawl_timestamp() incorrect.", "2022-07-20 00:04:53",
		//		targetUrlChangeResponse.getChange_indicator_list().get(0).getCurrent_crawl_timestamp());
		FormatUtils.getInstance().logMemoryUsage("testCase39() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase40() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase40() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-20 23:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_SEVERITY_ASC);
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase22() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase40() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertFalse("testCase40() targetUrlChangeResponse.getEnd_of_detail_list_flag() should be false.", targetUrlChangeResponse.getEnd_of_detail_list_flag());
		assertEquals("testCase40() targetUrlChangeResponse.getChange_indicator_list().size() incorrect.", "10",
				String.valueOf(targetUrlChangeResponse.getChange_indicator_list().size()));
		//assertEquals("testCase40() targetUrlChangeResponse.getTotal_rows() incorrect.", "175970", String.valueOf(targetUrlChangeResponse.getTotal_rows()));
		//assertEquals("testCase40() targetUrlChangeResponse.getChange_indicator_list().get(0).getCurrent_crawl_timestamp() incorrect.", "2022-07-20 00:04:53",
		//		targetUrlChangeResponse.getChange_indicator_list().get(0).getCurrent_crawl_timestamp());
		FormatUtils.getInstance().logMemoryUsage("testCase40() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase41() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase41() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-20 23:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_SEVERITY_DESC);
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase22() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase41() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertFalse("testCase41() targetUrlChangeResponse.getEnd_of_detail_list_flag() should be false.", targetUrlChangeResponse.getEnd_of_detail_list_flag());
		assertEquals("testCase41() targetUrlChangeResponse.getChange_indicator_list().size() incorrect.", "10",
				String.valueOf(targetUrlChangeResponse.getChange_indicator_list().size()));
		//assertEquals("testCase41() targetUrlChangeResponse.getTotal_rows() incorrect.", "175970", String.valueOf(targetUrlChangeResponse.getTotal_rows()));
		//assertEquals("testCase41() targetUrlChangeResponse.getChange_indicator_list().get(0).getCurrent_crawl_timestamp() incorrect.", "2022-07-20 00:04:53",
		//		targetUrlChangeResponse.getChange_indicator_list().get(0).getCurrent_crawl_timestamp());
		FormatUtils.getInstance().logMemoryUsage("testCase41() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase42() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase42() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-20 23:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);
		targetUrlChangeRequest
				.setPage_tag_ids(new Integer[] { 6229331, 1370808, 1365655, 1357660, 1365654, 1215835, 1306297, 1365656, 1377844, 1374728, 7010425, 7010427 });
		targetUrlChangeRequest.setChange_indicators(new String[] { "h1_chg_ind", "og_markup_chg_ind" });

		List<TargetUrlChangeContentType> targetUrlChangeContentTypeList = new ArrayList<TargetUrlChangeContentType>();

		TargetUrlChangeContentType targetUrlChangeContentType = null;

		// first content type
		targetUrlChangeContentType = new TargetUrlChangeContentType();
		targetUrlChangeContentType.setLeaf(true);
		targetUrlChangeContentType.setAction("ct");
		targetUrlChangeContentType.setValue("career-advice");
		targetUrlChangeContentType.setLevel(0);
		targetUrlChangeContentType.setCond("and");
		targetUrlChangeContentType.setItems(new TargetUrlChangeContentType[0]);
		targetUrlChangeContentTypeList.add(targetUrlChangeContentType);

		// second content type
		targetUrlChangeContentType = new TargetUrlChangeContentType();
		targetUrlChangeContentType.setLeaf(true);
		targetUrlChangeContentType.setAction("ct");
		targetUrlChangeContentType.setValue("career-development");
		targetUrlChangeContentType.setLevel(0);
		targetUrlChangeContentType.setCond("and");
		targetUrlChangeContentType.setItems(new TargetUrlChangeContentType[0]);
		targetUrlChangeContentTypeList.add(targetUrlChangeContentType);

		targetUrlChangeRequest.setContent_types(targetUrlChangeContentTypeList.toArray(new TargetUrlChangeContentType[0]));

		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase22() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase42() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertFalse("testCase42() targetUrlChangeResponse.getEnd_of_detail_list_flag() should be false.", targetUrlChangeResponse.getEnd_of_detail_list_flag());
		assertEquals("testCase42() targetUrlChangeResponse.getChange_indicator_list().size() incorrect.", "10",
				String.valueOf(targetUrlChangeResponse.getChange_indicator_list().size()));
		assertEquals("testCase42() targetUrlChangeResponse.getTotal_rows() incorrect.", "126", String.valueOf(targetUrlChangeResponse.getTotal_rows()));
		//assertEquals("testCase42() targetUrlChangeResponse.getChange_indicator_list().get(0).getUrl() incorrect.", "https://www.indeed.com/career/dietitian/career-advice",
		//		targetUrlChangeResponse.getChange_indicator_list().get(0).getUrl());
		//assertEquals("testCase42() targetUrlChangeResponse.getChange_indicator_list().get(0).getCurrent_crawl_timestamp() incorrect.", "2022-07-20 23:59:50",
		//		targetUrlChangeResponse.getChange_indicator_list().get(0).getCurrent_crawl_timestamp());
		FormatUtils.getInstance().logMemoryUsage("testCase42() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase43() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase43() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-20 23:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);

		UrlFilter urlFilter = null;
		List<UrlFilter> urlFilterList = new ArrayList<UrlFilter>();

		// 1) contains "indeed.com/career-advice" 
		urlFilter = new UrlFilter();
		urlFilter.setAction(IConstants.CONTENT_TYPE_ACTION_CONTAINS);
		urlFilter.setValue("career-advice");
		urlFilterList.add(urlFilter);

		targetUrlChangeRequest.setUrls(urlFilterList.toArray(new UrlFilter[0]));

		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase22() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase43() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertTrue("testCase43() targetUrlChangeResponse.getSuccess() should be true.", targetUrlChangeResponse.getSuccess());
		String testUrl = targetUrlChangeResponse.getChange_indicator_list().get(0).getUrl();
		boolean isContain = false;
		if (StringUtils.containsIgnoreCase(testUrl, "career-advice")) {
			isContain = true;
		}
		assertTrue("testCase43() isContain should be true.", isContain);
		FormatUtils.getInstance().logMemoryUsage("testCase43() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase44() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase44() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-20 23:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);

		UrlFilter urlFilter = null;
		List<UrlFilter> urlFilterList = new ArrayList<UrlFilter>();

		// 1) contains "career-advice" 
		urlFilter = new UrlFilter();
		urlFilter.setAction(IConstants.CONTENT_TYPE_ACTION_CONTAINS);
		urlFilter.setValue("career-advice");
		urlFilterList.add(urlFilter);

		// 1) is "https://www.indeed.com/career-advice/resumes-cover-letters/healthcare-data-analyst-resume" 
		urlFilter = new UrlFilter();
		urlFilter.setAction(IConstants.CONTENT_TYPE_ACTION_IS);
		urlFilter.setValue("https://www.indeed.com/career-advice/resumes-cover-letters/healthcare-data-analyst-resume");
		urlFilterList.add(urlFilter);

		targetUrlChangeRequest.setUrls(urlFilterList.toArray(new UrlFilter[0]));

		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase22() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase44() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertTrue("testCase44() targetUrlChangeResponse.getSuccess() should be true.", targetUrlChangeResponse.getSuccess());
		String testUrl = targetUrlChangeResponse.getChange_indicator_list().get(0).getUrl();
		boolean isEqual = false;
		if (StringUtils.equalsIgnoreCase(testUrl, "https://www.indeed.com/career-advice/resumes-cover-letters/healthcare-data-analyst-resume")) {
			isEqual = true;
		}
		assertTrue("testCase44() isEqual should be true.", isEqual);
		FormatUtils.getInstance().logMemoryUsage("testCase44() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase45() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase45() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-20 23:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_URL_ASC);

		UrlFilter urlFilter = null;
		List<UrlFilter> urlFilterList = new ArrayList<UrlFilter>();

		// 1) is "https://www.indeed.com/career-advice/resumes-cover-letters/healthcare-data-analyst-resume" 
		urlFilter = new UrlFilter();
		urlFilter.setAction(IConstants.CONTENT_TYPE_ACTION_CONTAINS);
		urlFilter.setValue("career-advice");
		urlFilterList.add(urlFilter);

		targetUrlChangeRequest.setUrls(urlFilterList.toArray(new UrlFilter[0]));

		List<TargetUrlChangeContentType> targetUrlChangeContentTypeList = new ArrayList<TargetUrlChangeContentType>();

		TargetUrlChangeContentType targetUrlChangeContentType = null;

		// first content type
		targetUrlChangeContentType = new TargetUrlChangeContentType();
		targetUrlChangeContentType.setLeaf(true);
		targetUrlChangeContentType.setAction(IConstants.CONTENT_TYPE_ACTION_IS);
		targetUrlChangeContentType.setValue("https://www.indeed.com/career-advice/resumes-cover-letters/healthcare-data-analyst-resume");
		targetUrlChangeContentType.setLevel(0);
		targetUrlChangeContentType.setCond("and");
		targetUrlChangeContentType.setItems(new TargetUrlChangeContentType[0]);
		targetUrlChangeContentTypeList.add(targetUrlChangeContentType);

		targetUrlChangeRequest.setContent_types(targetUrlChangeContentTypeList.toArray(new TargetUrlChangeContentType[0]));

		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase22() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase45() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertTrue("testCase45() targetUrlChangeResponse.getSuccess() should be true.", targetUrlChangeResponse.getSuccess());
		String testUrl = targetUrlChangeResponse.getChange_indicator_list().get(0).getUrl();
		boolean isEqual = false;
		if (StringUtils.equalsIgnoreCase(testUrl, "https://www.indeed.com/career-advice/resumes-cover-letters/healthcare-data-analyst-resume")) {
			isEqual = true;
		}
		assertTrue("testCase45() isEqual should be true.", isEqual);
		FormatUtils.getInstance().logMemoryUsage("testCase45() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase46() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase46() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-20 23:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_URL_ASC);
		targetUrlChangeRequest
				.setPage_tag_ids(new Integer[] { 6229331, 1370808, 1365655, 1357660, 1365654, 1215835, 1306297, 1365656, 1377844, 1374728, 7010425, 7010427 });
		targetUrlChangeRequest.setChange_indicators(new String[] { "description_chg_ind", "h2_removed_ind" });

		UrlFilter urlFilter = null;
		List<UrlFilter> urlFilterList = new ArrayList<UrlFilter>();

		// 1) is "https://www.indeed.com/career-advice/resumes-cover-letters/healthcare-data-analyst-resume" 
		urlFilter = new UrlFilter();
		urlFilter.setAction(IConstants.CONTENT_TYPE_ACTION_CONTAINS);
		urlFilter.setValue("www.indeed.com/career-advice");
		urlFilterList.add(urlFilter);

		targetUrlChangeRequest.setUrls(urlFilterList.toArray(new UrlFilter[0]));

		List<TargetUrlChangeContentType> targetUrlChangeContentTypeList = new ArrayList<TargetUrlChangeContentType>();

		TargetUrlChangeContentType targetUrlChangeContentType = null;

		// first content type
		targetUrlChangeContentType = new TargetUrlChangeContentType();
		targetUrlChangeContentType.setLeaf(true);
		targetUrlChangeContentType.setAction(IConstants.CONTENT_TYPE_ACTION_IS);
		targetUrlChangeContentType.setValue("https://www.indeed.com/career-advice/career-development/a-b-testing-examples");
		targetUrlChangeContentType.setLevel(0);
		targetUrlChangeContentType.setCond("and");
		targetUrlChangeContentType.setItems(new TargetUrlChangeContentType[0]);
		targetUrlChangeContentTypeList.add(targetUrlChangeContentType);

		// second content type
		targetUrlChangeContentType = new TargetUrlChangeContentType();
		targetUrlChangeContentType.setLeaf(true);
		targetUrlChangeContentType.setAction(IConstants.CONTENT_TYPE_ACTION_IS);
		targetUrlChangeContentType.setValue("https://www.indeed.com/career-advice/resumes-cover-letters/cover-letter-for-customer-service-call-center");
		targetUrlChangeContentType.setLevel(0);
		targetUrlChangeContentType.setCond("and");
		targetUrlChangeContentType.setItems(new TargetUrlChangeContentType[0]);
		targetUrlChangeContentTypeList.add(targetUrlChangeContentType);

		targetUrlChangeRequest.setContent_types(targetUrlChangeContentTypeList.toArray(new TargetUrlChangeContentType[0]));

		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase22() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase46() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertTrue("testCase46() targetUrlChangeResponse.getSuccess() should be true.", targetUrlChangeResponse.getSuccess());
		assertTrue("testCase46() targetUrlChangeResponse.getEnd_of_detail_list_flag() should be true.", targetUrlChangeResponse.getEnd_of_detail_list_flag());
		assertEquals("testCase46() targetUrlChangeResponse.getChange_indicator_list().size() incorrect.", "4",
				String.valueOf(targetUrlChangeResponse.getChange_indicator_list().size()));
		assertEquals("testCase46() targetUrlChangeResponse.getTotal_rows() incorrect.", "4", String.valueOf(targetUrlChangeResponse.getTotal_rows()));
		String testUrl = targetUrlChangeResponse.getChange_indicator_list().get(0).getUrl();
		boolean isEqual = false;
		if (StringUtils.equalsIgnoreCase(testUrl, "https://www.indeed.com/career-advice/career-development/a-b-testing-examples")) {
			isEqual = true;
		}
		assertTrue("testCase45() isEqual should be true.", isEqual);
		FormatUtils.getInstance().logMemoryUsage("testCase46() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase47() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase47() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-24 23:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_URL_ASC);
		targetUrlChangeRequest
				.setPage_tag_ids(new Integer[] { 6229331, 1370808, 1365655, 1357660, 1365654, 1215835, 1306297, 1365656, 1377844, 1374728, 7010425, 7010427 });
		targetUrlChangeRequest.setChange_indicators(new String[] { "response_code_chg_ind" });

		ResponseCodeFilter responseCodeFilter = null;
		List<ResponseCodeFilter> responseCodeFilterList = new ArrayList<ResponseCodeFilter>();

		// first response code fillter: from 404 to 200
		responseCodeFilter = new ResponseCodeFilter();
		responseCodeFilter.setResponse_code_previous("404");
		responseCodeFilter.setResponse_code_current("200");
		responseCodeFilterList.add(responseCodeFilter);

		targetUrlChangeRequest.setResponse_codes(responseCodeFilterList.toArray(new ResponseCodeFilter[0]));

		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase22() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase47() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertTrue("testCase47() targetUrlChangeResponse.getSuccess() should be true.", targetUrlChangeResponse.getSuccess());
		assertTrue("testCase47() targetUrlChangeResponse.getEnd_of_detail_list_flag() should be true.", targetUrlChangeResponse.getEnd_of_detail_list_flag());
		assertEquals("testCase47() targetUrlChangeResponse.getChange_indicator_list().size() incorrect.", "1",
				String.valueOf(targetUrlChangeResponse.getChange_indicator_list().size()));
		assertEquals("testCase47() targetUrlChangeResponse.getTotal_rows() incorrect.", "1", String.valueOf(targetUrlChangeResponse.getTotal_rows()));
		assertEquals("testCase47() targetUrlChangeResponse.getChange_indicator_list().get(0).getUrl() incorrect.",
				"https://www.indeed.com/career-advice/interviewing/body-language-during-interview", targetUrlChangeResponse.getChange_indicator_list().get(0).getUrl());
		assertEquals("testCase47() targetUrlChangeResponse.getChange_indicator_list().get(0).getResponse_code_current() incorrect.", "200",
				targetUrlChangeResponse.getChange_indicator_list().get(0).getResponse_code_current());
		assertEquals("testCase47() targetUrlChangeResponse.getChange_indicator_list().get(0).getResponse_code_previous() incorrect.", "404",
				targetUrlChangeResponse.getChange_indicator_list().get(0).getResponse_code_previous());
		FormatUtils.getInstance().logMemoryUsage("testCase47() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase48() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase48() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-24 23:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_URL_ASC);
		targetUrlChangeRequest
				.setPage_tag_ids(new Integer[] { 6229331, 1370808, 1365655, 1357660, 1365654, 1215835, 1306297, 1365656, 1377844, 1374728, 7010425, 7010427 });
		targetUrlChangeRequest.setChange_indicators(new String[] { "response_code_chg_ind" });

		//		ResponseCodeFilter responseCodeFilter = null;
		//		List<ResponseCodeFilter> responseCodeFilterList = new ArrayList<ResponseCodeFilter>();
		//		
		//		responseCodeFilter = new ResponseCodeFilter();
		//		responseCodeFilter.setFrom_response_code("200");
		//		responseCodeFilter.setTo_response_code("302");
		//		responseCodeFilterList.add(responseCodeFilter);
		//		
		//		responseCodeFilter = new ResponseCodeFilter();
		//		responseCodeFilter.setFrom_response_code("410");
		//		responseCodeFilter.setTo_response_code("302");
		//		responseCodeFilterList.add(responseCodeFilter);

		//targetUrlChangeRequest.setResponse_codes(responseCodeFilterList.toArray(new ResponseCodeFilter[0]));

		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase22() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase48() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertTrue("testCase48() targetUrlChangeResponse.getSuccess() should be true.", targetUrlChangeResponse.getSuccess());
		assertFalse("testCase48() targetUrlChangeResponse.getEnd_of_detail_list_flag() should be false.", targetUrlChangeResponse.getEnd_of_detail_list_flag());
		assertEquals("testCase48() targetUrlChangeResponse.getChange_indicator_list().size() incorrect.", "10",
				String.valueOf(targetUrlChangeResponse.getChange_indicator_list().size()));
		assertEquals("testCase48() targetUrlChangeResponse.getTotal_rows() incorrect.", "3043", String.valueOf(targetUrlChangeResponse.getTotal_rows()));
		//		assertEquals("testCase48() targetUrlChangeResponse.getChange_indicator_list().get(0).getUrl() incorrect.",
		//				"https://www.indeed.com/career-advice/interviewing/body-language-during-interview", targetUrlChangeResponse.getChange_indicator_list().get(0).getUrl());
		//		assertEquals("testCase48() targetUrlChangeResponse.getChange_indicator_list().get(0).getResponse_code_current() incorrect.", "200",
		//				targetUrlChangeResponse.getChange_indicator_list().get(0).getResponse_code_current());
		//		assertEquals("testCase48() targetUrlChangeResponse.getChange_indicator_list().get(0).getResponse_code_previous() incorrect.", "404",
		//				targetUrlChangeResponse.getChange_indicator_list().get(0).getResponse_code_previous());
		FormatUtils.getInstance().logMemoryUsage("testCase48() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase49() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase49() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-24 23:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_URL_ASC);
		targetUrlChangeRequest
				.setPage_tag_ids(new Integer[] { 6229331, 1370808, 1365655, 1357660, 1365654, 1215835, 1306297, 1365656, 1377844, 1374728, 7010425, 7010427 });
		targetUrlChangeRequest.setChange_indicators(new String[] { "response_code_chg_ind", "description_chg_ind", "h2_removed_ind" });
		ResponseCodeFilter responseCodeFilter = null;
		List<ResponseCodeFilter> responseCodeFilterList = new ArrayList<ResponseCodeFilter>();
		responseCodeFilter = new ResponseCodeFilter();
		responseCodeFilter.setResponse_code_previous("301");
		responseCodeFilter.setResponse_code_current("302");
		responseCodeFilterList.add(responseCodeFilter);
		responseCodeFilter = new ResponseCodeFilter();
		responseCodeFilter.setResponse_code_previous("404");
		responseCodeFilter.setResponse_code_current("200");
		responseCodeFilterList.add(responseCodeFilter);
		targetUrlChangeRequest.setResponse_codes(responseCodeFilterList.toArray(new ResponseCodeFilter[0]));
		targetUrlChangeRequest.setDebug_ind(true); //debug

		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase22() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase49() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertTrue("testCase49() targetUrlChangeResponse.getSuccess() should be true.", targetUrlChangeResponse.getSuccess());
		assertFalse("testCase49() targetUrlChangeResponse.getEnd_of_detail_list_flag() should be false.", targetUrlChangeResponse.getEnd_of_detail_list_flag());
		assertEquals("testCase49() targetUrlChangeResponse.getChange_indicator_list().size() incorrect.", "10",
				String.valueOf(targetUrlChangeResponse.getChange_indicator_list().size()));
		assertEquals("testCase49() targetUrlChangeResponse.getTotal_rows() incorrect.", "24602", String.valueOf(targetUrlChangeResponse.getTotal_rows()));
		//assertEquals("testCase49() targetUrlChangeResponse.getChange_indicator_list().get(0).getUrl() incorrect.",
		//		"https://www.indeed.com/career-advice/interviewing/body-language-during-interview", targetUrlChangeResponse.getChange_indicator_list().get(0).getUrl());
		//assertEquals("testCase49() targetUrlChangeResponse.getChange_indicator_list().get(0).getResponse_code_current() incorrect.", "200",
		//		targetUrlChangeResponse.getChange_indicator_list().get(0).getResponse_code_current());
		//assertEquals("testCase49() targetUrlChangeResponse.getChange_indicator_list().get(0).getResponse_code_previous() incorrect.", "404",
		//		targetUrlChangeResponse.getChange_indicator_list().get(0).getResponse_code_previous());
		FormatUtils.getInstance().logMemoryUsage("testCase49() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase50() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase50() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-24 23:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_URL_ASC);
		targetUrlChangeRequest
				.setPage_tag_ids(new Integer[] { 6229331, 1370808, 1365655, 1357660, 1365654, 1215835, 1306297, 1365656, 1377844, 1374728, 7010425, 7010427 });
		targetUrlChangeRequest.setChange_indicators(new String[] { "description_chg_ind", "h2_removed_ind" });

		ResponseCodeFilter responseCodeFilter = null;
		List<ResponseCodeFilter> responseCodeFilterList = new ArrayList<ResponseCodeFilter>();
		responseCodeFilter = new ResponseCodeFilter();
		responseCodeFilter.setResponse_code_previous("301");
		responseCodeFilter.setResponse_code_current("302");
		responseCodeFilterList.add(responseCodeFilter);
		responseCodeFilter = new ResponseCodeFilter();
		responseCodeFilter.setResponse_code_previous("404");
		responseCodeFilter.setResponse_code_current("200");
		responseCodeFilterList.add(responseCodeFilter);
		targetUrlChangeRequest.setResponse_codes(responseCodeFilterList.toArray(new ResponseCodeFilter[0]));

		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase22() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase50() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertFalse("testCase50() targetUrlChangeResponse.getSuccess() should be false.", targetUrlChangeResponse.getSuccess());
		String json = new Gson().toJson(targetUrlChangeResponse, TargetUrlChangeResponse.class);
		assertNotNull("testCase50() targetUrlChangeResponse.getError() should not be null.", targetUrlChangeResponse.getError());
		assertEquals("testCase50() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00023\",\"error_message\":\"Request parameter change indicators must include response_code_chg_ind.\"}}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase50() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase51() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase51() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-24 23:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_URL_ASC);
		targetUrlChangeRequest
				.setPage_tag_ids(new Integer[] { 6229331, 1370808, 1365655, 1357660, 1365654, 1215835, 1306297, 1365656, 1377844, 1374728, 7010425, 7010427 });

		ResponseCodeFilter responseCodeFilter = null;
		List<ResponseCodeFilter> responseCodeFilterList = new ArrayList<ResponseCodeFilter>();
		responseCodeFilter = new ResponseCodeFilter();
		responseCodeFilter.setResponse_code_previous("301");
		responseCodeFilter.setResponse_code_current("302");
		responseCodeFilterList.add(responseCodeFilter);
		responseCodeFilter = new ResponseCodeFilter();
		responseCodeFilter.setResponse_code_previous("404");
		responseCodeFilter.setResponse_code_current("200");
		responseCodeFilterList.add(responseCodeFilter);
		targetUrlChangeRequest.setResponse_codes(responseCodeFilterList.toArray(new ResponseCodeFilter[0]));

		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase22() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase51() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertFalse("testCase51() targetUrlChangeResponse.getSuccess() should be false.", targetUrlChangeResponse.getSuccess());
		String json = new Gson().toJson(targetUrlChangeResponse, TargetUrlChangeResponse.class);
		assertNotNull("testCase51() targetUrlChangeResponse.getError() should not be null.", targetUrlChangeResponse.getError());
		assertEquals("testCase51() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00023\",\"error_message\":\"Request parameter change indicators must include response_code_chg_ind.\"}}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase51() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase52() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase52() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-24 23:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_URL_ASC);
		targetUrlChangeRequest
				.setPage_tag_ids(new Integer[] { 6229331, 1370808, 1365655, 1357660, 1365654, 1215835, 1306297, 1365656, 1377844, 1374728, 7010425, 7010427 });
		targetUrlChangeRequest.setChange_indicators(new String[] { "response_code_chg_ind", "description_chg_ind", "h2_removed_ind" });

		ResponseCodeFilter responseCodeFilter = null;
		List<ResponseCodeFilter> responseCodeFilterList = new ArrayList<ResponseCodeFilter>();
		responseCodeFilter = new ResponseCodeFilter();
		responseCodeFilter.setResponse_code_previous("301");
		responseCodeFilter.setResponse_code_current("301");
		responseCodeFilterList.add(responseCodeFilter);
		responseCodeFilter = new ResponseCodeFilter();
		responseCodeFilter.setResponse_code_previous("404");
		responseCodeFilter.setResponse_code_current("200");
		responseCodeFilterList.add(responseCodeFilter);
		targetUrlChangeRequest.setResponse_codes(responseCodeFilterList.toArray(new ResponseCodeFilter[0]));

		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase22() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase52() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertFalse("testCase52() targetUrlChangeResponse.getSuccess() should be false.", targetUrlChangeResponse.getSuccess());
		String json = new Gson().toJson(targetUrlChangeResponse, TargetUrlChangeResponse.class);
		assertNotNull("testCase52() targetUrlChangeResponse.getError() should not be null.", targetUrlChangeResponse.getError());
		assertEquals("testCase52() targetUrlChangeResponse.getError().getError_code() is incorrect", "00022", targetUrlChangeResponse.getError().getError_code());
		FormatUtils.getInstance().logMemoryUsage("testCase52() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase53() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase53() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-20 23:59:59");
		targetUrlChangeRequest.setPage_number(17597);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);
		targetUrlChangeRequest.setSummary(IConstants.DAILY);

		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase53() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase53() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertTrue("testCase53() targetUrlChangeResponse.getEnd_of_detail_list_flag() should be true.", targetUrlChangeResponse.getEnd_of_detail_list_flag());
		FormatUtils.getInstance().logMemoryUsage("testCase53() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase54() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase54() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-07-20 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-07-20 23:59:59");
		targetUrlChangeRequest.setPage_number(17597);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);
		targetUrlChangeRequest.setSummary(IConstants.HOURLY);

		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase54() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase54() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertTrue("testCase54() targetUrlChangeResponse.getEnd_of_detail_list_flag() should be true.", targetUrlChangeResponse.getEnd_of_detail_list_flag());
		FormatUtils.getInstance().logMemoryUsage("testCase54() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase55() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_GET_URL_SUMMARY;
		System.out.println("testCase55() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-08-13 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-08-13 00:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_URL_ASC);
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase55() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase55() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertFalse("testCase55() targetUrlChangeResponse.getEnd_of_detail_list_flag() should be false.", targetUrlChangeResponse.getEnd_of_detail_list_flag());
		assertEquals("testCase55() targetUrlChangeResponse.getTotal_rows() incorrect.", "773", String.valueOf(targetUrlChangeResponse.getTotal_rows()));
		assertNotNull("testCase55() targetUrlChangeResponse.getUrl_summary_list() should not be null.", targetUrlChangeResponse.getUrl_summary_list());
		assertEquals("testCase55() targetUrlChangeResponse.getUrl_summary_list().size() incorrect.", "10",
				String.valueOf(targetUrlChangeResponse.getUrl_summary_list().size()));
		assertEquals("testCase55() targetUrlChangeResponse.getUrl_summary_list().get(0).getUrl() incorrect.",
				"https://ca.indeed.com/Top-Media-Company-jobs-in-Vancouver,-BC", targetUrlChangeResponse.getUrl_summary_list().get(0).getUrl());
		FormatUtils.getInstance().logMemoryUsage("testCase55() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase56() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_GET_URL_SUMMARY;
		System.out.println("testCase56() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-08-13 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-08-13 00:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_URL_DESC);
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase56() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase56() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertFalse("testCase56() targetUrlChangeResponse.getEnd_of_detail_list_flag() should be false.", targetUrlChangeResponse.getEnd_of_detail_list_flag());
		assertEquals("testCase56() targetUrlChangeResponse.getTotal_rows() incorrect.", "773", String.valueOf(targetUrlChangeResponse.getTotal_rows()));
		assertNotNull("testCase56() targetUrlChangeResponse.getUrl_summary_list() should not be null.", targetUrlChangeResponse.getUrl_summary_list());
		assertEquals("testCase56() targetUrlChangeResponse.getUrl_summary_list().size() incorrect.", "10",
				String.valueOf(targetUrlChangeResponse.getUrl_summary_list().size()));
		assertEquals("testCase56() targetUrlChangeResponse.getUrl_summary_list().get(0).getUrl() incorrect.",
				"https://www.indeed.com/q-pressure-vessel-designer-jobs.html", targetUrlChangeResponse.getUrl_summary_list().get(0).getUrl());
		FormatUtils.getInstance().logMemoryUsage("testCase56() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase57() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_GET_URL_SUMMARY;
		System.out.println("testCase57() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-08-13 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-08-13 00:59:59");
		targetUrlChangeRequest.setPage_number(78);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_URL_DESC);
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase57() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase57() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertTrue("testCase57() targetUrlChangeResponse.getEnd_of_detail_list_flag() should be true.", targetUrlChangeResponse.getEnd_of_detail_list_flag());
		assertEquals("testCase57() targetUrlChangeResponse.getTotal_rows() incorrect.", "773", String.valueOf(targetUrlChangeResponse.getTotal_rows()));
		assertNotNull("testCase57() targetUrlChangeResponse.getUrl_summary_list() should not be null.", targetUrlChangeResponse.getUrl_summary_list());
		assertEquals("testCase57() targetUrlChangeResponse.getUrl_summary_list().size() incorrect.", "3",
				String.valueOf(targetUrlChangeResponse.getUrl_summary_list().size()));
		assertEquals("testCase57() targetUrlChangeResponse.getUrl_summary_list().get(0).getUrl() incorrect.", "https://ca.indeed.com/hire/faq",
				targetUrlChangeResponse.getUrl_summary_list().get(0).getUrl());
		FormatUtils.getInstance().logMemoryUsage("testCase57() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase58() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_GET_URL_SUMMARY;
		System.out.println("testCase58() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-08-13 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-08-13 00:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_TOTAL_CHANGES_ASC);
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase58() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase58() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertFalse("testCase58() targetUrlChangeResponse.getEnd_of_detail_list_flag() should be false.", targetUrlChangeResponse.getEnd_of_detail_list_flag());
		assertEquals("testCase58() targetUrlChangeResponse.getTotal_rows() incorrect.", "773", String.valueOf(targetUrlChangeResponse.getTotal_rows()));
		assertNotNull("testCase58() targetUrlChangeResponse.getUrl_summary_list() should not be null.", targetUrlChangeResponse.getUrl_summary_list());
		assertEquals("testCase58() targetUrlChangeResponse.getUrl_summary_list().size() incorrect.", "10",
				String.valueOf(targetUrlChangeResponse.getUrl_summary_list().size()));
		assertEquals("testCase58() targetUrlChangeResponse.getUrl_summary_list().get(0).getUrl() incorrect.",
				"https://ca.indeed.com/Top-Media-Company-jobs-in-Vancouver,-BC", targetUrlChangeResponse.getUrl_summary_list().get(0).getUrl());
		FormatUtils.getInstance().logMemoryUsage("testCase58() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase59() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_GET_URL_SUMMARY;
		System.out.println("testCase59() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-08-13 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-08-13 00:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_TOTAL_CHANGES_DESC);
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase59() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase59() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertFalse("testCase59() targetUrlChangeResponse.getEnd_of_detail_list_flag() should be false.", targetUrlChangeResponse.getEnd_of_detail_list_flag());
		assertEquals("testCase59() targetUrlChangeResponse.getTotal_rows() incorrect.", "773", String.valueOf(targetUrlChangeResponse.getTotal_rows()));
		assertNotNull("testCase59() targetUrlChangeResponse.getUrl_summary_list() should not be null.", targetUrlChangeResponse.getUrl_summary_list());
		assertEquals("testCase59() targetUrlChangeResponse.getUrl_summary_list().size() incorrect.", "10",
				String.valueOf(targetUrlChangeResponse.getUrl_summary_list().size()));
		assertEquals("testCase59() targetUrlChangeResponse.getUrl_summary_list().get(0).getUrl() incorrect.",
				"https://www.indeed.com/q-City-of-Allen-l-Allen,-TX-jobs.html", targetUrlChangeResponse.getUrl_summary_list().get(0).getUrl());
		FormatUtils.getInstance().logMemoryUsage("testCase59() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase60() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_GET_URL_SUMMARY;
		System.out.println("testCase59() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(8711);
		targetUrlChangeRequest.setStart_crawl_timestamp("2021-08-13 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2021-08-13 00:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_TOTAL_CHANGES_DESC);
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase22() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase60() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		String json = new Gson().toJson(targetUrlChangeResponse, TargetUrlChangeResponse.class);
		assertNotNull("testCase60() targetUrlChangeResponse.getError() should not be null.", targetUrlChangeResponse.getError());
		assertEquals("testCase60() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00020\",\"error_message\":\"Data not available for the search criteria.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase60() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase61() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase61() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(4661);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-08-22 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-08-22 23:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);
		targetUrlChangeRequest.setChange_indicators(new String[] { IConstants.H2_CHG_IND });
		targetUrlChangeRequest.setPage_tag_ids(new Integer[] { 7153247 });
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase61() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase61() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		FormatUtils.getInstance().logMemoryUsage("testCase61() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase62() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase62() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(569);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-09-21 11:46:46");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-09-21 11:46:46");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_URL_ASC);
		targetUrlChangeRequest.setChange_indicators(new String[] { "redirect_301_detected_ind", "redirect_302_detected_ind" });
		targetUrlChangeRequest.setDebug_ind(true); //debug

		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase22() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase62() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertTrue("testCase62() targetUrlChangeResponse.getSuccess() should be true.", targetUrlChangeResponse.getSuccess());
		FormatUtils.getInstance().logMemoryUsage("testCase62() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase63() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase63() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		targetUrlChangeRequest.setDomain_id(9632);
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-09-22 00:00:00");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-09-22 23:59:59");
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_URL_ASC);
		targetUrlChangeRequest.setChange_indicators(new String[] { "robots_txt_chg_ind" });
		targetUrlChangeRequest.setDebug_ind(true); //debug

		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase22() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase63() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		assertTrue("testCase63() targetUrlChangeResponse.getSuccess() should be true.", targetUrlChangeResponse.getSuccess());
		FormatUtils.getInstance().logMemoryUsage("testCase63() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase64() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase64() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		
		// custom_data_chg_ind
		targetUrlChangeRequest.setDomain_id(9688); 
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-09-28 08:41:57");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-09-28 08:41:57");
		targetUrlChangeRequest.setChange_indicators(new String[] { "custom_data_chg_ind" });
		
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_URL_ASC);
		targetUrlChangeRequest.setDownload_all_ind(true);
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase64() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase64() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		FormatUtils.getInstance().logMemoryUsage("testCase64() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase65() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase65() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		
		// alternate_links_chg_ind
		targetUrlChangeRequest.setDomain_id(185); 
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-10-03 23:46:01");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-10-03 23:46:01");
		targetUrlChangeRequest.setChange_indicators(new String[] { "alternate_links_chg_ind" });
		
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_URL_ASC);
		targetUrlChangeRequest.setDownload_all_ind(true);
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase65() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase65() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		FormatUtils.getInstance().logMemoryUsage("testCase65() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase66() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase66() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		
		// alternate_links_chg_ind
		targetUrlChangeRequest.setDomain_id(256); 
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-10-01 09:47:04");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-10-01 09:47:04");
		targetUrlChangeRequest.setChange_indicators(new String[] { "amphtml_href_chg_ind" });
		
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_URL_ASC);
		targetUrlChangeRequest.setDownload_all_ind(true);
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase66() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase66() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		FormatUtils.getInstance().logMemoryUsage("testCase66() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase67() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase67() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		
		// alternate_links_chg_ind
		targetUrlChangeRequest.setDomain_id(212); 
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-10-11 03:53:02");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-10-11 03:53:02");
		targetUrlChangeRequest.setChange_indicators(new String[] { "h2_chg_ind" });
		
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_URL_ASC);
		targetUrlChangeRequest.setDownload_all_ind(true);
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase67() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase67() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		FormatUtils.getInstance().logMemoryUsage("testCase67() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase68() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase68() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		
		// alternate_links_chg_ind
		targetUrlChangeRequest.setDomain_id(8443); 
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-08-01 04:47:41");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-08-01 04:47:41");
		targetUrlChangeRequest.setChange_indicators(new String[] { "hreflang_errors_chg_ind" });
		
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_URL_ASC);
		targetUrlChangeRequest.setDownload_all_ind(true);
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase68() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase68() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		FormatUtils.getInstance().logMemoryUsage("testCase68() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase69() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase69() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		
		// alternate_links_chg_ind
		targetUrlChangeRequest.setDomain_id(212); 
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-10-07 03:55:01");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-10-07 03:55:01");
		targetUrlChangeRequest.setChange_indicators(new String[] { "hreflang_links_chg_ind" });
		
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_URL_ASC);
		targetUrlChangeRequest.setDownload_all_ind(true);
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase69() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase69() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		FormatUtils.getInstance().logMemoryUsage("testCase69() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase70() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase70() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		
		// alternate_links_chg_ind
		targetUrlChangeRequest.setDomain_id(16); 
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-10-09 05:02:04");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-10-09 05:02:04");
		targetUrlChangeRequest.setChange_indicators(new String[] { "og_markup_chg_ind" });
		
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_URL_ASC);
		targetUrlChangeRequest.setDownload_all_ind(true);
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase70() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase70() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		FormatUtils.getInstance().logMemoryUsage("testCase70() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase71() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase71() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		
		// alternate_links_chg_ind
		targetUrlChangeRequest.setDomain_id(7529); 
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-10-08 01:49:03");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-10-08 01:49:03");
		targetUrlChangeRequest.setChange_indicators(new String[] { "page_link_chg_ind" });
		
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_URL_ASC);
		targetUrlChangeRequest.setDownload_all_ind(true);
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase71() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase71() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		FormatUtils.getInstance().logMemoryUsage("testCase71() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase72() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase72() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		
		// alternate_links_chg_ind
		targetUrlChangeRequest.setDomain_id(7529); 
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-10-02 17:24:01");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-10-02 17:24:01");
		targetUrlChangeRequest.setChange_indicators(new String[] { "redirect_chain_chg_ind" });
		
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_URL_ASC);
		targetUrlChangeRequest.setDownload_all_ind(true);
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase72() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase72() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		FormatUtils.getInstance().logMemoryUsage("testCase72() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase73() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase73() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		
		// alternate_links_chg_ind
		targetUrlChangeRequest.setDomain_id(7529); 
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-08-01 21:07:46");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-08-01 21:07:46");
		targetUrlChangeRequest.setChange_indicators(new String[] { "response_headers_removed_ind" });
		
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_URL_ASC);
		targetUrlChangeRequest.setDownload_all_ind(true);
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase73() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase73() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		FormatUtils.getInstance().logMemoryUsage("testCase73() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase74() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = endpointPrefix + PoliteCrawlWebServiceClientService.ROUTER_TARGET_URL_CHANGE + IConstants.COMMAND_LIST_CHANGE_INDICATORS;
		System.out.println("testCase74() requestUrl=" + requestUrl);
		TargetUrlChangeRequest targetUrlChangeRequest = new TargetUrlChangeRequest();
		targetUrlChangeRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		
		// alternate_links_chg_ind
		targetUrlChangeRequest.setDomain_id(16); 
		targetUrlChangeRequest.setStart_crawl_timestamp("2022-08-28 03:55:36");
		targetUrlChangeRequest.setEnd_crawl_timestamp("2022-08-28 03:55:36");
		targetUrlChangeRequest.setChange_indicators(new String[] { "structured_data_chg_ind" });
		
		targetUrlChangeRequest.setPage_number(1);
		targetUrlChangeRequest.setRows_per_page(10);
		targetUrlChangeRequest.setSort_by(IConstants.SORT_BY_URL_ASC);
		targetUrlChangeRequest.setDownload_all_ind(true);
		String requestParameters = new Gson().toJson(targetUrlChangeRequest, TargetUrlChangeRequest.class);
		//System.out.println("testCase74() requestParameters=" + requestParameters);
		TargetUrlChangeResponse targetUrlChangeResponse = politeCrawlWebServiceClientService.targetUrlChange(requestUrl, requestParameters);
		assertNotNull("testCase74() targetUrlChangeResponse should not be null.", targetUrlChangeResponse);
		FormatUtils.getInstance().logMemoryUsage("testCase74() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

}