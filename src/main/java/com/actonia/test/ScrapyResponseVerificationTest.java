package com.actonia.test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.fail;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.dao.TargetUrlHtmlClickHouseDAO;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.utils.CommonUtils;
import com.actonia.utils.CrawlerUtils;
import com.actonia.value.object.CrawlerResponse;
import com.actonia.value.object.PageAnalysisResult;
import com.actonia.value.object.ScrapyCrawlerResponse;
import com.google.gson.Gson;

public class ScrapyResponseVerificationTest {
	private boolean isDebug = false;
	private static final String DEFAULT_USER_AGENT = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 (compatible; PoliteCrawl.v2.0)";
	private static final String TABLE_NAME = "test_target_url_html";
	private static final boolean isResponseAsHtml = false;

	public ScrapyResponseVerificationTest() {
		super();
	}

	public static void main(String[] args) {
		ScrapyResponseVerificationTest scrapyResponseVerificationTest = null;
		try {
			scrapyResponseVerificationTest = new ScrapyResponseVerificationTest();
			scrapyResponseVerificationTest.testArabic();
			scrapyResponseVerificationTest.testCzech();
			scrapyResponseVerificationTest.testDanish();
			scrapyResponseVerificationTest.testGerman();
			scrapyResponseVerificationTest.testEnglish();
			scrapyResponseVerificationTest.testSpanish();
			scrapyResponseVerificationTest.testEstonian();
			scrapyResponseVerificationTest.testFinnish();
			scrapyResponseVerificationTest.testFrench();
			scrapyResponseVerificationTest.testHebrew();
			scrapyResponseVerificationTest.testHungarian();
			scrapyResponseVerificationTest.testIndonesian();
			scrapyResponseVerificationTest.testItalian();
			scrapyResponseVerificationTest.testJapanese();
			scrapyResponseVerificationTest.testKorean();
			scrapyResponseVerificationTest.testLuxembourgish();
			scrapyResponseVerificationTest.testLithuanian();
			scrapyResponseVerificationTest.testLatvian();
			scrapyResponseVerificationTest.testMalay();
			scrapyResponseVerificationTest.testDutch();
			scrapyResponseVerificationTest.testNorwegian();
			scrapyResponseVerificationTest.testPolish();
			scrapyResponseVerificationTest.testPortuguese();
			scrapyResponseVerificationTest.testRomanian();
			scrapyResponseVerificationTest.testRussian();
			scrapyResponseVerificationTest.testSwedish();
			scrapyResponseVerificationTest.testThai();
			scrapyResponseVerificationTest.testTagalog();
			scrapyResponseVerificationTest.testTurkish();
			scrapyResponseVerificationTest.testVietnamese();
			scrapyResponseVerificationTest.testSimplifiedChinese();
			scrapyResponseVerificationTest.testTraditionalChinese();
			scrapyResponseVerificationTest.testGreek();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void testArabic() {
		String decodedUrlString = "http://www.bbc.com/arabic";
		crawl(decodedUrlString, null);
		verify(decodedUrlString, "الرئيسية - BBC News Arabic");
		System.out.println("testArabic() passed.");
	}

	private void testCzech() {
		String decodedUrlString = "https://www.apple.com/cz/apple-pay/";
		crawl(decodedUrlString, null);
		verify(decodedUrlString, "Apple Pay – Apple (CZ)");
		System.out.println("testCzech() passed.");
	}

	private void testDanish() {
		String decodedUrlString = "https://www.888poker.dk/poker-regler/";
		crawl(decodedUrlString, null);
		verify(decodedUrlString, "Poker regler – lær poker regler hos 888poker.dk");
		System.out.println("testDanish() passed.");
	}

	private void testGerman() {
		String decodedUrlString = "https://www.ebookers.ch/";
		crawl(decodedUrlString, "ClarityBot-Expedia");
		verify(decodedUrlString, "Reiseangebote vergleichen und günstige Ferien buchen - ebookers.ch");
		System.out.println("testGerman() passed.");
	}

	private void testEnglish() {
		String decodedUrlString = "https://www.crresearch.com/";
		crawl(decodedUrlString, null);
		verify(decodedUrlString, "Market Research Chicago | Market Research Companies | C+R");
		System.out.println("testEnglish() passed.");
	}

	private void testSpanish() {
		String decodedUrlString = "https://www.rightcasino.com/es/";
		crawl(decodedUrlString, null);
		verify(decodedUrlString, "Encuentra Tu Casino Online Adecuado | RightCasino.com");
		System.out.println("testSpanish() passed.");
	}

	private void testEstonian() {
		String decodedUrlString = "https://www.postimees.ee/";
		crawl(decodedUrlString, null);
		verify(decodedUrlString, "Postimees: Värsked uudised Eestist ja välismaalt");
		System.out.println("testEstonian() passed.");
	}

	private void testFinnish() {
		String decodedUrlString = "https://www.ebookers.fi/";
		crawl(decodedUrlString, "ClarityBot-Expedia");
		verify(decodedUrlString, "Vertaa ja varaa lennot, hotellit ja halvat matkat - ebookers.fi");
		System.out.println("testFinnish() passed.");
	}

	private void testFrench() {
		String decodedUrlString = "https://www.ebookers.fr/";
		crawl(decodedUrlString, "ClarityBot-Expedia");
		verify(decodedUrlString, "Voyage pas cher avec réservation d'hôtel, vol & voiture - ebookers.fr");
		System.out.println("testFrench() passed.");
	}

	private void testHebrew() {
		String decodedUrlString = "https://www.searchi.co.il/";
		crawl(decodedUrlString, null);
		verify(decodedUrlString, "סרצ'י | קידום אתרים");
		System.out.println("testHebrew() passed.");
	}

	private void testHungarian() {
		String decodedUrlString = "https://www.apple.com/hu/iphone-8/specs/";
		crawl(decodedUrlString, null);
		verify(decodedUrlString, "iPhone 8 – Technikai adatok – Apple (HU)");
		System.out.println("testHungarian() passed.");
	}

	private void testIndonesian() {
		String decodedUrlString = "https://www.expedia.co.id/";
		crawl(decodedUrlString, "ClarityBot-Expedia");
		verify(decodedUrlString, "Hotel, Penerbangan Murah, Paket Liburan | Expedia.co.id");
		System.out.println("testIndonesian() passed.");
	}

	private void testItalian() {
		String decodedUrlString = "https://www.888.it/";
		crawl(decodedUrlString, null);
		verify(decodedUrlString, "Scommesse sportive, Poker e Casinò online su 888 | 888.it™");
		System.out.println("testItalian() passed.");
	}

	private void testJapanese() {
		String decodedUrlString = "https://www.reform-contact.com/";
		crawl(decodedUrlString, null);
		verify(decodedUrlString, "リフォーム会社探しならLIXIL（リクシル）のリフォームコンタクト");
		System.out.println("testJapanese() passed.");
	}

	private void testKorean() {
		String decodedUrlString = "https://travelblog.expedia.co.kr/";
		crawl(decodedUrlString, "ClarityBot-Expedia");
		verify(decodedUrlString, "익스피디아 트래블 블로그 | travelblog.expedia.co.kr");
		System.out.println("testKorean() passed.");
	}

	private void testLuxembourgish() {
		String decodedUrlString = "https://www.rtl.lu/";
		crawl(decodedUrlString, null);
		verify(decodedUrlString, "RTL - Home");
		System.out.println("testLuxembourgish() passed.");
	}

	private void testLithuanian() {
		String decodedUrlString = "https://www.youngliving.com/lt_LT";
		crawl(decodedUrlString, null);
		verify(decodedUrlString, "Eteriniai aliejai ir aromaterapija | Eteriniai aliejai ir aromaterapija | „Young Living Essential Oils“");
		System.out.println("testLithuanian() passed.");
	}

	private void testLatvian() {
		String decodedUrlString = "https://www.apollo.lv/";
		crawl(decodedUrlString, null);
		verify(decodedUrlString, "Apollo.lv");
		System.out.println("testLatvian() passed.");
	}

	private void testMalay() {
		String decodedUrlString = "https://www.okezone.com/";
		crawl(decodedUrlString, null);
		verify(decodedUrlString, "Okezone | Berita Terbaru | Berita Hari Ini | Berita Online");
		System.out.println("testMalay() passed.");
	}

	private void testDutch() {
		String decodedUrlString = "https://www.telegraaf.nl/";
		crawl(decodedUrlString, null);
		verify(decodedUrlString, "Nieuws | Het laatste nieuws uit Nederland leest u op Telegraaf.nl");
		System.out.println("testDutch() passed.");
	}

	private void testNorwegian() {
		String decodedUrlString = "https://www.expedia.no/";
		crawl(decodedUrlString, "ClarityBot-Expedia");
		verify(decodedUrlString, "Reise | Hotell, flybilletter og leiebil | Expedia.no");
		System.out.println("testNorwegian() passed.");
	}

	private void testPolish() {
		String decodedUrlString = "https://www.apple.com/pl/";
		crawl(decodedUrlString, null);
		verify(decodedUrlString, "Apple (Polska)");
		System.out.println("testPolish() passed.");
	}

	private void testPortuguese() {
		String decodedUrlString = "https://www.cmegroup.com/pt/";
		crawl(decodedUrlString, null);
		verify(decodedUrlString, "Mercado de liderança mundial em futuros e opções");
		System.out.println("testPortuguese() passed.");
	}

	private void testRomanian() {
		String decodedUrlString = "https://www.888sport.ro/";
		crawl(decodedUrlString, null);
		verify(decodedUrlString, "888sport™ Casa de pariuri online – Bonus de până la 1.000 Lei");
		System.out.println("testRomanian() passed.");
	}

	private void testRussian() {
		String decodedUrlString = "https://www.gismeteo.ru/";
		crawl(decodedUrlString, null);
		verify(decodedUrlString, "GISMETEO: погода в России — прогноз погоды на сегодня, завтра, 3 дня, выходные, неделю, 10 дней, месяц.");
		System.out.println("testRussian() passed.");
	}

	private void testSwedish() {
		String decodedUrlString = "https://www.mrjet.se/";
		crawl(decodedUrlString, "ClarityBot-Expedia");
		verify(decodedUrlString, "Boka resor och hitta erbjudanden på resor - mrjet.se");
		System.out.println("testSwedish() passed.");
	}

	private void testThai() {
		String decodedUrlString = "https://www.expedia.co.th/";
		crawl(decodedUrlString, "ClarityBot-Expedia");
		verify(decodedUrlString, "จองโรงแรม ตั๋วเครื่องบิน แพ็คเกจท่องเที่ยวออนไลน์ | Expedia.co.th");
		System.out.println("testThai() passed.");
	}

	private void testTagalog() {
		String decodedUrlString = "https://bandera.inquirer.net/";
		crawl(decodedUrlString, null);
		verify(decodedUrlString, "Bandera | Philippine Entertainment Tabloid for the latest showbiz news");
		System.out.println("testTagalog() passed.");
	}

	private void testTurkish() {
		String decodedUrlString = "https://www.apple.com/tr/";
		crawl(decodedUrlString, null);
		verify(decodedUrlString, "Apple (Türkiye)");
		System.out.println("testTurkish() passed.");
	}

	private void testVietnamese() {
		String decodedUrlString = "https://www.expedia.com.vn";
		crawl(decodedUrlString, "ClarityBot-Expedia");
		verify(decodedUrlString, "Du lịch: Vé máy bay giá rẻ, Khách sạn và du lịch trọn gói | Expedia.com.vn");
		System.out.println("testVietnamese() passed.");
	}

	private void testSimplifiedChinese() {
		String decodedUrlString = "https://www.cmegroup.com/cn-s/";
		crawl(decodedUrlString, null);
		verify(decodedUrlString, "期货介绍_期货行情分析_期货期权知识大全-芝商所");
		System.out.println("testSimplifiedChinese() passed.");
	}

	private void testTraditionalChinese() {
		String decodedUrlString = "https://www.cmegroup.com/cn-t/";
		crawl(decodedUrlString, null);
		verify(decodedUrlString, "全球最大的期貨及期貨交易中心--芝商所");
		System.out.println("testTraditionalChinese() passed.");
	}

	// test term frequencies for undefined language Greek
	private void testGreek() {
		String decodedUrlString = "https://www.iefimerida.gr/";
		crawl(decodedUrlString, null);
		verify(decodedUrlString, "iefimerida.gr | Ειδήσεις και νέα  - Η κορυφαία εφημερίδα online");
		System.out.println("testGreek() passed.");
	}

	private void verify(String decodedUrlString, String expectedTitle) {
		int domainId = 1701;
		HtmlClickHouseEntity htmlClickHouseEntity = null;
		List<String> databaseFields = null;
		try {
			databaseFields = CrawlerUtils.getInstance().getTargetUrlHtmlTableAllFieldNames();
			htmlClickHouseEntity = TargetUrlHtmlClickHouseDAO.getInstance().getPrevious(null, null, domainId, decodedUrlString, databaseFields, TABLE_NAME, null, null);
			assertNotNull("htmlClickHouseEntity should not be null.", htmlClickHouseEntity);
			if (!StringUtils.containsIgnoreCase(htmlClickHouseEntity.getCrawlerResponse().getTitle(), expectedTitle)) {
				fail("actual title does not contain expected title. expected=" + expectedTitle + ",actual=" + htmlClickHouseEntity.getCrawlerResponse().getTitle());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void crawl(String urlString, String specialUserAgent) {
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = null;
		HtmlClickHouseEntity htmlClickHouseEntity = null;
		ScrapyCrawlerResponse scrapyCrawlerResponse = null;
		CrawlerResponse crawlerResponse = null;
		PageAnalysisResult[] pageAnalysisResultArray = null;
		String testJson = null;
		String userAgent = null;
		Date trackDate = null;
		boolean isJavascriptCrawler = false;
		boolean isStoreHtml = false;
		Integer javascriptTimeoutInSecond = null;

		try {
			trackDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
			if (isDebug == true) {
				System.out.println("crawl() urlString=" + urlString + ",isResponseAsHtml=" + isResponseAsHtml);
			}
			if (StringUtils.isNotBlank(specialUserAgent)) {
				userAgent = specialUserAgent;
			} else {
				userAgent = DEFAULT_USER_AGENT;
			}
			scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, userAgent, null, isJavascriptCrawler,javascriptTimeoutInSecond,
					IConstants.CRAWL_TYPE_TARGET_URL_HTML, null, isStoreHtml, null, isResponseAsHtml, null);
			assertNotNull("crawl() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
			crawlerResponse = scrapyCrawlerResponse.getCrawlerResponse();
			assertNotNull("crawl() crawlerResponse should not be null.", crawlerResponse);
			htmlClickHouseEntity = new HtmlClickHouseEntity();
			htmlClickHouseEntity.setDomainId(1701);
			htmlClickHouseEntity.setUrl(urlString);
			htmlClickHouseEntity.setTrackDate(trackDate);
			htmlClickHouseEntity.setCrawlerResponse(crawlerResponse);
			assertEquals("crawl() HTTP status code incorrect.", 200, htmlClickHouseEntity.getHttpStatusCode().intValue());
			if (htmlClickHouseEntity.getHttpStatusCode() != null && htmlClickHouseEntity.getHttpStatusCode() == 200) {

				// transformation process 1: aggregate page analysis results
				pageAnalysisResultArray = CrawlerUtils.getInstance().getPageAnalysisResultArray(crawlerResponse);
				assertNotNull("crawl() pageAnalysisResultArray should not be null.", pageAnalysisResultArray);
				assertNotEquals("crawl() pageAnalysisResultArray.length should not be 0.", 0, pageAnalysisResultArray.length);
				htmlClickHouseEntity.setPageAnalysisResultArray(pageAnalysisResultArray);
				testJson = new Gson().toJson(pageAnalysisResultArray, PageAnalysisResult[].class);
				if (isDebug == true) {
					System.out.println("crawl() pageAnalysis=" + testJson);
				}
			}
			htmlClickHouseEntity.setWeekOfYear(CommonUtils.calculateWeekOfYear(trackDate));
			htmlClickHouseEntityList = new ArrayList<HtmlClickHouseEntity>();
			htmlClickHouseEntityList.add(htmlClickHouseEntity);
			TargetUrlHtmlClickHouseDAO.getInstance().createBatch(null, null, htmlClickHouseEntityList, TABLE_NAME);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
