package com.actonia.test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import java.util.Map;

import org.apache.commons.lang.math.NumberUtils;

import com.actonia.IConstants;
import com.actonia.utils.CrawlerUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.value.object.ScrapyCrawlerResponse;

public class GtmTest {

	public GtmTest() {
		super();
	}

	public static void main(String[] args) throws Exception {
		new GtmTest().testScrapyApi(args);
	}

	private void testScrapyApi(String[] args) throws Exception {
		long startTimestamp = System.currentTimeMillis();

		int totalUrls = 0;

		if (args != null && args.length >= 1) {
			totalUrls = NumberUtils.toInt(args[0]);
			FormatUtils.getInstance().logMemoryUsage("testScrapyApi() runtime argument 1: totalUrls=" + totalUrls);
		} else {
			FormatUtils.getInstance().logMemoryUsage("testScrapyApi() runtime argument 1 (totalUrls) is required.");
			return;
		}

		ScrapyCrawlerResponse scrapyCrawlerResponse = null;
		Map<String, String> pageCrawlerApiRequestHeaders = null;
		boolean isJavascriptCrawler = false;
		String ip = "1";
		String queueName = "testQueueName";
		scrapyCrawlerResponse = null;
		String urlString = null;
		String region = null;
		boolean isStoreHtml = false;
		Integer javascriptTimeoutInSecond = 10;
		boolean isResponseAsHtml = false;

		String userAgent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 (compatible; politeCrawl.v2.0)";
		isJavascriptCrawler = true;

		// first iteration of 3500 URLs
		totalUrls = 3500;
		for (int i = 0; i < totalUrls; i++) {
			urlString = "https://test.edgeseo.dev/681358337/page_" + (i + 1) + ".html";
			if ((i + 1) % 100 == 0) {
				FormatUtils.getInstance().logMemoryUsage("testScrapyApi() total URLs processed=" + (i + 1) + ",urlString=" + urlString);
			}
			scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(ip, queueName, urlString, userAgent, null, isJavascriptCrawler,
					javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null, isResponseAsHtml, region);
			assertNotNull("testScrapyApi() scrapyCrawlerResponse should not be null, urlString=" + urlString, scrapyCrawlerResponse);
			assertEquals("testScrapyApi() response code incorrect. urlString=" + urlString, "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
			assertEquals("testScrapyApi() title incorrect. urlString=" + urlString, "title after change", scrapyCrawlerResponse.getCrawlerResponse().getTitle());
		}

		// second iteration of 3500 URLs
		totalUrls = 3500;
		for (int i = 0; i < totalUrls; i++) {
			urlString = "https://test.edgeseo.dev/681358337/page_" + (i + 1) + ".html";
			if ((i + 1) % 100 == 0) {
				FormatUtils.getInstance().logMemoryUsage("testScrapyApi() total URLs processed=" + (i + 1) + ",urlString=" + urlString);
			}
			scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(ip, queueName, urlString, userAgent, null, isJavascriptCrawler,
					javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null, isResponseAsHtml, region);
			assertNotNull("testScrapyApi() scrapyCrawlerResponse should not be null, urlString=" + urlString, scrapyCrawlerResponse);
			assertEquals("testScrapyApi() response code incorrect. urlString=" + urlString, "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
			assertEquals("testScrapyApi() title incorrect. urlString=" + urlString, "title after change", scrapyCrawlerResponse.getCrawlerResponse().getTitle());
		}

		// third iteration of 1000 URLs
		totalUrls = 1000;
		for (int i = 0; i < totalUrls; i++) {
			urlString = "https://test.edgeseo.dev/681358337/page_" + (i + 1) + ".html";
			if ((i + 1) % 100 == 0) {
				FormatUtils.getInstance().logMemoryUsage("testScrapyApi() total URLs processed=" + (i + 1) + ",urlString=" + urlString);
			}
			scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(ip, queueName, urlString, userAgent, null, isJavascriptCrawler,
					javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null, isResponseAsHtml, region);
			assertNotNull("testScrapyApi() scrapyCrawlerResponse should not be null, urlString=" + urlString, scrapyCrawlerResponse);
			assertEquals("testScrapyApi() response code incorrect. urlString=" + urlString, "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
			assertEquals("testScrapyApi() title incorrect. urlString=" + urlString, "title after change", scrapyCrawlerResponse.getCrawlerResponse().getTitle());
		}

		FormatUtils.getInstance().logMemoryUsage("testScrapyApi() elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}
}