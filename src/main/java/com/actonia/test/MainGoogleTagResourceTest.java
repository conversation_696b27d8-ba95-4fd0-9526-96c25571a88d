package com.actonia.test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

import com.actonia.IConstants;
import com.actonia.dao.GoogleTagJavascriptDAO;
import com.actonia.dao.GoogleTagJavascriptUrlDAO;
import com.actonia.entity.GoogleTagJavascriptEntity;
import com.actonia.entity.GoogleTagJavascriptUrlEntity;
import com.actonia.service.AccessTokenService;
import com.actonia.service.MainWebServiceClientService;
import com.actonia.utils.GoogleTagUtils;
import com.actonia.utils.Md5Util;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.GoogleTagResourceRequest;
import com.actonia.value.object.GoogleTagResourceResponse;
import com.google.gson.Gson;

public class MainGoogleTagResourceTest {
	private static final String WEB_SERVICE_ENDPOINT = "https://api.seoclarity.net/seoClarity";

	private MainWebServiceClientService mainWebServiceClientService;

	private static final String WORKSPACE_NAME = "Default Workspace";

	private static final String URL_GTM_2 = "https://test.edgeseo.dev/gtm_2.html";

	private static final String SCHEMA_TYPE_LOCAL_BUSINESS = "LocalBusiness";
	private static final String SCHEMA_TYPE_PRODUCT = "Product";

	private static final String structuredDataNewLocalBusiness = "{  \"@context\": \"https://schema.org/\",  \"@type\": \"LocalBusiness\",  \"name\": \"name_gtm_2\",  \"address\": \"address_gtm_2\",  \"image\": \"https://images-eu.ssl-images-amazon.com/images/I/41kcjQkH5VL._AC_SX184_.jpg\"}";
	private static final String structuredDataNewProduct = "{\"@context\":\"https://schema.org/\",\"@type\": \"Product\",\"name\": \"testProductName\",\"image\": \"https://images-eu.ssl-images-amazon.com/images/I/41kcjQkH5VL._AC_SX184_.jpg\",\"offers\": {\"@type\": \"Offer\",\"priceCurrency\": \"USD\",\"price\": \"168\",\"availability\": \"https://schema.org/InStock\"}}";

	private static final String structuredDataUpdateLocalBusiness = "{\n  \"@context\": \"https://schema.org/\",\n  \"@type\": \"LocalBusiness\",\n  \"name\": \"name_gtm_2_20200904_0140pm\",\n  \"address\": \"address_gtm_2_20200904_0140pm\",\n  \"image\": \"https://images-eu.ssl-images-amazon.com/images/I/41kcjQkH5VL._AC_SX184_.jpg\"\n}\n";
	private static final String structuredDataUpdateProduct = "{\"@context\":\"https://schema.org/\",\"@type\": \"Product\",\"name\": \"testProductName_20200904_0140pm\",\"image\": \"https://images-eu.ssl-images-amazon.com/images/I/41kcjQkH5VL._AC_SX184_.jpg\",\"offers\": {\"@type\": \"Offer\",\"priceCurrency\": \"USD\",\"price\": \"168\",\"availability\": \"https://schema.org/InStock\"}}";

	private static final String actionableInsightNew = "(function(){try{document.title = \"Title Updated 2021-03-15 3:09 PM\";console.log(\"title updated=\"+document.title);}catch(error) {console.log(\"error=\"+error);}})();";

	private static final String JAVASCRIPT_NAME = "Javascript name 168";

	private static final String ACCOUNT_ID = "**********";

	private static final String CONTAINER_ID = "********";

	private GoogleTagJavascriptDAO googleTagJavascriptDAO;
	private GoogleTagJavascriptUrlDAO googleTagJavascriptUrlDAO;

	public MainGoogleTagResourceTest() {
		super();
		mainWebServiceClientService = SpringBeanFactory.getBean("mainWebServiceClientService");
		this.googleTagJavascriptDAO = SpringBeanFactory.getBean("googleTagJavascriptDAO");
		this.googleTagJavascriptUrlDAO = SpringBeanFactory.getBean("googleTagJavascriptUrlDAO");
	}

	public static void main(String[] args) throws Exception {
		new MainGoogleTagResourceTest().runTests();
	}

	private void runTests() throws Exception {

		// structured data tests begin
		testCase1();
		testCase2();
		testCase3();
		testCase4();
		testCase5();
		testCase6();
		testCase7();
		testCase8();
		testCase9();
		testCase10();
		testCase11();
		testCase12();
		testCase13();
		testCase14();
		testCase15();
		testCase16();
		testCase17();
		testCase18();
		testCase19();
		testCase20();
		testCase21();
		testCase22();
		testCase23();
		testCase26();
		testCase27();
		testCase28();
		testCase29();
		// structured data tests end

		// Javascript tests begin
		testCase30();
		testCase32();
		testCase33();
		testCase34();
		testCase36();
		testCase37();
		testCase38();
		testCase39();
		testCase40();
		testCase41();
		testCase42();
		testCase43();
		testCase44();
		testCase47();
		testCase48();
		testCase49();
		testCase50();
		testCase51();
		testCase52();
		testCase53();
		testCase54();
		testCase55();
		testCase56();
		testCase57();
		testCase58();
		testCase59();
		testCase60();
		testCase61();
		testCase62();
		testCase63();
		testCase64();
		testCase65();
		testCase66();
		testCase67();
		// Javascript tests end
	}

	private void testCase1() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_UPDATE_STRUCTURED_DATA;
		System.out.println("testCase1() requestUrl=" + requestUrl);
		//GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		//String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		String requestParameters = IConstants.EMPTY_STRING;
		//System.out.println("testCase1() requestParameters=" + requestParameters);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase1() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase1() googleTagResourceResponse json=" + json);
		assertEquals("testCase1() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000004\",\"error_message\":\"Request JSON is required.\"}}", json);
		System.out.println("testCase1() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase2() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_UPDATE_STRUCTURED_DATA;
		System.out.println("testCase2() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		//System.out.println("testCase2() requestParameters=" + requestParameters);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase2() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase2() googleTagResourceResponse json=" + json);
		assertEquals("testCase2() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000001\",\"error_message\":\"Request parameter access token is required.\"}}", json);
		System.out.println("testCase2() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase3() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_UPDATE_STRUCTURED_DATA;
		System.out.println("testCase3() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token("testaccesstoken");
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		//System.out.println("testCase3() requestParameters=" + requestParameters);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase3() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase3() googleTagResourceResponse json=" + json);
		assertEquals("testCase3() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000002\",\"error_message\":\"Request parameter access token is invalid.\"}}", json);
		System.out.println("testCase3() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase4() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_UPDATE_STRUCTURED_DATA;
		System.out.println("testCase4() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		//System.out.println("testCase4() requestParameters=" + requestParameters);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase4() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase4() googleTagResourceResponse json=" + json);
		assertEquals("testCase4() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000006\",\"error_message\":\"Request parameter GTM edit containers access token is required.\"}}",
				json);
		System.out.println("testCase4() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase5() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_UPDATE_STRUCTURED_DATA;
		System.out.println("testCase5() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token("gtm_edit_containers_access_token");
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		//System.out.println("testCase5() requestParameters=" + requestParameters);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase5() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase5() googleTagResourceResponse json=" + json);
		assertEquals("testCase5() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000007\",\"error_message\":\"Request parameter GTM edit container versions access token is required.\"}}",
				json);
		System.out.println("testCase5() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase6() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_UPDATE_STRUCTURED_DATA;
		System.out.println("testCase6() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token("gtm_edit_containers_access_token");
		googleTagResourceRequest.setGtm_edit_container_versions_access_token("gtm_edit_container_versions_access_token");
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		//System.out.println("testCase6() requestParameters=" + requestParameters);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase6() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase6() googleTagResourceResponse json=" + json);
		assertEquals("testCase6() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000008\",\"error_message\":\"Request parameter GTM publish access token is required.\"}}", json);
		System.out.println("testCase6() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase7() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_UPDATE_STRUCTURED_DATA;
		System.out.println("testCase7() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token("gtm_edit_containers_access_token");
		googleTagResourceRequest.setGtm_edit_container_versions_access_token("gtm_edit_container_versions_access_token");
		googleTagResourceRequest.setGtm_publish_access_token("gtm_publish_access_token");
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		//System.out.println("testCase7() requestParameters=" + requestParameters);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase7() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase7() googleTagResourceResponse json=" + json);
		assertEquals("testCase7() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000009\",\"error_message\":\"Request parameter account ID is required.\"}}", json);
		System.out.println("testCase7() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase8() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_UPDATE_STRUCTURED_DATA;
		System.out.println("testCase8() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token("gtm_edit_containers_access_token");
		googleTagResourceRequest.setGtm_edit_container_versions_access_token("gtm_edit_container_versions_access_token");
		googleTagResourceRequest.setGtm_publish_access_token("gtm_publish_access_token");
		googleTagResourceRequest.setAccount_id("account_id");
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		//System.out.println("testCase8() requestParameters=" + requestParameters);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase8() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase8() googleTagResourceResponse json=" + json);
		assertEquals("testCase8() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000010\",\"error_message\":\"Request parameter container ID is required.\"}}", json);
		System.out.println("testCase8() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase9() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_UPDATE_STRUCTURED_DATA;
		System.out.println("testCase9() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token("gtm_edit_containers_access_token");
		googleTagResourceRequest.setGtm_edit_container_versions_access_token("gtm_edit_container_versions_access_token");
		googleTagResourceRequest.setGtm_publish_access_token("gtm_publish_access_token");
		googleTagResourceRequest.setAccount_id("account_id");
		googleTagResourceRequest.setContainer_id("container_id");
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		//System.out.println("testCase9() requestParameters=" + requestParameters);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase9() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase9() googleTagResourceResponse json=" + json);
		assertEquals("testCase9() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000011\",\"error_message\":\"Request parameter workspace name is required.\"}}", json);
		System.out.println("testCase9() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase10() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_UPDATE_STRUCTURED_DATA;
		System.out.println("testCase10() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token("gtm_edit_containers_access_token");
		googleTagResourceRequest.setGtm_edit_container_versions_access_token("gtm_edit_container_versions_access_token");
		googleTagResourceRequest.setGtm_publish_access_token("gtm_publish_access_token");
		googleTagResourceRequest.setAccount_id("account_id");
		googleTagResourceRequest.setContainer_id("container_id");
		googleTagResourceRequest.setWorkspace_name("workspace_name");
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		//System.out.println("testCase10() requestParameters=" + requestParameters);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase10() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase10() googleTagResourceResponse json=" + json);
		assertEquals("testCase10() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000012\",\"error_message\":\"Request parameter url is required.\"}}", json);
		System.out.println("testCase10() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase11() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_UPDATE_STRUCTURED_DATA;
		System.out.println("testCase11() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token("gtm_edit_containers_access_token");
		googleTagResourceRequest.setGtm_edit_container_versions_access_token("gtm_edit_container_versions_access_token");
		googleTagResourceRequest.setGtm_publish_access_token("gtm_publish_access_token");
		googleTagResourceRequest.setAccount_id("account_id");
		googleTagResourceRequest.setContainer_id("container_id");
		googleTagResourceRequest.setWorkspace_name("workspace_name");
		googleTagResourceRequest.setUrl("url");
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		//System.out.println("testCase11() requestParameters=" + requestParameters);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase11() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase11() googleTagResourceResponse json=" + json);
		assertEquals("testCase11() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000013\",\"error_message\":\"Request parameter url is invalid.\"}}", json);
		System.out.println("testCase11() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase12() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_UPDATE_STRUCTURED_DATA;
		System.out.println("testCase12() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token("gtm_edit_containers_access_token");
		googleTagResourceRequest.setGtm_edit_container_versions_access_token("gtm_edit_container_versions_access_token");
		googleTagResourceRequest.setGtm_publish_access_token("gtm_publish_access_token");
		googleTagResourceRequest.setAccount_id("account_id");
		googleTagResourceRequest.setContainer_id("container_id");
		googleTagResourceRequest.setWorkspace_name("workspace_name");
		googleTagResourceRequest.setUrl(URL_GTM_2);
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		//System.out.println("testCase12() requestParameters=" + requestParameters);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase12() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase12() googleTagResourceResponse json=" + json);
		assertEquals("testCase12() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000024\",\"error_message\":\"Request parameter schema type is required.\"}}", json);
		System.out.println("testCase12() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase13() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_UPDATE_STRUCTURED_DATA;
		System.out.println("testCase13() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token("gtm_edit_containers_access_token");
		googleTagResourceRequest.setGtm_edit_container_versions_access_token("gtm_edit_container_versions_access_token");
		googleTagResourceRequest.setGtm_publish_access_token("gtm_publish_access_token");
		googleTagResourceRequest.setAccount_id("account_id");
		googleTagResourceRequest.setContainer_id("container_id");
		googleTagResourceRequest.setWorkspace_name("workspace_name");
		googleTagResourceRequest.setUrl(URL_GTM_2);
		googleTagResourceRequest.setSchema_type(SCHEMA_TYPE_LOCAL_BUSINESS);
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		//System.out.println("testCase13() requestParameters=" + requestParameters);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase13() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase13() googleTagResourceResponse json=" + json);
		assertEquals("testCase13() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000014\",\"error_message\":\"Request parameter data is required.\"}}", json);
		System.out.println("testCase13() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase14() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_UPDATE_STRUCTURED_DATA;
		System.out.println("testCase14() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token("gtm_edit_containers_access_token");
		googleTagResourceRequest.setGtm_edit_container_versions_access_token("gtm_edit_container_versions_access_token");
		googleTagResourceRequest.setGtm_publish_access_token("gtm_publish_access_token");
		googleTagResourceRequest.setAccount_id("account_id");
		googleTagResourceRequest.setContainer_id("container_id");
		googleTagResourceRequest.setWorkspace_name("workspace_name");
		googleTagResourceRequest.setUrl(URL_GTM_2);
		googleTagResourceRequest.setSchema_type(SCHEMA_TYPE_LOCAL_BUSINESS);
		googleTagResourceRequest.setData(structuredDataNewLocalBusiness);
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		//System.out.println("testCase14() requestParameters=" + requestParameters);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase14() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase14() googleTagResourceResponse json=" + json);
		assertEquals("testCase14() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000015\",\"error_message\":\"GTM workspace ID cannot be determined for workspace name workspace_name.\"}}",
				json);
		System.out.println("testCase14() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase15() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + "test";
		System.out.println("testCase15() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setUrl(URL_GTM_2);
		googleTagResourceRequest.setSchema_type(SCHEMA_TYPE_LOCAL_BUSINESS);
		googleTagResourceRequest.setData(structuredDataNewLocalBusiness);
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		//System.out.println("testCase15() requestParameters=" + requestParameters);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase15() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase15() googleTagResourceResponse json=" + json);
		assertEquals("testCase15() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000005\",\"error_message\":\"Query command test is invalid.\"}}", json);
		System.out.println("testCase15() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase16() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_UPDATE_STRUCTURED_DATA;
		System.out.println("testCase16() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setUrl(URL_GTM_2);
		googleTagResourceRequest.setSchema_type(SCHEMA_TYPE_LOCAL_BUSINESS);
		googleTagResourceRequest.setData(structuredDataNewLocalBusiness);
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		System.out.println("testCase16() requestParameters=" + requestParameters);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase16() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		assertTrue("testCase16() googleTagResourceResponse.getSuccess() should be true.", googleTagResourceResponse.getSuccess());
		//String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase16() googleTagResourceResponse json=" + json);
		System.out.println("testCase16() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		try {
			Thread.sleep(268);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase17() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_RETRIEVE_STRUCTURED_DATA;
		System.out.println("testCase17() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setUrl(URL_GTM_2);
		googleTagResourceRequest.setSchema_type(SCHEMA_TYPE_LOCAL_BUSINESS);
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		//System.out.println("testCase17() requestParameters=" + requestParameters);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase17() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		assertTrue("testCase17() googleTagResourceResponse.getSuccess() should be true.", googleTagResourceResponse.getSuccess());
		//String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase17() googleTagResourceResponse json=" + json);
		System.out.println("testCase17() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase18() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_UPDATE_STRUCTURED_DATA;
		System.out.println("testCase18() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setUrl(URL_GTM_2);
		googleTagResourceRequest.setSchema_type(SCHEMA_TYPE_PRODUCT);
		googleTagResourceRequest.setData(structuredDataNewProduct);
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		//System.out.println("testCase18() requestParameters=" + requestParameters);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase18() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		assertTrue("testCase18() googleTagResourceResponse.getSuccess() should be true.", googleTagResourceResponse.getSuccess());
		//String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase18() googleTagResourceResponse json=" + json);
		System.out.println("testCase18() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		try {
			Thread.sleep(268);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase19() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_RETRIEVE_STRUCTURED_DATA;
		System.out.println("testCase19() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setUrl(URL_GTM_2);
		googleTagResourceRequest.setSchema_type(SCHEMA_TYPE_PRODUCT);
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		//System.out.println("testCase19() requestParameters=" + requestParameters);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase19() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		assertTrue("testCase19() googleTagResourceResponse.getSuccess() should be true.", googleTagResourceResponse.getSuccess());
		//String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase19() googleTagResourceResponse json=" + json);
		System.out.println("testCase19() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase20() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_UPDATE_STRUCTURED_DATA;
		System.out.println("testCase20() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setUrl(URL_GTM_2);
		googleTagResourceRequest.setSchema_type(SCHEMA_TYPE_LOCAL_BUSINESS);
		googleTagResourceRequest.setData(structuredDataUpdateLocalBusiness);
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		//System.out.println("testCase20() requestParameters=" + requestParameters);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase20() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		assertTrue("testCase20() googleTagResourceResponse.getSuccess() should be true.", googleTagResourceResponse.getSuccess());
		//String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase20() googleTagResourceResponse json=" + json);
		System.out.println("testCase20() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		try {
			Thread.sleep(268);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase21() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_RETRIEVE_STRUCTURED_DATA;
		System.out.println("testCase21() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setUrl(URL_GTM_2);
		googleTagResourceRequest.setSchema_type(SCHEMA_TYPE_LOCAL_BUSINESS);
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		//System.out.println("testCase21() requestParameters=" + requestParameters);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase21() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		assertTrue("testCase21() googleTagResourceResponse.getSuccess() should be true.", googleTagResourceResponse.getSuccess());
		//String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase21() googleTagResourceResponse json=" + json);
		System.out.println("testCase21() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase22() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_UPDATE_STRUCTURED_DATA;
		System.out.println("testCase22() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setUrl(URL_GTM_2);
		googleTagResourceRequest.setSchema_type(SCHEMA_TYPE_PRODUCT);
		googleTagResourceRequest.setData(structuredDataUpdateProduct);
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		//System.out.println("testCase22() requestParameters=" + requestParameters);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase22() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		assertTrue("testCase22() googleTagResourceResponse.getSuccess() should be true.", googleTagResourceResponse.getSuccess());
		//String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase22() googleTagResourceResponse json=" + json);
		System.out.println("testCase22() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		try {
			Thread.sleep(268);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase23() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_RETRIEVE_STRUCTURED_DATA;
		System.out.println("testCase23() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setUrl(URL_GTM_2);
		googleTagResourceRequest.setSchema_type(SCHEMA_TYPE_PRODUCT);
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		//System.out.println("testCase23() requestParameters=" + requestParameters);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase23() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		assertTrue("testCase23() googleTagResourceResponse.getSuccess() should be true.", googleTagResourceResponse.getSuccess());
		//String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase23() googleTagResourceResponse json=" + json);
		System.out.println("testCase23() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase26() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_DELETE_STRUCTURED_DATA;
		System.out.println("testCase26() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setUrl(URL_GTM_2);
		googleTagResourceRequest.setSchema_type(SCHEMA_TYPE_LOCAL_BUSINESS);
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		//System.out.println("testCase26() requestParameters=" + requestParameters);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase26() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		assertTrue("testCase26() googleTagResourceResponse.getSuccess() should be true.", googleTagResourceResponse.getSuccess());
		//String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase26() googleTagResourceResponse json=" + json);
		System.out.println("testCase26() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase27() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_RETRIEVE_STRUCTURED_DATA;
		System.out.println("testCase27() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setUrl(URL_GTM_2);
		googleTagResourceRequest.setSchema_type(SCHEMA_TYPE_LOCAL_BUSINESS);
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		//System.out.println("testCase27() requestParameters=" + requestParameters);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase27() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		assertFalse("testCase27() googleTagResourceResponse.getSuccess() should be false.", googleTagResourceResponse.getSuccess());
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase27() googleTagResourceResponse json=" + json);
		assertEquals("testCase27() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000016\",\"error_message\":\"Data not found.\"}}", json);
		System.out.println("testCase27() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase28() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_DELETE_STRUCTURED_DATA;
		System.out.println("testCase28() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setUrl(URL_GTM_2);
		googleTagResourceRequest.setSchema_type(SCHEMA_TYPE_PRODUCT);
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		//System.out.println("testCase28() requestParameters=" + requestParameters);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase28() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		assertTrue("testCase28() googleTagResourceResponse.getSuccess() should be true.", googleTagResourceResponse.getSuccess());
		//String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase28() googleTagResourceResponse json=" + json);
		System.out.println("testCase28() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase29() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_RETRIEVE_STRUCTURED_DATA;
		System.out.println("testCase29() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setUrl(URL_GTM_2);
		googleTagResourceRequest.setSchema_type(SCHEMA_TYPE_PRODUCT);
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		//System.out.println("testCase29() requestParameters=" + requestParameters);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase29() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		assertFalse("testCase29() googleTagResourceResponse.getSuccess() should be false.", googleTagResourceResponse.getSuccess());
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase29() googleTagResourceResponse json=" + json);
		assertEquals("testCase29() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000016\",\"error_message\":\"Data not found.\"}}", json);
		System.out.println("testCase29() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase30() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_CREATE_JAVASCRIPT;
		System.out.println("testCase30() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase30() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase30() googleTagResourceResponse json=" + json);
		assertEquals("testCase30() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000025\",\"error_message\":\"Request parameter javascript_name is required.\"}}", json);
		System.out.println("testCase30() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase31() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_CREATE_JAVASCRIPT;
		System.out.println("testCase31() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setJavascript_name(JAVASCRIPT_NAME);
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase31() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase31() googleTagResourceResponse json=" + json);
		assertEquals("testCase31() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000026\",\"error_message\":\"Request parameter script_content is required.\"}}", json);
		System.out.println("testCase31() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase32() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_RETRIEVE_JAVASCRIPT;
		System.out.println("testCase32() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase32() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase32() googleTagResourceResponse json=" + json);
		assertEquals("testCase32() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000025\",\"error_message\":\"Request parameter javascript_name is required.\"}}", json);
		System.out.println("testCase32() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase33() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_RETRIEVE_JAVASCRIPT;
		System.out.println("testCase33() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setJavascript_name(JAVASCRIPT_NAME);
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase33() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase33() googleTagResourceResponse json=" + json);
		assertEquals("testCase33() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000028\",\"error_message\":\"Request parameter javascript_name Javascript name 168 not available.\"}}",
				json);
		System.out.println("testCase33() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase34() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_UPDATE_JAVASCRIPT;
		System.out.println("testCase34() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase34() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase34() googleTagResourceResponse json=" + json);
		assertEquals("testCase34() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000025\",\"error_message\":\"Request parameter javascript_name is required.\"}}", json);
		System.out.println("testCase34() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase35() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_UPDATE_JAVASCRIPT;
		System.out.println("testCase35() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setJavascript_name(JAVASCRIPT_NAME);
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase35() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase35() googleTagResourceResponse json=" + json);
		assertEquals("testCase35() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000026\",\"error_message\":\"Request parameter script_content is required.\"}}", json);
		System.out.println("testCase35() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase36() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_UPDATE_JAVASCRIPT;
		System.out.println("testCase36() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setJavascript_name(JAVASCRIPT_NAME);
		googleTagResourceRequest.setScript_content("document.title = 'title after change';");
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase36() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase36() googleTagResourceResponse json=" + json);
		assertEquals("testCase36() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000028\",\"error_message\":\"Request parameter javascript_name Javascript name 168 not available.\"}}",
				json);
		System.out.println("testCase36() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase37() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_UPDATE_JAVASCRIPT_URL;
		System.out.println("testCase37() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase37() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase37() googleTagResourceResponse json=" + json);
		assertEquals("testCase37() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000025\",\"error_message\":\"Request parameter javascript_name is required.\"}}", json);
		System.out.println("testCase37() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase38() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_RETRIEVE_JAVASCRIPT_URL;
		System.out.println("testCase38() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase38() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase38() googleTagResourceResponse json=" + json);
		assertEquals("testCase38() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000025\",\"error_message\":\"Request parameter javascript_name is required.\"}}", json);
		System.out.println("testCase38() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase39() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_UPDATE_JAVASCRIPT_URL;
		System.out.println("testCase39() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase39() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase39() googleTagResourceResponse json=" + json);
		assertEquals("testCase39() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000025\",\"error_message\":\"Request parameter javascript_name is required.\"}}", json);
		System.out.println("testCase39() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase40() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_UPDATE_JAVASCRIPT_URL;
		System.out.println("testCase40() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setJavascript_name(JAVASCRIPT_NAME);
		googleTagResourceRequest.setScript_content("document.title = 'title after change';");
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase40() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase40() googleTagResourceResponse json=" + json);
		assertEquals("testCase40() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000028\",\"error_message\":\"Request parameter javascript_name Javascript name 168 not available.\"}}",
				json);
		System.out.println("testCase40() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase41() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_RETRIEVE_JAVASCRIPT_URL;
		System.out.println("testCase41() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setJavascript_name(JAVASCRIPT_NAME);
		googleTagResourceRequest.setScript_content("document.title = 'title after change';");
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase41() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase41() googleTagResourceResponse json=" + json);
		assertEquals("testCase41() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000028\",\"error_message\":\"Request parameter javascript_name Javascript name 168 not available.\"}}",
				json);
		System.out.println("testCase41() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase42() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_UPDATE_JAVASCRIPT_URL;
		System.out.println("testCase42() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setJavascript_name(JAVASCRIPT_NAME);
		googleTagResourceRequest.setScript_content("document.title = 'title after change';");
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase42() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase42() googleTagResourceResponse json=" + json);
		assertEquals("testCase42() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000028\",\"error_message\":\"Request parameter javascript_name Javascript name 168 not available.\"}}",
				json);
		System.out.println("testCase42() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase43() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_DELETE_JAVASCRIPT;
		System.out.println("testCase43() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase43() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase43() googleTagResourceResponse json=" + json);
		assertEquals("testCase43() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000025\",\"error_message\":\"Request parameter javascript_name is required.\"}}", json);
		System.out.println("testCase43() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase44() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_DELETE_JAVASCRIPT;
		System.out.println("testCase44() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setJavascript_name(JAVASCRIPT_NAME);
		googleTagResourceRequest.setScript_content("document.title = 'title after change';");
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase44() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase44() googleTagResourceResponse json=" + json);
		assertEquals("testCase44() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000028\",\"error_message\":\"Request parameter javascript_name Javascript name 168 not available.\"}}",
				json);
		System.out.println("testCase44() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase45() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_CREATE_JAVASCRIPT;
		System.out.println("testCase45() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setJavascript_name(JAVASCRIPT_NAME);
		googleTagResourceRequest.setScript_content("<script>document.title = 'title after change';</script>");
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase45() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		assertEquals("testCase45() googleTagResourceResponse.getSuccess() incorrect.", false, googleTagResourceResponse.getSuccess());
		assertNotNull("testCase45() googleTagResourceResponse.getError() should not be null.", googleTagResourceResponse.getError());
		assertEquals("testCase45() googleTagResourceResponse.getError().getError_code() incorrect.", "000035", googleTagResourceResponse.getError().getError_code());
		System.out.println("testCase45() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase46() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_CREATE_JAVASCRIPT;
		System.out.println("testCase46() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setJavascript_name(JAVASCRIPT_NAME);
		googleTagResourceRequest.setScript_content("document.title = 'title after change';</script>");
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase46() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		assertEquals("testCase46() googleTagResourceResponse.getSuccess() incorrect.", false, googleTagResourceResponse.getSuccess());
		assertNotNull("testCase46() googleTagResourceResponse.getError() should not be null.", googleTagResourceResponse.getError());
		assertEquals("testCase46() googleTagResourceResponse.getError().getError_code() incorrect.", "000036", googleTagResourceResponse.getError().getError_code());
		System.out.println("testCase46() passed.");
		try {
			Thread.sleep(268);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase47() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_CREATE_JAVASCRIPT;
		System.out.println("testCase47() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setJavascript_name(JAVASCRIPT_NAME);
		googleTagResourceRequest.setScript_content("document.title = 'title after change';");
		googleTagResourceRequest.setDomain_id(9632);
		googleTagResourceRequest.setUser_id(214);
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase47() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		assertEquals("testCase47() googleTagResourceResponse.getSuccess() incorrect.", true, googleTagResourceResponse.getSuccess());
		assertNull("testCase47() googleTagResourceResponse.getError() should be null.", googleTagResourceResponse.getError());

		GoogleTagJavascriptEntity googleTagJavascriptEntity = googleTagJavascriptDAO.get(googleTagResourceRequest.getAccount_id(),
				googleTagResourceRequest.getContainer_id(), googleTagResourceRequest.getWorkspace_name(), googleTagResourceRequest.getJavascript_name());
		assertNotNull("testCase47() googleTagJavascriptEntity should not be null.", googleTagJavascriptEntity);

		System.out.println("testCase47() passed.");
		try {
			Thread.sleep(268);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase48() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_CREATE_JAVASCRIPT;
		System.out.println("testCase48() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setJavascript_name(JAVASCRIPT_NAME);
		googleTagResourceRequest.setScript_content("document.title = 'title after change';");
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase48() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		assertEquals("testCase48() googleTagResourceResponse.getSuccess() incorrect.", false, googleTagResourceResponse.getSuccess());
		assertNotNull("testCase48() googleTagResourceResponse.getError() should not be null.", googleTagResourceResponse.getError());
		assertEquals("testCase48() googleTagResourceResponse.getError().getError_code() incorrect.", "000027", googleTagResourceResponse.getError().getError_code());
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase44() googleTagResourceResponse json=" + json);
		assertEquals("testCase48() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000027\",\"error_message\":\"Request parameter javascript_name Javascript name 168 already created.\"}}",
				json);
		System.out.println("testCase48() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase49() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_UPDATE_JAVASCRIPT_URL;
		System.out.println("testCase49() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setJavascript_name(JAVASCRIPT_NAME);
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase49() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		assertEquals("testCase49() googleTagResourceResponse.getSuccess() incorrect.", false, googleTagResourceResponse.getSuccess());
		assertNotNull("testCase49() googleTagResourceResponse.getError() should not be null.", googleTagResourceResponse.getError());
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase44() googleTagResourceResponse json=" + json);
		assertEquals("testCase49() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000029\",\"error_message\":\"Request parameter create_url_array or delete_url_array is required and cannot be empty.\"}}",
				json);
		System.out.println("testCase49() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase50() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_UPDATE_JAVASCRIPT_URL;
		System.out.println("testCase50() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setJavascript_name(JAVASCRIPT_NAME);
		googleTagResourceRequest.setCreate_url_array(new String[] { "test_url" });
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase50() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		assertEquals("testCase50() googleTagResourceResponse.getSuccess() incorrect.", false, googleTagResourceResponse.getSuccess());
		assertNotNull("testCase50() googleTagResourceResponse.getError() should not be null.", googleTagResourceResponse.getError());
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase44() googleTagResourceResponse json=" + json);
		assertEquals("testCase50() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000038\",\"error_message\":\"Request parameter create_url_array URL test_url is invalid.\"}}", json);
		System.out.println("testCase50() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase51() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_UPDATE_JAVASCRIPT_URL;
		System.out.println("testCase51() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setJavascript_name(JAVASCRIPT_NAME);
		googleTagResourceRequest.setCreate_url_array(new String[] { "https://test.edgeseo.dev/*********/page_1.html" });
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase51() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		assertEquals("testCase51() googleTagResourceResponse.getSuccess() incorrect.", true, googleTagResourceResponse.getSuccess());
		assertNull("testCase51() googleTagResourceResponse.getError() should be null.", googleTagResourceResponse.getError());

		GoogleTagJavascriptEntity googleTagJavascriptEntity = googleTagJavascriptDAO.get(googleTagResourceRequest.getAccount_id(),
				googleTagResourceRequest.getContainer_id(), googleTagResourceRequest.getWorkspace_name(), googleTagResourceRequest.getJavascript_name());
		assertNotNull("testCase51() googleTagJavascriptEntity should not be null.", googleTagJavascriptEntity);
		Long javascriptId = googleTagJavascriptEntity.getId();
		String hashCode = Md5Util.Md5("https://test.edgeseo.dev/*********/page_1.html");
		GoogleTagJavascriptUrlEntity googleTagJavascriptUrlEntity = googleTagJavascriptUrlDAO.get(javascriptId, hashCode);
		assertNotNull("testCase51() googleTagJavascriptUrlEntity should not be null.", googleTagJavascriptUrlEntity);

		System.out.println("testCase51() passed.");
		try {
			Thread.sleep(268);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase52() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_UPDATE_JAVASCRIPT_URL;
		System.out.println("testCase52() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setJavascript_name(JAVASCRIPT_NAME);
		googleTagResourceRequest.setCreate_url_array(new String[] { "https://test.edgeseo.dev/*********/page_1.html" });
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase52() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		assertEquals("testCase52() googleTagResourceResponse.getSuccess() incorrect.", false, googleTagResourceResponse.getSuccess());
		assertNotNull("testCase52() googleTagResourceResponse.getError() should not be null.", googleTagResourceResponse.getError());
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase44() googleTagResourceResponse json=" + json);
		assertEquals("testCase52() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000033\",\"error_message\":\"URL https://test.edgeseo.dev/*********/page_1.html already exists.\"}}",
				json);
		System.out.println("testCase52() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase53() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_UPDATE_JAVASCRIPT_URL;
		System.out.println("testCase53() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setJavascript_name(JAVASCRIPT_NAME);
		googleTagResourceRequest.setDelete_url_array(new String[] { "test_url" });
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase53() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		assertEquals("testCase53() googleTagResourceResponse.getSuccess() incorrect.", false, googleTagResourceResponse.getSuccess());
		assertNotNull("testCase53() googleTagResourceResponse.getError() should not be null.", googleTagResourceResponse.getError());
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase44() googleTagResourceResponse json=" + json);
		assertEquals("testCase53() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000030\",\"error_message\":\"Request parameter delete_url_array URL test_url is invalid.\"}}", json);
		System.out.println("testCase53() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase54() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_UPDATE_JAVASCRIPT_URL;
		System.out.println("testCase54() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setJavascript_name(JAVASCRIPT_NAME);
		googleTagResourceRequest.setDelete_url_array(new String[] { "https://test.edgeseo.dev/*********/page_2.html" });
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase54() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		assertEquals("testCase54() googleTagResourceResponse.getSuccess() incorrect.", false, googleTagResourceResponse.getSuccess());
		assertNotNull("testCase54() googleTagResourceResponse.getError() should not be null.", googleTagResourceResponse.getError());
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase44() googleTagResourceResponse json=" + json);
		assertEquals("testCase54() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000032\",\"error_message\":\"URL https://test.edgeseo.dev/*********/page_2.html does not exist.\"}}",
				json);
		System.out.println("testCase54() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase55() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_RETRIEVE_JAVASCRIPT_URL;
		System.out.println("testCase55() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setJavascript_name(JAVASCRIPT_NAME);
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase55() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		assertEquals("testCase55() googleTagResourceResponse.getSuccess() incorrect.", true, googleTagResourceResponse.getSuccess());
		assertNull("testCase55() googleTagResourceResponse.getError() should be null.", googleTagResourceResponse.getError());
		System.out.println("testCase55() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase56() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_UPDATE_JAVASCRIPT_URL;
		System.out.println("testCase56() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setJavascript_name(JAVASCRIPT_NAME);
		googleTagResourceRequest.setDelete_url_array(new String[] { "https://test.edgeseo.dev/*********/page_1.html" });
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase56() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		assertEquals("testCase56() googleTagResourceResponse.getSuccess() incorrect.", true, googleTagResourceResponse.getSuccess());
		assertNull("testCase56() googleTagResourceResponse.getError() should be null.", googleTagResourceResponse.getError());

		GoogleTagJavascriptEntity googleTagJavascriptEntity = googleTagJavascriptDAO.get(googleTagResourceRequest.getAccount_id(),
				googleTagResourceRequest.getContainer_id(), googleTagResourceRequest.getWorkspace_name(), googleTagResourceRequest.getJavascript_name());
		assertNotNull("testCase56() googleTagJavascriptEntity should not be null.", googleTagJavascriptEntity);
		Long javascriptId = googleTagJavascriptEntity.getId();
		String hashCode = Md5Util.Md5("https://test.edgeseo.dev/*********/page_1.html");
		GoogleTagJavascriptUrlEntity googleTagJavascriptUrlEntity = googleTagJavascriptUrlDAO.get(javascriptId, hashCode);
		assertNull("testCase56() googleTagJavascriptUrlEntity should be null.", googleTagJavascriptUrlEntity);

		System.out.println("testCase56() passed.");
		try {
			Thread.sleep(268);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase57() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_UPDATE_JAVASCRIPT_URL;
		System.out.println("testCase57() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setJavascript_name(JAVASCRIPT_NAME);
		googleTagResourceRequest.setDelete_url_array(new String[] { "https://test.edgeseo.dev/*********/page_1.html" });
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase57() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		assertEquals("testCase57() googleTagResourceResponse.getSuccess() incorrect.", false, googleTagResourceResponse.getSuccess());
		assertNotNull("testCase57() googleTagResourceResponse.getError() should not be null.", googleTagResourceResponse.getError());
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase44() googleTagResourceResponse json=" + json);
		assertEquals("testCase54() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000031\",\"error_message\":\"This are no URLs for the request parameter javascript_name Javascript name 168.\"}}",
				json);
		System.out.println("testCase57() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase58() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_RETRIEVE_JAVASCRIPT_URL;
		System.out.println("testCase58() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setJavascript_name(JAVASCRIPT_NAME);
		googleTagResourceRequest.setDelete_url_array(new String[] { "https://test.edgeseo.dev/*********/page_1.html" });
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase58() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		assertEquals("testCase58() googleTagResourceResponse.getSuccess() incorrect.", false, googleTagResourceResponse.getSuccess());
		assertNotNull("testCase58() googleTagResourceResponse.getError() should not be null.", googleTagResourceResponse.getError());
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase44() googleTagResourceResponse json=" + json);
		assertEquals("testCase54() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000031\",\"error_message\":\"This are no URLs for the request parameter javascript_name Javascript name 168.\"}}",
				json);
		System.out.println("testCase58() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase59() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_UPDATE_JAVASCRIPT;
		System.out.println("testCase59() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setJavascript_name(JAVASCRIPT_NAME);
		googleTagResourceRequest.setScript_content("document.title = 'title updated';");
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase59() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		assertEquals("testCase59() googleTagResourceResponse.getSuccess() incorrect.", true, googleTagResourceResponse.getSuccess());
		assertNull("testCase59() googleTagResourceResponse.getError() should be null.", googleTagResourceResponse.getError());
		GoogleTagJavascriptEntity googleTagJavascriptEntity = googleTagJavascriptDAO.get(googleTagResourceRequest.getAccount_id(),
				googleTagResourceRequest.getContainer_id(), googleTagResourceRequest.getWorkspace_name(), googleTagResourceRequest.getJavascript_name());
		assertNotNull("testCase59() googleTagJavascriptEntity should not be null.", googleTagJavascriptEntity);
		assertEquals("testCase59() googleTagJavascriptEntity.getScriptContent() incorrect.", "document.title = 'title updated';",
				googleTagJavascriptEntity.getScriptContent());

		System.out.println("testCase59() passed.");
		try {
			Thread.sleep(268);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase60() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_UPDATE_JAVASCRIPT_URL;
		System.out.println("testCase60() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setJavascript_name(JAVASCRIPT_NAME);
		googleTagResourceRequest.setCreate_url_array(new String[] { "https://test.edgeseo.dev/*********/page_1.html", "https://test.edgeseo.dev/*********/page_2.html",
				"https://test.edgeseo.dev/*********/page_3.html" });
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase60() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		assertEquals("testCase60() googleTagResourceResponse.getSuccess() incorrect.", true, googleTagResourceResponse.getSuccess());
		assertNull("testCase60() googleTagResourceResponse.getError() should be null.", googleTagResourceResponse.getError());

		GoogleTagJavascriptEntity googleTagJavascriptEntity = googleTagJavascriptDAO.get(googleTagResourceRequest.getAccount_id(),
				googleTagResourceRequest.getContainer_id(), googleTagResourceRequest.getWorkspace_name(), googleTagResourceRequest.getJavascript_name());
		assertNotNull("testCase60() googleTagJavascriptEntity should not be null.", googleTagJavascriptEntity);
		Long javascriptId = googleTagJavascriptEntity.getId();
		String hashCode = Md5Util.Md5("https://test.edgeseo.dev/*********/page_1.html");
		GoogleTagJavascriptUrlEntity googleTagJavascriptUrlEntity = googleTagJavascriptUrlDAO.get(javascriptId, hashCode);
		assertNotNull("testCase60() googleTagJavascriptUrlEntity 1 should not be null.", googleTagJavascriptUrlEntity);

		hashCode = Md5Util.Md5("https://test.edgeseo.dev/*********/page_2.html");
		googleTagJavascriptUrlEntity = googleTagJavascriptUrlDAO.get(javascriptId, hashCode);
		assertNotNull("testCase60() googleTagJavascriptUrlEntity 2 should not be null.", googleTagJavascriptUrlEntity);

		hashCode = Md5Util.Md5("https://test.edgeseo.dev/*********/page_3.html");
		googleTagJavascriptUrlEntity = googleTagJavascriptUrlDAO.get(javascriptId, hashCode);
		assertNotNull("testCase60() googleTagJavascriptUrlEntity 3 should not be null.", googleTagJavascriptUrlEntity);

		System.out.println("testCase60() passed.");
		try {
			Thread.sleep(268);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase61() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_UPDATE_JAVASCRIPT_URL;
		System.out.println("testCase61() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setJavascript_name(JAVASCRIPT_NAME);
		googleTagResourceRequest.setDelete_url_array(new String[] { "https://test.edgeseo.dev/*********/page_2.html" });
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase61() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		assertEquals("testCase61() googleTagResourceResponse.getSuccess() incorrect.", true, googleTagResourceResponse.getSuccess());
		assertNull("testCase61() googleTagResourceResponse.getError() should be null.", googleTagResourceResponse.getError());

		GoogleTagJavascriptEntity googleTagJavascriptEntity = googleTagJavascriptDAO.get(googleTagResourceRequest.getAccount_id(),
				googleTagResourceRequest.getContainer_id(), googleTagResourceRequest.getWorkspace_name(), googleTagResourceRequest.getJavascript_name());
		assertNotNull("testCase61() googleTagJavascriptEntity should not be null.", googleTagJavascriptEntity);
		Long javascriptId = googleTagJavascriptEntity.getId();
		String hashCode = Md5Util.Md5("https://test.edgeseo.dev/*********/page_1.html");
		GoogleTagJavascriptUrlEntity googleTagJavascriptUrlEntity = googleTagJavascriptUrlDAO.get(javascriptId, hashCode);
		assertNotNull("testCase61() googleTagJavascriptUrlEntity 1 should not be null.", googleTagJavascriptUrlEntity);

		hashCode = Md5Util.Md5("https://test.edgeseo.dev/*********/page_2.html");
		googleTagJavascriptUrlEntity = googleTagJavascriptUrlDAO.get(javascriptId, hashCode);
		assertNull("testCase61() googleTagJavascriptUrlEntity 2 should be null.", googleTagJavascriptUrlEntity);

		hashCode = Md5Util.Md5("https://test.edgeseo.dev/*********/page_3.html");
		googleTagJavascriptUrlEntity = googleTagJavascriptUrlDAO.get(javascriptId, hashCode);
		assertNotNull("testCase61() googleTagJavascriptUrlEntity 3 should not be null.", googleTagJavascriptUrlEntity);

		System.out.println("testCase61() passed.");
		try {
			Thread.sleep(268);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase62() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_RETRIEVE_JAVASCRIPT;
		System.out.println("testCase62() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setJavascript_name(JAVASCRIPT_NAME);
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase62() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		assertEquals("testCase62() googleTagResourceResponse.getSuccess() incorrect.", true, googleTagResourceResponse.getSuccess());
		assertNull("testCase62() googleTagResourceResponse.getError() should be null.", googleTagResourceResponse.getError());
		System.out.println("testCase62() passed.");
		try {
			Thread.sleep(168);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase63() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_UPDATE_JAVASCRIPT_URL;
		System.out.println("testCase63() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setJavascript_name(JAVASCRIPT_NAME);
		googleTagResourceRequest
				.setCreate_url_array(new String[] { "https://test.edgeseo.dev/*********/page_6.html", "https://test.edgeseo.dev/*********/page_8.html" });
		googleTagResourceRequest.setDelete_url_array(new String[] { "https://test.edgeseo.dev/*********/page_3.html" });
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase63() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		assertEquals("testCase63() googleTagResourceResponse.getSuccess() incorrect.", true, googleTagResourceResponse.getSuccess());
		assertNull("testCase63() googleTagResourceResponse.getError() should be null.", googleTagResourceResponse.getError());

		GoogleTagJavascriptEntity googleTagJavascriptEntity = googleTagJavascriptDAO.get(googleTagResourceRequest.getAccount_id(),
				googleTagResourceRequest.getContainer_id(), googleTagResourceRequest.getWorkspace_name(), googleTagResourceRequest.getJavascript_name());
		assertNotNull("testCase63() googleTagJavascriptEntity should not be null.", googleTagJavascriptEntity);
		Long javascriptId = googleTagJavascriptEntity.getId();

		String hashCode = Md5Util.Md5("https://test.edgeseo.dev/*********/page_1.html");
		GoogleTagJavascriptUrlEntity googleTagJavascriptUrlEntity = googleTagJavascriptUrlDAO.get(javascriptId, hashCode);
		assertNotNull("testCase63() googleTagJavascriptUrlEntity 1 should not be null.", googleTagJavascriptUrlEntity);

		hashCode = Md5Util.Md5("https://test.edgeseo.dev/*********/page_2.html");
		googleTagJavascriptUrlEntity = googleTagJavascriptUrlDAO.get(javascriptId, hashCode);
		assertNull("testCase63() googleTagJavascriptUrlEntity 2 should be null.", googleTagJavascriptUrlEntity);

		hashCode = Md5Util.Md5("https://test.edgeseo.dev/*********/page_3.html");
		googleTagJavascriptUrlEntity = googleTagJavascriptUrlDAO.get(javascriptId, hashCode);
		assertNull("testCase63() googleTagJavascriptUrlEntity 3 should be null.", googleTagJavascriptUrlEntity);

		hashCode = Md5Util.Md5("https://test.edgeseo.dev/*********/page_6.html");
		googleTagJavascriptUrlEntity = googleTagJavascriptUrlDAO.get(javascriptId, hashCode);
		assertNotNull("testCase63() googleTagJavascriptUrlEntity 6 should not be null.", googleTagJavascriptUrlEntity);

		hashCode = Md5Util.Md5("https://test.edgeseo.dev/*********/page_8.html");
		googleTagJavascriptUrlEntity = googleTagJavascriptUrlDAO.get(javascriptId, hashCode);
		assertNotNull("testCase63() googleTagJavascriptUrlEntity 8 should not be null.", googleTagJavascriptUrlEntity);

		System.out.println("testCase63() passed.");
		try {
			Thread.sleep(268);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase64() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_LIST_JAVASCRIPTS;
		System.out.println("testCase63() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase64() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		assertEquals("testCase64() googleTagResourceResponse.getSuccess() incorrect.", true, googleTagResourceResponse.getSuccess());
		assertNull("testCase64() googleTagResourceResponse.getError() should be null.", googleTagResourceResponse.getError());
		System.out.println("testCase64() passed.");
		try {
			Thread.sleep(268);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase65() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_DELETE_JAVASCRIPT;
		System.out.println("testCase63() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setJavascript_name(JAVASCRIPT_NAME);
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase65() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		assertEquals("testCase65() googleTagResourceResponse.getSuccess() incorrect.", true, googleTagResourceResponse.getSuccess());
		assertNull("testCase65() googleTagResourceResponse.getError() should be null.", googleTagResourceResponse.getError());

		GoogleTagJavascriptEntity googleTagJavascriptEntity = googleTagJavascriptDAO.get(googleTagResourceRequest.getAccount_id(),
				googleTagResourceRequest.getContainer_id(), googleTagResourceRequest.getWorkspace_name(), googleTagResourceRequest.getJavascript_name());
		assertNull("testCase65() googleTagJavascriptEntity should be null.", googleTagJavascriptEntity);

		System.out.println("testCase65() passed.");
		try {
			Thread.sleep(268);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase66() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_CREATE_JAVASCRIPT;
		System.out.println("testCase66() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setJavascript_name(JAVASCRIPT_NAME);
		googleTagResourceRequest.setScript_content("document.title = 'title after change';");
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase66() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		assertEquals("testCase66() googleTagResourceResponse.getSuccess() incorrect.", false, googleTagResourceResponse.getSuccess());
		assertNotNull("testCase66() googleTagResourceResponse.getError() should not be null.", googleTagResourceResponse.getError());
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase44() googleTagResourceResponse json=" + json);
		assertEquals("testCase66() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000039\",\"error_message\":\"Request parameter domain_id is required.\"}}", json);
		System.out.println("testCase66() passed.");
		try {
			Thread.sleep(268);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	private void testCase67() throws Exception {
		String requestUrl = WEB_SERVICE_ENDPOINT + MainWebServiceClientService.ROUTER_GOOGLE_TAG + IConstants.COMMAND_CREATE_JAVASCRIPT;
		System.out.println("testCase67() requestUrl=" + requestUrl);
		GoogleTagResourceRequest googleTagResourceRequest = new GoogleTagResourceRequest();
		googleTagResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		googleTagResourceRequest.setGtm_edit_containers_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINERS_ACCESS_TOKEN));
		googleTagResourceRequest.setGtm_edit_container_versions_access_token(
				GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_EDIT_CONTAINER_VERSIONS_ACCESS_TOKEN));
		googleTagResourceRequest
				.setGtm_publish_access_token(GoogleTagUtils.getInstance().getGoogleAccessToken(IConstants.RUNTIME_PROPERTY_NAME_GOOGLE_TAG_API_PUBLISH_ACCESS_TOKEN));
		googleTagResourceRequest.setAccount_id(ACCOUNT_ID);
		googleTagResourceRequest.setContainer_id(CONTAINER_ID);
		googleTagResourceRequest.setWorkspace_name(WORKSPACE_NAME);
		googleTagResourceRequest.setJavascript_name(JAVASCRIPT_NAME);
		googleTagResourceRequest.setScript_content("document.title = 'title after change';");
		googleTagResourceRequest.setDomain_id(9632);
		String requestParameters = new Gson().toJson(googleTagResourceRequest, GoogleTagResourceRequest.class);
		GoogleTagResourceResponse googleTagResourceResponse = mainWebServiceClientService.maintainStructuredData(requestUrl, requestParameters);
		assertNotNull("testCase67() googleTagResourceResponse should not be null.", googleTagResourceResponse);
		assertEquals("testCase67() googleTagResourceResponse.getSuccess() incorrect.", false, googleTagResourceResponse.getSuccess());
		assertNotNull("testCase67() googleTagResourceResponse.getError() should not be null.", googleTagResourceResponse.getError());
		String json = new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class);
		//System.out.println("testCase44() googleTagResourceResponse json=" + json);
		assertEquals("testCase67() GoogleTagResourceResponse JSON incorrect.",
				"{\"success\":false,\"error\":{\"error_code\":\"000040\",\"error_message\":\"Request parameter user_id is required.\"}}", json);
		System.out.println("testCase67() passed.");
		try {
			Thread.sleep(268);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

}