package com.actonia.test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import org.apache.commons.lang.BooleanUtils;

import com.actonia.IConstants;
import com.actonia.service.AccessTokenService;
import com.actonia.service.PoliteCrawlWebServiceClientService;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.ProphetRequest;
import com.actonia.value.object.ProphetResponse;
import com.google.gson.Gson;

public class ProphetResourceTest {

	private PoliteCrawlWebServiceClientService politeCrawlWebServiceClientService;

	public ProphetResourceTest() {
		super();
		this.politeCrawlWebServiceClientService = SpringBeanFactory.getBean("politeCrawlWebServiceClientService");
	}

	public static void main(String[] args) throws Exception {
		new ProphetResourceTest().runTests();
	}

	private void runTests() throws Exception {
		testCase1();
		testCase2();
		testCase3();
		testCase4();
		testCase5();
		testCase6();
		testCase7();
		testCase8();
		testCase9();
		testCase10();
		testCase11();
		testCase12();
	}

	private Double[] getDoubleArrayForProphet() {
		return new Double[] { 9.59076113897809, 8.51959031601596, 8.18367658262066, 8.07246736935477, 7.8935720735049, 7.78364059622125, 8.41405243249672,
				8.82922635473185, 8.38251828808963, 8.06965530688617, 7.87929148508227, 7.76174498465891, 7.52940645783701, 8.38526052015541, 8.62011072542292,
				7.85243908535751, 7.85399308722424, 8.0519780789023, 7.92660259918138, 7.83834331555712, 9.70314458114435, 9.38597294061934, 8.29379960884682,
				8.43468076984177, 8.26204284396694, 8.10681603894705, 7.95014988765202, 9.50925907635395, 8.84678466694523, 8.43054538469057, 8.2482674474469,
				8.28172399041139, 8.29279885820037, 8.19918935907807, 9.99652241850332, 10.1270710070787, 8.93379604393486, 8.56617381363786, 8.54772239645106,
				8.39976009452414, 8.22309055116153, 8.83898679349679, 10.8972021813751, 9.44493807333551, 8.92332474406756, 8.5434455625603, 8.49556089128912,
				8.41737285613403, 8.57262789830434, 8.73648935100155, 8.63408694288774, 8.67351294567119, 8.82423661734664, 8.53797573059877, 9.69806112202708,
				12.0974568371517, 10.6352783566883, 9.69171658751689, 9.31560088263368, 8.97081334141145, 8.58914169072882, 8.61740045183326, 8.61631428228404,
				8.21554741194707, 8.06495089174914, 8.11342663994365, 7.79934339821592, 7.6275443904885, 7.55590509361135, 7.71154897962915, 7.78862606562503,
				7.70841066725737, 7.76853330092603, 7.69530313496357, 7.37838371299671, 7.91059061225648, 7.62657020629066, 7.57353126274595, 7.56786260546388,
				7.5522372875608, 7.33693691370762, 8.12474302038557, 7.88758403166028, 7.81963630236759, 7.38398945797851, 7.81439963380449, 7.5422134631934,
				7.54855597916987, 7.6889133368648, 7.4770384723197, 7.35883089834235, 7.03262426102801, 7.11801620446533, 7.34987370473834, 7.32646561384032,
				7.36391350140582, 7.27793857294566, 7.25134498337221, 7.00215595440362, 7.16394668434255, 7.7591874385078, 7.51860721681525, 7.41397029019044,
				7.44249272279444, 7.2283884515736, 6.99117688712121, 7.2115567333138, 7.31121838441963, 7.34923082461333, 7.51425465281641, 7.39326309476384,
				7.28619171470238, 7.27309259599952, 7.16857989726403, 7.61134771740362, 7.61775957660851, 7.42595365707754, 8.18200013629341, 7.39939808333135,
				7.1066061377273, 7.08086789669078, 7.42117752859539, 7.49164547360513, 7.38585107812521, 7.68294316987829, 7.6889133368648, 7.2591161280971,
				8.14496941708788, 8.71391062849392, 8.09101504171053, 7.84031298332016, 7.64873978895624, 8.02092771898158, 7.83002808253384, 7.72753511047545,
				7.83597458172157, 7.62657020629066, 7.64444076155657, 7.54855597916987, 7.44073370738926, 7.07326971745971, 7.02642680869964, 7.39203156751459,
				7.29301767977278, 7.36137542897735, 7.51261754467451, 7.46049030582534, 7.11476944836646, 7.12528309151071, 7.45587668749182, 7.31721240835984,
				7.41034709782102, 7.40245152081824, 7.29437729928882, 6.98933526597456, 6.99301512293296, 7.4312996751559, 7.36201055125973, 7.454719949364,
				7.36833968631138, 7.14361760270412, 6.8351845861473, 6.89060912014717, 6.97447891102505, 7.26612877955645, 7.27031288607902, 7.26542972325395,
				7.15773548424991, 6.86589107488344, 7.15383380157884, 7.21670948670946, 7.20191631753163, 7.21229446850034, 7.18614430452233, 6.82001636467413,
				6.88243747099785, 7.18311170174328, 7.24279792279376, 7.1929342212158, 7.18387071506245, 6.97073007814353, 6.9177056098353, 7.22256601882217,
				7.35691824235602, 7.24279792279376, 7.22329567956231, 7.16317239084664, 7.24136628332232, 6.98656645940643, 6.96318998587024, 7.25063551189868,
				7.1608459066643, 7.13249755166004, 7.09174211509515, 7.01211529430638, 6.85751406254539, 6.82762923450285, 7.13009851012558, 7.27724772663148,
				7.03878354138854, 6.89060912014717, 6.95749737087695, 7.29573507274928, 7.49720722320332, 7.31055015853442, 7.20489251020467, 7.22256601882217,
				7.25770767716004, 7.42595365707754, 7.11720550316434, 7.24992553671799, 8.27690348126706, 7.60638738977265, 7.64396194900253, 8.01234963932779,
				7.93020620668468, 7.50878717063428, 7.52131798019924, 7.58984151218266, 7.50052948539529, 7.37023064180708, 7.38523092306657, 7.30249642372733,
				7.27517231945277, 7.39203156751459, 7.51697722460432, 7.90912218321141, 7.72312009226633, 7.67042852219069, 7.62900388965296, 7.43070708254597,
				7.57967882309046, 7.9483852851119, 7.79564653633459, 8.00736706798333, 7.79069603117474, 7.83280751652486, 7.51479976048867, 7.6275443904885,
				7.85515700588134, 7.96485088744731, 7.74586822979227, 8.08085641964099, 8.25997565976828, 7.7698009960039, 8.17751582384608, 9.28173036806286,
				8.33854487998858, 7.83042561782033, 7.8087293067444, 7.81681996576455, 7.55485852104068, 8.55506684384432, 8.20794694104862, 8.08363720314155,
				7.79110951061003, 7.67089483136212, 7.64012317269536, 7.55013534248843, 8.24931374626064, 8.3039999709552, 8.12681372072611, 7.70616297019958,
				7.66387725870347, 7.52671756135271, 7.92588031673756, 7.92153563213355, 8.03398273468322, 7.72577144158795, 7.69439280262942, 7.44949800538285,
				7.39141523467536, 7.34601020991329, 8.10137467122858, 8.15651022607997, 7.97108575350561, 7.87283617502572, 7.63530388625941, 7.58781721999343,
				7.34213173058472, 8.17751582384608, 8.34093322600088, 8.47657950853094, 7.87359778968554, 7.71735127218533, 7.49052940206071, 7.37588214821501,
				8.07558263667172, 8.16536363247398, 7.58528107863913, 7.60738142563979, 7.26752542782817, 8.04012466444838, 8.10922495308995, 8.80687326653069,
				7.74716496652033, 7.48099216286952, 7.34665516317654, 7.24708058458576, 7.93808872689695, 9.03562977818356, 8.04109100370863, 7.40610338123702,
				7.97384437594469, 7.76811037852599, 7.4713630881871, 8.03008409426756, 8.72939712269206, 7.93701748951545, 7.66528471847135, 7.58018941794454,
				7.7106533235012, 7.26122509197192, 8.04654935728308, 8.09346227450118, 7.76726399675731, 7.49665243816828, 7.5522372875608, 7.49720722320332,
				7.3125534981026, 7.93880224815448, 9.05870319731322, 8.19422930481982, 7.51914995766982, 7.55118686729615, 7.71378461659875, 7.60589000105312,
				8.49902922078857, 8.29179710504873, 7.89469085042562, 7.79028238070348, 7.65539064482615, 7.61035761831284, 7.53101633207792, 8.23137604557397,
				8.00670084544037, 7.85864065562079, 7.69712131728263, 7.59588991771854, 7.73587031995257, 7.35115822643069, 8.03138533062553, 8.39434736141739,
				7.82364593083495, 8.08671792030391, 7.77148876011762, 8.68895923427068, 7.74716496652033, 7.96067260838812, 8.62461158818351, 7.99665387546261,
				7.62070508683826, 7.4318919168078, 7.46278915741245, 7.4489161025442, 8.25140306538056, 8.55525939222269, 8.31581113188354, 8.30992298925832,
				7.75876054415766, 8.80821966511841, 9.12194622121359, 10.1538181636943, 9.26785427817679, 8.43424627059531, 8.13768818497761, 8.04494704961772,
				8.22897764335831, 8.14118979345769, 9.21562637640542, 8.73278832497312, 8.51016857647927, 8.10409905614358, 7.95014988765202, 7.85205020726589,
				7.65633716643018, 8.04430540699064, 8.80101783354071, 7.82963038915019, 7.77737360265786, 7.93522953981691, 7.67229245562876, 7.38832785957711,
				7.51152464839087, 7.66340766489348, 7.78945456608667, 7.80791662892641, 7.82484569102686, 7.90654723236804, 7.70210434005105, 8.47762041629641,
				9.14952823257943, 8.06211758275474, 8.06652149046999, 8.05959232888755, 8.04942705711069, 7.7621706071382, 8.09773057366422, 8.01829613851552,
				7.61677580869837, 7.84267147497946, 7.76853330092603, 7.53047999524554, 7.33236920592906, 7.22402480828583, 7.41637847919293, 7.42714413340862,
				7.39756153552405, 7.49554194388426, 7.39939808333135, 7.00850518208228, 7.11801620446533, 7.48380668766583, 7.57147364885127, 7.64826303090192,
				7.47420480649612, 7.47250074473756, 7.2115567333138, 7.34342622914737, 7.48211892355212, 7.41095187558364, 7.40306109109009, 7.45298232946546,
				7.42356844425917, 7.08506429395255, 7.21081845347222, 7.35627987655075, 7.30451594646016, 7.46565531013406, 7.86901937649902, 7.24850407237061,
				7.07834157955767, 7.2211050981825, 7.33432935030054, 7.29233717617388, 7.2991214627108, 7.28344822875663, 7.30182234213793, 7.06219163228656,
				7.18159194461187, 7.50549227473742, 7.87473912517181, 7.57865685059476, 7.36707705988101, 7.25700270709207, 7.05617528410041, 7.50273821075485,
				7.44307837434852, 7.48155570190952, 7.48211892355212, 7.51914995766982, 7.65964295456468, 7.24422751560335, 7.23273313617761, 7.34213173058472,
				7.42117752859539, 7.49220304261874, 7.31521838975297, 7.14124512235049, 7.00940893270864, 7.08757370555797, 7.30451594646016, 7.37462901521894,
				7.51261754467451, 7.51752085060303, 7.37400185935016, 7.14124512235049, 7.16162200293919, 7.48493028328966, 7.51261754467451, 7.44483327389219,
				7.47420480649612, 7.67182679787878, 7.84227877911735, 7.92407232492342, 7.82843635915759, 7.58680353516258, 7.62997570702779, 7.70975686445416,
				7.52671756135271, 7.19368581839511, 7.25770767716004, 7.45414107814668, 7.48155570190952, 7.55903825544338, 7.44483327389219, 7.3375877435386,
				7.13568734702814, 7.08506429395255, 7.27239839257005, 7.5109777520141, 7.49886973397693, 7.44424864949671, 7.40306109109009, 6.91671502035361,
				6.97728134163075, 7.48268182815465, 7.41397029019044, 7.37211802833779, 7.33367639565768, 7.39510754656249, 7.03614849375054, 6.87419849545329,
				6.98471632011827, 7.45587668749182, 7.49498623395053, 7.33106030521863, 7.10496544826984, 6.99393297522319, 6.93049476595163, 7.21817683840341,
				7.4759059693674, 7.36454701425564, 7.23993259132047, 7.30921236569276, 7.13886699994552, 6.97260625130175, 7.18841273649695, 7.33498187887181,
				7.33432935030054, 7.43248380791712, 7.39141523467536, 6.96129604591017, 7.02197642307216, 7.21376830811864, 7.50988306115491, 7.32843735289516,
				7.49665243816828, 7.20042489294496, 7.08422642209792, 7.14913159855741, 7.25629723969068, 7.19818357710194, 7.28892769452126, 7.12849594568004,
				7.0825485693553, 6.82219739062049, 6.94793706861497, 7.11232744471091, 7.19967834569117, 7.31721240835984, 7.09837563859079, 7.04490511712937,
				7.30451594646016, 7.79358680337158, 7.80547462527086, 7.48324441607385, 7.35691824235602, 7.50714107972761, 7.37525577800975, 7.19668657083435,
				7.22329567956231, 7.35244110024358, 7.31721240835984, 7.44424864949671, 7.35564110297425, 7.32448997934853, 7.21450441415114, 7.2841348061952,
				7.54009032014532, 7.47477218239787, 7.82923253754359, 7.68109900153636, 7.72973533138505, 7.3031700512368, 7.28207365809346, 7.48549160803075,
				7.61874237767041, 7.69393732550927, 7.53955882930103, 7.43838353004431, 7.27378631784489, 7.35564110297425, 7.65822752616135, 7.84345640437612,
				8.36846113761584, 8.1721644521119, 7.81156848934518, 7.57507169950756, 7.6586995582683, 7.86633892304654, 7.78113850984502, 7.75491027202143,
				7.70885960104718, 8.20712916807133, 7.71154897962915, 7.73455884435476, 7.96762673933382, 8.40380050406115, 8.29279885820037, 7.98548435673382,
				8.67180090964268, 7.78239033558746, 7.78696700261487, 7.94661756324447, 8.17357548663415, 7.80954132465341, 7.80302664363222, 8.05134093329298,
				7.97315543344413, 7.86901937649902, 8.16251625014018, 8.04012466444838, 7.96346006663897, 7.83834331555712, 7.91315518592807, 7.95331834656043,
				8.07620452723903, 7.98241634682773, 8.1285852003745, 7.95472333449791, 8.1101268019411, 8.21365270303, 8.04686951095958, 8.9242570208881,
				8.61721950548336, 8.51959031601596, 8.01433573729942, 8.05769419481559, 8.10440130792161, 7.99395754757357, 8.43185314424922, 9.73820008829795,
				10.1799822793473, 8.36194190614495, 9.94884325425692, 8.75904072752422, 8.25166392360559, 8.07868822922987, 8.17357548663415, 7.88908440703551,
				9.09985563880091, 9.40656483393913, 9.16440114003474, 8.48735234940522, 8.43141741439483, 8.2776661608515, 8.31213510764841, 8.79011689289247,
				10.2800386504796, 9.07577987858049, 8.69918135930895, 9.08658956454001, 8.86149186428691, 8.54441917766983, 8.44762872803033, 8.25270667656764,
				8.10982627601848, 8.13681086367554, 8.7268056084461, 9.38269576445829, 8.57828829077605, 8.23880116587155, 8.12237124340655, 8.05515773181968,
				7.83241092718792, 8.89754559870933, 8.72566970568704, 8.56407677731509, 8.2190566610606, 8.14757773620177, 8.0013550258267, 7.78945456608667,
				8.81195017753998, 9.00220857828241, 8.59304250369967, 8.28197705886776, 8.46505743699571, 8.49474306257865, 8.40514368760761, 10.558699193753,
				9.12456459495478, 8.73182058296211, 8.52892411429194, 8.50512061018197, 8.27639470486331, 9.1239106439778, 8.58597270681106, 8.31556648356428,
				8.66112036022288, 8.92572027356022, 8.44139147799996, 9.2277872855799, 9.26473385580652, 9.27491014262548, 8.39298958795693, 8.58522560180806,
				8.42376124662369, 8.33782726244791, 9.05975001334368, 9.29825967001407, 8.76186337327473, 8.50754681436443, 8.39931015075952, 8.52357279838028,
				8.37953902611744, 9.09110628405248, 9.76198159024195, 8.92956770782534, 8.53070154144103, 8.58709231879591, 9.79784922051313, 8.66475075577385,
				9.17232692977797, 9.20140053040671, 9.33052053223229, 8.68457030082437, 8.50248556254396, 8.28878581042693, 8.29804166137157, 9.16293424957891,
				9.54795481317617, 9.01724094201035, 8.78492762605832, 8.38662882139512, 8.33447155460094, 8.53601494565683, 8.70863965598719, 8.73004395324502,
				8.37562962709445, 8.31898612539206, 8.46442512587758, 8.5972974356579, 8.92279162396964, 9.49167735686812, 9.21014035197352, 8.67795057029435,
				8.60226936377136, 8.61450137388324, 8.65886634973238, 8.77940359789435, 11.0079327963967, 9.75324588920559, 9.22513045744882, 9.0177260256968,
				8.93695560422523, 9.00932517273497, 11.4840629202851, 10.2642341958449, 9.69443180053954, 9.44041981429151, 9.35374783527091, 9.22847494217167,
				9.30392178559771, 10.2401740519157, 9.91595945403145, 10.1115174660403, 9.85859478364539, 10.1190020766858, 10.0005688901867, 11.1914521795828,
				10.4633318857817, 9.65406419220144, 9.11975899374495, 8.79573360595074, 8.44848599340645, 8.2666784433059, 8.21851757748959, 8.24249315318763,
				8.00803284696931, 8.0452677166078, 7.9287663216267, 7.74500280351584, 7.86633892304654, 7.94165125293056, 8.31041499418829, 7.82803803212583,
				7.87359778968554, 7.75705114203201, 7.72621265050753, 7.77527584648686, 7.79523492900217, 7.74975340627444, 8.06808962627824, 8.72583205652757,
				7.65444322647011, 7.60339933974067, 7.75319426988434, 7.77022320415879, 7.63143166457691, 7.54380286750151, 7.60439634879634, 7.58426481838906,
				7.5109777520141, 7.67461749736436, 7.71289096149013, 7.70165236264223, 7.63819824428578, 7.56268124672188, 7.40367029001237, 7.46622755621548,
				7.61233683716775, 7.80180040190897, 8.02878116248715, 7.73017479524622, 7.63964228785801, 7.56320059235807, 7.48661331313996, 7.5076900778199,
				7.65396918047877, 7.61283103040736, 7.45414107814668, 7.36707705988101, 7.45298232946546, 7.47873482556787, 7.98514393119862, 7.82164312623998,
				7.66058546170326, 7.5595594960077, 7.57660976697304, 7.4500795698075, 7.49886973397693, 7.51588908521513, 7.60837447438078, 7.58629630715272,
				7.68063742756094, 7.7848892956551, 7.5522372875608, 7.59890045687141, 7.64826303090192, 7.66996199547358, 7.85554467791566, 8.09651291750159,
				8.92105701815743, 8.3986348552921, 7.98820359702258, 8.00269416228394, 8.07309119969315, 7.98309894071089, 7.84619881549743, 7.78655180642871,
				7.44483327389219, 7.5422134631934, 7.6425241342329, 7.6511201757027, 7.51152464839087, 7.67693714581808, 7.9912539298402, 7.44190672805162,
				7.38398945797851, 7.60589000105312, 7.58680353516258, 7.62119516280984, 7.29573507274928, 7.48885295573346, 7.27309259599952, 7.34665516317654,
				7.47363710849621, 7.35564110297425, 7.2283884515736, 7.39694860262101, 7.47533923656674, 7.40974195408092, 7.34601020991329, 7.4079243225596,
				7.38398945797851, 7.38087903556412, 7.20637729147225, 7.09340462586877, 7.10987946307227, 7.05531284333975, 7.11639414409346, 7.19218205871325,
				7.24921505711439, 7.41938058291869, 7.56216163122565, 7.43307534889858, 7.28550654852279, 7.9355873855892, 9.01954299670119, 7.22548147278229,
				7.02731451403978, 6.7990558620588, 5.44673737166631, 5.32300997913841, 5.26269018890489, 6.30627528694802, 6.65286302935335, 7.21964204013074,
				7.38832785957711, 7.13886699994552, 7.04315991598834, 7.20637729147225, 7.02997291170639, 7.00760061395185, 6.91869521902047, 6.88448665204278,
				6.13772705408623, 7.20637729147225, 7.13727843726039, 7.05444965813294, 7.11232744471091, 6.92657703322272, 6.81454289725996, 7.35500192110526,
				7.13169851046691, 7.07749805356923, 7.24208235925696, 7.24708058458576, 7.646353722446, 7.45645455517621, 7.30988148582479, 7.23777819192344,
				7.27517231945277, 7.46908388492123, 7.45066079621154, 8.12740456269308, 7.77485576666552, 7.52131798019924, 7.54960916515453, 7.94979721616185,
				7.79770203551669, 7.79975331828725, 7.9002660367677, 7.85825418218603, 7.94165125293056, 7.67136092319064, 8.13534694890671, 8.68777949199177,
				8.45318786144033, 8.06463647577422, 8.00936307663004, 7.87739718635329, 7.85515700588134, 8.14089846060785, 7.92117272158701, 7.9707403900071,
				7.96519829061218, 8.13476078241865, 7.79852305362521, 8.30770596654951, 8.28071107566285, 8.74448811385292, 8.59137258959049, 8.44052810648075,
				8.50976567558744, 8.35514473946184, 8.28096440055337, 8.44052810648075, 8.31385226739821, 8.08085641964099, 8.18590748148232, 8.85680335672838,
				8.07309119969315, 8.14148104145742, 8.18785544369562, 8.11522197256233, 8.15908865466791, 8.20439841814938, 8.28500889544988, 8.08271113423758,
				8.24564690087386, 8.2220164372022, 8.45126704130007, 8.5519810169019, 8.62515033292133, 9.13194630454817, 8.33997857199043, 9.86620096775011,
				9.27077674078001, 8.77183540978982, 8.49474306257865, 8.61631428228404, 8.94780609305705, 9.07577987858049, 9.50046944807102, 11.4261031610143,
				9.29550838434606, 8.61721950548336, 8.47699600166482, 8.32772616646141, 8.30375241556341, 9.330431852234, 9.41613428495528, 8.92292493064183,
				8.44591198941127, 8.31115254800169, 8.27052509505507, 8.20166019080868, 9.00981411052738, 9.30909914399945, 9.12847934549586, 8.44741429680832,
				8.25426877009018, 8.32482129876878, 8.10288913464087, 9.15957325492253, 8.85109068766498, 9.54057893384188, 8.49043845410742, 8.56464913257253,
				8.352318548226, 8.10440130792161, 9.15334665045606, 10.0752957033132, 8.71800933084636, 8.34474275441755, 8.2630748358026, 8.25608813381491,
				8.00869818298853, 8.52971447196991, 8.861350110796, 9.78914235075127, 8.50025047068593, 8.40559101483493, 8.9441588309704, 8.866581653304,
				9.01456876745782, 9.13010597926558, 10.2465097200211, 8.58969988220299, 8.65067458279072, 8.78124833323686, 8.33302993974291, 9.06762406977459,
				9.52332462729018, 8.70996000607173, 8.37101068123816, 8.37770121259764, 8.40043463080604, 8.18283871076603, 8.8750074860484, 9.21034037197618,
				8.71456755083648, 8.31752199628717, 8.55929436743487, 8.60465446718623, 8.73375513136489, 9.54057893384188, 10.1616893196654, 8.8167050156216,
				8.31409733540581, 8.7106195279423, 8.74369111054302, 8.39231000926955, 9.23073106162392, 10.2561143136283, 8.9138193508572, 8.65032450401942,
				8.39004140575575, 8.3091845276863, 8.22228507387272, 9.28238192484115, 9.92260366972836, 9.16889318206201, 8.74830491237962, 8.81507308884446,
				9.76330552193627, 8.56883642456808, 8.92611897115338, 9.12891337328045, 9.7195647143752, 8.78124833323686, 8.48838210956212, 8.53640741034004,
				8.14409846333852, 9.10664513563742, 9.11569996782206, 9.68421151274841, 8.80011394676631, 8.54752839121231, 8.3221510702129, 8.09529377684465,
				8.92345797969497, 9.37974553683691, 8.90327158572421, 8.87556669199055, 8.44139147799996, 8.59674347017425, 9.00969189848934, 9.39897529082673,
				9.99392223000734, 9.06149227523977, 8.97119446318447, 8.94689552388845, 9.18696938565294, 9.0980671294934, 10.8781037947059, 9.38269576445829,
				9.19897604189713, 8.62119278143472, 8.61323037961318, 8.69517199877606, 8.72029728739272, 9.50031980347665, 9.34757739028127, 8.78370269863522,
				8.70217786562968, 8.6821990260005, 8.48363640788739, 8.40916244720253, 8.97309789628247, 9.55030649785165, 8.78630387828258, 8.60813018640834,
				8.49494758246892, 8.44870019497094, 8.19174002127746, 8.38091517312361, 9.07394774707063, 8.35608503102148, 8.3485378253861, 8.53503310954457,
				8.43489794868941, 8.5354259596773, 8.99168672593482, 9.77713036365961, 8.63887970967284, 8.28324144138542, 8.27333659850449, 8.15908865466791,
				7.91352101728389, 7.8407064517494, 8.02486215028641, 7.97143099776935, 8.47782846789396, 7.95787735848981, 8.02355239240435, 7.54908271081229,
				7.51969240411654, 7.87169266432365, 7.68156036255954, 7.73236922228439, 7.78239033558746, 7.65633716643018, 7.48324441607385, 7.59890045687141,
				7.78613643778307, 7.75061473277041, 7.76472054477148, 7.70481192293259, 7.6586995582683, 7.34729970074316, 7.54433210805369, 7.74716496652033,
				7.6889133368648, 7.77064523412918, 7.61184239958042, 7.48773376143644, 7.55747290161475, 7.56837926783652, 7.52563997504154, 7.66199755890189,
				7.41637847919293, 7.44366368311559, 7.31654817718298, 7.17472430983638, 7.24779258176785, 7.41397029019044, 7.52940645783701, 7.63964228785801,
				8.26975694753298, 8.33134542484572, 7.78447323573647, 7.48099216286952, 7.58781721999343, 7.67182679787878, 7.59739632021279, 7.67740043051481,
				7.48436864328613, 7.26122509197192, 7.39203156751459, 7.42833319419081, 7.55747290161475, 7.48885295573346, 7.42714413340862, 8.21527695893663,
				8.49371983523059, 8.14322675036744, 8.12177741916107, 8.20794694104862, 8.5197898172635, 8.57470709761684, 8.04782935745784, 7.48773376143644,
				7.56631101477246, 7.9976631270201, 8.00836557031292, 7.91498300584839, 7.8804263442924, 7.84893372636407, 7.50823877467866, 7.66058546170326,
				8.02747653086048, 8.10046489102936, 8.18088094199639, 8.33351070898294, 8.64100247714252, 8.46315930292375, 8.01201823915906, 7.79564653633459,
				7.70571282389443, 7.77863014732581, 7.81237820598861, 7.56164174558878, 7.26332961747684, 7.35564110297425, 7.47477218239787, 7.53422832627409,
				7.62997570702779, 7.62608275807238, 7.44483327389219, 7.11963563801764, 7.37023064180708, 7.54115245513631, 7.5137092478397, 7.59034694560257,
				7.54802896993501, 7.36833968631138, 7.11314210870709, 7.23705902612474, 7.48717369421374, 7.61233683716775, 8.21716859576607, 7.66669020008009,
				7.32646561384032, 7.09340462586877, 7.13966033596492, 7.40367029001237, 7.42595365707754, 7.43779512167193, 7.55328660560042, 7.32184971378836,
				7.10332206252611, 7.11069612297883, 7.48211892355212, 7.54591815120932, 7.32778053842163, 7.21964204013074, 7.36327958696304, 7.18841273649695,
				7.25063551189868, 7.4500795698075, 7.19743535409659, 7.41938058291869, 7.37963215260955, 7.40306109109009, 7.51588908521513, 7.63723438878947,
				7.52617891334615, 7.3185395485679, 7.38212436573751, 7.74975340627444, 7.67600993202889, 7.26612877955645, 7.58324752430336, 7.56682847920833,
				7.56008046502183, 7.63288550539513, 7.50052948539529, 7.42356844425917, 7.39018142822643, 7.21007962817079, 7.94555542825349, 7.5999019592085,
				7.56268124672188, 7.61184239958042, 8.99998964246073, 8.73793385811414, 8.26796230533871, 7.77904864492556, 7.57865685059476, 7.60539236481493,
				8.29179710504873, 7.61775957660851, 7.35883089834235, 7.68662133494462, 7.75362354655975, 7.77904864492556, 8.05706068196577, 7.8984110928116,
				7.79729127354747, 7.51534457118044, 7.48268182815465, 7.88683299895506, 8.39728289474368, 8.26770566476243, 8.23615566168312, 8.38890517111471,
				9.0788640091878, 9.63299030483845, 8.94780609305705, 8.53934599605737, 8.3478273457825, 8.32530602975258, 8.25556865328375, 8.09712193091871,
				8.13593277200489, 8.01895468315572, 8.0861025356691, 8.13329386122263, 8.04974629095219, 8.19063168090354, 8.04334217044161, 8.43814998407578,
				8.10741881171997, 8.16876982367527, 8.17470288246946, 8.29129585190541, 8.3059782109673, 8.42310226801664, 8.75621009188674, 8.72583205652757,
				8.70748291785937, 8.51077262361331, 8.70217786562968, 8.580543506917, 9.22542600939422, 8.41116578677071, 8.45190772471761, 8.77183540978982,
				8.23880116587155, 8.15708378502887, 8.03915739047324, 8.1185050675871, 9.18563775933581, 9.15239341202133, 9.02617712030286, 9.74226190403691,
				10.0828463914793, 9.11107237031751, 9.73878978049572, 9.614938437645, 9.34801317710126, 8.55429627936774, 8.44955654270043, 8.43054538469057,
				8.60538720215215, 9.219894584781, 9.50076867009599, 9.52230033688749, 8.47886807709457, 8.28349412616251, 8.26898820950666, 8.28324144138542,
				9.03013657115323, 10.2525586604481, 9.16659744902826, 8.27537637483641, 8.3466420902212, 8.42156296040099, 8.19450550976564, 8.87766093359367,
				8.9941724343984, 9.55676293945056, 8.4144957931779, 8.31139827843664, 8.365672383775, 8.14902386805177, 8.96826881077645, 8.88322423027899,
				8.70450228972123, 8.23297179059344, 8.17301131172497, 8.13446757027756, 8.83433697401764, 9.08975340898706, 9.0107912695156, 8.71751837264977,
				8.42200300441249, 8.20712916807133, 8.05484022110102, 8.83156587912106, 10.1827467519768, 8.80986280537906, 8.76013937002663, 8.88502565805085,
				8.56159277871292, 8.2495751500002, 9.35660287895444, 9.12750209366718, 8.79102985704596, 8.65347080970879, 8.74337213127397, 8.86742743852498,
				8.44009614103127, 9.37568530456302, 9.74102744483773, 8.83622857152601, 8.46104603079324, 8.21635833238616, 8.22844388300403, 8.0471895621705,
				9.07234187381889, 9.46761478200654, 8.98669669562029, 8.43923164994653, 8.42398080969406, 8.58802437217683, 8.25400859056484, 8.74512525946224,
				9.49514330367712, 8.72469504674049, 8.35960327084147, 8.76374072050946, 8.7279402223939, 8.38548870041881, 9.3130774494273, 9.3061958576197,
				9.84675845829004, 8.79679268767466, 8.64611397148308, 8.9398431242785, 8.84375938191798, 9.7005142080113, 9.53914039514886, 8.9082888855571,
				9.02183976410551, 9.10963566785455, 8.87164566750187, 8.38228942895144, 9.23229753932823, 9.85287823470959, 8.84707231256781, 8.53346016388011,
				8.58802437217683, 8.48549610467298, 8.18979961872823, 9.49687178267057, 9.46280968867222, 8.84347078162738, 8.36310917603352, 8.60575336839572,
				6.58617165485467, 7.63578686139558, 9.24879155835043, 8.88072457615146, 8.69617584694468, 8.45382731579442, 8.14467918344776, 9.07635173197287,
				10.2446985435045, 9.85828095969805, 9.18758338485357, 8.76248954737158, 8.5016733797582, 8.65521448931361, 10.0388921895423, 9.46436224293533,
				8.97309789628247, 8.95557714628151, 8.91945316857545, 8.72631895096224, 8.73921611506174, 10.26196586942, 10.5694947531438, 9.56120848888113,
				9.60400276796519, 10.0861007334703, 9.72841962445348, 9.41205597587677, 9.84357829978222, 11.5721750241742, 10.2817184876905, 10.1697672187275,
				9.68290322361684, 9.89550578279447, 9.37627844951961, 9.58217975243469, 10.3414521187349, 10.3339704236196, 10.2182252970113, 9.73406247747719,
				10.1874627630566, 9.88857693980037, 11.075086947327, 12.6735418157462, 10.9246967023573, 10.1815358690196, 9.86339445896968, 9.92573816147095,
				9.40153907670774, 9.33441468707811, 9.14750706280461, 8.91395385889425, 9.1801903950253, 9.05718919248201, 8.71275997496021, 8.40312823512826,
				8.29479935899257, 9.11591979635669, 8.95156964301882, 8.3513747067213, 8.65381978894806, 8.6429443967218, 8.71620797115185, 8.36497397843873,
				8.37378460812088, 8.51719319141624, 8.31825432879885, 8.39547743273214, 8.3228800217699, 8.24564690087386, 8.63194942871443, 8.31066090590723,
				8.43294163896865, 11.6448305358502, 11.3632879189498, 10.6929444132335, 10.3343929611261, 9.98608085083998, 10.2820952064744, 10.1943645158844,
				10.0954706196007, 10.1468650106811, 10.140888975597, 10.2095373998461, 10.033682134194, 11.0828346170357, 11.1744832892926, 10.7792895676801,
				9.9475044379529, 9.37602428761711, 8.99776577201121, 8.83287946027762, 8.89822898560123, 8.76467807411661, 8.54110501146255, 8.39615486303918,
				8.31238059678675, 8.34117174717076, 8.1300590399928, 8.35819745992578, 8.35561499576018, 8.18172045512811, 8.10952565975287, 8.06463647577422,
				7.82324569068552, 7.85476918349913, 8.10167774745457, 8.09040229659332, 7.98989937494294, 8.09894674894334, 8.65381978894806, 8.04109100370863,
				8.04974629095219, 8.22147894726719, 8.17075142375753, 8.3354314778808, 8.25660734462616, 8.05769419481559, 7.70796153183549, 7.91717198884578,
				8.2602342916073, 8.28803156777646, 8.36869318309779, 8.63355299253243, 9.27246974344173, 8.67556352738768, 8.48342956126343, 8.17188200612782,
				8.20658361432075, 8.18896686364888, 8.03073492409854, 8.05484022110102, 9.15514473650823, 8.83331693749932, 8.34972083747249, 8.18339736999843,
				7.95647679803678, 7.86940171257709, 7.70930833338587, 7.81923445385907, 7.83241092718792, 7.88683299895506, 8.03786623470962, 7.952615111651,
				7.76768727718691, 7.47816969415979, 7.539027055824, 7.99799931797973, 8.30967689598773, 8.02878116248715, 7.79028238070348, 7.76174498465891,
				7.47647238116391, 7.63964228785801, 7.65586401761606, 7.81963630236759, 7.81359155295243, 7.99057688174392, 7.81278281857758, 7.65775527113487,
				7.69439280262942, 7.84149292446001, 7.93343838762749, 7.6511201757027, 7.72356247227797, 7.88004820097158, 7.94093976232779, 7.75876054415766,
				7.63336964967958, 7.84854348245668, 7.89729647259588, 7.72223474470961, 7.71244383427499, 7.53955882930103, 7.91169052070834, 7.80139132029149,
				8.4013333053217, 8.18144069571937, 7.86288203464149, 7.92407232492342, 7.56682847920833, 7.51914995766982, 7.64873978895624, 7.77064523412918,
				7.60986220091355, 7.59186171488993, 7.539027055824, 7.34536484041687, 7.46336304552002, 7.51479976048867, 7.6425241342329, 7.67600993202889,
				7.55799495853081, 7.6889133368648, 7.60638738977265, 7.58222919427646, 7.74196789982069, 7.78239033558746, 8.00636756765025, 8.65102453904976,
				8.40357646462927, 8.3850322878139, 8.02812905943176, 7.95787735848981, 7.99530662029082, 7.99226864327075, 7.9359451033537, 7.98786409608569,
				7.78364059622125, 7.8087293067444, 8.0532511535491, 7.97590836016554, 8.12237124340655, 8.55986946569667, 8.9274468162562, 8.28576542051433,
				8.28399930424853, 8.16337131645991, 7.91425227874244, 7.86441990499457, 8.07215530818825, 8.0802374162167, 8.12088602109284, 8.11312710422178,
				8.14438886554762, 8.06463647577422, 7.944846711002, 8.24143968982973, 9.00736702745136, 8.66233195708248, 8.80056599227992, 8.3742461820963,
				8.56407677731509, 8.38434727808281, 8.12651816878071, 8.39072252736229, 8.3351915834332, 8.9278448262117, 9.0079793598445, 8.37816098272068,
				8.20330402679528, 8.38571682862785, 8.31115254800169, 8.74145611599836, 9.44295889365291, 9.14590851181679, 8.58951385299586, 8.46484671104403,
				8.36590507720246, 8.56541176368671, 8.4724050086261, 8.96648377906443, 8.56006109164341, 8.4690528160883, 8.74385056203024, 10.0138206842205,
				8.69114649853968, 8.78094111357239, 9.83900236330972, 11.614940390377, 9.62865589206317, 8.78293635634926, 8.68118104152169, 9.00097644407034,
				8.74623928838306, 9.8072519446553, 9.61266722758384, 10.5920994642943, 8.75542238014849, 8.56063574925907, 9.40516674990861, 8.45807992692373,
				9.54959444997195, 9.60602446822924, 8.67726913926287, 8.17103418920548, 8.24143968982973, 8.51097389160232, 8.32360844234357, 9.25922576970599,
				9.84966474583862, 8.83317113302287, 8.49780647761605, 8.63408694288774, 9.04227668692893, 8.55004752828718, 9.81809304951918, 9.9020865716205,
				8.91637191488169, 8.33206770728955, 8.23668532271246, 8.40178233990491, 8.24170315972982, 9.03562977818356, 9.10409057213347, 10.8321415433937,
				8.7787879291047, 8.48011418317482, 8.48941081040379, 8.25062008217469, 9.54344981789221, 9.09717167387054, 8.66939912430557, 8.34924780056679,
				8.34069464792507, 8.49474306257865, 8.53326337159373, 9.21979553074694, 10.4442990717924, 8.87696334026227, 8.68185981297147, 8.49821422481843,
				8.56845648535378, 8.45871626165726, 9.71818154670121, 9.68700923909068, 8.83010431791379, 8.38799525294456, 8.4984180360899, 8.84721610435754,
				8.28096440055337, 9.26492324974647, 9.11173476822206, 8.70682132339263, 8.33182700443606, 8.36660283278374, 8.27690348126706, 8.12946976478423,
				9.17915925449261, 9.68558026801716, 8.65521448931361, 8.29454951514368, 8.6522484224091, 8.92970011431345, 8.3959291039232, 9.46753746341524,
				9.88979422540413, 8.84922702143852, 8.61431990214696, 8.48156601377309, 8.74909824839902, 8.65364531455174, 9.3482745580655, 9.67683786189263,
				9.64290170574605, 8.72891172506098, 8.77894188184151, 9.96057651952026, 8.73777346032728, 9.25263328416643, 9.26624800391448, 9.42730487221368,
				8.79300509129753, 8.70300863746445, 8.43944784279138, 8.29104513108173, 9.31325790598287, 9.34792603492875, 8.791486026749, 8.51899157335762,
				8.41294317004244, 8.29679586577005, 8.21256839823415, 9.25655579577315, 9.65226597708712, 8.63746202380718, 8.60776488960062, 8.96533457380484,
				8.68372406230387, 8.53267276226462, 9.49016666846238, 10.142858720955, 9.11162439903702, 9.08500388066489, 9.05508908670489, 9.33626792857397,
				9.23960786965675, 10.1327324527083, 9.49122438992696, 9.1122864315008, 9.06357899058078, 8.97297111339799, 9.14548179962769, 10.5418617072488,
				11.5075208865114, 10.1931676276506, 9.27995971385624, 8.84635304331433, 8.73262709966039, 8.65504025810836, 8.45446636150793, 8.96367227561502,
				10.0210927946104, 9.00565049932022, 8.86092472971904, 8.58522560180806, 8.536211197252, 8.45850419506756, 8.53444354482276, 10.1042218823372,
				8.65067458279072, 8.51218064959269, 8.48549610467298, 8.57791192645094, 8.54985397365579, 9.60622641363735, 10.0261917925116, 8.87024156729927,
				8.52793528794814, 8.38343320123671, 8.20083725837985, 8.09285102753838, 8.03883475778775, 8.08641027532378, 8.03657340970731, 7.97522083865341,
				7.84267147497946, 7.8935720735049, 7.81762544305337, 7.82284529027977, 7.9672801789422, 8.00670084544037, 7.91132401896335, 7.85166117788927,
				7.87207397986687, 7.75362354655975, 7.68294316987829, 7.84384863815247, 8.19146305132693, 7.97831096986772, 7.92334821193015, 7.87131120332341,
				7.74370325817375, 7.77863014732581, 7.83518375526675, 7.83834331555712, 7.84619881549743, 7.92044650514261, 7.75790620835175, 7.58629630715272,
				7.51479976048867, 7.75790620835175, 7.80343505695217, 8.07899825868515, 8.38068594676157, 8.0643219609108, 7.85282781228174, 7.90396563403217,
				7.84463264446468, 7.88945914940452, 8.22550309756692, 8.54071438645758, 8.01928379291679, 7.83122021460429, 8.43315919580623, 8.09620827165004,
				7.86633892304654, 7.77904864492556, 7.77359446736019, 7.77275271646874, 7.76811037852599, 7.48099216286952, 7.74370325817375, 7.5963923040642,
				7.68063742756094, 7.53849499941346, 7.4500795698075, 7.44307837434852, 7.54855597916987, 7.64060382639363, 7.67647364638916, 7.56734567601324,
				8.07682603129881, 7.70120018085745, 7.36833968631138, 7.3664451483276, 7.48661331313996, 7.5740450053722, 7.568895663407, 7.63964228785801,
				7.85321638815607, 7.31188616407716, 7.53636393840451, 7.68248244653451, 7.73193072194849, 8.01201823915906, 7.98036576511125, 8.17131687471973,
				7.97796809312855, 7.79482315217939, 8.30424746507847, 8.05642676752298, 7.77779262633883, 8.22897764335831, 7.900636613018, 7.46164039220858,
				7.54908271081229, 7.81681996576455, 7.72223474470961, 7.71556953452021, 7.72621265050753, 7.58171964012531, 7.26542972325395, 7.30114780585603,
				7.47420480649612, 7.58882987830781, 7.61085279039525, 7.64778604544093, 7.60190195987517, 7.25417784645652, 7.1800698743028, 7.37713371283395,
				7.57507169950756, 7.50714107972761, 7.58578882173203, 7.40731771046942, 7.03085747611612, 7.15070145759253, 7.25417784645652, 7.45066079621154,
				7.55118686729615, 7.61332497954064, 7.42714413340862, 7.350516171834, 7.28824440102012, 7.434847875212, 7.53743003658651, 7.35244110024358,
				7.28207365809346, 7.3031700512368, 7.29369772060144, 7.22983877815125, 7.57507169950756, 7.97418866928601, 7.61579107203583, 7.47420480649612,
				7.33432935030054, 7.31920245876785, 7.40488757561612, 7.42476176182321, 7.47022413589997, 7.36770857237437, 7.2841348061952, 7.32580750259577,
				7.29097477814298, 7.19142933003638, 7.28961052145117, 7.33236920592906, 7.39572160860205, 7.56734567601324, 7.62119516280984, 7.26892012819372,
				7.26961674960817, 7.30787278076371, 7.28138566357028, 7.32118855673948, 7.51207124583547, 7.50052948539529, 7.11314210870709, 7.1420365747068,
				7.32383056620232, 7.42892719480227, 7.52886925664225, 7.41997992366183, 7.4730690880322, 7.3375877435386, 7.35436233042148, 7.58273848891441,
				7.62608275807238, 7.7596141506969, 7.94058382710424, 7.59085212368858, 7.41818082272679, 7.41155628781116, 7.59789795052178, 6.63594655568665,
				7.64730883235624, 7.82763954636642, 7.63385355968177, 8.53030683056162, 8.52951694110507, 7.85127199710988, 7.79564653633459, 7.58222919427646,
				7.43897159239586, 7.63867982387611, 7.52725591937378, 7.72488843932307, 7.91352101728389, 8.65956043270316, 8.29579811063615, 8.13241267450091,
				7.92551897978693, 7.82843635915759, 7.84424071814181, 7.77695440332244, 7.76684053708551, 7.78986855905471, 7.69393732550927, 7.71556953452021,
				8.15277405274407, 8.2529671950008, 7.94129557090653, 7.80954132465341, 7.81923445385907, 7.81237820598861, 7.54538974961182, 8.47428569040496,
				7.79193595693806, 7.66809370908241, 7.80547462527086, 7.9672801789422, 7.99429498641598, 7.80954132465341, 8.70317470904168, 7.9672801789422,
				8.09620827165004, 8.03786623470962, 8.58016799057763, 10.8718582692757, 9.19248185367487, 9.15069651904867, 9.82319898130729, 8.76888532613486,
				8.50855599802057, 8.72972059026726, 8.92145757894788, 8.52991196382401, 10.4159817834027, 10.3369892693381, 9.14644164612595, 8.50875771259514,
				8.38617292897783, 8.36100710822691, 8.12976444579417, 8.73198193834769, 8.73584667745758, 10.8196982812101, 10.6590929669357, 9.84945366404364,
				8.88820487145502, 8.92771217382708, 9.66738540005753, 10.1635029066262, 9.3379417165699, 9.17719715338293, 8.87905466204227, 8.57866451350434,
				8.73004395324502, 9.9533247873833, 10.2387447656008, 9.20311432688444, 8.74719318352693, 8.77554943448619, 9.2098402469345, 8.52813313145457,
				9.05765528431053, 9.42294862137501, 9.02917814290207, 9.09773142759353, 9.44809663565824, 9.11250701162742, 8.80267284031282, 9.20843856468659,
				11.0470891404358, 9.32758993202642, 8.67880170661265, 8.57659353469768, 8.43598313599069, 8.19007704971905, 9.06044728240157, 9.27030595314362,
				8.5016733797582, 8.18729927015515, 8.0959035329611, 8.04334217044161, 7.952615111651, 8.39908510293591, 8.79102985704596, 8.3030093814735,
				8.11910083763749, 8.23031079913502, 8.15765701519647, 7.82923253754359, 8.57395152523485, 9.61132880805727, 8.92385758009988, 8.3654396361887,
				8.31188955823036, 8.63141433550626, 8.45382731579442, 8.90585118120802, 10.8674821444793, 9.15514473650823, 8.43944784279138, 8.44354665124794,
				8.57262789830434, 8.372398606513, 8.73600738456922, 10.3885029394023, 8.70880479511728, 8.19533366716287, 8.22147894726719, 8.27512163021651,
				8.16990264735914, 8.82232217747174, 9.80543361206074, 9.38907215991958, 8.98130449495713, 8.57922858233569, 8.48776438072542, 8.72192834304709,
				8.9182485910357, 9.65162297294974, 8.86474666090541, 8.50936261230105, 8.63177109612367, 9.20271134481169, 8.90381521172292, 9.02653771890043,
				9.23766366762507, 8.89508153175417, 8.6429443967218, 8.12976444579417, 8.29179710504873, 8.09803475617607, 9.51878049751247, 9.90468683311161,
				8.93734984826739, 8.57885257180297, 8.71588010229646, 8.48899945704546, 8.50572771330696, 9.30008966411979, 10.1461591836579, 9.17709377818255,
				8.83564692253477, 8.83287946027762, 8.92305821954573, 8.89329814421792, 8.60263667323371, 8.99143781491923, 8.80687326653069, 8.85409390765552,
				8.93102321585603, 8.85280791762332, 10.6933076203563, 11.3075604350077, 9.83745458193169, 9.60508151672137, 9.74537068443899, 9.67564548044036,
				9.43468320386588, 11.5036223246441, 11.9767789709185, 10.5425744562461, 10.004282662571, 9.73281784848262, 9.86646043169905, 9.37092743662413,
				9.490544554572, 10.139152384404, 9.99984264077889, 10.0327159505439, 10.3803736928726, 10.453053004618, 10.2401383446439, 11.7605196483804,
				12.846746888829, 10.7668837086558, 9.84522264440415, 9.29035230994557, 9.10331179921766, 8.79573360595074, 8.62335338724463, 8.41825644355621,
				8.31090675716845, 8.23615566168312, 8.13123654969612, 7.92768504561578, 7.7591874385078, 7.72665366484764, 7.83518375526675, 7.88419993367604,
				7.91461770904068, 7.92551897978693, 7.75319426988434, 7.50878717063428, 7.55747290161475, 7.80261806344267, 7.68386398025643, 7.9844627322622,
				7.85166117788927, 7.68478394352278, 7.3375877435386, 7.40367029001237, 7.86787149039632, 7.8984110928116, 7.58426481838906, 7.71423114484909,
				7.88945914940452, 7.32580750259577, 7.48885295573346, 7.55381085200823, 7.66996199547358, 7.98820359702258, 8.00436556497957, 7.6511201757027,
				7.48661331313996, 7.44949800538285, 7.59538727885397, 7.60986220091355, 7.54802896993501, 7.61775957660851, 7.59538727885397, 7.34665516317654,
				7.40123126441302, 8.09315669772264, 7.92371033396924, 7.69074316354187, 8.43901541035221, 7.78239033558746, 7.30854279753919, 7.26192709270275,
				7.43720636687129, 7.54009032014532, 7.58528107863913, 7.60887062919126, 7.46450983463653, 7.15695636461564, 7.48773376143644, 7.4489161025442,
				7.47022413589997, 7.43602781635185, 7.52185925220163, 7.41034709782102, 7.15617663748062, 7.13807303404435, 7.36264527041782, 7.51697722460432,
				7.61726781362835, 7.49554194388426, 7.39203156751459, 7.17472430983638, 8.09132127353041, 7.51534457118044, 7.8458075026378, 7.69120009752286,
				7.83478810738819, 7.67740043051481, 7.24850407237061, 7.40245152081824, 7.69439280262942, 7.82604401351897, 7.61184239958042, 7.5137092478397,
				7.67600993202889, 7.24064969425547, 7.65539064482615, 8.13944052187461, 8.37493814383537, 7.90174751852014, 8.02387999273488, 8.75020786252571,
				8.081784206935, 7.70436116791031, 7.86825426552061, 7.81963630236759, 7.76089319585102, 7.66715825531915, 7.63433723562832, 7.35115822643069,
				7.58933582317062, 7.91022370709734, 7.85476918349913, 7.64683139143048, 7.49164547360513, 7.5234813125735, 7.0352685992811, 7.06561336359772,
				7.22983877815125, 7.50823877467866, 8.31164394850298, 8.01400499477946, 7.49720722320332, 7.83161727635261, 7.15148546390474, 7.41095187558364,
				7.63094658089046, 7.4759059693674, 7.58832367733522, 7.24636808010246, 7.10332206252611, 7.10414409298753, 7.1929342212158, 7.40123126441302,
				7.43955930913332, 7.70796153183549, 7.350516171834, 7.32251043399739, 7.28550654852279, 7.36770857237437, 7.26752542782817, 7.22475340576797,
				7.22256601882217, 7.35179986905778, 6.97541392745595, 7.04315991598834, 7.48099216286952, 7.20340552108309, 7.22402480828583, 7.11314210870709,
				7.07918439460967, 6.88550967003482, 6.87419849545329, 7.72312009226633, 7.49665243816828, 7.22402480828583, 7.27239839257005, 7.07918439460967,
				6.85435450225502, 6.93049476595163, 7.15617663748062, 7.31455283232408, 7.22693601849329, 7.65681009148038, 7.54009032014532, 7.21007962817079,
				7.24992553671799, 7.29437729928882, 7.51914995766982, 7.42237370098682, 8.22362717580548, 7.49276030092238, 7.22475340576797, 7.25629723969068,
				7.4489161025442, 7.65539064482615, 7.67136092319064, 7.92407232492342, 7.80098207125774, 7.45645455517621, 7.36264527041782, 8.21311069759668,
				8.23642052726539, 7.92153563213355, 7.74500280351584, 7.57814547241947, 7.56682847920833, 7.65822752616135, 8.27078101316267, 7.80302664363222,
				7.6226639513236, 7.70029520342012, 8.05864371221562, 7.64108424917491, 7.83636976054512, 8.37355374121463, 8.60940767540405, 8.17723488551019,
				8.03689677268507, 7.95331834656043, 7.7848892956551, 8.07371464110986, 8.28045768658256, 8.19918935907807, 8.00034949532468, 7.88720858581393,
				7.83715965000168, 7.97968130238774, 8.51839247199172, 8.35631996582815, 7.93236215433975, 7.83676478326407, 8.53719187792293, 8.02649693894541,
				7.9728107841214, 8.37447688921464, 8.25322764558177, 8.44591198941127, 8.49269555981584, 8.83913175254611, 8.07589363029886, 8.75020786252571,
				10.702412661625, 10.0599783492956, 8.79315687091382, 8.71440336070394, 9.05625635659347, 8.62155320674048, 9.96142621745657, 9.70856696016566,
				9.19644426678407, 8.61431990214696, 8.88903257187474, 9.01627006814768, 8.19918935907807, 9.16219999664825, 9.60750445504496, 8.44290058683438,
				8.15737044118677, 8.18451375303372, 8.83898679349679, 8.21283958467648, 8.33615081612066, 8.59044365315583, 8.70134640303916, 8.26642147298455,
				8.27461194620955, 8.36637030168165, 8.03527891114467, 9.23151460720759, 9.96467672084855, 8.84548923675327, 8.67299964255444, 8.40065937516029,
				8.58035576637388, 8.02059914989697, 9.1075321519945, 9.43835205468725, 8.50126704086598, 8.3133619511344, 8.3255483071614, 8.47637119689598,
				8.20111164444276, 8.70051424854327, 11.2744652095441, 9.60757167515724, 8.87863674743007, 8.76592651372944, 9.85639594500228, 8.43424627059531,
				8.8034242116007, 9.38176948760371, 8.76029622047005, 8.55506684384432, 8.46884293047519, 8.53129331579502, 8.04558828080353, 9.0902045707362,
				9.45414892373398, 9.0590522577624, 8.25945819533241, 8.18952211074809, 8.19533366716287, 7.69393732550927, 8.29004161870449, 9.03288694657909,
				8.38274709486331, 8.21797820315073, 8.12474302038557, 8.04686951095958, 7.57301725605255, 8.3986348552921, 8.71144331907547, 8.25114213909075,
				7.99226864327075, 8.00536706731666, 8.08085641964099, 7.52833176670725, 8.20248244657654, 9.07440609473535, 8.2147358333823, 7.96797317966293,
				8.12829017160705, 7.9536697786498, 7.66669020008009, 7.96554557312999, 9.14216859187285, 8.28702502516506, 8.28324144138542, 8.30102525383845,
				8.38799525294456, 7.70975686445416, 8.11102783819368, 8.74560285240295, 8.39140318535794, 8.11969625295725, 8.2358907259285, 8.10681603894705,
				7.71199650704767, 8.4252971767117, 8.84937050375457, 8.49310539588715, 8.17413934342947, 8.10228362448007, 7.8336002236611, 7.52294091807237,
				7.91022370709734, 8.3654396361887, 9.06056344665796, 8.17919979842309, 8.01631789850341, 8.10319175228579, 7.81439963380449, 8.38799525294456,
				8.74814616962193, 8.31287139434261, 7.92334821193015, 7.84658997529119, 8.3020178097512, 8.43620003220671, 8.93458687038968, 8.88861880730088,
				8.66423293406555, 8.50004703258127, 8.41825644355621, 8.4721958254855, 8.30721262662831, 9.88659568486591, 10.694985739443, 9.76019438270965,
				9.11007795003779, 8.79951090136887, 8.7830896717961, 8.42989086301344, 8.87877607170755, 9.75938620856187, 8.9520876435484, 8.66112036022288,
				8.58485183989005, 8.39660622842712, 7.92371033396924, 8.08548677210285, 8.35890061242164, 8.30350479887278, 8.27792025817214, 8.36357570275064,
				8.59822003005861, 8.08116577772543, 9.03443816698441, 10.2832245120716, 9.27322127001538, 8.71407489954152, 8.23350314023399, 7.88419993367604,
				7.81278281857758, 7.93128476152589, 8.4144957931779, 8.15651022607997, 7.85709386490249, 7.9098566672694, 7.80913539812054, 7.5076900778199,
				8.20385137218388, 7.82164312623998, 7.80384330353877, 7.76089319585102, 7.70345904786717, 8.06117135969092, 7.350516171834, 7.48380668766583,
				7.54062152865715, 7.69666708152646, 7.50384074669895, 7.39817409297047, 7.04228617193974, 7.05272104923232, 7.36264527041782, 7.6231530684769,
				7.79523492900217, 8.42683075133585, 7.9168074909376, 7.23633934275434, 7.20637729147225, 7.55642796944025, 7.58273848891441, 7.68294316987829,
				7.60688453121963, 7.70345904786717, 7.15148546390474, 7.04053639021596, 7.33888813383888, 7.48436864328613, 7.35179986905778, 7.42356844425917,
				7.29165620917446, 6.81673588059497, 6.91075078796194, 7.32118855673948, 7.99159228206809, 7.71289096149013, 7.28276117960559, 7.30586003268401,
				6.90575327631146, 7.87321705486274, 7.08590146436561, 7.27100853828099, 7.21376830811864, 7.16626597413364, 7.21303165983487, 6.82979373751242,
				6.80128303447162, 7.15773548424991, 7.04577657687951, 7.09174211509515, 7.23417717974985, 7.28000825288419, 6.69703424766648, 7.028201432058,
				7.17472430983638, 7.22329567956231, 7.33693691370762, 7.36201055125973, 7.26332961747684, 6.81124437860129, 7.3185395485679, 7.64012317269536,
				7.40549566319947, 7.57250298502038, 7.48549160803075, 7.33302301438648, 7.20563517641036, 7.09090982207998, 7.32646561384032, 7.35564110297425,
				7.46336304552002, 7.56734567601324, 7.74975340627444, 7.14440718032114, 6.89972310728487, 7.33106030521863, 7.2211050981825, 7.7376162828579,
				7.36327958696304, 7.29097477814298, 7.03878354138854, 6.93244789157251, 7.30586003268401, 7.68616230349291, 7.47929963778283, 7.26961674960817,
				7.30921236569276, 6.74051935960622, 6.80572255341699, 7.14282740116162, 7.18690102041163, 7.14045304310116, 8.76155013912964, 8.27944348771267,
				7.43720636687129, 7.16006920759613, 7.04141166379481, 7.17472430983638, 7.41276401742656, 7.25629723969068, 7.2848209125686, 6.72623340235875,
				6.93244789157251, 7.07411681619736, 7.20266119652324, 7.16472037877186, 7.06731984865348, 6.90675477864855, 6.67708346124714, 6.51767127291227,
				7.04315991598834, 7.04315991598834, 6.9177056098353, 7.05789793741186, 6.85540879860993, 6.64378973314767, 6.52502965784346, 6.89060912014717,
				6.92657703322272, 7.00488198971286, 6.87316383421252, 6.89060912014717, 6.4425401664682, 6.8351845861473, 7.27239839257005, 7.07749805356923,
				7.34407285057307, 7.29165620917446, 7.27170370688737, 7.454719949364, 6.69208374250663, 6.96318998587024, 7.01660968389422, 6.79122146272619,
				6.82001636467413, 6.61873898351722, 6.47389069635227, 6.49978704065585, 6.8596149036542, 6.88141130364254, 6.99759598298193, 7.15226885603254,
				7.19668657083435, 6.70808408385307, 6.98286275146894, 7.12849594568004, 7.08924315502751, 7.19893124068817, 8.090708716084, 7.39387829010776,
				7.05012252026906, 7.19518732017871, 7.44132038971762, 7.41758040241454, 7.47420480649612, 7.39264752072162, 7.18538701558042, 6.86484777797086,
				6.83410873881384, 7.28756064030972, 7.22402480828583, 7.24422751560335, 7.30653139893951, 7.31721240835984, 6.95844839329766, 6.82546003625531,
				7.18159194461187, 7.27655640271871, 7.27100853828099, 7.82444593087762, 7.41938058291869, 7.00760061395185, 7.07326971745971, 7.26542972325395,
				7.60240133566582, 7.55747290161475, 7.58222919427646, 7.28961052145117, 7.3304052118444, 7.2211050981825, 7.4312996751559, 7.75018416225784,
				7.62997570702779, 7.73061406606374, 7.82404601085629, 7.22620901010067, 7.4770384723197, 7.86326672400957, 7.94909149983052, 7.7698009960039,
				7.57967882309046, 7.54908271081229, 7.29165620917446, 7.78986855905471, 7.65681009148038, 7.53476265703754, 7.66528471847135, 8.06652149046999,
				8.14931284363534, 7.72533003791713, 7.71735127218533, 7.67786350067821, 7.89506349809157, 8.07992777075827, 8.1934002319521, 8.66509582133973,
				7.84463264446468, 8.77909581088053, 9.05870319731322, 8.42178300661158, 8.07215530818825, 8.40469616018909, 9.72184576464693, 8.11402544235676,
				8.43076346341785, 8.54888563814873, 8.3228800217699, 8.00836557031292, 8.11999382772511, 8.58260632996447, 7.60986220091355, 8.41205487329293,
				9.5410100922274, 8.55948610360649, 8.14438886554762, 7.9912539298402, 7.88532923927319, 7.4599147662411, 8.37516869138682, 8.7268056084461,
				8.07527154629746, 7.80057265467065, 7.74975340627444, 7.91971976092457, 7.35627987655075, 8.17301131172497, 8.26100978602383, 7.84658997529119,
				7.74022952476318, 7.83042561782033, 7.36137542897735, 8.2987883944492, 8.7417757069247, 8.39705739017626, 7.77821147451249, 7.93379687481541,
				7.86018505747217, 7.94236223767433, 8.19808924895612, 8.42901750051251, 8.05674377497531, 7.81116338502528, 7.78655180642871, 8.1086232683546,
				7.62119516280984, 8.09285102753838, 9.39224517527379, 8.45318786144033, 8.09437844497296, 7.9912539298402, 8.32820949174873, 7.64108424917491,
				8.48632152774915, 9.16356318041725, 8.18841130807903, 7.82644313545601, 7.96067260838812, 7.67229245562876, 7.16317239084664, 7.90211754627645,
				9.63430006272051, 8.84822206837138, 8.38320455141292, 8.16451026874704, 8.05293303679757, 7.56112158953024, 8.25634777291802, 8.67282848294769,
				8.30647216010058, 8.05896001776942, 7.87245515006398, 8.19533366716287, 7.59135704669855, 8.02158453345511, 12.1496715918794, 11.5230440984914,
				8.71177264560569, 8.05610965954506, 8.08147504013705, 7.45876269238096, 8.01400499477946, 8.49678638163858, 7.98104975966596, 7.77779262633883,
				8.2602342916073, 7.86633892304654, 7.31055015853442, 7.71824095195932, 8.31947369244219, 8.23668532271246, 7.80751004221619, 7.59186171488993,
				7.52886925664225, 7.17165682276851, 7.89133075766189, 8.36007143564403, 8.11042723757502, 7.77527584648686, 7.34729970074316, 7.30182234213793,
				7.12044437239249, 8.87877607170755, 9.25061821847475, 9.24792513230345, 8.39140318535794, 8.00469951054955, 7.58933582317062, 7.82524529143177,
				8.24931374626064, 9.29514097366865, 8.56826646160024, 8.35255436947459, 8.29579811063615, 8.29029259122431, 7.78572089653462, 8.28172399041139,
				8.4707303170059, 8.13505390861157, 8.06714903991011, 8.02355239240435, 8.02191277898571, 7.81722278550817, 9.27387839278017, 10.3337753460756,
				9.12587121534973, 8.89137400948464, };
	}

	private String[] getStringArrayForProphet() {
		return new String[] { "2007-12-10", "2007-12-11", "2007-12-12", "2007-12-13", "2007-12-14", "2007-12-15", "2007-12-16", "2007-12-17", "2007-12-18",
				"2007-12-19", "2007-12-20", "2007-12-21", "2007-12-22", "2007-12-23", "2007-12-24", "2007-12-25", "2007-12-26", "2007-12-27", "2007-12-28",
				"2007-12-29", "2007-12-30", "2007-12-31", "2008-01-01", "2008-01-02", "2008-01-03", "2008-01-04", "2008-01-05", "2008-01-06", "2008-01-07",
				"2008-01-08", "2008-01-09", "2008-01-10", "2008-01-11", "2008-01-12", "2008-01-13", "2008-01-14", "2008-01-15", "2008-01-16", "2008-01-17",
				"2008-01-18", "2008-01-19", "2008-01-20", "2008-01-21", "2008-01-22", "2008-01-23", "2008-01-24", "2008-01-25", "2008-01-26", "2008-01-27",
				"2008-01-28", "2008-01-29", "2008-01-30", "2008-02-01", "2008-02-02", "2008-02-03", "2008-02-04", "2008-02-05", "2008-02-06", "2008-02-07",
				"2008-02-08", "2008-02-09", "2008-02-10", "2008-02-11", "2008-02-12", "2008-02-13", "2008-02-14", "2008-02-15", "2008-02-16", "2008-02-17",
				"2008-02-18", "2008-02-19", "2008-02-20", "2008-02-21", "2008-02-22", "2008-02-23", "2008-02-24", "2008-02-25", "2008-02-26", "2008-02-27",
				"2008-02-29", "2008-03-02", "2008-03-05", "2008-03-06", "2008-03-07", "2008-03-08", "2008-03-09", "2008-03-10", "2008-03-11", "2008-03-12",
				"2008-03-13", "2008-03-14", "2008-03-15", "2008-03-16", "2008-03-17", "2008-03-18", "2008-03-19", "2008-03-20", "2008-03-21", "2008-03-22",
				"2008-03-23", "2008-03-24", "2008-03-25", "2008-03-26", "2008-03-27", "2008-03-28", "2008-03-29", "2008-03-30", "2008-03-31", "2008-04-01",
				"2008-04-02", "2008-04-03", "2008-04-04", "2008-04-05", "2008-04-06", "2008-04-07", "2008-04-08", "2008-04-09", "2008-04-10", "2008-04-11",
				"2008-04-12", "2008-04-13", "2008-04-14", "2008-04-15", "2008-04-16", "2008-04-17", "2008-04-18", "2008-04-19", "2008-04-20", "2008-04-21",
				"2008-04-22", "2008-04-23", "2008-04-24", "2008-04-25", "2008-04-26", "2008-04-27", "2008-04-28", "2008-04-29", "2008-04-30", "2008-05-01",
				"2008-05-02", "2008-05-03", "2008-05-04", "2008-05-05", "2008-05-06", "2008-05-07", "2008-05-08", "2008-05-09", "2008-05-10", "2008-05-11",
				"2008-05-12", "2008-05-13", "2008-05-14", "2008-05-15", "2008-05-16", "2008-05-17", "2008-05-18", "2008-05-19", "2008-05-20", "2008-05-21",
				"2008-05-22", "2008-05-23", "2008-05-24", "2008-05-25", "2008-05-26", "2008-05-27", "2008-05-28", "2008-05-29", "2008-05-30", "2008-05-31",
				"2008-06-03", "2008-06-04", "2008-06-05", "2008-06-06", "2008-06-07", "2008-06-08", "2008-06-09", "2008-06-10", "2008-06-11", "2008-06-12",
				"2008-06-13", "2008-06-14", "2008-06-15", "2008-06-16", "2008-06-17", "2008-06-18", "2008-06-19", "2008-06-20", "2008-06-21", "2008-06-22",
				"2008-06-23", "2008-06-24", "2008-06-25", "2008-06-26", "2008-06-27", "2008-06-28", "2008-06-29", "2008-06-30", "2008-07-02", "2008-07-03",
				"2008-07-04", "2008-07-05", "2008-07-06", "2008-07-07", "2008-07-08", "2008-07-09", "2008-07-10", "2008-07-11", "2008-07-12", "2008-08-01",
				"2008-08-02", "2008-08-03", "2008-08-04", "2008-08-05", "2008-08-06", "2008-08-07", "2008-08-08", "2008-08-09", "2008-08-10", "2008-08-11",
				"2008-08-12", "2008-08-13", "2008-08-14", "2008-08-15", "2008-08-16", "2008-08-17", "2008-08-18", "2008-08-19", "2008-08-20", "2008-08-21",
				"2008-08-22", "2008-08-23", "2008-08-24", "2008-08-25", "2008-08-26", "2008-08-27", "2008-08-28", "2008-08-29", "2008-08-30", "2008-08-31",
				"2008-09-01", "2008-09-02", "2008-09-03", "2008-09-04", "2008-09-05", "2008-09-06", "2008-09-07", "2008-09-08", "2008-09-09", "2008-09-10",
				"2008-09-11", "2008-09-12", "2008-09-13", "2008-09-14", "2008-09-15", "2008-09-16", "2008-09-17", "2008-09-18", "2008-09-19", "2008-09-20",
				"2008-09-21", "2008-09-22", "2008-09-23", "2008-09-24", "2008-09-25", "2008-09-26", "2008-09-27", "2008-09-28", "2008-09-29", "2008-09-30",
				"2008-10-01", "2008-10-02", "2008-10-03", "2008-10-04", "2008-10-05", "2008-10-06", "2008-10-07", "2008-10-08", "2008-10-09", "2008-10-10",
				"2008-10-11", "2008-10-12", "2008-10-13", "2008-10-14", "2008-10-15", "2008-10-16", "2008-10-17", "2008-10-18", "2008-10-19", "2008-10-20",
				"2008-10-23", "2008-10-24", "2008-10-25", "2008-10-26", "2008-10-27", "2008-10-28", "2008-10-29", "2008-10-30", "2008-10-31", "2008-11-01",
				"2008-11-02", "2008-11-03", "2008-11-04", "2008-11-05", "2008-11-06", "2008-11-07", "2008-11-08", "2008-11-09", "2008-11-10", "2008-11-11",
				"2008-11-12", "2008-11-13", "2008-11-14", "2008-11-15", "2008-11-16", "2008-11-17", "2008-11-18", "2008-11-19", "2008-11-20", "2008-11-21",
				"2008-11-22", "2008-11-23", "2008-11-24", "2008-11-25", "2008-11-26", "2008-11-27", "2008-11-28", "2008-11-29", "2008-11-30", "2008-12-01",
				"2008-12-02", "2008-12-03", "2008-12-04", "2008-12-05", "2008-12-06", "2008-12-07", "2008-12-08", "2008-12-09", "2008-12-10", "2008-12-11",
				"2008-12-12", "2008-12-13", "2008-12-14", "2008-12-15", "2008-12-16", "2008-12-17", "2008-12-18", "2008-12-19", "2008-12-20", "2008-12-21",
				"2008-12-22", "2008-12-23", "2008-12-24", "2008-12-25", "2008-12-26", "2008-12-27", "2008-12-28", "2008-12-29", "2008-12-30", "2008-12-31",
				"2009-01-01", "2009-01-02", "2009-01-03", "2009-01-04", "2009-01-05", "2009-01-06", "2009-01-07", "2009-01-08", "2009-01-09", "2009-01-10",
				"2009-01-11", "2009-01-12", "2009-01-13", "2009-01-14", "2009-01-15", "2009-01-16", "2009-01-17", "2009-01-18", "2009-01-19", "2009-01-20",
				"2009-01-21", "2009-01-22", "2009-01-23", "2009-01-24", "2009-01-25", "2009-01-26", "2009-01-27", "2009-01-28", "2009-01-29", "2009-01-30",
				"2009-01-31", "2009-02-01", "2009-02-02", "2009-02-03", "2009-02-04", "2009-02-05", "2009-02-06", "2009-02-07", "2009-02-08", "2009-02-09",
				"2009-02-10", "2009-02-11", "2009-02-12", "2009-02-13", "2009-02-14", "2009-02-15", "2009-02-16", "2009-02-17", "2009-02-18", "2009-02-19",
				"2009-02-20", "2009-02-21", "2009-02-22", "2009-02-23", "2009-02-24", "2009-02-25", "2009-02-26", "2009-02-27", "2009-02-28", "2009-03-01",
				"2009-03-02", "2009-03-03", "2009-03-04", "2009-03-05", "2009-03-06", "2009-03-07", "2009-03-08", "2009-03-09", "2009-03-10", "2009-03-11",
				"2009-03-12", "2009-03-13", "2009-03-14", "2009-03-15", "2009-03-16", "2009-03-17", "2009-03-18", "2009-03-19", "2009-03-20", "2009-03-21",
				"2009-03-22", "2009-03-23", "2009-03-24", "2009-03-25", "2009-03-26", "2009-03-27", "2009-03-28", "2009-03-29", "2009-03-30", "2009-03-31",
				"2009-04-01", "2009-04-02", "2009-04-03", "2009-04-04", "2009-04-05", "2009-04-06", "2009-04-07", "2009-04-08", "2009-04-09", "2009-04-10",
				"2009-04-11", "2009-04-12", "2009-04-13", "2009-04-14", "2009-04-15", "2009-04-16", "2009-04-17", "2009-04-18", "2009-04-19", "2009-04-20",
				"2009-04-21", "2009-04-22", "2009-04-23", "2009-04-24", "2009-04-25", "2009-04-26", "2009-04-27", "2009-04-28", "2009-04-29", "2009-04-30",
				"2009-05-01", "2009-05-02", "2009-05-03", "2009-05-04", "2009-05-05", "2009-05-06", "2009-05-07", "2009-05-08", "2009-05-09", "2009-05-10",
				"2009-05-11", "2009-05-12", "2009-05-13", "2009-05-14", "2009-05-15", "2009-05-16", "2009-05-17", "2009-05-18", "2009-05-19", "2009-05-20",
				"2009-05-21", "2009-05-22", "2009-05-23", "2009-05-24", "2009-05-25", "2009-05-26", "2009-05-27", "2009-05-28", "2009-05-29", "2009-05-30",
				"2009-05-31", "2009-06-01", "2009-06-02", "2009-06-03", "2009-06-04", "2009-06-05", "2009-06-06", "2009-06-07", "2009-06-08", "2009-06-09",
				"2009-06-10", "2009-06-11", "2009-06-12", "2009-06-13", "2009-06-14", "2009-06-15", "2009-06-16", "2009-06-17", "2009-06-18", "2009-06-19",
				"2009-06-20", "2009-06-21", "2009-06-22", "2009-06-23", "2009-06-24", "2009-06-25", "2009-06-26", "2009-06-27", "2009-06-28", "2009-06-29",
				"2009-06-30", "2009-07-01", "2009-07-02", "2009-07-03", "2009-07-04", "2009-07-05", "2009-07-06", "2009-07-07", "2009-07-08", "2009-07-09",
				"2009-07-10", "2009-07-11", "2009-07-12", "2009-07-13", "2009-07-14", "2009-07-15", "2009-07-16", "2009-07-17", "2009-07-18", "2009-07-19",
				"2009-07-20", "2009-07-21", "2009-07-22", "2009-07-23", "2009-07-24", "2009-07-25", "2009-07-26", "2009-07-27", "2009-07-28", "2009-07-29",
				"2009-07-30", "2009-07-31", "2009-08-01", "2009-08-02", "2009-08-03", "2009-08-04", "2009-08-05", "2009-08-06", "2009-08-07", "2009-08-08",
				"2009-08-09", "2009-08-10", "2009-08-11", "2009-08-12", "2009-08-13", "2009-08-14", "2009-08-15", "2009-08-16", "2009-08-17", "2009-08-18",
				"2009-08-19", "2009-08-20", "2009-08-21", "2009-08-22", "2009-08-23", "2009-08-24", "2009-08-25", "2009-08-26", "2009-08-27", "2009-08-28",
				"2009-08-29", "2009-08-30", "2009-08-31", "2009-09-01", "2009-09-02", "2009-09-03", "2009-09-04", "2009-09-05", "2009-09-06", "2009-09-07",
				"2009-09-08", "2009-09-09", "2009-09-10", "2009-09-11", "2009-09-12", "2009-09-13", "2009-09-14", "2009-09-15", "2009-09-16", "2009-09-17",
				"2009-09-18", "2009-09-19", "2009-09-20", "2009-09-21", "2009-09-22", "2009-09-24", "2009-09-28", "2009-09-29", "2009-09-30", "2009-10-01",
				"2009-10-02", "2009-10-03", "2009-10-04", "2009-10-05", "2009-10-06", "2009-10-07", "2009-10-08", "2009-10-09", "2009-10-10", "2009-10-11",
				"2009-10-12", "2009-10-13", "2009-10-17", "2009-10-18", "2009-10-19", "2009-10-20", "2009-10-21", "2009-10-22", "2009-10-23", "2009-10-24",
				"2009-10-25", "2009-10-26", "2009-10-27", "2009-10-28", "2009-10-29", "2009-10-30", "2009-10-31", "2009-11-01", "2009-11-02", "2009-11-03",
				"2009-11-04", "2009-11-05", "2009-11-06", "2009-11-07", "2009-11-08", "2009-11-09", "2009-11-10", "2009-11-11", "2009-11-12", "2009-11-13",
				"2009-11-14", "2009-11-16", "2009-11-17", "2009-11-18", "2009-11-19", "2009-11-20", "2009-11-21", "2009-11-23", "2009-11-24", "2009-11-25",
				"2009-11-26", "2009-11-27", "2009-11-28", "2009-11-29", "2009-11-30", "2009-12-01", "2009-12-02", "2009-12-03", "2009-12-04", "2009-12-05",
				"2009-12-06", "2009-12-07", "2009-12-08", "2009-12-09", "2009-12-10", "2009-12-11", "2009-12-12", "2009-12-13", "2009-12-14", "2009-12-15",
				"2009-12-16", "2009-12-17", "2009-12-18", "2009-12-19", "2009-12-20", "2009-12-21", "2009-12-22", "2009-12-23", "2009-12-24", "2009-12-25",
				"2009-12-26", "2009-12-27", "2009-12-28", "2009-12-29", "2009-12-30", "2009-12-31", "2010-01-01", "2010-01-02", "2010-01-03", "2010-01-04",
				"2010-01-05", "2010-01-06", "2010-01-07", "2010-01-08", "2010-01-09", "2010-01-10", "2010-01-11", "2010-01-12", "2010-01-13", "2010-01-14",
				"2010-01-15", "2010-01-16", "2010-01-17", "2010-01-18", "2010-01-19", "2010-01-20", "2010-01-21", "2010-01-22", "2010-01-25", "2010-01-26",
				"2010-01-27", "2010-01-28", "2010-01-29", "2010-01-30", "2010-01-31", "2010-02-01", "2010-02-02", "2010-02-03", "2010-02-04", "2010-02-05",
				"2010-02-06", "2010-02-07", "2010-02-09", "2010-02-10", "2010-02-11", "2010-02-12", "2010-02-13", "2010-02-14", "2010-02-15", "2010-02-16",
				"2010-02-17", "2010-02-18", "2010-02-19", "2010-02-20", "2010-02-21", "2010-02-22", "2010-02-24", "2010-02-25", "2010-02-26", "2010-02-27",
				"2010-02-28", "2010-03-01", "2010-03-02", "2010-03-03", "2010-03-04", "2010-03-05", "2010-03-06", "2010-03-07", "2010-03-08", "2010-03-09",
				"2010-03-10", "2010-03-11", "2010-03-12", "2010-03-13", "2010-03-14", "2010-03-15", "2010-03-16", "2010-03-17", "2010-03-18", "2010-03-19",
				"2010-03-20", "2010-03-21", "2010-03-22", "2010-03-23", "2010-03-24", "2010-03-25", "2010-03-26", "2010-03-27", "2010-03-28", "2010-03-29",
				"2010-03-30", "2010-03-31", "2010-04-01", "2010-04-02", "2010-04-03", "2010-04-04", "2010-04-05", "2010-04-06", "2010-04-07", "2010-04-08",
				"2010-04-09", "2010-04-10", "2010-04-11", "2010-04-12", "2010-04-13", "2010-04-14", "2010-04-15", "2010-04-16", "2010-04-17", "2010-04-18",
				"2010-04-19", "2010-04-20", "2010-04-21", "2010-04-22", "2010-04-23", "2010-04-24", "2010-04-25", "2010-04-26", "2010-04-27", "2010-04-28",
				"2010-04-29", "2010-04-30", "2010-05-01", "2010-05-02", "2010-05-03", "2010-05-04", "2010-05-05", "2010-05-06", "2010-05-07", "2010-05-08",
				"2010-05-09", "2010-05-10", "2010-05-11", "2010-05-12", "2010-05-13", "2010-05-14", "2010-05-15", "2010-05-16", "2010-05-17", "2010-05-18",
				"2010-05-19", "2010-05-20", "2010-05-21", "2010-05-22", "2010-05-23", "2010-05-24", "2010-05-25", "2010-05-26", "2010-05-27", "2010-05-28",
				"2010-05-29", "2010-05-30", "2010-05-31", "2010-06-01", "2010-06-02", "2010-06-03", "2010-06-04", "2010-06-05", "2010-06-06", "2010-06-07",
				"2010-06-08", "2010-06-09", "2010-06-10", "2010-06-11", "2010-06-12", "2010-06-13", "2010-06-14", "2010-06-15", "2010-06-16", "2010-06-17",
				"2010-06-18", "2010-06-19", "2010-06-20", "2010-06-21", "2010-06-22", "2010-06-23", "2010-06-24", "2010-06-25", "2010-06-27", "2010-06-29",
				"2010-06-30", "2010-07-01", "2010-07-02", "2010-07-03", "2010-07-04", "2010-07-06", "2010-07-11", "2010-07-12", "2010-07-13", "2010-07-14",
				"2010-07-15", "2010-07-16", "2010-07-17", "2010-07-18", "2010-07-19", "2010-07-20", "2010-07-21", "2010-07-22", "2010-07-23", "2010-07-24",
				"2010-07-25", "2010-07-26", "2010-07-27", "2010-07-28", "2010-07-29", "2010-07-30", "2010-07-31", "2010-08-01", "2010-08-02", "2010-08-03",
				"2010-08-04", "2010-08-05", "2010-08-06", "2010-08-07", "2010-08-08", "2010-08-09", "2010-08-10", "2010-08-11", "2010-08-12", "2010-08-13",
				"2010-08-14", "2010-08-15", "2010-08-16", "2010-08-17", "2010-08-18", "2010-08-19", "2010-08-20", "2010-08-21", "2010-08-22", "2010-08-23",
				"2010-08-24", "2010-08-25", "2010-08-26", "2010-08-27", "2010-08-28", "2010-08-29", "2010-08-30", "2010-08-31", "2010-09-01", "2010-09-02",
				"2010-09-03", "2010-09-04", "2010-09-05", "2010-09-06", "2010-09-07", "2010-09-08", "2010-09-09", "2010-09-10", "2010-09-11", "2010-09-12",
				"2010-09-13", "2010-09-14", "2010-09-15", "2010-09-16", "2010-09-17", "2010-09-18", "2010-09-19", "2010-09-20", "2010-09-21", "2010-09-22",
				"2010-09-23", "2010-09-24", "2010-09-25", "2010-09-26", "2010-09-27", "2010-09-28", "2010-09-29", "2010-09-30", "2010-10-01", "2010-10-02",
				"2010-10-03", "2010-10-04", "2010-10-05", "2010-10-06", "2010-10-07", "2010-10-08", "2010-10-09", "2010-10-10", "2010-10-11", "2010-10-12",
				"2010-10-13", "2010-10-14", "2010-10-15", "2010-10-16", "2010-10-17", "2010-10-18", "2010-10-19", "2010-10-20", "2010-10-21", "2010-10-22",
				"2010-10-23", "2010-10-24", "2010-10-25", "2010-10-26", "2010-10-27", "2010-10-28", "2010-10-29", "2010-10-30", "2010-10-31", "2010-11-01",
				"2010-11-02", "2010-11-03", "2010-11-04", "2010-11-05", "2010-11-06", "2010-11-07", "2010-11-08", "2010-11-09", "2010-11-10", "2010-11-11",
				"2010-11-12", "2010-11-13", "2010-11-14", "2010-11-15", "2010-11-16", "2010-11-17", "2010-11-18", "2010-11-19", "2010-11-20", "2010-11-21",
				"2010-11-22", "2010-11-23", "2010-11-24", "2010-11-25", "2010-11-26", "2010-11-27", "2010-11-28", "2010-11-29", "2010-11-30", "2010-12-01",
				"2010-12-02", "2010-12-03", "2010-12-04", "2010-12-05", "2010-12-06", "2010-12-07", "2010-12-08", "2010-12-09", "2010-12-10", "2010-12-11",
				"2010-12-12", "2010-12-13", "2010-12-14", "2010-12-15", "2010-12-16", "2010-12-17", "2010-12-18", "2010-12-19", "2010-12-20", "2010-12-21",
				"2010-12-22", "2010-12-23", "2010-12-24", "2010-12-25", "2010-12-26", "2010-12-27", "2010-12-28", "2010-12-29", "2010-12-30", "2010-12-31",
				"2011-01-01", "2011-01-02", "2011-01-03", "2011-01-04", "2011-01-05", "2011-01-06", "2011-01-07", "2011-01-08", "2011-01-09", "2011-01-10",
				"2011-01-11", "2011-01-12", "2011-01-13", "2011-01-14", "2011-01-15", "2011-01-16", "2011-01-17", "2011-01-18", "2011-01-19", "2011-01-20",
				"2011-01-21", "2011-01-22", "2011-01-23", "2011-01-24", "2011-01-25", "2011-01-26", "2011-01-27", "2011-01-28", "2011-01-29", "2011-01-30",
				"2011-01-31", "2011-02-01", "2011-02-02", "2011-02-03", "2011-02-04", "2011-02-05", "2011-02-06", "2011-02-07", "2011-02-08", "2011-02-09",
				"2011-02-10", "2011-02-11", "2011-02-12", "2011-02-13", "2011-02-14", "2011-02-15", "2011-02-16", "2011-02-17", "2011-02-18", "2011-02-19",
				"2011-02-20", "2011-02-21", "2011-02-22", "2011-02-23", "2011-02-24", "2011-02-25", "2011-02-26", "2011-02-27", "2011-02-28", "2011-03-01",
				"2011-03-02", "2011-03-03", "2011-03-04", "2011-03-05", "2011-03-06", "2011-03-07", "2011-03-08", "2011-03-09", "2011-03-10", "2011-03-11",
				"2011-03-12", "2011-03-13", "2011-03-14", "2011-03-15", "2011-03-16", "2011-03-17", "2011-03-18", "2011-03-19", "2011-03-20", "2011-03-21",
				"2011-03-22", "2011-03-23", "2011-03-24", "2011-03-25", "2011-03-26", "2011-03-27", "2011-03-28", "2011-03-29", "2011-03-30", "2011-03-31",
				"2011-04-01", "2011-04-02", "2011-04-03", "2011-04-04", "2011-04-05", "2011-04-06", "2011-04-07", "2011-04-08", "2011-04-09", "2011-04-10",
				"2011-04-11", "2011-04-12", "2011-04-13", "2011-04-14", "2011-04-15", "2011-04-16", "2011-04-17", "2011-04-18", "2011-04-19", "2011-04-20",
				"2011-04-21", "2011-04-22", "2011-04-23", "2011-04-24", "2011-04-25", "2011-04-26", "2011-04-27", "2011-04-28", "2011-04-29", "2011-04-30",
				"2011-05-01", "2011-05-02", "2011-05-03", "2011-05-04", "2011-05-05", "2011-05-06", "2011-05-07", "2011-05-08", "2011-05-09", "2011-05-10",
				"2011-05-11", "2011-05-12", "2011-05-13", "2011-05-14", "2011-05-15", "2011-05-16", "2011-05-17", "2011-05-18", "2011-05-19", "2011-05-20",
				"2011-05-21", "2011-05-22", "2011-05-23", "2011-05-24", "2011-05-25", "2011-05-26", "2011-05-27", "2011-05-28", "2011-05-29", "2011-05-30",
				"2011-05-31", "2011-06-01", "2011-06-02", "2011-06-03", "2011-06-04", "2011-06-05", "2011-06-06", "2011-06-07", "2011-06-08", "2011-06-09",
				"2011-06-10", "2011-06-11", "2011-06-12", "2011-06-13", "2011-06-14", "2011-06-15", "2011-06-16", "2011-06-17", "2011-06-18", "2011-06-19",
				"2011-06-20", "2011-06-21", "2011-06-22", "2011-06-23", "2011-06-24", "2011-06-25", "2011-06-26", "2011-06-27", "2011-06-28", "2011-06-29",
				"2011-06-30", "2011-07-01", "2011-07-02", "2011-07-03", "2011-07-04", "2011-07-05", "2011-07-06", "2011-07-07", "2011-07-08", "2011-07-09",
				"2011-07-10", "2011-07-11", "2011-07-12", "2011-07-13", "2011-07-14", "2011-07-15", "2011-07-16", "2011-07-17", "2011-07-18", "2011-07-19",
				"2011-07-20", "2011-07-21", "2011-07-22", "2011-07-23", "2011-07-24", "2011-07-25", "2011-07-26", "2011-07-27", "2011-07-28", "2011-07-29",
				"2011-07-30", "2011-07-31", "2011-08-01", "2011-08-02", "2011-08-03", "2011-08-04", "2011-08-05", "2011-08-06", "2011-08-07", "2011-08-08",
				"2011-08-09", "2011-08-10", "2011-08-11", "2011-08-12", "2011-08-13", "2011-08-14", "2011-08-15", "2011-08-16", "2011-08-17", "2011-08-18",
				"2011-08-19", "2011-08-20", "2011-08-21", "2011-08-22", "2011-08-23", "2011-08-24", "2011-08-25", "2011-08-26", "2011-08-27", "2011-08-28",
				"2011-08-29", "2011-08-30", "2011-08-31", "2011-09-01", "2011-09-03", "2011-09-04", "2011-09-05", "2011-09-06", "2011-09-07", "2011-09-08",
				"2011-09-09", "2011-09-10", "2011-09-11", "2011-09-12", "2011-09-13", "2011-09-14", "2011-09-15", "2011-09-16", "2011-09-17", "2011-09-18",
				"2011-09-19", "2011-09-20", "2011-09-21", "2011-09-22", "2011-09-23", "2011-09-24", "2011-09-25", "2011-09-26", "2011-09-27", "2011-09-28",
				"2011-09-29", "2011-09-30", "2011-10-01", "2011-10-02", "2011-10-03", "2011-10-04", "2011-10-05", "2011-10-06", "2011-10-07", "2011-10-08",
				"2011-10-09", "2011-10-10", "2011-10-11", "2011-10-12", "2011-10-13", "2011-10-14", "2011-10-15", "2011-10-16", "2011-10-17", "2011-10-18",
				"2011-10-19", "2011-10-21", "2011-10-22", "2011-10-23", "2011-10-24", "2011-10-25", "2011-10-26", "2011-10-27", "2011-10-28", "2011-10-29",
				"2011-10-30", "2011-10-31", "2011-11-01", "2011-11-02", "2011-11-03", "2011-11-04", "2011-11-05", "2011-11-06", "2011-11-07", "2011-11-08",
				"2011-11-09", "2011-11-10", "2011-11-11", "2011-11-12", "2011-11-13", "2011-11-14", "2011-11-15", "2011-11-16", "2011-11-17", "2011-11-18",
				"2011-11-19", "2011-11-20", "2011-11-21", "2011-11-22", "2011-11-23", "2011-11-24", "2011-11-25", "2011-11-26", "2011-11-27", "2011-11-28",
				"2011-11-29", "2011-11-30", "2011-12-01", "2011-12-02", "2011-12-03", "2011-12-04", "2011-12-05", "2011-12-06", "2011-12-07", "2011-12-08",
				"2011-12-09", "2011-12-10", "2011-12-11", "2011-12-12", "2011-12-13", "2011-12-14", "2011-12-15", "2011-12-16", "2011-12-17", "2011-12-18",
				"2011-12-19", "2011-12-20", "2011-12-21", "2011-12-22", "2011-12-23", "2011-12-26", "2011-12-27", "2011-12-28", "2011-12-29", "2011-12-30",
				"2011-12-31", "2012-01-01", "2012-01-02", "2012-01-03", "2012-01-04", "2012-01-05", "2012-01-06", "2012-01-07", "2012-01-08", "2012-01-09",
				"2012-01-10", "2012-01-11", "2012-01-12", "2012-01-13", "2012-01-14", "2012-01-15", "2012-01-16", "2012-01-17", "2012-01-18", "2012-01-19",
				"2012-01-20", "2012-01-21", "2012-01-22", "2012-01-23", "2012-01-24", "2012-01-25", "2012-01-26", "2012-01-27", "2012-01-28", "2012-01-29",
				"2012-01-30", "2012-01-31", "2012-02-01", "2012-02-02", "2012-02-03", "2012-02-04", "2012-02-05", "2012-02-06", "2012-02-07", "2012-02-08",
				"2012-02-09", "2012-02-10", "2012-02-11", "2012-02-12", "2012-02-13", "2012-02-14", "2012-02-15", "2012-02-16", "2012-02-17", "2012-02-18",
				"2012-02-19", "2012-02-20", "2012-02-21", "2012-02-22", "2012-02-23", "2012-02-24", "2012-02-25", "2012-02-26", "2012-02-27", "2012-02-28",
				"2012-02-29", "2012-03-01", "2012-03-02", "2012-03-03", "2012-03-04", "2012-03-05", "2012-03-06", "2012-03-07", "2012-03-08", "2012-03-09",
				"2012-03-10", "2012-03-11", "2012-03-12", "2012-03-13", "2012-03-14", "2012-03-15", "2012-03-16", "2012-03-17", "2012-03-18", "2012-03-19",
				"2012-03-20", "2012-03-21", "2012-03-22", "2012-03-23", "2012-03-24", "2012-03-25", "2012-03-26", "2012-03-27", "2012-03-28", "2012-03-29",
				"2012-03-30", "2012-03-31", "2012-04-01", "2012-04-02", "2012-04-03", "2012-04-04", "2012-04-05", "2012-04-06", "2012-04-07", "2012-04-08",
				"2012-04-09", "2012-04-10", "2012-04-11", "2012-04-12", "2012-04-13", "2012-04-14", "2012-04-15", "2012-04-16", "2012-04-17", "2012-04-18",
				"2012-04-19", "2012-04-20", "2012-04-21", "2012-04-22", "2012-04-23", "2012-04-24", "2012-04-25", "2012-04-26", "2012-04-27", "2012-04-28",
				"2012-04-29", "2012-05-01", "2012-05-02", "2012-05-03", "2012-05-04", "2012-05-05", "2012-05-06", "2012-05-07", "2012-05-08", "2012-05-09",
				"2012-05-10", "2012-05-11", "2012-05-12", "2012-05-13", "2012-05-14", "2012-05-15", "2012-05-16", "2012-05-17", "2012-05-18", "2012-05-19",
				"2012-05-20", "2012-05-21", "2012-05-22", "2012-05-23", "2012-05-24", "2012-05-25", "2012-05-26", "2012-05-27", "2012-05-28", "2012-05-29",
				"2012-05-30", "2012-05-31", "2012-06-01", "2012-06-02", "2012-06-03", "2012-06-04", "2012-06-05", "2012-06-06", "2012-06-07", "2012-06-08",
				"2012-06-09", "2012-06-10", "2012-06-11", "2012-06-12", "2012-06-13", "2012-06-14", "2012-06-15", "2012-06-16", "2012-06-17", "2012-06-18",
				"2012-06-19", "2012-06-20", "2012-06-21", "2012-06-22", "2012-06-23", "2012-06-24", "2012-06-25", "2012-06-26", "2012-06-27", "2012-06-28",
				"2012-06-29", "2012-06-30", "2012-07-01", "2012-07-02", "2012-07-03", "2012-07-04", "2012-07-05", "2012-07-06", "2012-07-07", "2012-07-08",
				"2012-07-09", "2012-07-10", "2012-07-11", "2012-07-12", "2012-07-13", "2012-07-14", "2012-07-15", "2012-07-16", "2012-07-17", "2012-07-18",
				"2012-07-19", "2012-07-20", "2012-07-21", "2012-07-22", "2012-07-23", "2012-07-24", "2012-07-25", "2012-07-26", "2012-07-27", "2012-07-28",
				"2012-07-29", "2012-07-30", "2012-07-31", "2012-08-01", "2012-08-02", "2012-08-03", "2012-08-04", "2012-08-05", "2012-08-06", "2012-08-07",
				"2012-08-08", "2012-08-09", "2012-08-10", "2012-08-11", "2012-08-12", "2012-08-13", "2012-08-14", "2012-08-15", "2012-08-16", "2012-08-17",
				"2012-08-18", "2012-08-19", "2012-08-20", "2012-08-21", "2012-08-22", "2012-08-23", "2012-08-24", "2012-08-25", "2012-08-26", "2012-08-27",
				"2012-08-28", "2012-08-29", "2012-08-30", "2012-08-31", "2012-09-01", "2012-09-02", "2012-09-03", "2012-09-04", "2012-09-05", "2012-09-06",
				"2012-09-07", "2012-09-08", "2012-09-09", "2012-09-10", "2012-09-11", "2012-09-12", "2012-09-13", "2012-09-14", "2012-09-15", "2012-09-16",
				"2012-09-17", "2012-09-18", "2012-09-19", "2012-09-20", "2012-09-21", "2012-09-22", "2012-09-23", "2012-09-24", "2012-09-25", "2012-09-26",
				"2012-09-27", "2012-09-28", "2012-09-29", "2012-09-30", "2012-10-01", "2012-10-02", "2012-10-03", "2012-10-04", "2012-10-05", "2012-10-06",
				"2012-10-07", "2012-10-08", "2012-10-09", "2012-10-10", "2012-10-11", "2012-10-12", "2012-10-13", "2012-10-14", "2012-10-15", "2012-10-16",
				"2012-10-17", "2012-10-18", "2012-10-19", "2012-10-20", "2012-10-21", "2012-10-22", "2012-10-23", "2012-10-24", "2012-10-25", "2012-10-26",
				"2012-10-27", "2012-10-28", "2012-10-29", "2012-10-30", "2012-10-31", "2012-11-01", "2012-11-02", "2012-11-03", "2012-11-04", "2012-11-05",
				"2012-11-06", "2012-11-07", "2012-11-08", "2012-11-09", "2012-11-10", "2012-11-11", "2012-11-12", "2012-11-13", "2012-11-14", "2012-11-15",
				"2012-11-16", "2012-11-17", "2012-11-18", "2012-11-19", "2012-11-20", "2012-11-21", "2012-11-22", "2012-11-23", "2012-11-24", "2012-11-25",
				"2012-11-26", "2012-11-27", "2012-11-28", "2012-11-29", "2012-11-30", "2012-12-01", "2012-12-02", "2012-12-03", "2012-12-04", "2012-12-05",
				"2012-12-06", "2012-12-07", "2012-12-08", "2012-12-09", "2012-12-10", "2012-12-11", "2012-12-12", "2012-12-13", "2012-12-14", "2012-12-15",
				"2012-12-16", "2012-12-17", "2012-12-18", "2012-12-19", "2012-12-20", "2012-12-21", "2012-12-22", "2012-12-23", "2012-12-24", "2012-12-25",
				"2012-12-26", "2012-12-27", "2012-12-28", "2012-12-29", "2012-12-30", "2012-12-31", "2013-01-01", "2013-01-02", "2013-01-03", "2013-01-04",
				"2013-01-05", "2013-01-06", "2013-01-07", "2013-01-08", "2013-01-09", "2013-01-10", "2013-01-11", "2013-01-12", "2013-01-13", "2013-01-14",
				"2013-01-15", "2013-01-16", "2013-01-17", "2013-01-18", "2013-01-19", "2013-01-20", "2013-01-21", "2013-01-22", "2013-01-23", "2013-01-24",
				"2013-01-25", "2013-01-26", "2013-01-27", "2013-01-28", "2013-01-29", "2013-01-30", "2013-01-31", "2013-02-01", "2013-02-02", "2013-02-03",
				"2013-02-04", "2013-02-05", "2013-02-06", "2013-02-07", "2013-02-08", "2013-02-09", "2013-02-10", "2013-02-11", "2013-02-12", "2013-02-13",
				"2013-02-14", "2013-02-15", "2013-02-16", "2013-02-17", "2013-02-18", "2013-02-19", "2013-02-20", "2013-02-21", "2013-02-22", "2013-02-23",
				"2013-02-24", "2013-02-25", "2013-02-26", "2013-02-27", "2013-02-28", "2013-03-01", "2013-03-02", "2013-03-03", "2013-03-04", "2013-03-05",
				"2013-03-06", "2013-03-07", "2013-03-08", "2013-03-09", "2013-03-10", "2013-03-11", "2013-03-12", "2013-03-13", "2013-03-14", "2013-03-15",
				"2013-03-16", "2013-03-17", "2013-03-18", "2013-03-19", "2013-03-20", "2013-03-21", "2013-03-22", "2013-03-23", "2013-03-24", "2013-03-25",
				"2013-03-26", "2013-03-27", "2013-03-28", "2013-03-29", "2013-03-30", "2013-03-31", "2013-04-01", "2013-04-02", "2013-04-03", "2013-04-04",
				"2013-04-05", "2013-04-06", "2013-04-07", "2013-04-08", "2013-04-09", "2013-04-10", "2013-04-11", "2013-04-12", "2013-04-13", "2013-04-14",
				"2013-04-15", "2013-04-16", "2013-04-17", "2013-04-18", "2013-04-19", "2013-04-20", "2013-04-21", "2013-04-22", "2013-04-23", "2013-04-24",
				"2013-04-25", "2013-04-26", "2013-04-27", "2013-04-28", "2013-04-29", "2013-04-30", "2013-05-01", "2013-05-02", "2013-05-03", "2013-05-04",
				"2013-05-05", "2013-05-06", "2013-05-07", "2013-05-08", "2013-05-09", "2013-05-10", "2013-05-11", "2013-05-12", "2013-05-13", "2013-05-14",
				"2013-05-15", "2013-05-16", "2013-05-17", "2013-05-18", "2013-05-19", "2013-05-20", "2013-05-21", "2013-05-22", "2013-05-23", "2013-05-24",
				"2013-05-25", "2013-05-26", "2013-05-27", "2013-05-28", "2013-05-29", "2013-05-30", "2013-05-31", "2013-06-01", "2013-06-02", "2013-06-03",
				"2013-06-04", "2013-06-05", "2013-06-06", "2013-06-07", "2013-06-08", "2013-06-09", "2013-06-10", "2013-06-11", "2013-06-12", "2013-06-13",
				"2013-06-14", "2013-06-15", "2013-06-16", "2013-06-17", "2013-06-18", "2013-06-19", "2013-06-20", "2013-06-21", "2013-06-22", "2013-06-23",
				"2013-06-24", "2013-06-25", "2013-06-26", "2013-06-27", "2013-06-28", "2013-06-29", "2013-06-30", "2013-07-01", "2013-07-02", "2013-07-03",
				"2013-07-04", "2013-07-05", "2013-07-06", "2013-07-07", "2013-07-08", "2013-07-09", "2013-07-10", "2013-07-11", "2013-07-12", "2013-07-13",
				"2013-07-14", "2013-07-15", "2013-07-16", "2013-07-17", "2013-07-18", "2013-07-19", "2013-07-20", "2013-07-21", "2013-07-22", "2013-07-24",
				"2013-07-25", "2013-07-26", "2013-07-27", "2013-07-28", "2013-07-29", "2013-07-30", "2013-07-31", "2013-08-01", "2013-08-02", "2013-08-03",
				"2013-08-04", "2013-08-05", "2013-08-06", "2013-08-07", "2013-08-08", "2013-08-09", "2013-08-10", "2013-08-11", "2013-08-12", "2013-08-13",
				"2013-08-14", "2013-08-15", "2013-08-16", "2013-08-17", "2013-08-18", "2013-08-19", "2013-08-20", "2013-08-21", "2013-08-22", "2013-08-23",
				"2013-08-24", "2013-08-25", "2013-08-26", "2013-08-27", "2013-08-28", "2013-08-29", "2013-08-30", "2013-08-31", "2013-09-01", "2013-09-02",
				"2013-09-03", "2013-09-04", "2013-09-05", "2013-09-06", "2013-09-07", "2013-09-08", "2013-09-09", "2013-09-10", "2013-09-11", "2013-09-12",
				"2013-09-13", "2013-09-14", "2013-09-15", "2013-09-16", "2013-09-17", "2013-09-18", "2013-09-19", "2013-09-20", "2013-09-21", "2013-09-22",
				"2013-09-23", "2013-09-24", "2013-09-25", "2013-09-26", "2013-09-27", "2013-09-28", "2013-09-29", "2013-09-30", "2013-10-01", "2013-10-02",
				"2013-10-03", "2013-10-04", "2013-10-05", "2013-10-06", "2013-10-07", "2013-10-08", "2013-10-09", "2013-10-10", "2013-10-11", "2013-10-12",
				"2013-10-13", "2013-10-14", "2013-10-15", "2013-10-16", "2013-10-17", "2013-10-18", "2013-10-19", "2013-10-20", "2013-10-21", "2013-10-22",
				"2013-10-23", "2013-10-24", "2013-10-25", "2013-10-26", "2013-10-27", "2013-10-28", "2013-10-29", "2013-10-30", "2013-10-31", "2013-11-01",
				"2013-11-02", "2013-11-03", "2013-11-04", "2013-11-05", "2013-11-06", "2013-11-07", "2013-11-08", "2013-11-09", "2013-11-10", "2013-11-11",
				"2013-11-12", "2013-11-13", "2013-11-14", "2013-11-15", "2013-11-16", "2013-11-17", "2013-11-18", "2013-11-19", "2013-11-20", "2013-11-21",
				"2013-11-22", "2013-11-23", "2013-11-24", "2013-11-25", "2013-11-26", "2013-11-27", "2013-11-28", "2013-11-29", "2013-11-30", "2013-12-01",
				"2013-12-02", "2013-12-03", "2013-12-04", "2013-12-05", "2013-12-06", "2013-12-07", "2013-12-08", "2013-12-09", "2013-12-10", "2013-12-11",
				"2013-12-12", "2013-12-13", "2013-12-14", "2013-12-15", "2013-12-16", "2013-12-17", "2013-12-18", "2013-12-19", "2013-12-20", "2013-12-21",
				"2013-12-22", "2013-12-23", "2013-12-24", "2013-12-25", "2013-12-26", "2013-12-27", "2013-12-28", "2013-12-29", "2013-12-30", "2013-12-31",
				"2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04", "2014-01-05", "2014-01-07", "2014-01-08", "2014-01-09", "2014-01-10", "2014-01-11",
				"2014-01-12", "2014-01-13", "2014-01-14", "2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19", "2014-01-20", "2014-01-21",
				"2014-01-22", "2014-01-23", "2014-01-24", "2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29", "2014-01-30", "2014-01-31",
				"2014-02-01", "2014-02-02", "2014-02-03", "2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08", "2014-02-09", "2014-02-10",
				"2014-02-11", "2014-02-12", "2014-02-13", "2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18", "2014-02-19", "2014-02-20",
				"2014-02-21", "2014-02-22", "2014-02-23", "2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28", "2014-03-01", "2014-03-02",
				"2014-03-03", "2014-03-04", "2014-03-05", "2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10", "2014-03-11", "2014-03-12",
				"2014-03-13", "2014-03-14", "2014-03-15", "2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20", "2014-03-21", "2014-03-22",
				"2014-03-23", "2014-03-24", "2014-03-25", "2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30", "2014-03-31", "2014-04-01",
				"2014-04-02", "2014-04-03", "2014-04-04", "2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09", "2014-04-10", "2014-04-11",
				"2014-04-12", "2014-04-13", "2014-04-14", "2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19", "2014-04-20", "2014-04-21",
				"2014-04-22", "2014-04-23", "2014-04-24", "2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29", "2014-04-30", "2014-05-01",
				"2014-05-02", "2014-05-03", "2014-05-04", "2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09", "2014-05-10", "2014-05-11",
				"2014-05-12", "2014-05-13", "2014-05-14", "2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19", "2014-05-20", "2014-05-21",
				"2014-05-22", "2014-05-23", "2014-05-24", "2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29", "2014-05-30", "2014-05-31",
				"2014-06-01", "2014-06-02", "2014-06-03", "2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08", "2014-06-09", "2014-06-10",
				"2014-06-11", "2014-06-12", "2014-06-13", "2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18", "2014-06-19", "2014-06-20",
				"2014-06-21", "2014-06-22", "2014-06-23", "2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28", "2014-06-29", "2014-06-30",
				"2014-07-01", "2014-07-02", "2014-07-03", "2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08", "2014-07-09", "2014-07-10",
				"2014-07-11", "2014-07-12", "2014-07-13", "2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18", "2014-07-19", "2014-07-20",
				"2014-07-21", "2014-07-22", "2014-07-23", "2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28", "2014-07-29", "2014-07-30",
				"2014-07-31", "2014-08-01", "2014-08-02", "2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07", "2014-08-08", "2014-08-09",
				"2014-08-10", "2014-08-11", "2014-08-12", "2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17", "2014-08-18", "2014-08-19",
				"2014-08-20", "2014-08-21", "2014-08-22", "2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27", "2014-08-29", "2014-08-30",
				"2014-08-31", "2014-09-01", "2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06", "2014-09-07", "2014-09-08", "2014-09-09",
				"2014-09-10", "2014-09-11", "2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16", "2014-09-17", "2014-09-18", "2014-09-19",
				"2014-09-20", "2014-09-21", "2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26", "2014-09-27", "2014-09-28", "2014-09-29",
				"2014-09-30", "2014-10-01", "2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06", "2014-10-07", "2014-10-08", "2014-10-09",
				"2014-10-10", "2014-10-11", "2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16", "2014-10-17", "2014-10-18", "2014-10-19",
				"2014-10-20", "2014-10-21", "2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26", "2014-10-27", "2014-10-28", "2014-10-29",
				"2014-10-30", "2014-10-31", "2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05", "2014-11-06", "2014-11-07", "2014-11-08",
				"2014-11-09", "2014-11-10", "2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15", "2014-11-16", "2014-11-17", "2014-11-18",
				"2014-11-19", "2014-11-20", "2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25", "2014-11-26", "2014-11-27", "2014-11-28",
				"2014-11-29", "2014-11-30", "2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05", "2014-12-06", "2014-12-07", "2014-12-08",
				"2014-12-09", "2014-12-10", "2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15", "2014-12-16", "2014-12-17", "2014-12-18",
				"2014-12-19", "2014-12-20", "2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25", "2014-12-26", "2014-12-27", "2014-12-28",
				"2014-12-29", "2014-12-30", "2014-12-31", "2015-01-01", "2015-01-02", "2015-01-03", "2015-01-04", "2015-01-05", "2015-01-06", "2015-01-07",
				"2015-01-08", "2015-01-09", "2015-01-10", "2015-01-11", "2015-01-12", "2015-01-13", "2015-01-14", "2015-01-15", "2015-01-16", "2015-01-17",
				"2015-01-18", "2015-01-19", "2015-01-20", "2015-01-21", "2015-01-22", "2015-01-23", "2015-01-24", "2015-01-25", "2015-01-26", "2015-01-27",
				"2015-01-28", "2015-01-29", "2015-01-30", "2015-01-31", "2015-02-01", "2015-02-02", "2015-02-03", "2015-02-04", "2015-02-06", "2015-02-07",
				"2015-02-08", "2015-02-09", "2015-02-10", "2015-02-11", "2015-02-12", "2015-02-13", "2015-02-14", "2015-02-15", "2015-02-16", "2015-02-17",
				"2015-02-18", "2015-02-19", "2015-02-20", "2015-02-21", "2015-02-22", "2015-02-23", "2015-02-24", "2015-02-25", "2015-02-26", "2015-02-27",
				"2015-02-28", "2015-03-01", "2015-03-02", "2015-03-03", "2015-03-04", "2015-03-05", "2015-03-06", "2015-03-07", "2015-03-08", "2015-03-09",
				"2015-03-10", "2015-03-11", "2015-03-12", "2015-03-13", "2015-03-14", "2015-03-15", "2015-03-16", "2015-03-17", "2015-03-18", "2015-03-19",
				"2015-03-20", "2015-03-21", "2015-03-22", "2015-03-23", "2015-03-24", "2015-03-25", "2015-03-26", "2015-03-27", "2015-03-28", "2015-03-29",
				"2015-03-30", "2015-03-31", "2015-04-01", "2015-04-02", "2015-04-03", "2015-04-04", "2015-04-05", "2015-04-06", "2015-04-07", "2015-04-08",
				"2015-04-09", "2015-04-10", "2015-04-11", "2015-04-12", "2015-04-13", "2015-04-14", "2015-04-15", "2015-04-16", "2015-04-17", "2015-04-18",
				"2015-04-19", "2015-04-20", "2015-04-21", "2015-04-22", "2015-04-23", "2015-04-24", "2015-04-25", "2015-04-26", "2015-04-27", "2015-04-28",
				"2015-04-29", "2015-04-30", "2015-05-01", "2015-05-02", "2015-05-03", "2015-05-04", "2015-05-05", "2015-05-06", "2015-05-07", "2015-05-08",
				"2015-05-09", "2015-05-10", "2015-05-11", "2015-05-12", "2015-05-13", "2015-05-14", "2015-05-15", "2015-05-16", "2015-05-17", "2015-05-18",
				"2015-05-19", "2015-05-20", "2015-05-21", "2015-05-22", "2015-05-23", "2015-05-24", "2015-05-25", "2015-05-26", "2015-05-27", "2015-05-28",
				"2015-05-29", "2015-05-30", "2015-05-31", "2015-06-01", "2015-06-02", "2015-06-03", "2015-06-04", "2015-06-05", "2015-06-06", "2015-06-07",
				"2015-06-08", "2015-06-09", "2015-06-10", "2015-06-11", "2015-06-12", "2015-06-13", "2015-06-14", "2015-06-15", "2015-06-16", "2015-06-17",
				"2015-06-18", "2015-06-19", "2015-06-20", "2015-06-21", "2015-06-22", "2015-06-23", "2015-06-24", "2015-06-25", "2015-06-26", "2015-06-27",
				"2015-06-28", "2015-06-29", "2015-06-30", "2015-07-01", "2015-07-02", "2015-07-03", "2015-07-04", "2015-07-05", "2015-07-06", "2015-07-07",
				"2015-07-08", "2015-07-09", "2015-07-10", "2015-07-11", "2015-07-12", "2015-07-13", "2015-07-14", "2015-07-15", "2015-07-16", "2015-07-17",
				"2015-07-18", "2015-07-19", "2015-07-20", "2015-07-21", "2015-07-22", "2015-07-23", "2015-07-24", "2015-07-25", "2015-07-26", "2015-07-27",
				"2015-07-28", "2015-07-29", "2015-07-30", "2015-07-31", "2015-08-01", "2015-08-02", "2015-08-03", "2015-08-04", "2015-08-05", "2015-08-06",
				"2015-08-07", "2015-08-08", "2015-08-09", "2015-08-10", "2015-08-11", "2015-08-12", "2015-08-13", "2015-08-14", "2015-08-15", "2015-08-16",
				"2015-08-17", "2015-08-18", "2015-08-19", "2015-08-20", "2015-08-21", "2015-08-22", "2015-08-23", "2015-08-24", "2015-08-25", "2015-08-26",
				"2015-08-27", "2015-08-28", "2015-08-29", "2015-08-30", "2015-08-31", "2015-09-01", "2015-09-02", "2015-09-03", "2015-09-04", "2015-09-05",
				"2015-09-06", "2015-09-07", "2015-09-08", "2015-09-09", "2015-09-10", "2015-09-11", "2015-09-12", "2015-09-13", "2015-09-14", "2015-09-15",
				"2015-09-16", "2015-09-17", "2015-09-18", "2015-09-19", "2015-09-20", "2015-09-21", "2015-09-22", "2015-09-23", "2015-09-24", "2015-09-25",
				"2015-09-26", "2015-09-27", "2015-09-28", "2015-09-29", "2015-09-30", "2015-10-01", "2015-10-02", "2015-10-03", "2015-10-04", "2015-10-05",
				"2015-10-06", "2015-10-07", "2015-10-08", "2015-10-09", "2015-10-10", "2015-10-11", "2015-10-13", "2015-10-14", "2015-10-15", "2015-10-16",
				"2015-10-17", "2015-10-18", "2015-10-19", "2015-10-20", "2015-10-21", "2015-10-22", "2015-10-23", "2015-10-24", "2015-10-25", "2015-10-26",
				"2015-10-27", "2015-10-28", "2015-10-29", "2015-10-30", "2015-10-31", "2015-11-01", "2015-11-02", "2015-11-03", "2015-11-04", "2015-11-05",
				"2015-11-06", "2015-11-07", "2015-11-08", "2015-11-09", "2015-11-10", "2015-11-11", "2015-11-12", "2015-11-13", "2015-11-14", "2015-11-15",
				"2015-11-16", "2015-11-17", "2015-11-18", "2015-11-19", "2015-11-20", "2015-11-21", "2015-11-22", "2015-11-23", "2015-11-24", "2015-11-25",
				"2015-11-26", "2015-11-27", "2015-11-28", "2015-11-29", "2015-11-30", "2015-12-01", "2015-12-02", "2015-12-03", "2015-12-04", "2015-12-05",
				"2015-12-06", "2015-12-07", "2015-12-08", "2015-12-09", "2015-12-10", "2015-12-11", "2015-12-12", "2015-12-13", "2015-12-14", "2015-12-15",
				"2015-12-16", "2015-12-17", "2015-12-18", "2015-12-19", "2015-12-20", "2015-12-21", "2015-12-22", "2015-12-23", "2015-12-24", "2015-12-25",
				"2015-12-26", "2015-12-27", "2015-12-28", "2015-12-29", "2015-12-30", "2015-12-31", "2016-01-01", "2016-01-02", "2016-01-03", "2016-01-04",
				"2016-01-05", "2016-01-06", "2016-01-07", "2016-01-08", "2016-01-09", "2016-01-10", "2016-01-11", "2016-01-12", "2016-01-13", "2016-01-14",
				"2016-01-15", "2016-01-16", "2016-01-17", "2016-01-18", "2016-01-19", "2016-01-20", };
	}

	private void testCase1() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_PROPHET;
		System.out.println("testCase1() requestUrl=" + requestUrl);
		String requestParameters = IConstants.EMPTY_STRING;
		ProphetResponse prophetResponse = politeCrawlWebServiceClientService.prophet(requestUrl, requestParameters);
		assertNotNull("testCase1() prophetResponse should not be null.", prophetResponse);
		String json = new Gson().toJson(prophetResponse, ProphetResponse.class);
		assertNotNull("testCase1() prophetResponse.getError() should not be null.", prophetResponse.getError());
		assertEquals("testCase1() json is incorrect", "{\"success\":false,\"error\":{\"error_code\":\"00001\",\"error_message\":\"Request JSON is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase1() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase2() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_PROPHET;
		System.out.println("testCase2() requestUrl=" + requestUrl);
		ProphetRequest prophetRequest = new ProphetRequest();
		String requestParameters = new Gson().toJson(prophetRequest, ProphetRequest.class);
		ProphetResponse prophetResponse = politeCrawlWebServiceClientService.prophet(requestUrl, requestParameters);
		assertNotNull("testCase2() prophetResponse should not be null.", prophetResponse);
		String json = new Gson().toJson(prophetResponse, ProphetResponse.class);
		assertNotNull("testCase2() prophetResponse.getError() should not be null.", prophetResponse.getError());
		assertEquals("testCase2() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00004\",\"error_message\":\"Request parameter access_token is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase2() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase3() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_PROPHET;
		System.out.println("testCase3() requestUrl=" + requestUrl);
		ProphetRequest prophetRequest = new ProphetRequest();
		prophetRequest.setAccess_token("testaccesstoken");
		String requestParameters = new Gson().toJson(prophetRequest, ProphetRequest.class);
		ProphetResponse prophetResponse = politeCrawlWebServiceClientService.prophet(requestUrl, requestParameters);
		assertNotNull("testCase3() prophetResponse should not be null.", prophetResponse);
		String json = new Gson().toJson(prophetResponse, ProphetResponse.class);
		assertNotNull("testCase3() prophetResponse.getError() should not be null.", prophetResponse.getError());
		assertEquals("testCase3() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00002\",\"error_message\":\"Request parameter access_token testaccesstoken is invalid.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase3() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase4() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_PROPHET;
		System.out.println("testCase4() requestUrl=" + requestUrl);
		ProphetRequest prophetRequest = new ProphetRequest();
		prophetRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		String requestParameters = new Gson().toJson(prophetRequest, ProphetRequest.class);
		ProphetResponse prophetResponse = politeCrawlWebServiceClientService.prophet(requestUrl, requestParameters);
		assertNotNull("testCase4() prophetResponse should not be null.", prophetResponse);
		String json = new Gson().toJson(prophetResponse, ProphetResponse.class);
		assertNotNull("testCase4() prophetResponse.getError() should not be null.", prophetResponse.getError());
		assertEquals("testCase4() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00005\",\"error_message\":\"Request parameter forecast_days is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase4() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase5() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_PROPHET;
		System.out.println("testCase5() requestUrl=" + requestUrl);
		ProphetRequest prophetRequest = new ProphetRequest();
		prophetRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		prophetRequest.setForecast_days(365);
		String requestParameters = new Gson().toJson(prophetRequest, ProphetRequest.class);
		ProphetResponse prophetResponse = politeCrawlWebServiceClientService.prophet(requestUrl, requestParameters);
		assertNotNull("testCase5() prophetResponse should not be null.", prophetResponse);
		String json = new Gson().toJson(prophetResponse, ProphetResponse.class);
		assertNotNull("testCase5() prophetResponse.getError() should not be null.", prophetResponse.getError());
		assertEquals("testCase5() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00006\",\"error_message\":\"Request parameter date_array is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase5() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase6() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_PROPHET;
		System.out.println("testCase6() requestUrl=" + requestUrl);
		ProphetRequest prophetRequest = new ProphetRequest();
		prophetRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		prophetRequest.setForecast_days(365);
		prophetRequest.setDate_array(new String[] { "2021-08-18", "asdfsadf" });
		String requestParameters = new Gson().toJson(prophetRequest, ProphetRequest.class);
		ProphetResponse prophetResponse = politeCrawlWebServiceClientService.prophet(requestUrl, requestParameters);
		assertNotNull("testCase6() prophetResponse should not be null.", prophetResponse);
		String json = new Gson().toJson(prophetResponse, ProphetResponse.class);
		assertNotNull("testCase6() prophetResponse.getError() should not be null.", prophetResponse.getError());
		assertEquals("testCase6() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00008\",\"error_message\":\"Request parameter date_array contains invalid data.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase6() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase7() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_PROPHET;
		System.out.println("testCase7() requestUrl=" + requestUrl);
		ProphetRequest prophetRequest = new ProphetRequest();
		prophetRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		prophetRequest.setForecast_days(365);
		prophetRequest.setDate_array(new String[] { "2021-08-18", "2021-08-17", });
		String requestParameters = new Gson().toJson(prophetRequest, ProphetRequest.class);
		ProphetResponse prophetResponse = politeCrawlWebServiceClientService.prophet(requestUrl, requestParameters);
		assertNotNull("testCase7() prophetResponse should not be null.", prophetResponse);
		String json = new Gson().toJson(prophetResponse, ProphetResponse.class);
		assertNotNull("testCase7() prophetResponse.getError() should not be null.", prophetResponse.getError());
		assertEquals("testCase7() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00008\",\"error_message\":\"Request parameter date_array contains invalid data.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase7() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase8() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_PROPHET;
		System.out.println("testCase8() requestUrl=" + requestUrl);
		ProphetRequest prophetRequest = new ProphetRequest();
		prophetRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		prophetRequest.setForecast_days(365);
		prophetRequest.setDate_array(new String[] { "2021-08-17", "2021-08-19", "2021-08-18", });
		String requestParameters = new Gson().toJson(prophetRequest, ProphetRequest.class);
		ProphetResponse prophetResponse = politeCrawlWebServiceClientService.prophet(requestUrl, requestParameters);
		assertNotNull("testCase8() prophetResponse should not be null.", prophetResponse);
		String json = new Gson().toJson(prophetResponse, ProphetResponse.class);
		assertNotNull("testCase8() prophetResponse.getError() should not be null.", prophetResponse.getError());
		assertEquals("testCase8() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00008\",\"error_message\":\"Request parameter date_array contains invalid data.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase8() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase9() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_PROPHET;
		System.out.println("testCase9() requestUrl=" + requestUrl);
		ProphetRequest prophetRequest = new ProphetRequest();
		prophetRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		prophetRequest.setForecast_days(365);
		prophetRequest.setDate_array(new String[] { "2021-08-18", "2021-08-17", "2021-08-19", });
		String requestParameters = new Gson().toJson(prophetRequest, ProphetRequest.class);
		ProphetResponse prophetResponse = politeCrawlWebServiceClientService.prophet(requestUrl, requestParameters);
		assertNotNull("testCase9() prophetResponse should not be null.", prophetResponse);
		String json = new Gson().toJson(prophetResponse, ProphetResponse.class);
		assertNotNull("testCase9() prophetResponse.getError() should not be null.", prophetResponse.getError());
		assertEquals("testCase9() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00008\",\"error_message\":\"Request parameter date_array contains invalid data.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase9() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase10() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_PROPHET;
		System.out.println("testCase10() requestUrl=" + requestUrl);
		ProphetRequest prophetRequest = new ProphetRequest();
		prophetRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		prophetRequest.setForecast_days(365);
		prophetRequest.setDate_array(new String[] { "2021-08-17", "2021-08-18", "2021-08-19", });
		String requestParameters = new Gson().toJson(prophetRequest, ProphetRequest.class);
		ProphetResponse prophetResponse = politeCrawlWebServiceClientService.prophet(requestUrl, requestParameters);
		assertNotNull("testCase10() prophetResponse should not be null.", prophetResponse);
		String json = new Gson().toJson(prophetResponse, ProphetResponse.class);
		assertNotNull("testCase10() prophetResponse.getError() should not be null.", prophetResponse.getError());
		assertEquals("testCase10() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00007\",\"error_message\":\"Request parameter value_array is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase10() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase11() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_PROPHET;
		System.out.println("testCase11() requestUrl=" + requestUrl);
		ProphetRequest prophetRequest = new ProphetRequest();
		prophetRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		prophetRequest.setForecast_days(365);
		prophetRequest.setDate_array(new String[] { "2021-08-17", "2021-08-18", "2021-08-19", });
		prophetRequest.setValue_array(new Double[] { 1D, 2D, });
		String requestParameters = new Gson().toJson(prophetRequest, ProphetRequest.class);
		ProphetResponse prophetResponse = politeCrawlWebServiceClientService.prophet(requestUrl, requestParameters);
		assertNotNull("testCase11() prophetResponse should not be null.", prophetResponse);
		String json = new Gson().toJson(prophetResponse, ProphetResponse.class);
		assertNotNull("testCase11() prophetResponse.getError() should not be null.", prophetResponse.getError());
		assertEquals("testCase11() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00009\",\"error_message\":\"The length of date_array and value_array must be the same.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase11() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase12() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_PROPHET;
		System.out.println("testCase12() requestUrl=" + requestUrl);
		ProphetRequest prophetRequest = new ProphetRequest();
		prophetRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		prophetRequest.setForecast_days(365);
		prophetRequest.setDate_array(getStringArrayForProphet());
		prophetRequest.setValue_array(getDoubleArrayForProphet());
		String requestParameters = new Gson().toJson(prophetRequest, ProphetRequest.class);
		ProphetResponse prophetResponse = politeCrawlWebServiceClientService.prophet(requestUrl, requestParameters);
		assertNotNull("testCase12() prophetResponse should not be null.", prophetResponse);
		assertNotNull("testCase12() prophetResponse.getDate_array() should not be null.", prophetResponse.getDate_array());
		assertEquals("testCase12() prophetResponse.getDate_array().length incorrect.", "3270", String.valueOf(prophetResponse.getDate_array().length));
		assertEquals("testCase12() prophetResponse.getTrend_array().length incorrect.", "3270", String.valueOf(prophetResponse.getTrend_array().length));
		assertEquals("testCase12() prophetResponse.getTrend_lower_array().length incorrect.", "3270", String.valueOf(prophetResponse.getTrend_lower_array().length));
		assertEquals("testCase12() prophetResponse.getTrend_upper_array().length incorrect.", "3270", String.valueOf(prophetResponse.getTrend_upper_array().length));
		assertEquals("testCase12() prophetResponse.getForecast_array().length incorrect.", "3270", String.valueOf(prophetResponse.getForecast_array().length));
		assertEquals("testCase12() prophetResponse.getForecast_lower_array().length incorrect.", "3270",
				String.valueOf(prophetResponse.getForecast_lower_array().length));
		assertEquals("testCase12() prophetResponse.getForecast_upper_array().length incorrect.", "3270",
				String.valueOf(prophetResponse.getForecast_upper_array().length));
		assertEquals("testCase12() prophetResponse.getWeekly_seasonality_map().size() incorrect.", "7",
				String.valueOf(prophetResponse.getWeekly_seasonality_map().size()));
		assertEquals("testCase12() prophetResponse.getYearly_seasonality_array().length incorrect.", "365",
				String.valueOf(prophetResponse.getYearly_seasonality_array().length));
		FormatUtils.getInstance().logMemoryUsage("testCase12() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void test() throws Exception {
		String requestUrl = "https://api.seoclarity.net/seoClarity/prophet";
		ProphetRequest prophetRequest = new ProphetRequest();
		prophetRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		prophetRequest.setForecast_days(365);
		prophetRequest.setDate_array(new String[] { "2007-12-10", "2007-12-11", "2007-12-12", });
		prophetRequest.setValue_array(new Double[] { 9.59076113897809, 8.51959031601596, 8.18367658262066, });
		String requestParameters = new Gson().toJson(prophetRequest, ProphetRequest.class);
		ProphetResponse prophetResponse = politeCrawlWebServiceClientService.prophet(requestUrl, requestParameters);
		if (BooleanUtils.isTrue(prophetResponse.getSuccess())) {
			for (String dateString : prophetResponse.getDate_array()) {
				System.out.println("dateString=" + dateString);
			}
			for (double trend : prophetResponse.getTrend_array()) {
				System.out.println("trend=" + trend);
			}
			for (double trendLower : prophetResponse.getTrend_lower_array()) {
				System.out.println("trendLower=" + trendLower);
			}
			for (Integer dayNumber : prophetResponse.getWeekly_seasonality_map().keySet()) {
				System.out.println("dayNumber=" + dayNumber + ",weekly seasonality=" + prophetResponse.getWeekly_seasonality_map().get(dayNumber));
			}
			for (double yearlySeasonality : prophetResponse.getYearly_seasonality_array()) {
				System.out.println("yearlySeasonality=" + yearlySeasonality);
			}
		}
	}

}