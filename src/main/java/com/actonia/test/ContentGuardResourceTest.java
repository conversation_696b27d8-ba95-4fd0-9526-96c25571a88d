package com.actonia.test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import java.util.List;
import java.util.Map;

import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;

import com.actonia.IConstants;
import com.actonia.dao.ContentGuardSkipUrlDAO;
import com.actonia.entity.ContentGuardSkipUrlEntity;
import com.actonia.service.AccessTokenService;
import com.actonia.service.PoliteCrawlWebServiceClientService;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.ContentGuardChangeDetails;
import com.actonia.value.object.ContentGuardDailyGroupSeverity;
import com.actonia.value.object.ContentGuardDailyGroupTimeline;
import com.actonia.value.object.ContentGuardHourlyGroupSeverity;
import com.actonia.value.object.ContentGuardHourlyGroupTimeline;
import com.actonia.value.object.ContentGuardHourlySeverity;
import com.actonia.value.object.ContentGuardHourlyTimeline;
import com.actonia.value.object.ContentGuardIndicatorUrlChanges;
import com.actonia.value.object.ContentGuardResourceRequest;
import com.actonia.value.object.ContentGuardResourceResponse;
import com.actonia.value.object.ContentGuardSeverity;
import com.actonia.value.object.ContentGuardUrlChanges;
import com.actonia.value.object.ContentGuardUrlCrawlHistory;
import com.actonia.value.object.UrlPageAnalysisResults;
import com.google.gson.Gson;

public class ContentGuardResourceTest {

	private boolean isUnitTest = true;
	private PoliteCrawlWebServiceClientService politeCrawlWebServiceClientService;
	private ContentGuardSkipUrlDAO contentGuardSkipUrlDAO;
	private static final int DOMAIN_ID_9632 = 9632;
	private static final Long DOMAIN_ID_9632_GROUP_ID_2 = 2L;
	private static final Long DOMAIN_ID_9632_GROUP_ID_3 = 3L;
	private static final String DOMAIN_ID_9632_CRAWL_DATE = "2021-01-15";
	private static final Integer DOMAIN_ID_9632_CRAWL_HOUR = 0;
	private static final String DOMAIN_ID_9632_CRAWL_DATE_WITHOUT_CRAWL_DATA = "1688-10-24";

	private static final String DOMAIN_ID_9632_URL_1 = "https://emplois.ca.indeed.com/Montr%C3%A9al,-QC-Emplois-Psychologue";
	private static final String DOMAIN_ID_9632_URL_1_CRAWL_TIMESTAMP = "2021-01-15 00:37:09";
	private static final String DOMAIN_ID_9632_URL_1_CRAWL_TIMESTAMP_WITHOUT_CRAWL_DATA = "1688-10-24 19:43:27";

	private static final String DOMAIN_ID_9632_URL_2 = "https://emplois.ca.indeed.com/Montr%C3%A9al,-QC-Emplois-Serveur";
	private static final String DOMAIN_ID_9632_URL_2_CRAWL_TIMESTAMP = "2021-01-15 00:37:07";

	private static final String DOMAIN_ID_9632_URL_3 = "https://emplois.ca.indeed.com/Montr%C3%A9al,-QC-Emplois-Station-Service";
	private static final String DOMAIN_ID_9632_URL_3_CRAWL_TIMESTAMP = "2021-01-15 00:37:01";

	private static final String DOMAIN_ID_9632_URL_4 = "https://emplois.ca.indeed.com/Montr%C3%A9al,-QC-Emplois-Poste-Canada-Jean-Coutu";
	private static final String DOMAIN_ID_9632_URL_4_CRAWL_TIMESTAMP = "2021-01-15 00:37:02";

	private static final String DOMAIN_ID_9632_URL_6 = "https://emplois.ca.indeed.com/Montr%C3%A9al,-QC-Emplois-Game-Access";

	private static final Long DOMAIN_ID_9632_INVALID_GROUP_ID = 138L;
	private static final Long DOMAIN_ID_9632_GROUP_ID_WITHOUT_URLS = 4L;
	private static final Long DOMAIN_ID_9632_GROUP_ID_CRAWL_FREQUENCY_DAILY = 2L;
	private static final Long DOMAIN_ID_9632_GROUP_ID_CRAWL_FREQUENCY_HOURLY = 3L;

	public ContentGuardResourceTest() {
		super();
		this.politeCrawlWebServiceClientService = SpringBeanFactory.getBean("politeCrawlWebServiceClientService");
		this.contentGuardSkipUrlDAO = SpringBeanFactory.getBean("contentGuardSkipUrlDAO");
	}

	public static void main(String[] args) throws Exception {
		new ContentGuardResourceTest().runTests();
	}

	private void runTests() throws Exception {
		initializeContentGuardSkipUrlMySqlTable(DOMAIN_ID_9632, DOMAIN_ID_9632_GROUP_ID_2);
		testCase1();
		testCase2();
		if (isUnitTest == true) {
			testCase3();
		}
		testCase4();
		testCase5();
		testCase6();
		testCase7();
		testCase8();
		testCase9();
		testCase10();
		testCase11();
		testCase12();
		testCase13();
		testCase14();
		testCase15();
		testCase16();
		testCase17();
		testCase21();
		testCase22();
		testCase23();
		testCase24();
		testCase25();
		testCase26();
		testCase27();
		testCase32();
		testCase33();
		initializeContentGuardSkipUrlMySqlTable(DOMAIN_ID_9632, DOMAIN_ID_9632_GROUP_ID_2);
		testCase34();
		testCase35();
		testCase36();
		testCase50();
		if (isUnitTest == true) {
			testCase51();
		}
		testCase52();
		testCase53();
		testCase56();
		testCase57();
		testCase58();
		testCase59();
		testCase60();
		testCase63();
		testCase64();
		testCase65();
		testCase66();
		testCase67();
		testCase68();
		testCase69();
		testCase70();
		testCase71();
		testCase72();
		testCase73();
		testCase74();
		testCase75();
		testCase76();
		testCase77();
		testCase78();
		testCase79();
		testCase80();
		testCase81();
		testCase82();
		testCase83();
		testCase84();
		testCase85();
		testCase86();
		testCase87();
		testCase88();
		testCase89();
		testCase90();
		testCase91();
		testCase92();
		testCase93();
		testCase94();
		testCase95();
		testCase96();
		testCase97();
		testCase98();
		testCase99();
		testCase100();
		testCase101();
		testCase102();
		testCase103();
		testCase104();
		testCase105();
		testCase106();
		testCase107();
		testCase108();
		testCase109();
		testCase110();
		testCase111();
		testCase112();
		testCase113();
		testCase114();
		testCase115();
		testCase116();

		// 'usage' test cases begin
		testCase117();
		testCase118();
		testCase119();
		testCase120();
		testCase121();
		testCase122();
		testCase123();
		testCase124();
		testCase125();
		// 'usage' test cases end

		//testCase36(); // for extreme response JSON size test

		// for finding test data: testCase61, testCase62
		//testCase61();
		//testCase62();
	}

	private void initializeContentGuardSkipUrlMySqlTable(int domainId, Long groupId) throws Exception {
		contentGuardSkipUrlDAO.delete(domainId, groupId);
	}

	private void testCase1() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase1() requestUrl=" + requestUrl);
		String requestParameters = IConstants.EMPTY_STRING;
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase1() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase1() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase1() json is incorrect", "{\"success\":false,\"error\":{\"error_code\":\"00001\",\"error_message\":\"Request JSON is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase1() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase2() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase2() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase2() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase2() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase2() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase2() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00013\",\"error_message\":\"Request parameter access_token is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase2() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase3() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase3() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token("testaccesstoken");
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase3() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase3() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase3() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase3() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00002\",\"error_message\":\"Request parameter access_token testaccesstoken is invalid.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase3() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase4() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD + "test";
		System.out.println("testCase4() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase4() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase4() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase4() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase4() json is incorrect", "{\"success\":false,\"error\":{\"error_code\":\"00004\",\"error_message\":\"Request command test is invalid.\"}}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase4() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase5() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase5() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase5() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase5() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase5() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase5() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00005\",\"error_message\":\"Request parameter domain_id is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase5() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase6() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase6() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase6() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase6() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase6() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase6() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00021\",\"error_message\":\"Request parameter group_id is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase6() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase7() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase7() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setCrawl_date("test_crawl_date");

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase7() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase7() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase7() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase7() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00011\",\"error_message\":\"Request parameter crawl_date test_crawl_date is invalid.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase7() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase8() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase8() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setCrawl_date(DOMAIN_ID_9632_CRAWL_DATE);
		contentGuardResourceRequest.setCrawl_hour(168);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase8() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase8() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase8() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase8() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00012\",\"error_message\":\"Request parameter crawl_hour 168 is invalid.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase8() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase9() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase9() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setCrawl_hour(DOMAIN_ID_9632_CRAWL_HOUR);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase9() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase9() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase9() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase9() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00009\",\"error_message\":\"Request parameter crawl_date is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase9() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase10() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase10() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(10);
		contentGuardResourceRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);
		contentGuardResourceRequest.setReturn_details(true);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase10() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase10() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase10() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		assertNull("testCase10() contentGuardResourceResponse.getStart_crawl_timestamp() should be null.", contentGuardResourceResponse.getStart_crawl_timestamp());
		assertNull("testCase10() contentGuardResourceResponse.getEnd_crawl_timestamp() should be null.", contentGuardResourceResponse.getEnd_crawl_timestamp());
		assertEquals("testCase10() contentGuardResourceResponse.getUrl_changes_list().size() incorrect.", 10,
				contentGuardResourceResponse.getUrl_changes_list().size());
		//String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		FormatUtils.getInstance().logMemoryUsage("testCase10() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));

		Map<String, Integer> changeTrackingIndicatorTotalsMap = null;
		List<ContentGuardUrlChanges> urlChangesList = null;
		List<ContentGuardChangeDetails> changeDetailsList = null;
		int totalChanges = 0;
		if (BooleanUtils.isTrue(contentGuardResourceResponse.getSuccess())) {
			changeTrackingIndicatorTotalsMap = contentGuardResourceResponse.getChange_tracking_indicator_totals_map();
			if (changeTrackingIndicatorTotalsMap != null && changeTrackingIndicatorTotalsMap.size() > 0) {
				for (String changeTrackingIndicator : changeTrackingIndicatorTotalsMap.keySet()) {
					totalChanges = changeTrackingIndicatorTotalsMap.get(changeTrackingIndicator);
					//System.out.println("changeTrackingIndicator=" + changeTrackingIndicator + ",totalChanges=" + totalChanges);
				}
			}
			urlChangesList = contentGuardResourceResponse.getUrl_changes_list();
			if (urlChangesList != null && urlChangesList.size() > 0) {
				for (ContentGuardUrlChanges contentGuardUrlChanges : urlChangesList) {
					//System.out.println("url totalChanges=" + contentGuardUrlChanges.getTotal_changes());
					//System.out.println("url added=" + contentGuardUrlChanges.getAdded());
					//System.out.println("url modified=" + contentGuardUrlChanges.getModified());
					//System.out.println("url removed=" + contentGuardUrlChanges.getRemoved());
					changeDetailsList = contentGuardUrlChanges.getChange_details_list();
					if (changeDetailsList != null && changeDetailsList.size() > 0) {
						for (ContentGuardChangeDetails contentGuardChangeDetails : changeDetailsList) {
							System.out.println("url changeIndicator=" + contentGuardChangeDetails.getChange_indicator());
						}
					}
				}
			}
		}
	}

	private void testCase11() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase11() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setCrawl_date(DOMAIN_ID_9632_CRAWL_DATE);
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(10);
		contentGuardResourceRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);
		contentGuardResourceRequest.setReturn_details(true);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase11() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase11() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase11() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		assertEquals("testCase11() contentGuardResourceResponse.getStart_crawl_timestamp() incorrect.", "2021-01-15 00:00:00",
				contentGuardResourceResponse.getStart_crawl_timestamp());
		assertEquals("testCase11() contentGuardResourceResponse.getEnd_crawl_timestamp() incorrect.", "2021-01-15 23:59:59",
				contentGuardResourceResponse.getEnd_crawl_timestamp());
		//String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		FormatUtils.getInstance().logMemoryUsage("testCase11() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase12() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase12() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setCrawl_date(DOMAIN_ID_9632_CRAWL_DATE);
		contentGuardResourceRequest.setCrawl_hour(DOMAIN_ID_9632_CRAWL_HOUR);
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(10);
		contentGuardResourceRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);
		contentGuardResourceRequest.setReturn_details(true);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase12() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase12() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase12() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		assertEquals("testCase12() contentGuardResourceResponse.getStart_crawl_timestamp() incorrect.", "2021-01-15 00:00:00",
				contentGuardResourceResponse.getStart_crawl_timestamp());
		assertEquals("testCase12() contentGuardResourceResponse.getEnd_crawl_timestamp() incorrect.", "2021-01-15 00:59:59",
				contentGuardResourceResponse.getEnd_crawl_timestamp());

		for (ContentGuardUrlChanges contentGuardUrlChanges : contentGuardResourceResponse.getUrl_changes_list()) {
			// validate 'https://emplois.ca.indeed.com/Montr%C3%A9al,-QC-Emplois-Poste-Canada-Jean-Coutu'
			if (StringUtils.equalsIgnoreCase(contentGuardUrlChanges.getUrl(), DOMAIN_ID_9632_URL_4)) {
				assertEquals("testCase12() " + DOMAIN_ID_9632_URL_4 + " hash_cd incorrect.", "3b014606ff3cc56b09d78c618503b588", contentGuardUrlChanges.getHash_cd());
				assertEquals("testCase12() " + DOMAIN_ID_9632_URL_4 + " added incorrect.", "1", String.valueOf(contentGuardUrlChanges.getAdded()));
				assertEquals("testCase12() " + DOMAIN_ID_9632_URL_4 + " modified incorrect.", "3", String.valueOf(contentGuardUrlChanges.getModified()));
				assertEquals("testCase12() " + DOMAIN_ID_9632_URL_4 + " total changes incorrect.", "4",
						String.valueOf(contentGuardUrlChanges.getChange_details_list().size()));
				assertEquals("testCase12() " + DOMAIN_ID_9632_URL_4 + " first change incorrect.", "description_chg_ind",
						String.valueOf(contentGuardUrlChanges.getChange_details_list().get(0).getChange_indicator()));
				assertEquals("testCase12() " + DOMAIN_ID_9632_URL_4 + " second change incorrect.", "h2_added_ind",
						String.valueOf(contentGuardUrlChanges.getChange_details_list().get(1).getChange_indicator()));
				assertEquals("testCase12() " + DOMAIN_ID_9632_URL_4 + " third change incorrect.", "h2_chg_ind",
						String.valueOf(contentGuardUrlChanges.getChange_details_list().get(2).getChange_indicator()));
				assertEquals("testCase12() " + DOMAIN_ID_9632_URL_4 + " fourth change incorrect.", "page_link_chg_ind",
						String.valueOf(contentGuardUrlChanges.getChange_details_list().get(3).getChange_indicator()));
			}
		}

		//String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		FormatUtils.getInstance().logMemoryUsage("testCase12() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase13() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_URL_DETAILS;
		System.out.println("testCase13() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase13() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase13() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase13() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase13() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00007\",\"error_message\":\"Request parameter url is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase13() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase14() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_URL_DETAILS;
		System.out.println("testCase14() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setUrl(DOMAIN_ID_9632_URL_1);
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase14() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase14() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase14() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase14() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00008\",\"error_message\":\"Request parameter crawl_timestamp is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase14() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase15() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_URL_DETAILS;
		System.out.println("testCase15() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setUrl(DOMAIN_ID_9632_URL_1);
		contentGuardResourceRequest.setCrawl_timestamp("test_crawl_timestamp");
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase15() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase15() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase15() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase15() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00010\",\"error_message\":\"Request parameter crawl_timestamp test_crawl_timestamp is invalid.\"}}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase15() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase16() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		int totalChangesAdded = 0;
		int totalChangesModified = 0;
		int totalChangesRemoved = 0;
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_URL_DETAILS;
		System.out.println("testCase16() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setUrl(DOMAIN_ID_9632_URL_4);
		contentGuardResourceRequest.setCrawl_timestamp(DOMAIN_ID_9632_URL_4_CRAWL_TIMESTAMP);
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase16() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase16() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase16() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		assertEquals("testCase16() contentGuardResourceResponse.getCurrent_crawl_timestamp() incorrect.", DOMAIN_ID_9632_URL_4_CRAWL_TIMESTAMP,
				contentGuardResourceResponse.getCurrent_crawl_timestamp());
		for (ContentGuardChangeDetails contentGuardChangeDetails : contentGuardResourceResponse.getUrl_change_details_list()) {
			if (StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_type(), IConstants.CHANGE_TYPE_ADDED)) {
				totalChangesAdded++;
			} else if (StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_type(), IConstants.CHANGE_TYPE_MODIFIED)) {
				totalChangesModified++;
			} else if (StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_type(), IConstants.CHANGE_TYPE_REMOVED)) {
				totalChangesRemoved++;
			}
		}
		assertEquals("testCase16() totalChangesAdded is incorrect", 1, totalChangesAdded);
		assertEquals("testCase16() totalChangesModified is incorrect", 3, totalChangesModified);
		assertEquals("testCase16() totalChangesRemoved is incorrect", 0, totalChangesRemoved);

		assertEquals("testCase16() first indicator is incorrect", "description_chg_ind",
				contentGuardResourceResponse.getUrl_change_details_list().get(0).getChange_indicator());
		assertEquals("testCase16() second indicator is incorrect", "h2_added_ind",
				contentGuardResourceResponse.getUrl_change_details_list().get(1).getChange_indicator());
		assertEquals("testCase16() third indicator is incorrect", "h2_chg_ind", contentGuardResourceResponse.getUrl_change_details_list().get(2).getChange_indicator());
		assertEquals("testCase16() fourth indicator is incorrect", "page_link_chg_ind",
				contentGuardResourceResponse.getUrl_change_details_list().get(3).getChange_indicator());

		if (BooleanUtils.isTrue(contentGuardResourceResponse.getSuccess())) {
			System.out.println("previousCrawlTimestamp=" + contentGuardResourceResponse.getPrevious_crawl_timestamp());
			if (contentGuardResourceResponse.getUrl_change_details_list() != null && contentGuardResourceResponse.getUrl_change_details_list().size() > 0) {
				for (ContentGuardChangeDetails contentGuardChangeDetails : contentGuardResourceResponse.getUrl_change_details_list()) {
					//System.out.println("changeType=" + contentGuardChangeDetails.getChange_type());
					//System.out.println("indicator=" + contentGuardChangeDetails.getChange_indicator());
					//System.out.println("field=" + contentGuardChangeDetails.getChange_field());
					//System.out.println("previousContent=" + contentGuardChangeDetails.getPrevious_content());
					//System.out.println("currentContent=" + contentGuardChangeDetails.getCurrent_content());
				}
			}
		}

		FormatUtils.getInstance().logMemoryUsage("testCase16() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase17() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		int totalChangesAdded = 0;
		int totalChangesModified = 0;
		int totalChangesRemoved = 0;
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_URL_DETAILS;
		System.out.println("testCase17() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setUrl(DOMAIN_ID_9632_URL_2);
		contentGuardResourceRequest.setCrawl_timestamp(DOMAIN_ID_9632_URL_2_CRAWL_TIMESTAMP);
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase17() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase17() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase17() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		assertEquals("testCase17() contentGuardResourceResponse.getCurrent_crawl_timestamp() incorrect.", DOMAIN_ID_9632_URL_2_CRAWL_TIMESTAMP,
				contentGuardResourceResponse.getCurrent_crawl_timestamp());
		for (ContentGuardChangeDetails contentGuardChangeDetails : contentGuardResourceResponse.getUrl_change_details_list()) {
			if (StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_type(), IConstants.CHANGE_TYPE_ADDED)) {
				totalChangesAdded++;
			} else if (StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_type(), IConstants.CHANGE_TYPE_MODIFIED)) {
				totalChangesModified++;
			} else if (StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_type(), IConstants.CHANGE_TYPE_REMOVED)) {
				totalChangesRemoved++;
			}
		}
		assertEquals("testCase17() totalChangesAdded is incorrect", 1, totalChangesAdded);
		assertEquals("testCase17() totalChangesModified is incorrect", 4, totalChangesModified);
		assertEquals("testCase17() totalChangesRemoved is incorrect", 0, totalChangesRemoved);

		assertEquals("testCase17() first indicator is incorrect", "description_chg_ind",
				contentGuardResourceResponse.getUrl_change_details_list().get(0).getChange_indicator());
		assertEquals("testCase17() second indicator is incorrect", "h2_added_ind",
				contentGuardResourceResponse.getUrl_change_details_list().get(1).getChange_indicator());
		assertEquals("testCase17() third indicator is incorrect", "h2_chg_ind", contentGuardResourceResponse.getUrl_change_details_list().get(2).getChange_indicator());
		assertEquals("testCase17() fourth indicator is incorrect", "page_analysis_results_chg_ind_json",
				contentGuardResourceResponse.getUrl_change_details_list().get(3).getChange_indicator());
		assertEquals("testCase17() fifth indicator is incorrect", "page_link_chg_ind",
				contentGuardResourceResponse.getUrl_change_details_list().get(4).getChange_indicator());
		FormatUtils.getInstance().logMemoryUsage("testCase17() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase21() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase21() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setCrawl_date(DOMAIN_ID_9632_CRAWL_DATE_WITHOUT_CRAWL_DATA);
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(10);
		contentGuardResourceRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);
		contentGuardResourceRequest.setReturn_details(true);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase21() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase21() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase21() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		assertEquals("testCase21() contentGuardResourceResponse.getStart_crawl_timestamp() incorrect.", "1688-10-24 00:00:00",
				contentGuardResourceResponse.getStart_crawl_timestamp());
		assertEquals("testCase21() contentGuardResourceResponse.getEnd_crawl_timestamp() incorrect.", "1688-10-24 23:59:59",
				contentGuardResourceResponse.getEnd_crawl_timestamp());
		assertEquals("testCase21() contentGuardResourceResponse.getTotal_pages_changed() incorrect.", "0",
				String.valueOf(contentGuardResourceResponse.getTotal_pages_changed()));
		//String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		FormatUtils.getInstance().logMemoryUsage("testCase21() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase22() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_URL_DETAILS;
		System.out.println("testCase22() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setUrl(DOMAIN_ID_9632_URL_1);
		contentGuardResourceRequest.setCrawl_timestamp(DOMAIN_ID_9632_URL_1_CRAWL_TIMESTAMP_WITHOUT_CRAWL_DATA);
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase22() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase22() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase22() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		boolean isCorrectErrorMessage = StringUtils.containsIgnoreCase(json,
				"{\"success\":false,\"error\":{\"error_code\":\"00003\",\"error_message\":\"Crawl data not available for domain ID 9632, URL https://emplois.ca.indeed.com/Montr%C3%A9al,-QC-Emplois-Psychologue, crawl timestamp 1688-10-24 19:43:27\"}");
		assertEquals("testCase22() json is incorrect", true, isCorrectErrorMessage);
		FormatUtils.getInstance().logMemoryUsage("testCase22() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase23() throws Exception {
		long startTimestamp = System.currentTimeMillis();

		initializeContentGuardSkipUrlMySqlTable(DOMAIN_ID_9632, DOMAIN_ID_9632_GROUP_ID_2);

		// filtering test 1
		ContentGuardSkipUrlEntity contentGuardSkipUrlEntity = new ContentGuardSkipUrlEntity();
		contentGuardSkipUrlEntity.setDomainId(DOMAIN_ID_9632);
		contentGuardSkipUrlEntity.setGroupId(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardSkipUrlEntity.setIndicator(IConstants.PAGE_LINK_CHG_IND);
		contentGuardSkipUrlEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_REGEXP);
		contentGuardSkipUrlEntity.setUrlSelector(".*l,-QC-Emplois-Psychologue");
		Long filteringTest1Id = contentGuardSkipUrlDAO.create(contentGuardSkipUrlEntity);
		System.out.println("testCase23() filteringTest1Id=" + filteringTest1Id);

		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase23() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setCrawl_date(DOMAIN_ID_9632_CRAWL_DATE);
		contentGuardResourceRequest.setCrawl_hour(DOMAIN_ID_9632_CRAWL_HOUR);
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(10);
		contentGuardResourceRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);
		contentGuardResourceRequest.setReturn_details(true);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase23() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase23() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase23() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		assertEquals("testCase23() contentGuardResourceResponse.getStart_crawl_timestamp() incorrect.", "2021-01-15 00:00:00",
				contentGuardResourceResponse.getStart_crawl_timestamp());
		assertEquals("testCase23() contentGuardResourceResponse.getEnd_crawl_timestamp() incorrect.", "2021-01-15 00:59:59",
				contentGuardResourceResponse.getEnd_crawl_timestamp());

		for (ContentGuardUrlChanges contentGuardUrlChanges : contentGuardResourceResponse.getUrl_changes_list()) {
			// validate 'https://test.edgeseo.dev/ai_rule_46.html'
			if (StringUtils.equalsIgnoreCase(contentGuardUrlChanges.getUrl(), DOMAIN_ID_9632_URL_1)) {
				assertEquals("testCase23() " + DOMAIN_ID_9632_URL_1 + " added incorrect.", "0", String.valueOf(contentGuardUrlChanges.getAdded()));
				assertEquals("testCase23() " + DOMAIN_ID_9632_URL_1 + " modified incorrect.", "2", String.valueOf(contentGuardUrlChanges.getModified()));
				assertEquals("testCase23() " + DOMAIN_ID_9632_URL_1 + " total changes incorrect.", "2",
						String.valueOf(contentGuardUrlChanges.getChange_details_list().size()));
				assertEquals("testCase23() " + DOMAIN_ID_9632_URL_1 + " 1st change incorrect.", "description_chg_ind",
						String.valueOf(contentGuardUrlChanges.getChange_details_list().get(0).getChange_indicator()));
				assertEquals("testCase23() " + DOMAIN_ID_9632_URL_1 + " 2nd change incorrect.", "h2_chg_ind",
						String.valueOf(contentGuardUrlChanges.getChange_details_list().get(1).getChange_indicator()));
			}
		}

		//String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		FormatUtils.getInstance().logMemoryUsage("testCase23() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase24() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		int totalChangesAdded = 0;
		int totalChangesModified = 0;
		int totalChangesRemoved = 0;
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_URL_DETAILS;
		System.out.println("testCase24() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setUrl(DOMAIN_ID_9632_URL_1);
		contentGuardResourceRequest.setCrawl_timestamp(DOMAIN_ID_9632_URL_1_CRAWL_TIMESTAMP);
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase24() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase24() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase24() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		assertEquals("testCase24() contentGuardResourceResponse.getCurrent_crawl_timestamp() incorrect.", DOMAIN_ID_9632_URL_1_CRAWL_TIMESTAMP,
				contentGuardResourceResponse.getCurrent_crawl_timestamp());
		for (ContentGuardChangeDetails contentGuardChangeDetails : contentGuardResourceResponse.getUrl_change_details_list()) {
			if (StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_type(), IConstants.CHANGE_TYPE_ADDED)) {
				totalChangesAdded++;
			} else if (StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_type(), IConstants.CHANGE_TYPE_MODIFIED)) {
				totalChangesModified++;
			} else if (StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_type(), IConstants.CHANGE_TYPE_REMOVED)) {
				totalChangesRemoved++;
			}
		}
		assertEquals("testCase24() totalChangesAdded is incorrect", 0, totalChangesAdded);
		assertEquals("testCase24() totalChangesModified is incorrect", 2, totalChangesModified);
		assertEquals("testCase24() totalChangesRemoved is incorrect", 0, totalChangesRemoved);

		assertEquals("testCase24() 1st indicator is incorrect", "description_chg_ind",
				contentGuardResourceResponse.getUrl_change_details_list().get(0).getChange_indicator());
		assertEquals("testCase24() 2nd indicator is incorrect", "h2_chg_ind", contentGuardResourceResponse.getUrl_change_details_list().get(1).getChange_indicator());

		FormatUtils.getInstance().logMemoryUsage("testCase24() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase25() throws Exception {
		long startTimestamp = System.currentTimeMillis();

		ContentGuardSkipUrlEntity contentGuardSkipUrlEntity = null;

		// filtering test 2a
		contentGuardSkipUrlEntity = new ContentGuardSkipUrlEntity();
		contentGuardSkipUrlEntity.setDomainId(DOMAIN_ID_9632);
		contentGuardSkipUrlEntity.setGroupId(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardSkipUrlEntity.setIndicator(IConstants.H2_CHG_IND);
		contentGuardSkipUrlEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_EQUALS);
		contentGuardSkipUrlEntity.setUrlSelector(DOMAIN_ID_9632_URL_6);
		Long filteringTest2aId = contentGuardSkipUrlDAO.create(contentGuardSkipUrlEntity);
		System.out.println("testCase25() filteringTest2aId=" + filteringTest2aId);

		// filtering test 2b
		contentGuardSkipUrlEntity = new ContentGuardSkipUrlEntity();
		contentGuardSkipUrlEntity.setDomainId(DOMAIN_ID_9632);
		contentGuardSkipUrlEntity.setGroupId(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardSkipUrlEntity.setIndicator(IConstants.PAGE_LINK_CHG_IND);
		contentGuardSkipUrlEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_EQUALS);
		contentGuardSkipUrlEntity.setUrlSelector(DOMAIN_ID_9632_URL_6);
		Long filteringTest2bId = contentGuardSkipUrlDAO.create(contentGuardSkipUrlEntity);
		System.out.println("testCase25() filteringTest2bId=" + filteringTest2bId);

		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase25() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setCrawl_date(DOMAIN_ID_9632_CRAWL_DATE);
		contentGuardResourceRequest.setCrawl_hour(DOMAIN_ID_9632_CRAWL_HOUR);
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(10);
		contentGuardResourceRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);
		contentGuardResourceRequest.setReturn_details(true);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase25() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase25() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase25() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		assertEquals("testCase25() contentGuardResourceResponse.getStart_crawl_timestamp() incorrect.", "2021-01-15 00:00:00",
				contentGuardResourceResponse.getStart_crawl_timestamp());
		assertEquals("testCase25() contentGuardResourceResponse.getEnd_crawl_timestamp() incorrect.", "2021-01-15 00:59:59",
				contentGuardResourceResponse.getEnd_crawl_timestamp());

		for (ContentGuardUrlChanges contentGuardUrlChanges : contentGuardResourceResponse.getUrl_changes_list()) {
			if (StringUtils.equalsIgnoreCase(contentGuardUrlChanges.getUrl(), DOMAIN_ID_9632_URL_1)) {
				assertEquals("testCase23() " + DOMAIN_ID_9632_URL_1 + " added incorrect.", "0", String.valueOf(contentGuardUrlChanges.getAdded()));
				assertEquals("testCase23() " + DOMAIN_ID_9632_URL_1 + " modified incorrect.", "2", String.valueOf(contentGuardUrlChanges.getModified()));
				assertEquals("testCase23() " + DOMAIN_ID_9632_URL_1 + " total changes incorrect.", "2",
						String.valueOf(contentGuardUrlChanges.getChange_details_list().size()));
				assertEquals("testCase23() " + DOMAIN_ID_9632_URL_1 + " 1st change incorrect.", "description_chg_ind",
						String.valueOf(contentGuardUrlChanges.getChange_details_list().get(0).getChange_indicator()));
				assertEquals("testCase23() " + DOMAIN_ID_9632_URL_1 + " 2nd change incorrect.", "h2_chg_ind",
						String.valueOf(contentGuardUrlChanges.getChange_details_list().get(1).getChange_indicator()));
			} else if (StringUtils.equalsIgnoreCase(contentGuardUrlChanges.getUrl(), DOMAIN_ID_9632_URL_2)) {
				assertEquals("testCase25() " + DOMAIN_ID_9632_URL_2 + " added incorrect.", "1", String.valueOf(contentGuardUrlChanges.getAdded()));
				assertEquals("testCase25() " + DOMAIN_ID_9632_URL_2 + " removed incorrect.", "0", String.valueOf(contentGuardUrlChanges.getRemoved()));
				assertEquals("testCase25() " + DOMAIN_ID_9632_URL_2 + " modified incorrect.", "4", String.valueOf(contentGuardUrlChanges.getModified()));
				assertEquals("testCase25() " + DOMAIN_ID_9632_URL_2 + " total changes incorrect.", "5",
						String.valueOf(contentGuardUrlChanges.getChange_details_list().size()));
				assertEquals("testCase25() " + DOMAIN_ID_9632_URL_2 + " 1st change incorrect.", "description_chg_ind",
						String.valueOf(contentGuardUrlChanges.getChange_details_list().get(0).getChange_indicator()));
				assertEquals("testCase25() " + DOMAIN_ID_9632_URL_2 + " 2nd change incorrect.", "h2_added_ind",
						String.valueOf(contentGuardUrlChanges.getChange_details_list().get(1).getChange_indicator()));
				assertEquals("testCase25() " + DOMAIN_ID_9632_URL_2 + " 3rd change incorrect.", "h2_chg_ind",
						String.valueOf(contentGuardUrlChanges.getChange_details_list().get(2).getChange_indicator()));
				assertEquals("testCase25() " + DOMAIN_ID_9632_URL_2 + " 4th change incorrect.", "page_analysis_results_chg_ind_json",
						String.valueOf(contentGuardUrlChanges.getChange_details_list().get(3).getChange_indicator()));
				assertEquals("testCase25() " + DOMAIN_ID_9632_URL_2 + " 5th change incorrect.", "page_link_chg_ind",
						String.valueOf(contentGuardUrlChanges.getChange_details_list().get(4).getChange_indicator()));
			} else if (StringUtils.equalsIgnoreCase(contentGuardUrlChanges.getUrl(), DOMAIN_ID_9632_URL_6)) {
				fail("testCase25() https://emplois.ca.indeed.com/Montr%C3%A9al,-QC-Emplois-Game-Access should have been filtered out.");
			}
		}

		//String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		FormatUtils.getInstance().logMemoryUsage("testCase25() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase26() throws Exception {
		long startTimestamp = System.currentTimeMillis();

		ContentGuardSkipUrlEntity contentGuardSkipUrlEntity = null;

		// filtering test 3a
		contentGuardSkipUrlEntity = new ContentGuardSkipUrlEntity();
		contentGuardSkipUrlEntity.setDomainId(DOMAIN_ID_9632);
		contentGuardSkipUrlEntity.setGroupId(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardSkipUrlEntity.setIndicator(IConstants.DESCRIPTION_CHG_IND);
		contentGuardSkipUrlEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_CONTAINS);
		contentGuardSkipUrlEntity.setUrlSelector("l,-QC-Emplois-Serveur");
		Long filteringTest3aId = contentGuardSkipUrlDAO.create(contentGuardSkipUrlEntity);
		System.out.println("testCase26() filteringTest3aId=" + filteringTest3aId);

		// filtering test 3b
		contentGuardSkipUrlEntity = new ContentGuardSkipUrlEntity();
		contentGuardSkipUrlEntity.setDomainId(DOMAIN_ID_9632);
		contentGuardSkipUrlEntity.setGroupId(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardSkipUrlEntity.setIndicator(IConstants.PAGE_LINK_CHG_IND);
		contentGuardSkipUrlEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_CONTAINS);
		contentGuardSkipUrlEntity.setUrlSelector("l,-QC-Emplois-Serveur");
		Long filteringTest3bId = contentGuardSkipUrlDAO.create(contentGuardSkipUrlEntity);
		System.out.println("testCase26() filteringTest3bId=" + filteringTest3bId);

		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase26() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setCrawl_date(DOMAIN_ID_9632_CRAWL_DATE);
		contentGuardResourceRequest.setCrawl_hour(DOMAIN_ID_9632_CRAWL_HOUR);
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(10);
		contentGuardResourceRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);
		contentGuardResourceRequest.setReturn_details(true);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase26() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase26() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase26() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		assertEquals("testCase26() contentGuardResourceResponse.getStart_crawl_timestamp() incorrect.", "2021-01-15 00:00:00",
				contentGuardResourceResponse.getStart_crawl_timestamp());
		assertEquals("testCase26() contentGuardResourceResponse.getEnd_crawl_timestamp() incorrect.", "2021-01-15 00:59:59",
				contentGuardResourceResponse.getEnd_crawl_timestamp());

		for (ContentGuardUrlChanges contentGuardUrlChanges : contentGuardResourceResponse.getUrl_changes_list()) {
			if (StringUtils.equalsIgnoreCase(contentGuardUrlChanges.getUrl(), DOMAIN_ID_9632_URL_1)) {
				assertEquals("testCase23() " + DOMAIN_ID_9632_URL_1 + " added incorrect.", "0", String.valueOf(contentGuardUrlChanges.getAdded()));
				assertEquals("testCase23() " + DOMAIN_ID_9632_URL_1 + " modified incorrect.", "2", String.valueOf(contentGuardUrlChanges.getModified()));
				assertEquals("testCase23() " + DOMAIN_ID_9632_URL_1 + " total changes incorrect.", "2",
						String.valueOf(contentGuardUrlChanges.getChange_details_list().size()));
				assertEquals("testCase23() " + DOMAIN_ID_9632_URL_1 + " 1st change incorrect.", "description_chg_ind",
						String.valueOf(contentGuardUrlChanges.getChange_details_list().get(0).getChange_indicator()));
				assertEquals("testCase23() " + DOMAIN_ID_9632_URL_1 + " 2nd change incorrect.", "h2_chg_ind",
						String.valueOf(contentGuardUrlChanges.getChange_details_list().get(1).getChange_indicator()));
			} else if (StringUtils.equalsIgnoreCase(contentGuardUrlChanges.getUrl(), DOMAIN_ID_9632_URL_2)) {
				assertEquals("testCase26() " + DOMAIN_ID_9632_URL_2 + " added incorrect.", "1", String.valueOf(contentGuardUrlChanges.getAdded()));
				assertEquals("testCase26() " + DOMAIN_ID_9632_URL_2 + " modified incorrect.", "2", String.valueOf(contentGuardUrlChanges.getModified()));
				assertEquals("testCase26() " + DOMAIN_ID_9632_URL_2 + " total changes incorrect.", "3",
						String.valueOf(contentGuardUrlChanges.getChange_details_list().size()));
				assertEquals("testCase26() " + DOMAIN_ID_9632_URL_2 + " 1st change incorrect.", "h2_added_ind",
						String.valueOf(contentGuardUrlChanges.getChange_details_list().get(0).getChange_indicator()));
				assertEquals("testCase26() " + DOMAIN_ID_9632_URL_2 + " 2nd change incorrect.", "h2_chg_ind",
						String.valueOf(contentGuardUrlChanges.getChange_details_list().get(1).getChange_indicator()));
				assertEquals("testCase26() " + DOMAIN_ID_9632_URL_2 + " 3rd change incorrect.", "page_analysis_results_chg_ind_json",
						String.valueOf(contentGuardUrlChanges.getChange_details_list().get(2).getChange_indicator()));
			} else if (StringUtils.equalsIgnoreCase(contentGuardUrlChanges.getUrl(), DOMAIN_ID_9632_URL_6)) {
				fail("testCase25() https://emplois.ca.indeed.com/Montr%C3%A9al,-QC-Emplois-Game-Access should have been filtered out.");
			}
		}

		//String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		FormatUtils.getInstance().logMemoryUsage("testCase26() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase27() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		int totalChangesAdded = 0;
		int totalChangesModified = 0;
		int totalChangesRemoved = 0;
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_URL_DETAILS;
		System.out.println("testCase27() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setUrl(DOMAIN_ID_9632_URL_2);
		contentGuardResourceRequest.setCrawl_timestamp(DOMAIN_ID_9632_URL_2_CRAWL_TIMESTAMP);
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase27() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase27() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase27() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		assertEquals("testCase27() contentGuardResourceResponse.getCurrent_crawl_timestamp() incorrect.", DOMAIN_ID_9632_URL_2_CRAWL_TIMESTAMP,
				contentGuardResourceResponse.getCurrent_crawl_timestamp());
		for (ContentGuardChangeDetails contentGuardChangeDetails : contentGuardResourceResponse.getUrl_change_details_list()) {
			if (StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_type(), IConstants.CHANGE_TYPE_ADDED)) {
				totalChangesAdded++;
			} else if (StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_type(), IConstants.CHANGE_TYPE_MODIFIED)) {
				totalChangesModified++;
			} else if (StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_type(), IConstants.CHANGE_TYPE_REMOVED)) {
				totalChangesRemoved++;
			}
		}
		assertEquals("testCase27() totalChangesAdded is incorrect", 1, totalChangesAdded);
		assertEquals("testCase27() totalChangesModified is incorrect", 2, totalChangesModified);
		assertEquals("testCase27() totalChangesRemoved is incorrect", 0, totalChangesRemoved);

		assertEquals("testCase27() 1st indicator is incorrect", "h2_added_ind", contentGuardResourceResponse.getUrl_change_details_list().get(0).getChange_indicator());
		assertEquals("testCase27() 2nd indicator is incorrect", "h2_chg_ind", contentGuardResourceResponse.getUrl_change_details_list().get(1).getChange_indicator());
		assertEquals("testCase27() 3rd indicator is incorrect", "page_analysis_results_chg_ind_json",
				contentGuardResourceResponse.getUrl_change_details_list().get(2).getChange_indicator());

		FormatUtils.getInstance().logMemoryUsage("testCase27() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase32() throws Exception {
		long startTimestamp = System.currentTimeMillis();

		ContentGuardSkipUrlEntity contentGuardSkipUrlEntity = null;

		// filtering test 4
		contentGuardSkipUrlEntity = new ContentGuardSkipUrlEntity();
		contentGuardSkipUrlEntity.setDomainId(DOMAIN_ID_9632);
		contentGuardSkipUrlEntity.setGroupId(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardSkipUrlEntity.setIndicator(IConstants.PAGE_LINK_CHG_IND);
		contentGuardSkipUrlEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_ALL);
		Long filteringTest5aId = contentGuardSkipUrlDAO.create(contentGuardSkipUrlEntity);
		System.out.println("testCase32() filteringTest5aId=" + filteringTest5aId);

		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase32() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setCrawl_date(DOMAIN_ID_9632_CRAWL_DATE);
		contentGuardResourceRequest.setCrawl_hour(DOMAIN_ID_9632_CRAWL_HOUR);
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(10);
		contentGuardResourceRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);
		contentGuardResourceRequest.setReturn_details(true);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase32() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase32() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase32() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		assertEquals("testCase32() contentGuardResourceResponse.getStart_crawl_timestamp() incorrect.", "2021-01-15 00:00:00",
				contentGuardResourceResponse.getStart_crawl_timestamp());
		assertEquals("testCase32() contentGuardResourceResponse.getEnd_crawl_timestamp() incorrect.", "2021-01-15 00:59:59",
				contentGuardResourceResponse.getEnd_crawl_timestamp());

		for (String indicator : contentGuardResourceResponse.getChange_tracking_indicator_totals_map().keySet()) {
			if (StringUtils.equalsIgnoreCase(indicator, IConstants.PAGE_LINK_CHG_IND)) {
				fail("testCase32() indicator " + IConstants.PAGE_LINK_CHG_IND + " should have been filtered out.");
			}
		}

		for (ContentGuardUrlChanges contentGuardUrlChanges : contentGuardResourceResponse.getUrl_changes_list()) {
			if (StringUtils.equalsIgnoreCase(contentGuardUrlChanges.getUrl(), DOMAIN_ID_9632_URL_1)) {
				assertEquals("testCase32() " + DOMAIN_ID_9632_URL_1 + " added incorrect.", "0", String.valueOf(contentGuardUrlChanges.getAdded()));
				assertEquals("testCase32() " + DOMAIN_ID_9632_URL_1 + " modified incorrect.", "2", String.valueOf(contentGuardUrlChanges.getModified()));
				assertEquals("testCase32() " + DOMAIN_ID_9632_URL_1 + " total changes incorrect.", "2",
						String.valueOf(contentGuardUrlChanges.getChange_details_list().size()));
				assertEquals("testCase32() " + DOMAIN_ID_9632_URL_1 + " 1st change incorrect.", "description_chg_ind",
						String.valueOf(contentGuardUrlChanges.getChange_details_list().get(0).getChange_indicator()));
				assertEquals("testCase32() " + DOMAIN_ID_9632_URL_1 + " 2nd change incorrect.", "h2_chg_ind",
						String.valueOf(contentGuardUrlChanges.getChange_details_list().get(1).getChange_indicator()));
			} else if (StringUtils.equalsIgnoreCase(contentGuardUrlChanges.getUrl(), DOMAIN_ID_9632_URL_2)) {
				assertEquals("testCase26() " + DOMAIN_ID_9632_URL_2 + " added incorrect.", "1", String.valueOf(contentGuardUrlChanges.getAdded()));
				assertEquals("testCase26() " + DOMAIN_ID_9632_URL_2 + " modified incorrect.", "2", String.valueOf(contentGuardUrlChanges.getModified()));
				assertEquals("testCase26() " + DOMAIN_ID_9632_URL_2 + " total changes incorrect.", "3",
						String.valueOf(contentGuardUrlChanges.getChange_details_list().size()));
				assertEquals("testCase26() " + DOMAIN_ID_9632_URL_2 + " 1st change incorrect.", "h2_added_ind",
						String.valueOf(contentGuardUrlChanges.getChange_details_list().get(0).getChange_indicator()));
				assertEquals("testCase26() " + DOMAIN_ID_9632_URL_2 + " 2nd change incorrect.", "h2_chg_ind",
						String.valueOf(contentGuardUrlChanges.getChange_details_list().get(1).getChange_indicator()));
				assertEquals("testCase26() " + DOMAIN_ID_9632_URL_2 + " 3rd change incorrect.", "page_analysis_results_chg_ind_json",
						String.valueOf(contentGuardUrlChanges.getChange_details_list().get(2).getChange_indicator()));
			} else if (StringUtils.equalsIgnoreCase(contentGuardUrlChanges.getUrl(), DOMAIN_ID_9632_URL_3)) {
				assertEquals("testCase32() " + DOMAIN_ID_9632_URL_3 + " added incorrect.", "0", String.valueOf(contentGuardUrlChanges.getAdded()));
				assertEquals("testCase32() " + DOMAIN_ID_9632_URL_3 + " removed incorrect.", "0", String.valueOf(contentGuardUrlChanges.getRemoved()));
				assertEquals("testCase32() " + DOMAIN_ID_9632_URL_3 + " modified incorrect.", "2", String.valueOf(contentGuardUrlChanges.getModified()));
				assertEquals("testCase32() " + DOMAIN_ID_9632_URL_2 + " total changes incorrect.", "2",
						String.valueOf(contentGuardUrlChanges.getChange_details_list().size()));
				assertEquals("testCase32() " + DOMAIN_ID_9632_URL_3 + " 1st change incorrect.", "description_chg_ind",
						String.valueOf(contentGuardUrlChanges.getChange_details_list().get(0).getChange_indicator()));
				assertEquals("testCase32() " + DOMAIN_ID_9632_URL_3 + " 2nd change incorrect.", "h2_chg_ind",
						String.valueOf(contentGuardUrlChanges.getChange_details_list().get(1).getChange_indicator()));
			} else if (StringUtils.equalsIgnoreCase(contentGuardUrlChanges.getUrl(), DOMAIN_ID_9632_URL_6)) {
				fail("testCase32() https://emplois.ca.indeed.com/Montr%C3%A9al,-QC-Emplois-Game-Access should have been filtered out.");
			}
		}

		//String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		FormatUtils.getInstance().logMemoryUsage("testCase32() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase33() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		int totalChangesAdded = 0;
		int totalChangesModified = 0;
		int totalChangesRemoved = 0;
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_URL_DETAILS;
		System.out.println("testCase33() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setUrl(DOMAIN_ID_9632_URL_3);
		contentGuardResourceRequest.setCrawl_timestamp(DOMAIN_ID_9632_URL_3_CRAWL_TIMESTAMP);
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase33() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase33() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase33() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		assertEquals("testCase33() contentGuardResourceResponse.getCurrent_crawl_timestamp() incorrect.", DOMAIN_ID_9632_URL_3_CRAWL_TIMESTAMP,
				contentGuardResourceResponse.getCurrent_crawl_timestamp());
		for (ContentGuardChangeDetails contentGuardChangeDetails : contentGuardResourceResponse.getUrl_change_details_list()) {
			if (StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_type(), IConstants.CHANGE_TYPE_ADDED)) {
				totalChangesAdded++;
			} else if (StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_type(), IConstants.CHANGE_TYPE_MODIFIED)) {
				totalChangesModified++;
			} else if (StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_type(), IConstants.CHANGE_TYPE_REMOVED)) {
				totalChangesRemoved++;
			}
		}
		assertEquals("testCase33() totalChangesAdded is incorrect", 0, totalChangesAdded);
		assertEquals("testCase33() totalChangesModified is incorrect", 2, totalChangesModified);
		assertEquals("testCase33() totalChangesRemoved is incorrect", 0, totalChangesRemoved);
		assertEquals("testCase33() 1st indicator is incorrect", "description_chg_ind",
				contentGuardResourceResponse.getUrl_change_details_list().get(0).getChange_indicator());
		assertEquals("testCase33() 2nd indicator is incorrect", "h2_chg_ind", contentGuardResourceResponse.getUrl_change_details_list().get(1).getChange_indicator());

		FormatUtils.getInstance().logMemoryUsage("testCase33() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase34() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		Map<Integer, Integer> timeline = null;
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_TIMELINE;
		System.out.println("testCase34() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setStart_crawl_date("2021-01-14");
		contentGuardResourceRequest.setEnd_crawl_date("2021-01-15");

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase34() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase34() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase34() contentGuardResourceResponse.getSuccess() should be true.", false, contentGuardResourceResponse.getSuccess());
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase34() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase34() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00021\",\"error_message\":\"Request parameter group_id is required.\"}}", json);

		FormatUtils.getInstance().logMemoryUsage("testCase34() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase35() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_TIMELINE;
		System.out.println("testCase35() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setStart_crawl_date("2021-01-14");
		contentGuardResourceRequest.setEnd_crawl_date("2021-01-15");

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase35() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase35() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase35() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		assertEquals("testCase35() contentGuardResourceResponse.getStart_crawl_timestamp() incorrect.", "2021-01-14 00:00:00",
				contentGuardResourceResponse.getStart_crawl_timestamp());
		assertEquals("testCase35() contentGuardResourceResponse.getEnd_crawl_timestamp() incorrect.", "2021-01-15 23:59:59",
				contentGuardResourceResponse.getEnd_crawl_timestamp());
		//String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		//System.out.println("testCase35() json=" + json);

		assertNotNull("testCase35() contentGuardResourceResponse.getDaily_group_timeline_list() should not be null.",
				contentGuardResourceResponse.getDaily_group_timeline_list());
		assertEquals("testCase35() contentGuardResourceResponse.getDaily_group_timeline_list().length incorrect.", 1,
				contentGuardResourceResponse.getDaily_group_timeline_list().length);
		assertEquals("testCase35() contentGuardResourceResponse.getDaily_group_timeline_list()[0].getCrawl_date() incorrect.", "2021-01-15",
				contentGuardResourceResponse.getDaily_group_timeline_list()[0].getCrawl_date());
		assertEquals("testCase35() contentGuardResourceResponse.getDaily_group_timeline_list()[0].getTotal_changes() incorrect.", "4968",
				String.valueOf(contentGuardResourceResponse.getDaily_group_timeline_list()[0].getTotal_changes()));

		assertEquals("testCase35() contentGuardResourceResponse.getDaily_group_severity_list().length incorrect.", 1,
				contentGuardResourceResponse.getDaily_group_severity_list().length);
		assertEquals("testCase35() contentGuardResourceResponse.getDaily_group_severity_list()[0].getCrawl_date() incorrect.", "2021-01-15",
				contentGuardResourceResponse.getDaily_group_severity_list()[0].getCrawl_date());
		assertEquals("testCase35() contentGuardResourceResponse.getDaily_group_severity_list()[0].getSeverity_list().length incorrect.", 4,
				contentGuardResourceResponse.getDaily_group_severity_list()[0].getSeverity_list().length);
		assertEquals("testCase35() contentGuardResourceResponse.getDaily_group_severity_list()[0].getSeverity_list()[0].getCritical_flag() incorrect.", "1",
				String.valueOf(contentGuardResourceResponse.getDaily_group_severity_list()[0].getSeverity_list()[0].getCritical_flag()));
		assertEquals("testCase35() contentGuardResourceResponse.getDaily_group_severity_list()[0].getSeverity_list()[0].getTotal_changes() incorrect.", "67",
				String.valueOf(contentGuardResourceResponse.getDaily_group_severity_list()[0].getSeverity_list()[0].getTotal_changes()));
		assertEquals("testCase35() contentGuardResourceResponse.getDaily_group_severity_list()[0].getSeverity_list()[1].getCritical_flag() incorrect.", "2",
				String.valueOf(contentGuardResourceResponse.getDaily_group_severity_list()[0].getSeverity_list()[1].getCritical_flag()));
		assertEquals("testCase35() contentGuardResourceResponse.getDaily_group_severity_list()[0].getSeverity_list()[1].getTotal_changes() incorrect.", "30",
				String.valueOf(contentGuardResourceResponse.getDaily_group_severity_list()[0].getSeverity_list()[1].getTotal_changes()));
		assertEquals("testCase35() contentGuardResourceResponse.getDaily_group_severity_list()[0].getSeverity_list()[2].getCritical_flag() incorrect.", "3",
				String.valueOf(contentGuardResourceResponse.getDaily_group_severity_list()[0].getSeverity_list()[2].getCritical_flag()));
		assertEquals("testCase35() contentGuardResourceResponse.getDaily_group_severity_list()[0].getSeverity_list()[2].getTotal_changes() incorrect.", "1098",
				String.valueOf(contentGuardResourceResponse.getDaily_group_severity_list()[0].getSeverity_list()[2].getTotal_changes()));
		assertEquals("testCase35() contentGuardResourceResponse.getDaily_group_severity_list()[0].getSeverity_list()[3].getCritical_flag() incorrect.", "4",
				String.valueOf(contentGuardResourceResponse.getDaily_group_severity_list()[0].getSeverity_list()[3].getCritical_flag()));
		assertEquals("testCase35() contentGuardResourceResponse.getDaily_group_severity_list()[0].getSeverity_list()[3].getTotal_changes() incorrect.", "3773",
				String.valueOf(contentGuardResourceResponse.getDaily_group_severity_list()[0].getSeverity_list()[3].getTotal_changes()));

		if (BooleanUtils.isTrue(contentGuardResourceResponse.getSuccess())) {
			if (contentGuardResourceResponse.getDaily_group_timeline_list() != null) {
				for (ContentGuardDailyGroupTimeline contentGuardDailyGroupTimeline : contentGuardResourceResponse.getDaily_group_timeline_list()) {
					System.out.println("timeline crawlDate=" + contentGuardDailyGroupTimeline.getCrawl_date());
					System.out.println("timeline totalChanges=" + contentGuardDailyGroupTimeline.getTotal_changes());
				}
			}
			if (contentGuardResourceResponse.getDaily_group_severity_list() != null) {
				for (ContentGuardDailyGroupSeverity contentGuardDailyGroupSeverity : contentGuardResourceResponse.getDaily_group_severity_list()) {
					System.out.println("severity crawlDate=" + contentGuardDailyGroupSeverity.getCrawl_date());
					if (contentGuardDailyGroupSeverity.getSeverity_list() != null) {
						for (ContentGuardSeverity contentGuardSeverity : contentGuardDailyGroupSeverity.getSeverity_list()) {
							System.out.println("severity critcalFlag=" + contentGuardSeverity.getCritical_flag());
							System.out.println("severity totalChanges=" + contentGuardSeverity.getTotal_changes());
						}
					}
				}
			}
		}

		FormatUtils.getInstance().logMemoryUsage("testCase35() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));

	}

	private void testCase36() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_TIMELINE;
		System.out.println("testCase36() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_3);
		contentGuardResourceRequest.setStart_crawl_date("2021-01-14");
		contentGuardResourceRequest.setEnd_crawl_date("2021-01-15");

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase36() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase36() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase36() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		assertEquals("testCase36() contentGuardResourceResponse.getStart_crawl_timestamp() incorrect.", "2021-01-14 00:00:00",
				contentGuardResourceResponse.getStart_crawl_timestamp());
		assertEquals("testCase36() contentGuardResourceResponse.getEnd_crawl_timestamp() incorrect.", "2021-01-15 23:59:59",
				contentGuardResourceResponse.getEnd_crawl_timestamp());
		//String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		//System.out.println("testCase36() json=" + json);

		assertNotNull("testCase36() contentGuardResourceResponse.getHourly_group_timeline_list() should not be null.",
				contentGuardResourceResponse.getHourly_group_timeline_list());
		assertEquals("testCase36() contentGuardResourceResponse.getHourly_group_timeline_list().length incorrect.", 2,
				contentGuardResourceResponse.getHourly_group_timeline_list().length);
		assertEquals("testCase36() contentGuardResourceResponse.getHourly_group_timeline_list()[0].getCrawl_date() incorrect.", "2021-01-14",
				contentGuardResourceResponse.getHourly_group_timeline_list()[0].getCrawl_date());
		assertEquals("testCase36() contentGuardResourceResponse.getHourly_group_timeline_list()[0].getTimeline_list().length incorrect.", "13",
				String.valueOf(contentGuardResourceResponse.getHourly_group_timeline_list()[0].getTimeline_list().length));
		assertEquals("testCase36() contentGuardResourceResponse.getHourly_group_timeline_list()[0].getTimeline_list()[0].getCrawl_hour() incorrect.", "11",
				String.valueOf(contentGuardResourceResponse.getHourly_group_timeline_list()[0].getTimeline_list()[0].getCrawl_hour()));
		assertEquals("testCase36() contentGuardResourceResponse.getHourly_group_timeline_list()[0].getTimeline_list()[0].getTotal_changes() incorrect.", "980",
				String.valueOf(contentGuardResourceResponse.getHourly_group_timeline_list()[0].getTimeline_list()[0].getTotal_changes()));

		assertEquals("testCase36() contentGuardResourceResponse.getHourly_group_severity_list().length incorrect.", 2,
				contentGuardResourceResponse.getHourly_group_severity_list().length);
		assertEquals("testCase36() contentGuardResourceResponse.getHourly_group_severity_list()[0].getCrawl_date() incorrect.", "2021-01-14",
				contentGuardResourceResponse.getHourly_group_severity_list()[0].getCrawl_date());
		assertEquals("testCase36() contentGuardResourceResponse.getHourly_group_severity_list()[0].getSeverity_list().length incorrect.", "13",
				String.valueOf(contentGuardResourceResponse.getHourly_group_severity_list()[0].getSeverity_list().length));
		assertEquals("testCase36() contentGuardResourceResponse.getHourly_group_severity_list()[0].getSeverity_list()[0].getCrawl_hour() incorrect.", "11",
				String.valueOf(contentGuardResourceResponse.getHourly_group_severity_list()[0].getSeverity_list()[0].getCrawl_hour()));
		assertEquals("testCase36() contentGuardResourceResponse.getHourly_group_severity_list()[0].getSeverity_list()[0].getSeverity_list().length incorrect.", "4",
				String.valueOf(contentGuardResourceResponse.getHourly_group_severity_list()[0].getSeverity_list()[0].getSeverity_list().length));
		assertEquals(
				"testCase36() contentGuardResourceResponse.getHourly_group_severity_list()[0].getSeverity_list()[0].getSeverity_list()[0].getCritical_flag() incorrect.",
				"1", String.valueOf(contentGuardResourceResponse.getHourly_group_severity_list()[0].getSeverity_list()[0].getSeverity_list()[0].getCritical_flag()));
		assertEquals(
				"testCase36() contentGuardResourceResponse.getHourly_group_severity_list()[0].getSeverity_list()[0].getSeverity_list()[0].getTotal_changes() incorrect.",
				"3", String.valueOf(contentGuardResourceResponse.getHourly_group_severity_list()[0].getSeverity_list()[0].getSeverity_list()[0].getTotal_changes()));
		assertEquals(
				"testCase36() contentGuardResourceResponse.getHourly_group_severity_list()[0].getSeverity_list()[0].getSeverity_list()[1].getCritical_flag() incorrect.",
				"2", String.valueOf(contentGuardResourceResponse.getHourly_group_severity_list()[0].getSeverity_list()[0].getSeverity_list()[1].getCritical_flag()));
		assertEquals(
				"testCase36() contentGuardResourceResponse.getHourly_group_severity_list()[0].getSeverity_list()[0].getSeverity_list()[1].getTotal_changes() incorrect.",
				"1", String.valueOf(contentGuardResourceResponse.getHourly_group_severity_list()[0].getSeverity_list()[0].getSeverity_list()[1].getTotal_changes()));
		assertEquals(
				"testCase36() contentGuardResourceResponse.getHourly_group_severity_list()[0].getSeverity_list()[0].getSeverity_list()[2].getCritical_flag() incorrect.",
				"3", String.valueOf(contentGuardResourceResponse.getHourly_group_severity_list()[0].getSeverity_list()[0].getSeverity_list()[2].getCritical_flag()));
		assertEquals(
				"testCase36() contentGuardResourceResponse.getHourly_group_severity_list()[0].getSeverity_list()[0].getSeverity_list()[2].getTotal_changes() incorrect.",
				"143", String.valueOf(contentGuardResourceResponse.getHourly_group_severity_list()[0].getSeverity_list()[0].getSeverity_list()[2].getTotal_changes()));
		assertEquals(
				"testCase36() contentGuardResourceResponse.getHourly_group_severity_list()[0].getSeverity_list()[0].getSeverity_list()[3].getCritical_flag() incorrect.",
				"4", String.valueOf(contentGuardResourceResponse.getHourly_group_severity_list()[0].getSeverity_list()[0].getSeverity_list()[3].getCritical_flag()));
		assertEquals(
				"testCase36() contentGuardResourceResponse.getHourly_group_severity_list()[0].getSeverity_list()[0].getSeverity_list()[3].getTotal_changes() incorrect.",
				"833", String.valueOf(contentGuardResourceResponse.getHourly_group_severity_list()[0].getSeverity_list()[0].getSeverity_list()[3].getTotal_changes()));

		if (BooleanUtils.isTrue(contentGuardResourceResponse.getSuccess())) {
			if (contentGuardResourceResponse.getHourly_group_timeline_list() != null) {
				for (ContentGuardHourlyGroupTimeline contentGuardHourlyGroupTimeline : contentGuardResourceResponse.getHourly_group_timeline_list()) {
					System.out.println("timeline crawlDate=" + contentGuardHourlyGroupTimeline.getCrawl_date());
					if (contentGuardHourlyGroupTimeline.getTimeline_list() != null) {
						for (ContentGuardHourlyTimeline contentGuardHourlyTimeline : contentGuardHourlyGroupTimeline.getTimeline_list()) {
							System.out.println("timeline crawlHour=" + contentGuardHourlyTimeline.getCrawl_hour());
							System.out.println("timeline totalChanges=" + contentGuardHourlyTimeline.getTotal_changes());
						}
					}
				}
			}
			if (contentGuardResourceResponse.getHourly_group_severity_list() != null) {
				for (ContentGuardHourlyGroupSeverity contentGuardHourlyGroupSeverity : contentGuardResourceResponse.getHourly_group_severity_list()) {
					System.out.println("severity crawlDate=" + contentGuardHourlyGroupSeverity.getCrawl_date());
					if (contentGuardHourlyGroupSeverity.getSeverity_list() != null) {
						for (ContentGuardHourlySeverity contentGuardHourlySeverity : contentGuardHourlyGroupSeverity.getSeverity_list()) {
							System.out.println("severity crawlHour=" + contentGuardHourlySeverity.getCrawl_hour());
							if (contentGuardHourlySeverity.getSeverity_list() != null) {
								for (ContentGuardSeverity contentGuardSeverity : contentGuardHourlySeverity.getSeverity_list()) {
									System.out.println("severity criticalFlag=" + contentGuardSeverity.getCritical_flag());
									System.out.println("severity totalChanges=" + contentGuardSeverity.getTotal_changes());
								}
							}
						}
					}
				}
			}
		}

		FormatUtils.getInstance().logMemoryUsage("testCase36() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase50() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase50() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(10);
		contentGuardResourceRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);
		contentGuardResourceRequest.setReturn_details(true);

		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_INVALID_GROUP_ID);
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase50() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase50() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase50() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase50() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00019\",\"error_message\":\"Request parameter group_id 138 is invalid.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase50() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase51() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase51() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(10);
		contentGuardResourceRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);
		contentGuardResourceRequest.setReturn_details(true);

		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_WITHOUT_URLS);
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase51() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase51() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase51() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase51() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00020\",\"error_message\":\"Request parameter group_id 4 does not have any URLs.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase51() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase52() throws Exception {
		long startTimestamp = System.currentTimeMillis();

		initializeContentGuardSkipUrlMySqlTable(DOMAIN_ID_9632, DOMAIN_ID_9632_GROUP_ID_2);

		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase52() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setCrawl_date(DOMAIN_ID_9632_CRAWL_DATE);
		contentGuardResourceRequest.setCrawl_hour(DOMAIN_ID_9632_CRAWL_HOUR);
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(Integer.MAX_VALUE);
		contentGuardResourceRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);
		contentGuardResourceRequest.setReturn_details(false);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_CRAWL_FREQUENCY_DAILY);
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase52() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase52() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase52() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		assertEquals("testCase52() contentGuardResourceResponse.getStart_crawl_timestamp() incorrect.", "2021-01-15 00:00:00",
				contentGuardResourceResponse.getStart_crawl_timestamp());
		assertEquals("testCase52() contentGuardResourceResponse.getEnd_crawl_timestamp() incorrect.", "2021-01-15 00:59:59",
				contentGuardResourceResponse.getEnd_crawl_timestamp());
		assertEquals("testCase52() contentGuardResourceResponse.getGroup_id() incorrect.", String.valueOf(DOMAIN_ID_9632_GROUP_ID_CRAWL_FREQUENCY_DAILY),
				String.valueOf(contentGuardResourceResponse.getGroup_id().longValue()));
		assertEquals("testCase52() contentGuardResourceResponse.getUrl_changes_list().size() incorrect.", 1998,
				contentGuardResourceResponse.getUrl_changes_list().size());
		//String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		FormatUtils.getInstance().logMemoryUsage("testCase52() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase53() throws Exception {
		long startTimestamp = System.currentTimeMillis();

		initializeContentGuardSkipUrlMySqlTable(DOMAIN_ID_9632, DOMAIN_ID_9632_GROUP_ID_2);

		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase53() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setCrawl_date(DOMAIN_ID_9632_CRAWL_DATE);
		contentGuardResourceRequest.setCrawl_hour(DOMAIN_ID_9632_CRAWL_HOUR);
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(Integer.MAX_VALUE);
		contentGuardResourceRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);
		contentGuardResourceRequest.setReturn_details(false);

		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_CRAWL_FREQUENCY_HOURLY);
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase53() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase53() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase53() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		assertEquals("testCase53() contentGuardResourceResponse.getStart_crawl_timestamp() incorrect.", "2021-01-15 00:00:00",
				contentGuardResourceResponse.getStart_crawl_timestamp());
		assertEquals("testCase53() contentGuardResourceResponse.getEnd_crawl_timestamp() incorrect.", "2021-01-15 00:59:59",
				contentGuardResourceResponse.getEnd_crawl_timestamp());
		assertEquals("testCase53() contentGuardResourceResponse.getGroup_id() incorrect.", String.valueOf(DOMAIN_ID_9632_GROUP_ID_CRAWL_FREQUENCY_HOURLY),
				String.valueOf(contentGuardResourceResponse.getGroup_id().longValue()));
		assertEquals("testCase53() contentGuardResourceResponse.getUrl_changes_list().size() incorrect.", 541,
				contentGuardResourceResponse.getUrl_changes_list().size());

		//String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		FormatUtils.getInstance().logMemoryUsage("testCase53() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase56() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_TIMELINE;
		System.out.println("testCase56() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setEnd_crawl_date(DOMAIN_ID_9632_CRAWL_DATE);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase56() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase56() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase56() contentGuardResourceResponse.getSuccess() should be false.", false, contentGuardResourceResponse.getSuccess());
		assertNotNull("testCase56() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertEquals("testCase56() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00016\",\"error_message\":\"Request parameter start_crawl_date is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase56() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase57() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_TIMELINE;
		System.out.println("testCase57() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setStart_crawl_date("test");
		contentGuardResourceRequest.setEnd_crawl_date(DOMAIN_ID_9632_CRAWL_DATE);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase57() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase57() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase57() contentGuardResourceResponse.getSuccess() should be false.", false, contentGuardResourceResponse.getSuccess());
		assertNotNull("testCase57() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertEquals("testCase57() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00014\",\"error_message\":\"Request parameter start_crawl_date test is invalid.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase57() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase58() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_TIMELINE;
		System.out.println("testCase58() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setStart_crawl_date(DOMAIN_ID_9632_CRAWL_DATE);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase58() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase58() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase58() contentGuardResourceResponse.getSuccess() should be false.", false, contentGuardResourceResponse.getSuccess());
		assertNotNull("testCase58() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertEquals("testCase58() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00017\",\"error_message\":\"Request parameter end_crawl_date is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase58() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase59() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_TIMELINE;
		System.out.println("testCase59() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setStart_crawl_date(DOMAIN_ID_9632_CRAWL_DATE);
		contentGuardResourceRequest.setEnd_crawl_date("test");

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase59() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase59() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase59() contentGuardResourceResponse.getSuccess() should be false.", false, contentGuardResourceResponse.getSuccess());
		assertNotNull("testCase59() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertEquals("testCase59() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00015\",\"error_message\":\"Request parameter end_crawl_date test is invalid.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase59() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase60() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_TIMELINE;
		System.out.println("testCase60() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setStart_crawl_date("2021-01-14");
		contentGuardResourceRequest.setEnd_crawl_date("2020-10-23");

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase60() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase60() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase60() contentGuardResourceResponse.getSuccess() should be false.", false, contentGuardResourceResponse.getSuccess());
		assertNotNull("testCase60() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertEquals("testCase60() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00018\",\"error_message\":\"Request parameter start_crawl_date 2021-01-14 cannot be later than end_crawl_date.\"}}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase60() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase61() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase61() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632); //debug
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		//contentGuardResourceRequest.setCrawl_date("2020-12-31");
		//contentGuardResourceRequest.setCrawl_hour(17);
		contentGuardResourceRequest.setGroup_id(3L);
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(542);
		contentGuardResourceRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);
		//contentGuardResourceRequest.setReturn_details(true);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase61() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase61() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase61() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		//String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		FormatUtils.getInstance().logMemoryUsage("testCase61() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase62() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_TIMELINE;
		System.out.println("testCase62() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		//contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632); //debug
		contentGuardResourceRequest.setDomain_id(8757); //debug
		contentGuardResourceRequest.setStart_crawl_date("2020-12-01");
		contentGuardResourceRequest.setEnd_crawl_date("2021-01-03");

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase62() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase62() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase62() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		//String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		//System.out.println("response json=" + json);
		FormatUtils.getInstance().logMemoryUsage("testCase62() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase63() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase63() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setReturn_details(true);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase63() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase63() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase63() contentGuardResourceResponse.getSuccess() should be true.", false, contentGuardResourceResponse.getSuccess());
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase63() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase63() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00027\",\"error_message\":\"Request parameter page_number is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase63() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase64() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase64() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setPage_number(999);
		contentGuardResourceRequest.setReturn_details(true);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase64() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase64() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase64() contentGuardResourceResponse.getSuccess() should be true.", false, contentGuardResourceResponse.getSuccess());
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase64() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase64() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00028\",\"error_message\":\"Request parameter rows_per_page is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase64() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase65() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase65() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setPage_number(999);
		contentGuardResourceRequest.setRows_per_page(999);
		contentGuardResourceRequest.setReturn_details(true);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase65() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase65() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase65() contentGuardResourceResponse.getSuccess() should be true.", false, contentGuardResourceResponse.getSuccess());
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase65() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase65() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00029\",\"error_message\":\"Request parameter sort_by is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase65() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase66() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase66() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setPage_number(0);
		contentGuardResourceRequest.setRows_per_page(999);
		contentGuardResourceRequest.setSort_by(999);
		contentGuardResourceRequest.setReturn_details(true);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase66() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase66() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase66() contentGuardResourceResponse.getSuccess() should be true.", false, contentGuardResourceResponse.getSuccess());
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase66() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase66() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00024\",\"error_message\":\"Request parameter page_number 0 is invalid.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase66() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase67() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase67() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setPage_number(999);
		contentGuardResourceRequest.setRows_per_page(0);
		contentGuardResourceRequest.setSort_by(999);
		contentGuardResourceRequest.setReturn_details(true);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase67() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase67() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase67() contentGuardResourceResponse.getSuccess() should be true.", false, contentGuardResourceResponse.getSuccess());
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase67() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase67() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00025\",\"error_message\":\"Request parameter rows_per_page 0 is invalid.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase67() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase68() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase68() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setPage_number(999);
		contentGuardResourceRequest.setRows_per_page(999);
		contentGuardResourceRequest.setSort_by(999);
		contentGuardResourceRequest.setReturn_details(true);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase68() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase68() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase68() contentGuardResourceResponse.getSuccess() should be true.", false, contentGuardResourceResponse.getSuccess());
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase68() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase68() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00026\",\"error_message\":\"Request parameter sort_by 999 is invalid.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase68() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase69() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase69() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setPage_number(999);
		contentGuardResourceRequest.setRows_per_page(999);
		contentGuardResourceRequest.setSort_by(4);
		contentGuardResourceRequest.setReturn_details(true);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase69() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase69() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase69() contentGuardResourceResponse.getSuccess() should be true.", false, contentGuardResourceResponse.getSuccess());
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase69() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		String errorMessage = contentGuardResourceResponse.getError().getError_message();
		FormatUtils.getInstance().logMemoryUsage("testCase69() errorMessage=" + errorMessage);
		boolean isValidErrorMessage = StringUtils.contains(errorMessage, "Request parameter page_number and row_per_page is invalid.");
		FormatUtils.getInstance().logMemoryUsage("testCase69() isValidErrorMessage=" + isValidErrorMessage);
		assertTrue("testCase69() isValidErrorMessage is incorrect", isValidErrorMessage);
		FormatUtils.getInstance().logMemoryUsage("testCase69() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase70() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_URL_DETAILS;
		System.out.println("testCase70() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase70() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase70() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase70() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase70() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00021\",\"error_message\":\"Request parameter group_id is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase70() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase71() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase71() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(10);
		contentGuardResourceRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase71() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase71() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase71() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase71() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00031\",\"error_message\":\"Request parameter return_details is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase71() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase72() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_INDICATOR_URL_LIST;
		System.out.println("testCase72() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(10);
		contentGuardResourceRequest.setReturn_details(true);
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase72() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase72() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase72() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		assertEquals("testCase72() contentGuardResourceResponse.getEnd_of_indicator_urls_list_flag() should be false.", false,
				contentGuardResourceResponse.getEnd_of_indicator_url_list_flag());

		if (BooleanUtils.isTrue(contentGuardResourceResponse.getSuccess())) {
			if (contentGuardResourceResponse.getIndicator_url_list() != null) {
				for (ContentGuardIndicatorUrlChanges contentGuardIndicatorUrlChanges : contentGuardResourceResponse.getIndicator_url_list()) {
					System.out.println("change indicator=" + contentGuardIndicatorUrlChanges.getChange_indicator());
					System.out.println("url=" + contentGuardIndicatorUrlChanges.getUrl());
					System.out.println("hash code=" + contentGuardIndicatorUrlChanges.getHash_cd());
					System.out.println("change type=" + contentGuardIndicatorUrlChanges.getChange_type());
					System.out.println("change field=" + contentGuardIndicatorUrlChanges.getChange_field());
					System.out.println("previous crawl timestamp=" + contentGuardIndicatorUrlChanges.getPrevious_crawl_timestamp());
					System.out.println("current crawl timestamp=" + contentGuardIndicatorUrlChanges.getCurrent_crawl_timestamp());
					if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.ALTERNATE_LINKS)
							|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.CUSTOM_DATA)
							|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.H1)
							|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.H2)
							|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.HREFLANG_ERRORS)
							|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.HREFLANG_LINKS)
							|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.INSECURE_RESOURCES)
							|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.OG_MARKUP)
							|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.PAGE_ANALYSIS_RESULTS_CHG_IND_JSON)
							|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.PAGE_LINK)
							|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.REDIRECT_CHAIN)
							|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.RESPONSE_HEADERS)
							|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.STRUCTURED_DATA)) {
					} else {
						System.out.println("previous content=" + contentGuardIndicatorUrlChanges.getPrevious_content());
						System.out.println("current content=" + contentGuardIndicatorUrlChanges.getCurrent_content());
					}
				}
			}
		}

		FormatUtils.getInstance().logMemoryUsage("testCase72() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase73() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_INDICATOR_URL_LIST;
		System.out.println("testCase73() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setPage_number(1179);
		contentGuardResourceRequest.setRows_per_page(10);
		contentGuardResourceRequest.setReturn_details(true);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase73() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase73() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase73() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		assertEquals("testCase73() contentGuardResourceResponse.getEnd_of_indicator_urls_list_flag() should be true.", true,
				contentGuardResourceResponse.getEnd_of_indicator_url_list_flag());
		FormatUtils.getInstance().logMemoryUsage("testCase73() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase74() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_INDICATOR_URL_LIST;
		System.out.println("testCase74() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setPage_number(999);
		contentGuardResourceRequest.setRows_per_page(999);
		contentGuardResourceRequest.setReturn_details(true);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase74() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase74() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase74() contentGuardResourceResponse.getSuccess() should be true.", false, contentGuardResourceResponse.getSuccess());
		assertNotNull("testCase74() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		String errorMessage = contentGuardResourceResponse.getError().getError_message();
		FormatUtils.getInstance().logMemoryUsage("testCase74() errorMessage=" + errorMessage);
		boolean isValidErrorMessage = StringUtils.contains(errorMessage, "Request parameter page_number and row_per_page is invalid.");
		FormatUtils.getInstance().logMemoryUsage("testCase74() isValidErrorMessage=" + isValidErrorMessage);
		assertTrue("testCase74() isValidErrorMessage is incorrect", isValidErrorMessage);
		FormatUtils.getInstance().logMemoryUsage("testCase74() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase75() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_INDICATOR_URL_LIST;
		System.out.println("testCase75() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_3);
		contentGuardResourceRequest.setCrawl_date("2021-01-15");
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(10);
		contentGuardResourceRequest.setReturn_details(true);
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase75() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase75() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase75() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		assertEquals("testCase75() contentGuardResourceResponse.getEnd_of_indicator_urls_list_flag() should be false.", false,
				contentGuardResourceResponse.getEnd_of_indicator_url_list_flag());

		if (BooleanUtils.isTrue(contentGuardResourceResponse.getSuccess())) {
			if (contentGuardResourceResponse.getIndicator_url_list() != null) {
				for (ContentGuardIndicatorUrlChanges contentGuardIndicatorUrlChanges : contentGuardResourceResponse.getIndicator_url_list()) {
					System.out.println("change indicator=" + contentGuardIndicatorUrlChanges.getChange_indicator());
					System.out.println("url=" + contentGuardIndicatorUrlChanges.getUrl());
					System.out.println("change type=" + contentGuardIndicatorUrlChanges.getChange_type());
					System.out.println("change field=" + contentGuardIndicatorUrlChanges.getChange_field());
					System.out.println("previous crawl timestamp=" + contentGuardIndicatorUrlChanges.getPrevious_crawl_timestamp());
					System.out.println("current crawl timestamp=" + contentGuardIndicatorUrlChanges.getCurrent_crawl_timestamp());
					if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.ALTERNATE_LINKS)
							|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.CUSTOM_DATA)
							|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.H1)
							|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.H2)
							|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.HREFLANG_ERRORS)
							|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.HREFLANG_LINKS)
							|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.INSECURE_RESOURCES)
							|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.OG_MARKUP)
							|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.PAGE_ANALYSIS_RESULTS_CHG_IND_JSON)
							|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.PAGE_LINK)
							|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.REDIRECT_CHAIN)
							|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.RESPONSE_HEADERS)
							|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.STRUCTURED_DATA)) {
					} else {
						System.out.println("previous content=" + contentGuardIndicatorUrlChanges.getPrevious_content());
						System.out.println("current content=" + contentGuardIndicatorUrlChanges.getCurrent_content());
					}
				}
			}
		}

		FormatUtils.getInstance().logMemoryUsage("testCase75() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase76() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_INDICATOR_URL_LIST;
		System.out.println("testCase76() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_3);
		contentGuardResourceRequest.setCrawl_date("2021-01-14");
		contentGuardResourceRequest.setCrawl_hour(18);
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(10);
		contentGuardResourceRequest.setReturn_details(true);
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase76() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase76() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase76() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		assertEquals("testCase76() contentGuardResourceResponse.getEnd_of_indicator_urls_list_flag() should be false.", false,
				contentGuardResourceResponse.getEnd_of_indicator_url_list_flag());

		if (BooleanUtils.isTrue(contentGuardResourceResponse.getSuccess())) {
			if (contentGuardResourceResponse.getIndicator_url_list() != null) {
				for (ContentGuardIndicatorUrlChanges contentGuardIndicatorUrlChanges : contentGuardResourceResponse.getIndicator_url_list()) {
					System.out.println("change indicator=" + contentGuardIndicatorUrlChanges.getChange_indicator());
					System.out.println("url=" + contentGuardIndicatorUrlChanges.getUrl());
					System.out.println("change type=" + contentGuardIndicatorUrlChanges.getChange_type());
					System.out.println("change field=" + contentGuardIndicatorUrlChanges.getChange_field());
					System.out.println("previous crawl timestamp=" + contentGuardIndicatorUrlChanges.getPrevious_crawl_timestamp());
					System.out.println("current crawl timestamp=" + contentGuardIndicatorUrlChanges.getCurrent_crawl_timestamp());
					if (StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.ALTERNATE_LINKS)
							|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.CUSTOM_DATA)
							|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.H1)
							|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.H2)
							|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.HREFLANG_ERRORS)
							|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.HREFLANG_LINKS)
							|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.INSECURE_RESOURCES)
							|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.OG_MARKUP)
							|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.PAGE_ANALYSIS_RESULTS_CHG_IND_JSON)
							|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.PAGE_LINK)
							|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.REDIRECT_CHAIN)
							|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.RESPONSE_HEADERS)
							|| StringUtils.equalsIgnoreCase(contentGuardIndicatorUrlChanges.getChange_field(), IConstants.STRUCTURED_DATA)) {
					} else {
						System.out.println("previous content=" + contentGuardIndicatorUrlChanges.getPrevious_content());
						System.out.println("current content=" + contentGuardIndicatorUrlChanges.getCurrent_content());
					}
				}
			}
		}

		FormatUtils.getInstance().logMemoryUsage("testCase76() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase77() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_INDICATOR_URL_LIST;
		System.out.println("testCase77() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_3);
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(10);
		contentGuardResourceRequest.setReturn_details(true);
		contentGuardResourceRequest.setFilter_change_indicator("test_change_indicator");

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase77() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase77() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase77() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase77() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00023\",\"error_message\":\"Request parameter filter_change_indicator test_change_indicator is invalid.\"}}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase77() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase78() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_INDICATOR_URL_LIST;
		System.out.println("testCase78() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_3);
		contentGuardResourceRequest.setCrawl_date("2021-01-15");
		contentGuardResourceRequest.setPage_number(12);
		contentGuardResourceRequest.setRows_per_page(10);
		contentGuardResourceRequest.setReturn_details(true);
		contentGuardResourceRequest.setFilter_change_indicator("description_chg_ind");
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase78() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase78() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase78() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		FormatUtils.getInstance().logMemoryUsage("testCase78() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase79() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_URL_ALL_DETAILS;
		System.out.println("testCase79() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_3);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase79() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase79() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase79() contentGuardResourceResponse.getSuccess() should be false.", false, contentGuardResourceResponse.getSuccess());
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase79() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase79() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00007\",\"error_message\":\"Request parameter url is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase79() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase80() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_URL_ALL_DETAILS;
		System.out.println("testCase80() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_3);
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(10);
		contentGuardResourceRequest.setUrl("https://www.test.com/page_1.html");

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase80() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase80() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase80() contentGuardResourceResponse.getSuccess() should be false.", false, contentGuardResourceResponse.getSuccess());
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase80() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase80() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00032\",\"error_message\":\"Request parameter crawl timestamp history not available for https://www.test.com/page_1.html.\"},\"group_id\":3,\"url\":\"https://www.test.com/page_1.html\"}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase80() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase81() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_URL_ALL_DETAILS;
		System.out.println("testCase81() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(9678);
		contentGuardResourceRequest.setGroup_id(1L);
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(10);
		contentGuardResourceRequest.setUrl("https://www.choicehomewarranty.com/user-agreement/");
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase81() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase81() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase81() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		if (BooleanUtils.isTrue(contentGuardResourceResponse.getSuccess())) {
			System.out.println("total number of current crawl timestamps=" + contentGuardResourceResponse.getTotal_crawl_timestamps());
			System.out.println("end_of_url_crawl_history_flag=" + contentGuardResourceResponse.getEnd_of_url_crawl_history_flag());
			if (contentGuardResourceResponse.getUrl_crawl_history() != null) {
				for (ContentGuardUrlCrawlHistory contentGuardUrlCrawlHistory : contentGuardResourceResponse.getUrl_crawl_history()) {
					System.out.println("previous_crawl_timestamp=" + contentGuardUrlCrawlHistory.getPrevious_crawl_timestamp());
					System.out.println("current_crawl_timestamp=" + contentGuardUrlCrawlHistory.getCurrent_crawl_timestamp());
					for (ContentGuardChangeDetails contentGuardChangeDetails : contentGuardUrlCrawlHistory.getUrl_change_details_List()) {
						System.out.println("changeType=" + contentGuardChangeDetails.getChange_type());
						System.out.println("indicator=" + contentGuardChangeDetails.getChange_indicator());
						System.out.println("field=" + contentGuardChangeDetails.getChange_field());
						System.out.println("url change severity=" + contentGuardChangeDetails.getSeverity());
						if (StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.ALTERNATE_LINKS)
								|| StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.CUSTOM_DATA)
								|| StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.H1)
								|| StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.H2)
								|| StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.HREFLANG_ERRORS)
								|| StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.HREFLANG_LINKS)
								|| StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.INSECURE_RESOURCES)
								|| StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.OG_MARKUP)
								|| StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.PAGE_ANALYSIS_RESULTS_CHG_IND_JSON)
								|| StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.PAGE_LINK)
								|| StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.REDIRECT_CHAIN)
								|| StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.RESPONSE_HEADERS)
								|| StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.STRUCTURED_DATA)) {
							// To show the previous and current content of the above change fields,
							// see 'How to show the 'current_content' and 'previous_content' in the 'url_details' response'
						} else {
							System.out.println("previous content=" + contentGuardChangeDetails.getPrevious_content());
							System.out.println("current content=" + contentGuardChangeDetails.getCurrent_content());
						}

					}
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase81() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase82() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_INDICATOR_URL_LIST;
		System.out.println("testCase82() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(8422);
		contentGuardResourceRequest.setGroup_id(29L);
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(10);
		contentGuardResourceRequest.setFilter_change_indicator("RESPONSE_CODE_CHG_IND_TO_200");
		contentGuardResourceRequest.setReturn_details(true);
		contentGuardResourceRequest.setCrawl_date("2021-04-30");
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase82() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase82() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase82() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		if (BooleanUtils.isTrue(contentGuardResourceResponse.getSuccess())) {
			System.out.println("total number of current crawl timestamps=" + contentGuardResourceResponse.getTotal_crawl_timestamps());
			System.out.println("end_of_url_crawl_history_flag=" + contentGuardResourceResponse.getEnd_of_url_crawl_history_flag());
			if (contentGuardResourceResponse.getUrl_crawl_history() != null) {
				for (ContentGuardUrlCrawlHistory contentGuardUrlCrawlHistory : contentGuardResourceResponse.getUrl_crawl_history()) {
					System.out.println("previous_crawl_timestamp=" + contentGuardUrlCrawlHistory.getPrevious_crawl_timestamp());
					System.out.println("current_crawl_timestamp=" + contentGuardUrlCrawlHistory.getCurrent_crawl_timestamp());
					for (ContentGuardChangeDetails contentGuardChangeDetails : contentGuardUrlCrawlHistory.getUrl_change_details_List()) {
						System.out.println("changeType=" + contentGuardChangeDetails.getChange_type());
						System.out.println("indicator=" + contentGuardChangeDetails.getChange_indicator());
						System.out.println("field=" + contentGuardChangeDetails.getChange_field());
						System.out.println("url change severity=" + contentGuardChangeDetails.getSeverity());
						if (StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.ALTERNATE_LINKS)
								|| StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.CUSTOM_DATA)
								|| StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.H1)
								|| StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.H2)
								|| StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.HREFLANG_ERRORS)
								|| StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.HREFLANG_LINKS)
								|| StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.INSECURE_RESOURCES)
								|| StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.OG_MARKUP)
								|| StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.PAGE_ANALYSIS_RESULTS_CHG_IND_JSON)
								|| StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.PAGE_LINK)
								|| StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.REDIRECT_CHAIN)
								|| StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.RESPONSE_HEADERS)
								|| StringUtils.equalsIgnoreCase(contentGuardChangeDetails.getChange_field(), IConstants.STRUCTURED_DATA)) {
							// To show the previous and current content of the above change fields,
							// see 'How to show the 'current_content' and 'previous_content' in the 'url_details' response'
						} else {
							System.out.println("previous content=" + contentGuardChangeDetails.getPrevious_content());
							System.out.println("current content=" + contentGuardChangeDetails.getCurrent_content());
						}

					}
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase82() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase83() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_PAGE_ANALYSIS_ISSUES;
		System.out.println("testCase83() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(9632);
		contentGuardResourceRequest.setGroup_id(2L);
		contentGuardResourceRequest.setCrawl_date("2021-03-18");
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase83() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase83() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase83() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		assertEquals("testCase83() contentGuardResourceResponse.getTotal_page_analysis_issues() incorrect.", "0",
				String.valueOf(contentGuardResourceResponse.getTotal_page_analysis_issues()));
		FormatUtils.getInstance().logMemoryUsage("testCase83() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase84() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_PAGE_ANALYSIS_ISSUES;
		System.out.println("testCase84() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(9678);
		contentGuardResourceRequest.setGroup_id(1L);
		contentGuardResourceRequest.setCrawl_date("2021-04-05");
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase84() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase84() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase84() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		assertEquals("testCase84() contentGuardResourceResponse.getTotal_page_analysis_issues() incorrect.", "24",
				String.valueOf(contentGuardResourceResponse.getTotal_page_analysis_issues()));
		assertNotNull("testCase84() contentGuardResourceResponse.getUrl_page_analysis_results_list() should not be null.",
				contentGuardResourceResponse.getUrl_page_analysis_results_list());
		assertEquals("testCase84() contentGuardResourceResponse.getUrl_page_analysis_results_list().length incorrect.", "2",
				String.valueOf(contentGuardResourceResponse.getUrl_page_analysis_results_list().length));

		assertEquals("testCase84() contentGuardResourceResponse.getUrl_page_analysis_results_list()[0].getUrl() incorrect.",
				"https://selecthomewarranty.com/termsconditions/", contentGuardResourceResponse.getUrl_page_analysis_results_list()[0].getUrl());
		assertEquals("testCase84() contentGuardResourceResponse.getUrl_page_analysis_results_list()[0].getCrawl_timestamp() incorrect.", "2021-04-05 00:02:24",
				contentGuardResourceResponse.getUrl_page_analysis_results_list()[0].getCrawl_timestamp());

		Integer[] ruleArray = contentGuardResourceResponse.getUrl_page_analysis_results_list()[0].getRule_array();
		assertEquals("testCase84() ruleArray.length incorrect.", 10, ruleArray.length);

		FormatUtils.getInstance().logMemoryUsage("testCase84() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase85() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_PAGE_ANALYSIS_ISSUES;
		System.out.println("testCase85() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(9678);
		contentGuardResourceRequest.setGroup_id(1L);
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase85() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase85() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase85() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		boolean isTotalIssuesGreaterThanZero = false;
		if (contentGuardResourceResponse.getTotal_page_analysis_issues() > 0) {
			isTotalIssuesGreaterThanZero = true;
		}
		assertEquals("testCase85() isTotalIssuesGreaterThanZero incorrect.", false, isTotalIssuesGreaterThanZero);
//		assertNotNull("testCase85() contentGuardResourceResponse.getUrl_page_analysis_results_list() should not be null.",
//				contentGuardResourceResponse.getUrl_page_analysis_results_list());
//		assertEquals("testCase85() contentGuardResourceResponse.getUrl_page_analysis_results_list().length incorrect.", "2",
//				String.valueOf(contentGuardResourceResponse.getUrl_page_analysis_results_list().length));
//
//		if (BooleanUtils.isTrue(contentGuardResourceResponse.getSuccess())) {
//			System.out.println("total page analysis issues=" + contentGuardResourceResponse.getTotal_page_analysis_issues());
//			for (UrlPageAnalysisResults urlPageAnalysisResults : contentGuardResourceResponse.getUrl_page_analysis_results_list()) {
//				System.out.println("url=" + urlPageAnalysisResults.getUrl());
//				System.out.println("crawl timestamp=" + urlPageAnalysisResults.getCrawl_timestamp());
//				for (Integer ruleNumber : urlPageAnalysisResults.getRule_array()) {
//					System.out.println("ruleNumber=" + ruleNumber);
//				}
//			}
//		}

		FormatUtils.getInstance().logMemoryUsage("testCase85() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase86() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_PAGE_ANALYSIS_ISSUES;
		System.out.println("testCase86() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(9678);
		contentGuardResourceRequest.setGroup_id(1L);
		contentGuardResourceRequest.setCrawl_date("2021-04-05");
		contentGuardResourceRequest.setCrawl_hour(0);
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase86() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase86() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase86() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		assertEquals("testCase86() contentGuardResourceResponse.getTotal_page_analysis_issues() incorrect.", "24",
				String.valueOf(contentGuardResourceResponse.getTotal_page_analysis_issues()));
		assertNotNull("testCase86() contentGuardResourceResponse.getUrl_page_analysis_results_list() should not be null.",
				contentGuardResourceResponse.getUrl_page_analysis_results_list());
		assertEquals("testCase86() contentGuardResourceResponse.getUrl_page_analysis_results_list().length incorrect.", "2",
				String.valueOf(contentGuardResourceResponse.getUrl_page_analysis_results_list().length));

		assertEquals("testCase86() contentGuardResourceResponse.getUrl_page_analysis_results_list()[0].getUrl() incorrect.",
				"https://selecthomewarranty.com/termsconditions/", contentGuardResourceResponse.getUrl_page_analysis_results_list()[0].getUrl());
		assertEquals("testCase86() contentGuardResourceResponse.getUrl_page_analysis_results_list()[0].getCrawl_timestamp() incorrect.", "2021-04-05 00:02:24",
				contentGuardResourceResponse.getUrl_page_analysis_results_list()[0].getCrawl_timestamp());

		Integer[] ruleArray = contentGuardResourceResponse.getUrl_page_analysis_results_list()[0].getRule_array();
		assertEquals("testCase86() ruleArray.length incorrect.", 10, ruleArray.length);

		FormatUtils.getInstance().logMemoryUsage("testCase86() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase87() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_TIMELINE;
		System.out.println("testCase87() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setCrawl_date("2021-01-14");
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase87() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase87() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase87() contentGuardResourceResponse.getSuccess() should be false.", false, contentGuardResourceResponse.getSuccess());
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase87() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase87() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00033\",\"error_message\":\"Request parameter crawl_date 2021-01-14 is not allowed.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase87() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase88() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_TIMELINE;
		System.out.println("testCase88() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setCrawl_hour(18);
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase88() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase88() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase88() contentGuardResourceResponse.getSuccess() should be false.", false, contentGuardResourceResponse.getSuccess());
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase88() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase88() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00034\",\"error_message\":\"Request parameter crawl_hour 18 is not allowed.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase88() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase89() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_TIMELINE;
		System.out.println("testCase89() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setCrawl_timestamp("test");
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase89() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase89() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase89() contentGuardResourceResponse.getSuccess() should be false.", false, contentGuardResourceResponse.getSuccess());
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase89() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase89() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00037\",\"error_message\":\"Request parameter crawl_timestamp test is not allowed.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase89() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase90() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase90() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setReturn_details(true);
		contentGuardResourceRequest.setStart_crawl_date("2020-01-02");
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase90() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase90() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase90() contentGuardResourceResponse.getSuccess() should be true.", false, contentGuardResourceResponse.getSuccess());
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase90() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase90() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00035\",\"error_message\":\"Request parameter start_crawl_date 2020-01-02 is not allowed.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase90() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase91() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase91() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setReturn_details(true);
		contentGuardResourceRequest.setEnd_crawl_date("2020-01-02");
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase91() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase91() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase91() contentGuardResourceResponse.getSuccess() should be true.", false, contentGuardResourceResponse.getSuccess());
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase91() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase91() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00036\",\"error_message\":\"Request parameter end_crawl_date 2020-01-02 is not allowed.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase91() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase92() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase92() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setReturn_details(true);
		contentGuardResourceRequest.setCrawl_timestamp("2020-01-02");
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase92() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase92() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase92() contentGuardResourceResponse.getSuccess() should be true.", false, contentGuardResourceResponse.getSuccess());
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase92() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase92() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00037\",\"error_message\":\"Request parameter crawl_timestamp 2020-01-02 is not allowed.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase92() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase93() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_URL_DETAILS;
		System.out.println("testCase93() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setStart_crawl_date("2021-01-02");
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase93() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase93() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase93() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase93() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00035\",\"error_message\":\"Request parameter start_crawl_date 2021-01-02 is not allowed.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase93() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase94() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_URL_DETAILS;
		System.out.println("testCase94() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setEnd_crawl_date("2021-01-02");
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase94() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase94() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase94() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase94() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00036\",\"error_message\":\"Request parameter end_crawl_date 2021-01-02 is not allowed.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase94() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase95() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_URL_DETAILS;
		System.out.println("testCase95() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setCrawl_date("2021-01-02");
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase95() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase95() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase95() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase95() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00033\",\"error_message\":\"Request parameter crawl_date 2021-01-02 is not allowed.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase95() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase96() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_URL_DETAILS;
		System.out.println("testCase96() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setCrawl_hour(18);
		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase96() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase96() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase96() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase96() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00034\",\"error_message\":\"Request parameter crawl_hour 18 is not allowed.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase96() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase97() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase97() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(9678);
		contentGuardResourceRequest.setGroup_id(1L);
		contentGuardResourceRequest.setCrawl_date("2021-04-21");
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(10);
		contentGuardResourceRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);
		contentGuardResourceRequest.setReturn_details(true);
		contentGuardResourceRequest.setFilter_url("cover");

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase97() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase97() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase97() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		assertEquals("testCase97() contentGuardResourceResponse.getStart_crawl_timestamp() incorrect.", "2021-04-21 00:00:00",
				contentGuardResourceResponse.getStart_crawl_timestamp());
		assertEquals("testCase97() contentGuardResourceResponse.getEnd_crawl_timestamp() incorrect.", "2021-04-21 23:59:59",
				contentGuardResourceResponse.getEnd_crawl_timestamp());
		assertEquals("testCase97() contentGuardResourceResponse.getFilter_url() incorrect.", "cover", contentGuardResourceResponse.getFilter_url());
		assertEquals("testCase97() contentGuardResourceResponse.getUrl_changes_list().size() incorrect.", "1",
				String.valueOf(contentGuardResourceResponse.getUrl_changes_list().size()));
		assertEquals("testCase97() contentGuardResourceResponse.getFilter_url_totals() incorrect.", "1",
				String.valueOf(contentGuardResourceResponse.getFilter_url_totals()));
		assertEquals("testCase97() contentGuardResourceResponse.getUrl_changes_list().get(0).getUrl() incorrect.", "http://coverage.firstam.com/#additional",
				contentGuardResourceResponse.getUrl_changes_list().get(0).getUrl());
		FormatUtils.getInstance().logMemoryUsage("testCase97() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase98() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase98() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setCrawl_date(DOMAIN_ID_9632_CRAWL_DATE);
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(10);
		contentGuardResourceRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);
		contentGuardResourceRequest.setReturn_details(true);
		contentGuardResourceRequest.setFilter_url("Access");

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase98() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase98() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase98() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		assertEquals("testCase98() contentGuardResourceResponse.getStart_crawl_timestamp() incorrect.", "2021-01-15 00:00:00",
				contentGuardResourceResponse.getStart_crawl_timestamp());
		assertEquals("testCase98() contentGuardResourceResponse.getEnd_crawl_timestamp() incorrect.", "2021-01-15 23:59:59",
				contentGuardResourceResponse.getEnd_crawl_timestamp());
		assertEquals("testCase98() contentGuardResourceResponse.getFilter_url() incorrect.", "Access", contentGuardResourceResponse.getFilter_url());
		assertEquals("testCase98() contentGuardResourceResponse.getUrl_changes_list().size() incorrect.", "6",
				String.valueOf(contentGuardResourceResponse.getUrl_changes_list().size()));
		assertEquals("testCase98() contentGuardResourceResponse.getFilter_url_totals() incorrect.", "6",
				String.valueOf(contentGuardResourceResponse.getFilter_url_totals()));
		assertEquals("testCase98() contentGuardResourceResponse.getUrl_changes_list().get(0).getUrl() incorrect.",
				"https://emplois.ca.indeed.com/Montr%C3%A9al,-QC-Emplois-Game-Access", contentGuardResourceResponse.getUrl_changes_list().get(0).getUrl());

		assertEquals("testCase98() contentGuardResourceResponse.getUrl_changes_list().get(1).getUrl() incorrect.",
				"https://emplois.ca.indeed.com/emplois?q=Game+Access&jt=casual", contentGuardResourceResponse.getUrl_changes_list().get(1).getUrl());

		assertEquals("testCase98() contentGuardResourceResponse.getUrl_changes_list().get(2).getUrl() incorrect.",
				"https://emplois.ca.indeed.com/emplois?q=Game+Access&l=Montreal-nord,+QC", contentGuardResourceResponse.getUrl_changes_list().get(2).getUrl());

		//String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		FormatUtils.getInstance().logMemoryUsage("testCase98() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase99() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase99() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setCrawl_date(DOMAIN_ID_9632_CRAWL_DATE);
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(10);
		contentGuardResourceRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);
		contentGuardResourceRequest.setReturn_details(true);
		contentGuardResourceRequest.setFilter_url("https://emplois.ca.indeed.com/Montr%C3%A9al168-QC-Emplois-Game-Access");

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase99() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase99() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase99() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		assertEquals("testCase99() contentGuardResourceResponse.getFilter_url_totals() incorrect.", "0",
				String.valueOf(contentGuardResourceResponse.getFilter_url_totals()));
		FormatUtils.getInstance().logMemoryUsage("testCase99() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase100() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase100() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(DOMAIN_ID_9632);
		contentGuardResourceRequest.setGroup_id(DOMAIN_ID_9632_GROUP_ID_2);
		contentGuardResourceRequest.setCrawl_date(DOMAIN_ID_9632_CRAWL_DATE);
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(10);
		contentGuardResourceRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);
		contentGuardResourceRequest.setReturn_details(true);
		contentGuardResourceRequest.setFilter_url("emplois?q");

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase100() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase100() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase100() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		assertEquals("testCase100() contentGuardResourceResponse.getStart_crawl_timestamp() incorrect.", "2021-01-15 00:00:00",
				contentGuardResourceResponse.getStart_crawl_timestamp());
		assertEquals("testCase100() contentGuardResourceResponse.getEnd_crawl_timestamp() incorrect.", "2021-01-15 23:59:59",
				contentGuardResourceResponse.getEnd_crawl_timestamp());
		assertEquals("testCase100() contentGuardResourceResponse.getFilter_url() incorrect.", "emplois?q", contentGuardResourceResponse.getFilter_url());
		assertEquals("testCase100() contentGuardResourceResponse.getUrl_changes_list().size() incorrect.", "10",
				String.valueOf(contentGuardResourceResponse.getUrl_changes_list().size()));
		assertEquals("testCase100() contentGuardResourceResponse.getFilter_url_totals() incorrect.", "625",
				String.valueOf(contentGuardResourceResponse.getFilter_url_totals()));

		//String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		FormatUtils.getInstance().logMemoryUsage("testCase100() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase101() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_TRACKED_PAGES;
		System.out.println("testCase101() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(9632);
		contentGuardResourceRequest.setGroup_id(2L);
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(8);
		contentGuardResourceRequest.setSort_by(IConstants.SORT_BY_URL_ASC);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase101() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase101() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase101() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		assertEquals("testCase101() contentGuardResourceResponse.getEnd_of_tracked_pages_flag() incorrect.", false,
				contentGuardResourceResponse.getEnd_of_tracked_pages_flag());
		assertEquals("testCase101() contentGuardResourceResponse.getTracked_group_list().length incorrect.", "1",
				String.valueOf(contentGuardResourceResponse.getTracked_group_list().length));
		assertEquals("testCase101() contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list().length incorrect.", "8",
				String.valueOf(contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list().length));
		assertEquals("testCase101() contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list()[0].getUrl() incorrect.",
				"http://emplois.ca.indeed.com/Brossard,-QC-Emplois-Les-Mini",
				contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list()[0].getUrl());

		//		if (BooleanUtils.isTrue(contentGuardResourceResponse.getSuccess())) {
		//			if (contentGuardResourceResponse.getTracked_group_list() != null && contentGuardResourceResponse.getTracked_group_list().length > 0) {
		//				for (ContentGuardTrackedGroup contentGuardTrackedGroup : contentGuardResourceResponse.getTracked_group_list()) {
		//					for (ContentGuardTrackedPage contentGuardTrackedPage : contentGuardTrackedGroup.getTracked_page_list()) {
		//						System.out.println("groupId=" + contentGuardTrackedGroup.getGroup_id() + ",groupName=" + contentGuardTrackedGroup.getGroup_name()
		//								+ ",groupCrawlFrequencyType=" + contentGuardTrackedGroup.getCrawl_frequency_type() + ",url=" + contentGuardTrackedPage.getUrl()
		//								+ ",responseCode=" + contentGuardTrackedPage.getResponse_code() + ",lastUpdateTimestamp="
		//								+ contentGuardTrackedPage.getLast_update_timestamp());
		//					}
		//				}
		//			}
		//		}

		FormatUtils.getInstance().logMemoryUsage("testCase101() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase102() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_TRACKED_PAGES;
		System.out.println("testCase102() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(9632);
		contentGuardResourceRequest.setGroup_id(2L);
		contentGuardResourceRequest.setPage_number(250);
		contentGuardResourceRequest.setRows_per_page(8);
		contentGuardResourceRequest.setSort_by(IConstants.SORT_BY_URL_ASC);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase102() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase102() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase102() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		assertEquals("testCase102() contentGuardResourceResponse.getEnd_of_tracked_pages_flag() incorrect.", true,
				contentGuardResourceResponse.getEnd_of_tracked_pages_flag());
		assertEquals("testCase102() contentGuardResourceResponse.getTracked_group_list().length incorrect.", "1",
				String.valueOf(contentGuardResourceResponse.getTracked_group_list().length));
		assertEquals("testCase102() contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list().length incorrect.", "6",
				String.valueOf(contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list().length));
		assertEquals("testCase102() contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list()[0].getUrl() incorrect.",
				"https://emplois.ca.indeed.com/m/jobs?q=The+Brick&l=Lachenaie%2C+QC&fromTravel=1&start=0",
				contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list()[0].getUrl());

		FormatUtils.getInstance().logMemoryUsage("testCase102() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase103() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_TRACKED_PAGES;
		System.out.println("testCase103() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(9632);
		contentGuardResourceRequest.setGroup_id(2L);
		contentGuardResourceRequest.setPage_number(251);
		contentGuardResourceRequest.setRows_per_page(8);
		contentGuardResourceRequest.setSort_by(IConstants.SORT_BY_URL_ASC);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase103() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase103() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase103() contentGuardResourceResponse.getSuccess() should be true.", false, contentGuardResourceResponse.getSuccess());
		assertNotNull("testCase103() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase103() contentGuardResourceResponse.getError().getError_code() is incorrect", "00030",
				contentGuardResourceResponse.getError().getError_code());
		FormatUtils.getInstance().logMemoryUsage("testCase103() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase104() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_TRACKED_PAGES;
		System.out.println("testCase104() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(9632);
		contentGuardResourceRequest.setGroup_id(2L);
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(8);
		contentGuardResourceRequest.setSort_by(IConstants.SORT_BY_URL_DESC);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase104() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase104() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase104() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		assertEquals("testCase104() contentGuardResourceResponse.getEnd_of_tracked_pages_flag() incorrect.", false,
				contentGuardResourceResponse.getEnd_of_tracked_pages_flag());
		assertEquals("testCase104() contentGuardResourceResponse.getTracked_group_list().length incorrect.", "1",
				String.valueOf(contentGuardResourceResponse.getTracked_group_list().length));
		assertEquals("testCase104() contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list().length incorrect.", "8",
				String.valueOf(contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list().length));
		assertEquals("testCase104() contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list()[0].getUrl() incorrect.",
				"https://emplois.ca.indeed.com/m/jobs?q=Walmart+Canada%2C+Saint+Joseph&l=Gatineau%2C+QC",
				contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list()[0].getUrl());

		FormatUtils.getInstance().logMemoryUsage("testCase104() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase105() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_TRACKED_PAGES;
		System.out.println("testCase105() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(9632);
		contentGuardResourceRequest.setGroup_id(2L);
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(8);
		contentGuardResourceRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_ASC);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase105() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase105() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase105() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		assertEquals("testCase105() contentGuardResourceResponse.getEnd_of_tracked_pages_flag() incorrect.", false,
				contentGuardResourceResponse.getEnd_of_tracked_pages_flag());
		assertEquals("testCase105() contentGuardResourceResponse.getTracked_group_list().length incorrect.", "1",
				String.valueOf(contentGuardResourceResponse.getTracked_group_list().length));
		assertEquals("testCase105() contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list().length incorrect.", "8",
				String.valueOf(contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list().length));
		assertEquals("testCase105() contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list()[0].getUrl() incorrect.",
				"http://emplois.ca.indeed.com/Sherbrooke,-QC-Emplois-UPS", contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list()[0].getUrl());
		assertEquals("testCase105() contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list()[0].getLast_update_timestamp() incorrect.",
				"2021-01-14 10:56:20", contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list()[0].getLast_update_timestamp());

		FormatUtils.getInstance().logMemoryUsage("testCase105() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase106() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_TRACKED_PAGES;
		System.out.println("testCase106() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(9632);
		contentGuardResourceRequest.setGroup_id(2L);
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(8);
		contentGuardResourceRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase106() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase106() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase106() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		assertEquals("testCase106() contentGuardResourceResponse.getEnd_of_tracked_pages_flag() incorrect.", false,
				contentGuardResourceResponse.getEnd_of_tracked_pages_flag());
		assertEquals("testCase106() contentGuardResourceResponse.getTracked_group_list().length incorrect.", "1",
				String.valueOf(contentGuardResourceResponse.getTracked_group_list().length));
		assertEquals("testCase106() contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list().length incorrect.", "8",
				String.valueOf(contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list().length));
		assertEquals("testCase106() contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list()[0].getUrl() incorrect.",
				"https://emplois.ca.indeed.com/Montr%C3%A9al,-QC-Emplois-McDonald",
				contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list()[0].getUrl());
		assertEquals("testCase106() contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list()[0].getLast_update_timestamp() incorrect.",
				"2021-04-23 10:57:52", contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list()[0].getLast_update_timestamp());

		FormatUtils.getInstance().logMemoryUsage("testCase106() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase107() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_TRACKED_PAGES;
		System.out.println("testCase107() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(9632);
		contentGuardResourceRequest.setGroup_id(2L);
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(8);
		contentGuardResourceRequest.setSort_by(IConstants.SORT_BY_RESPONSE_CODE_ASC);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase107() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase107() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase107() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		assertEquals("testCase107() contentGuardResourceResponse.getEnd_of_tracked_pages_flag() incorrect.", false,
				contentGuardResourceResponse.getEnd_of_tracked_pages_flag());
		assertEquals("testCase107() contentGuardResourceResponse.getTracked_group_list().length incorrect.", "1",
				String.valueOf(contentGuardResourceResponse.getTracked_group_list().length));
		assertEquals("testCase107() contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list().length incorrect.", "8",
				String.valueOf(contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list().length));
		assertEquals("testCase107() contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list()[0].getUrl() incorrect.",
				"https://emplois.ca.indeed.com/", contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list()[0].getUrl());
		assertEquals("testCase107() contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list()[0].getLast_update_timestamp() incorrect.",
				"2021-04-23 10:20:29", contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list()[0].getLast_update_timestamp());
		assertEquals("testCase107() contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list()[0].getResponse_code() incorrect.", "200",
				contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list()[0].getResponse_code());

		FormatUtils.getInstance().logMemoryUsage("testCase107() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase108() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_TRACKED_PAGES;
		System.out.println("testCase108() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(9632);
		contentGuardResourceRequest.setGroup_id(2L);
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(8);
		contentGuardResourceRequest.setSort_by(IConstants.SORT_BY_RESPONSE_CODE_DESC);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase108() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase108() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase108() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		assertEquals("testCase108() contentGuardResourceResponse.getEnd_of_tracked_pages_flag() incorrect.", false,
				contentGuardResourceResponse.getEnd_of_tracked_pages_flag());
		assertEquals("testCase108() contentGuardResourceResponse.getTracked_group_list().length incorrect.", "1",
				String.valueOf(contentGuardResourceResponse.getTracked_group_list().length));
		assertEquals("testCase108() contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list().length incorrect.", "8",
				String.valueOf(contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list().length));
		assertEquals("testCase108() contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list()[0].getUrl() incorrect.",
				"https://emplois.ca.indeed.com/cmp/School-District-%2336-%28surrey%29/reviews",
				contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list()[0].getUrl());
		assertEquals("testCase108() contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list()[0].getLast_update_timestamp() incorrect.",
				"2021-01-14 11:06:16", contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list()[0].getLast_update_timestamp());
		assertEquals("testCase108() contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list()[0].getResponse_code() incorrect.", "404",
				contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list()[0].getResponse_code());

		FormatUtils.getInstance().logMemoryUsage("testCase108() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase109() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_TRACKED_PAGES;
		System.out.println("testCase109() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(9632);
		contentGuardResourceRequest.setGroup_id(2L);
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(8);
		contentGuardResourceRequest.setSort_by(IConstants.SORT_BY_URL_ASC);
		contentGuardResourceRequest.setFilter_url("https://emplois.ca.indeed.com/Brossard,-QC-Emplois-Mini");

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase109() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase109() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase109() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());
		assertEquals("testCase109() contentGuardResourceResponse.getEnd_of_tracked_pages_flag() incorrect.", true,
				contentGuardResourceResponse.getEnd_of_tracked_pages_flag());
		assertEquals("testCase109() contentGuardResourceResponse.getTracked_group_list().length incorrect.", "1",
				String.valueOf(contentGuardResourceResponse.getTracked_group_list().length));
		assertEquals("testCase109() contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list().length incorrect.", "1",
				String.valueOf(contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list().length));
		assertEquals("testCase109() contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list()[0].getUrl() incorrect.",
				"https://emplois.ca.indeed.com/Brossard,-QC-Emplois-Mini", contentGuardResourceResponse.getTracked_group_list()[0].getTracked_page_list()[0].getUrl());

		FormatUtils.getInstance().logMemoryUsage("testCase109() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase110() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_TRACKED_PAGES;
		System.out.println("testCase110() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(9632);
		contentGuardResourceRequest.setGroup_id(2L);
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(8);
		contentGuardResourceRequest.setSort_by(IConstants.SORT_BY_URL_ASC);
		contentGuardResourceRequest.setFilter_url("https://test.com/Brossard,-QC-Emplois-Mini");

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase110() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase110() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase110() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());

		FormatUtils.getInstance().logMemoryUsage("testCase110() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase111() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase111() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(9879);
		contentGuardResourceRequest.setGroup_id(3234L);
		contentGuardResourceRequest.setCrawl_date("2021-10-27");
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(18);
		contentGuardResourceRequest.setSort_by(IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC);
		contentGuardResourceRequest.setReturn_details(true);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase111() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase111() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase111() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());

		for (ContentGuardUrlChanges contentGuardUrlChanges : contentGuardResourceResponse.getUrl_changes_list()) {
			if (StringUtils.equalsIgnoreCase(contentGuardUrlChanges.getUrl(), "https://www.phoenix.edu/degrees/business.html")) {
				assertNotNull("testCase111() contentGuardUrlChanges.getResponse_code() should not be null.", contentGuardUrlChanges.getResponse_code());
				assertNotNull("testCase111() contentGuardUrlChanges.getLast_update_timestamp() should not be null.", contentGuardUrlChanges.getLast_update_timestamp());
			}
		}

		FormatUtils.getInstance().logMemoryUsage("testCase111() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase112() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase112() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(9879);
		contentGuardResourceRequest.setGroup_id(3234L);
		contentGuardResourceRequest.setCrawl_date("2021-10-27");
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(18);
		contentGuardResourceRequest.setSort_by(IConstants.SORT_BY_RESPONSE_CODE_ASC);
		contentGuardResourceRequest.setReturn_details(true);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase112() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase112() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase112() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());

		for (ContentGuardUrlChanges contentGuardUrlChanges : contentGuardResourceResponse.getUrl_changes_list()) {
			if (StringUtils.equalsIgnoreCase(contentGuardUrlChanges.getUrl(), "https://www.phoenix.edu/degrees/business.html")) {
				assertNotNull("testCase112() contentGuardUrlChanges.getResponse_code() should not be null.", contentGuardUrlChanges.getResponse_code());
				assertNotNull("testCase112() contentGuardUrlChanges.getLast_update_timestamp() should not be null.", contentGuardUrlChanges.getLast_update_timestamp());
			}
		}

		FormatUtils.getInstance().logMemoryUsage("testCase112() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase113() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase113() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(9879);
		contentGuardResourceRequest.setGroup_id(3234L);
		contentGuardResourceRequest.setCrawl_date("2021-10-27");
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(18);
		contentGuardResourceRequest.setSort_by(IConstants.SORT_BY_RESPONSE_CODE_DESC);
		contentGuardResourceRequest.setReturn_details(true);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase113() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase113() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase113() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());

		for (ContentGuardUrlChanges contentGuardUrlChanges : contentGuardResourceResponse.getUrl_changes_list()) {
			if (StringUtils.equalsIgnoreCase(contentGuardUrlChanges.getUrl(), "https://www.phoenix.edu/degrees/business.html")) {
				assertNotNull("testCase113() contentGuardUrlChanges.getResponse_code() should not be null.", contentGuardUrlChanges.getResponse_code());
				assertNotNull("testCase113() contentGuardUrlChanges.getLast_update_timestamp() should not be null.", contentGuardUrlChanges.getLast_update_timestamp());
			}
		}

		FormatUtils.getInstance().logMemoryUsage("testCase113() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase114() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase114() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(9879);
		contentGuardResourceRequest.setGroup_id(3234L);
		contentGuardResourceRequest.setCrawl_date("2021-10-27");
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(18);
		contentGuardResourceRequest.setSort_by(IConstants.SORT_BY_LAST_UPDATE_TIMESTAMP_ASC);
		contentGuardResourceRequest.setReturn_details(true);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase114() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase114() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase114() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());

		for (ContentGuardUrlChanges contentGuardUrlChanges : contentGuardResourceResponse.getUrl_changes_list()) {
			if (StringUtils.equalsIgnoreCase(contentGuardUrlChanges.getUrl(), "https://www.phoenix.edu/degrees/business.html")) {
				assertNotNull("testCase114() contentGuardUrlChanges.getResponse_code() should not be null.", contentGuardUrlChanges.getResponse_code());
				assertNotNull("testCase114() contentGuardUrlChanges.getLast_update_timestamp() should not be null.", contentGuardUrlChanges.getLast_update_timestamp());
			}
		}

		FormatUtils.getInstance().logMemoryUsage("testCase114() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase115() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase115() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(9879);
		contentGuardResourceRequest.setGroup_id(3234L);
		contentGuardResourceRequest.setCrawl_date("2021-10-27");
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(18);
		contentGuardResourceRequest.setSort_by(IConstants.SORT_BY_LAST_UPDATE_TIMESTAMP_DESC);
		contentGuardResourceRequest.setReturn_details(true);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase115() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase115() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase115() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());

		for (ContentGuardUrlChanges contentGuardUrlChanges : contentGuardResourceResponse.getUrl_changes_list()) {
			if (StringUtils.equalsIgnoreCase(contentGuardUrlChanges.getUrl(), "https://www.phoenix.edu/degrees/business.html")) {
				assertNotNull("testCase115() contentGuardUrlChanges.getResponse_code() should not be null.", contentGuardUrlChanges.getResponse_code());
				assertNotNull("testCase115() contentGuardUrlChanges.getLast_update_timestamp() should not be null.", contentGuardUrlChanges.getLast_update_timestamp());
			}
		}

		FormatUtils.getInstance().logMemoryUsage("testCase115() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase116() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_DOMAIN_SUMMARY;
		System.out.println("testCase116() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(4661);
		contentGuardResourceRequest.setGroup_id(215L);
		contentGuardResourceRequest.setCrawl_date("2000-10-27");
		contentGuardResourceRequest.setCrawl_hour(18);
		contentGuardResourceRequest.setPage_number(1);
		contentGuardResourceRequest.setRows_per_page(18);
		contentGuardResourceRequest.setSort_by(IConstants.SORT_BY_LAST_UPDATE_TIMESTAMP_DESC);
		contentGuardResourceRequest.setReturn_details(true);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase116() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase116() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase116() contentGuardResourceResponse.getSuccess() should be true.", true, contentGuardResourceResponse.getSuccess());

		for (ContentGuardUrlChanges contentGuardUrlChanges : contentGuardResourceResponse.getUrl_changes_list()) {
			assertEquals("testCase116() contentGuardUrlChanges.getTotal_changes() should be 0.", "0", String.valueOf(contentGuardUrlChanges.getTotal_changes()));
		}

		FormatUtils.getInstance().logMemoryUsage("testCase116() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase117() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_USAGE;
		System.out.println("testCase117() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(256);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase117() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase117() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase117() contentGuardResourceResponse.getSuccess() should be false.", false, contentGuardResourceResponse.getSuccess());
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase117() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase117() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00038\",\"error_message\":\"Request parameter start_usage_date is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase117() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase118() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_USAGE;
		System.out.println("testCase118() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(256);
		contentGuardResourceRequest.setStart_usage_date("2022-02-01");

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase118() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase118() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase118() contentGuardResourceResponse.getSuccess() should be false.", false, contentGuardResourceResponse.getSuccess());
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase118() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase118() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00040\",\"error_message\":\"Request parameter end_usage_date is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase118() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase119() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_USAGE;
		System.out.println("testCase119() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(256);
		contentGuardResourceRequest.setStart_usage_date("test");

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase119() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase119() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase119() contentGuardResourceResponse.getSuccess() should be false.", false, contentGuardResourceResponse.getSuccess());
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase119() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase119() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00039\",\"error_message\":\"Request parameter start_usage_date test is invalid.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase119() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase120() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_USAGE;
		System.out.println("testCase120() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(256);
		contentGuardResourceRequest.setStart_usage_date("2022-02-01");
		contentGuardResourceRequest.setEnd_usage_date("test");

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase120() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase120() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase120() contentGuardResourceResponse.getSuccess() should be false.", false, contentGuardResourceResponse.getSuccess());
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase120() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase120() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00041\",\"error_message\":\"Request parameter end_usage_date test is invalid.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase120() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase121() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_USAGE;
		System.out.println("testCase121() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(256);
		contentGuardResourceRequest.setStart_usage_date("2022-02-01");
		contentGuardResourceRequest.setEnd_usage_date("2022-01-01");

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase121() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase121() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase121() contentGuardResourceResponse.getSuccess() should be false.", false, contentGuardResourceResponse.getSuccess());
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase121() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase121() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00042\",\"error_message\":\"Request parameter start_usage_date 2022-02-01 cannot be later than end_usage_date.\"}}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase121() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase122() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_USAGE;
		System.out.println("testCase122() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(256);
		contentGuardResourceRequest.setStart_usage_date("2022-02-01");
		contentGuardResourceRequest.setEnd_usage_date("2022-02-28");

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase122() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase122() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertTrue("testCase122() contentGuardResourceResponse.getSuccess() should be true.", contentGuardResourceResponse.getSuccess());
		assertNotNull("testCase122() contentGuardResourceResponse.getGroup_usage_list() should not be null.", contentGuardResourceResponse.getGroup_usage_list());
		boolean isLengthCorrect = false;
		if (contentGuardResourceResponse.getGroup_usage_list().length > 1) {
			isLengthCorrect = true;
		}
		assertTrue("testCase122() contentGuardResourceResponse.getGroup_usage_list().length should be > 1.", isLengthCorrect);
		assertNotNull("testCase122() contentGuardResourceResponse.getGroup_usage_list()[0].getGroup_id() should not be null.",
				contentGuardResourceResponse.getGroup_usage_list()[0].getGroup_id());
		assertNotNull("testCase122() contentGuardResourceResponse.getGroup_usage_list()[0].getGroup_name() should not be null.",
				contentGuardResourceResponse.getGroup_usage_list()[0].getGroup_name());
		assertNotNull("testCase122() contentGuardResourceResponse.getGroup_usage_list()[0].getCrawl_frequency() should not be null.",
				contentGuardResourceResponse.getGroup_usage_list()[0].getCrawl_frequency());
		assertNotNull("testCase122() contentGuardResourceResponse.getGroup_usage_list()[0].getUsage_list() should not be null.",
				contentGuardResourceResponse.getGroup_usage_list()[0].getUsage_list());
		isLengthCorrect = false;
		if (contentGuardResourceResponse.getGroup_usage_list()[0].getUsage_list().length > 1) {
			isLengthCorrect = true;
		}
		assertTrue("testCase122() contentGuardResourceResponse.getGroup_usage_list()[0].getUsage_list().length should be > 1.", isLengthCorrect);
		FormatUtils.getInstance().logMemoryUsage("testCase122() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase123() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_USAGE;
		System.out.println("testCase123() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(256);
		contentGuardResourceRequest.setStart_usage_date("2022-02-01");
		contentGuardResourceRequest.setEnd_usage_date("2022-01-01");
		contentGuardResourceRequest.setGroup_id(12345678L);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase123() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase123() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertEquals("testCase123() contentGuardResourceResponse.getSuccess() should be false.", false, contentGuardResourceResponse.getSuccess());
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		assertNotNull("testCase123() contentGuardResourceResponse.getError() should not be null.", contentGuardResourceResponse.getError());
		assertEquals("testCase123() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"00019\",\"error_message\":\"Request parameter group_id 12345678 is invalid.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase123() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase124() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_USAGE;
		System.out.println("testCase124() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(256);
		contentGuardResourceRequest.setStart_usage_date("2022-02-01");
		contentGuardResourceRequest.setEnd_usage_date("2022-02-28");
		contentGuardResourceRequest.setGroup_id(42L);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase124() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase124() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertTrue("testCase124() contentGuardResourceResponse.getSuccess() should be true.", contentGuardResourceResponse.getSuccess());
		assertNotNull("testCase124() contentGuardResourceResponse.getGroup_usage_list() should not be null.", contentGuardResourceResponse.getGroup_usage_list());
		boolean isLengthCorrect = false;
		if (contentGuardResourceResponse.getGroup_usage_list().length == 1) {
			isLengthCorrect = true;
		}
		assertTrue("testCase124() contentGuardResourceResponse.getGroup_usage_list().length should be 1.", isLengthCorrect);
		assertNotNull("testCase124() contentGuardResourceResponse.getGroup_usage_list()[0].getGroup_id() should not be null.",
				contentGuardResourceResponse.getGroup_usage_list()[0].getGroup_id());
		assertNotNull("testCase124() contentGuardResourceResponse.getGroup_usage_list()[0].getGroup_name() should not be null.",
				contentGuardResourceResponse.getGroup_usage_list()[0].getGroup_name());
		assertNotNull("testCase124() contentGuardResourceResponse.getGroup_usage_list()[0].getCrawl_frequency() should not be null.",
				contentGuardResourceResponse.getGroup_usage_list()[0].getCrawl_frequency());
		assertNotNull("testCase124() contentGuardResourceResponse.getGroup_usage_list()[0].getUsage_list() should not be null.",
				contentGuardResourceResponse.getGroup_usage_list()[0].getUsage_list());
		isLengthCorrect = false;
		if (contentGuardResourceResponse.getGroup_usage_list()[0].getUsage_list().length > 1) {
			isLengthCorrect = true;
		}
		assertTrue("testCase124() contentGuardResourceResponse.getGroup_usage_list()[0].getUsage_list().length should be > 1.", isLengthCorrect);
		FormatUtils.getInstance().logMemoryUsage("testCase124() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase125() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = politeCrawlWebServiceClientService.getPoliteCrawlWebServiceEndPoint() + PoliteCrawlWebServiceClientService.ROUTER_CONTENT_GUARD
				+ IConstants.COMMAND_USAGE;
		System.out.println("testCase125() requestUrl=" + requestUrl);
		ContentGuardResourceRequest contentGuardResourceRequest = new ContentGuardResourceRequest();
		contentGuardResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		contentGuardResourceRequest.setDomain_id(256);
		contentGuardResourceRequest.setStart_usage_date("2022-02-22");
		contentGuardResourceRequest.setEnd_usage_date("2022-02-22");
		contentGuardResourceRequest.setGroup_id(42L);

		String requestParameters = new Gson().toJson(contentGuardResourceRequest, ContentGuardResourceRequest.class);
		//System.out.println("testCase125() requestParameters=" + requestParameters);
		ContentGuardResourceResponse contentGuardResourceResponse = politeCrawlWebServiceClientService.contentGuard(requestUrl, requestParameters);
		assertNotNull("testCase125() contentGuardResourceResponse should not be null.", contentGuardResourceResponse);
		assertTrue("testCase125() contentGuardResourceResponse.getSuccess() should be true.", contentGuardResourceResponse.getSuccess());
		assertNotNull("testCase125() contentGuardResourceResponse.getGroup_usage_list() should not be null.", contentGuardResourceResponse.getGroup_usage_list());
		boolean isLengthCorrect = false;
		if (contentGuardResourceResponse.getGroup_usage_list().length == 1) {
			isLengthCorrect = true;
		}
		assertTrue("testCase125() contentGuardResourceResponse.getGroup_usage_list().length should be 1.", isLengthCorrect);
		assertNotNull("testCase125() contentGuardResourceResponse.getGroup_usage_list()[0].getGroup_id() should not be null.",
				contentGuardResourceResponse.getGroup_usage_list()[0].getGroup_id());
		assertNotNull("testCase125() contentGuardResourceResponse.getGroup_usage_list()[0].getGroup_name() should not be null.",
				contentGuardResourceResponse.getGroup_usage_list()[0].getGroup_name());
		assertNotNull("testCase125() contentGuardResourceResponse.getGroup_usage_list()[0].getCrawl_frequency() should not be null.",
				contentGuardResourceResponse.getGroup_usage_list()[0].getCrawl_frequency());
		assertNotNull("testCase125() contentGuardResourceResponse.getGroup_usage_list()[0].getUsage_list() should not be null.",
				contentGuardResourceResponse.getGroup_usage_list()[0].getUsage_list());
		isLengthCorrect = false;
		if (contentGuardResourceResponse.getGroup_usage_list()[0].getUsage_list().length == 1) {
			isLengthCorrect = true;
		}
		assertTrue("testCase125() contentGuardResourceResponse.getGroup_usage_list()[0].getUsage_list().length should be == 1.", isLengthCorrect);
		FormatUtils.getInstance().logMemoryUsage("testCase125() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

}