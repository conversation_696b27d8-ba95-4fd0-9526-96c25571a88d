package com.actonia.test;

import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.dao.AiRulesDAO;
import com.actonia.dao.CommonParamDAO;
import com.actonia.dao.CompetitorUrlHtmlClickHouseDAO;
import com.actonia.dao.CompetitorUrlMd5EntityDAO;
import com.actonia.dao.ContentGuardAlertDAO;
import com.actonia.dao.ContentGuardChangeTrackingDAO;
import com.actonia.dao.ContentGuardClickHouseDAO;
import com.actonia.dao.ContentGuardCrawlTrackingDAO;
import com.actonia.dao.ContentGuardGroupDAO;
import com.actonia.dao.ContentGuardUrlDAO;
import com.actonia.dao.CrawlRequestLogDAO;
import com.actonia.dao.GroupTagEntityDAO;
import com.actonia.dao.LocalTargetUrlHtmlClickHouseDAO;
import com.actonia.dao.OwnDomainEntityDAO;
import com.actonia.dao.OwnDomainSettingEntityDAO;
import com.actonia.dao.RankingDetailClickHouseDAO;
import com.actonia.dao.SeoClarityKeywordEntityDAO;
import com.actonia.dao.TargetUrlChangeAlertDAO;
import com.actonia.dao.TargetUrlClickHouseDAO;
import com.actonia.dao.TargetUrlCrawlAdditionalContentEntityDAO;
import com.actonia.dao.TargetUrlDailyCrawlTrackingEntityDAO;
import com.actonia.dao.TargetUrlEntityDAO;
import com.actonia.dao.TargetUrlHtmlClickHouseDAO;
import com.actonia.dao.UserBacklinkDAO;
import com.actonia.dao.WebhookDAO;
import com.actonia.dao.ZapierErrorCodeDAO;
import com.actonia.dao.ZapierWebhookDAO;
import com.actonia.entity.AdditionalContentEntity;
import com.actonia.entity.AiRulesEntity;
import com.actonia.entity.CommonParamEntity;
import com.actonia.entity.CompetitorUrlMd5Entity;
import com.actonia.entity.ContentGuardAlertEntity;
import com.actonia.entity.ContentGuardChangeTrackingEntity;
import com.actonia.entity.ContentGuardCrawlTrackingEntity;
import com.actonia.entity.ContentGuardGroupEntity;
import com.actonia.entity.ContentGuardUrlEntity;
import com.actonia.entity.CrawlRequestLog;
import com.actonia.entity.GroupTagEntity;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.entity.SeoClarityKeywordEntity;
import com.actonia.entity.TargetUrlChangeAlertEntity;
import com.actonia.entity.TargetUrlClickHouseEntity;
import com.actonia.entity.TargetUrlDailyCrawlTrackingEntity;
import com.actonia.entity.TargetUrlEntity;
import com.actonia.entity.UserBacklink;
import com.actonia.entity.WebhookEntity;
import com.actonia.entity.ZapierErrorCodeEntity;
import com.actonia.entity.ZapierWebhookEntity;
import com.actonia.service.MainWebServiceClientService;
import com.actonia.service.ScKeywordRankService;
import com.actonia.utils.CommonUtils;
import com.actonia.utils.ContentGuardUtils;
import com.actonia.utils.CrawlerUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.HttpUtils;
import com.actonia.utils.PutMessageUtils;
import com.actonia.utils.SQSUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.PageAnalysisResult;
import com.actonia.value.object.PageLink;
import com.actonia.value.object.PoliteCrawlSummaryValueObject;
import com.actonia.value.object.ScrapyCrawlerResponse;
import com.actonia.value.object.SiteAuditCheckDomainResponse;
import com.actonia.value.object.TargetUrlChangeContentType;
import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.model.Message;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

public class SQSUtilsTest {

	private OwnDomainEntityDAO ownDomainEntityDAO;
	private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;

	private TargetUrlCrawlAdditionalContentEntityDAO targetUrlCrawlAdditionalContentEntityDAO;

	private ContentGuardUrlDAO contentGuardUrlDAO;

	private ContentGuardCrawlTrackingDAO contentGuardCrawlTrackingDAO;
	private static ContentGuardChangeTrackingDAO contentGuardChangeTrackingDAO;

	private UserBacklinkDAO userBacklinkDAO;
	private TargetUrlEntityDAO targetUrlEntityDAO;
	private AiRulesDAO aiRulesDAO;
	private ContentGuardGroupDAO contentGuardGroupDAO;
	private ContentGuardAlertDAO contentGuardAlertDAO;
	private SeoClarityKeywordEntityDAO seoClarityKeywordEntityDAO;
	private CompetitorUrlMd5EntityDAO competitorUrlMd5EntityDAO;
	private WebhookDAO webhookDAO;
	private ZapierWebhookDAO zapierWebhookDAO;
	private ZapierErrorCodeDAO zapierErrorCodeDAO;
	private CrawlRequestLogDAO crawlRequestLogDAO;
	private static final String USER_AGENT_GOOGLE_BOT = "Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)";
	private TargetUrlDailyCrawlTrackingEntityDAO targetUrlDailyCrawlTrackingEntityDAO;
	private TargetUrlChangeAlertDAO targetUrlChangeAlertDAO;
	private GroupTagEntityDAO groupTagEntityDAO;
	private CommonParamDAO commonParamDAO;
	private static final String TEST_FIFO_QUEUE_1 = "TEST_FIFO_QUEUE_1.fifo";

	public SQSUtilsTest() {
		super();
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		targetUrlCrawlAdditionalContentEntityDAO = SpringBeanFactory.getBean("targetUrlCrawlAdditionalContentEntityDAO");
		contentGuardUrlDAO = SpringBeanFactory.getBean("contentGuardUrlDAO");
		contentGuardCrawlTrackingDAO = SpringBeanFactory.getBean("contentGuardCrawlTrackingDAO");
		userBacklinkDAO = SpringBeanFactory.getBean("userBacklinkDAO");
		targetUrlEntityDAO = SpringBeanFactory.getBean("targetUrlEntityDAO");
		contentGuardChangeTrackingDAO = SpringBeanFactory.getBean("contentGuardChangeTrackingDAO");
		aiRulesDAO = SpringBeanFactory.getBean("aiRulesDAO");
		contentGuardGroupDAO = SpringBeanFactory.getBean("contentGuardGroupDAO");
		contentGuardAlertDAO = SpringBeanFactory.getBean("contentGuardAlertDAO");
		seoClarityKeywordEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordEntityDAO");
		competitorUrlMd5EntityDAO = SpringBeanFactory.getBean("competitorUrlMd5EntityDAO");
		webhookDAO = SpringBeanFactory.getBean("webhookDAO");
		zapierWebhookDAO = SpringBeanFactory.getBean("zapierWebhookDAO");
		zapierErrorCodeDAO = SpringBeanFactory.getBean("zapierErrorCodeDAO");
		crawlRequestLogDAO = SpringBeanFactory.getBean("crawlRequestLogDAO");
		targetUrlDailyCrawlTrackingEntityDAO = SpringBeanFactory.getBean("targetUrlDailyCrawlTrackingEntityDAO");
		targetUrlChangeAlertDAO = SpringBeanFactory.getBean("targetUrlChangeAlertDAO");
		groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
		commonParamDAO = SpringBeanFactory.getBean("commonParamDAO");
		ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
	}

	public static void main(String[] args) throws Exception {
		new SQSUtilsTest().runTests(args);
	}

	private void runTests(String[] args) throws Exception {

		testOwnDomainEntityDAO();

		//testGetFifoQueueMessages();

		//testDeleteFifoQueue();

		//testDeleteQueue();

		//testCreateFifoQueue();

		//testGetDateFormatDayByDomainId();

		//testGetApproximateNumberOfMessagesFromQueue();

		//testPurgeQueues();

		//testDomainSearchEngineIDLanguageID();

		//testTargetUrlHtmlClickHouseDAOGetByCrawlTimestamp();

		//testIsValidRobotTxtUrl();

		//testGetHttpGetResponseString();

		//testExtractRawHtml();

		//testExtractCrawlDate();

		// runtime parameter 1: summary/detail
		//if (args != null && args.length >= 1) {
		//	System.out.println("runtime parameter 1: summary/detail=" + args[0]);
		//	if (StringUtils.equalsIgnoreCase(args[0], "summary")) {
		//		testTargetUrlChangeSummary(args);
		//		testTargetUrlChangeSummary(args);
		//		testTargetUrlChangeSummary(args);
		//	} else if (StringUtils.equalsIgnoreCase(args[0], "detail")) {
		//		testTargetUrlChangeDetail(args);
		//		testTargetUrlChangeDetail(args);
		//		testTargetUrlChangeDetail(args);
		//	}
		//}

		//testTargetUrlChangeContentType();

		//testGetAllParamJsons();

		//testRegExp();

		//testRemoveTargetUrlQueue();
		//testCreateAlertMetricsNameInClickHouse();
		//testExtractLastSegment();
		//testCalculateMaxConcurrentCrawlThreads();
		//testCalculateDelayInSecondsPerHttpRequest();
		//testExtractDomainIdListFromString();
		//testGetAdditionalContentEntityList();
		//testRemovePageLinkFromFieldNameList();
		//testGetPoliteCrawlSummaryValueObjectList();
		//testSerializePageLink();
		//testDeserializePageLinkJson();
		//testGetHttpStatusCode();
		//testOptimizeCompetitorUrlHtml();
		//testPurgeSpecificQueues();
		//testGetTargetUrlHtmlClickHouseEntityMap();
		//testGetCompetitorUrlHtmlClickHouseEntityMap();
		//testSendTwoBatchMessages();
		//testMemoryUsageInPercent();
		//testDeserializeJsonToSiteAuditCheckDomain();
		//testGetCurrentTrackTime();
		//testRobotsTxtCrawl();
		//testContentGuardUrlDAO();
		//testTargetUrlHtmlClickHouseDAOGetChangeList();
		//testTargetUrlHtmlClickHouseDAOGetPrevious();
		//testContentGuardCrawlTrackingDAOCreate();
		//testOwnDomainEntityDAOGetByCrawlTrackingDate();
		//testLongConversion();
		//testSystemEnvironmentVariables();
		//testOptimizeTargetUrlHtml();
		//testGetDomainsByCompanyName();
		//testGetUniqueSourceUrlFromUserBacklink();
		//testGetTotalTargetUrlsByDomainId();
		//testGetDomainIdsByCompanyName();
		//testStringArrayToString();
		//testGetCriticalIndicatorList();
		//testGetUrlsWithoutCrawlData();
		//testGetGroupTagUrlList();
		//testGetDomainIdsWithTargetUrls();
		//testContentGuardGroupUrlDAO();
		//testAiRulesDAO();
		//testParseJson();
		//testContentGuardGroupDAOGet();
		//testContentGuardGroupDAOGetTotalUrls();
		//testContentGuardGroupDAOGetDomainIdsByCrawlFrequency();
		//testContentGuardUrlDAOGetListByCrawlFrequency();
		//testContentGuardUrlDAOUpdateCrawlStatus();
		//testContentGuardUrlDAOGetTotalUrlsByDomainId();
		//testContentGuardUrlDAOGetGroupUrlsMd5HashMap();
		//testSeoClarityKeywordEntityDAO();
		//testCompetitorUrlMd5EntityDAO();
		//testRankingDetailClickHouseDAO();
		//testWebhookDAO();
		//testContentGuardClickHouseDAO();
		//testZapierWebhookDAOCreate();
		//testZapierWebhookDAOGet();
		//testZapierWebhookDAODelete();
		//testZapierErrorCodeDAOGet();
		//testDeleteMessagesFromQueue();
		//testRetrieveCrawlProjectRequestDateIdList();
		//testConvertDateNumberToString();
		//testGetTrackedGroupUrlList();
		//testTargetUrlDailyCrawlTrackingEntityDAO();
		//testTargetUrlChangeAlertDAO();
		//testGroupTagEntityDAO();
		//testGetGroupTagIdUrlList();
		//testCrawlDateHour();
	}

	private void testOwnDomainEntityDAO() throws Exception {
		List<OwnDomainEntity> ownDomainEntityList = ownDomainEntityDAO.getByCrawlTrackingDate();
		for (OwnDomainEntity ownDomainEntity : ownDomainEntityList) {
			System.out.println("testOwnDomainEntityDAO() ownDomainEntity=" + ownDomainEntity.toString());
		}
	}

	private void testGetFifoQueueMessages() throws Exception {
		List<Message> messageList = null;
		String messageBody = null;
		String queueUrl = SQSUtils.getInstance().createFifoQueue(TEST_FIFO_QUEUE_1);
		boolean isContinueProcessing = true;
		do {
			messageList = SQSUtils.getInstance().getMessageFromQueue(queueUrl, 1, IConstants.SQS_MSG_TIMEOUT_IN_2000_SEC);
			if (messageList != null && messageList.size() > 0) {
				for (Message message : messageList) {
					messageBody = message.getBody();
					FormatUtils.getInstance().logMemoryUsage("testGetFifoQueueMessages() queue message=" + messageBody);
					SQSUtils.getInstance().deleteMessageFromQueue(queueUrl, message);
				}
			} else {
				FormatUtils.getInstance().logMemoryUsage("testGetFifoQueueMessages() messageList is empty.");
				isContinueProcessing = false;
				break;
			}

		} while (isContinueProcessing == true);
	}

	private void testDeleteFifoQueue() throws Exception {
		SQSUtils.getInstance().deleteFifoQueue(TEST_FIFO_QUEUE_1);
	}

	private void testDeleteQueue() throws Exception {
		String queueNamePrefix = "LINK_CLARITY_";
		String queueName = null;
		int endQueueNumber = 88;
		try {
			for (int i = 0; i < endQueueNumber; i++) {
				queueName = queueNamePrefix + (i + 1);
				System.out.println("testDeleteQueue() processing queueName=" + queueName);
				SQSUtils.getInstance().deleteQueue(queueName);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void testCreateFifoQueue() throws Exception {
		System.out.println("testCreateFifoQueue() queueName=" + TEST_FIFO_QUEUE_1);
		String queueUrl = SQSUtils.getInstance().createFifoQueue(TEST_FIFO_QUEUE_1);
		System.out.println("testCreateFifoQueue() queueUrl=" + queueUrl);
		SQSUtils.getInstance().purgeQueue(queueUrl);
		System.out.println("testCreateFifoQueue() purged queueUrl=" + queueUrl);
	}

	private void testGetDateFormatDayByDomainId() {
		int domainId = 9632;
		//int domainId = 999999;
		System.out.println("domainId=" + domainId);
		String dateFormatDay = null;
		try {
			dateFormatDay = ownDomainSettingEntityDAO.getDateFormatDayByDomainId(domainId);
		} catch (Exception e) {
			e.printStackTrace();
		}
		System.out.println("dateFormatDay=" + dateFormatDay);
	}

	private void testGetApproximateNumberOfMessagesFromQueue() {
		System.out.println("testGetApproximateNumberOfMessagesFromQueue() begins.");
		List<OwnDomainEntity> ownDomainEntityList = null;
		String queueNamePrefix = "TARGET_URL_HTML_";
		String language = null;
		int domainId = 0;
		String queueName = null;
		Integer[] numberOfMessagesArray = null;
		String domainName = null;
		try {
			//			ownDomainEntityList = ownDomainEntityDAO.queryForAll();
			//			for (OwnDomainEntity ownDomainEntity : ownDomainEntityList) {
			//				language = ownDomainEntity.getLanguage();
			//				domainId = ownDomainEntity.getId();
			//				domainName = ownDomainEntity.getDomain();
			//				queueName = queueNamePrefix + language.toUpperCase() + "_" + domainId;
			//				numberOfMessagesArray = SQSUtils.getInstance().getApproximateNumberOfMessagesAndInflight(queueName);
			//				if (numberOfMessagesArray != null && numberOfMessagesArray.length == 2) {
			//					System.out.println("domainId`" + domainId + "`domainName`" + domainName + "`queueName`" + queueName + "`messages in queue`"
			//							+ numberOfMessagesArray[0] + "`messages in flight`" + numberOfMessagesArray[1]);
			//				}
			//			}

			queueName = "TARGET_URL_HTML_EN_7135";
			numberOfMessagesArray = SQSUtils.getInstance().getApproximateNumberOfMessagesAndInflight(queueName);
			if (numberOfMessagesArray != null && numberOfMessagesArray.length == 2) {
				System.out.println("domainId`" + domainId + "`domainName`" + domainName + "`queueName`" + queueName + "`messages in queue`" + numberOfMessagesArray[0]
						+ "`messages in flight`" + numberOfMessagesArray[1]);
			}

		} catch (Exception e) {
			e.printStackTrace();
		}
		System.out.println("testGetApproximateNumberOfMessagesFromQueue() ends.");

	}

	private void testTargetUrlHtmlClickHouseDAOGetByCrawlTimestamp() throws Exception {
		int domainId = 9688;
		Date crawlTimestamp = DateUtils.parseDate("2022-09-25 04:59:56", new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS });
		List<String> databaseFields = CrawlerUtils.getInstance().getTargetUrlHtmlTableAllFieldNames();
		String TABLE_NAME = null;
		String targetUrlString = "https://www.target.com/s/toddlers+walking+shoes";
		HtmlClickHouseEntity htmlClickHouseEntity = TargetUrlHtmlClickHouseDAO.getInstance().getByCrawlTimestamp(crawlTimestamp, domainId, targetUrlString,
				databaseFields, TABLE_NAME);
		if (htmlClickHouseEntity != null) {
			System.out.println("htmlClickHouseEntity=" + new Gson().toJson(htmlClickHouseEntity, HtmlClickHouseEntity.class));
			//System.out.println("url=" + htmlClickHouseEntity.getUrl() + ",trackDate=" + htmlClickHouseEntity.getTrackDate() + ",responseCode="
			//		+ htmlClickHouseEntity.getHttpStatusCode() + ",crawlTimestamp=" + htmlClickHouseEntity.getCrawlTimestamp());

		}
	}

	private void testRemoveTargetUrlQueue() {
		//String queueName = "TEST_MAJESTIC_URL_STATUS_UPDATE_QUEUE_1";
		//String queueNamePrefix = "CRAWL_URLS_DAILY_HTML_";
		String queueNamePrefix = "CLOUD_CRAWL_URLS_DAILY_HTML_";

		String queueName = null;
		List<OwnDomainEntity> ownDomainEntityList = null;
		String language = null;
		int domainId = 0;
		try {
			ownDomainEntityList = ownDomainEntityDAO.queryForInactive();
			nextOwnDomainEntity: for (OwnDomainEntity ownDomainEntity : ownDomainEntityList) {
				domainId = ownDomainEntity.getId();
				if (2537 > domainId) {
					continue nextOwnDomainEntity;
				}
				language = ownDomainEntity.getLanguage();
				queueName = queueNamePrefix + language.toUpperCase() + "_" + domainId;
				System.out.println("processing queueName=" + queueName);
				SQSUtils.getInstance().deleteQueue(queueName);
			}
			//for (int i = 0; i < endQueueNumber; i++) {
			//	queueName = queueNamePrefix + (i + 1);
			//	System.out.println("processing queueName="+queueName);				
			//	SQSUtils.getInstance().deleteQueue(queueName);
			//}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void testPurgeQueues() {
		//String queueName = "TEST_MAJESTIC_URL_STATUS_UPDATE_QUEUE_1";
		//String queueNamePrefix = "CRAWL_ASSOCIATED_URLS_HTML_";
		//String queueNamePrefix = "TEST_COMPETITOR_URL_HTML_";
		String queueNamePrefix = "COMPETITOR_URL_HTML_";
		//String queueNamePrefix = "LINK_CLARITY_";
		//String queueNamePrefix = "888HOLDINGS_";
		String queueName = null;
		int endQueueNumber = 3168;
		String queueUrl = null;
		try {
			for (int i = 0; i < endQueueNumber; i++) {
				queueName = queueNamePrefix + (i + 1);
				queueUrl = SQSUtils.getInstance().createQueue(queueName);
				System.out.println("testPurgeQueues() processing queueName=" + queueName + ",queueUrl=" + queueUrl);
				SQSUtils.getInstance().purgeQueue(queueUrl);

				//SQSUtils.getInstance().deleteQueue(queueName);
				//System.out.println("testPurgeQueues() processing queueName=" + queueName);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void testExtractLastSegment() {
		String queueUrl = "https://sqs.us-west-2.amazonaws.com/397485469449/CRAWL_ASSOCIATED_URLS_HTML_215";
		String queueName = StringUtils.substringAfterLast(queueUrl, "/");
		System.out.println("queueUrl=" + queueUrl);
		System.out.println("queueName=" + queueName);
	}

	private void testCalculateMaxConcurrentCrawlThreads() {
		int totalCompetitorUrls = 415814;
		int maxConcurrentCrawlThreads = (totalCompetitorUrls / IConstants.COMPETITOR_WEEKLY_CRAWL_TRACKING_THRESHOLD) + 1;
		System.out.println("totalCompetitorUrls1=" + totalCompetitorUrls);
		System.out.println("maxConcurrentCrawlThreads1=" + maxConcurrentCrawlThreads);
		totalCompetitorUrls = 559205;
		maxConcurrentCrawlThreads = (totalCompetitorUrls / IConstants.COMPETITOR_WEEKLY_CRAWL_TRACKING_THRESHOLD) + 1;
		System.out.println("totalCompetitorUrls2=" + totalCompetitorUrls);
		System.out.println("maxConcurrentCrawlThreads2=" + maxConcurrentCrawlThreads);
		totalCompetitorUrls = 597880;
		maxConcurrentCrawlThreads = (totalCompetitorUrls / IConstants.COMPETITOR_WEEKLY_CRAWL_TRACKING_THRESHOLD) + 1;
		System.out.println("totalCompetitorUrls3=" + totalCompetitorUrls);
		System.out.println("maxConcurrentCrawlThreads3=" + maxConcurrentCrawlThreads);
		totalCompetitorUrls = 1381344;
		maxConcurrentCrawlThreads = (totalCompetitorUrls / IConstants.COMPETITOR_WEEKLY_CRAWL_TRACKING_THRESHOLD) + 1;
		System.out.println("totalCompetitorUrls4=" + totalCompetitorUrls);
		System.out.println("maxConcurrentCrawlThreads4=" + maxConcurrentCrawlThreads);
		totalCompetitorUrls = 1388451;
		maxConcurrentCrawlThreads = (totalCompetitorUrls / IConstants.COMPETITOR_WEEKLY_CRAWL_TRACKING_THRESHOLD) + 1;
		System.out.println("totalCompetitorUrls5=" + totalCompetitorUrls);
		System.out.println("maxConcurrentCrawlThreads5=" + maxConcurrentCrawlThreads);
		totalCompetitorUrls = 1927454;
		maxConcurrentCrawlThreads = (totalCompetitorUrls / IConstants.COMPETITOR_WEEKLY_CRAWL_TRACKING_THRESHOLD) + 1;
		System.out.println("totalCompetitorUrls6=" + totalCompetitorUrls);
		System.out.println("maxConcurrentCrawlThreads6=" + maxConcurrentCrawlThreads);
		totalCompetitorUrls = 10;
		maxConcurrentCrawlThreads = (totalCompetitorUrls / IConstants.COMPETITOR_WEEKLY_CRAWL_TRACKING_THRESHOLD) + 1;
		System.out.println("totalCompetitorUrls7=" + totalCompetitorUrls);
		System.out.println("maxConcurrentCrawlThreads7=" + maxConcurrentCrawlThreads);
	}

	private void testCalculateDelayInSecondsPerHttpRequest() {
		int totalCompetitorUrls = 415814;
		int delayInSecondsPerHttpRequest = (totalCompetitorUrls / IConstants.COMPETITOR_WEEKLY_CRAWL_TRACKING_THRESHOLD) - 1;
		System.out.println("totalCompetitorUrls1=" + totalCompetitorUrls);
		System.out.println("delayInSecondsPerHttpRequest1=" + delayInSecondsPerHttpRequest);
		totalCompetitorUrls = 559205;
		delayInSecondsPerHttpRequest = (totalCompetitorUrls / IConstants.COMPETITOR_WEEKLY_CRAWL_TRACKING_THRESHOLD) - 1;
		System.out.println("totalCompetitorUrls2=" + totalCompetitorUrls);
		System.out.println("delayInSecondsPerHttpRequest2=" + delayInSecondsPerHttpRequest);
		totalCompetitorUrls = 597880;
		delayInSecondsPerHttpRequest = (totalCompetitorUrls / IConstants.COMPETITOR_WEEKLY_CRAWL_TRACKING_THRESHOLD) - 1;
		System.out.println("totalCompetitorUrls3=" + totalCompetitorUrls);
		System.out.println("delayInSecondsPerHttpRequest3=" + delayInSecondsPerHttpRequest);
		totalCompetitorUrls = 1381344;
		delayInSecondsPerHttpRequest = (totalCompetitorUrls / IConstants.COMPETITOR_WEEKLY_CRAWL_TRACKING_THRESHOLD) - 1;
		System.out.println("totalCompetitorUrls4=" + totalCompetitorUrls);
		System.out.println("delayInSecondsPerHttpRequest4=" + delayInSecondsPerHttpRequest);
		totalCompetitorUrls = 1388451;
		delayInSecondsPerHttpRequest = (totalCompetitorUrls / IConstants.COMPETITOR_WEEKLY_CRAWL_TRACKING_THRESHOLD) - 1;
		System.out.println("totalCompetitorUrls5=" + totalCompetitorUrls);
		System.out.println("delayInSecondsPerHttpRequest5=" + delayInSecondsPerHttpRequest);
		totalCompetitorUrls = 1927454;
		delayInSecondsPerHttpRequest = (totalCompetitorUrls / IConstants.COMPETITOR_WEEKLY_CRAWL_TRACKING_THRESHOLD) - 1;
		System.out.println("totalCompetitorUrls6=" + totalCompetitorUrls);
		System.out.println("delayInSecondsPerHttpRequest6=" + delayInSecondsPerHttpRequest);
		totalCompetitorUrls = 10;
		delayInSecondsPerHttpRequest = (totalCompetitorUrls / IConstants.COMPETITOR_WEEKLY_CRAWL_TRACKING_THRESHOLD) - 1;
		System.out.println("totalCompetitorUrls7=" + totalCompetitorUrls);
		System.out.println("delayInSecondsPerHttpRequest7=" + delayInSecondsPerHttpRequest);
	}

	private void testExtractDomainIdListFromString() {
		String domainIdsString = "475,1053,1845,1846,1848,1849,1860,2481,2529,2548,2654,3431,4344,4345,4346,4348,4661,5544,5592,6101,6542,6597,6613,6620,7119,7426,7545";
		String[] domainIdStringArray = domainIdsString.split(IConstants.COMMA);
		List<Integer> domainIdList = new ArrayList<Integer>();
		for (String domainIdString : domainIdStringArray) {
			domainIdList.add(NumberUtils.toInt(domainIdString));
		}
		System.out.println("domainIdsString=" + domainIdsString);
		System.out.println("domainIdList.toString()=" + domainIdList.toString());
	}

	private void testGetAdditionalContentEntityList() {
		//int domainId = 561;
		//int domainId = 1895;
		int domainId = 1;
		List<AdditionalContentEntity> additionalContentEntityList = targetUrlCrawlAdditionalContentEntityDAO.getByDomainId(domainId);
		if (additionalContentEntityList != null && additionalContentEntityList.size() > 0) {
			for (AdditionalContentEntity additionalContentEntity : additionalContentEntityList) {
				System.out.println("additionalContentEntity=" + additionalContentEntity.toString());
			}
		} else {
			System.out.println("additionalContentEntityList is empty.");
		}
	}

	private void testGetPoliteCrawlSummaryValueObjectList() {
		int domainId = 256;
		String tableName = "dis_target_url_html_daily";
		List<PoliteCrawlSummaryValueObject> politeCrawlSummaryValueObjectList = null;
		Date todayDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
		try {
			politeCrawlSummaryValueObjectList = TargetUrlHtmlClickHouseDAO.getInstance().getDailyUpdateSummaryValueObjectList(domainId, todayDate, tableName);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void testSerializePageLink() {

		PageLink[] pageLinkArray = new PageLink[2];

		PageLink pageLink = null;

		// first object
		pageLink = new PageLink();
		pageLink.setDestination_url("destination_link_1");
		pageLinkArray[0] = pageLink;

		// second object
		pageLink = new PageLink();
		pageLink.setDestination_url("destination_link_2");
		pageLinkArray[1] = pageLink;

		Gson gson = new Gson();
		String jsonString = gson.toJson(pageLinkArray, PageLink[].class);

		System.out.println("testSerializePageLink() jsonString=" + jsonString);

		// [{"anchor_link":"anchor_link_1","destination_url":"destination_link_1"},{"anchor_link":"anchor_link_2","destination_url":"destination_link_2"}]

	}

	private void testDeserializePageLinkJson() {
		String jsonString = "[{\"anchor_link\":\"anchor_link_1\",\"destination_url\":\"destination_link_1\"},{\"anchor_link\":\"anchor_link_2\",\"destination_url\":\"destination_link_2\"}]";
		Gson gson = new Gson();
		PageLink[] pageLinkArray = gson.fromJson(jsonString, PageLink[].class);
		for (PageLink pageLink : pageLinkArray) {
			System.out.println("pageLink=" + pageLink.toString());
		}
	}

	private void testPurgeSpecificQueues() {
		AmazonSQS amazonSQS = null;
		String queueName = null;
		String queueUrl = null;
		List<String> specificQueueNamesToBePurged = new ArrayList<String>();
		specificQueueNamesToBePurged.add("TARGET_URL_HTML_DE_4763");
		specificQueueNamesToBePurged.add("TARGET_URL_HTML_DE_553");
		specificQueueNamesToBePurged.add("TARGET_URL_HTML_DE_561");
		specificQueueNamesToBePurged.add("TARGET_URL_HTML_EN_263");
		specificQueueNamesToBePurged.add("TARGET_URL_HTML_EN_4731");
		specificQueueNamesToBePurged.add("TARGET_URL_HTML_EN_4744");
		specificQueueNamesToBePurged.add("TARGET_URL_HTML_EN_4762");
		specificQueueNamesToBePurged.add("TARGET_URL_HTML_EN_550");
		specificQueueNamesToBePurged.add("TARGET_URL_HTML_EN_6101");
		specificQueueNamesToBePurged.add("TARGET_URL_HTML_EN_6504");
		specificQueueNamesToBePurged.add("TARGET_URL_HTML_EN_7426");
		specificQueueNamesToBePurged.add("TARGET_URL_HTML_EN_7529");
		specificQueueNamesToBePurged.add("TARGET_URL_HTML_EN_7545");
		specificQueueNamesToBePurged.add("TARGET_URL_HTML_FR_552");
		specificQueueNamesToBePurged.add("TARGET_URL_HTML_NL_4729");
		try {
			for (String specificQueueName : specificQueueNamesToBePurged) {
				queueName = specificQueueName;
				queueUrl = SQSUtils.getInstance().createQueue(queueName);
				System.out.println("testPurgeSpecificQueues() processing queueName=" + queueName + ",queueUrl=" + queueUrl);
				SQSUtils.getInstance().purgeQueue(queueUrl);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void testGetTargetUrlHtmlClickHouseEntityMap() throws Exception {
		String ip = null;
		int domainId = 0;
		String domainName = null;
		Map<String, HtmlClickHouseEntity> targetUrlHtmlClickHouseEntityMap = null;
		List<OwnDomainEntity> ownDomainEntityList = null;
		List<String> targetUrlHtmlFieldNames = PutMessageUtils.getInstance().getHistoricalHtmlFieldNames();
		try {
			ownDomainEntityList = ownDomainEntityDAO.queryForAll();
			if (ownDomainEntityList != null && ownDomainEntityList.size() > 0) {
				for (OwnDomainEntity ownDomainEntity : ownDomainEntityList) {
					domainId = ownDomainEntity.getId();
					domainName = ownDomainEntity.getDomain();
					targetUrlHtmlClickHouseEntityMap = PutMessageUtils.getInstance().getTargetUrlHtmlClickHouseEntityMap(ip, domainId, domainName,
							targetUrlHtmlFieldNames);
					if (targetUrlHtmlClickHouseEntityMap != null && targetUrlHtmlClickHouseEntityMap.size() > 0) {
						System.out.println("testGetTargetUrlHtmlClickHouseEntityMap() domainId=" + domainId + ",domainName=" + domainName
								+ ",targetUrlHtmlClickHouseEntityMap.size()=" + targetUrlHtmlClickHouseEntityMap.size());
						//for (String hashCode : targetUrlHtmlClickHouseEntityMap.keySet()) {
						//	System.out.println("domainId=" + domainId
						//			+",domainName="+domainName
						//			+ ",data=" + targetUrlHtmlClickHouseEntityMap.get(hashCode).toString());
						//}
					} else {
						System.out.println("testGetTargetUrlHtmlClickHouseEntityMap() domainId=" + domainId + ",domainName=" + domainName
								+ ",targetUrlHtmlClickHouseEntityMap is empty.");
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void testGetCompetitorUrlHtmlClickHouseEntityMap() {
		String ip = null;
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = null;
		List<String> reversedUrlDomainList = null;
		List<String> databaseFields = null;
		boolean filterByResponseCode = true;
		try {
			reversedUrlDomainList = new ArrayList<String>();
			reversedUrlDomainList.add("com.debbiemeyer.www");

			databaseFields = new ArrayList<String>();
			databaseFields.add(IConstants.URL);
			databaseFields.add(IConstants.RESPONSE_CODE);
			databaseFields.add(IConstants.TITLE);

			//htmlClickHouseEntityList = CompetitorUrlHtmlClickHouseDAO.getInstance().getUniqueUrlDomains(limit);
			htmlClickHouseEntityList = CompetitorUrlHtmlClickHouseDAO.getInstance().getLatestFromHistorical(ip, reversedUrlDomainList, databaseFields, null,
					filterByResponseCode);
			if (htmlClickHouseEntityList != null && htmlClickHouseEntityList.size() > 0) {
				for (HtmlClickHouseEntity htmlClickHouseEntity : htmlClickHouseEntityList) {
					System.out.println("testGetCompetitorUrlHtmlClickHouseEntityMap() url=" + htmlClickHouseEntity.getUrl() + ",responseCode="
							+ htmlClickHouseEntity.getCrawlerResponse().getResponse_code() + ",title=" + htmlClickHouseEntity.getCrawlerResponse().getTitle());
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void testSendTwoBatchMessages() {

		// put all source URL strings in queue
		Map<String, String> messages = new HashMap<String, String>();
		messages.put("key1", "value1");
		messages.put("key2", "value2");
		messages.put("key3", "value3");
		messages.put("key4", "value4");
		messages.put("key5", "value5");
		messages.put("key6", "value6");
		messages.put("key7", "value7");
		messages.put("key8", "value8");
		messages.put("key9", "value9");
		messages.put("key10", "value10");

		Map<String, String> messages1 = new HashMap<String, String>();
		Map<String, String> messages2 = new HashMap<String, String>();

		Set<String> keySet = messages.keySet();
		List<String> keyList = new ArrayList<String>(keySet);
		int halfListSize = keyList.size() / 2;
		String key = null;
		String value = null;

		messages1 = new HashMap<String, String>();
		for (int i = 0; i < halfListSize; i++) {
			key = keyList.get(i);
			value = messages.get(key);
			messages1.put(key, value);
		}
		for (String testKey : messages1.keySet()) {
			System.out.println("messages1 key=" + testKey + ",value=" + messages1.get(testKey));
		}

		messages2 = new HashMap<String, String>();
		for (int i = halfListSize; i < messages.size(); i++) {
			key = keyList.get(i);
			value = messages.get(key);
			messages2.put(key, value);
		}
		for (String testKey : messages2.keySet()) {
			System.out.println("messages2 key=" + testKey + ",value=" + messages2.get(testKey));
		}
	}

	private void testMemoryUsageInPercent() {
		Integer DECIMAL_PRECISION = 4;
		long maxMemoryLong = 0L;
		long totalMemoryLong = 0L;
		//maxMemoryLong = Runtime.getRuntime().maxMemory();
		//totalMemoryLong = Runtime.getRuntime().totalMemory();
		maxMemoryLong = 35418800128L;
		//totalMemoryLong = 4251451392L;
		totalMemoryLong = 33872881664L;
		System.out.println("maxMemoryLong=" + maxMemoryLong);
		System.out.println("totalMemoryLong=" + totalMemoryLong);
		BigDecimal totalMemory = new BigDecimal(totalMemoryLong).setScale(0, RoundingMode.HALF_UP);
		BigDecimal maxMemory = new BigDecimal(maxMemoryLong).setScale(0, RoundingMode.HALF_UP);
		BigDecimal memoryUsageInPercent = totalMemory.divide(maxMemory, DECIMAL_PRECISION, RoundingMode.HALF_UP).multiply(new BigDecimal(100.00));
		System.out.println("memoryUsageInPercent=" + memoryUsageInPercent);
	}

	private void testDeserializeJsonToSiteAuditCheckDomain() {

		// all elements available
		try {
			File inputFile = new File("C://dev//eclipse_workspace_svn_1.6//polite-crawl-put-messages//src//main//resources//json//site_audit_check_domain.json");
			List<String> testStringList = FileUtils.readLines(inputFile, IConstants.UTF_8);
			String json = testStringList.get(0);
			System.out.println("json1=" + json);
			Gson gson = new Gson();
			SiteAuditCheckDomainResponse siteAuditCheckDomainResponse = gson.fromJson(json, SiteAuditCheckDomainResponse.class);
			if (siteAuditCheckDomainResponse != null) {
				System.out.println("deserialzied siteAuditCheckDomainResponse=" + siteAuditCheckDomainResponse.toString());
			} else {
				System.out.println("error--siteAuditCheckDomainResponse is null.");
			}
			//System.out.println("crawlerResponse.getPage_link().toString()="+crawlerResponse.getPage_link().toString());
			String testString = new Gson().toJson(siteAuditCheckDomainResponse, SiteAuditCheckDomainResponse.class);
			System.out.println("serialized siteAuditCheckDomainResponse=" + testString);

		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void testGetCurrentTrackTime() {
		//yyyy-MM-dd HH:mm:ss
		//01234567890
		String trackDateTime = DateFormatUtils.format(new Date(), IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
		String trackTime = StringUtils.substring(trackDateTime, 11);
		System.out.println("trackDateTime=" + trackDateTime);
		System.out.println("trackTime=" + trackTime);
	}

	private void testRobotsTxtCrawl() throws Exception {
		//String domainName = "www.seoclarity.net";
		//String domainName = "www.bradfordexchangechecks.com";
		//String domainName = "www.overstock.com";
		//String domainName = "www.amazon.com.br";
		//String domainName = "www.goodreads.com";
		String domainName = "www.888poker.se";
		String ip = "1";
		MainWebServiceClientService mainWebServiceClientService = SpringBeanFactory.getBean("mainWebServiceClientService");
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_FIND_SITE_AUDIT_CHECK_DOMAIN
				+ IConstants.QUESTION_MARK + MainWebServiceClientService.QUERY_PARM_URL + domainName;
		System.out.println("process() requestUrl=" + requestUrl);
		SiteAuditCheckDomainResponse siteAuditCheckDomainResponse = mainWebServiceClientService.siteAuditCheckDomain(ip, requestUrl);
	}

	private void testContentGuardUrlDAO() {
		int domainId = 1701;
		//		List<ContentGuardUrlEntity> contentGuardUrlEntityList = contentGuardUrlDAO.getList(domainId);
		//		if (contentGuardUrlEntityList != null && contentGuardUrlEntityList.size() > 0) {
		//			for (ContentGuardUrlEntity contentGuardUrlDAO : contentGuardUrlEntityList) {
		//				System.out.println("testContentGuardUrlDAO() contentGuardUrlDAO=" + contentGuardUrlDAO.toString());
		//			}
		//		} else {
		//			System.out.println("testContentGuardUrlDAO() contentGuardUrlEntityList is empty.");
		//		}

		List<ContentGuardUrlEntity> contentGuardUrlEntityList = contentGuardUrlDAO.getListByCrawlFrequency(IConstants.FREQUENCY_TYPE_DAILY, domainId, null);
		if (contentGuardUrlEntityList != null && contentGuardUrlEntityList.size() > 0) {
			for (ContentGuardUrlEntity contentGuardUrlDAO : contentGuardUrlEntityList) {
				System.out.println("urlString=" + contentGuardUrlDAO.getUrl());
			}
		} else {
			System.out.println("urlList is empty.");

		}

	}

	private void testTargetUrlHtmlClickHouseDAOGetChangeList() throws Exception {
		int domainId = 256;
		Date trackDate = DateUtils.parseDate("2020-05-15", new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
		List<String> databaseFields = new ArrayList<String>();
		databaseFields.add(IConstants.URL);
		databaseFields.add(IConstants.TRACK_DATE);
		databaseFields.add(IConstants.RESPONSE_CODE);
		databaseFields.add(IConstants.CRAWL_TIMESTAMP);
		databaseFields.add(IConstants.RESPONSE_CODE_CHG_IND);
		databaseFields.add(IConstants.TITLE_CHG_IND);
		databaseFields.add(IConstants.DESCRIPTION_CHG_IND);
		databaseFields.add(IConstants.H1_CHG_IND);
		databaseFields.add(IConstants.H2_CHG_IND);
		databaseFields.add(IConstants.ROBOTS_CONTENTS_CHG_IND);
		databaseFields.add(IConstants.CANONICAL_CHG_IND);
		databaseFields.add(IConstants.CUSTOM_DATA_CHG_IND);
		String TABLE_NAME = null;
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = TargetUrlHtmlClickHouseDAO.getInstance().getChangeList(domainId, trackDate, databaseFields, TABLE_NAME);
		if (htmlClickHouseEntityList != null && htmlClickHouseEntityList.size() > 0) {
			for (HtmlClickHouseEntity htmlClickHouseEntity : htmlClickHouseEntityList) {
				System.out.println("url=" + htmlClickHouseEntity.getUrl() + ",trackDate=" + htmlClickHouseEntity.getTrackDate() + ",responseCode="
						+ htmlClickHouseEntity.getHttpStatusCode() + ",crawlTimestamp=" + htmlClickHouseEntity.getCrawlTimestamp());
			}
		}
	}

	private void testTargetUrlHtmlClickHouseDAOGetPrevious() throws Exception {
		HtmlClickHouseEntity htmlClickHouseEntity = null;
		int domainId = 256;
		Date trackDate = DateUtils.parseDate("2020-05-15", new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
		List<String> databaseFields = new ArrayList<String>();
		databaseFields.add(IConstants.URL);
		databaseFields.add(IConstants.TRACK_DATE);
		databaseFields.add(IConstants.RESPONSE_CODE);
		databaseFields.add(IConstants.CRAWL_TIMESTAMP);
		String TABLE_NAME = null;
		String[] targetUrlArray = getTargetUrls();
		for (String targetUrlString : targetUrlArray) {
			htmlClickHouseEntity = TargetUrlHtmlClickHouseDAO.getInstance().getPrevious(null, null, domainId, targetUrlString, databaseFields, TABLE_NAME, trackDate,
					null);
			if (htmlClickHouseEntity != null) {
				System.out.println("url=" + htmlClickHouseEntity.getUrl() + ",trackDate=" + htmlClickHouseEntity.getTrackDate() + ",responseCode="
						+ htmlClickHouseEntity.getHttpStatusCode() + ",crawlTimestamp=" + htmlClickHouseEntity.getCrawlTimestamp());
			}

		}
	}

	private String[] getTargetUrls() {
		return new String[] { "https://www.seoclarity.net/resources/news/new-features-improve-voice-contextual-search-performance-16714/?hs_amp=true",
				"https://www.seoclarity.net/blog/seo-strategies", "https://www.seoclarity.net/blog/google-algorithm-update-on-02242011-part-1-806/?hs_amp=true",
				"https://www.seoclarity.net/resources/news/new-local-clarity-platform-16870/",
				"https://www.seoclarity.net/resources/news/seoclarity-selects-neustars-localeze-enhance-local-search-capabilities-10694/?hs_amp=true",
				"https://www.seoclarity.net/resources/knowledgebase/google-sitelinks-remove-update-17277/?hs_amp=true",
				"https://www.seoclarity.net/blog/holiday-seo-planning-learning-last-years-success-11012/?hs_amp=true",
				"https://www.seoclarity.net/blog/keyword-ranking-fluctuations", "https://www.seoclarity.net/blog/understanding-robots-txt",
				"https://www.seoclarity.net/resources/knowledgebase/archive/2017/10",
				"https://www.seoclarity.net/resources/news/new-local-clarity-platform-16870/?hs_amp=true",
				"https://www.seoclarity.net/blog/cheat-sheet-internal-link-analysis?hs_amp=true", "https://www.seoclarity.net/blog/clickstream-data",
				"https://www.seoclarity.net/blog/travel-seo?hs_amp=true",
				"https://www.seoclarity.net/resources/news/voice-search-optimization-brands-opportunity-16891/", "https://www.seoclarity.net/see-it-in-action/",
				"https://www.seoclarity.net/resources/news/seoclarity-launches-patent-pending-traffic-clarity-in-response-to-term-not-provided-at-2013-smx-east-conference-in-new-york-city-2-8212/",
				"https://www.seoclarity.net/blog/seoclarity-innovations-2019", "https://www.seoclarity.net/resources/knowledgebase/page/2",
				"https://www.seoclarity.net/blog/travel-seo", "https://www.seoclarity.net/blog/visibility-share-workflow",
				"https://www.seoclarity.net/webinar-practical-applications-insights-voice-search-17229/",
				"https://www.seoclarity.net/blog/why-choose-an-seo-platform-over-semrush", "https://www.seoclarity.net/blog/seo-platform-birthday",
				"https://www.seoclarity.net/blog/seo-segmentation-to-scale-for-seo-success", "https://www.seoclarity.net/blog/travel-seo-challenges",
				"https://www.seoclarity.net/resources/knowledgebase/tag/global-seo", "https://www.seoclarity.net/blog/seoclarity-security",
				"https://www.seoclarity.net/resources/knowledgebase/author/andy-piper",
				"https://www.seoclarity.net/resources/news/seo-industrys-first-chief-seo-evangelist-appointed-11620/",
				"https://www.seoclarity.net/seoclarity-imi-hosted-webinar-huge-success-parts-2-3-come-10895/",
				"https://www.seoclarity.net/webinar-recap-rankbrain-ai-and-seo-13273/",
				"https://www.seoclarity.net/blog/how-content-fusion-helps-create-more-relevant-content", "https://www.seoclarity.net/professional-services/",
				"https://www.seoclarity.net/blog/googles-mobile-first-search-index-the-latest-updates-17046/",
				"https://www.seoclarity.net/resources/news/voice-search-optimization-brands-opportunity-16891/?hs_amp=true",
				"https://www.seoclarity.net/blog/seo-content-writing?hs_amp=true", "https://www.seoclarity.net/resources/knowledgebase/archive/2017/07",
				"https://www.seoclarity.net/resources/news/seo-industrys-first-chief-seo-evangelist-appointed-11620/?hs_amp=true",
				"https://www.seoclarity.net/blog/seo-content-writing", "https://www.seoclarity.net/webinar-3-seo-secrets-successful-content-strategy-17478/",
				"https://www.seoclarity.net/blog/seo-rank-report-what-to-include", "https://www.seoclarity.net/blog/cheat-sheet-internal-link-analysis", };
	}

	private void testContentGuardCrawlTrackingDAOCreate() {
		int domainId = 1701;
		String domainName = "testDomain";
		ContentGuardCrawlTrackingEntity contentGuardCrawlTrackingEntity = null;
		List<ContentGuardCrawlTrackingEntity> contentGuardCrawlTrackingEntityList = new ArrayList<ContentGuardCrawlTrackingEntity>();

		// first record
		contentGuardCrawlTrackingEntity = new ContentGuardCrawlTrackingEntity();
		contentGuardCrawlTrackingEntity.setCrawlTimestamp(new Date());
		contentGuardCrawlTrackingEntity.setDomainId(domainId);
		contentGuardCrawlTrackingEntity.setDomainName(domainName);
		contentGuardCrawlTrackingEntity.setProcessType(IConstants.PROCESS_TYPE_NUMBER_CLICKHOUSE);
		contentGuardCrawlTrackingEntity.setTotalUrls(168);
		contentGuardCrawlTrackingEntityList.add(contentGuardCrawlTrackingEntity);

		// second record
		contentGuardCrawlTrackingEntity = new ContentGuardCrawlTrackingEntity();
		contentGuardCrawlTrackingEntity.setCrawlTimestamp(new Date());
		contentGuardCrawlTrackingEntity.setDomainId(168);
		contentGuardCrawlTrackingEntity.setDomainName("testDomain2");
		contentGuardCrawlTrackingEntity.setProcessType(IConstants.PROCESS_TYPE_NUMBER_CLICKHOUSE);
		contentGuardCrawlTrackingEntity.setTotalUrls(368);
		contentGuardCrawlTrackingEntityList.add(contentGuardCrawlTrackingEntity);

		contentGuardCrawlTrackingDAO.insertMultiRowsBatch(contentGuardCrawlTrackingEntityList);

	}

	private void testOwnDomainEntityDAOGetByCrawlTrackingDate() {
		List<OwnDomainEntity> ownDomainEntityList = ownDomainEntityDAO.getByCrawlTrackingDate();
		for (OwnDomainEntity ownDomainEntity : ownDomainEntityList) {
			System.out.println("domainId=" + ownDomainEntity.getId() + ",domainName=" + ownDomainEntity.getDomain());
		}

	}

	private void testLongConversion() {
		String inputString = "15416610004542656267";
		System.out.println("inputString=" + inputString);
		Long testLong = new Long(inputString);
		System.out.println("testLong=" + testLong);
	}

	private void testExtractRawHtml() throws Exception {
		ScrapyCrawlerResponse scrapyCrawlerResponse = null;
		HtmlClickHouseEntity htmlClickHouseEntity = null;
		PageAnalysisResult[] pageAnalysisResultArray = null;
		Map<String, String> pageCrawlerApiRequestHeaders = null;
		boolean isJavascriptCrawler = false;
		String ip = "1";
		String queueName = "testQueueName";
		String urlString = null;
		String userAgent = null;
		boolean isStoreHtml = false;
		boolean isResponseAsHtml = true;
		File file = null;

		//String[] testUrls = getTestUrls();
		//String[] testPageNames = getTestPageNames();
		//String[] testUserAgents = getTestUserAgents();

		//String[] testUrls = new String[] {"https://www.actonia.com/privacy-policy/" };
		//String[] testUrls = new String[] {"https://test.edgeseo.dev/ai_rule_38.html" };		
		//String[] testUrls = new String[] { "https://test.edgeseo.dev/ai_rule_39.html" };
		//String[] testUrls = new String[] {"https://test.edgeseo.dev/schema_validation.html" };
		//String[] testUrls = new String[] {"https://test.edgeseo.dev/gtm_1.html" };		
		//String[] testUrls = new String[] { "https://test.edgeseo.dev/gtm_2.html" };		
		//String[] testUrls = new String[] { "https://www.on-running.com/es-us/products/cloudsurfer-glow-glacier-w" };
		//String[] testUrls = new String[] { "https://www.booking.com/city/us/london.html" };
		//String[] testUrls = new String[] { "https://9632-so.clrt.ai/https://www.seoclarity.net/mobile-desktop-ctr-study-11302/" };
		//String[] testUrls = new String[] { "https://www.seoclarity.net/mobile-desktop-ctr-study-11302/" };
		String[] testUrls = new String[] { "https://www.seoclarity.net/robots.txt" };

		String[] testPageNames = new String[] { "www.seoclarity.net.robots.txt" };
		//String[] testPageNames = new String[] { "test_original.html" };
		//String[] testUserAgents = new String[] {"default"};
		Integer javascriptTimeoutInSecond = null;

		for (int i = 0; i < testUrls.length; i++) {

			urlString = testUrls[i];

			file = new File("/home/<USER>/source/test_polite_crawl_put_messages/output/" + testPageNames[i]);

			userAgent = USER_AGENT_GOOGLE_BOT;
			//userAgent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 (compatible; politeCrawl.v2.0)";
			//userAgent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 (compatible; Bing)";
			//if (StringUtils.equalsIgnoreCase(testUserAgents[i], "default")) {
			//	userAgent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 (compatible; politeCrawl.v2.0)";
			//} else {
			//	userAgent = testUserAgents[i];
			//}
			isJavascriptCrawler = false;

			System.out.println("testExtractRawHtml() urlString=" + urlString);
			System.out.println("testExtractRawHtml() userAgent=" + userAgent);
			System.out.println("testExtractRawHtml() isJavascriptCrawler=" + isJavascriptCrawler);

			// to test formatted response
			scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(ip, queueName, urlString, userAgent, null, isJavascriptCrawler,
					javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null, isResponseAsHtml, null);
			System.out.println("scrapyCrawlerResponse.getStatus()=" + scrapyCrawlerResponse.getStatus());
			FileUtils.writeStringToFile(file, scrapyCrawlerResponse.getHtml(), StandardCharsets.UTF_8);
			System.out.println("html ends");

			Date currentTrackDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
			if (scrapyCrawlerResponse != null) {
				FormatUtils.getInstance()
						.logMemoryUsage("testExtractRawHtml() scrapyCrawlerResponse.getExceptionMessage()=" + scrapyCrawlerResponse.getExceptionMessage());
				if (scrapyCrawlerResponse.getCrawlerResponse() != null) {
					System.out.println("testExtractRawHtml() response_code=" + scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
					htmlClickHouseEntity = new HtmlClickHouseEntity();
					htmlClickHouseEntity.setDomainId(0);
					htmlClickHouseEntity.setUrl(urlString);
					htmlClickHouseEntity.setTrackDate(currentTrackDate);
					htmlClickHouseEntity.setCrawlTimestamp(new Date());
					htmlClickHouseEntity.setCrawlerResponse(scrapyCrawlerResponse.getCrawlerResponse());
					htmlClickHouseEntity.setSign(IConstants.CLICKHOUSE_SIGN_POSITIVE_1);
					if (htmlClickHouseEntity.getHttpStatusCode() != null) {
						if (htmlClickHouseEntity.getHttpStatusCode().intValue() == 200) {
							// aggregate page analysis results
							pageAnalysisResultArray = CrawlerUtils.getInstance().getPageAnalysisResultArray(scrapyCrawlerResponse.getCrawlerResponse());
							htmlClickHouseEntity.setPageAnalysisResultArray(pageAnalysisResultArray);
							//System.out.println("htmlClickHouseEntity=" + new Gson().toJson(htmlClickHouseEntity));
						}
						htmlClickHouseEntity.setWeekOfYear(CommonUtils.calculateWeekOfYear(currentTrackDate));
					} else {
						System.out.println("invokePageCrawlerApi() error--ip=" + ip + ",queueName=" + queueName + ",urlString=" + urlString
								+ ",htmlClickHouseEntity.getHttpStatusCode() is null.");
					}
				} else {
				}
			}

		}

	}

	//	private String[] getTestUrls() {
	//		return new String[] { "https://www.walgreens.com/store/c/natrol-5-htp-100-mg-double-strength-capsules/ID=prod1469357-product", "https://baito.mynavi.jp/tokyo",
	//				"https://www.agoda.com/th-th/account/signin.html",
	//				"https://www.apple.com/us_eduind-highered_805121/shop/buy-iphone/iphone-xr/6.1-inch-display-64gb-coral-verizon",
	//				"https://www.amazon.it/gp/adlp/starwars", "https://www.ross-simons.com/jewelry/bracelets/",
	//				"https://www.balsamhill.com/c/pre-lit-artificial-christmas-trees-with-clear-lights",
	//				"https://www.hsn.com/shop/concierge-collection-green-blankets-and-throws/ho0015-1451-5340",
	//				"https://business.linkedin.com/zh-cn/talent-solutions/customer-supporting-center/tutorial",
	//				"https://www.retailmenot.com/blog/general-tips-for-online-shopping.html", "https://www.linkedin.com/showcase/bmwfrance",
	//				"https://quickbooks.intuit.com/small-business/coronavirus/resources/where-get-sba-loan/", "https://www.retailmenot.com/coupons/detergent",
	//				"https://www.balsamhill.com/c/artificial-christmas-trees-900-to-1200-dollars", "https://slack.com/", "https://www.retailmenot.com/coupons/osprey",
	//				"https://www.ftmyersjaguar.com/blog/", "https://www.retailmenot.com/blog/celebrity-clothing-lines.html", "https://m.betsson.com/de/casino",
	//				"https://www.bradfordexchange.com/mcategory/gifts_121/wizard-of-oz.html",
	//				"https://quickbooks.intuit.com/learn-support/en-us/account-management/how-do-i-find-an-accountant/00/190127", "https://www.betsson.com/pe/casino",
	//				"https://www.honeybutter.com/press", "https://de.888casino.com", "https://www.888poker.com/magazine/strategy/omaha-poker/starting-hands-in-omaha",
	//				"https://help.quickbooks.intuit.com/en_GB/contact", "https://www.niveausa.com/advice/lifestyle",
	//				"https://www.amazon.es/soportes-radiocomunicacion/b?ie=UTF8&node=*********", "https://www.autotrader.com/car-news/mercedes+benz-gle-class.xhtml",
	//				"https://www.abebooks.de/buch-suchen/titel/kursbuch-gesundheit/autor/corazza/sortby/3/", "https://www.autotrader.com/car-news/mercedes+benz-c350.xhtml",
	//				"https://www.balsamhill.com/c/christmas-ornament-sets",
	//				"https://www.lowes.ca/dept/kitchen-brushes-cleaning-brushes-cleaning-tools-cleaning-supplies-storage-solutions-a3281", "http://www.888.es",
	//				"https://www.retailmenot.com/blog/penny-loafers-are-back.html", "https://www.abebooks.com/book-search/title/il-primo-conflitto-tra-napoleone-e-s-sede/",
	//				"https://www.cmegroup.com/trading/energy/crude-oil/light-sweet-crude_quotes_globex_options.html?optionExpiration=M6", "https://www.cmegroup.com/",
	//				"https://www.cmegroup.com/trading/agricultural/", "https://www.abebooks.co.uk/products/isbn/*************",
	//				"https://www.abebooks.it/products/isbn/*************", "https://www.retailmenot.com/view/adidas.co.uk",
	//				"https://www.abebooks.co.uk/products/isbn/*************", "https://squareup.com/help/us/en/article/3896-link-and-edit-your-bank-account",
	//				"https://www.bestbuy.ca/en-ca/category/samsung-cases/492099", "https://www.ebags.com/product/adidas/prime-v-laptop-backpack/377685",
	//				"https://realestate.ha.com/formal-appraisals.s", "https://sp.comics.mecha.cc/feature/h_kageki",
	//				"https://fineart.ha.com/artist-index/berenice-abbott.s?id=*********",
	//				"https://www.balsamhill.com/p/late-autumn-artificial-wreath-garland-foliage?sku=4001596", "https://mynavi-agent.jp/it/",
	//				"https://www.webmd.com/heart-disease/tetralogy-fallot", "https://www.iberlibro.com/products/isbn/*************",
	//				"https://furu-po.com/goods_detail.php?id=457840", "https://www.sparefoot.com/boat-storage.html", "https://www.cvs.com/shop/content/holiday",
	//				"https://www.hsn.com/shop/chaco-canyon-southwest-jewelry-gemstone-rings/j00129-6092-18546",
	//				"https://www.hotwire.com/Seligman-Hotels.d6056023.Travel-Guide-Hotels", "https://www.iberlibro.com/colecciones/materias/cine-televisi%C3%B3n",
	//				"https://www.zvab.com/products/isbn/9783060313969", "https://store.nolo.com/products/the-california-landlords-law-book-lbev.html",
	//				"https://www.apple.com/shop/ipad/ipad-accessories/displays-stands", "https://www.retailmenot.com/coupons/michaelkors",
	//				"https://blog.racebets.com/recent-grand-national-winners", };
	//	}

	private String[] getTestUrls() {
		return new String[] { "https://test.edgeseo.dev/ai_rule_38.html", "https://test.edgeseo.dev/ai_rule_39.html", "https://test.edgeseo.dev/ai_rule_40.html",
				"https://test.edgeseo.dev/ai_rule_41.html", "https://test.edgeseo.dev/ai_rule_42.html", "https://test.edgeseo.dev/ai_rule_43.html",
				"https://test.edgeseo.dev/ai_rule_44.html", "https://test.edgeseo.dev/ai_rule_45.html", "https://test.edgeseo.dev/ai_rule_46.html",
				"https://test.edgeseo.dev/ai_rule_47.html", "https://test.edgeseo.dev/ai_rule_48.html", "https://test.edgeseo.dev/ai_rule_49.html",
				"https://test.edgeseo.dev/ai_rule_50.html", "https://test.edgeseo.dev/ai_rule_51.html", "https://test.edgeseo.dev/ai_rule_52.html",
				"https://test.edgeseo.dev/ai_rule_53.html", "https://test.edgeseo.dev/ai_rule_54.html", "https://test.edgeseo.dev/ai_rule_58.html",
				"https://test.edgeseo.dev/ai_rule_59.html", "https://test.edgeseo.dev/ai_rule_60.html", "https://test.edgeseo.dev/ai_rule_61.html",
				"https://test.edgeseo.dev/ai_rule_62.html", "https://test.edgeseo.dev/ai_rule_65.html", "https://test.edgeseo.dev/ai_rule_67.html",
				"https://test.edgeseo.dev/ai_rule_68.html", "https://test.edgeseo.dev/ai_rule_69.html", "https://test.edgeseo.dev/ai_rule_70.html",
				"https://test.edgeseo.dev/ai_rule_71.html", "https://test.edgeseo.dev/ai_rule_72.html", "https://test.edgeseo.dev/ai_rule_73.html",
				"https://test.edgeseo.dev/ai_rule_75.html", "https://test.edgeseo.dev/ai_rule_76.html", "https://test.edgeseo.dev/ai_rule_77.html",
				"https://test.edgeseo.dev/ai_rule_78.html", "https://test.edgeseo.dev/ai_rule_79.html", "https://test.edgeseo.dev/ai_rule_80.html",
				"https://test.edgeseo.dev/ai_rule_81.html", "https://test.edgeseo.dev/ai_rule_82.html", "https://test.edgeseo.dev/ai_rule_83.html",
				"https://test.edgeseo.dev/ai_rule_84.html", "https://test.edgeseo.dev/ai_rule_86.html", "https://test.edgeseo.dev/ai_rule_87.html",
				"https://test.edgeseo.dev/ai_rule_88.html", "https://test.edgeseo.dev/ai_rule_89.html", "https://test.edgeseo.dev/ai_rule_90.html",
				"https://test.edgeseo.dev/ai_rule_91.html", "https://test.edgeseo.dev/ai_rule_92.html", "https://test.edgeseo.dev/ai_rule_93.html",
				"https://test.edgeseo.dev/ai_rule_94.html", "https://test.edgeseo.dev/ai_rule_95.html", "https://test.edgeseo.dev/ai_rule_96.html",
				"https://test.edgeseo.dev/ai_rule_97.html", "https://test.edgeseo.dev/ai_rule_98.html", "https://test.edgeseo.dev/ai_rule_99.html",
				"https://test.edgeseo.dev/ai_rule_100.html", "https://test.edgeseo.dev/ai_rule_101.html", "https://test.edgeseo.dev/ai_rule_102.html",
				"https://test.edgeseo.dev/ai_rule_103.html", "https://test.edgeseo.dev/ai_rule_104.html", "https://test.edgeseo.dev/ai_rule_105.html",
				"https://test.edgeseo.dev/ai_rule_106.html", "https://test.edgeseo.dev/ai_rule_107.html", "https://test.edgeseo.dev/ai_rule_108.html", };
	}

	private String[] getTestPageNames() {
		return new String[] { "ai_rule_38_af.html", "ai_rule_39_af.html", "ai_rule_40_af.html", "ai_rule_41_af.html", "ai_rule_42_af.html", "ai_rule_43_af.html",
				"ai_rule_44_af.html", "ai_rule_45_af.html", "ai_rule_46_af.html", "ai_rule_47_af.html", "ai_rule_48_af.html", "ai_rule_49_af.html",
				"ai_rule_50_af.html", "ai_rule_51_af.html", "ai_rule_52_af.html", "ai_rule_53_af.html", "ai_rule_54_af.html", "ai_rule_58_af.html",
				"ai_rule_59_af.html", "ai_rule_60_af.html", "ai_rule_61_af.html", "ai_rule_62_af.html", "ai_rule_65_af.html", "ai_rule_67_af.html",
				"ai_rule_68_af.html", "ai_rule_69_af.html", "ai_rule_70_af.html", "ai_rule_71_af.html", "ai_rule_72_af.html", "ai_rule_73_af.html",
				"ai_rule_75_af.html", "ai_rule_76_af.html", "ai_rule_77_af.html", "ai_rule_78_af.html", "ai_rule_79_af.html", "ai_rule_80_af.html",
				"ai_rule_81_af.html", "ai_rule_82_af.html", "ai_rule_83_af.html", "ai_rule_84_af.html", "ai_rule_86_af.html", "ai_rule_87_af.html",
				"ai_rule_88_af.html", "ai_rule_89_af.html", "ai_rule_90_af.html", "ai_rule_91_af.html", "ai_rule_92_af.html", "ai_rule_93_af.html",
				"ai_rule_94_af.html", "ai_rule_95_af.html", "ai_rule_96_af.html", "ai_rule_97_af.html", "ai_rule_98_af.html", "ai_rule_99_af.html",
				"ai_rule_100_af.html", "ai_rule_101_af.html", "ai_rule_102_af.html", "ai_rule_103_af.html", "ai_rule_104_af.html", "ai_rule_105_af.html",
				"ai_rule_106_af.html", "ai_rule_107_af.html", "ai_rule_108_af.html", };
	}

	private String[] getTestUserAgents() {
		return new String[] { "default", "default", "default", "default", "default", "default", "default", "default", "default",
				"6W3vXZkRLJEQK9v9yBmkwAD6WEHH2S49wd8b3YncUVHpZX5W5r", "ClarityBot", "default", "6W3vXZkRLJEQK9v9yBmkwAD6WEHH2S49wd8b3YncUVHpZX5W5r", "default",
				"default", "6W3vXZkRLJEQK9v9yBmkwAD6WEHH2S49wd8b3YncUVHpZX5W5r", "default", "6W3vXZkRLJEQK9v9yBmkwAD6WEHH2S49wd8b3YncUVHpZX5W5r", "default", "default",
				"default", "default", "default", "default", "default", "default", "default", "default", "ClarityBot", "default", "ClarityBot", "default", "default",
				"default", "6W3vXZkRLJEQK9v9yBmkwAD6WEHH2S49wd8b3YncUVHpZX5W5r", "default", "default", "default", "default", "default", "default",
				"6W3vXZkRLJEQK9v9yBmkwAD6WEHH2S49wd8b3YncUVHpZX5W5r", "default", "default", "default", "default", "default", "default", "default", "default", "default",
				"default", "default", "default", "default", "default", "default", "default", "default", "default", "default", "default",
				"6W3vXZkRLJEQK9v9yBmkwAD6WEHH2S49wd8b3YncUVHpZX5W5r", "default", };
	}

	// optimize the 'local_target_url_html' table on all servers in the cluster
	private void testOptimizeTargetUrlHtml() throws Exception {
		System.out.println("testOptimizeTargetUrlHtml() begins.");
		String LOCAL_TARGET_URL_HTML_TABLE_NAME = null;
		Set<Integer> processYearMonthSet = new HashSet<Integer>();
		processYearMonthSet.add(202006);
		long startTimestamp = System.currentTimeMillis();
		if (processYearMonthSet != null && processYearMonthSet.size() > 0) {
			for (Integer processYearMonth : processYearMonthSet) {
				System.out.println("testOptimizeTargetUrlHtml() optimizing partition=" + processYearMonth);
				LocalTargetUrlHtmlClickHouseDAO.getInstance().optimizeOnePartition(processYearMonth, LOCAL_TARGET_URL_HTML_TABLE_NAME);
			}
		} else {
			System.out.println("testOptimizeTargetUrlHtml() processYearMonthSet is empty.");
		}
		System.out.println("testOptimizeTargetUrlHtml() ends. total elapsed (s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private void testDomainSearchEngineIDLanguageID() {
		int searchEngineId = 0;
		int searchLanguageId = 0;
		List<OwnDomainEntity> ownDomainEntityList = ownDomainEntityDAO.queryForAll();
		for (OwnDomainEntity ownDomainEntity : ownDomainEntityList) {
			searchEngineId = ScKeywordRankService.getSearchEngineId(ownDomainEntity);
			searchLanguageId = ScKeywordRankService.getSearchLanguageId(ownDomainEntity);
			System.out.println("domainID=" + ownDomainEntity.getId() + ",domainName=" + ownDomainEntity.getDomain() + ",lang=" + ownDomainEntity.getLanguage()
					+ ",searchEngineId=" + searchEngineId + ",searchLanguageId=" + searchLanguageId);
		}
	}

	private void testGetDomainsByCompanyName() throws Exception {
		List<String> domainNameList = PutMessageUtils.getInstance().getExpediaDomainNames();
		for (String domainName : domainNameList) {
			System.out.println("domainName=" + domainName);
		}
	}

	private void testGetUniqueSourceUrlFromUserBacklink() {
		int domainId = 1053;
		//int domainId = 1701;
		System.out.println("domainId=" + domainId);
		List<UserBacklink> userBacklinkList = userBacklinkDAO.getUniqueSourceUrls(domainId);
		if (userBacklinkList != null && userBacklinkList.size() > 0) {
			System.out.println("userBacklinkList.size()=" + userBacklinkList.size());
		} else {
			System.out.println("userBacklinkList is empty.");
		}
	}

	private void testGetTotalTargetUrlsByDomainId() {
		//int domainId = 256;
		//int domainId = 1701;
		int domainId = 9786;
		System.out.println("domainId=" + domainId);
		TargetUrlEntity targetUrlEntity = targetUrlEntityDAO.getTotalTargetUrls(domainId);
		if (targetUrlEntity != null) {
			System.out.println("targetUrlEntity.getTotalUrls()=" + targetUrlEntity.getTotalUrls());
		} else {
			System.out.println("targetUrlEntity is null.");
		}
	}

	private void testGetDomainIdsByCompanyName() {
		List<Integer> domainIdList = ownDomainEntityDAO.getDomainIdsByCompanyName(IConstants.COMPANY_NAME_EXPEDIA);
		if (domainIdList != null && domainIdList.size() > 0) {
			for (Integer domainId : domainIdList) {
				System.out.println("domainId=" + domainId);
			}
		}
	}

	private void testStringArrayToString() {
		List<String> testStringList = new ArrayList<String>();
		testStringList.add("string1");
		testStringList.add("string2");
		System.out.println("testStringList=" + StringUtils.removeEnd(StringUtils.removeStart(testStringList.toString(), "["), "]"));
	}

	private void testGetCriticalIndicatorList() {
		List<ContentGuardChangeTrackingEntity> contentGuardChangeTrackingEntityList = contentGuardChangeTrackingDAO.getCriticalIndicatorList();
		if (contentGuardChangeTrackingEntityList != null && contentGuardChangeTrackingEntityList.size() > 0) {
			for (ContentGuardChangeTrackingEntity contentGuardChangeTrackingEntity : contentGuardChangeTrackingEntityList) {
				System.out.println("contentGuardChangeTrackingEntity=" + contentGuardChangeTrackingEntity.toString());
			}
		} else {
			System.out.println("contentGuardChangeTrackingEntityList is empty.");
		}
	}

	private void testGetUrlsWithoutCrawlData() throws Exception {
		int domainId = 8757;
		String tableName = null;
		List<TargetUrlClickHouseEntity> targetUrlClickHouseEntityList = TargetUrlClickHouseDAO.getInstance().getList(domainId, tableName);
		if (targetUrlClickHouseEntityList != null && targetUrlClickHouseEntityList.size() > 0) {
			for (TargetUrlClickHouseEntity targetUrlClickHouseEntity : targetUrlClickHouseEntityList) {
				System.out.println("url=" + targetUrlClickHouseEntity.getUrl() + ",urlHash=" + targetUrlClickHouseEntity.getUrl_hash() + ",murmurHash="
						+ targetUrlClickHouseEntity.getUrl_murmur_hash());
			}
		}

		//Date dailyDataCreationDate = DateUtils.parseDate("2020-12-16", new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
		//List<String> urlList = TargetUrlClickHouseDAO.getInstance().getUrlsWithoutCrawlData(dailyDataCreationDate, domainId, tableName);
		//if (urlList != null && urlList.size() > 0) {
		//	for (String urlString : urlList) {
		//		System.out.println("urlString=" + urlString);
		//	}
		//} else {
		//	System.out.println("urlList is empty.");
		//}
	}

	private void testGetGroupTagUrlList() {
		int domainId = 475;
		int groupTagId = 1412118;
		List<TargetUrlEntity> testTargetUrlEntityList = targetUrlEntityDAO.getGroupTagUrlList(domainId, groupTagId);
		if (testTargetUrlEntityList != null && testTargetUrlEntityList.size() > 0) {
			for (TargetUrlEntity targetUrlEntity : testTargetUrlEntityList) {
				System.out.println("targetUrlEntity.getUrl()=" + targetUrlEntity.getUrl());
			}
		} else {
			System.out.println("testTargetUrlEntityList is empty.");
		}
	}

	private void testGetDomainIdsWithTargetUrls() {
		List<OwnDomainEntity> ownDomainEntityList = null;

		ownDomainEntityList = ownDomainEntityDAO.getDomainsWithUrlsNotMoreThanLimit(30000);
		System.out.println("average ownDomainEntityList.size()=" + ownDomainEntityList.size());
		for (OwnDomainEntity ownDomainEntity : ownDomainEntityList) {
			System.out.println(
					"average domainId=" + ownDomainEntity.getId() + ",domainName=" + ownDomainEntity.getDomain() + ",totalUrls=" + ownDomainEntity.getTotalUrls());
		}

		ownDomainEntityList = ownDomainEntityDAO.getDomainsWithUrlsMoreThanLimit(30000);
		System.out.println("large ownDomainEntityList.size()=" + ownDomainEntityList.size());
		for (OwnDomainEntity ownDomainEntity : ownDomainEntityList) {
			System.out.println(
					"large domainId=" + ownDomainEntity.getId() + ",domainName=" + ownDomainEntity.getDomain() + ",totalUrls=" + ownDomainEntity.getTotalUrls());
		}

		//		List<Integer> domainIdsWithTargetUrls = ownDomainEntityDAO.getDomainIdsWithTargetUrls();
		//		if (domainIdsWithTargetUrls != null && domainIdsWithTargetUrls.size() > 0) {
		//			System.out.println("domainIdsWithTargetUrls.size()=" + domainIdsWithTargetUrls.size());
		//			for (Integer domainId : domainIdsWithTargetUrls) {
		//				System.out.println("domainId=" + domainId);
		//			}
		//
		//		} else {
		//			System.out.println("domainIdsWithTargetUrls is empty.");
		//
		//		}

	}

	private void testAiRulesDAO() {
		List<AiRulesEntity> aiRulesEntityList = aiRulesDAO.getPageAnalysisRuleList();
		for (AiRulesEntity aiRulesEntity : aiRulesEntityList) {
			System.out.println("cdbRuleId=" + aiRulesEntity.getCdbRuleId() + ",ruleDesc=" + aiRulesEntity.getRuleDesc());
		}
	}

	private void testParseJson() {
		String json = "{\"9\":1,\"12\":0,\"56\":1}";
		JsonParser parser = new JsonParser();
		JsonElement jsonElement = parser.parse(json);
		JsonObject rootObject = jsonElement.getAsJsonObject();
		Set<String> keySet = rootObject.keySet();
		for (String key : keySet) {
			System.out.println("key=" + key + ",value=" + rootObject.get(key));
		}
		String testString = rootObject.toString();
	}

	private void testContentGuardAlertDAOGetList() {
		int domainId = 9678;
		//int domainId = 9632;
		System.out.println("domainId=" + domainId);
		int crawlFrequencyType = 1;
		System.out.println("crawlFrequencyType=" + crawlFrequencyType);
		List<ContentGuardAlertEntity> contentGuardAlertEntityList = contentGuardAlertDAO.getList(domainId, crawlFrequencyType);
		if (contentGuardAlertEntityList != null && contentGuardAlertEntityList.size() > 0) {
			for (ContentGuardAlertEntity contentGuardAlertEntity : contentGuardAlertEntityList) {
				System.out.println("contentGuardAlertEntity=" + contentGuardAlertEntity.toString());
			}
		} else {
			System.out.println("contentGuardAlertEntityList is empty.");
		}
	}

	private void testContentGuardGroupDAOGet() {
		Long groupId = 1L;
		//Long groupId = 2L;
		System.out.println("groupId=" + groupId);
		int domainId = 9678;
		//int domainId = 9632;
		System.out.println("domainId=" + domainId);
		ContentGuardGroupEntity contentGuardGroupEntity = contentGuardGroupDAO.get(domainId, groupId);
		if (contentGuardGroupEntity != null) {
			System.out.println("contentGuardGroupEntity=" + contentGuardGroupEntity.toString());
		} else {
			System.out.println("contentGuardGroupEntity is null.");
		}
	}

	private void testContentGuardGroupDAOGetTotalUrls() {

		//int domainId = 9678;
		int domainId = 9632;
		System.out.println("domainId=" + domainId);

		Long groupId = 1L;
		System.out.println("groupId=" + groupId);

		int totalUrls = contentGuardGroupDAO.getTotalUrls(domainId, groupId);
		System.out.println("totalUrls=" + totalUrls);
	}

	private void testContentGuardGroupDAOGetDomainIdsByCrawlFrequency() {
		int crawlFrequencyType = IConstants.FREQUENCY_TYPE_DAILY;
		//int crawlFrequencyType = IConstants.CRAWL_FREQUENCY_TYPE_HOURLY;
		System.out.println("crawlFrequencyType=" + crawlFrequencyType);

		List<Integer> domainIdList = contentGuardGroupDAO.getDomainIdsByCrawlFrequency(crawlFrequencyType);
		if (domainIdList != null && domainIdList.size() > 0) {
			for (Integer domainId : domainIdList) {
				System.out.println("domainId=" + domainId);
			}
		} else {
			System.out.println("domainIdList is null.");
		}
	}

	private void testContentGuardUrlDAOGetListByCrawlFrequency() {
		//int domainId = 9632;
		int domainId = 9678;
		int crawlFrequencyType = IConstants.FREQUENCY_TYPE_DAILY;
		Integer crawlStatus = null;
		List<ContentGuardUrlEntity> contentGuardUrlEntityList = contentGuardUrlDAO.getListByCrawlFrequency(domainId, crawlFrequencyType, crawlStatus);
		if (contentGuardUrlEntityList != null && contentGuardUrlEntityList.size() > 0) {
			for (ContentGuardUrlEntity contentGuardUrlEntity : contentGuardUrlEntityList) {
				System.out.println("contentGuardUrlEntity=" + contentGuardUrlEntity.toString());
			}
		} else {
			System.out.println("contentGuardUrlEntityList is null.");
		}
	}

	private void testContentGuardUrlDAOUpdateCrawlStatus() throws Exception {
		//int domainId = 9632;
		int domainId = 9678;
		System.out.println("domainId=" + domainId);

		String urlString = "https://www.choicehomewarranty.com/user-agreement/";
		System.out.println("urlString=" + urlString);

		String hashCode = CrawlerUtils.getInstance().getMd5HashCode(urlString);
		System.out.println("hashCode=" + hashCode);

		//Integer crawlStatus = IConstants.CRAWL_STATUS_CRAWLED;
		Integer crawlStatus = null;
		System.out.println("crawlStatus=" + crawlStatus);

		int totalUrlsUpdated = contentGuardUrlDAO.updateCrawlStatus(domainId, hashCode, crawlStatus);
		System.out.println("totalUrlsUpdated=" + totalUrlsUpdated);
	}

	private void testContentGuardUrlDAOGetTotalUrlsByDomainId() {

		//int domainId = 9632;
		int domainId = 9678;
		System.out.println("domainId=" + domainId);

		int totalUrls = contentGuardUrlDAO.getTotalUrlsByDomainId(domainId);
		System.out.println("totalUrls=" + totalUrls);
	}

	private void testContentGuardUrlDAOGetGroupUrlsMd5HashMap() {

		//int domainId = 9632;
		int domainId = 9678;
		System.out.println("domainId=" + domainId);

		Long groupId = 1L;
		//Long groupId = 2L;
		System.out.println("groupId=" + groupId);

		Map<String, String> groupUrlMd5HashMap = contentGuardUrlDAO.getMd5HashUrlMap(domainId, groupId);
		if (groupUrlMd5HashMap != null && groupUrlMd5HashMap.size() > 0) {
			for (String key : groupUrlMd5HashMap.keySet()) {
				System.out.println("key=" + key + ",value=" + groupUrlMd5HashMap.get(key));
			}
		} else {
			System.out.println("groupUrlMd5HashMap is null.");
		}
	}

	private void testSeoClarityKeywordEntityDAO() {
		int keywordRankCheckId = 206751;
		System.out.println("keywordRankCheckId=" + keywordRankCheckId);
		SeoClarityKeywordEntity seoClarityKeywordEntity = seoClarityKeywordEntityDAO.getKeywordTextById(keywordRankCheckId);
		if (seoClarityKeywordEntity != null) {
			System.out.println("seoClarityKeywordEntity.getKeywordText()=" + seoClarityKeywordEntity.getKeywordText());
		} else {
			System.out.println("seoClarityKeywordEntity is null.");
		}

	}

	private void testCompetitorUrlMd5EntityDAO() {
		int domainId = 4;
		System.out.println("testCompetitorUrlMd5EntityDAO() domainId=" + domainId);
		Map<String, CompetitorUrlMd5Entity> hashCodeCompetitorUrlMd5EntityMap = competitorUrlMd5EntityDAO.getByDomain(domainId);
		if (hashCodeCompetitorUrlMd5EntityMap != null && hashCodeCompetitorUrlMd5EntityMap.size() > 0) {
			for (String hashCode : hashCodeCompetitorUrlMd5EntityMap.keySet()) {
				FormatUtils.getInstance()
						.logMemoryUsage("testCompetitorUrlMd5EntityDAO() competitorUrlMd5Entity=" + hashCodeCompetitorUrlMd5EntityMap.get(hashCode).toString());
			}
		} else {
			System.out.println("testCompetitorUrlMd5EntityDAO() hashCodeCompetitorUrlMd5EntityMap is null.");
		}
		//competitorUrlMd5EntityDAO.cleanupInvalidCompetitorUrl(domainId);
	}

	private void testRankingDetailClickHouseDAO() throws Exception {
		String ip = null;
		int domainId = 256;
		int searchEngineId = 1;
		int searchLanguageId = 1;
		Date rankDate = DateUtils.addDays(new Date(), -2);
		int topRankedPositions = 5;
		System.out.println("domainId=" + domainId + ",searchEngineId=" + searchEngineId + ",searchLanguageId=" + searchLanguageId + ",rankDate=" + rankDate
				+ ",topRankedPositions=" + topRankedPositions);
		Map<Integer, Set<String>> keywordIdCompetitorUrlSetMap = RankingDetailClickHouseDAO.getInstance().getKeywordIdCompetitorUrlSetMap(true, domainId, searchEngineId,
				searchLanguageId, rankDate, topRankedPositions);
		if (keywordIdCompetitorUrlSetMap != null && keywordIdCompetitorUrlSetMap.size() > 0) {
			for (Integer keywordId : keywordIdCompetitorUrlSetMap.keySet()) {
				System.out.println("keywordId=" + keywordId + ",competitorUrl=" + keywordIdCompetitorUrlSetMap.get(keywordId));
			}
		} else {
			System.out.println("keywordIdCompetitorUrlSetMap is null.");
		}
	}

	private void testWebhookDAO() {
		//int domainId = 256;
		int domainId = 9678;
		WebhookEntity webhookEntity = webhookDAO.get(domainId, IConstants.WEBBOOK_TYPE_SLACK, IConstants.WEBBOOK_ALERT_TYPE_CONTENT_GUARD);
		if (webhookEntity != null) {
			System.out.println("webhookEntity=" + webhookEntity.toString());
		} else {
			System.out.println("webhookEntity is null.");
		}
	}

	private void testContentGuardClickHouseDAO() throws Exception {
		int domainId = 9678;
		String urlString = "https://www.totalhomeprotection.com/service_agreement";
		List<String> changeTrackingFieldList = ContentGuardUtils.getInstance().getChangeTrackingIndicatorList();
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = ContentGuardClickHouseDAO.getInstance().getCrawlTimestampHistory(domainId, urlString,
				changeTrackingFieldList);
		if (htmlClickHouseEntityList != null) {
			for (HtmlClickHouseEntity htmlClickHouseEntity : htmlClickHouseEntityList) {
				System.out.println("crawlTimestamp=" + DateFormatUtils.format(htmlClickHouseEntity.getCrawlTimestamp(), IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS));
			}
		}
	}

	private void testZapierWebhookDAOCreate() throws Exception {
		int domainId = 9632;
		int userId = 214;
		long groupId = 1;
		String subType = String.valueOf(groupId);
		String callbackUrl = "https://webhook.zapier.com/callback_url_1";

		ZapierWebhookEntity zapierWebhookEntity = new ZapierWebhookEntity();
		zapierWebhookEntity.setDomainId(domainId);
		zapierWebhookEntity.setUserId(userId);
		zapierWebhookEntity.setTriggerType(IConstants.ZAPIER_TRIGGER_TYPE_CONTENT_GUARD_ALERT);
		zapierWebhookEntity.setSubTypeHashCd(CrawlerUtils.getInstance().getMd5HashCode(subType));
		zapierWebhookEntity.setSubType(subType);
		zapierWebhookEntity.setCallbackUrl(callbackUrl);
		zapierWebhookEntity.setCreateDate(new Date());
		Long id = zapierWebhookDAO.create(zapierWebhookEntity);
		System.out.println("testZapierWebhookDAOCreate() id=" + id);
	}

	private void testZapierWebhookDAOGet() throws Exception {
		//long id = 1;		
		//ZapierWebhookEntity zapierWebhookEntity = zapierWebhookDAO.get(id);

		int domainId = 9632;
		//int domainId = 168;
		int userId = 214;
		long groupId = 1;
		String subType = String.valueOf(groupId);
		String subTypeHashCd = CrawlerUtils.getInstance().getMd5HashCode(subType);
		ZapierWebhookEntity zapierWebhookEntity = zapierWebhookDAO.get(domainId, IConstants.ZAPIER_TRIGGER_TYPE_CONTENT_GUARD_ALERT, userId, subTypeHashCd);
		System.out.println("testZapierWebhookDAOGet() zapierWebhookEntity=" + zapierWebhookEntity);
	}

	private void testZapierWebhookDAODelete() {
		long id = 1;
		zapierWebhookDAO.delete(id);
	}

	private void testZapierErrorCodeDAOGet() {
		ZapierErrorCodeEntity zapierErrorCodeEntity = zapierErrorCodeDAO.get("00001");
		if (zapierErrorCodeEntity != null) {
			System.out.println("zapierErrorCodeEntity=" + zapierErrorCodeEntity.toString());
		} else {
			System.out.println("zapierErrorCodeEntity is null.");
		}
	}

	private void testDeleteMessagesFromQueue() {
		String queueName = "TEST_TARGET_URL_HTML_EN_475";
		int messagesPerIteration = 10;
		List<Message> messageList = new ArrayList<Message>();
		String queueUrl = null;
		String messageBody = null;
		try {
			queueUrl = SQSUtils.getInstance().createQueue(queueName);
			System.out.println("queueUrl=" + queueUrl);
			messageList = SQSUtils.getInstance().getMessageFromQueue(queueUrl, messagesPerIteration, IConstants.SQS_MSG_TIMEOUT_IN_43200_SECONDS);
			for (Message message : messageList) {
				messageBody = message.getBody();
				System.out.println("messageBody=" + messageBody);
			}
			System.out.println("b4 deleteMessagesFromQueue.");
			SQSUtils.getInstance().deleteMessagesFromQueue(queueUrl, messageList, messagesPerIteration);
			System.out.println("af deleteMessagesFromQueue.");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void testRetrieveCrawlProjectRequestDateIdList() {
		int domainId = 4609;
		int startCrawlRequestDate = 20210701;
		int endCrawlRequestDate = 20210831;
		List<CrawlRequestLog> crawlRequestLogList = crawlRequestLogDAO.getCrawlProjects(domainId, startCrawlRequestDate, endCrawlRequestDate);
		if (crawlRequestLogList != null && crawlRequestLogList.size() > 0) {
			for (CrawlRequestLog crawlRequestLog : crawlRequestLogList) {
				System.out.println(
						"projectName=" + crawlRequestLog.getProjectName() + ",requestDate=" + crawlRequestLog.getCrawlRequestDate() + ",id=" + crawlRequestLog.getId());
			}
		}
	}

	private void testConvertDateNumberToString() {
		int dateNumber = 20210908;
		System.out.println("dateNumber=" + dateNumber);
		String dateString = FormatUtils.getInstance().convertDateNumberToString(dateNumber);
		System.out.println("dateString=" + dateString);
	}

	private void testGetTrackedGroupUrlList() {
		int domainId = 9632;
		Long groupId = 2L;
		//String filterUrl = null;
		//String filterUrl = "https://emplois.ca.indeed.com/cmp/Videotron";
		String filterUrl = "https://www.test.com/";
		System.out.println("domainId=" + domainId + ",groupId=" + groupId + ",filterUrl=" + filterUrl);
		List<ContentGuardGroupEntity> contentGuardGroupEntityList = contentGuardGroupDAO.getTrackedGroupUrlList(domainId, groupId, filterUrl);
		if (contentGuardGroupEntityList != null && contentGuardGroupEntityList.size() > 0) {
			for (ContentGuardGroupEntity contentGuardGroupEntity : contentGuardGroupEntityList) {
				System.out.println("groupId=" + contentGuardGroupEntity.getId() + ",groupName=" + contentGuardGroupEntity.getGroupName() + ",groupCrawlFrequencyType="
						+ contentGuardGroupEntity.getCrawlFrequencyType() + ",url=" + contentGuardGroupEntity.getUrl());
			}
		} else {
			System.out.println("contentGuardGroupEntityList is empty.");
		}
	}

	private void testTargetUrlDailyCrawlTrackingEntityDAO() {
		TargetUrlDailyCrawlTrackingEntity targetUrlDailyCrawlTrackingEntity = null;
		int domainId = 0;
		String updateTargetUrlChangeTimestamp = DateFormatUtils.format(new Date(), IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
		targetUrlDailyCrawlTrackingEntity = targetUrlDailyCrawlTrackingEntityDAO.get(IConstants.PROCESS_TYPE_NUMBER_TARGET_URL_CHANGE, domainId);
		if (targetUrlDailyCrawlTrackingEntity != null) {
			targetUrlDailyCrawlTrackingEntity.setUpdateTargetUrlChangeTimestamp(updateTargetUrlChangeTimestamp);
			targetUrlDailyCrawlTrackingEntityDAO.update(targetUrlDailyCrawlTrackingEntity.getProcessType(), targetUrlDailyCrawlTrackingEntity.getDomainId(),
					targetUrlDailyCrawlTrackingEntity.getUpdateTargetUrlChangeTimestamp());
			FormatUtils.getInstance()
					.logMemoryUsage("testTargetUrlDailyCrawlTrackingEntityDAO() updated processType=" + IConstants.PROCESS_TYPE_NUMBER_TARGET_URL_CHANGE + ",domainId="
							+ domainId + ",updateTargetUrlChangeTimestamp=" + targetUrlDailyCrawlTrackingEntity.getUpdateTargetUrlChangeTimestamp());
		} else {
			targetUrlDailyCrawlTrackingEntity = new TargetUrlDailyCrawlTrackingEntity();
			targetUrlDailyCrawlTrackingEntity.setProcessType(IConstants.PROCESS_TYPE_NUMBER_TARGET_URL_CHANGE);
			targetUrlDailyCrawlTrackingEntity.setDomainId(domainId);
			targetUrlDailyCrawlTrackingEntity.setDomainName(IConstants.EMPTY_STRING);
			targetUrlDailyCrawlTrackingEntity.setTrackDate(0);
			targetUrlDailyCrawlTrackingEntity.setTotalUrls(0);
			targetUrlDailyCrawlTrackingEntity.setUpdateTargetUrlChangeTimestamp(updateTargetUrlChangeTimestamp);
			targetUrlDailyCrawlTrackingEntityDAO.create(targetUrlDailyCrawlTrackingEntity);
			FormatUtils.getInstance()
					.logMemoryUsage("testTargetUrlDailyCrawlTrackingEntityDAO() created processType=" + IConstants.PROCESS_TYPE_NUMBER_TARGET_URL_CHANGE + ",domainId="
							+ domainId + ",updateTargetUrlChangeTimestamp=" + targetUrlDailyCrawlTrackingEntity.getUpdateTargetUrlChangeTimestamp());
		}
	}

	private void testTargetUrlChangeAlertDAO() {
		//List<Integer> domainIdList = targetUrlChangeAlertDAO.getDomainIdsByAlertFrequency(IConstants.FREQUENCY_TYPE_HOURLY);
		//if (domainIdList != null && domainIdList.size() > 0) {
		//	for (Integer domainId : domainIdList) {
		//		System.out.println("testTargetUrlChangeAlertDAO() domainId=" + domainId);
		//	}
		//} else {
		//	System.out.println("testTargetUrlChangeAlertDAO() domainIdList is empty.");
		//}

		int domainId = 256;
		List<TargetUrlChangeAlertEntity> targetUrlChangeAlertEntityList = targetUrlChangeAlertDAO.getListByDomainAlertFrequency(domainId,
				IConstants.FREQUENCY_TYPE_DAILY);
		if (targetUrlChangeAlertEntityList != null && targetUrlChangeAlertEntityList.size() > 0) {
			for (TargetUrlChangeAlertEntity targetUrlChangeAlertEntity : targetUrlChangeAlertEntityList) {
				System.out.println("testTargetUrlChangeAlertDAO() targetUrlChangeAlertEntity=" + targetUrlChangeAlertEntity.toString());
			}
		} else {
			System.out.println("testTargetUrlChangeAlertDAO() targetUrlChangeAlertEntityList is empty.");
		}

	}

	private void testGroupTagEntityDAO() {
		int domainId = 8711;
		int tagType = GroupTagEntity.TAG_TYPE_TARGET_URL;
		String json = "[6229331,1370808,1365655,1357660,1365654,1215835,1306297,1365656,1374728,1377844,7010425,7010427,7010412,3595130,1194746,1237185,1306301,3596993,1194742,1194743,1192830,3595135,1196390,1192827,3595136,1456861,1377842,1374727,1274783,1357658,1357657,1357659,1274786,1194741,1369106,1369374,3597006,1388243,1193166,1194740,1237201,1193164,1388229,7010414,1193168,1274782,3595137,3576825,1430960,3596994,1377929,3596999,3596997,7010408,1370276,3597563,7056623,1196355,3595131,1370291,1196373,1196357,1196387,1370252,1373140,1379752,1377930,1370258,1196406,1346159,1346160,1346157,1346158,1196367,1196428,1196416,7108716,1400346,1196408,1196378,7108718,3576826,3576827,1196412,7108726,7108721,1196404,1196414,1196359,1359266,1196424,1196364,1412566,1196418,1196420,1196422,1196353,7108632,1388228,7108717,1196402,1306299,1430971,1196430,1373649,1430959,7199322,7199321,3597302,3597309,1369105,1369372,1369104,1369373,1196375,1359270,1379697,1430962,1430963,1430964,1430961,1194747,1359268,1430969,1430970,1359267,1359269,1358581,1400310,3597303,3597304,3597305,3597306,3597307,3597308,3597310,1196410,1358583,3597311,3597312,3597313,3597314,3597315,1430967,1430968,1196242,7010406,1430965,1395426,1320570,1370807,1365604,1430966,1365601,1346177,6231393,1365602,1365600,1365603,3597562,1346853,1395563,1373579,1395560,1395558,7278636]";
		Integer[] integerArray = new Gson().fromJson(json, Integer[].class);
		List<GroupTagEntity> groupTagEntityList = groupTagEntityDAO.getList(domainId, tagType, integerArray);
		if (groupTagEntityList != null && groupTagEntityList.size() > 0) {
			for (GroupTagEntity groupTagEntity : groupTagEntityList) {
				System.out.println(
						"testGroupTagEntityDAO() groupTagEntity.getId()=" + groupTagEntity.getId() + ",groupTagEntity.getTagName()=" + groupTagEntity.getTagName());
			}
		} else {
			System.out.println("testGroupTagEntityDAO() groupTagEntityList is null.");
		}
	}

	private void testGetGroupTagIdUrlList() {
		int domainId = 8711;
		int tagType = GroupTagEntity.TAG_TYPE_TARGET_URL;
		String json = "[6229331,1370808,1365655,1357660,1365654,1215835,1306297,1365656,1374728,1377844,7010425,7010427,7010412,3595130,1194746,1237185,1306301,3596993,1194742,1194743,1192830,3595135,1196390,1192827,3595136,1456861,1377842,1374727,1274783,1357658,1357657,1357659,1274786,1194741,1369106,1369374,3597006,1388243,1193166,1194740,1237201,1193164,1388229,7010414,1193168,1274782,3595137,3576825,1430960,3596994,1377929,3596999,3596997,7010408,1370276,3597563,7056623,1196355,3595131,1370291,1196373,1196357,1196387,1370252,1373140,1379752,1377930,1370258,1196406,1346159,1346160,1346157,1346158,1196367,1196428,1196416,7108716,1400346,1196408,1196378,7108718,3576826,3576827,1196412,7108726,7108721,1196404,1196414,1196359,1359266,1196424,1196364,1412566,1196418,1196420,1196422,1196353,7108632,1388228,7108717,1196402,1306299,1430971,1196430,1373649,1430959,7199322,7199321,3597302,3597309,1369105,1369372,1369104,1369373,1196375,1359270,1379697,1430962,1430963,1430964,1430961,1194747,1359268,1430969,1430970,1359267,1359269,1358581,1400310,3597303,3597304,3597305,3597306,3597307,3597308,3597310,1196410,1358583,3597311,3597312,3597313,3597314,3597315,1430967,1430968,1196242,7010406,1430965,1395426,1320570,1370807,1365604,1430966,1365601,1346177,6231393,1365602,1365600,1365603,3597562,1346853,1395563,1373579,1395560,1395558,7278636]";
		Integer[] integerArray = new Gson().fromJson(json, Integer[].class);
		List<TargetUrlEntity> targetUrlEntityList = targetUrlEntityDAO.getGroupTagIdUrlList(domainId, tagType, integerArray);
		if (targetUrlEntityList != null && targetUrlEntityList.size() > 0) {
			for (TargetUrlEntity targetUrlEntity : targetUrlEntityList) {
				FormatUtils.getInstance()
						.logMemoryUsage("testGetGroupTagIdUrlList() groupTagId=" + targetUrlEntity.getGroupTagId() + ",url=" + targetUrlEntity.getUrl());
			}
		} else {
			System.out.println("testGetGroupTagIdUrlList() targetUrlEntityList is null.");
		}
	}

	private void testCrawlDateHour() {
		int crawlDateHour = 0;
		Date crawlTimestamp = new Date();
		System.out.println("testCrawlDateHour() crawlTimestamp=" + crawlTimestamp);
		String crawlTimestampString = DateFormatUtils.format(crawlTimestamp, IConstants.DATE_FORMAT_YYYYMMDDHHMMSS);
		System.out.println("testCrawlDateHour() crawlTimestampString=" + crawlTimestampString);
		String crawlDateHourString = StringUtils.substring(crawlTimestampString, 0, 10);
		System.out.println("testCrawlDateHour() crawlDateHourString=" + crawlDateHourString);
		crawlDateHour = NumberUtils.toInt(crawlDateHourString);
		System.out.println("testCrawlDateHour() crawlDateHour=" + crawlDateHour);
	}

	private void generatePredicate(TargetUrlChangeContentType targetUrlChangeContentType) {
		StringBuilder stringBuilder = null;
		if (BooleanUtils.toBoolean(targetUrlChangeContentType.getLeaf()) == true) {
			stringBuilder = new StringBuilder();
			stringBuilder.append("(");
			if (StringUtils.equalsIgnoreCase(targetUrlChangeContentType.getAction(), IConstants.CONTENT_TYPE_ACTION_CONTAINS)) {
				stringBuilder.append("url like '%");
				stringBuilder.append(StringEscapeUtils.escapeSql(targetUrlChangeContentType.getValue()));
				stringBuilder.append("%'");
			} else if (StringUtils.equalsIgnoreCase(targetUrlChangeContentType.getAction(), IConstants.CONTENT_TYPE_ACTION_DOES_NOT_CONTAIN)) {
				stringBuilder.append("url not like '%");
				stringBuilder.append(StringEscapeUtils.escapeSql(targetUrlChangeContentType.getValue()));
				stringBuilder.append("%'");
			} else if (StringUtils.equalsIgnoreCase(targetUrlChangeContentType.getAction(), IConstants.CONTENT_TYPE_ACTION_ENDS_WITH)) {
				stringBuilder.append("url like '%");
				stringBuilder.append(StringEscapeUtils.escapeSql(targetUrlChangeContentType.getValue()));
				stringBuilder.append("'");
			} else if (StringUtils.equalsIgnoreCase(targetUrlChangeContentType.getAction(), IConstants.CONTENT_TYPE_ACTION_IS)) {
				stringBuilder.append("url = '");
				stringBuilder.append(StringEscapeUtils.escapeSql(targetUrlChangeContentType.getValue()));
				stringBuilder.append("'");
			} else if (StringUtils.equalsIgnoreCase(targetUrlChangeContentType.getAction(), IConstants.CONTENT_TYPE_ACTION_IS_NOT)) {
				stringBuilder.append("url != '");
				stringBuilder.append(StringEscapeUtils.escapeSql(targetUrlChangeContentType.getValue()));
				stringBuilder.append("'");
			} else if (StringUtils.equalsIgnoreCase(targetUrlChangeContentType.getAction(), IConstants.CONTENT_TYPE_ACTION_REGEXP)) {
				stringBuilder.append("match(url, '");
				stringBuilder.append(StringEscapeUtils.escapeSql(targetUrlChangeContentType.getValue()));
				stringBuilder.append("') = 1");
			} else if (StringUtils.equalsIgnoreCase(targetUrlChangeContentType.getAction(), IConstants.CONTENT_TYPE_ACTION_REGEXP_NOT_MATCH)) {
				stringBuilder.append("match(url, '");
				stringBuilder.append(StringEscapeUtils.escapeSql(targetUrlChangeContentType.getValue()));
				stringBuilder.append("') = 0");
			} else if (StringUtils.equalsIgnoreCase(targetUrlChangeContentType.getAction(), IConstants.CONTENT_TYPE_ACTION_STARTS_WITH)) {
				stringBuilder.append("url like '");
				stringBuilder.append(StringEscapeUtils.escapeSql(targetUrlChangeContentType.getValue()));
				stringBuilder.append("%'");
			}
			stringBuilder.append(")");
			targetUrlChangeContentType.setPredicate(stringBuilder.toString());
		}
		TargetUrlChangeContentType[] items = targetUrlChangeContentType.getItems();
		if (items != null && items.length > 0) {
			for (TargetUrlChangeContentType item : items) {
				generatePredicate(item);
			}
		}
	}

	private void rollupLevelTwoPredicate(TargetUrlChangeContentType targetUrlChangeContentType, TargetUrlChangeContentType previousLeveltargetUrlChangeContentType,
			List<String> predicateList) {
		TargetUrlChangeContentType[] items = null;
		StringBuilder stringBuilder = null;
		if (targetUrlChangeContentType.getLevel() == 2 && StringUtils.isNotBlank(targetUrlChangeContentType.getPredicate())) {
			if (predicateList.size() > 0) {
				predicateList.add(" " + previousLeveltargetUrlChangeContentType.getCond() + " " + targetUrlChangeContentType.getPredicate());
			} else {
				predicateList.add(targetUrlChangeContentType.getPredicate());
			}
		} else {
			items = targetUrlChangeContentType.getItems();
			if (items != null && items.length > 0) {
				predicateList = new ArrayList<String>();
				for (TargetUrlChangeContentType item : items) {
					rollupLevelTwoPredicate(item, targetUrlChangeContentType, predicateList);
				}
				//System.out.println("=============================================");
				//System.out.println("rollupLevelTwoPredicate() leaf=" + targetUrlChangeContentType.getLeaf());
				//System.out.println("rollupLevelTwoPredicate() non-leaf cond=" + previousLeveltargetUrlChangeContentType.getCond());
				//System.out.println("rollupLevelTwoPredicate() action=" + targetUrlChangeContentType.getAction());
				//System.out.println("rollupLevelTwoPredicate() value=" + targetUrlChangeContentType.getValue());
				//System.out.println("rollupLevelTwoPredicate() level=" + targetUrlChangeContentType.getLevel());
				//System.out.println("rollupLevelTwoPredicate() cond=" + targetUrlChangeContentType.getCond());
				stringBuilder = new StringBuilder();
				stringBuilder.append(" ( ");
				for (String predicate : predicateList) {
					stringBuilder.append(predicate);
				}
				stringBuilder.append(" ) ");
				targetUrlChangeContentType.setPredicate(stringBuilder.toString());
				//System.out.println("rollupLevelTwoPredicate() predicate=" + targetUrlChangeContentType.getPredicate());
			}
		}
	}

	private void rollupLevelOnePredicate(TargetUrlChangeContentType targetUrlChangeContentType, TargetUrlChangeContentType previousLeveltargetUrlChangeContentType,
			List<String> predicateList) {
		TargetUrlChangeContentType[] items = null;
		StringBuilder stringBuilder = null;
		if (targetUrlChangeContentType.getLevel() == 1 && StringUtils.isNotBlank(targetUrlChangeContentType.getPredicate())) {
			if (predicateList.size() > 0) {
				predicateList.add(" " + previousLeveltargetUrlChangeContentType.getCond() + " " + targetUrlChangeContentType.getPredicate());
			} else {
				predicateList.add(targetUrlChangeContentType.getPredicate());
			}
		} else {
			items = targetUrlChangeContentType.getItems();
			if (items != null && items.length > 0) {
				predicateList = new ArrayList<String>();
				for (TargetUrlChangeContentType item : items) {
					rollupLevelOnePredicate(item, targetUrlChangeContentType, predicateList);
				}
				//System.out.println("=============================================");
				//System.out.println("rollupLevelOnePredicate() leaf=" + targetUrlChangeContentType.getLeaf());
				//System.out.println("rollupLevelOnePredicate() non-leaf cond=" + previousLeveltargetUrlChangeContentType.getCond());
				//System.out.println("rollupLevelOnePredicate() action=" + targetUrlChangeContentType.getAction());
				//System.out.println("rollupLevelOnePredicate() value=" + targetUrlChangeContentType.getValue());
				//System.out.println("rollupLevelOnePredicate() level=" + targetUrlChangeContentType.getLevel());
				//System.out.println("rollupLevelOnePredicate() cond=" + targetUrlChangeContentType.getCond());
				stringBuilder = new StringBuilder();
				stringBuilder.append(" ( ");
				for (String predicate : predicateList) {
					stringBuilder.append(predicate);
				}
				stringBuilder.append(" ) ");
				targetUrlChangeContentType.setPredicate(stringBuilder.toString());
				//System.out.println("rollupLevelOnePredicate() predicate=" + targetUrlChangeContentType.getPredicate());
			}
		}
	}

	private void combineLevelZeroPredicate(TargetUrlChangeContentType targetUrlChangeContentType, TargetUrlChangeContentType previousLeveltargetUrlChangeContentType,
			List<String> predicateList) {
		if (targetUrlChangeContentType.getLevel() == 0 && StringUtils.isNotBlank(targetUrlChangeContentType.getPredicate())) {
			if (predicateList.size() > 0) {
				predicateList.add(" " + previousLeveltargetUrlChangeContentType.getCond() + " " + targetUrlChangeContentType.getPredicate());
			} else {
				predicateList.add(targetUrlChangeContentType.getPredicate());
			}
		}
		TargetUrlChangeContentType[] items = targetUrlChangeContentType.getItems();
		if (items != null && items.length > 0) {
			for (TargetUrlChangeContentType item : items) {
				combineLevelZeroPredicate(item, targetUrlChangeContentType, predicateList);
			}
		}
	}

	private void testGetAllParamJsons() {
		List<CommonParamEntity> commonParamEntityList = commonParamDAO.getAllParamJsons(IConstants.URL_TEXT_TYPE);
		if (commonParamEntityList != null && commonParamEntityList.size() > 0) {
			for (CommonParamEntity commonParamEntity : commonParamEntityList) {
				System.out.println("domainId=" + commonParamEntity.getOwnDomainId() + ",paramJson=" + commonParamEntity.getParamJson());
			}
		}
	}

	private void testRegExp() {
		String testString = "https://www.test.com";
		Pattern pattern = Pattern.compile("^(?=.*(?:rh=n))(?!.*(?:k:))(?!.*(?:k%3[aA])).*$");
		Matcher matcher = pattern.matcher(testString);
		System.out.println("matcher.find()=" + matcher.find());
	}

	private void testExtractCrawlDate() {
		String testCrawlDateMMDDYYYY = null;
		String crawlDateTime = "2022-08-04 08:00:00";
		System.out.println("crawlDateTime=" + crawlDateTime);
		String testCrawlDateYYYYMMDD = StringUtils.substringBefore(crawlDateTime, IConstants.ONE_SPACE);
		System.out.println("testCrawlDateYYYYMMDD=" + testCrawlDateYYYYMMDD);
		testCrawlDateMMDDYYYY = StringUtils.substring(testCrawlDateYYYYMMDD, 5, 7) + IConstants.SLASH + StringUtils.substring(testCrawlDateYYYYMMDD, 8, 10)
				+ IConstants.SLASH + StringUtils.substring(testCrawlDateYYYYMMDD, 0, 4);
		System.out.println("testCrawlDateMMDDYYYY=" + testCrawlDateMMDDYYYY);
	}

	private void testGetHttpGetResponseString() throws Exception {
		String requestUrl = "https://www.seoclarity.net/robots.txt";
		System.out.println("requestUrl=" + requestUrl);
		boolean isSendGetRequest = true;
		String requestParameters = null;
		String responseString = HttpUtils.getInstance().getResponseString(requestUrl, isSendGetRequest, requestParameters);
		System.out.println("responseString=" + responseString);
	}

	private void testIsValidRobotTxtUrl() throws Exception {
		String urlString = "https://test.edgeseo.dev/segment1/robots.txt";
		System.out.println("urlString=" + urlString);
		boolean output = CrawlerUtils.getInstance().isValidRobotTxtUrl(urlString);
		System.out.println("output=" + output);
	}
}
