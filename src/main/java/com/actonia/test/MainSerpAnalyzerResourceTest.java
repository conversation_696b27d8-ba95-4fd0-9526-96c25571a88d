package com.actonia.test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

import com.actonia.IConstants;
import com.actonia.service.MainWebServiceClientService;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.SerpAnalyzerResourceRequest;
import com.actonia.value.object.SerpAnalyzerResourceResponse;
import com.google.gson.Gson;

public class MainSerpAnalyzerResourceTest {
	private MainWebServiceClientService mainWebServiceClientService;
	private static final String REQUEST_1 = "{\"isEnableCrawl\":false,\"isAnalysisEnabled\":true,\"keyword\":\"black shoe\",\"content\":[{\"documentRank\":1,\"description\":[\"i have a blacks shoes\"],\"title\":[\"Amazon Best black Sellers: shoe Best Bed Pillows - Amazon.com\"],\"url\":\"https://www.amazon.com/Best-Sellers-Home-Kitchen-Bed-Pillows/zgbs/home-garden/10671043011\"},{\"documentRank\":2,\"description\":[\"i have a black shoes\"],\"title\":[\"i hava a black$ shoe\"],\"url\":\"https://www.target.com/c/bed-pillows-protectors-bedding-home/-/N-5xtv3\"},{\"documentRank\":3,\"description\":[\"i hava a black shoe (two spaces）\"],\"title\":[\"Bedroom Pillows | Bed Bath & Beyond\"],\"url\":\"https://www.bedbathandbeyond.com/store/category/bedding/pillows/bed-pillows/16066/\"}]}";
	private static final String REQUEST_2 = "{\"isEnableCrawl\":false,\"isAnalysisEnabled\":true,\"keyword\":\"black^shoe\",\"content\":[{\"documentRank\":1,\"description\":[\"i have a black^shoe\"],\"title\":[\"Amazon Best Sellers: Best Bed Pillows - Amazon.com\"],\"url\":\"https://www.amazon.com/Best-Sellers-Home-Kitchen-Bed-Pillows/zgbs/home-garden/10671043011\"}]}";
	private static final String REQUEST_3 = "{\"keyword\" : \"temperature sensor\",\"isAnalysisEnabled\" : true,\"isEnableCrawl\" : true,\"referenceUrl\":\"https://www.amazon.in/Laptops/b?ie=UTF8&node=1375424031\",\"content\": [{\"documentRank\": 1,\"url\": \"https://www.apexcontrols.com/product/02529964000yorktemperaturesensor/\",\"title\": \"025 29964 000 York Temperature Sensor | Apex Controls\",\"description\": \"Laptops : Upto 50% OFF on Laptops with Best Prices & Offers ...	Laptops · HP 15 Ryzen 3 Dual Core 3200U - (4 GB/1 TB HDD/ · HP 15s Core i5 11th Gen - (8 GB/1 TB HDD/Windows · Lenovo Ideapad S145 Core i3 7th Gen - (4 ... ‎Laptops Online - Budget ... · ‎Dell Laptops · ‎HP Laptops · ‎Lenovo Laptops\"},{\"documentRank\": 2,\"url\": \"https://www.ebay.com/p/620486647\",\"title\": \"Replacement YORK Chiller Parts Water Temperature Sensor ...\",\"description\": \"Temperature Sensor 025-29964-000 for YORK. Temperature Sensor. 1 Temperature Sensor. Import Duties. See details ...\"},{\"documentRank\": 3,\"url\": \"https://www.furnacepartsource.com/york-025-29964-000-sensor/\",\"title\": \"York #025-29964-000 Sensor - FurnacePartSource.com\",\"description\": \"York #025-29964-000 Sensor. Image 1 · Pin It. $160.56 $121.81. (You save $38.75). SKU: 025-29964-000. Condition: Weight: Availability: Usually Ships Same ...\"}]}";

	public MainSerpAnalyzerResourceTest() {
		super();
		this.mainWebServiceClientService = SpringBeanFactory.getBean("mainWebServiceClientService");
	}

	public static void main(String[] args) throws Exception {
		new MainSerpAnalyzerResourceTest().runTests();
	}

	private void runTests() throws Exception {
		testCase1();
		testCase2();
		testCase3();
		testCase4();
		testCase5();
		testCase6();
		testCase7();
	}

	private void testCase1() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_SERP_ANALYZER;
		System.out.println("testCase1() requestUrl=" + requestUrl);
		String requestParameters = IConstants.EMPTY_STRING;
		SerpAnalyzerResourceResponse serpAnalyzerResourceResponse = mainWebServiceClientService.serpAnalyzer(requestUrl, requestParameters);
		assertNotNull("testCase1() serpAnalyzerResourceResponse should not be null.", serpAnalyzerResourceResponse);
		assertNotNull("testCase1() serpAnalyzerResourceResponse.getError() should not be null.", serpAnalyzerResourceResponse.getError());
		assertFalse("testCase1() serpAnalyzerResourceResponse.getSuccess() should be false.", serpAnalyzerResourceResponse.getSuccess());
		String json = new Gson().toJson(serpAnalyzerResourceResponse, SerpAnalyzerResourceResponse.class);
		assertEquals("testCase1() json is incorrect", "{\"success\":false,\"error\":{\"error_code\":\"PCWS-000017\",\"error_message\":\"Request JSON is required.\"}}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase1() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase2() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_SERP_ANALYZER;
		System.out.println("testCase2() requestUrl=" + requestUrl);
		SerpAnalyzerResourceRequest serpAnalyzerResourceRequest = new SerpAnalyzerResourceRequest();
		String requestParameters = new Gson().toJson(serpAnalyzerResourceRequest, SerpAnalyzerResourceRequest.class);
		SerpAnalyzerResourceResponse serpAnalyzerResourceResponse = mainWebServiceClientService.serpAnalyzer(requestUrl, requestParameters);
		assertNotNull("testCase2() serpAnalyzerResourceResponse should not be null.", serpAnalyzerResourceResponse);
		assertNotNull("testCase2() serpAnalyzerResourceResponse.getError() should not be null.", serpAnalyzerResourceResponse.getError());
		assertFalse("testCase2() serpAnalyzerResourceResponse.getSuccess() should be false.", serpAnalyzerResourceResponse.getSuccess());
		String json = new Gson().toJson(serpAnalyzerResourceResponse, SerpAnalyzerResourceResponse.class);
		assertEquals("testCase2() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"PCWS-000034\",\"error_message\":\"Request parameter access_token is required.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase2() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase3() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_SERP_ANALYZER;
		System.out.println("testCase3() requestUrl=" + requestUrl);
		SerpAnalyzerResourceRequest serpAnalyzerResourceRequest = new SerpAnalyzerResourceRequest();
		serpAnalyzerResourceRequest.setAccess_token("test");
		String requestParameters = new Gson().toJson(serpAnalyzerResourceRequest, SerpAnalyzerResourceRequest.class);
		SerpAnalyzerResourceResponse serpAnalyzerResourceResponse = mainWebServiceClientService.serpAnalyzer(requestUrl, requestParameters);
		assertNotNull("testCase3() serpAnalyzerResourceResponse should not be null.", serpAnalyzerResourceResponse);
		assertNotNull("testCase3() serpAnalyzerResourceResponse.getError() should not be null.", serpAnalyzerResourceResponse.getError());
		assertFalse("testCase3() serpAnalyzerResourceResponse.getSuccess() should be false.", serpAnalyzerResourceResponse.getSuccess());
		String json = new Gson().toJson(serpAnalyzerResourceResponse, SerpAnalyzerResourceResponse.class);
		assertEquals("testCase3() json is incorrect",
				"{\"success\":false,\"error\":{\"error_code\":\"PCWS-000033\",\"error_message\":\"Request parameter access_token test is invalid.\"}}", json);
		FormatUtils.getInstance().logMemoryUsage("testCase3() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase4() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_SERP_ANALYZER;
		System.out.println("testCase4() requestUrl=" + requestUrl);
		SerpAnalyzerResourceRequest serpAnalyzerResourceRequest = new SerpAnalyzerResourceRequest();
		serpAnalyzerResourceRequest.setAccess_token(IConstants.INTERNAL_KEY);
		String requestParameters = new Gson().toJson(serpAnalyzerResourceRequest, SerpAnalyzerResourceRequest.class);
		SerpAnalyzerResourceResponse serpAnalyzerResourceResponse = mainWebServiceClientService.serpAnalyzer(requestUrl, requestParameters);
		assertNotNull("testCase4() serpAnalyzerResourceResponse should not be null.", serpAnalyzerResourceResponse);
		assertNotNull("testCase4() serpAnalyzerResourceResponse.getError() should not be null.", serpAnalyzerResourceResponse.getError());
		assertFalse("testCase4() serpAnalyzerResourceResponse.getSuccess() should be false.", serpAnalyzerResourceResponse.getSuccess());
		String json = new Gson().toJson(serpAnalyzerResourceResponse, SerpAnalyzerResourceResponse.class);
		assertEquals("testCase4() json is incorrect", "{\"success\":false,\"error\":{\"error_code\":\"0005\",\"error_message\":\"Request parameter json required.\"}}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase4() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase5() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_SERP_ANALYZER;
		System.out.println("testCase5() requestUrl=" + requestUrl);
		SerpAnalyzerResourceRequest serpAnalyzerResourceRequest = new SerpAnalyzerResourceRequest();
		serpAnalyzerResourceRequest.setAccess_token(IConstants.INTERNAL_KEY);
		serpAnalyzerResourceRequest.setJson(IConstants.EMPTY_STRING);
		String requestParameters = new Gson().toJson(serpAnalyzerResourceRequest, SerpAnalyzerResourceRequest.class);
		SerpAnalyzerResourceResponse serpAnalyzerResourceResponse = mainWebServiceClientService.serpAnalyzer(requestUrl, requestParameters);
		assertNotNull("testCase5() serpAnalyzerResourceResponse should not be null.", serpAnalyzerResourceResponse);
		assertNotNull("testCase5() serpAnalyzerResourceResponse.getError() should not be null.", serpAnalyzerResourceResponse.getError());
		assertFalse("testCase5() serpAnalyzerResourceResponse.getSuccess() should be false.", serpAnalyzerResourceResponse.getSuccess());
		String json = new Gson().toJson(serpAnalyzerResourceResponse, SerpAnalyzerResourceResponse.class);
		assertEquals("testCase5() json is incorrect", "{\"success\":false,\"error\":{\"error_code\":\"0005\",\"error_message\":\"Request parameter json required.\"}}",
				json);
		FormatUtils.getInstance().logMemoryUsage("testCase5() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase6() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_SERP_ANALYZER;
		System.out.println("testCase6() requestUrl=" + requestUrl);
		SerpAnalyzerResourceRequest serpAnalyzerResourceRequest = new SerpAnalyzerResourceRequest();
		serpAnalyzerResourceRequest.setAccess_token(IConstants.INTERNAL_KEY);
		serpAnalyzerResourceRequest.setJson(REQUEST_3);
		String requestParameters = new Gson().toJson(serpAnalyzerResourceRequest, SerpAnalyzerResourceRequest.class);
		SerpAnalyzerResourceResponse serpAnalyzerResourceResponse = mainWebServiceClientService.serpAnalyzer(requestUrl, requestParameters);
		assertNotNull("testCase6() serpAnalyzerResourceResponse should not be null.", serpAnalyzerResourceResponse);
		assertNull("testCase6() serpAnalyzerResourceResponse.getError() should be null.", serpAnalyzerResourceResponse.getError());
		assertTrue("testCase6() serpAnalyzerResourceResponse.getSuccess() should be true.", serpAnalyzerResourceResponse.getSuccess());
		assertNotNull("testCase6() serpAnalyzerResourceResponse.getJson() should not be null.", serpAnalyzerResourceResponse.getJson());
		FormatUtils.getInstance().logMemoryUsage("testCase6() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase7() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = mainWebServiceClientService.getMainWebServiceEndPoint() + MainWebServiceClientService.ROUTER_SERP_ANALYZER;
		System.out.println("testCase7() requestUrl=" + requestUrl);
		SerpAnalyzerResourceRequest serpAnalyzerResourceRequest = new SerpAnalyzerResourceRequest();
		serpAnalyzerResourceRequest.setAccess_token(IConstants.INTERNAL_KEY);
		serpAnalyzerResourceRequest.setJson(REQUEST_1);
		String requestParameters = new Gson().toJson(serpAnalyzerResourceRequest, SerpAnalyzerResourceRequest.class);
		SerpAnalyzerResourceResponse serpAnalyzerResourceResponse = mainWebServiceClientService.serpAnalyzer(requestUrl, requestParameters);
		assertNotNull("testCase7() serpAnalyzerResourceResponse should not be null.", serpAnalyzerResourceResponse);
		assertFalse("testCase7() serpAnalyzerResourceResponse.getSuccess() should be false.", serpAnalyzerResourceResponse.getSuccess());
		assertNotNull("testCase7() serpAnalyzerResourceResponse.getError() should not be null.", serpAnalyzerResourceResponse.getError());
		assertEquals("testCase7() serpAnalyzerResourceResponse.getError().getError_code() incorrect.", "0001", serpAnalyzerResourceResponse.getError().getError_code());
		FormatUtils.getInstance().logMemoryUsage("testCase7() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

}