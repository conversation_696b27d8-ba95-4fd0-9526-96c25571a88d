package com.actonia.test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.fail;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.BooleanUtils;

import com.actonia.IConstants;
import com.actonia.entity.AdditionalContentEntity;
import com.actonia.utils.CrawlerUtils;
import com.actonia.value.object.CrawlerResponse;
import com.actonia.value.object.CustomData;
import com.actonia.value.object.HreflangErrors;
import com.actonia.value.object.PageLink;
import com.actonia.value.object.ScrapyCrawlerResponse;
import com.google.gson.Gson;

public class CustomDataTest {

	private static final boolean isResponseAsHtml = false;

	public static void main(String[] args) {
		CustomDataTest customDataTest = null;
		try {
			customDataTest = new CustomDataTest();
			//			customDataTest.testCase1();
			//			customDataTest.testCase2();
			//			customDataTest.testCase3();
			//			customDataTest.testCase4();
			//			customDataTest.testCase5();
			//			customDataTest.testCase6();
			//			customDataTest.testCase7();
			//			customDataTest.testCase8();
			//			customDataTest.testCase9();
			//			customDataTest.testCase10();
			//			customDataTest.testCase11();
			//			customDataTest.testCase12();
			//			customDataTest.testCase13();
			//			customDataTest.testCase14();
			//			customDataTest.testCase15();
			//			customDataTest.testCase16();
			//			customDataTest.testCase17();
			//			customDataTest.testCase18();
			//			customDataTest.testCase19();
			//			customDataTest.testCase20();
			//			customDataTest.testCase21();
			//			customDataTest.testCase22();
			//			customDataTest.testCase23();
			//			customDataTest.testCase24();
			//			customDataTest.testCase26();
			//			customDataTest.testCase27();
			//			customDataTest.testCase28();
			//			customDataTest.testCase29();
			//			customDataTest.testCase30();
			//			customDataTest.testCase31();
			//			customDataTest.testCase32();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void testCase1() throws Exception {
		AdditionalContentEntity additionalContentEntity = null;
		String ip = "";
		String queueName = "";
		int domainId = 168;
		String urlString = "https://www.advdermatology.com/chemical-peels-pages-52.php";
		//String userAgent = "6W3vXZkRLJEQK9v9yBmkwAD6WEHH2S49wd8b3YncUVHpZX5W5r";
		String userAgent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 (compatible; PoliteCrawl.v2.0)";

		List<AdditionalContentEntity> additionalContentEntityList = new ArrayList<AdditionalContentEntity>();

		// first selector - XPATH
		additionalContentEntity = new AdditionalContentEntity();
		additionalContentEntity.setDomainId(domainId);
		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_XPATH);
		additionalContentEntity.setSelector("//h1/text()");
		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_ALL);
		additionalContentEntityList.add(additionalContentEntity);

		// second selector - XPATH
		additionalContentEntity = new AdditionalContentEntity();
		additionalContentEntity.setDomainId(domainId);
		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_XPATH);
		additionalContentEntity.setSelector("//h3/text()");
		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_ALL);
		additionalContentEntityList.add(additionalContentEntity);

		// third selector - CSS
		additionalContentEntity = new AdditionalContentEntity();
		additionalContentEntity.setDomainId(domainId);
		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_CSS);
		additionalContentEntity.setSelector("p");
		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_ALL);
		additionalContentEntityList.add(additionalContentEntity);

		// fourth selector - DIV ID
		additionalContentEntity = new AdditionalContentEntity();
		additionalContentEntity.setDomainId(domainId);
		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_DIV_ID);
		additionalContentEntity.setSelector("toplinks");
		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_ALL);
		additionalContentEntityList.add(additionalContentEntity);

		// fifth selector - DIV CLASS
		additionalContentEntity = new AdditionalContentEntity();
		additionalContentEntity.setDomainId(domainId);
		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_DIV_CLASS);
		additionalContentEntity.setSelector("related-product");
		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_ALL);
		additionalContentEntityList.add(additionalContentEntity);

		boolean isJavascriptCrawler = false;
		boolean isStoreHtml = false;
		Integer javascriptTimeoutInSecond = 10;

		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(ip, queueName, urlString, userAgent,
				additionalContentEntityList, isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, null, isStoreHtml, null,
				isResponseAsHtml, null);
		assertNotNull("testCase1() scrapyCrawlerResponse should not be null", scrapyCrawlerResponse);
		boolean isCustomDataAvailable = CrawlerUtils.getInstance().checkIfCustomDataAvailable(scrapyCrawlerResponse.getCrawlerResponse().getCustom_data());
		if (isCustomDataAvailable == false) {
			fail("testCase1() isCustomDataAvailable should be true.");
		}
		System.out.println("testCase1() passed.");
	}

	private void testCase2() throws Exception {
		AdditionalContentEntity additionalContentEntity = null;
		String ip = "";
		String queueName = "";
		int domainId = 168;
		String urlString = "https://www.advdermatology.com/chemical-peels-pages-52.php";
		//String userAgent = "6W3vXZkRLJEQK9v9yBmkwAD6WEHH2S49wd8b3YncUVHpZX5W5r";
		String userAgent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 (compatible; PoliteCrawl.v2.0)";

		List<AdditionalContentEntity> additionalContentEntityList = new ArrayList<AdditionalContentEntity>();

		// first selector - XPATH
		additionalContentEntity = new AdditionalContentEntity();
		additionalContentEntity.setDomainId(domainId);
		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_XPATH);
		additionalContentEntity.setSelector("//h1/text()");
		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_CONTAINS);
		additionalContentEntity.setUrlSelector("chemical-peels-pages-52");
		additionalContentEntityList.add(additionalContentEntity);

		boolean isJavascriptCrawler = false;
		boolean isStoreHtml = false;
		Integer javascriptTimeoutInSecond = 10;

		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(ip, queueName, urlString, userAgent,
				additionalContentEntityList, isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, null, isStoreHtml, null,
				isResponseAsHtml, null);
		assertNotNull("testCase2() scrapyCrawlerResponse should not be null", scrapyCrawlerResponse);
		boolean isCustomDataAvailable = CrawlerUtils.getInstance().checkIfCustomDataAvailable(scrapyCrawlerResponse.getCrawlerResponse().getCustom_data());
		if (isCustomDataAvailable == false) {
			fail("testCase2() isCustomDataAvailable should be true.");
		}
		System.out.println("testCase2() passed.");
	}

	private void testCase3() throws Exception {
		AdditionalContentEntity additionalContentEntity = null;
		String ip = "";
		String queueName = "";
		int domainId = 168;
		String urlString = "https://www.advdermatology.com/chemical-peels-pages-52.php";
		//String userAgent = "6W3vXZkRLJEQK9v9yBmkwAD6WEHH2S49wd8b3YncUVHpZX5W5r";
		String userAgent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 (compatible; PoliteCrawl.v2.0)";

		List<AdditionalContentEntity> additionalContentEntityList = new ArrayList<AdditionalContentEntity>();

		// first selector - XPATH
		additionalContentEntity = new AdditionalContentEntity();
		additionalContentEntity.setDomainId(domainId);
		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_XPATH);
		additionalContentEntity.setSelector("//h1/text()");
		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_CONTAINS);
		additionalContentEntity.setUrlSelector("chemical-peels-pages-53");
		additionalContentEntityList.add(additionalContentEntity);

		boolean isJavascriptCrawler = false;
		boolean isStoreHtml = false;
		Integer javascriptTimeoutInSecond = 10;

		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(ip, queueName, urlString, userAgent,
				additionalContentEntityList, isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, null, isStoreHtml, null,
				isResponseAsHtml, null);
		assertNotNull("testCase3() scrapyCrawlerResponse should not be null", scrapyCrawlerResponse);
		boolean isCustomDataAvailable = CrawlerUtils.getInstance().checkIfCustomDataAvailable(scrapyCrawlerResponse.getCrawlerResponse().getCustom_data());
		if (isCustomDataAvailable == true) {
			fail("testCase3() isCustomDataAvailable should be false.");
		}
		System.out.println("testCase3() passed.");
	}

	private void testCase4() throws Exception {
		AdditionalContentEntity additionalContentEntity = null;
		String ip = "";
		String queueName = "";
		int domainId = 168;
		String urlString = "https://www.advdermatology.com/chemical-peels-pages-52.php";
		//String userAgent = "6W3vXZkRLJEQK9v9yBmkwAD6WEHH2S49wd8b3YncUVHpZX5W5r";
		String userAgent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 (compatible; PoliteCrawl.v2.0)";

		List<AdditionalContentEntity> additionalContentEntityList = new ArrayList<AdditionalContentEntity>();

		// first selector - XPATH
		additionalContentEntity = new AdditionalContentEntity();
		additionalContentEntity.setDomainId(domainId);
		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_XPATH);
		additionalContentEntity.setSelector("//h1/text()");
		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_EQUALS);
		additionalContentEntity.setUrlSelector("https://www.advdermatology.com/chemical-peels-pages-52.php");
		additionalContentEntityList.add(additionalContentEntity);

		boolean isJavascriptCrawler = false;
		boolean isStoreHtml = false;
		Integer javascriptTimeoutInSecond = 10;

		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(ip, queueName, urlString, userAgent,
				additionalContentEntityList, isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, null, isStoreHtml, null,
				isResponseAsHtml, null);
		assertNotNull("testCase4() scrapyCrawlerResponse should not be null", scrapyCrawlerResponse);
		boolean isCustomDataAvailable = CrawlerUtils.getInstance().checkIfCustomDataAvailable(scrapyCrawlerResponse.getCrawlerResponse().getCustom_data());
		if (isCustomDataAvailable == false) {
			fail("testCase4() isCustomDataAvailable should be true.");
		}
		System.out.println("testCase4() passed.");
	}

	private void testCase5() throws Exception {
		AdditionalContentEntity additionalContentEntity = null;
		String ip = "";
		String queueName = "";
		int domainId = 168;
		String urlString = "https://www.advdermatology.com/chemical-peels-pages-52.php";
		//String userAgent = "6W3vXZkRLJEQK9v9yBmkwAD6WEHH2S49wd8b3YncUVHpZX5W5r";
		String userAgent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 (compatible; PoliteCrawl.v2.0)";

		List<AdditionalContentEntity> additionalContentEntityList = new ArrayList<AdditionalContentEntity>();

		// first selector - XPATH
		additionalContentEntity = new AdditionalContentEntity();
		additionalContentEntity.setDomainId(domainId);
		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_XPATH);
		additionalContentEntity.setSelector("//h1/text()");
		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_EQUALS);
		additionalContentEntity.setUrlSelector("https://www.advdermatology.com/chemical-peels-pages-53.php");
		additionalContentEntityList.add(additionalContentEntity);

		boolean isJavascriptCrawler = false;
		boolean isStoreHtml = false;
		Integer javascriptTimeoutInSecond = 10;

		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(ip, queueName, urlString, userAgent,
				additionalContentEntityList, isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, null, isStoreHtml, null,
				isResponseAsHtml, null);
		assertNotNull("testCase5() scrapyCrawlerResponse should not be null", scrapyCrawlerResponse);
		boolean isCustomDataAvailable = CrawlerUtils.getInstance().checkIfCustomDataAvailable(scrapyCrawlerResponse.getCrawlerResponse().getCustom_data());
		if (isCustomDataAvailable == true) {
			fail("testCase5() isCustomDataAvailable should be false.");
		}
		System.out.println("testCase5() passed.");
	}

	private void testCase6() throws Exception {
		AdditionalContentEntity additionalContentEntity = null;
		String ip = "";
		String queueName = "";
		int domainId = 168;
		String urlString = "https://www.advdermatology.com/chemical-peels-pages-52.php";
		//String userAgent = "6W3vXZkRLJEQK9v9yBmkwAD6WEHH2S49wd8b3YncUVHpZX5W5r";
		String userAgent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 (compatible; PoliteCrawl.v2.0)";

		List<AdditionalContentEntity> additionalContentEntityList = new ArrayList<AdditionalContentEntity>();

		// first selector - XPATH
		additionalContentEntity = new AdditionalContentEntity();
		additionalContentEntity.setDomainId(domainId);
		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_XPATH);
		additionalContentEntity.setSelector("//h1/text()");
		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_REGEXP);
		additionalContentEntity.setUrlSelector(".*chemical-peels-pages-52.php");
		additionalContentEntityList.add(additionalContentEntity);

		boolean isJavascriptCrawler = false;
		boolean isStoreHtml = false;
		Integer javascriptTimeoutInSecond = 10;

		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(ip, queueName, urlString, userAgent,
				additionalContentEntityList, isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, null, isStoreHtml, null,
				isResponseAsHtml, null);
		assertNotNull("testCase6() scrapyCrawlerResponse should not be null", scrapyCrawlerResponse);
		boolean isCustomDataAvailable = CrawlerUtils.getInstance().checkIfCustomDataAvailable(scrapyCrawlerResponse.getCrawlerResponse().getCustom_data());
		if (isCustomDataAvailable == false) {
			fail("testCase6() isCustomDataAvailable should be true.");
		}
		System.out.println("testCase6() passed.");
	}

	private void testCase7() throws Exception {
		AdditionalContentEntity additionalContentEntity = null;
		String ip = "";
		String queueName = "";
		int domainId = 168;
		String urlString = "https://www.advdermatology.com/chemical-peels-pages-52.php";
		String userAgent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 (compatible; PoliteCrawl.v2.0)";

		List<AdditionalContentEntity> additionalContentEntityList = new ArrayList<AdditionalContentEntity>();

		// first selector - XPATH
		additionalContentEntity = new AdditionalContentEntity();
		additionalContentEntity.setDomainId(domainId);
		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_XPATH);
		additionalContentEntity.setSelector("//h1/text()");
		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_REGEXP);
		additionalContentEntity.setUrlSelector(".*chemical-peels-pages-53.php");
		additionalContentEntityList.add(additionalContentEntity);

		boolean isJavascriptCrawler = false;
		boolean isStoreHtml = false;
		Integer javascriptTimeoutInSecond = 10;

		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(ip, queueName, urlString, userAgent,
				additionalContentEntityList, isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, null, isStoreHtml, null,
				isResponseAsHtml, null);
		assertNotNull("testCase7() scrapyCrawlerResponse should not be null", scrapyCrawlerResponse);
		boolean isCustomDataAvailable = CrawlerUtils.getInstance().checkIfCustomDataAvailable(scrapyCrawlerResponse.getCrawlerResponse().getCustom_data());
		if (isCustomDataAvailable == true) {
			fail("testCase7() isCustomDataAvailable should be false.");
		}
		System.out.println("testCase7() passed.");
	}

	private void testCase8() throws Exception {
		AdditionalContentEntity additionalContentEntity = null;
		String ip = "";
		String queueName = "";
		int domainId = 168;
		String urlString = "https://sp.comics.mecha.cc/authors/list?word=た";
		String userAgent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 (compatible; PoliteCrawl.v2.0)";

		List<AdditionalContentEntity> additionalContentEntityList = new ArrayList<AdditionalContentEntity>();

		// first selector - XPATH
		additionalContentEntity = new AdditionalContentEntity();
		additionalContentEntity.setDomainId(domainId);
		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_XPATH);
		additionalContentEntity.setSelector("//h1/text()");
		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_ALL);
		additionalContentEntityList.add(additionalContentEntity);

		// second selector - XPATH
		additionalContentEntity = new AdditionalContentEntity();
		additionalContentEntity.setDomainId(domainId);
		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_XPATH);
		additionalContentEntity.setSelector("//h2/text()");
		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_ALL);
		additionalContentEntityList.add(additionalContentEntity);

		// second selector - CSS
		additionalContentEntity = new AdditionalContentEntity();
		additionalContentEntity.setDomainId(domainId);
		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_CSS);
		additionalContentEntity.setSelector("p");
		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_ALL);
		additionalContentEntityList.add(additionalContentEntity);

		// third selector - DIV CLASS
		additionalContentEntity = new AdditionalContentEntity();
		additionalContentEntity.setDomainId(domainId);
		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_DIV_CLASS);
		additionalContentEntity.setSelector("linkmore");
		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_ALL);
		additionalContentEntityList.add(additionalContentEntity);

		boolean isJavascriptCrawler = false;
		boolean isStoreHtml = false;
		Integer javascriptTimeoutInSecond = 10;

		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(ip, queueName, urlString, userAgent,
				additionalContentEntityList, isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, null, isStoreHtml, null,
				isResponseAsHtml, null);
		assertNotNull("testCase8() scrapyCrawlerResponse should not be null", scrapyCrawlerResponse);
		boolean isCustomDataAvailable = CrawlerUtils.getInstance().checkIfCustomDataAvailable(scrapyCrawlerResponse.getCrawlerResponse().getCustom_data());
		if (isCustomDataAvailable == false) {
			fail("testCase8() isCustomDataAvailable should be true.");
		}
		System.out.println("testCase8() passed.");
	}

	private void testCase9() throws Exception {
		AdditionalContentEntity additionalContentEntity = null;
		String ip = "";
		String queueName = "";
		int domainId = 168;
		String urlString = "https://sp.comics.mecha.cc/authors/list?word=た";
		String userAgent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 (compatible; PoliteCrawl.v2.0)";

		List<AdditionalContentEntity> additionalContentEntityList = new ArrayList<AdditionalContentEntity>();

		// first selector - XPATH
		additionalContentEntity = new AdditionalContentEntity();
		additionalContentEntity.setDomainId(domainId);
		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_XPATH);
		additionalContentEntity.setSelector("//h1/text()");
		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_CONTAINS);
		additionalContentEntity.setUrlSelector("た");
		additionalContentEntityList.add(additionalContentEntity);

		boolean isJavascriptCrawler = false;
		boolean isStoreHtml = false;
		Integer javascriptTimeoutInSecond = 10;

		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(ip, queueName, urlString, userAgent,
				additionalContentEntityList, isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, null, isStoreHtml, null,
				isResponseAsHtml, null);
		assertNotNull("testCase9() scrapyCrawlerResponse should not be null", scrapyCrawlerResponse);
		boolean isCustomDataAvailable = CrawlerUtils.getInstance().checkIfCustomDataAvailable(scrapyCrawlerResponse.getCrawlerResponse().getCustom_data());
		if (isCustomDataAvailable == false) {
			fail("testCase9() isCustomDataAvailable should be true.");
		}
		System.out.println("testCase9() passed.");
	}

	private void testCase10() throws Exception {
		AdditionalContentEntity additionalContentEntity = null;
		String ip = "";
		String queueName = "";
		int domainId = 168;
		String urlString = "https://sp.comics.mecha.cc/authors/list?word=た";
		String userAgent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 (compatible; PoliteCrawl.v2.0)";

		List<AdditionalContentEntity> additionalContentEntityList = new ArrayList<AdditionalContentEntity>();

		// first selector - XPATH
		additionalContentEntity = new AdditionalContentEntity();
		additionalContentEntity.setDomainId(domainId);
		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_XPATH);
		additionalContentEntity.setSelector("//h1/text()");
		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_CONTAINS);
		additionalContentEntity.setUrlSelector("無料の作品");
		additionalContentEntityList.add(additionalContentEntity);

		boolean isJavascriptCrawler = false;
		boolean isStoreHtml = false;
		Integer javascriptTimeoutInSecond = 10;

		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(ip, queueName, urlString, userAgent,
				additionalContentEntityList, isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, null, isStoreHtml, null,
				isResponseAsHtml, null);
		assertNotNull("testCase10() scrapyCrawlerResponse should not be null", scrapyCrawlerResponse);
		boolean isCustomDataAvailable = CrawlerUtils.getInstance().checkIfCustomDataAvailable(scrapyCrawlerResponse.getCrawlerResponse().getCustom_data());
		if (isCustomDataAvailable == true) {
			fail("testCase10() isCustomDataAvailable should be false.");
		}
		System.out.println("testCase10() passed.");
	}

	private void testCase11() throws Exception {
		AdditionalContentEntity additionalContentEntity = null;
		String ip = "";
		String queueName = "";
		int domainId = 168;
		String urlString = "https://sp.comics.mecha.cc/authors/list?word=た";
		String userAgent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 (compatible; PoliteCrawl.v2.0)";

		List<AdditionalContentEntity> additionalContentEntityList = new ArrayList<AdditionalContentEntity>();

		// first selector - XPATH
		additionalContentEntity = new AdditionalContentEntity();
		additionalContentEntity.setDomainId(domainId);
		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_XPATH);
		additionalContentEntity.setSelector("//h1/text()");
		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_EQUALS);
		additionalContentEntity.setUrlSelector("https://sp.comics.mecha.cc/authors/list?word=た");
		additionalContentEntityList.add(additionalContentEntity);

		boolean isJavascriptCrawler = false;
		boolean isStoreHtml = false;
		Integer javascriptTimeoutInSecond = 10;

		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(ip, queueName, urlString, userAgent,
				additionalContentEntityList, isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, null, isStoreHtml, null,
				isResponseAsHtml, null);
		assertNotNull("testCase11() scrapyCrawlerResponse should not be null", scrapyCrawlerResponse);
		boolean isCustomDataAvailable = CrawlerUtils.getInstance().checkIfCustomDataAvailable(scrapyCrawlerResponse.getCrawlerResponse().getCustom_data());
		if (isCustomDataAvailable == false) {
			fail("testCase11() isCustomDataAvailable should be true.");
		}
		System.out.println("testCase11() passed.");
	}

	private void testCase12() throws Exception {
		AdditionalContentEntity additionalContentEntity = null;
		String ip = "";
		String queueName = "";
		int domainId = 168;
		String urlString = "https://sp.comics.mecha.cc/authors/list?word=た";
		String userAgent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 (compatible; PoliteCrawl.v2.0)";

		List<AdditionalContentEntity> additionalContentEntityList = new ArrayList<AdditionalContentEntity>();

		// first selector - XPATH
		additionalContentEntity = new AdditionalContentEntity();
		additionalContentEntity.setDomainId(domainId);
		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_XPATH);
		additionalContentEntity.setSelector("//h1/text()");
		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_EQUALS);
		additionalContentEntity.setUrlSelector("https://sp.comics.mecha.cc/authors/list?word=無料の作品");
		additionalContentEntityList.add(additionalContentEntity);

		boolean isJavascriptCrawler = false;
		boolean isStoreHtml = false;
		Integer javascriptTimeoutInSecond = 10;

		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(ip, queueName, urlString, userAgent,
				additionalContentEntityList, isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, null, isStoreHtml, null,
				isResponseAsHtml, null);
		assertNotNull("testCase12() scrapyCrawlerResponse should not be null", scrapyCrawlerResponse);
		boolean isCustomDataAvailable = CrawlerUtils.getInstance().checkIfCustomDataAvailable(scrapyCrawlerResponse.getCrawlerResponse().getCustom_data());
		if (isCustomDataAvailable == true) {
			fail("testCase12() isCustomDataAvailable should be false.");
		}
		System.out.println("testCase12() passed.");
	}

	private void testCase13() throws Exception {
		AdditionalContentEntity additionalContentEntity = null;
		String ip = "";
		String queueName = "";
		int domainId = 168;
		String urlString = "https://sp.comics.mecha.cc/authors/list?word=た";
		String userAgent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 (compatible; PoliteCrawl.v2.0)";

		List<AdditionalContentEntity> additionalContentEntityList = new ArrayList<AdditionalContentEntity>();

		// first selector - XPATH
		additionalContentEntity = new AdditionalContentEntity();
		additionalContentEntity.setDomainId(domainId);
		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_XPATH);
		additionalContentEntity.setSelector("//h1/text()");
		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_REGEXP);
		additionalContentEntity.setUrlSelector(".*た");
		additionalContentEntityList.add(additionalContentEntity);

		boolean isJavascriptCrawler = false;
		boolean isStoreHtml = false;
		Integer javascriptTimeoutInSecond = 10;

		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(ip, queueName, urlString, userAgent,
				additionalContentEntityList, isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, null, isStoreHtml, null,
				isResponseAsHtml, null);
		assertNotNull("testCase13() scrapyCrawlerResponse should not be null", scrapyCrawlerResponse);
		boolean isCustomDataAvailable = CrawlerUtils.getInstance().checkIfCustomDataAvailable(scrapyCrawlerResponse.getCrawlerResponse().getCustom_data());
		if (isCustomDataAvailable == false) {
			fail("testCase13() isCustomDataAvailable should be true.");
		}
		System.out.println("testCase13() passed.");
	}

	private void testCase14() throws Exception {
		AdditionalContentEntity additionalContentEntity = null;
		String ip = "";
		String queueName = "";
		int domainId = 168;
		String urlString = "https://sp.comics.mecha.cc/authors/list?word=た";
		String userAgent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 (compatible; PoliteCrawl.v2.0)";

		List<AdditionalContentEntity> additionalContentEntityList = new ArrayList<AdditionalContentEntity>();

		// first selector - XPATH
		additionalContentEntity = new AdditionalContentEntity();
		additionalContentEntity.setDomainId(domainId);
		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_XPATH);
		additionalContentEntity.setSelector("//h1/text()");
		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_REGEXP);
		additionalContentEntity.setUrlSelector(".*た無料の作品");
		additionalContentEntityList.add(additionalContentEntity);

		boolean isJavascriptCrawler = false;
		boolean isStoreHtml = false;
		Integer javascriptTimeoutInSecond = 10;

		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(ip, queueName, urlString, userAgent,
				additionalContentEntityList, isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, null, isStoreHtml, null,
				isResponseAsHtml, null);
		assertNotNull("testCase14() scrapyCrawlerResponse should not be null", scrapyCrawlerResponse);
		boolean isCustomDataAvailable = CrawlerUtils.getInstance().checkIfCustomDataAvailable(scrapyCrawlerResponse.getCrawlerResponse().getCustom_data());
		if (isCustomDataAvailable == true) {
			fail("testCase14() isCustomDataAvailable should be false.");
		}
		System.out.println("testCase14() passed.");
	}

	private void testCase15() throws Exception {
		String ip = "";
		String queueName = "";
		String urlString = "https://sp.comics.mecha.cc/authors/list?word=た";
		String userAgent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 (compatible; PoliteCrawl.v2.0)";

		List<AdditionalContentEntity> additionalContentEntityList = null;

		boolean isJavascriptCrawler = false;
		boolean isStoreHtml = false;
		Integer javascriptTimeoutInSecond = 10;

		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(ip, queueName, urlString, userAgent,
				additionalContentEntityList, isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, null, isStoreHtml, null,
				isResponseAsHtml, null);
		assertNotNull("testCase15() scrapyCrawlerResponse should not be null", scrapyCrawlerResponse);
		assertEquals("testCase15() scrapyCrawlerResponse.getCrawlerResponse().getCustom_data() is incorrect.", "[]",
				new Gson().toJson(scrapyCrawlerResponse.getCrawlerResponse().getCustom_data(), CustomData[].class));
		System.out.println("testCase15() passed.");
	}

	private void testCase16() throws Exception {
		AdditionalContentEntity additionalContentEntity = null;
		String ip = "";
		String queueName = "";
		int domainId = 168;
		String urlString = "https://www.opendoor.com/w/home-sale-calculator";
		String userAgent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 (compatible; PoliteCrawl.v2.0)";

		List<AdditionalContentEntity> additionalContentEntityList = new ArrayList<AdditionalContentEntity>();

		// first selector - XPATH
		additionalContentEntity = new AdditionalContentEntity();
		additionalContentEntity.setDomainId(domainId);
		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_XPATH);
		additionalContentEntity.setSelector("//a[contains(@class,'menu-item')]/@href");
		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_REGEXP);
		additionalContentEntity.setUrlSelector(".*calculator");
		additionalContentEntityList.add(additionalContentEntity);

		boolean isJavascriptCrawler = true;
		boolean isStoreHtml = false;
		Integer javascriptTimeoutInSecond = 10;

		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(ip, queueName, urlString, userAgent,
				additionalContentEntityList, isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, null, isStoreHtml, null,
				isResponseAsHtml, null);
		assertNotNull("testCase16() scrapyCrawlerResponse should not be null", scrapyCrawlerResponse);
		boolean isCustomDataAvailable = CrawlerUtils.getInstance().checkIfCustomDataAvailable(scrapyCrawlerResponse.getCrawlerResponse().getCustom_data());
		if (isCustomDataAvailable == false) {
			fail("testCase14() isCustomDataAvailable should be true.");
		}
		System.out.println("testCase16() passed.");
	}

	private void testCase17() throws Exception {
		String ip = "";
		String queueName = "";
		String urlString = "https://www.orbitmedia.com/blog/website-footer-design-best-practices/";
		String userAgent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 (compatible; PoliteCrawl.v2.0)";
		List<AdditionalContentEntity> additionalContentEntityList = null;
		CrawlerResponse crawlerResponse = null;
		boolean isJavascriptCrawler = false;
		boolean isStoreHtml = false;
		Integer javascriptTimeoutInSecond = 10;

		boolean isFooterLinkIdentified = false;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(ip, queueName, urlString, userAgent,
				additionalContentEntityList, isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, null, isStoreHtml, null,
				isResponseAsHtml, null);
		assertNotNull("testCase17() scrapyCrawlerResponse should not be null", scrapyCrawlerResponse);
		crawlerResponse = scrapyCrawlerResponse.getCrawlerResponse();

		if (crawlerResponse == null) {
			fail("testCase17() crawlerResponse should not be null.");
		} else {
			// page_link
			if (crawlerResponse.getPage_link() != null && crawlerResponse.getPage_link().length > 0) {
				nextPageLink: for (PageLink pageLink : crawlerResponse.getPage_link()) {
					if (BooleanUtils.isTrue(pageLink.getFooter_link())) {
						isFooterLinkIdentified = true;
						System.out.println("testCase17() link is footer link=" + pageLink.getDestination_url());
						break nextPageLink;
					}
				}
				if (isFooterLinkIdentified == false) {
					fail("testCase17() footer link could not be identified.");
				}
			} else {
				fail("testCase17() crawlerResponse.getPage_link() should not be empty.");
			}
		}

		System.out.println("testCase17() passed.");
	}

	private void testCase18() throws Exception {
		String ip = "";
		String queueName = "";
		String urlString = "https://www.woorank.com/en/edu/seo-guides/html-header";
		String userAgent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 (compatible; PoliteCrawl.v2.0)";
		List<AdditionalContentEntity> additionalContentEntityList = null;
		CrawlerResponse crawlerResponse = null;
		boolean isJavascriptCrawler = false;
		boolean isStoreHtml = false;
		Integer javascriptTimeoutInSecond = 10;

		boolean isHeaderLinkIdentified = false;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(ip, queueName, urlString, userAgent,
				additionalContentEntityList, isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, null, isStoreHtml, null,
				isResponseAsHtml, null);
		assertNotNull("testCase18() scrapyCrawlerResponse should not be null", scrapyCrawlerResponse);
		crawlerResponse = scrapyCrawlerResponse.getCrawlerResponse();

		if (crawlerResponse == null) {
			fail("testCase18() crawlerResponse should not be null.");
		} else {
			// page_link
			if (crawlerResponse.getPage_link() != null && crawlerResponse.getPage_link().length > 0) {
				nextPageLink: for (PageLink pageLink : crawlerResponse.getPage_link()) {
					if (BooleanUtils.isTrue(pageLink.getHeader_link())) {
						isHeaderLinkIdentified = true;
						System.out.println("testCase18() link is header link=" + pageLink.getDestination_url());
						break nextPageLink;
					}
				}
				if (isHeaderLinkIdentified == false) {
					fail("testCase18() header link could not be identified.");
				}
			} else {
				fail("testCase18() crawlerResponse.getPage_link() should not be empty.");
			}
		}

		System.out.println("testCase18() passed.");
	}

	private void testCase19() throws Exception {
		String ip = "";
		String queueName = "";
		String urlString = "https://varvy.com/textversusimages.html";
		String userAgent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 (compatible; PoliteCrawl.v2.0)";
		List<AdditionalContentEntity> additionalContentEntityList = null;
		CrawlerResponse crawlerResponse = null;
		boolean isJavascriptCrawler = false;
		boolean isStoreHtml = false;
		Integer javascriptTimeoutInSecond = 10;

		boolean isImageLinkIdentified = false;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(ip, queueName, urlString, userAgent,
				additionalContentEntityList, isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, null, isStoreHtml, null,
				isResponseAsHtml, null);
		assertNotNull("testCase19() scrapyCrawlerResponse should not be null", scrapyCrawlerResponse);
		crawlerResponse = scrapyCrawlerResponse.getCrawlerResponse();

		if (crawlerResponse == null) {
			fail("testCase19() crawlerResponse should not be null.");
		} else {
			// page_link
			if (crawlerResponse.getPage_link() != null && crawlerResponse.getPage_link().length > 0) {
				nextPageLink: for (PageLink pageLink : crawlerResponse.getPage_link()) {
					if (BooleanUtils.isTrue(pageLink.getImage_link())) {
						isImageLinkIdentified = true;
						System.out.println("testCase19() link is image link=" + pageLink.getDestination_url());
						break nextPageLink;
					}
				}
				if (isImageLinkIdentified == false) {
					fail("testCase19() image link could not be identified.");
				}
			} else {
				fail("testCase19() crawlerResponse.getPage_link() should not be empty.");
			}
		}

		System.out.println("testCase19() passed.");
	}

	private void testCase20() throws Exception {
		String ip = "";
		String queueName = "";
		String urlString = "https://www.grainger.com/content/how-to-go-home-safely-at-the-end-of-the-day";
		String userAgent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 (compatible; PoliteCrawl.v2.0)";
		List<AdditionalContentEntity> additionalContentEntityList = null;
		CrawlerResponse crawlerResponse = null;
		boolean isJavascriptCrawler = false;
		boolean isStoreHtml = false;
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(ip, queueName, urlString, userAgent,
				additionalContentEntityList, isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, null, isStoreHtml, null,
				isResponseAsHtml, null);
		assertNotNull("testCase20() scrapyCrawlerResponse should not be null", scrapyCrawlerResponse);
		crawlerResponse = scrapyCrawlerResponse.getCrawlerResponse();
		String expectedRobotsContent = "NOINDEX, NOFOLLOW";
		if (crawlerResponse == null) {
			fail("testCase20() crawlerResponse should not be null.");
		} else {
			// robots
			//System.out.println("testCase20() Robots="+crawlerResponse.getRobots());
			//System.out.println("testCase20() Robots_contents="+crawlerResponse.getRobots_contents());
			//System.out.println("testCase20() Robots_flg="+crawlerResponse.getRobots_flg());
			//System.out.println("testCase20() Robots_flg_x_tag="+crawlerResponse.getRobots_flg_x_tag());
			//System.out.println("testCase20() Robots_contents_x_tag="+crawlerResponse.getRobots_contents_x_tag());
			assertEquals("testCase20() Robots_contents not correct.", expectedRobotsContent, crawlerResponse.getRobots_contents());
		}
		System.out.println("testCase20() passed.");
	}

	private void testCase21() throws Exception {
		String ip = "";
		String queueName = "";
		String urlString = "https://www.retailmenot.com/coupons/wallfountains";
		String userAgent = "6W3vXZkRLJEQK9v9yBmkwAD6WEHH2S49wd8b3YncUVHpZX5W5r";
		List<AdditionalContentEntity> additionalContentEntityList = null;
		boolean isJavascriptCrawler = false;
		boolean isStoreHtml = false;
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(ip, queueName, urlString, userAgent,
				additionalContentEntityList, isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, null, isStoreHtml, null,
				isResponseAsHtml, null);
		assertNotNull("testCase21() scrapyCrawlerResponse should not be null", scrapyCrawlerResponse);
		System.out.println("testCase21() passed.");
	}

	private void testCase22() throws Exception {
		String ip = "";
		String queueName = "";
		String urlString = "https://www.cheaptickets.com/Rovio.d6056107.Destination-Travel-Guides";
		String userAgent = "ClarityBot-Expedia";
		List<AdditionalContentEntity> additionalContentEntityList = null;
		CrawlerResponse crawlerResponse = null;
		boolean isJavascriptCrawler = false;
		boolean isStoreHtml = false;
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(ip, queueName, urlString, userAgent,
				additionalContentEntityList, isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, null, isStoreHtml, null,
				isResponseAsHtml, null);
		assertNotNull("testCase22() scrapyCrawlerResponse should not be null", scrapyCrawlerResponse);
		crawlerResponse = scrapyCrawlerResponse.getCrawlerResponse();
		HreflangErrors hreflangErrors = crawlerResponse.getHreflang_errors();
		String json = new Gson().toJson(hreflangErrors, HreflangErrors.class);
		System.out.println("testCase22() hreflangErrors JSON=" + json);
		System.out.println("testCase22() passed.");
	}

	private void testCase23() throws Exception {
		String ip = "";
		String queueName = "";
		String urlString = "https://es.investing.com/equities/dia-income-statement";
		String userAgent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 (compatible; PoliteCrawl.v2.0)";
		List<AdditionalContentEntity> additionalContentEntityList = null;
		CrawlerResponse crawlerResponse = null;
		boolean isJavascriptCrawler = false;
		boolean isStoreHtml = false;
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(ip, queueName, urlString, userAgent,
				additionalContentEntityList, isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, null, isStoreHtml, null,
				isResponseAsHtml, null);
		assertNotNull("testCase23() scrapyCrawlerResponse should not be null", scrapyCrawlerResponse);
		crawlerResponse = scrapyCrawlerResponse.getCrawlerResponse();
		HreflangErrors hreflangErrors = crawlerResponse.getHreflang_errors();
		String json = new Gson().toJson(hreflangErrors, HreflangErrors.class);
		System.out.println("testCase23() hreflangErrors JSON=" + json);
		System.out.println("testCase23() passed.");
	}

	private void testCase24() throws Exception {
		String ip = "";
		String queueName = "";
		String urlString = "https://www.playstation.com/en-gb/games/grand-theft-auto-v-ps4/";
		String userAgent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 (compatible; PoliteCrawl.v2.0)";
		List<AdditionalContentEntity> additionalContentEntityList = null;
		CrawlerResponse crawlerResponse = null;
		boolean isJavascriptCrawler = false;
		boolean isStoreHtml = false;
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(ip, queueName, urlString, userAgent,
				additionalContentEntityList, isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, null, isStoreHtml, null,
				isResponseAsHtml, null);
		assertNotNull("testCase24() scrapyCrawlerResponse should not be null", scrapyCrawlerResponse);
		crawlerResponse = scrapyCrawlerResponse.getCrawlerResponse();
		HreflangErrors hreflangErrors = crawlerResponse.getHreflang_errors();
		String json = new Gson().toJson(hreflangErrors, HreflangErrors.class);
		System.out.println("testCase24() hreflangErrors JSON=" + json);
		System.out.println("testCase24() passed.");
	}

	private void testCase26() {
		CustomData[] customDataArray = null;
		String[] contentArray = null;
		try {
			//File inputFile = new File("C://dev//eclipse_workspace_svn_1.6//polite-crawl-put-messages//src//main//resources//json//custom_data.json");
			File inputFile = new File("C://dev//eclipse_workspace_svn_1.6//polite-crawl-put-messages//src//main//resources//json//crawler_api_response2.json");
			List<String> testStringList = FileUtils.readLines(inputFile, IConstants.UTF_8);
			String json = testStringList.get(0);
			System.out.println("testCase26() json b4=" + json);
			Gson gson = new Gson();
			CrawlerResponse crawlerResponse = gson.fromJson(json, CrawlerResponse.class);
			if (crawlerResponse != null) {
				//System.out.println("testCase26() crawlerResponse=" + crawlerResponse.toString());
				customDataArray = crawlerResponse.getCustom_data();
				if (customDataArray != null && customDataArray.length > 0) {
					for (CustomData customData : customDataArray) {
						contentArray = customData.getContent();
						for (String content : contentArray) {
							System.out.println("testCase26() content=" + content);
						}
					}
				}
			} else {
				System.out.println("testCase26() crawlerResponse is null.");
			}
			//System.out.println("crawlerResponse.getPage_link().toString()="+crawlerResponse.getPage_link().toString());
			//String testString = new Gson().toJson(crawlerResponse.getPage_link(), PageLink[].class);

		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void testCase27() throws Exception {
		AdditionalContentEntity additionalContentEntity = null;
		String ip = "";
		String queueName = "";
		int domainId = 168;
		String urlString = "https://www.retailmenot.com/blog/supermarket-shortcut-scams.html";
		String userAgent = "6W3vXZkRLJEQK9v9yBmkwAD6WEHH2S49wd8b3YncUVHpZX5W5r";
		//String userAgent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 (compatible; PoliteCrawl.v2.0)";

		List<AdditionalContentEntity> additionalContentEntityList = new ArrayList<AdditionalContentEntity>();

		// first selector - XPATH
		additionalContentEntity = new AdditionalContentEntity();
		additionalContentEntity.setDomainId(domainId);
		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_XPATH);
		additionalContentEntity.setSelector("//nosuchheader1/text()");
		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_ALL);
		additionalContentEntityList.add(additionalContentEntity);

		// second selector - XPATH
		additionalContentEntity = new AdditionalContentEntity();
		additionalContentEntity.setDomainId(domainId);
		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_XPATH);
		additionalContentEntity.setSelector("//nosuchheader2/text()");
		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_ALL);
		additionalContentEntityList.add(additionalContentEntity);

		// third selector - CSS
		additionalContentEntity = new AdditionalContentEntity();
		additionalContentEntity.setDomainId(domainId);
		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_CSS);
		additionalContentEntity.setSelector("no-such-css");
		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_ALL);
		additionalContentEntityList.add(additionalContentEntity);

		// fourth selector - DIV ID
		additionalContentEntity = new AdditionalContentEntity();
		additionalContentEntity.setDomainId(domainId);
		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_DIV_ID);
		additionalContentEntity.setSelector("google_ads_iframe_/31179041/desktop_blogpost_aftercontent_0__container__");
		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_ALL);
		additionalContentEntityList.add(additionalContentEntity);

		// fifth selector - DIV CLASS
		additionalContentEntity = new AdditionalContentEntity();
		additionalContentEntity.setDomainId(domainId);
		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_DIV_CLASS);
		additionalContentEntity.setSelector("no-such-div-class");
		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_ALL);
		additionalContentEntityList.add(additionalContentEntity);

		boolean isJavascriptCrawler = false;
		boolean isStoreHtml = false;
		Integer javascriptTimeoutInSecond = 10;

		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(ip, queueName, urlString, userAgent,
				additionalContentEntityList, isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, null, isStoreHtml, null,
				isResponseAsHtml, null);
		assertNotNull("testCase27() scrapyCrawlerResponse should not be null", scrapyCrawlerResponse);
		boolean isCustomDataAvailable = CrawlerUtils.getInstance().checkIfCustomDataAvailable(scrapyCrawlerResponse.getCrawlerResponse().getCustom_data());
		if (isCustomDataAvailable == true) {
			fail("testCase27() isCustomDataAvailable should be false.");
		}

		//String expectedCustomData = "Green-Bay-Hotels-Baymont-by-Wyndham-Green-Bay.h40154-p2.Hotel-Reviews";
		//boolean isExpectedCustomDataIdentified = false;
		//nextCustomData: for (CustomData customdata : scrapyCrawlerResponse.getCrawlerResponse().getCustom_data()) {
		//if (StringUtils.containsIgnoreCase(customdata.getContent(), expectedCustomData) == true) {
		//	isExpectedCustomDataIdentified = true;
		//	break nextCustomData;
		//}
		//}
		//if (isExpectedCustomDataIdentified == false) {
		//	fail("testCase27() expectedCustomData should be in actualCustomData");
		//	System.out.println("testCase27() expectedCustomData=" + expectedCustomData);
		//}
		System.out.println("testCase27() passed.");
	}

	private void testCase28() {
		String urlSelector = ".*chemical-peels-pages-52.php";
		System.out.println("testCase28() urlSelector=" + urlSelector);
		String unencodedUrlString = "https://www.advdermatology.com/chemical-peels-pages-52.php";
		System.out.println("testCase28() unencodedUrlString=" + unencodedUrlString);
		boolean isUrlSelected = Pattern.matches(urlSelector, unencodedUrlString);
		System.out.println("testCase28() isUrlSelected=" + isUrlSelected);
	}

	private void testCase29() throws Exception {
		AdditionalContentEntity additionalContentEntity = null;
		String ip = "";
		String queueName = "";
		int domainId = 168;
		String urlString = "http://downloads.seoclarity.net/extract/Off_Market_HDP_CONTROL.html";
		//String userAgent = "6W3vXZkRLJEQK9v9yBmkwAD6WEHH2S49wd8b3YncUVHpZX5W5r";
		String userAgent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 (compatible; PoliteCrawl.v2.0)";

		List<AdditionalContentEntity> additionalContentEntityList = new ArrayList<AdditionalContentEntity>();

		// XPATH
		//		additionalContentEntity = new AdditionalContentEntity();
		//		additionalContentEntity.setDomainId(domainId);
		//		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_XPATH);
		//		additionalContentEntity.setSelector("//h10/text()");
		//		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_REGEXP);
		//		additionalContentEntity.setUrlSelector(".*Off_Market_HDP_CONTROL.html");
		//		additionalContentEntityList.add(additionalContentEntity);

		// DIV ID
		additionalContentEntity = new AdditionalContentEntity();
		additionalContentEntity.setDomainId(domainId);
		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_DIV_ID);
		additionalContentEntity.setSelector("pfs-is-logged-in");
		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_REGEXP);
		additionalContentEntity.setUrlSelector(".*Off_Market_HDP_CONTROL.html");
		additionalContentEntityList.add(additionalContentEntity);

		// CSS
		additionalContentEntity = new AdditionalContentEntity();
		additionalContentEntity.setDomainId(domainId);
		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_CSS);
		additionalContentEntity.setSelector("p");
		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_REGEXP);
		additionalContentEntity.setUrlSelector(".*Off_Market_HDP_CONTROL.html");
		additionalContentEntityList.add(additionalContentEntity);

		boolean isJavascriptCrawler = false;
		boolean isStoreHtml = false;
		Integer javascriptTimeoutInSecond = 10;

		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(ip, queueName, urlString, userAgent,
				additionalContentEntityList, isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, null, isStoreHtml, null,
				isResponseAsHtml, null);
		assertNotNull("testCase29() scrapyCrawlerResponse should not be null", scrapyCrawlerResponse);
		boolean isCustomDataAvailable = CrawlerUtils.getInstance().checkIfCustomDataAvailable(scrapyCrawlerResponse.getCrawlerResponse().getCustom_data());
		if (isCustomDataAvailable == false) {
			fail("testCase29() isCustomDataAvailable should be true.");
		}
		System.out.println("testCase29() passed.");
	}

	private void testCase30() throws Exception {
		AdditionalContentEntity additionalContentEntity = null;
		String ip = "";
		String queueName = "";
		int domainId = 168;
		String urlString = "http://downloads.seoclarity.net/extract/Off_Market_HDP_CONTROL.html";
		//String userAgent = "6W3vXZkRLJEQK9v9yBmkwAD6WEHH2S49wd8b3YncUVHpZX5W5r";
		String userAgent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 (compatible; PoliteCrawl.v2.0)";

		List<AdditionalContentEntity> additionalContentEntityList = new ArrayList<AdditionalContentEntity>();

		// XPATH
		additionalContentEntity = new AdditionalContentEntity();
		additionalContentEntity.setDomainId(domainId);
		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_XPATH);
		additionalContentEntity.setSelector("//h1/");
		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_ALL);
		additionalContentEntityList.add(additionalContentEntity);

		// CSS
		additionalContentEntity = new AdditionalContentEntity();
		additionalContentEntity.setDomainId(domainId);
		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_CSS);
		additionalContentEntity.setSelector("//h1/");
		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_ALL);
		additionalContentEntityList.add(additionalContentEntity);

		// DIV_ID
		additionalContentEntity = new AdditionalContentEntity();
		additionalContentEntity.setDomainId(domainId);
		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_DIV_ID);
		additionalContentEntity.setSelector("//h1/");
		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_ALL);
		additionalContentEntityList.add(additionalContentEntity);

		// DIV_CLASS
		additionalContentEntity = new AdditionalContentEntity();
		additionalContentEntity.setDomainId(domainId);
		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_DIV_CLASS);
		additionalContentEntity.setSelector("//h1/");
		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_ALL);
		additionalContentEntityList.add(additionalContentEntity);

		boolean isJavascriptCrawler = false;
		boolean isStoreHtml = false;
		Integer javascriptTimeoutInSecond = 10;

		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(ip, queueName, urlString, userAgent,
				additionalContentEntityList, isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, null, isStoreHtml, null,
				isResponseAsHtml, null);
		assertNotNull("testCase30() scrapyCrawlerResponse should not be null", scrapyCrawlerResponse);
		System.out.println("testCase30() scrapyCrawlerResponse=" + scrapyCrawlerResponse.toString());
		boolean isCustomDataAvailable = CrawlerUtils.getInstance().checkIfCustomDataAvailable(scrapyCrawlerResponse.getCrawlerResponse().getCustom_data());
		if (isCustomDataAvailable == true) {
			fail("testCase30() isCustomDataAvailable should be false.");
		}
		System.out.println("testCase30() passed.");
	}

	private void testCase31() throws Exception {
		AdditionalContentEntity additionalContentEntity = null;
		String ip = "";
		String queueName = "";
		int domainId = 168;
		String urlString = "http://downloads.seoclarity.net/extract/100_E_Coronado_RD_Unit_Control.htm";
		//String userAgent = "6W3vXZkRLJEQK9v9yBmkwAD6WEHH2S49wd8b3YncUVHpZX5W5r";
		String userAgent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 (compatible; PoliteCrawl.v2.0)";

		List<AdditionalContentEntity> additionalContentEntityList = new ArrayList<AdditionalContentEntity>();

		// XPATH
		additionalContentEntity = new AdditionalContentEntity();
		additionalContentEntity.setDomainId(domainId);
		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_XPATH);
		additionalContentEntity.setSelector("//h1/");
		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_CONTAINS);
		additionalContentEntity.setUrlSelector("/extract/100_E_Coronado");
		additionalContentEntityList.add(additionalContentEntity);

		// CSS
		additionalContentEntity = new AdditionalContentEntity();
		additionalContentEntity.setDomainId(domainId);
		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_CSS);
		additionalContentEntity.setSelector("//h2/");
		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_ALL);
		additionalContentEntityList.add(additionalContentEntity);

		// DIV_ID
		additionalContentEntity = new AdditionalContentEntity();
		additionalContentEntity.setDomainId(domainId);
		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_DIV_ID);
		additionalContentEntity.setSelector("//h3/");
		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_EQUALS);
		additionalContentEntity.setUrlSelector("http://downloads.seoclarity.net/extract/100_E_Coronado_RD_Unit_Control.htm");
		additionalContentEntityList.add(additionalContentEntity);

		// DIV_CLASS
		additionalContentEntity = new AdditionalContentEntity();
		additionalContentEntity.setDomainId(domainId);
		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_DIV_CLASS);
		additionalContentEntity.setSelector("//h4/");
		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_REGEXP);
		additionalContentEntity.setUrlSelector(".*100_E_Coronado_RD_Unit_Control.htm");
		additionalContentEntityList.add(additionalContentEntity);

		boolean isJavascriptCrawler = false;
		boolean isStoreHtml = false;
		Integer javascriptTimeoutInSecond = 10;

		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(ip, queueName, urlString, userAgent,
				additionalContentEntityList, isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, null, isStoreHtml, null,
				isResponseAsHtml, null);
		assertNotNull("testCase31() scrapyCrawlerResponse should not be null", scrapyCrawlerResponse);
		System.out.println("testCase31() scrapyCrawlerResponse=" + scrapyCrawlerResponse.toString());
		boolean isCustomDataAvailable = CrawlerUtils.getInstance().checkIfCustomDataAvailable(scrapyCrawlerResponse.getCrawlerResponse().getCustom_data());
		if (isCustomDataAvailable == true) {
			fail("testCase31() isCustomDataAvailable should be false.");
		}
		System.out.println("testCase31() passed.");
	}

	private void testCase32() throws Exception {
		AdditionalContentEntity additionalContentEntity = null;
		String ip = "";
		String queueName = "";
		int domainId = 168;
		String urlString = "https://www.retailmenot.com/blog/supermarket-shortcut-scams.html";
		String userAgent = "6W3vXZkRLJEQK9v9yBmkwAD6WEHH2S49wd8b3YncUVHpZX5W5r";

		List<AdditionalContentEntity> additionalContentEntityList = new ArrayList<AdditionalContentEntity>();

		// XPATH
		additionalContentEntity = new AdditionalContentEntity();
		additionalContentEntity.setDomainId(domainId);
		additionalContentEntity.setSelectorType(IConstants.SELECTOR_TYPE_NUMBER_XPATH);
		additionalContentEntity.setSelector("//h3/");
		additionalContentEntity.setUrlSelectorType(IConstants.URL_SELECTOR_TYPE_CONTAINS);
		additionalContentEntity.setUrlSelector("https://www.retailmenot.com/blog/supermarket-shortcut-scams.html");
		additionalContentEntityList.add(additionalContentEntity);

		boolean isJavascriptCrawler = false;
		boolean isStoreHtml = false;
		Integer javascriptTimeoutInSecond = 10;

		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(ip, queueName, urlString, userAgent,
				additionalContentEntityList, isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, null, isStoreHtml, null,
				isResponseAsHtml, null);
		assertNotNull("testCase32() scrapyCrawlerResponse should not be null", scrapyCrawlerResponse);
		//System.out.println("testCase32() scrapyCrawlerResponse=" + scrapyCrawlerResponse.toString());
		boolean isCustomDataAvailable = CrawlerUtils.getInstance().checkIfCustomDataAvailable(scrapyCrawlerResponse.getCrawlerResponse().getCustom_data());
		if (isCustomDataAvailable == true) {
			fail("testCase32() isCustomDataAvailable should be false.");
		}
		System.out.println("testCase32() passed.");
	}
}
