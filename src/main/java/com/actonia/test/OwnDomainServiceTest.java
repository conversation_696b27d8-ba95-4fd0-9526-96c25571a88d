package com.actonia.test;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;

import com.actonia.service.OwnDomainService;
import com.actonia.value.object.ExcelAddinsData;
import com.google.gson.Gson;

public class OwnDomainServiceTest {

	public OwnDomainServiceTest() {
		super();
	}

	public static void main(String[] args) throws Exception {
		new OwnDomainServiceTest().runTests();
	}

	private void runTests() throws Exception {
		test(10873); 
//		test(10164);
//		test(9511);
//		test(9622);
//		test(4661);
//		test(10293);
//		test(9263);
//		test(4609);
//		test(10111);
//		test(8414);
//		test(9621);
//		test(9799);
//		test(9800);
//		test(9961);
//		test(7741);
//		test(9688);
//		test(9566);
//		test(10103);
//		test(7487);
//		test(8597);
//		test(8598);
//		test(8600);
//		test(9266);
//		test(765);
//		test(8884);
//		test(10173);
//		test(8520);
//		test(8622);
//		test(10136);
//		test(10174);
//		test(10660);
//		test(4663);
//		test(6077);
//		test(9814);
//		test(4392);
//		test(9595);
//		test(659);
	}

	private void test(int domainId) throws Exception {
		Object object = null;
		String mapValue = null;
		//int listIndex = 0;
		int engineId = 0;
		int languageId = 0;
		String engineName = null;
		String device = null;
		ExcelAddinsData excelAddinsData = null;
		List<ExcelAddinsData> excelAddinsDataList = null;

		List<Map<String, Object>> searchEnginesForJson4 = OwnDomainService.getSearchEnginesForJson4(domainId, false);
		if (searchEnginesForJson4 != null && searchEnginesForJson4.size() > 0) {
			excelAddinsDataList = new ArrayList<ExcelAddinsData>();
			for (Map<String, Object> map : searchEnginesForJson4) {
				//++listIndex;
				engineId = 0;
				languageId = 0;
				engineName = null;
				device = null;
				for (String mapKey : map.keySet()) {
					object = map.get(mapKey);
					if (object instanceof String) {
						mapValue = (String) object;
					} else if (object instanceof Integer) {
						mapValue = String.valueOf((Integer) object);
					} else if (object instanceof Boolean) {
						mapValue = String.valueOf((Boolean) object);
					}
					if (StringUtils.equalsIgnoreCase(mapKey, "engineId")) {
						engineId = NumberUtils.toInt(mapValue);
					} else if (StringUtils.equalsIgnoreCase(mapKey, "languageId")) {
						languageId = NumberUtils.toInt(mapValue);
					} else if (StringUtils.equalsIgnoreCase(mapKey, "engineName")) {
						engineName = mapValue;
					} else if (StringUtils.equalsIgnoreCase(mapKey, "device")) {
						device = mapValue;
					}
				}
				if (engineId > 0 && languageId > 0 && StringUtils.isNotBlank(engineName) && StringUtils.isNotBlank(device)) {
					//System.out.println("test() test() domainId=" + domainId + ",listIndex=" + listIndex + ",device=" + device + ",engineId=" + engineId + ",languageId="
					//		+ languageId + ",engineName=" + engineName);
					excelAddinsData = new ExcelAddinsData();
					excelAddinsData.setEngine_id(engineId);
					excelAddinsData.setLanguage_id(languageId);
					excelAddinsData.setDevice(device);
					excelAddinsData.setEngine_name(engineName);
					excelAddinsDataList.add(excelAddinsData);
				}
			}
		} else {
			System.out.println("test() searchEnginesForJson4 is empty.");
		}
		if (excelAddinsDataList != null && excelAddinsDataList.size() > 0) {
			for (ExcelAddinsData testExcelAddinsData : excelAddinsDataList) {
				System.out.println("testExcelAddinsData="+testExcelAddinsData.toString());
				System.out.println("test() excelAddinsData=" + new Gson().toJson(testExcelAddinsData, ExcelAddinsData.class));
			}

		} else {
			System.out.println("test() excelAddinsDataList is empty for domain ID=" + domainId);
		}
	}
}
