package com.actonia.test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

import java.util.Arrays;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import com.actonia.IConstants;
import com.actonia.utils.CrawlerUtils;
import com.actonia.value.object.HreflangLinks;
import com.actonia.value.object.PageLink;
import com.actonia.value.object.ScrapyCrawlerResponse;

public class WorkersScriptVerificationTest {

	private static final String DEFAULT_USER_AGENT = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 (compatible; politeCrawl.v2.0)";
	private static final boolean isJavascriptCrawler = false;
	private static final boolean isStoreHtml = false;
	private static final Map<String, String> pageCrawlerApiRequestHeaders = null;
	private static final boolean isResponseAsHtmlFalse = false;
	private static final boolean isResponseAsHtmlTrue = true;

	public WorkersScriptVerificationTest() {
	}

	public static void main(String[] args) {
		WorkersScriptVerificationTest workersScriptVerificationTest = null;
		try {
			workersScriptVerificationTest = new WorkersScriptVerificationTest();
			workersScriptVerificationTest.testRule36();
			workersScriptVerificationTest.testRule38();
			workersScriptVerificationTest.testRule39();
			workersScriptVerificationTest.testRule40();
			workersScriptVerificationTest.testRule41();
			workersScriptVerificationTest.testRule42();
			workersScriptVerificationTest.testRule43();
			workersScriptVerificationTest.testRule44();
			workersScriptVerificationTest.testRule45();
			workersScriptVerificationTest.testRule46();
			workersScriptVerificationTest.testRule47();
			workersScriptVerificationTest.testRule48();
			workersScriptVerificationTest.testRule49();
			workersScriptVerificationTest.testRule50();
			workersScriptVerificationTest.testRule52();
			workersScriptVerificationTest.testRule53();
			workersScriptVerificationTest.testRule53b();
			workersScriptVerificationTest.testRule53c();
			workersScriptVerificationTest.testRule53d();
			workersScriptVerificationTest.testRule53e();
			workersScriptVerificationTest.testRule53f();
			workersScriptVerificationTest.testRule52_53();
			workersScriptVerificationTest.testRule54();
			workersScriptVerificationTest.testRule54b();
			workersScriptVerificationTest.testRule58();
			workersScriptVerificationTest.testRule59();
			workersScriptVerificationTest.testRule60();
			workersScriptVerificationTest.testRule61();
			workersScriptVerificationTest.testRule62();
			workersScriptVerificationTest.testRule65();
			workersScriptVerificationTest.testRule65b();
			workersScriptVerificationTest.testRule67();
			workersScriptVerificationTest.testRule68();
			workersScriptVerificationTest.testRule69();
			workersScriptVerificationTest.testRule70();
			workersScriptVerificationTest.testRule71();
			workersScriptVerificationTest.testRule72();
			workersScriptVerificationTest.testRule73();
			workersScriptVerificationTest.testRule75();
			workersScriptVerificationTest.testRule76();
			workersScriptVerificationTest.testRule78();
			workersScriptVerificationTest.testRule79();
			workersScriptVerificationTest.testRule80();
			workersScriptVerificationTest.testRule81();
			workersScriptVerificationTest.testRule82();
			workersScriptVerificationTest.testRule83();
			workersScriptVerificationTest.testRule84();
			workersScriptVerificationTest.testRule86();
			workersScriptVerificationTest.testRule87();
			workersScriptVerificationTest.testRule88();
			workersScriptVerificationTest.testRule89();
			workersScriptVerificationTest.testRule90();
			workersScriptVerificationTest.testRule91();
			workersScriptVerificationTest.testRule92();
			workersScriptVerificationTest.testRule93();
			workersScriptVerificationTest.testRule94();
			workersScriptVerificationTest.testRule95();
			workersScriptVerificationTest.testRule97();
			workersScriptVerificationTest.testRule98();
			workersScriptVerificationTest.testRule99();
			workersScriptVerificationTest.testRule100();
			workersScriptVerificationTest.testRule101();
			workersScriptVerificationTest.testRule102();
			workersScriptVerificationTest.testRule103();
			workersScriptVerificationTest.testRule104();
			workersScriptVerificationTest.testRule107();
			workersScriptVerificationTest.testRule108();
			workersScriptVerificationTest.testSchemaValidation();
			workersScriptVerificationTest.testQueryParm1();
			workersScriptVerificationTest.testQueryParm2();
		} catch (Exception e) {
			e.printStackTrace();
		}

	}

	private void testRule36() throws Exception {
		String urlString = "https://test.edgeseo.dev/AI_Rule_36.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule36() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule36() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule36() response code incorrect.", "301", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		assertEquals("testRule36() redirect final url incorrect.", "https://test.edgeseo.dev/ai_rule_36_lower_case_path.html",
				scrapyCrawlerResponse.getCrawlerResponse().getRedirect_final_url());
		System.out.println("testRule36() passed.");
	}

	private void testRule38() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_38.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule38() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule38() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule38() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		assertEquals("testRule38() Robots Contents incorrect.", "robots of rules 38", scrapyCrawlerResponse.getCrawlerResponse().getRobots_contents());
		System.out.println("testRule38() passed.");
	}

	private void testRule39() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_39.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule39() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule39() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule39() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("scrapyCrawlerResponse.getCrawlerResponse().getRobots_contents()="+scrapyCrawlerResponse.getCrawlerResponse().getRobots_contents());
		//System.out.println("scrapyCrawlerResponse.getCrawlerResponse().getRobots()="+scrapyCrawlerResponse.getCrawlerResponse().getRobots());
		//System.out.println("scrapyCrawlerResponse.getCrawlerResponse().getRobots_flg()="+scrapyCrawlerResponse.getCrawlerResponse().getRobots_flg());
		assertEquals("testRule39() Robots Contents incorrect.", "robots of rule 39", scrapyCrawlerResponse.getCrawlerResponse().getRobots_contents());
		System.out.println("testRule39() passed.");
	}

	private void testRule40() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_40.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule40() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule40() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule40() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		assertEquals("testRule40() Robots Contents incorrect.", "robots of rule 40", scrapyCrawlerResponse.getCrawlerResponse().getRobots_contents());
		System.out.println("testRule40() passed.");
	}

	private void testRule41() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_41.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule41() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule41() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule41() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("testRule41() title="+scrapyCrawlerResponse.getCrawlerResponse().getTitle());
		assertEquals("testRule41() title incorrect.", "title of rule 41", scrapyCrawlerResponse.getCrawlerResponse().getTitle());
		System.out.println("testRule41() passed.");
	}

	private void testRule42() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_42.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule42() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule42() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule42() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("testRule42() title="+scrapyCrawlerResponse.getCrawlerResponse().getTitle());
		assertEquals("testRule42() title incorrect.", "title of rule 42", scrapyCrawlerResponse.getCrawlerResponse().getTitle());
		System.out.println("testRule42() passed.");
	}

	private void testRule43() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_43.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule43() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule43() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule43() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("testRule43() title="+scrapyCrawlerResponse.getCrawlerResponse().getTitle());
		assertEquals("testRule43() title incorrect.", "title of rule 43", scrapyCrawlerResponse.getCrawlerResponse().getTitle());
		System.out.println("testRule43() passed.");
	}

	private void testRule44() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_44.html";
		int totalNumberOfTitleTags = 0;
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlTrue, null);
		assertNotNull("testRule44() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertEquals("testRule44() response code incorrect.", 200, scrapyCrawlerResponse.getStatus());
		String html = scrapyCrawlerResponse.getHtml();
		Document document = Jsoup.parse(html);
		//String body = document.body().html();
		//System.out.println("testRule50() body=" + body);
		Elements elements = document.select("title");
		if (elements != null && elements.size() > 0) {
			totalNumberOfTitleTags = elements.size();
			//System.out.println("testRule45() totalNumberOfTitleTags="+totalNumberOfTitleTags);
			//for (Element element : elements) {
			//	title = element.text();
			//	System.out.println("testRule45() title="+title);
			//}
		}
		assertEquals("testRule44() totalNumberOfTitleTags incorrect.", 1, totalNumberOfTitleTags);
		System.out.println("testRule44() passed.");
	}

	private void testRule45() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_45.html";
		int totalNumberOfTitleTags = 0;
		//String title = null;
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlTrue, null);
		assertNotNull("testRule45() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertEquals("testRule45() response code incorrect.", 200, scrapyCrawlerResponse.getStatus());
		String html = scrapyCrawlerResponse.getHtml();
		Document document = Jsoup.parse(html);
		//String body = document.body().html();
		//System.out.println("testRule50() body=" + body);
		Elements elements = document.select("title");
		if (elements != null && elements.size() > 0) {
			totalNumberOfTitleTags = elements.size();
			//System.out.println("testRule45() totalNumberOfTitleTags="+totalNumberOfTitleTags);
			//for (Element element : elements) {
			//	title = element.text();
			//	System.out.println("testRule45() title="+title);
			//}
		}
		assertEquals("testRule45() totalNumberOfTitleTags incorrect.", 1, totalNumberOfTitleTags);
		System.out.println("testRule45() passed.");
	}

	private void testRule46() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_46.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule46() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule46() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule46() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("testRule46() title="+scrapyCrawlerResponse.getCrawlerResponse().getTitle());
		assertEquals("testRule46() desc incorrect.", "desc of rule 46", scrapyCrawlerResponse.getCrawlerResponse().getDescription());
		System.out.println("testRule46() passed.");
	}

	private void testRule47() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_47.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule47() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule47() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule47() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("testRule47() title="+scrapyCrawlerResponse.getCrawlerResponse().getTitle());
		assertEquals("testRule47() desc incorrect.", "desc of rule 47", scrapyCrawlerResponse.getCrawlerResponse().getDescription());
		System.out.println("testRule47() passed.");
	}

	private void testRule48() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_48.html";
		int totalDesc = 0;
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlTrue, null);
		assertNotNull("testRule48() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertEquals("testRule48() response code incorrect.", 200, scrapyCrawlerResponse.getStatus());
		String html = scrapyCrawlerResponse.getHtml();
		Document document = Jsoup.parse(html);
		Elements elements = document.select("meta[name=description]");
		if (elements != null && elements.size() > 0) {
			totalDesc = elements.size();
		}
		assertEquals("testRule48() totalDesc incorrect.", 1, totalDesc);
		System.out.println("testRule48() passed.");
	}

	private void testRule49() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_49.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlTrue, null);
		assertNotNull("testRule49() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertEquals("testRule49() response code incorrect.", 200, scrapyCrawlerResponse.getStatus());
		String html = scrapyCrawlerResponse.getHtml();
		int totalMetaKeywordsTags = 0;
		Document document = Jsoup.parse(html);
		Elements elements = document.select("meta[name=keywords]");
		if (elements != null && elements.size() > 0) {
			totalMetaKeywordsTags = elements.size();
		}
		//System.out.println("testRule49() keywords=" + keywords);
		assertEquals("testRule49() totalMetaKeywordsTags incorrect.", 0, totalMetaKeywordsTags);
		System.out.println("testRule49() passed.");
	}

	private void testRule50() throws Exception {
		boolean isMetaRemoved = true;
		int index = 0;
		String urlString = "https://test.edgeseo.dev/ai_rule_50.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlTrue, null);
		assertNotNull("testRule50() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertEquals("testRule50() response code incorrect.", 200, scrapyCrawlerResponse.getStatus());
		String html = scrapyCrawlerResponse.getHtml();
		Document document = Jsoup.parse(html);
		//String body = document.body().html();
		//System.out.println("testRule50() body=" + body);
		Elements metaTags = document.body().getElementsByTag("meta");
		for (Element metaTag : metaTags) {
			index++;
			//System.out.println("testRule50() name=" + metaTag.attr("name") + ",content=" + metaTag.attr("content"));
			//if (index == 3 && StringUtils.isBlank(metaTag.attr("name")) && StringUtils.equalsIgnoreCase(metaTag.attr("content"), "https://www.webphotosource.net/img/source/gdcdngjbmc/236x295px@2x/BLH-T_Black-Hills-Spruce_Twinkly_SSC.jpeg?keep=c&crop=yes&u=7mzq6p&q=70")) {
			if (index == 3 && StringUtils.isBlank(metaTag.attr("name")) && StringUtils.equalsIgnoreCase(metaTag.attr("content"),
					"https://www.webphotosource.net/img/source/gykfj0tq1u/236x295px@2x/RSS-T_Red-Spruce-Slim_LEDCA_SSC-10.jpeg?keep=c&crop=yes&u=7mzq6p&q=70")) {
				isMetaRemoved = false;
				break;
			}
		}
		assertTrue("testRule50() isMetaRemoved should be true.", isMetaRemoved);
		System.out.println("testRule50() passed.");
	}

	private void testRule52() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_52.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule52() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule52() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule52() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("testRule52() h2="+Arrays.toString(scrapyCrawlerResponse.getCrawlerResponse().getH2()));
		assertEquals("testRule52() h2 incorrect.", "[first h2 of rule 52, second h2 of rule 52, third h2 of rule 52, fourth h2 of rule 52]",
				Arrays.toString(scrapyCrawlerResponse.getCrawlerResponse().getH2()));
		System.out.println("testRule52() passed.");
	}

	private void testRule53() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_53.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule53() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule53() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule53() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("testRule53() title="+scrapyCrawlerResponse.getCrawlerResponse().getTitle());
		assertEquals("testRule53() h1 incorrect.", "[h1 of rule 53]", Arrays.toString(scrapyCrawlerResponse.getCrawlerResponse().getH1()));
		System.out.println("testRule53() passed.");
	}

	private void testRule53b() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_53_b.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule53b() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule53b() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule53b() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("testRule53b() title="+scrapyCrawlerResponse.getCrawlerResponse().getTitle());
		assertEquals("testRule53b() h1 incorrect.", "[h1 of rule 53b]", Arrays.toString(scrapyCrawlerResponse.getCrawlerResponse().getH1()));
		System.out.println("testRule53b() passed.");
	}

	private void testRule53c() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_53_c.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule53c() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule53c() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule53c() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("testRule53c() title="+scrapyCrawlerResponse.getCrawlerResponse().getTitle());
		assertEquals("testRule53c() h1 incorrect.", "[h1 of rule 53c]", Arrays.toString(scrapyCrawlerResponse.getCrawlerResponse().getH1()));
		System.out.println("testRule53c() passed.");
	}

	private void testRule53d() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_53_d.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule53d() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule53d() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule53d() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("testRule53d() title="+scrapyCrawlerResponse.getCrawlerResponse().getTitle());
		assertEquals("testRule53d() h1 incorrect.", "[h1 of rule 53d]", Arrays.toString(scrapyCrawlerResponse.getCrawlerResponse().getH1()));
		System.out.println("testRule53d() passed.");
	}

	private void testRule53e() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_53_e.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule53e() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule53e() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule53e() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("testRule53e() title="+scrapyCrawlerResponse.getCrawlerResponse().getTitle());
		assertNull("testRule53e() h1 should be null.", scrapyCrawlerResponse.getCrawlerResponse().getH1());
		System.out.println("testRule53e() passed.");
	}

	private void testRule53f() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_53_f.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule53f() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule53f() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule53f() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("testRule53f() title="+scrapyCrawlerResponse.getCrawlerResponse().getTitle());
		assertEquals("testRule53f() h1 incorrect.", "[h1 of rule 53f]", Arrays.toString(scrapyCrawlerResponse.getCrawlerResponse().getH1()));
		System.out.println("testRule53f() passed.");
	}

	private void testRule52_53() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_52_53.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule52_53() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule52_53() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule52_53() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("testRule52_53() title="+scrapyCrawlerResponse.getCrawlerResponse().getTitle());
		assertEquals("testRule52_53() h1 incorrect.", "[h1 of rule 52_53]", Arrays.toString(scrapyCrawlerResponse.getCrawlerResponse().getH1()));
		assertEquals("testRule52_53() h2 incorrect.", "[h2 of rule 52_53]", Arrays.toString(scrapyCrawlerResponse.getCrawlerResponse().getH2()));
		System.out.println("testRule52_53() passed.");
	}

	private void testRule54() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_54.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule54() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule54() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule54() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("testRule54() title="+scrapyCrawlerResponse.getCrawlerResponse().getTitle());
		assertNull("h1 should be null.", scrapyCrawlerResponse.getCrawlerResponse().getH1());
		System.out.println("testRule54() passed.");
	}

	private void testRule54b() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_54_b.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule54b() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule54b() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule54b() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		assertEquals("testRule52_53() h1 incorrect.", "[Like Deals?, Shop Star Style]", Arrays.toString(scrapyCrawlerResponse.getCrawlerResponse().getH1()));
		System.out.println("testRule54b() passed.");
	}

	private void testRule58() throws Exception {
		int totalNumberOfBaseTags = 0;
		String urlString = "https://test.edgeseo.dev/ai_rule_58.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlTrue, null);
		assertNotNull("testRule58() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertEquals("testRule58() response code incorrect.", 200, scrapyCrawlerResponse.getStatus());
		String html = scrapyCrawlerResponse.getHtml();
		Document document = Jsoup.parse(html);
		//String baseHref = null;
		//String body = document.body().html();
		//System.out.println("testRule50() body=" + body);
		Elements elements = document.select("base");
		if (elements != null && elements.size() > 0) {
			totalNumberOfBaseTags = elements.size();
			//for (Element element : elements) {
			//	baseHref = elements.attr("href");
			//	System.out.println("testRule58() baseHref="+baseHref);
			//}
		}
		assertEquals("testRule58() totalNumberOfBaseTags incorrect.", 1, totalNumberOfBaseTags);
		System.out.println("testRule58() passed.");
	}

	private void testRule59() throws Exception {
		int totalNumberOfBaseTags = 0;
		String urlString = "https://test.edgeseo.dev/ai_rule_59.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlTrue, null);
		assertNotNull("testRule59() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertEquals("testRule59() response code incorrect.", 200, scrapyCrawlerResponse.getStatus());
		String html = scrapyCrawlerResponse.getHtml();
		Document document = Jsoup.parse(html);
		//String baseHref = null;
		//String body = document.body().html();
		//System.out.println("testRule50() body=" + body);
		Elements elements = document.select("base");
		if (elements != null && elements.size() > 0) {
			totalNumberOfBaseTags = elements.size();
			//for (Element element : elements) {
			//	baseHref = elements.attr("href");
			//	System.out.println("testRule59() baseHref="+baseHref);
			//}
		}
		assertEquals("testRule59() totalNumberOfBaseTags incorrect.", 1, totalNumberOfBaseTags);
		System.out.println("testRule59() passed.");
	}

	private void testRule60() throws Exception {
		int totalNumberOfBaseTags = 0;
		String urlString = "https://test.edgeseo.dev/ai_rule_60.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlTrue, null);
		assertNotNull("testRule60() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertEquals("testRule60() response code incorrect.", 200, scrapyCrawlerResponse.getStatus());
		String html = scrapyCrawlerResponse.getHtml();
		Document document = Jsoup.parse(html);
		String baseHref = null;
		//String body = document.body().html();
		//System.out.println("testRule50() body=" + body);
		Elements elements = document.select("base");
		if (elements != null && elements.size() > 0) {
			totalNumberOfBaseTags = elements.size();
			baseHref = elements.first().attr("href");
			//for (Element element : elements) {
			//	baseHref = elements.attr("href");
			//	System.out.println("testRule60() baseHref="+baseHref);
			//}
		}
		assertEquals("testRule60() totalNumberOfBaseTags incorrect.", 1, totalNumberOfBaseTags);
		assertEquals("testRule60() baseHref incorrect.", "base of rule 60", baseHref);
		System.out.println("testRule60() passed.");
	}

	private void testRule61() throws Exception {
		int totalNumberOfBaseTags = 0;
		String urlString = "https://test.edgeseo.dev/ai_rule_61.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlTrue, null);
		assertNotNull("testRule61() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertEquals("testRule61() response code incorrect.", 200, scrapyCrawlerResponse.getStatus());
		String html = scrapyCrawlerResponse.getHtml();
		Document document = Jsoup.parse(html);
		String baseHref = null;
		//String body = document.body().html();
		//System.out.println("testRule50() body=" + body);
		Elements elements = document.select("base");
		if (elements != null && elements.size() > 0) {
			totalNumberOfBaseTags = elements.size();
			baseHref = elements.first().attr("href");
			//for (Element element : elements) {
			//	baseHref = elements.attr("href");
			//	System.out.println("testRule61() baseHref="+baseHref);
			//}
		}
		assertEquals("testRule61() totalNumberOfBaseTags incorrect.", 1, totalNumberOfBaseTags);
		assertEquals("testRule61() baseHref incorrect.", "base of rule 61", baseHref);
		System.out.println("testRule61() passed.");
	}

	private void testRule62() throws Exception {
		int totalNumberOfBaseTags = 0;
		String urlString = "https://test.edgeseo.dev/ai_rule_62.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlTrue, null);
		assertNotNull("testRule62() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertEquals("testRule62() response code incorrect.", 200, scrapyCrawlerResponse.getStatus());
		String html = scrapyCrawlerResponse.getHtml();
		Document document = Jsoup.parse(html);
		String baseHref = null;
		//String body = document.body().html();
		//System.out.println("testRule50() body=" + body);
		Elements elements = document.select("base");
		if (elements != null && elements.size() > 0) {
			totalNumberOfBaseTags = elements.size();
			baseHref = elements.first().attr("href");
			//for (Element element : elements) {
			//	baseHref = elements.attr("href");
			//	System.out.println("testRule62() baseHref="+baseHref);
			//}
		}
		assertEquals("testRule62() totalNumberOfBaseTags incorrect.", 1, totalNumberOfBaseTags);
		assertEquals("testRule62() baseHref incorrect.", "base of rule 62", baseHref);
		System.out.println("testRule62() passed.");
	}

	private void testRule65() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_65.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule65() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule65() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule65() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("testRule65() title="+scrapyCrawlerResponse.getCrawlerResponse().getTitle());

		PageLink[] pageLinkArray = scrapyCrawlerResponse.getCrawlerResponse().getPage_link();
		if (pageLinkArray != null && pageLinkArray.length > 0) {
			for (PageLink pageLink : pageLinkArray) {
				//System.out.println("pageLink.getDestination_url()="+pageLink.getDestination_url());
				assertEquals("testRule65() internal link incorrect", "https://test.edgeseo.dev/rule 65 internal link", pageLink.getDestination_url());
			}
		}
		System.out.println("testRule65() passed.");
	}

	private void testRule65b() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_65_b.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule65b() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule65b() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule65b() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("testRule65b() title="+scrapyCrawlerResponse.getCrawlerResponse().getTitle());

		PageLink[] pageLinkArray = scrapyCrawlerResponse.getCrawlerResponse().getPage_link();
		if (pageLinkArray != null && pageLinkArray.length > 0) {
			for (PageLink pageLink : pageLinkArray) {
				//System.out.println("pageLink.getDestination_url()="+pageLink.getDestination_url());
				assertEquals("testRule65b() internal link incorrect", "https://test.edgeseo.dev/rule 65b internal link", pageLink.getDestination_url());
				break;
			}
		}
		System.out.println("testRule65b() passed.");
	}

	private void testRule67() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_67.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule67() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule67() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule67() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("testRule67() canonical="+scrapyCrawlerResponse.getCrawlerResponse().getCanonical());
		assertEquals("testRule67() canonical not correct.", "https://test.edgeseo.dev/canonical of rule 67", scrapyCrawlerResponse.getCrawlerResponse().getCanonical());
		System.out.println("testRule67() passed.");
	}

	private void testRule68() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_68.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule68() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule68() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule68() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("testRule68() canonical="+scrapyCrawlerResponse.getCrawlerResponse().getCanonical());
		assertNull("testRule68() canonical should be null.", scrapyCrawlerResponse.getCrawlerResponse().getCanonical());
		System.out.println("testRule68() passed.");
	}

	private void testRule69() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_69.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule69() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule69() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule69() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("testRule69() canonical="+scrapyCrawlerResponse.getCrawlerResponse().getCanonical());
		assertEquals("testRule69() canonical incorrect.", "https://test.edgeseo.dev/canonical of rule 69", scrapyCrawlerResponse.getCrawlerResponse().getCanonical());
		System.out.println("testRule69() passed.");
	}

	private void testRule70() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_70.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule70() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule70() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule70() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("testRule70() title="+scrapyCrawlerResponse.getCrawlerResponse().getTitle());
		assertEquals("testRule70() canonical not correct.", "https://test.edgeseo.dev/canonical of rule 70", scrapyCrawlerResponse.getCrawlerResponse().getCanonical());
		System.out.println("testRule70() passed.");
	}

	private void testRule71() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_71.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule71() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule71() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule71() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("testRule71() title="+scrapyCrawlerResponse.getCrawlerResponse().getTitle());
		assertEquals("testRule71() canonical not correct.", "https://test.edgeseo.dev/canonical of rule 71", scrapyCrawlerResponse.getCrawlerResponse().getCanonical());
		System.out.println("testRule71() passed.");
	}

	private void testRule72() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_72.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule72() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule72() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule72() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("testRule72() Viewport content="+scrapyCrawlerResponse.getCrawlerResponse().getViewport_content());
		assertEquals("viewport content incorrect.", "viewport content of rule 72", scrapyCrawlerResponse.getCrawlerResponse().getViewport_content());
		System.out.println("testRule72() passed.");
	}

	private void testRule73() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_73.html";
		int totalNumberOfMetaViewportTags = 0;
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlTrue, null);
		assertNotNull("testRule73() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertEquals("testRule73() response code incorrect.", 200, scrapyCrawlerResponse.getStatus());
		String html = scrapyCrawlerResponse.getHtml();
		Document document = Jsoup.parse(html);
		//String body = document.body().html();
		//System.out.println("testRule50() body=" + body);
		Elements elements = document.select("meta[name=viewport]");
		if (elements != null && elements.size() > 0) {
			totalNumberOfMetaViewportTags = elements.size();
			//System.out.println("testRule45() totalNumberOfTitleTags="+totalNumberOfTitleTags);
			//for (Element element : elements) {
			//	title = element.text();
			//	System.out.println("testRule45() title="+title);
			//}
		}
		assertEquals("testRule73() totalNumberOfMetaViewportTags incorrect.", 1, totalNumberOfMetaViewportTags);
		System.out.println("testRule73() passed.");
	}

	private void testRule75() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_75.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule75() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule75() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule75() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("testRule75() Viewport content="+scrapyCrawlerResponse.getCrawlerResponse().getViewport_content());
		assertEquals("viewport content incorrect.", "viewport content of rule 75", scrapyCrawlerResponse.getCrawlerResponse().getViewport_content());
		System.out.println("testRule75() passed.");
	}

	private void testRule76() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_39.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule76() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule76() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule76() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("testRule76() Viewport content="+scrapyCrawlerResponse.getCrawlerResponse().getViewport_content());
		assertEquals("testRule76() Robots Contents incorrect.", "robots of rule 39", scrapyCrawlerResponse.getCrawlerResponse().getRobots_contents());
		assertEquals("testRule76() title incorrect.", "title of rule 76", scrapyCrawlerResponse.getCrawlerResponse().getTitle());
		assertEquals("testRule76() desc incorrect.", "desc of rule 76", scrapyCrawlerResponse.getCrawlerResponse().getDescription());
		System.out.println("testRule76() passed.");
	}

	private void testRule78() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_78.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule78() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule78() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule78() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("testRule78() title="+scrapyCrawlerResponse.getCrawlerResponse().getTitle());
		assertEquals("testRule78() canonical not correct.", "https://test.edgeseo.dev/canonical of rule 78", scrapyCrawlerResponse.getCrawlerResponse().getCanonical());
		System.out.println("testRule78() passed.");
	}

	private void testRule79() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_79.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule79() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule79() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule79() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("testRule79() title="+scrapyCrawlerResponse.getCrawlerResponse().getTitle());
		assertEquals("testRule79() canonical not correct.", "https://test.edgeseo.dev/canonical of rule 79", scrapyCrawlerResponse.getCrawlerResponse().getCanonical());
		System.out.println("testRule79() passed.");
	}

	private void testRule80() throws Exception {
		int totalHreflangEn = 0;
		String urlString = "https://test.edgeseo.dev/ai_rule_80.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule80() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule80() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule80() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		HreflangLinks[] hreflangLinks = scrapyCrawlerResponse.getCrawlerResponse().getHreflang_links();
		if (hreflangLinks != null && hreflangLinks.length > 0) {
			for (HreflangLinks hreflangLink : hreflangLinks) {
				//System.out.println("testRule80() lang="+hreflangLink.getLang()+",href="+hreflangLink.getHref());
				if (StringUtils.equalsIgnoreCase(hreflangLink.getLang(), "en")) {
					totalHreflangEn++;
				}
			}
		}
		assertEquals("testRule80() totalHreflangEn incorrect.", 1, totalHreflangEn);
		System.out.println("testRule80() passed.");
	}

	private void testRule81() throws Exception {
		int totalHreflangXDefault = 0;
		String urlString = "https://test.edgeseo.dev/ai_rule_81.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule81() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule81() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule81() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		HreflangLinks[] hreflangLinks = scrapyCrawlerResponse.getCrawlerResponse().getHreflang_links();
		if (hreflangLinks != null && hreflangLinks.length > 0) {
			for (HreflangLinks hreflangLink : hreflangLinks) {
				//System.out.println("testRule81() lang="+hreflangLink.getLang()+",href="+hreflangLink.getHref());
				if (StringUtils.equalsIgnoreCase(hreflangLink.getLang(), "x-default")) {
					totalHreflangXDefault++;
				}
			}
		}
		assertEquals("testRule81() totalHreflangXDefault incorrect.", 1, totalHreflangXDefault);
		System.out.println("testRule81() passed.");
	}

	private void testRule82() throws Exception {
		String hrefOfSixthEntry = null;
		String hrefOfSeventhEntry = null;
		int index = 0;
		String urlString = "https://test.edgeseo.dev/ai_rule_82.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule82() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule82() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule82() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		HreflangLinks[] hreflangLinks = scrapyCrawlerResponse.getCrawlerResponse().getHreflang_links();
		if (hreflangLinks != null && hreflangLinks.length > 0) {
			for (HreflangLinks hreflangLink : hreflangLinks) {
				index++;
				if (index == 6) {
					hrefOfSixthEntry = hreflangLink.getHref();
				} else if (index == 7) {
					hrefOfSeventhEntry = hreflangLink.getHref();
				}
				//System.out.println("testRule82() lang="+hreflangLink.getLang()+",href="+hreflangLink.getHref());
			}
		}
		assertEquals("testRule82() hrefOfSixthEntry incorrect.", "https://test.edgeseo.dev/href 6 of rule 82", hrefOfSixthEntry);
		assertEquals("testRule82() hrefOfSeventhEntry incorrect.", "https://test.edgeseo.dev/href 7 of rule 82", hrefOfSeventhEntry);
		System.out.println("testRule82() passed.");
	}

	private void testRule83() throws Exception {
		String langOfEighthEntry = null;
		String langOfEighteenthEntry = null;
		int index = 0;
		String urlString = "https://test.edgeseo.dev/ai_rule_83.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule83() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule83() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule83() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		HreflangLinks[] hreflangLinks = scrapyCrawlerResponse.getCrawlerResponse().getHreflang_links();
		if (hreflangLinks != null && hreflangLinks.length > 0) {
			for (HreflangLinks hreflangLink : hreflangLinks) {
				index++;
				if (index == 8) {
					langOfEighthEntry = hreflangLink.getLang();
				} else if (index == 18) {
					langOfEighteenthEntry = hreflangLink.getLang();
				}
				//System.out.println("testRule83() lang="+hreflangLink.getLang()+",href="+hreflangLink.getHref());
			}
		}
		assertEquals("testRule83() langOfEighthEntry incorrect.", "lang 8 of rule 83", langOfEighthEntry);
		assertEquals("testRule83() langOfEighteenthEntry incorrect.", "lang 18 of rule 83", langOfEighteenthEntry);
		System.out.println("testRule83() passed.");
	}

	private void testRule84() throws Exception {
		boolean isNewHreflangEntryZhFound = false;
		boolean isNewHreflangEntryXxFound = false;
		boolean isNewHreflangEntryYyFound = false;
		boolean isNewHreflangEntryZzFound = false;
		int index = 0;
		String urlString = "https://test.edgeseo.dev/ai_rule_84.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule84() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule84() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule84() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		HreflangLinks[] hreflangLinks = scrapyCrawlerResponse.getCrawlerResponse().getHreflang_links();
		if (hreflangLinks != null && hreflangLinks.length > 0) {
			for (HreflangLinks hreflangLink : hreflangLinks) {
				index++;
				//System.out.println("testRule84() lang="+hreflangLink.getLang()+",href="+hreflangLink.getHref());
				if (StringUtils.equalsIgnoreCase(hreflangLink.getLang(), "zh")) {
					isNewHreflangEntryZhFound = true;
				} else if (StringUtils.equalsIgnoreCase(hreflangLink.getLang(), "xx")) {
					isNewHreflangEntryXxFound = true;
				} else if (StringUtils.equalsIgnoreCase(hreflangLink.getLang(), "yy")) {
					isNewHreflangEntryYyFound = true;
				} else if (StringUtils.equalsIgnoreCase(hreflangLink.getLang(), "zz")) {
					isNewHreflangEntryZzFound = true;
				}
			}
		}
		assertEquals("testRule84() isNewHreflangEntryZhFound incorrect.", true, isNewHreflangEntryZhFound);
		assertEquals("testRule84() isNewHreflangEntryXxFound incorrect.", true, isNewHreflangEntryXxFound);
		assertEquals("testRule84() isNewHreflangEntryYyFound incorrect.", true, isNewHreflangEntryYyFound);
		assertEquals("testRule84() isNewHreflangEntryZzFound incorrect.", true, isNewHreflangEntryZzFound);
		System.out.println("testRule84() passed.");
	}

	private void testRule86() throws Exception {
		int index = 0;
		String href1 = null;
		String lang1 = null;
		String href6 = null;
		String lang6 = null;
		String urlString = "https://test.edgeseo.dev/ai_rule_86.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule86() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule86() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule86() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		HreflangLinks[] hreflangLinks = scrapyCrawlerResponse.getCrawlerResponse().getHreflang_links();
		if (hreflangLinks != null && hreflangLinks.length > 0) {
			for (HreflangLinks hreflangLink : hreflangLinks) {
				index++;
				//System.out.println("testRule86() lang="+hreflangLink.getLang()+",href="+hreflangLink.getHref());
				if (index == 1) {
					href1 = hreflangLink.getHref();
					lang1 = hreflangLink.getLang();
				} else if (index == 6) {
					href6 = hreflangLink.getHref();
					lang6 = hreflangLink.getLang();
				}
			}
		}
		assertEquals("testRule86() lang1 incorrect.", "lang 1 of rule 86", lang1);
		assertEquals("testRule86() href1 incorrect.", "https://test.edgeseo.dev/href 1 of rule 86", href1);
		assertEquals("testRule86() lang6 incorrect.", "lang 6 of rule 86", lang6);
		assertEquals("testRule86() href6 incorrect.", "https://test.edgeseo.dev/href 6 of rule 86", href6);
		System.out.println("testRule86() passed.");
	}

	private void testRule87() throws Exception {
		int index = 0;
		String href1 = null;
		String urlString = "https://test.edgeseo.dev/ai_rule_87.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule87() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule87() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule87() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		HreflangLinks[] hreflangLinks = scrapyCrawlerResponse.getCrawlerResponse().getHreflang_links();
		if (hreflangLinks != null && hreflangLinks.length > 0) {
			for (HreflangLinks hreflangLink : hreflangLinks) {
				index++;
				if (index == 1) {
					href1 = hreflangLink.getHref();
				}
				//System.out.println("testRule87() lang="+hreflangLink.getLang()+",href="+hreflangLink.getHref());
			}
		}
		assertEquals("testRule87() href1 incorrect.", "https://test.edgeseo.dev/ai_rule_87.html", href1);
		System.out.println("testRule87() passed.");
	}

	private void testRule88() throws Exception {
		int index = 0;
		String href1 = null;
		String urlString = "https://test.edgeseo.dev/ai_rule_88.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule88() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule88() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule88() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		HreflangLinks[] hreflangLinks = scrapyCrawlerResponse.getCrawlerResponse().getHreflang_links();
		if (hreflangLinks != null && hreflangLinks.length > 0) {
			for (HreflangLinks hreflangLink : hreflangLinks) {
				index++;
				if (index == 1) {
					assertEquals("testRule88() href1 incorrect.", "https://test.edgeseo.dev/href 1 of rule 88", hreflangLink.getHref());
				} else if (index == 2) {
					assertEquals("testRule88() href2 incorrect.", "https://test.edgeseo.dev/href 2 of rule 88", hreflangLink.getHref());
				} else if (index == 3) {
					assertEquals("testRule88() href3 incorrect.", "https://test.edgeseo.dev/href 3 of rule 88", hreflangLink.getHref());
				} else if (index == 4) {
					assertEquals("testRule88() href4 incorrect.", "https://test.edgeseo.dev/href 4 of rule 88", hreflangLink.getHref());
				} else if (index == 5) {
					assertEquals("testRule88() href5 incorrect.", "https://test.edgeseo.dev/href 5 of rule 88", hreflangLink.getHref());
				} else if (index == 6) {
					assertEquals("testRule88() href6 incorrect.", "https://test.edgeseo.dev/href 6 of rule 88", hreflangLink.getHref());
				}
				//System.out.println("testRule88() lang="+hreflangLink.getLang()+",href="+hreflangLink.getHref());
			}
		}
		//assertEquals("testRule88() href1 incorrect.", "https://test.edgeseo.dev/ai_rule_87.html", href1);
		System.out.println("testRule88() passed.");
	}

	private void testRule89() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_89.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule89() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule89() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule89() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("testRule89() title="+scrapyCrawlerResponse.getCrawlerResponse().getTitle());
		assertEquals("testRule89() title incorrect.", "title of rule 89", scrapyCrawlerResponse.getCrawlerResponse().getTitle());
		System.out.println("testRule89() passed.");
	}

	private void testRule90() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_90.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule90() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule90() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule90() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("testRule90() title="+scrapyCrawlerResponse.getCrawlerResponse().getTitle());
		assertEquals("testRule90() title incorrect.", "title of rule 90", scrapyCrawlerResponse.getCrawlerResponse().getTitle());
		System.out.println("testRule90() passed.");
	}

	private void testRule91() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_91.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule91() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule91() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule91() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("testRule91() title="+scrapyCrawlerResponse.getCrawlerResponse().getTitle());
		assertEquals("testRule91() desc incorrect.", "desc of rule 91", scrapyCrawlerResponse.getCrawlerResponse().getDescription());
		System.out.println("testRule91() passed.");
	}

	private void testRule92() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_92.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule92() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule92() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule92() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("testRule92() title="+scrapyCrawlerResponse.getCrawlerResponse().getTitle());
		assertEquals("testRule92() h1 incorrect.", "[h1 of rule 92]", Arrays.toString(scrapyCrawlerResponse.getCrawlerResponse().getH1()));
		System.out.println("testRule92() passed.");
	}

	private void testRule93() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_93.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule93() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule93() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule93() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("testRule93() h2="+Arrays.toString(scrapyCrawlerResponse.getCrawlerResponse().getH2()));
		assertEquals("testRule93() first h2 incorrect.", "first h2 of rule 93", scrapyCrawlerResponse.getCrawlerResponse().getH2()[0]);
		assertEquals("testRule93() second h2 incorrect.", "second h2 of rule 93", scrapyCrawlerResponse.getCrawlerResponse().getH2()[1]);
		System.out.println("testRule93() passed.");
	}

	private void testRule94() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_94.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule94() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule94() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule94() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("testRule94() Viewport content="+scrapyCrawlerResponse.getCrawlerResponse().getViewport_content());
		assertEquals("testRule94() viewport content incorrect.", "viewport content of rule 94", scrapyCrawlerResponse.getCrawlerResponse().getViewport_content());
		System.out.println("testRule94() passed.");
	}

	private void testRule95() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_95.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule95() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule95() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule95() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("testRule95() Viewport content="+scrapyCrawlerResponse.getCrawlerResponse().getViewport_content());
		assertEquals("testRule95() viewport content incorrect.", "viewport content of rule 95", scrapyCrawlerResponse.getCrawlerResponse().getViewport_content());
		System.out.println("testRule95() passed.");
	}

	private void testRule97() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_97.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule97() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule97() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule97() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("testRule97() Viewport content="+scrapyCrawlerResponse.getCrawlerResponse().getViewport_content());
		assertEquals("testRule97() viewport content incorrect.", "viewport content of rule 97", scrapyCrawlerResponse.getCrawlerResponse().getViewport_content());
		System.out.println("testRule97() passed.");
	}

	private void testRule98() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_98.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule98() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule98() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule98() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("testRule98() Viewport content="+scrapyCrawlerResponse.getCrawlerResponse().getViewport_content());
		assertEquals("testRule98() viewport content incorrect.", "viewport content of rule 98", scrapyCrawlerResponse.getCrawlerResponse().getViewport_content());
		System.out.println("testRule98() passed.");
	}

	private void testRule99() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_99.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule99() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule99() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule99() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("testRule99() title="+scrapyCrawlerResponse.getCrawlerResponse().getTitle());
		assertEquals("testRule99() canonical not correct.", "https://test.edgeseo.dev/canonical of rule 99", scrapyCrawlerResponse.getCrawlerResponse().getCanonical());
		System.out.println("testRule99() passed.");
	}

	private void testRule100() throws Exception {
		int index = 0;
		String href1 = null;
		String urlString = "https://test.edgeseo.dev/ai_rule_100.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule100() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule100() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule100() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		HreflangLinks[] hreflangLinks = scrapyCrawlerResponse.getCrawlerResponse().getHreflang_links();
		if (hreflangLinks != null && hreflangLinks.length > 0) {
			for (HreflangLinks hreflangLink : hreflangLinks) {
				//System.out.println("testRule100() lang="+hreflangLink.getLang()+",href="+hreflangLink.getHref());
				index++;
				if (index == 1) {
					assertEquals("testRule100() href1 incorrect.", "https://test.edgeseo.dev/href 1 of rule 100", hreflangLink.getHref());
				} else if (index == 2) {
					assertEquals("testRule100() href2 incorrect.", "https://test.edgeseo.dev/href 2 of rule 100", hreflangLink.getHref());
				}
			}
		}
		System.out.println("testRule100() passed.");
	}

	private void testRule101() throws Exception {
		int index = 0;
		String href1 = null;
		String urlString = "https://test.edgeseo.dev/ai_rule_101.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule101() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule101() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule101() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		HreflangLinks[] hreflangLinks = scrapyCrawlerResponse.getCrawlerResponse().getHreflang_links();
		if (hreflangLinks != null && hreflangLinks.length > 0) {
			for (HreflangLinks hreflangLink : hreflangLinks) {
				//System.out.println("testRule101() lang="+hreflangLink.getLang()+",href="+hreflangLink.getHref());
				index++;
				if (index == 1) {
					assertEquals("testRule101() href1 incorrect.", "https://test.edgeseo.dev/ai_rule_101.html", hreflangLink.getHref());
				}
			}
		}
		System.out.println("testRule101() passed.");
	}

	private void testRule102() throws Exception {
		int index = 0;
		String href1 = null;
		int totalNumberOfHreflangs = 0;
		String urlString = "https://test.edgeseo.dev/ai_rule_102.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule102() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule102() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule102() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		HreflangLinks[] hreflangLinks = scrapyCrawlerResponse.getCrawlerResponse().getHreflang_links();
		if (hreflangLinks != null && hreflangLinks.length > 0) {
			totalNumberOfHreflangs = hreflangLinks.length;
			for (HreflangLinks hreflangLink : hreflangLinks) {
				//System.out.println("testRule102() lang="+hreflangLink.getLang()+",href="+hreflangLink.getHref());
				index++;
				if (index == 1) {
					assertEquals("testRule102() href1 incorrect.", "https://test.edgeseo.dev/ai_rule_102.html", hreflangLink.getHref());
				}
			}
		}
		assertEquals("testRule102() totalNumberOfHreflangs incorrect.", 1, totalNumberOfHreflangs);
		System.out.println("testRule102() passed.");
	}

	private void testRule103() throws Exception {
		int index = 0;
		String href1 = null;
		String urlString = "https://test.edgeseo.dev/ai_rule_103.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule103() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule103() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule103() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		HreflangLinks[] hreflangLinks = scrapyCrawlerResponse.getCrawlerResponse().getHreflang_links();
		if (hreflangLinks != null && hreflangLinks.length > 0) {
			for (HreflangLinks hreflangLink : hreflangLinks) {
				//System.out.println("testRule103() lang="+hreflangLink.getLang()+",href="+hreflangLink.getHref());
				index++;
				if (index == 1) {
					assertEquals("testRule103() href1 incorrect.", "https://test.edgeseo.dev/ai_rule_103.html", hreflangLink.getHref());
				}
			}
		}
		System.out.println("testRule103() passed.");
	}

	private void testRule104() throws Exception {
		int index = 0;
		String href1 = null;
		String urlString = "https://test.edgeseo.dev/ai_rule_104.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule104() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule104() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule104() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		HreflangLinks[] hreflangLinks = scrapyCrawlerResponse.getCrawlerResponse().getHreflang_links();
		if (hreflangLinks != null && hreflangLinks.length > 0) {
			for (HreflangLinks hreflangLink : hreflangLinks) {
				//System.out.println("testRule104() lang="+hreflangLink.getLang()+",href="+hreflangLink.getHref());
				index++;
				if (index == 1) {
					assertEquals("testRule104() href1 incorrect.", "https://test.edgeseo.dev/ai_rule_104.html", hreflangLink.getHref());
				}
			}
		}
		assertEquals("testRule104() canonical incorrect.", "https://test.edgeseo.dev/canonical of rule 104", scrapyCrawlerResponse.getCrawlerResponse().getCanonical());
		System.out.println("testRule104() passed.");
	}

	private void testRule107() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_107.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule107() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule107() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule107() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		assertEquals("testRule107() title incorrect.", "title of rule 107", scrapyCrawlerResponse.getCrawlerResponse().getTitle());
		assertEquals("testRule107() h1 incorrect.", "[h1 of rule 107]", Arrays.toString(scrapyCrawlerResponse.getCrawlerResponse().getH1()));
		System.out.println("testRule107() passed.");
	}

	private void testRule108() throws Exception {
		String urlString = "https://test.edgeseo.dev/ai_rule_108.html";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testRule108() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testRule108() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testRule108() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		assertEquals("testRule108() desc incorrect.", "desc of rule 108", scrapyCrawlerResponse.getCrawlerResponse().getDescription());
		assertEquals("testRule108() h1 incorrect.", "[h1 of rule 108]", Arrays.toString(scrapyCrawlerResponse.getCrawlerResponse().getH1()));
		System.out.println("testRule108() passed.");
	}

	private void testSchemaValidation() throws Exception {
		String urlString = "https://test.edgeseo.dev/schema_validation.html";
		int totalNumberOfDivATags = 0;
		int totalNumberOfDivBTags = 0;
		int totalNumberOfDivCTags = 0;
		int totalNumberOfDivDTags = 0;
		boolean isFirstTextFound = false;
		boolean isSecondTextFound = false;
		boolean isThirdTextFound = false;
		boolean isFourthTextFound = false;
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlTrue, null);
		assertNotNull("testSchemaValidation() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertEquals("testSchemaValidation() response code incorrect.", 200, scrapyCrawlerResponse.getStatus());
		String html = scrapyCrawlerResponse.getHtml();
		//System.out.println("testSchemaValidation() html="+html);
		Document document = Jsoup.parse(html);
		Elements elements = document.select("div[attr=a]");
		if (elements != null && elements.size() > 0) {
			totalNumberOfDivATags = elements.size();
		}
		elements = document.select("div[attr=b]");
		if (elements != null && elements.size() > 0) {
			totalNumberOfDivBTags = elements.size();
		}
		elements = document.select("div[attr=c]");
		if (elements != null && elements.size() > 0) {
			totalNumberOfDivCTags = elements.size();
		}
		elements = document.select("div[attr=d]");
		if (elements != null && elements.size() > 0) {
			totalNumberOfDivDTags = elements.size();
		}
		String body = document.body().html();
		if (StringUtils.containsIgnoreCase(body, "text prepended to body")) {
			isFirstTextFound = true;
		}
		if (StringUtils.containsIgnoreCase(body, "text created after nav id mobHeaderId")) {
			isSecondTextFound = true;
		}
		if (StringUtils.containsIgnoreCase(body, "text created before a href")) {
			isThirdTextFound = true;
		}
		if (StringUtils.containsIgnoreCase(body, "text appended to body")) {
			isFourthTextFound = true;
		}

		assertEquals("testSchemaValidation() totalNumberOfDivATags incorrect.", 1, totalNumberOfDivATags);
		assertEquals("testSchemaValidation() totalNumberOfDivBTags incorrect.", 1, totalNumberOfDivBTags);
		assertEquals("testSchemaValidation() totalNumberOfDivCTags incorrect.", 1, totalNumberOfDivCTags);
		assertEquals("testSchemaValidation() totalNumberOfDivDTags incorrect.", 1, totalNumberOfDivDTags);
		assertEquals("testSchemaValidation() isFirstTextFound incorrect.", true, isFirstTextFound);
		assertEquals("testSchemaValidation() isSecondTextFound incorrect.", true, isSecondTextFound);
		assertEquals("testSchemaValidation() isThirdTextFound incorrect.", true, isThirdTextFound);
		assertEquals("testSchemaValidation() isFourthTextFound incorrect.", true, isFourthTextFound);
		System.out.println("testSchemaValidation() passed.");
	}

	private void testQueryParm1() throws Exception {
		String urlString = "https://test.edgeseo.dev/query_parms.html?parm=1";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testQueryParm1() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testQueryParm1() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testQueryParm1() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("testQueryParm1() Viewport content="+scrapyCrawlerResponse.getCrawlerResponse().getViewport_content());
		assertEquals("testQueryParm1() Robots Contents incorrect.", "robots of test case 144", scrapyCrawlerResponse.getCrawlerResponse().getRobots_contents());
		assertEquals("testQueryParm1() title incorrect.", "title of test case 144", scrapyCrawlerResponse.getCrawlerResponse().getTitle());
		assertEquals("testQueryParm1() desc incorrect.", "desc of test case 144", scrapyCrawlerResponse.getCrawlerResponse().getDescription());
		System.out.println("testQueryParm1() passed.");
	}

	private void testQueryParm2() throws Exception {
		String urlString = "https://test.edgeseo.dev/query_parms.html?newparm=2";
		Integer javascriptTimeoutInSecond = 10;
		ScrapyCrawlerResponse scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(null, null, urlString, DEFAULT_USER_AGENT, null,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, null,
				isResponseAsHtmlFalse, null);
		assertNotNull("testQueryParm2() scrapyCrawlerResponse should not be null.", scrapyCrawlerResponse);
		assertNotNull("testQueryParm2() scrapyCrawlerResponse.getCrawlerResponse() should not be null.", scrapyCrawlerResponse.getCrawlerResponse());
		assertEquals("testQueryParm2() response code incorrect.", "200", scrapyCrawlerResponse.getCrawlerResponse().getResponse_code());
		//System.out.println("testQueryParm2() Viewport content="+scrapyCrawlerResponse.getCrawlerResponse().getViewport_content());
		assertEquals("testQueryParm2() Robots Contents incorrect.", "robots of test case 144", scrapyCrawlerResponse.getCrawlerResponse().getRobots_contents());
		assertEquals("testQueryParm2() title incorrect.", "title of test case 144", scrapyCrawlerResponse.getCrawlerResponse().getTitle());
		assertEquals("testQueryParm2() desc incorrect.", "desc of test case 144", scrapyCrawlerResponse.getCrawlerResponse().getDescription());
		System.out.println("testQueryParm2() passed.");
	}
}
