package com.actonia.test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;

import java.io.File;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.io.FileUtils;

import com.actonia.IConstants;
import com.actonia.entity.UrlMetricsEntityV3;
import com.actonia.value.object.ChangeTrackingHashCdJson;
import com.google.gson.Gson;

public class ChangeTrackingHashCdJsonTest {

	public static void main(String[] args) {
		ChangeTrackingHashCdJsonTest changeTrackingHashCdJsonTest = null;
		try {
			changeTrackingHashCdJsonTest = new ChangeTrackingHashCdJsonTest();
			//changeTrackingHashCdJsonTest.testCase1();
			//changeTrackingHashCdJsonTest.testCase2();
			changeTrackingHashCdJsonTest.testDeserializeJsonToUrlMetricsEntity();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void testCase1() throws Exception {
		try {
			File inputFile = new File("C://dev//eclipse_workspace_svn_1.6//polite-crawl-maven//src//main//resources//json//change_tracking_hash_cd_json_1.json");
			List<String> testStringList = FileUtils.readLines(inputFile, IConstants.UTF_8);
			String json = testStringList.get(0);
			System.out.println("testCase1() json=" + json);
			Gson gson = new Gson();
			// serialize from JSON to Java object
			ChangeTrackingHashCdJson[] changeTrackingHashCdJsonArray = gson.fromJson(json, ChangeTrackingHashCdJson[].class);
			if (changeTrackingHashCdJsonArray != null) {
				System.out.println("testCase1() changeTrackingHashCdJsonArray.toString()=" + Arrays.toString(changeTrackingHashCdJsonArray));
				// de-serialize Java object to JSON 
				// [{"name":"name1","value":"value1"}]
				String testString = new Gson().toJson(changeTrackingHashCdJsonArray, ChangeTrackingHashCdJson[].class);
				System.out.println("testCase1() de-serialized changeTrackingHashCdJson=" + testString);
				assertEquals("testCase1() de-serialized changeTrackingHashCdJson incorrect", "[{\"name\":\"name1\",\"value\":\"value1\"}]", testString);				
			} else {
				fail("testCase1() changeTrackingHashCdJson should not be null.");
			}
			System.out.println("testCase1() passed.");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void testCase2() throws Exception {
		try {
			File inputFile = new File("C://dev//eclipse_workspace_svn_1.6//polite-crawl-maven//src//main//resources//json//change_tracking_hash_cd_json_2.json");
			List<String> testStringList = FileUtils.readLines(inputFile, IConstants.UTF_8);
			String json = testStringList.get(0);
			System.out.println("testCase2() json=" + json);
			Gson gson = new Gson();
			// serialize from JSON to Java object
			ChangeTrackingHashCdJson[] changeTrackingHashCdJsonArray = gson.fromJson(json, ChangeTrackingHashCdJson[].class);
			if (changeTrackingHashCdJsonArray != null) {
				System.out.println("testCase2() changeTrackingHashCdJsonArray.toString()=" + Arrays.toString(changeTrackingHashCdJsonArray));
				// de-serialize Java object to JSON 
				// [{"name":"name1","value":"value1"},{"name":"name2","value":"value2"}]
				String testString = new Gson().toJson(changeTrackingHashCdJsonArray, ChangeTrackingHashCdJson[].class);
				System.out.println("testCase2() de-serialized changeTrackingHashCdJson=" + testString);
				assertEquals("testCase2() de-serialized changeTrackingHashCdJson incorrect", "[{\"name\":\"name1\",\"value\":\"value1\"},{\"name\":\"name2\",\"value\":\"value2\"}]", testString);				
			} else {
				fail("testCase2() changeTrackingHashCdJson should not be null.");
			}
			System.out.println("testCase2() passed.");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void testDeserializeJsonToUrlMetricsEntity() {

		// all elements available
		try {
			File inputFile = new File("C://dev//eclipse_workspace_svn_1.6//polite-crawl-maven//src//main//resources//json//url_metrics_entity.json");
			List<String> testStringList = FileUtils.readLines(inputFile, IConstants.UTF_8);
			String json = testStringList.get(0);
			System.out.println("json1=" + json);
			Gson gson = new Gson();
			UrlMetricsEntityV3 urlMetricsEntityV3 = gson.fromJson(json, UrlMetricsEntityV3.class);
			if (urlMetricsEntityV3 != null) {
				System.out.println("deserialzied urlMetricsEntityV3=" + urlMetricsEntityV3.toString());
			} else {
				System.out.println("error--urlMetricsEntityV3 is null.");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
