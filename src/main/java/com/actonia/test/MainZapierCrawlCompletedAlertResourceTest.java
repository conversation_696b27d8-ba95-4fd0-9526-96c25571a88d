package com.actonia.test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.fail;

import com.actonia.IConstants;
import com.actonia.dao.ZapierWebhookDAO;
import com.actonia.entity.ZapierWebhookEntity;
import com.actonia.exception.ZapierException;
import com.actonia.service.AccessTokenService;
import com.actonia.service.MainZapierWebServiceClientService;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.ZapierCrawlCompletedAlert;
import com.actonia.value.object.ZapierResourceRequest;
import com.actonia.value.object.ZapierResourceResponse;
import com.google.gson.Gson;

public class MainZapierCrawlCompletedAlertResourceTest {
	private boolean isUnitTest = true;
	private MainZapierWebServiceClientService webServiceClientService;
	private ZapierWebhookDAO zapierWebhookDAO;

	public MainZapierCrawlCompletedAlertResourceTest() {
		super();
		this.webServiceClientService = SpringBeanFactory.getBean("mainZapierWebServiceClientService");
		this.zapierWebhookDAO = SpringBeanFactory.getBean("zapierWebhookDAO");
	}

	public static void main(String[] args) throws Exception {
		new MainZapierCrawlCompletedAlertResourceTest().runTests();
	}

	public void runTests() throws Exception {
		testCase1();
		testCase2();
		if (isUnitTest == true) {
			testCase3();
		}
		testCase4();
		testCase5();
		testCase8();
		testCase9();
		testCase12();
		testCase13();
		testCase14();
		testCase15();
		testCase16();
		testCase17();
		testCase18();
		testCase19();
		testCase20();
		testCase21();
		testCase22();
		testCase23();
	}

	private void testCase1() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_COMPLETED_ALERT;
		System.out.println("testCase1() requestUrl=" + requestUrl);
		String requestParameters = IConstants.EMPTY_STRING;
		ZapierException zapierException = null;
		try {
			webServiceClientService.sendHttpPostRequest(requestUrl, requestParameters);
			fail("testCase1() should throw Exception.");
		} catch (Exception e) {
			if (e instanceof ZapierException == false) {
				fail("testCase1() should throw ZapierException.");
			} else {
				zapierException = (ZapierException) e;
				assertEquals("testCase1() zapierException.getHttpStatusCode() is incorrect.", "801", String.valueOf(zapierException.getHttpStatusCode()));
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase1() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase2() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_COMPLETED_ALERT;
		System.out.println("testCase2() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierResourceRequest = new ZapierResourceRequest();
		String requestParameters = new Gson().toJson(zapierResourceRequest, ZapierResourceRequest.class);
		ZapierException zapierException = null;
		try {
			webServiceClientService.sendHttpPostRequest(requestUrl, requestParameters);
			fail("testCase2() should throw Exception.");
		} catch (Exception e) {
			if (e instanceof ZapierException == false) {
				fail("testCase2() should throw ZapierException.");
			} else {
				zapierException = (ZapierException) e;
				assertEquals("testCase2() zapierException.getHttpStatusCode() is incorrect.", "802", String.valueOf(zapierException.getHttpStatusCode()));
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase2() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase3() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_COMPLETED_ALERT;
		System.out.println("testCase3() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierResourceRequest = new ZapierResourceRequest();
		zapierResourceRequest.setAccess_token("testaccesstoken");
		String requestParameters = new Gson().toJson(zapierResourceRequest, ZapierResourceRequest.class);
		ZapierException zapierException = null;
		try {
			webServiceClientService.sendHttpPostRequest(requestUrl, requestParameters);
			fail("testCase3() should throw Exception.");
		} catch (Exception e) {
			if (e instanceof ZapierException == false) {
				fail("testCase3() should throw ZapierException.");
			} else {
				zapierException = (ZapierException) e;
				assertEquals("testCase3() zapierException.getHttpStatusCode() is incorrect.", "803", String.valueOf(zapierException.getHttpStatusCode()));
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase3() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase4() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_COMPLETED_ALERT;
		System.out.println("testCase4() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierResourceRequest = new ZapierResourceRequest();
		zapierResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		String requestParameters = new Gson().toJson(zapierResourceRequest, ZapierResourceRequest.class);
		ZapierException zapierException = null;
		try {
			webServiceClientService.sendHttpPostRequest(requestUrl, requestParameters);
			fail("testCase4() should throw Exception.");
		} catch (Exception e) {
			if (e instanceof ZapierException == false) {
				fail("testCase4() should throw ZapierException.");
			} else {
				zapierException = (ZapierException) e;
				assertEquals("testCase4() zapierException.getHttpStatusCode() is incorrect.", "804", String.valueOf(zapierException.getHttpStatusCode()));
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase4() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase5() {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_COMPLETED_ALERT;
		System.out.println("testCase5() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierResourceRequest = new ZapierResourceRequest();
		zapierResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		zapierResourceRequest.setDomain_id(1);
		String requestParameters = new Gson().toJson(zapierResourceRequest, ZapierResourceRequest.class);
		ZapierException zapierException = null;
		try {
			webServiceClientService.sendHttpPostRequest(requestUrl, requestParameters);
			fail("testCase5() should throw Exception.");
		} catch (Exception e) {
			if (e instanceof ZapierException == false) {
				fail("testCase5() should throw ZapierException.");
			} else {
				zapierException = (ZapierException) e;
				assertEquals("testCase5() zapierException.getHttpStatusCode() is incorrect.", "805", String.valueOf(zapierException.getHttpStatusCode()));
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase5() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase8() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_COMPLETED_ALERT;
		System.out.println("testCase8() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierResourceRequest = new ZapierResourceRequest();
		zapierResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		zapierResourceRequest.setDomain_id(1701);
		String requestParameters = new Gson().toJson(zapierResourceRequest, ZapierResourceRequest.class);
		ZapierException zapierException = null;
		try {
			webServiceClientService.sendHttpPostRequest(requestUrl, requestParameters);
			fail("testCase8() should throw Exception.");
		} catch (Exception e) {
			if (e instanceof ZapierException == false) {
				fail("testCase8() should throw ZapierException.");
			} else {
				zapierException = (ZapierException) e;
				assertEquals("testCase8() zapierException.getHttpStatusCode() is incorrect.", "806", String.valueOf(zapierException.getHttpStatusCode()));
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase8() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase9() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_COMPLETED_ALERT;
		System.out.println("testCase9() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierResourceRequest = new ZapierResourceRequest();
		zapierResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		zapierResourceRequest.setDomain_id(1701);
		zapierResourceRequest.setUser_email("<EMAIL>");
		String requestParameters = new Gson().toJson(zapierResourceRequest, ZapierResourceRequest.class);
		ZapierException zapierException = null;
		try {
			webServiceClientService.sendHttpPostRequest(requestUrl, requestParameters);
			fail("testCase9() should throw Exception.");
		} catch (Exception e) {
			if (e instanceof ZapierException == false) {
				fail("testCase9() should throw ZapierException.");
			} else {
				zapierException = (ZapierException) e;
				assertEquals("testCase9() zapierException.getHttpStatusCode() is incorrect.", "807", String.valueOf(zapierException.getHttpStatusCode()));
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase9() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase12() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_COMPLETED_ALERT;
		System.out.println("testCase12() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierResourceRequest = new ZapierResourceRequest();
		zapierResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		zapierResourceRequest.setDomain_id(1701);
		zapierResourceRequest.setUser_email("<EMAIL>");
		String requestParameters = new Gson().toJson(zapierResourceRequest, ZapierResourceRequest.class);
		ZapierException zapierException = null;
		try {
			webServiceClientService.sendHttpPostRequest(requestUrl, requestParameters);
			fail("testCase12() should throw Exception.");
		} catch (Exception e) {
			if (e instanceof ZapierException == false) {
				fail("testCase12() should throw ZapierException.");
			} else {
				zapierException = (ZapierException) e;
				assertEquals("testCase12() zapierException.getHttpStatusCode() is incorrect.", "808", String.valueOf(zapierException.getHttpStatusCode()));
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase12() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase13() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_COMPLETED_ALERT;
		System.out.println("testCase13() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierResourceRequest = new ZapierResourceRequest();
		zapierResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		zapierResourceRequest.setDomain_id(1701);
		zapierResourceRequest.setUser_email("<EMAIL>");
		zapierResourceRequest.setCallback_url("test");
		String requestParameters = new Gson().toJson(zapierResourceRequest, ZapierResourceRequest.class);
		ZapierException zapierException = null;
		try {
			webServiceClientService.sendHttpPostRequest(requestUrl, requestParameters);
			fail("testCase13() should throw Exception.");
		} catch (Exception e) {
			if (e instanceof ZapierException == false) {
				fail("testCase13() should throw ZapierException.");
			} else {
				zapierException = (ZapierException) e;
				assertEquals("testCase13() zapierException.getHttpStatusCode() is incorrect.", "809", String.valueOf(zapierException.getHttpStatusCode()));
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase13() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase14() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		ZapierWebhookEntity zapierWebhookEntity = null;
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_COMPLETED_ALERT;
		System.out.println("testCase14() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierResourceRequest = new ZapierResourceRequest();
		zapierResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		zapierResourceRequest.setDomain_id(1701);
		zapierResourceRequest.setUser_email("<EMAIL>");
		zapierResourceRequest.setCallback_url("https://hook.zapier.com/callback_url_1");
		String requestParameters = new Gson().toJson(zapierResourceRequest, ZapierResourceRequest.class);

		ZapierResourceResponse zapierResourceResponse = webServiceClientService.sendHttpPostRequest(requestUrl, requestParameters);
		assertNotNull("testCase14() zapierResourceResponse should not be null.", zapierResourceResponse);
		assertNotNull("testCase14() zapierResourceResponse.getId() should not be null.", zapierResourceResponse.getId());
		if (isUnitTest == true) {
			zapierWebhookEntity = zapierWebhookDAO.get(zapierResourceResponse.getId());
			assertNotNull("testCase14() zapierWebhookEntity should not be null.", zapierWebhookEntity);
			assertEquals("testCase14() zapierWebhookEntity.getDomainId() is incorrect.", "1701", String.valueOf(zapierWebhookEntity.getDomainId()));
			assertEquals("testCase14() zapierWebhookEntity.getUserId() is incorrect.", "214", String.valueOf(zapierWebhookEntity.getUserId()));
			assertEquals("testCase14() zapierWebhookEntity.getTriggerType() is incorrect.", "3", String.valueOf(zapierWebhookEntity.getTriggerType()));
			assertEquals("testCase14() zapierWebhookEntity.getCallbackUrl() is incorrect.", "https://hook.zapier.com/callback_url_1",
					String.valueOf(zapierWebhookEntity.getCallbackUrl()));
			zapierWebhookDAO.delete(zapierResourceResponse.getId());
		}
		FormatUtils.getInstance().logMemoryUsage("testCase14() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase15() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_COMPLETED_ALERT;
		System.out.println("testCase15() requestUrl=" + requestUrl);
		ZapierException zapierException = null;
		try {
			webServiceClientService.sendHttpDeleteRequest(requestUrl);
			fail("testCase15() should throw Exception.");
		} catch (Exception e) {
			if (e instanceof ZapierException == false) {
				fail("testCase15() should throw ZapierException.");
			} else {
				zapierException = (ZapierException) e;
				assertEquals("testCase15() zapierException.getHttpStatusCode() is incorrect.", "802", String.valueOf(zapierException.getHttpStatusCode()));
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase15() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase16() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_COMPLETED_ALERT
				+ "?access_token=testaccesstoken";
		System.out.println("testCase16() requestUrl=" + requestUrl);
		ZapierException zapierException = null;
		try {
			webServiceClientService.sendHttpDeleteRequest(requestUrl);
			fail("testCase16() should throw Exception.");
		} catch (Exception e) {
			if (e instanceof ZapierException == false) {
				fail("testCase16() should throw ZapierException.");
			} else {
				zapierException = (ZapierException) e;
				assertEquals("testCase16() zapierException.getHttpStatusCode() is incorrect.", "803", String.valueOf(zapierException.getHttpStatusCode()));
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase16() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase17() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_COMPLETED_ALERT
				+ "?access_token=" + AccessTokenService.INTERNAL_KEY;
		System.out.println("testCase17() requestUrl=" + requestUrl);
		ZapierException zapierException = null;
		try {
			webServiceClientService.sendHttpDeleteRequest(requestUrl);
			fail("testCase17() should throw Exception.");
		} catch (Exception e) {
			if (e instanceof ZapierException == false) {
				fail("testCase17() should throw ZapierException.");
			} else {
				zapierException = (ZapierException) e;
				assertEquals("testCase17() zapierException.getHttpStatusCode() is incorrect.", "812", String.valueOf(zapierException.getHttpStatusCode()));
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase17() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase18() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_COMPLETED_ALERT
				+ "?access_token=" + AccessTokenService.INTERNAL_KEY + "&id=abcd";
		System.out.println("testCase18() requestUrl=" + requestUrl);
		ZapierException zapierException = null;
		try {
			webServiceClientService.sendHttpDeleteRequest(requestUrl);
			fail("testCase18() should throw Exception.");
		} catch (Exception e) {
			if (e instanceof ZapierException == false) {
				fail("testCase18() should throw ZapierException.");
			} else {
				zapierException = (ZapierException) e;
				assertEquals("testCase18() zapierException.getHttpStatusCode() is incorrect.", "813", String.valueOf(zapierException.getHttpStatusCode()));
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase18() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase19() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_COMPLETED_ALERT
				+ "?access_token=" + AccessTokenService.INTERNAL_KEY + "&id=168168";
		System.out.println("testCase19() requestUrl=" + requestUrl);
		ZapierException zapierException = null;
		try {
			webServiceClientService.sendHttpDeleteRequest(requestUrl);
			fail("testCase19() should throw Exception.");
		} catch (Exception e) {
			if (e instanceof ZapierException == false) {
				fail("testCase19() should throw ZapierException.");
			} else {
				zapierException = (ZapierException) e;
				assertEquals("testCase19() zapierException.getHttpStatusCode() is incorrect.", "813", String.valueOf(zapierException.getHttpStatusCode()));
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase19() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase20() throws Exception {
		long startTimestamp = System.currentTimeMillis();

		// step 1: create subscription
		ZapierWebhookEntity zapierWebhookEntity = null;
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_COMPLETED_ALERT;
		System.out.println("testCase20() requestUrl=" + requestUrl);
		ZapierResourceRequest zapierResourceRequest = new ZapierResourceRequest();
		zapierResourceRequest.setAccess_token(AccessTokenService.INTERNAL_KEY);
		zapierResourceRequest.setDomain_id(1701);
		zapierResourceRequest.setUser_email("<EMAIL>");
		zapierResourceRequest.setCallback_url("https://hook.zapier.com/callback_url_1");
		String requestParameters = new Gson().toJson(zapierResourceRequest, ZapierResourceRequest.class);

		ZapierResourceResponse zapierResourceResponse = webServiceClientService.sendHttpPostRequest(requestUrl, requestParameters);
		assertNotNull("testCase20() zapierResourceResponse should not be null.", zapierResourceResponse);
		assertNotNull("testCase20() zapierResourceResponse.getId() should not be null.", zapierResourceResponse.getId());
		if (isUnitTest == true) {
			zapierWebhookEntity = zapierWebhookDAO.get(zapierResourceResponse.getId());
			assertNotNull("testCase20() zapierWebhookEntity should not be null.", zapierWebhookEntity);
			assertEquals("testCase20() zapierWebhookEntity.getDomainId() is incorrect.", "1701", String.valueOf(zapierWebhookEntity.getDomainId()));
			assertEquals("testCase20() zapierWebhookEntity.getUserId() is incorrect.", "214", String.valueOf(zapierWebhookEntity.getUserId()));
			assertEquals("testCase20() zapierWebhookEntity.getTriggerType() is incorrect.", "3", String.valueOf(zapierWebhookEntity.getTriggerType()));
			assertEquals("testCase20() zapierWebhookEntity.getCallbackUrl() is incorrect.", "https://hook.zapier.com/callback_url_1",
					String.valueOf(zapierWebhookEntity.getCallbackUrl()));
		}

		// step 2: delete subscription		
		requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_COMPLETED_ALERT + "?access_token="
				+ AccessTokenService.INTERNAL_KEY + "&id=" + zapierResourceResponse.getId();
		System.out.println("testCase20() requestUrl=" + requestUrl);
		zapierResourceResponse = webServiceClientService.sendHttpDeleteRequest(requestUrl);
		assertNotNull("testCase20() zapierResourceResponse should not be null.", zapierResourceResponse);
		String json = new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class);
		System.out.println("testCase20() json=" + json);
		FormatUtils.getInstance().logMemoryUsage("testCase20() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase21() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_COMPLETED_ALERT;
		System.out.println("testCase21() requestUrl=" + requestUrl);
		ZapierException zapierException = null;
		try {
			webServiceClientService.getZapierCrawlCompletedAlerts(requestUrl);
			fail("testCase21() should throw Exception.");
		} catch (Exception e) {
			if (e instanceof ZapierException == false) {
				fail("testCase21() should throw ZapierException.");
			} else {
				zapierException = (ZapierException) e;
				assertEquals("testCase21() zapierException.getHttpStatusCode() is incorrect.", "802", String.valueOf(zapierException.getHttpStatusCode()));
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase21() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase22() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_COMPLETED_ALERT
				+ "?access_token=testaccesstoken";
		System.out.println("testCase22() requestUrl=" + requestUrl);
		ZapierException zapierException = null;
		try {
			webServiceClientService.getZapierCrawlCompletedAlerts(requestUrl);
			fail("testCase22() should throw Exception.");
		} catch (Exception e) {
			if (e instanceof ZapierException == false) {
				fail("testCase22() should throw ZapierException.");
			} else {
				zapierException = (ZapierException) e;
				assertEquals("testCase22() zapierException.getHttpStatusCode() is incorrect.", "803", String.valueOf(zapierException.getHttpStatusCode()));
			}
		}
		FormatUtils.getInstance().logMemoryUsage("testCase22() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testCase23() throws Exception {
		long startTimestamp = System.currentTimeMillis();

		// retrieve sample content guard alert messages
		String requestUrl = webServiceClientService.getMainWebServiceEndPoint() + MainZapierWebServiceClientService.ROUTER_ZAPIER + IConstants.COMMAND_CRAWL_COMPLETED_ALERT
				+ "?access_token=" + AccessTokenService.INTERNAL_KEY;
		System.out.println("testCase23() requestUrl=" + requestUrl);
		ZapierCrawlCompletedAlert[] zapierCrawlCompletedAlertArray = webServiceClientService.getZapierCrawlCompletedAlerts(requestUrl);
		assertNotNull("testCase23() zapierCrawlCompletedAlertArray should not be null.", zapierCrawlCompletedAlertArray);

		// first alert
		assertEquals("testCase23() zapierCrawlCompletedAlertArray[0].getId() is incorrect.", "1614180689836", zapierCrawlCompletedAlertArray[0].getId());
		assertEquals("testCase23() zapierCrawlCompletedAlertArray[0].getCrawl_start_timestamp() is incorrect.", "2021-02-24 09:28:03",
				zapierCrawlCompletedAlertArray[0].getCrawl_start_timestamp());
		assertEquals("testCase23() zapierCrawlCompletedAlertArray[0].getDomain_id() is incorrect.", "123456",
				String.valueOf(zapierCrawlCompletedAlertArray[0].getDomain_id()));
		assertEquals("testCase23() zapierCrawlCompletedAlertArray[0].getDomain_name() is incorrect.", "www.testdomain1.com",
				zapierCrawlCompletedAlertArray[0].getDomain_name());
		assertEquals("testCase23() zapierCrawlCompletedAlertArray[0].getRequest_id() is incorrect.", "234567",
				String.valueOf(zapierCrawlCompletedAlertArray[0].getRequest_id()));
		assertEquals("testCase23() zapierCrawlCompletedAlertArray[0].getRequester_email() is incorrect.", "<EMAIL>",
				zapierCrawlCompletedAlertArray[0].getRequester_email());
		assertEquals("testCase23() zapierCrawlCompletedAlertArray[0].getProject() is incorrect.", "project name 1", zapierCrawlCompletedAlertArray[0].getProject());
		assertEquals("testCase23() zapierCrawlCompletedAlertArray[0].getLanguage() is incorrect.", "English", zapierCrawlCompletedAlertArray[0].getLanguage());
		assertEquals("testCase23() zapierCrawlCompletedAlertArray[0].getWhat_to_crawl() is incorrect.", "URL", zapierCrawlCompletedAlertArray[0].getWhat_to_crawl());
		assertEquals("testCase23() zapierCrawlCompletedAlertArray[0].getStarting_url() is incorrect.", "https://www.testdomain1.com",
				zapierCrawlCompletedAlertArray[0].getStarting_url());
		assertEquals("testCase23() zapierCrawlCompletedAlertArray[0].getCrawl_type() is incorrect.", "non-Javascript",
				zapierCrawlCompletedAlertArray[0].getCrawl_type());
		assertEquals("testCase23() zapierCrawlCompletedAlertArray[0].getCrawl_speed() is incorrect.", "3",
				String.valueOf(zapierCrawlCompletedAlertArray[0].getCrawl_speed()));
		assertEquals("testCase23() zapierCrawlCompletedAlertArray[0].getCrawl_depth() is incorrect.", "8",
				String.valueOf(zapierCrawlCompletedAlertArray[0].getCrawl_depth()));
		assertEquals("testCase23() zapierCrawlCompletedAlertArray[0].getCrawl_starting_url_status_code() is incorrect.", "120",
				zapierCrawlCompletedAlertArray[0].getCrawl_starting_url_status_code());
		assertEquals("testCase23() zapierCrawlCompletedAlertArray[0].getCrawl_starting_url_status_message() is incorrect.",
				"Starting URL blocked by Robots.txt instructions : https://www.plusmore.com",
				String.valueOf(zapierCrawlCompletedAlertArray[0].getCrawl_starting_url_status_message()));
		assertEquals("testCase23() zapierCrawlCompletedAlertArray[0].getError_code() is incorrect.", "", zapierCrawlCompletedAlertArray[0].getError_code());
		assertEquals("testCase23() zapierCrawlCompletedAlertArray[0].getError_message() is incorrect.", "", zapierCrawlCompletedAlertArray[0].getError_message());

		// second alert
		assertEquals("testCase23() zapierCrawlCompletedAlertArray[1].getId() is incorrect.", "1614234567898", zapierCrawlCompletedAlertArray[1].getId());
		assertEquals("testCase23() zapierCrawlCompletedAlertArray[1].getCrawl_start_timestamp() is incorrect.", "2021-02-25 16:33:08",
				zapierCrawlCompletedAlertArray[1].getCrawl_start_timestamp());
		assertEquals("testCase23() zapierCrawlCompletedAlertArray[1].getDomain_id() is incorrect.", "345678",
				String.valueOf(zapierCrawlCompletedAlertArray[1].getDomain_id()));
		assertEquals("testCase23() zapierCrawlCompletedAlertArray[1].getDomain_name() is incorrect.", "www.testdomain2.com",
				zapierCrawlCompletedAlertArray[1].getDomain_name());
		assertEquals("testCase23() zapierCrawlCompletedAlertArray[1].getRequest_id() is incorrect.", "567890",
				String.valueOf(zapierCrawlCompletedAlertArray[1].getRequest_id()));
		assertEquals("testCase23() zapierCrawlCompletedAlertArray[1].getRequester_email() is incorrect.", "<EMAIL>",
				zapierCrawlCompletedAlertArray[1].getRequester_email());
		assertEquals("testCase23() zapierCrawlCompletedAlertArray[1].getProject() is incorrect.", "project name 2", zapierCrawlCompletedAlertArray[1].getProject());
		assertEquals("testCase23() zapierCrawlCompletedAlertArray[1].getLanguage() is incorrect.", "French", zapierCrawlCompletedAlertArray[1].getLanguage());
		assertEquals("testCase23() zapierCrawlCompletedAlertArray[1].getWhat_to_crawl() is incorrect.", "URL", zapierCrawlCompletedAlertArray[1].getWhat_to_crawl());
		assertEquals("testCase23() zapierCrawlCompletedAlertArray[1].getStarting_url() is incorrect.", "https://www.testdomain2.com",
				zapierCrawlCompletedAlertArray[1].getStarting_url());
		assertEquals("testCase23() zapierCrawlCompletedAlertArray[1].getCrawl_type() is incorrect.", "Javascript", zapierCrawlCompletedAlertArray[1].getCrawl_type());
		assertEquals("testCase23() zapierCrawlCompletedAlertArray[1].getCrawl_speed() is incorrect.", "2",
				String.valueOf(zapierCrawlCompletedAlertArray[1].getCrawl_speed()));
		assertEquals("testCase23() zapierCrawlCompletedAlertArray[1].getCrawl_depth() is incorrect.", "6",
				String.valueOf(zapierCrawlCompletedAlertArray[1].getCrawl_depth()));
		assertEquals("testCase23() zapierCrawlCompletedAlertArray[1].getCrawl_starting_url_status_code() is incorrect.", "",
				zapierCrawlCompletedAlertArray[1].getCrawl_starting_url_status_code());
		assertEquals("testCase23() zapierCrawlCompletedAlertArray[1].getCrawl_starting_url_status_message() is incorrect.", "",
				zapierCrawlCompletedAlertArray[1].getCrawl_starting_url_status_message());
		assertEquals("testCase23() zapierCrawlCompletedAlertArray[1].getError_code() is incorrect.", "", zapierCrawlCompletedAlertArray[1].getError_code());
		assertEquals("testCase23() zapierCrawlCompletedAlertArray[1].getError_message() is incorrect.", "", zapierCrawlCompletedAlertArray[1].getError_message());

		FormatUtils.getInstance().logMemoryUsage("testCase23() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

}