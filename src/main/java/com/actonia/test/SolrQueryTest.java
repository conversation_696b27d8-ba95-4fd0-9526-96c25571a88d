package com.actonia.test;

import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.net.URL;
import java.net.URLEncoder;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;

import com.actonia.IConstants;
import com.actonia.dao.CompetitorUrlHtmlClickHouseDAO;
import com.actonia.entity.AdditionalContentEntity;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.UrlMetricsEntityV3;
import com.actonia.utils.CommonUtils;
import com.actonia.utils.CrawlerUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.PoliteCrawlConfigurations;
import com.actonia.utils.SQSUtils;
import com.actonia.value.object.CrawlerResponse;
import com.actonia.value.object.PageAnalysisResult;
import com.actonia.value.object.PageAnalysisResultChgInd;
import com.actonia.value.object.ScrapyCrawlerResponse;
import com.actonia.value.object.UrlCrawlParametersVO;
import com.amazonaws.services.sqs.model.Message;
import com.google.gson.Gson;
import com.google.gson.JsonObject;

public class SolrQueryTest {
	private static final String MD5 = "MD5";
	private static final String STRING_ZERO = "0";
	private static final String EMPTY_STRING = "";
	private static final String UTF_8 = "utf-8";
	private static final String NEWLINE = "";
	private static final String TAB = "\t";
	private static final String NO_BREAK_SPACE = "\u00A0";
	private static final String ANY_SPACES = " +";
	private static final String ONE_SPACE = " ";
	private static final int MAX_SOLR_RETRY_COUNT = 1;

	private static final String USER_AGENT = "Mozilla/5.0 (compatible; linkCheckV3.0)";
	private static final String LOCATION = "location";
	private static final int MAXIMUM_NUMBER_OF_REDIRECTS_TO_FOLLOW = 3;

	private static final String OUTPUT_FILE_PATH_1 = "/home/<USER>/source/test_site_clarity_batch/output/";
	private static final String OUTPUT_FILE_PATH_2 = ".txt";
	private static final String DELIMITER = "~";
	private static final String REPLACEMENT_CHAR = "ï¿½";

	private List<String> COMPETITOR_URL_HTML_PREVIOUS_FIELD_NAME_LIST;

	//private RobotsExclusionService robotsExclusionService;

	public SolrQueryTest() {
		super();
		//this.robotsExclusionService = SpringBeanFactory.getBean("robotsExclusionService");
	}

	public static void main(String[] args) throws Exception {
		//new SolrQueryTest().testScrapyApi(args);

		//new SolrQueryTest().testLongConversion();

		//new SolrQueryTest().testBooleanUtils();

		//new SolrQueryTest().testMd5OfDifferenceCases();

		//new SolrQueryTest().testPageAnalysisRulesVersion1_0_11();

		//new SolrQueryTest().testConvertPageAnalysisChangeIndJson();

		//new SolrQueryTest().testGetPageAnalysisResultsChgIndJson();

		//new SolrQueryTest().testNormalizedUrlHashCode();

		//new SolrQueryTest().testSerializedUrlUrlMetricsEntityV3();

		//new SolrQueryTest().testUpdateChangeTrackingChangeIndicator();

		//new SolrQueryTest().testGetFacebookSharedCounts();

		//new SolrQueryTest().testGson();

		//new SolrQueryTest().createTestData();

		//new SolrQueryTest().testGetByDocumentId();
		//new SolrQueryTest().resetTestData(primaryServer);

		// new SolrQueryTest().testCacheAdditionalContentToBeTracked();
		// new SolrQueryTest().test1();
		// new SolrQueryTest().test2();
		//new SolrQueryTest().resetTestSolrData();
		//new SolrQueryTest().resetRemoteSolrData();
		//new SolrQueryTest().testGetHtmlDocumentsByTrackDate();
		//new SolrQueryTest().testGetDocumentsByCore();
		// new SolrQueryTest().testGetSharedCountsDocumentsByTrackDate();
		// new SolrQueryTest().testGetLastHtmlDocumentByHashCodeStat("associated_urls_html","2a9b8f9f45b9d926e83b69ad33b52f83");		

		// new SolrQueryTest().testGetLastHtmlDocumentByHashCodeSort("associated_urls_html",
		// "2a9b8f9f45b9d926e83b69ad33b52f83");
		//new SolrQueryTest().testUpdateDocument();
		// new SolrQueryTest().testGetHtmlDocumentsByHashCode("e043b3cc8272e56c7a844d60c290ca54", "en");
		// new SolrQueryTest().testGetSharedCountsDocumentsFacet();
		// new SolrQueryTest().testGetByTrackDateWithoutMajesticData();
		// new SolrQueryTest().testGetGooglePlusHttpClient();
		// new SolrQueryTest().testGetSharedCountsDocumentsByUrl();
		//new SolrQueryTest().testDeleteByQuery();
		// new SolrQueryTest().testTermFrequency();
		// new SolrQueryTest().optimizeSolrCore();
		// new SolrQueryTest().testGetLastHtmlDocumentsWithHashCodes();
		// new SolrQueryTest().testFindInetAddressByIp();
		// new
		// SolrQueryTest().getHtmlDocumentsByHashCodeWeekNumber("38db42a8fe27a4d77d9daade07344edb",
		// 201443, "associated_urls_html");
		// new SolrQueryTest().testUrlMetricsSolrDao();
		// new SolrQueryTest().disableReplication(primaryServer);
		// new SolrQueryTest().enableReplication(primaryServer);
		// new SolrQueryTest().enablePoll(secondaryServer1);
		// new SolrQueryTest().disablePoll(secondaryServer2);
		// new SolrQueryTest().calculateTotalNumberOfDocuments();
		// new
		// SolrQueryTest().testGetPageRank("http://www.treeclassics.com/Pre-Lit-Lighted-Artificial-Christmas-Trees-s/230.htm");
		// new
		// SolrQueryTest().testGetPageAuthority("http://www.treeclassics.com/Pre-Lit-Lighted-Artificial-Christmas-Trees-s/230.htm",
		// "mozscape-a6c126ddb5", "fbf8a87c07be585b50e819624a3091e4");
		// new
		// SolrQueryTest().testGetPageAuthority("http://www.treeclassics.com/",
		// "mozscape-a6c126ddb5", "fbf8a87c07be585b50e819624a3091e4");
		// new SolrQueryTest().testUrlCrawlParametersVO();
		//new SolrQueryTest().testHttpsURLConnection();
		// new SolrQueryTest().testCrawlLinkedIn();
		// new SolrQueryTest().testGetDuplicateUrlsWithSameTitle();
		// new SolrQueryTest().writeJsonToFile();
		// new SolrQueryTest().testFinalUrlCrawler();
		// new SolrQueryTest().testIsChanged();
		// new SolrQueryTest().testSimHash();
		// new SolrQueryTest().testTextExtraction();
		// new SolrQueryTest().testPrintUrlSimilarity();
		// new SolrQueryTest().testRetrieveAdwordsFile();
		// new SolrQueryTest().createSearchVolumeSolrCloudCore();
		// new SolrQueryTest().tallyCores();
		//new SolrQueryTest().writeOutputToTabDelimitedFile();
		//new SolrQueryTest().wrike_ticket_112200469();
		//new SolrQueryTest().testTimeFormatting();
		//new SolrQueryTest().testCalculateEndTimeStartEnd();
		//new SolrQueryTest().testProcessBuilder();
		//new SolrQueryTest().testCrawlFacebook();
		//new SolrQueryTest().testContainsIgnoreCase();
		//new SolrQueryTest().testCrawlFacebookGraphApi();
		//new SolrQueryTest().testHttpProxy();
		//new SolrQueryTest().testGetLastPageAnalysisResultsHashCode();
		//new SolrQueryTest().testStartsWith();
		//new SolrQueryTest().testGetPriorityOneMessage();
		//new SolrQueryTest().testCreateCityHash();
		//new SolrQueryTest().testReverseDomainNameByDot();
		//new SolrQueryTest().testRemoveLastDomainNameSegment();
		//new SolrQueryTest().testSharedCountsClickHouseDAOGetList();
		//new SolrQueryTest().testGetLastSundayDate();
		//new SolrQueryTest().testParseCityHashUrl();
		//new SolrQueryTest().testGetTopFolders();
		//new SolrQueryTest().testGetDomainName();
		//new SolrQueryTest().testGetTopLevelDomain();
		//new SolrQueryTest().testIntLinkDelete();
		//new SolrQueryTest().testStartAndEndWith();
		//new SolrQueryTest().testGetFinalUrl();
		//new SolrQueryTest().testTrimUrlText();
		//new SolrQueryTest().testGenerateProjectId();
		//new SolrQueryTest().testRemoveSpreadsheetVersion();
		//new SolrQueryTest().testWriteToLogFile();
		//new SolrQueryTest().testCheckUrlQueryString();
		//new SolrQueryTest().testCompareDomains();
		//new SolrQueryTest().testCharset();
		//new SolrQueryTest().testApacheCrawlerResponse();
		//new SolrQueryTest().testTrimText();
		//new SolrQueryTest().testBlockedByRobotsTxt();
		//new SolrQueryTest().testWhenRobotsTxtAvailable();
		//new SolrQueryTest().testWhenRobotsTxtAvailable();
		//new SolrQueryTest().testCompetitorUrlHtmlGetPrevious();
		//new SolrQueryTest().testCheckIfStopCrawl();
	}

	private CloseableHttpClient getProxiedHttpClient() {
		String proxyIpAddress = "***************";
		Integer proxyPortNumber = 4040;
		org.apache.http.HttpHost proxy = new org.apache.http.HttpHost(proxyIpAddress, proxyPortNumber);
		FormatUtils.getInstance().logMemoryUsage("getProxiedHttpClient() proxyIpAddress=" + proxyIpAddress + ",proxyPortNumber=" + proxyPortNumber);
		org.apache.http.client.CredentialsProvider credentialsProvider = new org.apache.http.impl.client.BasicCredentialsProvider();
		credentialsProvider.setCredentials(org.apache.http.auth.AuthScope.ANY, new org.apache.http.auth.UsernamePasswordCredentials("usr1747165", "asdf09Sf90nSfg"));
		CloseableHttpClient httpclient = HttpClients.custom().setProxy(proxy).setDefaultCredentialsProvider(credentialsProvider).build();
		return httpclient;
	}

	private void testStartsWith() {
		String prefix = "tleti_";
		String testString = "title_5aa64e145da1a7ca89a896d9e9523c10";
		if (StringUtils.startsWith(testString, prefix)) {
			System.out.println(testString + " starts with " + prefix);
		} else {
			System.out.println(testString + " not starts with " + prefix);
		}
	}

	private String[] getLanguageCodes() {
		return new String[] { "am", "ar", "az", "bg", "cn", "cs", "da", "de", "el", "en", "es", "es-PA", "fi", "fr", "he", "hr", "hu", "id", "it", "ja", "ka", "mt",
				"nl", "no", "pl", "pt", "ro", "ru", "sk", "sq", "sr", "sv", "sw", "tg", "th", "tl", "tr", "uk", "zh", "zh-tw", "ko", "vi", };
	}

	private void testReverseDomainNameByDot() throws Exception {
		String domainName = "www.petvacationhomes.com";
		String reversedDomainName = CrawlerUtils.getInstance().getReversedDomainName(domainName);
		System.out.println("domainName=" + domainName + ",reversedDomainName=" + reversedDomainName);
	}

	private void testRemoveLastDomainNameSegment() {
		//		String reversedDomainNameWithoutLastSegment = null;
		//		String reversedDomainName = "com.petvacationhomes.www";
		//		int indexOfLastDomainDot = StringUtils.lastIndexOf(reversedDomainName, IConstants.DOMAIN_DOT);
		//		if (indexOfLastDomainDot != -1) {
		//			reversedDomainNameWithoutLastSegment = StringUtils.substring(reversedDomainName, 0, indexOfLastDomainDot);
		//			System.out.println("reversedDomainName="+reversedDomainName+",indexOfLastDomainDot="+indexOfLastDomainDot+",reversedDomainNameWithoutLastSegment="+reversedDomainNameWithoutLastSegment);
		//		} else {
		//			System.out.println("reversedDomainName="+reversedDomainName+",indexOfLastDomainDot="+indexOfLastDomainDot);			
		//		}

		String[] segmentArray = null;
		//String urlString = "https://www.petvacationhomes.com";
		//String urlString = "https://www.petvacationhomes.com/";
		//String urlString = "https://www.petvacationhomes.com/index.html";
		//String urlString = "https://www.petvacationhomes.com/segment1/index.html";
		//String urlString = "https://www.petvacationhomes.com/segment1/segement2";
		//String urlString = "https://www.petvacationhomes.com/segment1/segment2/index.html";
		//String urlString = "https://www.petvacationhomes.com/segment1/segment2/segment3";
		//String urlString = "https://www.petvacationhomes.com/segment1/segment2/segment3/index.html";
		//String urlString = "http://www.caranddriver.com/nissan/rogue";
		String urlString = "http://www.baidu.com/";
		//String urlString = "http://www.baidu.com/link?url=wDsvK_38uGT7xTMNnG9v-InchLDEBY0PyaRUjPb6aHyhi8n1YlrZdsGWPcAPEu7G#anchor1";
		String protocol = null;
		URL url = null;
		String uri = null;
		String folder1 = null;
		String folder2 = null;
		String hostname = null;
		String testString = null;
		int extractLength = 0;
		try {
			url = new URL(urlString);
			protocol = url.getProtocol();
			hostname = url.getHost();
			if (StringUtils.equalsIgnoreCase(protocol, IConstants.PROTOCOL_HTTP) || StringUtils.equalsIgnoreCase(protocol, IConstants.PROTOCOL_HTTPS)) {
				testString = protocol + IConstants.COLON_SLASH_SLASH + hostname;
				uri = urlString.substring(testString.length());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		System.out.println("url=" + urlString);
		System.out.println("testString=" + testString);
		System.out.println("uri=" + uri);
	}

	private boolean checkIfLastSegmentContainsAPeriod(String[] segmentArray) {
		boolean output = false;
		int totalSegments = segmentArray.length;
		String finalSegment = segmentArray[totalSegments - 1];
		if (StringUtils.containsIgnoreCase(finalSegment, IConstants.DOT_STRING)) {
			output = true;
		}
		return output;
	}

	private void testGetLastSundayDate() {

		Date lastSundayDate = null;

		//String currentDateString = "2017-06-04"; // Sunday
		//String currentDateString = "2017-06-05"; // Monday
		//String currentDateString = "2017-06-06"; // Tuesday
		//String currentDateString = "2017-06-07"; // Wednesday
		//String currentDateString = "2017-06-08"; // Thursday
		//String currentDateString = "2017-06-09"; // Friday
		//String currentDateString = "2017-06-10"; // Saturday
		String currentDateString = "2017-06-11"; // next Sunday
		int dayOfWeek = 0;
		Date currentDate = null;
		Calendar calendar = null;

		try {
			currentDate = DateUtils.parseDate(currentDateString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
			calendar = new GregorianCalendar();
			calendar.setTime(currentDate);

			dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);

			if (dayOfWeek == Calendar.SUNDAY) {
				System.out.println("currentDateString " + currentDateString + " is Sunday.");
				lastSundayDate = currentDate;
			} else if (dayOfWeek == Calendar.SATURDAY) {
				System.out.println("currentDateString " + currentDateString + " is Saturday.");
			} else if (dayOfWeek == Calendar.FRIDAY) {
				System.out.println("currentDateString " + currentDateString + " is Friday.");
			} else if (dayOfWeek == Calendar.THURSDAY) {
				System.out.println("currentDateString " + currentDateString + " is Thursday.");
			} else if (dayOfWeek == Calendar.WEDNESDAY) {
				System.out.println("currentDateString " + currentDateString + " is Wednesday.");
			} else if (dayOfWeek == Calendar.TUESDAY) {
				System.out.println("currentDateString " + currentDateString + " is Tuesday.");
			} else if (dayOfWeek == Calendar.MONDAY) {
				System.out.println("currentDateString " + currentDateString + " is Monday.");
			} else {
				System.out.println("currentDateString " + currentDateString + " is Unknown.");
			}
			while (lastSundayDate == null) {
				currentDate = DateUtils.addDays(currentDate, -1);
				calendar = new GregorianCalendar();
				calendar.setTime(currentDate);
				dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
				if (dayOfWeek == Calendar.SUNDAY) {
					lastSundayDate = currentDate;
				}
			}
			System.out.println("lastSundayDate=" + DateFormatUtils.format(lastSundayDate, IConstants.DATE_FORMAT_YYYY_MM_DD));
		} catch (Exception e) {
			e.printStackTrace();
			// TODO: handle exception
		}

		int lengthMinusOne = 0;
		String docId = "16486278348501399121";
		System.out.println("docId b4:" + docId);
		Long testDocIdLong1 = null;
		try {
			testDocIdLong1 = new Long(docId);
		} catch (Exception e) {
			lengthMinusOne = docId.length() - 1;
			docId = docId.substring(0, lengthMinusOne);
			System.out.println("docId af:" + docId);
			try {
				testDocIdLong1 = new Long(docId);
			} catch (Exception e2) {
				lengthMinusOne = docId.length() - 1;
				docId = docId.substring(0, lengthMinusOne);
				System.out.println("docId af:" + docId);
				try {
					testDocIdLong1 = new Long(docId);
				} catch (Exception e3) {
					// TODO: handle exception
				}
			}
			System.out.println("testDocIdLong1:" + testDocIdLong1);
		}

	}

	private void testParseCityHashUrl() {
		String inputString = "12064903587766636097";
		System.out.println("inputString=" + inputString);
		Long testLong = null;
		BigInteger testBigInteger = null;

		try {
			testLong = new Long(inputString);
			System.out.println("testLong=" + testLong);
		} catch (Exception e) {
			e.printStackTrace();
		}

		try {
			testBigInteger = new BigInteger(inputString);
			System.out.println("testBigInteger=" + testBigInteger);
		} catch (Exception e) {
			e.printStackTrace();
		}

	}

	private void testGetTopFolders() throws Exception {
		String folder = null;
		//String urlString = "http://www.seoclarity.net";
		String urlString = "http://www.seoclarity.net/";
		//String urlString = "http://www.seoclarity.net/technology";
		//String urlString = "http://www.seoclarity.net/technology/testpage1";
		//String urlString = "http://www.seoclarity.net/technology/customizable-dashboards/";
		//String urlString = "http://www.seoclarity.net/technology/customizable-dashboards/index.html";
		System.out.println("urlString=" + urlString);
		URL url = null;
		String uri = null;

		try {
			url = new URL(urlString);
			uri = url.getPath();
			System.out.println("uri=" + uri);
			if (StringUtils.isNotBlank(uri)) {
				List<String> topFolders = CrawlerUtils.getInstance().getTopFolders(uri);
				for (int i = 0; i < topFolders.size(); i++) {
					System.out.println("folder" + (i + 1) + "=" + topFolders.get(i));
				}
			} else {
				System.out.println("uri is empty.");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void testGetDomainName() throws Exception {
		//String urlString = "xn--t8js6db3ita2diin0100gf0ui.com";
		String urlString = "www.yasasiiokanenohanasi.com";
		//String urlString = "www.ginkou-card-sinsa.com";
		//String urlString = "xn--98j6ck4m8ag6rc53a5b6ec2793l9k9amk9gbe8a.com"; //debug
		String competitorDomainName = "www.xn--jprz7eg9rzm0a73gn7h.com";
		System.out.println("competitor domain Name=" + competitorDomainName);
		String reversedDomainName = CrawlerUtils.getInstance().reverseDomainNameByDot(competitorDomainName, true, false);
		//String reversedDomainName = CrawlerUtils.getInstance().reverseDomainNameByDot(competitorDomainName);
		System.out.println("reversed competitor domain name=" + reversedDomainName);
		String rootDomain = CrawlerUtils.getInstance().reverseDomainNameByDot(competitorDomainName.substring(competitorDomainName.indexOf(".")), true, false);
		//String rootDomain = CrawlerUtils.getInstance().reverseDomainNameByDot(competitorDomainName.substring(competitorDomainName.indexOf(".")));
		System.out.println("competitor root domain=" + rootDomain);

		int numberOfMatches = StringUtils.countMatches(urlString, ".");
		System.out.println(urlString + " contains " + numberOfMatches + " matches.");
	}

	private void testGetTopLevelDomain() {

		//		String BLANK_STRING = "";
		//		String SLASH_SLASH_DOT = "\\.";
		//		int MAX_TOP_LEVEl_DOMAIN_NAME_LENGTH = 50;
		//		
		//		String domain = "test.asdfsf.seoclarity.com";
		//		
		//		String topLevelDomain = BLANK_STRING;
		//		String[] domainArray = domain.split(SLASH_SLASH_DOT);
		//		if (domainArray.length > 0) {
		//			topLevelDomain = domainArray[domainArray.length - 1];
		//			if (topLevelDomain.length() > MAX_TOP_LEVEl_DOMAIN_NAME_LENGTH) {
		//				topLevelDomain = topLevelDomain.substring(0, MAX_TOP_LEVEl_DOMAIN_NAME_LENGTH);
		//			}
		//		}
		//		System.out.println("topLevelDomain="+topLevelDomain);
		//		String clickHouseDatabaseHostnames = Configurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_HOSTNAMES);
		//		if (StringUtils.isNotBlank(clickHouseDatabaseHostnames)) {
		//			System.out.println("clickHouseDatabaseHostnames="+clickHouseDatabaseHostnames);			
		//		} else {
		//			System.out.println("clickHouseDatabaseHostnames is blank.");
		//		}

		String RIGHT_SINGLE_QUOTATION_MARK = "â€™";
		String ENCODED_RIGHT_SINGLE_QUOTATION_MARK = "&#39;";

		String testString = "Shop Under Armour for Boys&#39; UA Storm Insulated Pullover Swacket in our Boysâ€™ Jacket department.";

		String outputString = StringUtils.replace(testString, RIGHT_SINGLE_QUOTATION_MARK, ENCODED_RIGHT_SINGLE_QUOTATION_MARK);

		System.out.println("testString=" + testString);

		System.out.println("outputString=" + outputString);

		//String crawlDateLongString = "1492164496104";

		Date testDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
		testDate.setTime(1488467527043L);
		System.out.println("testDate=" + testDate);

	}

	private void testStartAndEndWith() throws UnsupportedEncodingException, ParseException {
		//		String urlString = "https://www.test.com:443";
		//		boolean isSkipUrl = false;
		//		if (StringUtils.startsWithIgnoreCase(urlString, "https://")  && StringUtils.endsWithIgnoreCase(urlString, ":443")) {
		//			FormatUtils.getInstance().logMemoryUsage("crawlHtml() skip urlString=" + urlString + ", to prevent Connection reset.");
		//			isSkipUrl = true;
		//		}
		//		FormatUtils.getInstance().logMemoryUsage("crawlHtml() isSkipUrl=" + isSkipUrl);

		char b4Character = 0x2019;
		String b4String = String.valueOf(b4Character);

		char afCharacter = 0x0027;
		String afString = String.valueOf(afCharacter);

		String inputString = "Boysâ€™ Basketball";

		String outputString = StringUtils.replace(inputString, b4String, afString);
		System.out.println("inputString=" + inputString);
		System.out.println("outputString=" + outputString);

		String decodedUrlString = "http://multiplayer.it/giochi/playstation-3/picchiaduro/?pagina=4&_=mlzsv&o=i-migliori";
		String encodedUrlString = URLEncoder.encode(decodedUrlString, IConstants.UTF_8);
		//String encodedUrlString = CrawlerUtils.getInstance().encodeUrl(decodedUrlString);
		System.out.println("decodedUrlString=" + decodedUrlString);
		//System.out.println("encodedUrlString="+encodedUrlString);
		System.out.println("encodedUrlString=" + encodedUrlString);

		//String crawlDateLongString = "1506524673";
		//Date testDate = new Date();
		//testDate.setTime(1506524673L);
		//System.out.println("testDate="+testDate);

		String dateString = "June 1, 2017";
		//String dateString = "October 1, 2017";
		String DATE_FORMAT_MONTH_DAY_YEAR = "MMM d, YYYY";
		Date testDate = DateUtils.parseDate(dateString, new String[] { DATE_FORMAT_MONTH_DAY_YEAR });
		System.out.println("testDate=" + testDate);
	}

	private void testTrimUrlText() {
		String testStringB4 = "ï¿½https://www.wantedly.com/companies/enigmo";
		String testStringAf = trimUrlText(testStringB4);
		System.out.println("testStringB4=" + testStringB4);
		System.out.println("testStringAf=" + testStringAf);
	}

	private String trimUrlText(final String input) {
		return StringUtils
				.trim(input.replaceAll(NEWLINE, ONE_SPACE).replaceAll(TAB, ONE_SPACE).replaceAll(NO_BREAK_SPACE, ONE_SPACE).replaceAll(REPLACEMENT_CHAR, ONE_SPACE));
	}

	private void testGenerateProjectId() {
		BigInteger projectId = new BigInteger(String.valueOf(System.nanoTime()));
		System.out.println("projectId=" + projectId);
	}

	private void testRemoveSpreadsheetVersion() {
		String originalFileName = "seoClarity_Link.xls";
		//String originalFileName = "seoClarity_Link (1).xlsx";
		System.out.println("originalFileName=" + originalFileName);

		// replace space character with underscore
		String formattedFileName = StringUtils.replace(originalFileName, IConstants.EMPTY_STRING, IConstants.UNDERSCORE);

		// remove version (ie. from "seoClarity_Link (1).xls" to "seoClarity_Link.xls"
		String formattedFileName1 = StringUtils.substringBefore(formattedFileName, " (");
		String formattedFileName2 = StringUtils.substringAfter(formattedFileName, ")");
		System.out.println("formattedFileName1=" + formattedFileName1);
		System.out.println("formattedFileName2=" + formattedFileName2);

		formattedFileName = formattedFileName1 + formattedFileName2;
		System.out.println("formattedFileName=" + formattedFileName);

	}

	private void testWriteToLogFile() {

		String scrapyCrawlCompareResultsLogTemplate = "/home/<USER>/source/target_url_polite_crawl_test/log/compare_results_{0}.log";
		String testTimestamp = "201804250338";
		String scrapyCrawlCompareResultsLogFile = StringUtils.replace(scrapyCrawlCompareResultsLogTemplate, "{0}", testTimestamp);
		System.out.println("scrapyCrawlCompareResultsLogFile=" + scrapyCrawlCompareResultsLogFile);

		FileWriter fileWriter = null;
		PrintWriter printWriter = null;
		try {
			fileWriter = new FileWriter("log/test.txt");
			printWriter = new PrintWriter(fileWriter);

			printWriter.println("output line 1");
			printWriter.flush();

			printWriter.println("output line 2");
			printWriter.flush();

			printWriter.println("output line 3");
			printWriter.flush();

			printWriter.println("output line 4");
			printWriter.flush();

			printWriter.println("output line 5");
			printWriter.flush();

			printWriter.println("output line 6");
			printWriter.flush();

			printWriter.println("output line 7");
			printWriter.flush();

			printWriter.println("output line 8");
			printWriter.flush();

			printWriter.println("output line 9");
			printWriter.flush();

		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			printWriter.close();
			try {
				fileWriter.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

	private void testCheckUrlQueryString() {
		String urlString = "https://blog.gobrightline.com/best-travel-accessories-families-go#anchor1?q=45345435";
		URL url = null;
		String queryString = null;
		String anchorString = null;
		try {
			url = new URL(urlString);
			anchorString = url.getRef();
			queryString = url.getQuery();
			System.out.println("testCheckUrlQueryString() urlString=" + urlString);
			System.out.println("testCheckUrlQueryString() anchorString=" + anchorString);
			System.out.println("testCheckUrlQueryString() queryString=" + queryString);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void testCompareDomains() throws Exception {

		boolean isDomainMatched = false;

		boolean isExactMatchRequired = true;
		System.out.println("testCompareDomains() isExactMatchRequired=" + isExactMatchRequired);

		//String targetDomain = "www.seoclarity.net"; // 1-true
		//String targetDomain = "blog.seoclarity.net"; // 2-false
		//String targetDomain = "seoclarity.net"; // 3-true
		//String targetDomain = "seoclarity.net"; // 4-true
		//String targetDomain = "seoclarity.net"; // 5-false
		//String targetDomain = "test.blog.seoclarity.net"; // 6-false
		//String targetDomain = "blog.seoclarity.co.uk"; // 7-false
		//String targetDomain = "www.seoclarity.co.uk"; // 8-true
		String targetDomain = "app.ignition.fun"; // 9-true
		//String targetDomain = "seoclarity.co.uk"; // 10-false
		System.out.println("testCompareDomains() targetDomain=" + targetDomain);

		//String hrefDomain = "seoclarity.net"; // 1-true
		//String hrefDomain = "seoclarity.net"; // 2-false
		//String hrefDomain = "seoclarity.net"; // 3-true
		//String hrefDomain = "www.seoclarity.net"; // 4-true
		//String hrefDomain = "blog.seoclarity.net"; // 5-false
		//String hrefDomain = "seoclarity.net"; // 6-false
		//String hrefDomain = "seoclarity.co.uk"; // 7-false
		//String hrefDomain = "seoclarity.co.uk"; // 8-true
		String hrefDomain = "app.ignition.fun"; // 9-true
		//String hrefDomain = "blog.seoclarity.co.uk"; // 10-false
		System.out.println("testCompareDomains() hrefDomain=" + hrefDomain);

		String reversedRootTargetDomain = FormatUtils.getInstance().getReversedRootDomainName(targetDomain);
		System.out.println("testCompareDomains() reversedRootTargetDomain=" + reversedRootTargetDomain);

		String reversedRootHrefDomain = FormatUtils.getInstance().getReversedRootDomainName(hrefDomain);
		System.out.println("testCompareDomains() reversedRootHrefDomain=" + reversedRootHrefDomain);

		String reversedTargetDomain = CrawlerUtils.getInstance().getReversedDomainName(targetDomain);
		System.out.println("testCompareDomains() reversedTargetDomain=" + reversedTargetDomain);
		String reversedHrefDomain = CrawlerUtils.getInstance().getReversedDomainName(hrefDomain);
		System.out.println("testCompareDomains() reversedHrefDomain=" + reversedHrefDomain);

		// when exact match of domain is not required....
		//		if (isExactMatchRequired == false && StringUtils.equalsIgnoreCase(reversedRootTargetDomain, reversedRootHrefDomain)) {
		//			isDomainMatched = true;
		//		}
		//		// when exact match of domain is required....
		//		else {
		//			if (StringUtils.equalsIgnoreCase(reversedTargetDomain, reversedHrefDomain)) {
		//				isDomainMatched = true;
		//			} else if ((StringUtils.startsWith(targetDomain, "www")) && StringUtils.equalsIgnoreCase(reversedRootTargetDomain, reversedRootHrefDomain)
		//					&& StringUtils.equalsIgnoreCase(reversedRootHrefDomain, reversedHrefDomain)) {
		//				isDomainMatched = true;
		//			} else if ((StringUtils.startsWith(hrefDomain, "www")) && StringUtils.equalsIgnoreCase(reversedRootTargetDomain, reversedRootHrefDomain)
		//					&& StringUtils.equalsIgnoreCase(reversedRootHrefDomain, reversedTargetDomain)) {
		//				isDomainMatched = true;
		//			}
		//		}
		System.out.println("testCompareDomains() isDomainMatched=" + isDomainMatched);
	}

	private String[] getUrlStringArray() {
		return new String[] { "https://www.grainger.com/category/pneumatic-and-solid-rubber-wheels/wheels/casters-and-wheels/material-handling/ecatalog/N-13hb",
				"https://www.grainger.com/category/pallet-jacks/pallet-jacks-and-pallet-moving-equipment/material-handling/ecatalog/N-142w",
				"https://www.grainger.com/category/booster-pumps-and-systems/booster-and-pressure-pumps/centrifugal-pumps/pumps/ecatalog/N-1ac7",
				"https://www.grainger.com/category/circuit-breaker-lockouts/electrical-lockouts/lockout-tagout/safety/ecatalog/N-17jt",
				"https://www.grainger.com/category/hvac-relays/hvac-controls-and-thermostats/hvac-and-refrigeration/ecatalog/N-yvk",
				"https://www.grainger.com/category/snap-buttons/hardware/fasteners/ecatalog/N-19qg",
				"https://www.grainger.com/category/wire-rope-slings/rigging-and-lifting-slings/material-handling/ecatalog/N-r25",
				"https://www.grainger.com/category/underground-electrical-enclosures/enclosures/enclosures-and-accessories/wire-cable-and-carrier-systems/electrical/ecatalog/N-19ju",
				"https://www.grainger.com/category/machine-tool-lights/task-lights/lighting/ecatalog/N-ot7",
				"https://www.grainger.com/category/drill-bit-sharpeners/drilling-and-holemaking/machining/ecatalog/N-13uk",
				"https://www.grainger.com/category/skatewheel-conveyors/conveyors/material-handling/ecatalog/N-otv",
				"https://www.grainger.com/category/flow-rails/conveyors/material-handling/ecatalog/N-otu",
				"https://www.grainger.com/category/putties/adhesives-sealants-and-tape/ecatalog/N-852",
				"https://www.grainger.com/category/water-supply-stops/shut-off-valves/plumbing-valves/plumbing/ecatalog/N-ri1",
				"https://www.grainger.com/category/evaporator-coils/central-equipment/hvac-and-refrigeration/ecatalog/N-jq0",
				"https://www.grainger.com/category/circuit-breaker-accessories/circuit-breakers/distribution-circuit-breakers-and-temporary-power-solutions/power-management-circuit-protection-and-distribution/electrical/ecatalog/N-qlf",
				"https://www.grainger.com/category/phone-accessories/communications/electronics-appliances-and-batteries/ecatalog/N-dap",
				"https://www.grainger.com/category/rotary-brushes/floor-polishers-burnishers-scrubbers-and-accessories/floor-cleaning-machines/cleaning-and-janitorial/ecatalog/N-1anr",
				"https://www.grainger.com/category/conveyor-belt-tensioners/conveyors/material-handling/ecatalog/N-ol6",
				"https://www.grainger.com/category/hygienic-shovels/food-processing/food-service-and-food-processing/furniture-hospitality-and-food-service/ecatalog/N-144v",
				"https://www.grainger.com/category/lifeline-accessories/fall-protection/safety/ecatalog/N-kam",
				"https://www.grainger.com/category/hook-and-loop-cable-ties/wire-management-organization/electrical-supplies/electrical/ecatalog/N-qf9",
				"https://www.grainger.com/category/disposable-gloves/gloves-and-hand-protection/safety/ecatalog/N-mlbZ1z13n16",
				"https://www.grainger.com/category/marking-chalk-and-refills/marking-tools/measuring-and-layout-tools/tools/ecatalog/N-15yq",
				"https://www.grainger.com/category/indexable-end-mills/indexable-milling/indexable-tools/machining/ecatalog/N-1d2y",
				"https://www.grainger.com/category/5-11-tactical/ecatalog/N-1z088ny",
				"https://www.grainger.com/category/kitchen-and-range-hoods-and-accessories/ventilation-equipment-and-supplies/hvac-and-refrigeration/ecatalog/N-ymn",
				"https://www.grainger.com/category/dip-slides/microbiology-and-molecular-biology-supplies/lab-supplies/ecatalog/N-10sa",
				"https://www.grainger.com/category/tarp-accessories/tarps-and-tarp-accessories/hardware/ecatalog/N-178g",
				"https://www.grainger.com/category/pneumatic-sleeve-valves/pneumatic-valves/pneumatics/ecatalog/N-17tr",
				"https://www.grainger.com/category/arc-cutting-and-gouging/welding/ecatalog/N-plh",
				"https://www.grainger.com/category/netting-and-netting-accessories/structures-and-sheds/material-handling/ecatalog/N-r2x",
				"https://www.grainger.com/category/molecular-biology-reagents/microbiology-and-molecular-biology-supplies/lab-supplies/ecatalog/N-nxx",
				"https://www.grainger.com/category/solenoid-valves-less-coil/solenoid-valves-and-coils/plumbing-valves/plumbing/ecatalog/N-rie",
				"https://www.grainger.com/category/vapor-tight-fixtures/hazardous-location-fixtures/lighting-fixtures/lighting/ecatalog/N-1dcz",
				"https://inventory.grainger.com",
				"https://www.grainger.com/category/foam-blanks-flats-bars-plates-and-sheet-stock/foam/raw-materials/ecatalog/N-dcvZ1z0jnhp",
				"https://www.grainger.com/category/foam-blanks-flats-bars-plates-and-sheet-stock/foam/raw-materials/ecatalog/N-dcvZ1z10qmw",
				"https://www.grainger.com/category/foam-blanks-flats-bars-plates-and-sheet-stock/foam/raw-materials/ecatalog/N-dcvZ1z0jnjl",
				"https://www.grainger.com/content/int_Peru", "https://www.grainger.com/category/submerged-arc-welding/welding/ecatalog/N-ppw",
				"https://www.grainger.com/category/mulch-mats-and-stepping-stones/crop-nursery-and-landscaping/outdoor-equipment/ecatalog/N-lfn",
				"https://www.grainger.com/category/sleeping-bags/camping-equipment/outdoor-equipment/ecatalog/N-o20",
				"https://www.grainger.com/category/data-display-devices/communications/electronics-appliances-and-batteries/ecatalog/N-om6",
				"https://www.grainger.com/category/submerged-arc-welders/submerged-arc-welding/welding/ecatalog/N-ppx",
				"https://www.grainger.com/category/welding-flux/filler-metals/welding/ecatalog/N-pt7",
				"https://www.grainger.com/category/business-envelopes/office-paper-and-notebooks/office-supplies/ecatalog/N-r3e",
				"https://www.grainger.com/category/foam-blanks-flats-bars-plates-and-sheet-stock/foam/raw-materials/ecatalog/N-dcvZ1z0jnj3",
				"https://www.grainger.com/content/supplylink-types-of-welding",
				"https://www.grainger.com/category/foam-blanks-flats-bars-plates-and-sheet-stock/foam/raw-materials/ecatalog/N-dcvZ1z13g27",
				"https://www.grainger.com/content/supplylink-how-to-solder",
				"https://www.grainger.com/category/foam-blanks-flats-bars-plates-and-sheet-stock/foam/raw-materials/ecatalog/N-dcvZ1z0nruh",
				"https://inventory.grainger.com/", "https://safety.grainger.com/insights/posters", "https://safety.grainger.com/resources",
				"https://www.grainger.com/category/electric-hose-chop-saws/chop-saws-cut-off-machines-and-accessories/saws-and-accessories/tools/ecatalog/N-164b",
				"https://www.grainger.com/category/foam-blanks-flats-bars-plates-and-sheet-stock/foam/raw-materials/ecatalog/N-dcvZ1z0ewet",
				"https://www.grainger.com/category/foam-blanks-flats-bars-plates-and-sheet-stock/foam/raw-materials/ecatalog/N-dcvZ1z0nqsy",
				"https://www.grainger.com/category/foam-blanks-flats-bars-plates-and-sheet-stock/foam/raw-materials/ecatalog/N-dcvZ1yzc0jd",
				"https://www.grainger.com/content/supplylink-blast-media-chart", "https://www.grainger.com/content/supplylink-characteristics-of-abrasives",
				"https://m.grainger.com/mobile/category/foam-blanks-flats-bars-plates-and-sheet-stock/foam/raw-materials/ecatalog/N-dcv?searchSortKey=brandDesc",
				"https://www.grainger.com/category/cube-trucks/cube-trucks-and-accessories/hoppers-and-cube-trucks/trash-and-recycling-containers/trash-and-recycling/cleaning-and-janitorial/ecatalog/N-1boy",
				"https://www.grainger.com/category/foam-blanks-flats-bars-plates-and-sheet-stock/foam/raw-materials/ecatalog/N-dcvZ1z0nrup?sst=All",
				"https://www.grainger.com/category/foam-blanks-flats-bars-plates-and-sheet-stock/foam/raw-materials/ecatalog/N-dcvZ1z13nwa",
				"https://www.grainger.com/category/foam-blanks-flats-bars-plates-and-sheet-stock/foam/raw-materials/ecatalog/N-dcvZ1yzdv3c?s_kwcid=AL!2966!3!71385055490!e!!g!!",
				"https://www.grainger.com/category/hazardous-location-lighting-signals/hazardous-location-fixtures/lighting-fixtures/lighting/ecatalog/N-1dcx",
				"https://www.grainger.com/content/kc-gen-five-ways-to-balance-strategic-mro-inventory-management-with-emergency-preparedness",
				"https://www.grainger.com/content/kc-gen-six-points-not-to-leave-out-of-your-IT-disaster-recovery-plan",
				"https://www.grainger.com/content/kc-gen-five-ways-an-organization-can-better-utilize-employees-during-a-crisis",
				"https://www.grainger.com/category/gas-welding-outfits/gas-welder-fittings/cutting-torches-and-accessories/gas-welding-equipment/welding/ecatalog/N-1edw",
				"https://www.grainger.com/category/ecatalog/N-/Ntt-MERCURY SWITCH", "https://www.grainger.com/category/ecatalog/N-/Ntt-Plastic Scraper|Plastic Scraper",
				"https://www.grainger.com/category/chemical-dosimeter-badges/gas-detection/safety/ecatalog/N-ij2",
				"https://www.grainger.com/category/wet-location-fluorescent-fixtures/indoor-fixtures/lighting-fixtures/lighting/ecatalog/N-1dddZ1z0hjiy",
				"https://www.grainger.com/category/wet-location-fluorescent-fixtures/indoor-fixtures/lighting-fixtures/lighting/ecatalog/N-1dddZ1z0jfp7",
				"https://www.grainger.com/category/foam-blanks-flats-bars-plates-and-sheet-stock/foam/raw-materials/ecatalog/N-dcvZ1z0o0wc",
				"https://www.grainger.com/category/foam-blanks-flats-bars-plates-and-sheet-stock/foam/raw-materials/ecatalog/N-dcvZ1z0njta",
				"https://www.grainger.com/category/foam-blanks-flats-bars-plates-and-sheet-stock/foam/raw-materials/ecatalog/N-dcvZ1z0cvnn",
				"https://www.grainger.com/category/foam-blanks-flats-bars-plates-and-sheet-stock/foam/raw-materials/ecatalog/N-dcvZ1z0jnix",
				"https://www.grainger.com/category/foam-blanks-flats-bars-plates-and-sheet-stock/foam/raw-materials/ecatalog/N-dcvZ1z13k69Z1z0nqse", };
	}

	//	private void testBlockedByRobotsTxt() {
	//		
	//		boolean isBlockedByRobotsTxt = false;
	//		String ip = "0";
	//		int domainId = 0;
	//		String targetDomain = null;
	//		String userAgent = null;
	//		String urlString = "http://www.yahoo.com";
	//		boolean isUseProxy = false;
	//		// check if blocked by robots.txt
	//		if (robotsExclusionService.checkIfAccessible(ip, domainId, targetDomain, userAgent, urlString, isUseProxy) == true) {
	//			isBlockedByRobotsTxt = false;
	//		} else {
	//			isBlockedByRobotsTxt = true;
	//		}
	//		System.out.println("urlString="+urlString);
	//		System.out.println("isBlockedByRobotsTxt="+isBlockedByRobotsTxt);
	//	}

	//	private void testWhenRobotsTxtAvailable() {
	//		RobotsExclusionService robotsExclusionService = SpringBeanFactory.getBean("robotsExclusionService");
	//		
	//		boolean isAccessible = false;
	//		String userAgent = null;
	//		String urlString = null;
	//		boolean isUseProxy = false;
	//		
	//		// User-agent: *
	//		// Disallow: /thank-you
	//		// Disallow: /thank-you/
	//		// Disallow: /*thank-you$
	//		// Disallow: /blog/author*
	//		// Disallow: /blog/tag*
	//		// Disallow: /_hcms/preview/
	//		// Disallow: /_hcms/rss/
	//		// Disallow: /_hcms/postlisting
	//		// Disallow: /hs/manage-preferences/
	//		
	//		// has no access - https://www.seoclarity.net/thank-you
	//		urlString = "https://www.seoclarity.net/thank-you";
	//		isAccessible = robotsExclusionService.checkIfAccessible(null, 0, null,userAgent, urlString, isUseProxy);
	//		assertFalse("error--urlString should not be accessible="+urlString, isAccessible);
	//		
	//		// has no access - https://www.seoclarity.net/page1/thank-you
	//		urlString = "https://www.seoclarity.net/page1/thank-you";
	//		isAccessible = robotsExclusionService.checkIfAccessible(null, 0, null,userAgent, urlString, isUseProxy);
	//		assertFalse("error--urlString should not be accessible="+urlString, isAccessible);
	//		
	//		// has no access - https://www.seoclarity.net/thank-you/page2
	//		urlString = "https://www.seoclarity.net/thank-you/page2";
	//		isAccessible = robotsExclusionService.checkIfAccessible(null, 0, null,userAgent, urlString, isUseProxy);
	//		assertFalse("error--urlString should not be accessible="+urlString, isAccessible);
	//		
	//		// has no access - https://www.seoclarity.net/blog/author
	//		urlString = "https://www.seoclarity.net/blog/author";
	//		isAccessible = robotsExclusionService.checkIfAccessible(null, 0, null,userAgent, urlString, isUseProxy);
	//		assertFalse("error--urlString should not be accessible="+urlString, isAccessible);
	//		
	//		// has no access - https://www.seoclarity.net/blog/author?test
	//		urlString = "https://www.seoclarity.net/blog/author?test";
	//		isAccessible = robotsExclusionService.checkIfAccessible(null, 0, null,userAgent, urlString, isUseProxy);
	//		assertFalse("error--urlString should not be accessible="+urlString, isAccessible);
	//		
	//		// has no access - https://www.seoclarity.net/blog/author/page3
	//		urlString = "https://www.seoclarity.net/blog/author/page3";
	//		isAccessible = robotsExclusionService.checkIfAccessible(null, 0, null,userAgent, urlString, isUseProxy);
	//		assertFalse("error--urlString should not be accessible="+urlString, isAccessible);
	//		
	//		// has access - https://www.seoclarity.net/athank-youz
	//		urlString = "https://www.seoclarity.net/athank-youz";
	//		isAccessible = robotsExclusionService.checkIfAccessible(null, 0, null,userAgent, urlString, isUseProxy);
	//		assertTrue("error--urlString should be accessible="+urlString, isAccessible);
	//	}

	private void testTrimText() throws Exception {
		String inputString = "\n\n\n\n\n\n\t\t\n\t\n\t\n\t\n\t\n\t\n\t\n\t\n\t\n\nEN\ntoggle menuLive Help\nBecome a Member\nSign In\n\n\n12Messages\n7Shopping Cart\n\n\n\n\n\n\n\n\n\n\n\n\nEterinis aliejus\n\nSužinokite daugiau\n\tApie „Young Living“\n\tEterinių aliejų vadovas\n\tSaugumo gairės\n\t„Seed to Seal“ programa\n\n\n„Young Living“ ūkiai\n\n\n\n\n\n\n\n\t\n\n\n\n\nProduktai\n\n„Young Living“ produktų linijos\n\t„Slique“\n\t„Savvy Minerals“\n\t„Seedlings“\n\n\nEteriniai aliejai ir mišiniai\n\tEteriniai aliejai\n\tEterinių aliejų mišiniai\n\tPieštukiniai („Roll-on“) aliejai\n\tMasažo aliejai\n\tRinkiniai\n\n\nEterinių aliejų priemonės\n\tPriedai\n\tGarintuvai\n\n\nNamai\n\t„Seedlings“\n\n\nSveikatingumas\n\tSvorio mažinimas\n\tTikslinė parama\n\tSveiki užkandžiai\n\n\nGrožis\n\t„Savvy Minerals“\n\tVeido odos priežiūra\n\tKūnui ir voniai\n\n\nDaugiau galimybių\n\nNaujienos\n\tSezono naujienos\n\n\n\n\n\n\n\nNarystės galimybės\n\nKompensavimo planas\n\n„Essential Rewards“\n\nSkatinamosios priemonės\n\t„Elite Express“\n\t„Help 5“\n\t„Event to Event“\n\n\nAkcijos\n\nSėkmės istorijos\n\nGlobalus pripažinimas\n\t„Diamond“ lyderiai\n\tPasiekimai\n\tSkatinamosios kelionės\n\n\n\n\n\n\n\nVeikla\n\nApie „Young Living“\n\tValdybos komanda\n\tVykdomosios valdybos komanda\n\tMokslinė patariamoji taryba\n\tVeterinarinė patariamoji taryba \n\tPrekių ženklų ambasadoriai\n\tŪkiai\n\n\nMūsų įkūrėjas\n\nRenginiai\n\tTeminiai renginiai\n\tSkatinamosios kelionės\n\tNarių renginių kalendorius\n\n\nNarystės galimybės\n\tAkcijos \n\n\nŽiniasklaidos centras\n\tPranešimai\n\n\nKarjera\n\nSusisiekite su mumis\n\tTarptautiniai biurai\n\n\n\nNaujienosDAUGIAU\n\t„Gary’s True Grit Einkorn Granola“ javainiai - jau prekyboje\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nVeido odos priežiūra\n\n\n\n\n\n\n\n\n\n\n\n\t\n\n\n\n\nSandalwood Moisture Cream\n\n\n\nDalintis\n\n\n\n\n\nProduktai »Sandalwood Moisture Cream\nProduktai »Grožis »Veido odos priežiūra »Sandalwood Moisture Cream\n\n\n\n\n\n\nDrėkinamasis kremas „Sandalwood“ yra ypač stiprus drėkiklis, papildytas „Young Living“ grynaisiais eteriniais aliejais. Natūrali augalinė cheminė medžiaga metilsulfonilmetanas (MSM) minkština odą ir jai suteikia elastingumo. Eteriniai aliejai: raudonmedžio, levandų, rozmarinų, santalų, miramedžių.\n\n\n\n\n\n\n\t\n\n\n\n\n\n\tEteriniai aliejai ir mišiniai\n\tNamai\n\tSveikatingumas\n\tGrožis\tSenėjimą lėtinančios priemonės\n\tVeido odos priežiūra\n\tKūnui ir voniai\n\n\n\tPusiausvyra\n\tSavvy Minerals\n\n\n\n\n„Seed to Seal“ kokybės įsipareigojimas - tai 25-erius metus vykdomas mūsų pažadas jums bei tarptautinės lyderystės ir atsakomybės atspindys. Tikime, kad uoliai rūpinatės savo šeima ir savimi - tad norite produktų, kurie būtų autentiški, išskirtinai gryni bei sudėtyje neturintys kenksmingų chemikalų. Patentuotas „Seed to Seal“ pažadas - tai mūsų priesaika jums, žemei, ir mums patiems, jog „Young Living“ produktai yra patys geriausi - dabar ir visada.\n\n „Seed to Seal“ ir šios programos 3 pakopos — ištekliai, žinios ir standartai — įtrauktos į kiekvieną eterinių aliejų gamybos proceso sritį tiek mūsų ūkiuose, tiek kruopščiai prižiūrimuose partnerių ūkiuose. „Seed to Seal“ įpareigoja kruopštų testavimą ir pakartotinį testavimą, siekiant užtikrinti, kad jūsų šeima mėgautųsi gryniausiais eterinių aliejų produktais Žemėje. „Seed to Seal“ pažadas mus lydi kiekviename žingsnyje. Žinome, kad mums - ir jums, tai geriausias pasirinkimas.\n\n\n\n\nKaip naudotiIngredientai\n\n\n\nKaip naudoti\nKaip naudoti\nNuvalykite ir tonizuokite odą su veido prausikliu „Orange Blossom™“. Nedidelį kiekį drėkinamojo kremo „Sandalwood™“ užtepkite ant veido ir kaklo, braukdami iš apačios į viršų.\n\n\n\nIngredientai\nIngredientai\nDejonizuotas vanduo, glicerinas, kaprilo trigliceridai, cetearilo alkoholis, glicerolio stearatas, natrio stearoil-laktilatas, alyvuogių vaisiai (Olea europaea), stearino rūgštis, mielių (Pichia Anomala) eksktraktas, šluoteliniai santalai (Santalum paniculatum), amūrinių kamštenių (Phellodendron Amurense) žievės ekstraktas, miežių (Hordeum distichon) ekstraktas, levulinic rūgštis, p-anyžių rūgštis, kalendrų (Coriandrum Sativum) sėklų aliejus, bergamočių be furanokumarino (Citrus Aurantium Bergamia) žievelių aliejus, kvapiųjų kanangų (Cananga Odorata) žiedų aliejus, pelargonijų (Pelargonium Graveolens) žiedų aliejus, santalų (Santalum Album) aliejus, levandų (Lavandula Angustifolia) aliejus, mirų (Commiphora Myrrha) aliejus, rozmarinų (Rsmarinus Officinalis) lapų aliejus, taukmedžių (Butyrospermun Parkii) sviestas, hidrolizuoti kviečių baltymai, hidrolizuotas kviečių krakmolas, sorbo rūgštis, alantoinas, ksantano derva, askorbo rūgštis, natrio PBS, erškėtuogių (Rosa Rubiginosa) sėklų aliejus, ožerškių (Lycium Barbarum) sėklų aliejus, vaistinių medetkų (Calendula Officinalis) žiedų ekstraktas, ramunėlių (Anthemis Nobilis) žiedų ekstraktas, dviskiaučių ginkmedžių (Ginkgo Biloba) lapų ekstraktas, vynuogių (Vitis Vinifera) sėklų ekstraktas, apelsinų žiedų (Citrus Aurantium Dulcis) ekstraktas, paprastųjų erškėčių (Rosa Canina) žiedų ekstraktas, jonažolių (Hypericum Perforatum) ekstraktas, natrio fitatas, tokoferilacetatas (vitaminas E), retinilo palmitatas (vitaminsd A), alavijų (Aloe Vera 200x / Aloe Barbadensis), natrio hialuronatas, natrio hidroksidas ir citrinų rūgštis.\n\n\n\n\nPopuliarūs produktai\nTea Tree (Melaleuca Alternifolia)\nĮdėti į prekių krepšelį Pridėti\n\nLavender Essential Oil\nĮdėti į prekių krepšelį Pridėti\n\nPeppermint Essential Oil\nĮdėti į prekių krepšelį Pridėti\n\nLantern Diffuser\nĮdėti į prekių krepšelį Pridėti\n\nLemon Essential Oil\nĮdėti į prekių krepšelį Pridėti\n\n\n\n\n\n\n\n\n\n\n\n\n\tKlientų aptarnavimas (nemokami skambučiai iš laidinių telefonų Lietuvoje) - 08800 30914\n\tSusisiekite Su Mumis\n\tVirtualusis biuras\n\tNarystė\n\n\n\tKlientų aptarnavimas\n\tRenginiai ir žiniasklaida\n\n\n\t„D. Gary Young“ fondas\n\tKarjera\n\n\nSusisiekite su mumis\nYoung Living (Europe) Limited\nBuilding 11, Chiswick Park\n566 Chiswick High Road\nLondon W4 5YS\n\nKlientų aptarnavimas (nemokami skambučiai iš laidinių telefonų Lietuvoje) \n08800 30914\n\nEuropos pagrindinė būstinė \n+44 (0) 20 3935 9000\n\n\n\n\n\n\nCopyright © 2019 Young Living Essential Oils. Visos teisės saugomos.\n|\nPrivatumo politika \n\n\n\n\n\n  \n  \n  \n  \n  \n  \n  \n  \n  \n\n  \n\n\n\n";
		System.out.println("inputString=" + inputString);
		String outputString = CrawlerUtils.getInstance().trimText(inputString);
		System.out.println("outputString=" + outputString);
	}

	private void testScrapyApi(String[] args) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		//String userAgent = "Mozilla/5.0 (compatible; linkCheckV3.0)";
		//String userAgent = "Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.0 ; Claritybot)";
		ScrapyCrawlerResponse scrapyCrawlerResponse = null;
		HtmlClickHouseEntity htmlClickHouseEntity = null;
		PageAnalysisResult[] pageAnalysisResultArray = null;
		Map<String, String> pageCrawlerApiRequestHeaders = null;
		List<AdditionalContentEntity> additionalContentEntityList = null;
		boolean isJavascriptCrawler = false;
		String ip = "1";
		String queueName = "testQueueName";
		scrapyCrawlerResponse = null;
		String urlString = null;
		String userAgent = null;
		CrawlerResponse crawlerResponse = null;
		int crawlType = IConstants.CRAWL_TYPE_TARGET_URL_HTML;
		String reversedUrlDomain = null;
		int domainId = 9688;
		Date currentTrackDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
		Date crawlTimestamp = null;
		String s3FileName = null;
		String trimmedUrlString = null;

		if (args != null && args.length >= 4) {
			urlString = args[1];
			userAgent = args[2];
			isJavascriptCrawler = BooleanUtils.toBoolean(args[3]);
		} else {
			urlString = "https://themillions.com/books-reviews/siddhartha-penguin-classics-deluxe-edition-0142437182";
			userAgent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 (compatible; PoliteCrawl.v2.0)";
		}
		//urlString = "https://www.retailmenot.com/view/vaperempire.com.au"; //debug

		//isJavascriptCrawler = false; //debug

		//urlString = "https://shop.guess.com/en/catalog/view/w54gdgmp848"; //debug
		//urlString = "https://www.cheaptickets.com/Rovio.d6056107.Destination-Travel-Guides";
		//urlString = "https://help.abebooks.com/what-is-an-international-edition/"; //debug		
		//urlString = "https://www.amazon.com/b?ie=UTF8&node=3775601"; //debug
		//urlString = "http://www.sonysonpo.co.jp/share/pdf/auto/guide_20120401.pdf"; //debug		
		//urlString = "https://uk.hotels.com/de1734973/hotels-near-point-de-la-torche-plomeur-france/";		
		//urlString = "https://jp.hotels.com/ho956397824/homu2-suitsu-bai-hiruton-nasshubiru-dauntaun-konbenshon-senta-nasshubiru-amerika/";		
		//urlString = "http://maps.factweavers.com/";

		//urlString = "http://byronhousten.com/pandas-is-disgustingly-faster-than-i-expected/";
		//urlString = "https://www.linkedin.com/jobs/product-manager-jobs-new-york";
		//urlString = "https://www.fda.gov/medical-devices/contact-lenses/decorative-contact-lenses";
		//urlString = "https://yvonnevanderlaan.nl/sri-lanka-met-kinderen/";
		//urlString = "https://es.investing.com/equities/dia-income-statement";
		//urlString = "https://www.newegg.com/sellers/index.php/payment-and-logistic-partners/";
		//urlString = "https://www.seoclarity.net/platform";
		//urlString = "http://maps.factweavers.com/";
		//urlString = "https://www.retailmenot.com/coupons/wallfountains";
		//urlString = "https://www.orbitz.com/Santa-Cruz-Hotels-Motel-Santa-Cruz.h1335721.Hotel-Information";
		//urlString = "https://quickbooks.intuit.com/community/Other-Questions/Square-Tax-Import/m-p/293616";
		//urlString = "https://quickbooks.intuit.com/learn-support/en-us/pay-bills/resolve-sync-error-status-in-online-bill-pay/00/192527";
		//urlString = "https://www.expedia.es/Madrid-Hoteles-Gran-Via-Capital.h13171890.Informacion-Hotel";
		//urlString = "https://quickbooks.intuit.com/community/Reports-and-accounting/I-ve-got-a-state-ID-It-is-7-digits-long-all-numbers-like-your/m-p/218993";
		//urlString = "http://www.hm.com/de/life/fashion/everyday-icon/2015/10/kristina-bazan";
		//urlString = "https://www.abebooks.com/Republican-Manual-Smalley-E-V-American/**********/bd";		
		//urlString = "https://www.avvo.com/attorneys/97005-or-nicholas-heydenrych-1494341.html";
		//urlString = "https://www.lowesforpros.com/pl/Air-tools-compressors-Tools/**********?goToProdList=true&refinement=**********";
		//urlString = "https://www.retailmenot.com/coupons/lebanon";
		//urlString = "http://www.orbitz.com/flights/to-Ayers_Rock.d574/";
		//urlString = "https://www.lowes.com/pl/Bathtub-shower-door-roller-assemblies-Bathtub-shower-door-hardware-Door-hardware-Hardware/**********";
		//urlString = "https://support.t-mobile.com/docs/DOC-34411";
		//urlString = "https://www.expedia.com/York-Hotels-Wheatlands-Lodge-Hotel.h8183878.Hotel-Information";
		//urlString = "https://support.t-mobile.com/message/614877#614877";
		//urlString = "https://vtrips.com/product/langstons-valley-view-4-bedrooms-sleeps-464la";
		//urlString = "https://quickbooks.intuit.com/community/Employees-and-payroll/How-do-I-pay-a-recently-terminated-employee/m-p/229205";
		//urlString = "https://www.pacsun.com/pacsun/chopped-colorblock-half-zip-hoodie-8515645.html";
		//urlString = "https://www.orbitz.com/blacksmith-sitemaps/70201/en_US/HOTEL_INFOSITE/3";
		//urlString = "http://www.ebookers.com/flights/DELTA-AIRLINES";
		//urlString = "https://www.amazon.com/Discounted-Decorative-Suspended-Ceiling-Dimension/dp/B0015DYURK";
		//urlString = "https://www.seoclarity.net/platform/";
		//urlString = "https://www.expedia.com/Bali-Hotels-Golden-Tulip-Jineng-Resort-Bali.h13300431.Hotel-Information";
		//urlString = "https://quickbooks.intuit.com/community/Account-management/Can-t-seem-to-log-in-Logging-into-QBO-and-I-m-getting-quot-Not/m-p/195837";
		//urlString = "https://www.ebookers.com/lp/flights/358/615/alta-to-borlange";
		//urlString = "https://www.expedia.com/Belgian-Ardennes-Hotels-Cocoon-For-Lovers-Near-Bouillon.h16556772.Hotel-Information";
		//urlString = "https://quickbooks.intuit.com/community/Pay-bills/Clearing-stale-vendor-credits/m-p/308680";
		//urlString = "https://vtrips.com/product/langstons-valley-view-4-bedrooms-sleeps-464la";
		//urlString = "https://www.apple.com/ipad-pro/";
		//urlString = "https://www.seoclarity.net/platform";
		//urlString = "https://www.seoclarity.net/hs/cta/cta/redirect/1291678/f90df704-cd97-4036-8878-433c9cc34033";
		//urlString = "https://www.opendoor.com/w/vendor-partnerships";
		//urlString = "http://downloads.seoclarity.net/extract/1501_S_Snoqualmie_St_Test.htm";
		//urlString = "http://downloads.seoclarity.net/extract/7934_W_Monterosa_St_Test.htm";
		//urlString = "https://www.reddit.com/r/2007scape/";
		//urlString = "https://www.ajg.com/us/services/pharmacy-benefit-management/";
		//urlString = "https://www.hpe.com/us/en/what-is/tech-refresh.html";
		//urlString = "https://store.hp.com/us/en/vwa/2-in-1/feat=Backlit-keyboard";
		//urlString = "https://www.menards.com/main/wtmeasuremententry.html?cid=9245";
		//urlString = "https://www.carmax.com/cars/infiniti/qx60";
		//urlString = "https://www.apple.com/fr/apple-watch-hermes/";
		//urlString = "https://www.mansionsstonehill.com/";
		//urlString = "https://answers.sap.com/questions/4636908/basis-responsibilities.html";
		//urlString = "https://www.menards.com";
		//urlString = "https://www.menards.com/main/home.html";
		//urlString = "https://www.ulta.com/yellow-diamond-eau-de-toilette?productId=xlsImpprod4230037";
		//urlString = "http://101things.com/humboldt/bear-river-casino/";
		//urlString = "https://www.grainger.com/category/brand/C & D Valve";
		//urlString = "https://www.bradfordexchange.com/mcategory/apparel-and-accessories_59+11643/disney+handbags--accessories.html";
		//urlString = "https://www.bloomingdales.com/shop/womens-designer-shoes?id=16961";
		//urlString = "https://www.helzberg.com/valentines-day-gift-guide";
		//urlString = "https://www.orbitz.com/Green-Bay-Hotels-Baymont-By-Wyndham-Green-Bay.h40154.Hotel-Information";
		//urlString = "https://www.spokeo.com/Brown+Rd+Wexford+PA+addresses";
		//urlString = "https://www.retailmenot.com/blog/supermarket-shortcut-scams.html";
		//urlString = "https://drizly.com/wine/red-wine/mammolo/c196656";
		//urlString = "https://www.compassion.com/world-days/world-photography-day.htm";
		//urlString = "https://www.expedia.com/Coloane-Hotels-The-13.h34913378.Hotel-Information";
		//urlString = "http://************/content_guard/page_10000.html";
		//urlString = "https://www.seoclarity.net/resources/knowledgebase/write-perfect-meta-description-seo-17115/?hs_amp=true";
		//urlString = "https://www.yummly.com/cuisines/mediterranean";
		//urlString = "https://www.hotels.com/sd934558/hotel-special-deals-amsterdam/";
		//urlString = "http://downloads.seoclarity.net/extract/1501_S_Snoqualmie_St_Control.htm";
		//urlString = "https://www.opendoor.com/w/stories/ron-gray";
		//urlString = "https://www.expedia.com/Mont-Tremblant-Hotels-Le-Couvent-BB.h6570745.Hotel-Information";
		//urlString = "s3://fw-crawl/page_crawler/11111/db8d759b6787a502d793d5b4e9d9aca9.warc";
		//urlString = "https://www.grainger.com/mobile/category/torque-wrenches-and-accessories/wrenches/tools/ecatalog/N-167xZ1yzbhj8";
		//urlString = "https://m.grainger.com/mobile/category/arc-flash-protection/safety/ecatalog/N-ayt";
		//urlString = "https://test.edgeseo.dev/ai_rule_38.html";
		//urlString = "https://about.hm.com/zh_cn/news/Beijing-Ikea-opening.html";
		//urlString = "https://www.yummly.com/recipes/good-chicken-recipes";
		//urlString = "https://www.booking.com/hotel/us/mandalay-bay.html";
		//urlString = "https://www.autoescape.com/Fromentine-Location-Voiture-g-cty.d3000395236.Deals-Location-De-Voiture";
		//urlString = "https://www.expedia.com/las-vegas-hotels-mandalay-bay-resort-and-casino.h203613.hotel-information";
		//urlString = "https://test.edgeseo.dev/ai_rule_59.html";
		//urlString = "https://pages.factweavers.com/base_tag_found.html";
		//urlString = "https://test.edgeseo.dev/ai_rule_38.html";
		//urlString = "https://www.synchronybank.com/blog/compound-interest-101/";
		//urlString = "https://test.edgeseo.dev/AI_Rule_36.html";
		//urlString = "https://www.moneysupermarket.com/home-insurance/jewellery-insurance/";
		//urlString = "https://www.bestbuy.com/site/questions/apple-iphone-8-plus-64gb-silver-sprint/6009772/question/b330a617-6ec2-3506-83bc-b55dbcdbd7ce";
		//urlString = "https://www.publichealth.columbia.edu/research/precision-prevention/sharp-training-skills-health-and-research-professionals";
		//urlString = "https://www.radissonhotels.com/pt-br/destino/eua/wyoming/gillette";
		//urlString = "https://www.realsimple.com/beauty-fashion/skincare/best-face-scrubs";
		//urlString = "https://quickbooks.intuit.com/uk/resources/hiring-employees/hiring-dos-and-donts/";
		//urlString = "https://test.edgeseo.dev/gtm_1.html";
		//urlString = "https://www.fanatics.com/nba/chicago-bulls/hats/o-7925+t-********+d-3100115+z-8-**********";
		//urlString = "https://test.edgeseo.dev/simhash_test1.html";
		//urlString = "https://www.hotels.com/trips";
		//urlString = "https://www.choicehomewarranty.com/user-agreement/";
		//urlString = "https://www.qvc.com/for-the-home/bedding/duvet-covers/_/N-1z139hqZpnuy/c.html";
		//urlString = "https://shop.usa.yamaha.com";
		//urlString = "https://www.walgreens.com/locator/walgreens-7150+w+atlantic+blvd-margate-fl-33063/id=3193";
		//urlString = "https://www.moneysupermarket.com/wedding-insurance/guide/";
		//urlString = "https://www.reddit.com/r/dating_advice/comments/a4iwny/how_to_date/";
		//urlString = "https://www.reddit.com/r/sportsbook/";
		//urlString = "https://www.synchronybusiness.com/consumer-financing.html";
		//urlString = "https://www.cmegroup.com/cn-t/trading/fx/g10/euro-fx.html";
		//urlString = "https://www.acklandsgrainger.com/en/category/Mortar-and-Pestle-Sets/Labware/c/25812";
		//urlString = "https://www.tractorsupply.com/tsc/store_Zephyrhills-FL-33541_545";
		//urlString = "https://www.grainger.com/category/tools/power-tools/drills-drivers/drill-driver-accessories/drill-bits/drill-bits-for-metal-plastic/jobber-length-drill-bits/high-speed-steel-jobber-length-drill-bits/non-coolant-through-high-speed-steel-jobber-length-drill-bits";
		//urlString = "https://www.awcwire.com/";
		//urlString = "https://www.renodepot.com/en/lighting-and-ceiling-fans/indoor-lighting-333503/pendant-lighting-333526?int_cmp=search-_-trending_pendantlighting-_-hp";
		//urlString = "http://www.seoclarity.net"; // HTTP status code 301
		//urlString = "https://www.seoclarity.net/test_page.html"; // HTTP status code 404
		//urlString = "http://listings.hickoryrealestategroup.com/idx/details/listing/b021/3173113/1090-Carter-Loop-Road-12-Rockwell-NC-28138?printable=1"; // HTTP status code 503
		//urlString = "https://www.target.com/s/storage+cabinets+with+doors"; // HTTP status code 301
		//urlString = "https://www.seoclarity.net/"; // HTTP status code 200
		//urlString = "https://www.carrollwater.com/water-issues/iron-water/"; // HTTP status code 200
		//urlString = "https://www.homebase.co.uk/paint-decorating/paint/fence-paint.list";
		//urlString = "https://www.livecareer.com/resume-search/";
		//urlString = "https://www.overstock.com/Bedding-Bath/Hotel-Grand-1000-Thread-Count-Egyptian-Cotton-Siberian-White-Down-Pillow/10221185/product.html";
		//urlString = "https://www.digitaltrends.com/gaming/marvels-spider-man-taskmaster-challenges-guide/";
		//urlString = "https://www.fanatics.com/nhl/toronto-maple-leafs/jerseys/o-1373+t-03040840+d-86442334+z-9-2464498415";
		//urlString = "https://www.airtable.com/articles/product/user-story";
		//urlString = "https://seygen.com/resources/articles/";
		//urlString = "https://www.seoclarity.net"; // HTTP status code 200
		//urlString = "https://www.actonia.com"; // HTTP status code 200
		//urlString = "https://www.overstock.com/Luggage-Bags/Travel-Accessories/Windsor-Home,/brand,/277/dept.html";
		//urlString = "https://www.target.com/c/earrings-jewelry/gold/-/N-5xtbiZgup4zc5xkto"; // HTTP status code 200
		//urlString = "https://www.target.com/s/toddlers+walking+shoes"; // HTTP status code 200
		//urlString = "https://www.belmond.com/ideas/articles/a-balmy-british-summer";
		//urlString = "https://www.expedia.com/Car-Rentals-In-Dromana.d6134185.Car-Rental-Guide";
		//urlString = "https://www.expedia.co.uk/Bristol-Hotels.d688.Travel-Guide-Hotels";
		//urlString = "https://www.awcwire.com/product/soow-18_12";
		//urlString = "https://www.carrollwater.com/products/";
		//urlString = "https://www.tesco.com/store-locator/directory";
		//urlString = "https://www.tesco.com/groceries/en-GB/products/*********";
		urlString = "https://www.shopbop.com/clothing-dresses/br/v=1/13351.htm";

		//userAgent = "seoClarityCrawl";
		//userAgent = "6W3vXZkRLJEQK9v9yBmkwAD6WEHH2S49wd8b3YncUVHpZX5W5r";
		//userAgent = "soasta";
		//userAgent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_2) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.88 Safari/537.36";
		//userAgent = "ClarityBot";
		//userAgent = "Mozilla/5.0 (Linux; Android 9; SM-G960F Build/PPR1.180610.011; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/74.0.3729.157 Mobile Safari/537.36 (compatible; ClarityBot/9.0; +https://www.seoclarity.net/bot.html)";
		//userAgent = "claritybot";
		//userAgent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.132 Safari/537.36";
		//userAgent = "Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)";
		//userAgent = "Screaming Frog SEO Spider";
		//userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.88 Safari/537.36";
		//userAgent = "Mozilla/5.0 (compatible; ClarityBot/9.0; +https://www.seoclarity.net/bot.html)";
		//userAgent = "Mozilla/5.0 (Linux; Android 9; SM-G960F Build/PPR1.180610.011; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/74.0.3729.157 Mobile Safari/537.36 (compatible; ClarityBot/9.0; +https://www.seoclarity.net/bot.html)";
		//userAgent = "Mozilla/5.0 (compatible; ClarityBot/9.0; +https://www.seoclarity.net/bot.html)";
		//userAgent = "ClarityBot-Expedia";
		userAgent = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 (compatible; PoliteCrawl.v2.0)";
		//userAgent = "Mozilla/5.0 (compatible; ClarityBot/9.0; +https://www.seoclarity.net/bot.html)";

		isJavascriptCrawler = false;
		boolean isStoreHtml = false;
		boolean isResponseAsHtml = false;
		String region = null;
		Integer javascriptTimeoutInSecond = 15;

		System.out.println("testScrapyApi() urlString=" + urlString);
		System.out.println("testScrapyApi() userAgent=" + userAgent);
		System.out.println("testScrapyApi() region=" + region);
		System.out.println("testScrapyApi() isJavascriptCrawler=" + isJavascriptCrawler);

		//pageCrawlerApiRequestHeaders = new HashMap<String, String>();
		//pageCrawlerApiRequestHeaders.put("x-http-claritybot", "a4259178d0b4ffcd2fa55400031c804a");
		//pageCrawlerApiRequestHeaders.put("User-Agent", "ClarityBot-Expedia");

		String s3Location = null;

		//		additionalContentEntityList = new ArrayList<AdditionalContentEntity>();
		//		AdditionalContentEntity additionalContentEntity = new AdditionalContentEntity();
		//		additionalContentEntity.setId(267);
		//		additionalContentEntity.setDomainId(9970);
		//		additionalContentEntity.setSelectorType(4);
		//		additionalContentEntity.setSelector("readmore_content");
		//		additionalContentEntity.setUrlSelectorType(2);
		//		additionalContentEntity.setUrlSelector("/paint-decorating/");
		//		additionalContentEntityList.add(additionalContentEntity);

		// to test formatted response
		scrapyCrawlerResponse = CrawlerUtils.getInstance().getScrapyFormattedResponse(ip, queueName, urlString, userAgent, additionalContentEntityList,
				isJavascriptCrawler, javascriptTimeoutInSecond, IConstants.CRAWL_TYPE_TARGET_URL_HTML, pageCrawlerApiRequestHeaders, isStoreHtml, s3Location,
				isResponseAsHtml, region); //debug

		if (scrapyCrawlerResponse != null) {
			if (StringUtils.containsIgnoreCase(scrapyCrawlerResponse.getExceptionMessage(), IConstants.ILLEGAL_STATE_EXCEPTION_MSG)
					|| StringUtils.containsIgnoreCase(scrapyCrawlerResponse.getExceptionMessage(), IConstants.GATEWAY_EXCEPTION_MSG)
					|| StringUtils.containsIgnoreCase(scrapyCrawlerResponse.getExceptionMessage(), IConstants.PAGE_CRAWLER_API_EXCEPTION_MSG)) {
				FormatUtils.getInstance().logMemoryUsage("invokePageCrawlerApi() error--ip=" + ip + ",queueName=" + queueName + ",urlString=" + urlString
						+ ",scrapyCrawlerResponse.getExceptionMessage()=" + scrapyCrawlerResponse.getExceptionMessage());
			} else {
				crawlerResponse = scrapyCrawlerResponse.getCrawlerResponse();
				if (crawlerResponse != null) {

					System.out.println("crawlerResponse.getResponse_code()=" + crawlerResponse.getResponse_code());

					htmlClickHouseEntity = new HtmlClickHouseEntity();
					// crawled data for competitor URLs is stored by domain name
					if (crawlType == IConstants.CRAWL_TYPE_COMPETITOR_URL_HTML) {
						htmlClickHouseEntity.setUrlDomain(reversedUrlDomain);
					}
					// crawled data for target URL or Link Clarity is stored by domain ID 
					else {
						htmlClickHouseEntity.setDomainId(domainId);

						// when crawling Link Clarity, filter the 'page_link' element to select backlinks from client domain name
						//if (crawlType == IConstants.CRAWL_TYPE_LINK_CLARITY) {
						//	pageLinkArray = filterLinkClarityPageLinks(domainName, crawlerResponse.getPage_link());
						//	crawlerResponse.setPage_link(pageLinkArray);
						//}
					}
					htmlClickHouseEntity.setUrl(urlString);
					htmlClickHouseEntity.setTrackDate(currentTrackDate);
					//if (isDebug == true) {
					//	htmlClickHouseEntity.setTrackDate(DateUtils.addDays(new Date(), -1));
					//}
					htmlClickHouseEntity.setCrawlerResponse(crawlerResponse);
					htmlClickHouseEntity.setCrawlTimestamp(new Date());
					//if (crawlTimestamp != null) {
					//	htmlClickHouseEntity.setTrackDate(DateUtils.truncate(crawlTimestamp, Calendar.DAY_OF_MONTH));
					//	htmlClickHouseEntity.setCrawlTimestamp(crawlTimestamp);
					//} else {
					//htmlClickHouseEntity.setCrawlTimestamp(new Date());
					//if (isDebug == true) {
					//	htmlClickHouseEntity.setCrawlTimestamp(DateUtils.addDays(htmlClickHouseEntity.getCrawlTimestamp(), -1));									
					//}
					//}
					if (StringUtils.isNotBlank(s3FileName)) {
						htmlClickHouseEntity.getCrawlerResponse().setFile_name(s3FileName);
					}
					trimmedUrlString = StringUtils.trimToEmpty(urlString);
					htmlClickHouseEntity.setInternalLinkCount(CrawlerUtils.getInstance().getInternalLinkCount(trimmedUrlString, crawlerResponse));
					htmlClickHouseEntity.setSign(IConstants.CLICKHOUSE_SIGN_POSITIVE_1);
					if (htmlClickHouseEntity.getHttpStatusCode() != null) {
						if (htmlClickHouseEntity.getHttpStatusCode().intValue() == 200) {
							// aggregate page analysis results
							pageAnalysisResultArray = CrawlerUtils.getInstance().getPageAnalysisResultArray(crawlerResponse);
							if (pageAnalysisResultArray != null && pageAnalysisResultArray.length > 0) {
								htmlClickHouseEntity.setPageAnalysisResultArray(pageAnalysisResultArray);
								htmlClickHouseEntity.setPageAnalysisResultsReverse(CrawlerUtils.getInstance().reversePageAnalysisResults(pageAnalysisResultArray));
								htmlClickHouseEntity.setPageAnalysisFragmentsArray(CrawlerUtils.getInstance().getPageAnalysisFragmentsArray(crawlerResponse));
							}
						}
						htmlClickHouseEntity.setWeekOfYear(CommonUtils.calculateWeekOfYear(currentTrackDate));
						System.out.println("htmlClickHouseEntity=" + new Gson().toJson(htmlClickHouseEntity, HtmlClickHouseEntity.class));
					} else {
						FormatUtils.getInstance().logMemoryUsage("invokePageCrawlerApi() error--ip=" + ip + ",queueName=" + queueName + ",urlString=" + urlString
								+ ",htmlClickHouseEntity.getHttpStatusCode() is null.");
						//return getHtmlClickHouseEntityWithHttpStatusCode999(urlString, currentTrackDate, crawlTimestamp);
					}
				} else {
					FormatUtils.getInstance().logMemoryUsage(
							"invokePageCrawlerApi() error--ip=" + ip + ",queueName=" + queueName + ",urlString=" + urlString + ",crawlerResponse is null.");
					//return getHtmlClickHouseEntityWithHttpStatusCode999(urlString, currentTrackDate, crawlTimestamp);
				}

			}
		} else {
			FormatUtils.getInstance().logMemoryUsage(
					"invokePageCrawlerApi() error--ip=" + ip + ",queueName=" + queueName + ",urlString=" + urlString + ",scrapyCrawlerResponse is null.");
			//return getHtmlClickHouseEntityWithHttpStatusCode999(urlString, currentTrackDate, crawlTimestamp);
		}

	}

	private void testCompetitorUrlHtmlGetPrevious() {

		String ip = "";
		String queueName = "";
		String urlString = "https://www.muscletech.ca/products/pro-series/premium-gold-100-whey-protein/";
		HtmlClickHouseEntity htmlClickHouseEntityPrevious = null;
		String urlDomain = "ca.muscletech.www";

		int previousWeekOfYear = 0;
		int currentWeekOfYear = 0;
		try {
			htmlClickHouseEntityPrevious = CompetitorUrlHtmlClickHouseDAO.getInstance().getPrevious(ip, queueName, urlDomain, urlString,
					getCompetitorUrlHtmlPreviousFieldNames(), IConstants.CLICKHOUSE_TABLE_NAME_NULL, null);
			previousWeekOfYear = htmlClickHouseEntityPrevious.getWeekOfYear();
			currentWeekOfYear = CommonUtils.calculateWeekOfYear(new Date());
			System.out.println("previousWeekOfYear=" + previousWeekOfYear);
			System.out.println("currentWeekOfYear=" + currentWeekOfYear);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private List<String> getCompetitorUrlHtmlPreviousFieldNames() {
		if (COMPETITOR_URL_HTML_PREVIOUS_FIELD_NAME_LIST == null || COMPETITOR_URL_HTML_PREVIOUS_FIELD_NAME_LIST.size() == 0) {
			COMPETITOR_URL_HTML_PREVIOUS_FIELD_NAME_LIST = new ArrayList<String>();
			COMPETITOR_URL_HTML_PREVIOUS_FIELD_NAME_LIST.add(IConstants.WEEK_OF_YEAR);
			COMPETITOR_URL_HTML_PREVIOUS_FIELD_NAME_LIST.add(IConstants.CHANGE_TRACKING_HASH);
		}
		return COMPETITOR_URL_HTML_PREVIOUS_FIELD_NAME_LIST;
	}

	private Boolean testCheckIfStopCrawl() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		Boolean output = false;
		String stopCrawlQueueName = PoliteCrawlConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_STOP_CRAWL_QUEUE);
		String stopCrawlQueueUrl = SQSUtils.getInstance().createQueue(stopCrawlQueueName);
		List<Message> messageList = new ArrayList<Message>();
		Message message = null;
		UrlCrawlParametersVO[] urlCrawlParametersVoArray = null;
		UrlCrawlParametersVO urlCrawlParametersVO = null;
		Gson gson = new Gson();
		String messageBody = null;
		String stopCrawlString = null;

		try {
			// There is one message (in JSON format) in the controller queue for
			// all running cloud instances
			// do not remove message from stop crawl queue after required
			// information has been retrieved so that other instances can
			// retrieve the same data
			messageList = SQSUtils.getInstance().getMessageFromQueue(stopCrawlQueueUrl, 10, IConstants.SQS_MSG_TIMEOUT_IN_SEC);
			if (messageList != null && messageList.size() > 0) {
				message = messageList.get(0);
				messageBody = message.getBody();
				urlCrawlParametersVoArray = gson.fromJson(messageBody, UrlCrawlParametersVO[].class);
				if (urlCrawlParametersVoArray != null && urlCrawlParametersVoArray.length > 0) {
					for (int idx = 0; idx < urlCrawlParametersVoArray.length; idx++) {
						urlCrawlParametersVO = urlCrawlParametersVoArray[idx];
						FormatUtils.getInstance().logMemoryUsage("checkIfStopCrawl() urlCrawlParametersVO.getType()=" + urlCrawlParametersVO.getType());
						FormatUtils.getInstance().logMemoryUsage("checkIfStopCrawl() urlCrawlParametersVO.getData()=" + urlCrawlParametersVO.getData());
						// stop crawl
						if (urlCrawlParametersVO.getType().equalsIgnoreCase(IConstants.STOP_CRAWL)) {
							stopCrawlString = urlCrawlParametersVO.getData();
							if (StringUtils.isNotBlank(stopCrawlString)) {
								output = new Boolean(stopCrawlString);
							}
						}
					}
				}
			}
		} catch (Exception exception) {
			exception.printStackTrace();
		}
		FormatUtils.getInstance().logMemoryUsage("checkIfStopCrawl() messageBody=" + messageBody + ",output=" + output + ",total elapsed time in sec.="
				+ (System.currentTimeMillis() - startTimestamp) / 1000);
		return output;
	}

	private void testUpdateChangeTrackingChangeIndicator() throws Exception {
		HtmlClickHouseEntity htmlClickHouseEntity = new HtmlClickHouseEntity();
		System.out.println("b4 htmlClickHouseEntity.getAlternateLinksChgInd()=" + htmlClickHouseEntity.getAlternateLinksChgInd());
		CrawlerUtils.getInstance().trackChanges(null, htmlClickHouseEntity, null, null, null, null, 0, null, null);
		System.out.println("af htmlClickHouseEntity.getAlternateLinksChgInd()=" + htmlClickHouseEntity.getAlternateLinksChgInd());
	}

	private void testSerializedUrlUrlMetricsEntityV3() {

		String json = "{\"languageCode\":\"en\",\"targetUrlHtmlTrackDate\":\"2019-09-14\",\"response_code\":\"404\",\"targetUrlHtmlDailyDataInd\":true,\"urlId\":80027935589282222,\"url\":\"https://www.seoclarity.net/research-and-discovery\"}";
		Gson gson = new Gson();
		UrlMetricsEntityV3 urlMetricsEntityV3 = gson.fromJson(json, UrlMetricsEntityV3.class);
		System.out.println("urlMetricsEntityV3=" + urlMetricsEntityV3.toString());
	}

	private void testGetPageAnalysisResultsChgIndJson() throws Exception {
		PageAnalysisResultChgInd[] pageAnalysisResultChgIndArray = new PageAnalysisResultChgInd[3];
		PageAnalysisResultChgInd pageAnalysisResultChgInd = null;

		pageAnalysisResultChgInd = new PageAnalysisResultChgInd();
		pageAnalysisResultChgInd.setRule_nbr(1);
		pageAnalysisResultChgInd.setChg_ind(1);
		pageAnalysisResultChgIndArray[0] = pageAnalysisResultChgInd;

		pageAnalysisResultChgInd = new PageAnalysisResultChgInd();
		pageAnalysisResultChgInd.setRule_nbr(2);
		pageAnalysisResultChgInd.setChg_ind(0);
		pageAnalysisResultChgIndArray[1] = pageAnalysisResultChgInd;

		pageAnalysisResultChgInd = new PageAnalysisResultChgInd();
		pageAnalysisResultChgInd.setRule_nbr(3);
		pageAnalysisResultChgInd.setChg_ind(1);
		pageAnalysisResultChgIndArray[2] = pageAnalysisResultChgInd;

		String pageAnalysisResultsChgIndJson = CrawlerUtils.getInstance().transformPageAnalysisResultsChgIndArrayToString(pageAnalysisResultChgIndArray);
		System.out.println("pageAnalysisResultsChgIndJson=" + pageAnalysisResultsChgIndJson);
	}

	private void testConvertPageAnalysisChangeIndJson() {
		JsonObject jsonObject = new JsonObject();
		jsonObject.addProperty("11", 0);
		jsonObject.addProperty("12", 1);
		System.out.println(jsonObject.toString());
	}

	private void testMd5OfDifferenceCases() throws Exception {

		String urlString1 = "34";
		String urlString2 = "30";
		String urlMd5Hash1 = CrawlerUtils.getInstance().getMd5HashCode(urlString1);
		String urlMd5Hash2 = CrawlerUtils.getInstance().getMd5HashCode(urlString2);
		System.out.println("urlString1=" + urlString1);
		System.out.println("urlMd5Hash1=" + urlMd5Hash1);
		System.out.println("urlString2=" + urlString2);
		System.out.println("urlMd5Hash2=" + urlMd5Hash2);

	}

	private void testBooleanUtils() {
		Boolean input = null;
		System.out.println("input=" + input);
		System.out.println("BooleanUtils.isTrue(input)=" + BooleanUtils.isTrue(input));

		input = true;
		System.out.println("input=" + input);
		System.out.println("BooleanUtils.isTrue(input)=" + BooleanUtils.isTrue(input));

		input = false;
		System.out.println("input=" + input);
		System.out.println("BooleanUtils.isTrue(input)=" + BooleanUtils.isTrue(input));

	}

	private void testLongConversion() {
		String inputString = "15416610004542656267";
		System.out.println("inputString=" + inputString);
		Long testLong = new Long(inputString);
		System.out.println("testLong=" + testLong);
	}
}