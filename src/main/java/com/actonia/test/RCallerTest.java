package com.actonia.test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.RUtils;
import com.actonia.value.object.AreaDateValue;
import com.actonia.value.object.BestMatch;
import com.actonia.value.object.CausalImpactObject;
import com.actonia.value.object.CausalImpactRequest;
import com.actonia.value.object.CausalImpactResponse;
import com.actonia.value.object.Coefficients;
import com.actonia.value.object.Inference;
import com.actonia.value.object.MarketMatchingRequest;
import com.actonia.value.object.MarketMatchingResponse;
import com.actonia.value.object.PosteriorInference;
import com.actonia.value.object.ProphetRequest;
import com.actonia.value.object.ProphetResponse;
import com.actonia.value.object.RRequest;
import com.actonia.value.object.RResponse;
import com.github.rcaller.datatypes.DataFrame;
import com.github.rcaller.rstuff.RCaller;
import com.github.rcaller.rstuff.RCode;
import com.google.gson.Gson;

public class RCallerTest {

	public static void main(String[] args) {
		try {
			new RCallerTest().runTests();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private void runTests() throws Exception {
		testRUtilsMarketMatching();
		//testMarketMatching();
		//testProphet();
		//testGetDay();
		//testGetMonth();
		//testExtractDoubleArray();
		//testCorrelationCoefficient();
		//testCausalImpact();
	}

	private void testRUtilsMarketMatching() throws Exception {
		System.out.println("testRUtilsMarketMatching() begins.");
		long startTimestamp = System.currentTimeMillis();
		MarketMatchingRequest marketMatchingRequest = getMarketMatchingRequest();
		System.out.println("testRUtilsMarketMatching() requestJson=" + new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class));
		String command = null;
		MarketMatchingResponse marketMatchingResponse = null;

		RRequest rRequest = new RRequest();
		rRequest.setrPackage(IConstants.R_PACKAGE_MARKET_MATCHING);
		rRequest.setMarketMatchingRequest(marketMatchingRequest);
		rRequest.setCommand(command);

		RResponse rResponse = RUtils.getInstance().invokeR(rRequest);
		if (rResponse != null) {
			marketMatchingResponse = rResponse.getMarketMatchingResponse();
			System.out.println("testRUtilsMarketMatching() responseJson=" + new Gson().toJson(marketMatchingResponse, MarketMatchingResponse.class));
		}
		System.out.println("testRUtilsMarketMatching() ends. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void testMarketMatching() throws Exception {
		System.out.println("testMarketMatching() begins.");
		long startTimestamp = System.currentTimeMillis();
		long testStartTimestamp = 0L;
		RCaller rCaller = null;
		RCode code = null;
		String[] testStringArray = null;
		Long totalPreAndPostPeriodDays = null;
		Object[][] objects = null;
		String[] names = null;
		DataFrame dataFrame = null;
		MarketMatchingRequest marketMatchingRequest = null;
		MarketMatchingResponse marketMatchingResponse = null;

		List<BestMatch> bestMatchList = new ArrayList<BestMatch>();
		BestMatch bestMatch = null;
		List<String> areaList = new ArrayList<String>();
		List<String> bestControlList = new ArrayList<String>();
		List<Double> relativeDistanceList = new ArrayList<Double>();
		List<Double> correlationList = new ArrayList<Double>();
		List<Double> lengthList = new ArrayList<Double>();
		List<Double> sumTestList = new ArrayList<Double>();
		List<Double> sumCntlList = new ArrayList<Double>();
		List<Double> rawDistList = new ArrayList<Double>();
		List<Double> correlationOfLogsList = new ArrayList<Double>();
		List<Double> rankList = new ArrayList<Double>();
		List<Double> normDistList = new ArrayList<Double>();

		Inference inference = new Inference();

		Coefficients coefficients = new Coefficients();
		List<String> marketList = new ArrayList<String>();
		List<Double> averageBetaList = new ArrayList<Double>();

		CausalImpactObject causalImpactObject = new CausalImpactObject();

		String requestJson = null;
		String responseJson = null;
		boolean isBestMatchAvailable = false;

		try {
			marketMatchingRequest = getMarketMatchingRequest();
			requestJson = new Gson().toJson(marketMatchingRequest, MarketMatchingRequest.class);
			System.out.println("testMarketMatching() requestJson=" + requestJson);
			totalPreAndPostPeriodDays = ChronoUnit.DAYS.between(LocalDate.parse(marketMatchingRequest.getPre_period_start_date()),
					LocalDate.parse(marketMatchingRequest.getPost_period_end_date())) + IConstants.LONG_ONE;
			System.out.println("testMarketMatching() totalPreAndPostPeriodDays=" + totalPreAndPostPeriodDays);

			marketMatchingResponse = new MarketMatchingResponse();

			rCaller = RCaller.create();
			code = RCode.create();
			code.addRCode("library(MarketMatching)");
			code.addRCode("library(magrittr)");
			code.addRCode("library(tidyverse)");
			objects = new Object[][] { getAreaArray(marketMatchingRequest), getDateArray(marketMatchingRequest), getValueArray(marketMatchingRequest) };
			names = new String[] { "Area", "Date", "Value" };
			dataFrame = DataFrame.create(objects, names);
			code.addDataFrame("dataframe", dataFrame);
			code.addRCode("dataframe <- dataframe %>% ");
			code.addRCode(" mutate(Date = as.Date(Date))");
			code.addRCode("mm <- MarketMatching::best_matches(data=dataframe,id_variable=\"Area\",markets_to_be_matched=c(\"" + marketMatchingRequest.getTest_market()
					+ "\"),date_variable=\"Date\",matching_variable=\"Value\",parallel=TRUE,warping_limit=1,dtw_emphasis=1,matches=5,start_match_period=\""
					+ marketMatchingRequest.getPre_period_start_date() + "\",end_match_period=\"" + marketMatchingRequest.getPre_period_end_date() + "\")");
			code.addRCode("results <- MarketMatching::inference(matched_markets = mm,test_market = \"" + marketMatchingRequest.getTest_market() + "\")");
			rCaller.setRCode(code);

			testStartTimestamp = System.currentTimeMillis();
			rCaller.runAndReturnResultOnline("mm$BestMatches");
			//System.out.println("mm$BestMatches XML="+rCaller.getParser().getXMLFileAsString());
			for (String name : rCaller.getParser().getNames()) {
				//System.out.println("mm$BestMatches rCaller.getParser().getNames()=" + name);
				testStringArray = rCaller.getParser().getAsStringArray(name);
				for (String value : testStringArray) {
					//System.out.println("mm$BestMatches name=" + name + ",value=" + value);
					isBestMatchAvailable = true;
					if (StringUtils.equalsIgnoreCase(name, IConstants.AREA)) {
						areaList.add(value);
					} else if (StringUtils.equalsIgnoreCase(name, IConstants.BEST_CONTROL)) {
						bestControlList.add(value);
					} else if (StringUtils.equalsIgnoreCase(name, IConstants.RELATIVE_DISTANCE)) {
						relativeDistanceList.add(new Double(value));
					} else if (StringUtils.equalsIgnoreCase(name, IConstants.CORRELATION)) {
						correlationList.add(new Double(value));
					} else if (StringUtils.equalsIgnoreCase(name, IConstants.LENGTH)) {
						lengthList.add(new Double(value));
					} else if (StringUtils.equalsIgnoreCase(name, IConstants.SUM_TEST)) {
						sumTestList.add(new Double(value));
					} else if (StringUtils.equalsIgnoreCase(name, IConstants.SUM_CNTL)) {
						sumCntlList.add(new Double(value));
					} else if (StringUtils.equalsIgnoreCase(name, IConstants.RAW_DIST)) {
						rawDistList.add(new Double(value));
					} else if (StringUtils.equalsIgnoreCase(name, IConstants.CORRELATION_OF_LOGS)) {
						correlationOfLogsList.add(new Double(value));
					} else if (StringUtils.equalsIgnoreCase(name, IConstants.RANK)) {
						rankList.add(new Double(value));
					} else if (StringUtils.equalsIgnoreCase(name, IConstants.NORM_DIST)) {
						normDistList.add(new Double(value));
					}
				}
			}
			if (isBestMatchAvailable == true) {
				for (int i = 0; i < areaList.size(); i++) {
					bestMatch = new BestMatch();
					bestMatch.setArea(areaList.get(i));
					bestMatch.setBest_control(bestControlList.get(i));
					bestMatch.setRelative_distance(relativeDistanceList.get(i));
					bestMatch.setCorrelation(correlationList.get(i));
					bestMatch.setLength(lengthList.get(i));
					bestMatch.setSum_test(sumTestList.get(i));
					bestMatch.setSum_cntl(sumCntlList.get(i));
					bestMatch.setRaw_dist(rawDistList.get(i));
					bestMatch.setCorrelation_of_logs(correlationOfLogsList.get(i));
					bestMatch.setRank(rankList.get(i));
					bestMatch.setNorm_dist(normDistList.get(i));
					bestMatchList.add(bestMatch);
				}
			}

			if (bestMatchList != null && bestMatchList.size() > 0) {
				marketMatchingResponse.setSuccess(true);
				marketMatchingResponse.setTest_market(marketMatchingRequest.getTest_market());
				marketMatchingResponse.setPre_period_start_date(marketMatchingRequest.getPre_period_start_date());
				marketMatchingResponse.setPre_period_end_date(marketMatchingRequest.getPre_period_end_date());
				marketMatchingResponse.setPost_period_start_date(marketMatchingRequest.getPost_period_start_date());
				marketMatchingResponse.setPost_period_end_date(marketMatchingRequest.getPost_period_end_date());
				marketMatchingResponse.setBest_match_array(bestMatchList.toArray(new BestMatch[0]));
			}

			System.out.println("testMarketMatching() mm$BestMatches elapsed(ms.)=" + (System.currentTimeMillis() - testStartTimestamp));

			testStartTimestamp = System.currentTimeMillis();
			rCaller.runAndReturnResultOnline("results");
			//System.out.println("mm$results XML="+rCaller.getParser().getXMLFileAsString());
			for (String name : rCaller.getParser().getNames()) {
				//System.out.println("results$Predictions rCaller.getParser().getNames()=" + name);
				if (StringUtils.equalsIgnoreCase(name, IConstants.TEST_DATA) == false && StringUtils.equalsIgnoreCase(name, IConstants.ZOO_DATA) == false
						&& StringUtils.equalsIgnoreCase(name, IConstants.TEST_NAME) == false && StringUtils.equalsIgnoreCase(name, IConstants.CONTROL_NAME) == false) {
					testStringArray = rCaller.getParser().getAsStringArray(name);
					for (String value : testStringArray) {
						//System.out.println("results name=" + name + ",value=" + value);
						if (StringUtils.equalsIgnoreCase(name, IConstants.ABSOLUTE_EFFECT)) {
							inference.setAbsolute_effect(new Double(value));
						} else if (StringUtils.equalsIgnoreCase(name, IConstants.ABSOLUTE_EFFECT_LOWER)) {
							inference.setAbsolute_effect_lower(new Double(value));
						} else if (StringUtils.equalsIgnoreCase(name, IConstants.ABSOLUTE_EFFECT_UPPER)) {
							inference.setAbsolute_effect_upper(new Double(value));
						} else if (StringUtils.equalsIgnoreCase(name, IConstants.RELATIVE_EFFECT)) {
							inference.setRelative_effect(new Double(value));
						} else if (StringUtils.equalsIgnoreCase(name, IConstants.RELATIVE_EFFECT_LOWER)) {
							inference.setRelative_effect_lower(new Double(value));
						} else if (StringUtils.equalsIgnoreCase(name, IConstants.RELATIVE_EFFECT_UPPER)) {
							inference.setRelative_effect_upper(new Double(value));
						} else if (StringUtils.equalsIgnoreCase(name, IConstants.TAIL_PROB)) {
							inference.setTail_prob(new Double(value));
						} else if (StringUtils.equalsIgnoreCase(name, IConstants.PRE_PERIOD_MAPE)) {
							inference.setPre_period_mape(new Double(value));
						} else if (StringUtils.equalsIgnoreCase(name, IConstants.DW)) {
							inference.setDw(new Double(value));
						}
					}
				}
			}
			marketMatchingResponse.setInference(inference);
			System.out.println("testMarketMatching() results elapsed(ms.)=" + (System.currentTimeMillis() - testStartTimestamp));

			testStartTimestamp = System.currentTimeMillis();
			rCaller.runAndReturnResultOnline("results$CausalImpactObject");
			for (String name : rCaller.getParser().getNames()) {
				//System.out.println("results$Predictions rCaller.getParser().getNames()=" + name);
				testStringArray = rCaller.getParser().getAsStringArray(name);
				for (String value : testStringArray) {
					//System.out.println("results$CausalImpactObject name=" + name + ",value=" + value);
					if (StringUtils.equalsIgnoreCase(name, IConstants.REPORT)) {
						causalImpactObject.setReport(value);
					} else if (StringUtils.equalsIgnoreCase(name, IConstants.SERIES)) {
						causalImpactObject.setTest_time_series(
								RUtils.getInstance().extractTimeSeries(IConstants.TEST_DATA_POINTS_POSITION, totalPreAndPostPeriodDays, testStringArray));

						causalImpactObject.setCounter_factual_prediction_time_series(RUtils.getInstance()
								.extractTimeSeries(IConstants.COUNTER_FACTUAL_PREDICTION_DATA_POINTS_POSITION, totalPreAndPostPeriodDays, testStringArray));
						causalImpactObject.setCounter_factual_prediction_time_series_lower(RUtils.getInstance()
								.extractTimeSeries(IConstants.COUNTER_FACTUAL_PREDICTION_DATA_POINTS_POSITION_LOWER, totalPreAndPostPeriodDays, testStringArray));
						causalImpactObject.setCounter_factual_prediction_time_series_upper(RUtils.getInstance()
								.extractTimeSeries(IConstants.COUNTER_FACTUAL_PREDICTION_DATA_POINTS_POSITION_UPPER, totalPreAndPostPeriodDays, testStringArray));

						causalImpactObject.setPointwise_causal_effect_time_series(RUtils.getInstance()
								.extractTimeSeries(IConstants.POINTWISE_CAUSAL_EFFECT_DATA_POINTS_POSITION, totalPreAndPostPeriodDays, testStringArray));
						causalImpactObject.setPointwise_causal_effect_time_series_lower(RUtils.getInstance()
								.extractTimeSeries(IConstants.POINTWISE_CAUSAL_EFFECT_DATA_POINTS_POSITION_LOWER, totalPreAndPostPeriodDays, testStringArray));
						causalImpactObject.setPointwise_causal_effect_time_series_upper(RUtils.getInstance()
								.extractTimeSeries(IConstants.POINTWISE_CAUSAL_EFFECT_DATA_POINTS_POSITION_UPPER, totalPreAndPostPeriodDays, testStringArray));

						causalImpactObject.setCumulative_effect_time_series(
								RUtils.getInstance().extractTimeSeries(IConstants.CUMULATIVE_EFFECT_DATA_POINTS_POSITION, totalPreAndPostPeriodDays, testStringArray));
						causalImpactObject.setCumulative_effect_time_series_lower(RUtils.getInstance()
								.extractTimeSeries(IConstants.CUMULATIVE_EFFECT_DATA_POINTS_POSITION_LOWER, totalPreAndPostPeriodDays, testStringArray));
						causalImpactObject.setCumulative_effect_time_series_upper(RUtils.getInstance()
								.extractTimeSeries(IConstants.CUMULATIVE_EFFECT_DATA_POINTS_POSITION_UPPER, totalPreAndPostPeriodDays, testStringArray));
					}
				}
			}
			marketMatchingResponse.setCausal_impact_object(causalImpactObject);
			System.out.println("testMarketMatching() results$CausalImpactObject elapsed(ms.)=" + (System.currentTimeMillis() - testStartTimestamp));

			testStartTimestamp = System.currentTimeMillis();
			rCaller.runAndReturnResultOnline("results$Coefficients");
			for (String name : rCaller.getParser().getNames()) {
				//System.out.println("results$Coefficients rCaller.getParser().getNames()=" + name);
				testStringArray = rCaller.getParser().getAsStringArray(name);
				for (String value : testStringArray) {
					//System.out.println("results$Coefficients name=" + name + ",value=" + value);
					if (StringUtils.equalsIgnoreCase(name, IConstants.MARKET)) {
						marketList.add(value);
					} else if (StringUtils.equalsIgnoreCase(name, IConstants.AVERAGE_BETA)) {
						averageBetaList.add(new Double(value));
					}
				}
			}
			coefficients.setMarket_array(marketList.toArray(new String[0]));
			coefficients.setAverage_beta(averageBetaList.toArray(new Double[0]));
			marketMatchingResponse.setCoefficients(coefficients);
			System.out.println("testMarketMatching() results$Coefficients elapsed(ms.)=" + (System.currentTimeMillis() - testStartTimestamp));

			responseJson = new Gson().toJson(marketMatchingResponse, MarketMatchingResponse.class);
			System.out.println("testMarketMatching() responseJson=" + responseJson);
			System.out.println("testMarketMatching() ends. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		} catch (Exception e) {
			throw e;
		} finally {
			if (rCaller != null) {
				rCaller.stopRCallerOnline();
			}
		}
	}

	private int getTestMarketTotalNumberOfValues(MarketMatchingRequest marketMatchingRequest) {
		int testMarketTotalNumberOfValues = 0;
		String testMarket = marketMatchingRequest.getTest_market();
		for (AreaDateValue areaDateValue : marketMatchingRequest.getArea_date_value_array()) {
			if (StringUtils.equalsIgnoreCase(areaDateValue.getArea(), testMarket)) {
				testMarketTotalNumberOfValues++;
			}
		}
		return testMarketTotalNumberOfValues;
	}

	private String getTestMarketStartPostPeriod(String testMarketEndPrePeriod) throws Exception {
		Date testDate = DateUtils.parseDate(testMarketEndPrePeriod, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
		testDate = DateUtils.addDays(testDate, +1);
		return DateFormatUtils.format(testDate, IConstants.DATE_FORMAT_YYYY_MM_DD);
	}

	private String getTestMarketEndPostPeriod(MarketMatchingRequest marketMatchingRequest) {
		int length = marketMatchingRequest.getArea_date_value_array().length;
		AreaDateValue areaDateValue = marketMatchingRequest.getArea_date_value_array()[length - 1];
		return areaDateValue.getDate();
	}

	private String[] getAreaArray(MarketMatchingRequest marketMatchingRequest) {
		String[] areaArray = null;
		int totalLength = marketMatchingRequest.getArea_date_value_array().length;
		areaArray = new String[totalLength];
		for (int i = 0; i < totalLength; i++) {
			areaArray[i] = marketMatchingRequest.getArea_date_value_array()[i].getArea();
		}
		return areaArray;
	}

	private String[] getDateArray(MarketMatchingRequest marketMatchingRequest) {
		String[] dateArray = null;
		int totalLength = marketMatchingRequest.getArea_date_value_array().length;
		dateArray = new String[totalLength];
		for (int i = 0; i < totalLength; i++) {
			dateArray[i] = marketMatchingRequest.getArea_date_value_array()[i].getDate();
		}
		return dateArray;
	}

	private Double[] getValueArray(MarketMatchingRequest marketMatchingRequest) {
		Double[] valueArray = null;
		int totalLength = marketMatchingRequest.getArea_date_value_array().length;
		valueArray = new Double[totalLength];
		for (int i = 0; i < totalLength; i++) {
			valueArray[i] = marketMatchingRequest.getArea_date_value_array()[i].getValue();
		}
		return valueArray;
	}

	private MarketMatchingRequest getMarketMatchingRequest() {
		MarketMatchingRequest marketMatchingRequest = new MarketMatchingRequest();
		marketMatchingRequest.setTest_market("CPH");
		marketMatchingRequest.setPre_period_start_date("2014-01-01");
		marketMatchingRequest.setPre_period_end_date("2014-10-01");
		marketMatchingRequest.setPost_period_start_date("2014-10-02");
		marketMatchingRequest.setPost_period_end_date("2014-12-31");
		marketMatchingRequest.setNumber_of_best_matches(5);

		List<AreaDateValue> areaDateValueList = new ArrayList<AreaDateValue>();
		AreaDateValue areaDateValue = null;
		String[] areaArray = getAreaArrayForMarketMatching();
		String[] dateArray = getDateArrayForMarketMatching();
		int[] integerArray = getIntArrayForMarketMatching();
		Double testDouble = null;
		for (int i = 0; i < integerArray.length; i++) {
			areaDateValue = new AreaDateValue();
			areaDateValue.setArea(areaArray[i]);
			areaDateValue.setDate(dateArray[i]);
			testDouble = new Double(integerArray[i]);
			areaDateValue.setValue(testDouble);
			areaDateValueList.add(areaDateValue);
		}
		marketMatchingRequest.setArea_date_value_array(areaDateValueList.toArray(new AreaDateValue[0]));
		return marketMatchingRequest;
	}

	private void testProphet() throws Exception {

		long startTimestamp = System.currentTimeMillis();
		RCaller rCaller = null;
		RCode code = null;
		//String[] testStringArray = null;
		double[] testDoubleArray = null;

		Object[][] objects = null;
		String[] names = null;
		DataFrame dataFrame = null;
		ProphetRequest prophetRequest = null;
		ProphetResponse prophetResponse = null;
		String responseJson = null;

		try {
			prophetRequest = getProphetRequest();
			prophetResponse = new ProphetResponse();
			prophetResponse.setDate_array(getResponseDateArray(prophetRequest));

			rCaller = RCaller.create();
			code = RCode.create();
			code.addRCode("library(prophet)");
			objects = new Object[][] { prophetRequest.getDate_array(), prophetRequest.getValue_array() };
			names = new String[] { "ds", "y" };
			dataFrame = DataFrame.create(objects, names);
			code.addDataFrame("df", dataFrame);
			code.addRCode("m <- prophet(df)");
			code.addRCode("future <- make_future_dataframe(m, periods = " + prophetRequest.getForecast_days() + ")");
			code.addRCode("forecast <- predict(m, future)");
			rCaller.setRCode(code);

			//rCaller.runAndReturnResultOnline("forecast");
			rCaller.runAndReturnResult("forecast");
			//System.out.println(rCaller.getParser().getXMLFileAsString());
			for (String name : rCaller.getParser().getNames()) {
				System.out.println("rCaller.getParser().getNames()=" + name);
				//testStringArray = rCaller.getParser().getAsStringArray(name);
				//for (String testString : testStringArray) {
				//	System.out.println("name=" + name + ",value=" + testString);
				//}
				testDoubleArray = rCaller.getParser().getAsDoubleArray(name);

				if (StringUtils.equalsIgnoreCase(name, IConstants.TREND)) {
					prophetResponse.setTrend_array(testDoubleArray);
				} else if (StringUtils.equalsIgnoreCase(name, IConstants.TREND_LOWER)) {
					prophetResponse.setTrend_lower_array(testDoubleArray);
				} else if (StringUtils.equalsIgnoreCase(name, IConstants.TREND_UPPER)) {
					prophetResponse.setTrend_upper_array(testDoubleArray);
				} else if (StringUtils.equalsIgnoreCase(name, IConstants.YHAT)) {
					prophetResponse.setForecast_array(testDoubleArray);
				} else if (StringUtils.equalsIgnoreCase(name, IConstants.YHAT_LOWER)) {
					prophetResponse.setForecast_lower_array(testDoubleArray);
				} else if (StringUtils.equalsIgnoreCase(name, IConstants.YHAT_UPPER)) {
					prophetResponse.setForecast_upper_array(testDoubleArray);
				} else if (StringUtils.equalsIgnoreCase(name, IConstants.WEEKLY)) {
					prophetResponse.setWeekly_seasonality_map(getWeeklySeasonalityMap(prophetRequest.getDate_array(), testDoubleArray));
				} else if (StringUtils.equalsIgnoreCase(name, IConstants.YEARLY)) {
					prophetResponse.setYearly_seasonality_array(getYearlySeasonalityArray(prophetRequest.getDate_array(), testDoubleArray));
				}

				//for (double testDouble : testDoubleArray) {
				//	System.out.println("name=" + name + ",value=" + testDouble);
				//}
			}

			responseJson = new Gson().toJson(prophetResponse, ProphetResponse.class);
			System.out.println("testProphet() responseJson=" + responseJson);
			System.out.println("testProphet() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		} catch (Exception e) {
			throw e;
		} finally {
			//if (rCaller != null) {
			//	rCaller.stopRCallerOnline();
			//}
		}

	}

	private String[] getResponseDateArray(ProphetRequest prophetRequest) throws Exception {
		Date testDate = null;
		String testString = null;
		List<String> dateList = Arrays.asList(prophetRequest.getDate_array());
		List<String> responseDateList = new ArrayList<String>(dateList);
		String startDateString = prophetRequest.getDate_array()[prophetRequest.getDate_array().length - 1];
		//System.out.println("getResponseDateArray() startDateString=" + startDateString);
		Date startDate = DateUtils.parseDateStrictly(startDateString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
		int forecastDays = prophetRequest.getForecast_days();
		for (int i = 0; i < forecastDays; i++) {
			testDate = DateUtils.addDays(startDate, (i + 1));
			testString = DateFormatUtils.format(testDate, IConstants.DATE_FORMAT_YYYY_MM_DD);
			//System.out.println("getResponseDateArray() testString=" + testString);
			responseDateList.add(testString);
		}
		return responseDateList.toArray(new String[0]);
	}

	private Map<Integer, Double> getWeeklySeasonalityMap(String[] dateArray, double[] weeklyArray) {

		// map key = days of week
		// map value = seasonality value
		Map<Integer, Double> weeklySeasonalityMap = null;

		Integer dayOfWeekNumber = null;
		//String dayOfWeekName = null;

		// there must be at least one week's data
		if (dateArray.length >= 7 && weeklyArray.length >= 7) {
			weeklySeasonalityMap = new HashMap<Integer, Double>();
			nextDate: for (int i = 0; i < dateArray.length; i++) {
				dayOfWeekNumber = LocalDate.parse(dateArray[i]).getDayOfWeek().getValue();
				//dayOfWeekName = LocalDate.parse(dateArray[i]).getDayOfWeek().getDisplayName(TextStyle.FULL, Locale.US);
				//System.out.println(
				//		"getWeeklySeasonalityMap() dayOfWeekNumber=" + dayOfWeekNumber + ",dayOfWeekName=" + dayOfWeekName + ",weeklySeasonality=" + weeklyArray[i]);
				if (weeklySeasonalityMap.containsKey(dayOfWeekNumber) == false) {
					weeklySeasonalityMap.put(dayOfWeekNumber, weeklyArray[i]);
				}

				// when all day of weeks have been processed
				if (weeklySeasonalityMap.size() == 7) {
					break nextDate;
				}
			}
		}

		for (Integer testDayOfWeekNumber : weeklySeasonalityMap.keySet()) {
			System.out.println(
					"getWeeklySeasonalityMap() dayOfWeekNumber=" + testDayOfWeekNumber + ",weeklySeasonality=" + weeklySeasonalityMap.get(testDayOfWeekNumber));
		}

		return weeklySeasonalityMap;
	}

	private double[] getYearlySeasonalityArray(String[] dateArray, double[] yearlyArray) {

		double[] yearlySeasonalityArray = null;
		int dayOfYearNumber = 0;
		Integer month = null;
		Integer day = null;
		int lastDayOfMonth = 0;
		String nonLeapYearDateString = null;
		Map<Integer, Integer> monthLastDayMap = null;
		String dateString = null;
		double yearlySeasonality = 0d;

		Map<Integer, Double> dayOfYearSeasonalityMap = new HashMap<Integer, Double>();

		// there must be at least one year's data
		if (dateArray.length >= 365 && yearlyArray.length >= 365) {
			monthLastDayMap = FormatUtils.getInstance().getMonthLastDayMap();
			nextDate: for (int i = 0; i < dateArray.length; i++) {
				dateString = dateArray[i];
				month = FormatUtils.getInstance().getMonth(dateString);
				if (month != null) {
					if (monthLastDayMap.containsKey(month)) {
						lastDayOfMonth = monthLastDayMap.get(month);
						day = FormatUtils.getInstance().getDay(dateString);
						if (day != null) {
							if (day <= lastDayOfMonth) {
								nonLeapYearDateString = IConstants.NON_LEAP_YEAR + IConstants.DASH + StringUtils.substring(dateString, 5, 10);
								dayOfYearNumber = LocalDate.parse(nonLeapYearDateString).getDayOfYear();
								if (dayOfYearSeasonalityMap.containsKey(dayOfYearNumber) == false) {
									//System.out.println("getYearlySeasonalityArray() nonLeapYearDateString=" + nonLeapYearDateString
									//		+ ",dayOfYearNumber=" + dayOfYearNumber + ",yearlySeasonality=" + yearlyArray[i]);
									dayOfYearSeasonalityMap.put(dayOfYearNumber, yearlyArray[i]);
								}

								if (dayOfYearSeasonalityMap.size() >= 365) {
									break nextDate;
								}
							}
						}
					}
				}
			}

			if (dayOfYearSeasonalityMap != null && dayOfYearSeasonalityMap.size() > 0) {
				yearlySeasonalityArray = new double[365];
				for (int i = 0; i < yearlySeasonalityArray.length; i++) {
					yearlySeasonalityArray[i] = 0d;
				}
				for (Integer testDayOfYear : dayOfYearSeasonalityMap.keySet()) {
					yearlySeasonality = dayOfYearSeasonalityMap.get(testDayOfYear);
					//System.out.println("getYearlySeasonalityArray() testDayOfYear=" + testDayOfYear + ",yearlySeasonality=" + yearlySeasonality);
					yearlySeasonalityArray[testDayOfYear - 1] = yearlySeasonality;
				}
			}
		}

		//if (yearlySeasonalityArray != null && yearlySeasonalityArray.length > 0) {
		//	for (int i = 0; i < yearlySeasonalityArray.length; i++) {
		//		System.out.println("getYearlySeasonalityArray() index=" + i + ",yearlySeasonality=" + yearlySeasonalityArray[i]);
		//	}
		//}

		return yearlySeasonalityArray;
	}

	private void testGetDay() throws Exception {
		String dateString = null;
		Integer day = null;

		// test 1
		dateString = "2021-01-17";
		System.out.println("testGetDay() dateString=" + dateString);
		day = FormatUtils.getInstance().getDay(dateString);
		assertNotNull("testGetDay() day should not be null.", day);
		System.out.println("testGetDay() day=" + day);
		assertEquals("testGetDay() day incorrect.", "17", String.valueOf(day));

		// test 2
		dateString = "2021-01-01";
		System.out.println("testGetDay() dateString=" + dateString);
		day = FormatUtils.getInstance().getDay(dateString);
		assertNotNull("testGetDay() day should not be null.", day);
		System.out.println("testGetDay() day=" + day);
		assertEquals("testGetDay() day incorrect.", "1", String.valueOf(day));

		// test 3
		dateString = "20210101";
		System.out.println("testGetDay() dateString=" + dateString);
		day = FormatUtils.getInstance().getDay(dateString);
		assertNull("testGetDay() day should be null.", day);

	}

	private void testGetMonth() throws Exception {
		String dateString = null;
		Integer month = null;

		// test 1
		dateString = "2021-01-17";
		System.out.println("testGetMonth() dateString=" + dateString);
		month = FormatUtils.getInstance().getMonth(dateString);
		assertNotNull("testGetMonth() month should not be null.", month);
		System.out.println("testGetMonth() month=" + month);
		assertEquals("testGetMonth() month incorrect.", "1", String.valueOf(month));

		// test 2
		dateString = "2021-12-17";
		System.out.println("testGetMonth() dateString=" + dateString);
		month = FormatUtils.getInstance().getMonth(dateString);
		assertNotNull("testGetMonth() month should not be null.", month);
		System.out.println("testGetMonth() month=" + month);
		assertEquals("testGetMonth() month incorrect.", "12", String.valueOf(month));

		// test 3
		dateString = "20211217";
		System.out.println("testGetMonth() dateString=" + dateString);
		month = FormatUtils.getInstance().getMonth(dateString);
		assertNull("testGetMonth() month should be null.", month);
	}

	private ProphetRequest getProphetRequest() {
		ProphetRequest prophetRequest = new ProphetRequest();
		prophetRequest.setForecast_days(365);
		prophetRequest.setDate_array(getStringArrayForProphet());
		prophetRequest.setValue_array(getDoubleArrayForProphet());
		return prophetRequest;
	}

	private void testExtractDoubleArray() throws Exception {
		double[] doubleArray = null;
		int fromPos = 0;
		int size = 0;

		// extract only the first occurrence
		doubleArray = FormatUtils.getInstance().extractDoubleArray(0, 0, getDoubleArrayForY());
		//fromPos = 0;
		//size = 1;
		//doubleArray = Arrays.copyOfRange(getDoubleArrayForY(), fromPos, fromPos + size);
		assertEquals("testExtractDoubleArray() doubleArray.length incorrect.", "1", String.valueOf(doubleArray.length));
		assertEquals("testExtractDoubleArray() doubleArray[0] incorrect.", "105.294986741563", String.valueOf(doubleArray[0]));

		// extract first and second occurrence
		doubleArray = FormatUtils.getInstance().extractDoubleArray(0, 1, getDoubleArrayForY());
		//fromPos = 0;
		//size = 2;
		//doubleArray = Arrays.copyOfRange(getDoubleArrayForY(), fromPos, fromPos + size);
		assertEquals("testExtractDoubleArray() doubleArray.length incorrect.", "2", String.valueOf(doubleArray.length));
		assertEquals("testExtractDoubleArray() doubleArray[0] incorrect.", "105.294986741563", String.valueOf(doubleArray[0]));
		assertEquals("testExtractDoubleArray() doubleArray[1] incorrect.", "105.894309669798", String.valueOf(doubleArray[1]));

		// extract only the last occurrence
		doubleArray = FormatUtils.getInstance().extractDoubleArray(getDoubleArrayForY().length - 1, getDoubleArrayForY().length - 1, getDoubleArrayForY());
		//fromPos = getDoubleArrayForY().length - 1;
		//size = 1;
		//doubleArray = Arrays.copyOfRange(getDoubleArrayForY(), fromPos, fromPos + size);
		assertEquals("testExtractDoubleArray() doubleArray.length incorrect.", "1", String.valueOf(doubleArray.length));
		assertEquals("testExtractDoubleArray() doubleArray[0] incorrect.", "114.326392437154", String.valueOf(doubleArray[0]));

		// extract last two occurrences
		doubleArray = FormatUtils.getInstance().extractDoubleArray(getDoubleArrayForY().length - 2, getDoubleArrayForY().length - 1, getDoubleArrayForY());
		//fromPos = getDoubleArrayForY().length - 2;
		//size = 2;
		//doubleArray = Arrays.copyOfRange(getDoubleArrayForY(), fromPos, fromPos + size);
		assertEquals("testExtractDoubleArray() doubleArray.length incorrect.", "2", String.valueOf(doubleArray.length));
		assertEquals("testExtractDoubleArray() doubleArray[0] incorrect.", "111.339439225794", String.valueOf(doubleArray[0]));
		assertEquals("testExtractDoubleArray() doubleArray[1] incorrect.", "114.326392437154", String.valueOf(doubleArray[1]));

		// extract second and third occurrence
		doubleArray = FormatUtils.getInstance().extractDoubleArray(1, 2, getDoubleArrayForY());
		//fromPos = 1;
		//size = 2;
		//doubleArray = Arrays.copyOfRange(getDoubleArrayForY(), fromPos, fromPos + size);
		assertEquals("testExtractDoubleArray() doubleArray.length incorrect.", "2", String.valueOf(doubleArray.length));
		assertEquals("testExtractDoubleArray() doubleArray[0] incorrect.", "105.894309669798", String.valueOf(doubleArray[0]));
		assertEquals("testExtractDoubleArray() doubleArray[1] incorrect.", "106.620875838828", String.valueOf(doubleArray[1]));

		System.out.println("testExtractDoubleArray() passed.");
	}

	private void testCorrelationCoefficient() throws Exception {

		long startTimestamp = System.currentTimeMillis();

		CausalImpactRequest causalImpactRequest = getTestCorrelationCoefficientCalculationRequest();
		String requestJson = new Gson().toJson(causalImpactRequest, CausalImpactRequest.class);
		System.out.println("testCorrelationCoefficient() requestJson=" + requestJson);

		CausalImpactResponse causalImpactResponse = null;
		double[] testDoubleArray = null;
		RCaller rCaller = null;
		RCode code = null;
		StringBuilder stringBuilder = null;
		String controlName = null;
		String responseJson = null;

		// output
		// map key = control time series name
		// map value = control time series correlation coefficient
		Map<String, Double> controlNameCorrelationCoefficientMap = new HashMap<String, Double>();

		try {
			rCaller = RCaller.create();
			code = RCode.create();
			code.addRCode("library(CausalImpact)");
			code.addDoubleArray("testTimeSeries", causalImpactRequest.getTest_time_series());

			for (int i = 0; i < causalImpactRequest.getControl_name_list().size(); i++) {
				controlName = causalImpactRequest.getControl_name_list().get(i);
				code.addDoubleArray(controlName, causalImpactRequest.getControl_time_series_list().get(i));
			}

			stringBuilder = new StringBuilder();
			stringBuilder.append("data <- cbind(");
			stringBuilder.append("testTimeSeries");
			for (int i = 0; i < causalImpactRequest.getControl_name_list().size(); i++) {
				controlName = causalImpactRequest.getControl_name_list().get(i);
				stringBuilder.append(IConstants.COMMA);
				stringBuilder.append(controlName);
			}
			stringBuilder.append(IConstants.CLOSING_PARENTHESIS);
			code.addRCode(stringBuilder.toString());

			code.addRCode("cor(data)");
			code.addRCode("result <- cor(data)");
			rCaller.setRCode(code);

			rCaller.runAndReturnResultOnline("result");
			//System.out.println(rCaller.getParser().getXMLFileAsString());
			//System.out.println("Names : " + rCaller.getParser().getNames());
			testDoubleArray = rCaller.getParser().getAsDoubleArray("result");
			for (int i = 0; i < testDoubleArray.length; i++) {
				if (i == 0) {
					continue;
				}
				//System.out.println("testCorrelationCoefficient() i=" + i + ",testDoubleArray[i]=" + testDoubleArray[i]);
				controlNameCorrelationCoefficientMap.put(causalImpactRequest.getControl_name_list().get(i - 1), testDoubleArray[i]);
				if ((i + 1) > causalImpactRequest.getControl_name_list().size()) {
					break;
				}
			}

			//			for (int i = 0; i < causalImpactRequest.getControl_name_list().size(); i++) {
			//				controlName = causalImpactRequest.getControl_name_list().get(i);
			//				System.out.println(
			//						"testCorrelationCoefficient() controlName=" + controlName + ",correlationCoefficient=" + controlNameCorrelationCoefficientMap.get(controlName));
			//				if (i == 0) {
			//					assertEquals("testCorrelationCoefficient() controlName incorrect.", "control1", controlName);
			//					assertEquals("testCorrelationCoefficient() correlationCoefficient incorrect.", "0.937228731078556",
			//							String.valueOf(controlNameCorrelationCoefficientMap.get(controlName)));
			//				} else if (i == 1) {
			//					assertEquals("testCorrelationCoefficient() controlName incorrect.", "control2", controlName);
			//					assertEquals("testCorrelationCoefficient() correlationCoefficient incorrect.", "0.871505340991362",
			//							String.valueOf(controlNameCorrelationCoefficientMap.get(controlName)));
			//				}
			//			}

			causalImpactResponse = new CausalImpactResponse();
			causalImpactResponse.setSuccess(true);
			causalImpactResponse.setControl_name_corr_coef_map(controlNameCorrelationCoefficientMap);
			responseJson = new Gson().toJson(causalImpactResponse, CausalImpactResponse.class);
			System.out.println("testCorrelationCoefficient() responseJson=" + responseJson);

			System.out.println("testCorrelationCoefficient() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		} catch (Exception e) {
			throw e;
		} finally {
			if (rCaller != null) {
				rCaller.stopRCallerOnline();
			}
		}

	}

	private void testCausalImpact() throws Exception {

		long startTimestamp = System.currentTimeMillis();

		CausalImpactRequest causalImpactRequest = getTestCausalImpactRequest();
		String requestJson = new Gson().toJson(causalImpactRequest, CausalImpactRequest.class);
		System.out.println("testCausalImpact() requestJson=" + requestJson);

		CausalImpactResponse causalImpactResponse = null;
		RCaller rCaller = null;
		RCode code = null;
		String[] testStringArray = null;
		double[] testDoubleArray = null;
		Long totalPrePeriodDays = null;
		Long totalPostPeriodDays = null;
		Long totalPreAndPostPeriodDays = null;
		StringBuilder stringBuilder = null;
		String responseJson = null;
		PosteriorInference posteriorInferenceAverage = null;
		PosteriorInference posteriorInferenceCumulative = null;
		String summary = null;
		int counterFactualPredictionDataPointsStartPosition = 0;
		int counterFactualPredictionDataPointsEndPosition = 0;
		double[] counterFactualPredictionDataPoints = null;
		int pointwiseCausalEffectDataPointsStartPosition = 0;
		int pointwiseCausalEffectDataPointsEndPosition = 0;
		double[] pointwiseCausalEffectDataPoints = null;
		int cumulativeEffectDataPointsStartPosition = 0;
		int cumulativeEffectDataPointsEndPosition = 0;
		double[] cumulativeEffectDataPoints = null;

		try {
			System.out.println("testCausalImpact() prePeriodStartDateString=" + causalImpactRequest.getPre_period_start_date());
			System.out.println("testCausalImpact() prePeriodEndDateString=" + causalImpactRequest.getPre_period_end_date());
			System.out.println("testCausalImpact() postPeriodStartDateString=" + causalImpactRequest.getPost_period_start_date());
			System.out.println("testCausalImpact() postPeriodEndDateString=" + causalImpactRequest.getPost_period_end_date());
			totalPreAndPostPeriodDays = ChronoUnit.DAYS.between(LocalDate.parse(causalImpactRequest.getPre_period_start_date()),
					LocalDate.parse(causalImpactRequest.getPost_period_end_date())) + IConstants.LONG_ONE;
			System.out.println("testCausalImpact() totalPreAndPostPeriodDays=" + totalPreAndPostPeriodDays);
			totalPrePeriodDays = ChronoUnit.DAYS.between(LocalDate.parse(causalImpactRequest.getPre_period_start_date()),
					LocalDate.parse(causalImpactRequest.getPre_period_end_date())) + IConstants.LONG_ONE;
			System.out.println("testCausalImpact() totalPrePeriodDays=" + totalPrePeriodDays);
			totalPostPeriodDays = ChronoUnit.DAYS.between(LocalDate.parse(causalImpactRequest.getPost_period_start_date()),
					LocalDate.parse(causalImpactRequest.getPost_period_end_date())) + IConstants.LONG_ONE;
			System.out.println("testCausalImpact() totalPostPeriodDays=" + totalPostPeriodDays);

			rCaller = RCaller.create();
			code = RCode.create();
			code.addRCode("library(CausalImpact)");
			code.addDoubleArray("y", causalImpactRequest.getTest_time_series());

			for (int i = 0; i < causalImpactRequest.getControl_time_series_list().size(); i++) {
				code.addDoubleArray("x" + (i + 1), causalImpactRequest.getControl_time_series_list().get(i));
			}
			code.addRCode("time.points <- seq.Date(as.Date(\"" + causalImpactRequest.getPre_period_start_date() + "\"), by = 1, length.out = "
					+ totalPreAndPostPeriodDays + ")");

			stringBuilder = new StringBuilder();
			stringBuilder.append("data <- zoo(cbind(");
			stringBuilder.append("y");
			for (int i = 0; i < causalImpactRequest.getControl_time_series_list().size(); i++) {
				stringBuilder.append(IConstants.COMMA);
				stringBuilder.append("x" + (i + 1));
			}
			stringBuilder.append("), time.points)");
			code.addRCode(stringBuilder.toString());

			code.addRCode(
					"pre.period <- as.Date(c(\"" + causalImpactRequest.getPre_period_start_date() + "\", \"" + causalImpactRequest.getPre_period_end_date() + "\"))");
			code.addRCode("post.period <- as.Date(c(\"" + causalImpactRequest.getPost_period_start_date() + "\", \"" + causalImpactRequest.getPost_period_end_date()
					+ "\"))");
			code.addRCode("impact <- CausalImpact(data, pre.period, post.period)");

			//			File file = code.startPlot();
			//			System.out.println("Plot will be saved to : " + file);
			//			code.addRCode("plot(impact)");
			//			code.endPlot();

			rCaller.setRCode(code);

			// detailed summary
			rCaller.runAndReturnResultOnline("impact$summary");
			//System.out.println("Names : " + rCaller.getParser().getNames());
			//System.out.println(rCaller.getParser().getXMLFileAsString());

			posteriorInferenceAverage = new PosteriorInference();
			posteriorInferenceCumulative = new PosteriorInference();
			for (String name : rCaller.getParser().getNames()) {
				testDoubleArray = rCaller.getParser().getAsDoubleArray(name);
				System.out.println("testCausalImpact() Average " + name + "=" + testDoubleArray[0]);
				System.out.println("testCausalImpact() Cumulative " + name + "=" + testDoubleArray[1]);
				if (StringUtils.equalsIgnoreCase(name, IConstants.ACTUAL)) {
					posteriorInferenceAverage.setActual(testDoubleArray[0]);
					posteriorInferenceCumulative.setActual(testDoubleArray[1]);
				} else if (StringUtils.equalsIgnoreCase(name, IConstants.PRED)) {
					posteriorInferenceAverage.setPrediction(testDoubleArray[0]);
					posteriorInferenceCumulative.setPrediction(testDoubleArray[1]);
				} else if (StringUtils.equalsIgnoreCase(name, IConstants.PRED_LOWER)) {
					posteriorInferenceAverage.setPrediction_lower(testDoubleArray[0]);
					posteriorInferenceCumulative.setPrediction_lower(testDoubleArray[1]);
				} else if (StringUtils.equalsIgnoreCase(name, IConstants.PRED_UPPER)) {
					posteriorInferenceAverage.setPrediction_upper(testDoubleArray[0]);
					posteriorInferenceCumulative.setPrediction_upper(testDoubleArray[1]);
				} else if (StringUtils.equalsIgnoreCase(name, IConstants.PRED_SD)) {
					posteriorInferenceAverage.setPrediction_standard_derivation(testDoubleArray[0]);
					posteriorInferenceCumulative.setPrediction_standard_derivation(testDoubleArray[1]);
				} else if (StringUtils.equalsIgnoreCase(name, IConstants.ABS_EFFECT)) {
					posteriorInferenceAverage.setAbsolute_effect(testDoubleArray[0]);
					posteriorInferenceCumulative.setAbsolute_effect(testDoubleArray[1]);
				} else if (StringUtils.equalsIgnoreCase(name, IConstants.ABS_EFFECT_LOWER)) {
					posteriorInferenceAverage.setAbsolute_effect_lower(testDoubleArray[0]);
					posteriorInferenceCumulative.setAbsolute_effect_lower(testDoubleArray[1]);
				} else if (StringUtils.equalsIgnoreCase(name, IConstants.ABS_EFFECT_UPPER)) {
					posteriorInferenceAverage.setAbsolute_effect_upper(testDoubleArray[0]);
					posteriorInferenceCumulative.setAbsolute_effect_upper(testDoubleArray[1]);
				} else if (StringUtils.equalsIgnoreCase(name, IConstants.ABS_EFFECT_SD)) {
					posteriorInferenceAverage.setAbsolute_effect_standard_derivation(testDoubleArray[0]);
					posteriorInferenceCumulative.setAbsolute_effect_standard_derivation(testDoubleArray[1]);
				} else if (StringUtils.equalsIgnoreCase(name, IConstants.REL_EFFECT)) {
					posteriorInferenceAverage.setRelative_effect(testDoubleArray[0]);
					posteriorInferenceCumulative.setRelative_effect(testDoubleArray[1]);
				} else if (StringUtils.equalsIgnoreCase(name, IConstants.REL_EFFECT_LOWER)) {
					posteriorInferenceAverage.setRelative_effect_lower(testDoubleArray[0]);
					posteriorInferenceCumulative.setRelative_effect_lower(testDoubleArray[1]);
				} else if (StringUtils.equalsIgnoreCase(name, IConstants.REL_EFFECT_UPPER)) {
					posteriorInferenceAverage.setRelative_effect_upper(testDoubleArray[0]);
					posteriorInferenceCumulative.setRelative_effect_upper(testDoubleArray[1]);
				} else if (StringUtils.equalsIgnoreCase(name, IConstants.REL_EFFECT_SD)) {
					posteriorInferenceAverage.setRelative_effect_standard_derivation(testDoubleArray[0]);
					posteriorInferenceCumulative.setRelative_effect_standard_derivation(testDoubleArray[1]);
				} else if (StringUtils.equalsIgnoreCase(name, IConstants.ALPHA)) {
					posteriorInferenceAverage.setAlpha(testDoubleArray[0]);
					posteriorInferenceCumulative.setAlpha(testDoubleArray[1]);
				} else if (StringUtils.equalsIgnoreCase(name, IConstants.P)) {
					posteriorInferenceAverage.setTail_area_probability(testDoubleArray[0]);
					posteriorInferenceCumulative.setTail_area_probability(testDoubleArray[1]);
				}
			}

			// summary report
			rCaller.runAndReturnResultOnline("impact");
			//System.out.println("Names : " + rCaller.getParser().getNames());
			//System.out.println(rCaller.getParser().getXMLFileAsString());
			testStringArray = rCaller.getParser().getAsStringArray("report");
			if (testStringArray != null && testStringArray.length > 0) {
				stringBuilder = new StringBuilder();
				System.out.println("testCausalImpact() report:");
				for (String testString : testStringArray) {
					System.out.println(testString);
					stringBuilder.append(testString);
				}
				summary = stringBuilder.toString();
			}

			testDoubleArray = rCaller.getParser().getAsDoubleArray("series");
			System.out.println("testCausalImpact() series:");
			for (double testDouble : testDoubleArray) {
				System.out.println(testDouble);
			}

			counterFactualPredictionDataPointsStartPosition = IConstants.COUNTER_FACTUAL_PREDICTION_DATA_POINTS_POSITION * totalPreAndPostPeriodDays.intValue();
			counterFactualPredictionDataPointsEndPosition = counterFactualPredictionDataPointsStartPosition + totalPreAndPostPeriodDays.intValue() - 1;
			System.out.println("testCausalImpact() counterFactualPredictionDataPointsStartPosition=" + counterFactualPredictionDataPointsStartPosition);
			System.out.println("testCausalImpact() counterFactualPredictionDataPointsEndPosition=" + counterFactualPredictionDataPointsEndPosition);
			counterFactualPredictionDataPoints = FormatUtils.getInstance().extractDoubleArray(counterFactualPredictionDataPointsStartPosition,
					counterFactualPredictionDataPointsEndPosition, testDoubleArray);

			pointwiseCausalEffectDataPointsStartPosition = IConstants.POINTWISE_CAUSAL_EFFECT_DATA_POINTS_POSITION * totalPreAndPostPeriodDays.intValue();
			pointwiseCausalEffectDataPointsEndPosition = pointwiseCausalEffectDataPointsStartPosition + totalPreAndPostPeriodDays.intValue() - 1;
			System.out.println("testCausalImpact() pointwiseCausalEffectDataPointsStartPosition=" + pointwiseCausalEffectDataPointsStartPosition);
			System.out.println("testCausalImpact() pointwiseCausalEffectDataPointsEndPosition=" + pointwiseCausalEffectDataPointsEndPosition);
			pointwiseCausalEffectDataPoints = FormatUtils.getInstance().extractDoubleArray(pointwiseCausalEffectDataPointsStartPosition,
					pointwiseCausalEffectDataPointsEndPosition, testDoubleArray);

			cumulativeEffectDataPointsStartPosition = IConstants.CUMULATIVE_EFFECT_DATA_POINTS_POSITION * totalPreAndPostPeriodDays.intValue();
			cumulativeEffectDataPointsEndPosition = cumulativeEffectDataPointsStartPosition + totalPreAndPostPeriodDays.intValue() - 1;
			System.out.println("testCausalImpact() cumulativeEffectDataPointsStartPosition=" + cumulativeEffectDataPointsStartPosition);
			System.out.println("testCausalImpact() cumulativeEffectDataPointsEndPosition=" + cumulativeEffectDataPointsEndPosition);
			cumulativeEffectDataPoints = FormatUtils.getInstance().extractDoubleArray(cumulativeEffectDataPointsStartPosition, cumulativeEffectDataPointsEndPosition,
					testDoubleArray);

			//			code.showPlot(file);

			causalImpactResponse = new CausalImpactResponse();
			causalImpactResponse.setSuccess(true);
			causalImpactResponse.setPre_period_start_date(causalImpactRequest.getPre_period_start_date());
			causalImpactResponse.setPre_period_end_date(causalImpactRequest.getPre_period_end_date());
			causalImpactResponse.setPost_period_start_date(causalImpactRequest.getPost_period_start_date());
			causalImpactResponse.setPost_period_end_date(causalImpactRequest.getPost_period_end_date());
			causalImpactResponse.setSummary(summary);
			causalImpactResponse.setPosterior_inference_average(posteriorInferenceAverage);
			causalImpactResponse.setPosterior_inference_cumulative(posteriorInferenceCumulative);
			causalImpactResponse.setCounter_factual_prediction_time_series(counterFactualPredictionDataPoints);
			causalImpactResponse.setPointwise_causal_effect_time_series(pointwiseCausalEffectDataPoints);
			causalImpactResponse.setCumulative_effect_time_series(cumulativeEffectDataPoints);
			responseJson = new Gson().toJson(causalImpactResponse, CausalImpactResponse.class);
			System.out.println("testCausalImpact() responseJson=" + responseJson);
		} catch (Exception e) {
			throw e;
		} finally {
			if (rCaller != null) {
				rCaller.stopRCallerOnline();
			}
		}

		System.out.println("testCausalImpact() passed. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));

	}

	private CausalImpactRequest getTestCorrelationCoefficientCalculationRequest() {
		CausalImpactRequest causalImpactRequest = new CausalImpactRequest();

		//causalImpactRequest.setTest_time_series(FormatUtils.getInstance().extractDoubleArray(0, 69, getDoubleArrayForY()));
		causalImpactRequest.setTest_time_series(FormatUtils.getInstance().extractDoubleArray(62, 69, getDoubleArrayForY()));

		List<String> controlNameList = new ArrayList<String>();
		controlNameList.add("control1");
		controlNameList.add("control2");
		causalImpactRequest.setControl_name_list(controlNameList);

		List<double[]> controlDataPointsList = new ArrayList<double[]>();
		//controlDataPointsList.add(FormatUtils.getInstance().extractDoubleArray(0, 69, getDoubleArrayForX1()));
		//controlDataPointsList.add(FormatUtils.getInstance().extractDoubleArray(0, 69, getDoubleArrayForX2()));
		controlDataPointsList.add(FormatUtils.getInstance().extractDoubleArray(62, 69, getDoubleArrayForX1()));
		controlDataPointsList.add(FormatUtils.getInstance().extractDoubleArray(62, 69, getDoubleArrayForX2()));
		causalImpactRequest.setControl_time_series_list(controlDataPointsList);

		return causalImpactRequest;
	}

	private CausalImpactRequest getTestCausalImpactRequest() {
		CausalImpactRequest causalImpactRequest = new CausalImpactRequest();

		causalImpactRequest.setPre_period_start_date("2021-06-01");
		causalImpactRequest.setPre_period_end_date("2021-08-09");
		causalImpactRequest.setPost_period_start_date("2021-08-10");
		causalImpactRequest.setPost_period_end_date("2021-09-08");

		causalImpactRequest.setTest_time_series(getDoubleArrayForY());

		List<String> controlNameList = new ArrayList<String>();
		controlNameList.add("control1");
		controlNameList.add("control2");
		causalImpactRequest.setControl_name_list(controlNameList);

		List<double[]> controlDataPointsList = new ArrayList<double[]>();
		controlDataPointsList.add(getDoubleArrayForX1());
		controlDataPointsList.add(getDoubleArrayForX2());
		causalImpactRequest.setControl_time_series_list(controlDataPointsList);

		return causalImpactRequest;
	}

	private double[] getDoubleArrayForY() {
		return new double[] { 105.294986741563, 105.894309669798, 106.620875838828, 106.157206715611, 101.281175367611, 101.448430404394, 100.973531295986,
				102.544706623256, 102.196095062485, 106.813823912820, 103.097143547068, 101.582386666749, 101.673556412411, 101.841354437786, 102.237455737321,
				100.784815884160, 100.799475990316, 101.469293747955, 103.323753845151, 102.501752563532, 105.202970606505, 109.529621758391, 106.281388964034,
				107.390781247210, 106.034848798153, 106.484644636029, 104.828440129525, 102.986789724487, 104.130447420883, 107.589625606610, 104.126018233013,
				106.157168423783, 105.719404485374, 101.833157375086, 102.205645807644, 101.952496215371, 100.201140097063, 99.968448170460, 99.885528892698,
				100.180869994240, 100.268671427130, 99.638394336843, 103.404272138826, 104.747438903684, 103.498399511826, 100.196157136733, 98.088133412256,
				100.261950572215, 100.180117923862, 103.356375745830, 101.015791636131, 102.518960350355, 100.687986526053, 102.553632658390, 104.047084239932,
				103.378053360498, 107.071924590776, 104.469947322470, 106.301462055551, 109.496829525290, 108.485277812686, 111.014291469492, 112.867343694573,
				111.538727869305, 112.284408699329, 109.686179697106, 109.521634601159, 106.002957468914, 106.543084370612, 108.555071439643, 117.294312743074,
				116.495691770188, 118.253265267922, 117.975995109054, 120.639716329814, 117.622604506845, 116.215262174588, 117.143538695230, 116.553033903326,
				115.102037228661, 117.017486067743, 117.978480593359, 118.860957062418, 117.747853794938, 118.690190569880, 117.019148380786, 118.602027351201,
				118.129658809177, 121.840240231535, 117.284462260685, 116.016020338404, 118.965519657894, 116.731018002460, 118.203885420013, 115.214840888210,
				114.942928099231, 116.669401819417, 112.580096233226, 111.339439225794, 114.326392437154, };
	}

	private double[] getDoubleArrayForX1() {
		return new double[] { 105.836141287184, 106.148510516934, 105.445452924548, 104.174624934334, 101.675598883830, 101.656041522905, 101.372338529136,
				102.008552534637, 101.377638838788, 103.255779683329, 103.910403745630, 104.135368839852, 102.943195123412, 102.536488797069, 100.958200053214,
				100.353419093033, 100.689176347747, 102.231157386632, 102.048638166882, 103.739780612894, 105.659565709992, 106.844259292871, 106.915370650487,
				108.074732360516, 106.164712254082, 106.340999773123, 105.571828893883, 103.916211782304, 104.895122501999, 105.204349326708, 104.419813122256,
				104.413818240674, 103.435890330798, 101.278563777492, 102.151483900528, 101.763057302727, 100.162249070190, 99.269532787228, 99.995174846545,
				99.688699369268, 101.554391330291, 101.391699619787, 103.155366586517, 103.647997154701, 102.804078261380, 100.948799447533, 100.363500936749,
				101.747782931054, 102.008131140481, 101.783745089183, 100.507386914872, 102.651660431658, 101.857792871126, 102.898020774525, 103.847919878205,
				104.265810052247, 105.840626285985, 105.114701077161, 106.748061127334, 108.184444971677, 108.533147494931, 112.010924653116, 112.135961643253,
				111.790476570709, 112.119071373401, 110.389096157736, 109.012492053947, 107.959737625246, 108.131449479650, 106.942126862978, 106.418696537089,
				106.409460393465, 107.727921968003, 108.022532200572, 108.857643844320, 107.437434828973, 106.255361448164, 105.812997659729, 105.305540406008,
				105.039422413256, 106.121031760117, 107.868870765596, 108.205670254235, 108.168855253221, 108.141218947449, 107.384712422050, 108.487507045951,
				108.309874349626, 109.237501913860, 106.924731961598, 106.204478423473, 107.520928445624, 106.577055429915, 107.721529353336, 105.711658563560,
				104.790707088902, 104.221192239512, 102.463902225578, 102.721718277757, 102.085333680785, };
	}

	private double[] getDoubleArrayForX2() {
		return new double[] { 105.836141287184, 104.148510516934, 107.445452924548, 106.174624934334, 102.675598883830, 101.656041522905, 101.372338529136,
				101.008552534637, 99.377638838788, 104.255779683329, 105.910403745630, 106.135368839852, 100.943195123412, 104.536488797069, 99.958200053214,
				101.353419093033, 98.689176347747, 100.231157386632, 102.048638166882, 101.739780612894, 103.659565709992, 105.844259292871, 104.915370650487,
				108.074732360516, 105.164712254082, 107.340999773123, 106.571828893883, 101.916211782304, 102.895122501999, 103.204349326708, 104.419813122256,
				106.413818240674, 105.435890330798, 102.278563777492, 101.151483900528, 101.763057302727, 101.162249070190, 100.269532787228, 99.995174846545,
				99.688699369268, 100.554391330291, 101.391699619787, 104.155366586517, 102.647997154701, 103.804078261380, 100.948799447533, 98.363500936749,
				100.747782931054, 101.008131140481, 101.783745089183, 100.507386914872, 102.651660431658, 103.857792871126, 100.898020774525, 105.847919878205,
				106.265810052247, 107.840626285985, 107.114701077161, 104.748061127334, 108.184444971677, 109.533147494931, 110.010924653116, 112.135961643253,
				109.790476570709, 114.119071373401, 112.389096157736, 111.012492053947, 109.959737625246, 106.131449479650, 104.942126862978, 104.418696537089,
				108.409460393465, 108.727921968003, 109.022532200572, 110.857643844320, 105.437434828973, 107.255361448164, 105.812997659729, 107.305540406008,
				106.039422413256, 108.121031760117, 107.868870765596, 110.205670254235, 109.168855253221, 107.141218947449, 109.384712422050, 110.487507045951,
				107.309874349626, 110.237501913860, 105.924731961598, 108.204478423473, 107.520928445624, 104.577055429915, 108.721529353336, 106.711658563560,
				104.790707088902, 103.221192239512, 101.463902225578, 100.721718277757, 101.085333680785, };
	}

	private String[] getStringArrayForProphet() {
		return new String[] { "2007-12-10", "2007-12-11", "2007-12-12", "2007-12-13", "2007-12-14", "2007-12-15", "2007-12-16", "2007-12-17", "2007-12-18",
				"2007-12-19", "2007-12-20", "2007-12-21", "2007-12-22", "2007-12-23", "2007-12-24", "2007-12-25", "2007-12-26", "2007-12-27", "2007-12-28",
				"2007-12-29", "2007-12-30", "2007-12-31", "2008-01-01", "2008-01-02", "2008-01-03", "2008-01-04", "2008-01-05", "2008-01-06", "2008-01-07",
				"2008-01-08", "2008-01-09", "2008-01-10", "2008-01-11", "2008-01-12", "2008-01-13", "2008-01-14", "2008-01-15", "2008-01-16", "2008-01-17",
				"2008-01-18", "2008-01-19", "2008-01-20", "2008-01-21", "2008-01-22", "2008-01-23", "2008-01-24", "2008-01-25", "2008-01-26", "2008-01-27",
				"2008-01-28", "2008-01-29", "2008-01-30", "2008-02-01", "2008-02-02", "2008-02-03", "2008-02-04", "2008-02-05", "2008-02-06", "2008-02-07",
				"2008-02-08", "2008-02-09", "2008-02-10", "2008-02-11", "2008-02-12", "2008-02-13", "2008-02-14", "2008-02-15", "2008-02-16", "2008-02-17",
				"2008-02-18", "2008-02-19", "2008-02-20", "2008-02-21", "2008-02-22", "2008-02-23", "2008-02-24", "2008-02-25", "2008-02-26", "2008-02-27",
				"2008-02-29", "2008-03-02", "2008-03-05", "2008-03-06", "2008-03-07", "2008-03-08", "2008-03-09", "2008-03-10", "2008-03-11", "2008-03-12",
				"2008-03-13", "2008-03-14", "2008-03-15", "2008-03-16", "2008-03-17", "2008-03-18", "2008-03-19", "2008-03-20", "2008-03-21", "2008-03-22",
				"2008-03-23", "2008-03-24", "2008-03-25", "2008-03-26", "2008-03-27", "2008-03-28", "2008-03-29", "2008-03-30", "2008-03-31", "2008-04-01",
				"2008-04-02", "2008-04-03", "2008-04-04", "2008-04-05", "2008-04-06", "2008-04-07", "2008-04-08", "2008-04-09", "2008-04-10", "2008-04-11",
				"2008-04-12", "2008-04-13", "2008-04-14", "2008-04-15", "2008-04-16", "2008-04-17", "2008-04-18", "2008-04-19", "2008-04-20", "2008-04-21",
				"2008-04-22", "2008-04-23", "2008-04-24", "2008-04-25", "2008-04-26", "2008-04-27", "2008-04-28", "2008-04-29", "2008-04-30", "2008-05-01",
				"2008-05-02", "2008-05-03", "2008-05-04", "2008-05-05", "2008-05-06", "2008-05-07", "2008-05-08", "2008-05-09", "2008-05-10", "2008-05-11",
				"2008-05-12", "2008-05-13", "2008-05-14", "2008-05-15", "2008-05-16", "2008-05-17", "2008-05-18", "2008-05-19", "2008-05-20", "2008-05-21",
				"2008-05-22", "2008-05-23", "2008-05-24", "2008-05-25", "2008-05-26", "2008-05-27", "2008-05-28", "2008-05-29", "2008-05-30", "2008-05-31",
				"2008-06-03", "2008-06-04", "2008-06-05", "2008-06-06", "2008-06-07", "2008-06-08", "2008-06-09", "2008-06-10", "2008-06-11", "2008-06-12",
				"2008-06-13", "2008-06-14", "2008-06-15", "2008-06-16", "2008-06-17", "2008-06-18", "2008-06-19", "2008-06-20", "2008-06-21", "2008-06-22",
				"2008-06-23", "2008-06-24", "2008-06-25", "2008-06-26", "2008-06-27", "2008-06-28", "2008-06-29", "2008-06-30", "2008-07-02", "2008-07-03",
				"2008-07-04", "2008-07-05", "2008-07-06", "2008-07-07", "2008-07-08", "2008-07-09", "2008-07-10", "2008-07-11", "2008-07-12", "2008-08-01",
				"2008-08-02", "2008-08-03", "2008-08-04", "2008-08-05", "2008-08-06", "2008-08-07", "2008-08-08", "2008-08-09", "2008-08-10", "2008-08-11",
				"2008-08-12", "2008-08-13", "2008-08-14", "2008-08-15", "2008-08-16", "2008-08-17", "2008-08-18", "2008-08-19", "2008-08-20", "2008-08-21",
				"2008-08-22", "2008-08-23", "2008-08-24", "2008-08-25", "2008-08-26", "2008-08-27", "2008-08-28", "2008-08-29", "2008-08-30", "2008-08-31",
				"2008-09-01", "2008-09-02", "2008-09-03", "2008-09-04", "2008-09-05", "2008-09-06", "2008-09-07", "2008-09-08", "2008-09-09", "2008-09-10",
				"2008-09-11", "2008-09-12", "2008-09-13", "2008-09-14", "2008-09-15", "2008-09-16", "2008-09-17", "2008-09-18", "2008-09-19", "2008-09-20",
				"2008-09-21", "2008-09-22", "2008-09-23", "2008-09-24", "2008-09-25", "2008-09-26", "2008-09-27", "2008-09-28", "2008-09-29", "2008-09-30",
				"2008-10-01", "2008-10-02", "2008-10-03", "2008-10-04", "2008-10-05", "2008-10-06", "2008-10-07", "2008-10-08", "2008-10-09", "2008-10-10",
				"2008-10-11", "2008-10-12", "2008-10-13", "2008-10-14", "2008-10-15", "2008-10-16", "2008-10-17", "2008-10-18", "2008-10-19", "2008-10-20",
				"2008-10-23", "2008-10-24", "2008-10-25", "2008-10-26", "2008-10-27", "2008-10-28", "2008-10-29", "2008-10-30", "2008-10-31", "2008-11-01",
				"2008-11-02", "2008-11-03", "2008-11-04", "2008-11-05", "2008-11-06", "2008-11-07", "2008-11-08", "2008-11-09", "2008-11-10", "2008-11-11",
				"2008-11-12", "2008-11-13", "2008-11-14", "2008-11-15", "2008-11-16", "2008-11-17", "2008-11-18", "2008-11-19", "2008-11-20", "2008-11-21",
				"2008-11-22", "2008-11-23", "2008-11-24", "2008-11-25", "2008-11-26", "2008-11-27", "2008-11-28", "2008-11-29", "2008-11-30", "2008-12-01",
				"2008-12-02", "2008-12-03", "2008-12-04", "2008-12-05", "2008-12-06", "2008-12-07", "2008-12-08", "2008-12-09", "2008-12-10", "2008-12-11",
				"2008-12-12", "2008-12-13", "2008-12-14", "2008-12-15", "2008-12-16", "2008-12-17", "2008-12-18", "2008-12-19", "2008-12-20", "2008-12-21",
				"2008-12-22", "2008-12-23", "2008-12-24", "2008-12-25", "2008-12-26", "2008-12-27", "2008-12-28", "2008-12-29", "2008-12-30", "2008-12-31",
				"2009-01-01", "2009-01-02", "2009-01-03", "2009-01-04", "2009-01-05", "2009-01-06", "2009-01-07", "2009-01-08", "2009-01-09", "2009-01-10",
				"2009-01-11", "2009-01-12", "2009-01-13", "2009-01-14", "2009-01-15", "2009-01-16", "2009-01-17", "2009-01-18", "2009-01-19", "2009-01-20",
				"2009-01-21", "2009-01-22", "2009-01-23", "2009-01-24", "2009-01-25", "2009-01-26", "2009-01-27", "2009-01-28", "2009-01-29", "2009-01-30",
				"2009-01-31", "2009-02-01", "2009-02-02", "2009-02-03", "2009-02-04", "2009-02-05", "2009-02-06", "2009-02-07", "2009-02-08", "2009-02-09",
				"2009-02-10", "2009-02-11", "2009-02-12", "2009-02-13", "2009-02-14", "2009-02-15", "2009-02-16", "2009-02-17", "2009-02-18", "2009-02-19",
				"2009-02-20", "2009-02-21", "2009-02-22", "2009-02-23", "2009-02-24", "2009-02-25", "2009-02-26", "2009-02-27", "2009-02-28", "2009-03-01",
				"2009-03-02", "2009-03-03", "2009-03-04", "2009-03-05", "2009-03-06", "2009-03-07", "2009-03-08", "2009-03-09", "2009-03-10", "2009-03-11",
				"2009-03-12", "2009-03-13", "2009-03-14", "2009-03-15", "2009-03-16", "2009-03-17", "2009-03-18", "2009-03-19", "2009-03-20", "2009-03-21",
				"2009-03-22", "2009-03-23", "2009-03-24", "2009-03-25", "2009-03-26", "2009-03-27", "2009-03-28", "2009-03-29", "2009-03-30", "2009-03-31",
				"2009-04-01", "2009-04-02", "2009-04-03", "2009-04-04", "2009-04-05", "2009-04-06", "2009-04-07", "2009-04-08", "2009-04-09", "2009-04-10",
				"2009-04-11", "2009-04-12", "2009-04-13", "2009-04-14", "2009-04-15", "2009-04-16", "2009-04-17", "2009-04-18", "2009-04-19", "2009-04-20",
				"2009-04-21", "2009-04-22", "2009-04-23", "2009-04-24", "2009-04-25", "2009-04-26", "2009-04-27", "2009-04-28", "2009-04-29", "2009-04-30",
				"2009-05-01", "2009-05-02", "2009-05-03", "2009-05-04", "2009-05-05", "2009-05-06", "2009-05-07", "2009-05-08", "2009-05-09", "2009-05-10",
				"2009-05-11", "2009-05-12", "2009-05-13", "2009-05-14", "2009-05-15", "2009-05-16", "2009-05-17", "2009-05-18", "2009-05-19", "2009-05-20",
				"2009-05-21", "2009-05-22", "2009-05-23", "2009-05-24", "2009-05-25", "2009-05-26", "2009-05-27", "2009-05-28", "2009-05-29", "2009-05-30",
				"2009-05-31", "2009-06-01", "2009-06-02", "2009-06-03", "2009-06-04", "2009-06-05", "2009-06-06", "2009-06-07", "2009-06-08", "2009-06-09",
				"2009-06-10", "2009-06-11", "2009-06-12", "2009-06-13", "2009-06-14", "2009-06-15", "2009-06-16", "2009-06-17", "2009-06-18", "2009-06-19",
				"2009-06-20", "2009-06-21", "2009-06-22", "2009-06-23", "2009-06-24", "2009-06-25", "2009-06-26", "2009-06-27", "2009-06-28", "2009-06-29",
				"2009-06-30", "2009-07-01", "2009-07-02", "2009-07-03", "2009-07-04", "2009-07-05", "2009-07-06", "2009-07-07", "2009-07-08", "2009-07-09",
				"2009-07-10", "2009-07-11", "2009-07-12", "2009-07-13", "2009-07-14", "2009-07-15", "2009-07-16", "2009-07-17", "2009-07-18", "2009-07-19",
				"2009-07-20", "2009-07-21", "2009-07-22", "2009-07-23", "2009-07-24", "2009-07-25", "2009-07-26", "2009-07-27", "2009-07-28", "2009-07-29",
				"2009-07-30", "2009-07-31", "2009-08-01", "2009-08-02", "2009-08-03", "2009-08-04", "2009-08-05", "2009-08-06", "2009-08-07", "2009-08-08",
				"2009-08-09", "2009-08-10", "2009-08-11", "2009-08-12", "2009-08-13", "2009-08-14", "2009-08-15", "2009-08-16", "2009-08-17", "2009-08-18",
				"2009-08-19", "2009-08-20", "2009-08-21", "2009-08-22", "2009-08-23", "2009-08-24", "2009-08-25", "2009-08-26", "2009-08-27", "2009-08-28",
				"2009-08-29", "2009-08-30", "2009-08-31", "2009-09-01", "2009-09-02", "2009-09-03", "2009-09-04", "2009-09-05", "2009-09-06", "2009-09-07",
				"2009-09-08", "2009-09-09", "2009-09-10", "2009-09-11", "2009-09-12", "2009-09-13", "2009-09-14", "2009-09-15", "2009-09-16", "2009-09-17",
				"2009-09-18", "2009-09-19", "2009-09-20", "2009-09-21", "2009-09-22", "2009-09-24", "2009-09-28", "2009-09-29", "2009-09-30", "2009-10-01",
				"2009-10-02", "2009-10-03", "2009-10-04", "2009-10-05", "2009-10-06", "2009-10-07", "2009-10-08", "2009-10-09", "2009-10-10", "2009-10-11",
				"2009-10-12", "2009-10-13", "2009-10-17", "2009-10-18", "2009-10-19", "2009-10-20", "2009-10-21", "2009-10-22", "2009-10-23", "2009-10-24",
				"2009-10-25", "2009-10-26", "2009-10-27", "2009-10-28", "2009-10-29", "2009-10-30", "2009-10-31", "2009-11-01", "2009-11-02", "2009-11-03",
				"2009-11-04", "2009-11-05", "2009-11-06", "2009-11-07", "2009-11-08", "2009-11-09", "2009-11-10", "2009-11-11", "2009-11-12", "2009-11-13",
				"2009-11-14", "2009-11-16", "2009-11-17", "2009-11-18", "2009-11-19", "2009-11-20", "2009-11-21", "2009-11-23", "2009-11-24", "2009-11-25",
				"2009-11-26", "2009-11-27", "2009-11-28", "2009-11-29", "2009-11-30", "2009-12-01", "2009-12-02", "2009-12-03", "2009-12-04", "2009-12-05",
				"2009-12-06", "2009-12-07", "2009-12-08", "2009-12-09", "2009-12-10", "2009-12-11", "2009-12-12", "2009-12-13", "2009-12-14", "2009-12-15",
				"2009-12-16", "2009-12-17", "2009-12-18", "2009-12-19", "2009-12-20", "2009-12-21", "2009-12-22", "2009-12-23", "2009-12-24", "2009-12-25",
				"2009-12-26", "2009-12-27", "2009-12-28", "2009-12-29", "2009-12-30", "2009-12-31", "2010-01-01", "2010-01-02", "2010-01-03", "2010-01-04",
				"2010-01-05", "2010-01-06", "2010-01-07", "2010-01-08", "2010-01-09", "2010-01-10", "2010-01-11", "2010-01-12", "2010-01-13", "2010-01-14",
				"2010-01-15", "2010-01-16", "2010-01-17", "2010-01-18", "2010-01-19", "2010-01-20", "2010-01-21", "2010-01-22", "2010-01-25", "2010-01-26",
				"2010-01-27", "2010-01-28", "2010-01-29", "2010-01-30", "2010-01-31", "2010-02-01", "2010-02-02", "2010-02-03", "2010-02-04", "2010-02-05",
				"2010-02-06", "2010-02-07", "2010-02-09", "2010-02-10", "2010-02-11", "2010-02-12", "2010-02-13", "2010-02-14", "2010-02-15", "2010-02-16",
				"2010-02-17", "2010-02-18", "2010-02-19", "2010-02-20", "2010-02-21", "2010-02-22", "2010-02-24", "2010-02-25", "2010-02-26", "2010-02-27",
				"2010-02-28", "2010-03-01", "2010-03-02", "2010-03-03", "2010-03-04", "2010-03-05", "2010-03-06", "2010-03-07", "2010-03-08", "2010-03-09",
				"2010-03-10", "2010-03-11", "2010-03-12", "2010-03-13", "2010-03-14", "2010-03-15", "2010-03-16", "2010-03-17", "2010-03-18", "2010-03-19",
				"2010-03-20", "2010-03-21", "2010-03-22", "2010-03-23", "2010-03-24", "2010-03-25", "2010-03-26", "2010-03-27", "2010-03-28", "2010-03-29",
				"2010-03-30", "2010-03-31", "2010-04-01", "2010-04-02", "2010-04-03", "2010-04-04", "2010-04-05", "2010-04-06", "2010-04-07", "2010-04-08",
				"2010-04-09", "2010-04-10", "2010-04-11", "2010-04-12", "2010-04-13", "2010-04-14", "2010-04-15", "2010-04-16", "2010-04-17", "2010-04-18",
				"2010-04-19", "2010-04-20", "2010-04-21", "2010-04-22", "2010-04-23", "2010-04-24", "2010-04-25", "2010-04-26", "2010-04-27", "2010-04-28",
				"2010-04-29", "2010-04-30", "2010-05-01", "2010-05-02", "2010-05-03", "2010-05-04", "2010-05-05", "2010-05-06", "2010-05-07", "2010-05-08",
				"2010-05-09", "2010-05-10", "2010-05-11", "2010-05-12", "2010-05-13", "2010-05-14", "2010-05-15", "2010-05-16", "2010-05-17", "2010-05-18",
				"2010-05-19", "2010-05-20", "2010-05-21", "2010-05-22", "2010-05-23", "2010-05-24", "2010-05-25", "2010-05-26", "2010-05-27", "2010-05-28",
				"2010-05-29", "2010-05-30", "2010-05-31", "2010-06-01", "2010-06-02", "2010-06-03", "2010-06-04", "2010-06-05", "2010-06-06", "2010-06-07",
				"2010-06-08", "2010-06-09", "2010-06-10", "2010-06-11", "2010-06-12", "2010-06-13", "2010-06-14", "2010-06-15", "2010-06-16", "2010-06-17",
				"2010-06-18", "2010-06-19", "2010-06-20", "2010-06-21", "2010-06-22", "2010-06-23", "2010-06-24", "2010-06-25", "2010-06-27", "2010-06-29",
				"2010-06-30", "2010-07-01", "2010-07-02", "2010-07-03", "2010-07-04", "2010-07-06", "2010-07-11", "2010-07-12", "2010-07-13", "2010-07-14",
				"2010-07-15", "2010-07-16", "2010-07-17", "2010-07-18", "2010-07-19", "2010-07-20", "2010-07-21", "2010-07-22", "2010-07-23", "2010-07-24",
				"2010-07-25", "2010-07-26", "2010-07-27", "2010-07-28", "2010-07-29", "2010-07-30", "2010-07-31", "2010-08-01", "2010-08-02", "2010-08-03",
				"2010-08-04", "2010-08-05", "2010-08-06", "2010-08-07", "2010-08-08", "2010-08-09", "2010-08-10", "2010-08-11", "2010-08-12", "2010-08-13",
				"2010-08-14", "2010-08-15", "2010-08-16", "2010-08-17", "2010-08-18", "2010-08-19", "2010-08-20", "2010-08-21", "2010-08-22", "2010-08-23",
				"2010-08-24", "2010-08-25", "2010-08-26", "2010-08-27", "2010-08-28", "2010-08-29", "2010-08-30", "2010-08-31", "2010-09-01", "2010-09-02",
				"2010-09-03", "2010-09-04", "2010-09-05", "2010-09-06", "2010-09-07", "2010-09-08", "2010-09-09", "2010-09-10", "2010-09-11", "2010-09-12",
				"2010-09-13", "2010-09-14", "2010-09-15", "2010-09-16", "2010-09-17", "2010-09-18", "2010-09-19", "2010-09-20", "2010-09-21", "2010-09-22",
				"2010-09-23", "2010-09-24", "2010-09-25", "2010-09-26", "2010-09-27", "2010-09-28", "2010-09-29", "2010-09-30", "2010-10-01", "2010-10-02",
				"2010-10-03", "2010-10-04", "2010-10-05", "2010-10-06", "2010-10-07", "2010-10-08", "2010-10-09", "2010-10-10", "2010-10-11", "2010-10-12",
				"2010-10-13", "2010-10-14", "2010-10-15", "2010-10-16", "2010-10-17", "2010-10-18", "2010-10-19", "2010-10-20", "2010-10-21", "2010-10-22",
				"2010-10-23", "2010-10-24", "2010-10-25", "2010-10-26", "2010-10-27", "2010-10-28", "2010-10-29", "2010-10-30", "2010-10-31", "2010-11-01",
				"2010-11-02", "2010-11-03", "2010-11-04", "2010-11-05", "2010-11-06", "2010-11-07", "2010-11-08", "2010-11-09", "2010-11-10", "2010-11-11",
				"2010-11-12", "2010-11-13", "2010-11-14", "2010-11-15", "2010-11-16", "2010-11-17", "2010-11-18", "2010-11-19", "2010-11-20", "2010-11-21",
				"2010-11-22", "2010-11-23", "2010-11-24", "2010-11-25", "2010-11-26", "2010-11-27", "2010-11-28", "2010-11-29", "2010-11-30", "2010-12-01",
				"2010-12-02", "2010-12-03", "2010-12-04", "2010-12-05", "2010-12-06", "2010-12-07", "2010-12-08", "2010-12-09", "2010-12-10", "2010-12-11",
				"2010-12-12", "2010-12-13", "2010-12-14", "2010-12-15", "2010-12-16", "2010-12-17", "2010-12-18", "2010-12-19", "2010-12-20", "2010-12-21",
				"2010-12-22", "2010-12-23", "2010-12-24", "2010-12-25", "2010-12-26", "2010-12-27", "2010-12-28", "2010-12-29", "2010-12-30", "2010-12-31",
				"2011-01-01", "2011-01-02", "2011-01-03", "2011-01-04", "2011-01-05", "2011-01-06", "2011-01-07", "2011-01-08", "2011-01-09", "2011-01-10",
				"2011-01-11", "2011-01-12", "2011-01-13", "2011-01-14", "2011-01-15", "2011-01-16", "2011-01-17", "2011-01-18", "2011-01-19", "2011-01-20",
				"2011-01-21", "2011-01-22", "2011-01-23", "2011-01-24", "2011-01-25", "2011-01-26", "2011-01-27", "2011-01-28", "2011-01-29", "2011-01-30",
				"2011-01-31", "2011-02-01", "2011-02-02", "2011-02-03", "2011-02-04", "2011-02-05", "2011-02-06", "2011-02-07", "2011-02-08", "2011-02-09",
				"2011-02-10", "2011-02-11", "2011-02-12", "2011-02-13", "2011-02-14", "2011-02-15", "2011-02-16", "2011-02-17", "2011-02-18", "2011-02-19",
				"2011-02-20", "2011-02-21", "2011-02-22", "2011-02-23", "2011-02-24", "2011-02-25", "2011-02-26", "2011-02-27", "2011-02-28", "2011-03-01",
				"2011-03-02", "2011-03-03", "2011-03-04", "2011-03-05", "2011-03-06", "2011-03-07", "2011-03-08", "2011-03-09", "2011-03-10", "2011-03-11",
				"2011-03-12", "2011-03-13", "2011-03-14", "2011-03-15", "2011-03-16", "2011-03-17", "2011-03-18", "2011-03-19", "2011-03-20", "2011-03-21",
				"2011-03-22", "2011-03-23", "2011-03-24", "2011-03-25", "2011-03-26", "2011-03-27", "2011-03-28", "2011-03-29", "2011-03-30", "2011-03-31",
				"2011-04-01", "2011-04-02", "2011-04-03", "2011-04-04", "2011-04-05", "2011-04-06", "2011-04-07", "2011-04-08", "2011-04-09", "2011-04-10",
				"2011-04-11", "2011-04-12", "2011-04-13", "2011-04-14", "2011-04-15", "2011-04-16", "2011-04-17", "2011-04-18", "2011-04-19", "2011-04-20",
				"2011-04-21", "2011-04-22", "2011-04-23", "2011-04-24", "2011-04-25", "2011-04-26", "2011-04-27", "2011-04-28", "2011-04-29", "2011-04-30",
				"2011-05-01", "2011-05-02", "2011-05-03", "2011-05-04", "2011-05-05", "2011-05-06", "2011-05-07", "2011-05-08", "2011-05-09", "2011-05-10",
				"2011-05-11", "2011-05-12", "2011-05-13", "2011-05-14", "2011-05-15", "2011-05-16", "2011-05-17", "2011-05-18", "2011-05-19", "2011-05-20",
				"2011-05-21", "2011-05-22", "2011-05-23", "2011-05-24", "2011-05-25", "2011-05-26", "2011-05-27", "2011-05-28", "2011-05-29", "2011-05-30",
				"2011-05-31", "2011-06-01", "2011-06-02", "2011-06-03", "2011-06-04", "2011-06-05", "2011-06-06", "2011-06-07", "2011-06-08", "2011-06-09",
				"2011-06-10", "2011-06-11", "2011-06-12", "2011-06-13", "2011-06-14", "2011-06-15", "2011-06-16", "2011-06-17", "2011-06-18", "2011-06-19",
				"2011-06-20", "2011-06-21", "2011-06-22", "2011-06-23", "2011-06-24", "2011-06-25", "2011-06-26", "2011-06-27", "2011-06-28", "2011-06-29",
				"2011-06-30", "2011-07-01", "2011-07-02", "2011-07-03", "2011-07-04", "2011-07-05", "2011-07-06", "2011-07-07", "2011-07-08", "2011-07-09",
				"2011-07-10", "2011-07-11", "2011-07-12", "2011-07-13", "2011-07-14", "2011-07-15", "2011-07-16", "2011-07-17", "2011-07-18", "2011-07-19",
				"2011-07-20", "2011-07-21", "2011-07-22", "2011-07-23", "2011-07-24", "2011-07-25", "2011-07-26", "2011-07-27", "2011-07-28", "2011-07-29",
				"2011-07-30", "2011-07-31", "2011-08-01", "2011-08-02", "2011-08-03", "2011-08-04", "2011-08-05", "2011-08-06", "2011-08-07", "2011-08-08",
				"2011-08-09", "2011-08-10", "2011-08-11", "2011-08-12", "2011-08-13", "2011-08-14", "2011-08-15", "2011-08-16", "2011-08-17", "2011-08-18",
				"2011-08-19", "2011-08-20", "2011-08-21", "2011-08-22", "2011-08-23", "2011-08-24", "2011-08-25", "2011-08-26", "2011-08-27", "2011-08-28",
				"2011-08-29", "2011-08-30", "2011-08-31", "2011-09-01", "2011-09-03", "2011-09-04", "2011-09-05", "2011-09-06", "2011-09-07", "2011-09-08",
				"2011-09-09", "2011-09-10", "2011-09-11", "2011-09-12", "2011-09-13", "2011-09-14", "2011-09-15", "2011-09-16", "2011-09-17", "2011-09-18",
				"2011-09-19", "2011-09-20", "2011-09-21", "2011-09-22", "2011-09-23", "2011-09-24", "2011-09-25", "2011-09-26", "2011-09-27", "2011-09-28",
				"2011-09-29", "2011-09-30", "2011-10-01", "2011-10-02", "2011-10-03", "2011-10-04", "2011-10-05", "2011-10-06", "2011-10-07", "2011-10-08",
				"2011-10-09", "2011-10-10", "2011-10-11", "2011-10-12", "2011-10-13", "2011-10-14", "2011-10-15", "2011-10-16", "2011-10-17", "2011-10-18",
				"2011-10-19", "2011-10-21", "2011-10-22", "2011-10-23", "2011-10-24", "2011-10-25", "2011-10-26", "2011-10-27", "2011-10-28", "2011-10-29",
				"2011-10-30", "2011-10-31", "2011-11-01", "2011-11-02", "2011-11-03", "2011-11-04", "2011-11-05", "2011-11-06", "2011-11-07", "2011-11-08",
				"2011-11-09", "2011-11-10", "2011-11-11", "2011-11-12", "2011-11-13", "2011-11-14", "2011-11-15", "2011-11-16", "2011-11-17", "2011-11-18",
				"2011-11-19", "2011-11-20", "2011-11-21", "2011-11-22", "2011-11-23", "2011-11-24", "2011-11-25", "2011-11-26", "2011-11-27", "2011-11-28",
				"2011-11-29", "2011-11-30", "2011-12-01", "2011-12-02", "2011-12-03", "2011-12-04", "2011-12-05", "2011-12-06", "2011-12-07", "2011-12-08",
				"2011-12-09", "2011-12-10", "2011-12-11", "2011-12-12", "2011-12-13", "2011-12-14", "2011-12-15", "2011-12-16", "2011-12-17", "2011-12-18",
				"2011-12-19", "2011-12-20", "2011-12-21", "2011-12-22", "2011-12-23", "2011-12-26", "2011-12-27", "2011-12-28", "2011-12-29", "2011-12-30",
				"2011-12-31", "2012-01-01", "2012-01-02", "2012-01-03", "2012-01-04", "2012-01-05", "2012-01-06", "2012-01-07", "2012-01-08", "2012-01-09",
				"2012-01-10", "2012-01-11", "2012-01-12", "2012-01-13", "2012-01-14", "2012-01-15", "2012-01-16", "2012-01-17", "2012-01-18", "2012-01-19",
				"2012-01-20", "2012-01-21", "2012-01-22", "2012-01-23", "2012-01-24", "2012-01-25", "2012-01-26", "2012-01-27", "2012-01-28", "2012-01-29",
				"2012-01-30", "2012-01-31", "2012-02-01", "2012-02-02", "2012-02-03", "2012-02-04", "2012-02-05", "2012-02-06", "2012-02-07", "2012-02-08",
				"2012-02-09", "2012-02-10", "2012-02-11", "2012-02-12", "2012-02-13", "2012-02-14", "2012-02-15", "2012-02-16", "2012-02-17", "2012-02-18",
				"2012-02-19", "2012-02-20", "2012-02-21", "2012-02-22", "2012-02-23", "2012-02-24", "2012-02-25", "2012-02-26", "2012-02-27", "2012-02-28",
				"2012-02-29", "2012-03-01", "2012-03-02", "2012-03-03", "2012-03-04", "2012-03-05", "2012-03-06", "2012-03-07", "2012-03-08", "2012-03-09",
				"2012-03-10", "2012-03-11", "2012-03-12", "2012-03-13", "2012-03-14", "2012-03-15", "2012-03-16", "2012-03-17", "2012-03-18", "2012-03-19",
				"2012-03-20", "2012-03-21", "2012-03-22", "2012-03-23", "2012-03-24", "2012-03-25", "2012-03-26", "2012-03-27", "2012-03-28", "2012-03-29",
				"2012-03-30", "2012-03-31", "2012-04-01", "2012-04-02", "2012-04-03", "2012-04-04", "2012-04-05", "2012-04-06", "2012-04-07", "2012-04-08",
				"2012-04-09", "2012-04-10", "2012-04-11", "2012-04-12", "2012-04-13", "2012-04-14", "2012-04-15", "2012-04-16", "2012-04-17", "2012-04-18",
				"2012-04-19", "2012-04-20", "2012-04-21", "2012-04-22", "2012-04-23", "2012-04-24", "2012-04-25", "2012-04-26", "2012-04-27", "2012-04-28",
				"2012-04-29", "2012-05-01", "2012-05-02", "2012-05-03", "2012-05-04", "2012-05-05", "2012-05-06", "2012-05-07", "2012-05-08", "2012-05-09",
				"2012-05-10", "2012-05-11", "2012-05-12", "2012-05-13", "2012-05-14", "2012-05-15", "2012-05-16", "2012-05-17", "2012-05-18", "2012-05-19",
				"2012-05-20", "2012-05-21", "2012-05-22", "2012-05-23", "2012-05-24", "2012-05-25", "2012-05-26", "2012-05-27", "2012-05-28", "2012-05-29",
				"2012-05-30", "2012-05-31", "2012-06-01", "2012-06-02", "2012-06-03", "2012-06-04", "2012-06-05", "2012-06-06", "2012-06-07", "2012-06-08",
				"2012-06-09", "2012-06-10", "2012-06-11", "2012-06-12", "2012-06-13", "2012-06-14", "2012-06-15", "2012-06-16", "2012-06-17", "2012-06-18",
				"2012-06-19", "2012-06-20", "2012-06-21", "2012-06-22", "2012-06-23", "2012-06-24", "2012-06-25", "2012-06-26", "2012-06-27", "2012-06-28",
				"2012-06-29", "2012-06-30", "2012-07-01", "2012-07-02", "2012-07-03", "2012-07-04", "2012-07-05", "2012-07-06", "2012-07-07", "2012-07-08",
				"2012-07-09", "2012-07-10", "2012-07-11", "2012-07-12", "2012-07-13", "2012-07-14", "2012-07-15", "2012-07-16", "2012-07-17", "2012-07-18",
				"2012-07-19", "2012-07-20", "2012-07-21", "2012-07-22", "2012-07-23", "2012-07-24", "2012-07-25", "2012-07-26", "2012-07-27", "2012-07-28",
				"2012-07-29", "2012-07-30", "2012-07-31", "2012-08-01", "2012-08-02", "2012-08-03", "2012-08-04", "2012-08-05", "2012-08-06", "2012-08-07",
				"2012-08-08", "2012-08-09", "2012-08-10", "2012-08-11", "2012-08-12", "2012-08-13", "2012-08-14", "2012-08-15", "2012-08-16", "2012-08-17",
				"2012-08-18", "2012-08-19", "2012-08-20", "2012-08-21", "2012-08-22", "2012-08-23", "2012-08-24", "2012-08-25", "2012-08-26", "2012-08-27",
				"2012-08-28", "2012-08-29", "2012-08-30", "2012-08-31", "2012-09-01", "2012-09-02", "2012-09-03", "2012-09-04", "2012-09-05", "2012-09-06",
				"2012-09-07", "2012-09-08", "2012-09-09", "2012-09-10", "2012-09-11", "2012-09-12", "2012-09-13", "2012-09-14", "2012-09-15", "2012-09-16",
				"2012-09-17", "2012-09-18", "2012-09-19", "2012-09-20", "2012-09-21", "2012-09-22", "2012-09-23", "2012-09-24", "2012-09-25", "2012-09-26",
				"2012-09-27", "2012-09-28", "2012-09-29", "2012-09-30", "2012-10-01", "2012-10-02", "2012-10-03", "2012-10-04", "2012-10-05", "2012-10-06",
				"2012-10-07", "2012-10-08", "2012-10-09", "2012-10-10", "2012-10-11", "2012-10-12", "2012-10-13", "2012-10-14", "2012-10-15", "2012-10-16",
				"2012-10-17", "2012-10-18", "2012-10-19", "2012-10-20", "2012-10-21", "2012-10-22", "2012-10-23", "2012-10-24", "2012-10-25", "2012-10-26",
				"2012-10-27", "2012-10-28", "2012-10-29", "2012-10-30", "2012-10-31", "2012-11-01", "2012-11-02", "2012-11-03", "2012-11-04", "2012-11-05",
				"2012-11-06", "2012-11-07", "2012-11-08", "2012-11-09", "2012-11-10", "2012-11-11", "2012-11-12", "2012-11-13", "2012-11-14", "2012-11-15",
				"2012-11-16", "2012-11-17", "2012-11-18", "2012-11-19", "2012-11-20", "2012-11-21", "2012-11-22", "2012-11-23", "2012-11-24", "2012-11-25",
				"2012-11-26", "2012-11-27", "2012-11-28", "2012-11-29", "2012-11-30", "2012-12-01", "2012-12-02", "2012-12-03", "2012-12-04", "2012-12-05",
				"2012-12-06", "2012-12-07", "2012-12-08", "2012-12-09", "2012-12-10", "2012-12-11", "2012-12-12", "2012-12-13", "2012-12-14", "2012-12-15",
				"2012-12-16", "2012-12-17", "2012-12-18", "2012-12-19", "2012-12-20", "2012-12-21", "2012-12-22", "2012-12-23", "2012-12-24", "2012-12-25",
				"2012-12-26", "2012-12-27", "2012-12-28", "2012-12-29", "2012-12-30", "2012-12-31", "2013-01-01", "2013-01-02", "2013-01-03", "2013-01-04",
				"2013-01-05", "2013-01-06", "2013-01-07", "2013-01-08", "2013-01-09", "2013-01-10", "2013-01-11", "2013-01-12", "2013-01-13", "2013-01-14",
				"2013-01-15", "2013-01-16", "2013-01-17", "2013-01-18", "2013-01-19", "2013-01-20", "2013-01-21", "2013-01-22", "2013-01-23", "2013-01-24",
				"2013-01-25", "2013-01-26", "2013-01-27", "2013-01-28", "2013-01-29", "2013-01-30", "2013-01-31", "2013-02-01", "2013-02-02", "2013-02-03",
				"2013-02-04", "2013-02-05", "2013-02-06", "2013-02-07", "2013-02-08", "2013-02-09", "2013-02-10", "2013-02-11", "2013-02-12", "2013-02-13",
				"2013-02-14", "2013-02-15", "2013-02-16", "2013-02-17", "2013-02-18", "2013-02-19", "2013-02-20", "2013-02-21", "2013-02-22", "2013-02-23",
				"2013-02-24", "2013-02-25", "2013-02-26", "2013-02-27", "2013-02-28", "2013-03-01", "2013-03-02", "2013-03-03", "2013-03-04", "2013-03-05",
				"2013-03-06", "2013-03-07", "2013-03-08", "2013-03-09", "2013-03-10", "2013-03-11", "2013-03-12", "2013-03-13", "2013-03-14", "2013-03-15",
				"2013-03-16", "2013-03-17", "2013-03-18", "2013-03-19", "2013-03-20", "2013-03-21", "2013-03-22", "2013-03-23", "2013-03-24", "2013-03-25",
				"2013-03-26", "2013-03-27", "2013-03-28", "2013-03-29", "2013-03-30", "2013-03-31", "2013-04-01", "2013-04-02", "2013-04-03", "2013-04-04",
				"2013-04-05", "2013-04-06", "2013-04-07", "2013-04-08", "2013-04-09", "2013-04-10", "2013-04-11", "2013-04-12", "2013-04-13", "2013-04-14",
				"2013-04-15", "2013-04-16", "2013-04-17", "2013-04-18", "2013-04-19", "2013-04-20", "2013-04-21", "2013-04-22", "2013-04-23", "2013-04-24",
				"2013-04-25", "2013-04-26", "2013-04-27", "2013-04-28", "2013-04-29", "2013-04-30", "2013-05-01", "2013-05-02", "2013-05-03", "2013-05-04",
				"2013-05-05", "2013-05-06", "2013-05-07", "2013-05-08", "2013-05-09", "2013-05-10", "2013-05-11", "2013-05-12", "2013-05-13", "2013-05-14",
				"2013-05-15", "2013-05-16", "2013-05-17", "2013-05-18", "2013-05-19", "2013-05-20", "2013-05-21", "2013-05-22", "2013-05-23", "2013-05-24",
				"2013-05-25", "2013-05-26", "2013-05-27", "2013-05-28", "2013-05-29", "2013-05-30", "2013-05-31", "2013-06-01", "2013-06-02", "2013-06-03",
				"2013-06-04", "2013-06-05", "2013-06-06", "2013-06-07", "2013-06-08", "2013-06-09", "2013-06-10", "2013-06-11", "2013-06-12", "2013-06-13",
				"2013-06-14", "2013-06-15", "2013-06-16", "2013-06-17", "2013-06-18", "2013-06-19", "2013-06-20", "2013-06-21", "2013-06-22", "2013-06-23",
				"2013-06-24", "2013-06-25", "2013-06-26", "2013-06-27", "2013-06-28", "2013-06-29", "2013-06-30", "2013-07-01", "2013-07-02", "2013-07-03",
				"2013-07-04", "2013-07-05", "2013-07-06", "2013-07-07", "2013-07-08", "2013-07-09", "2013-07-10", "2013-07-11", "2013-07-12", "2013-07-13",
				"2013-07-14", "2013-07-15", "2013-07-16", "2013-07-17", "2013-07-18", "2013-07-19", "2013-07-20", "2013-07-21", "2013-07-22", "2013-07-24",
				"2013-07-25", "2013-07-26", "2013-07-27", "2013-07-28", "2013-07-29", "2013-07-30", "2013-07-31", "2013-08-01", "2013-08-02", "2013-08-03",
				"2013-08-04", "2013-08-05", "2013-08-06", "2013-08-07", "2013-08-08", "2013-08-09", "2013-08-10", "2013-08-11", "2013-08-12", "2013-08-13",
				"2013-08-14", "2013-08-15", "2013-08-16", "2013-08-17", "2013-08-18", "2013-08-19", "2013-08-20", "2013-08-21", "2013-08-22", "2013-08-23",
				"2013-08-24", "2013-08-25", "2013-08-26", "2013-08-27", "2013-08-28", "2013-08-29", "2013-08-30", "2013-08-31", "2013-09-01", "2013-09-02",
				"2013-09-03", "2013-09-04", "2013-09-05", "2013-09-06", "2013-09-07", "2013-09-08", "2013-09-09", "2013-09-10", "2013-09-11", "2013-09-12",
				"2013-09-13", "2013-09-14", "2013-09-15", "2013-09-16", "2013-09-17", "2013-09-18", "2013-09-19", "2013-09-20", "2013-09-21", "2013-09-22",
				"2013-09-23", "2013-09-24", "2013-09-25", "2013-09-26", "2013-09-27", "2013-09-28", "2013-09-29", "2013-09-30", "2013-10-01", "2013-10-02",
				"2013-10-03", "2013-10-04", "2013-10-05", "2013-10-06", "2013-10-07", "2013-10-08", "2013-10-09", "2013-10-10", "2013-10-11", "2013-10-12",
				"2013-10-13", "2013-10-14", "2013-10-15", "2013-10-16", "2013-10-17", "2013-10-18", "2013-10-19", "2013-10-20", "2013-10-21", "2013-10-22",
				"2013-10-23", "2013-10-24", "2013-10-25", "2013-10-26", "2013-10-27", "2013-10-28", "2013-10-29", "2013-10-30", "2013-10-31", "2013-11-01",
				"2013-11-02", "2013-11-03", "2013-11-04", "2013-11-05", "2013-11-06", "2013-11-07", "2013-11-08", "2013-11-09", "2013-11-10", "2013-11-11",
				"2013-11-12", "2013-11-13", "2013-11-14", "2013-11-15", "2013-11-16", "2013-11-17", "2013-11-18", "2013-11-19", "2013-11-20", "2013-11-21",
				"2013-11-22", "2013-11-23", "2013-11-24", "2013-11-25", "2013-11-26", "2013-11-27", "2013-11-28", "2013-11-29", "2013-11-30", "2013-12-01",
				"2013-12-02", "2013-12-03", "2013-12-04", "2013-12-05", "2013-12-06", "2013-12-07", "2013-12-08", "2013-12-09", "2013-12-10", "2013-12-11",
				"2013-12-12", "2013-12-13", "2013-12-14", "2013-12-15", "2013-12-16", "2013-12-17", "2013-12-18", "2013-12-19", "2013-12-20", "2013-12-21",
				"2013-12-22", "2013-12-23", "2013-12-24", "2013-12-25", "2013-12-26", "2013-12-27", "2013-12-28", "2013-12-29", "2013-12-30", "2013-12-31",
				"2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04", "2014-01-05", "2014-01-07", "2014-01-08", "2014-01-09", "2014-01-10", "2014-01-11",
				"2014-01-12", "2014-01-13", "2014-01-14", "2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19", "2014-01-20", "2014-01-21",
				"2014-01-22", "2014-01-23", "2014-01-24", "2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29", "2014-01-30", "2014-01-31",
				"2014-02-01", "2014-02-02", "2014-02-03", "2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08", "2014-02-09", "2014-02-10",
				"2014-02-11", "2014-02-12", "2014-02-13", "2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18", "2014-02-19", "2014-02-20",
				"2014-02-21", "2014-02-22", "2014-02-23", "2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28", "2014-03-01", "2014-03-02",
				"2014-03-03", "2014-03-04", "2014-03-05", "2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10", "2014-03-11", "2014-03-12",
				"2014-03-13", "2014-03-14", "2014-03-15", "2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20", "2014-03-21", "2014-03-22",
				"2014-03-23", "2014-03-24", "2014-03-25", "2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30", "2014-03-31", "2014-04-01",
				"2014-04-02", "2014-04-03", "2014-04-04", "2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09", "2014-04-10", "2014-04-11",
				"2014-04-12", "2014-04-13", "2014-04-14", "2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19", "2014-04-20", "2014-04-21",
				"2014-04-22", "2014-04-23", "2014-04-24", "2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29", "2014-04-30", "2014-05-01",
				"2014-05-02", "2014-05-03", "2014-05-04", "2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09", "2014-05-10", "2014-05-11",
				"2014-05-12", "2014-05-13", "2014-05-14", "2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19", "2014-05-20", "2014-05-21",
				"2014-05-22", "2014-05-23", "2014-05-24", "2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29", "2014-05-30", "2014-05-31",
				"2014-06-01", "2014-06-02", "2014-06-03", "2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08", "2014-06-09", "2014-06-10",
				"2014-06-11", "2014-06-12", "2014-06-13", "2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18", "2014-06-19", "2014-06-20",
				"2014-06-21", "2014-06-22", "2014-06-23", "2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28", "2014-06-29", "2014-06-30",
				"2014-07-01", "2014-07-02", "2014-07-03", "2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08", "2014-07-09", "2014-07-10",
				"2014-07-11", "2014-07-12", "2014-07-13", "2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18", "2014-07-19", "2014-07-20",
				"2014-07-21", "2014-07-22", "2014-07-23", "2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28", "2014-07-29", "2014-07-30",
				"2014-07-31", "2014-08-01", "2014-08-02", "2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07", "2014-08-08", "2014-08-09",
				"2014-08-10", "2014-08-11", "2014-08-12", "2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17", "2014-08-18", "2014-08-19",
				"2014-08-20", "2014-08-21", "2014-08-22", "2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27", "2014-08-29", "2014-08-30",
				"2014-08-31", "2014-09-01", "2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06", "2014-09-07", "2014-09-08", "2014-09-09",
				"2014-09-10", "2014-09-11", "2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16", "2014-09-17", "2014-09-18", "2014-09-19",
				"2014-09-20", "2014-09-21", "2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26", "2014-09-27", "2014-09-28", "2014-09-29",
				"2014-09-30", "2014-10-01", "2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06", "2014-10-07", "2014-10-08", "2014-10-09",
				"2014-10-10", "2014-10-11", "2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16", "2014-10-17", "2014-10-18", "2014-10-19",
				"2014-10-20", "2014-10-21", "2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26", "2014-10-27", "2014-10-28", "2014-10-29",
				"2014-10-30", "2014-10-31", "2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05", "2014-11-06", "2014-11-07", "2014-11-08",
				"2014-11-09", "2014-11-10", "2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15", "2014-11-16", "2014-11-17", "2014-11-18",
				"2014-11-19", "2014-11-20", "2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25", "2014-11-26", "2014-11-27", "2014-11-28",
				"2014-11-29", "2014-11-30", "2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05", "2014-12-06", "2014-12-07", "2014-12-08",
				"2014-12-09", "2014-12-10", "2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15", "2014-12-16", "2014-12-17", "2014-12-18",
				"2014-12-19", "2014-12-20", "2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25", "2014-12-26", "2014-12-27", "2014-12-28",
				"2014-12-29", "2014-12-30", "2014-12-31", "2015-01-01", "2015-01-02", "2015-01-03", "2015-01-04", "2015-01-05", "2015-01-06", "2015-01-07",
				"2015-01-08", "2015-01-09", "2015-01-10", "2015-01-11", "2015-01-12", "2015-01-13", "2015-01-14", "2015-01-15", "2015-01-16", "2015-01-17",
				"2015-01-18", "2015-01-19", "2015-01-20", "2015-01-21", "2015-01-22", "2015-01-23", "2015-01-24", "2015-01-25", "2015-01-26", "2015-01-27",
				"2015-01-28", "2015-01-29", "2015-01-30", "2015-01-31", "2015-02-01", "2015-02-02", "2015-02-03", "2015-02-04", "2015-02-06", "2015-02-07",
				"2015-02-08", "2015-02-09", "2015-02-10", "2015-02-11", "2015-02-12", "2015-02-13", "2015-02-14", "2015-02-15", "2015-02-16", "2015-02-17",
				"2015-02-18", "2015-02-19", "2015-02-20", "2015-02-21", "2015-02-22", "2015-02-23", "2015-02-24", "2015-02-25", "2015-02-26", "2015-02-27",
				"2015-02-28", "2015-03-01", "2015-03-02", "2015-03-03", "2015-03-04", "2015-03-05", "2015-03-06", "2015-03-07", "2015-03-08", "2015-03-09",
				"2015-03-10", "2015-03-11", "2015-03-12", "2015-03-13", "2015-03-14", "2015-03-15", "2015-03-16", "2015-03-17", "2015-03-18", "2015-03-19",
				"2015-03-20", "2015-03-21", "2015-03-22", "2015-03-23", "2015-03-24", "2015-03-25", "2015-03-26", "2015-03-27", "2015-03-28", "2015-03-29",
				"2015-03-30", "2015-03-31", "2015-04-01", "2015-04-02", "2015-04-03", "2015-04-04", "2015-04-05", "2015-04-06", "2015-04-07", "2015-04-08",
				"2015-04-09", "2015-04-10", "2015-04-11", "2015-04-12", "2015-04-13", "2015-04-14", "2015-04-15", "2015-04-16", "2015-04-17", "2015-04-18",
				"2015-04-19", "2015-04-20", "2015-04-21", "2015-04-22", "2015-04-23", "2015-04-24", "2015-04-25", "2015-04-26", "2015-04-27", "2015-04-28",
				"2015-04-29", "2015-04-30", "2015-05-01", "2015-05-02", "2015-05-03", "2015-05-04", "2015-05-05", "2015-05-06", "2015-05-07", "2015-05-08",
				"2015-05-09", "2015-05-10", "2015-05-11", "2015-05-12", "2015-05-13", "2015-05-14", "2015-05-15", "2015-05-16", "2015-05-17", "2015-05-18",
				"2015-05-19", "2015-05-20", "2015-05-21", "2015-05-22", "2015-05-23", "2015-05-24", "2015-05-25", "2015-05-26", "2015-05-27", "2015-05-28",
				"2015-05-29", "2015-05-30", "2015-05-31", "2015-06-01", "2015-06-02", "2015-06-03", "2015-06-04", "2015-06-05", "2015-06-06", "2015-06-07",
				"2015-06-08", "2015-06-09", "2015-06-10", "2015-06-11", "2015-06-12", "2015-06-13", "2015-06-14", "2015-06-15", "2015-06-16", "2015-06-17",
				"2015-06-18", "2015-06-19", "2015-06-20", "2015-06-21", "2015-06-22", "2015-06-23", "2015-06-24", "2015-06-25", "2015-06-26", "2015-06-27",
				"2015-06-28", "2015-06-29", "2015-06-30", "2015-07-01", "2015-07-02", "2015-07-03", "2015-07-04", "2015-07-05", "2015-07-06", "2015-07-07",
				"2015-07-08", "2015-07-09", "2015-07-10", "2015-07-11", "2015-07-12", "2015-07-13", "2015-07-14", "2015-07-15", "2015-07-16", "2015-07-17",
				"2015-07-18", "2015-07-19", "2015-07-20", "2015-07-21", "2015-07-22", "2015-07-23", "2015-07-24", "2015-07-25", "2015-07-26", "2015-07-27",
				"2015-07-28", "2015-07-29", "2015-07-30", "2015-07-31", "2015-08-01", "2015-08-02", "2015-08-03", "2015-08-04", "2015-08-05", "2015-08-06",
				"2015-08-07", "2015-08-08", "2015-08-09", "2015-08-10", "2015-08-11", "2015-08-12", "2015-08-13", "2015-08-14", "2015-08-15", "2015-08-16",
				"2015-08-17", "2015-08-18", "2015-08-19", "2015-08-20", "2015-08-21", "2015-08-22", "2015-08-23", "2015-08-24", "2015-08-25", "2015-08-26",
				"2015-08-27", "2015-08-28", "2015-08-29", "2015-08-30", "2015-08-31", "2015-09-01", "2015-09-02", "2015-09-03", "2015-09-04", "2015-09-05",
				"2015-09-06", "2015-09-07", "2015-09-08", "2015-09-09", "2015-09-10", "2015-09-11", "2015-09-12", "2015-09-13", "2015-09-14", "2015-09-15",
				"2015-09-16", "2015-09-17", "2015-09-18", "2015-09-19", "2015-09-20", "2015-09-21", "2015-09-22", "2015-09-23", "2015-09-24", "2015-09-25",
				"2015-09-26", "2015-09-27", "2015-09-28", "2015-09-29", "2015-09-30", "2015-10-01", "2015-10-02", "2015-10-03", "2015-10-04", "2015-10-05",
				"2015-10-06", "2015-10-07", "2015-10-08", "2015-10-09", "2015-10-10", "2015-10-11", "2015-10-13", "2015-10-14", "2015-10-15", "2015-10-16",
				"2015-10-17", "2015-10-18", "2015-10-19", "2015-10-20", "2015-10-21", "2015-10-22", "2015-10-23", "2015-10-24", "2015-10-25", "2015-10-26",
				"2015-10-27", "2015-10-28", "2015-10-29", "2015-10-30", "2015-10-31", "2015-11-01", "2015-11-02", "2015-11-03", "2015-11-04", "2015-11-05",
				"2015-11-06", "2015-11-07", "2015-11-08", "2015-11-09", "2015-11-10", "2015-11-11", "2015-11-12", "2015-11-13", "2015-11-14", "2015-11-15",
				"2015-11-16", "2015-11-17", "2015-11-18", "2015-11-19", "2015-11-20", "2015-11-21", "2015-11-22", "2015-11-23", "2015-11-24", "2015-11-25",
				"2015-11-26", "2015-11-27", "2015-11-28", "2015-11-29", "2015-11-30", "2015-12-01", "2015-12-02", "2015-12-03", "2015-12-04", "2015-12-05",
				"2015-12-06", "2015-12-07", "2015-12-08", "2015-12-09", "2015-12-10", "2015-12-11", "2015-12-12", "2015-12-13", "2015-12-14", "2015-12-15",
				"2015-12-16", "2015-12-17", "2015-12-18", "2015-12-19", "2015-12-20", "2015-12-21", "2015-12-22", "2015-12-23", "2015-12-24", "2015-12-25",
				"2015-12-26", "2015-12-27", "2015-12-28", "2015-12-29", "2015-12-30", "2015-12-31", "2016-01-01", "2016-01-02", "2016-01-03", "2016-01-04",
				"2016-01-05", "2016-01-06", "2016-01-07", "2016-01-08", "2016-01-09", "2016-01-10", "2016-01-11", "2016-01-12", "2016-01-13", "2016-01-14",
				"2016-01-15", "2016-01-16", "2016-01-17", "2016-01-18", "2016-01-19", "2016-01-20", };
	}

	private Double[] getDoubleArrayForProphet() {
		return new Double[] { 9.59076113897809, 8.51959031601596, 8.18367658262066, 8.07246736935477, 7.8935720735049, 7.78364059622125, 8.41405243249672,
				8.82922635473185, 8.38251828808963, 8.06965530688617, 7.87929148508227, 7.76174498465891, 7.52940645783701, 8.38526052015541, 8.62011072542292,
				7.85243908535751, 7.85399308722424, 8.0519780789023, 7.92660259918138, 7.83834331555712, 9.70314458114435, 9.38597294061934, 8.29379960884682,
				8.43468076984177, 8.26204284396694, 8.10681603894705, 7.95014988765202, 9.50925907635395, 8.84678466694523, 8.43054538469057, 8.2482674474469,
				8.28172399041139, 8.29279885820037, 8.19918935907807, 9.99652241850332, 10.1270710070787, 8.93379604393486, 8.56617381363786, 8.54772239645106,
				8.39976009452414, 8.22309055116153, 8.83898679349679, 10.8972021813751, 9.44493807333551, 8.92332474406756, 8.5434455625603, 8.49556089128912,
				8.41737285613403, 8.57262789830434, 8.73648935100155, 8.63408694288774, 8.67351294567119, 8.82423661734664, 8.53797573059877, 9.69806112202708,
				12.0974568371517, 10.6352783566883, 9.69171658751689, 9.31560088263368, 8.97081334141145, 8.58914169072882, 8.61740045183326, 8.61631428228404,
				8.21554741194707, 8.06495089174914, 8.11342663994365, 7.79934339821592, 7.6275443904885, 7.55590509361135, 7.71154897962915, 7.78862606562503,
				7.70841066725737, 7.76853330092603, 7.69530313496357, 7.37838371299671, 7.91059061225648, 7.62657020629066, 7.57353126274595, 7.56786260546388,
				7.5522372875608, 7.33693691370762, 8.12474302038557, 7.88758403166028, 7.81963630236759, 7.38398945797851, 7.81439963380449, 7.5422134631934,
				7.54855597916987, 7.6889133368648, 7.4770384723197, 7.35883089834235, 7.03262426102801, 7.11801620446533, 7.34987370473834, 7.32646561384032,
				7.36391350140582, 7.27793857294566, 7.25134498337221, 7.00215595440362, 7.16394668434255, 7.7591874385078, 7.51860721681525, 7.41397029019044,
				7.44249272279444, 7.2283884515736, 6.99117688712121, 7.2115567333138, 7.31121838441963, 7.34923082461333, 7.51425465281641, 7.39326309476384,
				7.28619171470238, 7.27309259599952, 7.16857989726403, 7.61134771740362, 7.61775957660851, 7.42595365707754, 8.18200013629341, 7.39939808333135,
				7.1066061377273, 7.08086789669078, 7.42117752859539, 7.49164547360513, 7.38585107812521, 7.68294316987829, 7.6889133368648, 7.2591161280971,
				8.14496941708788, 8.71391062849392, 8.09101504171053, 7.84031298332016, 7.64873978895624, 8.02092771898158, 7.83002808253384, 7.72753511047545,
				7.83597458172157, 7.62657020629066, 7.64444076155657, 7.54855597916987, 7.44073370738926, 7.07326971745971, 7.02642680869964, 7.39203156751459,
				7.29301767977278, 7.36137542897735, 7.51261754467451, 7.46049030582534, 7.11476944836646, 7.12528309151071, 7.45587668749182, 7.31721240835984,
				7.41034709782102, 7.40245152081824, 7.29437729928882, 6.98933526597456, 6.99301512293296, 7.4312996751559, 7.36201055125973, 7.454719949364,
				7.36833968631138, 7.14361760270412, 6.8351845861473, 6.89060912014717, 6.97447891102505, 7.26612877955645, 7.27031288607902, 7.26542972325395,
				7.15773548424991, 6.86589107488344, 7.15383380157884, 7.21670948670946, 7.20191631753163, 7.21229446850034, 7.18614430452233, 6.82001636467413,
				6.88243747099785, 7.18311170174328, 7.24279792279376, 7.1929342212158, 7.18387071506245, 6.97073007814353, 6.9177056098353, 7.22256601882217,
				7.35691824235602, 7.24279792279376, 7.22329567956231, 7.16317239084664, 7.24136628332232, 6.98656645940643, 6.96318998587024, 7.25063551189868,
				7.1608459066643, 7.13249755166004, 7.09174211509515, 7.01211529430638, 6.85751406254539, 6.82762923450285, 7.13009851012558, 7.27724772663148,
				7.03878354138854, 6.89060912014717, 6.95749737087695, 7.29573507274928, 7.49720722320332, 7.31055015853442, 7.20489251020467, 7.22256601882217,
				7.25770767716004, 7.42595365707754, 7.11720550316434, 7.24992553671799, 8.27690348126706, 7.60638738977265, 7.64396194900253, 8.01234963932779,
				7.93020620668468, 7.50878717063428, 7.52131798019924, 7.58984151218266, 7.50052948539529, 7.37023064180708, 7.38523092306657, 7.30249642372733,
				7.27517231945277, 7.39203156751459, 7.51697722460432, 7.90912218321141, 7.72312009226633, 7.67042852219069, 7.62900388965296, 7.43070708254597,
				7.57967882309046, 7.9483852851119, 7.79564653633459, 8.00736706798333, 7.79069603117474, 7.83280751652486, 7.51479976048867, 7.6275443904885,
				7.85515700588134, 7.96485088744731, 7.74586822979227, 8.08085641964099, 8.25997565976828, 7.7698009960039, 8.17751582384608, 9.28173036806286,
				8.33854487998858, 7.83042561782033, 7.8087293067444, 7.81681996576455, 7.55485852104068, 8.55506684384432, 8.20794694104862, 8.08363720314155,
				7.79110951061003, 7.67089483136212, 7.64012317269536, 7.55013534248843, 8.24931374626064, 8.3039999709552, 8.12681372072611, 7.70616297019958,
				7.66387725870347, 7.52671756135271, 7.92588031673756, 7.92153563213355, 8.03398273468322, 7.72577144158795, 7.69439280262942, 7.44949800538285,
				7.39141523467536, 7.34601020991329, 8.10137467122858, 8.15651022607997, 7.97108575350561, 7.87283617502572, 7.63530388625941, 7.58781721999343,
				7.34213173058472, 8.17751582384608, 8.34093322600088, 8.47657950853094, 7.87359778968554, 7.71735127218533, 7.49052940206071, 7.37588214821501,
				8.07558263667172, 8.16536363247398, 7.58528107863913, 7.60738142563979, 7.26752542782817, 8.04012466444838, 8.10922495308995, 8.80687326653069,
				7.74716496652033, 7.48099216286952, 7.34665516317654, 7.24708058458576, 7.93808872689695, 9.03562977818356, 8.04109100370863, 7.40610338123702,
				7.97384437594469, 7.76811037852599, 7.4713630881871, 8.03008409426756, 8.72939712269206, 7.93701748951545, 7.66528471847135, 7.58018941794454,
				7.7106533235012, 7.26122509197192, 8.04654935728308, 8.09346227450118, 7.76726399675731, 7.49665243816828, 7.5522372875608, 7.49720722320332,
				7.3125534981026, 7.93880224815448, 9.05870319731322, 8.19422930481982, 7.51914995766982, 7.55118686729615, 7.71378461659875, 7.60589000105312,
				8.49902922078857, 8.29179710504873, 7.89469085042562, 7.79028238070348, 7.65539064482615, 7.61035761831284, 7.53101633207792, 8.23137604557397,
				8.00670084544037, 7.85864065562079, 7.69712131728263, 7.59588991771854, 7.73587031995257, 7.35115822643069, 8.03138533062553, 8.39434736141739,
				7.82364593083495, 8.08671792030391, 7.77148876011762, 8.68895923427068, 7.74716496652033, 7.96067260838812, 8.62461158818351, 7.99665387546261,
				7.62070508683826, 7.4318919168078, 7.46278915741245, 7.4489161025442, 8.25140306538056, 8.55525939222269, 8.31581113188354, 8.30992298925832,
				7.75876054415766, 8.80821966511841, 9.12194622121359, 10.1538181636943, 9.26785427817679, 8.43424627059531, 8.13768818497761, 8.04494704961772,
				8.22897764335831, 8.14118979345769, 9.21562637640542, 8.73278832497312, 8.51016857647927, 8.10409905614358, 7.95014988765202, 7.85205020726589,
				7.65633716643018, 8.04430540699064, 8.80101783354071, 7.82963038915019, 7.77737360265786, 7.93522953981691, 7.67229245562876, 7.38832785957711,
				7.51152464839087, 7.66340766489348, 7.78945456608667, 7.80791662892641, 7.82484569102686, 7.90654723236804, 7.70210434005105, 8.47762041629641,
				9.14952823257943, 8.06211758275474, 8.06652149046999, 8.05959232888755, 8.04942705711069, 7.7621706071382, 8.09773057366422, 8.01829613851552,
				7.61677580869837, 7.84267147497946, 7.76853330092603, 7.53047999524554, 7.33236920592906, 7.22402480828583, 7.41637847919293, 7.42714413340862,
				7.39756153552405, 7.49554194388426, 7.39939808333135, 7.00850518208228, 7.11801620446533, 7.48380668766583, 7.57147364885127, 7.64826303090192,
				7.47420480649612, 7.47250074473756, 7.2115567333138, 7.34342622914737, 7.48211892355212, 7.41095187558364, 7.40306109109009, 7.45298232946546,
				7.42356844425917, 7.08506429395255, 7.21081845347222, 7.35627987655075, 7.30451594646016, 7.46565531013406, 7.86901937649902, 7.24850407237061,
				7.07834157955767, 7.2211050981825, 7.33432935030054, 7.29233717617388, 7.2991214627108, 7.28344822875663, 7.30182234213793, 7.06219163228656,
				7.18159194461187, 7.50549227473742, 7.87473912517181, 7.57865685059476, 7.36707705988101, 7.25700270709207, 7.05617528410041, 7.50273821075485,
				7.44307837434852, 7.48155570190952, 7.48211892355212, 7.51914995766982, 7.65964295456468, 7.24422751560335, 7.23273313617761, 7.34213173058472,
				7.42117752859539, 7.49220304261874, 7.31521838975297, 7.14124512235049, 7.00940893270864, 7.08757370555797, 7.30451594646016, 7.37462901521894,
				7.51261754467451, 7.51752085060303, 7.37400185935016, 7.14124512235049, 7.16162200293919, 7.48493028328966, 7.51261754467451, 7.44483327389219,
				7.47420480649612, 7.67182679787878, 7.84227877911735, 7.92407232492342, 7.82843635915759, 7.58680353516258, 7.62997570702779, 7.70975686445416,
				7.52671756135271, 7.19368581839511, 7.25770767716004, 7.45414107814668, 7.48155570190952, 7.55903825544338, 7.44483327389219, 7.3375877435386,
				7.13568734702814, 7.08506429395255, 7.27239839257005, 7.5109777520141, 7.49886973397693, 7.44424864949671, 7.40306109109009, 6.91671502035361,
				6.97728134163075, 7.48268182815465, 7.41397029019044, 7.37211802833779, 7.33367639565768, 7.39510754656249, 7.03614849375054, 6.87419849545329,
				6.98471632011827, 7.45587668749182, 7.49498623395053, 7.33106030521863, 7.10496544826984, 6.99393297522319, 6.93049476595163, 7.21817683840341,
				7.4759059693674, 7.36454701425564, 7.23993259132047, 7.30921236569276, 7.13886699994552, 6.97260625130175, 7.18841273649695, 7.33498187887181,
				7.33432935030054, 7.43248380791712, 7.39141523467536, 6.96129604591017, 7.02197642307216, 7.21376830811864, 7.50988306115491, 7.32843735289516,
				7.49665243816828, 7.20042489294496, 7.08422642209792, 7.14913159855741, 7.25629723969068, 7.19818357710194, 7.28892769452126, 7.12849594568004,
				7.0825485693553, 6.82219739062049, 6.94793706861497, 7.11232744471091, 7.19967834569117, 7.31721240835984, 7.09837563859079, 7.04490511712937,
				7.30451594646016, 7.79358680337158, 7.80547462527086, 7.48324441607385, 7.35691824235602, 7.50714107972761, 7.37525577800975, 7.19668657083435,
				7.22329567956231, 7.35244110024358, 7.31721240835984, 7.44424864949671, 7.35564110297425, 7.32448997934853, 7.21450441415114, 7.2841348061952,
				7.54009032014532, 7.47477218239787, 7.82923253754359, 7.68109900153636, 7.72973533138505, 7.3031700512368, 7.28207365809346, 7.48549160803075,
				7.61874237767041, 7.69393732550927, 7.53955882930103, 7.43838353004431, 7.27378631784489, 7.35564110297425, 7.65822752616135, 7.84345640437612,
				8.36846113761584, 8.1721644521119, 7.81156848934518, 7.57507169950756, 7.6586995582683, 7.86633892304654, 7.78113850984502, 7.75491027202143,
				7.70885960104718, 8.20712916807133, 7.71154897962915, 7.73455884435476, 7.96762673933382, 8.40380050406115, 8.29279885820037, 7.98548435673382,
				8.67180090964268, 7.78239033558746, 7.78696700261487, 7.94661756324447, 8.17357548663415, 7.80954132465341, 7.80302664363222, 8.05134093329298,
				7.97315543344413, 7.86901937649902, 8.16251625014018, 8.04012466444838, 7.96346006663897, 7.83834331555712, 7.91315518592807, 7.95331834656043,
				8.07620452723903, 7.98241634682773, 8.1285852003745, 7.95472333449791, 8.1101268019411, 8.21365270303, 8.04686951095958, 8.9242570208881,
				8.61721950548336, 8.51959031601596, 8.01433573729942, 8.05769419481559, 8.10440130792161, 7.99395754757357, 8.43185314424922, 9.73820008829795,
				10.1799822793473, 8.36194190614495, 9.94884325425692, 8.75904072752422, 8.25166392360559, 8.07868822922987, 8.17357548663415, 7.88908440703551,
				9.09985563880091, 9.40656483393913, 9.16440114003474, 8.48735234940522, 8.43141741439483, 8.2776661608515, 8.31213510764841, 8.79011689289247,
				10.2800386504796, 9.07577987858049, 8.69918135930895, 9.08658956454001, 8.86149186428691, 8.54441917766983, 8.44762872803033, 8.25270667656764,
				8.10982627601848, 8.13681086367554, 8.7268056084461, 9.38269576445829, 8.57828829077605, 8.23880116587155, 8.12237124340655, 8.05515773181968,
				7.83241092718792, 8.89754559870933, 8.72566970568704, 8.56407677731509, 8.2190566610606, 8.14757773620177, 8.0013550258267, 7.78945456608667,
				8.81195017753998, 9.00220857828241, 8.59304250369967, 8.28197705886776, 8.46505743699571, 8.49474306257865, 8.40514368760761, 10.558699193753,
				9.12456459495478, 8.73182058296211, 8.52892411429194, 8.50512061018197, 8.27639470486331, 9.1239106439778, 8.58597270681106, 8.31556648356428,
				8.66112036022288, 8.92572027356022, 8.44139147799996, 9.2277872855799, 9.26473385580652, 9.27491014262548, 8.39298958795693, 8.58522560180806,
				8.42376124662369, 8.33782726244791, 9.05975001334368, 9.29825967001407, 8.76186337327473, 8.50754681436443, 8.39931015075952, 8.52357279838028,
				8.37953902611744, 9.09110628405248, 9.76198159024195, 8.92956770782534, 8.53070154144103, 8.58709231879591, 9.79784922051313, 8.66475075577385,
				9.17232692977797, 9.20140053040671, 9.33052053223229, 8.68457030082437, 8.50248556254396, 8.28878581042693, 8.29804166137157, 9.16293424957891,
				9.54795481317617, 9.01724094201035, 8.78492762605832, 8.38662882139512, 8.33447155460094, 8.53601494565683, 8.70863965598719, 8.73004395324502,
				8.37562962709445, 8.31898612539206, 8.46442512587758, 8.5972974356579, 8.92279162396964, 9.49167735686812, 9.21014035197352, 8.67795057029435,
				8.60226936377136, 8.61450137388324, 8.65886634973238, 8.77940359789435, 11.0079327963967, 9.75324588920559, 9.22513045744882, 9.0177260256968,
				8.93695560422523, 9.00932517273497, 11.4840629202851, 10.2642341958449, 9.69443180053954, 9.44041981429151, 9.35374783527091, 9.22847494217167,
				9.30392178559771, 10.2401740519157, 9.91595945403145, 10.1115174660403, 9.85859478364539, 10.1190020766858, 10.0005688901867, 11.1914521795828,
				10.4633318857817, 9.65406419220144, 9.11975899374495, 8.79573360595074, 8.44848599340645, 8.2666784433059, 8.21851757748959, 8.24249315318763,
				8.00803284696931, 8.0452677166078, 7.9287663216267, 7.74500280351584, 7.86633892304654, 7.94165125293056, 8.31041499418829, 7.82803803212583,
				7.87359778968554, 7.75705114203201, 7.72621265050753, 7.77527584648686, 7.79523492900217, 7.74975340627444, 8.06808962627824, 8.72583205652757,
				7.65444322647011, 7.60339933974067, 7.75319426988434, 7.77022320415879, 7.63143166457691, 7.54380286750151, 7.60439634879634, 7.58426481838906,
				7.5109777520141, 7.67461749736436, 7.71289096149013, 7.70165236264223, 7.63819824428578, 7.56268124672188, 7.40367029001237, 7.46622755621548,
				7.61233683716775, 7.80180040190897, 8.02878116248715, 7.73017479524622, 7.63964228785801, 7.56320059235807, 7.48661331313996, 7.5076900778199,
				7.65396918047877, 7.61283103040736, 7.45414107814668, 7.36707705988101, 7.45298232946546, 7.47873482556787, 7.98514393119862, 7.82164312623998,
				7.66058546170326, 7.5595594960077, 7.57660976697304, 7.4500795698075, 7.49886973397693, 7.51588908521513, 7.60837447438078, 7.58629630715272,
				7.68063742756094, 7.7848892956551, 7.5522372875608, 7.59890045687141, 7.64826303090192, 7.66996199547358, 7.85554467791566, 8.09651291750159,
				8.92105701815743, 8.3986348552921, 7.98820359702258, 8.00269416228394, 8.07309119969315, 7.98309894071089, 7.84619881549743, 7.78655180642871,
				7.44483327389219, 7.5422134631934, 7.6425241342329, 7.6511201757027, 7.51152464839087, 7.67693714581808, 7.9912539298402, 7.44190672805162,
				7.38398945797851, 7.60589000105312, 7.58680353516258, 7.62119516280984, 7.29573507274928, 7.48885295573346, 7.27309259599952, 7.34665516317654,
				7.47363710849621, 7.35564110297425, 7.2283884515736, 7.39694860262101, 7.47533923656674, 7.40974195408092, 7.34601020991329, 7.4079243225596,
				7.38398945797851, 7.38087903556412, 7.20637729147225, 7.09340462586877, 7.10987946307227, 7.05531284333975, 7.11639414409346, 7.19218205871325,
				7.24921505711439, 7.41938058291869, 7.56216163122565, 7.43307534889858, 7.28550654852279, 7.9355873855892, 9.01954299670119, 7.22548147278229,
				7.02731451403978, 6.7990558620588, 5.44673737166631, 5.32300997913841, 5.26269018890489, 6.30627528694802, 6.65286302935335, 7.21964204013074,
				7.38832785957711, 7.13886699994552, 7.04315991598834, 7.20637729147225, 7.02997291170639, 7.00760061395185, 6.91869521902047, 6.88448665204278,
				6.13772705408623, 7.20637729147225, 7.13727843726039, 7.05444965813294, 7.11232744471091, 6.92657703322272, 6.81454289725996, 7.35500192110526,
				7.13169851046691, 7.07749805356923, 7.24208235925696, 7.24708058458576, 7.646353722446, 7.45645455517621, 7.30988148582479, 7.23777819192344,
				7.27517231945277, 7.46908388492123, 7.45066079621154, 8.12740456269308, 7.77485576666552, 7.52131798019924, 7.54960916515453, 7.94979721616185,
				7.79770203551669, 7.79975331828725, 7.9002660367677, 7.85825418218603, 7.94165125293056, 7.67136092319064, 8.13534694890671, 8.68777949199177,
				8.45318786144033, 8.06463647577422, 8.00936307663004, 7.87739718635329, 7.85515700588134, 8.14089846060785, 7.92117272158701, 7.9707403900071,
				7.96519829061218, 8.13476078241865, 7.79852305362521, 8.30770596654951, 8.28071107566285, 8.74448811385292, 8.59137258959049, 8.44052810648075,
				8.50976567558744, 8.35514473946184, 8.28096440055337, 8.44052810648075, 8.31385226739821, 8.08085641964099, 8.18590748148232, 8.85680335672838,
				8.07309119969315, 8.14148104145742, 8.18785544369562, 8.11522197256233, 8.15908865466791, 8.20439841814938, 8.28500889544988, 8.08271113423758,
				8.24564690087386, 8.2220164372022, 8.45126704130007, 8.5519810169019, 8.62515033292133, 9.13194630454817, 8.33997857199043, 9.86620096775011,
				9.27077674078001, 8.77183540978982, 8.49474306257865, 8.61631428228404, 8.94780609305705, 9.07577987858049, 9.50046944807102, 11.4261031610143,
				9.29550838434606, 8.61721950548336, 8.47699600166482, 8.32772616646141, 8.30375241556341, 9.330431852234, 9.41613428495528, 8.92292493064183,
				8.44591198941127, 8.31115254800169, 8.27052509505507, 8.20166019080868, 9.00981411052738, 9.30909914399945, 9.12847934549586, 8.44741429680832,
				8.25426877009018, 8.32482129876878, 8.10288913464087, 9.15957325492253, 8.85109068766498, 9.54057893384188, 8.49043845410742, 8.56464913257253,
				8.352318548226, 8.10440130792161, 9.15334665045606, 10.0752957033132, 8.71800933084636, 8.34474275441755, 8.2630748358026, 8.25608813381491,
				8.00869818298853, 8.52971447196991, 8.861350110796, 9.78914235075127, 8.50025047068593, 8.40559101483493, 8.9441588309704, 8.866581653304,
				9.01456876745782, 9.13010597926558, 10.2465097200211, 8.58969988220299, 8.65067458279072, 8.78124833323686, 8.33302993974291, 9.06762406977459,
				9.52332462729018, 8.70996000607173, 8.37101068123816, 8.37770121259764, 8.40043463080604, 8.18283871076603, 8.8750074860484, 9.21034037197618,
				8.71456755083648, 8.31752199628717, 8.55929436743487, 8.60465446718623, 8.73375513136489, 9.54057893384188, 10.1616893196654, 8.8167050156216,
				8.31409733540581, 8.7106195279423, 8.74369111054302, 8.39231000926955, 9.23073106162392, 10.2561143136283, 8.9138193508572, 8.65032450401942,
				8.39004140575575, 8.3091845276863, 8.22228507387272, 9.28238192484115, 9.92260366972836, 9.16889318206201, 8.74830491237962, 8.81507308884446,
				9.76330552193627, 8.56883642456808, 8.92611897115338, 9.12891337328045, 9.7195647143752, 8.78124833323686, 8.48838210956212, 8.53640741034004,
				8.14409846333852, 9.10664513563742, 9.11569996782206, 9.68421151274841, 8.80011394676631, 8.54752839121231, 8.3221510702129, 8.09529377684465,
				8.92345797969497, 9.37974553683691, 8.90327158572421, 8.87556669199055, 8.44139147799996, 8.59674347017425, 9.00969189848934, 9.39897529082673,
				9.99392223000734, 9.06149227523977, 8.97119446318447, 8.94689552388845, 9.18696938565294, 9.0980671294934, 10.8781037947059, 9.38269576445829,
				9.19897604189713, 8.62119278143472, 8.61323037961318, 8.69517199877606, 8.72029728739272, 9.50031980347665, 9.34757739028127, 8.78370269863522,
				8.70217786562968, 8.6821990260005, 8.48363640788739, 8.40916244720253, 8.97309789628247, 9.55030649785165, 8.78630387828258, 8.60813018640834,
				8.49494758246892, 8.44870019497094, 8.19174002127746, 8.38091517312361, 9.07394774707063, 8.35608503102148, 8.3485378253861, 8.53503310954457,
				8.43489794868941, 8.5354259596773, 8.99168672593482, 9.77713036365961, 8.63887970967284, 8.28324144138542, 8.27333659850449, 8.15908865466791,
				7.91352101728389, 7.8407064517494, 8.02486215028641, 7.97143099776935, 8.47782846789396, 7.95787735848981, 8.02355239240435, 7.54908271081229,
				7.51969240411654, 7.87169266432365, 7.68156036255954, 7.73236922228439, 7.78239033558746, 7.65633716643018, 7.48324441607385, 7.59890045687141,
				7.78613643778307, 7.75061473277041, 7.76472054477148, 7.70481192293259, 7.6586995582683, 7.34729970074316, 7.54433210805369, 7.74716496652033,
				7.6889133368648, 7.77064523412918, 7.61184239958042, 7.48773376143644, 7.55747290161475, 7.56837926783652, 7.52563997504154, 7.66199755890189,
				7.41637847919293, 7.44366368311559, 7.31654817718298, 7.17472430983638, 7.24779258176785, 7.41397029019044, 7.52940645783701, 7.63964228785801,
				8.26975694753298, 8.33134542484572, 7.78447323573647, 7.48099216286952, 7.58781721999343, 7.67182679787878, 7.59739632021279, 7.67740043051481,
				7.48436864328613, 7.26122509197192, 7.39203156751459, 7.42833319419081, 7.55747290161475, 7.48885295573346, 7.42714413340862, 8.21527695893663,
				8.49371983523059, 8.14322675036744, 8.12177741916107, 8.20794694104862, 8.5197898172635, 8.57470709761684, 8.04782935745784, 7.48773376143644,
				7.56631101477246, 7.9976631270201, 8.00836557031292, 7.91498300584839, 7.8804263442924, 7.84893372636407, 7.50823877467866, 7.66058546170326,
				8.02747653086048, 8.10046489102936, 8.18088094199639, 8.33351070898294, 8.64100247714252, 8.46315930292375, 8.01201823915906, 7.79564653633459,
				7.70571282389443, 7.77863014732581, 7.81237820598861, 7.56164174558878, 7.26332961747684, 7.35564110297425, 7.47477218239787, 7.53422832627409,
				7.62997570702779, 7.62608275807238, 7.44483327389219, 7.11963563801764, 7.37023064180708, 7.54115245513631, 7.5137092478397, 7.59034694560257,
				7.54802896993501, 7.36833968631138, 7.11314210870709, 7.23705902612474, 7.48717369421374, 7.61233683716775, 8.21716859576607, 7.66669020008009,
				7.32646561384032, 7.09340462586877, 7.13966033596492, 7.40367029001237, 7.42595365707754, 7.43779512167193, 7.55328660560042, 7.32184971378836,
				7.10332206252611, 7.11069612297883, 7.48211892355212, 7.54591815120932, 7.32778053842163, 7.21964204013074, 7.36327958696304, 7.18841273649695,
				7.25063551189868, 7.4500795698075, 7.19743535409659, 7.41938058291869, 7.37963215260955, 7.40306109109009, 7.51588908521513, 7.63723438878947,
				7.52617891334615, 7.3185395485679, 7.38212436573751, 7.74975340627444, 7.67600993202889, 7.26612877955645, 7.58324752430336, 7.56682847920833,
				7.56008046502183, 7.63288550539513, 7.50052948539529, 7.42356844425917, 7.39018142822643, 7.21007962817079, 7.94555542825349, 7.5999019592085,
				7.56268124672188, 7.61184239958042, 8.99998964246073, 8.73793385811414, 8.26796230533871, 7.77904864492556, 7.57865685059476, 7.60539236481493,
				8.29179710504873, 7.61775957660851, 7.35883089834235, 7.68662133494462, 7.75362354655975, 7.77904864492556, 8.05706068196577, 7.8984110928116,
				7.79729127354747, 7.51534457118044, 7.48268182815465, 7.88683299895506, 8.39728289474368, 8.26770566476243, 8.23615566168312, 8.38890517111471,
				9.0788640091878, 9.63299030483845, 8.94780609305705, 8.53934599605737, 8.3478273457825, 8.32530602975258, 8.25556865328375, 8.09712193091871,
				8.13593277200489, 8.01895468315572, 8.0861025356691, 8.13329386122263, 8.04974629095219, 8.19063168090354, 8.04334217044161, 8.43814998407578,
				8.10741881171997, 8.16876982367527, 8.17470288246946, 8.29129585190541, 8.3059782109673, 8.42310226801664, 8.75621009188674, 8.72583205652757,
				8.70748291785937, 8.51077262361331, 8.70217786562968, 8.580543506917, 9.22542600939422, 8.41116578677071, 8.45190772471761, 8.77183540978982,
				8.23880116587155, 8.15708378502887, 8.03915739047324, 8.1185050675871, 9.18563775933581, 9.15239341202133, 9.02617712030286, 9.74226190403691,
				10.0828463914793, 9.11107237031751, 9.73878978049572, 9.614938437645, 9.34801317710126, 8.55429627936774, 8.44955654270043, 8.43054538469057,
				8.60538720215215, 9.219894584781, 9.50076867009599, 9.52230033688749, 8.47886807709457, 8.28349412616251, 8.26898820950666, 8.28324144138542,
				9.03013657115323, 10.2525586604481, 9.16659744902826, 8.27537637483641, 8.3466420902212, 8.42156296040099, 8.19450550976564, 8.87766093359367,
				8.9941724343984, 9.55676293945056, 8.4144957931779, 8.31139827843664, 8.365672383775, 8.14902386805177, 8.96826881077645, 8.88322423027899,
				8.70450228972123, 8.23297179059344, 8.17301131172497, 8.13446757027756, 8.83433697401764, 9.08975340898706, 9.0107912695156, 8.71751837264977,
				8.42200300441249, 8.20712916807133, 8.05484022110102, 8.83156587912106, 10.1827467519768, 8.80986280537906, 8.76013937002663, 8.88502565805085,
				8.56159277871292, 8.2495751500002, 9.35660287895444, 9.12750209366718, 8.79102985704596, 8.65347080970879, 8.74337213127397, 8.86742743852498,
				8.44009614103127, 9.37568530456302, 9.74102744483773, 8.83622857152601, 8.46104603079324, 8.21635833238616, 8.22844388300403, 8.0471895621705,
				9.07234187381889, 9.46761478200654, 8.98669669562029, 8.43923164994653, 8.42398080969406, 8.58802437217683, 8.25400859056484, 8.74512525946224,
				9.49514330367712, 8.72469504674049, 8.35960327084147, 8.76374072050946, 8.7279402223939, 8.38548870041881, 9.3130774494273, 9.3061958576197,
				9.84675845829004, 8.79679268767466, 8.64611397148308, 8.9398431242785, 8.84375938191798, 9.7005142080113, 9.53914039514886, 8.9082888855571,
				9.02183976410551, 9.10963566785455, 8.87164566750187, 8.38228942895144, 9.23229753932823, 9.85287823470959, 8.84707231256781, 8.53346016388011,
				8.58802437217683, 8.48549610467298, 8.18979961872823, 9.49687178267057, 9.46280968867222, 8.84347078162738, 8.36310917603352, 8.60575336839572,
				6.58617165485467, 7.63578686139558, 9.24879155835043, 8.88072457615146, 8.69617584694468, 8.45382731579442, 8.14467918344776, 9.07635173197287,
				10.2446985435045, 9.85828095969805, 9.18758338485357, 8.76248954737158, 8.5016733797582, 8.65521448931361, 10.0388921895423, 9.46436224293533,
				8.97309789628247, 8.95557714628151, 8.91945316857545, 8.72631895096224, 8.73921611506174, 10.26196586942, 10.5694947531438, 9.56120848888113,
				9.60400276796519, 10.0861007334703, 9.72841962445348, 9.41205597587677, 9.84357829978222, 11.5721750241742, 10.2817184876905, 10.1697672187275,
				9.68290322361684, 9.89550578279447, 9.37627844951961, 9.58217975243469, 10.3414521187349, 10.3339704236196, 10.2182252970113, 9.73406247747719,
				10.1874627630566, 9.88857693980037, 11.075086947327, 12.6735418157462, 10.9246967023573, 10.1815358690196, 9.86339445896968, 9.92573816147095,
				9.40153907670774, 9.33441468707811, 9.14750706280461, 8.91395385889425, 9.1801903950253, 9.05718919248201, 8.71275997496021, 8.40312823512826,
				8.29479935899257, 9.11591979635669, 8.95156964301882, 8.3513747067213, 8.65381978894806, 8.6429443967218, 8.71620797115185, 8.36497397843873,
				8.37378460812088, 8.51719319141624, 8.31825432879885, 8.39547743273214, 8.3228800217699, 8.24564690087386, 8.63194942871443, 8.31066090590723,
				8.43294163896865, 11.6448305358502, 11.3632879189498, 10.6929444132335, 10.3343929611261, 9.98608085083998, 10.2820952064744, 10.1943645158844,
				10.0954706196007, 10.1468650106811, 10.140888975597, 10.2095373998461, 10.033682134194, 11.0828346170357, 11.1744832892926, 10.7792895676801,
				9.9475044379529, 9.37602428761711, 8.99776577201121, 8.83287946027762, 8.89822898560123, 8.76467807411661, 8.54110501146255, 8.39615486303918,
				8.31238059678675, 8.34117174717076, 8.1300590399928, 8.35819745992578, 8.35561499576018, 8.18172045512811, 8.10952565975287, 8.06463647577422,
				7.82324569068552, 7.85476918349913, 8.10167774745457, 8.09040229659332, 7.98989937494294, 8.09894674894334, 8.65381978894806, 8.04109100370863,
				8.04974629095219, 8.22147894726719, 8.17075142375753, 8.3354314778808, 8.25660734462616, 8.05769419481559, 7.70796153183549, 7.91717198884578,
				8.2602342916073, 8.28803156777646, 8.36869318309779, 8.63355299253243, 9.27246974344173, 8.67556352738768, 8.48342956126343, 8.17188200612782,
				8.20658361432075, 8.18896686364888, 8.03073492409854, 8.05484022110102, 9.15514473650823, 8.83331693749932, 8.34972083747249, 8.18339736999843,
				7.95647679803678, 7.86940171257709, 7.70930833338587, 7.81923445385907, 7.83241092718792, 7.88683299895506, 8.03786623470962, 7.952615111651,
				7.76768727718691, 7.47816969415979, 7.539027055824, 7.99799931797973, 8.30967689598773, 8.02878116248715, 7.79028238070348, 7.76174498465891,
				7.47647238116391, 7.63964228785801, 7.65586401761606, 7.81963630236759, 7.81359155295243, 7.99057688174392, 7.81278281857758, 7.65775527113487,
				7.69439280262942, 7.84149292446001, 7.93343838762749, 7.6511201757027, 7.72356247227797, 7.88004820097158, 7.94093976232779, 7.75876054415766,
				7.63336964967958, 7.84854348245668, 7.89729647259588, 7.72223474470961, 7.71244383427499, 7.53955882930103, 7.91169052070834, 7.80139132029149,
				8.4013333053217, 8.18144069571937, 7.86288203464149, 7.92407232492342, 7.56682847920833, 7.51914995766982, 7.64873978895624, 7.77064523412918,
				7.60986220091355, 7.59186171488993, 7.539027055824, 7.34536484041687, 7.46336304552002, 7.51479976048867, 7.6425241342329, 7.67600993202889,
				7.55799495853081, 7.6889133368648, 7.60638738977265, 7.58222919427646, 7.74196789982069, 7.78239033558746, 8.00636756765025, 8.65102453904976,
				8.40357646462927, 8.3850322878139, 8.02812905943176, 7.95787735848981, 7.99530662029082, 7.99226864327075, 7.9359451033537, 7.98786409608569,
				7.78364059622125, 7.8087293067444, 8.0532511535491, 7.97590836016554, 8.12237124340655, 8.55986946569667, 8.9274468162562, 8.28576542051433,
				8.28399930424853, 8.16337131645991, 7.91425227874244, 7.86441990499457, 8.07215530818825, 8.0802374162167, 8.12088602109284, 8.11312710422178,
				8.14438886554762, 8.06463647577422, 7.944846711002, 8.24143968982973, 9.00736702745136, 8.66233195708248, 8.80056599227992, 8.3742461820963,
				8.56407677731509, 8.38434727808281, 8.12651816878071, 8.39072252736229, 8.3351915834332, 8.9278448262117, 9.0079793598445, 8.37816098272068,
				8.20330402679528, 8.38571682862785, 8.31115254800169, 8.74145611599836, 9.44295889365291, 9.14590851181679, 8.58951385299586, 8.46484671104403,
				8.36590507720246, 8.56541176368671, 8.4724050086261, 8.96648377906443, 8.56006109164341, 8.4690528160883, 8.74385056203024, 10.0138206842205,
				8.69114649853968, 8.78094111357239, 9.83900236330972, 11.614940390377, 9.62865589206317, 8.78293635634926, 8.68118104152169, 9.00097644407034,
				8.74623928838306, 9.8072519446553, 9.61266722758384, 10.5920994642943, 8.75542238014849, 8.56063574925907, 9.40516674990861, 8.45807992692373,
				9.54959444997195, 9.60602446822924, 8.67726913926287, 8.17103418920548, 8.24143968982973, 8.51097389160232, 8.32360844234357, 9.25922576970599,
				9.84966474583862, 8.83317113302287, 8.49780647761605, 8.63408694288774, 9.04227668692893, 8.55004752828718, 9.81809304951918, 9.9020865716205,
				8.91637191488169, 8.33206770728955, 8.23668532271246, 8.40178233990491, 8.24170315972982, 9.03562977818356, 9.10409057213347, 10.8321415433937,
				8.7787879291047, 8.48011418317482, 8.48941081040379, 8.25062008217469, 9.54344981789221, 9.09717167387054, 8.66939912430557, 8.34924780056679,
				8.34069464792507, 8.49474306257865, 8.53326337159373, 9.21979553074694, 10.4442990717924, 8.87696334026227, 8.68185981297147, 8.49821422481843,
				8.56845648535378, 8.45871626165726, 9.71818154670121, 9.68700923909068, 8.83010431791379, 8.38799525294456, 8.4984180360899, 8.84721610435754,
				8.28096440055337, 9.26492324974647, 9.11173476822206, 8.70682132339263, 8.33182700443606, 8.36660283278374, 8.27690348126706, 8.12946976478423,
				9.17915925449261, 9.68558026801716, 8.65521448931361, 8.29454951514368, 8.6522484224091, 8.92970011431345, 8.3959291039232, 9.46753746341524,
				9.88979422540413, 8.84922702143852, 8.61431990214696, 8.48156601377309, 8.74909824839902, 8.65364531455174, 9.3482745580655, 9.67683786189263,
				9.64290170574605, 8.72891172506098, 8.77894188184151, 9.96057651952026, 8.73777346032728, 9.25263328416643, 9.26624800391448, 9.42730487221368,
				8.79300509129753, 8.70300863746445, 8.43944784279138, 8.29104513108173, 9.31325790598287, 9.34792603492875, 8.791486026749, 8.51899157335762,
				8.41294317004244, 8.29679586577005, 8.21256839823415, 9.25655579577315, 9.65226597708712, 8.63746202380718, 8.60776488960062, 8.96533457380484,
				8.68372406230387, 8.53267276226462, 9.49016666846238, 10.142858720955, 9.11162439903702, 9.08500388066489, 9.05508908670489, 9.33626792857397,
				9.23960786965675, 10.1327324527083, 9.49122438992696, 9.1122864315008, 9.06357899058078, 8.97297111339799, 9.14548179962769, 10.5418617072488,
				11.5075208865114, 10.1931676276506, 9.27995971385624, 8.84635304331433, 8.73262709966039, 8.65504025810836, 8.45446636150793, 8.96367227561502,
				10.0210927946104, 9.00565049932022, 8.86092472971904, 8.58522560180806, 8.536211197252, 8.45850419506756, 8.53444354482276, 10.1042218823372,
				8.65067458279072, 8.51218064959269, 8.48549610467298, 8.57791192645094, 8.54985397365579, 9.60622641363735, 10.0261917925116, 8.87024156729927,
				8.52793528794814, 8.38343320123671, 8.20083725837985, 8.09285102753838, 8.03883475778775, 8.08641027532378, 8.03657340970731, 7.97522083865341,
				7.84267147497946, 7.8935720735049, 7.81762544305337, 7.82284529027977, 7.9672801789422, 8.00670084544037, 7.91132401896335, 7.85166117788927,
				7.87207397986687, 7.75362354655975, 7.68294316987829, 7.84384863815247, 8.19146305132693, 7.97831096986772, 7.92334821193015, 7.87131120332341,
				7.74370325817375, 7.77863014732581, 7.83518375526675, 7.83834331555712, 7.84619881549743, 7.92044650514261, 7.75790620835175, 7.58629630715272,
				7.51479976048867, 7.75790620835175, 7.80343505695217, 8.07899825868515, 8.38068594676157, 8.0643219609108, 7.85282781228174, 7.90396563403217,
				7.84463264446468, 7.88945914940452, 8.22550309756692, 8.54071438645758, 8.01928379291679, 7.83122021460429, 8.43315919580623, 8.09620827165004,
				7.86633892304654, 7.77904864492556, 7.77359446736019, 7.77275271646874, 7.76811037852599, 7.48099216286952, 7.74370325817375, 7.5963923040642,
				7.68063742756094, 7.53849499941346, 7.4500795698075, 7.44307837434852, 7.54855597916987, 7.64060382639363, 7.67647364638916, 7.56734567601324,
				8.07682603129881, 7.70120018085745, 7.36833968631138, 7.3664451483276, 7.48661331313996, 7.5740450053722, 7.568895663407, 7.63964228785801,
				7.85321638815607, 7.31188616407716, 7.53636393840451, 7.68248244653451, 7.73193072194849, 8.01201823915906, 7.98036576511125, 8.17131687471973,
				7.97796809312855, 7.79482315217939, 8.30424746507847, 8.05642676752298, 7.77779262633883, 8.22897764335831, 7.900636613018, 7.46164039220858,
				7.54908271081229, 7.81681996576455, 7.72223474470961, 7.71556953452021, 7.72621265050753, 7.58171964012531, 7.26542972325395, 7.30114780585603,
				7.47420480649612, 7.58882987830781, 7.61085279039525, 7.64778604544093, 7.60190195987517, 7.25417784645652, 7.1800698743028, 7.37713371283395,
				7.57507169950756, 7.50714107972761, 7.58578882173203, 7.40731771046942, 7.03085747611612, 7.15070145759253, 7.25417784645652, 7.45066079621154,
				7.55118686729615, 7.61332497954064, 7.42714413340862, 7.350516171834, 7.28824440102012, 7.434847875212, 7.53743003658651, 7.35244110024358,
				7.28207365809346, 7.3031700512368, 7.29369772060144, 7.22983877815125, 7.57507169950756, 7.97418866928601, 7.61579107203583, 7.47420480649612,
				7.33432935030054, 7.31920245876785, 7.40488757561612, 7.42476176182321, 7.47022413589997, 7.36770857237437, 7.2841348061952, 7.32580750259577,
				7.29097477814298, 7.19142933003638, 7.28961052145117, 7.33236920592906, 7.39572160860205, 7.56734567601324, 7.62119516280984, 7.26892012819372,
				7.26961674960817, 7.30787278076371, 7.28138566357028, 7.32118855673948, 7.51207124583547, 7.50052948539529, 7.11314210870709, 7.1420365747068,
				7.32383056620232, 7.42892719480227, 7.52886925664225, 7.41997992366183, 7.4730690880322, 7.3375877435386, 7.35436233042148, 7.58273848891441,
				7.62608275807238, 7.7596141506969, 7.94058382710424, 7.59085212368858, 7.41818082272679, 7.41155628781116, 7.59789795052178, 6.63594655568665,
				7.64730883235624, 7.82763954636642, 7.63385355968177, 8.53030683056162, 8.52951694110507, 7.85127199710988, 7.79564653633459, 7.58222919427646,
				7.43897159239586, 7.63867982387611, 7.52725591937378, 7.72488843932307, 7.91352101728389, 8.65956043270316, 8.29579811063615, 8.13241267450091,
				7.92551897978693, 7.82843635915759, 7.84424071814181, 7.77695440332244, 7.76684053708551, 7.78986855905471, 7.69393732550927, 7.71556953452021,
				8.15277405274407, 8.2529671950008, 7.94129557090653, 7.80954132465341, 7.81923445385907, 7.81237820598861, 7.54538974961182, 8.47428569040496,
				7.79193595693806, 7.66809370908241, 7.80547462527086, 7.9672801789422, 7.99429498641598, 7.80954132465341, 8.70317470904168, 7.9672801789422,
				8.09620827165004, 8.03786623470962, 8.58016799057763, 10.8718582692757, 9.19248185367487, 9.15069651904867, 9.82319898130729, 8.76888532613486,
				8.50855599802057, 8.72972059026726, 8.92145757894788, 8.52991196382401, 10.4159817834027, 10.3369892693381, 9.14644164612595, 8.50875771259514,
				8.38617292897783, 8.36100710822691, 8.12976444579417, 8.73198193834769, 8.73584667745758, 10.8196982812101, 10.6590929669357, 9.84945366404364,
				8.88820487145502, 8.92771217382708, 9.66738540005753, 10.1635029066262, 9.3379417165699, 9.17719715338293, 8.87905466204227, 8.57866451350434,
				8.73004395324502, 9.9533247873833, 10.2387447656008, 9.20311432688444, 8.74719318352693, 8.77554943448619, 9.2098402469345, 8.52813313145457,
				9.05765528431053, 9.42294862137501, 9.02917814290207, 9.09773142759353, 9.44809663565824, 9.11250701162742, 8.80267284031282, 9.20843856468659,
				11.0470891404358, 9.32758993202642, 8.67880170661265, 8.57659353469768, 8.43598313599069, 8.19007704971905, 9.06044728240157, 9.27030595314362,
				8.5016733797582, 8.18729927015515, 8.0959035329611, 8.04334217044161, 7.952615111651, 8.39908510293591, 8.79102985704596, 8.3030093814735,
				8.11910083763749, 8.23031079913502, 8.15765701519647, 7.82923253754359, 8.57395152523485, 9.61132880805727, 8.92385758009988, 8.3654396361887,
				8.31188955823036, 8.63141433550626, 8.45382731579442, 8.90585118120802, 10.8674821444793, 9.15514473650823, 8.43944784279138, 8.44354665124794,
				8.57262789830434, 8.372398606513, 8.73600738456922, 10.3885029394023, 8.70880479511728, 8.19533366716287, 8.22147894726719, 8.27512163021651,
				8.16990264735914, 8.82232217747174, 9.80543361206074, 9.38907215991958, 8.98130449495713, 8.57922858233569, 8.48776438072542, 8.72192834304709,
				8.9182485910357, 9.65162297294974, 8.86474666090541, 8.50936261230105, 8.63177109612367, 9.20271134481169, 8.90381521172292, 9.02653771890043,
				9.23766366762507, 8.89508153175417, 8.6429443967218, 8.12976444579417, 8.29179710504873, 8.09803475617607, 9.51878049751247, 9.90468683311161,
				8.93734984826739, 8.57885257180297, 8.71588010229646, 8.48899945704546, 8.50572771330696, 9.30008966411979, 10.1461591836579, 9.17709377818255,
				8.83564692253477, 8.83287946027762, 8.92305821954573, 8.89329814421792, 8.60263667323371, 8.99143781491923, 8.80687326653069, 8.85409390765552,
				8.93102321585603, 8.85280791762332, 10.6933076203563, 11.3075604350077, 9.83745458193169, 9.60508151672137, 9.74537068443899, 9.67564548044036,
				9.43468320386588, 11.5036223246441, 11.9767789709185, 10.5425744562461, 10.004282662571, 9.73281784848262, 9.86646043169905, 9.37092743662413,
				9.490544554572, 10.139152384404, 9.99984264077889, 10.0327159505439, 10.3803736928726, 10.453053004618, 10.2401383446439, 11.7605196483804,
				12.846746888829, 10.7668837086558, 9.84522264440415, 9.29035230994557, 9.10331179921766, 8.79573360595074, 8.62335338724463, 8.41825644355621,
				8.31090675716845, 8.23615566168312, 8.13123654969612, 7.92768504561578, 7.7591874385078, 7.72665366484764, 7.83518375526675, 7.88419993367604,
				7.91461770904068, 7.92551897978693, 7.75319426988434, 7.50878717063428, 7.55747290161475, 7.80261806344267, 7.68386398025643, 7.9844627322622,
				7.85166117788927, 7.68478394352278, 7.3375877435386, 7.40367029001237, 7.86787149039632, 7.8984110928116, 7.58426481838906, 7.71423114484909,
				7.88945914940452, 7.32580750259577, 7.48885295573346, 7.55381085200823, 7.66996199547358, 7.98820359702258, 8.00436556497957, 7.6511201757027,
				7.48661331313996, 7.44949800538285, 7.59538727885397, 7.60986220091355, 7.54802896993501, 7.61775957660851, 7.59538727885397, 7.34665516317654,
				7.40123126441302, 8.09315669772264, 7.92371033396924, 7.69074316354187, 8.43901541035221, 7.78239033558746, 7.30854279753919, 7.26192709270275,
				7.43720636687129, 7.54009032014532, 7.58528107863913, 7.60887062919126, 7.46450983463653, 7.15695636461564, 7.48773376143644, 7.4489161025442,
				7.47022413589997, 7.43602781635185, 7.52185925220163, 7.41034709782102, 7.15617663748062, 7.13807303404435, 7.36264527041782, 7.51697722460432,
				7.61726781362835, 7.49554194388426, 7.39203156751459, 7.17472430983638, 8.09132127353041, 7.51534457118044, 7.8458075026378, 7.69120009752286,
				7.83478810738819, 7.67740043051481, 7.24850407237061, 7.40245152081824, 7.69439280262942, 7.82604401351897, 7.61184239958042, 7.5137092478397,
				7.67600993202889, 7.24064969425547, 7.65539064482615, 8.13944052187461, 8.37493814383537, 7.90174751852014, 8.02387999273488, 8.75020786252571,
				8.081784206935, 7.70436116791031, 7.86825426552061, 7.81963630236759, 7.76089319585102, 7.66715825531915, 7.63433723562832, 7.35115822643069,
				7.58933582317062, 7.91022370709734, 7.85476918349913, 7.64683139143048, 7.49164547360513, 7.5234813125735, 7.0352685992811, 7.06561336359772,
				7.22983877815125, 7.50823877467866, 8.31164394850298, 8.01400499477946, 7.49720722320332, 7.83161727635261, 7.15148546390474, 7.41095187558364,
				7.63094658089046, 7.4759059693674, 7.58832367733522, 7.24636808010246, 7.10332206252611, 7.10414409298753, 7.1929342212158, 7.40123126441302,
				7.43955930913332, 7.70796153183549, 7.350516171834, 7.32251043399739, 7.28550654852279, 7.36770857237437, 7.26752542782817, 7.22475340576797,
				7.22256601882217, 7.35179986905778, 6.97541392745595, 7.04315991598834, 7.48099216286952, 7.20340552108309, 7.22402480828583, 7.11314210870709,
				7.07918439460967, 6.88550967003482, 6.87419849545329, 7.72312009226633, 7.49665243816828, 7.22402480828583, 7.27239839257005, 7.07918439460967,
				6.85435450225502, 6.93049476595163, 7.15617663748062, 7.31455283232408, 7.22693601849329, 7.65681009148038, 7.54009032014532, 7.21007962817079,
				7.24992553671799, 7.29437729928882, 7.51914995766982, 7.42237370098682, 8.22362717580548, 7.49276030092238, 7.22475340576797, 7.25629723969068,
				7.4489161025442, 7.65539064482615, 7.67136092319064, 7.92407232492342, 7.80098207125774, 7.45645455517621, 7.36264527041782, 8.21311069759668,
				8.23642052726539, 7.92153563213355, 7.74500280351584, 7.57814547241947, 7.56682847920833, 7.65822752616135, 8.27078101316267, 7.80302664363222,
				7.6226639513236, 7.70029520342012, 8.05864371221562, 7.64108424917491, 7.83636976054512, 8.37355374121463, 8.60940767540405, 8.17723488551019,
				8.03689677268507, 7.95331834656043, 7.7848892956551, 8.07371464110986, 8.28045768658256, 8.19918935907807, 8.00034949532468, 7.88720858581393,
				7.83715965000168, 7.97968130238774, 8.51839247199172, 8.35631996582815, 7.93236215433975, 7.83676478326407, 8.53719187792293, 8.02649693894541,
				7.9728107841214, 8.37447688921464, 8.25322764558177, 8.44591198941127, 8.49269555981584, 8.83913175254611, 8.07589363029886, 8.75020786252571,
				10.702412661625, 10.0599783492956, 8.79315687091382, 8.71440336070394, 9.05625635659347, 8.62155320674048, 9.96142621745657, 9.70856696016566,
				9.19644426678407, 8.61431990214696, 8.88903257187474, 9.01627006814768, 8.19918935907807, 9.16219999664825, 9.60750445504496, 8.44290058683438,
				8.15737044118677, 8.18451375303372, 8.83898679349679, 8.21283958467648, 8.33615081612066, 8.59044365315583, 8.70134640303916, 8.26642147298455,
				8.27461194620955, 8.36637030168165, 8.03527891114467, 9.23151460720759, 9.96467672084855, 8.84548923675327, 8.67299964255444, 8.40065937516029,
				8.58035576637388, 8.02059914989697, 9.1075321519945, 9.43835205468725, 8.50126704086598, 8.3133619511344, 8.3255483071614, 8.47637119689598,
				8.20111164444276, 8.70051424854327, 11.2744652095441, 9.60757167515724, 8.87863674743007, 8.76592651372944, 9.85639594500228, 8.43424627059531,
				8.8034242116007, 9.38176948760371, 8.76029622047005, 8.55506684384432, 8.46884293047519, 8.53129331579502, 8.04558828080353, 9.0902045707362,
				9.45414892373398, 9.0590522577624, 8.25945819533241, 8.18952211074809, 8.19533366716287, 7.69393732550927, 8.29004161870449, 9.03288694657909,
				8.38274709486331, 8.21797820315073, 8.12474302038557, 8.04686951095958, 7.57301725605255, 8.3986348552921, 8.71144331907547, 8.25114213909075,
				7.99226864327075, 8.00536706731666, 8.08085641964099, 7.52833176670725, 8.20248244657654, 9.07440609473535, 8.2147358333823, 7.96797317966293,
				8.12829017160705, 7.9536697786498, 7.66669020008009, 7.96554557312999, 9.14216859187285, 8.28702502516506, 8.28324144138542, 8.30102525383845,
				8.38799525294456, 7.70975686445416, 8.11102783819368, 8.74560285240295, 8.39140318535794, 8.11969625295725, 8.2358907259285, 8.10681603894705,
				7.71199650704767, 8.4252971767117, 8.84937050375457, 8.49310539588715, 8.17413934342947, 8.10228362448007, 7.8336002236611, 7.52294091807237,
				7.91022370709734, 8.3654396361887, 9.06056344665796, 8.17919979842309, 8.01631789850341, 8.10319175228579, 7.81439963380449, 8.38799525294456,
				8.74814616962193, 8.31287139434261, 7.92334821193015, 7.84658997529119, 8.3020178097512, 8.43620003220671, 8.93458687038968, 8.88861880730088,
				8.66423293406555, 8.50004703258127, 8.41825644355621, 8.4721958254855, 8.30721262662831, 9.88659568486591, 10.694985739443, 9.76019438270965,
				9.11007795003779, 8.79951090136887, 8.7830896717961, 8.42989086301344, 8.87877607170755, 9.75938620856187, 8.9520876435484, 8.66112036022288,
				8.58485183989005, 8.39660622842712, 7.92371033396924, 8.08548677210285, 8.35890061242164, 8.30350479887278, 8.27792025817214, 8.36357570275064,
				8.59822003005861, 8.08116577772543, 9.03443816698441, 10.2832245120716, 9.27322127001538, 8.71407489954152, 8.23350314023399, 7.88419993367604,
				7.81278281857758, 7.93128476152589, 8.4144957931779, 8.15651022607997, 7.85709386490249, 7.9098566672694, 7.80913539812054, 7.5076900778199,
				8.20385137218388, 7.82164312623998, 7.80384330353877, 7.76089319585102, 7.70345904786717, 8.06117135969092, 7.350516171834, 7.48380668766583,
				7.54062152865715, 7.69666708152646, 7.50384074669895, 7.39817409297047, 7.04228617193974, 7.05272104923232, 7.36264527041782, 7.6231530684769,
				7.79523492900217, 8.42683075133585, 7.9168074909376, 7.23633934275434, 7.20637729147225, 7.55642796944025, 7.58273848891441, 7.68294316987829,
				7.60688453121963, 7.70345904786717, 7.15148546390474, 7.04053639021596, 7.33888813383888, 7.48436864328613, 7.35179986905778, 7.42356844425917,
				7.29165620917446, 6.81673588059497, 6.91075078796194, 7.32118855673948, 7.99159228206809, 7.71289096149013, 7.28276117960559, 7.30586003268401,
				6.90575327631146, 7.87321705486274, 7.08590146436561, 7.27100853828099, 7.21376830811864, 7.16626597413364, 7.21303165983487, 6.82979373751242,
				6.80128303447162, 7.15773548424991, 7.04577657687951, 7.09174211509515, 7.23417717974985, 7.28000825288419, 6.69703424766648, 7.028201432058,
				7.17472430983638, 7.22329567956231, 7.33693691370762, 7.36201055125973, 7.26332961747684, 6.81124437860129, 7.3185395485679, 7.64012317269536,
				7.40549566319947, 7.57250298502038, 7.48549160803075, 7.33302301438648, 7.20563517641036, 7.09090982207998, 7.32646561384032, 7.35564110297425,
				7.46336304552002, 7.56734567601324, 7.74975340627444, 7.14440718032114, 6.89972310728487, 7.33106030521863, 7.2211050981825, 7.7376162828579,
				7.36327958696304, 7.29097477814298, 7.03878354138854, 6.93244789157251, 7.30586003268401, 7.68616230349291, 7.47929963778283, 7.26961674960817,
				7.30921236569276, 6.74051935960622, 6.80572255341699, 7.14282740116162, 7.18690102041163, 7.14045304310116, 8.76155013912964, 8.27944348771267,
				7.43720636687129, 7.16006920759613, 7.04141166379481, 7.17472430983638, 7.41276401742656, 7.25629723969068, 7.2848209125686, 6.72623340235875,
				6.93244789157251, 7.07411681619736, 7.20266119652324, 7.16472037877186, 7.06731984865348, 6.90675477864855, 6.67708346124714, 6.51767127291227,
				7.04315991598834, 7.04315991598834, 6.9177056098353, 7.05789793741186, 6.85540879860993, 6.64378973314767, 6.52502965784346, 6.89060912014717,
				6.92657703322272, 7.00488198971286, 6.87316383421252, 6.89060912014717, 6.4425401664682, 6.8351845861473, 7.27239839257005, 7.07749805356923,
				7.34407285057307, 7.29165620917446, 7.27170370688737, 7.454719949364, 6.69208374250663, 6.96318998587024, 7.01660968389422, 6.79122146272619,
				6.82001636467413, 6.61873898351722, 6.47389069635227, 6.49978704065585, 6.8596149036542, 6.88141130364254, 6.99759598298193, 7.15226885603254,
				7.19668657083435, 6.70808408385307, 6.98286275146894, 7.12849594568004, 7.08924315502751, 7.19893124068817, 8.090708716084, 7.39387829010776,
				7.05012252026906, 7.19518732017871, 7.44132038971762, 7.41758040241454, 7.47420480649612, 7.39264752072162, 7.18538701558042, 6.86484777797086,
				6.83410873881384, 7.28756064030972, 7.22402480828583, 7.24422751560335, 7.30653139893951, 7.31721240835984, 6.95844839329766, 6.82546003625531,
				7.18159194461187, 7.27655640271871, 7.27100853828099, 7.82444593087762, 7.41938058291869, 7.00760061395185, 7.07326971745971, 7.26542972325395,
				7.60240133566582, 7.55747290161475, 7.58222919427646, 7.28961052145117, 7.3304052118444, 7.2211050981825, 7.4312996751559, 7.75018416225784,
				7.62997570702779, 7.73061406606374, 7.82404601085629, 7.22620901010067, 7.4770384723197, 7.86326672400957, 7.94909149983052, 7.7698009960039,
				7.57967882309046, 7.54908271081229, 7.29165620917446, 7.78986855905471, 7.65681009148038, 7.53476265703754, 7.66528471847135, 8.06652149046999,
				8.14931284363534, 7.72533003791713, 7.71735127218533, 7.67786350067821, 7.89506349809157, 8.07992777075827, 8.1934002319521, 8.66509582133973,
				7.84463264446468, 8.77909581088053, 9.05870319731322, 8.42178300661158, 8.07215530818825, 8.40469616018909, 9.72184576464693, 8.11402544235676,
				8.43076346341785, 8.54888563814873, 8.3228800217699, 8.00836557031292, 8.11999382772511, 8.58260632996447, 7.60986220091355, 8.41205487329293,
				9.5410100922274, 8.55948610360649, 8.14438886554762, 7.9912539298402, 7.88532923927319, 7.4599147662411, 8.37516869138682, 8.7268056084461,
				8.07527154629746, 7.80057265467065, 7.74975340627444, 7.91971976092457, 7.35627987655075, 8.17301131172497, 8.26100978602383, 7.84658997529119,
				7.74022952476318, 7.83042561782033, 7.36137542897735, 8.2987883944492, 8.7417757069247, 8.39705739017626, 7.77821147451249, 7.93379687481541,
				7.86018505747217, 7.94236223767433, 8.19808924895612, 8.42901750051251, 8.05674377497531, 7.81116338502528, 7.78655180642871, 8.1086232683546,
				7.62119516280984, 8.09285102753838, 9.39224517527379, 8.45318786144033, 8.09437844497296, 7.9912539298402, 8.32820949174873, 7.64108424917491,
				8.48632152774915, 9.16356318041725, 8.18841130807903, 7.82644313545601, 7.96067260838812, 7.67229245562876, 7.16317239084664, 7.90211754627645,
				9.63430006272051, 8.84822206837138, 8.38320455141292, 8.16451026874704, 8.05293303679757, 7.56112158953024, 8.25634777291802, 8.67282848294769,
				8.30647216010058, 8.05896001776942, 7.87245515006398, 8.19533366716287, 7.59135704669855, 8.02158453345511, 12.1496715918794, 11.5230440984914,
				8.71177264560569, 8.05610965954506, 8.08147504013705, 7.45876269238096, 8.01400499477946, 8.49678638163858, 7.98104975966596, 7.77779262633883,
				8.2602342916073, 7.86633892304654, 7.31055015853442, 7.71824095195932, 8.31947369244219, 8.23668532271246, 7.80751004221619, 7.59186171488993,
				7.52886925664225, 7.17165682276851, 7.89133075766189, 8.36007143564403, 8.11042723757502, 7.77527584648686, 7.34729970074316, 7.30182234213793,
				7.12044437239249, 8.87877607170755, 9.25061821847475, 9.24792513230345, 8.39140318535794, 8.00469951054955, 7.58933582317062, 7.82524529143177,
				8.24931374626064, 9.29514097366865, 8.56826646160024, 8.35255436947459, 8.29579811063615, 8.29029259122431, 7.78572089653462, 8.28172399041139,
				8.4707303170059, 8.13505390861157, 8.06714903991011, 8.02355239240435, 8.02191277898571, 7.81722278550817, 9.27387839278017, 10.3337753460756,
				9.12587121534973, 8.89137400948464, };
	}

	private Integer[] getIntegerArrayForProphet() {
		return new Integer[] { 9, 8, 8, 8, 7, 7, 8, 8, 8, 8, 7, 7, 7, 8, 8, 7, 7, 8, 7, 7, 9, 9, 8, 8, 8, 8, 7, 9, 8, 8, 8, 8, 8, 8, 9, 10, 8, 8, 8, 8, 8, 8, 10, 9, 8,
				8, 8, 8, 8, 8, 8, 8, 8, 8, 9, 12, 10, 9, 9, 8, 8, 8, 8, 8, 8, 8, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 8, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
				7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 6, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 8, 7, 7, 7, 7, 7, 7, 7, 7, 7, 8, 8, 8, 7, 7, 8, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
				7, 7, 7, 7, 7, 7, 7, 7, 6, 6, 7, 7, 7, 7, 7, 6, 6, 6, 7, 7, 7, 7, 6, 7, 7, 7, 7, 7, 6, 6, 7, 7, 7, 7, 6, 6, 7, 7, 7, 7, 7, 7, 6, 6, 7, 7, 7, 7, 7, 6, 6,
				7, 7, 7, 6, 6, 7, 7, 7, 7, 7, 7, 7, 7, 7, 8, 7, 7, 8, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 8, 7, 7, 7, 7, 7, 7, 7, 8, 8, 7, 8, 9, 8,
				7, 7, 7, 7, 8, 8, 8, 7, 7, 7, 7, 8, 8, 8, 7, 7, 7, 7, 7, 8, 7, 7, 7, 7, 7, 8, 8, 7, 7, 7, 7, 7, 8, 8, 8, 7, 7, 7, 7, 8, 8, 7, 7, 7, 8, 8, 8, 7, 7, 7, 7,
				7, 9, 8, 7, 7, 7, 7, 8, 8, 7, 7, 7, 7, 7, 8, 8, 7, 7, 7, 7, 7, 7, 9, 8, 7, 7, 7, 7, 8, 8, 7, 7, 7, 7, 7, 8, 8, 7, 7, 7, 7, 7, 8, 8, 7, 8, 7, 8, 7, 7, 8,
				7, 7, 7, 7, 7, 8, 8, 8, 8, 7, 8, 9, 10, 9, 8, 8, 8, 8, 8, 9, 8, 8, 8, 7, 7, 7, 8, 8, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 8, 9, 8, 8, 8, 8, 7, 8, 8, 7,
				7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
				7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 6, 6, 7, 7, 7, 7, 7, 7,
				6, 6, 7, 7, 7, 7, 6, 6, 7, 7, 7, 7, 7, 7, 6, 7, 7, 7, 7, 7, 6, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 6, 6, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
				7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 8, 8, 7, 7, 7, 7, 7, 7, 7, 8, 7, 7, 7, 8, 8, 7, 8, 7, 7, 7, 8, 7, 7, 8, 7, 7, 8, 8, 7,
				7, 7, 7, 8, 7, 8, 7, 8, 8, 8, 8, 8, 8, 8, 8, 8, 7, 8, 9, 10, 8, 9, 8, 8, 8, 8, 7, 9, 9, 9, 8, 8, 8, 8, 8, 10, 9, 8, 9, 8, 8, 8, 8, 8, 8, 8, 9, 8, 8, 8,
				8, 7, 8, 8, 8, 8, 8, 8, 7, 8, 9, 8, 8, 8, 8, 8, 10, 9, 8, 8, 8, 8, 9, 8, 8, 8, 8, 8, 9, 9, 9, 8, 8, 8, 8, 9, 9, 8, 8, 8, 8, 8, 9, 9, 8, 8, 8, 9, 8, 9,
				9, 9, 8, 8, 8, 8, 9, 9, 9, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 9, 9, 8, 8, 8, 8, 8, 11, 9, 9, 9, 8, 9, 11, 10, 9, 9, 9, 9, 9, 10, 9, 10, 9, 10, 10, 11, 10,
				9, 9, 8, 8, 8, 8, 8, 8, 8, 7, 7, 7, 7, 8, 7, 7, 7, 7, 7, 7, 7, 8, 8, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 8, 7, 7, 7, 7, 7, 7, 7, 7, 7,
				7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 8, 8, 8, 7, 8, 8, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
				7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 9, 7, 7, 6, 5, 5, 5, 6, 6, 7, 7, 7, 7, 7, 7, 7, 6, 6, 6, 7, 7, 7, 7, 6, 6, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
				7, 8, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 8, 8, 8, 8, 8, 7, 7, 8, 7, 7, 7, 8, 7, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8,
				9, 8, 9, 9, 8, 8, 8, 8, 9, 9, 11, 9, 8, 8, 8, 8, 9, 9, 8, 8, 8, 8, 8, 9, 9, 9, 8, 8, 8, 8, 9, 8, 9, 8, 8, 8, 8, 9, 10, 8, 8, 8, 8, 8, 8, 8, 9, 8, 8, 8,
				8, 9, 9, 10, 8, 8, 8, 8, 9, 9, 8, 8, 8, 8, 8, 8, 9, 8, 8, 8, 8, 8, 9, 10, 8, 8, 8, 8, 8, 9, 10, 8, 8, 8, 8, 8, 9, 9, 9, 8, 8, 9, 8, 8, 9, 9, 8, 8, 8, 8,
				9, 9, 9, 8, 8, 8, 8, 8, 9, 8, 8, 8, 8, 9, 9, 9, 9, 8, 8, 9, 9, 10, 9, 9, 8, 8, 8, 8, 9, 9, 8, 8, 8, 8, 8, 8, 9, 8, 8, 8, 8, 8, 8, 9, 8, 8, 8, 8, 8, 8,
				9, 8, 8, 8, 8, 7, 7, 8, 7, 8, 7, 8, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 8, 8, 7, 7, 7, 7,
				7, 7, 7, 7, 7, 7, 7, 7, 7, 8, 8, 8, 8, 8, 8, 8, 8, 7, 7, 7, 8, 7, 7, 7, 7, 7, 8, 8, 8, 8, 8, 8, 8, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
				7, 7, 7, 7, 7, 8, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 8, 8,
				8, 7, 7, 7, 8, 7, 7, 7, 7, 7, 8, 7, 7, 7, 7, 7, 8, 8, 8, 8, 9, 9, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 9, 8, 8,
				8, 8, 8, 8, 8, 9, 9, 9, 9, 10, 9, 9, 9, 9, 8, 8, 8, 8, 9, 9, 9, 8, 8, 8, 8, 9, 10, 9, 8, 8, 8, 8, 8, 8, 9, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 9, 9, 8, 8,
				8, 8, 8, 10, 8, 8, 8, 8, 8, 9, 9, 8, 8, 8, 8, 8, 9, 9, 8, 8, 8, 8, 8, 9, 9, 8, 8, 8, 8, 8, 8, 9, 8, 8, 8, 8, 8, 9, 9, 9, 8, 8, 8, 8, 9, 9, 8, 9, 9, 8,
				8, 9, 9, 8, 8, 8, 8, 8, 9, 9, 8, 8, 8, 6, 7, 9, 8, 8, 8, 8, 9, 10, 9, 9, 8, 8, 8, 10, 9, 8, 8, 8, 8, 8, 10, 10, 9, 9, 10, 9, 9, 9, 11, 10, 10, 9, 9, 9,
				9, 10, 10, 10, 9, 10, 9, 11, 12, 10, 10, 9, 9, 9, 9, 9, 8, 9, 9, 8, 8, 8, 9, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 11, 11, 10, 10, 9, 10, 10, 10,
				10, 10, 10, 10, 11, 11, 10, 9, 9, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 7, 7, 8, 8, 7, 8, 8, 8, 8, 8, 8, 8, 8, 8, 7, 7, 8, 8, 8, 8, 9, 8, 8, 8, 8,
				8, 8, 8, 9, 8, 8, 8, 7, 7, 7, 7, 7, 7, 8, 7, 7, 7, 7, 7, 8, 8, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 8, 8, 7, 7,
				7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 8, 8, 8, 8, 8, 7, 7, 7, 7, 7, 7, 7, 8, 7, 8, 8, 8, 8, 8, 8, 7, 7, 8, 8, 8, 8, 8, 8, 7, 8, 9, 8, 8,
				8, 8, 8, 8, 8, 8, 8, 9, 8, 8, 8, 8, 8, 9, 9, 8, 8, 8, 8, 8, 8, 8, 8, 8, 10, 8, 8, 9, 11, 9, 8, 8, 9, 8, 9, 9, 10, 8, 8, 9, 8, 9, 9, 8, 8, 8, 8, 8, 9, 9,
				8, 8, 8, 9, 8, 9, 9, 8, 8, 8, 8, 8, 9, 9, 10, 8, 8, 8, 8, 9, 9, 8, 8, 8, 8, 8, 9, 10, 8, 8, 8, 8, 8, 9, 9, 8, 8, 8, 8, 8, 9, 9, 8, 8, 8, 8, 8, 9, 9, 8,
				8, 8, 8, 8, 9, 9, 8, 8, 8, 8, 8, 9, 9, 9, 8, 8, 9, 8, 9, 9, 9, 8, 8, 8, 8, 9, 9, 8, 8, 8, 8, 8, 9, 9, 8, 8, 8, 8, 8, 9, 10, 9, 9, 9, 9, 9, 10, 9, 9, 9,
				8, 9, 10, 11, 10, 9, 8, 8, 8, 8, 8, 10, 9, 8, 8, 8, 8, 8, 10, 8, 8, 8, 8, 8, 9, 10, 8, 8, 8, 8, 8, 8, 8, 8, 7, 7, 7, 7, 7, 7, 8, 7, 7, 7, 7, 7, 7, 8, 7,
				7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 8, 8, 8, 7, 7, 7, 7, 8, 8, 8, 7, 8, 8, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 8, 7, 7, 7, 7, 7, 7, 7, 7,
				7, 7, 7, 7, 8, 7, 8, 7, 7, 8, 8, 7, 8, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
				7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 6, 7, 7, 7, 8, 8, 7, 7,
				7, 7, 7, 7, 7, 7, 8, 8, 8, 7, 7, 7, 7, 7, 7, 7, 7, 8, 8, 7, 7, 7, 7, 7, 8, 7, 7, 7, 7, 7, 7, 8, 7, 8, 8, 8, 10, 9, 9, 9, 8, 8, 8, 8, 8, 10, 10, 9, 8, 8,
				8, 8, 8, 8, 10, 10, 9, 8, 8, 9, 10, 9, 9, 8, 8, 8, 9, 10, 9, 8, 8, 9, 8, 9, 9, 9, 9, 9, 9, 8, 9, 11, 9, 8, 8, 8, 8, 9, 9, 8, 8, 8, 8, 7, 8, 8, 8, 8, 8,
				8, 7, 8, 9, 8, 8, 8, 8, 8, 8, 10, 9, 8, 8, 8, 8, 8, 10, 8, 8, 8, 8, 8, 8, 9, 9, 8, 8, 8, 8, 8, 9, 8, 8, 8, 9, 8, 9, 9, 8, 8, 8, 8, 8, 9, 9, 8, 8, 8, 8,
				8, 9, 10, 9, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 10, 11, 9, 9, 9, 9, 9, 11, 11, 10, 10, 9, 9, 9, 9, 10, 9, 10, 10, 10, 10, 11, 12, 10, 9, 9, 9, 8, 8, 8, 8, 8,
				8, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 8, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 8, 7, 7, 8, 7, 7, 7, 7, 7, 7, 7, 7,
				7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 8, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 8, 8, 7, 8, 8, 8, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
				7, 7, 8, 8, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 6, 7, 7, 7, 7, 7, 7, 6, 6, 7, 7, 7, 7, 7, 6, 6, 7, 7, 7, 7, 7, 7, 7, 7, 7,
				7, 8, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 8, 8, 7, 7, 7, 7, 7, 8, 7, 7, 7, 8, 7, 7, 8, 8, 8, 8, 7, 7, 8, 8, 8, 8, 7, 7, 7, 8, 8, 7, 7, 8, 8, 7, 8, 8, 8, 8, 8,
				8, 8, 10, 10, 8, 8, 9, 8, 9, 9, 9, 8, 8, 9, 8, 9, 9, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 9, 9, 8, 8, 8, 8, 8, 9, 9, 8, 8, 8, 8, 8, 8, 11, 9, 8, 8, 9, 8,
				8, 9, 8, 8, 8, 8, 8, 9, 9, 9, 8, 8, 8, 7, 8, 9, 8, 8, 8, 8, 7, 8, 8, 8, 7, 8, 8, 7, 8, 9, 8, 7, 8, 7, 7, 7, 9, 8, 8, 8, 8, 7, 8, 8, 8, 8, 8, 8, 7, 8, 8,
				8, 8, 8, 7, 7, 7, 8, 9, 8, 8, 8, 7, 8, 8, 8, 7, 7, 8, 8, 8, 8, 8, 8, 8, 8, 8, 9, 10, 9, 9, 8, 8, 8, 8, 9, 8, 8, 8, 8, 7, 8, 8, 8, 8, 8, 8, 8, 9, 10, 9,
				8, 8, 7, 7, 7, 8, 8, 7, 7, 7, 7, 8, 7, 7, 7, 7, 8, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 8, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 6, 6, 7, 7, 7, 7, 7,
				6, 7, 7, 7, 7, 7, 7, 6, 6, 7, 7, 7, 7, 7, 6, 7, 7, 7, 7, 7, 7, 6, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 6, 7, 7, 7, 7, 7, 7, 6, 7, 7, 7, 7, 7, 6, 6,
				7, 7, 7, 8, 8, 7, 7, 7, 7, 7, 7, 7, 6, 6, 7, 7, 7, 7, 6, 6, 6, 7, 7, 6, 7, 6, 6, 6, 6, 6, 7, 6, 6, 6, 6, 7, 7, 7, 7, 7, 7, 6, 6, 7, 6, 6, 6, 6, 6, 6, 6,
				6, 7, 7, 6, 6, 7, 7, 7, 8, 7, 7, 7, 7, 7, 7, 7, 7, 6, 6, 7, 7, 7, 7, 7, 6, 6, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
				7, 7, 7, 7, 7, 7, 8, 8, 7, 7, 7, 7, 8, 8, 8, 7, 8, 9, 8, 8, 8, 9, 8, 8, 8, 8, 8, 8, 8, 7, 8, 9, 8, 8, 7, 7, 7, 8, 8, 8, 7, 7, 7, 7, 8, 8, 7, 7, 7, 7, 8,
				8, 8, 7, 7, 7, 7, 8, 8, 8, 7, 7, 8, 7, 8, 9, 8, 8, 7, 8, 7, 8, 9, 8, 7, 7, 7, 7, 7, 9, 8, 8, 8, 8, 7, 8, 8, 8, 8, 7, 8, 7, 8, 12, 11, 8, 8, 8, 7, 8, 8,
				7, 7, 8, 7, 7, 7, 8, 8, 7, 7, 7, 7, 7, 8, 8, 7, 7, 7, 7, 8, 9, 9, 8, 8, 7, 7, 8, 9, 8, 8, 8, 8, 7, 8, 8, 8, 8, 8, 8, 7, 9, 10, 9, 8, };
	}

	private String[] getAreaArrayForMarketMatching() {
		return new String[] { "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO",
				"SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "SFO", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK",
				"JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "JFK", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX", "LAX",
				"LAX", "LAX", "LAX", "LAX", "LAX", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR",
				"LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "LHR", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA", "MIA",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC",
				"SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "SAC", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH",
				"CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "CPH", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD", "SYD",
				"SYD", "SYD", "SYD", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL",
				"ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "ATL", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR",
				"STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "STR", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND", "HND",
				"HND", "HND", "HND", "HND", "HND", "HND", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH",
				"GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "GOH", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS", "BOS",
				"BOS", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK",
				"PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "PEK", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH",
				"ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "ZRH", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST", "IST",
				"IST", "IST", "IST", "IST", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM",
				"BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BOM", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK",
				"BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "BKK", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER", "PER",
				"PER", "PER", "PER", "PER", "PER", "PER", "PER", };
	}

	private String[] getDateArrayForMarketMatching() {
		return new String[] { "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04", "2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09",
				"2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14", "2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19",
				"2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24", "2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29",
				"2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03", "2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08",
				"2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13", "2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18",
				"2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23", "2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28",
				"2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05", "2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10",
				"2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15", "2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20",
				"2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25", "2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30",
				"2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04", "2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09",
				"2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14", "2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19",
				"2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24", "2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29",
				"2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04", "2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09",
				"2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14", "2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19",
				"2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24", "2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29",
				"2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03", "2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08",
				"2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13", "2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18",
				"2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23", "2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28",
				"2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03", "2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08",
				"2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13", "2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18",
				"2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23", "2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28",
				"2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02", "2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07",
				"2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12", "2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17",
				"2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22", "2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27",
				"2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01", "2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06",
				"2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11", "2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16",
				"2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21", "2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26",
				"2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01", "2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06",
				"2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11", "2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16",
				"2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21", "2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26",
				"2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31", "2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05",
				"2014-11-06", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10", "2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15",
				"2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-20", "2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25",
				"2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-11-30", "2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05",
				"2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10", "2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15",
				"2014-12-16", "2014-12-17", "2014-12-18", "2014-12-19", "2014-12-20", "2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25",
				"2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30", "2014-12-31", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04",
				"2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09", "2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14",
				"2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19", "2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24",
				"2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29", "2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03",
				"2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08", "2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13",
				"2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18", "2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23",
				"2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28", "2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05",
				"2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10", "2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15",
				"2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20", "2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25",
				"2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30", "2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04",
				"2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09", "2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14",
				"2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19", "2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24",
				"2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29", "2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04",
				"2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09", "2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14",
				"2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19", "2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24",
				"2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29", "2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03",
				"2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08", "2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13",
				"2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18", "2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23",
				"2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28", "2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03",
				"2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08", "2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13",
				"2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18", "2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23",
				"2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28", "2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02",
				"2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07", "2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12",
				"2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17", "2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22",
				"2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27", "2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01",
				"2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06", "2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11",
				"2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16", "2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21",
				"2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26", "2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01",
				"2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06", "2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11",
				"2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16", "2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21",
				"2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26", "2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31",
				"2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05", "2014-11-06", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10",
				"2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15", "2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-20",
				"2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25", "2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-11-30",
				"2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05", "2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10",
				"2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15", "2014-12-16", "2014-12-17", "2014-12-18", "2014-12-19", "2014-12-20",
				"2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25", "2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30",
				"2014-12-31", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04", "2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09",
				"2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14", "2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19",
				"2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24", "2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29",
				"2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03", "2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08",
				"2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13", "2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18",
				"2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23", "2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28",
				"2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05", "2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10",
				"2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15", "2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20",
				"2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25", "2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30",
				"2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04", "2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09",
				"2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14", "2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19",
				"2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24", "2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29",
				"2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04", "2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09",
				"2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14", "2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19",
				"2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24", "2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29",
				"2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03", "2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08",
				"2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13", "2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18",
				"2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23", "2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28",
				"2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03", "2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08",
				"2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13", "2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18",
				"2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23", "2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28",
				"2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02", "2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07",
				"2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12", "2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17",
				"2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22", "2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27",
				"2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01", "2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06",
				"2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11", "2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16",
				"2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21", "2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26",
				"2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01", "2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06",
				"2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11", "2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16",
				"2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21", "2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26",
				"2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31", "2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05",
				"2014-11-06", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10", "2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15",
				"2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-20", "2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25",
				"2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-11-30", "2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05",
				"2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10", "2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15",
				"2014-12-16", "2014-12-17", "2014-12-18", "2014-12-19", "2014-12-20", "2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25",
				"2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30", "2014-12-31", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04",
				"2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09", "2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14",
				"2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19", "2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24",
				"2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29", "2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03",
				"2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08", "2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13",
				"2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18", "2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23",
				"2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28", "2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05",
				"2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10", "2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15",
				"2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20", "2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25",
				"2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30", "2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04",
				"2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09", "2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14",
				"2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19", "2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24",
				"2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29", "2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04",
				"2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09", "2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14",
				"2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19", "2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24",
				"2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29", "2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03",
				"2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08", "2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13",
				"2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18", "2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23",
				"2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28", "2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03",
				"2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08", "2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13",
				"2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18", "2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23",
				"2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28", "2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02",
				"2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07", "2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12",
				"2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17", "2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22",
				"2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27", "2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01",
				"2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06", "2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11",
				"2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16", "2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21",
				"2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26", "2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01",
				"2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06", "2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11",
				"2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16", "2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21",
				"2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26", "2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31",
				"2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05", "2014-11-06", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10",
				"2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15", "2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-20",
				"2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25", "2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-11-30",
				"2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05", "2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10",
				"2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15", "2014-12-16", "2014-12-17", "2014-12-18", "2014-12-19", "2014-12-20",
				"2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25", "2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30",
				"2014-12-31", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04", "2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09",
				"2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14", "2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19",
				"2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24", "2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29",
				"2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03", "2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08",
				"2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13", "2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18",
				"2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23", "2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28",
				"2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05", "2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10",
				"2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15", "2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20",
				"2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25", "2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30",
				"2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04", "2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09",
				"2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14", "2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19",
				"2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24", "2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29",
				"2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04", "2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09",
				"2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14", "2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19",
				"2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24", "2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29",
				"2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03", "2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08",
				"2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13", "2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18",
				"2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23", "2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28",
				"2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03", "2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08",
				"2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13", "2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18",
				"2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23", "2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28",
				"2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02", "2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07",
				"2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12", "2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17",
				"2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22", "2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27",
				"2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01", "2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06",
				"2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11", "2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16",
				"2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21", "2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26",
				"2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01", "2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06",
				"2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11", "2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16",
				"2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21", "2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26",
				"2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31", "2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05",
				"2014-11-06", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10", "2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15",
				"2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-20", "2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25",
				"2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-11-30", "2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05",
				"2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10", "2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15",
				"2014-12-16", "2014-12-17", "2014-12-18", "2014-12-19", "2014-12-20", "2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25",
				"2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30", "2014-12-31", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04",
				"2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09", "2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14",
				"2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19", "2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24",
				"2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29", "2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03",
				"2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08", "2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13",
				"2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18", "2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23",
				"2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28", "2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05",
				"2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10", "2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15",
				"2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20", "2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25",
				"2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30", "2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04",
				"2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09", "2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14",
				"2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19", "2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24",
				"2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29", "2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04",
				"2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09", "2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14",
				"2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19", "2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24",
				"2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29", "2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03",
				"2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08", "2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13",
				"2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18", "2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23",
				"2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28", "2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03",
				"2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08", "2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13",
				"2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18", "2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23",
				"2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28", "2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02",
				"2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07", "2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12",
				"2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17", "2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22",
				"2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27", "2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01",
				"2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06", "2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11",
				"2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16", "2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21",
				"2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26", "2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01",
				"2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06", "2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11",
				"2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16", "2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21",
				"2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26", "2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31",
				"2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05", "2014-11-06", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10",
				"2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15", "2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-20",
				"2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25", "2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-11-30",
				"2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05", "2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10",
				"2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15", "2014-12-16", "2014-12-17", "2014-12-18", "2014-12-19", "2014-12-20",
				"2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25", "2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30",
				"2014-12-31", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04", "2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09",
				"2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14", "2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19",
				"2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24", "2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29",
				"2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03", "2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08",
				"2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13", "2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18",
				"2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23", "2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28",
				"2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05", "2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10",
				"2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15", "2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20",
				"2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25", "2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30",
				"2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04", "2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09",
				"2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14", "2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19",
				"2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24", "2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29",
				"2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04", "2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09",
				"2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14", "2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19",
				"2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24", "2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29",
				"2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03", "2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08",
				"2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13", "2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18",
				"2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23", "2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28",
				"2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03", "2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08",
				"2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13", "2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18",
				"2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23", "2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28",
				"2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02", "2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07",
				"2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12", "2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17",
				"2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22", "2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27",
				"2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01", "2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06",
				"2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11", "2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16",
				"2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21", "2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26",
				"2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01", "2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06",
				"2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11", "2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16",
				"2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21", "2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26",
				"2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31", "2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05",
				"2014-11-06", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10", "2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15",
				"2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-20", "2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25",
				"2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-11-30", "2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05",
				"2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10", "2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15",
				"2014-12-16", "2014-12-17", "2014-12-18", "2014-12-19", "2014-12-20", "2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25",
				"2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30", "2014-12-31", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04",
				"2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09", "2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14",
				"2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19", "2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24",
				"2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29", "2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03",
				"2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08", "2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13",
				"2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18", "2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23",
				"2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28", "2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05",
				"2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10", "2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15",
				"2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20", "2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25",
				"2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30", "2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04",
				"2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09", "2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14",
				"2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19", "2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24",
				"2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29", "2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04",
				"2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09", "2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14",
				"2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19", "2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24",
				"2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29", "2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03",
				"2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08", "2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13",
				"2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18", "2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23",
				"2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28", "2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03",
				"2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08", "2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13",
				"2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18", "2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23",
				"2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28", "2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02",
				"2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07", "2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12",
				"2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17", "2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22",
				"2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27", "2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01",
				"2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06", "2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11",
				"2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16", "2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21",
				"2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26", "2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01",
				"2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06", "2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11",
				"2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16", "2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21",
				"2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26", "2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31",
				"2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05", "2014-11-06", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10",
				"2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15", "2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-20",
				"2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25", "2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-11-30",
				"2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05", "2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10",
				"2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15", "2014-12-16", "2014-12-17", "2014-12-18", "2014-12-19", "2014-12-20",
				"2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25", "2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30",
				"2014-12-31", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04", "2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09",
				"2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14", "2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19",
				"2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24", "2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29",
				"2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03", "2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08",
				"2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13", "2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18",
				"2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23", "2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28",
				"2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05", "2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10",
				"2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15", "2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20",
				"2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25", "2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30",
				"2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04", "2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09",
				"2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14", "2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19",
				"2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24", "2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29",
				"2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04", "2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09",
				"2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14", "2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19",
				"2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24", "2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29",
				"2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03", "2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08",
				"2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13", "2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18",
				"2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23", "2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28",
				"2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03", "2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08",
				"2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13", "2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18",
				"2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23", "2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28",
				"2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02", "2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07",
				"2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12", "2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17",
				"2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22", "2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27",
				"2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01", "2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06",
				"2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11", "2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16",
				"2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21", "2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26",
				"2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01", "2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06",
				"2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11", "2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16",
				"2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21", "2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26",
				"2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31", "2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05",
				"2014-11-06", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10", "2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15",
				"2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-20", "2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25",
				"2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-11-30", "2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05",
				"2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10", "2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15",
				"2014-12-16", "2014-12-17", "2014-12-18", "2014-12-19", "2014-12-20", "2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25",
				"2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30", "2014-12-31", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04",
				"2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09", "2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14",
				"2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19", "2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24",
				"2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29", "2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03",
				"2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08", "2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13",
				"2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18", "2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23",
				"2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28", "2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05",
				"2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10", "2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15",
				"2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20", "2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25",
				"2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30", "2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04",
				"2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09", "2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14",
				"2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19", "2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24",
				"2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29", "2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04",
				"2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09", "2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14",
				"2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19", "2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24",
				"2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29", "2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03",
				"2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08", "2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13",
				"2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18", "2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23",
				"2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28", "2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03",
				"2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08", "2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13",
				"2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18", "2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23",
				"2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28", "2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02",
				"2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07", "2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12",
				"2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17", "2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22",
				"2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27", "2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01",
				"2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06", "2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11",
				"2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16", "2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21",
				"2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26", "2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01",
				"2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06", "2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11",
				"2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16", "2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21",
				"2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26", "2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31",
				"2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05", "2014-11-06", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10",
				"2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15", "2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-20",
				"2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25", "2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-11-30",
				"2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05", "2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10",
				"2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15", "2014-12-16", "2014-12-17", "2014-12-18", "2014-12-19", "2014-12-20",
				"2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25", "2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30",
				"2014-12-31", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04", "2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09",
				"2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14", "2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19",
				"2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24", "2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29",
				"2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03", "2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08",
				"2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13", "2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18",
				"2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23", "2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28",
				"2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05", "2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10",
				"2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15", "2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20",
				"2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25", "2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30",
				"2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04", "2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09",
				"2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14", "2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19",
				"2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24", "2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29",
				"2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04", "2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09",
				"2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14", "2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19",
				"2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24", "2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29",
				"2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03", "2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08",
				"2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13", "2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18",
				"2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23", "2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28",
				"2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03", "2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08",
				"2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13", "2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18",
				"2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23", "2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28",
				"2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02", "2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07",
				"2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12", "2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17",
				"2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22", "2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27",
				"2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01", "2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06",
				"2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11", "2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16",
				"2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21", "2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26",
				"2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01", "2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06",
				"2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11", "2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16",
				"2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21", "2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26",
				"2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31", "2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05",
				"2014-11-06", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10", "2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15",
				"2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-20", "2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25",
				"2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-11-30", "2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05",
				"2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10", "2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15",
				"2014-12-16", "2014-12-17", "2014-12-18", "2014-12-19", "2014-12-20", "2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25",
				"2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30", "2014-12-31", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04",
				"2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09", "2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14",
				"2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19", "2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24",
				"2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29", "2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03",
				"2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08", "2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13",
				"2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18", "2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23",
				"2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28", "2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05",
				"2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10", "2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15",
				"2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20", "2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25",
				"2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30", "2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04",
				"2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09", "2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14",
				"2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19", "2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24",
				"2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29", "2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04",
				"2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09", "2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14",
				"2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19", "2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24",
				"2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29", "2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03",
				"2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08", "2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13",
				"2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18", "2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23",
				"2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28", "2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03",
				"2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08", "2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13",
				"2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18", "2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23",
				"2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28", "2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02",
				"2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07", "2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12",
				"2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17", "2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22",
				"2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27", "2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01",
				"2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06", "2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11",
				"2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16", "2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21",
				"2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26", "2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01",
				"2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06", "2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11",
				"2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16", "2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21",
				"2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26", "2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31",
				"2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05", "2014-11-06", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10",
				"2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15", "2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-20",
				"2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25", "2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-11-30",
				"2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05", "2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10",
				"2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15", "2014-12-16", "2014-12-17", "2014-12-18", "2014-12-19", "2014-12-20",
				"2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25", "2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30",
				"2014-12-31", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04", "2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09",
				"2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14", "2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19",
				"2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24", "2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29",
				"2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03", "2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08",
				"2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13", "2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18",
				"2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23", "2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28",
				"2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05", "2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10",
				"2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15", "2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20",
				"2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25", "2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30",
				"2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04", "2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09",
				"2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14", "2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19",
				"2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24", "2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29",
				"2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04", "2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09",
				"2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14", "2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19",
				"2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24", "2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29",
				"2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03", "2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08",
				"2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13", "2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18",
				"2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23", "2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28",
				"2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03", "2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08",
				"2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13", "2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18",
				"2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23", "2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28",
				"2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02", "2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07",
				"2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12", "2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17",
				"2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22", "2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27",
				"2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01", "2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06",
				"2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11", "2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16",
				"2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21", "2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26",
				"2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01", "2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06",
				"2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11", "2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16",
				"2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21", "2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26",
				"2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31", "2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05",
				"2014-11-06", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10", "2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15",
				"2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-20", "2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25",
				"2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-11-30", "2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05",
				"2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10", "2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15",
				"2014-12-16", "2014-12-17", "2014-12-18", "2014-12-19", "2014-12-20", "2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25",
				"2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30", "2014-12-31", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04",
				"2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09", "2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14",
				"2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19", "2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24",
				"2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29", "2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03",
				"2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08", "2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13",
				"2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18", "2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23",
				"2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28", "2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05",
				"2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10", "2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15",
				"2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20", "2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25",
				"2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30", "2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04",
				"2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09", "2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14",
				"2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19", "2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24",
				"2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29", "2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04",
				"2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09", "2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14",
				"2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19", "2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24",
				"2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29", "2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03",
				"2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08", "2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13",
				"2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18", "2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23",
				"2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28", "2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03",
				"2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08", "2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13",
				"2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18", "2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23",
				"2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28", "2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02",
				"2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07", "2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12",
				"2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17", "2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22",
				"2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27", "2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01",
				"2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06", "2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11",
				"2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16", "2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21",
				"2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26", "2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01",
				"2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06", "2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11",
				"2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16", "2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21",
				"2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26", "2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31",
				"2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05", "2014-11-06", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10",
				"2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15", "2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-20",
				"2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25", "2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-11-30",
				"2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05", "2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10",
				"2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15", "2014-12-16", "2014-12-17", "2014-12-18", "2014-12-19", "2014-12-20",
				"2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25", "2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30",
				"2014-12-31", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04", "2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09",
				"2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14", "2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19",
				"2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24", "2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29",
				"2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03", "2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08",
				"2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13", "2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18",
				"2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23", "2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28",
				"2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05", "2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10",
				"2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15", "2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20",
				"2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25", "2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30",
				"2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04", "2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09",
				"2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14", "2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19",
				"2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24", "2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29",
				"2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04", "2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09",
				"2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14", "2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19",
				"2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24", "2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29",
				"2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03", "2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08",
				"2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13", "2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18",
				"2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23", "2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28",
				"2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03", "2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08",
				"2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13", "2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18",
				"2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23", "2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28",
				"2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02", "2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07",
				"2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12", "2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17",
				"2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22", "2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27",
				"2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01", "2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06",
				"2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11", "2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16",
				"2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21", "2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26",
				"2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01", "2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06",
				"2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11", "2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16",
				"2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21", "2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26",
				"2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31", "2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05",
				"2014-11-06", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10", "2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15",
				"2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-20", "2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25",
				"2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-11-30", "2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05",
				"2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10", "2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15",
				"2014-12-16", "2014-12-17", "2014-12-18", "2014-12-19", "2014-12-20", "2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25",
				"2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30", "2014-12-31", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04",
				"2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09", "2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14",
				"2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19", "2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24",
				"2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29", "2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03",
				"2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08", "2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13",
				"2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18", "2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23",
				"2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28", "2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05",
				"2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10", "2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15",
				"2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20", "2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25",
				"2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30", "2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04",
				"2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09", "2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14",
				"2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19", "2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24",
				"2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29", "2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04",
				"2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09", "2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14",
				"2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19", "2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24",
				"2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29", "2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03",
				"2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08", "2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13",
				"2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18", "2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23",
				"2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28", "2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03",
				"2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08", "2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13",
				"2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18", "2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23",
				"2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28", "2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02",
				"2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07", "2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12",
				"2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17", "2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22",
				"2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27", "2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01",
				"2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06", "2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11",
				"2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16", "2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21",
				"2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26", "2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01",
				"2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06", "2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11",
				"2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16", "2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21",
				"2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26", "2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31",
				"2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05", "2014-11-06", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10",
				"2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15", "2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-20",
				"2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25", "2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-11-30",
				"2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05", "2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10",
				"2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15", "2014-12-16", "2014-12-17", "2014-12-18", "2014-12-19", "2014-12-20",
				"2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25", "2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30",
				"2014-12-31", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04", "2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09",
				"2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14", "2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19",
				"2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24", "2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29",
				"2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03", "2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08",
				"2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13", "2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18",
				"2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23", "2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28",
				"2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05", "2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10",
				"2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15", "2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20",
				"2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25", "2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30",
				"2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04", "2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09",
				"2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14", "2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19",
				"2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24", "2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29",
				"2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04", "2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09",
				"2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14", "2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19",
				"2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24", "2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29",
				"2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03", "2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08",
				"2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13", "2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18",
				"2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23", "2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28",
				"2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03", "2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08",
				"2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13", "2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18",
				"2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23", "2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28",
				"2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02", "2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07",
				"2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12", "2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17",
				"2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22", "2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27",
				"2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01", "2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06",
				"2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11", "2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16",
				"2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21", "2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26",
				"2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01", "2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06",
				"2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11", "2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16",
				"2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21", "2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26",
				"2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31", "2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05",
				"2014-11-06", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10", "2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15",
				"2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-20", "2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25",
				"2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-11-30", "2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05",
				"2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10", "2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15",
				"2014-12-16", "2014-12-17", "2014-12-18", "2014-12-19", "2014-12-20", "2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25",
				"2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30", "2014-12-31", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04",
				"2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09", "2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14",
				"2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19", "2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24",
				"2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29", "2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03",
				"2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08", "2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13",
				"2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18", "2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23",
				"2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28", "2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05",
				"2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10", "2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15",
				"2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20", "2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25",
				"2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30", "2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04",
				"2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09", "2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14",
				"2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19", "2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24",
				"2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29", "2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04",
				"2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09", "2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14",
				"2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19", "2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24",
				"2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29", "2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03",
				"2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08", "2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13",
				"2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18", "2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23",
				"2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28", "2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03",
				"2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08", "2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13",
				"2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18", "2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23",
				"2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28", "2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02",
				"2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07", "2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12",
				"2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17", "2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22",
				"2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27", "2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01",
				"2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06", "2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11",
				"2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16", "2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21",
				"2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26", "2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01",
				"2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06", "2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11",
				"2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16", "2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21",
				"2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26", "2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31",
				"2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05", "2014-11-06", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10",
				"2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15", "2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-20",
				"2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25", "2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-11-30",
				"2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05", "2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10",
				"2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15", "2014-12-16", "2014-12-17", "2014-12-18", "2014-12-19", "2014-12-20",
				"2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25", "2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30",
				"2014-12-31", "2014-01-01", "2014-01-02", "2014-01-03", "2014-01-04", "2014-01-05", "2014-01-06", "2014-01-07", "2014-01-08", "2014-01-09",
				"2014-01-10", "2014-01-11", "2014-01-12", "2014-01-13", "2014-01-14", "2014-01-15", "2014-01-16", "2014-01-17", "2014-01-18", "2014-01-19",
				"2014-01-20", "2014-01-21", "2014-01-22", "2014-01-23", "2014-01-24", "2014-01-25", "2014-01-26", "2014-01-27", "2014-01-28", "2014-01-29",
				"2014-01-30", "2014-01-31", "2014-02-01", "2014-02-02", "2014-02-03", "2014-02-04", "2014-02-05", "2014-02-06", "2014-02-07", "2014-02-08",
				"2014-02-09", "2014-02-10", "2014-02-11", "2014-02-12", "2014-02-13", "2014-02-14", "2014-02-15", "2014-02-16", "2014-02-17", "2014-02-18",
				"2014-02-19", "2014-02-20", "2014-02-21", "2014-02-22", "2014-02-23", "2014-02-24", "2014-02-25", "2014-02-26", "2014-02-27", "2014-02-28",
				"2014-03-01", "2014-03-02", "2014-03-03", "2014-03-04", "2014-03-05", "2014-03-06", "2014-03-07", "2014-03-08", "2014-03-09", "2014-03-10",
				"2014-03-11", "2014-03-12", "2014-03-13", "2014-03-14", "2014-03-15", "2014-03-16", "2014-03-17", "2014-03-18", "2014-03-19", "2014-03-20",
				"2014-03-21", "2014-03-22", "2014-03-23", "2014-03-24", "2014-03-25", "2014-03-26", "2014-03-27", "2014-03-28", "2014-03-29", "2014-03-30",
				"2014-03-31", "2014-04-01", "2014-04-02", "2014-04-03", "2014-04-04", "2014-04-05", "2014-04-06", "2014-04-07", "2014-04-08", "2014-04-09",
				"2014-04-10", "2014-04-11", "2014-04-12", "2014-04-13", "2014-04-14", "2014-04-15", "2014-04-16", "2014-04-17", "2014-04-18", "2014-04-19",
				"2014-04-20", "2014-04-21", "2014-04-22", "2014-04-23", "2014-04-24", "2014-04-25", "2014-04-26", "2014-04-27", "2014-04-28", "2014-04-29",
				"2014-04-30", "2014-05-01", "2014-05-02", "2014-05-03", "2014-05-04", "2014-05-05", "2014-05-06", "2014-05-07", "2014-05-08", "2014-05-09",
				"2014-05-10", "2014-05-11", "2014-05-12", "2014-05-13", "2014-05-14", "2014-05-15", "2014-05-16", "2014-05-17", "2014-05-18", "2014-05-19",
				"2014-05-20", "2014-05-21", "2014-05-22", "2014-05-23", "2014-05-24", "2014-05-25", "2014-05-26", "2014-05-27", "2014-05-28", "2014-05-29",
				"2014-05-30", "2014-05-31", "2014-06-01", "2014-06-02", "2014-06-03", "2014-06-04", "2014-06-05", "2014-06-06", "2014-06-07", "2014-06-08",
				"2014-06-09", "2014-06-10", "2014-06-11", "2014-06-12", "2014-06-13", "2014-06-14", "2014-06-15", "2014-06-16", "2014-06-17", "2014-06-18",
				"2014-06-19", "2014-06-20", "2014-06-21", "2014-06-22", "2014-06-23", "2014-06-24", "2014-06-25", "2014-06-26", "2014-06-27", "2014-06-28",
				"2014-06-29", "2014-06-30", "2014-07-01", "2014-07-02", "2014-07-03", "2014-07-04", "2014-07-05", "2014-07-06", "2014-07-07", "2014-07-08",
				"2014-07-09", "2014-07-10", "2014-07-11", "2014-07-12", "2014-07-13", "2014-07-14", "2014-07-15", "2014-07-16", "2014-07-17", "2014-07-18",
				"2014-07-19", "2014-07-20", "2014-07-21", "2014-07-22", "2014-07-23", "2014-07-24", "2014-07-25", "2014-07-26", "2014-07-27", "2014-07-28",
				"2014-07-29", "2014-07-30", "2014-07-31", "2014-08-01", "2014-08-02", "2014-08-03", "2014-08-04", "2014-08-05", "2014-08-06", "2014-08-07",
				"2014-08-08", "2014-08-09", "2014-08-10", "2014-08-11", "2014-08-12", "2014-08-13", "2014-08-14", "2014-08-15", "2014-08-16", "2014-08-17",
				"2014-08-18", "2014-08-19", "2014-08-20", "2014-08-21", "2014-08-22", "2014-08-23", "2014-08-24", "2014-08-25", "2014-08-26", "2014-08-27",
				"2014-08-28", "2014-08-29", "2014-08-30", "2014-08-31", "2014-09-01", "2014-09-02", "2014-09-03", "2014-09-04", "2014-09-05", "2014-09-06",
				"2014-09-07", "2014-09-08", "2014-09-09", "2014-09-10", "2014-09-11", "2014-09-12", "2014-09-13", "2014-09-14", "2014-09-15", "2014-09-16",
				"2014-09-17", "2014-09-18", "2014-09-19", "2014-09-20", "2014-09-21", "2014-09-22", "2014-09-23", "2014-09-24", "2014-09-25", "2014-09-26",
				"2014-09-27", "2014-09-28", "2014-09-29", "2014-09-30", "2014-10-01", "2014-10-02", "2014-10-03", "2014-10-04", "2014-10-05", "2014-10-06",
				"2014-10-07", "2014-10-08", "2014-10-09", "2014-10-10", "2014-10-11", "2014-10-12", "2014-10-13", "2014-10-14", "2014-10-15", "2014-10-16",
				"2014-10-17", "2014-10-18", "2014-10-19", "2014-10-20", "2014-10-21", "2014-10-22", "2014-10-23", "2014-10-24", "2014-10-25", "2014-10-26",
				"2014-10-27", "2014-10-28", "2014-10-29", "2014-10-30", "2014-10-31", "2014-11-01", "2014-11-02", "2014-11-03", "2014-11-04", "2014-11-05",
				"2014-11-06", "2014-11-07", "2014-11-08", "2014-11-09", "2014-11-10", "2014-11-11", "2014-11-12", "2014-11-13", "2014-11-14", "2014-11-15",
				"2014-11-16", "2014-11-17", "2014-11-18", "2014-11-19", "2014-11-20", "2014-11-21", "2014-11-22", "2014-11-23", "2014-11-24", "2014-11-25",
				"2014-11-26", "2014-11-27", "2014-11-28", "2014-11-29", "2014-11-30", "2014-12-01", "2014-12-02", "2014-12-03", "2014-12-04", "2014-12-05",
				"2014-12-06", "2014-12-07", "2014-12-08", "2014-12-09", "2014-12-10", "2014-12-11", "2014-12-12", "2014-12-13", "2014-12-14", "2014-12-15",
				"2014-12-16", "2014-12-17", "2014-12-18", "2014-12-19", "2014-12-20", "2014-12-21", "2014-12-22", "2014-12-23", "2014-12-24", "2014-12-25",
				"2014-12-26", "2014-12-27", "2014-12-28", "2014-12-29", "2014-12-30", "2014-12-31", };
	}

	private int[] getIntArrayForMarketMatching() {
		return new int[] { 49, 53, 54, 55, 55, 52, 54, 56, 56, 54, 52, 53, 55, 58, 61, 60, 56, 55, 55, 55, 56, 55, 57, 55, 57, 57, 54, 59, 59, 55, 54, 51, 53, 51, 50,
				49, 50, 54, 56, 57, 54, 53, 51, 59, 61, 57, 57, 54, 55, 57, 56, 58, 56, 55, 58, 55, 57, 60, 58, 58, 56, 55, 58, 62, 59, 57, 58, 65, 61, 63, 65, 64, 61,
				64, 60, 59, 60, 61, 60, 57, 56, 57, 60, 55, 58, 60, 63, 57, 56, 53, 54, 54, 57, 56, 57, 62, 69, 67, 58, 60, 59, 59, 63, 60, 59, 63, 59, 58, 57, 65, 61,
				58, 59, 61, 63, 55, 59, 61, 70, 75, 74, 64, 61, 63, 61, 60, 58, 60, 60, 60, 63, 68, 76, 77, 71, 64, 62, 63, 62, 64, 64, 62, 62, 61, 63, 64, 61, 63, 66,
				59, 60, 62, 60, 60, 61, 62, 63, 62, 71, 68, 66, 65, 62, 64, 70, 63, 60, 64, 71, 64, 66, 61, 62, 64, 63, 64, 65, 67, 66, 69, 71, 67, 67, 65, 62, 62, 61,
				63, 66, 67, 68, 66, 62, 61, 67, 70, 70, 69, 68, 68, 68, 71, 71, 70, 72, 76, 69, 69, 67, 67, 65, 65, 66, 64, 65, 68, 68, 69, 65, 67, 65, 69, 70, 70, 69,
				67, 67, 66, 65, 65, 68, 68, 68, 68, 66, 67, 68, 67, 67, 67, 66, 72, 69, 72, 67, 68, 68, 68, 66, 64, 63, 65, 69, 69, 67, 68, 67, 70, 72, 72, 72, 69, 71,
				69, 72, 71, 72, 68, 68, 68, 69, 66, 69, 73, 76, 81, 78, 74, 71, 66, 68, 64, 64, 68, 75, 76, 69, 69, 66, 67, 72, 68, 67, 64, 66, 69, 67, 68, 64, 63, 63,
				71, 67, 61, 60, 58, 60, 61, 65, 65, 62, 65, 61, 61, 61, 62, 62, 61, 60, 60, 58, 58, 60, 57, 58, 60, 58, 57, 57, 59, 57, 55, 61, 58, 61, 59, 63, 63, 63,
				63, 59, 62, 60, 62, 59, 55, 52, 55, 58, 57, 55, 56, 57, 59, 61, 60, 60, 55, 52, 52, 49, 50, 50, 51, 50, 29, 26, 14, 17, 36, 37, 13, 17, 28, 31, 46, 43,
				40, 46, 38, 39, 37, 35, 32, 41, 23, 13, 15, 16, 25, 27, 34, 18, 20, 21, 34, 37, 41, 36, 29, 33, 28, 29, 25, 25, 25, 21, 20, 31, 37, 33, 26, 26, 33, 36,
				39, 41, 44, 45, 37, 31, 27, 25, 19, 27, 37, 27, 21, 33, 24, 32, 46, 42, 43, 52, 46, 26, 32, 51, 37, 30, 34, 37, 49, 44, 50, 37, 29, 31, 32, 31, 44, 47,
				48, 45, 47, 43, 51, 43, 49, 47, 44, 57, 54, 46, 58, 59, 56, 59, 45, 41, 41, 41, 54, 48, 48, 54, 54, 54, 51, 57, 54, 56, 48, 49, 62, 61, 58, 60, 60, 60,
				57, 55, 60, 66, 70, 66, 61, 57, 66, 63, 63, 61, 62, 67, 68, 60, 63, 64, 67, 73, 74, 60, 57, 62, 64, 65, 66, 71, 73, 70, 71, 71, 73, 66, 73, 66, 69, 74,
				71, 71, 71, 75, 83, 73, 74, 69, 69, 72, 72, 76, 80, 72, 76, 72, 74, 78, 78, 78, 71, 75, 75, 77, 80, 78, 78, 76, 74, 75, 77, 76, 77, 75, 75, 74, 73, 74,
				76, 77, 78, 75, 75, 78, 77, 72, 72, 74, 77, 71, 72, 76, 78, 78, 77, 75, 76, 78, 76, 76, 77, 73, 69, 70, 76, 73, 72, 73, 73, 73, 70, 72, 75, 75, 77, 76,
				71, 71, 79, 80, 83, 80, 77, 78, 77, 74, 72, 70, 71, 77, 71, 65, 63, 63, 64, 64, 68, 62, 65, 72, 64, 60, 64, 62, 69, 73, 70, 71, 66, 65, 65, 62, 62, 54,
				58, 70, 69, 63, 58, 55, 56, 56, 70, 72, 66, 66, 65, 51, 52, 60, 55, 53, 57, 59, 60, 57, 59, 63, 54, 51, 46, 46, 51, 59, 59, 52, 48, 42, 51, 49, 53, 56,
				44, 41, 37, 38, 48, 36, 30, 40, 33, 35, 49, 60, 55, 44, 38, 35, 36, 49, 52, 42, 45, 42, 43, 47, 38, 32, 43, 38, 36, 37, 41, 42, 41, 42, 49, 41, 36, 33,
				36, 43, 49, 52, 50, 45, 46, 48, 41, 32, 31, 55, 64, 56, 58, 62, 64, 60, 58, 55, 57, 57, 60, 65, 65, 68, 69, 70, 67, 63, 64, 64, 63, 60, 65, 67, 60, 60,
				57, 57, 60, 60, 61, 54, 53, 55, 57, 57, 56, 58, 61, 60, 61, 63, 63, 62, 63, 63, 58, 59, 61, 64, 65, 61, 59, 59, 61, 62, 62, 61, 59, 59, 58, 60, 60, 63,
				62, 65, 73, 67, 66, 68, 62, 60, 64, 70, 64, 64, 64, 63, 62, 62, 60, 62, 62, 62, 59, 60, 63, 62, 58, 57, 56, 57, 58, 59, 62, 69, 69, 65, 59, 63, 61, 60,
				62, 59, 63, 64, 66, 63, 63, 63, 62, 63, 65, 63, 60, 60, 65, 73, 76, 77, 77, 72, 67, 64, 60, 61, 62, 62, 64, 75, 73, 79, 81, 84, 79, 70, 67, 66, 64, 64,
				64, 66, 66, 68, 68, 69, 69, 69, 68, 68, 67, 65, 66, 66, 66, 66, 66, 66, 64, 67, 68, 67, 66, 67, 68, 67, 67, 67, 67, 69, 70, 69, 70, 70, 69, 70, 71, 72,
				72, 71, 71, 70, 71, 72, 75, 74, 73, 72, 72, 71, 70, 71, 72, 72, 72, 72, 71, 71, 69, 72, 71, 72, 73, 74, 76, 71, 71, 74, 73, 75, 72, 71, 72, 73, 72, 68,
				68, 70, 71, 71, 71, 70, 71, 70, 71, 70, 72, 72, 72, 73, 72, 71, 72, 71, 71, 69, 71, 75, 76, 73, 72, 74, 74, 74, 73, 72, 71, 73, 76, 78, 76, 72, 75, 75,
				76, 78, 79, 82, 78, 72, 72, 71, 71, 69, 70, 71, 72, 71, 68, 70, 68, 70, 69, 79, 81, 77, 74, 75, 77, 76, 71, 70, 70, 71, 70, 69, 69, 67, 67, 67, 67, 69,
				69, 72, 69, 70, 69, 68, 66, 66, 70, 68, 67, 61, 59, 63, 63, 68, 75, 74, 70, 65, 64, 64, 63, 61, 65, 63, 64, 66, 65, 62, 61, 63, 62, 68, 68, 67, 67, 72,
				66, 62, 63, 63, 61, 61, 62, 63, 61, 63, 66, 64, 61, 63, 58, 56, 56, 57, 57, 57, 55, 57, 56, 58, 59, 66, 65, 58, 53, 50, 51, 51, 52, 50, 47, 48, 47, 43,
				44, 50, 50, 48, 46, 44, 40, 39, 45, 39, 46, 46, 44, 47, 42, 40, 40, 46, 42, 40, 48, 42, 41, 45, 40, 40, 41, 44, 46, 42, 44, 46, 46, 44, 46, 44, 43, 41,
				42, 42, 46, 46, 43, 44, 46, 44, 49, 44, 46, 50, 51, 46, 45, 45, 41, 42, 43, 42, 42, 44, 48, 52, 52, 54, 47, 47, 48, 50, 49, 54, 55, 49, 50, 52, 50, 50,
				46, 42, 42, 44, 42, 44, 48, 56, 56, 56, 55, 58, 58, 54, 50, 57, 52, 49, 50, 52, 54, 50, 54, 51, 48, 50, 52, 48, 48, 50, 54, 55, 52, 56, 50, 53, 52, 56,
				55, 55, 53, 49, 47, 50, 53, 59, 57, 56, 58, 58, 52, 56, 54, 55, 58, 61, 63, 64, 68, 62, 58, 58, 56, 56, 56, 51, 53, 53, 61, 57, 56, 62, 62, 60, 52, 56,
				60, 66, 66, 68, 64, 62, 65, 67, 65, 61, 60, 62, 64, 62, 63, 65, 66, 68, 66, 61, 62, 65, 60, 60, 60, 63, 64, 68, 68, 66, 64, 61, 60, 64, 64, 62, 70, 68,
				65, 72, 69, 74, 75, 74, 72, 70, 70, 72, 73, 71, 74, 72, 66, 70, 68, 67, 68, 68, 64, 66, 66, 70, 68, 67, 65, 66, 63, 62, 62, 62, 61, 62, 62, 60, 58, 56,
				54, 62, 57, 56, 59, 58, 60, 66, 64, 64, 62, 60, 64, 66, 63, 64, 66, 63, 61, 62, 61, 58, 64, 62, 62, 62, 64, 64, 68, 68, 63, 58, 56, 56, 58, 56, 66, 62,
				67, 60, 64, 64, 66, 62, 55, 50, 50, 52, 54, 58, 56, 56, 50, 54, 55, 56, 58, 61, 64, 63, 58, 52, 50, 56, 56, 52, 56, 60, 57, 55, 60, 63, 60, 55, 46, 46,
				44, 42, 50, 52, 50, 49, 52, 53, 51, 52, 49, 48, 48, 50, 48, 47, 44, 52, 45, 40, 40, 48, 48, 50, 50, 46, 44, 44, 43, 39, 40, 36, 44, 40, 42, 45, 46, 42,
				38, 40, 44, 39, 48, 54, 48, 44, 46, 53, 52, 46, 42, 40, 38, 36, 34, 33, 35, 78, 78, 67, 69, 76, 72, 54, 61, 75, 77, 78, 74, 73, 77, 66, 56, 56, 61, 58,
				65, 67, 59, 60, 66, 65, 67, 72, 72, 72, 66, 75, 77, 76, 79, 78, 80, 78, 76, 77, 74, 74, 77, 74, 67, 61, 68, 65, 69, 71, 74, 77, 79, 79, 77, 78, 78, 79,
				73, 70, 71, 72, 74, 74, 78, 77, 71, 67, 71, 73, 73, 80, 72, 68, 72, 76, 81, 75, 74, 76, 73, 80, 81, 75, 75, 65, 71, 77, 77, 73, 71, 72, 73, 76, 78, 77,
				77, 80, 79, 73, 72, 75, 77, 79, 80, 82, 78, 79, 79, 79, 73, 73, 76, 76, 78, 81, 80, 81, 83, 83, 84, 83, 83, 82, 77, 78, 78, 82, 83, 82, 82, 83, 83, 82,
				82, 78, 75, 77, 79, 80, 80, 79, 80, 82, 83, 82, 82, 83, 83, 81, 81, 83, 82, 78, 79, 77, 80, 82, 85, 83, 81, 80, 83, 82, 80, 81, 82, 84, 79, 80, 80, 81,
				81, 82, 83, 83, 85, 84, 85, 84, 83, 83, 83, 84, 85, 85, 83, 83, 84, 83, 84, 83, 83, 83, 84, 85, 81, 84, 83, 86, 85, 85, 82, 84, 84, 85, 85, 82, 86, 88,
				87, 85, 84, 85, 84, 81, 81, 84, 86, 83, 84, 86, 86, 85, 86, 86, 85, 82, 84, 85, 86, 88, 85, 87, 86, 85, 87, 85, 86, 85, 86, 87, 88, 87, 86, 84, 82, 82,
				85, 84, 83, 84, 84, 82, 83, 80, 81, 82, 85, 84, 85, 83, 81, 82, 81, 82, 78, 81, 82, 83, 84, 84, 83, 84, 86, 85, 86, 84, 79, 79, 79, 81, 83, 82, 83, 83,
				83, 84, 82, 79, 77, 77, 78, 78, 78, 80, 78, 76, 77, 77, 75, 78, 80, 78, 77, 68, 62, 69, 76, 78, 76, 76, 75, 72, 72, 72, 74, 76, 75, 74, 79, 81, 69, 60,
				70, 73, 78, 80, 82, 82, 71, 65, 62, 66, 74, 76, 77, 76, 76, 77, 75, 73, 69, 63, 59, 59, 61, 67, 67, 67, 68, 69, 70, 70, 71, 73, 74, 77, 80, 73, 73, 75,
				74, 76, 76, 76, 47, 51, 50, 51, 49, 48, 50, 45, 51, 49, 49, 49, 48, 49, 51, 51, 52, 50, 51, 51, 52, 48, 52, 59, 57, 51, 51, 59, 58, 57, 50, 48, 44, 50,
				44, 43, 48, 50, 53, 57, 59, 54, 55, 62, 61, 57, 56, 51, 54, 58, 57, 56, 57, 57, 58, 58, 57, 59, 56, 60, 55, 54, 58, 61, 60, 60, 56, 62, 61, 61, 64, 61,
				62, 65, 63, 59, 60, 59, 62, 61, 60, 62, 62, 54, 56, 56, 60, 56, 51, 52, 50, 52, 55, 54, 56, 62, 65, 69, 68, 66, 64, 60, 66, 63, 66, 66, 67, 68, 63, 67,
				65, 59, 57, 62, 55, 55, 59, 61, 71, 72, 74, 70, 65, 63, 63, 65, 63, 61, 66, 61, 68, 70, 72, 76, 77, 75, 69, 68, 64, 63, 69, 71, 72, 76, 76, 75, 73, 69,
				72, 70, 69, 72, 68, 68, 73, 76, 73, 75, 83, 84, 82, 70, 68, 71, 72, 71, 66, 69, 74, 75, 76, 75, 73, 74, 76, 72, 71, 73, 75, 80, 84, 77, 74, 76, 78, 77,
				78, 78, 81, 75, 73, 73, 75, 79, 85, 81, 76, 73, 73, 76, 71, 74, 74, 73, 79, 82, 84, 86, 79, 80, 82, 82, 85, 78, 71, 70, 71, 81, 79, 77, 74, 72, 77, 73,
				73, 76, 78, 78, 75, 73, 71, 70, 74, 74, 74, 75, 72, 80, 79, 80, 76, 80, 79, 81, 77, 77, 77, 75, 75, 74, 70, 72, 76, 80, 82, 83, 80, 78, 76, 76, 76, 74,
				75, 73, 75, 75, 74, 66, 67, 68, 67, 67, 72, 76, 71, 76, 75, 76, 77, 76, 76, 72, 71, 73, 76, 71, 66, 65, 64, 64, 67, 66, 64, 61, 62, 68, 64, 66, 60, 59,
				61, 65, 66, 61, 59, 57, 55, 57, 60, 62, 63, 63, 64, 62, 60, 55, 59, 59, 56, 54, 54, 51, 53, 54, 54, 55, 52, 52, 53, 54, 57, 52, 57, 55, 58, 56, 62, 62,
				61, 61, 58, 56, 55, 55, 57, 49, 51, 50, 53, 56, 53, 55, 53, 56, 61, 56, 55, 53, 51, 48, 44, 46, 43, 47, 46, 38, 40, 42, 43, 40, 40, 45, 44, 44, 42, 42,
				36, 32, 37, 36, 34, 34, 34, 34, 34, 33, 30, 29, 26, 24, 23, 26, 30, 25, 25, 28, 33, 34, 32, 34, 33, 36, 39, 38, 40, 38, 36, 37, 38, 38, 40, 41, 40, 36,
				40, 42, 43, 40, 40, 40, 38, 40, 40, 40, 38, 37, 37, 39, 38, 36, 42, 42, 43, 48, 42, 42, 42, 44, 46, 46, 47, 48, 45, 51, 44, 40, 42, 38, 40, 38, 36, 47,
				44, 43, 43, 40, 42, 44, 40, 40, 46, 52, 53, 45, 45, 46, 42, 48, 45, 46, 43, 44, 44, 50, 55, 56, 56, 51, 46, 53, 52, 55, 52, 54, 52, 48, 46, 46, 47, 47,
				50, 52, 52, 52, 52, 54, 52, 52, 50, 53, 53, 57, 56, 58, 58, 61, 68, 66, 60, 62, 62, 58, 52, 52, 60, 58, 59, 60, 58, 58, 61, 59, 59, 60, 65, 68, 64, 63,
				61, 60, 60, 58, 64, 64, 58, 60, 56, 56, 58, 58, 59, 58, 60, 63, 59, 60, 60, 59, 61, 66, 70, 68, 69, 72, 74, 74, 70, 64, 61, 64, 68, 66, 64, 67, 69, 71,
				72, 73, 73, 74, 74, 74, 72, 74, 76, 70, 69, 69, 74, 72, 69, 70, 66, 68, 68, 68, 65, 66, 64, 64, 64, 64, 60, 57, 59, 60, 60, 59, 59, 60, 58, 57, 57, 62,
				59, 60, 59, 58, 63, 60, 58, 62, 62, 64, 64, 60, 60, 58, 61, 60, 59, 62, 63, 62, 62, 63, 63, 58, 58, 52, 48, 57, 55, 58, 56, 58, 56, 56, 56, 56, 56, 60,
				57, 55, 54, 78, 60, 56, 54, 54, 50, 51, 52, 56, 54, 56, 58, 55, 51, 54, 52, 52, 51, 54, 54, 53, 49, 44, 46, 54, 54, 55, 53, 50, 48, 46, 50, 51, 48, 47,
				50, 50, 50, 49, 47, 46, 44, 41, 42, 44, 43, 46, 46, 42, 41, 44, 40, 38, 38, 35, 36, 36, 39, 40, 38, 36, 39, 36, 40, 39, 42, 41, 38, 40, 38, 38, 44, 44,
				38, 41, 46, 43, 42, 29, 24, 31, 26, 26, 34, 41, 74, 84, 76, 72, 76, 77, 66, 66, 70, 73, 78, 76, 73, 76, 78, 78, 78, 78, 72, 73, 72, 70, 72, 76, 69, 67,
				71, 74, 78, 77, 76, 75, 78, 78, 72, 68, 68, 70, 76, 78, 71, 72, 75, 78, 74, 75, 72, 70, 72, 77, 74, 71, 70, 70, 72, 76, 81, 70, 68, 70, 69, 70, 72, 76,
				71, 74, 76, 74, 77, 75, 74, 70, 74, 74, 75, 70, 72, 70, 74, 75, 76, 76, 68, 70, 72, 70, 70, 70, 72, 70, 72, 72, 74, 68, 68, 66, 66, 68, 68, 68, 73, 66,
				65, 62, 63, 64, 62, 66, 64, 64, 64, 66, 66, 72, 66, 67, 63, 66, 68, 66, 60, 64, 56, 60, 60, 59, 58, 57, 60, 64, 64, 61, 64, 64, 63, 65, 66, 65, 67, 68,
				64, 67, 66, 68, 68, 66, 69, 68, 62, 62, 64, 64, 63, 60, 60, 60, 58, 56, 56, 54, 60, 57, 60, 60, 62, 56, 58, 56, 58, 59, 62, 58, 58, 56, 57, 58, 60, 58,
				62, 56, 54, 54, 54, 54, 58, 55, 54, 56, 56, 56, 54, 57, 54, 50, 50, 56, 59, 58, 54, 52, 55, 54, 54, 55, 57, 58, 59, 56, 57, 64, 65, 67, 57, 51, 50, 54,
				54, 57, 57, 54, 58, 60, 50, 49, 52, 52, 54, 54, 58, 56, 58, 54, 54, 58, 56, 58, 59, 57, 58, 60, 54, 56, 60, 63, 56, 53, 54, 54, 58, 58, 60, 64, 65, 66,
				60, 60, 65, 62, 66, 64, 58, 58, 55, 59, 61, 61, 65, 66, 62, 61, 64, 75, 76, 74, 64, 62, 66, 76, 70, 72, 64, 64, 66, 70, 72, 68, 57, 54, 60, 58, 62, 66,
				62, 62, 65, 70, 73, 70, 76, 78, 73, 66, 74, 74, 82, 62, 63, 68, 70, 64, 66, 68, 66, 68, 64, 66, 70, 82, 68, 72, 67, 69, 68, 77, 82, 72, 79, 78, 72, 72,
				66, 68, 70, 75, 76, 76, 80, 76, 76, 75, 74, 78, 72, 72, 68, 66, 68, 68, 72, 74, 68, 73, 69, 69, 72, 76, 76, 71, 76, 74, 68, 72, 73, 80, 72, 41, 39, 29,
				30, 43, 33, 16, 32, 39, 44, 56, 49, 48, 52, 39, 35, 42, 35, 46, 51, 38, 27, 31, 24, 33, 44, 46, 25, 20, 27, 40, 45, 60, 52, 43, 43, 35, 40, 48, 51, 45,
				38, 32, 37, 44, 41, 47, 52, 57, 63, 67, 55, 52, 57, 54, 51, 41, 38, 42, 51, 60, 47, 39, 47, 42, 48, 56, 60, 60, 64, 56, 44, 50, 59, 56, 49, 43, 53, 58,
				55, 64, 57, 50, 44, 41, 52, 57, 58, 54, 61, 67, 67, 70, 69, 60, 55, 58, 55, 58, 59, 61, 66, 69, 67, 51, 48, 53, 54, 57, 63, 64, 66, 66, 65, 71, 66, 71,
				71, 72, 68, 61, 63, 63, 71, 75, 73, 72, 75, 71, 70, 74, 77, 77, 74, 59, 58, 60, 59, 65, 70, 74, 76, 79, 78, 79, 76, 75, 77, 77, 77, 78, 75, 72, 78, 79,
				77, 76, 78, 77, 79, 78, 75, 75, 75, 76, 80, 81, 82, 83, 83, 82, 79, 79, 80, 78, 79, 79, 79, 79, 82, 80, 81, 83, 79, 74, 77, 78, 81, 80, 82, 79, 78, 81,
				81, 81, 79, 74, 74, 75, 77, 72, 78, 77, 80, 79, 79, 80, 82, 81, 74, 72, 76, 74, 76, 78, 81, 80, 81, 83, 81, 80, 79, 78, 80, 76, 74, 77, 78, 79, 80, 79,
				81, 81, 84, 84, 82, 77, 76, 77, 80, 80, 80, 80, 82, 81, 82, 77, 81, 82, 82, 78, 77, 79, 81, 83, 78, 72, 73, 79, 77, 74, 77, 75, 75, 73, 69, 67, 71, 70,
				72, 70, 69, 75, 75, 74, 72, 57, 58, 65, 70, 76, 78, 76, 78, 75, 74, 66, 62, 62, 65, 66, 60, 62, 65, 59, 58, 62, 62, 71, 72, 68, 62, 57, 51, 43, 46, 52,
				57, 60, 61, 51, 51, 55, 57, 58, 56, 41, 36, 39, 44, 45, 31, 36, 48, 51, 50, 54, 66, 55, 50, 42, 40, 47, 55, 60, 61, 61, 63, 58, 58, 52, 43, 45, 43, 43,
				47, 50, 46, 48, 52, 44, 46, 47, 47, 48, 46, 49, 55, 45, 47, 50, 57, 58, 46, 44, 36, 40, 40, 40, 38, 38, 47, 49, 48, 40, 36, 38, 37, 38, 35, 42, 40, 34,
				34, 37, 38, 38, 38, 34, 36, 37, 35, 35, 29, 30, 32, 36, 37, 34, 38, 32, 43, 43, 40, 39, 34, 38, 37, 41, 40, 45, 42, 42, 39, 42, 44, 39, 38, 42, 40, 42,
				46, 43, 42, 40, 39, 40, 43, 42, 40, 43, 44, 46, 48, 46, 50, 48, 48, 46, 51, 51, 50, 50, 52, 55, 48, 40, 38, 38, 38, 42, 48, 50, 52, 52, 54, 56, 57, 58,
				53, 57, 56, 54, 48, 51, 51, 54, 56, 49, 40, 42, 44, 44, 48, 53, 50, 53, 56, 56, 62, 58, 52, 51, 54, 57, 54, 54, 44, 46, 51, 56, 56, 56, 60, 56, 54, 49,
				50, 52, 49, 53, 52, 55, 58, 62, 64, 68, 61, 59, 60, 59, 59, 57, 56, 50, 51, 56, 59, 60, 58, 56, 62, 69, 74, 77, 78, 72, 72, 69, 61, 59, 62, 61, 62, 63,
				60, 58, 62, 64, 66, 61, 58, 62, 65, 61, 60, 60, 63, 65, 70, 67, 72, 66, 58, 53, 56, 64, 66, 67, 66, 68, 68, 71, 72, 75, 73, 65, 69, 70, 68, 68, 64, 70,
				70, 64, 66, 68, 66, 66, 66, 67, 62, 64, 66, 66, 70, 72, 66, 64, 59, 60, 58, 60, 60, 60, 61, 57, 56, 58, 57, 55, 54, 61, 62, 62, 64, 64, 56, 56, 58, 58,
				61, 64, 67, 64, 67, 66, 60, 56, 54, 57, 62, 60, 62, 62, 66, 65, 66, 62, 49, 49, 51, 54, 57, 59, 59, 62, 62, 60, 58, 56, 52, 56, 54, 56, 62, 66, 60, 60,
				60, 56, 59, 58, 58, 57, 56, 60, 62, 54, 44, 47, 46, 48, 54, 46, 42, 47, 50, 53, 54, 50, 52, 51, 43, 43, 42, 46, 43, 42, 42, 42, 48, 48, 45, 48, 44, 43,
				42, 38, 41, 48, 44, 48, 44, 42, 42, 38, 35, 36, 36, 36, 36, 38, 37, 37, 36, 37, 32, 34, 40, 44, 46, 46, 43, 38, 38, 47, 49, 40, 40, 45, 43, 38, 39, 36,
				32, 20, 18, 30, 32, 48, 46, 44, 44, 42, 44, 44, 47, 44, 38, 38, 42, 42, 40, 39, 39, 44, 42, 40, 40, 42, 40, 42, 45, 46, 49, 38, 47, 46, 52, 50, 44, 48,
				56, 44, 36, 37, 38, 36, 40, 44, 38, 40, 40, 37, 38, 46, 47, 42, 42, 40, 42, 42, 42, 44, 46, 48, 52, 56, 48, 42, 46, 42, 46, 44, 40, 40, 44, 42, 42, 50,
				55, 50, 46, 52, 52, 57, 52, 47, 50, 48, 52, 55, 61, 60, 56, 57, 63, 62, 60, 56, 57, 57, 60, 52, 48, 50, 58, 58, 61, 54, 56, 56, 56, 59, 64, 62, 55, 54,
				50, 56, 58, 60, 60, 63, 64, 63, 65, 64, 60, 68, 68, 70, 66, 64, 58, 60, 64, 68, 65, 68, 66, 66, 70, 65, 70, 68, 68, 68, 70, 63, 65, 63, 67, 72, 70, 70,
				73, 72, 73, 76, 78, 76, 74, 74, 68, 68, 66, 68, 70, 73, 70, 72, 75, 76, 75, 76, 74, 71, 76, 74, 74, 71, 74, 73, 73, 74, 74, 73, 76, 74, 76, 78, 76, 71,
				70, 74, 74, 79, 75, 78, 82, 83, 78, 82, 81, 81, 80, 76, 74, 76, 76, 80, 83, 84, 84, 84, 86, 80, 81, 81, 84, 84, 84, 88, 86, 88, 86, 84, 82, 78, 79, 84,
				78, 78, 80, 84, 82, 81, 84, 86, 86, 84, 86, 82, 82, 78, 76, 72, 70, 73, 72, 74, 70, 75, 74, 76, 80, 80, 72, 70, 73, 74, 70, 72, 74, 73, 74, 76, 74, 72,
				70, 70, 68, 72, 70, 73, 76, 74, 68, 70, 74, 73, 73, 68, 76, 75, 66, 69, 68, 66, 68, 70, 66, 65, 68, 73, 62, 62, 65, 62, 64, 68, 66, 64, 57, 62, 62, 68,
				66, 62, 60, 60, 64, 63, 66, 65, 60, 58, 64, 64, 58, 60, 63, 58, 58, 60, 52, 54, 54, 55, 55, 54, 50, 54, 56, 57, 56, 52, 49, 55, 56, 60, 58, 60, 50, 48,
				52, 50, 42, 43, 46, 48, 46, 53, 50, 47, 40, 42, 42, 42, 40, 40, 44, 47, 45, 45, 48, 46, 42, 42, 40, 42, 45, 45, 30, 30, 20, 22, 24, 20, 24, 31, 22, 19,
				16, 14, 15, 24, 28, 28, 14, 14, 14, 17, 23, 29, 20, 18, 15, 16, 27, 31, 21, 18, 18, 20, 22, 24, 19, 16, 28, 32, 22, 20, 18, 16, 12, 12, 18, 14, 11, 10,
				16, 18, 15, 13, 7, 4, 6, 14, 26, 36, 20, 16, 14, 19, 22, 18, 16, 12, 11, 10, 12, 12, 13, 19, 22, 21, 14, 17, 16, 9, 9, 10, 14, 13, 9, 8, 4, 11, 27, 38,
				28, 22, 17, 13, 18, 18, 14, 12, 12, 16, 26, 36, 26, 28, 19, 13, 16, 18, 18, 11, 10, 14, 13, 19, 21, 22, 22, 32, 36, 24, 21, 29, 38, 40, 42, 38, 32, 30,
				27, 29, 31, 31, 34, 36, 38, 32, 34, 32, 24, 36, 33, 34, 36, 34, 26, 32, 34, 33, 34, 34, 34, 37, 36, 32, 38, 36, 37, 36, 39, 40, 44, 44, 36, 40, 46, 50,
				46, 50, 44, 46, 45, 51, 49, 47, 50, 42, 46, 40, 41, 42, 42, 39, 39, 38, 48, 54, 48, 41, 37, 52, 52, 45, 50, 48, 50, 42, 51, 40, 41, 48, 53, 47, 52, 46,
				52, 55, 52, 50, 53, 46, 48, 56, 55, 52, 48, 44, 46, 46, 46, 45, 48, 46, 52, 46, 50, 49, 46, 44, 49, 52, 48, 36, 47, 48, 50, 46, 46, 46, 48, 44, 44, 47,
				53, 44, 43, 40, 40, 42, 48, 43, 42, 43, 40, 38, 40, 36, 37, 36, 35, 38, 43, 44, 44, 41, 40, 43, 38, 37, 36, 35, 34, 32, 32, 32, 34, 32, 32, 29, 26, 30,
				34, 38, 39, 44, 47, 39, 34, 36, 38, 38, 34, 34, 32, 26, 27, 30, 30, 31, 24, 24, 21, 21, 24, 26, 29, 28, 26, 26, 26, 22, 24, 28, 30, 28, 26, 24, 26, 26,
				26, 32, 31, 36, 30, 26, 31, 30, 30, 30, 35, 28, 22, 22, 21, 18, 16, 12, 21, 24, 20, 21, 32, 22, 23, 21, 16, 14, 20, 22, 15, 22, 26, 16, 10, 14, 12, 10,
				10, 10, 19, 25, 21, 29, 36, 32, 28, 24, 22, 24, 14, 8, 15, 31, 43, 19, 15, 25, 28, 47, 46, 41, 47, 42, 36, 40, 35, 33, 34, 19, 13, 14, 13, 27, 21, 34,
				17, 21, 23, 33, 37, 43, 34, 29, 29, 24, 25, 24, 26, 25, 20, 16, 29, 33, 31, 28, 22, 25, 29, 41, 39, 42, 43, 35, 26, 24, 21, 18, 26, 35, 23, 22, 25, 18,
				25, 41, 37, 36, 47, 42, 21, 28, 48, 30, 22, 27, 34, 45, 40, 43, 33, 25, 30, 30, 34, 48, 46, 40, 38, 41, 42, 44, 41, 46, 47, 49, 54, 50, 47, 59, 59, 54,
				66, 55, 38, 36, 37, 50, 44, 53, 60, 52, 51, 50, 45, 46, 48, 43, 43, 52, 60, 59, 56, 58, 56, 58, 55, 55, 69, 71, 69, 49, 55, 67, 66, 67, 58, 60, 61, 59,
				55, 55, 57, 59, 67, 62, 47, 51, 57, 56, 59, 70, 65, 58, 59, 67, 71, 74, 74, 69, 61, 63, 62, 68, 69, 68, 73, 78, 76, 69, 66, 65, 68, 72, 78, 71, 66, 70,
				74, 77, 79, 82, 81, 70, 70, 76, 79, 83, 81, 74, 68, 71, 77, 79, 78, 73, 74, 71, 68, 67, 67, 75, 82, 70, 73, 75, 73, 75, 72, 71, 72, 74, 67, 66, 71, 76,
				77, 72, 73, 72, 73, 75, 73, 67, 70, 68, 70, 73, 71, 67, 67, 66, 68, 67, 69, 74, 75, 79, 73, 65, 69, 76, 79, 82, 79, 75, 79, 81, 70, 65, 65, 65, 71, 62,
				60, 59, 58, 60, 62, 65, 51, 60, 73, 64, 61, 60, 58, 62, 71, 74, 66, 57, 57, 54, 52, 58, 55, 55, 64, 66, 58, 57, 51, 52, 54, 68, 71, 65, 64, 63, 50, 49,
				56, 53, 51, 49, 55, 53, 55, 53, 63, 53, 50, 44, 38, 45, 51, 57, 49, 44, 42, 48, 48, 53, 53, 44, 39, 35, 36, 43, 36, 31, 40, 32, 34, 51, 54, 55, 42, 35,
				31, 29, 45, 52, 38, 44, 37, 34, 42, 30, 24, 39, 43, 36, 35, 37, 39, 37, 40, 45, 40, 33, 32, 33, 39, 45, 44, 52, 44, 45, 46, 36, 26, 25, 40, 32, 38, 28,
				32, 29, 32, 25, 22, 24, 24, 27, 24, 24, 28, 31, 34, 30, 33, 32, 30, 28, 26, 37, 34, 29, 36, 31, 27, 35, 30, 36, 40, 29, 26, 22, 26, 26, 26, 20, 19, 20,
				26, 28, 26, 29, 32, 34, 30, 30, 28, 30, 35, 35, 38, 36, 40, 40, 34, 40, 40, 32, 39, 34, 34, 32, 41, 38, 36, 42, 43, 40, 50, 52, 52, 53, 50, 47, 54, 55,
				56, 54, 54, 58, 60, 55, 62, 60, 61, 64, 60, 60, 56, 58, 54, 57, 60, 62, 71, 60, 58, 56, 60, 64, 66, 62, 58, 60, 52, 56, 60, 63, 68, 68, 66, 61, 62, 64,
				65, 68, 70, 65, 60, 58, 56, 58, 66, 62, 64, 62, 54, 66, 70, 64, 65, 72, 70, 70, 72, 76, 74, 80, 80, 72, 72, 74, 76, 78, 86, 82, 80, 77, 72, 74, 76, 80,
				70, 72, 74, 72, 71, 74, 80, 78, 76, 78, 78, 72, 76, 74, 74, 74, 74, 75, 77, 76, 82, 81, 82, 82, 84, 78, 74, 80, 84, 80, 82, 82, 84, 80, 84, 82, 84, 82,
				82, 81, 79, 78, 84, 88, 88, 87, 82, 81, 78, 80, 82, 85, 86, 85, 78, 81, 82, 85, 86, 76, 78, 82, 80, 78, 78, 80, 82, 78, 68, 73, 77, 80, 77, 77, 80, 80,
				82, 74, 81, 81, 76, 78, 78, 76, 73, 75, 74, 72, 70, 74, 76, 75, 76, 78, 72, 70, 73, 71, 72, 73, 67, 68, 62, 65, 66, 65, 71, 70, 69, 64, 69, 64, 68, 65,
				65, 65, 55, 55, 58, 58, 58, 60, 55, 58, 60, 61, 63, 64, 58, 50, 54, 57, 53, 57, 60, 62, 57, 50, 50, 54, 56, 56, 56, 48, 48, 50, 54, 55, 50, 50, 49, 46,
				50, 44, 41, 42, 44, 45, 45, 40, 38, 42, 38, 40, 42, 41, 38, 38, 44, 40, 39, 40, 37, 36, 40, 41, 34, 36, 26, 23, 24, 27, 27, 28, 34, 27, 24, 30, 30, 30,
				28, 28, 31, 28, 26, 25, 31, 28, 28, 32, 34, 32, 32, 28, 30, 34, 36, 34, 28, 35, 35, 36, 38, 36, 37, 46, 46, 42, 37, 37, 40, 40, 38, 34, 38, 39, 34, 34,
				38, 38, 37, 33, 32, 30, 37, 34, 32, 28, 28, 32, 33, 36, 38, 34, 34, 44, 36, 34, 37, 37, 38, 37, 38, 38, 44, 40, 40, 36, 40, 43, 40, 40, 42, 38, 40, 42,
				40, 44, 40, 37, 36, 36, 42, 39, 41, 43, 45, 44, 42, 43, 44, 46, 44, 53, 50, 51, 48, 52, 52, 46, 37, 39, 38, 36, 40, 45, 47, 50, 52, 54, 54, 54, 52, 48,
				56, 52, 53, 48, 48, 52, 58, 57, 49, 44, 42, 46, 42, 41, 49, 51, 52, 57, 58, 58, 59, 48, 46, 49, 47, 54, 53, 45, 48, 50, 55, 56, 57, 60, 60, 52, 48, 50,
				48, 48, 50, 53, 54, 61, 60, 63, 67, 58, 58, 60, 60, 56, 58, 58, 56, 52, 56, 60, 57, 58, 58, 62, 69, 71, 75, 76, 71, 74, 68, 64, 63, 62, 63, 62, 64, 64,
				64, 65, 66, 64, 62, 60, 64, 67, 58, 58, 58, 58, 62, 64, 66, 70, 68, 56, 54, 54, 60, 64, 64, 66, 66, 66, 69, 70, 70, 69, 63, 64, 69, 66, 68, 62, 63, 67,
				63, 62, 68, 65, 68, 66, 66, 66, 63, 67, 68, 70, 70, 62, 62, 61, 60, 56, 58, 57, 60, 61, 58, 58, 58, 54, 57, 54, 60, 64, 64, 64, 65, 58, 57, 56, 59, 64,
				66, 65, 65, 66, 66, 64, 58, 51, 59, 61, 60, 62, 62, 62, 65, 66, 62, 54, 50, 51, 54, 53, 56, 58, 62, 60, 63, 58, 56, 54, 58, 56, 56, 60, 62, 62, 60, 58,
				54, 55, 57, 58, 58, 56, 60, 60, 56, 42, 46, 45, 46, 52, 43, 49, 48, 50, 46, 48, 48, 46, 46, 38, 38, 46, 44, 38, 43, 44, 44, 49, 47, 49, 46, 42, 46, 42,
				36, 44, 39, 40, 46, 44, 46, 44, 40, 37, 36, 36, 38, 38, 38, 38, 37, 38, 34, 32, 34, 41, 42, 42, 46, 46, 44, 41, 45, 48, 42, 38, 36, 38, 36, 38, 33, 30,
				25, 14, 26, 24, 46, 49, 48, 47, 46, 46, 50, 49, 47, 46, 47, 44, 48, 48, 48, 53, 52, 52, 56, 56, 57, 53, 52, 52, 55, 42, 34, 42, 46, 34, 38, 41, 47, 48,
				45, 43, 40, 40, 49, 54, 54, 46, 54, 56, 54, 45, 46, 50, 52, 46, 55, 54, 47, 49, 46, 42, 42, 42, 44, 52, 55, 58, 54, 56, 54, 52, 48, 45, 46, 42, 45, 46,
				42, 48, 50, 54, 55, 58, 52, 55, 57, 52, 53, 56, 53, 57, 56, 50, 46, 44, 50, 54, 56, 57, 60, 58, 54, 55, 54, 60, 52, 50, 53, 52, 58, 62, 61, 56, 58, 60,
				59, 66, 66, 65, 60, 58, 60, 58, 60, 56, 59, 60, 62, 66, 62, 60, 58, 60, 56, 56, 60, 64, 66, 69, 66, 66, 65, 63, 66, 67, 69, 69, 68, 66, 68, 70, 72, 72,
				76, 68, 64, 66, 66, 66, 68, 66, 66, 70, 70, 73, 75, 73, 72, 71, 74, 76, 74, 75, 78, 76, 70, 70, 68, 72, 73, 76, 82, 80, 72, 73, 74, 74, 76, 74, 72, 75,
				74, 78, 79, 80, 78, 82, 80, 79, 78, 78, 78, 78, 77, 75, 77, 79, 82, 83, 78, 78, 80, 82, 84, 84, 83, 82, 83, 78, 78, 81, 83, 85, 76, 75, 76, 80, 82, 84,
				82, 84, 80, 86, 78, 75, 78, 78, 78, 82, 83, 80, 79, 79, 78, 78, 78, 78, 78, 76, 76, 80, 77, 80, 78, 76, 72, 74, 76, 74, 74, 76, 78, 76, 74, 70, 65, 68,
				67, 68, 69, 68, 61, 62, 66, 61, 62, 64, 64, 64, 67, 64, 64, 66, 63, 66, 68, 66, 66, 66, 62, 63, 62, 62, 68, 70, 66, 58, 54, 60, 64, 71, 66, 62, 57, 52,
				54, 60, 54, 51, 53, 50, 52, 52, 52, 58, 60, 61, 60, 59, 60, 60, 59, 60, 56, 55, 58, 60, 64, 54, 49, 44, 47, 48, 46, 44, 44, 46, 50, 48, 48, 52, 59, 56,
				51, 53, 58, 54, 54, 46, 50, 50, 50, 48, 48, 48, 55, 53, 49, 50, 48, 44, 50, 52, 56, 54, 45, 42, 46, 35, 38, 75, 80, 79, 78, 75, 72, 76, 80, 76, 72, 67,
				69, 73, 74, 74, 72, 70, 70, 74, 75, 76, 76, 76, 76, 78, 82, 81, 82, 77, 78, 80, 80, 80, 79, 78, 80, 76, 74, 74, 73, 72, 72, 70, 73, 76, 71, 72, 74, 78,
				80, 78, 78, 76, 76, 80, 80, 78, 80, 76, 73, 74, 78, 77, 79, 77, 78, 79, 80, 78, 76, 82, 84, 82, 84, 82, 88, 86, 84, 84, 82, 80, 79, 80, 82, 81, 81, 82,
				83, 85, 82, 81, 82, 83, 89, 84, 82, 81, 82, 82, 80, 81, 79, 80, 78, 79, 82, 82, 83, 82, 84, 91, 90, 89, 87, 84, 86, 84, 86, 84, 87, 84, 84, 86, 86, 86,
				86, 88, 88, 88, 86, 86, 85, 85, 84, 86, 86, 86, 86, 89, 89, 87, 86, 86, 86, 86, 86, 88, 87, 88, 88, 86, 87, 88, 90, 88, 88, 89, 89, 88, 90, 92, 92, 90,
				87, 86, 86, 85, 84, 87, 86, 88, 88, 88, 88, 87, 88, 88, 87, 88, 88, 88, 85, 80, 78, 81, 85, 86, 82, 80, 80, 81, 78, 76, 80, 82, 78, 79, 82, 82, 84, 84,
				83, 83, 81, 82, 84, 84, 80, 78, 80, 80, 78, 81, 81, 81, 80, 81, 80, 81, 80, 81, 82, 83, 82, 84, 82, 81, 82, 84, 82, 82, 82, 82, 81, 82, 82, 82, 80, 80,
				80, 80, 83, 79, 77, 81, 82, 83, 80, 82, 84, 82, 80, 80, 81, 82, 83, 83, 82, 80, 82, 81, 80, 82, 80, 82, 82, 82, 83, 82, 86, 86, 88, 86, 82, 84, 86, 85,
				85, 85, 83, 82, 84, 85, 86, 84, 84, 82, 84, 88, 88, 86, 84, 87, 88, 88, 88, 86, 80, 80, 80, 84, 82, 84, 82, 82, 82, 84, 84, 82, 83, 81, 82, 82, 85, 85,
				84, 86, 84, 82, 84, 86, 85, 86, 86, 82, 80, 80, 82, 82, 82, 79, 80, 80, 79, 78, 80, 77, 78, 82, 80, 77, 78, 78, 79, 78, 80, 80, 74, 67, 76, 78, 77, 78,
				78, 77, 74, 72, 73, 75, 76, 78, 76, 74, 72, 72, 76, 76, 78, 79, 80, 80, 81, 82, 82, 80, 80, 80, 77, 75, 74, 72, 74, 73, 70, 72, 72, 69, 70, 71, 74, 76,
				77, 78, 79, 79, 80, 80, 80, 82, 83, 83, 83, 83, 82, 82, 82, 82, 82, 82, 82, 82, 84, 84, 84, 85, 77, 76, 82, 83, 84, 83, 84, 84, 84, 82, 84, 84, 85, 84,
				85, 84, 86, 85, 85, 86, 86, 86, 86, 88, 86, 88, 88, 88, 88, 88, 80, 85, 86, 87, 88, 88, 88, 86, 89, 88, 88, 88, 90, 84, 84, 84, 89, 90, 88, 90, 86, 86,
				86, 86, 84, 86, 88, 88, 89, 90, 90, 90, 91, 90, 90, 90, 90, 84, 86, 88, 89, 84, 88, 86, 88, 86, 84, 88, 87, 88, 84, 90, 88, 88, 90, 92, 90, 84, 86, 88,
				89, 89, 89, 90, 90, 88, 89, 86, 89, 90, 90, 84, 87, 89, 86, 86, 86, 85, 86, 87, 86, 88, 84, 85, 88, 86, 86, 84, 85, 87, 86, 87, 84, 84, 86, 84, 82, 85,
				88, 90, 84, 83, 87, 88, 89, 89, 88, 87, 85, 85, 84, 87, 88, 86, 82, 84, 84, 86, 84, 82, 84, 86, 86, 86, 84, 86, 85, 84, 80, 86, 84, 84, 85, 84, 84, 84,
				85, 83, 82, 84, 85, 85, 85, 85, 85, 84, 84, 84, 84, 88, 84, 84, 82, 82, 86, 87, 85, 82, 83, 82, 84, 82, 81, 82, 84, 82, 84, 84, 84, 82, 86, 86, 86, 84,
				84, 86, 85, 85, 84, 84, 84, 86, 88, 88, 84, 88, 85, 84, 84, 84, 86, 83, 82, 84, 83, 84, 84, 85, 82, 81, 80, 82, 84, 83, 84, 84, 84, 83, 84, 84, 84, 83,
				82, 84, 84, 83, 82, 83, 84, 84, 84, 84, 85, 85, 84, 82, 84, 82, 82, 85, 84, 84, 85, 85, 87, 86, 84, 82, 83, 84, 82, 81, 79, 80, 80, 82, 84, 84, 84, 86,
				84, 84, 86, 86, 84, 82, 82, 82, 82, 84, 84, 82, 80, 82, 80, 82, 76, 78, 80, 79, 76, 76, 76, 78, 79, 76, 76, 78, 81, 82, 84, 82, 78, 77, 76, 68, 64, 69,
				69, 75, 82, 72, 72, 73, 78, 90, 91, 75, 70, 68, 71, 75, 78, 85, 85, 84, 86, 86, 82, 86, 80, 70, 74, 77, 79, 77, 74, 76, 78, 84, 80, 80, 78, 72, 76, 75,
				77, 80, 82, 78, 75, 72, 78, 80, 80, 84, 81, 84, 82, 76, 78, 73, 78, 80, 83, 80, 74, 76, 76, 79, 86, 74, 66, 69, 77, 83, 78, 68, 61, 64, 69, 74, 77, 76,
				72, 76, 80, 82, 76, 78, 73, 78, 69, 66, 66, 64, 64, 68, 69, 73, 66, 66, 70, 74, 80, 82, 78, 77, 75, 68, 68, 62, 68, 64, 64, 60, 58, 62, 66, 68, 65, 67,
				56, 55, 60, 56, 57, 58, 61, 62, 65, 65, 60, 62, 62, 64, 63, 61, 62, 63, 62, 62, 61, 64, 66, 64, 63, 64, 62, 60, 61, 62, 58, 57, 54, 55, 59, 62, 60, 60,
				64, 63, 62, 62, 66, 56, 50, 52, 49, 48, 52, 58, 54, 57, 56, 60, 58, 52, 47, 50, 52, 54, 50, 49, 50, 60, 63, 58, 48, 54, 58, 59, 57, 52, 48, 50, 54, 54,
				56, 54, 56, 52, 50, 54, 58, 54, 54, 53, 52, 56, 58, 56, 54, 57, 58, 59, 51, 50, 53, 53, 56, 62, 64, 62, 60, 58, 64, 64, 60, 63, 64, 60, 60, 65, 60, 61,
				60, 62, 57, 58, 58, 62, 60, 60, 64, 64, 58, 55, 54, 60, 59, 57, 65, 65, 62, 58, 60, 62, 59, 59, 59, 56, 60, 64, 67, 62, 62, 72, 62, 57, 51, 58, 60, 62,
				60, 61, 62, 62, 69, 70, 60, 62, 59, 56, 57, 57, 64, 60, 58, 59, 65, 68, 74, 72, 66, 66, 60, 60, 61, 74, 64, 64, 62, 64, 62, 71, 73, 64, 64, 68, 62, 62,
				65, 66, 62, 62, 62, 77, 67, 64, 64, 60, 60, 66, 68, 68, 67, 64, 63, 66, 64, 64, 68, 74, 80, 73, 68, 70, 68, 69, 69, 67, 66, 68, 68, 70, 70, 65, 68, 78,
				70, 66, 66, 64, 64, 65, 74, 80, 77, 72, 74, 74, 76, 74, 75, 78, 76, 78, 86, 74, };
	}
}
