package com.actonia.test;

import java.util.ArrayList;
import java.util.List;

import com.actonia.dao.ContentGuardClickHouseDAO;
import com.actonia.dao.ContentGuardSkipUrlDAO;
import com.actonia.entity.ContentGuardChangeTrackingEntity;
import com.actonia.entity.ContentGuardSkipUrlEntity;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.service.ContentGuardService;
import com.actonia.utils.ContentGuardUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.ContentGuardResourceResponse;
import com.actonia.value.object.PageAnalysisResult;
import com.google.gson.Gson;

public class ContentGuardServiceTest {

	private ContentGuardService contentGuardService;
	private ContentGuardSkipUrlDAO contentGuardSkipUrlDAO;

	public ContentGuardServiceTest() {
		super();
		contentGuardService = SpringBeanFactory.getBean("contentGuardService");
		contentGuardSkipUrlDAO = SpringBeanFactory.getBean("contentGuardSkipUrlDAO");
	}

	public static void main(String[] args) throws Exception {
		new ContentGuardServiceTest().runTests();
	}

	private void runTests() throws Exception {
		//testGetChangeTrackingSummaryList();
		//testGetChangeTrackingSummaryList();
		//testGetChangeTrackingSummaryList();

		//testFormatNumberAsStringWithLeadingZeros();
		//testGetContentGuardChangeTrackingFields();
		//testCreateContentGuardSkipUrl();
		//testDeleteContentGuardSkipUrl();
		//testGetContentGuardSkipUrlList();
		//testDeleteContentGuardSkipUrlByDomainId();

		//int domainId = 9632; // no non-tracking fields
		//String crawlDate = "2020-10-24";
		//String crawlHour = "19";

		//testGetChangeSummary(9632, "1168-12-09", 19);
		//testGetChangeSummary(9632, "2020-10-24", 19);
		//testGetChangeSummary(9632, "2020-10-24", null);
		//testGetChangeSummary(9632, null, null);
		//testGetChangeDetails(9632, "https://test.edgeseo.dev/AI_Rule_36.html", "2020-10-24 19:42:49");
		//testGetChangeDetails(9632, "https://test.edgeseo.dev/ai_rule_46.html", "2020-10-24 19:43:27");
		//testGetChangeDetails(9632, "https://test.edgeseo.dev/ai_rule_54.html", "2020-10-24 19:43:11");
		//testGetChangeDetails(9632, "https://test.edgeseo.dev/ai_rule_60.html", "2020-10-24 19:42:42");
		//testGetChangeDetails(9632, "https://test.edgeseo.dev/ai_rule_61.html", "2020-10-24 19:43:38");
		//testGetChangeDetails(9632, "https://test.edgeseo.dev/ai_rule_62.html", "2020-10-24 19:43:34");

		//testGetChangeSummary(7436, "1168-12-09", 6);
		//testGetChangeSummary(7436, "2020-10-09", 6);
		//testGetChangeSummary(7436, "2020-10-09", null);
		//testGetChangeSummary(7436, null, null);
		//testGetChangeDetails(7436, "http://www.nolo.com/products/101-law-forms-for-personal-use-SPOT.html", "2020-10-09 06:16:27");
		//testGetChangeDetails(7436, "https://www.nolo.com/legal-encyclopedia/can-i-chickens-goats-backyard.html", "2020-10-09 05:58:17");
		//testGetChangeDetails(7436,
		//		"https://store.nolo.com/products/becoming-a-us-citizen-uscit.html?utm_medium=nolo&utm_source=nolo-content&utm_content=pid%7c381673%7c%7cpa%7clawful+permanent+residents+seeking+citizenship%7c%7cref_src%7chttp%3a%2f%2fwww.nolo.com%252&utm_campaign=nolo-related-products",
		//		"2020-10-09 05:55:26");

		//testGetChangeSummary(7006, "2020-04-12", null);
		//testGetChangeSummary(9672, "2020-10-30", null);
		//testGetChangeSummary(9511, "2020-10-31", null);
		//testGetChangeSummary(9672, "2020-10-24", null);
		//testGetChangeSummary(7465, "2020-04-02", null);
		//testGetChangeSummary(9408, "2020-10-15", null);
		//testGetChangeSummary(9672, "2020-10-22", null);
		//testGetChangeSummary(7159, "2020-11-01", null);
		//testGetChangeSummary(7255, "2020-10-27", null);
		//testGetChangeSummary(7583, "2020-10-28", null);

		//testGetIndicatorUrlsList(9678, 1L, null, null, 1, 10);
		//testGetIndicatorUrlsList(256, 7L, null, null, 1, 10);
		//testGetIndicatorUrlsList(6060, 26L, null, null, 1, 10);
		//testGetIndicatorUrlsList(7048, 27L, null, null, 1, 10);
		//testGetIndicatorUrlsList(8387, 28L, null, null, 1, 10);
		//testGetIndicatorUrlsList(8422, 29L, null, null, 1, 10);
		//testGetIndicatorUrlsList(8443, 30L, null, null, 1, 10);
		//testGetIndicatorUrlsList(8625, 31L, null, null, 1, 10);
		//testGetIndicatorUrlsList(8773, 32L, null, null, 1, 10);
		//testGetIndicatorUrlsList(9511, 33L, null, null, 1, 10);
		//testGetIndicatorUrlsList(9676, 34L, null, null, 1, 10);
		//testGetIndicatorUrlsList(9678, 35L, null, null, 1, 10);
		//testGetIndicatorUrlsList(9803, 36L, null, null, 1, 10);			

		//testGetChangeDetails(9632, "https://emplois.ca.indeed.com/Montr%C3%A9al,-QC-Emplois-Psychologue", "2021-01-15 00:37:09");
		//testContentGuardClickHouseDAOGetPageAnalysisResultsList();
		//testGetUsage1();
		//testGetUsage2();
		testGetUsage3();
	}

	private void testGetContentGuardChangeTrackingFields() {
		int domainId = 7436;
		List<ContentGuardChangeTrackingEntity> contentGuardChangeTrackingEntityList = ContentGuardUtils.getInstance().getContentGuardChangeTrackingEntityList();
		if (contentGuardChangeTrackingEntityList != null && contentGuardChangeTrackingEntityList.size() > 0) {
			for (ContentGuardChangeTrackingEntity contentGuardChangeTrackingEntity : contentGuardChangeTrackingEntityList) {
				System.out.println("contentGuardChangeTrackingEntity=" + contentGuardChangeTrackingEntity.toString());
			}
		} else {
			System.out.println("contentGuardChangeTrackingEntityList is empty.");
		}
	}

	//int domainId = 256; // two non-tracking fields
	//String crawlDate = "2020-10-28";
	//String crawlHour = null;

	// 513 URLs changed
	//int domainId = 7529; 
	//String crawlDate = "2020-05-06";
	//String crawlHour = null;

	//int domainId = 9632; // no non-tracking fields
	//String crawlDate = "2020-10-24";
	//String crawlHour = "19";
	private void testGetChangeSummary(int domainId, String crawlDate, Integer crawlHour) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		System.out.println("testGetChangeSummary() begins. domainId=" + domainId + ",crawlDate=" + crawlDate + ",crawlHour=" + crawlHour);
		String json = null;
		Long groupId = null;
		int pageNumber = 1;
		int rowsPerPage = 10;
		int sortBy = 1;
		String changeIndicator = null;
		ContentGuardResourceResponse response = contentGuardService.getDomainSummary(domainId, groupId, crawlDate, crawlHour, pageNumber, rowsPerPage, sortBy, null,
				null, null);
		if (response != null) {
			json = new Gson().toJson(response, ContentGuardResourceResponse.class);
			System.out.println("json=" + json);
		} else {
			System.out.println("response is empty.");
		}
		System.out.println("testGetChangeSummary() ends. domainId=" + domainId + ",crawlDate=" + crawlDate + ",crawlHour=" + crawlHour + ",elapsed(ms.)="
				+ (System.currentTimeMillis() - startTimestamp));
	}

	private void testGetChangeDetails(int domainId, String urlString, String crawlTimestamp) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		System.out.println("testGetChangeDetails() begins. domainId=" + domainId + ",urlString=" + urlString + ",crawlTimestamp=" + crawlTimestamp);
		String json = null;
		Long groupId = null;
		ContentGuardResourceResponse response = contentGuardService.getUrlDetails(domainId, groupId, urlString, crawlTimestamp);
		if (response != null) {
			json = new Gson().toJson(response, ContentGuardResourceResponse.class);
			System.out.println("json=" + json);
		} else {
			System.out.println("response is empty.");
		}
		System.out.println("testGetChangeDetails() ends. domainId=" + domainId + ",urlString=" + urlString + ",crawlTimestamp=" + crawlTimestamp + ",elapsed(ms.)="
				+ (System.currentTimeMillis() - startTimestamp));
	}

	private void testFormatNumberAsStringWithLeadingZeros() {
		int inputNumber = 23;
		System.out.println("inputNumber=" + inputNumber);
		String outputString = String.format("%02d", inputNumber);
		System.out.println("outputString=" + outputString);
	}

	private void testCreateContentGuardSkipUrl() {
		Long groupId = 5L;
		ContentGuardSkipUrlEntity contentGuardSkipUrlEntity = new ContentGuardSkipUrlEntity();
		contentGuardSkipUrlEntity.setDomainId(2);
		contentGuardSkipUrlEntity.setGroupId(groupId);
		contentGuardSkipUrlEntity.setIndicator("3");
		contentGuardSkipUrlEntity.setUrlSelectorType(4);
		//contentGuardSkipUrlEntity.setUrlSelector("urlSelector");
		Long id = contentGuardSkipUrlDAO.create(contentGuardSkipUrlEntity);
		System.out.println("id=" + id);
	}

	private void testDeleteContentGuardSkipUrl() {
		int domainId = 2;
		String indicator = "3";
		int urlSelectorType = 4;
		Long groupId = 5L;
		String urlSelector = null;
		//String urlSelector = "urlSelector";
		int totalDeleted = contentGuardSkipUrlDAO.delete(domainId, groupId, indicator, urlSelectorType, urlSelector);
		System.out.println("totalDeleted=" + totalDeleted);
	}

	private void testGetContentGuardSkipUrlList() {
		int domainId = 2;
		Long groupId = 5L;
		List<ContentGuardSkipUrlEntity> contentGuardSkipUrlEntityList = contentGuardSkipUrlDAO.getList(domainId, groupId);
		if (contentGuardSkipUrlEntityList != null) {
			for (ContentGuardSkipUrlEntity contentGuardSkipUrlEntity : contentGuardSkipUrlEntityList) {
				System.out.println("contentGuardSkipUrlEntity=" + contentGuardSkipUrlEntity.toString());
			}
		} else {
			System.out.println("contentGuardSkipUrlEntityList is null.");
		}
	}

	private void testDeleteContentGuardSkipUrlByDomainId() {
		int domainId = 2;
		Long groupId = 5L;
		int totalDeleted = contentGuardSkipUrlDAO.delete(domainId, groupId);
		System.out.println("totalDeleted=" + totalDeleted);
	}

	private void testGetChangeTrackingSummaryList() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		int domainId = 7436;
		String startCrawlTimestamp = "2020-11-09 00:00:00";
		String endCrawlTimestamp = "2020-11-15 23:59:59";
		List<String> changeTrackingIndicatorList = null;
		List<ContentGuardChangeTrackingEntity> contentGuardChangeTrackingEntityList = null;
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = null;

		contentGuardChangeTrackingEntityList = ContentGuardUtils.getInstance().getContentGuardChangeTrackingEntityList();
		if (contentGuardChangeTrackingEntityList != null && contentGuardChangeTrackingEntityList.size() > 0) {
			changeTrackingIndicatorList = new ArrayList<String>();
			for (ContentGuardChangeTrackingEntity contentGuardChangeTrackingEntity : contentGuardChangeTrackingEntityList) {
				changeTrackingIndicatorList.add(contentGuardChangeTrackingEntity.getIndicator());
			}
			htmlClickHouseEntityList = ContentGuardClickHouseDAO.getInstance().getChangeTrackingSummaryList(domainId, startCrawlTimestamp, endCrawlTimestamp,
					changeTrackingIndicatorList);
			if (htmlClickHouseEntityList != null && htmlClickHouseEntityList.size() > 0) {
				System.out.println("htmlClickHouseEntityList.size()=" + htmlClickHouseEntityList.size());
			} else {
				System.out.println("htmlClickHouseEntityList is null.");
			}
		}
		System.out.println("elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private void testGetIndicatorUrlsList(int domainId, Long groupId, String crawlDate, Integer crawlHour, Integer pageNumber, Integer rowsPerPage) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		System.out.println("testGetIndicatorUrlsList() begins. domainId=" + domainId + ",crawlDate=" + crawlDate + ",crawlHour=" + crawlHour);
		String json = null;
		Boolean returnDetails = true;
		ContentGuardResourceResponse response = contentGuardService.getIndicatorUrlList(domainId, groupId, crawlDate, crawlHour, pageNumber, rowsPerPage, returnDetails,
				null);
		if (response != null) {
			json = new Gson().toJson(response, ContentGuardResourceResponse.class);
			System.out.println("json=" + json);
		} else {
			System.out.println("response is empty.");
		}
		System.out.println("testGetIndicatorUrlsList() ends. domainId=" + domainId + ",crawlDate=" + crawlDate + ",crawlHour=" + crawlHour + ",elapsed(ms.)="
				+ (System.currentTimeMillis() - startTimestamp));
	}

	private void testContentGuardClickHouseDAOGetPageAnalysisResultsList() throws Exception {
		int domainId = 9678;
		String startCrawlTimestamp = "2021-04-05 00:00:00";
		String endCrawlTimestamp = "2021-04-05 23:59:59";
		String urlString = null;
		PageAnalysisResult[] pageAnalysisResultArray = null;
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = ContentGuardClickHouseDAO.getInstance().getPageAnalysisResultsList(domainId, startCrawlTimestamp,
				endCrawlTimestamp);
		if (htmlClickHouseEntityList != null && htmlClickHouseEntityList.size() > 0) {
			for (HtmlClickHouseEntity htmlClickHouseEntity : htmlClickHouseEntityList) {
				urlString = htmlClickHouseEntity.getUrl();
				System.out.println("urlString=" + urlString);
				pageAnalysisResultArray = htmlClickHouseEntity.getPageAnalysisResultArray();
				if (pageAnalysisResultArray != null && pageAnalysisResultArray.length > 0) {
					for (PageAnalysisResult pageAnalysisResult : pageAnalysisResultArray) {
						if (pageAnalysisResult.getResult() == 1) {
							System.out.println("   rule number=" + pageAnalysisResult.getRule() + ",rule result=" + pageAnalysisResult.getResult());
						}
					}
				}
			}
		}
	}

	private void testGetUsage1() throws Exception {
		int domainId = 256;
		String startUsageDateString = null;
		String endUsageDateString = null;
		Long groupId = null;
		System.out.println("testGetUsage1() domainId=" + domainId + ",startUsageDateString=" + startUsageDateString + ",endUsageDateString=" + endUsageDateString
				+ ",groupId=" + groupId);
		ContentGuardResourceResponse contentGuardResourceResponse = contentGuardService.getUsage(domainId, startUsageDateString, endUsageDateString, groupId);
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		System.out.println("testGetUsage1() json=" + json);
	}

	private void testGetUsage2() throws Exception {
		int domainId = 256;
		String startUsageDateString = "2022-02-21";
		String endUsageDateString = "2022-02-21";
		Long groupId = null;
		System.out.println("testGetUsage2() domainId=" + domainId + ",startUsageDateString=" + startUsageDateString + ",endUsageDateString=" + endUsageDateString
				+ ",groupId=" + groupId);
		ContentGuardResourceResponse contentGuardResourceResponse = contentGuardService.getUsage(domainId, startUsageDateString, endUsageDateString, groupId);
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		System.out.println("testGetUsage2() json=" + json);
	}

	private void testGetUsage3() throws Exception {
		int domainId = 256;
		String startUsageDateString = "2022-02-01";
		String endUsageDateString = "2022-02-28";
		Long groupId = 220L;
		System.out.println("testGetUsage3() domainId=" + domainId + ",startUsageDateString=" + startUsageDateString + ",endUsageDateString=" + endUsageDateString
				+ ",groupId=" + groupId);
		ContentGuardResourceResponse contentGuardResourceResponse = contentGuardService.getUsage(domainId, startUsageDateString, endUsageDateString, groupId);
		String json = new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class);
		System.out.println("testGetUsage3() json=" + json);
	}
}
