package com.actonia.concurrency;

import java.util.concurrent.TimeUnit;

import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;

public class ThreadPoolParameter {

	public static final int CONFIG_POOL_SECOND_CODE = 1;

	public static final int CONFIG_POOL_MILLISECOND_CODE = 2;

	public static final int CONFIG_POOL_MICIOSECOND_CODE = 3;

	public static final int CONFIG_POOL_NSECOND_CODE = 4;

	private static final String THREADPOOL_XML = "/threadPool.xml";

	private static final String MAXIMUMPOOLSIZES = "MaximumPoolSizes";

	private static final String MINPOOLSIZES = "MinPoolSizes";

	private static final String QUEUECAPACITY = "QueueCapacity";

	private static final String KEEPALIVETIME = "KeepAliveTime";

	private static final String TIMEUNIT = "TimeUnit";

	static Element root;
	static {
		SAXReader r = new SAXReader();
		Document d;
		try {
			d = r.read(ThreadPoolParameter.class.getResourceAsStream(THREADPOOL_XML));
			root = d.getRootElement();
		} catch (DocumentException e) {
			e.printStackTrace();
		}
	}

	public static String getSubNode(String nodeNm) {
		return root.element(nodeNm).getText();
	}

	public static int getMaximumPoolSizes() {

		return Integer.valueOf(getSubNode(MAXIMUMPOOLSIZES));
	}

	public static int getMinPoolSizes() {

		return Integer.valueOf(getSubNode(MINPOOLSIZES));
	}

	public static int getQueueCapacity() {
		return Integer.valueOf(getSubNode(QUEUECAPACITY));
	}

	public static int getKeepAliveTime() {
		return Integer.valueOf(getSubNode(KEEPALIVETIME));
	}

	public static TimeUnit getTimeUnit() {
		int tUnit = Integer.valueOf(getSubNode(TIMEUNIT));
		if (CONFIG_POOL_MICIOSECOND_CODE == tUnit) {
			return TimeUnit.MICROSECONDS;
		} else if (CONFIG_POOL_MILLISECOND_CODE == tUnit) {
			return TimeUnit.MILLISECONDS;
		} else if (CONFIG_POOL_NSECOND_CODE == tUnit) {
			return TimeUnit.NANOSECONDS;
		} else {
			return TimeUnit.SECONDS;
		}
	}
}
