package com.actonia.concurrency;

import java.math.BigInteger;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.CopyOnWriteArrayList;

import com.actonia.IConstants;
import com.actonia.entity.AssociatedCompetitorUrlAuditTrailEntity;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.RobotsTxtClickHouseEntity;
import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
import com.actonia.value.object.AdditionalContentFilterValueObject;
import com.actonia.value.object.AssociateTopRankedTargetUrlSummaryValueObject;
import com.actonia.value.object.PageClarityUpdateSummaryValueObject;
import com.actonia.value.object.TargetUrlMetricsUpdateReportValueObject;

public class CacheModleFactory {

	private static CacheModleFactory cacheModleFactory;

	private Map<String, String> ipMap;

	private List<String> urlCrawlQueueList;

	private ConcurrentMap<String, String> targetUrlSharedCountsConcurrentMap = new ConcurrentHashMap<String, String>();

	private List<String> urlPrioritySharedCountsCrawlQueueList;

	private int totalPriorityMessages;

	// map key = domain ID
	// map value = map of URL hash to URL string map
	private Map<Integer, Map<BigInteger, String>> domainIdCityHashUrlStringMapMap = new HashMap<Integer, Map<BigInteger, String>>();	

	private Integer totalUrlsInClickHouse = null;
	private Integer totalUrlsNotInClickHouse = null;
	private Integer totalDailyRecordsCreated = null;
	private Integer totalLinkGained = null;
	private Integer totalLinkLost = null;

	private int cacheHitCount = 0;

	private TargetUrlMetricsUpdateReportValueObject targetUrlMetricsUpdateReportValueObject;

	private List<PageClarityUpdateSummaryValueObject> pageClarityUpdateSummaryValueObjectList;

	private Set<String> uniqueHashCodeSet = new HashSet<String>();

	// map key = URL hash code
	// map value = HtmlClickHouseEntity
	private Map<String, HtmlClickHouseEntity> urlHashCodeHtmlClickHouseEntityMap = new HashMap<String, HtmlClickHouseEntity>();

	private ConcurrentMap<String, Boolean> concurrentBooleanMap = new ConcurrentHashMap<String, Boolean>();

	// map key = domain ID
	// map value = List of additional content (to be tracked) value object per client domain 
	private Map<Integer, List<AdditionalContentFilterValueObject>> domainIdAdditionalContentFilterListMap = new HashMap<Integer, List<AdditionalContentFilterValueObject>>();

	private List<AssociateTopRankedTargetUrlSummaryValueObject> associateTopRankedTargetUrlSummaryValueObjectList;

	private List<AssociatedCompetitorUrlAuditTrailEntity> associatedCompetitorUrlAuditTrailEntityList;

	// map key = domain ID
	// map value = total number of HTTP status code 403
	private ConcurrentMap<Integer, Integer> domainIdTotalForbiddenMap = new ConcurrentHashMap<Integer, Integer>();
	
	private List<RobotsTxtClickHouseEntity> robotsTxtClickHouseEntityList = new CopyOnWriteArrayList<RobotsTxtClickHouseEntity>();
	
	private List<TargetUrlChangeIndClickHouseEntity> targetUrlChangeIndClickHouseEntityList = new CopyOnWriteArrayList<TargetUrlChangeIndClickHouseEntity>();

	public Map<String, String> getIpMap() {
		return ipMap;
	}

	private CacheModleFactory() {
		ipMap = new HashMap<String, String>();
	}

	public static synchronized CacheModleFactory getInstance() {
		if (cacheModleFactory == null) {
			cacheModleFactory = new CacheModleFactory();
		}
		return cacheModleFactory;
	}

	public String getAliveIpAddress() {
		Map<String, String> ipMap = getIpMap();
		String status = null;
		for (String ipAddress : ipMap.keySet()) {
			status = ipMap.get(ipAddress);
			if (IConstants.IP_STATUS_ALIVE.equals(status)) {
				ipMap.put(ipAddress, IConstants.IP_STATUS_WORKING);
				return ipAddress;
			}
		}
		return null;
	}

	public void setAliveIpAddress(String ip) {
		ipMap.put(ip, IConstants.IP_STATUS_ALIVE);
	}

	public List<String> getUrlCrawlQueueList() {
		return urlCrawlQueueList;
	}

	public void setUrlCrawlQueueList(List<String> urlCrawlQueueList) {
		this.urlCrawlQueueList = urlCrawlQueueList;
	}

	public ConcurrentMap<String, String> getTargetUrlSharedCountsConcurrentMap() {
		return targetUrlSharedCountsConcurrentMap;
	}

	public void setTargetUrlSharedCountsConcurrentMap(ConcurrentMap<String, String> targetUrlSharedCountsConcurrentMap) {
		this.targetUrlSharedCountsConcurrentMap = targetUrlSharedCountsConcurrentMap;
	}

	public List<String> getUrlPrioritySharedCountsCrawlQueueList() {
		return urlPrioritySharedCountsCrawlQueueList;
	}

	public void setUrlPrioritySharedCountsCrawlQueueList(List<String> urlPrioritySharedCountsCrawlQueueList) {
		this.urlPrioritySharedCountsCrawlQueueList = urlPrioritySharedCountsCrawlQueueList;
	}

	public int getTotalPriorityMessages() {
		return totalPriorityMessages;
	}

	public void setTotalPriorityMessages(int totalPriorityMessages) {
		this.totalPriorityMessages = totalPriorityMessages;
	}

	public Map<Integer, Map<BigInteger, String>> getDomainIdCityHashUrlStringMapMap() {
		return domainIdCityHashUrlStringMapMap;
	}

	public void setDomainIdCityHashUrlStringMapMap(Map<Integer, Map<BigInteger, String>> domainIdCityHashUrlStringMapMap) {
		this.domainIdCityHashUrlStringMapMap = domainIdCityHashUrlStringMapMap;
	}

	public Integer getTotalDailyRecordsCreated() {
		return totalDailyRecordsCreated;
	}

	public void setTotalDailyRecordsCreated(Integer totalDailyRecordsCreated) {
		this.totalDailyRecordsCreated = totalDailyRecordsCreated;
	}

	public synchronized void incrementTotalUrlsInClickHouse() {
		totalUrlsInClickHouse++;
	}

	public synchronized void incrementTotalUrlsNotInClickHouse() {
		totalUrlsNotInClickHouse++;
	}

	public synchronized void incrementTotalDailyRecordsCreated() {
		totalDailyRecordsCreated++;
	}

	public Integer getTotalUrlsInClickHouse() {
		return totalUrlsInClickHouse;
	}

	public void setTotalUrlsInClickHouse(Integer totalUrlsInClickHouse) {
		this.totalUrlsInClickHouse = totalUrlsInClickHouse;
	}

	public Integer getTotalUrlsNotInClickHouse() {
		return totalUrlsNotInClickHouse;
	}

	public void setTotalUrlsNotInClickHouse(Integer totalUrlsNotInClickHouse) {
		this.totalUrlsNotInClickHouse = totalUrlsNotInClickHouse;
	}

	public int getCacheHitCount() {
		return cacheHitCount;
	}

	public void setCacheHitCount(int cacheHitCount) {
		this.cacheHitCount = cacheHitCount;
	}

	public synchronized void incrementCacheHitCount() {
		cacheHitCount++;
	}

	public Integer getTotalLinkGained() {
		return totalLinkGained;
	}

	public void setTotalLinkGained(Integer totalLinkGained) {
		this.totalLinkGained = totalLinkGained;
	}

	public Integer getTotalLinkLost() {
		return totalLinkLost;
	}

	public void setTotalLinkLost(Integer totalLinkLost) {
		this.totalLinkLost = totalLinkLost;
	}

	public synchronized void incrementTotalLinkGained() {
		totalLinkGained++;
	}

	public synchronized void incrementTotalLinkLost() {
		totalLinkLost++;
	}

	public TargetUrlMetricsUpdateReportValueObject getTargetUrlMetricsUpdateReportValueObject() {
		return targetUrlMetricsUpdateReportValueObject;
	}

	public void setTargetUrlMetricsUpdateReportValueObject(TargetUrlMetricsUpdateReportValueObject targetUrlMetricsUpdateReportValueObject) {
		this.targetUrlMetricsUpdateReportValueObject = targetUrlMetricsUpdateReportValueObject;
	}

	public List<PageClarityUpdateSummaryValueObject> getPageClarityUpdateSummaryValueObjectList() {
		return pageClarityUpdateSummaryValueObjectList;
	}

	public void setPageClarityUpdateSummaryValueObjectList(List<PageClarityUpdateSummaryValueObject> pageClarityUpdateSummaryValueObjectList) {
		this.pageClarityUpdateSummaryValueObjectList = pageClarityUpdateSummaryValueObjectList;
	}

	public synchronized void updateTargetUrlMetricsUpdateReportValueObject(int totalTargetUrlsRetrieved, int totalTargetUrlsProcessed,
			int totalTargetUrlsFoundInDatabase, int totalTargetUrlsNotFoundInDatabase, int totalUrlMetricsDataEntityFound, int totalUrlMetricsDataEntityUpdated,
			int totalUrlMetricsDataEntityNotUpdated, int totalUrlMetricsDataEntityNotFound, int totalUrlMetricsDataEntityCreated, int totalTargetUrlCriteriaFound,
			int totalTargetUrlCriteriaUpdated, int totalTargetUrlCriteriaNotUpdated, int totalTargetUrlCriteriaNotFound, int totalTargetUrlCriteriaCreated,
			int totalInternalLinkCountCalculated) {
		targetUrlMetricsUpdateReportValueObject
				.setTotalTargetUrlsRetrieved(targetUrlMetricsUpdateReportValueObject.getTotalTargetUrlsRetrieved() + totalTargetUrlsRetrieved);
		targetUrlMetricsUpdateReportValueObject
				.setTotalTargetUrlsProcessed(targetUrlMetricsUpdateReportValueObject.getTotalTargetUrlsProcessed() + totalTargetUrlsProcessed);
		targetUrlMetricsUpdateReportValueObject
				.setTotalTargetUrlsFoundInDatabase(targetUrlMetricsUpdateReportValueObject.getTotalTargetUrlsFoundInDatabase() + totalTargetUrlsFoundInDatabase);
		targetUrlMetricsUpdateReportValueObject.setTotalTargetUrlsNotFoundInDatabase(
				targetUrlMetricsUpdateReportValueObject.getTotalTargetUrlsNotFoundInDatabase() + totalTargetUrlsNotFoundInDatabase);
		targetUrlMetricsUpdateReportValueObject
				.setTotalUrlMetricsDataEntityFound(targetUrlMetricsUpdateReportValueObject.getTotalUrlMetricsDataEntityFound() + totalUrlMetricsDataEntityFound);
		targetUrlMetricsUpdateReportValueObject
				.setTotalUrlMetricsDataEntityUpdated(targetUrlMetricsUpdateReportValueObject.getTotalUrlMetricsDataEntityUpdated() + totalUrlMetricsDataEntityUpdated);
		targetUrlMetricsUpdateReportValueObject.setTotalUrlMetricsDataEntityNotUpdated(
				targetUrlMetricsUpdateReportValueObject.getTotalUrlMetricsDataEntityNotUpdated() + totalUrlMetricsDataEntityNotUpdated);
		targetUrlMetricsUpdateReportValueObject.setTotalUrlMetricsDataEntityNotFound(
				targetUrlMetricsUpdateReportValueObject.getTotalUrlMetricsDataEntityNotFound() + totalUrlMetricsDataEntityNotFound);
		targetUrlMetricsUpdateReportValueObject
				.setTotalUrlMetricsDataEntityCreated(targetUrlMetricsUpdateReportValueObject.getTotalUrlMetricsDataEntityCreated() + totalUrlMetricsDataEntityCreated);
		targetUrlMetricsUpdateReportValueObject
				.setTotalTargetUrlCriteriaFound(targetUrlMetricsUpdateReportValueObject.getTotalTargetUrlCriteriaFound() + totalTargetUrlCriteriaFound);
		targetUrlMetricsUpdateReportValueObject
				.setTotalTargetUrlCriteriaUpdated(targetUrlMetricsUpdateReportValueObject.getTotalTargetUrlCriteriaUpdated() + totalTargetUrlCriteriaUpdated);
		targetUrlMetricsUpdateReportValueObject
				.setTotalTargetUrlCriteriaNotUpdated(targetUrlMetricsUpdateReportValueObject.getTotalTargetUrlCriteriaNotUpdated() + totalTargetUrlCriteriaNotUpdated);
		targetUrlMetricsUpdateReportValueObject
				.setTotalTargetUrlCriteriaNotFound(targetUrlMetricsUpdateReportValueObject.getTotalTargetUrlCriteriaNotFound() + totalTargetUrlCriteriaNotFound);
		targetUrlMetricsUpdateReportValueObject
				.setTotalTargetUrlCriteriaCreated(targetUrlMetricsUpdateReportValueObject.getTotalTargetUrlCriteriaCreated() + totalTargetUrlCriteriaCreated);
		targetUrlMetricsUpdateReportValueObject
				.setTotalInternalLinkCountCalculated(targetUrlMetricsUpdateReportValueObject.getTotalInternalLinkCountCalculated() + totalInternalLinkCountCalculated);

	}

	public Set<String> getUniqueHashCodeSet() {
		return uniqueHashCodeSet;
	}

	public void setUniqueHashCodeSet(Set<String> uniqueHashCodeSet) {
		this.uniqueHashCodeSet = uniqueHashCodeSet;
	}

	public Map<String, HtmlClickHouseEntity> getUrlHashCodeHtmlClickHouseEntityMap() {
		return urlHashCodeHtmlClickHouseEntityMap;
	}

	public void setUrlHashCodeHtmlClickHouseEntityMap(Map<String, HtmlClickHouseEntity> urlHashCodeHtmlClickHouseEntityMap) {
		this.urlHashCodeHtmlClickHouseEntityMap = urlHashCodeHtmlClickHouseEntityMap;
	}

	public ConcurrentMap<String, Boolean> getConcurrentBooleanMap() {
		return concurrentBooleanMap;
	}

	public void setConcurrentBooleanMap(ConcurrentMap<String, Boolean> concurrentBooleanMap) {
		this.concurrentBooleanMap = concurrentBooleanMap;
	}

	public Map<Integer, List<AdditionalContentFilterValueObject>> getDomainIdAdditionalContentFilterListMap() {
		return domainIdAdditionalContentFilterListMap;
	}

	public void setDomainIdAdditionalContentFilterListMap(Map<Integer, List<AdditionalContentFilterValueObject>> domainIdAdditionalContentFilterListMap) {
		this.domainIdAdditionalContentFilterListMap = domainIdAdditionalContentFilterListMap;
	}

	public void setIpMap(Map<String, String> ipMap) {
		this.ipMap = ipMap;
	}

	public List<AssociateTopRankedTargetUrlSummaryValueObject> getAssociateTopRankedTargetUrlSummaryValueObjectList() {
		return associateTopRankedTargetUrlSummaryValueObjectList;
	}

	public void setAssociateTopRankedTargetUrlSummaryValueObjectList(
			List<AssociateTopRankedTargetUrlSummaryValueObject> associateTopRankedTargetUrlSummaryValueObjectList) {
		this.associateTopRankedTargetUrlSummaryValueObjectList = associateTopRankedTargetUrlSummaryValueObjectList;
	}

	public static CacheModleFactory getCacheModleFactory() {
		return cacheModleFactory;
	}

	public static void setCacheModleFactory(CacheModleFactory cacheModleFactory) {
		CacheModleFactory.cacheModleFactory = cacheModleFactory;
	}

	public List<AssociatedCompetitorUrlAuditTrailEntity> getAssociatedCompetitorUrlAuditTrailEntityList() {
		return associatedCompetitorUrlAuditTrailEntityList;
	}

	public void setAssociatedCompetitorUrlAuditTrailEntityList(List<AssociatedCompetitorUrlAuditTrailEntity> associatedCompetitorUrlAuditTrailEntityList) {
		this.associatedCompetitorUrlAuditTrailEntityList = associatedCompetitorUrlAuditTrailEntityList;
	}

	public ConcurrentMap<Integer, Integer> getDomainIdTotalForbiddenMap() {
		return domainIdTotalForbiddenMap;
	}

	public void setDomainIdTotalForbiddenMap(ConcurrentMap<Integer, Integer> domainIdTotalForbiddenMap) {
		this.domainIdTotalForbiddenMap = domainIdTotalForbiddenMap;
	}

	public List<RobotsTxtClickHouseEntity> getRobotsTxtClickHouseEntityList() {
		return robotsTxtClickHouseEntityList;
	}

	public void setRobotsTxtClickHouseEntityList(List<RobotsTxtClickHouseEntity> robotsTxtClickHouseEntityList) {
		this.robotsTxtClickHouseEntityList = robotsTxtClickHouseEntityList;
	}

	public List<TargetUrlChangeIndClickHouseEntity> getTargetUrlChangeIndClickHouseEntityList() {
		return targetUrlChangeIndClickHouseEntityList;
	}

	public void setTargetUrlChangeIndClickHouseEntityList(List<TargetUrlChangeIndClickHouseEntity> targetUrlChangeIndClickHouseEntityList) {
		this.targetUrlChangeIndClickHouseEntityList = targetUrlChangeIndClickHouseEntityList;
	}

}
