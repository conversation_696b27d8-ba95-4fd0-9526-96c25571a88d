package com.actonia.concurrency;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class ThreadPoolService {
	private final int time = 1000;

	private static ThreadPoolService threadPoolService;

	private static ThreadPoolExecutor threadPool;

	private static LinkedBlockingQueue<BaseThreadCommand> taskQueue = new LinkedBlockingQueue<BaseThreadCommand>();

	private int maximumPoolSizes;

	private int minPoolSizes;

	private int queueCapacity;

	private int keepAliveTime;

	private TimeUnit timeUnit;

	ListenerThread taskThread = new ListenerThread();

	boolean queueSign = true;

	private ThreadPoolService() {
		super();
	}

	public static ThreadPoolService getInstance() {
		if (null == threadPoolService) {
			threadPoolService = new ThreadPoolService();
		}
		return threadPoolService;
	}

	public void clear() {
		if (threadPoolService != null)
			threadPoolService.destroy();
	}

	public void init() {
		try {
			taskThread = new ListenerThread();
			maximumPoolSizes = ThreadPoolParameter.getMaximumPoolSizes();

			minPoolSizes = ThreadPoolParameter.getMinPoolSizes();

			queueCapacity = ThreadPoolParameter.getQueueCapacity();

			keepAliveTime = ThreadPoolParameter.getKeepAliveTime();

			timeUnit = ThreadPoolParameter.getTimeUnit();

			ArrayBlockingQueue<Runnable> arrayQueue = new ArrayBlockingQueue<Runnable>(queueCapacity);

			threadPool = new ThreadPoolExecutor(minPoolSizes, maximumPoolSizes, keepAliveTime, timeUnit, arrayQueue, new ThreadPoolRejectedExecutionHandler());

			if (!queueSign) {
				queueSign = true;
			}
			taskThread.start();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public boolean execute(BaseThreadCommand task) {
		if (queueSign && !threadPool.isShutdown()) {
			taskQueue.add(task);
			synchronized (ListenerThread.lockEmpty) {
				ListenerThread.lockEmpty.notifyAll();
			}
			return true;
		} else {
			return false;
		}
	}

	public ThreadPoolExecutor getThreadPool() {
		if (null == threadPool) {
			init();
		}
		return threadPool;
	}

	LinkedBlockingQueue<BaseThreadCommand> getTaskQueue() {
		return taskQueue;
	}

	@SuppressWarnings("static-access")
	public void destroy() {
		queueSign = false;

		while (true) {
			try {
				Thread.currentThread().sleep(time);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
			if (taskQueue.isEmpty()) {
				break;
			}
		}

		taskThread.setSign(false);
		synchronized (ListenerThread.lockEmpty) {
			ListenerThread.lockEmpty.notifyAll();
		}

		try {
			// Wait for the task thread to complete its execution
			taskThread.join();
		} catch (InterruptedException e) {
			e.printStackTrace();
		}

		// Shut down the thread pool
		threadPool.shutdown();
	}

}
