package com.actonia.dao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

import com.actonia.IConstants;
import com.actonia.entity.LinkGainLossClickHouseEntity;
import com.actonia.utils.FormatUtils;

import ru.yandex.clickhouse.ClickHouseDataSource;
import ru.yandex.clickhouse.settings.ClickHouseProperties;

// Due to ClickHouse concurrency issues, use Singleton pattern, do not use Spring (ie. all methods must be 'synchronized')
public class LinkGainLossClickHouseDAO {

	//private static boolean isDebug = false;
	public static final String TABLE_NAME = "dis_link_gain_loss";
	private static final int TOTAL_DAO_INSTANCES = 2;
	private static int daoMapIndex = 0;

	// map key = DAO index (0  - 59)
	// map value = instance of LinkGainLossClickHouseDAO
	private static Map<Integer, LinkGainLossClickHouseDAO> linkGainLossClickHouseDAOMap;

	//private static LinkGainLossClickHouseDAO linkGainLossClickHouseDAO;

	private List<String> databaseHostnameList;
	private String databasePort = null;
	private String databaseName = null;
	private List<Connection> connectionList;
	private int batchCreationSize;
	private String databaseUser = null;
	private String databasePassword = null;
	private List<String> connectionUrlList = null;
	private int connectionTimeoutInMilliseconds;
	private int maximumRetryCounts;
	private int retryWaitMilliseconds;
	private int daoIndex;

	public static LinkGainLossClickHouseDAO getInstance() throws Exception {
		LinkGainLossClickHouseDAO linkGainLossClickHouseDAO = null;
		String clickHouseDatabaseHostnames = null;
		String[] clickHouseDatabaseHostnameArray = null;
		String clickHouseDatabasePort = null;
		String clickHouseDatabaseName = null;
		String clickHouseDatabaseUser = null;
		String clickHouseDatabasePassword = null;
		int clickHouseBatchCreationSize = 0;
		int clickHouseconnectionTimeoutInMilliseconds = 0;
		int clickHouseMaximumRetryCounts = 0;
		int clickHouseRetryWaitMilliseconds = 0;
		if (linkGainLossClickHouseDAOMap == null) {
			clickHouseDatabaseHostnames = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_HOSTNAMES);
			clickHouseDatabaseHostnameArray = clickHouseDatabaseHostnames.split(IConstants.COMMA);
			clickHouseDatabasePort = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_PORT);
			clickHouseDatabaseName = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_NAME);
			clickHouseDatabaseUser = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_USER);
			clickHouseDatabasePassword = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_PASSWORD);
			clickHouseBatchCreationSize = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_BATCH_CREATION_SIZE, 100);
			clickHouseconnectionTimeoutInMilliseconds = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_CONNECTION_TIMEOUT_MILLISECONDS, 88888);
			clickHouseMaximumRetryCounts = ClickHouseCrawlDatabaseConfigurations.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_MAX_RETRY_COUNTS,
					8);
			clickHouseRetryWaitMilliseconds = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_RETRY_WAIT_MILLISECONDS, 1000);
			FormatUtils.getInstance().logMemoryUsage("LinkGainLossClickHouseDAO() clickHouseDatabaseHostnames=" + clickHouseDatabaseHostnames.toString()
					+ ",clickHouseDatabasePort=" + clickHouseDatabasePort + ",clickHouseDatabaseName=" + clickHouseDatabaseName + ",clickHouseBatchCreationSize="
					+ clickHouseBatchCreationSize + ",clickHouseDatabaseUser=" + clickHouseDatabaseUser + ",clickHouseDatabasePassword=" + clickHouseDatabasePassword
					+ ",clickHouseconnectionTimeoutInMilliseconds=" + clickHouseconnectionTimeoutInMilliseconds + ",clickHouseMaximumRetryCounts="
					+ clickHouseMaximumRetryCounts + ",clickHouseRetryWaitMilliseconds=" + clickHouseRetryWaitMilliseconds + ",TABLE_NAME=" + TABLE_NAME);

			linkGainLossClickHouseDAOMap = new HashMap<Integer, LinkGainLossClickHouseDAO>();
			for (int i = 0; i < TOTAL_DAO_INSTANCES; i++) {
				linkGainLossClickHouseDAO = initialize(clickHouseDatabaseHostnameArray, clickHouseDatabasePort, clickHouseDatabaseName, clickHouseDatabaseUser,
						clickHouseDatabasePassword, clickHouseBatchCreationSize, clickHouseconnectionTimeoutInMilliseconds, clickHouseMaximumRetryCounts,
						clickHouseRetryWaitMilliseconds, i);
				linkGainLossClickHouseDAOMap.put(i, linkGainLossClickHouseDAO);
			}
			FormatUtils.getInstance().logMemoryUsage("getInstance() total linkGainLossClickHouseDAOs=" + linkGainLossClickHouseDAOMap.size());
		}
		int index = getDaoMapIndex();
		linkGainLossClickHouseDAO = linkGainLossClickHouseDAOMap.get(index);
		return linkGainLossClickHouseDAO;
	}

	private static synchronized int getDaoMapIndex() {
		int index = 0;
		if (daoMapIndex >= TOTAL_DAO_INSTANCES) {
			daoMapIndex = 0;
			index = 0;
		} else {
			index = daoMapIndex++;
		}
		return index;
	}

	// initialize LinkGainLossClickHouseDAO based on runtime clickhouse configurations 
	private static LinkGainLossClickHouseDAO initialize(String[] clickHouseDatabaseHostnameArray, String clickHouseDatabasePort, String clickHouseDatabaseName,
			String clickHouseDatabaseUser, String clickHouseDatabasePassword, int clickHouseBatchCreationSize, int clickHouseconnectionTimeoutInMilliseconds,
			int clickHouseMaximumRetryCounts, int clickHouseRetryWaitMilliseconds, int daoIndex) throws Exception {
		LinkGainLossClickHouseDAO linkGainLossClickHouseDAO = null;

		try {
			linkGainLossClickHouseDAO = new LinkGainLossClickHouseDAO(clickHouseDatabaseHostnameArray, clickHouseDatabasePort, clickHouseDatabaseName,
					clickHouseBatchCreationSize, clickHouseDatabaseUser, clickHouseDatabasePassword, clickHouseconnectionTimeoutInMilliseconds,
					clickHouseMaximumRetryCounts, clickHouseRetryWaitMilliseconds, daoIndex);
		} catch (Exception e) {
			e.printStackTrace();
		}

		//FormatUtils.getInstance().logMemoryUsage("initialize() linkGainLossClickHouseDAO=" + linkGainLossClickHouseDAO.toString());
		return linkGainLossClickHouseDAO;
	}

	private LinkGainLossClickHouseDAO(String[] databaseHostnameArrayInput, String databasePortInput, String databaseNameInput, int batchCreationSizeInput,
			String databaseUserInput, String databasePasswordInput, int connectionTimeoutInMillisecondsInput, int maximumRetryCountsInput,
			int retryWaitMillisecondsInput, int index) throws Exception {

		ClickHouseDataSource clickHouseDataSource = null;
		Connection connection = null;

		databaseHostnameList = Arrays.asList(databaseHostnameArrayInput);
		databasePort = databasePortInput;
		databaseName = databaseNameInput;
		batchCreationSize = batchCreationSizeInput;
		databaseUser = databaseUserInput;
		databasePassword = databasePasswordInput;
		connectionTimeoutInMilliseconds = connectionTimeoutInMillisecondsInput;
		maximumRetryCounts = maximumRetryCountsInput;
		retryWaitMilliseconds = retryWaitMillisecondsInput;
		daoIndex = index;

		ClickHouseProperties clickHouseProperties = new ClickHouseProperties();
		clickHouseProperties.setDecompress(true);
		clickHouseProperties.setConnectionTimeout(connectionTimeoutInMilliseconds);
		clickHouseProperties.setSocketTimeout(connectionTimeoutInMilliseconds);

		connectionList = new ArrayList<Connection>();

		String connectionUrl = null;

		connectionUrlList = new ArrayList<String>();

		for (String databaseHostname : databaseHostnameList) {
			if (StringUtils.isNotBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
						+ "?user=" + databaseUser + "&password=" + databasePassword;
			} else if (StringUtils.isBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
						+ "?password=" + databasePassword;
			} else {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH
						+ databaseName;
			}
			connectionUrlList.add(connectionUrl);
			//FormatUtils.getInstance().logMemoryUsage("LinkGainLossClickHouseDAO() connectionUrl=" + connectionUrl);
			clickHouseDataSource = new ClickHouseDataSource(connectionUrl, clickHouseProperties);
			connection = clickHouseDataSource.getConnection();
			connectionList.add(connection);
		}
	}

	@Override
	public String toString() {
		return "LinkGainLossClickHouseDAO [databaseHostnameList=" + databaseHostnameList.toString() + ", databasePort=" + databasePort + ", databaseName="
				+ databaseName + ", batchCreationSize=" + batchCreationSize + ", databaseUser=" + databaseUser + ", databasePassword=" + databasePassword
				+ ", connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ", maximumRetryCounts=" + maximumRetryCounts + ", retryWaitMilliseconds="
				+ retryWaitMilliseconds + ", daoIndex=" + daoIndex + "]";
	}

	private synchronized String getTableName(String tableName) {
		if (StringUtils.isNotBlank(tableName)) {
			return tableName;
		} else {
			return TABLE_NAME;
		}
	}

	public synchronized int getBatchCreationSize() {
		return batchCreationSize;
	}

	public synchronized void createBatch(String ip, String domainName, List<LinkGainLossClickHouseEntity> linkGainLossClickHouseEntityList, String tableName)
			throws Exception {
		long startTimestamp = System.currentTimeMillis();

		PreparedStatement preparedStatement = null;
		int index = 0;
		Connection connection = null;

		String creationSqlStatement = getCreationSqlStatement(tableName);
		//FormatUtils.getInstance().logMemoryUsage("createBatch() creationSqlStatement=" + creationSqlStatement);

		for (int i = 0; i < connectionList.size(); i++) {
			connection = connectionList.get(i);
			preparedStatement = connection.prepareStatement(creationSqlStatement);
			for (LinkGainLossClickHouseEntity linkGainLossClickHouseEntity : linkGainLossClickHouseEntityList) {
				//if (checkIfAlreadyExists(linkGainLossClickHouseEntity, tableName)) {
				//	FormatUtils.getInstance().logMemoryUsage("createBatch() error--skipped duplicates,ip=" + ip + ",domainName=" + domainName
				//			+ ",linkGainLossClickHouseEntity=" + linkGainLossClickHouseEntity.toString());
				//	continue nextLinkGainLossClickHouseEntity;
				//}

				index = 1;

				// domain_id
				preparedStatement.setInt(index++, linkGainLossClickHouseEntity.getDomainId());

				// gain_loss_date
				preparedStatement.setDate(index++, new java.sql.Date(linkGainLossClickHouseEntity.getGainLossDate().getTime()));

				// gain_loss_flag
				preparedStatement.setInt(index++, linkGainLossClickHouseEntity.getGainLossFlag());

				// source_url_hash

				// source_url_murmur_hash

				// target_url_hash

				// target_url_murmur_hash

				// source_url
				preparedStatement.setString(index++, linkGainLossClickHouseEntity.getSourceUrl());

				// target_url
				preparedStatement.setString(index++, linkGainLossClickHouseEntity.getTargetUrl());

				// anchor_text
				preparedStatement.setString(index++, linkGainLossClickHouseEntity.getAnchorText());

				// link_type
				preparedStatement.setInt(index++, linkGainLossClickHouseEntity.getLinkType());

				// target_url_http_status_code
				preparedStatement.setInt(index++, linkGainLossClickHouseEntity.getTargetUrlHttpStatusCode());

				preparedStatement.addBatch();
			}

			preparedStatement.executeBatch();
		}

		FormatUtils.getInstance().logMemoryUsage("createBatch() ip=" + ip + ",domainName=" + domainName + ",linkGainLossClickHouseEntityList.size()="
				+ linkGainLossClickHouseEntityList.size() + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private synchronized String getCreationSqlStatement(String tableName) {
		String response = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" insert into " + getTableName(tableName) + " ");
		stringBuilder.append(" (");
		stringBuilder.append("	domain_id,");
		stringBuilder.append("	gain_loss_date,");
		stringBuilder.append("	gain_loss_flag,");
		stringBuilder.append("	source_url,");
		stringBuilder.append("	target_url,");
		stringBuilder.append("	anchor_text,");
		stringBuilder.append("	link_type,");
		stringBuilder.append("	target_url_http_status_code");
		stringBuilder.append(" )");
		stringBuilder.append(" values ");
		stringBuilder.append(" (");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?");
		stringBuilder.append(" )");
		stringBuilder.append(IConstants.SEMI_COLON);
		response = stringBuilder.toString();
		return response;
	}

	public synchronized List<LinkGainLossClickHouseEntity> getListByDescendingGainLostDate(int domainId, String sourceUrlString, String tableName) throws Exception {
		//FormatUtils.getInstance().logMemoryUsage("getListByDescendingGainLostDate() begins. domainId=" + domainId + ",sourceUrlString=" + sourceUrlString);
		List<LinkGainLossClickHouseEntity> linkGainLossClickHouseEntityList = new ArrayList<LinkGainLossClickHouseEntity>();
		LinkGainLossClickHouseEntity linkGainLossClickHouseEntity = null;
		ResultSet resultSet = null;
		Connection connection = null;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select distinct");
		stringBuilder.append("  *");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("     domain_id = ?");
		stringBuilder.append(" and source_url_hash = URLHash(?)");
		stringBuilder.append(" and source_url_murmur_hash = murmurHash2_32(?)");
		stringBuilder.append(" and (");
		stringBuilder.append(" 		gain_loss_flag,");
		stringBuilder.append(" 		target_url_hash,");
		stringBuilder.append(" 		target_url_murmur_hash,");
		stringBuilder.append(" 		anchor_text,");
		stringBuilder.append(" 		link_type,");
		stringBuilder.append(" 		gain_loss_date");
		stringBuilder.append(" 	)");
		stringBuilder.append(" 	global in");
		stringBuilder.append(" 	(");
		stringBuilder.append(" 	select");
		stringBuilder.append(" 		gain_loss_flag,");
		stringBuilder.append(" 		target_url_hash,");
		stringBuilder.append(" 		target_url_murmur_hash,");
		stringBuilder.append(" 		anchor_text,");
		stringBuilder.append(" 		link_type,");
		stringBuilder.append(" 		max(gain_loss_date)");
		stringBuilder.append(" 	from");
		stringBuilder.append(" 		" + getTableName(tableName));
		stringBuilder.append(" 	where");
		stringBuilder.append(" 		domain_id = ?");
		stringBuilder.append(" 	and source_url_hash = URLHash(?)");
		stringBuilder.append("  and source_url_murmur_hash = murmurHash2_32(?)");
		stringBuilder.append(" 	group by");
		stringBuilder.append(" 		gain_loss_flag,");
		stringBuilder.append(" 		target_url_hash,");
		stringBuilder.append(" 		target_url_murmur_hash,");
		stringBuilder.append(" 		anchor_text,");
		stringBuilder.append(" 		link_type");
		stringBuilder.append(" 	)");
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getByTrackDate() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		int retryCount = 0;
		int index = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				linkGainLossClickHouseEntityList = new ArrayList<LinkGainLossClickHouseEntity>();
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setString(index++, sourceUrlString);
				preparedStatement.setString(index++, sourceUrlString);
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setString(index++, sourceUrlString);
				preparedStatement.setString(index++, sourceUrlString);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					linkGainLossClickHouseEntity = new LinkGainLossClickHouseEntity();

					// domain_id
					linkGainLossClickHouseEntity.setDomainId(domainId);

					// gain_loss_date
					linkGainLossClickHouseEntity.setGainLossDate(resultSet.getDate(IConstants.GAIN_LOSS_DATE));

					// gain_loss_flag
					linkGainLossClickHouseEntity.setGainLossFlag(resultSet.getInt(IConstants.GAIN_LOSS_FLAG));

					// source_url
					linkGainLossClickHouseEntity.setSourceUrl(sourceUrlString);

					// target_url
					linkGainLossClickHouseEntity.setTargetUrl(resultSet.getString(IConstants.TARGET_URL));

					// anchor_text
					linkGainLossClickHouseEntity.setAnchorText(resultSet.getString(IConstants.ANCHOR_TEXT));

					// link_type
					linkGainLossClickHouseEntity.setLinkType(resultSet.getInt(IConstants.LINK_TYPE));

					// target_url_http_status_code
					linkGainLossClickHouseEntity.setTargetUrlHttpStatusCode(resultSet.getInt(IConstants.TARGET_URL_HTTP_STATUS_CODE));

					linkGainLossClickHouseEntityList.add(linkGainLossClickHouseEntity);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage(
							"getListByDescendingGainLostDate() domainId=" + domainId + ",e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		//FormatUtils.getInstance().logMemoryUsage("getListByDescendingGainLostDate() ends. domainId=" + domainId + ",sourceUrlString=" + sourceUrlString
		//		+ ",linkGainLossClickHouseEntityList.size()=" + linkGainLossClickHouseEntityList.size());
		return linkGainLossClickHouseEntityList;
	}

	public synchronized List<LinkGainLossClickHouseEntity> getListByDomainId(int domainId, String tableName) throws Exception {
		List<LinkGainLossClickHouseEntity> linkGainLossClickHouseEntityList = new ArrayList<LinkGainLossClickHouseEntity>();
		LinkGainLossClickHouseEntity linkGainLossClickHouseEntity = null;
		ResultSet resultSet = null;
		Connection connection = null;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     gain_loss_date,");
		stringBuilder.append("     gain_loss_flag,");
		stringBuilder.append("     source_url,");
		stringBuilder.append("     target_url,");
		stringBuilder.append("     anchor_text,");
		stringBuilder.append("     link_type,");
		stringBuilder.append("     target_url_http_status_code");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("     domain_id = ?");
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getByTrackDate() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		int retryCount = 0;
		int index = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setInt(index++, domainId);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					linkGainLossClickHouseEntity = new LinkGainLossClickHouseEntity();

					// domain_id
					linkGainLossClickHouseEntity.setDomainId(domainId);

					// gain_loss_date
					linkGainLossClickHouseEntity.setGainLossDate(resultSet.getDate(IConstants.GAIN_LOSS_DATE));

					// gain_loss_flag
					linkGainLossClickHouseEntity.setGainLossFlag(resultSet.getInt(IConstants.GAIN_LOSS_FLAG));

					// source_url
					linkGainLossClickHouseEntity.setSourceUrl(resultSet.getString(IConstants.SOURCE_URL));

					// target_url
					linkGainLossClickHouseEntity.setTargetUrl(resultSet.getString(IConstants.TARGET_URL));

					// anchor_text
					linkGainLossClickHouseEntity.setAnchorText(resultSet.getString(IConstants.ANCHOR_TEXT));

					// link_type
					linkGainLossClickHouseEntity.setLinkType(resultSet.getInt(IConstants.LINK_TYPE));

					// target_url_http_status_code
					linkGainLossClickHouseEntity.setTargetUrlHttpStatusCode(resultSet.getInt(IConstants.TARGET_URL_HTTP_STATUS_CODE));

					linkGainLossClickHouseEntityList.add(linkGainLossClickHouseEntity);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance()
							.logMemoryUsage("getListByDomainId() domainId=" + domainId + ",e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		return linkGainLossClickHouseEntityList;
	}

	public synchronized List<LinkGainLossClickHouseEntity> getUniqueDomainIds(String tableName) throws Exception {
		List<LinkGainLossClickHouseEntity> linkGainLossClickHouseEntityList = new ArrayList<LinkGainLossClickHouseEntity>();
		LinkGainLossClickHouseEntity linkGainLossClickHouseEntity = null;
		ResultSet resultSet = null;
		Connection connection = null;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select distinct");
		stringBuilder.append("     domain_id");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getByTrackDate() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		int retryCount = 0;
		int index = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					linkGainLossClickHouseEntity = new LinkGainLossClickHouseEntity();

					// domain_id
					linkGainLossClickHouseEntity.setDomainId(resultSet.getInt(IConstants.DOMAIN_ID));

					linkGainLossClickHouseEntityList.add(linkGainLossClickHouseEntity);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getUniqueDomainIds() e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		return linkGainLossClickHouseEntityList;
	}

	public synchronized boolean checkIfAlreadyExists(LinkGainLossClickHouseEntity linkGainLossClickHouseEntity, String tableName) throws Exception {
		boolean isAlreadyExist = false;
		ResultSet resultSet = null;
		Connection connection = null;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     *");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("     domain_id = ?");
		stringBuilder.append(" and gain_loss_date = ?");
		stringBuilder.append(" and gain_loss_flag = ?");
		stringBuilder.append(" and source_url_hash = URLHash(?)");
		stringBuilder.append(" and source_url_murmur_hash = murmurHash2_32(?)");
		stringBuilder.append(" and target_url_hash = URLHash(?)");
		stringBuilder.append(" and target_url_murmur_hash = murmurHash2_32(?)");
		stringBuilder.append(" and anchor_text = ?");
		stringBuilder.append(" and link_type = ?");
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getByTrackDate() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		int retryCount = 0;
		int index = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setInt(index++, linkGainLossClickHouseEntity.getDomainId());
				preparedStatement.setDate(index++, new java.sql.Date(linkGainLossClickHouseEntity.getGainLossDate().getTime()));
				preparedStatement.setInt(index++, linkGainLossClickHouseEntity.getGainLossFlag());
				preparedStatement.setString(index++, linkGainLossClickHouseEntity.getSourceUrl());
				preparedStatement.setString(index++, linkGainLossClickHouseEntity.getSourceUrl());
				preparedStatement.setString(index++, linkGainLossClickHouseEntity.getTargetUrl());
				preparedStatement.setString(index++, linkGainLossClickHouseEntity.getTargetUrl());
				preparedStatement.setString(index++, linkGainLossClickHouseEntity.getAnchorText());
				preparedStatement.setInt(index++, linkGainLossClickHouseEntity.getLinkType());
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					isAlreadyExist = true;
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("checkIfAlreadyExists() linkGainLossClickHouseEntity=" + linkGainLossClickHouseEntity.toString()
							+ ",e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		return isAlreadyExist;
	}
}
