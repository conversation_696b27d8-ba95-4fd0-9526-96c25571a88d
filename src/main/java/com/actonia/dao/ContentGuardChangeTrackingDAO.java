package com.actonia.dao;

import java.util.List;

import com.actonia.entity.ContentGuardChangeTrackingEntity;

public class ContentGuardChangeTrackingDAO extends BaseJdbcSupport<ContentGuardChangeTrackingEntity> {

	@Override
	public String getTableName() {
		return "content_guard_change_tracking";
	}

	public List<ContentGuardChangeTrackingEntity> getList() {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("  *");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		String sqlString = stringBuilder.toString();
		return this.findBySql(sqlString);
	}

	public List<ContentGuardChangeTrackingEntity> getCriticalIndicatorList() {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("  *");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("  critical_flag = 1");
		String sqlString = stringBuilder.toString();
		return this.findBySql(sqlString);
	}
}