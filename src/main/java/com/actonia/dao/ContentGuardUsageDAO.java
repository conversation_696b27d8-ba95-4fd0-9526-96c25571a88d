package com.actonia.dao;

import java.util.ArrayList;
import java.util.List;

import com.actonia.IConstants;
import com.actonia.entity.ContentGuardUsageEntity;

public class ContentGuardUsageDAO extends BaseJdbcSupport<ContentGuardUsageEntity> {

	private static final int NUMBER_OF_FIELDS = 4;

	@Override
	public String getTableName() {
		return "content_guard_usage";
	}

	public void insertMultiRowsBatch(List<ContentGuardUsageEntity> contentGuardUsageEntityList) {
		Object[] objectArray = null;
		List<Object[]> objectArrayList = new ArrayList<Object[]>();
		List<ContentGuardUsageEntity> tempList = new ArrayList<ContentGuardUsageEntity>();

		for (ContentGuardUsageEntity contentGuardUsageEntity : contentGuardUsageEntityList) {
			tempList.add(contentGuardUsageEntity);
			if (tempList.size() == IConstants.RECORDS_PER_SQL_STATEMENT) {
				objectArray = getObjectArrayForInsert(tempList);
				objectArrayList.add(objectArray);
				if (objectArrayList.size() == IConstants.SQL_STATEMENTS_PER_JDBC_CALL) {
					this.executeBatch(getInsertMultiRowsSqlStatement(IConstants.RECORDS_PER_SQL_STATEMENT), objectArrayList);
					objectArrayList = new ArrayList<Object[]>();
				}
				tempList = new ArrayList<ContentGuardUsageEntity>();
			}
		}

		if (objectArrayList != null && objectArrayList.size() > 0) {
			this.executeBatch(getInsertMultiRowsSqlStatement(IConstants.RECORDS_PER_SQL_STATEMENT), objectArrayList);
			objectArrayList = new ArrayList<Object[]>();
		}

		if (tempList != null && tempList.size() > 0) {
			objectArray = getObjectArrayForInsert(tempList);
			objectArrayList.add(objectArray);
			if (objectArrayList != null && objectArrayList.size() > 0) {
				this.executeBatch(getInsertMultiRowsSqlStatement(tempList.size()), objectArrayList);
				objectArrayList = new ArrayList<Object[]>();
			}
		}
	}

	private Object[] getObjectArrayForInsert(List<ContentGuardUsageEntity> list) {
		List<Object[]> tempObjectArrayList = new ArrayList<Object[]>();
		Object[] targetUrlDailyCrawlTrackingObjectArray = null;
		int totalNumberOfObjects = 0;
		for (ContentGuardUsageEntity contentGuardUsageEntity : list) {
			targetUrlDailyCrawlTrackingObjectArray = new Object[] { contentGuardUsageEntity.getDomainId(), contentGuardUsageEntity.getUsageDate(),
					contentGuardUsageEntity.getGroupId(), contentGuardUsageEntity.getTotalUrls(), };
			tempObjectArrayList.add(targetUrlDailyCrawlTrackingObjectArray);
		}

		totalNumberOfObjects = tempObjectArrayList.size() * NUMBER_OF_FIELDS;

		Object[] objectArray = new Object[totalNumberOfObjects];
		int idx = 0;
		for (Object[] tempObjectArray : tempObjectArrayList) {
			for (int i = 0; i < tempObjectArray.length; i++) {
				objectArray[idx++] = tempObjectArray[i];
			}
		}
		return objectArray;
	}

	private String getInsertMultiRowsSqlStatement(int recordsPerInsertStatement) {
		String response = null;
		String sqlString = null;
		StringBuffer stringBuffer = new StringBuffer();
		stringBuffer.append(" insert into " + getTableName() + " ");
		stringBuffer.append(" (");
		stringBuffer.append("	domain_id,");
		stringBuffer.append("	usage_date,");
		stringBuffer.append("	group_id,");
		stringBuffer.append("	total_urls");
		stringBuffer.append(" )");
		stringBuffer.append("values ");
		sqlString = stringBuffer.toString();

		StringBuffer sql = new StringBuffer();
		sql.append(sqlString);

		for (int i = 0; i < recordsPerInsertStatement; i++) {
			if (i == 0) {
				sql.append("(?,?,?,?)");
			} else {
				sql.append(",(?,?,?,?)");
			}
		}
		sql.append(IConstants.SEMI_COLON);
		response = sql.toString();
		return response;
	}

	public ContentGuardUsageEntity get(int domainId, int usageDate, Long groupId) {
		StringBuffer stringBuffer = new StringBuffer();
		stringBuffer.append(" select");
		stringBuffer.append(" * ");
		stringBuffer.append(" from");
		stringBuffer.append(" " + getTableName());
		stringBuffer.append(" where");
		stringBuffer.append("     domain_id = ?");
		stringBuffer.append(" and usage_date = ?");
		stringBuffer.append(" and group_id = ?");
		String sqlString = stringBuffer.toString();
		return findObject(sqlString, domainId, usageDate, groupId);
	}

	public int update(int domainId, int usageDate, Long groupId, int totalUrls) {
		StringBuffer stringBuffer = new StringBuffer();
		stringBuffer.append(" update");
		stringBuffer.append(" " + getTableName());
		stringBuffer.append(" set");
		stringBuffer.append("    total_urls = ?");
		stringBuffer.append(" where");
		stringBuffer.append("     domain_id = ?");
		stringBuffer.append(" and usage_date = ?");
		stringBuffer.append(" and group_id = ?");
		String sqlString = stringBuffer.toString();
		return this.executeUpdate(sqlString, totalUrls, domainId, usageDate, groupId);
	}

	public List<ContentGuardUsageEntity> getList(int domainId, Integer startUsageDateNumber, Integer endUsageDateNumber, Long groupId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     content_guard_group.group_name as groupName,");
		stringBuilder.append("     content_guard_group.crawl_frequency_type as crawlFrequencyType,");
		stringBuilder.append("     content_guard_usage.*");
		stringBuilder.append(" from");
		stringBuilder.append("     content_guard_group content_guard_group,");
		stringBuilder.append(" 	   content_guard_usage content_guard_usage");
		stringBuilder.append(" where");
		stringBuilder.append("     content_guard_group.domain_id = ?");
		stringBuilder.append(" and content_guard_group.domain_id = content_guard_usage.domain_id");
		if (startUsageDateNumber != null && endUsageDateNumber != null) {
			stringBuilder.append(" and content_guard_usage.usage_date >= ?");
			stringBuilder.append(" and content_guard_usage.usage_date <= ?");
		}
		if (groupId != null) {
			stringBuilder.append(" and content_guard_group.id = ?");
		}
		stringBuilder.append(" and content_guard_group.id = content_guard_usage.group_id");
		String sqlString = stringBuilder.toString();
		if (startUsageDateNumber != null && endUsageDateNumber != null && groupId != null) {
			return this.findBySql(sqlString, domainId, startUsageDateNumber, endUsageDateNumber, groupId);
		} else if (startUsageDateNumber != null && endUsageDateNumber != null) {
			return this.findBySql(sqlString, domainId, startUsageDateNumber, endUsageDateNumber);
		} else {
			return this.findBySql(sqlString, domainId);
		}
	}

}