package com.actonia.dao;

import com.actonia.entity.WebhookEntity;

public class WebhookDAO extends BaseJdbcSupport<WebhookEntity> {

	@Override
	public String getTableName() {
		return "webhook";
	}

	public WebhookEntity get(int domainId, int type, int alertType) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     *");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     domain_id = ?");
		stringBuilder.append(" and type = ?");
		stringBuilder.append(" and alert_type = ?");
		String sqlString = stringBuilder.toString();
		return this.findObject(sqlString, domainId, type, alertType);
	}

}