/**
 *
 */
package com.actonia.dao;

import java.math.BigInteger;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.simple.ParameterizedBeanPropertyRowMapper;
import org.springframework.jdbc.core.simple.SimpleJdbcDaoSupport;
import org.springframework.jdbc.core.simple.SimpleJdbcInsert;

import com.actonia.utils.FormatUtils;
import com.actonia.utils.GenericsUtils;

/**
 * com.actonia.subserver.dao.BaseJdbcSupport.java
 *
 * @version $Revision: 89264 $ $Author: xucongjie@SHINETECHCHINA $
 */
@SuppressWarnings("unchecked")
public abstract class BaseJdbcSupport<T> extends SimpleJdbcDaoSupport {

    private SimpleJdbcInsert simpleJdbcInsert;

    private String primaryKeyName;

    protected Class<T> entityClass;

    public BaseJdbcSupport() {
        super();
        entityClass = GenericsUtils.getSuperClassGenricType(getClass());
    }

    protected Class<T> getEntityClass() {
        return entityClass;
    }

    public String getPrimaryKeyName() {
        if (StringUtils.isEmpty(primaryKeyName))
            primaryKeyName = "id";
        return primaryKeyName;
    }

    @Override
    protected void initTemplateConfig() {
        super.initTemplateConfig();
        simpleJdbcInsert = new SimpleJdbcInsert(getJdbcTemplate()).withTableName(getTableName())
                .usingGeneratedKeyColumns(getPrimaryKeyName());
    }

    /**
     * @param parameters
     * @return the new record's id, -1 if some error
     */
    public int insert(Map<String, Object> parameters) {
        Number newId = simpleJdbcInsert.executeAndReturnKey(parameters);
        return newId == null ? -1 : newId.intValue();
    }

    public long insertForLongId(Map<String, Object> parameters) {
        Number newId = simpleJdbcInsert.executeAndReturnKey(parameters);
        return newId == null ? -1 : newId.longValue();
    }

    public int insert(Map<String, Object> parameters, String tableName) {
        simpleJdbcInsert = new SimpleJdbcInsert(getJdbcTemplate()).withTableName(tableName)
                .usingGeneratedKeyColumns(getPrimaryKeyName());
        Number newId = simpleJdbcInsert.executeAndReturnKey(parameters);
        
        return newId == null ? -1 : newId.intValue();
    }
    
    public void insertWithoutAutoIncrementalKey(Map<String, Object> parameters) {
        simpleJdbcInsert.execute(parameters);
    }
    
    /**
     * find first object by name = value
     *
     * @param name
     * @param value
     * @return null if not found
     */
    protected final T findFirstBy(String name, Object value) {
        StringBuffer sql = new StringBuffer();
        sql.append(" SELECT * FROM ").append(getTableName());
        sql.append(" WHERE ").append(name).append(" = ? ");
        sql.append(" LIMIT 1");

        List result = getSimpleJdbcTemplate().query(sql.toString(),
                ParameterizedBeanPropertyRowMapper.newInstance(getEntityClass()), value);

        if (result != null && result.size() > 0) {
            return (T) result.get(0);
        }
        return null;
    }

    protected final List<T> findBySql(String sql, Object... args) {
        return getSimpleJdbcTemplate().query(sql, ParameterizedBeanPropertyRowMapper.newInstance(getEntityClass()),
                args);
    }

    protected final T findObject(String sql, Object... args) {
        List result = getSimpleJdbcTemplate().query(sql,
                ParameterizedBeanPropertyRowMapper.newInstance(getEntityClass()), args);
        if (result != null && result.size() > 0) {
            return (T) result.get(0);
        }
        return null;
    }

    protected String queryForString(String sql, Object... args) throws DataAccessException {
        String result = (String) this.getJdbcTemplate().queryForObject(sql, args, String.class);
        return (result != null ? result : null);
    }

    protected List<String> queryForStringList(String sql, Object... args) throws DataAccessException {
        List result = this.getJdbcTemplate().queryForList(sql, args, String.class);
        return (result != null ? result : null);
    }

    protected List<Object[]> queryForObjectArrayList(String sql, Object... args) throws DataAccessException {
        List result = this.getJdbcTemplate().queryForList(sql, args, Object[].class);
        return result;
    }
    
    protected List queryForMapList(String sql, Object... args) throws DataAccessException {
        List result = this.getJdbcTemplate().queryForList(sql, args);
        return result;
    }

    protected List<Integer> queryForIntegerList(String sql, Object... args) throws DataAccessException {
        List result = this.getJdbcTemplate().queryForList(sql, args, Integer.class);
        return (result != null ? result : null);
    }
    
    
    protected List<Long> queryForLongList(String sql, Object... args) throws DataAccessException {
        List result = this.getJdbcTemplate().queryForList(sql, args, Long.class);
        return (result != null ? result : null);
    }

    protected Integer queryForInteger(String sql, Object... args) throws DataAccessException {
        List list = this.getJdbcTemplate().queryForList(sql, args, BigInteger.class);
        if (list != null && list.size() > 0) {
            BigInteger result = (BigInteger) list.get(0);
            return (result != null ? result.intValue() : null);
        }
        return null;
    }

    protected Date queryForDate(String sql, Object... args) throws DataAccessException {
        Date result = (Date) this.getJdbcTemplate().queryForObject(sql, args, Date.class);
        return (result != null ? result : null);
    }

    protected final int executeUpdate(String sql, Object... args) {
        return getSimpleJdbcTemplate().update(sql, args);
    }

    /**
     * 1. pageNum start from 1 2. sql can't content limit
     *
     * @param sql
     * @param pageSize
     * @return
     */
    protected List<T> queryPageForList(String sql, int pageSize, Object... args) {
        sql += " limit " + pageSize;

        return getSimpleJdbcTemplate().query(sql, ParameterizedBeanPropertyRowMapper.newInstance(getEntityClass()),
                args);

    }

    protected int[] executeBatch(String sql, List<Object[]> batchData) {
        int[] updateCounts = getSimpleJdbcTemplate().batchUpdate(sql, batchData);
        return updateCounts;

    }

    protected Integer queryForInt(String sql, Object... args) throws DataAccessException {
        return this.getJdbcTemplate().queryForInt(sql, args);
    }
    
    protected Integer queryForIntWithDate(String sql) throws DataAccessException {
        return this.getJdbcTemplate().queryForInt(sql);
    }

    public abstract String getTableName();

    //https://www.wrike.com/open.htm?id=8147694
	protected String getQueryParamByIntArray(Integer[] intArray) {
		if (intArray == null || intArray.length == 0) {
			return null;
		}
		StringBuffer intStr = new StringBuffer();
		for (Integer i : intArray) {
			if (i != null) {
				intStr.append(i).append(",");
			}
		}
		String intParams = StringUtils.removeEndIgnoreCase(intStr.toString(), ",");
		
		return intParams;
	}
	
	protected String getQueryParamByLongArray(Long[] longArray) {
		if (longArray == null || longArray.length == 0) {
			return null;
		}
		StringBuffer intStr = new StringBuffer();
		for (Long i : longArray) {
			if (i != null) {
				intStr.append(i).append(",");
			}
		}
		String intParams = StringUtils.removeEndIgnoreCase(intStr.toString(), ",");
		
		return intParams;
	}

    protected String getIntQueryParamByStrArray(String[] intStrArray) {
        if (intStrArray == null || intStrArray.length == 0) {
            return null;
        }
        StringBuffer intStr = new StringBuffer();
        for (String str : intStrArray) {
            if (StringUtils.isNotBlank(str)) {
                intStr.append(NumberUtils.toInt(str)).append(",");
            }
        }
        String intParams = StringUtils.removeEndIgnoreCase(intStr.toString(), ",");

        return intParams;
    }
    
    //https://www.wrike.com/open.htm?id=46463151
    //by sunny
    protected String getQueryParamByStrArray(String[] strArray) {
		if (strArray == null || strArray.length == 0) {
			return null;
		}
		StringBuilder intStr = new StringBuilder();
		for (String str : strArray) {
			if (StringUtils.isNotBlank(str)) {
				intStr.append("'").append(str).append("',");
			}
		}
		String intParams = StringUtils.removeEndIgnoreCase(intStr.toString(), ",");
		
		return intParams;
	}
    
	protected String getQueryParamByIntList(List<Integer> intList) {
		if (intList == null || intList.size() == 0) {
			return null;
		}
		StringBuilder intStr = new StringBuilder();
		for (Integer i : intList) {
			
			if (i != null) {
				intStr.append(i).append(",");
			}
		}
		String intParams = StringUtils.removeEndIgnoreCase(intStr.toString(), ",");
		
		return intParams;
	}
	
	
	protected String getQueryParamByDoubList(List<Double> intList) {
		if (intList == null || intList.size() == 0) {
			return null;
		}
		StringBuilder intStr = new StringBuilder();
		for (Double i : intList) {
			
			if (i != null) {
				intStr.append(i.intValue()).append(",");
			}
		}
		String intParams = StringUtils.removeEndIgnoreCase(intStr.toString(), ",");
		
		return intParams;
	}
	
	//https://www.wrike.com/open.htm?id=52554350
	//by sunny
	protected String getQueryParamByStrArrayToInt(String[] strArray) {
		if (strArray == null || strArray.length == 0) {
			return null;
		}
		StringBuilder intStr = new StringBuilder();
		for (String i : strArray) {
			if (i != null) {
				intStr.append(i).append(",");
			}
		}
		String intParams = StringUtils.removeEndIgnoreCase(intStr.toString(), ",");
		
		return intParams;
	}

    protected BigInteger queryForBigInteger(String sql, Object... args) throws DataAccessException {
        BigInteger result = (BigInteger) this.getJdbcTemplate().queryForObject(sql, args, BigInteger.class);
        return (result != null ? result : null);
    }
    
    protected Long queryForLong(String sql, Object... args) throws DataAccessException {
    	Long result = (Long) this.getJdbcTemplate().queryForObject(sql, args, Long.class);
        return (result != null ? result : null);
    }
    
    protected String getIntegerListQueryParam(List<Integer> intList) {
		if (intList == null || intList.isEmpty()) {
			return "";
		}
		StringBuilder intStr = new StringBuilder();
		for (Integer i : intList) {
			if (i != null) {
				intStr.append(i).append(",");
			}
		}
		String intParams = StringUtils.removeEndIgnoreCase(intStr.toString(), ",");
		
		return intParams;
	}
    
    protected String getLongListQueryParam(List<Long> longList) {
		if (longList == null || longList.isEmpty()) {
			return "";
		}
		StringBuilder intStr = new StringBuilder();
		for (Long i : longList) {
			if (i != null) {
				intStr.append(i).append(",");
			}
		}
		String intParams = StringUtils.removeEndIgnoreCase(intStr.toString(), ",");
		
		return intParams;
	}

    // scott - https://www.wrike.com/open.htm?id=89588308
    protected String getIntegerListQueryParam(int[] ints) {
        if (ints == null || ints.length == 0) {
            return "";
        }
        StringBuilder intStr = new StringBuilder();
        String intParams = "";
        try {
            for (Integer i : ints) {
                if (i != null) {
                    intStr.append(i).append(",");
                }
            }
            intParams = StringUtils.removeEndIgnoreCase(intStr.toString(), ",");
        } catch (Exception e) {
            logger.error(e.getMessage());
        }

        return intParams;
    }
    
    protected String getQueryParamQuestionMark(Collection<String> strList) {
		if (strList == null || strList.isEmpty()) {
			return null;
		}
		StringBuilder intStr = new StringBuilder();
		for (String s : strList) {
			if (s != null) {
				intStr.append("?,");
			}
		}
		String intParams = StringUtils.removeEndIgnoreCase(intStr.toString(), ",");
		
		return intParams;
	}
    
    protected String getDateNumberListQueryParam(List<Date> dateList, String dateFormat) {
		StringBuilder dates = new StringBuilder();
		for (Date day : dateList) {
			dates.append(FormatUtils.getInstance().formatDate(day, dateFormat)).append(",");
		}
		String datesParam = StringUtils.removeEndIgnoreCase(dates.toString(), ",");		
		return datesParam;
	}
    
}
