package com.actonia.dao;

import java.util.List;

import com.actonia.entity.ContentGuardAlertEntity;
import com.actonia.entity.OwnDomainEntity;

public class ContentGuardAlertDAO extends BaseJdbcSupport<ContentGuardAlertEntity> {

	@Override
	public String getTableName() {
		return "content_guard_alert";
	}

	public List<ContentGuardAlertEntity> getList(int domainId, int crawlFrequencyType) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     content_guard_group.group_name as groupName,");
		stringBuilder.append("     content_guard_alert.*");
		stringBuilder.append(" from");
		stringBuilder.append("     content_guard_group content_guard_group,");
		stringBuilder.append("     content_guard_alert content_guard_alert");
		stringBuilder.append(" where");
		stringBuilder.append("     content_guard_group.domain_id = ?");
		stringBuilder.append(" and content_guard_group.crawl_frequency_type = ?");
		stringBuilder.append(" and content_guard_group.domain_id = content_guard_alert.domain_id");
		stringBuilder.append(" and content_guard_group.id = content_guard_alert.group_id");
		String sqlString = stringBuilder.toString();
		return this.findBySql(sqlString, domainId, crawlFrequencyType);
	}

	public ContentGuardAlertEntity getByDomainIdGroupName(int domainId, String groupName) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     content_guard_alert.*");
		stringBuilder.append(" from");
		stringBuilder.append("     t_own_domain t_own_domain,");
		stringBuilder.append("     content_guard_group content_guard_group,");
		stringBuilder.append("     content_guard_alert content_guard_alert");
		stringBuilder.append(" where");
		stringBuilder.append("     t_own_domain.id = ?");
		stringBuilder.append(" and t_own_domain.`status` = ?");
		stringBuilder.append(" and t_own_domain.id = content_guard_group.domain_id");
		stringBuilder.append(" and content_guard_group.group_name = ?");
		stringBuilder.append(" and content_guard_group.domain_id = content_guard_alert.domain_id");
		stringBuilder.append(" and content_guard_group.id = content_guard_alert.group_id");
		String sqlString = stringBuilder.toString();
		return this.findObject(sqlString, domainId, OwnDomainEntity.STATE_ACTIVE, groupName);
	}

	public String getCustomIndicatorsByDomainIdAndGroupId(int domainId, long groupId) {
		String sqlString = " select" +
				"     content_guard_alert.custom_indicators" +
				" from" +
				"     t_own_domain t_own_domain," +
				"     content_guard_group content_guard_group," +
				"     content_guard_alert content_guard_alert" +
				" where" +
				"     t_own_domain.id = ?" +
				" and t_own_domain.`status` = ?" +
				" and t_own_domain.id = content_guard_group.domain_id" +
				" and content_guard_group.id = ?" +
				" and content_guard_group.domain_id = content_guard_alert.domain_id" +
				" and content_guard_group.id = content_guard_alert.group_id";
		return this.queryForString(sqlString, domainId, OwnDomainEntity.STATE_ACTIVE, groupId);
	}
}
