package com.actonia.dao;

import java.util.List;
import java.util.Set;

import com.actonia.entity.SeoClarityKeywordEntity;
import com.actonia.utils.FormatUtils;

public class SeoClarityKeywordEntityDAO extends BaseJdbcSupport<SeoClarityKeywordEntity> {

	@Override
	public String getTableName() {
		return "keyword_entity";
	}

	public SeoClarityKeywordEntity getKeywordTextById(int id) {
		String sql = "select keyword_text from keyword_entity where id = ?";
		return findObject(sql, id);
	}

	public List<SeoClarityKeywordEntity> getList(Set<Integer> inputSet) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("  id,");
		stringBuilder.append("  keyword_text");
		stringBuilder.append(" from");
		stringBuilder.append("  keyword_entity");
		stringBuilder.append(" where");
		stringBuilder.append("  id in (");
		stringBuilder.append(FormatUtils.getInstance().convertIntSetToString(inputSet));
		stringBuilder.append(" )");
		String sqlString = stringBuilder.toString();
		return findBySql(sqlString);
	}

	//public SeoClarityKeywordEntity getKeywordById(int id) {
	//	String sql = "select * from keyword_entity where id = ?";
	//	return findObject(sql, id);
	//}

	public SeoClarityKeywordEntity getKeywordEntityByName(String keywordName) {
		String sql = " select * from keyword_entity where keyword_text = ? ";
		return findObject(sql, keywordName);
	}

}