package com.actonia.dao;

import java.util.List;

import com.actonia.entity.AiRulesEntity;

public class AiRulesDAO extends BaseJdbcSupport<AiRulesEntity> {

	@Override
	public String getTableName() {
		return "ai_rules";
	}

	public List<AiRulesEntity> getPageAnalysisRuleList() {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     cdb_rule_id,");
		stringBuilder.append("     rule_desc");
		stringBuilder.append(" from");
		stringBuilder.append("     " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     cdb_rule_id > 0");
		String sqlString = stringBuilder.toString();
		return this.findBySql(sqlString);
	}
}