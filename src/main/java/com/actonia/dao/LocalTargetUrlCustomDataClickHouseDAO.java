package com.actonia.dao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.utils.FormatUtils;

import ru.yandex.clickhouse.ClickHouseDataSource;
import ru.yandex.clickhouse.settings.ClickHouseProperties;

// Due to ClickHouse concurrency issues, use Singleton pattern, do not use Spring (ie. all methods must be 'synchronized')
public class LocalTargetUrlCustomDataClickHouseDAO {

	//private static boolean isDebug = false;
	public static final String TABLE_NAME = "local_target_url_custom_data";
	private static final int TOTAL_DAO_INSTANCES = 1;
	private static int daoMapIndex = 0;

	// map key = DAO index (0  - 59)
	// map value = instance of TargetUrlCustomDataClickHouseDAO
	private static Map<Integer, LocalTargetUrlCustomDataClickHouseDAO> localTargetUrlCustomDataClickHouseDAOMap;

	//private static TargetUrlCustomDataClickHouseDAO localTargetUrlCustomDataClickHouseDAO;

	private List<String> databaseHostnameList;
	private String databasePort = null;
	private String databaseName = null;
	private List<Connection> connectionList;
	private int batchCreationSize;
	private String databaseUser = null;
	private String databasePassword = null;
	private List<String> connectionUrlList = null;
	private int connectionTimeoutInMilliseconds;
	private int maximumRetryCounts;
	private int retryWaitMilliseconds;
	private int daoIndex;

	public static LocalTargetUrlCustomDataClickHouseDAO getInstance() throws Exception {
		LocalTargetUrlCustomDataClickHouseDAO localTargetUrlCustomDataClickHouseDAO = null;
		String clickHouseDatabaseHostnames = null;
		String[] clickHouseDatabaseHostnameArray = null;
		String clickHouseDatabasePort = null;
		String clickHouseDatabaseName = null;
		String clickHouseDatabaseUser = null;
		String clickHouseDatabasePassword = null;
		int clickHouseBatchCreationSize = 0;
		int clickHouseconnectionTimeoutInMilliseconds = 0;
		int clickHouseMaximumRetryCounts = 0;
		int clickHouseRetryWaitMilliseconds = 0;
		if (localTargetUrlCustomDataClickHouseDAOMap == null) {
			clickHouseDatabaseHostnames = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CLICKHOUSE_CLUSTER_SERVERS);
			clickHouseDatabaseHostnameArray = clickHouseDatabaseHostnames.split(IConstants.COMMA);
			clickHouseDatabasePort = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_PORT);
			clickHouseDatabaseName = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_NAME);
			clickHouseDatabaseUser = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_USER);
			clickHouseDatabasePassword = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_PASSWORD);
			clickHouseBatchCreationSize = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_BATCH_CREATION_SIZE, 100);
			clickHouseconnectionTimeoutInMilliseconds = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_CONNECTION_TIMEOUT_MILLISECONDS, 88888);
			clickHouseMaximumRetryCounts = ClickHouseCrawlDatabaseConfigurations.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_MAX_RETRY_COUNTS,
					8);
			clickHouseRetryWaitMilliseconds = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_RETRY_WAIT_MILLISECONDS, 1000);
			FormatUtils.getInstance().logMemoryUsage("TargetUrlCustomDataClickHouseDAO() clickHouseDatabaseHostnames=" + clickHouseDatabaseHostnames.toString()
					+ ",clickHouseDatabasePort=" + clickHouseDatabasePort + ",clickHouseDatabaseName=" + clickHouseDatabaseName + ",clickHouseBatchCreationSize="
					+ clickHouseBatchCreationSize + ",clickHouseDatabaseUser=" + clickHouseDatabaseUser + ",clickHouseDatabasePassword=" + clickHouseDatabasePassword
					+ ",clickHouseconnectionTimeoutInMilliseconds=" + clickHouseconnectionTimeoutInMilliseconds + ",clickHouseMaximumRetryCounts="
					+ clickHouseMaximumRetryCounts + ",clickHouseRetryWaitMilliseconds=" + clickHouseRetryWaitMilliseconds + ",TABLE_NAME=" + TABLE_NAME);

			localTargetUrlCustomDataClickHouseDAOMap = new HashMap<Integer, LocalTargetUrlCustomDataClickHouseDAO>();
			for (int i = 0; i < TOTAL_DAO_INSTANCES; i++) {
				localTargetUrlCustomDataClickHouseDAO = initialize(clickHouseDatabaseHostnameArray, clickHouseDatabasePort, clickHouseDatabaseName, clickHouseDatabaseUser,
						clickHouseDatabasePassword, clickHouseBatchCreationSize, clickHouseconnectionTimeoutInMilliseconds, clickHouseMaximumRetryCounts,
						clickHouseRetryWaitMilliseconds, i);
				localTargetUrlCustomDataClickHouseDAOMap.put(i, localTargetUrlCustomDataClickHouseDAO);
			}
			FormatUtils.getInstance().logMemoryUsage("getInstance() total localTargetUrlCustomDataClickHouseDAOs=" + localTargetUrlCustomDataClickHouseDAOMap.size());
		}
		int index = getDaoMapIndex();
		localTargetUrlCustomDataClickHouseDAO = localTargetUrlCustomDataClickHouseDAOMap.get(index);
		return localTargetUrlCustomDataClickHouseDAO;
	}

	private static synchronized int getDaoMapIndex() {
		int index = 0;
		if (daoMapIndex >= TOTAL_DAO_INSTANCES) {
			daoMapIndex = 0;
			index = 0;
		} else {
			index = daoMapIndex++;
		}
		return index;
	}

	// initialize TargetUrlCustomDataClickHouseDAO based on runtime clickhouse configurations 
	private static LocalTargetUrlCustomDataClickHouseDAO initialize(String[] clickHouseDatabaseHostnameArray, String clickHouseDatabasePort,
			String clickHouseDatabaseName, String clickHouseDatabaseUser, String clickHouseDatabasePassword, int clickHouseBatchCreationSize,
			int clickHouseconnectionTimeoutInMilliseconds, int clickHouseMaximumRetryCounts, int clickHouseRetryWaitMilliseconds, int daoIndex) throws Exception {
		LocalTargetUrlCustomDataClickHouseDAO localTargetUrlCustomDataClickHouseDAO = null;

		try {
			localTargetUrlCustomDataClickHouseDAO = new LocalTargetUrlCustomDataClickHouseDAO(clickHouseDatabaseHostnameArray, clickHouseDatabasePort, clickHouseDatabaseName,
					clickHouseBatchCreationSize, clickHouseDatabaseUser, clickHouseDatabasePassword, clickHouseconnectionTimeoutInMilliseconds,
					clickHouseMaximumRetryCounts, clickHouseRetryWaitMilliseconds, daoIndex);
		} catch (Exception e) {
			e.printStackTrace();
		}

		//FormatUtils.getInstance().logMemoryUsage("initialize() localTargetUrlCustomDataClickHouseDAO=" + localTargetUrlCustomDataClickHouseDAO.toString());
		return localTargetUrlCustomDataClickHouseDAO;
	}

	private LocalTargetUrlCustomDataClickHouseDAO(String[] databaseHostnameArrayInput, String databasePortInput, String databaseNameInput, int batchCreationSizeInput,
			String databaseUserInput, String databasePasswordInput, int connectionTimeoutInMillisecondsInput, int maximumRetryCountsInput,
			int retryWaitMillisecondsInput, int index) throws Exception {

		ClickHouseDataSource clickHouseDataSource = null;
		Connection connection = null;

		databaseHostnameList = Arrays.asList(databaseHostnameArrayInput);
		databasePort = databasePortInput;
		databaseName = databaseNameInput;
		batchCreationSize = batchCreationSizeInput;
		databaseUser = databaseUserInput;
		databasePassword = databasePasswordInput;
		connectionTimeoutInMilliseconds = connectionTimeoutInMillisecondsInput;
		maximumRetryCounts = maximumRetryCountsInput;
		retryWaitMilliseconds = retryWaitMillisecondsInput;
		daoIndex = index;

		ClickHouseProperties clickHouseProperties = new ClickHouseProperties();
		clickHouseProperties.setDecompress(true);
		clickHouseProperties.setConnectionTimeout(connectionTimeoutInMilliseconds);
		clickHouseProperties.setSocketTimeout(connectionTimeoutInMilliseconds);

		connectionList = new ArrayList<Connection>();

		String connectionUrl = null;

		connectionUrlList = new ArrayList<String>();

		for (String databaseHostname : databaseHostnameList) {
			if (StringUtils.isNotBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
						+ "?user=" + databaseUser + "&password=" + databasePassword;
			} else if (StringUtils.isBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
						+ "?password=" + databasePassword;
			} else {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH
						+ databaseName;
			}
			connectionUrlList.add(connectionUrl);
			//FormatUtils.getInstance().logMemoryUsage("TargetUrlCustomDataClickHouseDAO() connectionUrl=" + connectionUrl);
			clickHouseDataSource = new ClickHouseDataSource(connectionUrl, clickHouseProperties);
			connection = clickHouseDataSource.getConnection();
			connectionList.add(connection);
		}
	}

	@Override
	public String toString() {
		return "TargetUrlCustomDataClickHouseDAO [databaseHostnameList=" + databaseHostnameList.toString() + ", databasePort=" + databasePort + ", databaseName="
				+ databaseName + ", batchCreationSize=" + batchCreationSize + ", databaseUser=" + databaseUser + ", databasePassword=" + databasePassword
				+ ", connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ", maximumRetryCounts=" + maximumRetryCounts + ", retryWaitMilliseconds="
				+ retryWaitMilliseconds + ", daoIndex=" + daoIndex + "]";
	}

	private synchronized String getTableName(String tableName) {
		if (StringUtils.isNotBlank(tableName)) {
			return tableName;
		} else {
			return TABLE_NAME;
		}
	}

	public void detachDailyPartitions(Date startDate, Date endDate) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("detachDailyPartitions() begins. startDate=" + startDate + ",endDate=" + endDate);
		PreparedStatement preparedStatement = null;
		Connection connection = null;
		int retryCount = 0;
		Integer processYearMonthDay = null;
		// ALTER TABLE crawl.local_target_url_custom_data DETACH PARTITION '20190823'
		String sqlStringTemplate = "ALTER TABLE crawl." + getTableName(null) + " DETACH PARTITION '%d'";
		String sqlString = null;
		String connectionUrl = null;
		Date processDate = startDate;
		int recordCount = 0;
		while (processDate.after(endDate) == false) {
			FormatUtils.getInstance()
					.logMemoryUsage("detachDailyPartitions() processYearMonth=" + DateFormatUtils.format(processDate, IConstants.DATE_FORMAT_YYYYMMDD));
			processYearMonthDay = NumberUtils.toInt(DateFormatUtils.format(processDate, IConstants.DATE_FORMAT_YYYYMMDD));
			for (int i = 0; i < connectionList.size(); i++) {
				connection = connectionList.get(i);
				connectionUrl = connectionUrlList.get(i);
				FormatUtils.getInstance().logMemoryUsage("detachDailyPartitions() connectionUrl=" + connectionUrl);
				recordCount = getRecordCount(processDate, connection);
				FormatUtils.getInstance().logMemoryUsage("detachDailyPartitions() recordCount=" + recordCount);
				if (recordCount > 0) {
					retryCount = 0;
					while (retryCount < maximumRetryCounts) {
						try {
							sqlString = String.format(sqlStringTemplate, processYearMonthDay);
							FormatUtils.getInstance().logMemoryUsage("detachDailyPartitions() sqlString=" + sqlString);
							preparedStatement = connection.prepareStatement(sqlString);
							preparedStatement.executeUpdate();
							retryCount = maximumRetryCounts;
						} catch (Exception e) {
							retryCount++;
							if (retryCount >= maximumRetryCounts) {
								e.printStackTrace();
								throw e;
							} else {
								FormatUtils.getInstance().logMemoryUsage("detachDailyPartitions() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
								try {
									Thread.sleep(retryWaitMilliseconds);
								} catch (InterruptedException e1) {
									e1.printStackTrace();
								}
							}
						} finally {
							if (preparedStatement != null) {
								preparedStatement.closeOnCompletion();
							}
						}
					}
				}
			}
			processDate = DateUtils.addDays(processDate, +1);
		}
		FormatUtils.getInstance().logMemoryUsage("detachDailyPartitions() ends. startDate=" + startDate + ",endDate=" + endDate);
	}

	public synchronized int getRecordCount(Date dailyDataCreationDate, Connection connection) throws Exception {
		int totalRecords = 0;
		FormatUtils.getInstance().logMemoryUsage("getRecordCount() begins. dailyDataCreationDate=" + dailyDataCreationDate);
		ResultSet resultSet = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("  count(*) as total_records");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(null));
		stringBuilder.append(" where");
		stringBuilder.append("  daily_data_creation_date = ?");
		String sqlString = stringBuilder.toString();
		FormatUtils.getInstance().logMemoryUsage("getRecordCount() sqlString=" + sqlString);
		retryCount = 0;
		int index = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setDate(index++, new java.sql.Date(dailyDataCreationDate.getTime()));
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					totalRecords = resultSet.getInt(IConstants.TOTAL_RECORDS);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage(
							"getRecordCount() dailyDataCreationDate=" + dailyDataCreationDate + ",e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("getRecordCount() ends. dailyDataCreationDate=" + dailyDataCreationDate + ",totalRecords=" + totalRecords);
		return totalRecords;
	}

}
