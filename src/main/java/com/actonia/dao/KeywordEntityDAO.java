/**
 *
 */
package com.actonia.dao;

import java.util.List;

import org.springframework.jdbc.core.simple.ParameterizedBeanPropertyRowMapper;

import com.actonia.entity.KeywordEntity;

/**
 * com.actonia.subserver.dao.KeywordEntityDAO.java
 *
 * @version $Revision: 198105 $ $Author: ewain $
 */
public class KeywordEntityDAO extends BaseJdbcSupport<KeywordEntity> {

	@Override
	public String getTableName() {
		return "t_keyword";
	}

	public List<KeywordEntity> getIdLowercaseName(int domainId, int rankCheck) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select ");
		sql.append(" 	 id as id, ");
		sql.append(" 	 lower(keyword_name) as keywordName ");
		sql.append(" from ");
		sql.append("     t_keyword ");
		sql.append(" where ");
		sql.append("     own_domain_id = ? ");
		sql.append(" and rank_check = ? ");
		List<KeywordEntity> list = getSimpleJdbcTemplate().query(sql.toString(), ParameterizedBeanPropertyRowMapper.newInstance(KeywordEntity.class), domainId,
				rankCheck);
		return list;
	}

	public KeywordEntity getById(Long keywordId, int ownDomainId) {
		if (keywordId == null) {
			return null;
		}

		StringBuffer sql = new StringBuffer();
		sql.append(" select * from t_keyword ");
		sql.append(" where id = ? ");
		sql.append(" and own_domain_id = ? ");
		sql.append(" limit 1 ");

		return this.findObject(sql.toString(), keywordId, ownDomainId);
	}
}