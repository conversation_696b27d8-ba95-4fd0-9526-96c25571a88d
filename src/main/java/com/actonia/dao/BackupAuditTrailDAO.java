package com.actonia.dao;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.actonia.entity.BackupAuditTrailEntity;

public class BackupAuditTrailDAO extends BaseJdbcSupport<BackupAuditTrailEntity> {

	@Override
	public String getTableName() {
		return "backup_audit_trail";
	}

	public void create(BackupAuditTrailEntity backupAuditTrailEntity) {
		Map<String, Object> val = new HashMap<String, Object>();
		val.put("table_name", backupAuditTrailEntity.getTableName());
		val.put("bucket_name", backupAuditTrailEntity.getBucketName());
		val.put("partial_prefix", backupAuditTrailEntity.getPartialPrefix());
		val.put("backup_date", backupAuditTrailEntity.getBackupDate());
		val.put("start_date", backupAuditTrailEntity.getStartDate());
		val.put("end_date", backupAuditTrailEntity.getEndDate());
		insertWithoutAutoIncrementalKey(val);
	}

	public List<BackupAuditTrailEntity> getList(String tableName, String bucketName, String partialPrefix) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     *");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     table_name = ?");
		stringBuilder.append(" and bucket_name = ?");
		stringBuilder.append(" and partial_prefix = ?");
		stringBuilder.append(" order by");
		stringBuilder.append("     backup_date");
		String sqlString = stringBuilder.toString();
		return this.findBySql(sqlString, tableName, bucketName, partialPrefix);
	}

	public void delete(String tableName, String bucketName, String partialPrefix, String backupDate) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" delete from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     table_name = ?");
		stringBuilder.append(" and bucket_name = ?");
		stringBuilder.append(" and partial_prefix = ?");
		stringBuilder.append(" and backup_date = ?");
		String sqlString = stringBuilder.toString();
		this.executeUpdate(sqlString, tableName, bucketName, partialPrefix, backupDate);
	}

	public void updateEndDate(String tableName, String bucketName, String partialPrefix, String backupDateString, String endDateString) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" update " + getTableName());
		stringBuilder.append(" set");
		stringBuilder.append("   end_date = ?");
		stringBuilder.append(" where");
		stringBuilder.append("     table_name = ?");
		stringBuilder.append(" and bucket_name = ?");
		stringBuilder.append(" and partial_prefix = ?");
		stringBuilder.append(" and backup_date = ?");
		String sqlString = stringBuilder.toString();
		this.executeUpdate(sqlString, endDateString, tableName, bucketName, partialPrefix, backupDateString);
	}

}