package com.actonia.dao;

import java.util.ArrayList;
import java.util.List;

import com.actonia.IConstants;
import com.actonia.entity.PageContentChangeEntity;

public class PageContentChangeDAO extends BaseJdbcSupport<PageContentChangeEntity> {

	private static final int NUMBER_OF_FIELDS = 8;
	private static final String INSERT_VALUE_FORMAT_1 = "(?, ?, ?, ?, ?, ?, ?, ?)";
	private static final String INSERT_VALUE_FORMAT_2 = ",(?, ?, ?, ?, ?, ?, ?, ?)";

	@Override
	public String getTableName() {
		return "page_content_change";
	}

	public void insertMultiRowsBatch(List<PageContentChangeEntity> pageContentChangeEntityList) {
		Object[] objectArray = null;
		List<Object[]> objectArrayList = new ArrayList<Object[]>();
		List<PageContentChangeEntity> tempList = new ArrayList<PageContentChangeEntity>();

		for (PageContentChangeEntity pageContentChangeEntity : pageContentChangeEntityList) {
			tempList.add(pageContentChangeEntity);
			if (tempList.size() == IConstants.RECORDS_PER_SQL_STATEMENT) {
				objectArray = getObjectArrayForInsert(tempList);
				objectArrayList.add(objectArray);
				if (objectArrayList.size() == IConstants.SQL_STATEMENTS_PER_JDBC_CALL) {
					this.executeBatch(getInsertMultiRowsSqlStatement(IConstants.RECORDS_PER_SQL_STATEMENT), objectArrayList);
					objectArrayList = new ArrayList<Object[]>();
				}
				tempList = new ArrayList<PageContentChangeEntity>();
			}
		}

		if (objectArrayList != null && objectArrayList.size() > 0) {
			this.executeBatch(getInsertMultiRowsSqlStatement(IConstants.RECORDS_PER_SQL_STATEMENT), objectArrayList);
			objectArrayList = new ArrayList<Object[]>();
		}

		if (tempList != null && tempList.size() > 0) {
			objectArray = getObjectArrayForInsert(tempList);
			objectArrayList.add(objectArray);
			if (objectArrayList != null && objectArrayList.size() > 0) {
				this.executeBatch(getInsertMultiRowsSqlStatement(tempList.size()), objectArrayList);
				objectArrayList = new ArrayList<Object[]>();
			}
		}
	}

	private Object[] getObjectArrayForInsert(List<PageContentChangeEntity> pageContentChangeEntityList) {
		List<Object[]> tempObjectArrayList = new ArrayList<Object[]>();
		Object[] pageClarityObjectArray = null;
		int totalNumberOfObjects = 0;
		for (PageContentChangeEntity pageContentChangeEntity : pageContentChangeEntityList) {
			pageClarityObjectArray = new Object[] { pageContentChangeEntity.getDomainId(), pageContentChangeEntity.getChangeDate(),
					pageContentChangeEntity.getTargetUrlId(), pageContentChangeEntity.getChangeType(), pageContentChangeEntity.getDiff(),
					pageContentChangeEntity.getRespCodeChange(), pageContentChangeEntity.getSelectorType(), pageContentChangeEntity.getSelector() };
			tempObjectArrayList.add(pageClarityObjectArray);
		}
		totalNumberOfObjects = tempObjectArrayList.size() * NUMBER_OF_FIELDS;

		Object[] objectArray = new Object[totalNumberOfObjects];
		int idx = 0;
		for (Object[] tempObjectArray : tempObjectArrayList) {
			for (int i = 0; i < tempObjectArray.length; i++) {
				objectArray[idx++] = tempObjectArray[i];
			}
		}
		return objectArray;
	}

	private String getInsertMultiRowsSqlStatement(int recordsPerInsertStatement) {
		String response = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("insert into " + getTableName() + " ");
		stringBuilder.append("(");
		stringBuilder.append(" domain_id,");
		stringBuilder.append(" change_date,");
		stringBuilder.append(" target_url_id,");
		stringBuilder.append(" change_type,");
		stringBuilder.append(" diff,");
		stringBuilder.append(" resp_code_change,");
		stringBuilder.append(" selector_type,");
		stringBuilder.append(" selector");
		stringBuilder.append(")");
		stringBuilder.append("values ");
		String sqlString = stringBuilder.toString();

		StringBuilder sqlBuilder = new StringBuilder();
		sqlBuilder.append(sqlString);
		for (int i = 0; i < recordsPerInsertStatement; i++) {
			if (i == 0) {
				sqlBuilder.append(INSERT_VALUE_FORMAT_1);
			} else {
				sqlBuilder.append(INSERT_VALUE_FORMAT_2);
			}
		}
		sqlBuilder.append(IConstants.SEMI_COLON);
		response = sqlBuilder.toString();
		return response;
	}

	public PageContentChangeEntity get(int domainId, int changeDateNumber, Long targetUrlId, int changeType, Integer selectorType, String selector) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("select ");
		stringBuilder.append("    * ");
		stringBuilder.append("from ");
		stringBuilder.append(getTableName() + " ");
		stringBuilder.append("where ");
		stringBuilder.append("    domain_id = ? ");
		stringBuilder.append("and change_date = ? ");
		stringBuilder.append("and target_url_id = ? ");
		stringBuilder.append("and change_type = ? ");
		stringBuilder.append("and selector_type = ? ");
		stringBuilder.append("and selector = ? ");
		String sqlString = stringBuilder.toString();
		return findObject(sqlString, domainId, changeDateNumber, targetUrlId, changeType, selectorType, selector);
	}

	public List<PageContentChangeEntity> getGroupTagChanges(int domainId, int changeDateNumber, int groupTagType) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("select ");
		stringBuilder.append("    t_group_tag.id as groupTagId, ");
		stringBuilder.append(" 	  page_content_change.domain_id as domainId, ");
		stringBuilder.append(" 	  page_content_change.change_date as changeDate, ");
		stringBuilder.append("    page_content_change.target_url_id as targetUrlId, ");
		stringBuilder.append(" 	  page_content_change.change_type as changeType, ");
		stringBuilder.append(" 	  page_content_change.diff as diff, ");
		stringBuilder.append(" 	  page_content_change.resp_code_change as respCodeChange, ");
		stringBuilder.append(" 	  page_content_change.selector_type as selectorType, ");
		stringBuilder.append(" 	  page_content_change.selector as selector ");
		stringBuilder.append("from ");
		stringBuilder.append("    page_content_change page_content_change, ");
		stringBuilder.append("    t_group_tag t_group_tag, ");
		stringBuilder.append("    t_group_tag_relation t_group_tag_relation ");
		stringBuilder.append("where ");
		stringBuilder.append("    page_content_change.domain_id = ? ");
		stringBuilder.append("and page_content_change.change_date = ? ");
		stringBuilder.append("and page_content_change.domain_id = t_group_tag.domain_id ");
		stringBuilder.append("and t_group_tag.tag_type = ? ");
		stringBuilder.append("and t_group_tag_relation.group_tag_id = t_group_tag.id ");
		stringBuilder.append("and t_group_tag_relation.domain_id = t_group_tag.domain_id ");
		stringBuilder.append("and t_group_tag_relation.resource_type = t_group_tag.tag_type ");
		stringBuilder.append("and t_group_tag_relation.resource_id = page_content_change.target_url_id ");
		String sqlString = stringBuilder.toString();
		return findBySql(sqlString, domainId, changeDateNumber, groupTagType);
	}

	public void reset(int domainId, int changeDateNumber) {
		String sql = "delete from " + getTableName() + " where domain_id = ? and change_date= ? ";
		this.executeUpdate(sql, domainId, changeDateNumber);
	}

	public int clear(int domainId, int cutoffDate) {
		String sql = "delete from " + getTableName() + " where domain_id = ? and change_date <= ? ";
		return this.executeUpdate(sql, domainId, cutoffDate);
	}

	public List<Integer> getDistinctDomainId() {
		List<Integer> domainIdList = new ArrayList<Integer>();
		String sql = "select distinct domain_id from " + getTableName();
		List<PageContentChangeEntity> pageContentChangeEntityList = this.findBySql(sql);
		if (pageContentChangeEntityList != null && pageContentChangeEntityList.size() > 0) {
			for (PageContentChangeEntity pageContentChangeEntity : pageContentChangeEntityList) {
				domainIdList.add(pageContentChangeEntity.getDomainId());
			}
		}
		return domainIdList;
	}
}