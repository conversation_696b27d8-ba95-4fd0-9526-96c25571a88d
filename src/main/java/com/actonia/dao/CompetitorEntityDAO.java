package com.actonia.dao;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.actonia.entity.CompetitorEntity;

public class CompetitorEntityDAO extends BaseJdbcSupport<CompetitorEntity> {

	private static final int RECORDS_PER_SQL_STATEMENT = 10;
	private static final int SQL_STATEMENTS_PER_JDBC_CALL = 10;
	public static final int RECORDS_PER_BATCH_UPDATE = 100;
	private static final String SEMI_COLON = ";";
	private static final String INSERT_FORMAT_1 = "(?, ?, ?, ?, ?, ?, ?, ?, ?)";
	private static final String INSERT_FORMAT_2 = ",(?, ?, ?, ?, ?, ?, ?, ?, ?)";
	private static final int NUMBER_OF_FIELDS = 9;

	@Override
	public String getTableName() {
		return "t_competitor";
	}

	public List<CompetitorEntity> queryForAllByDomainId(int domainId) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from t_competitor where owndomain_id = ?");
		return findBySql(sql.toString(), domainId);
	}

	public int insertWithCreateDate(CompetitorEntity competitorEntity) {
		Map<String, Object> values = new HashMap<String, Object>();
		values.put("domain", competitorEntity.getDomain());
		values.put("name", competitorEntity.getDomain());
		values.put("create_date", competitorEntity.getCreateDate());
		values.put("owndomain_id", competitorEntity.getOwndomainId());
		values.put("add_by", competitorEntity.getAddBy());

		return this.insert(values);
	}

    public CompetitorEntity findById(int id) {
        String sql = "select * from t_competitor where id=?";
        return findObject(sql, id);
    }

    public int delete(int id) {
        String sql = "delete from t_competitor where id = ?";
        return this.executeUpdate(sql, id);
    }
}
