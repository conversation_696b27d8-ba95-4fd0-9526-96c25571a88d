package com.actonia.dao;

import com.actonia.IConstants;
import com.actonia.entity.ManagedHtmlChange;
import com.actonia.utils.FormatUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import ru.yandex.clickhouse.ClickHouseDataSource;
import ru.yandex.clickhouse.settings.ClickHouseProperties;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.*;

// Due to ClickHouse concurrency issues, use Singleton pattern, do not use Spring (ie. all methods must be 'synchronized')
public class ManagedHtmlChangeClickHouseDAO {

    //private static boolean isDebug = false;
    public static final String TABLE_NAME = "dis_managed_html_change";
    private static final int TOTAL_DAO_INSTANCES = 1;
    private static final Logger log = LogManager.getLogger(ManagedHtmlChangeClickHouseDAO.class);
    private static int daoMapIndex = 0;

    // map key = DAO index (0  - 59)
    // map value = instance of ManagedHtmlChangeClickHouseDAO
    private static Map<Integer, ManagedHtmlChangeClickHouseDAO> managedHtmlChangeClickHouseDAOMap;

    private final List<String> databaseHostnameList;
    private final String databasePort;
    private final String databaseName;
    private final List<Connection> connectionList;
    private final List<String> connectionUrlList;
    private final int batchCreationSize;
    private final String databaseUser;
    private final String databasePassword;
    private final int connectionTimeoutInMilliseconds;
    private final int maximumRetryCounts;
    private final int retryWaitMilliseconds;
    private final int daoIndex;

    private ManagedHtmlChangeClickHouseDAO(String[] databaseHostnameArrayInput, String databasePortInput, String databaseNameInput, int batchCreationSizeInput,
                                           String databaseUserInput, String databasePasswordInput, int connectionTimeoutInMillisecondsInput, int maximumRetryCountsInput,
                                           int retryWaitMillisecondsInput, int index) throws Exception {

        ClickHouseDataSource clickHouseDataSource = null;
        Connection connection = null;

        databaseHostnameList = Arrays.asList(databaseHostnameArrayInput);
        databasePort = databasePortInput;
        databaseName = databaseNameInput;
        batchCreationSize = batchCreationSizeInput;
        databaseUser = databaseUserInput;
        databasePassword = databasePasswordInput;
        connectionTimeoutInMilliseconds = connectionTimeoutInMillisecondsInput;
        maximumRetryCounts = maximumRetryCountsInput;
        retryWaitMilliseconds = retryWaitMillisecondsInput;
        daoIndex = index;

        ClickHouseProperties clickHouseProperties = new ClickHouseProperties();
        clickHouseProperties.setDecompress(true);
        clickHouseProperties.setConnectionTimeout(connectionTimeoutInMilliseconds);
        clickHouseProperties.setSocketTimeout(connectionTimeoutInMilliseconds);

        connectionList = new ArrayList<>();

        connectionUrlList = new ArrayList<>();

        String connectionUrl = null;
        for (String databaseHostname : databaseHostnameList) {
            if (StringUtils.isNotBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
                connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
                        + "?user=" + databaseUser + "&password=" + databasePassword;
            } else if (StringUtils.isBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
                connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
                        + "?password=" + databasePassword;
            } else {
                connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH
                        + databaseName;
            }
            connectionUrlList.add(connectionUrl);
            //FormatUtils.getInstance().logMemoryUsage("ManagedHtmlChangeClickHouseDAO() connectionUrl=" + connectionUrl);
            clickHouseDataSource = new ClickHouseDataSource(connectionUrl, clickHouseProperties);
            connection = clickHouseDataSource.getConnection();
            connectionList.add(connection);
        }
    }

    public static ManagedHtmlChangeClickHouseDAO getInstance() throws Exception {
        ManagedHtmlChangeClickHouseDAO managedHtmlChangeClickHouseDAO;
        if (managedHtmlChangeClickHouseDAOMap == null) {
            String clickHouseDatabaseHostnames = ClickHouseGADatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_HOSTNAMES);
            String[] clickHouseDatabaseHostnameArray = clickHouseDatabaseHostnames.split(IConstants.COMMA);
            String clickHouseDatabasePort = ClickHouseGADatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_PORT);
            String clickHouseDatabaseName = ClickHouseGADatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_NAME);
            String clickHouseDatabaseUser = ClickHouseGADatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_USER);
            String clickHouseDatabasePassword = ClickHouseGADatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_PASSWORD);
            int clickHouseBatchCreationSize = ClickHouseGADatabaseConfigurations
                    .getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_BATCH_CREATION_SIZE, 100);
            int clickHouseConnectionTimeoutInMilliseconds = ClickHouseGADatabaseConfigurations
                    .getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_CONNECTION_TIMEOUT_MILLISECONDS, 88888);
            int clickHouseMaximumRetryCounts = ClickHouseGADatabaseConfigurations.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_MAX_RETRY_COUNTS,
                    3);
            int clickHouseRetryWaitMilliseconds = ClickHouseGADatabaseConfigurations
                    .getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_RETRY_WAIT_MILLISECONDS, 1000);
            FormatUtils.getInstance().logMemoryUsage("ManagedHtmlChangeClickHouseDAO() clickHouseDatabaseHostnames=" + clickHouseDatabaseHostnames
                    + ",clickHouseDatabasePort=" + clickHouseDatabasePort + ",clickHouseDatabaseName=" + clickHouseDatabaseName + ",clickHouseBatchCreationSize="
                    + clickHouseBatchCreationSize + ",clickHouseDatabaseUser=" + clickHouseDatabaseUser + ",clickHouseDatabasePassword=" + clickHouseDatabasePassword
                    + ",clickHouseConnectionTimeoutInMilliseconds=" + clickHouseConnectionTimeoutInMilliseconds + ",clickHouseMaximumRetryCounts="
                    + clickHouseMaximumRetryCounts + ",clickHouseRetryWaitMilliseconds=" + clickHouseRetryWaitMilliseconds + ",TABLE_NAME=" + TABLE_NAME);

            managedHtmlChangeClickHouseDAOMap = new HashMap<>();
            for (int i = 0; i < TOTAL_DAO_INSTANCES; i++) {
                managedHtmlChangeClickHouseDAO = initialize(clickHouseDatabaseHostnameArray, clickHouseDatabasePort, clickHouseDatabaseName, clickHouseDatabaseUser,
                        clickHouseDatabasePassword, clickHouseBatchCreationSize, clickHouseConnectionTimeoutInMilliseconds, clickHouseMaximumRetryCounts,
                        clickHouseRetryWaitMilliseconds, i);
                managedHtmlChangeClickHouseDAOMap.put(i, managedHtmlChangeClickHouseDAO);
            }
            FormatUtils.getInstance().logMemoryUsage("getInstance() total managedHtmlChangeClickHouseDAOs=" + managedHtmlChangeClickHouseDAOMap.size());
        }
        int index = getDaoMapIndex();
        managedHtmlChangeClickHouseDAO = managedHtmlChangeClickHouseDAOMap.get(index);
        return managedHtmlChangeClickHouseDAO;
    }

    private static synchronized int getDaoMapIndex() {
        int index = 0;
        if (daoMapIndex >= TOTAL_DAO_INSTANCES) {
            daoMapIndex = 0;
        } else {
            index = daoMapIndex++;
        }
        return index;
    }

    // initialize ManagedHtmlChangeClickHouseDAO based on runtime clickhouse configurations
    private static ManagedHtmlChangeClickHouseDAO initialize(String[] clickHouseDatabaseHostnameArray, String clickHouseDatabasePort, String clickHouseDatabaseName,
                                                             String clickHouseDatabaseUser, String clickHouseDatabasePassword, int clickHouseBatchCreationSize, int clickHouseconnectionTimeoutInMilliseconds,
                                                             int clickHouseMaximumRetryCounts, int clickHouseRetryWaitMilliseconds, int daoIndex) throws Exception {
        //FormatUtils.getInstance().logMemoryUsage("initialize() managedHtmlChangeClickHouseDAO=" + managedHtmlChangeClickHouseDAO.toString());
        return new ManagedHtmlChangeClickHouseDAO(clickHouseDatabaseHostnameArray,
                clickHouseDatabasePort, clickHouseDatabaseName, clickHouseBatchCreationSize, clickHouseDatabaseUser, clickHouseDatabasePassword,
                clickHouseconnectionTimeoutInMilliseconds, clickHouseMaximumRetryCounts, clickHouseRetryWaitMilliseconds, daoIndex);
    }

    @Override
    public String toString() {
        return "ManagedHtmlChangeClickHouseDAO [databaseHostnameList=" + databaseHostnameList.toString() + ", databasePort=" + databasePort + ", databaseName="
                + databaseName + ", batchCreationSize=" + batchCreationSize + ", databaseUser=" + databaseUser + ", databasePassword=" + databasePassword
                + ", connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ", maximumRetryCounts=" + maximumRetryCounts + ", retryWaitMilliseconds="
                + retryWaitMilliseconds + ", daoIndex=" + daoIndex + "]";
    }

    public synchronized int getBatchCreationSize() {
        return batchCreationSize;
    }

    public synchronized void createBatch(List<ManagedHtmlChange> managedHtmlChangeList) {
        final String sql = "insert into " + TABLE_NAME + " (track_date, domain_id, url, chg_id, prev_value, curr_value, prev_crawl_timestamp, curr_crawl_timestamp, create_timestamp) values (?,?,?,?,?,?,?,?,?)";
        int retryCount = 0;
        while (retryCount < maximumRetryCounts) {
            Connection connection = connectionList.get(0);
            try (PreparedStatement preparedStatement = connection.prepareStatement(sql)) {
                for (ManagedHtmlChange managedHtmlChange : managedHtmlChangeList) {
                    int index = 1;
                    preparedStatement.setDate(index++, new java.sql.Date(managedHtmlChange.getTrackDate().getTime()));
                    preparedStatement.setInt(index++, managedHtmlChange.getDomainId());
                    preparedStatement.setString(index++, managedHtmlChange.getUrl());
                    preparedStatement.setInt(index++, managedHtmlChange.getChgId());
                    preparedStatement.setString(index++, managedHtmlChange.getPrevValue());
                    preparedStatement.setString(index++, managedHtmlChange.getCurrValue());
                    preparedStatement.setTimestamp(index++, new java.sql.Timestamp(managedHtmlChange.getPrevCrawlTimestamp().getTime()));
                    preparedStatement.setTimestamp(index++, new java.sql.Timestamp(managedHtmlChange.getCurrCrawlTimestamp().getTime()));
                    preparedStatement.setTimestamp(index++, new java.sql.Timestamp(managedHtmlChange.getCreateTimestamp().getTime()));
                    preparedStatement.addBatch();
                }
                preparedStatement.executeBatch();
                return;
            } catch (SQLException e) {
                log.error("batchCreateHtmlChange() sql=" + sql, e);
                retryCount++;
                try {
                    Thread.sleep(retryWaitMilliseconds);
                } catch (InterruptedException e1) {
                    log.error("batchCreateHtmlChange() Thread.sleep() error", e1);
                }
            }
        }
        log.error("batchCreateHtmlChange() retryCount={} sql=" + sql, retryCount);
        throw new RuntimeException("batchCreateHtmlChange() failed");
    }

}