package com.actonia.dao;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

import com.actonia.entity.ZapierWebhookEntity;

public class ZapierWebhookDAO extends BaseJdbcSupport<ZapierWebhookEntity> {

	@Override
	public String getTableName() {
		return "zapier_webhook";
	}

	public Long create(ZapierWebhookEntity zapierWebhookEntity) {
		Map<String, Object> values = new HashMap<String, Object>();
		values.put("domain_id", zapierWebhookEntity.getDomainId());
		values.put("trigger_type", zapierWebhookEntity.getTriggerType());
		values.put("user_id", zapierWebhookEntity.getUserId());
		if (StringUtils.isNotBlank(zapierWebhookEntity.getSubType())) {
			values.put("sub_type_hash_cd", zapierWebhookEntity.getSubTypeHashCd());
			values.put("sub_type", zapierWebhookEntity.getSubType());
		}
		values.put("callback_url", zapierWebhookEntity.getCallbackUrl());
		values.put("create_date", zapierWebhookEntity.getCreateDate());
		return this.insertForLongId(values);
	}

	public ZapierWebhookEntity get(int domainId, int triggerType, int userId) {
		return get(domainId, triggerType, userId, null);
	}

	public ZapierWebhookEntity get(int domainId, int triggerType, int userId, String subTypeHashCd) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     *");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     domain_id = ?");
		stringBuilder.append(" and trigger_type = ?");
		stringBuilder.append(" and user_id = ?");
		if (StringUtils.isNotBlank(subTypeHashCd)) {
			stringBuilder.append(" and sub_type_hash_cd = ?");
		} else {
			stringBuilder.append(" and sub_type_hash_cd is null");
		}
		String sqlString = stringBuilder.toString();
		if (StringUtils.isNotBlank(subTypeHashCd)) {
			return this.findObject(sqlString, domainId, userId, triggerType, subTypeHashCd);
		} else {
			return this.findObject(sqlString, domainId, userId, triggerType);
		}
	}

	public ZapierWebhookEntity get(Long id) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     *");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     id = ?");
		String sqlString = stringBuilder.toString();
		return this.findObject(sqlString, id);
	}

	public int delete(Long id) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" delete from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     id = ?");
		String sqlString = stringBuilder.toString();
		return this.executeUpdate(sqlString, id);
	}

	public List<ZapierWebhookEntity> getByDomainIdTriggerType(int domainId, int triggerType) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     *");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     domain_id = ?");
		stringBuilder.append(" and trigger_type = ?");
		String sqlString = stringBuilder.toString();
		return this.findBySql(sqlString, domainId, triggerType);
	}

}