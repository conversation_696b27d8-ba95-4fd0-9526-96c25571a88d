package com.actonia.dao;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.actonia.entity.CompetitorUrlMd5Entity;

public class CompetitorUrlMd5EntityDAO extends BaseJdbcSupport<CompetitorUrlMd5Entity> {

	private static final int RECORDS_PER_SQL_STATEMENT = 10;
	private static final int SQL_STATEMENTS_PER_JDBC_CALL = 10;
	private static final String SEMI_COLON = ";";
	private static final String INSERT_FORMAT_1 = "(?, ?, ?)";
	private static final String INSERT_FORMAT_2 = ",(?, ?, ?)";
	private static final int NUMBER_OF_FIELDS = 3;

	@Override
	public String getTableName() {
		return "competitor_url_md5";
	}

	public void cleanup(int domainId) {
		String sqlString = "delete from " + getTableName() + " where domain_id = ?";
		this.executeUpdate(sqlString, domainId);
	}

	public Integer getInvalidCompetitorUrlCount(int domainId) {
		String sqlString = "select 1 from " + getTableName() + " where domain_id = ? and competitor_url_id = 0 limit 1";
		return this.queryForInteger(sqlString, domainId);
	}

	public void cleanupInvalidCompetitorUrl(int domainId) {
		String sqlString = "delete from " + getTableName() + " where domain_id = ? and competitor_url_id = 0";
		this.executeUpdate(sqlString, domainId);
	}

	public Map<String, CompetitorUrlMd5Entity> getByDomain(int domainId) {
		Map<String, CompetitorUrlMd5Entity> hashCodeEntityMap = new HashMap<String, CompetitorUrlMd5Entity>();
		String sqlString = "select * from " + getTableName() + " where domain_id = ?";
		List<CompetitorUrlMd5Entity> competitorUrlMd5EntityList = findBySql(sqlString, domainId);
		if (competitorUrlMd5EntityList != null && competitorUrlMd5EntityList.size() > 0) {
			for (CompetitorUrlMd5Entity competitorUrlMd5Entity : competitorUrlMd5EntityList) {
				hashCodeEntityMap.put(competitorUrlMd5Entity.getHashCode(), competitorUrlMd5Entity);
			}
		}
		return hashCodeEntityMap;
	}

	//public CompetitorUrlMd5Entity getByDomainIdCompetitorUrlHashCode(int domainId, String hashCode) {
	//	String sqlString = "select * from " + getTableName() + " where domain_id = ? and hash_code = ?";
	//	return findObject(sqlString, domainId, hashCode);
	//}

	public void insertMultiRowsBatch(List<CompetitorUrlMd5Entity> competitorUrlMd5EntityList) {
		Object[] objectArray = null;
		List<Object[]> objectArrayList = new ArrayList<Object[]>();
		List<CompetitorUrlMd5Entity> tempList = new ArrayList<CompetitorUrlMd5Entity>();

		for (CompetitorUrlMd5Entity competitorUrlMd5Entity : competitorUrlMd5EntityList) {
			tempList.add(competitorUrlMd5Entity);
			if (tempList.size() == RECORDS_PER_SQL_STATEMENT) {
				objectArray = getObjectArrayForInsert(tempList);
				objectArrayList.add(objectArray);
				if (objectArrayList.size() == SQL_STATEMENTS_PER_JDBC_CALL) {
					this.executeBatch(getInsertMultiRowsSqlStatement(RECORDS_PER_SQL_STATEMENT), objectArrayList);
					objectArrayList = new ArrayList<Object[]>();
				}
				tempList = new ArrayList<CompetitorUrlMd5Entity>();
			}
		}

		if (objectArrayList != null && objectArrayList.size() > 0) {
			this.executeBatch(getInsertMultiRowsSqlStatement(RECORDS_PER_SQL_STATEMENT), objectArrayList);
			objectArrayList = new ArrayList<Object[]>();
		}

		if (tempList != null && tempList.size() > 0) {
			objectArray = getObjectArrayForInsert(tempList);
			objectArrayList.add(objectArray);
			if (objectArrayList != null && objectArrayList.size() > 0) {
				this.executeBatch(getInsertMultiRowsSqlStatement(tempList.size()), objectArrayList);
				objectArrayList = new ArrayList<Object[]>();
			}
		}
	}

	private Object[] getObjectArrayForInsert(List<CompetitorUrlMd5Entity> list) {
		List<Object[]> tempObjectArrayList = new ArrayList<Object[]>();
		Object[] backlinkObjectArray = null;
		int totalNumberOfObjects = 0;
		for (CompetitorUrlMd5Entity competitorUrlMd5Entity : list) {
			backlinkObjectArray = new Object[] { competitorUrlMd5Entity.getDomainId(), competitorUrlMd5Entity.getHashCode(),
					competitorUrlMd5Entity.getCompetitorUrlId(), };
			tempObjectArrayList.add(backlinkObjectArray);
		}
		totalNumberOfObjects = tempObjectArrayList.size() * NUMBER_OF_FIELDS;
		Object[] objectArray = new Object[totalNumberOfObjects];
		int idx = 0;
		for (Object[] tempObjectArray : tempObjectArrayList) {
			for (int i = 0; i < tempObjectArray.length; i++) {
				objectArray[idx++] = tempObjectArray[i];
			}
		}
		return objectArray;
	}

	private String getInsertMultiRowsSqlStatement(int recordsPerInsertStatement) {
		String response = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("insert into " + getTableName() + " ");
		stringBuilder.append("(");
		stringBuilder.append("	domain_id,");
		stringBuilder.append("	hash_code,");
		stringBuilder.append("	competitor_url_id");
		stringBuilder.append(")");
		stringBuilder.append("values ");
		String sqlString = stringBuilder.toString();
		StringBuffer sql = new StringBuffer();
		sql.append(sqlString);
		for (int i = 0; i < recordsPerInsertStatement; i++) {
			if (i == 0) {
				sql.append(INSERT_FORMAT_1);
			} else {
				sql.append(INSERT_FORMAT_2);
			}
		}
		sql.append(SEMI_COLON);
		response = sql.toString();
		return response;
	}

	public int updateCompetitorUrlId(int domainId, String hashCode, int competitorUrlId) {
		String sqlString = "update " + getTableName() + " set competitor_url_id = ? where domain_id = ? and hash_code = ?";
		return this.executeUpdate(sqlString, competitorUrlId, domainId, hashCode);
	}

	public void create(CompetitorUrlMd5Entity competitorUrlMd5Entity) {
		Map<String, Object> values = new HashMap<String, Object>();
		values.put("domain_id", competitorUrlMd5Entity.getDomainId());
		values.put("hash_code", competitorUrlMd5Entity.getHashCode());
		values.put("competitor_url_id", competitorUrlMd5Entity.getCompetitorUrlId());
		this.insertWithoutAutoIncrementalKey(values);
	}

	public List<CompetitorUrlMd5Entity> getList(int domainId) {
		String sqlString = "select * from " + getTableName() + " where domain_id = ?";
		return findBySql(sqlString, domainId);
	}

	public void delete(int domainId, String hashCode) {
		String sqlString = "delete from " + getTableName() + " where domain_id = ? and hash_code = ?";
		this.executeUpdate(sqlString, domainId, hashCode);
	}
}
