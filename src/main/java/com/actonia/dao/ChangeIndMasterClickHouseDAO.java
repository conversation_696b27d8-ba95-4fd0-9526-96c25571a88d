package com.actonia.dao;

import com.actonia.IConstants;
import com.actonia.entity.ChangeIndMaster;
import com.actonia.utils.FormatUtils;
import org.apache.commons.lang.StringUtils;
import ru.yandex.clickhouse.ClickHouseDataSource;
import ru.yandex.clickhouse.settings.ClickHouseProperties;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

// Due to ClickHouse concurrency issues, use Singleton pattern, do not use Spring (ie. all methods must be 'synchronized')
public class ChangeIndMasterClickHouseDAO {

    //private static boolean isDebug = false;
    public static final String TABLE_NAME = "dis_change_ind_master";
    private static final int TOTAL_DAO_INSTANCES = 1;
    private static int daoMapIndex = 0;

    // map key = DAO index (0  - 59)
    // map value = instance of ChangeIndMasterClickHouseDAO
    private static Map<Integer, ChangeIndMasterClickHouseDAO> changeIndMasterClickHouseDAOMap;

    private final List<String> databaseHostnameList;
    private final String databasePort;
    private final String databaseName;
    private final List<Connection> connectionList;
    private final List<String> connectionUrlList;
    private final int batchCreationSize;
    private final String databaseUser;
    private final String databasePassword;
    private final int connectionTimeoutInMilliseconds;
    private final int maximumRetryCounts;
    private final int retryWaitMilliseconds;
    private final int daoIndex;
    public final Map<Integer, ChangeIndMaster> changeIndMasterMap;
    public final Map<String, Integer> indicatorReverseIdMap;
    public final Map<String, ChangeIndMaster> indicatorMap;
    public final Set<ChangeIndMaster> bigDataIndicatorSet;

    private ChangeIndMasterClickHouseDAO(String[] databaseHostnameArrayInput, String databasePortInput, String databaseNameInput, int batchCreationSizeInput,
                                         String databaseUserInput, String databasePasswordInput, int connectionTimeoutInMillisecondsInput, int maximumRetryCountsInput,
                                         int retryWaitMillisecondsInput, int index) throws Exception {

        ClickHouseDataSource clickHouseDataSource = null;
        Connection connection = null;

        databaseHostnameList = Arrays.asList(databaseHostnameArrayInput);
        databasePort = databasePortInput;
        databaseName = databaseNameInput;
        batchCreationSize = batchCreationSizeInput;
        databaseUser = databaseUserInput;
        databasePassword = databasePasswordInput;
        connectionTimeoutInMilliseconds = connectionTimeoutInMillisecondsInput;
        maximumRetryCounts = maximumRetryCountsInput;
        retryWaitMilliseconds = retryWaitMillisecondsInput;
        daoIndex = index;

        ClickHouseProperties clickHouseProperties = new ClickHouseProperties();
        clickHouseProperties.setDecompress(true);
        clickHouseProperties.setConnectionTimeout(connectionTimeoutInMilliseconds);
        clickHouseProperties.setSocketTimeout(connectionTimeoutInMilliseconds);

        connectionList = new ArrayList<>();

        connectionUrlList = new ArrayList<>();

        String connectionUrl = null;
        for (String databaseHostname : databaseHostnameList) {
            if (StringUtils.isNotBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
                connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
                        + "?user=" + databaseUser + "&password=" + databasePassword;
            } else if (StringUtils.isBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
                connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
                        + "?password=" + databasePassword;
            } else {
                connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH
                        + databaseName;
            }
            connectionUrlList.add(connectionUrl);
            //FormatUtils.getInstance().logMemoryUsage("ChangeIndMasterClickHouseDAO() connectionUrl=" + connectionUrl);
            clickHouseDataSource = new ClickHouseDataSource(connectionUrl, clickHouseProperties);
            connection = clickHouseDataSource.getConnection();
            connectionList.add(connection);
        }

        final List<ChangeIndMaster> changeIndMasterList = this.getChangeIndMasterList(null);
        changeIndMasterMap = changeIndMasterList.parallelStream().collect(Collectors.toMap(ChangeIndMaster::getChgId, Function.identity()));
        indicatorReverseIdMap = changeIndMasterList.parallelStream().filter(changeIndMaster -> !changeIndMaster.getChgIndicator().isEmpty()).collect(Collectors.toMap(ChangeIndMaster::getChgIndicator, ChangeIndMaster::getChgId));
        indicatorMap = changeIndMasterList.parallelStream().filter(changeIndMaster -> !changeIndMaster.getChgIndicator().isEmpty()).collect(Collectors.toMap(ChangeIndMaster::getChgIndicator, Function.identity()));
        bigDataIndicatorSet = changeIndMasterList.parallelStream().filter(changeIndMaster -> changeIndMaster.getBigDataFlg() == 1).collect(Collectors.toSet());
    }

    public static ChangeIndMasterClickHouseDAO getInstance() throws Exception {
        ChangeIndMasterClickHouseDAO changeIndMasterClickHouseDAO;
        if (changeIndMasterClickHouseDAOMap == null) {
            String clickHouseDatabaseHostnames = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_HOSTNAMES);
            String[] clickHouseDatabaseHostnameArray = clickHouseDatabaseHostnames.split(IConstants.COMMA);
            String clickHouseDatabasePort = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_PORT);
            String clickHouseDatabaseName = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_NAME);
            String clickHouseDatabaseUser = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_USER);
            String clickHouseDatabasePassword = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_PASSWORD);
            int clickHouseBatchCreationSize = ClickHouseCrawlDatabaseConfigurations
                    .getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_BATCH_CREATION_SIZE, 100);
            int clickHouseConnectionTimeoutInMilliseconds = ClickHouseCrawlDatabaseConfigurations
                    .getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_CONNECTION_TIMEOUT_MILLISECONDS, 88888);
            int clickHouseMaximumRetryCounts = ClickHouseCrawlDatabaseConfigurations.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_MAX_RETRY_COUNTS,
                    8);
            int clickHouseRetryWaitMilliseconds = ClickHouseCrawlDatabaseConfigurations
                    .getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_RETRY_WAIT_MILLISECONDS, 1000);
            FormatUtils.getInstance().logMemoryUsage("ChangeIndMasterClickHouseDAO() clickHouseDatabaseHostnames=" + clickHouseDatabaseHostnames
                    + ",clickHouseDatabasePort=" + clickHouseDatabasePort + ",clickHouseDatabaseName=" + clickHouseDatabaseName + ",clickHouseBatchCreationSize="
                    + clickHouseBatchCreationSize + ",clickHouseDatabaseUser=" + clickHouseDatabaseUser + ",clickHouseDatabasePassword=" + clickHouseDatabasePassword
                    + ",clickHouseConnectionTimeoutInMilliseconds=" + clickHouseConnectionTimeoutInMilliseconds + ",clickHouseMaximumRetryCounts="
                    + clickHouseMaximumRetryCounts + ",clickHouseRetryWaitMilliseconds=" + clickHouseRetryWaitMilliseconds + ",TABLE_NAME=" + TABLE_NAME);

            changeIndMasterClickHouseDAOMap = new HashMap<>();
            for (int i = 0; i < TOTAL_DAO_INSTANCES; i++) {
                changeIndMasterClickHouseDAO = initialize(clickHouseDatabaseHostnameArray, clickHouseDatabasePort, clickHouseDatabaseName, clickHouseDatabaseUser,
                        clickHouseDatabasePassword, clickHouseBatchCreationSize, clickHouseConnectionTimeoutInMilliseconds, clickHouseMaximumRetryCounts,
                        clickHouseRetryWaitMilliseconds, i);
                changeIndMasterClickHouseDAOMap.put(i, changeIndMasterClickHouseDAO);
            }
            FormatUtils.getInstance().logMemoryUsage("getInstance() total changeIndMasterClickHouseDAOs=" + changeIndMasterClickHouseDAOMap.size());
        }
        int index = getDaoMapIndex();
        changeIndMasterClickHouseDAO = changeIndMasterClickHouseDAOMap.get(index);
        return changeIndMasterClickHouseDAO;
    }

    private static synchronized int getDaoMapIndex() {
        int index = 0;
        if (daoMapIndex >= TOTAL_DAO_INSTANCES) {
            daoMapIndex = 0;
        } else {
            index = daoMapIndex++;
        }
        return index;
    }

    // initialize ChangeIndMasterClickHouseDAO based on runtime clickhouse configurations
    private static ChangeIndMasterClickHouseDAO initialize(String[] clickHouseDatabaseHostnameArray, String clickHouseDatabasePort, String clickHouseDatabaseName,
                                                           String clickHouseDatabaseUser, String clickHouseDatabasePassword, int clickHouseBatchCreationSize, int clickHouseconnectionTimeoutInMilliseconds,
                                                           int clickHouseMaximumRetryCounts, int clickHouseRetryWaitMilliseconds, int daoIndex) throws Exception {
        //FormatUtils.getInstance().logMemoryUsage("initialize() changeIndMasterClickHouseDAO=" + changeIndMasterClickHouseDAO.toString());
        return new ChangeIndMasterClickHouseDAO(clickHouseDatabaseHostnameArray,
                clickHouseDatabasePort, clickHouseDatabaseName, clickHouseBatchCreationSize, clickHouseDatabaseUser, clickHouseDatabasePassword,
                clickHouseconnectionTimeoutInMilliseconds, clickHouseMaximumRetryCounts, clickHouseRetryWaitMilliseconds, daoIndex);
    }

    @Override
    public String toString() {
        return "ChangeIndMasterClickHouseDAO [databaseHostnameList=" + databaseHostnameList.toString() + ", databasePort=" + databasePort + ", databaseName="
                + databaseName + ", batchCreationSize=" + batchCreationSize + ", databaseUser=" + databaseUser + ", databasePassword=" + databasePassword
                + ", connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ", maximumRetryCounts=" + maximumRetryCounts + ", retryWaitMilliseconds="
                + retryWaitMilliseconds + ", daoIndex=" + daoIndex + "]";
    }

    private synchronized String getTableName(String tableName) {
        if (StringUtils.isNotBlank(tableName)) {
            return tableName;
        } else {
            return TABLE_NAME;
        }
    }

    public synchronized int getBatchCreationSize() {
        return batchCreationSize;
    }

    public List<ChangeIndMaster> getChangeIndMasterList(String tableName) {
        List<ChangeIndMaster> changeIndMasterList = new ArrayList<>();
        final String sql = "select * from " + getTableName(tableName);
        Connection connection = connectionList.get(0);
        try (ResultSet resultSet = connection.createStatement().executeQuery(sql)) {
            while (resultSet.next()) {
                ChangeIndMaster changeIndMaster = new ChangeIndMaster();
                changeIndMaster.setChgId(resultSet.getInt("chg_id"));
                changeIndMaster.setChgIndicator(resultSet.getString("chg_indicator"));
                changeIndMaster.setChgField(resultSet.getString("chg_field"));
                changeIndMaster.setChgType(resultSet.getString("chg_type"));
                changeIndMaster.setBigDataFlg(resultSet.getInt("big_data_flg"));
                changeIndMaster.setCriticalFlg(resultSet.getInt("critical_flg"));
                changeIndMasterList.add(changeIndMaster);
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return changeIndMasterList;
    }

}