package com.actonia.dao;

import java.util.List;

import com.actonia.entity.TargetUrlChangeAlertEntity;
import com.actonia.entity.TargetUrlChangeAlertEntity;

public class TargetUrlChangeAlertDAO extends BaseJdbcSupport<TargetUrlChangeAlertEntity> {

	@Override
	public String getTableName() {
		return "target_url_change_alert";
	}

	public List<Integer> getDomainIdsByAlertFrequency(int alertFrequencyType) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select distinct");
		stringBuilder.append("     domain_id");
		stringBuilder.append(" from");
		stringBuilder.append("     " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     alert_frequency = ?");
		String sqlString = stringBuilder.toString();
		return this.queryForIntegerList(sqlString, alertFrequencyType);
	}

	public List<TargetUrlChangeAlertEntity> getListByDomainAlertFrequency(int domainId, int alertFrequencyType) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     *");
		stringBuilder.append(" from");
		stringBuilder.append("     " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     domain_id = ?");
		stringBuilder.append(" and alert_frequency = ?");
		String sqlString = stringBuilder.toString();
		return this.findBySql(sqlString, domainId, alertFrequencyType);
	}

	public List<TargetUrlChangeAlertEntity> getListByDomain(int domainId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     *");
		stringBuilder.append(" from");
		stringBuilder.append("     " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     domain_id = ?");
		String sqlString = stringBuilder.toString();
		return this.findBySql(sqlString, domainId);
	}
}