package com.actonia.dao;

import com.actonia.entity.KeywordEntity;
import com.actonia.entity.KeywordTargetUrlEntity;

public class KeywordTargeturlEntityDAO extends BaseJdbcSupport<KeywordTargetUrlEntity> {

	@Override
	public String getTableName() {
		return "t_keyword_targeturl";
	}

	public Integer getAssociatedManagedKeywords(int urlVersion, long targeturlId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("select ");
		stringBuilder.append("	 count(distinct t_keyword_targeturl.keyword_id) ");
		stringBuilder.append("from ");
		stringBuilder.append("	 t_keyword_targeturl t_keyword_targeturl, ");
		stringBuilder.append("	 t_keyword t_keyword ");
		stringBuilder.append("where ");
		stringBuilder.append("    t_keyword_targeturl.url_version = ? ");
		stringBuilder.append("and t_keyword_targeturl.target_url_id = ? ");
		stringBuilder.append("and t_keyword_targeturl.keyword_id = t_keyword.id ");
		stringBuilder.append("and t_keyword.rank_check = ? ");
		String sqlString = stringBuilder.toString();
		return queryForInteger(sqlString, urlVersion, targeturlId, KeywordEntity.RANK_CHECK_ACTIVE);
	}

	public Integer getRankedAssociatedManagedKeywords(int urlVersion, long targeturlId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("select ");
		stringBuilder.append("	 count(distinct t_keyword_targeturl.keyword_id) ");
		stringBuilder.append("from ");
		stringBuilder.append("	 t_keyword_targeturl t_keyword_targeturl, ");
		stringBuilder.append("	 t_keyword t_keyword ");
		stringBuilder.append("where ");
		stringBuilder.append("    t_keyword_targeturl.url_version = ? ");
		stringBuilder.append("and t_keyword_targeturl.target_url_id = ? ");
		stringBuilder.append("and t_keyword_targeturl.keyword_id = t_keyword.id ");
		stringBuilder.append("and t_keyword.rank_check = ? ");
		stringBuilder.append("and t_keyword.rank1 >= 1 ");
		stringBuilder.append("and t_keyword.rank1 <= 100 ");
		String sqlString = stringBuilder.toString();
		return queryForInteger(sqlString, urlVersion, targeturlId, KeywordEntity.RANK_CHECK_ACTIVE);
	}

	public int getCompetitorUrlNum(int urlVersion, long targetUrlId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("select ");
		stringBuilder.append("	count(distinct t_competitor_url.id) ");
		stringBuilder.append("from ");
		stringBuilder.append("	t_keyword_targeturl t_keyword_targeturl ");
		stringBuilder.append("		left join t_keyword_competitorurl t_keyword_competitorurl ");
		stringBuilder.append("			on t_keyword_targeturl.keyword_id = t_keyword_competitorurl.keyword_id ");
		stringBuilder.append("				left join t_competitor_url t_competitor_url ");
		stringBuilder.append("					on t_keyword_competitorurl.competitorurl_id = t_competitor_url.id ");
		stringBuilder.append("where ");
		stringBuilder.append("    t_keyword_targeturl.url_version = ? ");
		stringBuilder.append("and t_keyword_targeturl.target_url_id = ? ");
		String sqlString = stringBuilder.toString();
		return queryForInt(sqlString, urlVersion, targetUrlId);
	}
}
