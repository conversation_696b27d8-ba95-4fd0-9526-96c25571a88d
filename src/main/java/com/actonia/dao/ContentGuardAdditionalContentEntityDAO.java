package com.actonia.dao;

import java.util.List;

import com.actonia.entity.AdditionalContentEntity;

public class ContentGuardAdditionalContentEntityDAO extends BaseJdbcSupport<AdditionalContentEntity> {

	@Override
	public String getTableName() {
		return "content_guard_additional_content";
	}

	public List<AdditionalContentEntity> getByDomainId(int domainId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("  *");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("  domain_id = ? ");
		String sqlString = stringBuilder.toString();
		return this.findBySql(sqlString, domainId);
	}
}