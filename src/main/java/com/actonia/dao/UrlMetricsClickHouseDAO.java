package com.actonia.dao;

import com.actonia.IConstants;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.utils.CrawlerUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.value.object.*;
import com.google.gson.Gson;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import ru.yandex.clickhouse.ClickHouseDataSource;
import ru.yandex.clickhouse.settings.ClickHouseProperties;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.*;

// Due to ClickHouse concurrency issues, use Singleton pattern, do not use Spring (ie. all methods must be 'synchronized')
public class UrlMetricsClickHouseDAO {

	//private static boolean isDebug = false;
	public static final String TABLE_NAME = "dis_url_metrics";
	//public static final String TABLE_NAME = "unit_test_target_url_html";
	private static final int TOTAL_DAO_INSTANCES = 3;
	private static final Logger log = LogManager.getLogger(UrlMetricsClickHouseDAO.class);
	private static int daoMapIndex = 0;

	// map key = DAO index (0  - 59)
	// map value = instance of TargetUrlHtmlClickHouseDAO
	private static Map<Integer, UrlMetricsClickHouseDAO> targetUrlHtmlClickHouseDAOMap;

	private final List<String> databaseHostnameList;
	private final String databasePort;
	private static final String databaseName = "polite_crawl";
	private final List<Connection> connectionList;
	private final int batchCreationSize;
	private final String databaseUser;
	private final String databasePassword;
    private final int connectionTimeoutInMilliseconds;
	private final int maximumRetryCounts;
	private final int retryWaitMilliseconds;
	private final int daoIndex;

    public static UrlMetricsClickHouseDAO getInstance() throws Exception {
		UrlMetricsClickHouseDAO targetUrlHtmlClickHouseDAO;
		String clickHouseDatabaseHostnames;
		String[] clickHouseDatabaseHostnameArray;
		String clickHouseDatabasePort;
		String clickHouseDatabaseUser;
		String clickHouseDatabasePassword;
		int clickHouseBatchCreationSize;
		int clickHouseconnectionTimeoutInMilliseconds;
		int clickHouseMaximumRetryCounts;
		int clickHouseRetryWaitMilliseconds;
		if (targetUrlHtmlClickHouseDAOMap == null) {
			clickHouseDatabaseHostnames = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_HOSTNAMES);
			clickHouseDatabaseHostnameArray = clickHouseDatabaseHostnames.split(IConstants.COMMA);
			clickHouseDatabasePort = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_PORT);
//			clickHouseDatabaseName = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_NAME);
			clickHouseDatabaseUser = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_USER);
			clickHouseDatabasePassword = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_PASSWORD);
			clickHouseBatchCreationSize = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_BATCH_CREATION_SIZE, 100);
			clickHouseconnectionTimeoutInMilliseconds = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_CONNECTION_TIMEOUT_MILLISECONDS, 88888);
			clickHouseMaximumRetryCounts = ClickHouseCrawlDatabaseConfigurations.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_MAX_RETRY_COUNTS,
					8);
			clickHouseRetryWaitMilliseconds = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_RETRY_WAIT_MILLISECONDS, 1000);
			FormatUtils.getInstance().logMemoryUsage("TargetUrlHtmlClickHouseDAO() clickHouseDatabaseHostnames=" + clickHouseDatabaseHostnames
					+ ",clickHouseDatabasePort=" + clickHouseDatabasePort + ",clickHouseDatabaseName=" + databaseName + ",clickHouseBatchCreationSize="
					+ clickHouseBatchCreationSize + ",clickHouseDatabaseUser=" + clickHouseDatabaseUser + ",clickHouseDatabasePassword=" + clickHouseDatabasePassword
					+ ",clickHouseconnectionTimeoutInMilliseconds=" + clickHouseconnectionTimeoutInMilliseconds + ",clickHouseMaximumRetryCounts="
					+ clickHouseMaximumRetryCounts + ",clickHouseRetryWaitMilliseconds=" + clickHouseRetryWaitMilliseconds + ",TABLE_NAME=" + TABLE_NAME);

			targetUrlHtmlClickHouseDAOMap = new HashMap<>();
			for (int i = 0; i < TOTAL_DAO_INSTANCES; i++) {
				targetUrlHtmlClickHouseDAO = initialize(clickHouseDatabaseHostnameArray, clickHouseDatabasePort, clickHouseDatabaseUser,
						clickHouseDatabasePassword, clickHouseBatchCreationSize, clickHouseconnectionTimeoutInMilliseconds, clickHouseMaximumRetryCounts,
						clickHouseRetryWaitMilliseconds, i);
				targetUrlHtmlClickHouseDAOMap.put(i, targetUrlHtmlClickHouseDAO);
			}
			FormatUtils.getInstance().logMemoryUsage("getInstance() total targetUrlHtmlClickHouseDAOs=" + targetUrlHtmlClickHouseDAOMap.size());
		}
		int index = getDaoMapIndex();
		targetUrlHtmlClickHouseDAO = targetUrlHtmlClickHouseDAOMap.get(index);
		return targetUrlHtmlClickHouseDAO;
	}

	private static synchronized int getDaoMapIndex() {
		int index;
		if (daoMapIndex >= TOTAL_DAO_INSTANCES) {
			daoMapIndex = 0;
			index = 0;
		} else {
			index = daoMapIndex++;
		}
		return index;
	}

	// initialize TargetUrlHtmlClickHouseDAO based on runtime clickhouse configurations
	private static UrlMetricsClickHouseDAO initialize(String[] clickHouseDatabaseHostnameArray, String clickHouseDatabasePort,
                                                      String clickHouseDatabaseUser, String clickHouseDatabasePassword, int clickHouseBatchCreationSize, int clickHouseconnectionTimeoutInMilliseconds,
                                                      int clickHouseMaximumRetryCounts, int clickHouseRetryWaitMilliseconds, int daoIndex) throws Exception {
		UrlMetricsClickHouseDAO targetUrlHtmlClickHouseDAO = new UrlMetricsClickHouseDAO(clickHouseDatabaseHostnameArray, clickHouseDatabasePort,
                clickHouseBatchCreationSize, clickHouseDatabaseUser, clickHouseDatabasePassword, clickHouseconnectionTimeoutInMilliseconds,
				clickHouseMaximumRetryCounts, clickHouseRetryWaitMilliseconds, daoIndex);
		//FormatUtils.getInstance().logMemoryUsage("initialize() targetUrlHtmlClickHouseDAO=" + targetUrlHtmlClickHouseDAO.toString());
		return targetUrlHtmlClickHouseDAO;
	}

	private UrlMetricsClickHouseDAO(String[] databaseHostnameArrayInput, String databasePortInput, int batchCreationSizeInput,
                                    String databaseUserInput, String databasePasswordInput, int connectionTimeoutInMillisecondsInput, int maximumRetryCountsInput,
                                    int retryWaitMillisecondsInput, int index) throws Exception {

		ClickHouseDataSource clickHouseDataSource;
		Connection connection;

		databaseHostnameList = Arrays.asList(databaseHostnameArrayInput);
		databasePort = databasePortInput;
		batchCreationSize = batchCreationSizeInput;
		databaseUser = databaseUserInput;
		databasePassword = databasePasswordInput;
		connectionTimeoutInMilliseconds = connectionTimeoutInMillisecondsInput;
		maximumRetryCounts = maximumRetryCountsInput;
		retryWaitMilliseconds = retryWaitMillisecondsInput;
		daoIndex = index;

		ClickHouseProperties clickHouseProperties = new ClickHouseProperties();
		clickHouseProperties.setDecompress(true);
		clickHouseProperties.setConnectionTimeout(connectionTimeoutInMilliseconds);
		clickHouseProperties.setSocketTimeout(connectionTimeoutInMilliseconds);

		connectionList = new ArrayList<>();

		String connectionUrl;

        List<String> connectionUrlList = new ArrayList<>();

		for (String databaseHostname : databaseHostnameList) {
			if (StringUtils.isNotBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
						+ "?user=" + databaseUser + "&password=" + databasePassword;
			} else if (StringUtils.isBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
						+ "?password=" + databasePassword;
			} else {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH
						+ databaseName;
			}
			connectionUrlList.add(connectionUrl);
			//FormatUtils.getInstance().logMemoryUsage("TargetUrlHtmlClickHouseDAO() connectionUrl=" + connectionUrl);
			clickHouseDataSource = new ClickHouseDataSource(connectionUrl, clickHouseProperties);
			connection = clickHouseDataSource.getConnection();
			connectionList.add(connection);
		}
	}

	@Override
	public String toString() {
		return "TargetUrlHtmlClickHouseDAO [databaseHostnameList=" + databaseHostnameList.toString() + ", databasePort=" + databasePort + ", databaseName="
				+ databaseName + ", batchCreationSize=" + batchCreationSize + ", databaseUser=" + databaseUser + ", databasePassword=" + databasePassword
				+ ", connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ", maximumRetryCounts=" + maximumRetryCounts + ", retryWaitMilliseconds="
				+ retryWaitMilliseconds + ", daoIndex=" + daoIndex + "]";
	}

	private synchronized String getTableName(String tableName) {
		if (StringUtils.isNotBlank(tableName)) {
			return tableName;
		} else {
			return TABLE_NAME;
		}
	}

	public synchronized int getBatchCreationSize() {
		return batchCreationSize;
	}


    // retrieve all latest records (with latest crawl timestamp) of a domain with target_url_to_crawl
	public synchronized List<HtmlClickHouseEntity> getLatestFromHistorical(int domainId, List<String> databaseFields) throws Exception {
		final StringBuilder stringBuilder = new StringBuilder("select ");
		if (databaseFields != null && !databaseFields.isEmpty()) {
			stringBuilder.append(String.join(IConstants.COMMA, databaseFields));
		} else {
			stringBuilder.append(" * ");
		}
        stringBuilder.append(" from dis_target_url_html where domain_id = ? and ( url_hash, url_murmur_hash, track_date ) global in ( select url_hash, url_murmur_hash, max(track_date) from dis_target_url_html where domain_id = ? group by url_hash, url_murmur_hash )");
		final Connection connection = connectionList.get(0);
		int retryCount = 0;
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = new ArrayList<>();
		while (retryCount < maximumRetryCounts) {
			int index = 1;
			try (PreparedStatement preparedStatement = connection.prepareStatement(stringBuilder.toString())) {
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setInt(index++, domainId);
				try (ResultSet resultSet = preparedStatement.executeQuery()) {
					HtmlClickHouseEntity htmlClickHouseEntity;
					while (resultSet.next()) {
						htmlClickHouseEntity = CrawlerUtils.getInstance().getHtmlClickHouseEntityFromResultSet(resultSet, databaseFields);
						htmlClickHouseEntityList.add(htmlClickHouseEntity);
					}
					return htmlClickHouseEntityList;
				}
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					log.error(e.getMessage(), e);
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getLatestFromHistorical() e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						log.error("getLatestFromHistorical() Thread.sleep() error", e1);
					}
				}
			}
		}
		return htmlClickHouseEntityList;
	}


	// retrieve all latest records (with latest crawl timestamp) of a domain
	public synchronized List<HtmlClickHouseEntity> getLatestFromHistorical(int domainId, List<String> databaseFields, String tableName) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		//FormatUtils.getInstance().logMemoryUsage("getLatestFromHistorical() begins. domainId=" + domainId + ",tableName=" + tableName);
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = new ArrayList<HtmlClickHouseEntity>();
		HtmlClickHouseEntity htmlClickHouseEntity = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		boolean isFirstField = true;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		if (databaseFields != null && databaseFields.size() > 0) {
			for (String databaseField : databaseFields) {
				if (isFirstField == true) {
					isFirstField = false;
					stringBuilder.append(IConstants.ONE_SPACE);
				} else {
					stringBuilder.append(IConstants.COMMA);
				}
				stringBuilder.append(databaseField);
			}
		} else {
			stringBuilder.append(" *");
		}
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where domain_id = ?");
		stringBuilder.append(" and (");
		stringBuilder.append("       url_hash,");
		stringBuilder.append("       url_murmur_hash,");
		stringBuilder.append("       crawl_timestamp");
		stringBuilder.append(" ) global in");
		stringBuilder.append(" (");
		stringBuilder.append(" select");
		stringBuilder.append("   url_hash,");
		stringBuilder.append("   url_murmur_hash,");
		stringBuilder.append("   max(crawl_timestamp)");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("   domain_id = ?");
		stringBuilder.append(" group by");
		stringBuilder.append("   url_hash,");
		stringBuilder.append("   url_murmur_hash");
		stringBuilder.append(" )");
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getLatestFromHistorical() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		retryCount = 0;
		int index = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				htmlClickHouseEntityList = new ArrayList<HtmlClickHouseEntity>();
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setInt(index++, domainId);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					htmlClickHouseEntity = CrawlerUtils.getInstance().getHtmlClickHouseEntityFromResultSet(resultSet, databaseFields);
					htmlClickHouseEntityList.add(htmlClickHouseEntity);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance()
							.logMemoryUsage("getLatestFromHistorical() domainId=" + domainId + ",e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		//FormatUtils.getInstance().logMemoryUsage("getLatestFromHistorical() ends. domainId=" + domainId + ",htmlClickHouseEntityList.size()="
		//		+ htmlClickHouseEntityList.size() + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		return htmlClickHouseEntityList;
	}

    public synchronized List<PoliteCrawlSummaryValueObject> getDailyUpdateSummaryValueObjectList(int domainId, Date targetUrlHtmlDailyDate, String tableName)
			throws Exception {
		List<PoliteCrawlSummaryValueObject> politeCrawlSummaryValueObjectList = null;
		PoliteCrawlSummaryValueObject politeCrawlSummaryValueObject = null;
		ResultSet resultSet = null;
		Connection connection = null;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("   response_code,");
		stringBuilder.append("   count(*) as total_urls_with_response_code");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("     daily_data_creation_date = ?");
		stringBuilder.append(" and domain_id = ?");
		stringBuilder.append(" group by");
		stringBuilder.append("   response_code");
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getByTrackDate() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		int retryCount = 0;
		int index = 0;
		String responseCode = null;
		int httpStatusCode = 0;

		while (retryCount < maximumRetryCounts) {
			try {
				politeCrawlSummaryValueObjectList = new ArrayList<PoliteCrawlSummaryValueObject>();
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setDate(index++, new java.sql.Date(targetUrlHtmlDailyDate.getTime()));
				preparedStatement.setInt(index++, domainId);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					politeCrawlSummaryValueObject = new PoliteCrawlSummaryValueObject();

					// domain_id
					politeCrawlSummaryValueObject.setDomainId(domainId);

					// response_code
					responseCode = resultSet.getString(IConstants.RESPONSE_CODE);
					httpStatusCode = NumberUtils.toInt(responseCode);
					politeCrawlSummaryValueObject.setHttpStatusCode(httpStatusCode);

					// total_urls_with_response_code
					politeCrawlSummaryValueObject.setTotalUrlsWithResponseCode(resultSet.getInt("total_urls_with_response_code"));

					politeCrawlSummaryValueObjectList.add(politeCrawlSummaryValueObject);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage(
							"getDailyUpdateSummaryValueObjectList() domainId=" + domainId + ",e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}

		//if (isDebug == true) {
		//	for (PoliteCrawlSummaryValueObject testPoliteCrawlSummaryValueObject : politeCrawlSummaryValueObjectList) {
		//		FormatUtils.getInstance().logMemoryUsage("getDailyUpdateSummaryValueObjectList() testPoliteCrawlSummaryValueObject=" + testPoliteCrawlSummaryValueObject.toString());
		//	}
		//}

		return politeCrawlSummaryValueObjectList;
	}

	public synchronized List<HtmlClickHouseEntity> getDailyData(Date dailyDataCreationDate, int domainId, String urlString, List<String> databaseFields,
			String tableName) throws Exception {
		//long startTimestamp = System.currentTimeMillis();
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = new ArrayList<HtmlClickHouseEntity>();
		HtmlClickHouseEntity htmlClickHouseEntity = null;
		ResultSet resultSet = null;
		Connection connection = null;
		String trimmedUrlString = StringUtils.trimToEmpty(urlString);
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		boolean isFirstField = true;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		if (databaseFields != null && databaseFields.size() > 0) {
			for (String databaseField : databaseFields) {
				if (isFirstField == true) {
					isFirstField = false;
					stringBuilder.append(IConstants.ONE_SPACE);
				} else {
					stringBuilder.append(IConstants.COMMA);
				}
				stringBuilder.append(databaseField);
			}
		} else {
			stringBuilder.append(" *");
		}
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("     daily_data_creation_date = ?");
		stringBuilder.append(" and domain_id = ?");
		if (StringUtils.isNotBlank(urlString)) {
			stringBuilder.append(" and url_hash = URLHash(?)");
			stringBuilder.append(" and url_murmur_hash = murmurHash3_64(?)");
		}
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getDailyData() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		retryCount = 0;
		int index = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setDate(index++, new java.sql.Date(dailyDataCreationDate.getTime()));
				preparedStatement.setInt(index++, domainId);
				if (StringUtils.isNotBlank(urlString)) {
					preparedStatement.setString(index++, trimmedUrlString);
					preparedStatement.setString(index++, trimmedUrlString);
				}
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					htmlClickHouseEntity = CrawlerUtils.getInstance().getHtmlClickHouseEntityFromResultSet(resultSet, databaseFields);
					htmlClickHouseEntityList.add(htmlClickHouseEntity);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage(
							"getDailyData() domainId=" + domainId + ", urlString=" + urlString + ",e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		return htmlClickHouseEntityList;
	}

	// retrieve the page links of one record with the exact crawl timestamp
	public synchronized HtmlClickHouseEntity getPageLinkByCrawlTimestamp(Date crawlTimestamp, int domainId, String urlString, String tableName) throws Exception {
		//long startTimestamp = System.currentTimeMillis();
		//FormatUtils.getInstance()
		//		.logMemoryUsage("getPageLinkByCrawlTimestamp() begins. crawlTimestamp=" + crawlTimestamp + ",domainId=" + domainId + ",urlString=" + urlString + ",tableName=" + tableName);
		String trimmedUrlString = StringUtils.trimToEmpty(urlString);
		Date trackDate = DateUtils.truncate(crawlTimestamp, Calendar.DAY_OF_MONTH);
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		retryCount = 0;
		String sqlString = null;
		StringBuilder stringBuilder = null;
		int index = 0;
		HtmlClickHouseEntity htmlClickHouseEntity = null;
		String testString = null;
		PageLink[] pageLinkArray = null;
		Gson gson = new Gson();
		CrawlerResponse crawlerResponse = null;
		stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("  page_link");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("     track_date = ?");
		stringBuilder.append(" and domain_id = ?");
		stringBuilder.append(" and url_hash = URLHash(?)");
		stringBuilder.append(" and url_murmur_hash = murmurHash3_64(?)");
		stringBuilder.append(" and crawl_timestamp = ?");
		sqlString = stringBuilder.toString();
		//if (isDebug == true) {
		//	FormatUtils.getInstance().logMemoryUsage("getPageLinkByCrawlTimestamp() sqlString=" + sqlString);					
		//}
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setDate(index++, new java.sql.Date(trackDate.getTime()));
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setString(index++, trimmedUrlString);
				preparedStatement.setString(index++, trimmedUrlString);
				preparedStatement.setTimestamp(index++, new java.sql.Timestamp(crawlTimestamp.getTime()));
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {

					htmlClickHouseEntity = new HtmlClickHouseEntity();
					crawlerResponse = new CrawlerResponse();

					// page_link
					testString = resultSet.getString(IConstants.PAGE_LINK);
					if (StringUtils.isNotBlank(testString)) {
						pageLinkArray = gson.fromJson(testString, PageLink[].class);
						if (pageLinkArray != null && pageLinkArray.length > 0) {
							crawlerResponse.setPage_link(pageLinkArray);
							htmlClickHouseEntity.setCrawlerResponse(crawlerResponse);
						}
					}
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				e.printStackTrace();
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getPageLinkByCrawlTimestamp() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			} finally {
			}
		}
		//FormatUtils.getInstance().logMemoryUsage("getPageLinkByCrawlTimestamp() ends. crawlTimestamp=" + crawlTimestamp + ",domainId=" + domainId + ",urlString=" + urlString + ",tableName="
		//		+ tableName + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		return htmlClickHouseEntity;
	}

	public synchronized Date getEarliestTrackDate(String tableName) throws Exception {
		Date trackDate = null;
		//long startTimestamp = System.currentTimeMillis();
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("  min(track_date) as min_track_date");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getEarliestTrackDate() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				preparedStatement = connection.prepareStatement(sqlString);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {

					// track_date
					trackDate = resultSet.getDate("min_track_date");
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getEarliestTrackDate() e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}

		return trackDate;
	}

	// retrieve a unique list of records (with the latest crawl timestamp) that were changed on track date
	public synchronized List<HtmlClickHouseEntity> getChangeList(int domainId, Date trackDate, List<String> databaseFields, String tableName) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("getChangeList() begins. domainId=" + domainId + ",trackDate=" + trackDate + ",tableName=" + tableName);
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = new ArrayList<HtmlClickHouseEntity>();
		HtmlClickHouseEntity htmlClickHouseEntity = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;

		PreparedStatement preparedStatement = null;
		boolean isFirstField = true;
		String prefixString = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		for (String databaseField : databaseFields) {
			if (isFirstField == true) {
				isFirstField = false;
				stringBuilder.append(IConstants.ONE_SPACE);
			} else {
				stringBuilder.append(IConstants.COMMA);
			}
			stringBuilder.append(databaseField);
		}
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("     track_date = ?");
		stringBuilder.append(" and domain_id = ?");
		stringBuilder.append(" and (");
		isFirstField = true;
		for (String databaseField : databaseFields) {
			if (StringUtils.equalsIgnoreCase(databaseField, IConstants.RESPONSE_CODE_CHG_IND) || StringUtils.equalsIgnoreCase(databaseField, IConstants.TITLE_CHG_IND)
					|| StringUtils.equalsIgnoreCase(databaseField, IConstants.DESCRIPTION_CHG_IND) || StringUtils.equalsIgnoreCase(databaseField, IConstants.H1_CHG_IND)
					|| StringUtils.equalsIgnoreCase(databaseField, IConstants.H2_CHG_IND)
					|| StringUtils.equalsIgnoreCase(databaseField, IConstants.ROBOTS_CONTENTS_CHG_IND)
					|| StringUtils.equalsIgnoreCase(databaseField, IConstants.CANONICAL_CHG_IND)
					|| StringUtils.equalsIgnoreCase(databaseField, IConstants.CUSTOM_DATA_CHG_IND)) {
				if (isFirstField == true) {
					isFirstField = false;
					prefixString = IConstants.ONE_SPACE;
				} else {
					prefixString = " or ";
				}
				stringBuilder.append(prefixString).append(databaseField).append(" = ?");
			}
		}
		stringBuilder.append(" )");
		stringBuilder.append(" and (url_hash, url_murmur_hash, crawl_timestamp) global in");
		stringBuilder.append(" (");
		stringBuilder.append(" select");
		stringBuilder.append("     url_hash,");
		stringBuilder.append("     url_murmur_hash,");
		stringBuilder.append("     max(crawl_timestamp)");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("     track_date = ?");
		stringBuilder.append(" and domain_id = ?");
		stringBuilder.append(" group by");
		stringBuilder.append("     url_hash,");
		stringBuilder.append("     url_murmur_hash");
		stringBuilder.append(" )");
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getChangeList() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		retryCount = 0;
		int index = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setDate(index++, new java.sql.Date(trackDate.getTime()));
				preparedStatement.setInt(index++, domainId);
				for (String databaseField : databaseFields) {
					if (StringUtils.equalsIgnoreCase(databaseField, IConstants.RESPONSE_CODE_CHG_IND)
							|| StringUtils.equalsIgnoreCase(databaseField, IConstants.TITLE_CHG_IND)
							|| StringUtils.equalsIgnoreCase(databaseField, IConstants.DESCRIPTION_CHG_IND)
							|| StringUtils.equalsIgnoreCase(databaseField, IConstants.H1_CHG_IND) || StringUtils.equalsIgnoreCase(databaseField, IConstants.H2_CHG_IND)
							|| StringUtils.equalsIgnoreCase(databaseField, IConstants.ROBOTS_CONTENTS_CHG_IND)
							|| StringUtils.equalsIgnoreCase(databaseField, IConstants.CANONICAL_CHG_IND)
							|| StringUtils.equalsIgnoreCase(databaseField, IConstants.CUSTOM_DATA_CHG_IND)) {
						preparedStatement.setInt(index++, 1);
					}
				}
				preparedStatement.setDate(index++, new java.sql.Date(trackDate.getTime()));
				preparedStatement.setInt(index++, domainId);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					htmlClickHouseEntity = CrawlerUtils.getInstance().getHtmlClickHouseEntityFromResultSet(resultSet, databaseFields);
					htmlClickHouseEntityList.add(htmlClickHouseEntity);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage(
							"getChangeList() domainId=" + domainId + ",trackDate=" + trackDate + ",e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("getChangeList() ends. domainId=" + domainId + ",trackDate=" + trackDate + ",tableName=" + tableName
				+ ",htmlClickHouseEntityList.size()=" + htmlClickHouseEntityList.size() + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return htmlClickHouseEntityList;
	}

	public synchronized List<HtmlClickHouseEntity> getExtract(int domainId, List<String> databaseFields, String tableName) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("getExtract() begins. domainId=" + domainId + ",tableName=" + tableName);
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = new ArrayList<HtmlClickHouseEntity>();
		HtmlClickHouseEntity htmlClickHouseEntity = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		boolean isFirstField = true;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		if (databaseFields != null && databaseFields.size() > 0) {
			for (String databaseField : databaseFields) {
				if (isFirstField == true) {
					isFirstField = false;
					stringBuilder.append(IConstants.ONE_SPACE);
				} else {
					stringBuilder.append(IConstants.COMMA);
				}
				stringBuilder.append(databaseField);
			}
		} else {
			stringBuilder.append(" *");
		}
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("     domain_id = ?");
		stringBuilder.append(" and response_code = '200'");
		stringBuilder.append(" and custom_data != '[]'");
		stringBuilder.append(" and (");
		stringBuilder.append("       url_hash,");
		stringBuilder.append("       url_murmur_hash,");
		stringBuilder.append("       crawl_timestamp");
		stringBuilder.append(" ) global in");
		stringBuilder.append(" (");
		stringBuilder.append(" select");
		stringBuilder.append("   url_hash,");
		stringBuilder.append("   url_murmur_hash,");
		stringBuilder.append("   max(crawl_timestamp)");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("     domain_id = ?");
		stringBuilder.append(" and response_code = '200'");
		stringBuilder.append(" and custom_data != '[]'");
		stringBuilder.append(" group by");
		stringBuilder.append("   url_hash,");
		stringBuilder.append("   url_murmur_hash");
		stringBuilder.append(" )");
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getExtract() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		retryCount = 0;
		int index = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				htmlClickHouseEntityList = new ArrayList<HtmlClickHouseEntity>();
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setInt(index++, domainId);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					htmlClickHouseEntity = CrawlerUtils.getInstance().getHtmlClickHouseEntityFromResultSet(resultSet, databaseFields);
					htmlClickHouseEntityList.add(htmlClickHouseEntity);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getExtract() domainId=" + domainId + ",e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("getExtract() ends. domainId=" + domainId + ",htmlClickHouseEntityList.size()=" + htmlClickHouseEntityList.size()
				+ ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		return htmlClickHouseEntityList;
	}

	public synchronized List<HtmlClickHouseEntity> getLatestCustomData(int domainId, List<String> databaseFields, String tableName) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("getLatestCustomData() begins. domainId=" + domainId + ",tableName=" + tableName);
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = new ArrayList<HtmlClickHouseEntity>();
		HtmlClickHouseEntity htmlClickHouseEntity = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		boolean isFirstField = true;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		if (databaseFields != null && databaseFields.size() > 0) {
			for (String databaseField : databaseFields) {
				if (isFirstField == true) {
					isFirstField = false;
					stringBuilder.append(IConstants.ONE_SPACE);
				} else {
					stringBuilder.append(IConstants.COMMA);
				}
				stringBuilder.append(databaseField);
			}
		} else {
			stringBuilder.append(" *");
		}
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("     domain_id = ?");
		stringBuilder.append(" and response_code = '200'");
		stringBuilder.append(" and custom_data != '[]'");
		stringBuilder.append(" and custom_data > ''");
		stringBuilder.append(" and (");
		stringBuilder.append("       url_hash,");
		stringBuilder.append("       url_murmur_hash,");
		stringBuilder.append("       crawl_timestamp");
		stringBuilder.append(" ) global in");
		stringBuilder.append(" (");
		stringBuilder.append(" select");
		stringBuilder.append("   url_hash,");
		stringBuilder.append("   url_murmur_hash,");
		stringBuilder.append("   max(crawl_timestamp)");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("     domain_id = ?");
		stringBuilder.append(" and response_code = '200'");
		stringBuilder.append(" and custom_data != '[]'");
		stringBuilder.append(" and custom_data > ''");
		stringBuilder.append(" group by");
		stringBuilder.append("   url_hash,");
		stringBuilder.append("   url_murmur_hash");
		stringBuilder.append(" )");
		stringBuilder.append(" and (");
		stringBuilder.append("       url_hash,");
		stringBuilder.append("       url_murmur_hash");
		stringBuilder.append(" ) global in");
		stringBuilder.append("    (");
		stringBuilder.append("    select");
		stringBuilder.append("      url_hash,");
		stringBuilder.append("      url_murmur_hash");
		stringBuilder.append("    from");
		stringBuilder.append("      dis_target_url");
		stringBuilder.append("    where");
		stringBuilder.append("      domain_id = ?");
		stringBuilder.append("   )");
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getLatestCustomData() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		retryCount = 0;
		int index = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				htmlClickHouseEntityList = new ArrayList<HtmlClickHouseEntity>();
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setInt(index++, domainId);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					htmlClickHouseEntity = CrawlerUtils.getInstance().getHtmlClickHouseEntityFromResultSet(resultSet, databaseFields);
					htmlClickHouseEntityList.add(htmlClickHouseEntity);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance()
							.logMemoryUsage("getLatestCustomData() domainId=" + domainId + ",e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("getLatestCustomData() ends. domainId=" + domainId + ",htmlClickHouseEntityList.size()="
				+ htmlClickHouseEntityList.size() + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		return htmlClickHouseEntityList;
	}

	public synchronized List<HtmlClickHouseEntity> getLatestCustomDataByPartition(int domainId, List<String> databaseFields, String tableName, int partitionSize, int partitionIndex) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("getLatestCustomDataByPage() begins. domainId=" + domainId + ",tableName=" + tableName + ",partitionSize=" + partitionSize + ",partitionIndex=" + partitionIndex);
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = new ArrayList<HtmlClickHouseEntity>();
		HtmlClickHouseEntity htmlClickHouseEntity = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		boolean isFirstField = true;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		if (databaseFields != null && databaseFields.size() > 0) {
			for (String databaseField : databaseFields) {
				if (isFirstField == true) {
					isFirstField = false;
					stringBuilder.append(IConstants.ONE_SPACE);
				} else {
					stringBuilder.append(IConstants.COMMA);
				}
				stringBuilder.append(databaseField);
			}
		} else {
			stringBuilder.append(" *");
		}
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("     domain_id = ? and url_hash % ? = ?");
		stringBuilder.append(" and response_code = '200'");
		stringBuilder.append(" and custom_data != '[]'");
		stringBuilder.append(" and custom_data > ''");
		stringBuilder.append(" and (");
		stringBuilder.append("       url_hash,");
		stringBuilder.append("       url_murmur_hash,");
		stringBuilder.append("       crawl_timestamp");
		stringBuilder.append(" ) global in");
		stringBuilder.append(" (");
		stringBuilder.append(" select");
		stringBuilder.append("   url_hash,");
		stringBuilder.append("   url_murmur_hash,");
		stringBuilder.append("   max(crawl_timestamp)");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("     domain_id = ? and url_hash % ? = ?");
		stringBuilder.append(" and response_code = '200'");
		stringBuilder.append(" and custom_data != '[]'");
		stringBuilder.append(" and custom_data > ''");
		stringBuilder.append(" group by");
		stringBuilder.append("   url_hash,");
		stringBuilder.append("   url_murmur_hash");
		stringBuilder.append(" )");
		stringBuilder.append(" and (");
		stringBuilder.append("       url_hash,");
		stringBuilder.append("       url_murmur_hash");
		stringBuilder.append(" ) global in");
		stringBuilder.append("    (");
		stringBuilder.append("    select");
		stringBuilder.append("      url_hash,");
		stringBuilder.append("      url_murmur_hash");
		stringBuilder.append("    from");
		stringBuilder.append("      dis_target_url");
		stringBuilder.append("    where");
		stringBuilder.append("      domain_id = ? and url_hash % ? = ?");
		stringBuilder.append("   )");
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getLatestCustomDataByPage() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		retryCount = 0;
		int index = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				htmlClickHouseEntityList = new ArrayList<HtmlClickHouseEntity>();
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setInt(index++, partitionSize);
				preparedStatement.setInt(index++, partitionIndex);
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setInt(index++, partitionSize);
				preparedStatement.setInt(index++, partitionIndex);
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setInt(index++, partitionSize);
				preparedStatement.setInt(index++, partitionIndex);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					htmlClickHouseEntity = CrawlerUtils.getInstance().getHtmlClickHouseEntityFromResultSet(resultSet, databaseFields);
					htmlClickHouseEntityList.add(htmlClickHouseEntity);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance()
							.logMemoryUsage("getLatestCustomDataByPage() domainId=" + domainId + ",e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("getLatestCustomDataByPage() ends. domainId=" + domainId + ",htmlClickHouseEntityList.size()="
				+ htmlClickHouseEntityList.size() + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		return htmlClickHouseEntityList;
	}

	public synchronized List<HtmlClickHouseEntity> getChangeTrackingSummaryList(int domainId, String startTrackDate, String endTrackDate, String startCrawlTimestamp,
			String endCrawlTimestamp, List<String> changeTrackingIndicatorList) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		//FormatUtils.getInstance().logMemoryUsage("getChangeTrackingSummaryList() begins. domainId=" + domainId + ",startCrawlTimestamp=" + startCrawlTimestamp
		//		+ ",endCrawlTimestamp=" + endCrawlTimestamp + ",changeTrackingIndicatorList.size()=" + changeTrackingIndicatorList.size());
		//long startTimestamp = System.currentTimeMillis();
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = new ArrayList<HtmlClickHouseEntity>();
		HtmlClickHouseEntity htmlClickHouseEntity = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		int index = 0;
		PreparedStatement preparedStatement = null;
		String testChangeTrackingIndicator = null;

		List<String> databaseFields = new ArrayList<String>(changeTrackingIndicatorList);
		databaseFields.add(IConstants.CRAWL_TIMESTAMP);
		databaseFields.add(IConstants.URL);
		databaseFields.add(IConstants.RESPONSE_CODE);
		databaseFields.add(IConstants.PREVIOUS_CRAWL_TIMESTAMP);

		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select crawl_timestamp,url,response_code,url,previous_crawl_timestamp");
		for (String changeTrackingIndicator : changeTrackingIndicatorList) {
			stringBuilder.append(IConstants.COMMA + changeTrackingIndicator);
		}
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(null));
		stringBuilder.append(" where");
		stringBuilder.append("     track_date >= '" + startTrackDate + IConstants.SINGLE_QUOTE);
		stringBuilder.append(" and track_date <= '" + endTrackDate + IConstants.SINGLE_QUOTE);
		stringBuilder.append(" and domain_id = ?");
		stringBuilder.append(" and crawl_timestamp > '" + startCrawlTimestamp + IConstants.SINGLE_QUOTE);
		stringBuilder.append(" and crawl_timestamp <= '" + endCrawlTimestamp + IConstants.SINGLE_QUOTE);
		stringBuilder.append(" and previous_crawl_timestamp != '1970-01-01 00:00:00'");
		for (int i = 0; i < changeTrackingIndicatorList.size(); i++) {
			testChangeTrackingIndicator = changeTrackingIndicatorList.get(i);
			if (i == 0) {
				if (StringUtils.equalsIgnoreCase(testChangeTrackingIndicator, IConstants.PAGE_ANALYSIS_RESULTS_CHG_IND_JSON)) {
					stringBuilder.append(" and (" + testChangeTrackingIndicator + " > ''");
				} else {
					stringBuilder.append(" and (" + testChangeTrackingIndicator + " = 1");
				}
			} else {
				if (StringUtils.equalsIgnoreCase(testChangeTrackingIndicator, IConstants.PAGE_ANALYSIS_RESULTS_CHG_IND_JSON)) {
					stringBuilder.append(" or " + testChangeTrackingIndicator + " > ''");
				} else {
					stringBuilder.append(" or " + testChangeTrackingIndicator + " = 1");
				}
			}
		}
		stringBuilder.append(" )");
		String sqlString = stringBuilder.toString();
		//if (isDebug == true) {
		//	System.out.println("getChangeTrackingSummaryList() sqlString=" + sqlString);
		//}
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setInt(index++, domainId);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					htmlClickHouseEntity = CrawlerUtils.getInstance().getHtmlClickHouseEntityFromResultSet(resultSet, databaseFields);
					htmlClickHouseEntityList.add(htmlClickHouseEntity);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance()
							.logMemoryUsage("getChangeTrackingSummaryList() domainId=" + domainId + ",e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		//if (isDebug == true) {
		//	FormatUtils.getInstance()
		//	.logMemoryUsage("getChangeTrackingSummaryList() ends. domainId=" + domainId + ",startTrackDate=" + startTrackDate + ",startCrawlTimestamp="
		//			+ startCrawlTimestamp + ",endTrackDate=" + endTrackDate + ",endCrawlTimestamp=" + endCrawlTimestamp + ",htmlClickHouseEntityList.size()"
		//			+ htmlClickHouseEntityList.size() + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));			
		//}
		return htmlClickHouseEntityList;
	}

	public synchronized List<HtmlClickHouseEntity> getChangeTrackingFields(int domainId, String urlString, String previousDate, String currentDate, Date crawlTimestamp, Date previousCrawlTimestamp,
	                                                                       List<String> changeTrackingFields) throws Exception {
		//FormatUtils.getInstance().logMemoryUsage("getChangeTrackingFields() begins. domainId=" + domainId + ",urlString=" + urlString + ",crawlTimestamp="
		//		+ crawlTimestamp + ",previousCrawlTimestamp=" + previousCrawlTimestamp + ",changeTrackingFields.size()=" + changeTrackingFields.size());
		long startTimestamp = System.currentTimeMillis();
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = new ArrayList<HtmlClickHouseEntity>();
		HtmlClickHouseEntity htmlClickHouseEntity = null;
		String trimmedUrlString = StringUtils.trimToEmpty(urlString);
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		int index = 0;
		PreparedStatement preparedStatement = null;
		List<String> databaseFields = new ArrayList<String>(changeTrackingFields);
		databaseFields.add(IConstants.CRAWL_TIMESTAMP);
		databaseFields.add(IConstants.RESPONSE_CODE);

		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select crawl_timestamp, response_code");
		for (String changeTrackingField : changeTrackingFields) {
			stringBuilder.append(IConstants.COMMA + changeTrackingField);
		}
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(null));
		stringBuilder.append(" where");
		stringBuilder.append("     domain_id = ?");
		stringBuilder.append(" and track_date in(?, ?)");
		stringBuilder.append(" and url_hash = URLHash(?)");
		stringBuilder.append(" and url_murmur_hash = murmurHash3_64(?)");
		stringBuilder.append(" and (crawl_timestamp = ? or crawl_timestamp = ?)");
		String sqlString = stringBuilder.toString();
		//if (isDebug == true) {
		//	FormatUtils.getInstance().logMemoryUsage("getChangeTrackingFields() sqlString=" + sqlString);			
		//}
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setString(index++, previousDate);
				preparedStatement.setString(index++, currentDate);
				preparedStatement.setString(index++, trimmedUrlString);
				preparedStatement.setString(index++, trimmedUrlString);
				preparedStatement.setTimestamp(index++, new java.sql.Timestamp(crawlTimestamp.getTime()));
				preparedStatement.setTimestamp(index++, new java.sql.Timestamp(previousCrawlTimestamp.getTime()));
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					htmlClickHouseEntity = CrawlerUtils.getInstance().getHtmlClickHouseEntityFromResultSet(resultSet, databaseFields);
					htmlClickHouseEntityList.add(htmlClickHouseEntity);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getChangeTrackingFields() e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					if (e.getMessage() == null) {
						targetUrlHtmlClickHouseDAOMap = null;
						getInstance();
					} else {
						try {
							Thread.sleep(retryWaitMilliseconds);
						} catch (InterruptedException e1) {
							e1.printStackTrace();
						}
					}
				}
			}
		}
		//FormatUtils.getInstance()
		//		.logMemoryUsage("getChangeTrackingFields() ends. domainId=" + domainId + ",urlString=" + urlString + ",crawlTimestamp=" + crawlTimestamp
		//				+ ",previousCrawlTimestamp=" + previousCrawlTimestamp + ",htmlClickHouseEntityList.size()=" + htmlClickHouseEntityList.size() + ",elapsed(ms.)="
		//				+ (System.currentTimeMillis() - startTimestamp));
		return htmlClickHouseEntityList;
	}

}