package com.actonia.dao;

import java.util.List;

import com.actonia.entity.GroupTagEntity;
import com.actonia.utils.FormatUtils;

public class GroupTagEntityDAO extends BaseJdbcSupport<GroupTagEntity> {

	@Override
	public String getTableName() {
		return "t_group_tag";
	}

	public GroupTagEntity getGroupTagEntity(int domainId, int groupTagId, int tagType) {
		//FormatUtils.getInstance().logMemoryUsage("getGroupTagEntity() domainId=" + domainId + ",groupTagId=" + groupTagId + ",tagType=" + tagType);
		String sqlString = "select * from t_group_tag where domain_id = ? and id = ? and tag_type = ?";
		return findObject(sqlString, domainId, groupTagId, tagType);
	}

	public GroupTagEntity getGroupTagEntity(int domainId, String tagName, int tagType) {
		//FormatUtils.getInstance().logMemoryUsage("getGroupTagEntity() domainId=" + domainId + ",tagName=" + tagName + ",tagType=" + tagType);
		String sqlString = "select * from t_group_tag where domain_id = ? and tag_name = ? and tag_type = ?";
		return findObject(sqlString, domainId, tagName, tagType);
	}

	public List<GroupTagEntity> getList(int domainId, int tagType, Integer[] groupTagIds) {
		String groupTagIdsString = FormatUtils.getInstance().convertArrayToString(groupTagIds);
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     id,");
		stringBuilder.append("     tag_name");
		stringBuilder.append(" from");
		stringBuilder.append("     t_group_tag");
		stringBuilder.append(" where");
		stringBuilder.append("     domain_id = ?");
		stringBuilder.append(" and tag_type = ?");
		stringBuilder.append(" and id in (").append(groupTagIdsString).append(" )");
		String sqlString = stringBuilder.toString();
		return findBySql(sqlString, domainId, tagType);
	}
}
