package com.actonia.dao;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.actonia.entity.OwnDomainEntity;
import com.actonia.entity.PageClarityTrackingEntity;

public class PageClarityTrackingEntityDAO extends BaseJdbcSupport<PageClarityTrackingEntity> {

	@Override
	public String getTableName() {
		return "page_clarity_tracking";
	}

	public void create(PageClarityTrackingEntity pageClarityTrackingEntity) {
		Map<String, Object> parameters = new HashMap<String, Object>();
		parameters.put("domain_id", pageClarityTrackingEntity.getDomainId());
		parameters.put("user_id", pageClarityTrackingEntity.getUserId());
		parameters.put("start_year_week", pageClarityTrackingEntity.getStartYearWeek());
		parameters.put("end_year_week", pageClarityTrackingEntity.getEndYearWeek());
		parameters.put("last_update_timestamp", pageClarityTrackingEntity.getLastUpdateTimestamp());
		this.insertWithoutAutoIncrementalKey(parameters);
	}

	public void update(PageClarityTrackingEntity pageClarityTrackingEntity) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" update");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" set");
		stringBuilder.append("     start_year_week = ?,");
		stringBuilder.append("     end_year_week = ?,");
		stringBuilder.append("     last_update_timestamp = ?");
		stringBuilder.append(" where");
		stringBuilder.append("     domain_id = ?");
		stringBuilder.append(" and user_id = ?");
		String sqlString = stringBuilder.toString();
		this.executeUpdate(sqlString, 
				pageClarityTrackingEntity.getStartYearWeek(), 
				pageClarityTrackingEntity.getEndYearWeek(),
				pageClarityTrackingEntity.getLastUpdateTimestamp(),
				pageClarityTrackingEntity.getDomainId(),
				pageClarityTrackingEntity.getUserId());
	}

	public PageClarityTrackingEntity get(int domainId, int userId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("  *");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     domain_id = ?");
		stringBuilder.append(" and user_id = ?");
		String sqlString = stringBuilder.toString();
		return this.findObject(sqlString, domainId, userId);
	}
	
	public List<Integer> findInactiveDomainIdList() {
		List<Integer> inactiveDomainIdList = new ArrayList<Integer>();
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     distinct page_clarity_tracking.domain_id");
		stringBuilder.append(" from");
		stringBuilder.append("     t_own_domain t_own_domain,");
		stringBuilder.append("     page_clarity_tracking page_clarity_tracking");
		stringBuilder.append(" where");
		stringBuilder.append("     t_own_domain.`status` != ?");
		stringBuilder.append(" and t_own_domain.id = page_clarity_tracking.domain_id");
		stringBuilder.append(" and page_clarity_tracking.user_id = 0");
		String sqlString = stringBuilder.toString();
		List<PageClarityTrackingEntity> pageClarityTrackingEntityList = this.findBySql(sqlString, OwnDomainEntity.STATE_ACTIVE);
		if (pageClarityTrackingEntityList != null && pageClarityTrackingEntityList.size()>0) {
			for (PageClarityTrackingEntity pageClarityTrackingEntity : pageClarityTrackingEntityList) {
				inactiveDomainIdList.add(pageClarityTrackingEntity.getDomainId());
			}
		}
		return inactiveDomainIdList;
	}
	
	public void reset(int domainId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" delete from page_clarity_tracking");
		stringBuilder.append(" where");
		stringBuilder.append("     domain_id = ?");
		String sqlString = stringBuilder.toString();
		this.executeUpdate(sqlString, domainId);
	}
}