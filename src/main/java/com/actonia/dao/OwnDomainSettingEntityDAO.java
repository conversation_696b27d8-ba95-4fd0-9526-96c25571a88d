package com.actonia.dao;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang.math.NumberUtils;

import com.actonia.IConstants;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.entity.OwnDomainSettingEntity;
import com.actonia.utils.FormatUtils;

public class OwnDomainSettingEntityDAO extends BaseJdbcSupport<OwnDomainSettingEntity> {

	@Override
	public String getTableName() {
		return "t_own_domain_setting";
	}

	public OwnDomainSettingEntity getSettingByDomainId(int domainId) {
		String sql = "select *,case when auto_associate_competitor_url is null then " + IConstants.DATABASE_NUMERIC_NULL_FIELD_PROXY_VALUE
				+ " else auto_associate_competitor_url end as autoAssociateCompetitorUrl from t_own_domain_setting where own_domain_id=?";
		return findObject(sql, domainId);
	}

	// https://www.wrike.com/open.htm?id=40580950
	// by floyd
	public Integer checkFiscalMonthByDomainId(int domainId) {
		String sql = "select enable_fiscal_month from t_own_domain_setting where own_domain_id=?";
		Object result = findObject(sql, domainId);
		if (result == null) {
			return 0;
		}
		return NumberUtils.toInt(result.toString());
	}

	// https://www.wrike.com/open.htm?id=39597430
	// by floyd
	public Integer checkCityEnableByDomainId(int domainId) {
		String sql = "select enable_city_rank from t_own_domain_setting where own_domain_id=?";
		return queryForInteger(sql, domainId);
	}

	// https://www.wrike.com/open.htm?id=********
	// by floyd
	public String getCompanyNameByDomainId(int domainId) {
		String sql = "select company_name from t_own_domain_setting where own_domain_id=?";
		return queryForString(sql, domainId);
	}

	public List<Integer> getDomainListTargetUlrCrawlWeekly() {
		String sql = " select distinct own_domain_id from t_own_domain_setting where targeturl_crawl_frequency = ? ";

		return this.queryForIntegerList(sql, OwnDomainSettingEntity.TARGET_URL_CRAWL_FREQUENCY_WEEKLY);
	}

	public void insert(OwnDomainSettingEntity ownDomainSettingEntity) {
		Map<String, Object> values = new HashMap<String, Object>();
		values.put("own_domain_id", ownDomainSettingEntity.getOwnDomainId());
		values.put("search_volume_top_keywords", ownDomainSettingEntity.getSearchVolumeTopKeywords());
		values.put("targeturl_crawl_frequency", ownDomainSettingEntity.getTargeturlCrawlFrequency());
		values.put("yoy_summary_flg", ownDomainSettingEntity.getYoySummaryFlg());
		values.put("gwt_conversion_rate", ownDomainSettingEntity.getGwtConversionRate());
		values.put("gwt_order_avg_value", ownDomainSettingEntity.getGwtOrderAvgValue());
		values.put("associate_competitor_topx", ownDomainSettingEntity.getAssociateCompetitorTopx());
		values.put("mgd_new_rank_max_date", ownDomainSettingEntity.getMgdNewRankMaxDate());
		values.put("gwt_password", ownDomainSettingEntity.getGwtPassword());
		values.put("gwt_account", ownDomainSettingEntity.getGwtAccount());
		values.put("domain_type", ownDomainSettingEntity.getDomainType());
		values.put("client_tier", ownDomainSettingEntity.getClientTier());
		values.put("company_name", ownDomainSettingEntity.getCompanyName());

		this.insert(values);

	}

	// https://www.wrike.com/open.htm?id=********
	// by floyd
	public int updateProperty(int domainId, String property, Object value) {
		if (value == null) {
			value = 0;
		}
		String sql = "update t_own_domain_setting set " + property + " = ? where own_domain_id = ? limit 1 ";
		return executeUpdate(sql, value, domainId);
	}

	public int updateIntProperty(int id, String property, Object value) {
		if (value == null) {
			value = 0;
		}
		String sql = "update t_own_domain_setting set " + property + " = ? where id = ? limit 1 ";
		return executeUpdate(sql, value, id);
	}

	public int updateTierCompanyDomainType(String company, int domainType, int tier, int domainId) {
		String sql = "update t_own_domain_setting set company_name = ? , domain_type = ? , client_tier = ? where own_domain_id = ? limit 1 ";
		return executeUpdate(sql, company, domainType, tier, domainId);
	}

	public int updateCompanyDomainType(String company, int domainType, int domainId) {
		String sql = "update t_own_domain_setting set company_name = ? , domain_type = ? where own_domain_id = ? limit 1 ";
		return executeUpdate(sql, company, domainType, domainId);
	}

	public boolean enableMobileRank(int domainId) {
		String sql = "select * from t_own_domain_setting where own_domain_id=? and enable_moblie = 1";

		OwnDomainSettingEntity ownDomainSettingEntity = findObject(sql, domainId);
		if (ownDomainSettingEntity == null || ownDomainSettingEntity.getEnableMoblie() == null || ownDomainSettingEntity.getEnableMoblie().intValue() == 0) {
			return false;
		} else {
			return true;
		}
	}

	public boolean enableLocalRank(int domainId) {
		String sql = "select * from t_own_domain_setting where own_domain_id=? and enable_local_business_rank = 1";

		OwnDomainSettingEntity ownDomainSettingEntity = findObject(sql, domainId);
		if (ownDomainSettingEntity == null || ownDomainSettingEntity.getEnableLocalBusinessRank() == null
				|| ownDomainSettingEntity.getEnableLocalBusinessRank().intValue() == 0) {
			return false;
		} else {
			return true;
		}
	}

	public List<OwnDomainSettingEntity> getSuspendedDomainList() {
		StringBuilder sql = new StringBuilder();
		sql.append(" select setting.* from t_own_domain_setting setting join t_own_domain tod on setting.own_domain_id=tod.id ");
		sql.append(" where (setting.suspend_process_status=? or setting.suspend_process_status=?)  ");
		sql.append(" and (tod.status=? or (setting.domain_suspend_date is not null and tod.status=?)) order by setting.own_domain_id ");
		return this.findBySql(sql.toString(), OwnDomainSettingEntity.SUSPEND_PROCESS_STATUS_NEWLY_ADDED, OwnDomainSettingEntity.SUSPEND_PROCESS_STATUS_PROCESS_ERROR,
				OwnDomainEntity.STATE_SUSPEND, OwnDomainEntity.STATE_ACTIVE);
	}

	public List<OwnDomainSettingEntity> getDomainListToRemoveUser(int date) {
		StringBuilder sql = new StringBuilder();
		sql.append(" select * from t_own_domain_setting where (remove_user_status=? or remove_user_status=?) ");
		sql.append(" and remove_user_date=? order by own_domain_id ");
		return this.findBySql(sql.toString(), OwnDomainSettingEntity.REMOVE_USER_STATUS_NEWLY_ADDED, OwnDomainSettingEntity.REMOVE_USER_STATUS_PROCESS_ERROR, date);
	}

	public List<OwnDomainSettingEntity> getUniqueGWTAccount() {
		StringBuilder sql = new StringBuilder();
		sql.append(" select distinct gwt_account as gwtAccount, gwt_password as gwtPassword from t_own_domain_setting t ");
		sql.append(" where gwt_account is not null and gwt_account!=''");
		return this.findBySql(sql.toString());
	}

	public List<OwnDomainSettingEntity> getUniqueGWTAccountByGroup(int gwtGroup) {
		StringBuilder sql = new StringBuilder();
		sql.append(" select distinct gwt_account as gwtAccount, gwt_password as gwtPassword from t_own_domain_setting ");
		sql.append(" where gwt_account is not null ");
		if (gwtGroup == OwnDomainSettingEntity.GWT_GROUP_DEFAULT) {
			sql.append(" and (gwt_group=? or gwt_group is null) ");
		} else {
			sql.append(" and gwt_group=? ");
		}

		return this.findBySql(sql.toString(), gwtGroup);
	}

	public List<Integer> getDomainList(int gwtGroup, String userName, String password) {
		StringBuilder sql = new StringBuilder();
		sql.append(" select distinct own_domain_id from t_own_domain_setting ");
		sql.append(" where gwt_account is not null and gwt_account=? and gwt_password=? ");
		if (gwtGroup == OwnDomainSettingEntity.GWT_GROUP_DEFAULT) {
			sql.append(" and (gwt_group=? or gwt_group is null) ");
		} else {
			sql.append(" and gwt_group=? ");
		}

		return this.queryForIntegerList(sql.toString(), userName, password, gwtGroup);
	}

	// https://www.wrike.com/open.htm?id=********
	public Map<Integer, Integer> getDomainTierMap() {
		String sql = " select own_domain_id, client_tier from t_own_domain_setting ";
		List<OwnDomainSettingEntity> list = findBySql(sql);
		Map<Integer, Integer> resultMap = new HashMap<Integer, Integer>();
		if (list != null) {
			for (OwnDomainSettingEntity entity : list) {
				resultMap.put(entity.getOwnDomainId(), entity.getClientTier());
			}
		}
		return resultMap;
	}

	public Map<Integer, OwnDomainSettingEntity> getOwnDomainSettingEntityMap() {
		String sql = " select * from t_own_domain_setting ";
		List<OwnDomainSettingEntity> result = findBySql(sql);
		Map<Integer, OwnDomainSettingEntity> resultMap = new HashMap<Integer, OwnDomainSettingEntity>();
		if (result != null) {
			for (OwnDomainSettingEntity entity : result) {
				resultMap.put(entity.getOwnDomainId(), entity);
			}
		}

		return resultMap;
	}

	public List<OwnDomainSettingEntity> getDomainByCompanyName(String companyName) {
		StringBuilder sql = new StringBuilder();
		sql.append("select * from t_own_domain t left join t_own_domain_setting s on t.id = s.own_domain_id ");
		sql.append("where t.`status` = 1 and s.company_name like ?");

		return findBySql(sql.toString(), "%" + companyName + "%");
	}

	/**
	 * find domainIds Set by companyName
	 * @param companyNames companyName for search
	 * @return HashSet<domainId>
	 */
	public Set<Integer> findDomainIdsByCompanyName(String[] companyNames) {
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT d.id ")
				.append("FROM t_own_domain_setting setting ")
				.append("JOIN t_own_domain d ON setting.own_domain_id = d.id AND d.`status` = 1 ");
		if (companyNames.length > 0) {
			String whereClause = Arrays.stream(companyNames)
					.map(name -> "setting.company_name = ?")
					.collect(Collectors.joining(" OR "));
			sql.append("WHERE ").append(whereClause);
		}
		final List<Integer> domainIds = super.queryForIntegerList(sql.toString(), companyNames);
		return new HashSet<>(domainIds);
	}

	public void updateTargetUrlCustomDataDate(List<Integer> domainIdList, Integer targetUrlCustomDataDate) {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage(
				"updateTargetUrlCustomDataDate() begins. domainIdList.size()=" + domainIdList.size() + ",targetUrlCustomDataDate=" + targetUrlCustomDataDate);
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" update " + getTableName());
		stringBuilder.append(" set");
		stringBuilder.append("    target_url_custom_data_date = ?");
		stringBuilder.append(" where");
		stringBuilder.append("    own_domain_id = ?");
		String sqlString = stringBuilder.toString();
		Object[] values = null;
		List<Object[]> batch = new ArrayList<Object[]>();
		for (Integer domainId : domainIdList) {
			values = new Object[] { targetUrlCustomDataDate, domainId, };
			batch.add(values);
		}
		this.executeBatch(sqlString, batch);
		FormatUtils.getInstance().logMemoryUsage("updateTargetUrlCustomDataDate() ends. domainIdList.size()=" + domainIdList.size() + ",targetUrlCustomDataDate="
				+ targetUrlCustomDataDate + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	public void resetTargetUrlCustomDataDate() {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("resetTargetUrlCustomDataDate() begins.");
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" update " + getTableName());
		stringBuilder.append(" set");
		stringBuilder.append("    target_url_custom_data_date = null");
		stringBuilder.append(" where");
		stringBuilder.append("    target_url_custom_data_date is not null");
		String sqlString = stringBuilder.toString();
		this.executeUpdate(sqlString);
		FormatUtils.getInstance().logMemoryUsage("resetTargetUrlCustomDataDate() ends ,elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	public String getDateFormatDayByDomainId(int domainId) {
		String sql = "select date_format_day from t_own_domain_setting where own_domain_id=?";
		return queryForString(sql, domainId);
	}

	public List<OwnDomainSettingEntity> getDeleteNon2XxUrlsInDaysForActiveDomain() {
		String sql = "select s.own_domain_id, s.delete_non2xx_urls_in_days deleteNon2xxUrlsInDays from t_own_domain_setting s join t_own_domain d on s.own_domain_id = d.id where d.status = ? and s.delete_non2xx_urls_in_days > 0";
		return findBySql(sql, 1);
	}
}
