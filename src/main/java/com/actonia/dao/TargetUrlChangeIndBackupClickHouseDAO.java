package com.actonia.dao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import com.actonia.utils.AwsCredentialsEnvKeyConstructor;
import org.apache.commons.lang.StringUtils;

import com.actonia.IConstants;
import com.actonia.utils.FormatUtils;

import ru.yandex.clickhouse.ClickHouseDataSource;
import ru.yandex.clickhouse.settings.ClickHouseProperties;

// Due to ClickHouse concurrency issues, use Singleton pattern, do not use Spring (ie. all methods must be 'synchronized')
public class TargetUrlChangeIndBackupClickHouseDAO {

	//private boolean isDebug = false;
	private List<String> databaseHostnameList;
	private String databasePort = null;
	private String databaseName = null;
	private List<Connection> connectionList;
	private int batchCreationSize;
	private String databaseUser = null;
	private String databasePassword = null;
	private List<String> connectionUrlList = null;
	private int connectionTimeoutInMilliseconds;
	private int maximumRetryCounts;
	private int retryWaitMilliseconds;
	public static final String TABLE_NAME = "local_target_url_change_ind";

	private final String s3AccessKey;
	private final String s3SecretKey;

	public TargetUrlChangeIndBackupClickHouseDAO(String[] databaseHostnameArrayInput, String databasePortInput, String databaseNameInput, int batchCreationSizeInput,
			String databaseUserInput, String databasePasswordInput, int connectionTimeoutInMillisecondsInput, int maximumRetryCountsInput,
			int retryWaitMillisecondsInput) throws Exception {

		ClickHouseDataSource clickHouseDataSource = null;
		Connection connection = null;

		databaseHostnameList = Arrays.asList(databaseHostnameArrayInput);

		databasePort = databasePortInput;
		databaseName = databaseNameInput;
		batchCreationSize = batchCreationSizeInput;
		databaseUser = databaseUserInput;
		databasePassword = databasePasswordInput;
		connectionTimeoutInMilliseconds = connectionTimeoutInMillisecondsInput;
		maximumRetryCounts = maximumRetryCountsInput;
		retryWaitMilliseconds = retryWaitMillisecondsInput;

		FormatUtils.getInstance()
				.logMemoryUsage("TargetUrlChangeIndBackupClickHouseDAO() databaseHostnameList=" + databaseHostnameList.toString() + ",databasePort=" + databasePort
						+ ",databaseName=" + databaseName + ",batchCreationSize=" + batchCreationSize + ",databaseUser=" + databaseUser + ",databasePassword="
						+ databasePassword + ",connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ",maximumRetryCounts=" + maximumRetryCounts
						+ ",retryWaitMilliseconds=" + retryWaitMilliseconds + ",TABLE_NAME=" + TABLE_NAME);

		ClickHouseProperties clickHouseProperties = new ClickHouseProperties();
		clickHouseProperties.setDecompress(true);
		clickHouseProperties.setConnectionTimeout(connectionTimeoutInMilliseconds);
		clickHouseProperties.setSocketTimeout(connectionTimeoutInMilliseconds);

		connectionList = new ArrayList<Connection>();

		String connectionUrl = null;

		connectionUrlList = new ArrayList<String>();

		for (String databaseHostname : databaseHostnameList) {
			if (StringUtils.isNotBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
						+ "?user=" + databaseUser + "&password=" + databasePassword;
			} else {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH
						+ databaseName;
			}
			connectionUrlList.add(connectionUrl);
			FormatUtils.getInstance().logMemoryUsage("TargetUrlChangeIndBackupClickHouseDAO() connectionUrl=" + connectionUrl);
			clickHouseDataSource = new ClickHouseDataSource(connectionUrl, clickHouseProperties);
			connection = clickHouseDataSource.getConnection();
			connectionList.add(connection);
		}
		this.s3AccessKey = AwsCredentialsEnvKeyConstructor.getInstance().getPlainTextS3AccessKey();
		this.s3SecretKey = AwsCredentialsEnvKeyConstructor.getInstance().getS3DecryptedSecretKey();
	}

	@Override
	public String toString() {
		return "TargetUrlChangeIndBackupClickHouseDAO [databaseHostnameList=" + databaseHostnameList.toString() + ", databasePort=" + databasePort + ", databaseName="
				+ databaseName + ", batchCreationSize=" + batchCreationSize + ", databaseUser=" + databaseUser + ", databasePassword=" + databasePassword
				+ ", connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ", maximumRetryCounts=" + maximumRetryCounts + ", retryWaitMilliseconds="
				+ retryWaitMilliseconds + "]";
	}

	private synchronized String getTableName(String tableName) {
		if (StringUtils.isNotBlank(tableName)) {
			return tableName;
		} else {
			return TABLE_NAME;
		}
	}

	public Date getEarliestTrackDate(String tableName) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		Date trackDate = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     min(track_date)");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getEarliestTrackDate() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		//if (isDebug == true) {
		//	connectionUrl = connectionUrlList.get(connectionIndex);
		//	FormatUtils.getInstance().logMemoryUsage("getEarliestTrackDate() connectionUrl=" + connectionUrl);
		//}
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				preparedStatement = connection.prepareStatement(sqlString);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					trackDate = resultSet.getTimestamp(1);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				e.printStackTrace();
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getEarliestTrackDate() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			} finally {
				if (resultSet != null) {
					resultSet.close();
				}
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();
				}
			}
		}
		FormatUtils.getInstance()
				.logMemoryUsage("getEarliestTrackDate() trackDate=" + trackDate + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		return trackDate;
	}

	public Date getLatestTrackDate(String tableName) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		Date trackDate = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     max(track_date)");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getLatestTrackDate() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		//if (isDebug == true) {
		//	connectionUrl = connectionUrlList.get(connectionIndex);
		//	FormatUtils.getInstance().logMemoryUsage("getLatestTrackDate() connectionUrl=" + connectionUrl);
		//}
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				preparedStatement = connection.prepareStatement(sqlString);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					trackDate = resultSet.getTimestamp(1);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				e.printStackTrace();
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getLatestTrackDate() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			} finally {
				if (resultSet != null) {
					resultSet.close();
				}
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();
				}
			}
		}
		FormatUtils.getInstance()
				.logMemoryUsage("getLatestTrackDate() trackDate=" + trackDate + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		return trackDate;
	}

	public void backupToS3(String s3ObjectURI, String tableName, String trackDateString) throws Exception {
		//FormatUtils.getInstance().logMemoryUsage("backupToS3() begins. s3ObjectURI=" + s3ObjectURI + ",tableName=" + tableName + ",trackDateString="
		//		+ trackDateString);
		//long startTimestamp = System.currentTimeMillis();
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("insert into function s3('" + s3ObjectURI + "', ");
		stringBuilder.append("  '" + s3AccessKey + "', ");
		stringBuilder.append("  '" + s3SecretKey + "', ");
		stringBuilder.append("  'Native', ");
		stringBuilder.append(
				"  '`domain_id` UInt32, `url` String, `track_date` Date, `url_hash` UInt64, `url_murmur_hash` UInt64, `current_crawl_timestamp` DateTime, `change_indicator` String, `previous_crawl_timestamp` DateTime, `update_timestamp` DateTime, `change_type` Int8, `critical_ind` Int8, `alternate_links_current` String, `alternate_links_previous` String, `amphtml_href_current` String, `amphtml_href_previous` String, `analyzed_url_s_current` String, `analyzed_url_s_previous` String, `archive_flg_current` String, `archive_flg_previous` String, `base_tag_current` String, `base_tag_previous` String, `base_tag_target_current` String, `base_tag_target_previous` String, `blocked_by_robots_current` String, `blocked_by_robots_previous` String, `canonical_current` String, `canonical_previous` String, `canonical_header_flag_current` Int8, `canonical_header_flag_previous` Int8, `canonical_header_type_current` String, `canonical_header_type_previous` String, `canonical_type_current` String, `canonical_type_previous` String, `canonical_url_is_consistent_current` String, `canonical_url_is_consistent_previous` String, `content_type_current` String, `content_type_previous` String, `custom_data_current` String, `custom_data_previous` String, `description_current` String, `description_previous` String, `description_length_current` Int32, `description_length_previous` Int32, `error_message_current` String, `error_message_previous` String, `final_response_code_current` Int32, `final_response_code_previous` Int32, `follow_flg_current` String, `follow_flg_previous` String, `h1_current` String, `h1_previous` String, `h1_count_current` Int32, `h1_count_previous` Int32, `h1_length_current` Int32, `h1_length_previous` Int32, `h2_current` String, `h2_previous` String, `header_noarchive_current` Int8, `header_noarchive_previous` Int8, `header_nofollow_current` Int8, `header_nofollow_previous` Int8, `header_noindex_current` Int8, `header_noindex_previous` Int8, `header_noodp_current` Int8, `header_noodp_previous` Int8, `header_nosnippet_current` Int8, `header_nosnippet_previous` Int8, `header_noydir_current` Int8, `header_noydir_previous` Int8, `hreflang_errors_current` String, `hreflang_errors_previous` String, `hreflang_links_current` String, `hreflang_links_previous` String, `hreflang_links_out_count_current` Int32, `hreflang_links_out_count_previous` Int32, `hreflang_url_count_current` Int32, `hreflang_url_count_previous` Int32, `index_flg_current` String, `index_flg_previous` String, `indexable_current` Int8, `indexable_previous` Int8, `insecure_resources_current` String, `insecure_resources_previous` String, `meta_charset_current` String, `meta_charset_previous` String, `meta_content_type_current` String, `meta_content_type_previous` String, `meta_disabled_sitelinks_current` Int8, `meta_disabled_sitelinks_previous` Int8, `meta_noodp_current` Int8, `meta_noodp_previous` Int8, `meta_nosnippet_current` Int8, `meta_nosnippet_previous` Int8, `meta_noydir_current` Int8, `meta_noydir_previous` Int8, `meta_redirect_current` Int8, `meta_redirect_previous` Int8, `mixed_redirects_current` Int8, `mixed_redirects_previous` Int8, `mobile_rel_alternate_url_is_consistent_current` Int8, `mobile_rel_alternate_url_is_consistent_previous` Int8, `noodp_current` Int8, `noodp_previous` Int8, `nosnippet_current` Int8, `nosnippet_previous` Int8, `noydir_current` Int8, `noydir_previous` Int8, `og_markup_current` String, `og_markup_previous` String, `og_markup_length_current` Int32, `og_markup_length_previous` Int32, `outlink_count_current` Int32, `outlink_count_previous` Int32, `page_analysis_results_chg_ind_json` String, `page_link_current` String, `page_link_previous` String, `redirect_blocked_current` Int8, `redirect_blocked_previous` Int8, `redirect_blocked_reason_current` String, `redirect_blocked_reason_previous` String, `redirect_chain_current` String, `redirect_chain_previous` String, `redirect_final_url_current` String, `redirect_final_url_previous` String, `redirect_times_current` Int32, `redirect_times_previous` Int32, `response_code_current` String, `response_code_previous` String, `response_headers_current` String, `response_headers_previous` String, `robots_contents_current` String, `robots_contents_previous` String, `structured_data_current` String, `structured_data_previous` String, `title_current` String, `title_previous` String, `title_length_current` Int32, `title_length_previous` Int32, `viewport_content_current` String, `viewport_content_previous` String, `sign` Int8, `robot_txt_current` String, `robot_txt_previous` String',");
		stringBuilder.append("   'zstd') ");
		stringBuilder.append("select * ");
		stringBuilder.append("from " + getTableName(tableName) + " ");
		stringBuilder.append("where track_date = '" + trackDateString + "'");
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("backupToS3() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		//if (isDebug == true) {
		//	connectionUrl = connectionUrlList.get(connectionIndex);
		//	FormatUtils.getInstance().logMemoryUsage("backupToS3() connectionUrl=" + connectionUrl);
		//}
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				preparedStatement = connection.prepareStatement(sqlString);
				resultSet = preparedStatement.executeQuery();
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				e.printStackTrace();
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("backupToS3() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			} finally {
				if (resultSet != null) {
					resultSet.close();
				}
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();
				}
			}
		}
		//FormatUtils.getInstance().logMemoryUsage("backupToS3() ends. s3ObjectURI=" + s3ObjectURI + ",tableName=" + tableName + ",trackDateString="
		//		+ trackDateString + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	public void restoreFromS3(String thread, String s3ObjectURI, String tableName) throws Exception {
		//FormatUtils.getInstance().logMemoryUsage("restoreFromS3() begins. s3ObjectURI=" + s3ObjectURI + ",tableName=" + tableName);
		//long startTimestamp = System.currentTimeMillis();
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("insert into " + getTableName(tableName) + " (domain_id, url, track_date, url_hash, url_murmur_hash, current_crawl_timestamp, change_indicator, previous_crawl_timestamp, update_timestamp, change_type, critical_ind, alternate_links_current, alternate_links_previous, amphtml_href_current, amphtml_href_previous, analyzed_url_s_current, analyzed_url_s_previous, archive_flg_current, archive_flg_previous, base_tag_current, base_tag_previous, base_tag_target_current, base_tag_target_previous, blocked_by_robots_current, blocked_by_robots_previous, canonical_current, canonical_previous, canonical_header_flag_current, canonical_header_flag_previous, canonical_header_type_current, canonical_header_type_previous, canonical_type_current, canonical_type_previous, canonical_url_is_consistent_current, canonical_url_is_consistent_previous, content_type_current, content_type_previous, custom_data_current, custom_data_previous, description_current, description_previous, description_length_current, description_length_previous, error_message_current, error_message_previous, final_response_code_current, final_response_code_previous, follow_flg_current, follow_flg_previous, h1_current, h1_previous, h1_count_current, h1_count_previous, h1_length_current, h1_length_previous, h2_current, h2_previous, header_noarchive_current, header_noarchive_previous, header_nofollow_current, header_nofollow_previous, header_noindex_current, header_noindex_previous, header_noodp_current, header_noodp_previous, header_nosnippet_current, header_nosnippet_previous, header_noydir_current, header_noydir_previous, hreflang_errors_current, hreflang_errors_previous, hreflang_links_current, hreflang_links_previous, hreflang_links_out_count_current, hreflang_links_out_count_previous, hreflang_url_count_current, hreflang_url_count_previous, index_flg_current, index_flg_previous, indexable_current, indexable_previous, insecure_resources_current, insecure_resources_previous, meta_charset_current, meta_charset_previous, meta_content_type_current, meta_content_type_previous, meta_disabled_sitelinks_current, meta_disabled_sitelinks_previous, meta_noodp_current, meta_noodp_previous, meta_nosnippet_current, meta_nosnippet_previous, meta_noydir_current, meta_noydir_previous, meta_redirect_current, meta_redirect_previous, mixed_redirects_current, mixed_redirects_previous, mobile_rel_alternate_url_is_consistent_current, mobile_rel_alternate_url_is_consistent_previous, noodp_current, noodp_previous, nosnippet_current, nosnippet_previous, noydir_current, noydir_previous, og_markup_current, og_markup_previous, og_markup_length_current, og_markup_length_previous, outlink_count_current, outlink_count_previous, page_analysis_results_chg_ind_json, page_link_current, page_link_previous, redirect_blocked_current, redirect_blocked_previous, redirect_blocked_reason_current, redirect_blocked_reason_previous, redirect_chain_current, redirect_chain_previous, redirect_final_url_current, redirect_final_url_previous, redirect_times_current, redirect_times_previous, response_code_current, response_code_previous, response_headers_current, response_headers_previous, robots_contents_current, robots_contents_previous, structured_data_current, structured_data_previous, title_current, title_previous, title_length_current, title_length_previous, viewport_content_current, viewport_content_previous, sign) ");
		stringBuilder.append("select domain_id, url, track_date, url_hash, url_murmur_hash, current_crawl_timestamp, change_indicator, previous_crawl_timestamp, update_timestamp, change_type, critical_ind, alternate_links_current, alternate_links_previous, amphtml_href_current, amphtml_href_previous, analyzed_url_s_current, analyzed_url_s_previous, archive_flg_current, archive_flg_previous, base_tag_current, base_tag_previous, base_tag_target_current, base_tag_target_previous, blocked_by_robots_current, blocked_by_robots_previous, canonical_current, canonical_previous, canonical_header_flag_current, canonical_header_flag_previous, canonical_header_type_current, canonical_header_type_previous, canonical_type_current, canonical_type_previous, canonical_url_is_consistent_current, canonical_url_is_consistent_previous, content_type_current, content_type_previous, custom_data_current, custom_data_previous, description_current, description_previous, description_length_current, description_length_previous, error_message_current, error_message_previous, final_response_code_current, final_response_code_previous, follow_flg_current, follow_flg_previous, h1_current, h1_previous, h1_count_current, h1_count_previous, h1_length_current, h1_length_previous, h2_current, h2_previous, header_noarchive_current, header_noarchive_previous, header_nofollow_current, header_nofollow_previous, header_noindex_current, header_noindex_previous, header_noodp_current, header_noodp_previous, header_nosnippet_current, header_nosnippet_previous, header_noydir_current, header_noydir_previous, hreflang_errors_current, hreflang_errors_previous, hreflang_links_current, hreflang_links_previous, hreflang_links_out_count_current, hreflang_links_out_count_previous, hreflang_url_count_current, hreflang_url_count_previous, index_flg_current, index_flg_previous, indexable_current, indexable_previous, insecure_resources_current, insecure_resources_previous, meta_charset_current, meta_charset_previous, meta_content_type_current, meta_content_type_previous, meta_disabled_sitelinks_current, meta_disabled_sitelinks_previous, meta_noodp_current, meta_noodp_previous, meta_nosnippet_current, meta_nosnippet_previous, meta_noydir_current, meta_noydir_previous, meta_redirect_current, meta_redirect_previous, mixed_redirects_current, mixed_redirects_previous, mobile_rel_alternate_url_is_consistent_current, mobile_rel_alternate_url_is_consistent_previous, noodp_current, noodp_previous, nosnippet_current, nosnippet_previous, noydir_current, noydir_previous, og_markup_current, og_markup_previous, og_markup_length_current, og_markup_length_previous, outlink_count_current, outlink_count_previous, page_analysis_results_chg_ind_json, page_link_current, page_link_previous, redirect_blocked_current, redirect_blocked_previous, redirect_blocked_reason_current, redirect_blocked_reason_previous, redirect_chain_current, redirect_chain_previous, redirect_final_url_current, redirect_final_url_previous, redirect_times_current, redirect_times_previous, response_code_current, response_code_previous, response_headers_current, response_headers_previous, robots_contents_current, robots_contents_previous, structured_data_current, structured_data_previous, title_current, title_previous, title_length_current, title_length_previous, viewport_content_current, viewport_content_previous, sign ");
		stringBuilder.append("from s3('" + s3ObjectURI + "', ");
		stringBuilder.append("  '" + s3AccessKey + "', ");
		stringBuilder.append("  '" + s3SecretKey + "', ");
		stringBuilder.append("  'Native', ");
		stringBuilder.append(
				"  '`domain_id` UInt32, `url` String, `track_date` Date, `url_hash` UInt64, `url_murmur_hash` UInt64, `current_crawl_timestamp` DateTime, `change_indicator` String, `previous_crawl_timestamp` DateTime, `update_timestamp` DateTime, `change_type` Int8, `critical_ind` Int8, `alternate_links_current` String, `alternate_links_previous` String, `amphtml_href_current` String, `amphtml_href_previous` String, `analyzed_url_s_current` String, `analyzed_url_s_previous` String, `archive_flg_current` String, `archive_flg_previous` String, `base_tag_current` String, `base_tag_previous` String, `base_tag_target_current` String, `base_tag_target_previous` String, `blocked_by_robots_current` String, `blocked_by_robots_previous` String, `canonical_current` String, `canonical_previous` String, `canonical_header_flag_current` Int8, `canonical_header_flag_previous` Int8, `canonical_header_type_current` String, `canonical_header_type_previous` String, `canonical_type_current` String, `canonical_type_previous` String, `canonical_url_is_consistent_current` String, `canonical_url_is_consistent_previous` String, `content_type_current` String, `content_type_previous` String, `custom_data_current` String, `custom_data_previous` String, `description_current` String, `description_previous` String, `description_length_current` Int32, `description_length_previous` Int32, `error_message_current` String, `error_message_previous` String, `final_response_code_current` Int32, `final_response_code_previous` Int32, `follow_flg_current` String, `follow_flg_previous` String, `h1_current` String, `h1_previous` String, `h1_count_current` Int32, `h1_count_previous` Int32, `h1_length_current` Int32, `h1_length_previous` Int32, `h2_current` String, `h2_previous` String, `header_noarchive_current` Int8, `header_noarchive_previous` Int8, `header_nofollow_current` Int8, `header_nofollow_previous` Int8, `header_noindex_current` Int8, `header_noindex_previous` Int8, `header_noodp_current` Int8, `header_noodp_previous` Int8, `header_nosnippet_current` Int8, `header_nosnippet_previous` Int8, `header_noydir_current` Int8, `header_noydir_previous` Int8, `hreflang_errors_current` String, `hreflang_errors_previous` String, `hreflang_links_current` String, `hreflang_links_previous` String, `hreflang_links_out_count_current` Int32, `hreflang_links_out_count_previous` Int32, `hreflang_url_count_current` Int32, `hreflang_url_count_previous` Int32, `index_flg_current` String, `index_flg_previous` String, `indexable_current` Int8, `indexable_previous` Int8, `insecure_resources_current` String, `insecure_resources_previous` String, `meta_charset_current` String, `meta_charset_previous` String, `meta_content_type_current` String, `meta_content_type_previous` String, `meta_disabled_sitelinks_current` Int8, `meta_disabled_sitelinks_previous` Int8, `meta_noodp_current` Int8, `meta_noodp_previous` Int8, `meta_nosnippet_current` Int8, `meta_nosnippet_previous` Int8, `meta_noydir_current` Int8, `meta_noydir_previous` Int8, `meta_redirect_current` Int8, `meta_redirect_previous` Int8, `mixed_redirects_current` Int8, `mixed_redirects_previous` Int8, `mobile_rel_alternate_url_is_consistent_current` Int8, `mobile_rel_alternate_url_is_consistent_previous` Int8, `noodp_current` Int8, `noodp_previous` Int8, `nosnippet_current` Int8, `nosnippet_previous` Int8, `noydir_current` Int8, `noydir_previous` Int8, `og_markup_current` String, `og_markup_previous` String, `og_markup_length_current` Int32, `og_markup_length_previous` Int32, `outlink_count_current` Int32, `outlink_count_previous` Int32, `page_analysis_results_chg_ind_json` String, `page_link_current` String, `page_link_previous` String, `redirect_blocked_current` Int8, `redirect_blocked_previous` Int8, `redirect_blocked_reason_current` String, `redirect_blocked_reason_previous` String, `redirect_chain_current` String, `redirect_chain_previous` String, `redirect_final_url_current` String, `redirect_final_url_previous` String, `redirect_times_current` Int32, `redirect_times_previous` Int32, `response_code_current` String, `response_code_previous` String, `response_headers_current` String, `response_headers_previous` String, `robots_contents_current` String, `robots_contents_previous` String, `structured_data_current` String, `structured_data_previous` String, `title_current` String, `title_previous` String, `title_length_current` Int32, `title_length_previous` Int32, `viewport_content_current` String, `viewport_content_previous` String, `sign` Int8, `robot_txt_current` String, `robot_txt_previous` String', ");
		stringBuilder.append("   'zstd') ");
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("restoreFromS3() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		//if (isDebug == true) {
		//	connectionUrl = connectionUrlList.get(connectionIndex);
		//	FormatUtils.getInstance().logMemoryUsage("restoreFromS3() connectionUrl=" + connectionUrl);
		//}
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				preparedStatement = connection.prepareStatement(sqlString);
				resultSet = preparedStatement.executeQuery();
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				e.printStackTrace();
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("restoreFromS3() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			} finally {
				if (resultSet != null) {
					resultSet.close();
				}
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();
				}
			}
		}
		//FormatUtils.getInstance().logMemoryUsage("restoreFromS3() ends. s3ObjectURI=" + s3ObjectURI + ",tableName=" + tableName + ",elapsed(s.)="
		//		+ (System.currentTimeMillis() - startTimestamp) / 1000);
	}

}
