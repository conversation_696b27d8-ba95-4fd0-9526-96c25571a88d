package com.actonia.dao;

import java.util.List;

import com.actonia.entity.TargeturlCrawler;

public class TargeturlCrawlerEntityDAO extends BaseJdbcSupport<TargeturlCrawler> {

	@Override
	public String getTableName() {
		return "targeturl_crawler";
	}

	public List<TargeturlCrawler> getPatternsByDomainId(int domainId) {
		String sql = "select * from targeturl_crawler where own_domain_id=?";
		return findBySql(sql, domainId);
	}
	
	public List<TargeturlCrawler> getAllPatterns() {
		String sql = "select * from targeturl_crawler";
		return findBySql(sql);
	}
	
    public List<TargeturlCrawler> getPatternsByDomainId(int domainId, String pattern) {
        String sql = "select * from targeturl_crawler where own_domain_id=? and pattern=?";
        return findBySql(sql, domainId, pattern);
    }
}
