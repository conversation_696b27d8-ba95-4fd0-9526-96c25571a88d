package com.actonia.dao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

import com.actonia.IConstants;
import com.actonia.entity.HtmlFileNameClickHouseEntity;
import com.actonia.utils.FormatUtils;

import ru.yandex.clickhouse.ClickHouseDataSource;
import ru.yandex.clickhouse.settings.ClickHouseProperties;

// Due to ClickHouse concurrency issues, use Singleton pattern, do not use Spring (ie. all methods must be 'synchronized')
public class TargetUrlHtmlFileNameClickHouseDAO {

	//private static boolean isDebug = false;
	public static final String TABLE_NAME = "dis_target_url_html_file_name";
	//public static final String TABLE_NAME = "unit_test_target_url_html_file_name";
	private static final int TOTAL_DAO_INSTANCES = 1;
	private static int daoMapIndex = 0;

	// map key = DAO index (0  - 59)
	// map value = instance of TargetUrlHtmlFileNameClickHouseDAO
	private static Map<Integer, TargetUrlHtmlFileNameClickHouseDAO> targetUrlHtmlFileNameClickHouseDAOMap;

	private List<String> databaseHostnameList;
	private String databasePort = null;
	private String databaseName = null;
	private List<Connection> connectionList;
	private int batchCreationSize;
	private String databaseUser = null;
	private String databasePassword = null;
	private List<String> connectionUrlList = null;
	private int connectionTimeoutInMilliseconds;
	private int maximumRetryCounts;
	private int retryWaitMilliseconds;
	private int daoIndex;

	public static TargetUrlHtmlFileNameClickHouseDAO getInstance() throws Exception {
		TargetUrlHtmlFileNameClickHouseDAO targetUrlHtmlFileNameClickHouseDAO = null;
		String clickHouseDatabaseHostnames = null;
		String[] clickHouseDatabaseHostnameArray = null;
		String clickHouseDatabasePort = null;
		String clickHouseDatabaseName = null;
		String clickHouseDatabaseUser = null;
		String clickHouseDatabasePassword = null;
		int clickHouseBatchCreationSize = 0;
		int clickHouseconnectionTimeoutInMilliseconds = 0;
		int clickHouseMaximumRetryCounts = 0;
		int clickHouseRetryWaitMilliseconds = 0;
		if (targetUrlHtmlFileNameClickHouseDAOMap == null) {
			clickHouseDatabaseHostnames = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_HOSTNAMES);
			clickHouseDatabaseHostnameArray = clickHouseDatabaseHostnames.split(IConstants.COMMA);
			clickHouseDatabasePort = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_PORT);
			clickHouseDatabaseName = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_NAME);
			clickHouseDatabaseUser = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_USER);
			clickHouseDatabasePassword = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_PASSWORD);
			clickHouseBatchCreationSize = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_BATCH_CREATION_SIZE, 100);
			clickHouseconnectionTimeoutInMilliseconds = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_CONNECTION_TIMEOUT_MILLISECONDS, 88888);
			clickHouseMaximumRetryCounts = ClickHouseCrawlDatabaseConfigurations.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_MAX_RETRY_COUNTS,
					8);
			clickHouseRetryWaitMilliseconds = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_RETRY_WAIT_MILLISECONDS, 1000);
			FormatUtils.getInstance().logMemoryUsage("TargetUrlHtmlFileNameClickHouseDAO() clickHouseDatabaseHostnames=" + clickHouseDatabaseHostnames.toString()
					+ ",clickHouseDatabasePort=" + clickHouseDatabasePort + ",clickHouseDatabaseName=" + clickHouseDatabaseName + ",clickHouseBatchCreationSize="
					+ clickHouseBatchCreationSize + ",clickHouseDatabaseUser=" + clickHouseDatabaseUser + ",clickHouseDatabasePassword=" + clickHouseDatabasePassword
					+ ",clickHouseconnectionTimeoutInMilliseconds=" + clickHouseconnectionTimeoutInMilliseconds + ",clickHouseMaximumRetryCounts="
					+ clickHouseMaximumRetryCounts + ",clickHouseRetryWaitMilliseconds=" + clickHouseRetryWaitMilliseconds + ",TABLE_NAME=" + TABLE_NAME);

			targetUrlHtmlFileNameClickHouseDAOMap = new HashMap<Integer, TargetUrlHtmlFileNameClickHouseDAO>();
			for (int i = 0; i < TOTAL_DAO_INSTANCES; i++) {
				targetUrlHtmlFileNameClickHouseDAO = initialize(clickHouseDatabaseHostnameArray, clickHouseDatabasePort, clickHouseDatabaseName, clickHouseDatabaseUser,
						clickHouseDatabasePassword, clickHouseBatchCreationSize, clickHouseconnectionTimeoutInMilliseconds, clickHouseMaximumRetryCounts,
						clickHouseRetryWaitMilliseconds, i);
				targetUrlHtmlFileNameClickHouseDAOMap.put(i, targetUrlHtmlFileNameClickHouseDAO);
			}
			FormatUtils.getInstance().logMemoryUsage("getInstance() total targetUrlHtmlFileNameClickHouseDAOs=" + targetUrlHtmlFileNameClickHouseDAOMap.size());
		}
		int index = getDaoMapIndex();
		targetUrlHtmlFileNameClickHouseDAO = targetUrlHtmlFileNameClickHouseDAOMap.get(index);
		return targetUrlHtmlFileNameClickHouseDAO;
	}

	private static synchronized int getDaoMapIndex() {
		int index = 0;
		if (daoMapIndex >= TOTAL_DAO_INSTANCES) {
			daoMapIndex = 0;
			index = 0;
		} else {
			index = daoMapIndex++;
		}
		return index;
	}

	// initialize TargetUrlHtmlFileNameClickHouseDAO based on runtime clickhouse configurations 
	private static TargetUrlHtmlFileNameClickHouseDAO initialize(String[] clickHouseDatabaseHostnameArray, String clickHouseDatabasePort, String clickHouseDatabaseName,
			String clickHouseDatabaseUser, String clickHouseDatabasePassword, int clickHouseBatchCreationSize, int clickHouseconnectionTimeoutInMilliseconds,
			int clickHouseMaximumRetryCounts, int clickHouseRetryWaitMilliseconds, int daoIndex) throws Exception {
		TargetUrlHtmlFileNameClickHouseDAO targetUrlHtmlFileNameClickHouseDAO = null;

		try {
			targetUrlHtmlFileNameClickHouseDAO = new TargetUrlHtmlFileNameClickHouseDAO(clickHouseDatabaseHostnameArray, clickHouseDatabasePort, clickHouseDatabaseName,
					clickHouseBatchCreationSize, clickHouseDatabaseUser, clickHouseDatabasePassword, clickHouseconnectionTimeoutInMilliseconds,
					clickHouseMaximumRetryCounts, clickHouseRetryWaitMilliseconds, daoIndex);
		} catch (Exception e) {
			e.printStackTrace();
		}

		//FormatUtils.getInstance().logMemoryUsage("initialize() targetUrlHtmlFileNameClickHouseDAO=" + targetUrlHtmlFileNameClickHouseDAO.toString());
		return targetUrlHtmlFileNameClickHouseDAO;
	}

	private TargetUrlHtmlFileNameClickHouseDAO(String[] databaseHostnameArrayInput, String databasePortInput, String databaseNameInput, int batchCreationSizeInput,
			String databaseUserInput, String databasePasswordInput, int connectionTimeoutInMillisecondsInput, int maximumRetryCountsInput,
			int retryWaitMillisecondsInput, int index) throws Exception {

		ClickHouseDataSource clickHouseDataSource = null;
		Connection connection = null;

		databaseHostnameList = Arrays.asList(databaseHostnameArrayInput);
		databasePort = databasePortInput;
		databaseName = databaseNameInput;
		batchCreationSize = batchCreationSizeInput;
		databaseUser = databaseUserInput;
		databasePassword = databasePasswordInput;
		connectionTimeoutInMilliseconds = connectionTimeoutInMillisecondsInput;
		maximumRetryCounts = maximumRetryCountsInput;
		retryWaitMilliseconds = retryWaitMillisecondsInput;
		daoIndex = index;

		ClickHouseProperties clickHouseProperties = new ClickHouseProperties();
		clickHouseProperties.setDecompress(true);
		clickHouseProperties.setConnectionTimeout(connectionTimeoutInMilliseconds);
		clickHouseProperties.setSocketTimeout(connectionTimeoutInMilliseconds);

		connectionList = new ArrayList<Connection>();

		String connectionUrl = null;

		connectionUrlList = new ArrayList<String>();

		for (String databaseHostname : databaseHostnameList) {
			if (StringUtils.isNotBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
						+ "?user=" + databaseUser + "&password=" + databasePassword;
			} else if (StringUtils.isBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
						+ "?password=" + databasePassword;
			} else {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH
						+ databaseName;
			}
			connectionUrlList.add(connectionUrl);
			//FormatUtils.getInstance().logMemoryUsage("TargetUrlHtmlFileNameClickHouseDAO() connectionUrl=" + connectionUrl);
			clickHouseDataSource = new ClickHouseDataSource(connectionUrl, clickHouseProperties);
			connection = clickHouseDataSource.getConnection();
			connectionList.add(connection);
		}
	}

	@Override
	public String toString() {
		return "TargetUrlHtmlFileNameClickHouseDAO [databaseHostnameList=" + databaseHostnameList.toString() + ", databasePort=" + databasePort + ", databaseName="
				+ databaseName + ", batchCreationSize=" + batchCreationSize + ", databaseUser=" + databaseUser + ", databasePassword=" + databasePassword
				+ ", connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ", maximumRetryCounts=" + maximumRetryCounts + ", retryWaitMilliseconds="
				+ retryWaitMilliseconds + ", daoIndex=" + daoIndex + "]";
	}

	private synchronized String getTableName(String tableName) {
		if (StringUtils.isNotBlank(tableName)) {
			return tableName;
		} else {
			return TABLE_NAME;
		}
	}

	public synchronized int getBatchCreationSize() {
		return batchCreationSize;
	}

	public synchronized void createBatch(String ip, String queueName, List<HtmlFileNameClickHouseEntity> htmlFileNameClickHouseEntityList, String tableName)
			throws Exception {
		//long startTimestamp = System.currentTimeMillis();

		PreparedStatement preparedStatement = null;
		int index = 0;
		Connection connection = null;

		String creationSqlStatement = getCreationSqlStatement(tableName);
		//FormatUtils.getInstance().logMemoryUsage("createBatch() creationSqlStatement=" + creationSqlStatement);

		for (int i = 0; i < connectionList.size(); i++) {
			connection = connectionList.get(i);
			preparedStatement = connection.prepareStatement(creationSqlStatement);
			for (HtmlFileNameClickHouseEntity htmlFileNameClickHouseEntity : htmlFileNameClickHouseEntityList) {

				index = 1;

				// domain_id
				preparedStatement.setInt(index++, htmlFileNameClickHouseEntity.getDomainId());

				// url
				preparedStatement.setString(index++, htmlFileNameClickHouseEntity.getUrl());

				// track_date
				preparedStatement.setDate(index++, new java.sql.Date(htmlFileNameClickHouseEntity.getTrackDate().getTime()));

				// crawl_timestamp
				preparedStatement.setTimestamp(index++, new java.sql.Timestamp(htmlFileNameClickHouseEntity.getCrawlTimestamp().getTime()));

				// file_name
				preparedStatement.setString(index++, htmlFileNameClickHouseEntity.getFileName());

				// url_hash (default to output of URLHash() ClickHouse function of original URL)

				// url_murmur_hash (default to output of murmurHash2_32() ClickHouse function of original URL)

				// sign
				preparedStatement.setInt(index++,
						htmlFileNameClickHouseEntity.getSign() != null ? htmlFileNameClickHouseEntity.getSign() : IConstants.CLICKHOUSE_SIGN_POSITIVE_1);

				preparedStatement.addBatch();
			}

			int retryCount = 0;
			//long startTimestamp = 0L;
			while (retryCount < maximumRetryCounts) {
				try {
					//startTimestamp = System.nanoTime();
					preparedStatement.executeBatch();
					retryCount = maximumRetryCounts;
				} catch (Exception e) {
					retryCount++;
					if (retryCount >= maximumRetryCounts) {
						e.printStackTrace();
						throw e;
					} else {
						if (StringUtils.containsIgnoreCase(e.getMessage(), IConstants.FAILED_TO_RESPOND) == false) {
							FormatUtils.getInstance().logMemoryUsage("createBatch() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
						}
						try {
							Thread.sleep(retryWaitMilliseconds);
						} catch (InterruptedException e1) {
							e1.printStackTrace();
						}
					}
				} finally {
					if (preparedStatement != null) {
						preparedStatement.closeOnCompletion();
					}
				}
			}
		}
		//try {
		//	FormatUtils.getInstance().logMemoryUsage("createBatch() ip=" + ip + ",queueName=" + queueName + ",htmlFileNameClickHouseEntityList.size()=" + htmlFileNameClickHouseEntityList.size()
		//			+ ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		//} catch (Exception e) {
		//	e.printStackTrace();
		//}
	}

	private synchronized String getCreationSqlStatement(String tableName) {
		String response = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" insert into " + getTableName(tableName) + " ");
		stringBuilder.append(" (");
		stringBuilder.append("	domain_id,");
		stringBuilder.append("	url,");
		stringBuilder.append("	track_date,");
		stringBuilder.append("	crawl_timestamp,");
		stringBuilder.append("	file_name,");
		stringBuilder.append("	sign");
		stringBuilder.append(" )");
		stringBuilder.append(" values ");
		stringBuilder.append(" (");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?");
		stringBuilder.append(" )");
		stringBuilder.append(IConstants.SEMI_COLON);
		response = stringBuilder.toString();
		return response;
	}

	public HtmlFileNameClickHouseEntity get(Date trackDate, Integer domainId, String urlString, Date crawlTimestamp) throws Exception {
		HtmlFileNameClickHouseEntity htmlFileNameClickHouseEntity = null;
		String tableName = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		int index = 0;
		String trimmedString = StringUtils.trimToEmpty(urlString);
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("   file_name");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("     track_date = ?");
		stringBuilder.append(" and domain_id = ?");
		stringBuilder.append(" and url_hash = URLHash(?)");
		stringBuilder.append(" and url_murmur_hash = murmurHash2_32(?)");
		stringBuilder.append(" and crawl_timestamp = ?");

		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("get() sqlString=" + sqlString);
		connection = connectionList.get(0);
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);

				preparedStatement.setDate(index++, new java.sql.Date(trackDate.getTime()));
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setString(index++, trimmedString);
				preparedStatement.setString(index++, trimmedString);
				preparedStatement.setTimestamp(index++, new java.sql.Timestamp(crawlTimestamp.getTime()));
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					htmlFileNameClickHouseEntity = new HtmlFileNameClickHouseEntity();

					// file_name
					htmlFileNameClickHouseEntity.setFileName(resultSet.getString(IConstants.FILE_NAME));
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("get() e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			} finally {
				if (resultSet != null) {
					resultSet.close();
				}
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();
				}
			}
		}
		//FormatUtils.getInstance().logMemoryUsage("get() ends. startTrackDate="+startTrackDate +",endTrackDate="+endTrackDate +",urlString="+urlString
		//		+ ",htmlFileNameClickHouseEntityList.size()=" + htmlFileNameClickHouseEntityList.size()
		//		+ ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp)
		//);
		return htmlFileNameClickHouseEntity;
	}

}