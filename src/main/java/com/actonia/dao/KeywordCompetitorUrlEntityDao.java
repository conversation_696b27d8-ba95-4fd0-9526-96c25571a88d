package com.actonia.dao;

import java.util.ArrayList;
import java.util.List;

import com.actonia.entity.KeywordCompetitorUrlEntity;

public class KeywordCompetitorUrlEntityDao extends BaseJdbcSupport<KeywordCompetitorUrlEntity> {

	private static final int RECORDS_PER_SQL_STATEMENT = 10;
	private static final int SQL_STATEMENTS_PER_JDBC_CALL = 10;
	public static final int RECORDS_PER_BATCH_UPDATE = 100;
	private static final String SEMI_COLON = ";";
	private static final String INSERT_FORMAT_1 = "(?, ?, ?)";
	private static final String INSERT_FORMAT_2 = ",(?, ?, ?)";
	private static final int NUMBER_OF_FIELDS = 3;

	public String getTableName() {
		return "t_keyword_competitorurl";
	}

	public List<KeywordCompetitorUrlEntity> getByDomainId(int domainId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("select ");
		stringBuilder.append("    t_keyword_competitorurl.keyword_id as keywordId, ");
		stringBuilder.append("	  t_competitor_url.url as competitorUrl ");
		stringBuilder.append("from ");
		stringBuilder.append("    t_competitor_url t_competitor_url, ");
		stringBuilder.append("	  t_keyword_competitorurl t_keyword_competitorurl ");
		stringBuilder.append("where ");
		stringBuilder.append("    t_competitor_url.owndomain_id = ? ");
		stringBuilder.append("and t_competitor_url.id = t_keyword_competitorurl.competitorurl_id ");
		String sqlString = stringBuilder.toString();
		return findBySql(sqlString, domainId);

	}

	//https://www.wrike.com/open.htm?id=14548058
	public List<KeywordCompetitorUrlEntity> getByKeywordIdCompetitorUrlId(Long keywordId, Integer competitorUrlId) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * ");
		sql.append(" from ").append(getTableName());
		sql.append(" where keyword_id = ? ");
		sql.append(" and competitorurl_id = ? ");

		return this.findBySql(sql.toString(), keywordId, competitorUrlId);
	}

	public void insertMultiRowsBatch(List<KeywordCompetitorUrlEntity> keywordCompetitorUrlEntityList) {
		Object[] objectArray = null;
		List<Object[]> objectArrayList = new ArrayList<Object[]>();
		List<KeywordCompetitorUrlEntity> tempList = new ArrayList<KeywordCompetitorUrlEntity>();

		for (KeywordCompetitorUrlEntity keywordCompetitorUrlEntity : keywordCompetitorUrlEntityList) {
			tempList.add(keywordCompetitorUrlEntity);
			if (tempList.size() == RECORDS_PER_SQL_STATEMENT) {
				objectArray = getObjectArrayForInsert(tempList);
				objectArrayList.add(objectArray);
				if (objectArrayList.size() == SQL_STATEMENTS_PER_JDBC_CALL) {
					this.executeBatch(getInsertMultiRowsSqlStatement(RECORDS_PER_SQL_STATEMENT), objectArrayList);
					objectArrayList = new ArrayList<Object[]>();
				}
				tempList = new ArrayList<KeywordCompetitorUrlEntity>();
			}
		}

		if (objectArrayList != null && objectArrayList.size() > 0) {
			this.executeBatch(getInsertMultiRowsSqlStatement(RECORDS_PER_SQL_STATEMENT), objectArrayList);
			objectArrayList = new ArrayList<Object[]>();
		}

		if (tempList != null && tempList.size() > 0) {
			objectArray = getObjectArrayForInsert(tempList);
			objectArrayList.add(objectArray);
			if (objectArrayList != null && objectArrayList.size() > 0) {
				this.executeBatch(getInsertMultiRowsSqlStatement(tempList.size()), objectArrayList);
				objectArrayList = new ArrayList<Object[]>();
			}
		}
	}

	private Object[] getObjectArrayForInsert(List<KeywordCompetitorUrlEntity> list) {
		List<Object[]> tempObjectArrayList = new ArrayList<Object[]>();
		Object[] backlinkObjectArray = null;
		int totalNumberOfObjects = 0;
		for (KeywordCompetitorUrlEntity keywordCompetitorUrlEntity : list) {
			backlinkObjectArray = new Object[] { keywordCompetitorUrlEntity.getKeywordId(), keywordCompetitorUrlEntity.getCompetitorurlId(),
					keywordCompetitorUrlEntity.getAddBy(), };
			tempObjectArrayList.add(backlinkObjectArray);
		}
		totalNumberOfObjects = tempObjectArrayList.size() * NUMBER_OF_FIELDS;
		Object[] objectArray = new Object[totalNumberOfObjects];
		int idx = 0;
		for (Object[] tempObjectArray : tempObjectArrayList) {
			for (int i = 0; i < tempObjectArray.length; i++) {
				objectArray[idx++] = tempObjectArray[i];
			}
		}
		return objectArray;
	}

	private String getInsertMultiRowsSqlStatement(int recordsPerInsertStatement) {
		String response = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("insert into " + getTableName() + " ");
		stringBuilder.append("(");
		stringBuilder.append("	keyword_id,");
		stringBuilder.append("	competitorurl_id,");
		stringBuilder.append("	add_by");
		stringBuilder.append(")");
		stringBuilder.append("values ");
		String sqlString = stringBuilder.toString();
		StringBuffer sql = new StringBuffer();
		sql.append(sqlString);
		for (int i = 0; i < recordsPerInsertStatement; i++) {
			if (i == 0) {
				sql.append(INSERT_FORMAT_1);
			} else {
				sql.append(INSERT_FORMAT_2);
			}
		}
		sql.append(SEMI_COLON);
		response = sql.toString();
		return response;
	}
	
	public void deleteByKeywordIdAndCompetitorUrl(Long keywordId,Integer competitorUrlId) {
	    String sql = " delete from t_keyword_competitorurl where keyword_id = ? and competitorurl_id = ? limit 1";
	    this.executeUpdate(sql, keywordId,competitorUrlId);
	}

	public List<Integer> getCompetitorUrlIdNotExistList(int domainId) {
		List<Integer> recordIdList = new ArrayList<Integer>();
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("select ");
		stringBuilder.append("    t_keyword_competitorurl.id ");
		stringBuilder.append("from ");
		stringBuilder.append("	  t_keyword t_keyword, ");
		stringBuilder.append("	  t_keyword_competitorurl t_keyword_competitorurl ");
		stringBuilder.append("where ");
		stringBuilder.append("	  t_keyword.own_domain_id = ? ");
		stringBuilder.append("and t_keyword.rank_check = 1 ");
		stringBuilder.append("and t_keyword.id = t_keyword_competitorurl.keyword_id ");
		stringBuilder.append("and t_keyword_competitorurl.competitorurl_id not in  ");
		stringBuilder.append("( ");
		stringBuilder.append("select ");
		stringBuilder.append("	t_competitor_url.id ");
		stringBuilder.append("from ");
		stringBuilder.append("	t_competitor_url t_competitor_url ");
		stringBuilder.append("where ");
		stringBuilder.append("	t_competitor_url.owndomain_id = ? ");
		stringBuilder.append(") ");
		String sqlString = stringBuilder.toString();
		List<KeywordCompetitorUrlEntity> keywordCompetitorUrlEntityList = this.findBySql(sqlString, domainId, domainId);
		if (keywordCompetitorUrlEntityList != null && keywordCompetitorUrlEntityList.size() > 0) {
			for (KeywordCompetitorUrlEntity keywordCompetitorUrlEntity : keywordCompetitorUrlEntityList) {
				recordIdList.add(keywordCompetitorUrlEntity.getId());
			}
		}
		return recordIdList;
	}

	public void deleteByRecordId(int id) {
	    String sql = " delete from t_keyword_competitorurl where id = ?";
	    this.executeUpdate(sql, id);
	} 
}
