package com.actonia.dao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.lang.StringUtils;

import com.actonia.IConstants;
import com.actonia.utils.FormatUtils;

import ru.yandex.clickhouse.ClickHouseDataSource;
import ru.yandex.clickhouse.settings.ClickHouseProperties;

// Due to ClickHouse concurrency issues, use Singleton pattern, do not use Spring (ie. all methods must be 'synchronized')
public class LocalLinkClarityClickHouseDAO {

	//private static boolean isDebug = false;
	private static List<String> databaseHostnameList;
	private static String databasePort = null;
	private static String databaseName = null;
	private static List<Connection> connectionList;
	private static int batchCreationSize;
	private static String databaseUser = null;
	private static String databasePassword = null;
	private static List<String> connectionUrlList = null;
	private static int connectionTimeoutInMilliseconds;
	private static int maximumRetryCounts;
	private static int retryWaitMilliseconds;
	public static final String TABLE_NAME = "local_link_clarity";
	private static LocalLinkClarityClickHouseDAO localLinkClarityClickHouseDAO;

	public static LocalLinkClarityClickHouseDAO getInstance() throws Exception {
		if (localLinkClarityClickHouseDAO == null) {
			localLinkClarityClickHouseDAO = initializeLocalLinkClarityClickHouseDAO();
		}
		return localLinkClarityClickHouseDAO;
	}

	// initialize LocalLinkClarityClickHouseDAO based on runtime clickhouse configurations 
	private static LocalLinkClarityClickHouseDAO initializeLocalLinkClarityClickHouseDAO() throws Exception {
		FormatUtils.getInstance().logMemoryUsage("initializeLocalLinkClarityClickHouseDAO() begins.");

		LocalLinkClarityClickHouseDAO localLinkClarityClickHouseDAO = null;

		String clickHouseDatabaseHostnames = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CLICKHOUSE_CLUSTER_SERVERS);
		String[] clickHouseDatabaseHostnameArray = clickHouseDatabaseHostnames.split(IConstants.COMMA);
		String clickHouseDatabasePort = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_PORT);
		String clickHouseDatabaseName = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_NAME);
		String clickHouseDatabaseUser = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_USER);
		String clickHouseDatabasePassword = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_PASSWORD);
		int clickHouseBatchCreationSize = ClickHouseCrawlDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_BATCH_CREATION_SIZE, 100);
		int clickHouseconnectionTimeoutInMilliseconds = ClickHouseCrawlDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_CONNECTION_TIMEOUT_MILLISECONDS, 88888);
		int clickHouseMaximumRetryCounts = ClickHouseCrawlDatabaseConfigurations.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_MAX_RETRY_COUNTS,
				8);
		int clickHouseRetryWaitMilliseconds = ClickHouseCrawlDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_RETRY_WAIT_MILLISECONDS, 1000);

		try {
			localLinkClarityClickHouseDAO = new LocalLinkClarityClickHouseDAO(clickHouseDatabaseHostnameArray, clickHouseDatabasePort, clickHouseDatabaseName,
					clickHouseBatchCreationSize, clickHouseDatabaseUser, clickHouseDatabasePassword, clickHouseconnectionTimeoutInMilliseconds,
					clickHouseMaximumRetryCounts, clickHouseRetryWaitMilliseconds);
		} catch (Exception e) {
			e.printStackTrace();
		}
		FormatUtils.getInstance()
				.logMemoryUsage("initializeLocalLinkClarityClickHouseDAO() ends. localLinkClarityClickHouseDAO=" + localLinkClarityClickHouseDAO.toString());
		return localLinkClarityClickHouseDAO;
	}

	private LocalLinkClarityClickHouseDAO(String[] databaseHostnameArrayInput, String databasePortInput, String databaseNameInput, int batchCreationSizeInput,
			String databaseUserInput, String databasePasswordInput, int connectionTimeoutInMillisecondsInput, int maximumRetryCountsInput,
			int retryWaitMillisecondsInput) throws Exception {

		ClickHouseDataSource clickHouseDataSource = null;
		Connection connection = null;

		databaseHostnameList = Arrays.asList(databaseHostnameArrayInput);

		databasePort = databasePortInput;
		databaseName = databaseNameInput;
		batchCreationSize = batchCreationSizeInput;
		databaseUser = databaseUserInput;
		databasePassword = databasePasswordInput;
		connectionTimeoutInMilliseconds = connectionTimeoutInMillisecondsInput;
		maximumRetryCounts = maximumRetryCountsInput;
		retryWaitMilliseconds = retryWaitMillisecondsInput;

		FormatUtils.getInstance()
				.logMemoryUsage("LocalLinkClarityClickHouseDAO() databaseHostnameList=" + databaseHostnameList.toString() + ",databasePort=" + databasePort
						+ ",databaseName=" + databaseName + ",batchCreationSize=" + batchCreationSize + ",databaseUser=" + databaseUser + ",databasePassword="
						+ databasePassword + ",connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ",maximumRetryCounts=" + maximumRetryCounts
						+ ",retryWaitMilliseconds=" + retryWaitMilliseconds + ",TABLE_NAME=" + TABLE_NAME);

		ClickHouseProperties clickHouseProperties = new ClickHouseProperties();
		clickHouseProperties.setDecompress(true);
		clickHouseProperties.setConnectionTimeout(connectionTimeoutInMilliseconds);
		clickHouseProperties.setSocketTimeout(connectionTimeoutInMilliseconds);

		connectionList = new ArrayList<Connection>();

		String connectionUrl = null;

		connectionUrlList = new ArrayList<String>();

		for (String databaseHostname : databaseHostnameList) {
			if (StringUtils.isNotBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
						+ "?user=" + databaseUser + "&password=" + databasePassword;
			} else {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH
						+ databaseName;
			}
			connectionUrlList.add(connectionUrl);
			FormatUtils.getInstance().logMemoryUsage("LocalLinkClarityClickHouseDAO() connectionUrl=" + connectionUrl);
			clickHouseDataSource = new ClickHouseDataSource(connectionUrl, clickHouseProperties);
			connection = clickHouseDataSource.getConnection();
			connectionList.add(connection);
		}
	}

	@Override
	public String toString() {
		return "LocalLinkClarityClickHouseDAO [databaseHostnameList=" + databaseHostnameList.toString() + ", databasePort=" + databasePort + ", databaseName="
				+ databaseName + ", batchCreationSize=" + batchCreationSize + ", databaseUser=" + databaseUser + ", databasePassword=" + databasePassword
				+ ", connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ", maximumRetryCounts=" + maximumRetryCounts + ", retryWaitMilliseconds="
				+ retryWaitMilliseconds + "]";
	}

	private synchronized String getTableName(String tableName) {
		if (StringUtils.isNotBlank(tableName)) {
			return tableName;
		} else {
			return TABLE_NAME;
		}
	}

	public synchronized void optimizeOnePartition(Integer processYearMonth, String tableName) {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("optimizeOnePartition() begins. processYearMonth=" + processYearMonth + ",tableName=" + tableName);
		PreparedStatement preparedStatement = null;
		Connection connection = null;
		int retryCount = 0;
		String sqlStringTemplate = "OPTIMIZE TABLE " + getTableName(tableName) + " PARTITION %d FINAL";
		String sqlString = null;
		String connectionUrl = null;
		try {
			sqlString = String.format(sqlStringTemplate, processYearMonth);
			FormatUtils.getInstance().logMemoryUsage("optimizeOnePartition() sqlString=" + sqlString);
			for (int i = 0; i < connectionList.size(); i++) {
				connection = connectionList.get(i);
				connectionUrl = connectionUrlList.get(i);
				FormatUtils.getInstance().logMemoryUsage("optimizeOnePartition() connectionUrl=" + connectionUrl);
				retryCount = 0;
				while (retryCount < maximumRetryCounts) {
					try {
						preparedStatement = connection.prepareStatement(sqlString);
						preparedStatement.executeUpdate();
						retryCount = maximumRetryCounts;
					} catch (Exception e) {
						retryCount++;
						if (retryCount >= maximumRetryCounts) {
							e.printStackTrace();
						} else {
							FormatUtils.getInstance().logMemoryUsage("optimizeOnePartition() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
							try {
								Thread.sleep(retryWaitMilliseconds);
							} catch (InterruptedException e1) {
								e1.printStackTrace();
							}
						}
					} finally {
						if (preparedStatement != null) {
							preparedStatement.closeOnCompletion();
						}
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		FormatUtils.getInstance().logMemoryUsage("optimizeOnePartition() ends. processYearMonth=" + processYearMonth + ",tableName=" + tableName + ",elapsed(s.)="
				+ (System.currentTimeMillis() - startTimestamp) / 1000);
	}

}
