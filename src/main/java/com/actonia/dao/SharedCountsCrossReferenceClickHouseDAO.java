package com.actonia.dao;

import java.math.BigInteger;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

import com.actonia.IConstants;
import com.actonia.utils.FormatUtils;
import com.actonia.value.object.SharedCountsCrossReferenceValueObject;

import ru.yandex.clickhouse.ClickHouseDataSource;
import ru.yandex.clickhouse.settings.ClickHouseProperties;

public class SharedCountsCrossReferenceClickHouseDAO {

	private boolean isDebug = false;
	private List<String> databaseHostnameList;
	private String databasePort = null;
	private String databaseName = null;
	private List<Connection> connectionList;
	private int batchCreationSize;
	private String databaseUser = null;
	private String databasePassword = null;
	private List<String> connectionUrlList = null;
	private int connectionTimeoutInMilliseconds;
	private int maximumRetryCounts;
	private int retryWaitMilliseconds;
	private static final String TABLE_NAME = "url_to_domains_xref";
	private static int cachedConnectionIndex = 0;

	public SharedCountsCrossReferenceClickHouseDAO(String[] databaseHostnameArray, String databasePort, String databaseName, int batchCreationSize,
			String databaseUser, String databasePassword, int connectionTimeoutInMilliseconds, int maximumRetryCounts, int retryWaitMilliseconds)
			throws Exception {

		ClickHouseDataSource clickHouseDataSource = null;
		Connection connection = null;

		this.databaseHostnameList = Arrays.asList(databaseHostnameArray);

		FormatUtils.getInstance().logMemoryUsage("SharedCountsCrossReferenceClickHouseDAO() databaseHostnameList=" + databaseHostnameList.toString() + ",databasePort="
				+ databasePort + ",databaseName=" + databaseName + ",batchCreationSize=" + batchCreationSize + ",databaseUser=" + databaseUser
				+ ",databasePassword=" + databasePassword + ",connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds
				+ ",maximumRetryCounts=" + maximumRetryCounts + ",retryWaitMilliseconds=" + retryWaitMilliseconds + ",tableName=" + TABLE_NAME);

		this.databasePort = databasePort;
		this.databaseName = databaseName;
		this.batchCreationSize = batchCreationSize;
		this.databaseUser = databaseUser;
		this.databasePassword = databasePassword;
		this.connectionTimeoutInMilliseconds = connectionTimeoutInMilliseconds;
		this.maximumRetryCounts = maximumRetryCounts;
		this.retryWaitMilliseconds = retryWaitMilliseconds;

		ClickHouseProperties clickHouseProperties = new ClickHouseProperties();
		clickHouseProperties.setDecompress(true);
		clickHouseProperties.setConnectionTimeout(connectionTimeoutInMilliseconds);
		clickHouseProperties.setSocketTimeout(connectionTimeoutInMilliseconds);

		connectionList = new ArrayList<Connection>();

		String connectionUrl = null;

		connectionUrlList = new ArrayList<String>();

		for (String databaseHostname : databaseHostnameList) {
			if (StringUtils.isNotBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort
						+ IConstants.FORWARD_SLASH + databaseName + "?user=" + databaseUser + "&password=" + databasePassword;
			} else {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort
						+ IConstants.FORWARD_SLASH + databaseName;
			}
			connectionUrlList.add(connectionUrl);
			FormatUtils.getInstance().logMemoryUsage("SharedCountsCrossReferenceClickHouseDAO() connectionUrl=" + connectionUrl);
			clickHouseDataSource = new ClickHouseDataSource(connectionUrl, clickHouseProperties);
			connection = clickHouseDataSource.getConnection();
			connectionList.add(connection);
		}
	}

	private String getTableName() {
		return TABLE_NAME;
	}

	public int getBatchCreationSize() {
		return batchCreationSize;
	}

	public void createBatch(List<SharedCountsCrossReferenceValueObject> sharedCountsCrossReferenceValueObjectList) throws Exception {
		//long startTimestamp = 0L;

		PreparedStatement preparedStatement = null;
		int index = 0;
		Connection connection = null;
		String connectionUrl = null;

		String creationSqlStatement = getCreationSqlStatement();
		//FormatUtils.getInstance().logMemoryUsage("createBatch() creationSqlStatement=" + creationSqlStatement);

		for (int i = 0; i < connectionList.size(); i++) {
			connection = connectionList.get(i);
			if (isDebug == true) {
				connectionUrl = connectionUrlList.get(i);
				FormatUtils.getInstance().logMemoryUsage("SharedCountsCrossReferenceClickHouseDAO.createBatch() connectionUrl=" + connectionUrl);
			}
			preparedStatement = connection.prepareStatement(creationSqlStatement);
			for (SharedCountsCrossReferenceValueObject sharedCountsCrossReferenceValueObject : sharedCountsCrossReferenceValueObjectList) {
				index = 1;
				preparedStatement.setDate(index++, new java.sql.Date(sharedCountsCrossReferenceValueObject.getTrackDate().getTime()));
				preparedStatement.setString(index++, sharedCountsCrossReferenceValueObject.getUrl());
				preparedStatement.setArray(index++, connection.createArrayOf("String", sharedCountsCrossReferenceValueObject.getDomainIds()));
				preparedStatement.setInt(index++, sharedCountsCrossReferenceValueObject.getSign());
				preparedStatement.addBatch();
			}

			int retryCount = 0;
			while (retryCount < maximumRetryCounts) {
				try {
					//startTimestamp = System.nanoTime();
					preparedStatement.executeBatch();
					retryCount = maximumRetryCounts;
				} catch (Exception e) {
					retryCount++;
					if (retryCount >= maximumRetryCounts) {
						e.printStackTrace();
						throw e;
					} else {
						FormatUtils.getInstance().logMemoryUsage(
								"SharedCountsCrossReferenceClickHouseDAO.createBatch() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
						try {
							Thread.sleep(retryWaitMilliseconds);
						} catch (InterruptedException e1) {
							e1.printStackTrace();
						}
					}
				}
			}
		}
		//FormatUtils.getInstance().logMemoryUsage("createBatch() sharedCountsCrossReferenceValueObjectList.size()=" + sharedCountsCrossReferenceValueObjectList.size()
		//		+ ",elapsed(ns.)=" + (System.nanoTime() - startTimestamp));
	}

	private String getCreationSqlStatement() {
		String response = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" insert into " + getTableName() + " ");
		stringBuilder.append(" (");
		stringBuilder.append(" track_date,");
		stringBuilder.append(" url,");
		stringBuilder.append(" domain_ids,");
		stringBuilder.append(" sign");
		stringBuilder.append(" )");
		stringBuilder.append(" values ");
		stringBuilder.append(" (");
		stringBuilder.append(" ?,");
		stringBuilder.append(" ?,");
		stringBuilder.append(" ?,");
		stringBuilder.append(" ?");
		stringBuilder.append(" )");
		stringBuilder.append(IConstants.SEMI_COLON);
		response = stringBuilder.toString();
		return response;
	}

	@Override
	public String toString() {
		return "SharedCountsCrossReferenceClickHouseDAO [isDebug=" + isDebug + ", databaseHostnameList=" + databaseHostnameList.toString()
				+ ", databasePort=" + databasePort + ", databaseName=" + databaseName + ", batchCreationSize=" + batchCreationSize + ", databaseUser="
				+ databaseUser + ", databasePassword=" + databasePassword + ", connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds
				+ ", maximumRetryCounts=" + maximumRetryCounts + ", retryWaitMilliseconds=" + retryWaitMilliseconds + "]";
	}

	public List<SharedCountsCrossReferenceValueObject> getList(Date trackDate, Integer limit) throws Exception {
		long startTimestamp = System.currentTimeMillis();

		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;

		List<SharedCountsCrossReferenceValueObject> sharedCountsCrossReferenceValueObjectList = new ArrayList<SharedCountsCrossReferenceValueObject>();
		SharedCountsCrossReferenceValueObject sharedCountsCrossReferenceValueObject = null;
		PreparedStatement preparedStatement = null;

		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append(" 	   track_date,");
		stringBuilder.append(" 	   url,");
		stringBuilder.append(" 	   url_hash,");
		stringBuilder.append(" 	   domain_ids");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append(" 	   track_date = ?");
		stringBuilder.append(" group by");
		stringBuilder.append(" 	   track_date,");
		stringBuilder.append(" 	   url,");
		stringBuilder.append(" 	   url_hash,");
		stringBuilder.append(" 	   domain_ids");
		stringBuilder.append(" having");
		stringBuilder.append(" 	   sum(sign) > 0");
		if (limit != null && limit.intValue() > 0) {
			stringBuilder.append(" limit " + limit);
		}
		String sqlString = stringBuilder.toString();
		String connectionUrl = null;
		connection = connectionList.get(getConnectionIndex());
		if (isDebug == true) {
			connectionUrl = connectionUrlList.get(getConnectionIndex());
			FormatUtils.getInstance().logMemoryUsage("SharedCountsCrossReferenceClickHouseDAO.getList() connectionUrl=" + connectionUrl);
		}
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				sharedCountsCrossReferenceValueObjectList = new ArrayList<SharedCountsCrossReferenceValueObject>();
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setDate(1, new java.sql.Date(trackDate.getTime()));
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					sharedCountsCrossReferenceValueObject = new SharedCountsCrossReferenceValueObject();

					// TrackDate
					sharedCountsCrossReferenceValueObject.setTrackDate(resultSet.getDate(IConstants.CLICKHOUSE_FLD_TRACK_DATE));

					// Url
					sharedCountsCrossReferenceValueObject.setUrl(resultSet.getString(IConstants.CLICKHOUSE_FLD_URL));

					// CityHashUrl
					sharedCountsCrossReferenceValueObject.setUrlHash(new BigInteger(resultSet.getString(IConstants.CLICKHOUSE_FLD_URL_HASH)));

					// DomainIds
					sharedCountsCrossReferenceValueObject.setDomainIds((Object[]) resultSet.getArray(IConstants.CLICKHOUSE_FLD_DOMAIN_IDS).getArray());

					if (isDebug == true) {
						sharedCountsCrossReferenceValueObject.setConnectionUrl(connectionUrl);
					}

					sharedCountsCrossReferenceValueObjectList.add(sharedCountsCrossReferenceValueObject);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage(
							"SharedCountsCrossReferenceClickHouseDAO.getList() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("SharedCountsCrossReferenceClickHouseDAO.getList() sharedCountsCrossReferenceValueObjectList.size()="
				+ sharedCountsCrossReferenceValueObjectList.size() + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		return sharedCountsCrossReferenceValueObjectList;
	}

	public List<BigInteger> getCityHashUrlByTrackDate(Date trackDate, Integer limit) throws Exception {
		long startTimestamp = System.currentTimeMillis();

		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		BigInteger cityHashUrl = null;
		List<BigInteger> cityHashUrlList = new ArrayList<BigInteger>();

		PreparedStatement preparedStatement = null;

		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append(" 	   url_hash");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append(" 	   track_date = ?");
		stringBuilder.append(" group by");
		stringBuilder.append(" 	   track_date,");
		stringBuilder.append(" 	   url,");
		stringBuilder.append(" 	   url_hash,");
		stringBuilder.append(" 	   domain_ids");
		stringBuilder.append(" having");
		stringBuilder.append(" 	   sum(sign) > 0");
		if (limit != null && limit.intValue() > 0) {
			stringBuilder.append(" limit " + limit);
		}
		String sqlString = stringBuilder.toString();
		FormatUtils.getInstance().logMemoryUsage("SharedCountsCrossReferenceClickHouseDAO.getCityHashUrlByTrackDate() sqlString=" + sqlString);
		connection = connectionList.get(getConnectionIndex());
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setDate(1, new java.sql.Date(trackDate.getTime()));
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					cityHashUrl = new BigInteger(resultSet.getString(IConstants.CLICKHOUSE_FLD_URL_HASH));
					cityHashUrlList.add(cityHashUrl);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("SharedCountsCrossReferenceClickHouseDAO.getCityHashUrlByTrackDate() exception message=" + e.getMessage()
							+ ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("SharedCountsCrossReferenceClickHouseDAO.getCityHashUrlByTrackDate() cityHashUrlList.size()=" + cityHashUrlList.size()
				+ ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		return cityHashUrlList;
	}

	public SharedCountsCrossReferenceValueObject get(Date trackDate, String urlString) throws Exception {

		//FormatUtils.getInstance().logMemoryUsage("SharedCountsCrossReferenceClickHouseDAO.get() trackDate=" + trackDate + ",urlString=" + urlString);

		//long startTimestamp = System.nanoTime();

		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;

		//PreparedStatement statement = connection.prepareStatement(sqlString);

		SharedCountsCrossReferenceValueObject sharedCountsCrossReferenceValueObject = null;

		// TrackDate, CityHashUrl
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append(" 	   track_date,");
		stringBuilder.append(" 	   url,");
		stringBuilder.append(" 	   url_hash,");
		stringBuilder.append(" 	   domain_ids");
		stringBuilder.append(" from");
		stringBuilder.append(" 	   url_to_domains_xref");
		stringBuilder.append(" where");
		stringBuilder.append(" 	   track_date = ?");
		stringBuilder.append(" and url_hash = URLHash(?)");
		stringBuilder.append(" group by");
		stringBuilder.append(" 	   track_date,");
		stringBuilder.append(" 	   url,");
		stringBuilder.append(" 	   url_hash,");
		stringBuilder.append(" 	   domain_ids");
		stringBuilder.append(" having");
		stringBuilder.append(" 	   sum(sign) > 0");
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("SharedCountsCrossReferenceClickHouseDAO.get() sqlString=" + sqlString);
		connection = connectionList.get(getConnectionIndex());
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				preparedStatement = connection.prepareStatement(sqlString);
				//preparedStatement.setDate(1, java.sql.Date.valueOf("2017-06-25"));
				preparedStatement.setDate(1, new java.sql.Date(trackDate.getTime()));
				preparedStatement.setString(2, urlString);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {

					sharedCountsCrossReferenceValueObject = new SharedCountsCrossReferenceValueObject();

					// TrackDate
					sharedCountsCrossReferenceValueObject.setTrackDate(resultSet.getDate(IConstants.CLICKHOUSE_FLD_TRACK_DATE));

					// Url
					sharedCountsCrossReferenceValueObject.setUrl(resultSet.getString(IConstants.CLICKHOUSE_FLD_URL));

					// CityHashUrl
					sharedCountsCrossReferenceValueObject.setUrlHash(new BigInteger(resultSet.getString(IConstants.CLICKHOUSE_FLD_URL_HASH)));

					// DomainIds
					sharedCountsCrossReferenceValueObject.setDomainIds((Object[]) resultSet.getArray(IConstants.CLICKHOUSE_FLD_DOMAIN_IDS).getArray());
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage(
							"SharedCountsCrossReferenceClickHouseDAO.get() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		//FormatUtils.getInstance().logMemoryUsage("SharedCountsCrossReferenceClickHouseDAO.get() cityHashUrl=" + cityHashUrl + ",trackDate="
		//		+ DateFormatUtils.getInstance().format(trackDate, IConstants.DATE_FORMAT_YYYY_MM_DD) + ",urlString=" + urlString + ",elapsed(ns.)="
		//		+ (System.nanoTime() - startTimestamp));
		return sharedCountsCrossReferenceValueObject;
	}

	public BigInteger calculateUrlHash(String inputString) throws Exception {

		BigInteger urlHash = null;

		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;

		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append(" 	   URLHash(?) as URLHash");
		String sqlString = stringBuilder.toString();
		connection = connectionList.get(getConnectionIndex());
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setString(1, inputString);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {

					// URLHash
					urlHash = new BigInteger(resultSet.getString("URLHash"));
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage(
							"SharedCountsCrossReferenceClickHouseDAO.calculateUrlHash() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		return urlHash;
	}

	public Map<String, BigInteger> calculateUrlHash(List<String> inputStringList) throws Exception {

		Map<String, BigInteger> urlStringCityHashMap = new HashMap<String, BigInteger>();
		BigInteger urlHash = null;
		int MAX_URL_HASH_PER_QUERY = 8;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		int index = 0;
		int inputListIndex = 0;
		String inputString = null;
		List<String> testUrlStringList = new ArrayList<String>();
		String urlString = null;
		int urlIndex = 0;

		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append(" 	   URLHash(?) as UrlHash1,");
		stringBuilder.append(" 	   URLHash(?) as UrlHash2,");
		stringBuilder.append(" 	   URLHash(?) as UrlHash3,");
		stringBuilder.append(" 	   URLHash(?) as UrlHash4,");
		stringBuilder.append(" 	   URLHash(?) as UrlHash5,");
		stringBuilder.append(" 	   URLHash(?) as UrlHash6,");
		stringBuilder.append(" 	   URLHash(?) as UrlHash7,");
		stringBuilder.append(" 	   URLHash(?) as UrlHash8");
		String sqlString = stringBuilder.toString();
		connection = connectionList.get(getConnectionIndex());
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				preparedStatement = connection.prepareStatement(sqlString);

				while (inputListIndex < inputStringList.size()) {
					for (int i = 0; i < MAX_URL_HASH_PER_QUERY; i++) {
						if (inputListIndex < inputStringList.size()) {
							inputString = inputStringList.get(inputListIndex++);
						}
						index = i + 1;
						preparedStatement.setString(index, inputString);
						testUrlStringList.add(inputString);
					}
					resultSet = preparedStatement.executeQuery();
					urlIndex = 0;

					while (resultSet.next()) {

						// URLHash1
						urlHash = new BigInteger(resultSet.getString("UrlHash1"));
						urlString = testUrlStringList.get(urlIndex++);
						urlStringCityHashMap.put(urlString, urlHash);

						// URLHash2
						urlHash = new BigInteger(resultSet.getString("UrlHash2"));
						urlString = testUrlStringList.get(urlIndex++);
						urlStringCityHashMap.put(urlString, urlHash);

						// URLHash3
						urlHash = new BigInteger(resultSet.getString("UrlHash3"));
						urlString = testUrlStringList.get(urlIndex++);
						urlStringCityHashMap.put(urlString, urlHash);

						// URLHash4
						urlHash = new BigInteger(resultSet.getString("UrlHash4"));
						urlString = testUrlStringList.get(urlIndex++);
						urlStringCityHashMap.put(urlString, urlHash);

						// URLHash5
						urlHash = new BigInteger(resultSet.getString("UrlHash5"));
						urlString = testUrlStringList.get(urlIndex++);
						urlStringCityHashMap.put(urlString, urlHash);

						// URLHash6
						urlHash = new BigInteger(resultSet.getString("UrlHash6"));
						urlString = testUrlStringList.get(urlIndex++);
						urlStringCityHashMap.put(urlString, urlHash);

						// URLHash7
						urlHash = new BigInteger(resultSet.getString("UrlHash7"));
						urlString = testUrlStringList.get(urlIndex++);
						urlStringCityHashMap.put(urlString, urlHash);

						// URLHash8
						urlHash = new BigInteger(resultSet.getString("UrlHash8"));
						urlString = testUrlStringList.get(urlIndex++);
						urlStringCityHashMap.put(urlString, urlHash);

					}
					testUrlStringList = new ArrayList<String>();
					index = 0;
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage(
							"SharedCountsCrossReferenceClickHouseDAO.calculateUrlHash() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		return urlStringCityHashMap;
	}

	private synchronized int getConnectionIndex() {
		int connectionIndex = 0;
		if (connectionList != null && connectionList.size() > 0) {
			if (cachedConnectionIndex >= connectionList.size()) {
				cachedConnectionIndex = 0;
			}
			connectionIndex = cachedConnectionIndex++;
		}
		return connectionIndex;
	}
}
