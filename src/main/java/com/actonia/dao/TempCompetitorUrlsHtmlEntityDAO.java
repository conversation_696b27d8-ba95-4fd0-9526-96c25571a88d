package com.actonia.dao;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import com.actonia.IConstants;
import com.actonia.entity.AssociatedUrlEntity;
import com.actonia.entity.AssociatedUrlEntityComparator;
import com.actonia.utils.FormatUtils;

public class TempCompetitorUrlsHtmlEntityDAO extends BaseJdbcSupport<AssociatedUrlEntity> {

	//private boolean isDebug = false;

	private boolean isCacheMemory = true; //debug

	private static final int NUMBER_OF_FIELDS = 6;

	// map key = reversed root domain name
	// map value = map of hashcode to AssociatedUrlEntity	
	private static ConcurrentHashMap<String, ConcurrentHashMap<String, AssociatedUrlEntity>> hostnameHashCodeAssociatedUrlEntityMapMap = new ConcurrentHashMap<String, ConcurrentHashMap<String, AssociatedUrlEntity>>();

	@Override
	public String getTableName() {
		return "temp_competitor_urls_html";
	}

	public void resetCache() {
		if (isCacheMemory == false) {
			return;
		}
		hostnameHashCodeAssociatedUrlEntityMapMap = new ConcurrentHashMap<String, ConcurrentHashMap<String, AssociatedUrlEntity>>();
	}

	public void resetTable() {

		if (isCacheMemory == true) {
			return;
		}

		String sqlString = null;
		sqlString = "drop table if exists " + getTableName();
		FormatUtils.getInstance().logMemoryUsage("resetTable() sqlString=" + sqlString);
		this.executeUpdate(sqlString);
		sqlString = "create table if not exists " + getTableName() + " like template_temp_competitor_urls_html;";
		FormatUtils.getInstance().logMemoryUsage("resetTable() sqlString=" + sqlString);
		this.executeUpdate(sqlString);
	}

	public void insertMultiRowsBatch(List<AssociatedUrlEntity> inputList) {

		if (isCacheMemory == true) {
			insertToCache(inputList);
			return;
		}

		Object[] objectArray = null;
		String hostname = null;
		String hashCode = null;
		List<Object[]> objectArrayList = new ArrayList<Object[]>();
		List<AssociatedUrlEntity> tempList = new ArrayList<AssociatedUrlEntity>();
		AssociatedUrlEntity associatedUrlEntityExisting = null;
		nextAssociatedUrlEntity: for (AssociatedUrlEntity associatedUrlEntity : inputList) {
			hostname = associatedUrlEntity.getHostname();
			hashCode = associatedUrlEntity.getHashCode();
			associatedUrlEntityExisting = get(hostname, hashCode);
			if (associatedUrlEntityExisting != null) {
				continue nextAssociatedUrlEntity;
			}
			tempList.add(associatedUrlEntity);
			if (tempList.size() == IConstants.RECORDS_PER_SQL_STATEMENT) {
				objectArray = getObjectArrayForInsert(tempList);
				objectArrayList.add(objectArray);
				if (objectArrayList.size() == IConstants.SQL_STATEMENTS_PER_JDBC_CALL) {
					this.executeBatch(getInsertMultiRowsSqlStatement(IConstants.RECORDS_PER_SQL_STATEMENT), objectArrayList);
					objectArrayList = new ArrayList<Object[]>();
				}
				tempList = new ArrayList<AssociatedUrlEntity>();
			}
		}
		if (objectArrayList != null && objectArrayList.size() > 0) {
			this.executeBatch(getInsertMultiRowsSqlStatement(IConstants.RECORDS_PER_SQL_STATEMENT), objectArrayList);
			objectArrayList = new ArrayList<Object[]>();
		}
		if (tempList != null && tempList.size() > 0) {
			objectArray = getObjectArrayForInsert(tempList);
			objectArrayList.add(objectArray);
			if (objectArrayList != null && objectArrayList.size() > 0) {
				this.executeBatch(getInsertMultiRowsSqlStatement(tempList.size()), objectArrayList);
				objectArrayList = new ArrayList<Object[]>();
			}
		}
	}

	private Object[] getObjectArrayForInsert(List<AssociatedUrlEntity> list) {
		List<Object[]> tempObjectArrayList = new ArrayList<Object[]>();
		Object[] associatedUrlObjectArray = null;
		int totalNumberOfObjects = 0;
		for (AssociatedUrlEntity associatedUrlEntity : list) {
			associatedUrlObjectArray = new Object[] { associatedUrlEntity.getHashCode(), associatedUrlEntity.getHostname(), associatedUrlEntity.getUrl(),
					associatedUrlEntity.getTrackDate(), associatedUrlEntity.getDomainIdLanguageCodeJson(), associatedUrlEntity.getProtocol(), };
			tempObjectArrayList.add(associatedUrlObjectArray);
		}

		totalNumberOfObjects = tempObjectArrayList.size() * NUMBER_OF_FIELDS;

		Object[] objectArray = new Object[totalNumberOfObjects];
		int idx = 0;
		for (Object[] tempObjectArray : tempObjectArrayList) {
			for (int i = 0; i < tempObjectArray.length; i++) {
				objectArray[idx++] = tempObjectArray[i];
			}
		}
		return objectArray;
	}

	private String getInsertMultiRowsSqlStatement(int recordsPerInsertStatement) {
		String response = null;
		String sqlString = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" insert into " + getTableName());
		stringBuilder.append(" (");
		stringBuilder.append("	hash_code,");
		stringBuilder.append("	hostname,");
		stringBuilder.append("	url,");
		stringBuilder.append("	track_date,");
		stringBuilder.append("	domain_id_language_code_json,");
		stringBuilder.append("	protocol");
		stringBuilder.append(" )");
		stringBuilder.append(" values");
		sqlString = stringBuilder.toString();

		StringBuilder sql = new StringBuilder();
		sql.append(sqlString);

		for (int i = 0; i < recordsPerInsertStatement; i++) {
			if (i == 0) {
				sql.append(" (?,?,?,?,?,?)");
			} else {
				sql.append(" ,(?,?,?,?,?,?)");
			}
		}
		sql.append(IConstants.SEMI_COLON);
		response = sql.toString();
		return response;
	}

	public AssociatedUrlEntity get(String hostname, String hashCode) {

		if (isCacheMemory == true) {
			return getFromCache(hostname, hashCode);
		}

		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append(" *");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("   hash_code = ?");
		String sqlString = stringBuilder.toString();
		return findObject(sqlString, hashCode);
	}

	public List<AssociatedUrlEntity> getTotalUrlsByHostname(int trackDateNumber) {

		if (isCacheMemory == true) {
			return getTotalUrlsByHostnameFromCache();
		}

		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("   hostname,");
		stringBuilder.append("   count(*) as totalUrls");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("   track_date = ?");
		stringBuilder.append(" group by");
		stringBuilder.append("   hostname");
		stringBuilder.append(" order by");
		stringBuilder.append("   totalUrls desc");
		String sqlString = stringBuilder.toString();
		return findBySql(sqlString, trackDateNumber);
	}

	public List<AssociatedUrlEntity> getUrlsByHostname(String hostname, int trackDateNumber) {

		if (isCacheMemory == true) {
			return getUrlsByHostnameFromCache(hostname);
		}

		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("   hostname,");
		stringBuilder.append("   url,");
		stringBuilder.append("   domain_id_language_code_json");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     hostname = ?");
		stringBuilder.append(" and track_date = ?");
		String sqlString = stringBuilder.toString();
		return findBySql(sqlString, hostname, trackDateNumber);
	}

	public ConcurrentMap<String, Integer> getAllHashCodes() {
		ConcurrentMap<String, Integer> concurrentMap = new ConcurrentHashMap<String, Integer>();
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("   hash_code,");
		stringBuilder.append("   protocol");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		String sqlString = stringBuilder.toString();
		List<AssociatedUrlEntity> outputList = findBySql(sqlString);
		if (outputList != null && outputList.size() > 0) {
			for (AssociatedUrlEntity associatedUrlEntity : outputList) {
				if (associatedUrlEntity.getProtocol() != null) {
					concurrentMap.put(associatedUrlEntity.getHashCode(), associatedUrlEntity.getProtocol());
				} else {
					concurrentMap.put(associatedUrlEntity.getHashCode(), IConstants.UNKNOWN_PROTOCOL);
				}
			}
		}
		return concurrentMap;
	}

	public int updateDomainIdLanguageCodeJson(String hostname, String hashCode, String domainIdLanguageCodeJson) {

		if (isCacheMemory == true) {
			return updateDomainIdLanguageCodeJsonInCache(hostname, hashCode, domainIdLanguageCodeJson);
		}

		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" update " + getTableName());
		stringBuilder.append(" set");
		stringBuilder.append("   domain_id_language_code_json = ?");
		stringBuilder.append(" where");
		stringBuilder.append("   hash_code = ?");
		String sqlString = stringBuilder.toString();
		//FormatUtils
		//		.logMemoryUsage("updateUrl() hashCode=" + hashCode + ",urlString=" + urlString + ",protocol=" + protocol + ",trackDate=" + trackDate);
		return this.executeUpdate(sqlString, domainIdLanguageCodeJson, hashCode);
	}

	private void insertToCache(List<AssociatedUrlEntity> inputList) {
		String hostname = null;
		String hashCode = null;
		ConcurrentHashMap<String, AssociatedUrlEntity> hashCodeAssociatedUrlEntityMap = null;
		AssociatedUrlEntity associatedUrlEntityToBeCreated = null;
		for (AssociatedUrlEntity associatedUrlEntity : inputList) {
			hostname = associatedUrlEntity.getHostname();
			if (hostnameHashCodeAssociatedUrlEntityMapMap.containsKey(hostname) == true) {
				hashCodeAssociatedUrlEntityMap = hostnameHashCodeAssociatedUrlEntityMapMap.get(hostname);
			} else {
				hashCodeAssociatedUrlEntityMap = new ConcurrentHashMap<String, AssociatedUrlEntity>();
			}
			hashCode = associatedUrlEntity.getHashCode();
			if (hashCodeAssociatedUrlEntityMap.containsKey(hashCode) == false) {
				associatedUrlEntityToBeCreated = new AssociatedUrlEntity();
				associatedUrlEntityToBeCreated.setUrl(associatedUrlEntity.getUrl());
				associatedUrlEntityToBeCreated.setDomainIdLanguageCodeJson(associatedUrlEntity.getDomainIdLanguageCodeJson());
				associatedUrlEntityToBeCreated.setProtocol(associatedUrlEntity.getProtocol());
				hashCodeAssociatedUrlEntityMap.put(hashCode, associatedUrlEntityToBeCreated);
				hostnameHashCodeAssociatedUrlEntityMapMap.put(hostname, hashCodeAssociatedUrlEntityMap);
			}
		}
		//if (isDebug == true) {
		//	for (AssociatedUrlEntity associatedUrlEntity : inputList) {
		//		FormatUtils.getInstance().logMemoryUsage("insertToCache() associatedUrlEntity=" + associatedUrlEntity.toString());
		//	}
		//}
	}

	private AssociatedUrlEntity getFromCache(String hostname, String hashCode) {
		AssociatedUrlEntity associatedUrlEntity = null;
		if (hostnameHashCodeAssociatedUrlEntityMapMap.containsKey(hostname) == true) {
			if (hostnameHashCodeAssociatedUrlEntityMapMap.get(hostname).containsKey(hashCode) == true) {
				associatedUrlEntity = hostnameHashCodeAssociatedUrlEntityMapMap.get(hostname).get(hashCode);
				associatedUrlEntity.setHostname(hostname);
				associatedUrlEntity.setHashCode(hashCode);
			}
		}
		//if (isDebug == true) {
		//	if (associatedUrlEntity != null) {
		//		FormatUtils.getInstance()
		//				.logMemoryUsage("getFromCache() hostname=" + hostname + ",hashCode=" + hashCode + ",associatedUrlEntity=" + associatedUrlEntity.toString());
		//	} else {
		//		FormatUtils.getInstance().logMemoryUsage("getFromCache() hostname=" + hostname + ",hashCode=" + hashCode + ",associatedUrlEntity is null.");
		//	}
		//}
		return associatedUrlEntity;
	}

	// retrieve:
	// hostname
	// totalUrls
	private List<AssociatedUrlEntity> getTotalUrlsByHostnameFromCache() {
		List<AssociatedUrlEntity> associatedUrlEntityList = new ArrayList<AssociatedUrlEntity>();
		AssociatedUrlEntity associatedUrlEntity = null;
		for (String hostname : hostnameHashCodeAssociatedUrlEntityMapMap.keySet()) {
			associatedUrlEntity = new AssociatedUrlEntity();
			associatedUrlEntity.setHostname(hostname);
			associatedUrlEntity.setTotalUrls(hostnameHashCodeAssociatedUrlEntityMapMap.get(hostname).size());
			associatedUrlEntityList.add(associatedUrlEntity);
		}
		Collections.sort(associatedUrlEntityList, new AssociatedUrlEntityComparator());
		//if (isDebug == true) {
		//	for (AssociatedUrlEntity testAssociatedUrlEntity : associatedUrlEntityList) {
		//		FormatUtils.getInstance().logMemoryUsage("getTotalUrlsByHostnameFromCache() associatedUrlEntity=" + testAssociatedUrlEntity.toString());
		//	}
		//}
		return associatedUrlEntityList;
	}

	// retrieve:
	// hostname
	// url
	// domain_id_language_code_json
	private List<AssociatedUrlEntity> getUrlsByHostnameFromCache(String hostname) {
		List<AssociatedUrlEntity> associatedUrlEntityList = new ArrayList<AssociatedUrlEntity>();
		AssociatedUrlEntity testAssociatedUrlEntity = null;
		AssociatedUrlEntity associatedUrlEntity = null;
		Map<String, AssociatedUrlEntity> hashCodeAssociatedUrlEntityMap = null;
		if (hostnameHashCodeAssociatedUrlEntityMapMap.containsKey(hostname) == true) {
			hashCodeAssociatedUrlEntityMap = hostnameHashCodeAssociatedUrlEntityMapMap.get(hostname);
			if (hashCodeAssociatedUrlEntityMap != null && hashCodeAssociatedUrlEntityMap.size() > 0) {
				for (String hashCode : hashCodeAssociatedUrlEntityMap.keySet()) {
					testAssociatedUrlEntity = hashCodeAssociatedUrlEntityMap.get(hashCode);
					associatedUrlEntity = new AssociatedUrlEntity();
					associatedUrlEntity.setHostname(hostname);
					associatedUrlEntity.setUrl(testAssociatedUrlEntity.getUrl());
					associatedUrlEntity.setDomainIdLanguageCodeJson(testAssociatedUrlEntity.getDomainIdLanguageCodeJson());
					associatedUrlEntityList.add(associatedUrlEntity);
				}
			}
		}
		//if (isDebug == true) {
		//	for (AssociatedUrlEntity testAssociatedUrlEntity3 : associatedUrlEntityList) {
		//		FormatUtils.getInstance().logMemoryUsage("getUrlsByHostnameFromCache() associatedUrlEntity=" + testAssociatedUrlEntity3.toString());
		//	}
		//}
		return associatedUrlEntityList;
	}

	// update 'domain_id_language_code_json' by 'hash_code'
	private int updateDomainIdLanguageCodeJsonInCache(String hostname, String hashCode, String domainIdLanguageCodeJson) {
		int recordsUpdated = 0;
		AssociatedUrlEntity associatedUrlEntity = null;
		if (hostnameHashCodeAssociatedUrlEntityMapMap.containsKey(hostname) == true) {
			if (hostnameHashCodeAssociatedUrlEntityMapMap.get(hostname).containsKey(hashCode) == true) {
				associatedUrlEntity = hostnameHashCodeAssociatedUrlEntityMapMap.get(hostname).get(hashCode);
				associatedUrlEntity.setDomainIdLanguageCodeJson(domainIdLanguageCodeJson);
				hostnameHashCodeAssociatedUrlEntityMapMap.get(hostname).put(hashCode, associatedUrlEntity);
				//if (isDebug == true) {
				//	FormatUtils.getInstance().logMemoryUsage("updateDomainIdLanguageCodeJsonInCache() associatedUrlEntity=" + associatedUrlEntity.toString());
				//}
				recordsUpdated = 1;
			} else {
				//if (isDebug == true) {
				//	FormatUtils.getInstance().logMemoryUsage("updateDomainIdLanguageCodeJsonInCache() associatedUrlEntity not updated.");
				//}
			}
		}
		return recordsUpdated;
	}

	public void removeFromCache(String hostname) {

		if (isCacheMemory == false) {
			return;
		}

		if (hostnameHashCodeAssociatedUrlEntityMapMap.containsKey(hostname) == true) {
			hostnameHashCodeAssociatedUrlEntityMapMap.put(hostname, new ConcurrentHashMap<String, AssociatedUrlEntity>());
			hostnameHashCodeAssociatedUrlEntityMapMap.remove(hostname);
		}
	}

}