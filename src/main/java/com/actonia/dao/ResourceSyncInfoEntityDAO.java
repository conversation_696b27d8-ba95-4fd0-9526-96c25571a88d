package com.actonia.dao;


import com.actonia.entity.ResourceSyncInfoEntity;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ResourceSyncInfoEntityDAO extends BaseJdbcSupport<ResourceSyncInfoEntity> {
    /*
     * (non-Javadoc)
     *
     * @see com.actonia.subserver.dao.BaseJdbcSupport#getTableName()
     */
    @Override
    public String getTableName() {
        return "resource_sync_info";
    }
   
   public ResourceSyncInfoEntity CheckProcessingProgrocess(int resourceType) {
       StringBuilder sql = new StringBuilder();
       sql.append(" select * from ").append(getTableName()).append(" where resourceType = ? and status = ? ");
       return findObject(sql.toString(), resourceType, ResourceSyncInfoEntity.STATUS_PROCESSING);
   }

    public ResourceSyncInfoEntity insert(ResourceSyncInfoEntity syncEntity) {
        Map<String, Object> val = new HashMap<String, Object>();
        val.put("resourceType", syncEntity.getResourceType());
        val.put("resourceAddDate", syncEntity.getResourceAddDate());
        val.put("fromLogId", syncEntity.getFromLogId());
        val.put("toLogId", syncEntity.getToLogId());
        val.put("status", syncEntity.getStatus());
        val.put("logCnt", syncEntity.getLogCnt());
        val.put("loadedCnt", syncEntity.getLoadedCnt());
        val.put("processEndDate", syncEntity.getProcessEndDate());
        val.put("createDate", syncEntity.getCreateDate());

        final int insert = insert(val);
        syncEntity.setId(insert);
        return syncEntity;
    }

    public void updateStatus(int id, int status, Date endDate) {
        String sql = "update " + getTableName() + " set status=?, processEndDate=? where id=? ";
        executeUpdate(sql, status, endDate, id);
    }

    public Long getMaxLogId(int resourceType, long fromLogId) {
        StringBuilder sql = new StringBuilder();
        sql.append(" select max(toLogId) from ").append(getTableName()).append(" where resourceType = ? and toLogId >= ? ");
        return queryForLong(sql.toString(), resourceType, fromLogId);
    }

    public Long getMaxLogIdByResourceType(int resourceType) {
        StringBuilder sql = new StringBuilder();
        sql.append(" select max(toLogId) from ").append(getTableName()).append(" where resourceType = ? ");
        return queryForLong(sql.toString(), resourceType);
    }

    public Long getMaxLogIdByResourceTypeAndDate(int resourceType, String createDate) {
        StringBuilder sql = new StringBuilder();
        sql.append(" select max(toLogId) from ").append(getTableName()).append(" where resourceType = ? and createDate >= ?");
        return queryForLong(sql.toString(), resourceType, createDate);
    }
    
    public List<ResourceSyncInfoEntity> getNotCompletedTagTraceSyncInfoList(Date backDate) {
        StringBuffer sql = new StringBuffer();
        sql.append(" select * from ").append(getTableName()).append(" where createDate>=? and resourceType in (101,102) and status = 3 order by id ");
        List<ResourceSyncInfoEntity> result = findBySql(sql.toString(), backDate);
        return result;
    }

}
