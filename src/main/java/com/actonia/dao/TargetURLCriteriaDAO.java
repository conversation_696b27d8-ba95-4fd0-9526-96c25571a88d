package com.actonia.dao;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.actonia.IConstants;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.entity.TargetURLCriteria;
import com.actonia.entity.TargetUrlEntity;

public class TargetURLCriteriaDAO extends BaseJdbcSupport<TargetURLCriteria> {

	private boolean isDebug = false;
	public static final int RECORDS_PER_SQL_STATEMENT = 10;
	public static final int SQL_STATEMENTS_PER_JDBC_CALL = 10;
	public static final int NUMBER_OF_FIELDS = 19;
	public static final String SQL_CREATION_FORMAT_1 = "(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
	public static final String SQL_CREATION_FORMAT_2 = ",(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
	private static String SQL_CREATION;

	static {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("insert into targeturl_criteria ");
		stringBuilder.append("(");
		stringBuilder.append("own_domain_id, ");
		stringBuilder.append("targeturl_id, ");
		stringBuilder.append("url, ");
		stringBuilder.append("cached_date, ");
		stringBuilder.append("resp_code, ");
		stringBuilder.append("page_title, ");
		stringBuilder.append("page_meta_keyword, ");
		stringBuilder.append("page_meta_desc, ");
		stringBuilder.append("page_h1, ");
		stringBuilder.append("page_h2, ");
		stringBuilder.append("page_title_length, ");
		stringBuilder.append("page_meta_keyword_length, ");
		stringBuilder.append("page_meta_desc_length, ");
		stringBuilder.append("page_h1_count, ");
		stringBuilder.append("page_h2_count, ");
		stringBuilder.append("meta_robot_index, ");
		stringBuilder.append("meta_robot_follow, ");
		stringBuilder.append("entrances_last7days, ");
		stringBuilder.append("count_associated_keyword ");
		stringBuilder.append(")");
		stringBuilder.append("values ");
		SQL_CREATION = stringBuilder.toString();
	}

	@Override
	public String getTableName() {
		return "targeturl_criteria";
	}

	public void updateScore(int domainId, long targeturlId, int score) {
		String sql = "update targeturl_criteria set score =? where own_domain_id=? and targeturl_id=?";
		executeUpdate(sql, score, domainId, targeturlId);
	}

	public TargetURLCriteria getExistCriteria(int domainId, Long urlId) {
		String sql = "select * from targeturl_criteria where own_domain_id=? and targeturl_id=?";
		return findObject(sql, domainId, urlId);
	}

	public int insert(TargetURLCriteria targetURLCriteria) {
		Map<String, Object> values = new HashMap<String, Object>();
		values.put("own_domain_id", targetURLCriteria.getOwnDomainId());
		values.put("targeturl_id", targetURLCriteria.getTargeturlId());
		values.put("url", targetURLCriteria.getUrl());
		values.put("cached_date", targetURLCriteria.getCachedDate());
		values.put("pagerank", targetURLCriteria.getPagerank());
		values.put("resp_code", targetURLCriteria.getRespCode());
		values.put("entrances_last7days", targetURLCriteria.getEntrancesLast7days());
		values.put("entrances_last30days", targetURLCriteria.getEntrancesLast30days());
		values.put("entrances_last_month", targetURLCriteria.getEntrancesLastMonth());

		values.put("entrances_week_over_week", targetURLCriteria.getEntrancesWeekOverWeek());
		values.put("entrances_month_over_month", targetURLCriteria.getEntrancesMonthOverMonth());
		values.put("page_title", targetURLCriteria.getPageTitle());
		values.put("page_meta_keyword", targetURLCriteria.getPageMetaKeyword());
		values.put("page_meta_desc", targetURLCriteria.getPageMetaDesc());
		values.put("page_h1", targetURLCriteria.getPageH1());
		values.put("page_h2", targetURLCriteria.getPageH2());

		values.put("page_title_length", targetURLCriteria.getPageTitleLength());
		values.put("page_meta_keyword_length", targetURLCriteria.getPageMetaKeywordLength());
		values.put("page_meta_desc_length", targetURLCriteria.getPageMetaDescLength());
		values.put("page_h1_count", targetURLCriteria.getPageH1Count());
		values.put("page_h2_count", targetURLCriteria.getPageH2Count());

		values.put("wtd_avg_rank", targetURLCriteria.getWtdAvgRank());
		values.put("total_search_volume_google", targetURLCriteria.getGoogleSearchVolume());
		values.put("count_associated_keyword ", targetURLCriteria.getCountAssociatedKeyword());

		values.put("meta_robot_index", targetURLCriteria.getMetaRobotIndex());
		values.put("meta_robot_follow", targetURLCriteria.getMetaRobotFollow());
		return insert(values);
	}

	public void updateById(int id, TargetURLCriteria targetURLCriteria) {
		StringBuffer sql = new StringBuffer();
		sql.append(" update targeturl_criteria set ");
		sql.append("	cached_date=?,");
		sql.append("	pagerank=?,");
		sql.append("	entrances_last7days=?,");
		sql.append("	entrances_last30days=?,");
		sql.append("	entrances_last_month=?,");
		sql.append("	entrances_week_over_week=?,");
		sql.append("	entrances_month_over_month=?,");
		sql.append("	wtd_avg_rank=?,");
		sql.append("	total_search_volume_google=?,");
		sql.append("	count_associated_keyword =? ");
		sql.append(" where id=?	");

		executeUpdate(sql.toString(), targetURLCriteria.getCachedDate(), targetURLCriteria.getPagerank(), targetURLCriteria.getEntrancesLast7days(),
				targetURLCriteria.getEntrancesLast30days(), targetURLCriteria.getEntrancesLastMonth(), targetURLCriteria.getEntrancesWeekOverWeek(),
				targetURLCriteria.getEntrancesMonthOverMonth(), targetURLCriteria.getWtdAvgRank(), targetURLCriteria.getGoogleSearchVolume(),
				targetURLCriteria.getCountAssociatedKeyword(), id);
	}

	public List<TargetURLCriteria> getByStartIndex(int ownDomainId, int startIndex, int pageSize) {
		StringBuffer pageQuery = new StringBuffer();
		pageQuery.append(" select tc.* ");
		pageQuery.append(" from targeturl_criteria tc ");
		pageQuery.append(" where tc.own_domain_id = ").append(ownDomainId);
		pageQuery.append(" and tc.targeturl_id > ").append(startIndex);
		pageQuery.append(" order by tc.targeturl_id ");
		pageQuery.append(" limit ").append(pageSize);

		return this.findBySql(pageQuery.toString());
	}

	/**
	 * delete the url by id
	 *
	 * @param id
	 */
	public void deleteUrlByTargetUrlId(int ownDomainId, long targetUrlId) {
		String sql = "delete from targeturl_criteria where own_domain_id = ? and targeturl_id = ? ";
		executeUpdate(sql, ownDomainId, targetUrlId);
	}

	public void updateTargetUrlId(int ownDomainId, long newUrlId, long oldUrlId) {
		String sql = "update targeturl_criteria set targeturl_id = ? where own_domain_id = ? and targeturl_id = ? ";
		executeUpdate(sql, newUrlId, ownDomainId, oldUrlId);
	}

	public TargetURLCriteria getTargetUrlById(Long targetUrlId, Integer ownDomainID) {
		String sql = " select * from targeturl_criteria where targeturl_id = ? and own_domain_id = ? ";
		return findObject(sql, targetUrlId, ownDomainID);
	}

	public void updateRespCode(int id, int respCode) {
		String sqlString = "update targeturl_criteria set resp_code = ? where id = ? ";
		executeUpdate(sqlString, respCode, id);
	}

	public void updateUrlMetrics(TargetURLCriteria targetURLCriteria) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("update targeturl_criteria ");
		stringBuilder.append("set ");
		stringBuilder.append(" resp_code = ?, ");
		stringBuilder.append(" page_title = ?, ");
		stringBuilder.append(" page_meta_keyword = ?, ");
		stringBuilder.append(" page_meta_desc = ?, ");
		stringBuilder.append(" page_h1 = ?, ");
		stringBuilder.append(" page_h2 = ?, ");
		stringBuilder.append(" meta_robot_follow = ?, ");
		stringBuilder.append(" meta_robot_index = ?, ");
		stringBuilder.append(" page_h1_count = ?, ");
		stringBuilder.append(" page_h2_count = ?, ");
		stringBuilder.append(" page_meta_desc_length = ?, ");
		stringBuilder.append(" page_meta_keyword_length = ?, ");
		stringBuilder.append(" page_title_length = ? ");
		stringBuilder.append("where id = ? ");
		String sqlString = stringBuilder.toString();
		executeUpdate(sqlString, targetURLCriteria.getRespCode(), targetURLCriteria.getPageTitle(), targetURLCriteria.getPageMetaKeyword(),
				targetURLCriteria.getPageMetaDesc(), targetURLCriteria.getPageH1(), targetURLCriteria.getPageH2(),
				targetURLCriteria.getMetaRobotFollow(), targetURLCriteria.getMetaRobotIndex(), targetURLCriteria.getPageH1Count(),
				targetURLCriteria.getPageH2Count(), targetURLCriteria.getPageMetaDescLength(), targetURLCriteria.getPageMetaKeywordLength(),
				targetURLCriteria.getPageTitleLength(), targetURLCriteria.getId());
	}

	public List<TargetURLCriteria> getByDomainIdAndResponseCode(int oid, int responseCode) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * ");
		sql.append(" from ").append(getTableName());
		sql.append(" where own_domain_id = ?");
		sql.append(" and resp_code = ?");

		return this.findBySql(sql.toString(), oid, responseCode);
	}

	public void updateUrlById(String url, int id) {
		String sql = "update targeturl_criteria set url =? where id=?";
		executeUpdate(sql, url, id);
	}

	public void updateForBatch(List<TargetURLCriteria> targetURLCriteriaList, String standardLoggingText) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("update targeturl_criteria ");
		stringBuilder.append("set ");
		stringBuilder.append(" url = ?, ");
		stringBuilder.append(" resp_code = ?, ");
		stringBuilder.append(" page_title = ?, ");
		stringBuilder.append(" page_meta_keyword = ?, ");
		stringBuilder.append(" page_meta_desc = ?, ");
		stringBuilder.append(" page_h1 = ?, ");
		stringBuilder.append(" page_h2 = ?, ");
		stringBuilder.append(" meta_robot_follow = ?, ");
		stringBuilder.append(" meta_robot_index = ?, ");
		stringBuilder.append(" page_h1_count = ?, ");
		stringBuilder.append(" page_h2_count = ?, ");
		stringBuilder.append(" page_meta_desc_length = ?, ");
		stringBuilder.append(" page_meta_keyword_length = ?, ");
		stringBuilder.append(" page_title_length = ?, ");
		stringBuilder.append(" cached_date = ?, ");
		stringBuilder.append(" entrances_last7days = ?, ");
		stringBuilder.append(" count_associated_keyword = ? ");
		stringBuilder.append("where id = ? ");
		String sqlString = stringBuilder.toString();
		List<Object[]> objectArrayList = new ArrayList<Object[]>();
		for (TargetURLCriteria targetURLCriteria : targetURLCriteriaList) {
			if (isDebug == true) {
//				FormatUtils.logMemoryUsage("updateForBatch() " + standardLoggingText + ",targetURLCriteria=" + targetURLCriteria.toString());
			}
			Object[] values = new Object[] {  
					targetURLCriteria.getUrl(),
					targetURLCriteria.getRespCode(), 
					targetURLCriteria.getPageTitle(),
					targetURLCriteria.getPageMetaKeyword(), targetURLCriteria.getPageMetaDesc(), targetURLCriteria.getPageH1(),
					targetURLCriteria.getPageH2(), targetURLCriteria.getMetaRobotFollow(), targetURLCriteria.getMetaRobotIndex(),
					targetURLCriteria.getPageH1Count(), targetURLCriteria.getPageH2Count(), targetURLCriteria.getPageMetaDescLength(),
					targetURLCriteria.getPageMetaKeywordLength(), targetURLCriteria.getPageTitleLength(), new Date(),
					targetURLCriteria.getEntrancesLast7days(),
					targetURLCriteria.getCountAssociatedKeyword(),
					targetURLCriteria.getId() };
			objectArrayList.add(values);
		}
		this.executeBatch(sqlString, objectArrayList);
	}

	public void insertMultiRowsBatch(List<TargetURLCriteria> targetURLCriteriaList) {
		Object[] objectArray = null;
		List<Object[]> objectArrayList = new ArrayList<Object[]>();
		List<TargetURLCriteria> tempList = new ArrayList<TargetURLCriteria>();
		for (TargetURLCriteria targetURLCriteria : targetURLCriteriaList) {
			tempList.add(targetURLCriteria);
			if (tempList.size() == RECORDS_PER_SQL_STATEMENT) {
				objectArray = getObjectArrayForInsert(tempList);
				objectArrayList.add(objectArray);
				if (objectArrayList.size() == SQL_STATEMENTS_PER_JDBC_CALL) {
					this.executeBatch(getInsertMultiRowsSqlStatement(RECORDS_PER_SQL_STATEMENT), objectArrayList);
					objectArrayList = new ArrayList<Object[]>();
				}
				tempList = new ArrayList<TargetURLCriteria>();
			}
		}

		if (objectArrayList != null && objectArrayList.size() > 0) {
			this.executeBatch(getInsertMultiRowsSqlStatement(RECORDS_PER_SQL_STATEMENT), objectArrayList);
			objectArrayList = new ArrayList<Object[]>();
		}

		if (tempList != null && tempList.size() > 0) {
			objectArray = getObjectArrayForInsert(tempList);
			objectArrayList.add(objectArray);
			if (objectArrayList != null && objectArrayList.size() > 0) {
				this.executeBatch(getInsertMultiRowsSqlStatement(tempList.size()), objectArrayList);
				objectArrayList = new ArrayList<Object[]>();
			}
		}
	}

	private Object[] getObjectArrayForInsert(List<TargetURLCriteria> list) {
		List<Object[]> tempObjectArrayList = new ArrayList<Object[]>();
		Object[] urlMetricsDataObjectArray = null;
		int totalNumberOfObjects = 0;
		for (TargetURLCriteria targetURLCriteria : list) {
			urlMetricsDataObjectArray = new Object[] { targetURLCriteria.getOwnDomainId(), targetURLCriteria.getTargeturlId(),
					targetURLCriteria.getUrl(), targetURLCriteria.getCachedDate(), targetURLCriteria.getRespCode(), targetURLCriteria.getPageTitle(),
					targetURLCriteria.getPageMetaKeyword(), targetURLCriteria.getPageMetaDesc(), targetURLCriteria.getPageH1(),
					targetURLCriteria.getPageH2(), targetURLCriteria.getPageTitleLength(), targetURLCriteria.getPageMetaKeywordLength(),
					targetURLCriteria.getPageMetaDescLength(), targetURLCriteria.getPageH1Count(), targetURLCriteria.getPageH2Count(),
					targetURLCriteria.getMetaRobotIndex(), targetURLCriteria.getMetaRobotFollow(), targetURLCriteria.getEntrancesLast7days(),
					targetURLCriteria.getCountAssociatedKeyword(),};
			tempObjectArrayList.add(urlMetricsDataObjectArray);
		}

		totalNumberOfObjects = tempObjectArrayList.size() * NUMBER_OF_FIELDS;

		Object[] objectArray = new Object[totalNumberOfObjects];
		int idx = 0;
		for (Object[] tempObjectArray : tempObjectArrayList) {
			for (int i = 0; i < tempObjectArray.length; i++) {
				objectArray[idx++] = tempObjectArray[i];
			}
		}
		return objectArray;
	}

	private String getInsertMultiRowsSqlStatement(int recordsPerInsertStatement) {
		String response = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(SQL_CREATION);
		for (int i = 0; i < recordsPerInsertStatement; i++) {
			if (i == 0) {
				stringBuilder.append(SQL_CREATION_FORMAT_1);
			} else {
				stringBuilder.append(SQL_CREATION_FORMAT_2);
			}
		}
		stringBuilder.append(IConstants.SEMI_COLON);
		response = stringBuilder.toString();
		return response;
	}

	public TargetURLCriteria get(Long targetUrlId, Integer ownDomainID) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("select ");
		stringBuilder.append("id, ");
		stringBuilder.append("own_domain_id, ");
		stringBuilder.append("targeturl_id, ");
		stringBuilder.append("url, ");
		stringBuilder.append("cached_date, ");
		stringBuilder.append("resp_code, ");
		stringBuilder.append("page_title, ");
		stringBuilder.append("page_meta_keyword, ");
		stringBuilder.append("page_meta_desc, ");
		stringBuilder.append("page_h1 as pageH1, ");
		stringBuilder.append("page_h2 as pageH2, ");
		stringBuilder.append("page_title_length, ");
		stringBuilder.append("page_meta_keyword_length, ");
		stringBuilder.append("page_meta_desc_length, ");
		stringBuilder.append("page_h1_count as pageH1Count, ");
		stringBuilder.append("page_h2_count as pageH2Count, ");
		stringBuilder.append("meta_robot_index, ");
		stringBuilder.append("meta_robot_follow, ");
		stringBuilder.append("entrances_last7days as entrancesLast7days, ");
		stringBuilder.append("count_associated_keyword ");
		stringBuilder.append("from ");
		stringBuilder.append(getTableName() + " ");
		stringBuilder.append("where ");
		stringBuilder.append("    targeturl_id = ? ");
		stringBuilder.append("and own_domain_id = ? ");
		String sqlString = stringBuilder.toString();
		return findObject(sqlString, targetUrlId, ownDomainID);
	}

	public void cleanup() {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("delete from ");
		stringBuilder.append(getTableName() + " ");
		stringBuilder.append("where ");
		stringBuilder.append("    targeturl_id not in ");
		stringBuilder.append("( ");
		stringBuilder.append("	select ");
		stringBuilder.append("	    t_target_url.id ");
		stringBuilder.append("	from ");
		stringBuilder.append("	    t_own_domain t_own_domain, ");
		stringBuilder.append("	    t_target_url t_target_url ");
		stringBuilder.append("	where ");
		stringBuilder.append("	    t_own_domain.`status` = ? ");
		stringBuilder.append("	and t_own_domain.id = t_target_url.own_domain_id ");
		stringBuilder.append("	and t_target_url.`type` = ? ");
		stringBuilder.append("	and t_target_url.`status` = ? ");
		stringBuilder.append(") ");
		String sqlString = stringBuilder.toString();
		this.executeUpdate(sqlString, OwnDomainEntity.STATE_ACTIVE, TargetUrlEntity.TYPE_ADD_BY_USER, TargetUrlEntity.STATUS_ACTIVE);
	}

	public Map<Long, TargetURLCriteria> getMap(int domainId) {
		Map<Long, TargetURLCriteria> output = new HashMap<Long, TargetURLCriteria>();
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     targeturl_criteria.id as id,");
		stringBuilder.append("     targeturl_criteria.own_domain_id as ownDomainId,");
		stringBuilder.append("     targeturl_criteria.targeturl_id as targeturlId,");
		stringBuilder.append("     targeturl_criteria.url as url,");
		stringBuilder.append("     targeturl_criteria.cached_date as cachedDate,");
		stringBuilder.append("     targeturl_criteria.pagerank as pagerank,");
		stringBuilder.append("     targeturl_criteria.resp_code as respCode,");
		stringBuilder.append("     targeturl_criteria.entrances_last7days as entrancesLast7days,");
		stringBuilder.append("     targeturl_criteria.entrances_last30days as entrancesLast30days,");
		stringBuilder.append("     targeturl_criteria.entrances_last_month as entrancesLastMonth,");
		stringBuilder.append("     targeturl_criteria.entrances_week_over_week as entrancesWeekOverWeek,");
		stringBuilder.append("     targeturl_criteria.entrances_month_over_month as entrancesMonthOverMonth,");
		stringBuilder.append("     targeturl_criteria.page_title as pageTitle,");
		stringBuilder.append("     targeturl_criteria.page_meta_keyword as pageMetaKeyword,");
		stringBuilder.append("     targeturl_criteria.page_meta_desc as pageMetaDesc,");
		stringBuilder.append("     targeturl_criteria.page_h1 as pageH1,");
		stringBuilder.append("     targeturl_criteria.page_h2 as pageH2,");
		stringBuilder.append("     targeturl_criteria.page_title_length as pageTitleLength,");
		stringBuilder.append("     targeturl_criteria.page_meta_keyword_length as pageMetaKeywordLength,");
		stringBuilder.append("     targeturl_criteria.page_meta_desc_length as pageMetaDescLength,");
		stringBuilder.append("     targeturl_criteria.page_h1_count as pageH1Count,");
		stringBuilder.append("     targeturl_criteria.page_h2_count as pageH2Count,");
		stringBuilder.append("     targeturl_criteria.score as score,");
		stringBuilder.append("     targeturl_criteria.count_associated_keyword as countAssociatedKeyword,");
		stringBuilder.append("     targeturl_criteria.wtd_avg_rank as wtdAvgRank,");
		stringBuilder.append("     targeturl_criteria.total_search_volume_google as googleSearchVolume,");
		stringBuilder.append("     targeturl_criteria.meta_robot_index as metaRobotIndex,");
		stringBuilder.append("     targeturl_criteria.meta_robot_follow as metaRobotFollow ");
		stringBuilder.append(" from");
		stringBuilder.append("     t_target_url t_target_url,");
		stringBuilder.append("     targeturl_criteria targeturl_criteria");
		stringBuilder.append(" where");
		stringBuilder.append("     t_target_url.own_domain_id = ?");
		stringBuilder.append(" and t_target_url.`type` = ?");
		stringBuilder.append(" and t_target_url.`status` = ?");
		stringBuilder.append(" and t_target_url.own_domain_id = targeturl_criteria.own_domain_id");
		stringBuilder.append(" and t_target_url.id = targeturl_criteria.targeturl_id");
		String sqlString = stringBuilder.toString();
		List<TargetURLCriteria> targetURLCriteriaList = this.findBySql(sqlString, domainId, TargetUrlEntity.TYPE_ADD_BY_USER,
				TargetUrlEntity.STATUS_ACTIVE);
		long targetUrlId = 0;
		if (targetURLCriteriaList != null && targetURLCriteriaList.size() > 0) {
			for (TargetURLCriteria targetURLCriteria : targetURLCriteriaList) {
				targetUrlId = targetURLCriteria.getTargeturlId();
				output.put(targetUrlId, targetURLCriteria);
			}
		}
		return output;
	}
}
