package com.actonia.dao;

import java.util.List;

import com.actonia.entity.TUsers;

public class UsersDAO extends BaseJdbcSupport<TUsers> {

	public String getTableName() {
		return "t_users";
	}

	public TUsers checkAuthority(String accessToken) {
		String sql = "select * from t_users where access_token=?";
		return findObject(sql, accessToken);
	}

	public void updateToken(String accessToken) {
		String sql = "update t_users set total_used = total_used+1 where access_token=? ";
		executeUpdate(sql, accessToken);
	}

	public List<TUsers> getAccessTokenDomainIdList(int userId) {
		String sql = "select access_token, own_domain_id from t_users where user_id = ?";
		return findBySql(sql, userId);
	}
}
