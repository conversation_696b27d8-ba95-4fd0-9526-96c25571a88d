package com.actonia.dao;

import com.actonia.entity.GroupTagRelationEntity;

public class GroupTagRelationEntityDAO extends BaseJdbcSupport<GroupTagRelationEntity> {
	@Override
	public String getTableName() {
		return "t_group_tag_relation";
	}

	public int getNumberOfUrls(int domainId, int groupTagId, int resourceType) {
		String sql = "select count(*) from t_group_tag_relation where domain_id = ? and group_tag_id = ? and resource_type = ?";
		return queryForInt(sql, domainId, groupTagId, resourceType);
	}
}