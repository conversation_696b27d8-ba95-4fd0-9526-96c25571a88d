package com.actonia.dao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import com.actonia.utils.AwsCredentialsEnvKeyConstructor;
import org.apache.commons.lang.StringUtils;

import com.actonia.IConstants;
import com.actonia.utils.FormatUtils;

import ru.yandex.clickhouse.ClickHouseDataSource;
import ru.yandex.clickhouse.settings.ClickHouseProperties;

// Due to ClickHouse concurrency issues, use Singleton pattern, do not use Spring (ie. all methods must be 'synchronized')
public class TargetUrlHtmlFileNameBackupClickHouseDAO {

	//private boolean isDebug = false;
	private List<String> databaseHostnameList;
	private String databasePort = null;
	private String databaseName = null;
	private List<Connection> connectionList;
	private int batchCreationSize;
	private String databaseUser = null;
	private String databasePassword = null;
	private List<String> connectionUrlList = null;
	private int connectionTimeoutInMilliseconds;
	private int maximumRetryCounts;
	private int retryWaitMilliseconds;
	public static final String TABLE_NAME = "local_target_url_html_file_name";
	private final String s3AccessKey;
	private final String s3SecretKey;

	public TargetUrlHtmlFileNameBackupClickHouseDAO(String[] databaseHostnameArrayInput, String databasePortInput, String databaseNameInput, int batchCreationSizeInput,
			String databaseUserInput, String databasePasswordInput, int connectionTimeoutInMillisecondsInput, int maximumRetryCountsInput,
			int retryWaitMillisecondsInput) throws Exception {

		ClickHouseDataSource clickHouseDataSource = null;
		Connection connection = null;

		databaseHostnameList = Arrays.asList(databaseHostnameArrayInput);

		databasePort = databasePortInput;
		databaseName = databaseNameInput;
		batchCreationSize = batchCreationSizeInput;
		databaseUser = databaseUserInput;
		databasePassword = databasePasswordInput;
		connectionTimeoutInMilliseconds = connectionTimeoutInMillisecondsInput;
		maximumRetryCounts = maximumRetryCountsInput;
		retryWaitMilliseconds = retryWaitMillisecondsInput;

		FormatUtils.getInstance()
				.logMemoryUsage("TargetUrlHtmlFileNameBackupClickHouseDAO() databaseHostnameList=" + databaseHostnameList.toString() + ",databasePort=" + databasePort
						+ ",databaseName=" + databaseName + ",batchCreationSize=" + batchCreationSize + ",databaseUser=" + databaseUser + ",databasePassword="
						+ databasePassword + ",connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ",maximumRetryCounts=" + maximumRetryCounts
						+ ",retryWaitMilliseconds=" + retryWaitMilliseconds + ",TABLE_NAME=" + TABLE_NAME);

		ClickHouseProperties clickHouseProperties = new ClickHouseProperties();
		clickHouseProperties.setDecompress(true);
		clickHouseProperties.setConnectionTimeout(connectionTimeoutInMilliseconds);
		clickHouseProperties.setSocketTimeout(connectionTimeoutInMilliseconds);

		connectionList = new ArrayList<Connection>();

		String connectionUrl = null;

		connectionUrlList = new ArrayList<String>();

		for (String databaseHostname : databaseHostnameList) {
			if (StringUtils.isNotBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
						+ "?user=" + databaseUser + "&password=" + databasePassword;
			} else {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH
						+ databaseName;
			}
			connectionUrlList.add(connectionUrl);
			FormatUtils.getInstance().logMemoryUsage("TargetUrlHtmlFileNameBackupClickHouseDAO() connectionUrl=" + connectionUrl);
			clickHouseDataSource = new ClickHouseDataSource(connectionUrl, clickHouseProperties);
			connection = clickHouseDataSource.getConnection();
			connectionList.add(connection);
		}
		this.s3AccessKey = AwsCredentialsEnvKeyConstructor.getInstance().getPlainTextS3AccessKey();
		this.s3SecretKey = AwsCredentialsEnvKeyConstructor.getInstance().getS3DecryptedSecretKey();
	}

	@Override
	public String toString() {
		return "TargetUrlHtmlFileNameBackupClickHouseDAO [databaseHostnameList=" + databaseHostnameList.toString() + ", databasePort=" + databasePort
				+ ", databaseName=" + databaseName + ", batchCreationSize=" + batchCreationSize + ", databaseUser=" + databaseUser + ", databasePassword="
				+ databasePassword + ", connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ", maximumRetryCounts=" + maximumRetryCounts
				+ ", retryWaitMilliseconds=" + retryWaitMilliseconds + "]";
	}

	private synchronized String getTableName(String tableName) {
		if (StringUtils.isNotBlank(tableName)) {
			return tableName;
		} else {
			return TABLE_NAME;
		}
	}

	public Date getEarliestTrackDate(String tableName) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		Date logDate = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     min(track_date)");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getEarliestTrackDate() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		//if (isDebug == true) {
		//	connectionUrl = connectionUrlList.get(connectionIndex);
		//	FormatUtils.getInstance().logMemoryUsage("getEarliestTrackDate() connectionUrl=" + connectionUrl);
		//}
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				preparedStatement = connection.prepareStatement(sqlString);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					logDate = resultSet.getTimestamp(1);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				e.printStackTrace();
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getEarliestTrackDate() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			} finally {
				if (resultSet != null) {
					resultSet.close();
				}
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("getEarliestTrackDate() logDate=" + logDate + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		return logDate;
	}

	public Date getLatestTrackDate(String tableName) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		Date logDate = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     max(track_date)");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getLatestTrackDate() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		//if (isDebug == true) {
		//	connectionUrl = connectionUrlList.get(connectionIndex);
		//	FormatUtils.getInstance().logMemoryUsage("getLatestTrackDate() connectionUrl=" + connectionUrl);
		//}
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				preparedStatement = connection.prepareStatement(sqlString);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					logDate = resultSet.getTimestamp(1);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				e.printStackTrace();
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getLatestTrackDate() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			} finally {
				if (resultSet != null) {
					resultSet.close();
				}
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("getLatestTrackDate() logDate=" + logDate + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		return logDate;
	}

	public void backupToS3(String s3ObjectURI, String tableName, String logDateString) throws Exception {
		//FormatUtils.getInstance().logMemoryUsage("backupToS3() begins. s3ObjectURI=" + s3ObjectURI + ",tableName=" + tableName + ",logDateString="
		//		+ logDateString);
		//long startTimestamp = System.currentTimeMillis();
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("insert into function s3('" + s3ObjectURI + "', ");
		stringBuilder.append("  '" + s3AccessKey + "', ");
		stringBuilder.append("  '" + s3SecretKey + "', ");
		stringBuilder.append("  'Native', ");
		stringBuilder.append(
				"  '`domain_id` UInt32, `url` String, `track_date` Date, `crawl_timestamp` DateTime, `file_name` String, `url_hash` UInt64, `url_murmur_hash` UInt32, `sign` Int8', ");
		stringBuilder.append("   'zstd') ");
		stringBuilder.append("select * ");
		stringBuilder.append("from " + getTableName(tableName));
		stringBuilder.append(" where track_date = '" + logDateString + "'");
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("backupToS3() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		//if (isDebug == true) {
		//	connectionUrl = connectionUrlList.get(connectionIndex);
		//	FormatUtils.getInstance().logMemoryUsage("backupToS3() connectionUrl=" + connectionUrl);
		//}
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				preparedStatement = connection.prepareStatement(sqlString);
				resultSet = preparedStatement.executeQuery();
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				e.printStackTrace();
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("backupToS3() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			} finally {
				if (resultSet != null) {
					resultSet.close();
				}
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();
				}
			}
		}
		//FormatUtils.getInstance().logMemoryUsage("backupToS3() ends. s3ObjectURI=" + s3ObjectURI + ",tableName=" + tableName + ",logDateString="
		//		+ logDateString + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	public void restoreFromS3(String s3ObjectURI, String tableName) throws Exception {
		//FormatUtils.getInstance().logMemoryUsage("restoreFromS3() begins. s3ObjectURI=" + s3ObjectURI + ",tableName=" + tableName);
		//long startTimestamp = System.currentTimeMillis();
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("insert into " + getTableName(tableName) + " ");
		stringBuilder.append("select * ");
		stringBuilder.append("from s3('" + s3ObjectURI + "', ");
		stringBuilder.append("  '" + s3AccessKey + "', ");
		stringBuilder.append("  '" + s3SecretKey + "', ");
		stringBuilder.append("  'Native', ");
		stringBuilder.append(
				"  '`domain_id` UInt32, `url` String, `track_date` Date, `crawl_timestamp` DateTime, `file_name` String, `url_hash` UInt64, `url_murmur_hash` UInt32, `sign` Int8', ");
		stringBuilder.append("   'zstd') ");
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("restoreFromS3() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		//if (isDebug == true) {
		//	connectionUrl = connectionUrlList.get(connectionIndex);
		//	FormatUtils.getInstance().logMemoryUsage("restoreFromS3() connectionUrl=" + connectionUrl);
		//}
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				preparedStatement = connection.prepareStatement(sqlString);
				resultSet = preparedStatement.executeQuery();
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				e.printStackTrace();
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("restoreFromS3() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			} finally {
				if (resultSet != null) {
					resultSet.close();
				}
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();
				}
			}
		}
		//FormatUtils.getInstance().logMemoryUsage("restoreFromS3() ends. s3ObjectURI=" + s3ObjectURI + ",tableName=" + tableName + ",elapsed(s.)="
		//		+ (System.currentTimeMillis() - startTimestamp) / 1000);
	}

}
