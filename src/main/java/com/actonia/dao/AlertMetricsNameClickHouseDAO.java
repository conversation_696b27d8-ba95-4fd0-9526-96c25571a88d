package com.actonia.dao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.lang.StringUtils;

import com.actonia.IConstants;
import com.actonia.entity.AlertMetricsNameClickHouseEntity;
import com.actonia.utils.FormatUtils;

import ru.yandex.clickhouse.ClickHouseDataSource;
import ru.yandex.clickhouse.settings.ClickHouseProperties;

// Due to ClickHouse concurrency issues, use Singleton pattern, do not use Spring (ie. all methods must be 'synchronized')
public class AlertMetricsNameClickHouseDAO {

	//private static boolean isDebug = false;
	private static List<String> databaseHostnameList;
	private static String databasePort = null;
	private static String databaseName = null;
	private static List<Connection> connectionList;
	private static int batchCreationSize;
	private static String databaseUser = null;
	private static String databasePassword = null;
	private static List<String> connectionUrlList = null;
	private static int connectionTimeoutInMilliseconds;
	private static int maximumRetryCounts;
	private static int retryWaitMilliseconds;
	public static final String TEST_TABLE_NAME = "alert_metrics_name_test";
	public static final String TABLE_NAME = "alert_metrics_name";
	private static AlertMetricsNameClickHouseDAO alertMetricsNameClickHouseDAO;

	public static AlertMetricsNameClickHouseDAO getInstance() throws Exception {
		if (alertMetricsNameClickHouseDAO == null) {
			alertMetricsNameClickHouseDAO = initialize();
		}
		return alertMetricsNameClickHouseDAO;
	}

	private static AlertMetricsNameClickHouseDAO initialize() throws Exception {
		FormatUtils.getInstance().logMemoryUsage("initialize() begins.");

		AlertMetricsNameClickHouseDAO alertMetricsNameClickHouseDAO = null;

		String clickHouseDatabaseHostnames = ClickHouseCrawlDatabaseConfigurations
				.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_HOSTNAMES);
		String[] clickHouseDatabaseHostnameArray = clickHouseDatabaseHostnames.split(IConstants.COMMA);
		String clickHouseDatabasePort = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_PORT);
		String clickHouseDatabaseName = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_NAME);
		String clickHouseDatabaseUser = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_USER);
		String clickHouseDatabasePassword = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_PASSWORD);
		int clickHouseBatchCreationSize = ClickHouseCrawlDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_BATCH_CREATION_SIZE, 100);
		int clickHouseconnectionTimeoutInMilliseconds = ClickHouseCrawlDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_CONNECTION_TIMEOUT_MILLISECONDS, 88888);
		int clickHouseMaximumRetryCounts = ClickHouseCrawlDatabaseConfigurations.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_MAX_RETRY_COUNTS,
				8);
		int clickHouseRetryWaitMilliseconds = ClickHouseCrawlDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_RETRY_WAIT_MILLISECONDS, 1000);

		try {
			alertMetricsNameClickHouseDAO = new AlertMetricsNameClickHouseDAO(clickHouseDatabaseHostnameArray, clickHouseDatabasePort, clickHouseDatabaseName,
					clickHouseBatchCreationSize, clickHouseDatabaseUser, clickHouseDatabasePassword, clickHouseconnectionTimeoutInMilliseconds,
					clickHouseMaximumRetryCounts, clickHouseRetryWaitMilliseconds);
		} catch (Exception e) {
			e.printStackTrace();
		}

		FormatUtils.getInstance().logMemoryUsage("initialize() ends. alertMetricsNameClickHouseDAO=" + alertMetricsNameClickHouseDAO.toString());
		return alertMetricsNameClickHouseDAO;
	}

	private AlertMetricsNameClickHouseDAO(String[] databaseHostnameArrayInput, String databasePortInput, String databaseNameInput, int batchCreationSizeInput,
			String databaseUserInput, String databasePasswordInput, int connectionTimeoutInMillisecondsInput, int maximumRetryCountsInput,
			int retryWaitMillisecondsInput) throws Exception {

		ClickHouseDataSource clickHouseDataSource = null;
		Connection connection = null;

		databaseHostnameList = Arrays.asList(databaseHostnameArrayInput);
		databasePort = databasePortInput;
		databaseName = databaseNameInput;
		batchCreationSize = batchCreationSizeInput;
		databaseUser = databaseUserInput;
		databasePassword = databasePasswordInput;
		connectionTimeoutInMilliseconds = connectionTimeoutInMillisecondsInput;
		maximumRetryCounts = maximumRetryCountsInput;
		retryWaitMilliseconds = retryWaitMillisecondsInput;

		FormatUtils.getInstance()
				.logMemoryUsage("AlertMetricsNameClickHouseDAO() databaseHostnameList=" + databaseHostnameList.toString() + ",databasePort=" + databasePort
						+ ",databaseName=" + databaseName + ",batchCreationSize=" + batchCreationSize + ",databaseUser=" + databaseUser + ",databasePassword="
						+ databasePassword + ",connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ",maximumRetryCounts=" + maximumRetryCounts
						+ ",retryWaitMilliseconds=" + retryWaitMilliseconds + ",TABLE_NAME=" + TABLE_NAME);

		ClickHouseProperties clickHouseProperties = new ClickHouseProperties();
		clickHouseProperties.setDecompress(true);
		clickHouseProperties.setConnectionTimeout(connectionTimeoutInMilliseconds);
		clickHouseProperties.setSocketTimeout(connectionTimeoutInMilliseconds);

		connectionList = new ArrayList<Connection>();

		String connectionUrl = null;

		connectionUrlList = new ArrayList<String>();

		for (String databaseHostname : databaseHostnameList) {
			if (StringUtils.isNotBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
						+ "?user=" + databaseUser + "&password=" + databasePassword;
			} else if (StringUtils.isBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
						+ "?password=" + databasePassword;
			} else {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH
						+ databaseName;
			}
			connectionUrlList.add(connectionUrl);
			clickHouseDataSource = new ClickHouseDataSource(connectionUrl, clickHouseProperties);
			connection = clickHouseDataSource.getConnection();
			connectionList.add(connection);
			FormatUtils.getInstance().logMemoryUsage("AlertMetricsNameClickHouseDAO() database connection created...connectionUrl=" + connectionUrl);
		}
	}

	@Override
	public String toString() {
		return "AlertMetricsNameClickHouseDAO [databaseHostnameList=" + databaseHostnameList.toString() + ", databasePort=" + databasePort + ", databaseName="
				+ databaseName + ", batchCreationSize=" + batchCreationSize + ", databaseUser=" + databaseUser + ", databasePassword=" + databasePassword
				+ ", connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ", maximumRetryCounts=" + maximumRetryCounts + ", retryWaitMilliseconds="
				+ retryWaitMilliseconds + "]";
	}

	//public synchronized String getTableName() {
	//	return getTableName(null);
	//}

	public synchronized String getTableName(String tableName) {
		if (StringUtils.isNotBlank(tableName)) {
			return tableName;
		} else {
			return TABLE_NAME;
		}
	}

	public synchronized int getBatchCreationSize() {
		return batchCreationSize;
	}

	public synchronized void createBatch(List<AlertMetricsNameClickHouseEntity> alertMetricsClickHouseEntityList, String tableName) throws Exception {
		//long startTimestamp = System.nanoTime();

		PreparedStatement preparedStatement = null;
		int index = 0;
		Connection connection = null;

		String creationSqlStatement = getCreationSqlStatement(tableName);
		//FormatUtils.getInstance().logMemoryUsage("createBatch() creationSqlStatement=" + creationSqlStatement);

		connection = connectionList.get(0);
		preparedStatement = connection.prepareStatement(creationSqlStatement);
		for (AlertMetricsNameClickHouseEntity alertMetricsClickHouseEntity : alertMetricsClickHouseEntityList) {

			index = 1;

			// domain_id
			preparedStatement.setInt(index++, alertMetricsClickHouseEntity.getDomain_id() != null ? alertMetricsClickHouseEntity.getDomain_id() : -1);

			// url_type
			preparedStatement.setInt(index++, alertMetricsClickHouseEntity.getUrl_type() != null ? alertMetricsClickHouseEntity.getUrl_type() : -1);

			// metrics_name
			preparedStatement.setString(index++, alertMetricsClickHouseEntity.getMetrics_name());

			preparedStatement.addBatch();
		}

		int retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				//startTimestamp = System.nanoTime();
				preparedStatement.executeBatch();
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("createBatch() e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			} finally {
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();					
				}
			}
		}
		//FormatUtils.getInstance().logMemoryUsage("createBatch() alertMetricsClickHouseEntityList.size()==" + alertMetricsClickHouseEntityList.size() + ",elapsed(ns.)="
		//		+ (System.nanoTime() - startTimestamp));
	}

	private synchronized String getCreationSqlStatement(String tableName) {
		String response = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" insert into " + getTableName(tableName));
		stringBuilder.append(" (");
		stringBuilder.append(" domain_id,");
		stringBuilder.append(" url_type,");
		stringBuilder.append(" metrics_name");
		stringBuilder.append(" )");
		stringBuilder.append(" values ");
		stringBuilder.append(" (");
		stringBuilder.append(" ?,");
		stringBuilder.append(" ?,");
		stringBuilder.append(" ?");
		stringBuilder.append(" )");
		stringBuilder.append(IConstants.SEMI_COLON);
		response = stringBuilder.toString();
		return response;
	}

	public void createTable(String tableName) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("createTable() begins.");
		StringBuilder stringBuilder = null;
		String sqlString = null;
		Connection connection = null;
		connection = connectionList.get(0);
		stringBuilder = new StringBuilder();
		stringBuilder.append(" CREATE TABLE IF NOT EXISTS " + getTableName(tableName));
		stringBuilder.append(" (");
		stringBuilder.append("	domain_id Int32 DEFAULT -1,");
		stringBuilder.append("	url_type Int32 DEFAULT -1,");
		stringBuilder.append("	metrics_name String DEFAULT ''");
		stringBuilder.append(" )");
		stringBuilder.append(" ENGINE = TinyLog");
		stringBuilder.append(" ;");
		sqlString = stringBuilder.toString();
		FormatUtils.getInstance().logMemoryUsage("createTable() sqlString=" + sqlString);
		connection.createStatement().execute(sqlString);
		FormatUtils.getInstance().logMemoryUsage("createTable() ends.");
	}

	public List<AlertMetricsNameClickHouseEntity> getList(String tableName) throws Exception {
		List<AlertMetricsNameClickHouseEntity> alertMetricsNameClickHouseEntityList = new ArrayList<AlertMetricsNameClickHouseEntity>();
		AlertMetricsNameClickHouseEntity alertMetricsNameClickHouseEntity = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("   *");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));

		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getList() sqlString=" + sqlString);
		connection = connectionList.get(0);
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				preparedStatement = connection.prepareStatement(sqlString);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					alertMetricsNameClickHouseEntity = new AlertMetricsNameClickHouseEntity();

					// domain_id
					alertMetricsNameClickHouseEntity.setDomain_id(resultSet.getInt(IConstants.FIELD_NAME_DOMAIN_ID));

					// url_type
					alertMetricsNameClickHouseEntity.setUrl_type(resultSet.getInt(IConstants.FIELD_NAME_URL_TYPE));

					// metrics_name
					alertMetricsNameClickHouseEntity.setMetrics_name(resultSet.getString(IConstants.FIELD_NAME_METRICS_NAME));

					alertMetricsNameClickHouseEntityList.add(alertMetricsNameClickHouseEntity);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getList() e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			} finally {
				if (resultSet != null) {
					resultSet.close();
				}
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();					
				}
			}
		}
		//FormatUtils.getInstance().logMemoryUsage("getList() ends. startTrackDate="+startTrackDate +",endTrackDate="+endTrackDate +",urlString="+urlString
		//		+ ",alertMetricsNameClickHouseEntityList.size()=" + alertMetricsNameClickHouseEntityList.size()
		//		+ ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp)
		//);
		return alertMetricsNameClickHouseEntityList;
	}

	public void resetTable(String tableName) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("resetTable() begins.");
		String sqlString = null;
		Connection connection = null;
		connection = connectionList.get(0);

		sqlString = "drop table if exists " + tableName;
		FormatUtils.getInstance().logMemoryUsage("resetTable() sqlString=" + sqlString);
		connection.createStatement().execute(sqlString);

		sqlString = "create table if not exists " + tableName + " as alert_metrics_name_template";
		FormatUtils.getInstance().logMemoryUsage("resetTable() sqlString=" + sqlString);
		connection.createStatement().execute(sqlString);

		FormatUtils.getInstance().logMemoryUsage("resetTable() ends.");
	}

	public void postDataCreationMaintenance() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("postDataCreationMaintenance() begins.");
		Connection connection = null;
		int retryCount = 0;
		connection = connectionList.get(0);
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				connection.createStatement().execute("drop table if exists alert_metrics_name_backup");
				connection.createStatement().execute("rename table alert_metrics_name to alert_metrics_name_backup");
				connection.createStatement().execute("rename table alert_metrics_name_test to alert_metrics_name");
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				e.printStackTrace();
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("postDataCreationMaintenance() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("postDataCreationMaintenance() ends. ,elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}
}
