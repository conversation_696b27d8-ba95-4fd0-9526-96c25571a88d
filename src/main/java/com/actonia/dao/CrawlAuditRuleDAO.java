package com.actonia.dao;

import java.util.List;

import com.actonia.entity.CrawlAuditRuleEntity;

public class CrawlAuditRuleDAO extends BaseJdbcSupport<CrawlAuditRuleEntity> {

	@Override
	public String getTableName() {
		return "crawl_audit_rule";
	}

	public List<CrawlAuditRuleEntity> getList() {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("   *");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		String sqlString = stringBuilder.toString();
		return this.findBySql(sqlString);
	}
}
