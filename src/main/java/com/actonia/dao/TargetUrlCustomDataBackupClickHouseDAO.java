package com.actonia.dao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import com.actonia.utils.AwsCredentialsEnvKeyConstructor;
import org.apache.commons.lang.StringUtils;

import com.actonia.IConstants;
import com.actonia.utils.FormatUtils;

import ru.yandex.clickhouse.ClickHouseDataSource;
import ru.yandex.clickhouse.settings.ClickHouseProperties;

// Due to ClickHouse concurrency issues, use Singleton pattern, do not use Spring (ie. all methods must be 'synchronized')
public class TargetUrlCustomDataBackupClickHouseDAO {

	//private boolean isDebug = false;
	private List<String> databaseHostnameList;
	private String databasePort = null;
	private String databaseName = null;
	private List<Connection> connectionList;
	private int batchCreationSize;
	private String databaseUser = null;
	private String databasePassword = null;
	private List<String> connectionUrlList = null;
	private int connectionTimeoutInMilliseconds;
	private int maximumRetryCounts;
	private int retryWaitMilliseconds;
	public static final String TABLE_NAME = "local_target_url_custom_data";

	private final String s3AccessKey;
	private final String s3SecretKey;

	public TargetUrlCustomDataBackupClickHouseDAO(String[] databaseHostnameArrayInput, String databasePortInput, String databaseNameInput, int batchCreationSizeInput,
			String databaseUserInput, String databasePasswordInput, int connectionTimeoutInMillisecondsInput, int maximumRetryCountsInput,
			int retryWaitMillisecondsInput) throws Exception {

		ClickHouseDataSource clickHouseDataSource = null;
		Connection connection = null;

		databaseHostnameList = Arrays.asList(databaseHostnameArrayInput);

		databasePort = databasePortInput;
		databaseName = databaseNameInput;
		batchCreationSize = batchCreationSizeInput;
		databaseUser = databaseUserInput;
		databasePassword = databasePasswordInput;
		connectionTimeoutInMilliseconds = connectionTimeoutInMillisecondsInput;
		maximumRetryCounts = maximumRetryCountsInput;
		retryWaitMilliseconds = retryWaitMillisecondsInput;

		FormatUtils.getInstance()
				.logMemoryUsage("TargetUrlHtmlDailyBackupClickHouseDAO() databaseHostnameList=" + databaseHostnameList.toString() + ",databasePort=" + databasePort
						+ ",databaseName=" + databaseName + ",batchCreationSize=" + batchCreationSize + ",databaseUser=" + databaseUser + ",databasePassword="
						+ databasePassword + ",connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ",maximumRetryCounts=" + maximumRetryCounts
						+ ",retryWaitMilliseconds=" + retryWaitMilliseconds + ",TABLE_NAME=" + TABLE_NAME);

		ClickHouseProperties clickHouseProperties = new ClickHouseProperties();
		clickHouseProperties.setDecompress(true);
		clickHouseProperties.setConnectionTimeout(connectionTimeoutInMilliseconds);
		clickHouseProperties.setSocketTimeout(connectionTimeoutInMilliseconds);

		connectionList = new ArrayList<Connection>();

		String connectionUrl = null;

		connectionUrlList = new ArrayList<String>();

		for (String databaseHostname : databaseHostnameList) {
			if (StringUtils.isNotBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
						+ "?user=" + databaseUser + "&password=" + databasePassword;
			} else {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH
						+ databaseName;
			}
			connectionUrlList.add(connectionUrl);
			FormatUtils.getInstance().logMemoryUsage("TargetUrlHtmlDailyBackupClickHouseDAO() connectionUrl=" + connectionUrl);
			clickHouseDataSource = new ClickHouseDataSource(connectionUrl, clickHouseProperties);
			connection = clickHouseDataSource.getConnection();
			connectionList.add(connection);
		}
		this.s3AccessKey = AwsCredentialsEnvKeyConstructor.getInstance().getPlainTextS3AccessKey();
		this.s3SecretKey = AwsCredentialsEnvKeyConstructor.getInstance().getS3DecryptedSecretKey();
	}

	@Override
	public String toString() {
		return "TargetUrlHtmlDailyBackupClickHouseDAO [databaseHostnameList=" + databaseHostnameList.toString() + ", databasePort=" + databasePort + ", databaseName="
				+ databaseName + ", batchCreationSize=" + batchCreationSize + ", databaseUser=" + databaseUser + ", databasePassword=" + databasePassword
				+ ", connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ", maximumRetryCounts=" + maximumRetryCounts + ", retryWaitMilliseconds="
				+ retryWaitMilliseconds + "]";
	}

	private synchronized String getTableName(String tableName) {
		if (StringUtils.isNotBlank(tableName)) {
			return tableName;
		} else {
			return TABLE_NAME;
		}
	}

	public Date getEarliestDailyDataCreationDate(String tableName) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		Date trackDate = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     min(daily_data_creation_date)");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getEarliestDailyDataCreationDate() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		//if (isDebug == true) {
		//	connectionUrl = connectionUrlList.get(connectionIndex);
		//	FormatUtils.getInstance().logMemoryUsage("getEarliestDailyDataCreationDate() connectionUrl=" + connectionUrl);
		//}
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				preparedStatement = connection.prepareStatement(sqlString);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					trackDate = resultSet.getTimestamp(1);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				e.printStackTrace();
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getEarliestDailyDataCreationDate() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			} finally {
				if (resultSet != null) {
					resultSet.close();
				}
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("getEarliestDailyDataCreationDate() trackDate=" + trackDate + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		return trackDate;
	}

	public Date getLatestDailyDataCreationDate(String tableName) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		Date trackDate = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     max(daily_data_creation_date)");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getLatestDailyDataCreationDate() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		//if (isDebug == true) {
		//	connectionUrl = connectionUrlList.get(connectionIndex);
		//	FormatUtils.getInstance().logMemoryUsage("getLatestDailyDataCreationDate() connectionUrl=" + connectionUrl);
		//}
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				preparedStatement = connection.prepareStatement(sqlString);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					trackDate = resultSet.getTimestamp(1);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				e.printStackTrace();
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getLatestDailyDataCreationDate() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			} finally {
				if (resultSet != null) {
					resultSet.close();
				}
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("getLatestDailyDataCreationDate() trackDate=" + trackDate + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		return trackDate;
	}

	public void backupToS3(String s3ObjectURI, String tableName, String dailyDataCreationDateString) throws Exception {
		//FormatUtils.getInstance().logMemoryUsage("backupToS3() begins. s3ObjectURI=" + s3ObjectURI + ",tableName=" + tableName + ",dailyDataCreationDateString="
		//		+ dailyDataCreationDateString);
		//long startTimestamp = System.currentTimeMillis();
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("insert into function s3('" + s3ObjectURI + "', ");
		stringBuilder.append("  '" + s3AccessKey + "', ");
		stringBuilder.append("  '" + s3SecretKey + "', ");
		stringBuilder.append("  'Native', ");
		stringBuilder.append(
				"  '`domain_id` UInt32, `url` String, `custom_data` String CODEC(LZ4HC(9)), `crawl_timestamp` DateTime, `daily_data_creation_date` Date, `url_hash` UInt64, `url_murmur_hash` UInt32', ");
		stringBuilder.append("   'zstd') ");
		stringBuilder.append("select * ");
		stringBuilder.append("from " + getTableName(tableName) + " ");
		stringBuilder.append("where daily_data_creation_date = '" + dailyDataCreationDateString + "'");
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("backupToS3() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		//if (isDebug == true) {
		//	connectionUrl = connectionUrlList.get(connectionIndex);
		//	FormatUtils.getInstance().logMemoryUsage("backupToS3() connectionUrl=" + connectionUrl);
		//}
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				preparedStatement = connection.prepareStatement(sqlString);
				resultSet = preparedStatement.executeQuery();
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				e.printStackTrace();
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("backupToS3() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			} finally {
				if (resultSet != null) {
					resultSet.close();
				}
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();
				}
			}
		}
		//FormatUtils.getInstance().logMemoryUsage("backupToS3() ends. s3ObjectURI=" + s3ObjectURI + ",tableName=" + tableName + ",dailyDataCreationDateString="
		//		+ dailyDataCreationDateString + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	public void restoreFromS3(String s3ObjectURI, String tableName) throws Exception {
		//FormatUtils.getInstance().logMemoryUsage("restoreFromS3() begins. s3ObjectURI=" + s3ObjectURI + ",tableName=" + tableName);
		//long startTimestamp = System.currentTimeMillis();
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("insert into " + getTableName(tableName) + " ");
		stringBuilder.append("select * ");
		stringBuilder.append("from s3('" + s3ObjectURI + "', ");
		stringBuilder.append("  '" + s3AccessKey + "', ");
		stringBuilder.append("  '" + s3SecretKey + "', ");
		stringBuilder.append("  'Native', ");
		stringBuilder.append(
				"  '`domain_id` UInt32, `url` String, `custom_data` String CODEC(LZ4HC(9)), `crawl_timestamp` DateTime, `daily_data_creation_date` Date, `url_hash` UInt64, `url_murmur_hash` UInt32', ");
		stringBuilder.append("   'zstd') ");
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("restoreFromS3() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		//if (isDebug == true) {
		//	connectionUrl = connectionUrlList.get(connectionIndex);
		//	FormatUtils.getInstance().logMemoryUsage("restoreFromS3() connectionUrl=" + connectionUrl);
		//}
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				preparedStatement = connection.prepareStatement(sqlString);
				resultSet = preparedStatement.executeQuery();
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				e.printStackTrace();
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("restoreFromS3() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			} finally {
				if (resultSet != null) {
					resultSet.close();
				}
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();
				}
			}
		}
		//FormatUtils.getInstance().logMemoryUsage("restoreFromS3() ends. s3ObjectURI=" + s3ObjectURI + ",tableName=" + tableName + ",elapsed(s.)="
		//		+ (System.currentTimeMillis() - startTimestamp) / 1000);
	}

}
