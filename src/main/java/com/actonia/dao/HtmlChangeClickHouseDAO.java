package com.actonia.dao;

import com.actonia.IConstants;
import com.actonia.entity.ChangeIndMaster;
import com.actonia.entity.HtmlChange;
import com.actonia.utils.FormatUtils;
import com.actonia.value.object.HtmlChangeResponse;
import com.actonia.value.object.TargetUrlChangeRequest;
import com.actonia.value.object.UrlSummary;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import ru.yandex.clickhouse.ClickHouseDataSource;
import ru.yandex.clickhouse.settings.ClickHouseProperties;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

import static com.actonia.value.object.HtmlChangeResponse.HtmlChangeAggregateModel;

// Due to ClickHouse concurrency issues, use Singleton pattern, do not use Spring (ie. all methods must be 'synchronized')
public class HtmlChangeClickHouseDAO {

	//private static boolean isDebug = false;
	public static final String TABLE_NAME = "dis_html_change";
	private static final int TOTAL_DAO_INSTANCES = 1;
	private static final Logger log = LogManager.getLogger(HtmlChangeClickHouseDAO.class);
	private static int daoMapIndex = 0;

	// map key = DAO index (0  - 59)
	// map value = instance of HtmlChangeClickHouseDAO
	private static Map<Integer, HtmlChangeClickHouseDAO> htmlChangeClickHouseDAOMap;

	private final List<String> databaseHostnameList;
	private final String databasePort;
	private final String databaseName;
	private final List<Connection> connectionList;
	private final List<String> connectionUrlList;
	private final int batchCreationSize;
	private final String databaseUser;
	private final String databasePassword;
	private final int connectionTimeoutInMilliseconds;
	private final int maximumRetryCounts;
	private final int retryWaitMilliseconds;
	private final int daoIndex;
	private final Map<Integer, ChangeIndMaster> changeIndMasterMap;

	private HtmlChangeClickHouseDAO(String[] databaseHostnameArrayInput, String databasePortInput, String databaseNameInput, int batchCreationSizeInput,
	                                String databaseUserInput, String databasePasswordInput, int connectionTimeoutInMillisecondsInput, int maximumRetryCountsInput,
	                                int retryWaitMillisecondsInput, int index) throws Exception {

		ClickHouseDataSource clickHouseDataSource = null;
		Connection connection = null;

		databaseHostnameList = Arrays.asList(databaseHostnameArrayInput);
		databasePort = databasePortInput;
		databaseName = databaseNameInput;
		batchCreationSize = batchCreationSizeInput;
		databaseUser = databaseUserInput;
		databasePassword = databasePasswordInput;
		connectionTimeoutInMilliseconds = connectionTimeoutInMillisecondsInput;
		maximumRetryCounts = maximumRetryCountsInput;
		retryWaitMilliseconds = retryWaitMillisecondsInput;
		daoIndex = index;

		ClickHouseProperties clickHouseProperties = new ClickHouseProperties();
		clickHouseProperties.setDecompress(true);
		clickHouseProperties.setConnectionTimeout(connectionTimeoutInMilliseconds);
		clickHouseProperties.setSocketTimeout(connectionTimeoutInMilliseconds);

		connectionList = new ArrayList<>();

		connectionUrlList = new ArrayList<>();

		String connectionUrl = null;
		for (String databaseHostname : databaseHostnameList) {
			if (StringUtils.isNotBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
						+ "?user=" + databaseUser + "&password=" + databasePassword;
			} else if (StringUtils.isBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
						+ "?password=" + databasePassword;
			} else {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH
						+ databaseName;
			}
			connectionUrlList.add(connectionUrl);
			//FormatUtils.getInstance().logMemoryUsage("HtmlChangeClickHouseDAO() connectionUrl=" + connectionUrl);
			clickHouseDataSource = new ClickHouseDataSource(connectionUrl, clickHouseProperties);
			connection = clickHouseDataSource.getConnection();
			connectionList.add(connection);
		}
		changeIndMasterMap = ChangeIndMasterClickHouseDAO.getInstance().changeIndMasterMap;
	}

	public static HtmlChangeClickHouseDAO getInstance() throws Exception {
		HtmlChangeClickHouseDAO htmlChangeClickHouseDAO;
		if (htmlChangeClickHouseDAOMap == null) {
			String clickHouseDatabaseHostnames = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_HOSTNAMES);
			String[] clickHouseDatabaseHostnameArray = clickHouseDatabaseHostnames.split(IConstants.COMMA);
			String clickHouseDatabasePort = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_PORT);
			String clickHouseDatabaseName = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_NAME);
			String clickHouseDatabaseUser = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_USER);
			String clickHouseDatabasePassword = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_PASSWORD);
			int clickHouseBatchCreationSize = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_BATCH_CREATION_SIZE, 100);
			int clickHouseConnectionTimeoutInMilliseconds = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_CONNECTION_TIMEOUT_MILLISECONDS, 88888);
			int clickHouseMaximumRetryCounts = ClickHouseCrawlDatabaseConfigurations.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_MAX_RETRY_COUNTS,
					3);
			int clickHouseRetryWaitMilliseconds = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_RETRY_WAIT_MILLISECONDS, 1000);
			FormatUtils.getInstance().logMemoryUsage("HtmlChangeClickHouseDAO() clickHouseDatabaseHostnames=" + clickHouseDatabaseHostnames
					+ ",clickHouseDatabasePort=" + clickHouseDatabasePort + ",clickHouseDatabaseName=" + clickHouseDatabaseName + ",clickHouseBatchCreationSize="
					+ clickHouseBatchCreationSize + ",clickHouseDatabaseUser=" + clickHouseDatabaseUser + ",clickHouseDatabasePassword=" + clickHouseDatabasePassword
					+ ",clickHouseConnectionTimeoutInMilliseconds=" + clickHouseConnectionTimeoutInMilliseconds + ",clickHouseMaximumRetryCounts="
					+ clickHouseMaximumRetryCounts + ",clickHouseRetryWaitMilliseconds=" + clickHouseRetryWaitMilliseconds + ",TABLE_NAME=" + TABLE_NAME);

			htmlChangeClickHouseDAOMap = new HashMap<>();
			for (int i = 0; i < TOTAL_DAO_INSTANCES; i++) {
				htmlChangeClickHouseDAO = initialize(clickHouseDatabaseHostnameArray, clickHouseDatabasePort, clickHouseDatabaseName, clickHouseDatabaseUser,
						clickHouseDatabasePassword, clickHouseBatchCreationSize, clickHouseConnectionTimeoutInMilliseconds, clickHouseMaximumRetryCounts,
						clickHouseRetryWaitMilliseconds, i);
				htmlChangeClickHouseDAOMap.put(i, htmlChangeClickHouseDAO);
			}
			FormatUtils.getInstance().logMemoryUsage("getInstance() total htmlChangeClickHouseDAOs=" + htmlChangeClickHouseDAOMap.size());
		}
		int index = getDaoMapIndex();
		htmlChangeClickHouseDAO = htmlChangeClickHouseDAOMap.get(index);
		return htmlChangeClickHouseDAO;
	}

	private static synchronized int getDaoMapIndex() {
		int index = 0;
		if (daoMapIndex >= TOTAL_DAO_INSTANCES) {
			daoMapIndex = 0;
		} else {
			index = daoMapIndex++;
		}
		return index;
	}

	// initialize HtmlChangeClickHouseDAO based on runtime clickhouse configurations
	private static HtmlChangeClickHouseDAO initialize(String[] clickHouseDatabaseHostnameArray, String clickHouseDatabasePort, String clickHouseDatabaseName,
	                                                  String clickHouseDatabaseUser, String clickHouseDatabasePassword, int clickHouseBatchCreationSize, int clickHouseconnectionTimeoutInMilliseconds,
	                                                  int clickHouseMaximumRetryCounts, int clickHouseRetryWaitMilliseconds, int daoIndex) throws Exception {
		//FormatUtils.getInstance().logMemoryUsage("initialize() htmlChangeClickHouseDAO=" + htmlChangeClickHouseDAO.toString());
		return new HtmlChangeClickHouseDAO(clickHouseDatabaseHostnameArray,
				clickHouseDatabasePort, clickHouseDatabaseName, clickHouseBatchCreationSize, clickHouseDatabaseUser, clickHouseDatabasePassword,
				clickHouseconnectionTimeoutInMilliseconds, clickHouseMaximumRetryCounts, clickHouseRetryWaitMilliseconds, daoIndex);
	}

	private static HtmlChange getHtmlChange(ResultSet resultSet) throws SQLException {
		HtmlChange htmlChange = new HtmlChange();
		htmlChange.setTrackDate(resultSet.getDate("track_date"));
		htmlChange.setUrlType(resultSet.getInt("url_type"));
		htmlChange.setDomainId(resultSet.getInt("domain_id"));
		htmlChange.setUrl(resultSet.getString("url"));
		htmlChange.setUrl_hash(resultSet.getString("url_hash"));
		htmlChange.setUrl_murmur_hash(resultSet.getString("url_murmur_hash"));
		htmlChange.setChgId(resultSet.getInt("chg_id"));
		htmlChange.setChange_indicator(resultSet.getString("chg_indicator"));
		htmlChange.setCriticalFlg(resultSet.getInt("critical_flg"));
		htmlChange.setChange_type(resultSet.getString("chg_type"));
		htmlChange.setPrevValue(resultSet.getString("prev_value"));
		htmlChange.setCurrValue(resultSet.getString("curr_value"));
		htmlChange.setCreateTimestamp(resultSet.getTimestamp("create_timestamp"));
		htmlChange.setPrevCrawlTimestamp(resultSet.getTimestamp("prev_crawl_timestamp"));
		htmlChange.setCurrCrawlTimestamp(resultSet.getTimestamp("curr_crawl_timestamp"));
		htmlChange.setPrevious_crawl_timestamp(resultSet.getString("prev_crawl_timestamp"));
		htmlChange.setCurrent_crawl_timestamp(resultSet.getString("curr_crawl_timestamp"));
		htmlChange.setPrevResponseCode(resultSet.getInt("prev_response_code"));
		htmlChange.setCurrResponseCode(resultSet.getInt("curr_response_code"));
		htmlChange.setResponse_code_previous(resultSet.getString("prev_response_code"));
		htmlChange.setResponse_code_current(resultSet.getString("curr_response_code"));
		return htmlChange;
	}

	@Override
	public String toString() {
		return "HtmlChangeClickHouseDAO [databaseHostnameList=" + databaseHostnameList.toString() + ", databasePort=" + databasePort + ", databaseName="
				+ databaseName + ", batchCreationSize=" + batchCreationSize + ", databaseUser=" + databaseUser + ", databasePassword=" + databasePassword
				+ ", connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ", maximumRetryCounts=" + maximumRetryCounts + ", retryWaitMilliseconds="
				+ retryWaitMilliseconds + ", daoIndex=" + daoIndex + "]";
	}

	private synchronized String getTableName(String tableName) {
		if (StringUtils.isNotBlank(tableName)) {
			return tableName;
		} else {
			return TABLE_NAME;
		}
	}

	public synchronized int getBatchCreationSize() {
		return batchCreationSize;
	}

	public synchronized void createBatch(List<HtmlChange> htmlChangeList) {
		final String sql = "insert into " + TABLE_NAME + " (track_date, url_type, domain_id, url, chg_id, prev_value, curr_value, prev_response_code, curr_response_code, prev_crawl_timestamp, curr_crawl_timestamp, create_timestamp) values (?,?,?,?,?,?,?,?,?,?,?,?)";
		int retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			Connection connection = connectionList.get(0);
			try (PreparedStatement preparedStatement = connection.prepareStatement(sql)) {
				for (HtmlChange htmlChange : htmlChangeList) {
					int preparedStatementIndex = 1;
					preparedStatement.setDate(preparedStatementIndex++, new java.sql.Date(htmlChange.getTrackDate().getTime()));
					preparedStatement.setInt(preparedStatementIndex++, htmlChange.getUrlType());
					preparedStatement.setInt(preparedStatementIndex++, htmlChange.getDomainId());
					preparedStatement.setString(preparedStatementIndex++, htmlChange.getUrl());
					preparedStatement.setInt(preparedStatementIndex++, htmlChange.getChgId());
					preparedStatement.setString(preparedStatementIndex++, htmlChange.getPrevValue());
					preparedStatement.setString(preparedStatementIndex++, htmlChange.getCurrValue());
					preparedStatement.setInt(preparedStatementIndex++, htmlChange.getPrevResponseCode());
					preparedStatement.setInt(preparedStatementIndex++, htmlChange.getCurrResponseCode());
					preparedStatement.setTimestamp(preparedStatementIndex++, new java.sql.Timestamp(htmlChange.getPrevCrawlTimestamp().getTime()));
					preparedStatement.setTimestamp(preparedStatementIndex++, new java.sql.Timestamp(htmlChange.getCurrCrawlTimestamp().getTime()));
					preparedStatement.setTimestamp(preparedStatementIndex++, new java.sql.Timestamp(htmlChange.getCreateTimestamp().getTime()));
					preparedStatement.addBatch();
				}
				preparedStatement.executeBatch();
				return;
			} catch (SQLException e) {
				log.error("batchCreateHtmlChange() sql=" + sql, e);
				retryCount++;
				try {
					Thread.sleep(retryWaitMilliseconds);
				} catch (InterruptedException e1) {
					log.error("batchCreateHtmlChange() Thread.sleep() error", e1);
				}
			}
		}
		log.error("batchCreateHtmlChange() retryCount={} sql=" + sql, retryCount);
		throw new RuntimeException("batchCreateHtmlChange() failed");
	}

	public synchronized List<HtmlChange> getHtmlChangeListByTime(int domainId, String startTrackDateString, String endTrackDateString, String startTimeStamp, String endTimeStamp) {
		List<HtmlChange> htmlChangeList = new ArrayList<>();
		final String sql = "select * from " + TABLE_NAME +
				" where url_type = 1 and domain_id = ? " +
				" and track_date >= ? and track_date <= ? " +
				" and curr_crawl_timestamp > '" + startTimeStamp + "' and curr_crawl_timestamp <= '" + endTimeStamp + "' ";
		Connection connection = connectionList.get(0);
		try (PreparedStatement preparedStatement = connection.prepareStatement(sql)) {
			int preparedStatementIndex = 1;
			preparedStatement.setInt(preparedStatementIndex++, domainId);
			preparedStatement.setString(preparedStatementIndex++, startTrackDateString);
			preparedStatement.setString(preparedStatementIndex++, endTrackDateString);
			final ResultSet resultSet = preparedStatement.executeQuery();
			while (resultSet.next()) {
				final HtmlChange htmlChange = getHtmlChange(resultSet);
				htmlChangeList.add(htmlChange);
			}
		} catch (SQLException e) {
			throw new RuntimeException(e);
		}
		return htmlChangeList;
	}

	public int calculateTotalRows(TargetUrlChangeRequest targetUrlChangeRequest, String filterCondition, boolean isUrlSummary) {
		final String startTimeStamp = targetUrlChangeRequest.getStart_crawl_timestamp();
		final String startTrackDateString = StringUtils.substringBefore(startTimeStamp, IConstants.ONE_SPACE);
		final String endTimeStamp = targetUrlChangeRequest.getEnd_crawl_timestamp();
		final String endTrackDateString = StringUtils.substringBefore(endTimeStamp, IConstants.ONE_SPACE);
		final StringBuilder stringBuilder = new StringBuilder().append("select ");
		if (isUrlSummary) {
			stringBuilder.append("countDistinct(url) as ");
		} else {
			stringBuilder.append("count(url) as ");
		}
		stringBuilder.append(IConstants.TOTAL).append(" from ").append(TABLE_NAME)
				.append(" where url_type = 1 and domain_id = ? ")
				.append(" and track_date >= '").append(startTrackDateString).append("' and track_date <= '").append(endTrackDateString).append("' ")
				.append(" and curr_crawl_timestamp >= '").append(startTimeStamp).append("' and curr_crawl_timestamp <= '").append(endTimeStamp).append("' ");
		if (StringUtils.isNotBlank(filterCondition)) {
			stringBuilder.append(filterCondition);
		}
		final String sql = stringBuilder.toString();
		log.info("calculateTotalRows() sql={}", sql);
		Connection connection = connectionList.get(0);
		int retry = 0;
		while (retry < maximumRetryCounts) {
			try (PreparedStatement preparedStatement = connection.prepareStatement(sql)) {
				preparedStatement.setInt(1, targetUrlChangeRequest.getDomain_id());
				final ResultSet resultSet = preparedStatement.executeQuery();
				if (resultSet.next()) {
					return resultSet.getInt(IConstants.TOTAL);
				}
			} catch (SQLException e) {
				log.error("calculateTotalRows() sql={}", sql, e);
				retry++;
				try {
					Thread.sleep(1000);
				} catch (InterruptedException e1) {
					log.error("calculateTotalRows() Thread.sleep() error", e1);
				}
			}
		}
		return 0;
	}

	public List<HtmlChange> getTargetUrlChangeIndicatorDetails(TargetUrlChangeRequest targetUrlChangeRequest, String filterCondition) {
		final String startTimeStamp = targetUrlChangeRequest.getStart_crawl_timestamp();
		final String startTrackDateString = StringUtils.substringBefore(startTimeStamp, IConstants.ONE_SPACE);
		final String endTimeStamp = targetUrlChangeRequest.getEnd_crawl_timestamp();
		final String endTrackDateString = StringUtils.substringBefore(endTimeStamp, IConstants.ONE_SPACE);
		final StringBuilder stringBuilder = new StringBuilder().append("select c.*, m.chg_indicator, m.chg_type ,m.critical_flg from ")
				.append(" (select distinct * from dis_html_change")
				.append(" where url_type = 1 and domain_id = ? ")
				.append(" and track_date >= '").append(startTrackDateString).append("' and track_date <= '").append(endTrackDateString).append("' ")
				.append(" and curr_crawl_timestamp >= '").append(startTimeStamp).append("' and curr_crawl_timestamp <= '").append(endTimeStamp).append("' ");
		if (StringUtils.isNotBlank(filterCondition)) {
			stringBuilder.append(filterCondition);
		}
		stringBuilder.append(") c join dis_change_ind_master m on c.chg_id = m.chg_id ");
		final Integer sortBy = targetUrlChangeRequest.getSort_by();
		stringBuilder.append(" order by ");
		switch (sortBy) {
			case IConstants.SORT_BY_URL_ASC:
				stringBuilder.append(" url, curr_crawl_timestamp");
				break;
			case IConstants.SORT_BY_URL_DESC:
				stringBuilder.append(" url desc, curr_crawl_timestamp");
				break;
			case IConstants.SORT_BY_CRAWL_TIMESTAMP_ASC:
				stringBuilder.append(" curr_crawl_timestamp, url");
				break;
			case IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC:
				stringBuilder.append(" curr_crawl_timestamp desc, url");
				break;
			case IConstants.SORT_BY_CHANGE_INDICATOR_ASC:
				stringBuilder.append(" chg_indicator, url");
				break;
			case IConstants.SORT_BY_CHANGE_INDICATOR_DESC:
				stringBuilder.append(" chg_indicator desc, url");
				break;
			case IConstants.SORT_BY_CHANGE_TYPE_ASC:
				stringBuilder.append(" chg_type, chg_indicator, url");
				break;
			case IConstants.SORT_BY_CHANGE_TYPE_DESC:
				stringBuilder.append(" chg_type desc, chg_indicator, url");
				break;
			case IConstants.SORT_BY_SEVERITY_ASC:
				stringBuilder.append(" critical_flg, chg_indicator, url");
				break;
			case IConstants.SORT_BY_SEVERITY_DESC:
				stringBuilder.append(" critical_flg desc, chg_indicator, url");
				break;
		}
		int offset = (targetUrlChangeRequest.getPage_number() - 1) * targetUrlChangeRequest.getRows_per_page();
		stringBuilder.append(" limit ").append(targetUrlChangeRequest.getRows_per_page()).append(" offset ").append(offset);

		final String sql = stringBuilder.toString();
		int retry = 0;
		Connection connection = connectionList.get(0);
		List<HtmlChange> htmlChangeList = new ArrayList<>();
		while (retry < maximumRetryCounts) {
			try (PreparedStatement preparedStatement = connection.prepareStatement(sql)) {
				preparedStatement.setInt(1, targetUrlChangeRequest.getDomain_id());
				final ResultSet resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					final HtmlChange htmlChange = getHtmlChange(resultSet);
					htmlChangeList.add(htmlChange);
				}
				log.info("getTargetUrlChangeIndicatorDetails size: {}", htmlChangeList.size());
				return htmlChangeList;
			} catch (Exception e) {
				log.error("calculateTotalRows() sql={}", sql, e);
				retry++;
				try {
					Thread.sleep(1000);
				} catch (InterruptedException e1) {
					log.error("calculateTotalRows() Thread.sleep() error", e1);
				}
			}
		}
		return htmlChangeList;
	}

	public List<HtmlChangeResponse.HtmlChangeAggregateModel> getHourlyTotals(TargetUrlChangeRequest targetUrlChangeRequest, String filterCondition) {
		final String startTimeStamp = targetUrlChangeRequest.getStart_crawl_timestamp();
		final String startTrackDateString = StringUtils.substringBefore(startTimeStamp, IConstants.ONE_SPACE);
		final String endTimeStamp = targetUrlChangeRequest.getEnd_crawl_timestamp();
		final String endTrackDateString = StringUtils.substringBefore(endTimeStamp, IConstants.ONE_SPACE);
		final StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("select c.*, m.chg_indicator as change_indicator, m.chg_type, m.critical_flg from (select substring(toString(curr_crawl_timestamp), 1, 13) as crawl_date_hour, chg_id, count(url) as total from ")
				.append(TABLE_NAME)
				.append(" where url_type = 1 and domain_id = ? ")
				.append(" and track_date >= '").append(startTrackDateString).append("' and track_date <= '").append(endTrackDateString).append("' ")
				.append(" and curr_crawl_timestamp >= '").append(startTimeStamp).append("' and curr_crawl_timestamp <= '").append(endTimeStamp).append("' ");
		if (StringUtils.isNotBlank(filterCondition)) {
			stringBuilder.append(filterCondition);
		}
		stringBuilder.append(" group by crawl_date_hour, chg_id order by crawl_date_hour, chg_id")
				.append(") c join dis_change_ind_master m on c.chg_id = m.chg_id");
		int retry = 0;
		Connection connection = connectionList.get(0);
		List<HtmlChangeAggregateModel> htmlChangeAggregateModelList = new ArrayList<>();
		while (retry < maximumRetryCounts) {
			try (PreparedStatement preparedStatement = connection.prepareStatement(stringBuilder.toString())) {
				preparedStatement.setInt(1, targetUrlChangeRequest.getDomain_id());
				final ResultSet resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					HtmlChangeAggregateModel htmlChangeAggregateModel = new HtmlChangeAggregateModel();
					htmlChangeAggregateModel.setCrawlDateHour(resultSet.getString("crawl_date_hour"));
					htmlChangeAggregateModel.setChgId(resultSet.getInt("chg_id"));
					htmlChangeAggregateModel.setChangeIndicator(resultSet.getString("change_indicator"));
					htmlChangeAggregateModel.setTotal(resultSet.getInt("total"));
					htmlChangeAggregateModel.setChangeType(resultSet.getString("chg_type"));
					htmlChangeAggregateModel.setCriticalFlg(resultSet.getInt("critical_flg"));
					htmlChangeAggregateModelList.add(htmlChangeAggregateModel);
				}
				return htmlChangeAggregateModelList;
			} catch (SQLException e) {
				log.error("calculateTotalRows() sql={}", stringBuilder.toString(), e);
				retry++;
				try {
					Thread.sleep(1000);
				} catch (InterruptedException e1) {
					log.error("calculateTotalRows() Thread.sleep() error", e1);
				}
			}
		}
		return htmlChangeAggregateModelList;
	}

	public List<UrlSummary> getUrlSummaryList(TargetUrlChangeRequest targetUrlChangeRequest, String filterCondition) {
		final String startCrawlTimestamp = targetUrlChangeRequest.getStart_crawl_timestamp();
		final String endCrawlTimestamp = targetUrlChangeRequest.getEnd_crawl_timestamp();
		final String startTrackDateString = StringUtils.substringBefore(startCrawlTimestamp, IConstants.ONE_SPACE);
		final String endTrackDateString = StringUtils.substringBefore(endCrawlTimestamp, IConstants.ONE_SPACE);
		final int rowsPerPage = targetUrlChangeRequest.getRows_per_page();
		final int offset = (targetUrlChangeRequest.getPage_number() - 1) * rowsPerPage;
		final int sortBy = targetUrlChangeRequest.getSort_by();
		List<UrlSummary> urlSummaryList = new ArrayList<>();
		final StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("select url, url_murmur_hash, count(*) as total_changes,")
				.append(" sum(case when critical_flg = 1 then 1 else 0 end) as total_severity_critical,")
				.append(" sum(case when critical_flg = 2 then 1 else 0 end) as total_severity_high,")
				.append(" sum(case when critical_flg = 3 then 1 else 0 end) as total_severity_medium,")
				.append(" sum(case when critical_flg = 4 then 1 else 0 end) as total_severity_low,")
				.append(" sum(case when chg_type = 'added' then 1 else 0 end) as total_change_type_added,")
				.append(" sum(case when chg_type = 'modified' then 1 else 0 end) as total_change_type_modified,")
				.append(" sum(case when chg_type = 'removed' then 1 else 0 end) as total_change_type_removed")
				.append(" from (select c.*, m.chg_type, m.critical_flg from ")
				.append(" (select distinct url, url_murmur_hash, chg_id from dis_html_change where url_type = 1 and domain_id = ? ")
				.append(" and track_date >= '").append(startTrackDateString).append("' and track_date <= '").append(endTrackDateString).append("'")
				.append(" and curr_crawl_timestamp >= '").append(startCrawlTimestamp).append("' and curr_crawl_timestamp <= '").append(endCrawlTimestamp).append("'");
		if (StringUtils.isNotBlank(filterCondition)) {
			stringBuilder.append(filterCondition);
		}
		stringBuilder.append(") c join dis_change_ind_master m on c.chg_id = m.chg_id)")
				.append(" group by url, url_murmur_hash order by ");
		switch (sortBy) {
			case IConstants.SORT_BY_URL_ASC:
				stringBuilder.append(" url");
				break;
			case IConstants.SORT_BY_URL_DESC:
				stringBuilder.append(" url desc");
				break;
			case IConstants.SORT_BY_TOTAL_CHANGES_ASC:
				stringBuilder.append(" total_changes, url");
				break;
			case IConstants.SORT_BY_TOTAL_CHANGES_DESC:
				stringBuilder.append(" total_changes desc, url");
				break;
			case IConstants.SORT_BY_TOTAL_SEVERITY_CRITICAL_ASC:
				stringBuilder.append(" total_severity_critical, url");
				break;
			case IConstants.SORT_BY_TOTAL_SEVERITY_CRITICAL_DESC:
				stringBuilder.append(" total_severity_critical desc, url");
				break;
			case IConstants.SORT_BY_TOTAL_SEVERITY_HIGH_ASC:
				stringBuilder.append(" total_severity_high, url");
				break;
			case IConstants.SORT_BY_TOTAL_SEVERITY_HIGH_DESC:
				stringBuilder.append(" total_severity_high desc, url");
				break;
			case IConstants.SORT_BY_TOTAL_SEVERITY_MEDIUM_ASC:
				stringBuilder.append(" total_severity_medium, url");
				break;
			case IConstants.SORT_BY_TOTAL_SEVERITY_MEDIUM_DESC:
				stringBuilder.append(" total_severity_medium desc, url");
				break;
			case IConstants.SORT_BY_TOTAL_SEVERITY_LOW_ASC:
				stringBuilder.append(" total_severity_low, url");
				break;
			case IConstants.SORT_BY_TOTAL_SEVERITY_LOW_DESC:
				stringBuilder.append(" total_severity_low desc, url");
				break;
			case IConstants.SORT_BY_TOTAL_CHANGE_TYPE_ADDED_ASC:
				stringBuilder.append(" total_change_type_added, url");
				break;
			case IConstants.SORT_BY_TOTAL_CHANGE_TYPE_ADDED_DESC:
				stringBuilder.append(" total_change_type_added desc, url");
				break;
			case IConstants.SORT_BY_TOTAL_CHANGE_TYPE_MODIFIED_ASC:
				stringBuilder.append(" total_change_type_modified, url");
				break;
			case IConstants.SORT_BY_TOTAL_CHANGE_TYPE_MODIFIED_DESC:
				stringBuilder.append(" total_change_type_modified desc, url");
				break;
			case IConstants.SORT_BY_TOTAL_CHANGE_TYPE_REMOVED_ASC:
				stringBuilder.append(" total_change_type_removed, url");
				break;
			case IConstants.SORT_BY_TOTAL_CHANGE_TYPE_REMOVED_DESC:
				stringBuilder.append(" total_change_type_removed desc, url");
				break;
		}
		stringBuilder.append(" limit ").append(rowsPerPage).append(" offset ").append(offset);
		int retry = 0;
		Connection connection = connectionList.get(0);
		while (retry < maximumRetryCounts) {
			try (PreparedStatement prepareStatement = connection.prepareStatement(stringBuilder.toString())) {
				prepareStatement.setInt(1, targetUrlChangeRequest.getDomain_id());
				ResultSet resultSet = prepareStatement.executeQuery();
				while (resultSet.next()) {
					UrlSummary urlSummary = new UrlSummary();
					urlSummary.setUrl(resultSet.getString("url"));
					urlSummary.setUrl_murmur_hash(resultSet.getString("url_murmur_hash"));
					urlSummary.setTotal_changes(resultSet.getInt("total_changes"));
					urlSummary.setTotal_severity_critical(resultSet.getInt("total_severity_critical"));
					urlSummary.setTotal_severity_high(resultSet.getInt("total_severity_high"));
					urlSummary.setTotal_severity_medium(resultSet.getInt("total_severity_medium"));
					urlSummary.setTotal_severity_low(resultSet.getInt("total_severity_low"));
					urlSummary.setTotal_change_type_added(resultSet.getInt("total_change_type_added"));
					urlSummary.setTotal_change_type_modified(resultSet.getInt("total_change_type_modified"));
					urlSummary.setTotal_change_type_removed(resultSet.getInt("total_change_type_removed"));
					urlSummaryList.add(urlSummary);
				}
				return urlSummaryList;
			} catch (SQLException e) {
				log.error("getUrlSummaryList() sql={}", stringBuilder.toString(), e);
				retry++;
				try {
					Thread.sleep(1000);
				} catch (InterruptedException e1) {
					log.error("getUrlSummaryList() Thread.sleep() error", e1);
				}
			}
		}

		return urlSummaryList;
	}

	public synchronized List<HtmlChangeAggregateModel> getUrlsWithMostChanges(TargetUrlChangeRequest targetUrlChangeRequest) {
		final String startCrawlTimestamp = targetUrlChangeRequest.getStart_crawl_timestamp();
		final String trackDate = startCrawlTimestamp.substring(0, 10);
		final String endCrawlTimestamp = targetUrlChangeRequest.getEnd_crawl_timestamp();
		final Integer domainId = targetUrlChangeRequest.getDomain_id();
		log.info("getUrlsWithMostChanges() domain_id={}", domainId);
		List<HtmlChangeAggregateModel> htmlChangeAggregateModels = new ArrayList<>();
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select url, count() as total_changes from dis_html_change where url_type = 1 and domain_id = ? ")
				.append(" and track_date = '").append(trackDate).append("' and curr_crawl_timestamp >= '").append(startCrawlTimestamp).append("' and curr_crawl_timestamp <= '").append(endCrawlTimestamp).append("' ");
		final String requestCondition = getRequestCondition(targetUrlChangeRequest);
		if (StringUtils.isNotBlank(requestCondition)) {
			stringBuilder.append(requestCondition);
		}
		stringBuilder.append(" group by url order by total_changes desc limit 20");
		log.info("getUrlsWithMostChanges() sql={}", stringBuilder.toString());
		int retry = 0;
		Connection connection = connectionList.get(0);
		while (retry < maximumRetryCounts) {
			try (PreparedStatement prepareStatement = connection.prepareStatement(stringBuilder.toString())) {
				prepareStatement.setInt(1, domainId);
				ResultSet resultSet = prepareStatement.executeQuery();
				while (resultSet.next()) {
					HtmlChangeAggregateModel htmlChangeAggregateModel = new HtmlChangeAggregateModel();
					htmlChangeAggregateModel.setUrl(resultSet.getString("url"));
					htmlChangeAggregateModel.setTotal(resultSet.getInt("total_changes"));
					htmlChangeAggregateModels.add(htmlChangeAggregateModel);
				}
				return htmlChangeAggregateModels;
			} catch (SQLException e) {
				log.error("getUrlsWithMostChanges() sql={}", stringBuilder.toString(), e);
				retry++;
				try {
					Thread.sleep(1000);
				} catch (InterruptedException e1) {
					log.error("getUrlsWithMostChanges() Thread.sleep() error", e1);
				}
			}
		}
		return htmlChangeAggregateModels;
	}

	private static String getRequestCondition(TargetUrlChangeRequest targetUrlChangeRequest) {
		StringBuilder stringBuilder = new StringBuilder();
		Integer[] pageTagIds = targetUrlChangeRequest.getPage_tag_ids();
		if (pageTagIds != null && pageTagIds.length > 0) {
			stringBuilder.append(" and (");
			for (int i = 0; i < pageTagIds.length; i++) {
				stringBuilder.append("(dictGetUInt64('file_dic_page_tag_murmur3hash', 'target_url_id', (toUInt64(")
						.append(targetUrlChangeRequest.getDomain_id()).append("), toUInt64(").append(pageTagIds[i]).append("), murmurHash3_64(url))) > 0)");
				if (i < pageTagIds.length - 1) {
					stringBuilder.append(" or ");
				}
			}
			stringBuilder.append(" )");
		}
		final String changeIndicatorsCondition = targetUrlChangeRequest.getChangeIndicatorsCondition();
		if (StringUtils.isNotBlank(changeIndicatorsCondition)) {
			stringBuilder.append(" and chg_id in (").append(changeIndicatorsCondition).append(")");
		}
		return stringBuilder.toString();
	}

	public List<HtmlChange> getHtmlChangeListByUrl(TargetUrlChangeRequest targetUrlChangeRequest, String url) {
		final String startCrawlTimestamp = targetUrlChangeRequest.getStart_crawl_timestamp();
		final String trackDate = startCrawlTimestamp.substring(0, 10);
		final String endCrawlTimestamp = targetUrlChangeRequest.getEnd_crawl_timestamp();
		final Integer domainId = targetUrlChangeRequest.getDomain_id();
		final StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("select m.chg_indicator, c.* from (select distinct url, url_hash, url_murmur_hash, chg_id, prev_value, curr_value, curr_crawl_timestamp, prev_crawl_timestamp, prev_response_code, curr_response_code from dis_html_change where url_type = 1 and domain_id =? ")
				.append(" and track_date = '").append(trackDate).append("' and curr_crawl_timestamp >= '").append(startCrawlTimestamp).append("' and curr_crawl_timestamp <= '").append(endCrawlTimestamp).append("' ");
		final String requestCondition = getRequestCondition(targetUrlChangeRequest);
		if (StringUtils.isNotBlank(requestCondition)) {
			stringBuilder.append(requestCondition);
		}
		stringBuilder.append(" and url = ?)c any join dis_change_ind_master m on c.chg_id = m.chg_id");
		log.info("getHtmlChangeListByUrl() sql={}", stringBuilder.toString());
		Connection connection = connectionList.get(0);
		int retry = 0;
		while (retry < maximumRetryCounts) {
			try (PreparedStatement prepareStatement = connection.prepareStatement(stringBuilder.toString())) {
				int parameterIndex = 1;
				prepareStatement.setInt(parameterIndex++, domainId);
				prepareStatement.setString(parameterIndex++, url);
				ResultSet resultSet = prepareStatement.executeQuery();
				List<HtmlChange> htmlChangeList = new ArrayList<>();
				while (resultSet.next()) {
					HtmlChange htmlChange = new HtmlChange();
					htmlChange.setUrl(resultSet.getString("url"));
					htmlChange.setUrl_hash(resultSet.getString("url_hash"));
					htmlChange.setUrl_murmur_hash(resultSet.getString("url_murmur_hash"));
					htmlChange.setChange_indicator(resultSet.getString("chg_indicator"));
					htmlChange.setPrevValue(resultSet.getString("prev_value"));
					htmlChange.setCurrValue(resultSet.getString("curr_value"));
					htmlChange.setResponse_code_previous(resultSet.getString("prev_response_code"));
					htmlChange.setResponse_code_current(resultSet.getString("curr_response_code"));
					htmlChange.setPrevCrawlTimestamp(resultSet.getTimestamp("prev_crawl_timestamp"));
					htmlChange.setCurrCrawlTimestamp(resultSet.getTimestamp("curr_crawl_timestamp"));
					htmlChangeList.add(htmlChange);
				}
				return htmlChangeList;
			} catch (SQLException e) {
				retry++;
				log.error("getHtmlChangeListByUrl() sql={}", stringBuilder.toString(), e);
			}
		}

		return Collections.emptyList();
	}

	public List<HtmlChangeAggregateModel> getAlertSummaryList(TargetUrlChangeRequest targetUrlChangeRequest) {
		final String startCrawlTimestamp = targetUrlChangeRequest.getStart_crawl_timestamp();
		final String trackDate = startCrawlTimestamp.substring(0, 10);
		final String endCrawlTimestamp = targetUrlChangeRequest.getEnd_crawl_timestamp();
		final Integer domainId = targetUrlChangeRequest.getDomain_id();
		final StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("select m.chg_indicator, c.* from (select chg_id, count(*) as total from dis_html_change where url_type = 1 and domain_id = ? ")
				.append(" and track_date = '").append(trackDate).append("' and curr_crawl_timestamp >= '").append(startCrawlTimestamp).append("' and curr_crawl_timestamp <= '").append(endCrawlTimestamp).append("' ");
		final String requestCondition = getRequestCondition(targetUrlChangeRequest);
		if (StringUtils.isNotBlank(requestCondition)) {
			stringBuilder.append(requestCondition);
		}
		stringBuilder.append(" group by chg_id)c any join dis_change_ind_master m on c.chg_id = m.chg_id");
		log.info("getAlertSummaryList() sql={}", stringBuilder.toString());
		Connection connection = connectionList.get(0);
		int retry = 0;
		while (retry < maximumRetryCounts) {
			try (PreparedStatement prepareStatement = connection.prepareStatement(stringBuilder.toString())) {
				int parameterIndex = 1;
				prepareStatement.setInt(parameterIndex++, domainId);
				ResultSet resultSet = prepareStatement.executeQuery();
				List<HtmlChangeAggregateModel> htmlChangeAggregateModels = new ArrayList<>();
				while (resultSet.next()) {
					HtmlChangeAggregateModel htmlChangeAggregateModel = new HtmlChangeAggregateModel();
					htmlChangeAggregateModel.setChangeIndicator(resultSet.getString("chg_indicator"));
					htmlChangeAggregateModel.setTotal(resultSet.getInt("total"));
					htmlChangeAggregateModels.add(htmlChangeAggregateModel);
				}
				return htmlChangeAggregateModels;
			} catch (SQLException e) {
				retry++;
				log.error("getAlertSummaryList() sql={}", stringBuilder.toString(), e);
			}
		}
		return Collections.emptyList();
	}

}