package com.actonia.dao;

import java.util.List;

import com.actonia.entity.EngineCountryLanguageMappingEntity;

public class EngineCountryLanguageMappingEntityDAO extends BaseJdbcSupport<EngineCountryLanguageMappingEntity> {

	@Override
	public String getTableName() {
		return "engine_country_language_mapping";
	}

	public List<EngineCountryLanguageMappingEntity> getAll() {
		String sql = "select * from " + getTableName() + " where enabled=?";
		return this.findBySql(sql.toString(), EngineCountryLanguageMappingEntity.ENABLED);
	}

	public List<EngineCountryLanguageMappingEntity> getByEngineLanguageId(int engineId, int languageId) {
		String sql = "select * from " + getTableName() + " where enabled=? and engineId=? and languageId=?";
		return this.findBySql(sql.toString(), EngineCountryLanguageMappingEntity.ENABLED, engineId, languageId);
	}

	public List<EngineCountryLanguageMappingEntity> getByEngineLanguageIdForCountry(int engineId, int languageId, String countryQueryName) {
		String sql = "select * from " + getTableName() + " where enabled=? and engineId=? and languageId=? and countryQueryName = ?";
		return this.findBySql(sql.toString(), EngineCountryLanguageMappingEntity.ENABLED, engineId, languageId, countryQueryName);
	}

	public String getCountryCodeByLanguageId(int languageId) {
		String sql = "select countryQueryName from " + getTableName() + " where enabled=? and validForSV=1 and languageId=? limit 1";
		return queryForString(sql, EngineCountryLanguageMappingEntity.ENABLED, languageId);
	}

	public EngineCountryLanguageMappingEntity getEngineDisplayNameLanguageDisplayName(String engineDisplayName, String languageDisplayName) {
		String sql = "select * from " + getTableName() + " where enabled = ? and engineDisplayName = ? and languageDisplayName = ?";
		return this.findObject(sql, EngineCountryLanguageMappingEntity.ENABLED, engineDisplayName, languageDisplayName);
	}

	public List<EngineCountryLanguageMappingEntity> findAll(boolean onlyEnabled, Integer ownDomainId) {
		StringBuilder sql = new StringBuilder();
		sql.append(" select e.* ");
		sql.append(" from ").append(getTableName()).append(" e ");
		if (ownDomainId != null) {
			sql.append(" join t_own_domain od on e.rankFrom = od.rank_from"); // Edwin - https://www.wrike.com/open.htm?id=601303443
			sql.append(" and od.id = ").append(ownDomainId);
		}
		sql.append(" where 1 = 1 ");

		//Cee - https://www.wrike.com/open.htm?id=336271109
		sql.append(" and ( concat(e.engineId, '-', e.languageId) != '5-8' ) ");
		//rankFrom = 1

		if (onlyEnabled) {
			sql.append(" and e.enabled = 1 ");
		}
		sql.append(" order by e.countryQueryName, e.engineQueryName, e.languageQueryName ");

		return findBySql(sql.toString());
	}
}