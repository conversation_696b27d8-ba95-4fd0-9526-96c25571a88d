package com.actonia.dao;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

import com.actonia.IConstants;
import com.actonia.entity.UserBacklink;

public class UserBacklinkDAO extends BaseJdbcSupport<UserBacklink> {

	@Override
	public String getTableName() {
		return "usr_backlink";
	}

	protected String getTableName(int domainId) {
		return IConstants.TABLE_NAME_USER_BACKLINK + domainId;
	}

	protected String getPartnerDomainTableName(int domainId) {
		return IConstants.TABLE_NAME_USER_PARTNER_DOMAIN + domainId;
	}

	//Leo - https://www.wrike.com/open.htm?id=147573475
	public List<UserBacklink> getLinkClarityPageMultiSortColumnFilters(int ownDomainId, int competitorId, boolean isShowGoogleWebmasterPartnerurls,
			Integer partnerUrlStatusCd, Integer partnerUrlRelationshipStatus, Integer targetUrlStatusCd, Integer partnerUrlAcquisitionType, int assignto) {
		//		if (ownDomain == null || ownDomain.getId() == null) {
		//			return null;
		//		}
		//
		//		int domainId = ownDomain.getId();

		//Page query
		StringBuilder stringBuilder = new StringBuilder();
		List<Object> params = new ArrayList<Object>();

		stringBuilder.append(" select usr_backlink.* ");
		stringBuilder.append(" from ").append(getTableName(ownDomainId)).append(" usr_backlink ");
		stringBuilder.append(" where usr_backlink.competitor_id = ? ");
		params.add(competitorId);

		if (assignto == 1) {
			stringBuilder.append(" and usr_backlink.partner_url_assigned_to is not null ");
		} else if (assignto == 2) {
			stringBuilder.append(" and usr_backlink.partner_url_assigned_to is null ");
		}

		if (isShowGoogleWebmasterPartnerurls == false) {
			stringBuilder.append(" and usr_backlink.partner_url_acquisition_src != 3 ");
		}

		if (partnerUrlStatusCd != null) {
			if (partnerUrlStatusCd == 200 || partnerUrlStatusCd >= 600) {
				stringBuilder.append(" and usr_backlink.partner_url_status_cd = ? ");
				params.add(partnerUrlStatusCd);
			} else if (partnerUrlStatusCd > 100) {
				stringBuilder.append(" and usr_backlink.partner_url_status_cd > ? and usr_backlink.partner_url_status_cd <= ? ");
				params.add(partnerUrlStatusCd - 100);
				params.add(partnerUrlStatusCd);
			} else {
				stringBuilder.append(" and usr_backlink.partner_url_status_cd is null ");
			}
		}

		if (partnerUrlRelationshipStatus != null) {
			if (partnerUrlRelationshipStatus > 0) {
				stringBuilder.append(" and usr_backlink.partner_url_relationship_status = ? ");
				params.add(partnerUrlRelationshipStatus);
			} else {
				stringBuilder.append(" and usr_backlink.partner_url_relationship_status is null ");
			}
		}

		if (targetUrlStatusCd != null) {
			if (targetUrlStatusCd > 0) {
				stringBuilder.append(" and usr_backlink.target_url_status_cd = ? ");
				params.add(targetUrlStatusCd);
			} else {
				stringBuilder.append(" and usr_backlink.target_url_status_cd is null ");
			}
		}

		if (partnerUrlAcquisitionType != null) {
			if (partnerUrlAcquisitionType > 0) {
				stringBuilder.append(" and usr_backlink.partner_url_acquisition_type = ? ");
				params.add(partnerUrlAcquisitionType);
			} else {
				stringBuilder.append(" and usr_backlink.partner_url_acquisition_type is null ");
			}
		}

		return findBySql(stringBuilder.toString(), params.toArray());
	}

	public List<Map> getReferringDomainsPageData(int domainId, boolean isShowGoogleWebmasterPartnerurls) {

		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select ");
		stringBuilder.append(" 	usr_backlink.partner_domain, ");
		stringBuilder.append(" 	usr_partner_domain.alexa_rank, ");
		stringBuilder.append(" 	usr_partner_domain.ext_back_links, ");
		stringBuilder.append(" 	usr_partner_domain.citation_flow, ");
		stringBuilder.append(" 	usr_partner_domain.trust_flow, ");
		stringBuilder.append(" 	usr_partner_domain.country_code, ");
		stringBuilder.append(" 	usr_partner_domain.ip_address, ");
		stringBuilder.append(" 	usr_partner_domain.subnet_ip_address, ");
		stringBuilder.append(" 	count(usr_backlink.partner_url) as backlink_to_client_domain, ");
		stringBuilder.append(" 	count(distinct usr_backlink.target_url) as unique_client_domain_target_urls ");
		stringBuilder.append(" from ").append(getTableName(domainId)).append(" usr_backlink ");
		stringBuilder.append("    	left join ").append(getPartnerDomainTableName(domainId)).append(" usr_partner_domain ");
		stringBuilder.append("    		on usr_backlink.partner_domain = usr_partner_domain.partner_domain ");

		if (isShowGoogleWebmasterPartnerurls == false) {
			stringBuilder.append(" where usr_backlink.partner_url_acquisition_src != 3 ");
		}

		stringBuilder.append(" group by ");
		stringBuilder.append(" 	usr_backlink.partner_domain ");

		return this.queryForMapList(stringBuilder.toString());
	}

	public List<Map> getAnchorTextPageData(int domainId, boolean isShowGoogleWebmasterPartnerurls) {

		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select ");
		stringBuilder.append(" 	usr_backlink.anchor_txt, ");
		stringBuilder.append(" 	count(*) as total_backlinks, ");
		stringBuilder.append(" 	sum(case usr_backlink.date_lost is not null when 1 then 1 else 0 end ) as total_deleted, ");
		stringBuilder.append(" 	sum(case usr_backlink.flag_no_follow when 1 then 1 else 0 end ) as total_no_follow, ");
		stringBuilder.append(" 	count(distinct(usr_backlink.target_url)) as total_distinct_target_urls, ");
		stringBuilder.append(" 	count(distinct(usr_backlink.partner_domain)) as total_distinct_referring_domains, ");
		stringBuilder.append(" 	usr_backlink.link_type ");
		stringBuilder.append(" from ").append(getTableName(domainId)).append(" usr_backlink ");
		if (isShowGoogleWebmasterPartnerurls == false) {
			stringBuilder.append(" where usr_backlink.partner_url_acquisition_src != 3 ");
		}
		stringBuilder.append(" group by ");
		stringBuilder.append(" 	usr_backlink.anchor_txt, usr_backlink.link_type ");

		return this.queryForMapList(stringBuilder.toString());
	}

	public Integer getTargetUrlCount(int domainId, String targetUrlMd5HashList) {

		Map<String, Integer> output = new HashMap<String, Integer>();

		String md5Hash = null;
		BigInteger tempBigInteger = null;
		Integer totalNumberOfRecords = null;

		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("select ");
		stringBuilder.append("    count(*) ");
		stringBuilder.append("from " + getTableName(domainId) + " usr_backlink ");
		stringBuilder.append("where ");
		stringBuilder.append("    usr_backlink.target_url_md5_hash = ? ");
		stringBuilder.append("group by ");
		stringBuilder.append("    usr_backlink.target_url_md5_hash ");
		String sqlString = stringBuilder.toString();

		return this.queryForInteger(sqlString, targetUrlMd5HashList);
	}

	public Integer getTotalPartnerLinks(int domainId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("select ");
		stringBuilder.append("    count(*) ");
		stringBuilder.append("from " + getTableName(domainId) + " usr_backlink ");
		String sqlString = stringBuilder.toString();
		return this.queryForInteger(sqlString);
	}

	public List<UserBacklink> getPotentialLinks(int ownDomainId) {
		int competitorId = 0;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select ");
		stringBuilder.append("     usr_backlink.partner_url, ");
		stringBuilder.append("     usr_backlink.partner_domain, ");
		stringBuilder.append("     usr_backlink.anchor_txt, ");
		stringBuilder.append("     usr_backlink.target_url ");
		stringBuilder.append(" from ").append(getTableName(ownDomainId)).append(" usr_backlink ");
		stringBuilder.append(" where ");
		stringBuilder.append("     usr_backlink.competitor_id = ? ");
		stringBuilder.append(" and usr_backlink.partner_url_relationship_status = ? ");
		return findBySql(stringBuilder.toString(), competitorId, UserBacklink.PARTNER_URL_RELATIONSHIP_STATUS_POTENTIAL);
	}

	public int reset(int domainId, String tableName) {
		String sqlString = "drop table if exists " + tableName + domainId;
		int returnCode = executeUpdate(sqlString);
		return returnCode;
	}

	public List<UserBacklink> getUniqueSourceUrls(int ownDomainId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select distinct");
		stringBuilder.append("     partner_url");
		stringBuilder.append(" from ").append(getTableName(ownDomainId));
		return findBySql(stringBuilder.toString());
	}

}
