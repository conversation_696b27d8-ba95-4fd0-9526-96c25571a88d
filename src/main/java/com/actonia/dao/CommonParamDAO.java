/**
 * 
 */
package com.actonia.dao;

import java.util.List;

import com.actonia.entity.CommonParamEntity;
import com.actonia.entity.OwnDomainEntity;

public class CommonParamDAO extends BaseJdbcSupport<CommonParamEntity> {

	@Override
	public String getTableName() {
		return "common_param";
	}

	public CommonParamEntity getById(int ownDomainId, long commParamId) {
		String sql = "select * from common_param where id = ? and ownDomainId = ? ";

		return findObject(sql, commParamId, ownDomainId);
	}

	public Integer getByFuncName(int ownDomainId, String funcName) {
		StringBuilder sql = new StringBuilder();
		sql.append(" select count(1) ");
		sql.append(" from common_param ");
		sql.append(" where ownDomainId = ? ");
		sql.append(" and funcName = ? ");

		return queryForInteger(sql.toString(), ownDomainId, funcName);
	}

	public CommonParamEntity getUiJsonByFuncNameTitle(int domainId, String funcName, String title) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     version,");
		stringBuilder.append("     uiJson");
		stringBuilder.append(" from");
		stringBuilder.append("     common_param");
		stringBuilder.append(" where");
		stringBuilder.append("     ownDomainId = ?");
		stringBuilder.append(" and funcName = ?");
		stringBuilder.append(" and title = ?");
		String sqlString = stringBuilder.toString();
		return findObject(sqlString, domainId, funcName, title);
	}

	public List<CommonParamEntity> getAllParamJsons(String functionName) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     common_param.id,");
		stringBuilder.append("     common_param.ownDomainId,");
		stringBuilder.append("     common_param.paramJson");
		stringBuilder.append(" from");
		stringBuilder.append("     t_own_domain t_own_domain,");
		stringBuilder.append("     common_param common_param");
		stringBuilder.append(" where");
		stringBuilder.append("     t_own_domain.`status` = ?");
		stringBuilder.append(" and t_own_domain.id = common_param.ownDomainId");
		stringBuilder.append(" and common_param.funcName = ?");
		String sqlString = stringBuilder.toString();
		return findBySql(sqlString, OwnDomainEntity.STATE_ACTIVE, functionName);
	}

}
