package com.actonia.dao;

import com.actonia.entity.ZapierErrorCodeEntity;

public class ZapierErrorCodeDAO extends BaseJdbcSupport<ZapierErrorCodeEntity> {

	@Override
	public String getTableName() {
		return "zapier_error_code";
	}

	public ZapierErrorCodeEntity get(String errorCode) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     *");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     error_code = ?");
		String sqlString = stringBuilder.toString();
		return this.findObject(sqlString, errorCode);
	}
}