package com.actonia.dao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.StringUtils;

import com.actonia.IConstants;
import com.actonia.entity.TargetUrlClickHouseEntity;
import com.actonia.utils.FormatUtils;

import org.apache.commons.lang.time.DateUtils;
import ru.yandex.clickhouse.ClickHouseDataSource;
import ru.yandex.clickhouse.settings.ClickHouseProperties;

// Due to ClickHouse concurrency issues, use Singleton pattern, do not use Spring (ie. all methods must be 'synchronized')
public class TargetUrlClickHouseDAO {

	//private static boolean isDebug = false;
	private static List<String> databaseHostnameList;
	private static String databasePort = null;
	private static String databaseName = null;
	private static List<Connection> connectionList;
	private static int batchCreationSize;
	private static String databaseUser = null;
	private static String databasePassword = null;
	private static List<String> connectionUrlList = null;
	private static int connectionTimeoutInMilliseconds;
	private static int maximumRetryCounts;
	private static int retryWaitMilliseconds;
	public static final String TABLE_NAME = "dis_target_url";
	private static TargetUrlClickHouseDAO targetUrlClickHouseDAO;

	public static TargetUrlClickHouseDAO getInstance() throws Exception {
		if (targetUrlClickHouseDAO == null) {
			targetUrlClickHouseDAO = initialize();
		}
		return targetUrlClickHouseDAO;
	}

	private static TargetUrlClickHouseDAO initialize() throws Exception {
		FormatUtils.getInstance().logMemoryUsage("initialize() begins.");

		TargetUrlClickHouseDAO targetUrlClickHouseDAO = null;

		String clickHouseDatabaseHostnames = ClickHouseCrawlDatabaseConfigurations
				.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_HOSTNAMES);
		String[] clickHouseDatabaseHostnameArray = clickHouseDatabaseHostnames.split(IConstants.COMMA);
		String clickHouseDatabasePort = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_PORT);
		String clickHouseDatabaseName = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_NAME);
		String clickHouseDatabaseUser = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_USER);
		String clickHouseDatabasePassword = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_PASSWORD);
		int clickHouseBatchCreationSize = ClickHouseCrawlDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_BATCH_CREATION_SIZE, 100);
		int clickHouseconnectionTimeoutInMilliseconds = ClickHouseCrawlDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_CONNECTION_TIMEOUT_MILLISECONDS, 88888);
		int clickHouseMaximumRetryCounts = ClickHouseCrawlDatabaseConfigurations.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_MAX_RETRY_COUNTS,
				8);
		int clickHouseRetryWaitMilliseconds = ClickHouseCrawlDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_RETRY_WAIT_MILLISECONDS, 1000);

		try {
			targetUrlClickHouseDAO = new TargetUrlClickHouseDAO(clickHouseDatabaseHostnameArray, clickHouseDatabasePort, clickHouseDatabaseName,
					clickHouseBatchCreationSize, clickHouseDatabaseUser, clickHouseDatabasePassword, clickHouseconnectionTimeoutInMilliseconds,
					clickHouseMaximumRetryCounts, clickHouseRetryWaitMilliseconds);
		} catch (Exception e) {
			e.printStackTrace();
		}

		FormatUtils.getInstance().logMemoryUsage("initialize() ends. targetUrlClickHouseDAO=" + targetUrlClickHouseDAO.toString());
		return targetUrlClickHouseDAO;
	}

	private TargetUrlClickHouseDAO(String[] databaseHostnameArrayInput, String databasePortInput, String databaseNameInput, int batchCreationSizeInput,
			String databaseUserInput, String databasePasswordInput, int connectionTimeoutInMillisecondsInput, int maximumRetryCountsInput,
			int retryWaitMillisecondsInput) throws Exception {

		ClickHouseDataSource clickHouseDataSource = null;
		Connection connection = null;

		databaseHostnameList = Arrays.asList(databaseHostnameArrayInput);
		databasePort = databasePortInput;
		databaseName = databaseNameInput;
		batchCreationSize = batchCreationSizeInput;
		databaseUser = databaseUserInput;
		databasePassword = databasePasswordInput;
		connectionTimeoutInMilliseconds = connectionTimeoutInMillisecondsInput;
		maximumRetryCounts = maximumRetryCountsInput;
		retryWaitMilliseconds = retryWaitMillisecondsInput;

		FormatUtils.getInstance()
				.logMemoryUsage("TargetUrlClickHouseDAO() databaseHostnameList=" + databaseHostnameList.toString() + ",databasePort=" + databasePort + ",databaseName="
						+ databaseName + ",batchCreationSize=" + batchCreationSize + ",databaseUser=" + databaseUser + ",databasePassword=" + databasePassword
						+ ",connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ",maximumRetryCounts=" + maximumRetryCounts
						+ ",retryWaitMilliseconds=" + retryWaitMilliseconds + ",TABLE_NAME=" + TABLE_NAME);

		ClickHouseProperties clickHouseProperties = new ClickHouseProperties();
		clickHouseProperties.setDecompress(true);
		clickHouseProperties.setConnectionTimeout(connectionTimeoutInMilliseconds);
		clickHouseProperties.setSocketTimeout(connectionTimeoutInMilliseconds);

		connectionList = new ArrayList<Connection>();

		String connectionUrl = null;

		connectionUrlList = new ArrayList<String>();

		for (String databaseHostname : databaseHostnameList) {
			if (StringUtils.isNotBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
						+ "?user=" + databaseUser + "&password=" + databasePassword;
			} else if (StringUtils.isBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
						+ "?password=" + databasePassword;
			} else {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH
						+ databaseName;
			}
			connectionUrlList.add(connectionUrl);
			clickHouseDataSource = new ClickHouseDataSource(connectionUrl, clickHouseProperties);
			connection = clickHouseDataSource.getConnection();
			connectionList.add(connection);
			FormatUtils.getInstance().logMemoryUsage("TargetUrlClickHouseDAO() database connection created...connectionUrl=" + connectionUrl);
		}
	}

	@Override
	public String toString() {
		return "TargetUrlClickHouseDAO [databaseHostnameList=" + databaseHostnameList.toString() + ", databasePort=" + databasePort + ", databaseName=" + databaseName
				+ ", batchCreationSize=" + batchCreationSize + ", databaseUser=" + databaseUser + ", databasePassword=" + databasePassword
				+ ", connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ", maximumRetryCounts=" + maximumRetryCounts + ", retryWaitMilliseconds="
				+ retryWaitMilliseconds + "]";
	}

	//public synchronized String getTableName() {
	//	return getTableName(null);
	//}

	public synchronized String getTableName(String tableName) {
		if (StringUtils.isNotBlank(tableName)) {
			return tableName;
		} else {
			return TABLE_NAME;
		}
	}

	public synchronized int getBatchCreationSize() {
		return batchCreationSize;
	}

	public synchronized void createBatch(List<TargetUrlClickHouseEntity> targetUrlClickHouseEntityList, String tableName) throws Exception {
		long startTimestamp = System.currentTimeMillis();

		PreparedStatement preparedStatement = null;
		int index = 0;
		Connection connection = null;

		String creationSqlStatement = getCreationSqlStatement(tableName);
		//FormatUtils.getInstance().logMemoryUsage("createBatch() creationSqlStatement=" + creationSqlStatement);

		int retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				//startTimestamp = System.nanoTime();
				connection = connectionList.get(0);
				preparedStatement = connection.prepareStatement(creationSqlStatement);
				for (TargetUrlClickHouseEntity targetUrlClickHouseEntity : targetUrlClickHouseEntityList) {

					index = 1;

					// track_date
					preparedStatement.setDate(index++, new java.sql.Date(targetUrlClickHouseEntity.getTrack_date().getTime()));

					// domain_id
					preparedStatement.setInt(index++, targetUrlClickHouseEntity.getDomain_id() != null ? targetUrlClickHouseEntity.getDomain_id() : -1);

					// url
					preparedStatement.setString(index++, targetUrlClickHouseEntity.getUrl());

					preparedStatement.addBatch();
				}
				preparedStatement.executeBatch();
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("createBatch() e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			} finally {
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("createBatch() targetUrlClickHouseEntityList.size()==" + targetUrlClickHouseEntityList.size() + ",elapsed(ms.)="
				+ (System.currentTimeMillis() - startTimestamp));
	}

	private synchronized String getCreationSqlStatement(String tableName) {
		String response = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" insert into " + getTableName(tableName));
		stringBuilder.append(" (");
		stringBuilder.append(" track_date,");
		stringBuilder.append(" domain_id,");
		stringBuilder.append(" url");
		stringBuilder.append(" )");
		stringBuilder.append(" values ");
		stringBuilder.append(" (");
		stringBuilder.append(" ?,");
		stringBuilder.append(" ?,");
		stringBuilder.append(" ?");
		stringBuilder.append(" )");
		stringBuilder.append(IConstants.SEMI_COLON);
		response = stringBuilder.toString();
		return response;
	}

	public List<TargetUrlClickHouseEntity> getList(int domainId, String tableName) throws Exception {
		List<TargetUrlClickHouseEntity> targetUrlClickHouseEntityList = new ArrayList<TargetUrlClickHouseEntity>();
		TargetUrlClickHouseEntity targetUrlClickHouseEntity = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		int index = 0;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("   url_hash,");
		stringBuilder.append("   url_murmur_hash,");
		stringBuilder.append("   url");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("     domain_id = ?");

		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getList() sqlString=" + sqlString);
		connection = connectionList.get(0);
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setInt(index++, domainId);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					targetUrlClickHouseEntity = new TargetUrlClickHouseEntity();

					// url
					targetUrlClickHouseEntity.setUrl(resultSet.getString(IConstants.URL));

					// url_hash
					targetUrlClickHouseEntity.setUrl_hash(resultSet.getString(IConstants.URL_HASH));

					// url_murmur_hash
					targetUrlClickHouseEntity.setUrl_murmur_hash(resultSet.getString(IConstants.URL_MURMUR_HASH));

					targetUrlClickHouseEntityList.add(targetUrlClickHouseEntity);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getList() e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			} finally {
				if (resultSet != null) {
					resultSet.close();
				}
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();
				}
			}
		}
		//FormatUtils.getInstance().logMemoryUsage("getList() ends. startTrackDate="+startTrackDate +",endTrackDate="+endTrackDate +",urlString="+urlString
		//		+ ",targetUrlClickHouseEntityList.size()=" + targetUrlClickHouseEntityList.size()
		//		+ ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp)
		//);
		return targetUrlClickHouseEntityList;
	}

	public int getTotalRecords(Date trackDate, int domainId, String tableName) throws Exception {
		int totalRecords = 0;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		int index = 0;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     count(*) as " + IConstants.TOTAL_RECORDS);
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("     track_date = ?");
		stringBuilder.append(" and domain_id = ?");
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getTotalRecords() sqlString=" + sqlString);
		connection = connectionList.get(0);
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setDate(index++, new java.sql.Date(trackDate.getTime()));
				preparedStatement.setInt(index++, domainId);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					totalRecords = resultSet.getInt(IConstants.TOTAL_RECORDS);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getTotalRecords() e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			} finally {
				if (resultSet != null) {
					resultSet.close();
				}
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();
				}
			}
		}
		//FormatUtils.getInstance().logMemoryUsage("getTotalRecords() ends. startTrackDate="+startTrackDate +",endTrackDate="+endTrackDate +",urlString="+urlString
		//		+ ",targetUrlClickHouseEntityList.size()=" + targetUrlClickHouseEntityList.size()
		//		+ ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp)
		//);
		return totalRecords;
	}

	public List<String> getUrlsWithoutCrawlData(Date dailyDataCreationDate, int domainId, String tableName) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		//FormatUtils.getInstance().logMemoryUsage(
		//		"getUrlsWithoutCrawlData() begins. dailyDataCreationDate=" + dailyDataCreationDate + ",domainId=" + domainId + ",tableName=" + tableName);
		List<String> urlList = new ArrayList<String>();
		int totalRecords = 0;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		int index = 0;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     url");
		stringBuilder.append(" from");
		stringBuilder.append("     dis_target_url");
		stringBuilder.append(" where");
		stringBuilder.append("     domain_id = ?");
		stringBuilder.append(" and (");
		stringBuilder.append("     url_hash,");
		stringBuilder.append("     url_murmur_hash");
		stringBuilder.append(" ) global not in");
		stringBuilder.append(" (");
		stringBuilder.append(" select");
		stringBuilder.append("     url_hash,");
		stringBuilder.append(" 	   url_murmur_hash");
		stringBuilder.append(" from");
		stringBuilder.append("     dis_target_url_html_daily");
		stringBuilder.append(" where");
		stringBuilder.append("     daily_data_creation_date = ?");
		stringBuilder.append(" and domain_id = ?");
		stringBuilder.append(" )");
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getUrlsWithoutCrawlData() sqlString=" + sqlString);
		connection = connectionList.get(0);
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setDate(index++, new java.sql.Date(dailyDataCreationDate.getTime()));
				preparedStatement.setInt(index++, domainId);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					urlList.add(resultSet.getString(IConstants.URL));
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getUrlsWithoutCrawlData() e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			} finally {
				if (resultSet != null) {
					resultSet.close();
				}
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();
				}
			}
		}
		//FormatUtils.getInstance().logMemoryUsage("getUrlsWithoutCrawlData() dailyDataCreationDate=" + dailyDataCreationDate + ",domainId=" + domainId + ",tableName="
		//		+ tableName + ",urlList.size()=" + urlList.size() + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return urlList;
	}
}
