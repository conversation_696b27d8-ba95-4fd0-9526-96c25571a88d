package com.actonia.dao;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.actonia.entity.PageRankEdgeEntity;

public class PageRankEdgeDAO extends BaseJdbcSupport<PageRankEdgeEntity> {

	private static final int RECORDS_PER_SQL_STATEMENT = 10;
	private static final int SQL_STATEMENTS_PER_JDBC_CALL = 10;
	private static final String SEMI_COLON = ";";
	private static final String INSERT_FORMAT_1 = "(?, ?)";
	private static final String INSERT_FORMAT_2 = ",(?, ?)";
	private static final int NUMBER_OF_FIELDS = 2;

	@Override
	public String getTableName() {
		return "page_rank_edge";
	}

	public void batchCreate(List<PageRankEdgeEntity> pageRankEdgeEntityList) {
		Object[] objectArray = null;
		List<Object[]> objectArrayList = new ArrayList<Object[]>();
		List<PageRankEdgeEntity> tempList = new ArrayList<PageRankEdgeEntity>();

		for (PageRankEdgeEntity pageRankEdgeEntity : pageRankEdgeEntityList) {
			tempList.add(pageRankEdgeEntity);
			if (tempList.size() == RECORDS_PER_SQL_STATEMENT) {
				objectArray = getObjectArrayForInsert(tempList);
				objectArrayList.add(objectArray);
				if (objectArrayList.size() == SQL_STATEMENTS_PER_JDBC_CALL) {
					this.executeBatch(getInsertMultiRowsSqlStatement(RECORDS_PER_SQL_STATEMENT), objectArrayList);
					objectArrayList = new ArrayList<Object[]>();
				}
				tempList = new ArrayList<PageRankEdgeEntity>();
			}
		}

		if (objectArrayList != null && objectArrayList.size() > 0) {
			this.executeBatch(getInsertMultiRowsSqlStatement(RECORDS_PER_SQL_STATEMENT), objectArrayList);
			objectArrayList = new ArrayList<Object[]>();
		}

		if (tempList != null && tempList.size() > 0) {
			objectArray = getObjectArrayForInsert(tempList);
			objectArrayList.add(objectArray);
			if (objectArrayList != null && objectArrayList.size() > 0) {
				this.executeBatch(getInsertMultiRowsSqlStatement(tempList.size()), objectArrayList);
				objectArrayList = new ArrayList<Object[]>();
			}
		}
	}

	private Object[] getObjectArrayForInsert(List<PageRankEdgeEntity> list) {
		List<Object[]> tempObjectArrayList = new ArrayList<Object[]>();
		Object[] backlinkObjectArray = null;
		int totalNumberOfObjects = 0;
		for (PageRankEdgeEntity pageRankEdgeEntity : list) {
			backlinkObjectArray = new Object[] { pageRankEdgeEntity.getSourceNodeId(), pageRankEdgeEntity.getTargetNodeId() };
			tempObjectArrayList.add(backlinkObjectArray);
		}
		totalNumberOfObjects = tempObjectArrayList.size() * NUMBER_OF_FIELDS;
		Object[] objectArray = new Object[totalNumberOfObjects];
		int idx = 0;
		for (Object[] tempObjectArray : tempObjectArrayList) {
			for (int i = 0; i < tempObjectArray.length; i++) {
				objectArray[idx++] = tempObjectArray[i];
			}
		}
		return objectArray;
	}

	private String getInsertMultiRowsSqlStatement(int recordsPerInsertStatement) {
		String response = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" insert into " + getTableName());
		stringBuilder.append(" (");
		stringBuilder.append("	source_node_id,");
		stringBuilder.append("	target_node_id");
		stringBuilder.append(" )");
		stringBuilder.append(" values");
		String sqlString = stringBuilder.toString();
		StringBuffer sql = new StringBuffer();
		sql.append(sqlString);
		for (int i = 0; i < recordsPerInsertStatement; i++) {
			if (i == 0) {
				sql.append(INSERT_FORMAT_1);
			} else {
				sql.append(INSERT_FORMAT_2);
			}
		}
		sql.append(SEMI_COLON);
		response = sql.toString();
		return response;
	}

	public List<PageRankEdgeEntity> getList() {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("  source_node_id,");
		stringBuilder.append("  target_node_id");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		String sqlString = stringBuilder.toString();
		return this.findBySql(sqlString);
	}

	public void reset() {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" truncate");
		stringBuilder.append(" " + getTableName());
		String sqlString = stringBuilder.toString();
		this.executeUpdate(sqlString);
	}

	public List<PageRankEdgeEntity> getTargetNodeCountList() {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("  source_node_id,");
		stringBuilder.append("  count(*) as targetNodeCount");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" group by");
		stringBuilder.append("  source_node_id");
		String sqlString = stringBuilder.toString();
		return this.findBySql(sqlString);
	}
}