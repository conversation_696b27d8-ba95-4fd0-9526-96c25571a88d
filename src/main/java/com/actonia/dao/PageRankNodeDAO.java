package com.actonia.dao;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.actonia.entity.PageRankNodeEntity;

public class PageRankNodeDAO extends BaseJdbcSupport<PageRankNodeEntity> {

	private static final int RECORDS_PER_SQL_STATEMENT = 10;
	private static final int SQL_STATEMENTS_PER_JDBC_CALL = 10;
	private static final String SEMI_COLON = ";";
	private static final String INSERT_FORMAT_1 = "(?, ?)";
	private static final String INSERT_FORMAT_2 = ",(?, ?)";
	private static final int NUMBER_OF_FIELDS = 2;

	@Override
	public String getTableName() {
		return "page_rank_node";
	}

	public void batchCreate(List<PageRankNodeEntity> pageRankNodeEntityList) {
		Object[] objectArray = null;
		List<Object[]> objectArrayList = new ArrayList<Object[]>();
		List<PageRankNodeEntity> tempList = new ArrayList<PageRankNodeEntity>();

		for (PageRankNodeEntity pageRankNodeEntity : pageRankNodeEntityList) {
			tempList.add(pageRankNodeEntity);
			if (tempList.size() == RECORDS_PER_SQL_STATEMENT) {
				objectArray = getObjectArrayForInsert(tempList);
				objectArrayList.add(objectArray);
				if (objectArrayList.size() == SQL_STATEMENTS_PER_JDBC_CALL) {
					this.executeBatch(getInsertMultiRowsSqlStatement(RECORDS_PER_SQL_STATEMENT), objectArrayList);
					objectArrayList = new ArrayList<Object[]>();
				}
				tempList = new ArrayList<PageRankNodeEntity>();
			}
		}

		if (objectArrayList != null && objectArrayList.size() > 0) {
			this.executeBatch(getInsertMultiRowsSqlStatement(RECORDS_PER_SQL_STATEMENT), objectArrayList);
			objectArrayList = new ArrayList<Object[]>();
		}

		if (tempList != null && tempList.size() > 0) {
			objectArray = getObjectArrayForInsert(tempList);
			objectArrayList.add(objectArray);
			if (objectArrayList != null && objectArrayList.size() > 0) {
				this.executeBatch(getInsertMultiRowsSqlStatement(tempList.size()), objectArrayList);
				objectArrayList = new ArrayList<Object[]>();
			}
		}
	}

	private Object[] getObjectArrayForInsert(List<PageRankNodeEntity> list) {
		List<Object[]> tempObjectArrayList = new ArrayList<Object[]>();
		Object[] backlinkObjectArray = null;
		int totalNumberOfObjects = 0;
		for (PageRankNodeEntity pageRankNodeEntity : list) {
			backlinkObjectArray = new Object[] { pageRankNodeEntity.getNodeUrlHashCode(), pageRankNodeEntity.getNodeUrl() };
			tempObjectArrayList.add(backlinkObjectArray);
		}
		totalNumberOfObjects = tempObjectArrayList.size() * NUMBER_OF_FIELDS;
		Object[] objectArray = new Object[totalNumberOfObjects];
		int idx = 0;
		for (Object[] tempObjectArray : tempObjectArrayList) {
			for (int i = 0; i < tempObjectArray.length; i++) {
				objectArray[idx++] = tempObjectArray[i];
			}
		}
		return objectArray;
	}

	private String getInsertMultiRowsSqlStatement(int recordsPerInsertStatement) {
		String response = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("insert into " + getTableName() + " ");
		stringBuilder.append("(");
		stringBuilder.append("	node_url_hash_code,");
		stringBuilder.append("	node_url");
		stringBuilder.append(")");
		stringBuilder.append("values ");
		String sqlString = stringBuilder.toString();
		StringBuffer sql = new StringBuffer();
		sql.append(sqlString);
		for (int i = 0; i < recordsPerInsertStatement; i++) {
			if (i == 0) {
				sql.append(INSERT_FORMAT_1);
			} else {
				sql.append(INSERT_FORMAT_2);
			}
		}
		sql.append(SEMI_COLON);
		response = sql.toString();
		return response;
	}

	public int getTotalNodes() {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("  count(*)");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		String sqlString = stringBuilder.toString();
		return this.queryForInt(sqlString);
	}

	public List<Integer> getNodeIdList() {
		List<Integer> nodeIdList = new ArrayList<Integer>();
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("  node_id");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		String sqlString = stringBuilder.toString();
		List<PageRankNodeEntity> pageRankNodeEntityList = this.findBySql(sqlString);
		if (pageRankNodeEntityList != null && pageRankNodeEntityList.size() > 0) {
			for (PageRankNodeEntity pageRankNodeEntity : pageRankNodeEntityList) {
				nodeIdList.add(pageRankNodeEntity.getNodeId());
			}
		}
		return nodeIdList;
	}


	public boolean isNotConvergedExists() {
		boolean output = false;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("  count(*)");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("  node_converged = 0");
		String sqlString = stringBuilder.toString();
		int totalNotConverged = this.queryForInt(sqlString);
		if (totalNotConverged > 0) {
			output = true;
		}
		return output;		
	}

	public List<PageRankNodeEntity> getNodeIdNodeWeightList() {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("  node_id,");
		stringBuilder.append("  node_weight");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		String sqlString = stringBuilder.toString();
		return this.findBySql(sqlString);
	}

	public List<PageRankNodeEntity> getNodeIdUrlNodeHashCodeList() {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("  node_id,");
		stringBuilder.append("  node_url_hash_code");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		String sqlString = stringBuilder.toString();
		return this.findBySql(sqlString);
	}

	public List<PageRankNodeEntity> getList() {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("  *");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		String sqlString = stringBuilder.toString();
		return this.findBySql(sqlString);
	}

	public void updateNodeCount(int nodeId, int nodeCount) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" update");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" set");
		stringBuilder.append("  node_count = ?");
		stringBuilder.append(" where");
		stringBuilder.append("  node_id = ?");
		String sqlString = stringBuilder.toString();
		this.executeUpdate(sqlString, nodeCount, nodeId);
	}

	public void updateNodeWeightNodeConverged(int nodeId, BigDecimal nodeWeight, boolean isNodeConverged) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" update");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" set");
		stringBuilder.append("  node_weight = ?,");
		stringBuilder.append("  node_converged = ?");
		stringBuilder.append(" where");
		stringBuilder.append("  node_id = ?");
		String sqlString = stringBuilder.toString();
		this.executeUpdate(sqlString, nodeWeight, isNodeConverged, nodeId);
	}

	public void reset() {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" truncate");
		stringBuilder.append(" " + getTableName());
		String sqlString = stringBuilder.toString();
		this.executeUpdate(sqlString);
	}

	public Map<Integer, BigDecimal> getTargetNodeIdTransferredNodeWeightMap() {
		
		// map key = target node ID
		// map value = transferred node weight
		Map<Integer, BigDecimal> targetNodeIdTransferredNodeWeightMap = new HashMap<Integer, BigDecimal>();

		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append(" 	page_rank_edge.target_node_id as nodeId,");
		stringBuilder.append(" 	sum(page_rank_node.node_weight / page_rank_node.node_count) * 0.85 as transferredNodeWeight");
		stringBuilder.append(" from");
		stringBuilder.append(" page_rank_node page_rank_node");
		stringBuilder.append("  inner join page_rank_edge page_rank_edge");
		stringBuilder.append("   on page_rank_node.node_id = page_rank_edge.source_node_id");
		stringBuilder.append(" where");
		stringBuilder.append("  page_rank_edge.source_node_id <> page_rank_edge.target_node_id");
		stringBuilder.append(" group by");
		stringBuilder.append("  page_rank_edge.target_node_id");
		String sqlString = stringBuilder.toString();
		List<PageRankNodeEntity> pageRankNodeEntityList = this.findBySql(sqlString);
		if (pageRankNodeEntityList != null && pageRankNodeEntityList.size() > 0) {
			for (PageRankNodeEntity pageRankNodeEntity : pageRankNodeEntityList) {
				targetNodeIdTransferredNodeWeightMap.put(pageRankNodeEntity.getNodeId(), pageRankNodeEntity.getTransferredNodeWeight());
			}
		}
		return targetNodeIdTransferredNodeWeightMap;
	}
}