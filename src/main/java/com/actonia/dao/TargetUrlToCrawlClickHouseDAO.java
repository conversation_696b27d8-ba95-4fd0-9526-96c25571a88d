
package com.actonia.dao;

import com.actonia.IConstants;
import com.actonia.entity.TargetUrlToCrawl;
import com.actonia.utils.FormatUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import ru.yandex.clickhouse.ClickHouseDataSource;
import ru.yandex.clickhouse.settings.ClickHouseProperties;

import java.math.BigInteger;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

// Due to ClickHouse concurrency issues, use Singleton pattern, do not use Spring (ie. all methods must be 'synchronized')
public class TargetUrlToCrawlClickHouseDAO {

    //private static boolean isDebug = false;
    public static final String TABLE_NAME = "dis_target_url_to_crawl";
    private static final int TOTAL_DAO_INSTANCES = 1;
    private static final Logger log = LogManager.getLogger(TargetUrlToCrawlClickHouseDAO.class);
    private static int daoMapIndex = 0;
    private static final BigInteger MOD_DIVISION = BigInteger.valueOf(1000);

    // map key = DAO index (0  - 59)
    // map value = instance of TargetUrlToCrawlClickHouseDAO
    private static Map<Integer, TargetUrlToCrawlClickHouseDAO> targetUrlToCrawlClickHouseDAOMap;

    private final List<String> databaseHostnameList;
    private final String databasePort;
    private final String databaseName;
    private final List<Connection> connectionList;
    private final List<String> connectionUrlList;
    private final int batchCreationSize;
    private final String databaseUser;
    private final String databasePassword;
    private final int connectionTimeoutInMilliseconds;
    private final int maximumRetryCounts;
    private final int retryWaitMilliseconds;
    private final int daoIndex;

    private TargetUrlToCrawlClickHouseDAO(String[] databaseHostnameArrayInput, String databasePortInput, String databaseNameInput, int batchCreationSizeInput,
                                          String databaseUserInput, String databasePasswordInput, int connectionTimeoutInMillisecondsInput, int maximumRetryCountsInput,
                                          int retryWaitMillisecondsInput, int index) throws Exception {

        ClickHouseDataSource clickHouseDataSource = null;
        Connection connection = null;

        databaseHostnameList = Arrays.asList(databaseHostnameArrayInput);
        databasePort = databasePortInput;
        databaseName = databaseNameInput;
        batchCreationSize = batchCreationSizeInput;
        databaseUser = databaseUserInput;
        databasePassword = databasePasswordInput;
        connectionTimeoutInMilliseconds = connectionTimeoutInMillisecondsInput;
        maximumRetryCounts = maximumRetryCountsInput;
        retryWaitMilliseconds = retryWaitMillisecondsInput;
        daoIndex = index;

        ClickHouseProperties clickHouseProperties = new ClickHouseProperties();
        clickHouseProperties.setDecompress(true);
        clickHouseProperties.setConnectionTimeout(connectionTimeoutInMilliseconds);
        clickHouseProperties.setSocketTimeout(connectionTimeoutInMilliseconds);

        connectionList = new ArrayList<>();

        connectionUrlList = new ArrayList<>();

        String connectionUrl = null;
        for (String databaseHostname : databaseHostnameList) {
            if (StringUtils.isNotBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
                connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
                        + "?user=" + databaseUser + "&password=" + databasePassword;
            } else if (StringUtils.isBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
                connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
                        + "?password=" + databasePassword;
            } else {
                connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH
                        + databaseName;
            }
            connectionUrlList.add(connectionUrl);
            //FormatUtils.getInstance().logMemoryUsage("TargetUrlToCrawlClickHouseDAO() connectionUrl=" + connectionUrl);
            clickHouseDataSource = new ClickHouseDataSource(connectionUrl, clickHouseProperties);
            connection = clickHouseDataSource.getConnection();
            connectionList.add(connection);
        }
    }

    public static TargetUrlToCrawlClickHouseDAO getInstance() throws Exception {
        TargetUrlToCrawlClickHouseDAO targetUrlToCrawlClickHouseDAO;
        if (targetUrlToCrawlClickHouseDAOMap == null) {
            String clickHouseDatabaseHostnames = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_HOSTNAMES);
            String[] clickHouseDatabaseHostnameArray = clickHouseDatabaseHostnames.split(IConstants.COMMA);
            String clickHouseDatabasePort = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_PORT);
            String clickHouseDatabaseName = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_NAME);
            String clickHouseDatabaseUser = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_USER);
            String clickHouseDatabasePassword = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_PASSWORD);
            int clickHouseBatchCreationSize = ClickHouseCrawlDatabaseConfigurations
                    .getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_BATCH_CREATION_SIZE, 100);
            int clickHouseConnectionTimeoutInMilliseconds = ClickHouseCrawlDatabaseConfigurations
                    .getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_CONNECTION_TIMEOUT_MILLISECONDS, 88888);
            int clickHouseMaximumRetryCounts = ClickHouseCrawlDatabaseConfigurations.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_MAX_RETRY_COUNTS,
                    3);
            int clickHouseRetryWaitMilliseconds = ClickHouseCrawlDatabaseConfigurations
                    .getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_RETRY_WAIT_MILLISECONDS, 1000);
            FormatUtils.getInstance().logMemoryUsage("TargetUrlToCrawlClickHouseDAO() clickHouseDatabaseHostnames=" + clickHouseDatabaseHostnames
                    + ",clickHouseDatabasePort=" + clickHouseDatabasePort + ",clickHouseDatabaseName=" + clickHouseDatabaseName + ",clickHouseBatchCreationSize="
                    + clickHouseBatchCreationSize + ",clickHouseDatabaseUser=" + clickHouseDatabaseUser + ",clickHouseDatabasePassword=" + clickHouseDatabasePassword
                    + ",clickHouseConnectionTimeoutInMilliseconds=" + clickHouseConnectionTimeoutInMilliseconds + ",clickHouseMaximumRetryCounts="
                    + clickHouseMaximumRetryCounts + ",clickHouseRetryWaitMilliseconds=" + clickHouseRetryWaitMilliseconds + ",TABLE_NAME=" + TABLE_NAME);

            targetUrlToCrawlClickHouseDAOMap = new HashMap<>();
            for (int i = 0; i < TOTAL_DAO_INSTANCES; i++) {
                targetUrlToCrawlClickHouseDAO = initialize(clickHouseDatabaseHostnameArray, clickHouseDatabasePort, clickHouseDatabaseName, clickHouseDatabaseUser,
                        clickHouseDatabasePassword, clickHouseBatchCreationSize, clickHouseConnectionTimeoutInMilliseconds, clickHouseMaximumRetryCounts,
                        clickHouseRetryWaitMilliseconds, i);
                targetUrlToCrawlClickHouseDAOMap.put(i, targetUrlToCrawlClickHouseDAO);
            }
            FormatUtils.getInstance().logMemoryUsage("getInstance() total targetUrlToCrawlClickHouseDAOs=" + targetUrlToCrawlClickHouseDAOMap.size());
        }
        int index = getDaoMapIndex();
        targetUrlToCrawlClickHouseDAO = targetUrlToCrawlClickHouseDAOMap.get(index);
        return targetUrlToCrawlClickHouseDAO;
    }

    private static synchronized int getDaoMapIndex() {
        int index = 0;
        if (daoMapIndex >= TOTAL_DAO_INSTANCES) {
            daoMapIndex = 0;
        } else {
            index = daoMapIndex++;
        }
        return index;
    }

    // initialize TargetUrlToCrawlClickHouseDAO based on runtime clickhouse configurations
    private static TargetUrlToCrawlClickHouseDAO initialize(String[] clickHouseDatabaseHostnameArray, String clickHouseDatabasePort, String clickHouseDatabaseName,
                                                            String clickHouseDatabaseUser, String clickHouseDatabasePassword, int clickHouseBatchCreationSize, int clickHouseconnectionTimeoutInMilliseconds,
                                                            int clickHouseMaximumRetryCounts, int clickHouseRetryWaitMilliseconds, int daoIndex) throws Exception {
        //FormatUtils.getInstance().logMemoryUsage("initialize() targetUrlToCrawlClickHouseDAO=" + targetUrlToCrawlClickHouseDAO.toString());
        return new TargetUrlToCrawlClickHouseDAO(clickHouseDatabaseHostnameArray,
                clickHouseDatabasePort, clickHouseDatabaseName, clickHouseBatchCreationSize, clickHouseDatabaseUser, clickHouseDatabasePassword,
                clickHouseconnectionTimeoutInMilliseconds, clickHouseMaximumRetryCounts, clickHouseRetryWaitMilliseconds, daoIndex);
    }

    @Override
    public String toString() {
        return "TargetUrlToCrawlClickHouseDAO [databaseHostnameList=" + databaseHostnameList.toString() + ", databasePort=" + databasePort + ", databaseName="
                + databaseName + ", batchCreationSize=" + batchCreationSize + ", databaseUser=" + databaseUser + ", databasePassword=" + databasePassword
                + ", connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ", maximumRetryCounts=" + maximumRetryCounts + ", retryWaitMilliseconds="
                + retryWaitMilliseconds + ", daoIndex=" + daoIndex + "]";
    }

    private synchronized String getTableName(String tableName) {
        if (StringUtils.isNotBlank(tableName)) {
            return tableName;
        } else {
            return TABLE_NAME;
        }
    }

    public synchronized int getBatchCreationSize() {
        return batchCreationSize;
    }

    public synchronized void createBatch(List<TargetUrlToCrawl> targetUrlToCrawlList) {
        final String sql = "insert into " + TABLE_NAME + " (track_date, domain_id, url) values (?,?,?)";
        int retryCount = 0;
        while (retryCount < maximumRetryCounts) {
            Connection connection = connectionList.get(0);
            try (PreparedStatement preparedStatement = connection.prepareStatement(sql)) {
                for (TargetUrlToCrawl targetUrlToCrawl : targetUrlToCrawlList) {
                    int preparedStatementIndex = 1;
                    preparedStatement.setDate(preparedStatementIndex++, new java.sql.Date(targetUrlToCrawl.getTrackDate().getTime()));
                    preparedStatement.setInt(preparedStatementIndex++, targetUrlToCrawl.getDomainId());
                    preparedStatement.setString(preparedStatementIndex++, targetUrlToCrawl.getUrl());
                    preparedStatement.addBatch();
                }
                preparedStatement.executeBatch();
                return;
            } catch (SQLException e) {
                log.error("batchCreate() sql=" + sql, e);
                retryCount++;
                try {
                    Thread.sleep(retryWaitMilliseconds);
                } catch (InterruptedException e1) {
                    log.error("batchCreate() Thread.sleep() error", e1);
                }
            }
        }
        log.error("batchCreate() retryCount={} sql=" + sql, retryCount);
        throw new RuntimeException("batchCreate() failed");
    }

    public synchronized long countByDomainId(int domainId) {
        final String sql = "select count(*) from dis_target_url_to_crawl where domain_id = ? and track_date global in (select max(track_date) from dis_target_url_to_crawl where domain_id = ?)";
        int retryCount = 0;
        while (retryCount < maximumRetryCounts) {
            Connection connection = connectionList.get(0);
            try (PreparedStatement preparedStatement = connection.prepareStatement(sql)) {
                preparedStatement.setInt(1, domainId);
                preparedStatement.setInt(2, domainId);
                ResultSet resultSet = preparedStatement.executeQuery();
                if (resultSet.next()) {
                    return resultSet.getLong(1);
                }
                return 0;
            } catch (SQLException e) {
                log.error("countByDomainId() sql=" + sql, e);
                retryCount++;
                try {
                    Thread.sleep(retryWaitMilliseconds);
                } catch (InterruptedException e1) {
                    log.error("countByDomainId() Thread.sleep() error", e1);
                }
            }
        }
        log.error("countByDomainId() retryCount={} sql=" + sql, retryCount);
        throw new RuntimeException("countByDomainId() failed");
    }


}