package com.actonia.dao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import com.actonia.utils.AwsCredentialsEnvKeyConstructor;
import org.apache.commons.lang.StringUtils;

import com.actonia.IConstants;
import com.actonia.utils.FormatUtils;

import ru.yandex.clickhouse.ClickHouseDataSource;
import ru.yandex.clickhouse.settings.ClickHouseProperties;

// Due to ClickHouse concurrency issues, use Singleton pattern, do not use Spring (ie. all methods must be 'synchronized')
public class ContentGuardBackupClickHouseDAO {

	//private boolean isDebug = false;
	private List<String> databaseHostnameList;
	private String databasePort = null;
	private String databaseName = null;
	private List<Connection> connectionList;
	private int batchCreationSize;
	private String databaseUser = null;
	private String databasePassword = null;
	private List<String> connectionUrlList = null;
	private int connectionTimeoutInMilliseconds;
	private int maximumRetryCounts;
	private int retryWaitMilliseconds;
	public static final String TABLE_NAME = "local_content_guard";
	private final String s3AccessKey;
	private final String s3SecretKey;

	public ContentGuardBackupClickHouseDAO(String[] databaseHostnameArrayInput, String databasePortInput, String databaseNameInput, int batchCreationSizeInput,
			String databaseUserInput, String databasePasswordInput, int connectionTimeoutInMillisecondsInput, int maximumRetryCountsInput,
			int retryWaitMillisecondsInput) throws Exception {

		ClickHouseDataSource clickHouseDataSource = null;
		Connection connection = null;

		databaseHostnameList = Arrays.asList(databaseHostnameArrayInput);

		databasePort = databasePortInput;
		databaseName = databaseNameInput;
		batchCreationSize = batchCreationSizeInput;
		databaseUser = databaseUserInput;
		databasePassword = databasePasswordInput;
		connectionTimeoutInMilliseconds = connectionTimeoutInMillisecondsInput;
		maximumRetryCounts = maximumRetryCountsInput;
		retryWaitMilliseconds = retryWaitMillisecondsInput;

		FormatUtils.getInstance()
				.logMemoryUsage("ContentGuardBackupClickHouseDAO() databaseHostnameList=" + databaseHostnameList.toString() + ",databasePort=" + databasePort
						+ ",databaseName=" + databaseName + ",batchCreationSize=" + batchCreationSize + ",databaseUser=" + databaseUser + ",databasePassword="
						+ databasePassword + ",connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ",maximumRetryCounts=" + maximumRetryCounts
						+ ",retryWaitMilliseconds=" + retryWaitMilliseconds + ",TABLE_NAME=" + TABLE_NAME);

		ClickHouseProperties clickHouseProperties = new ClickHouseProperties();
		clickHouseProperties.setDecompress(true);
		clickHouseProperties.setConnectionTimeout(connectionTimeoutInMilliseconds);
		clickHouseProperties.setSocketTimeout(connectionTimeoutInMilliseconds);

		connectionList = new ArrayList<Connection>();

		String connectionUrl = null;

		connectionUrlList = new ArrayList<String>();

		for (String databaseHostname : databaseHostnameList) {
			if (StringUtils.isNotBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
						+ "?user=" + databaseUser + "&password=" + databasePassword;
			} else {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH
						+ databaseName;
			}
			connectionUrlList.add(connectionUrl);
			FormatUtils.getInstance().logMemoryUsage("ContentGuardBackupClickHouseDAO() connectionUrl=" + connectionUrl);
			clickHouseDataSource = new ClickHouseDataSource(connectionUrl, clickHouseProperties);
			connection = clickHouseDataSource.getConnection();
			connectionList.add(connection);
		}
		this.s3AccessKey = AwsCredentialsEnvKeyConstructor.getInstance().getPlainTextS3AccessKey();
		this.s3SecretKey = AwsCredentialsEnvKeyConstructor.getInstance().getS3DecryptedSecretKey();
	}

	@Override
	public String toString() {
		return "ContentGuardBackupClickHouseDAO [databaseHostnameList=" + databaseHostnameList.toString() + ", databasePort=" + databasePort + ", databaseName="
				+ databaseName + ", batchCreationSize=" + batchCreationSize + ", databaseUser=" + databaseUser + ", databasePassword=" + databasePassword
				+ ", connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ", maximumRetryCounts=" + maximumRetryCounts + ", retryWaitMilliseconds="
				+ retryWaitMilliseconds + "]";
	}

	private synchronized String getTableName(String tableName) {
		if (StringUtils.isNotBlank(tableName)) {
			return tableName;
		} else {
			return TABLE_NAME;
		}
	}

	public Date getEarliestTrackDate(String tableName) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		Date trackDate = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     min(track_date)");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getEarliestTrackDate() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		//if (isDebug == true) {
		//	connectionUrl = connectionUrlList.get(connectionIndex);
		//	FormatUtils.getInstance().logMemoryUsage("getEarliestTrackDate() connectionUrl=" + connectionUrl);
		//}
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				preparedStatement = connection.prepareStatement(sqlString);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					trackDate = resultSet.getTimestamp(1);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				e.printStackTrace();
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getEarliestTrackDate() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			} finally {
				if (resultSet != null) {
					resultSet.close();
				}
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("getEarliestTrackDate() trackDate=" + trackDate + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		return trackDate;
	}

	public Date getLatestTrackDate(String tableName) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		Date trackDate = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     max(track_date)");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getLatestTrackDate() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		//if (isDebug == true) {
		//	connectionUrl = connectionUrlList.get(connectionIndex);
		//	FormatUtils.getInstance().logMemoryUsage("getLatestTrackDate() connectionUrl=" + connectionUrl);
		//}
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				preparedStatement = connection.prepareStatement(sqlString);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					trackDate = resultSet.getTimestamp(1);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				e.printStackTrace();
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getLatestTrackDate() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			} finally {
				if (resultSet != null) {
					resultSet.close();
				}
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("getLatestTrackDate() trackDate=" + trackDate + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		return trackDate;
	}

	public void backupToS3(String s3ObjectURI, String tableName, String trackDateString) throws Exception {
		//FormatUtils.getInstance().logMemoryUsage("backupToS3() begins. s3ObjectURI=" + s3ObjectURI + ",tableName=" + tableName + ",trackDateString="
		//		+ trackDateString);
		//long startTimestamp = System.currentTimeMillis();
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("insert into function s3('" + s3ObjectURI + "', ");
		stringBuilder.append("  '" + s3AccessKey + "', ");
		stringBuilder.append("  '" + s3SecretKey + "', ");
		stringBuilder.append("  'Native', ");
		stringBuilder.append(
				"  '`domain_id` UInt32, `url` String, `track_date` Date, `alt_img_list` String, `alternate_links` String, `amphtml_flag` Int8, `amphtml_href` String, `analyzed_url_flg_s` String, `analyzed_url_s` String, `archive_flg` String, `archive_flg_x_tag` String, `blocked_by_robots` String, `canonical` String, `canonical_flg` String, `canonical_header_flag` Int8, `canonical_header_type` String, `canonical_type` String, `canonical_url_is_consistent` String, `content_type` String, `count_of_objects` Int32, `custom_data` String, `description` String, `description_flg` String, `description_length` Int32, `description_simhash` String, `document_size` Int32, `download_latency` String, `download_time` String, `error_message` String, `final_response_code` Int32, `folder_level_1` String, `folder_level_2` String, `folder_level_3` String, `folder_level_count` Int32, `follow_flg` String, `follow_flg_x_tag` String, `h1` String, `h1_count` Int32, `h1_flg` String, `h1_length` Int32, `h1_md5` String, `h1_simhash` String, `h2` String, `h2_simhash` String, `header_noarchive` Int8, `header_nofollow` Int8, `header_noindex` Int8, `header_noodp` Int8, `header_nosnippet` Int8, `header_noydir` Int8, `hreflang_errors` String, `hreflang_links` String, `hreflang_links_out_count` Int32, `hreflang_url_count` Int32, `index_flg` String, `index_flg_x_tag` String, `indexable` Int8, `insecure_resources` String, `insecure_resources_flag` Int8, `long_redirect` Int8, `meta_charset` String, `meta_content_type` String, `meta_disabled_sitelinks` Int8, `meta_noodp` Int8, `meta_nosnippet` Int8, `meta_noydir` Int8, `meta_redirect` Int8, `mixed_redirects` Int8, `mobile_rel_alternate_url_is_consistent` Int8, `noodp` Int8, `nosnippet` Int8, `noydir` Int8, `og_markup` String, `og_markup_flag` Int8, `og_markup_length` Int32, `outlink_count` Int32, `page_1` Int8, `page_link` String, `page_timeout_flag` Int8, `paginated` Int8, `pagination_links` String, `protocol` String, `redirect_blocked` Int8, `redirect_blocked_reason` String, `redirect_chain` String, `redirect_final_url` String, `redirect_flg` Int8, `redirect_times` Int32, `rel_next_html_url` String, `rel_next_url_is_consistent` Int8, `rel_prev_url_is_consistent` Int8, `request_headers` String, `request_time` String, `response_code` String, `response_headers` String, `retry_attempted` Int8, `robots` String, `robots_contents` String, `robots_contents_x_tag` Int8, `robots_flg` String, `robots_flg_x_tag` String, `server_response_time` String, `source_url` String, `splash_took` String, `structured_data` String, `title` String, `title_flg` String, `title_length` Int32, `title_md5` String, `title_simhash` String, `twitter_description_length` Int32, `twitter_markup` String, `twitter_markup_flag` Int8, `twitter_markup_length` Int32, `url_length` Int32, `valid_twitter_card` String, `viewport_content` String, `viewport_flag` Int8, `page_analysis_results` String, `change_tracking_hash` String, `week_of_year` Int32, `crawl_timestamp` DateTime, `url_hash` UInt64, `lower_case_url_hash` UInt64, `url_murmur_hash` UInt32, `alternate_links_chg_ind` Int8, `amphtml_flag_chg_ind` Int8, `amphtml_href_chg_ind` Int8, `analyzed_url_flg_s_chg_ind` Int8, `analyzed_url_s_chg_ind` Int8, `archive_flg_chg_ind` Int8, `archive_flg_x_tag_chg_ind` Int8, `blocked_by_robots_chg_ind` Int8, `canonical_chg_ind` Int8, `canonical_flg_chg_ind` Int8, `canonical_header_flag_chg_ind` Int8, `canonical_header_type_chg_ind` Int8, `canonical_type_chg_ind` Int8, `canonical_url_is_consistent_chg_ind` Int8, `content_type_chg_ind` Int8, `description_chg_ind` Int8, `description_flg_chg_ind` Int8, `description_length_chg_ind` Int8, `description_simhash_chg_ind` Int8, `error_message_chg_ind` Int8, `final_response_code_chg_ind` Int8, `follow_flg_chg_ind` Int8, `follow_flg_x_tag_chg_ind` Int8, `h1_chg_ind` Int8, `h1_count_chg_ind` Int8, `h1_flg_chg_ind` Int8, `h1_length_chg_ind` Int8, `h1_md5_chg_ind` Int8, `header_noarchive_chg_ind` Int8, `header_nofollow_chg_ind` Int8, `header_noindex_chg_ind` Int8, `header_noodp_chg_ind` Int8, `header_nosnippet_chg_ind` Int8, `header_noydir_chg_ind` Int8, `hreflang_errors_chg_ind` Int8, `hreflang_links_chg_ind` Int8, `hreflang_links_out_count_chg_ind` Int8, `hreflang_url_count_chg_ind` Int8, `index_flg_chg_ind` Int8, `index_flg_x_tag_chg_ind` Int8, `indexable_chg_ind` Int8, `insecure_resources_chg_ind` Int8, `insecure_resources_flag_chg_ind` Int8, `meta_charset_chg_ind` Int8, `meta_content_type_chg_ind` Int8, `meta_disabled_sitelinks_chg_ind` Int8, `meta_noodp_chg_ind` Int8, `meta_nosnippet_chg_ind` Int8, `meta_noydir_chg_ind` Int8, `meta_redirect_chg_ind` Int8, `mixed_redirects_chg_ind` Int8, `mobile_rel_alternate_url_is_consistent_chg_ind` Int8, `noodp_chg_ind` Int8, `nosnippet_chg_ind` Int8, `noydir_chg_ind` Int8, `og_markup_chg_ind` Int8, `og_markup_flag_chg_ind` Int8, `og_markup_length_chg_ind` Int8, `outlink_count_chg_ind` Int8, `redirect_blocked_chg_ind` Int8, `redirect_blocked_reason_chg_ind` Int8, `redirect_chain_chg_ind` Int8, `redirect_final_url_chg_ind` Int8, `redirect_flg_chg_ind` Int8, `redirect_times_chg_ind` Int8, `response_code_chg_ind` Int8, `robots_chg_ind` Int8, `robots_contents_chg_ind` Int8, `robots_contents_x_tag_chg_ind` Int8, `robots_flg_chg_ind` Int8, `robots_flg_x_tag_chg_ind` Int8, `structured_data_chg_ind` Int8, `title_chg_ind` Int8, `title_flg_chg_ind` Int8, `title_length_chg_ind` Int8, `title_md5_chg_ind` Int8, `title_simhash_chg_ind` Int8, `viewport_content_chg_ind` Int8, `viewport_flag_chg_ind` Int8, `change_tracking_hash_cd_json` String, `sign` Int8, `page_analysis_results_chg_ind_json` String, `canonical_added_ind` Int8, `canonical_removed_ind` Int8, `description_added_ind` Int8, `description_removed_ind` Int8, `h1_added_ind` Int8, `h1_removed_ind` Int8, `h2_added_ind` Int8, `h2_chg_ind` Int8, `h2_removed_ind` Int8, `hreflang_links_added_ind` Int8, `hreflang_links_removed_ind` Int8, `open_graph_added_ind` Int8, `open_graph_removed_ind` Int8, `redirect_301_chg_ind` Int8, `redirect_301_detected_ind` Int8, `redirect_301_removed_ind` Int8, `redirect_302_chg_ind` Int8, `redirect_302_detected_ind` Int8, `redirect_302_removed_ind` Int8, `redirect_diff_code_ind` Int8, `robots_added_ind` Int8, `robots_removed_ind` Int8, `title_added_ind` Int8, `title_removed_ind` Int8, `viewport_added_ind` Int8, `viewport_removed_ind` Int8, `page_link_chg_ind` Int8, `internal_link_count` Int32, `custom_data_chg_ind` Int8, `page_analysis_results_reverse` String, `base_tag` String, `base_tag_flag` Int8, `base_tag_target` String, `base_tag_chg_ind` Int8, `base_tag_flag_chg_ind` Int8, `base_tag_target_chg_ind` Int8, `base_tag_added_ind` Int8, `base_tag_removed_ind` Int8, `page_analysis_fragments` String, `page_analysis_fragments_chg_ind` Int8, `content_guard_ind` Int8, `custom_data_added_ind` Int8, `custom_data_removed_ind` Int8, `response_headers_added_ind` Int8, `response_headers_removed_ind` Int8, `robot_txt` String, `robots_txt_chg_ind` Int8', ");
		stringBuilder.append("   'zstd') ");
		stringBuilder.append("select * ");
		stringBuilder.append("from " + getTableName(tableName));
		stringBuilder.append(" where track_date = '" + trackDateString + "'");
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("backupToS3() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		//if (isDebug == true) {
		//	connectionUrl = connectionUrlList.get(connectionIndex);
		//	FormatUtils.getInstance().logMemoryUsage("backupToS3() connectionUrl=" + connectionUrl);
		//}
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				preparedStatement = connection.prepareStatement(sqlString);
				resultSet = preparedStatement.executeQuery();
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				e.printStackTrace();
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("backupToS3() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			} finally {
				if (resultSet != null) {
					resultSet.close();
				}
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();
				}
			}
		}
		//FormatUtils.getInstance().logMemoryUsage("backupToS3() ends. s3ObjectURI=" + s3ObjectURI + ",tableName=" + tableName + ",trackDateString="
		//		+ trackDateString + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	public void restoreFromS3(String s3ObjectURI, String tableName) throws Exception {
		//FormatUtils.getInstance().logMemoryUsage("restoreFromS3() begins. s3ObjectURI=" + s3ObjectURI + ",tableName=" + tableName);
		//long startTimestamp = System.currentTimeMillis();
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("insert into " + getTableName(tableName) + " ");
		stringBuilder.append("select * ");
		stringBuilder.append("from s3('" + s3ObjectURI + "', ");
		stringBuilder.append("  '" + s3AccessKey + "', ");
		stringBuilder.append("  '" + s3SecretKey + "', ");
		stringBuilder.append("  'Native', ");
		stringBuilder.append(
				"  '`domain_id` UInt32, `url` String, `track_date` Date, `alt_img_list` String, `alternate_links` String, `amphtml_flag` Int8, `amphtml_href` String, `analyzed_url_flg_s` String, `analyzed_url_s` String, `archive_flg` String, `archive_flg_x_tag` String, `blocked_by_robots` String, `canonical` String, `canonical_flg` String, `canonical_header_flag` Int8, `canonical_header_type` String, `canonical_type` String, `canonical_url_is_consistent` String, `content_type` String, `count_of_objects` Int32, `custom_data` String, `description` String, `description_flg` String, `description_length` Int32, `description_simhash` String, `document_size` Int32, `download_latency` String, `download_time` String, `error_message` String, `final_response_code` Int32, `folder_level_1` String, `folder_level_2` String, `folder_level_3` String, `folder_level_count` Int32, `follow_flg` String, `follow_flg_x_tag` String, `h1` String, `h1_count` Int32, `h1_flg` String, `h1_length` Int32, `h1_md5` String, `h1_simhash` String, `h2` String, `h2_simhash` String, `header_noarchive` Int8, `header_nofollow` Int8, `header_noindex` Int8, `header_noodp` Int8, `header_nosnippet` Int8, `header_noydir` Int8, `hreflang_errors` String, `hreflang_links` String, `hreflang_links_out_count` Int32, `hreflang_url_count` Int32, `index_flg` String, `index_flg_x_tag` String, `indexable` Int8, `insecure_resources` String, `insecure_resources_flag` Int8, `long_redirect` Int8, `meta_charset` String, `meta_content_type` String, `meta_disabled_sitelinks` Int8, `meta_noodp` Int8, `meta_nosnippet` Int8, `meta_noydir` Int8, `meta_redirect` Int8, `mixed_redirects` Int8, `mobile_rel_alternate_url_is_consistent` Int8, `noodp` Int8, `nosnippet` Int8, `noydir` Int8, `og_markup` String, `og_markup_flag` Int8, `og_markup_length` Int32, `outlink_count` Int32, `page_1` Int8, `page_link` String, `page_timeout_flag` Int8, `paginated` Int8, `pagination_links` String, `protocol` String, `redirect_blocked` Int8, `redirect_blocked_reason` String, `redirect_chain` String, `redirect_final_url` String, `redirect_flg` Int8, `redirect_times` Int32, `rel_next_html_url` String, `rel_next_url_is_consistent` Int8, `rel_prev_url_is_consistent` Int8, `request_headers` String, `request_time` String, `response_code` String, `response_headers` String, `retry_attempted` Int8, `robots` String, `robots_contents` String, `robots_contents_x_tag` Int8, `robots_flg` String, `robots_flg_x_tag` String, `server_response_time` String, `source_url` String, `splash_took` String, `structured_data` String, `title` String, `title_flg` String, `title_length` Int32, `title_md5` String, `title_simhash` String, `twitter_description_length` Int32, `twitter_markup` String, `twitter_markup_flag` Int8, `twitter_markup_length` Int32, `url_length` Int32, `valid_twitter_card` String, `viewport_content` String, `viewport_flag` Int8, `page_analysis_results` String, `change_tracking_hash` String, `week_of_year` Int32, `crawl_timestamp` DateTime, `url_hash` UInt64, `lower_case_url_hash` UInt64, `url_murmur_hash` UInt32, `alternate_links_chg_ind` Int8, `amphtml_flag_chg_ind` Int8, `amphtml_href_chg_ind` Int8, `analyzed_url_flg_s_chg_ind` Int8, `analyzed_url_s_chg_ind` Int8, `archive_flg_chg_ind` Int8, `archive_flg_x_tag_chg_ind` Int8, `blocked_by_robots_chg_ind` Int8, `canonical_chg_ind` Int8, `canonical_flg_chg_ind` Int8, `canonical_header_flag_chg_ind` Int8, `canonical_header_type_chg_ind` Int8, `canonical_type_chg_ind` Int8, `canonical_url_is_consistent_chg_ind` Int8, `content_type_chg_ind` Int8, `description_chg_ind` Int8, `description_flg_chg_ind` Int8, `description_length_chg_ind` Int8, `description_simhash_chg_ind` Int8, `error_message_chg_ind` Int8, `final_response_code_chg_ind` Int8, `follow_flg_chg_ind` Int8, `follow_flg_x_tag_chg_ind` Int8, `h1_chg_ind` Int8, `h1_count_chg_ind` Int8, `h1_flg_chg_ind` Int8, `h1_length_chg_ind` Int8, `h1_md5_chg_ind` Int8, `header_noarchive_chg_ind` Int8, `header_nofollow_chg_ind` Int8, `header_noindex_chg_ind` Int8, `header_noodp_chg_ind` Int8, `header_nosnippet_chg_ind` Int8, `header_noydir_chg_ind` Int8, `hreflang_errors_chg_ind` Int8, `hreflang_links_chg_ind` Int8, `hreflang_links_out_count_chg_ind` Int8, `hreflang_url_count_chg_ind` Int8, `index_flg_chg_ind` Int8, `index_flg_x_tag_chg_ind` Int8, `indexable_chg_ind` Int8, `insecure_resources_chg_ind` Int8, `insecure_resources_flag_chg_ind` Int8, `meta_charset_chg_ind` Int8, `meta_content_type_chg_ind` Int8, `meta_disabled_sitelinks_chg_ind` Int8, `meta_noodp_chg_ind` Int8, `meta_nosnippet_chg_ind` Int8, `meta_noydir_chg_ind` Int8, `meta_redirect_chg_ind` Int8, `mixed_redirects_chg_ind` Int8, `mobile_rel_alternate_url_is_consistent_chg_ind` Int8, `noodp_chg_ind` Int8, `nosnippet_chg_ind` Int8, `noydir_chg_ind` Int8, `og_markup_chg_ind` Int8, `og_markup_flag_chg_ind` Int8, `og_markup_length_chg_ind` Int8, `outlink_count_chg_ind` Int8, `redirect_blocked_chg_ind` Int8, `redirect_blocked_reason_chg_ind` Int8, `redirect_chain_chg_ind` Int8, `redirect_final_url_chg_ind` Int8, `redirect_flg_chg_ind` Int8, `redirect_times_chg_ind` Int8, `response_code_chg_ind` Int8, `robots_chg_ind` Int8, `robots_contents_chg_ind` Int8, `robots_contents_x_tag_chg_ind` Int8, `robots_flg_chg_ind` Int8, `robots_flg_x_tag_chg_ind` Int8, `structured_data_chg_ind` Int8, `title_chg_ind` Int8, `title_flg_chg_ind` Int8, `title_length_chg_ind` Int8, `title_md5_chg_ind` Int8, `title_simhash_chg_ind` Int8, `viewport_content_chg_ind` Int8, `viewport_flag_chg_ind` Int8, `change_tracking_hash_cd_json` String, `sign` Int8, `page_analysis_results_chg_ind_json` String, `canonical_added_ind` Int8, `canonical_removed_ind` Int8, `description_added_ind` Int8, `description_removed_ind` Int8, `h1_added_ind` Int8, `h1_removed_ind` Int8, `h2_added_ind` Int8, `h2_chg_ind` Int8, `h2_removed_ind` Int8, `hreflang_links_added_ind` Int8, `hreflang_links_removed_ind` Int8, `open_graph_added_ind` Int8, `open_graph_removed_ind` Int8, `redirect_301_chg_ind` Int8, `redirect_301_detected_ind` Int8, `redirect_301_removed_ind` Int8, `redirect_302_chg_ind` Int8, `redirect_302_detected_ind` Int8, `redirect_302_removed_ind` Int8, `redirect_diff_code_ind` Int8, `robots_added_ind` Int8, `robots_removed_ind` Int8, `title_added_ind` Int8, `title_removed_ind` Int8, `viewport_added_ind` Int8, `viewport_removed_ind` Int8, `page_link_chg_ind` Int8, `internal_link_count` Int32, `custom_data_chg_ind` Int8, `page_analysis_results_reverse` String, `base_tag` String, `base_tag_flag` Int8, `base_tag_target` String, `base_tag_chg_ind` Int8, `base_tag_flag_chg_ind` Int8, `base_tag_target_chg_ind` Int8, `base_tag_added_ind` Int8, `base_tag_removed_ind` Int8, `page_analysis_fragments` String, `page_analysis_fragments_chg_ind` Int8, `content_guard_ind` Int8, `custom_data_added_ind` Int8, `custom_data_removed_ind` Int8, `response_headers_added_ind` Int8, `response_headers_removed_ind` Int8, `robot_txt` String, `robots_txt_chg_ind` Int8', ");
		stringBuilder.append("   'zstd') ");
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("restoreFromS3() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		//if (isDebug == true) {
		//	connectionUrl = connectionUrlList.get(connectionIndex);
		//	FormatUtils.getInstance().logMemoryUsage("restoreFromS3() connectionUrl=" + connectionUrl);
		//}
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				preparedStatement = connection.prepareStatement(sqlString);
				resultSet = preparedStatement.executeQuery();
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				e.printStackTrace();
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("restoreFromS3() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			} finally {
				if (resultSet != null) {
					resultSet.close();
				}
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();
				}
			}
		}
		//FormatUtils.getInstance().logMemoryUsage("restoreFromS3() ends. s3ObjectURI=" + s3ObjectURI + ",tableName=" + tableName + ",elapsed(s.)="
		//		+ (System.currentTimeMillis() - startTimestamp) / 1000);
	}

}
