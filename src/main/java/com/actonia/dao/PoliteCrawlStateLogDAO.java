package com.actonia.dao;

import com.actonia.entity.PoliteCrawlStateLog;

import java.util.List;
import java.util.stream.Collectors;

public class PoliteCrawlStateLogDAO extends BaseJdbcSupport<PoliteCrawlStateLog> {

    private static final String TABLE_NAME = "polite_crawl_state_log";
    private static final String COL_ID = "id";
    private static final String COL_OWN_DOMAIN_ID = "ownDomainId";
    private static final String COL_URL_MURMUR3_HASH = "urlMurmur3Hash";
    private static final String COL_CRAWL_DATE = "crawlDate";
    private static final String COL_RESPONSE_CODE = "responseCode";
    private static final String COL_URL_TYPE = "urlType";
    private static final String COL_CREATE_DATE = "createDate";

    private static final String SQL_INSERT = "INSERT INTO " + TABLE_NAME + " (" +
            COL_OWN_DOMAIN_ID + ", " +
            COL_URL_MURMUR3_HASH + ", " +
            COL_CRAWL_DATE + ", " +
            COL_RESPONSE_CODE + ", " +
            COL_URL_TYPE + ", " +
            COL_CREATE_DATE +
            ") VALUES (?, ?, ?, ?, ?, ?)";

    // Other SQL statements for updates, selects, etc.

    public void insert(PoliteCrawlStateLog log) {
        getSimpleJdbcTemplate().update(SQL_INSERT,
                log.getOwnDomainId(),
                log.getUrlMurmur3Hash(),
                log.getCrawlDate(),
                log.getResponseCode(),
                log.getUrlType(),
                log.getCreateDate());
    }

    @Override
    public String getTableName() {
        return TABLE_NAME;
    }

    // batch insert method
    public void batchInsert(List<PoliteCrawlStateLog> list) {
        final String batchInsertSql = "INSERT ignore INTO " + TABLE_NAME + " (" +
                COL_OWN_DOMAIN_ID + ", " +
                COL_URL_MURMUR3_HASH + ", " +
                COL_CRAWL_DATE + ", " +
                COL_RESPONSE_CODE + ", " +
                COL_URL_TYPE +
                ") VALUES (?, ?, ?, ?, ?)";
        final List<Object[]> dataBatch = list.stream().map(politeCrawlStateLog -> new Object[]{politeCrawlStateLog.getOwnDomainId(),
                politeCrawlStateLog.getUrlMurmur3Hash(),
                politeCrawlStateLog.getCrawlDate(),
                politeCrawlStateLog.getResponseCode(),
                politeCrawlStateLog.getUrlType()}).collect(Collectors.toList());
        super.executeBatch(batchInsertSql, dataBatch);
    }

    public List<PoliteCrawlStateLog> findListByOwnDomainId(int ownDomainId) {
        String sql = "SELECT t3.ownDomainId, t3.urlMurmur3Hash, COUNT(*) cnt, " +
                "SUM(IF(responseCode = 403 OR responseCode = 408 OR (responseCode >= 500 AND responseCode <= 599), 1, 0)) ngResponseCodeCnt " +
                "FROM (SELECT ownDomainId, urlMurmur3Hash, MIN(crawlDate) minDate " +
                "FROM (SELECT ownDomainId, l.urlMurmur3Hash, crawlDate, " +
                "RANK() OVER (PARTITION BY urlMurmur3Hash ORDER BY crawlDate DESC) AS nthDay " +
                "FROM polite_crawl_state_log l JOIN t_target_url u ON l.ownDomainId = u.own_domain_id AND l.urlMurmur3Hash = u.urlMurmur3Hash " +
                "WHERE l.ownDomainId = ? AND u.disable_crawl = 0 AND l.urlType in (1, 2)) k WHERE nthDay = 7 GROUP BY ownDomainId, urlMurmur3Hash) t " +
                "JOIN polite_crawl_state_log t3 ON t.ownDomainId = t3.ownDomainId " +
                "AND t.urlMurmur3Hash = t3.urlMurmur3Hash AND t.minDate <= t3.crawlDate " +
                "GROUP BY t3.ownDomainId, t3.urlMurmur3Hash " +
                "HAVING cnt >= 7 AND ngResponseCodeCnt >= cnt";
        return super.findBySql(sql, ownDomainId);
    }


    public List<String> findTodayUrlMurmur3HashListByDomainId(int domainId) {
        String sql = "SELECT urlMurmur3Hash FROM polite_crawl_state_log WHERE ownDomainId = ? AND crawlDate = CURDATE()";
        System.out.println(sql);
        return super.queryForStringList(sql, domainId);
    }
}
