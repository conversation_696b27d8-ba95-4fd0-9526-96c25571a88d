/**
 *
 */
package com.actonia.dao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.actonia.entity.UserDomain;
import com.actonia.entity.UserEntity;

/**
 * com.actonia.subserver.dao.UserDAO.java
 *
 * <AUTHOR>
 * @version $Revision: 65109 $
 *          $Author: wangc@SHINETECHCHINA $
 */
public class UserDAO extends BaseJdbcSupport<UserEntity> {

    @Override
    public String getTableName() {
        return "t_user";
    }

    public UserEntity getUser(int userId) {
        String sql = "select * from t_user where id = ? ";
        return findObject(sql, userId);
    }
    
    //https://www.wrike.com/open.htm?id=37003416
    //by cee
    public UserEntity getUser(int userId, int ownDomainId) {
    	StringBuffer sql = new StringBuffer();
        sql.append(" select tu.* ");
        sql.append(" from t_user tu ");
        sql.append(" left join t_user_domain rel  ");
        sql.append(" on tu.id = rel.user_id ");
        sql.append(" where tu.id = ? ");
        sql.append(" and tu.state = ? ");
        sql.append(" and rel.domain_id = ? ");
        sql.append(" and rel.state = ? ");
        
        return findObject(sql.toString(), userId, UserEntity.STATE_ACTIVE, ownDomainId, UserDomain.STATE_ACTIVE);
    }
    
    //https://www.wrike.com/open.htm?id=54915680
    //by cee
    public UserEntity getUserToSendDailyEmail(int userId, int ownDomainId) {
    	StringBuffer sql = new StringBuffer();
        sql.append(" select tu.* ");
        sql.append(" from t_user tu ");
        sql.append(" left join t_user_domain rel  ");
        sql.append(" on tu.id = rel.user_id ");
        sql.append(" where tu.id = ? ");
        sql.append(" and tu.state = ? ");
        sql.append(" and rel.domain_id = ? ");
        sql.append(" and rel.state = ? ");
        sql.append(" and (rel.suppress_alert_email is null or rel.suppress_alert_email not in (1, 3) ) ");
        
        return findObject(sql.toString(), userId, UserEntity.STATE_ACTIVE, ownDomainId, UserDomain.STATE_ACTIVE);
    }
    
    public List<UserEntity> getUsers(int ownDomainId) {
        StringBuffer sql = new StringBuffer();
        sql.append(" select tu.* ");
        sql.append(" from `t_user_domain` rel ");
        sql.append(" left join `t_user` tu on rel.user_id = tu.id ");
        sql.append(" where rel.domain_id = ? ");
        sql.append(" and rel.state = ? ");
        sql.append(" and tu.state = ? ");
        return findBySql(sql.toString(), ownDomainId, UserDomain.STATE_ACTIVE, UserEntity.STATE_ACTIVE);
    }
    
    //by cee
    public List<UserEntity> getAvailableUsers(int ownDomainId) {
        StringBuffer sql = new StringBuffer();
        sql.append(" select tu.* ");
        sql.append(" from `t_user_domain` rel ");
        sql.append(" left join `t_user` tu on rel.user_id = tu.id ");
        sql.append(" where rel.domain_id = ? ");
        sql.append(" and rel.state = ? ");
        sql.append(" and tu.state = ? ");
        return findBySql(sql.toString(), ownDomainId, UserDomain.STATE_ACTIVE, UserEntity.STATE_ACTIVE);
    }
    
    //https://www.wrike.com/open.htm?id=27806116
    //by cee
    public List<UserEntity> getUsersWhoEnableWeeklyEmail(int ownDomainId) {
        StringBuffer sql = new StringBuffer();
        sql.append(" select tu.* ");
        sql.append(" from `t_user_domain` rel ");
        sql.append(" left join `t_user` tu on rel.user_id = tu.id ");
        sql.append(" where rel.domain_id = ? ");
        sql.append(" and rel.state = ? ");
        sql.append(" and tu.state = ? ");
        sql.append(" and (rel.suppress_alert_email is null or rel.suppress_alert_email not in (2, 3) ) ");
        sql.append(" order by tu.id desc ");

        return findBySql(sql.toString(), ownDomainId, 1, UserEntity.STATE_ACTIVE);
    }
    
    //https://www.wrike.com/open.htm?id=27806116
    //by cee
    public List<UserEntity> getUsersWhoEnableDailyEmail(int ownDomainId) {
        StringBuffer sql = new StringBuffer();
        sql.append(" select tu.* ");
        sql.append(" from `t_user_domain` rel ");
        sql.append(" left join `t_user` tu on rel.user_id = tu.id ");
        sql.append(" where rel.domain_id = ? ");
        sql.append(" and rel.state = ? ");
        sql.append(" and tu.state = ? ");
        sql.append(" and (rel.suppress_alert_email is null or rel.suppress_alert_email not in (1, 3) ) ");
        sql.append(" order by tu.id desc ");

        return findBySql(sql.toString(), ownDomainId, 1, UserEntity.STATE_ACTIVE);
    }

    /**
     * find a user by his domain id and email address
     *
     * @param ownDomainId
     * @param useremail
     * @return
     */
    public UserEntity getUserByUserEmailAndDomainId(Integer ownDomainId, String useremail) {

        StringBuilder sql = new StringBuilder();
        sql.append(" select tu.* ");
        sql.append(" from t_user tu ");
        sql.append(" left join t_user_domain tud ");
        sql.append(" on tu.id=tud.user_id ");
        sql.append(" where tud.domain_id=? ");
        sql.append(" and tu.email=? and tu.state=? ");
        return findObject(sql.toString(), ownDomainId, useremail, UserEntity.STATE_ACTIVE);

    }

    public List<Object[]> getActiveUserInformation() {
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT  u.email,u.company,ud.roles,od.google_analytics_id,od.ga_session_token ,od.ga_oauth2_access_token, od.ga_oauth2_refresh_token,od.domain,od.id ");
        sql.append(" FROM t_user_domain ud ");
        sql.append(" LEFT JOIN t_user u ON ud.user_id = u.id ");
        sql.append(" LEFT JOIN t_own_domain od  ON od.id = ud.domain_id ");
        sql.append(" WHERE ud.state = 1 ");
        //return this.queryForObjectArrayList(sql.toString());
        //return this.query
        List<Map> mapList = this.queryForMapList(sql.toString());
        List<Object[]> values = new ArrayList<Object[]>();
        for (Map m : mapList) {
            Object[] value = new Object[9];
            value[0] = String.valueOf(m.get("email"));
            value[1] = String.valueOf(m.get("company"));
            value[2] = String.valueOf(m.get("roles"));
            value[3] = String.valueOf(m.get("google_analytics_id"));
            value[4] = String.valueOf(m.get("ga_session_token"));
            value[5] = String.valueOf(m.get("ga_oauth2_access_token"));
            value[6] = String.valueOf(m.get("ga_oauth2_refresh_token"));
            value[7] = String.valueOf(m.get("domain"));
            value[8] = String.valueOf(m.get("id"));
            values.add(value);
        }
        return values;
        //return this.queryForMapList(sql.toString());
    }

	public int updateIntProperty(int id, String property, Object value) {
		if (value == null) {
			value = 0;
		}
		String sql = "update t_user set " + property + " = ? where id = ? limit 1 ";
		return executeUpdate(sql, value, id);
	}

    public UserEntity getByEmail(String email) {
    	StringBuffer sql = new StringBuffer();
        sql.append("select ");
        sql.append(" t_user.* ");
        sql.append("from ");
        sql.append(" t_user t_user ");
        sql.append("where  ");
        sql.append(" t_user.email = ? ");        
        return findObject(sql.toString(), email);
    }
}
