package com.actonia.dao;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.actonia.IConstants;
import com.actonia.entity.ContentGuardCrawlTrackingEntity;

public class ContentGuardCrawlTrackingDAO extends BaseJdbcSupport<ContentGuardCrawlTrackingEntity> {

	private static final int NUMBER_OF_FIELDS = 6;

	@Override
	public String getTableName() {
		return "content_guard_crawl_tracking";
	}

	public void insertMultiRowsBatch(List<ContentGuardCrawlTrackingEntity> contentGuardCrawlTrackingEntityList) {
		Object[] objectArray = null;
		List<Object[]> objectArrayList = new ArrayList<Object[]>();
		List<ContentGuardCrawlTrackingEntity> tempList = new ArrayList<ContentGuardCrawlTrackingEntity>();

		for (ContentGuardCrawlTrackingEntity contentGuardCrawlTrackingEntity : contentGuardCrawlTrackingEntityList) {
			tempList.add(contentGuardCrawlTrackingEntity);
			if (tempList.size() == IConstants.RECORDS_PER_SQL_STATEMENT) {
				objectArray = getObjectArrayForInsert(tempList);
				objectArrayList.add(objectArray);
				if (objectArrayList.size() == IConstants.SQL_STATEMENTS_PER_JDBC_CALL) {
					this.executeBatch(getInsertMultiRowsSqlStatement(IConstants.RECORDS_PER_SQL_STATEMENT), objectArrayList);
					objectArrayList = new ArrayList<Object[]>();
				}
				tempList = new ArrayList<ContentGuardCrawlTrackingEntity>();
			}
		}

		if (objectArrayList != null && objectArrayList.size() > 0) {
			this.executeBatch(getInsertMultiRowsSqlStatement(IConstants.RECORDS_PER_SQL_STATEMENT), objectArrayList);
			objectArrayList = new ArrayList<Object[]>();
		}

		if (tempList != null && tempList.size() > 0) {
			objectArray = getObjectArrayForInsert(tempList);
			objectArrayList.add(objectArray);
			if (objectArrayList != null && objectArrayList.size() > 0) {
				this.executeBatch(getInsertMultiRowsSqlStatement(tempList.size()), objectArrayList);
				objectArrayList = new ArrayList<Object[]>();
			}
		}
	}

	private Object[] getObjectArrayForInsert(List<ContentGuardCrawlTrackingEntity> list) {
		List<Object[]> tempObjectArrayList = new ArrayList<Object[]>();
		Object[] targetUrlDailyCrawlTrackingObjectArray = null;
		int totalNumberOfObjects = 0;
		for (ContentGuardCrawlTrackingEntity contentGuardCrawlTrackingEntity : list) {
			targetUrlDailyCrawlTrackingObjectArray = new Object[] { contentGuardCrawlTrackingEntity.getProcessType(), contentGuardCrawlTrackingEntity.getDomainId(),
					contentGuardCrawlTrackingEntity.getCrawlFrequencyType(), contentGuardCrawlTrackingEntity.getDomainName(),
					contentGuardCrawlTrackingEntity.getCrawlTimestamp(), contentGuardCrawlTrackingEntity.getTotalUrls(), };
			tempObjectArrayList.add(targetUrlDailyCrawlTrackingObjectArray);
		}

		totalNumberOfObjects = tempObjectArrayList.size() * NUMBER_OF_FIELDS;

		Object[] objectArray = new Object[totalNumberOfObjects];
		int idx = 0;
		for (Object[] tempObjectArray : tempObjectArrayList) {
			for (int i = 0; i < tempObjectArray.length; i++) {
				objectArray[idx++] = tempObjectArray[i];
			}
		}
		return objectArray;
	}

	private String getInsertMultiRowsSqlStatement(int recordsPerInsertStatement) {
		String response = null;
		String sqlString = null;
		StringBuffer stringBuffer = new StringBuffer();
		stringBuffer.append(" insert into " + getTableName() + " ");
		stringBuffer.append(" (");
		stringBuffer.append("	process_type,");
		stringBuffer.append("	domain_id,");
		stringBuffer.append("	crawl_frequency_type,");
		stringBuffer.append("	domain_name,");
		stringBuffer.append("	crawl_timestamp,");
		stringBuffer.append("	total_urls");
		stringBuffer.append(" )");
		stringBuffer.append("values ");
		sqlString = stringBuffer.toString();

		StringBuffer sql = new StringBuffer();
		sql.append(sqlString);

		for (int i = 0; i < recordsPerInsertStatement; i++) {
			if (i == 0) {
				sql.append("(?,?,?,?,?,?)");
			} else {
				sql.append(",(?,?,?,?,?,?)");
			}
		}
		sql.append(IConstants.SEMI_COLON);
		response = sql.toString();
		return response;
	}

	public ContentGuardCrawlTrackingEntity get(int processType, int domainId, int crawlFrequencyType) {
		StringBuffer stringBuffer = new StringBuffer();
		stringBuffer.append(" select");
		stringBuffer.append(" * ");
		stringBuffer.append(" from");
		stringBuffer.append(" " + getTableName());
		stringBuffer.append(" where");
		stringBuffer.append("     process_type = ?");
		stringBuffer.append(" and domain_id = ?");
		stringBuffer.append(" and crawl_frequency_type = ?");
		String sqlString = stringBuffer.toString();
		return findObject(sqlString, processType, domainId, crawlFrequencyType);
	}

	public int update(int processType, int domainId, int crawlFrequencyType, Date crawlTimestamp, int totalUrls) {
		StringBuffer stringBuffer = new StringBuffer();
		stringBuffer.append(" update");
		stringBuffer.append(" " + getTableName());
		stringBuffer.append(" set");
		stringBuffer.append("    crawl_timestamp = ?,");
		stringBuffer.append("    total_urls = ?");
		stringBuffer.append(" where");
		stringBuffer.append("     process_type = ?");
		stringBuffer.append(" and domain_id = ?");
		stringBuffer.append(" and crawl_frequency_type = ?");
		String sqlString = stringBuffer.toString();
		return this.executeUpdate(sqlString, crawlTimestamp, totalUrls, processType, domainId, crawlFrequencyType);
	}

}