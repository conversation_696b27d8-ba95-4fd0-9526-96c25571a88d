package com.actonia.dao;

import java.util.ArrayList;
import java.util.List;

import com.actonia.IConstants;
import com.actonia.entity.CompetitorUrlWeeklyCrawlTrackingEntity;

public class CompetitorUrlWeeklyCrawlTrackingEntityDAO extends BaseJdbcSupport<CompetitorUrlWeeklyCrawlTrackingEntity> {

	private static final int NUMBER_OF_FIELDS = 6;

	@Override
	public String getTableName() {
		return "competitor_url_weekly_crawl_tracking";
	}

	public void resetTable(int processType) {
		String sqlString = "delete from " + getTableName() + " where process_type = ?";
		this.executeUpdate(sqlString, processType);
	}

	public void insertMultiRowsBatch(List<CompetitorUrlWeeklyCrawlTrackingEntity> competitorUrlWeeklyCrawlTrackingEntityList) {
		Object[] objectArray = null;
		List<Object[]> objectArrayList = new ArrayList<Object[]>();
		List<CompetitorUrlWeeklyCrawlTrackingEntity> tempList = new ArrayList<CompetitorUrlWeeklyCrawlTrackingEntity>();

		for (CompetitorUrlWeeklyCrawlTrackingEntity competitorUrlWeeklyCrawlTrackingEntity : competitorUrlWeeklyCrawlTrackingEntityList) {
			tempList.add(competitorUrlWeeklyCrawlTrackingEntity);
			if (tempList.size() == IConstants.RECORDS_PER_SQL_STATEMENT) {
				objectArray = getObjectArrayForInsert(tempList);
				objectArrayList.add(objectArray);
				if (objectArrayList.size() == IConstants.SQL_STATEMENTS_PER_JDBC_CALL) {
					this.executeBatch(getInsertMultiRowsSqlStatement(IConstants.RECORDS_PER_SQL_STATEMENT), objectArrayList);
					objectArrayList = new ArrayList<Object[]>();
				}
				tempList = new ArrayList<CompetitorUrlWeeklyCrawlTrackingEntity>();
			}
		}

		if (objectArrayList != null && objectArrayList.size() > 0) {
			this.executeBatch(getInsertMultiRowsSqlStatement(IConstants.RECORDS_PER_SQL_STATEMENT), objectArrayList);
			objectArrayList = new ArrayList<Object[]>();
		}

		if (tempList != null && tempList.size() > 0) {
			objectArray = getObjectArrayForInsert(tempList);
			objectArrayList.add(objectArray);
			if (objectArrayList != null && objectArrayList.size() > 0) {
				this.executeBatch(getInsertMultiRowsSqlStatement(tempList.size()), objectArrayList);
				objectArrayList = new ArrayList<Object[]>();
			}
		}
	}

	private Object[] getObjectArrayForInsert(List<CompetitorUrlWeeklyCrawlTrackingEntity> list) {
		List<Object[]> tempObjectArrayList = new ArrayList<Object[]>();
		Object[] competitorUrlWeeklyCrawlTrackingObjectArray = null;
		int totalNumberOfObjects = 0;
		for (CompetitorUrlWeeklyCrawlTrackingEntity competitorUrlWeeklyCrawlTrackingEntity : list) {
			competitorUrlWeeklyCrawlTrackingObjectArray = new Object[] {  
					competitorUrlWeeklyCrawlTrackingEntity.getProcessType(),
					competitorUrlWeeklyCrawlTrackingEntity.getQueueName(), 
					competitorUrlWeeklyCrawlTrackingEntity.getCompetitorDomain(),
					competitorUrlWeeklyCrawlTrackingEntity.getTotalUrls(),
					competitorUrlWeeklyCrawlTrackingEntity.getConcurrentRequests(), 
					competitorUrlWeeklyCrawlTrackingEntity.getLastUpdateTimestamp(), };
			tempObjectArrayList.add(competitorUrlWeeklyCrawlTrackingObjectArray);
		}

		totalNumberOfObjects = tempObjectArrayList.size() * NUMBER_OF_FIELDS;

		Object[] objectArray = new Object[totalNumberOfObjects];
		int idx = 0;
		for (Object[] tempObjectArray : tempObjectArrayList) {
			for (int i = 0; i < tempObjectArray.length; i++) {
				objectArray[idx++] = tempObjectArray[i];
			}
		}
		return objectArray;
	}

	private String getInsertMultiRowsSqlStatement(int recordsPerInsertStatement) {
		String response = null;
		String sqlString = null;
		StringBuffer stringBuffer = new StringBuffer();
		stringBuffer.append(" insert into " + getTableName() + " ");
		stringBuffer.append(" (");
		stringBuffer.append("	process_type,");
		stringBuffer.append("	queue_name,");
		stringBuffer.append("	competitor_domain,");
		stringBuffer.append("	total_urls,");
		stringBuffer.append("	concurrent_requests,");
		stringBuffer.append("	last_update_timestamp");
		stringBuffer.append(" )");
		stringBuffer.append("values ");
		sqlString = stringBuffer.toString();

		StringBuffer sql = new StringBuffer();
		sql.append(sqlString);

		for (int i = 0; i < recordsPerInsertStatement; i++) {
			if (i == 0) {
				sql.append("(?,?,?,?,?,?)");
			} else {
				sql.append(",(?,?,?,?,?,?)");
			}
		}
		sql.append(IConstants.SEMI_COLON);
		response = sql.toString();
		return response;
	}

	public CompetitorUrlWeeklyCrawlTrackingEntity get(int processType, String queueName) {
		StringBuffer stringBuffer = new StringBuffer();
		stringBuffer.append(" select");
		stringBuffer.append(" * ");
		stringBuffer.append(" from");
		stringBuffer.append(" " + getTableName());
		stringBuffer.append(" where");
		stringBuffer.append("     process_type = ?");
		stringBuffer.append(" and queue_name = ?");
		String sqlString = stringBuffer.toString();
		return findObject(sqlString, processType, queueName);
	}

}