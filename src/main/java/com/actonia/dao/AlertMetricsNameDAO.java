package com.actonia.dao;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.actonia.entity.AlertMetricsNameEntity;

public class AlertMetricsNameDAO extends BaseJdbcSupport<AlertMetricsNameEntity> {
	
	@Override
	public String getTableName() {
		return "alert_metrics_name";
	}
	
	public Map<Integer, String> getMetricsNamesMap() {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * ");
		sql.append(" from ").append(getTableName());
		
		Map<Integer, String> resultMap = new HashMap<Integer, String>();
		List<AlertMetricsNameEntity> result = this.findBySql(sql.toString());		
		if (result != null) {
			for (AlertMetricsNameEntity entity : result) {
				resultMap.put(entity.getId(), entity.getMetricsName());			
			}
		}

		return resultMap;	
	}

	public List<AlertMetricsNameEntity> getAlertNames(int domainId, int urlType, int status) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("select ");
		stringBuilder.append("	  alert_metrics_name.id as id, ");
		stringBuilder.append("	  alert_metrics_name.metrics_name as metricsName ");
		stringBuilder.append("from ");
		stringBuilder.append("	  alert_metrics alert_metrics, ");
		stringBuilder.append("	  alert_metrics_name alert_metrics_name ");
		stringBuilder.append("where ");
		stringBuilder.append("    alert_metrics.own_domain_id = ? ");
		stringBuilder.append("and alert_metrics.url_type = ? ");
		stringBuilder.append("and alert_metrics.`status` = ? ");
		stringBuilder.append("and alert_metrics.metrics_id = alert_metrics_name.id ");
		stringBuilder.append("order by ");
		stringBuilder.append("    alert_metrics_name.metrics_name ");
		String sqlString = stringBuilder.toString();
		return this.findBySql(sqlString, domainId, urlType, status);
	}
}