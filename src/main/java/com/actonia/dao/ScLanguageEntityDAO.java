package com.actonia.dao;

import java.util.List;

import com.actonia.entity.ScLanguageEntity;

public class ScLanguageEntityDAO extends BaseJdbcSupport<ScLanguageEntity> {

    public String getTableName() {
	    return "language_entity";
    }

	public String queryById(int id) {
		String sql = "select language from language_entity where id=?";
		ScLanguageEntity scLanguageEntity =  findObject(sql, id);
		if(scLanguageEntity != null) {
			return scLanguageEntity.getLanguage();
		}
		return null;
	}
	
	//Cee - https://www.wrike.com/open.htm?id=391372442
	public List<ScLanguageEntity> findAll() {
		String sql = "select * from language_entity order by id";
		return this.findBySql(sql);
	}
	
}
