package com.actonia.dao;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.actonia.entity.UserDomain;

public class UserDomainDAO extends BaseJdbcSupport<UserDomain> {

	@Override
	public String getTableName() {
		return "t_user_domain";
	}

	public List<UserDomain> getUserIdByDomainId(int ownDomainId) {
		String sql = "select * from t_user_domain where domain_id = ? and state = 1";
		return findBySql(sql, ownDomainId);
	}
	
	public UserDomain getUserIdByDomainAndUserId(int ownDomainId, int userId) {
		String sql = "select * from t_user_domain where domain_id = ? and user_id = ? and state = 1";
		return findObject(sql, ownDomainId, userId);
	}
	
	public UserDomain getByDomainAndUserId(int ownDomainId, int userId) {
		String sql = "select * from t_user_domain where domain_id = ? and user_id = ? ";
		return findObject(sql, ownDomainId, userId);
	}
	
    public List<UserDomain> getUsersByDomainId(int ownDomainId) {
        StringBuffer sql = new StringBuffer();
        sql.append(" select tud.*,tu.email from t_user tu join t_user_domain tud on tud.user_id = tu.id ");
        sql.append(" where tud.domain_id=? order by tud.id ");
        return findBySql(sql.toString(), ownDomainId);
    }
    
    public void deleteById(int id) {
        String sql = "delete from t_user_domain where id = ?";
        executeUpdate(sql, id);
    }
    
    public int inesrt(UserDomain userDomain) {
        Map<String, Object> values = new HashMap<String, Object>();
        
        values.put("user_id", userDomain.getUserId());
        values.put("domain_id", userDomain.getDomainId());
        values.put("roles", userDomain.getRoles());
        
        if (userDomain.getState() != null) {
        	values.put("state", userDomain.getState());
        }
        if (userDomain.getRole() != null) {
        	values.put("role", userDomain.getRole());
        }
        
        values.put("create_date", userDomain.getCreateDate() == null ? new Date() : userDomain.getCreateDate());
        values.put("suppress_alert_email", userDomain.getSuppressAlertEmail() == null ? 3 : userDomain.getSuppressAlertEmail());
        values.put("hidden_default_dashboard", userDomain.getHiddenDefaultDashboard() == null ? 0 : userDomain.getHiddenDefaultDashboard());
        values.put("hidden_domain_dashboard", userDomain.getHiddenDomainDashboard() == null ? 0 : userDomain.getHiddenDomainDashboard());
        values.put("grant_access_all_tags", userDomain.getGrantAccessAllTags() == null ? 0 : userDomain.getGrantAccessAllTags());
        values.put("is_sticky_competitor_admin", userDomain.getIsStickyCompetitorAdmin() == null ? 0 : userDomain.getIsStickyCompetitorAdmin());

        return this.insert(values);
    }
    
    public void update(UserDomain userDomain) {
        StringBuffer sql = new StringBuffer();
        sql.append(" update t_user_domain ");
        sql.append(" set roles = ? ");
        sql.append(" , state = ? ");
        sql.append(" , role = ? ");
        sql.append(" , suppress_alert_email = ? ");
        sql.append(" , hidden_default_dashboard = ? ");
        sql.append(" , hidden_domain_dashboard = ? ");
        sql.append(" , grant_access_all_tags = ? ");
        sql.append(" , is_sticky_competitor_admin = ? ");
        sql.append(" where id = ? ");
        sql.append(" limit 1 ");

        this.executeUpdate(sql.toString(), 
        		userDomain.getRoles(), 
        		userDomain.getState(),
        		userDomain.getRole(), 
        		userDomain.getSuppressAlertEmail(), 
        		userDomain.getHiddenDefaultDashboard(),
        		userDomain.getHiddenDomainDashboard(),
        		userDomain.getGrantAccessAllTags(),
        		userDomain.getIsStickyCompetitorAdmin(),
        		userDomain.getId());
    }
    
}