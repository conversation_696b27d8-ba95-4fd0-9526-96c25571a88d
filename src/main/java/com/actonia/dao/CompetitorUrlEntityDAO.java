/**
 * 
 */
package com.actonia.dao;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang.StringUtils;

import com.actonia.IConstants;
import com.actonia.entity.CompetitorUrlEntity;
import com.actonia.entity.KeywordEntity;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.utils.FormatUtils;

public class CompetitorUrlEntityDAO extends BaseJdbcSupport<CompetitorUrlEntity> {

	private static String SQL_GET_MANAGED_COMPETITOR_URLS_BY_DOMAIN;

	private static String SQL_GET_COMPETITORS_WITH_MANAGED_URLS;

	private static String SQL_GET_ASSOCIATED_COMPETITORS_URLS;

	private static String SQL_GET_BY_RECORD_ID;

	private static String SQL_UPDATE_URL_HASH;

	@Override
	public String getTableName() {
		return "t_competitor_url";
	}

	public CompetitorUrlEntity getCompetitorUrlEntityById(int id) {
		return this.findFirstBy("id", id);
	}

	public Integer getOwnDomainId(int id) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select tc.owndomain_id ");
		sql.append(" from t_competitor_url tcu, t_competitor tc ");
		sql.append(" where tcu.id = ? ");
		sql.append(" and tcu.competitor_id = tc.id");

		return queryForInteger(sql.toString(), id);
	}

	public List<CompetitorUrlEntity> getByDomainId(int domainId) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select tcu.* ");
		sql.append(" from t_competitor_url tcu, t_competitor tc ");
		sql.append(" where tc.owndomain_id = ? ");
		sql.append(" and tcu.competitor_id = tc.id");

		return findBySql(sql.toString(), domainId);
	}

	public String getUrlById(int id) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * ");
		sql.append(" from ").append(getTableName());
		sql.append(" where id = ? ");
		sql.append(" limit 1 ");

		CompetitorUrlEntity entity = this.findObject(sql.toString(), id);

		return entity == null ? null : entity.getUrl();
	}

	public String getYahooAppId(int id) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select tod.yahoo_appid ");
		sql.append(" from t_competitor_url tcu, t_competitor tc, t_own_domain tod ");
		sql.append(" where tcu.id = ? ");
		sql.append(" and tcu.competitor_id = tc.id ");
		sql.append(" and tc.owndomain_id = tod.id ");

		return this.queryForString(sql.toString(), id);
	}

	public List<CompetitorUrlEntity> getUrl(Date date, int maxId, int pageSize, int cycle, int domainId) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select tcu.id as id, tcu.url as url from t_competitor_url tcu left join t_url_metrics_data tumd ");
		sql.append(" on tcu.id=tumd.url_id  and tumd.type='3'");
		sql.append(" left join t_competitor tc on tcu.competitor_id = tc.id ");
		sql.append(" where (tumd.pr_update_date <= Date(?)-? or tumd.pr_update_date is null) ");
		sql.append(" and tcu.add_by=? and tcu.id>? ");
		sql.append(" and tc.owndomain_id=?");
		sql.append(" order by tcu.id ");

		return queryPageForList(sql.toString(), pageSize, date, cycle, IConstants.COMPETITORURL_ADD_BY_USER, maxId, domainId);
	}

	public Integer getUrlCount(Date date, int cycle, int domainId) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select count(*) from t_competitor_url tcu left join t_url_metrics_data tumd ");
		sql.append(" on tcu.id=tumd.url_id  and tumd.type='3' ");
		sql.append(" left join t_competitor tc on tcu.competitor_id = tc.id ");
		sql.append(" where (tumd.pr_update_date <= Date(?)-? or tumd.pr_update_date is null) ");
		sql.append(" and tcu.add_by=?  ");
		sql.append(" and tc.owndomain_id=?");

		return queryForInteger(sql.toString(), date, cycle, IConstants.COMPETITORURL_ADD_BY_USER, domainId);
	}

	public List<CompetitorUrlEntity> getYahooInlinkUrl(Date date, int maxId, int pageSize, int cycle, int ownDomainId, boolean firstQuery) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select tmp.id, tmp.url from ( ");
		sql.append("	(select tcu.id,tcu.url from t_competitor_url tcu left join t_competitor tc on tcu.competitor_id =tc.id ");
		sql.append("     where tc.owndomain_id=? and tcu.add_by=? ) tmp  ");
		// sql.append("     where tc.owndomain_id=? ) tmp  ");
		sql.append("	left join t_url_metrics_data tumd on tmp.id=tumd.url_id and tumd.type=3) ");
		sql.append(" where (tumd.yahoo_update_date <= Date(?)-? or tumd.yahoo_update_date is null) ");
		sql.append(" and tmp.id>? order by tmp.id ");
		return queryPageForList(sql.toString(), pageSize, ownDomainId, IConstants.COMPETITORURL_ADD_BY_USER, date, cycle, maxId);
	}

	public Integer getYahooInlinkUrlCount(Date date, int cycle, int ownDomainId) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select count(*) from ( ");
		sql.append("	(select tcu.id,tcu.url from t_competitor_url tcu left join t_competitor tc on tcu.competitor_id =tc.id ");
		sql.append("     where tc.owndomain_id=? and tcu.add_by=? ) tmp  ");
		// sql.append("     where tc.owndomain_id=?  ) tmp  ");
		sql.append("	left join t_url_metrics_data tumd on tmp.id=tumd.url_id and tumd.type=3) ");
		sql.append(" where (tumd.yahoo_update_date <= Date(?)-? or tumd.yahoo_update_date is null) ");

		return queryForInteger(sql.toString(), ownDomainId, IConstants.COMPETITORURL_ADD_BY_USER, date, cycle);

	}

	public List<CompetitorUrlEntity> getCacheDateUrl(Date date, int maxId, int pageSize, int cycle, int domainId) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select tcu.id as id, tcu.url as url from t_competitor_url tcu left join t_url_metrics_data tumd ");
		sql.append(" on tcu.id=tumd.url_id  and tumd.type='3'");
		sql.append(" left join t_competitor tc on tcu.competitor_id = tc.id ");
		sql.append(" where (tumd.cachedate_update_date <= Date(?)-? or tumd.cachedate_update_date is null) ");
		sql.append(" and tcu.add_by=? and tcu.id>? ");
		sql.append(" and tc.owndomain_id=?");
		sql.append(" order by tcu.id ");

		return queryPageForList(sql.toString(), pageSize, date, cycle, IConstants.COMPETITORURL_ADD_BY_USER, maxId, domainId);
	}

	public Integer getCacheDateUrlCount(Date date, int cycle, int domainId) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select count(*) from t_competitor_url tcu left join t_url_metrics_data tumd ");
		sql.append(" on tcu.id=tumd.url_id  and tumd.type='3' ");
		sql.append(" left join t_competitor tc on tcu.competitor_id = tc.id ");
		sql.append(" where (tumd.cachedate_update_date <= Date(?)-? or tumd.cachedate_update_date is null) ");
		sql.append(" and tcu.add_by=?  ");
		sql.append(" and tc.owndomain_id=?");

		return queryForInteger(sql.toString(), date, cycle, IConstants.COMPETITORURL_ADD_BY_USER, domainId);
	}

	public Integer getHtmlContentCountForCycle(Date date, int cycle, OwnDomainEntity ownDomainEntity) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select count(*) from t_competitor_url tcu left join t_competitor tc on tcu.competitor_id = tc.id  ");
		sql.append(" left join t_url_metrics_data tumd  on tcu.id=tumd.url_id and tumd.type=3");
		sql.append(" where (tumd.respcode_update_date <= Date(?)-? or tumd.respcode_update_date is null) ");
		sql.append(" and (tcu.add_by = ? or tcu.add_by = 30 ) and tc.owndomain_id=? ");

		return queryForInteger(sql.toString(), date, cycle, IConstants.COMPETITORURL_ADD_BY_USER, ownDomainEntity.getId());
	}

	public Integer getHtmlContentCountForDom(Date date, int cycle, OwnDomainEntity ownDomainEntity) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select count(*) from t_competitor_url tcu left join t_competitor tc on tcu.competitor_id = tc.id  ");
		sql.append(" left join t_url_metrics_data tumd  on tcu.id=tumd.url_id and tumd.type=3");
		sql.append(" where (tumd.respcode_update_date <= Date(?)-? or tumd.respcode_update_date is null) ");
		sql.append(" and (tcu.add_by = ? or tcu.add_by = 30 ) and tc.owndomain_id=? and tcu.type = 1 ");

		return queryForInteger(sql.toString(), date, cycle, IConstants.COMPETITORURL_ADD_BY_USER, ownDomainEntity.getId());
	}

	public Integer getDailyHtmlContentCount(String date, OwnDomainEntity ownDomainEntity) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select count(*) from t_competitor_url tcu left join t_competitor tc on tcu.competitor_id = tc.id  ");
		sql.append(" left join t_url_metrics_data tumd  on tcu.id=tumd.url_id and tumd.type=3");
		sql.append(" where date_format(tcu.create_date, '%Y-%m-%d')=? and (tcu.add_by = ? or tcu.add_by = 30 ) and tc.owndomain_id=? ");

		return queryForInteger(sql.toString(), date, IConstants.COMPETITORURL_ADD_BY_USER, ownDomainEntity.getId());
	}

	public Integer getHtmlContentCount(Date date, OwnDomainEntity ownDomainEntity) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select count(*) from t_competitor_url tcu left join t_competitor tc on tcu.competitor_id = tc.id  ");
		sql.append(" left join t_url_metrics_data tumd  on tcu.id=tumd.url_id and tumd.type=3");
		sql.append(" where (tumd.respcode_update_date != Date(?) or tumd.respcode_update_date is null) ");
		sql.append(" and (tcu.add_by = ? or tcu.add_by = 30 )and tc.owndomain_id=? ");

		// sql.append(" select count(*) from t_competitor_url tc left join t_url_metrics_data tumd ");
		// sql.append(" on tc.id=tumd.url_id and tumd.type=3 ");
		// sql.append(" where (tumd.respcode_update_date != Date(?) or tumd.respcode_update_date is null) ");
		// sql.append(" and tc.add_by = ?");

		return queryForInteger(sql.toString(), date, IConstants.COMPETITORURL_ADD_BY_USER, ownDomainEntity.getId());
	}

	public List<CompetitorUrlEntity> getHtmlContentForCycle(Date date, int cycle, int maxId, int pageSize, OwnDomainEntity ownDomainEntity) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select tcu.id as id, tcu.url as url , tc.owndomain_id as ownDomainId  from t_competitor_url tcu ");
		sql.append(" left join t_competitor tc on tcu.competitor_id = tc.id ");
		sql.append(" left join t_url_metrics_data tumd on tcu.id=tumd.url_id and tumd.type=3 ");
		sql.append(" where (tumd.respcode_update_date <= Date(?)-? or tumd.respcode_update_date is null) ");
		sql.append(" and  (tcu.add_by = ? or tcu.add_by = 30 ) ");
		sql.append(" and tcu.id>?  and tc.owndomain_id=?  ");
		sql.append(" order by tcu.id ");

		return queryPageForList(sql.toString(), pageSize, date, cycle, IConstants.COMPETITORURL_ADD_BY_USER, maxId, ownDomainEntity.getId());
	}

	public List<CompetitorUrlEntity> getHtmlContentForDom(Date date, int cycle, int maxId, int pageSize, OwnDomainEntity ownDomainEntity) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select tcu.id as id, tcu.url as url , tc.owndomain_id as ownDomainId  from t_competitor_url tcu ");
		sql.append(" left join t_competitor tc on tcu.competitor_id = tc.id ");
		sql.append(" left join t_url_metrics_data tumd on tcu.id=tumd.url_id and tumd.type=3 ");
		sql.append(" where (tumd.respcode_update_date <= Date(?)-? or tumd.respcode_update_date is null) ");
		sql.append(" and  (tcu.add_by = ? or tcu.add_by = 30 ) ");
		sql.append(" and tcu.id>?  and tc.owndomain_id=? and tcu.type = 1 ");
		sql.append(" order by tcu.id ");

		return queryPageForList(sql.toString(), pageSize, date, cycle, IConstants.COMPETITORURL_ADD_BY_USER, maxId, ownDomainEntity.getId());
	}

	public List<CompetitorUrlEntity> getDailyHtmlContent(String date, int maxId, int pageSize, OwnDomainEntity ownDomainEntity) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select tcu.id as id, tcu.url as url , tc.owndomain_id as ownDomainId  from t_competitor_url tcu ");
		sql.append(" left join t_competitor tc on tcu.competitor_id = tc.id ");
		sql.append(" left join t_url_metrics_data tumd on tcu.id=tumd.url_id and tumd.type=3 ");
		sql.append(" where date_format(tcu.create_date, '%Y-%m-%d')=? and (tcu.add_by = ? or tcu.add_by = 30 ) ");
		sql.append(" and tcu.id>?  and tc.owndomain_id=? order by tcu.id ");

		return queryPageForList(sql.toString(), pageSize, date, IConstants.COMPETITORURL_ADD_BY_USER, maxId, ownDomainEntity.getId());
	}

	public List<CompetitorUrlEntity> getHtmlContent(Date date, int maxId, int pageSize, OwnDomainEntity ownDomainEntity) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select tcu.id as id, tcu.url as url , tc.owndomain_id as ownDomainId  from t_competitor_url tcu ");
		sql.append(" left join t_competitor tc on tcu.competitor_id = tc.id ");
		sql.append(" left join t_url_metrics_data tumd on tcu.id=tumd.url_id and tumd.type=3 ");
		sql.append(" where (tumd.respcode_update_date != Date(?) or tumd.respcode_update_date is null) ");
		sql.append(" and  (tcu.add_by = ? or tcu.add_by = 30 ) ");
		sql.append(" and tcu.id>?  and tc.owndomain_id=?  ");
		sql.append(" order by tcu.id ");

		return queryPageForList(sql.toString(), pageSize, date, IConstants.COMPETITORURL_ADD_BY_USER, maxId, ownDomainEntity.getId());
	}

	public Integer getCompetitorDomainHtmlContentCount(Date date) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select count(*) from t_competitor tc left join t_url_metrics_data tumd ");
		sql.append(" on tc.id=tumd.url_id and tumd.type=3 ");
		sql.append(" where (tumd.respcode_update_date != Date(?) or tumd.respcode_update_date is null) ");
		// sql.append(" and tc.add_by = ?");

		return queryForInteger(sql.toString(), date);
	}

	public List<CompetitorUrlEntity> getCompetitorDomainHtmlContent(Date date, int maxId, int pageSize) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select tc.id as id, concat('http://',tc.domain) as url , tc.owndomain_id as ownDomainId  from ");
		sql.append(" t_competitor tc ");
		sql.append(" left join t_url_metrics_data tumd on tc.id=tumd.url_id and tumd.type=3 ");
		sql.append(" where (tumd.respcode_update_date != Date(?) or tumd.respcode_update_date is null) ");
		// sql.append(" and  tcu.add_by = ? ");
		sql.append(" and tc.id>?  order by tc.id ");

		return queryPageForList(sql.toString(), pageSize, date, maxId);
	}

	public Integer getSemRushHtmlContentCount(Date date, int cycle, OwnDomainEntity ownDomainEntity) {
		StringBuffer sql = new StringBuffer();
		// sql.append(" select count(*) from t_competitor_url tc left join t_url_metrics_data tumd ");
		// sql.append(" on tc.id=tumd.url_id and tumd.type=3 ");
		// sql.append(" where (tumd.semrush_update_date <= Date(?)-? or tumd.semrush_update_date is null) ");
		// sql.append(" and tc.add_by = 1");
		// sql.append(" and  tc.owndomain_id = ?");

		sql.append(" select count(*) from t_competitor_url tcu ");
		sql.append(" left join t_competitor tc on tcu.competitor_id = tc.id ");
		sql.append(" left join t_url_metrics_data tumd on tcu.id=tumd.url_id and tumd.type=3 ");
		sql.append(" where (tumd.semrush_update_date <= Date(?)-? or tumd.semrush_update_date is null) ");
		sql.append(" and  ( tcu.add_by = 1 or tcu.add_by = 30) ");
		sql.append(" and  tc.owndomain_id = ?");

		return queryForInteger(sql.toString(), date, cycle, ownDomainEntity.getId());
	}

	public List<CompetitorUrlEntity> getSemRushHtmlContent(Date date, int cycle, int maxId, int pageSize, OwnDomainEntity ownDomainEntity) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select tcu.id as id, tcu.url as url , tc.owndomain_id as ownDomainId, tcu.create_date as createDate from t_competitor_url tcu ");
		sql.append(" left join t_competitor tc on tcu.competitor_id = tc.id ");
		sql.append(" left join t_url_metrics_data tumd on tcu.id=tumd.url_id and tumd.type=3 ");
		sql.append(" where (tumd.semrush_update_date <= Date(?)-? or tumd.semrush_update_date is null) ");
		sql.append(" and  (tcu.add_by = 1 or tcu.add_by = 30 )");
		sql.append(" and  tc.owndomain_id = ?");
		sql.append(" and tcu.id>?  order by tcu.id ");

		return queryPageForList(sql.toString(), pageSize, date, cycle, ownDomainEntity.getId(), maxId);
	}

	public CompetitorUrlEntity getCompetitorUrlByOwnDomainidAndUrl(int ownDomainid, String url) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select tcu.id as id, tcu.add_by as addBy from ");
		sql.append(" `t_competitor_url` tcu left join `t_competitor` tc");
		sql.append(" on tcu.competitor_id = tc.id");
		sql.append(" where tc.owndomain_id=? and (tcu.url=? or tcu.url=?)");

		return findObject(sql.toString(), ownDomainid, url, url + "/");
	}

	public int insert(CompetitorUrlEntity competitorUrlEntity) {
		Map<String, Object> values = new HashMap<String, Object>();
		values.put("competitor_id", competitorUrlEntity.getCompetitorId());
		values.put("url", competitorUrlEntity.getUrl());
		values.put("create_date", competitorUrlEntity.getCreateDate());
		// add by user upload
		values.put("add_by", competitorUrlEntity.getAddBy());
		values.put("owndomain_id", competitorUrlEntity.getOwnDomainId());

		return this.insert(values);
	}

	public List<CompetitorUrlEntity> getSeoMozUrl(Date date, int maxId, int pageSize, int cycle, int ownDomainId, boolean refreshEveryMonth) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select tmp.id, tmp.url from ( ");
		sql.append("	(select tcu.id,tcu.url from t_competitor_url tcu left join t_competitor tc on tcu.competitor_id =tc.id ");
		sql.append("     where tc.owndomain_id=? and tcu.add_by=1 ) tmp  ");
		sql.append("	left join t_url_metrics_data tumd on tmp.id=tumd.url_id and tumd.type=3) ");
		if (refreshEveryMonth) {
			sql.append(" where (tumd.seomoz_update_date < Date(?) or tumd.seomoz_update_date is null) ");
		} else {
			sql.append(" where (tumd.seomoz_update_date is null) ");
		}
		sql.append(" and tmp.id>? order by tmp.id ");

		if (refreshEveryMonth) {
			return queryPageForList(sql.toString(), pageSize, ownDomainId, date, maxId);
		} else {
			return queryPageForList(sql.toString(), pageSize, ownDomainId, maxId);
		}
	}

	public Integer getSeoMozUrlCount(Date date, int cycle, int ownDomainId, boolean refreshEveryMonth) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select count(*) from ( ");
		sql.append("	(select tcu.id,tcu.url from t_competitor_url tcu left join t_competitor tc on tcu.competitor_id =tc.id ");
		sql.append("     where tc.owndomain_id=? and tcu.add_by=1  ) tmp  ");
		sql.append("	left join t_url_metrics_data tumd on tmp.id=tumd.url_id and tumd.type=3) ");
		if (refreshEveryMonth) {
			sql.append(" where (tumd.seomoz_update_date < Date(?) or tumd.seomoz_update_date is null) ");
			return queryForInteger(sql.toString(), ownDomainId, date);
		} else {
			sql.append(" where (tumd.seomoz_update_date is null) ");
			return queryForInteger(sql.toString(), ownDomainId);
		}
	}

	public List<CompetitorUrlEntity> getAllCompetitorUrls() {
		String sql = "select * from t_competitor_url";
		return findBySql(sql);
	}

	public void addUrlStartWithHttp(int id, String url) {
		String sql = "update t_competitor_url set url = ? where id = ?";
		executeUpdate(sql, url, id);
	}

	public CompetitorUrlEntity getCompetitorUrlBycIdAndUrl(int cometptiorId, String url) {
		String sql = "select * from t_competitor_url where competitor_id=? and (url=? or url=?) limit 1";
		return findObject(sql, cometptiorId, url, url + "/");
	}

	public void updateUrlAddBy(int id, int addBy) {
		String sql = "update t_competitor_url set add_by = ? where id = ?";
		executeUpdate(sql, addBy, id);
	}

	public int getTotalCountByDomainIdAddByUser(int domainId) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select count(*) ");
		sql.append(" from t_competitor_url tcu, t_competitor tc ");
		sql.append(" where tc.owndomain_id = ? ");
		sql.append(" and tcu.competitor_id = tc.id and tcu.add_by=? ");

		return queryForInt(sql.toString(), domainId, IConstants.COMPETITORURL_ADD_BY_USER);
	}

	public List<CompetitorUrlEntity> getByDomainIdAddByUserForPage(int domainId, int maxId, int pageSize) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select tcu.* ");
		sql.append(" from t_competitor_url tcu, t_competitor tc ");
		sql.append(" where tc.owndomain_id = ? ");
		sql.append(" and tcu.competitor_id = tc.id and tcu.add_by=? ");
		sql.append(" and tcu.id>? order by tcu.id ");
		return queryPageForList(sql.toString(), pageSize, domainId, IConstants.COMPETITORURL_ADD_BY_USER, maxId);
	}

	public List<CompetitorUrlEntity> getByDomainIdAddByUser(int domainId) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select tcu.* ");
		sql.append(" from t_competitor_url tcu, t_competitor tc ");
		sql.append(" where tc.owndomain_id = ? ");
		sql.append(" and tcu.competitor_id = tc.id and tcu.add_by=? ");
		return findBySql(sql.toString(), domainId, IConstants.COMPETITORURL_ADD_BY_USER);
	}

	public List<CompetitorUrlEntity> getByStartIndex(int ownDomainId, int competitorDomainId, int startIndex, int pageSize) {
		StringBuffer pageQuery = new StringBuffer();
		pageQuery.append(" select tcu.*  ");
		pageQuery.append(" from t_competitor_url tcu ");
		pageQuery.append(" where tcu.competitor_id = ? ");
		pageQuery.append(" and tcu.id > ? ");
		pageQuery.append(" and owndomain_id = ? ");
		pageQuery.append(" order by tcu.id ");
		pageQuery.append(" limit ").append(pageSize);

		return this.findBySql(pageQuery.toString(), competitorDomainId, startIndex, ownDomainId);
	}

	public CompetitorUrlEntity getCompetitorUrlByCompetorIdAndUrl(int competitorId, String url) {
		String sql = "select * from `t_competitor_url` where `competitor_id`=? and (url =? or url =?)";

		return findObject(sql.toString(), competitorId, url, url + "/");
	}

	public void deleteById(int id) {
		String sql = "delete from t_competitor_url where id =?";
		executeUpdate(sql, id);
	}

	public List<CompetitorUrlEntity> getPageSpeedUrl(Date date, int maxId, int pageSize, int cycle, int domainId) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select tcu.id as id, tcu.url as url from t_competitor_url tcu left join t_url_metrics_data tumd ");
		sql.append(" on tcu.id=tumd.url_id  and tumd.type='3'");
		sql.append(" left join t_competitor tc on tcu.competitor_id = tc.id ");
		sql.append(" where (tumd.pagespeed_update_date <= Date(?)-? or tumd.pagespeed_update_date is null) ");
		sql.append(" and tcu.add_by=? and tcu.id>? ");
		sql.append(" and tc.owndomain_id=?");
		sql.append(" order by tcu.id ");

		return queryPageForList(sql.toString(), pageSize, date, cycle, IConstants.COMPETITORURL_ADD_BY_USER, maxId, domainId);
	}

	public Integer getPageSpeedCount(Date date, int cycle, int domainId) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select count(*) from t_competitor_url tcu left join t_url_metrics_data tumd ");
		sql.append(" on tcu.id=tumd.url_id  and tumd.type='3' ");
		sql.append(" left join t_competitor tc on tcu.competitor_id = tc.id ");
		sql.append(" where (tumd.pagespeed_update_date <= Date(?)-? or tumd.pagespeed_update_date is null) ");
		sql.append(" and tcu.add_by=?  ");
		sql.append(" and tc.owndomain_id=?");

		return queryForInteger(sql.toString(), date, cycle, IConstants.COMPETITORURL_ADD_BY_USER, domainId);
	}

	public List<CompetitorUrlEntity> getMajesticseoUrl(Date date, int maxId, int pageSize, int cycle, int domainId) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select tcu.id as id, tcu.url as url from t_competitor_url tcu left join t_url_metrics_data tumd ");
		sql.append(" on tcu.id=tumd.url_id  and tumd.type='3'");
		sql.append(" left join t_competitor tc on tcu.competitor_id = tc.id ");
		sql.append(" where (tumd.majestics_update_date <= Date(?)-? or tumd.majestics_update_date is null) ");
		sql.append(" and tcu.add_by=? and tcu.id>? ");
		sql.append(" and tc.owndomain_id=?");
		sql.append(" order by tcu.id ");

		return queryPageForList(sql.toString(), pageSize, date, cycle, IConstants.COMPETITORURL_ADD_BY_USER, maxId, domainId);
	}

	public Integer getMajesticseoCount(Date date, int cycle, int domainId) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select count(*) from t_competitor_url tcu left join t_url_metrics_data tumd ");
		sql.append(" on tcu.id=tumd.url_id  and tumd.type='3' ");
		sql.append(" left join t_competitor tc on tcu.competitor_id = tc.id ");
		sql.append(" where (tumd.majestics_update_date <= Date(?)-? or tumd.majestics_update_date is null) ");
		sql.append(" and tcu.add_by=?  ");
		sql.append(" and tc.owndomain_id=?");

		return queryForInteger(sql.toString(), date, cycle, IConstants.COMPETITORURL_ADD_BY_USER, domainId);
	}

	public List<CompetitorUrlEntity> getManagedUrlByDomainIdCompetitorId(int domainId, int competitorId) {

		if (SQL_GET_MANAGED_COMPETITOR_URLS_BY_DOMAIN == null) {
			StringBuffer sql = new StringBuffer();
			sql.append(" select ");
			sql.append("     distinct(t_competitor_url.url) ");
			sql.append(" from ");
			sql.append("     t_competitor_url t_competitor_url ");
			sql.append(" where ");
			sql.append("     t_competitor_url.owndomain_id = ? ");
			sql.append(" and t_competitor_url.competitor_id = ? ");
			sql.append(" and t_competitor_url.add_by = ? ");
			SQL_GET_MANAGED_COMPETITOR_URLS_BY_DOMAIN = sql.toString();
		}

		return findBySql(SQL_GET_MANAGED_COMPETITOR_URLS_BY_DOMAIN, domainId, competitorId, IConstants.COMPETITORURL_ADD_BY_USER);
	}

	public List<CompetitorUrlEntity> getUrlNeedCompareByDom(int domainId) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from t_competitor_url t");
		sql.append(" where t.owndomain_id=? and t.type=1 ");

		return findBySql(sql.toString(), domainId);
	}

	public List<Integer> getCompetitorIdsWithManagedUrls(int domainId) {

		List<Integer> response = null;

		if (SQL_GET_COMPETITORS_WITH_MANAGED_URLS == null) {
			StringBuffer sql = new StringBuffer();
			sql.append("select ");
			sql.append("    distinct(t_competitor_url.competitor_id) as competitorId ");
			sql.append("from ");
			sql.append("    t_competitor_url t_competitor_url ");
			sql.append("where ");
			sql.append("    t_competitor_url.owndomain_id= ? ");
			sql.append("and t_competitor_url.add_by= ? ");
			SQL_GET_COMPETITORS_WITH_MANAGED_URLS = sql.toString();
		}
		List<CompetitorUrlEntity> competitorUrlEntityList = findBySql(SQL_GET_COMPETITORS_WITH_MANAGED_URLS, domainId, IConstants.COMPETITORURL_ADD_BY_USER);
		if (competitorUrlEntityList != null && !competitorUrlEntityList.isEmpty()) {
			response = new ArrayList<Integer>();
			for (CompetitorUrlEntity competitorUrlEntity : competitorUrlEntityList) {
				response.add(competitorUrlEntity.getCompetitorId());
			}
		}

		return response;

	}

	public CompetitorUrlEntity getById(int id) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * ");
		sql.append(" from ").append(getTableName());
		sql.append(" where id = ? ");
		sql.append(" limit 1 ");
		return this.findObject(sql.toString(), id);
	}

	public List<CompetitorUrlEntity> getAllUniversalCompetitorUrls(int ownDomainId) {
		StringBuffer pageQuery = new StringBuffer();
		pageQuery.append(" select tcu.*  ");
		pageQuery.append(" from t_competitor_url tcu ");
		pageQuery.append(" left join grouptag_competitor_rel gcr ");
		pageQuery.append(" on tcu.competitor_id = gcr.competitor_id ");
		pageQuery.append(" where gcr.own_domain_id=? and gcr.grouptag_id=0  ");

		return this.findBySql(pageQuery.toString(), ownDomainId);
	}

	public List<CompetitorUrlEntity> getByRecordId(int domainId, int id, int limit) {

		if (SQL_GET_BY_RECORD_ID == null) {
			StringBuffer stringBuffer = new StringBuffer();
			stringBuffer.append("select ");
			stringBuffer.append("	 t_competitor_url.* ");
			stringBuffer.append("from ");
			stringBuffer.append("	 t_competitor_url t_competitor_url ");
			stringBuffer.append("where ");
			stringBuffer.append("    t_competitor_url.owndomain_id = ? ");
			stringBuffer.append("and t_competitor_url.id > ? ");
			stringBuffer.append("and t_competitor_url.url_hash is null ");
			stringBuffer.append("order by ");
			stringBuffer.append("    t_competitor_url.id ");
			stringBuffer.append("limit " + limit);
			SQL_GET_BY_RECORD_ID = stringBuffer.toString();
		}
		String sql = SQL_GET_BY_RECORD_ID;
		List<CompetitorUrlEntity> response = this.findBySql(sql, domainId, id);
		return response;
	}

	public void updateUrlHash(List<CompetitorUrlEntity> competitorUrlEntityList) {

		if (SQL_UPDATE_URL_HASH == null) {
			StringBuffer stringBuffer = new StringBuffer();
			stringBuffer.append(" update t_competitor_url ");
			stringBuffer.append(" set ");
			stringBuffer.append("    url_hash = ? ");
			stringBuffer.append(" where ");
			stringBuffer.append("    id = ? ");
			SQL_UPDATE_URL_HASH = stringBuffer.toString();
		}

		Object[] values = null;
		List<Object[]> batch = new ArrayList<Object[]>();
		for (CompetitorUrlEntity competitorUrlEntity : competitorUrlEntityList) {
			values = new Object[] { competitorUrlEntity.getUrlHash(), competitorUrlEntity.getId() };
			batch.add(values);
		}
		this.executeBatch(SQL_UPDATE_URL_HASH, batch);
	}

	public List<CompetitorUrlEntity> getUniqueManagedUrlByCompetitorDomain(List<String> competitorDomainList, Set<String> languageCodeSet, String execDomainIds,
			String notExecDomainIds) {
		String queryParameterCompetitorDomains = FormatUtils.getInstance().getQueryParamString(competitorDomainList);
		String queryParameterLanguageCodes = FormatUtils.getInstance().getQueryParamString(languageCodeSet);
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("select distinct ");
		stringBuilder.append("    t_competitor_url.url as url, ");
		stringBuilder.append("    t_own_domain.`language` as urlHash ");
		stringBuilder.append("from ");
		stringBuilder.append("    t_competitor t_competitor, ");
		stringBuilder.append("    t_competitor_url t_competitor_url, ");
		stringBuilder.append("    t_own_domain t_own_domain ");
		stringBuilder.append("where ");
		stringBuilder.append("    t_competitor.domain in (" + queryParameterCompetitorDomains + ") ");
		stringBuilder.append("and t_competitor.id = t_competitor_url.competitor_id ");
		stringBuilder.append("and t_competitor_url.add_by = ? ");
		stringBuilder.append("and t_own_domain.id = t_competitor.owndomain_id ");
		stringBuilder.append("and t_own_domain.`language` in ( " + queryParameterLanguageCodes + " )");
		if (StringUtils.isNotBlank(execDomainIds)) {
			stringBuilder.append("and t_own_domain.id in ( " + execDomainIds + " )");
		} else if (StringUtils.isNotBlank(notExecDomainIds)) {
			stringBuilder.append("and t_own_domain.id not in ( " + execDomainIds + " )");
		}
		String sqlString = stringBuilder.toString();
		return findBySql(sqlString, IConstants.COMPETITORURL_ADD_BY_USER);
	}

	public List<CompetitorUrlEntity> getUniqueAssociatedCompetitorUrls(int domainId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("select distinct t_competitor_url.url as url ");
		stringBuilder.append("from ");
		stringBuilder.append("    t_keyword t_keyword, ");
		stringBuilder.append("	  t_keyword_competitorurl t_keyword_competitorurl, ");
		stringBuilder.append("	  t_competitor_url t_competitor_url ");
		stringBuilder.append("where ");
		stringBuilder.append("    t_keyword.own_domain_id = ? ");
		stringBuilder.append("and t_keyword.rank_check = ? ");
		stringBuilder.append("and t_keyword.id = t_keyword_competitorurl.keyword_id ");
		stringBuilder.append("and t_keyword_competitorurl.competitorurl_id = t_competitor_url.id ");
		String sqlString = stringBuilder.toString();
		return findBySql(sqlString, domainId, KeywordEntity.RANK_CHECK_ACTIVE);
	}

	public List<CompetitorUrlEntity> getUniqueAssociatedCompetitorUrlIds(int domainId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("select distinct ");
		stringBuilder.append("    t_competitor_url.id,");
		stringBuilder.append("    t_competitor_url.url ");
		stringBuilder.append("from ");
		stringBuilder.append("    t_keyword t_keyword, ");
		stringBuilder.append("	  t_keyword_competitorurl t_keyword_competitorurl, ");
		stringBuilder.append("	  t_competitor_url t_competitor_url ");
		stringBuilder.append("where ");
		stringBuilder.append("    t_keyword.own_domain_id = ? ");
		stringBuilder.append("and t_keyword.rank_check = ? ");
		stringBuilder.append("and t_keyword_competitorurl.keyword_id = t_keyword.id ");
		stringBuilder.append("and t_keyword_competitorurl.competitorurl_id = t_competitor_url.id ");
		String sqlString = stringBuilder.toString();
		return findBySql(sqlString, domainId, KeywordEntity.RANK_CHECK_ACTIVE);
	}

	public List<String> getUrlList() {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("select distinct ");
		stringBuilder.append("    t_competitor_url.url ");
		stringBuilder.append("from ");
		stringBuilder.append("    t_own_domain t_own_domain, ");
		stringBuilder.append("	  t_competitor t_competitor, ");
		stringBuilder.append("	  t_competitor_url t_competitor_url ");
		stringBuilder.append("where ");
		stringBuilder.append("    t_own_domain.status = ? ");
		stringBuilder.append("and t_own_domain.id = t_competitor.owndomain_id ");
		stringBuilder.append("and t_competitor.id = t_competitor_url.competitor_id ");
		stringBuilder.append("and t_competitor_url.add_by = ? ");
		return queryForStringList(stringBuilder.toString(), OwnDomainEntity.STATE_ACTIVE, IConstants.COMPETITORURL_ADD_BY_USER);
	}

	public List<CompetitorUrlEntity> getNonAssociatedUrlListByAddedBy(int domainId, int addedBy) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("select distinct ");
		stringBuilder.append("    t_competitor_url_outer.url as url ");
		stringBuilder.append("from ");
		stringBuilder.append("    t_competitor t_competitor, ");
		stringBuilder.append("	  t_competitor_url t_competitor_url_outer ");
		stringBuilder.append("where ");
		stringBuilder.append("    t_competitor.owndomain_id = ? ");
		stringBuilder.append("and t_competitor.id = t_competitor_url_outer.competitor_id ");
		stringBuilder.append("and t_competitor_url_outer.add_by = ? ");
		stringBuilder.append("and t_competitor_url_outer.id not in ");
		stringBuilder.append("( ");
		stringBuilder.append("	select ");
		stringBuilder.append("		t_competitor_url_inner.id ");
		stringBuilder.append("	from ");
		stringBuilder.append("	    t_keyword t_keyword, ");
		stringBuilder.append("		t_keyword_competitorurl t_keyword_competitorurl, ");
		stringBuilder.append("		t_competitor_url t_competitor_url_inner ");
		stringBuilder.append("	where ");
		stringBuilder.append("	    t_keyword.own_domain_id = ? ");
		stringBuilder.append("	and t_keyword.rank_check = ? ");
		stringBuilder.append("	and t_keyword.id = t_keyword_competitorurl.keyword_id ");
		stringBuilder.append("	and t_keyword_competitorurl.competitorurl_id = t_competitor_url_inner.id ");
		stringBuilder.append(") ");
		return findBySql(stringBuilder.toString(), domainId, addedBy, domainId, addedBy);
	}

	public List<CompetitorUrlEntity> getCompetitorUrls(int domainId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("select ");
		stringBuilder.append("    t_competitor_url.id,");
		stringBuilder.append("    t_competitor_url.url ");
		stringBuilder.append("from ");
		stringBuilder.append("	  t_competitor_url t_competitor_url ");
		stringBuilder.append("where ");
		stringBuilder.append("    t_competitor_url.owndomain_id = ? ");
		stringBuilder.append("and t_competitor_url.add_by = ? ");
		String sqlString = stringBuilder.toString();
		return findBySql(sqlString, domainId, IConstants.COMPETITORURL_ADD_BY_USER);
	}

	public Integer getCompetitorId(int competitorUrlId) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select ul.competitor_id  ");
		sql.append(" from t_competitor_url ul where ");
		sql.append(" ul.id = ? ");

		return queryForInteger(sql.toString(), competitorUrlId);
	}

	public CompetitorUrlEntity getCompetitorUrl(int ownDomainId, int competitorId, String url) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from t_competitor_url ttl ");
		sql.append(" where ttl.owndomain_id=? and competitor_id = ?");
		sql.append(" and (ttl.url=? or ttl.url=?) ");

		List<CompetitorUrlEntity> urls = findBySql(sql.toString(), ownDomainId, competitorId, url, url + "/");
		if (urls == null || urls.size() == 0) {
			return null;
		} else if (urls.size() == 1) {
			return urls.get(0);
		} else {
			boolean returned = false;
			for (CompetitorUrlEntity urlEntity : urls) {
				if (urlEntity.getAddBy() != null && (urlEntity.getAddBy().intValue() == 1 || urlEntity.getAddBy().intValue() == 10)) {
					returned = true;
					return urlEntity;
				}
			}
			if (!returned) {
				return urls.get(0);
			}
			return null;
		}
	}

	public List<CompetitorUrlEntity> getByKeywordGroupTag(int domainId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("select ");
		stringBuilder.append("    t_own_domain.id as ownDomainId, ");
		stringBuilder.append("    t_own_domain.domain as domainName, ");
		stringBuilder.append("    t_group_tag.tag_name as tagName, ");
		stringBuilder.append("    t_keyword.id as keywordId, ");
		stringBuilder.append("    t_keyword.keyword_name as keywordName, ");
		stringBuilder.append("    t_competitor_url.url as url ");
		stringBuilder.append("from ");
		stringBuilder.append("    t_own_domain t_own_domain, ");
		stringBuilder.append("    t_group_tag t_group_tag, ");
		stringBuilder.append("    t_group_tag_relation t_group_tag_relation, ");
		stringBuilder.append("    t_keyword t_keyword, ");
		stringBuilder.append("    t_keyword_competitorurl t_keyword_competitorurl, ");
		stringBuilder.append("    t_competitor_url t_competitor_url ");
		stringBuilder.append("where ");
		stringBuilder.append("    t_own_domain.id = ? ");
		stringBuilder.append("and t_own_domain.id = t_group_tag.domain_id ");
		stringBuilder.append("and (t_group_tag.tag_name = 'Chennai' or t_group_tag.tag_name = 'Bangalore') ");
		stringBuilder.append("and t_group_tag.id = t_group_tag_relation.group_tag_id ");
		stringBuilder.append("and t_group_tag_relation.resource_type = 2 ");
		stringBuilder.append("and t_group_tag_relation.resource_id = t_keyword.id ");
		stringBuilder.append("and t_keyword.id = t_keyword_competitorurl.keyword_id ");
		stringBuilder.append("and t_keyword_competitorurl.competitorurl_id = t_competitor_url.id ");
		stringBuilder.append("order by ");
		stringBuilder.append("    t_own_domain.id, ");
		stringBuilder.append("    t_group_tag.tag_name ");
		String sqlString = stringBuilder.toString();
		return findBySql(sqlString, domainId);
	}

	public int calcTotal() {
		String sql = "select count(*) from t_competitor_url";
		return this.queryForInteger(sql);
	}

	public int calcMaxId() {
		String sql = "select max(id) from t_competitor_url";
		return this.queryForInteger(sql);
	}

	public int[] updateToAddedByUser(List<Integer> competitorUrlIdToBeUpdatedList) {
		StringBuffer stringBuffer = new StringBuffer();
		stringBuffer.append("update t_competitor_url ");
		stringBuffer.append("set ");
		stringBuffer.append(" add_by = ? ");
		stringBuffer.append("where ");
		stringBuffer.append(" id = ? ");
		String sqlString = stringBuffer.toString();
		Object[] values = null;
		List<Object[]> batch = new ArrayList<Object[]>();
		for (Integer competitorUrlId : competitorUrlIdToBeUpdatedList) {
			values = new Object[] { CompetitorUrlEntity.ADD_BY_USER, competitorUrlId };
			batch.add(values);
		}
		return this.executeBatch(sqlString, batch);
	}

	public int calcTotalByCompetitorDomainId(int competitorId) {
		String sql = "select count(*) from t_competitor_url where competitor_id = ?";
		return this.queryForInteger(sql, competitorId);
	}

	public List<String> getDistinctUrlList(int domainId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select distinct");
		stringBuilder.append("     t_competitor_url.url");
		stringBuilder.append(" from ");
		stringBuilder.append("	   t_competitor t_competitor,");
		stringBuilder.append("	   t_competitor_url t_competitor_url");
		stringBuilder.append(" where");
		stringBuilder.append("     t_competitor.owndomain_id = ?");
		stringBuilder.append(" and t_competitor.id = t_competitor_url.competitor_id ");
		return queryForStringList(stringBuilder.toString(), domainId);
	}

	public Set<Integer> getIdSet(int domainId) {
		Set<Integer> integerSet = new HashSet<Integer>();
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     t_competitor_url.id");
		stringBuilder.append(" from ");
		stringBuilder.append("	   t_competitor_url t_competitor_url");
		stringBuilder.append(" where");
		stringBuilder.append("     t_competitor_url.owndomain_id = ?");
		stringBuilder.append(" and t_competitor_url.add_by = ?");
		String sqlString = stringBuilder.toString();
		List<Integer> integerList = queryForIntegerList(sqlString, domainId, CompetitorUrlEntity.ADD_BY_USER);
		if (integerList != null && integerList.size() > 0) {
			for (Integer integer : integerList) {
				integerSet.add(integer);
			}
		}
		return integerSet;
	}

}
