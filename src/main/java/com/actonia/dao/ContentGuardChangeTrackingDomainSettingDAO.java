package com.actonia.dao;

import java.util.List;

import com.actonia.entity.ContentGuardChangeTrackingDomainSettingEntity;

public class ContentGuardChangeTrackingDomainSettingDAO extends BaseJdbcSupport<ContentGuardChangeTrackingDomainSettingEntity> {

	@Override
	public String getTableName() {
		return "content_guard_change_tracking_domain_setting";
	}

	public List<ContentGuardChangeTrackingDomainSettingEntity> getList(int domainId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     indicator");
		stringBuilder.append(" from");
		stringBuilder.append("     " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     ownDomainId = ?");
		String sqlString = stringBuilder.toString();
		return this.findBySql(sqlString, domainId);
	}
}