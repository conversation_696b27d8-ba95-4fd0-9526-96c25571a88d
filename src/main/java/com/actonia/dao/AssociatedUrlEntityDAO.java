package com.actonia.dao;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import com.actonia.IConstants;
import com.actonia.entity.AssociatedUrlEntity;
import com.actonia.entity.AssociatedUrlEntityComparator;

public class AssociatedUrlEntityDAO extends BaseJdbcSupport<AssociatedUrlEntity> {

	private static final int NUMBER_OF_FIELDS = 6;

	private boolean isCacheMemory = true; //debug

	// map key = reversed root domain name
	// map value = map of hashcode to AssociatedUrlEntity	
	private static ConcurrentHashMap<String, ConcurrentHashMap<String, AssociatedUrlEntity>> hostnameHashCodeAssociatedUrlEntityMapMap = new ConcurrentHashMap<String, ConcurrentHashMap<String, AssociatedUrlEntity>>();

	@Override
	public String getTableName() {
		return "temp_associated_urls_html";
	}

	public void resetTable() {

		if (isCacheMemory == true) {
			return;
		}

		String sqlString = null;
		sqlString = "drop table if exists " + getTableName();
		this.executeUpdate(sqlString);
		sqlString = "create table if not exists " + getTableName() + " like template_associated_urls;";
		this.executeUpdate(sqlString);
	}

	public void insertMultiRowsBatch(List<AssociatedUrlEntity> inputList) {

		if (isCacheMemory == true) {
			insertToCache(inputList);
			return;
		}

		Object[] objectArray = null;
		List<Object[]> objectArrayList = new ArrayList<Object[]>();
		List<AssociatedUrlEntity> tempList = new ArrayList<AssociatedUrlEntity>();
		AssociatedUrlEntity associatedUrlEntityExisting = null;

		nextAssociatedUrlEntity: for (AssociatedUrlEntity associatedUrlEntity : inputList) {
			associatedUrlEntityExisting = get(associatedUrlEntity.getHostname(), associatedUrlEntity.getHashCode());
			if (associatedUrlEntityExisting != null) {
				continue nextAssociatedUrlEntity;
			}

			tempList.add(associatedUrlEntity);
			if (tempList.size() == IConstants.RECORDS_PER_SQL_STATEMENT) {
				objectArray = getObjectArrayForInsert(tempList);
				objectArrayList.add(objectArray);
				if (objectArrayList.size() == IConstants.SQL_STATEMENTS_PER_JDBC_CALL) {
					//					long startTimestamp = System.currentTimeMillis();
					this.executeBatch(getInsertMultiRowsSqlStatement(IConstants.RECORDS_PER_SQL_STATEMENT), objectArrayList);
					//					FormatUtils.getInstance().logMemoryUsage("insertMultiRowsBatch() associatedUrlEntity a, elapsed time in sec. per " + inputList.size() + " records="
					//							+ (System.currentTimeMillis() - startTimestamp) / 1000);
					objectArrayList = new ArrayList<Object[]>();
				}
				tempList = new ArrayList<AssociatedUrlEntity>();
			}
		}

		if (objectArrayList != null && objectArrayList.size() > 0) {
			//			long startTimestamp = System.currentTimeMillis();
			this.executeBatch(getInsertMultiRowsSqlStatement(IConstants.RECORDS_PER_SQL_STATEMENT), objectArrayList);
			//			FormatUtils.getInstance().logMemoryUsage("insertMultiRowsBatch() associatedUrlEntity b, elapsed time in sec. per " + objectArrayList.size() + " records="
			//					+ (System.currentTimeMillis() - startTimestamp) / 1000);
			objectArrayList = new ArrayList<Object[]>();
		}

		if (tempList != null && tempList.size() > 0) {
			objectArray = getObjectArrayForInsert(tempList);
			objectArrayList.add(objectArray);
			if (objectArrayList != null && objectArrayList.size() > 0) {
				//				long startTimestamp = System.currentTimeMillis();
				this.executeBatch(getInsertMultiRowsSqlStatement(tempList.size()), objectArrayList);
				//				FormatUtils.getInstance().logMemoryUsage("insertMultiRowsBatch() associatedUrlEntity c, elapsed time in sec. per " + tempList.size() + " records="
				//						+ (System.currentTimeMillis() - startTimestamp) / 1000);
				objectArrayList = new ArrayList<Object[]>();
			}
		}
	}

	private Object[] getObjectArrayForInsert(List<AssociatedUrlEntity> list) {
		List<Object[]> tempObjectArrayList = new ArrayList<Object[]>();
		Object[] associatedUrlObjectArray = null;
		int totalNumberOfObjects = 0;
		for (AssociatedUrlEntity associatedUrlEntity : list) {
			associatedUrlObjectArray = new Object[] { associatedUrlEntity.getHashCode(), associatedUrlEntity.getHostname(), associatedUrlEntity.getUrl(),
					associatedUrlEntity.getTrackDate(), associatedUrlEntity.getLanguageCode(), associatedUrlEntity.getProtocol(), };
			tempObjectArrayList.add(associatedUrlObjectArray);
		}

		totalNumberOfObjects = tempObjectArrayList.size() * NUMBER_OF_FIELDS;

		Object[] objectArray = new Object[totalNumberOfObjects];
		int idx = 0;
		for (Object[] tempObjectArray : tempObjectArrayList) {
			for (int i = 0; i < tempObjectArray.length; i++) {
				objectArray[idx++] = tempObjectArray[i];
			}
		}
		return objectArray;
	}

	private String getInsertMultiRowsSqlStatement(int recordsPerInsertStatement) {
		String response = null;
		String sqlString = null;
		StringBuffer stringBuffer = new StringBuffer();
		stringBuffer.append("insert into " + getTableName() + " ");
		stringBuffer.append("(");
		stringBuffer.append("	hash_code, ");
		stringBuffer.append("	hostname, ");
		stringBuffer.append("	url, ");
		stringBuffer.append("	track_date, ");
		stringBuffer.append("	language_code, ");
		stringBuffer.append("	protocol ");
		stringBuffer.append(")");
		stringBuffer.append("values ");
		sqlString = stringBuffer.toString();

		StringBuffer sql = new StringBuffer();
		sql.append(sqlString);

		for (int i = 0; i < recordsPerInsertStatement; i++) {
			if (i == 0) {
				sql.append("(?,?,?,?,?,?)");
			} else {
				sql.append(",(?,?,?,?,?,?)");
			}
		}
		sql.append(IConstants.SEMI_COLON);
		response = sql.toString();
		return response;
	}

	public AssociatedUrlEntity get(String hostname, String hashCode) {

		if (isCacheMemory == true) {
			return getFromCache(hostname, hashCode);
		}

		StringBuffer stringBuffer = new StringBuffer();
		stringBuffer.append("select ");
		stringBuffer.append(" * ");
		stringBuffer.append("from ");
		stringBuffer.append(" " + getTableName() + " ");
		stringBuffer.append("where ");
		stringBuffer.append("   hash_code = ? ");
		String sqlString = stringBuffer.toString();
		return findObject(sqlString, hashCode);
	}

	public List<AssociatedUrlEntity> getTotalUrlsByHostname(int trackDateNumber) {

		if (isCacheMemory == true) {
			return getTotalUrlsByHostnameFromCache();
		}

		StringBuffer stringBuffer = new StringBuffer();
		stringBuffer.append("select ");
		stringBuffer.append("   temp_associated_urls.hostname as hostname, ");
		stringBuffer.append("   count(*) as totalUrls ");
		stringBuffer.append("from ");
		stringBuffer.append(" " + getTableName() + " temp_associated_urls ");
		stringBuffer.append("where ");
		stringBuffer.append("   temp_associated_urls.track_date = ? ");
		stringBuffer.append("group by ");
		stringBuffer.append("   temp_associated_urls.hostname ");
		stringBuffer.append("order by ");
		stringBuffer.append("   totalUrls desc ");
		String sqlString = stringBuffer.toString();
		return findBySql(sqlString, trackDateNumber);
	}

	public List<AssociatedUrlEntity> getUrlsByHostname(String hostname, int trackDateNumber) {

		if (isCacheMemory == true) {
			return getUrlsByHostnameFromCache(hostname);
		}

		StringBuffer stringBuffer = new StringBuffer();
		stringBuffer.append("select ");
		stringBuffer.append("   temp_associated_urls.hostname as hostname, ");
		stringBuffer.append("   temp_associated_urls.url as url, ");
		stringBuffer.append("   temp_associated_urls.language_code as languageCode ");
		stringBuffer.append("from ");
		stringBuffer.append(" " + getTableName() + " temp_associated_urls ");
		stringBuffer.append("where ");
		stringBuffer.append("    temp_associated_urls.hostname = ? ");
		stringBuffer.append("and temp_associated_urls.track_date = ? ");
		String sqlString = stringBuffer.toString();
		return findBySql(sqlString, hostname, trackDateNumber);
	}

	//	public List<AssociatedUrlEntity> getUrlsByHostnames(List<String> hostnameList, int trackDateNumber) {
	//		String hostnames = FormatUtils.getInstance().getQueryParamString(hostnameList);
	//		StringBuffer stringBuffer = new StringBuffer();
	//		stringBuffer.append("select ");
	//		stringBuffer.append("   temp_associated_urls.hostname as hostname, ");
	//		stringBuffer.append("   temp_associated_urls.url as url, ");
	//		stringBuffer.append("   temp_associated_urls.language_code as languageCode ");
	//		stringBuffer.append("from ");
	//		stringBuffer.append(" " + getTableName() + " temp_associated_urls ");
	//		stringBuffer.append("where ");
	//		stringBuffer.append("   temp_associated_urls.hostname in (" + hostnames + ")");
	//		stringBuffer.append("and temp_associated_urls.track_date = ? ");
	//		String sqlString = stringBuffer.toString();
	//		return findBySql(sqlString, trackDateNumber);
	//	}

	public ConcurrentMap<String, Integer> getAllHashCodes() {
		ConcurrentMap<String, Integer> concurrentMap = new ConcurrentHashMap<String, Integer>();
		StringBuffer stringBuffer = new StringBuffer();
		stringBuffer.append("select ");
		stringBuffer.append("   temp_associated_urls.hash_code as hashCode, ");
		stringBuffer.append("   temp_associated_urls.protocol ");
		stringBuffer.append("from ");
		stringBuffer.append(" " + getTableName() + " temp_associated_urls ");
		String sqlString = stringBuffer.toString();
		List<AssociatedUrlEntity> outputList = findBySql(sqlString);
		if (outputList != null && outputList.size() > 0) {
			for (AssociatedUrlEntity associatedUrlEntity : outputList) {
				if (associatedUrlEntity.getProtocol() != null) {
					concurrentMap.put(associatedUrlEntity.getHashCode(), associatedUrlEntity.getProtocol());
				} else {
					concurrentMap.put(associatedUrlEntity.getHashCode(), IConstants.UNKNOWN_PROTOCOL);
				}
			}
		}
		return concurrentMap;
	}

	// map key = hash code
	// map value = URL string
	//	public Map<String, String> getUrlsGreaterThanHashCodeWithLimit(String hashCode, int limit) {
	//		long startTimestamp = System.currentTimeMillis();
	//		Map<String, String> hashCodeUrlStringMap = new HashMap<String, String>();
	//		StringBuffer stringBuffer = new StringBuffer();
	//		stringBuffer.append(" select");
	//		stringBuffer.append("   temp_associated_urls.hash_code as hashCode,");
	//		stringBuffer.append("   temp_associated_urls.url");
	//		stringBuffer.append(" from");
	//		stringBuffer.append(" " + getTableName() + " temp_associated_urls");
	//		stringBuffer.append(" where");
	//		stringBuffer.append("   temp_associated_urls.hash_code > ?");
	//		stringBuffer.append(" limit");
	//		stringBuffer.append(" " + limit);
	//		String sqlString = stringBuffer.toString();
	//		List<AssociatedUrlEntity> outputList = findBySql(sqlString, hashCode);
	//		if (outputList != null && outputList.size() > 0) {
	//			for (AssociatedUrlEntity associatedUrlEntity : outputList) {
	//				hashCodeUrlStringMap.put(associatedUrlEntity.getHashCode(), associatedUrlEntity.getUrl());
	//			}
	//		}
	//		FormatUtils.getInstance().logMemoryUsage("getUrlsGreaterThanHashCodeWithLimit() hashCode=" + hashCode + ",limit=" + limit + ",hashCodeUrlStringMap.size()="
	//				+ hashCodeUrlStringMap.size() + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
	//		return hashCodeUrlStringMap;
	//	}

	//	public List<AssociatedUrlEntity> getUrlLanguageGreaterThanHashCodeWithLimit(String hashCode, int limit) {
	//		StringBuffer stringBuffer = new StringBuffer();
	//		stringBuffer.append(" select");
	//		stringBuffer.append("   temp_associated_urls.hash_code as hashCode,");
	//		stringBuffer.append("   temp_associated_urls.url,");
	//		stringBuffer.append("   temp_associated_urls.language_code");
	//		stringBuffer.append(" from");
	//		stringBuffer.append(" " + getTableName() + " temp_associated_urls");
	//		stringBuffer.append(" where");
	//		stringBuffer.append("   temp_associated_urls.hash_code > ?");
	//		stringBuffer.append(" limit");
	//		stringBuffer.append(" " + limit);
	//		String sqlString = stringBuffer.toString();
	//		return findBySql(sqlString, hashCode);
	//	}

	public int updateUrl(String hostname, String hashCode, String urlString, Integer protocol, int trackDate) {

		if (isCacheMemory == true) {
			return updateUrlInCache(hostname, hashCode, urlString, protocol, trackDate);
		}

		StringBuffer stringBuffer = new StringBuffer();
		stringBuffer.append("update " + getTableName() + " ");
		stringBuffer.append("set ");
		stringBuffer.append("   url = ?, ");
		stringBuffer.append("   protocol = ?, ");
		stringBuffer.append("   track_date = ? ");
		stringBuffer.append("where ");
		stringBuffer.append("   hash_code = ? ");
		String sqlString = stringBuffer.toString();
		//FormatUtils
		//		.logMemoryUsage("updateUrl() hashCode=" + hashCode + ",urlString=" + urlString + ",protocol=" + protocol + ",trackDate=" + trackDate);
		return this.executeUpdate(sqlString, urlString, protocol, trackDate, hashCode);
	}

	private void insertToCache(List<AssociatedUrlEntity> inputList) {
		String hostname = null;
		String hashCode = null;
		ConcurrentHashMap<String, AssociatedUrlEntity> hashCodeAssociatedUrlEntityMap = null;
		AssociatedUrlEntity associatedUrlEntityToBeCreated = null;
		for (AssociatedUrlEntity associatedUrlEntity : inputList) {
			hostname = associatedUrlEntity.getHostname();
			if (hostnameHashCodeAssociatedUrlEntityMapMap.containsKey(hostname) == true) {
				hashCodeAssociatedUrlEntityMap = hostnameHashCodeAssociatedUrlEntityMapMap.get(hostname);
			} else {
				hashCodeAssociatedUrlEntityMap = new ConcurrentHashMap<String, AssociatedUrlEntity>();
			}
			hashCode = associatedUrlEntity.getHashCode();
			if (hashCodeAssociatedUrlEntityMap.containsKey(hashCode) == false) {
				associatedUrlEntityToBeCreated = new AssociatedUrlEntity();

				// url
				associatedUrlEntityToBeCreated.setUrl(associatedUrlEntity.getUrl());

				// language_code
				associatedUrlEntityToBeCreated.setLanguageCode(associatedUrlEntity.getLanguageCode());

				// protocol
				associatedUrlEntityToBeCreated.setProtocol(associatedUrlEntity.getProtocol());

				// track_date
				associatedUrlEntityToBeCreated.setTrackDate(associatedUrlEntity.getTrackDate());

				//if (isDebug == true) {
				//	FormatUtils.getInstance().logMemoryUsage("getUrlsByHostnameFromCache() associatedUrlEntityToBeCreated=" + associatedUrlEntityToBeCreated.toString());						
				//}

				// hash_code
				hashCodeAssociatedUrlEntityMap.put(hashCode, associatedUrlEntityToBeCreated);

				// hostname
				hostnameHashCodeAssociatedUrlEntityMapMap.put(hostname, hashCodeAssociatedUrlEntityMap);
			}
		}
		//if (isDebug == true) {
		//	for (AssociatedUrlEntity associatedUrlEntity : inputList) {
		//		FormatUtils.getInstance().logMemoryUsage("insertToCache() associatedUrlEntity=" + associatedUrlEntity.toString());
		//	}
		//}
	}

	private AssociatedUrlEntity getFromCache(String hostname, String hashCode) {
		AssociatedUrlEntity associatedUrlEntity = null;
		if (hostnameHashCodeAssociatedUrlEntityMapMap.containsKey(hostname) == true) {
			if (hostnameHashCodeAssociatedUrlEntityMapMap.get(hostname).containsKey(hashCode) == true) {
				associatedUrlEntity = hostnameHashCodeAssociatedUrlEntityMapMap.get(hostname).get(hashCode);
			}
		}
		//if (isDebug == true) {
		//	if (associatedUrlEntity != null) {
		//		FormatUtils.getInstance()
		//				.logMemoryUsage("getFromCache() hostname=" + hostname + ",hashCode=" + hashCode + ",associatedUrlEntity=" + associatedUrlEntity.toString());
		//	} else {
		//		FormatUtils.getInstance().logMemoryUsage("getFromCache() hostname=" + hostname + ",hashCode=" + hashCode + ",associatedUrlEntity is null.");
		//	}
		//}
		return associatedUrlEntity;
	}

	// retrieve:
	// hostname
	// totalUrls
	private List<AssociatedUrlEntity> getTotalUrlsByHostnameFromCache() {
		List<AssociatedUrlEntity> associatedUrlEntityList = new ArrayList<AssociatedUrlEntity>();
		AssociatedUrlEntity associatedUrlEntity = null;
		for (String hostname : hostnameHashCodeAssociatedUrlEntityMapMap.keySet()) {
			associatedUrlEntity = new AssociatedUrlEntity();
			associatedUrlEntity.setHostname(hostname);
			associatedUrlEntity.setTotalUrls(hostnameHashCodeAssociatedUrlEntityMapMap.get(hostname).size());
			associatedUrlEntityList.add(associatedUrlEntity);
		}
		Collections.sort(associatedUrlEntityList, new AssociatedUrlEntityComparator());
		//if (isDebug == true) {
		//	for (AssociatedUrlEntity testAssociatedUrlEntity : associatedUrlEntityList) {
		//		FormatUtils.getInstance().logMemoryUsage("getTotalUrlsByHostnameFromCache() associatedUrlEntity=" + testAssociatedUrlEntity.toString());
		//	}
		//}
		return associatedUrlEntityList;
	}

	// retrieve:
	// hostname
	// url
	// domain_id_language_code_json
	private List<AssociatedUrlEntity> getUrlsByHostnameFromCache(String hostname) {
		List<AssociatedUrlEntity> associatedUrlEntityList = new ArrayList<AssociatedUrlEntity>();
		AssociatedUrlEntity testAssociatedUrlEntity = null;
		AssociatedUrlEntity associatedUrlEntity = null;
		Map<String, AssociatedUrlEntity> hashCodeAssociatedUrlEntityMap = null;
		if (hostnameHashCodeAssociatedUrlEntityMapMap.containsKey(hostname) == true) {
			hashCodeAssociatedUrlEntityMap = hostnameHashCodeAssociatedUrlEntityMapMap.get(hostname);
			if (hashCodeAssociatedUrlEntityMap != null && hashCodeAssociatedUrlEntityMap.size() > 0) {
				for (String hashCode : hashCodeAssociatedUrlEntityMap.keySet()) {
					associatedUrlEntity = hashCodeAssociatedUrlEntityMap.get(hashCode);
					testAssociatedUrlEntity = new AssociatedUrlEntity();
					testAssociatedUrlEntity.setUrl(associatedUrlEntity.getUrl());
					testAssociatedUrlEntity.setLanguageCode(associatedUrlEntity.getLanguageCode());
					associatedUrlEntityList.add(testAssociatedUrlEntity);
					//if (isDebug == true) {
					//	FormatUtils.getInstance().logMemoryUsage("getUrlsByHostnameFromCache() associatedUrlEntity=" + associatedUrlEntity.toString());						
					//}
				}
			}
		}
		//if (isDebug == true) {
		//	for (AssociatedUrlEntity testAssociatedUrlEntity3 : associatedUrlEntityList) {
		//		FormatUtils.getInstance().logMemoryUsage("getUrlsByHostnameFromCache() associatedUrlEntity=" + testAssociatedUrlEntity3.toString());
		//	}
		//}
		return associatedUrlEntityList;
	}

	// update:
	// url
	// protocol
	// track_date
	private int updateUrlInCache(String hostname, String hashCode, String urlString, Integer protocol, int trackDate) {
		int recordsUpdated = 0;
		AssociatedUrlEntity associatedUrlEntity = null;
		if (hostnameHashCodeAssociatedUrlEntityMapMap.containsKey(hostname) == true
				&& hostnameHashCodeAssociatedUrlEntityMapMap.get(hostname).containsKey(hashCode) == true) {
			associatedUrlEntity = hostnameHashCodeAssociatedUrlEntityMapMap.get(hostname).get(hashCode);
			if (associatedUrlEntity != null) {
				associatedUrlEntity.setUrl(urlString);
				associatedUrlEntity.setProtocol(protocol);
				associatedUrlEntity.setTrackDate(trackDate);
				hostnameHashCodeAssociatedUrlEntityMapMap.get(hostname).put(hashCode, associatedUrlEntity);
				recordsUpdated = 1;
			}
		}
		return recordsUpdated;
	}

	public void removeFromCache(String hostname) {

		if (isCacheMemory == false) {
			return;
		}

		if (hostnameHashCodeAssociatedUrlEntityMapMap.containsKey(hostname) == true) {
			hostnameHashCodeAssociatedUrlEntityMapMap.remove(hostname);
		}
	}

}