package com.actonia.dao;

import com.actonia.IConstants;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.utils.CrawlerUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.value.object.*;
import com.google.gson.Gson;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import ru.yandex.clickhouse.ClickHouseDataSource;
import ru.yandex.clickhouse.settings.ClickHouseProperties;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.*;

// Due to ClickHouse concurrency issues, use Singleton pattern, do not use Spring (ie. all methods must be 'synchronized')
public class TargetUrlHtmlClickHouseDAO {

	//private static boolean isDebug = false;
	public static final String TABLE_NAME = "dis_target_url_html";
	//public static final String TABLE_NAME = "unit_test_target_url_html";
	private static final int TOTAL_DAO_INSTANCES = 3;
	private static final Logger log = LogManager.getLogger(TargetUrlHtmlClickHouseDAO.class);
	private static int daoMapIndex = 0;

	// map key = DAO index (0  - 59)
	// map value = instance of TargetUrlHtmlClickHouseDAO
	private static Map<Integer, TargetUrlHtmlClickHouseDAO> targetUrlHtmlClickHouseDAOMap;

	private List<String> databaseHostnameList;
	private String databasePort = null;
	private String databaseName = null;
	private List<Connection> connectionList;
	private int batchCreationSize;
	private String databaseUser = null;
	private String databasePassword = null;
	private List<String> connectionUrlList = null;
	private int connectionTimeoutInMilliseconds;
	private int maximumRetryCounts;
	private int retryWaitMilliseconds;
	private int daoIndex;

	public static TargetUrlHtmlClickHouseDAO getInstance() throws Exception {
		TargetUrlHtmlClickHouseDAO targetUrlHtmlClickHouseDAO = null;
		String clickHouseDatabaseHostnames = null;
		String[] clickHouseDatabaseHostnameArray = null;
		String clickHouseDatabasePort = null;
		String clickHouseDatabaseName = null;
		String clickHouseDatabaseUser = null;
		String clickHouseDatabasePassword = null;
		int clickHouseBatchCreationSize = 0;
		int clickHouseconnectionTimeoutInMilliseconds = 0;
		int clickHouseMaximumRetryCounts = 0;
		int clickHouseRetryWaitMilliseconds = 0;
		if (targetUrlHtmlClickHouseDAOMap == null) {
			clickHouseDatabaseHostnames = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_HOSTNAMES);
			clickHouseDatabaseHostnameArray = clickHouseDatabaseHostnames.split(IConstants.COMMA);
			clickHouseDatabasePort = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_PORT);
			clickHouseDatabaseName = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_NAME);
			clickHouseDatabaseUser = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_USER);
			clickHouseDatabasePassword = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_PASSWORD);
			clickHouseBatchCreationSize = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_BATCH_CREATION_SIZE, 100);
			clickHouseconnectionTimeoutInMilliseconds = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_CONNECTION_TIMEOUT_MILLISECONDS, 88888);
			clickHouseMaximumRetryCounts = ClickHouseCrawlDatabaseConfigurations.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_MAX_RETRY_COUNTS,
					8);
			clickHouseRetryWaitMilliseconds = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_RETRY_WAIT_MILLISECONDS, 1000);
			FormatUtils.getInstance().logMemoryUsage("TargetUrlHtmlClickHouseDAO() clickHouseDatabaseHostnames=" + clickHouseDatabaseHostnames.toString()
					+ ",clickHouseDatabasePort=" + clickHouseDatabasePort + ",clickHouseDatabaseName=" + clickHouseDatabaseName + ",clickHouseBatchCreationSize="
					+ clickHouseBatchCreationSize + ",clickHouseDatabaseUser=" + clickHouseDatabaseUser + ",clickHouseDatabasePassword=" + clickHouseDatabasePassword
					+ ",clickHouseconnectionTimeoutInMilliseconds=" + clickHouseconnectionTimeoutInMilliseconds + ",clickHouseMaximumRetryCounts="
					+ clickHouseMaximumRetryCounts + ",clickHouseRetryWaitMilliseconds=" + clickHouseRetryWaitMilliseconds + ",TABLE_NAME=" + TABLE_NAME);

			targetUrlHtmlClickHouseDAOMap = new HashMap<Integer, TargetUrlHtmlClickHouseDAO>();
			for (int i = 0; i < TOTAL_DAO_INSTANCES; i++) {
				targetUrlHtmlClickHouseDAO = initialize(clickHouseDatabaseHostnameArray, clickHouseDatabasePort, clickHouseDatabaseName, clickHouseDatabaseUser,
						clickHouseDatabasePassword, clickHouseBatchCreationSize, clickHouseconnectionTimeoutInMilliseconds, clickHouseMaximumRetryCounts,
						clickHouseRetryWaitMilliseconds, i);
				targetUrlHtmlClickHouseDAOMap.put(i, targetUrlHtmlClickHouseDAO);
			}
			FormatUtils.getInstance().logMemoryUsage("getInstance() total targetUrlHtmlClickHouseDAOs=" + targetUrlHtmlClickHouseDAOMap.size());
		}
		int index = getDaoMapIndex();
		targetUrlHtmlClickHouseDAO = targetUrlHtmlClickHouseDAOMap.get(index);
		return targetUrlHtmlClickHouseDAO;
	}

	private static synchronized int getDaoMapIndex() {
		int index = 0;
		if (daoMapIndex >= TOTAL_DAO_INSTANCES) {
			daoMapIndex = 0;
			index = 0;
		} else {
			index = daoMapIndex++;
		}
		return index;
	}

	// initialize TargetUrlHtmlClickHouseDAO based on runtime clickhouse configurations
	private static TargetUrlHtmlClickHouseDAO initialize(String[] clickHouseDatabaseHostnameArray, String clickHouseDatabasePort, String clickHouseDatabaseName,
			String clickHouseDatabaseUser, String clickHouseDatabasePassword, int clickHouseBatchCreationSize, int clickHouseconnectionTimeoutInMilliseconds,
			int clickHouseMaximumRetryCounts, int clickHouseRetryWaitMilliseconds, int daoIndex) throws Exception {
		TargetUrlHtmlClickHouseDAO targetUrlHtmlClickHouseDAO = new TargetUrlHtmlClickHouseDAO(clickHouseDatabaseHostnameArray, clickHouseDatabasePort,
				clickHouseDatabaseName, clickHouseBatchCreationSize, clickHouseDatabaseUser, clickHouseDatabasePassword, clickHouseconnectionTimeoutInMilliseconds,
				clickHouseMaximumRetryCounts, clickHouseRetryWaitMilliseconds, daoIndex);
		//FormatUtils.getInstance().logMemoryUsage("initialize() targetUrlHtmlClickHouseDAO=" + targetUrlHtmlClickHouseDAO.toString());
		return targetUrlHtmlClickHouseDAO;
	}

	private TargetUrlHtmlClickHouseDAO(String[] databaseHostnameArrayInput, String databasePortInput, String databaseNameInput, int batchCreationSizeInput,
			String databaseUserInput, String databasePasswordInput, int connectionTimeoutInMillisecondsInput, int maximumRetryCountsInput,
			int retryWaitMillisecondsInput, int index) throws Exception {

		ClickHouseDataSource clickHouseDataSource = null;
		Connection connection = null;

		databaseHostnameList = Arrays.asList(databaseHostnameArrayInput);
		databasePort = databasePortInput;
		databaseName = databaseNameInput;
		batchCreationSize = batchCreationSizeInput;
		databaseUser = databaseUserInput;
		databasePassword = databasePasswordInput;
		connectionTimeoutInMilliseconds = connectionTimeoutInMillisecondsInput;
		maximumRetryCounts = maximumRetryCountsInput;
		retryWaitMilliseconds = retryWaitMillisecondsInput;
		daoIndex = index;

		ClickHouseProperties clickHouseProperties = new ClickHouseProperties();
		clickHouseProperties.setDecompress(true);
		clickHouseProperties.setConnectionTimeout(connectionTimeoutInMilliseconds);
		clickHouseProperties.setSocketTimeout(connectionTimeoutInMilliseconds);

		connectionList = new ArrayList<Connection>();

		String connectionUrl = null;

		connectionUrlList = new ArrayList<String>();

		for (String databaseHostname : databaseHostnameList) {
			if (StringUtils.isNotBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
						+ "?user=" + databaseUser + "&password=" + databasePassword;
			} else if (StringUtils.isBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
						+ "?password=" + databasePassword;
			} else {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH
						+ databaseName;
			}
			connectionUrlList.add(connectionUrl);
			//FormatUtils.getInstance().logMemoryUsage("TargetUrlHtmlClickHouseDAO() connectionUrl=" + connectionUrl);
			clickHouseDataSource = new ClickHouseDataSource(connectionUrl, clickHouseProperties);
			connection = clickHouseDataSource.getConnection();
			connectionList.add(connection);
		}
	}

	@Override
	public String toString() {
		return "TargetUrlHtmlClickHouseDAO [databaseHostnameList=" + databaseHostnameList.toString() + ", databasePort=" + databasePort + ", databaseName="
				+ databaseName + ", batchCreationSize=" + batchCreationSize + ", databaseUser=" + databaseUser + ", databasePassword=" + databasePassword
				+ ", connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ", maximumRetryCounts=" + maximumRetryCounts + ", retryWaitMilliseconds="
				+ retryWaitMilliseconds + ", daoIndex=" + daoIndex + "]";
	}

	private synchronized String getTableName(String tableName) {
		if (StringUtils.isNotBlank(tableName)) {
			return tableName;
		} else {
			return TABLE_NAME;
		}
	}

	public synchronized int getBatchCreationSize() {
		return batchCreationSize;
	}

	public synchronized void createBatch(String ip, String queueName, List<HtmlClickHouseEntity> htmlClickHouseEntityList, String tableName) throws Exception {
		//long startTimestamp = System.currentTimeMillis();

		PreparedStatement preparedStatement = null;
		int index = 0;
		Connection connection = null;

		String creationSqlStatement = getCreationSqlStatement(tableName);
		//FormatUtils.getInstance().logMemoryUsage("createBatch() creationSqlStatement=" + creationSqlStatement);

		CrawlerResponse crawlerResponse = null;

		Gson gson = new Gson();

		Date nowDate = null;

		for (int i = 0; i < connectionList.size(); i++) {
			connection = connectionList.get(i);
			preparedStatement = connection.prepareStatement(creationSqlStatement);
			nextHtmlClickHouseEntity: for (HtmlClickHouseEntity htmlClickHouseEntity : htmlClickHouseEntityList) {

				crawlerResponse = htmlClickHouseEntity.getCrawlerResponse();

				if (crawlerResponse == null) {
					continue nextHtmlClickHouseEntity;
				}

				index = 1;

				nowDate = new Date();

				// domain_id
				preparedStatement.setInt(index++, htmlClickHouseEntity.getDomainId());

				// url
				preparedStatement.setString(index++, htmlClickHouseEntity.getUrl());

				// track_date
				preparedStatement.setDate(index++, new java.sql.Date(htmlClickHouseEntity.getTrackDate().getTime()));

				// alt_img_list
				preparedStatement.setString(index++,
						crawlerResponse.getAlt_img_list() != null ? gson.toJson(crawlerResponse.getAlt_img_list(), AltImgList[].class) : null);

				// alternate_links
				preparedStatement.setString(index++,
						crawlerResponse.getAlternate_links() != null ? gson.toJson(crawlerResponse.getAlternate_links(), AlternateLinks[].class) : null);

				// amphtml_flag
				preparedStatement.setInt(index++, crawlerResponse.getAmphtml_flag() != null ? BooleanUtils.toInteger(crawlerResponse.getAmphtml_flag()) : -1);

				// amphtml_href
				preparedStatement.setString(index++, crawlerResponse.getAmphtml_href());

				// analyzed_url_flg_s
				preparedStatement.setString(index++, crawlerResponse.getAnalyzed_url_flg_s());

				// analyzed_url_s
				preparedStatement.setString(index++, crawlerResponse.getAnalyzed_url_s());

				// archive_flg
				preparedStatement.setString(index++, crawlerResponse.getArchive_flg());

				// archive_flg_x_tag
				preparedStatement.setString(index++, crawlerResponse.getArchive_flg_x_tag());

				// blocked_by_robots
				preparedStatement.setString(index++, crawlerResponse.getBlocked_by_robots());

				// canonical
				preparedStatement.setString(index++, crawlerResponse.getCanonical());

				// canonical_flg
				preparedStatement.setString(index++, crawlerResponse.getCanonical_flg());

				// canonical_header_flag
				preparedStatement.setInt(index++,
						crawlerResponse.getCanonical_header_flag() != null ? BooleanUtils.toInteger(crawlerResponse.getCanonical_header_flag()) : -1);

				// canonical_header_type
				preparedStatement.setString(index++, crawlerResponse.getCanonical_header_type());

				// canonical_type
				preparedStatement.setString(index++, crawlerResponse.getCanonical_type());

				// canonical_url_is_consistent
				preparedStatement.setString(index++, crawlerResponse.getCanonical_url_is_consistent());

				// content_type
				preparedStatement.setString(index++, crawlerResponse.getContent_type());

				// count_of_objects
				preparedStatement.setInt(index++, crawlerResponse.getCount_of_objects() != null ? crawlerResponse.getCount_of_objects() : -1);

				// custom_data
				preparedStatement.setString(index++,
						crawlerResponse.getCustom_data() != null ? gson.toJson(crawlerResponse.getCustom_data(), CustomData[].class) : null);

				// description
				preparedStatement.setString(index++, crawlerResponse.getDescription());

				// description_flg
				preparedStatement.setString(index++, crawlerResponse.getDescription_flg());

				// description_length
				preparedStatement.setInt(index++, crawlerResponse.getDescription_length() != null ? crawlerResponse.getDescription_length() : -1);

				// description_simhash
				preparedStatement.setString(index++, crawlerResponse.getDescription_simhash() != null ? crawlerResponse.getDescription_simhash().toString() : null);

				// document_size
				preparedStatement.setInt(index++, crawlerResponse.getDocument_size() != null ? crawlerResponse.getDocument_size() : -1);

				// download_latency
				preparedStatement.setString(index++, crawlerResponse.getDownload_latency() != null ? crawlerResponse.getDownload_latency().toString() : null);

				// download_time
				preparedStatement.setString(index++, crawlerResponse.getDownload_time() != null ? crawlerResponse.getDownload_time().toString() : null);

				// error_message
				preparedStatement.setString(index++, crawlerResponse.getError_message());

				// final_response_code
				preparedStatement.setInt(index++, crawlerResponse.getFinal_response_code() != null ? crawlerResponse.getFinal_response_code() : -1);

				// folder_level_1
				preparedStatement.setString(index++, crawlerResponse.getFolder_level_1());

				// folder_level_2
				preparedStatement.setString(index++, crawlerResponse.getFolder_level_2());

				// folder_level_3
				preparedStatement.setString(index++, crawlerResponse.getFolder_level_3());

				// folder_level_count
				preparedStatement.setInt(index++, crawlerResponse.getFolder_level_count() != null ? crawlerResponse.getFolder_level_count() : -1);

				// follow_flg
				preparedStatement.setString(index++, crawlerResponse.getFollow_flg());

				// follow_flg_x_tag
				preparedStatement.setString(index++, crawlerResponse.getFollow_flg_x_tag());

				// h1
				preparedStatement.setString(index++, crawlerResponse.getH1() != null ? gson.toJson(crawlerResponse.getH1(), String[].class) : null);

				// h1_count
				preparedStatement.setInt(index++, crawlerResponse.getH1_count() != null ? crawlerResponse.getH1_count() : -1);

				// h1_flg
				preparedStatement.setString(index++, crawlerResponse.getH1_flg());

				// h1_length
				preparedStatement.setInt(index++, crawlerResponse.getH1_length() != null ? crawlerResponse.getH1_length() : -1);

				// h1_md5
				preparedStatement.setString(index++, crawlerResponse.getH1_md5() != null ? gson.toJson(crawlerResponse.getH1_md5(), String[].class) : null);

				// h1_simhash
				preparedStatement.setString(index++, crawlerResponse.getH1_simhash() != null ? crawlerResponse.getH1_simhash().toString() : null);

				// h2
				preparedStatement.setString(index++, crawlerResponse.getH2() != null ? gson.toJson(crawlerResponse.getH2(), String[].class) : null);

				// h2_simhash
				preparedStatement.setString(index++, crawlerResponse.getH2_simhash() != null ? crawlerResponse.getH2_simhash().toString() : null);

				// header_noarchive
				preparedStatement.setInt(index++, crawlerResponse.getHeader_noarchive() != null ? BooleanUtils.toInteger(crawlerResponse.getHeader_noarchive()) : -1);

				// header_nofollow
				preparedStatement.setInt(index++, crawlerResponse.getHeader_nofollow() != null ? BooleanUtils.toInteger(crawlerResponse.getHeader_nofollow()) : -1);

				// header_noindex
				preparedStatement.setInt(index++, crawlerResponse.getHeader_noindex() != null ? BooleanUtils.toInteger(crawlerResponse.getHeader_noindex()) : -1);

				// header_noodp
				preparedStatement.setInt(index++, crawlerResponse.getHeader_noodp() != null ? BooleanUtils.toInteger(crawlerResponse.getHeader_noodp()) : -1);

				// header_nosnippet
				preparedStatement.setInt(index++, crawlerResponse.getHeader_nosnippet() != null ? BooleanUtils.toInteger(crawlerResponse.getHeader_nosnippet()) : -1);

				// header_noydir
				preparedStatement.setInt(index++, crawlerResponse.getHeader_noydir() != null ? BooleanUtils.toInteger(crawlerResponse.getHeader_noydir()) : -1);

				// hreflang_errors
				preparedStatement.setString(index++,
						crawlerResponse.getHreflang_errors() != null ? gson.toJson(crawlerResponse.getHreflang_errors(), HreflangErrors.class) : null);

				// hreflang_links
				preparedStatement.setString(index++,
						crawlerResponse.getHreflang_links() != null ? gson.toJson(crawlerResponse.getHreflang_links(), HreflangLinks[].class) : null);

				// hreflang_links_out_count
				preparedStatement.setInt(index++, crawlerResponse.getHreflang_links_out_count() != null ? crawlerResponse.getHreflang_links_out_count() : -1);

				// hreflang_url_count
				preparedStatement.setInt(index++, crawlerResponse.getHreflang_url_count() != null ? crawlerResponse.getHreflang_url_count() : -1);

				// index_flg
				preparedStatement.setString(index++, crawlerResponse.getIndex_flg());

				// index_flg_x_tag
				preparedStatement.setString(index++, crawlerResponse.getIndex_flg_x_tag());

				// indexable
				preparedStatement.setInt(index++, crawlerResponse.getIndexable() != null ? BooleanUtils.toInteger(crawlerResponse.getIndexable()) : -1);

				// insecure_resources
				preparedStatement.setString(index++,
						crawlerResponse.getInsecure_resources() != null ? gson.toJson(crawlerResponse.getInsecure_resources(), String[].class) : null);

				// insecure_resources_flag
				preparedStatement.setInt(index++,
						crawlerResponse.getInsecure_resources_flag() != null ? BooleanUtils.toInteger(crawlerResponse.getInsecure_resources_flag()) : -1);

				// long_redirect
				preparedStatement.setInt(index++, crawlerResponse.getLong_redirect() != null ? BooleanUtils.toInteger(crawlerResponse.getLong_redirect()) : -1);

				// meta_charset
				preparedStatement.setString(index++, crawlerResponse.getMeta_charset());

				// meta_content_type
				preparedStatement.setString(index++, crawlerResponse.getMeta_content_type());

				// meta_disabled_sitelinks
				preparedStatement.setInt(index++,
						crawlerResponse.getMeta_disabled_sitelinks() != null ? BooleanUtils.toInteger(crawlerResponse.getMeta_disabled_sitelinks()) : -1);

				// meta_noodp
				preparedStatement.setInt(index++, crawlerResponse.getMeta_noodp() != null ? BooleanUtils.toInteger(crawlerResponse.getMeta_noodp()) : -1);

				// meta_nosnippet
				preparedStatement.setInt(index++, crawlerResponse.getMeta_nosnippet() != null ? BooleanUtils.toInteger(crawlerResponse.getMeta_nosnippet()) : -1);

				// meta_noydir
				preparedStatement.setInt(index++, crawlerResponse.getMeta_noydir() != null ? BooleanUtils.toInteger(crawlerResponse.getMeta_noydir()) : -1);

				// meta_redirect
				preparedStatement.setInt(index++, crawlerResponse.getMeta_redirect() != null ? BooleanUtils.toInteger(crawlerResponse.getMeta_redirect()) : -1);

				// mixed_redirects
				preparedStatement.setInt(index++, crawlerResponse.getMixed_redirects() != null ? BooleanUtils.toInteger(crawlerResponse.getMixed_redirects()) : -1);

				// mobile_rel_alternate_url_is_consistent
				preparedStatement.setInt(index++,
						crawlerResponse.getMobile_rel_alternate_url_is_consistent() != null
								? BooleanUtils.toInteger(crawlerResponse.getMobile_rel_alternate_url_is_consistent())
								: -1);

				// noodp
				preparedStatement.setInt(index++, crawlerResponse.getNoodp() != null ? BooleanUtils.toInteger(crawlerResponse.getNoodp()) : -1);

				// nosnippet
				preparedStatement.setInt(index++, crawlerResponse.getNosnippet() != null ? BooleanUtils.toInteger(crawlerResponse.getNosnippet()) : -1);

				// noydir
				preparedStatement.setInt(index++, crawlerResponse.getNoydir() != null ? BooleanUtils.toInteger(crawlerResponse.getNoydir()) : -1);

				// og_markup
				preparedStatement.setString(index++, crawlerResponse.getOg_markup() != null ? gson.toJson(crawlerResponse.getOg_markup(), OgMarkup[].class) : null);

				// og_markup_flag
				preparedStatement.setInt(index++, crawlerResponse.getOg_markup_flag() != null ? BooleanUtils.toInteger(crawlerResponse.getOg_markup_flag()) : -1);

				// og_markup_length
				preparedStatement.setInt(index++, crawlerResponse.getOg_markup_length() != null ? crawlerResponse.getOg_markup_length() : -1);

				// outlink_count
				preparedStatement.setInt(index++, crawlerResponse.getOutlink_count() != null ? crawlerResponse.getOutlink_count() : -1);

				// page_1
				preparedStatement.setInt(index++, crawlerResponse.getPage_1() != null ? BooleanUtils.toInteger(crawlerResponse.getPage_1()) : -1);

				// page_link
				preparedStatement.setString(index++, crawlerResponse.getPage_link() != null ? gson.toJson(crawlerResponse.getPage_link(), PageLink[].class) : null);

				// page_timeout_flag
				preparedStatement.setInt(index++, crawlerResponse.getPage_timeout_flag() != null ? BooleanUtils.toInteger(crawlerResponse.getPage_timeout_flag()) : -1);

				// paginated
				preparedStatement.setInt(index++, crawlerResponse.getPaginated() != null ? BooleanUtils.toInteger(crawlerResponse.getPaginated()) : -1);

				// pagination_links
				preparedStatement.setString(index++,
						crawlerResponse.getPagination_links() != null ? gson.toJson(crawlerResponse.getPagination_links(), PaginationLinks[].class) : null);

				// protocol
				preparedStatement.setString(index++, crawlerResponse.getProtocol());

				// redirect_blocked
				preparedStatement.setInt(index++, crawlerResponse.getRedirect_blocked() != null ? BooleanUtils.toInteger(crawlerResponse.getRedirect_blocked()) : -1);

				// redirect_blocked_reason
				preparedStatement.setString(index++, crawlerResponse.getRedirect_blocked_reason());

				// redirect_chain
				preparedStatement.setString(index++,
						crawlerResponse.getRedirect_chain() != null ? gson.toJson(crawlerResponse.getRedirect_chain(), RedirectChain[].class) : null);

				// redirect_final_url
				preparedStatement.setString(index++, crawlerResponse.getRedirect_final_url());

				// redirect_flg
				preparedStatement.setInt(index++, crawlerResponse.getRedirect_flg() != null ? BooleanUtils.toInteger(crawlerResponse.getRedirect_flg()) : -1);

				// redirect_times
				preparedStatement.setInt(index++, crawlerResponse.getRedirect_times() != null ? crawlerResponse.getRedirect_times() : -1);

				// rel_next_html_url
				preparedStatement.setString(index++, crawlerResponse.getRel_next_html_url());

				// rel_next_url_is_consistent
				preparedStatement.setInt(index++,
						crawlerResponse.getRel_next_url_is_consistent() != null ? BooleanUtils.toInteger(crawlerResponse.getRel_next_url_is_consistent()) : -1);

				// rel_prev_url_is_consistent
				preparedStatement.setInt(index++,
						crawlerResponse.getRel_prev_url_is_consistent() != null ? BooleanUtils.toInteger(crawlerResponse.getRel_prev_url_is_consistent()) : -1);

				// request_headers
				preparedStatement.setString(index++, crawlerResponse.getRequest_headers() != null ? crawlerResponse.getRequest_headers().toString() : null);

				// request_time
				preparedStatement.setString(index++, crawlerResponse.getRequest_time());

				// response_code
				preparedStatement.setString(index++, crawlerResponse.getResponse_code());

				// response_headers
				preparedStatement.setString(index++,
						crawlerResponse.getResponse_headers() != null ? gson.toJson(crawlerResponse.getResponse_headers(), ResponseHeaders[].class) : null);

				// retry_attempted
				preparedStatement.setInt(index++, crawlerResponse.getRetry_attempted() != null ? BooleanUtils.toInteger(crawlerResponse.getRetry_attempted()) : -1);

				// robots
				preparedStatement.setString(index++, crawlerResponse.getRobots());

				// robots_contents
				preparedStatement.setString(index++, crawlerResponse.getRobots_contents());

				// robots_contents_x_tag
				preparedStatement.setInt(index++,
						crawlerResponse.getRobots_contents_x_tag() != null ? BooleanUtils.toInteger(crawlerResponse.getRobots_contents_x_tag()) : -1);

				// robots_flg
				preparedStatement.setString(index++, crawlerResponse.getRobots_flg());

				// robots_flg_x_tag
				preparedStatement.setString(index++, crawlerResponse.getRobots_flg_x_tag());

				// server_response_time
				preparedStatement.setString(index++, crawlerResponse.getServer_response_time() != null ? crawlerResponse.getServer_response_time().toString() : null);

				// source_url
				preparedStatement.setString(index++, crawlerResponse.getSource_url() != null ? gson.toJson(crawlerResponse.getSource_url(), String[].class) : null);

				// splash_took
				preparedStatement.setString(index++, crawlerResponse.getSplash_took() != null ? crawlerResponse.getSplash_took().toString() : null);

				// https://www.wrike.com/open.htm?id=390204287
				// structured_data
				if (CrawlerUtils.getInstance().checkIfStructuredDataAvailable(crawlerResponse.getStructured_data()) == true) {
					preparedStatement.setString(index++,
							crawlerResponse.getStructured_data() != null ? gson.toJson(crawlerResponse.getStructured_data(), StructuredData.class) : null);
				} else {
					preparedStatement.setString(index++, null);
				}

				// title
				preparedStatement.setString(index++, crawlerResponse.getTitle());

				// title_flg
				preparedStatement.setString(index++, crawlerResponse.getTitle_flg());

				// title_length
				preparedStatement.setInt(index++, crawlerResponse.getTitle_length() != null ? crawlerResponse.getTitle_length() : -1);

				// title_md5
				preparedStatement.setString(index++, crawlerResponse.getTitle_md5() != null ? gson.toJson(crawlerResponse.getTitle_md5(), String[].class) : null);

				// title_simhash
				preparedStatement.setString(index++, crawlerResponse.getTitle_simhash() != null ? crawlerResponse.getTitle_simhash().toString() : null);

				// twitter_description_length
				preparedStatement.setInt(index++, crawlerResponse.getTwitter_description_length() != null ? crawlerResponse.getTwitter_description_length() : -1);

				// twitter_markup
				preparedStatement.setString(index++,
						crawlerResponse.getTwitter_markup() != null ? gson.toJson(crawlerResponse.getTwitter_markup(), TwitterMarkup[].class) : null);

				// twitter_markup_flag
				preparedStatement.setInt(index++,
						crawlerResponse.getTwitter_markup_flag() != null ? BooleanUtils.toInteger(crawlerResponse.getTwitter_markup_flag()) : -1);

				// twitter_markup_length
				preparedStatement.setInt(index++, crawlerResponse.getTwitter_markup_length() != null ? crawlerResponse.getTwitter_markup_length() : -1);

				// url_length
				preparedStatement.setInt(index++, crawlerResponse.getUrl_length() != null ? crawlerResponse.getUrl_length() : -1);

				// valid_twitter_card
				preparedStatement.setString(index++, crawlerResponse.getValid_twitter_card());

				// viewport_content
				preparedStatement.setString(index++, crawlerResponse.getViewport_content());

				// viewport_flag
				preparedStatement.setInt(index++, crawlerResponse.getViewport_flag() != null ? BooleanUtils.toInteger(crawlerResponse.getViewport_flag()) : -1);

				// page_analysis_results
				preparedStatement.setString(index++,
						htmlClickHouseEntity.getPageAnalysisResultArray() != null && htmlClickHouseEntity.getPageAnalysisResultArray().length > 0
								? gson.toJson(htmlClickHouseEntity.getPageAnalysisResultArray(), PageAnalysisResult[].class)
								: null);

				// change_tracking_hash
				preparedStatement.setString(index++, htmlClickHouseEntity.getChangeTrackingHash());

				// week_of_year
				preparedStatement.setInt(index++, htmlClickHouseEntity.getWeekOfYear() != null ? htmlClickHouseEntity.getWeekOfYear() : -1);

				// crawl_timestamp
				preparedStatement.setTimestamp(index++, new java.sql.Timestamp(htmlClickHouseEntity.getCrawlTimestamp().getTime()));

				// url_hash (default to output of URLHash() ClickHouse function of original URL)

				// lower_case_url_hash (default to output of URLHash() ClickHouse function of the lower-cased version of the original URL)

				// url_murmur_hash (default to output of murmurHash3_64() ClickHouse function of original URL)

				// alternate_links_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getAlternateLinksChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getAlternateLinksChgInd()) : -1);

				// amphtml_flag_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getAmphtmlFlagChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getAmphtmlFlagChgInd()) : -1);

				// amphtml_href_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getAmphtmlHrefChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getAmphtmlHrefChgInd()) : -1);

				// analyzed_url_flg_s_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getAnalyzedUrlFlgSChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getAnalyzedUrlFlgSChgInd()) : -1);

				// analyzed_url_s_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getAnalyzedUrlSChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getAnalyzedUrlSChgInd()) : -1);

				// archive_flg_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getArchiveFlgChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getArchiveFlgChgInd()) : -1);

				// archive_flg_x_tag_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getArchiveFlgXTagChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getArchiveFlgXTagChgInd()) : -1);

				// blocked_by_robots_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getBlockedByRobotsChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getBlockedByRobotsChgInd()) : -1);

				// canonical_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getCanonicalChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getCanonicalChgInd()) : -1);

				// canonical_flg_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getCanonicalFlgChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getCanonicalFlgChgInd()) : -1);

				// canonical_header_flag_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getCanonicalHeaderFlagChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getCanonicalHeaderFlagChgInd()) : -1);

				// canonical_header_type_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getCanonicalHeaderTypeChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getCanonicalHeaderTypeChgInd()) : -1);

				// canonical_type_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getCanonicalTypeChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getCanonicalTypeChgInd()) : -1);

				// canonical_url_is_consistent_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getCanonicalUrlIsConsistentChgInd() != null
								? BooleanUtils.toInteger(htmlClickHouseEntity.getCanonicalUrlIsConsistentChgInd())
								: -1);

				// content_type_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getContentTypeChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getContentTypeChgInd()) : -1);

				// description_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getDescriptionChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getDescriptionChgInd()) : -1);

				// description_flg_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getDescriptionFlgChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getDescriptionFlgChgInd()) : -1);

				// description_length_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getDescriptionLengthChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getDescriptionLengthChgInd()) : -1);

				// description_simhash_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getDescriptionSimhashChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getDescriptionSimhashChgInd()) : -1);

				// error_message_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getErrorMessageChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getErrorMessageChgInd()) : -1);

				// final_response_code_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getFinalResponseCodeChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getFinalResponseCodeChgInd()) : -1);

				// follow_flg_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getFollowFlgChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getFollowFlgChgInd()) : -1);

				// follow_flg_x_tag_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getFollowFlgXTagChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getFollowFlgXTagChgInd()) : -1);

				// h1_chg_ind
				preparedStatement.setInt(index++, htmlClickHouseEntity.getH1ChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getH1ChgInd()) : -1);

				// h1_count_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getH1CountChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getH1CountChgInd()) : -1);

				// h1_flg_chg_ind
				preparedStatement.setInt(index++, htmlClickHouseEntity.getH1FlgChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getH1FlgChgInd()) : -1);

				// h1_length_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getH1LengthChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getH1LengthChgInd()) : -1);

				// h1_md5_chg_ind
				preparedStatement.setInt(index++, htmlClickHouseEntity.getH1Md5ChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getH1Md5ChgInd()) : -1);

				// header_noarchive_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getHeaderNoarchiveChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getHeaderNoarchiveChgInd()) : -1);

				// header_nofollow_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getHeaderNofollowChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getHeaderNofollowChgInd()) : -1);

				// header_noindex_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getHeaderNoindexChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getHeaderNoindexChgInd()) : -1);

				// header_noodp_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getHeaderNoodpChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getHeaderNoodpChgInd()) : -1);

				// header_nosnippet_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getHeaderNosnippetChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getHeaderNosnippetChgInd()) : -1);

				// header_noydir_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getHeaderNoydirChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getHeaderNoydirChgInd()) : -1);

				// hreflang_errors_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getHreflangErrorsChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getHreflangErrorsChgInd()) : -1);

				// hreflang_links_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getHreflangLinksChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getHreflangLinksChgInd()) : -1);

				// hreflang_links_out_count_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getHreflangLinksOutCountChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getHreflangLinksOutCountChgInd())
								: -1);

				// hreflang_url_count_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getHreflangUrlCountChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getHreflangUrlCountChgInd()) : -1);

				// index_flg_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getIndexFlgChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getIndexFlgChgInd()) : -1);

				// index_flg_x_tag_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getIndexFlgXTagChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getIndexFlgXTagChgInd()) : -1);

				// indexable_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getIndexableChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getIndexableChgInd()) : -1);

				// insecure_resources_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getInsecureResourcesChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getInsecureResourcesChgInd()) : -1);

				// insecure_resources_flag_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getInsecureResourcesFlagChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getInsecureResourcesFlagChgInd())
								: -1);

				// meta_charset_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getMetaCharsetChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getMetaCharsetChgInd()) : -1);

				// meta_content_type_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getMetaContentTypeChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getMetaContentTypeChgInd()) : -1);

				// meta_disabled_sitelinks_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getMetaDisabledSitelinksChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getMetaDisabledSitelinksChgInd())
								: -1);

				// meta_noodp_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getMetaNoodpChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getMetaNoodpChgInd()) : -1);

				// meta_nosnippet_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getMetaNosnippetChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getMetaNosnippetChgInd()) : -1);

				// meta_noydir_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getMetaNoydirChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getMetaNoydirChgInd()) : -1);

				// meta_redirect_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getMetaRedirectChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getMetaRedirectChgInd()) : -1);

				// mixed_redirects_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getMixedRedirectsChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getMixedRedirectsChgInd()) : -1);

				// mobile_rel_alternate_url_is_consistent_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getMobileRelAlternateUrlIsConsistentChgInd() != null
								? BooleanUtils.toInteger(htmlClickHouseEntity.getMobileRelAlternateUrlIsConsistentChgInd())
								: -1);

				// noodp_chg_ind
				preparedStatement.setInt(index++, htmlClickHouseEntity.getNoodpChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getNoodpChgInd()) : -1);

				// nosnippet_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getNosnippetChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getNosnippetChgInd()) : -1);

				// noydir_chg_ind
				preparedStatement.setInt(index++, htmlClickHouseEntity.getNoydirChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getNoydirChgInd()) : -1);

				// og_markup_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getOgMarkupChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getOgMarkupChgInd()) : -1);

				// og_markup_flag_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getOgMarkupFlagChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getOgMarkupFlagChgInd()) : -1);

				// og_markup_length_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getOgMarkupLengthChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getOgMarkupLengthChgInd()) : -1);

				// outlink_count_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getOutlinkCountChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getOutlinkCountChgInd()) : -1);

				// page_link_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getPageLinkChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getPageLinkChgInd()) : -1);

				// redirect_blocked_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getRedirectBlockedChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getRedirectBlockedChgInd()) : -1);

				// redirect_blocked_reason_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getRedirectBlockedReasonChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getRedirectBlockedReasonChgInd())
								: -1);

				// redirect_chain_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getRedirectChainChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getRedirectChainChgInd()) : -1);

				// redirect_final_url_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getRedirectFinalUrlChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getRedirectFinalUrlChgInd()) : -1);

				// redirect_flg_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getRedirectFlgChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getRedirectFlgChgInd()) : -1);

				// redirect_times_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getRedirectTimesChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getRedirectTimesChgInd()) : -1);

				// response_code_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getResponseCodeChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getResponseCodeChgInd()) : -1);

				// robots_chg_ind
				preparedStatement.setInt(index++, htmlClickHouseEntity.getRobotsChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getRobotsChgInd()) : -1);

				// robots_contents_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getRobotsContentsChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getRobotsContentsChgInd()) : -1);

				// robots_contents_x_tag_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getRobotsContentsXTagChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getRobotsContentsXTagChgInd()) : -1);

				// robots_flg_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getRobotsFlgChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getRobotsFlgChgInd()) : -1);

				// robots_flg_x_tag_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getRobotsFlgXTagChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getRobotsFlgXTagChgInd()) : -1);

				// structured_data_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getStructuredDataChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getStructuredDataChgInd()) : -1);

				// title_chg_ind
				preparedStatement.setInt(index++, htmlClickHouseEntity.getTitleChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getTitleChgInd()) : -1);

				// title_flg_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getTitleFlgChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getTitleFlgChgInd()) : -1);

				// title_length_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getTitleLengthChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getTitleLengthChgInd()) : -1);

				// title_md5_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getTitleMd5ChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getTitleMd5ChgInd()) : -1);

				// title_simhash_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getTitleSimhashChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getTitleSimhashChgInd()) : -1);

				// viewport_content_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getViewportContentChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getViewportContentChgInd()) : -1);

				// viewport_flag_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getViewportFlagChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getViewportFlagChgInd()) : -1);

				// change_tracking_hash_cd_json
				preparedStatement.setString(index++,
						htmlClickHouseEntity.getChangeTrackingHashCdJsonArray() != null && htmlClickHouseEntity.getChangeTrackingHashCdJsonArray().length > 0
								? gson.toJson(htmlClickHouseEntity.getChangeTrackingHashCdJsonArray(), ChangeTrackingHashCdJson[].class)
								: null);

				// sign
				preparedStatement.setInt(index++, htmlClickHouseEntity.getSign() != null ? htmlClickHouseEntity.getSign() : IConstants.CLICKHOUSE_SIGN_POSITIVE_1);

				// page_analysis_results_chg_ind_json
				preparedStatement.setString(index++, htmlClickHouseEntity.getPageAnalysisResultsChgIndJson());

				// canonical_added_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getCanonicalAddedInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getCanonicalAddedInd()) : -1);

				// canonical_removed_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getCanonicalRemovedInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getCanonicalRemovedInd()) : -1);

				// description_added_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getDescriptionAddedInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getDescriptionAddedInd()) : -1);

				// description_removed_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getDescriptionRemovedInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getDescriptionRemovedInd()) : -1);

				// h1_added_ind
				preparedStatement.setInt(index++, htmlClickHouseEntity.getH1AddedInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getH1AddedInd()) : -1);

				// h1_removed_ind
				preparedStatement.setInt(index++, htmlClickHouseEntity.getH1RemovedInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getH1RemovedInd()) : -1);

				// h2_added_ind
				preparedStatement.setInt(index++, htmlClickHouseEntity.getH2AddedInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getH2AddedInd()) : -1);

				// h2_chg_ind
				preparedStatement.setInt(index++, htmlClickHouseEntity.getH2ChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getH2ChgInd()) : -1);

				// h2_removed_ind
				preparedStatement.setInt(index++, htmlClickHouseEntity.getH2RemovedInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getH2RemovedInd()) : -1);

				// hreflang_links_added_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getHreflangLinksAddedInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getHreflangLinksAddedInd()) : -1);

				// hreflang_links_removed_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getHreflangLinksRemovedInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getHreflangLinksRemovedInd()) : -1);

				// open_graph_added_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getOpenGraphAddedInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getOpenGraphAddedInd()) : -1);

				// open_graph_removed_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getOpenGraphRemovedInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getOpenGraphRemovedInd()) : -1);

				// redirect_301_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getRedirect301ChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getRedirect301ChgInd()) : -1);

				// redirect_301_detected_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getRedirect301DetectedInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getRedirect301DetectedInd()) : -1);

				// redirect_301_removed_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getRedirect301RemovedInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getRedirect301RemovedInd()) : -1);

				// redirect_302_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getRedirect302ChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getRedirect302ChgInd()) : -1);

				// redirect_302_detected_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getRedirect302DetectedInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getRedirect302DetectedInd()) : -1);

				// redirect_302_removed_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getRedirect302RemovedInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getRedirect302RemovedInd()) : -1);

				// redirect_diff_code_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getRedirectDiffCodeInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getRedirectDiffCodeInd()) : -1);

				// robots_added_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getRobotsAddedInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getRobotsAddedInd()) : -1);

				// robots_removed_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getRobotsRemovedInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getRobotsRemovedInd()) : -1);

				// title_added_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getTitleAddedInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getTitleAddedInd()) : -1);

				// title_removed_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getTitleRemovedInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getTitleRemovedInd()) : -1);

				// viewport_added_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getViewportAddedInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getViewportAddedInd()) : -1);

				// viewport_removed_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getViewportRemovedInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getViewportRemovedInd()) : -1);

				// internal_link_count
				preparedStatement.setInt(index++, htmlClickHouseEntity.getInternalLinkCount() != null ? htmlClickHouseEntity.getInternalLinkCount() : -1);

				// custom_data_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getCustomDataChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getCustomDataChgInd()) : -1);

				// page_analysis_results_reverse
				preparedStatement.setString(index++, htmlClickHouseEntity.getPageAnalysisResultsReverse());

				// base_tag
				preparedStatement.setString(index++, crawlerResponse.getBase_tag());

				// base_tag_flag
				preparedStatement.setInt(index++, crawlerResponse.getBase_tag_flag() != null ? BooleanUtils.toInteger(crawlerResponse.getBase_tag_flag()) : -1);

				// base_tag_target
				preparedStatement.setString(index++, crawlerResponse.getBase_tag_target());

				// base_tag_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getBaseTagChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getBaseTagChgInd()) : -1);

				// base_tag_flag_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getBaseTagFlagChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getBaseTagFlagChgInd()) : -1);

				// base_tag_target_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getBaseTagTargetChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getBaseTagTargetChgInd()) : -1);

				// base_tag_added_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getBaseTagAddedInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getBaseTagAddedInd()) : -1);

				// base_tag_removed_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getBaseTagRemovedInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getBaseTagRemovedInd()) : -1);

				// page_analysis_fragments
				preparedStatement.setString(index++,
						htmlClickHouseEntity.getPageAnalysisFragmentsArray() != null && htmlClickHouseEntity.getPageAnalysisFragmentsArray().length > 0
								? gson.toJson(htmlClickHouseEntity.getPageAnalysisFragmentsArray(), PageAnalysisFragments[].class)
								: null);

				// page_analysis_fragments_chg_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getPageAnalysisFragmentsChgInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getPageAnalysisFragmentsChgInd())
								: -1);

				// content_guard_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getContentGuardInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getContentGuardInd()) : -1);

				// custom_data_added_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getCustomDataAddedInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getCustomDataAddedInd()) : -1);

				// custom_data_removed_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getCustomDataRemovedInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getCustomDataRemovedInd()) : -1);

				// response_headers_added_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getResponseHeadersAddedInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getResponseHeadersAddedInd()) : -1);

				// response_headers_removed_ind
				preparedStatement.setInt(index++,
						htmlClickHouseEntity.getResponseHeadersRemovedInd() != null ? BooleanUtils.toInteger(htmlClickHouseEntity.getResponseHeadersRemovedInd()) : -1);

				// previous_crawl_timestamp

				preparedStatement.setTimestamp(index++,
						htmlClickHouseEntity.getPreviousCrawlTimestamp() != null ? new java.sql.Timestamp(htmlClickHouseEntity.getPreviousCrawlTimestamp().getTime())
								: null);

				preparedStatement.addBatch();
			}

			int retryCount = 0;
			//long startTimestamp = 0L;
			while (retryCount < maximumRetryCounts) {
				try {
					//startTimestamp = System.nanoTime();
					preparedStatement.executeBatch();
					retryCount = maximumRetryCounts;
				} catch (Exception e) {
					retryCount++;
					if (retryCount >= maximumRetryCounts) {
						e.printStackTrace();
						throw e;
					} else {
						if (StringUtils.containsIgnoreCase(e.getMessage(), IConstants.FAILED_TO_RESPOND) == false) {
							FormatUtils.getInstance().logMemoryUsage("createBatch() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
						}
						try {
							Thread.sleep(retryWaitMilliseconds);
						} catch (InterruptedException e1) {
							e1.printStackTrace();
						}
					}
				} finally {
					if (preparedStatement != null) {
						preparedStatement.closeOnCompletion();
					}
				}
			}
		}
		//try {
		//	FormatUtils.getInstance().logMemoryUsage("createBatch() ip=" + ip + ",queueName=" + queueName + ",htmlClickHouseEntityList.size()=" + htmlClickHouseEntityList.size()
		//			+ ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		//} catch (Exception e) {
		//	e.printStackTrace();
		//}
	}

	private synchronized String getCreationSqlStatement(String tableName) {
		String response = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" insert into " + getTableName(tableName) + " ");
		stringBuilder.append(" (");
		stringBuilder.append("	domain_id,");
		stringBuilder.append("	url,");
		stringBuilder.append("	track_date,");
		stringBuilder.append("	alt_img_list,");
		stringBuilder.append("	alternate_links,");
		stringBuilder.append("	amphtml_flag,");
		stringBuilder.append("	amphtml_href,");
		stringBuilder.append("	analyzed_url_flg_s,");
		stringBuilder.append("	analyzed_url_s,");
		stringBuilder.append("	archive_flg,");
		stringBuilder.append("	archive_flg_x_tag,");
		stringBuilder.append("	blocked_by_robots,");
		stringBuilder.append("	canonical,");
		stringBuilder.append("	canonical_flg,");
		stringBuilder.append("	canonical_header_flag,");
		stringBuilder.append("	canonical_header_type,");
		stringBuilder.append("	canonical_type,");
		stringBuilder.append("	canonical_url_is_consistent,");
		stringBuilder.append("	content_type,");
		stringBuilder.append("	count_of_objects,");
		stringBuilder.append("	custom_data,");
		stringBuilder.append("	description,");
		stringBuilder.append("	description_flg,");
		stringBuilder.append("	description_length,");
		stringBuilder.append("	description_simhash,");
		stringBuilder.append("	document_size,");
		stringBuilder.append("	download_latency,");
		stringBuilder.append("	download_time,");
		stringBuilder.append("	error_message,");
		stringBuilder.append("	final_response_code,");
		stringBuilder.append("	folder_level_1,");
		stringBuilder.append("	folder_level_2,");
		stringBuilder.append("	folder_level_3,");
		stringBuilder.append("	folder_level_count,");
		stringBuilder.append("	follow_flg,");
		stringBuilder.append("	follow_flg_x_tag,");
		stringBuilder.append("	h1,");
		stringBuilder.append("	h1_count,");
		stringBuilder.append("	h1_flg,");
		stringBuilder.append("	h1_length,");
		stringBuilder.append("	h1_md5,");
		stringBuilder.append("	h1_simhash,");
		stringBuilder.append("	h2,");
		stringBuilder.append("	h2_simhash,");
		stringBuilder.append("	header_noarchive,");
		stringBuilder.append("	header_nofollow,");
		stringBuilder.append("	header_noindex,");
		stringBuilder.append("	header_noodp,");
		stringBuilder.append("	header_nosnippet,");
		stringBuilder.append("	header_noydir,");
		stringBuilder.append("	hreflang_errors,");
		stringBuilder.append("	hreflang_links,");
		stringBuilder.append("	hreflang_links_out_count,");
		stringBuilder.append("	hreflang_url_count,");
		stringBuilder.append("	index_flg,");
		stringBuilder.append("	index_flg_x_tag,");
		stringBuilder.append("	indexable,");
		stringBuilder.append("	insecure_resources,");
		stringBuilder.append("	insecure_resources_flag,");
		stringBuilder.append("	long_redirect,");
		stringBuilder.append("	meta_charset,");
		stringBuilder.append("	meta_content_type,");
		stringBuilder.append("	meta_disabled_sitelinks,");
		stringBuilder.append("	meta_noodp,");
		stringBuilder.append("	meta_nosnippet,");
		stringBuilder.append("	meta_noydir,");
		stringBuilder.append("	meta_redirect,");
		stringBuilder.append("	mixed_redirects,");
		stringBuilder.append("	mobile_rel_alternate_url_is_consistent,");
		stringBuilder.append("	noodp,");
		stringBuilder.append("	nosnippet,");
		stringBuilder.append("	noydir,");
		stringBuilder.append("	og_markup,");
		stringBuilder.append("	og_markup_flag,");
		stringBuilder.append("	og_markup_length,");
		stringBuilder.append("	outlink_count,");
		stringBuilder.append("	page_1,");
		stringBuilder.append("	page_link,");
		stringBuilder.append("	page_timeout_flag,");
		stringBuilder.append("	paginated,");
		stringBuilder.append("	pagination_links,");
		stringBuilder.append("	protocol,");
		stringBuilder.append("	redirect_blocked,");
		stringBuilder.append("	redirect_blocked_reason,");
		stringBuilder.append("	redirect_chain,");
		stringBuilder.append("	redirect_final_url,");
		stringBuilder.append("	redirect_flg,");
		stringBuilder.append("	redirect_times,");
		stringBuilder.append("	rel_next_html_url,");
		stringBuilder.append("	rel_next_url_is_consistent,");
		stringBuilder.append("	rel_prev_url_is_consistent,");
		stringBuilder.append("	request_headers,");
		stringBuilder.append("	request_time,");
		stringBuilder.append("	response_code,");
		stringBuilder.append("	response_headers,");
		stringBuilder.append("	retry_attempted,");
		stringBuilder.append("	robots,");
		stringBuilder.append("	robots_contents,");
		stringBuilder.append("	robots_contents_x_tag,");
		stringBuilder.append("	robots_flg,");
		stringBuilder.append("	robots_flg_x_tag,");
		stringBuilder.append("	server_response_time,");
		stringBuilder.append("	source_url,");
		stringBuilder.append("	splash_took,");
		// https://www.wrike.com/open.htm?id=390204287
		stringBuilder.append("	structured_data,");
		stringBuilder.append("	title,");
		stringBuilder.append("	title_flg,");
		stringBuilder.append("	title_length,");
		stringBuilder.append("	title_md5,");
		stringBuilder.append("	title_simhash,");
		stringBuilder.append("	twitter_description_length,");
		stringBuilder.append("	twitter_markup,");
		stringBuilder.append("	twitter_markup_flag,");
		stringBuilder.append("	twitter_markup_length,");
		stringBuilder.append("	url_length,");
		stringBuilder.append("	valid_twitter_card,");
		stringBuilder.append("	viewport_content,");
		stringBuilder.append("	viewport_flag,");
		stringBuilder.append("	page_analysis_results,");
		stringBuilder.append("	change_tracking_hash,");
		stringBuilder.append("	week_of_year,");
		stringBuilder.append("	crawl_timestamp,");
		stringBuilder.append("	alternate_links_chg_ind,");
		stringBuilder.append("	amphtml_flag_chg_ind,");
		stringBuilder.append("	amphtml_href_chg_ind,");
		stringBuilder.append("	analyzed_url_flg_s_chg_ind,");
		stringBuilder.append("	analyzed_url_s_chg_ind,");
		stringBuilder.append("	archive_flg_chg_ind,");
		stringBuilder.append("	archive_flg_x_tag_chg_ind,");
		stringBuilder.append("	blocked_by_robots_chg_ind,");
		stringBuilder.append("	canonical_chg_ind,");
		stringBuilder.append("	canonical_flg_chg_ind,");
		stringBuilder.append("	canonical_header_flag_chg_ind,");
		stringBuilder.append("	canonical_header_type_chg_ind,");
		stringBuilder.append("	canonical_type_chg_ind,");
		stringBuilder.append("	canonical_url_is_consistent_chg_ind,");
		stringBuilder.append("	content_type_chg_ind,");
		stringBuilder.append("	description_chg_ind,");
		stringBuilder.append("	description_flg_chg_ind,");
		stringBuilder.append("	description_length_chg_ind,");
		stringBuilder.append("	description_simhash_chg_ind,");
		stringBuilder.append("	error_message_chg_ind,");
		stringBuilder.append("	final_response_code_chg_ind,");
		stringBuilder.append("	follow_flg_chg_ind,");
		stringBuilder.append("	follow_flg_x_tag_chg_ind,");
		stringBuilder.append("	h1_chg_ind,");
		stringBuilder.append("	h1_count_chg_ind,");
		stringBuilder.append("	h1_flg_chg_ind,");
		stringBuilder.append("	h1_length_chg_ind,");
		stringBuilder.append("	h1_md5_chg_ind,");
		stringBuilder.append("	header_noarchive_chg_ind,");
		stringBuilder.append("	header_nofollow_chg_ind,");
		stringBuilder.append("	header_noindex_chg_ind,");
		stringBuilder.append("	header_noodp_chg_ind,");
		stringBuilder.append("	header_nosnippet_chg_ind,");
		stringBuilder.append("	header_noydir_chg_ind,");
		stringBuilder.append("	hreflang_errors_chg_ind,");
		stringBuilder.append("	hreflang_links_chg_ind,");
		stringBuilder.append("	hreflang_links_out_count_chg_ind,");
		stringBuilder.append("	hreflang_url_count_chg_ind,");
		stringBuilder.append("	index_flg_chg_ind,");
		stringBuilder.append("	index_flg_x_tag_chg_ind,");
		stringBuilder.append("	indexable_chg_ind,");
		stringBuilder.append("	insecure_resources_chg_ind,");
		stringBuilder.append("	insecure_resources_flag_chg_ind,");
		stringBuilder.append("	meta_charset_chg_ind,");
		stringBuilder.append("	meta_content_type_chg_ind,");
		stringBuilder.append("	meta_disabled_sitelinks_chg_ind,");
		stringBuilder.append("	meta_noodp_chg_ind,");
		stringBuilder.append("	meta_nosnippet_chg_ind,");
		stringBuilder.append("	meta_noydir_chg_ind,");
		stringBuilder.append("	meta_redirect_chg_ind,");
		stringBuilder.append("	mixed_redirects_chg_ind,");
		stringBuilder.append("	mobile_rel_alternate_url_is_consistent_chg_ind,");
		stringBuilder.append("	noodp_chg_ind,");
		stringBuilder.append("	nosnippet_chg_ind,");
		stringBuilder.append("	noydir_chg_ind,");
		stringBuilder.append("	og_markup_chg_ind,");
		stringBuilder.append("	og_markup_flag_chg_ind,");
		stringBuilder.append("	og_markup_length_chg_ind,");
		stringBuilder.append("	outlink_count_chg_ind,");
		stringBuilder.append("	page_link_chg_ind,");
		stringBuilder.append("	redirect_blocked_chg_ind,");
		stringBuilder.append("	redirect_blocked_reason_chg_ind,");
		stringBuilder.append("	redirect_chain_chg_ind,");
		stringBuilder.append("	redirect_final_url_chg_ind,");
		stringBuilder.append("	redirect_flg_chg_ind,");
		stringBuilder.append("	redirect_times_chg_ind,");
		stringBuilder.append("	response_code_chg_ind,");
		stringBuilder.append("	robots_chg_ind,");
		stringBuilder.append("	robots_contents_chg_ind,");
		stringBuilder.append("	robots_contents_x_tag_chg_ind,");
		stringBuilder.append("	robots_flg_chg_ind,");
		stringBuilder.append("	robots_flg_x_tag_chg_ind,");
		stringBuilder.append("	structured_data_chg_ind,");
		stringBuilder.append("	title_chg_ind,");
		stringBuilder.append("	title_flg_chg_ind,");
		stringBuilder.append("	title_length_chg_ind,");
		stringBuilder.append("	title_md5_chg_ind,");
		stringBuilder.append("	title_simhash_chg_ind,");
		stringBuilder.append("	viewport_content_chg_ind,");
		stringBuilder.append("	viewport_flag_chg_ind,");
		stringBuilder.append("	change_tracking_hash_cd_json,");
		stringBuilder.append("	sign,");
		stringBuilder.append("	page_analysis_results_chg_ind_json,");
		stringBuilder.append("	canonical_added_ind,");
		stringBuilder.append("	canonical_removed_ind,");
		stringBuilder.append("	description_added_ind,");
		stringBuilder.append("	description_removed_ind,");
		stringBuilder.append("	h1_added_ind,");
		stringBuilder.append("	h1_removed_ind,");
		stringBuilder.append("	h2_added_ind,");
		stringBuilder.append("	h2_chg_ind,");
		stringBuilder.append("	h2_removed_ind,");
		stringBuilder.append("	hreflang_links_added_ind,");
		stringBuilder.append("	hreflang_links_removed_ind,");
		stringBuilder.append("	open_graph_added_ind,");
		stringBuilder.append("	open_graph_removed_ind,");
		stringBuilder.append("	redirect_301_chg_ind,");
		stringBuilder.append("	redirect_301_detected_ind,");
		stringBuilder.append("	redirect_301_removed_ind,");
		stringBuilder.append("	redirect_302_chg_ind,");
		stringBuilder.append("	redirect_302_detected_ind,");
		stringBuilder.append("	redirect_302_removed_ind,");
		stringBuilder.append("	redirect_diff_code_ind,");
		stringBuilder.append("	robots_added_ind,");
		stringBuilder.append("	robots_removed_ind,");
		stringBuilder.append("	title_added_ind,");
		stringBuilder.append("	title_removed_ind,");
		stringBuilder.append("	viewport_added_ind,");
		stringBuilder.append("	viewport_removed_ind,");
		stringBuilder.append("	internal_link_count,");
		stringBuilder.append("	custom_data_chg_ind,");
		stringBuilder.append("	page_analysis_results_reverse,");
		stringBuilder.append("	base_tag,");
		stringBuilder.append("	base_tag_flag,");
		stringBuilder.append("	base_tag_target,");
		stringBuilder.append("	base_tag_chg_ind,");
		stringBuilder.append("	base_tag_flag_chg_ind,");
		stringBuilder.append("	base_tag_target_chg_ind,");
		stringBuilder.append("	base_tag_added_ind,");
		stringBuilder.append("	base_tag_removed_ind,");
		stringBuilder.append("	page_analysis_fragments,");
		stringBuilder.append("	page_analysis_fragments_chg_ind,");
		stringBuilder.append("	content_guard_ind,");
		stringBuilder.append("	custom_data_added_ind,");
		stringBuilder.append("	custom_data_removed_ind,");
		stringBuilder.append("	response_headers_added_ind,");
		stringBuilder.append("	response_headers_removed_ind,");
		stringBuilder.append("	previous_crawl_timestamp");
		stringBuilder.append(" )");
		stringBuilder.append(" values ");
		stringBuilder.append(" (");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?");
		stringBuilder.append(" )");
		stringBuilder.append(IConstants.SEMI_COLON);
		response = stringBuilder.toString();
		return response;
	}

	// 1) when 'cutoffTrackDate' (YYYY-MM-DD) is available in input, find the last record before 'cutoffTrackDate' (ie. in any day before cutoff track date).
	// 2) when 'cutoffCrawlTimestamp' (YYYY-MM-DD HH:MM:SS) is available in input, find the last record before 'cutoffCrawlTimestamp' (could be in same track date).
	public synchronized HtmlClickHouseEntity getPrevious(String ip, String queueName, int domainId, String urlString, List<String> databaseFields, String tableName,
			Date cutoffTrackDate, Date cutoffCrawlTimestamp) throws Exception {
		//long startTimestamp = System.currentTimeMillis();
		HtmlClickHouseEntity htmlClickHouseEntity = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		String trimmedUrlString = StringUtils.trimToEmpty(urlString);
		PreparedStatement preparedStatement = null;

		boolean isFirstField = true;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		if (databaseFields != null && databaseFields.size() > 0) {
			for (String databaseField : databaseFields) {
				if (isFirstField == true) {
					isFirstField = false;
					stringBuilder.append(IConstants.ONE_SPACE);
				} else {
					stringBuilder.append(IConstants.COMMA);
				}
				stringBuilder.append(databaseField);
			}
		} else {
			stringBuilder.append(" *");
		}
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("     domain_id = ?");
		stringBuilder.append(" and url_hash = URLHash(?)");
		stringBuilder.append(" and url_murmur_hash = murmurHash3_64(?)");
		stringBuilder.append(" and crawl_timestamp =");
		stringBuilder.append(" (");
		stringBuilder.append("  select");
		stringBuilder.append("      max(crawl_timestamp)");
		stringBuilder.append("  from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append("  where");
		if (cutoffTrackDate != null) {
			stringBuilder.append("      track_date < ?");
			stringBuilder.append("  and domain_id = ?");
		} else {
			stringBuilder.append("      domain_id = ?");
		}
		stringBuilder.append("  and url_hash = URLHash(?)");
		stringBuilder.append("  and url_murmur_hash = murmurHash3_64(?)");
		if (cutoffCrawlTimestamp != null) {
			stringBuilder.append("  and crawl_timestamp < ?");
		}
		stringBuilder.append(" )");
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getPrevious() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		retryCount = 0;
		int index = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setString(index++, trimmedUrlString);
				preparedStatement.setString(index++, trimmedUrlString);
				if (cutoffTrackDate != null) {
					preparedStatement.setDate(index++, new java.sql.Date(cutoffTrackDate.getTime()));
				}
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setString(index++, trimmedUrlString);
				preparedStatement.setString(index++, trimmedUrlString);
				if (cutoffCrawlTimestamp != null) {
					preparedStatement.setTimestamp(index++, new java.sql.Timestamp(cutoffCrawlTimestamp.getTime()));
				}
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					htmlClickHouseEntity = CrawlerUtils.getInstance().getHtmlClickHouseEntityFromResultSet(resultSet, databaseFields);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getPrevious() ip=" + ip + ", queueName=" + queueName + ", domainId=" + domainId + ", urlString="
							+ urlString + ",e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		//FormatUtils.getInstance()
		//		.logMemoryUsage("getPrevious() ends. ip=" + ip + ", queueName=" + queueName + ", domainId=" + domainId + ", urlString=" + urlString + ",isAvailable="
		//				+ (htmlClickHouseEntity != null ? true : false) + ",daoIndex=" + daoIndex + ", elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return htmlClickHouseEntity;
	}

	// retrieve the one record with the exact crawl timestamp
	public synchronized HtmlClickHouseEntity getByCrawlTimestamp(Date crawlTimestamp, int domainId, String urlString, List<String> databaseFields, String tableName)
			throws Exception {
		//long startTimestamp = System.currentTimeMillis();
		//FormatUtils.getInstance()
		//		.logMemoryUsage("getByCrawlTimestamp() begins. crawlTimestamp=" + crawlTimestamp + ",domainId=" + domainId + ",urlString=" + urlString);
		HtmlClickHouseEntity htmlClickHouseEntity = null;
		ResultSet resultSet = null;
		Date trackDate = DateUtils.truncate(crawlTimestamp, Calendar.DAY_OF_MONTH);
		String trimmedString = StringUtils.trimToEmpty(urlString);
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		boolean isFirstField = true;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		if (databaseFields != null && databaseFields.size() > 0) {
			for (String databaseField : databaseFields) {
				if (isFirstField == true) {
					isFirstField = false;
					stringBuilder.append(IConstants.ONE_SPACE);
				} else {
					stringBuilder.append(IConstants.COMMA);
				}
				stringBuilder.append(databaseField);
			}
		} else {
			stringBuilder.append(" *");
		}
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("     track_date = ?");
		stringBuilder.append(" and domain_id = ?");
		stringBuilder.append(" and url_hash = URLHash(?)");
		stringBuilder.append(" and url_murmur_hash = murmurHash3_64(?)");
		stringBuilder.append(" and crawl_timestamp = ?");
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getByCrawlTimestamp() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		retryCount = 0;
		int index = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setDate(index++, new java.sql.Date(trackDate.getTime()));
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setString(index++, trimmedString);
				preparedStatement.setString(index++, trimmedString);
				preparedStatement.setTimestamp(index++, new java.sql.Timestamp(crawlTimestamp.getTime()));
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					htmlClickHouseEntity = CrawlerUtils.getInstance().getHtmlClickHouseEntityFromResultSet(resultSet, databaseFields);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getByCrawlTimestamp() crawlTimestamp=" + crawlTimestamp + ",trackDate=" + trackDate + ",domainId="
							+ domainId + ", urlString=" + urlString + ",e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		//FormatUtils.getInstance().logMemoryUsage("getByCrawlTimestamp() ends. crawlTimestamp=" + crawlTimestamp + ",trackDate=" + trackDate + ",domainId=" + domainId
		//		+ ",urlString=" + urlString + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return htmlClickHouseEntity;
	}

	public synchronized List<HtmlClickHouseEntity> getLatestFromHistoricalAfterTrackDate(int domainId, List<String> databaseFields, String trackDate, String lastChangedTrackDate) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		//FormatUtils.getInstance().logMemoryUsage("getLatestFromHistorical() begins. domainId=" + domainId + ",tableName=" + tableName);
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = new ArrayList<>();
		HtmlClickHouseEntity htmlClickHouseEntity = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		boolean isFirstField = true;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		if (databaseFields != null && databaseFields.size() > 0) {
			for (String databaseField : databaseFields) {
				if (isFirstField == true) {
					isFirstField = false;
					stringBuilder.append(IConstants.ONE_SPACE);
				} else {
					stringBuilder.append(IConstants.COMMA);
				}
				stringBuilder.append(databaseField);
			}
		} else {
			stringBuilder.append(" *");
		}
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(null));
		stringBuilder.append(" where domain_id = ? and track_date >= ?");
		stringBuilder.append(" and (");
		stringBuilder.append("       url_hash,");
		stringBuilder.append("       url_murmur_hash,");
		stringBuilder.append("       crawl_timestamp");
		stringBuilder.append(" ) global in");
		stringBuilder.append(" (");
		stringBuilder.append(" select");
		stringBuilder.append("   url_hash,");
		stringBuilder.append("   url_murmur_hash,");
		stringBuilder.append("   max(crawl_timestamp)");
		stringBuilder.append(" from ");
		stringBuilder.append(getTableName(null));
		stringBuilder.append(" where ");
        stringBuilder.append(" domain_id = ? and track_date >= ? and (url_hash, url_murmur_hash) global in ");
        stringBuilder.append(" (select url_hash, url_murmur_hash from dis_url_last_changed_timestamp where domain_id = ? and track_date = ?) ");
		stringBuilder.append(" group by");
		stringBuilder.append("   url_hash,");
		stringBuilder.append("   url_murmur_hash");
		stringBuilder.append(" )");
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getLatestFromHistorical() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		retryCount = 0;
		int index = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				htmlClickHouseEntityList = new ArrayList<>();
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setString(index++, trackDate);
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setString(index++, trackDate);
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setString(index++, lastChangedTrackDate);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					htmlClickHouseEntity = CrawlerUtils.getInstance().getHtmlClickHouseEntityFromResultSet(resultSet, databaseFields);
					htmlClickHouseEntityList.add(htmlClickHouseEntity);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance()
							.logMemoryUsage("getLatestFromHistorical() domainId=" + domainId + ",e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		//FormatUtils.getInstance().logMemoryUsage("getLatestFromHistorical() ends. domainId=" + domainId + ",htmlClickHouseEntityList.size()="
		//		+ htmlClickHouseEntityList.size() + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		return htmlClickHouseEntityList;
	}

	// retrieve all latest records (with latest crawl timestamp) of a domain with target_url_to_crawl
	public synchronized List<HtmlClickHouseEntity> getLatestFromHistorical(int domainId, List<String> databaseFields) throws Exception {
		final StringBuilder stringBuilder = new StringBuilder("select ");
		if (databaseFields != null && !databaseFields.isEmpty()) {
			stringBuilder.append(String.join(IConstants.COMMA, databaseFields));
		} else {
			stringBuilder.append(" * ");
		}
		stringBuilder.append(" from ")
				.append(getTableName(null))
				.append(" where domain_id = ? and ( url_hash, url_murmur_hash, crawl_timestamp ) global in ( select url_hash, url_murmur_hash, max(crawl_timestamp) from ")
				.append(getTableName(null))
				.append(" where domain_id = ? and ( url_hash, url_murmur_hash ) global in (select distinct url_hash, url_murmur_hash from dis_target_url_to_crawl where domain_id = ? and track_date global in")
				.append(" (select max(track_date) from dis_target_url_to_crawl where domain_id = ?) ) group by url_hash, url_murmur_hash )");
		final Connection connection = connectionList.get(0);
		int retryCount = 0;
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = new ArrayList<>();
		while (retryCount < maximumRetryCounts) {
			int index = 1;
			try (PreparedStatement preparedStatement = connection.prepareStatement(stringBuilder.toString())) {
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setInt(index++, domainId);
				try (ResultSet resultSet = preparedStatement.executeQuery()) {
					HtmlClickHouseEntity htmlClickHouseEntity;
					while (resultSet.next()) {
						htmlClickHouseEntity = CrawlerUtils.getInstance().getHtmlClickHouseEntityFromResultSet(resultSet, databaseFields);
						htmlClickHouseEntityList.add(htmlClickHouseEntity);
					}
					return htmlClickHouseEntityList;
				}
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					log.error(e.getMessage(), e);
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getLatestFromHistorical() e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						log.error("getLatestFromHistorical() Thread.sleep() error", e1);
					}
				}
			}
		}
		return htmlClickHouseEntityList;
	}


	// retrieve all latest records (with latest crawl timestamp) of a domain
	public synchronized List<HtmlClickHouseEntity> getLatestFromHistorical(int domainId, List<String> databaseFields, String tableName) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		//FormatUtils.getInstance().logMemoryUsage("getLatestFromHistorical() begins. domainId=" + domainId + ",tableName=" + tableName);
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = new ArrayList<HtmlClickHouseEntity>();
		HtmlClickHouseEntity htmlClickHouseEntity = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		boolean isFirstField = true;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		if (databaseFields != null && databaseFields.size() > 0) {
			for (String databaseField : databaseFields) {
				if (isFirstField == true) {
					isFirstField = false;
					stringBuilder.append(IConstants.ONE_SPACE);
				} else {
					stringBuilder.append(IConstants.COMMA);
				}
				stringBuilder.append(databaseField);
			}
		} else {
			stringBuilder.append(" *");
		}
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where domain_id = ?");
		stringBuilder.append(" and (");
		stringBuilder.append("       url_hash,");
		stringBuilder.append("       url_murmur_hash,");
		stringBuilder.append("       crawl_timestamp");
		stringBuilder.append(" ) global in");
		stringBuilder.append(" (");
		stringBuilder.append(" select");
		stringBuilder.append("   url_hash,");
		stringBuilder.append("   url_murmur_hash,");
		stringBuilder.append("   max(crawl_timestamp)");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("   domain_id = ?");
		stringBuilder.append(" group by");
		stringBuilder.append("   url_hash,");
		stringBuilder.append("   url_murmur_hash");
		stringBuilder.append(" )");
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getLatestFromHistorical() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		retryCount = 0;
		int index = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				htmlClickHouseEntityList = new ArrayList<HtmlClickHouseEntity>();
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setInt(index++, domainId);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					htmlClickHouseEntity = CrawlerUtils.getInstance().getHtmlClickHouseEntityFromResultSet(resultSet, databaseFields);
					htmlClickHouseEntityList.add(htmlClickHouseEntity);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance()
							.logMemoryUsage("getLatestFromHistorical() domainId=" + domainId + ",e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		//FormatUtils.getInstance().logMemoryUsage("getLatestFromHistorical() ends. domainId=" + domainId + ",htmlClickHouseEntityList.size()="
		//		+ htmlClickHouseEntityList.size() + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		return htmlClickHouseEntityList;
	}

    public synchronized List<PoliteCrawlSummaryValueObject> getDailyUpdateSummaryValueObjectList(int domainId, Date targetUrlHtmlDailyDate, String tableName)
			throws Exception {
		List<PoliteCrawlSummaryValueObject> politeCrawlSummaryValueObjectList = null;
		PoliteCrawlSummaryValueObject politeCrawlSummaryValueObject = null;
		ResultSet resultSet = null;
		Connection connection = null;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("   response_code,");
		stringBuilder.append("   count(*) as total_urls_with_response_code");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("     daily_data_creation_date = ?");
		stringBuilder.append(" and domain_id = ?");
		stringBuilder.append(" group by");
		stringBuilder.append("   response_code");
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getByTrackDate() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		int retryCount = 0;
		int index = 0;
		String responseCode = null;
		int httpStatusCode = 0;

		while (retryCount < maximumRetryCounts) {
			try {
				politeCrawlSummaryValueObjectList = new ArrayList<PoliteCrawlSummaryValueObject>();
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setDate(index++, new java.sql.Date(targetUrlHtmlDailyDate.getTime()));
				preparedStatement.setInt(index++, domainId);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					politeCrawlSummaryValueObject = new PoliteCrawlSummaryValueObject();

					// domain_id
					politeCrawlSummaryValueObject.setDomainId(domainId);

					// response_code
					responseCode = resultSet.getString(IConstants.RESPONSE_CODE);
					httpStatusCode = NumberUtils.toInt(responseCode);
					politeCrawlSummaryValueObject.setHttpStatusCode(httpStatusCode);

					// total_urls_with_response_code
					politeCrawlSummaryValueObject.setTotalUrlsWithResponseCode(resultSet.getInt("total_urls_with_response_code"));

					politeCrawlSummaryValueObjectList.add(politeCrawlSummaryValueObject);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage(
							"getDailyUpdateSummaryValueObjectList() domainId=" + domainId + ",e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}

		//if (isDebug == true) {
		//	for (PoliteCrawlSummaryValueObject testPoliteCrawlSummaryValueObject : politeCrawlSummaryValueObjectList) {
		//		FormatUtils.getInstance().logMemoryUsage("getDailyUpdateSummaryValueObjectList() testPoliteCrawlSummaryValueObject=" + testPoliteCrawlSummaryValueObject.toString());
		//	}
		//}

		return politeCrawlSummaryValueObjectList;
	}

	public synchronized List<HtmlClickHouseEntity> getDailyData(Date dailyDataCreationDate, int domainId, String urlString, List<String> databaseFields,
			String tableName) throws Exception {
		//long startTimestamp = System.currentTimeMillis();
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = new ArrayList<HtmlClickHouseEntity>();
		HtmlClickHouseEntity htmlClickHouseEntity = null;
		ResultSet resultSet = null;
		Connection connection = null;
		String trimmedUrlString = StringUtils.trimToEmpty(urlString);
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		boolean isFirstField = true;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		if (databaseFields != null && databaseFields.size() > 0) {
			for (String databaseField : databaseFields) {
				if (isFirstField == true) {
					isFirstField = false;
					stringBuilder.append(IConstants.ONE_SPACE);
				} else {
					stringBuilder.append(IConstants.COMMA);
				}
				stringBuilder.append(databaseField);
			}
		} else {
			stringBuilder.append(" *");
		}
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("     daily_data_creation_date = ?");
		stringBuilder.append(" and domain_id = ?");
		if (StringUtils.isNotBlank(urlString)) {
			stringBuilder.append(" and url_hash = URLHash(?)");
			stringBuilder.append(" and url_murmur_hash = murmurHash3_64(?)");
		}
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getDailyData() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		retryCount = 0;
		int index = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setDate(index++, new java.sql.Date(dailyDataCreationDate.getTime()));
				preparedStatement.setInt(index++, domainId);
				if (StringUtils.isNotBlank(urlString)) {
					preparedStatement.setString(index++, trimmedUrlString);
					preparedStatement.setString(index++, trimmedUrlString);
				}
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					htmlClickHouseEntity = CrawlerUtils.getInstance().getHtmlClickHouseEntityFromResultSet(resultSet, databaseFields);
					htmlClickHouseEntityList.add(htmlClickHouseEntity);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage(
							"getDailyData() domainId=" + domainId + ", urlString=" + urlString + ",e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		return htmlClickHouseEntityList;
	}

	// retrieve the page links of one record with the exact crawl timestamp
	public synchronized HtmlClickHouseEntity getPageLinkByCrawlTimestamp(Date crawlTimestamp, int domainId, String urlString, String tableName) throws Exception {
		//long startTimestamp = System.currentTimeMillis();
		//FormatUtils.getInstance()
		//		.logMemoryUsage("getPageLinkByCrawlTimestamp() begins. crawlTimestamp=" + crawlTimestamp + ",domainId=" + domainId + ",urlString=" + urlString + ",tableName=" + tableName);
		String trimmedUrlString = StringUtils.trimToEmpty(urlString);
		Date trackDate = DateUtils.truncate(crawlTimestamp, Calendar.DAY_OF_MONTH);
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		retryCount = 0;
		String sqlString = null;
		StringBuilder stringBuilder = null;
		int index = 0;
		HtmlClickHouseEntity htmlClickHouseEntity = null;
		String testString = null;
		PageLink[] pageLinkArray = null;
		Gson gson = new Gson();
		CrawlerResponse crawlerResponse = null;
		stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("  page_link");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("     track_date = ?");
		stringBuilder.append(" and domain_id = ?");
		stringBuilder.append(" and url_hash = URLHash(?)");
		stringBuilder.append(" and url_murmur_hash = murmurHash3_64(?)");
		stringBuilder.append(" and crawl_timestamp = ?");
		sqlString = stringBuilder.toString();
		//if (isDebug == true) {
		//	FormatUtils.getInstance().logMemoryUsage("getPageLinkByCrawlTimestamp() sqlString=" + sqlString);					
		//}
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setDate(index++, new java.sql.Date(trackDate.getTime()));
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setString(index++, trimmedUrlString);
				preparedStatement.setString(index++, trimmedUrlString);
				preparedStatement.setTimestamp(index++, new java.sql.Timestamp(crawlTimestamp.getTime()));
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {

					htmlClickHouseEntity = new HtmlClickHouseEntity();
					crawlerResponse = new CrawlerResponse();

					// page_link
					testString = resultSet.getString(IConstants.PAGE_LINK);
					if (StringUtils.isNotBlank(testString)) {
						pageLinkArray = gson.fromJson(testString, PageLink[].class);
						if (pageLinkArray != null && pageLinkArray.length > 0) {
							crawlerResponse.setPage_link(pageLinkArray);
							htmlClickHouseEntity.setCrawlerResponse(crawlerResponse);
						}
					}
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				e.printStackTrace();
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getPageLinkByCrawlTimestamp() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			} finally {
			}
		}
		//FormatUtils.getInstance().logMemoryUsage("getPageLinkByCrawlTimestamp() ends. crawlTimestamp=" + crawlTimestamp + ",domainId=" + domainId + ",urlString=" + urlString + ",tableName="
		//		+ tableName + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		return htmlClickHouseEntity;
	}

	public synchronized Date getEarliestTrackDate(String tableName) throws Exception {
		Date trackDate = null;
		//long startTimestamp = System.currentTimeMillis();
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("  min(track_date) as min_track_date");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getEarliestTrackDate() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				preparedStatement = connection.prepareStatement(sqlString);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {

					// track_date
					trackDate = resultSet.getDate("min_track_date");
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getEarliestTrackDate() e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}

		return trackDate;
	}

	// retrieve a unique list of records (with the latest crawl timestamp) that were changed on track date
	public synchronized List<HtmlClickHouseEntity> getChangeList(int domainId, Date trackDate, List<String> databaseFields, String tableName) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("getChangeList() begins. domainId=" + domainId + ",trackDate=" + trackDate + ",tableName=" + tableName);
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = new ArrayList<HtmlClickHouseEntity>();
		HtmlClickHouseEntity htmlClickHouseEntity = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;

		PreparedStatement preparedStatement = null;
		boolean isFirstField = true;
		String prefixString = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		for (String databaseField : databaseFields) {
			if (isFirstField == true) {
				isFirstField = false;
				stringBuilder.append(IConstants.ONE_SPACE);
			} else {
				stringBuilder.append(IConstants.COMMA);
			}
			stringBuilder.append(databaseField);
		}
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("     track_date = ?");
		stringBuilder.append(" and domain_id = ?");
		stringBuilder.append(" and (");
		isFirstField = true;
		for (String databaseField : databaseFields) {
			if (StringUtils.equalsIgnoreCase(databaseField, IConstants.RESPONSE_CODE_CHG_IND) || StringUtils.equalsIgnoreCase(databaseField, IConstants.TITLE_CHG_IND)
					|| StringUtils.equalsIgnoreCase(databaseField, IConstants.DESCRIPTION_CHG_IND) || StringUtils.equalsIgnoreCase(databaseField, IConstants.H1_CHG_IND)
					|| StringUtils.equalsIgnoreCase(databaseField, IConstants.H2_CHG_IND)
					|| StringUtils.equalsIgnoreCase(databaseField, IConstants.ROBOTS_CONTENTS_CHG_IND)
					|| StringUtils.equalsIgnoreCase(databaseField, IConstants.CANONICAL_CHG_IND)
					|| StringUtils.equalsIgnoreCase(databaseField, IConstants.CUSTOM_DATA_CHG_IND)) {
				if (isFirstField == true) {
					isFirstField = false;
					prefixString = IConstants.ONE_SPACE;
				} else {
					prefixString = " or ";
				}
				stringBuilder.append(prefixString).append(databaseField).append(" = ?");
			}
		}
		stringBuilder.append(" )");
		stringBuilder.append(" and (url_hash, url_murmur_hash, crawl_timestamp) global in");
		stringBuilder.append(" (");
		stringBuilder.append(" select");
		stringBuilder.append("     url_hash,");
		stringBuilder.append("     url_murmur_hash,");
		stringBuilder.append("     max(crawl_timestamp)");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("     track_date = ?");
		stringBuilder.append(" and domain_id = ?");
		stringBuilder.append(" group by");
		stringBuilder.append("     url_hash,");
		stringBuilder.append("     url_murmur_hash");
		stringBuilder.append(" )");
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getChangeList() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		retryCount = 0;
		int index = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setDate(index++, new java.sql.Date(trackDate.getTime()));
				preparedStatement.setInt(index++, domainId);
				for (String databaseField : databaseFields) {
					if (StringUtils.equalsIgnoreCase(databaseField, IConstants.RESPONSE_CODE_CHG_IND)
							|| StringUtils.equalsIgnoreCase(databaseField, IConstants.TITLE_CHG_IND)
							|| StringUtils.equalsIgnoreCase(databaseField, IConstants.DESCRIPTION_CHG_IND)
							|| StringUtils.equalsIgnoreCase(databaseField, IConstants.H1_CHG_IND) || StringUtils.equalsIgnoreCase(databaseField, IConstants.H2_CHG_IND)
							|| StringUtils.equalsIgnoreCase(databaseField, IConstants.ROBOTS_CONTENTS_CHG_IND)
							|| StringUtils.equalsIgnoreCase(databaseField, IConstants.CANONICAL_CHG_IND)
							|| StringUtils.equalsIgnoreCase(databaseField, IConstants.CUSTOM_DATA_CHG_IND)) {
						preparedStatement.setInt(index++, 1);
					}
				}
				preparedStatement.setDate(index++, new java.sql.Date(trackDate.getTime()));
				preparedStatement.setInt(index++, domainId);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					htmlClickHouseEntity = CrawlerUtils.getInstance().getHtmlClickHouseEntityFromResultSet(resultSet, databaseFields);
					htmlClickHouseEntityList.add(htmlClickHouseEntity);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage(
							"getChangeList() domainId=" + domainId + ",trackDate=" + trackDate + ",e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("getChangeList() ends. domainId=" + domainId + ",trackDate=" + trackDate + ",tableName=" + tableName
				+ ",htmlClickHouseEntityList.size()=" + htmlClickHouseEntityList.size() + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return htmlClickHouseEntityList;
	}

	public synchronized List<HtmlClickHouseEntity> getExtract(int domainId, List<String> databaseFields, String tableName) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("getExtract() begins. domainId=" + domainId + ",tableName=" + tableName);
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = new ArrayList<HtmlClickHouseEntity>();
		HtmlClickHouseEntity htmlClickHouseEntity = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		boolean isFirstField = true;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		if (databaseFields != null && databaseFields.size() > 0) {
			for (String databaseField : databaseFields) {
				if (isFirstField == true) {
					isFirstField = false;
					stringBuilder.append(IConstants.ONE_SPACE);
				} else {
					stringBuilder.append(IConstants.COMMA);
				}
				stringBuilder.append(databaseField);
			}
		} else {
			stringBuilder.append(" *");
		}
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("     domain_id = ?");
		stringBuilder.append(" and response_code = '200'");
		stringBuilder.append(" and custom_data != '[]'");
		stringBuilder.append(" and (");
		stringBuilder.append("       url_hash,");
		stringBuilder.append("       url_murmur_hash,");
		stringBuilder.append("       crawl_timestamp");
		stringBuilder.append(" ) global in");
		stringBuilder.append(" (");
		stringBuilder.append(" select");
		stringBuilder.append("   url_hash,");
		stringBuilder.append("   url_murmur_hash,");
		stringBuilder.append("   max(crawl_timestamp)");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("     domain_id = ?");
		stringBuilder.append(" and response_code = '200'");
		stringBuilder.append(" and custom_data != '[]'");
		stringBuilder.append(" group by");
		stringBuilder.append("   url_hash,");
		stringBuilder.append("   url_murmur_hash");
		stringBuilder.append(" )");
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getExtract() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		retryCount = 0;
		int index = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				htmlClickHouseEntityList = new ArrayList<HtmlClickHouseEntity>();
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setInt(index++, domainId);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					htmlClickHouseEntity = CrawlerUtils.getInstance().getHtmlClickHouseEntityFromResultSet(resultSet, databaseFields);
					htmlClickHouseEntityList.add(htmlClickHouseEntity);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getExtract() domainId=" + domainId + ",e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("getExtract() ends. domainId=" + domainId + ",htmlClickHouseEntityList.size()=" + htmlClickHouseEntityList.size()
				+ ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		return htmlClickHouseEntityList;
	}

	public synchronized List<HtmlClickHouseEntity> getLatestCustomData(int domainId, List<String> databaseFields, String tableName) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("getLatestCustomData() begins. domainId=" + domainId + ",tableName=" + tableName);
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = new ArrayList<HtmlClickHouseEntity>();
		HtmlClickHouseEntity htmlClickHouseEntity = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		boolean isFirstField = true;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		if (databaseFields != null && databaseFields.size() > 0) {
			for (String databaseField : databaseFields) {
				if (isFirstField == true) {
					isFirstField = false;
					stringBuilder.append(IConstants.ONE_SPACE);
				} else {
					stringBuilder.append(IConstants.COMMA);
				}
				stringBuilder.append(databaseField);
			}
		} else {
			stringBuilder.append(" *");
		}
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("     domain_id = ?");
		stringBuilder.append(" and response_code = '200'");
		stringBuilder.append(" and custom_data != '[]'");
		stringBuilder.append(" and custom_data > ''");
		stringBuilder.append(" and (");
		stringBuilder.append("       url_hash,");
		stringBuilder.append("       url_murmur_hash,");
		stringBuilder.append("       crawl_timestamp");
		stringBuilder.append(" ) global in");
		stringBuilder.append(" (");
		stringBuilder.append(" select");
		stringBuilder.append("   url_hash,");
		stringBuilder.append("   url_murmur_hash,");
		stringBuilder.append("   max(crawl_timestamp)");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("     domain_id = ?");
		stringBuilder.append(" and response_code = '200'");
		stringBuilder.append(" and custom_data != '[]'");
		stringBuilder.append(" and custom_data > ''");
		stringBuilder.append(" group by");
		stringBuilder.append("   url_hash,");
		stringBuilder.append("   url_murmur_hash");
		stringBuilder.append(" )");
		stringBuilder.append(" and (");
		stringBuilder.append("       url_hash,");
		stringBuilder.append("       url_murmur_hash");
		stringBuilder.append(" ) global in");
		stringBuilder.append("    (");
		stringBuilder.append("    select");
		stringBuilder.append("      url_hash,");
		stringBuilder.append("      url_murmur_hash");
		stringBuilder.append("    from");
		stringBuilder.append("      dis_target_url");
		stringBuilder.append("    where");
		stringBuilder.append("      domain_id = ?");
		stringBuilder.append("   )");
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getLatestCustomData() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		retryCount = 0;
		int index = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				htmlClickHouseEntityList = new ArrayList<HtmlClickHouseEntity>();
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setInt(index++, domainId);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					htmlClickHouseEntity = CrawlerUtils.getInstance().getHtmlClickHouseEntityFromResultSet(resultSet, databaseFields);
					htmlClickHouseEntityList.add(htmlClickHouseEntity);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance()
							.logMemoryUsage("getLatestCustomData() domainId=" + domainId + ",e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("getLatestCustomData() ends. domainId=" + domainId + ",htmlClickHouseEntityList.size()="
				+ htmlClickHouseEntityList.size() + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		return htmlClickHouseEntityList;
	}

	public synchronized List<HtmlClickHouseEntity> getLatestCustomDataByPartition(int domainId, List<String> databaseFields, String tableName, int partitionSize, int partitionIndex) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("getLatestCustomDataByPage() begins. domainId=" + domainId + ",tableName=" + tableName + ",partitionSize=" + partitionSize + ",partitionIndex=" + partitionIndex);
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = new ArrayList<HtmlClickHouseEntity>();
		HtmlClickHouseEntity htmlClickHouseEntity = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		boolean isFirstField = true;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		if (databaseFields != null && databaseFields.size() > 0) {
			for (String databaseField : databaseFields) {
				if (isFirstField == true) {
					isFirstField = false;
					stringBuilder.append(IConstants.ONE_SPACE);
				} else {
					stringBuilder.append(IConstants.COMMA);
				}
				stringBuilder.append(databaseField);
			}
		} else {
			stringBuilder.append(" *");
		}
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("     domain_id = ? and url_hash % ? = ?");
		stringBuilder.append(" and response_code = '200'");
		stringBuilder.append(" and custom_data != '[]'");
		stringBuilder.append(" and custom_data > ''");
		stringBuilder.append(" and (");
		stringBuilder.append("       url_hash,");
		stringBuilder.append("       url_murmur_hash,");
		stringBuilder.append("       crawl_timestamp");
		stringBuilder.append(" ) global in");
		stringBuilder.append(" (");
		stringBuilder.append(" select");
		stringBuilder.append("   url_hash,");
		stringBuilder.append("   url_murmur_hash,");
		stringBuilder.append("   max(crawl_timestamp)");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("     domain_id = ? and url_hash % ? = ?");
		stringBuilder.append(" and response_code = '200'");
		stringBuilder.append(" and custom_data != '[]'");
		stringBuilder.append(" and custom_data > ''");
		stringBuilder.append(" group by");
		stringBuilder.append("   url_hash,");
		stringBuilder.append("   url_murmur_hash");
		stringBuilder.append(" )");
		stringBuilder.append(" and (");
		stringBuilder.append("       url_hash,");
		stringBuilder.append("       url_murmur_hash");
		stringBuilder.append(" ) global in");
		stringBuilder.append("    (");
		stringBuilder.append("    select");
		stringBuilder.append("      url_hash,");
		stringBuilder.append("      url_murmur_hash");
		stringBuilder.append("    from");
		stringBuilder.append("      dis_target_url");
		stringBuilder.append("    where");
		stringBuilder.append("      domain_id = ? and url_hash % ? = ?");
		stringBuilder.append("   )");
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getLatestCustomDataByPage() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		retryCount = 0;
		int index = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				htmlClickHouseEntityList = new ArrayList<HtmlClickHouseEntity>();
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setInt(index++, partitionSize);
				preparedStatement.setInt(index++, partitionIndex);
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setInt(index++, partitionSize);
				preparedStatement.setInt(index++, partitionIndex);
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setInt(index++, partitionSize);
				preparedStatement.setInt(index++, partitionIndex);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					htmlClickHouseEntity = CrawlerUtils.getInstance().getHtmlClickHouseEntityFromResultSet(resultSet, databaseFields);
					htmlClickHouseEntityList.add(htmlClickHouseEntity);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance()
							.logMemoryUsage("getLatestCustomDataByPage() domainId=" + domainId + ",e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("getLatestCustomDataByPage() ends. domainId=" + domainId + ",htmlClickHouseEntityList.size()="
				+ htmlClickHouseEntityList.size() + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		return htmlClickHouseEntityList;
	}

	public synchronized List<HtmlClickHouseEntity> getChangeTrackingSummaryList(int domainId, String startTrackDate, String endTrackDate, String startCrawlTimestamp,
			String endCrawlTimestamp, List<String> changeTrackingIndicatorList) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		//FormatUtils.getInstance().logMemoryUsage("getChangeTrackingSummaryList() begins. domainId=" + domainId + ",startCrawlTimestamp=" + startCrawlTimestamp
		//		+ ",endCrawlTimestamp=" + endCrawlTimestamp + ",changeTrackingIndicatorList.size()=" + changeTrackingIndicatorList.size());
		//long startTimestamp = System.currentTimeMillis();
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = new ArrayList<HtmlClickHouseEntity>();
		HtmlClickHouseEntity htmlClickHouseEntity = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		int index = 0;
		PreparedStatement preparedStatement = null;
		String testChangeTrackingIndicator = null;

		List<String> databaseFields = new ArrayList<String>(changeTrackingIndicatorList);
		databaseFields.add(IConstants.CRAWL_TIMESTAMP);
		databaseFields.add(IConstants.URL);
		databaseFields.add(IConstants.RESPONSE_CODE);
		databaseFields.add(IConstants.PREVIOUS_CRAWL_TIMESTAMP);

		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select crawl_timestamp,url,response_code,url,previous_crawl_timestamp");
		for (String changeTrackingIndicator : changeTrackingIndicatorList) {
			stringBuilder.append(IConstants.COMMA + changeTrackingIndicator);
		}
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(null));
		stringBuilder.append(" where");
		stringBuilder.append("     track_date >= '" + startTrackDate + IConstants.SINGLE_QUOTE);
		stringBuilder.append(" and track_date <= '" + endTrackDate + IConstants.SINGLE_QUOTE);
		stringBuilder.append(" and domain_id = ?");
		stringBuilder.append(" and crawl_timestamp > '" + startCrawlTimestamp + IConstants.SINGLE_QUOTE);
		stringBuilder.append(" and crawl_timestamp <= '" + endCrawlTimestamp + IConstants.SINGLE_QUOTE);
		stringBuilder.append(" and previous_crawl_timestamp != '1970-01-01 00:00:00'");
		for (int i = 0; i < changeTrackingIndicatorList.size(); i++) {
			testChangeTrackingIndicator = changeTrackingIndicatorList.get(i);
			if (i == 0) {
				if (StringUtils.equalsIgnoreCase(testChangeTrackingIndicator, IConstants.PAGE_ANALYSIS_RESULTS_CHG_IND_JSON)) {
					stringBuilder.append(" and (" + testChangeTrackingIndicator + " > ''");
				} else {
					stringBuilder.append(" and (" + testChangeTrackingIndicator + " = 1");
				}
			} else {
				if (StringUtils.equalsIgnoreCase(testChangeTrackingIndicator, IConstants.PAGE_ANALYSIS_RESULTS_CHG_IND_JSON)) {
					stringBuilder.append(" or " + testChangeTrackingIndicator + " > ''");
				} else {
					stringBuilder.append(" or " + testChangeTrackingIndicator + " = 1");
				}
			}
		}
		stringBuilder.append(" )");
		String sqlString = stringBuilder.toString();
		//if (isDebug == true) {
		//	System.out.println("getChangeTrackingSummaryList() sqlString=" + sqlString);
		//}
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setInt(index++, domainId);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					htmlClickHouseEntity = CrawlerUtils.getInstance().getHtmlClickHouseEntityFromResultSet(resultSet, databaseFields);
					htmlClickHouseEntityList.add(htmlClickHouseEntity);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance()
							.logMemoryUsage("getChangeTrackingSummaryList() domainId=" + domainId + ",e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		//if (isDebug == true) {
		//	FormatUtils.getInstance()
		//	.logMemoryUsage("getChangeTrackingSummaryList() ends. domainId=" + domainId + ",startTrackDate=" + startTrackDate + ",startCrawlTimestamp="
		//			+ startCrawlTimestamp + ",endTrackDate=" + endTrackDate + ",endCrawlTimestamp=" + endCrawlTimestamp + ",htmlClickHouseEntityList.size()"
		//			+ htmlClickHouseEntityList.size() + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));			
		//}
		return htmlClickHouseEntityList;
	}

	public synchronized List<HtmlClickHouseEntity> getChangeTrackingFields(int domainId, String urlString, String previousDate, String currentDate, Date crawlTimestamp, Date previousCrawlTimestamp,
	                                                                       List<String> changeTrackingFields) throws Exception {
		//FormatUtils.getInstance().logMemoryUsage("getChangeTrackingFields() begins. domainId=" + domainId + ",urlString=" + urlString + ",crawlTimestamp="
		//		+ crawlTimestamp + ",previousCrawlTimestamp=" + previousCrawlTimestamp + ",changeTrackingFields.size()=" + changeTrackingFields.size());
		long startTimestamp = System.currentTimeMillis();
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = new ArrayList<HtmlClickHouseEntity>();
		HtmlClickHouseEntity htmlClickHouseEntity = null;
		String trimmedUrlString = StringUtils.trimToEmpty(urlString);
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		int index = 0;
		PreparedStatement preparedStatement = null;
		List<String> databaseFields = new ArrayList<String>(changeTrackingFields);
		databaseFields.add(IConstants.CRAWL_TIMESTAMP);
		databaseFields.add(IConstants.RESPONSE_CODE);

		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select crawl_timestamp, response_code");
		for (String changeTrackingField : changeTrackingFields) {
			stringBuilder.append(IConstants.COMMA + changeTrackingField);
		}
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(null));
		stringBuilder.append(" where");
		stringBuilder.append("     domain_id = ?");
		stringBuilder.append(" and track_date in(?, ?)");
		stringBuilder.append(" and url_hash = URLHash(?)");
		stringBuilder.append(" and url_murmur_hash = murmurHash3_64(?)");
		stringBuilder.append(" and (crawl_timestamp = ? or crawl_timestamp = ?)");
		String sqlString = stringBuilder.toString();
		//if (isDebug == true) {
		//	FormatUtils.getInstance().logMemoryUsage("getChangeTrackingFields() sqlString=" + sqlString);			
		//}
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setString(index++, previousDate);
				preparedStatement.setString(index++, currentDate);
				preparedStatement.setString(index++, trimmedUrlString);
				preparedStatement.setString(index++, trimmedUrlString);
				preparedStatement.setTimestamp(index++, new java.sql.Timestamp(crawlTimestamp.getTime()));
				preparedStatement.setTimestamp(index++, new java.sql.Timestamp(previousCrawlTimestamp.getTime()));
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					htmlClickHouseEntity = CrawlerUtils.getInstance().getHtmlClickHouseEntityFromResultSet(resultSet, databaseFields);
					htmlClickHouseEntityList.add(htmlClickHouseEntity);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getChangeTrackingFields() e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					if (e.getMessage() == null) {
						targetUrlHtmlClickHouseDAOMap = null;
						getInstance();
					} else {
						try {
							Thread.sleep(retryWaitMilliseconds);
						} catch (InterruptedException e1) {
							e1.printStackTrace();
						}
					}
				}
			}
		}
		//FormatUtils.getInstance()
		//		.logMemoryUsage("getChangeTrackingFields() ends. domainId=" + domainId + ",urlString=" + urlString + ",crawlTimestamp=" + crawlTimestamp
		//				+ ",previousCrawlTimestamp=" + previousCrawlTimestamp + ",htmlClickHouseEntityList.size()=" + htmlClickHouseEntityList.size() + ",elapsed(ms.)="
		//				+ (System.currentTimeMillis() - startTimestamp));
		return htmlClickHouseEntityList;
	}

	public synchronized int getTotalFilteredUrls(int domainId, String sqlPredicate, Date targetUrlHtmlDailyDate) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage(
				"getTotalFilteredUrls() begins. domainId=" + domainId + ",sqlPredicate=" + sqlPredicate + ",targetUrlHtmlDailyDate=" + targetUrlHtmlDailyDate);
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		int total = 0;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     count(*) as " + IConstants.TOTAL);
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(IConstants.TABLE_NAME_TARGET_URL_HTML_DAILY));
		stringBuilder.append(" where");
		stringBuilder.append("     daily_data_creation_date = ?");
		stringBuilder.append(" and domain_id = ?");
		stringBuilder.append(" and ").append(sqlPredicate);
		String sqlString = stringBuilder.toString();
		FormatUtils.getInstance().logMemoryUsage("getTotalFilteredUrls() domainId=" + domainId + ",sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		retryCount = 0;
		int index = 0;
		//while (retryCount < maximumRetryCounts) {
		while (retryCount < 2) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setDate(index++, new java.sql.Date(targetUrlHtmlDailyDate.getTime()));
				preparedStatement.setInt(index++, domainId);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					total = resultSet.getInt(IConstants.TOTAL);
				}
				//retryCount = maximumRetryCounts;
				retryCount = 2;
			} catch (Exception e) {
				retryCount++;
				//if (retryCount >= maximumRetryCounts) {
				if (retryCount >= 2) {
					//e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getTotalFilteredUrls() domainId=" + domainId + ",sqlPredicate=" + sqlPredicate + ",e.getMessage()="
							+ e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("getTotalFilteredUrls() ends. domainId=" + domainId + ",sqlPredicate=" + sqlPredicate + ",targetUrlHtmlDailyDate="
				+ targetUrlHtmlDailyDate + ",total=" + total + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return total;
	}

	public void createDailyDataFromHistoricalDataByTrackDate(int domainId, String sourceTableName, String destinationTableName, String minTrackDate, String maxTrackDate, String dailyDataCreationDate, int i) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		Connection connection = null;
		int retryCount = 0;
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		retryCount = 0;
		String sqlString = null;
		StringBuilder stringBuilder = null;
		maximumRetryCounts = 7;
		while (retryCount < maximumRetryCounts) {
			try {
				stringBuilder = new StringBuilder();
				stringBuilder.append(" insert into");
				stringBuilder.append(" " + destinationTableName);
				stringBuilder.append(" (");
				stringBuilder.append(
						"  domain_id,url,track_date,amphtml_flag,amphtml_href,analyzed_url_flg_s,analyzed_url_s,archive_flg,archive_flg_x_tag,blocked_by_robots,canonical,canonical_flg,canonical_header_flag,canonical_header_type,canonical_type,canonical_url_is_consistent,content_type,count_of_objects,description,description_flg,description_length,description_simhash,document_size,download_latency,download_time,error_message,final_response_code,folder_level_1,folder_level_2,folder_level_3,folder_level_count,follow_flg,follow_flg_x_tag,h1,h1_count,h1_flg,h1_length,h1_md5,h1_simhash,h2,h2_simhash,header_noarchive,header_nofollow,header_noindex,header_noodp,header_nosnippet,header_noydir,hreflang_links_out_count,hreflang_url_count,index_flg,index_flg_x_tag,indexable,insecure_resources_flag,long_redirect,meta_charset,meta_content_type,meta_disabled_sitelinks,meta_noodp,meta_nosnippet,meta_noydir,meta_redirect,mixed_redirects,mobile_rel_alternate_url_is_consistent,noodp,nosnippet,noydir,og_markup_flag,og_markup_length,outlink_count,page_1,page_timeout_flag,paginated,protocol,redirect_blocked,redirect_blocked_reason,redirect_final_url,redirect_flg,redirect_times,rel_next_html_url,rel_next_url_is_consistent,rel_prev_url_is_consistent,request_headers,request_time,response_code,retry_attempted,robots,robots_contents,robots_contents_x_tag,robots_flg,robots_flg_x_tag,server_response_time,source_url,splash_took,title,title_flg,title_length,title_md5,title_simhash,twitter_description_length,twitter_markup_flag,twitter_markup_length,url_length,valid_twitter_card,viewport_content,viewport_flag,week_of_year,crawl_timestamp,change_tracking_hash_cd_json,alternate_links_chg_ind,amphtml_flag_chg_ind,amphtml_href_chg_ind,analyzed_url_flg_s_chg_ind,analyzed_url_s_chg_ind,archive_flg_chg_ind,archive_flg_x_tag_chg_ind,blocked_by_robots_chg_ind,canonical_chg_ind,canonical_flg_chg_ind,canonical_header_flag_chg_ind,canonical_header_type_chg_ind,canonical_type_chg_ind,canonical_url_is_consistent_chg_ind,content_type_chg_ind,description_chg_ind,description_flg_chg_ind,description_length_chg_ind,description_simhash_chg_ind,error_message_chg_ind,final_response_code_chg_ind,follow_flg_chg_ind,follow_flg_x_tag_chg_ind,h1_chg_ind,h1_count_chg_ind,h1_flg_chg_ind,h1_length_chg_ind,h1_md5_chg_ind,header_noarchive_chg_ind,header_nofollow_chg_ind,header_noindex_chg_ind,header_noodp_chg_ind,header_nosnippet_chg_ind,header_noydir_chg_ind,hreflang_errors_chg_ind,hreflang_links_chg_ind,hreflang_links_out_count_chg_ind,hreflang_url_count_chg_ind,index_flg_chg_ind,index_flg_x_tag_chg_ind,indexable_chg_ind,insecure_resources_chg_ind,insecure_resources_flag_chg_ind,meta_charset_chg_ind,meta_content_type_chg_ind,meta_disabled_sitelinks_chg_ind,meta_noodp_chg_ind,meta_nosnippet_chg_ind,meta_noydir_chg_ind,meta_redirect_chg_ind,mixed_redirects_chg_ind,mobile_rel_alternate_url_is_consistent_chg_ind,noodp_chg_ind,nosnippet_chg_ind,noydir_chg_ind,og_markup_chg_ind,og_markup_flag_chg_ind,og_markup_length_chg_ind,outlink_count_chg_ind,page_link_chg_ind,redirect_blocked_chg_ind,redirect_blocked_reason_chg_ind,redirect_chain_chg_ind,redirect_final_url_chg_ind,redirect_flg_chg_ind,redirect_times_chg_ind,response_code_chg_ind,robots_chg_ind,robots_contents_chg_ind,robots_contents_x_tag_chg_ind,robots_flg_chg_ind,robots_flg_x_tag_chg_ind,structured_data_chg_ind,title_chg_ind,title_flg_chg_ind,title_length_chg_ind,title_md5_chg_ind,title_simhash_chg_ind,viewport_content_chg_ind,viewport_flag_chg_ind,page_analysis_results_chg_ind_json,page_analysis_results,canonical_added_ind,canonical_removed_ind,description_added_ind,description_removed_ind,h1_added_ind,h1_removed_ind,h2_added_ind,h2_chg_ind,h2_removed_ind,hreflang_links_added_ind,hreflang_links_removed_ind,open_graph_added_ind,open_graph_removed_ind,redirect_301_chg_ind,redirect_301_detected_ind,redirect_301_removed_ind,redirect_302_chg_ind,redirect_302_detected_ind,redirect_302_removed_ind,redirect_diff_code_ind,robots_added_ind,robots_removed_ind,title_added_ind,title_removed_ind,viewport_added_ind,viewport_removed_ind,internal_link_count,custom_data_chg_ind,page_analysis_results_reverse,base_tag,base_tag_flag,base_tag_target,base_tag_chg_ind,base_tag_flag_chg_ind,base_tag_target_chg_ind,base_tag_added_ind,base_tag_removed_ind,page_analysis_fragments_chg_ind,custom_data_added_ind,custom_data_removed_ind,response_headers_added_ind,response_headers_removed_ind,daily_data_creation_date");
				stringBuilder.append(" )");
				stringBuilder.append(" select");
				stringBuilder.append(
						"  domain_id,url,track_date,amphtml_flag,amphtml_href,analyzed_url_flg_s,analyzed_url_s,archive_flg,archive_flg_x_tag,blocked_by_robots,canonical,canonical_flg,canonical_header_flag,canonical_header_type,canonical_type,canonical_url_is_consistent,content_type,count_of_objects,description,description_flg,description_length,description_simhash,document_size,download_latency,download_time,error_message,final_response_code,folder_level_1,folder_level_2,folder_level_3,folder_level_count,follow_flg,follow_flg_x_tag,h1,h1_count,h1_flg,h1_length,h1_md5,h1_simhash,h2,h2_simhash,header_noarchive,header_nofollow,header_noindex,header_noodp,header_nosnippet,header_noydir,hreflang_links_out_count,hreflang_url_count,index_flg,index_flg_x_tag,indexable,insecure_resources_flag,long_redirect,meta_charset,meta_content_type,meta_disabled_sitelinks,meta_noodp,meta_nosnippet,meta_noydir,meta_redirect,mixed_redirects,mobile_rel_alternate_url_is_consistent,noodp,nosnippet,noydir,og_markup_flag,og_markup_length,outlink_count,page_1,page_timeout_flag,paginated,protocol,redirect_blocked,redirect_blocked_reason,redirect_final_url,redirect_flg,redirect_times,rel_next_html_url,rel_next_url_is_consistent,rel_prev_url_is_consistent,request_headers,request_time,response_code,retry_attempted,robots,robots_contents,robots_contents_x_tag,robots_flg,robots_flg_x_tag,server_response_time,source_url,splash_took,title,title_flg,title_length,title_md5,title_simhash,twitter_description_length,twitter_markup_flag,twitter_markup_length,url_length,valid_twitter_card,viewport_content,viewport_flag,week_of_year,crawl_timestamp,change_tracking_hash_cd_json,alternate_links_chg_ind,amphtml_flag_chg_ind,amphtml_href_chg_ind,analyzed_url_flg_s_chg_ind,analyzed_url_s_chg_ind,archive_flg_chg_ind,archive_flg_x_tag_chg_ind,blocked_by_robots_chg_ind,canonical_chg_ind,canonical_flg_chg_ind,canonical_header_flag_chg_ind,canonical_header_type_chg_ind,canonical_type_chg_ind,canonical_url_is_consistent_chg_ind,content_type_chg_ind,description_chg_ind,description_flg_chg_ind,description_length_chg_ind,description_simhash_chg_ind,error_message_chg_ind,final_response_code_chg_ind,follow_flg_chg_ind,follow_flg_x_tag_chg_ind,h1_chg_ind,h1_count_chg_ind,h1_flg_chg_ind,h1_length_chg_ind,h1_md5_chg_ind,header_noarchive_chg_ind,header_nofollow_chg_ind,header_noindex_chg_ind,header_noodp_chg_ind,header_nosnippet_chg_ind,header_noydir_chg_ind,hreflang_errors_chg_ind,hreflang_links_chg_ind,hreflang_links_out_count_chg_ind,hreflang_url_count_chg_ind,index_flg_chg_ind,index_flg_x_tag_chg_ind,indexable_chg_ind,insecure_resources_chg_ind,insecure_resources_flag_chg_ind,meta_charset_chg_ind,meta_content_type_chg_ind,meta_disabled_sitelinks_chg_ind,meta_noodp_chg_ind,meta_nosnippet_chg_ind,meta_noydir_chg_ind,meta_redirect_chg_ind,mixed_redirects_chg_ind,mobile_rel_alternate_url_is_consistent_chg_ind,noodp_chg_ind,nosnippet_chg_ind,noydir_chg_ind,og_markup_chg_ind,og_markup_flag_chg_ind,og_markup_length_chg_ind,outlink_count_chg_ind,page_link_chg_ind,redirect_blocked_chg_ind,redirect_blocked_reason_chg_ind,redirect_chain_chg_ind,redirect_final_url_chg_ind,redirect_flg_chg_ind,redirect_times_chg_ind,response_code_chg_ind,robots_chg_ind,robots_contents_chg_ind,robots_contents_x_tag_chg_ind,robots_flg_chg_ind,robots_flg_x_tag_chg_ind,structured_data_chg_ind,title_chg_ind,title_flg_chg_ind,title_length_chg_ind,title_md5_chg_ind,title_simhash_chg_ind,viewport_content_chg_ind,viewport_flag_chg_ind,page_analysis_results_chg_ind_json,page_analysis_results,canonical_added_ind,canonical_removed_ind,description_added_ind,description_removed_ind,h1_added_ind,h1_removed_ind,h2_added_ind,h2_chg_ind,h2_removed_ind,hreflang_links_added_ind,hreflang_links_removed_ind,open_graph_added_ind,open_graph_removed_ind,redirect_301_chg_ind,redirect_301_detected_ind,redirect_301_removed_ind,redirect_302_chg_ind,redirect_302_detected_ind,redirect_302_removed_ind,redirect_diff_code_ind,robots_added_ind,robots_removed_ind,title_added_ind,title_removed_ind,viewport_added_ind,viewport_removed_ind,internal_link_count,custom_data_chg_ind,page_analysis_results_reverse,base_tag,base_tag_flag,base_tag_target,base_tag_chg_ind,base_tag_flag_chg_ind,base_tag_target_chg_ind,base_tag_added_ind,base_tag_removed_ind,page_analysis_fragments_chg_ind,custom_data_added_ind,custom_data_removed_ind,response_headers_added_ind,response_headers_removed_ind,'" + dailyDataCreationDate + "' as daily_data_creation_date");
				stringBuilder.append(" from");
				stringBuilder.append(" " + sourceTableName);
				stringBuilder.append(" where domain_id =" + domainId + " and track_date >= '" + minTrackDate + "' and track_date < '" + maxTrackDate + "'");
				stringBuilder.append(" and (");
				stringBuilder.append("       url_hash,");
				stringBuilder.append("       url_murmur_hash,");
				stringBuilder.append("       crawl_timestamp");
				stringBuilder.append(" ) global in");
				stringBuilder.append("    (");
				stringBuilder.append("    select");
				stringBuilder.append("      url_hash,");
				stringBuilder.append("      url_murmur_hash,");
				stringBuilder.append("      last_changed_crawl_timestamp ");
				stringBuilder.append("    from dis_url_last_changed_timestamp");
				stringBuilder.append("    where");
				stringBuilder.append("      domain_id = " + domainId + " and url_type = 1 and track_date = '" + dailyDataCreationDate + "'");
				stringBuilder.append(" ) and (");
				stringBuilder.append("       url_hash,");
				stringBuilder.append("       url_murmur_hash");
				stringBuilder.append(" ) global in");
				stringBuilder.append("    (");
				stringBuilder.append("    select");
				stringBuilder.append("      url_hash,");
				stringBuilder.append("      url_murmur_hash");
				stringBuilder.append("    from");
				stringBuilder.append("      dis_target_url");
				stringBuilder.append("    where");
				stringBuilder.append("      domain_id = " + domainId);
				stringBuilder.append("   )");
				stringBuilder.append("and (url_hash,  url_murmur_hash ) global not in(select url_hash,  url_murmur_hash ");
				stringBuilder.append(" from dis_target_url_html_daily where domain_id = " + domainId + " and daily_data_creation_date = '" + dailyDataCreationDate + "' )");


				sqlString = stringBuilder.toString();
				//if (isDebug == true) {
//                  FormatUtils.getInstance().logMemoryUsage("createDailyDataFromHistoricalData() sqlString=" + sqlString);
				//}
				if (i == 0) {
					log.info("OID:{}, sqlString={}", domainId, sqlString.substring(sqlString.length() - 765));
				}
				connection.createStatement().execute(sqlString);
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					log.error("reach maximum retry count. OID: {}, TrackDate:{} - {}", domainId, minTrackDate, maxTrackDate);
					throw e;
				} else {
					log.error("createDailyDataFromHistoricalData() exception, OID: {} retryCount={},TrackDate:{} - {}, exception message={}", domainId, retryCount, minTrackDate, maxTrackDate, e.getMessage());
//                  FormatUtils.getInstance().logMemoryUsage("createDailyDataFromHistoricalData() exception message=" + e.getMessage() + ",retryCount=" + retryCount + ",sqlString=" + sqlString);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		log.info("createDailyDataFromHistoricalData() ends. OId:{}, TrackDate:{} - {},Seconds={}", domainId, minTrackDate, maxTrackDate, (System.currentTimeMillis() - startTimestamp) / 1000);
	}

}