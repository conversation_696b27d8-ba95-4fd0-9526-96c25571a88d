package com.actonia.dao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import ru.yandex.clickhouse.ClickHouseDataSource;
import ru.yandex.clickhouse.settings.ClickHouseProperties;

import com.actonia.IConstants;
import com.actonia.utils.FormatUtils;

// Due to ClickHouse concurrency issues, use Singleton pattern, do not use Spring (ie. all methods must be 'synchronized')
public class RankingDetailClickHouseDAO {

	//private static boolean isDebug = false;
	private static List<String> databaseHostnameList;
	private static String databasePort = null;
	private static String databaseName = null;
	private static List<Connection> connectionList;
	private static int batchCreationSize;
	private static String databaseUser = null;
	private static String databasePassword = null;
	private static List<String> connectionUrlList = null;
	private static int connectionTimeoutInMilliseconds;
	private static int maximumRetryCounts;
	private static int retryWaitMilliseconds;

	private final String MOBILE_TABLE_NAME_PREFIX = "m_";
	private final String DESKTOP_TABLE_NAME_PREFIX = "d_";
	public static final String TABLE_NAME_ENGLISH = "ranking_detail_us";
	public static final String TABLE_NAME_NON_ENGLISH = "ranking_detail_intl";
	private static RankingDetailClickHouseDAO rankingDetailClickHouseDAO;

	public static RankingDetailClickHouseDAO getInstance() throws Exception {
		if (rankingDetailClickHouseDAO == null) {
			rankingDetailClickHouseDAO = initialize();
		}
		return rankingDetailClickHouseDAO;
	}

	private static RankingDetailClickHouseDAO initialize() throws Exception {
		FormatUtils.getInstance().logMemoryUsage("initialize() begins.");

		RankingDetailClickHouseDAO rankingDetailClickHouseDAO = null;

		String clickHouseDatabaseHostnames = ClickHouseSeoDailyRankingDatabaseConfigurations
				.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_HOSTNAMES);
		String[] clickHouseDatabaseHostnameArray = clickHouseDatabaseHostnames.split(IConstants.COMMA);
		String clickHouseDatabasePort = ClickHouseSeoDailyRankingDatabaseConfigurations
				.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_PORT);
		String clickHouseDatabaseName = ClickHouseSeoDailyRankingDatabaseConfigurations
				.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_NAME);
		String clickHouseDatabaseUser = ClickHouseSeoDailyRankingDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_USER);
		String clickHouseDatabasePassword = ClickHouseSeoDailyRankingDatabaseConfigurations
				.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_PASSWORD);
		int clickHouseBatchCreationSize = ClickHouseSeoDailyRankingDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_BATCH_CREATION_SIZE, 100);
		int clickHouseconnectionTimeoutInMilliseconds = ClickHouseSeoDailyRankingDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_CONNECTION_TIMEOUT_MILLISECONDS, 88888);
		int clickHouseMaximumRetryCounts = ClickHouseSeoDailyRankingDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_MAX_RETRY_COUNTS, 8);
		int clickHouseRetryWaitMilliseconds = ClickHouseSeoDailyRankingDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_RETRY_WAIT_MILLISECONDS, 1000);

		try {
			rankingDetailClickHouseDAO = new RankingDetailClickHouseDAO(clickHouseDatabaseHostnameArray, clickHouseDatabasePort, clickHouseDatabaseName,
					clickHouseBatchCreationSize, clickHouseDatabaseUser, clickHouseDatabasePassword, clickHouseconnectionTimeoutInMilliseconds,
					clickHouseMaximumRetryCounts, clickHouseRetryWaitMilliseconds);
		} catch (Exception e) {
			e.printStackTrace();
		}

		FormatUtils.getInstance().logMemoryUsage("initialize() ends. rankingDetailClickHouseDAO=" + rankingDetailClickHouseDAO.toString());
		return rankingDetailClickHouseDAO;
	}

	private RankingDetailClickHouseDAO(String[] databaseHostnameArrayInput, String databasePortInput, String databaseNameInput, int batchCreationSizeInput,
			String databaseUserInput, String databasePasswordInput, int connectionTimeoutInMillisecondsInput, int maximumRetryCountsInput,
			int retryWaitMillisecondsInput) throws Exception {

		ClickHouseDataSource clickHouseDataSource = null;
		Connection connection = null;

		databaseHostnameList = Arrays.asList(databaseHostnameArrayInput);
		databasePort = databasePortInput;
		databaseName = databaseNameInput;
		batchCreationSize = batchCreationSizeInput;
		databaseUser = databaseUserInput;
		databasePassword = databasePasswordInput;
		connectionTimeoutInMilliseconds = connectionTimeoutInMillisecondsInput;
		maximumRetryCounts = maximumRetryCountsInput;
		retryWaitMilliseconds = retryWaitMillisecondsInput;

		FormatUtils.getInstance()
				.logMemoryUsage("RankingDetailClickHouseDAO() databaseHostnameList=" + databaseHostnameList.toString() + ",databasePort=" + databasePort
						+ ",databaseName=" + databaseName + ",batchCreationSize=" + batchCreationSize + ",databaseUser=" + databaseUser + ",databasePassword="
						+ databasePassword + ",connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ",maximumRetryCounts=" + maximumRetryCounts
						+ ",retryWaitMilliseconds=" + retryWaitMilliseconds);

		ClickHouseProperties clickHouseProperties = new ClickHouseProperties();
		clickHouseProperties.setDecompress(true);
		clickHouseProperties.setConnectionTimeout(connectionTimeoutInMilliseconds);
		clickHouseProperties.setSocketTimeout(connectionTimeoutInMilliseconds);

		connectionList = new ArrayList<Connection>();

		String connectionUrl = null;

		connectionUrlList = new ArrayList<String>();

		for (String databaseHostname : databaseHostnameList) {
			if (StringUtils.isNotBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
						+ "?user=" + databaseUser + "&password=" + databasePassword;
			} else if (StringUtils.isBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
						+ "?password=" + databasePassword;
			} else {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH
						+ databaseName;
			}
			connectionUrlList.add(connectionUrl);
			clickHouseDataSource = new ClickHouseDataSource(connectionUrl, clickHouseProperties);
			connection = clickHouseDataSource.getConnection();
			connectionList.add(connection);
			FormatUtils.getInstance().logMemoryUsage("RankingDetailClickHouseDAO() database connection created...connectionUrl=" + connectionUrl);
		}
	}

	@Override
	public String toString() {
		return "RankingDetailClickHouseDAO [databaseHostnameList=" + databaseHostnameList.toString() + ", databasePort=" + databasePort + ", databaseName="
				+ databaseName + ", batchCreationSize=" + batchCreationSize + ", databaseUser=" + databaseUser + ", databasePassword=" + databasePassword
				+ ", connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ", maximumRetryCounts=" + maximumRetryCounts + ", retryWaitMilliseconds="
				+ retryWaitMilliseconds + "]";
	}

	public synchronized String getTableName(boolean isMobileDomain, int searchEngineId, int searchLanguageId, Date rankDate) {

		String rankDateYYYYMM = DateFormatUtils.format(rankDate, IConstants.DATE_FORMAT_YYYYMM);

		//  t_own_domain.mobile_domain_flg: 1: mobile domain, else: desktop domain
		String tableNamePrefix =  isMobileDomain ? MOBILE_TABLE_NAME_PREFIX : DESKTOP_TABLE_NAME_PREFIX;

		final String rankingDetailTableName;
		if (searchEngineId == 1 && searchLanguageId == 1) {
            rankingDetailTableName = tableNamePrefix + TABLE_NAME_ENGLISH + IConstants.UNDERSCORE + rankDateYYYYMM;
        } else {
			rankingDetailTableName = tableNamePrefix + TABLE_NAME_NON_ENGLISH + IConstants.UNDERSCORE + rankDateYYYYMM;
		}
		System.out.println("rankingDetailTableName=" + rankingDetailTableName);
		return rankingDetailTableName;
	}

	public synchronized int getBatchCreationSize() {
		return batchCreationSize;
	}

	public synchronized List<String> getUrlList(String ip, int domainId, int searchEngineId, int searchLanguageId, Date rankDate, String rootDomainReverse,
			String domainReverse) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("getUrlList() begins. ip=" + ip + ", domainId=" + domainId + ",searchEngineId=" + searchEngineId + ",searchLanguageId="
				+ searchLanguageId + ",rankDate=" + rankDate + ",rootDomainReverse=" + rootDomainReverse + ",domainReverse=" + domainReverse);
		List<String> urlList = new ArrayList<String>();
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		int index = 0;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     url");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(false, searchEngineId, searchLanguageId, rankDate));
		stringBuilder.append(" where");
		stringBuilder.append("     own_domain_id = ?");
		stringBuilder.append(" and engine_id = ?");
		stringBuilder.append(" and language_id = ?");
		stringBuilder.append(" and location_id = 0");
		stringBuilder.append(" and ranking_date = ?");
		stringBuilder.append(" and root_domain_reverse = ?");
		if (StringUtils.equalsIgnoreCase(rootDomainReverse, domainReverse) == false) {
			stringBuilder.append(" and domain_reverse = ?");
		}
		stringBuilder.append(" and hrrd = 1");

		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getUrlList() sqlString=" + sqlString);
		connection = connectionList.get(0);
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setInt(index++, searchEngineId);
				preparedStatement.setInt(index++, searchLanguageId);
				preparedStatement.setDate(index++, new java.sql.Date(rankDate.getTime()));
				preparedStatement.setString(index++, rootDomainReverse);
				if (StringUtils.equalsIgnoreCase(rootDomainReverse, domainReverse) == false) {
					preparedStatement.setString(index++, domainReverse);
				}
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {

					// url
					urlList.add(resultSet.getString(IConstants.URL));
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getUrlList() e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					if (e.getMessage() == null) {
						initialize();
					} else {
						try {
							Thread.sleep(retryWaitMilliseconds);
						} catch (InterruptedException e1) {
							e1.printStackTrace();
						}
					}
				}
			} finally {
				if (resultSet != null) {
					resultSet.close();
				}
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();
				}
			}
		}
		FormatUtils.getInstance()
				.logMemoryUsage("getUrlList() ends. ip=" + ip + ", domainId=" + domainId + ",searchEngineId=" + searchEngineId + ",searchLanguageId=" + searchLanguageId
						+ ",rankDate=" + rankDate + ",rootDomainReverse=" + rootDomainReverse + ",domainReverse=" + domainReverse + ",urlList.size()=" + urlList.size()
						+ ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return urlList;
	}

	public synchronized List<String> getUrlList(String ip, int domainId, boolean isMobileDomain, int searchEngineId, int searchLanguageId, Date rankDate, int topxRankingUrl, String rootDomainReverse,
	                                            String domainReverse) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("getUrlList() begins. ip=" + ip + ", domainId=" + domainId + ",searchEngineId=" + searchEngineId + ",searchLanguageId="
				+ searchLanguageId + ",rankDate=" + rankDate + ",rootDomainReverse=" + rootDomainReverse + ",domainReverse=" + domainReverse);
		List<String> urlList = new ArrayList<String>();
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		int index = 0;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     url");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(isMobileDomain, searchEngineId, searchLanguageId, rankDate));
		stringBuilder.append(" where");
		stringBuilder.append("     own_domain_id = ?");
		stringBuilder.append(" and engine_id = ?");
		stringBuilder.append(" and language_id = ?");
		stringBuilder.append(" and location_id = 0");
		stringBuilder.append(" and ranking_date = ?");
		stringBuilder.append(" and true_rank > 0 and true_rank <= ? ");
		stringBuilder.append(" and root_domain_reverse = ?");
		if (StringUtils.equalsIgnoreCase(rootDomainReverse, domainReverse) == false) {
			stringBuilder.append(" and domain_reverse = ?");
		}
		stringBuilder.append(" and hrrd = 1");

		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getUrlList() sqlString=" + sqlString);
		connection = connectionList.get(0);
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setInt(index++, searchEngineId);
				preparedStatement.setInt(index++, searchLanguageId);
				preparedStatement.setDate(index++, new java.sql.Date(rankDate.getTime()));
				preparedStatement.setInt(index++, topxRankingUrl);
				preparedStatement.setString(index++, rootDomainReverse);
				if (StringUtils.equalsIgnoreCase(rootDomainReverse, domainReverse) == false) {
					preparedStatement.setString(index++, domainReverse);
				}
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {

					// url
					urlList.add(resultSet.getString(IConstants.URL));
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getUrlList() e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					if (e.getMessage() == null) {
						initialize();
					} else {
						try {
							Thread.sleep(retryWaitMilliseconds);
						} catch (InterruptedException e1) {
							e1.printStackTrace();
						}
					}
				}
			} finally {
				if (resultSet != null) {
					resultSet.close();
				}
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();
				}
			}
		}
		FormatUtils.getInstance()
				.logMemoryUsage("getUrlList() ends. ip=" + ip + ", domainId=" + domainId + ",searchEngineId=" + searchEngineId + ",searchLanguageId=" + searchLanguageId
						+ ",rankDate=" + rankDate + ",rootDomainReverse=" + rootDomainReverse + ",domainReverse=" + domainReverse + ",urlList.size()=" + urlList.size()
						+ ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return urlList;
	}


	public synchronized Map<Integer, Set<String>> getKeywordIdCompetitorUrlSetMap(boolean mobileDomainFlg, int domainId, int searchEngineId, int searchLanguageId, Date rankDate,
	                                                                              int topPos) throws Exception {

		Map<Integer, Set<String>> keywordIdCompetitorUrlSetMap = new HashMap<>();

		//long startTimestamp = System.currentTimeMillis();
		//if (isDebug == true) {
		//	FormatUtils.getInstance().logMemoryUsage("getKeywordIdCompetitorUrlSetMap() begins. ip=" + ip + ", domainId=" + domainId + ",searchEngineId="
		//			+ searchEngineId + ",searchLanguageId=" + searchLanguageId + ",rankDate=" + rankDate);
		//}
		Set<String> competitorUrlSet = new HashSet<String>();
		int keywordRankcheckId = 0;
		String url = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		int index = 0;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     keyword_rankcheck_id,");
		stringBuilder.append("     url");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(mobileDomainFlg, searchEngineId, searchLanguageId, rankDate));
		stringBuilder.append(" where");
		stringBuilder.append("     own_domain_id = ?");
		stringBuilder.append(" and engine_id = ?");
		stringBuilder.append(" and language_id = ?");
		stringBuilder.append(" and location_id = 0");
		stringBuilder.append(" and ranking_date = ?");
		stringBuilder.append(" and web_rank <= ?");

		String sqlString = stringBuilder.toString();
		//if (isDebug == true) {
		//	FormatUtils.getInstance().logMemoryUsage("getKeywordIdCompetitorUrlSetMap() sqlString=" + sqlString);
		//}
		connection = connectionList.get(0);
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setInt(index++, searchEngineId);
				preparedStatement.setInt(index++, searchLanguageId);
				preparedStatement.setDate(index++, new java.sql.Date(rankDate.getTime()));
				preparedStatement.setInt(index++, topPos);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {

					// keyword_rankcheck_id
					keywordRankcheckId = resultSet.getInt(IConstants.KEYWORD_RANKCHECK_ID);

					// url
					url = resultSet.getString(IConstants.URL);

					if (keywordIdCompetitorUrlSetMap.containsKey(keywordRankcheckId)) {
						competitorUrlSet = keywordIdCompetitorUrlSetMap.get(keywordRankcheckId);
					} else {
						competitorUrlSet = new HashSet<String>();
					}
					competitorUrlSet.add(url);
					keywordIdCompetitorUrlSetMap.put(keywordRankcheckId, competitorUrlSet);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getKeywordIdCompetitorUrlSetMap() e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					if (e.getMessage() == null) {
						initialize();
					} else {
						try {
							Thread.sleep(retryWaitMilliseconds);
						} catch (InterruptedException e1) {
							e1.printStackTrace();
						}
					}
				}
			} finally {
				if (resultSet != null) {
					resultSet.close();
				}
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();
				}
			}
		}
		//if (isDebug == true) {
		//	FormatUtils.getInstance()
		//			.logMemoryUsage("getKeywordIdCompetitorUrlSetMap() ends. ip=" + ip + ", domainId=" + domainId + ",searchEngineId=" + searchEngineId
		//					+ ",searchLanguageId=" + searchLanguageId + ",rankDate=" + rankDate + ",keywordIdCompetitorUrlSetMap.size()="
		//					+ keywordIdCompetitorUrlSetMap.size() + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		//}
		return keywordIdCompetitorUrlSetMap;
	}
}
