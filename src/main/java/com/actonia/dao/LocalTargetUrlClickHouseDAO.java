package com.actonia.dao;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.lang.StringUtils;

import com.actonia.IConstants;
import com.actonia.utils.FormatUtils;

import ru.yandex.clickhouse.ClickHouseDataSource;
import ru.yandex.clickhouse.settings.ClickHouseProperties;

// Due to ClickHouse concurrency issues, use Singleton pattern, do not use Spring (ie. all methods must be 'synchronized')
public class LocalTargetUrlClickHouseDAO {

	//private static boolean isDebug = false;
	private static List<String> databaseHostnameList;
	private static String databasePort = null;
	private static String databaseName = null;
	private static List<Connection> connectionList;
	private static int batchCreationSize;
	private static String databaseUser = null;
	private static String databasePassword = null;
	private static List<String> connectionUrlList = null;
	private static int connectionTimeoutInMilliseconds;
	private static int maximumRetryCounts;
	private static int retryWaitMilliseconds;
	public static final String TABLE_NAME = "local_target_url";
	private static LocalTargetUrlClickHouseDAO targetUrlClickHouseDAO;

	public static LocalTargetUrlClickHouseDAO getInstance() throws Exception {
		if (targetUrlClickHouseDAO == null) {
			targetUrlClickHouseDAO = initialize();
		}
		return targetUrlClickHouseDAO;
	}

	private static LocalTargetUrlClickHouseDAO initialize() throws Exception {
		FormatUtils.getInstance().logMemoryUsage("initialize() begins.");

		LocalTargetUrlClickHouseDAO targetUrlClickHouseDAO = null;

		String clickHouseDatabaseHostnames = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CLICKHOUSE_CLUSTER_SERVERS);
		String[] clickHouseDatabaseHostnameArray = clickHouseDatabaseHostnames.split(IConstants.COMMA);
		String clickHouseDatabasePort = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_PORT);
		String clickHouseDatabaseName = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_NAME);
		String clickHouseDatabaseUser = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_USER);
		String clickHouseDatabasePassword = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_PASSWORD);
		int clickHouseBatchCreationSize = ClickHouseCrawlDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_BATCH_CREATION_SIZE, 100);
		int clickHouseconnectionTimeoutInMilliseconds = ClickHouseCrawlDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_CONNECTION_TIMEOUT_MILLISECONDS, 88888);
		int clickHouseMaximumRetryCounts = ClickHouseCrawlDatabaseConfigurations.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_MAX_RETRY_COUNTS,
				8);
		int clickHouseRetryWaitMilliseconds = ClickHouseCrawlDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_RETRY_WAIT_MILLISECONDS, 1000);

		try {
			targetUrlClickHouseDAO = new LocalTargetUrlClickHouseDAO(clickHouseDatabaseHostnameArray, clickHouseDatabasePort, clickHouseDatabaseName,
					clickHouseBatchCreationSize, clickHouseDatabaseUser, clickHouseDatabasePassword, clickHouseconnectionTimeoutInMilliseconds,
					clickHouseMaximumRetryCounts, clickHouseRetryWaitMilliseconds);
		} catch (Exception e) {
			e.printStackTrace();
		}

		FormatUtils.getInstance().logMemoryUsage("initialize() ends. targetUrlClickHouseDAO=" + targetUrlClickHouseDAO.toString());
		return targetUrlClickHouseDAO;
	}

	private LocalTargetUrlClickHouseDAO(String[] databaseHostnameArrayInput, String databasePortInput, String databaseNameInput, int batchCreationSizeInput,
			String databaseUserInput, String databasePasswordInput, int connectionTimeoutInMillisecondsInput, int maximumRetryCountsInput,
			int retryWaitMillisecondsInput) throws Exception {

		ClickHouseDataSource clickHouseDataSource = null;
		Connection connection = null;

		databaseHostnameList = Arrays.asList(databaseHostnameArrayInput);
		databasePort = databasePortInput;
		databaseName = databaseNameInput;
		batchCreationSize = batchCreationSizeInput;
		databaseUser = databaseUserInput;
		databasePassword = databasePasswordInput;
		connectionTimeoutInMilliseconds = connectionTimeoutInMillisecondsInput;
		maximumRetryCounts = maximumRetryCountsInput;
		retryWaitMilliseconds = retryWaitMillisecondsInput;

		FormatUtils.getInstance()
				.logMemoryUsage("TargetUrlClickHouseDAO() databaseHostnameList=" + databaseHostnameList.toString() + ",databasePort=" + databasePort + ",databaseName="
						+ databaseName + ",batchCreationSize=" + batchCreationSize + ",databaseUser=" + databaseUser + ",databasePassword=" + databasePassword
						+ ",connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ",maximumRetryCounts=" + maximumRetryCounts
						+ ",retryWaitMilliseconds=" + retryWaitMilliseconds + ",TABLE_NAME=" + TABLE_NAME);

		ClickHouseProperties clickHouseProperties = new ClickHouseProperties();
		clickHouseProperties.setDecompress(true);
		clickHouseProperties.setConnectionTimeout(connectionTimeoutInMilliseconds);
		clickHouseProperties.setSocketTimeout(connectionTimeoutInMilliseconds);

		connectionList = new ArrayList<Connection>();

		String connectionUrl = null;

		connectionUrlList = new ArrayList<String>();

		for (String databaseHostname : databaseHostnameList) {
			if (StringUtils.isNotBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
						+ "?user=" + databaseUser + "&password=" + databasePassword;
			} else if (StringUtils.isBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
						+ "?password=" + databasePassword;
			} else {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH
						+ databaseName;
			}
			connectionUrlList.add(connectionUrl);
			clickHouseDataSource = new ClickHouseDataSource(connectionUrl, clickHouseProperties);
			connection = clickHouseDataSource.getConnection();
			connectionList.add(connection);
			FormatUtils.getInstance().logMemoryUsage("TargetUrlClickHouseDAO() database connection created...connectionUrl=" + connectionUrl);
		}
	}

	@Override
	public String toString() {
		return "TargetUrlClickHouseDAO [databaseHostnameList=" + databaseHostnameList.toString() + ", databasePort=" + databasePort + ", databaseName=" + databaseName
				+ ", batchCreationSize=" + batchCreationSize + ", databaseUser=" + databaseUser + ", databasePassword=" + databasePassword
				+ ", connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ", maximumRetryCounts=" + maximumRetryCounts + ", retryWaitMilliseconds="
				+ retryWaitMilliseconds + "]";
	}

	//public synchronized String getTableName() {
	//	return getTableName(null);
	//}

	public synchronized String getTableName(String tableName) {
		if (StringUtils.isNotBlank(tableName)) {
			return tableName;
		} else {
			return TABLE_NAME;
		}
	}

	public synchronized int getBatchCreationSize() {
		return batchCreationSize;
	}

	public void resetTable(String tableName) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("resetTable() begins.");
		String sqlString = null;
		Connection connection = null;
		String connectionUrl = null;
		int retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				for (int i = 0; i < connectionList.size(); i++) {
					connection = connectionList.get(i);
					connectionUrl = connectionUrlList.get(i);
					FormatUtils.getInstance().logMemoryUsage("resetTable() connectionUrl=" + connectionUrl);
					sqlString = "truncate table if exists crawl." + getTableName(tableName);
					FormatUtils.getInstance().logMemoryUsage("resetTable() sqlString=" + sqlString);
					connection.createStatement().execute(sqlString);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("resetTable() e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}

		FormatUtils.getInstance().logMemoryUsage("resetTable() ends.");
	}
}
