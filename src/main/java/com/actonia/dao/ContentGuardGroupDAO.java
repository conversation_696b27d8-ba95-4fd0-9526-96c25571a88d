package com.actonia.dao;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.StringUtils;

import com.actonia.entity.ContentGuardGroupEntity;

public class ContentGuardGroupDAO extends BaseJdbcSupport<ContentGuardGroupEntity> {

	@Override
	public String getTableName() {
		return "content_guard_group";
	}

	public ContentGuardGroupEntity get(int domainId, Long groupId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     *");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     id = ?");
		stringBuilder.append(" and domain_id = ?");
		String sqlString = stringBuilder.toString();
		return this.findObject(sqlString, groupId, domainId);
	}

	public int getTotalUrls(int domainId, Long groupId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     count(*)");
		stringBuilder.append(" from");
		stringBuilder.append("     content_guard_group content_guard_group,");
		stringBuilder.append("     content_guard_url content_guard_url");
		stringBuilder.append(" where");
		stringBuilder.append("     content_guard_group.id = ?");
		stringBuilder.append(" and content_guard_group.domain_id = ?");
		stringBuilder.append(" and content_guard_group.domain_id = content_guard_url.domain_id");
		stringBuilder.append(" and content_guard_group.id = content_guard_url.group_id");
		String sqlString = stringBuilder.toString();
		return this.queryForInt(sqlString, groupId, domainId);
	}

	public List<Integer> getDomainIdsByCrawlFrequency(int crawlFrequencyType) {
		List<Integer> uniqueDomainIdList = new ArrayList<Integer>();
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select distinct");
		stringBuilder.append("     domain_id");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     crawl_frequency_type = ?");
		String sqlString = stringBuilder.toString();
		List<ContentGuardGroupEntity> contentGuardGroupEntityList = this.findBySql(sqlString, crawlFrequencyType);
		if (contentGuardGroupEntityList != null && contentGuardGroupEntityList.size() > 0) {
			for (ContentGuardGroupEntity contentGuardGroupEntity : contentGuardGroupEntityList) {
				uniqueDomainIdList.add(contentGuardGroupEntity.getDomainId());
			}
		}
		return uniqueDomainIdList;
	}

	public List<ContentGuardGroupEntity> getTrackedGroupUrlList(int domainId, Long groupId, String filterUrl) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     content_guard_group.id,");
		stringBuilder.append("     content_guard_group.group_name,");
		stringBuilder.append("     content_guard_group.crawl_frequency_type,");
		stringBuilder.append("     content_guard_url.url as url");
		stringBuilder.append(" from");
		stringBuilder.append("     content_guard_group content_guard_group,");
		stringBuilder.append("     content_guard_url content_guard_url");
		stringBuilder.append(" where");
		stringBuilder.append("     content_guard_group.id = ?");
		stringBuilder.append(" and content_guard_group.domain_id = ?");
		stringBuilder.append(" and content_guard_url.domain_id = content_guard_group.domain_id");
		stringBuilder.append(" and content_guard_url.group_id = content_guard_group.id");
		if (StringUtils.isNotBlank(filterUrl)) {
			stringBuilder.append(" and content_guard_url.url = ?");
		}
		String sqlString = stringBuilder.toString();

		if (StringUtils.isNotBlank(filterUrl)) {
			return this.findBySql(sqlString, groupId, domainId, filterUrl);
		} else {
			return this.findBySql(sqlString, groupId, domainId);
		}
	}
}