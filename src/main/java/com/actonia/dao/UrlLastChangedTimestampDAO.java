package com.actonia.dao;

import com.actonia.IConstants;
import com.actonia.entity.UrlLastChangedTimestampEntity;
import com.actonia.utils.FormatUtils;
import org.apache.commons.lang.StringUtils;
import ru.yandex.clickhouse.ClickHouseDataSource;
import ru.yandex.clickhouse.settings.ClickHouseProperties;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.*;

// Due to ClickHouse concurrency issues, use Singleton pattern, do not use Spring (ie. all methods must be 'synchronized')
public class UrlLastChangedTimestampDAO {

    //private static boolean isDebug = false;
    public static final String TABLE_NAME = "dis_url_last_changed_timestamp";
    //public static final String TABLE_NAME = "unit_test_target_url_html";
    private static final int TOTAL_DAO_INSTANCES = 1;
    private static int daoMapIndex = 0;

    // map key = DAO index (0  - 59)
    // map value = instance of UrlLastChangedTimestampDAO
    private static Map<Integer, UrlLastChangedTimestampDAO> urlLastChangedTimestampDAOMap;

    private final List<String> databaseHostnameList;
    private final String databasePort;
    private final String databaseName;
    private final List<Connection> connectionList;
    private final int batchCreationSize;
    private final int connectionTimeoutInMilliseconds;
    private final int maximumRetryCounts;
    private final int retryWaitMilliseconds;
    private final int daoIndex;
    private String databaseUser = null;
    private String databasePassword = null;

    private UrlLastChangedTimestampDAO(String[] databaseHostnameArrayInput, String databasePortInput, String databaseNameInput, int batchCreationSizeInput,
                                       String databaseUserInput, String databasePasswordInput, int connectionTimeoutInMillisecondsInput, int maximumRetryCountsInput,
                                       int retryWaitMillisecondsInput, int index) throws Exception {

        ClickHouseDataSource clickHouseDataSource = null;
        Connection connection = null;

        databaseHostnameList = Arrays.asList(databaseHostnameArrayInput);
        databasePort = databasePortInput;
        databaseName = databaseNameInput;
        batchCreationSize = batchCreationSizeInput;
        databaseUser = databaseUserInput;
        databasePassword = databasePasswordInput;
        connectionTimeoutInMilliseconds = connectionTimeoutInMillisecondsInput;
        maximumRetryCounts = maximumRetryCountsInput;
        retryWaitMilliseconds = retryWaitMillisecondsInput;
        daoIndex = index;

        ClickHouseProperties clickHouseProperties = new ClickHouseProperties();
        clickHouseProperties.setDecompress(true);
        clickHouseProperties.setConnectionTimeout(connectionTimeoutInMilliseconds);
        clickHouseProperties.setSocketTimeout(connectionTimeoutInMilliseconds);

        connectionList = new ArrayList<>();

        String connectionUrl = null;

        List<String> connectionUrlList = new ArrayList<>();

        for (String databaseHostname : databaseHostnameList) {
            if (StringUtils.isNotBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
                connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
                        + "?user=" + databaseUser + "&password=" + databasePassword;
            } else if (StringUtils.isBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
                connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
                        + "?password=" + databasePassword;
            } else {
                connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH
                        + databaseName;
            }
            connectionUrlList.add(connectionUrl);
            //FormatUtils.getInstance().logMemoryUsage("UrlLastChangedTimestampDAO() connectionUrl=" + connectionUrl);
            clickHouseDataSource = new ClickHouseDataSource(connectionUrl, clickHouseProperties);
            connection = clickHouseDataSource.getConnection();
            connectionList.add(connection);
        }
    }

    public static UrlLastChangedTimestampDAO getInstance() throws Exception {
        UrlLastChangedTimestampDAO urlLastChangedTimestampDAO = null;
        String clickHouseDatabaseHostnames = null;
        String[] clickHouseDatabaseHostnameArray = null;
        String clickHouseDatabasePort = null;
        String clickHouseDatabaseName = null;
        String clickHouseDatabaseUser = null;
        String clickHouseDatabasePassword = null;
        int clickHouseBatchCreationSize = 0;
        int clickHouseconnectionTimeoutInMilliseconds = 0;
        int clickHouseMaximumRetryCounts = 0;
        int clickHouseRetryWaitMilliseconds = 0;
        if (urlLastChangedTimestampDAOMap == null) {
            clickHouseDatabaseHostnames = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_HOSTNAMES);
            clickHouseDatabaseHostnameArray = clickHouseDatabaseHostnames.split(IConstants.COMMA);
            clickHouseDatabasePort = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_PORT);
            clickHouseDatabaseName = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_NAME);
            clickHouseDatabaseUser = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_USER);
            clickHouseDatabasePassword = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_PASSWORD);
            clickHouseBatchCreationSize = ClickHouseCrawlDatabaseConfigurations
                    .getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_BATCH_CREATION_SIZE, 100);
            clickHouseconnectionTimeoutInMilliseconds = ClickHouseCrawlDatabaseConfigurations
                    .getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_CONNECTION_TIMEOUT_MILLISECONDS, 88888);
            clickHouseMaximumRetryCounts = ClickHouseCrawlDatabaseConfigurations.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_MAX_RETRY_COUNTS,
                    8);
            clickHouseRetryWaitMilliseconds = ClickHouseCrawlDatabaseConfigurations
                    .getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_RETRY_WAIT_MILLISECONDS, 1000);
            FormatUtils.getInstance().logMemoryUsage("UrlLastChangedTimestampDAO() clickHouseDatabaseHostnames=" + clickHouseDatabaseHostnames.toString()
                    + ",clickHouseDatabasePort=" + clickHouseDatabasePort + ",clickHouseDatabaseName=" + clickHouseDatabaseName + ",clickHouseBatchCreationSize="
                    + clickHouseBatchCreationSize + ",clickHouseDatabaseUser=" + clickHouseDatabaseUser + ",clickHouseDatabasePassword=" + clickHouseDatabasePassword
                    + ",clickHouseconnectionTimeoutInMilliseconds=" + clickHouseconnectionTimeoutInMilliseconds + ",clickHouseMaximumRetryCounts="
                    + clickHouseMaximumRetryCounts + ",clickHouseRetryWaitMilliseconds=" + clickHouseRetryWaitMilliseconds + ",TABLE_NAME=" + TABLE_NAME);

            urlLastChangedTimestampDAOMap = new HashMap<>();
            for (int i = 0; i < TOTAL_DAO_INSTANCES; i++) {
                urlLastChangedTimestampDAO = initialize(clickHouseDatabaseHostnameArray, clickHouseDatabasePort, clickHouseDatabaseName, clickHouseDatabaseUser,
                        clickHouseDatabasePassword, clickHouseBatchCreationSize, clickHouseconnectionTimeoutInMilliseconds, clickHouseMaximumRetryCounts,
                        clickHouseRetryWaitMilliseconds, i);
                urlLastChangedTimestampDAOMap.put(i, urlLastChangedTimestampDAO);
            }
            FormatUtils.getInstance().logMemoryUsage("getInstance() total urlLastChangedTimestampDAOs=" + urlLastChangedTimestampDAOMap.size());
        }
        int index = getDaoMapIndex();
        urlLastChangedTimestampDAO = urlLastChangedTimestampDAOMap.get(index);
        return urlLastChangedTimestampDAO;
    }

    private static synchronized int getDaoMapIndex() {
        int index = 0;
        if (daoMapIndex >= TOTAL_DAO_INSTANCES) {
            daoMapIndex = 0;
            index = 0;
        } else {
            index = daoMapIndex++;
        }
        return index;
    }
    // initialize UrlLastChangedTimestampDAO based on runtime clickhouse configurations

    private static UrlLastChangedTimestampDAO initialize(String[] clickHouseDatabaseHostnameArray, String clickHouseDatabasePort, String clickHouseDatabaseName,
                                                         String clickHouseDatabaseUser, String clickHouseDatabasePassword, int clickHouseBatchCreationSize, int clickHouseconnectionTimeoutInMilliseconds,
                                                         int clickHouseMaximumRetryCounts, int clickHouseRetryWaitMilliseconds, int daoIndex) throws Exception {
        UrlLastChangedTimestampDAO urlLastChangedTimestampDAO = new UrlLastChangedTimestampDAO(clickHouseDatabaseHostnameArray, clickHouseDatabasePort,
                clickHouseDatabaseName, clickHouseBatchCreationSize, clickHouseDatabaseUser, clickHouseDatabasePassword, clickHouseconnectionTimeoutInMilliseconds,
                clickHouseMaximumRetryCounts, clickHouseRetryWaitMilliseconds, daoIndex);
        //FormatUtils.getInstance().logMemoryUsage("initialize() urlLastChangedTimestampDAO=" + urlLastChangedTimestampDAO.toString());
        return urlLastChangedTimestampDAO;
    }

    @Override
    public String toString() {
        return "UrlLastChangedTimestampDAO [databaseHostnameList=" + databaseHostnameList.toString() + ", databasePort=" + databasePort + ", databaseName="
                + databaseName + ", batchCreationSize=" + batchCreationSize + ", databaseUser=" + databaseUser + ", databasePassword=" + databasePassword
                + ", connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ", maximumRetryCounts=" + maximumRetryCounts + ", retryWaitMilliseconds="
                + retryWaitMilliseconds + ", daoIndex=" + daoIndex + "]";
    }

    private synchronized String getTableName(String tableName) {
        if (StringUtils.isNotBlank(tableName)) {
            return tableName;
        } else {
            return TABLE_NAME;
        }
    }

    public synchronized int getBatchCreationSize() {
        return batchCreationSize;
    }

    public synchronized void createBatch(List<UrlLastChangedTimestampEntity> urlLastChangedTimestampEntityList, String tableName) throws Exception {
        //long startTimestamp = System.currentTimeMillis();

        PreparedStatement preparedStatement = null;
        int index = 0;
        Connection connection = null;

        String creationSqlStatement = "insert into " + getTableName(tableName) + " (track_date, url_type, domain_id, url_hash, url_murmur_hash, last_changed_crawl_timestamp) values (?, ?, ?, ?, ?, ?)";
        //FormatUtils.getInstance().logMemoryUsage("createBatch() creationSqlStatement=" + creationSqlStatement);

        Date nowDate = null;

        for (Connection value : connectionList) {
            connection = value;
            preparedStatement = connection.prepareStatement(creationSqlStatement);
            nextUrlLastChangedTimestamp:
            for (UrlLastChangedTimestampEntity urlLastChangedTimestampEntity : urlLastChangedTimestampEntityList) {

                index = 1;

                nowDate = new Date();

                // track_date
                preparedStatement.setDate(index++, new java.sql.Date(urlLastChangedTimestampEntity.getTrackDate().getTime()));

                preparedStatement.setInt(index++, urlLastChangedTimestampEntity.getUrlType());

                // domain_id
                preparedStatement.setInt(index++, urlLastChangedTimestampEntity.getDomainId());

                // url_hash
                preparedStatement.setString(index++, urlLastChangedTimestampEntity.getUrlHash());

                // url_murmur_hash
                preparedStatement.setString(index++, urlLastChangedTimestampEntity.getUrlMurmurHash());

                // last_changed_crawl_timestamp
                preparedStatement.setTimestamp(index++, new java.sql.Timestamp(urlLastChangedTimestampEntity.getLastChangedCrawlTimestamp().getTime()));

                preparedStatement.addBatch();
            }

            int retryCount = 0;
            //long startTimestamp = 0L;
            while (retryCount < maximumRetryCounts) {
                try {
                    //startTimestamp = System.nanoTime();
                    preparedStatement.executeBatch();
                    retryCount = maximumRetryCounts;
                } catch (Exception e) {
                    retryCount++;
                    if (retryCount >= maximumRetryCounts) {
                        e.printStackTrace();
                        throw e;
                    } else {
                        if (StringUtils.containsIgnoreCase(e.getMessage(), IConstants.FAILED_TO_RESPOND) == false) {
                            FormatUtils.getInstance().logMemoryUsage("createBatch() exception message=" + e.getMessage() + ",retryCount=" + retryCount + ", sql=" + creationSqlStatement);
                        }
                        try {
                            Thread.sleep(retryWaitMilliseconds);
                        } catch (InterruptedException e1) {
                            e1.printStackTrace();
                        }
                    }
                } finally {
                    if (preparedStatement != null) {
                        preparedStatement.closeOnCompletion();
                    }
                }
            }
        }
    }

    public synchronized Map<Integer, String> getMinLastChangedCrawlTimestamp(String tableName, String trackDate) throws Exception {
        Map<Integer, String> hashMap = new HashMap<>();
        final String sql = "select domain_id, toDate(min(last_changed_crawl_timestamp)) as minLastChangedCrawlTimestamp from " + getTableName(tableName) + " where track_date = ? group by domain_id";
        Connection connection = connectionList.get(0);
        PreparedStatement preparedStatement = connection.prepareStatement(sql);
        preparedStatement.setString(1, trackDate);
        ResultSet resultSet = preparedStatement.executeQuery();
        while (resultSet.next()) {
            hashMap.put(resultSet.getInt("domain_id"), resultSet.getString("minLastChangedCrawlTimestamp"));
        }
        return hashMap;
    }

    public List<Date> getAllLastChangedCrawlTimestamp(String tableName, Integer domainId, Date dailyDataCreationDate, Integer urlType) throws Exception {
        final String sql = "select distinct toDate(last_changed_crawl_timestamp) as last_changed_crawl_timestamp from " + getTableName(tableName) + " where domain_id = ? and track_date = ? and url_type = ? order by last_changed_crawl_timestamp";
        List<Date> lastChangedCrawlTimestampList = new ArrayList<>();
        Connection connection = connectionList.get(0);
        FormatUtils.getInstance().logMemoryUsage("getAllLastChangedCrawlTimestamp() sql=" + sql);
        PreparedStatement preparedStatement = connection.prepareStatement(sql);
        preparedStatement.setInt(1, domainId);
        preparedStatement.setDate(2, new java.sql.Date(dailyDataCreationDate.getTime()));
        preparedStatement.setInt(3, urlType);
        ResultSet resultSet = preparedStatement.executeQuery();
        while (resultSet.next()) {
            lastChangedCrawlTimestampList.add(resultSet.getDate("last_changed_crawl_timestamp"));
        }
        return lastChangedCrawlTimestampList;
    }

    public TreeMap<String, Integer> getLastChangedCrawlTimestamp(int domainId, Date trackDate) throws Exception {
        final String sql = "select toDate(last_changed_crawl_timestamp) as last_changed_crawl_timestamp, count(1) as count from " + getTableName(TABLE_NAME) + " where domain_id = ? and track_date = ? and url_type = 1 group by last_changed_crawl_timestamp order by last_changed_crawl_timestamp";
        TreeMap<String, Integer> treeMap = new TreeMap<>();
        Connection connection = connectionList.get(0);
        PreparedStatement preparedStatement = connection.prepareStatement(sql);
        preparedStatement.setInt(1, domainId);
        preparedStatement.setDate(2, new java.sql.Date(trackDate.getTime()));
        ResultSet resultSet = preparedStatement.executeQuery();
        while (resultSet.next()) {
            treeMap.put(resultSet.getString("last_changed_crawl_timestamp"), resultSet.getInt("count"));
        }
        return treeMap;
    }

}
