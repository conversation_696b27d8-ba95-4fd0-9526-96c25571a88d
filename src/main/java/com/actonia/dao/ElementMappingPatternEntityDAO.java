package com.actonia.dao;

import java.util.List;

import com.actonia.entity.ElementMappingPatternEntity;
import com.actonia.entity.OwnDomainEntity;

public class ElementMappingPatternEntityDAO extends BaseJdbcSupport<ElementMappingPatternEntity> {
	
	private static String SQL_GET;

	@Override
	public String getTableName() {
		// TODO Auto-generated method stub
		return "element_mapping_pattern_entity";
	}

	public List<ElementMappingPatternEntity> getElementByDomainId(int domainId, int scriptId) {
		String sql = "select * from element_mapping_pattern_entity where own_domain_id=? and process_script=? order by priority asc";
		return findBySql(sql, domainId, scriptId);
	}
	
	public List<ElementMappingPatternEntity> getElementsForCrawl(int scriptId) {
		String sql = "select * from element_mapping_pattern_entity where process_script=? order by priority asc";
		return findBySql(sql, scriptId);
	}

	public List<ElementMappingPatternEntity> getList(int domainId, int elementType, int processType, int processScript) {
		
		if (SQL_GET == null)
		{
			StringBuilder stringBuffer = new StringBuilder();
			stringBuffer.append("select ");
			stringBuffer.append("    * ");
			stringBuffer.append("from ");
			stringBuffer.append("	 element_mapping_pattern_entity ");
			stringBuffer.append("where ");
			stringBuffer.append("	 own_domain_id = ? ");
			stringBuffer.append("and element_type = ? ");
			stringBuffer.append("and process_type = ? ");
			stringBuffer.append("and process_script = ? ");
			SQL_GET = stringBuffer.toString();
		}
		return this.findBySql(SQL_GET, domainId, elementType, processType, processScript);
	}
}
