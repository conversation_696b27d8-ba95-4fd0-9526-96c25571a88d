package com.actonia.dao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.StringUtils;

import com.actonia.IConstants;
import com.actonia.utils.FormatUtils;

import ru.yandex.clickhouse.ClickHouseDataSource;
import ru.yandex.clickhouse.settings.ClickHouseProperties;

public class CompetitorUrlHtmlTransferClickHouseDAO {

	//private static boolean isDebug = false;
	private List<String> databaseHostnameList;
	private String databasePort = null;
	private String databaseName = null;
	private List<Connection> connectionList;
	private int batchCreationSize;
	private String databaseUser = null;
	private String databasePassword = null;
	private List<String> connectionUrlList = null;
	private int connectionTimeoutInMilliseconds;
	private int maximumRetryCounts;
	private int retryWaitMilliseconds;
	public static final String TABLE_NAME = "local_competitor_url_html";

	public CompetitorUrlHtmlTransferClickHouseDAO(String[] databaseHostnameArrayInput, String databasePortInput, String databaseNameInput, int batchCreationSizeInput,
			String databaseUserInput, String databasePasswordInput, int connectionTimeoutInMillisecondsInput, int maximumRetryCountsInput,
			int retryWaitMillisecondsInput) throws Exception {

		ClickHouseDataSource clickHouseDataSource = null;
		Connection connection = null;

		databaseHostnameList = Arrays.asList(databaseHostnameArrayInput);

		databasePort = databasePortInput;
		databaseName = databaseNameInput;
		batchCreationSize = batchCreationSizeInput;
		databaseUser = databaseUserInput;
		databasePassword = databasePasswordInput;
		connectionTimeoutInMilliseconds = connectionTimeoutInMillisecondsInput;
		maximumRetryCounts = maximumRetryCountsInput;
		retryWaitMilliseconds = retryWaitMillisecondsInput;

		FormatUtils.getInstance()
				.logMemoryUsage("TargetUrlHtmlBackupClickHouseDAO() databaseHostnameList=" + databaseHostnameList.toString() + ",databasePort=" + databasePort
						+ ",databaseName=" + databaseName + ",batchCreationSize=" + batchCreationSize + ",databaseUser=" + databaseUser + ",databasePassword="
						+ databasePassword + ",connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ",maximumRetryCounts=" + maximumRetryCounts
						+ ",retryWaitMilliseconds=" + retryWaitMilliseconds + ",TABLE_NAME=" + TABLE_NAME);

		ClickHouseProperties clickHouseProperties = new ClickHouseProperties();
		clickHouseProperties.setDecompress(true);
		clickHouseProperties.setConnectionTimeout(connectionTimeoutInMilliseconds);
		clickHouseProperties.setSocketTimeout(connectionTimeoutInMilliseconds);

		connectionList = new ArrayList<Connection>();

		String connectionUrl = null;

		connectionUrlList = new ArrayList<String>();

		for (String databaseHostname : databaseHostnameList) {
			if (StringUtils.isNotBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
						+ "?user=" + databaseUser + "&password=" + databasePassword;
			} else {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH
						+ databaseName;
			}
			connectionUrlList.add(connectionUrl);
			FormatUtils.getInstance().logMemoryUsage("TargetUrlHtmlBackupClickHouseDAO() connectionUrl=" + connectionUrl);
			clickHouseDataSource = new ClickHouseDataSource(connectionUrl, clickHouseProperties);
			connection = clickHouseDataSource.getConnection();
			connectionList.add(connection);
		}
	}

	@Override
	public String toString() {
		return "TargetUrlHtmlBackupClickHouseDAO [databaseHostnameList=" + databaseHostnameList.toString() + ", databasePort=" + databasePort + ", databaseName="
				+ databaseName + ", batchCreationSize=" + batchCreationSize + ", databaseUser=" + databaseUser + ", databasePassword=" + databasePassword
				+ ", connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ", maximumRetryCounts=" + maximumRetryCounts + ", retryWaitMilliseconds="
				+ retryWaitMilliseconds + "]";
	}

	private synchronized String getTableName(String tableName) {
		if (StringUtils.isNotBlank(tableName)) {
			return tableName;
		} else {
			return TABLE_NAME;
		}
	}

	public Date getEarliestTrackDate(String tableName) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		Date trackDate = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     min(track_date)");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getEarliestTrackDate() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		//if (isDebug == true) {
		//	connectionUrl = connectionUrlList.get(connectionIndex);
		//	FormatUtils.getInstance().logMemoryUsage("getEarliestTrackDate() connectionUrl=" + connectionUrl);
		//}
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				preparedStatement = connection.prepareStatement(sqlString);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					trackDate = resultSet.getDate(1);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				e.printStackTrace();
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getEarliestTrackDate() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			} finally {
				if (resultSet != null) {
					resultSet.close();
				}
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();
				}
			}
		}
		//FormatUtils.getInstance()
		//		.logMemoryUsage("getEarliestTrackDate() trackDate=" + trackDate + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		return trackDate;
	}

	public Date getLatestTrackDate(String tableName) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		Date trackDate = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     max(track_date)");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getLatestTrackDate() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		//if (isDebug == true) {
		//	connectionUrl = connectionUrlList.get(connectionIndex);
		//	FormatUtils.getInstance().logMemoryUsage("getLatestTrackDate() connectionUrl=" + connectionUrl);
		//}
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				preparedStatement = connection.prepareStatement(sqlString);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					trackDate = resultSet.getDate(1);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				e.printStackTrace();
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getLatestTrackDate() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			} finally {
				if (resultSet != null) {
					resultSet.close();
				}
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();
				}
			}
		}
		//FormatUtils.getInstance()
		//		.logMemoryUsage("getLatestTrackDate() trackDate=" + trackDate + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		return trackDate;
	}

	public void transferData(String sourceDatabaseServerIpAddress, String sourceTableName, String destinationTableName, String trackDateString) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("transferData() begins. sourceDatabaseServerIpAddress=" + sourceDatabaseServerIpAddress + ",sourceTableName="
				+ sourceTableName + ",destinationTableName=" + destinationTableName + ",trackDateString=" + trackDateString);
		long startTimestamp = System.currentTimeMillis();
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("insert into " + getTableName(destinationTableName) + " ");
		stringBuilder.append("select * from remote('");
		stringBuilder.append(sourceDatabaseServerIpAddress);
		stringBuilder.append("', crawl.");
		stringBuilder.append(sourceTableName);
		stringBuilder.append(", '");
		stringBuilder.append(databaseUser);
		stringBuilder.append("', '");
		stringBuilder.append(databasePassword);
		stringBuilder.append("') where track_date = '");
		stringBuilder.append(trackDateString);
		stringBuilder.append("'");
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("transferData() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		//if (isDebug == true) {
		//	connectionUrl = connectionUrlList.get(connectionIndex);
		//	FormatUtils.getInstance().logMemoryUsage("transferData() connectionUrl=" + connectionUrl);
		//}
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				preparedStatement = connection.prepareStatement(sqlString);
				resultSet = preparedStatement.executeQuery();
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				e.printStackTrace();
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("transferData() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			} finally {
				if (resultSet != null) {
					resultSet.close();
				}
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();
				}
			}
		}
		FormatUtils.getInstance()
				.logMemoryUsage("transferData() ends. sourceDatabaseServerIpAddress=" + sourceDatabaseServerIpAddress + ",sourceTableName=" + sourceTableName
						+ ",destinationTableName=" + destinationTableName + ",trackDateString=" + trackDateString + ",elapsed(s.)="
						+ (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	public int getRecordCount(Date trackDate, String tableName) throws Exception {
		//FormatUtils.getInstance().logMemoryUsage("getRecordCount() begins. trackDate=" + trackDate + ",tableName=" + tableName);
		long startTimestamp = System.currentTimeMillis();
		int recordCount = 0;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     count(*)");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("     track_date = ?");
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getRecordCount() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		//if (isDebug == true) {
		//	connectionUrl = connectionUrlList.get(connectionIndex);
		//	FormatUtils.getInstance().logMemoryUsage("getRecordCount() connectionUrl=" + connectionUrl);
		//}
		retryCount = 0;
		int index = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setDate(index++, new java.sql.Date(trackDate.getTime()));
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					recordCount = resultSet.getInt(1);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				e.printStackTrace();
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getRecordCount() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			} finally {
				if (resultSet != null) {
					resultSet.close();
				}
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();
				}
			}
		}
		//FormatUtils.getInstance()
		//		.logMemoryUsage("getRecordCount() ends. recordCount=" + recordCount + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		return recordCount;
	}
}
