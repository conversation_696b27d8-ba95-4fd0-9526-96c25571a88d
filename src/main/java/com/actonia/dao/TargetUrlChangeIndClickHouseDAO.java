package com.actonia.dao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;

import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;

import com.actonia.IConstants;
import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
import com.actonia.utils.ContentGuardUtils;
import com.actonia.utils.CrawlerUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.value.object.AlternateLinks;
import com.actonia.value.object.CustomData;
import com.actonia.value.object.HreflangErrors;
import com.actonia.value.object.HreflangLinks;
import com.actonia.value.object.OgMarkup;
import com.actonia.value.object.PageLink;
import com.actonia.value.object.RedirectChain;
import com.actonia.value.object.ResponseHeaders;
import com.actonia.value.object.StructuredData;
import com.actonia.value.object.TargetUrlChangeHourlyTotals;
import com.actonia.value.object.TargetUrlResponseCodeHourlyTotals;
import com.google.gson.Gson;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import ru.yandex.clickhouse.ClickHouseDataSource;
import ru.yandex.clickhouse.settings.ClickHouseProperties;

// Due to ClickHouse concurrency issues, use Singleton pattern, do not use Spring (ie. all methods must be 'synchronized')
public class TargetUrlChangeIndClickHouseDAO {

	private static final Logger log = LogManager.getLogger(TargetUrlChangeIndClickHouseDAO.class);
	private static boolean isDebug = true; //debug
	public static final String TABLE_NAME = "dis_target_url_change_ind";
	//public static final String TABLE_NAME = "test_target_url_change_ind";
	private static final int TOTAL_DAO_INSTANCES = 8;
	private static int daoMapIndex = 0;

	// map key = DAO index (0  - 59)
	// map value = instance of TargetUrlChangeIndClickHouseDAO
	private static Map<Integer, TargetUrlChangeIndClickHouseDAO> targetUrlChangeIndClickHouseDAOMap;

	private List<String> databaseHostnameList;
	private String databasePort = null;
	private String databaseName = null;
	private List<Connection> connectionList;
	private int batchCreationSize;
	private String databaseUser = null;
	private String databasePassword = null;
	private List<String> connectionUrlList = null;
	private int connectionTimeoutInMilliseconds;
	private int maximumRetryCounts;
	private int retryWaitMilliseconds;
	private int daoIndex;

	public static TargetUrlChangeIndClickHouseDAO getInstance() throws Exception {
		TargetUrlChangeIndClickHouseDAO targetUrlChangeIndClickHouseDAO = null;
		String clickHouseDatabaseHostnames = null;
		String[] clickHouseDatabaseHostnameArray = null;
		String clickHouseDatabasePort = null;
		String clickHouseDatabaseName = null;
		String clickHouseDatabaseUser = null;
		String clickHouseDatabasePassword = null;
		int clickHouseBatchCreationSize = 0;
		int clickHouseconnectionTimeoutInMilliseconds = 0;
		int clickHouseMaximumRetryCounts = 0;
		int clickHouseRetryWaitMilliseconds = 0;
		if (targetUrlChangeIndClickHouseDAOMap == null) {
			clickHouseDatabaseHostnames = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_HOSTNAMES);
			clickHouseDatabaseHostnameArray = clickHouseDatabaseHostnames.split(IConstants.COMMA);
			clickHouseDatabasePort = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_PORT);
			clickHouseDatabaseName = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_NAME);
			clickHouseDatabaseUser = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_USER);
			clickHouseDatabasePassword = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_PASSWORD);
			clickHouseBatchCreationSize = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_BATCH_CREATION_SIZE, 100);
			clickHouseconnectionTimeoutInMilliseconds = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_CONNECTION_TIMEOUT_MILLISECONDS, 88888);
			clickHouseMaximumRetryCounts = ClickHouseCrawlDatabaseConfigurations.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_MAX_RETRY_COUNTS,
					8);
			clickHouseRetryWaitMilliseconds = ClickHouseCrawlDatabaseConfigurations
					.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_RETRY_WAIT_MILLISECONDS, 1000);
			FormatUtils.getInstance().logMemoryUsage("TargetUrlChangeIndClickHouseDAO() clickHouseDatabaseHostnames=" + clickHouseDatabaseHostnames.toString()
					+ ",clickHouseDatabasePort=" + clickHouseDatabasePort + ",clickHouseDatabaseName=" + clickHouseDatabaseName + ",clickHouseBatchCreationSize="
					+ clickHouseBatchCreationSize + ",clickHouseDatabaseUser=" + clickHouseDatabaseUser + ",clickHouseDatabasePassword=" + clickHouseDatabasePassword
					+ ",clickHouseconnectionTimeoutInMilliseconds=" + clickHouseconnectionTimeoutInMilliseconds + ",clickHouseMaximumRetryCounts="
					+ clickHouseMaximumRetryCounts + ",clickHouseRetryWaitMilliseconds=" + clickHouseRetryWaitMilliseconds + ",TABLE_NAME=" + TABLE_NAME);

			targetUrlChangeIndClickHouseDAOMap = new HashMap<Integer, TargetUrlChangeIndClickHouseDAO>();
			for (int i = 0; i < TOTAL_DAO_INSTANCES; i++) {
				targetUrlChangeIndClickHouseDAO = initialize(clickHouseDatabaseHostnameArray, clickHouseDatabasePort, clickHouseDatabaseName, clickHouseDatabaseUser,
						clickHouseDatabasePassword, clickHouseBatchCreationSize, clickHouseconnectionTimeoutInMilliseconds, clickHouseMaximumRetryCounts,
						clickHouseRetryWaitMilliseconds, i);
				targetUrlChangeIndClickHouseDAOMap.put(i, targetUrlChangeIndClickHouseDAO);
			}
			FormatUtils.getInstance().logMemoryUsage("getInstance() total targetUrlChangeIndClickHouseDAOs=" + targetUrlChangeIndClickHouseDAOMap.size());
		}
		int index = getDaoMapIndex();
		targetUrlChangeIndClickHouseDAO = targetUrlChangeIndClickHouseDAOMap.get(index);
		return targetUrlChangeIndClickHouseDAO;
	}

	private static synchronized int getDaoMapIndex() {
		int index = 0;
		if (daoMapIndex >= TOTAL_DAO_INSTANCES) {
			daoMapIndex = 0;
			index = 0;
		} else {
			index = daoMapIndex++;
		}
		return index;
	}

	// initialize TargetUrlChangeIndClickHouseDAO based on runtime clickhouse configurations
	private static TargetUrlChangeIndClickHouseDAO initialize(String[] clickHouseDatabaseHostnameArray, String clickHouseDatabasePort, String clickHouseDatabaseName,
			String clickHouseDatabaseUser, String clickHouseDatabasePassword, int clickHouseBatchCreationSize, int clickHouseconnectionTimeoutInMilliseconds,
			int clickHouseMaximumRetryCounts, int clickHouseRetryWaitMilliseconds, int daoIndex) throws Exception {
		TargetUrlChangeIndClickHouseDAO targetUrlChangeIndClickHouseDAO = new TargetUrlChangeIndClickHouseDAO(clickHouseDatabaseHostnameArray, clickHouseDatabasePort,
				clickHouseDatabaseName, clickHouseBatchCreationSize, clickHouseDatabaseUser, clickHouseDatabasePassword, clickHouseconnectionTimeoutInMilliseconds,
				clickHouseMaximumRetryCounts, clickHouseRetryWaitMilliseconds, daoIndex);
		//FormatUtils.getInstance().logMemoryUsage("initialize() targetUrlChangeIndClickHouseDAO=" + targetUrlChangeIndClickHouseDAO.toString());
		return targetUrlChangeIndClickHouseDAO;
	}

	private TargetUrlChangeIndClickHouseDAO(String[] databaseHostnameArrayInput, String databasePortInput, String databaseNameInput, int batchCreationSizeInput,
			String databaseUserInput, String databasePasswordInput, int connectionTimeoutInMillisecondsInput, int maximumRetryCountsInput,
			int retryWaitMillisecondsInput, int index) throws Exception {

		ClickHouseDataSource clickHouseDataSource = null;
		Connection connection = null;

		databaseHostnameList = Arrays.asList(databaseHostnameArrayInput);
		databasePort = databasePortInput;
		databaseName = databaseNameInput;
		batchCreationSize = batchCreationSizeInput;
		databaseUser = databaseUserInput;
		databasePassword = databasePasswordInput;
		connectionTimeoutInMilliseconds = connectionTimeoutInMillisecondsInput;
		maximumRetryCounts = maximumRetryCountsInput;
		retryWaitMilliseconds = retryWaitMillisecondsInput;
		daoIndex = index;

		ClickHouseProperties clickHouseProperties = new ClickHouseProperties();
		clickHouseProperties.setDecompress(true);
		clickHouseProperties.setConnectionTimeout(connectionTimeoutInMilliseconds);
		clickHouseProperties.setSocketTimeout(connectionTimeoutInMilliseconds);

		connectionList = new ArrayList<Connection>();

		String connectionUrl = null;

		connectionUrlList = new ArrayList<String>();

		for (String databaseHostname : databaseHostnameList) {
			if (StringUtils.isNotBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
						+ "?user=" + databaseUser + "&password=" + databasePassword;
			} else if (StringUtils.isBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
						+ "?password=" + databasePassword;
			} else {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH
						+ databaseName;
			}
			connectionUrlList.add(connectionUrl);
			//FormatUtils.getInstance().logMemoryUsage("TargetUrlChangeIndClickHouseDAO() connectionUrl=" + connectionUrl);
			clickHouseDataSource = new ClickHouseDataSource(connectionUrl, clickHouseProperties);
			connection = clickHouseDataSource.getConnection();
			connectionList.add(connection);
		}
	}

	@Override
	public String toString() {
		return "TargetUrlChangeIndClickHouseDAO [databaseHostnameList=" + databaseHostnameList.toString() + ", databasePort=" + databasePort + ", databaseName="
				+ databaseName + ", batchCreationSize=" + batchCreationSize + ", databaseUser=" + databaseUser + ", databasePassword=" + databasePassword
				+ ", connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ", maximumRetryCounts=" + maximumRetryCounts + ", retryWaitMilliseconds="
				+ retryWaitMilliseconds + ", daoIndex=" + daoIndex + "]";
	}

	private synchronized String getTableName(String tableName) {
		if (StringUtils.isNotBlank(tableName)) {
			return tableName;
		} else {
			return TABLE_NAME;
		}
	}

	public synchronized int getBatchCreationSize() {
		return batchCreationSize;
	}

	public synchronized void createBatch(String ip, List<TargetUrlChangeIndClickHouseEntity> targetUrlChangeIndClickHouseEntityList, String tableName)
			throws Exception {
		long startTimestamp = System.currentTimeMillis();

		PreparedStatement preparedStatement = null;
		int index = 0;
		Connection connection = null;

		String creationSqlStatement = getCreationSqlStatement(tableName);
		//FormatUtils.getInstance().logMemoryUsage("createBatch() creationSqlStatement=" + creationSqlStatement);

		Gson gson = new Gson();

		for (int i = 0; i < connectionList.size(); i++) {
			connection = connectionList.get(i);
			preparedStatement = connection.prepareStatement(creationSqlStatement);
			for (TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity : targetUrlChangeIndClickHouseEntityList) {

				index = 1;

				// domain_id
				preparedStatement.setInt(index++, targetUrlChangeIndClickHouseEntity.getDomainId());

				// url
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getUrl());

				// track_date
				preparedStatement.setDate(index++, new java.sql.Date(targetUrlChangeIndClickHouseEntity.getTrackDate().getTime()));

				// current_crawl_timestamp
				preparedStatement.setTimestamp(index++, new java.sql.Timestamp(targetUrlChangeIndClickHouseEntity.getCurrentCrawlTimestamp().getTime()));

				// change_indicator
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getChangeIndicator());

				// previous_crawl_timestamp
				preparedStatement.setTimestamp(index++, new java.sql.Timestamp(targetUrlChangeIndClickHouseEntity.getPreviousCrawlTimestamp().getTime()));

				// update_timestamp
				preparedStatement.setTimestamp(index++, new java.sql.Timestamp(new Date().getTime()));

				// change_type
				preparedStatement.setInt(index++, targetUrlChangeIndClickHouseEntity.getChangeType());

				// critical_ind
				preparedStatement.setInt(index++, targetUrlChangeIndClickHouseEntity.getCriticalInd());

				// alternate_links_current
				preparedStatement.setString(index++,
						targetUrlChangeIndClickHouseEntity.getAlternateLinksCurrent() != null
								? gson.toJson(targetUrlChangeIndClickHouseEntity.getAlternateLinksCurrent(), AlternateLinks[].class)
								: null);

				// alternate_links_previous
				preparedStatement.setString(index++,
						targetUrlChangeIndClickHouseEntity.getAlternateLinksPrevious() != null
								? gson.toJson(targetUrlChangeIndClickHouseEntity.getAlternateLinksPrevious(), AlternateLinks[].class)
								: null);

				// amphtml_href_current
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getAmphtmlHrefCurrent());

				// amphtml_href_previous
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getAmphtmlHrefPrevious());

				// analyzed_url_s_current
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getAnalyzedUrlSCurrent());

				// analyzed_url_s_previous
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getAnalyzedUrlSPrevious());

				// archive_flg_current
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getArchiveFlgCurrent());

				// archive_flg_previous
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getArchiveFlgPrevious());

				// base_tag_current
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getBaseTagCurrent());

				// base_tag_previous
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getBaseTagPrevious());

				// base_tag_target_current
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getBaseTagTargetCurrent());

				// base_tag_target_previous
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getBaseTagTargetPrevious());

				// blocked_by_robots_current
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getBlockedByRobotsCurrent());

				// blocked_by_robots_previous
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getBlockedByRobotsPrevious());

				// canonical_current
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getCanonicalCurrent());

				// canonical_previous
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getCanonicalPrevious());

				// canonical_header_flag_current
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getCanonicalHeaderFlagCurrent() != null
								? BooleanUtils.toInteger(targetUrlChangeIndClickHouseEntity.getCanonicalHeaderFlagCurrent())
								: -1);

				// canonical_header_flag_previous
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getCanonicalHeaderFlagPrevious() != null
								? BooleanUtils.toInteger(targetUrlChangeIndClickHouseEntity.getCanonicalHeaderFlagPrevious())
								: -1);

				// canonical_header_type_current
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getCanonicalHeaderTypeCurrent());

				// canonical_header_type_previous
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getCanonicalHeaderTypePrevious());

				// canonical_type_current
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getCanonicalTypeCurrent());

				// canonical_type_previous
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getCanonicalTypePrevious());

				// canonical_url_is_consistent_current
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getCanonicalUrlIsConsistentCurrent());

				// canonical_url_is_consistent_previous
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getCanonicalUrlIsConsistentPrevious());

				// content_type_current
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getContentTypeCurrent());

				// content_type_previous
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getContentTypePrevious());

				// custom_data_current
				preparedStatement.setString(index++,
						targetUrlChangeIndClickHouseEntity.getCustomDataCurrent() != null
								? gson.toJson(targetUrlChangeIndClickHouseEntity.getCustomDataCurrent(), CustomData[].class)
								: null);

				// custom_data_previous
				preparedStatement.setString(index++,
						targetUrlChangeIndClickHouseEntity.getCustomDataPrevious() != null
								? gson.toJson(targetUrlChangeIndClickHouseEntity.getCustomDataPrevious(), CustomData[].class)
								: null);

				// description_current
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getDescriptionCurrent());

				// description_previous
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getDescriptionPrevious());

				// description_length_current
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getDescriptionLengthCurrent() != null ? targetUrlChangeIndClickHouseEntity.getDescriptionLengthCurrent()
								: -1);

				// description_length_previous
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getDescriptionLengthPrevious() != null ? targetUrlChangeIndClickHouseEntity.getDescriptionLengthPrevious()
								: -1);

				// error_message_current
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getErrorMessageCurrent());

				// error_message_previous
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getErrorMessagePrevious());

				// final_response_code_current
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getFinalResponseCodeCurrent() != null ? targetUrlChangeIndClickHouseEntity.getFinalResponseCodeCurrent()
								: -1);

				// final_response_code_previous
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getFinalResponseCodePrevious() != null ? targetUrlChangeIndClickHouseEntity.getFinalResponseCodePrevious()
								: -1);

				// follow_flg_current
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getFollowFlgCurrent());

				// follow_flg_previous
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getFollowFlgPrevious());

				// h1_current
				preparedStatement.setString(index++,
						targetUrlChangeIndClickHouseEntity.getH1Current() != null ? gson.toJson(targetUrlChangeIndClickHouseEntity.getH1Current(), String[].class)
								: null);

				// h1_previous
				preparedStatement.setString(index++,
						targetUrlChangeIndClickHouseEntity.getH1Previous() != null ? gson.toJson(targetUrlChangeIndClickHouseEntity.getH1Previous(), String[].class)
								: null);

				// h1_count_current
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getH1CountCurrent() != null ? targetUrlChangeIndClickHouseEntity.getH1CountCurrent() : -1);

				// h1_count_previous
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getH1CountPrevious() != null ? targetUrlChangeIndClickHouseEntity.getH1CountPrevious() : -1);

				// h1_length_current
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getH1LengthCurrent() != null ? targetUrlChangeIndClickHouseEntity.getH1LengthCurrent() : -1);

				// h1_length_previous
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getH1LengthPrevious() != null ? targetUrlChangeIndClickHouseEntity.getH1LengthPrevious() : -1);

				// h2_current
				preparedStatement.setString(index++,
						targetUrlChangeIndClickHouseEntity.getH2Current() != null ? gson.toJson(targetUrlChangeIndClickHouseEntity.getH2Current(), String[].class)
								: null);

				// h2_previous
				preparedStatement.setString(index++,
						targetUrlChangeIndClickHouseEntity.getH2Previous() != null ? gson.toJson(targetUrlChangeIndClickHouseEntity.getH2Previous(), String[].class)
								: null);

				// header_noarchive_current
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getHeaderNoarchiveCurrent() != null
								? BooleanUtils.toInteger(targetUrlChangeIndClickHouseEntity.getHeaderNoarchiveCurrent())
								: -1);

				// header_noarchive_previous
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getHeaderNoarchivePrevious() != null
								? BooleanUtils.toInteger(targetUrlChangeIndClickHouseEntity.getHeaderNoarchivePrevious())
								: -1);

				// header_nofollow_current
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getHeaderNofollowCurrent() != null
								? BooleanUtils.toInteger(targetUrlChangeIndClickHouseEntity.getHeaderNofollowCurrent())
								: -1);

				// header_nofollow_previous
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getHeaderNofollowPrevious() != null
								? BooleanUtils.toInteger(targetUrlChangeIndClickHouseEntity.getHeaderNofollowPrevious())
								: -1);

				// header_noindex_current
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getHeaderNoindexCurrent() != null
								? BooleanUtils.toInteger(targetUrlChangeIndClickHouseEntity.getHeaderNoindexCurrent())
								: -1);

				// header_noindex_previous
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getHeaderNoindexPrevious() != null
								? BooleanUtils.toInteger(targetUrlChangeIndClickHouseEntity.getHeaderNoindexPrevious())
								: -1);

				// header_noodp_current
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getHeaderNoodpCurrent() != null
								? BooleanUtils.toInteger(targetUrlChangeIndClickHouseEntity.getHeaderNoodpCurrent())
								: -1);

				// header_noodp_previous
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getHeaderNoodpPrevious() != null
								? BooleanUtils.toInteger(targetUrlChangeIndClickHouseEntity.getHeaderNoodpPrevious())
								: -1);

				// header_nosnippet_current
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getHeaderNosnippetCurrent() != null
								? BooleanUtils.toInteger(targetUrlChangeIndClickHouseEntity.getHeaderNosnippetCurrent())
								: -1);

				// header_nosnippet_previous
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getHeaderNosnippetPrevious() != null
								? BooleanUtils.toInteger(targetUrlChangeIndClickHouseEntity.getHeaderNosnippetPrevious())
								: -1);

				// header_noydir_current
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getHeaderNoydirCurrent() != null
								? BooleanUtils.toInteger(targetUrlChangeIndClickHouseEntity.getHeaderNoydirCurrent())
								: -1);

				// header_noydir_previous
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getHeaderNoydirPrevious() != null
								? BooleanUtils.toInteger(targetUrlChangeIndClickHouseEntity.getHeaderNoydirPrevious())
								: -1);

				// hreflang_errors_current
				preparedStatement.setString(index++,
						targetUrlChangeIndClickHouseEntity.getHreflangErrorsCurrent() != null
								? gson.toJson(targetUrlChangeIndClickHouseEntity.getHreflangErrorsCurrent(), HreflangErrors.class)
								: null);

				// hreflang_errors_previous
				preparedStatement.setString(index++,
						targetUrlChangeIndClickHouseEntity.getHreflangErrorsPrevious() != null
								? gson.toJson(targetUrlChangeIndClickHouseEntity.getHreflangErrorsPrevious(), HreflangErrors.class)
								: null);

				// hreflang_links_current
				preparedStatement.setString(index++,
						targetUrlChangeIndClickHouseEntity.getHreflangLinksCurrent() != null
								? gson.toJson(targetUrlChangeIndClickHouseEntity.getHreflangLinksCurrent(), HreflangLinks[].class)
								: null);

				// hreflang_links_previous
				preparedStatement.setString(index++,
						targetUrlChangeIndClickHouseEntity.getHreflangLinksPrevious() != null
								? gson.toJson(targetUrlChangeIndClickHouseEntity.getHreflangLinksPrevious(), HreflangLinks[].class)
								: null);

				// hreflang_links_out_count_current
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getHreflangLinksOutCountCurrent() != null
								? targetUrlChangeIndClickHouseEntity.getHreflangLinksOutCountCurrent()
								: -1);

				// hreflang_links_out_count_previous
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getHreflangLinksOutCountPrevious() != null
								? targetUrlChangeIndClickHouseEntity.getHreflangLinksOutCountPrevious()
								: -1);

				// hreflang_url_count_current
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getHreflangUrlCountCurrent() != null ? targetUrlChangeIndClickHouseEntity.getHreflangUrlCountCurrent() : -1);

				// hreflang_url_count_previous
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getHreflangUrlCountPrevious() != null ? targetUrlChangeIndClickHouseEntity.getHreflangUrlCountPrevious()
								: -1);

				// index_flg_current
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getIndexFlgCurrent());

				// index_flg_previous
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getIndexFlgPrevious());

				// indexable_current
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getIndexableCurrent() != null
								? BooleanUtils.toInteger(targetUrlChangeIndClickHouseEntity.getIndexableCurrent())
								: -1);

				// indexable_previous
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getIndexablePrevious() != null
								? BooleanUtils.toInteger(targetUrlChangeIndClickHouseEntity.getIndexablePrevious())
								: -1);

				// insecure_resources_current
				preparedStatement.setString(index++,
						targetUrlChangeIndClickHouseEntity.getInsecureResourcesCurrent() != null
								? gson.toJson(targetUrlChangeIndClickHouseEntity.getInsecureResourcesCurrent(), String[].class)
								: null);

				// insecure_resources_previous
				preparedStatement.setString(index++,
						targetUrlChangeIndClickHouseEntity.getInsecureResourcesPrevious() != null
								? gson.toJson(targetUrlChangeIndClickHouseEntity.getInsecureResourcesPrevious(), String[].class)
								: null);

				// meta_charset_current
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getMetaCharsetCurrent());

				// meta_charset_previous
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getMetaCharsetPrevious());

				// meta_content_type_current
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getMetaContentTypeCurrent());

				// meta_content_type_previous
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getMetaContentTypePrevious());

				// meta_disabled_sitelinks_current
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getMetaDisabledSitelinksCurrent() != null
								? BooleanUtils.toInteger(targetUrlChangeIndClickHouseEntity.getMetaDisabledSitelinksCurrent())
								: -1);

				// meta_disabled_sitelinks_previous
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getMetaDisabledSitelinksPrevious() != null
								? BooleanUtils.toInteger(targetUrlChangeIndClickHouseEntity.getMetaDisabledSitelinksPrevious())
								: -1);

				// meta_noodp_current
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getMetaNoodpCurrent() != null
								? BooleanUtils.toInteger(targetUrlChangeIndClickHouseEntity.getMetaNoodpCurrent())
								: -1);

				// meta_noodp_previous
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getMetaNoodpPrevious() != null
								? BooleanUtils.toInteger(targetUrlChangeIndClickHouseEntity.getMetaNoodpPrevious())
								: -1);

				// meta_nosnippet_current
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getMetaNosnippetCurrent() != null
								? BooleanUtils.toInteger(targetUrlChangeIndClickHouseEntity.getMetaNosnippetCurrent())
								: -1);

				// meta_nosnippet_previous
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getMetaNosnippetPrevious() != null
								? BooleanUtils.toInteger(targetUrlChangeIndClickHouseEntity.getMetaNosnippetPrevious())
								: -1);

				// meta_noydir_current
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getMetaNoydirCurrent() != null
								? BooleanUtils.toInteger(targetUrlChangeIndClickHouseEntity.getMetaNoydirCurrent())
								: -1);

				// meta_noydir_previous
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getMetaNoydirPrevious() != null
								? BooleanUtils.toInteger(targetUrlChangeIndClickHouseEntity.getMetaNoydirPrevious())
								: -1);

				// meta_redirect_current
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getMetaRedirectCurrent() != null
								? BooleanUtils.toInteger(targetUrlChangeIndClickHouseEntity.getMetaRedirectCurrent())
								: -1);

				// meta_redirect_previous
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getMetaRedirectPrevious() != null
								? BooleanUtils.toInteger(targetUrlChangeIndClickHouseEntity.getMetaRedirectPrevious())
								: -1);

				// mixed_redirects_current
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getMixedRedirectsCurrent() != null
								? BooleanUtils.toInteger(targetUrlChangeIndClickHouseEntity.getMixedRedirectsCurrent())
								: -1);

				// mixed_redirects_previous
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getMixedRedirectsPrevious() != null
								? BooleanUtils.toInteger(targetUrlChangeIndClickHouseEntity.getMixedRedirectsPrevious())
								: -1);

				// mobile_rel_alternate_url_is_consistent_current
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getMobileRelAlternateUrlIsConsistentCurrent() != null
								? BooleanUtils.toInteger(targetUrlChangeIndClickHouseEntity.getMobileRelAlternateUrlIsConsistentCurrent())
								: -1);

				// mobile_rel_alternate_url_is_consistent_previous
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getMobileRelAlternateUrlIsConsistentPrevious() != null
								? BooleanUtils.toInteger(targetUrlChangeIndClickHouseEntity.getMobileRelAlternateUrlIsConsistentPrevious())
								: -1);

				// noodp_current
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getNoodpCurrent() != null ? BooleanUtils.toInteger(targetUrlChangeIndClickHouseEntity.getNoodpCurrent())
								: -1);

				// noodp_previous
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getNoodpPrevious() != null ? BooleanUtils.toInteger(targetUrlChangeIndClickHouseEntity.getNoodpPrevious())
								: -1);

				// nosnippet_current
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getNosnippetCurrent() != null
								? BooleanUtils.toInteger(targetUrlChangeIndClickHouseEntity.getNosnippetCurrent())
								: -1);

				// nosnippet_previous
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getNosnippetPrevious() != null
								? BooleanUtils.toInteger(targetUrlChangeIndClickHouseEntity.getNosnippetPrevious())
								: -1);

				// noydir_current
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getNoydirCurrent() != null ? BooleanUtils.toInteger(targetUrlChangeIndClickHouseEntity.getNoydirCurrent())
								: -1);

				// noydir_previous
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getNoydirPrevious() != null ? BooleanUtils.toInteger(targetUrlChangeIndClickHouseEntity.getNoydirPrevious())
								: -1);

				// og_markup_current
				preparedStatement.setString(index++,
						targetUrlChangeIndClickHouseEntity.getOgMarkupCurrent() != null
								? gson.toJson(targetUrlChangeIndClickHouseEntity.getOgMarkupCurrent(), OgMarkup[].class)
								: null);

				// og_markup_previous
				preparedStatement.setString(index++,
						targetUrlChangeIndClickHouseEntity.getOgMarkupPrevious() != null
								? gson.toJson(targetUrlChangeIndClickHouseEntity.getOgMarkupPrevious(), OgMarkup[].class)
								: null);

				// og_markup_length_current
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getOgMarkupLengthCurrent() != null ? targetUrlChangeIndClickHouseEntity.getOgMarkupLengthCurrent() : -1);

				// og_markup_length_previous
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getOgMarkupLengthPrevious() != null ? targetUrlChangeIndClickHouseEntity.getOgMarkupLengthPrevious() : -1);

				// outlink_count_current
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getOutlinkCountCurrent() != null ? targetUrlChangeIndClickHouseEntity.getOutlinkCountCurrent() : -1);

				// outlink_count_previous
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getOutlinkCountPrevious() != null ? targetUrlChangeIndClickHouseEntity.getOutlinkCountPrevious() : -1);

				// page_analysis_results_chg_ind_json
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getPageAnalysisResultsChgIndJson());

				// page_link_current
				preparedStatement.setString(index++,
						targetUrlChangeIndClickHouseEntity.getPageLinkCurrent() != null
								? gson.toJson(targetUrlChangeIndClickHouseEntity.getPageLinkCurrent(), PageLink[].class)
								: null);

				// page_link_previous
				preparedStatement.setString(index++,
						targetUrlChangeIndClickHouseEntity.getPageLinkPrevious() != null
								? gson.toJson(targetUrlChangeIndClickHouseEntity.getPageLinkPrevious(), PageLink[].class)
								: null);

				// redirect_blocked_current
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getRedirectBlockedCurrent() != null
								? BooleanUtils.toInteger(targetUrlChangeIndClickHouseEntity.getRedirectBlockedCurrent())
								: -1);

				// redirect_blocked_previous
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getRedirectBlockedPrevious() != null
								? BooleanUtils.toInteger(targetUrlChangeIndClickHouseEntity.getRedirectBlockedPrevious())
								: -1);

				// redirect_blocked_reason_current
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getRedirectBlockedReasonCurrent());

				// redirect_blocked_reason_previous
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getRedirectBlockedReasonPrevious());

				// redirect_chain_current
				preparedStatement.setString(index++,
						targetUrlChangeIndClickHouseEntity.getRedirectChainCurrent() != null
								? gson.toJson(targetUrlChangeIndClickHouseEntity.getRedirectChainCurrent(), RedirectChain[].class)
								: null);

				// redirect_chain_previous
				preparedStatement.setString(index++,
						targetUrlChangeIndClickHouseEntity.getRedirectChainPrevious() != null
								? gson.toJson(targetUrlChangeIndClickHouseEntity.getRedirectChainPrevious(), RedirectChain[].class)
								: null);

				// redirect_final_url_current
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getRedirectFinalUrlCurrent());

				// redirect_final_url_previous
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getRedirectFinalUrlPrevious());

				// redirect_times_current
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getRedirectTimesCurrent() != null ? targetUrlChangeIndClickHouseEntity.getRedirectTimesCurrent() : -1);

				// redirect_times_previous
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getRedirectTimesPrevious() != null ? targetUrlChangeIndClickHouseEntity.getRedirectTimesPrevious() : -1);

				// response_code_current
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getResponseCodeCurrent());

				// response_code_previous
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getResponseCodePrevious());

				// response_headers_current
				preparedStatement.setString(index++,
						targetUrlChangeIndClickHouseEntity.getResponseHeadersCurrent() != null
								? gson.toJson(targetUrlChangeIndClickHouseEntity.getResponseHeadersCurrent(), ResponseHeaders[].class)
								: null);

				// response_headers_previous
				preparedStatement.setString(index++,
						targetUrlChangeIndClickHouseEntity.getResponseHeadersPrevious() != null
								? gson.toJson(targetUrlChangeIndClickHouseEntity.getResponseHeadersPrevious(), ResponseHeaders[].class)
								: null);

				// robots_contents_current
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getRobotsContentsCurrent());

				// robots_contents_previous
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getRobotsContentsPrevious());

				// structured_data_current
				if (CrawlerUtils.getInstance().checkIfStructuredDataAvailable(targetUrlChangeIndClickHouseEntity.getStructuredDataCurrent()) == true) {
					preparedStatement.setString(index++,
							targetUrlChangeIndClickHouseEntity.getStructuredDataCurrent() != null
									? gson.toJson(targetUrlChangeIndClickHouseEntity.getStructuredDataCurrent(), StructuredData.class)
									: null);
				} else {
					preparedStatement.setString(index++, null);
				}

				// structured_data_previous
				if (CrawlerUtils.getInstance().checkIfStructuredDataAvailable(targetUrlChangeIndClickHouseEntity.getStructuredDataPrevious()) == true) {
					preparedStatement.setString(index++,
							targetUrlChangeIndClickHouseEntity.getStructuredDataPrevious() != null
									? gson.toJson(targetUrlChangeIndClickHouseEntity.getStructuredDataPrevious(), StructuredData.class)
									: null);
				} else {
					preparedStatement.setString(index++, null);
				}

				// title_current
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getTitleCurrent());

				// title_previous
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getTitlePrevious());

				// title_length_current
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getTitleLengthCurrent() != null ? targetUrlChangeIndClickHouseEntity.getTitleLengthCurrent() : -1);

				// title_length_previous
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getTitleLengthPrevious() != null ? targetUrlChangeIndClickHouseEntity.getTitleLengthPrevious() : -1);

				// viewport_content_current
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getViewportContentCurrent());

				// viewport_content_previous
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getViewportContentPrevious());

				// sign
				preparedStatement.setInt(index++,
						targetUrlChangeIndClickHouseEntity.getSign() != null ? targetUrlChangeIndClickHouseEntity.getSign() : IConstants.CLICKHOUSE_SIGN_POSITIVE_1);

				// robot_txt_current
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getRobotTxtCurrent());

				// robot_txt_previous
				preparedStatement.setString(index++, targetUrlChangeIndClickHouseEntity.getRobotTxtPrevious());

				preparedStatement.addBatch();
			}

			int retryCount = 0;
			//long startTimestamp = 0L;
			while (retryCount < maximumRetryCounts) {
				try {
					//startTimestamp = System.nanoTime();
					preparedStatement.executeBatch();
					retryCount = maximumRetryCounts;
				} catch (Exception e) {
					retryCount++;
					if (retryCount >= maximumRetryCounts) {
						e.printStackTrace();
						throw e;
					} else {
						if (StringUtils.containsIgnoreCase(e.getMessage(), IConstants.FAILED_TO_RESPOND) == false) {
							FormatUtils.getInstance().logMemoryUsage("createBatch() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
						}
						try {
							Thread.sleep(retryWaitMilliseconds);
						} catch (InterruptedException e1) {
							e1.printStackTrace();
						}
					}
				} finally {
					if (preparedStatement != null) {
						preparedStatement.closeOnCompletion();
					}
				}
			}
		}
		try {
			FormatUtils.getInstance().logMemoryUsage("createBatch() ip=" + ip + ",targetUrlChangeIndClickHouseEntityList.size()="
					+ targetUrlChangeIndClickHouseEntityList.size() + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private synchronized String getCreationSqlStatement(String tableName) {
		String response = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" insert into " + getTableName(tableName) + " ");
		stringBuilder.append(" (");
		stringBuilder.append("	domain_id,");
		stringBuilder.append("	url,");
		stringBuilder.append("	track_date,");
		stringBuilder.append("	current_crawl_timestamp,");
		stringBuilder.append("	change_indicator,");
		stringBuilder.append("	previous_crawl_timestamp,");
		stringBuilder.append("	update_timestamp,");
		stringBuilder.append("	change_type,");
		stringBuilder.append("	critical_ind,");
		stringBuilder.append("	alternate_links_current,");
		stringBuilder.append("	alternate_links_previous,");
		stringBuilder.append("	amphtml_href_current,");
		stringBuilder.append("	amphtml_href_previous,");
		stringBuilder.append("	analyzed_url_s_current,");
		stringBuilder.append("	analyzed_url_s_previous,");
		stringBuilder.append("	archive_flg_current,");
		stringBuilder.append("	archive_flg_previous,");
		stringBuilder.append("	base_tag_current,");
		stringBuilder.append("	base_tag_previous,");
		stringBuilder.append("	base_tag_target_current,");
		stringBuilder.append("	base_tag_target_previous,");
		stringBuilder.append("	blocked_by_robots_current,");
		stringBuilder.append("	blocked_by_robots_previous,");
		stringBuilder.append("	canonical_current,");
		stringBuilder.append("	canonical_previous,");
		stringBuilder.append("	canonical_header_flag_current,");
		stringBuilder.append("	canonical_header_flag_previous,");
		stringBuilder.append("	canonical_header_type_current,");
		stringBuilder.append("	canonical_header_type_previous,");
		stringBuilder.append("	canonical_type_current,");
		stringBuilder.append("	canonical_type_previous,");
		stringBuilder.append("	canonical_url_is_consistent_current,");
		stringBuilder.append("	canonical_url_is_consistent_previous,");
		stringBuilder.append("	content_type_current,");
		stringBuilder.append("	content_type_previous,");
		stringBuilder.append("	custom_data_current,");
		stringBuilder.append("	custom_data_previous,");
		stringBuilder.append("	description_current,");
		stringBuilder.append("	description_previous,");
		stringBuilder.append("	description_length_current,");
		stringBuilder.append("	description_length_previous,");
		stringBuilder.append("	error_message_current,");
		stringBuilder.append("	error_message_previous,");
		stringBuilder.append("	final_response_code_current,");
		stringBuilder.append("	final_response_code_previous,");
		stringBuilder.append("	follow_flg_current,");
		stringBuilder.append("	follow_flg_previous,");
		stringBuilder.append("	h1_current,");
		stringBuilder.append("	h1_previous,");
		stringBuilder.append("	h1_count_current,");
		stringBuilder.append("	h1_count_previous,");
		stringBuilder.append("	h1_length_current,");
		stringBuilder.append("	h1_length_previous,");
		stringBuilder.append("	h2_current,");
		stringBuilder.append("	h2_previous,");
		stringBuilder.append("	header_noarchive_current,");
		stringBuilder.append("	header_noarchive_previous,");
		stringBuilder.append("	header_nofollow_current,");
		stringBuilder.append("	header_nofollow_previous,");
		stringBuilder.append("	header_noindex_current,");
		stringBuilder.append("	header_noindex_previous,");
		stringBuilder.append("	header_noodp_current,");
		stringBuilder.append("	header_noodp_previous,");
		stringBuilder.append("	header_nosnippet_current,");
		stringBuilder.append("	header_nosnippet_previous,");
		stringBuilder.append("	header_noydir_current,");
		stringBuilder.append("	header_noydir_previous,");
		stringBuilder.append("	hreflang_errors_current,");
		stringBuilder.append("	hreflang_errors_previous,");
		stringBuilder.append("	hreflang_links_current,");
		stringBuilder.append("	hreflang_links_previous,");
		stringBuilder.append("	hreflang_links_out_count_current,");
		stringBuilder.append("	hreflang_links_out_count_previous,");
		stringBuilder.append("	hreflang_url_count_current,");
		stringBuilder.append("	hreflang_url_count_previous,");
		stringBuilder.append("	index_flg_current,");
		stringBuilder.append("	index_flg_previous,");
		stringBuilder.append("	indexable_current,");
		stringBuilder.append("	indexable_previous,");
		stringBuilder.append("	insecure_resources_current,");
		stringBuilder.append("	insecure_resources_previous,");
		stringBuilder.append("	meta_charset_current,");
		stringBuilder.append("	meta_charset_previous,");
		stringBuilder.append("	meta_content_type_current,");
		stringBuilder.append("	meta_content_type_previous,");
		stringBuilder.append("	meta_disabled_sitelinks_current,");
		stringBuilder.append("	meta_disabled_sitelinks_previous,");
		stringBuilder.append("	meta_noodp_current,");
		stringBuilder.append("	meta_noodp_previous,");
		stringBuilder.append("	meta_nosnippet_current,");
		stringBuilder.append("	meta_nosnippet_previous,");
		stringBuilder.append("	meta_noydir_current,");
		stringBuilder.append("	meta_noydir_previous,");
		stringBuilder.append("	meta_redirect_current,");
		stringBuilder.append("	meta_redirect_previous,");
		stringBuilder.append("	mixed_redirects_current,");
		stringBuilder.append("	mixed_redirects_previous,");
		stringBuilder.append("	mobile_rel_alternate_url_is_consistent_current,");
		stringBuilder.append("	mobile_rel_alternate_url_is_consistent_previous,");
		stringBuilder.append("	noodp_current,");
		stringBuilder.append("	noodp_previous,");
		stringBuilder.append("	nosnippet_current,");
		stringBuilder.append("	nosnippet_previous,");
		stringBuilder.append("	noydir_current,");
		stringBuilder.append("	noydir_previous,");
		stringBuilder.append("	og_markup_current,");
		stringBuilder.append("	og_markup_previous,");
		stringBuilder.append("	og_markup_length_current,");
		stringBuilder.append("	og_markup_length_previous,");
		stringBuilder.append("	outlink_count_current,");
		stringBuilder.append("	outlink_count_previous,");
		stringBuilder.append("	page_analysis_results_chg_ind_json,");
		stringBuilder.append("	page_link_current,");
		stringBuilder.append("	page_link_previous,");
		stringBuilder.append("	redirect_blocked_current,");
		stringBuilder.append("	redirect_blocked_previous,");
		stringBuilder.append("	redirect_blocked_reason_current,");
		stringBuilder.append("	redirect_blocked_reason_previous,");
		stringBuilder.append("	redirect_chain_current,");
		stringBuilder.append("	redirect_chain_previous,");
		stringBuilder.append("	redirect_final_url_current,");
		stringBuilder.append("	redirect_final_url_previous,");
		stringBuilder.append("	redirect_times_current,");
		stringBuilder.append("	redirect_times_previous,");
		stringBuilder.append("	response_code_current,");
		stringBuilder.append("	response_code_previous,");
		stringBuilder.append("	response_headers_current,");
		stringBuilder.append("	response_headers_previous,");
		stringBuilder.append("	robots_contents_current,");
		stringBuilder.append("	robots_contents_previous,");
		stringBuilder.append("	structured_data_current,");
		stringBuilder.append("	structured_data_previous,");
		stringBuilder.append("	title_current,");
		stringBuilder.append("	title_previous,");
		stringBuilder.append("	title_length_current,");
		stringBuilder.append("	title_length_previous,");
		stringBuilder.append("	viewport_content_current,");
		stringBuilder.append("	viewport_content_previous,");
		stringBuilder.append("	sign,");
		stringBuilder.append("	robot_txt_current,");
		stringBuilder.append("	robot_txt_previous");
		stringBuilder.append(" )");
		stringBuilder.append(" values ");
		stringBuilder.append(" (");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?,");
		stringBuilder.append("	?");
		stringBuilder.append(" )");
		stringBuilder.append(IConstants.SEMI_COLON);
		response = stringBuilder.toString();
		return response;
	}

	// retrieve the one record with the exact crawl timestamp
	public synchronized TargetUrlChangeIndClickHouseEntity get(int domainId, String urlString, String crawlTimestampString, List<String> databaseFields,
			String tableName) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("get() begins. domainId=" + domainId + ",urlString=" + urlString + ",crawlTimestampString=" + crawlTimestampString);
		TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = null;
		ResultSet resultSet = null;
		String trackDateString = StringUtils.substringBefore(crawlTimestampString, IConstants.ONE_SPACE);
		String trimmedString = StringUtils.trimToEmpty(urlString);
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		boolean isFirstField = true;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		if (databaseFields != null && databaseFields.size() > 0) {
			nextDatabaseField: for (String databaseField : databaseFields) {
				if (StringUtils.startsWithIgnoreCase(databaseField, IConstants.RESPONSE_CODE_CHG_IND_TO)) {
					continue nextDatabaseField;
				}
				if (isFirstField == true) {
					isFirstField = false;
					stringBuilder.append(IConstants.ONE_SPACE);
				} else {
					stringBuilder.append(IConstants.COMMA);
				}
				stringBuilder.append(databaseField);
			}
		} else {
			stringBuilder.append(" *");
		}
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("     track_date = '" + trackDateString + "'");
		stringBuilder.append(" and domain_id = ?");
		stringBuilder.append(" and url_hash = URLHash(?)");
		stringBuilder.append(" and url_murmur_hash = murmurHash3_64(?)");
		stringBuilder.append(" and current_crawl_timestamp = '" + crawlTimestampString + "'");
		String sqlString = stringBuilder.toString();
		FormatUtils.getInstance().logMemoryUsage("get() sqlString=" + sqlString);
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		retryCount = 0;
		int index = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setString(index++, trimmedString);
				preparedStatement.setString(index++, trimmedString);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					targetUrlChangeIndClickHouseEntity = CrawlerUtils.getInstance().getTargetUrlChangeIndClickHouseEntityFromResultSet(resultSet, databaseFields);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("get() trackDateString=" + trackDateString + ",domainId=" + domainId + ",urlString=" + urlString
							+ ",crawlTimestampString=" + crawlTimestampString + ",e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("get() ends. domainId=" + domainId + ",urlString=" + urlString + ",crawlTimestampString=" + crawlTimestampString
				+ ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return targetUrlChangeIndClickHouseEntity;
	}

	public synchronized List<TargetUrlChangeIndClickHouseEntity> getAlertList(String ip, int domainId, String trackDateString, String startCrawlTimestampString,
			String endCrawlTimestampString, List<String> databaseFields, Integer pageTagId) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("getAlertList() ip=" + ip + " begins, domainId=" + domainId + ",startCrawlTimestampString=" + startCrawlTimestampString
				+ ",endCrawlTimestampString=" + endCrawlTimestampString + ",databaseFields.size()=" + databaseFields.size() + ",pageTagId=" + pageTagId);
		List<TargetUrlChangeIndClickHouseEntity> targetUrlChangeIndClickHouseEntityList = new ArrayList<TargetUrlChangeIndClickHouseEntity>();
		TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select change_indicator, count(*) as total ");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(null));
		stringBuilder.append(" where");
		stringBuilder.append("     track_date = '" + trackDateString + "'");
		stringBuilder.append(" and domain_id = ?");
		if (StringUtils.isNotBlank(startCrawlTimestampString) && StringUtils.isNotBlank(endCrawlTimestampString)) {
			stringBuilder.append(" and current_crawl_timestamp >= '" + startCrawlTimestampString + "'");
			stringBuilder.append(" and current_crawl_timestamp <= '" + endCrawlTimestampString + "'");
		}
		if (pageTagId != null && pageTagId.intValue() > 0) {
			stringBuilder.append(" and (dictGetUInt64('file_dic_page_tag_murmur3hash', 'target_url_id', (toUInt64(" + domainId + "), toUInt64(" + pageTagId
					+ "), murmurHash3_64(url))) > 0)");
		}
		stringBuilder.append(" and change_indicator in (");
		stringBuilder.append(getChangeIndicatorsPredicateFilter(databaseFields));
		stringBuilder.append(")");
		stringBuilder.append(" group by");
		stringBuilder.append("   change_indicator");
		String sqlString = stringBuilder.toString();
		//if (isDebug == true) {
		//	FormatUtils.getInstance().logMemoryUsage("getAlertList() sqlString=" + sqlString);
		//}
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		retryCount = 0;
		int index = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setInt(index++, domainId);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					targetUrlChangeIndClickHouseEntity = new TargetUrlChangeIndClickHouseEntity();
					targetUrlChangeIndClickHouseEntity.setChangeIndicator(resultSet.getString(IConstants.CHANGE_INDICATOR));
					targetUrlChangeIndClickHouseEntity.setTotal(resultSet.getInt(IConstants.TOTAL));
					targetUrlChangeIndClickHouseEntityList.add(targetUrlChangeIndClickHouseEntity);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance()
							.logMemoryUsage("getAlertList() trackDateString=" + trackDateString + ",domainId=" + domainId + ",startCrawlTimestampString="
									+ startCrawlTimestampString + ",endCrawlTimestampString=" + endCrawlTimestampString + ",e.getMessage()=" + e.getMessage()
									+ ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		FormatUtils.getInstance()
				.logMemoryUsage("getAlertList() ip=" + ip + " ends, domainId=" + domainId + ",startCrawlTimestampString=" + startCrawlTimestampString
						+ ",endCrawlTimestampString=" + endCrawlTimestampString + ",pageTagId=" + pageTagId + ",targetUrlChangeIndClickHouseEntityList.size()="
						+ targetUrlChangeIndClickHouseEntityList.size() + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return targetUrlChangeIndClickHouseEntityList;
	}

	private String getChangeIndicatorsPredicateFilter(List<String> databaseFields) {
		Map<String, String> changeTrackingIndicatorMap = new HashMap<String, String>();
		List<String> changeTrackingIndicatorList = ContentGuardUtils.getInstance().getChangeTrackingIndicatorList();
		for (String changeTrackingIndicator : changeTrackingIndicatorList) {
			changeTrackingIndicatorMap.put(changeTrackingIndicator, null);
		}
		StringBuilder stringBuilder = new StringBuilder();
		nextDatabaseField: for (String databaseField : databaseFields) {
			if (StringUtils.startsWithIgnoreCase(databaseField, IConstants.RESPONSE_CODE_CHG_IND_TO)) {
				continue nextDatabaseField;
			}
			if (changeTrackingIndicatorMap.containsKey(databaseField) == false) {
				continue nextDatabaseField;
			}
			if (stringBuilder.length() > 0) {
				stringBuilder.append(IConstants.COMMA);
			}
			stringBuilder.append(IConstants.SINGLE_QUOTE);
			stringBuilder.append(databaseField);
			stringBuilder.append(IConstants.SINGLE_QUOTE);
		}
		return stringBuilder.toString();
	}

	public synchronized List<TargetUrlChangeHourlyTotals> getHourlyTotals(int domainId, String startCrawlTimestampString, String endCrawlTimestampString,
			List<String> databaseFields, String[] changeIndicatorArray, Integer[] pageTagIdArray, String contentTypePredicate, String urlPredicate,
			String responseCodePredicate) throws Exception {
		//long startTimestamp = System.currentTimeMillis();
		//if (isDebug == true) {
		//	FormatUtils.getInstance()
		//			.logMemoryUsage("getHourlyTotals() begins. domainId=" + domainId + ",startCrawlTimestampString=" + startCrawlTimestampString
		//					+ ",endCrawlTimestampString=" + endCrawlTimestampString + ",contentTypePredicate=" + contentTypePredicate + ",urlPredicate=" + urlPredicate
		//					+ ",responseCodePredicate=" + responseCodePredicate);
		//}
		List<TargetUrlChangeHourlyTotals> targetUrlChangeHourlyTotalsList = new ArrayList<TargetUrlChangeHourlyTotals>();
		TargetUrlChangeHourlyTotals targetUrlChangeHourlyTotals = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;

		// map key = crawl date hour (yyyy-mm-dd hh:00:00)
		// map value = targetUrlChangeHourlyTotals
		SortedMap<String, TargetUrlChangeHourlyTotals> crawlDateHourTargetUrlChangeHourlyTotalsMap = new TreeMap<String, TargetUrlChangeHourlyTotals>();

		String startTrackDateString = StringUtils.substringBefore(startCrawlTimestampString, IConstants.ONE_SPACE);
		String endTrackDateString = StringUtils.substringBefore(endCrawlTimestampString, IConstants.ONE_SPACE);

		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("  substring(toString(current_crawl_timestamp), 1, 13) as " + IConstants.CRAWL_DATE_HOUR + ",");
		stringBuilder.append("  change_indicator,");
		stringBuilder.append("  count(*) as total");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(null));
		stringBuilder.append(" where");
		stringBuilder.append("     track_date >= '" + startTrackDateString + "'");
		stringBuilder.append(" and track_date <= '" + endTrackDateString + "'");
		stringBuilder.append(" and domain_id = ?");
		stringBuilder.append(" and current_crawl_timestamp >= '" + startCrawlTimestampString + "'");
		stringBuilder.append(" and current_crawl_timestamp <= '" + endCrawlTimestampString + "'");
		if (pageTagIdArray != null && pageTagIdArray.length > 0) {
			stringBuilder.append(getPageTagIdsPredicate(domainId, pageTagIdArray));
		}
		if (changeIndicatorArray != null && changeIndicatorArray.length > 0) {
			stringBuilder.append(getChangeIndicatorsPredicate(changeIndicatorArray, responseCodePredicate));
		}
		if (StringUtils.isNotBlank(urlPredicate)) {
			stringBuilder.append(urlPredicate);
		}
		if (StringUtils.isNotBlank(contentTypePredicate)) {
			stringBuilder.append(" and " + contentTypePredicate);
		}
		stringBuilder.append(" group by");
		stringBuilder.append("  crawl_date_hour,");
		stringBuilder.append("  change_indicator");
		stringBuilder.append(" order by");
		stringBuilder.append("  crawl_date_hour,");
		stringBuilder.append("  change_indicator");
		String sqlString = stringBuilder.toString();
		//if (isDebug == true) {
		//	FormatUtils.getInstance().logMemoryUsage("getHourlyTotals() sqlString=" + sqlString);
		//}
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		retryCount = 0;
		int index = 0;
		databaseFields.add(IConstants.CRAWL_DATE_HOUR);
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setInt(index++, domainId);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					// update 'crawlDateHourTargetUrlChangeHourlyTotalsMap' using pass by reference
					CrawlerUtils.getInstance().updateStartHourTargetUrlChangeHourlyTotalsMap(crawlDateHourTargetUrlChangeHourlyTotalsMap, resultSet);
				}
				for (String crawlDateHour : crawlDateHourTargetUrlChangeHourlyTotalsMap.keySet()) {
					targetUrlChangeHourlyTotals = crawlDateHourTargetUrlChangeHourlyTotalsMap.get(crawlDateHour);
					targetUrlChangeHourlyTotalsList.add(targetUrlChangeHourlyTotals);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				if (StringUtils.containsIgnoreCase(e.getMessage(), IConstants.CANNOT_COMPILE_REGEXP)) {
					retryCount = maximumRetryCounts;
				} else {
					retryCount++;
					if (retryCount >= maximumRetryCounts) {
						e.printStackTrace();
						throw e;
					} else {
						FormatUtils.getInstance()
								.logMemoryUsage("getHourlyTotals() domainId=" + domainId + ",startTrackDateString=" + startTrackDateString
										+ ",startCrawlTimestampString=" + startCrawlTimestampString + ",endTrackDateString=" + endTrackDateString
										+ ",endCrawlTimestampString=" + endCrawlTimestampString + ",e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
						try {
							Thread.sleep(retryWaitMilliseconds);
						} catch (InterruptedException e1) {
							e1.printStackTrace();
						}
					}
				}
			}
		}
		//if (isDebug == true) {
		//	FormatUtils.getInstance()
		//			.logMemoryUsage("getHourlyTotals() ends. domainId=" + domainId + ",startCrawlTimestampString=" + startCrawlTimestampString
		//					+ ",endCrawlTimestampString=" + endCrawlTimestampString + ",targetUrlChangeHourlyTotalsList.size()="
		//					+ targetUrlChangeHourlyTotalsList.size() + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		//}
		return targetUrlChangeHourlyTotalsList;
	}

	public synchronized List<TargetUrlChangeIndClickHouseEntity> getIndicatorDetail(int domainId, String startCrawlTimestampString, String endCrawlTimestampString,
			List<String> databaseFields, int sortBy, int rows, int offset, boolean isTotal, String[] changeIndicatorArray, Integer[] pageTagIdArray,
			String contentTypePredicate, String urlPredicate, String responseCodePredicate) throws Exception {
		//long startTimestamp = System.currentTimeMillis();
		//if (isDebug == true) {
		//	FormatUtils.getInstance()
		//			.logMemoryUsage("getIndicatorDetail() begins. domainId=" + domainId + ",startCrawlTimestampString=" + startCrawlTimestampString
		//					+ ",endCrawlTimestampString=" + endCrawlTimestampString + ",sortBy=" + sortBy + ",rows=" + rows + ",offset=" + offset
		//					+ ",contentTypePredicate=" + contentTypePredicate + ",urlPredicate=" + urlPredicate + ",responseCodePredicate=" + responseCodePredicate);
		//}
		List<TargetUrlChangeIndClickHouseEntity> targetUrlChangeIndClickHouseEntityList = new ArrayList<TargetUrlChangeIndClickHouseEntity>();
		TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;

		String startTrackDateString = StringUtils.substringBefore(startCrawlTimestampString, IConstants.ONE_SPACE);
		String endTrackDateString = StringUtils.substringBefore(endCrawlTimestampString, IConstants.ONE_SPACE);

		boolean isFirstField = true;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		if (isTotal == true) {
			stringBuilder.append(" count(*) as " + IConstants.TOTAL);
		} else {
			if (databaseFields != null && databaseFields.size() > 0) {
				nextDatabaseField: for (String databaseField : databaseFields) {
					if (StringUtils.startsWithIgnoreCase(databaseField, IConstants.RESPONSE_CODE_CHG_IND_TO)) {
						continue nextDatabaseField;
					}
					if (isFirstField == true) {
						isFirstField = false;
						stringBuilder.append(IConstants.ONE_SPACE);
					} else {
						stringBuilder.append(IConstants.COMMA);
					}
					stringBuilder.append(databaseField);
				}
			} else {
				stringBuilder.append(" *");
			}
		}
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(null));
		stringBuilder.append(" where");
		stringBuilder.append("     track_date >= '" + startTrackDateString + "'");
		stringBuilder.append(" and track_date <= '" + endTrackDateString + "'");
		stringBuilder.append(" and domain_id = ?");
		stringBuilder.append(" and current_crawl_timestamp >= '" + startCrawlTimestampString + "'");
		stringBuilder.append(" and current_crawl_timestamp <= '" + endCrawlTimestampString + "'");
		if (pageTagIdArray != null && pageTagIdArray.length > 0) {
			stringBuilder.append(getPageTagIdsPredicate(domainId, pageTagIdArray));
		}
		if (changeIndicatorArray != null && changeIndicatorArray.length > 0) {
			stringBuilder.append(getChangeIndicatorsPredicate(changeIndicatorArray, responseCodePredicate));
		}
		if (StringUtils.isNotBlank(urlPredicate)) {
			stringBuilder.append(urlPredicate);
		}
		if (StringUtils.isNotBlank(contentTypePredicate)) {
			stringBuilder.append(" and " + contentTypePredicate);
		}
		if (isTotal == false) {
			stringBuilder.append(" order by");
			if (sortBy == IConstants.SORT_BY_URL_ASC) {
				stringBuilder.append(" url, current_crawl_timestamp");
			} else if (sortBy == IConstants.SORT_BY_URL_DESC) {
				stringBuilder.append(" url desc, current_crawl_timestamp");
			} else if (sortBy == IConstants.SORT_BY_CRAWL_TIMESTAMP_ASC) {
				stringBuilder.append(" current_crawl_timestamp, url");
			} else if (sortBy == IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC) {
				stringBuilder.append(" current_crawl_timestamp desc, url");
			} else if (sortBy == IConstants.SORT_BY_CHANGE_INDICATOR_ASC) {
				stringBuilder.append(" change_indicator, url");
			} else if (sortBy == IConstants.SORT_BY_CHANGE_INDICATOR_DESC) {
				stringBuilder.append(" change_indicator desc, url");
			} else if (sortBy == IConstants.SORT_BY_CHANGE_TYPE_ASC) {
				stringBuilder.append(" change_type, change_indicator, url");
			} else if (sortBy == IConstants.SORT_BY_CHANGE_TYPE_DESC) {
				stringBuilder.append(" change_type desc, change_indicator, url");
			} else if (sortBy == IConstants.SORT_BY_SEVERITY_ASC) {
				stringBuilder.append(" critical_ind, change_indicator, url");
			} else if (sortBy == IConstants.SORT_BY_SEVERITY_DESC) {
				stringBuilder.append(" critical_ind desc, change_indicator, url");
			}
			stringBuilder.append(" limit " + rows);
			stringBuilder.append(" offset " + offset);
		}
		String sqlString = stringBuilder.toString();
		//if (isDebug == true) {
		//	FormatUtils.getInstance().logMemoryUsage("getIndicatorDetail() sqlString=" + sqlString);
		//}
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		retryCount = 0;
		int index = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setInt(index++, domainId);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					if (isTotal == true) {
						targetUrlChangeIndClickHouseEntity = new TargetUrlChangeIndClickHouseEntity();
						targetUrlChangeIndClickHouseEntity.setTotal(resultSet.getInt(IConstants.TOTAL));
					} else {
						targetUrlChangeIndClickHouseEntity = CrawlerUtils.getInstance().getTargetUrlChangeIndClickHouseEntityFromResultSet(resultSet, databaseFields);
					}
					targetUrlChangeIndClickHouseEntityList.add(targetUrlChangeIndClickHouseEntity);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				if (StringUtils.containsIgnoreCase(e.getMessage(), IConstants.CANNOT_COMPILE_REGEXP)) {
					retryCount = maximumRetryCounts;
				} else {
					retryCount++;
					if (retryCount >= maximumRetryCounts) {
						e.printStackTrace();
						throw e;
					} else {
						FormatUtils.getInstance()
								.logMemoryUsage("getIndicatorDetail() domainId=" + domainId + ",startTrackDateString=" + startTrackDateString
										+ ",startCrawlTimestampString=" + startCrawlTimestampString + ",endTrackDateString=" + endTrackDateString
										+ ",endCrawlTimestampString=" + endCrawlTimestampString + ",sortBy=" + sortBy + ",rows=" + rows + ",offset=" + offset
										+ ",e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
						try {
							Thread.sleep(retryWaitMilliseconds);
						} catch (InterruptedException e1) {
							e1.printStackTrace();
						}
					}
				}
			}
		}
		//if (isDebug == true) {
		//	FormatUtils.getInstance()
		//			.logMemoryUsage("getIndicatorDetail() ends. domainId=" + domainId + ",startCrawlTimestampString=" + startCrawlTimestampString
		//					+ ",endCrawlTimestampString=" + endCrawlTimestampString + ",sortBy=" + sortBy + ",rows=" + rows + ",offset=" + offset
		//					+ ",targetUrlChangeIndClickHouseEntityList.size()=" + targetUrlChangeIndClickHouseEntityList.size() + ",elapsed(ms.)="
		//					+ (System.currentTimeMillis() - startTimestamp));
		//}
		return targetUrlChangeIndClickHouseEntityList;
	}

	private String getPageTagIdsPredicate(int domainId, Integer[] pageTagIdArray) {
		StringBuilder stringBuilder = null;
		for (Integer pageTagId : pageTagIdArray) {
			if (stringBuilder == null) {
				stringBuilder = new StringBuilder();
				stringBuilder.append(" and ((dictGetUInt64('file_dic_page_tag_murmur3hash', 'target_url_id', (toUInt64(" + domainId + "), toUInt64(" + pageTagId
						+ "), murmurHash3_64(url))) > 0)");
			} else {
				stringBuilder.append(" or (dictGetUInt64('file_dic_page_tag_murmur3hash', 'target_url_id', (toUInt64(" + domainId + "), toUInt64(" + pageTagId
						+ "), murmurHash3_64(url))) > 0)");
			}
		}
		stringBuilder.append(")");
		return stringBuilder.toString();
	}

	private String getChangeIndicatorsPredicate(String[] changeIndicatorArray, String responseCodePredicate) {
		StringBuilder stringBuilder = new StringBuilder();
		boolean isFirstChangeIndicator = true;
		if (changeIndicatorArray != null && changeIndicatorArray.length > 0) {
			if (changeIndicatorArray.length == 1 && StringUtils.equalsIgnoreCase(changeIndicatorArray[0], IConstants.RESPONSE_CODE_CHG_IND) == true
					&& StringUtils.isNotBlank(responseCodePredicate)) {
				stringBuilder.append(" and");
				stringBuilder.append(responseCodePredicate);
			} else {
				if (StringUtils.isNotBlank(responseCodePredicate)) {
					stringBuilder.append(" and (change_indicator in (");
				} else {
					stringBuilder.append(" and change_indicator in (");
				}
				nextFilterChangeIndicator: for (String filterChangeIndicator : changeIndicatorArray) {
					if (StringUtils.isNotBlank(responseCodePredicate) && StringUtils.equalsIgnoreCase(filterChangeIndicator, IConstants.RESPONSE_CODE_CHG_IND)) {
						continue nextFilterChangeIndicator;
					}
					if (StringUtils.startsWithIgnoreCase(filterChangeIndicator, IConstants.RESPONSE_CODE_CHG_IND_TO)) {
						continue nextFilterChangeIndicator;
					}
					if (isFirstChangeIndicator == true) {
						isFirstChangeIndicator = false;
					} else {
						stringBuilder.append(IConstants.COMMA);
					}
					stringBuilder.append(IConstants.SINGLE_QUOTE);
					stringBuilder.append(filterChangeIndicator);
					stringBuilder.append(IConstants.SINGLE_QUOTE);
				}
				stringBuilder.append(" )");
				if (StringUtils.isNotBlank(responseCodePredicate)) {
					stringBuilder.append(" or");
					stringBuilder.append(responseCodePredicate);
					stringBuilder.append(" )");
				}
			}
			return stringBuilder.toString();
		} else {
			return IConstants.EMPTY_STRING;
		}
	}

	public synchronized List<TargetUrlResponseCodeHourlyTotals> getResponseCodeHourlyTotals(int domainId, String startCrawlTimestampString,
			String endCrawlTimestampString, List<String> databaseFields, String[] changeIndicatorArray, Integer[] pageTagIdArray, String contentTypePredicate,
			String urlPredicate, String responseCodePredicate) throws Exception {
		//long startTimestamp = System.currentTimeMillis();
		//if (isDebug == true) {
		//	FormatUtils.getInstance()
		//			.logMemoryUsage("getResponseCodeHourlyTotals() begins. domainId=" + domainId + ",startCrawlTimestampString=" + startCrawlTimestampString
		//					+ ",endCrawlTimestampString=" + endCrawlTimestampString + ",contentTypePredicate=" + contentTypePredicate + ",urlPredicate=" + urlPredicate
		//					+ ",responseCodePredicate=" + responseCodePredicate);
		//}
		List<TargetUrlResponseCodeHourlyTotals> targetUrlResponseCodeHourlyTotalsList = new ArrayList<TargetUrlResponseCodeHourlyTotals>();
		TargetUrlResponseCodeHourlyTotals targetUrlResponseCodeHourlyTotals = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;

		String startTrackDateString = StringUtils.substringBefore(startCrawlTimestampString, IConstants.ONE_SPACE);
		String endTrackDateString = StringUtils.substringBefore(endCrawlTimestampString, IConstants.ONE_SPACE);

		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("  substring(toString(current_crawl_timestamp), 1, 13) as " + IConstants.CRAWL_DATE_HOUR + ",");
		stringBuilder.append("  response_code_previous,");
		stringBuilder.append("  response_code_current,");
		stringBuilder.append("  count(*) as total");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(null));
		stringBuilder.append(" where");
		stringBuilder.append("     track_date >= '" + startTrackDateString + "'");
		stringBuilder.append(" and track_date <= '" + endTrackDateString + "'");
		stringBuilder.append(" and domain_id = ?");
		stringBuilder.append(" and current_crawl_timestamp >= '" + startCrawlTimestampString + "'");
		stringBuilder.append(" and current_crawl_timestamp <= '" + endCrawlTimestampString + "'");
		if (pageTagIdArray != null && pageTagIdArray.length > 0) {
			stringBuilder.append(getPageTagIdsPredicate(domainId, pageTagIdArray));
		}
		if (StringUtils.isNotBlank(urlPredicate)) {
			stringBuilder.append(urlPredicate);
		}
		if (StringUtils.isNotBlank(contentTypePredicate)) {
			stringBuilder.append(" and " + contentTypePredicate);
		}
		if (StringUtils.isNotBlank(responseCodePredicate)) {
			stringBuilder.append(" and " + responseCodePredicate);
		} else {
			stringBuilder.append(" and change_indicator = '" + IConstants.RESPONSE_CODE_CHG_IND + "'");
		}
		stringBuilder.append(" group by");
		stringBuilder.append("  crawl_date_hour,");
		stringBuilder.append("  response_code_previous,");
		stringBuilder.append("  response_code_current");
		stringBuilder.append(" order by");
		stringBuilder.append("  crawl_date_hour,");
		stringBuilder.append("  response_code_previous,");
		stringBuilder.append("  response_code_current");
		String sqlString = stringBuilder.toString();
		//if (isDebug == true) {
		//	FormatUtils.getInstance().logMemoryUsage("getResponseCodeHourlyTotals() sqlString=" + sqlString);
		//}
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		retryCount = 0;
		int index = 0;
		databaseFields.add(IConstants.CRAWL_DATE_HOUR);
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setInt(index++, domainId);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					targetUrlResponseCodeHourlyTotals = new TargetUrlResponseCodeHourlyTotals();

					// crawl_date_hour
					targetUrlResponseCodeHourlyTotals.setCrawlDateHour(resultSet.getString(IConstants.CRAWL_DATE_HOUR) + IConstants.CRAWL_TIMESTAMP_START_OF_HOUR);

					// response_code_previous
					targetUrlResponseCodeHourlyTotals.setResponseCodePrevious(resultSet.getString(IConstants.RESPONSE_CODE + IConstants.UNDERSCORE_PREVIOUS));

					// response_code_current
					targetUrlResponseCodeHourlyTotals.setResponseCodeCurrent(resultSet.getString(IConstants.RESPONSE_CODE + IConstants.UNDERSCORE_CURRENT));

					// total
					targetUrlResponseCodeHourlyTotals.setTotalUrls(resultSet.getInt(IConstants.TOTAL));

					targetUrlResponseCodeHourlyTotalsList.add(targetUrlResponseCodeHourlyTotals);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				if (StringUtils.containsIgnoreCase(e.getMessage(), IConstants.CANNOT_COMPILE_REGEXP)) {
					retryCount = maximumRetryCounts;
				} else {
					retryCount++;
					if (retryCount >= maximumRetryCounts) {
						e.printStackTrace();
						throw e;
					} else {
						FormatUtils.getInstance()
								.logMemoryUsage("getResponseCodeHourlyTotals() domainId=" + domainId + ",startTrackDateString=" + startTrackDateString
										+ ",startCrawlTimestampString=" + startCrawlTimestampString + ",endTrackDateString=" + endTrackDateString
										+ ",endCrawlTimestampString=" + endCrawlTimestampString + ",e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
						try {
							Thread.sleep(retryWaitMilliseconds);
						} catch (InterruptedException e1) {
							e1.printStackTrace();
						}
					}
				}
			}
		}
		//if (isDebug == true) {
		//	FormatUtils.getInstance()
		//			.logMemoryUsage("getResponseCodeHourlyTotals() ends. domainId=" + domainId + ",startCrawlTimestampString=" + startCrawlTimestampString
		//					+ ",endCrawlTimestampString=" + endCrawlTimestampString + ",targetUrlResponseCodeHourlyTotalsList.size()="
		//					+ targetUrlResponseCodeHourlyTotalsList.size() + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		//}
		return targetUrlResponseCodeHourlyTotalsList;
	}

	public synchronized List<TargetUrlChangeIndClickHouseEntity> getUrlSummary(int domainId, String startCrawlTimestampString, String endCrawlTimestampString,
			int sortBy, int rows, int offset, boolean isTotal, String[] changeIndicatorArray, Integer[] pageTagIdArray, String contentTypePredicate,
			String urlPredicate, String responseCodePredicate) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance()
				.logMemoryUsage("getUrlSummary() begins. domainId=" + domainId + ",startCrawlTimestampString=" + startCrawlTimestampString + ",endCrawlTimestampString="
						+ endCrawlTimestampString + ",sortBy=" + sortBy + ",rows=" + rows + ",offset=" + offset + ",contentTypePredicate=" + contentTypePredicate
						+ ",urlPredicate=" + urlPredicate + ",responseCodePredicate=" + responseCodePredicate);
		List<TargetUrlChangeIndClickHouseEntity> targetUrlChangeIndClickHouseEntityList = new ArrayList<TargetUrlChangeIndClickHouseEntity>();
		TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;

		String startTrackDateString = StringUtils.substringBefore(startCrawlTimestampString, IConstants.ONE_SPACE);
		String endTrackDateString = StringUtils.substringBefore(endCrawlTimestampString, IConstants.ONE_SPACE);

		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		if (isTotal == true) {
			stringBuilder.append(" count(distinct url) as " + IConstants.TOTAL);
		} else {
			stringBuilder.append(" url,");
			stringBuilder.append(" url_murmur_hash,");
			stringBuilder.append(" count(*) as total_changes,");
			stringBuilder.append(" sum(case when critical_ind = 1 then 1 else 0 end) as total_severity_critical,");
			stringBuilder.append(" sum(case when critical_ind = 2 then 1 else 0 end) as total_severity_high,");
			stringBuilder.append(" sum(case when critical_ind = 3 then 1 else 0 end) as total_severity_medium,");
			stringBuilder.append(" sum(case when critical_ind = 4 then 1 else 0 end) as total_severity_low,");
			stringBuilder.append(" sum(case when change_type = 1 then 1 else 0 end) as total_change_type_added,");
			stringBuilder.append(" sum(case when change_type = 2 then 1 else 0 end) as total_change_type_modified,");
			stringBuilder.append(" sum(case when change_type = 3 then 1 else 0 end) as total_change_type_removed");
		}
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(null));
		stringBuilder.append(" where");
		stringBuilder.append("     track_date >= '" + startTrackDateString + "'");
		stringBuilder.append(" and track_date <= '" + endTrackDateString + "'");
		stringBuilder.append(" and domain_id = ?");
		stringBuilder.append(" and current_crawl_timestamp >= '" + startCrawlTimestampString + "'");
		stringBuilder.append(" and current_crawl_timestamp <= '" + endCrawlTimestampString + "'");
		if (pageTagIdArray != null && pageTagIdArray.length > 0) {
			stringBuilder.append(getPageTagIdsPredicate(domainId, pageTagIdArray));
		}
		if (changeIndicatorArray != null && changeIndicatorArray.length > 0) {
			stringBuilder.append(getChangeIndicatorsPredicate(changeIndicatorArray, responseCodePredicate));
		}
		if (StringUtils.isNotBlank(urlPredicate)) {
			stringBuilder.append(urlPredicate);
		}
		if (StringUtils.isNotBlank(contentTypePredicate)) {
			stringBuilder.append(" and " + contentTypePredicate);
		}
		if (isTotal == false) {
			stringBuilder.append(" group by");
			stringBuilder.append("     url,");
			stringBuilder.append("     url_murmur_hash");
			stringBuilder.append(" order by");
			if (sortBy == IConstants.SORT_BY_URL_ASC) {
				stringBuilder.append(" url");
			} else if (sortBy == IConstants.SORT_BY_URL_DESC) {
				stringBuilder.append(" url desc");
			} else if (sortBy == IConstants.SORT_BY_TOTAL_CHANGES_ASC) {
				stringBuilder.append(" total_changes, url");
			} else if (sortBy == IConstants.SORT_BY_TOTAL_CHANGES_DESC) {
				stringBuilder.append(" total_changes desc, url");
			} else if (sortBy == IConstants.SORT_BY_TOTAL_SEVERITY_CRITICAL_ASC) {
				stringBuilder.append(" total_severity_critical, url");
			} else if (sortBy == IConstants.SORT_BY_TOTAL_SEVERITY_CRITICAL_DESC) {
				stringBuilder.append(" total_severity_critical desc, url");
			} else if (sortBy == IConstants.SORT_BY_TOTAL_SEVERITY_HIGH_ASC) {
				stringBuilder.append(" total_severity_high, url");
			} else if (sortBy == IConstants.SORT_BY_TOTAL_SEVERITY_HIGH_DESC) {
				stringBuilder.append(" total_severity_high desc, url");
			} else if (sortBy == IConstants.SORT_BY_TOTAL_SEVERITY_MEDIUM_ASC) {
				stringBuilder.append(" total_severity_medium, url");
			} else if (sortBy == IConstants.SORT_BY_TOTAL_SEVERITY_MEDIUM_DESC) {
				stringBuilder.append(" total_severity_medium desc, url");
			} else if (sortBy == IConstants.SORT_BY_TOTAL_SEVERITY_LOW_ASC) {
				stringBuilder.append(" total_severity_low, url");
			} else if (sortBy == IConstants.SORT_BY_TOTAL_SEVERITY_LOW_DESC) {
				stringBuilder.append(" total_severity_low desc, url");
			} else if (sortBy == IConstants.SORT_BY_TOTAL_CHANGE_TYPE_ADDED_ASC) {
				stringBuilder.append(" total_change_type_added, url");
			} else if (sortBy == IConstants.SORT_BY_TOTAL_CHANGE_TYPE_ADDED_DESC) {
				stringBuilder.append(" total_change_type_added desc, url");
			} else if (sortBy == IConstants.SORT_BY_TOTAL_CHANGE_TYPE_MODIFIED_ASC) {
				stringBuilder.append(" total_change_type_modified, url");
			} else if (sortBy == IConstants.SORT_BY_TOTAL_CHANGE_TYPE_MODIFIED_DESC) {
				stringBuilder.append(" total_change_type_modified desc, url");
			} else if (sortBy == IConstants.SORT_BY_TOTAL_CHANGE_TYPE_REMOVED_ASC) {
				stringBuilder.append(" total_change_type_removed, url");
			} else if (sortBy == IConstants.SORT_BY_TOTAL_CHANGE_TYPE_REMOVED_DESC) {
				stringBuilder.append(" total_change_type_removed desc, url");
			}
			stringBuilder.append(" limit " + rows);
			stringBuilder.append(" offset " + offset);
		}
		String sqlString = stringBuilder.toString();
		//if (isDebug == true) {
		//	FormatUtils.getInstance().logMemoryUsage("getUrlSummary() sqlString=" + sqlString);
		//}
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		retryCount = 0;
		int index = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setInt(index++, domainId);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					targetUrlChangeIndClickHouseEntity = new TargetUrlChangeIndClickHouseEntity();
					if (isTotal == true) {
						targetUrlChangeIndClickHouseEntity.setTotal(resultSet.getInt(IConstants.TOTAL));
					} else {
						targetUrlChangeIndClickHouseEntity.setUrl(resultSet.getString(IConstants.URL));
						targetUrlChangeIndClickHouseEntity.setUrlMurmurHash(resultSet.getString(IConstants.URL_MURMUR_HASH));
						targetUrlChangeIndClickHouseEntity.setTotalChanges(resultSet.getInt(IConstants.TOTAL_CHANGES));
						targetUrlChangeIndClickHouseEntity.setTotalSeverityCritical(resultSet.getInt(IConstants.TOTAL_SEVERITY_CRITICAL));
						targetUrlChangeIndClickHouseEntity.setTotalSeverityHigh(resultSet.getInt(IConstants.TOTAL_SEVERITY_HIGH));
						targetUrlChangeIndClickHouseEntity.setTotalSeverityMedium(resultSet.getInt(IConstants.TOTAL_SEVERITY_MEDIUM));
						targetUrlChangeIndClickHouseEntity.setTotalSeverityLow(resultSet.getInt(IConstants.TOTAL_SEVERITY_LOW));
						targetUrlChangeIndClickHouseEntity.setTotalChangeTypeAdded(resultSet.getInt(IConstants.TOTAL_CHANGE_TYPE_ADDED));
						targetUrlChangeIndClickHouseEntity.setTotalChangeTypeModified(resultSet.getInt(IConstants.TOTAL_CHANGE_TYPE_MODIFIED));
						targetUrlChangeIndClickHouseEntity.setTotalChangeTypeRemoved(resultSet.getInt(IConstants.TOTAL_CHANGE_TYPE_REMOVED));
					}
					targetUrlChangeIndClickHouseEntityList.add(targetUrlChangeIndClickHouseEntity);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				if (StringUtils.containsIgnoreCase(e.getMessage(), IConstants.CANNOT_COMPILE_REGEXP)) {
					retryCount = maximumRetryCounts;
				} else {
					retryCount++;
					if (retryCount >= maximumRetryCounts) {
						e.printStackTrace();
						throw e;
					} else {
						FormatUtils.getInstance()
								.logMemoryUsage("getUrlSummary() domainId=" + domainId + ",startTrackDateString=" + startTrackDateString + ",startCrawlTimestampString="
										+ startCrawlTimestampString + ",endTrackDateString=" + endTrackDateString + ",endCrawlTimestampString="
										+ endCrawlTimestampString + ",sortBy=" + sortBy + ",rows=" + rows + ",offset=" + offset + ",e.getMessage()=" + e.getMessage()
										+ ",retryCount=" + retryCount);
						try {
							Thread.sleep(retryWaitMilliseconds);
						} catch (InterruptedException e1) {
							e1.printStackTrace();
						}
					}
				}
			}
		}
		FormatUtils.getInstance()
				.logMemoryUsage("getUrlSummary() ends. domainId=" + domainId + ",startCrawlTimestampString=" + startCrawlTimestampString + ",endCrawlTimestampString="
						+ endCrawlTimestampString + ",sortBy=" + sortBy + ",rows=" + rows + ",offset=" + offset + ",targetUrlChangeIndClickHouseEntityList.size()="
						+ targetUrlChangeIndClickHouseEntityList.size() + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return targetUrlChangeIndClickHouseEntityList;
	}

	public synchronized List<TargetUrlChangeIndClickHouseEntity> getUrlsWithMostChanges(String ip, int domainId, String trackDateString,
			String startCrawlTimestampString, String endCrawlTimestampString, List<String> databaseFields, Integer[] pageTagIdArray) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("getUrlsWithMostChanges() begins. ip=" + ip + ",domainId=" + domainId + ",trackDateString=" + trackDateString
				+ ",startCrawlTimestampString=" + startCrawlTimestampString + ",endCrawlTimestampString=" + endCrawlTimestampString);
		List<TargetUrlChangeIndClickHouseEntity> targetUrlChangeIndClickHouseEntityList = new ArrayList<TargetUrlChangeIndClickHouseEntity>();
		TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		boolean isFirstPageTagId = false;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select url, count(*) as total_changes ");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(null));
		stringBuilder.append(" where");
		stringBuilder.append("     track_date = '" + trackDateString + "'");
		stringBuilder.append(" and domain_id = ?");
		if (StringUtils.isNotBlank(startCrawlTimestampString) && StringUtils.isNotBlank(endCrawlTimestampString)) {
			stringBuilder.append(" and current_crawl_timestamp >= '" + startCrawlTimestampString + "'");
			stringBuilder.append(" and current_crawl_timestamp <= '" + endCrawlTimestampString + "'");
		}

		if (pageTagIdArray != null && pageTagIdArray.length > 0) {
			isFirstPageTagId = true;
			stringBuilder.append(" and (");
			for (int pageTagId : pageTagIdArray) {
				if (isFirstPageTagId == true) {
					isFirstPageTagId = false;
				} else {
					stringBuilder.append(" or ");
				}
				if (isDebug == true) {
					System.out.println("getUrlsWithMostChanges() pageTagId=" + pageTagId);
				}
				stringBuilder.append("(dictGetUInt64('file_dic_page_tag_murmur3hash', 'target_url_id', (toUInt64(" + domainId + "), toUInt64(" + pageTagId
						+ "), murmurHash3_64(url))) > 0)");
			}
			stringBuilder.append(") ");
		}
		stringBuilder.append(" and change_indicator in (");
		stringBuilder.append(getChangeIndicatorsPredicateFilter(databaseFields));
		stringBuilder.append(")");
		stringBuilder.append(" group by");
		stringBuilder.append("   url");
		stringBuilder.append(" order by");
		stringBuilder.append("   total_changes desc,");
		stringBuilder.append("   url");
		stringBuilder.append(" limit");
		stringBuilder.append("   20");
		String sqlString = stringBuilder.toString();
		if (isDebug == true) {
			System.out.println("getUrlsWithMostChanges() sqlString=" + sqlString);
		}
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		retryCount = 0;
		int index = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setInt(index++, domainId);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					targetUrlChangeIndClickHouseEntity = new TargetUrlChangeIndClickHouseEntity();
					targetUrlChangeIndClickHouseEntity.setUrl(resultSet.getString(IConstants.URL));
					targetUrlChangeIndClickHouseEntity.setTotalChanges(resultSet.getInt(IConstants.TOTAL_CHANGES));
					targetUrlChangeIndClickHouseEntityList.add(targetUrlChangeIndClickHouseEntity);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance()
							.logMemoryUsage("getUrlsWithMostChanges() trackDateString=" + trackDateString + ",domainId=" + domainId + ",startCrawlTimestampString="
									+ startCrawlTimestampString + ",endCrawlTimestampString=" + endCrawlTimestampString + ",e.getMessage()=" + e.getMessage()
									+ ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		FormatUtils.getInstance()
				.logMemoryUsage("getUrlsWithMostChanges() ip=" + ip + " ends, domainId=" + domainId + ",startCrawlTimestampString=" + startCrawlTimestampString
						+ ",endCrawlTimestampString=" + endCrawlTimestampString + ",targetUrlChangeIndClickHouseEntityList.size()="
						+ targetUrlChangeIndClickHouseEntityList.size() + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return targetUrlChangeIndClickHouseEntityList;
	}

	public synchronized List<TargetUrlChangeIndClickHouseEntity> getChangeIndicatorsByUrl(String ip, int domainId, String trackDateString,
			String startCrawlTimestampString, String endCrawlTimestampString, List<String> databaseFields, Integer[] pageTagIdArray, String urlString)
			throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("getChangeIndicatorsByUrl() ip=" + ip + " begins, domainId=" + domainId + ",trackDateString=" + trackDateString
				+ ",startCrawlTimestampString=" + startCrawlTimestampString + ",endCrawlTimestampString=" + endCrawlTimestampString + ",urlString=" + urlString);
		List<TargetUrlChangeIndClickHouseEntity> targetUrlChangeIndClickHouseEntityList = new ArrayList<TargetUrlChangeIndClickHouseEntity>();
		TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		boolean isFirstPageTagId = false;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select distinct change_indicator ");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(null));
		stringBuilder.append(" where");
		stringBuilder.append("     track_date = '" + trackDateString + "'");
		stringBuilder.append(" and domain_id = ?");
		if (StringUtils.isNotBlank(startCrawlTimestampString) && StringUtils.isNotBlank(endCrawlTimestampString)) {
			stringBuilder.append(" and current_crawl_timestamp >= '" + startCrawlTimestampString + "'");
			stringBuilder.append(" and current_crawl_timestamp <= '" + endCrawlTimestampString + "'");
		}

		if (pageTagIdArray != null && pageTagIdArray.length > 0) {
			isFirstPageTagId = true;
			stringBuilder.append(" and (");
			for (int pageTagId : pageTagIdArray) {
				if (isFirstPageTagId == true) {
					isFirstPageTagId = false;
				} else {
					stringBuilder.append(" or ");
				}
				if (isDebug == true) {
					System.out.println("getChangeIndicatorsByUrl() pageTagId=" + pageTagId);
				}
				stringBuilder.append("(dictGetUInt64('file_dic_page_tag_murmur3hash', 'target_url_id', (toUInt64(" + domainId + "), toUInt64(" + pageTagId
						+ "), murmurHash3_64(url))) > 0)");
			}
			stringBuilder.append(") ");
		}
		stringBuilder.append(" and change_indicator in (");
		stringBuilder.append(getChangeIndicatorsPredicateFilter(databaseFields));
		stringBuilder.append(")");
		stringBuilder.append(" and url_hash = URLHash(?)");
		stringBuilder.append(" and url_murmur_hash = murmurHash3_64(?)");
		String sqlString = stringBuilder.toString();
		if (isDebug == true) {
			System.out.println("getChangeIndicatorsByUrl() sqlString=" + sqlString);
		}
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		retryCount = 0;
		int index = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setString(index++, urlString);
				preparedStatement.setString(index++, urlString);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					targetUrlChangeIndClickHouseEntity = new TargetUrlChangeIndClickHouseEntity();
					targetUrlChangeIndClickHouseEntity.setChangeIndicator(resultSet.getString(IConstants.CHANGE_INDICATOR));
					targetUrlChangeIndClickHouseEntityList.add(targetUrlChangeIndClickHouseEntity);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance()
							.logMemoryUsage("getChangeIndicatorsByUrl() trackDateString=" + trackDateString + ",domainId=" + domainId + ",startCrawlTimestampString="
									+ startCrawlTimestampString + ",endCrawlTimestampString=" + endCrawlTimestampString + ",e.getMessage()=" + e.getMessage()
									+ ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		FormatUtils.getInstance()
				.logMemoryUsage("getChangeIndicatorsByUrl() ip=" + ip + " ends, domainId=" + domainId + ",startCrawlTimestampString=" + startCrawlTimestampString
						+ ",endCrawlTimestampString=" + endCrawlTimestampString + ",urlString=" + urlString + ",targetUrlChangeIndClickHouseEntityList.size()="
						+ targetUrlChangeIndClickHouseEntityList.size() + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return targetUrlChangeIndClickHouseEntityList;
	}

	public synchronized List<TargetUrlChangeIndClickHouseEntity> getCurrentPreviousByChangeIndicator(String ip, int domainId, String trackDateString,
			String startCrawlTimestampString, String endCrawlTimestampString, List<String> databaseFields, Integer[] pageTagIdArray, String urlString,
			String changeIndicator) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance()
				.logMemoryUsage("getCurrentPreviousByChangeIndicator() ip=" + ip + " begins, domainId=" + domainId + ",trackDateString=" + trackDateString
						+ ",startCrawlTimestampString=" + startCrawlTimestampString + ",endCrawlTimestampString=" + endCrawlTimestampString + ",urlString=" + urlString
						+ ",changeIndicator=" + changeIndicator);
		List<TargetUrlChangeIndClickHouseEntity> targetUrlChangeIndClickHouseEntityList = new ArrayList<TargetUrlChangeIndClickHouseEntity>();
		TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		boolean isFirstField = true;
		boolean isFirstPageTagId = false;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();

		stringBuilder.append(" select");
		if (databaseFields != null && databaseFields.size() > 0) {
			nextDatabaseField: for (String databaseField : databaseFields) {
				if (StringUtils.startsWithIgnoreCase(databaseField, IConstants.RESPONSE_CODE_CHG_IND_TO)) {
					continue nextDatabaseField;
				}
				if (isFirstField == true) {
					isFirstField = false;
					stringBuilder.append(IConstants.ONE_SPACE);
				} else {
					stringBuilder.append(IConstants.COMMA);
				}
				stringBuilder.append(databaseField);
			}
		} else {
			stringBuilder.append(" *");
		}
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(null));
		stringBuilder.append(" where");
		stringBuilder.append("     track_date = '" + trackDateString + "'");
		stringBuilder.append(" and domain_id = ?");
		if (StringUtils.isNotBlank(startCrawlTimestampString) && StringUtils.isNotBlank(endCrawlTimestampString)) {
			stringBuilder.append(" and current_crawl_timestamp >= '" + startCrawlTimestampString + "'");
			stringBuilder.append(" and current_crawl_timestamp <= '" + endCrawlTimestampString + "'");
		}

		if (pageTagIdArray != null && pageTagIdArray.length > 0) {
			isFirstPageTagId = true;
			stringBuilder.append(" and (");
			for (int pageTagId : pageTagIdArray) {
				if (isFirstPageTagId == true) {
					isFirstPageTagId = false;
				} else {
					stringBuilder.append(" or ");
				}
				if (isDebug == true) {
					System.out.println("getCurrentPreviousByChangeIndicator() pageTagId=" + pageTagId);
				}
				stringBuilder.append("(dictGetUInt64('file_dic_page_tag_murmur3hash', 'target_url_id', (toUInt64(" + domainId + "), toUInt64(" + pageTagId
						+ "), murmurHash3_64(url))) > 0)");
			}
			stringBuilder.append(") ");
		}
		stringBuilder.append(" and change_indicator = ?");
		stringBuilder.append(" and url_hash = URLHash(?)");
		stringBuilder.append(" and url_murmur_hash = murmurHash3_64(?)");
		String sqlString = stringBuilder.toString();
		if (isDebug == true) {
			System.out.println("getCurrentPreviousByChangeIndicator() sqlString=" + sqlString);
		}
		int connectionIndex = 0;
		connection = connectionList.get(connectionIndex);
		retryCount = 0;
		int index = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setInt(index++, domainId);
				preparedStatement.setString(index++, changeIndicator);
				preparedStatement.setString(index++, urlString);
				preparedStatement.setString(index++, urlString);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					targetUrlChangeIndClickHouseEntity = CrawlerUtils.getInstance().getTargetUrlChangeIndClickHouseEntityFromResultSet(resultSet, databaseFields);
					targetUrlChangeIndClickHouseEntityList.add(targetUrlChangeIndClickHouseEntity);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance()
							.logMemoryUsage("getCurrentPreviousByChangeIndicator() trackDateString=" + trackDateString + ",domainId=" + domainId
									+ ",startCrawlTimestampString=" + startCrawlTimestampString + ",endCrawlTimestampString=" + endCrawlTimestampString
									+ ",e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		FormatUtils.getInstance()
				.logMemoryUsage("getCurrentPreviousByChangeIndicator() ip=" + ip + " ends, domainId=" + domainId + ",startCrawlTimestampString="
						+ startCrawlTimestampString + ",endCrawlTimestampString=" + endCrawlTimestampString + ",urlString=" + urlString + ",changeIndicator="
						+ changeIndicator + ",targetUrlChangeIndClickHouseEntityList.size()=" + targetUrlChangeIndClickHouseEntityList.size() + ",elapsed(ms.)="
						+ (System.currentTimeMillis() - startTimestamp));
		return targetUrlChangeIndClickHouseEntityList;
	}

	public List<TargetUrlChangeIndClickHouseEntity> queryListByTrackPeriod(String trackDate, int mod, int index, List<String> databaseFields) throws Exception {
		final String sql = "select * from " + getTableName(null) + " where track_date = ? and url_murmur_hash % ? =?";
		final Connection connection = connectionList.get(0);
		final PreparedStatement preparedStatement = connection.prepareStatement(sql);
		preparedStatement.setString(1, trackDate);
		preparedStatement.setInt(2, mod);
		preparedStatement.setInt(3, index);
		final List<TargetUrlChangeIndClickHouseEntity> targetUrlChangeIndClickHouseEntityList = new ArrayList<>();
		TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity = null;
		int count = 0;
		while (count < 9) {
			try {
				final ResultSet resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					targetUrlChangeIndClickHouseEntity = CrawlerUtils.getInstance().getTargetUrlChangeIndClickHouseEntityFromResultSet(resultSet, databaseFields);
					targetUrlChangeIndClickHouseEntityList.add(targetUrlChangeIndClickHouseEntity);
				}
				return targetUrlChangeIndClickHouseEntityList;
			} catch (Exception e) {
                log.error("queryListByTrackPeriod() error, count={}, e.getMessage()={}", count, e.getMessage());
				count++;
				targetUrlChangeIndClickHouseEntityList.clear();
				try {
					Thread.sleep(1000);
				} catch (InterruptedException e1) {
					e1.printStackTrace();
				}
			}
		}
		return targetUrlChangeIndClickHouseEntityList;
	}
}