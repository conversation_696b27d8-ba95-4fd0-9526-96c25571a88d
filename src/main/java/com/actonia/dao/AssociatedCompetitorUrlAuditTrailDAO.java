package com.actonia.dao;

import java.util.ArrayList;
import java.util.List;

import com.actonia.entity.AssociatedCompetitorUrlAuditTrailEntity;

public class AssociatedCompetitorUrlAuditTrailDAO extends BaseJdbcSupport<AssociatedCompetitorUrlAuditTrailEntity> {

	private static final int RECORDS_PER_SQL_STATEMENT = 10;
	private static final int SQL_STATEMENTS_PER_JDBC_CALL = 10;
	private static final String SEMI_COLON = ";";
	private static final String INSERT_FORMAT_1 = "(?, ?)";
	private static final String INSERT_FORMAT_2 = ",(?, ?)";
	private static final int NUMBER_OF_FIELDS = 2;

	@Override
	public String getTableName() {
		return "associate_competitor_url_audit_trail";
	}

	public AssociatedCompetitorUrlAuditTrailEntity get(Integer batchRunDate) {
		String sqlString = "select * from " + getTableName() + " where batch_run_date = ?";
		return findObject(sqlString, batchRunDate);
	}

	public List<AssociatedCompetitorUrlAuditTrailEntity> getAll() {
		String sqlString = "select * from " + getTableName();
		return findBySql(sqlString);
	}

	public List<AssociatedCompetitorUrlAuditTrailEntity> getByBatchRunDateEarlierThan(int batchRunDate) {
		String sqlString = "select * from " + getTableName() + " where batch_run_date < ?";
		return findBySql(sqlString, batchRunDate);
	}

	public void create(List<AssociatedCompetitorUrlAuditTrailEntity> associatedCompetitorUrlAuditTrailEntityList) {
		Object[] objectArray = null;
		List<Object[]> objectArrayList = new ArrayList<Object[]>();
		List<AssociatedCompetitorUrlAuditTrailEntity> tempList = new ArrayList<AssociatedCompetitorUrlAuditTrailEntity>();

		for (AssociatedCompetitorUrlAuditTrailEntity associatedCompetitorUrlAuditTrailEntity : associatedCompetitorUrlAuditTrailEntityList) {
			tempList.add(associatedCompetitorUrlAuditTrailEntity);
			if (tempList.size() == RECORDS_PER_SQL_STATEMENT) {
				objectArray = getObjectArrayForInsert(tempList);
				objectArrayList.add(objectArray);
				if (objectArrayList.size() == SQL_STATEMENTS_PER_JDBC_CALL) {
					this.executeBatch(getInsertMultiRowsSqlStatement(RECORDS_PER_SQL_STATEMENT), objectArrayList);
					objectArrayList = new ArrayList<Object[]>();
				}
				tempList = new ArrayList<AssociatedCompetitorUrlAuditTrailEntity>();
			}
		}

		if (objectArrayList != null && objectArrayList.size() > 0) {
			this.executeBatch(getInsertMultiRowsSqlStatement(RECORDS_PER_SQL_STATEMENT), objectArrayList);
			objectArrayList = new ArrayList<Object[]>();
		}

		if (tempList != null && tempList.size() > 0) {
			objectArray = getObjectArrayForInsert(tempList);
			objectArrayList.add(objectArray);
			if (objectArrayList != null && objectArrayList.size() > 0) {
				this.executeBatch(getInsertMultiRowsSqlStatement(tempList.size()), objectArrayList);
				objectArrayList = new ArrayList<Object[]>();
			}
		}
	}

	private Object[] getObjectArrayForInsert(List<AssociatedCompetitorUrlAuditTrailEntity> list) {
		List<Object[]> testObjectArrayList = new ArrayList<Object[]>();
		Object[] testObjectArray = null;
		int totalNumberOfObjects = 0;
		for (AssociatedCompetitorUrlAuditTrailEntity associatedCompetitorUrlAuditTrailEntity : list) {
			testObjectArray = new Object[] { associatedCompetitorUrlAuditTrailEntity.getBatchRunDate(),
					associatedCompetitorUrlAuditTrailEntity.getBatchRunUpdateTimestamp(), };
			testObjectArrayList.add(testObjectArray);
		}
		totalNumberOfObjects = testObjectArrayList.size() * NUMBER_OF_FIELDS;
		Object[] objectArray = new Object[totalNumberOfObjects];
		int idx = 0;
		for (Object[] tempObjectArray : testObjectArrayList) {
			for (int i = 0; i < tempObjectArray.length; i++) {
				objectArray[idx++] = tempObjectArray[i];
			}
		}
		return objectArray;
	}

	private String getInsertMultiRowsSqlStatement(int recordsPerInsertStatement) {
		String response = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("insert into " + getTableName() + " ");
		stringBuilder.append("(");
		stringBuilder.append("	batch_run_date,");
		stringBuilder.append("	batch_run_update_timestamp");
		stringBuilder.append(")");
		stringBuilder.append("values ");
		String sqlString = stringBuilder.toString();
		StringBuffer sql = new StringBuffer();
		sql.append(sqlString);
		for (int i = 0; i < recordsPerInsertStatement; i++) {
			if (i == 0) {
				sql.append(INSERT_FORMAT_1);
			} else {
				sql.append(INSERT_FORMAT_2);
			}
		}
		sql.append(SEMI_COLON);
		response = sql.toString();
		return response;
	}

	public int updateCompetitorUrlId(int domainId, String hashCode, int competitorUrlId) {
		String sqlString = "update " + getTableName() + " set competitor_url_id = ? where domain_id = ? and hash_code = ?";
		return this.executeUpdate(sqlString, competitorUrlId, domainId, hashCode);
	}
}
