package com.actonia.dao;

import java.math.BigInteger;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.StringUtils;

import com.actonia.IConstants;
import com.actonia.entity.SharedCountsEntity;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.JCSManager;
import com.actonia.value.object.Facebook;
import com.actonia.value.object.SharedCountsValueObject;

import ru.yandex.clickhouse.ClickHouseDataSource;
import ru.yandex.clickhouse.settings.ClickHouseProperties;

public class SharedCountsClickHouseDAO {

	private boolean isDebug = false;
	private List<String> databaseHostnameList;
	private String databasePort = null;
	private String databaseName = null;
	private List<Connection> connectionList;
	private int batchCreationSize;
	private String databaseUser = null;
	private String databasePassword = null;
	private List<String> connectionUrlList = null;
	private int connectionTimeoutInMilliseconds;
	private int maximumRetryCounts;
	private int retryWaitMilliseconds;
	private static final String TABLE_NAME = "dis_social_detail";
	private static int cachedConnectionIndex = 0;

	public SharedCountsClickHouseDAO(String[] databaseHostnameArray, String databasePort, String databaseName, int batchCreationSize, String databaseUser,
			String databasePassword, int connectionTimeoutInMilliseconds, int maximumRetryCounts, int retryWaitMilliseconds) throws Exception {

		ClickHouseDataSource clickHouseDataSource = null;
		Connection connection = null;

		this.databaseHostnameList = Arrays.asList(databaseHostnameArray);

		FormatUtils.getInstance()
				.logMemoryUsage("SharedCountsClickHouseDAO() databaseHostnameList=" + databaseHostnameList.toString() + ",databasePort=" + databasePort
						+ ",databaseName=" + databaseName + ",batchCreationSize=" + batchCreationSize + ",databaseUser=" + databaseUser + ",databasePassword="
						+ databasePassword + ",connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ",maximumRetryCounts=" + maximumRetryCounts
						+ ",retryWaitMilliseconds=" + retryWaitMilliseconds + ",tableName=" + TABLE_NAME);

		this.databasePort = databasePort;
		this.databaseName = databaseName;
		this.batchCreationSize = batchCreationSize;
		this.databaseUser = databaseUser;
		this.databasePassword = databasePassword;
		this.connectionTimeoutInMilliseconds = connectionTimeoutInMilliseconds;
		this.maximumRetryCounts = maximumRetryCounts;
		this.retryWaitMilliseconds = retryWaitMilliseconds;

		ClickHouseProperties clickHouseProperties = new ClickHouseProperties();
		clickHouseProperties.setDecompress(true);
		clickHouseProperties.setConnectionTimeout(connectionTimeoutInMilliseconds);
		clickHouseProperties.setSocketTimeout(connectionTimeoutInMilliseconds);

		connectionList = new ArrayList<Connection>();

		String connectionUrl = null;

		connectionUrlList = new ArrayList<String>();

		for (String databaseHostname : databaseHostnameList) {
			if (StringUtils.isNotBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
						+ "?user=" + databaseUser + "&password=" + databasePassword;
			} else {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH
						+ databaseName;
			}
			connectionUrlList.add(connectionUrl);
			FormatUtils.getInstance().logMemoryUsage("SharedCountsClickHouseDAO() connectionUrl=" + connectionUrl);
			clickHouseDataSource = new ClickHouseDataSource(connectionUrl, clickHouseProperties);
			connection = clickHouseDataSource.getConnection();
			connectionList.add(connection);
		}
	}

	public synchronized String getTableName(String tableName) {
		if (StringUtils.isNotBlank(tableName)) {
			return tableName;
		} else {
			return TABLE_NAME;
		}
	}

	public int getBatchCreationSize() {
		return batchCreationSize;
	}

	public void createBatch(List<SharedCountsEntity> sharedCountsEntityList, String tableName) throws Exception {
		createBatch(sharedCountsEntityList, null, null, tableName);
	}

	public void createBatch(List<SharedCountsEntity> sharedCountsEntityList, String ip, String queueName, String tableName) throws Exception {
		long startTimestamp = 0L;

		PreparedStatement preparedStatement = null;
		int index = 0;
		Connection connection = null;
		String connectionUrl = null;

		String creationSqlStatement = getCreationSqlStatement(tableName);
		//FormatUtils.getInstance().logMemoryUsage("createBatch() creationSqlStatement=" + creationSqlStatement);

		for (int i = 0; i < connectionList.size(); i++) {
			connection = connectionList.get(i);
			if (isDebug == true) {
				connectionUrl = connectionUrlList.get(i);
				FormatUtils.getInstance().logMemoryUsage("SharedCountsClickHouseDAO.createBatch() connectionUrl=" + connectionUrl);
			}
			preparedStatement = connection.prepareStatement(creationSqlStatement);
			for (SharedCountsEntity sharedCountsEntity : sharedCountsEntityList) {
				index = 1;
				preparedStatement.setDate(index++, new java.sql.Date(sharedCountsEntity.getTrackDate().getTime()));
				preparedStatement.setString(index++, sharedCountsEntity.getDomain());
				preparedStatement.setString(index++, sharedCountsEntity.getRootDomain());
				preparedStatement.setString(index++, sharedCountsEntity.getUrl());
				preparedStatement.setString(index++, sharedCountsEntity.getProtocol());
				preparedStatement.setString(index++, sharedCountsEntity.getUri());
				preparedStatement.setString(index++, sharedCountsEntity.getFolder1());
				preparedStatement.setString(index++, sharedCountsEntity.getFolder2());
				preparedStatement.setInt(index++, sharedCountsEntity.getSharedCountsValueObject().getFacebook().getShare_count());
				preparedStatement.setInt(index++, sharedCountsEntity.getSharedCountsValueObject().getLinkedin());
				preparedStatement.setInt(index++, sharedCountsEntity.getSharedCountsValueObject().getTwitter());
				preparedStatement.setInt(index++, sharedCountsEntity.getSharedCountsValueObject().getGoogleplusone());
				preparedStatement.setInt(index++, sharedCountsEntity.getSharedCountsValueObject().getPinterest());
				preparedStatement.setInt(index++, sharedCountsEntity.getSharedCountsValueObject().getStumbleupon());
				preparedStatement.setInt(index++, sharedCountsEntity.getSign());
				preparedStatement.addBatch();
			}

			int retryCount = 0;
			while (retryCount < maximumRetryCounts) {
				try {
					startTimestamp = System.nanoTime();
					preparedStatement.executeBatch();
					retryCount = maximumRetryCounts;
				} catch (Exception e) {
					retryCount++;
					if (retryCount >= maximumRetryCounts) {
						e.printStackTrace();
						throw e;
					} else {
						FormatUtils.getInstance()
								.logMemoryUsage("SharedCountsClickHouseDAO.createBatch() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
						try {
							Thread.sleep(retryWaitMilliseconds);
						} catch (InterruptedException e1) {
							e1.printStackTrace();
						}
					}
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("createBatch() ip=" + ip + ",queueName=" + queueName + ",sharedCountsEntityList.size()="
				+ sharedCountsEntityList.size() + ",elapsed(ns.)=" + (System.nanoTime() - startTimestamp));
	}

	private String getCreationSqlStatement(String tableName) {
		String response = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" insert into " + getTableName(tableName) + " ");
		stringBuilder.append(" (");
		stringBuilder.append(" track_date,");
		stringBuilder.append(" domain,");
		stringBuilder.append(" root_domain,");
		stringBuilder.append(" url,");
		stringBuilder.append(" protocol,");
		stringBuilder.append(" uri,");
		stringBuilder.append(" folder1,");
		stringBuilder.append(" folder2,");
		stringBuilder.append(" facebook_shared_count,");
		stringBuilder.append(" linkedin_shared_count,");
		stringBuilder.append(" twitter_shared_count,");
		stringBuilder.append(" google_plus_shared_count,");
		stringBuilder.append(" pinterest_shared_count,");
		stringBuilder.append(" stumbleupon_shared_count,");
		stringBuilder.append(" sign");
		stringBuilder.append(" )");
		stringBuilder.append(" values ");
		stringBuilder.append(" (");
		stringBuilder.append(" ?,");
		stringBuilder.append(" ?,");
		stringBuilder.append(" ?,");
		stringBuilder.append(" ?,");
		stringBuilder.append(" ?,");
		stringBuilder.append(" ?,");
		stringBuilder.append(" ?,");
		stringBuilder.append(" ?,");
		stringBuilder.append(" ?,");
		stringBuilder.append(" ?,");
		stringBuilder.append(" ?,");
		stringBuilder.append(" ?,");
		stringBuilder.append(" ?,");
		stringBuilder.append(" ?,");
		stringBuilder.append(" ?");
		stringBuilder.append(" )");
		stringBuilder.append(IConstants.SEMI_COLON);
		response = stringBuilder.toString();
		return response;
	}

	@Override
	public String toString() {
		return "SharedCountsClickHouseDAO [isDebug=" + isDebug + ", databaseHostnameList=" + databaseHostnameList.toString() + ", databasePort=" + databasePort
				+ ", databaseName=" + databaseName + ", batchCreationSize=" + batchCreationSize + ", databaseUser=" + databaseUser + ", databasePassword="
				+ databasePassword + ", connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ", maximumRetryCounts=" + maximumRetryCounts
				+ ", retryWaitMilliseconds=" + retryWaitMilliseconds + "]";
	}

	public SharedCountsEntity get(String rootDomain, String domain, Date trackDate, String urlString, String tableName) throws Exception {

		//long startTimestamp = System.nanoTime();

		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		SharedCountsValueObject sharedCountsValueObject;
		SharedCountsEntity sharedCountsEntity = null;
		Facebook facebook = null;

		// TrackDate, RootDomain, Domain, CityHashUrl
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("  track_date,");
		stringBuilder.append("  domain,");
		stringBuilder.append("  root_domain,");
		stringBuilder.append("  url,");
		stringBuilder.append("  protocol,");
		stringBuilder.append("  uri,");
		stringBuilder.append("  folder1,");
		stringBuilder.append("  folder2,");
		stringBuilder.append("  url_hash,");
		stringBuilder.append("  uri_hash,");
		stringBuilder.append("  folder1_hash,");
		stringBuilder.append("  folder2_hash,");
		stringBuilder.append("  facebook_shared_count,");
		stringBuilder.append("  linkedin_shared_count,");
		stringBuilder.append("  twitter_shared_count,");
		stringBuilder.append("  google_plus_shared_count,");
		stringBuilder.append("  pinterest_shared_count,");
		stringBuilder.append("  stumbleupon_shared_count");
		stringBuilder.append(" from");
		stringBuilder.append(" ").append(getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append(" 	   track_date = ?");
		stringBuilder.append(" and root_domain = ?");
		stringBuilder.append(" and domain = ?");
		stringBuilder.append(" and url_hash = URLHash(?)");
		//		stringBuilder.append(" group by");
		//		stringBuilder.append("  track_date,");
		//		stringBuilder.append("  domain,");
		//		stringBuilder.append("  root_domain,");
		//		stringBuilder.append("  url,");
		//		stringBuilder.append("  protocol,");
		//		stringBuilder.append("  uri,");
		//		stringBuilder.append("  folder1,");
		//		stringBuilder.append("  folder2,");
		//		stringBuilder.append("  url_hash,");
		//		stringBuilder.append("  uri_hash,");
		//		stringBuilder.append("  folder1_hash,");
		//		stringBuilder.append("  folder2_hash,");
		//		stringBuilder.append("  facebook_shared_count,");
		//		stringBuilder.append("  linkedin_shared_count,");
		//		stringBuilder.append("  twitter_shared_count,");
		//		stringBuilder.append("  google_plus_shared_count,");
		//		stringBuilder.append("  pinterest_shared_count,");
		//		stringBuilder.append("  stumbleupon_shared_count");
		//		stringBuilder.append(" having");
		//		stringBuilder.append(" 	   sum(sign) > 0");
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("SharedCountsClickHouseDAO.get() sqlString=" + sqlString);
		connection = connectionList.get(getConnectionIndex());
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setDate(1, new java.sql.Date(trackDate.getTime()));
				preparedStatement.setString(2, rootDomain);
				preparedStatement.setString(3, domain);
				preparedStatement.setString(4, urlString);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {

					sharedCountsEntity = new SharedCountsEntity();

					// TrackDate
					sharedCountsEntity.setTrackDate(resultSet.getDate("track_date"));

					// Domain
					sharedCountsEntity.setDomain(resultSet.getString("domain"));

					// RootDomain
					sharedCountsEntity.setRootDomain(resultSet.getString("root_domain"));

					// Url
					sharedCountsEntity.setUrl(resultSet.getString("url"));

					// Protocol
					sharedCountsEntity.setProtocol(resultSet.getString("protocol"));

					// Uri
					sharedCountsEntity.setUri(resultSet.getString("uri"));

					// Folder1
					sharedCountsEntity.setFolder1(resultSet.getString("folder1"));

					// Folder2
					sharedCountsEntity.setFolder2(resultSet.getString("folder2"));

					// CityHashUrl
					sharedCountsEntity.setUrlHash(new BigInteger(resultSet.getString("url_hash")));

					// CityHashUri
					sharedCountsEntity.setUriHash(new BigInteger(resultSet.getString("uri_hash")));

					// CityHashFolder1
					sharedCountsEntity.setFolder1Hash(new BigInteger(resultSet.getString("folder1_hash")));

					// CityHashFolder2
					sharedCountsEntity.setFolder2Hash(new BigInteger(resultSet.getString("folder2_hash")));

					sharedCountsValueObject = new SharedCountsValueObject();

					// FacebookSharedCount
					facebook = new Facebook();
					facebook.setShare_count(resultSet.getInt("facebook_shared_count"));
					sharedCountsValueObject.setFacebook(facebook);

					// LinkedInSharedCount
					sharedCountsValueObject.setLinkedin(resultSet.getInt("linkedin_shared_count"));

					// TwitterSharedCount
					sharedCountsValueObject.setTwitter(resultSet.getInt("twitter_shared_count"));

					// GooglePlusSharedCount
					sharedCountsValueObject.setGoogleplusone(resultSet.getInt("google_plus_shared_count"));

					// PinterestSharedCount
					sharedCountsValueObject.setPinterest(resultSet.getInt("pinterest_shared_count"));

					// stumbleupon_shared_count
					sharedCountsValueObject.setStumbleupon(resultSet.getInt("stumbleupon_shared_count"));

					sharedCountsEntity.setSharedCountsValueObject(sharedCountsValueObject);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("SharedCountsClickHouseDAO.get() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		//FormatUtils.getInstance().logMemoryUsage("SharedCountsClickHouseDAO.get() rootDomain=" + rootDomain + ",domain=" + domain + ",trackDate="
		//		+ DateFormatUtils.getInstance().format(trackDate, IConstants.DATE_FORMAT_YYYY_MM_DD) + ",urlString=" + urlString + ",elapsed(ns.)="
		//		+ (System.nanoTime() - startTimestamp));
		return sharedCountsEntity;
	}

	private synchronized int getConnectionIndex() {
		int connectionIndex = 0;
		if (connectionList != null && connectionList.size() > 0) {
			if (cachedConnectionIndex >= connectionList.size()) {
				cachedConnectionIndex = 0;
			}
			connectionIndex = cachedConnectionIndex++;
		}
		return connectionIndex;
	}

	public List<SharedCountsEntity> getList(Date trackDate, Integer limit, String rootDomain, String domain, String tableName) throws Exception {

		long startTimestamp = System.currentTimeMillis();

		List<SharedCountsEntity> sharedCountsEntityList = new ArrayList<SharedCountsEntity>();

		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		SharedCountsValueObject sharedCountsValueObject;
		SharedCountsEntity sharedCountsEntity = null;
		Facebook facebook = null;

		// TrackDate
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("  track_date,");
		stringBuilder.append("  domain,");
		stringBuilder.append("  root_domain,");
		stringBuilder.append("  url,");
		stringBuilder.append("  protocol,");
		stringBuilder.append("  uri,");
		stringBuilder.append("  folder1,");
		stringBuilder.append("  folder2,");
		stringBuilder.append("  url_hash,");
		stringBuilder.append("  uri_hash,");
		stringBuilder.append("  folder1_hash,");
		stringBuilder.append("  folder2_hash,");
		stringBuilder.append("  facebook_shared_count,");
		stringBuilder.append("  linkedin_shared_count,");
		stringBuilder.append("  twitter_shared_count,");
		stringBuilder.append("  google_plus_shared_count,");
		stringBuilder.append("  pinterest_shared_count,");
		stringBuilder.append("  stumbleupon_shared_count");
		stringBuilder.append(" from");
		stringBuilder.append(" ").append(getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append(" 	   track_date = ?");

		if (StringUtils.isNotBlank(rootDomain) && StringUtils.isNotBlank(domain)) {
			stringBuilder.append(" and root_domain = ?");
			stringBuilder.append(" and domain = ?");
		}

		if (limit != null && limit.intValue() > 0) {
			stringBuilder.append(" limit");
			stringBuilder.append(" " + limit);
		}
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("SharedCountsClickHouseDAO.get() sqlString=" + sqlString);
		connection = connectionList.get(getConnectionIndex());
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setDate(1, new java.sql.Date(trackDate.getTime()));

				if (StringUtils.isNotBlank(rootDomain) && StringUtils.isNotBlank(domain)) {
					preparedStatement.setString(2, rootDomain);
					preparedStatement.setString(3, domain);
				}

				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {

					sharedCountsEntity = new SharedCountsEntity();

					// TrackDate
					sharedCountsEntity.setTrackDate(resultSet.getDate("track_date"));

					// Domain
					sharedCountsEntity.setDomain(resultSet.getString("domain"));

					// RootDomain
					sharedCountsEntity.setRootDomain(resultSet.getString("root_domain"));

					// Url
					sharedCountsEntity.setUrl(resultSet.getString("url"));

					// Protocol
					sharedCountsEntity.setProtocol(resultSet.getString("protocol"));

					// Uri
					sharedCountsEntity.setUri(resultSet.getString("uri"));

					// Folder1
					sharedCountsEntity.setFolder1(resultSet.getString("folder1"));

					// Folder2
					sharedCountsEntity.setFolder2(resultSet.getString("folder2"));

					// CityHashUrl
					sharedCountsEntity.setUrlHash(new BigInteger(resultSet.getString("url_hash")));

					// CityHashUri
					sharedCountsEntity.setUriHash(new BigInteger(resultSet.getString("uri_hash")));

					// CityHashFolder1
					sharedCountsEntity.setFolder1Hash(new BigInteger(resultSet.getString("folder1_hash")));

					// CityHashFolder2
					sharedCountsEntity.setFolder2Hash(new BigInteger(resultSet.getString("folder2_hash")));

					sharedCountsValueObject = new SharedCountsValueObject();

					// FacebookSharedCount
					facebook = new Facebook();
					facebook.setShare_count(resultSet.getInt("facebook_shared_count"));
					sharedCountsValueObject.setFacebook(facebook);

					// LinkedInSharedCount
					sharedCountsValueObject.setLinkedin(resultSet.getInt("linkedin_shared_count"));

					// TwitterSharedCount
					sharedCountsValueObject.setTwitter(resultSet.getInt("twitter_shared_count"));

					// GooglePlusSharedCount
					sharedCountsValueObject.setGoogleplusone(resultSet.getInt("google_plus_shared_count"));

					// PinterestSharedCount
					sharedCountsValueObject.setPinterest(resultSet.getInt("pinterest_shared_count"));

					// stumbleupon_shared_count
					sharedCountsValueObject.setStumbleupon(resultSet.getInt("stumbleupon_shared_count"));

					sharedCountsEntity.setSharedCountsValueObject(sharedCountsValueObject);

					sharedCountsEntityList.add(sharedCountsEntity);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("SharedCountsClickHouseDAO.getList() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("SharedCountsClickHouseDAO.getList() trackDate=" + DateFormatUtils.format(trackDate, IConstants.DATE_FORMAT_YYYY_MM_DD)
				+ ",sharedCountsEntityList.size()=" + sharedCountsEntityList.size() + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		return sharedCountsEntityList;
	}

	public List<SharedCountsEntity> getNonZeroSharedCountList(Date trackDate, Integer limit, String tableName) throws Exception {

		long startTimestamp = System.currentTimeMillis();

		List<SharedCountsEntity> sharedCountsEntityList = new ArrayList<SharedCountsEntity>();

		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		SharedCountsValueObject sharedCountsValueObject;
		SharedCountsEntity sharedCountsEntity = null;
		Facebook facebook = null;

		// TrackDate
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("  track_date,");
		stringBuilder.append("  domain,");
		stringBuilder.append("  root_domain,");
		stringBuilder.append("  url,");
		stringBuilder.append("  protocol,");
		stringBuilder.append("  uri,");
		stringBuilder.append("  folder1,");
		stringBuilder.append("  folder2,");
		stringBuilder.append("  url_hash,");
		stringBuilder.append("  uri_hash,");
		stringBuilder.append("  folder1_hash,");
		stringBuilder.append("  folder2_hash,");
		stringBuilder.append("  facebook_shared_count,");
		stringBuilder.append("  linkedin_shared_count,");
		stringBuilder.append("  twitter_shared_count,");
		stringBuilder.append("  google_plus_shared_count,");
		stringBuilder.append("  pinterest_shared_count,");
		stringBuilder.append("  stumbleupon_shared_count");
		stringBuilder.append(" from");
		stringBuilder.append(" ").append(getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append(" 	   track_date = ?");
		stringBuilder.append(" group by");
		stringBuilder.append("  track_date,");
		stringBuilder.append("  domain,");
		stringBuilder.append("  root_domain,");
		stringBuilder.append("  url,");
		stringBuilder.append("  protocol,");
		stringBuilder.append("  uri,");
		stringBuilder.append("  folder1,");
		stringBuilder.append("  folder2,");
		stringBuilder.append("  url_hash,");
		stringBuilder.append("  uri_hash,");
		stringBuilder.append("  folder1_hash,");
		stringBuilder.append("  folder2_hash,");
		stringBuilder.append("  facebook_shared_count,");
		stringBuilder.append("  linkedin_shared_count,");
		stringBuilder.append("  twitter_shared_count,");
		stringBuilder.append("  google_plus_shared_count,");
		stringBuilder.append("  pinterest_shared_count,");
		stringBuilder.append("  stumbleupon_shared_count");
		stringBuilder.append(" having");
		stringBuilder.append(" 	   sum(sign) > 0");
		stringBuilder.append(" and (sum(facebook_shared_count) > 0");
		stringBuilder.append(" or   sum(linkedin_shared_count) > 0");
		stringBuilder.append(" or   sum(twitter_shared_count) > 0");
		stringBuilder.append(" or   sum(google_plus_shared_count) > 0");
		stringBuilder.append(" or   sum(pinterest_shared_count) > 0");
		stringBuilder.append(" or   sum(stumbleupon_shared_count) > 0)");
		if (limit != null && limit.intValue() > 0) {
			stringBuilder.append(" limit");
			stringBuilder.append(" " + limit);
		}
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("SharedCountsClickHouseDAO.get() sqlString=" + sqlString);
		connection = connectionList.get(getConnectionIndex());
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setDate(1, new java.sql.Date(trackDate.getTime()));
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {

					sharedCountsEntity = new SharedCountsEntity();

					// TrackDate
					sharedCountsEntity.setTrackDate(resultSet.getDate("track_date"));

					// Domain
					sharedCountsEntity.setDomain(resultSet.getString("domain"));

					// RootDomain
					sharedCountsEntity.setRootDomain(resultSet.getString("root_domain"));

					// Url
					sharedCountsEntity.setUrl(resultSet.getString("url"));

					// Protocol
					sharedCountsEntity.setProtocol(resultSet.getString("protocol"));

					// Uri
					sharedCountsEntity.setUri(resultSet.getString("uri"));

					// Folder1
					sharedCountsEntity.setFolder1(resultSet.getString("folder1"));

					// Folder2
					sharedCountsEntity.setFolder2(resultSet.getString("folder2"));

					// CityHashUrl
					sharedCountsEntity.setUrlHash(new BigInteger(resultSet.getString("url_hash")));

					// CityHashUri
					sharedCountsEntity.setUriHash(new BigInteger(resultSet.getString("uri_hash")));

					// CityHashFolder1
					sharedCountsEntity.setFolder1Hash(new BigInteger(resultSet.getString("folder1_hash")));

					// CityHashFolder2
					sharedCountsEntity.setFolder2Hash(new BigInteger(resultSet.getString("folder2_hash")));

					sharedCountsValueObject = new SharedCountsValueObject();

					// FacebookSharedCount
					facebook = new Facebook();
					facebook.setShare_count(resultSet.getInt("facebook_shared_count"));
					sharedCountsValueObject.setFacebook(facebook);

					// LinkedInSharedCount
					sharedCountsValueObject.setLinkedin(resultSet.getInt("linkedin_shared_count"));

					// TwitterSharedCount
					sharedCountsValueObject.setTwitter(resultSet.getInt("twitter_shared_count"));

					// GooglePlusSharedCount
					sharedCountsValueObject.setGoogleplusone(resultSet.getInt("google_plus_shared_count"));

					// PinterestSharedCount
					sharedCountsValueObject.setPinterest(resultSet.getInt("pinterest_shared_count"));

					// stumbleupon_shared_count
					sharedCountsValueObject.setStumbleupon(resultSet.getInt("stumbleupon_shared_count"));

					sharedCountsEntity.setSharedCountsValueObject(sharedCountsValueObject);

					sharedCountsEntityList.add(sharedCountsEntity);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance()
							.logMemoryUsage("SharedCountsClickHouseDAO.getNonZeroSharedCountList() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		FormatUtils.getInstance()
				.logMemoryUsage("SharedCountsClickHouseDAO.getNonZeroSharedCountList() trackDate="
						+ DateFormatUtils.format(trackDate, IConstants.DATE_FORMAT_YYYY_MM_DD) + ",sharedCountsEntityList.size()=" + sharedCountsEntityList.size()
						+ ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		return sharedCountsEntityList;
	}

	public Map<String, String> getUrlHashByTrackDate(Date trackDate, Integer limit, String tableName) throws Exception {
		long startTimestamp = System.currentTimeMillis();

		Map<String, String> urlHashMap = new HashMap<String, String>();

		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		List<String> urlHashList = new ArrayList<String>();
		String urlString = null;
		String hashCode = null;
		int modulus = 0;

		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     url");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("    track_date = '" + DateFormatUtils.format(trackDate, IConstants.DATE_FORMAT_YYYY_MM_DD) + "'");
		if (limit != null && limit.intValue() > 0) {
			stringBuilder.append(" limit " + limit);
		}
		String sqlString = stringBuilder.toString();
		FormatUtils.getInstance().logMemoryUsage("SharedCountsClickHouseDAO.getUrlHashByTrackDate() sqlString=" + sqlString);
		if (connectionList != null && connectionList.size() > 0) {
			connection = connectionList.get(0);
			retryCount = 0;
			while (retryCount < maximumRetryCounts) {
				try {
					resultSet = connection.createStatement().executeQuery(sqlString);
					while (resultSet.next()) {
						urlString = resultSet.getString(IConstants.CLICKHOUSE_FLD_URL);
						//FormatUtils.getInstance().logMemoryUsage("SharedCountsClickHouseDAO.getUrlHashByTrackDate() cityHashUrl=" + cityHashUrl);
						if (StringUtils.isNotBlank(urlString)) {
							hashCode = JCSManager.Md5(urlString);
							if (StringUtils.isNotBlank(hashCode)) {
								urlHashList.add(hashCode);
								modulus = urlHashList.size() % 100000;
								if (modulus == 0) {
									FormatUtils.getInstance().logMemoryUsage("SharedCountsClickHouseDAO.getList() urlHashList.size()=" + urlHashList.size());
								}
							}
						}
					}
					retryCount = maximumRetryCounts;
				} catch (Exception e) {
					retryCount++;
					if (retryCount >= maximumRetryCounts) {
						e.printStackTrace();
						throw e;
					} else {
						FormatUtils.getInstance()
								.logMemoryUsage("SharedCountsClickHouseDAO.getList() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
						try {
							Thread.sleep(retryWaitMilliseconds);
						} catch (InterruptedException e1) {
							e1.printStackTrace();
						}
					}
				}
			}
		}

		if (urlHashList != null && urlHashList.size() > 0) {
			for (String urlHashCode : urlHashList) {
				urlHashMap.put(urlHashCode, IConstants.EMPTY_STRING);
			}
		}
		FormatUtils.getInstance().logMemoryUsage(
				"SharedCountsClickHouseDAO.getList() urlHashMap.size()=" + urlHashMap.size() + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		return urlHashMap;
	}

	public synchronized void createBatch(List<SharedCountsEntity> sharedCountsEntityList) throws Exception {
		createBatch(sharedCountsEntityList, null, null, null);
	}

	public int transferFromSourceToDestination(Date trackDate, String tableName, SharedCountsClickHouseDAO destinationSharedCountsClickHouseDAO) throws Exception {
		FormatUtils.getInstance()
				.logMemoryUsage("transferFromSourceToDestination() begins. trackDate=" + DateFormatUtils.format(trackDate, IConstants.DATE_FORMAT_YYYY_MM_DD));

		long startTimestamp = System.currentTimeMillis();

		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;

		List<SharedCountsEntity> sharedCountsEntityList = new ArrayList<SharedCountsEntity>();
		SharedCountsEntity sharedCountsEntity = null;
		SharedCountsValueObject sharedCountsValueObject = null;
		Facebook facebook = null;
		PreparedStatement preparedStatement = null;
		int totalRecordsReIndexed = 0;

		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     *");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("    track_date = ?");
		String sqlString = stringBuilder.toString();
		String connectionUrl = null;
		int connectionIndex = getConnectionIndex();
		connection = connectionList.get(connectionIndex);
		if (isDebug == true) {
			connectionUrl = connectionUrlList.get(connectionIndex);
			FormatUtils.getInstance().logMemoryUsage("SharedCountsClickHouseDAO.transferFromSourceToDestination() connectionUrl=" + connectionUrl);
		}
		retryCount = 0;
		int index = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				sharedCountsEntityList = new ArrayList<SharedCountsEntity>();
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setDate(index++, new java.sql.Date(trackDate.getTime()));
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					sharedCountsEntity = new SharedCountsEntity();

					// track_date
					sharedCountsEntity.setTrackDate(resultSet.getDate(IConstants.TRACK_DATE));

					// domain
					sharedCountsEntity.setDomain(resultSet.getString(IConstants.CLICKHOUSE_FLD_DOMAIN));

					// root_domain
					sharedCountsEntity.setRootDomain(resultSet.getString(IConstants.CLICKHOUSE_FLD_ROOT_DOMAIN));

					// url
					sharedCountsEntity.setUrl(resultSet.getString(IConstants.CLICKHOUSE_FLD_URL));

					// protocol
					sharedCountsEntity.setProtocol(resultSet.getString(IConstants.CLICKHOUSE_FLD_PROTOCOL));

					// uri
					sharedCountsEntity.setUri(resultSet.getString(IConstants.CLICKHOUSE_FLD_URI));

					// folder1
					sharedCountsEntity.setFolder1(resultSet.getString(IConstants.CLICKHOUSE_FLD_FOLDER1));

					// folder2
					sharedCountsEntity.setFolder2(resultSet.getString(IConstants.CLICKHOUSE_FLD_FOLDER2));

					// url_hash
					sharedCountsEntity.setUrlHash(new BigInteger(resultSet.getString(IConstants.URL_HASH)));

					// uri_hash
					sharedCountsEntity.setUriHash(new BigInteger(resultSet.getString(IConstants.CLICKHOUSE_FLD_URI_HASH)));

					// folder1_hash
					sharedCountsEntity.setFolder1Hash(new BigInteger(resultSet.getString(IConstants.CLICKHOUSE_FLD_FOLDER1_HASH)));

					// folder2_hash
					sharedCountsEntity.setFolder2Hash(new BigInteger(resultSet.getString(IConstants.CLICKHOUSE_FLD_FOLDER2_HASH)));

					sharedCountsValueObject = new SharedCountsValueObject();

					// facebook_shared_count
					facebook = new Facebook();
					facebook.setShare_count(resultSet.getInt(IConstants.CLICKHOUSE_FLD_FACEBOOK_SHARED_COUNT));
					sharedCountsValueObject.setFacebook(facebook);

					// linkedin_shared_count
					sharedCountsValueObject.setLinkedin(resultSet.getInt(IConstants.CLICKHOUSE_FLD_LINKEDIN_SHARED_COUNT));

					// twitter_shared_count
					sharedCountsValueObject.setTwitter(resultSet.getInt(IConstants.CLICKHOUSE_FLD_TWITTER_SHARED_COUNT));

					// google_plus_shared_count
					sharedCountsValueObject.setGoogleplusone(resultSet.getInt(IConstants.CLICKHOUSE_FLD_GOOGLE_PLUS_SHARED_COUNT));

					// pinterest_shared_count
					sharedCountsValueObject.setPinterest(resultSet.getInt(IConstants.CLICKHOUSE_FLD_PINTEREST_SHARED_COUNT));

					// stumbleupon_shared_count
					sharedCountsValueObject.setStumbleupon(resultSet.getInt(IConstants.CLICKHOUSE_FLD_STUMBLEUPON_SHARED_COUNT));

					// shared counts
					sharedCountsEntity.setSharedCountsValueObject(sharedCountsValueObject);

					// sign
					sharedCountsEntity.setSign(resultSet.getInt(IConstants.SIGN));

					//if (isDebug == true) {
					//	sharedCountsEntity.setConnectionUrl(connectionUrl);
					//}

					sharedCountsEntityList.add(sharedCountsEntity);

					if (sharedCountsEntityList.size() >= destinationSharedCountsClickHouseDAO.getBatchCreationSize()) {
						destinationSharedCountsClickHouseDAO.createBatch(sharedCountsEntityList);
						totalRecordsReIndexed = totalRecordsReIndexed + sharedCountsEntityList.size();
						sharedCountsEntityList = new ArrayList<SharedCountsEntity>();
						if (totalRecordsReIndexed % 100000 == 0) {
							FormatUtils.getInstance().logMemoryUsage("transferFromSourceToDestination() trackDate="
									+ DateFormatUtils.format(trackDate, IConstants.DATE_FORMAT_YYYY_MM_DD) + ",totalRecordsReIndexed=" + totalRecordsReIndexed);
						}
					}
				}

				if (sharedCountsEntityList.size() > 0) {
					destinationSharedCountsClickHouseDAO.createBatch(sharedCountsEntityList);
					totalRecordsReIndexed = totalRecordsReIndexed + sharedCountsEntityList.size();
					sharedCountsEntityList = new ArrayList<SharedCountsEntity>();
					FormatUtils.getInstance().logMemoryUsage("transferFromSourceToDestination() trackDate="
							+ DateFormatUtils.format(trackDate, IConstants.DATE_FORMAT_YYYY_MM_DD) + ",totalRecordsReIndexed=" + totalRecordsReIndexed);
				}

				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					//CrawlerUtils
					//		.logMemoryUsage("SharedCountsClickHouseDAO.transferFromSourceToDestination() exception message=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		FormatUtils.getInstance()
				.logMemoryUsage("transferFromSourceToDestination() ends. trackDate=" + DateFormatUtils.format(trackDate, IConstants.DATE_FORMAT_YYYY_MM_DD)
						+ ",totalRecordsReIndexed=" + totalRecordsReIndexed + ",elapsed(s.)=" + (System.currentTimeMillis() - startTimestamp) / 1000);
		return totalRecordsReIndexed;
	}
}
