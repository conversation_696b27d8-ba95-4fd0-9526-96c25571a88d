package com.actonia.dao;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

import com.actonia.entity.ContentGuardSkipUrlEntity;

public class ContentGuardSkipUrlDAO extends BaseJdbcSupport<ContentGuardSkipUrlEntity> {

	@Override
	public String getTableName() {
		return "content_guard_skip_url";
	}

	public List<ContentGuardSkipUrlEntity> getList(int domainId, Long groupId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("  *");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     domain_id = ?");
		stringBuilder.append(" and group_id = ?");
		String sqlString = stringBuilder.toString();
		return this.findBySql(sqlString, domainId, groupId);
	}

	public Long create(ContentGuardSkipUrlEntity contentGuardSkipUrlEntity) {
		Map<String, Object> values = new HashMap<String, Object>();
		values.put("domain_id", contentGuardSkipUrlEntity.getDomainId());
		values.put("group_id", contentGuardSkipUrlEntity.getGroupId());
		values.put("indicator", contentGuardSkipUrlEntity.getIndicator());
		values.put("url_selector_type", contentGuardSkipUrlEntity.getUrlSelectorType());
		values.put("url_selector", contentGuardSkipUrlEntity.getUrlSelector());
		return insertForLongId(values);
	}

	public int delete(int domainId, Long groupId, String indicator, int urlSelectorType, String urlSelector) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" delete from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     domain_id = ?");
		stringBuilder.append(" and group_id = ?");
		stringBuilder.append(" and indicator = ?");
		stringBuilder.append(" and url_selector_type = ?");
		if (StringUtils.isNotBlank(urlSelector)) {
			stringBuilder.append(" and url_selector = ?");
		}
		String sqlString = stringBuilder.toString();
		if (StringUtils.isNotBlank(urlSelector)) {
			return this.executeUpdate(sqlString, domainId, groupId, indicator, urlSelectorType, urlSelector);
		} else {
			return this.executeUpdate(sqlString, domainId, groupId, indicator, urlSelectorType);
		}
	}

	public int delete(int domainId, Long groupId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" delete from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     domain_id = ?");
		stringBuilder.append(" and group_id = ?");
		String sqlString = stringBuilder.toString();
		return this.executeUpdate(sqlString, domainId, groupId);
	}
}