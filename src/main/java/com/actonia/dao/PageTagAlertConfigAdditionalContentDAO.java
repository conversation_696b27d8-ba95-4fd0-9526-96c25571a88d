package com.actonia.dao;

import java.util.List;

import com.actonia.entity.PageTagAlertConfigAdditionalContentEntity;

public class PageTagAlertConfigAdditionalContentDAO extends BaseJdbcSupport<PageTagAlertConfigAdditionalContentEntity> {

	@Override
	public String getTableName() {
		return "page_tag_alert_config_additional_content";
	}

	public List<PageTagAlertConfigAdditionalContentEntity> getByPageTagAlertConfigId(int pageTagAlertConfigId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("select ");
		stringBuilder.append("    * ");
		stringBuilder.append("from ");
		stringBuilder.append(getTableName() + " ");
		stringBuilder.append("where ");
		stringBuilder.append("    page_tag_alert_config_id = ? ");
		String sqlString = stringBuilder.toString();
		return findBySql(sqlString, pageTagAlertConfigId);
	}
}