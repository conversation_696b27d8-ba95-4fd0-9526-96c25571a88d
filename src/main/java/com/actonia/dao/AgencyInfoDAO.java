/**
 * 
 */
package com.actonia.dao;

import org.apache.commons.lang.StringUtils;

import com.actonia.entity.AgencyInfoEntity;

/**
 * com.actonia.subserver.dao.AgencyInfoDAO.java
 * 
 * https://www.wrike.com/open.htm?id=59034165
 *
 * <AUTHOR>
 *
 * @version $Revision:$
 *          $Author:$
 */
public class AgencyInfoDAO extends BaseJdbcSupport<AgencyInfoEntity> {

	@Override
	public String getTableName() {
		return "agency_info";
	}
	
	public AgencyInfoEntity getByCompanyName(String companyName) {
		if (StringUtils.isBlank(companyName)) {
			companyName = AgencyInfoEntity.DEFAULT_COMPANY_NAME;
		}
		
		StringBuilder sql = new StringBuilder();
		sql.append(" select * ");
		sql.append(" from ").append(getTableName());
		sql.append(" where company_name = ? ");
		
		return this.findObject(sql.toString(), companyName);
	}
	
	public AgencyInfoEntity getByWebsiteDomain(String websiteDomain) {
		StringBuilder sql = new StringBuilder();
		sql.append(" select * ");
		sql.append(" from ").append(getTableName());
		sql.append(" where website_domain like ? ");
		
		return this.findObject(sql.toString(), "%" + websiteDomain + "%");
	}
	
	public AgencyInfoEntity getDefault() {
		return getByCompanyName(AgencyInfoEntity.DEFAULT_COMPANY_NAME);
	}

}
