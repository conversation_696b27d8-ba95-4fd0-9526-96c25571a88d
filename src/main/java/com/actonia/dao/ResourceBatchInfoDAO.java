package com.actonia.dao;

import com.actonia.entity.ResourceBatchInfoEntity;

import java.util.HashMap;
import java.util.Map;

public class ResourceBatchInfoDAO extends BaseJdbcSupport<ResourceBatchInfoEntity> {


	@Override
	public String getTableName() {
		return "queue_base_info";
	}

	public long insert(ResourceBatchInfoEntity infoEntity) {
		Map<String, Object> value = new HashMap<String, Object>();
		value.put("actionType", infoEntity.getActionType());
		value.put("ownDomainId", infoEntity.getOwnDomainId());
		value.put("operationType", infoEntity.getOperationType());
		value.put("status", infoEntity.getStatus());
		value.put("userId", infoEntity.getUserId());
		value.put("customFlag", infoEntity.getCustomFlag());
		value.put("createDate", infoEntity.getCreateDate());
		value.put("engineLanguageDevice", infoEntity.getEngineLanguageDevice());

		return insertForLongId(value);
	}

	public void updateStatusById(int statusCreated, long infoId) {
		final String sql = "update " + getTableName() + " set status = ? where id = ?";
		super.executeUpdate(sql, statusCreated, infoId);
	}
}