package com.actonia.dao;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

import com.actonia.entity.ContentGuardUrlEntity;
import com.actonia.utils.Md5Util;

public class ContentGuardUrlDAO extends BaseJdbcSupport<ContentGuardUrlEntity> {

	@Override
	public String getTableName() {
		return "content_guard_url";
	}

	public List<ContentGuardUrlEntity> getListByCrawlFrequency(int domainId, int crawlFrequencyType, Integer crawlStatus) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     content_guard_url.*,");
		stringBuilder.append("     content_guard_url.url_hash as urlHashString,");
		stringBuilder.append("     content_guard_url.murmur_hash as murmurHash,");
		stringBuilder.append("     content_guard_url.s3_file_name as s3FileName");
		stringBuilder.append(" from");
		stringBuilder.append("     content_guard_group content_guard_group,");
		stringBuilder.append("     content_guard_url content_guard_url");
		stringBuilder.append(" where");
		stringBuilder.append("     content_guard_group.domain_id = ?");
		stringBuilder.append(" and content_guard_group.crawl_frequency_type = ?");
		stringBuilder.append(" and content_guard_group.domain_id = content_guard_url.domain_id");
		stringBuilder.append(" and content_guard_group.id = content_guard_url.group_id");
		if (crawlStatus != null) {
			stringBuilder.append(" and content_guard_url.crawl_status is not null");
			stringBuilder.append(" and content_guard_url.crawl_status = ?");
		}
		String sqlString = stringBuilder.toString();
		if (crawlStatus != null) {
			return this.findBySql(sqlString, domainId, crawlFrequencyType, crawlStatus);
		} else {
			return this.findBySql(sqlString, domainId, crawlFrequencyType);
		}
	}

	public int updateCrawlStatus(int domainId, String hashCd, Integer crawlStatus) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" update");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" set");
		stringBuilder.append("     crawl_status = ?");
		stringBuilder.append(" where");
		stringBuilder.append("     domain_id = ?");
		stringBuilder.append(" and hash_cd = ?");
		String sqlString = stringBuilder.toString();
		return this.executeUpdate(sqlString, crawlStatus, domainId, hashCd);
	}

	public Integer getTotalUrlsByDomainId(int domainId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     count(*)");
		stringBuilder.append(" from");
		stringBuilder.append("     content_guard_group content_guard_group,");
		stringBuilder.append("     content_guard_url content_guard_url");
		stringBuilder.append(" where");
		stringBuilder.append("     content_guard_group.domain_id = ?");
		stringBuilder.append(" and content_guard_group.domain_id = content_guard_url.domain_id");
		stringBuilder.append(" and content_guard_group.id = content_guard_url.group_id");
		String sqlString = stringBuilder.toString();
		return this.queryForInteger(sqlString, domainId);
	}

	public Map<String, String> getMd5HashUrlMap(int domainId, Long groupId) {
		Map<String, String> md5HashUrlMap = new HashMap<String, String>();
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     content_guard_url.url");
		stringBuilder.append(" from");
		stringBuilder.append("     content_guard_group content_guard_group,");
		stringBuilder.append("     content_guard_url content_guard_url");
		stringBuilder.append(" where");
		stringBuilder.append("     content_guard_group.id = ?");
		stringBuilder.append(" and content_guard_group.domain_id = ?");
		stringBuilder.append(" and content_guard_group.domain_id = content_guard_url.domain_id");
		stringBuilder.append(" and content_guard_group.id = content_guard_url.group_id");
		String sqlString = stringBuilder.toString();
		List<ContentGuardUrlEntity> contentGuardUrlEntityList = this.findBySql(sqlString, groupId, domainId);
		if (contentGuardUrlEntityList != null && contentGuardUrlEntityList.size() > 0) {
			for (ContentGuardUrlEntity contentGuardUrlEntity : contentGuardUrlEntityList) {
				md5HashUrlMap.put(Md5Util.Md5(StringUtils.trim(contentGuardUrlEntity.getUrl())), StringUtils.trim(contentGuardUrlEntity.getUrl()));
			}
		}
		return md5HashUrlMap;
	}

	public Map<String, String> getMd5HashUrlMap(int domainId) {
		Map<String, String> md5HashUrlMap = new HashMap<String, String>();
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     content_guard_url.url");
		stringBuilder.append(" from");
		stringBuilder.append("     content_guard_group content_guard_group,");
		stringBuilder.append("     content_guard_url content_guard_url");
		stringBuilder.append(" where");
		stringBuilder.append("     content_guard_group.domain_id = ?");
		stringBuilder.append(" and content_guard_group.domain_id = content_guard_url.domain_id");
		stringBuilder.append(" and content_guard_group.id = content_guard_url.group_id");
		String sqlString = stringBuilder.toString();
		List<ContentGuardUrlEntity> contentGuardUrlEntityList = this.findBySql(sqlString, domainId);
		if (contentGuardUrlEntityList != null && contentGuardUrlEntityList.size() > 0) {
			for (ContentGuardUrlEntity contentGuardUrlEntity : contentGuardUrlEntityList) {
				md5HashUrlMap.put(Md5Util.Md5(StringUtils.trim(contentGuardUrlEntity.getUrl())), StringUtils.trim(contentGuardUrlEntity.getUrl()));
			}
		}
		return md5HashUrlMap;
	}
}