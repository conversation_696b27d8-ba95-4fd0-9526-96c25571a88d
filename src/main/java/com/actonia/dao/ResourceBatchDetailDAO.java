package com.actonia.dao;

import com.actonia.entity.ResourceBatchDetailEntity;

import java.util.ArrayList;
import java.util.List;

public class ResourceBatchDetailDAO extends BaseJdbcSupport<ResourceBatchDetailEntity> {

    @Override
    public String getTableName() {
        return "queue_base_detail";
    }


    public void insertBatchIgnoreDup(List<ResourceBatchDetailEntity> detailList) {
        String sql = "insert into " + getTableName() + " (infoId, actionType, ownDomainId,resourceId,resourceSubId,resourceMain,resourceSubordinate,resourceSearchengines,resourceMd5,resourceCategory,createDate,resourceAdditional)" +
                " values(?,?,?,?,?,?,?,?,?,?,?,?) ";
        List<Object[]> batch = new ArrayList<>();
        for (ResourceBatchDetailEntity detailEntity : detailList) {
            Object[] values = new Object[]{detailEntity.getInfoId(), detailEntity.getActionType(), detailEntity.getOwnDomainId(), detailEntity.getResourceId(), detailEntity.getResourceSubId(),
                    detailEntity.getResourceMain(), detailEntity.getResourceSubordinate(), detailEntity.getResourceSearchengines(), detailEntity.getResourceMd5(), detailEntity.getResourceCategory(), detailEntity.getCreateDate(), detailEntity.getResourceAdditional()};
            batch.add(values);
        }
        super.executeBatch(sql, batch);
    }
}