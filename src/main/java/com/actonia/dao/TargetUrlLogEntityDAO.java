package com.actonia.dao;

import com.actonia.IConstants;
import com.actonia.entity.TargetUrlEntity;
import com.actonia.entity.TargetUrlLogEntity;

import java.util.List;

public class TargetUrlLogEntityDAO extends BaseJdbcSupport<TargetUrlLogEntity> {

	@Override
	public String getTableName() {
		return "t_target_url_log";
	}

	public TargetUrlLogEntity getById(long id) {
		String sql = "select * from t_target_url_log where id = ?";
		return findObject(sql, id);
	}

	public List<TargetUrlLogEntity> getLogListByOffsetDays(int domainId) {
		StringBuffer sql = new StringBuffer();
		sql.append("select l.*, max(l.logDate) as maxLogDate")
				.append(" from t_target_url_log l")
				.append(" join t_target_url u on l.target_url_id = u.id and u.own_domain_id = ? and u.disable_crawl = 1 and u.disable_by = 3")
				.append(" where l.operationType = 2")
				.append(" and u.type = ? ")
				.append(" and u.status = ?")
				.append(" and IFNULL(l.disable_crawl, 0) = 0")
				// disable_crawl column is added after 2023-07-13 https://www.wrike.com/open.htm?id=1159167369
				.append(" and l.logDate > 20230713")
				.append(" group by l.target_url_id");
		return this.findBySql(sql.toString(), domainId, TargetUrlEntity.TYPE_ADD_BY_USER, TargetUrlEntity.STATUS_ACTIVE);
	}

}
