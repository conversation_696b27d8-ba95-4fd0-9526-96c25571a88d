package com.actonia.dao;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.actonia.entity.GoogleTagJavascriptEntity;
import com.actonia.utils.FormatUtils;

public class GoogleTagJavascriptDAO extends BaseJdbcSupport<GoogleTagJavascriptEntity> {

	@Override
	public String getTableName() {
		return "google_tag_javascript";
	}

	public Long create(GoogleTagJavascriptEntity googleTagJavascriptEntity) {
		FormatUtils.getInstance().logMemoryUsage("create() accountId=" + googleTagJavascriptEntity.getAccountId() + ",containerId=" + googleTagJavascriptEntity.getContainerId()
				+ ",workspaceName=" + googleTagJavascriptEntity.getWorkspaceName() + ",javascriptName=" + googleTagJavascriptEntity.getJavascriptName());
		Map<String, Object> values = new HashMap<String, Object>();
		values.put("account_id", googleTagJavascriptEntity.getAccountId());
		values.put("container_id", googleTagJavascriptEntity.getContainerId());
		values.put("workspace_name", googleTagJavascriptEntity.getWorkspaceName());
		values.put("javascript_name", googleTagJavascriptEntity.getJavascriptName());
		values.put("tag_id", googleTagJavascriptEntity.getTagId());
		values.put("trigger_id", googleTagJavascriptEntity.getTriggerId());
		values.put("variable_id", googleTagJavascriptEntity.getVariableId());
		values.put("script_content", googleTagJavascriptEntity.getScriptContent());
		values.put("last_update_timestamp", googleTagJavascriptEntity.getLastUpdateTimestamp());
		return insertForLongId(values);
	}

	public GoogleTagJavascriptEntity get(String accountId, String containerId, String workspaceName, String javascriptName) {
		FormatUtils.getInstance().logMemoryUsage(
				"get() accountId=" + accountId + ",containerId=" + containerId + ",workspaceName=" + workspaceName + ",javascriptName=" + javascriptName);
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     *");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     account_id = ?");
		stringBuilder.append(" and container_id = ?");
		stringBuilder.append(" and workspace_name = ?");
		stringBuilder.append(" and javascript_name = ?");
		String sqlString = stringBuilder.toString();
		return this.findObject(sqlString, accountId, containerId, workspaceName, javascriptName);
	}

	public void updateScriptContent(String accountId, String containerId, String workspaceName, String javascriptName, String data, Date lastUpdateTimestamp) {
		FormatUtils.getInstance().logMemoryUsage(
				"update() accountId=" + accountId + ",containerId=" + containerId + ",workspaceName=" + workspaceName + ",javascriptName=" + javascriptName);
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" update");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" set");
		stringBuilder.append("     script_content = ?,");
		stringBuilder.append("     last_update_timestamp = ?");
		stringBuilder.append(" where");
		stringBuilder.append("     account_id = ?");
		stringBuilder.append(" and container_id = ?");
		stringBuilder.append(" and workspace_name = ?");
		stringBuilder.append(" and javascript_name = ?");
		String sqlString = stringBuilder.toString();
		this.executeUpdate(sqlString, data, lastUpdateTimestamp, accountId, containerId, workspaceName, javascriptName);
	}

	public void delete(String accountId, String containerId, String workspaceName, String javascriptName) {
		FormatUtils.getInstance().logMemoryUsage(
				"delete() accountId=" + accountId + ",containerId=" + containerId + ",workspaceName=" + workspaceName + ",javascriptName=" + javascriptName);
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" delete from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     account_id = ?");
		stringBuilder.append(" and container_id = ?");
		stringBuilder.append(" and workspace_name = ?");
		stringBuilder.append(" and javascript_name = ?");
		String sqlString = stringBuilder.toString();
		this.executeUpdate(sqlString, accountId, containerId, workspaceName, javascriptName);
	}
}