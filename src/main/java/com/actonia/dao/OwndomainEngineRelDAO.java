package com.actonia.dao;

import java.util.List;

import com.actonia.entity.OwndomainEngineRel;

public class OwndomainEngineRelDAO extends BaseJdbcSupport<OwndomainEngineRel> {

	@Override
	public String getTableName() {
		return "owndomain_engine_rel";
	}

	public OwndomainEngineRel getPrimaryEngine(int ownDomainId) {
		StringBuilder sql = new StringBuilder();
		sql.append(" select * ");
		sql.append(" from owndomain_engine_rel ");
		sql.append(" where ownDomainId = ? ");
		sql.append(" and enabled = ? ");
		sql.append(" and primaryFlag = ? ");
				
		return findObject(sql.toString(), ownDomainId, true, true);
	}

	public List<OwndomainEngineRel> getEnabledEngines(int ownDomainId) {
		StringBuilder sql = new StringBuilder();
		sql.append(" select * ");
		sql.append(" from owndomain_engine_rel ");
		sql.append(" where ownDomainId = ? ");
		sql.append(" and enabled = ? ");
		sql.append(" order by primaryFlag desc, engineId ");

		return findBySql(sql.toString(), ownDomainId, true);
	}
}