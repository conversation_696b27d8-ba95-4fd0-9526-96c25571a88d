package com.actonia.dao;

import java.util.ArrayList;
import java.util.List;

import com.actonia.IConstants;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.entity.PageTagContentChangeEntity;

public class PageTagContentChangeDAO extends BaseJdbcSupport<PageTagContentChangeEntity> {

	private static final int NUMBER_OF_FIELDS = 8;
	private static final String INSERT_VALUE_FORMAT_1 = "(?, ?, ?, ?, ?, ?, ?, ?)";
	private static final String INSERT_VALUE_FORMAT_2 = ",(?, ?, ?, ?, ?, ?, ?, ?)";

	@Override
	public String getTableName() {
		return "page_tag_content_change";
	}

	public void insertMultiRowsBatch(List<PageTagContentChangeEntity> pageTagContentChangeEntityList) {
		Object[] objectArray = null;
		List<Object[]> objectArrayList = new ArrayList<Object[]>();
		List<PageTagContentChangeEntity> tempList = new ArrayList<PageTagContentChangeEntity>();

		for (PageTagContentChangeEntity pageTagContentChangeEntity : pageTagContentChangeEntityList) {
			tempList.add(pageTagContentChangeEntity);
			if (tempList.size() == IConstants.RECORDS_PER_SQL_STATEMENT) {
				objectArray = getObjectArrayForInsert(tempList);
				objectArrayList.add(objectArray);
				if (objectArrayList.size() == IConstants.SQL_STATEMENTS_PER_JDBC_CALL) {
					this.executeBatch(getInsertMultiRowsSqlStatement(IConstants.RECORDS_PER_SQL_STATEMENT), objectArrayList);
					objectArrayList = new ArrayList<Object[]>();
				}
				tempList = new ArrayList<PageTagContentChangeEntity>();
			}
		}

		if (objectArrayList != null && objectArrayList.size() > 0) {
			this.executeBatch(getInsertMultiRowsSqlStatement(IConstants.RECORDS_PER_SQL_STATEMENT), objectArrayList);
			objectArrayList = new ArrayList<Object[]>();
		}

		if (tempList != null && tempList.size() > 0) {
			objectArray = getObjectArrayForInsert(tempList);
			objectArrayList.add(objectArray);
			if (objectArrayList != null && objectArrayList.size() > 0) {
				this.executeBatch(getInsertMultiRowsSqlStatement(tempList.size()), objectArrayList);
				objectArrayList = new ArrayList<Object[]>();
			}
		}
	}

	private Object[] getObjectArrayForInsert(List<PageTagContentChangeEntity> pageTagContentChangeEntityList) {
		List<Object[]> tempObjectArrayList = new ArrayList<Object[]>();
		Object[] pageClarityObjectArray = null;
		int totalNumberOfObjects = 0;
		for (PageTagContentChangeEntity pageTagContentChangeEntity : pageTagContentChangeEntityList) {
			pageClarityObjectArray = new Object[] { pageTagContentChangeEntity.getDomainId(), pageTagContentChangeEntity.getGroupTagId(),
					pageTagContentChangeEntity.getChangeDate(), pageTagContentChangeEntity.getChangeType(), pageTagContentChangeEntity.getTargetUrlId(),
					pageTagContentChangeEntity.getRespCodeChange(), pageTagContentChangeEntity.getAdditionalContentChange(),
					pageTagContentChangeEntity.getDivContentChange(), };
			tempObjectArrayList.add(pageClarityObjectArray);
		}
		totalNumberOfObjects = tempObjectArrayList.size() * NUMBER_OF_FIELDS;

		Object[] objectArray = new Object[totalNumberOfObjects];
		int idx = 0;
		for (Object[] tempObjectArray : tempObjectArrayList) {
			for (int i = 0; i < tempObjectArray.length; i++) {
				objectArray[idx++] = tempObjectArray[i];
			}
		}
		return objectArray;
	}

	private String getInsertMultiRowsSqlStatement(int recordsPerInsertStatement) {
		String response = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("insert into " + getTableName() + " ");
		stringBuilder.append("(");
		stringBuilder.append(" domain_id,");
		stringBuilder.append(" group_tag_id,");
		stringBuilder.append(" change_date,");
		stringBuilder.append(" change_type,");
		stringBuilder.append(" target_url_id,");
		stringBuilder.append(" resp_code_change,");
		stringBuilder.append(" additional_content_change,");
		stringBuilder.append(" div_content_change");
		stringBuilder.append(")");
		stringBuilder.append("values ");
		String sqlString = stringBuilder.toString();

		StringBuilder sqlBuilder = new StringBuilder();
		sqlBuilder.append(sqlString);
		for (int i = 0; i < recordsPerInsertStatement; i++) {
			if (i == 0) {
				sqlBuilder.append(INSERT_VALUE_FORMAT_1);
			} else {
				sqlBuilder.append(INSERT_VALUE_FORMAT_2);
			}
		}
		sqlBuilder.append(IConstants.SEMI_COLON);
		response = sqlBuilder.toString();
		return response;
	}

	public PageTagContentChangeEntity get(int domainId, int groupTagId, int changeDateNumber, int changeType, Long targetUrlId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("select ");
		stringBuilder.append("    * ");
		stringBuilder.append("from ");
		stringBuilder.append(getTableName() + " ");
		stringBuilder.append("where ");
		stringBuilder.append("    domain_id = ? ");
		stringBuilder.append("and group_tag_id = ? ");
		stringBuilder.append("and change_date = ? ");
		stringBuilder.append("and change_type = ? ");
		stringBuilder.append("and target_url_id = ? ");
		String sqlString = stringBuilder.toString();
		return findObject(sqlString, domainId, groupTagId, changeDateNumber, changeType, targetUrlId);
	}

	public PageTagContentChangeEntity getAdditionalContentChange(int domainId, int groupTagId, int changeDateNumber, int changeType, Long targetUrlId,
			String additionalContentChange) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("select ");
		stringBuilder.append("    * ");
		stringBuilder.append("from ");
		stringBuilder.append(getTableName() + " ");
		stringBuilder.append("where ");
		stringBuilder.append("    domain_id = ? ");
		stringBuilder.append("and group_tag_id = ? ");
		stringBuilder.append("and change_date = ? ");
		stringBuilder.append("and change_type = ? ");
		stringBuilder.append("and target_url_id = ? ");
		stringBuilder.append("and additional_content_change = ? ");
		String sqlString = stringBuilder.toString();
		return findObject(sqlString, domainId, groupTagId, changeDateNumber, changeType, targetUrlId, additionalContentChange);
	}

	public PageTagContentChangeEntity getDivContentChange(int domainId, int groupTagId, int changeDateNumber, int changeType, Long targetUrlId,
			String divContentChange) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("select ");
		stringBuilder.append("    * ");
		stringBuilder.append("from ");
		stringBuilder.append(getTableName() + " ");
		stringBuilder.append("where ");
		stringBuilder.append("    domain_id = ? ");
		stringBuilder.append("and group_tag_id = ? ");
		stringBuilder.append("and change_date = ? ");
		stringBuilder.append("and change_type = ? ");
		stringBuilder.append("and target_url_id = ? ");
		stringBuilder.append("and div_content_change = ? ");
		String sqlString = stringBuilder.toString();
		return findObject(sqlString, domainId, groupTagId, changeDateNumber, changeType, targetUrlId, divContentChange);
	}

	public void reset(int domainId, int changeDateNumber) {
		String sql = "delete from " + getTableName() + " where domain_id = ? and change_date= ? ";
		this.executeUpdate(sql, domainId, changeDateNumber);
	}

	public int clear(int domainId, int cutoffDate) {
		String sql = "delete from " + getTableName() + " where domain_id = ? and change_date <= ? ";
		return this.executeUpdate(sql, domainId, cutoffDate);
	}

	public List<Integer> getDistinctDomainId() {
		List<Integer> domainIdList = new ArrayList<Integer>();
		String sql = "select distinct domain_id from " + getTableName();
		List<PageTagContentChangeEntity> pageTagContentChangeEntityList = this.findBySql(sql);
		if (pageTagContentChangeEntityList != null && pageTagContentChangeEntityList.size() > 0) {
			for (PageTagContentChangeEntity pageTagContentChangeEntity : pageTagContentChangeEntityList) {
				domainIdList.add(pageTagContentChangeEntity.getDomainId());
			}
		}
		return domainIdList;
	}

	public List<PageTagContentChangeEntity> getDomainIdTagIdList() {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("select distinct ");
		stringBuilder.append("    page_tag_content_change.domain_id, ");
		stringBuilder.append("    page_tag_content_change.group_tag_id ");
		stringBuilder.append("from ");
		stringBuilder.append("    t_own_domain t_own_domain, ");
		stringBuilder.append("    page_tag_content_change page_tag_content_change ");
		stringBuilder.append("where ");
		stringBuilder.append("    t_own_domain.`status` = ? ");
		stringBuilder.append("and t_own_domain.id = page_tag_content_change.domain_id ");
		stringBuilder.append("order by ");
		stringBuilder.append("    page_tag_content_change.domain_id, ");
		stringBuilder.append("    page_tag_content_change.group_tag_id ");
		String sqlString = stringBuilder.toString();
		return this.findBySql(sqlString, OwnDomainEntity.STATE_ACTIVE);
	}

	public int removeTagId(int domainId, int groupTagId) {
		String sql = "delete from " + getTableName() + " where domain_id = ? and group_tag_id = ? ";
		return this.executeUpdate(sql, domainId, groupTagId);
	}

	public int updateTagId(int domainId, int groupTagIdType1, int groupTagIdType9) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("update ");
		stringBuilder.append(getTableName() + " ");
		stringBuilder.append("set ");
		stringBuilder.append("    group_tag_id = ? ");
		stringBuilder.append("where ");
		stringBuilder.append("    domain_id = ? ");
		stringBuilder.append("and group_tag_id = ? ");
		String sqlString = stringBuilder.toString();
		return this.executeUpdate(sqlString, groupTagIdType9, domainId, groupTagIdType1);
	}
}