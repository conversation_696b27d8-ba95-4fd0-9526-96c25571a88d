package com.actonia.dao;

import com.actonia.IConstants;
import com.actonia.entity.ClarityAuditsEntity;

public class ClarityAuditsEntityDAO extends BaseJdbcSupport<ClarityAuditsEntity> {

	@Override
	public String getTableName() {
		return "clarity_audits_summary";
	}

	public ClarityAuditsEntity getGrandTotals(int crawlRequestLogId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     *");
		stringBuilder.append(" from");
		stringBuilder.append("     " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     crawl_request_log_id = ?");
		stringBuilder.append(" and depth = ?");
		String sqlString = stringBuilder.toString();
		return this.findObject(sqlString, crawlRequestLogId, IConstants.CLARITY_SUMMARY_MAXIMUM_DEPTH);
	}
}
