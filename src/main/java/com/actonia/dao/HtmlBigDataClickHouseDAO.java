package com.actonia.dao;

import com.actonia.IConstants;
import com.actonia.entity.HtmlBigData;
import com.actonia.utils.FormatUtils;
import lombok.SneakyThrows;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import ru.yandex.clickhouse.ClickHouseDataSource;
import ru.yandex.clickhouse.settings.ClickHouseProperties;

import java.math.BigInteger;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

// Due to ClickHouse concurrency issues, use Singleton pattern, do not use Spring (ie. all methods must be 'synchronized')
public class HtmlBigDataClickHouseDAO {

    //private static boolean isDebug = false;
    public static final String TABLE_NAME = "dis_html_big_data";
    private static final int TOTAL_DAO_INSTANCES = 1;
    private static final Logger log = LogManager.getLogger(HtmlBigDataClickHouseDAO.class);
    private static int daoMapIndex = 0;
    private static final BigInteger MOD_DIVISION = BigInteger.valueOf(1000);

    // map key = DAO index (0  - 59)
    // map value = instance of HtmlBigDataClickHouseDAO
    private static Map<Integer, HtmlBigDataClickHouseDAO> htmlBigDataClickHouseDAOMap;

    private final List<String> databaseHostnameList;
    private final String databasePort;
    private final String databaseName;
    private final List<Connection> connectionList;
    private final List<String> connectionUrlList;
    private final int batchCreationSize;
    private final String databaseUser;
    private final String databasePassword;
    private final int connectionTimeoutInMilliseconds;
    private final int maximumRetryCounts;
    private final int retryWaitMilliseconds;
    private final int daoIndex;

    private HtmlBigDataClickHouseDAO(String[] databaseHostnameArrayInput, String databasePortInput, String databaseNameInput, int batchCreationSizeInput,
                                     String databaseUserInput, String databasePasswordInput, int connectionTimeoutInMillisecondsInput, int maximumRetryCountsInput,
                                     int retryWaitMillisecondsInput, int index) throws Exception {

        ClickHouseDataSource clickHouseDataSource = null;
        Connection connection = null;

        databaseHostnameList = Arrays.asList(databaseHostnameArrayInput);
        databasePort = databasePortInput;
        databaseName = databaseNameInput;
        batchCreationSize = batchCreationSizeInput;
        databaseUser = databaseUserInput;
        databasePassword = databasePasswordInput;
        connectionTimeoutInMilliseconds = connectionTimeoutInMillisecondsInput;
        maximumRetryCounts = maximumRetryCountsInput;
        retryWaitMilliseconds = retryWaitMillisecondsInput;
        daoIndex = index;

        ClickHouseProperties clickHouseProperties = new ClickHouseProperties();
        clickHouseProperties.setDecompress(true);
        clickHouseProperties.setConnectionTimeout(connectionTimeoutInMilliseconds);
        clickHouseProperties.setSocketTimeout(connectionTimeoutInMilliseconds);

        connectionList = new ArrayList<>();

        connectionUrlList = new ArrayList<>();

        String connectionUrl = null;
        for (String databaseHostname : databaseHostnameList) {
            if (StringUtils.isNotBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
                connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
                        + "?user=" + databaseUser + "&password=" + databasePassword;
            } else if (StringUtils.isBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
                connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
                        + "?password=" + databasePassword;
            } else {
                connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH
                        + databaseName;
            }
            connectionUrlList.add(connectionUrl);
            //FormatUtils.getInstance().logMemoryUsage("HtmlBigDataClickHouseDAO() connectionUrl=" + connectionUrl);
            clickHouseDataSource = new ClickHouseDataSource(connectionUrl, clickHouseProperties);
            connection = clickHouseDataSource.getConnection();
            connectionList.add(connection);
        }
    }

    public static HtmlBigDataClickHouseDAO getInstance() throws Exception {
        HtmlBigDataClickHouseDAO htmlBigDataClickHouseDAO;
        if (htmlBigDataClickHouseDAOMap == null) {
            String clickHouseDatabaseHostnames = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_HOSTNAMES);
            String[] clickHouseDatabaseHostnameArray = clickHouseDatabaseHostnames.split(IConstants.COMMA);
            String clickHouseDatabasePort = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_PORT);
            String clickHouseDatabaseName = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_NAME);
            String clickHouseDatabaseUser = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_USER);
            String clickHouseDatabasePassword = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_PASSWORD);
            int clickHouseBatchCreationSize = ClickHouseCrawlDatabaseConfigurations
                    .getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_BATCH_CREATION_SIZE, 100);
            int clickHouseConnectionTimeoutInMilliseconds = ClickHouseCrawlDatabaseConfigurations
                    .getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_CONNECTION_TIMEOUT_MILLISECONDS, 88888);
            int clickHouseMaximumRetryCounts = ClickHouseCrawlDatabaseConfigurations.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_MAX_RETRY_COUNTS,
                    3);
            int clickHouseRetryWaitMilliseconds = ClickHouseCrawlDatabaseConfigurations
                    .getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_RETRY_WAIT_MILLISECONDS, 1000);
            FormatUtils.getInstance().logMemoryUsage("HtmlBigDataClickHouseDAO() clickHouseDatabaseHostnames=" + clickHouseDatabaseHostnames
                    + ",clickHouseDatabasePort=" + clickHouseDatabasePort + ",clickHouseDatabaseName=" + clickHouseDatabaseName + ",clickHouseBatchCreationSize="
                    + clickHouseBatchCreationSize + ",clickHouseDatabaseUser=" + clickHouseDatabaseUser + ",clickHouseDatabasePassword=" + clickHouseDatabasePassword
                    + ",clickHouseConnectionTimeoutInMilliseconds=" + clickHouseConnectionTimeoutInMilliseconds + ",clickHouseMaximumRetryCounts="
                    + clickHouseMaximumRetryCounts + ",clickHouseRetryWaitMilliseconds=" + clickHouseRetryWaitMilliseconds + ",TABLE_NAME=" + TABLE_NAME);

            htmlBigDataClickHouseDAOMap = new HashMap<>();
            for (int i = 0; i < TOTAL_DAO_INSTANCES; i++) {
                htmlBigDataClickHouseDAO = initialize(clickHouseDatabaseHostnameArray, clickHouseDatabasePort, clickHouseDatabaseName, clickHouseDatabaseUser,
                        clickHouseDatabasePassword, clickHouseBatchCreationSize, clickHouseConnectionTimeoutInMilliseconds, clickHouseMaximumRetryCounts,
                        clickHouseRetryWaitMilliseconds, i);
                htmlBigDataClickHouseDAOMap.put(i, htmlBigDataClickHouseDAO);
            }
            FormatUtils.getInstance().logMemoryUsage("getInstance() total htmlBigDataClickHouseDAOs=" + htmlBigDataClickHouseDAOMap.size());
        }
        int index = getDaoMapIndex();
        htmlBigDataClickHouseDAO = htmlBigDataClickHouseDAOMap.get(index);
        return htmlBigDataClickHouseDAO;
    }

    private static synchronized int getDaoMapIndex() {
        int index = 0;
        if (daoMapIndex >= TOTAL_DAO_INSTANCES) {
            daoMapIndex = 0;
        } else {
            index = daoMapIndex++;
        }
        return index;
    }

    // initialize HtmlBigDataClickHouseDAO based on runtime clickhouse configurations
    private static HtmlBigDataClickHouseDAO initialize(String[] clickHouseDatabaseHostnameArray, String clickHouseDatabasePort, String clickHouseDatabaseName,
                                                       String clickHouseDatabaseUser, String clickHouseDatabasePassword, int clickHouseBatchCreationSize, int clickHouseconnectionTimeoutInMilliseconds,
                                                       int clickHouseMaximumRetryCounts, int clickHouseRetryWaitMilliseconds, int daoIndex) throws Exception {
        //FormatUtils.getInstance().logMemoryUsage("initialize() htmlBigDataClickHouseDAO=" + htmlBigDataClickHouseDAO.toString());
        return new HtmlBigDataClickHouseDAO(clickHouseDatabaseHostnameArray,
                clickHouseDatabasePort, clickHouseDatabaseName, clickHouseBatchCreationSize, clickHouseDatabaseUser, clickHouseDatabasePassword,
                clickHouseconnectionTimeoutInMilliseconds, clickHouseMaximumRetryCounts, clickHouseRetryWaitMilliseconds, daoIndex);
    }

    @Override
    public String toString() {
        return "HtmlBigDataClickHouseDAO [databaseHostnameList=" + databaseHostnameList.toString() + ", databasePort=" + databasePort + ", databaseName="
                + databaseName + ", batchCreationSize=" + batchCreationSize + ", databaseUser=" + databaseUser + ", databasePassword=" + databasePassword
                + ", connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ", maximumRetryCounts=" + maximumRetryCounts + ", retryWaitMilliseconds="
                + retryWaitMilliseconds + ", daoIndex=" + daoIndex + "]";
    }

    private synchronized String getTableName(String tableName) {
        if (StringUtils.isNotBlank(tableName)) {
            return tableName;
        } else {
            return TABLE_NAME;
        }
    }

    public synchronized int getBatchCreationSize() {
        return batchCreationSize;
    }

    public void createBatch(List<HtmlBigData> htmlBigDataList) {
        final String sql = "insert into " + TABLE_NAME + " (track_date,url,md5,raw_data) values (?,?,?,?)";
        int retryCount = 0;
        while (retryCount < maximumRetryCounts) {
            Connection connection = connectionList.get(0);
            try (PreparedStatement preparedStatement = connection.prepareStatement(sql)) {
                for (HtmlBigData htmlBigData : htmlBigDataList) {
                    int preparedStatementIndex = 1;
                    preparedStatement.setObject(preparedStatementIndex++, new java.sql.Date(htmlBigData.getTrackDate().getTime()));
                    preparedStatement.setObject(preparedStatementIndex++, htmlBigData.getUrl());
                    preparedStatement.setObject(preparedStatementIndex++, htmlBigData.getMd5());
                    preparedStatement.setObject(preparedStatementIndex++, htmlBigData.getRawData());
                    preparedStatement.addBatch();
                }
                preparedStatement.executeBatch();
	            log.info("batch create htmlBigDataList, size={}", htmlBigDataList.size());
                return;
            } catch (SQLException e) {
                log.error("batchCreate() sql=" + sql, e);
                retryCount++;
                try {
                    Thread.sleep(retryWaitMilliseconds);
                } catch (InterruptedException e1) {
                    log.error("batchCreate() Thread.sleep() error", e1);
                }
            }
        }
        log.error("batchCreate() retryCount={} sql=" + sql, retryCount);
        throw new RuntimeException("batchCreate() failed");
    }

    public List<HtmlBigData> getHtmlBigDataListByTime(int urlMurmurHashMod, List<String> urlMurmurHash) {
        final String sql = "select * from " + TABLE_NAME + " where url_murmur_hash_mod = " + urlMurmurHashMod +
                " and url_murmur_hash in (" + StringUtils.join(urlMurmurHash, ",") + ")";
        return getHtmlBigData(sql);
    }

    public List<HtmlBigData> getHtmlBigDataByMod(int urlMurmurHashMod, String urlMurmurHashList) {
        final String sql = "select * from " + TABLE_NAME + " where url_murmur_hash_mod = " + urlMurmurHashMod + " and url_murmur_hash in (" + urlMurmurHashList + ")";
        return getHtmlBigData(sql);
    }

    public List<HtmlBigData> queryListByUrlMurmurHash(String urlMurmurHash, List<String> md5) {
        final BigInteger urlMurmurHashBI = new BigInteger(urlMurmurHash);
        final BigInteger mod = urlMurmurHashBI.mod(MOD_DIVISION);
        final String sql = "select * from " + TABLE_NAME + " where url_murmur_hash_mod = " + mod + " and url_murmur_hash = " + urlMurmurHash + " and md5 in (" + StringUtils.join(md5, ",") + ")";
        return getHtmlBigData(sql);
    }

    private List<HtmlBigData> getHtmlBigData(String sql) {
        List<HtmlBigData> htmlBigDataList = new ArrayList<>();
        Connection connection = connectionList.get(0);
        try (ResultSet resultSet = connection.createStatement().executeQuery(sql)) {
            while (resultSet.next()) {
                final HtmlBigData htmlBigData = getHtmlBigData(resultSet);
                htmlBigDataList.add(htmlBigData);
            }
        } catch (SQLException e) {
            log.error("getHtmlBigData() sql={}", sql, e);
            throw new RuntimeException(e);
        }
        return htmlBigDataList;
    }

    private static HtmlBigData getHtmlBigData(ResultSet resultSet) throws SQLException {
        HtmlBigData htmlBigData = new HtmlBigData();
        htmlBigData.setTrackDate(resultSet.getDate("track_date"));
        htmlBigData.setUrlHash(resultSet.getString("url_hash"));
        htmlBigData.setUrlMurmurHash(resultSet.getString("url_murmur_hash"));
        htmlBigData.setMd5(resultSet.getString("md5"));
        htmlBigData.setRawData(resultSet.getString("raw_data"));
        return htmlBigData;
    }

    public List<HtmlBigData> queryMurmurHashAndMd5ByTrackDateInUrlList(String trackDate, List<String> urlMurmurHashList) {
        final String sql = "select url_murmur_hash, md5 from " + TABLE_NAME + " where track_date = '" + trackDate + "' and url_murmur_hash in (" + StringUtils.join(urlMurmurHashList, ",") + ")";
        final Connection connection = connectionList.get(0);
        try (ResultSet resultSet = connection.createStatement().executeQuery(sql)) {
            List<HtmlBigData> htmlBigDataList = new ArrayList<>();
            while (resultSet.next()) {
                HtmlBigData htmlBigData = new HtmlBigData();
                htmlBigData.setUrlMurmurHash(resultSet.getString("url_murmur_hash"));
                htmlBigData.setMd5(resultSet.getString("md5"));
                htmlBigDataList.add(htmlBigData);
            }
            return htmlBigDataList;
        } catch (SQLException e) {
            log.error("queryMurmurHashAndMd5ByTrackDateInUrlList() sql={}", sql, e);
            throw new RuntimeException(e);
        }
    }

    @SneakyThrows
    public List<HtmlBigData> queryAllBySqlCondition(String sqlCondition) {
        final String sql = "select track_date, url_hash, url_murmur_hash, md5, raw_data from " + TABLE_NAME + " where " + sqlCondition;
        final Connection connection = connectionList.get(0);
        try (ResultSet resultSet = connection.createStatement().executeQuery(sql)) {
            List<HtmlBigData> htmlBigDataList = new ArrayList<>();
            while (resultSet.next()) {
                HtmlBigData htmlBigData = getHtmlBigData(resultSet);
                htmlBigDataList.add(htmlBigData);
            }
            return htmlBigDataList;
        } catch (SQLException e) {
            log.error("queryAllBySqlCondition() sql={}", sql, e);
            throw new RuntimeException(e);
        }
    }
}