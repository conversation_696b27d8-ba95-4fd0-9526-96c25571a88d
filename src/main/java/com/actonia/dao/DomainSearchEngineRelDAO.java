package com.actonia.dao;

import java.util.List;

import com.actonia.entity.DomainSearchEngineRelEntity;

public class DomainSearchEngineRelDAO extends BaseJdbcSupport<DomainSearchEngineRelEntity> {

	@Override
	public String getTableName() {
		return "domain_search_engine_rel";
	}
	
	public List<DomainSearchEngineRelEntity> getByOwnDomain(int ownDomainId, boolean avoidPauseEngines) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * ");
		sql.append(" from ").append(getTableName());
		sql.append(" where own_domain_id = ? ");
		sql.append(" order by id "); //by Cee#81629303
		return this.findBySql(sql.toString(), ownDomainId);
	}
}