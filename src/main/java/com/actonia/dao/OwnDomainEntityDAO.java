package com.actonia.dao;

import com.actonia.entity.OwnDomainEntity;
import com.actonia.entity.TargetUrlEntity;
import com.actonia.utils.FormatUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

public class OwnDomainEntityDAO extends BaseJdbcSupport<OwnDomainEntity> {

	//private boolean isDebug = false;

	@Override
	public String getTableName() {
		return "t_own_domain";
	}

	//Leo - https://www.wrike.com/open.htm?id=101670758
	public List<OwnDomainEntity> queryForDomainIds(String[] domainIds) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from t_own_domain where `status` = ? and id in ( ");
		sql.append(StringUtils.join(domainIds, ","));
		sql.append(" ) ");
		return findBySql(sql.toString(), OwnDomainEntity.STATE_ACTIVE);
	}

	public List<OwnDomainEntity> queryForAll() {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from t_own_domain where `status` = ? ");
		return findBySql(sql.toString(), OwnDomainEntity.STATE_ACTIVE);
	}

	public List<OwnDomainEntity> queryForDistinctDomainIds() {
		StringBuffer sql = new StringBuffer();
		sql.append(" select distinct tod.id from t_own_domain tod join t_target_url ttu on tod.id=ttu.own_domain_id where tod.status=1 and ttu.type=1 and ttu.status=1 ");
		return findBySql(sql.toString());
	}

	public List<OwnDomainEntity> getAllDomains() {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from t_own_domain order by id desc ");
		return findBySql(sql.toString());
	}

	public List<OwnDomainEntity> getSuspendOwnDomains() {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from t_own_domain where `status` = ? ");
		return findBySql(sql.toString(), OwnDomainEntity.STATE_SUSPEND);
	}

	public OwnDomainEntity getOwnDomainEntityById(int ownDomainId) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from t_own_domain where id = ?");
		return findObject(sql.toString(), ownDomainId);
	}

	//https://www.wrike.com/open.htm?id=27806116
	//by cee
	public List<OwnDomainEntity> getDomainsHasStartDayOfWeek() {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from t_own_domain where `status` = ? and start_day_of_week is not null order by id desc ");
		return findBySql(sql.toString(), OwnDomainEntity.STATE_ACTIVE);
	}

	public OwnDomainEntity getOwnDomainEntityByOwnDomainId(int ownDomainId) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from t_own_domain where id = ? and `status` = ? ");
		return findObject(sql.toString(), ownDomainId, OwnDomainEntity.STATE_ACTIVE);
	}

	public OwnDomainEntity getById(int ownDomainId) {
		//		StringBuffer sql = new StringBuffer();
		//		sql.append(" select * from t_own_domain where id = ? ");
		//		return findObject(sql.toString(), ownDomainId);
		return getOwnDomainEntityByOwnDomainId(ownDomainId);
	}

	// https://www.wrike.com/open.htm?id=39657687
	// by floyd
	public List<OwnDomainEntity> getByIdArr(String domainList) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from t_own_domain where `status` = ? and id in (" + domainList + ")");
		return findBySql(sql.toString(), OwnDomainEntity.STATE_ACTIVE);
	}


	public List<OwnDomainEntity> getDomainsByLanguage(String languageCode) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from t_own_domain where `status` = ? and language = ? ");
		return findBySql(sql.toString(), OwnDomainEntity.STATE_ACTIVE, languageCode);
	}

	public List<OwnDomainEntity> getDomainsByNotLanguage(String languageCode) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from t_own_domain where `status` = ? and language != ? ");
		return findBySql(sql.toString(), OwnDomainEntity.STATE_ACTIVE, languageCode);
	}

	public List<OwnDomainEntity> getByCrawlTrackingDate() {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("SELECT tod.id, count(*) as totalUrls");
		stringBuilder.append(" FROM t_own_domain tod, t_own_domain_setting tods, target_url_daily_crawl_tracking tudct, t_target_url ttu");
		stringBuilder.append(" WHERE tod.`status`=1");
		stringBuilder.append(" AND tod.id=tods.own_domain_id");
		stringBuilder.append(" AND ( (tods.company_name != 'Expedia') or tods.company_name is null or tods.company_name='' )");
		stringBuilder.append(" AND tod.id=tudct.domain_id");
		stringBuilder.append(" AND tudct.process_type=2");
		stringBuilder.append(" AND tod.id=ttu.own_domain_id");
		stringBuilder.append(" AND ttu.`type`=1");
		stringBuilder.append(" AND ttu.`status`=1");
		stringBuilder.append(" AND ifnull(ttu.disable_crawl,0)=0");
		stringBuilder.append(" GROUP BY tod.id");
		stringBuilder.append(" ORDER BY totalUrls;");

		String sqlString = stringBuilder.toString();
		return findBySql(sqlString);
	}

	public HashSet<OwnDomainEntity> getDomainsWithUrlCrawlParameters() {
		StringBuffer sql = new StringBuffer();
		sql.append("select tod.id, tod.domain, tod.language, tod.url_crawl_parameters from t_own_domain tod where status= ?");
		return new HashSet<>(findBySql(sql.toString(), OwnDomainEntity.STATE_ACTIVE));
	}

	public List<OwnDomainEntity> queryForInactive() {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from t_own_domain where `status` = ? ");
		return findBySql(sql.toString(), OwnDomainEntity.STATE_SUSPEND);
	}

	public List<String> getDomainsByCompanyName(String companyName) {
		List<String> domainNameList = new ArrayList<String>();
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select distinct");
		stringBuilder.append("     t_own_domain.domain");
		stringBuilder.append(" from");
		stringBuilder.append("     t_own_domain_setting t_own_domain_setting,");
		stringBuilder.append("     t_own_domain t_own_domain");
		stringBuilder.append(" where");
		stringBuilder.append("     t_own_domain_setting.company_name = ?");
		stringBuilder.append(" and t_own_domain.id = t_own_domain_setting.own_domain_id");
		stringBuilder.append(" and t_own_domain.`status` = ?");
		String sqlString = stringBuilder.toString();
		List<OwnDomainEntity> ownDomainEntityList = findBySql(sqlString, companyName, OwnDomainEntity.STATE_ACTIVE);
		if (ownDomainEntityList != null && ownDomainEntityList.size() > 0) {
			for (OwnDomainEntity ownDomainEntity : ownDomainEntityList) {
				domainNameList.add(ownDomainEntity.getDomain());
			}
		}
		return domainNameList;
	}

	// 'Expedia','CarRentals.com'
	public List<Integer> getDomainIdsByCompanyName(String companyName) {
		List<Integer> domainIdList = new ArrayList<Integer>();
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select distinct");
		stringBuilder.append("     t_own_domain.id");
		stringBuilder.append(" from");
		stringBuilder.append("     t_own_domain_setting t_own_domain_setting,");
		stringBuilder.append("     t_own_domain t_own_domain");
		stringBuilder.append(" where");
		stringBuilder.append("     t_own_domain_setting.company_name = ?");
		stringBuilder.append(" and t_own_domain.id = t_own_domain_setting.own_domain_id");
		stringBuilder.append(" and t_own_domain.`status` = ?");
		String sqlString = stringBuilder.toString();
		List<OwnDomainEntity> ownDomainEntityList = findBySql(sqlString, companyName, OwnDomainEntity.STATE_ACTIVE);
		if (ownDomainEntityList != null && ownDomainEntityList.size() > 0) {
			for (OwnDomainEntity ownDomainEntity : ownDomainEntityList) {
				domainIdList.add(ownDomainEntity.getId());
			}
		}
		return domainIdList;
	}

	public List<Integer> getDomainIdsWithTargetUrls() {
		List<Integer> domainIdList = new ArrayList<Integer>();
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select distinct");
		stringBuilder.append("     t_own_domain.id");
		stringBuilder.append(" from");
		stringBuilder.append("     t_own_domain t_own_domain,");
		stringBuilder.append("     t_own_domain_setting t_own_domain_setting,");
		stringBuilder.append("     t_target_url t_target_url");
		stringBuilder.append(" where");
		stringBuilder.append("     t_own_domain.`status` = 1");
		stringBuilder.append(" and t_own_domain.id = t_own_domain_setting.own_domain_id");
		stringBuilder.append(" and ( ");
		// https://www.wrike.com/open.htm?id=1000993989
		//stringBuilder.append("   (t_own_domain_setting.company_name != 'Lowe\\'s US' and t_own_domain_setting.company_name != 'Expedia') ");
		stringBuilder.append("   (t_own_domain_setting.company_name != 'Expedia') ");
		stringBuilder.append(" or t_own_domain_setting.company_name is null ");
		stringBuilder.append(" or t_own_domain_setting.company_name = '' ");
		stringBuilder.append(" ) ");
		stringBuilder.append(" and t_own_domain_setting.own_domain_id = t_target_url.own_domain_id");
		stringBuilder.append(" and t_target_url.`type` = 1");
		stringBuilder.append(" and t_target_url.`status` = 1");
		stringBuilder.append(" AND ifnull(t_target_url.disable_crawl,0)=0");
		String sqlString = stringBuilder.toString();
		FormatUtils.getInstance().logMemoryUsage("getDomainIdsWithTargetUrls() sqlString=" + sqlString);
		List<OwnDomainEntity> ownDomainEntityList = findBySql(sqlString);
		if (ownDomainEntityList != null && ownDomainEntityList.size() > 0) {
			for (OwnDomainEntity ownDomainEntity : ownDomainEntityList) {
				domainIdList.add(ownDomainEntity.getId());
			}
		}
		return domainIdList;
	}

	public List<OwnDomainEntity> getDomainsWithUrlsNotMoreThanLimit(int limit) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select d.*");
		stringBuilder.append(" from t_own_domain d ");
		stringBuilder.append(" join (select u.own_domain_id, count(u.id) as totalUrls from t_target_url u ");
		stringBuilder.append(" join polite_crawl_domain_setting setting on setting.ownDomainId = u.own_domain_id and setting.enabled = 1 ");
		stringBuilder.append(" where u.type = ? and u.status = ? and ifnull(u.disable_crawl,0) = 0 ");
		stringBuilder.append(" group by u.own_domain_id having count(*) <= ").append(limit);
		stringBuilder.append(" order by totalUrls desc ) s ");
		stringBuilder.append(" on s.own_domain_id = d.id where d.`status` = ? ");
		String sqlString = stringBuilder.toString();
		return findBySql(sqlString, TargetUrlEntity.TYPE_ADD_BY_USER, TargetUrlEntity.STATUS_ACTIVE, OwnDomainEntity.STATE_ACTIVE);
	}

	public List<OwnDomainEntity> getDomainsWithUrlsMoreThanLimit(int limit) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select d.* ");
		stringBuilder.append(" from t_own_domain d ");
		stringBuilder.append(" join (select u.own_domain_id, count(u.id) as totalUrls from t_target_url u ");
		stringBuilder.append(" join polite_crawl_domain_setting setting on setting.ownDomainId = u.own_domain_id and setting.enabled = 1 ");
		stringBuilder.append(" where u.type = ? and u.status = ? and ifnull(u.disable_crawl,0) = 0 ");
		stringBuilder.append(" group by u.own_domain_id having count(*) > ").append(limit);
		stringBuilder.append(" order by totalUrls desc ) s ");
		stringBuilder.append(" on s.own_domain_id = d.id where d.`status` = ? ");
		String sqlString = stringBuilder.toString();
		FormatUtils.getInstance().logMemoryUsage("getDomainsWithUrlsMoreThanLimit() sqlString=" + sqlString);
		return findBySql(sqlString, TargetUrlEntity.TYPE_ADD_BY_USER, TargetUrlEntity.STATUS_ACTIVE, OwnDomainEntity.STATE_ACTIVE);
	}

	public List<OwnDomainEntity> getDomainsWithTargetUrls() {
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT d.* ")
				.append("FROM t_own_domain d ")
				.append("JOIN ( ")
				.append(" SELECT s.ownDomainId, COUNT(u.id) cnt ")
				.append(" FROM polite_crawl_domain_setting s ")
				.append(" JOIN t_target_url u ON s.ownDomainId = u.own_domain_id ")
				.append("   AND u.status = ? AND u.type = ? AND IFNULL(disable_crawl, 0) = 0 ")
				.append(" WHERE s.enabled = 1 ")
				.append(" GROUP BY s.ownDomainId ")
				.append(" HAVING cnt > 0 ")
				.append(") s ON d.id = s.ownDomainId ")
				.append("WHERE d.status = ?");
		String sqlString = sql.toString();
		return findBySql(sqlString, TargetUrlEntity.STATUS_ACTIVE, TargetUrlEntity.TYPE_ADD_BY_USER, OwnDomainEntity.STATE_ACTIVE);
	}

	/**
	 * get domains filtered by function_entity.title
	 *  `resourceType` tinyint(1) unsigned NOT NULL COMMENT '1:OwnDomainGroup, 2:OwnDomain'
	 * @param functionEntityTitle function_entity.title
	 * @param valueType function_relation.valueType
	 * @param value function_relation.value
	 * @return List<OwnDomainEntity>
	 */
	public List<OwnDomainEntity> getDomainsFilteredByFunctionEntityTitle(String functionEntityTitle, int valueType, String value) {
		StringBuilder sqlBuilder = new StringBuilder();
		sqlBuilder.append("SELECT tod.* ");
		sqlBuilder.append(" FROM function_entity fe ");
		sqlBuilder.append(" join function_relation fr on fr.funcId = fe.id and fe.title = ? ");
		sqlBuilder.append(" join t_own_domain tod on fr.resourceId = tod.id and tod.status = 1 ");
		sqlBuilder.append(" join polite_crawl_domain_setting pcds on pcds.ownDomainId = tod.id and pcds.enabled = 1 ");
		sqlBuilder.append(" WHERE resourceType = 2 and valueType = ? and value = ? ");
		sqlBuilder.append(" union all ");
		sqlBuilder.append(" SELECT tod.* ");
		sqlBuilder.append(" FROM function_entity fe ");
		sqlBuilder.append(" join function_relation fr on fr.funcId = fe.id and fr.valueType = ? and fr.value = ? ");
		sqlBuilder.append(" join owndomain_group og on fr.resourceId = og.id and fr.resourceType = 1 ");
		sqlBuilder.append(" join owndomain_group_rel rel on og.id = rel.groupId ");
		sqlBuilder.append(" join t_own_domain tod on rel.ownDomainId=tod.id and tod.status=1 ");
		sqlBuilder.append(" join polite_crawl_domain_setting pcds on pcds.ownDomainId = tod.id and pcds.enabled = 1 ");
		sqlBuilder.append(" WHERE fe.title = ? ");

		String sql = sqlBuilder.toString();
		return findBySql(sql, functionEntityTitle, valueType, value, valueType, value, functionEntityTitle);
	}

	/**
	 * find domains with target url and order by urls count
	 * @return List<OwnDomainEntity>
	 */
	public List<OwnDomainEntity> findDomainsWithTargetUrl() {
		StringBuilder sql = new StringBuilder();
		sql.append("select d.id, d.name, d.language, d.status from t_own_domain d ")
				.append("join(select own_domain_id, count(t1.id) urlCount from t_target_url t1 ")
				.append("join t_own_domain t2 on t1.own_domain_id = t2.id and t2.status =1 ")
				.append("where t1.type = 1 and t1.status = 1 ")
				.append("group by t1.own_domain_id) u on d.id = u.own_domain_id ")
				.append("order by urlCount");
		return findBySql(sql.toString());
	}

	public List<OwnDomainEntity> getLargeTargetUrlDomainIds(int limit) {
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT d.id, d.name, d.sub_folder, d.language, d.url_crawl_parameters, d.status ")
				.append("FROM t_own_domain d JOIN ( ")
				.append("SELECT own_domain_id, COUNT(*) urlCount ")
				.append("FROM t_target_url t1 JOIN t_own_domain t2 ON t1.own_domain_id = t2.id ")
				.append("WHERE t1.type = 1 AND t1.status = 1 AND t2.status = 1 ")
				.append("GROUP BY own_domain_id HAVING urlCount > ").append(limit)
				.append(") t ON d.id = t.own_domain_id ")
				.append("ORDER BY urlCount");
		return super.findBySql(sql.toString());
	}

	public List<OwnDomainEntity> getSmallTargetUrlDomainIds(int limit) {
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT d.id, d.name, d.sub_folder, d.language, d.url_crawl_parameters, d.status ")
				.append("FROM t_own_domain d JOIN ( ")
				.append("SELECT own_domain_id, COUNT(*) urlCount ")
				.append("FROM t_target_url t1 JOIN t_own_domain t2 ON t1.own_domain_id = t2.id ")
				.append("WHERE t1.type = 1 AND t1.status = 1 AND t2.status = 1 ")
				.append("GROUP BY own_domain_id HAVING urlCount <= ").append(limit)
				.append(") t ON d.id = t.own_domain_id ")
				.append("ORDER BY urlCount");
		return super.findBySql(sql.toString());
	}
}
