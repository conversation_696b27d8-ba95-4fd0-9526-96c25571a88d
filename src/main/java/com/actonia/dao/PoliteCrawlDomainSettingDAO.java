package com.actonia.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.jdbc.core.simple.ParameterizedRowMapper;

import com.actonia.entity.PoliteCrawlDomainSetting;

public class PoliteCrawlDomainSettingDAO extends BaseJdbcSupport<PoliteCrawlDomainSetting> {
    private static final String TABLE_NAME = "polite_crawl_domain_setting";
    private static final String ID_COLUMN = "id";
    private static final String OWN_DOMAIN_ID_COLUMN = "ownDomainId";
    private static final String ENABLED_COLUMN = "enabled";
    private static final String FREQUENCE_COLUMN = "frequence";
    private static final String ENABLE_RANKING_URL_COLUMN = "enable_rankingurl";
    private static final String TOPX_RANKING_URL_COLUMN = "topx_rankingurl";
    private static final String CREATE_DATE_COLUMN = "createDate";

    // find all list where frequence is 1
    public List<PoliteCrawlDomainSetting> findAll() {
        final String sql = "SELECT * FROM " + TABLE_NAME + " WHERE " + FREQUENCE_COLUMN + " = 1";
        return super.getSimpleJdbcTemplate().query(sql, new PoliteCrawlDomainSettingRowMapper());
    }

    public PoliteCrawlDomainSetting findByOwnDomainId(int ownDomainId) {
        final StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM ")
                .append(TABLE_NAME)
                .append(" WHERE ")
                .append(OWN_DOMAIN_ID_COLUMN)
                .append(" = ?");
        return getSimpleJdbcTemplate().queryForObject(sql.toString(), new PoliteCrawlDomainSettingRowMapper(), ownDomainId);
    }

    public PoliteCrawlDomainSetting addPoliteCrawlDomainSetting(PoliteCrawlDomainSetting setting) {
        final StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO ")
                .append(TABLE_NAME)
                .append(" (")
                .append(OWN_DOMAIN_ID_COLUMN).append(", ")
                .append(ENABLED_COLUMN).append(", ")
                .append(FREQUENCE_COLUMN).append(", ")
                .append(CREATE_DATE_COLUMN).append(", ")
                .append(ENABLE_RANKING_URL_COLUMN).append(", ")
                .append(TOPX_RANKING_URL_COLUMN)
                .append(") VALUES (?, ?, ?, ?, ?, ?)");
        Map<String, Object> params = new HashMap<>();
        params.put(OWN_DOMAIN_ID_COLUMN, setting.getOwnDomainId());
        params.put(ENABLED_COLUMN, setting.getEnabled().getValue());
        params.put(FREQUENCE_COLUMN, setting.getFrequence());
        params.put(CREATE_DATE_COLUMN, setting.getCreateDate());
        params.put(ENABLE_RANKING_URL_COLUMN, setting.getEnableRankingUrl());
        params.put(TOPX_RANKING_URL_COLUMN, setting.getTopxRankingUrl());
        final int id = super.insert(params);
        setting.setId(id);
        return setting;
    }

    public void update(PoliteCrawlDomainSetting setting) {
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE ").append(TABLE_NAME).append(" SET ");
        boolean hasUpdates = false;
        List<Object> params = new ArrayList<>();

        if (setting.getEnabled() != null) {
            sql.append(ENABLED_COLUMN)
                    .append(" = ?, ");
            params.add(setting.getEnabled().getValue());
            hasUpdates = true;
        }
        if (setting.getFrequence() != null) {
            sql.append(FREQUENCE_COLUMN)
                    .append(" = ?, ");
            params.add(setting.getFrequence());
            hasUpdates = true;
        }

        if (hasUpdates) {
            sql.delete(sql.length() - 2, sql.length());
            sql.append(" WHERE ")
                    .append(ID_COLUMN)
                    .append(" = ?");
            params.add(setting.getId());
            super.executeUpdate(sql.toString(), params.toArray());
        }
    }

    public List<PoliteCrawlDomainSetting> findByEnabledStatus(PoliteCrawlDomainSetting.EnabledStatus enabled) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM ")
                .append(TABLE_NAME)
                .append(" WHERE ").append(ENABLED_COLUMN)
                .append(" = ?");
        return super.getSimpleJdbcTemplate().query(sql.toString(), new PoliteCrawlDomainSettingRowMapper(), enabled.getValue());
    }

    public Set<Integer> findDomainIdsByEnabledStatus() {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT ")
                .append(OWN_DOMAIN_ID_COLUMN)
                .append(" FROM ")
                .append(TABLE_NAME)
                .append(" WHERE ")
                .append(ENABLED_COLUMN)
                .append(" != ?");
        final List<Integer> integers = super.queryForIntegerList(sql.toString(), PoliteCrawlDomainSetting.EnabledStatus.ENABLED.getValue());
        return new HashSet<>(integers);
    }

    @Override
    public String getTableName() {
        return TABLE_NAME;
    }

    private final static class PoliteCrawlDomainSettingRowMapper implements ParameterizedRowMapper<PoliteCrawlDomainSetting> {

        @Override
        public PoliteCrawlDomainSetting mapRow(ResultSet rs, int rowNum) throws SQLException {
            PoliteCrawlDomainSetting setting = new PoliteCrawlDomainSetting();
            setting.setId(rs.getInt(ID_COLUMN));
            setting.setOwnDomainId(rs.getInt(OWN_DOMAIN_ID_COLUMN));
            setting.setEnabled(PoliteCrawlDomainSetting.EnabledStatus.values()[(rs.getByte(ENABLED_COLUMN))]);
            setting.setFrequence(rs.getInt(FREQUENCE_COLUMN));
            setting.setEnableRankingUrl(rs.getInt(ENABLE_RANKING_URL_COLUMN));
            setting.setTopxRankingUrl(rs.getInt(TOPX_RANKING_URL_COLUMN));
            setting.setCreateDate(rs.getTimestamp(CREATE_DATE_COLUMN).toLocalDateTime());
            return setting;
        }
    }

}
