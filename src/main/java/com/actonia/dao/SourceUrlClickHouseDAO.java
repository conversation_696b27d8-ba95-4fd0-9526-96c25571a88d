package com.actonia.dao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.StringUtils;

import com.actonia.IConstants;
import com.actonia.entity.SourceUrlClickHouseEntity;
import com.actonia.utils.FormatUtils;

import ru.yandex.clickhouse.ClickHouseDataSource;
import ru.yandex.clickhouse.settings.ClickHouseProperties;

// Due to ClickHouse concurrency issues, use Singleton pattern, do not use Spring (ie. all methods must be 'synchronized')
public class SourceUrlClickHouseDAO {

	//private static boolean isDebug = false;
	private static List<String> databaseHostnameList;
	private static String databasePort = null;
	private static String databaseName = null;
	private static List<Connection> connectionList;
	private static int batchCreationSize;
	private static String databaseUser = null;
	private static String databasePassword = null;
	private static List<String> connectionUrlList = null;
	private static int connectionTimeoutInMilliseconds;
	private static int maximumRetryCounts;
	private static int retryWaitMilliseconds;
	public static final String TABLE_NAME = "dis_source_url";
	private static SourceUrlClickHouseDAO sourceUrlClickHouseDAO;

	public static SourceUrlClickHouseDAO getInstance() throws Exception {
		if (sourceUrlClickHouseDAO == null) {
			sourceUrlClickHouseDAO = initialize();
		}
		return sourceUrlClickHouseDAO;
	}

	private static SourceUrlClickHouseDAO initialize() throws Exception {
		FormatUtils.getInstance().logMemoryUsage("initialize() begins.");

		SourceUrlClickHouseDAO sourceUrlClickHouseDAO = null;

		String clickHouseDatabaseHostnames = ClickHouseCrawlDatabaseConfigurations
				.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_HOSTNAMES);
		String[] clickHouseDatabaseHostnameArray = clickHouseDatabaseHostnames.split(IConstants.COMMA);
		String clickHouseDatabasePort = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_PORT);
		String clickHouseDatabaseName = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_NAME);
		String clickHouseDatabaseUser = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_USER);
		String clickHouseDatabasePassword = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_PASSWORD);
		int clickHouseBatchCreationSize = ClickHouseCrawlDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_BATCH_CREATION_SIZE, 100);
		int clickHouseconnectionTimeoutInMilliseconds = ClickHouseCrawlDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_CONNECTION_TIMEOUT_MILLISECONDS, 88888);
		int clickHouseMaximumRetryCounts = ClickHouseCrawlDatabaseConfigurations.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_MAX_RETRY_COUNTS,
				8);
		int clickHouseRetryWaitMilliseconds = ClickHouseCrawlDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_RETRY_WAIT_MILLISECONDS, 1000);

		try {
			sourceUrlClickHouseDAO = new SourceUrlClickHouseDAO(clickHouseDatabaseHostnameArray, clickHouseDatabasePort, clickHouseDatabaseName,
					clickHouseBatchCreationSize, clickHouseDatabaseUser, clickHouseDatabasePassword, clickHouseconnectionTimeoutInMilliseconds,
					clickHouseMaximumRetryCounts, clickHouseRetryWaitMilliseconds);
		} catch (Exception e) {
			e.printStackTrace();
		}

		FormatUtils.getInstance().logMemoryUsage("initialize() ends. sourceUrlClickHouseDAO=" + sourceUrlClickHouseDAO.toString());
		return sourceUrlClickHouseDAO;
	}

	private SourceUrlClickHouseDAO(String[] databaseHostnameArrayInput, String databasePortInput, String databaseNameInput, int batchCreationSizeInput,
			String databaseUserInput, String databasePasswordInput, int connectionTimeoutInMillisecondsInput, int maximumRetryCountsInput,
			int retryWaitMillisecondsInput) throws Exception {

		ClickHouseDataSource clickHouseDataSource = null;
		Connection connection = null;

		databaseHostnameList = Arrays.asList(databaseHostnameArrayInput);
		databasePort = databasePortInput;
		databaseName = databaseNameInput;
		batchCreationSize = batchCreationSizeInput;
		databaseUser = databaseUserInput;
		databasePassword = databasePasswordInput;
		connectionTimeoutInMilliseconds = connectionTimeoutInMillisecondsInput;
		maximumRetryCounts = maximumRetryCountsInput;
		retryWaitMilliseconds = retryWaitMillisecondsInput;

		FormatUtils.getInstance()
				.logMemoryUsage("SourceUrlClickHouseDAO() databaseHostnameList=" + databaseHostnameList.toString() + ",databasePort=" + databasePort + ",databaseName="
						+ databaseName + ",batchCreationSize=" + batchCreationSize + ",databaseUser=" + databaseUser + ",databasePassword=" + databasePassword
						+ ",connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ",maximumRetryCounts=" + maximumRetryCounts
						+ ",retryWaitMilliseconds=" + retryWaitMilliseconds + ",TABLE_NAME=" + TABLE_NAME);

		ClickHouseProperties clickHouseProperties = new ClickHouseProperties();
		clickHouseProperties.setDecompress(true);
		clickHouseProperties.setConnectionTimeout(connectionTimeoutInMilliseconds);
		clickHouseProperties.setSocketTimeout(connectionTimeoutInMilliseconds);

		connectionList = new ArrayList<Connection>();

		String connectionUrl = null;

		connectionUrlList = new ArrayList<String>();

		for (String databaseHostname : databaseHostnameList) {
			if (StringUtils.isNotBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
						+ "?user=" + databaseUser + "&password=" + databasePassword;
			} else if (StringUtils.isBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
						+ "?password=" + databasePassword;
			} else {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH
						+ databaseName;
			}
			connectionUrlList.add(connectionUrl);
			clickHouseDataSource = new ClickHouseDataSource(connectionUrl, clickHouseProperties);
			connection = clickHouseDataSource.getConnection();
			connectionList.add(connection);
			FormatUtils.getInstance().logMemoryUsage("SourceUrlClickHouseDAO() database connection created...connectionUrl=" + connectionUrl);
		}
	}

	@Override
	public String toString() {
		return "SourceUrlClickHouseDAO [databaseHostnameList=" + databaseHostnameList.toString() + ", databasePort=" + databasePort + ", databaseName=" + databaseName
				+ ", batchCreationSize=" + batchCreationSize + ", databaseUser=" + databaseUser + ", databasePassword=" + databasePassword
				+ ", connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ", maximumRetryCounts=" + maximumRetryCounts + ", retryWaitMilliseconds="
				+ retryWaitMilliseconds + "]";
	}

	//public synchronized String getTableName() {
	//	return getTableName(null);
	//}

	public synchronized String getTableName(String tableName) {
		if (StringUtils.isNotBlank(tableName)) {
			return tableName;
		} else {
			return TABLE_NAME;
		}
	}

	public synchronized int getBatchCreationSize() {
		return batchCreationSize;
	}

	public synchronized void createBatch(List<SourceUrlClickHouseEntity> sourceUrlClickHouseEntityList, String tableName) throws Exception {
		long startTimestamp = System.currentTimeMillis();

		PreparedStatement preparedStatement = null;
		int index = 0;
		Connection connection = null;

		String creationSqlStatement = getCreationSqlStatement(tableName);
		//FormatUtils.getInstance().logMemoryUsage("createBatch() creationSqlStatement=" + creationSqlStatement);

		int retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				//startTimestamp = System.nanoTime();
				connection = connectionList.get(0);
				preparedStatement = connection.prepareStatement(creationSqlStatement);
				for (SourceUrlClickHouseEntity sourceUrlClickHouseEntity : sourceUrlClickHouseEntityList) {

					index = 1;

					// track_date
					preparedStatement.setDate(index++, new java.sql.Date(sourceUrlClickHouseEntity.getTrack_date().getTime()));

					// domain_id
					preparedStatement.setInt(index++, sourceUrlClickHouseEntity.getDomain_id() != null ? sourceUrlClickHouseEntity.getDomain_id() : -1);

					// url
					preparedStatement.setString(index++, sourceUrlClickHouseEntity.getUrl());

					preparedStatement.addBatch();
				}
				preparedStatement.executeBatch();
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("createBatch() e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			} finally {
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("createBatch() sourceUrlClickHouseEntityList.size()==" + sourceUrlClickHouseEntityList.size() + ",elapsed(ms.)="
				+ (System.currentTimeMillis() - startTimestamp));
	}

	private synchronized String getCreationSqlStatement(String tableName) {
		String response = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" insert into " + getTableName(tableName));
		stringBuilder.append(" (");
		stringBuilder.append(" track_date,");
		stringBuilder.append(" domain_id,");
		stringBuilder.append(" url");
		stringBuilder.append(" )");
		stringBuilder.append(" values ");
		stringBuilder.append(" (");
		stringBuilder.append(" ?,");
		stringBuilder.append(" ?,");
		stringBuilder.append(" ?");
		stringBuilder.append(" )");
		stringBuilder.append(IConstants.SEMI_COLON);
		response = stringBuilder.toString();
		return response;
	}

	public List<SourceUrlClickHouseEntity> getList(String tableName) throws Exception {
		List<SourceUrlClickHouseEntity> sourceUrlClickHouseEntityList = new ArrayList<SourceUrlClickHouseEntity>();
		SourceUrlClickHouseEntity sourceUrlClickHouseEntity = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("   *");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));

		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getList() sqlString=" + sqlString);
		connection = connectionList.get(0);
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				preparedStatement = connection.prepareStatement(sqlString);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					sourceUrlClickHouseEntity = new SourceUrlClickHouseEntity();

					// track_date
					sourceUrlClickHouseEntity.setTrack_date(resultSet.getDate(IConstants.TRACK_DATE));

					// domain_id
					sourceUrlClickHouseEntity.setDomain_id(resultSet.getInt(IConstants.DOMAIN_ID));

					// url
					sourceUrlClickHouseEntity.setUrl(resultSet.getString(IConstants.URL));

					// url_hash
					sourceUrlClickHouseEntity.setUrl_hash(resultSet.getString(IConstants.URL_HASH));

					// url_murmur_hash
					sourceUrlClickHouseEntity.setUrl_murmur_hash(resultSet.getString(IConstants.URL_MURMUR_HASH));

					sourceUrlClickHouseEntityList.add(sourceUrlClickHouseEntity);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getList() e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			} finally {
				if (resultSet != null) {
					resultSet.close();
				}
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();
				}
			}
		}
		//FormatUtils.getInstance().logMemoryUsage("getList() ends. startTrackDate="+startTrackDate +",endTrackDate="+endTrackDate +",urlString="+urlString
		//		+ ",sourceUrlClickHouseEntityList.size()=" + sourceUrlClickHouseEntityList.size()
		//		+ ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp)
		//);
		return sourceUrlClickHouseEntityList;
	}

	public int getTotalRecords(Date trackDate, int domainId, String tableName) throws Exception {
		int totalRecords = 0;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		int index = 0;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     count(*) as " + IConstants.TOTAL_RECORDS);
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("     track_date = ?");
		stringBuilder.append(" and domain_id = ?");
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getTotalRecords() sqlString=" + sqlString);
		connection = connectionList.get(0);
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setDate(index++, new java.sql.Date(trackDate.getTime()));
				preparedStatement.setInt(index++, domainId);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					totalRecords = resultSet.getInt(IConstants.TOTAL_RECORDS);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getTotalRecords() e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			} finally {
				if (resultSet != null) {
					resultSet.close();
				}
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();
				}
			}
		}
		//FormatUtils.getInstance().logMemoryUsage("getTotalRecords() ends. startTrackDate="+startTrackDate +",endTrackDate="+endTrackDate +",urlString="+urlString
		//		+ ",sourceUrlClickHouseEntityList.size()=" + sourceUrlClickHouseEntityList.size()
		//		+ ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp)
		//);
		return totalRecords;
	}
}
