/**
 *
 */
package com.actonia.dao;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;

import com.actonia.IConstants;
import com.actonia.entity.TargetUrlEntity;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.Md5Util;

/**
 * com.actonia.subserver.dao.TargetUrlEntityDAO.java
 *
 * @version $Revision: 110613 $ $Author: xucongjie@SHINETECHCHINA $
 */
public class TargetUrlEntityDAO extends BaseJdbcSupport<TargetUrlEntity> {

	@Override
	public String getTableName() {
		return "t_target_url";
	}

	public TargetUrlEntity getById(long id) {
		String sql = "select * from t_target_url where id = ?";
		return findObject(sql, id);
	}

	public List<TargetUrlEntity> getUrlByDomainId(int domainId) {
		FormatUtils.getInstance().logMemoryUsage("getUrlByDomainId() begins. domainId=" + domainId);
		List<TargetUrlEntity> targetUrlEntityList = null;
		StringBuffer stringBuilder = new StringBuffer();
		stringBuilder.append(" select url as url");
		stringBuilder.append(" from t_target_url");
		stringBuilder.append(" where own_domain_id = ?");
		stringBuilder.append(" and type = ?");
		stringBuilder.append(" and status = ?");
		String sqlString = stringBuilder.toString();
		targetUrlEntityList = this.findBySql(sqlString, domainId, TargetUrlEntity.TYPE_ADD_BY_USER, TargetUrlEntity.STATUS_ACTIVE);
		FormatUtils.getInstance().logMemoryUsage("getUrlByDomainId() ends. domainId=" + domainId + ",targetUrlEntityList.size()=" + targetUrlEntityList.size());
		return targetUrlEntityList;
	}

	public Map<Long, String> getIdUrlMap(int ownDomainID) {
		Map<Long, String> result = new HashMap<Long, String>();
		String url = null;
		Long id = null;
		StringBuffer sql = new StringBuffer();
		sql.append(" select ");
		sql.append("     id as id, ");
		sql.append("     url as url ");
		sql.append(" from ");
		sql.append("     t_target_url ");
		sql.append(" where ");
		sql.append("     own_domain_id = ? ");
		sql.append(" and type = ? ");
		sql.append(" and status = ? ");
		String sqlString = sql.toString();
		List<TargetUrlEntity> targetUrlEntityList = this.findBySql(sqlString, ownDomainID, IConstants.TARGET_URL_TYPE_ADDED_BY_USER,
				IConstants.TARGET_URL_STATUS_ACTIVE);
		if (targetUrlEntityList != null && !targetUrlEntityList.isEmpty()) {
			for (TargetUrlEntity targetUrlEntity : targetUrlEntityList) {
				if (targetUrlEntity.getUrl() != null && targetUrlEntity.getUrl().trim().length() > 0) {
					id = targetUrlEntity.getId();
					url = targetUrlEntity.getUrl();
					result.put(id, url);
				}
			}
		}
		return result;
	}

	public List<TargetUrlEntity> getIdUrlAdditionalContent(int ownDomainID) {
		StringBuffer sql = new StringBuffer();
		sql.append("select ");
		sql.append("	 t_target_url.id as id, ");
		sql.append("	 t_target_url.url as url, ");
		sql.append("	 t_option.option_type as weekEntrances, ");
		sql.append("	 t_option.option_text as friendlyName ");
		sql.append("from  ");
		sql.append("	 t_target_url t_target_url ");
		sql.append("	 	left join t_option t_option ");
		sql.append("	 		on  t_target_url.own_domain_id = t_option.own_domain_id ");
		sql.append("			and t_target_url.id = t_option.url_id ");
		sql.append("			and t_option.option_type in (3,4) ");
		sql.append("where ");
		sql.append("    t_target_url.own_domain_id = ? ");
		sql.append("and t_target_url.`type` = ? ");
		sql.append("and t_target_url.`status` = ? ");
		String sqlString = sql.toString();
		List<TargetUrlEntity> targetUrlEntityList = this.findBySql(sqlString, ownDomainID, IConstants.TARGET_URL_TYPE_ADDED_BY_USER,
				IConstants.TARGET_URL_STATUS_ACTIVE);
		return targetUrlEntityList;
	}

	public List<TargetUrlEntity> getIdUrl(int domainID) {
		StringBuffer sql = new StringBuffer();
		sql.append("select ");
		sql.append("	t_target_url.id as id, ");
		sql.append("	t_target_url.url as url ");
		sql.append("from  ");
		sql.append("	t_target_url t_target_url ");
		sql.append("where ");
		sql.append("    t_target_url.own_domain_id = ? ");
		sql.append("and t_target_url.`type` = ? ");
		sql.append("and t_target_url.`status` = ? ");
		String sqlString = sql.toString();
		List<TargetUrlEntity> targetUrlEntityList = this.findBySql(sqlString, domainID, IConstants.TARGET_URL_TYPE_ADDED_BY_USER, IConstants.TARGET_URL_STATUS_ACTIVE);
		return targetUrlEntityList;
	}

	public List<TargetUrlEntity> getTargetUrlList(int ownDomainID) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select url, source_type, initial_crawl_only ");
		sql.append(" from ");
		sql.append("     t_target_url");
		sql.append(" where");
		sql.append("     own_domain_id = ?");
		sql.append(" and type = ?");
		sql.append(" and status = ?");
		sql.append(" and IFNULL(disable_crawl, 0) = ?");
		String sqlString = sql.toString();
		return this.findBySql(sqlString, ownDomainID, IConstants.TARGET_URL_TYPE_ADDED_BY_USER, IConstants.TARGET_URL_STATUS_ACTIVE, IConstants.TARGET_URL_DISABLE_CRAWL_INACTIVE);
	}
	public List<TargetUrlEntity> getTargetUrlListWithoutDisableCrawl(int ownDomainID) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select url, url_hash, urlMurmur3Hash ");
		sql.append(" from ");
		sql.append("     t_target_url");
		sql.append(" where");
		sql.append("     own_domain_id = ?");
		sql.append(" and type = ?");
		sql.append(" and status = ?");
		String sqlString = sql.toString();
		return this.findBySql(sqlString, ownDomainID, IConstants.TARGET_URL_TYPE_ADDED_BY_USER, IConstants.TARGET_URL_STATUS_ACTIVE);
	}

	public TargetUrlEntity getTotalTargetUrls(int ownDomainID) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select count(*) as totalUrls");
		sql.append(" from ");
		sql.append("     t_target_url");
		sql.append(" where");
		sql.append("     own_domain_id = ?");
		sql.append(" and type = ?");
		sql.append(" and status = ?");
		String sqlString = sql.toString();
		return this.findObject(sqlString, ownDomainID, IConstants.TARGET_URL_TYPE_ADDED_BY_USER, IConstants.TARGET_URL_STATUS_ACTIVE);
	}

	public List<TargetUrlEntity> getManagedTargetUrlEntityList(int ownDomainID) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     id,");
		stringBuilder.append("     url,");
		stringBuilder.append("     week_entrances,");
		stringBuilder.append("     friendly_name,");
		stringBuilder.append("     week_bounces");
		stringBuilder.append(" from");
		stringBuilder.append("     t_target_url");
		stringBuilder.append(" where");
		stringBuilder.append("     own_domain_id = ?");
		stringBuilder.append(" and type = ?");
		stringBuilder.append(" and status = ?");
		String sqlString = stringBuilder.toString();
		return this.findBySql(sqlString, ownDomainID, IConstants.TARGET_URL_TYPE_ADDED_BY_USER, IConstants.TARGET_URL_STATUS_ACTIVE);
	}

	public int getTargetUrlCount(int domainId, int type, int status) {
		String sqlString = "select count(*) from t_target_url where own_domain_id = ? and type = ? and status = ? ";
		return queryForInt(sqlString, domainId, type, status);
	}

	public List<TargetUrlEntity> getTargetUrlIdsByGenericSearchPattern(int domainId, String genericSearchPattern) {
		StringBuffer stringBuffer = new StringBuffer();
		stringBuffer.append("select ");
		stringBuffer.append("	 id ");
		stringBuffer.append("from ");
		stringBuffer.append("	 t_target_url ");
		stringBuffer.append("where ");
		stringBuffer.append("    own_domain_id = ? ");
		stringBuffer.append("and `type` = ? ");
		stringBuffer.append("and `status` = ? ");
		stringBuffer.append("and url like ? ");
		String sqlString = stringBuffer.toString();
		return findBySql(sqlString, domainId, TargetUrlEntity.TYPE_ADD_BY_USER, TargetUrlEntity.STATUS_ACTIVE, genericSearchPattern);
	}

	public List<TargetUrlEntity> getListForIdUrl(int ownDomainID) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     id,");
		stringBuilder.append("     url");
		stringBuilder.append(" from");
		stringBuilder.append("     t_target_url");
		stringBuilder.append(" where");
		stringBuilder.append("     own_domain_id = ?");
		stringBuilder.append(" and type = ?");
		stringBuilder.append(" and status = ?");
		String sqlString = stringBuilder.toString();
		return this.findBySql(sqlString, ownDomainID, IConstants.TARGET_URL_TYPE_ADDED_BY_USER, IConstants.TARGET_URL_STATUS_ACTIVE);
	}

	public Map<String, Long> getType1(int domainId) {
		Map<String, Long> response = new HashMap<String, Long>();
		StringBuffer stringBuffer = new StringBuffer();
		stringBuffer.append(" select");
		stringBuffer.append("	 t_target_url.id,");
		stringBuffer.append("	 t_target_url.url");
		stringBuffer.append(" from");
		stringBuffer.append("	 t_target_url t_target_url");
		stringBuffer.append(" where");
		stringBuffer.append("	 t_target_url.own_domain_id = ?");
		stringBuffer.append(" and t_target_url.`type` = ?");
		String sqlString = stringBuffer.toString();
		List<TargetUrlEntity> targetUrlEntityList = findBySql(sqlString, domainId, TargetUrlEntity.TYPE_ADD_BY_USER);
		if (targetUrlEntityList != null && targetUrlEntityList.size() > 0) {
			for (TargetUrlEntity targetUrlEntity : targetUrlEntityList) {
				response.put(Md5Util.Md5(StringUtils.trim(targetUrlEntity.getUrl())), new Long(String.valueOf(targetUrlEntity.getId())));
			}
		}
		return response;
	}

	public void updateStatusType(int domainId, long targetUrlId, Integer status, Integer type) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" update `t_target_url`");
		stringBuilder.append(" set");
		stringBuilder.append("     `status` = ?,");
		stringBuilder.append("     `type` = ?");
		stringBuilder.append(" where");
		stringBuilder.append("     `own_domain_id` = ?  ");
		stringBuilder.append(" and id = ?  ");
		String sqlString = stringBuilder.toString();
		executeUpdate(sqlString, status, type, domainId, targetUrlId);
	}

	public List<TargetUrlEntity> getByUrlString(int domainId, int status, String urlString, int type) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     t_target_url.id");
		stringBuilder.append(" from");
		stringBuilder.append("     t_target_url t_target_url");
		stringBuilder.append(" where");
		stringBuilder.append("     t_target_url.own_domain_id = ?");
		stringBuilder.append(" and t_target_url.`status` = ?");
		stringBuilder.append(" and t_target_url.`type` = ?");
		stringBuilder.append(" and t_target_url.url = ?");
		String sqlString = stringBuilder.toString();
		return this.findBySql(sqlString, domainId, status, type, urlString);
	}

	public void updateUrlStatusType(int domainId, long targetUrlId, String urlString, Integer status, Integer type) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" update `t_target_url`");
		stringBuilder.append(" set");
		stringBuilder.append("     url = ?,");
		stringBuilder.append("     `status` = ?,");
		stringBuilder.append("     `type` = ?");
		stringBuilder.append(" where");
		stringBuilder.append("     `own_domain_id` = ?  ");
		stringBuilder.append(" and id = ?  ");
		String sqlString = stringBuilder.toString();
		executeUpdate(sqlString, urlString, status, type, domainId, targetUrlId);
	}

	public List<TargetUrlEntity> getGroupTagUrlList(int domainId, int groupTagId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     t_target_url.*");
		stringBuilder.append(" from");
		stringBuilder.append("     t_group_tag t_group_tag,");
		stringBuilder.append("     t_group_tag_relation t_group_tag_relation,");
		stringBuilder.append("     t_target_url t_target_url");
		stringBuilder.append(" where");
		stringBuilder.append("     t_group_tag.domain_id = ?");
		stringBuilder.append(" and t_group_tag.id = ?");
		stringBuilder.append(" and t_group_tag.id = t_group_tag_relation.group_tag_id");
		stringBuilder.append(" and t_group_tag_relation.resource_type = ?");
		stringBuilder.append(" and t_group_tag.domain_id = t_target_url.own_domain_id");
		stringBuilder.append(" and t_group_tag_relation.resource_id = t_target_url.id");
		String sqlString = stringBuilder.toString();
		return this.findBySql(sqlString, domainId, groupTagId, IConstants.RESOURCE_TYPE_TARGET_URL);
	}

	public List<TargetUrlEntity> getGroupTagIdUrlList(int domainId, int tagType, Integer[] groupTagIdArray) {
		String groupTagIdsString = FormatUtils.getInstance().convertArrayToString(groupTagIdArray);
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     t_group_tag.id as groupTagId,");
		stringBuilder.append("     t_target_url.url");
		stringBuilder.append(" from");
		stringBuilder.append("     t_group_tag t_group_tag,");
		stringBuilder.append("     t_group_tag_relation t_group_tag_relation,");
		stringBuilder.append("     t_target_url t_target_url");
		stringBuilder.append(" where");
		stringBuilder.append("     t_group_tag.domain_id = ?");
		stringBuilder.append(" and t_group_tag.tag_type = ?");
		stringBuilder.append(" and t_group_tag.id in (").append(groupTagIdsString).append(" )");
		stringBuilder.append(" and t_group_tag.id = t_group_tag_relation.group_tag_id");
		stringBuilder.append(" and t_group_tag_relation.resource_type = ?");
		stringBuilder.append(" and t_group_tag_relation.resource_id = t_target_url.id");
		String sqlString = stringBuilder.toString();
		return this.findBySql(sqlString, domainId, tagType, IConstants.RESOURCE_TYPE_TARGET_URL);
	}

	public Set<String> getHashCodeSet(int domainID) {
		Set<String> hashCodeSet = new HashSet<String>();
		String hashCode = null;
		StringBuffer sql = new StringBuffer();
		sql.append(" select url");
		sql.append(" from ");
		sql.append("     t_target_url");
		sql.append(" where");
		sql.append("     own_domain_id = ?");
		sql.append(" and type = ?");
		sql.append(" and status = ? and ifnull(disable_crawl,0) = 0");
		String sqlString = sql.toString();
		List<TargetUrlEntity> targetUrlEntityList = this.findBySql(sqlString, domainID, IConstants.TARGET_URL_TYPE_ADDED_BY_USER, IConstants.TARGET_URL_STATUS_ACTIVE);
		if (targetUrlEntityList != null && targetUrlEntityList.size() > 0) {
			for (TargetUrlEntity targetUrlEntity : targetUrlEntityList) {
				hashCode = Md5Util.Md5(targetUrlEntity.getUrl());
				hashCodeSet.add(hashCode);
			}
		}
		return hashCodeSet;
	}

	public TargetUrlEntity getRobotsTxtUrl(int domainId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select url");
		stringBuilder.append(" from ");
		stringBuilder.append("     t_target_url");
		stringBuilder.append(" where");
		stringBuilder.append("     own_domain_id = ?");
		stringBuilder.append(" and type = ?");
		stringBuilder.append(" and status = ?");
		stringBuilder.append(" and url like '%robots.txt'");
		stringBuilder.append(" limit 1");
		String sqlString = stringBuilder.toString();
		return this.findObject(sqlString, domainId, IConstants.TARGET_URL_TYPE_ADDED_BY_USER, IConstants.TARGET_URL_STATUS_ACTIVE);
	}

	public void batchUpdateUrlDisableCrawl(int ownDomainId, List<String> urlMurmur3HashList) {
		final String batchUpdateSql = "update t_target_url set disable_crawl = 1, disable_by = 3 where own_domain_id = ? and urlMurmur3Hash = ?";
		final List<Object[]> batchData = urlMurmur3HashList.stream().map(urlMurmur3Hash -> new Object[]{ownDomainId, urlMurmur3Hash}).collect(Collectors.toList());
		super.executeBatch(batchUpdateSql, batchData);
	}

	public Set<String> getNewUrlsByDomainIdAndSourceType(int domainId, Date date) {
		StringBuffer sql = new StringBuffer();
        sql.append(" select url");
        sql.append(" from ");
        sql.append("     t_target_url");
        sql.append(" where");
        sql.append("     own_domain_id = ?");
        sql.append(" and type = ?");
        sql.append(" and status = ?");
        sql.append(" and IFNULL(disable_crawl, 0) = ?");
		sql.append(" and source_type = ?");
		sql.append(" and initial_crawl_only = ?");
		sql.append(" and create_date >= ?");
        String sqlString = sql.toString();
		return new HashSet<>(super.queryForStringList(sqlString, domainId, IConstants.TARGET_URL_TYPE_ADDED_BY_USER, IConstants.TARGET_URL_STATUS_ACTIVE,
				IConstants.TARGET_URL_DISABLE_CRAWL_INACTIVE, IConstants.TARGET_URL_SOURCE_TYPE_RSS, IConstants.TARGET_URL_INITIAL_CRAWL_ONLY_YES, date));
	}

	public HashSet<String> getDisableCrawlUrlList(int domainId) {
		final String sql = "select url from t_target_url where own_domain_id = ? and type = ? and status = ? and ifnull(disable_crawl, 0) = ?";
		final List<String> urls = super.queryForStringList(sql, domainId, IConstants.TARGET_URL_TYPE_ADDED_BY_USER, IConstants.TARGET_URL_STATUS_ACTIVE, IConstants.TARGET_URL_DISABLE_CRAWL_ACTIVE);
		return new HashSet<>(urls);
	}

}
