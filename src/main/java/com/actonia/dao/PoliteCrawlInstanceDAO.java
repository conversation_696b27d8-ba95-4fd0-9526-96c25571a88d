package com.actonia.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.google.gson.Gson;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.jdbc.core.simple.ParameterizedRowMapper;

import com.actonia.entity.PoliteCrawlInstance;

public class PoliteCrawlInstanceDAO extends BaseJdbcSupport<PoliteCrawlInstance> {

    private final Logger log = LogManager.getLogger(PoliteCrawlInstanceDAO.class);

    @Override
    public String getTableName() {
        return TABLE_NAME;
    }

    private static final String TABLE_NAME = "polite_crawl_instance";
    private static final String ID_COLUMN = "id";
    private static final String OWN_DOMAIN_ID_COLUMN = "ownDomainId";
    private static final String CRAWL_TYPE_COLUMN = "crawlType";
    private static final String CRAWL_DATE_COLUMN = "crawlDate";
    private static final String SEND_STATUS_COLUMN = "sendStatus";
    private static final String END_SEND_DATE_COLUMN = "endSendDate";
    private static final String MANAGED_URL_COUNT_COLUMN = "managedUrlCount";
    private static final String RANK_URL_COUNT_COLUMN = "rankUrlCount";
    private static final String TOTAL_SEND_COUNT_COLUMN = "totalSendCount";
    private static final String QUEUE_NAME_COLUMN = "queueName";
    private static final String CRAWL_PARAM_COLUMN = "crawlParam";
    private static final String CRAWL_STATUS_COLUMN = "crawlStatus";
    private static final String START_CRAWL_DATE_COLUMN = "startCrawlDate";
    private static final String END_CRAWL_DATE_COLUMN = "endCrawlDate";
    private static final String NO_CHANGE_COUNT_COLUMN = "noChangeCount";
    private static final String SAVE_TO_CH_COUNT_COLUMN = "saveToChCount";
    private static final String CREATE_DATE_COLUMN = "createDate";

    public List<PoliteCrawlInstance> getAll() {
        String sql = "SELECT " +
                ID_COLUMN + ", " +
                OWN_DOMAIN_ID_COLUMN + ", " +
                CRAWL_TYPE_COLUMN + ", " +
                CRAWL_DATE_COLUMN + ", " +
                SEND_STATUS_COLUMN + ", " +
                END_SEND_DATE_COLUMN + ", " +
                MANAGED_URL_COUNT_COLUMN + ", " +
                RANK_URL_COUNT_COLUMN + ", " +
                TOTAL_SEND_COUNT_COLUMN + ", " +
                QUEUE_NAME_COLUMN + ", " +
                CRAWL_PARAM_COLUMN + ", " +
                CRAWL_STATUS_COLUMN + ", " +
                START_CRAWL_DATE_COLUMN + ", " +
                END_CRAWL_DATE_COLUMN + ", " +
                NO_CHANGE_COUNT_COLUMN + ", " +
                SAVE_TO_CH_COUNT_COLUMN + ", " +
                CREATE_DATE_COLUMN + " " +
                "FROM " + TABLE_NAME;
        return getSimpleJdbcTemplate().query(sql, new PoliteCrawlInstanceRowMapper());
    }

    public PoliteCrawlInstance insert(PoliteCrawlInstance politeCrawlInstance) {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO ").append(TABLE_NAME).append(" (")
                .append(OWN_DOMAIN_ID_COLUMN).append(", ")
                .append(CRAWL_TYPE_COLUMN).append(", ")
                .append(CRAWL_DATE_COLUMN).append(", ")
                .append(SEND_STATUS_COLUMN).append(", ")
                .append(END_SEND_DATE_COLUMN).append(", ")
                .append(MANAGED_URL_COUNT_COLUMN).append(", ")
                .append(RANK_URL_COUNT_COLUMN).append(", ")
                .append(TOTAL_SEND_COUNT_COLUMN).append(", ")
                .append(QUEUE_NAME_COLUMN).append(", ")
                .append(CRAWL_PARAM_COLUMN).append(", ")
                .append(CRAWL_STATUS_COLUMN).append(", ")
                .append(START_CRAWL_DATE_COLUMN).append(", ")
                .append(END_CRAWL_DATE_COLUMN).append(", ")
                .append(NO_CHANGE_COUNT_COLUMN).append(", ")
                .append(SAVE_TO_CH_COUNT_COLUMN).append(", ")
                .append(CREATE_DATE_COLUMN).append(") ")
                .append("VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        Map<String, Object> params = new HashMap<>();
        // put all column in politeCrawlInstance into this map
        params.put(OWN_DOMAIN_ID_COLUMN, politeCrawlInstance.getOwnDomainId());
        params.put(CRAWL_TYPE_COLUMN, politeCrawlInstance.getCrawlType().getValue());
        params.put(CRAWL_DATE_COLUMN, politeCrawlInstance.getCrawlDate());
        params.put(SEND_STATUS_COLUMN, politeCrawlInstance.getSendStatus().getValue());
        params.put(END_SEND_DATE_COLUMN, politeCrawlInstance.getEndSendDate());
        params.put(MANAGED_URL_COUNT_COLUMN, politeCrawlInstance.getManagedUrlCount());
        params.put(RANK_URL_COUNT_COLUMN, politeCrawlInstance.getRankUrlCount());
        params.put(TOTAL_SEND_COUNT_COLUMN, politeCrawlInstance.getTotalSendCount());
        params.put(QUEUE_NAME_COLUMN, politeCrawlInstance.getQueueName());
        params.put(CRAWL_PARAM_COLUMN, politeCrawlInstance.getCrawlParam());
        params.put(CRAWL_STATUS_COLUMN, politeCrawlInstance.getCrawlStatus() == null ? null : politeCrawlInstance.getCrawlStatus().getValue());
        params.put(NO_CHANGE_COUNT_COLUMN, politeCrawlInstance.getNoChangeCount());
        params.put(SAVE_TO_CH_COUNT_COLUMN, politeCrawlInstance.getSaveToChCount());
        params.put(CREATE_DATE_COLUMN, politeCrawlInstance.getCreateDate());
        final int insert = super.insert(params);
        politeCrawlInstance.setId(insert);
        return politeCrawlInstance;
    }

    public PoliteCrawlInstance getById(int id) {
        String sb = "SELECT * FROM " + TABLE_NAME + " " +
                "WHERE " + ID_COLUMN + " = ?";
        if (log.isDebugEnabled()) {
            log.debug("id: {}", id);
        }
        return getSimpleJdbcTemplate().queryForObject(sb, new PoliteCrawlInstanceRowMapper(), id);
    }

    public List<PoliteCrawlInstance> query(PoliteCrawlInstance politeCrawlInstance) {
        StringBuilder sql = new StringBuilder("SELECT * FROM ").append(TABLE_NAME).append(" WHERE ");
        log.info("instance to query: {}", politeCrawlInstance);
        List<Object> params = new ArrayList<>();

        if (politeCrawlInstance.getId() != null) {
            sql.append(ID_COLUMN + " = ? AND ");
            params.add(politeCrawlInstance.getId());
        }
        if (politeCrawlInstance.getOwnDomainId() != null) {
            sql.append(OWN_DOMAIN_ID_COLUMN + " = ? AND ");
            params.add(politeCrawlInstance.getOwnDomainId());
        }
        if (politeCrawlInstance.getCrawlType() != null) {
            sql.append(CRAWL_TYPE_COLUMN + " = ? AND ");
            params.add(politeCrawlInstance.getCrawlType().getValue());
        }
        if (politeCrawlInstance.getCrawlDate() != null) {
            sql.append(CRAWL_DATE_COLUMN + " = ? AND ");
            params.add(politeCrawlInstance.getCrawlDate());
        }
        if (politeCrawlInstance.getSendStatus() != null) {
            sql.append(SEND_STATUS_COLUMN + " = ? AND ");
            params.add(politeCrawlInstance.getSendStatus().getValue());
        }
        if (politeCrawlInstance.getEndSendDate() != null) {
            sql.append(END_SEND_DATE_COLUMN + " = ? AND ");
            params.add(politeCrawlInstance.getEndSendDate());
        }
        if (politeCrawlInstance.getManagedUrlCount() != null) {
            sql.append(MANAGED_URL_COUNT_COLUMN + " = ? AND ");
            params.add(politeCrawlInstance.getManagedUrlCount());
        }
        if (politeCrawlInstance.getRankUrlCount() != null) {
            sql.append(RANK_URL_COUNT_COLUMN + " = ? AND ");
            params.add(politeCrawlInstance.getRankUrlCount());
        }
        if (politeCrawlInstance.getTotalSendCount() != null) {
            sql.append(TOTAL_SEND_COUNT_COLUMN + " = ? AND ");
            params.add(politeCrawlInstance.getTotalSendCount());
        }
        if (politeCrawlInstance.getQueueName() != null) {
            sql.append(QUEUE_NAME_COLUMN + " = ? AND ");
            params.add(politeCrawlInstance.getQueueName());
        }
        if (politeCrawlInstance.getCrawlParam() != null) {
            sql.append(CRAWL_PARAM_COLUMN + " = ? AND ");
            params.add(politeCrawlInstance.getCrawlParam());
        }
        if (politeCrawlInstance.getCrawlStatus() != null) {
            sql.append(CRAWL_STATUS_COLUMN + " = ? AND ");
            params.add(politeCrawlInstance.getCrawlStatus().getValue());
        }
        if (politeCrawlInstance.getStartCrawlDate() != null) {
            sql.append(START_CRAWL_DATE_COLUMN + " = ? AND ");
            params.add(politeCrawlInstance.getStartCrawlDate());
        }
        if (politeCrawlInstance.getEndCrawlDate() != null) {
            sql.append(END_CRAWL_DATE_COLUMN + " = ? AND ");
            params.add(politeCrawlInstance.getEndCrawlDate());
        }
        if (politeCrawlInstance.getNoChangeCount() != null) {
            sql.append(NO_CHANGE_COUNT_COLUMN + " = ? AND ");
            params.add(politeCrawlInstance.getNoChangeCount());
        }
        if (politeCrawlInstance.getSaveToChCount() != null) {
            sql.append(SAVE_TO_CH_COUNT_COLUMN + " = ? AND ");
            params.add(politeCrawlInstance.getSaveToChCount());
        }
        if (politeCrawlInstance.getCreateDate() != null) {
            sql.append(CREATE_DATE_COLUMN + " = ? AND ");
            params.add(politeCrawlInstance.getCreateDate());
        }

        if (params.isEmpty()) {
            // No parameters were added to the WHERE clause
            sql.delete(sql.length() - 7, sql.length());  // remove " WHERE " from the SQL statement
        } else {
            sql.delete(sql.length() - 5, sql.length());
        }

        // print sql if log level is debug
        if (log.isDebugEnabled()) {
            log.debug("politeCrawlInstance: {} \n params: {}\n sql: {}", politeCrawlInstance, new Gson().toJson(params.toArray()), sql);
        }

        return getSimpleJdbcTemplate().query(sql.toString(), new PoliteCrawlInstanceRowMapper(), params.toArray());

    }


    public int update(PoliteCrawlInstance instance) {
        String sql = "UPDATE " + TABLE_NAME + " SET " +
                OWN_DOMAIN_ID_COLUMN + " = ?, " +
                CRAWL_TYPE_COLUMN + " = ?, " +
                CRAWL_DATE_COLUMN + " = ?, " +
                SEND_STATUS_COLUMN + " = ?, " +
                END_SEND_DATE_COLUMN + " = ?, " +
                MANAGED_URL_COUNT_COLUMN + " = ?, " +
                RANK_URL_COUNT_COLUMN + " = ?, " +
                TOTAL_SEND_COUNT_COLUMN + " = ?, " +
                QUEUE_NAME_COLUMN + " = ?, " +
                CRAWL_PARAM_COLUMN + " = ?, " +
                CRAWL_STATUS_COLUMN + " = ?, " +
                START_CRAWL_DATE_COLUMN + " = ?, " +
                END_CRAWL_DATE_COLUMN + " = ?, " +
                NO_CHANGE_COUNT_COLUMN + " = ?, " +
                SAVE_TO_CH_COUNT_COLUMN + " = ? " +
                "WHERE " +
                ID_COLUMN + " = ?";

        Object[] params = {
                instance.getOwnDomainId(),
                instance.getCrawlType() == null ? null : instance.getCrawlType().getValue(),
                instance.getCrawlDate(),
                instance.getSendStatus() == null ? null : instance.getSendStatus().getValue(),
                instance.getEndSendDate(),
                instance.getManagedUrlCount(),
                instance.getRankUrlCount(),
                instance.getTotalSendCount(),
                instance.getQueueName(),
                instance.getCrawlParam(),
                instance.getCrawlStatus() == null ? null : instance.getCrawlStatus().getValue(),
                instance.getStartCrawlDate(),
                instance.getEndCrawlDate(),
                instance.getNoChangeCount(),
                instance.getSaveToChCount(),
                instance.getId()
        };

        return getSimpleJdbcTemplate().update(sql, params);
    }

    public void updateRankUrlCountById(int id, int rankUrlCount) {
        String sql = "UPDATE " + TABLE_NAME + " SET " +
                RANK_URL_COUNT_COLUMN + " = ? " +
                " WHERE " +
                ID_COLUMN + " = ?";
        super.executeUpdate(sql, rankUrlCount, id);

    }

    /**
     * change the status sent.
     * save endSendDate and update sendStatus to send completed or send error
     *
     * @param instance       PoliteCrawlInstance
     */
    public void updateStatusToSent(PoliteCrawlInstance instance) {
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE ").append(TABLE_NAME).append(" SET ")
                .append(SEND_STATUS_COLUMN).append(" = ?, ")
                .append(END_SEND_DATE_COLUMN).append(" = ? ");
        List<Object> params = new ArrayList<>();
        params.add(instance.getSendStatus().getValue());
        params.add(instance.getEndSendDate());
        if (instance.getManagedUrlCount() != null) {
            sql.append(", ").append(MANAGED_URL_COUNT_COLUMN).append(" = ? ");
            params.add(instance.getManagedUrlCount());
        }
        if (instance.getRankUrlCount() != null) {
            sql.append(", ").append(RANK_URL_COUNT_COLUMN).append(" = ? ");
            params.add(instance.getRankUrlCount());
        }
        if (instance.getTotalSendCount() != null) {
            sql.append(", ").append(TOTAL_SEND_COUNT_COLUMN).append(" = ? ");
            params.add(instance.getTotalSendCount());
        }
        if (instance.getQueueName() != null) {
            sql.append(", ").append(QUEUE_NAME_COLUMN).append(" = ? ");
            params.add(instance.getQueueName());
        }
        sql.append("WHERE ").append(ID_COLUMN).append(" = ?");
        params.add(instance.getId());
        if (log.isDebugEnabled()) {
            log.debug("sql: {}, \n params: {}", sql, params.toArray());
        }
        final int i = super.executeUpdate(sql.toString(), params.toArray());
        if (i == 0) {
            throw new RuntimeException("Update failed sql: " + sql);
        }
    }

    /**
     * update sendStatus to NO_MANAGED_URL by id
     * @param id the id of the instance
     */
    public void updateSendStatusToNoManagedUrl(Integer id) {
        String sql = "UPDATE " + TABLE_NAME + " SET " +
                SEND_STATUS_COLUMN + " = " + PoliteCrawlInstance.SendStatusEnum.NO_MANAGED_URL.getValue() +
                " , " + END_SEND_DATE_COLUMN + " = ? " +
                " WHERE " +
                ID_COLUMN + " = ?";
        super.executeUpdate(sql, PoliteCrawlInstance.SendStatusEnum.NO_MANAGED_URL.getValue(), LocalDateTime.now(), id);

    }


    /**
     * update crawlStatus to completed by id
     * @param instance PoliteCrawlInstance
     */
    public void updateCrawlStatusToCompleted(PoliteCrawlInstance instance) {
        instance.setCrawlStatus(PoliteCrawlInstance.CrawlStatusEnum.CRAWL_COMPLETED);
        if (instance.getEndCrawlDate() == null) {
            instance.setEndCrawlDate(LocalDateTime.now());
        }
        this.updateCrawlStatus(instance);
    }

    /**
     * update crawlStatus by id
     * @param instance PoliteCrawlInstance for update crawlStatus
     */
    public void updateCrawlStatus(PoliteCrawlInstance instance) {
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE ").append(TABLE_NAME).append(" SET ")
                .append(CRAWL_STATUS_COLUMN).append(" = ? ")
                .append(" , ")
                .append(END_CRAWL_DATE_COLUMN).append(" = ? ");
        List<Object> params = new ArrayList<>();
        params.add(instance.getCrawlStatus().getValue());
        params.add(instance.getEndCrawlDate());
        sql.append("WHERE ").append(ID_COLUMN).append(" = ?");
        params.add(instance.getId());
        if (log.isDebugEnabled()) {
            log.debug("sql: {}, \n params: {}", sql, params.toArray());
        }
        final int i = super.executeUpdate(sql.toString(), params.toArray());
        if (i == 0) {
            throw new RuntimeException("Update failed sql: " + sql);
        }
    }

    /**
     * find by ownDomainId and crawlType and crawlDate
     * @param ownDomainId ownDomainId
     * @param crawlType crawlType
     * @param crawlDate crawlDate
     * @return PoliteCrawlInstance or null
     */
    public Optional<PoliteCrawlInstance> findByOwnDomainIdAndCrawlTypeAndCrawlDate(int ownDomainId, PoliteCrawlInstance.CrawlTypeEnum crawlType, int crawlDate) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("SELECT * FROM ")
                .append(TABLE_NAME)
                .append(" WHERE ")
                .append(OWN_DOMAIN_ID_COLUMN).append(" = ? AND ")
                .append(CRAWL_TYPE_COLUMN).append(" = ? AND ")
                .append(CRAWL_DATE_COLUMN).append(" = ?");
        Object[] params = new Object[]{ownDomainId, crawlType.getValue(), crawlDate};
        if (log.isDebugEnabled()) {
            log.debug("sql: {}, \n params: {}", stringBuilder.toString(), params);
        }
        String sql = stringBuilder.toString();
        final PoliteCrawlInstance politeCrawlInstance = getSimpleJdbcTemplate().queryForObject(sql, new PoliteCrawlInstanceRowMapper(), params);
        return Optional.ofNullable(politeCrawlInstance);
    }

    public List<PoliteCrawlInstance> findLatestCrawlDateInstance(int crawlDate) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT t1.* FROM ")
                .append(TABLE_NAME).append(" t1 JOIN ( ")
                .append("SELECT ").append(OWN_DOMAIN_ID_COLUMN).append(", MAX(")
                .append(CRAWL_DATE_COLUMN).append(") AS max_crawl_date ")
                .append("FROM ").append(TABLE_NAME)
                .append(" WHERE ").append(CRAWL_DATE_COLUMN).append(" >= ? ")
                .append("GROUP BY ").append(OWN_DOMAIN_ID_COLUMN)
                .append(") t2 ON t1.").append(OWN_DOMAIN_ID_COLUMN)
                .append(" = t2.").append(OWN_DOMAIN_ID_COLUMN)
                .append(" AND t1.").append(CRAWL_DATE_COLUMN)
                .append(" = t2.max_crawl_date");
        if (log.isDebugEnabled()) {
            log.debug("sql: {}, \n params: {}", sql, crawlDate);
        }
        return super.getSimpleJdbcTemplate().query(sql.toString(), new PoliteCrawlInstanceRowMapper(), crawlDate);
    }

    public List<PoliteCrawlInstance> findCrawlStatusNotCompleted() {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM ")
                .append(TABLE_NAME)
                .append(" WHERE ")
                .append(CRAWL_STATUS_COLUMN)
                .append(" < ")
                .append(PoliteCrawlInstance.CrawlStatusEnum.CRAWL_COMPLETED.getValue());
        return super.getSimpleJdbcTemplate().query(sql.toString(), new PoliteCrawlInstanceRowMapper());
    }

    public void updateCrawlParam(PoliteCrawlInstance instance) {
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE ").append(TABLE_NAME).append(" SET ")
                .append(CRAWL_PARAM_COLUMN).append(" = ? ")
                .append(" WHERE ").append(ID_COLUMN).append(" = ?");
        List<Object> params = new ArrayList<>();
        params.add(instance.getCrawlParam());
        params.add(instance.getId());
        if (log.isDebugEnabled()) {
            log.debug("sql: {}, \n params: {}", sql, params.toArray());
        }
        super.executeUpdate(sql.toString(), params.toArray());
    }

    /**
     * Maps a row of the result set to a PoliteCrawlInstance object.
     */
    private final static class PoliteCrawlInstanceRowMapper implements ParameterizedRowMapper<PoliteCrawlInstance> {

        @Override
        public PoliteCrawlInstance mapRow(ResultSet rs, int rowNum) throws SQLException {
            PoliteCrawlInstance instance = new PoliteCrawlInstance();
            instance.setId(rs.getInt(ID_COLUMN));
            instance.setOwnDomainId(rs.getInt(OWN_DOMAIN_ID_COLUMN));
            instance.setCrawlType(PoliteCrawlInstance.CrawlTypeEnum.values()[rs.getByte(CRAWL_TYPE_COLUMN) - 1]);
            instance.setCrawlDate(rs.getInt(CRAWL_DATE_COLUMN));
            instance.setSendStatus(PoliteCrawlInstance.SendStatusEnum.values()[rs.getByte(SEND_STATUS_COLUMN) - 1]);
            instance.setEndSendDate(rs.getTimestamp(END_SEND_DATE_COLUMN) == null ? null : rs.getTimestamp(END_SEND_DATE_COLUMN).toLocalDateTime());
            instance.setManagedUrlCount(rs.getInt(MANAGED_URL_COUNT_COLUMN));
            instance.setRankUrlCount(rs.getInt(RANK_URL_COUNT_COLUMN));
            instance.setTotalSendCount(rs.getInt(TOTAL_SEND_COUNT_COLUMN));
            instance.setQueueName(rs.getString(QUEUE_NAME_COLUMN));
            instance.setCrawlParam(rs.getString(CRAWL_PARAM_COLUMN));
            instance.setCrawlStatus(PoliteCrawlInstance.CrawlStatusEnum.values()[rs.getByte(CRAWL_STATUS_COLUMN)]);
            instance.setStartCrawlDate(rs.getTimestamp(START_CRAWL_DATE_COLUMN) == null ? null : rs.getTimestamp(START_CRAWL_DATE_COLUMN).toLocalDateTime());
            instance.setEndCrawlDate(rs.getTimestamp(END_CRAWL_DATE_COLUMN) == null ? null : rs.getTimestamp(END_CRAWL_DATE_COLUMN).toLocalDateTime());
            instance.setNoChangeCount(rs.getInt(NO_CHANGE_COUNT_COLUMN));
            instance.setSaveToChCount(rs.getInt(SAVE_TO_CH_COUNT_COLUMN));
            instance.setCreateDate(rs.getTimestamp(CREATE_DATE_COLUMN) == null ? null : rs.getTimestamp(CREATE_DATE_COLUMN).toLocalDateTime());
            return instance;
        }
    }

}
