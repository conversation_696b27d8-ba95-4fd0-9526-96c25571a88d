package com.actonia.dao;

import java.util.List;

import com.actonia.entity.OwnDomainEntity;
import com.actonia.entity.PageTagAlertConfigEntity;

public class PageTagAlertConfigDAO extends BaseJdbcSupport<PageTagAlertConfigEntity> {

	@Override
	public String getTableName() {
		return "page_tag_alert_config";
	}

	public List<PageTagAlertConfigEntity> get(int domainId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("select ");
		stringBuilder.append("    * ");
		stringBuilder.append("from ");
		stringBuilder.append(getTableName() + " ");
		stringBuilder.append("where ");
		stringBuilder.append("    domain_id = ? ");
		String sqlString = stringBuilder.toString();
		return findBySql(sqlString, domainId);
	}

	public List<PageTagAlertConfigEntity> getList() {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("select distinct ");
		stringBuilder.append("    page_tag_alert_config.* ");
		stringBuilder.append("from ");
		stringBuilder.append("    t_own_domain t_own_domain, ");
		stringBuilder.append("    page_tag_alert_config page_tag_alert_config ");
		stringBuilder.append("where ");
		stringBuilder.append("    t_own_domain.`status` = ? ");
		stringBuilder.append("and t_own_domain.id = page_tag_alert_config.domain_id ");
		stringBuilder.append("order by ");
		stringBuilder.append("    id ");
		String sqlString = stringBuilder.toString();
		return findBySql(sqlString, OwnDomainEntity.STATE_ACTIVE);
	}

	public int updateTagContentChangeTagIds(int id, String tagContentChangeTagIds) {
		String sqlString = "update " + getTableName() + " set tag_content_change_tag_ids = ? where id = ?";
		return this.executeUpdate(sqlString, tagContentChangeTagIds, id);
	}
}