/**
 * 
 */
package com.actonia.dao;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.actonia.IConstants;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.entity.TargetUrlEntity;
import com.actonia.entity.UrlMetricsDataEntity;
import com.actonia.value.object.UrlMetricsUpdateValueObject;

/**
 * com.actonia.subserver.dao.UrlMetricsDataEntityDAO.java
 * 
 * @version $Revision: 140259 $ $Author: limt $
 */
public class UrlMetricsDataEntityDAO extends BaseJdbcSupport<UrlMetricsDataEntity> {

	public static final int RECORDS_PER_SQL_STATEMENT = 10;
	public static final int SQL_STATEMENTS_PER_JDBC_CALL = 10;
	public static final int NUMBER_OF_FIELDS = 33;
	public static String SQL_CREATION = null;
	public static final String SQL_CREATION_FORMAT_1 =  "(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
	public static final String SQL_CREATION_FORMAT_2 = ",(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

	static
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("insert into t_url_metrics_data ");
		stringBuilder.append("(");
		stringBuilder.append("url, ");
		stringBuilder.append("url_id, ");
		stringBuilder.append("type, ");
		stringBuilder.append("pagerank, ");
		stringBuilder.append("inbounds, ");
		stringBuilder.append("internal_links, ");
		stringBuilder.append("outbounds, ");
		stringBuilder.append("cached_date, ");
		stringBuilder.append("resp_code, ");
		stringBuilder.append("ipaddress, ");
		stringBuilder.append("last_update, ");
		stringBuilder.append("pr_update_date, ");
		stringBuilder.append("yahoo_update_date, ");
		stringBuilder.append("cachedate_update_date, ");
		stringBuilder.append("respcode_update_date, ");
		stringBuilder.append("semrush_update_date, ");
		stringBuilder.append("pagespeed_update_date, ");
		stringBuilder.append("google_index, ");
		stringBuilder.append("seomoz_update_date, ");
		stringBuilder.append("moz_rank, ");
		stringBuilder.append("page_authority, ");
		stringBuilder.append("moz_inbounds, ");
		stringBuilder.append("robot_meta, ");
		stringBuilder.append("links, ");
		stringBuilder.append("domain_authority, ");
		stringBuilder.append("majestics_update_date, ");
		stringBuilder.append("ac_rank, ");
		stringBuilder.append("percent_mobile_entrances, ");
		stringBuilder.append("associated_keywords, ");
		stringBuilder.append("associated_keywords_ranked, ");
		stringBuilder.append("links_acquired_organically, ");
		stringBuilder.append("links_acquired_manually, ");
		stringBuilder.append("associated_competitors ");
		stringBuilder.append(")");
		stringBuilder.append("values ");
		SQL_CREATION = stringBuilder.toString();
	}

	@Override
	public String getTableName() {
		return "t_url_metrics_data";
	}

	public UrlMetricsDataEntity getUrlMetricsById(int id) {
		String sql = "select * from t_url_metrics_data where id = ?";
		return findObject(sql, id);
	}

	public void updatePageRank(int id, int pagerank) {
		StringBuffer sql = new StringBuffer();
		sql.append(" update t_url_metrics_data set pagerank = ? ");
		sql.append(" where id = ? ");

		executeUpdate(sql.toString(), pagerank, id);
	}
	
	public void deleteByUrlId(int ownDomainId, long urlId, int urlType) {
		StringBuffer sql = new StringBuffer();
		sql.append(" delete from t_url_metrics_data ");
		sql.append(" where url_id = ? ");
		sql.append(" and type = ? ");

		executeUpdate(sql.toString(), urlId, urlId);
	}

	public void updateUrl(int id, String url) {
		StringBuffer sql = new StringBuffer();
		sql.append(" update t_url_metrics_data set url = ? ");
		sql.append(" where id = ? ");

		executeUpdate(sql.toString(), url, id);
	}

	public void updateCachedDate(int id, Date cachedDate) {
		StringBuffer sql = new StringBuffer();
		sql.append(" update t_url_metrics_data set cached_date = ? ");
		sql.append(" where id = ? ");

		executeUpdate(sql.toString(), cachedDate, id);
	}

	public UrlMetricsDataEntity getUrlMetricsDataEntityByUrlId(long urlId, int type) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from t_url_metrics_data ");
		sql.append(" where url_id = ? and type = ? ");

		return findObject(sql.toString(), urlId, type);
	}

	public List<UrlMetricsDataEntity> getUrlMetricsDataEntities(String url) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from t_url_metrics_data ");
		sql.append(" where url = ? ");

		return findBySql(sql.toString(), url);
	}

	public List<UrlMetricsDataEntity> getNoCachedDateUrl(int size) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from t_url_metrics_data ");
		sql.append(" where cached_date is null ");
		// sql.append(" or cached_date > '3000-01-01' ");
		sql.append(" order by id desc ");
		sql.append(" limit ").append(size);

		return findBySql(sql.toString());
	}

	public int insert(UrlMetricsDataEntity entity) {
		Map<String, Object> values = new HashMap<String, Object>();
		values.put("url", entity.getUrl());
		values.put("url_id", entity.getUrlId());
		values.put("type", entity.getType());
		values.put("last_update", entity.getLastUpdate());
		values.put("pagerank", entity.getPagerank());
		values.put("cached_date", entity.getCachedDate());
		values.put("inbounds", entity.getInbounds());
		values.put("pr_update_date", entity.getPrUpdateDate());
		values.put("yahoo_update_date", entity.getYahooUpdateDate());
		values.put("cachedate_update_date", entity.getCacheDateUpdateDate());
		values.put("resp_code", entity.getRespCode());
		values.put("respcode_update_date", entity.getHtmlContentUpdateDate());
		values.put("outbounds", entity.getOutbounds());
		values.put("semrush_update_date", entity.getSemrushUpdateDate());
		values.put("google_index", entity.getGoogleIndex() == null ? 1 : entity.getGoogleIndex());
		values.put("seomoz_update_date", entity.getSeomozUpdateDate());
		values.put("moz_rank", entity.getMozRank());
		values.put("page_authority", entity.getPageAuthority());
		values.put("moz_inbounds", entity.getMozInbounds());
		values.put("robot_meta", entity.getRobotMeta());

		values.put("links", entity.getLinks());
		values.put("domain_authority", entity.getDomainAuthority());
		values.put("internal_links", entity.getInternalLinks());
		
		values.put("majestics_update_date", entity.getMajesticsUpdateDate());
		values.put("ac_rank", entity.getAcRank());
		return this.insert(values);
	}

	public void updateYahooInbounds(Integer id, int yahooInboud) {
		StringBuffer sql = new StringBuffer();
		sql.append(" update t_url_metrics_data set inbounds = ? ");
		sql.append(" where id = ? ");

		executeUpdate(sql.toString(), yahooInboud, id);
	}

	public void updateResponsecode(int responseCode, long urlId, int type) {
		StringBuffer sql = new StringBuffer();
		sql.append(" update t_url_metrics_data set resp_code = ? ");
		sql.append(" where url_id=? and type=? ");

		executeUpdate(sql.toString(), responseCode, urlId, type);
	}

	public void updatePageRankUpdateTime(int id, Date pagerankDate) {
		StringBuffer sql = new StringBuffer();
		sql.append(" update t_url_metrics_data set pr_update_date = ? ");
		sql.append(" where id = ? ");

		executeUpdate(sql.toString(), pagerankDate, id);
	}

	public void updateYahooInlinkUpdateTime(int id, Date yahooDate) {
		StringBuffer sql = new StringBuffer();
		sql.append(" update t_url_metrics_data set yahoo_update_date = ? ");
		sql.append(" where id = ? ");

		executeUpdate(sql.toString(), yahooDate, id);
	}

	public void updateCacheDateUpdateTime(int id, Date yahooDate) {
		StringBuffer sql = new StringBuffer();
		sql.append(" update t_url_metrics_data set cachedate_update_date = ? ");
		sql.append(" where id = ? ");

		executeUpdate(sql.toString(), yahooDate, id);
	}

	public void updateHtmlContentUpdateTimeByUrlId(long id, Date htmlContentDate, int type) {
		StringBuffer sql = new StringBuffer();
		sql.append(" update t_url_metrics_data set respcode_update_date = ? ");
		sql.append(" where url_id = ? and type =? ");

		executeUpdate(sql.toString(), htmlContentDate, id, type);
	}

	public void updateOutBounds(long id, int outBounds, int type) {
		StringBuffer sql = new StringBuffer();
		sql.append(" update t_url_metrics_data set outbounds = ? ");
		sql.append(" where url_id = ? and type =? ");

		executeUpdate(sql.toString(), outBounds, id, type);
	}

	public void updateSemRushCachedDate(long id, int type, Date cachedDate) {
		StringBuffer sql = new StringBuffer();
		sql.append(" update t_url_metrics_data set semrush_update_date = ? ");
		sql.append(" where url_id = ? and type =?  ");

		executeUpdate(sql.toString(), cachedDate, id, type);
	}

	public void updateGoogleIndex(int id, int indexed) {
		StringBuffer sql = new StringBuffer();
		sql.append(" update t_url_metrics_data set google_index = ? ");
		sql.append(" where id = ? ");

		executeUpdate(sql.toString(), indexed, id);
	}

	public void updatSeoMoz(int id, String mozRank, String pageAutrioy) {
		StringBuffer sql = new StringBuffer();
		sql.append(" update t_url_metrics_data set moz_rank = ? , page_authority=? ");
		sql.append(" where id = ? ");

		executeUpdate(sql.toString(), mozRank, pageAutrioy, id);
	}

	public void updateSeoMozUpdateDate(int id, int type, Date seoMozDate, int mozInbounds) {
		StringBuffer sql = new StringBuffer();
		sql.append(" update t_url_metrics_data set seomoz_update_date = ? ,moz_inbounds=? ");
		sql.append(" where id = ? and type =?  ");

		executeUpdate(sql.toString(), seoMozDate, mozInbounds, id, type);
	}
	
	// by Meo
	public void updateSeoMozUpdateDate(int id, int type, Date seoMozDate) {
		StringBuffer sql = new StringBuffer();
		sql.append(" update t_url_metrics_data set seomoz_update_date = ? ");
		sql.append(" where id = ? and type =?  ");

		executeUpdate(sql.toString(), seoMozDate, id, type);
	}

	public void updateRobotMeta(int id, int type) {
		StringBuffer sql = new StringBuffer();
		sql.append(" update t_url_metrics_data set robot_meta = ? ");
		sql.append(" where id = ?");

		executeUpdate(sql.toString(), type, id);
	}

	public void updateLinks(int id, int links, String domainAuthority) {
		StringBuffer sql = new StringBuffer();
		sql.append(" update t_url_metrics_data set links=?, domain_authority=? ");
		sql.append(" where id= ?");

		executeUpdate(sql.toString(), links, domainAuthority, id);
	}

	public void updatePageSpeedDate(Date pageSpeedDate, long urlId, int type) {
		StringBuffer sql = new StringBuffer();
		sql.append(" update t_url_metrics_data set pagespeed_update_date = ? ");
		sql.append(" where url_id=? and type=? ");

		executeUpdate(sql.toString(), pageSpeedDate, urlId, type);
	}

	public void updateInternalLinks(long id, int internalLinks, int type) {
		StringBuffer sql = new StringBuffer();
		sql.append(" update t_url_metrics_data set internal_links = ? ");
		sql.append(" where url_id = ? and type =? ");

		executeUpdate(sql.toString(), internalLinks, id, type);
	}

	public void updateMajestic(int id, int acRank, Date updateData) {
		StringBuffer sql = new StringBuffer();
		sql.append(" update t_url_metrics_data set majestics_update_date=? ,");
		sql.append(" ac_rank=? where id = ? ");

		executeUpdate(sql.toString(), updateData, acRank, id);
	}
	
	public void updateUrlMetricsData(int responseCode, Date updateTime, int outBounds, int internalLinks, long urlId, int type) {
        StringBuffer sql = new StringBuffer();
		sql.append(" update t_url_metrics_data set resp_code = ?, respcode_update_date = ?, outbounds = ?, internal_links = ? ");
		sql.append(" where url_id=? and type=? ");
	
		executeUpdate(sql.toString(), responseCode, updateTime, outBounds, internalLinks, urlId, type);
	}

	public List<UrlMetricsDataEntity> getZeroInbounds() {
		StringBuffer sql = new StringBuffer();
		sql.append("select * from t_url_metrics_data where inbounds = 0 or inbounds is null ");
		return findBySql(sql.toString());
	}
	
	public void updateInBounds(int id, BigInteger inBounds) {
		String sql = "update t_url_metrics_data set inbounds = ? where id = ? ";
		executeUpdate(sql, inBounds, id);
	}

	public void updateWithPoliteCrawlData(UrlMetricsUpdateValueObject urlMetricsUpdateValueObject) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("update t_url_metrics_data ");
		stringBuilder.append("set ");
		stringBuilder.append(" resp_code = ?, ");
		stringBuilder.append(" respcode_update_date = ?, ");
		stringBuilder.append(" internal_links = ?, ");
		stringBuilder.append(" outbounds = ?, ");
		stringBuilder.append("where ");
		stringBuilder.append(" id = ? ");
		String sqlString = stringBuilder.toString();
		executeUpdate(sqlString, 
				urlMetricsUpdateValueObject.getPoliteCrawlResponseCode(),
				urlMetricsUpdateValueObject.getRespcodeUpdateDate(),
				urlMetricsUpdateValueObject.getPoliteCrawlInternalLinks(),
				urlMetricsUpdateValueObject.getPoliteCrawlOutboundLinks(),
				urlMetricsUpdateValueObject.getUrlMetricsId());
	}

	public void insertMultiRowsBatch(List<UrlMetricsDataEntity> urlMetricsDataEntityList) {

		Object[] objectArray = null;
		List<Object[]> objectArrayList = new ArrayList<Object[]>();
		List<UrlMetricsDataEntity> tempList = new ArrayList<UrlMetricsDataEntity>();
		for (UrlMetricsDataEntity urlMetricsDataEntity : urlMetricsDataEntityList) {
			tempList.add(urlMetricsDataEntity);
			if (tempList.size() == RECORDS_PER_SQL_STATEMENT) {
				objectArray = getObjectArrayForInsert(tempList);
				objectArrayList.add(objectArray);
				if (objectArrayList.size() == SQL_STATEMENTS_PER_JDBC_CALL) {
					this.executeBatch(getInsertMultiRowsSqlStatement(RECORDS_PER_SQL_STATEMENT), objectArrayList);
					objectArrayList = new ArrayList<Object[]>();
				}
				tempList = new ArrayList<UrlMetricsDataEntity>();
			}
		}

		if (objectArrayList != null && objectArrayList.size() > 0) {
			this.executeBatch(getInsertMultiRowsSqlStatement(RECORDS_PER_SQL_STATEMENT), objectArrayList);
			objectArrayList = new ArrayList<Object[]>();
		}

		if (tempList != null && tempList.size() > 0) {
			objectArray = getObjectArrayForInsert(tempList);
			objectArrayList.add(objectArray);
			if (objectArrayList != null && objectArrayList.size() > 0) {
				this.executeBatch(getInsertMultiRowsSqlStatement(tempList.size()), objectArrayList);
				objectArrayList = new ArrayList<Object[]>();
			}
		}
	}

	private Object[] getObjectArrayForInsert(List<UrlMetricsDataEntity> list) {
		List<Object[]> tempObjectArrayList = new ArrayList<Object[]>();
		Object[] urlMetricsDataObjectArray = null;
		int totalNumberOfObjects = 0;
		for (UrlMetricsDataEntity urlMetricsDataEntity : list) {
			urlMetricsDataObjectArray = new Object[] {
					urlMetricsDataEntity.getUrl(),
					urlMetricsDataEntity.getUrlId(),
					urlMetricsDataEntity.getType(),
					urlMetricsDataEntity.getPagerank(),
					urlMetricsDataEntity.getInbounds(),
					urlMetricsDataEntity.getInternalLinks(),
					urlMetricsDataEntity.getOutbounds(),
					urlMetricsDataEntity.getCachedDate(),
					urlMetricsDataEntity.getRespCode(),
					urlMetricsDataEntity.getIpaddress(),
					urlMetricsDataEntity.getLastUpdate(),
					urlMetricsDataEntity.getPrUpdateDate(),
					urlMetricsDataEntity.getYahooUpdateDate(),
					urlMetricsDataEntity.getCacheDateUpdateDate(),
					urlMetricsDataEntity.getRespcodeUpdateDate(),
					urlMetricsDataEntity.getSemrushUpdateDate(),
					urlMetricsDataEntity.getPagespeedUpdateDate(),
					urlMetricsDataEntity.getGoogleIndex(),
					urlMetricsDataEntity.getSeomozUpdateDate(),
					urlMetricsDataEntity.getMozRank(),
					urlMetricsDataEntity.getPageAuthority(),
					urlMetricsDataEntity.getMozInbounds(),
					urlMetricsDataEntity.getRobotMeta(),
					urlMetricsDataEntity.getLinks(),
					urlMetricsDataEntity.getDomainAuthority(),
					urlMetricsDataEntity.getMajesticsUpdateDate(),
					urlMetricsDataEntity.getAcRank(),
					urlMetricsDataEntity.getPercentMobileEntrances(),
					urlMetricsDataEntity.getAssociatedKeywords(),
					urlMetricsDataEntity.getAssociatedKeywordsRanked(),
					urlMetricsDataEntity.getLinksAcquiredOrganically(),
					urlMetricsDataEntity.getLinksAcquiredManually(),
					urlMetricsDataEntity.getAssociatedCompetitors()
			};
			tempObjectArrayList.add(urlMetricsDataObjectArray);
		}

		totalNumberOfObjects = tempObjectArrayList.size() * NUMBER_OF_FIELDS;

		Object[] objectArray = new Object[totalNumberOfObjects];
		int idx = 0;
		for (Object[] tempObjectArray : tempObjectArrayList) {
			for (int i = 0; i < tempObjectArray.length; i++) {
				objectArray[idx++] = tempObjectArray[i];
			}
		}
		return objectArray;
	}

	private String getInsertMultiRowsSqlStatement(int recordsPerInsertStatement) {
		String response = null;
		StringBuffer sql = new StringBuffer();
		sql.append(SQL_CREATION);
		for (int i = 0; i < recordsPerInsertStatement; i++) {
			if (i == 0) {
				sql.append(SQL_CREATION_FORMAT_1);
			} else {
				sql.append(SQL_CREATION_FORMAT_2);
			}
		}
		sql.append(IConstants.SEMI_COLON);
		response = sql.toString();
		return response;
	}

	public void updateForBatch(List<UrlMetricsUpdateValueObject> urlMetricsUpdateValueObjectList) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("update ");
		stringBuilder.append(getTableName() + " ");
		stringBuilder.append("set ");
		stringBuilder.append(" url = ?, ");
		stringBuilder.append(" resp_code = ?, ");
		stringBuilder.append(" respcode_update_date = ?, ");
		stringBuilder.append(" internal_links = ?, ");
		stringBuilder.append(" outbounds = ?, ");
		stringBuilder.append(" percent_mobile_entrances = ?, ");
		stringBuilder.append(" associated_keywords = ?, ");
		stringBuilder.append(" associated_keywords_ranked = ?, ");
		stringBuilder.append(" links_acquired_organically = ?, ");
		stringBuilder.append(" links_acquired_manually = ?, ");
		stringBuilder.append(" associated_competitors = ?, ");
		stringBuilder.append(" last_update = ? ");
		stringBuilder.append("where ");
		stringBuilder.append(" id = ? ");
		String sqlString = stringBuilder.toString();
		List<Object[]> objectArrayList = new ArrayList<Object[]>();
		for (UrlMetricsUpdateValueObject urlMetricsUpdateValueObject : urlMetricsUpdateValueObjectList) {
			Object[] values = new Object[] {
					urlMetricsUpdateValueObject.getTargetUrlString(),
					urlMetricsUpdateValueObject.getPoliteCrawlResponseCode(),
					urlMetricsUpdateValueObject.getRespcodeUpdateDate(),
					urlMetricsUpdateValueObject.getPoliteCrawlInternalLinks(),
					urlMetricsUpdateValueObject.getPoliteCrawlOutboundLinks(),
					urlMetricsUpdateValueObject.getPercentMobileEntrancesNew(),
					urlMetricsUpdateValueObject.getAssociatedKeywordsNew(),
					urlMetricsUpdateValueObject.getAssociatedKeywordsRankedNew(),
					urlMetricsUpdateValueObject.getLinksAcquiredOrganicallyNew(),
					urlMetricsUpdateValueObject.getLinksAcquiredManuallyNew(),
					urlMetricsUpdateValueObject.getAssociatedCompetitorsNew(),
					new Date(),
					urlMetricsUpdateValueObject.getUrlMetricsId() };
			objectArrayList.add(values);
		}
		this.executeBatch(sqlString, objectArrayList);
	}

	public void cleanupTargetUrls() {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("delete from ");
		stringBuilder.append(getTableName() + " ");
		stringBuilder.append("where ");
		stringBuilder.append("    `type` = ? ");
		stringBuilder.append("and url_id not in ");
		stringBuilder.append("( ");
		stringBuilder.append("	select ");
		stringBuilder.append("	    t_target_url.id ");
		stringBuilder.append("	from ");
		stringBuilder.append("	    t_own_domain t_own_domain, ");
		stringBuilder.append("	    t_target_url t_target_url ");
		stringBuilder.append("	where ");
		stringBuilder.append("	    t_own_domain.`status` = ? ");
		stringBuilder.append("	and t_own_domain.id = t_target_url.own_domain_id ");
		stringBuilder.append("	and t_target_url.`type` = ? ");
		stringBuilder.append("	and t_target_url.`status` = ? ");
		stringBuilder.append(") ");
		String sqlString = stringBuilder.toString();
		this.executeUpdate(sqlString, UrlMetricsDataEntity.TYPE_TARGET_URL, OwnDomainEntity.STATE_ACTIVE, TargetUrlEntity.TYPE_ADD_BY_USER,
				TargetUrlEntity.STATUS_ACTIVE);
	}

	public void cleanupCompetitorUrls() {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("delete from ");
		stringBuilder.append(getTableName() + " ");
		stringBuilder.append("where ");
		stringBuilder.append("    `type` = ? ");
		stringBuilder.append("and url_id not in ");
		stringBuilder.append("( ");
		stringBuilder.append("select ");
		stringBuilder.append("    t_competitor_url.id ");
		stringBuilder.append("from ");
		stringBuilder.append("    t_competitor_url t_competitor_url ");
		stringBuilder.append(") ");
		String sqlString = stringBuilder.toString();
		this.executeUpdate(sqlString, UrlMetricsDataEntity.TYPE_COMPETITOR_URL);
	}

	public Map<Long, UrlMetricsDataEntity> getMap(int domainId) {
		Map<Long, UrlMetricsDataEntity> output = new HashMap<Long, UrlMetricsDataEntity>();
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     t_url_metrics_data.*");
		stringBuilder.append(" from");
		stringBuilder.append("     t_target_url t_target_url,");
		stringBuilder.append("     t_url_metrics_data t_url_metrics_data");
		stringBuilder.append(" where");
		stringBuilder.append("     t_target_url.own_domain_id = ?");
		stringBuilder.append(" and t_target_url.`type` = ?");
		stringBuilder.append(" and t_target_url.`status` = ?");
		stringBuilder.append(" and t_url_metrics_data.url_id = t_target_url.id");
		stringBuilder.append(" and t_url_metrics_data.`type` = ?");
		String sqlString = stringBuilder.toString();
		List<UrlMetricsDataEntity> urlMetricsDataEntityList = this.findBySql(sqlString, domainId, TargetUrlEntity.TYPE_ADD_BY_USER,
				TargetUrlEntity.STATUS_ACTIVE, UrlMetricsDataEntity.TYPE_TARGET_URL);
		long targetUrlId = 0;
		if (urlMetricsDataEntityList != null && urlMetricsDataEntityList.size() > 0) {
			for (UrlMetricsDataEntity urlMetricsDataEntity : urlMetricsDataEntityList) {
				targetUrlId = urlMetricsDataEntity.getUrlId();
				output.put(targetUrlId, urlMetricsDataEntity);
			}
		}
		return output;
	}

	public List<UrlMetricsDataEntity> getDistinctHttpStatusCodes(int domainId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select distinct t_url_metrics_data.resp_code");
		stringBuilder.append(" from");
		stringBuilder.append(" 	   t_target_url t_target_url,");
		stringBuilder.append(" 	   t_url_metrics_data t_url_metrics_data");
		stringBuilder.append(" where");
		stringBuilder.append("     t_target_url.own_domain_id = ?");
		stringBuilder.append(" and t_target_url.type = 1");
		stringBuilder.append(" and t_target_url.status = 1");
		stringBuilder.append(" and t_target_url.id = t_url_metrics_data.url_id");
		stringBuilder.append(" and t_url_metrics_data.resp_code > 0");
		stringBuilder.append(" and t_url_metrics_data.resp_code <= 999");
		stringBuilder.append(" and t_url_metrics_data.type = 1");
		String sqlString = stringBuilder.toString();
		return this.findBySql(sqlString, domainId);
	}

	public UrlMetricsDataEntity getByHttpStatusCode(int domainId, int httpStatusCode) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append(" 	   t_target_url.url as url");
		stringBuilder.append(" from");
		stringBuilder.append(" 	   t_target_url t_target_url,");
		stringBuilder.append(" 	   t_url_metrics_data t_url_metrics_data");
		stringBuilder.append(" where");
		stringBuilder.append("     t_target_url.own_domain_id = ?");
		stringBuilder.append(" and t_target_url.type = 1");
		stringBuilder.append(" and t_target_url.status = 1");
		stringBuilder.append(" and t_target_url.id = t_url_metrics_data.url_id");
		stringBuilder.append(" and t_url_metrics_data.resp_code = ?");
		stringBuilder.append(" and t_url_metrics_data.type = 1");
		stringBuilder.append(" limit");
		stringBuilder.append(" 	   1");
		String sqlString = stringBuilder.toString();
		return this.findObject(sqlString, domainId, httpStatusCode);
	}
}
