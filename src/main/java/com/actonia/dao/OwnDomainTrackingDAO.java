package com.actonia.dao;

import com.actonia.entity.OwnDomainTracking;

public class OwnDomainTrackingDAO extends BaseJdbcSupport<OwnDomainTracking> {

    @Override
    public String getTableName() {
        return "t_own_domain_tracking";
    }

    // query by ownDomainId to check if the record of the domain exists
    public OwnDomainTracking queryByOwnDomainId(int ownDomainId) {
        final String sql = "SELECT * FROM " + getTableName() + " WHERE ownDomainId = ?";
        return super.findObject(sql, ownDomainId);
    }

    // insert a new record only containing ownDomainId
    public void insertByOwnDomainId(int ownDomainId) {
        final String sql = "INSERT INTO " + getTableName() + " (ownDomainId) VALUES (?)";
        super.executeUpdate(sql, ownDomainId);
    }

    // update the targetUrlLatestDate by ownDomainId
    public void updateTargetUrlLatestDateByOwnDomainId(int ownDomainId, Integer targetUrlLatestDate) {
        final String sql = "UPDATE " + getTableName() + " SET targetUrlLatestDate = ? WHERE ownDomainId = ?";
        super.executeUpdate(sql, targetUrlLatestDate, ownDomainId);
    }

    public Integer getTargetUrlLatestDateOwnDomainIdByDomain(int domainId) {
        final String sql = "SELECT targetUrlLatestDate FROM " + getTableName() + " WHERE ownDomainId = ?";
        return super.queryForInteger(sql, domainId);
    }
}
