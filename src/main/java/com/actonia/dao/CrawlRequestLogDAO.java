package com.actonia.dao;

import java.util.List;

import com.actonia.IConstants;
import com.actonia.entity.CrawlRequestLog;
import com.actonia.entity.OwnDomainEntity;

public class CrawlRequestLogDAO extends BaseJdbcSupport<CrawlRequestLog> {

	@Override
	public String getTableName() {
		return "crawl_request_log";
	}

	public List<CrawlRequestLog> getLastFiveCompletedCrawls(int domainId, int crawlRequestLogId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     crawl_request_log.own_domain_id,");
		stringBuilder.append("     crawl_request_log.project_name,");
		stringBuilder.append("     crawl_request_log.crawl_request_date,");
		stringBuilder.append("     crawl_request_log.id");
		stringBuilder.append(" from");
		stringBuilder.append("     t_own_domain t_own_domain,");
		stringBuilder.append("     crawl_request_log crawl_request_log");
		stringBuilder.append(" where");
		stringBuilder.append("     t_own_domain.`status` = ?");
		stringBuilder.append(" and t_own_domain.id = ?");
		stringBuilder.append(" and t_own_domain.id = crawl_request_log.own_domain_id");
		stringBuilder.append(" and crawl_request_log.own_domain_id = ");
		stringBuilder.append(" 		(");
		stringBuilder.append(" 			select");
		stringBuilder.append(" 				crawl_request_log_1.own_domain_id");
		stringBuilder.append(" 			from");
		stringBuilder.append(" 				crawl_request_log crawl_request_log_1");
		stringBuilder.append(" 			where");
		stringBuilder.append(" 		   		crawl_request_log_1.id = ?");
		stringBuilder.append(" 		)");
		stringBuilder.append(" and crawl_request_log.id <= ?");
		stringBuilder.append(" and crawl_request_log.crawl_status = ?");
		stringBuilder.append(" and crawl_request_log.project_id = ");
		stringBuilder.append(" 		(");
		stringBuilder.append(" 			select");
		stringBuilder.append(" 				crawl_request_log_2.project_id");
		stringBuilder.append(" 			from");
		stringBuilder.append(" 				crawl_request_log crawl_request_log_2");
		stringBuilder.append(" 			where");
		stringBuilder.append(" 		   		crawl_request_log_2.id = ?");
		stringBuilder.append(" 		)");
		stringBuilder.append(" and (crawl_request_log.state is null or crawl_request_log.state = ?)");
		stringBuilder.append(" order by");
		stringBuilder.append("     crawl_request_log.crawl_request_date desc");
		stringBuilder.append(" limit");
		stringBuilder.append("     5");
		String sqlString = stringBuilder.toString();
		return this.findBySql(sqlString, OwnDomainEntity.STATE_ACTIVE, domainId, crawlRequestLogId, crawlRequestLogId, IConstants.CRAWL_STATUS_COMPLETED, crawlRequestLogId,
				IConstants.CRAWL_STATE_ACTIVE);
	}

	public List<CrawlRequestLog> getCrawlProjects(int domainId, int startCrawlRequestDate, int endCrawlRequestDate) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     crawl_request_log.project_name,");
		stringBuilder.append("     crawl_request_log.crawl_request_date,");
		stringBuilder.append("     crawl_request_log.id");
		stringBuilder.append(" from");
		stringBuilder.append("     crawl_request_log crawl_request_log");
		stringBuilder.append(" where");
		stringBuilder.append("     crawl_request_log.own_domain_id = ?");
		stringBuilder.append(" and crawl_request_log.crawl_request_date >= ?");
		stringBuilder.append(" and crawl_request_log.crawl_request_date <= ?");
		stringBuilder.append(" and crawl_request_log.state = ?");
		stringBuilder.append(" and crawl_request_log.crawl_status = ?");
		stringBuilder.append(" order by");
		stringBuilder.append("     crawl_request_log.project_name,");
		stringBuilder.append("     crawl_request_log.crawl_request_date");
		String sqlString = stringBuilder.toString();
		return this.findBySql(sqlString, domainId, startCrawlRequestDate, endCrawlRequestDate, IConstants.CRAWL_STATE_ACTIVE, IConstants.CRAWL_STATUS_COMPLETED);
	}
}
