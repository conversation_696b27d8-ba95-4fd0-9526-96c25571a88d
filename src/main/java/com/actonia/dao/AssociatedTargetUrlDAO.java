package com.actonia.dao;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.entity.AssociatedTargetUrlEntity;

public class AssociatedTargetUrlDAO extends BaseJdbcSupport<AssociatedTargetUrlEntity> {

	private static final int RECORDS_PER_SQL_STATEMENT = 10;
	private static final int SQL_STATEMENTS_PER_JDBC_CALL = 10;
	private static final String SEMI_COLON = ";";
	private static final String INSERT_FORMAT_1 = "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
	private static final String INSERT_FORMAT_2 = ",(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
	private static final int NUMBER_OF_FIELDS = 11;
	private static final String START_HH_MM_SS = " 00:00:00";
	private static final String END_HH_MM_SS = " 23:59:59";

	@Override
	public String getTableName() {
		return "associated_target_url";
	}

	public void insertMultiRowsBatch(List<AssociatedTargetUrlEntity> associatedTargetUrlEntityList) {
		Object[] objectArray = null;
		List<Object[]> objectArrayList = new ArrayList<Object[]>();
		List<AssociatedTargetUrlEntity> tempList = new ArrayList<AssociatedTargetUrlEntity>();

		for (AssociatedTargetUrlEntity associatedTargetUrlEntity : associatedTargetUrlEntityList) {
			tempList.add(associatedTargetUrlEntity);
			if (tempList.size() == RECORDS_PER_SQL_STATEMENT) {
				objectArray = getObjectArrayForInsert(tempList);
				objectArrayList.add(objectArray);
				if (objectArrayList.size() == SQL_STATEMENTS_PER_JDBC_CALL) {
					this.executeBatch(getInsertMultiRowsSqlStatement(RECORDS_PER_SQL_STATEMENT), objectArrayList);
					objectArrayList = new ArrayList<Object[]>();
				}
				tempList = new ArrayList<AssociatedTargetUrlEntity>();
			}
		}

		if (objectArrayList != null && objectArrayList.size() > 0) {
			this.executeBatch(getInsertMultiRowsSqlStatement(RECORDS_PER_SQL_STATEMENT), objectArrayList);
			objectArrayList = new ArrayList<Object[]>();
		}

		if (tempList != null && tempList.size() > 0) {
			objectArray = getObjectArrayForInsert(tempList);
			objectArrayList.add(objectArray);
			if (objectArrayList != null && objectArrayList.size() > 0) {
				this.executeBatch(getInsertMultiRowsSqlStatement(tempList.size()), objectArrayList);
				objectArrayList = new ArrayList<Object[]>();
			}
		}
	}

	private Object[] getObjectArrayForInsert(List<AssociatedTargetUrlEntity> list) {
		List<Object[]> testObjectArrayList = new ArrayList<Object[]>();
		Object[] testObjectArray = null;
		int totalNumberOfObjects = 0;
		for (AssociatedTargetUrlEntity associatedTargetUrlEntity : list) {
			testObjectArray = new Object[] { associatedTargetUrlEntity.getDomainId(), associatedTargetUrlEntity.getTargetUrlId(),
					associatedTargetUrlEntity.getAssociationInd(), associatedTargetUrlEntity.getTargetUrlPreviousStatus(),
					associatedTargetUrlEntity.getTargetUrlPreviousType(), associatedTargetUrlEntity.getTargetUrlPreviousUrl(),
					associatedTargetUrlEntity.getTargetUrlHttpStatusCode(), associatedTargetUrlEntity.getAssociationComment(),
					associatedTargetUrlEntity.getRankedUrl(), associatedTargetUrlEntity.getRankedUrlHttpStatusCode(),
					associatedTargetUrlEntity.getLastUpdateTimestamp(), };
			testObjectArrayList.add(testObjectArray);
		}
		totalNumberOfObjects = testObjectArrayList.size() * NUMBER_OF_FIELDS;
		Object[] objectArray = new Object[totalNumberOfObjects];
		int idx = 0;
		for (Object[] tempObjectArray : testObjectArrayList) {
			for (int i = 0; i < tempObjectArray.length; i++) {
				objectArray[idx++] = tempObjectArray[i];
			}
		}
		return objectArray;
	}

	private String getInsertMultiRowsSqlStatement(int recordsPerInsertStatement) {
		String response = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("insert into " + getTableName() + " ");
		stringBuilder.append("(");
		stringBuilder.append("	domain_id,");
		stringBuilder.append("	target_url_id,");
		stringBuilder.append("	association_ind,");
		stringBuilder.append("	target_url_previous_status,");
		stringBuilder.append("	target_url_previous_type,");
		stringBuilder.append("	target_url_previous_url,");
		stringBuilder.append("	target_url_http_status_code,");
		stringBuilder.append("	association_comment,");
		stringBuilder.append("	ranked_url,");
		stringBuilder.append("	ranked_url_http_status_code,");
		stringBuilder.append("	last_update_timestamp");
		stringBuilder.append(")");
		stringBuilder.append("values ");
		String sqlString = stringBuilder.toString();
		StringBuffer sql = new StringBuffer();
		sql.append(sqlString);
		for (int i = 0; i < recordsPerInsertStatement; i++) {
			if (i == 0) {
				sql.append(INSERT_FORMAT_1);
			} else {
				sql.append(INSERT_FORMAT_2);
			}
		}
		sql.append(SEMI_COLON);
		response = sql.toString();
		return response;
	}

	public void cleanup(int domainId, long targetUrlId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("delete from associated_target_url ");
		stringBuilder.append("where ");
		stringBuilder.append("    domain_id = ? ");
		stringBuilder.append("and target_url_id = ? ");
		String sqlString = stringBuilder.toString();
		this.executeUpdate(sqlString, domainId, targetUrlId);
	}

	public AssociatedTargetUrlEntity getOneByDomainId(int domainId) {
		String sqlString = "select * from " + getTableName() + " where domain_id = ? limit 1";
		return findObject(sqlString, domainId);
	}

	public void delete(int domainId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("delete from associated_target_url ");
		stringBuilder.append("where ");
		stringBuilder.append("    domain_id = ? ");
		String sqlString = stringBuilder.toString();
		this.executeUpdate(sqlString, domainId);
	}

	public List<AssociatedTargetUrlEntity> getTargetUrlIds(int domainId, int conversionInd) {
		String sqlString = "select target_url_id from " + getTableName() + " where domain_id = ? and conversion_ind = ?";
		return findBySql(sqlString, domainId, conversionInd);
	}

	public AssociatedTargetUrlEntity getByTargetUrlId(int domainId, long targetUrlId) {
		String sqlString = "select * from " + getTableName() + " where domain_id = ? and target_url_id = ?";
		return findObject(sqlString, domainId, targetUrlId);
	}

	public List<AssociatedTargetUrlEntity> getTargetUrlIds(int domainId) {
		String sqlString = "select target_url_id, association_ind from " + getTableName() + " where domain_id = ?";
		return findBySql(sqlString, domainId);
	}

	public void update(AssociatedTargetUrlEntity associatedTargetUrlEntity) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" update associated_target_url ");
		stringBuilder.append(" set ");
		stringBuilder.append("     association_ind = ?,");
		stringBuilder.append("     target_url_previous_status = ?,");
		stringBuilder.append("     target_url_previous_type = ?,");
		stringBuilder.append("     target_url_previous_url = ?,");
		stringBuilder.append("     target_url_http_status_code = ?,");
		stringBuilder.append("     association_comment = ?,");
		stringBuilder.append("     ranked_url = ?,");
		stringBuilder.append("     ranked_url_http_status_code = ?,");
		stringBuilder.append("     last_update_timestamp = ?");
		stringBuilder.append(" where ");
		stringBuilder.append("     domain_id = ? ");
		stringBuilder.append(" and target_url_id = ? ");
		String sqlString = stringBuilder.toString();
		this.executeUpdate(sqlString, associatedTargetUrlEntity.getAssociationInd(), associatedTargetUrlEntity.getTargetUrlPreviousStatus(),
				associatedTargetUrlEntity.getTargetUrlPreviousType(), associatedTargetUrlEntity.getTargetUrlPreviousUrl(),
				associatedTargetUrlEntity.getTargetUrlHttpStatusCode(), associatedTargetUrlEntity.getAssociationComment(),
				associatedTargetUrlEntity.getRankedUrl(), associatedTargetUrlEntity.getRankedUrlHttpStatusCode(),
				associatedTargetUrlEntity.getLastUpdateTimestamp(), associatedTargetUrlEntity.getDomainId(),
				associatedTargetUrlEntity.getTargetUrlId());
	}

	public List<AssociatedTargetUrlEntity> getToBeAssociatedByUpdatingTargetUrlString(int domainId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     associated_target_url.target_url_id,");
		stringBuilder.append("     associated_target_url.target_url_previous_url,");
		stringBuilder.append("     associated_target_url.ranked_url");
		stringBuilder.append(" from");
		stringBuilder.append("     associated_target_url associated_target_url");
		stringBuilder.append(" where");
		stringBuilder.append("     associated_target_url.domain_id = ?");
		stringBuilder.append(" and associated_target_url.association_ind = ?");
		stringBuilder.append(" and associated_target_url.target_url_previous_url is not null");
		String sqlString = stringBuilder.toString();
		return findBySql(sqlString, domainId, AssociatedTargetUrlEntity.ASSOCIATION_IND_TRUE);
	}

	public void resetNotAssociated() {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" delete from associated_target_url");
		stringBuilder.append(" where");
		stringBuilder.append("    association_ind = ?");
		String sqlString = stringBuilder.toString();
		this.executeUpdate(sqlString, AssociatedTargetUrlEntity.ASSOCIATION_IND_FALSE);
	}

	// and associated_target_url.last_update_timestamp > '2017-05-16 00:00:00'
	// and associated_target_url.last_update_timestamp < '2017-05-16 23:59:59'
	public List<AssociatedTargetUrlEntity> getAssociatedByDateRange(int domainId, Date startDate, Date endDate) {
		List<AssociatedTargetUrlEntity> associatedTargetUrlEntityList = null;
		String startDateTimeString = null;
		String endDateTimeString = null;
		Date testStartDateTime = null;
		Date testEndDateTime = null;
		StringBuilder stringBuilder = null;
		String sqlString = null;
		try {
			startDateTimeString = DateFormatUtils.format(startDate, IConstants.DATE_FORMAT_YYYY_MM_DD) + START_HH_MM_SS;
			testStartDateTime = DateUtils.parseDate(startDateTimeString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD_HHMMSS });
			endDateTimeString = DateFormatUtils.format(endDate, IConstants.DATE_FORMAT_YYYY_MM_DD) + END_HH_MM_SS;
			testEndDateTime = DateUtils.parseDate(endDateTimeString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD_HHMMSS });
			System.out.println("getAssociatedByDateRange() domainId=" + domainId 
					+ ",startDate=" + startDate					
					+ ",startDateTimeString="+ startDateTimeString
					+ ",testStartDateTime="+ testStartDateTime 
					+ ",endDate=" + endDate  
					+ ",endDateTimeString=" + endDateTimeString
					+ ",testEndDateTime=" + testEndDateTime
			);
			stringBuilder = new StringBuilder();
			stringBuilder.append(" select");
			stringBuilder.append("     associated_target_url.target_url_id");
			stringBuilder.append(" from");
			stringBuilder.append("     associated_target_url associated_target_url");
			stringBuilder.append(" where");
			stringBuilder.append("     associated_target_url.domain_id = ?");
			stringBuilder.append(" and associated_target_url.association_ind = ?");
			stringBuilder.append(" and");
			stringBuilder.append(" (");
			stringBuilder.append(" 	 associated_target_url.target_url_previous_url is not null");
			stringBuilder.append(" or (associated_target_url.target_url_previous_url is null");
			stringBuilder.append(" and associated_target_url.association_comment is null)");
			stringBuilder.append(" )");
			stringBuilder.append(" and associated_target_url.last_update_timestamp >= ?");
			stringBuilder.append(" and associated_target_url.last_update_timestamp <= ?");
			sqlString = stringBuilder.toString();
			associatedTargetUrlEntityList = findBySql(sqlString, domainId, AssociatedTargetUrlEntity.ASSOCIATION_IND_TRUE, testStartDateTime,
					testEndDateTime);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return associatedTargetUrlEntityList;
	}

	public List<AssociatedTargetUrlEntity> getAllAssociated(int domainId) {
		List<AssociatedTargetUrlEntity> associatedTargetUrlEntityList = null;
		StringBuilder stringBuilder = null;
		String sqlString = null;
		try {
			stringBuilder = new StringBuilder();
			stringBuilder.append(" select");
			stringBuilder.append("     associated_target_url.target_url_id");
			stringBuilder.append(" from");
			stringBuilder.append("     associated_target_url associated_target_url");
			stringBuilder.append(" where");
			stringBuilder.append("     associated_target_url.domain_id = ?");
			stringBuilder.append(" and associated_target_url.association_ind = ?");
			stringBuilder.append(" and");
			stringBuilder.append(" (");
			stringBuilder.append(" 	 associated_target_url.target_url_previous_url is not null");
			stringBuilder.append(" or (associated_target_url.target_url_previous_url is null");
			stringBuilder.append(" and associated_target_url.association_comment is null)");
			stringBuilder.append(" )");
			sqlString = stringBuilder.toString();
			associatedTargetUrlEntityList = findBySql(sqlString, domainId, AssociatedTargetUrlEntity.ASSOCIATION_IND_TRUE);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return associatedTargetUrlEntityList;
	}

	public boolean checkIfAutoAssociated(int domainId) {		
		boolean output = false;
		StringBuilder stringBuilder = null;
		String sqlString = null;
		int totalRecords = 0;
		try {
			stringBuilder = new StringBuilder();
			stringBuilder.append(" select");
			stringBuilder.append("     count(*)");
			stringBuilder.append(" from");
			stringBuilder.append("     associated_target_url associated_target_url");
			stringBuilder.append(" where");
			stringBuilder.append("     associated_target_url.domain_id = ?");
			stringBuilder.append(" and associated_target_url.association_ind = ?");
			stringBuilder.append(" and");
			stringBuilder.append(" (");
			stringBuilder.append(" 	 associated_target_url.target_url_previous_url is not null");
			stringBuilder.append(" or (associated_target_url.target_url_previous_url is null");
			stringBuilder.append(" and associated_target_url.association_comment is null)");
			stringBuilder.append(" )");
			sqlString = stringBuilder.toString();
			totalRecords = queryForInt(sqlString, domainId, AssociatedTargetUrlEntity.ASSOCIATION_IND_TRUE);
			if (totalRecords > 0) {
				output = true;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return output;
	}
}
