package com.actonia.dao;

import com.actonia.entity.KeywordEntity;
import com.actonia.entity.KeywordPageRelEntity;

public class KeywordPageRelEntityDAO extends BaseJdbcSupport<KeywordPageRelEntity> {

	@Override
	public String getTableName() {
		return "keyword_page_rel";
	}

//	public Integer getAssociatedManagedKeywords(long targeturlId) {
//		StringBuilder stringBuilder = new StringBuilder();
//		stringBuilder.append("select ");
//		stringBuilder.append("	 count(distinct keyword_page_rel.keywordId) ");
//		stringBuilder.append("from ");
//		stringBuilder.append("	 keyword_page_rel keyword_page_rel, ");
//		stringBuilder.append("	 t_keyword t_keyword ");
//		stringBuilder.append("where ");
//		stringBuilder.append("    keyword_page_rel.pageId = ? ");
//		stringBuilder.append("and keyword_page_rel.keywordId = t_keyword.id ");
//		stringBuilder.append("and t_keyword.rank_check = ? ");
//		String sqlString = stringBuilder.toString();
//		return queryForInteger(sqlString, targeturlId, KeywordEntity.RANK_CHECK_ACTIVE);
//	}

//	public Integer getRankedAssociatedManagedKeywords(long targeturlId) {
//		StringBuilder stringBuilder = new StringBuilder();
//		stringBuilder.append("select ");
//		stringBuilder.append("	 count(distinct keyword_page_rel.keywordId) ");
//		stringBuilder.append("from ");
//		stringBuilder.append("	 keyword_page_rel keyword_page_rel, ");
//		stringBuilder.append("	 t_keyword t_keyword ");
//		stringBuilder.append("where ");
//		stringBuilder.append("    keyword_page_rel.pageId = ? ");
//		stringBuilder.append("and keyword_page_rel.keywordId = t_keyword.id ");
//		stringBuilder.append("and t_keyword.rank_check = ? ");
//		stringBuilder.append("and t_keyword.rank1 >= 1 ");
//		stringBuilder.append("and t_keyword.rank1 <= 100 ");
//		String sqlString = stringBuilder.toString();
//		return queryForInteger(sqlString, targeturlId, KeywordEntity.RANK_CHECK_ACTIVE);
//	}

//	public int getCompetitorUrlNum(long targetUrlId) {
//		StringBuilder stringBuilder = new StringBuilder();
//		stringBuilder.append("select ");
//		stringBuilder.append("	count(distinct t_competitor_url.id) ");
//		stringBuilder.append("from ");
//		stringBuilder.append("	keyword_page_rel keyword_page_rel ");
//		stringBuilder.append("		left join t_keyword_competitorurl t_keyword_competitorurl ");
//		stringBuilder.append("			on keyword_page_rel.keywordId = t_keyword_competitorurl.keyword_id ");
//		stringBuilder.append("				left join t_competitor_url t_competitor_url ");
//		stringBuilder.append("					on t_keyword_competitorurl.competitorurl_id = t_competitor_url.id ");
//		stringBuilder.append("where ");
//		stringBuilder.append("    keyword_page_rel.pageId = ? ");
//		String sqlString = stringBuilder.toString();
//		return queryForInt(sqlString, targetUrlId);
//	}
}