package com.actonia.dao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.StringUtils;

import com.actonia.IConstants;
import com.actonia.entity.CompetitorUrlClickHouseEntity;
import com.actonia.utils.FormatUtils;

import ru.yandex.clickhouse.ClickHouseDataSource;
import ru.yandex.clickhouse.settings.ClickHouseProperties;

// Due to ClickHouse concurrency issues, use Singleton pattern, do not use Spring (ie. all methods must be 'synchronized')
public class CompetitorUrlClickHouseDAO {

	//private static boolean isDebug = false;
	private static List<String> databaseHostnameList;
	private static String databasePort = null;
	private static String databaseName = null;
	private static List<Connection> connectionList;
	private static int batchCreationSize;
	private static String databaseUser = null;
	private static String databasePassword = null;
	private static List<String> connectionUrlList = null;
	private static int connectionTimeoutInMilliseconds;
	private static int maximumRetryCounts;
	private static int retryWaitMilliseconds;
	public static final String TABLE_NAME = "dis_competitor_url";
	private static CompetitorUrlClickHouseDAO competitorUrlClickHouseDAO;

	public static CompetitorUrlClickHouseDAO getInstance() throws Exception {
		if (competitorUrlClickHouseDAO == null) {
			competitorUrlClickHouseDAO = initialize();
		}
		return competitorUrlClickHouseDAO;
	}

	private static CompetitorUrlClickHouseDAO initialize() throws Exception {
		FormatUtils.getInstance().logMemoryUsage("initialize() begins.");

		CompetitorUrlClickHouseDAO competitorUrlClickHouseDAO = null;

		String clickHouseDatabaseHostnames = ClickHouseCrawlDatabaseConfigurations
				.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_HOSTNAMES);
		String[] clickHouseDatabaseHostnameArray = clickHouseDatabaseHostnames.split(IConstants.COMMA);
		String clickHouseDatabasePort = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_PORT);
		String clickHouseDatabaseName = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_DB_NAME);
		String clickHouseDatabaseUser = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_USER);
		String clickHouseDatabasePassword = ClickHouseCrawlDatabaseConfigurations.getStringProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_PASSWORD);
		int clickHouseBatchCreationSize = ClickHouseCrawlDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_BATCH_CREATION_SIZE, 100);
		int clickHouseconnectionTimeoutInMilliseconds = ClickHouseCrawlDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_CONNECTION_TIMEOUT_MILLISECONDS, 88888);
		int clickHouseMaximumRetryCounts = ClickHouseCrawlDatabaseConfigurations.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_MAX_RETRY_COUNTS,
				8);
		int clickHouseRetryWaitMilliseconds = ClickHouseCrawlDatabaseConfigurations
				.getIntProperty(IConstants.RUNTIME_PROPERTY_NAME_CRAWLER_V3_CLICKHOUSE_RETRY_WAIT_MILLISECONDS, 1000);

		try {
			competitorUrlClickHouseDAO = new CompetitorUrlClickHouseDAO(clickHouseDatabaseHostnameArray, clickHouseDatabasePort, clickHouseDatabaseName,
					clickHouseBatchCreationSize, clickHouseDatabaseUser, clickHouseDatabasePassword, clickHouseconnectionTimeoutInMilliseconds,
					clickHouseMaximumRetryCounts, clickHouseRetryWaitMilliseconds);
		} catch (Exception e) {
			e.printStackTrace();
		}

		FormatUtils.getInstance().logMemoryUsage("initialize() ends. competitorUrlClickHouseDAO=" + competitorUrlClickHouseDAO.toString());
		return competitorUrlClickHouseDAO;
	}

	private CompetitorUrlClickHouseDAO(String[] databaseHostnameArrayInput, String databasePortInput, String databaseNameInput, int batchCreationSizeInput,
			String databaseUserInput, String databasePasswordInput, int connectionTimeoutInMillisecondsInput, int maximumRetryCountsInput,
			int retryWaitMillisecondsInput) throws Exception {

		ClickHouseDataSource clickHouseDataSource = null;
		Connection connection = null;

		databaseHostnameList = Arrays.asList(databaseHostnameArrayInput);
		databasePort = databasePortInput;
		databaseName = databaseNameInput;
		batchCreationSize = batchCreationSizeInput;
		databaseUser = databaseUserInput;
		databasePassword = databasePasswordInput;
		connectionTimeoutInMilliseconds = connectionTimeoutInMillisecondsInput;
		maximumRetryCounts = maximumRetryCountsInput;
		retryWaitMilliseconds = retryWaitMillisecondsInput;

		FormatUtils.getInstance()
				.logMemoryUsage("CompetitorUrlClickHouseDAO() databaseHostnameList=" + databaseHostnameList.toString() + ",databasePort=" + databasePort
						+ ",databaseName=" + databaseName + ",batchCreationSize=" + batchCreationSize + ",databaseUser=" + databaseUser + ",databasePassword="
						+ databasePassword + ",connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ",maximumRetryCounts=" + maximumRetryCounts
						+ ",retryWaitMilliseconds=" + retryWaitMilliseconds + ",TABLE_NAME=" + TABLE_NAME);

		ClickHouseProperties clickHouseProperties = new ClickHouseProperties();
		clickHouseProperties.setDecompress(true);
		clickHouseProperties.setConnectionTimeout(connectionTimeoutInMilliseconds);
		clickHouseProperties.setSocketTimeout(connectionTimeoutInMilliseconds);

		connectionList = new ArrayList<Connection>();

		String connectionUrl = null;

		connectionUrlList = new ArrayList<String>();

		for (String databaseHostname : databaseHostnameList) {
			if (StringUtils.isNotBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
						+ "?user=" + databaseUser + "&password=" + databasePassword;
			} else if (StringUtils.isBlank(databaseUser) && StringUtils.isNotBlank(databasePassword)) {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH + databaseName
						+ "?password=" + databasePassword;
			} else {
				connectionUrl = IConstants.CLICKHOUSE_JDBC_CONNECTION_URL + databaseHostname + IConstants.COLON + databasePort + IConstants.FORWARD_SLASH
						+ databaseName;
			}
			connectionUrlList.add(connectionUrl);
			clickHouseDataSource = new ClickHouseDataSource(connectionUrl, clickHouseProperties);
			connection = clickHouseDataSource.getConnection();
			connectionList.add(connection);
			FormatUtils.getInstance().logMemoryUsage("CompetitorUrlClickHouseDAO() database connection created...connectionUrl=" + connectionUrl);
		}
	}

	@Override
	public String toString() {
		return "CompetitorUrlClickHouseDAO [databaseHostnameList=" + databaseHostnameList.toString() + ", databasePort=" + databasePort + ", databaseName="
				+ databaseName + ", batchCreationSize=" + batchCreationSize + ", databaseUser=" + databaseUser + ", databasePassword=" + databasePassword
				+ ", connectionTimeoutInMilliseconds=" + connectionTimeoutInMilliseconds + ", maximumRetryCounts=" + maximumRetryCounts + ", retryWaitMilliseconds="
				+ retryWaitMilliseconds + "]";
	}

	//public synchronized String getTableName() {
	//	return getTableName(null);
	//}

	public synchronized String getTableName(String tableName) {
		if (StringUtils.isNotBlank(tableName)) {
			return tableName;
		} else {
			return TABLE_NAME;
		}
	}

	public synchronized int getBatchCreationSize() {
		return batchCreationSize;
	}

	public synchronized void createBatch(List<CompetitorUrlClickHouseEntity> competitorUrlClickHouseEntityList, String tableName) throws Exception {
		//long startTimestamp = System.currentTimeMillis();

		PreparedStatement preparedStatement = null;
		int index = 0;
		Connection connection = null;

		String creationSqlStatement = getCreationSqlStatement(tableName);
		//FormatUtils.getInstance().logMemoryUsage("createBatch() creationSqlStatement=" + creationSqlStatement);

		int retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				//startTimestamp = System.nanoTime();
				connection = connectionList.get(0);
				preparedStatement = connection.prepareStatement(creationSqlStatement);
				for (CompetitorUrlClickHouseEntity competitorUrlClickHouseEntity : competitorUrlClickHouseEntityList) {

					index = 1;

					// track_date
					preparedStatement.setDate(index++, new java.sql.Date(competitorUrlClickHouseEntity.getTrack_date().getTime()));

					// url_domain
					preparedStatement.setString(index++, competitorUrlClickHouseEntity.getUrl_domain());

					// url
					preparedStatement.setString(index++, competitorUrlClickHouseEntity.getUrl());

					preparedStatement.addBatch();
				}
				preparedStatement.executeBatch();
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("createBatch() e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			} finally {
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();
				}
			}
		}
		//FormatUtils.getInstance().logMemoryUsage("createBatch() competitorUrlClickHouseEntityList.size()==" + competitorUrlClickHouseEntityList.size()
		//		+ ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private synchronized String getCreationSqlStatement(String tableName) {
		String response = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" insert into " + getTableName(tableName));
		stringBuilder.append(" (");
		stringBuilder.append(" track_date,");
		stringBuilder.append(" url_domain,");
		stringBuilder.append(" url");
		stringBuilder.append(" )");
		stringBuilder.append(" values ");
		stringBuilder.append(" (");
		stringBuilder.append(" ?,");
		stringBuilder.append(" ?,");
		stringBuilder.append(" ?");
		stringBuilder.append(" )");
		stringBuilder.append(IConstants.SEMI_COLON);
		response = stringBuilder.toString();
		return response;
	}

	public List<CompetitorUrlClickHouseEntity> getList(String tableName) throws Exception {
		List<CompetitorUrlClickHouseEntity> competitorUrlClickHouseEntityList = new ArrayList<CompetitorUrlClickHouseEntity>();
		CompetitorUrlClickHouseEntity competitorUrlClickHouseEntity = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("   *");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));

		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getList() sqlString=" + sqlString);
		connection = connectionList.get(0);
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				preparedStatement = connection.prepareStatement(sqlString);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					competitorUrlClickHouseEntity = new CompetitorUrlClickHouseEntity();

					// track_date
					competitorUrlClickHouseEntity.setTrack_date(resultSet.getDate(IConstants.TRACK_DATE));

					// url_domain
					competitorUrlClickHouseEntity.setUrl_domain(resultSet.getString(IConstants.URL_DOMAIN));

					// url
					competitorUrlClickHouseEntity.setUrl(resultSet.getString(IConstants.URL));

					// url_hash
					competitorUrlClickHouseEntity.setUrl_hash(resultSet.getString(IConstants.URL_HASH));

					// url_murmur_hash
					competitorUrlClickHouseEntity.setUrl_murmur_hash(resultSet.getString(IConstants.URL_MURMUR_HASH));

					competitorUrlClickHouseEntityList.add(competitorUrlClickHouseEntity);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getList() e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			} finally {
				if (resultSet != null) {
					resultSet.close();
				}
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();
				}
			}
		}
		//FormatUtils.getInstance().logMemoryUsage("getList() ends. startTrackDate="+startTrackDate +",endTrackDate="+endTrackDate +",urlString="+urlString
		//		+ ",competitorUrlClickHouseEntityList.size()=" + competitorUrlClickHouseEntityList.size()
		//		+ ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp)
		//);
		return competitorUrlClickHouseEntityList;
	}

	public int getTotalRecords(Date trackDate, String reversedUrlDomain, String tableName) throws Exception {
		int totalRecords = 0;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		int index = 0;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     count(*) as " + IConstants.TOTAL_RECORDS);
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("     track_date = ?");
		stringBuilder.append(" and url_domain = ?");
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getTotalRecords() sqlString=" + sqlString);
		connection = connectionList.get(0);
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setDate(index++, new java.sql.Date(trackDate.getTime()));
				preparedStatement.setString(index++, reversedUrlDomain);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					totalRecords = resultSet.getInt(IConstants.TOTAL_RECORDS);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getTotalRecords() e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			} finally {
				if (resultSet != null) {
					resultSet.close();
				}
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();
				}
			}
		}
		//FormatUtils.getInstance().logMemoryUsage("getTotalRecords() ends. startTrackDate="+startTrackDate +",endTrackDate="+endTrackDate +",urlString="+urlString
		//		+ ",competitorUrlClickHouseEntityList.size()=" + competitorUrlClickHouseEntityList.size()
		//		+ ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp)
		//);
		return totalRecords;
	}

	public List<CompetitorUrlClickHouseEntity> getTotalRecordsByDomain(Date trackDate, String tableName) throws Exception {
		List<CompetitorUrlClickHouseEntity> competitorUrlClickHouseEntityList = new ArrayList<CompetitorUrlClickHouseEntity>();
		CompetitorUrlClickHouseEntity competitorUrlClickHouseEntity = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		int index = 0;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     url_domain,");
		stringBuilder.append("     count(*) as " + IConstants.TOTAL_RECORDS);
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("     track_date = ?");
		stringBuilder.append(" group by");
		stringBuilder.append("     url_domain");
		stringBuilder.append(" order by");
		stringBuilder.append("     " + IConstants.TOTAL_RECORDS + " desc");
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getTotalRecordsByDomain() sqlString=" + sqlString);
		connection = connectionList.get(0);
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setDate(index++, new java.sql.Date(trackDate.getTime()));
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {

					competitorUrlClickHouseEntity = new CompetitorUrlClickHouseEntity();

					// url_domain
					competitorUrlClickHouseEntity.setUrl_domain(resultSet.getString(IConstants.URL_DOMAIN));

					// total_records
					competitorUrlClickHouseEntity.setTotal_records(resultSet.getInt(IConstants.TOTAL_RECORDS));

					competitorUrlClickHouseEntityList.add(competitorUrlClickHouseEntity);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getTotalRecordsByDomain() e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			} finally {
				if (resultSet != null) {
					resultSet.close();
				}
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();
				}
			}
		}
		//FormatUtils.getInstance().logMemoryUsage("getTotalRecordsByDomain() ends. startTrackDate="+startTrackDate +",endTrackDate="+endTrackDate +",urlString="+urlString
		//		+ ",competitorUrlClickHouseEntityList.size()=" + competitorUrlClickHouseEntityList.size()
		//		+ ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp)
		//);
		return competitorUrlClickHouseEntityList;
	}

	public List<CompetitorUrlClickHouseEntity> getUrlsByDomain(Date trackDate, List<String> reversedUrlDomainList, String tableName) throws Exception {
		List<CompetitorUrlClickHouseEntity> competitorUrlClickHouseEntityList = new ArrayList<CompetitorUrlClickHouseEntity>();
		CompetitorUrlClickHouseEntity competitorUrlClickHouseEntity = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		int index = 0;
		PreparedStatement preparedStatement = null;
		boolean isFirstField = true;
		StringBuilder stringBuilder = null;
		String reversedUrlDomains = null;

		// reversedUrlDomains
		isFirstField = true;
		stringBuilder = new StringBuilder();
		for (String reversedUrlDomain : reversedUrlDomainList) {
			if (isFirstField == true) {
				isFirstField = false;
			} else {
				stringBuilder.append(IConstants.COMMA);
			}
			stringBuilder.append(IConstants.SINGLE_QUOTE);
			stringBuilder.append(reversedUrlDomain);
			stringBuilder.append(IConstants.SINGLE_QUOTE);
		}
		reversedUrlDomains = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("CompetitorUrlClickHouseDAO.getUrlsByDomain() reversedUrlDomains=" + reversedUrlDomains);

		stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("   url");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		stringBuilder.append(" where");
		stringBuilder.append("     track_date = ?");
		stringBuilder.append(" and url_domain in (" + reversedUrlDomains + ")");

		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("CompetitorUrlClickHouseDAO.getUrlsByDomain() sqlString=" + sqlString);
		connection = connectionList.get(0);
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				preparedStatement.setDate(index++, new java.sql.Date(trackDate.getTime()));
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {
					competitorUrlClickHouseEntity = new CompetitorUrlClickHouseEntity();

					// url
					competitorUrlClickHouseEntity.setUrl(resultSet.getString(IConstants.URL));

					competitorUrlClickHouseEntityList.add(competitorUrlClickHouseEntity);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getUrlsByDomain() e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			} finally {
				if (resultSet != null) {
					resultSet.close();
				}
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();
				}
			}
		}
		//FormatUtils.getInstance().logMemoryUsage("getUrlsByDomain() ends. startTrackDate="+startTrackDate +",endTrackDate="+endTrackDate +",urlString="+urlString
		//		+ ",competitorUrlClickHouseEntityList.size()=" + competitorUrlClickHouseEntityList.size()
		//		+ ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp)
		//);
		return competitorUrlClickHouseEntityList;
	}

	public Date getTrackDate(String tableName) throws Exception {
		Date trackDate = null;
		ResultSet resultSet = null;
		Connection connection = null;
		int retryCount = 0;
		int index = 0;
		PreparedStatement preparedStatement = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select distinct");
		stringBuilder.append("     track_date");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName(tableName));
		String sqlString = stringBuilder.toString();
		//FormatUtils.getInstance().logMemoryUsage("getTrackDate() sqlString=" + sqlString);
		connection = connectionList.get(0);
		retryCount = 0;
		while (retryCount < maximumRetryCounts) {
			try {
				index = 1;
				preparedStatement = connection.prepareStatement(sqlString);
				resultSet = preparedStatement.executeQuery();
				while (resultSet.next()) {

					// track_date
					trackDate = resultSet.getDate(IConstants.TRACK_DATE);
				}
				retryCount = maximumRetryCounts;
			} catch (Exception e) {
				retryCount++;
				if (retryCount >= maximumRetryCounts) {
					e.printStackTrace();
					throw e;
				} else {
					FormatUtils.getInstance().logMemoryUsage("getTrackDate() e.getMessage()=" + e.getMessage() + ",retryCount=" + retryCount);
					try {
						Thread.sleep(retryWaitMilliseconds);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			} finally {
				if (resultSet != null) {
					resultSet.close();
				}
				if (preparedStatement != null) {
					preparedStatement.closeOnCompletion();
				}
			}
		}
		return trackDate;
	}
}
