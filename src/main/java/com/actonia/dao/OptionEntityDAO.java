package com.actonia.dao;

import java.util.ArrayList;
import java.util.List;

import com.actonia.entity.OptionEntity;

public class OptionEntityDAO extends BaseJdbcSupport<OptionEntity> {

	@Override
	public String getTableName() {
		return "t_option";
	}

	public List<OptionEntity> queryForDiv(Integer ownDomainId, Long urlId) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from t_option where own_domain_id = ? and url_id = ? ");
		sql.append(" and (option_type = 3 or option_type= 4)");

		return findBySql(sql.toString(), ownDomainId, urlId);
	}

	public List<OptionEntity> getRatingOptions(int oid) {
		String hql = " select * from t_option where own_domain_id = ? and option_type = 5 ";
		return this.findBySql(hql, oid);
	}

	public void insertForBatch(List<OptionEntity> insertData) {
		String sql = "INSERT INTO `t_option` (`option_type`, `own_domain_id`, `option_text`, `url_id`) VALUES (?,?,?,?)";

		List<Object[]> batch = new ArrayList<Object[]>();
		for (OptionEntity optionEntity : insertData) {
			Object[] values = new Object[] { optionEntity.getOptionType(), optionEntity.getOwnDomainId(), optionEntity.getOptionText(), optionEntity.getUrlId() };
			batch.add(values);
		}
		executeBatch(sql, batch);
	}

	public List<OptionEntity> getRatingOptionsForKeywords(int oid) {
		String hql = " select * from t_option where own_domain_id = ? and option_type = 21 and url_id is null order by option_value";
		return this.findBySql(hql, oid);
	}

	public void updateOptionLength(long urlId, int oid, int length, String optionText, int optionType) {
		String sql = " update t_option set option_length=? where own_domain_id=? and url_id=? and option_text=? and option_type = ?";

		executeUpdate(sql, length, oid, urlId, optionText, optionType);
	}

	public List<OptionEntity> getDivForDomain(int domainId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("select ");
		stringBuilder.append("	  t_option.* ");
		stringBuilder.append("from ");
		stringBuilder.append("	  t_option t_option ");
		stringBuilder.append("where ");
		stringBuilder.append("    t_option.own_domain_id = ? ");
		stringBuilder.append("and t_option.option_type in (3,4) ");
		String sqlString = stringBuilder.toString();
		return this.findBySql(sqlString, domainId);
	}
}
