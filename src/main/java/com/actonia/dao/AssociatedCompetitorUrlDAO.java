package com.actonia.dao;

import java.util.ArrayList;
import java.util.List;

import com.actonia.IConstants;
import com.actonia.entity.AssociatedCompetitorUrlEntity;


public class AssociatedCompetitorUrlDAO extends BaseJdbcSupport<AssociatedCompetitorUrlEntity> {


	private static final String INSERT_FORMAT_1 = "(?, ?, ?, ?, ?, ?, ?, ?)";
	private static final String INSERT_FORMAT_2 = ",(?, ?, ?, ?, ?, ?, ?, ?)";
	private static final int NUMBER_OF_FIELDS = 8;

	@Override
	public String getTableName() {
		return "associated_competitor_url";
	}

	public List<AssociatedCompetitorUrlEntity> getByDomainIdProcessDate(int domainId, int processDate) {
		String sqlString = "select * from " + getTableName() + " where domain_id = ? and process_date = ?";
		return findBySql(sqlString, domainId, processDate);
	}

	public AssociatedCompetitorUrlEntity get(int domainId, Long keywordId, String hashCode) {
		String sqlString = "select * from " + getTableName() + " where domain_id = ? and keyword_id = ? and competitor_url_hash_code = ?";
		return findObject(sqlString, domainId, keywordId, hashCode);
	}

	public void insertMultiRowsBatch(List<AssociatedCompetitorUrlEntity> associatedCompetitorUrlEntityList) {
		Object[] objectArray = null;
		List<Object[]> objectArrayList = new ArrayList<Object[]>();
		List<AssociatedCompetitorUrlEntity> tempList = new ArrayList<AssociatedCompetitorUrlEntity>();

		for (AssociatedCompetitorUrlEntity associatedCompetitorUrlEntity : associatedCompetitorUrlEntityList) {
			tempList.add(associatedCompetitorUrlEntity);
			if (tempList.size() == IConstants.RECORDS_PER_SQL_STATEMENT) {
				objectArray = getObjectArrayForInsert(tempList);
				objectArrayList.add(objectArray);
				if (objectArrayList.size() == IConstants.SQL_STATEMENTS_PER_JDBC_CALL) {
					this.executeBatch(getInsertMultiRowsSqlStatement(IConstants.RECORDS_PER_SQL_STATEMENT), objectArrayList);
					objectArrayList = new ArrayList<Object[]>();
				}
				tempList = new ArrayList<AssociatedCompetitorUrlEntity>();
			}
		}

		if (objectArrayList != null && objectArrayList.size() > 0) {
			this.executeBatch(getInsertMultiRowsSqlStatement(IConstants.RECORDS_PER_SQL_STATEMENT), objectArrayList);
			objectArrayList = new ArrayList<Object[]>();
		}

		if (tempList != null && tempList.size() > 0) {
			objectArray = getObjectArrayForInsert(tempList);
			objectArrayList.add(objectArray);
			if (objectArrayList != null && objectArrayList.size() > 0) {
				this.executeBatch(getInsertMultiRowsSqlStatement(tempList.size()), objectArrayList);
				objectArrayList = new ArrayList<Object[]>();
			}
		}
	}

	private Object[] getObjectArrayForInsert(List<AssociatedCompetitorUrlEntity> list) {
		List<Object[]> testObjectArrayList = new ArrayList<Object[]>();
		Object[] testObjectArray = null;
		int totalNumberOfObjects = 0;
		for (AssociatedCompetitorUrlEntity associatedCompetitorUrlEntity : list) {
			testObjectArray = new Object[] { 
					associatedCompetitorUrlEntity.getDomainId(), 
					associatedCompetitorUrlEntity.getKeywordId(), 
					associatedCompetitorUrlEntity.getCompetitorUrlHashCode(),
					associatedCompetitorUrlEntity.getCreateAssociationOnlyInd(), 
					associatedCompetitorUrlEntity.getCompetitorUrl(),
					0,
					associatedCompetitorUrlEntity.getKeywordName(),
					associatedCompetitorUrlEntity.getProcessDate(),
			};
			testObjectArrayList.add(testObjectArray);
		}
		totalNumberOfObjects = testObjectArrayList.size() * NUMBER_OF_FIELDS;
		Object[] objectArray = new Object[totalNumberOfObjects];
		int idx = 0;
		for (Object[] tempObjectArray : testObjectArrayList) {
			for (int i = 0; i < tempObjectArray.length; i++) {
				objectArray[idx++] = tempObjectArray[i];
			}
		}
		return objectArray;
	}

	private String getInsertMultiRowsSqlStatement(int recordsPerInsertStatement) {
		String response = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("insert into " + getTableName() + " ");
		stringBuilder.append("(");
		stringBuilder.append("	domain_id,");
		stringBuilder.append("	keyword_id,");
		stringBuilder.append("	competitor_url_hash_code,");
		stringBuilder.append("	create_association_only_ind,");
		stringBuilder.append("	competitor_url,");
		stringBuilder.append("	rank_pos,");
		stringBuilder.append("	keyword_name,");
		stringBuilder.append("	process_date");
		stringBuilder.append(")");
		stringBuilder.append("values ");
		String sqlString = stringBuilder.toString();
		StringBuffer sql = new StringBuffer();
		sql.append(sqlString);
		for (int i = 0; i < recordsPerInsertStatement; i++) {
			if (i == 0) {
				sql.append(INSERT_FORMAT_1);
			} else {
				sql.append(INSERT_FORMAT_2);
			}
		}
		sql.append(IConstants.SEMI_COLON);
		response = sql.toString();
		return response;
	}

	public List<AssociatedCompetitorUrlEntity> getDistinctProcessDates(int domainId, int startProcessDate, int endProcessDate) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("select ");
		stringBuilder.append("    distinct associated_competitor_url.process_date ");
		stringBuilder.append("from ");
		stringBuilder.append("    associated_competitor_url associated_competitor_url ");
		stringBuilder.append("where ");
		stringBuilder.append("    associated_competitor_url.domain_id = ? ");
		stringBuilder.append("and associated_competitor_url.process_date >= ? ");
		stringBuilder.append("and associated_competitor_url.process_date <= ? ");
		String sqlString = stringBuilder.toString();
		return findBySql(sqlString, domainId, startProcessDate, endProcessDate);
	}

	public List<AssociatedCompetitorUrlEntity> getByProcessDate(int domainId, int processDate) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("select ");
		stringBuilder.append("    associated_competitor_url.keyword_id, ");
		stringBuilder.append("    associated_competitor_url.create_association_only_ind, ");
		stringBuilder.append("    associated_competitor_url.competitor_url, ");
		stringBuilder.append("    associated_competitor_url.keyword_name, ");
		stringBuilder.append("    associated_competitor_url.competitor_url_hash_code, ");
		stringBuilder.append("    associated_competitor_url.process_date ");
		stringBuilder.append("from ");
		stringBuilder.append("    associated_competitor_url associated_competitor_url ");
		stringBuilder.append("where ");
		stringBuilder.append("    associated_competitor_url.domain_id = ? ");
		stringBuilder.append("and associated_competitor_url.process_date = ? ");
		String sqlString = stringBuilder.toString();
		return findBySql(sqlString, domainId, processDate);
	}
	
	public void delete(int domainId, Long keywordId, String competitorUrlHashCode) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("delete from associated_competitor_url ");
		stringBuilder.append("where ");
		stringBuilder.append("    domain_id = ? ");
		stringBuilder.append("and keyword_id = ? ");
		stringBuilder.append("and competitor_url_hash_code = ? ");
		String sqlString = stringBuilder.toString();
		this.executeUpdate(sqlString, domainId, keywordId, competitorUrlHashCode);		
	}

	public AssociatedCompetitorUrlEntity get(String hashCode) {
		String sqlString = "select * from " + getTableName() + " where competitor_url_hash_code = ? order by process_date desc limit 1";
		return findObject(sqlString, hashCode);
	}

	public List<AssociatedCompetitorUrlEntity> getDistinctAssociatedUrls() {
		String sqlString = "select distinct competitor_url_hash_code, competitor_url from " + getTableName();
		return findBySql(sqlString);
	}
	
	public void delete(int domainId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("delete from associated_competitor_url ");
		stringBuilder.append("where ");
		stringBuilder.append("    domain_id = ? ");
		String sqlString = stringBuilder.toString();
		this.executeUpdate(sqlString, domainId);		
	}

	public AssociatedCompetitorUrlEntity getOneByDomainId(int domainId) {
		String sqlString = "select * from " + getTableName() + " where domain_id = ? limit 1";
		return findObject(sqlString, domainId);
	}
	
	public List<AssociatedCompetitorUrlEntity> getUrlByDomainId(int domainId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("select distinct ");
		stringBuilder.append("    associated_competitor_url.competitor_url ");
		stringBuilder.append("from ");
		stringBuilder.append("    associated_competitor_url associated_competitor_url ");
		stringBuilder.append("where ");
		stringBuilder.append("    associated_competitor_url.domain_id = ? ");
		String sqlString = stringBuilder.toString();
		return findBySql(sqlString, domainId);
	}

	public List<AssociatedCompetitorUrlEntity> getNotInCompetitorUrlMd5List(int domainId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("select ");
		stringBuilder.append("    associated_competitor_url.keyword_id, ");
		stringBuilder.append("    associated_competitor_url.competitor_url_hash_code ");
		stringBuilder.append("from ");
		stringBuilder.append("	  associated_competitor_url associated_competitor_url ");
		stringBuilder.append("where ");
		stringBuilder.append("    associated_competitor_url.domain_id = ? ");
		stringBuilder.append("and associated_competitor_url.competitor_url_hash_code not in ");
		stringBuilder.append("( ");
		stringBuilder.append("select ");
		stringBuilder.append("   competitor_url_md5.hash_code ");
		stringBuilder.append("from ");
		stringBuilder.append("	competitor_url_md5 competitor_url_md5 ");
		stringBuilder.append("where ");
		stringBuilder.append("   competitor_url_md5.domain_id = ? ");
		stringBuilder.append(") ");
		String sqlString = stringBuilder.toString();
		return findBySql(sqlString, domainId, domainId);
	}

	public List<AssociatedCompetitorUrlEntity> getInCompetitorUrlMd5List(int domainId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("select ");
		stringBuilder.append("    associated_competitor_url.keyword_id, ");
		stringBuilder.append("    associated_competitor_url.competitor_url_hash_code ");
		stringBuilder.append("from ");
		stringBuilder.append("	  associated_competitor_url associated_competitor_url ");
		stringBuilder.append("where ");
		stringBuilder.append("    associated_competitor_url.domain_id = ? ");
		stringBuilder.append("and associated_competitor_url.competitor_url_hash_code in ");
		stringBuilder.append("( ");
		stringBuilder.append("select ");
		stringBuilder.append("   competitor_url_md5.hash_code ");
		stringBuilder.append("from ");
		stringBuilder.append("	competitor_url_md5 competitor_url_md5 ");
		stringBuilder.append("where ");
		stringBuilder.append("   competitor_url_md5.domain_id = ? ");
		stringBuilder.append(") ");
		String sqlString = stringBuilder.toString();
		return findBySql(sqlString, domainId, domainId);
	}
}
