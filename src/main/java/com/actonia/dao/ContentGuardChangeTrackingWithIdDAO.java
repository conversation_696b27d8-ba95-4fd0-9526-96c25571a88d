package com.actonia.dao;

import com.actonia.entity.ContentGuardChangeTrackingEntity;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class ContentGuardChangeTrackingWithIdDAO extends BaseJdbcSupport<ContentGuardChangeTrackingEntity> {

	@Override
	public String getTableName() {
		return "content_guard_change_tracking_with_id";
	}

	public List<ContentGuardChangeTrackingEntity> getList() {
		String sqlString = " select  * from " + getTableName();
		return this.findBySql(sqlString);
	}

	public List<ContentGuardChangeTrackingEntity> getCriticalIndicatorList() {
		String sqlString = " select  * from " + getTableName() + " where criticalFlag = 1";
		return this.findBySql(sqlString);
	}
}