package com.actonia.dao;

import java.util.Properties;

import com.actonia.IConstants;

public final class ClickHouseSeoDailyRankingDatabaseConfigurations {

	private static Properties prop = new Properties();

	public static String getStringProperty(String key, String defaultValue) {
		if (prop == null || prop.getProperty(key) == null) {
			return defaultValue;
		}
		return prop.getProperty(key);
	}

	public static String getStringProperty(String key) {
		if (prop == null || prop.getProperty(key) == null) {
			return "";
		}
		return prop.getProperty(key);
	}

	public static int getIntProperty(String key, int defaultValue) {
		if (prop == null || prop.getProperty(key) == null) {
			return defaultValue;
		}
		return Integer.parseInt(prop.getProperty(key));
	}

	public static short getShortProperty(String key, short defaultValue) {
		if (prop == null || prop.getProperty(key) == null) {
			return defaultValue;
		}
		return Short.parseShort(prop.getProperty(key));
	}

	public static long getLongProperty(String key, long defaultValue) {
		if (prop == null || prop.getProperty(key) == null) {
			return defaultValue;
		}
		return Long.parseLong(prop.getProperty(key));
	}

	public static boolean getBooleanProperty(String key, boolean defaultValue) {
		if (prop == null || prop.getProperty(key) == null) {
			return defaultValue;
		}
		return prop.getProperty(key).toLowerCase().trim().equals("true");
	}

	static {
		try {
			prop.load(ClickHouseSeoDailyRankingDatabaseConfigurations.class.getClassLoader().getResourceAsStream(IConstants.CLICKHOUSE_SEO_DAILY_RANKING_DATABASE_PROPERTIES));
		} catch (Exception e) {
			prop = null;
			System.err.println("WARNING: Could not find " + IConstants.CLICKHOUSE_SEO_DAILY_RANKING_DATABASE_PROPERTIES + " file in class path. use the default values.");
		}
	}
}
