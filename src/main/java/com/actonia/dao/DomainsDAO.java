package com.actonia.dao;

import com.actonia.entity.TDomains;

public class DomainsDAO extends BaseJdbcSupport<TDomains> {

	@Override
	public String getTableName() {
		return "t_domains";
	}

	public void updateToken(int ownDomainId) {
		String sql = "update t_domains set total_used = total_used+1 where own_domain_id=?";
		executeUpdate(sql, ownDomainId);
	}

	public TDomains domainInfo(int ownDomainId) {
		String sql = "select * from t_domains where own_domain_id = ?";
		return findObject(sql, ownDomainId);
	}
}
