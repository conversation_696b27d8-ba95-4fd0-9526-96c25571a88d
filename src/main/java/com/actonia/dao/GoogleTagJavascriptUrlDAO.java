package com.actonia.dao;

import java.util.ArrayList;
import java.util.List;

import com.actonia.entity.GoogleTagJavascriptUrlEntity;
import com.actonia.utils.FormatUtils;

public class GoogleTagJavascriptUrlDAO extends BaseJdbcSupport<GoogleTagJavascriptUrlEntity> {

	private static final int RECORDS_PER_SQL_STATEMENT = 10;
	private static final int SQL_STATEMENTS_PER_JDBC_CALL = 10;
	private static final String SEMI_COLON = ";";
	private static final String INSERT_FORMAT_1 = "(?, ?, ?, ?)";
	private static final String INSERT_FORMAT_2 = ",(?, ?, ?, ?)";
	private static final int NUMBER_OF_FIELDS = 4;

	@Override
	public String getTableName() {
		return "google_tag_javascript_url";
	}

	public GoogleTagJavascriptUrlEntity get(Long javascriptId, String urlHashCd) {
		FormatUtils.getInstance().logMemoryUsage("get() javascriptId=" + javascriptId + ",urlHashCd=" + urlHashCd);
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     *");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     javascript_id = ?");
		stringBuilder.append(" and url_hash_cd = ?");
		String sqlString = stringBuilder.toString();
		return this.findObject(sqlString, javascriptId, urlHashCd);
	}

	public List<GoogleTagJavascriptUrlEntity> getList(Long javascriptId) {
		FormatUtils.getInstance().logMemoryUsage("get() javascriptId=" + javascriptId);
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     *");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     javascript_id = ?");
		String sqlString = stringBuilder.toString();
		return this.findBySql(sqlString, javascriptId);
	}

	public void deleteAll(Long javascriptId) {
		FormatUtils.getInstance().logMemoryUsage("deleteAll() javascriptId=" + javascriptId);
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" delete from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     javascript_id = ?");
		String sqlString = stringBuilder.toString();
		this.executeUpdate(sqlString, javascriptId);
	}

	public void delete(Long javascriptId, String urlHashCd) {
		FormatUtils.getInstance().logMemoryUsage("delete() javascriptId=" + javascriptId + ",urlHashCd=" + urlHashCd);
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" delete from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     javascript_id = ?");
		stringBuilder.append(" and url_hash_cd = ?");
		String sqlString = stringBuilder.toString();
		this.executeUpdate(sqlString, javascriptId, urlHashCd);
	}

	public void batchCreate(List<GoogleTagJavascriptUrlEntity> googleTagJavascriptUrlEntityList) {
		Object[] objectArray = null;
		List<Object[]> objectArrayList = new ArrayList<Object[]>();
		List<GoogleTagJavascriptUrlEntity> tempList = new ArrayList<GoogleTagJavascriptUrlEntity>();

		for (GoogleTagJavascriptUrlEntity googleTagJavascriptUrlEntity : googleTagJavascriptUrlEntityList) {
			tempList.add(googleTagJavascriptUrlEntity);
			if (tempList.size() == RECORDS_PER_SQL_STATEMENT) {
				objectArray = getObjectArrayForInsert(tempList);
				objectArrayList.add(objectArray);
				if (objectArrayList.size() == SQL_STATEMENTS_PER_JDBC_CALL) {
					this.executeBatch(getInsertMultiRowsSqlStatement(RECORDS_PER_SQL_STATEMENT), objectArrayList);
					objectArrayList = new ArrayList<Object[]>();
				}
				tempList = new ArrayList<GoogleTagJavascriptUrlEntity>();
			}
		}

		if (objectArrayList != null && objectArrayList.size() > 0) {
			this.executeBatch(getInsertMultiRowsSqlStatement(RECORDS_PER_SQL_STATEMENT), objectArrayList);
			objectArrayList = new ArrayList<Object[]>();
		}

		if (tempList != null && tempList.size() > 0) {
			objectArray = getObjectArrayForInsert(tempList);
			objectArrayList.add(objectArray);
			if (objectArrayList != null && objectArrayList.size() > 0) {
				this.executeBatch(getInsertMultiRowsSqlStatement(tempList.size()), objectArrayList);
				objectArrayList = new ArrayList<Object[]>();
			}
		}
	}

	private Object[] getObjectArrayForInsert(List<GoogleTagJavascriptUrlEntity> list) {
		List<Object[]> tempObjectArrayList = new ArrayList<Object[]>();
		Object[] backlinkObjectArray = null;
		int totalNumberOfObjects = 0;
		for (GoogleTagJavascriptUrlEntity googleTagJavascriptUrlEntity : list) {
			backlinkObjectArray = new Object[] { googleTagJavascriptUrlEntity.getJavascriptId(), googleTagJavascriptUrlEntity.getUrlHashCd(),
					googleTagJavascriptUrlEntity.getUrl(), googleTagJavascriptUrlEntity.getLastUpdateTimestamp(), };
			tempObjectArrayList.add(backlinkObjectArray);
		}
		totalNumberOfObjects = tempObjectArrayList.size() * NUMBER_OF_FIELDS;
		Object[] objectArray = new Object[totalNumberOfObjects];
		int idx = 0;
		for (Object[] tempObjectArray : tempObjectArrayList) {
			for (int i = 0; i < tempObjectArray.length; i++) {
				objectArray[idx++] = tempObjectArray[i];
			}
		}
		return objectArray;
	}

	private String getInsertMultiRowsSqlStatement(int recordsPerInsertStatement) {
		String response = null;
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("insert into " + getTableName() + " ");
		stringBuilder.append("(");
		stringBuilder.append("	javascript_id,");
		stringBuilder.append("	url_hash_cd,");
		stringBuilder.append("	url,");
		stringBuilder.append("	last_update_timestamp");
		stringBuilder.append(")");
		stringBuilder.append("values ");
		String sqlString = stringBuilder.toString();
		StringBuffer sql = new StringBuffer();
		sql.append(sqlString);
		for (int i = 0; i < recordsPerInsertStatement; i++) {
			if (i == 0) {
				sql.append(INSERT_FORMAT_1);
			} else {
				sql.append(INSERT_FORMAT_2);
			}
		}
		sql.append(SEMI_COLON);
		response = sql.toString();
		return response;
	}

	public List<GoogleTagJavascriptUrlEntity> getHashCodeList(Long javascriptId, Integer limit) {
		FormatUtils.getInstance().logMemoryUsage("get() javascriptId=" + javascriptId);
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     url_hash_cd");
		stringBuilder.append(" from");
		stringBuilder.append(" " + getTableName());
		stringBuilder.append(" where");
		stringBuilder.append("     javascript_id = ?");
		if (limit != null && limit.intValue() > 1) {
			stringBuilder.append(" limit " + limit.intValue());
		}
		String sqlString = stringBuilder.toString();
		return this.findBySql(sqlString, javascriptId);
	}
}