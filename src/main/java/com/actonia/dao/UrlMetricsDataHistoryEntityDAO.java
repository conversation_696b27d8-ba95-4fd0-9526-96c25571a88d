package com.actonia.dao;

import java.util.ArrayList;
import java.util.List;

import com.actonia.entity.TargetURLCriteria;
import com.actonia.entity.UrlMetricsDataHistoryEntity;

public class UrlMetricsDataHistoryEntityDAO extends BaseJdbcSupport<UrlMetricsDataHistoryEntity> {

	private boolean isDebug = false;
	private static final int NUMBER_OF_FIELDS = 60;
	private static final String INSERT_VALUE_FORMAT_1 = "(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
	private static final String INSERT_VALUE_FORMAT_2 = ",(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
	private static final int RECORDS_PER_SQL_STATEMENT = 10;
	private static final int SQL_STATEMENTS_PER_JDBC_CALL = 10;
	private static final String SEMI_COLON = ";";

	@Override
	public String getTableName() {
		return "t_url_metrics_data_hist";
	}

	public void insertMultiRowsBatch(List<UrlMetricsDataHistoryEntity> urlMetricsDataHistoryEntityList) {
		Object[] objectArray = null;
		List<Object[]> objectArrayList = new ArrayList<Object[]>();
		List<UrlMetricsDataHistoryEntity> tempList = new ArrayList<UrlMetricsDataHistoryEntity>();

		for (UrlMetricsDataHistoryEntity urlMetricsDataHistoryEntity : urlMetricsDataHistoryEntityList) {
			tempList.add(urlMetricsDataHistoryEntity);
			if (tempList.size() == RECORDS_PER_SQL_STATEMENT) {
				objectArray = getObjectArrayForInsert(tempList);
				objectArrayList.add(objectArray);
				if (objectArrayList.size() == SQL_STATEMENTS_PER_JDBC_CALL) {
					this.executeBatch(getInsertMultiRowsSqlStatement(RECORDS_PER_SQL_STATEMENT), objectArrayList);
					if (isDebug == true) {
						System.out.println("UrlMetricsDataHistoryEntityDAO.insertMultiRowsBatch() objectArrayList.size()="
								+ objectArrayList.size());
					}
					objectArrayList = new ArrayList<Object[]>();
				}
				tempList = new ArrayList<UrlMetricsDataHistoryEntity>();
			}
		}

		if (objectArrayList != null && objectArrayList.size() > 0) {
			this.executeBatch(getInsertMultiRowsSqlStatement(RECORDS_PER_SQL_STATEMENT), objectArrayList);
			if (isDebug == true) {
				System.out.println("UrlMetricsDataHistoryEntityDAO.insertMultiRowsBatch() objectArrayList.size()=" + objectArrayList.size());
			}
			objectArrayList = new ArrayList<Object[]>();
		}

		if (tempList != null && tempList.size() > 0) {
			objectArray = getObjectArrayForInsert(tempList);
			objectArrayList.add(objectArray);
			if (objectArrayList != null && objectArrayList.size() > 0) {
				this.executeBatch(getInsertMultiRowsSqlStatement(tempList.size()), objectArrayList);
				if (isDebug == true) {
					System.out.println("UrlMetricsDataHistoryEntityDAO.insertMultiRowsBatch() objectArrayList.size()=" + objectArrayList.size());
				}
				objectArrayList = new ArrayList<Object[]>();
			}
		}
	}

	private Object[] getObjectArrayForInsert(List<UrlMetricsDataHistoryEntity> list) {
		List<Object[]> tempObjectArrayList = new ArrayList<Object[]>();
		Object[] urlMetricsDataHistory = null;
		int totalNumberOfObjects = 0;
		TargetURLCriteria targetURLCriteria = null;
		for (UrlMetricsDataHistoryEntity urlMetricsDataHistoryEntity : list) {
			
			targetURLCriteria = urlMetricsDataHistoryEntity.getTargetURLCriteria();
			if (targetURLCriteria == null) {
				targetURLCriteria = new TargetURLCriteria();
			}
			
			urlMetricsDataHistory = new Object[] { 
					urlMetricsDataHistoryEntity.getYearWeek(), 
					urlMetricsDataHistoryEntity.getId(),
					urlMetricsDataHistoryEntity.getUrl(), 
					urlMetricsDataHistoryEntity.getUrlId(), 
					urlMetricsDataHistoryEntity.getType(),
					urlMetricsDataHistoryEntity.getPagerank(), 
					urlMetricsDataHistoryEntity.getInbounds(),
					urlMetricsDataHistoryEntity.getInternalLinks(), 
					urlMetricsDataHistoryEntity.getOutbounds(),
					urlMetricsDataHistoryEntity.getCachedDate(), 
					urlMetricsDataHistoryEntity.getRespCode(),
					urlMetricsDataHistoryEntity.getIpaddress(), 
					urlMetricsDataHistoryEntity.getLastUpdate(),
					urlMetricsDataHistoryEntity.getPrUpdateDate(), 
					urlMetricsDataHistoryEntity.getYahooUpdateDate(),
					urlMetricsDataHistoryEntity.getCacheDateUpdateDate(), 
					urlMetricsDataHistoryEntity.getRespcodeUpdateDate(),
					urlMetricsDataHistoryEntity.getSemrushUpdateDate(), 
					urlMetricsDataHistoryEntity.getPagespeedUpdateDate(),
					urlMetricsDataHistoryEntity.getGoogleIndex(), 
					urlMetricsDataHistoryEntity.getSeomozUpdateDate(),
					urlMetricsDataHistoryEntity.getMozRank(), 
					urlMetricsDataHistoryEntity.getPageAuthority(),
					urlMetricsDataHistoryEntity.getMozInbounds(), 
					urlMetricsDataHistoryEntity.getRobotMeta(), 
					urlMetricsDataHistoryEntity.getLinks(),
					urlMetricsDataHistoryEntity.getDomainAuthority(), 
					urlMetricsDataHistoryEntity.getMajesticsUpdateDate(),
					urlMetricsDataHistoryEntity.getAcRank(), 
					urlMetricsDataHistoryEntity.getPercentMobileEntrances(),
					urlMetricsDataHistoryEntity.getAssociatedKeywords(), 
					urlMetricsDataHistoryEntity.getAssociatedKeywordsRanked(),
					urlMetricsDataHistoryEntity.getLinksAcquiredOrganically(), 
					urlMetricsDataHistoryEntity.getLinksAcquiredManually(),
					urlMetricsDataHistoryEntity.getAssociatedCompetitors(), 
					urlMetricsDataHistoryEntity.getFriendlyName(),
					urlMetricsDataHistoryEntity.getWeekBounces(), 
					targetURLCriteria.getId(),
					targetURLCriteria.getOwnDomainId(), 
					targetURLCriteria.getEntrancesLast7days(), 
					targetURLCriteria.getEntrancesLast30days(),
					targetURLCriteria.getEntrancesLastMonth(), 
					targetURLCriteria.getEntrancesWeekOverWeek(),
					targetURLCriteria.getEntrancesMonthOverMonth(), 
					targetURLCriteria.getPageTitle(),
					targetURLCriteria.getPageMetaKeyword(), 
					targetURLCriteria.getPageMetaDesc(), 
					targetURLCriteria.getPageH1(),
					targetURLCriteria.getPageH2(), 
					targetURLCriteria.getPageTitleLength(),
					targetURLCriteria.getPageMetaKeywordLength(), 
					targetURLCriteria.getPageMetaDescLength(),
					targetURLCriteria.getPageH1Count(), 
					targetURLCriteria.getPageH2Count(), 
					targetURLCriteria.getScore(),
					targetURLCriteria.getCountAssociatedKeyword(), 
					targetURLCriteria.getWtdAvgRank(),
					targetURLCriteria.getGoogleSearchVolume(), 
					targetURLCriteria.getMetaRobotIndex(),
					targetURLCriteria.getMetaRobotFollow(),
			};
			tempObjectArrayList.add(urlMetricsDataHistory);
		}
		totalNumberOfObjects = tempObjectArrayList.size() * NUMBER_OF_FIELDS;

		Object[] objectArray = new Object[totalNumberOfObjects];
		int idx = 0;
		for (Object[] tempObjectArray : tempObjectArrayList) {
			for (int i = 0; i < tempObjectArray.length; i++) {
				objectArray[idx++] = tempObjectArray[i];
			}
		}
		return objectArray;
	}

	private String getInsertMultiRowsSqlStatement(int recordsPerInsertStatement) {
		String response = null;
		StringBuffer stringBuffer = new StringBuffer();
		stringBuffer.append(" insert into");
		stringBuffer.append(" " + getTableName());
		stringBuffer.append(" (");
		stringBuffer.append(" year_week,");
		stringBuffer.append(" t_url_metrics_data_id,");
		stringBuffer.append(" url,");
		stringBuffer.append(" url_id,");
		stringBuffer.append(" type,");
		stringBuffer.append(" pagerank,");
		stringBuffer.append(" inbounds,");
		stringBuffer.append(" internal_links,");
		stringBuffer.append(" outbounds,");
		stringBuffer.append(" cached_date,");
		stringBuffer.append(" resp_code,");
		stringBuffer.append(" ipaddress,");
		stringBuffer.append(" last_update,");
		stringBuffer.append(" pr_update_date,");
		stringBuffer.append(" yahoo_update_date,");
		stringBuffer.append(" cachedate_update_date,");
		stringBuffer.append(" respcode_update_date,");
		stringBuffer.append(" semrush_update_date,");
		stringBuffer.append(" pagespeed_update_date,");
		stringBuffer.append(" google_index,");
		stringBuffer.append(" seomoz_update_date,");
		stringBuffer.append(" moz_rank,");
		stringBuffer.append(" page_authority,");
		stringBuffer.append(" moz_inbounds,");
		stringBuffer.append(" robot_meta,");
		stringBuffer.append(" links,");
		stringBuffer.append(" domain_authority,");
		stringBuffer.append(" majestics_update_date,");
		stringBuffer.append(" ac_rank,");
		stringBuffer.append(" percent_mobile_entrances,");
		stringBuffer.append(" associated_keywords,");
		stringBuffer.append(" associated_keywords_ranked,");
		stringBuffer.append(" links_acquired_organically,");
		stringBuffer.append(" links_acquired_manually,");
		stringBuffer.append(" associated_competitors,");
		stringBuffer.append(" friendly_name,");
		stringBuffer.append(" week_bounces,");
		stringBuffer.append(" targeturl_criteria_id,");
		stringBuffer.append(" own_domain_id,");
		stringBuffer.append(" entrances_last7days,");
		stringBuffer.append(" entrances_last30days,");
		stringBuffer.append(" entrances_last_month,");
		stringBuffer.append(" entrances_week_over_week,");
		stringBuffer.append(" entrances_month_over_month,");
		stringBuffer.append(" page_title,");
		stringBuffer.append(" page_meta_keyword,");
		stringBuffer.append(" page_meta_desc,");
		stringBuffer.append(" page_h1,");
		stringBuffer.append(" page_h2,");
		stringBuffer.append(" page_title_length,");
		stringBuffer.append(" page_meta_keyword_length,");
		stringBuffer.append(" page_meta_desc_length,");
		stringBuffer.append(" page_h1_count,");
		stringBuffer.append(" page_h2_count,");
		stringBuffer.append(" score,");
		stringBuffer.append(" count_associated_keyword,");
		stringBuffer.append(" wtd_avg_rank,");
		stringBuffer.append(" total_search_volume_google,");
		stringBuffer.append(" meta_robot_index,");
		stringBuffer.append(" meta_robot_follow");
		stringBuffer.append(" )");
		stringBuffer.append(" values ");
		String sqlString = stringBuffer.toString();
		StringBuffer sql = new StringBuffer();
		sql.append(sqlString);
		for (int i = 0; i < recordsPerInsertStatement; i++) {
			if (i == 0) {
				sql.append(INSERT_VALUE_FORMAT_1);
			} else {
				sql.append(INSERT_VALUE_FORMAT_2);
			}
		}
		sql.append(SEMI_COLON);
		response = sql.toString();
		return response;
	}

	public void reset(int domainId, int yearWeek) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" delete from t_url_metrics_data_hist");
		stringBuilder.append(" where");
		stringBuilder.append("     own_domain_id = ?");
		stringBuilder.append(" and year_week = ?");
		String sqlString = stringBuilder.toString();
		this.executeUpdate(sqlString, domainId, yearWeek);
	}
	
	public void reset(int domainId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" delete from t_url_metrics_data_hist");
		stringBuilder.append(" where");
		stringBuilder.append("     own_domain_id = ?");
		String sqlString = stringBuilder.toString();
		this.executeUpdate(sqlString, domainId);
	}
	
	public List<UrlMetricsDataHistoryEntity> getUniqueDomainIdYearWeekList() {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     t_url_metrics_data_hist.own_domain_id as domainId,");
		stringBuilder.append("     t_url_metrics_data_hist.year_week");
		stringBuilder.append(" from");
		stringBuilder.append("     t_url_metrics_data_hist t_url_metrics_data_hist");
		stringBuilder.append(" group by");
		stringBuilder.append("     t_url_metrics_data_hist.own_domain_id,");
		stringBuilder.append("     t_url_metrics_data_hist.year_week");
		stringBuilder.append(" order by");
		stringBuilder.append("     t_url_metrics_data_hist.own_domain_id,");
		stringBuilder.append("     t_url_metrics_data_hist.year_week desc");
		String sqlString = stringBuilder.toString();
		return this.findBySql(sqlString);
	}
	
	public List<UrlMetricsDataHistoryEntity> getYearWeekList(int domainId, int numberOfWeeksToBeProcessed) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     t_url_metrics_data_hist.year_week");
		stringBuilder.append(" from");
		stringBuilder.append("     t_url_metrics_data_hist t_url_metrics_data_hist");
		stringBuilder.append(" where");
		stringBuilder.append("     t_url_metrics_data_hist.own_domain_id = ?");
		stringBuilder.append(" group by");
		stringBuilder.append("     t_url_metrics_data_hist.year_week");
		stringBuilder.append(" order by");
		stringBuilder.append("     t_url_metrics_data_hist.year_week desc");
		stringBuilder.append(" limit " + numberOfWeeksToBeProcessed);
		String sqlString = stringBuilder.toString();
		return this.findBySql(sqlString, domainId);
	}
	
	public List<UrlMetricsDataHistoryEntity> getByDomainIdYearWeek(int domainId, int yearWeek) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" select");
		stringBuilder.append("     t_url_metrics_data_hist.url,");
		stringBuilder.append("     t_url_metrics_data_hist.url_id,");
		stringBuilder.append("     t_url_metrics_data_hist.entrances_last7days as week_entrances,");
		stringBuilder.append("     t_url_metrics_data_hist.week_bounces");
		stringBuilder.append(" from");
		stringBuilder.append("     t_url_metrics_data_hist t_url_metrics_data_hist");
		stringBuilder.append(" where");
		stringBuilder.append("     t_url_metrics_data_hist.own_domain_id = ?");
		stringBuilder.append(" and t_url_metrics_data_hist.year_week = ?");
		String sqlString = stringBuilder.toString();
		return this.findBySql(sqlString, domainId, yearWeek);
	}
	
	public void updateEntrancesBounces(int domainId, int yearWeek, Long targetUrlId, Integer totalEntrances, Integer totalBounces) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" update t_url_metrics_data_hist");
		stringBuilder.append(" set entrances_last7days = ?,");
		stringBuilder.append("     week_bounces = ?");
		stringBuilder.append(" where");
		stringBuilder.append("     own_domain_id = ?");
		stringBuilder.append(" and year_week = ?");
		stringBuilder.append(" and url_id = ?");
		String sqlString = stringBuilder.toString();
		this.executeUpdate(sqlString, totalEntrances, totalBounces, domainId, yearWeek, targetUrlId);
	}

}
