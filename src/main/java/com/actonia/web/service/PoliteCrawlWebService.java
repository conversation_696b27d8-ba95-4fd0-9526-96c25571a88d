package com.actonia.web.service;

import org.restlet.Component;
import org.restlet.data.Protocol;

import com.actonia.IConstants;
import com.actonia.dao.ContentGuardClickHouseDAO;
import com.actonia.dao.TargetUrlChangeIndClickHouseDAO;
import com.actonia.utils.ContentGuardUtils;
import com.actonia.utils.TargetUrlChangeUtils;

public class PoliteCrawlWebService {

	public static void main(String[] args) throws Exception {

		ContentGuardClickHouseDAO.getInstance();
		ContentGuardUtils.getInstance();
		TargetUrlChangeIndClickHouseDAO.getInstance();
		TargetUrlChangeUtils.getInstance();

		// Create a new Component.
		Component component = new Component();

		component.getServers().add(Protocol.HTTP, IConstants.WEB_SERVICE_PORT_NUMBER); // production setting port 16819

		component.getServers().getContext().getParameters().add("maxThreads", "300");
		component.getServers().getContext().getParameters().add("maxTotalConnections", "100000");

		component.getContext().getParameters().add("maxThreads", "1512000");
		component.getContext().getParameters().add("maxTotalConnections", "100000");

		component.getDefaultHost().attach("/politeCrawlService", new PoliteCrawlApplication());

		// Start the component.
		component.start();

	}
}
