package com.actonia.web.service;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.text.MessageFormat;

import org.apache.commons.lang.StringUtils;
import org.restlet.ext.jackson.JacksonRepresentation;
import org.restlet.representation.Representation;
import org.restlet.resource.Post;
import org.restlet.resource.ServerResource;

import com.actonia.IConstants;
import com.actonia.service.SerpAnalyzerApiService;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.SerpAnalyzerResourceRequest;
import com.actonia.value.object.SerpAnalyzerResourceResponse;
import com.actonia.value.object.ValidationError;
import com.actonia.value.object.WebServiceError;
import com.google.gson.Gson;

public class SerpAnalyzerResource extends ServerResource {

	private boolean isDebug = true; //debug

	private SerpAnalyzerApiService serpAnalyzerApiService;

	private static final String ERROR_MSG_NOT_AVAILABLE = "Error message not available.";

	// error codes and messages
	private static final String MSG_CD_WEB_SERVICE_METHOD_EXCEPTION = "0001";
	private static final String MSG_CD_REQUEST_JSON_REQUIRED = "0002";
	private static final String MSG_CD_REQUEST_PARM_ACCESS_TOKEN_REQUIRED = "0003";
	private static final String MSG_CD_REQUEST_PARM_ACCESS_TOKEN_INVALID = "0004";
	private static final String MSG_CD_REQUEST_PARM_JSON_REQUIRED = "0005";

	private static final String[] ERROR_CODE_ARRAY = new String[] { "0001", "0002", "0003", "0004", "0005", };

	private static final String[] ERROR_MESSAGE_TEMPLATE_ARRAY = new String[] { "Web service exception: {0}", "Request JSON required.",
			"Request parameter access_token required.", "Request parameter access_token invalid.", "Request parameter json required.", };

	public SerpAnalyzerResource() {
		super();
		this.serpAnalyzerApiService = SpringBeanFactory.getBean("serpAnalyzerApiService");
	}

	@Post("application/json")
	public Representation doPost(Representation representationInput) {
		Representation representationOutput = null;
		String requestParameters = null;
		SerpAnalyzerResourceRequest serpAnalyzerResourceRequest = null;
		String accessToken = null;
		SerpAnalyzerResourceResponse serpAnalyzerResourceResponse = null;

		try {
			if (representationInput == null) {
				return generateJsonError(MSG_CD_REQUEST_JSON_REQUIRED);
			}
			requestParameters = FormatUtils.getInstance().removeCarriageReturnNewLine(representationInput.getText());
			FormatUtils.getInstance().logMemoryUsage("doPost() requestParameters=" + requestParameters);
			if (StringUtils.isBlank(requestParameters)) {
				return generateJsonError(MSG_CD_REQUEST_JSON_REQUIRED);
			}
			serpAnalyzerResourceRequest = new Gson().fromJson(requestParameters, SerpAnalyzerResourceRequest.class);
			accessToken = serpAnalyzerResourceRequest.getAccess_token();

			// validate input access token
			if (StringUtils.isBlank(accessToken)) {
				FormatUtils.getInstance().logMemoryUsage("doPost() error--access token is not available in request, accessToken=" + accessToken);
				return generateJsonError(MSG_CD_REQUEST_PARM_ACCESS_TOKEN_REQUIRED);
			} else if (StringUtils.equalsIgnoreCase(accessToken, IConstants.INTERNAL_KEY) == false) {
				return generateJsonError(MSG_CD_REQUEST_PARM_ACCESS_TOKEN_INVALID, accessToken);
			}

			representationOutput = validateRequest(serpAnalyzerResourceRequest);
			// when validation failed, return the error JSON representation
			if (representationOutput != null) {
				return representationOutput;
			}

			serpAnalyzerResourceResponse = getSerpAnalyzerResourceResponse(serpAnalyzerResourceRequest);

			return new JacksonRepresentation<SerpAnalyzerResourceResponse>(serpAnalyzerResourceResponse);

		} catch (Exception e) {
			e.printStackTrace();
			StringWriter stringWriter = new StringWriter();
			e.printStackTrace(new PrintWriter(stringWriter));
			return generateJsonError(MSG_CD_WEB_SERVICE_METHOD_EXCEPTION, stringWriter.toString());
		}
	}

	private SerpAnalyzerResourceResponse getSerpAnalyzerResourceResponse(SerpAnalyzerResourceRequest serpAnalyzerResourceRequest) throws Exception {
		SerpAnalyzerResourceResponse serpAnalyzerResourceResponse = new SerpAnalyzerResourceResponse();
		serpAnalyzerResourceResponse.setSuccess(true);
		String requestUrl = serpAnalyzerApiService.getEndPoint();
		String responseJson = serpAnalyzerApiService.sendSerpAnalyzerRequest(requestUrl, serpAnalyzerResourceRequest.getJson());
		serpAnalyzerResourceResponse.setJson(responseJson);
		return serpAnalyzerResourceResponse;
	}

	private Representation validateRequest(SerpAnalyzerResourceRequest serpAnalyzerResourceRequest) throws Exception {
		Representation representation = null;
		SerpAnalyzerResourceResponse serpAnalyzerResourceResponse = null;
		WebServiceError webServiceError = null;
		ValidationError validationError = getValidationErrorCode(serpAnalyzerResourceRequest);
		if (validationError != null) {
			serpAnalyzerResourceResponse = new SerpAnalyzerResourceResponse();
			webServiceError = new WebServiceError();
			webServiceError.setError_code(validationError.getErrorCode());
			webServiceError.setError_message(getErrorMessage(validationError.getErrorCode(), validationError.getErrorData()));
			serpAnalyzerResourceResponse.setError(webServiceError);
			serpAnalyzerResourceResponse.setSuccess(false);
			representation = new JacksonRepresentation<SerpAnalyzerResourceResponse>(serpAnalyzerResourceResponse);
		}
		return representation;
	}

	private ValidationError getValidationErrorCode(SerpAnalyzerResourceRequest serpAnalyzerResourceRequest) throws Exception {
		ValidationError validationError = null;
		if (StringUtils.isBlank(serpAnalyzerResourceRequest.getJson())) {
			return getValidationError(MSG_CD_REQUEST_PARM_JSON_REQUIRED);
		}

		return validationError;
	}

	private ValidationError getValidationError(String errorCode) {
		return getValidationError(errorCode, null);
	}

	private ValidationError getValidationError(String errorCode, String errorData) {
		ValidationError validationError = new ValidationError();
		validationError.setErrorCode(errorCode);
		validationError.setErrorData(errorData);
		return validationError;
	}

	public Representation generateJsonError(String errorCode) {
		return generateJsonError(errorCode, null);
	}

	public Representation generateJsonError(String errorCode, String supplementalMessageText) {
		FormatUtils.getInstance().logMemoryUsage("generateJsonError() errorCode=" + errorCode + ",supplementalMessageText=" + supplementalMessageText);
		String errorMessage = getErrorMessage(errorCode, supplementalMessageText);
		SerpAnalyzerResourceResponse serpAnalyzerResourceResponse = new SerpAnalyzerResourceResponse();
		WebServiceError webServiceError = new WebServiceError();
		webServiceError.setError_code(errorCode);
		webServiceError.setError_message(errorMessage);
		serpAnalyzerResourceResponse.setError(webServiceError);
		serpAnalyzerResourceResponse.setSuccess(false);
		return new JacksonRepresentation<SerpAnalyzerResourceResponse>(serpAnalyzerResourceResponse);
	}

	private String getErrorMessage(String errorCodeInput, String supplementalMessageText) {
		String errorMessage = null;
		String errorTemplate = null;
		for (int i = 0; i < ERROR_CODE_ARRAY.length; i++) {
			if (StringUtils.equalsIgnoreCase(ERROR_CODE_ARRAY[i], errorCodeInput)) {
				errorTemplate = ERROR_MESSAGE_TEMPLATE_ARRAY[i];
			}
		}
		if (StringUtils.isNotBlank(supplementalMessageText)) {
			if (StringUtils.isNotBlank(errorTemplate)) {
				errorMessage = MessageFormat.format(errorTemplate, supplementalMessageText);
			}
		} else {
			errorMessage = errorTemplate;
		}
		if (StringUtils.isBlank(errorMessage)) {
			errorMessage = ERROR_MSG_NOT_AVAILABLE;
		}
		return errorMessage;
	}
}
