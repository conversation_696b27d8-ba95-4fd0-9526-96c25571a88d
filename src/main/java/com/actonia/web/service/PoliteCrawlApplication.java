package com.actonia.web.service;

import org.restlet.Application;
import org.restlet.Request;
import org.restlet.Response;
import org.restlet.Restlet;
import org.restlet.data.MediaType;
import org.restlet.representation.StringRepresentation;

public class PoliteCrawlApplication extends Application {

	@Override
	public synchronized Restlet createInboundRoot() {
		PoliteCrawlRouter router = new PoliteCrawlRouter(getContext());

		getContext();

		// MarketMatching
		router.attach("/market_matching", MarketMatchingResource.class);

		router.attach("/sitehealthsummary/{command}", ExcelAddinsResource.class);

		// Prophet
		router.attach("/prophet", ProphetResource.class);

		// Causal Impact
		router.attach("/causal_impact/{command}", CausalImpactResource.class);

		// Content Guard
		router.attach("/content_guard/{command}", ContentGuardResource.class);

		// find the differences between two target URLs stored in ClickHouse database
		router.attach("/findTargetUrlsDifferences", FindTargetUrlsDifferencesResource.class);

		// Zapier
		router.attach("/zapier/{command}", ZapierResource.class);

		// SERP Analyzer
		router.attach("/serp_analyzer", SerpAnalyzerResource.class);

		// target URL change
		router.attach("/target_url_change/{command}", TargetUrlChangeResource.class);

		Restlet mainpage = new Restlet() {
			@Override
			public void handle(Request request, Response response) {
				StringBuilder stringBuilder = new StringBuilder();

				stringBuilder.append("<html>");
				stringBuilder.append("<head><title>Polite Crawl Web Service</title></head>");
				stringBuilder.append("<body bgcolor=white>");

				stringBuilder.append("<table border=\"0\">");
				stringBuilder.append("<tr>");
				stringBuilder.append("<td>");
				stringBuilder.append("<h1>Welcome to the Polite Crawl Web Service User Guide</h1>");
				stringBuilder.append("</td>");
				stringBuilder.append("</tr>");
				stringBuilder.append("</table>");
				stringBuilder.append("Click <a href=\"../politeCrawlClarity/help\">Getting Started</a> to begin");
				stringBuilder.append("</body>");
				stringBuilder.append("</html>");

				response.setEntity(new StringRepresentation(stringBuilder.toString(), MediaType.TEXT_HTML));

			}
		};

		router.attach("", mainpage);
		router.attach("/", mainpage);

		return router;
	}
}
