package com.actonia.web.service;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.restlet.ext.jackson.JacksonRepresentation;
import org.restlet.representation.Representation;
import org.restlet.resource.Post;
import org.restlet.resource.ResourceException;

import com.actonia.IConstants;
import com.actonia.service.AccessTokenService;
import com.actonia.utils.CommonUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.RUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.CausalImpactRequest;
import com.actonia.value.object.CausalImpactResponse;
import com.actonia.value.object.RRequest;
import com.actonia.value.object.RResponse;
import com.actonia.value.object.ValidationError;
import com.actonia.value.object.WebServiceError;
import com.google.gson.Gson;

public class CausalImpactResource extends BaseServerResouce {

	private boolean isDebug = false;

	private String command;

	private AccessTokenService accessTokenService;

	private static final String[] VALID_COMMAND_ARRAY = new String[] { IConstants.COMMAND_CALCULATE_CORRELATION_COEFFICIENT, IConstants.COMMAND_ANALYZE,
			IConstants.COMMAND_ANALYZE_WITHOUT_CONTROL_SERIES, };

	public CausalImpactResource() {
		super();
		accessTokenService = SpringBeanFactory.getBean("accessTokenService");
	}

	@Override
	protected void doInit() throws ResourceException {
		this.command = (String) getRequest().getAttributes().get("command");
	}

	@Post("application/json")
	public Representation doPost(Representation representationInput) {
		long startTimestamp = System.currentTimeMillis();
		Representation representationOutput = null;
		String requestParameters = null;
		RRequest rRequest = null;
		RResponse rResponse = null;
		CausalImpactRequest causalImpactRequest = null;
		CausalImpactResponse causalImpactResponse = null;
		String accessToken = null;
		boolean isCommandValid = false;
		String accessTokenValidationErrorCode = null;

		try {
			isCommandValid = validateCommand();
			if (isCommandValid == false) {
				return generateJsonError(IConstants.MSG_CD_CAUSAL_IMPACT_REQUEST_COMMAND_INVALID, command);
			}

			if (representationInput == null) {
				return generateJsonError(IConstants.MSG_CD_CAUSAL_IMPACT_REQUEST_JSON_REQUIRED);
			}

			requestParameters = FormatUtils.getInstance().removeCarriageReturnNewLine(representationInput.getText());
			FormatUtils.getInstance().logMemoryUsage("doPost() command=" + command + ",requestParameters=" + requestParameters);
			if (StringUtils.isBlank(requestParameters)) {
				return generateJsonError(IConstants.MSG_CD_CAUSAL_IMPACT_REQUEST_JSON_REQUIRED);
			}

			causalImpactRequest = new Gson().fromJson(requestParameters, CausalImpactRequest.class);
			if (isDebug == true) {
				FormatUtils.getInstance().logMemoryUsage("doPost() causalImpactRequest=" + causalImpactRequest.toString());
			}

			accessToken = causalImpactRequest.getAccess_token();
			if (StringUtils.isBlank(accessToken)) {
				return generateJsonError(IConstants.MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_ACCESS_TOKEN_REQUIRED);
			} else if (StringUtils.equalsIgnoreCase(accessToken, AccessTokenService.INTERNAL_KEY) == false) {
				accessTokenValidationErrorCode = accessTokenService.checkToken(accessToken);
				if (StringUtils.isNotBlank(accessTokenValidationErrorCode)) {
					return generateJsonError(IConstants.MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_ACCESS_TOKEN_INVALID, accessToken);
				}
			}

			representationOutput = validateRequest(causalImpactRequest);
			// when validation failed, return the error JSON representation
			if (representationOutput != null) {
				return representationOutput;
			}

			causalImpactResponse = new CausalImpactResponse();

			rRequest = new RRequest();
			rRequest.setrPackage(IConstants.R_PACKAGE_CAUSAL_IMPACT);
			rRequest.setCausalImpactRequest(causalImpactRequest);
			rRequest.setCommand(command);

			// command:calculate_correlation_coefficient
			if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_CALCULATE_CORRELATION_COEFFICIENT)) {
				rResponse = RUtils.getInstance().invokeR(rRequest);
				if (rResponse != null) {
					causalImpactResponse = rResponse.getCausalImpactResponse();
				}
			}
			// command:analyze
			else if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_ANALYZE)
					|| StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_ANALYZE_WITHOUT_CONTROL_SERIES)) {
				rResponse = RUtils.getInstance().invokeR(rRequest);
				if (rResponse != null) {
					causalImpactResponse = rResponse.getCausalImpactResponse();
				}
			}
			return new JacksonRepresentation<CausalImpactResponse>(causalImpactResponse);
		} catch (Exception e) {
			e.printStackTrace();
			StringWriter stringWriter = new StringWriter();
			e.printStackTrace(new PrintWriter(stringWriter));

			// send support alert
			String message = stringWriter.toString();
			CommonUtils.sendCausalImpactAlert(message, requestParameters);
			return generateJsonError(IConstants.MSG_CD_CAUSAL_IMPACT_WEB_SERVICE_METHOD_EXCEPTION, message);
		} finally {
			if (causalImpactResponse != null) {
				if (isDebug == true) {
					FormatUtils.getInstance().logMemoryUsage("doPost() ends. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp) + ",causalImpactResponse="
							+ causalImpactResponse.toString());
				} else {
					FormatUtils.getInstance().logMemoryUsage("doPost() ends. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp)
							+ ",causalImpactResponse.getSuccess()=" + causalImpactResponse.getSuccess());
				}
			}
		}
	}

	private boolean validateCommand() {
		boolean output = false;
		if (StringUtils.isNotBlank(command)) {
			nextValidCommand: for (String validCommand : VALID_COMMAND_ARRAY) {
				if (StringUtils.equalsIgnoreCase(command, validCommand)) {
					output = true;
					break nextValidCommand;
				}
			}
		}
		return output;
	}

	private Representation validateRequest(CausalImpactRequest causalImpactRequest) throws Exception {
		Representation representation = null;
		CausalImpactResponse causalImpactResponse = null;
		WebServiceError webServiceError = null;

		ValidationError validationError = getValidationErrorCode(causalImpactRequest);

		if (validationError != null) {
			causalImpactResponse = new CausalImpactResponse();
			webServiceError = new WebServiceError();
			webServiceError.setError_code(validationError.getErrorCode());
			webServiceError.setError_message(getErrorMessage(validationError.getErrorCode(), validationError.getErrorData()));
			causalImpactResponse.setError(webServiceError);
			causalImpactResponse.setSuccess(false);
			FormatUtils.getInstance().logMemoryUsage("validateRequest() causalImpactResponse=" + new Gson().toJson(causalImpactResponse, CausalImpactResponse.class));
			representation = new JacksonRepresentation<CausalImpactResponse>(causalImpactResponse);
		}

		return representation;
	}

	private ValidationError getValidationErrorCode(CausalImpactRequest causalImpactRequest) throws Exception {
		ValidationError validationError = null;

		// command:analyze
		if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_ANALYZE)) {
			// 'pre_period_start_date' is required
			if (StringUtils.isBlank(causalImpactRequest.getPre_period_start_date())) {
				return getValidationError(IConstants.MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_PRE_PERIOD_START_DATE_REQUIRED);
			}
			// 'pre_period_end_date' is required
			else if (StringUtils.isBlank(causalImpactRequest.getPre_period_end_date())) {
				return getValidationError(IConstants.MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_PRE_PERIOD_END_DATE_REQUIRED);
			}
			// 'post_period_start_date' is required
			else if (StringUtils.isBlank(causalImpactRequest.getPost_period_start_date())) {
				return getValidationError(IConstants.MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_POST_PERIOD_START_DATE_REQUIRED);
			}
			// 'post_period_end_date' is required
			else if (StringUtils.isBlank(causalImpactRequest.getPost_period_end_date())) {
				return getValidationError(IConstants.MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_POST_PERIOD_END_DATE_REQUIRED);
			}
			// 'pre_period_start_date' is invalid
			else if (isDateValid(causalImpactRequest.getPre_period_start_date()) == false) {
				return getValidationError(IConstants.MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_PRE_PERIOD_START_DATE_INVALID, causalImpactRequest.getPre_period_start_date());
			}
			// 'pre_period_end_date' is invalid
			else if (isDateValid(causalImpactRequest.getPre_period_end_date()) == false) {
				return getValidationError(IConstants.MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_PRE_PERIOD_END_DATE_INVALID, causalImpactRequest.getPre_period_end_date());
			}
			// 'post_period_start_date' is invalid
			else if (isDateValid(causalImpactRequest.getPost_period_start_date()) == false) {
				return getValidationError(IConstants.MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_POST_PERIOD_START_DATE_INVALID, causalImpactRequest.getPost_period_start_date());
			}
			// 'post_period_end_date' is invalid
			else if (isDateValid(causalImpactRequest.getPost_period_end_date()) == false) {
				return getValidationError(IConstants.MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_POST_PERIOD_END_DATE_INVALID, causalImpactRequest.getPost_period_end_date());
			}
			// 'pre_period_start_date' and 'pre_period_end_date' combination is invalid
			else if (areDatesValid(causalImpactRequest.getPre_period_start_date(), causalImpactRequest.getPre_period_end_date()) == false) {
				return getValidationError(IConstants.MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_PRE_PERIOD_START_END_DATE_INVALID);
			}
			// 'post_period_start_date' and 'post_period_end_date' combination is invalid
			else if (areDatesValid(causalImpactRequest.getPost_period_start_date(), causalImpactRequest.getPost_period_end_date()) == false) {
				return getValidationError(IConstants.MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_POST_PERIOD_START_END_DATE_INVALID);
			}
			// total number of days between pre-period start and post-period end must equal 
			// total number of days between pre-period start and end +
			// total number of days between post-period start and end
			// # https://www.wrike.com/open.htm?id=1220015502
//			else if (isPreAndPostDatesConsistent(causalImpactRequest) == false) {
//				return getValidationError(IConstants.MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_PRE_AND_POST_PERIOD_DATES_INCONSISTENT);
//			}
		}

		// command:analyze
		// command:calculate_correlation_coefficient
		if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_CALCULATE_CORRELATION_COEFFICIENT)
				|| StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_ANALYZE)) {
			// 'test_time_series' is required
			if (causalImpactRequest.getTest_time_series() == null || causalImpactRequest.getTest_time_series().length == 0) {
				return getValidationError(IConstants.MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_TEST_DATA_POINTS_REQUIRED);
			}
			// 'control_name_list' is required
			else if (causalImpactRequest.getControl_name_list() == null || causalImpactRequest.getControl_name_list().size() == 0) {
				return getValidationError(IConstants.MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_CONTROL_NAME_LIST_REQUIRED);
			}
			// control names cannot be blank
			else if (isBlank(causalImpactRequest.getControl_name_list()) == true) {
				return getValidationError(IConstants.MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_CONTROL_NAMES_CANNOT_BE_BLANK);
			}
			// control names must be unique
			else if (containsDuplicateEntries(causalImpactRequest.getControl_name_list()) == true) {
				return getValidationError(IConstants.MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_CONTROL_NAMES_MUST_BE_UNIQUE);
			}
			// 'control_time_series_list' is required
			else if (causalImpactRequest.getControl_time_series_list() == null || causalImpactRequest.getControl_time_series_list().size() == 0) {
				return getValidationError(IConstants.MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_CONTROL_DATA_POINTS_LIST_REQUIRED);
			}
			// total number of control names must equal total number of control time series
			else if (causalImpactRequest.getControl_name_list().size() != causalImpactRequest.getControl_time_series_list().size()) {
				return getValidationError(IConstants.MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_CONTROL_NAMES_INCONSISTENT);
			}
			// total number of data points must be consistent
			else if (areDataPointsInconsistent(causalImpactRequest) == true) {
				return getValidationError(IConstants.MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_DATA_POINTS_INCONSISTENT);
			}
		}

		// command:analyze
		if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_ANALYZE)) {
			// Days between pre-period start and post-period end must equal total number of test data points.
			// # https://www.wrike.com/open.htm?id=1220015502
//			if (isTestDataPointsAndPrePostDatesConsistent(causalImpactRequest) == false) {
//				return getValidationError(IConstants.MSG_CD_CAUSAL_IMPACT_REQUEST_PARM_TEST_DATA_POINTS_PRE_AND_POST_PERIOD_DATES_INCONSISTENT);
//			}
		}

		return validationError;
	}

	private boolean isPreAndPostDatesConsistent(CausalImpactRequest causalImpactRequest) {
		boolean output = false;
		long totalPreAndPostPeriodDays = ChronoUnit.DAYS.between(LocalDate.parse(causalImpactRequest.getPre_period_start_date()),
				LocalDate.parse(causalImpactRequest.getPost_period_end_date())) + IConstants.LONG_ONE;
		long totalPrePeriodDays = ChronoUnit.DAYS.between(LocalDate.parse(causalImpactRequest.getPre_period_start_date()),
				LocalDate.parse(causalImpactRequest.getPre_period_end_date())) + IConstants.LONG_ONE;
		long totalPostPeriodDays = ChronoUnit.DAYS.between(LocalDate.parse(causalImpactRequest.getPost_period_start_date()),
				LocalDate.parse(causalImpactRequest.getPost_period_end_date())) + IConstants.LONG_ONE;
		if (totalPreAndPostPeriodDays == (totalPrePeriodDays + totalPostPeriodDays)) {
			output = true;
		}
		return output;
	}

	private boolean isTestDataPointsAndPrePostDatesConsistent(CausalImpactRequest causalImpactRequest) {
		boolean output = false;
		long totalPreAndPostPeriodDaysLong = ChronoUnit.DAYS.between(LocalDate.parse(causalImpactRequest.getPre_period_start_date()),
				LocalDate.parse(causalImpactRequest.getPost_period_end_date())) + IConstants.LONG_ONE;
		Long testLong = new Long(totalPreAndPostPeriodDaysLong);
		int totalPreAndPostPeriodDays = testLong.intValue();
		int totalTestDatePoints = causalImpactRequest.getTest_time_series().length;
		if (totalPreAndPostPeriodDays == totalTestDatePoints) {
			output = true;
		}
		return output;
	}

	private boolean isBlank(List<String> inputStringList) {
		boolean output = false;
		nextInputString: for (String inputString : inputStringList) {
			if (StringUtils.isBlank(inputString)) {
				output = true;
				break nextInputString;
			}
		}
		return output;
	}

	private boolean containsDuplicateEntries(List<String> inputStringList) {
		boolean output = false;
		Set<String> uniqueStringSet = new HashSet<String>(inputStringList);
		if (inputStringList.size() != uniqueStringSet.size()) {
			output = true;
		}
		return output;
	}

	private boolean areDataPointsInconsistent(CausalImpactRequest causalImpactRequest) {
		boolean output = false;
		int totalTestDatePoints = causalImpactRequest.getTest_time_series().length;
		nextDataPoints: for (double[] dataPoints : causalImpactRequest.getControl_time_series_list()) {
			if (dataPoints.length != totalTestDatePoints) {
				output = true;
				break nextDataPoints;
			}
		}
		return output;
	}

	private boolean isDateValid(String inputDateString) {
		boolean output = false;
		try {
			DateUtils.parseDateStrictly(inputDateString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
			output = true;
		} catch (Exception e) {
			output = false;
		}
		return output;
	}

	private boolean areDatesValid(String startDateString, String endDateString) {
		boolean output = false;
		Date startDate = null;
		Date endDate = null;
		try {
			startDate = DateUtils.parseDateStrictly(startDateString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
			endDate = DateUtils.parseDateStrictly(endDateString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
			if (endDate.before(startDate)) {
				output = false;
			} else {
				output = true;
			}
		} catch (Exception e) {
			output = false;
		}
		return output;
	}

	private ValidationError getValidationError(String errorCode) {
		return getValidationError(errorCode, null);
	}

	private ValidationError getValidationError(String errorCode, String errorData) {
		ValidationError validationError = new ValidationError();
		validationError.setErrorCode(errorCode);
		validationError.setErrorData(errorData);
		return validationError;
	}

	private Representation generateJsonError(String errorCode) {
		return generateJsonError(errorCode, null);
	}

	private Representation generateJsonError(String errorCode, String supplementalMessageText) {
		String errorMessage = getErrorMessage(errorCode, supplementalMessageText);
		CausalImpactResponse causalImpactResponse = new CausalImpactResponse();
		causalImpactResponse.setSuccess(false);
		WebServiceError webServiceError = new WebServiceError();
		webServiceError.setError_code(errorCode);
		webServiceError.setError_message(errorMessage);
		causalImpactResponse.setError(webServiceError);
		return new JacksonRepresentation<CausalImpactResponse>(causalImpactResponse);
	}

	private String getErrorMessage(String errorCode, String supplementalMessageText) {
		String errorMessage = null;
		String errorTemplate = CausalImpactWebServiceMessage.getStringProperty(errorCode);
		if (StringUtils.isNotBlank(supplementalMessageText)) {
			errorMessage = MessageFormat.format(errorTemplate, supplementalMessageText);
		} else {
			errorMessage = errorTemplate;
		}
		return errorMessage;
	}
}
