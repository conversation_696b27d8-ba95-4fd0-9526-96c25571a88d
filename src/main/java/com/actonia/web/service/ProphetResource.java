package com.actonia.web.service;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.text.MessageFormat;
import java.util.Date;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.restlet.ext.jackson.JacksonRepresentation;
import org.restlet.representation.Representation;
import org.restlet.resource.Post;

import com.actonia.IConstants;
import com.actonia.service.AccessTokenService;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.RUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.ProphetRequest;
import com.actonia.value.object.ProphetResponse;
import com.actonia.value.object.RRequest;
import com.actonia.value.object.RResponse;
import com.actonia.value.object.ValidationError;
import com.actonia.value.object.WebServiceError;
import com.google.gson.Gson;

public class ProphetResource extends BaseServerResouce {

	private boolean isDebug = false;

	private AccessTokenService accessTokenService;

	public ProphetResource() {
		super();
		accessTokenService = SpringBeanFactory.getBean("accessTokenService");
	}

	@Post("application/json")
	public Representation doPost(Representation representationInput) {
		long startTimestamp = System.currentTimeMillis();
		Representation representationOutput = null;
		String requestParameters = null;
		RRequest rRequest = null;
		RResponse rResponse = null;
		ProphetRequest prophetRequest = null;
		ProphetResponse prophetResponse = null;
		String accessToken = null;
		String accessTokenValidationErrorCode = null;

		try {

			if (representationInput == null) {
				return generateJsonError(IConstants.MSG_CD_PROPHET_REQUEST_JSON_REQUIRED);
			}

			requestParameters = FormatUtils.getInstance().removeCarriageReturnNewLine(representationInput.getText());
			FormatUtils.getInstance().logMemoryUsage("doPost() requestParameters=" + requestParameters);
			if (StringUtils.isBlank(requestParameters)) {
				return generateJsonError(IConstants.MSG_CD_PROPHET_REQUEST_JSON_REQUIRED);
			}

			prophetRequest = new Gson().fromJson(requestParameters, ProphetRequest.class);
			if (isDebug == true) {
				FormatUtils.getInstance().logMemoryUsage("doPost() prophetRequest=" + prophetRequest.toString());
			}

			accessToken = prophetRequest.getAccess_token();
			if (StringUtils.isBlank(accessToken)) {
				return generateJsonError(IConstants.MSG_CD_PROPHET_REQUEST_PARM_ACCESS_TOKEN_REQUIRED);
			} else if (StringUtils.equalsIgnoreCase(accessToken, AccessTokenService.INTERNAL_KEY) == false) {
				accessTokenValidationErrorCode = accessTokenService.checkToken(accessToken);
				if (StringUtils.isNotBlank(accessTokenValidationErrorCode)) {
					return generateJsonError(IConstants.MSG_CD_PROPHET_REQUEST_PARM_ACCESS_TOKEN_INVALID, accessToken);
				}
			}

			representationOutput = validateRequest(prophetRequest);
			// when validation failed, return the error JSON representation
			if (representationOutput != null) {
				return representationOutput;
			}

			rRequest = new RRequest();
			rRequest.setrPackage(IConstants.R_PACKAGE_PROPHET);
			rRequest.setProphetRequest(prophetRequest);
			rResponse = RUtils.getInstance().invokeR(rRequest);
			if (rResponse != null) {
				prophetResponse = rResponse.getProphetResponse();
			}
			return new JacksonRepresentation<ProphetResponse>(prophetResponse);
		} catch (Exception e) {
			e.printStackTrace();
			StringWriter stringWriter = new StringWriter();
			e.printStackTrace(new PrintWriter(stringWriter));

			// send support alert
			String message = stringWriter.toString();
			return generateJsonError(IConstants.MSG_CD_PROPHET_WEB_SERVICE_METHOD_EXCEPTION, message);
		} finally {
			if (prophetResponse != null) {
				if (isDebug == true) {
					FormatUtils.getInstance().logMemoryUsage(
							"doPost() ends. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp) + ",prophetResponse=" + prophetResponse.toString());
				} else {
					FormatUtils.getInstance().logMemoryUsage("doPost() ends. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp)
							+ ",prophetResponse.getSuccess()=" + prophetResponse.getSuccess());
				}
			}
		}
	}

	private Representation validateRequest(ProphetRequest prophetRequest) throws Exception {
		Representation representation = null;
		ProphetResponse prophetResponse = null;
		WebServiceError webServiceError = null;

		ValidationError validationError = getValidationErrorCode(prophetRequest);

		if (validationError != null) {
			prophetResponse = new ProphetResponse();
			webServiceError = new WebServiceError();
			webServiceError.setError_code(validationError.getErrorCode());
			webServiceError.setError_message(getErrorMessage(validationError.getErrorCode(), validationError.getErrorData()));
			prophetResponse.setError(webServiceError);
			prophetResponse.setSuccess(false);
			FormatUtils.getInstance().logMemoryUsage("validateRequest() prophetResponse=" + new Gson().toJson(prophetResponse, ProphetResponse.class));
			representation = new JacksonRepresentation<ProphetResponse>(prophetResponse);
		}

		return representation;
	}

	private ValidationError getValidationErrorCode(ProphetRequest prophetRequest) throws Exception {
		ValidationError validationError = null;

		// 'forecast_days' is required
		if (prophetRequest.getForecast_days() == null) {
			return getValidationError(IConstants.MSG_CD_PROPHET_REQUEST_PARM_FORECAST_DAYS_REQUIRED);
		}
		// 'date_array' is required
		else if (prophetRequest.getDate_array() == null) {
			return getValidationError(IConstants.MSG_CD_PROPHET_REQUEST_PARM_DATE_ARRAY_REQUIRED);
		}
		// 'date_array' contains invalid data
		else if (isDateArrayInvalid(prophetRequest.getDate_array()) == true) {
			return getValidationError(IConstants.MSG_CD_PROPHET_REQUEST_PARM_DATE_ARRAY_INVALID);
		}
		// 'value_array' is required
		else if (prophetRequest.getValue_array() == null) {
			return getValidationError(IConstants.MSG_CD_PROPHET_REQUEST_PARM_VALUE_ARRAY_REQUIRED);
		}
		// 'date_array' length must equal 'value_array' length
		else if (prophetRequest.getDate_array().length != prophetRequest.getValue_array().length) {
			return getValidationError(IConstants.MSG_CD_PROPHET_REQUEST_PARM_ARRAY_LENGTH_MUST_BE_SAME);
		}

		return validationError;
	}

	private boolean isDateArrayInvalid(String[] dateArray) {
		boolean output = false;
		String dateBeforeString = null;
		String dateAfterString = null;

		nextDateString: for (String dateString : dateArray) {
			if (isDateValid(dateString) == false) {
				output = true;
				break nextDateString;
			}
		}

		if (output == false) {
			nextDateArray: for (int i = 0; i < dateArray.length; i++) {
				if (dateArray.length > (i + 1)) {
					dateBeforeString = dateArray[i];
					dateAfterString = dateArray[i + 1];
					if (areDatesValid(dateBeforeString, dateAfterString) == false) {
						output = true;
						break nextDateArray;
					}
				}
			}
		}

		return output;
	}

	private boolean isDateValid(String inputDateString) {
		boolean output = false;
		try {
			DateUtils.parseDateStrictly(inputDateString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
			output = true;
		} catch (Exception e) {
			output = false;
		}
		return output;
	}

	private boolean areDatesValid(String startDateString, String endDateString) {
		boolean output = false;
		Date startDate = null;
		Date endDate = null;
		try {
			startDate = DateUtils.parseDateStrictly(startDateString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
			endDate = DateUtils.parseDateStrictly(endDateString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
			if (endDate.before(startDate)) {
				output = false;
			} else {
				output = true;
			}
		} catch (Exception e) {
			output = false;
		}
		return output;
	}

	private ValidationError getValidationError(String errorCode) {
		return getValidationError(errorCode, null);
	}

	private ValidationError getValidationError(String errorCode, String errorData) {
		ValidationError validationError = new ValidationError();
		validationError.setErrorCode(errorCode);
		validationError.setErrorData(errorData);
		return validationError;
	}

	private Representation generateJsonError(String errorCode) {
		return generateJsonError(errorCode, null);
	}

	private Representation generateJsonError(String errorCode, String supplementalMessageText) {
		String errorMessage = getErrorMessage(errorCode, supplementalMessageText);
		ProphetResponse prophetResponse = new ProphetResponse();
		prophetResponse.setSuccess(false);
		WebServiceError webServiceError = new WebServiceError();
		webServiceError.setError_code(errorCode);
		webServiceError.setError_message(errorMessage);
		prophetResponse.setError(webServiceError);
		return new JacksonRepresentation<ProphetResponse>(prophetResponse);
	}

	private String getErrorMessage(String errorCode, String supplementalMessageText) {
		String errorMessage = null;
		String errorTemplate = ProphetWebServiceMessage.getStringProperty(errorCode);
		if (StringUtils.isNotBlank(supplementalMessageText)) {
			errorMessage = MessageFormat.format(errorTemplate, supplementalMessageText);
		} else {
			errorMessage = errorTemplate;
		}
		return errorMessage;
	}
}
