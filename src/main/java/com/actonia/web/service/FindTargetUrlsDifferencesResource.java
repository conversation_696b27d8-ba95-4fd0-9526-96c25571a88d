package com.actonia.web.service;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.restlet.ext.jackson.JacksonRepresentation;
import org.restlet.representation.Representation;
import org.restlet.resource.Post;

import com.actonia.IConstants;
import com.actonia.dao.ContentGuardClickHouseDAO;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.UrlMetricsEntityV3;
import com.actonia.service.AccessTokenService;
import com.actonia.utils.CrawlerUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.PutMessageUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.ChangeTrackingHashCdJson;
import com.actonia.value.object.ErrorValueObject;
import com.actonia.value.object.FindTargetUrlsDifferencesRequest;
import com.actonia.value.object.FindTargetUrlsDifferencesResponse;
import com.actonia.value.object.OgMarkup;
import com.actonia.value.object.PageLink;
import com.actonia.value.object.TargetUrlsDifferencesRequest;
import com.actonia.value.object.TargetUrlsDifferencesResponse;
import com.google.gson.Gson;

public class FindTargetUrlsDifferencesResource extends BaseServerResouce {

	//private boolean isDebug = false;

	private AccessTokenService accessTokenService;

	public FindTargetUrlsDifferencesResource() {
		super();
		accessTokenService = SpringBeanFactory.getBean("accessTokenService");
	}

	@Post("application/json")
	public Representation findTargetUrlsDifferences(Representation representationInput) {

		Representation representationOutput = null;
		String requestParameters = null;
		FindTargetUrlsDifferencesRequest findTargetUrlsDifferencesRequest = null;
		String accessToken = null;
		String accessTokenValidationErrorCode = null;

		try {
			if (representationInput == null) {
				return generatePoliteCrawlJsonError(IConstants.MSG_CD_REQUEST_JSON_REQUIRED);
			}

			requestParameters = representationInput.getText();
			FormatUtils.getInstance().logMemoryUsage("findTargetUrlsDifferences() requestParameters=" + requestParameters);
			if (StringUtils.isBlank(requestParameters)) {
				return generatePoliteCrawlJsonError(IConstants.MSG_CD_REQUEST_JSON_REQUIRED);
			}

			findTargetUrlsDifferencesRequest = new Gson().fromJson(requestParameters, FindTargetUrlsDifferencesRequest.class);

			accessToken = findTargetUrlsDifferencesRequest.getAccess_token();
			if (StringUtils.equals(accessToken, IConstants.INTERNAL_KEY) == false
					&& StringUtils.equalsIgnoreCase(accessToken, AccessTokenService.INTERNAL_KEY) == false) {
				accessTokenValidationErrorCode = accessTokenService.checkToken(accessToken);
				if (StringUtils.isNotBlank(accessTokenValidationErrorCode)) {
					return generatePoliteCrawlJsonError(IConstants.MSG_CD_INVALID_ACCESS);
				}
			}

			representationOutput = validateFindTargetUrlsDifferencesRequest(findTargetUrlsDifferencesRequest.getRequests());
			// when validation failed, return the error JSON representation
			if (representationOutput != null) {
				return representationOutput;
			}

			// when validation successful, process the data in the query parameters
			return getFindTargetUrlsDifferencesResponse(findTargetUrlsDifferencesRequest.getRequests());
		} catch (Exception e) {
			e.printStackTrace();
			StringWriter stringWriter = new StringWriter();
			e.printStackTrace(new PrintWriter(stringWriter));

			// send support alert
			String message = stringWriter.toString();

			return generatePoliteCrawlJsonError(IConstants.MSG_CD_WEB_SERVICE_METHOD_EXCEPTION, message);

		}
	}

	private Representation validateFindTargetUrlsDifferencesRequest(TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequests) throws Exception {
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = null;
		List<TargetUrlsDifferencesResponse> targetUrlsDifferencesResponseList = new ArrayList<TargetUrlsDifferencesResponse>();
		TargetUrlsDifferencesResponse[] targetUrlsDifferencesResponses = null;
		TargetUrlsDifferencesResponse targetUrlsDifferencesResponse = null;
		String sequence = null;
		String crawlTimestamp1String = null;
		String domainId1String = null;
		String url1String = null;
		String crawlTimestamp2String = null;
		String domainId2String = null;
		String url2String = null;
		String urlSkipDomainNameFlg = null;
		String textCaseInsensitiveFlg = null;
		ErrorValueObject errorValueObject = null;
		String errorCode = null;
		int domainIdNumber = 0;
		HtmlClickHouseEntity htmlClickHouseEntity1 = null;
		HtmlClickHouseEntity htmlClickHouseEntity2 = null;
		Date crawlTimestamp2Date = null;
		List<String> databaseFields = null;

		nextTargetUrlsDifferencesRequest: for (TargetUrlsDifferencesRequest targetUrlsDifferencesRequest : targetUrlsDifferencesRequests) {

			// sequence
			sequence = targetUrlsDifferencesRequest.getSequence();

			// crawlTimestamp1
			crawlTimestamp1String = targetUrlsDifferencesRequest.getCrawl_timestamp_1();

			// domainId1
			domainId1String = targetUrlsDifferencesRequest.getDomain_id_1();

			// url1
			url1String = targetUrlsDifferencesRequest.getUrl_1();

			// crawlTimestamp2
			crawlTimestamp2String = targetUrlsDifferencesRequest.getCrawl_timestamp_2();

			// domainId2
			domainId2String = targetUrlsDifferencesRequest.getDomain_id_2();

			// url2
			url2String = targetUrlsDifferencesRequest.getUrl_2();

			// url_skip_domain_name_flg
			urlSkipDomainNameFlg = targetUrlsDifferencesRequest.getUrl_skip_domain_name_flg();

			// text_case_insensitive_flg
			textCaseInsensitiveFlg = targetUrlsDifferencesRequest.getText_case_insensitive_flg();

			FormatUtils.getInstance()
					.logMemoryUsage("validateFindTargetUrlsDifferencesRequest() sequence=" + sequence + ",crawlTimestamp1String=" + crawlTimestamp1String
							+ ",domainId1String=" + domainId1String + ",url1String=" + url1String + ",crawlTimestamp2String=" + crawlTimestamp2String
							+ ",domainId2String=" + domainId2String + ",url2String=" + url2String + ",urlSkipDomainNameFlg=" + urlSkipDomainNameFlg
							+ ",textCaseInsensitiveFlg=" + textCaseInsensitiveFlg);

			// target URL 1's crawl timestamp, domain ID and URL are optional
			// when request contains target URL 1's crawl timestamp, domain ID or URL, validate target URL 1's crawl timestamp, domain ID and URL 
			if (StringUtils.isNotBlank(crawlTimestamp1String) || StringUtils.isNotBlank(domainId1String) || StringUtils.isNotBlank(url1String)) {
				if (StringUtils.isBlank(crawlTimestamp1String)) {
					targetUrlsDifferencesResponse = new TargetUrlsDifferencesResponse();
					errorValueObject = new ErrorValueObject();
					errorCode = IConstants.MSG_CD_REQUEST_PARM_CRAWL_TIMESTAMP_1_IS_REQUIRED;
					errorValueObject.setError_code(errorCode);
					errorValueObject.setError_message(getPoliteCrawlErrorMessage(errorCode));
					errorValueObject.setSequence(sequence);
					targetUrlsDifferencesResponse.setError(errorValueObject);
					targetUrlsDifferencesResponseList.add(targetUrlsDifferencesResponse);
					continue nextTargetUrlsDifferencesRequest;
				}

				try {
					DateUtils.parseDateStrictly(crawlTimestamp1String, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS });
				} catch (Exception e) {
					targetUrlsDifferencesResponse = new TargetUrlsDifferencesResponse();
					errorValueObject = new ErrorValueObject();
					errorCode = IConstants.MSG_CD_REQUEST_PARM_CRAWL_TIMESTAMP_1_IS_INVALID;
					errorValueObject.setError_code(errorCode);
					errorValueObject.setError_message(getPoliteCrawlErrorMessage(errorCode, crawlTimestamp1String));
					errorValueObject.setSequence(sequence);
					targetUrlsDifferencesResponse.setError(errorValueObject);
					targetUrlsDifferencesResponseList.add(targetUrlsDifferencesResponse);
					continue nextTargetUrlsDifferencesRequest;
				}

				if (StringUtils.isBlank(domainId1String)) {
					targetUrlsDifferencesResponse = new TargetUrlsDifferencesResponse();
					errorValueObject = new ErrorValueObject();
					errorCode = IConstants.MSG_CD_REQUEST_PARM_DOMAIN_ID_1_IS_REQUIRED;
					errorValueObject.setError_code(errorCode);
					errorValueObject.setError_message(getPoliteCrawlErrorMessage(errorCode));
					errorValueObject.setSequence(sequence);
					targetUrlsDifferencesResponse.setError(errorValueObject);
					targetUrlsDifferencesResponseList.add(targetUrlsDifferencesResponse);
					continue nextTargetUrlsDifferencesRequest;
				}

				domainIdNumber = NumberUtils.toInt(domainId1String);
				if (domainIdNumber == 0) {
					targetUrlsDifferencesResponse = new TargetUrlsDifferencesResponse();
					errorValueObject = new ErrorValueObject();
					errorCode = IConstants.MSG_CD_REQUEST_PARM_DOMAIN_ID_1_IS_INVALID;
					errorValueObject.setError_code(errorCode);
					errorValueObject.setError_message(getPoliteCrawlErrorMessage(errorCode, domainId1String));
					errorValueObject.setSequence(sequence);
					targetUrlsDifferencesResponse.setError(errorValueObject);
					targetUrlsDifferencesResponseList.add(targetUrlsDifferencesResponse);
					continue nextTargetUrlsDifferencesRequest;
				}
				if (StringUtils.isBlank(url1String)) {
					targetUrlsDifferencesResponse = new TargetUrlsDifferencesResponse();
					errorValueObject = new ErrorValueObject();
					errorCode = IConstants.MSG_CD_REQUEST_PARM_URL_1_IS_REQUIRED;
					errorValueObject.setError_code(errorCode);
					errorValueObject.setError_message(getPoliteCrawlErrorMessage(errorCode));
					errorValueObject.setSequence(sequence);
					targetUrlsDifferencesResponse.setError(errorValueObject);
					targetUrlsDifferencesResponseList.add(targetUrlsDifferencesResponse);
					continue nextTargetUrlsDifferencesRequest;
				}
				try {
					new URL(url1String);
				} catch (Exception e) {
					targetUrlsDifferencesResponse = new TargetUrlsDifferencesResponse();
					errorValueObject = new ErrorValueObject();
					errorCode = IConstants.MSG_CD_REQUEST_PARM_URL_1_IS_INVALID;
					errorValueObject.setError_code(errorCode);
					errorValueObject.setError_message(getPoliteCrawlErrorMessage(errorCode, url1String));
					errorValueObject.setSequence(sequence);
					targetUrlsDifferencesResponse.setError(errorValueObject);
					targetUrlsDifferencesResponseList.add(targetUrlsDifferencesResponse);
					continue nextTargetUrlsDifferencesRequest;
				}
			}

			// target URL 2's crawl timestamp is optional
			// when request contains target URL 2's crawl timestamp, validate target URL 2's crawl timestamp 
			if (StringUtils.isNotBlank(crawlTimestamp2String)) {
				try {
					DateUtils.parseDateStrictly(crawlTimestamp2String, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS });
				} catch (Exception e) {
					targetUrlsDifferencesResponse = new TargetUrlsDifferencesResponse();
					errorValueObject = new ErrorValueObject();
					errorCode = IConstants.MSG_CD_REQUEST_PARM_CRAWL_TIMESTAMP_2_IS_INVALID;
					errorValueObject.setError_code(errorCode);
					errorValueObject.setError_message(getPoliteCrawlErrorMessage(errorCode, crawlTimestamp2String));
					errorValueObject.setSequence(sequence);
					targetUrlsDifferencesResponse.setError(errorValueObject);
					targetUrlsDifferencesResponseList.add(targetUrlsDifferencesResponse);
					continue nextTargetUrlsDifferencesRequest;
				}
			}

			// target URL 2's domain ID is required
			if (StringUtils.isBlank(domainId2String)) {
				targetUrlsDifferencesResponse = new TargetUrlsDifferencesResponse();
				errorValueObject = new ErrorValueObject();
				errorCode = IConstants.MSG_CD_REQUEST_PARM_DOMAIN_ID_2_IS_REQUIRED;
				errorValueObject.setError_code(errorCode);
				errorValueObject.setError_message(getPoliteCrawlErrorMessage(errorCode));
				errorValueObject.setSequence(sequence);
				targetUrlsDifferencesResponse.setError(errorValueObject);
				targetUrlsDifferencesResponseList.add(targetUrlsDifferencesResponse);
				continue nextTargetUrlsDifferencesRequest;
			}

			domainIdNumber = NumberUtils.toInt(domainId2String);
			if (domainIdNumber == 0) {
				targetUrlsDifferencesResponse = new TargetUrlsDifferencesResponse();
				errorValueObject = new ErrorValueObject();
				errorCode = IConstants.MSG_CD_REQUEST_PARM_DOMAIN_ID_2_IS_INVALID;
				errorValueObject.setError_code(errorCode);
				errorValueObject.setError_message(getPoliteCrawlErrorMessage(errorCode, domainId2String));
				errorValueObject.setSequence(sequence);
				targetUrlsDifferencesResponse.setError(errorValueObject);
				targetUrlsDifferencesResponseList.add(targetUrlsDifferencesResponse);
				continue nextTargetUrlsDifferencesRequest;
			}

			// target URL 2's URL is required
			if (StringUtils.isBlank(url2String)) {
				targetUrlsDifferencesResponse = new TargetUrlsDifferencesResponse();
				errorValueObject = new ErrorValueObject();
				errorCode = IConstants.MSG_CD_REQUEST_PARM_URL_2_IS_REQUIRED;
				errorValueObject.setError_code(errorCode);
				errorValueObject.setError_message(getPoliteCrawlErrorMessage(errorCode));
				errorValueObject.setSequence(sequence);
				targetUrlsDifferencesResponse.setError(errorValueObject);
				targetUrlsDifferencesResponseList.add(targetUrlsDifferencesResponse);
				continue nextTargetUrlsDifferencesRequest;
			}
			try {
				new URL(url2String);
			} catch (Exception e) {
				targetUrlsDifferencesResponse = new TargetUrlsDifferencesResponse();
				errorValueObject = new ErrorValueObject();
				errorCode = IConstants.MSG_CD_REQUEST_PARM_URL_2_IS_INVALID;
				errorValueObject.setError_code(errorCode);
				errorValueObject.setError_message(getPoliteCrawlErrorMessage(errorCode, url2String));
				errorValueObject.setSequence(sequence);
				targetUrlsDifferencesResponse.setError(errorValueObject);
				targetUrlsDifferencesResponseList.add(targetUrlsDifferencesResponse);
				continue nextTargetUrlsDifferencesRequest;
			}

			// determine target URL 2 crawl timestamp date when not provided in request 
			if (StringUtils.isBlank(crawlTimestamp2String)) {
				databaseFields = new ArrayList<String>();
				databaseFields.add(IConstants.CRAWL_TIMESTAMP);
				domainIdNumber = NumberUtils.toInt(domainId2String);
				htmlClickHouseEntity2 = ContentGuardClickHouseDAO.getInstance().getPrevious(null, null, domainIdNumber, url2String, databaseFields, null, null, null);
				if (htmlClickHouseEntity2 != null) {
					crawlTimestamp2Date = htmlClickHouseEntity2.getCrawlTimestamp();
					crawlTimestamp2String = DateFormatUtils.format(crawlTimestamp2Date, IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
					FormatUtils.getInstance().logMemoryUsage(
							"validateFindTargetUrlsDifferencesRequest() sequence=" + sequence + ",crawlTimestamp2String determined to be=" + crawlTimestamp2String);

					// update 'targetUrlsDifferencesRequest' using pass by reference
					targetUrlsDifferencesRequest
							.setCrawl_timestamp_2(DateFormatUtils.format(htmlClickHouseEntity2.getCrawlTimestamp(), IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS));

				} else {
					targetUrlsDifferencesResponse = new TargetUrlsDifferencesResponse();
					errorValueObject = new ErrorValueObject();
					errorCode = IConstants.MSG_CD_TARGET_URL_2_CRAWL_TIMESTAMP_CANNOT_BE_DETERMINED;
					errorValueObject.setError_code(errorCode);
					errorValueObject.setError_message(getPoliteCrawlErrorMessage(errorCode, url2String));
					errorValueObject.setSequence(sequence);
					targetUrlsDifferencesResponse.setError(errorValueObject);
					targetUrlsDifferencesResponseList.add(targetUrlsDifferencesResponse);
					continue nextTargetUrlsDifferencesRequest;
				}
			}

			// determine target URL 1 cawl timestamp which is the last crawl timestamp before target URL 2 crawl timestamp
			if (StringUtils.isBlank(crawlTimestamp1String) && StringUtils.isBlank(domainId1String) && StringUtils.isBlank(url1String)) {
				databaseFields = new ArrayList<String>();
				databaseFields.add(IConstants.CRAWL_TIMESTAMP);
				domainIdNumber = NumberUtils.toInt(domainId2String);
				crawlTimestamp2Date = DateUtils.parseDateStrictly(crawlTimestamp2String, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS });
				htmlClickHouseEntity1 = ContentGuardClickHouseDAO.getInstance().getPrevious(null, null, domainIdNumber, url2String, databaseFields, null, null,
						crawlTimestamp2Date);
				if (htmlClickHouseEntity1 != null) {
					FormatUtils.getInstance().logMemoryUsage("validateFindTargetUrlsDifferencesRequest() sequence=" + sequence
							+ ",crawlTimestamp1String determined to be=" + htmlClickHouseEntity1.getCrawlTimestamp());

					// update 'targetUrlsDifferencesRequest' using pass by reference
					targetUrlsDifferencesRequest
							.setCrawl_timestamp_1(DateFormatUtils.format(htmlClickHouseEntity1.getCrawlTimestamp(), IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS));
					targetUrlsDifferencesRequest.setDomain_id_1(domainId2String);
					targetUrlsDifferencesRequest.setUrl_1(url2String);

				} else {
					targetUrlsDifferencesResponse = new TargetUrlsDifferencesResponse();
					errorValueObject = new ErrorValueObject();
					errorCode = IConstants.MSG_CD_TARGET_URL_1_CRAWL_TIMESTAMP_CANNOT_BE_DETERMINED;
					errorValueObject.setError_code(errorCode);
					errorValueObject.setError_message(getPoliteCrawlErrorMessage(errorCode, url2String));
					errorValueObject.setSequence(sequence);
					targetUrlsDifferencesResponse.setError(errorValueObject);
					targetUrlsDifferencesResponseList.add(targetUrlsDifferencesResponse);
					continue nextTargetUrlsDifferencesRequest;
				}
			}

			// 'urlSkipDomainNameFlg' must be 'true', 'false', or null
			if (StringUtils.isNotBlank(urlSkipDomainNameFlg)) {
				if (StringUtils.equalsIgnoreCase(urlSkipDomainNameFlg, IConstants.TRUE) == false
						&& StringUtils.equalsIgnoreCase(urlSkipDomainNameFlg, IConstants.FALSE) == false) {
					targetUrlsDifferencesResponse = new TargetUrlsDifferencesResponse();
					errorValueObject = new ErrorValueObject();
					errorCode = IConstants.MSG_CD_URL_SKIP_DOMAIN_NAME_FLG_INVALID;
					errorValueObject.setError_code(errorCode);
					errorValueObject.setError_message(getPoliteCrawlErrorMessage(errorCode, urlSkipDomainNameFlg));
					errorValueObject.setSequence(sequence);
					targetUrlsDifferencesResponse.setError(errorValueObject);
					targetUrlsDifferencesResponseList.add(targetUrlsDifferencesResponse);
					continue nextTargetUrlsDifferencesRequest;
				}
			}

			// 'textCaseInsensitiveFlg' must be 'true', 'false', or null
			if (StringUtils.isNotBlank(textCaseInsensitiveFlg)) {
				if (StringUtils.equalsIgnoreCase(textCaseInsensitiveFlg, IConstants.TRUE) == false
						&& StringUtils.equalsIgnoreCase(textCaseInsensitiveFlg, IConstants.FALSE) == false) {
					targetUrlsDifferencesResponse = new TargetUrlsDifferencesResponse();
					errorValueObject = new ErrorValueObject();
					errorCode = IConstants.MSG_CD_URL_TEXT_CASE_INSENSITIVE_FLG_INVALID;
					errorValueObject.setError_code(errorCode);
					errorValueObject.setError_message(getPoliteCrawlErrorMessage(errorCode, textCaseInsensitiveFlg));
					errorValueObject.setSequence(sequence);
					targetUrlsDifferencesResponse.setError(errorValueObject);
					targetUrlsDifferencesResponseList.add(targetUrlsDifferencesResponse);
					continue nextTargetUrlsDifferencesRequest;
				}
			}
		}

		// when there are validation errors, return error representation
		if (targetUrlsDifferencesResponseList != null && targetUrlsDifferencesResponseList.size() > 0) {
			targetUrlsDifferencesResponses = targetUrlsDifferencesResponseList.toArray(new TargetUrlsDifferencesResponse[0]);
			findTargetUrlsDifferencesResponse = new FindTargetUrlsDifferencesResponse();
			findTargetUrlsDifferencesResponse.setResponses(targetUrlsDifferencesResponses);
			return new JacksonRepresentation<FindTargetUrlsDifferencesResponse>(findTargetUrlsDifferencesResponse);
		}
		// when all Query parameters have been validated, return null representation
		else {
			return null;
		}
	}

	private Representation getFindTargetUrlsDifferencesResponse(TargetUrlsDifferencesRequest[] targetUrlsDifferencesRequests) throws Exception {
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = new FindTargetUrlsDifferencesResponse();
		List<TargetUrlsDifferencesResponse> targetUrlsDifferencesResponseList = new ArrayList<TargetUrlsDifferencesResponse>();
		TargetUrlsDifferencesResponse[] targetUrlsDifferencesResponses = null;
		TargetUrlsDifferencesResponse targetUrlsDifferencesResponse = null;
		String tableName = null;
		boolean isDifferent = false;
		UrlMetricsEntityV3 urlMetricsEntityV3 = null;
		String sequence = null;
		String crawlTimestamp1String = null;
		String domainId1String = null;
		String url1String = null;
		String crawlTimestamp2String = null;
		String domainId2String = null;
		String url2String = null;
		ErrorValueObject errorValueObject = null;
		String errorCode = null;
		Date crawlTimestamp1Date = null;
		int domainId1Number = 0;
		Date crawlTimestamp2Date = null;
		int domainId2Number = 0;
		List<String> fieldNameList = null;
		HtmlClickHouseEntity htmlClickHouseEntity1 = null;
		HtmlClickHouseEntity htmlClickHouseEntity2 = null;
		String previousResponseCodeInCickHouse = null;
		Date previousTrackDateInCickHouse = null;
		ChangeTrackingHashCdJson[] currentChangeTrackingHashCdJsonArray = null;
		Boolean urlSkipDomainNameFlg = null;
		Boolean textCaseInsensitiveFlg = null;

		nextTargetUrlsDifferencesRequest: for (TargetUrlsDifferencesRequest targetUrlsDifferencesRequest : targetUrlsDifferencesRequests) {

			// sequence
			sequence = targetUrlsDifferencesRequest.getSequence();

			// crawlTimestamp1
			crawlTimestamp1String = targetUrlsDifferencesRequest.getCrawl_timestamp_1();
			crawlTimestamp1Date = DateUtils.parseDateStrictly(crawlTimestamp1String, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS });

			// domainId1
			domainId1String = targetUrlsDifferencesRequest.getDomain_id_1();
			domainId1Number = NumberUtils.toInt(domainId1String);

			// url1
			url1String = targetUrlsDifferencesRequest.getUrl_1();

			// crawlTimestamp2
			crawlTimestamp2String = targetUrlsDifferencesRequest.getCrawl_timestamp_2();
			crawlTimestamp2Date = DateUtils.parseDateStrictly(crawlTimestamp2String, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS });

			// domainId2
			domainId2String = targetUrlsDifferencesRequest.getDomain_id_2();
			domainId2Number = NumberUtils.toInt(domainId2String);

			// url2
			url2String = targetUrlsDifferencesRequest.getUrl_2();

			// url_skip_domain_name_flg
			urlSkipDomainNameFlg = BooleanUtils.toBoolean(targetUrlsDifferencesRequest.getUrl_skip_domain_name_flg());

			// text_case_insensitive_flg
			textCaseInsensitiveFlg = BooleanUtils.toBoolean(targetUrlsDifferencesRequest.getText_case_insensitive_flg());

			FormatUtils.getInstance()
					.logMemoryUsage("getFindTargetUrlsDifferencesResponse() sequence=" + sequence + ",crawlTimestamp1String=" + crawlTimestamp1String
							+ ",crawlTimestamp1Date=" + crawlTimestamp1Date + ",domainId1String=" + domainId1String + ",domainId1Number=" + domainId1Number
							+ ",url1String=" + url1String + ",crawlTimestamp2String=" + crawlTimestamp2String + ",crawlTimestamp2Date=" + crawlTimestamp2Date
							+ ",domainId2String=" + domainId2String + ",domainId2Number=" + domainId2Number + ",url2String=" + url2String + ",urlSkipDomainNameFlg="
							+ urlSkipDomainNameFlg + ",textCaseInsensitiveFlg=" + textCaseInsensitiveFlg);

			fieldNameList = PutMessageUtils.getInstance().getHistoricalHtmlFieldNames();
			fieldNameList.addAll(CrawlerUtils.getInstance().getChangeTrackingHashCdJsonFieldNames());

			// retrieve historical data for the first record
			htmlClickHouseEntity1 = ContentGuardClickHouseDAO.getInstance().getByCrawlTimestamp(crawlTimestamp1Date, domainId1Number, url1String, fieldNameList,
					tableName);
			if (htmlClickHouseEntity1 != null) {

				//FormatUtils.getInstance().logMemoryUsage("getFindTargetUrlsDifferencesResponse() htmlClickHouseEntity1=" + htmlClickHouseEntity1.toString());

				// When skip domain name in URLs, update 'htmlClickHouseEntity1' using pass by reference after removing domain name from the URLs in the following fields:
				// 1) analyzed_url_s 
				// 2) canonical
				// 3) og_markup
				// 4) page_link
				if (BooleanUtils.isTrue(urlSkipDomainNameFlg)) {
					//FormatUtils.getInstance().logMemoryUsage("getFindTargetUrlsDifferencesResponse() htmlClickHouseEntity1 b4=" + htmlClickHouseEntity1.toString());
					removeDomainNameFromUrls(htmlClickHouseEntity1);
					//FormatUtils.getInstance().logMemoryUsage("getFindTargetUrlsDifferencesResponse() htmlClickHouseEntity1 af=" + htmlClickHouseEntity1.toString());
				}
			} else {
				FormatUtils.getInstance().logMemoryUsage("getFindTargetUrlsDifferencesResponse() htmlClickHouseEntity1 not available.");
				targetUrlsDifferencesResponse = new TargetUrlsDifferencesResponse();
				errorValueObject = new ErrorValueObject();
				errorCode = IConstants.MSG_CD_QUERY_RECORD_NOT_AVAILABLE_IN_CLARITYDB;
				errorValueObject.setError_code(errorCode);
				errorValueObject.setError_message(getPoliteCrawlErrorMessage(errorCode));
				errorValueObject.setSequence(sequence);
				errorValueObject.setCrawl_timestamp(crawlTimestamp1String);
				errorValueObject.setDomain_id(domainId1String);
				errorValueObject.setUrl(url1String);
				targetUrlsDifferencesResponse.setError(errorValueObject);
				targetUrlsDifferencesResponse.setSequence(sequence);
				targetUrlsDifferencesResponse.setCrawl_timestamp_1(crawlTimestamp1String);
				targetUrlsDifferencesResponse.setDomain_id_1(domainId1String);
				targetUrlsDifferencesResponse.setUrl_1(url1String);
				targetUrlsDifferencesResponse.setCrawl_timestamp_2(crawlTimestamp2String);
				targetUrlsDifferencesResponse.setDomain_id_2(domainId2String);
				targetUrlsDifferencesResponse.setUrl_2(url2String);
				targetUrlsDifferencesResponseList.add(targetUrlsDifferencesResponse);
				continue nextTargetUrlsDifferencesRequest;
			}

			// retrieve historical data for the second record
			htmlClickHouseEntity2 = ContentGuardClickHouseDAO.getInstance().getByCrawlTimestamp(crawlTimestamp2Date, domainId2Number, url2String, fieldNameList,
					tableName);
			if (htmlClickHouseEntity2 != null) {

				//FormatUtils.getInstance().logMemoryUsage("getFindTargetUrlsDifferencesResponse() htmlClickHouseEntity2=" + htmlClickHouseEntity2.toString());

				// When skip domain name in URLs, update 'htmlClickHouseEntity2' using pass by reference after removing domain name from the URLs in the following fields:
				// 1) analyzed_url_s 
				// 2) canonical
				// 3) og_markup
				// 4) page_link
				if (BooleanUtils.isTrue(urlSkipDomainNameFlg)) {
					//FormatUtils.getInstance().logMemoryUsage("getFindTargetUrlsDifferencesResponse() htmlClickHouseEntity2 b4=" + htmlClickHouseEntity2.toString());
					removeDomainNameFromUrls(htmlClickHouseEntity2);
					//FormatUtils.getInstance().logMemoryUsage("getFindTargetUrlsDifferencesResponse() htmlClickHouseEntity2 af=" + htmlClickHouseEntity2.toString());
				}
			} else {
				FormatUtils.getInstance().logMemoryUsage("getFindTargetUrlsDifferencesResponse() htmlClickHouseEntity2 not available.");
				targetUrlsDifferencesResponse = new TargetUrlsDifferencesResponse();
				errorValueObject = new ErrorValueObject();
				errorCode = IConstants.MSG_CD_QUERY_RECORD_NOT_AVAILABLE_IN_CLARITYDB;
				errorValueObject.setError_code(errorCode);
				errorValueObject.setError_message(getPoliteCrawlErrorMessage(errorCode));
				errorValueObject.setSequence(sequence);
				errorValueObject.setCrawl_timestamp(crawlTimestamp2String);
				errorValueObject.setDomain_id(domainId2String);
				errorValueObject.setUrl(url2String);
				targetUrlsDifferencesResponse.setError(errorValueObject);
				targetUrlsDifferencesResponse.setSequence(sequence);
				targetUrlsDifferencesResponse.setCrawl_timestamp_1(crawlTimestamp1String);
				targetUrlsDifferencesResponse.setDomain_id_1(domainId1String);
				targetUrlsDifferencesResponse.setUrl_1(url1String);
				targetUrlsDifferencesResponse.setCrawl_timestamp_2(crawlTimestamp2String);
				targetUrlsDifferencesResponse.setDomain_id_2(domainId2String);
				targetUrlsDifferencesResponse.setUrl_2(url2String);
				targetUrlsDifferencesResponseList.add(targetUrlsDifferencesResponse);
				continue nextTargetUrlsDifferencesRequest;
			}

			// htmlClickHouseEntity1 is the previous crawl results
			// htmlClickHouseEntity2 is the current crawl results
			if (htmlClickHouseEntity1 != null && htmlClickHouseEntity2 != null) {

				if (BooleanUtils.isTrue(textCaseInsensitiveFlg)) {
					changeFieldsContentToLowerCase(htmlClickHouseEntity1);
					changeFieldsContentToLowerCase(htmlClickHouseEntity2);
				}

				// convert HtmlClickHouseEntity of the first record to UrlMetricsEntityV3
				urlMetricsEntityV3 = getUrlMetricsEntityV3(htmlClickHouseEntity1);
				previousResponseCodeInCickHouse = htmlClickHouseEntity1.getCrawlerResponse().getResponse_code();
				previousTrackDateInCickHouse = htmlClickHouseEntity1.getTrackDate();

				currentChangeTrackingHashCdJsonArray = htmlClickHouseEntity2.getChangeTrackingHashCdJsonArray();

				isDifferent = CrawlerUtils.getInstance().trackChanges(IConstants.NUMBER_1, htmlClickHouseEntity2, urlMetricsEntityV3,
						currentChangeTrackingHashCdJsonArray, previousResponseCodeInCickHouse, previousTrackDateInCickHouse, IConstants.CRAWL_TYPE_CONTENT_GUARD,
						urlSkipDomainNameFlg, textCaseInsensitiveFlg);

				if (isDifferent == true) {
					targetUrlsDifferencesResponse = getTargetUrlsDifferencesResponse(htmlClickHouseEntity1, htmlClickHouseEntity2);
					targetUrlsDifferencesResponse.setIs_different(true);
				} else {
					targetUrlsDifferencesResponse = new TargetUrlsDifferencesResponse();
					targetUrlsDifferencesResponse.setIs_different(false);
				}
				targetUrlsDifferencesResponse.setSequence(sequence);
				targetUrlsDifferencesResponse.setCrawl_timestamp_1(crawlTimestamp1String);
				targetUrlsDifferencesResponse.setDomain_id_1(domainId1String);
				targetUrlsDifferencesResponse.setUrl_1(url1String);
				targetUrlsDifferencesResponse.setCrawl_timestamp_2(crawlTimestamp2String);
				targetUrlsDifferencesResponse.setDomain_id_2(domainId2String);
				targetUrlsDifferencesResponse.setUrl_2(url2String);
				targetUrlsDifferencesResponseList.add(targetUrlsDifferencesResponse);
			}
		}

		if (targetUrlsDifferencesResponseList != null && targetUrlsDifferencesResponseList.size() > 0) {
			targetUrlsDifferencesResponses = targetUrlsDifferencesResponseList.toArray(new TargetUrlsDifferencesResponse[0]);
			findTargetUrlsDifferencesResponse.setResponses(targetUrlsDifferencesResponses);
		}
		return new JacksonRepresentation<FindTargetUrlsDifferencesResponse>(findTargetUrlsDifferencesResponse);
	}

	// update 'htmlClickHouseEntity' using pass by reference
	private void changeFieldsContentToLowerCase(HtmlClickHouseEntity htmlClickHouseEntity) {
		String testString = null;
		String[] testStringArray = null;
		String[] stringArray = null;

		// description
		if (StringUtils.isNotBlank(htmlClickHouseEntity.getCrawlerResponse().getDescription())) {
			testString = htmlClickHouseEntity.getCrawlerResponse().getDescription().toLowerCase();
			htmlClickHouseEntity.getCrawlerResponse().setDescription(testString);
		}

		// h1
		if (htmlClickHouseEntity.getCrawlerResponse().getH1() != null && htmlClickHouseEntity.getCrawlerResponse().getH1().length > 0) {
			testStringArray = htmlClickHouseEntity.getCrawlerResponse().getH1();
			stringArray = new String[testStringArray.length];
			for (int i = 0; i < testStringArray.length; i++) {
				stringArray[i] = testStringArray[i].toLowerCase();
			}
			htmlClickHouseEntity.getCrawlerResponse().setH1(stringArray);
		}

		// h2
		if (htmlClickHouseEntity.getCrawlerResponse().getH2() != null && htmlClickHouseEntity.getCrawlerResponse().getH2().length > 0) {
			testStringArray = htmlClickHouseEntity.getCrawlerResponse().getH2();
			stringArray = new String[testStringArray.length];
			for (int i = 0; i < testStringArray.length; i++) {
				stringArray[i] = testStringArray[i].toLowerCase();
			}
			htmlClickHouseEntity.getCrawlerResponse().setH2(stringArray);
		}

		// title
		if (StringUtils.isNotBlank(htmlClickHouseEntity.getCrawlerResponse().getTitle())) {
			testString = htmlClickHouseEntity.getCrawlerResponse().getTitle().toLowerCase();
			htmlClickHouseEntity.getCrawlerResponse().setTitle(testString);
		}
	}

	private void removeDomainNameFromUrls(HtmlClickHouseEntity htmlClickHouseEntity) throws Exception {

		String inputString = null;
		String outputString = null;

		// 1) analyzed_url_s
		inputString = htmlClickHouseEntity.getCrawlerResponse().getAnalyzed_url_s();
		if (StringUtils.isNotBlank(inputString)) {
			outputString = CrawlerUtils.getInstance().removeDomainNameFromUrl(inputString);
			htmlClickHouseEntity.getCrawlerResponse().setAnalyzed_url_s(outputString);
		}

		// 2) canonical
		inputString = htmlClickHouseEntity.getCrawlerResponse().getCanonical();
		if (StringUtils.isNotBlank(inputString)) {
			outputString = CrawlerUtils.getInstance().removeDomainNameFromUrl(inputString);
			htmlClickHouseEntity.getCrawlerResponse().setCanonical(outputString);
		}

		// 3) og_markup
		if (htmlClickHouseEntity.getCrawlerResponse().getOg_markup() != null && htmlClickHouseEntity.getCrawlerResponse().getOg_markup().length > 0) {
			for (OgMarkup ogMarkup : htmlClickHouseEntity.getCrawlerResponse().getOg_markup()) {
				inputString = ogMarkup.getContent();
				outputString = CrawlerUtils.getInstance().removeDomainNameFromUrl(inputString);
				ogMarkup.setContent(outputString);
			}
		}

		// 4) page_link
		if (htmlClickHouseEntity.getCrawlerResponse().getPage_link() != null && htmlClickHouseEntity.getCrawlerResponse().getPage_link().length > 0) {
			for (PageLink pageLink : htmlClickHouseEntity.getCrawlerResponse().getPage_link()) {
				inputString = pageLink.getDestination_url();
				outputString = CrawlerUtils.getInstance().removeDomainNameFromUrl(inputString);
				pageLink.setDestination_url(outputString);
			}
		}
	}

	private UrlMetricsEntityV3 getUrlMetricsEntityV3(HtmlClickHouseEntity htmlClickHouseEntity) {
		UrlMetricsEntityV3 urlMetricsEntityV3 = new UrlMetricsEntityV3();
		Integer httpStatusCode = null;
		int httpStatusCodeNumber = 0;
		try {
			urlMetricsEntityV3.setTargetUrlHtmlDailyDataInd(true);
			httpStatusCode = htmlClickHouseEntity.getHttpStatusCode();
			if (httpStatusCode != null) {

				httpStatusCodeNumber = httpStatusCode.intValue();

				// track_date
				urlMetricsEntityV3.setTargetUrlHtmlTrackDate(DateFormatUtils.format(htmlClickHouseEntity.getTrackDate(), IConstants.DATE_FORMAT_YYYY_MM_DD));

				// response_code
				urlMetricsEntityV3.setResponse_code(htmlClickHouseEntity.getCrawlerResponse().getResponse_code());

				if (httpStatusCodeNumber == 0) {
					// when historical HTTP status code is 0, no need to update UrlMetricsEntity with historical crawled data

				} else if (httpStatusCodeNumber > 399) {
					// when historical HTTP status code is 4xx or 5xx, no need to update UrlMetricsEntity with historical crawled data
				}
				// when historical HTTP status code is 3xx
				// when historical HTTP status code is 200
				else if (httpStatusCodeNumber == 200 || (httpStatusCodeNumber >= 300 && httpStatusCodeNumber <= 399)) {

					// amphtml_flag
					urlMetricsEntityV3.setAmphtml_flag(htmlClickHouseEntity.getCrawlerResponse().getAmphtml_flag());

					// analyzed_url_flg_s
					urlMetricsEntityV3.setAnalyzed_url_flg_s(htmlClickHouseEntity.getCrawlerResponse().getAnalyzed_url_flg_s());

					// analyzed_url_s
					urlMetricsEntityV3.setAnalyzed_url_s(htmlClickHouseEntity.getCrawlerResponse().getAnalyzed_url_s());

					// archive_flg
					urlMetricsEntityV3.setArchive_flg(htmlClickHouseEntity.getCrawlerResponse().getArchive_flg());

					// archive_flg_x_tag
					urlMetricsEntityV3.setArchive_flg_x_tag(htmlClickHouseEntity.getCrawlerResponse().getArchive_flg_x_tag());

					// blocked_by_robots
					urlMetricsEntityV3.setBlocked_by_robots(htmlClickHouseEntity.getCrawlerResponse().getBlocked_by_robots());

					// canonical
					urlMetricsEntityV3.setCanonical(htmlClickHouseEntity.getCrawlerResponse().getCanonical());

					// canonical_flg
					urlMetricsEntityV3.setCanonical_flg(htmlClickHouseEntity.getCrawlerResponse().getCanonical_flg());

					// canonical_header_flag
					urlMetricsEntityV3.setCanonical_header_flag(htmlClickHouseEntity.getCrawlerResponse().getCanonical_header_flag());

					// canonical_header_type
					urlMetricsEntityV3.setCanonical_header_type(htmlClickHouseEntity.getCrawlerResponse().getCanonical_header_type());

					// canonical_type
					urlMetricsEntityV3.setCanonical_type(htmlClickHouseEntity.getCrawlerResponse().getCanonical_type());

					// canonical_url_is_consistent
					urlMetricsEntityV3.setCanonical_url_is_consistent(htmlClickHouseEntity.getCrawlerResponse().getCanonical_url_is_consistent());

					// content_type
					urlMetricsEntityV3.setContent_type(htmlClickHouseEntity.getCrawlerResponse().getContent_type());

					// description
					urlMetricsEntityV3.setDescription(htmlClickHouseEntity.getCrawlerResponse().getDescription());

					// description_flg
					urlMetricsEntityV3.setDescription_flg(htmlClickHouseEntity.getCrawlerResponse().getDescription_flg());

					// description_length
					urlMetricsEntityV3.setDescription_length(htmlClickHouseEntity.getCrawlerResponse().getDescription_length());

					// description_simhash
					urlMetricsEntityV3.setDescription_simhash(htmlClickHouseEntity.getCrawlerResponse().getDescription_simhash());

					// error_message
					urlMetricsEntityV3.setError_message(htmlClickHouseEntity.getCrawlerResponse().getError_message());

					// final_response_code
					urlMetricsEntityV3.setFinal_response_code(htmlClickHouseEntity.getCrawlerResponse().getFinal_response_code());

					// follow_flg
					urlMetricsEntityV3.setFollow_flg(htmlClickHouseEntity.getCrawlerResponse().getFollow_flg());

					// follow_flg_x_tag
					urlMetricsEntityV3.setFollow_flg_x_tag(htmlClickHouseEntity.getCrawlerResponse().getFollow_flg_x_tag());

					// h1_array
					urlMetricsEntityV3.setH1_array(htmlClickHouseEntity.getCrawlerResponse().getH1());

					// h1_count
					urlMetricsEntityV3.setH1_count(htmlClickHouseEntity.getCrawlerResponse().getH1_count());

					// h1_flg
					urlMetricsEntityV3.setH1_flg(htmlClickHouseEntity.getCrawlerResponse().getH1_flg());

					// h1_length
					urlMetricsEntityV3.setH1_length(htmlClickHouseEntity.getCrawlerResponse().getH1_length());

					// h1_md5
					urlMetricsEntityV3.setH1_md5(htmlClickHouseEntity.getCrawlerResponse().getH1_md5());

					// h2_array
					if (StringUtils.equalsIgnoreCase(urlMetricsEntityV3.getUrl(), IConstants.URL_WITH_TOO_MANY_H2) == false) {
						urlMetricsEntityV3.setH2_array(htmlClickHouseEntity.getCrawlerResponse().getH2());
					}

					// header_noarchive
					urlMetricsEntityV3.setHeader_noarchive(htmlClickHouseEntity.getCrawlerResponse().getHeader_noarchive());

					// header_nofollow
					urlMetricsEntityV3.setHeader_nofollow(htmlClickHouseEntity.getCrawlerResponse().getHeader_nofollow());

					// header_noindex
					urlMetricsEntityV3.setHeader_noindex(htmlClickHouseEntity.getCrawlerResponse().getHeader_noindex());

					// header_noodp
					urlMetricsEntityV3.setHeader_noodp(htmlClickHouseEntity.getCrawlerResponse().getHeader_noodp());

					// header_nosnippet
					urlMetricsEntityV3.setHeader_nosnippet(htmlClickHouseEntity.getCrawlerResponse().getHeader_nosnippet());

					// header_noydir
					urlMetricsEntityV3.setHeader_noydir(htmlClickHouseEntity.getCrawlerResponse().getHeader_noydir());

					// hreflang_links_out_count
					urlMetricsEntityV3.setHreflang_links_out_count(htmlClickHouseEntity.getCrawlerResponse().getHreflang_links_out_count());

					// hreflang_url_count
					urlMetricsEntityV3.setHreflang_url_count(htmlClickHouseEntity.getCrawlerResponse().getHreflang_url_count());

					// index_flg
					urlMetricsEntityV3.setIndex_flg(htmlClickHouseEntity.getCrawlerResponse().getIndex_flg());

					// index_flg_x_tag
					urlMetricsEntityV3.setIndex_flg_x_tag(htmlClickHouseEntity.getCrawlerResponse().getIndex_flg_x_tag());

					// indexable
					urlMetricsEntityV3.setIndexable(htmlClickHouseEntity.getCrawlerResponse().getIndexable());

					// insecure_resources_flag
					urlMetricsEntityV3.setInsecure_resources_flag(htmlClickHouseEntity.getCrawlerResponse().getInsecure_resources_flag());

					// meta_charset
					urlMetricsEntityV3.setMeta_charset(htmlClickHouseEntity.getCrawlerResponse().getMeta_charset());

					// meta_content_type
					urlMetricsEntityV3.setMeta_content_type(htmlClickHouseEntity.getCrawlerResponse().getMeta_content_type());

					// meta_disabled_sitelinks
					urlMetricsEntityV3.setMeta_disabled_sitelinks(htmlClickHouseEntity.getCrawlerResponse().getMeta_disabled_sitelinks());

					// meta_noodp
					urlMetricsEntityV3.setMeta_noodp(htmlClickHouseEntity.getCrawlerResponse().getMeta_noodp());

					// meta_nosnippet
					urlMetricsEntityV3.setMeta_nosnippet(htmlClickHouseEntity.getCrawlerResponse().getMeta_nosnippet());

					// meta_noydir
					urlMetricsEntityV3.setMeta_noydir(htmlClickHouseEntity.getCrawlerResponse().getMeta_noydir());

					// meta_redirect
					urlMetricsEntityV3.setMeta_redirect(htmlClickHouseEntity.getCrawlerResponse().getMeta_redirect());

					// mixed_redirects
					urlMetricsEntityV3.setMixed_redirects(htmlClickHouseEntity.getCrawlerResponse().getMixed_redirects());

					// mobile_rel_alternate_url_is_consistent
					urlMetricsEntityV3.setMobile_rel_alternate_url_is_consistent(htmlClickHouseEntity.getCrawlerResponse().getMobile_rel_alternate_url_is_consistent());

					// noodp
					urlMetricsEntityV3.setNoodp(htmlClickHouseEntity.getCrawlerResponse().getNoodp());

					// nosnippet
					urlMetricsEntityV3.setNosnippet(htmlClickHouseEntity.getCrawlerResponse().getNosnippet());

					// noydir
					urlMetricsEntityV3.setNoydir(htmlClickHouseEntity.getCrawlerResponse().getNoydir());

					// og_markup
					urlMetricsEntityV3.setOg_markup(htmlClickHouseEntity.getCrawlerResponse().getOg_markup());

					// og_markup_flag
					urlMetricsEntityV3.setOg_markup_flag(htmlClickHouseEntity.getCrawlerResponse().getOg_markup_flag());

					// og_markup_length
					urlMetricsEntityV3.setOg_markup_length(htmlClickHouseEntity.getCrawlerResponse().getOg_markup_length());

					// outlink_count
					urlMetricsEntityV3.setOutlink_count(htmlClickHouseEntity.getCrawlerResponse().getOutlink_count());

					// page_link
					urlMetricsEntityV3.setPage_link(htmlClickHouseEntity.getCrawlerResponse().getPage_link());

					// redirect_blocked
					urlMetricsEntityV3.setRedirect_blocked(htmlClickHouseEntity.getCrawlerResponse().getRedirect_blocked());

					// redirect_blocked_reason
					urlMetricsEntityV3.setRedirect_blocked_reason(htmlClickHouseEntity.getCrawlerResponse().getRedirect_blocked_reason());

					// redirect_flg
					urlMetricsEntityV3.setRedirect_flg(htmlClickHouseEntity.getCrawlerResponse().getRedirect_flg());

					// redirect_times
					urlMetricsEntityV3.setRedirect_times(htmlClickHouseEntity.getCrawlerResponse().getRedirect_times());

					// response_headers (names)
					urlMetricsEntityV3.setResponse_header_names(
							CrawlerUtils.getInstance().getResponseHeaderNames(htmlClickHouseEntity.getCrawlerResponse().getResponse_headers()));

					// robots
					urlMetricsEntityV3.setRobots(htmlClickHouseEntity.getCrawlerResponse().getRobots());

					// robots_contents
					urlMetricsEntityV3.setRobots_contents(htmlClickHouseEntity.getCrawlerResponse().getRobots_contents());

					// robots_contents_x_tag
					urlMetricsEntityV3.setRobots_contents_x_tag(htmlClickHouseEntity.getCrawlerResponse().getRobots_contents_x_tag());

					// robots_flg
					urlMetricsEntityV3.setRobots_flg(htmlClickHouseEntity.getCrawlerResponse().getRobots_flg());

					// robots_flg_x_tag
					urlMetricsEntityV3.setRobots_flg_x_tag(htmlClickHouseEntity.getCrawlerResponse().getRobots_flg_x_tag());

					// title
					urlMetricsEntityV3.setTitle(htmlClickHouseEntity.getCrawlerResponse().getTitle());

					// title_flg
					urlMetricsEntityV3.setTitle_flg(htmlClickHouseEntity.getCrawlerResponse().getTitle_flg());

					// title_length
					urlMetricsEntityV3.setTitle_length(htmlClickHouseEntity.getCrawlerResponse().getTitle_length());

					// title_md5
					urlMetricsEntityV3.setTitle_md5(htmlClickHouseEntity.getCrawlerResponse().getTitle_md5());

					// title_simhash
					urlMetricsEntityV3.setTitle_simhash(htmlClickHouseEntity.getCrawlerResponse().getTitle_simhash());

					// viewport_flag
					urlMetricsEntityV3.setViewport_flag(htmlClickHouseEntity.getCrawlerResponse().getViewport_flag());

					// page_analysis_results
					urlMetricsEntityV3.setPageAnalysisResultArray(htmlClickHouseEntity.getPageAnalysisResultArray());
					if (htmlClickHouseEntity.getPageAnalysisResultArray() != null && htmlClickHouseEntity.getPageAnalysisResultArray().length > 0) {
						urlMetricsEntityV3.setPageAnalysisResultInd(true);
					} else {
						urlMetricsEntityV3.setPageAnalysisResultInd(false);
					}

					// change_tracking_hash_cd_json
					urlMetricsEntityV3.setChangeTrackingHashCdJsonArray(htmlClickHouseEntity.getChangeTrackingHashCdJsonArray());

					// base_tag
					urlMetricsEntityV3.setBase_tag(htmlClickHouseEntity.getCrawlerResponse().getBase_tag());

					// base_tag_flag
					urlMetricsEntityV3.setBase_tag_flag(htmlClickHouseEntity.getCrawlerResponse().getBase_tag_flag());

					// base_tag_target
					urlMetricsEntityV3.setBase_tag_target(htmlClickHouseEntity.getCrawlerResponse().getBase_tag_target());
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return urlMetricsEntityV3;
	}

	private TargetUrlsDifferencesResponse getTargetUrlsDifferencesResponse(HtmlClickHouseEntity htmlClickHouseEntity1, HtmlClickHouseEntity htmlClickHouseEntity2)
			throws Exception {
		TargetUrlsDifferencesResponse targetUrlsDifferencesResponse = new TargetUrlsDifferencesResponse();

		// change indicators are from 'htmlClickHouseEntity2'

		// alternate_links_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getAlternateLinksChgInd())) {
			targetUrlsDifferencesResponse.setAlternate_links_chg_ind(htmlClickHouseEntity2.getAlternateLinksChgInd());
			targetUrlsDifferencesResponse.setAlternate_links_1(htmlClickHouseEntity1.getCrawlerResponse().getAlternate_links());
			targetUrlsDifferencesResponse.setAlternate_links_2(htmlClickHouseEntity2.getCrawlerResponse().getAlternate_links());
		}

		// amphtml_href_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getAmphtmlHrefChgInd())) {
			targetUrlsDifferencesResponse.setAmphtml_href_chg_ind(htmlClickHouseEntity2.getAmphtmlHrefChgInd());
			targetUrlsDifferencesResponse.setAmphtml_href_1(htmlClickHouseEntity1.getCrawlerResponse().getAmphtml_href());
			targetUrlsDifferencesResponse.setAmphtml_href_2(htmlClickHouseEntity2.getCrawlerResponse().getAmphtml_href());
		}

		// analyzed_url_s_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getAnalyzedUrlSChgInd())) {
			targetUrlsDifferencesResponse.setAnalyzed_url_s_chg_ind(htmlClickHouseEntity2.getAnalyzedUrlSChgInd());
			targetUrlsDifferencesResponse.setAnalyzed_url_s_1(htmlClickHouseEntity1.getCrawlerResponse().getAnalyzed_url_s());
			targetUrlsDifferencesResponse.setAnalyzed_url_s_2(htmlClickHouseEntity2.getCrawlerResponse().getAnalyzed_url_s());
		}

		// archive_flg_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getArchiveFlgChgInd())) {
			targetUrlsDifferencesResponse.setArchive_flg_chg_ind(htmlClickHouseEntity2.getArchiveFlgChgInd());
			targetUrlsDifferencesResponse.setArchive_flg_1(htmlClickHouseEntity1.getCrawlerResponse().getArchive_flg());
			targetUrlsDifferencesResponse.setArchive_flg_2(htmlClickHouseEntity2.getCrawlerResponse().getArchive_flg());
		}

		// base_tag_added_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getBaseTagAddedInd())) {
			targetUrlsDifferencesResponse.setBase_tag_added_ind(htmlClickHouseEntity2.getBaseTagAddedInd());
			targetUrlsDifferencesResponse.setBase_tag_flag_1(htmlClickHouseEntity1.getCrawlerResponse().getBase_tag_flag());
			targetUrlsDifferencesResponse.setBase_tag_flag_2(htmlClickHouseEntity2.getCrawlerResponse().getBase_tag_flag());
		}

		// base_tag_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getBaseTagChgInd())) {
			targetUrlsDifferencesResponse.setBase_tag_chg_ind(htmlClickHouseEntity2.getBaseTagChgInd());
			targetUrlsDifferencesResponse.setBase_tag_1(htmlClickHouseEntity1.getCrawlerResponse().getBase_tag());
			targetUrlsDifferencesResponse.setBase_tag_2(htmlClickHouseEntity2.getCrawlerResponse().getBase_tag());
		}

		// base_tag_removed_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getBaseTagRemovedInd())) {
			targetUrlsDifferencesResponse.setBase_tag_removed_ind(htmlClickHouseEntity2.getBaseTagRemovedInd());
			targetUrlsDifferencesResponse.setBase_tag_flag_1(htmlClickHouseEntity1.getCrawlerResponse().getBase_tag_flag());
			targetUrlsDifferencesResponse.setBase_tag_flag_2(htmlClickHouseEntity2.getCrawlerResponse().getBase_tag_flag());
		}

		// base_tag_target_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getBaseTagTargetChgInd())) {
			targetUrlsDifferencesResponse.setBase_tag_target_chg_ind(htmlClickHouseEntity2.getBaseTagTargetChgInd());
			targetUrlsDifferencesResponse.setBase_tag_target_1(htmlClickHouseEntity1.getCrawlerResponse().getBase_tag_target());
			targetUrlsDifferencesResponse.setBase_tag_target_2(htmlClickHouseEntity2.getCrawlerResponse().getBase_tag_target());
		}

		// blocked_by_robots_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getBlockedByRobotsChgInd())) {
			targetUrlsDifferencesResponse.setBlocked_by_robots_chg_ind(htmlClickHouseEntity2.getBlockedByRobotsChgInd());
			targetUrlsDifferencesResponse.setBlocked_by_robots_1(htmlClickHouseEntity1.getCrawlerResponse().getBlocked_by_robots());
			targetUrlsDifferencesResponse.setBlocked_by_robots_2(htmlClickHouseEntity2.getCrawlerResponse().getBlocked_by_robots());
		}

		// canonical_added_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getCanonicalAddedInd())) {
			targetUrlsDifferencesResponse.setCanonical_added_ind(htmlClickHouseEntity2.getCanonicalAddedInd());
			targetUrlsDifferencesResponse.setCanonical_flg_1(htmlClickHouseEntity1.getCrawlerResponse().getCanonical_flg());
			targetUrlsDifferencesResponse.setCanonical_flg_2(htmlClickHouseEntity2.getCrawlerResponse().getCanonical_flg());
		}

		// canonical_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getCanonicalChgInd())) {
			targetUrlsDifferencesResponse.setCanonical_chg_ind(htmlClickHouseEntity2.getCanonicalChgInd());
			targetUrlsDifferencesResponse.setCanonical_1(htmlClickHouseEntity1.getCrawlerResponse().getCanonical());
			targetUrlsDifferencesResponse.setCanonical_2(htmlClickHouseEntity2.getCrawlerResponse().getCanonical());
		}

		// canonical_header_flag_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getCanonicalHeaderFlagChgInd())) {
			targetUrlsDifferencesResponse.setCanonical_header_flag_chg_ind(htmlClickHouseEntity2.getCanonicalHeaderFlagChgInd());
			targetUrlsDifferencesResponse.setCanonical_header_flag_1(htmlClickHouseEntity1.getCrawlerResponse().getCanonical_header_flag());
			targetUrlsDifferencesResponse.setCanonical_header_flag_2(htmlClickHouseEntity2.getCrawlerResponse().getCanonical_header_flag());
		}

		// canonical_header_type_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getCanonicalHeaderTypeChgInd())) {
			targetUrlsDifferencesResponse.setCanonical_header_type_chg_ind(htmlClickHouseEntity2.getCanonicalHeaderTypeChgInd());
			targetUrlsDifferencesResponse.setCanonical_header_type_1(htmlClickHouseEntity1.getCrawlerResponse().getCanonical_header_type());
			targetUrlsDifferencesResponse.setCanonical_header_type_2(htmlClickHouseEntity2.getCrawlerResponse().getCanonical_header_type());
		}

		// canonical_removed_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getCanonicalRemovedInd())) {
			targetUrlsDifferencesResponse.setCanonical_removed_ind(htmlClickHouseEntity2.getCanonicalRemovedInd());
			targetUrlsDifferencesResponse.setCanonical_flg_1(htmlClickHouseEntity1.getCrawlerResponse().getCanonical_flg());
			targetUrlsDifferencesResponse.setCanonical_flg_2(htmlClickHouseEntity2.getCrawlerResponse().getCanonical_flg());
		}

		// canonical_type_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getCanonicalTypeChgInd())) {
			targetUrlsDifferencesResponse.setCanonical_type_chg_ind(htmlClickHouseEntity2.getCanonicalTypeChgInd());
			targetUrlsDifferencesResponse.setCanonical_type_1(htmlClickHouseEntity1.getCrawlerResponse().getCanonical_type());
			targetUrlsDifferencesResponse.setCanonical_type_2(htmlClickHouseEntity2.getCrawlerResponse().getCanonical_type());
		}

		// canonical_url_is_consistent_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getCanonicalUrlIsConsistentChgInd())) {
			targetUrlsDifferencesResponse.setCanonical_url_is_consistent_chg_ind(htmlClickHouseEntity2.getCanonicalUrlIsConsistentChgInd());
			targetUrlsDifferencesResponse.setCanonical_url_is_consistent_1(htmlClickHouseEntity1.getCrawlerResponse().getCanonical_url_is_consistent());
			targetUrlsDifferencesResponse.setCanonical_url_is_consistent_2(htmlClickHouseEntity2.getCrawlerResponse().getCanonical_url_is_consistent());
		}

		// content_type_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getContentTypeChgInd())) {
			targetUrlsDifferencesResponse.setContent_type_chg_ind(htmlClickHouseEntity2.getContentTypeChgInd());
			targetUrlsDifferencesResponse.setContent_type_1(htmlClickHouseEntity1.getCrawlerResponse().getContent_type());
			targetUrlsDifferencesResponse.setContent_type_2(htmlClickHouseEntity2.getCrawlerResponse().getContent_type());
		}

		// custom_data_added_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getCustomDataAddedInd())) {
			targetUrlsDifferencesResponse.setCustom_data_added_ind(htmlClickHouseEntity2.getCustomDataAddedInd());
			targetUrlsDifferencesResponse.setCustom_data_1(htmlClickHouseEntity1.getCrawlerResponse().getCustom_data());
			targetUrlsDifferencesResponse.setCustom_data_2(htmlClickHouseEntity2.getCrawlerResponse().getCustom_data());
		}

		// custom_data_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getCustomDataChgInd())) {
			targetUrlsDifferencesResponse.setCustom_data_chg_ind(htmlClickHouseEntity2.getCustomDataChgInd());
			targetUrlsDifferencesResponse.setCustom_data_1(htmlClickHouseEntity1.getCrawlerResponse().getCustom_data());
			targetUrlsDifferencesResponse.setCustom_data_2(htmlClickHouseEntity2.getCrawlerResponse().getCustom_data());
		}

		// custom_data_removed_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getCustomDataRemovedInd())) {
			targetUrlsDifferencesResponse.setCustom_data_removed_ind(htmlClickHouseEntity2.getCustomDataRemovedInd());
			targetUrlsDifferencesResponse.setCustom_data_1(htmlClickHouseEntity1.getCrawlerResponse().getCustom_data());
			targetUrlsDifferencesResponse.setCustom_data_2(htmlClickHouseEntity2.getCrawlerResponse().getCustom_data());
		}

		// description_added_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getDescriptionAddedInd())) {
			targetUrlsDifferencesResponse.setDescription_added_ind(htmlClickHouseEntity2.getDescriptionAddedInd());
			targetUrlsDifferencesResponse.setDescription_flg_1(htmlClickHouseEntity1.getCrawlerResponse().getDescription_flg());
			targetUrlsDifferencesResponse.setDescription_flg_2(htmlClickHouseEntity2.getCrawlerResponse().getDescription_flg());
		}

		// description_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getDescriptionChgInd())) {
			targetUrlsDifferencesResponse.setDescription_chg_ind(htmlClickHouseEntity2.getDescriptionChgInd());
			targetUrlsDifferencesResponse.setDescription_1(htmlClickHouseEntity1.getCrawlerResponse().getDescription());
			targetUrlsDifferencesResponse.setDescription_2(htmlClickHouseEntity2.getCrawlerResponse().getDescription());
		}

		// description_length_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getDescriptionLengthChgInd())) {
			targetUrlsDifferencesResponse.setDescription_length_chg_ind(htmlClickHouseEntity2.getDescriptionLengthChgInd());
			targetUrlsDifferencesResponse.setDescription_length_1(htmlClickHouseEntity1.getCrawlerResponse().getDescription_length());
			targetUrlsDifferencesResponse.setDescription_length_2(htmlClickHouseEntity2.getCrawlerResponse().getDescription_length());
		}

		// description_removed_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getDescriptionRemovedInd())) {
			targetUrlsDifferencesResponse.setDescription_removed_ind(htmlClickHouseEntity2.getDescriptionRemovedInd());
			targetUrlsDifferencesResponse.setDescription_flg_1(htmlClickHouseEntity1.getCrawlerResponse().getDescription_flg());
			targetUrlsDifferencesResponse.setDescription_flg_2(htmlClickHouseEntity2.getCrawlerResponse().getDescription_flg());
		}

		// error_message_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getErrorMessageChgInd())) {
			targetUrlsDifferencesResponse.setError_message_chg_ind(htmlClickHouseEntity2.getErrorMessageChgInd());
			targetUrlsDifferencesResponse.setError_message_1(htmlClickHouseEntity1.getCrawlerResponse().getError_message());
			targetUrlsDifferencesResponse.setError_message_2(htmlClickHouseEntity2.getCrawlerResponse().getError_message());
		}

		// final_response_code_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getFinalResponseCodeChgInd())) {
			targetUrlsDifferencesResponse.setFinal_response_code_chg_ind(htmlClickHouseEntity2.getFinalResponseCodeChgInd());
			targetUrlsDifferencesResponse.setFinal_response_code_1(htmlClickHouseEntity1.getCrawlerResponse().getFinal_response_code());
			targetUrlsDifferencesResponse.setFinal_response_code_2(htmlClickHouseEntity2.getCrawlerResponse().getFinal_response_code());
		}

		// follow_flg_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getFollowFlgChgInd())) {
			targetUrlsDifferencesResponse.setFollow_flg_chg_ind(htmlClickHouseEntity2.getFollowFlgChgInd());
			targetUrlsDifferencesResponse.setFollow_flg_1(htmlClickHouseEntity1.getCrawlerResponse().getFollow_flg());
			targetUrlsDifferencesResponse.setFollow_flg_2(htmlClickHouseEntity2.getCrawlerResponse().getFollow_flg());
		}

		// h1_added_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getH1AddedInd())) {
			targetUrlsDifferencesResponse.setH1_added_ind(htmlClickHouseEntity2.getH1AddedInd());
			targetUrlsDifferencesResponse.setH1_count_1(htmlClickHouseEntity1.getCrawlerResponse().getH1_count());
			targetUrlsDifferencesResponse.setH1_count_2(htmlClickHouseEntity2.getCrawlerResponse().getH1_count());
		}

		// h1_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getH1ChgInd())) {
			targetUrlsDifferencesResponse.setH1_chg_ind(htmlClickHouseEntity2.getH1ChgInd());
			targetUrlsDifferencesResponse.setH1_1(htmlClickHouseEntity1.getCrawlerResponse().getH1());
			targetUrlsDifferencesResponse.setH1_2(htmlClickHouseEntity2.getCrawlerResponse().getH1());
		}

		// h1_count_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getH1CountChgInd())) {
			targetUrlsDifferencesResponse.setH1_count_chg_ind(htmlClickHouseEntity2.getH1CountChgInd());
			targetUrlsDifferencesResponse.setH1_count_1(htmlClickHouseEntity1.getCrawlerResponse().getH1_count());
			targetUrlsDifferencesResponse.setH1_count_2(htmlClickHouseEntity2.getCrawlerResponse().getH1_count());
		}

		// h1_length_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getH1LengthChgInd())) {
			targetUrlsDifferencesResponse.setH1_length_chg_ind(htmlClickHouseEntity2.getH1LengthChgInd());
			targetUrlsDifferencesResponse.setH1_length_1(htmlClickHouseEntity1.getCrawlerResponse().getH1_length());
			targetUrlsDifferencesResponse.setH1_length_2(htmlClickHouseEntity2.getCrawlerResponse().getH1_length());
		}

		// h1_removed_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getH1RemovedInd())) {
			targetUrlsDifferencesResponse.setH1_removed_ind(htmlClickHouseEntity2.getH1RemovedInd());
			targetUrlsDifferencesResponse.setH1_count_1(htmlClickHouseEntity1.getCrawlerResponse().getH1_count());
			targetUrlsDifferencesResponse.setH1_count_2(htmlClickHouseEntity2.getCrawlerResponse().getH1_count());
		}

		// h2_added_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getH2AddedInd())) {
			targetUrlsDifferencesResponse.setH2_added_ind(htmlClickHouseEntity2.getH2AddedInd());
			targetUrlsDifferencesResponse.setH2_1(htmlClickHouseEntity1.getCrawlerResponse().getH2());
			targetUrlsDifferencesResponse.setH2_2(htmlClickHouseEntity2.getCrawlerResponse().getH2());
		}

		// h2_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getH2ChgInd())) {
			targetUrlsDifferencesResponse.setH2_chg_ind(htmlClickHouseEntity2.getH2ChgInd());
			targetUrlsDifferencesResponse.setH2_1(htmlClickHouseEntity1.getCrawlerResponse().getH2());
			targetUrlsDifferencesResponse.setH2_2(htmlClickHouseEntity2.getCrawlerResponse().getH2());
		}

		// h2_removed_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getH2RemovedInd())) {
			targetUrlsDifferencesResponse.setH2_removed_ind(htmlClickHouseEntity2.getH2RemovedInd());
			targetUrlsDifferencesResponse.setH2_1(htmlClickHouseEntity1.getCrawlerResponse().getH2());
			targetUrlsDifferencesResponse.setH2_2(htmlClickHouseEntity2.getCrawlerResponse().getH2());
		}

		// header_noarchive_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getHeaderNoarchiveChgInd())) {
			targetUrlsDifferencesResponse.setHeader_noarchive_chg_ind(htmlClickHouseEntity2.getHeaderNoarchiveChgInd());
			targetUrlsDifferencesResponse.setHeader_noarchive_1(htmlClickHouseEntity1.getCrawlerResponse().getHeader_noarchive());
			targetUrlsDifferencesResponse.setHeader_noarchive_2(htmlClickHouseEntity2.getCrawlerResponse().getHeader_noarchive());
		}

		// header_nofollow_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getHeaderNofollowChgInd())) {
			targetUrlsDifferencesResponse.setHeader_nofollow_chg_ind(htmlClickHouseEntity2.getHeaderNofollowChgInd());
			targetUrlsDifferencesResponse.setHeader_nofollow_1(htmlClickHouseEntity1.getCrawlerResponse().getHeader_nofollow());
			targetUrlsDifferencesResponse.setHeader_nofollow_2(htmlClickHouseEntity2.getCrawlerResponse().getHeader_nofollow());
		}

		// header_noindex_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getHeaderNoindexChgInd())) {
			targetUrlsDifferencesResponse.setHeader_noindex_chg_ind(htmlClickHouseEntity2.getHeaderNoindexChgInd());
			targetUrlsDifferencesResponse.setHeader_noindex_1(htmlClickHouseEntity1.getCrawlerResponse().getHeader_noindex());
			targetUrlsDifferencesResponse.setHeader_noindex_2(htmlClickHouseEntity2.getCrawlerResponse().getHeader_noindex());
		}

		// header_noodp_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getHeaderNoodpChgInd())) {
			targetUrlsDifferencesResponse.setHeader_noodp_chg_ind(htmlClickHouseEntity2.getHeaderNoodpChgInd());
			targetUrlsDifferencesResponse.setHeader_noodp_1(htmlClickHouseEntity1.getCrawlerResponse().getHeader_noodp());
			targetUrlsDifferencesResponse.setHeader_noodp_2(htmlClickHouseEntity2.getCrawlerResponse().getHeader_noodp());
		}

		// header_nosnippet_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getHeaderNosnippetChgInd())) {
			targetUrlsDifferencesResponse.setHeader_nosnippet_chg_ind(htmlClickHouseEntity2.getHeaderNosnippetChgInd());
			targetUrlsDifferencesResponse.setHeader_nosnippet_1(htmlClickHouseEntity1.getCrawlerResponse().getHeader_nosnippet());
			targetUrlsDifferencesResponse.setHeader_nosnippet_2(htmlClickHouseEntity2.getCrawlerResponse().getHeader_nosnippet());
		}

		// header_noydir_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getHeaderNoydirChgInd())) {
			targetUrlsDifferencesResponse.setHeader_noydir_chg_ind(htmlClickHouseEntity2.getHeaderNoydirChgInd());
			targetUrlsDifferencesResponse.setHeader_noydir_1(htmlClickHouseEntity1.getCrawlerResponse().getHeader_noydir());
			targetUrlsDifferencesResponse.setHeader_noydir_2(htmlClickHouseEntity2.getCrawlerResponse().getHeader_noydir());
		}

		// hreflang_errors_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getHreflangErrorsChgInd())) {
			targetUrlsDifferencesResponse.setHreflang_errors_chg_ind(htmlClickHouseEntity2.getHreflangErrorsChgInd());
			targetUrlsDifferencesResponse.setHreflang_errors_1(htmlClickHouseEntity1.getCrawlerResponse().getHreflang_errors());
			targetUrlsDifferencesResponse.setHreflang_errors_2(htmlClickHouseEntity2.getCrawlerResponse().getHreflang_errors());
		}

		// hreflang_links_added_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getHreflangLinksAddedInd())) {
			targetUrlsDifferencesResponse.setHreflang_links_added_ind(htmlClickHouseEntity2.getHreflangLinksAddedInd());
			targetUrlsDifferencesResponse.setHreflang_url_count_1(htmlClickHouseEntity1.getCrawlerResponse().getHreflang_url_count());
			targetUrlsDifferencesResponse.setHreflang_url_count_2(htmlClickHouseEntity2.getCrawlerResponse().getHreflang_url_count());
		}

		// hreflang_links_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getHreflangLinksChgInd())) {
			targetUrlsDifferencesResponse.setHreflang_links_chg_ind(htmlClickHouseEntity2.getHreflangLinksChgInd());
			targetUrlsDifferencesResponse.setHreflang_links_1(htmlClickHouseEntity1.getCrawlerResponse().getHreflang_links());
			targetUrlsDifferencesResponse.setHreflang_links_2(htmlClickHouseEntity2.getCrawlerResponse().getHreflang_links());
		}

		// hreflang_links_out_count_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getHreflangLinksOutCountChgInd())) {
			targetUrlsDifferencesResponse.setHreflang_links_out_count_chg_ind(htmlClickHouseEntity2.getHreflangLinksOutCountChgInd());
			targetUrlsDifferencesResponse.setHreflang_links_out_count_1(htmlClickHouseEntity1.getCrawlerResponse().getHreflang_links_out_count());
			targetUrlsDifferencesResponse.setHreflang_links_out_count_2(htmlClickHouseEntity2.getCrawlerResponse().getHreflang_links_out_count());
		}

		// hreflang_links_removed_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getHreflangLinksRemovedInd())) {
			targetUrlsDifferencesResponse.setHreflang_links_removed_ind(htmlClickHouseEntity2.getHreflangLinksRemovedInd());
			targetUrlsDifferencesResponse.setHreflang_url_count_1(htmlClickHouseEntity1.getCrawlerResponse().getHreflang_url_count());
			targetUrlsDifferencesResponse.setHreflang_url_count_2(htmlClickHouseEntity2.getCrawlerResponse().getHreflang_url_count());
		}

		// hreflang_url_count_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getHreflangUrlCountChgInd())) {
			targetUrlsDifferencesResponse.setHreflang_url_count_chg_ind(htmlClickHouseEntity2.getHreflangUrlCountChgInd());
			targetUrlsDifferencesResponse.setHreflang_url_count_1(htmlClickHouseEntity1.getCrawlerResponse().getHreflang_url_count());
			targetUrlsDifferencesResponse.setHreflang_url_count_2(htmlClickHouseEntity2.getCrawlerResponse().getHreflang_url_count());
		}

		// indexable_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getIndexableChgInd())) {
			targetUrlsDifferencesResponse.setIndexable_chg_ind(htmlClickHouseEntity2.getIndexableChgInd());
			targetUrlsDifferencesResponse.setIndexable_1(htmlClickHouseEntity1.getCrawlerResponse().getIndexable());
			targetUrlsDifferencesResponse.setIndexable_2(htmlClickHouseEntity2.getCrawlerResponse().getIndexable());
		}

		// index_flg_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getIndexFlgChgInd())) {
			targetUrlsDifferencesResponse.setIndex_flg_chg_ind(htmlClickHouseEntity2.getIndexFlgChgInd());
			targetUrlsDifferencesResponse.setIndex_flg_1(htmlClickHouseEntity1.getCrawlerResponse().getIndex_flg());
			targetUrlsDifferencesResponse.setIndex_flg_2(htmlClickHouseEntity2.getCrawlerResponse().getIndex_flg());
		}

		// insecure_resources_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getInsecureResourcesChgInd())) {
			targetUrlsDifferencesResponse.setInsecure_resources_chg_ind(htmlClickHouseEntity2.getInsecureResourcesChgInd());
			targetUrlsDifferencesResponse.setInsecure_resources_1(htmlClickHouseEntity1.getCrawlerResponse().getInsecure_resources());
			targetUrlsDifferencesResponse.setInsecure_resources_2(htmlClickHouseEntity2.getCrawlerResponse().getInsecure_resources());
		}

		// meta_charset_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getMetaCharsetChgInd())) {
			targetUrlsDifferencesResponse.setMeta_charset_chg_ind(htmlClickHouseEntity2.getMetaCharsetChgInd());
			targetUrlsDifferencesResponse.setMeta_charset_1(htmlClickHouseEntity1.getCrawlerResponse().getMeta_charset());
			targetUrlsDifferencesResponse.setMeta_charset_2(htmlClickHouseEntity2.getCrawlerResponse().getMeta_charset());
		}

		// meta_content_type_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getMetaContentTypeChgInd())) {
			targetUrlsDifferencesResponse.setMeta_content_type_chg_ind(htmlClickHouseEntity2.getMetaContentTypeChgInd());
			targetUrlsDifferencesResponse.setMeta_content_type_1(htmlClickHouseEntity1.getCrawlerResponse().getMeta_content_type());
			targetUrlsDifferencesResponse.setMeta_content_type_2(htmlClickHouseEntity2.getCrawlerResponse().getMeta_content_type());
		}

		// meta_disabled_sitelinks_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getMetaDisabledSitelinksChgInd())) {
			targetUrlsDifferencesResponse.setMeta_disabled_sitelinks_chg_ind(htmlClickHouseEntity2.getMetaDisabledSitelinksChgInd());
			targetUrlsDifferencesResponse.setMeta_disabled_sitelinks_1(htmlClickHouseEntity1.getCrawlerResponse().getMeta_disabled_sitelinks());
			targetUrlsDifferencesResponse.setMeta_disabled_sitelinks_2(htmlClickHouseEntity2.getCrawlerResponse().getMeta_disabled_sitelinks());
		}

		// meta_noodp_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getMetaNoodpChgInd())) {
			targetUrlsDifferencesResponse.setMeta_noodp_chg_ind(htmlClickHouseEntity2.getMetaNoodpChgInd());
			targetUrlsDifferencesResponse.setMeta_noodp_1(htmlClickHouseEntity1.getCrawlerResponse().getMeta_noodp());
			targetUrlsDifferencesResponse.setMeta_noodp_2(htmlClickHouseEntity2.getCrawlerResponse().getMeta_noodp());
		}

		// meta_nosnippet_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getMetaNosnippetChgInd())) {
			targetUrlsDifferencesResponse.setMeta_nosnippet_chg_ind(htmlClickHouseEntity2.getMetaNosnippetChgInd());
			targetUrlsDifferencesResponse.setMeta_nosnippet_1(htmlClickHouseEntity1.getCrawlerResponse().getMeta_nosnippet());
			targetUrlsDifferencesResponse.setMeta_nosnippet_2(htmlClickHouseEntity2.getCrawlerResponse().getMeta_nosnippet());
		}

		// meta_noydir_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getMetaNoydirChgInd())) {
			targetUrlsDifferencesResponse.setMeta_noydir_chg_ind(htmlClickHouseEntity2.getMetaNoydirChgInd());
			targetUrlsDifferencesResponse.setMeta_noydir_1(htmlClickHouseEntity1.getCrawlerResponse().getMeta_noydir());
			targetUrlsDifferencesResponse.setMeta_noydir_2(htmlClickHouseEntity2.getCrawlerResponse().getMeta_noydir());
		}

		// meta_redirect_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getMetaRedirectChgInd())) {
			targetUrlsDifferencesResponse.setMeta_redirect_chg_ind(htmlClickHouseEntity2.getMetaRedirectChgInd());
			targetUrlsDifferencesResponse.setMeta_redirect_1(htmlClickHouseEntity1.getCrawlerResponse().getMeta_redirect());
			targetUrlsDifferencesResponse.setMeta_redirect_2(htmlClickHouseEntity2.getCrawlerResponse().getMeta_redirect());
		}

		// mixed_redirects_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getMixedRedirectsChgInd())) {
			targetUrlsDifferencesResponse.setMixed_redirects_chg_ind(htmlClickHouseEntity2.getMixedRedirectsChgInd());
			targetUrlsDifferencesResponse.setMixed_redirects_1(htmlClickHouseEntity1.getCrawlerResponse().getMixed_redirects());
			targetUrlsDifferencesResponse.setMixed_redirects_2(htmlClickHouseEntity2.getCrawlerResponse().getMixed_redirects());
		}

		// mobile_rel_alternate_url_is_consistent_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getMobileRelAlternateUrlIsConsistentChgInd())) {
			targetUrlsDifferencesResponse.setMobile_rel_alternate_url_is_consistent_chg_ind(htmlClickHouseEntity2.getMobileRelAlternateUrlIsConsistentChgInd());
			targetUrlsDifferencesResponse
					.setMobile_rel_alternate_url_is_consistent_1(htmlClickHouseEntity1.getCrawlerResponse().getMobile_rel_alternate_url_is_consistent());
			targetUrlsDifferencesResponse
					.setMobile_rel_alternate_url_is_consistent_2(htmlClickHouseEntity2.getCrawlerResponse().getMobile_rel_alternate_url_is_consistent());
		}

		// noodp_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getNoodpChgInd())) {
			targetUrlsDifferencesResponse.setNoodp_chg_ind(htmlClickHouseEntity2.getNoodpChgInd());
			targetUrlsDifferencesResponse.setNoodp_1(htmlClickHouseEntity1.getCrawlerResponse().getNoodp());
			targetUrlsDifferencesResponse.setNoodp_2(htmlClickHouseEntity2.getCrawlerResponse().getNoodp());
		}

		// nosnippet_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getNosnippetChgInd())) {
			targetUrlsDifferencesResponse.setNosnippet_chg_ind(htmlClickHouseEntity2.getNosnippetChgInd());
			targetUrlsDifferencesResponse.setNosnippet_1(htmlClickHouseEntity1.getCrawlerResponse().getNosnippet());
			targetUrlsDifferencesResponse.setNosnippet_2(htmlClickHouseEntity2.getCrawlerResponse().getNosnippet());
		}

		// noydir_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getNoydirChgInd())) {
			targetUrlsDifferencesResponse.setNoydir_chg_ind(htmlClickHouseEntity2.getNoydirChgInd());
			targetUrlsDifferencesResponse.setNoydir_1(htmlClickHouseEntity1.getCrawlerResponse().getNoydir());
			targetUrlsDifferencesResponse.setNoydir_2(htmlClickHouseEntity2.getCrawlerResponse().getNoydir());
		}

		// og_markup_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getOgMarkupChgInd())) {
			targetUrlsDifferencesResponse.setOg_markup_chg_ind(htmlClickHouseEntity2.getOgMarkupChgInd());
			targetUrlsDifferencesResponse.setOg_markup_1(htmlClickHouseEntity1.getCrawlerResponse().getOg_markup());
			targetUrlsDifferencesResponse.setOg_markup_2(htmlClickHouseEntity2.getCrawlerResponse().getOg_markup());
		}

		// og_markup_length_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getOgMarkupLengthChgInd())) {
			targetUrlsDifferencesResponse.setOg_markup_length_chg_ind(htmlClickHouseEntity2.getOgMarkupLengthChgInd());
			targetUrlsDifferencesResponse.setOg_markup_length_1(htmlClickHouseEntity1.getCrawlerResponse().getOg_markup_length());
			targetUrlsDifferencesResponse.setOg_markup_length_2(htmlClickHouseEntity2.getCrawlerResponse().getOg_markup_length());
		}

		// open_graph_added_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getOpenGraphAddedInd())) {
			targetUrlsDifferencesResponse.setOpen_graph_added_ind(htmlClickHouseEntity2.getOpenGraphAddedInd());
			targetUrlsDifferencesResponse.setOg_markup_flag_1(htmlClickHouseEntity1.getCrawlerResponse().getOg_markup_flag());
			targetUrlsDifferencesResponse.setOg_markup_flag_2(htmlClickHouseEntity2.getCrawlerResponse().getOg_markup_flag());
		}

		// open_graph_removed_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getOpenGraphRemovedInd())) {
			targetUrlsDifferencesResponse.setOpen_graph_removed_ind(htmlClickHouseEntity2.getOpenGraphRemovedInd());
			targetUrlsDifferencesResponse.setOg_markup_flag_1(htmlClickHouseEntity1.getCrawlerResponse().getOg_markup_flag());
			targetUrlsDifferencesResponse.setOg_markup_flag_2(htmlClickHouseEntity2.getCrawlerResponse().getOg_markup_flag());
		}

		// outlink_count_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getOutlinkCountChgInd())) {
			targetUrlsDifferencesResponse.setOutlink_count_chg_ind(htmlClickHouseEntity2.getOutlinkCountChgInd());
			targetUrlsDifferencesResponse.setOutlink_count_1(htmlClickHouseEntity1.getCrawlerResponse().getOutlink_count());
			targetUrlsDifferencesResponse.setOutlink_count_2(htmlClickHouseEntity2.getCrawlerResponse().getOutlink_count());
		}

		// page_analysis_results_chg_ind_json
		if (StringUtils.isNotBlank(htmlClickHouseEntity2.getPageAnalysisResultsChgIndJson())) {
			targetUrlsDifferencesResponse.setPage_analysis_results_chg_ind_json(htmlClickHouseEntity2.getPageAnalysisResultsChgIndJson());
		}

		// page_link_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getPageLinkChgInd())) {
			targetUrlsDifferencesResponse.setPage_link_chg_ind(htmlClickHouseEntity2.getPageLinkChgInd());
			targetUrlsDifferencesResponse.setPage_link_1(htmlClickHouseEntity1.getCrawlerResponse().getPage_link());
			targetUrlsDifferencesResponse.setPage_link_2(htmlClickHouseEntity2.getCrawlerResponse().getPage_link());
		}

		// redirect_301_detected_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getRedirect301DetectedInd())) {
			targetUrlsDifferencesResponse.setRedirect_301_detected_ind(htmlClickHouseEntity2.getRedirect301DetectedInd());
			targetUrlsDifferencesResponse.setResponse_code_1(htmlClickHouseEntity1.getCrawlerResponse().getResponse_code());
			targetUrlsDifferencesResponse.setResponse_code_2(htmlClickHouseEntity2.getCrawlerResponse().getResponse_code());
		}

		// redirect_301_removed_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getRedirect301RemovedInd())) {
			targetUrlsDifferencesResponse.setRedirect_301_removed_ind(htmlClickHouseEntity2.getRedirect301RemovedInd());
			targetUrlsDifferencesResponse.setResponse_code_1(htmlClickHouseEntity1.getCrawlerResponse().getResponse_code());
			targetUrlsDifferencesResponse.setResponse_code_2(htmlClickHouseEntity2.getCrawlerResponse().getResponse_code());
		}

		// redirect_302_detected_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getRedirect302DetectedInd())) {
			targetUrlsDifferencesResponse.setRedirect_302_detected_ind(htmlClickHouseEntity2.getRedirect302DetectedInd());
			targetUrlsDifferencesResponse.setResponse_code_1(htmlClickHouseEntity1.getCrawlerResponse().getResponse_code());
			targetUrlsDifferencesResponse.setResponse_code_2(htmlClickHouseEntity2.getCrawlerResponse().getResponse_code());
		}

		// redirect_302_removed_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getRedirect302RemovedInd())) {
			targetUrlsDifferencesResponse.setRedirect_302_removed_ind(htmlClickHouseEntity2.getRedirect302RemovedInd());
			targetUrlsDifferencesResponse.setResponse_code_1(htmlClickHouseEntity1.getCrawlerResponse().getResponse_code());
			targetUrlsDifferencesResponse.setResponse_code_2(htmlClickHouseEntity2.getCrawlerResponse().getResponse_code());
		}

		// redirect_blocked_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getRedirectBlockedChgInd())) {
			targetUrlsDifferencesResponse.setRedirect_blocked_chg_ind(htmlClickHouseEntity2.getRedirectBlockedChgInd());
			targetUrlsDifferencesResponse.setRedirect_blocked_1(htmlClickHouseEntity1.getCrawlerResponse().getRedirect_blocked());
			targetUrlsDifferencesResponse.setRedirect_blocked_2(htmlClickHouseEntity2.getCrawlerResponse().getRedirect_blocked());
		}

		// redirect_blocked_reason_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getRedirectBlockedReasonChgInd())) {
			targetUrlsDifferencesResponse.setRedirect_blocked_reason_chg_ind(htmlClickHouseEntity2.getRedirectBlockedReasonChgInd());
			targetUrlsDifferencesResponse.setRedirect_blocked_reason_1(htmlClickHouseEntity1.getCrawlerResponse().getRedirect_blocked_reason());
			targetUrlsDifferencesResponse.setRedirect_blocked_reason_2(htmlClickHouseEntity2.getCrawlerResponse().getRedirect_blocked_reason());
		}

		// redirect_chain_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getRedirectChainChgInd())) {
			targetUrlsDifferencesResponse.setRedirect_chain_chg_ind(htmlClickHouseEntity2.getRedirectChainChgInd());
			targetUrlsDifferencesResponse.setRedirect_chain_1(htmlClickHouseEntity1.getCrawlerResponse().getRedirect_chain());
			targetUrlsDifferencesResponse.setRedirect_chain_2(htmlClickHouseEntity2.getCrawlerResponse().getRedirect_chain());
		}

		// redirect_diff_code_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getRedirectDiffCodeInd())) {
			targetUrlsDifferencesResponse.setRedirect_diff_code_ind(htmlClickHouseEntity2.getRedirectDiffCodeInd());
			targetUrlsDifferencesResponse.setResponse_code_1(htmlClickHouseEntity1.getCrawlerResponse().getResponse_code());
			targetUrlsDifferencesResponse.setResponse_code_2(htmlClickHouseEntity2.getCrawlerResponse().getResponse_code());
		}

		// redirect_final_url_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getRedirectFinalUrlChgInd())) {
			targetUrlsDifferencesResponse.setRedirect_final_url_chg_ind(htmlClickHouseEntity2.getRedirectFinalUrlChgInd());
			targetUrlsDifferencesResponse.setRedirect_final_url_1(htmlClickHouseEntity1.getCrawlerResponse().getRedirect_final_url());
			targetUrlsDifferencesResponse.setRedirect_final_url_2(htmlClickHouseEntity2.getCrawlerResponse().getRedirect_final_url());
		}

		// redirect_times_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getRedirectTimesChgInd())) {
			targetUrlsDifferencesResponse.setRedirect_times_chg_ind(htmlClickHouseEntity2.getRedirectTimesChgInd());
			targetUrlsDifferencesResponse.setRedirect_times_1(htmlClickHouseEntity1.getCrawlerResponse().getRedirect_times());
			targetUrlsDifferencesResponse.setRedirect_times_2(htmlClickHouseEntity2.getCrawlerResponse().getRedirect_times());
		}

		// response_code_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getResponseCodeChgInd())) {
			targetUrlsDifferencesResponse.setResponse_code_chg_ind(htmlClickHouseEntity2.getResponseCodeChgInd());
			targetUrlsDifferencesResponse.setResponse_code_1(htmlClickHouseEntity1.getCrawlerResponse().getResponse_code());
			targetUrlsDifferencesResponse.setResponse_code_2(htmlClickHouseEntity2.getCrawlerResponse().getResponse_code());
		}

		// response_headers_added_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getResponseHeadersAddedInd())) {
			targetUrlsDifferencesResponse.setResponse_headers_added_ind(htmlClickHouseEntity2.getResponseHeadersAddedInd());
			targetUrlsDifferencesResponse
					.setResponse_header_names_1(CrawlerUtils.getInstance().getResponseHeaderNames(htmlClickHouseEntity1.getCrawlerResponse().getResponse_headers()));
			targetUrlsDifferencesResponse
					.setResponse_header_names_2(CrawlerUtils.getInstance().getResponseHeaderNames(htmlClickHouseEntity2.getCrawlerResponse().getResponse_headers()));
		}

		// response_headers_removed_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getResponseHeadersRemovedInd())) {
			targetUrlsDifferencesResponse.setResponse_headers_removed_ind(htmlClickHouseEntity2.getResponseHeadersRemovedInd());
			targetUrlsDifferencesResponse
					.setResponse_header_names_1(CrawlerUtils.getInstance().getResponseHeaderNames(htmlClickHouseEntity1.getCrawlerResponse().getResponse_headers()));
			targetUrlsDifferencesResponse
					.setResponse_header_names_2(CrawlerUtils.getInstance().getResponseHeaderNames(htmlClickHouseEntity2.getCrawlerResponse().getResponse_headers()));
		}

		// robots_added_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getRobotsAddedInd())) {
			targetUrlsDifferencesResponse.setRobots_added_ind(htmlClickHouseEntity2.getRobotsAddedInd());
			targetUrlsDifferencesResponse.setRobots_flg_1(htmlClickHouseEntity1.getCrawlerResponse().getRobots_flg());
			targetUrlsDifferencesResponse.setRobots_flg_2(htmlClickHouseEntity2.getCrawlerResponse().getRobots_flg());
		}

		// robots_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getRobotsChgInd())) {
			targetUrlsDifferencesResponse.setRobots_chg_ind(htmlClickHouseEntity2.getRobotsChgInd());
			targetUrlsDifferencesResponse.setRobots_1(htmlClickHouseEntity1.getCrawlerResponse().getRobots());
			targetUrlsDifferencesResponse.setRobots_2(htmlClickHouseEntity2.getCrawlerResponse().getRobots());
		}

		// robots_contents_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getRobotsContentsChgInd())) {
			targetUrlsDifferencesResponse.setRobots_contents_chg_ind(htmlClickHouseEntity2.getRobotsContentsChgInd());
			targetUrlsDifferencesResponse.setRobots_contents_1(htmlClickHouseEntity1.getCrawlerResponse().getRobots_contents());
			targetUrlsDifferencesResponse.setRobots_contents_2(htmlClickHouseEntity2.getCrawlerResponse().getRobots_contents());
		}

		// robots_removed_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getRobotsRemovedInd())) {
			targetUrlsDifferencesResponse.setRobots_removed_ind(htmlClickHouseEntity2.getRobotsRemovedInd());
			targetUrlsDifferencesResponse.setRobots_flg_1(htmlClickHouseEntity1.getCrawlerResponse().getRobots_flg());
			targetUrlsDifferencesResponse.setRobots_flg_2(htmlClickHouseEntity2.getCrawlerResponse().getRobots_flg());
		}

		// structured_data_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getStructuredDataChgInd())) {
			targetUrlsDifferencesResponse.setStructured_data_chg_ind(htmlClickHouseEntity2.getStructuredDataChgInd());
			targetUrlsDifferencesResponse.setStructured_data_1(htmlClickHouseEntity1.getCrawlerResponse().getStructured_data());
			targetUrlsDifferencesResponse.setStructured_data_2(htmlClickHouseEntity2.getCrawlerResponse().getStructured_data());
		}

		// title_added_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getTitleAddedInd())) {
			targetUrlsDifferencesResponse.setTitle_added_ind(htmlClickHouseEntity2.getTitleAddedInd());
			targetUrlsDifferencesResponse.setTitle_flg_1(htmlClickHouseEntity1.getCrawlerResponse().getTitle_flg());
			targetUrlsDifferencesResponse.setTitle_flg_2(htmlClickHouseEntity2.getCrawlerResponse().getTitle_flg());
		}

		// title_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getTitleChgInd())) {
			targetUrlsDifferencesResponse.setTitle_chg_ind(htmlClickHouseEntity2.getTitleChgInd());
			targetUrlsDifferencesResponse.setTitle_1(htmlClickHouseEntity1.getCrawlerResponse().getTitle());
			targetUrlsDifferencesResponse.setTitle_2(htmlClickHouseEntity2.getCrawlerResponse().getTitle());
		}

		// title_length_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getTitleLengthChgInd())) {
			targetUrlsDifferencesResponse.setTitle_length_chg_ind(htmlClickHouseEntity2.getTitleLengthChgInd());
			targetUrlsDifferencesResponse.setTitle_length_1(htmlClickHouseEntity1.getCrawlerResponse().getTitle_length());
			targetUrlsDifferencesResponse.setTitle_length_2(htmlClickHouseEntity2.getCrawlerResponse().getTitle_length());
		}

		// title_removed_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getTitleRemovedInd())) {
			targetUrlsDifferencesResponse.setTitle_removed_ind(htmlClickHouseEntity2.getTitleRemovedInd());
			targetUrlsDifferencesResponse.setTitle_flg_1(htmlClickHouseEntity1.getCrawlerResponse().getTitle_flg());
			targetUrlsDifferencesResponse.setTitle_flg_2(htmlClickHouseEntity2.getCrawlerResponse().getTitle_flg());
		}

		// viewport_added_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getViewportAddedInd())) {
			targetUrlsDifferencesResponse.setViewport_added_ind(htmlClickHouseEntity2.getViewportAddedInd());
			targetUrlsDifferencesResponse.setViewport_flag_1(htmlClickHouseEntity1.getCrawlerResponse().getViewport_flag());
			targetUrlsDifferencesResponse.setViewport_flag_2(htmlClickHouseEntity2.getCrawlerResponse().getViewport_flag());
		}

		// viewport_content_chg_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getViewportContentChgInd())) {
			targetUrlsDifferencesResponse.setViewport_content_chg_ind(htmlClickHouseEntity2.getViewportContentChgInd());
			targetUrlsDifferencesResponse.setViewport_content_1(htmlClickHouseEntity1.getCrawlerResponse().getViewport_content());
			targetUrlsDifferencesResponse.setViewport_content_2(htmlClickHouseEntity2.getCrawlerResponse().getViewport_content());
		}

		// viewport_removed_ind
		if (BooleanUtils.isTrue(htmlClickHouseEntity2.getViewportRemovedInd())) {
			targetUrlsDifferencesResponse.setViewport_removed_ind(htmlClickHouseEntity2.getViewportRemovedInd());
			targetUrlsDifferencesResponse.setViewport_flag_1(htmlClickHouseEntity1.getCrawlerResponse().getViewport_flag());
			targetUrlsDifferencesResponse.setViewport_flag_2(htmlClickHouseEntity2.getCrawlerResponse().getViewport_flag());
		}

		return targetUrlsDifferencesResponse;
	}
}
