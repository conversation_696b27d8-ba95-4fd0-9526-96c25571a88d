package com.actonia.web.service;

import java.util.Properties;

import com.actonia.IConstants;

public final class ContentGuardWebServiceMessage {

	private static Properties prop = new Properties();

	public static String getStringProperty(String key, String defaultValue) {
		if (prop == null || prop.getProperty(key) == null) {
			return defaultValue;
		}
		return prop.getProperty(key);
	}

	public static String getStringProperty(String key) {
		if (prop == null || prop.getProperty(key) == null) {
			return prop.getProperty(IConstants.MSG_CD_MSG_CD_NOT_PROVIDED);
		}
		return prop.getProperty(key);
	}

	public static int getIntProperty(String key, int defaultValue) {
		if (prop == null || prop.getProperty(key) == null) {
			return defaultValue;
		}
		return Integer.parseInt(prop.getProperty(key));
	}

	public static short getShortProperty(String key, short defaultValue) {
		if (prop == null || prop.getProperty(key) == null) {
			return defaultValue;
		}
		return Short.parseShort(prop.getProperty(key));
	}

	public static long getLongProperty(String key, long defaultValue) {
		if (prop == null || prop.getProperty(key) == null) {
			return defaultValue;
		}
		return Long.parseLong(prop.getProperty(key));
	}

	public static boolean getBooleanProperty(String key, boolean defaultValue) {
		if (prop == null || prop.getProperty(key) == null) {
			return defaultValue;
		}
		return prop.getProperty(key).toLowerCase().trim().equals("true");
	}

	static {
		try {
			prop.load(ContentGuardWebServiceMessage.class.getClassLoader().getResourceAsStream("content_guard_messages.properties"));
		} catch (Exception e) {
			prop = null;
			System.err.println("WARNING: Could not find content_guard_messages.properties file in class path. use the default values.");
		}
	}
}
