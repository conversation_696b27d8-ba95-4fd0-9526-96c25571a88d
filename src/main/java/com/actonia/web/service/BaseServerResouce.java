package com.actonia.web.service;

import java.io.IOException;
import java.text.MessageFormat;

import org.apache.commons.lang.StringUtils;
import org.restlet.data.MediaType;
import org.restlet.ext.jackson.JacksonRepresentation;
import org.restlet.ext.xml.DomRepresentation;
import org.restlet.representation.Representation;
import org.restlet.resource.ServerResource;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

import com.actonia.utils.FormatUtils;
import com.actonia.value.object.ErrorValueObject;
import com.actonia.value.object.TargetUrlsDifferencesResponse;

public class BaseServerResouce extends ServerResource {
	public DomRepresentation generateXmlError(String errorCode) {
		String errorMessage = PoliteCrawlServiceConfigurations.getStringProperty(errorCode);
		DomRepresentation result = null;
		try {
			result = new DomRepresentation(MediaType.TEXT_XML);
			Document d = result.getDocument();
			Element r = d.createElement("errors");
			d.appendChild(r);

			Element eltError = d.createElement("error");

			Element eltCode = d.createElement("code");
			eltCode.appendChild(d.createTextNode(errorCode));
			eltError.appendChild(eltCode);

			Element eltMessage = d.createElement("message");
			eltMessage.appendChild(d.createTextNode(errorMessage));
			eltError.appendChild(eltMessage);

			r.appendChild(eltError);
			d.normalizeDocument();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return result;
	}

	public Representation generatePoliteCrawlJsonError(String errorCode) {
		return generatePoliteCrawlJsonError(errorCode, null);
	}

	public Representation generatePoliteCrawlJsonError(String errorCode, String supplementalMessageText) {
		FormatUtils.getInstance().logMemoryUsage("generatePoliteCrawlJsonError() errorCode=" + errorCode + ",supplementalMessageText=" + supplementalMessageText);
		String errorMessage = getPoliteCrawlErrorMessage(errorCode, supplementalMessageText);
		TargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = new TargetUrlsDifferencesResponse();
		ErrorValueObject errorValueObject = new ErrorValueObject();
		errorValueObject.setError_code(errorCode);
		errorValueObject.setError_message(errorMessage);
		findTargetUrlsDifferencesResponse.setError(errorValueObject);
		return new JacksonRepresentation<TargetUrlsDifferencesResponse>(findTargetUrlsDifferencesResponse);
	}

	public String getPoliteCrawlErrorMessage(String errorCode) {
		return getPoliteCrawlErrorMessage(errorCode, null);
	}

	public String getPoliteCrawlErrorMessage(String errorCode, String supplementalMessageText) {
		String errorMessage = null;
		String errorTemplate = PoliteCrawlServiceConfigurations.getStringProperty(errorCode);
		FormatUtils.getInstance().logMemoryUsage("getPoliteCrawlErrorMessage() errorTemplate=" + errorTemplate);
		if (StringUtils.isNotBlank(supplementalMessageText)) {
			errorMessage = MessageFormat.format(errorTemplate, supplementalMessageText);
		} else {
			errorMessage = errorTemplate;
		}
		FormatUtils.getInstance().logMemoryUsage("getPoliteCrawlErrorMessage() errorMessage=" + errorMessage);
		return errorMessage;
	}
}
