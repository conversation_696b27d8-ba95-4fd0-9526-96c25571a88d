package com.actonia.web.service;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.net.URL;
import java.util.Date;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.restlet.ext.jackson.JacksonRepresentation;
import org.restlet.representation.Representation;
import org.restlet.resource.Post;
import org.restlet.resource.ResourceException;

import com.actonia.IConstants;
import com.actonia.dao.ContentGuardGroupDAO;
import com.actonia.entity.ContentGuardChangeTrackingEntity;
import com.actonia.entity.ContentGuardGroupEntity;
import com.actonia.service.AccessTokenService;
import com.actonia.service.ContentGuardService;
import com.actonia.utils.ContentGuardUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.ContentGuardResourceRequest;
import com.actonia.value.object.ContentGuardResourceResponse;
import com.actonia.value.object.ValidationError;
import com.actonia.value.object.WebServiceError;
import com.google.gson.Gson;

public class ContentGuardResource extends BaseServerResouce {

	private String command;

	private ContentGuardService contentGuardService;

	private ContentGuardGroupDAO contentGuardGroupDAO;

	private AccessTokenService accessTokenService;

	private static final String[] VALID_COMMAND_ARRAY = new String[] { IConstants.COMMAND_TIMELINE, IConstants.COMMAND_DOMAIN_SUMMARY, IConstants.COMMAND_URL_DETAILS,
			IConstants.COMMAND_INDICATOR_URL_LIST, IConstants.COMMAND_URL_ALL_DETAILS, IConstants.COMMAND_PAGE_ANALYSIS_ISSUES, IConstants.COMMAND_TRACKED_PAGES,
			IConstants.COMMAND_USAGE, };

	public ContentGuardResource() {
		super();
		contentGuardService = SpringBeanFactory.getBean("contentGuardService");
		contentGuardGroupDAO = SpringBeanFactory.getBean("contentGuardGroupDAO");
		accessTokenService = SpringBeanFactory.getBean("accessTokenService");
	}

	@Override
	protected void doInit() throws ResourceException {
		this.command = (String) getRequest().getAttributes().get("command");
	}

	@Post("application/json")
	public Representation doPost(Representation representationInput) {

		Representation representationOutput = null;
		String requestParameters = null;
		ContentGuardResourceRequest contentGuardResourceRequest = null;
		ContentGuardResourceResponse contentGuardResourceResponse = null;
		String accessToken = null;
		boolean isCommandValid = false;
		String accessTokenValidationErrorCode = null;
		String testFilterChangeIndicator = null;

		try {
			isCommandValid = validateCommand();
			if (isCommandValid == false) {
				return generateJsonError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_COMMAND_INVALID, command);
			}

			if (representationInput == null) {
				return generateJsonError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_JSON_REQUIRED);
			}

			requestParameters = representationInput.getText();
			FormatUtils.getInstance().logMemoryUsage("doPost() command=" + command + ",requestParameters=" + requestParameters);
			if (StringUtils.isBlank(requestParameters)) {
				return generateJsonError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_JSON_REQUIRED);
			}

			contentGuardResourceRequest = new Gson().fromJson(requestParameters, ContentGuardResourceRequest.class);
			FormatUtils.getInstance().logMemoryUsage("doPost() contentGuardResourceRequest=" + contentGuardResourceRequest.toString());

			accessToken = contentGuardResourceRequest.getAccess_token();
			if (StringUtils.isBlank(accessToken)) {
				return generateJsonError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_ACCESS_TOKEN_REQUIRED);
			} else if (StringUtils.equals(accessToken, IConstants.INTERNAL_KEY) == false
					&& StringUtils.equalsIgnoreCase(accessToken, AccessTokenService.INTERNAL_KEY) == false) {
				accessTokenValidationErrorCode = accessTokenService.checkToken(accessToken);
				if (StringUtils.isNotBlank(accessTokenValidationErrorCode)) {
					return generateJsonError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_ACCESS_TOKEN_INVALID, accessToken);
				}
			}

			representationOutput = validateRequest(contentGuardResourceRequest);
			// when validation failed, return the error JSON representation
			if (representationOutput != null) {
				return representationOutput;
			}

			contentGuardResourceResponse = new ContentGuardResourceResponse();

			// command:timeline
			if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_TIMELINE)) {
				contentGuardResourceResponse = contentGuardService.getTimeline(contentGuardResourceRequest.getDomain_id(), contentGuardResourceRequest.getGroup_id(),
						contentGuardResourceRequest.getStart_crawl_date(), contentGuardResourceRequest.getEnd_crawl_date(), contentGuardResourceRequest.getUseCustomIndicators());
			}
			// command:domain_summary
			else if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_DOMAIN_SUMMARY)) {
				contentGuardResourceResponse = contentGuardService.getDomainSummary(contentGuardResourceRequest.getDomain_id(),
						contentGuardResourceRequest.getGroup_id(), contentGuardResourceRequest.getCrawl_date(), contentGuardResourceRequest.getCrawl_hour(),
						contentGuardResourceRequest.getPage_number(), contentGuardResourceRequest.getRows_per_page(), contentGuardResourceRequest.getSort_by(),
						contentGuardResourceRequest.getReturn_details(), contentGuardResourceRequest.getFilter_url(), contentGuardResourceRequest.getUseCustomIndicators());
			}
			// command:url_change_details
			else if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_URL_DETAILS)) {
				contentGuardResourceResponse = contentGuardService.getUrlDetails(contentGuardResourceRequest.getDomain_id(), contentGuardResourceRequest.getGroup_id(),
						contentGuardResourceRequest.getUrl(), contentGuardResourceRequest.getCrawl_timestamp());
			}
			// command:indicator_url_list
			else if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_INDICATOR_URL_LIST)) {
				if (StringUtils.startsWithIgnoreCase(contentGuardResourceRequest.getFilter_change_indicator(), IConstants.RESPONSE_CODE_CHG_IND)) {
					testFilterChangeIndicator = IConstants.RESPONSE_CODE_CHG_IND;
				} else {
					testFilterChangeIndicator = contentGuardResourceRequest.getFilter_change_indicator();
				}
				contentGuardResourceResponse = contentGuardService.getIndicatorUrlList(contentGuardResourceRequest.getDomain_id(),
						contentGuardResourceRequest.getGroup_id(), contentGuardResourceRequest.getCrawl_date(), contentGuardResourceRequest.getCrawl_hour(),
						contentGuardResourceRequest.getPage_number(), contentGuardResourceRequest.getRows_per_page(), contentGuardResourceRequest.getReturn_details(),
						testFilterChangeIndicator);
			}
			// command:url_all_details
			else if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_URL_ALL_DETAILS)) {
				contentGuardResourceResponse = contentGuardService.getUrlAllDetails(contentGuardResourceRequest.getDomain_id(),
						contentGuardResourceRequest.getGroup_id(), contentGuardResourceRequest.getUrl(), contentGuardResourceRequest.getPage_number(),
						contentGuardResourceRequest.getRows_per_page());
			}
			// command:page_analysis_issues
			else if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_PAGE_ANALYSIS_ISSUES)) {
				contentGuardResourceResponse = contentGuardService.getPageAnalysisIssues(contentGuardResourceRequest.getDomain_id(),
						contentGuardResourceRequest.getGroup_id(), contentGuardResourceRequest.getCrawl_date(), contentGuardResourceRequest.getCrawl_hour());
			}
			// command:tracked_pages
			else if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_TRACKED_PAGES)) {
				contentGuardResourceResponse = contentGuardService.getTrackedPages(contentGuardResourceRequest.getDomain_id(),
						contentGuardResourceRequest.getGroup_id(), contentGuardResourceRequest.getPage_number(), contentGuardResourceRequest.getRows_per_page(),
						contentGuardResourceRequest.getSort_by(), contentGuardResourceRequest.getFilter_url());
			}
			// command:usage
			else if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_USAGE)) {
				contentGuardResourceResponse = contentGuardService.getUsage(contentGuardResourceRequest.getDomain_id(),
						contentGuardResourceRequest.getStart_usage_date(), contentGuardResourceRequest.getEnd_usage_date(), contentGuardResourceRequest.getGroup_id());
			}

			FormatUtils.getInstance().logMemoryUsage("doPost() ends. contentGuardResourceResponse.getSuccess()=" + contentGuardResourceResponse.getSuccess());

			return new JacksonRepresentation<ContentGuardResourceResponse>(contentGuardResourceResponse);
		} catch (Exception e) {
			e.printStackTrace();
			StringWriter stringWriter = new StringWriter();
			e.printStackTrace(new PrintWriter(stringWriter));

			// send support alert
			String message = stringWriter.toString();

			return generateJsonError(IConstants.MSG_CD_CONTENT_GUARD_WEB_SERVICE_METHOD_EXCEPTION, message);

		}
	}

	private boolean validateCommand() {
		boolean output = false;
		if (StringUtils.isNotBlank(command)) {
			nextValidCommand: for (String validCommand : VALID_COMMAND_ARRAY) {
				if (StringUtils.equalsIgnoreCase(command, validCommand)) {
					output = true;
					break nextValidCommand;
				}
			}
		}
		return output;
	}

	private Representation validateRequest(ContentGuardResourceRequest contentGuardResourceRequest) throws Exception {
		Representation representation = null;
		ContentGuardResourceResponse contentGuardResourceResponse = null;
		WebServiceError webServiceError = null;

		ValidationError validationError = getValidationErrorCode(contentGuardResourceRequest);

		if (validationError != null) {
			contentGuardResourceResponse = new ContentGuardResourceResponse();
			webServiceError = new WebServiceError();
			webServiceError.setError_code(validationError.getErrorCode());
			webServiceError.setError_message(ContentGuardUtils.getInstance().getErrorMessage(validationError.getErrorCode(), validationError.getErrorData()));
			contentGuardResourceResponse.setError(webServiceError);
			contentGuardResourceResponse.setSuccess(false);
			representation = new JacksonRepresentation<ContentGuardResourceResponse>(contentGuardResourceResponse);
		}

		return representation;
	}

	private ValidationError getValidationErrorCode(ContentGuardResourceRequest contentGuardResourceRequest) throws Exception {
		ValidationError validationError = null;
		ContentGuardGroupEntity contentGuardGroupEntity = null;
		int totalUrls = 0;
		ContentGuardChangeTrackingEntity contentGuardChangeTrackingEntity = null;
		URL url = null;

		// 'domain_id' is required
		if (contentGuardResourceRequest.getDomain_id() == null) {
			return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_DOMAIN_ID_REQUIRED);
		}

		// 'group_id' is required when command is not 'usage'
		if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_USAGE) == false) {
			if (contentGuardResourceRequest.getGroup_id() == null) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_GROUP_ID_REQUIRED);
			}
		}

		if (contentGuardResourceRequest.getGroup_id() != null) {
			// when 'group_id' is not in 'content_guard_group' MySQL table
			contentGuardGroupEntity = contentGuardGroupDAO.get(contentGuardResourceRequest.getDomain_id(), contentGuardResourceRequest.getGroup_id());
			if (contentGuardGroupEntity == null) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_GROUP_ID_INVALID, String.valueOf(contentGuardResourceRequest.getGroup_id()));
			}
			// when there is no URL for the 'group_id' in 'content_guard_group' & 'content_guard_url' MySQL table
			else {
				totalUrls = contentGuardGroupDAO.getTotalUrls(contentGuardResourceRequest.getDomain_id(), contentGuardResourceRequest.getGroup_id());
				if (totalUrls == 0) {
					return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_GROUP_ID_DOES_NOT_HAVE_URLS,
							String.valueOf(contentGuardResourceRequest.getGroup_id()));
				}
			}
		}

		// command:tracked_pages
		if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_TRACKED_PAGES)) {
			// 'page_number' is not in request
			if (contentGuardResourceRequest.getPage_number() == null) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_PAGE_NUMBER_REQUIRED);
			}
			// 'rows_per_page' is not in request
			else if (contentGuardResourceRequest.getRows_per_page() == null) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_ROWS_PER_PAGE_REQUIRED);
			}
			// 'sort_by' is not in request
			else if (contentGuardResourceRequest.getSort_by() == null) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_SORT_BY_REQUIRED);
			}
			// 'page_number' in request must be > 0
			else if (contentGuardResourceRequest.getPage_number().intValue() < 1) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_PAGE_NUMBER_INVALID,
						String.valueOf(contentGuardResourceRequest.getPage_number()));
			}
			// 'rows_per_page' in request must be > 0
			else if (contentGuardResourceRequest.getRows_per_page().intValue() < 1) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_ROWS_PER_PAGE_INVALID,
						String.valueOf(contentGuardResourceRequest.getRows_per_page()));
			}
			// command:domain_change_summary only, 'sort_by' in request must be 1, 2, 3, 4, 13 and 14
			else if (contentGuardResourceRequest.getSort_by().intValue() != IConstants.SORT_BY_URL_ASC
					&& contentGuardResourceRequest.getSort_by().intValue() != IConstants.SORT_BY_URL_DESC
					&& contentGuardResourceRequest.getSort_by().intValue() != IConstants.SORT_BY_CRAWL_TIMESTAMP_ASC
					&& contentGuardResourceRequest.getSort_by().intValue() != IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC
					&& contentGuardResourceRequest.getSort_by().intValue() != IConstants.SORT_BY_RESPONSE_CODE_ASC
					&& contentGuardResourceRequest.getSort_by().intValue() != IConstants.SORT_BY_RESPONSE_CODE_DESC) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_SORT_BY_INVALID, String.valueOf(contentGuardResourceRequest.getSort_by()));
			}
		}
		// command:timeline
		else if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_TIMELINE)) {
			// 'crawl_date' is not allowed
			if (StringUtils.isNotBlank(contentGuardResourceRequest.getCrawl_date())) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_CRAWL_DATE_NOT_ALLOWED, contentGuardResourceRequest.getCrawl_date());
			}
			// 'crawl_hour' is not allowed
			else if (contentGuardResourceRequest.getCrawl_hour() != null) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_CRAWL_HOUR_NOT_ALLOWED,
						String.valueOf(contentGuardResourceRequest.getCrawl_hour()));
			}
			// 'crawl_timestamp' is not allowed
			else if (StringUtils.isNotBlank(contentGuardResourceRequest.getCrawl_timestamp())) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_CRAWL_TIMESTAMP_NOT_ALLOWED, contentGuardResourceRequest.getCrawl_timestamp());
			}
			// 'start_crawl_date' is required
			else if (StringUtils.isBlank(contentGuardResourceRequest.getStart_crawl_date())) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_START_CRAWL_DATE_REQUIRED);
			}
			// 'start_crawl_date' in request is invalid
			else if (isDateStringValid(contentGuardResourceRequest.getStart_crawl_date()) == false) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_START_CRAWL_DATE_INVALID, contentGuardResourceRequest.getStart_crawl_date());
			}
			// 'end_crawl_date' is required
			else if (StringUtils.isBlank(contentGuardResourceRequest.getEnd_crawl_date())) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_END_CRAWL_DATE_REQUIRED);
			}
			// 'end_crawl_date' in request is invalid
			else if (isDateStringValid(contentGuardResourceRequest.getEnd_crawl_date()) == false) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_END_CRAWL_DATE_INVALID, contentGuardResourceRequest.getEnd_crawl_date());
			}
			// 'start_crawl_date' in request is later 'end_crawl_date'
			else if (isDateStringValid(contentGuardResourceRequest.getStart_crawl_date(), contentGuardResourceRequest.getEnd_crawl_date()) == false) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_START_CRAWL_DATE_LATER_THAN_END_CRAWL_DATE,
						contentGuardResourceRequest.getStart_crawl_date());
			}
		}
		// command:domain_change_summary
		// command:indicator_url_list
		else if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_DOMAIN_SUMMARY)
				|| StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_INDICATOR_URL_LIST)) {
			// 'start_crawl_date' in request is not allowed
			if (StringUtils.isNotBlank(contentGuardResourceRequest.getStart_crawl_date())) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_START_CRAWL_DATE_NOT_ALLOWED, contentGuardResourceRequest.getStart_crawl_date());
			}
			// 'end_crawl_date' in request is not allowed
			else if (StringUtils.isNotBlank(contentGuardResourceRequest.getEnd_crawl_date())) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_END_CRAWL_DATE_NOT_ALLOWED, contentGuardResourceRequest.getEnd_crawl_date());
			}
			// 'crawl_timestamp' is not allowed
			else if (StringUtils.isNotBlank(contentGuardResourceRequest.getCrawl_timestamp())) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_CRAWL_TIMESTAMP_NOT_ALLOWED, contentGuardResourceRequest.getCrawl_timestamp());
			}
			// 'crawl_date' in request is invalid
			else if (StringUtils.isNotBlank(contentGuardResourceRequest.getCrawl_date()) && isDateStringValid(contentGuardResourceRequest.getCrawl_date()) == false) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_CRAWL_DATE_INVALID, contentGuardResourceRequest.getCrawl_date());
			}
			// 'crawl_hour' in request is invalid
			else if (contentGuardResourceRequest.getCrawl_hour() != null && isCrawlHourValid(contentGuardResourceRequest.getCrawl_hour()) == false) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_CRAWL_HOUR_INVALID, String.valueOf(contentGuardResourceRequest.getCrawl_hour()));
			}
			// when 'crawl_hour' is in request but 'crawl_date' is not in request
			else if (contentGuardResourceRequest.getCrawl_hour() != null && StringUtils.isBlank(contentGuardResourceRequest.getCrawl_date())) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_CRAWL_DATE_REQUIRED);
			}
			// 'page_number' is not in request
			else if (contentGuardResourceRequest.getPage_number() == null) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_PAGE_NUMBER_REQUIRED);
			}
			// 'rows_per_page' is not in request
			else if (contentGuardResourceRequest.getRows_per_page() == null) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_ROWS_PER_PAGE_REQUIRED);
			}
			// command:domain_change_summary only, 'sort_by' is not in request
			else if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_DOMAIN_SUMMARY) && contentGuardResourceRequest.getSort_by() == null) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_SORT_BY_REQUIRED);
			}
			// 'page_number' in request must be > 0
			else if (contentGuardResourceRequest.getPage_number().intValue() < 1) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_PAGE_NUMBER_INVALID,
						String.valueOf(contentGuardResourceRequest.getPage_number()));
			}
			// 'rows_per_page' in request must be > 0
			else if (contentGuardResourceRequest.getRows_per_page().intValue() < 1) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_ROWS_PER_PAGE_INVALID,
						String.valueOf(contentGuardResourceRequest.getRows_per_page()));
			}
			// command:domain_change_summary only, 'sort_by' in request must be >= 1 and <= 16
			else if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_DOMAIN_SUMMARY)
					&& (contentGuardResourceRequest.getSort_by().intValue() < 1 || contentGuardResourceRequest.getSort_by().intValue() > 16)) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_SORT_BY_INVALID, String.valueOf(contentGuardResourceRequest.getSort_by()));
			}
			// 'return_details' is not in request
			else if (contentGuardResourceRequest.getReturn_details() == null) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_RETURN_DETAILS_REQUIRED);
			}
			// command:indicator_url_list and 'filter_change_indicator' is in request
			else if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_INDICATOR_URL_LIST)
					&& StringUtils.isNotBlank(contentGuardResourceRequest.getFilter_change_indicator())) {

				// 'filter_change_indicator' but must be valid
				contentGuardChangeTrackingEntity = ContentGuardUtils.getInstance()
						.getContentGuardChangeTrackingEntity(contentGuardResourceRequest.getFilter_change_indicator());
				if (contentGuardChangeTrackingEntity == null) {
					return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_FILTER_CHANGE_INDICATOR_INVALID,
							contentGuardResourceRequest.getFilter_change_indicator());
				}

				// when filter change indicator by response codes, crawl_date is required.
				if (StringUtils.startsWithIgnoreCase(contentGuardResourceRequest.getFilter_change_indicator(), IConstants.RESPONSE_CODE_CHG_IND)
						&& StringUtils.isBlank(contentGuardResourceRequest.getCrawl_date())) {
					return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_CRAWL_DATE_REQUIRED);
				}
			}
		}
		// command:url_change_details
		else if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_URL_DETAILS)) {
			// 'start_crawl_date' in request is not allowed
			if (StringUtils.isNotBlank(contentGuardResourceRequest.getStart_crawl_date())) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_START_CRAWL_DATE_NOT_ALLOWED, contentGuardResourceRequest.getStart_crawl_date());
			}
			// 'end_crawl_date' in request is not allowed
			else if (StringUtils.isNotBlank(contentGuardResourceRequest.getEnd_crawl_date())) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_END_CRAWL_DATE_NOT_ALLOWED, contentGuardResourceRequest.getEnd_crawl_date());
			}
			// 'crawl_date' is not allowed
			else if (StringUtils.isNotBlank(contentGuardResourceRequest.getCrawl_date())) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_CRAWL_DATE_NOT_ALLOWED, contentGuardResourceRequest.getCrawl_date());
			}
			// 'crawl_hour' is not allowed
			else if (contentGuardResourceRequest.getCrawl_hour() != null) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_CRAWL_HOUR_NOT_ALLOWED,
						String.valueOf(contentGuardResourceRequest.getCrawl_hour()));
			}
			// 'url' is required
			else if (StringUtils.isBlank(contentGuardResourceRequest.getUrl())) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_URL_REQUIRED);
			}
			// 'crawl_timestamp' is required
			else if (StringUtils.isBlank(contentGuardResourceRequest.getCrawl_timestamp())) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_CRAWL_TIMESTAMP_REQUIRED);
			}
			// when 'crawl_timestamp' is invalid 
			else if (StringUtils.isNotBlank(contentGuardResourceRequest.getCrawl_timestamp())
					&& isCrawlTimestampValid(contentGuardResourceRequest.getCrawl_timestamp()) == false) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_CRAWL_TIMESTAMP_INVALID, contentGuardResourceRequest.getCrawl_timestamp());
			}
		}
		// command:url_all_details
		else if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_URL_ALL_DETAILS)) {
			// 'start_crawl_date' in request is not allowed
			if (StringUtils.isNotBlank(contentGuardResourceRequest.getStart_crawl_date())) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_START_CRAWL_DATE_NOT_ALLOWED, contentGuardResourceRequest.getStart_crawl_date());
			}
			// 'end_crawl_date' in request is not allowed
			else if (StringUtils.isNotBlank(contentGuardResourceRequest.getEnd_crawl_date())) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_END_CRAWL_DATE_NOT_ALLOWED, contentGuardResourceRequest.getEnd_crawl_date());
			}
			// 'crawl_date' is not allowed
			else if (StringUtils.isNotBlank(contentGuardResourceRequest.getCrawl_date())) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_CRAWL_DATE_NOT_ALLOWED, contentGuardResourceRequest.getCrawl_date());
			}
			// 'crawl_hour' is not allowed
			else if (contentGuardResourceRequest.getCrawl_hour() != null) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_CRAWL_HOUR_NOT_ALLOWED,
						String.valueOf(contentGuardResourceRequest.getCrawl_hour()));
			}
			// 'crawl_timestamp' is not allowed
			else if (StringUtils.isNotBlank(contentGuardResourceRequest.getCrawl_timestamp())) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_CRAWL_TIMESTAMP_NOT_ALLOWED, contentGuardResourceRequest.getCrawl_timestamp());
			}
			// 'url' is required
			else if (StringUtils.isBlank(contentGuardResourceRequest.getUrl())) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_URL_REQUIRED);
			}
			// 'page_number' is not in request
			else if (contentGuardResourceRequest.getPage_number() == null) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_PAGE_NUMBER_REQUIRED);
			}
			// 'rows_per_page' is not in request
			else if (contentGuardResourceRequest.getRows_per_page() == null) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_ROWS_PER_PAGE_REQUIRED);
			}
			// 'page_number' in request must be > 0
			else if (contentGuardResourceRequest.getPage_number().intValue() < 1) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_PAGE_NUMBER_INVALID,
						String.valueOf(contentGuardResourceRequest.getPage_number()));
			}
			// 'rows_per_page' in request must be > 0
			else if (contentGuardResourceRequest.getRows_per_page().intValue() < 1) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_ROWS_PER_PAGE_INVALID,
						String.valueOf(contentGuardResourceRequest.getRows_per_page()));
			}
		}
		// command:page_analysis_issues
		else if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_PAGE_ANALYSIS_ISSUES)) {
			// 'start_crawl_date' in request is not allowed
			if (StringUtils.isNotBlank(contentGuardResourceRequest.getStart_crawl_date())) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_START_CRAWL_DATE_NOT_ALLOWED, contentGuardResourceRequest.getStart_crawl_date());
			}
			// 'end_crawl_date' in request is not allowed
			else if (StringUtils.isNotBlank(contentGuardResourceRequest.getEnd_crawl_date())) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_END_CRAWL_DATE_NOT_ALLOWED, contentGuardResourceRequest.getEnd_crawl_date());
			}
			// 'crawl_timestamp' is not allowed
			else if (StringUtils.isNotBlank(contentGuardResourceRequest.getCrawl_timestamp())) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_CRAWL_TIMESTAMP_NOT_ALLOWED, contentGuardResourceRequest.getCrawl_timestamp());
			}
			// 'crawl_date' in request is invalid
			else if (StringUtils.isNotBlank(contentGuardResourceRequest.getCrawl_date()) && isDateStringValid(contentGuardResourceRequest.getCrawl_date()) == false) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_CRAWL_DATE_INVALID, contentGuardResourceRequest.getCrawl_date());
			}
			// 'crawl_hour' in request is invalid
			else if (contentGuardResourceRequest.getCrawl_hour() != null && isCrawlHourValid(contentGuardResourceRequest.getCrawl_hour()) == false) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_CRAWL_HOUR_INVALID, String.valueOf(contentGuardResourceRequest.getCrawl_hour()));
			}
			// when 'crawl_hour' is in request but 'crawl_date' is not in request
			else if (contentGuardResourceRequest.getCrawl_hour() != null && StringUtils.isBlank(contentGuardResourceRequest.getCrawl_date())) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_CRAWL_DATE_REQUIRED);
			}
		}
		// command:usage
		else if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_USAGE)) {
			// 'start_usage_date' is required
			if (StringUtils.isBlank(contentGuardResourceRequest.getStart_usage_date())) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_START_USAGE_DATE_REQUIRED);
			}
			// 'start_usage_date' in request is invalid
			else if (isDateStringValid(contentGuardResourceRequest.getStart_usage_date()) == false) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_START_USAGE_DATE_INVALID, contentGuardResourceRequest.getStart_usage_date());
			}
			// 'end_usage_date' is required
			else if (StringUtils.isBlank(contentGuardResourceRequest.getEnd_usage_date())) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_END_USAGE_DATE_REQUIRED);
			}
			// 'end_usage_date' in request is invalid
			else if (isDateStringValid(contentGuardResourceRequest.getEnd_usage_date()) == false) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_END_USAGE_DATE_INVALID, contentGuardResourceRequest.getEnd_usage_date());
			}
			// 'start_usage_date' in request is later 'end_usage_date'
			else if (isDateStringValid(contentGuardResourceRequest.getStart_usage_date(), contentGuardResourceRequest.getEnd_usage_date()) == false) {
				return getValidationError(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_START_USAGE_DATE_LATER_THAN_END_USAGE_DATE,
						contentGuardResourceRequest.getStart_usage_date());
			}
		}

		return validationError;
	}

	private boolean isDateStringValid(String crawlDate) {
		boolean output = false;
		try {
			DateUtils.parseDateStrictly(crawlDate, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
			output = true;
		} catch (Exception e) {
			output = false;
		}
		return output;
	}

	private boolean isDateStringValid(String startCrawlDateString, String endCrawlDateString) {
		boolean output = false;
		Date startCrawlDate = null;
		Date endCrawlDate = null;
		try {
			startCrawlDate = DateUtils.parseDateStrictly(startCrawlDateString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
			endCrawlDate = DateUtils.parseDateStrictly(endCrawlDateString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
			if (endCrawlDate.before(startCrawlDate)) {
				output = false;
			} else {
				output = true;
			}
		} catch (Exception e) {
			output = false;
		}
		return output;
	}

	private boolean isCrawlHourValid(Integer crawlHour) {
		boolean output = false;
		if (crawlHour.intValue() >= 0 && crawlHour.intValue() <= 23) {
			output = true;
		} else {
			output = false;
		}
		return output;
	}

	private boolean isCrawlTimestampValid(String crawlTimestamp) {
		boolean output = false;
		try {
			DateUtils.parseDateStrictly(crawlTimestamp, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS });
			output = true;
		} catch (Exception e) {
			output = false;
		}
		return output;
	}

	private ValidationError getValidationError(String errorCode) {
		return getValidationError(errorCode, null);
	}

	private ValidationError getValidationError(String errorCode, String errorData) {
		ValidationError validationError = new ValidationError();
		validationError.setErrorCode(errorCode);
		validationError.setErrorData(errorData);
		return validationError;
	}

	public Representation generateJsonError(String errorCode) {
		return generateJsonError(errorCode, null);
	}

	public Representation generateJsonError(String errorCode, String supplementalMessageText) {
		FormatUtils.getInstance().logMemoryUsage("generateJsonError() errorCode=" + errorCode + ",supplementalMessageText=" + supplementalMessageText);
		String errorMessage = ContentGuardUtils.getInstance().getErrorMessage(errorCode, supplementalMessageText);
		ContentGuardResourceResponse contentGuardResourceResponse = new ContentGuardResourceResponse();
		contentGuardResourceResponse.setSuccess(false);
		WebServiceError webServiceError = new WebServiceError();
		webServiceError.setError_code(errorCode);
		webServiceError.setError_message(errorMessage);
		contentGuardResourceResponse.setError(webServiceError);
		return new JacksonRepresentation<ContentGuardResourceResponse>(contentGuardResourceResponse);
	}

	public String getErrorMessage(String errorCode) {
		return ContentGuardUtils.getInstance().getErrorMessage(errorCode, null);
	}
}
