package com.actonia.web.service;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.restlet.ext.jackson.JacksonRepresentation;
import org.restlet.representation.Representation;
import org.restlet.resource.Post;

import com.actonia.IConstants;
import com.actonia.service.AccessTokenService;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.RUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.AreaDateValue;
import com.actonia.value.object.MarketMatchingRequest;
import com.actonia.value.object.MarketMatchingResponse;
import com.actonia.value.object.RRequest;
import com.actonia.value.object.RResponse;
import com.actonia.value.object.ValidationError;
import com.actonia.value.object.WebServiceError;
import com.google.gson.Gson;

public class MarketMatchingResource extends BaseServerResouce {

	private boolean isDebug = false;

	private AccessTokenService accessTokenService;

	public MarketMatchingResource() {
		super();
		accessTokenService = SpringBeanFactory.getBean("accessTokenService");
	}

	@Post("application/json")
	public Representation doPost(Representation representationInput) {
		long startTimestamp = System.currentTimeMillis();
		Representation representationOutput = null;
		String requestParameters = null;
		RRequest rRequest = null;
		RResponse rResponse = null;
		MarketMatchingRequest marketMatchingRequest = null;
		MarketMatchingResponse marketMatchingResponse = null;
		String accessToken = null;
		String accessTokenValidationErrorCode = null;

		try {

			if (representationInput == null) {
				return generateJsonError(IConstants.MSG_CD_MARKET_MATCHING_REQUEST_JSON_REQUIRED);
			}

			requestParameters = FormatUtils.getInstance().removeCarriageReturnNewLine(representationInput.getText());
			FormatUtils.getInstance().logMemoryUsage("doPost() requestParameters=" + requestParameters);
			if (StringUtils.isBlank(requestParameters)) {
				return generateJsonError(IConstants.MSG_CD_MARKET_MATCHING_REQUEST_JSON_REQUIRED);
			}

			marketMatchingRequest = new Gson().fromJson(requestParameters, MarketMatchingRequest.class);
			if (isDebug == true) {
				FormatUtils.getInstance().logMemoryUsage("doPost() marketMatchingRequest=" + marketMatchingRequest.toString());
			}

			accessToken = marketMatchingRequest.getAccess_token();
			if (StringUtils.isBlank(accessToken)) {
				return generateJsonError(IConstants.MSG_CD_MARKET_MATCHING_REQUEST_PARM_ACCESS_TOKEN_REQUIRED);
			} else if (StringUtils.equalsIgnoreCase(accessToken, AccessTokenService.INTERNAL_KEY) == false) {
				accessTokenValidationErrorCode = accessTokenService.checkToken(accessToken);
				if (StringUtils.isNotBlank(accessTokenValidationErrorCode)) {
					return generateJsonError(IConstants.MSG_CD_MARKET_MATCHING_REQUEST_PARM_ACCESS_TOKEN_INVALID, accessToken);
				}
			}

			representationOutput = validateRequest(marketMatchingRequest);
			// when validation failed, return the error JSON representation
			if (representationOutput != null) {
				return representationOutput;
			}

			rRequest = new RRequest();
			rRequest.setrPackage(IConstants.R_PACKAGE_MARKET_MATCHING);
			rRequest.setMarketMatchingRequest(marketMatchingRequest);
			rResponse = RUtils.getInstance().invokeR(rRequest);
			if (rResponse != null) {
				marketMatchingResponse = rResponse.getMarketMatchingResponse();
			}
			return new JacksonRepresentation<MarketMatchingResponse>(marketMatchingResponse);
		} catch (Exception e) {
			e.printStackTrace();
			StringWriter stringWriter = new StringWriter();
			e.printStackTrace(new PrintWriter(stringWriter));

			// send support alert
			String message = stringWriter.toString();
			return generateJsonError(IConstants.MSG_CD_MARKET_MATCHING_WEB_SERVICE_METHOD_EXCEPTION, message);
		} finally {
			if (marketMatchingResponse != null) {
				if (isDebug == true) {
					FormatUtils.getInstance().logMemoryUsage("doPost() ends. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp) + ",marketMatchingResponse="
							+ marketMatchingResponse.toString());
				} else {
					FormatUtils.getInstance().logMemoryUsage("doPost() ends. elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp)
							+ ",marketMatchingResponse.getSuccess()=" + marketMatchingResponse.getSuccess());
				}
			}
		}
	}

	private Representation validateRequest(MarketMatchingRequest marketMatchingRequest) throws Exception {
		Representation representation = null;
		MarketMatchingResponse marketMatchingResponse = null;
		WebServiceError webServiceError = null;

		ValidationError validationError = getValidationErrorCode(marketMatchingRequest);

		if (validationError != null) {
			marketMatchingResponse = new MarketMatchingResponse();
			webServiceError = new WebServiceError();
			webServiceError.setError_code(validationError.getErrorCode());
			webServiceError.setError_message(getErrorMessage(validationError.getErrorCode(), validationError.getErrorData()));
			marketMatchingResponse.setError(webServiceError);
			marketMatchingResponse.setSuccess(false);
			FormatUtils.getInstance()
					.logMemoryUsage("validateRequest() marketMatchingResponse=" + new Gson().toJson(marketMatchingResponse, MarketMatchingResponse.class));
			representation = new JacksonRepresentation<MarketMatchingResponse>(marketMatchingResponse);
		}

		return representation;
	}

	private ValidationError getValidationErrorCode(MarketMatchingRequest marketMatchingRequest) throws Exception {
		ValidationError validationError = null;

		// 'test_market' is required
		if (StringUtils.isBlank(marketMatchingRequest.getTest_market())) {
			return getValidationError(IConstants.MSG_CD_MARKET_MATCHING_REQUEST_PARM_TEST_MARKET_REQUIRED);
		}
		// 'pre_period_start_date' is required
		else if (StringUtils.isBlank(marketMatchingRequest.getPre_period_start_date())) {
			return getValidationError(IConstants.MSG_CD_MARKET_MATCHING_REQUEST_PARM_PRE_PERIOD_START_DATE_REQUIRED);
		}
		// 'pre_period_start_date' is invalid
		else if (isDateValid(marketMatchingRequest.getPre_period_start_date()) == false) {
			return getValidationError(IConstants.MSG_CD_MARKET_MATCHING_REQUEST_PARM_PRE_PERIOD_START_DATE_INVALID, marketMatchingRequest.getPre_period_start_date());
		}
		// 'pre_period_end_date' is required
		else if (StringUtils.isBlank(marketMatchingRequest.getPre_period_end_date())) {
			return getValidationError(IConstants.MSG_CD_MARKET_MATCHING_REQUEST_PARM_PRE_PERIOD_END_DATE_REQUIRED);
		}
		// 'pre_period_end_date' is invalid
		else if (isDateValid(marketMatchingRequest.getPre_period_end_date()) == false) {
			return getValidationError(IConstants.MSG_CD_MARKET_MATCHING_REQUEST_PARM_PRE_PERIOD_END_DATE_INVALID, marketMatchingRequest.getPre_period_end_date());
		}
		// 'pre_period_start_date' and 'pre_period_end_date' are invalid
		else if (areDatesValid(marketMatchingRequest.getPre_period_start_date(), marketMatchingRequest.getPre_period_end_date()) == false) {
			return getValidationError(IConstants.MSG_CD_MARKET_MATCHING_REQUEST_PARM_PRE_PERIOD_START_END_DATE_INVALID);
		}
		// 'post_period_start_date' is required
		else if (StringUtils.isBlank(marketMatchingRequest.getPost_period_start_date())) {
			return getValidationError(IConstants.MSG_CD_MARKET_MATCHING_REQUEST_PARM_POST_PERIOD_START_DATE_REQUIRED);
		}
		// 'post_period_start_date' is invalid
		else if (isDateValid(marketMatchingRequest.getPost_period_start_date()) == false) {
			return getValidationError(IConstants.MSG_CD_MARKET_MATCHING_REQUEST_PARM_POST_PERIOD_START_DATE_INVALID, marketMatchingRequest.getPost_period_start_date());
		}
		// 'post_period_end_date' is required
		else if (StringUtils.isBlank(marketMatchingRequest.getPost_period_end_date())) {
			return getValidationError(IConstants.MSG_CD_MARKET_MATCHING_REQUEST_PARM_POST_PERIOD_END_DATE_REQUIRED);
		}
		// 'post_period_end_date' is invalid
		else if (isDateValid(marketMatchingRequest.getPost_period_end_date()) == false) {
			return getValidationError(IConstants.MSG_CD_MARKET_MATCHING_REQUEST_PARM_POST_PERIOD_END_DATE_INVALID, marketMatchingRequest.getPost_period_end_date());
		}
		// 'post_period_start_date' and 'post_period_end_date' are invalid
		else if (areDatesValid(marketMatchingRequest.getPost_period_start_date(), marketMatchingRequest.getPost_period_end_date()) == false) {
			return getValidationError(IConstants.MSG_CD_MARKET_MATCHING_REQUEST_PARM_POST_PERIOD_START_END_DATE_INVALID);
		}
		// 'post_period_start_date' is invalid
		else if (checkIfPostPeriodStartDateValid(marketMatchingRequest.getPre_period_end_date(), marketMatchingRequest.getPost_period_start_date()) == false) {
			return getValidationError(IConstants.MSG_CD_MARKET_MATCHING_REQUEST_PARM_POST_PERIOD_START_DATE_MUST_BE_ONE_DAY_AFTER);
		}
		// 'number_of_best_matches' is required
		else if (marketMatchingRequest.getNumber_of_best_matches() == null || marketMatchingRequest.getNumber_of_best_matches().intValue() == 0) {
			return getValidationError(IConstants.MSG_CD_MARKET_MATCHING_REQUEST_PARM_NUMBER_OF_BEST_MATCHES_REQUIRED);
		}
		// 'area_date_value_array' is required
		else if (marketMatchingRequest.getArea_date_value_array() == null || marketMatchingRequest.getArea_date_value_array().length == 0) {
			return getValidationError(IConstants.MSG_CD_MARKET_MATCHING_REQUEST_PARM_AREA_DATE_VALUE_ARRAY_REQUIRED);
		}

		validationError = validateAreaDateValueArray(marketMatchingRequest);
		if (validationError != null) {
			return validationError;
		}

		if (checkIfTestMarketValid(marketMatchingRequest) == false) {
			return getValidationError(IConstants.MSG_CD_MARKET_MATCHING_REQUEST_PARM_TEST_MARKET_INVALID, marketMatchingRequest.getTest_market());
		}

		return validationError;
	}

	private ValidationError validateAreaDateValueArray(MarketMatchingRequest marketMatchingRequest) {

		ValidationError validationError = null;

		// map key = area
		// map value = total occurrences
		Map<String, Integer> areaOccurrencesMap = null;

		int occurrences = 0;
		int totalOccurrences = 0;
		int averageOccurrences = 0;
		String[] dateArray = null;
		Double[] doubleArray = null;
		Integer value = null;
		Long totalPreAndPostPeriodDays = null;
		int largestOccurrences = 0;
		int totalAreas = 0;

		String[] areaArray = RUtils.getInstance().getAreaArray(marketMatchingRequest);
		if (areaArray == null || areaArray.length == 0) {
			return getValidationError(IConstants.MSG_CD_MARKET_MATCHING_REQUEST_PARM_AREA_ARRAY_REQUIRED);
		}
		areaOccurrencesMap = new HashMap<String, Integer>();
		for (String area : areaArray) {
			if (areaOccurrencesMap.containsKey(area)) {
				occurrences = areaOccurrencesMap.get(area);
			} else {
				occurrences = 0;
			}
			occurrences++;
			areaOccurrencesMap.put(area, occurrences);
		}
		totalOccurrences = 0;
		for (String key : areaOccurrencesMap.keySet()) {
			totalOccurrences = totalOccurrences + areaOccurrencesMap.get(key);
		}
		averageOccurrences = totalOccurrences / areaOccurrencesMap.size();
		for (String key : areaOccurrencesMap.keySet()) {
			value = areaOccurrencesMap.get(key);
			if (value > largestOccurrences) {
				largestOccurrences = value;
			}
		}
		totalAreas = areaOccurrencesMap.keySet().size();
		if (marketMatchingRequest.getNumber_of_best_matches() > (totalAreas - 1)) {
			return getValidationError(IConstants.MSG_CD_MARKET_MATCHING_REQUEST_PARM_NUMBER_OF_BEST_MATCHES_INVALID,
					String.valueOf(marketMatchingRequest.getNumber_of_best_matches()));
		}

		totalPreAndPostPeriodDays = ChronoUnit.DAYS.between(LocalDate.parse(marketMatchingRequest.getPre_period_start_date()),
				LocalDate.parse(marketMatchingRequest.getPost_period_end_date())) + IConstants.LONG_ONE;
		if (isDebug == true) {
			FormatUtils.getInstance().logMemoryUsage("validateAreaDateValueArray() totalPreAndPostPeriodDays=" + totalPreAndPostPeriodDays + ",area totalOccurrences="
					+ totalOccurrences + ",averageOccurrences=" + averageOccurrences + ",largestOccurrences=" + largestOccurrences);
		}
		if (totalPreAndPostPeriodDays.intValue() != largestOccurrences) {
			return getValidationError(IConstants.MSG_CD_MARKET_MATCHING_REQUEST_PARM_AREA_DATE_VALUE_ARRAY_NOT_MATCH_START_END_DATE);
		}
		for (String key : areaOccurrencesMap.keySet()) {
			value = areaOccurrencesMap.get(key);
			if (value != averageOccurrences) {
				return getValidationError(IConstants.MSG_CD_MARKET_MATCHING_REQUEST_PARM_AREA_ARRAY_INVALID);
			}
		}

		dateArray = RUtils.getInstance().getDateArray(marketMatchingRequest);
		if (dateArray == null || dateArray.length == 0) {
			return getValidationError(IConstants.MSG_CD_MARKET_MATCHING_REQUEST_PARM_DATE_ARRAY_REQUIRED);
		}
		if (dateArray.length != totalOccurrences) {
			if (isDebug == true) {
				FormatUtils.getInstance().logMemoryUsage("validateAreaDateValueArray() MSG_CD_MARKET_MATCHING_REQUEST_PARM_DATE_ARRAY_INVALID 1");
			}
			return getValidationError(IConstants.MSG_CD_MARKET_MATCHING_REQUEST_PARM_DATE_ARRAY_INVALID);
		}
		validationError = validateDateArray(marketMatchingRequest, averageOccurrences, marketMatchingRequest.getPre_period_start_date(),
				marketMatchingRequest.getPost_period_end_date());
		if (validationError != null) {
			return validationError;
		}

		doubleArray = RUtils.getInstance().getValueArray(marketMatchingRequest);
		if (doubleArray == null || doubleArray.length == 0) {
			return getValidationError(IConstants.MSG_CD_MARKET_MATCHING_REQUEST_PARM_VALUE_ARRAY_REQUIRED);
		}
		if (doubleArray.length != totalOccurrences) {
			if (isDebug == true) {
				FormatUtils.getInstance().logMemoryUsage("validateAreaDateValueArray() MSG_CD_MARKET_MATCHING_REQUEST_PARM_VALUE_ARRAY_INVALID 1");
			}
			return getValidationError(IConstants.MSG_CD_MARKET_MATCHING_REQUEST_PARM_VALUE_ARRAY_INVALID);
		}
		for (Double testDouble : doubleArray) {
			if (testDouble == null) {
				if (isDebug == true) {
					FormatUtils.getInstance().logMemoryUsage("validateAreaDateValueArray() MSG_CD_MARKET_MATCHING_REQUEST_PARM_VALUE_ARRAY_INVALID 2");
				}
				return getValidationError(IConstants.MSG_CD_MARKET_MATCHING_REQUEST_PARM_VALUE_ARRAY_INVALID);
			}
		}

		return validationError;
	}

	private ValidationError validateDateArray(MarketMatchingRequest marketMatchingRequest, int averageOccurrences, String prePeriodStartDateString,
			String postPeriodEndDateString) {
		ValidationError validationError = null;

		Map<String, List<String>> areaDateListMap = new HashMap<String, List<String>>();
		String[] dateArray = null;
		List<String> dateList = null;
		int totalDates = 0;

		AreaDateValue[] areaDateValueArray = marketMatchingRequest.getArea_date_value_array();
		for (AreaDateValue areaDateValue : areaDateValueArray) {
			if (areaDateListMap.containsKey(areaDateValue.getArea())) {
				dateList = areaDateListMap.get(areaDateValue.getArea());
			} else {
				dateList = new ArrayList<String>();
			}
			dateList.add(areaDateValue.getDate());
			areaDateListMap.put(areaDateValue.getArea(), dateList);
		}

		for (String area : areaDateListMap.keySet()) {
			dateList = areaDateListMap.get(area);
			totalDates = dateList.size();
			dateArray = dateList.toArray(new String[0]);
			if (dateList.size() != averageOccurrences) {
				if (isDebug == true) {
					FormatUtils.getInstance().logMemoryUsage("validateDateArray() area=" + area + ",MSG_CD_MARKET_MATCHING_REQUEST_PARM_DATE_ARRAY_INVALID 2");
				}
				return getValidationError(IConstants.MSG_CD_MARKET_MATCHING_REQUEST_PARM_DATE_ARRAY_INVALID);
			} else if (isDateArrayInvalid(dateArray) == true) {
				if (isDebug == true) {
					FormatUtils.getInstance().logMemoryUsage("validateDateArray() area=" + area + ",MSG_CD_MARKET_MATCHING_REQUEST_PARM_DATE_ARRAY_INVALID 3");
				}
				return getValidationError(IConstants.MSG_CD_MARKET_MATCHING_REQUEST_PARM_DATE_ARRAY_INVALID);
			} else if (StringUtils.equalsIgnoreCase(prePeriodStartDateString, dateArray[0]) == false) {
				if (isDebug == true) {
					FormatUtils.getInstance().logMemoryUsage("validateDateArray() area=" + area + ",MSG_CD_MARKET_MATCHING_REQUEST_PARM_DATE_ARRAY_INVALID 4");
				}
				return getValidationError(IConstants.MSG_CD_MARKET_MATCHING_REQUEST_PARM_DATE_ARRAY_INVALID);
			} else if (StringUtils.equalsIgnoreCase(postPeriodEndDateString, dateArray[totalDates - 1]) == false) {
				if (isDebug == true) {
					FormatUtils.getInstance().logMemoryUsage("validateDateArray() area=" + area + ",MSG_CD_MARKET_MATCHING_REQUEST_PARM_DATE_ARRAY_INVALID 5");
				}
				return getValidationError(IConstants.MSG_CD_MARKET_MATCHING_REQUEST_PARM_DATE_ARRAY_INVALID);
			}
		}

		return validationError;
	}

	private boolean checkIfTestMarketValid(MarketMatchingRequest marketMatchingRequest) {
		String testMarket = marketMatchingRequest.getTest_market();
		for (AreaDateValue areaDateValue : marketMatchingRequest.getArea_date_value_array()) {
			if (StringUtils.equals(areaDateValue.getArea(), testMarket) == true) {
				return true;
			}
		}
		return false;
	}

	private boolean isDateArrayInvalid(String[] dateArray) {
		boolean output = false;
		String dateBeforeString = null;
		String dateAfterString = null;

		nextDateString: for (String dateString : dateArray) {
			if (isDateValid(dateString) == false) {
				output = true;
				break nextDateString;
			}
		}

		if (output == false) {
			nextDateArray: for (int i = 0; i < dateArray.length; i++) {
				if (dateArray.length > (i + 1)) {
					dateBeforeString = dateArray[i];
					dateAfterString = dateArray[i + 1];
					if (areDatesOneDayApart(dateBeforeString, dateAfterString) == false) {
						output = true;
						break nextDateArray;
					}
				}
			}
		}

		return output;
	}

	private boolean checkIfPostPeriodStartDateValid(String prePeriodEndDateString, String postPeriodStartDateString) throws Exception {
		boolean output = false;
		Date prePeriodEndDate = DateUtils.parseDate(prePeriodEndDateString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
		Date postPeriodStartDate = DateUtils.addDays(prePeriodEndDate, +1);
		String testDateString = DateFormatUtils.format(postPeriodStartDate, IConstants.DATE_FORMAT_YYYY_MM_DD);
		if (StringUtils.equalsIgnoreCase(postPeriodStartDateString, testDateString)) {
			output = true;
		}
		return output;
	}

	private boolean isDateValid(String inputDateString) {
		boolean output = false;
		try {
			DateUtils.parseDateStrictly(inputDateString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
			output = true;
		} catch (Exception e) {
			output = false;
		}
		return output;
	}

	private boolean areDatesOneDayApart(String startDateString, String endDateString) {
		boolean output = false;
		Date startDate = null;
		Date testDate = null;
		Date endDate = null;
		try {
			startDate = DateUtils.parseDateStrictly(startDateString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
			testDate = DateUtils.addDays(startDate, +1);
			endDate = DateUtils.parseDateStrictly(endDateString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
			if (endDate.before(testDate) || endDate.after(testDate)) {
				output = false;
			} else {
				output = true;
			}
		} catch (Exception e) {
			output = false;
		}
		return output;
	}

	private boolean areDatesValid(String startDateString, String endDateString) {
		boolean output = false;
		Date startDate = null;
		Date endDate = null;
		try {
			startDate = DateUtils.parseDateStrictly(startDateString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
			endDate = DateUtils.parseDateStrictly(endDateString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
			if (endDate.before(startDate)) {
				output = false;
			} else {
				output = true;
			}
		} catch (Exception e) {
			output = false;
		}
		return output;
	}

	private ValidationError getValidationError(String errorCode) {
		return getValidationError(errorCode, null);
	}

	private ValidationError getValidationError(String errorCode, String errorData) {
		ValidationError validationError = new ValidationError();
		validationError.setErrorCode(errorCode);
		validationError.setErrorData(errorData);
		return validationError;
	}

	private Representation generateJsonError(String errorCode) {
		return generateJsonError(errorCode, null);
	}

	private Representation generateJsonError(String errorCode, String supplementalMessageText) {
		String errorMessage = getErrorMessage(errorCode, supplementalMessageText);
		MarketMatchingResponse marketMatchingResponse = new MarketMatchingResponse();
		marketMatchingResponse.setSuccess(false);
		WebServiceError webServiceError = new WebServiceError();
		webServiceError.setError_code(errorCode);
		webServiceError.setError_message(errorMessage);
		marketMatchingResponse.setError(webServiceError);
		return new JacksonRepresentation<MarketMatchingResponse>(marketMatchingResponse);
	}

	private String getErrorMessage(String errorCode, String supplementalMessageText) {
		String errorMessage = null;
		String errorTemplate = MarketMatchingWebServiceMessage.getStringProperty(errorCode);
		if (StringUtils.isNotBlank(supplementalMessageText)) {
			errorMessage = MessageFormat.format(errorTemplate, supplementalMessageText);
		} else {
			errorMessage = errorTemplate;
		}
		return errorMessage;
	}
}
