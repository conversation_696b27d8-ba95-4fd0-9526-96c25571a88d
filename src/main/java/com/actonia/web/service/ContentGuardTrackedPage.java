package com.actonia.web.service;

public class ContentGuardTrackedPage {
	private String url;
	private String last_update_timestamp;
	private String response_code;

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getLast_update_timestamp() {
		return last_update_timestamp;
	}

	public void setLast_update_timestamp(String last_update_timestamp) {
		this.last_update_timestamp = last_update_timestamp;
	}

	public String getResponse_code() {
		return response_code;
	}

	public void setResponse_code(String response_code) {
		this.response_code = response_code;
	}

	@Override
	public String toString() {
		return "ContentGuardTrackedPage [url=" + url + ", last_update_timestamp=" + last_update_timestamp + ", response_code=" + response_code + "]";
	}

}
