package com.actonia.web.service;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.net.URL;

import org.apache.commons.lang.StringUtils;
import org.restlet.data.Form;
import org.restlet.ext.jackson.JacksonRepresentation;
import org.restlet.representation.Representation;
import org.restlet.resource.Delete;
import org.restlet.resource.Get;
import org.restlet.resource.Post;
import org.restlet.resource.ResourceException;

import com.actonia.IConstants;
import com.actonia.dao.ContentGuardAlertDAO;
import com.actonia.dao.OwnDomainEntityDAO;
import com.actonia.dao.UserDAO;
import com.actonia.entity.ContentGuardAlertEntity;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.entity.UserEntity;
import com.actonia.service.AccessTokenService;
import com.actonia.service.ZapierService;
import com.actonia.utils.EmailSenderComponent;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.ZapierContentGuardAlert;
import com.actonia.value.object.ZapierCrawlCompletedAlert;
import com.actonia.value.object.ZapierCrawlInitiatedAlert;
import com.actonia.value.object.ZapierPageTagContentAlert;
import com.actonia.value.object.ZapierResourceRequest;
import com.actonia.value.object.ZapierResourceResponse;
import com.actonia.value.object.ZapierTargetUrlChangeAlert;
import com.google.gson.Gson;

public class ZapierResource extends BaseServerResouce {

	//private boolean isDebug = false;

	private String command;

	private ZapierService zapierService;
	private ContentGuardAlertDAO contentGuardAlertDAO;
	private EmailSenderComponent emailSenderComponent;
	private AccessTokenService accessTokenService;
	private OwnDomainEntityDAO ownDomainEntityDAO;
	private UserDAO userDAO;

	private static final String[] VALID_COMMAND_ARRAY = new String[] { IConstants.COMMAND_CONTENT_GUARD_ALERT, IConstants.COMMAND_CRAWL_INITIATED_ALERT,
			IConstants.COMMAND_CRAWL_COMPLETED_ALERT, IConstants.COMMAND_PAGE_TAG_CONTENT_ALERT, IConstants.COMMAND_TARGET_URL_CHANGE_ALERT, };

	public ZapierResource() {
		super();
		this.zapierService = SpringBeanFactory.getBean("zapierService");
		this.emailSenderComponent = SpringBeanFactory.getBean("emailSenderComponent");
		this.contentGuardAlertDAO = SpringBeanFactory.getBean("contentGuardAlertDAO");
		this.accessTokenService = SpringBeanFactory.getBean("accessTokenService");
		this.ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		this.userDAO = SpringBeanFactory.getBean("userDAO");
	}

	@Override
	protected void doInit() throws ResourceException {
		this.command = (String) getRequest().getAttributes().get("command");
	}

	@Post("application/json")
	public Representation doPost(Representation representationInput) {
		boolean isHttpGet = false;
		String requestParameters = null;
		ZapierResourceRequest zapierResourceRequest = null;
		String accessToken = null;
		String accessTokenValidationErrorCode = null;
		Integer domainId = null;
		String userEmail = null;
		String callbackUrl = null;
		String groupName = null;
		String pageTagName = null;
		OwnDomainEntity ownDomainEntity = null;
		ContentGuardAlertEntity contentGuardAlertEntity = null;
		UserEntity userEntity = null;
		ZapierResourceResponse zapierResourceResponse = null;
		Long groupId = null;
		int userId = 0;
		boolean isCommandValid = false;
		Integer pageTagId = null;

		try {
			FormatUtils.getInstance().logMemoryUsage("doPost() command=" + command);
			isCommandValid = validateCommand();
			if (isCommandValid == false) {
				return generateJsonError(IConstants.MSG_CD_ZAPIER_REQUEST_COMMAND_INVALID, command, isHttpGet);
			}

			if (representationInput == null) {
				return generateJsonError(IConstants.MSG_CD_ZAPIER_REQUEST_DATA_REQUIRED, isHttpGet);
			}

			requestParameters = representationInput.getText();
			FormatUtils.getInstance().logMemoryUsage("doPost() requestParameters=" + requestParameters);
			if (StringUtils.isBlank(requestParameters)) {
				return generateJsonError(IConstants.MSG_CD_ZAPIER_REQUEST_DATA_REQUIRED, isHttpGet);
			}

			zapierResourceRequest = new Gson().fromJson(requestParameters, ZapierResourceRequest.class);
			FormatUtils.getInstance().logMemoryUsage("doPost() zapierResourceRequest=" + zapierResourceRequest.toString());

			// request parameter: access_token
			accessToken = zapierResourceRequest.getAccess_token();
			if (StringUtils.isBlank(accessToken)) {
				return generateJsonError(IConstants.MSG_CD_ZAPIER_REQUEST_PARM_ACCESS_TOKEN_REQUIRED, isHttpGet);
			} else if (StringUtils.equals(accessToken, IConstants.INTERNAL_KEY) == false
					&& StringUtils.equalsIgnoreCase(accessToken, AccessTokenService.INTERNAL_KEY) == false) {
				accessTokenValidationErrorCode = accessTokenService.checkToken(accessToken);
				if (StringUtils.isNotBlank(accessTokenValidationErrorCode)) {
					return generateJsonError(IConstants.MSG_CD_ZAPIER_REQUEST_PARM_ACCESS_TOKEN_INVALID, accessToken, isHttpGet);
				}
			}

			// request parameter: domain_id
			domainId = zapierResourceRequest.getDomain_id();
			if (domainId == null) {
				return generateJsonError(IConstants.MSG_CD_ZAPIER_REQUEST_PARM_DOMAIN_ID_REQUIRED, isHttpGet);
			} else {
				ownDomainEntity = ownDomainEntityDAO.getById(domainId);
				if (ownDomainEntity == null) {
					return generateJsonError(IConstants.MSG_CD_ZAPIER_REQUEST_PARM_DOMAIN_ID_INVALID, String.valueOf(domainId), isHttpGet);
				}
			}

			// request parameter: user_email
			userEmail = zapierResourceRequest.getUser_email();
			if (StringUtils.isBlank(userEmail)) {
				return generateJsonError(IConstants.MSG_CD_ZAPIER_REQUEST_PARM_USER_EMAIL_REQUIRED, isHttpGet);
			} else {
				userEntity = userDAO.getUserByUserEmailAndDomainId(domainId, userEmail);
				if (userEntity == null) {
					return generateJsonError(IConstants.MSG_CD_ZAPIER_REQUEST_PARM_USER_EMAIL_INVALID, userEmail, isHttpGet);
				}
			}
			FormatUtils.getInstance().logMemoryUsage(
					"ZapierContentGuardAlertResource.doPost() userEntity.getId()=" + userEntity.getId() + ",userEntity.getName()=" + userEntity.getName());
			userId = userEntity.getId();

			// request parameter: callback_url
			callbackUrl = zapierResourceRequest.getCallback_url();
			if (StringUtils.isBlank(callbackUrl)) {
				return generateJsonError(IConstants.MSG_CD_ZAPIER_REQUEST_PARM_CALLBACK_URL_REQUIRED, isHttpGet);
			} else {
				try {
					new URL(callbackUrl);
				} catch (Exception e) {
					return generateJsonError(IConstants.MSG_CD_ZAPIER_REQUEST_PARM_CALLBACK_URL_INVALID, callbackUrl, isHttpGet);
				}
			}

			// when command is 'content_guard_alert', validate request parameter 'group_name'
			if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_CONTENT_GUARD_ALERT)) {
				groupName = zapierResourceRequest.getGroup_name();
				FormatUtils.getInstance().logMemoryUsage("ZapierContentGuardAlertResource.doPost() groupName=" + groupName);
				if (StringUtils.isBlank(groupName)) {
					return generateJsonError(IConstants.MSG_CD_ZAPIER_REQUEST_PARM_GROUP_NAME_REQUIRED, isHttpGet);
				}
				contentGuardAlertEntity = contentGuardAlertDAO.getByDomainIdGroupName(domainId, groupName);
				if (contentGuardAlertEntity == null) {
					return generateJsonError(IConstants.MSG_CD_ZAPIER_REQUEST_PARM_DOMAIN_ID_GROUP_NAME_NOT_CONFIGURED_TO_HAVE_ALERT,
							String.valueOf(domainId) + IConstants.ONE_SPACE + groupName, isHttpGet);
				}
				FormatUtils.getInstance().logMemoryUsage("ZapierContentGuardAlertResource.doPost() contentGuardAlertEntity=" + contentGuardAlertEntity.toString());
				groupId = contentGuardAlertEntity.getGroupId();
			}
			// when command is 'page_tag_content_alert', validate request parameter 'page_tag_name'
			else if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_PAGE_TAG_CONTENT_ALERT)) {
				pageTagName = zapierResourceRequest.getPage_tag_name();
				FormatUtils.getInstance().logMemoryUsage("ZapierContentGuardAlertResource.doPost() pageTagName=" + pageTagName);
				if (StringUtils.isBlank(pageTagName)) {
					return generateJsonError(IConstants.MSG_CD_ZAPIER_REQUEST_PARM_PAGE_TAG_NAME_REQUIRED, isHttpGet);
				}
				pageTagId = zapierService.getPageTagAlertPageTagId(domainId, pageTagName);
				FormatUtils.getInstance().logMemoryUsage("ZapierContentGuardAlertResource.doPost() pageTagId=" + pageTagId);
				if (pageTagId == null) {
					return generateJsonError(IConstants.MSG_CD_ZAPIER_REQUEST_PARM_DOMAIN_ID_PAGE_TAG_NAME_NOT_CONFIGURED_TO_HAVE_ALERT,
							String.valueOf(domainId) + IConstants.ONE_SPACE + pageTagName, isHttpGet);
				}
			}
			// when command is 'target_url_change_alert', validate optional request parameter 'page_tag_name'
			else if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_TARGET_URL_CHANGE_ALERT)) {
				if (StringUtils.isNotBlank(zapierResourceRequest.getPage_tag_name())) {
					pageTagName = zapierResourceRequest.getPage_tag_name();
					FormatUtils.getInstance().logMemoryUsage("ZapierContentGuardAlertResource.doPost() pageTagName=" + pageTagName);
					pageTagId = zapierService.getTargetUrlChangePageTagId(domainId, pageTagName);
					FormatUtils.getInstance().logMemoryUsage("ZapierContentGuardAlertResource.doPost() pageTagId=" + pageTagId);
					if (pageTagId == null) {
						return generateJsonError(IConstants.MSG_CD_ZAPIER_REQUEST_PARM_DOMAIN_ID_PAGE_TAG_NAME_NOT_CONFIGURED_TO_HAVE_ALERT,
								String.valueOf(domainId) + IConstants.ONE_SPACE + pageTagName, isHttpGet);
					}
				}
			}

			if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_CONTENT_GUARD_ALERT)) {
				zapierResourceResponse = zapierService.createContentGuardAlertSubscription(domainId, userId, groupId, callbackUrl);
			} else if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_CRAWL_INITIATED_ALERT)) {
				zapierResourceResponse = zapierService.createSiteAuditAlertSubscription(domainId, userId, callbackUrl,
						IConstants.ZAPIER_TRIGGER_TYPE_CRAWL_INITIATED_ALERT);
			} else if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_CRAWL_COMPLETED_ALERT)) {
				zapierResourceResponse = zapierService.createSiteAuditAlertSubscription(domainId, userId, callbackUrl,
						IConstants.ZAPIER_TRIGGER_TYPE_CRAWL_COMPLETED_ALERT);
			} else if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_PAGE_TAG_CONTENT_ALERT)) {
				zapierResourceResponse = zapierService.createPageTagContentAlertSubscription(domainId, userId, pageTagId, callbackUrl);
			} else if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_TARGET_URL_CHANGE_ALERT)) {
				zapierResourceResponse = zapierService.createTargetUrlChangeAlertSubscription(domainId, userId, pageTagId, callbackUrl);
			}

			return new JacksonRepresentation<ZapierResourceResponse>(zapierResourceResponse);
		} catch (Exception e) {
			e.printStackTrace();
			StringWriter stringWriter = new StringWriter();
			e.printStackTrace(new PrintWriter(stringWriter));

			// send support alert
			String message = stringWriter.toString();

			return generateJsonError(IConstants.MSG_CD_ZAPIER_WEB_SERVICE_METHOD_EXCEPTION, message, isHttpGet);

		}
	}

	@Delete("application/json")
	public Representation doDelete() {
		boolean isHttpGet = false;
		String accessToken = null;
		String accessTokenValidationErrorCode = null;
		String idString = null;
		Form form = null;
		Long id = null;
		ZapierResourceResponse zapierResourceResponse = null;
		boolean isCommandValid = false;

		try {
			FormatUtils.getInstance().logMemoryUsage("doDelete() command=" + command);
			isCommandValid = validateCommand();
			if (isCommandValid == false) {
				return generateJsonError(IConstants.MSG_CD_ZAPIER_REQUEST_COMMAND_INVALID, command, isHttpGet);
			}

			form = getRequest().getResourceRef().getQueryAsForm();

			// URL parameter: access_token
			accessToken = form.getFirstValue("access_token", true);
			if (StringUtils.isBlank(accessToken)) {
				return generateJsonError(IConstants.MSG_CD_ZAPIER_REQUEST_PARM_ACCESS_TOKEN_REQUIRED, isHttpGet);
			} else if (StringUtils.equals(accessToken, IConstants.INTERNAL_KEY) == false
					&& StringUtils.equalsIgnoreCase(accessToken, AccessTokenService.INTERNAL_KEY) == false) {
				accessTokenValidationErrorCode = accessTokenService.checkToken(accessToken);
				if (StringUtils.isNotBlank(accessTokenValidationErrorCode)) {
					return generateJsonError(IConstants.MSG_CD_ZAPIER_REQUEST_PARM_ACCESS_TOKEN_INVALID, accessToken, isHttpGet);
				}
			}

			// URL parameter: id
			idString = form.getFirstValue("id", true);
			if (StringUtils.isBlank(idString)) {
				return generateJsonError(IConstants.MSG_CD_ZAPIER_REQUEST_PARM_ID_REQUIRED, isHttpGet);
			} else if (zapierService.isValidId(idString) == false) {
				return generateJsonError(IConstants.MSG_CD_ZAPIER_REQUEST_PARM_ID_INVALID, idString, isHttpGet);
			}

			id = new Long(idString);
			zapierService.deleteSubscription(id);
			zapierResourceResponse = new ZapierResourceResponse();
			zapierResourceResponse.setId(id);
			return new JacksonRepresentation<ZapierResourceResponse>(zapierResourceResponse);
		} catch (Exception e) {
			e.printStackTrace();
			StringWriter stringWriter = new StringWriter();
			e.printStackTrace(new PrintWriter(stringWriter));

			// send support alert
			String message = stringWriter.toString();

			return generateJsonError(IConstants.MSG_CD_ZAPIER_WEB_SERVICE_METHOD_EXCEPTION, message, isHttpGet);

		}
	}

	@Get("application/json")
	public Representation doGet() {
		boolean isHttpGet = true;
		String accessToken = null;
		String accessTokenValidationErrorCode = null;
		String idString = null;
		Form form = null;
		ZapierContentGuardAlert[] zapierContentGuardAlertArray = null;
		ZapierCrawlInitiatedAlert[] zapierCrawlInitiatedAlertArray = null;
		ZapierCrawlCompletedAlert[] zapierCrawlCompletedAlertArray = null;
		ZapierPageTagContentAlert[] zapierPageTagContentAlertArray = null;
		ZapierTargetUrlChangeAlert[] zapierTargetUrlChangeAlertArray = null;
		boolean isCommandValid = false;
		Representation outputRepresentation = null;

		try {
			FormatUtils.getInstance().logMemoryUsage("doGet() command=" + command);
			isCommandValid = validateCommand();
			if (isCommandValid == false) {
				return generateJsonError(IConstants.MSG_CD_ZAPIER_REQUEST_COMMAND_INVALID, command, isHttpGet);
			}

			form = getRequest().getResourceRef().getQueryAsForm();

			// URL parameter: access_token
			accessToken = form.getFirstValue("access_token", true);
			if (StringUtils.isBlank(accessToken)) {
				return generateJsonError(IConstants.MSG_CD_ZAPIER_REQUEST_PARM_ACCESS_TOKEN_REQUIRED, isHttpGet);
			} else if (StringUtils.equals(accessToken, IConstants.INTERNAL_KEY) == false
					&& StringUtils.equalsIgnoreCase(accessToken, AccessTokenService.INTERNAL_KEY) == false) {
				accessTokenValidationErrorCode = accessTokenService.checkToken(accessToken);
				if (StringUtils.isNotBlank(accessTokenValidationErrorCode)) {
					return generateJsonError(IConstants.MSG_CD_ZAPIER_REQUEST_PARM_ACCESS_TOKEN_INVALID, accessToken, isHttpGet);
				}
			}

			if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_CONTENT_GUARD_ALERT)) {
				zapierContentGuardAlertArray = zapierService.getSampleZapierContentGuardAlerts();
				outputRepresentation = new JacksonRepresentation<ZapierContentGuardAlert[]>(zapierContentGuardAlertArray);
			} else if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_CRAWL_INITIATED_ALERT)) {
				zapierCrawlInitiatedAlertArray = zapierService.getSampleZapierCrawlInitiatedAlerts();
				outputRepresentation = new JacksonRepresentation<ZapierCrawlInitiatedAlert[]>(zapierCrawlInitiatedAlertArray);
			} else if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_CRAWL_COMPLETED_ALERT)) {
				zapierCrawlCompletedAlertArray = zapierService.getSampleZapierCrawlCompletedAlerts();
				outputRepresentation = new JacksonRepresentation<ZapierCrawlCompletedAlert[]>(zapierCrawlCompletedAlertArray);
			} else if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_PAGE_TAG_CONTENT_ALERT)) {
				zapierPageTagContentAlertArray = zapierService.getSampleZapierPageTagContentAlerts();
				outputRepresentation = new JacksonRepresentation<ZapierPageTagContentAlert[]>(zapierPageTagContentAlertArray);
			} else if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_TARGET_URL_CHANGE_ALERT)) {
				zapierTargetUrlChangeAlertArray = zapierService.getSampleZapierTargetUrlChangeAlerts();
				outputRepresentation = new JacksonRepresentation<ZapierTargetUrlChangeAlert[]>(zapierTargetUrlChangeAlertArray);
			}

			return outputRepresentation;

		} catch (Exception e) {
			e.printStackTrace();
			StringWriter stringWriter = new StringWriter();
			e.printStackTrace(new PrintWriter(stringWriter));

			// send support alert
			String message = stringWriter.toString();

			return generateJsonError(IConstants.MSG_CD_ZAPIER_WEB_SERVICE_METHOD_EXCEPTION, message, isHttpGet);

		}
	}

	private boolean validateCommand() {
		boolean output = false;
		if (StringUtils.isNotBlank(command)) {
			nextValidCommand: for (String validCommand : VALID_COMMAND_ARRAY) {
				if (StringUtils.equalsIgnoreCase(command, validCommand)) {
					output = true;
					break nextValidCommand;
				}
			}
		}
		return output;
	}

	private Representation generateJsonError(String errorCode, boolean isHttpGet) {
		return generateJsonError(errorCode, null, isHttpGet);
	}

	private Representation generateJsonError(String errorCode, String supplementalMessageText, boolean isHttpGet) {
		FormatUtils.getInstance()
				.logMemoryUsage("generateJsonError() errorCode=" + errorCode + ",supplementalMessageText=" + supplementalMessageText + ",isHttpGet=" + isHttpGet);
		ZapierContentGuardAlert[] zapierContentGuardAlertArray = null;
		String errorMessage = zapierService.getErrorMessage(errorCode, supplementalMessageText);
		ZapierContentGuardAlert zapierContentGuardAlert = null;
		ZapierResourceResponse zapierResourceResponse = null;
		if (isHttpGet == true) {
			zapierContentGuardAlert = new ZapierContentGuardAlert();
			zapierContentGuardAlert.setError_code(errorCode);
			zapierContentGuardAlert.setError_message(errorMessage);
			zapierContentGuardAlertArray = new ZapierContentGuardAlert[1];
			zapierContentGuardAlertArray[0] = zapierContentGuardAlert;
			return new JacksonRepresentation<ZapierContentGuardAlert[]>(zapierContentGuardAlertArray);
		} else {
			zapierResourceResponse = new ZapierResourceResponse();
			zapierResourceResponse.setError_code(errorCode);
			zapierResourceResponse.setError_message(errorMessage);
			return new JacksonRepresentation<ZapierResourceResponse>(zapierResourceResponse);
		}
	}
}
