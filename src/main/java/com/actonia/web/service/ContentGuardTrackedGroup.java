package com.actonia.web.service;

import java.util.Arrays;

public class ContentGuardTrackedGroup {
	private Long group_id;
	private String group_name;
	private Integer crawl_frequency_type;
	private ContentGuardTrackedPage[] tracked_page_list;

	public Long getGroup_id() {
		return group_id;
	}

	public void setGroup_id(Long group_id) {
		this.group_id = group_id;
	}

	public String getGroup_name() {
		return group_name;
	}

	public void setGroup_name(String group_name) {
		this.group_name = group_name;
	}

	public Integer getCrawl_frequency_type() {
		return crawl_frequency_type;
	}

	public void setCrawl_frequency_type(Integer crawl_frequency_type) {
		this.crawl_frequency_type = crawl_frequency_type;
	}

	public ContentGuardTrackedPage[] getTracked_page_list() {
		return tracked_page_list;
	}

	public void setTracked_page_list(ContentGuardTrackedPage[] tracked_page_list) {
		this.tracked_page_list = tracked_page_list;
	}

	@Override
	public String toString() {
		return "ContentGuardTrackedGroup [group_id=" + group_id + ", group_name=" + group_name + ", crawl_frequency_type=" + crawl_frequency_type
				+ ", tracked_page_list=" + Arrays.toString(tracked_page_list) + "]";
	}

}
