package com.actonia.web.service;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Arrays;
import java.util.Date;

import com.actonia.value.object.*;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.restlet.ext.jackson.JacksonRepresentation;
import org.restlet.representation.Representation;
import org.restlet.representation.StringRepresentation;
import org.restlet.resource.Post;
import org.restlet.resource.ResourceException;

import com.actonia.IConstants;
import com.actonia.entity.ContentGuardChangeTrackingEntity;
import com.actonia.service.AccessTokenService;
import com.actonia.service.GetUrlSummaryService;
import com.actonia.service.ListChangeIndicatorsService;
import com.actonia.utils.ContentGuardUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.utils.TargetUrlChangeUtils;
import com.google.gson.Gson;

public class TargetUrlChangeResource extends BaseServerResouce {

	private String command;

	private AccessTokenService accessTokenService;

	private ListChangeIndicatorsService listChangeIndicatorsService;

	private GetUrlSummaryService getUrlSummaryService;

	private static final String[] VALID_COMMAND_ARRAY = new String[] { IConstants.COMMAND_LIST_CHANGE_INDICATORS, IConstants.COMMAND_LIST_CHANGE_INDICATORS + "V2", IConstants.COMMAND_GET_URL_SUMMARY,IConstants.COMMAND_LIST_CHANGE_INDICATORS + "V2_downloadAll" };

	public TargetUrlChangeResource() {
		super();
		accessTokenService = SpringBeanFactory.getBean("accessTokenService");
		listChangeIndicatorsService = SpringBeanFactory.getBean("listChangeIndicatorsService");
		getUrlSummaryService = SpringBeanFactory.getBean("getUrlSummaryService");
	}

	@Override
	protected void doInit() throws ResourceException {
		this.command = (String) getRequest().getAttributes().get("command");
	}

	@Post("application/json")
	public Representation doPost(Representation representationInput) {

		Representation representationOutput = null;
		String requestParameters = null;
		TargetUrlChangeRequest targetUrlChangeRequest = null;
		TargetUrlChangeResponse targetUrlChangeResponse = null;
		String accessToken = null;
		boolean isCommandValid = false;
		String accessTokenValidationErrorCode = null;

		try {
			isCommandValid = validateCommand();
			if (isCommandValid == false) {
				return generateJsonError(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_COMMAND_INVALID, command);
			}

			if (representationInput == null) {
				return generateJsonError(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_JSON_REQUIRED);
			}

			requestParameters = representationInput.getText();
			FormatUtils.getInstance().logMemoryUsage("doPost() command=" + command + ",requestParameters=" + requestParameters);
			if (StringUtils.isBlank(requestParameters)) {
				return generateJsonError(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_JSON_REQUIRED);
			}

			targetUrlChangeRequest = new Gson().fromJson(requestParameters, TargetUrlChangeRequest.class);
			//FormatUtils.getInstance().logMemoryUsage("doPost() targetUrlChangeRequest=" + targetUrlChangeRequest.toString());

			accessToken = targetUrlChangeRequest.getAccess_token();
			if (StringUtils.isBlank(accessToken)) {
				return generateJsonError(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_ACCESS_TOKEN_REQUIRED);
			} else if (StringUtils.equals(accessToken, IConstants.INTERNAL_KEY) == false
					&& StringUtils.equalsIgnoreCase(accessToken, AccessTokenService.INTERNAL_KEY) == false) {
				accessTokenValidationErrorCode = accessTokenService.checkToken(accessToken);
				if (StringUtils.isNotBlank(accessTokenValidationErrorCode)) {
					return generateJsonError(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_ACCESS_TOKEN_INVALID, accessToken);
				}
			}

			representationOutput = validateRequest(targetUrlChangeRequest);
			// when validation failed, return the error JSON representation
			if (representationOutput != null) {
				return representationOutput;
			}

			targetUrlChangeResponse = new TargetUrlChangeResponse();

			// command:list_change_indicators
			if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_LIST_CHANGE_INDICATORS)) {
				targetUrlChangeResponse = listChangeIndicatorsService.getList(command, targetUrlChangeRequest);
			}
			// command:list_change_indicatorsV2
			else if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_LIST_CHANGE_INDICATORS + "V2")) {
				final TargetUrlChangeResponseV2 listV2 = listChangeIndicatorsService.getListV2(command, targetUrlChangeRequest);
				return new JacksonRepresentation<>(listV2);
			}
			// command:list_change_indicatorsV2 download all
			else if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_LIST_CHANGE_INDICATORS + "V2_downloadAll")) {
				String sql = listChangeIndicatorsService.getTargetUrlChangeIndicatorDetailsDownLoadSql(targetUrlChangeRequest);
				return new StringRepresentation(sql);
			}
			// command:get_url_summary
			else if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_GET_URL_SUMMARY)) {
				targetUrlChangeResponse = getUrlSummaryService.getList(command, targetUrlChangeRequest);
			}

			try {
				if (BooleanUtils.isTrue(targetUrlChangeRequest.getDebug_ind()) == true) {
					if (targetUrlChangeResponse != null) {
						System.out.println("doPost() ends. targetUrlChangeResponse=" + new Gson().toJson(targetUrlChangeResponse, TargetUrlChangeResponse.class));
					} else {
						System.out.println("doPost() ends. targetUrlChangeResponse is null.");
					}
				}
			} catch (Exception e) {
				e.printStackTrace();
			}

			return new JacksonRepresentation<TargetUrlChangeResponse>(targetUrlChangeResponse);
		} catch (Exception e) {
			e.printStackTrace();
			StringWriter stringWriter = new StringWriter();
			e.printStackTrace(new PrintWriter(stringWriter));

			// send support alert
			String message = stringWriter.toString();

			return generateJsonError(IConstants.MSG_CD_TARGET_URL_CHANGE_WEB_SERVICE_METHOD_EXCEPTION, message);

		}
	}

	private boolean validateCommand() {
		boolean output = false;
		if (StringUtils.isNotBlank(command)) {
			nextValidCommand: for (String validCommand : VALID_COMMAND_ARRAY) {
				if (StringUtils.equalsIgnoreCase(command, validCommand)) {
					output = true;
					break nextValidCommand;
				}
			}
		}
		return output;
	}

	private Representation validateRequest(TargetUrlChangeRequest targetUrlChangeRequest) throws Exception {
		Representation representation = null;
		TargetUrlChangeResponse targetUrlChangeResponse = null;
		WebServiceError webServiceError = null;

		ValidationError validationError = getValidationErrorCode(targetUrlChangeRequest);

		if (validationError != null) {
			targetUrlChangeResponse = new TargetUrlChangeResponse();
			webServiceError = new WebServiceError();
			webServiceError.setError_code(validationError.getErrorCode());
			webServiceError.setError_message(TargetUrlChangeUtils.getInstance().getErrorMessage(validationError.getErrorCode(), validationError.getErrorData()));
			targetUrlChangeResponse.setError(webServiceError);
			targetUrlChangeResponse.setSuccess(false);
			representation = new JacksonRepresentation<TargetUrlChangeResponse>(targetUrlChangeResponse);
		}

		return representation;
	}

	private ValidationError getValidationErrorCode(TargetUrlChangeRequest targetUrlChangeRequest) throws Exception {
		ValidationError validationError = null;

		// command:list_change_indicators
		if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_LIST_CHANGE_INDICATORS)) {
			validationError = validationListChangeIndicatorsCommand(targetUrlChangeRequest);
		}
		// command:list_change_urls
		else if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_GET_URL_SUMMARY)) {
			validationError = validationGetUrlSummaryCommand(targetUrlChangeRequest);
		}
		return validationError;
	}

	private ValidationError validationListChangeIndicatorsCommand(TargetUrlChangeRequest targetUrlChangeRequest) throws Exception {
		ValidationError validationError = null;

		// 'domain_id' is required
		if (targetUrlChangeRequest.getDomain_id() == null) {
			return getValidationError(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_DOMAIN_ID_REQUIRED);
		}
		// 'start_crawl_timestamp' is required
		else if (StringUtils.isBlank(targetUrlChangeRequest.getStart_crawl_timestamp())) {
			return getValidationError(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_START_CRAWL_TIMESTAMP_REQUIRED);
		}
		// 'start_crawl_timestamp' is invalid
		else if (isCrawlTimestampValid(targetUrlChangeRequest.getStart_crawl_timestamp()) == false) {
			return getValidationError(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_START_CRAWL_TIMESTAMP_INVALID,
					targetUrlChangeRequest.getStart_crawl_timestamp());
		}
		// 'end_crawl_timestamp' is required
		else if (StringUtils.isBlank(targetUrlChangeRequest.getEnd_crawl_timestamp())) {
			return getValidationError(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_END_CRAWL_TIMESTAMP_REQUIRED);
		}
		// 'end_crawl_timestamp' is invalid
		else if (isCrawlTimestampValid(targetUrlChangeRequest.getEnd_crawl_timestamp()) == false) {
			return getValidationError(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_END_CRAWL_TIMESTAMP_INVALID, targetUrlChangeRequest.getEnd_crawl_timestamp());
		}
		// 'start_crawl_timestamp' is later than 'end_crawl_timestamp'
		else if (isCrawlTimestampValid(targetUrlChangeRequest.getStart_crawl_timestamp(), targetUrlChangeRequest.getEnd_crawl_timestamp()) == false) {
			return getValidationError(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_START_CRAWL_TIMESTAMP_LATER_THAN_END_CRAWL_TIMESTAMP);
		}
		// 'page_number' is required
		else if (targetUrlChangeRequest.getPage_number() == null) {
			return getValidationError(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_PAGE_NUMBER_REQUIRED);
		}
		// 'page_number' is invalid
		else if (targetUrlChangeRequest.getPage_number().intValue() < 1) {
			return getValidationError(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_PAGE_NUMBER_INVALID, String.valueOf(targetUrlChangeRequest.getPage_number()));
		}
		// 'rows_per_page' is required
		else if (targetUrlChangeRequest.getRows_per_page() == null) {
			return getValidationError(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_ROWS_PER_PAGE_REQUIRED);
		}
		// 'rows_per_page' is invalid
		else if (targetUrlChangeRequest.getRows_per_page().intValue() < 1) {
			return getValidationError(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_ROWS_PER_PAGE_INVALID,
					String.valueOf(targetUrlChangeRequest.getRows_per_page()));
		}
		// 'sort_by' is required
		else if (targetUrlChangeRequest.getSort_by() == null) {
			return getValidationError(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_SORT_BY_REQUIRED);
		}
		// 'sort_by' is invalid
		else if (isValidListChangeIndicatorsCommandSortBy(targetUrlChangeRequest.getSort_by()) == false) {
			return getValidationError(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_SORT_BY_INVALID, String.valueOf(targetUrlChangeRequest.getSort_by()));
		}
		// 'change_indicators' is optional but must be valid when present
		else if (isValidChangeIndicators(targetUrlChangeRequest.getChange_indicators()) == false) {
			return getValidationError(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_CHANGE_INDICATORS_INVALID,
					Arrays.toString(targetUrlChangeRequest.getChange_indicators()));
		}
		// 'content_types' is optional but must be valid when present
		else if (isValidContentTypes(targetUrlChangeRequest.getContent_types()) == false) {
			return getValidationError(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_CONTENT_TYPES_INVALID,
					Arrays.toString(targetUrlChangeRequest.getContent_types()));
		}
		// 'response_codes' is optional but change indicators must contain 'response_code_chg_ind'
		else if (isValidResponseCodeFilterRequest(targetUrlChangeRequest) == false) {
			return getValidationError(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_MUST_INCLUDE_RESP_CD_CHG_IND);
		}
		// 'response_codes' is optional but must be valid when present
		else if (isValidResponseCodeFilters(targetUrlChangeRequest) == false) {
			return getValidationError(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_RESPONSE_CODES_INVALID,
					Arrays.toString(targetUrlChangeRequest.getResponse_codes()));
		}
		// 'summary' is required but must be valid when present
		else if (isValidSummary(targetUrlChangeRequest) == false) {
			return getValidationError(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_SUMMARY_INVALID, targetUrlChangeRequest.getSummary());
		}

		return validationError;
	}

	private ValidationError validationGetUrlSummaryCommand(TargetUrlChangeRequest targetUrlChangeRequest) throws Exception {
		ValidationError validationError = null;

		// 'domain_id' is required
		if (targetUrlChangeRequest.getDomain_id() == null) {
			return getValidationError(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_DOMAIN_ID_REQUIRED);
		}
		// 'start_crawl_timestamp' is required
		else if (StringUtils.isBlank(targetUrlChangeRequest.getStart_crawl_timestamp())) {
			return getValidationError(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_START_CRAWL_TIMESTAMP_REQUIRED);
		}
		// 'start_crawl_timestamp' is invalid
		else if (isCrawlTimestampValid(targetUrlChangeRequest.getStart_crawl_timestamp()) == false) {
			return getValidationError(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_START_CRAWL_TIMESTAMP_INVALID,
					targetUrlChangeRequest.getStart_crawl_timestamp());
		}
		// 'end_crawl_timestamp' is required
		else if (StringUtils.isBlank(targetUrlChangeRequest.getEnd_crawl_timestamp())) {
			return getValidationError(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_END_CRAWL_TIMESTAMP_REQUIRED);
		}
		// 'end_crawl_timestamp' is invalid
		else if (isCrawlTimestampValid(targetUrlChangeRequest.getEnd_crawl_timestamp()) == false) {
			return getValidationError(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_END_CRAWL_TIMESTAMP_INVALID, targetUrlChangeRequest.getEnd_crawl_timestamp());
		}
		// 'start_crawl_timestamp' is later than 'end_crawl_timestamp'
		else if (isCrawlTimestampValid(targetUrlChangeRequest.getStart_crawl_timestamp(), targetUrlChangeRequest.getEnd_crawl_timestamp()) == false) {
			return getValidationError(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_START_CRAWL_TIMESTAMP_LATER_THAN_END_CRAWL_TIMESTAMP);
		}
		// 'page_number' is required
		else if (targetUrlChangeRequest.getPage_number() == null) {
			return getValidationError(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_PAGE_NUMBER_REQUIRED);
		}
		// 'page_number' is invalid
		else if (targetUrlChangeRequest.getPage_number().intValue() < 1) {
			return getValidationError(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_PAGE_NUMBER_INVALID, String.valueOf(targetUrlChangeRequest.getPage_number()));
		}
		// 'rows_per_page' is required
		else if (targetUrlChangeRequest.getRows_per_page() == null) {
			return getValidationError(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_ROWS_PER_PAGE_REQUIRED);
		}
		// 'rows_per_page' is invalid
		else if (targetUrlChangeRequest.getRows_per_page().intValue() < 1) {
			return getValidationError(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_ROWS_PER_PAGE_INVALID,
					String.valueOf(targetUrlChangeRequest.getRows_per_page()));
		}
		// 'sort_by' is required
		else if (targetUrlChangeRequest.getSort_by() == null) {
			return getValidationError(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_SORT_BY_REQUIRED);
		}
		// 'sort_by' is invalid
		else if (isValidGetUrlSummaryCommandSortBy(targetUrlChangeRequest.getSort_by()) == false) {
			return getValidationError(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_SORT_BY_INVALID, String.valueOf(targetUrlChangeRequest.getSort_by()));
		}
		// 'content_types' is optional but must be valid when present
		else if (isValidContentTypes(targetUrlChangeRequest.getContent_types()) == false) {
			return getValidationError(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_CONTENT_TYPES_INVALID,
					Arrays.toString(targetUrlChangeRequest.getContent_types()));
		}

		return validationError;
	}

	private boolean isValidSummary(TargetUrlChangeRequest targetUrlChangeRequest) {
		boolean output = false;
		if (StringUtils.isNotBlank(targetUrlChangeRequest.getSummary())) {
			if (StringUtils.equalsIgnoreCase(targetUrlChangeRequest.getSummary(), IConstants.DAILY)
					|| StringUtils.equalsIgnoreCase(targetUrlChangeRequest.getSummary(), IConstants.HOURLY)) {
				output = true;
			}
		} else {
			output = true;
		}
		return output;
	}

	private boolean isValidResponseCodeFilterRequest(TargetUrlChangeRequest targetUrlChangeRequest) {
		boolean output = true;
		boolean isResponseCodeChgInd = false;
		// when filtering by response codes
		if (targetUrlChangeRequest.getResponse_codes() != null && targetUrlChangeRequest.getResponse_codes().length > 0) {
			// if change indicators are available in request, 'response_code_chg_ind' must be present
			if (targetUrlChangeRequest.getChange_indicators() != null && targetUrlChangeRequest.getChange_indicators().length > 0) {
				nextChangeIndicator: for (String changeIndicator : targetUrlChangeRequest.getChange_indicators()) {
					if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.RESPONSE_CODE_CHG_IND)) {
						isResponseCodeChgInd = true;
						break nextChangeIndicator;
					}
				}
			} else {
				isResponseCodeChgInd = false;
			}
			// when 'response_code_chg_ind' is not present
			if (isResponseCodeChgInd == false) {
				output = false;
			}
		}
		return output;
	}

	private boolean isValidResponseCodeFilters(TargetUrlChangeRequest targetUrlChangeRequest) {
		boolean output = true;
		// when filtering by response codes
		if (targetUrlChangeRequest.getResponse_codes() != null && targetUrlChangeRequest.getResponse_codes().length > 0) {
			nextResponseCodeFilter: for (ResponseCodeFilter responseCodeFilter : targetUrlChangeRequest.getResponse_codes()) {
				if (StringUtils.isBlank(responseCodeFilter.getResponse_code_previous()) || StringUtils.isBlank(responseCodeFilter.getResponse_code_current())) {
					output = false;
					break nextResponseCodeFilter;
				} else if (StringUtils.equalsIgnoreCase(responseCodeFilter.getResponse_code_previous(), responseCodeFilter.getResponse_code_current()) == true) {
					output = false;
					break nextResponseCodeFilter;
				} else if (NumberUtils.toInt(responseCodeFilter.getResponse_code_previous()) == 0
						|| NumberUtils.toInt(responseCodeFilter.getResponse_code_current()) == 0) {
					output = false;
					break nextResponseCodeFilter;
				}
			}
		}
		return output;
	}

	private boolean isValidChangeIndicators(String[] changeIndicators) {
		boolean output = true;
		ContentGuardChangeTrackingEntity contentGuardChangeTrackingEntity = null;
		if (changeIndicators != null && changeIndicators.length > 0) {
			nextChangeIndicator: for (String changeIndicator : changeIndicators) {
				if (StringUtils.startsWithIgnoreCase(changeIndicator, IConstants.RESPONSE_CODE_CHG_IND)
						&& StringUtils.equalsIgnoreCase(changeIndicator, IConstants.RESPONSE_CODE_CHG_IND) == false) {
					output = false;
					break nextChangeIndicator;
				} else {
					contentGuardChangeTrackingEntity = ContentGuardUtils.getInstance().getContentGuardChangeTrackingEntity(changeIndicator);
					if (contentGuardChangeTrackingEntity == null) {
						output = false;
						break nextChangeIndicator;
					}
				}
			}
		}
		return output;
	}

	private boolean isValidContentTypes(TargetUrlChangeContentType[] contentTypes) {
		boolean output = true;
		if (contentTypes != null && contentTypes.length > 0) {
			nextTargetUrlChangeContentType: for (TargetUrlChangeContentType targetUrlChangeContentType : contentTypes) {
				if ((targetUrlChangeContentType.getLeaf() == null || targetUrlChangeContentType.getAction() == null || targetUrlChangeContentType.getValue() == null
						|| targetUrlChangeContentType.getLevel() == null || targetUrlChangeContentType.getCond() == null)
						|| (StringUtils.equalsIgnoreCase(targetUrlChangeContentType.getAction(), IConstants.CONTENT_TYPE_ACTION_CONTAINS) == false
								&& StringUtils.equalsIgnoreCase(targetUrlChangeContentType.getAction(), IConstants.CONTENT_TYPE_ACTION_DOES_NOT_CONTAIN) == false
								&& StringUtils.equalsIgnoreCase(targetUrlChangeContentType.getAction(), IConstants.CONTENT_TYPE_ACTION_ENDS_WITH) == false
								&& StringUtils.equalsIgnoreCase(targetUrlChangeContentType.getAction(), IConstants.CONTENT_TYPE_ACTION_IS) == false
								&& StringUtils.equalsIgnoreCase(targetUrlChangeContentType.getAction(), IConstants.CONTENT_TYPE_ACTION_IS_NOT) == false
								&& StringUtils.equalsIgnoreCase(targetUrlChangeContentType.getAction(), IConstants.CONTENT_TYPE_ACTION_REGEXP) == false
								&& StringUtils.equalsIgnoreCase(targetUrlChangeContentType.getAction(), IConstants.CONTENT_TYPE_ACTION_REGEXP_NOT_MATCH) == false
								&& StringUtils.equalsIgnoreCase(targetUrlChangeContentType.getAction(), IConstants.CONTENT_TYPE_ACTION_STARTS_WITH) == false)
						|| (StringUtils.equalsIgnoreCase(targetUrlChangeContentType.getCond(), IConstants.AND) == false
								&& StringUtils.equalsIgnoreCase(targetUrlChangeContentType.getCond(), IConstants.OR) == false)) {
					output = false;
					break nextTargetUrlChangeContentType;
				}
			}
		}
		return output;
	}

	private boolean isValidListChangeIndicatorsCommandSortBy(Integer sortBy) {
		boolean output = false;
		if (sortBy.intValue() == IConstants.SORT_BY_URL_ASC || sortBy.intValue() == IConstants.SORT_BY_URL_DESC
				|| sortBy.intValue() == IConstants.SORT_BY_CRAWL_TIMESTAMP_ASC || sortBy.intValue() == IConstants.SORT_BY_CRAWL_TIMESTAMP_DESC
				|| sortBy.intValue() == IConstants.SORT_BY_CHANGE_INDICATOR_ASC || sortBy.intValue() == IConstants.SORT_BY_CHANGE_INDICATOR_DESC
				|| sortBy.intValue() == IConstants.SORT_BY_CHANGE_TYPE_ASC || sortBy.intValue() == IConstants.SORT_BY_CHANGE_TYPE_DESC
				|| sortBy.intValue() == IConstants.SORT_BY_SEVERITY_ASC || sortBy.intValue() == IConstants.SORT_BY_SEVERITY_DESC) {
			output = true;
		}
		return output;
	}

	private boolean isValidGetUrlSummaryCommandSortBy(Integer sortBy) {
		boolean output = false;
		if (sortBy.intValue() == IConstants.SORT_BY_URL_ASC || sortBy.intValue() == IConstants.SORT_BY_URL_DESC
				|| sortBy.intValue() == IConstants.SORT_BY_TOTAL_CHANGES_ASC || sortBy.intValue() == IConstants.SORT_BY_TOTAL_CHANGES_DESC
				|| sortBy.intValue() == IConstants.SORT_BY_TOTAL_SEVERITY_CRITICAL_ASC || sortBy.intValue() == IConstants.SORT_BY_TOTAL_SEVERITY_CRITICAL_DESC
				|| sortBy.intValue() == IConstants.SORT_BY_TOTAL_SEVERITY_HIGH_ASC || sortBy.intValue() == IConstants.SORT_BY_TOTAL_SEVERITY_HIGH_DESC
				|| sortBy.intValue() == IConstants.SORT_BY_TOTAL_SEVERITY_MEDIUM_ASC || sortBy.intValue() == IConstants.SORT_BY_TOTAL_SEVERITY_MEDIUM_DESC
				|| sortBy.intValue() == IConstants.SORT_BY_TOTAL_SEVERITY_LOW_ASC || sortBy.intValue() == IConstants.SORT_BY_TOTAL_SEVERITY_LOW_DESC
				|| sortBy.intValue() == IConstants.SORT_BY_TOTAL_CHANGE_TYPE_ADDED_ASC || sortBy.intValue() == IConstants.SORT_BY_TOTAL_CHANGE_TYPE_ADDED_DESC
				|| sortBy.intValue() == IConstants.SORT_BY_TOTAL_CHANGE_TYPE_MODIFIED_ASC || sortBy.intValue() == IConstants.SORT_BY_TOTAL_CHANGE_TYPE_MODIFIED_DESC
				|| sortBy.intValue() == IConstants.SORT_BY_TOTAL_CHANGE_TYPE_REMOVED_ASC || sortBy.intValue() == IConstants.SORT_BY_TOTAL_CHANGE_TYPE_REMOVED_DESC) {
			output = true;
		}
		return output;
	}

	private boolean isCrawlTimestampValid(String crawlTimestamp) {
		boolean output = false;
		try {
			DateUtils.parseDateStrictly(crawlTimestamp, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS });
			output = true;
		} catch (Exception e) {
			output = false;
		}
		return output;
	}

	private boolean isCrawlTimestampValid(String startCrawlTimestampString, String endCrawlTimestampString) {
		boolean output = false;
		Date startCrawlTimestamp = null;
		Date endCrawlTimestamp = null;
		try {
			startCrawlTimestamp = DateUtils.parseDateStrictly(startCrawlTimestampString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS });
			endCrawlTimestamp = DateUtils.parseDateStrictly(endCrawlTimestampString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS });
			if (startCrawlTimestamp.after(endCrawlTimestamp)) {
				output = false;
			} else {
				output = true;
			}
		} catch (Exception e) {
			output = false;
		}
		return output;
	}

	private ValidationError getValidationError(String errorCode) {
		return getValidationError(errorCode, null);
	}

	private ValidationError getValidationError(String errorCode, String errorData) {
		ValidationError validationError = new ValidationError();
		validationError.setErrorCode(errorCode);
		validationError.setErrorData(errorData);
		return validationError;
	}

	public Representation generateJsonError(String errorCode) {
		return generateJsonError(errorCode, null);
	}

	public Representation generateJsonError(String errorCode, String supplementalMessageText) {
		FormatUtils.getInstance().logMemoryUsage("generateJsonError() errorCode=" + errorCode + ",supplementalMessageText=" + supplementalMessageText);
		String errorMessage = TargetUrlChangeUtils.getInstance().getErrorMessage(errorCode, supplementalMessageText);
		TargetUrlChangeResponse targetUrlChangeResponse = new TargetUrlChangeResponse();
		targetUrlChangeResponse.setSuccess(false);
		WebServiceError webServiceError = new WebServiceError();
		webServiceError.setError_code(errorCode);
		webServiceError.setError_message(errorMessage);
		targetUrlChangeResponse.setError(webServiceError);
		return new JacksonRepresentation<TargetUrlChangeResponse>(targetUrlChangeResponse);
	}

	public String getErrorMessage(String errorCode) {
		return TargetUrlChangeUtils.getInstance().getErrorMessage(errorCode, null);
	}
}
