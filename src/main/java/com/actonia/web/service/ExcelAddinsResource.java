package com.actonia.web.service;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.restlet.data.Form;
import org.restlet.ext.jackson.JacksonRepresentation;
import org.restlet.representation.Representation;
import org.restlet.resource.Get;
import org.restlet.resource.ResourceException;

import com.actonia.IConstants;
import com.actonia.dao.ClarityAuditsEntityDAO;
import com.actonia.dao.CrawlAuditRuleDAO;
import com.actonia.dao.CrawlRequestLogDAO;
import com.actonia.dao.OwnDomainEntityDAO;
import com.actonia.entity.ClarityAuditsEntity;
import com.actonia.entity.CrawlAuditRuleEntity;
import com.actonia.entity.CrawlRequestLog;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.service.AccessTokenService;
import com.actonia.service.OwnDomainService;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.CrawlRequestLogComparator;
import com.actonia.value.object.ExcelAddinsData;
import com.actonia.value.object.PageAnalysisRule;
import com.actonia.value.object.WebServiceError;
import com.google.gson.Gson;

public class ExcelAddinsResource extends BaseServerResouce {

	private String command;

	private AccessTokenService accessTokenService;
	private CrawlRequestLogDAO crawlRequestLogDAO;
	private ClarityAuditsEntityDAO clarityAuditsEntityDAO;
	private CrawlAuditRuleDAO crawlAuditRuleDAO;
	private OwnDomainEntityDAO ownDomainEntityDAO;

	public ExcelAddinsResource() {
		this.accessTokenService = SpringBeanFactory.getBean("accessTokenService");
		this.crawlRequestLogDAO = SpringBeanFactory.getBean("crawlRequestLogDAO");
		this.clarityAuditsEntityDAO = SpringBeanFactory.getBean("clarityAuditsEntityDAO");
		this.crawlAuditRuleDAO = SpringBeanFactory.getBean("crawlAuditRuleDAO");
		this.ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
	}

	@Override
	protected void doInit() throws ResourceException {
		this.command = (String) getRequest().getAttributes().get("command");
		FormatUtils.getInstance().logMemoryUsage("doInit() command=" + this.command);
	}

	@Get("json")
	public Representation doGet() {
		Representation result = null;
		Form queryParams = null;
		String access_token = null;
		String crawlRequestIdString = null;
		String startCrawlRequestDateString = null;
		String endCrawlRequestDateString = null;
		int crawlRequestId = 0;
		List<ExcelAddinsData> excelAddinsDataList = new ArrayList<ExcelAddinsData>();
		String accessTokenValidationErrorCode = null;
		int domainId = 0;
		String testString = null;
		int userId = 0;

		// map key = domain ID
		// map value = access token
		Map<Integer, String> domainIdAccessTokenMap = null;

		try {
			queryParams = getRequest().getResourceRef().getQueryAsForm();

			// access token
			access_token = queryParams.getFirstValue("access_token", true);
			FormatUtils.getInstance().logMemoryUsage("doGet() query parameter accessToken=" + access_token);

			// validate input access token
			if (StringUtils.isBlank(access_token)) {
				FormatUtils.getInstance().logMemoryUsage("doGet() error--accessToken is empty.");
				return new JacksonRepresentation<List<ExcelAddinsData>>(excelAddinsDataList);
			} else if (StringUtils.equalsIgnoreCase(access_token, IConstants.INTERNAL_KEY) == false
					&& StringUtils.equalsIgnoreCase(access_token, AccessTokenService.INTERNAL_KEY) == false) {
				accessTokenValidationErrorCode = accessTokenService.checkToken(access_token);
				if (StringUtils.isNotBlank(accessTokenValidationErrorCode)) {
					FormatUtils.getInstance()
							.logMemoryUsage("doGet() error--accessTokenValidationErrorCode=" + accessTokenValidationErrorCode + ",access_token=" + access_token);
					return new JacksonRepresentation<List<ExcelAddinsData>>(excelAddinsDataList);
				}
			}

			// domain ID
			testString = queryParams.getFirstValue("domain_id", true);
			if (StringUtils.isNotBlank(testString)) {
				domainId = NumberUtils.toInt(testString);
				FormatUtils.getInstance().logMemoryUsage("doGet() query parameter domain_id string=" + testString + ",domainId=" + domainId);
				userId = accessTokenService.getUserIdByAccessToken(access_token);
				FormatUtils.getInstance().logMemoryUsage("doGet() userId=" + userId);
				domainIdAccessTokenMap = accessTokenService.getDomainIdAccessTokenMapByUserId(userId);
				if (domainIdAccessTokenMap.containsKey(domainId)) {
					access_token = domainIdAccessTokenMap.get(domainId);
				} else {
					access_token = null;
				}
				//if (isDebug == true) {
				//	for (Integer testDomainId : domainIdAccessTokenMap.keySet()) {
				//		FormatUtils.getInstance().logMemoryUsage("doGet() domainId=" + testDomainId + ",accessToken=" + domainIdAccessTokenMap.get(testDomainId));
				//	}
				//}
			}
			FormatUtils.getInstance().logMemoryUsage("doGet() final access_token=" + access_token);
			if (StringUtils.isNotBlank(access_token)) {

				domainId = accessTokenService.getDomainIdByAccessToken(access_token);
				FormatUtils.getInstance().logMemoryUsage("doGet() domainId=" + domainId);

				//command:crawl_project
				if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_CRAWL_PROJECT)) {

					// start crawl request date
					startCrawlRequestDateString = queryParams.getFirstValue("start_crawl_request_date", true);
					FormatUtils.getInstance().logMemoryUsage("doGet() query parameter startCrawlRequestDateString=" + startCrawlRequestDateString);

					// end crawl request date
					endCrawlRequestDateString = queryParams.getFirstValue("end_crawl_request_date", true);
					FormatUtils.getInstance().logMemoryUsage("doGet() query parameter endCrawlRequestDateString=" + endCrawlRequestDateString);

					excelAddinsDataList = getCrawlProjects(domainId, startCrawlRequestDateString, endCrawlRequestDateString);
				}
				//command:site_health_summary
				else if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_SITE_HEALTH_SUMMARY)) {

					// crawl request ID
					crawlRequestIdString = queryParams.getFirstValue("crawl_request_id", true);
					FormatUtils.getInstance().logMemoryUsage("doGet() query parameter crawlRequestIdString=" + crawlRequestIdString);

					crawlRequestId = NumberUtils.toInt(crawlRequestIdString);

					excelAddinsDataList = getSiteHealthSummary(domainId, crawlRequestId);
				}
				//command:search_engine
				else if (StringUtils.equalsIgnoreCase(command, IConstants.COMMAND_SEARCH_ENGINE)) {

					excelAddinsDataList = getSearchEngines(domainId);
				}
			}

			Set<String> testSet = new HashSet<String>();
			testSet.add("*");
			getResponse().setAccessControlAllowHeaders(testSet);
			getResponse().setAccessControlAllowCredentials(true);
			getResponse().setAccessControlAllowOrigin("*");
			result = new JacksonRepresentation<List<ExcelAddinsData>>(excelAddinsDataList);

		} catch (Exception e) {
			e.printStackTrace();
			StringWriter stringWriter = new StringWriter();
			e.printStackTrace(new PrintWriter(stringWriter));
			String message = stringWriter.toString();
			return generateJsonError("exception message is " + message);
		}

		return result;
	}

	private List<ExcelAddinsData> getCrawlProjects(int domainId, String startCrawlRequestDateString, String endCrawlRequestDateString) {
		FormatUtils.getInstance().logMemoryUsage("getCrawlProjects() begins. domainId=" + domainId + ",startCrawlRequestDateString=" + startCrawlRequestDateString
				+ ",endCrawlRequestDateString=" + endCrawlRequestDateString);
		long startTimestamp = System.currentTimeMillis();
		List<ExcelAddinsData> excelAddinsDataList = new ArrayList<ExcelAddinsData>();
		ExcelAddinsData excelAddinsData = null;
		int startCrawlRequestDateNumber = NumberUtils.toInt(StringUtils.remove(startCrawlRequestDateString, IConstants.DASH));
		int endCrawlRequestDateNumber = NumberUtils.toInt(StringUtils.remove(endCrawlRequestDateString, IConstants.DASH));
		List<CrawlRequestLog> crawlRequestLogList = crawlRequestLogDAO.getCrawlProjects(domainId, startCrawlRequestDateNumber, endCrawlRequestDateNumber);
		if (crawlRequestLogList != null && crawlRequestLogList.size() > 0) {
			FormatUtils.getInstance().logMemoryUsage("getCrawlProjects() crawlRequestLogList.size()=" + crawlRequestLogList.size());
			for (CrawlRequestLog crawlRequestLog : crawlRequestLogList) {
				FormatUtils.getInstance().logMemoryUsage("getCrawlProjects() crawlRequestLog.getProjectName()=" + crawlRequestLog.getProjectName()
						+ ",crawlRequestLog.getId()=" + crawlRequestLog.getId() + ",crawlRequestLog.getCrawlRequestDate()=" + crawlRequestLog.getCrawlRequestDate());
				excelAddinsData = new ExcelAddinsData();
				excelAddinsData.setProject_name(crawlRequestLog.getProjectName());
				excelAddinsData.setCrawl_request_date(FormatUtils.getInstance().convertDateNumberToString(crawlRequestLog.getCrawlRequestDate()));
				excelAddinsData.setCrawl_request_id(crawlRequestLog.getId());
				excelAddinsDataList.add(excelAddinsData);
			}
		}
		FormatUtils.getInstance().logMemoryUsage(
				"getCrawlProjects() ends. excelAddinsDataList.size()=" + excelAddinsDataList.size() + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return excelAddinsDataList;
	}

	private List<ExcelAddinsData> getSiteHealthSummary(int domainId, int crawlRequestId) {
		FormatUtils.getInstance().logMemoryUsage("getSiteHealthSummary() begins. domainId=" + domainId + ",crawlRequestId=" + crawlRequestId);
		long startTimestamp = System.currentTimeMillis();
		List<ExcelAddinsData> excelAddinsDataList = new ArrayList<ExcelAddinsData>();
		ClarityAuditsEntity clarityAuditsEntity = null;

		// map key = rule ID ('rule_id' field of 'crawl_audit_rule' MySQL table)
		// map value = CrawlAuditRuleEntity
		Map<String, CrawlAuditRuleEntity> ruleIdCrawlAuditRuleEntityMap = getRuleIdCrawlAuditRuleEntityMap();

		List<CrawlRequestLog> crawlRequestLogList = crawlRequestLogDAO.getLastFiveCompletedCrawls(domainId, crawlRequestId);
		if (crawlRequestLogList == null || crawlRequestLogList.size() == 0) {
			FormatUtils.getInstance()
					.logMemoryUsage("getSiteHealthSummary() error--crawlRequestLogList is empty,domainId=" + domainId + ",crawlRequestId=" + crawlRequestId);
			return excelAddinsDataList;
		}

		FormatUtils.getInstance().logMemoryUsage("getSiteHealthSummary() crawlRequestLogList.size()=" + crawlRequestLogList.size());

		Collections.sort(crawlRequestLogList, new CrawlRequestLogComparator());

		for (CrawlRequestLog crawlRequestLog : crawlRequestLogList) {
			FormatUtils.getInstance().logMemoryUsage("getSiteHealthSummary() crawlRequestLog.getProjectName()=" + crawlRequestLog.getProjectName()
					+ ",crawlRequestLog.getId()=" + crawlRequestLog.getId() + ",crawlRequestLog.getCrawlRequestDate()=" + crawlRequestLog.getCrawlRequestDate());
			clarityAuditsEntity = clarityAuditsEntityDAO.getGrandTotals(crawlRequestLog.getId());
			if (clarityAuditsEntity != null) {
				FormatUtils.getInstance().logMemoryUsage(
						"getSiteHealthSummary() crawlRequestLog.getId()=" + crawlRequestLog.getId() + ",clarityAuditsEntity=" + clarityAuditsEntity.toString());

				// type: summary (audited pages)
				processSummaryAuditedPages(crawlRequestLog, clarityAuditsEntity, excelAddinsDataList);

				// type: crawlability issues (Redirected)
				processCrawlabilityIssuesRedirected(crawlRequestLog, clarityAuditsEntity, excelAddinsDataList);

				// type: crawlability issues (Broken)
				processCrawlabilityIssuesBroken(crawlRequestLog, clarityAuditsEntity, excelAddinsDataList);

				// type: crawlability issues (Error)
				processCrawlabilityIssuesError(crawlRequestLog, clarityAuditsEntity, excelAddinsDataList);

				// type: crawlability issues (Other)
				processCrawlabilityIssuesOther(crawlRequestLog, clarityAuditsEntity, excelAddinsDataList);

				// type: page analysis issues
				processPageAnalysisIssues(crawlRequestLog, clarityAuditsEntity, excelAddinsDataList, ruleIdCrawlAuditRuleEntityMap);

			} else {
				FormatUtils.getInstance().logMemoryUsage("getSiteHealthSummary() clarityAuditsEntityList is empty.");
			}
		}
		FormatUtils.getInstance().logMemoryUsage("getSiteHealthSummary() ends. excelAddinsDataList.size()=" + excelAddinsDataList.size() + ",elapsed(ms.)="
				+ (System.currentTimeMillis() - startTimestamp));
		return excelAddinsDataList;
	}

	private List<ExcelAddinsData> getSearchEngines(int domainId) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("getSearchEngines() begins. domainId=" + domainId);
		Object object = null;
		String mapValue = null;
		//int listIndex = 0;
		int engineId = 0;
		int languageId = 0;
		String engineName = null;
		String device = null;
		ExcelAddinsData excelAddinsData = null;
		List<ExcelAddinsData> excelAddinsDataList = null;
		String domainName = IConstants.EMPTY_STRING;

		OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(domainId);
		if (ownDomainEntity != null) {
			domainName = ownDomainEntity.getDomain();
		}

		List<Map<String, Object>> searchEnginesForJson4 = OwnDomainService.getSearchEnginesForJson4(domainId, false);
		if (searchEnginesForJson4 != null && searchEnginesForJson4.size() > 0) {
			excelAddinsDataList = new ArrayList<ExcelAddinsData>();
			for (Map<String, Object> map : searchEnginesForJson4) {
				//++listIndex;
				engineId = 0;
				languageId = 0;
				engineName = null;
				device = null;
				for (String mapKey : map.keySet()) {
					object = map.get(mapKey);
					if (object instanceof String) {
						mapValue = (String) object;
					} else if (object instanceof Integer) {
						mapValue = String.valueOf((Integer) object);
					} else if (object instanceof Boolean) {
						mapValue = String.valueOf((Boolean) object);
					}
					if (StringUtils.equalsIgnoreCase(mapKey, "engineId")) {
						engineId = NumberUtils.toInt(mapValue);
					} else if (StringUtils.equalsIgnoreCase(mapKey, "languageId")) {
						languageId = NumberUtils.toInt(mapValue);
					} else if (StringUtils.equalsIgnoreCase(mapKey, "engineName")) {
						engineName = mapValue;
					} else if (StringUtils.equalsIgnoreCase(mapKey, "device")) {
						device = mapValue;
					}
				}
				if (engineId > 0 && languageId > 0 && StringUtils.isNotBlank(engineName) && StringUtils.isNotBlank(device)) {
					//FormatUtils.getInstance().logMemoryUsage("getSearchEngines() domainId=" + domainId + ",listIndex=" + listIndex + ",device=" + device + ",engineId=" + engineId + ",languageId="
					//		+ languageId + ",engineName=" + engineName);
					excelAddinsData = new ExcelAddinsData();
					excelAddinsData.setDomain_id(domainId);
					excelAddinsData.setDomain_name(domainName);
					excelAddinsData.setEngine_id(engineId);
					excelAddinsData.setLanguage_id(languageId);
					excelAddinsData.setDevice(device);
					excelAddinsData.setEngine_name(engineName);
					excelAddinsDataList.add(excelAddinsData);
				}
			}
		} else {
			FormatUtils.getInstance().logMemoryUsage("getSearchEngines() searchEnginesForJson4 is empty.");
		}
		if (excelAddinsDataList != null && excelAddinsDataList.size() > 0) {
			FormatUtils.getInstance().logMemoryUsage("getSearchEngines() ends. excelAddinsDataList.size()=" + excelAddinsDataList.size());
			//for (ExcelAddinsData testExcelAddinsData : excelAddinsDataList) {
			//	FormatUtils.getInstance().logMemoryUsage("getSearchEngines() excelAddinsData=" + new Gson().toJson(testExcelAddinsData, ExcelAddinsData.class));
			//}
		} else {
			FormatUtils.getInstance().logMemoryUsage("getSearchEngines() ends. excelAddinsDataList is empty for domain ID=" + domainId);
		}
		return excelAddinsDataList;
	}

	private Map<String, CrawlAuditRuleEntity> getRuleIdCrawlAuditRuleEntityMap() {
		Map<String, CrawlAuditRuleEntity> ruleIdCrawlAuditRuleEntityMap = new HashMap<String, CrawlAuditRuleEntity>();

		// retrieve all crawl audit rules reference records
		List<CrawlAuditRuleEntity> crawlAuditRuleEntityList = crawlAuditRuleDAO.getList();
		if (crawlAuditRuleEntityList != null && crawlAuditRuleEntityList.size() > 0) {
			for (CrawlAuditRuleEntity crawlAuditRuleEntity : crawlAuditRuleEntityList) {
				ruleIdCrawlAuditRuleEntityMap.put(crawlAuditRuleEntity.getRuleId(), crawlAuditRuleEntity);
			}
		}

		return ruleIdCrawlAuditRuleEntityMap;
	}

	// type: summary (audited pages)
	// update 'excelAddinsDataList' using pass by reference
	private void processSummaryAuditedPages(CrawlRequestLog crawlRequestLog, ClarityAuditsEntity clarityAuditsEntity, List<ExcelAddinsData> excelAddinsDataList) {
		ExcelAddinsData excelAddinsData = new ExcelAddinsData();
		excelAddinsData.setDomain_id(crawlRequestLog.getOwnDomainId());
		excelAddinsData.setProject_name(crawlRequestLog.getProjectName());
		excelAddinsData.setCrawl_request_date(getInternationalDateFormat(crawlRequestLog.getCrawlRequestDate()));
		excelAddinsData.setCrawl_request_id(crawlRequestLog.getId());
		excelAddinsData.setType(IConstants.SUMMARY);
		excelAddinsData.setDescription(IConstants.AUDITED_PAGES);
		excelAddinsData.setImportance(IConstants.EMPTY_STRING);
		if (clarityAuditsEntity.getTotalPage() != null) {
			excelAddinsData.setCount(clarityAuditsEntity.getTotalPage());
		} else {
			excelAddinsData.setCount(0);
		}
		excelAddinsDataList.add(excelAddinsData);
	}

	// type: crawlability issues (Redirected)
	// update 'excelAddinsDataList' using pass by reference
	private void processCrawlabilityIssuesRedirected(CrawlRequestLog crawlRequestLog, ClarityAuditsEntity clarityAuditsEntity,
			List<ExcelAddinsData> excelAddinsDataList) {
		ExcelAddinsData excelAddinsData = new ExcelAddinsData();
		excelAddinsData.setDomain_id(crawlRequestLog.getOwnDomainId());
		excelAddinsData.setProject_name(crawlRequestLog.getProjectName());
		excelAddinsData.setCrawl_request_date(getInternationalDateFormat(crawlRequestLog.getCrawlRequestDate()));
		excelAddinsData.setCrawl_request_id(crawlRequestLog.getId());
		excelAddinsData.setType(IConstants.CRAWLABILITY_ISSUES);
		excelAddinsData.setDescription(IConstants.REDIRECTED_3XX);
		excelAddinsData.setImportance(IConstants.EMPTY_STRING);
		if (clarityAuditsEntity.getRespCode3xx() != null) {
			excelAddinsData.setCount(clarityAuditsEntity.getRespCode3xx());
		} else {
			excelAddinsData.setCount(0);
		}
		excelAddinsDataList.add(excelAddinsData);
	}

	// type: crawlability issues (Broken)
	// update 'excelAddinsDataList' using pass by reference
	private void processCrawlabilityIssuesBroken(CrawlRequestLog crawlRequestLog, ClarityAuditsEntity clarityAuditsEntity, List<ExcelAddinsData> excelAddinsDataList) {
		ExcelAddinsData excelAddinsData = new ExcelAddinsData();
		excelAddinsData.setDomain_id(crawlRequestLog.getOwnDomainId());
		excelAddinsData.setProject_name(crawlRequestLog.getProjectName());
		excelAddinsData.setCrawl_request_date(getInternationalDateFormat(crawlRequestLog.getCrawlRequestDate()));
		excelAddinsData.setCrawl_request_id(crawlRequestLog.getId());
		excelAddinsData.setType(IConstants.CRAWLABILITY_ISSUES);
		excelAddinsData.setDescription(IConstants.BROKEN_4XX);
		excelAddinsData.setImportance(IConstants.EMPTY_STRING);
		if (clarityAuditsEntity.getRespCode4xx() != null) {
			excelAddinsData.setCount(clarityAuditsEntity.getRespCode4xx());
		} else {
			excelAddinsData.setCount(0);
		}
		excelAddinsDataList.add(excelAddinsData);
	}

	// type: crawlability issues (Error)
	// update 'excelAddinsDataList' using pass by reference
	private void processCrawlabilityIssuesError(CrawlRequestLog crawlRequestLog, ClarityAuditsEntity clarityAuditsEntity, List<ExcelAddinsData> excelAddinsDataList) {
		ExcelAddinsData excelAddinsData = new ExcelAddinsData();
		excelAddinsData.setDomain_id(crawlRequestLog.getOwnDomainId());
		excelAddinsData.setProject_name(crawlRequestLog.getProjectName());
		excelAddinsData.setCrawl_request_date(getInternationalDateFormat(crawlRequestLog.getCrawlRequestDate()));
		excelAddinsData.setCrawl_request_id(crawlRequestLog.getId());
		excelAddinsData.setType(IConstants.CRAWLABILITY_ISSUES);
		excelAddinsData.setDescription(IConstants.ERROR_5XX);
		excelAddinsData.setImportance(IConstants.EMPTY_STRING);
		if (clarityAuditsEntity.getRespCode5xx() != null) {
			excelAddinsData.setCount(clarityAuditsEntity.getRespCode5xx());
		} else {
			excelAddinsData.setCount(0);
		}
		excelAddinsDataList.add(excelAddinsData);
	}

	// type: crawlability issues (Other)
	// update 'excelAddinsDataList' using pass by reference
	private void processCrawlabilityIssuesOther(CrawlRequestLog crawlRequestLog, ClarityAuditsEntity clarityAuditsEntity, List<ExcelAddinsData> excelAddinsDataList) {
		ExcelAddinsData excelAddinsData = new ExcelAddinsData();
		excelAddinsData.setDomain_id(crawlRequestLog.getOwnDomainId());
		excelAddinsData.setProject_name(crawlRequestLog.getProjectName());
		excelAddinsData.setCrawl_request_date(getInternationalDateFormat(crawlRequestLog.getCrawlRequestDate()));
		excelAddinsData.setCrawl_request_id(crawlRequestLog.getId());
		excelAddinsData.setType(IConstants.CRAWLABILITY_ISSUES);
		excelAddinsData.setDescription(IConstants.OTHER);
		excelAddinsData.setImportance(IConstants.EMPTY_STRING);
		if (clarityAuditsEntity.getRespCodeXxx() != null) {
			excelAddinsData.setCount(clarityAuditsEntity.getRespCodeXxx());
		} else {
			excelAddinsData.setCount(0);
		}
		excelAddinsDataList.add(excelAddinsData);
	}

	// type: page analysis issues
	// update 'excelAddinsDataList' using pass by reference
	private void processPageAnalysisIssues(CrawlRequestLog crawlRequestLog, ClarityAuditsEntity clarityAuditsEntity, List<ExcelAddinsData> excelAddinsDataList,
			Map<String, CrawlAuditRuleEntity> ruleIdCrawlAuditRuleEntityMap) {

		String crawlAuditRuleId = null;
		String crawlAuditRuleDesc = null;
		String crawlAuditRuleSeverity = null;
		CrawlAuditRuleEntity crawlAuditRuleEntity = null;
		ExcelAddinsData excelAddinsData = null;

		if (StringUtils.isBlank(clarityAuditsEntity.getPageAnalysisRulesJson())) {
			return;
		}

		PageAnalysisRule[] pageAnalysisRuleArray = new Gson().fromJson(clarityAuditsEntity.getPageAnalysisRulesJson(), PageAnalysisRule[].class);
		if (pageAnalysisRuleArray != null && pageAnalysisRuleArray.length > 0) {
			for (PageAnalysisRule pageAnalysisRule : pageAnalysisRuleArray) {
				if (pageAnalysisRule.getTotalTrue() > 0) {
					crawlAuditRuleId = String.valueOf(pageAnalysisRule.getRuleNbr());
					if (ruleIdCrawlAuditRuleEntityMap.containsKey(crawlAuditRuleId) == true) {
						crawlAuditRuleEntity = ruleIdCrawlAuditRuleEntityMap.get(crawlAuditRuleId);
						if (crawlAuditRuleEntity != null) {
							crawlAuditRuleDesc = crawlAuditRuleEntity.getTitle();
							crawlAuditRuleSeverity = crawlAuditRuleEntity.getSeverity();
							excelAddinsData = new ExcelAddinsData();
							excelAddinsData.setDomain_id(crawlRequestLog.getOwnDomainId());
							excelAddinsData.setProject_name(crawlRequestLog.getProjectName());
							excelAddinsData.setCrawl_request_date(getInternationalDateFormat(crawlRequestLog.getCrawlRequestDate()));
							excelAddinsData.setCrawl_request_id(crawlRequestLog.getId());
							excelAddinsData.setType(IConstants.PAGE_ANALYSIS_ISSUES);
							excelAddinsData.setDescription(crawlAuditRuleDesc);
							excelAddinsData.setImportance(crawlAuditRuleSeverity);
							excelAddinsData.setCount(pageAnalysisRule.getTotalTrue());
							excelAddinsDataList.add(excelAddinsData);
						}
					}
				}
			}
		}
	}

	// input format is 'yyyymmdd'
	// output format is 'yyyy-mm-dd'
	private String getInternationalDateFormat(int inputDate) {
		String testString = String.valueOf(inputDate);
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(StringUtils.substring(testString, 0, 4));
		stringBuilder.append(IConstants.DASH);
		stringBuilder.append(StringUtils.substring(testString, 4, 6));
		stringBuilder.append(IConstants.DASH);
		stringBuilder.append(StringUtils.substring(testString, 6, 8));
		return stringBuilder.toString();
	}

	private Representation generateJsonError(String errorMessage) {
		FormatUtils.getInstance().logMemoryUsage("generateJsonError() errorMessage=" + errorMessage);
		WebServiceError webServiceError = new WebServiceError();
		webServiceError.setError_code("error");
		webServiceError.setError_message(errorMessage);
		return new JacksonRepresentation<WebServiceError>(webServiceError);
	}
}
