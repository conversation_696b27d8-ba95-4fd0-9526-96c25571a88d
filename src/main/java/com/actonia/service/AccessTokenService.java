package com.actonia.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

import com.actonia.IConstants;
import com.actonia.dao.DomainsDAO;
import com.actonia.dao.UsersDAO;
import com.actonia.entity.TDomains;
import com.actonia.entity.TUsers;

public class AccessTokenService {
	// https://www.wrike.com/open.htm?id=546688927
	// public static final String INTERNAL_KEY = "20ac1cb6-15e1-493a-ab43-5eb3b9747f3a";
	// public static final String INTERNAL_KEY = "a10bef62-aed0-b459-6586-3eb31687af79";
	public static final String INTERNAL_KEY = "c09yxv13-opr3-d745-9734-8pu48420nj67";
	public static final String INTERNAL_KEY_FOR_10X = "2f7adda6-054e-e472-3db4-6c62c641807b";
	public static final String INTERNAL_TYK_HEADER = "seoclarity_tky_internal_authorization";

	private UsersDAO usersDAO;

	private DomainsDAO domainsDAO;

	public void setUsersDAO(UsersDAO usersDAO) {
		this.usersDAO = usersDAO;
	}

	public void setDomainsDAO(DomainsDAO domainsDAO) {
		this.domainsDAO = domainsDAO;
	}

	public String checkToken(String accessToken, Map<String, Object> requestAttributes) {
		if (requestAttributes.get("org.restlet.http.headers") != null
				&& StringUtils.containsIgnoreCase(requestAttributes.get("org.restlet.http.headers").toString(), AccessTokenService.INTERNAL_TYK_HEADER)) {
			accessToken = AccessTokenService.INTERNAL_KEY;
		}
		return this.checkToken(accessToken);
	}

	public String checkToken(String accessToken) {
		if (StringUtils.equalsIgnoreCase(accessToken, INTERNAL_KEY) || StringUtils.equalsIgnoreCase(accessToken, INTERNAL_KEY_FOR_10X)) {
			// internal tokens.
			return null;
		}

		TUsers users = usersDAO.checkAuthority(accessToken);
		if (users == null || users.getOwnDomainId() == null || users.getUserId() == null || users.getUserId().intValue() == 0) {
			return IConstants.API000011;
		}

		if (users.getTokensAllowed() == null || users.getTotalUsed() == null || users.getTokensAllowed().intValue() == 0) {
			return IConstants.API000011;
		}

		if (users.getTotalUsed() >= users.getTokensAllowed()) {
			return IConstants.API000012;
		}

		TDomains ownDomainInfo = domainsDAO.domainInfo(users.getOwnDomainId());
		if (ownDomainInfo != null) {
			if (users.getTotalUsed() >= ownDomainInfo.getTokensAllowed()) {
				return IConstants.API000036;
			}

			if (ownDomainInfo.getTotalUsed() >= ownDomainInfo.getTokensAllowed()) {
				return IConstants.API000036;
			}
		}
		usersDAO.updateToken(accessToken);
		domainsDAO.updateToken(users.getOwnDomainId());
		return null;
	}

	public int getDomainIdByAccessToken(String accessToken) {
		TUsers users = usersDAO.checkAuthority(accessToken);

		if (users != null) {
			return users.getOwnDomainId();
		}
		return 0;
	}

	public int getUserIdByAccessToken(String accessToken) {
		TUsers users = usersDAO.checkAuthority(accessToken);

		if (users != null) {
			return users.getUserId();
		}
		return 0;
	}

	public Map<Integer, String> getDomainIdAccessTokenMapByUserId(int userId) {

		// map key = domain ID
		// map value = access token
		Map<Integer, String> domainIdAccessTokenMap = new HashMap<Integer, String>();

		List<TUsers> userList = usersDAO.getAccessTokenDomainIdList(userId);

		if (userList != null && userList.size() > 0) {
			for (TUsers tUsers : userList) {
				domainIdAccessTokenMap.put(tUsers.getOwnDomainId(), tUsers.getAccessToken());
			}
		}

		return domainIdAccessTokenMap;
	}
}
