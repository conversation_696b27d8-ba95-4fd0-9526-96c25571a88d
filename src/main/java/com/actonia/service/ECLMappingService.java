package com.actonia.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

import org.apache.commons.lang.StringUtils;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import com.actonia.IConstants;
import com.actonia.dao.EngineCountryLanguageMappingEntityDAO;
import com.actonia.dao.ScLanguageEntityDAO;
import com.actonia.entity.EngineCountryLanguageMappingEntity;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.entity.ScLanguageEntity;
import com.actonia.entity.ScSearchEngineEntity;
import com.actonia.utils.SpringBeanFactory;

public class ECLMappingService {

	//Cee - https://www.wrike.com/open.htm?id=391372442
	//cache rankcheck DB - language_entity table
	private static final ConcurrentHashMap<Integer, String> PRELOAD_RC_LANGUAGE_ENTITY;

	private static final List<EngineCountryLanguageMappingEntity> PRELOAD_MAPPING_ENTITIES;

	/**
	 * Combined_Key
	 * [countryQueryName,engineQueryName,languageQueryName,rankFrom]
	 * [us,google.com,en,0]
	 */
	private static final ConcurrentHashMap<String, EngineCountryLanguageMappingEntity> PRELOAD_ECL_MAP_BY_UK;

	/**
	 * Combined_Key
	 * [engineId,languageId]
	 * [1,1]
	 */
	private static final ConcurrentHashMap<String, List<EngineCountryLanguageMappingEntity>> PRELOAD_ECL_MAP_BY_ENGINE_AND_LANGUAGE;

	/**
	 * Combined_Key
	 * [engineId] or [engineName]
	 * [1]
	 */
	private static final ConcurrentHashMap<String, List<EngineCountryLanguageMappingEntity>> PRELOAD_ECL_MAP_BY_ENGINE;

	private static EngineCountryLanguageMappingEntityDAO engineCountryLanguageMappingEntityDAO;

	private static ScLanguageEntityDAO scLanguageEntityDAO;

	static {
		PRELOAD_MAPPING_ENTITIES = new ArrayList<EngineCountryLanguageMappingEntity>();
		PRELOAD_ECL_MAP_BY_UK = new ConcurrentHashMap<String, EngineCountryLanguageMappingEntity>();
		PRELOAD_ECL_MAP_BY_ENGINE = new ConcurrentHashMap<String, List<EngineCountryLanguageMappingEntity>>();
		PRELOAD_ECL_MAP_BY_ENGINE_AND_LANGUAGE = new ConcurrentHashMap<String, List<EngineCountryLanguageMappingEntity>>();

		//Cee - https://www.wrike.com/open.htm?id=391372442
		PRELOAD_RC_LANGUAGE_ENTITY = new ConcurrentHashMap<Integer, String>();
		engineCountryLanguageMappingEntityDAO = SpringBeanFactory.getBean("engineCountryLanguageMappingEntityDAO");
		scLanguageEntityDAO = SpringBeanFactory.getBean("scLanguageEntityDAO");
		init();
	}

	public static void init() {
		final List<EngineCountryLanguageMappingEntity> preloadECLMappings = engineCountryLanguageMappingEntityDAO.findAll(true, null);
		PRELOAD_MAPPING_ENTITIES.addAll(preloadECLMappings);

		for (EngineCountryLanguageMappingEntity engineCountryLanguageMapping : PRELOAD_MAPPING_ENTITIES) {
			String countryQueryName = engineCountryLanguageMapping.getCountryQueryName(); // US
			String engineQueryName = engineCountryLanguageMapping.getEngineQueryName(); // google.com, from ownDomain
			String languageQueryName = engineCountryLanguageMapping.getLanguageQueryName(); // en
			Integer rankFrom = engineCountryLanguageMapping.getRankFrom(); // 0: all, 1: special country
			Integer enginId = engineCountryLanguageMapping.getEngineId(); // 1
			Integer languageId = engineCountryLanguageMapping.getLanguageId(); // 1

			// build PRELOAD_ECL_MAP_BY_UK
			String uniqueKey = buildKey(countryQueryName, engineQueryName, languageQueryName, rankFrom);
			PRELOAD_ECL_MAP_BY_UK.put(uniqueKey, engineCountryLanguageMapping);

			// build PRELOAD_ECL_MAP_BY_ENGINE_AND_LANGUAGE
			String engineLanguageKey = buildKey(enginId, languageId);
			List<EngineCountryLanguageMappingEntity> engineLanguageMappings = PRELOAD_ECL_MAP_BY_ENGINE_AND_LANGUAGE.get(engineLanguageKey);
			if (engineLanguageMappings == null) {
				engineLanguageMappings = new ArrayList<EngineCountryLanguageMappingEntity>();
			}
			engineLanguageMappings.add(engineCountryLanguageMapping);
			PRELOAD_ECL_MAP_BY_ENGINE_AND_LANGUAGE.put(engineLanguageKey, engineLanguageMappings);

			// build engine mapping
			// - id mapping
			String engineIdKey = String.valueOf(enginId);
			List<EngineCountryLanguageMappingEntity> engineIdMappings = PRELOAD_ECL_MAP_BY_ENGINE.get(engineIdKey);
			if (engineIdMappings == null) {
				engineIdMappings = new ArrayList<EngineCountryLanguageMappingEntity>();
			}
			engineIdMappings.add(engineCountryLanguageMapping);
			PRELOAD_ECL_MAP_BY_ENGINE.put(engineIdKey, engineIdMappings);
			// - name mapping
			List<EngineCountryLanguageMappingEntity> engineNameMappings = PRELOAD_ECL_MAP_BY_ENGINE.get(engineQueryName);
			if (engineNameMappings == null) {
				engineNameMappings = new ArrayList<EngineCountryLanguageMappingEntity>();
			}
			engineNameMappings.add(engineCountryLanguageMapping);
			PRELOAD_ECL_MAP_BY_ENGINE.put(engineQueryName, engineNameMappings);

		}
		//		testV1AndV2EngineId();
		//		testV1AndV2LanguageId();
		//		testV1AndV2CountryCode();

		//Cee - https://www.wrike.com/open.htm?id=391372442
		List<ScLanguageEntity> languageList = scLanguageEntityDAO.findAll();
		if (languageList != null) {
			for (ScLanguageEntity lang : languageList) {
				PRELOAD_RC_LANGUAGE_ENTITY.put(lang.getId(), lang.getLanguage());
			}
		}
	}

	public static List<EngineCountryLanguageMappingEntity> getECLMappingInfos(int engine, int language) {
		return PRELOAD_ECL_MAP_BY_ENGINE_AND_LANGUAGE.get(buildKey(engine, language));
	}

	public static EngineCountryLanguageMappingEntity getECLMappingInfo(String key) {

		return PRELOAD_ECL_MAP_BY_UK.get(key);
	}

	public static EngineCountryLanguageMappingEntity getECLMappingInfo(OwnDomainEntity ownDomain) {

		Assert.notNull(ownDomain);

		String country = ownDomain.getSearchEngineCountry();
		String engine = ownDomain.getSearchEngine();
		String language = ownDomain.getLanguage();
		Integer rankFrom = ownDomain.getRankFrom();
		return getECLMappingInfo(buildKey(country, engine, language, rankFrom));
	}

//	public static boolean isGoogleEngine(int engineId) {
//		//Cee - https://www.wrike.com/open.htm?id=308695958 - do NOT include 255 (bing.com)
//		if ((engineId > 0 && engineId < 100) || (engineId > 200 && engineId < 255)) { // https://www.wrike.com/open.htm?id=261755221, Please change Google Engine id from (< 100) OR (> 10000) TO (< 100) OR (> 200 && < 255).
//			return true;
//		}
//		return isGoogleEngine(String.valueOf(engineId));
//	}

	// Edwin - https://www.wrike.com/open.htm?id=261755221, engineKey can be string value of engineId or engine query name
	public static boolean isGoogleEngine(String engineKey) {
		List<EngineCountryLanguageMappingEntity> mappings = PRELOAD_ECL_MAP_BY_ENGINE.get(engineKey);
		if (CollectionUtils.isEmpty(mappings)) {
			return false;
		} else {
			boolean isGoogle = false;
			for (EngineCountryLanguageMappingEntity mapping : mappings) {
				if (StringUtils.contains(mapping.getEngineDisplayName(), "google")) {
					// if any engine mapping display name contains "google", then it is Google Engine
					isGoogle = true;
					break;
				}
			}
			return isGoogle;
		}
	}

	private static String buildKey(String countryQueryName, String engineQueryName, String languageQueryName, Integer rankFrom) {
		return StringUtils.join(Arrays.asList(lowerCaseAndTrimString(countryQueryName), lowerCaseAndTrimString(engineQueryName),
				lowerCaseAndTrimString(languageQueryName), rankFrom == null ? 0 : String.valueOf(rankFrom)), IConstants.COMMA);
	}

	private static String buildKey(Integer enginId, Integer languageId) {
		return StringUtils.join(Arrays.asList(enginId == null ? 0 : String.valueOf(enginId), languageId == null ? 0 : String.valueOf(languageId)), IConstants.COMMA);
	}

	public static String lowerCaseAndTrimString(String value) {
		return StringUtils.lowerCase(StringUtils.trimToEmpty(value));

	}

	//Cee - https://www.wrike.com/open.htm?id=391372442
	public static String getLanguageShortName(Integer languageId) {
		if (languageId == null) {
			return "";
		}
		String languageName = PRELOAD_RC_LANGUAGE_ENTITY.get(languageId);
		if (StringUtils.isBlank(languageName)) {
			return "";
		} else {
			return StringUtils.right(languageName, 2);
		}
	}

	//EngineId-LanguageId may return multi-records, their country are same 
	public static String getCountryShortName(int engineId, int languageId) {
		final String default_country = "US";

		if (languageId == ScLanguageEntity.SC_LANGUAGE_US_EN && engineId == ScSearchEngineEntity.GOOGLE_COM) {
			return default_country;
		}

		String engineLanguageKey = buildKey(engineId, languageId);
		List<EngineCountryLanguageMappingEntity> engineLanguageMappings = PRELOAD_ECL_MAP_BY_ENGINE_AND_LANGUAGE.get(engineLanguageKey);
		if (engineLanguageMappings != null && !engineLanguageMappings.isEmpty()) {
			return engineLanguageMappings.get(0).getCountryQueryName();
		}

		return default_country;
	}

	//
	//
	////	- - - - - - Hard code testing section - - - - -
	//
	//	private void testV1AndV2EngineId() {
	//
	//		logger.info("############### testV1AndV2EngineIdL mapping #################");
	//		List<TOwnDomain> ownDomains = ownDomainEntityDAO.getAllOfActiveDomain();
	//
	//		int count = 0;
	//		for (TOwnDomain ownDomain: ownDomains) {
	//			int v1 = ScKeywordRankManager.getSearchEngineIdDeprecated(ownDomain);
	//			int v2 = ScKeywordRankManager.getSearchEngineId(ownDomain);
	//			if (v1 != v2) {
	//				logger.info(">> Found ownDomain " + ownDomain.getId() + " has wrong engine mapping....>>" + ownDomain.getDomain() + "<<");
	//				logger.info(">> V1 >>" + v1);
	//				logger.info(">> V2 >>" + v2);
	//				String country = ownDomain.getSearchEngineCountry();
	//				String engine = ownDomain.getSearchEngine();
	//				String language = ownDomain.getLanguage();
	//				Integer rankFrom = ownDomain.getRankFrom();
	//				logger.info(">> Key >>" + buildKey(country, engine, language, rankFrom));
	//				count++;
	//			}
	//		}
	//		logger.info("Total:" + count);
	//		logger.info("---------------------------------------------------------------");
	//	}
	//
	//	private void testV1AndV2LanguageId() {
	//
	//		logger.info("############### testV1AndV2EngineId mapping #################");
	//		List<TOwnDomain> ownDomains = ownDomainEntityDAO.getAllOfActiveDomain();
	//
	//		int count = 0;
	//		for (TOwnDomain ownDomain: ownDomains) {
	//			int v1 = ScKeywordRankManager.getSearchLanguageIdDeprecated(ownDomain);
	//			int v2 = ScKeywordRankManager.getSearchLanguageId(ownDomain);
	//			if (v1 != v2) {
	//				logger.info(">> Found ownDomain " + ownDomain.getId() + " has wrong language mapping....>>" + ownDomain.getDomain() + "<<");
	//				logger.info(">> V1 >>" + v1);
	//				logger.info(">> V2 >>" + v2);
	//				String country = ownDomain.getSearchEngineCountry();
	//				String engine = ownDomain.getSearchEngine();
	//				String language = ownDomain.getLanguage();
	//				Integer rankFrom = ownDomain.getRankFrom();
	//				logger.info(">> Key >>" + buildKey(country, engine, language, rankFrom));
	//				count++;
	//			}
	//		}
	//		logger.info("Total:" + count);
	//		logger.info("---------------------------------------------------------------");
	//	}
	//
	//	private void testV1AndV2CountryCode() {
	//
	//		logger.info("############### testV1AndV2EngineId mapping #################");
	//		List<TOwnDomain> ownDomains = ownDomainEntityDAO.getAllOfActiveDomain();
	//
	//		int count = 0;
	//		for (TOwnDomain ownDomain: ownDomains) {
	//			int engineId = ScKeywordRankManager.getSearchEngineId(ownDomain);
	//			int languageId = ScKeywordRankManager.getSearchLanguageId(ownDomain);
	//			String v1 = ScKeywordRankManager.getCountryCodeDeprecated(engineId, languageId);
	//			String v2 = ScKeywordRankManager.getCountryCode(engineId, languageId);
	//			if (!StringUtils.equals(v1,v2)) {
	//				logger.info(">> Found ownDomain " + ownDomain.getId() + " has wrong countryCode mapping....>>" + ownDomain.getDomain() + "<<");
	//				logger.info(">> V1 >>" + v1);
	//				logger.info(">> V2 >>" + v2);
	//				logger.info(">> Key >>" + buildKey(engineId, languageId));
	//				count++;
	//			}
	//		}
	//		logger.info("Total:" + count);
	//		logger.info("---------------------------------------------------------------");
	//	}
}
