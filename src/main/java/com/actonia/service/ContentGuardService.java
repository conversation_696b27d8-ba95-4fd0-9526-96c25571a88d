package com.actonia.service;

import com.actonia.IConstants;
import com.actonia.dao.*;
import com.actonia.entity.ContentGuardGroupEntity;
import com.actonia.entity.ContentGuardUsageEntity;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.utils.ContentGuardUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.Md5Util;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.*;
import com.actonia.web.service.ContentGuardTrackedGroup;
import com.actonia.web.service.ContentGuardTrackedPage;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.gson.Gson;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.util.StopWatch;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

public class ContentGuardService {

	//private boolean isDebug = false;
	private final ContentGuardUrlDAO contentGuardUrlDAO;
	private final ContentGuardGroupDAO contentGuardGroupDAO;
	private final ContentGuardUsageDAO contentGuardUsageDAO;
	private final ContentGuardAlertDAO contentGuardAlertDAO;

	/**
	 * cache for md5HashContentGuardUrlMap, key = {domainId}:{groupId}
	 */
	private final Cache<String, Map<String, String>> md5HashContentGuardUrlMapCache = CacheBuilder.newBuilder()
			.expireAfterAccess(Duration.ofMinutes(2))
			.expireAfterWrite(Duration.ofMinutes(5))
			.build();

	public ContentGuardService() {
		super();
		this.contentGuardUrlDAO = SpringBeanFactory.getBean("contentGuardUrlDAO");
		this.contentGuardGroupDAO = SpringBeanFactory.getBean("contentGuardGroupDAO");
		this.contentGuardUsageDAO = SpringBeanFactory.getBean("contentGuardUsageDAO");
		this.contentGuardAlertDAO = SpringBeanFactory.getBean("contentGuardAlertDAO");
	}

	public ContentGuardResourceResponse getTimeline(int domainId, Long groupId, String startCrawlDate, String endCrawlDate, Boolean useCustomIndicators) throws Exception {
		FormatUtils.getInstance().logMemoryUsage(
				"getTimeline() begins. domainId=" + domainId + ",groupId=" + groupId + ",startCrawlDate=" + startCrawlDate + ",endCrawlDate=" + endCrawlDate);
		long startTimestamp = System.currentTimeMillis();

		// map key = URL MD5 hash
		// map value = Content Guard URL string
		Map<String, String> md5HashContentGuardUrlMap = md5HashContentGuardUrlMapCache.get(domainId + "::" + groupId, () -> contentGuardUrlDAO.getMd5HashUrlMap(domainId, groupId));

		ContentGuardResourceResponse response = new ContentGuardResourceResponse();
		response.setGroup_id(groupId);
		response.setSuccess(true);
		response.setTotal_pages_tracked(md5HashContentGuardUrlMap.size());

		List<String> changeTrackingIndicatorList = ContentGuardUtils.getInstance().getChangeTrackingIndicatorList();
		if (changeTrackingIndicatorList != null && changeTrackingIndicatorList.size() > 0) {
			if (BooleanUtils.isTrue(useCustomIndicators)) {
				final List<String> customTrackingIndicatorList = getCustomTrackingIndicatorList(domainId, groupId);
				if (!customTrackingIndicatorList.isEmpty()) {
					changeTrackingIndicatorList = customTrackingIndicatorList;
				}
			}
			String startCrawlHour = IConstants.CRAWL_HOUR_00;
			String endCrawlHour = IConstants.CRAWL_HOUR_23;
			String startCrawlTimestamp = startCrawlDate + IConstants.ONE_SPACE + startCrawlHour + IConstants.CRAWL_TIMESTAMP_START_OF_HOUR;
			response.setStart_crawl_timestamp(startCrawlTimestamp);
			String endCrawlTimestamp = endCrawlDate + IConstants.ONE_SPACE + endCrawlHour + IConstants.CRAWL_TIMESTAMP_END_OF_HOUR;
			response.setEnd_crawl_timestamp(endCrawlTimestamp);
			List<String> finalChangeTrackingIndicatorList = changeTrackingIndicatorList;
			changeTrackingIndicatorList.remove(IConstants.RESPONSE_CODE_404_REMOVED_IND);
			changeTrackingIndicatorList.remove(IConstants.RESPONSE_CODE_404_DETECTED_IND);
			List<HtmlClickHouseEntity> htmlClickHouseEntityList = ContentGuardClickHouseDAO.getInstance().getChangeTrackingSummaryList(domainId, startCrawlTimestamp, endCrawlTimestamp, finalChangeTrackingIndicatorList);
			if (htmlClickHouseEntityList != null && htmlClickHouseEntityList.size() > 0) {
				htmlClickHouseEntityList = htmlClickHouseEntityList.stream()
						.filter(htmlClickHouseEntityInput -> md5HashContentGuardUrlMap.containsKey(Md5Util.Md5(htmlClickHouseEntityInput.getUrl())))
						.collect(Collectors.toList());
				ContentGuardUtils.getInstance().processTimeline(domainId, groupId, response, htmlClickHouseEntityList);
				response.setElapsed_millisecond((System.currentTimeMillis() - startTimestamp));
			} else {
				FormatUtils.getInstance().logMemoryUsage("getTimeline() htmlClickHouseEntityList is empty.");
			}
		} else {
			FormatUtils.getInstance().logMemoryUsage("getTimeline() error--changeTrackingIndicatorList is empty.");
		}
		FormatUtils.getInstance().logMemoryUsage("getTimeline() ends. domainId=" + domainId + ",startCrawlDate=" + startCrawlDate + ",endCrawlDate=" + endCrawlDate
				+ ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return response;
	}

	private List<String> getCustomTrackingIndicatorList(int domainId, Long groupId) {
		final String customIndicators = this.contentGuardAlertDAO.getCustomIndicatorsByDomainIdAndGroupId(domainId, groupId);
		if (customIndicators != null && customIndicators.length() > 2) {
			// get custom indicator list from string array
			final String[] customIndicatorArray = new Gson().fromJson(customIndicators, String[].class);
			return Arrays.asList(customIndicatorArray);
		}
		return Collections.emptyList();
	}

	public ContentGuardResourceResponse getDomainSummary(int domainId, Long groupId, String crawlDate, Integer crawlHour, int pageNumber, int rowsPerPage, int sortBy,
														 Boolean returnDetails, String filterUrl, Boolean useCustomIndicators) throws Exception {
		FormatUtils.getInstance()
				.logMemoryUsage("getDomainSummary() begins. domainId=" + domainId + ",groupId=" + groupId + ",crawlDate=" + crawlDate + ",crawlHour=" + crawlHour
						+ ",pageNumber=" + pageNumber + ",rowsPerPage=" + rowsPerPage + ",sortBy=" + sortBy + ",returnDetails=" + returnDetails + ",filterUrl="
						+ filterUrl);
		long startTimestamp = System.currentTimeMillis();
		final StopWatch stopWatch = new StopWatch("domainSummary:" + domainId + "::" + groupId + "::" + crawlDate + "::" + crawlHour);

		stopWatch.start("getMd5HashUrlMap()");
		// map key = URL MD5 hash 
		// map value = Content Guard URL string
		Map<String, String> md5HashContentGuardUrlMap = md5HashContentGuardUrlMapCache.get(domainId + "::" + groupId, () -> contentGuardUrlDAO.getMd5HashUrlMap(domainId, groupId));
		stopWatch.stop();

		ContentGuardResourceResponse response = instantiateResponse(md5HashContentGuardUrlMap.size());
		response.setSuccess(true);
		response.setGroup_id(groupId);

		List<HtmlClickHouseEntity> testHtmlClickHouseEntityList = null;
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = null;
		String startCrawlHour = null;
		String endCrawlHour = null;
		String startCrawlTimestamp;
		String endCrawlTimestamp;

		stopWatch.start("getChangeTrackingSummaryList()");
		List<String> changeTrackingIndicatorList = ContentGuardUtils.getInstance().getChangeTrackingIndicatorList();
		changeTrackingIndicatorList.remove(IConstants.RESPONSE_CODE_404_DETECTED_IND);
		changeTrackingIndicatorList.remove(IConstants.RESPONSE_CODE_404_REMOVED_IND);
		stopWatch.stop();
		if (changeTrackingIndicatorList != null && !changeTrackingIndicatorList.isEmpty()) {
			if (BooleanUtils.isTrue(useCustomIndicators)) {
				stopWatch.start("getCustomTrackingIndicatorList()");
				final List<String> customTrackingIndicatorList = getCustomTrackingIndicatorList(domainId, groupId);
				stopWatch.stop();
                if (!customTrackingIndicatorList.isEmpty()) {
	                changeTrackingIndicatorList = customTrackingIndicatorList;
                }
            }

			// when request does not specify crawl date and time
			if (crawlDate == null && crawlHour == null) {
				endCrawlTimestamp = null;
				startCrawlTimestamp = null;
				List<String> finalChangeTrackingIndicatorList1 = changeTrackingIndicatorList;
				stopWatch.start("getChangeTrackingSummaryList() without crawl date and time");
				testHtmlClickHouseEntityList = ContentGuardClickHouseDAO.getInstance().getChangeTrackingSummaryList(domainId, finalChangeTrackingIndicatorList1);
				stopWatch.stop();
				htmlClickHouseEntityList = filterHtmlClickHouseEntityListByGroupUrls(testHtmlClickHouseEntityList, md5HashContentGuardUrlMap);
			}
			// when request specifies crawl date and time
			else {
				// calculate start crawl timestamp, and end crawl timestamp
				if (crawlHour != null) {
					startCrawlHour = String.format(IConstants.STRING_WITH_ONE_LEADING_ZERO, crawlHour);
					endCrawlHour = String.format(IConstants.STRING_WITH_ONE_LEADING_ZERO, crawlHour);
				} else {
					startCrawlHour = IConstants.CRAWL_HOUR_00;
					endCrawlHour = IConstants.CRAWL_HOUR_23;
				}
				startCrawlTimestamp = crawlDate + IConstants.ONE_SPACE + startCrawlHour + IConstants.CRAWL_TIMESTAMP_START_OF_HOUR;
				response.setStart_crawl_timestamp(startCrawlTimestamp);
				endCrawlTimestamp = crawlDate + IConstants.ONE_SPACE + endCrawlHour + IConstants.CRAWL_TIMESTAMP_END_OF_HOUR;
				response.setEnd_crawl_timestamp(endCrawlTimestamp);
				List<String> finalChangeTrackingIndicatorList = changeTrackingIndicatorList;

				stopWatch.start("getChangeTrackingSummaryList()" + startCrawlTimestamp + " to " + endCrawlTimestamp);
				testHtmlClickHouseEntityList = ContentGuardClickHouseDAO.getInstance().getChangeTrackingSummaryList(domainId, startCrawlTimestamp, endCrawlTimestamp, finalChangeTrackingIndicatorList);
				stopWatch.stop();
				htmlClickHouseEntityList = filterHtmlClickHouseEntityListByGroupUrls(testHtmlClickHouseEntityList, md5HashContentGuardUrlMap);
			}
			stopWatch.start("processChangeTrackingSummary()");
			ContentGuardUtils.getInstance().processChangeTrackingSummary(domainId, groupId, response, htmlClickHouseEntityList, md5HashContentGuardUrlMap, pageNumber,
					rowsPerPage, sortBy, returnDetails, filterUrl);
			stopWatch.stop();
			response.setElapsed_millisecond((System.currentTimeMillis() - startTimestamp));
		} else {
			FormatUtils.getInstance().logMemoryUsage("getDomainSummary() error--changeTrackingIndicatorList is empty.");
		}

		FormatUtils.getInstance()
				.logMemoryUsage("getDomainSummary() ends. domainId=" + domainId + ",crawlDate=" + crawlDate + ",crawlHour=" + crawlHour + ",groupId=" + groupId
						+ ",pageNumber=" + pageNumber + ",rowsPerPage=" + rowsPerPage + ",sortBy=" + sortBy + ",totalPagesTracked=" + response.getTotal_pages_tracked()
						+ ",totalPagesChanged=" + response.getTotal_pages_changed() + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp) + "\nstopWatch=" + stopWatch.prettyPrint());
		return response;
	}

	private List<HtmlClickHouseEntity> filterHtmlClickHouseEntityListByGroupUrls(List<HtmlClickHouseEntity> htmlClickHouseEntityInputList,
																				 Map<String, String> md5HashContentGuardUrlMap) {
		List<HtmlClickHouseEntity> htmlClickHouseEntityOutputList = new ArrayList<HtmlClickHouseEntity>();
		String md5Hash = null;
		for (HtmlClickHouseEntity htmlClickHouseEntityInput : htmlClickHouseEntityInputList) {
			md5Hash = Md5Util.Md5(htmlClickHouseEntityInput.getUrl());
			if (md5HashContentGuardUrlMap.containsKey(md5Hash)) {
				htmlClickHouseEntityOutputList.add(htmlClickHouseEntityInput);
			}
		}
		return htmlClickHouseEntityOutputList;
	}

	private ContentGuardResourceResponse instantiateResponse(int totalPagesTracked) {
		ContentGuardResourceResponse response = new ContentGuardResourceResponse();
		response.setTotal_pages_tracked(totalPagesTracked);
		response.setTotal_pages_changed(0);
		return response;
	}

	public ContentGuardResourceResponse getUrlDetails(int domainId, Long groupId, String urlString, String crawlTimestamp) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage(
				"getUrlDetails() begins. domainId=" + domainId + ",groupId=" + groupId + ",urlString=" + urlString + ",crawlTimestamp=" + crawlTimestamp);

		HtmlClickHouseEntity htmlClickHouseEntityIndicators = null;
		WebServiceError webServiceError = null;

		ContentGuardResourceResponse response = null;

		List<String> changeTrackingIndicatorList = ContentGuardUtils.getInstance().getChangeTrackingIndicatorList();
		if (changeTrackingIndicatorList != null && changeTrackingIndicatorList.size() > 0) {
			htmlClickHouseEntityIndicators = ContentGuardClickHouseDAO.getInstance().getChangeTrackingIndicators(domainId, urlString, crawlTimestamp,
					changeTrackingIndicatorList);
			if (htmlClickHouseEntityIndicators != null) {
				response = ContentGuardUtils.getInstance().getChangeTrackingDetails(domainId, groupId, htmlClickHouseEntityIndicators);
			} else {
				response = new ContentGuardResourceResponse();
				response.setSuccess(false);
				webServiceError = new WebServiceError();
				webServiceError.setError_code(IConstants.MSG_CD_CONTENT_GUARD_WEB_SERVICE_METHOD_EXCEPTION);
				webServiceError.setError_message("Crawl data not available for domain ID " + domainId + ", URL " + urlString + ", crawl timestamp " + crawlTimestamp);
				response.setError(webServiceError);
			}
			response.setElapsed_millisecond((System.currentTimeMillis() - startTimestamp));
		} else {
			FormatUtils.getInstance().logMemoryUsage("getUrlDetails() changeTrackingIndicatorList is empty.");
		}
		return response;
	}

	public ContentGuardResourceResponse getIndicatorUrlList(int domainId, Long groupId, String crawlDate, Integer crawlHour, int pageNumber, int rowsPerPage,
															Boolean returnDetails, String filterChangeIndicator) throws Exception {
		FormatUtils.getInstance()
				.logMemoryUsage("getIndicatorUrlList() begins. domainId=" + domainId + ",groupId=" + groupId + ",crawlDate=" + crawlDate + ",crawlHour=" + crawlHour
						+ ",pageNumber=" + pageNumber + ",rowsPerPage=" + rowsPerPage + ",returnDetails=" + returnDetails + ",filterChangeIndicator="
						+ filterChangeIndicator);
		long startTimestamp = System.currentTimeMillis();

		// map key = URL MD5 hash 
		// map value = Content Guard URL string
		Map<String, String> md5HashContentGuardUrlMap = contentGuardUrlDAO.getMd5HashUrlMap(domainId, groupId);

		ContentGuardResourceResponse response = instantiateResponse(md5HashContentGuardUrlMap.size());
		response.setSuccess(true);
		response.setGroup_id(groupId);

		List<HtmlClickHouseEntity> testHtmlClickHouseEntityList = null;
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = null;
		String startCrawlHour = null;
		String endCrawlHour = null;
		String startCrawlTimestamp = null;
		String endCrawlTimestamp = null;

		List<String> changeTrackingIndicatorList = ContentGuardUtils.getInstance().getChangeTrackingIndicatorList();
		if (changeTrackingIndicatorList != null && changeTrackingIndicatorList.size() > 0) {

			// when request does not specify crawl date and time
			if (crawlDate == null && crawlHour == null) {
				testHtmlClickHouseEntityList = ContentGuardClickHouseDAO.getInstance().getChangeTrackingSummaryList(domainId, changeTrackingIndicatorList);
				htmlClickHouseEntityList = filterHtmlClickHouseEntityListByGroupUrls(testHtmlClickHouseEntityList, md5HashContentGuardUrlMap);
			}
			// when request specifies crawl date and time
			else {
				// calculate start crawl timestamp, and end crawl timestamp
				if (crawlHour != null) {
					startCrawlHour = String.format(IConstants.STRING_WITH_ONE_LEADING_ZERO, crawlHour);
					endCrawlHour = String.format(IConstants.STRING_WITH_ONE_LEADING_ZERO, crawlHour);
				} else {
					startCrawlHour = IConstants.CRAWL_HOUR_00;
					endCrawlHour = IConstants.CRAWL_HOUR_23;
				}
				startCrawlTimestamp = crawlDate + IConstants.ONE_SPACE + startCrawlHour + IConstants.CRAWL_TIMESTAMP_START_OF_HOUR;
				response.setStart_crawl_timestamp(startCrawlTimestamp);
				endCrawlTimestamp = crawlDate + IConstants.ONE_SPACE + endCrawlHour + IConstants.CRAWL_TIMESTAMP_END_OF_HOUR;
				response.setEnd_crawl_timestamp(endCrawlTimestamp);
				testHtmlClickHouseEntityList = ContentGuardClickHouseDAO.getInstance().getChangeTrackingSummaryList(domainId, startCrawlTimestamp, endCrawlTimestamp,
						changeTrackingIndicatorList);
				htmlClickHouseEntityList = filterHtmlClickHouseEntityListByGroupUrls(testHtmlClickHouseEntityList, md5HashContentGuardUrlMap);

			}
			if (htmlClickHouseEntityList != null && htmlClickHouseEntityList.size() > 0) {
				ContentGuardUtils.getInstance().processIndicatorUrlList(domainId, groupId, response, htmlClickHouseEntityList, md5HashContentGuardUrlMap, pageNumber,
						rowsPerPage, returnDetails, filterChangeIndicator);
				response.setElapsed_millisecond((System.currentTimeMillis() - startTimestamp));
			} else {
				FormatUtils.getInstance().logMemoryUsage("getIndicatorUrlList() testHtmlClickHouseEntityList is empty.");
			}
		} else {
			FormatUtils.getInstance().logMemoryUsage("getIndicatorUrlList() error--changeTrackingIndicatorList is empty.");
		}
		FormatUtils.getInstance()
				.logMemoryUsage("getIndicatorUrlList() ends. domainId=" + domainId + ",crawlDate=" + crawlDate + ",crawlHour=" + crawlHour + ",groupId=" + groupId
						+ ",pageNumber=" + pageNumber + ",rowsPerPage=" + rowsPerPage + ",totalPagesTracked=" + response.getTotal_pages_tracked()
						+ ",totalPagesChanged=" + response.getTotal_pages_changed() + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return response;
	}

	public ContentGuardResourceResponse getUrlAllDetails(int domainId, Long groupId, String urlString, int pageNumber, int rowsPerPage) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("getUrlAllDetails() begins. domainId=" + domainId + ",groupId=" + groupId + ",urlString=" + urlString + ",pageNumber="
				+ pageNumber + ",rowsPerPage=" + rowsPerPage);
		long startTimestamp = System.currentTimeMillis();

		ContentGuardResourceResponse response = new ContentGuardResourceResponse();
		response.setSuccess(true);
		response.setGroup_id(groupId);
		response.setUrl(urlString);
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = null;
		List<String> crawlTimestampList = null;
		WebServiceError webServiceError = null;

		List<String> changeTrackingIndicatorList = ContentGuardUtils.getInstance().getChangeTrackingIndicatorList();
		if (changeTrackingIndicatorList != null && changeTrackingIndicatorList.size() > 0) {
			htmlClickHouseEntityList = ContentGuardClickHouseDAO.getInstance().getCrawlTimestampHistory(domainId, urlString, changeTrackingIndicatorList);
			if (htmlClickHouseEntityList != null && htmlClickHouseEntityList.size() > 0) {
				crawlTimestampList = new ArrayList<String>();
				for (int i = 0; i < htmlClickHouseEntityList.size(); i++) {
					crawlTimestampList.add(DateFormatUtils.format(htmlClickHouseEntityList.get(i).getCrawlTimestamp(), IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS));
				}
				response.setTotal_crawl_timestamps(crawlTimestampList.size());
				processUrlAllDetailsPagination(domainId, groupId, urlString, response, crawlTimestampList, pageNumber, rowsPerPage);
			} else {
				response.setSuccess(false);
				webServiceError = new WebServiceError();
				webServiceError.setError_code(IConstants.MSG_CD_CONTENT_GUARD_CRAWL_TIMESTAMP_HISTORY_NOT_AVAILABLE);
				webServiceError.setError_message(ContentGuardUtils.getInstance().getErrorMessage(webServiceError.getError_code(), urlString));
				response.setError(webServiceError);
			}
		} else {
			FormatUtils.getInstance().logMemoryUsage("getUrlAllDetails() error--changeTrackingIndicatorList is empty.");
		}
		FormatUtils.getInstance().logMemoryUsage("getUrlAllDetails() ends. domainId=" + domainId + ",groupId=" + groupId + ",urlString=" + urlString + ",pageNumber="
				+ pageNumber + ",rowsPerPage=" + rowsPerPage + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return response;
	}

	// update 'response' using pass by reference
	private void processUrlAllDetailsPagination(int domainId, Long groupId, String urlString, ContentGuardResourceResponse response,
												List<String> crawlTimestampInputList, int pageNumber, int rowsPerPage) throws Exception {
		//FormatUtils.getInstance().logMemoryUsage("processUrlAllDetailsPagination() begins. domainId=" + domainId + ",groupId=" + groupId + ",urlString=" + urlString
		//		+ ",crawlTimestampInputList.size()=" + crawlTimestampInputList.size() + ",pageNumber=" + pageNumber + ",rowsPerPage" + rowsPerPage);
		ContentGuardUrlCrawlHistory[] contentGuardUrlCrawlHistoryArray = null;
		ContentGuardUrlCrawlHistory contentGuardUrlCrawlHistory = null;
		List<String> crawlTimestampOutputList = null;
		WebServiceError webServiceError = null;
		String crawlTimestamp = null;
		ContentGuardResourceResponse urlDetailsResponse = null;
		int fromIndex = (pageNumber - 1) * rowsPerPage;
		int toIndex = pageNumber * rowsPerPage;
		//FormatUtils.getInstance().logMemoryUsage("processUrlAllDetailsPagination() fromIndex=" + fromIndex + ",toIndex=" + toIndex);
		if (fromIndex <= (crawlTimestampInputList.size() - 1)) {
			if (toIndex > crawlTimestampInputList.size()) {
				toIndex = crawlTimestampInputList.size();
				//FormatUtils.getInstance().logMemoryUsage("processUrlAllDetailsPagination() revised toIndex=" + toIndex);
			}
			crawlTimestampOutputList = crawlTimestampInputList.subList(fromIndex, toIndex);
			contentGuardUrlCrawlHistoryArray = new ContentGuardUrlCrawlHistory[crawlTimestampOutputList.size()];
			for (int i = 0; i < crawlTimestampOutputList.size(); i++) {
				crawlTimestamp = crawlTimestampOutputList.get(i);
				urlDetailsResponse = getUrlDetails(domainId, groupId, urlString, crawlTimestamp);
				contentGuardUrlCrawlHistory = new ContentGuardUrlCrawlHistory();
				contentGuardUrlCrawlHistory.setPrevious_crawl_timestamp(urlDetailsResponse.getPrevious_crawl_timestamp());
				contentGuardUrlCrawlHistory.setCurrent_crawl_timestamp(urlDetailsResponse.getCurrent_crawl_timestamp());
				contentGuardUrlCrawlHistory.setUrl_change_details_List(urlDetailsResponse.getUrl_change_details_list());
				contentGuardUrlCrawlHistoryArray[i] = contentGuardUrlCrawlHistory;
			}
			response.setUrl_crawl_history(contentGuardUrlCrawlHistoryArray);
			if (toIndex == crawlTimestampInputList.size()) {
				response.setEnd_of_url_crawl_history_flag(true);
			} else {
				response.setEnd_of_url_crawl_history_flag(false);
			}

		} else {
			response.setSuccess(false);
			webServiceError = new WebServiceError();
			webServiceError.setError_code(IConstants.MSG_CD_CONTENT_GUARD_REQUEST_PARM_OUTSIDE_DATA_RANGE);
			webServiceError
					.setError_message(ContentGuardUtils.getInstance().getErrorMessage(webServiceError.getError_code(), String.valueOf(crawlTimestampInputList.size())));
			response.setError(webServiceError);
		}
		response.setPage_number(pageNumber);
		response.setRows_per_page(rowsPerPage);
		//FormatUtils.getInstance()
		//		.logMemoryUsage("processUrlAllDetailsPagination() ends. contentGuardIndicatorUrlChangesInputList.size()=" + contentGuardIndicatorUrlChangesInputList.size() + ",pageNumber="
		//				+ pageNumber + ",rowsPerPage" + rowsPerPage + ",sortBy=" + sortBy + ",contentGuardIndicatorUrlChangesOutputList.size()="
		//				+ contentGuardIndicatorUrlChangesOutputList.size());
	}

	public ContentGuardResourceResponse getPageAnalysisIssues(int domainId, Long groupId, String crawlDate, Integer crawlHour) throws Exception {
		FormatUtils.getInstance()
				.logMemoryUsage("getPageAnalysisIssues() begins. domainId=" + domainId + ",groupId=" + groupId + ",crawlDate=" + crawlDate + ",crawlHour=" + crawlHour);
		long startTimestamp = System.currentTimeMillis();

		// map key = URL MD5 hash 
		// map value = Content Guard URL string
		Map<String, String> md5HashContentGuardUrlMap = contentGuardUrlDAO.getMd5HashUrlMap(domainId, groupId);

		ContentGuardResourceResponse response = new ContentGuardResourceResponse();
		response.setSuccess(true);
		response.setGroup_id(groupId);

		List<HtmlClickHouseEntity> testHtmlClickHouseEntityList = null;
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = null;
		String startCrawlHour = null;
		String endCrawlHour = null;
		String startCrawlTimestamp = null;
		String endCrawlTimestamp = null;
		PageAnalysisResult[] pageAnalysisResultArray = null;
		int totalPageAnalysisIssues = 0;
		List<UrlPageAnalysisResults> urlPageAnalysisResultsList = null;
		UrlPageAnalysisResults urlPageAnalysisResults = null;
		List<Integer> ruleList = null;

		// when request does not specify crawl date and time
		if (crawlDate == null && crawlHour == null) {
			testHtmlClickHouseEntityList = ContentGuardClickHouseDAO.getInstance().getPageAnalysisResultsList(domainId);
			htmlClickHouseEntityList = filterHtmlClickHouseEntityListByGroupUrls(testHtmlClickHouseEntityList, md5HashContentGuardUrlMap);
		}
		// when request specifies crawl date and time
		else {
			// calculate start crawl timestamp, and end crawl timestamp
			if (crawlHour != null) {
				startCrawlHour = String.format(IConstants.STRING_WITH_ONE_LEADING_ZERO, crawlHour);
				endCrawlHour = String.format(IConstants.STRING_WITH_ONE_LEADING_ZERO, crawlHour);
			} else {
				startCrawlHour = IConstants.CRAWL_HOUR_00;
				endCrawlHour = IConstants.CRAWL_HOUR_23;
			}
			startCrawlTimestamp = crawlDate + IConstants.ONE_SPACE + startCrawlHour + IConstants.CRAWL_TIMESTAMP_START_OF_HOUR;
			response.setStart_crawl_timestamp(startCrawlTimestamp);
			endCrawlTimestamp = crawlDate + IConstants.ONE_SPACE + endCrawlHour + IConstants.CRAWL_TIMESTAMP_END_OF_HOUR;
			response.setEnd_crawl_timestamp(endCrawlTimestamp);
			testHtmlClickHouseEntityList = ContentGuardClickHouseDAO.getInstance().getPageAnalysisResultsList(domainId, startCrawlTimestamp, endCrawlTimestamp);
			htmlClickHouseEntityList = filterHtmlClickHouseEntityListByGroupUrls(testHtmlClickHouseEntityList, md5HashContentGuardUrlMap);
		}
		if (htmlClickHouseEntityList != null && htmlClickHouseEntityList.size() > 0) {
			if (htmlClickHouseEntityList != null && htmlClickHouseEntityList.size() > 0) {
				urlPageAnalysisResultsList = new ArrayList<UrlPageAnalysisResults>();
				for (HtmlClickHouseEntity htmlClickHouseEntity : htmlClickHouseEntityList) {
					urlPageAnalysisResults = new UrlPageAnalysisResults();
					urlPageAnalysisResults.setUrl(htmlClickHouseEntity.getUrl());
					urlPageAnalysisResults
							.setCrawl_timestamp(DateFormatUtils.format(htmlClickHouseEntity.getCrawlTimestamp(), IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS));
					//FormatUtils.getInstance().logMemoryUsage("getPageAnalysisIssues() urlPageAnalysisResults.getUrl()=" + urlPageAnalysisResults.getUrl());
					pageAnalysisResultArray = htmlClickHouseEntity.getPageAnalysisResultArray();
					if (pageAnalysisResultArray != null && pageAnalysisResultArray.length > 0) {
						ruleList = new ArrayList<Integer>();
						for (PageAnalysisResult pageAnalysisResult : pageAnalysisResultArray) {
							if (pageAnalysisResult.getResult() == 1) {
								//FormatUtils.getInstance().logMemoryUsage(
								//		"getPageAnalysisIssues() rule number=" + pageAnalysisResult.getRule() + ",rule result=" + pageAnalysisResult.getResult());
								ruleList.add(pageAnalysisResult.getRule());
								totalPageAnalysisIssues++;
							}
						}
						urlPageAnalysisResults.setRule_array(ruleList.toArray(new Integer[0]));
					}
					urlPageAnalysisResultsList.add(urlPageAnalysisResults);
				}
				if (urlPageAnalysisResultsList != null && urlPageAnalysisResultsList.size() > 0) {
					response.setUrl_page_analysis_results_list(urlPageAnalysisResultsList.toArray(new UrlPageAnalysisResults[0]));
				}
			}
		} else {
			FormatUtils.getInstance().logMemoryUsage("getPageAnalysisIssues() testHtmlClickHouseEntityList is empty.");
		}
		response.setTotal_page_analysis_issues(totalPageAnalysisIssues);
		response.setElapsed_millisecond((System.currentTimeMillis() - startTimestamp));
		FormatUtils.getInstance().logMemoryUsage("getPageAnalysisIssues() ends. domainId=" + domainId + ",groupId=" + groupId + ",crawlDate=" + crawlDate
				+ ",crawlHour=" + crawlHour + ",totalPageAnalysisIssues=" + totalPageAnalysisIssues + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return response;
	}

	public ContentGuardResourceResponse getTrackedPages(int domainId, Long groupId, int pageNumber, int rowsPerPage, int sortBy, String filterUrl) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("getTrackedPages() begins. domainId=" + domainId + ",groupId=" + groupId + ",pageNumber=" + pageNumber
				+ ",rowsPerPage=" + rowsPerPage + ",sortBy=" + sortBy + ",filterUrl=" + filterUrl);
		long startTimestamp = System.currentTimeMillis();

		ContentGuardResourceResponse response = new ContentGuardResourceResponse();
		response.setSuccess(true);

		ContentGuardTrackedGroup contentGuardTrackedGroup = null;
		ContentGuardTrackedPage contentGuardTrackedPage = null;
		List<ContentGuardGroupEntity> contentGuardGroupEntityList = null;
		HtmlClickHouseEntity htmlClickHouseEntity = null;
		String urlHashCode = null;
		List<ContentGuardTrackedPage> contentGuardTrackedPageList = null;
		List<ContentGuardTrackedGroup> contentGuardTrackedGroupList = null;

		// map key = URL MD5 hash code
		// map value = URL's latest HtmlClickHouseEntity
		Map<String, HtmlClickHouseEntity> hashCodeLatestHtmlClickHouseEntityMap = null;

		// map key = group ID
		// map value = ContentGuardTrackedGroup
		Map<Long, ContentGuardTrackedGroup> groupIdContentGuardTrackedGroupMap = new HashMap<Long, ContentGuardTrackedGroup>();

		// map key = group ID
		// map value = list of ContentGuardTrackedPage
		Map<Long, List<ContentGuardTrackedPage>> groupIdContentGuardTrackedPageListMap = new HashMap<Long, List<ContentGuardTrackedPage>>();

		contentGuardGroupEntityList = contentGuardGroupDAO.getTrackedGroupUrlList(domainId, groupId, filterUrl);
		if (contentGuardGroupEntityList != null && contentGuardGroupEntityList.size() > 0) {
			hashCodeLatestHtmlClickHouseEntityMap = ContentGuardUtils.getInstance().retrieveLatestCrawlTimestampResponseCode(domainId);
			for (ContentGuardGroupEntity contentGuardGroupEntity : contentGuardGroupEntityList) {
				if (groupIdContentGuardTrackedGroupMap.containsKey(contentGuardGroupEntity.getId()) == false) {
					contentGuardTrackedGroup = new ContentGuardTrackedGroup();
					contentGuardTrackedGroup.setGroup_id(contentGuardGroupEntity.getId());
					contentGuardTrackedGroup.setGroup_name(contentGuardGroupEntity.getGroupName());
					contentGuardTrackedGroup.setCrawl_frequency_type(contentGuardGroupEntity.getCrawlFrequencyType());
					groupIdContentGuardTrackedGroupMap.put(contentGuardGroupEntity.getId(), contentGuardTrackedGroup);
				}
				contentGuardTrackedPage = new ContentGuardTrackedPage();
				contentGuardTrackedPage.setUrl(contentGuardGroupEntity.getUrl());
				urlHashCode = Md5Util.Md5(contentGuardGroupEntity.getUrl());
				if (hashCodeLatestHtmlClickHouseEntityMap.containsKey(urlHashCode)) {
					htmlClickHouseEntity = hashCodeLatestHtmlClickHouseEntityMap.get(urlHashCode);
					contentGuardTrackedPage.setResponse_code(htmlClickHouseEntity.getCrawlerResponse().getResponse_code());
					contentGuardTrackedPage
							.setLast_update_timestamp(DateFormatUtils.format(htmlClickHouseEntity.getCrawlTimestamp(), IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS));
					if (groupIdContentGuardTrackedPageListMap.containsKey(contentGuardGroupEntity.getId()) == true) {
						contentGuardTrackedPageList = groupIdContentGuardTrackedPageListMap.get(contentGuardGroupEntity.getId());
					} else {
						contentGuardTrackedPageList = new ArrayList<ContentGuardTrackedPage>();
					}
				} else {
					contentGuardTrackedPageList = new ArrayList<ContentGuardTrackedPage>();
				}
				contentGuardTrackedPageList.add(contentGuardTrackedPage);
				groupIdContentGuardTrackedPageListMap.put(contentGuardGroupEntity.getId(), contentGuardTrackedPageList);
			}
			contentGuardTrackedGroupList = new ArrayList<ContentGuardTrackedGroup>();
			for (Long testGroupId : groupIdContentGuardTrackedGroupMap.keySet()) {
				contentGuardTrackedGroup = groupIdContentGuardTrackedGroupMap.get(testGroupId);
				contentGuardTrackedPageList = groupIdContentGuardTrackedPageListMap.get(testGroupId);
				ContentGuardUtils.getInstance().sortContentGuardTrackedPageList(contentGuardTrackedPageList, sortBy);

				// when there is no 'filter_url'
				if (StringUtils.isBlank(filterUrl)) {
					// return the required page
					contentGuardTrackedPageList = ContentGuardUtils.getInstance().paginateContentGuardTrackedPageList(domainId, groupId, response,
							contentGuardTrackedPageList, pageNumber, rowsPerPage, sortBy);
				} else {
					response.setEnd_of_tracked_pages_flag(true);
				}

				contentGuardTrackedGroup.setTracked_page_list(contentGuardTrackedPageList.toArray(new ContentGuardTrackedPage[0]));
				contentGuardTrackedGroupList.add(contentGuardTrackedGroup);
			}
			response.setTracked_group_list(contentGuardTrackedGroupList.toArray(new ContentGuardTrackedGroup[0]));
		}
		FormatUtils.getInstance().logMemoryUsage("getTrackedPages() ends. domainId=" + domainId + ",groupId=" + groupId + ",pageNumber=" + pageNumber + ",rowsPerPage="
				+ rowsPerPage + ",sortBy=" + sortBy + ",filterUrl=" + filterUrl);
		return response;
	}

	public ContentGuardResourceResponse getUsage(int domainId, String startUsageDateString, String endUsageDateString, Long groupId) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("getUsage() begins. domainId=" + domainId + ",startUsageDateString=" + startUsageDateString + ",endUsageDateString="
				+ endUsageDateString + ",groupId=" + groupId);
		long startTimestamp = System.currentTimeMillis();
		ContentGuardResourceResponse response = new ContentGuardResourceResponse();
		response.setSuccess(true);

		List<ContentGuardGroupUsage> contentGuardGroupUsageList = null;
		List<ContentGuardUsage> contentGuardUsageList = null;
		ContentGuardGroupUsage contentGuardGroupUsage = null;
		ContentGuardUsage contentGuardUsage = null;

		Integer startUsageDateNumber = null;
		Integer endUsageDateNumber = null;
		String usageDateString = null;
		ContentGuardGroupEntity contentGuardGroupEntity = null;

		// map key = group ID
		// map value = content guard group entity
		Map<Long, ContentGuardGroupEntity> groupIdContentGuardGroupEntityMap = new HashMap<Long, ContentGuardGroupEntity>();

		// map key = group ID
		// map value = map of crawl date string, total URLs
		Map<Long, Map<String, Integer>> groupIdUsageDateTotalUrlsMapMap = new HashMap<Long, Map<String, Integer>>();

		// map key = crawl date string
		// map value = total Urls
		Map<String, Integer> usageDateTotalUrlsMap = null;

		if (StringUtils.isNotBlank(startUsageDateString)) {
			startUsageDateNumber = NumberUtils.toInt(StringUtils.remove(startUsageDateString, IConstants.DASH));
		}
		if (StringUtils.isNotBlank(endUsageDateString)) {
			endUsageDateNumber = NumberUtils.toInt(StringUtils.remove(endUsageDateString, IConstants.DASH));
		}

		List<ContentGuardUsageEntity> contentGuardUsageEntityList = contentGuardUsageDAO.getList(domainId, startUsageDateNumber, endUsageDateNumber, groupId);
		if (contentGuardUsageEntityList != null && contentGuardUsageEntityList.size() > 0) {
			for (ContentGuardUsageEntity contentGuardUsageEntity : contentGuardUsageEntityList) {

				if (groupIdContentGuardGroupEntityMap.containsKey(contentGuardUsageEntity.getGroupId()) == false) {
					contentGuardGroupEntity = new ContentGuardGroupEntity();
					contentGuardGroupEntity.setGroupName(contentGuardUsageEntity.getGroupName());
					contentGuardGroupEntity.setCrawlFrequencyType(contentGuardUsageEntity.getCrawlFrequencyType());
					groupIdContentGuardGroupEntityMap.put(contentGuardUsageEntity.getGroupId(), contentGuardGroupEntity);
				}

				if (groupIdUsageDateTotalUrlsMapMap.containsKey(contentGuardUsageEntity.getGroupId())) {
					usageDateTotalUrlsMap = groupIdUsageDateTotalUrlsMapMap.get(contentGuardUsageEntity.getGroupId());
				} else {
					usageDateTotalUrlsMap = new HashMap<String, Integer>();
				}
				usageDateString = FormatUtils.getInstance().formatDateNumberToString(contentGuardUsageEntity.getUsageDate());
				usageDateTotalUrlsMap.put(usageDateString, contentGuardUsageEntity.getTotalUrls());
				groupIdUsageDateTotalUrlsMapMap.put(contentGuardUsageEntity.getGroupId(), usageDateTotalUrlsMap);
			}

			if (groupIdUsageDateTotalUrlsMapMap != null && groupIdUsageDateTotalUrlsMapMap.size() > 0) {
				contentGuardGroupUsageList = new ArrayList<ContentGuardGroupUsage>();
				for (Long testGroupId : groupIdUsageDateTotalUrlsMapMap.keySet()) {
					contentGuardGroupUsage = new ContentGuardGroupUsage();
					contentGuardGroupEntity = groupIdContentGuardGroupEntityMap.get(testGroupId);
					contentGuardGroupUsage.setGroup_id(testGroupId);
					contentGuardGroupUsage.setGroup_name(contentGuardGroupEntity.getGroupName());
					contentGuardGroupUsage.setCrawl_frequency(contentGuardGroupEntity.getCrawlFrequencyType());
					contentGuardUsageList = new ArrayList<ContentGuardUsage>();
					usageDateTotalUrlsMap = groupIdUsageDateTotalUrlsMapMap.get(testGroupId);
					if (usageDateTotalUrlsMap != null && usageDateTotalUrlsMap.size() > 0) {
						for (String testUsageDateString : usageDateTotalUrlsMap.keySet()) {
							contentGuardUsage = new ContentGuardUsage();
							contentGuardUsage.setUsage_date(testUsageDateString);
							contentGuardUsage.setTotal_urls(usageDateTotalUrlsMap.get(testUsageDateString));
							contentGuardUsageList.add(contentGuardUsage);
						}
					}
					if (contentGuardUsageList != null && contentGuardUsageList.size() > 0) {
						contentGuardGroupUsage.setUsage_list(contentGuardUsageList.toArray(new ContentGuardUsage[0]));
					}
					contentGuardGroupUsageList.add(contentGuardGroupUsage);
				}
				if (contentGuardGroupUsageList != null && contentGuardGroupUsageList.size() > 0) {
					response.setGroup_usage_list(contentGuardGroupUsageList.toArray(new ContentGuardGroupUsage[0]));
				}
			}
		}

		FormatUtils.getInstance().logMemoryUsage("getUsage() ends. domainId=" + domainId + ",startUsageDateString=" + startUsageDateString + ",endUsageDateString="
				+ endUsageDateString + ",groupId=" + groupId + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return response;
	}
}
