package com.actonia.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.actonia.IConstants;
import com.actonia.dao.TargetUrlChangeIndClickHouseDAO;
import com.actonia.entity.ContentGuardChangeTrackingEntity;
import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
import com.actonia.exception.ExceededTotalRowsException;
import com.actonia.utils.ContentGuardUtils;
import com.actonia.utils.TargetUrlChangeUtils;
import com.actonia.value.object.TargetUrlChangeRequest;
import com.actonia.value.object.TargetUrlChangeResponse;
import com.actonia.value.object.TargetUrlChangeTotal;
import com.actonia.value.object.UrlSummary;
import com.actonia.value.object.WebServiceError;

public class GetUrlSummaryService {
	private final Map<String, Integer> changeIndicatorIdMap;

	//private boolean isDebug = false;

	public GetUrlSummaryService() {
		super();
		final List<ContentGuardChangeTrackingEntity> contentGuardChangeTrackingEntities = ContentGuardUtils.getInstance().contentGuardChangeTrackingWithIdList();
		this.changeIndicatorIdMap = contentGuardChangeTrackingEntities.stream().collect(Collectors.toMap(ContentGuardChangeTrackingEntity::getIndicator, ContentGuardChangeTrackingEntity::getId));
	}

	public TargetUrlChangeResponse getList(String command, TargetUrlChangeRequest targetUrlChangeRequest) throws Exception {
		TargetUrlChangeResponse targetUrlChangeResponse = new TargetUrlChangeResponse();
		WebServiceError webServiceError = null;
		TargetUrlChangeTotal targetUrlChangeTotal = null;
		List<UrlSummary> urlSummaryList = null;
		long startTimestamp = System.currentTimeMillis();

		try {
			final HtmlChangeService htmlChangeService = new HtmlChangeService();
			targetUrlChangeRequest.setChangeIndicatorIdMap(this.changeIndicatorIdMap);
			// total rows
			targetUrlChangeTotal = htmlChangeService.calculateTotalRows(true, targetUrlChangeRequest);
			if (targetUrlChangeTotal.getTotalRows() == 0) {
				targetUrlChangeResponse.setSuccess(false);
				webServiceError = new WebServiceError();
				webServiceError.setError_code(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_DATA_NOT_AVAILABLE);
				webServiceError.setError_message(TargetUrlChangeUtils.getInstance().getErrorMessage(webServiceError.getError_code(), null));
				targetUrlChangeResponse.setError(webServiceError);
			} else {

				// URL summary
				urlSummaryList = htmlChangeService.getUrlSummaryList(targetUrlChangeRequest);
				targetUrlChangeResponse.setUrl_summary_list(urlSummaryList);

				targetUrlChangeResponse.setSuccess(true);
				targetUrlChangeResponse.setPage_number(targetUrlChangeRequest.getPage_number());
				targetUrlChangeResponse.setRows_per_page(targetUrlChangeRequest.getRows_per_page());
				targetUrlChangeResponse.setSort_by(targetUrlChangeRequest.getSort_by());
				targetUrlChangeResponse.setTotal_rows(targetUrlChangeTotal.getTotalRows());
				targetUrlChangeResponse.setEnd_of_detail_list_flag(targetUrlChangeTotal.isLastPage());
				targetUrlChangeResponse.setElapsed_millisecond(System.currentTimeMillis() - startTimestamp);
			}
		} catch (ExceededTotalRowsException e) {
			targetUrlChangeResponse.setSuccess(false);
			webServiceError = new WebServiceError();
			webServiceError.setError_code(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_OUTSIDE_DATA_RANGE);
			webServiceError.setError_message(TargetUrlChangeUtils.getInstance().getErrorMessage(webServiceError.getError_code(), String.valueOf(e.getTotalRows())));
			targetUrlChangeResponse.setError(webServiceError);
		}

		return targetUrlChangeResponse;
	}

	private List<UrlSummary> getUrlSummaryList(TargetUrlChangeRequest targetUrlChangeRequest) throws Exception {

		List<UrlSummary> urlSummaryList = new ArrayList<UrlSummary>();

		int domainId = targetUrlChangeRequest.getDomain_id();
		String startCrawlTimestampString = targetUrlChangeRequest.getStart_crawl_timestamp();
		String endCrawlTimestampString = targetUrlChangeRequest.getEnd_crawl_timestamp();
		int sortBy = targetUrlChangeRequest.getSort_by();
		int rowsPerPage = targetUrlChangeRequest.getRows_per_page();
		int pageNumber = targetUrlChangeRequest.getPage_number();
		Integer[] pageTagIdArray = targetUrlChangeRequest.getPage_tag_ids();
		List<TargetUrlChangeIndClickHouseEntity> targetUrlChangeIndClickHouseEntityList = null;

		String urlPredicate = TargetUrlChangeUtils.getInstance().getUrlPredicate(targetUrlChangeRequest.getDomain_id(), targetUrlChangeRequest.getUrls());

		String contentTypePredicate = TargetUrlChangeUtils.getInstance().getContentTypePredicate(targetUrlChangeRequest.getDomain_id(),
				targetUrlChangeRequest.getContent_types());

		String responseCodePredicate = TargetUrlChangeUtils.getInstance().getResponseCodePredicate(targetUrlChangeRequest.getDomain_id(),
				targetUrlChangeRequest.getResponse_codes());

		String[] changeIndicatorArray = targetUrlChangeRequest.getChange_indicators();

		int offset = (pageNumber - 1) * rowsPerPage;

		// retrieve details
		boolean isTotal = false;
		targetUrlChangeIndClickHouseEntityList = TargetUrlChangeIndClickHouseDAO.getInstance().getUrlSummary(domainId, startCrawlTimestampString,
				endCrawlTimestampString, sortBy, rowsPerPage, offset, isTotal, changeIndicatorArray, pageTagIdArray, contentTypePredicate, urlPredicate,
				responseCodePredicate);
		urlSummaryList = convertTargetUrlChangeIndClickHouseEntityList(targetUrlChangeIndClickHouseEntityList);
		return urlSummaryList;
	}

	private List<UrlSummary> convertTargetUrlChangeIndClickHouseEntityList(List<TargetUrlChangeIndClickHouseEntity> targetUrlChangeIndClickHouseEntityList) {
		List<UrlSummary> urlSummaryList = new ArrayList<UrlSummary>();
		UrlSummary urlSummary = null;
		if (targetUrlChangeIndClickHouseEntityList != null && targetUrlChangeIndClickHouseEntityList.size() > 0) {
			for (TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity : targetUrlChangeIndClickHouseEntityList) {
				urlSummary = new UrlSummary();
				urlSummary.setUrl(targetUrlChangeIndClickHouseEntity.getUrl());
				urlSummary.setUrl_murmur_hash(targetUrlChangeIndClickHouseEntity.getUrlMurmurHash());
				urlSummary.setTotal_changes(targetUrlChangeIndClickHouseEntity.getTotalChanges());
				urlSummary.setTotal_severity_critical(targetUrlChangeIndClickHouseEntity.getTotalSeverityCritical());
				urlSummary.setTotal_severity_high(targetUrlChangeIndClickHouseEntity.getTotalSeverityHigh());
				urlSummary.setTotal_severity_medium(targetUrlChangeIndClickHouseEntity.getTotalSeverityMedium());
				urlSummary.setTotal_severity_low(targetUrlChangeIndClickHouseEntity.getTotalSeverityLow());
				urlSummary.setTotal_change_type_added(targetUrlChangeIndClickHouseEntity.getTotalChangeTypeAdded());
				urlSummary.setTotal_change_type_modified(targetUrlChangeIndClickHouseEntity.getTotalChangeTypeModified());
				urlSummary.setTotal_change_type_removed(targetUrlChangeIndClickHouseEntity.getTotalChangeTypeRemoved());
				urlSummaryList.add(urlSummary);
			}
		}
		return urlSummaryList;
	}
}
