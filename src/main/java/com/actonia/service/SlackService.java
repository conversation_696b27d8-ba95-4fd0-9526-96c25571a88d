package com.actonia.service;

import java.util.ArrayList;
import java.util.List;

import com.actonia.IConstants;
import com.actonia.test.SlackRequest;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.HttpUtils;
import com.actonia.value.object.ContentGuardAlertDetails;
import com.actonia.value.object.ContentGuardAlertSummary;
import com.actonia.value.object.SlackBlock;
import com.actonia.value.object.SlackText;
import com.google.gson.Gson;

public class SlackService {

	//private boolean isDebug = false;

	public SlackService() {
		super();
	}

	public void sendContentGuardAlert(int domainId, String emailSubject, String groupInformation1, String groupInformation2, String groupInformation3,
			List<ContentGuardAlertSummary> contentGuardAlertSummaryList, String webhookEndpoint) throws Exception {

		FormatUtils.getInstance().logMemoryUsage("sendContentGuardAlert() begins. domainId=" + domainId + ",emailSubject=" + emailSubject + ",groupInformation1="
				+ groupInformation1 + ",groupInformation2=" + groupInformation2 + ",groupInformation3=" + groupInformation3 + ",webhookEndpoint=" + webhookEndpoint);

		String alertMessage = null;
		StringBuilder stringBuilder = null;
		String contentGuardAlertSummaryString = null;

		// {0}: emailSubject

		// {1}: groupInformation1

		// {2}: groupInformation2

		// {3}: groupInformation3

		// {4}: domainId

		// {5}: Content Guard Alert Summary 
		stringBuilder = new StringBuilder();
		if (contentGuardAlertSummaryList != null && contentGuardAlertSummaryList.size() > 0) {
			for (ContentGuardAlertSummary contentGuardAlertSummary : contentGuardAlertSummaryList) {
				for (ContentGuardAlertDetails contentGuardAlertDetails : contentGuardAlertSummary.getContentGuardAlertDetailsList()) {
					stringBuilder.append(IConstants.NEWLINE);
					stringBuilder.append(contentGuardAlertSummary.getChangeIndicatorDesc());
					stringBuilder.append(IConstants.ONE_SPACE);
					stringBuilder.append(IConstants.DASH);
					stringBuilder.append(IConstants.ONE_SPACE);
					stringBuilder.append(contentGuardAlertDetails.getUrl());
				}
			}
			contentGuardAlertSummaryString = stringBuilder.toString();
		}

		alertMessage = getSlackJson(emailSubject, groupInformation1, groupInformation2, groupInformation3, domainId, contentGuardAlertSummaryString);
		System.out.println("alertMessage=" + alertMessage);
		invokeWebhook(webhookEndpoint, alertMessage);
		FormatUtils.getInstance()
				.logMemoryUsage("sendContentGuardAlert() ends. domainId=" + domainId + ",emailSubject=" + emailSubject + ",groupInformation1=" + groupInformation1
						+ ",groupInformation2=" + groupInformation2 + ",groupInformation3=" + groupInformation3 + ",webhookEndpoint=" + webhookEndpoint
						+ ",alertMessage=" + alertMessage);
	}

	private void invokeWebhook(String requestUrl, String requestParameters) {
		FormatUtils.getInstance().logMemoryUsage("invokeWebhook() begins. requestUrl=" + requestUrl + ",requestParameters=" + requestParameters);
		long startTimestamp = System.currentTimeMillis();
		String responseString = null;
		boolean isSendGetRequest = false;
		try {
			responseString = HttpUtils.getInstance().getResponseString(requestUrl, isSendGetRequest, requestParameters);
			if (responseString != null) {
				FormatUtils.getInstance().logMemoryUsage("invokeWebhook() responseString=" + responseString);
			} else {
				FormatUtils.getInstance().logMemoryUsage("invokeWebhook() responseString is empty.");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		FormatUtils.getInstance().logMemoryUsage("invokeWebhook() ends. requestUrl=" + requestUrl + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private String getSlackJson(String emailSubject, String groupInformation1, String groupInformation2, String groupInformation3, int domainId,
			String contentGuardAlertSummaryString) {
		SlackRequest slackRequest = null;
		SlackBlock[] blocks = null;
		SlackBlock slackBlock = null;
		SlackText slackText = null;
		String json = null;
		StringBuilder stringBuilder = null;

		List<SlackBlock> slackBlockList = new ArrayList<SlackBlock>();

		// emailSubject
		slackText = new SlackText();
		slackText.setType("mrkdwn");
		stringBuilder = new StringBuilder();
		stringBuilder.append("*");
		stringBuilder.append(emailSubject);
		stringBuilder.append("*");
		slackText.setText(stringBuilder.toString());
		slackBlock = new SlackBlock();
		slackBlock.setType("section");
		slackBlock.setText(slackText);
		slackBlockList.add(slackBlock);

		// groupInformation1, groupInformation2, groupInformation3, domainId
		slackText = new SlackText();
		slackText.setType("mrkdwn");
		stringBuilder = new StringBuilder();
		stringBuilder.append("_");
		stringBuilder.append(groupInformation1);
		stringBuilder.append("._\n_");
		stringBuilder.append(groupInformation2);
		stringBuilder.append("._\n_");
		stringBuilder.append(groupInformation3);
		stringBuilder.append("._\n_");
		stringBuilder.append("The domain ID is ");
		stringBuilder.append(domainId);
		stringBuilder.append("._");
		slackText.setText(stringBuilder.toString());
		slackBlock = new SlackBlock();
		slackBlock.setType("section");
		slackBlock.setText(slackText);
		slackBlockList.add(slackBlock);

		// Content Guard Alert Summary
		slackText = new SlackText();
		slackText.setType("mrkdwn");
		stringBuilder = new StringBuilder();
		stringBuilder.append("*Change - URL*");
		stringBuilder.append(contentGuardAlertSummaryString);
		slackText.setText(stringBuilder.toString());
		slackBlock = new SlackBlock();
		slackBlock.setType("section");
		slackBlock.setText(slackText);
		slackBlockList.add(slackBlock);

		blocks = slackBlockList.toArray(new SlackBlock[0]);

		slackRequest = new SlackRequest();
		slackRequest.setBlocks(blocks);

		json = new Gson().toJson(slackRequest, SlackRequest.class);

		System.out.println("json=" + json);

		return json;

	}
}
