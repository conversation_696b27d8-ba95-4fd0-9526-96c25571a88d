package com.actonia.service;

import java.util.ArrayList;
import java.util.List;

import com.actonia.dao.AlertMetricsNameClickHouseDAO;
import com.actonia.entity.AlertMetricsNameClickHouseEntity;
import com.actonia.utils.FormatUtils;

// Due to ClickHouse concurrency issues, use Singleton pattern, do not use Spring (ie. all methods must be 'synchronized')
public class AlertMetricsNameClickHouseService {

	private static AlertMetricsNameClickHouseService alertMetricsNameClickHouseService;

	public static AlertMetricsNameClickHouseService getInstance() throws Exception {
		if (alertMetricsNameClickHouseService == null) {
			alertMetricsNameClickHouseService = new AlertMetricsNameClickHouseService();
		}
		return alertMetricsNameClickHouseService;
	}
	
	public synchronized void resetTestTable() throws Exception {
		AlertMetricsNameClickHouseDAO.getInstance().resetTable(AlertMetricsNameClickHouseDAO.TEST_TABLE_NAME);
	}

	public synchronized void createInTestTable(List<AlertMetricsNameClickHouseEntity> alertMetricsNameClickHouseEntityList) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("createInTestTable() begins. alertMetricsNameClickHouseEntityList.size()=" + alertMetricsNameClickHouseEntityList.size());
		List<AlertMetricsNameClickHouseEntity> testAlertMetricsNameClickHouseEntity = null;
		if (alertMetricsNameClickHouseEntityList != null && alertMetricsNameClickHouseEntityList.size() > 0) {
			testAlertMetricsNameClickHouseEntity = new ArrayList<AlertMetricsNameClickHouseEntity>();
			for (AlertMetricsNameClickHouseEntity alertMetricsNameClickHouseEntity : alertMetricsNameClickHouseEntityList) {
				testAlertMetricsNameClickHouseEntity.add(alertMetricsNameClickHouseEntity);
				if (testAlertMetricsNameClickHouseEntity != null
						&& testAlertMetricsNameClickHouseEntity.size() >= AlertMetricsNameClickHouseDAO.getInstance().getBatchCreationSize()) {
					AlertMetricsNameClickHouseDAO.getInstance().createBatch(testAlertMetricsNameClickHouseEntity, AlertMetricsNameClickHouseDAO.TEST_TABLE_NAME);
					testAlertMetricsNameClickHouseEntity = new ArrayList<AlertMetricsNameClickHouseEntity>();
				}
			}
			if (testAlertMetricsNameClickHouseEntity != null && testAlertMetricsNameClickHouseEntity.size() > 0) {
				AlertMetricsNameClickHouseDAO.getInstance().createBatch(testAlertMetricsNameClickHouseEntity, AlertMetricsNameClickHouseDAO.TEST_TABLE_NAME);
				testAlertMetricsNameClickHouseEntity = new ArrayList<AlertMetricsNameClickHouseEntity>();
			}
		}
		FormatUtils.getInstance().logMemoryUsage("createInTestTable() ends. alertMetricsNameClickHouseEntityList.size()=" + alertMetricsNameClickHouseEntityList.size()
				+ ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	public synchronized int getBatchCreationSize() throws Exception {
		return AlertMetricsNameClickHouseDAO.getInstance().getBatchCreationSize();
	}

	public synchronized List<AlertMetricsNameClickHouseEntity> getList(String tableName) throws Exception {
		return AlertMetricsNameClickHouseDAO.getInstance().getList(tableName);
	}

}
