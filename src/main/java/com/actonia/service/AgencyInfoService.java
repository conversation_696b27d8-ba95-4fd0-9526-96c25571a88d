/**
 * 
 */
package com.actonia.service;

import com.actonia.dao.AgencyInfoDAO;
import com.actonia.dao.OwnDomainSettingEntityDAO;
import com.actonia.entity.AgencyInfoEntity;
import com.actonia.entity.OwnDomainSettingEntity;

public class AgencyInfoService {

	private AgencyInfoDAO agencyInfoDAO;
	
	private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
	
	public AgencyInfoEntity getByDomainId(int ownDomainId) {
		String companyName = null;
		
		OwnDomainSettingEntity ownDomainSetting = ownDomainSettingEntityDAO.getSettingByDomainId(ownDomainId);
		if (ownDomainSetting != null) {
			companyName = ownDomainSetting.getCompanyName();
		}
		
		return getByCompanyName(companyName);
	}
	
	public AgencyInfoEntity getByCompanyName(String companyName) {
		AgencyInfoEntity agencyInfo = agencyInfoDAO.getByCompanyName(companyName);
		if (agencyInfo != null) {
			return agencyInfo;
		}
		return agencyInfoDAO.getDefault();
	}

	public void setAgencyInfoDAO(AgencyInfoDAO agencyInfoDAO) {
		this.agencyInfoDAO = agencyInfoDAO;
	}

	public void setOwnDomainSettingEntityDAO(OwnDomainSettingEntityDAO ownDomainSettingEntityDAO) {
		this.ownDomainSettingEntityDAO = ownDomainSettingEntityDAO;
	}
	
}
