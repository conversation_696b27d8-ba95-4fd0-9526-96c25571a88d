package com.actonia.service;

import com.actonia.dao.ChangeIndMasterClickHouseDAO;
import com.actonia.dao.HtmlBigDataClickHouseDAO;
import com.actonia.dao.HtmlChangeClickHouseDAO;
import com.actonia.entity.ChangeIndMaster;
import com.actonia.entity.HtmlBigData;
import com.actonia.entity.HtmlChange;
import com.actonia.exception.ExceededTotalRowsException;
import com.actonia.value.object.*;
import com.actonia.value.object.HtmlChangeResponse.HtmlChangeAggregateModel;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.util.StopWatch;

import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

import static com.actonia.IConstants.*;

@Log4j2
public class HtmlChangeService {

	private final HtmlChangeClickHouseDAO htmlChangeClickHouseDAO;
	private final HtmlBigDataClickHouseDAO htmlBigDataClickHouseDAO;
	private final Set<Integer> bigDataIdSet;
	private final Map<String, Integer> changeIndicatorIdMap;

	@SneakyThrows
	public HtmlChangeService() {
		htmlBigDataClickHouseDAO = HtmlBigDataClickHouseDAO.getInstance();
		htmlChangeClickHouseDAO = HtmlChangeClickHouseDAO.getInstance();
		ChangeIndMasterClickHouseDAO changeIndMasterClickHouseDAO = ChangeIndMasterClickHouseDAO.getInstance();
		bigDataIdSet = changeIndMasterClickHouseDAO.bigDataIndicatorSet.stream().map(ChangeIndMaster::getChgId).collect(Collectors.toSet());
		this.changeIndicatorIdMap = changeIndMasterClickHouseDAO.indicatorReverseIdMap;
	}

	private static String generateConditions(HtmlChange htmlChange) {
		final String urlMurmurHash = htmlChange.getUrl_murmur_hash();
		StringBuilder sb = new StringBuilder();
		final String prevMd5 = htmlChange.getPrevValue();
		final BigInteger mod = new BigInteger(urlMurmurHash).mod(BigInteger.valueOf(1000));
		if (StringUtils.isNotBlank(prevMd5) && prevMd5.length() > 2) {
			final String previousTrackDate = DateFormatUtils.format(htmlChange.getPrevCrawlTimestamp(), DATE_FORMAT_YYYY_MM_DD);
			sb.append(" (track_date = '")
					.append(previousTrackDate)
					.append("' and url_murmur_hash_mod = '").append(mod.intValue())
					.append("' and url_murmur_hash = '")
					.append(urlMurmurHash)
					.append("' and md5 = '")
					.append(prevMd5)
					.append("')");
		}
		final String currMd5 = htmlChange.getCurrValue();
		if (StringUtils.isNotBlank(currMd5) && currMd5.length() > 2) {
			final String currentTrackDate = DateFormatUtils.format(htmlChange.getCurrCrawlTimestamp(), DATE_FORMAT_YYYY_MM_DD);
			if (sb.length() > 0) sb.append(" OR ");
			sb.append(" (track_date = '")
					.append(currentTrackDate)
					.append("' and url_murmur_hash_mod = '").append(mod.intValue())
					.append("' and url_murmur_hash = '")
					.append(urlMurmurHash)
					.append("' and md5 = '")
					.append(currMd5)
					.append("' )");
		}
		return sb.toString();
	}

	public TargetUrlChangeTotal calculateTotalRows(boolean isUrlSummary, TargetUrlChangeRequest targetUrlChangeRequest) throws ExceededTotalRowsException {
		targetUrlChangeRequest.setChangeIndicatorIdMap(this.changeIndicatorIdMap);
		TargetUrlChangeTotal targetUrlChangeTotal = new TargetUrlChangeTotal();
		final int totalRows = htmlChangeClickHouseDAO.calculateTotalRows(targetUrlChangeRequest, targetUrlChangeRequest.getFilterCondition(), isUrlSummary);
		targetUrlChangeTotal.setTotalRows(totalRows);
		if (totalRows > 0) {
			int pageNumber = targetUrlChangeRequest.getPage_number();
			int lastPageNumber = getLastPageNumber(totalRows, targetUrlChangeRequest.getRows_per_page());
			if (pageNumber > lastPageNumber) {
				log.error("domainId: {} calculateTotalRows() exceeds the maximum totalRows: {}", targetUrlChangeRequest.getDomain_id(), totalRows);
				throw new ExceededTotalRowsException(totalRows);
			} else targetUrlChangeTotal.setLastPage(pageNumber == lastPageNumber);
		}
		return targetUrlChangeTotal;
	}

	public List<HtmlChange> getTargetUrlChangeIndicatorDetails(TargetUrlChangeRequest targetUrlChangeRequest) {
		final StopWatch stopWatch = new StopWatch("getTargetUrlChangeIndicatorDetails()");
		stopWatch.start("query");
		final List<HtmlChange> htmlChanges = this.htmlChangeClickHouseDAO.getTargetUrlChangeIndicatorDetails(targetUrlChangeRequest, targetUrlChangeRequest.getFilterCondition());
		stopWatch.stop();
		log.info("query htmlChangeList size: {}, cost: {}", htmlChanges.size(), stopWatch.getTotalTimeMillis());
		// 1. split list by chgId is bigDataIdSet or not
		final List<HtmlChange> bigDataList = new ArrayList<>();
		final List<HtmlChange> notBigDataList = new ArrayList<>();
		final List<HtmlChange> htmlChangesList = new ArrayList<>(htmlChanges.size());
		for (int i = 0; i < htmlChanges.size(); i++) {
			HtmlChange change = htmlChanges.get(i);
			change.setOrderIndex(i);
			change.setSeverityString();
			if (bigDataIdSet.contains(change.getChgId())) {
				bigDataList.add(change);
			} else {
				if ("response_code_chg_ind".equals(change.getChange_indicator())) {
					change.setPrevValue(change.getResponse_code_previous());
					change.setCurrValue(change.getResponse_code_current());
				}
				notBigDataList.add(change);
			}
		}
		// 2. replace md5 to rawData for bigDataList
		if (!bigDataList.isEmpty()) {
			stopWatch.start("replaceBigData");
			replaceBigData(bigDataList);
			stopWatch.stop();
			log.info("replaceBigData cost: {}", stopWatch.getTotalTimeMillis());
		} else {
			// no bigDataList to replace md5 to rawData, just return htmlChanges
			return htmlChanges;
		}
		// 3. merge bigDataList and notBigDataList
		htmlChangesList.addAll(bigDataList);
		htmlChangesList.addAll(notBigDataList);
		htmlChangesList.sort(Comparator.comparingInt(HtmlChange::getOrderIndex));
		log.info("merge bigDataList and notBigDataList size: {}\n{}", htmlChangesList.size(), stopWatch.prettyPrint());
		return htmlChangesList;
	}

	/**
	 * change indicator summary
	 *
	 * @return List<TargetUrlChangeSummary>
	 */
	public List<TargetUrlChangeSummary> getTargetUrlChangeSummaryList(TargetUrlChangeRequest targetUrlChangeRequest) {
		final List<HtmlChangeAggregateModel> hourlyTotals = htmlChangeClickHouseDAO.getHourlyTotals(targetUrlChangeRequest, targetUrlChangeRequest.getFilterCondition());
		if (hourlyTotals.isEmpty()) return Collections.emptyList();
		// group by crawl_date_hour first
		final Map<String, List<HtmlChangeAggregateModel>> hourlyTotalsMap = hourlyTotals.stream().collect(Collectors.groupingBy(HtmlChangeAggregateModel::getCrawlDateHour));
		// convert map to list
		final List<TargetUrlChangeSummary> targetUrlChangeSummaryList = new ArrayList<>(hourlyTotalsMap.size());
		for (Map.Entry<String, List<HtmlChangeAggregateModel>> entry : hourlyTotalsMap.entrySet()) {
			final String crawlDateHour = entry.getKey();
			final List<HtmlChangeAggregateModel> hourlyTotalsList = entry.getValue();
			int totalAdded = 0;
			int totalModified = 0;
			int totalRemoved = 0;
			int totalCritical = 0;
			int totalHigh = 0;
			int totalMedium = 0;
			int totalLow = 0;
			ChangeIndicatorTotalUrls[] change_indicator_total_urls_array = new ChangeIndicatorTotalUrls[hourlyTotalsList.size()];
			for (int i = 0; i < hourlyTotalsList.size(); i++) {
				HtmlChangeAggregateModel htmlChangeAggregateModel = hourlyTotalsList.get(i);
				switch (htmlChangeAggregateModel.getChangeType()) {
					case CHANGE_TYPE_ADDED:
						totalAdded += htmlChangeAggregateModel.getTotal();
						break;
					case CHANGE_TYPE_MODIFIED:
						totalModified += htmlChangeAggregateModel.getTotal();
						break;
					case CHANGE_TYPE_REMOVED:
						totalRemoved += htmlChangeAggregateModel.getTotal();
						break;
				}
				switch (htmlChangeAggregateModel.getCriticalFlg()) {
					case 1:
						totalCritical += htmlChangeAggregateModel.getTotal();
						break;
					case 2:
						totalHigh += htmlChangeAggregateModel.getTotal();
						break;
					case 3:
						totalMedium += htmlChangeAggregateModel.getTotal();
						break;
					case 4:
						totalLow += htmlChangeAggregateModel.getTotal();
						break;
				}

				ChangeIndicatorTotalUrls changeIndicatorTotalUrls = new ChangeIndicatorTotalUrls();
				changeIndicatorTotalUrls.setChange_indicator(htmlChangeAggregateModel.getChangeIndicator());
				changeIndicatorTotalUrls.setTotal_urls(htmlChangeAggregateModel.getTotal());
				change_indicator_total_urls_array[i] = changeIndicatorTotalUrls;
			}
			final TargetUrlChangeSummary targetUrlChangeSummary = new TargetUrlChangeSummary();
			targetUrlChangeSummary.setTotal_added(totalAdded);
			targetUrlChangeSummary.setTotal_modified(totalModified);
			targetUrlChangeSummary.setTotal_removed(totalRemoved);
			targetUrlChangeSummary.setTotal_severity_critical(totalCritical);
			targetUrlChangeSummary.setTotal_severity_high(totalHigh);
			targetUrlChangeSummary.setTotal_severity_medium(totalMedium);
			targetUrlChangeSummary.setTotal_severity_low(totalLow);
			targetUrlChangeSummary.setCrawl_date_hour(crawlDateHour);
			targetUrlChangeSummary.setChange_indicator_total_urls_array(change_indicator_total_urls_array);
			targetUrlChangeSummaryList.add(targetUrlChangeSummary);
		}
		return targetUrlChangeSummaryList;
	}

	public List<UrlSummary> getUrlSummaryList(TargetUrlChangeRequest targetUrlChangeRequest) {
		return this.htmlChangeClickHouseDAO.getUrlSummaryList(targetUrlChangeRequest, targetUrlChangeRequest.getFilterCondition());
	}

	public void replaceBigData(List<HtmlChange> bigDataList) {
		final StopWatch stopWatch = new StopWatch("replaceBigData");
		stopWatch.start("generate sql condition");
//		final String sqlCondition = bigDataList.stream().map(HtmlChangeService::generateConditions).filter(StringUtils::isNotBlank).collect(Collectors.joining(" OR "));
		Map<String, List<String>> trackDateCondition = new HashMap<>();
		for (HtmlChange htmlChange : bigDataList) {
			final String urlMurmurHash = htmlChange.getUrl_murmur_hash();
			final String prevValue = htmlChange.getPrevValue();
			if (StringUtils.isNotBlank(prevValue) && prevValue.length() > 2) {
				final String previousTrackDate = DateFormatUtils.format(htmlChange.getPrevCrawlTimestamp(), DATE_FORMAT_YYYY_MM_DD);
				String previousCondition = "(url_hash = " + htmlChange.getUrl_hash() + " and url_murmur_hash = '" + urlMurmurHash + "' and md5 = '" + prevValue + "')";
				final List<String> conditions = trackDateCondition.getOrDefault(previousTrackDate, new ArrayList<>());
				conditions.add(previousCondition);
				trackDateCondition.put(previousTrackDate, conditions);
			}
			final String currValue = htmlChange.getCurrValue();
			if (StringUtils.isNotBlank(currValue) && currValue.length() > 2) {
				final String currentTrackDate = DateFormatUtils.format(htmlChange.getCurrCrawlTimestamp(), DATE_FORMAT_YYYY_MM_DD);
				String currentCondition = "(url_hash = " + htmlChange.getUrl_hash() + " and url_murmur_hash = '" + urlMurmurHash + "' and md5 = '" + currValue + "')";
				final List<String> conditions = trackDateCondition.getOrDefault(currentTrackDate, new ArrayList<>());
				conditions.add(currentCondition);
				trackDateCondition.put(currentTrackDate, conditions);
			}
		}
		final String sqlCondition = trackDateCondition.keySet().stream().map(trackDate -> "( track_date = '" + trackDate + "' and (" + StringUtils.join(trackDateCondition.get(trackDate), "or") + "))").collect(Collectors.joining(" OR "));
		stopWatch.stop();
		stopWatch.start("query bigDataList");
		// 1. query bigDataList
		log.info("replace bigData sql: {}", sqlCondition);
		// if sqlCondition is blank, means no need to query bigDataList just return
		if (StringUtils.isBlank(sqlCondition)) return;
		final List<HtmlBigData> htmlBigDataList = this.htmlBigDataClickHouseDAO.queryAllBySqlCondition(sqlCondition);
		stopWatch.stop();
		log.info("query bigDataList size: {}, cost: {}", htmlBigDataList.size(), stopWatch.getTotalTimeMillis());
		// 2. convert list to map
		stopWatch.start("convert bigDataList to map");
		final Map<String, String> bigDataMap = htmlBigDataList.stream().collect(Collectors.toMap(HtmlBigData::getMd5, HtmlBigData::getRawData, (oldValue, newValue) -> oldValue));
		stopWatch.stop();
		log.info("convert bigDataList to map size: {}, cost: {}", bigDataMap.size(), stopWatch.getTotalTimeMillis());
		stopWatch.start("replace bigData");
		// 3. replace md5 to rawData
		bigDataList.forEach(htmlChange -> {
			String prevValue = htmlChange.getPrevValue();
			if (StringUtils.isNotBlank(prevValue)) {
				final String prevRawData = bigDataMap.get(prevValue);
				htmlChange.setPrevValue(prevRawData);
			}
			String currValue = htmlChange.getCurrValue();
			if (StringUtils.isNotBlank(currValue)) {
				final String currRawData = bigDataMap.get(currValue);
				htmlChange.setCurrValue(currRawData);
			}
		});
		stopWatch.stop();
		log.info("replace bigData cost: {}\n{}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
	}

	private int getLastPageNumber(int totalRows, int rowsPerPage) {
		int initialPageNumber = totalRows / rowsPerPage;
		int modulus = totalRows % rowsPerPage;
		return modulus > 0 ? initialPageNumber + 1 : initialPageNumber;
	}

}
