package com.actonia.service;

import java.text.MessageFormat;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;

import com.actonia.IConstants;
import com.actonia.dao.GroupTagEntityDAO;
import com.actonia.dao.PageTagAlertConfigDAO;
import com.actonia.dao.TargetUrlChangeAlertDAO;
import com.actonia.dao.ZapierErrorCodeDAO;
import com.actonia.dao.ZapierWebhookDAO;
import com.actonia.entity.GroupTagEntity;
import com.actonia.entity.PageTagAlertConfigEntity;
import com.actonia.entity.TargetUrlChangeAlertEntity;
import com.actonia.entity.ZapierErrorCodeEntity;
import com.actonia.entity.ZapierWebhookEntity;
import com.actonia.utils.CrawlerUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.ZapierContentGuardAlert;
import com.actonia.value.object.ZapierCrawlCompletedAlert;
import com.actonia.value.object.ZapierCrawlInitiatedAlert;
import com.actonia.value.object.ZapierPageTagContentAlert;
import com.actonia.value.object.ZapierResourceResponse;
import com.actonia.value.object.ZapierTargetUrlChangeAlert;
import com.google.gson.Gson;

public class ZapierService {

	//private boolean isDebug = false;
	private ZapierWebhookDAO zapierWebhookDAO;
	private ZapierErrorCodeDAO zapierErrorCodeDAO;
	private GroupTagEntityDAO groupTagEntityDAO;
	private PageTagAlertConfigDAO pageTagAlertConfigDAO;
	private TargetUrlChangeAlertDAO targetUrlChangeAlertDAO;

	public ZapierService() {
		super();
		zapierWebhookDAO = SpringBeanFactory.getBean("zapierWebhookDAO");
		zapierErrorCodeDAO = SpringBeanFactory.getBean("zapierErrorCodeDAO");
		groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
		pageTagAlertConfigDAO = SpringBeanFactory.getBean("pageTagAlertConfigDAO");
		targetUrlChangeAlertDAO = SpringBeanFactory.getBean("targetUrlChangeAlertDAO");
	}

	public ZapierResourceResponse createContentGuardAlertSubscription(int domainId, int userId, Long groupId, String callbackUrl) throws Exception {
		FormatUtils.getInstance().logMemoryUsage(
				"createContentGuardAlertSubscription() begins. domainId=" + domainId + ",userId=" + userId + ",groupId=" + groupId + ",callbackUrl=" + callbackUrl);

		ZapierResourceResponse zapierResourceResponse = new ZapierResourceResponse();
		Long id = null;
		String subType = String.valueOf(groupId);
		String subTypeHashCd = CrawlerUtils.getInstance().getMd5HashCode(subType);

		ZapierWebhookEntity zapierWebhookEntity = zapierWebhookDAO.get(domainId, IConstants.ZAPIER_TRIGGER_TYPE_CONTENT_GUARD_ALERT, userId, subTypeHashCd);
		if (zapierWebhookEntity != null) {
			zapierResourceResponse.setError_code(IConstants.MSG_CD_ZAPIER_SUBSCRIPTION_ALREADY_EXIST);
			zapierResourceResponse.setError_message(getErrorMessage(IConstants.MSG_CD_ZAPIER_SUBSCRIPTION_ALREADY_EXIST, null));
		} else {
			zapierWebhookEntity = new ZapierWebhookEntity();
			zapierWebhookEntity.setDomainId(domainId);
			zapierWebhookEntity.setUserId(userId);
			zapierWebhookEntity.setTriggerType(IConstants.ZAPIER_TRIGGER_TYPE_CONTENT_GUARD_ALERT);
			zapierWebhookEntity.setSubTypeHashCd(subTypeHashCd);
			zapierWebhookEntity.setSubType(subType);
			zapierWebhookEntity.setCallbackUrl(callbackUrl);
			zapierWebhookEntity.setCreateDate(new Date());
			id = zapierWebhookDAO.create(zapierWebhookEntity);
			if (id != null) {
				zapierResourceResponse.setId(id);
			} else {
				zapierResourceResponse.setError_code(IConstants.MSG_CD_ZAPIER_SUBSCRIPTION_CANNOT_BE_CREATED);
				zapierResourceResponse.setError_message(getErrorMessage(IConstants.MSG_CD_ZAPIER_SUBSCRIPTION_CANNOT_BE_CREATED, null));
			}
		}

		FormatUtils.getInstance().logMemoryUsage(
				"createContentGuardAlertSubscription() ends. domainId=" + domainId + ",userId=" + userId + ",groupId=" + groupId + ",callbackUrl=" + callbackUrl);

		return zapierResourceResponse;
	}

	public ZapierResourceResponse createPageTagContentAlertSubscription(int domainId, int userId, Integer pageTagId, String callbackUrl) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("createPageTagContentAlertSubscription() begins. domainId=" + domainId + ",userId=" + userId + ",pageTagId="
				+ pageTagId + ",callbackUrl=" + callbackUrl);

		ZapierResourceResponse zapierResourceResponse = new ZapierResourceResponse();
		Long id = null;
		String subType = String.valueOf(pageTagId);
		String subTypeHashCd = CrawlerUtils.getInstance().getMd5HashCode(subType);

		ZapierWebhookEntity zapierWebhookEntity = zapierWebhookDAO.get(domainId, IConstants.ZAPIER_TRIGGER_TYPE_PAGE_TAG_CONTENT_ALERT, userId, subTypeHashCd);
		if (zapierWebhookEntity != null) {
			zapierResourceResponse.setError_code(IConstants.MSG_CD_ZAPIER_SUBSCRIPTION_ALREADY_EXIST);
			zapierResourceResponse.setError_message(getErrorMessage(IConstants.MSG_CD_ZAPIER_SUBSCRIPTION_ALREADY_EXIST, null));
		} else {
			zapierWebhookEntity = new ZapierWebhookEntity();
			zapierWebhookEntity.setDomainId(domainId);
			zapierWebhookEntity.setUserId(userId);
			zapierWebhookEntity.setTriggerType(IConstants.ZAPIER_TRIGGER_TYPE_PAGE_TAG_CONTENT_ALERT);
			zapierWebhookEntity.setSubTypeHashCd(subTypeHashCd);
			zapierWebhookEntity.setSubType(subType);
			zapierWebhookEntity.setCallbackUrl(callbackUrl);
			zapierWebhookEntity.setCreateDate(new Date());
			id = zapierWebhookDAO.create(zapierWebhookEntity);
			if (id != null) {
				zapierResourceResponse.setId(id);
			} else {
				zapierResourceResponse.setError_code(IConstants.MSG_CD_ZAPIER_SUBSCRIPTION_CANNOT_BE_CREATED);
				zapierResourceResponse.setError_message(getErrorMessage(IConstants.MSG_CD_ZAPIER_SUBSCRIPTION_CANNOT_BE_CREATED, null));
			}
		}

		FormatUtils.getInstance().logMemoryUsage(
				"createPageTagContentAlertSubscription() ends. domainId=" + domainId + ",userId=" + userId + ",pageTagId=" + pageTagId + ",callbackUrl=" + callbackUrl);

		return zapierResourceResponse;
	}

	public ZapierContentGuardAlert[] getSampleZapierContentGuardAlerts() throws Exception {
		ZapierContentGuardAlert[] zapierContentGuardAlertArray = new ZapierContentGuardAlert[2];
		ZapierContentGuardAlert zapierContentGuardAlert = null;

		// first alert
		zapierContentGuardAlert = new ZapierContentGuardAlert();
		zapierContentGuardAlert.setId("1614274302629");
		zapierContentGuardAlert.setAlert_timestamp("2021-02-25 11:31:32");
		zapierContentGuardAlert.setDomain_id(987654);
		zapierContentGuardAlert.setDomain_name("www.testdomain.com");
		zapierContentGuardAlert.setGroup_id(876583L);
		zapierContentGuardAlert.setGroup_name("Daily Test Group 1");
		zapierContentGuardAlert.setUrl("https://www.testdomain.com/page_1.html");
		zapierContentGuardAlert.setChange_desc("Description changed");
		zapierContentGuardAlert.setChange_severity("Medium");
		zapierContentGuardAlert.setPrevious_crawl_timestamp("2021-02-10 00:02:21");
		zapierContentGuardAlert.setCurrent_crawl_timestamp("2021-02-11 00:02:23");
		zapierContentGuardAlert.setPrevious_content("Previous description content.");
		zapierContentGuardAlert.setCurrent_content("Current description content.");
		zapierContentGuardAlert.setError_code("");
		zapierContentGuardAlert.setError_message("");
		zapierContentGuardAlertArray[0] = zapierContentGuardAlert;

		// second alert
		zapierContentGuardAlert = new ZapierContentGuardAlert();
		zapierContentGuardAlert.setId("1614274302630");
		zapierContentGuardAlert.setAlert_timestamp("2021-02-25 11:31:38");
		zapierContentGuardAlert.setDomain_id(987654);
		zapierContentGuardAlert.setDomain_name("www.testdomain.com");
		zapierContentGuardAlert.setGroup_id(876588L);
		zapierContentGuardAlert.setGroup_name("Hourly Test Group 2");
		zapierContentGuardAlert.setUrl("https://www.testdomain.com/page_2.html");
		zapierContentGuardAlert.setChange_desc("Title changed");
		zapierContentGuardAlert.setChange_severity("Critical");
		zapierContentGuardAlert.setPrevious_crawl_timestamp("2021-02-27 18:02:21");
		zapierContentGuardAlert.setCurrent_crawl_timestamp("2021-02-28 18:02:23");
		zapierContentGuardAlert.setPrevious_content("Previous title content.");
		zapierContentGuardAlert.setCurrent_content("Current title content.");
		zapierContentGuardAlert.setError_code("");
		zapierContentGuardAlert.setError_message("");
		zapierContentGuardAlertArray[1] = zapierContentGuardAlert;

		return zapierContentGuardAlertArray;
	}

	public String getErrorMessage(String errorCode, String supplementalMessageText) {
		String errorMessage = null;
		String errorTemplate = null;
		ZapierErrorCodeEntity zapierErrorCodeEntity = zapierErrorCodeDAO.get(errorCode);
		if (zapierErrorCodeEntity != null) {
			errorTemplate = zapierErrorCodeEntity.getErrorMessage();
			FormatUtils.getInstance().logMemoryUsage("getErrorMessage() errorTemplate=" + errorTemplate);
			if (StringUtils.isNotBlank(supplementalMessageText)) {
				errorMessage = MessageFormat.format(errorTemplate, supplementalMessageText);
			} else {
				errorMessage = errorTemplate;
			}
		}
		FormatUtils.getInstance().logMemoryUsage("getErrorMessage() errorMessage=" + errorMessage);
		return errorMessage;
	}

	public boolean isValidId(String idString) {
		boolean output = false;
		Long id = null;
		ZapierWebhookEntity zapierWebhookEntity = null;
		try {
			id = new Long(idString);
			zapierWebhookEntity = zapierWebhookDAO.get(id);
			if (zapierWebhookEntity != null) {
				output = true;
			}
		}
		// when idString is not a Long number
		catch (Exception e) {
		}
		return output;
	}

	public void deleteSubscription(Long id) {
		FormatUtils.getInstance().logMemoryUsage("deleteSubscription() begins. id=" + id);
		ZapierResourceResponse zapierResourceResponse = new ZapierResourceResponse();
		zapierWebhookDAO.delete(id);
		zapierResourceResponse.setId(id);
		FormatUtils.getInstance().logMemoryUsage("deleteSubscription() ends. id=" + id);
	}

	public ZapierResourceResponse createSiteAuditAlertSubscription(int domainId, int userId, String callbackUrl, int triggerType) {
		FormatUtils.getInstance().logMemoryUsage("createSiteAuditAlertSubscription() begins. domainId=" + domainId + ",userId=" + userId + ",callbackUrl=" + callbackUrl
				+ ",triggerType=" + triggerType);

		ZapierResourceResponse zapierResourceResponse = new ZapierResourceResponse();
		Long id = null;

		ZapierWebhookEntity zapierWebhookEntity = zapierWebhookDAO.get(domainId, triggerType, userId);
		if (zapierWebhookEntity != null) {
			zapierResourceResponse.setError_code(IConstants.MSG_CD_ZAPIER_SUBSCRIPTION_ALREADY_EXIST);
			zapierResourceResponse.setError_message(getErrorMessage(IConstants.MSG_CD_ZAPIER_SUBSCRIPTION_ALREADY_EXIST, null));
		} else {
			zapierWebhookEntity = new ZapierWebhookEntity();
			zapierWebhookEntity.setDomainId(domainId);
			zapierWebhookEntity.setUserId(userId);
			zapierWebhookEntity.setTriggerType(triggerType);
			zapierWebhookEntity.setCallbackUrl(callbackUrl);
			zapierWebhookEntity.setCreateDate(new Date());
			id = zapierWebhookDAO.create(zapierWebhookEntity);
			if (id != null) {
				zapierResourceResponse.setId(id);
			} else {
				zapierResourceResponse.setError_code(IConstants.MSG_CD_ZAPIER_SUBSCRIPTION_CANNOT_BE_CREATED);
				zapierResourceResponse.setError_message(getErrorMessage(IConstants.MSG_CD_ZAPIER_SUBSCRIPTION_CANNOT_BE_CREATED, null));
			}
		}

		FormatUtils.getInstance().logMemoryUsage("createSiteAuditAlertSubscription() ends. domainId=" + domainId + ",userId=" + userId + ",callbackUrl=" + callbackUrl);

		return zapierResourceResponse;
	}

	public ZapierCrawlInitiatedAlert[] getSampleZapierCrawlInitiatedAlerts() throws Exception {

		ZapierCrawlInitiatedAlert[] zapierCrawlInitiatedAlertArray = new ZapierCrawlInitiatedAlert[2];
		ZapierCrawlInitiatedAlert zapierCrawlInitiatedAlert = null;

		// first alert
		zapierCrawlInitiatedAlert = new ZapierCrawlInitiatedAlert();
		zapierCrawlInitiatedAlert.setId("1614180689836");
		zapierCrawlInitiatedAlert.setDomain_id(123456);
		zapierCrawlInitiatedAlert.setDomain_name("www.testdomain1.com");
		zapierCrawlInitiatedAlert.setRequest_id(234567);
		zapierCrawlInitiatedAlert.setCrawl_start_timestamp("2021-02-24 09:28:03");
		zapierCrawlInitiatedAlert.setRequester_email("<EMAIL>");
		zapierCrawlInitiatedAlert.setProject("project name 1");
		zapierCrawlInitiatedAlert.setLanguage("English");
		zapierCrawlInitiatedAlert.setWhat_to_crawl("URL");
		zapierCrawlInitiatedAlert.setStarting_url("https://www.testdomain1.com");
		zapierCrawlInitiatedAlert.setCrawl_type("non-Javascript");
		zapierCrawlInitiatedAlert.setCrawl_speed(3);
		zapierCrawlInitiatedAlert.setCrawl_depth(8);
		zapierCrawlInitiatedAlert.setDescription("crawl description 1");
		zapierCrawlInitiatedAlert.setUser_agent("user agent 1");
		zapierCrawlInitiatedAlert.setObey_robots_txt(true);
		zapierCrawlInitiatedAlert.setStore_blocked_links("store blocked links 1");
		zapierCrawlInitiatedAlert.setEnable_cookies(true);
		zapierCrawlInitiatedAlert.setRegion("New York 3");
		zapierCrawlInitiatedAlert.setUrl_parameters_to_remove("url parameters to remove 1");
		zapierCrawlInitiatedAlert.setInternal_links_analysis(true);
		zapierCrawlInitiatedAlert.setHreflang_crawl(true);
		zapierCrawlInitiatedAlert.setCanonical_crawl(false);
		zapierCrawlInitiatedAlert.setAllow_domains("allow domains 1");
		zapierCrawlInitiatedAlert.setDeny_domains("deny domains 1");
		zapierCrawlInitiatedAlert.setFollow_nofollow_links(true);
		zapierCrawlInitiatedAlert.setUrl_patterns_to_allow("url patterns to allow 1");
		zapierCrawlInitiatedAlert.setUrl_patterns_to_disallow("url patterns to disallow 1");
		zapierCrawlInitiatedAlert.setUrls_to_crawl_but_not_index("urls to crawl but not index 1");
		zapierCrawlInitiatedAlert.setUrls_to_index_but_not_crawl("urls to index but not crawl 1");
		zapierCrawlInitiatedAlert.setRestrict_to_xpath("restrict to xpath 1");
		zapierCrawlInitiatedAlert.setRestrict_to_css("restrict to css 1");
		zapierCrawlInitiatedAlert.setAdditional_content("additional content 1");
		zapierCrawlInitiatedAlert.setCustom_search("custom search 1");
		zapierCrawlInitiatedAlert.setError_code("");
		zapierCrawlInitiatedAlert.setError_message("");
		zapierCrawlInitiatedAlertArray[0] = zapierCrawlInitiatedAlert;

		// second alert
		zapierCrawlInitiatedAlert = new ZapierCrawlInitiatedAlert();
		zapierCrawlInitiatedAlert.setId("1614234567898");
		zapierCrawlInitiatedAlert.setDomain_id(345678);
		zapierCrawlInitiatedAlert.setDomain_name("www.testdomain2.com");
		zapierCrawlInitiatedAlert.setRequest_id(567890);
		zapierCrawlInitiatedAlert.setCrawl_start_timestamp("2021-02-25 16:33:08");
		zapierCrawlInitiatedAlert.setRequester_email("<EMAIL>");
		zapierCrawlInitiatedAlert.setProject("project name 2");
		zapierCrawlInitiatedAlert.setLanguage("French");
		zapierCrawlInitiatedAlert.setWhat_to_crawl("URL");
		zapierCrawlInitiatedAlert.setStarting_url("https://www.testdomain2.com");
		zapierCrawlInitiatedAlert.setCrawl_type("Javascript");
		zapierCrawlInitiatedAlert.setCrawl_speed(2);
		zapierCrawlInitiatedAlert.setCrawl_depth(6);
		zapierCrawlInitiatedAlert.setDescription("crawl description 2");
		zapierCrawlInitiatedAlert.setUser_agent("user agent 2");
		zapierCrawlInitiatedAlert.setObey_robots_txt(false);
		zapierCrawlInitiatedAlert.setStore_blocked_links("store blocked links 2");
		zapierCrawlInitiatedAlert.setEnable_cookies(false);
		zapierCrawlInitiatedAlert.setRegion("London 1");
		zapierCrawlInitiatedAlert.setUrl_parameters_to_remove("url parameters to remove 2");
		zapierCrawlInitiatedAlert.setInternal_links_analysis(false);
		zapierCrawlInitiatedAlert.setHreflang_crawl(false);
		zapierCrawlInitiatedAlert.setCanonical_crawl(true);
		zapierCrawlInitiatedAlert.setAllow_domains("allow domains 2");
		zapierCrawlInitiatedAlert.setDeny_domains("deny domains 2");
		zapierCrawlInitiatedAlert.setFollow_nofollow_links(false);
		zapierCrawlInitiatedAlert.setUrl_patterns_to_allow("url patterns to allow 2");
		zapierCrawlInitiatedAlert.setUrl_patterns_to_disallow("url patterns to disallow 2");
		zapierCrawlInitiatedAlert.setUrls_to_crawl_but_not_index("urls to crawl but not index 2");
		zapierCrawlInitiatedAlert.setUrls_to_index_but_not_crawl("urls to index but not crawl 2");
		zapierCrawlInitiatedAlert.setRestrict_to_xpath("restrict to xpath 2");
		zapierCrawlInitiatedAlert.setRestrict_to_css("restrict to css 2");
		zapierCrawlInitiatedAlert.setAdditional_content("additional content 2");
		zapierCrawlInitiatedAlert.setCustom_search("custom search 2");
		zapierCrawlInitiatedAlert.setError_code("");
		zapierCrawlInitiatedAlert.setError_message("");
		zapierCrawlInitiatedAlertArray[1] = zapierCrawlInitiatedAlert;

		return zapierCrawlInitiatedAlertArray;
	}

	public ZapierCrawlCompletedAlert[] getSampleZapierCrawlCompletedAlerts() throws Exception {

		ZapierCrawlCompletedAlert[] zapierCrawlCompletedAlertArray = new ZapierCrawlCompletedAlert[2];
		ZapierCrawlCompletedAlert zapierCrawlCompletedAlert = null;

		// first alert
		zapierCrawlCompletedAlert = new ZapierCrawlCompletedAlert();
		zapierCrawlCompletedAlert.setId("1614180689836");
		zapierCrawlCompletedAlert.setDomain_id(123456);
		zapierCrawlCompletedAlert.setDomain_name("www.testdomain1.com");
		zapierCrawlCompletedAlert.setRequest_id(234567);
		zapierCrawlCompletedAlert.setCrawl_start_timestamp("2021-02-24 09:28:03");
		zapierCrawlCompletedAlert.setRequester_email("<EMAIL>");
		zapierCrawlCompletedAlert.setProject("project name 1");
		zapierCrawlCompletedAlert.setLanguage("English");
		zapierCrawlCompletedAlert.setWhat_to_crawl("URL");
		zapierCrawlCompletedAlert.setStarting_url("https://www.testdomain1.com");
		zapierCrawlCompletedAlert.setCrawl_type("non-Javascript");
		zapierCrawlCompletedAlert.setCrawl_speed(3);
		zapierCrawlCompletedAlert.setCrawl_depth(8);
		zapierCrawlCompletedAlert.setCrawl_starting_url_status_code("120");
		zapierCrawlCompletedAlert.setCrawl_starting_url_status_message("Starting URL blocked by Robots.txt instructions : https://www.plusmore.com");
		zapierCrawlCompletedAlert.setTotal_pages_crawled(0);
		zapierCrawlCompletedAlert.setError_code("");
		zapierCrawlCompletedAlert.setError_message("");
		zapierCrawlCompletedAlertArray[0] = zapierCrawlCompletedAlert;

		// second alert
		zapierCrawlCompletedAlert = new ZapierCrawlCompletedAlert();
		zapierCrawlCompletedAlert.setId("1614234567898");
		zapierCrawlCompletedAlert.setDomain_id(345678);
		zapierCrawlCompletedAlert.setDomain_name("www.testdomain2.com");
		zapierCrawlCompletedAlert.setRequest_id(567890);
		zapierCrawlCompletedAlert.setCrawl_start_timestamp("2021-02-25 16:33:08");
		zapierCrawlCompletedAlert.setRequester_email("<EMAIL>");
		zapierCrawlCompletedAlert.setProject("project name 2");
		zapierCrawlCompletedAlert.setLanguage("French");
		zapierCrawlCompletedAlert.setWhat_to_crawl("URL");
		zapierCrawlCompletedAlert.setStarting_url("https://www.testdomain2.com");
		zapierCrawlCompletedAlert.setCrawl_type("Javascript");
		zapierCrawlCompletedAlert.setCrawl_speed(2);
		zapierCrawlCompletedAlert.setCrawl_depth(6);
		zapierCrawlCompletedAlert.setCrawl_starting_url_status_code("");
		zapierCrawlCompletedAlert.setCrawl_starting_url_status_message("");
		zapierCrawlCompletedAlert.setTotal_pages_crawled(168);
		zapierCrawlCompletedAlert.setError_code("");
		zapierCrawlCompletedAlert.setError_message("");
		zapierCrawlCompletedAlertArray[1] = zapierCrawlCompletedAlert;

		return zapierCrawlCompletedAlertArray;
	}

	public Integer getPageTagAlertPageTagId(int domainId, String pageTagName) {
		Integer pageTagId = null;
		List<PageTagAlertConfigEntity> pageTagAlertConfigEntityList = null;
		String[] pageTagIdStringArray = null;
		int pageTagIdToBeTested = 0;

		// retrieve page tag ID
		GroupTagEntity groupTagEntity = groupTagEntityDAO.getGroupTagEntity(domainId, pageTagName, GroupTagEntity.TAG_TYPE_TARGET_URL);
		if (groupTagEntity != null) {
			// check if page tag ID requires page tag content alert
			pageTagAlertConfigEntityList = pageTagAlertConfigDAO.get(domainId);
			if (pageTagAlertConfigEntityList != null && pageTagAlertConfigEntityList.size() > 0) {
				nextPageTagAlertConfigEntity: for (PageTagAlertConfigEntity pageTagAlertConfigEntity : pageTagAlertConfigEntityList) {
					pageTagIdStringArray = pageTagAlertConfigEntity.getTagContentChangeTagIds().split(IConstants.COMMA);
					if (pageTagIdStringArray != null && pageTagIdStringArray.length > 0) {
						for (String pageTagIdString : pageTagIdStringArray) {
							pageTagIdToBeTested = NumberUtils.toInt(pageTagIdString);
							if (groupTagEntity.getId().intValue() == pageTagIdToBeTested) {
								pageTagId = groupTagEntity.getId();
								break nextPageTagAlertConfigEntity;
							}
						}
					}
				}
			}
		}
		return pageTagId;
	}

	public ZapierPageTagContentAlert[] getSampleZapierPageTagContentAlerts() throws Exception {

		ZapierPageTagContentAlert[] zapierPageTagContentAlertArray = new ZapierPageTagContentAlert[2];
		ZapierPageTagContentAlert zapierPageTagContentAlert = null;

		// first alert
		zapierPageTagContentAlert = new ZapierPageTagContentAlert();
		zapierPageTagContentAlert.setId("1614180689836");
		zapierPageTagContentAlert.setAlert_timestamp("2021-03-12 12:26:13");
		zapierPageTagContentAlert.setDomain_id(123456);
		zapierPageTagContentAlert.setDomain_name("www.testdomain1.com");
		zapierPageTagContentAlert.setPage_tag_id(234567);
		zapierPageTagContentAlert.setPage_tag_name("page tag name 1");
		zapierPageTagContentAlert.setContent_change_date("2021-03-11");
		zapierPageTagContentAlert.setContent_change_threshold("1 character");
		zapierPageTagContentAlert.setPage_tag_level_change_threshold("2 percents");
		zapierPageTagContentAlert.setTotal_urls_changed(8);
		zapierPageTagContentAlert.setTotal_urls_in_tag(168);
		zapierPageTagContentAlert.setPercent_of_urls_changed("4.76");
		zapierPageTagContentAlert.setChanges("6 URLs with 'Title' changes, 5 URLs with 'Meta Desc' changes");
		zapierPageTagContentAlert.setError_code("");
		zapierPageTagContentAlert.setError_message("");
		zapierPageTagContentAlertArray[0] = zapierPageTagContentAlert;

		// second alert
		zapierPageTagContentAlert = new ZapierPageTagContentAlert();
		zapierPageTagContentAlert.setId("1614180689837");
		zapierPageTagContentAlert.setAlert_timestamp("2021-03-13 23:28:08");
		zapierPageTagContentAlert.setDomain_id(345678);
		zapierPageTagContentAlert.setDomain_name("www.testdomain2.com");
		zapierPageTagContentAlert.setPage_tag_id(567890);
		zapierPageTagContentAlert.setPage_tag_name("page tag name 2");
		zapierPageTagContentAlert.setContent_change_date("2021-03-12");
		zapierPageTagContentAlert.setContent_change_threshold("3 characters");
		zapierPageTagContentAlert.setPage_tag_level_change_threshold("18 percents");
		zapierPageTagContentAlert.setTotal_urls_changed(168);
		zapierPageTagContentAlert.setTotal_urls_in_tag(368);
		zapierPageTagContentAlert.setPercent_of_urls_changed("45.65");
		zapierPageTagContentAlert.setChanges("160 URLs with '404 to 200' changes, 18 URLs with 'Canonical' changes");
		zapierPageTagContentAlert.setError_code("");
		zapierPageTagContentAlert.setError_message("");
		zapierPageTagContentAlertArray[1] = zapierPageTagContentAlert;

		return zapierPageTagContentAlertArray;
	}

	public Integer getTargetUrlChangePageTagId(int domainId, String pageTagName) {
		Integer pageTagId = null;
		List<TargetUrlChangeAlertEntity> targetUrlChangeAlertEntityList = null;
		Integer[] pageTagIdArray = null;

		// retrieve page tag ID
		GroupTagEntity groupTagEntity = groupTagEntityDAO.getGroupTagEntity(domainId, pageTagName, GroupTagEntity.TAG_TYPE_TARGET_URL);
		if (groupTagEntity != null) {
			// check if page tag ID requires target URL change alert
			targetUrlChangeAlertEntityList = targetUrlChangeAlertDAO.getListByDomain(domainId);
			if (targetUrlChangeAlertEntityList != null && targetUrlChangeAlertEntityList.size() > 0) {
				nextTargetUrlChangeAlertEntity: for (TargetUrlChangeAlertEntity targetUrlChangeAlertEntity : targetUrlChangeAlertEntityList) {
					if (StringUtils.isNotBlank(targetUrlChangeAlertEntity.getPageTagIds())) {
						pageTagIdArray = new Gson().fromJson(targetUrlChangeAlertEntity.getPageTagIds(), Integer[].class);
						if (pageTagIdArray != null && pageTagIdArray.length > 0) {
							for (Integer pageTagIdToBeTested : pageTagIdArray) {
								if (groupTagEntity.getId().intValue() == pageTagIdToBeTested.intValue()) {
									pageTagId = groupTagEntity.getId().intValue();
									break nextTargetUrlChangeAlertEntity;
								}
							}
						}
					}
				}
			}
		}
		return pageTagId;
	}

	public ZapierResourceResponse createTargetUrlChangeAlertSubscription(int domainId, int userId, Integer pageTagId, String callbackUrl) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("createTargetUrlChangeAlertSubscription() begins. domainId=" + domainId + ",userId=" + userId + ",pageTagId="
				+ pageTagId + ",callbackUrl=" + callbackUrl);

		ZapierResourceResponse zapierResourceResponse = new ZapierResourceResponse();
		Long id = null;
		String subType = null;
		String subTypeHashCd = null;

		if (pageTagId != null) {
			subType = String.valueOf(pageTagId);
			subTypeHashCd = CrawlerUtils.getInstance().getMd5HashCode(subType);
		}

		ZapierWebhookEntity zapierWebhookEntity = zapierWebhookDAO.get(domainId, IConstants.ZAPIER_TRIGGER_TYPE_TARGET_URL_CHANGE_ALERT, userId, subTypeHashCd);
		if (zapierWebhookEntity != null) {
			zapierResourceResponse.setError_code(IConstants.MSG_CD_ZAPIER_SUBSCRIPTION_ALREADY_EXIST);
			zapierResourceResponse.setError_message(getErrorMessage(IConstants.MSG_CD_ZAPIER_SUBSCRIPTION_ALREADY_EXIST, null));
		} else {
			zapierWebhookEntity = new ZapierWebhookEntity();
			zapierWebhookEntity.setDomainId(domainId);
			zapierWebhookEntity.setUserId(userId);
			zapierWebhookEntity.setTriggerType(IConstants.ZAPIER_TRIGGER_TYPE_TARGET_URL_CHANGE_ALERT);
			if (pageTagId != null) {
				zapierWebhookEntity.setSubTypeHashCd(subTypeHashCd);
				zapierWebhookEntity.setSubType(subType);
			}
			zapierWebhookEntity.setCallbackUrl(callbackUrl);
			zapierWebhookEntity.setCreateDate(new Date());
			id = zapierWebhookDAO.create(zapierWebhookEntity);
			if (id != null) {
				zapierResourceResponse.setId(id);
			} else {
				zapierResourceResponse.setError_code(IConstants.MSG_CD_ZAPIER_SUBSCRIPTION_CANNOT_BE_CREATED);
				zapierResourceResponse.setError_message(getErrorMessage(IConstants.MSG_CD_ZAPIER_SUBSCRIPTION_CANNOT_BE_CREATED, null));
			}
		}

		FormatUtils.getInstance().logMemoryUsage("createTargetUrlChangeAlertSubscription() ends. domainId=" + domainId + ",userId=" + userId + ",pageTagId=" + pageTagId
				+ ",callbackUrl=" + callbackUrl);

		return zapierResourceResponse;
	}

	public ZapierTargetUrlChangeAlert[] getSampleZapierTargetUrlChangeAlerts() throws Exception {

		ZapierTargetUrlChangeAlert[] zapierTargetUrlChangeAlertArray = new ZapierTargetUrlChangeAlert[2];
		ZapierTargetUrlChangeAlert zapierTargetUrlChangeAlert = null;

		// first alert
		zapierTargetUrlChangeAlert = new ZapierTargetUrlChangeAlert();
		zapierTargetUrlChangeAlert.setId("1614180689838");
		zapierTargetUrlChangeAlert.setDomain_id(123456);
		zapierTargetUrlChangeAlert.setDomain_name("www.testdomain1.com");
		zapierTargetUrlChangeAlert.setPage_tag_id(234567);
		zapierTargetUrlChangeAlert.setPage_tag_name("page tag name 1");
		zapierTargetUrlChangeAlert.setMessage("message 1");
		zapierTargetUrlChangeAlert.setChange_description("change description 1");
		zapierTargetUrlChangeAlert.setTotal_urls(1);
		zapierTargetUrlChangeAlert.setError_code("");
		zapierTargetUrlChangeAlert.setError_message("");
		zapierTargetUrlChangeAlertArray[0] = zapierTargetUrlChangeAlert;

		// second alert
		zapierTargetUrlChangeAlert = new ZapierTargetUrlChangeAlert();
		zapierTargetUrlChangeAlert.setId("1614180689839");
		zapierTargetUrlChangeAlert.setDomain_id(345678);
		zapierTargetUrlChangeAlert.setDomain_name("www.testdomain2.com");
		zapierTargetUrlChangeAlert.setPage_tag_id(567890);
		zapierTargetUrlChangeAlert.setPage_tag_name("page tag name 2");
		zapierTargetUrlChangeAlert.setMessage("message 2");
		zapierTargetUrlChangeAlert.setChange_description("change description 2");
		zapierTargetUrlChangeAlert.setTotal_urls(2);
		zapierTargetUrlChangeAlert.setError_code("");
		zapierTargetUrlChangeAlert.setError_message("");
		zapierTargetUrlChangeAlertArray[1] = zapierTargetUrlChangeAlert;

		return zapierTargetUrlChangeAlertArray;
	}
}
