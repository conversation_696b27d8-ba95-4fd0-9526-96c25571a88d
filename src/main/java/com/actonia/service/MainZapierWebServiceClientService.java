package com.actonia.service;

import java.util.Properties;

import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.StatusLine;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;

import com.actonia.IConstants;
import com.actonia.exception.ZapierException;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.HttpUtils;
import com.actonia.value.object.ZapierContentGuardAlert;
import com.actonia.value.object.ZapierCrawlCompletedAlert;
import com.actonia.value.object.ZapierCrawlInitiatedAlert;
import com.actonia.value.object.ZapierPageTagContentAlert;
import com.actonia.value.object.ZapierResourceResponse;
import com.google.gson.Gson;

public class MainZapierWebServiceClientService {

	//private boolean isDebug = false;
	private static String mainWebServiceEndPoint = null;
	public static final String ROUTER_ZAPIER = "/zapier/";

	public MainZapierWebServiceClientService() {
		super();
	}

	public String getMainWebServiceEndPoint() {
		if (StringUtils.isBlank(mainWebServiceEndPoint)) {
			try {
				Properties properties = new Properties();
				properties.load(MainWebServiceClientService.class.getResourceAsStream("/main_web_service.properties"));

				// polite.crawl.web.service.endpoint
				mainWebServiceEndPoint = properties.getProperty(IConstants.RUNTIME_PROPERTY_NAME_MAIN_WEB_SERVICE_ENDPOINT);
				FormatUtils.getInstance().logMemoryUsage("getMainWebServiceEndPoint() mainWebServiceEndPoint=" + mainWebServiceEndPoint);

			} catch (Exception e) {
				e.printStackTrace();
				FormatUtils.getInstance().logMemoryUsage("getMainWebServiceEndPoint() exception message=" + e.getMessage());
				System.exit(-1);
			}
		}
		return mainWebServiceEndPoint;
	}

	public ZapierResourceResponse sendHttpPostRequest(String requestUrl, String requestParameters) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("sendHttpPostRequest() requestUrl=" + requestUrl + ",requestJSON=" + requestParameters);
		boolean isSendGetRequest = false;
		ZapierResourceResponse zapierResourceResponse = null;
		String responseString = getResponseString(requestUrl, isSendGetRequest, requestParameters);
		FormatUtils.getInstance().logMemoryUsage("sendHttpPostRequest() responseString=" + responseString);
		if (StringUtils.isNotBlank(responseString)) {
			zapierResourceResponse = new Gson().fromJson(responseString, ZapierResourceResponse.class);
			if (zapierResourceResponse != null) {
				FormatUtils.getInstance()
						.logMemoryUsage("sendHttpPostRequest() responseJSON=" + new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class));
			} else {
				FormatUtils.getInstance().logMemoryUsage("sendHttpPostRequest() zapierResourceResponse is null.");
			}
		}
		return zapierResourceResponse;
	}

	public ZapierContentGuardAlert[] getZapierContentGuardAlerts(String requestUrl) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("getZapierContentGuardAlerts() requestUrl=" + requestUrl);
		boolean isSendGetRequest = true;
		String requestParameters = null;
		ZapierContentGuardAlert[] zapierContentGuardAlertArray = null;
		String responseString = getResponseString(requestUrl, isSendGetRequest, requestParameters);
		if (StringUtils.isNotBlank(responseString)) {
			zapierContentGuardAlertArray = new Gson().fromJson(responseString, ZapierContentGuardAlert[].class);
			if (zapierContentGuardAlertArray != null) {
				FormatUtils.getInstance().logMemoryUsage(
						"getZapierContentGuardAlerts() responseJSON=" + new Gson().toJson(zapierContentGuardAlertArray, ZapierContentGuardAlert[].class));
			} else {
				FormatUtils.getInstance().logMemoryUsage("getZapierContentGuardAlerts() zapierContentGuardAlertArray is null.");
			}
		}
		return zapierContentGuardAlertArray;
	}

	public ZapierCrawlInitiatedAlert[] getZapierCrawlInitiatedAlerts(String requestUrl) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("getZapierCrawlInitiatedAlerts() requestUrl=" + requestUrl);
		boolean isSendGetRequest = true;
		String requestParameters = null;
		ZapierCrawlInitiatedAlert[] zapierCrawlInitiatedAlertArray = null;
		String responseString = getResponseString(requestUrl, isSendGetRequest, requestParameters);
		if (StringUtils.isNotBlank(responseString)) {
			zapierCrawlInitiatedAlertArray = new Gson().fromJson(responseString, ZapierCrawlInitiatedAlert[].class);
			if (zapierCrawlInitiatedAlertArray != null) {
				FormatUtils.getInstance().logMemoryUsage(
						"getZapierCrawlInitiatedAlerts() responseJSON=" + new Gson().toJson(zapierCrawlInitiatedAlertArray, ZapierCrawlInitiatedAlert[].class));
			} else {
				FormatUtils.getInstance().logMemoryUsage("getZapierCrawlInitiatedAlerts() zapierCrawlInitiatedAlertArray is null.");
			}
		}
		return zapierCrawlInitiatedAlertArray;
	}

	public ZapierCrawlCompletedAlert[] getZapierCrawlCompletedAlerts(String requestUrl) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("getZapierCrawlCompletedAlerts() requestUrl=" + requestUrl);
		boolean isSendGetRequest = true;
		String requestParameters = null;
		ZapierCrawlCompletedAlert[] zapierCrawlCompletedAlertArray = null;
		String responseString = getResponseString(requestUrl, isSendGetRequest, requestParameters);
		if (StringUtils.isNotBlank(responseString)) {
			zapierCrawlCompletedAlertArray = new Gson().fromJson(responseString, ZapierCrawlCompletedAlert[].class);
			if (zapierCrawlCompletedAlertArray != null) {
				FormatUtils.getInstance().logMemoryUsage(
						"getZapierCrawlCompletedAlerts() responseJSON=" + new Gson().toJson(zapierCrawlCompletedAlertArray, ZapierCrawlCompletedAlert[].class));
			} else {
				FormatUtils.getInstance().logMemoryUsage("getZapierCrawlCompletedAlerts() zapierCrawlCompletedAlertArray is null.");
			}
		}
		return zapierCrawlCompletedAlertArray;
	}

	public ZapierPageTagContentAlert[] getZapierPageTagContentAlerts(String requestUrl) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("getZapierPageTagContentAlerts() requestUrl=" + requestUrl);
		boolean isSendGetRequest = true;
		String requestParameters = null;
		ZapierPageTagContentAlert[] zapierPageTagContentAlertArray = null;
		String responseString = getResponseString(requestUrl, isSendGetRequest, requestParameters);
		if (StringUtils.isNotBlank(responseString)) {
			zapierPageTagContentAlertArray = new Gson().fromJson(responseString, ZapierPageTagContentAlert[].class);
			if (zapierPageTagContentAlertArray != null) {
				FormatUtils.getInstance().logMemoryUsage(
						"getZapierPageTagContentAlerts() responseJSON=" + new Gson().toJson(zapierPageTagContentAlertArray, ZapierPageTagContentAlert[].class));
			} else {
				FormatUtils.getInstance().logMemoryUsage("getZapierPageTagContentAlerts() zapierPageTagContentAlertArray is null.");
			}
		}
		return zapierPageTagContentAlertArray;
	}

	public ZapierResourceResponse sendHttpDeleteRequest(String requestUrl) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("sendHttpDeleteRequest() requestUrl=" + requestUrl);
		ZapierResourceResponse zapierResourceResponse = null;
		String responseString = getDeleteResponseString(requestUrl);
		if (StringUtils.isNotBlank(responseString)) {
			zapierResourceResponse = new Gson().fromJson(responseString, ZapierResourceResponse.class);
			if (zapierResourceResponse != null) {
				FormatUtils.getInstance()
						.logMemoryUsage("sendHttpDeleteRequest() responseJSON=" + new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class));
			} else {
				FormatUtils.getInstance().logMemoryUsage("sendHttpDeleteRequest() zapierResourceResponse is null.");
			}
		}
		return zapierResourceResponse;
	}

	public String getResponseString(String requestUrl, boolean isSendGetRequest, String requestParameters) throws Exception {
		String responseString = null;
		StatusLine statusLine = null;
		int httpStatusCode = 0;
		String httpReasonPhrase = null;
		CloseableHttpClient httpClient = null;
		HttpGet httpGet = null;
		HttpPost httpPost = null;
		CloseableHttpResponse httpResponse = null;
		int retryCount = 0;
		nextRetry: while (retryCount < IConstants.MAX_RETRY_COUNT) {
			try {
				httpClient = HttpUtils.getInstance().getHttpClient();

				// when sending HTTP GET request.....
				if (isSendGetRequest == true) {

					httpGet = HttpUtils.getInstance().getHttpGet(requestUrl);

					// when httpGet object cannot be instantiated due to "IllegalArgumentException" 
					if (httpGet == null) {
						FormatUtils.getInstance()
								.logMemoryUsage("getResponseString() httpGet cannot be instantiated assigning HTTP status code 999, requestUrl=" + requestUrl);
						throw new ZapierException(902, "httpGet cannot be instantiated");
					}

					httpResponse = httpClient.execute(httpGet);
				}
				// when sending HTTP POST request.....
				else {
					httpPost = HttpUtils.getInstance().getHttpPost(requestUrl);
					httpPost.setEntity(new StringEntity(requestParameters, IConstants.UTF_8));
					httpPost.setHeader(IConstants.CACHE_CONTROL, IConstants.NO_CACHE);
					httpPost.setHeader(IConstants.CONTENT_DASH_TYPE, IConstants.APPLICATION_SLASH_JSON);
					httpResponse = httpClient.execute(httpPost);
				}
				if (httpResponse != null) {

					// HTTP status code
					statusLine = httpResponse.getStatusLine();
					if (statusLine != null) {
						httpStatusCode = statusLine.getStatusCode();
						httpReasonPhrase = statusLine.getReasonPhrase();

						// HTTP content
						if (httpStatusCode == 200) {
							responseString = getHttpResponseAsString(httpResponse);
						} else {
							FormatUtils.getInstance().logMemoryUsage(
									"getResponseString() httpStatusCode=" + httpStatusCode + ",httpReasonPhrase=" + httpReasonPhrase + ", requestUrl=" + requestUrl);
							throw new ZapierException(httpStatusCode, httpReasonPhrase);
						}
					}
				}
				retryCount = IConstants.MAX_RETRY_COUNT;
				break nextRetry;
			} catch (Exception e) {
				throw e;
			} finally {
				if (httpResponse != null) {
					try {
						httpResponse.close();
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
				if (httpClient != null) {
					try {
						httpClient.close();
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			}
		}
		return responseString;
	}

	private String getDeleteResponseString(String requestUrl) throws Exception {
		//long startTimestamp = System.currentTimeMillis();
		String responseString = null;
		StatusLine statusLine = null;
		int httpStatusCode = 0;
		String httpReasonPhrase = null;
		CloseableHttpClient httpClient = null;
		HttpDelete httpDelete = null;
		CloseableHttpResponse httpResponse = null;
		int retryCount = 0;
		nextRetry: while (retryCount < IConstants.MAX_RETRY_COUNT) {
			try {
				httpClient = HttpUtils.getInstance().getHttpClient();
				httpDelete = HttpUtils.getInstance().getHttpDelete(requestUrl);
				httpResponse = httpClient.execute(httpDelete);
				if (httpResponse != null) {

					// HTTP status code
					statusLine = httpResponse.getStatusLine();
					if (statusLine != null) {
						httpStatusCode = statusLine.getStatusCode();
						httpReasonPhrase = statusLine.getReasonPhrase();

						// HTTP content
						if (httpStatusCode == 200) {
							responseString = getHttpResponseAsString(httpResponse);
						} else {
							FormatUtils.getInstance().logMemoryUsage("getDeleteResponseString() httpStatusCode=" + httpStatusCode + ",httpReasonPhrase="
									+ httpReasonPhrase + ", requestUrl=" + requestUrl);
							throw new ZapierException(httpStatusCode, httpReasonPhrase);
						}
					}
				}
				retryCount = IConstants.MAX_RETRY_COUNT;
				break nextRetry;
			} catch (Exception e) {
				throw e;
			} finally {
				if (httpResponse != null) {
					try {
						httpResponse.close();
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
				if (httpClient != null) {
					try {
						httpClient.close();
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			}
		}
		return responseString;
	}

	private String getHttpResponseAsString(HttpResponse httpResponse) {
		String responseString = null;
		HttpEntity httpEntity = null;
		try {
			if (httpResponse != null) {
				httpEntity = httpResponse.getEntity();
				if (httpEntity != null) {
					responseString = EntityUtils.toString(httpEntity, IConstants.UTF_8);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return responseString;
	}
}
