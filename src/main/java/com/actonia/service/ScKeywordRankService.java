package com.actonia.service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

import com.actonia.dao.EngineCountryLanguageMappingEntityDAO;
import com.actonia.dao.OwndomainEngineRelDAO;
import com.actonia.entity.EngineCountryLanguageMappingEntity;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.entity.OwndomainEngineRel;
import com.actonia.utils.SpringBeanFactory;

public class ScKeywordRankService {

	//Cee - https://www.wrike.com/open.htm?id=148854433 
	//https://www.wrike.com/open.htm?id=40600752
	//by cee
	//Leo - https://www.wrike.com/open.htm?id=101208739
	public enum ScEngine {
		<PERSON>O(170), <PERSON><PERSON><PERSON>(160), <PERSON><PERSON>(255), <PERSON><PERSON><PERSON><PERSON><PERSON>(100), <PERSON><PERSON><PERSON>(150), <PERSON><PERSON><PERSON><PERSON>(120), <PERSON>O<PERSON><PERSON><PERSON><PERSON><PERSON>(999), <PERSON><PERSON>G<PERSON>(1), DEFAULT(0), GoogleImage(888), BAIDUMOBILE(155), APPLE(
				110), GOOGLE_RU(39), GOOGLE_JOB(119), GOOGLE_IMAGE(236), GOOGLE_SHOPPING(237), NAVER_MOBILE(165), //Cee - https://www.wrike.com/open.htm?id=302629076
		YAHOO_MOBILE(105), //Cee - https://www.wrike.com/open.htm?id=397504741
		YANDEX_MOBILE(125), //Cee - https://www.wrike.com/open.htm?id=397504741
		BING_MOBILE(195); //Cee - https://www.wrike.com/open.htm?id=321303116

		private final int value;

		ScEngine(int value) {
			this.value = value;
		}

		public int getValue() {
			return value;
		}

		public static ScEngine findByEngineId(int engineId, ScEngine defaultEngine) {
			for (ScEngine scEngine : ScEngine.values()) {
				if (engineId == scEngine.getValue()) {
					return scEngine;
				}
			}
			return defaultEngine;
		}

		//Cee - https://www.wrike.com/open.htm?id=285295508
		public static ScEngine findByEngineName(String engineName, ScEngine defaultEngine) {
			for (ScEngine scEngine : ScEngine.values()) {
				if (StringUtils.equalsIgnoreCase(engineName, scEngine.name())) {
					return scEngine;
				}
			}
			return defaultEngine;
		}

		//Cee - https://www.wrike.com/open.htm?id=353400734
		public static ScEngine getMobileEngineByPrimaryEngine(int primaryEngineId) {
			if (BAIDU.getValue() == primaryEngineId) {
				return BAIDUMOBILE;
			}
			if (BING.getValue() == primaryEngineId) {
				return BING_MOBILE;
			}
			if (NAVER.getValue() == primaryEngineId) {
				return NAVER_MOBILE;
			}

			//Cee - https://www.wrike.com/open.htm?id=397504741
			if (YAHOO.getValue() == primaryEngineId) {
				return YAHOO_MOBILE;
			}
			if (YANDEX.getValue() == primaryEngineId) {
				return YANDEX_MOBILE;
			}

			return GOOGLEMOBILE;
		}

		//Cee - 
		public static String getMobileEngineQueryName(ScEngine engine, String defaultName) {
			if (engine == null) {
				return defaultName;
			}
			if (engine == BAIDUMOBILE) {
				return "m.baidu.com";
			}
			if (engine == BING_MOBILE) {
				return SEARCH_ENGINE_BING_MOBILE_NAME;
			}
			if (engine == NAVER_MOBILE) {
				return "naver.com";
			}
			if (engine == YAHOO_MOBILE) {
				return "yahoo.co.jp";
			}
			if (engine == YANDEX_MOBILE) {
				return "yandex.ru";
			}
			if (engine == GOOGLEMOBILE) {
				return "m.google.com";
			}
			return defaultName;
		}

		//Cee - https://www.wrike.com/open.htm?id=397504741
		//DB table : virtual_search_engine_rel
		public static boolean isMobile(Integer engineId, boolean includeGoogleMobile) {
			ScEngine eng = mobileToDesktop(engineId, includeGoogleMobile);
			if (eng != null) {
				return true;
			}
			return false;
		}

		//Cee - https://www.wrike.com/open.htm?id=397504741
		//DB table : virtual_search_engine_rel
		public static Integer mobileToDesktop(Integer engineId, Integer defaultEngineId) {
			ScEngine desktop = mobileToDesktop(engineId, true);
			if (desktop == null) {
				return defaultEngineId;
			}
			if (desktop == GOOGLE) {
				return defaultEngineId;
			}
			return desktop.getValue();
		}

		public static ScEngine mobileToDesktop(Integer engineId, boolean includeGoogleMobile) {
			if (engineId == null) {
				return null;
			}
			if (engineId.intValue() == NAVER_MOBILE.getValue()) {
				return NAVER;
			}
			if (engineId.intValue() == YAHOO_MOBILE.getValue()) {
				return YAHOO;
			}
			if (engineId.intValue() == YANDEX_MOBILE.getValue()) {
				return YANDEX;
			}
			if (engineId.intValue() == BING_MOBILE.getValue()) {
				return BING;
			}
			if (engineId.intValue() == BAIDUMOBILE.getValue()) {
				return BAIDU;
			}
			if (includeGoogleMobile) {
				if (engineId.intValue() == GOOGLEMOBILE.getValue()) {
					return GOOGLE;
				}
			}

			return null;
		}

		public static ScEngine toMobileEngine(String engineName, boolean includeGoogleMobile) {
			if (StringUtils.startsWith(engineName, "m.") || StringUtils.containsIgnoreCase(engineName, "Mobile")) {

				if (StringUtils.contains(engineName, "baidu")) {
					return BAIDUMOBILE;
				}
				if (StringUtils.contains(engineName, "naver")) {
					return NAVER_MOBILE;
				}
				if (StringUtils.contains(engineName, "yahoo")) {
					return YAHOO_MOBILE;
				}
				if (StringUtils.contains(engineName, "yandex")) {
					return YANDEX_MOBILE;
				}
				if (StringUtils.contains(engineName, "bing")) {
					return BING_MOBILE;
				}
				if (includeGoogleMobile) {
					if (StringUtils.contains(engineName, "google")) {
						return GOOGLEMOBILE;
					}
				}
			}
			return null;
		}

	}

	public static final int SEARCH_ENGINE_BING = 255;

	public static final int SEARCH_ENGINE_YAHOO = 100;

	//Cee - https://www.wrike.com/open.htm?id=169503186
	public static final int SEARCH_ENGINE_YANDEX = 120;
	//	public static final int SEARCH_ENGINE_YANDEX = 39;

	//Cee - https://www.wrike.com/open.htm?id=168394969
	public static final int SEARCH_ENGINE_BAIDU = 150;

	//Cee - https://www.wrike.com/open.htm?id=185570978
	public static final int SEARCH_ENGINE_BAIDU_MOBILE = 155;

	//Leo - https://www.wrike.com/open.htm?id=101208739
	public static final int SEARCH_ENGINE_SO = 170;

	//scott - https://www.wrike.com/open.htm?id=150977329
	public static final int SEARCH_ENGINE_GOOGLE_IMAGE_MOBILE = 888;

	public static final int SEARCH_ENGINE_NAVER = 160;

	public static final int SEARCH_ENGINE_GOOGLEMOBILE = 999;

	public static final String COUNTRY_US = "US";

	public static final String LANGUAGE_ENGLISH = "en";

	public static final String SEARCH_ENGINE = "Google";

	public static final String SEARCH_ENGINE_BING_NAME = "Bing";
	public static final String SEARCH_ENGINE_BING_MOBILE_NAME = "m.bing.com";

	//Cee - https://www.wrike.com/open.htm?id=397504741
	public static final String SEARCH_ENGINE_YAHOO_MOBILE_NAME = "m.yahoo.co.jp";
	public static final String SEARCH_ENGINE_YANDEX_MOBILE_NAME = "m.yandex.ru";

	public static final String SEARCH_ENGINE_BAIDU_NAME = "Baidu";

	//Cee - https://www.wrike.com/open.htm?id=185570978
	public static final String SEARCH_ENGINE_BAIDU_NAME_MOBILE = "Baidu Mobile";

	public static final String SEARCH_ENGINE_YAHOO_NAME = "Yahoo";

	public static final String SEARCH_ENGINE_NAVER_NAME = "Naver";
	public static final String SEARCH_ENGINE_NAVER_MOBILE_NAME = "m.search.naver.com";

	public static final String SEARCH_ENGINE_GOOGLEMOBILE_NAME = "Google Mobile";

	public static final String SEARCH_ENGINE_YANDEX_NAME = "Yandex";

	//Cee - https://www.wrike.com/open.htm?id=230201764
	public static final String SEARCH_ENGINE_APPLE_NAME = "Apple";

	//Leo - https://www.wrike.com/open.htm?id=101208739
	public static final String SEARCH_ENGINE_SO_NAME = "360 search";

	//scott - https://www.wrike.com/open.htm?id=150977329
	public static final String SEARCH_ENGINE_GOOGLE_IMAGE_MOBILE_NAME = "Google Image Mobile";

	//Leo - https://www.wrike.com/open.htm?id=83080155
	private static final String PERIOD_TYPE = "";

	//by cee - mongodaily4 contain last 10 days data
	//Leo - https://www.wrike.com/open.htm?id=79961507
	//	public static final int MONGO_DAILY4_MOBILE_STORE_DAYS = -9;
	//	public static final int MONGO_DAILY4_STORE_DAYS = -14;

	//Cee - Alps said mongoDaily6/7 broken, then only last 2 days available
	public static final int MONGO_DAILY4_MOBILE_STORE_DAYS = -10;
	public static final int MONGO_DAILY4_STORE_DAYS = -10;

	//Leo - https://www.wrike.com/open.htm?id=83080155
	private static enum DATE_RANGE_TYPE {
		DATA_RANGE, DATA_LIST
	}

	public static final int BATCH_PROCESS_COUNT = 200;
	private static String KEY_SPLIT = "!_!";
	public static int ENGINE_LANGUAGE_ID_NOT_FOUND = -1;
	private static int DOMAIN_ID_NO = 0;

	private static EngineCountryLanguageMappingEntityDAO engineCountryLanguageMappingEntityDAO;
	private static Map<String, EngineCountryLanguageMappingEntity> engineCountryLanguageMap;
	private static OwndomainEngineRelDAO owndomainEngineRelDAO;
	static {
		engineCountryLanguageMappingEntityDAO = SpringBeanFactory.getBean("engineCountryLanguageMappingEntityDAO");
		retrieveEngineCountryLanguageMappings(false);
		owndomainEngineRelDAO = SpringBeanFactory.getBean("owndomainEngineRelDAO");
	}

	private static void retrieveEngineCountryLanguageMappings(boolean refreshCache) {
		if (refreshCache || engineCountryLanguageMap == null || engineCountryLanguageMap.size() == 0) {
			List<EngineCountryLanguageMappingEntity> engineCountryLanguageMappingList = engineCountryLanguageMappingEntityDAO.getAll();
			System.out.println("===RetrieveEngineCountryLanguageMap size:" + engineCountryLanguageMappingList.size() + " refreshCache:" + refreshCache);
			engineCountryLanguageMap = new HashMap<String, EngineCountryLanguageMappingEntity>();
			if (engineCountryLanguageMappingList != null && engineCountryLanguageMappingList.size() > 0) {
				for (EngineCountryLanguageMappingEntity entity : engineCountryLanguageMappingList) {
					String countryQueryName = entity.getCountryQueryName();
					String engineQueryName = entity.getEngineQueryName();
					String languageQueryName = entity.getLanguageQueryName();
					int rankFrom = entity.getRankFrom().intValue();
					StringBuffer sb = new StringBuffer();
					sb.append(countryQueryName.toLowerCase()).append(KEY_SPLIT);
					sb.append(engineQueryName.toLowerCase()).append(KEY_SPLIT);
					sb.append(languageQueryName.toLowerCase()).append(KEY_SPLIT);
					sb.append(rankFrom);
					engineCountryLanguageMap.put(sb.toString(), entity);

					System.out.println("==MAP:" + entity.getId() + " \"" + countryQueryName + "\" SE:\"" + engineQueryName + "\" lang:\"" + languageQueryName
							+ "\" rFrom:" + rankFrom + " " + entity.getEngineId() + "/" + entity.getLanguageId());
				}
			}
		}
	}

	public ScKeywordRankService() {
	}

	// Wilber https://www.wrike.com/open.htm?id=220634717
	public static int getSearchEngineId(OwnDomainEntity ownDomain) {
		return getEngineLanguageId(ownDomain, true);
	}

	public static int getSearchLanguageId(OwnDomainEntity ownDomain, int... objs) {
		int languageId = getEngineLanguageId(ownDomain, false);
		if (languageId == ENGINE_LANGUAGE_ID_NOT_FOUND && objs != null && objs.length > 0) {
			return objs[0];
		}
		return languageId;
	}

	private static int getEngineLanguageId(OwnDomainEntity ownDomain, boolean isEngine) {
		if (ownDomain == null) {
			System.out.println("===ERROR: ownDomain is null.");
			return ENGINE_LANGUAGE_ID_NOT_FOUND;
		}

		// TopXKeywordRankingExportDailyExpedia OwnDomainEntity no id
		int oid = DOMAIN_ID_NO;
		if (ownDomain.getId() != null) {
			oid = ownDomain.getId().intValue();
		}

		String searchEngineCountry = ownDomain.getSearchEngineCountry();
		if (StringUtils.isBlank(searchEngineCountry)) {
			System.out.println("===ERROR: empty searchEngineCountry. OID:" + oid);
			return ENGINE_LANGUAGE_ID_NOT_FOUND;
		}

		String searchEngine = ownDomain.getSearchEngine();
		if (StringUtils.isBlank(searchEngine)) {
			System.out.println("===ERROR: empty searchEngine. OID:" + oid);
			return ENGINE_LANGUAGE_ID_NOT_FOUND;
		} else {
			searchEngine = searchEngine.toLowerCase();
			if (StringUtils.containsIgnoreCase(searchEngine, "yandex") && !StringUtils.equals(searchEngine, "yandex.ru")) {
				System.out.println("==yandex search engine " + " engineQueryName:" + searchEngine + " OID:" + oid);
				searchEngine = "yandex.ru";
				System.out.println("==yandexAppend '.ru' search engine " + " engineQueryName:" + searchEngine + " OID:" + oid);
			}
		}

		String language = ownDomain.getLanguage();
		if (StringUtils.isBlank(language)) {
			System.out.println("===ERROR: empty language. OID:" + oid);
			return ENGINE_LANGUAGE_ID_NOT_FOUND;
		} else {
			language = language.toLowerCase();
		}

		Integer rankFromObj = ownDomain.getRankFrom();
		int rankFrom = EngineCountryLanguageMappingEntity.DISABLED;
		if (rankFromObj != null) {
			rankFrom = rankFromObj.intValue();
		}

		StringBuffer sb = new StringBuffer();
		sb.append(searchEngineCountry.toLowerCase()).append(KEY_SPLIT);
		sb.append(searchEngine.toLowerCase()).append(KEY_SPLIT);
		sb.append(language.toLowerCase()).append(KEY_SPLIT);
		sb.append(rankFrom);
		String key = sb.toString();

		EngineCountryLanguageMappingEntity entity = engineCountryLanguageMap.get(key);
		if (entity == null) {
			retrieveEngineCountryLanguageMappings(true);
			entity = engineCountryLanguageMap.get(key);
		}

		return getEngineLanguageId(oid, entity, isEngine);
	}

	private static int getEngineLanguageId(int oid, EngineCountryLanguageMappingEntity mappingEntity, boolean isEngine) {
		if (mappingEntity == null) {
			System.out.println("===ERROR: EngineCountryLanguageMapping not found. OID:" + oid);
			return ENGINE_LANGUAGE_ID_NOT_FOUND; // TODO
		}

		if (isEngine) {
			return mappingEntity.getEngineId().intValue();
		} else {
			return mappingEntity.getLanguageId().intValue();
		}
	}

	public static int getSearchEngineIdForAdwords(OwnDomainEntity ownDomain) {
		// adwords only from google for now
		return 99; // default google
	}

	//Cee - https://www.wrike.com/open.htm?id=375484923
	//query from owndomain_engine_rel
	public static OwndomainEngineRel getPrimaryEngine(int ownDomainId) {
		return owndomainEngineRelDAO.getPrimaryEngine(ownDomainId);
	}

	//Cee - https://www.wrike.com/open.htm?id=375484923
	//query from owndomain_engine_rel
	public static List<Map<String, Object>> getOwndomainEngineMap(int ownDomainId, boolean enableGeoMobile, boolean enableGeoDesktop) {
		List<OwndomainEngineRel> enabledEngines = getEnabledEngines(ownDomainId);
		if (enabledEngines == null || enabledEngines.isEmpty()) {
			return Collections.EMPTY_LIST;
		}

		List<Map<String, Object>> engineMapList = new ArrayList<Map<String, Object>>();
		for (OwndomainEngineRel engineRel : enabledEngines) {

			Map<String, Object> searchEngineMap = new LinkedHashMap<String, Object>();

			searchEngineMap.put("languageId", engineRel.getLanguageId());
			searchEngineMap.put("isMobileDomain", engineRel.isMobile());
			searchEngineMap.put("device", engineRel.getDevice());
			searchEngineMap.put("isPrimaryEngine", engineRel.getPrimaryFlag());
			searchEngineMap.put("country", engineRel.getCountryQueryName());

			if (engineRel.hasVirtualEngine()) {
				//Cee - https://www.wrike.com/open.htm?id=718823955
				searchEngineMap.put("engineId", engineRel.getEngineId());
				//	        	searchEngineMap.put("engineId", engineRel.getVirtualEngineId());
				searchEngineMap.put("engineIdOld", engineRel.getVirtualEngineId());
				searchEngineMap.put("engineName", engineRel.getVirtualEngineDisplayName());
				searchEngineMap.put("supportGeo", isSupportGeoRank(engineRel.getVirtualEngineDisplayName(), enableGeoDesktop, enableGeoMobile));
			} else {
				searchEngineMap.put("engineId", engineRel.getEngineId());
				searchEngineMap.put("engineIdOld", engineRel.getEngineId());
				searchEngineMap.put("engineName", engineRel.getEngineDisplayName());
				searchEngineMap.put("supportGeo", isSupportGeoRank(engineRel.getEngineDisplayName(), enableGeoDesktop, enableGeoMobile));
			}

			//Cee - https://www.wrike.com/open.htm?id=539329160
			//for JS code only, useless for java side
			searchEngineMap.put("deviceOld", searchEngineMap.get("device"));

			engineMapList.add(searchEngineMap);
		}

		return engineMapList;
	}

	//Cee - https://www.wrike.com/open.htm?id=375484923
	//query from owndomain_engine_rel
	public static List<OwndomainEngineRel> getEnabledEngines(int ownDomainId) {
		return owndomainEngineRelDAO.getEnabledEngines(ownDomainId);
	}

	//Cee - https://www.wrike.com/open.htm?id=237451207
	public static boolean isSupportGeoRank(String engineName, boolean enableGeoDesktop, boolean enableGeoMobile) {
		if (!enableGeoDesktop && !enableGeoMobile) {
			return false;
		}
		if (StringUtils.containsIgnoreCase(engineName, "image")) {
			return false;
		}
		boolean isGoogleEngine = StringUtils.containsIgnoreCase(engineName, "google");
		boolean isMobileEngine = StringUtils.containsIgnoreCase(engineName, "mobile");

		if (isGoogleEngine && isMobileEngine && enableGeoMobile) {
			return true;
		}

		if (isGoogleEngine && !isMobileEngine && enableGeoDesktop) {
			return true;
		}

		return false;
	}
}