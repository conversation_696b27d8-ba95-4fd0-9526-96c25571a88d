package com.actonia.service;

import java.util.Properties;

import org.apache.commons.lang.StringUtils;

import com.actonia.IConstants;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.HttpUtils;
import com.actonia.value.object.CausalImpactResponse;
import com.actonia.value.object.ContentGuardResourceResponse;
import com.actonia.value.object.FindTargetUrlsDifferencesResponse;
import com.actonia.value.object.GoogleTagResourceResponse;
import com.actonia.value.object.MarketMatchingResponse;
import com.actonia.value.object.ProphetResponse;
import com.actonia.value.object.SerpAnalyzerResourceResponse;
import com.actonia.value.object.SiteAuditCheckDomainResponse;
import com.actonia.value.object.ZapierContentGuardAlert;
import com.actonia.value.object.ZapierResourceResponse;
import com.google.gson.Gson;

public class MainWebServiceClientService {

	private boolean isDebug = false;

	private static String mainWebServiceEndPoint = null;
	public static final String ROUTER_FIND_SITE_AUDIT_CHECK_DOMAIN = "/siteAudit/checkDomain";
	public static final String QUERY_PARM_URL = "url=";
	public static final String ROUTER_FIND_TARGET_URLS_DIFFERENCES = "/findTargetUrlsDifferences";
	public static final String ROUTER_CONTENT_GUARD = "/content_guard/";
	public static final String ROUTER_ZAPIER = "/zapier/";
	public static final String ROUTER_GOOGLE_TAG = "/google/tag/";
	public static final String ROUTER_CAUSAL_IMPACT = "/causal_impact/";
	public static final String ROUTER_PROPHET = "/prophet";
	public static final String ROUTER_MARKET_MATCHING = "/market_matching";
	public static final String ROUTER_SERP_ANALYZER = "/serp_analyzer";	

	private MainWebServiceClientService() {
	}

	public String getMainWebServiceEndPoint() {
		if (StringUtils.isBlank(mainWebServiceEndPoint)) {
			try {
				Properties properties = new Properties();
				properties.load(MainWebServiceClientService.class.getResourceAsStream("/main_web_service.properties"));

				// polite.crawl.web.service.endpoint
				mainWebServiceEndPoint = properties.getProperty(IConstants.RUNTIME_PROPERTY_NAME_MAIN_WEB_SERVICE_ENDPOINT);
				FormatUtils.getInstance().logMemoryUsage("getMainWebServiceEndPoint() mainWebServiceEndPoint=" + mainWebServiceEndPoint);

			} catch (Exception e) {
				e.printStackTrace();
				FormatUtils.getInstance().logMemoryUsage("getMainWebServiceEndPoint() exception message=" + e.getMessage());
				System.exit(-1);
			}
		}
		return mainWebServiceEndPoint;
	}

	public ZapierResourceResponse zapierContentGuardAlertPost(String requestUrl, String requestParameters) {
		//long startTimestamp = System.currentTimeMillis();
		System.out.println("requestJSON=" + requestParameters);
		boolean isSendGetRequest = false;
		ZapierResourceResponse zapierResourceResponse = null;
		//FormatUtils.getInstance().logMemoryUsage("zapierContentGuardAlertPost() requestUrl=" + requestUrl);
		String responseString = null;

		try {
			responseString = HttpUtils.getInstance().getResponseString(requestUrl, isSendGetRequest, requestParameters);
			System.out.println("responseString=" + responseString);
			if (StringUtils.isNotBlank(responseString)) {
				zapierResourceResponse = new Gson().fromJson(responseString, ZapierResourceResponse.class);
				if (zapierResourceResponse != null) {
					System.out.println("responseJSON=" + new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class));
				} else {
					FormatUtils.getInstance().logMemoryUsage("zapierContentGuardAlertPost() zapierResourceResponse is null.");
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		//FormatUtils.getInstance().logMemoryUsage(
		//		"zapierContentGuardAlertPost() ends. requestUrl=" + requestUrl + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return zapierResourceResponse;
	}

	public ZapierContentGuardAlert[] zapierContentGuardAlertGet(String requestUrl) {
		//long startTimestamp = System.currentTimeMillis();
		boolean isSendGetRequest = true;
		String requestParameters = null;
		ZapierContentGuardAlert[] zapierContentGuardAlertArray = null;
		//FormatUtils.getInstance().logMemoryUsage("zapierContentGuardAlertGet() requestUrl=" + requestUrl);
		String responseString = null;
		try {
			responseString = HttpUtils.getInstance().getResponseString(requestUrl, isSendGetRequest, requestParameters);
			if (StringUtils.isNotBlank(responseString)) {
				zapierContentGuardAlertArray = new Gson().fromJson(responseString, ZapierContentGuardAlert[].class);
				if (zapierContentGuardAlertArray != null) {
					System.out.println("responseJSON=" + new Gson().toJson(zapierContentGuardAlertArray, ZapierContentGuardAlert[].class));
				} else {
					FormatUtils.getInstance().logMemoryUsage("zapierContentGuardAlertGet() zapierContentGuardAlertArray is null.");
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		//FormatUtils.getInstance().logMemoryUsage(
		//		"zapierContentGuardAlertGet() ends. requestUrl=" + requestUrl + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return zapierContentGuardAlertArray;
	}

	public ZapierResourceResponse zapierContentGuardAlertDelete(String requestUrl) {
		System.out.println("zapierContentGuardAlertDelete() begins. requestUrl=" + requestUrl);
		long startTimestamp = System.currentTimeMillis();
		String responseString = null;
		ZapierResourceResponse zapierResourceResponse = null;
		try {
			responseString = HttpUtils.getInstance().getDeleteResponseString(requestUrl);
			if (StringUtils.isNotBlank(responseString)) {
				zapierResourceResponse = new Gson().fromJson(responseString, ZapierResourceResponse.class);
				if (zapierResourceResponse != null) {
					System.out.println("responseJSON=" + new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class));
				} else {
					FormatUtils.getInstance().logMemoryUsage("zapierContentGuardAlert() zapierResourceResponse is null.");
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		System.out.println("zapierContentGuardAlertDelete() ends. requestUrl=" + requestUrl);
		return zapierResourceResponse;
	}

	public FindTargetUrlsDifferencesResponse findTargetUrlsDifferences(String ip, String requestUrl, String requestParameters) {
		long startTimestamp = System.currentTimeMillis();
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = null;
		//FormatUtils.getInstance().logMemoryUsage("findTargetUrlsDifferences() requestUrl=" + requestUrl);
		String responseString = null;
		boolean isSendGetRequest = false;

		try {
			responseString = HttpUtils.getInstance().getResponseString(requestUrl, isSendGetRequest, requestParameters);
			if (responseString != null) {
				//FormatUtils.getInstance().logMemoryUsage("findTargetUrlsDifferences() responseString=" + responseString);
				findTargetUrlsDifferencesResponse = new Gson().fromJson(responseString, FindTargetUrlsDifferencesResponse.class);
				if (findTargetUrlsDifferencesResponse != null) {
					//FormatUtils.getInstance().logMemoryUsage("findTargetUrlsDifferences() findTargetUrlsDifferencesResponse="
					//		+ new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class));
				} else {
					FormatUtils.getInstance().logMemoryUsage("findTargetUrlsDifferences() findTargetUrlsDifferencesResponse is empty.");
				}
			} else {
				FormatUtils.getInstance().logMemoryUsage("findTargetUrlsDifferences() responseString is empty.");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		//FormatUtils.getInstance().logMemoryUsage(
		//		"findTargetUrlsDifferences() ends. requestUrl=" + requestUrl + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return findTargetUrlsDifferencesResponse;
	}

	public SiteAuditCheckDomainResponse siteAuditCheckDomain(String ip, String requestUrl) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		SiteAuditCheckDomainResponse siteAuditCheckDomainResponse = null;
		FormatUtils.getInstance().logMemoryUsage("siteAuditCheckDomain() begins. ip=" + ip + ",requestUrl=" + requestUrl);
		String responseString = null;
		boolean isSendGetRequest = true;

		responseString = HttpUtils.getInstance().getResponseString(requestUrl, isSendGetRequest, null);
		if (responseString != null) {
			FormatUtils.getInstance().logMemoryUsage("siteAuditCheckDomain() ip=" + ip + ",responseString=" + responseString);
			siteAuditCheckDomainResponse = new Gson().fromJson(responseString, SiteAuditCheckDomainResponse.class);
			if (siteAuditCheckDomainResponse != null) {
				FormatUtils.getInstance().logMemoryUsage("siteAuditCheckDomain() ip=" + ip + ",siteAuditCheckDomainResponse="
						+ new Gson().toJson(siteAuditCheckDomainResponse, SiteAuditCheckDomainResponse.class));
			} else {
				FormatUtils.getInstance().logMemoryUsage("siteAuditCheckDomain() ip=" + ip + ",siteAuditCheckDomain(Response is empty.");
			}
		} else {
			FormatUtils.getInstance().logMemoryUsage("siteAuditCheckDomain() ip=" + ip + ",responseString is empty.");
		}
		FormatUtils.getInstance().logMemoryUsage(
				"siteAuditCheckDomain() ends. ip=" + ip + ",requestUrl=" + requestUrl + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return siteAuditCheckDomainResponse;
	}

	public ContentGuardResourceResponse contentGuard(String requestUrl, String requestParameters) {
		return contentGuard(requestUrl, requestParameters, null, null);
	}

	public ContentGuardResourceResponse contentGuard(String requestUrl, String requestParameters, Integer domainId, String ip) {
		//System.out.println("requestUrl=" + requestUrl);
		if (domainId != null && StringUtils.isNotBlank(ip)) {
			System.out.println("contentGuard() ip=" + ip + ",domainId=" + domainId + ",requestJSON=" + requestParameters);
		} else {
			System.out.println("contentGuard() requestJSON=" + requestParameters);
		}
		ContentGuardResourceResponse contentGuardResourceResponse = null;
		String responseString = null;
		boolean isSendGetRequest = false;

		try {
			responseString = HttpUtils.getInstance().getResponseString(requestUrl, isSendGetRequest, requestParameters);
			if (responseString != null) {
				//FormatUtils.getInstance().logMemoryUsage("contentGuard() responseString=" + responseString);
				contentGuardResourceResponse = new Gson().fromJson(responseString, ContentGuardResourceResponse.class);
				if (contentGuardResourceResponse != null) {
					if (isDebug == true) {
						System.out.println("contentGuard() ip=" + ip + ",domainId=" + domainId + ",responseJSON="
								+ new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class));
					}
				} else {
					FormatUtils.getInstance().logMemoryUsage("contentGuard() contentGuardResourceResponse is empty.");
				}
			} else {
				FormatUtils.getInstance().logMemoryUsage("contentGuard() responseString is empty.");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		//FormatUtils.getInstance().logMemoryUsage(
		//		"contentGuard() ends. requestUrl=" + requestUrl + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return contentGuardResourceResponse;
	}

	public ZapierContentGuardAlert[] zapierContentGuardAlert(String requestUrl, String requestParameters) {
		System.out.println("requestJSON=" + requestParameters);
		long startTimestamp = System.currentTimeMillis();
		ZapierContentGuardAlert[] zapierContentGuardAlertQueueMessages = null;
		//FormatUtils.getInstance().logMemoryUsage("zapierContentGuardAlert() requestUrl=" + requestUrl);
		String responseString = null;
		boolean isSendGetRequest = false;

		try {
			responseString = HttpUtils.getInstance().getResponseString(requestUrl, isSendGetRequest, requestParameters);
			if (StringUtils.isBlank(responseString)) {
				zapierContentGuardAlertQueueMessages = new ZapierContentGuardAlert[0];
			} else {
				zapierContentGuardAlertQueueMessages = new Gson().fromJson(responseString, ZapierContentGuardAlert[].class);
				if (zapierContentGuardAlertQueueMessages != null) {
					System.out.println("responseJSON=" + new Gson().toJson(zapierContentGuardAlertQueueMessages, ZapierContentGuardAlert[].class));
				} else {
					FormatUtils.getInstance().logMemoryUsage("zapierContentGuardAlert() zapierContentGuardAlertQueueMessages is null.");
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		//FormatUtils.getInstance().logMemoryUsage(
		//		"zapierContentGuardAlert() ends. requestUrl=" + requestUrl + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return zapierContentGuardAlertQueueMessages;
	}

	public GoogleTagResourceResponse maintainStructuredData(String requestUrl, String requestParameters) {
		//System.out.println("requestUrl=" + requestUrl);
		System.out.println("requestJSON=" + requestParameters); //debug
		GoogleTagResourceResponse googleTagResourceResponse = null;
		String responseString = null;
		boolean isSendGetRequest = false;

		try {
			responseString = HttpUtils.getInstance().getResponseString(requestUrl, isSendGetRequest, requestParameters);
			if (responseString != null) {
				//FormatUtils.getInstance().logMemoryUsage("maintainStructuredData() responseString=" + responseString);
				googleTagResourceResponse = new Gson().fromJson(responseString, GoogleTagResourceResponse.class);
				if (googleTagResourceResponse != null) {
					System.out.println("responseJSON=" + new Gson().toJson(googleTagResourceResponse, GoogleTagResourceResponse.class));
				} else {
					FormatUtils.getInstance().logMemoryUsage("maintainStructuredData() googleTagResourceResponse is empty.");
				}
			} else {
				FormatUtils.getInstance().logMemoryUsage("maintainStructuredData() responseString is empty.");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		//FormatUtils.getInstance().logMemoryUsage(
		//		"maintainStructuredData() ends. requestUrl=" + requestUrl + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return googleTagResourceResponse;
	}

	public CausalImpactResponse causalImpact(String requestUrl, String requestParameters) {
		if (isDebug == true) {
			System.out.println("causalImpact() requestUrl=" + requestUrl);
			System.out.println("causalImpact() requestJSON=" + requestParameters);
		}
		CausalImpactResponse causalImpactResponse = null;
		String responseString = null;
		boolean isSendGetRequest = false;

		try {
			responseString = HttpUtils.getInstance().getResponseString(requestUrl, isSendGetRequest, requestParameters);
			if (responseString != null) {
				if (isDebug == true) {
					FormatUtils.getInstance().logMemoryUsage("causalImpact() responseString=" + responseString);
				}
				causalImpactResponse = new Gson().fromJson(responseString, CausalImpactResponse.class);
				if (causalImpactResponse != null) {
				} else {
					FormatUtils.getInstance().logMemoryUsage("causalImpact() causalImpactResponse is empty.");
				}
			} else {
				FormatUtils.getInstance().logMemoryUsage("causalImpact() responseString is empty.");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		//FormatUtils.getInstance().logMemoryUsage(
		//		"causalImpact() ends. requestUrl=" + requestUrl + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return causalImpactResponse;
	}

	public ProphetResponse prophet(String requestUrl, String requestParameters) {
		if (isDebug == true) {
			System.out.println("prophet() requestUrl=" + requestUrl);
			System.out.println("prophet() requestJSON=" + requestParameters);
		}
		ProphetResponse prophetResponse = null;
		String responseString = null;
		boolean isSendGetRequest = false;

		try {
			responseString = HttpUtils.getInstance().getResponseString(requestUrl, isSendGetRequest, requestParameters);
			if (responseString != null) {
				if (isDebug == true) {
					FormatUtils.getInstance().logMemoryUsage("prophet() responseString=" + responseString);
				}
				prophetResponse = new Gson().fromJson(responseString, ProphetResponse.class);
				if (prophetResponse != null) {
				} else {
					FormatUtils.getInstance().logMemoryUsage("prophet() prophetResponse is empty.");
				}
			} else {
				FormatUtils.getInstance().logMemoryUsage("prophet() responseString is empty.");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		//FormatUtils.getInstance().logMemoryUsage(
		//		"prophet() ends. requestUrl=" + requestUrl + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return prophetResponse;
	}

	public MarketMatchingResponse marketMatching(String requestUrl, String requestParameters) {
		if (isDebug == true) {
			System.out.println("marketMatching() requestUrl=" + requestUrl);
			System.out.println("marketMatching() requestJSON=" + requestParameters);
		}
		MarketMatchingResponse marketMatchingResponse = null;
		String responseString = null;
		boolean isSendGetRequest = false;

		try {
			responseString = HttpUtils.getInstance().getResponseString(requestUrl, isSendGetRequest, requestParameters);
			if (responseString != null) {
				if (isDebug == true) {
					FormatUtils.getInstance().logMemoryUsage("marketMatching() responseString=" + responseString);
				}
				marketMatchingResponse = new Gson().fromJson(responseString, MarketMatchingResponse.class);
				if (marketMatchingResponse != null) {
				} else {
					FormatUtils.getInstance().logMemoryUsage("marketMatching() marketMatchingResponse is empty.");
				}
			} else {
				FormatUtils.getInstance().logMemoryUsage("marketMatching() responseString is empty.");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		//FormatUtils.getInstance().logMemoryUsage(
		//		"marketMatching() ends. requestUrl=" + requestUrl + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return marketMatchingResponse;
	}

	public SerpAnalyzerResourceResponse serpAnalyzer(String requestUrl, String requestParameters) {
		if (isDebug == true) {
			System.out.println("serpAnalyzer() requestUrl=" + requestUrl);
			System.out.println("serpAnalyzer() requestJSON=" + requestParameters);
		}
		SerpAnalyzerResourceResponse serpAnalyzerResourceResponse = null;
		String responseString = null;
		boolean isSendGetRequest = false;

		try {
			responseString = HttpUtils.getInstance().getResponseString(requestUrl, isSendGetRequest, requestParameters);
			if (responseString != null) {
				if (isDebug == true) {
					FormatUtils.getInstance().logMemoryUsage("serpAnalyzer() responseString=" + responseString);
				}
				serpAnalyzerResourceResponse = new Gson().fromJson(responseString, SerpAnalyzerResourceResponse.class);
				if (serpAnalyzerResourceResponse != null) {
				} else {
					FormatUtils.getInstance().logMemoryUsage("serpAnalyzer() serpAnalyzerResourceResponse is empty.");
				}
			} else {
				FormatUtils.getInstance().logMemoryUsage("serpAnalyzer() responseString is empty.");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		//FormatUtils.getInstance().logMemoryUsage(
		//		"serpAnalyzer() ends. requestUrl=" + requestUrl + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return serpAnalyzerResourceResponse;
	}
}
