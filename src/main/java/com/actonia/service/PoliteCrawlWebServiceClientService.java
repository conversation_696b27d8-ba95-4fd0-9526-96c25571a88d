package com.actonia.service;

import java.util.Properties;

import org.apache.commons.lang.StringUtils;

import com.actonia.IConstants;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.HttpUtils;
import com.actonia.value.object.CausalImpactResponse;
import com.actonia.value.object.ContentGuardResourceResponse;
import com.actonia.value.object.FindTargetUrlsDifferencesResponse;
import com.actonia.value.object.MarketMatchingResponse;
import com.actonia.value.object.ProphetResponse;
import com.actonia.value.object.SerpAnalyzerResourceResponse;
import com.actonia.value.object.TargetUrlChangeResponse;
import com.actonia.value.object.ZapierContentGuardAlert;
import com.actonia.value.object.ZapierCrawlCompletedAlert;
import com.actonia.value.object.ZapierCrawlInitiatedAlert;
import com.actonia.value.object.ZapierPageTagContentAlert;
import com.actonia.value.object.ZapierResourceResponse;
import com.actonia.value.object.ZapierTargetUrlChangeAlert;
import com.google.gson.Gson;

public class PoliteCrawlWebServiceClientService {

	//private boolean isDebug = false;

	private static String politeCrawlWebServiceEndPoint = null;
	public static final String ROUTER_FIND_TARGET_URLS_DIFFERENCES = "/findTargetUrlsDifferences";
	public static final String ROUTER_CONTENT_GUARD = "/content_guard/";
	public static final String ROUTER_CAUSAL_IMPACT = "/causal_impact/";
	public static final String ROUTER_ZAPIER = "/zapier/";
	public static final String ROUTER_PROPHET = "/prophet";
	public static final String ROUTER_MARKET_MATCHING = "/market_matching";
	public static final String ROUTER_SERP_ANALYZER = "/serp_analyzer";
	public static final String ROUTER_TARGET_URL_CHANGE = "/target_url_change/";

	private PoliteCrawlWebServiceClientService() {
	}

	public String getPoliteCrawlWebServiceEndPoint() {
		if (StringUtils.isBlank(politeCrawlWebServiceEndPoint)) {
			try {
				Properties properties = new Properties();
				properties.load(PoliteCrawlWebServiceClientService.class.getResourceAsStream("/polite.crawl.v2.0.properties"));

				// polite.crawl.web.service.endpoint
				politeCrawlWebServiceEndPoint = properties.getProperty(IConstants.RUNTIME_PROPERTY_NAME_POLITE_CRAWL_WEB_SERVICE_ENDPOINT);
				FormatUtils.getInstance().logMemoryUsage("getPoliteCrawlWebServiceEndPoint() politeCrawlWebServiceEndPoint=" + politeCrawlWebServiceEndPoint);

			} catch (Exception e) {
				e.printStackTrace();
				FormatUtils.getInstance().logMemoryUsage("getPoliteCrawlWebServiceEndPoint() exception message=" + e.getMessage());
				System.exit(-1);
			}
		}
		return politeCrawlWebServiceEndPoint;
	}

	public TargetUrlChangeResponse targetUrlChange(String requestUrl, String requestParameters) {
		System.out.println("requestJSON=" + requestParameters);
		long startTimestamp = System.currentTimeMillis();
		TargetUrlChangeResponse targetUrlChangeResponse = null;
		//FormatUtils.getInstance().logMemoryUsage("targetUrlChange() requestUrl=" + requestUrl);
		String responseString = null;
		boolean isSendGetRequest = false;

		try {
			responseString = HttpUtils.getInstance().getResponseString(requestUrl, isSendGetRequest, requestParameters);
			if (responseString != null) {
				//System.out.println("targetUrlChange() responseString=" + responseString); //debug
				targetUrlChangeResponse = new Gson().fromJson(responseString, TargetUrlChangeResponse.class);
				if (targetUrlChangeResponse != null) {
					System.out.println("responseJSON=" + new Gson().toJson(targetUrlChangeResponse, TargetUrlChangeResponse.class));
				} else {
					FormatUtils.getInstance().logMemoryUsage("targetUrlChange() targetUrlChangeResponse is empty.");
				}
			} else {
				FormatUtils.getInstance().logMemoryUsage("targetUrlChange() responseString is empty.");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		//FormatUtils.getInstance().logMemoryUsage(
		//		"targetUrlChange() ends. requestUrl=" + requestUrl + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return targetUrlChangeResponse;
	}

	public SerpAnalyzerResourceResponse serpAnalyzer(String requestUrl, String requestParameters) {
		System.out.println("requestJSON=" + requestParameters);
		long startTimestamp = System.currentTimeMillis();
		SerpAnalyzerResourceResponse serpAnalyzerResourceResponse = null;
		//FormatUtils.getInstance().logMemoryUsage("serpAnalyzer() requestUrl=" + requestUrl);
		String responseString = null;
		boolean isSendGetRequest = false;

		try {
			responseString = HttpUtils.getInstance().getResponseString(requestUrl, isSendGetRequest, requestParameters);
			if (responseString != null) {
				//FormatUtils.getInstance().logMemoryUsage("serpAnalyzer() responseString=" + responseString);
				serpAnalyzerResourceResponse = new Gson().fromJson(responseString, SerpAnalyzerResourceResponse.class);
				if (serpAnalyzerResourceResponse != null) {
					System.out.println("responseJSON=" + new Gson().toJson(serpAnalyzerResourceResponse, SerpAnalyzerResourceResponse.class));
				} else {
					FormatUtils.getInstance().logMemoryUsage("serpAnalyzer() serpAnalyzerResourceResponse is empty.");
				}
			} else {
				FormatUtils.getInstance().logMemoryUsage("serpAnalyzer() responseString is empty.");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		//FormatUtils.getInstance().logMemoryUsage(
		//		"serpAnalyzer() ends. requestUrl=" + requestUrl + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return serpAnalyzerResourceResponse;
	}

	public ZapierResourceResponse zapierPost(String requestUrl, String requestParameters) {
		//long startTimestamp = System.currentTimeMillis();
		System.out.println("requestJSON=" + requestParameters);
		boolean isSendGetRequest = false;
		ZapierResourceResponse zapierResourceResponse = null;
		//FormatUtils.getInstance().logMemoryUsage("zapierPost() requestUrl=" + requestUrl);
		String responseString = null;

		try {
			responseString = HttpUtils.getInstance().getResponseString(requestUrl, isSendGetRequest, requestParameters);
			//System.out.println("responseString=" + responseString);
			if (StringUtils.isNotBlank(responseString)) {
				zapierResourceResponse = new Gson().fromJson(responseString, ZapierResourceResponse.class);
				if (zapierResourceResponse != null) {
					System.out.println("responseJSON=" + new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class));
				} else {
					FormatUtils.getInstance().logMemoryUsage("zapierPost() zapierResourceResponse is null.");
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		//FormatUtils.getInstance().logMemoryUsage(
		//		"zapierPost() ends. requestUrl=" + requestUrl + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return zapierResourceResponse;
	}

	public ZapierContentGuardAlert[] zapierContentGuardAlertGet(String requestUrl) {
		//long startTimestamp = System.currentTimeMillis();
		boolean isSendGetRequest = true;
		String requestParameters = null;
		ZapierContentGuardAlert[] zapierContentGuardAlertArray = null;
		//FormatUtils.getInstance().logMemoryUsage("zapierContentGuardAlertGet() requestUrl=" + requestUrl);
		String responseString = null;
		try {
			responseString = HttpUtils.getInstance().getResponseString(requestUrl, isSendGetRequest, requestParameters);
			if (StringUtils.isNotBlank(responseString)) {
				zapierContentGuardAlertArray = new Gson().fromJson(responseString, ZapierContentGuardAlert[].class);
				if (zapierContentGuardAlertArray != null) {
					System.out.println("responseJSON=" + new Gson().toJson(zapierContentGuardAlertArray, ZapierContentGuardAlert[].class));
				} else {
					FormatUtils.getInstance().logMemoryUsage("zapierContentGuardAlertGet() zapierContentGuardAlertArray is null.");
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		//FormatUtils.getInstance().logMemoryUsage(
		//		"zapierContentGuardAlertGet() ends. requestUrl=" + requestUrl + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return zapierContentGuardAlertArray;
	}

	public ZapierResourceResponse zapierDelete(String requestUrl) {
		System.out.println("zapierDelete() begins. requestUrl=" + requestUrl);
		long startTimestamp = System.currentTimeMillis();
		String responseString = null;
		ZapierResourceResponse zapierResourceResponse = null;
		try {
			responseString = HttpUtils.getInstance().getDeleteResponseString(requestUrl);
			if (StringUtils.isNotBlank(responseString)) {
				zapierResourceResponse = new Gson().fromJson(responseString, ZapierResourceResponse.class);
				if (zapierResourceResponse != null) {
					System.out.println("responseJSON=" + new Gson().toJson(zapierResourceResponse, ZapierResourceResponse.class));
				} else {
					FormatUtils.getInstance().logMemoryUsage("zapierContentGuardAlert() zapierResourceResponse is null.");
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		System.out.println("zapierDelete() ends. requestUrl=" + requestUrl);
		return zapierResourceResponse;
	}

	public ZapierCrawlInitiatedAlert[] zapierCrawlInitiatedAlertGet(String requestUrl) {
		//long startTimestamp = System.currentTimeMillis();
		boolean isSendGetRequest = true;
		String requestParameters = null;
		ZapierCrawlInitiatedAlert[] zapierCrawlInitiatedAlertArray = null;
		//FormatUtils.getInstance().logMemoryUsage("zapierCrawlInitiatedAlertGet() requestUrl=" + requestUrl);
		String responseString = null;
		try {
			responseString = HttpUtils.getInstance().getResponseString(requestUrl, isSendGetRequest, requestParameters);
			if (StringUtils.isNotBlank(responseString)) {
				zapierCrawlInitiatedAlertArray = new Gson().fromJson(responseString, ZapierCrawlInitiatedAlert[].class);
				if (zapierCrawlInitiatedAlertArray != null) {
					System.out.println("responseJSON=" + new Gson().toJson(zapierCrawlInitiatedAlertArray, ZapierCrawlInitiatedAlert[].class));
				} else {
					FormatUtils.getInstance().logMemoryUsage("zapierCrawlInitiatedAlertGet() zapierCrawlInitiatedAlertArray is null.");
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		//FormatUtils.getInstance().logMemoryUsage(
		//		"zapierCrawlInitiatedAlertGet() ends. requestUrl=" + requestUrl + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return zapierCrawlInitiatedAlertArray;
	}

	public ZapierCrawlCompletedAlert[] zapierCrawlCompletedAlertGet(String requestUrl) {
		//long startTimestamp = System.currentTimeMillis();
		boolean isSendGetRequest = true;
		String requestParameters = null;
		ZapierCrawlCompletedAlert[] zapierCrawlCompletedAlertArray = null;
		//FormatUtils.getInstance().logMemoryUsage("zapierCrawlCompletedAlertGet() requestUrl=" + requestUrl);
		String responseString = null;
		try {
			responseString = HttpUtils.getInstance().getResponseString(requestUrl, isSendGetRequest, requestParameters);
			if (StringUtils.isNotBlank(responseString)) {
				zapierCrawlCompletedAlertArray = new Gson().fromJson(responseString, ZapierCrawlCompletedAlert[].class);
				if (zapierCrawlCompletedAlertArray != null) {
					System.out.println("responseJSON=" + new Gson().toJson(zapierCrawlCompletedAlertArray, ZapierCrawlCompletedAlert[].class));
				} else {
					FormatUtils.getInstance().logMemoryUsage("zapierCrawlCompletedAlertGet() zapierCrawlCompletedAlertArray is null.");
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		//FormatUtils.getInstance().logMemoryUsage(
		//		"zapierCrawlCompletedAlertGet() ends. requestUrl=" + requestUrl + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return zapierCrawlCompletedAlertArray;
	}

	public ZapierPageTagContentAlert[] zapierPageTagContentAlertGet(String requestUrl) {
		//long startTimestamp = System.currentTimeMillis();
		boolean isSendGetRequest = true;
		String requestParameters = null;
		ZapierPageTagContentAlert[] zapierPageTagContentAlertArray = null;
		//FormatUtils.getInstance().logMemoryUsage("zapierPageTagContentAlertGet() requestUrl=" + requestUrl);
		String responseString = null;
		try {
			responseString = HttpUtils.getInstance().getResponseString(requestUrl, isSendGetRequest, requestParameters);
			if (StringUtils.isNotBlank(responseString)) {
				zapierPageTagContentAlertArray = new Gson().fromJson(responseString, ZapierPageTagContentAlert[].class);
				if (zapierPageTagContentAlertArray != null) {
					System.out.println("responseJSON=" + new Gson().toJson(zapierPageTagContentAlertArray, ZapierPageTagContentAlert[].class));
				} else {
					FormatUtils.getInstance().logMemoryUsage("zapierPageTagContentAlertGet() zapierPageTagContentAlertArray is null.");
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		//FormatUtils.getInstance().logMemoryUsage(
		//		"zapierPageTagContentAlertGet() ends. requestUrl=" + requestUrl + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return zapierPageTagContentAlertArray;
	}

	public FindTargetUrlsDifferencesResponse findTargetUrlsDifferences(String requestUrl, String requestParameters) {
		System.out.println("requestJSON=" + requestParameters);
		long startTimestamp = System.currentTimeMillis();
		FindTargetUrlsDifferencesResponse findTargetUrlsDifferencesResponse = null;
		//FormatUtils.getInstance().logMemoryUsage("findTargetUrlsDifferences() requestUrl=" + requestUrl);
		String responseString = null;
		boolean isSendGetRequest = false;

		try {
			responseString = HttpUtils.getInstance().getResponseString(requestUrl, isSendGetRequest, requestParameters);
			if (responseString != null) {
				//FormatUtils.getInstance().logMemoryUsage("findTargetUrlsDifferences() responseString=" + responseString);
				findTargetUrlsDifferencesResponse = new Gson().fromJson(responseString, FindTargetUrlsDifferencesResponse.class);
				if (findTargetUrlsDifferencesResponse != null) {
					System.out.println("responseJSON=" + new Gson().toJson(findTargetUrlsDifferencesResponse, FindTargetUrlsDifferencesResponse.class));
				} else {
					FormatUtils.getInstance().logMemoryUsage("findTargetUrlsDifferences() findTargetUrlsDifferencesResponse is empty.");
				}
			} else {
				FormatUtils.getInstance().logMemoryUsage("findTargetUrlsDifferences() responseString is empty.");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		//FormatUtils.getInstance().logMemoryUsage(
		//		"findTargetUrlsDifferences() ends. requestUrl=" + requestUrl + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return findTargetUrlsDifferencesResponse;
	}

	public ContentGuardResourceResponse contentGuard(String requestUrl, String requestParameters) {
		System.out.println("requestJSON=" + requestParameters);
		long startTimestamp = System.currentTimeMillis();
		ContentGuardResourceResponse contentGuardResourceResponse = null;
		//FormatUtils.getInstance().logMemoryUsage("contentGuard() requestUrl=" + requestUrl);
		String responseString = null;
		boolean isSendGetRequest = false;

		try {
			responseString = HttpUtils.getInstance().getResponseString(requestUrl, isSendGetRequest, requestParameters);
			if (responseString != null) {
				//FormatUtils.getInstance().logMemoryUsage("contentGuard() responseString=" + responseString);
				contentGuardResourceResponse = new Gson().fromJson(responseString, ContentGuardResourceResponse.class);
				if (contentGuardResourceResponse != null) {
					System.out.println("responseJSON=" + new Gson().toJson(contentGuardResourceResponse, ContentGuardResourceResponse.class));
				} else {
					FormatUtils.getInstance().logMemoryUsage("contentGuard() contentGuardResourceResponse is empty.");
				}
			} else {
				FormatUtils.getInstance().logMemoryUsage("contentGuard() responseString is empty.");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		//FormatUtils.getInstance().logMemoryUsage(
		//		"contentGuard() ends. requestUrl=" + requestUrl + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return contentGuardResourceResponse;
	}

	public CausalImpactResponse causalImpact(String requestUrl, String requestParameters) {
		System.out.println("requestJSON=" + requestParameters);
		CausalImpactResponse causalImpactResourceResponse = null;
		//FormatUtils.getInstance().logMemoryUsage("causalImpact() requestUrl=" + requestUrl);
		String responseString = null;
		boolean isSendGetRequest = false;

		try {
			responseString = HttpUtils.getInstance().getResponseString(requestUrl, isSendGetRequest, requestParameters);
			if (responseString != null) {
				//FormatUtils.getInstance().logMemoryUsage("causalImpact() responseString=" + responseString);
				causalImpactResourceResponse = new Gson().fromJson(responseString, CausalImpactResponse.class);
				if (causalImpactResourceResponse != null) {
					System.out.println("responseJSON=" + new Gson().toJson(causalImpactResourceResponse, CausalImpactResponse.class));
				} else {
					FormatUtils.getInstance().logMemoryUsage("causalImpact() causalImpactResourceResponse is empty.");
				}
			} else {
				FormatUtils.getInstance().logMemoryUsage("causalImpact() responseString is empty.");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return causalImpactResourceResponse;
	}

	public ProphetResponse prophet(String requestUrl, String requestParameters) {
		System.out.println("requestJSON=" + requestParameters);
		ProphetResponse prophetResponse = null;
		//FormatUtils.getInstance().logMemoryUsage("prophet() requestUrl=" + requestUrl);
		String responseString = null;
		boolean isSendGetRequest = false;

		try {
			responseString = HttpUtils.getInstance().getResponseString(requestUrl, isSendGetRequest, requestParameters);
			if (responseString != null) {
				//FormatUtils.getInstance().logMemoryUsage("prophet() responseString=" + responseString);
				prophetResponse = new Gson().fromJson(responseString, ProphetResponse.class);
				if (prophetResponse != null) {
					System.out.println("responseJSON=" + new Gson().toJson(prophetResponse, ProphetResponse.class));
				} else {
					FormatUtils.getInstance().logMemoryUsage("prophet() prophetResponse is empty.");
				}
			} else {
				FormatUtils.getInstance().logMemoryUsage("prophet() responseString is empty.");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return prophetResponse;
	}

	public MarketMatchingResponse marketMatching(String requestUrl, String requestParameters) {
		System.out.println("requestJSON=" + requestParameters);
		MarketMatchingResponse marketMatchingResponse = null;
		//FormatUtils.getInstance().logMemoryUsage("marketMatching() requestUrl=" + requestUrl);
		String responseString = null;
		boolean isSendGetRequest = false;

		try {
			responseString = HttpUtils.getInstance().getResponseString(requestUrl, isSendGetRequest, requestParameters);
			if (responseString != null) {
				//FormatUtils.getInstance().logMemoryUsage("marketMatching() responseString=" + responseString);
				marketMatchingResponse = new Gson().fromJson(responseString, MarketMatchingResponse.class);
				if (marketMatchingResponse != null) {
					System.out.println("responseJSON=" + new Gson().toJson(marketMatchingResponse, MarketMatchingResponse.class));
				} else {
					FormatUtils.getInstance().logMemoryUsage("marketMatching() marketMatchingResponse is empty.");
				}
			} else {
				FormatUtils.getInstance().logMemoryUsage("marketMatching() responseString is empty.");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return marketMatchingResponse;
	}

	public ZapierTargetUrlChangeAlert[] zapierTargetUrlChangeAlertGet(String requestUrl) {
		//long startTimestamp = System.currentTimeMillis();
		boolean isSendGetRequest = true;
		String requestParameters = null;
		ZapierTargetUrlChangeAlert[] zapierTargetUrlChangeAlertArray = null;
		//FormatUtils.getInstance().logMemoryUsage("zapierTargetUrlChangeAlertGet() requestUrl=" + requestUrl);
		String responseString = null;
		try {
			responseString = HttpUtils.getInstance().getResponseString(requestUrl, isSendGetRequest, requestParameters);
			if (StringUtils.isNotBlank(responseString)) {
				zapierTargetUrlChangeAlertArray = new Gson().fromJson(responseString, ZapierTargetUrlChangeAlert[].class);
				if (zapierTargetUrlChangeAlertArray != null) {
					System.out.println("responseJSON=" + new Gson().toJson(zapierTargetUrlChangeAlertArray, ZapierTargetUrlChangeAlert[].class));
				} else {
					FormatUtils.getInstance().logMemoryUsage("zapierTargetUrlChangeAlertGet() zapierTargetUrlChangeAlertArray is null.");
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		//FormatUtils.getInstance().logMemoryUsage(
		//		"zapierTargetUrlChangeAlertGet() ends. requestUrl=" + requestUrl + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return zapierTargetUrlChangeAlertArray;
	}
}
