package com.actonia.service;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

import com.actonia.dao.DomainSearchEngineRelDAO;
import com.actonia.dao.OwnDomainEntityDAO;
import com.actonia.dao.OwnDomainSettingEntityDAO;
import com.actonia.entity.DomainSearchEngineRelEntity;
import com.actonia.entity.EngineCountryLanguageMappingEntity;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.entity.OwnDomainSettingEntity;
import com.actonia.service.ECLMappingService;
import com.actonia.service.ScKeywordRankService;
import com.actonia.service.ScKeywordRankService.ScEngine;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.SpringBeanFactory;

public class OwnDomainService {

	private static OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
	private static DomainSearchEngineRelDAO domainSearchEngineRelDAO;
	private static OwnDomainEntityDAO ownDomainEntityDAO;

	static {
		ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
		domainSearchEngineRelDAO = SpringBeanFactory.getBean("domainSearchEngineRelDAO");
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
	}

	//Cee - https://www.wrike.com/open.htm?id=457657544
	public static List<Map<String, Object>> getSearchEnginesForJson4(int ownDomainId, boolean avoidPauseEngines) {
		List<Map<String, Object>> engineMapList = new ArrayList<Map<String, Object>>();

		OwnDomainSettingEntity ownDomainSetting = getDomainSettingByOwnDomainId(ownDomainId);
		boolean domainPausedRanking = ownDomainSetting.isPausedRanking();
		boolean enableGeoMobile = false;
		boolean enableGeoDesktop = false;
		//boolean enableGeoMobile = ownDomainSetting.isGeoMobileEnable();
		//boolean enableGeoDesktop = ownDomainSetting.getEnableCityRank() == null ? false : ownDomainSetting.getEnableCityRank();

		//Cee - https://www.wrike.com/open.htm?id=375484923
		//query from owndomain_engine_rel
		if (ScKeywordRankService.getPrimaryEngine(ownDomainId) != null) {
			return ScKeywordRankService.getOwndomainEngineMap(ownDomainId, enableGeoMobile, enableGeoDesktop);
		}

		//primary
		OwnDomainEntity ownDomain = getOwnDomain(ownDomainId);
		String primaryCountry = ownDomain.getSearchEngineCountry();
		int primaryLanguageId = ScKeywordRankService.getSearchLanguageId(ownDomain);
		int primarySearchEngineId = ScKeywordRankService.getSearchEngineId(ownDomain);
		String primaryDevice = "d";
		if (ownDomain.isMobileDomain()) {
			primaryDevice = "m";
		}

		Map<String, Object> searchEngineMap = new LinkedHashMap<String, Object>();
		//Cee - https://www.wrike.com/open.htm?id=423184188
		String primaryEngineName = ownDomain.getSearchEngineDisplayName();
		if (StringUtils.isBlank(primaryEngineName)) {
			primaryEngineName = shortenEngineName2(ownDomain.getSearchEngine(), primaryLanguageId, ownDomain.getSearchEngineCountry(), primaryDevice,
					domainPausedRanking);
		}

		if (ScEngine.isMobile(primarySearchEngineId, true)) {
			primarySearchEngineId = ScEngine.mobileToDesktop(primarySearchEngineId, 1);
			primaryDevice = "m";
		}

		searchEngineMap.put("engineId", primarySearchEngineId);
		searchEngineMap.put("languageId", primaryLanguageId);
		searchEngineMap.put("engineName", primaryEngineName);
		searchEngineMap.put("isPrimaryEngine", true);
		searchEngineMap.put("isPausedRank", domainPausedRanking);
		searchEngineMap.put("device", primaryDevice);
		searchEngineMap.put("supportGeo", ScKeywordRankService.isSupportGeoRank(primaryEngineName, enableGeoDesktop, enableGeoMobile));
		searchEngineMap.put("country", primaryCountry);

		//Cee - for saved Dashboard Json mapping
		searchEngineMap.put("engineIdOld", ScKeywordRankService.getSearchEngineId(ownDomain));
		searchEngineMap.put("deviceOld", ownDomain.isMobileDomain() ? "m" : "");

		engineMapList.add(searchEngineMap);

		//secondary
		List<DomainSearchEngineRelEntity> otherEngines = domainSearchEngineRelDAO.getByOwnDomain(ownDomainId, avoidPauseEngines);
		if (otherEngines != null && !otherEngines.isEmpty()) {
			for (DomainSearchEngineRelEntity rel : otherEngines) {

				Integer languageId = rel.getSearchEngineLanguageid();
				if (languageId == null || languageId.intValue() <= 0) {
					languageId = primaryLanguageId;
				}

				boolean enginePauseRank = rel.isPausedRanking();
				if (domainPausedRanking) {
					enginePauseRank = true;
				}
				Integer secondEngineId = rel.getSearchEngineId();
				if (ScEngine.isMobile(rel.getSearchEngineId(), true)) {
					secondEngineId = ScEngine.mobileToDesktop(rel.getSearchEngineId(), primarySearchEngineId);
				}

				searchEngineMap = new LinkedHashMap<String, Object>();
				searchEngineMap.put("engineId", secondEngineId);
				searchEngineMap.put("languageId", languageId);

				//Cee - https://www.wrike.com/open.htm?id=423184188
				String engineDisplayName = rel.getSearchEngineDisplayName();
				if (StringUtils.isBlank(engineDisplayName)) {
					//Cee - https://www.wrike.com/open.htm?id=545568765
					engineDisplayName = rel.getSearchEngine();
					if (StringUtils.startsWithIgnoreCase(rel.getDevice(), "m")) {
						int itemEngineId = rel.getSearchEngineId();
						if (ScEngine.isMobile(itemEngineId, true)) {
							itemEngineId = ScEngine.mobileToDesktop(itemEngineId, primarySearchEngineId);
						}
						List<EngineCountryLanguageMappingEntity> eclList = ECLMappingService.getECLMappingInfos(itemEngineId, languageId);
						if (eclList != null && !eclList.isEmpty()) {
							engineDisplayName = eclList.get(0).getEngineQueryName();
						}
					}
					engineDisplayName = shortenEngineName2(engineDisplayName, languageId, ownDomain.getSearchEngineCountry(), rel.getDevice(), enginePauseRank);
				}
				searchEngineMap.put("engineName", engineDisplayName);
				searchEngineMap.put("isPrimaryEngine", false);
				searchEngineMap.put("isPausedRank", enginePauseRank);

				//Cee - https://www.wrike.com/open.htm?id=513105019
				if (ownDomain.isMobileDomain()) {
					searchEngineMap.put("supportGeo", ScKeywordRankService.isSupportGeoRank(engineDisplayName, enableGeoDesktop, false));
				} else {
					searchEngineMap.put("supportGeo", ScKeywordRankService.isSupportGeoRank(engineDisplayName, false, enableGeoMobile));
				}

				searchEngineMap.put("device", StringUtils.isBlank(rel.getDevice()) ? "d" : rel.getDevice());

				searchEngineMap.put("country", ECLMappingService.getCountryShortName(secondEngineId, languageId));

				//Cee - for saved Dashboard Json mapping
				searchEngineMap.put("engineIdOld", rel.getSearchEngineId());
				searchEngineMap.put("deviceOld", StringUtils.isBlank(rel.getDeviceForUI()) ? "" : rel.getDeviceForUI());

				engineMapList.add(searchEngineMap);
			}
		}

		updateEngineSupportGeoFlag(enableGeoDesktop, enableGeoMobile, engineMapList);

		return engineMapList;
	}

	private static OwnDomainSettingEntity getDomainSettingByOwnDomainId(int ownDomainId) {

		return ownDomainSettingEntityDAO.getSettingByDomainId(ownDomainId);
	}

	/**
	 * find OwnDomainEntity by JDBC DAO
	 * @param ownDomainId
	 * @return
	 */
	public static OwnDomainEntity getOwnDomain(int ownDomainId) {
		return ownDomainEntityDAO.getById(ownDomainId);
	}

	//Cee - https://www.wrike.com/open.htm?id=388309290
	private static String shortenEngineName2(String searchEngineName, Integer languageId, String country, String device, boolean isPausedRank) {
		if (StringUtils.isBlank(searchEngineName)) {
			return searchEngineName;
		}

		String languageStr = "";
		if (languageId != null) {
			//			//Cee - https://www.wrike.com/open.htm?id=208258937
			//			if (ScLanguageEntity.LANGUAGE_ALIAS_MAP.containsKey(languageId)) {
			//				languageStr = " (" + ScLanguageEntity.LANGUAGE_ALIAS_MAP.get(languageId) + ")";
			//			}

			String languageName = ECLMappingService.getLanguageShortName(languageId);
			if (StringUtils.isNotBlank(languageName)) {
				languageStr = " (" + StringUtils.lowerCase(languageName) + ")";
			}
		}

		String deviceWord = "";
		if (StringUtils.startsWithIgnoreCase(device, "m") || StringUtils.startsWith(searchEngineName, "m.")) {
			deviceWord = " Mobile";
		}

		//Cee - https://www.wrike.com/open.htm?id=423184188
		//remove TLD from m.google.com
		if (StringUtils.containsIgnoreCase(deviceWord, "Mobile") && StringUtils.startsWith(searchEngineName, "m.") && StringUtils.endsWith(searchEngineName, ".com")) {

			searchEngineName = StringUtils.removeStart(searchEngineName, "m.");
			searchEngineName = StringUtils.removeEnd(searchEngineName, ".com");
		}

		String result = FormatUtils.getInstance().upperCaseFirstCharacter(searchEngineName);
		if (StringUtils.isNotBlank(languageStr)) {
			result = result + languageStr;
		}
		if (StringUtils.isNotBlank(deviceWord)) {
			result = result + deviceWord;
		}

		//Cee - https://www.wrike.com/open.htm?id=698658417
		if (isPausedRank) {
			result = result + " (Paused)";
		}

		return result;
	}

	//Cee - https://www.wrike.com/open.htm?id=528848873
	private static void updateEngineSupportGeoFlag(boolean enableGeoDesktop, boolean enableGeoMobile, List<Map<String, Object>> engineList) {
		if (engineList == null) {
			return;
		}

		int primaryEngineId = 0;
		int primaryLanguageId = 0;
		for (Map<String, Object> engineMap : engineList) {
			if (engineMap.containsKey("isPrimaryEngine") && (Boolean) engineMap.get("isPrimaryEngine")) {
				primaryEngineId = (int) engineMap.get("engineId");
				primaryLanguageId = (int) engineMap.get("languageId");
				break;
			}
		}

		for (Map<String, Object> engineMap : engineList) {
			int engineId = (int) engineMap.get("engineId");
			int languageId = (int) engineMap.get("languageId");
			String device = (String) engineMap.get("device");
			String engineName = (String) engineMap.get("engineName");

			if (StringUtils.containsIgnoreCase(engineName, "Google")) {
				if (StringUtils.equals("d", device) && enableGeoDesktop && engineId == primaryEngineId && languageId == primaryLanguageId) {
					engineMap.put("supportGeo", true);
				}
				if (StringUtils.equals("m", device) && enableGeoMobile && engineId == primaryEngineId && languageId == primaryLanguageId) {
					engineMap.put("supportGeo", true);
				}
			}
		}
	}
}
