package com.actonia.service;

import java.util.Properties;

import org.apache.commons.lang.StringUtils;

import com.actonia.IConstants;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.HttpUtils;

public class SerpAnalyzerApiService {

	private static String endPoint = null;

	private SerpAnalyzerApiService() {
	}

	public String sendSerpAnalyzerRequest(String requestUrl, String requestParameters) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("sendSerpAnalyzerRequest() begins. requestUrl=" + requestUrl + ",requestParameters=" + requestParameters);
		long startTimestamp = System.currentTimeMillis();
		boolean isSendGetRequest = false;
		String responseString = HttpUtils.getInstance().getResponseString(requestUrl, isSendGetRequest, requestParameters);
		FormatUtils.getInstance()
				.logMemoryUsage("sendSerpAnalyzerRequest() ends. responseString=" + responseString + ",elapsed(ms.)=" + (System.currentTimeMillis() - startTimestamp));
		return responseString;
	}

	public String getEndPoint() {
		if (StringUtils.isBlank(endPoint)) {
			try {
				Properties properties = new Properties();
				properties.load(SerpAnalyzerApiService.class.getResourceAsStream("/polite.crawl.v2.0.properties"));

				// serp.analyzer.api.endpoint
				endPoint = properties.getProperty(IConstants.RUNTIME_PROPERTY_NAME_SERP_ANALYZER_API_ENDPOINT);
				FormatUtils.getInstance().logMemoryUsage("getEndPoint() endPoint=" + endPoint);

			} catch (Exception e) {
				e.printStackTrace();
				FormatUtils.getInstance().logMemoryUsage("getEndPoint() exception message=" + e.getMessage());
				System.exit(-1);
			}
		}
		return endPoint;
	}
}
