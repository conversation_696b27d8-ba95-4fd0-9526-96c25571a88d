package com.actonia.service;

import java.util.*;
import java.util.stream.Collectors;

import com.actonia.content.guard.change.ChangeIndicatorEnum;
import com.actonia.entity.HtmlChange;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.BooleanUtils;

import com.actonia.IConstants;
import com.actonia.dao.OwnDomainSettingEntityDAO;
import com.actonia.dao.TargetUrlChangeIndClickHouseDAO;
import com.actonia.entity.ContentGuardChangeTrackingEntity;
import com.actonia.entity.TargetUrlChangeIndClickHouseEntity;
import com.actonia.exception.ExceededTotalRowsException;
import com.actonia.utils.ContentGuardUtils;
import com.actonia.utils.CrawlerUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.utils.TargetUrlChangeUtils;
import com.actonia.value.object.*;
import org.springframework.util.StopWatch;

@Log4j2
public class ListChangeIndicatorsService {

	//private boolean isDebug = false;
	private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
	private final HtmlChangeService htmlChangeService;
	private final Map<String, Integer> changeIndicatorIdMap;

	public ListChangeIndicatorsService() {
		super();
		this.ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
		this.htmlChangeService = new HtmlChangeService();
		final List<ContentGuardChangeTrackingEntity> contentGuardChangeTrackingEntities = ContentGuardUtils.getInstance().contentGuardChangeTrackingWithIdList();
		this.changeIndicatorIdMap = contentGuardChangeTrackingEntities.stream().collect(Collectors.toMap(ContentGuardChangeTrackingEntity::getIndicator, ContentGuardChangeTrackingEntity::getId));
	}

	public TargetUrlChangeResponse getList(String command, TargetUrlChangeRequest targetUrlChangeRequest) throws Exception {
		TargetUrlChangeResponse targetUrlChangeResponse = new TargetUrlChangeResponse();
		WebServiceError webServiceError = null;
		List<TargetUrlChangeSummary> targetUrlChangeSummaryList = null;
		List<TargetUrlChangeIndicatorDetail> targetUrlChangeIndicatorDetailList = null;
		TargetUrlChangeTotal targetUrlChangeTotal = null;
		List<ResponseCodeSummary> responseCodeSummaryList;
		List<TargetUrlChangeDailySummary> targetUrlChangeDailySummaryList = null;
		List<ChangeIndicatorTotalUrls> changeIndicatorTotalUrlsList = null;
		long startTimestamp = System.currentTimeMillis();
		String header = null;
		String detail = null;
		List<String> stringList = null;
		String downloadAllLink = null;
		String dateFormatDay = null;

		StopWatch stopWatch = new StopWatch("getList()");
		try {
			stopWatch.start("calculateTotalRows");
			// total rows
			targetUrlChangeTotal = TargetUrlChangeUtils.getInstance().calculateTotalRows(command, targetUrlChangeRequest);
			stopWatch.stop();
			if (targetUrlChangeTotal.getTotalRows() == 0) {
				targetUrlChangeResponse.setSuccess(false);
				webServiceError = new WebServiceError();
				webServiceError.setError_code(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_DATA_NOT_AVAILABLE);
				webServiceError.setError_message(TargetUrlChangeUtils.getInstance().getErrorMessage(webServiceError.getError_code(), null));
				targetUrlChangeResponse.setError(webServiceError);
			} else {

				stopWatch.start("getTargetUrlChangeIndicatorDetails");
				// detail
				targetUrlChangeIndicatorDetailList = getTargetUrlChangeIndicatorDetailList(targetUrlChangeRequest);
				log.info("detailList size: {}", targetUrlChangeIndicatorDetailList.size());
				stopWatch.stop();
				targetUrlChangeResponse.setChange_indicator_list(targetUrlChangeIndicatorDetailList);

				stopWatch.start("getTargetUrlChangeSummaryList");
				// change indicator summary
				targetUrlChangeSummaryList = getTargetUrlChangeSummaryList(targetUrlChangeRequest);
				stopWatch.stop();
				targetUrlChangeResponse.setSummary_list(targetUrlChangeSummaryList);

				// from response code to response code summary
				responseCodeSummaryList = getResponseCodeSummaryList(targetUrlChangeRequest);
				targetUrlChangeResponse.setResponse_code_summary_list(responseCodeSummaryList);

				targetUrlChangeResponse.setSuccess(true);
				targetUrlChangeResponse.setPage_number(targetUrlChangeRequest.getPage_number());
				targetUrlChangeResponse.setRows_per_page(targetUrlChangeRequest.getRows_per_page());
				targetUrlChangeResponse.setSort_by(targetUrlChangeRequest.getSort_by());
				targetUrlChangeResponse.setTotal_rows(targetUrlChangeTotal.getTotalRows());
				targetUrlChangeResponse.setEnd_of_detail_list_flag(targetUrlChangeTotal.isLastPage());
				targetUrlChangeResponse.setElapsed_millisecond(System.currentTimeMillis() - startTimestamp);

				// daily summary
				targetUrlChangeDailySummaryList = getTargetUrlChangeDailySummaryList(targetUrlChangeResponse);
				targetUrlChangeResponse.setDaily_summary_list(targetUrlChangeDailySummaryList);
				changeIndicatorTotalUrlsList = getChangeIndicatorTotalUrlsList(targetUrlChangeResponse.getDaily_summary_list());
				targetUrlChangeResponse.setChange_indicator_total_urls_list(changeIndicatorTotalUrlsList);

				if (StringUtils.isNotBlank(targetUrlChangeRequest.getSummary())) {
					if (StringUtils.equalsIgnoreCase(targetUrlChangeRequest.getSummary(), IConstants.DAILY)) {
						targetUrlChangeResponse.setSummary_list(null);
						targetUrlChangeResponse.setResponse_code_summary_list(null);
					} else if (StringUtils.equalsIgnoreCase(targetUrlChangeRequest.getSummary(), IConstants.HOURLY)) {
						targetUrlChangeResponse.setDaily_summary_list(null);
					}
				}
			}
		} catch (ExceededTotalRowsException e) {
			targetUrlChangeResponse.setSuccess(false);
			webServiceError = new WebServiceError();
			webServiceError.setError_code(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_OUTSIDE_DATA_RANGE);
			webServiceError.setError_message(TargetUrlChangeUtils.getInstance().getErrorMessage(webServiceError.getError_code(), String.valueOf(e.getTotalRows())));
			targetUrlChangeResponse.setError(webServiceError);
		}

		if (BooleanUtils.isTrue(targetUrlChangeResponse.getSuccess()) && BooleanUtils.isTrue(targetUrlChangeRequest.getDownload_all_ind())) {
			stringList = new ArrayList<String>();

			// header
			header = getDownloadAllHeader();
			stringList.add(header);

			dateFormatDay = getDateFormatDay(targetUrlChangeRequest.getDomain_id());

			// detail
			for (TargetUrlChangeIndicatorDetail targetUrlChangeIndicatorDetail : targetUrlChangeResponse.getChange_indicator_list()) {
				detail = getDownloadAllDetail(targetUrlChangeIndicatorDetail, dateFormatDay);
				stringList.add(detail);
			}

			switch (IConstants.SAVE_FILE_TO_REMOTE_TYPE) {
				case IConstants.SAVE_FILE_TO_SEAGATE:
					downloadAllLink = TargetUrlChangeUtils.getInstance().uploadDownloadAllFileToSeagate(IConstants.CONTENT_GUARD_DOWNLOAD_ALL_FILE_NAME, stringList);
					break;
				case IConstants.SAVE_FILE_TO_FTP:
				default:
					downloadAllLink = TargetUrlChangeUtils.getInstance().uploadDownloadAllFileToFTP(IConstants.CONTENT_GUARD_DOWNLOAD_ALL_FILE_NAME, stringList);
					break;
			}

			targetUrlChangeResponse.setSuccess(true);
			targetUrlChangeResponse.setDownload_all_link(downloadAllLink);
		}

		log.info(stopWatch.prettyPrint());
		return targetUrlChangeResponse;
	}

	public TargetUrlChangeResponseV2 getListV2(String command, TargetUrlChangeRequest targetUrlChangeRequest) throws Exception {
		TargetUrlChangeResponseV2 targetUrlChangeResponse = new TargetUrlChangeResponseV2();
		WebServiceError webServiceError = null;
		List<TargetUrlChangeSummary> targetUrlChangeSummaryList = null;
		List<TargetUrlChangeIndicatorDetail> targetUrlChangeIndicatorDetailList = null;
		TargetUrlChangeTotal targetUrlChangeTotal = null;
		List<ResponseCodeSummary> responseCodeSummaryList;
		List<TargetUrlChangeDailySummary> targetUrlChangeDailySummaryList = null;
		List<ChangeIndicatorTotalUrls> changeIndicatorTotalUrlsList = null;
		long startTimestamp = System.currentTimeMillis();
		String header = null;
		String detail = null;
		List<String> stringList = null;
		String downloadAllLink = null;
		String dateFormatDay = null;

		StopWatch stopWatch = new StopWatch("getListV2()");
		try {
			stopWatch.start("getTargetUrlChangeIndicatorDetails");
			targetUrlChangeRequest.setChangeIndicatorIdMap(this.changeIndicatorIdMap);
			// total rows
			targetUrlChangeTotal = htmlChangeService.calculateTotalRows(false, targetUrlChangeRequest);
			stopWatch.stop();
			if (targetUrlChangeTotal.getTotalRows() == 0) {
				targetUrlChangeResponse.setSuccess(false);
				webServiceError = new WebServiceError();
				webServiceError.setError_code(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_DATA_NOT_AVAILABLE);
				webServiceError.setError_message(TargetUrlChangeUtils.getInstance().getErrorMessage(webServiceError.getError_code(), null));
				targetUrlChangeResponse.setError(webServiceError);
			} else {

				stopWatch.start("getListV2()::getTargetUrlChangeIndicatorDetails");
				// detail
				final List<HtmlChange> htmlChangeDetailList = htmlChangeService.getTargetUrlChangeIndicatorDetails(targetUrlChangeRequest);
				stopWatch.stop();
				targetUrlChangeResponse.setChange_indicator_list(htmlChangeDetailList);

				stopWatch.start("get change indicator summary");
				// change indicator summary
				targetUrlChangeSummaryList = htmlChangeService.getTargetUrlChangeSummaryList(targetUrlChangeRequest);
				stopWatch.stop();
				targetUrlChangeResponse.setSummary_list(targetUrlChangeSummaryList);

				// from response code to response code summary
				responseCodeSummaryList = getResponseCodeSummaryList(targetUrlChangeRequest);
				targetUrlChangeResponse.setResponse_code_summary_list(responseCodeSummaryList);

				targetUrlChangeResponse.setSuccess(true);
				targetUrlChangeResponse.setPage_number(targetUrlChangeRequest.getPage_number());
				targetUrlChangeResponse.setRows_per_page(targetUrlChangeRequest.getRows_per_page());
				targetUrlChangeResponse.setSort_by(targetUrlChangeRequest.getSort_by());
				targetUrlChangeResponse.setTotal_rows(targetUrlChangeTotal.getTotalRows());
				targetUrlChangeResponse.setEnd_of_detail_list_flag(targetUrlChangeTotal.isLastPage());
				targetUrlChangeResponse.setElapsed_millisecond(System.currentTimeMillis() - startTimestamp);

				// daily summary
				targetUrlChangeDailySummaryList = getTargetUrlChangeDailySummaryList(targetUrlChangeResponse);
				targetUrlChangeResponse.setDaily_summary_list(targetUrlChangeDailySummaryList);
				changeIndicatorTotalUrlsList = getChangeIndicatorTotalUrlsList(targetUrlChangeResponse.getDaily_summary_list());
				targetUrlChangeResponse.setChange_indicator_total_urls_list(changeIndicatorTotalUrlsList);

				if (StringUtils.isNotBlank(targetUrlChangeRequest.getSummary())) {
					if (StringUtils.equalsIgnoreCase(targetUrlChangeRequest.getSummary(), IConstants.DAILY)) {
						targetUrlChangeResponse.setSummary_list(null);
						targetUrlChangeResponse.setResponse_code_summary_list(null);
					} else if (StringUtils.equalsIgnoreCase(targetUrlChangeRequest.getSummary(), IConstants.HOURLY)) {
						targetUrlChangeResponse.setDaily_summary_list(null);
					}
				}
			}
		} catch (ExceededTotalRowsException e) {
			targetUrlChangeResponse.setSuccess(false);
			webServiceError = new WebServiceError();
			webServiceError.setError_code(IConstants.MSG_CD_TARGET_URL_CHANGE_REQUEST_PARM_OUTSIDE_DATA_RANGE);
			webServiceError.setError_message(TargetUrlChangeUtils.getInstance().getErrorMessage(webServiceError.getError_code(), String.valueOf(e.getTotalRows())));
			targetUrlChangeResponse.setError(webServiceError);
		}

		if (BooleanUtils.isTrue(targetUrlChangeResponse.getSuccess()) && BooleanUtils.isTrue(targetUrlChangeRequest.getDownload_all_ind())) {
			stringList = new ArrayList<String>();

			// header
			header = getDownloadAllHeader();
			stringList.add(header);

			final List<TargetUrlChangeIndClickHouseEntity> targetUrlChangeIndClickHouseEntityList = targetUrlChangeResponse.getChange_indicator_list().stream()
					.map(htmlChange -> {
						final ChangeIndicatorEnum changeIndicatorEnum = ChangeIndicatorEnum.fromIndicator(htmlChange.getChange_indicator());
						return changeIndicatorEnum.getStrategy().convertToTargetUrlChangeIndClickHouseEntity(htmlChange);
					}).collect(Collectors.toList());
			targetUrlChangeIndicatorDetailList = this.convertTargetUrlChangeIndClickHouseEntityList(targetUrlChangeIndClickHouseEntityList);
			dateFormatDay = getDateFormatDay(targetUrlChangeRequest.getDomain_id());

			// detail
			for (TargetUrlChangeIndicatorDetail targetUrlChangeIndicatorDetail : targetUrlChangeIndicatorDetailList) {
				detail = getDownloadAllDetail(targetUrlChangeIndicatorDetail, dateFormatDay);
				stringList.add(detail);
			}

			switch (IConstants.SAVE_FILE_TO_REMOTE_TYPE) {
				case IConstants.SAVE_FILE_TO_SEAGATE:
					downloadAllLink = TargetUrlChangeUtils.getInstance().uploadDownloadAllFileToSeagate(IConstants.CONTENT_GUARD_DOWNLOAD_ALL_FILE_NAME, stringList);
					break;
				case IConstants.SAVE_FILE_TO_FTP:
				default:
					downloadAllLink = TargetUrlChangeUtils.getInstance().uploadDownloadAllFileToFTP(IConstants.CONTENT_GUARD_DOWNLOAD_ALL_FILE_NAME, stringList);
					break;
			}

			targetUrlChangeResponse.setSuccess(true);
			targetUrlChangeResponse.setDownload_all_link(downloadAllLink);
		}
		log.info("getListV2()::end: {}", stopWatch.prettyPrint());
		return targetUrlChangeResponse;
	}

	private List<TargetUrlChangeDailySummary> getTargetUrlChangeDailySummaryList(TargetUrlChangeResponseV2 targetUrlChangeResponse) {
		List<TargetUrlChangeDailySummary> targetUrlChangeDailySummaryList = null;
		TargetUrlChangeDailySummary targetUrlChangeDailySummary = null;
		List<TargetUrlChangeSummary> targetUrlChangeSummaryList = null;
		List<ResponseCodeSummary> responseCodeSummaryList = null;
		List<ChangeIndicatorTotalUrls> changeIndicatorTotalUrlsList = null;
		ChangeIndicatorTotalUrls testChangeIndicatorTotalUrls = null;
		String crawlDate = null;

		// map key: crawl date
		// map value: TargetUrlChangeDailySummary
		SortedMap<String, TargetUrlChangeDailySummary> crawlDateTargetUrlChangeDailySummaryMap = new TreeMap<String, TargetUrlChangeDailySummary>();

		// map key: crawl date
		// map value: change indicator total URLs map
		SortedMap<String, SortedMap<String, Integer>> crawlDateChangeIndicatorTotalUrlsMapMap = new TreeMap<String, SortedMap<String, Integer>>();

		// map key: change indicator
		// map value: total URLs
		SortedMap<String, Integer> changeIndicatorTotalUrlsMap = null;

		// map key = crawl date
		// map value = response code previous (response code current total URLs map) map
		SortedMap<String, SortedMap<String, SortedMap<String, Integer>>> crawlDateResponseCodePreviousResponseCodeCurrentTotalUrlsMapMap = new TreeMap<String, SortedMap<String, SortedMap<String, Integer>>>();

		// map key: response code previous
		// map value : response code current total URLs map
		SortedMap<String, SortedMap<String, Integer>> responseCodePreviousResponseCodeCurrentTotalUrlsMap = null;

		SortedMap<String, Integer> responseCodeCurrentTotalUrlsMap = null;

		Integer totalUrls = null;
		String responseCodePrevious = null;
		String responseCodeCurrent = null;
		String changeIndicator = null;
		ResponseCodeTotalUrls testResponseCodeTotalUrls = null;
		List<ResponseCodeTotalUrls> responseCodeTotalUrlsList = null;

		targetUrlChangeSummaryList = targetUrlChangeResponse.getSummary_list();
		if (targetUrlChangeSummaryList != null && targetUrlChangeSummaryList.size() > 0) {
			targetUrlChangeDailySummaryList = new ArrayList<TargetUrlChangeDailySummary>();
			for (TargetUrlChangeSummary targetUrlChangeSummary : targetUrlChangeSummaryList) {
				crawlDate = StringUtils.substringBefore(targetUrlChangeSummary.getCrawl_date_hour(), IConstants.ONE_SPACE);

				if (crawlDateTargetUrlChangeDailySummaryMap.containsKey(crawlDate)) {
					targetUrlChangeDailySummary = crawlDateTargetUrlChangeDailySummaryMap.get(crawlDate);
				} else {
					targetUrlChangeDailySummary = new TargetUrlChangeDailySummary();
				}
				targetUrlChangeDailySummary.setCrawl_date(crawlDate);
				targetUrlChangeDailySummary.setTotal_added(targetUrlChangeDailySummary.getTotal_added() + targetUrlChangeSummary.getTotal_added());
				targetUrlChangeDailySummary.setTotal_modified(targetUrlChangeDailySummary.getTotal_modified() + targetUrlChangeSummary.getTotal_modified());
				targetUrlChangeDailySummary.setTotal_removed(targetUrlChangeDailySummary.getTotal_removed() + targetUrlChangeSummary.getTotal_removed());
				targetUrlChangeDailySummary.setTotal_severity_critical(targetUrlChangeDailySummary.getTotal_severity_critical() + targetUrlChangeSummary.getTotal_severity_critical());
				targetUrlChangeDailySummary.setTotal_severity_high(targetUrlChangeDailySummary.getTotal_severity_high() + targetUrlChangeSummary.getTotal_severity_high());
				targetUrlChangeDailySummary.setTotal_severity_medium(targetUrlChangeDailySummary.getTotal_severity_medium() + targetUrlChangeSummary.getTotal_severity_medium());
				targetUrlChangeDailySummary.setTotal_severity_low(targetUrlChangeDailySummary.getTotal_severity_low() + targetUrlChangeSummary.getTotal_severity_low());
				crawlDateTargetUrlChangeDailySummaryMap.put(crawlDate, targetUrlChangeDailySummary);

				if (crawlDateChangeIndicatorTotalUrlsMapMap.containsKey(crawlDate)) {
					changeIndicatorTotalUrlsMap = crawlDateChangeIndicatorTotalUrlsMapMap.get(crawlDate);
				} else {
					changeIndicatorTotalUrlsMap = new TreeMap<String, Integer>();
				}

				if (targetUrlChangeSummary.getChange_indicator_total_urls_array() != null && targetUrlChangeSummary.getChange_indicator_total_urls_array().length > 0) {
					for (ChangeIndicatorTotalUrls changeIndicatorTotalUrls : targetUrlChangeSummary.getChange_indicator_total_urls_array()) {
						changeIndicator = changeIndicatorTotalUrls.getChange_indicator();
						if (changeIndicatorTotalUrlsMap.containsKey(changeIndicator)) {
							totalUrls = changeIndicatorTotalUrlsMap.get(changeIndicator);
						} else {
							totalUrls = 0;
						}
						totalUrls = totalUrls + changeIndicatorTotalUrls.getTotal_urls();
						changeIndicatorTotalUrlsMap.put(changeIndicator, totalUrls);
					}
				}
				crawlDateChangeIndicatorTotalUrlsMapMap.put(crawlDate, changeIndicatorTotalUrlsMap);
			}
		}

		responseCodeSummaryList = targetUrlChangeResponse.getResponse_code_summary_list();
		if (responseCodeSummaryList != null && responseCodeSummaryList.size() > 0) {
			for (ResponseCodeSummary responseCodeSummary : responseCodeSummaryList) {
				crawlDate = StringUtils.substringBefore(responseCodeSummary.getCrawl_date_hour(), IConstants.ONE_SPACE);
				if (crawlDateResponseCodePreviousResponseCodeCurrentTotalUrlsMapMap.containsKey(crawlDate)) {
					responseCodePreviousResponseCodeCurrentTotalUrlsMap = crawlDateResponseCodePreviousResponseCodeCurrentTotalUrlsMapMap.get(crawlDate);
				} else {
					responseCodePreviousResponseCodeCurrentTotalUrlsMap = new TreeMap<String, SortedMap<String, Integer>>();
				}
				for (ResponseCodeTotalUrls responseCodeTotalUrls : responseCodeSummary.getResponse_code_total_urls_array()) {
					responseCodePrevious = responseCodeTotalUrls.getResponse_code_previous();
					responseCodeCurrent = responseCodeTotalUrls.getResponse_code_current();
					if (responseCodePreviousResponseCodeCurrentTotalUrlsMap.containsKey(responseCodePrevious)) {
						responseCodeCurrentTotalUrlsMap = responseCodePreviousResponseCodeCurrentTotalUrlsMap.get(responseCodePrevious);
					} else {
						responseCodeCurrentTotalUrlsMap = new TreeMap<String, Integer>();
					}
					if (responseCodeCurrentTotalUrlsMap.containsKey(responseCodeCurrent)) {
						totalUrls = responseCodeCurrentTotalUrlsMap.get(responseCodeCurrent);
					} else {
						totalUrls = 0;
					}
					totalUrls = totalUrls + responseCodeTotalUrls.getTotal_urls();
					responseCodeCurrentTotalUrlsMap.put(responseCodeCurrent, totalUrls);
					responseCodePreviousResponseCodeCurrentTotalUrlsMap.put(responseCodePrevious, responseCodeCurrentTotalUrlsMap);
				}
				crawlDateResponseCodePreviousResponseCodeCurrentTotalUrlsMapMap.put(crawlDate, responseCodePreviousResponseCodeCurrentTotalUrlsMap);
			}
		}

		for (String testCrawlDate : crawlDateTargetUrlChangeDailySummaryMap.keySet()) {
			targetUrlChangeDailySummary = crawlDateTargetUrlChangeDailySummaryMap.get(testCrawlDate);
			if (crawlDateChangeIndicatorTotalUrlsMapMap.containsKey(testCrawlDate)) {
				changeIndicatorTotalUrlsList = new ArrayList<ChangeIndicatorTotalUrls>();
				changeIndicatorTotalUrlsMap = crawlDateChangeIndicatorTotalUrlsMapMap.get(testCrawlDate);
				for (String testChangeIndicator : changeIndicatorTotalUrlsMap.keySet()) {
					testChangeIndicatorTotalUrls = new ChangeIndicatorTotalUrls();
					testChangeIndicatorTotalUrls.setChange_indicator(testChangeIndicator);
					testChangeIndicatorTotalUrls.setTotal_urls(changeIndicatorTotalUrlsMap.get(testChangeIndicator));
					changeIndicatorTotalUrlsList.add(testChangeIndicatorTotalUrls);
				}
				targetUrlChangeDailySummary.setChange_indicator_total_urls_array(changeIndicatorTotalUrlsList.toArray(new ChangeIndicatorTotalUrls[0]));
			}
			if (crawlDateResponseCodePreviousResponseCodeCurrentTotalUrlsMapMap.containsKey(testCrawlDate)) {
				responseCodeTotalUrlsList = new ArrayList<ResponseCodeTotalUrls>();
				responseCodePreviousResponseCodeCurrentTotalUrlsMap = crawlDateResponseCodePreviousResponseCodeCurrentTotalUrlsMapMap.get(testCrawlDate);
				for (String testResponseCodePrevious : responseCodePreviousResponseCodeCurrentTotalUrlsMap.keySet()) {
					responseCodeCurrentTotalUrlsMap = responseCodePreviousResponseCodeCurrentTotalUrlsMap.get(testResponseCodePrevious);
					for (String testResponseCodeCurrent : responseCodeCurrentTotalUrlsMap.keySet()) {
						testResponseCodeTotalUrls = new ResponseCodeTotalUrls();
						testResponseCodeTotalUrls.setResponse_code_previous(testResponseCodePrevious);
						testResponseCodeTotalUrls.setResponse_code_current(testResponseCodeCurrent);
						testResponseCodeTotalUrls.setTotal_urls(responseCodeCurrentTotalUrlsMap.get(testResponseCodeCurrent));
						responseCodeTotalUrlsList.add(testResponseCodeTotalUrls);
					}
				}
				targetUrlChangeDailySummary.setResponse_code_total_urls_array(responseCodeTotalUrlsList.toArray(new ResponseCodeTotalUrls[0]));
			}
			targetUrlChangeDailySummaryList.add(targetUrlChangeDailySummary);
		}

		return targetUrlChangeDailySummaryList;
	}

	private String getDateFormatDay(int domainId) {
		String dateFormatDay = "MM/DD/YYYY";
		try {
			dateFormatDay = ownDomainSettingEntityDAO.getDateFormatDayByDomainId(domainId);
			dateFormatDay = FormatUtils.getInstance().trimText(dateFormatDay);
			dateFormatDay = StringUtils.replace(dateFormatDay, "Y", "y");
			dateFormatDay = StringUtils.replace(dateFormatDay, "D", "d");
		} catch (Exception e) {
			e.printStackTrace();
		}
		FormatUtils.getInstance().logMemoryUsage("getDateFormatDay() domainId=" + domainId + ",dateFormatDay=" + dateFormatDay);
		return dateFormatDay;
	}

	private String getDownloadAllHeader() {
		return IConstants.ONE_DOUBLE_QUOTES + IConstants.HEADER_COLUMN_DATE + IConstants.ONE_DOUBLE_QUOTES + IConstants.DOWNLOAD_ALL_DELIMITER
				+ IConstants.ONE_DOUBLE_QUOTES + IConstants.HEADER_COLUMN_CHANGE_ELEMENT + IConstants.ONE_DOUBLE_QUOTES + IConstants.DOWNLOAD_ALL_DELIMITER
				+ IConstants.ONE_DOUBLE_QUOTES + IConstants.HEADER_COLUMN_CHANGE_SEVERITY + IConstants.ONE_DOUBLE_QUOTES + IConstants.DOWNLOAD_ALL_DELIMITER
				+ IConstants.ONE_DOUBLE_QUOTES + IConstants.HEADER_COLUMN_URL + IConstants.ONE_DOUBLE_QUOTES + IConstants.DOWNLOAD_ALL_DELIMITER
				+ IConstants.ONE_DOUBLE_QUOTES + IConstants.HEADER_COLUMN_PREVIOUS + IConstants.ONE_DOUBLE_QUOTES + IConstants.DOWNLOAD_ALL_DELIMITER
				+ IConstants.ONE_DOUBLE_QUOTES + IConstants.HEADER_COLUMN_CURRENT + IConstants.ONE_DOUBLE_QUOTES;
	}

	private String getDownloadAllDetail(TargetUrlChangeIndicatorDetail targetUrlChangeIndicatorDetail, String dateFormatDay) throws Exception {
		StringBuilder stringBuilder = new StringBuilder();

		// date
		stringBuilder.append(IConstants.ONE_DOUBLE_QUOTES);
		String testDateString = StringUtils.substring(targetUrlChangeIndicatorDetail.getCurrent_crawl_timestamp(), 0, 10);
		Date testDate = DateUtils.parseDate(testDateString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
		String formattedDateString = DateFormatUtils.format(testDate, dateFormatDay);
		stringBuilder.append(formattedDateString);
		stringBuilder.append(IConstants.ONE_DOUBLE_QUOTES);
		stringBuilder.append(IConstants.DOWNLOAD_ALL_DELIMITER);

		// change element
		stringBuilder.append(IConstants.ONE_DOUBLE_QUOTES);
		stringBuilder.append(ContentGuardUtils.getInstance().getChangeIndicatorDescription(targetUrlChangeIndicatorDetail.getChange_indicator()));
		stringBuilder.append(IConstants.ONE_DOUBLE_QUOTES);
		stringBuilder.append(IConstants.DOWNLOAD_ALL_DELIMITER);

		// change severity
		stringBuilder.append(IConstants.ONE_DOUBLE_QUOTES);
		stringBuilder.append(ContentGuardUtils.getInstance().getChangeIndicatorSeverity(targetUrlChangeIndicatorDetail.getChange_indicator()));
		stringBuilder.append(IConstants.ONE_DOUBLE_QUOTES);
		stringBuilder.append(IConstants.DOWNLOAD_ALL_DELIMITER);

		// url
		stringBuilder.append(IConstants.ONE_DOUBLE_QUOTES);
		stringBuilder.append(targetUrlChangeIndicatorDetail.getUrl());
		stringBuilder.append(IConstants.ONE_DOUBLE_QUOTES);
		stringBuilder.append(IConstants.DOWNLOAD_ALL_DELIMITER);

		String[] stringArray = getDownloadAllPreviousCurrent(targetUrlChangeIndicatorDetail);

		// previous
		stringBuilder.append(IConstants.ONE_DOUBLE_QUOTES);
		stringBuilder.append(stringArray[0]);
		stringBuilder.append(IConstants.ONE_DOUBLE_QUOTES);
		stringBuilder.append(IConstants.DOWNLOAD_ALL_DELIMITER);

		// current
		stringBuilder.append(IConstants.ONE_DOUBLE_QUOTES);
		stringBuilder.append(stringArray[1]);
		stringBuilder.append(IConstants.ONE_DOUBLE_QUOTES);

		return stringBuilder.toString();
	}

	public String[] getDownloadAllPreviousCurrent(TargetUrlChangeIndicatorDetail targetUrlChangeIndicatorDetail) throws Exception {
		String[] stringArray = new String[2];
		StringBuilder stringBuilder = null;
		String formattedJson = null;

		// alternate_links_chg_ind
		if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.ALTERNATE_LINKS_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getAlternate_links_previous() != null && targetUrlChangeIndicatorDetail.getAlternate_links_previous().length > 0) {
				stringBuilder = null;
				for (AlternateLinks alternateLinks : targetUrlChangeIndicatorDetail.getAlternate_links_previous()) {
					if (stringBuilder == null) {
						stringBuilder = new StringBuilder();
					} else {
						stringBuilder.append(IConstants.NEWLINE);
					}
					stringBuilder.append(alternateLinks.getHref());
				}
				stringArray[0] = stringBuilder.toString();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getAlternate_links_current() != null && targetUrlChangeIndicatorDetail.getAlternate_links_current().length > 0) {
				stringBuilder = null;
				for (AlternateLinks alternateLinks : targetUrlChangeIndicatorDetail.getAlternate_links_current()) {
					if (stringBuilder == null) {
						stringBuilder = new StringBuilder();
					} else {
						stringBuilder.append(IConstants.NEWLINE);
					}
					stringBuilder.append(alternateLinks.getHref());
				}
				stringArray[1] = stringBuilder.toString();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// amphtml_href_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.AMPHTML_HREF_CHG_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getAmphtml_href_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getAmphtml_href_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getAmphtml_href_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getAmphtml_href_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// analyzed_url_s_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.ANALYZED_URL_S_CHG_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getAnalyzed_url_s_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getAnalyzed_url_s_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getAnalyzed_url_s_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getAnalyzed_url_s_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// archive_flg_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.ARCHIVE_FLG_CHG_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getArchive_flg_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getArchive_flg_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getArchive_flg_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getArchive_flg_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// base_tag_added_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.BASE_TAG_ADDED_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getBase_tag_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getBase_tag_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getBase_tag_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getBase_tag_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// base_tag_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.BASE_TAG_CHG_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getBase_tag_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getBase_tag_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getBase_tag_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getBase_tag_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// base_tag_removed_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.BASE_TAG_REMOVED_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getBase_tag_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getBase_tag_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getBase_tag_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getBase_tag_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// base_tag_target_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.BASE_TAG_TARGET_CHG_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getBase_tag_target_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getBase_tag_target_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getBase_tag_target_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getBase_tag_target_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// blocked_by_robots_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.BLOCKED_BY_ROBOTS_CHG_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getBlocked_by_robots_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getBlocked_by_robots_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getBlocked_by_robots_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getBlocked_by_robots_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// canonical_added_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.CANONICAL_ADDED_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getCanonical_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getCanonical_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getCanonical_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getCanonical_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// canonical_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.CANONICAL_CHG_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getCanonical_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getCanonical_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getCanonical_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getCanonical_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// canonical_removed_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.CANONICAL_REMOVED_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getCanonical_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getCanonical_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getCanonical_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getCanonical_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// canonical_header_flag_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.CANONICAL_HEADER_FLAG_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getCanonical_header_flag_previous() != null) {
				stringArray[0] = BooleanUtils.toStringTrueFalse(targetUrlChangeIndicatorDetail.getCanonical_header_flag_previous());
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getCanonical_header_flag_current() != null) {
				stringArray[1] = BooleanUtils.toStringTrueFalse(targetUrlChangeIndicatorDetail.getCanonical_header_flag_current());
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// canonical_header_type_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.CANONICAL_HEADER_TYPE_CHG_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getCanonical_header_type_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getCanonical_header_type_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getCanonical_header_type_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getCanonical_header_type_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// canonical_type_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.CANONICAL_TYPE_CHG_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getCanonical_type_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getCanonical_type_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getCanonical_type_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getCanonical_type_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// canonical_url_is_consistent_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.CANONICAL_URL_IS_CONSISTENT_CHG_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getCanonical_url_is_consistent_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getCanonical_url_is_consistent_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getCanonical_url_is_consistent_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getCanonical_url_is_consistent_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// content_type_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.CONTENT_TYPE_CHG_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getContent_type_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getContent_type_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getContent_type_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getContent_type_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// custom_data_added_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.CUSTOM_DATA_ADDED_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getCustom_data_previous() != null && targetUrlChangeIndicatorDetail.getCustom_data_previous().length > 0) {
				formattedJson = new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(targetUrlChangeIndicatorDetail.getCustom_data_previous());
				stringArray[0] = FormatUtils.getInstance().cleanseJson(formattedJson);
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getCustom_data_current() != null && targetUrlChangeIndicatorDetail.getCustom_data_current().length > 0) {
				formattedJson = new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(targetUrlChangeIndicatorDetail.getCustom_data_current());
				stringArray[1] = FormatUtils.getInstance().cleanseJson(formattedJson);
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// custom_data_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.CUSTOM_DATA_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getCustom_data_previous() != null && targetUrlChangeIndicatorDetail.getCustom_data_previous().length > 0) {
				formattedJson = new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(targetUrlChangeIndicatorDetail.getCustom_data_previous());
				stringArray[0] = FormatUtils.getInstance().cleanseJson(formattedJson);
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getCustom_data_current() != null && targetUrlChangeIndicatorDetail.getCustom_data_current().length > 0) {
				formattedJson = new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(targetUrlChangeIndicatorDetail.getCustom_data_current());
				stringArray[1] = FormatUtils.getInstance().cleanseJson(formattedJson);
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// custom_data_removed_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.CUSTOM_DATA_REMOVED_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getCustom_data_previous() != null && targetUrlChangeIndicatorDetail.getCustom_data_previous().length > 0) {
				formattedJson = new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(targetUrlChangeIndicatorDetail.getCustom_data_previous());
				stringArray[0] = FormatUtils.getInstance().cleanseJson(formattedJson);
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getCustom_data_current() != null && targetUrlChangeIndicatorDetail.getCustom_data_current().length > 0) {
				formattedJson = new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(targetUrlChangeIndicatorDetail.getCustom_data_current());
				stringArray[1] = FormatUtils.getInstance().cleanseJson(formattedJson);
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// description_added_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.DESCRIPTION_ADDED_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getDescription_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getDescription_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getDescription_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getDescription_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// description_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.DESCRIPTION_CHG_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getDescription_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getDescription_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getDescription_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getDescription_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// description_removed_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.DESCRIPTION_REMOVED_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getDescription_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getDescription_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getDescription_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getDescription_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// description_length_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.DESCRIPTION_LENGTH_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getDescription_length_previous() != null) {
				stringArray[0] = String.valueOf(targetUrlChangeIndicatorDetail.getDescription_length_previous());
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getDescription_length_current() != null) {
				stringArray[1] = String.valueOf(targetUrlChangeIndicatorDetail.getDescription_length_current());
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// error_message_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.ERROR_MESSAGE_CHG_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getError_message_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getError_message_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getError_message_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getError_message_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// final_response_code_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.FINAL_RESPONSE_CODE_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getFinal_response_code_previous() != null) {
				stringArray[0] = String.valueOf(targetUrlChangeIndicatorDetail.getFinal_response_code_previous());
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getFinal_response_code_current() != null) {
				stringArray[1] = String.valueOf(targetUrlChangeIndicatorDetail.getFinal_response_code_current());
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// follow_flg_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.FOLLOW_FLG_CHG_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getFollow_flg_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getFollow_flg_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getFollow_flg_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getFollow_flg_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// h1_added_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.H1_ADDED_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getH1_previous() != null && targetUrlChangeIndicatorDetail.getH1_previous().length > 0) {
				stringBuilder = null;
				for (HtmlHeading htmlHeading : targetUrlChangeIndicatorDetail.getH1_previous()) {
					if (stringBuilder == null) {
						stringBuilder = new StringBuilder();
					} else {
						stringBuilder.append(IConstants.NEWLINE);
					}
					stringBuilder.append(htmlHeading.getValue());
				}
				stringArray[0] = stringBuilder.toString();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getH1_current() != null && targetUrlChangeIndicatorDetail.getH1_current().length > 0) {
				stringBuilder = null;
				for (HtmlHeading htmlHeading : targetUrlChangeIndicatorDetail.getH1_current()) {
					if (stringBuilder == null) {
						stringBuilder = new StringBuilder();
					} else {
						stringBuilder.append(IConstants.NEWLINE);
					}
					stringBuilder.append(htmlHeading.getValue());
				}
				stringArray[1] = stringBuilder.toString();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// h1_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.H1_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getH1_previous() != null && targetUrlChangeIndicatorDetail.getH1_previous().length > 0) {
				stringBuilder = null;
				for (HtmlHeading htmlHeading : targetUrlChangeIndicatorDetail.getH1_previous()) {
					if (stringBuilder == null) {
						stringBuilder = new StringBuilder();
					} else {
						stringBuilder.append(IConstants.NEWLINE);
					}
					stringBuilder.append(htmlHeading.getValue());
				}
				stringArray[0] = stringBuilder.toString();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getH1_current() != null && targetUrlChangeIndicatorDetail.getH1_current().length > 0) {
				stringBuilder = null;
				for (HtmlHeading htmlHeading : targetUrlChangeIndicatorDetail.getH1_current()) {
					if (stringBuilder == null) {
						stringBuilder = new StringBuilder();
					} else {
						stringBuilder.append(IConstants.NEWLINE);
					}
					stringBuilder.append(htmlHeading.getValue());
				}
				stringArray[1] = stringBuilder.toString();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// h1_removed_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.H1_REMOVED_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getH1_previous() != null && targetUrlChangeIndicatorDetail.getH1_previous().length > 0) {
				stringBuilder = null;
				for (HtmlHeading htmlHeading : targetUrlChangeIndicatorDetail.getH1_previous()) {
					if (stringBuilder == null) {
						stringBuilder = new StringBuilder();
					} else {
						stringBuilder.append(IConstants.NEWLINE);
					}
					stringBuilder.append(htmlHeading.getValue());
				}
				stringArray[0] = stringBuilder.toString();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getH1_current() != null && targetUrlChangeIndicatorDetail.getH1_current().length > 0) {
				stringBuilder = null;
				for (HtmlHeading htmlHeading : targetUrlChangeIndicatorDetail.getH1_current()) {
					if (stringBuilder == null) {
						stringBuilder = new StringBuilder();
					} else {
						stringBuilder.append(IConstants.NEWLINE);
					}
					stringBuilder.append(htmlHeading.getValue());
				}
				stringArray[1] = stringBuilder.toString();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// h1_count_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.H1_COUNT_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getH1_count_previous() != null) {
				stringArray[0] = String.valueOf(targetUrlChangeIndicatorDetail.getH1_count_previous());
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getH1_count_current() != null) {
				stringArray[1] = String.valueOf(targetUrlChangeIndicatorDetail.getH1_count_current());
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// h1_length_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.H1_LENGTH_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getH1_length_previous() != null) {
				stringArray[0] = String.valueOf(targetUrlChangeIndicatorDetail.getH1_length_previous());
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getH1_length_current() != null) {
				stringArray[1] = String.valueOf(targetUrlChangeIndicatorDetail.getH1_length_current());
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// h2_added_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.H2_ADDED_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getH2_previous() != null && targetUrlChangeIndicatorDetail.getH2_previous().length > 0) {
				stringBuilder = null;
				for (HtmlHeading htmlHeading : targetUrlChangeIndicatorDetail.getH2_previous()) {
					if (stringBuilder == null) {
						stringBuilder = new StringBuilder();
					} else {
						stringBuilder.append(IConstants.NEWLINE);
					}
					stringBuilder.append(htmlHeading.getValue());
				}
				stringArray[0] = stringBuilder.toString();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getH2_current() != null && targetUrlChangeIndicatorDetail.getH2_current().length > 0) {
				stringBuilder = null;
				for (HtmlHeading htmlHeading : targetUrlChangeIndicatorDetail.getH2_current()) {
					if (stringBuilder == null) {
						stringBuilder = new StringBuilder();
					} else {
						stringBuilder.append(IConstants.NEWLINE);
					}
					stringBuilder.append(htmlHeading.getValue());
				}
				stringArray[1] = stringBuilder.toString();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// h2_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.H2_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getH2_previous() != null && targetUrlChangeIndicatorDetail.getH2_previous().length > 0) {
				stringBuilder = null;
				for (HtmlHeading htmlHeading : targetUrlChangeIndicatorDetail.getH2_previous()) {
					if (stringBuilder == null) {
						stringBuilder = new StringBuilder();
					} else {
						stringBuilder.append(IConstants.NEWLINE);
					}
					stringBuilder.append(htmlHeading.getValue());
				}
				stringArray[0] = stringBuilder.toString();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getH2_current() != null && targetUrlChangeIndicatorDetail.getH2_current().length > 0) {
				stringBuilder = null;
				for (HtmlHeading htmlHeading : targetUrlChangeIndicatorDetail.getH2_current()) {
					if (stringBuilder == null) {
						stringBuilder = new StringBuilder();
					} else {
						stringBuilder.append(IConstants.NEWLINE);
					}
					stringBuilder.append(htmlHeading.getValue());
				}
				stringArray[1] = stringBuilder.toString();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// h2_removed_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.H2_REMOVED_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getH2_previous() != null && targetUrlChangeIndicatorDetail.getH2_previous().length > 0) {
				stringBuilder = null;
				for (HtmlHeading htmlHeading : targetUrlChangeIndicatorDetail.getH2_previous()) {
					if (stringBuilder == null) {
						stringBuilder = new StringBuilder();
					} else {
						stringBuilder.append(IConstants.NEWLINE);
					}
					stringBuilder.append(htmlHeading.getValue());
				}
				stringArray[0] = stringBuilder.toString();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getH2_current() != null && targetUrlChangeIndicatorDetail.getH2_current().length > 0) {
				stringBuilder = null;
				for (HtmlHeading htmlHeading : targetUrlChangeIndicatorDetail.getH2_current()) {
					if (stringBuilder == null) {
						stringBuilder = new StringBuilder();
					} else {
						stringBuilder.append(IConstants.NEWLINE);
					}
					stringBuilder.append(htmlHeading.getValue());
				}
				stringArray[1] = stringBuilder.toString();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// header_noarchive_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.HEADER_NOARCHIVE_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getHeader_noarchive_previous() != null) {
				stringArray[0] = BooleanUtils.toStringTrueFalse(targetUrlChangeIndicatorDetail.getHeader_noarchive_previous());
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getHeader_noarchive_current() != null) {
				stringArray[1] = BooleanUtils.toStringTrueFalse(targetUrlChangeIndicatorDetail.getHeader_noarchive_current());
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// header_nofollow_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.HEADER_NOFOLLOW_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getHeader_nofollow_previous() != null) {
				stringArray[0] = BooleanUtils.toStringTrueFalse(targetUrlChangeIndicatorDetail.getHeader_nofollow_previous());
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getHeader_nofollow_current() != null) {
				stringArray[1] = BooleanUtils.toStringTrueFalse(targetUrlChangeIndicatorDetail.getHeader_nofollow_current());
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// header_noindex_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.HEADER_NOINDEX_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getHeader_noindex_previous() != null) {
				stringArray[0] = BooleanUtils.toStringTrueFalse(targetUrlChangeIndicatorDetail.getHeader_noindex_previous());
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getHeader_noindex_current() != null) {
				stringArray[1] = BooleanUtils.toStringTrueFalse(targetUrlChangeIndicatorDetail.getHeader_noindex_current());
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// header_noodp_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.HEADER_NOODP_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getHeader_noodp_previous() != null) {
				stringArray[0] = BooleanUtils.toStringTrueFalse(targetUrlChangeIndicatorDetail.getHeader_noodp_previous());
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getHeader_noodp_current() != null) {
				stringArray[1] = BooleanUtils.toStringTrueFalse(targetUrlChangeIndicatorDetail.getHeader_noodp_current());
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// header_nosnippet_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.HEADER_NOSNIPPET_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getHeader_nosnippet_previous() != null) {
				stringArray[0] = BooleanUtils.toStringTrueFalse(targetUrlChangeIndicatorDetail.getHeader_nosnippet_previous());
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getHeader_nosnippet_current() != null) {
				stringArray[1] = BooleanUtils.toStringTrueFalse(targetUrlChangeIndicatorDetail.getHeader_nosnippet_current());
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// header_noydir_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.HEADER_NOYDIR_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getHeader_noydir_previous() != null) {
				stringArray[0] = BooleanUtils.toStringTrueFalse(targetUrlChangeIndicatorDetail.getHeader_noydir_previous());
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getHeader_noydir_current() != null) {
				stringArray[1] = BooleanUtils.toStringTrueFalse(targetUrlChangeIndicatorDetail.getHeader_noydir_current());
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// hreflang_errors_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.HREFLANG_ERRORS_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getHreflang_errors_previous() != null) {
				formattedJson = new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(targetUrlChangeIndicatorDetail.getHreflang_errors_previous());
				stringArray[0] = FormatUtils.getInstance().cleanseJson(formattedJson);
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getHreflang_errors_current() != null) {
				formattedJson = new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(targetUrlChangeIndicatorDetail.getHreflang_errors_current());
				stringArray[1] = FormatUtils.getInstance().cleanseJson(formattedJson);
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// hreflang_links_added_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.HREFLANG_LINKS_ADDED_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getHreflang_links_previous() != null && targetUrlChangeIndicatorDetail.getHreflang_links_previous().length > 0) {
				formattedJson = new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(targetUrlChangeIndicatorDetail.getHreflang_links_previous());
				stringArray[0] = FormatUtils.getInstance().cleanseJson(formattedJson);
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getHreflang_links_current() != null && targetUrlChangeIndicatorDetail.getHreflang_links_current().length > 0) {
				formattedJson = new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(targetUrlChangeIndicatorDetail.getHreflang_links_current());
				stringArray[1] = FormatUtils.getInstance().cleanseJson(formattedJson);
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// hreflang_links_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.HREFLANG_LINKS_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getHreflang_links_previous() != null && targetUrlChangeIndicatorDetail.getHreflang_links_previous().length > 0) {
				formattedJson = new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(targetUrlChangeIndicatorDetail.getHreflang_links_previous());
				stringArray[0] = FormatUtils.getInstance().cleanseJson(formattedJson);
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getHreflang_links_current() != null && targetUrlChangeIndicatorDetail.getHreflang_links_current().length > 0) {
				formattedJson = new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(targetUrlChangeIndicatorDetail.getHreflang_links_current());
				stringArray[1] = FormatUtils.getInstance().cleanseJson(formattedJson);
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// hreflang_links_removed_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.HREFLANG_LINKS_REMOVED_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getHreflang_links_previous() != null && targetUrlChangeIndicatorDetail.getHreflang_links_previous().length > 0) {
				formattedJson = new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(targetUrlChangeIndicatorDetail.getHreflang_links_previous());
				stringArray[0] = FormatUtils.getInstance().cleanseJson(formattedJson);
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getHreflang_links_current() != null && targetUrlChangeIndicatorDetail.getHreflang_links_current().length > 0) {
				formattedJson = new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(targetUrlChangeIndicatorDetail.getHreflang_links_current());
				stringArray[1] = FormatUtils.getInstance().cleanseJson(formattedJson);
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// hreflang_links_out_count_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.HREFLANG_LINKS_OUT_COUNT_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getHreflang_links_out_count_previous() != null) {
				stringArray[0] = String.valueOf(targetUrlChangeIndicatorDetail.getHreflang_links_out_count_previous());
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getHreflang_links_out_count_current() != null) {
				stringArray[1] = String.valueOf(targetUrlChangeIndicatorDetail.getHreflang_links_out_count_current());
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// hreflang_url_count_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.HREFLANG_URL_COUNT_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getHreflang_url_count_previous() != null) {
				stringArray[0] = String.valueOf(targetUrlChangeIndicatorDetail.getHreflang_url_count_previous());
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getHreflang_url_count_current() != null) {
				stringArray[1] = String.valueOf(targetUrlChangeIndicatorDetail.getHreflang_url_count_current());
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// index_flg_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.INDEX_FLG_CHG_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getIndex_flg_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getIndex_flg_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getIndex_flg_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getIndex_flg_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// indexable_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.INDEXABLE_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getIndexable_previous() != null) {
				stringArray[0] = BooleanUtils.toStringTrueFalse(targetUrlChangeIndicatorDetail.getIndexable_previous());
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getIndexable_current() != null) {
				stringArray[1] = BooleanUtils.toStringTrueFalse(targetUrlChangeIndicatorDetail.getIndexable_current());
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// insecure_resources_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.INSECURE_RESOURCES_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getInsecure_resources_previous() != null && targetUrlChangeIndicatorDetail.getInsecure_resources_previous().length > 0) {
				stringBuilder = null;
				for (String testString : targetUrlChangeIndicatorDetail.getInsecure_resources_previous()) {
					if (stringBuilder == null) {
						stringBuilder = new StringBuilder();
					} else {
						stringBuilder.append(IConstants.NEWLINE);
					}
					stringBuilder.append(testString);
				}
				stringArray[0] = stringBuilder.toString();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getInsecure_resources_current() != null && targetUrlChangeIndicatorDetail.getInsecure_resources_current().length > 0) {
				stringBuilder = null;
				for (String testString : targetUrlChangeIndicatorDetail.getInsecure_resources_current()) {
					if (stringBuilder == null) {
						stringBuilder = new StringBuilder();
					} else {
						stringBuilder.append(IConstants.NEWLINE);
					}
					stringBuilder.append(testString);
				}
				stringArray[1] = stringBuilder.toString();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// meta_charset_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.META_CHARSET_CHG_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getMeta_charset_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getMeta_charset_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getMeta_charset_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getMeta_charset_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// meta_content_type_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.META_CONTENT_TYPE_CHG_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getMeta_content_type_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getMeta_content_type_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getMeta_content_type_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getMeta_content_type_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// meta_disabled_sitelinks_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.META_DISABLED_SITELINKS_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getMeta_disabled_sitelinks_previous() != null) {
				stringArray[0] = BooleanUtils.toStringTrueFalse(targetUrlChangeIndicatorDetail.getMeta_disabled_sitelinks_previous());
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getMeta_disabled_sitelinks_current() != null) {
				stringArray[1] = BooleanUtils.toStringTrueFalse(targetUrlChangeIndicatorDetail.getMeta_disabled_sitelinks_current());
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// meta_noodp_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.META_NOODP_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getMeta_noodp_previous() != null) {
				stringArray[0] = BooleanUtils.toStringTrueFalse(targetUrlChangeIndicatorDetail.getMeta_noodp_previous());
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getMeta_noodp_current() != null) {
				stringArray[1] = BooleanUtils.toStringTrueFalse(targetUrlChangeIndicatorDetail.getMeta_noodp_current());
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// meta_nosnippet_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.META_NOSNIPPET_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getMeta_nosnippet_previous() != null) {
				stringArray[0] = BooleanUtils.toStringTrueFalse(targetUrlChangeIndicatorDetail.getMeta_nosnippet_previous());
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getMeta_nosnippet_current() != null) {
				stringArray[1] = BooleanUtils.toStringTrueFalse(targetUrlChangeIndicatorDetail.getMeta_nosnippet_current());
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// meta_noydir_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.META_NOYDIR_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getMeta_noydir_previous() != null) {
				stringArray[0] = BooleanUtils.toStringTrueFalse(targetUrlChangeIndicatorDetail.getMeta_noydir_previous());
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getMeta_noydir_current() != null) {
				stringArray[1] = BooleanUtils.toStringTrueFalse(targetUrlChangeIndicatorDetail.getMeta_noydir_current());
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// meta_redirect_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.META_REDIRECT_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getMeta_redirect_previous() != null) {
				stringArray[0] = BooleanUtils.toStringTrueFalse(targetUrlChangeIndicatorDetail.getMeta_redirect_previous());
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getMeta_redirect_current() != null) {
				stringArray[1] = BooleanUtils.toStringTrueFalse(targetUrlChangeIndicatorDetail.getMeta_redirect_current());
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// mixed_redirects_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.MIXED_REDIRECTS_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getMixed_redirects_previous() != null) {
				stringArray[0] = BooleanUtils.toStringTrueFalse(targetUrlChangeIndicatorDetail.getMixed_redirects_previous());
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getMixed_redirects_current() != null) {
				stringArray[1] = BooleanUtils.toStringTrueFalse(targetUrlChangeIndicatorDetail.getMixed_redirects_current());
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// mobile_rel_alternate_url_is_consistent_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.MOBILE_REL_ALTERNATE_URL_IS_CONSISTENT_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getMobile_rel_alternate_url_is_consistent_previous() != null) {
				stringArray[0] = BooleanUtils.toStringTrueFalse(targetUrlChangeIndicatorDetail.getMobile_rel_alternate_url_is_consistent_previous());
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getMobile_rel_alternate_url_is_consistent_current() != null) {
				stringArray[1] = BooleanUtils.toStringTrueFalse(targetUrlChangeIndicatorDetail.getMobile_rel_alternate_url_is_consistent_current());
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// noodp_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.NOODP_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getNoodp_previous() != null) {
				stringArray[0] = BooleanUtils.toStringTrueFalse(targetUrlChangeIndicatorDetail.getNoodp_previous());
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getNoodp_current() != null) {
				stringArray[1] = BooleanUtils.toStringTrueFalse(targetUrlChangeIndicatorDetail.getNoodp_current());
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// nosnippet_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.NOSNIPPET_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getNosnippet_previous() != null) {
				stringArray[0] = BooleanUtils.toStringTrueFalse(targetUrlChangeIndicatorDetail.getNosnippet_previous());
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getNosnippet_current() != null) {
				stringArray[1] = BooleanUtils.toStringTrueFalse(targetUrlChangeIndicatorDetail.getNosnippet_current());
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// noydir_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.NOYDIR_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getNoydir_previous() != null) {
				stringArray[0] = BooleanUtils.toStringTrueFalse(targetUrlChangeIndicatorDetail.getNoydir_previous());
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getNoydir_current() != null) {
				stringArray[1] = BooleanUtils.toStringTrueFalse(targetUrlChangeIndicatorDetail.getNoydir_current());
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// open_graph_added_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.OPEN_GRAPH_ADDED_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getOg_markup_previous() != null && targetUrlChangeIndicatorDetail.getOg_markup_previous().length > 0) {
				formattedJson = new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(targetUrlChangeIndicatorDetail.getOg_markup_previous());
				stringArray[0] = FormatUtils.getInstance().cleanseJson(formattedJson);
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getOg_markup_current() != null && targetUrlChangeIndicatorDetail.getOg_markup_current().length > 0) {
				formattedJson = new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(targetUrlChangeIndicatorDetail.getOg_markup_current());
				stringArray[1] = FormatUtils.getInstance().cleanseJson(formattedJson);
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// og_markup_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.OG_MARKUP_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getOg_markup_previous() != null && targetUrlChangeIndicatorDetail.getOg_markup_previous().length > 0) {
				formattedJson = new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(targetUrlChangeIndicatorDetail.getOg_markup_previous());
				stringArray[0] = FormatUtils.getInstance().cleanseJson(formattedJson);
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getOg_markup_current() != null && targetUrlChangeIndicatorDetail.getOg_markup_current().length > 0) {
				formattedJson = new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(targetUrlChangeIndicatorDetail.getOg_markup_current());
				stringArray[1] = FormatUtils.getInstance().cleanseJson(formattedJson);
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// open_graph_removed_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.OPEN_GRAPH_REMOVED_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getOg_markup_previous() != null && targetUrlChangeIndicatorDetail.getOg_markup_previous().length > 0) {
				formattedJson = new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(targetUrlChangeIndicatorDetail.getOg_markup_previous());
				stringArray[0] = FormatUtils.getInstance().cleanseJson(formattedJson);
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getOg_markup_current() != null && targetUrlChangeIndicatorDetail.getOg_markup_current().length > 0) {
				formattedJson = new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(targetUrlChangeIndicatorDetail.getOg_markup_current());
				stringArray[1] = FormatUtils.getInstance().cleanseJson(formattedJson);
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// og_markup_length_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.OG_MARKUP_LENGTH_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getOg_markup_length_previous() != null) {
				stringArray[0] = String.valueOf(targetUrlChangeIndicatorDetail.getOg_markup_length_previous());
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getOg_markup_length_current() != null) {
				stringArray[1] = String.valueOf(targetUrlChangeIndicatorDetail.getOg_markup_length_current());
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// outlink_count_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.OUTLINK_COUNT_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getOutlink_count_previous() != null) {
				stringArray[0] = String.valueOf(targetUrlChangeIndicatorDetail.getOutlink_count_previous());
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getOutlink_count_current() != null) {
				stringArray[1] = String.valueOf(targetUrlChangeIndicatorDetail.getOutlink_count_current());
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// page_analysis_results_chg_ind_json
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.PAGE_ANALYSIS_RESULTS_CHG_IND_JSON)) {

			// previous
			stringArray[0] = IConstants.EMPTY_STRING;

			// current
			stringArray[1] = targetUrlChangeIndicatorDetail.getPage_analysis_results_chg_ind_json();
		}
		// page_link_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.PAGE_LINK_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getPage_link_previous() != null && targetUrlChangeIndicatorDetail.getPage_link_previous().length > 0) {
				formattedJson = new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(targetUrlChangeIndicatorDetail.getPage_link_previous());
				stringArray[0] = FormatUtils.getInstance().cleanseJson(formattedJson);
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getPage_link_current() != null && targetUrlChangeIndicatorDetail.getPage_link_current().length > 0) {
				formattedJson = new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(targetUrlChangeIndicatorDetail.getPage_link_current());
				stringArray[1] = FormatUtils.getInstance().cleanseJson(formattedJson);
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// redirect_blocked_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.REDIRECT_BLOCKED_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getRedirect_blocked_previous() != null) {
				stringArray[0] = BooleanUtils.toStringTrueFalse(targetUrlChangeIndicatorDetail.getRedirect_blocked_previous());
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getRedirect_blocked_current() != null) {
				stringArray[1] = BooleanUtils.toStringTrueFalse(targetUrlChangeIndicatorDetail.getRedirect_blocked_current());
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// redirect_blocked_reason_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.REDIRECT_BLOCKED_REASON_CHG_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getRedirect_blocked_reason_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getRedirect_blocked_reason_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getRedirect_blocked_reason_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getRedirect_blocked_reason_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// redirect_chain_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.REDIRECT_CHAIN_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getRedirect_chain_previous() != null && targetUrlChangeIndicatorDetail.getRedirect_chain_previous().length > 0) {
				formattedJson = new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(targetUrlChangeIndicatorDetail.getRedirect_chain_previous());
				stringArray[0] = FormatUtils.getInstance().cleanseJson(formattedJson);
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getRedirect_chain_current() != null && targetUrlChangeIndicatorDetail.getRedirect_chain_current().length > 0) {
				formattedJson = new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(targetUrlChangeIndicatorDetail.getRedirect_chain_current());
				stringArray[1] = FormatUtils.getInstance().cleanseJson(formattedJson);
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// redirect_final_url_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.REDIRECT_FINAL_URL_CHG_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getRedirect_final_url_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getRedirect_final_url_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getRedirect_final_url_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getRedirect_final_url_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// redirect_times_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.REDIRECT_TIMES_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getRedirect_times_previous() != null) {
				stringArray[0] = String.valueOf(targetUrlChangeIndicatorDetail.getRedirect_times_previous());
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getRedirect_times_current() != null) {
				stringArray[1] = String.valueOf(targetUrlChangeIndicatorDetail.getRedirect_times_current());
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// response_code_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.RESPONSE_CODE_CHG_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getResponse_code_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getResponse_code_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getResponse_code_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getResponse_code_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// redirect_301_detected_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.REDIRECT_301_DETECTED_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getResponse_code_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getResponse_code_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getResponse_code_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getResponse_code_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// redirect_301_removed_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.REDIRECT_301_REMOVED_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getResponse_code_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getResponse_code_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getResponse_code_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getResponse_code_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// redirect_302_detected_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.REDIRECT_302_DETECTED_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getResponse_code_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getResponse_code_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getResponse_code_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getResponse_code_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// redirect_302_removed_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.REDIRECT_302_REMOVED_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getResponse_code_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getResponse_code_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getResponse_code_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getResponse_code_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// redirect_diff_code_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.REDIRECT_DIFF_CODE_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getResponse_code_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getResponse_code_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getResponse_code_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getResponse_code_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// response_headers_added_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.RESPONSE_HEADERS_ADDED_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getResponse_headers_previous() != null && targetUrlChangeIndicatorDetail.getResponse_headers_previous().length > 0) {
				formattedJson = new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(targetUrlChangeIndicatorDetail.getResponse_headers_previous());
				stringArray[0] = FormatUtils.getInstance().cleanseJson(formattedJson);
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getResponse_headers_current() != null && targetUrlChangeIndicatorDetail.getResponse_headers_current().length > 0) {
				formattedJson = new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(targetUrlChangeIndicatorDetail.getResponse_headers_current());
				stringArray[1] = FormatUtils.getInstance().cleanseJson(formattedJson);
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// response_headers_removed_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.RESPONSE_HEADERS_REMOVED_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getResponse_headers_previous() != null && targetUrlChangeIndicatorDetail.getResponse_headers_previous().length > 0) {
				formattedJson = new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(targetUrlChangeIndicatorDetail.getResponse_headers_previous());
				stringArray[0] = FormatUtils.getInstance().cleanseJson(formattedJson);
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getResponse_headers_current() != null && targetUrlChangeIndicatorDetail.getResponse_headers_current().length > 0) {
				formattedJson = new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(targetUrlChangeIndicatorDetail.getResponse_headers_current());
				stringArray[1] = FormatUtils.getInstance().cleanseJson(formattedJson);
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// robots_added_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.ROBOTS_ADDED_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getRobots_contents_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getRobots_contents_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getRobots_contents_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getRobots_contents_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// robots_contents_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.ROBOTS_CONTENTS_CHG_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getRobots_contents_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getRobots_contents_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getRobots_contents_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getRobots_contents_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// robots_removed_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.ROBOTS_REMOVED_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getRobots_contents_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getRobots_contents_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getRobots_contents_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getRobots_contents_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// structured_data_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.STRUCTURED_DATA_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getStructured_data_previous() != null) {
				formattedJson = new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(targetUrlChangeIndicatorDetail.getStructured_data_previous());
				stringArray[0] = FormatUtils.getInstance().cleanseJson(formattedJson);
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getStructured_data_current() != null) {
				formattedJson = new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(targetUrlChangeIndicatorDetail.getStructured_data_current());
				stringArray[1] = FormatUtils.getInstance().cleanseJson(formattedJson);
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// title_added_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.TITLE_ADDED_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getTitle_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getTitle_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getTitle_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getTitle_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// title_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.TITLE_CHG_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getTitle_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getTitle_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getTitle_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getTitle_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// title_removed_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.TITLE_REMOVED_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getTitle_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getTitle_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getTitle_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getTitle_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// title_length_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.TITLE_LENGTH_CHG_IND)) {

			// previous
			if (targetUrlChangeIndicatorDetail.getTitle_length_previous() != null) {
				stringArray[0] = String.valueOf(targetUrlChangeIndicatorDetail.getTitle_length_previous());
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (targetUrlChangeIndicatorDetail.getTitle_length_current() != null) {
				stringArray[1] = String.valueOf(targetUrlChangeIndicatorDetail.getTitle_length_current());
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// viewport_added_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.VIEWPORT_ADDED_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getViewport_content_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getViewport_content_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getViewport_content_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getViewport_content_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// viewport_content_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.VIEWPORT_CONTENT_CHG_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getViewport_content_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getViewport_content_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getViewport_content_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getViewport_content_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// viewport_removed_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.VIEWPORT_REMOVED_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getViewport_content_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getViewport_content_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getViewport_content_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getViewport_content_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}
		// robots_txt_chg_ind
		else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndicatorDetail.getChange_indicator(), IConstants.ROBOTS_TXT_CHG_IND)) {

			// previous
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getRobots_txt_previous())) {
				stringArray[0] = targetUrlChangeIndicatorDetail.getRobots_txt_previous();
			} else {
				stringArray[0] = IConstants.EMPTY_STRING;
			}

			// current
			if (StringUtils.isNotBlank(targetUrlChangeIndicatorDetail.getRobots_txt_current())) {
				stringArray[1] = targetUrlChangeIndicatorDetail.getRobots_txt_current();
			} else {
				stringArray[1] = IConstants.EMPTY_STRING;
			}
		}

		return stringArray;
	}

	private List<ChangeIndicatorTotalUrls> getChangeIndicatorTotalUrlsList(List<TargetUrlChangeDailySummary> daily_summary_list) {
		List<ChangeIndicatorTotalUrls> changeIndicatorTotalUrlsList = null;
		ChangeIndicatorTotalUrls[] changeIndicatorTotalUrlsArray = null;
		int totalUrls = 0;
		ChangeIndicatorTotalUrls changeIndicatorTotalUrls = null;

		// map key = change indicators
		// map value = total URLs
		Map<String, Integer> changeIndicatorTotalUrlMap = new HashMap<String, Integer>();

		if (daily_summary_list != null && daily_summary_list.size() > 0) {
			changeIndicatorTotalUrlsList = new ArrayList<ChangeIndicatorTotalUrls>();
			for (TargetUrlChangeDailySummary targetUrlChangeDailySummary : daily_summary_list) {
				changeIndicatorTotalUrlsArray = targetUrlChangeDailySummary.getChange_indicator_total_urls_array();
				for (ChangeIndicatorTotalUrls testChangeIndicatorTotalUrls : changeIndicatorTotalUrlsArray) {
					if (changeIndicatorTotalUrlMap.containsKey(testChangeIndicatorTotalUrls.getChange_indicator())) {
						totalUrls = changeIndicatorTotalUrlMap.get(testChangeIndicatorTotalUrls.getChange_indicator());
					} else {
						totalUrls = 0;
					}
					totalUrls = totalUrls + testChangeIndicatorTotalUrls.getTotal_urls().intValue();
					changeIndicatorTotalUrlMap.put(testChangeIndicatorTotalUrls.getChange_indicator(), totalUrls);
				}
			}

			for (String changeIndicator : changeIndicatorTotalUrlMap.keySet()) {
				changeIndicatorTotalUrls = new ChangeIndicatorTotalUrls();
				changeIndicatorTotalUrls.setChange_indicator(changeIndicator);
				changeIndicatorTotalUrls.setTotal_urls(changeIndicatorTotalUrlMap.get(changeIndicator));
				changeIndicatorTotalUrlsList.add(changeIndicatorTotalUrls);
			}
		}
		return changeIndicatorTotalUrlsList;

	}

	private List<TargetUrlChangeDailySummary> getTargetUrlChangeDailySummaryList(TargetUrlChangeResponse targetUrlChangeResponse) {

		List<TargetUrlChangeDailySummary> targetUrlChangeDailySummaryList = null;
		TargetUrlChangeDailySummary targetUrlChangeDailySummary = null;
		List<TargetUrlChangeSummary> targetUrlChangeSummaryList = null;
		List<ResponseCodeSummary> responseCodeSummaryList = null;
		List<ChangeIndicatorTotalUrls> changeIndicatorTotalUrlsList = null;
		ChangeIndicatorTotalUrls testChangeIndicatorTotalUrls = null;
		String crawlDate = null;

		// map key: crawl date
		// map value: TargetUrlChangeDailySummary
		SortedMap<String, TargetUrlChangeDailySummary> crawlDateTargetUrlChangeDailySummaryMap = new TreeMap<String, TargetUrlChangeDailySummary>();

		// map key: crawl date
		// map value: change indicator total URLs map
		SortedMap<String, SortedMap<String, Integer>> crawlDateChangeIndicatorTotalUrlsMapMap = new TreeMap<String, SortedMap<String, Integer>>();

		// map key: change indicator
		// map value: total URLs
		SortedMap<String, Integer> changeIndicatorTotalUrlsMap = null;

		// map key = crawl date
		// map value = response code previous (response code current total URLs map) map
		SortedMap<String, SortedMap<String, SortedMap<String, Integer>>> crawlDateResponseCodePreviousResponseCodeCurrentTotalUrlsMapMap = new TreeMap<String, SortedMap<String, SortedMap<String, Integer>>>();

		// map key: response code previous
		// map value : response code current total URLs map
		SortedMap<String, SortedMap<String, Integer>> responseCodePreviousResponseCodeCurrentTotalUrlsMap = null;

		SortedMap<String, Integer> responseCodeCurrentTotalUrlsMap = null;

		Integer totalUrls = null;
		String responseCodePrevious = null;
		String responseCodeCurrent = null;
		String changeIndicator = null;
		ResponseCodeTotalUrls testResponseCodeTotalUrls = null;
		List<ResponseCodeTotalUrls> responseCodeTotalUrlsList = null;

		targetUrlChangeSummaryList = targetUrlChangeResponse.getSummary_list();
		if (targetUrlChangeSummaryList != null && targetUrlChangeSummaryList.size() > 0) {
			targetUrlChangeDailySummaryList = new ArrayList<TargetUrlChangeDailySummary>();
			for (TargetUrlChangeSummary targetUrlChangeSummary : targetUrlChangeSummaryList) {
				crawlDate = StringUtils.substringBefore(targetUrlChangeSummary.getCrawl_date_hour(), IConstants.ONE_SPACE);

				if (crawlDateTargetUrlChangeDailySummaryMap.containsKey(crawlDate)) {
					targetUrlChangeDailySummary = crawlDateTargetUrlChangeDailySummaryMap.get(crawlDate);
				} else {
					targetUrlChangeDailySummary = new TargetUrlChangeDailySummary();
				}
				targetUrlChangeDailySummary.setCrawl_date(crawlDate);
				targetUrlChangeDailySummary.setTotal_added(targetUrlChangeDailySummary.getTotal_added() + targetUrlChangeSummary.getTotal_added());
				targetUrlChangeDailySummary.setTotal_modified(targetUrlChangeDailySummary.getTotal_modified() + targetUrlChangeSummary.getTotal_modified());
				targetUrlChangeDailySummary.setTotal_removed(targetUrlChangeDailySummary.getTotal_removed() + targetUrlChangeSummary.getTotal_removed());
				targetUrlChangeDailySummary.setTotal_severity_critical(targetUrlChangeDailySummary.getTotal_severity_critical() + targetUrlChangeSummary.getTotal_severity_critical());
				targetUrlChangeDailySummary.setTotal_severity_high(targetUrlChangeDailySummary.getTotal_severity_high() + targetUrlChangeSummary.getTotal_severity_high());
				targetUrlChangeDailySummary.setTotal_severity_medium(targetUrlChangeDailySummary.getTotal_severity_medium() + targetUrlChangeSummary.getTotal_severity_medium());
				targetUrlChangeDailySummary.setTotal_severity_low(targetUrlChangeDailySummary.getTotal_severity_low() + targetUrlChangeSummary.getTotal_severity_low());
				crawlDateTargetUrlChangeDailySummaryMap.put(crawlDate, targetUrlChangeDailySummary);

				if (crawlDateChangeIndicatorTotalUrlsMapMap.containsKey(crawlDate)) {
					changeIndicatorTotalUrlsMap = crawlDateChangeIndicatorTotalUrlsMapMap.get(crawlDate);
				} else {
					changeIndicatorTotalUrlsMap = new TreeMap<String, Integer>();
				}

				if (targetUrlChangeSummary.getChange_indicator_total_urls_array() != null && targetUrlChangeSummary.getChange_indicator_total_urls_array().length > 0) {
					for (ChangeIndicatorTotalUrls changeIndicatorTotalUrls : targetUrlChangeSummary.getChange_indicator_total_urls_array()) {
						changeIndicator = changeIndicatorTotalUrls.getChange_indicator();
						if (changeIndicatorTotalUrlsMap.containsKey(changeIndicator)) {
							totalUrls = changeIndicatorTotalUrlsMap.get(changeIndicator);
						} else {
							totalUrls = 0;
						}
						totalUrls = totalUrls + changeIndicatorTotalUrls.getTotal_urls();
						changeIndicatorTotalUrlsMap.put(changeIndicator, totalUrls);
					}
				}
				crawlDateChangeIndicatorTotalUrlsMapMap.put(crawlDate, changeIndicatorTotalUrlsMap);
			}
		}

		responseCodeSummaryList = targetUrlChangeResponse.getResponse_code_summary_list();
		if (responseCodeSummaryList != null && responseCodeSummaryList.size() > 0) {
			for (ResponseCodeSummary responseCodeSummary : responseCodeSummaryList) {
				crawlDate = StringUtils.substringBefore(responseCodeSummary.getCrawl_date_hour(), IConstants.ONE_SPACE);
				if (crawlDateResponseCodePreviousResponseCodeCurrentTotalUrlsMapMap.containsKey(crawlDate)) {
					responseCodePreviousResponseCodeCurrentTotalUrlsMap = crawlDateResponseCodePreviousResponseCodeCurrentTotalUrlsMapMap.get(crawlDate);
				} else {
					responseCodePreviousResponseCodeCurrentTotalUrlsMap = new TreeMap<String, SortedMap<String, Integer>>();
				}
				for (ResponseCodeTotalUrls responseCodeTotalUrls : responseCodeSummary.getResponse_code_total_urls_array()) {
					responseCodePrevious = responseCodeTotalUrls.getResponse_code_previous();
					responseCodeCurrent = responseCodeTotalUrls.getResponse_code_current();
					if (responseCodePreviousResponseCodeCurrentTotalUrlsMap.containsKey(responseCodePrevious)) {
						responseCodeCurrentTotalUrlsMap = responseCodePreviousResponseCodeCurrentTotalUrlsMap.get(responseCodePrevious);
					} else {
						responseCodeCurrentTotalUrlsMap = new TreeMap<String, Integer>();
					}
					if (responseCodeCurrentTotalUrlsMap.containsKey(responseCodeCurrent)) {
						totalUrls = responseCodeCurrentTotalUrlsMap.get(responseCodeCurrent);
					} else {
						totalUrls = 0;
					}
					totalUrls = totalUrls + responseCodeTotalUrls.getTotal_urls();
					responseCodeCurrentTotalUrlsMap.put(responseCodeCurrent, totalUrls);
					responseCodePreviousResponseCodeCurrentTotalUrlsMap.put(responseCodePrevious, responseCodeCurrentTotalUrlsMap);
				}
				crawlDateResponseCodePreviousResponseCodeCurrentTotalUrlsMapMap.put(crawlDate, responseCodePreviousResponseCodeCurrentTotalUrlsMap);
			}
		}

		for (String testCrawlDate : crawlDateTargetUrlChangeDailySummaryMap.keySet()) {
			targetUrlChangeDailySummary = crawlDateTargetUrlChangeDailySummaryMap.get(testCrawlDate);
			if (crawlDateChangeIndicatorTotalUrlsMapMap.containsKey(testCrawlDate)) {
				changeIndicatorTotalUrlsList = new ArrayList<ChangeIndicatorTotalUrls>();
				changeIndicatorTotalUrlsMap = crawlDateChangeIndicatorTotalUrlsMapMap.get(testCrawlDate);
				for (String testChangeIndicator : changeIndicatorTotalUrlsMap.keySet()) {
					testChangeIndicatorTotalUrls = new ChangeIndicatorTotalUrls();
					testChangeIndicatorTotalUrls.setChange_indicator(testChangeIndicator);
					testChangeIndicatorTotalUrls.setTotal_urls(changeIndicatorTotalUrlsMap.get(testChangeIndicator));
					changeIndicatorTotalUrlsList.add(testChangeIndicatorTotalUrls);
				}
				targetUrlChangeDailySummary.setChange_indicator_total_urls_array(changeIndicatorTotalUrlsList.toArray(new ChangeIndicatorTotalUrls[0]));
			}
			if (crawlDateResponseCodePreviousResponseCodeCurrentTotalUrlsMapMap.containsKey(testCrawlDate)) {
				responseCodeTotalUrlsList = new ArrayList<ResponseCodeTotalUrls>();
				responseCodePreviousResponseCodeCurrentTotalUrlsMap = crawlDateResponseCodePreviousResponseCodeCurrentTotalUrlsMapMap.get(testCrawlDate);
				for (String testResponseCodePrevious : responseCodePreviousResponseCodeCurrentTotalUrlsMap.keySet()) {
					responseCodeCurrentTotalUrlsMap = responseCodePreviousResponseCodeCurrentTotalUrlsMap.get(testResponseCodePrevious);
					for (String testResponseCodeCurrent : responseCodeCurrentTotalUrlsMap.keySet()) {
						testResponseCodeTotalUrls = new ResponseCodeTotalUrls();
						testResponseCodeTotalUrls.setResponse_code_previous(testResponseCodePrevious);
						testResponseCodeTotalUrls.setResponse_code_current(testResponseCodeCurrent);
						testResponseCodeTotalUrls.setTotal_urls(responseCodeCurrentTotalUrlsMap.get(testResponseCodeCurrent));
						responseCodeTotalUrlsList.add(testResponseCodeTotalUrls);
					}
				}
				targetUrlChangeDailySummary.setResponse_code_total_urls_array(responseCodeTotalUrlsList.toArray(new ResponseCodeTotalUrls[0]));
			}
			targetUrlChangeDailySummaryList.add(targetUrlChangeDailySummary);
		}

		return targetUrlChangeDailySummaryList;
	}

	private List<ResponseCodeSummary> getResponseCodeSummaryList(TargetUrlChangeRequest targetUrlChangeRequest) throws Exception {
		List<ResponseCodeSummary> responseCodeSummaryList = null;
		ResponseCodeSummary responseCodeSummary = null;
		List<TargetUrlResponseCodeHourlyTotals> targetUrlResponseCodeHourlyTotalsList = null;

		// map key = crawl date hour
		// map value = list of TargetUrlResponseCodeHourlyTotals
		SortedMap<String, List<TargetUrlResponseCodeHourlyTotals>> crawlDateHourTargetUrlResponseCodeHourlyTotalsListMap = new TreeMap<String, List<TargetUrlResponseCodeHourlyTotals>>();

		List<TargetUrlResponseCodeHourlyTotals> testTargetUrlResponseCodeHourlyTotalsList = null;
		List<ResponseCodeTotalUrls> responseCodeTotalUrlsList = null;
		ResponseCodeTotalUrls responseCodeTotalUrls = null;

		List<String> databaseFields = null;
		String[] changeIndicatorArray = targetUrlChangeRequest.getChange_indicators();

		String urlPredicate = TargetUrlChangeUtils.getInstance().getUrlPredicate(targetUrlChangeRequest.getDomain_id(), targetUrlChangeRequest.getUrls());

		String contentTypePredicate = TargetUrlChangeUtils.getInstance().getContentTypePredicate(targetUrlChangeRequest.getDomain_id(),
				targetUrlChangeRequest.getContent_types());

		String responseCodePredicate = TargetUrlChangeUtils.getInstance().getResponseCodePredicate(targetUrlChangeRequest.getDomain_id(),
				targetUrlChangeRequest.getResponse_codes());

		if (changeIndicatorArray != null && changeIndicatorArray.length > 0) {
			databaseFields = getDomainSummaryDatabaseFields(changeIndicatorArray);
		} else {
			databaseFields = CrawlerUtils.getInstance().getChangeIndicatorDatabaseFields();
			changeIndicatorArray = CrawlerUtils.getInstance().getChangeIndicatorDatabaseFields().toArray(new String[0]);
		}

		boolean isResponseCodeChgInd = false;
		nextChangeIndicator: for (String changeIndicator : changeIndicatorArray) {
			if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.RESPONSE_CODE_CHG_IND)) {
				isResponseCodeChgInd = true;
				break nextChangeIndicator;
			}
		}

		if (isResponseCodeChgInd == true) {
			targetUrlResponseCodeHourlyTotalsList = TargetUrlChangeIndClickHouseDAO.getInstance().getResponseCodeHourlyTotals(targetUrlChangeRequest.getDomain_id(),
					targetUrlChangeRequest.getStart_crawl_timestamp(), targetUrlChangeRequest.getEnd_crawl_timestamp(), databaseFields,
					targetUrlChangeRequest.getChange_indicators(), targetUrlChangeRequest.getPage_tag_ids(), contentTypePredicate, urlPredicate, responseCodePredicate);
			if (targetUrlResponseCodeHourlyTotalsList != null && targetUrlResponseCodeHourlyTotalsList.size() > 0) {
				responseCodeSummaryList = new ArrayList<ResponseCodeSummary>();
				for (TargetUrlResponseCodeHourlyTotals targetUrlResponseCodeHourlyTotals : targetUrlResponseCodeHourlyTotalsList) {
					if (crawlDateHourTargetUrlResponseCodeHourlyTotalsListMap.containsKey(targetUrlResponseCodeHourlyTotals.getCrawlDateHour())) {
						testTargetUrlResponseCodeHourlyTotalsList = crawlDateHourTargetUrlResponseCodeHourlyTotalsListMap
								.get(targetUrlResponseCodeHourlyTotals.getCrawlDateHour());
					} else {
						testTargetUrlResponseCodeHourlyTotalsList = new ArrayList<TargetUrlResponseCodeHourlyTotals>();
					}
					testTargetUrlResponseCodeHourlyTotalsList.add(targetUrlResponseCodeHourlyTotals);
					crawlDateHourTargetUrlResponseCodeHourlyTotalsListMap.put(targetUrlResponseCodeHourlyTotals.getCrawlDateHour(),
							testTargetUrlResponseCodeHourlyTotalsList);
				}
				for (String crawlDateHour : crawlDateHourTargetUrlResponseCodeHourlyTotalsListMap.keySet()) {
					responseCodeSummary = new ResponseCodeSummary();
					responseCodeSummary.setCrawl_date_hour(crawlDateHour);
					testTargetUrlResponseCodeHourlyTotalsList = crawlDateHourTargetUrlResponseCodeHourlyTotalsListMap.get(crawlDateHour);
					responseCodeTotalUrlsList = new ArrayList<ResponseCodeTotalUrls>();
					for (TargetUrlResponseCodeHourlyTotals targetUrlResponseCodeHourlyTotals : testTargetUrlResponseCodeHourlyTotalsList) {
						responseCodeTotalUrls = new ResponseCodeTotalUrls();
						responseCodeTotalUrls.setResponse_code_previous(targetUrlResponseCodeHourlyTotals.getResponseCodePrevious());
						responseCodeTotalUrls.setResponse_code_current(targetUrlResponseCodeHourlyTotals.getResponseCodeCurrent());
						responseCodeTotalUrls.setTotal_urls(targetUrlResponseCodeHourlyTotals.getTotalUrls());
						responseCodeTotalUrlsList.add(responseCodeTotalUrls);
					}
					responseCodeSummary.setResponse_code_total_urls_array(responseCodeTotalUrlsList.toArray(new ResponseCodeTotalUrls[0]));
					responseCodeSummaryList.add(responseCodeSummary);
				}
			}
		}
		return responseCodeSummaryList;
	}

	private List<TargetUrlChangeSummary> getTargetUrlChangeSummaryList(TargetUrlChangeRequest targetUrlChangeRequest) throws Exception {
		List<TargetUrlChangeSummary> targetUrlChangeSummaryList = new ArrayList<TargetUrlChangeSummary>();

		List<String> databaseFields = null;
		String[] changeIndicatorArray = targetUrlChangeRequest.getChange_indicators();

		String urlPredicate = TargetUrlChangeUtils.getInstance().getUrlPredicate(targetUrlChangeRequest.getDomain_id(), targetUrlChangeRequest.getUrls());

		String contentTypePredicate = TargetUrlChangeUtils.getInstance().getContentTypePredicate(targetUrlChangeRequest.getDomain_id(),
				targetUrlChangeRequest.getContent_types());

		String responseCodePredicate = TargetUrlChangeUtils.getInstance().getResponseCodePredicate(targetUrlChangeRequest.getDomain_id(),
				targetUrlChangeRequest.getResponse_codes());

		if (changeIndicatorArray != null && changeIndicatorArray.length > 0) {
			databaseFields = getDomainSummaryDatabaseFields(changeIndicatorArray);
		} else {
			databaseFields = CrawlerUtils.getInstance().getChangeIndicatorDatabaseFields();
			changeIndicatorArray = CrawlerUtils.getInstance().getChangeIndicatorDatabaseFields().toArray(new String[0]);
		}

		List<TargetUrlChangeHourlyTotals> targetUrlChangeHourlyTotalsList = TargetUrlChangeIndClickHouseDAO.getInstance().getHourlyTotals(
				targetUrlChangeRequest.getDomain_id(), targetUrlChangeRequest.getStart_crawl_timestamp(), targetUrlChangeRequest.getEnd_crawl_timestamp(),
				databaseFields, targetUrlChangeRequest.getChange_indicators(), targetUrlChangeRequest.getPage_tag_ids(), contentTypePredicate, urlPredicate,
				responseCodePredicate);
		if (targetUrlChangeHourlyTotalsList != null && targetUrlChangeHourlyTotalsList.size() > 0) {
			targetUrlChangeSummaryList = getTargetUrlChangeSummaryList(targetUrlChangeHourlyTotalsList);
		}
		return targetUrlChangeSummaryList;
	}

	private List<String> getDomainSummaryDatabaseFields(String[] changeIndicatorArray) throws Exception {
		List<String> databaseFields = null;
		Set<String> databaseFieldSet = null;
		if (changeIndicatorArray != null && changeIndicatorArray.length > 0) {
			databaseFieldSet = new HashSet<String>();
			for (String changeIndicator : changeIndicatorArray) {
				if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.ALTERNATE_LINKS_CHG_IND)) {
					databaseFieldSet.add(IConstants.ALTERNATE_LINKS_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.AMPHTML_HREF_CHG_IND)) {
					databaseFieldSet.add(IConstants.AMPHTML_HREF_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.ANALYZED_URL_S_CHG_IND)) {
					databaseFieldSet.add(IConstants.ANALYZED_URL_S_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.ARCHIVE_FLG_CHG_IND)) {
					databaseFieldSet.add(IConstants.ARCHIVE_FLG_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.BASE_TAG_ADDED_IND)) {
					databaseFieldSet.add(IConstants.BASE_TAG_ADDED_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.BASE_TAG_CHG_IND)) {
					databaseFieldSet.add(IConstants.BASE_TAG_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.BASE_TAG_REMOVED_IND)) {
					databaseFieldSet.add(IConstants.BASE_TAG_REMOVED_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.BASE_TAG_TARGET_CHG_IND)) {
					databaseFieldSet.add(IConstants.BASE_TAG_TARGET_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.BLOCKED_BY_ROBOTS_CHG_IND)) {
					databaseFieldSet.add(IConstants.BLOCKED_BY_ROBOTS_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.CANONICAL_ADDED_IND)) {
					databaseFieldSet.add(IConstants.CANONICAL_ADDED_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.CANONICAL_CHG_IND)) {
					databaseFieldSet.add(IConstants.CANONICAL_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.CANONICAL_REMOVED_IND)) {
					databaseFieldSet.add(IConstants.CANONICAL_REMOVED_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.CANONICAL_HEADER_FLAG_CHG_IND)) {
					databaseFieldSet.add(IConstants.CANONICAL_HEADER_FLAG_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.CANONICAL_HEADER_TYPE_CHG_IND)) {
					databaseFieldSet.add(IConstants.CANONICAL_HEADER_TYPE_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.CANONICAL_TYPE_CHG_IND)) {
					databaseFieldSet.add(IConstants.CANONICAL_TYPE_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.CANONICAL_URL_IS_CONSISTENT_CHG_IND)) {
					databaseFieldSet.add(IConstants.CANONICAL_URL_IS_CONSISTENT_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.CONTENT_TYPE_CHG_IND)) {
					databaseFieldSet.add(IConstants.CONTENT_TYPE_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.CUSTOM_DATA_ADDED_IND)) {
					databaseFieldSet.add(IConstants.CUSTOM_DATA_ADDED_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.CUSTOM_DATA_CHG_IND)) {
					databaseFieldSet.add(IConstants.CUSTOM_DATA_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.CUSTOM_DATA_REMOVED_IND)) {
					databaseFieldSet.add(IConstants.CUSTOM_DATA_REMOVED_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.DESCRIPTION_ADDED_IND)) {
					databaseFieldSet.add(IConstants.DESCRIPTION_ADDED_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.DESCRIPTION_CHG_IND)) {
					databaseFieldSet.add(IConstants.DESCRIPTION_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.DESCRIPTION_REMOVED_IND)) {
					databaseFieldSet.add(IConstants.DESCRIPTION_REMOVED_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.DESCRIPTION_LENGTH_CHG_IND)) {
					databaseFieldSet.add(IConstants.DESCRIPTION_LENGTH_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.ERROR_MESSAGE_CHG_IND)) {
					databaseFieldSet.add(IConstants.ERROR_MESSAGE_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.FINAL_RESPONSE_CODE_CHG_IND)) {
					databaseFieldSet.add(IConstants.FINAL_RESPONSE_CODE_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.FOLLOW_FLG_CHG_IND)) {
					databaseFieldSet.add(IConstants.FOLLOW_FLG_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.H1_ADDED_IND)) {
					databaseFieldSet.add(IConstants.H1_ADDED_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.H1_CHG_IND)) {
					databaseFieldSet.add(IConstants.H1_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.H1_REMOVED_IND)) {
					databaseFieldSet.add(IConstants.H1_REMOVED_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.H1_COUNT_CHG_IND)) {
					databaseFieldSet.add(IConstants.H1_COUNT_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.H1_LENGTH_CHG_IND)) {
					databaseFieldSet.add(IConstants.H1_LENGTH_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.H2_ADDED_IND)) {
					databaseFieldSet.add(IConstants.H2_ADDED_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.H2_CHG_IND)) {
					databaseFieldSet.add(IConstants.H2_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.H2_REMOVED_IND)) {
					databaseFieldSet.add(IConstants.H2_REMOVED_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.HEADER_NOARCHIVE_CHG_IND)) {
					databaseFieldSet.add(IConstants.HEADER_NOARCHIVE_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.HEADER_NOFOLLOW_CHG_IND)) {
					databaseFieldSet.add(IConstants.HEADER_NOFOLLOW_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.HEADER_NOINDEX_CHG_IND)) {
					databaseFieldSet.add(IConstants.HEADER_NOINDEX_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.HEADER_NOODP_CHG_IND)) {
					databaseFieldSet.add(IConstants.HEADER_NOODP_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.HEADER_NOSNIPPET_CHG_IND)) {
					databaseFieldSet.add(IConstants.HEADER_NOSNIPPET_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.HEADER_NOYDIR_CHG_IND)) {
					databaseFieldSet.add(IConstants.HEADER_NOYDIR_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.HREFLANG_ERRORS_CHG_IND)) {
					databaseFieldSet.add(IConstants.HREFLANG_ERRORS_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.HREFLANG_LINKS_CHG_IND)) {
					databaseFieldSet.add(IConstants.HREFLANG_LINKS_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.HREFLANG_LINKS_OUT_COUNT_CHG_IND)) {
					databaseFieldSet.add(IConstants.HREFLANG_LINKS_OUT_COUNT_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.HREFLANG_LINKS_ADDED_IND)) {
					databaseFieldSet.add(IConstants.HREFLANG_LINKS_ADDED_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.HREFLANG_URL_COUNT_CHG_IND)) {
					databaseFieldSet.add(IConstants.HREFLANG_URL_COUNT_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.HREFLANG_LINKS_REMOVED_IND)) {
					databaseFieldSet.add(IConstants.HREFLANG_LINKS_REMOVED_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.INDEX_FLG_CHG_IND)) {
					databaseFieldSet.add(IConstants.INDEX_FLG_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.INDEXABLE_CHG_IND)) {
					databaseFieldSet.add(IConstants.INDEXABLE_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.INSECURE_RESOURCES_CHG_IND)) {
					databaseFieldSet.add(IConstants.INSECURE_RESOURCES_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.META_CHARSET_CHG_IND)) {
					databaseFieldSet.add(IConstants.META_CHARSET_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.META_CONTENT_TYPE_CHG_IND)) {
					databaseFieldSet.add(IConstants.META_CONTENT_TYPE_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.META_DISABLED_SITELINKS_CHG_IND)) {
					databaseFieldSet.add(IConstants.META_DISABLED_SITELINKS_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.META_NOODP_CHG_IND)) {
					databaseFieldSet.add(IConstants.META_NOODP_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.META_NOSNIPPET_CHG_IND)) {
					databaseFieldSet.add(IConstants.META_NOSNIPPET_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.META_NOYDIR_CHG_IND)) {
					databaseFieldSet.add(IConstants.META_NOYDIR_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.META_REDIRECT_CHG_IND)) {
					databaseFieldSet.add(IConstants.META_REDIRECT_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.MIXED_REDIRECTS_CHG_IND)) {
					databaseFieldSet.add(IConstants.MIXED_REDIRECTS_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.MOBILE_REL_ALTERNATE_URL_IS_CONSISTENT_CHG_IND)) {
					databaseFieldSet.add(IConstants.MOBILE_REL_ALTERNATE_URL_IS_CONSISTENT_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.NOODP_CHG_IND)) {
					databaseFieldSet.add(IConstants.NOODP_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.NOSNIPPET_CHG_IND)) {
					databaseFieldSet.add(IConstants.NOSNIPPET_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.NOYDIR_CHG_IND)) {
					databaseFieldSet.add(IConstants.NOYDIR_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.OPEN_GRAPH_ADDED_IND)) {
					databaseFieldSet.add(IConstants.OPEN_GRAPH_ADDED_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.OG_MARKUP_CHG_IND)) {
					databaseFieldSet.add(IConstants.OG_MARKUP_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.OPEN_GRAPH_REMOVED_IND)) {
					databaseFieldSet.add(IConstants.OPEN_GRAPH_REMOVED_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.OG_MARKUP_LENGTH_CHG_IND)) {
					databaseFieldSet.add(IConstants.OG_MARKUP_LENGTH_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.OUTLINK_COUNT_CHG_IND)) {
					databaseFieldSet.add(IConstants.OUTLINK_COUNT_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.PAGE_ANALYSIS_RESULTS_CHG_IND_JSON)) {
					databaseFieldSet.add(IConstants.PAGE_ANALYSIS_RESULTS_CHG_IND_JSON);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.PAGE_LINK_CHG_IND)) {
					databaseFieldSet.add(IConstants.PAGE_LINK_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.REDIRECT_BLOCKED_CHG_IND)) {
					databaseFieldSet.add(IConstants.REDIRECT_BLOCKED_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.REDIRECT_BLOCKED_REASON_CHG_IND)) {
					databaseFieldSet.add(IConstants.REDIRECT_BLOCKED_REASON_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.REDIRECT_CHAIN_CHG_IND)) {
					databaseFieldSet.add(IConstants.REDIRECT_CHAIN_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.REDIRECT_FINAL_URL_CHG_IND)) {
					databaseFieldSet.add(IConstants.REDIRECT_FINAL_URL_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.REDIRECT_TIMES_CHG_IND)) {
					databaseFieldSet.add(IConstants.REDIRECT_TIMES_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.RESPONSE_CODE_CHG_IND)) {
					databaseFieldSet.add(IConstants.RESPONSE_CODE_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.REDIRECT_301_DETECTED_IND)) {
					databaseFieldSet.add(IConstants.REDIRECT_301_DETECTED_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.REDIRECT_301_REMOVED_IND)) {
					databaseFieldSet.add(IConstants.REDIRECT_301_REMOVED_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.REDIRECT_302_DETECTED_IND)) {
					databaseFieldSet.add(IConstants.REDIRECT_302_DETECTED_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.REDIRECT_302_REMOVED_IND)) {
					databaseFieldSet.add(IConstants.REDIRECT_302_REMOVED_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.REDIRECT_DIFF_CODE_IND)) {
					databaseFieldSet.add(IConstants.REDIRECT_DIFF_CODE_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.RESPONSE_HEADERS_ADDED_IND)) {
					databaseFieldSet.add(IConstants.RESPONSE_HEADERS_ADDED_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.RESPONSE_HEADERS_REMOVED_IND)) {
					databaseFieldSet.add(IConstants.RESPONSE_HEADERS_REMOVED_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.ROBOTS_ADDED_IND)) {
					databaseFieldSet.add(IConstants.ROBOTS_ADDED_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.ROBOTS_CONTENTS_CHG_IND)) {
					databaseFieldSet.add(IConstants.ROBOTS_CONTENTS_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.ROBOTS_REMOVED_IND)) {
					databaseFieldSet.add(IConstants.ROBOTS_REMOVED_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.STRUCTURED_DATA_CHG_IND)) {
					databaseFieldSet.add(IConstants.STRUCTURED_DATA_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.TITLE_ADDED_IND)) {
					databaseFieldSet.add(IConstants.TITLE_ADDED_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.TITLE_CHG_IND)) {
					databaseFieldSet.add(IConstants.TITLE_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.TITLE_REMOVED_IND)) {
					databaseFieldSet.add(IConstants.TITLE_REMOVED_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.TITLE_LENGTH_CHG_IND)) {
					databaseFieldSet.add(IConstants.TITLE_LENGTH_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.VIEWPORT_ADDED_IND)) {
					databaseFieldSet.add(IConstants.VIEWPORT_ADDED_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.VIEWPORT_CONTENT_CHG_IND)) {
					databaseFieldSet.add(IConstants.VIEWPORT_CONTENT_CHG_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.VIEWPORT_REMOVED_IND)) {
					databaseFieldSet.add(IConstants.VIEWPORT_REMOVED_IND);
				} else if (StringUtils.equalsIgnoreCase(changeIndicator, IConstants.ROBOTS_TXT_CHG_IND)) {
					databaseFieldSet.add(IConstants.ROBOTS_TXT_CHG_IND);
				}
			}
			databaseFields = new ArrayList<String>(databaseFieldSet);
		} else {
			databaseFields = CrawlerUtils.getInstance().getChangeIndicatorDatabaseFields();
		}
		return databaseFields;
	}

	private List<TargetUrlChangeSummary> getTargetUrlChangeSummaryList(List<TargetUrlChangeHourlyTotals> targetUrlChangeHourlyTotalsList) {
		List<TargetUrlChangeSummary> targetUrlChangeSummaryList = new ArrayList<TargetUrlChangeSummary>();
		TargetUrlChangeSummary targetUrlChangeSummary = null;
		for (TargetUrlChangeHourlyTotals targetUrlChangeHourlyTotals : targetUrlChangeHourlyTotalsList) {
			targetUrlChangeSummary = getTargetUrlChangeSummary(targetUrlChangeHourlyTotals);
			targetUrlChangeSummaryList.add(targetUrlChangeSummary);
		}
		return targetUrlChangeSummaryList;
	}

	private TargetUrlChangeSummary getTargetUrlChangeSummary(TargetUrlChangeHourlyTotals targetUrlChangeHourlyTotals) {
		TargetUrlChangeSummary targetUrlChangeSummary = new TargetUrlChangeSummary();

		int[] totalArray = new int[7];
		totalArray[0] = 0; // total added
		totalArray[1] = 0; // total modified
		totalArray[2] = 0; // total removed
		totalArray[3] = 0; // total severity critical
		totalArray[4] = 0; // total severity high
		totalArray[5] = 0; // total severity medium
		totalArray[6] = 0; // total severity low

		List<ChangeIndicatorTotalUrls> changeIndicatorTotalUrlsList = new ArrayList<ChangeIndicatorTotalUrls>();

		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalAlternateLinksChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.ALTERNATE_LINKS_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalAlternateLinksChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalAmphtmlHrefChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.AMPHTML_HREF_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalAmphtmlHrefChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalAnalyzedUrlSChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.ANALYZED_URL_S_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalAnalyzedUrlSChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalArchiveFlgChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.ARCHIVE_FLG_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalArchiveFlgChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalBaseTagAddedInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.BASE_TAG_ADDED_IND, totalArray, targetUrlChangeHourlyTotals.getTotalBaseTagAddedInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalBaseTagChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.BASE_TAG_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalBaseTagChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalBaseTagRemovedInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.BASE_TAG_REMOVED_IND, totalArray, targetUrlChangeHourlyTotals.getTotalBaseTagRemovedInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalBaseTagTargetChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.BASE_TAG_TARGET_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalBaseTagTargetChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalBlockedByRobotsChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.BLOCKED_BY_ROBOTS_CHG_IND, totalArray,
					targetUrlChangeHourlyTotals.getTotalBlockedByRobotsChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalCanonicalAddedInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.CANONICAL_ADDED_IND, totalArray, targetUrlChangeHourlyTotals.getTotalCanonicalAddedInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalCanonicalChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.CANONICAL_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalCanonicalChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalCanonicalRemovedInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.CANONICAL_REMOVED_IND, totalArray, targetUrlChangeHourlyTotals.getTotalCanonicalRemovedInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalCanonicalHeaderFlagChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.CANONICAL_HEADER_FLAG_CHG_IND, totalArray,
					targetUrlChangeHourlyTotals.getTotalCanonicalHeaderFlagChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalCanonicalHeaderTypeChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.CANONICAL_HEADER_TYPE_CHG_IND, totalArray,
					targetUrlChangeHourlyTotals.getTotalCanonicalHeaderTypeChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalCanonicalTypeChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.CANONICAL_TYPE_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalCanonicalTypeChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalCanonicalUrlIsConsistentChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.CANONICAL_URL_IS_CONSISTENT_CHG_IND, totalArray,
					targetUrlChangeHourlyTotals.getTotalCanonicalUrlIsConsistentChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalContentTypeChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.CONTENT_TYPE_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalContentTypeChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalCustomDataAddedInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.CUSTOM_DATA_ADDED_IND, totalArray, targetUrlChangeHourlyTotals.getTotalCustomDataAddedInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalCustomDataChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.CUSTOM_DATA_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalCustomDataChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalCustomDataRemovedInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.CUSTOM_DATA_REMOVED_IND, totalArray, targetUrlChangeHourlyTotals.getTotalCustomDataRemovedInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalDescriptionAddedInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.DESCRIPTION_ADDED_IND, totalArray, targetUrlChangeHourlyTotals.getTotalDescriptionAddedInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalDescriptionChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.DESCRIPTION_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalDescriptionChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalDescriptionRemovedInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.DESCRIPTION_REMOVED_IND, totalArray, targetUrlChangeHourlyTotals.getTotalDescriptionRemovedInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalDescriptionLengthChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.DESCRIPTION_LENGTH_CHG_IND, totalArray,
					targetUrlChangeHourlyTotals.getTotalDescriptionLengthChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalErrorMessageChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.ERROR_MESSAGE_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalErrorMessageChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalFinalResponseCodeChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.FINAL_RESPONSE_CODE_CHG_IND, totalArray,
					targetUrlChangeHourlyTotals.getTotalFinalResponseCodeChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalFollowFlgChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.FOLLOW_FLG_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalFollowFlgChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalH1AddedInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.H1_ADDED_IND, totalArray, targetUrlChangeHourlyTotals.getTotalH1AddedInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalH1ChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.H1_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalH1ChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalH1RemovedInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.H1_REMOVED_IND, totalArray, targetUrlChangeHourlyTotals.getTotalH1RemovedInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalH1CountChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.H1_COUNT_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalH1CountChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalH1LengthChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.H1_LENGTH_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalH1LengthChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalH2AddedInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.H2_ADDED_IND, totalArray, targetUrlChangeHourlyTotals.getTotalH2AddedInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalH2ChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.H2_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalH2ChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalH2RemovedInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.H2_REMOVED_IND, totalArray, targetUrlChangeHourlyTotals.getTotalH2RemovedInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalHeaderNoarchiveChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.HEADER_NOARCHIVE_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalHeaderNoarchiveChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalHeaderNofollowChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.HEADER_NOFOLLOW_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalHeaderNofollowChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalHeaderNoindexChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.HEADER_NOINDEX_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalHeaderNoindexChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalHeaderNoodpChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.HEADER_NOODP_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalHeaderNoodpChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalHeaderNosnippetChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.HEADER_NOSNIPPET_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalHeaderNosnippetChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalHeaderNoydirChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.HEADER_NOYDIR_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalHeaderNoydirChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalHreflangErrorsChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.HREFLANG_ERRORS_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalHreflangErrorsChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalHreflangLinksChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.HREFLANG_LINKS_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalHreflangLinksChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalHreflangLinksOutCountChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.HREFLANG_LINKS_OUT_COUNT_CHG_IND, totalArray,
					targetUrlChangeHourlyTotals.getTotalHreflangLinksOutCountChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalHreflangLinksAddedInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.HREFLANG_LINKS_ADDED_IND, totalArray, targetUrlChangeHourlyTotals.getTotalHreflangLinksAddedInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalHreflangUrlCountChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.HREFLANG_URL_COUNT_CHG_IND, totalArray,
					targetUrlChangeHourlyTotals.getTotalHreflangUrlCountChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalHreflangLinksRemovedInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.HREFLANG_LINKS_REMOVED_IND, totalArray,
					targetUrlChangeHourlyTotals.getTotalHreflangLinksRemovedInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalIndexFlgChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.INDEX_FLG_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalIndexFlgChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalIndexableChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.INDEXABLE_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalIndexableChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalInsecureResourcesChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.INSECURE_RESOURCES_CHG_IND, totalArray,
					targetUrlChangeHourlyTotals.getTotalInsecureResourcesChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalMetaCharsetChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.META_CHARSET_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalMetaCharsetChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalMetaContentTypeChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.META_CONTENT_TYPE_CHG_IND, totalArray,
					targetUrlChangeHourlyTotals.getTotalMetaContentTypeChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalMetaDisabledSitelinksChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.META_DISABLED_SITELINKS_CHG_IND, totalArray,
					targetUrlChangeHourlyTotals.getTotalMetaDisabledSitelinksChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalMetaNoodpChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.META_NOODP_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalMetaNoodpChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalMetaNosnippetChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.META_NOSNIPPET_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalMetaNosnippetChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalMetaNoydirChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.META_NOYDIR_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalMetaNoydirChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalMetaRedirectChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.META_REDIRECT_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalMetaRedirectChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalMixedRedirectsChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.MIXED_REDIRECTS_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalMixedRedirectsChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalMobileRelAlternateUrlIsConsistentChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.MOBILE_REL_ALTERNATE_URL_IS_CONSISTENT_CHG_IND, totalArray,
					targetUrlChangeHourlyTotals.getTotalMobileRelAlternateUrlIsConsistentChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalNoodpChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.NOODP_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalNoodpChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalNosnippetChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.NOSNIPPET_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalNosnippetChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalNoydirChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.NOYDIR_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalNoydirChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalOpenGraphAddedInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.OPEN_GRAPH_ADDED_IND, totalArray, targetUrlChangeHourlyTotals.getTotalOpenGraphAddedInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalOgMarkupChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.OG_MARKUP_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalOgMarkupChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalOpenGraphRemovedInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.OPEN_GRAPH_REMOVED_IND, totalArray, targetUrlChangeHourlyTotals.getTotalOpenGraphRemovedInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalOgMarkupLengthChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.OG_MARKUP_LENGTH_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalOgMarkupLengthChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalOutlinkCountChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.OUTLINK_COUNT_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalOutlinkCountChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalPageAnalysisResultsChgIndJson())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.PAGE_ANALYSIS_RESULTS_CHG_IND_JSON, totalArray,
					targetUrlChangeHourlyTotals.getTotalPageAnalysisResultsChgIndJson());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalPageLinkChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.PAGE_LINK_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalPageLinkChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalRedirectBlockedChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.REDIRECT_BLOCKED_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalRedirectBlockedChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalRedirectBlockedReasonChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.REDIRECT_BLOCKED_REASON_CHG_IND, totalArray,
					targetUrlChangeHourlyTotals.getTotalRedirectBlockedReasonChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalRedirectChainChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.REDIRECT_CHAIN_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalRedirectChainChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalRedirectFinalUrlChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.REDIRECT_FINAL_URL_CHG_IND, totalArray,
					targetUrlChangeHourlyTotals.getTotalRedirectFinalUrlChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalRedirectTimesChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.REDIRECT_TIMES_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalRedirectTimesChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalResponseCodeChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.RESPONSE_CODE_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalResponseCodeChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotal404DetectedInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.RESPONSE_CODE_404_DETECTED_IND, totalArray, targetUrlChangeHourlyTotals.getTotal404DetectedInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotal404RemovedInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.RESPONSE_CODE_404_REMOVED_IND, totalArray, targetUrlChangeHourlyTotals.getTotal404RemovedInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalRedirect301DetectedInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.REDIRECT_301_DETECTED_IND, totalArray,
					targetUrlChangeHourlyTotals.getTotalRedirect301DetectedInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalRedirect301RemovedInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.REDIRECT_301_REMOVED_IND, totalArray, targetUrlChangeHourlyTotals.getTotalRedirect301RemovedInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalRedirect302DetectedInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.REDIRECT_302_DETECTED_IND, totalArray,
					targetUrlChangeHourlyTotals.getTotalRedirect302DetectedInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalRedirect302RemovedInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.REDIRECT_302_REMOVED_IND, totalArray, targetUrlChangeHourlyTotals.getTotalRedirect302RemovedInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalRedirectDiffCodeInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.REDIRECT_DIFF_CODE_IND, totalArray, targetUrlChangeHourlyTotals.getTotalRedirectDiffCodeInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalResponseHeadersAddedInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.RESPONSE_HEADERS_ADDED_IND, totalArray,
					targetUrlChangeHourlyTotals.getTotalResponseHeadersAddedInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalResponseHeadersRemovedInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.RESPONSE_HEADERS_REMOVED_IND, totalArray,
					targetUrlChangeHourlyTotals.getTotalResponseHeadersRemovedInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalRobotsAddedInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.ROBOTS_ADDED_IND, totalArray, targetUrlChangeHourlyTotals.getTotalRobotsAddedInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalRobotsContentsChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.ROBOTS_CONTENTS_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalRobotsContentsChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalRobotsRemovedInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.ROBOTS_REMOVED_IND, totalArray, targetUrlChangeHourlyTotals.getTotalRobotsRemovedInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalStructuredDataChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.STRUCTURED_DATA_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalStructuredDataChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalTitleAddedInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.TITLE_ADDED_IND, totalArray, targetUrlChangeHourlyTotals.getTotalTitleAddedInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalTitleChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.TITLE_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalTitleChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalTitleRemovedInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.TITLE_REMOVED_IND, totalArray, targetUrlChangeHourlyTotals.getTotalTitleRemovedInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalTitleLengthChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.TITLE_LENGTH_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalTitleLengthChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalViewportAddedInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.VIEWPORT_ADDED_IND, totalArray, targetUrlChangeHourlyTotals.getTotalViewportAddedInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalViewportContentChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.VIEWPORT_CONTENT_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalViewportContentChgInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalViewportRemovedInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.VIEWPORT_REMOVED_IND, totalArray, targetUrlChangeHourlyTotals.getTotalViewportRemovedInd());
		}
		if (isGreaterThanZero(targetUrlChangeHourlyTotals.getTotalRobotsTxtChgInd())) {
			calculateTotals(changeIndicatorTotalUrlsList, IConstants.ROBOTS_TXT_CHG_IND, totalArray, targetUrlChangeHourlyTotals.getTotalRobotsTxtChgInd());
		}

		targetUrlChangeSummary.setCrawl_date_hour(targetUrlChangeHourlyTotals.getCrawlDateHour());
		targetUrlChangeSummary.setTotal_added(totalArray[0]);
		targetUrlChangeSummary.setTotal_modified(totalArray[1]);
		targetUrlChangeSummary.setTotal_removed(totalArray[2]);
		targetUrlChangeSummary.setTotal_severity_critical(totalArray[3]);
		targetUrlChangeSummary.setTotal_severity_high(totalArray[4]);
		targetUrlChangeSummary.setTotal_severity_medium(totalArray[5]);
		targetUrlChangeSummary.setTotal_severity_low(totalArray[6]);
		targetUrlChangeSummary.setChange_indicator_total_urls_array(changeIndicatorTotalUrlsList.toArray(new ChangeIndicatorTotalUrls[0]));
		return targetUrlChangeSummary;
	}

	private boolean isGreaterThanZero(Integer integerInput) {
		boolean output = false;
		if (integerInput != null && integerInput.intValue() > 0) {
			output = true;
		}
		return output;
	}

	// update 'totalArray' and 'changeIndicatorTotalUrlsList' using pass by reference
	private void calculateTotals(List<ChangeIndicatorTotalUrls> changeIndicatorTotalUrlsList, String changeIndicator, int[] totalArray, int total) {
		ChangeIndicatorTotalUrls changeIndicatorTotalUrls = null;
		ContentGuardChangeTrackingEntity contentGuardChangeTrackingEntity = ContentGuardUtils.getInstance().getContentGuardChangeTrackingEntity(changeIndicator);
		if (contentGuardChangeTrackingEntity != null) {
			if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getType(), IConstants.CHANGE_TYPE_ADDED)) {
				totalArray[0] = totalArray[0] + total;
			} else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getType(), IConstants.CHANGE_TYPE_MODIFIED)) {
				totalArray[1] = totalArray[1] + total;
			} else if (StringUtils.equalsIgnoreCase(contentGuardChangeTrackingEntity.getType(), IConstants.CHANGE_TYPE_REMOVED)) {
				totalArray[2] = totalArray[2] + total;
			}
			final Integer criticalFlag = contentGuardChangeTrackingEntity.getCriticalFlag();
			switch (criticalFlag) {
				case IConstants.CONTENT_GUARD_CHANGE_SEVERITY_CRITICAL:
					totalArray[3] = totalArray[3] + total;
					break;
				case IConstants.CONTENT_GUARD_CHANGE_SEVERITY_HIGH:
					totalArray[4] = totalArray[4] + total;
					break;
				case IConstants.CONTENT_GUARD_CHANGE_SEVERITY_MEDIUM:
					totalArray[5] = totalArray[5] + total;
					break;
				case IConstants.CONTENT_GUARD_CHANGE_SEVERITY_LOW:
					totalArray[6] = totalArray[6] + total;
					break;
				default:
					break;
			}
			changeIndicatorTotalUrls = new ChangeIndicatorTotalUrls();
			changeIndicatorTotalUrls.setChange_indicator(changeIndicator);
			changeIndicatorTotalUrls.setTotal_urls(total);
			changeIndicatorTotalUrlsList.add(changeIndicatorTotalUrls);
		}
	}

	private List<TargetUrlChangeIndicatorDetail> getTargetUrlChangeIndicatorDetailList(TargetUrlChangeRequest targetUrlChangeRequest) throws Exception {

		log.info("getTargetUrlChangeIndicatorDetailList() targetUrlChangeRequest={}", targetUrlChangeRequest);

		List<TargetUrlChangeIndicatorDetail> targetUrlChangeIndicatorDetailList = new ArrayList<TargetUrlChangeIndicatorDetail>();

		int domainId = targetUrlChangeRequest.getDomain_id();
		String startCrawlTimestampString = targetUrlChangeRequest.getStart_crawl_timestamp();
		String endCrawlTimestampString = targetUrlChangeRequest.getEnd_crawl_timestamp();
		int sortBy = targetUrlChangeRequest.getSort_by();
		int rowsPerPage = targetUrlChangeRequest.getRows_per_page();
		int pageNumber = targetUrlChangeRequest.getPage_number();
		Integer[] pageTagIdArray = targetUrlChangeRequest.getPage_tag_ids();
		List<TargetUrlChangeIndClickHouseEntity> targetUrlChangeIndClickHouseEntityList = null;

		String urlPredicate = TargetUrlChangeUtils.getInstance().getUrlPredicate(targetUrlChangeRequest.getDomain_id(), targetUrlChangeRequest.getUrls());

		String contentTypePredicate = TargetUrlChangeUtils.getInstance().getContentTypePredicate(targetUrlChangeRequest.getDomain_id(),
				targetUrlChangeRequest.getContent_types());

		String responseCodePredicate = TargetUrlChangeUtils.getInstance().getResponseCodePredicate(targetUrlChangeRequest.getDomain_id(),
				targetUrlChangeRequest.getResponse_codes());

		String[] changeIndicatorArray = targetUrlChangeRequest.getChange_indicators();

		int offset = (pageNumber - 1) * rowsPerPage;

		List<String> databaseFields = TargetUrlChangeUtils.getInstance().getChangeDetailsDatabaseFields(changeIndicatorArray);

		// retrieve details
		boolean isTotal = false;
		targetUrlChangeIndClickHouseEntityList = TargetUrlChangeIndClickHouseDAO.getInstance().getIndicatorDetail(domainId, startCrawlTimestampString,
				endCrawlTimestampString, databaseFields, sortBy, rowsPerPage, offset, isTotal, changeIndicatorArray, pageTagIdArray, contentTypePredicate, urlPredicate,
				responseCodePredicate);
		targetUrlChangeIndicatorDetailList = convertTargetUrlChangeIndClickHouseEntityList(targetUrlChangeIndClickHouseEntityList);
		return targetUrlChangeIndicatorDetailList;
	}

	public List<TargetUrlChangeIndicatorDetail> convertTargetUrlChangeIndClickHouseEntityList(
			List<TargetUrlChangeIndClickHouseEntity> targetUrlChangeIndClickHouseEntityList) {
		List<TargetUrlChangeIndicatorDetail> targetUrlChangeIndicatorDetailList = null;
		TargetUrlChangeIndicatorDetail targetUrlChangeIndicatorDetail = null;
		HtmlHeading[] htmlHeadings = null;
		if (targetUrlChangeIndClickHouseEntityList != null && targetUrlChangeIndClickHouseEntityList.size() > 0) {
			//if (isDebug == true) {
			//	FormatUtils.getInstance().logMemoryUsage("convertTargetUrlChangeIndClickHouseEntityList() targetUrlChangeIndClickHouseEntityList.size()="
			//			+ targetUrlChangeIndClickHouseEntityList.size());
			//}
			targetUrlChangeIndicatorDetailList = new ArrayList<TargetUrlChangeIndicatorDetail>();
			for (TargetUrlChangeIndClickHouseEntity targetUrlChangeIndClickHouseEntity : targetUrlChangeIndClickHouseEntityList) {
				//if (isDebug == true) {
				//	System.out.println(
				//			"convertTargetUrlChangeIndClickHouseEntityList() targetUrlChangeIndClickHouseEntity=" + targetUrlChangeIndClickHouseEntity.toString());
				//}
				targetUrlChangeIndicatorDetail = new TargetUrlChangeIndicatorDetail();
				targetUrlChangeIndicatorDetail.setUrl(targetUrlChangeIndClickHouseEntity.getUrl());
				targetUrlChangeIndicatorDetail.setUrl_hash(targetUrlChangeIndClickHouseEntity.getUrlHash());
				targetUrlChangeIndicatorDetail.setUrl_murmur_hash(targetUrlChangeIndClickHouseEntity.getUrlMurmurHash());
				targetUrlChangeIndicatorDetail.setResponse_code_current(targetUrlChangeIndClickHouseEntity.getResponseCodeCurrent());
				targetUrlChangeIndicatorDetail.setResponse_code_previous(targetUrlChangeIndClickHouseEntity.getResponseCodePrevious());
				if (targetUrlChangeIndClickHouseEntity.getCurrentCrawlTimestamp() != null) {
					targetUrlChangeIndicatorDetail.setCurrent_crawl_timestamp(
							DateFormatUtils.format(targetUrlChangeIndClickHouseEntity.getCurrentCrawlTimestamp(), IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS));
				}
				if (targetUrlChangeIndClickHouseEntity.getPreviousCrawlTimestamp() != null) {
					targetUrlChangeIndicatorDetail.setPrevious_crawl_timestamp(
							DateFormatUtils.format(targetUrlChangeIndClickHouseEntity.getPreviousCrawlTimestamp(), IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS));
				}
				targetUrlChangeIndicatorDetail.setChange_indicator(targetUrlChangeIndClickHouseEntity.getChangeIndicator());
				if (targetUrlChangeIndClickHouseEntity.getChangeType() != null) {
					targetUrlChangeIndicatorDetail.setChange_type(getChangeTypeString(targetUrlChangeIndClickHouseEntity.getChangeType()));
				}
				if (targetUrlChangeIndClickHouseEntity.getCriticalInd() != null) {
					targetUrlChangeIndicatorDetail.setSeverity(getSeverityString(targetUrlChangeIndClickHouseEntity.getCriticalInd()));
				}

				// alternate_links_chg_ind
				if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.ALTERNATE_LINKS_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setAlternate_links_current(targetUrlChangeIndClickHouseEntity.getAlternateLinksCurrent());
					targetUrlChangeIndicatorDetail.setAlternate_links_previous(targetUrlChangeIndClickHouseEntity.getAlternateLinksPrevious());
					if (targetUrlChangeIndicatorDetail.getAlternate_links_current() != null && targetUrlChangeIndicatorDetail.getAlternate_links_current().length > 0) {
						for (int i = 0; i < targetUrlChangeIndicatorDetail.getAlternate_links_current().length; i++) {
							targetUrlChangeIndicatorDetail.getAlternate_links_current()[i].setIndex(i + 1);
						}
					}
					if (targetUrlChangeIndicatorDetail.getAlternate_links_previous() != null
							&& targetUrlChangeIndicatorDetail.getAlternate_links_previous().length > 0) {
						for (int i = 0; i < targetUrlChangeIndicatorDetail.getAlternate_links_previous().length; i++) {
							targetUrlChangeIndicatorDetail.getAlternate_links_previous()[i].setIndex(i + 1);
						}
					}
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// amphtml_href_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.AMPHTML_HREF_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setAmphtml_href_current(targetUrlChangeIndClickHouseEntity.getAmphtmlHrefCurrent());
					targetUrlChangeIndicatorDetail.setAmphtml_href_previous(targetUrlChangeIndClickHouseEntity.getAmphtmlHrefPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// analyzed_url_s_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.ANALYZED_URL_S_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setAnalyzed_url_s_current(targetUrlChangeIndClickHouseEntity.getAnalyzedUrlSCurrent());
					targetUrlChangeIndicatorDetail.setAnalyzed_url_s_previous(targetUrlChangeIndClickHouseEntity.getAnalyzedUrlSPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// archive_flg_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.ARCHIVE_FLG_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setArchive_flg_current(targetUrlChangeIndClickHouseEntity.getArchiveFlgCurrent());
					targetUrlChangeIndicatorDetail.setArchive_flg_previous(targetUrlChangeIndClickHouseEntity.getArchiveFlgPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// base_tag_added_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.BASE_TAG_ADDED_IND)) {
					targetUrlChangeIndicatorDetail.setBase_tag_current(targetUrlChangeIndClickHouseEntity.getBaseTagCurrent());
					targetUrlChangeIndicatorDetail.setBase_tag_previous(targetUrlChangeIndClickHouseEntity.getBaseTagPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// base_tag_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.BASE_TAG_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setBase_tag_current(targetUrlChangeIndClickHouseEntity.getBaseTagCurrent());
					targetUrlChangeIndicatorDetail.setBase_tag_previous(targetUrlChangeIndClickHouseEntity.getBaseTagPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// base_tag_removed_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.BASE_TAG_REMOVED_IND)) {
					targetUrlChangeIndicatorDetail.setBase_tag_current(targetUrlChangeIndClickHouseEntity.getBaseTagCurrent());
					targetUrlChangeIndicatorDetail.setBase_tag_previous(targetUrlChangeIndClickHouseEntity.getBaseTagPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// base_tag_target_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.BASE_TAG_TARGET_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setBase_tag_target_current(targetUrlChangeIndClickHouseEntity.getBaseTagTargetCurrent());
					targetUrlChangeIndicatorDetail.setBase_tag_target_previous(targetUrlChangeIndClickHouseEntity.getBaseTagTargetPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// blocked_by_robots_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.BLOCKED_BY_ROBOTS_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setBlocked_by_robots_current(targetUrlChangeIndClickHouseEntity.getBlockedByRobotsCurrent());
					targetUrlChangeIndicatorDetail.setBlocked_by_robots_previous(targetUrlChangeIndClickHouseEntity.getBlockedByRobotsPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// canonical_added_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.CANONICAL_ADDED_IND)) {
					targetUrlChangeIndicatorDetail.setCanonical_current(targetUrlChangeIndClickHouseEntity.getCanonicalCurrent());
					targetUrlChangeIndicatorDetail.setCanonical_previous(targetUrlChangeIndClickHouseEntity.getCanonicalPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// canonical_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.CANONICAL_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setCanonical_current(targetUrlChangeIndClickHouseEntity.getCanonicalCurrent());
					targetUrlChangeIndicatorDetail.setCanonical_previous(targetUrlChangeIndClickHouseEntity.getCanonicalPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// canonical_removed_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.CANONICAL_REMOVED_IND)) {
					targetUrlChangeIndicatorDetail.setCanonical_current(targetUrlChangeIndClickHouseEntity.getCanonicalCurrent());
					targetUrlChangeIndicatorDetail.setCanonical_previous(targetUrlChangeIndClickHouseEntity.getCanonicalPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// canonical_header_flag_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.CANONICAL_HEADER_FLAG_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setCanonical_header_flag_current(targetUrlChangeIndClickHouseEntity.getCanonicalHeaderFlagCurrent());
					targetUrlChangeIndicatorDetail.setCanonical_header_flag_previous(targetUrlChangeIndClickHouseEntity.getCanonicalHeaderFlagPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// canonical_header_type_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.CANONICAL_HEADER_TYPE_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setCanonical_header_type_current(targetUrlChangeIndClickHouseEntity.getCanonicalHeaderTypeCurrent());
					targetUrlChangeIndicatorDetail.setCanonical_header_type_previous(targetUrlChangeIndClickHouseEntity.getCanonicalHeaderTypePrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// canonical_type_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.CANONICAL_TYPE_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setCanonical_type_current(targetUrlChangeIndClickHouseEntity.getCanonicalTypeCurrent());
					targetUrlChangeIndicatorDetail.setCanonical_type_previous(targetUrlChangeIndClickHouseEntity.getCanonicalTypePrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// canonical_url_is_consistent_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.CANONICAL_URL_IS_CONSISTENT_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setCanonical_url_is_consistent_current(targetUrlChangeIndClickHouseEntity.getCanonicalUrlIsConsistentCurrent());
					targetUrlChangeIndicatorDetail.setCanonical_url_is_consistent_previous(targetUrlChangeIndClickHouseEntity.getCanonicalUrlIsConsistentPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// content_type_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.CONTENT_TYPE_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setContent_type_current(targetUrlChangeIndClickHouseEntity.getContentTypeCurrent());
					targetUrlChangeIndicatorDetail.setContent_type_previous(targetUrlChangeIndClickHouseEntity.getContentTypePrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// custom_data_added_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.CUSTOM_DATA_ADDED_IND)) {
					targetUrlChangeIndicatorDetail.setCustom_data_current(targetUrlChangeIndClickHouseEntity.getCustomDataCurrent());
					targetUrlChangeIndicatorDetail.setCustom_data_previous(targetUrlChangeIndClickHouseEntity.getCustomDataPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// custom_data_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.CUSTOM_DATA_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setCustom_data_current(targetUrlChangeIndClickHouseEntity.getCustomDataCurrent());
					targetUrlChangeIndicatorDetail.setCustom_data_previous(targetUrlChangeIndClickHouseEntity.getCustomDataPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// custom_data_removed_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.CUSTOM_DATA_REMOVED_IND)) {
					targetUrlChangeIndicatorDetail.setCustom_data_current(targetUrlChangeIndClickHouseEntity.getCustomDataCurrent());
					targetUrlChangeIndicatorDetail.setCustom_data_previous(targetUrlChangeIndClickHouseEntity.getCustomDataPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// description_added_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.DESCRIPTION_ADDED_IND)) {
					targetUrlChangeIndicatorDetail.setDescription_current(targetUrlChangeIndClickHouseEntity.getDescriptionCurrent());
					targetUrlChangeIndicatorDetail.setDescription_previous(targetUrlChangeIndClickHouseEntity.getDescriptionPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// description_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.DESCRIPTION_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setDescription_current(targetUrlChangeIndClickHouseEntity.getDescriptionCurrent());
					targetUrlChangeIndicatorDetail.setDescription_previous(targetUrlChangeIndClickHouseEntity.getDescriptionPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// description_removed_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.DESCRIPTION_REMOVED_IND)) {
					targetUrlChangeIndicatorDetail.setDescription_current(targetUrlChangeIndClickHouseEntity.getDescriptionCurrent());
					targetUrlChangeIndicatorDetail.setDescription_previous(targetUrlChangeIndClickHouseEntity.getDescriptionPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// description_length_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.DESCRIPTION_LENGTH_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setDescription_length_current(targetUrlChangeIndClickHouseEntity.getDescriptionLengthCurrent());
					targetUrlChangeIndicatorDetail.setDescription_length_previous(targetUrlChangeIndClickHouseEntity.getDescriptionLengthPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// error_message_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.ERROR_MESSAGE_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setError_message_current(targetUrlChangeIndClickHouseEntity.getErrorMessageCurrent());
					targetUrlChangeIndicatorDetail.setError_message_previous(targetUrlChangeIndClickHouseEntity.getErrorMessagePrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// final_response_code_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.FINAL_RESPONSE_CODE_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setFinal_response_code_current(targetUrlChangeIndClickHouseEntity.getFinalResponseCodeCurrent());
					targetUrlChangeIndicatorDetail.setFinal_response_code_previous(targetUrlChangeIndClickHouseEntity.getFinalResponseCodePrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// follow_flg_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.FOLLOW_FLG_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setFollow_flg_current(targetUrlChangeIndClickHouseEntity.getFollowFlgCurrent());
					targetUrlChangeIndicatorDetail.setFollow_flg_previous(targetUrlChangeIndClickHouseEntity.getFollowFlgPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// h1_added_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.H1_ADDED_IND)) {
					htmlHeadings = TargetUrlChangeUtils.getInstance().getHtmlHeadings(targetUrlChangeIndClickHouseEntity.getH1Current());
					targetUrlChangeIndicatorDetail.setH1_current(htmlHeadings);
					htmlHeadings = TargetUrlChangeUtils.getInstance().getHtmlHeadings(targetUrlChangeIndClickHouseEntity.getH1Previous());
					targetUrlChangeIndicatorDetail.setH1_previous(htmlHeadings);
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// h1_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.H1_CHG_IND)) {
					htmlHeadings = TargetUrlChangeUtils.getInstance().getHtmlHeadings(targetUrlChangeIndClickHouseEntity.getH1Current());
					targetUrlChangeIndicatorDetail.setH1_current(htmlHeadings);
					htmlHeadings = TargetUrlChangeUtils.getInstance().getHtmlHeadings(targetUrlChangeIndClickHouseEntity.getH1Previous());
					targetUrlChangeIndicatorDetail.setH1_previous(htmlHeadings);
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// h1_removed_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.H1_REMOVED_IND)) {
					htmlHeadings = TargetUrlChangeUtils.getInstance().getHtmlHeadings(targetUrlChangeIndClickHouseEntity.getH1Current());
					targetUrlChangeIndicatorDetail.setH1_current(htmlHeadings);
					htmlHeadings = TargetUrlChangeUtils.getInstance().getHtmlHeadings(targetUrlChangeIndClickHouseEntity.getH1Previous());
					targetUrlChangeIndicatorDetail.setH1_previous(htmlHeadings);
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// h1_count_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.H1_COUNT_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setH1_count_current(targetUrlChangeIndClickHouseEntity.getH1CountCurrent());
					targetUrlChangeIndicatorDetail.setH1_count_previous(targetUrlChangeIndClickHouseEntity.getH1CountPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// h1_length_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.H1_LENGTH_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setH1_length_current(targetUrlChangeIndClickHouseEntity.getH1LengthCurrent());
					targetUrlChangeIndicatorDetail.setH1_length_previous(targetUrlChangeIndClickHouseEntity.getH1LengthPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// h2_added_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.H2_ADDED_IND)) {
					htmlHeadings = TargetUrlChangeUtils.getInstance().getHtmlHeadings(targetUrlChangeIndClickHouseEntity.getH2Current());
					targetUrlChangeIndicatorDetail.setH2_current(htmlHeadings);
					htmlHeadings = TargetUrlChangeUtils.getInstance().getHtmlHeadings(targetUrlChangeIndClickHouseEntity.getH2Previous());
					targetUrlChangeIndicatorDetail.setH2_previous(htmlHeadings);
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// h2_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.H2_CHG_IND)) {
					htmlHeadings = TargetUrlChangeUtils.getInstance().getHtmlHeadings(targetUrlChangeIndClickHouseEntity.getH2Current());
					targetUrlChangeIndicatorDetail.setH2_current(htmlHeadings);
					htmlHeadings = TargetUrlChangeUtils.getInstance().getHtmlHeadings(targetUrlChangeIndClickHouseEntity.getH2Previous());
					targetUrlChangeIndicatorDetail.setH2_previous(htmlHeadings);
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// h2_removed_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.H2_REMOVED_IND)) {
					htmlHeadings = TargetUrlChangeUtils.getInstance().getHtmlHeadings(targetUrlChangeIndClickHouseEntity.getH2Current());
					targetUrlChangeIndicatorDetail.setH2_current(htmlHeadings);
					htmlHeadings = TargetUrlChangeUtils.getInstance().getHtmlHeadings(targetUrlChangeIndClickHouseEntity.getH2Previous());
					targetUrlChangeIndicatorDetail.setH2_previous(htmlHeadings);
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// header_noarchive_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.HEADER_NOARCHIVE_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setHeader_noarchive_current(targetUrlChangeIndClickHouseEntity.getHeaderNoarchiveCurrent());
					targetUrlChangeIndicatorDetail.setHeader_noarchive_previous(targetUrlChangeIndClickHouseEntity.getHeaderNoarchivePrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// header_nofollow_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.HEADER_NOFOLLOW_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setHeader_nofollow_current(targetUrlChangeIndClickHouseEntity.getHeaderNofollowCurrent());
					targetUrlChangeIndicatorDetail.setHeader_nofollow_previous(targetUrlChangeIndClickHouseEntity.getHeaderNofollowPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// header_noindex_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.HEADER_NOINDEX_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setHeader_noindex_current(targetUrlChangeIndClickHouseEntity.getHeaderNoindexCurrent());
					targetUrlChangeIndicatorDetail.setHeader_noindex_previous(targetUrlChangeIndClickHouseEntity.getHeaderNoindexPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// header_noodp_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.HEADER_NOODP_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setHeader_noodp_current(targetUrlChangeIndClickHouseEntity.getHeaderNoodpCurrent());
					targetUrlChangeIndicatorDetail.setHeader_noodp_previous(targetUrlChangeIndClickHouseEntity.getHeaderNoodpPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// header_nosnippet_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.HEADER_NOSNIPPET_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setHeader_nosnippet_current(targetUrlChangeIndClickHouseEntity.getHeaderNosnippetCurrent());
					targetUrlChangeIndicatorDetail.setHeader_nosnippet_previous(targetUrlChangeIndClickHouseEntity.getHeaderNosnippetPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// header_noydir_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.HEADER_NOYDIR_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setHeader_noydir_current(targetUrlChangeIndClickHouseEntity.getHeaderNoydirCurrent());
					targetUrlChangeIndicatorDetail.setHeader_noydir_previous(targetUrlChangeIndClickHouseEntity.getHeaderNoydirPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// hreflang_errors_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.HREFLANG_ERRORS_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setHreflang_errors_current(targetUrlChangeIndClickHouseEntity.getHreflangErrorsCurrent());
					targetUrlChangeIndicatorDetail.setHreflang_errors_previous(targetUrlChangeIndClickHouseEntity.getHreflangErrorsPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// hreflang_links_added_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.HREFLANG_LINKS_ADDED_IND)) {
					targetUrlChangeIndicatorDetail.setHreflang_url_count_current(targetUrlChangeIndClickHouseEntity.getHreflangUrlCountCurrent());
					targetUrlChangeIndicatorDetail.setHreflang_url_count_previous(targetUrlChangeIndClickHouseEntity.getHreflangUrlCountPrevious());
					targetUrlChangeIndicatorDetail.setHreflang_links_current(targetUrlChangeIndClickHouseEntity.getHreflangLinksCurrent());
					targetUrlChangeIndicatorDetail.setHreflang_links_previous(targetUrlChangeIndClickHouseEntity.getHreflangLinksPrevious());
					if (targetUrlChangeIndicatorDetail.getHreflang_links_current() != null && targetUrlChangeIndicatorDetail.getHreflang_links_current().length > 0) {
						for (int i = 0; i < targetUrlChangeIndicatorDetail.getHreflang_links_current().length; i++) {
							targetUrlChangeIndicatorDetail.getHreflang_links_current()[i].setIndex(i + 1);
						}
					}
					if (targetUrlChangeIndicatorDetail.getHreflang_links_previous() != null && targetUrlChangeIndicatorDetail.getHreflang_links_previous().length > 0) {
						for (int i = 0; i < targetUrlChangeIndicatorDetail.getHreflang_links_previous().length; i++) {
							targetUrlChangeIndicatorDetail.getHreflang_links_previous()[i].setIndex(i + 1);
						}
					}
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// hreflang_links_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.HREFLANG_LINKS_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setHreflang_url_count_current(targetUrlChangeIndClickHouseEntity.getHreflangUrlCountCurrent());
					targetUrlChangeIndicatorDetail.setHreflang_url_count_previous(targetUrlChangeIndClickHouseEntity.getHreflangUrlCountPrevious());
					targetUrlChangeIndicatorDetail.setHreflang_links_current(targetUrlChangeIndClickHouseEntity.getHreflangLinksCurrent());
					targetUrlChangeIndicatorDetail.setHreflang_links_previous(targetUrlChangeIndClickHouseEntity.getHreflangLinksPrevious());
					if (targetUrlChangeIndicatorDetail.getHreflang_links_current() != null && targetUrlChangeIndicatorDetail.getHreflang_links_current().length > 0) {
						for (int i = 0; i < targetUrlChangeIndicatorDetail.getHreflang_links_current().length; i++) {
							targetUrlChangeIndicatorDetail.getHreflang_links_current()[i].setIndex(i + 1);
						}
					}
					if (targetUrlChangeIndicatorDetail.getHreflang_links_previous() != null && targetUrlChangeIndicatorDetail.getHreflang_links_previous().length > 0) {
						for (int i = 0; i < targetUrlChangeIndicatorDetail.getHreflang_links_previous().length; i++) {
							targetUrlChangeIndicatorDetail.getHreflang_links_previous()[i].setIndex(i + 1);
						}
					}
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// hreflang_links_removed_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.HREFLANG_LINKS_REMOVED_IND)) {
					targetUrlChangeIndicatorDetail.setHreflang_url_count_current(targetUrlChangeIndClickHouseEntity.getHreflangUrlCountCurrent());
					targetUrlChangeIndicatorDetail.setHreflang_url_count_previous(targetUrlChangeIndClickHouseEntity.getHreflangUrlCountPrevious());
					targetUrlChangeIndicatorDetail.setHreflang_links_current(targetUrlChangeIndClickHouseEntity.getHreflangLinksCurrent());
					targetUrlChangeIndicatorDetail.setHreflang_links_previous(targetUrlChangeIndClickHouseEntity.getHreflangLinksPrevious());
					if (targetUrlChangeIndicatorDetail.getHreflang_links_current() != null && targetUrlChangeIndicatorDetail.getHreflang_links_current().length > 0) {
						for (int i = 0; i < targetUrlChangeIndicatorDetail.getHreflang_links_current().length; i++) {
							targetUrlChangeIndicatorDetail.getHreflang_links_current()[i].setIndex(i + 1);
						}
					}
					if (targetUrlChangeIndicatorDetail.getHreflang_links_previous() != null && targetUrlChangeIndicatorDetail.getHreflang_links_previous().length > 0) {
						for (int i = 0; i < targetUrlChangeIndicatorDetail.getHreflang_links_previous().length; i++) {
							targetUrlChangeIndicatorDetail.getHreflang_links_previous()[i].setIndex(i + 1);
						}
					}
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// hreflang_links_out_count_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.HREFLANG_LINKS_OUT_COUNT_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setHreflang_links_out_count_current(targetUrlChangeIndClickHouseEntity.getHreflangLinksOutCountCurrent());
					targetUrlChangeIndicatorDetail.setHreflang_links_out_count_previous(targetUrlChangeIndClickHouseEntity.getHreflangLinksOutCountPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// hreflang_url_count_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.HREFLANG_URL_COUNT_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setHreflang_url_count_current(targetUrlChangeIndClickHouseEntity.getHreflangUrlCountCurrent());
					targetUrlChangeIndicatorDetail.setHreflang_url_count_previous(targetUrlChangeIndClickHouseEntity.getHreflangUrlCountPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// index_flg_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.INDEX_FLG_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setIndex_flg_current(targetUrlChangeIndClickHouseEntity.getIndexFlgCurrent());
					targetUrlChangeIndicatorDetail.setIndex_flg_previous(targetUrlChangeIndClickHouseEntity.getIndexFlgPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// indexable_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.INDEXABLE_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setIndexable_current(targetUrlChangeIndClickHouseEntity.getIndexableCurrent());
					targetUrlChangeIndicatorDetail.setIndexable_previous(targetUrlChangeIndClickHouseEntity.getIndexablePrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// insecure_resources_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.INSECURE_RESOURCES_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setInsecure_resources_current(targetUrlChangeIndClickHouseEntity.getInsecureResourcesCurrent());
					targetUrlChangeIndicatorDetail.setInsecure_resources_previous(targetUrlChangeIndClickHouseEntity.getInsecureResourcesPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// meta_charset_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.META_CHARSET_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setMeta_charset_current(targetUrlChangeIndClickHouseEntity.getMetaCharsetCurrent());
					targetUrlChangeIndicatorDetail.setMeta_charset_previous(targetUrlChangeIndClickHouseEntity.getMetaCharsetPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// meta_content_type_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.META_CONTENT_TYPE_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setMeta_content_type_current(targetUrlChangeIndClickHouseEntity.getMetaContentTypeCurrent());
					targetUrlChangeIndicatorDetail.setMeta_content_type_previous(targetUrlChangeIndClickHouseEntity.getMetaContentTypePrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// meta_disabled_sitelinks_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.META_DISABLED_SITELINKS_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setMeta_disabled_sitelinks_current(targetUrlChangeIndClickHouseEntity.getMetaDisabledSitelinksCurrent());
					targetUrlChangeIndicatorDetail.setMeta_disabled_sitelinks_previous(targetUrlChangeIndClickHouseEntity.getMetaDisabledSitelinksPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// meta_noodp_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.META_NOODP_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setMeta_noodp_current(targetUrlChangeIndClickHouseEntity.getMetaNoodpCurrent());
					targetUrlChangeIndicatorDetail.setMeta_noodp_previous(targetUrlChangeIndClickHouseEntity.getMetaNoodpPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// meta_nosnippet_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.META_NOSNIPPET_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setMeta_nosnippet_current(targetUrlChangeIndClickHouseEntity.getMetaNosnippetCurrent());
					targetUrlChangeIndicatorDetail.setMeta_nosnippet_previous(targetUrlChangeIndClickHouseEntity.getMetaNosnippetPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// meta_noydir_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.META_NOYDIR_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setMeta_noydir_current(targetUrlChangeIndClickHouseEntity.getMetaNoydirCurrent());
					targetUrlChangeIndicatorDetail.setMeta_noydir_previous(targetUrlChangeIndClickHouseEntity.getMetaNoydirPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// meta_redirect_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.META_REDIRECT_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setMeta_redirect_current(targetUrlChangeIndClickHouseEntity.getMetaRedirectCurrent());
					targetUrlChangeIndicatorDetail.setMeta_redirect_previous(targetUrlChangeIndClickHouseEntity.getMetaRedirectPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// mixed_redirects_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.MIXED_REDIRECTS_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setMixed_redirects_current(targetUrlChangeIndClickHouseEntity.getMixedRedirectsCurrent());
					targetUrlChangeIndicatorDetail.setMixed_redirects_previous(targetUrlChangeIndClickHouseEntity.getMixedRedirectsPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// mobile_rel_alternate_url_is_consistent_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(),
						IConstants.MOBILE_REL_ALTERNATE_URL_IS_CONSISTENT_CHG_IND)) {
					targetUrlChangeIndicatorDetail
							.setMobile_rel_alternate_url_is_consistent_current(targetUrlChangeIndClickHouseEntity.getMobileRelAlternateUrlIsConsistentCurrent());
					targetUrlChangeIndicatorDetail
							.setMobile_rel_alternate_url_is_consistent_previous(targetUrlChangeIndClickHouseEntity.getMobileRelAlternateUrlIsConsistentPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// noodp_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.NOODP_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setNoodp_current(targetUrlChangeIndClickHouseEntity.getNoodpCurrent());
					targetUrlChangeIndicatorDetail.setNoodp_previous(targetUrlChangeIndClickHouseEntity.getNoodpPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// nosnippet_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.NOSNIPPET_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setNosnippet_current(targetUrlChangeIndClickHouseEntity.getNosnippetCurrent());
					targetUrlChangeIndicatorDetail.setNosnippet_previous(targetUrlChangeIndClickHouseEntity.getNosnippetPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// noydir_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.NOYDIR_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setNoydir_current(targetUrlChangeIndClickHouseEntity.getNoydirCurrent());
					targetUrlChangeIndicatorDetail.setNoydir_previous(targetUrlChangeIndClickHouseEntity.getNoydirPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// open_graph_added_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.OPEN_GRAPH_ADDED_IND)) {
					targetUrlChangeIndicatorDetail.setOg_markup_current(targetUrlChangeIndClickHouseEntity.getOgMarkupCurrent());
					targetUrlChangeIndicatorDetail.setOg_markup_previous(targetUrlChangeIndClickHouseEntity.getOgMarkupPrevious());
					if (targetUrlChangeIndicatorDetail.getOg_markup_current() != null && targetUrlChangeIndicatorDetail.getOg_markup_current().length > 0) {
						for (int i = 0; i < targetUrlChangeIndicatorDetail.getOg_markup_current().length; i++) {
							targetUrlChangeIndicatorDetail.getOg_markup_current()[i].setIndex(i + 1);
						}
					}
					if (targetUrlChangeIndicatorDetail.getOg_markup_previous() != null && targetUrlChangeIndicatorDetail.getOg_markup_previous().length > 0) {
						for (int i = 0; i < targetUrlChangeIndicatorDetail.getOg_markup_previous().length; i++) {
							targetUrlChangeIndicatorDetail.getOg_markup_previous()[i].setIndex(i + 1);
						}
					}
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// og_markup_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.OG_MARKUP_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setOg_markup_current(targetUrlChangeIndClickHouseEntity.getOgMarkupCurrent());
					targetUrlChangeIndicatorDetail.setOg_markup_previous(targetUrlChangeIndClickHouseEntity.getOgMarkupPrevious());
					if (targetUrlChangeIndicatorDetail.getOg_markup_current() != null && targetUrlChangeIndicatorDetail.getOg_markup_current().length > 0) {
						for (int i = 0; i < targetUrlChangeIndicatorDetail.getOg_markup_current().length; i++) {
							targetUrlChangeIndicatorDetail.getOg_markup_current()[i].setIndex(i + 1);
						}
					}
					if (targetUrlChangeIndicatorDetail.getOg_markup_previous() != null && targetUrlChangeIndicatorDetail.getOg_markup_previous().length > 0) {
						for (int i = 0; i < targetUrlChangeIndicatorDetail.getOg_markup_previous().length; i++) {
							targetUrlChangeIndicatorDetail.getOg_markup_previous()[i].setIndex(i + 1);
						}
					}
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// open_graph_removed_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.OPEN_GRAPH_REMOVED_IND)) {
					targetUrlChangeIndicatorDetail.setOg_markup_current(targetUrlChangeIndClickHouseEntity.getOgMarkupCurrent());
					targetUrlChangeIndicatorDetail.setOg_markup_previous(targetUrlChangeIndClickHouseEntity.getOgMarkupPrevious());
					if (targetUrlChangeIndicatorDetail.getOg_markup_current() != null && targetUrlChangeIndicatorDetail.getOg_markup_current().length > 0) {
						for (int i = 0; i < targetUrlChangeIndicatorDetail.getOg_markup_current().length; i++) {
							targetUrlChangeIndicatorDetail.getOg_markup_current()[i].setIndex(i + 1);
						}
					}
					if (targetUrlChangeIndicatorDetail.getOg_markup_previous() != null && targetUrlChangeIndicatorDetail.getOg_markup_previous().length > 0) {
						for (int i = 0; i < targetUrlChangeIndicatorDetail.getOg_markup_previous().length; i++) {
							targetUrlChangeIndicatorDetail.getOg_markup_previous()[i].setIndex(i + 1);
						}
					}
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// og_markup_length_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.OG_MARKUP_LENGTH_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setOg_markup_length_current(targetUrlChangeIndClickHouseEntity.getOgMarkupLengthCurrent());
					targetUrlChangeIndicatorDetail.setOg_markup_length_previous(targetUrlChangeIndClickHouseEntity.getOgMarkupLengthPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// outlink_count_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.OUTLINK_COUNT_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setOutlink_count_current(targetUrlChangeIndClickHouseEntity.getOutlinkCountCurrent());
					targetUrlChangeIndicatorDetail.setOutlink_count_previous(targetUrlChangeIndClickHouseEntity.getOutlinkCountPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// page_analysis_results_chg_ind_json
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.PAGE_ANALYSIS_RESULTS_CHG_IND_JSON)) {
					targetUrlChangeIndicatorDetail.setPage_analysis_results_chg_ind_json(targetUrlChangeIndClickHouseEntity.getPageAnalysisResultsChgIndJson());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// page_link_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.PAGE_LINK_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setPage_link_current(targetUrlChangeIndClickHouseEntity.getPageLinkCurrent());
					targetUrlChangeIndicatorDetail.setPage_link_previous(targetUrlChangeIndClickHouseEntity.getPageLinkPrevious());
					if (targetUrlChangeIndicatorDetail.getPage_link_current() != null && targetUrlChangeIndicatorDetail.getPage_link_current().length > 0) {
						for (int i = 0; i < targetUrlChangeIndicatorDetail.getPage_link_current().length; i++) {
							targetUrlChangeIndicatorDetail.getPage_link_current()[i].setIndex(i + 1);
						}
					}
					if (targetUrlChangeIndicatorDetail.getPage_link_previous() != null && targetUrlChangeIndicatorDetail.getPage_link_previous().length > 0) {
						for (int i = 0; i < targetUrlChangeIndicatorDetail.getPage_link_previous().length; i++) {
							targetUrlChangeIndicatorDetail.getPage_link_previous()[i].setIndex(i + 1);
						}
					}
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// redirect_blocked_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.REDIRECT_BLOCKED_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setRedirect_blocked_current(targetUrlChangeIndClickHouseEntity.getRedirectBlockedCurrent());
					targetUrlChangeIndicatorDetail.setRedirect_blocked_previous(targetUrlChangeIndClickHouseEntity.getRedirectBlockedPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// redirect_blocked_reason_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.REDIRECT_BLOCKED_REASON_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setRedirect_blocked_reason_current(targetUrlChangeIndClickHouseEntity.getRedirectBlockedReasonCurrent());
					targetUrlChangeIndicatorDetail.setRedirect_blocked_reason_previous(targetUrlChangeIndClickHouseEntity.getRedirectBlockedReasonPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// redirect_chain_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.REDIRECT_CHAIN_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setRedirect_chain_current(targetUrlChangeIndClickHouseEntity.getRedirectChainCurrent());
					targetUrlChangeIndicatorDetail.setRedirect_chain_previous(targetUrlChangeIndClickHouseEntity.getRedirectChainPrevious());
					if (targetUrlChangeIndicatorDetail.getRedirect_chain_current() != null && targetUrlChangeIndicatorDetail.getRedirect_chain_current().length > 0) {
						for (int i = 0; i < targetUrlChangeIndicatorDetail.getRedirect_chain_current().length; i++) {
							targetUrlChangeIndicatorDetail.getRedirect_chain_current()[i].setIndex(i + 1);
						}
					}
					if (targetUrlChangeIndicatorDetail.getRedirect_chain_previous() != null && targetUrlChangeIndicatorDetail.getRedirect_chain_previous().length > 0) {
						for (int i = 0; i < targetUrlChangeIndicatorDetail.getRedirect_chain_previous().length; i++) {
							targetUrlChangeIndicatorDetail.getRedirect_chain_previous()[i].setIndex(i + 1);
						}
					}
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// redirect_final_url_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.REDIRECT_FINAL_URL_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setRedirect_final_url_current(targetUrlChangeIndClickHouseEntity.getRedirectFinalUrlCurrent());
					targetUrlChangeIndicatorDetail.setRedirect_final_url_previous(targetUrlChangeIndClickHouseEntity.getRedirectFinalUrlPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// redirect_times_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.REDIRECT_TIMES_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setRedirect_times_current(targetUrlChangeIndClickHouseEntity.getRedirectTimesCurrent());
					targetUrlChangeIndicatorDetail.setRedirect_times_previous(targetUrlChangeIndClickHouseEntity.getRedirectTimesPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// response_code_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.RESPONSE_CODE_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setResponse_code_current(targetUrlChangeIndClickHouseEntity.getResponseCodeCurrent());
					targetUrlChangeIndicatorDetail.setResponse_code_previous(targetUrlChangeIndClickHouseEntity.getResponseCodePrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// 404_detected_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.RESPONSE_CODE_404_DETECTED_IND)) {
					targetUrlChangeIndicatorDetail.setResponse_code_current(targetUrlChangeIndClickHouseEntity.getResponseCodeCurrent());
					targetUrlChangeIndicatorDetail.setResponse_code_previous(targetUrlChangeIndClickHouseEntity.getResponseCodePrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// 404_removed_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.RESPONSE_CODE_404_REMOVED_IND)) {
					targetUrlChangeIndicatorDetail.setResponse_code_current(targetUrlChangeIndClickHouseEntity.getResponseCodeCurrent());
					targetUrlChangeIndicatorDetail.setResponse_code_previous(targetUrlChangeIndClickHouseEntity.getResponseCodePrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// redirect_301_detected_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.REDIRECT_301_DETECTED_IND)) {
					targetUrlChangeIndicatorDetail.setResponse_code_current(targetUrlChangeIndClickHouseEntity.getResponseCodeCurrent());
					targetUrlChangeIndicatorDetail.setResponse_code_previous(targetUrlChangeIndClickHouseEntity.getResponseCodePrevious());
					if (StringUtils.isNotBlank(targetUrlChangeIndClickHouseEntity.getRedirectFinalUrlCurrent())) {
						targetUrlChangeIndicatorDetail.setRedirect_final_url_current(targetUrlChangeIndClickHouseEntity.getRedirectFinalUrlCurrent());
					}
					if (StringUtils.isNotBlank(targetUrlChangeIndClickHouseEntity.getRedirectFinalUrlPrevious())) {
						targetUrlChangeIndicatorDetail.setRedirect_final_url_previous(targetUrlChangeIndClickHouseEntity.getRedirectFinalUrlPrevious());
					}
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// redirect_301_removed_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.REDIRECT_301_REMOVED_IND)) {
					targetUrlChangeIndicatorDetail.setResponse_code_current(targetUrlChangeIndClickHouseEntity.getResponseCodeCurrent());
					targetUrlChangeIndicatorDetail.setResponse_code_previous(targetUrlChangeIndClickHouseEntity.getResponseCodePrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// redirect_302_detected_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.REDIRECT_302_DETECTED_IND)) {
					targetUrlChangeIndicatorDetail.setResponse_code_current(targetUrlChangeIndClickHouseEntity.getResponseCodeCurrent());
					targetUrlChangeIndicatorDetail.setResponse_code_previous(targetUrlChangeIndClickHouseEntity.getResponseCodePrevious());
					if (StringUtils.isNotBlank(targetUrlChangeIndClickHouseEntity.getRedirectFinalUrlCurrent())) {
						targetUrlChangeIndicatorDetail.setRedirect_final_url_current(targetUrlChangeIndClickHouseEntity.getRedirectFinalUrlCurrent());
					}
					if (StringUtils.isNotBlank(targetUrlChangeIndClickHouseEntity.getRedirectFinalUrlPrevious())) {
						targetUrlChangeIndicatorDetail.setRedirect_final_url_previous(targetUrlChangeIndClickHouseEntity.getRedirectFinalUrlPrevious());
					}
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// redirect_302_removed_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.REDIRECT_302_REMOVED_IND)) {
					targetUrlChangeIndicatorDetail.setResponse_code_current(targetUrlChangeIndClickHouseEntity.getResponseCodeCurrent());
					targetUrlChangeIndicatorDetail.setResponse_code_previous(targetUrlChangeIndClickHouseEntity.getResponseCodePrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// redirect_diff_code_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.REDIRECT_DIFF_CODE_IND)) {
					targetUrlChangeIndicatorDetail.setResponse_code_current(targetUrlChangeIndClickHouseEntity.getResponseCodeCurrent());
					targetUrlChangeIndicatorDetail.setResponse_code_previous(targetUrlChangeIndClickHouseEntity.getResponseCodePrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// response_headers_added_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.RESPONSE_HEADERS_ADDED_IND)) {
					targetUrlChangeIndicatorDetail.setResponse_headers_current(targetUrlChangeIndClickHouseEntity.getResponseHeadersCurrent());
					targetUrlChangeIndicatorDetail.setResponse_headers_previous(targetUrlChangeIndClickHouseEntity.getResponseHeadersPrevious());
					if (targetUrlChangeIndicatorDetail.getResponse_headers_current() != null
							&& targetUrlChangeIndicatorDetail.getResponse_headers_current().length > 0) {
						for (int i = 0; i < targetUrlChangeIndicatorDetail.getResponse_headers_current().length; i++) {
							targetUrlChangeIndicatorDetail.getResponse_headers_current()[i].setIndex(i + 1);
						}
					}
					if (targetUrlChangeIndicatorDetail.getResponse_headers_previous() != null
							&& targetUrlChangeIndicatorDetail.getResponse_headers_previous().length > 0) {
						for (int i = 0; i < targetUrlChangeIndicatorDetail.getResponse_headers_previous().length; i++) {
							targetUrlChangeIndicatorDetail.getResponse_headers_previous()[i].setIndex(i + 1);
						}
					}
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// response_headers_removed_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.RESPONSE_HEADERS_REMOVED_IND)) {
					targetUrlChangeIndicatorDetail.setResponse_headers_current(targetUrlChangeIndClickHouseEntity.getResponseHeadersCurrent());
					targetUrlChangeIndicatorDetail.setResponse_headers_previous(targetUrlChangeIndClickHouseEntity.getResponseHeadersPrevious());
					if (targetUrlChangeIndicatorDetail.getResponse_headers_current() != null
							&& targetUrlChangeIndicatorDetail.getResponse_headers_current().length > 0) {
						for (int i = 0; i < targetUrlChangeIndicatorDetail.getResponse_headers_current().length; i++) {
							targetUrlChangeIndicatorDetail.getResponse_headers_current()[i].setIndex(i + 1);
						}
					}
					if (targetUrlChangeIndicatorDetail.getResponse_headers_previous() != null
							&& targetUrlChangeIndicatorDetail.getResponse_headers_previous().length > 0) {
						for (int i = 0; i < targetUrlChangeIndicatorDetail.getResponse_headers_previous().length; i++) {
							targetUrlChangeIndicatorDetail.getResponse_headers_previous()[i].setIndex(i + 1);
						}
					}
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// robots_added_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.ROBOTS_ADDED_IND)) {
					targetUrlChangeIndicatorDetail.setRobots_contents_current(targetUrlChangeIndClickHouseEntity.getRobotsContentsCurrent());
					targetUrlChangeIndicatorDetail.setRobots_contents_previous(targetUrlChangeIndClickHouseEntity.getRobotsContentsPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// robots_contents_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.ROBOTS_CONTENTS_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setRobots_contents_current(targetUrlChangeIndClickHouseEntity.getRobotsContentsCurrent());
					targetUrlChangeIndicatorDetail.setRobots_contents_previous(targetUrlChangeIndClickHouseEntity.getRobotsContentsPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// robots_removed_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.ROBOTS_REMOVED_IND)) {
					targetUrlChangeIndicatorDetail.setRobots_contents_current(targetUrlChangeIndClickHouseEntity.getRobotsContentsCurrent());
					targetUrlChangeIndicatorDetail.setRobots_contents_previous(targetUrlChangeIndClickHouseEntity.getRobotsContentsPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// structured_data_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.STRUCTURED_DATA_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setStructured_data_current(targetUrlChangeIndClickHouseEntity.getStructuredDataCurrent());
					targetUrlChangeIndicatorDetail.setStructured_data_previous(targetUrlChangeIndClickHouseEntity.getStructuredDataPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// title_added_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.TITLE_ADDED_IND)) {
					targetUrlChangeIndicatorDetail.setTitle_current(targetUrlChangeIndClickHouseEntity.getTitleCurrent());
					targetUrlChangeIndicatorDetail.setTitle_previous(targetUrlChangeIndClickHouseEntity.getTitlePrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// title_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.TITLE_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setTitle_current(targetUrlChangeIndClickHouseEntity.getTitleCurrent());
					targetUrlChangeIndicatorDetail.setTitle_previous(targetUrlChangeIndClickHouseEntity.getTitlePrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// title_removed_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.TITLE_REMOVED_IND)) {
					targetUrlChangeIndicatorDetail.setTitle_current(targetUrlChangeIndClickHouseEntity.getTitleCurrent());
					targetUrlChangeIndicatorDetail.setTitle_previous(targetUrlChangeIndClickHouseEntity.getTitlePrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// title_length_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.TITLE_LENGTH_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setTitle_length_current(targetUrlChangeIndClickHouseEntity.getTitleLengthCurrent());
					targetUrlChangeIndicatorDetail.setTitle_length_previous(targetUrlChangeIndClickHouseEntity.getTitleLengthPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// viewport_added_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.VIEWPORT_ADDED_IND)) {
					targetUrlChangeIndicatorDetail.setViewport_content_current(targetUrlChangeIndClickHouseEntity.getViewportContentCurrent());
					targetUrlChangeIndicatorDetail.setViewport_content_previous(targetUrlChangeIndClickHouseEntity.getViewportContentPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// viewport_content_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.VIEWPORT_CONTENT_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setViewport_content_current(targetUrlChangeIndClickHouseEntity.getViewportContentCurrent());
					targetUrlChangeIndicatorDetail.setViewport_content_previous(targetUrlChangeIndClickHouseEntity.getViewportContentPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// viewport_removed_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.VIEWPORT_REMOVED_IND)) {
					targetUrlChangeIndicatorDetail.setViewport_content_current(targetUrlChangeIndClickHouseEntity.getViewportContentCurrent());
					targetUrlChangeIndicatorDetail.setViewport_content_previous(targetUrlChangeIndClickHouseEntity.getViewportContentPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
				// robots_txt_chg_ind
				else if (StringUtils.equalsIgnoreCase(targetUrlChangeIndClickHouseEntity.getChangeIndicator(), IConstants.ROBOTS_TXT_CHG_IND)) {
					targetUrlChangeIndicatorDetail.setRobots_txt_current(targetUrlChangeIndClickHouseEntity.getRobotTxtCurrent());
					targetUrlChangeIndicatorDetail.setRobots_txt_previous(targetUrlChangeIndClickHouseEntity.getRobotTxtPrevious());
					targetUrlChangeIndicatorDetailList.add(targetUrlChangeIndicatorDetail);
				}
			}
		} else {
			//if (isDebug == true) {
			//	FormatUtils.getInstance().logMemoryUsage("convertTargetUrlChangeIndClickHouseEntityList() targetUrlChangeIndClickHouseEntityList is null.");
			//}
		}
		//if (isDebug == true) {
		//	FormatUtils.getInstance().logMemoryUsage(
		//			"convertTargetUrlChangeIndClickHouseEntityList() end. targetUrlChangeIndicatorDetailList.size()=" + targetUrlChangeIndicatorDetailList.size());
		//}
		return targetUrlChangeIndicatorDetailList;
	}

	private String getChangeTypeString(int changeType) {
		String changeTypeString = null;
		if (changeType == IConstants.CHANGE_TYPE_NUMBER_ADDED) {
			changeTypeString = IConstants.CHANGE_TYPE_ADDED;
		} else if (changeType == IConstants.CHANGE_TYPE_NUMBER_MODIFIED) {
			changeTypeString = IConstants.CHANGE_TYPE_MODIFIED;
		} else if (changeType == IConstants.CHANGE_TYPE_NUMBER_REMOVED) {
			changeTypeString = IConstants.CHANGE_TYPE_REMOVED;
		}
		return changeTypeString;
	}

	private String getSeverityString(int criticalInd) {
		String severityString = null;
		if (criticalInd == IConstants.CONTENT_GUARD_CHANGE_SEVERITY_CRITICAL) {
			severityString = IConstants.SEVERITY_CRITICAL;
		} else if (criticalInd == IConstants.CONTENT_GUARD_CHANGE_SEVERITY_HIGH) {
			severityString = IConstants.SEVERITY_HIGH;
		} else if (criticalInd == IConstants.CONTENT_GUARD_CHANGE_SEVERITY_MEDIUM) {
			severityString = IConstants.SEVERITY_MEDIUM;
		} else if (criticalInd == IConstants.CONTENT_GUARD_CHANGE_SEVERITY_LOW) {
			severityString = IConstants.SEVERITY_LOW;
		}
		return severityString;
	}

	// https://www.wrike.com/open.htm?id=1595718862
	public String getTargetUrlChangeIndicatorDetailsDownLoadSql(TargetUrlChangeRequest targetUrlChangeRequest) {
		targetUrlChangeRequest.setChangeIndicatorIdMap(this.changeIndicatorIdMap);
		String filterCondition = targetUrlChangeRequest.getFilterCondition();
		final String startTimeStamp = targetUrlChangeRequest.getStart_crawl_timestamp();
		final String startTrackDateString = StringUtils.substringBefore(startTimeStamp, IConstants.ONE_SPACE);
		final String endTimeStamp = targetUrlChangeRequest.getEnd_crawl_timestamp();
		final String endTrackDateString = StringUtils.substringBefore(endTimeStamp, IConstants.ONE_SPACE);
		final StringBuilder stringBuilder = new StringBuilder().append("select c.url,m.description,c.prev_response_code,c.curr_response_code,c.track_date from ")
				.append(" (select distinct url,chg_id,prev_response_code,curr_response_code,track_date from dis_html_change")
				.append(" where url_type = 1 and domain_id = " + targetUrlChangeRequest.getDomain_id())
				.append(" and track_date >= '").append(startTrackDateString).append("' and track_date <= '").append(endTrackDateString).append("' ")
				.append(" and curr_crawl_timestamp >= '").append(startTimeStamp).append("' and curr_crawl_timestamp <= '").append(endTimeStamp).append("' ");
		if (StringUtils.isNotBlank(filterCondition)) {
			stringBuilder.append(filterCondition);
		}
		stringBuilder.append(") c join dis_change_ind_master m on c.chg_id = m.chg_id ");
		return stringBuilder.toString();
	}

}
