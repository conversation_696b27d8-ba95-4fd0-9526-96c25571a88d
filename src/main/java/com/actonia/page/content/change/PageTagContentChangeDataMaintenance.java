package com.actonia.page.content.change;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.dao.PageContentChangeDAO;
import com.actonia.dao.PageTagContentChangeDAO;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.SpringBeanFactory;

/***
 * 
 * clear out the records in the 'page_content_change' table in the 'actonia' MySQL database when records are older than 13 months.
 * clear out the records in the 'page_tag_content_change' table in the 'actonia' MySQL database when records are older than 13 months.
 */
public class PageTagContentChangeDataMaintenance {

	PageContentChangeDAO pageContentChangeDAO;

	PageTagContentChangeDAO pageTagContentChangeDAO;

	int todayDateNumber = 0;

	public PageTagContentChangeDataMaintenance() {
		pageContentChangeDAO = SpringBeanFactory.getBean("pageContentChangeDAO");
		pageTagContentChangeDAO = SpringBeanFactory.getBean("pageTagContentChangeDAO");
	}

	public static void main(String args[]) throws Exception {
		System.out.println("main() begins.");
		long startTimestamp = System.currentTimeMillis();
		PageTagContentChangeDataMaintenance pageTagContentChangeDataMaintenance = new PageTagContentChangeDataMaintenance();
		pageTagContentChangeDataMaintenance.process();
		System.out.println("main() ends. elapsed time in sec.:" + (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private void process() throws Exception {
		Date todayDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
		Date cutoffDate = DateUtils.addMonths(todayDate, -13); // cutoff date is 13 months ago
		int cutoffDateNumber = Integer.parseInt(DateFormatUtils.format(cutoffDate, IConstants.DATE_FORMAT_YYYYMMDD));
		FormatUtils.getInstance().logMemoryUsage("process() cutoffDateNumber=" + cutoffDateNumber);
		List<Integer> domainIdList = null;
		int totalRecordsCleared = 0;

		domainIdList = pageContentChangeDAO.getDistinctDomainId();
		if (domainIdList != null && domainIdList.size() > 0) {
			for (Integer domainId : domainIdList) {
				totalRecordsCleared = pageContentChangeDAO.clear(domainId, cutoffDateNumber);
				FormatUtils.getInstance().logMemoryUsage("process() 'page_content_change' table cleared. domainId=" + domainId + ",cutoffDateNumber=" + cutoffDateNumber
						+ ",totalRecordsCleared=" + totalRecordsCleared);
			}
		}

		domainIdList = pageTagContentChangeDAO.getDistinctDomainId();
		if (domainIdList != null && domainIdList.size() > 0) {
			for (Integer domainId : domainIdList) {
				totalRecordsCleared = pageTagContentChangeDAO.clear(domainId, cutoffDateNumber);
				FormatUtils.getInstance().logMemoryUsage("process() 'page_tag_content_change' table cleared. domainId=" + domainId + ",cutoffDateNumber="
						+ cutoffDateNumber + ",totalRecordsCleared=" + totalRecordsCleared);
			}
		}
	}
}
