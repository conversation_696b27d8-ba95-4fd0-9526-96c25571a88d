package com.actonia.page.content.change;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Properties;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import com.actonia.IConstants;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.concurrency.ThreadPoolService;
import com.actonia.dao.OwnDomainEntityDAO;
import com.actonia.dao.PageTagAlertConfigDAO;
import com.actonia.dao.TargetUrlCrawlAdditionalContentEntityDAO;
import com.actonia.entity.AdditionalContentEntity;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.entity.PageTagAlertConfigEntity;
import com.actonia.utils.CommonUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.PutMessageUtils;
import com.actonia.utils.SpringBeanFactory;

/***
 * 
 * update 'page_content_change' MySQL table  
 *
 */
public class PageContentChangeUpdate {

	public static final Log logger = LogFactory.getLog(PageContentChangeUpdate.class);

	private static ThreadPoolService threadPool = ThreadPoolService.getInstance();

	private OwnDomainEntityDAO ownDomainEntityDAO;

	private int processDateNumber;

	private PageTagAlertConfigDAO pageTagAlertConfigDAO;

	private TargetUrlCrawlAdditionalContentEntityDAO targetUrlCrawlAdditionalContentEntityDAO;

	public PageContentChangeUpdate() {
		this.ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		this.pageTagAlertConfigDAO = SpringBeanFactory.getBean("pageTagAlertConfigDAO");
		this.targetUrlCrawlAdditionalContentEntityDAO = SpringBeanFactory.getBean("targetUrlCrawlAdditionalContentEntityDAO");
	}

	public static void main(String args[]) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("main() begins.");
		long startTimestamp = System.currentTimeMillis();
		try {
			threadPool.init();
			CommonUtils.initThreads(1);
			new PageContentChangeUpdate().process(args);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			threadPool.destroy();
			FormatUtils.getInstance().logMemoryUsage("main() ends. elapsed time in sec.:" + (System.currentTimeMillis() - startTimestamp) / 1000);
		}
	}

	private void process(String args[]) throws Exception {

		// by default, process date is yesterday's date
		Date todayDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
		FormatUtils.getInstance().logMemoryUsage("process() today's date=" + DateFormatUtils.format(todayDate, IConstants.DATE_FORMAT_YYYY_MM_DD));

		Date yesterdayDate = DateUtils.addDays(todayDate, -1);
		FormatUtils.getInstance().logMemoryUsage("process() yesterday's date=" + DateFormatUtils.format(yesterdayDate, IConstants.DATE_FORMAT_YYYY_MM_DD));
		processDateNumber = NumberUtils.toInt(DateFormatUtils.format(yesterdayDate, IConstants.DATE_FORMAT_YYYYMMDD));

		// runtime parameter 1 (optional): process date override (by default, process date is yesterday's date) (YYYY-MM-DD)
		if (args != null && args.length >= 1) {
			String processDateOverrideString = args[0];
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameter 1: process date override=" + processDateOverrideString);
			if (StringUtils.isNotBlank(processDateOverrideString)) {
				try {
					Date processDateOverride = DateUtils.parseDate(processDateOverrideString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
					processDateNumber = NumberUtils.toInt(DateFormatUtils.format(processDateOverride, IConstants.DATE_FORMAT_YYYYMMDD));
				} catch (ParseException e) {
					FormatUtils.getInstance().logMemoryUsage("process() processDateOverride invalid, exception message=" + e.getMessage());
					System.exit(-1);
				}
			}
		}

		FormatUtils.getInstance().logMemoryUsage("process() processDateNumber=" + processDateNumber);

		Boolean isExecDomainIdsInd = null;
		Set<Integer> runtimeDomainSet = null;

		Properties domainProperties = new Properties();
		try {
			domainProperties.load(PageContentChangeUpdate.class.getResourceAsStream("/domain.properties"));
		} catch (Exception e) {
			FormatUtils.getInstance().logMemoryUsage("process() no domain.properties file found");
			System.exit(-1);
		}

		String execDomainIds = domainProperties.getProperty("exec.domain");
		String notExecDomainIds = domainProperties.getProperty("notexec.domain");
		FormatUtils.getInstance().logMemoryUsage("process() execDomainIds=" + execDomainIds);
		FormatUtils.getInstance().logMemoryUsage("process() notExecDomainIds=" + notExecDomainIds);

		List<OwnDomainEntity> allOwnDomainEntityList = new ArrayList<OwnDomainEntity>();

		allOwnDomainEntityList = ownDomainEntityDAO.queryForAll();

		// process specific domains
		if (StringUtils.isNotBlank(execDomainIds)) {
			runtimeDomainSet = CommonUtils.getDomainSet(execDomainIds);
			isExecDomainIdsInd = true;
		}
		// do not process specific domains
		else if (StringUtils.isNotBlank(notExecDomainIds)) {
			runtimeDomainSet = CommonUtils.getDomainSet(notExecDomainIds);
			isExecDomainIdsInd = false;
		}
		// process all domains
		else {
			runtimeDomainSet = null;
			isExecDomainIdsInd = null;
		}

		List<OwnDomainEntity> filteredOwnDomainEntityList = PutMessageUtils.getInstance().filterDomains(allOwnDomainEntityList, isExecDomainIdsInd, runtimeDomainSet);
		if (filteredOwnDomainEntityList != null && filteredOwnDomainEntityList.size() > 0) {
			processDomainsConcurrently(filteredOwnDomainEntityList);
		} else {
			FormatUtils.getInstance().logMemoryUsage("process() filteredOwnDomainEntityList is empty.");
		}
	}

	private void processDomainsConcurrently(List<OwnDomainEntity> allOwnDomainEntityList) {
		int totalNumberOfDomains = allOwnDomainEntityList.size();
		long startTimestamp = System.currentTimeMillis();
		String ipAddress = null;
		OwnDomainEntity ownDomainEntity = null;
		PageContentChangeUpdateCommand pageContentChangeUpdateCommand = null;
		int numberOfDomainsProcessed = 0;
		List<PageTagAlertConfigEntity> pageTagAlertConfigEntityList = null;
		do {
			ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
			if (ipAddress == null) {
				continue;
			}
			ownDomainEntity = allOwnDomainEntityList.get(numberOfDomainsProcessed++);

			// when the client domain has configured alerts, then process the change update
			pageTagAlertConfigEntityList = pageTagAlertConfigDAO.get(ownDomainEntity.getId());
			if (pageTagAlertConfigEntityList != null && pageTagAlertConfigEntityList.size() > 0) {
				pageContentChangeUpdateCommand = getPageContentChangeUpdateCommand(ipAddress, ownDomainEntity);
				if (pageContentChangeUpdateCommand != null) {
					try {
						FormatUtils.getInstance().logMemoryUsage("processDomainsConcurrently() ipAddress acquired=" + ipAddress + ",domain=" + ownDomainEntity.getId()
								+ " - " + ownDomainEntity.getDomain());
						threadPool.execute(pageContentChangeUpdateCommand);
					} catch (Exception e) {
						e.printStackTrace();
					}
				} else {
					CacheModleFactory.getInstance().setAliveIpAddress(ipAddress);
				}
			} else {
				CacheModleFactory.getInstance().setAliveIpAddress(ipAddress);
			}
		} while (numberOfDomainsProcessed < totalNumberOfDomains);

		do {
			try {
				Thread.sleep(1000);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		} while (threadPool.getThreadPool().getActiveCount() > 0);
		FormatUtils.getInstance().logMemoryUsage("processDomainsConcurrently() end. elapsed time in seconds=" + ((System.currentTimeMillis() - startTimestamp) / 1000));
	}

	private PageContentChangeUpdateCommand getPageContentChangeUpdateCommand(String ip, OwnDomainEntity ownDomainEntity) {
		int domainId = ownDomainEntity.getId();
		List<AdditionalContentEntity> additionalContentEntityList = targetUrlCrawlAdditionalContentEntityDAO.getByDomainId(domainId);
		PageContentChangeUpdateCommand pageContentChangeUpdateCommand = new PageContentChangeUpdateCommand(ip, ownDomainEntity, processDateNumber,
				additionalContentEntityList);
		pageContentChangeUpdateCommand.setStatus(true);
		return pageContentChangeUpdateCommand;
	}
}
