package com.actonia.page.content.change;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Properties;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import com.actonia.IConstants;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.concurrency.ThreadPoolService;
import com.actonia.dao.OwnDomainEntityDAO;
import com.actonia.dao.PageTagAlertConfigAdditionalContentDAO;
import com.actonia.dao.PageTagAlertConfigDAO;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.entity.PageTagAlertConfigAdditionalContentEntity;
import com.actonia.entity.PageTagAlertConfigEntity;
import com.actonia.utils.CommonUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.PutMessageUtils;
import com.actonia.utils.SpringBeanFactory;

/***
 * 
 * update 'page_tag_content_change' MySQL table  
 *
 */
public class PageTagContentChangeUpdate {

	public static final Log logger = LogFactory.getLog(PageTagContentChangeUpdate.class);

	private static ThreadPoolService threadPool = ThreadPoolService.getInstance();

	private OwnDomainEntityDAO ownDomainEntityDAO;

	private PageTagAlertConfigDAO pageTagAlertConfigDAO;

	private PageTagAlertConfigAdditionalContentDAO pageTagAlertConfigAdditionalContentDAO;

	private int processDateNumber;

	public PageTagContentChangeUpdate() {
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		pageTagAlertConfigDAO = SpringBeanFactory.getBean("pageTagAlertConfigDAO");
		pageTagAlertConfigAdditionalContentDAO = SpringBeanFactory.getBean("pageTagAlertConfigAdditionalContentDAO");
	}

	public static void main(String args[]) {

		FormatUtils.getInstance().logMemoryUsage("main() begins.");
		long startTimestamp = System.currentTimeMillis();

		try {
			threadPool.init();
			CommonUtils.initThreads(8);
			new PageTagContentChangeUpdate().process(args);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			threadPool.destroy();
			FormatUtils.getInstance().logMemoryUsage("main() ends. elapsed time in sec.:" + (System.currentTimeMillis() - startTimestamp) / 1000);
		}
	}

	private void process(String args[]) throws Exception {

		// by default, the process date is yesterday's date
		Date todayDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
		FormatUtils.getInstance().logMemoryUsage("process() today's date=" + DateFormatUtils.format(todayDate, IConstants.DATE_FORMAT_YYYY_MM_DD));

		Date yesterdayDate = DateUtils.addDays(todayDate, -1);
		FormatUtils.getInstance().logMemoryUsage("process() yesterday's date=" + DateFormatUtils.format(yesterdayDate, IConstants.DATE_FORMAT_YYYY_MM_DD));
		processDateNumber = NumberUtils.toInt(DateFormatUtils.format(yesterdayDate, IConstants.DATE_FORMAT_YYYYMMDD));

		// runtime parameter 1 (optional): process date override (by default, process date is yesterday's date) (YYYY-MM-DD)
		if (args != null && args.length >= 1) {
			String processDateOverrideString = args[0];
			FormatUtils.getInstance().logMemoryUsage("process() runtime parameter 1: process date override=" + processDateOverrideString);
			if (StringUtils.isNotBlank(processDateOverrideString)) {
				try {
					Date processDateOverride = DateUtils.parseDate(processDateOverrideString, new String[] { IConstants.DATE_FORMAT_YYYY_MM_DD });
					processDateNumber = NumberUtils.toInt(DateFormatUtils.format(processDateOverride, IConstants.DATE_FORMAT_YYYYMMDD));
				} catch (ParseException e) {
					FormatUtils.getInstance().logMemoryUsage("process() processDateOverride invalid, exception message=" + e.getMessage());
					System.exit(-1);
				}
			}
		}

		FormatUtils.getInstance().logMemoryUsage("process() processDateNumber=" + processDateNumber);

		Boolean isExecDomainIdsInd = null;
		Set<Integer> runtimeDomainSet = null;

		Properties domainProperties = new Properties();
		try {
			domainProperties.load(PageTagContentChangeUpdate.class.getResourceAsStream("/domain.properties"));
		} catch (Exception e) {
			FormatUtils.getInstance().logMemoryUsage("process() no domain.properties file found");
			System.exit(-1);
		}

		String execDomainIds = domainProperties.getProperty("exec.domain");
		String notExecDomainIds = domainProperties.getProperty("notexec.domain");
		FormatUtils.getInstance().logMemoryUsage("process() execDomainIds=" + execDomainIds);
		FormatUtils.getInstance().logMemoryUsage("process() notExecDomainIds=" + notExecDomainIds);

		List<OwnDomainEntity> allOwnDomainEntityList = new ArrayList<OwnDomainEntity>();

		allOwnDomainEntityList = ownDomainEntityDAO.queryForAll();

		// process specific domains
		if (StringUtils.isNotBlank(execDomainIds)) {
			runtimeDomainSet = CommonUtils.getDomainSet(execDomainIds);
			isExecDomainIdsInd = true;
		}
		// do not process specific domains
		else if (StringUtils.isNotBlank(notExecDomainIds)) {
			runtimeDomainSet = CommonUtils.getDomainSet(notExecDomainIds);
			isExecDomainIdsInd = false;
		}
		// process all domains
		else {
			runtimeDomainSet = null;
			isExecDomainIdsInd = null;
		}

		List<OwnDomainEntity> filteredOwnDomainEntityList = PutMessageUtils.getInstance().filterDomains(allOwnDomainEntityList, isExecDomainIdsInd, runtimeDomainSet);
		if (filteredOwnDomainEntityList != null && filteredOwnDomainEntityList.size() > 0) {
			FormatUtils.getInstance().logMemoryUsage("process() filteredOwnDomainEntityList.size()=" + filteredOwnDomainEntityList.size());
			processDomainsConcurrently(filteredOwnDomainEntityList);
		} else {
			FormatUtils.getInstance().logMemoryUsage("process() filteredOwnDomainEntityList is empty.");
		}
	}

	private void processDomainsConcurrently(List<OwnDomainEntity> allOwnDomainEntityList) {
		FormatUtils.getInstance().logMemoryUsage("processDomainsConcurrently() begins.");
		int totalNumberOfDomains = allOwnDomainEntityList.size();
		long startTimestamp = System.currentTimeMillis();
		String ipAddress = null;
		OwnDomainEntity ownDomainEntity = null;
		PageTagContentChangeUpdateCommand pageTagContentChangeUpdateCommand = null;
		int numberOfDomainsProcessed = 0;
		do {
			ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
			if (ipAddress == null) {
				continue;
			}
			ownDomainEntity = allOwnDomainEntityList.get(numberOfDomainsProcessed++);
			//FormatUtils.getInstance()
			//		.logMemoryUsage("processDomainsConcurrently() processing domainId=" + ownDomainEntity.getId() + ",domainName=" + ownDomainEntity.getDomain());
			pageTagContentChangeUpdateCommand = getPageTagContentChangeUpdateCommand(ipAddress, ownDomainEntity);
			if (pageTagContentChangeUpdateCommand != null) {
				try {
					FormatUtils.getInstance().logMemoryUsage("processDomainsConcurrently() ipAddress acquired=" + ipAddress + ",domain=" + ownDomainEntity.getId()
							+ " - " + ownDomainEntity.getDomain());
					threadPool.execute(pageTagContentChangeUpdateCommand);
				} catch (Exception e) {
					e.printStackTrace();
				}
			} else {
				CacheModleFactory.getInstance().setAliveIpAddress(ipAddress);
			}
		} while (numberOfDomainsProcessed < totalNumberOfDomains);

		do {
			try {
				Thread.sleep(1000);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		} while (threadPool.getThreadPool().getActiveCount() > 0);
		FormatUtils.getInstance().logMemoryUsage("processDomainsConcurrently() end. elapsed time in seconds=" + ((System.currentTimeMillis() - startTimestamp) / 1000));
	}

	private PageTagContentChangeUpdateCommand getPageTagContentChangeUpdateCommand(String ip, OwnDomainEntity ownDomainEntity) {
		PageTagContentChangeUpdateCommand pageTagContentChangeUpdateCommand = null;

		int pageTagAlertConfigId = 0;

		List<PageTagAlertConfigAdditionalContentEntity> pageTagAlertConfigAdditionalContentEntityList = null;
		List<PageTagAlertConfigAdditionalContentEntity> tempPageTagAlertConfigAdditionalContentEntityList = null;

		// retrieve page tag alert configuration for client domain
		List<PageTagAlertConfigEntity> pageTagAlertConfigEntityList = pageTagAlertConfigDAO.get(ownDomainEntity.getId());
		if (pageTagAlertConfigEntityList != null && pageTagAlertConfigEntityList.size() > 0) {
			pageTagAlertConfigAdditionalContentEntityList = new ArrayList<PageTagAlertConfigAdditionalContentEntity>();
			for (PageTagAlertConfigEntity pageTagAlertConfigEntity : pageTagAlertConfigEntityList) {
				// retrieve page tag alert configuration for additional content for client domain
				pageTagAlertConfigId = pageTagAlertConfigEntity.getId();
				tempPageTagAlertConfigAdditionalContentEntityList = pageTagAlertConfigAdditionalContentDAO.getByPageTagAlertConfigId(pageTagAlertConfigId);
				if (tempPageTagAlertConfigAdditionalContentEntityList != null && tempPageTagAlertConfigAdditionalContentEntityList.size() > 0) {
					pageTagAlertConfigAdditionalContentEntityList.addAll(tempPageTagAlertConfigAdditionalContentEntityList);
				}
			}
			pageTagContentChangeUpdateCommand = new PageTagContentChangeUpdateCommand(ip, ownDomainEntity, processDateNumber, pageTagAlertConfigEntityList,
					pageTagAlertConfigAdditionalContentEntityList);
			pageTagContentChangeUpdateCommand.setStatus(true);
		} else {
			//FormatUtils.getInstance().logMemoryUsage("getPageTagContentChangeUpdateCommand() skip processing domainId=" + ownDomainEntity.getId() + ",domainName="
			//		+ ownDomainEntity.getDomain() + ",pageTagAlertConfigEntityList is empty.");
		}
		return pageTagContentChangeUpdateCommand;
	}
}
