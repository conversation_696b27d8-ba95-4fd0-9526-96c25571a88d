package com.actonia.page.content.change;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;

import com.actonia.IConstants;
import com.actonia.concurrency.BaseThreadCommand;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.dao.AlertMetricsNameDAO;
import com.actonia.dao.PageContentChangeDAO;
import com.actonia.dao.TargetUrlEntityDAO;
import com.actonia.dao.TargetUrlHtmlClickHouseDAO;
import com.actonia.entity.AlertMetricsEntity;
import com.actonia.entity.AlertMetricsNameEntity;
import com.actonia.entity.HtmlClickHouseEntity;
import com.actonia.entity.OwnDomainEntity;
import com.actonia.entity.PageContentChangeEntity;
import com.actonia.entity.AdditionalContentEntity;
import com.actonia.entity.TargetUrlEntity;
import com.actonia.utils.CrawlerUtils;
import com.actonia.utils.FormatUtils;
import com.actonia.utils.Md5Util;
import com.actonia.utils.SpringBeanFactory;
import com.actonia.value.object.CustomData;
import com.actonia.value.object.SelectorValueObject;
import com.google.gson.Gson;

public class PageContentChangeUpdateCommand extends BaseThreadCommand {

	//private boolean isDebug = false;
	private String ip;
	private AlertMetricsNameDAO alertMetricsNameDAO;
	private TargetUrlEntityDAO targetUrlEntityDAO;
	private PageContentChangeDAO pageContentChangeDAO;
	private int processDateNumber;
	private int domainId;
	private String domainName;
	private List<String> alertNameList;
	private List<PageContentChangeEntity> pageContentChangeEntityToBeCreatedList;

	// map key = alert name
	// map value = change type
	Map<String, Integer> alertNameChangeTypeMap = new HashMap<String, Integer>();

	private static final String STRING_ZERO = "0";
	private static final String ALERT_NAME_TITLE = "title";
	private static final String ALERT_NAME_META_DESC = "metaDescription";
	private static final String ALERT_NAME_H1 = "h1";
	private static final String ALERT_NAME_H1_CONTENT = "h1Content";
	private static final String ALERT_NAME_H2 = "h2";
	private static final String ALERT_NAME_H2_CONTENT = "h2Content";
	private static final String ALERT_NAME_META_ROBOTS = "meta_robots";
	private static final String ALERT_NAME_CANONICAL_URL = "canonical_url";
	private static final String ALERT_NAME_ADDITIONAL_CONTENT = "Additional Content";
	private static final String ALERT_NAME_DIV_CONTENT = "divContent";

	private static final String TO = " to ";
	private static final int MAX_RECORDS_PER_DATABASE_UPDATE = 100;

	// 1 = title
	// 2 = meta desc
	// 4 = number of h1
	// 5 = content of h1
	// 6 = number of h2
	// 7 = content of h2
	// 10 = response code
	// 11 = meta robots
	// 12 = canonical URL
	// 13 = additional content change (target_url_crawl_additional_content)
	public static final int CHANGE_TYPE_TITLE = 1;
	public static final int CHANGE_TYPE_META_DESC = 2;
	public static final int CHANGE_TYPE_NUMBER_OF_H1 = 4;
	public static final int CHANGE_TYPE_CONTENT_OF_H1 = 5;
	public static final int CHANGE_TYPE_NUMBER_OF_H2 = 6;
	public static final int CHANGE_TYPE_CONTENT_OF_H2 = 7;
	public static final int CHANGE_TYPE_RESP_CODE = 10;
	public static final int CHANGE_TYPE_META_ROBOTS = 11;
	public static final int CHANGE_TYPE_CANONICAL_URL = 12;
	public static final int CHANGE_TYPE_ADDITIONAL_CONTENT = 13;

	public static final int RESP_CODE_200 = 200;

	private boolean isOverrideProcessDate = false;

	private static final String TABLE_NAME = null;

	private boolean isNumberOfH1Changed = false;
	private boolean isNumberOfH2Changed = false;

	private List<AdditionalContentEntity> additionalContentEntityList;

	public PageContentChangeUpdateCommand(String ip, OwnDomainEntity ownDomainEntity, int processDateNumber,
			List<AdditionalContentEntity> additionalContentEntityList) {
		super();
		this.ip = ip;
		this.domainId = ownDomainEntity.getId();
		this.domainName = ownDomainEntity.getDomain();
		this.processDateNumber = processDateNumber;
		this.alertMetricsNameDAO = SpringBeanFactory.getBean("alertMetricsNameDAO");
		this.targetUrlEntityDAO = SpringBeanFactory.getBean("targetUrlEntityDAO");
		this.pageContentChangeDAO = SpringBeanFactory.getBean("pageContentChangeDAO");
		this.pageContentChangeEntityToBeCreatedList = new ArrayList<PageContentChangeEntity>();
		this.alertNameList = new ArrayList<String>();

		// check if overriding process date which is by default yesterday's date
		Date todayDate = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
		Date yesterdayDate = DateUtils.addDays(todayDate, -1);
		int yesterdayDateNumber = NumberUtils.toInt(DateFormatUtils.format(yesterdayDate, IConstants.DATE_FORMAT_YYYYMMDD));
		if (yesterdayDateNumber != processDateNumber) {
			this.isOverrideProcessDate = true;
		}

		this.additionalContentEntityList = additionalContentEntityList;
	}

	@Override
	protected void execute() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("execute() begins. ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",processDateNumber="
				+ processDateNumber + ",isOverrideProcessDate=" + isOverrideProcessDate);
		try {
			process();
			if (pageContentChangeEntityToBeCreatedList.size() > 0) {
				pageContentChangeDAO.insertMultiRowsBatch(pageContentChangeEntityToBeCreatedList);
				pageContentChangeEntityToBeCreatedList = new ArrayList<PageContentChangeEntity>();
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			CacheModleFactory.getInstance().setAliveIpAddress(ip);
			FormatUtils.getInstance().logMemoryUsage("execute() ends. ip=" + ip + ",domainId=" + domainId + " ,domainName=" + domainName + ",elapsed time in sec.="
					+ (System.currentTimeMillis() - startTimestamp) / 1000);

		}
	}

	private void process() throws Exception {

		// reset existing data (domain ID + process date) for re-runnability
		pageContentChangeDAO.reset(domainId, processDateNumber);
		if (additionalContentEntityList != null && additionalContentEntityList.size() > 0) {
			for (AdditionalContentEntity additionalContentEntity : additionalContentEntityList) {
				FormatUtils.getInstance().logMemoryUsage("process() ip=" + ip + ",domainId=" + domainId + " ,domainName=" + domainName
						+ ",additionalContentEntity=" + additionalContentEntity.toString());
			}
		} else {
			FormatUtils.getInstance().logMemoryUsage(
					"process() ip=" + ip + ",domainId=" + domainId + " ,domainName=" + domainName + ",additionalContentEntityList is empty.");
		}

		retrieveAlertNames(additionalContentEntityList);

		List<String> databaseFields = getDatabaseFields(additionalContentEntityList);
		List<TargetUrlEntity> targetUrlEntityList = null;

		// map key = MD5 hash code target URL string
		// map value = target URL ID
		Map<String, Long> targetUrlHashCodeIdMap = new HashMap<String, Long>();

		Long targetUrlId = null;

		String hashCode = null;

		// track date is yesterday's date
		Date trackDate = DateUtils.parseDate(String.valueOf(processDateNumber), new String[] { IConstants.DATE_FORMAT_YYYYMMDD });
		FormatUtils.getInstance().logMemoryUsage("process() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",trackDate=" + trackDate);

		// retrieve a list of unique 'target_url_html' records that had changes yesterday
		List<HtmlClickHouseEntity> htmlClickHouseEntityList = TargetUrlHtmlClickHouseDAO.getInstance().getChangeList(domainId, trackDate, databaseFields, TABLE_NAME);
		if (htmlClickHouseEntityList != null && htmlClickHouseEntityList.size() > 0) {
			FormatUtils.getInstance().logMemoryUsage("process() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",htmlClickHouseEntityList.size()="
					+ htmlClickHouseEntityList.size());
			FormatUtils.getInstance().logMemoryUsage("process() ip=" + ip + ",domainId=" + domainId + ",targetUrlHashCodeIdMap updating...");
			targetUrlEntityList = targetUrlEntityDAO.getListForIdUrl(domainId);
			if (targetUrlEntityList != null && targetUrlEntityList.size() > 0) {
				for (TargetUrlEntity targetUrlEntity : targetUrlEntityList) {
					hashCode = Md5Util.Md5(targetUrlEntity.getUrl());
					if (StringUtils.isNotBlank(hashCode)) {
						targetUrlHashCodeIdMap.put(hashCode, targetUrlEntity.getId());
					}
				}
				FormatUtils.getInstance().logMemoryUsage("process() ip=" + ip + ",domainId=" + domainId
						+ ",targetUrlHashCodeIdMap update completed,targetUrlHashCodeIdMap.size()=" + targetUrlHashCodeIdMap.size());
				for (HtmlClickHouseEntity htmlClickHouseEntity : htmlClickHouseEntityList) {
					hashCode = Md5Util.Md5(htmlClickHouseEntity.getUrl());
					if (targetUrlHashCodeIdMap.containsKey(hashCode) == true) {
						targetUrlId = targetUrlHashCodeIdMap.get(hashCode);
						processOneTargetUrl(htmlClickHouseEntity, trackDate, targetUrlId);
					} else {
						FormatUtils.getInstance().logMemoryUsage(
								"process() error--target URL ID cannot be determined. ip=" + ip + ",domainId=" + domainId + ",url=" + htmlClickHouseEntity.getUrl());
					}
				}
			} else {
				FormatUtils.getInstance()
						.logMemoryUsage("process() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",targetUrlEntityList is empty.");
			}
		} else {
			FormatUtils.getInstance()
					.logMemoryUsage("process() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",htmlClickHouseEntityList is empty.");
		}
	}

	private void retrieveAlertNames(List<AdditionalContentEntity> additionalContentEntityList) {
		// retrieve the list alert names for the client domain
		List<AlertMetricsNameEntity> alertMetricsNameEntityList = alertMetricsNameDAO.getAlertNames(domainId, AlertMetricsEntity.URL_TYPE_TARGET,
				AlertMetricsEntity.STATUS_ENABLED);
		if (alertMetricsNameEntityList != null && alertMetricsNameEntityList.size() > 0) {
			nextAlertMetricsNameEntity: for (AlertMetricsNameEntity alertMetricsNameEntity : alertMetricsNameEntityList) {
				if (StringUtils.equalsIgnoreCase(alertMetricsNameEntity.getMetricsName(), ALERT_NAME_DIV_CONTENT)
						|| StringUtils.equalsIgnoreCase(alertMetricsNameEntity.getMetricsName(), ALERT_NAME_ADDITIONAL_CONTENT)) {
					continue nextAlertMetricsNameEntity;
				}
				alertNameList.add(alertMetricsNameEntity.getMetricsName());
				alertNameChangeTypeMap.put(alertMetricsNameEntity.getMetricsName(), alertMetricsNameEntity.getId());
			}
		}

		// additional content
		if (additionalContentEntityList != null && additionalContentEntityList.size() > 0) {
			alertNameList.add(ALERT_NAME_ADDITIONAL_CONTENT);
			alertNameChangeTypeMap.put(ALERT_NAME_ADDITIONAL_CONTENT, CHANGE_TYPE_ADDITIONAL_CONTENT);
		}

		// meta robots
		alertNameList.add(ALERT_NAME_META_ROBOTS);
		alertNameChangeTypeMap.put(ALERT_NAME_META_ROBOTS, CHANGE_TYPE_META_ROBOTS);

		// canonical URL
		alertNameList.add(ALERT_NAME_CANONICAL_URL);
		alertNameChangeTypeMap.put(ALERT_NAME_CANONICAL_URL, CHANGE_TYPE_CANONICAL_URL);

		if (alertNameList != null && alertNameList.size() > 0) {
			for (String alertName : alertNameList) {
				FormatUtils.getInstance().logMemoryUsage("retrieveAlertNames() ip=" + ip + ",domainId=" + domainId + " ,domainName=" + domainName + ",alertName="
						+ alertName + ",changeType=" + alertNameChangeTypeMap.get(alertName));
			}
		} else {
			FormatUtils.getInstance()
					.logMemoryUsage("retrieveAlertNames() ip=" + ip + ",domainId=" + domainId + " ,domainName=" + domainName + ",alertNameList is empty.");
		}
	}

	private List<String> getDatabaseFields(List<AdditionalContentEntity> additionalContentEntityList) {
		List<String> clickHouseFieldNameList = new ArrayList<String>();
		clickHouseFieldNameList.add(IConstants.TRACK_DATE);
		clickHouseFieldNameList.add(IConstants.URL);
		clickHouseFieldNameList.add(IConstants.RESPONSE_CODE);
		clickHouseFieldNameList.add(IConstants.RESPONSE_CODE_CHG_IND);
		if (alertNameList.contains(ALERT_NAME_TITLE)) {
			clickHouseFieldNameList.add(IConstants.TITLE);
			clickHouseFieldNameList.add(IConstants.TITLE_CHG_IND);
		}
		if (alertNameList.contains(ALERT_NAME_META_DESC)) {
			clickHouseFieldNameList.add(IConstants.DESCRIPTION);
			clickHouseFieldNameList.add(IConstants.DESCRIPTION_CHG_IND);
		}
		if (alertNameList.contains(ALERT_NAME_H1) || alertNameList.contains(ALERT_NAME_H1_CONTENT)) {
			clickHouseFieldNameList.add(IConstants.H1);
			clickHouseFieldNameList.add(IConstants.H1_CHG_IND);
		}
		if (alertNameList.contains(ALERT_NAME_H2) || alertNameList.contains(ALERT_NAME_H2_CONTENT)) {
			clickHouseFieldNameList.add(IConstants.H2);
			clickHouseFieldNameList.add(IConstants.H2_CHG_IND);
		}
		if (alertNameList.contains(ALERT_NAME_META_ROBOTS)) {
			clickHouseFieldNameList.add(IConstants.ROBOTS_CONTENTS);
			clickHouseFieldNameList.add(IConstants.ROBOTS_CONTENTS_CHG_IND);
		}
		if (alertNameList.contains(ALERT_NAME_CANONICAL_URL)) {
			clickHouseFieldNameList.add(IConstants.CANONICAL);
			clickHouseFieldNameList.add(IConstants.CANONICAL_CHG_IND);
		}
		if (additionalContentEntityList != null && additionalContentEntityList.size() > 0) {
			clickHouseFieldNameList.add(IConstants.CUSTOM_DATA);
			clickHouseFieldNameList.add(IConstants.CUSTOM_DATA_CHG_IND);
		}
		for (String clickHouseFieldName : clickHouseFieldNameList) {
			FormatUtils.getInstance().logMemoryUsage(
					"getDatabaseFields() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",clickHouseFieldName=" + clickHouseFieldName);
		}
		return clickHouseFieldNameList;
	}

	private void processOneTargetUrl(HtmlClickHouseEntity htmlClickHouseEntityCurrent, Date trackDate, Long targetUrlId) throws Exception {
		//String[] h1Array = null;
		//String h1String = null;
		//String[] h2Array = null;
		//String h2String = null;
		Integer fromResponseCode = null;
		Integer toResponseCode = null;
		List<String> previousDatabaseFields = new ArrayList<String>();

		String targetUrlString = htmlClickHouseEntityCurrent.getUrl();
		//FormatUtils.getInstance().logMemoryUsage("processOneTargetUrl() ip=" + ip + ",domainId=" + domainId + ",trackDate=" + trackDate + ",targetUrlString="
		//		+ targetUrlString + ",targetUrlId=" + targetUrlId);

		// response code
		//if (BooleanUtils.isTrue(htmlClickHouseEntityCurrent.getResponseCodeChgInd())) {
		//FormatUtils.getInstance().logMemoryUsage("processOneTargetUrl() ip=" + ip + ",domainId=" + domainId + ",current responseCode="
		//		+ htmlClickHouseEntityCurrent.getCrawlerResponse().getResponse_code());
		//FormatUtils.getInstance().logMemoryUsage(
		//		"processOneTargetUrl() ip=" + ip + ",domainId=" + domainId + ",current responseCodeChgInd=" + htmlClickHouseEntityCurrent.getResponseCodeChgInd());			
		//}
		previousDatabaseFields.add(IConstants.RESPONSE_CODE);

		// title
		if (BooleanUtils.isTrue(htmlClickHouseEntityCurrent.getTitleChgInd())) {
			//FormatUtils.getInstance().logMemoryUsage(
			//		"processOneTargetUrl() ip=" + ip + ",domainId=" + domainId + ",current title=" + htmlClickHouseEntityCurrent.getCrawlerResponse().getTitle());
			//FormatUtils.getInstance().logMemoryUsage(
			//		"processOneTargetUrl() ip=" + ip + ",domainId=" + domainId + ",current titleChgInd=" + htmlClickHouseEntityCurrent.getTitleChgInd());
			previousDatabaseFields.add(IConstants.TITLE);
		}

		// description
		if (BooleanUtils.isTrue(htmlClickHouseEntityCurrent.getDescriptionChgInd())) {
			//FormatUtils.getInstance().logMemoryUsage("processOneTargetUrl() ip=" + ip + ",domainId=" + domainId + ",current description="
			//		+ htmlClickHouseEntityCurrent.getCrawlerResponse().getDescription());
			//FormatUtils.getInstance().logMemoryUsage(
			//		"processOneTargetUrl() ip=" + ip + ",domainId=" + domainId + ",current descriptionChgInd=" + htmlClickHouseEntityCurrent.getDescriptionChgInd());
			previousDatabaseFields.add(IConstants.DESCRIPTION);
		}

		// h1
		if (BooleanUtils.isTrue(htmlClickHouseEntityCurrent.getH1ChgInd())) {
			//h1Array = htmlClickHouseEntityCurrent.getCrawlerResponse().getH1();
			//if (h1Array != null && h1Array.length > 0) {
			//	h1String = Arrays.toString(h1Array);
			//} else {
			//	h1String = null;
			//}
			//FormatUtils.getInstance().logMemoryUsage("processOneTargetUrl() ip=" + ip + ",domainId=" + domainId + ",current h1=" + h1String);
			//FormatUtils.getInstance()
			//		.logMemoryUsage("processOneTargetUrl() ip=" + ip + ",domainId=" + domainId + ",current h1ChgInd=" + htmlClickHouseEntityCurrent.getH1ChgInd());
			previousDatabaseFields.add(IConstants.H1);
		}

		// h2
		if (BooleanUtils.isTrue(htmlClickHouseEntityCurrent.getH2ChgInd())) {
			//h2Array = htmlClickHouseEntityCurrent.getCrawlerResponse().getH2();
			//if (h2Array != null && h2Array.length > 0) {
			//	h2String = Arrays.toString(h2Array);
			//} else {
			//	h2String = null;
			//}
			//FormatUtils.getInstance().logMemoryUsage("processOneTargetUrl() ip=" + ip + ",domainId=" + domainId + ",current h2=" + h2String);
			//FormatUtils.getInstance()
			//		.logMemoryUsage("processOneTargetUrl() ip=" + ip + ",domainId=" + domainId + ",current h2ChgInd=" + htmlClickHouseEntityCurrent.getH2ChgInd());
			previousDatabaseFields.add(IConstants.H2);
		}

		// robots
		if (BooleanUtils.isTrue(htmlClickHouseEntityCurrent.getRobotsContentsChgInd())) {
			//FormatUtils.getInstance().logMemoryUsage("processOneTargetUrl() ip=" + ip + ",domainId=" + domainId + ",current robotsContents="
			//		+ htmlClickHouseEntityCurrent.getCrawlerResponse().getRobots_contents());
			//FormatUtils.getInstance().logMemoryUsage("processOneTargetUrl() ip=" + ip + ",domainId=" + domainId + ",current robotsContentsChgInd="
			//		+ htmlClickHouseEntityCurrent.getRobotsContentsChgInd());
			previousDatabaseFields.add(IConstants.ROBOTS_CONTENTS);
		}

		// canonical
		if (BooleanUtils.isTrue(htmlClickHouseEntityCurrent.getCanonicalChgInd())) {
			//FormatUtils.getInstance().logMemoryUsage("processOneTargetUrl() ip=" + ip + ",domainId=" + domainId + ",current canonical="
			//		+ htmlClickHouseEntityCurrent.getCrawlerResponse().getCanonical());
			//FormatUtils.getInstance().logMemoryUsage(
			//		"processOneTargetUrl() ip=" + ip + ",domainId=" + domainId + ",current canonicalChgInd=" + htmlClickHouseEntityCurrent.getCanonicalChgInd());
			previousDatabaseFields.add(IConstants.CANONICAL);
		}

		// custom data
		if (BooleanUtils.isTrue(htmlClickHouseEntityCurrent.getCustomDataChgInd())) {
			previousDatabaseFields.add(IConstants.CUSTOM_DATA);
		}

		// retrieve last unique crawl data before track date (ie. yesterday's date)
		HtmlClickHouseEntity htmlClickHouseEntityPrevious = TargetUrlHtmlClickHouseDAO.getInstance().getPrevious(ip, null, domainId, targetUrlString,
				previousDatabaseFields, TABLE_NAME, trackDate, null);
		if (htmlClickHouseEntityPrevious != null) {

			// response code
			//if (BooleanUtils.isTrue(htmlClickHouseEntityCurrent.getResponseCodeChgInd())) {
			//FormatUtils.getInstance().logMemoryUsage("processOneTargetUrl() ip=" + ip + ",domainId=" + domainId + ",previous responseCode="
			//		+ htmlClickHouseEntityPrevious.getCrawlerResponse().getResponse_code());
			//}

			// title
			//if (BooleanUtils.isTrue(htmlClickHouseEntityCurrent.getTitleChgInd())) {
			//FormatUtils.getInstance().logMemoryUsage(
			//		"processOneTargetUrl() ip=" + ip + ",domainId=" + domainId + ",previous title=" + htmlClickHouseEntityPrevious.getCrawlerResponse().getTitle());
			//}

			// description
			//if (BooleanUtils.isTrue(htmlClickHouseEntityCurrent.getDescriptionChgInd())) {
			//FormatUtils.getInstance().logMemoryUsage("processOneTargetUrl() ip=" + ip + ",domainId=" + domainId + ",previous description="
			//		+ htmlClickHouseEntityPrevious.getCrawlerResponse().getDescription());
			//}

			// h1
			//if (BooleanUtils.isTrue(htmlClickHouseEntityCurrent.getH1ChgInd())) {
			//h1Array = htmlClickHouseEntityPrevious.getCrawlerResponse().getH1();
			//if (h1Array != null && h1Array.length > 0) {
			//	h1String = Arrays.toString(h1Array);
			//} else {
			//	h1String = null;
			//}
			//FormatUtils.getInstance().logMemoryUsage("processOneTargetUrl() ip=" + ip + ",domainId=" + domainId + ",previous h1=" + h1String);
			//}

			// h2
			//if (BooleanUtils.isTrue(htmlClickHouseEntityCurrent.getH2ChgInd())) {
			//h2Array = htmlClickHouseEntityPrevious.getCrawlerResponse().getH2();
			//if (h2Array != null && h2Array.length > 0) {
			//	h2String = Arrays.toString(h2Array);
			//} else {
			//	h2String = null;
			//}
			//FormatUtils.getInstance().logMemoryUsage("processOneTargetUrl() ip=" + ip + ",domainId=" + domainId + ",previous h2=" + h2String);
			//}

			// robots
			//if (BooleanUtils.isTrue(htmlClickHouseEntityCurrent.getRobotsContentsChgInd())) {
			//FormatUtils.getInstance().logMemoryUsage("processOneTargetUrl() ip=" + ip + ",domainId=" + domainId + ",previous robotsContents="
			//		+ htmlClickHouseEntityPrevious.getCrawlerResponse().getRobots_contents());
			//}

			// canonical
			//if (BooleanUtils.isTrue(htmlClickHouseEntityCurrent.getCanonicalChgInd())) {
			//FormatUtils.getInstance().logMemoryUsage("processOneTargetUrl() ip=" + ip + ",domainId=" + domainId + ",previous canonical="
			//		+ htmlClickHouseEntityPrevious.getCrawlerResponse().getCanonical());
			//}

			isNumberOfH1Changed = false;
			isNumberOfH2Changed = false;
			if (BooleanUtils.isTrue(htmlClickHouseEntityCurrent.getResponseCodeChgInd())) {
				fromResponseCode = htmlClickHouseEntityPrevious.getHttpStatusCode();
				toResponseCode = htmlClickHouseEntityCurrent.getHttpStatusCode();
				updateResults(fromResponseCode, toResponseCode, targetUrlId);
			} else if (htmlClickHouseEntityCurrent.getHttpStatusCode() == 200 && htmlClickHouseEntityPrevious.getHttpStatusCode() == 200) {
				for (String alertName : alertNameList) {
					if (StringUtils.equalsIgnoreCase(alertName, ALERT_NAME_ADDITIONAL_CONTENT)) {
						processAdditionalContentChanges(htmlClickHouseEntityCurrent, alertName, htmlClickHouseEntityPrevious, targetUrlId, targetUrlString);
					} else {
						processNonAdditionalContentChanges(htmlClickHouseEntityCurrent, alertName, htmlClickHouseEntityPrevious, targetUrlId, targetUrlString);
					}
				}
			}
		} else {
			FormatUtils.getInstance().logMemoryUsage("processOneTargetUrl() error--ip=" + ip + ",domainId=" + domainId + ",trackDate=" + trackDate + ",targetUrlString="
					+ targetUrlString + ",htmlClickHouseEntityPrevious is null.");
		}
	}

	private void processAdditionalContentChanges(HtmlClickHouseEntity htmlClickHouseEntityCurrent, String alertName, HtmlClickHouseEntity htmlClickHouseEntityPrevious,
			Long targetUrlId, String targetUrlString) throws Exception {
		FormatUtils.getInstance()
				.logMemoryUsage("processAdditionalContentChanges() begins. ip=" + ip + ",domainId=" + domainId + ",alertName=" + alertName + ",targetUrlId="
						+ targetUrlId + ",targetUrlString=" + targetUrlString + ",htmlClickHouseEntityCurrent.getCustomDataChgInd()="
						+ htmlClickHouseEntityCurrent.getCustomDataChgInd());
		String currentContent = null;
		String previousContent = null;
		int numberOfDifferences = 0;

		// when any of the change tracking field is changed
		if (BooleanUtils.isTrue(htmlClickHouseEntityCurrent.getCustomDataChgInd()) && alertName.equalsIgnoreCase(ALERT_NAME_ADDITIONAL_CONTENT)) {
			// continue processing...
		} else {
			return;
		}

		String selectorTypeText = null;
		String selector = null;
		Integer selectTypeNumber = null;

		// get selector type text and selector from current crawl results
		List<SelectorValueObject> selectorValueObjectListCurrent = getSelectorValueObjectList(htmlClickHouseEntityCurrent);

		if (selectorValueObjectListCurrent != null && selectorValueObjectListCurrent.size() > 0) {
			nextSelectorValueObject: for (SelectorValueObject selectorValueObject : selectorValueObjectListCurrent) {
				selectorTypeText = selectorValueObject.getSelectorTypeText();
				selector = selectorValueObject.getSelector();
				selectTypeNumber = CrawlerUtils.getInstance().getSelectorTypeNumber(selectorTypeText);
				FormatUtils.getInstance().logMemoryUsage("processAdditionalContentChanges() ip=" + ip + ",domainId=" + domainId + ",alertName=" + alertName
						+ ",selectorTypeText=" + selectorTypeText + ",selectTypeNumber=" + selectTypeNumber + ",selector=" + selector);
				if (selectTypeNumber == null) {
					FormatUtils.getInstance().logMemoryUsage("processAdditionalContentChanges() error--ip=" + ip + ",domainId=" + domainId + ",targetUrlString="
							+ targetUrlString + ",invalid selectorTypeText=" + selectorTypeText);
					continue nextSelectorValueObject;
				}

				currentContent = extractAdditionalContent(htmlClickHouseEntityCurrent, selectorTypeText, selector);
				FormatUtils.getInstance()
						.logMemoryUsage("processAdditionalContentChanges() ip=" + ip + ",domainId=" + domainId + ",alertName=" + alertName + ",selectorTypeText="
								+ selectorTypeText + ",selectTypeNumber=" + selectTypeNumber + ",selector=" + selector + ",currentContent=" + currentContent);
				previousContent = extractAdditionalContent(htmlClickHouseEntityPrevious, selectorTypeText, selector);
				FormatUtils.getInstance()
						.logMemoryUsage("processAdditionalContentChanges() ip=" + ip + ",domainId=" + domainId + ",alertName=" + alertName + ",selectorTypeText="
								+ selectorTypeText + ",selectTypeNumber=" + selectTypeNumber + ",selector=" + selector + ",previousContent=" + previousContent);

				// calculate the difference in content length
				numberOfDifferences = FormatUtils.getInstance().calcLengthDifference(currentContent, previousContent);
				if (numberOfDifferences != 0) {
					updateResults(alertName, targetUrlId, numberOfDifferences, selectTypeNumber, selector);
				}
				// when the length of current and previous content identical, calculate the content difference... 
				else {
					numberOfDifferences = FormatUtils.getInstance().calcContentDifference(currentContent, previousContent);
					if (numberOfDifferences > 0) {
						updateResults(alertName, targetUrlId, numberOfDifferences, selectTypeNumber, selector);
					}
				}
			}
		} else {
			FormatUtils.getInstance().logMemoryUsage("processAdditionalContentChanges() ip=" + ip + ",domainId=" + domainId + ",alertName=" + alertName
					+ ",targetUrlId=" + targetUrlId + ",targetUrlString=" + targetUrlString + ",selectorValueObjectListCurrent is empty.");
		}
		FormatUtils.getInstance().logMemoryUsage("processAdditionalContentChanges() ends. ip=" + ip + ",domainId=" + domainId + ",alertName=" + alertName
				+ ",targetUrlId=" + targetUrlId + ",targetUrlString=" + targetUrlString);
	}

	private void processNonAdditionalContentChanges(HtmlClickHouseEntity htmlClickHouseEntityCurrent, String alertName,
			HtmlClickHouseEntity htmlClickHouseEntityPrevious, Long targetUrlId, String targetUrlString) {
		String currentContent = null;
		String previousContent = null;
		int numberOfDifferences = 0;

		// title
		if (BooleanUtils.isTrue(htmlClickHouseEntityCurrent.getTitleChgInd()) && alertName.equalsIgnoreCase(ALERT_NAME_TITLE)) {
			// continue processing...
		}
		// description
		else if (BooleanUtils.isTrue(htmlClickHouseEntityCurrent.getDescriptionChgInd()) && alertName.equalsIgnoreCase(ALERT_NAME_META_DESC)) {
			// continue processing...			
		}
		// robots
		else if (BooleanUtils.isTrue(htmlClickHouseEntityCurrent.getRobotsContentsChgInd()) && alertName.equalsIgnoreCase(ALERT_NAME_META_ROBOTS)) {
			// continue processing...			
		}
		// canonical
		else if (BooleanUtils.isTrue(htmlClickHouseEntityCurrent.getCanonicalChgInd()) && alertName.equalsIgnoreCase(ALERT_NAME_CANONICAL_URL)) {
			// continue processing...			
		}
		// number of h1
		else if (BooleanUtils.isTrue(htmlClickHouseEntityCurrent.getH1ChgInd()) && alertName.equalsIgnoreCase(ALERT_NAME_H1)) {
			// continue processing...			
		}
		// number of h2
		else if (BooleanUtils.isTrue(htmlClickHouseEntityCurrent.getH2ChgInd()) && alertName.equalsIgnoreCase(ALERT_NAME_H2)) {
			// continue processing...				
		}
		// content of h1
		else if (BooleanUtils.isTrue(htmlClickHouseEntityCurrent.getH1ChgInd()) && alertName.equalsIgnoreCase(ALERT_NAME_H1_CONTENT)) {
			// continue processing...				
		}
		// content of h2
		else if (BooleanUtils.isTrue(htmlClickHouseEntityCurrent.getH2ChgInd()) && alertName.equalsIgnoreCase(ALERT_NAME_H2_CONTENT)) {
			// continue processing...				
		} else {
			return;
		}

		currentContent = extractNonAdditionalContent(htmlClickHouseEntityCurrent, alertName);
		//FormatUtils.getInstance()
		//		.logMemoryUsage("processNonAdditionalContentChanges() ip=" + ip + ",domainId=" + domainId + ",alertName=" + alertName + ",currentContent=" + currentContent);
		previousContent = extractNonAdditionalContent(htmlClickHouseEntityPrevious, alertName);
		//FormatUtils.getInstance()
		//		.logMemoryUsage("processNonAdditionalContentChanges() ip=" + ip + ",domainId=" + domainId + ",alertName=" + alertName + ",previousContent=" + previousContent);

		// when alert is to check the number of h1 or h2, the extracted content is a number..
		if (alertName.equalsIgnoreCase(ALERT_NAME_H1) || alertName.equalsIgnoreCase(ALERT_NAME_H2)) {
			// calculate the number of headers difference
			numberOfDifferences = NumberUtils.toInt(currentContent) - NumberUtils.toInt(previousContent);
			if (numberOfDifferences != 0) {
				updateResults(alertName, targetUrlId, numberOfDifferences);
				if (alertName.equalsIgnoreCase(ALERT_NAME_H1)) {
					isNumberOfH1Changed = true;
				} else if (alertName.equalsIgnoreCase(ALERT_NAME_H2)) {
					isNumberOfH2Changed = true;
				}
			}
		}
		// for all other alerts, the extracted content is text 
		else {
			if (alertName.equalsIgnoreCase(ALERT_NAME_H1_CONTENT) && isNumberOfH1Changed == true) {
				// when number of H1 changed, no need to compare H1 content
			} else if (alertName.equalsIgnoreCase(ALERT_NAME_H2_CONTENT) && isNumberOfH2Changed == true) {
				// when number of H2 changed, no need to compare H2 content
			} else {
				// calculate the difference in content length
				numberOfDifferences = FormatUtils.getInstance().calcLengthDifference(currentContent, previousContent);
				if (numberOfDifferences != 0) {
					updateResults(alertName, targetUrlId, numberOfDifferences);
				}
				// when the length of current and previous content identical, calculate the content difference... 
				else {
					numberOfDifferences = FormatUtils.getInstance().calcContentDifference(currentContent, previousContent);
					if (numberOfDifferences > 0) {
						updateResults(alertName, targetUrlId, numberOfDifferences);
					}
				}
			}
		}
		//FormatUtils.getInstance().logMemoryUsage("processNonAdditionalContentChanges() ip=" + ip + ",domainId=" + domainId + ",alertName=" + alertName + ",numberOfDifferences="
		//		+ numberOfDifferences);
	}

	private String extractAdditionalContent(HtmlClickHouseEntity htmlClickHouseEntity, String selectorTypeText, String selector) {
		String contentString = null;
		CustomData[] customDataArray = htmlClickHouseEntity.getCrawlerResponse().getCustom_data();
		if (customDataArray != null && customDataArray.length > 0) {
			for (CustomData customData : customDataArray) {
				if (StringUtils.equalsIgnoreCase(customData.getSelector_type(), selectorTypeText) && StringUtils.equalsIgnoreCase(customData.getSelector(), selector)) {
					contentString = new Gson().toJson(customData, CustomData.class);
				}
			}
		}

		if (contentString == null) {
			contentString = IConstants.EMPTY_STRING;
		} else {
			contentString = StringEscapeUtils.unescapeHtml(contentString);
		}

		return contentString;
	}

	private String extractNonAdditionalContent(HtmlClickHouseEntity htmlClickHouseEntity, String alertName) {
		String contentString = null;
		if (alertName.equalsIgnoreCase(ALERT_NAME_TITLE)) {
			contentString = htmlClickHouseEntity.getCrawlerResponse().getTitle();
		} else if (alertName.equalsIgnoreCase(ALERT_NAME_META_DESC)) {
			contentString = htmlClickHouseEntity.getCrawlerResponse().getDescription();
		} else if (alertName.equalsIgnoreCase(ALERT_NAME_H1_CONTENT)) {
			contentString = FormatUtils.getInstance().toString(htmlClickHouseEntity.getCrawlerResponse().getH1());
		} else if (alertName.equalsIgnoreCase(ALERT_NAME_H2_CONTENT)) {
			contentString = FormatUtils.getInstance().toString(htmlClickHouseEntity.getCrawlerResponse().getH2());
		}
		// compare the number of h1
		else if (alertName.equalsIgnoreCase(ALERT_NAME_H1)) {
			if (htmlClickHouseEntity.getCrawlerResponse().getH1() != null && htmlClickHouseEntity.getCrawlerResponse().getH1().length > 0) {
				contentString = String.valueOf(htmlClickHouseEntity.getCrawlerResponse().getH1().length);
			} else {
				contentString = STRING_ZERO;
			}
		}
		// compare the number of h2
		else if (alertName.equalsIgnoreCase(ALERT_NAME_H2)) {
			if (htmlClickHouseEntity.getCrawlerResponse().getH2() != null && htmlClickHouseEntity.getCrawlerResponse().getH2().length > 0) {
				contentString = String.valueOf(htmlClickHouseEntity.getCrawlerResponse().getH2().length);
			} else {
				contentString = STRING_ZERO;
			}
		}
		// meta robots
		else if (alertName.equalsIgnoreCase(ALERT_NAME_META_ROBOTS)) {
			contentString = htmlClickHouseEntity.getCrawlerResponse().getRobots_contents();
		}
		// canonical URL
		else if (alertName.equalsIgnoreCase(ALERT_NAME_CANONICAL_URL)) {
			contentString = htmlClickHouseEntity.getCrawlerResponse().getCanonical();
		}

		if (contentString == null) {
			contentString = IConstants.EMPTY_STRING;
		} else {
			contentString = StringEscapeUtils.unescapeHtml(contentString);
		}

		return contentString;
	}

	private void updateResults(Integer fromResponseCode, Integer toResponseCode, Long targetUrlId) {
		PageContentChangeEntity pageContentChangeEntity = new PageContentChangeEntity();
		pageContentChangeEntity.setDomainId(domainId);
		pageContentChangeEntity.setChangeDate(processDateNumber);
		pageContentChangeEntity.setTargetUrlId(targetUrlId);
		pageContentChangeEntity.setChangeType(CHANGE_TYPE_RESP_CODE);
		pageContentChangeEntity.setDiff(null);
		pageContentChangeEntity.setRespCodeChange(fromResponseCode + TO + toResponseCode);
		pageContentChangeEntity.setSelectorType(0);
		pageContentChangeEntity.setSelector(IConstants.EMPTY_STRING);
		// create only when not already in database
		PageContentChangeEntity pageContentChangeEntityExisting = pageContentChangeDAO.get(domainId, processDateNumber, targetUrlId, CHANGE_TYPE_RESP_CODE,
				pageContentChangeEntity.getSelectorType(), pageContentChangeEntity.getSelector());
		if (pageContentChangeEntityExisting == null) {
			pageContentChangeEntityToBeCreatedList.add(pageContentChangeEntity);
			if (pageContentChangeEntityToBeCreatedList.size() >= MAX_RECORDS_PER_DATABASE_UPDATE) {
				pageContentChangeDAO.insertMultiRowsBatch(pageContentChangeEntityToBeCreatedList);
				pageContentChangeEntityToBeCreatedList = new ArrayList<PageContentChangeEntity>();
			}
		}
	}

	private void updateResults(String alertName, Long targetUrlId, Integer difference) {
		PageContentChangeEntity pageContentChangeEntity = new PageContentChangeEntity();
		pageContentChangeEntity.setDomainId(domainId);
		pageContentChangeEntity.setChangeDate(processDateNumber);
		pageContentChangeEntity.setTargetUrlId(targetUrlId);
		pageContentChangeEntity.setChangeType(alertNameChangeTypeMap.get(alertName));
		pageContentChangeEntity.setDiff(difference);
		pageContentChangeEntity.setRespCodeChange(null);
		pageContentChangeEntity.setSelectorType(0);
		pageContentChangeEntity.setSelector(IConstants.EMPTY_STRING);

		// create only when not already in database
		PageContentChangeEntity pageContentChangeEntityExisting = pageContentChangeDAO.get(domainId, processDateNumber, targetUrlId,
				pageContentChangeEntity.getChangeType(), pageContentChangeEntity.getSelectorType(), pageContentChangeEntity.getSelector());
		if (pageContentChangeEntityExisting == null) {
			pageContentChangeEntityToBeCreatedList.add(pageContentChangeEntity);
			if (pageContentChangeEntityToBeCreatedList.size() >= MAX_RECORDS_PER_DATABASE_UPDATE) {
				pageContentChangeDAO.insertMultiRowsBatch(pageContentChangeEntityToBeCreatedList);
				pageContentChangeEntityToBeCreatedList = new ArrayList<PageContentChangeEntity>();
			}
		}
	}

	private void updateResults(String alertName, Long targetUrlId, Integer difference, Integer selectorType, String selector) {
		PageContentChangeEntity pageContentChangeEntity = new PageContentChangeEntity();
		pageContentChangeEntity.setDomainId(domainId);
		pageContentChangeEntity.setChangeDate(processDateNumber);
		pageContentChangeEntity.setTargetUrlId(targetUrlId);
		pageContentChangeEntity.setChangeType(alertNameChangeTypeMap.get(alertName));
		pageContentChangeEntity.setDiff(difference);
		pageContentChangeEntity.setRespCodeChange(null);
		pageContentChangeEntity.setSelectorType(selectorType);
		pageContentChangeEntity.setSelector(selector);

		// create only when not already in database
		PageContentChangeEntity pageContentChangeEntityExisting = pageContentChangeDAO.get(domainId, processDateNumber, targetUrlId,
				pageContentChangeEntity.getChangeType(), pageContentChangeEntity.getSelectorType(), pageContentChangeEntity.getSelector());
		if (pageContentChangeEntityExisting == null) {
			pageContentChangeEntityToBeCreatedList.add(pageContentChangeEntity);
			if (pageContentChangeEntityToBeCreatedList.size() >= MAX_RECORDS_PER_DATABASE_UPDATE) {
				pageContentChangeDAO.insertMultiRowsBatch(pageContentChangeEntityToBeCreatedList);
				pageContentChangeEntityToBeCreatedList = new ArrayList<PageContentChangeEntity>();
			}
		}
	}

	private List<SelectorValueObject> getSelectorValueObjectList(HtmlClickHouseEntity htmlClickHouseEntity) {
		List<SelectorValueObject> selectorValueObjectList = new ArrayList<SelectorValueObject>();
		SelectorValueObject testSelectorValueObject = null;
		CustomData[] customDataArray = htmlClickHouseEntity.getCrawlerResponse().getCustom_data();
		if (customDataArray != null && customDataArray.length > 0) {
			nextCustomData: for (CustomData customData : customDataArray) {
				for (SelectorValueObject selectorValueObject : selectorValueObjectList) {
					if (StringUtils.equalsIgnoreCase(customData.getSelector_type(), selectorValueObject.getSelectorTypeText())
							&& StringUtils.equalsIgnoreCase(customData.getSelector(), selectorValueObject.getSelector())) {
						continue nextCustomData;
					}
				}
				testSelectorValueObject = new SelectorValueObject();
				testSelectorValueObject.setSelectorTypeText(customData.getSelector_type());
				testSelectorValueObject.setSelector(customData.getSelector());
				selectorValueObjectList.add(testSelectorValueObject);
			}
		}
		return selectorValueObjectList;
	}

	@Override
	protected void undo() throws Exception {
		// TODO Auto-generated method stub

	}
}
