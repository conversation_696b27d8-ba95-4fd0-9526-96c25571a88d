package com.actonia.page.content.change;

import com.actonia.IConstants;
import com.actonia.concurrency.BaseThreadCommand;
import com.actonia.concurrency.CacheModleFactory;
import com.actonia.dao.*;
import com.actonia.entity.*;
import com.actonia.service.AgencyInfoService;
import com.actonia.utils.*;
import com.actonia.value.object.*;
import com.google.gson.Gson;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.util.*;

public class PageTagContentChangeUpdateCommand extends BaseThreadCommand {

	//private boolean isDebug = false;
	private String ip;
	private GroupTagEntityDAO groupTagEntityDAO;
	private GroupTagRelationEntityDAO groupTagRelationEntityDAO;
	private PageContentChangeDAO pageContentChangeDAO;
	private PageTagContentChangeDAO pageTagContentChangeDAO;
	private int processDateNumber;
	private int domainId;
	private String domainName;
	private String coreName;

	private EmailSenderComponent emailSenderComponent;

	private static final String EMAIL_SUBJECT = "ALERT! Significant Page Tag Content Changes Detected on ";
	private static final String DASH = " - ";
	private static final String PERCENT = "%";

	private static final int MAX_NUMBER_OF_DB_RECORDS_PER_CREATION = 100;

	private String processDateString;

	private static final int ALERT_METRIC_TYPE_TITLE = 1;
	private static final int ALERT_METRIC_TYPE_META_DESC = 2;
	private static final int ALERT_METRIC_TYPE_NUMBER_OF_H1 = 4;
	private static final int ALERT_METRIC_TYPE_CONTENT_OF_H1 = 5;
	private static final int ALERT_METRIC_TYPE_NUMBER_OF_H2 = 6;
	private static final int ALERT_METRIC_TYPE_CONTENT_OF_H2 = 7;
	private static final int ALERT_METRIC_TYPE_META_ROBOTS = 9;
	private static final int ALERT_METRIC_TYPE_CANONICAL_URL = 10;
	private static final int ALERT_METRIC_TYPE_RESP_CODE = 88;
	private static final String ALERT_METRIC_TITLE = "Title";
	private static final String ALERT_METRIC_META_DESC = "Meta Desc";
	private static final String ALERT_METRIC_NUMBER_OF_H1 = "Number of H1";
	private static final String ALERT_METRIC_CONTENT_OF_H1 = "Content of H1";
	private static final String ALERT_METRIC_NUMBER_OF_H2 = "Number of H2";
	private static final String ALERT_METRIC_CONTENT_OF_H2 = "Content of H2";
	private static final String ALERT_METRIC_META_ROBOTS = "Meta Robots";
	private static final String ALERT_METRIC_CANONICAL_URL = "Canonical URL";
	private static final String ALERT_METRIC_RESP_CODE = "Response Code";
	private final static BigDecimal DECIMAL_100 = new BigDecimal(100);
	private static final String DATE_FORMAT_MMDDYY = "MM/dd/yy";

	private OwnDomainEntity ownDomainEntity;
	private static final String DEV_EMAIL_ADDRESS = "<EMAIL>";
	private Date processDate;

	private List<PageTagAlertConfigEntity> pageTagAlertConfigEntityList;

	private AgencyInfoService agencyInfoService;

	//private static final String PAGE_TAG_NAME_HOTEL_PROP_PAGES_FOR_CHANGE_ALERTS = "Hotel Property Pages for Change Alerts";

	//private TargetUrlEntityDAO targetUrlEntityDAO;

	//private static final String HOTEL_URL_SQL_SRCH_PATTERN = "%/hotel/%.h%";

	//private static final Integer[] specialPageTagDomainArray = new Integer[] { 522, 551, 552, 553, 554, 555, 556, 557, 558, 560, 561, 562, 593 };

	private List<PageTagAlertConfigAdditionalContentEntity> allPageTagAlertConfigAdditionalContentEntityList;

	private ZapierWebhookDAO zapierWebhookDAO;

	public PageTagContentChangeUpdateCommand(String ip, OwnDomainEntity ownDomainEntity, int processDateNumber,
			List<PageTagAlertConfigEntity> pageTagAlertConfigEntityList,
			List<PageTagAlertConfigAdditionalContentEntity> allPageTagAlertConfigAdditionalContentEntityList) {
		super();
		this.ip = ip;
		this.groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
		this.groupTagRelationEntityDAO = SpringBeanFactory.getBean("groupTagRelationEntityDAO");
		this.pageContentChangeDAO = SpringBeanFactory.getBean("pageContentChangeDAO");
		this.pageTagContentChangeDAO = SpringBeanFactory.getBean("pageTagContentChangeDAO");
		this.emailSenderComponent = SpringBeanFactory.getBean("emailSenderComponent");
		this.agencyInfoService = SpringBeanFactory.getBean("agencyInfoService");
		//this.targetUrlEntityDAO = SpringBeanFactory.getBean("targetUrlEntityDAO");
		this.domainId = ownDomainEntity.getId();
		this.domainName = ownDomainEntity.getDomain();
		this.coreName = ownDomainEntity.getLanguage();
		this.processDateNumber = processDateNumber;
		this.pageTagAlertConfigEntityList = pageTagAlertConfigEntityList;
		this.ownDomainEntity = ownDomainEntity;
		try {
			processDate = DateUtils.parseDate(String.valueOf(processDateNumber), new String[] { IConstants.DATE_FORMAT_YYYYMMDD });
			processDateString = DateFormatUtils.format(processDate, IConstants.DATE_FORMAT_YYYY_MM_DD);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		this.allPageTagAlertConfigAdditionalContentEntityList = allPageTagAlertConfigAdditionalContentEntityList;
		this.zapierWebhookDAO = SpringBeanFactory.getBean("zapierWebhookDAO");
	}

	@Override
	protected void execute() throws Exception {
		long startTimestamp = System.currentTimeMillis();
		FormatUtils.getInstance().logMemoryUsage("execute() begins. ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",coreName=" + coreName
				+ ",processDateNumber=" + processDateNumber);

		// map key = group tag ID
		// map value = List of PageContentChangeEntity
		Map<Integer, List<PageContentChangeEntity>> groupTagContentChangesMap = null;

		List<PageTagAlertConfigAdditionalContentEntity> pageTagAlertConfigAdditionalContentEntityList = null;

		// reset the existing data (domain ID + process date) for re-runnability
		pageTagContentChangeDAO.reset(domainId, processDateNumber);

		// wrike ticket #60574485
		//maintainSpecialHotelPropertyPagesTag();

		// when there are page content changes for the client domain on the process date
		List<PageContentChangeEntity> pageContentChangeEntityList = pageContentChangeDAO.getGroupTagChanges(domainId, processDateNumber,
				GroupTagEntity.TAG_TYPE_TARGET_URL);
		if (pageContentChangeEntityList != null && pageContentChangeEntityList.size() > 0) {
			groupTagContentChangesMap = getGroupTagContentChangeEntityListMap(pageContentChangeEntityList);
			if (groupTagContentChangesMap != null && groupTagContentChangesMap.size() > 0) {
				FormatUtils.getInstance().logMemoryUsage("execute() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ","
						+ groupTagContentChangesMap.keySet().size() + " group tags changed on " + processDateNumber);
				for (PageTagAlertConfigEntity pageTagAlertConfigEntity : pageTagAlertConfigEntityList) {
					pageTagAlertConfigAdditionalContentEntityList = getPageTagAlertConfigAdditionalContentEntityList(pageTagAlertConfigEntity.getId());
					process(pageTagAlertConfigEntity, pageTagAlertConfigAdditionalContentEntityList, groupTagContentChangesMap);
				}
			}
		} else {
			FormatUtils.getInstance()
					.logMemoryUsage("execute() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",no content changes on " + processDateNumber);
		}

		CacheModleFactory.getInstance().setAliveIpAddress(ip);

		FormatUtils.getInstance().logMemoryUsage("execute() ends. ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",elapsed(s.)="
				+ (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private List<PageTagAlertConfigAdditionalContentEntity> getPageTagAlertConfigAdditionalContentEntityList(int pageTagAlertConfigId) {
		List<PageTagAlertConfigAdditionalContentEntity> pageTagAlertConfigAdditionalContentEntityList = new ArrayList<PageTagAlertConfigAdditionalContentEntity>();
		if (allPageTagAlertConfigAdditionalContentEntityList != null && allPageTagAlertConfigAdditionalContentEntityList.size() > 0) {
			for (PageTagAlertConfigAdditionalContentEntity pageTagAlertConfigAdditionalContentEntity : allPageTagAlertConfigAdditionalContentEntityList) {
				if (pageTagAlertConfigAdditionalContentEntity.getPageTagAlertConfigId() == pageTagAlertConfigId) {
					pageTagAlertConfigAdditionalContentEntityList.add(pageTagAlertConfigAdditionalContentEntity);
				}
			}
		}
		return pageTagAlertConfigAdditionalContentEntityList;
	}

	private Map<Integer, List<PageContentChangeEntity>> getGroupTagContentChangeEntityListMap(List<PageContentChangeEntity> pageContentChangeEntityList) {
		List<PageContentChangeEntity> tempPageContentChangeEntityList = null;
		Map<Integer, List<PageContentChangeEntity>> groupTagContentChangesMap = new HashMap<Integer, List<PageContentChangeEntity>>();
		for (PageContentChangeEntity pageContentChangeEntity : pageContentChangeEntityList) {
			if (groupTagContentChangesMap.containsKey(pageContentChangeEntity.getGroupTagId())) {
				tempPageContentChangeEntityList = groupTagContentChangesMap.get(pageContentChangeEntity.getGroupTagId());
			} else {
				tempPageContentChangeEntityList = new ArrayList<PageContentChangeEntity>();
			}
			tempPageContentChangeEntityList.add(pageContentChangeEntity);
			groupTagContentChangesMap.put(pageContentChangeEntity.getGroupTagId(), tempPageContentChangeEntityList);
		}
		//if (isDebug == true) {
		//	for (Integer groupTagId : groupTagContentChangesMap.keySet()) {
		//		tempPageContentChangeEntityList = groupTagContentChangesMap.get(groupTagId);
		//		for (PageContentChangeEntity pageContentChangeEntity : tempPageContentChangeEntityList) {
		//			FormatUtils.getInstance().logMemoryUsage("getGroupTagContentChangeEntityListMap() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
		//					+ ",groupTagId=" + groupTagId + ",pageContentChangeEntity=" + pageContentChangeEntity.toString());
		//		}
		//	}
		//}
		return groupTagContentChangesMap;
	}

	private void process(PageTagAlertConfigEntity pageTagAlertConfigEntity,
			List<PageTagAlertConfigAdditionalContentEntity> pageTagAlertConfigAdditionalContentEntityList,
			Map<Integer, List<PageContentChangeEntity>> groupTagContentChangesMap) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("process() begins. ip=" + ip + ",alertId=" + pageTagAlertConfigEntity.getId() + ",domainId="
				+ pageTagAlertConfigEntity.getDomainId() + ",createdByUserId=" + pageTagAlertConfigEntity.getCreatedByUserId() + ",dataContentChangeThreshold="
				+ pageTagAlertConfigEntity.getDataContentChangeThreshold() + ",tagContentChangeThreshold=" + pageTagAlertConfigEntity.getTagContentChangeThreshold()
				+ ",tagContentChangeThresholdPercent=" + pageTagAlertConfigEntity.getTagContentChangeThresholdPercent() + ",tagContentChangeTagIds="
				+ pageTagAlertConfigEntity.getTagContentChangeTagIds() + ",tagContentChangeAlertEmailAddresses="
				+ pageTagAlertConfigEntity.getTagContentChangeAlertEmailAddresses() + ",alertMetricTypes=" + pageTagAlertConfigEntity.getAlertMetricTypes());
		if (pageTagAlertConfigAdditionalContentEntityList != null && pageTagAlertConfigAdditionalContentEntityList.size() > 0) {
			for (PageTagAlertConfigAdditionalContentEntity pageTagAlertConfigAdditionalContentEntity : pageTagAlertConfigAdditionalContentEntityList) {
				FormatUtils.getInstance().logMemoryUsage("process() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
						+ ",pageTagAlertConfigAdditionalContentEntity=" + pageTagAlertConfigAdditionalContentEntity.toString());
			}
		} else {
			FormatUtils.getInstance().logMemoryUsage(
					"process() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName + ",pageTagAlertConfigAdditionalContentEntityList is empty.");
		}
		long startTimestamp = System.currentTimeMillis();
		List<PageContentChangeEntity> pageContentChangeEntityList = null;
		int totalTargetUrlsRequireAlert = 0;
		int totalTargetUrlsInGroupTag = 0;
		int contentDifference = 0;
		PageTagContentChangeResults pageTagContentChangeResults = null;
		GroupTagEntity groupTagEntity = null;
		List<Long> responseCodeUrlIdList = null;
		List<Long> additionalContentUrlIdList = null;
		List<Long> changeTypeUrlIdList = null;
		String responseCodeChange = null;
		Set<Long> targetUrlRequiredAlertSet = null;
		Integer changeType = null;
		BigDecimal totalTargetUrlsInGroupTagDecimal = null;
		BigDecimal totalTargetUrlsRequireAlertDecimal = null;
		BigDecimal percentOfTargetUrlsRequireAlert = null;
		BigDecimal fractionOfTargetUrlsChanged = null;
		PageTagContentChangeAlertEmailValueObject pageTagContentChangeAlertEmailValueObject = null;
		PageTagResponseCodeChangeAlertEmailValueObject pageTagResponseCodeChangeAlertEmailValueObject = null;
		PageTagAdditionalContentChangeAlertEmailValueObject pageTagAdditionalContentChangeAlertEmailValueObject = null;
		List<PageTagContentChangeAlertEmailValueObject> contentChangeEmailAlertList = null;
		List<PageTagResponseCodeChangeAlertEmailValueObject> responseCodeChangeEmailAlertList = null;
		List<PageTagAdditionalContentChangeAlertEmailValueObject> additionalContentChangeEmailAlertList = null;
		List<PageTagAlertEmailValueObject> pageTagAlertEmailValueObjectList = new ArrayList<PageTagAlertEmailValueObject>();
		PageTagAlertEmailValueObject pageTagAlertEmailValueObject = null;
		String[] emailAddressArray = null;
		int contentChangeThreshold = pageTagAlertConfigEntity.getDataContentChangeThreshold();
		BigDecimal tagLevelChangeThreshold = null;
		if (pageTagAlertConfigEntity.getTagContentChangeThresholdPercent() != null) {
			tagLevelChangeThreshold = pageTagAlertConfigEntity.getTagContentChangeThresholdPercent().setScale(8, RoundingMode.HALF_UP);
		} else {
			tagLevelChangeThreshold = new BigDecimal(pageTagAlertConfigEntity.getTagContentChangeThreshold()).setScale(8, RoundingMode.HALF_UP);
		}
		boolean isProcessAdditionalContentChange = false;
		String mapKey = null;
		//int optionId = 0;
		boolean isContentThresholdFiftyCharacters = false;
		//OptionEntity currentOptionEntity = null;

		List<Integer> alertGroupTagIdList = getAlertGroupTagIdList(domainId, pageTagAlertConfigEntity.getId(), pageTagAlertConfigEntity.getTagContentChangeTagIds());

		List<Integer> changeTypeList = getChangeTypeList(domainId, pageTagAlertConfigEntity.getId(), pageTagAlertConfigEntity.getAlertMetricTypes());

		String selectorTypeText = null;

		for (Integer groupTagId : groupTagContentChangesMap.keySet()) {
			// when alert group tag ID list is empty, process the group tag id in groupTagContentChangesMap 
			// when alert group tag ID list is not empty, skip the group tag id in groupTagContentChangesMap when the group tag ID not in alert group tag ID list
			if (alertGroupTagIdList != null && alertGroupTagIdList.size() > 0) {
				if (alertGroupTagIdList.contains(groupTagId)) {
					//if (isDebug == true) {
					//	FormatUtils.getInstance().logMemoryUsage("process() ip=" + ip + ",alertId=" + pageTagAlertConfigEntity.getId() + ",domainId="
					//			+ pageTagAlertConfigEntity.getDomainId() + ",process group tag ID in alert config=" + groupTagId);
					//}
				} else {
					//if (isDebug == true) {
					//	FormatUtils.getInstance().logMemoryUsage("process() ip=" + ip + ",alertId=" + pageTagAlertConfigEntity.getId() + ",domainId="
					//			+ pageTagAlertConfigEntity.getDomainId() + ",skip group tag ID not in alert config=" + groupTagId);
					//}
					continue;
				}
			} else {
				//if (isDebug == true) {
				//	FormatUtils.getInstance().logMemoryUsage("process() ip=" + ip + ",alertId=" + pageTagAlertConfigEntity.getId() + ",domainId="
				//			+ pageTagAlertConfigEntity.getDomainId() + ",alert group ID list is empty, process group tag ID=" + groupTagId);
				//}
			}

			pageContentChangeEntityList = groupTagContentChangesMap.get(groupTagId);
			if (pageContentChangeEntityList != null && pageContentChangeEntityList.size() > 0) {

				targetUrlRequiredAlertSet = new HashSet<Long>();
				pageTagContentChangeResults = new PageTagContentChangeResults();
				pageTagContentChangeResults.setGroupTagId(groupTagId);
				groupTagEntity = groupTagEntityDAO.getGroupTagEntity(domainId, groupTagId, GroupTagEntity.TAG_TYPE_TARGET_URL);
				if (groupTagEntity != null) {
					pageTagContentChangeResults.setGroupTagName(groupTagEntity.getTagName());
				}

				// determine the total number of target URLs in page tag
				totalTargetUrlsInGroupTag = groupTagRelationEntityDAO.getNumberOfUrls(domainId, groupTagId, GroupTagRelationEntity.RESOURCE_TYPE_TARGETURL);
				pageTagContentChangeResults.setTotalTargetUrlsInGroupTag(totalTargetUrlsInGroupTag);

				// calculate the total number of target URLs in group tag that require alert
				nextPageContentChangeEntity: for (PageContentChangeEntity pageContentChangeEntity : pageContentChangeEntityList) {
					isProcessAdditionalContentChange = false;
					// when page content change type is 13, content change is for additional content, 
					// match the 'selector_type', and 'selector' in page content change record 
					// with the "pageTagAlertConfigAdditionalContentEntityList" (ie. page_tag_alert_config_additional_content)
					// to determine if the 'additional content' page content change needs to be processed...
					if (pageContentChangeEntity.getChangeType() == PageContentChangeUpdateCommand.CHANGE_TYPE_ADDITIONAL_CONTENT) {
						for (PageTagAlertConfigAdditionalContentEntity pageTagAlertConfigAdditionalContentEntity : pageTagAlertConfigAdditionalContentEntityList) {
							if (pageContentChangeEntity.getSelectorType() != null && pageTagAlertConfigAdditionalContentEntity.getSelectorType() != null
									&& pageContentChangeEntity.getSelectorType().compareTo(pageTagAlertConfigAdditionalContentEntity.getSelectorType()) == 0
									&& StringUtils.equalsIgnoreCase(pageContentChangeEntity.getSelector(),
											pageTagAlertConfigAdditionalContentEntity.getSelector()) == true) {
								isProcessAdditionalContentChange = true;
								break;
							}
						}
					}
					// when the 'additional content' page content change needs to be processed...
					if (isProcessAdditionalContentChange == true) {
						// execute the logic below....
						//if (isDebug == true) {
						//	FormatUtils.getInstance().logMemoryUsage("process() ip=" + ip + ",alertId=" + pageTagAlertConfigEntity.getId() + ",domainId="
						//			+ pageTagAlertConfigEntity.getDomainId() + ",isProcessAdditionalContentChange=" + isProcessAdditionalContentChange);
						//}
					} else
					// when page content change type is not in the alert configuration (page_tag_alert_config), skip this page content change record
					if (changeTypeList == null || changeTypeList.size() == 0
							|| (changeTypeList != null && changeTypeList.size() > 0 && !changeTypeList.contains(pageContentChangeEntity.getChangeType()))) {
						//if (isDebug == true) {
						//	FormatUtils.getInstance()
						//			.logMemoryUsage("process() ip=" + ip + ",alertId=" + pageTagAlertConfigEntity.getId() + ",domainId="
						//					+ pageTagAlertConfigEntity.getDomainId() + ",skip change type in pageContentChangeEntity="
						//					+ pageContentChangeEntity.getChangeType());
						//}
						continue;
					}

					//if (isDebug == true) {
					//	FormatUtils.getInstance().logMemoryUsage("process() ip=" + ip + ",processing alert=" + pageTagAlertConfigEntity.toString()
					//			+ ",pageContentChangeEntity=" + pageContentChangeEntity.toString());
					//}

					// alert is required when response code changed
					if (pageContentChangeEntity.getChangeType() == PageContentChangeUpdateCommand.CHANGE_TYPE_RESP_CODE) {
						targetUrlRequiredAlertSet.add(pageContentChangeEntity.getTargetUrlId());
						responseCodeChange = pageContentChangeEntity.getRespCodeChange();
						if (pageTagContentChangeResults.getResponseCodesUrlIdListMap().containsKey(responseCodeChange)) {
							responseCodeUrlIdList = pageTagContentChangeResults.getResponseCodesUrlIdListMap().get(responseCodeChange);
						} else {
							responseCodeUrlIdList = new ArrayList<Long>();
						}
						responseCodeUrlIdList.add(pageContentChangeEntity.getTargetUrlId());
						pageTagContentChangeResults.getResponseCodesUrlIdListMap().put(responseCodeChange, responseCodeUrlIdList);
					}
					// alert is required when number of H1 or H2 changed
					else if (pageContentChangeEntity.getChangeType() == PageContentChangeUpdateCommand.CHANGE_TYPE_NUMBER_OF_H1
							|| pageContentChangeEntity.getChangeType() == PageContentChangeUpdateCommand.CHANGE_TYPE_NUMBER_OF_H2) {
						targetUrlRequiredAlertSet.add(pageContentChangeEntity.getTargetUrlId());
						changeType = pageContentChangeEntity.getChangeType();
						if (pageTagContentChangeResults.getChangeTypeUrlIdListMap().containsKey(changeType)) {
							changeTypeUrlIdList = pageTagContentChangeResults.getChangeTypeUrlIdListMap().get(changeType);
						} else {
							changeTypeUrlIdList = new ArrayList<Long>();
						}
						changeTypeUrlIdList.add(pageContentChangeEntity.getTargetUrlId());
						pageTagContentChangeResults.getChangeTypeUrlIdListMap().put(changeType, changeTypeUrlIdList);
					}
					// determine if alert is required by comparing the content length changes against the content change threshold
					else {

						contentDifference = pageContentChangeEntity.getDiff();
						if (contentDifference < 0) {
							contentDifference = contentDifference * -1;
						}

						isContentThresholdFiftyCharacters = false;
						//if (pageContentChangeEntity.getChangeType() == PageContentChangeUpdateCommand.CHANGE_TYPE_DIV_CLASS_CONTENT && currentOptionEntity != null) {
						//	if (StringUtils.equalsIgnoreCase(currentOptionEntity.getOptionText(), ARTICLE_CONTENT_PREFORMATTED_READ_MORE_AGENT)) {
						//		isContentThresholdFiftyCharacters = true;
						//		FormatUtils.getInstance().logMemoryUsage("process() ip=" + ip + ",pageContentChangeEntity=" + pageContentChangeEntity.toString()
						//				+ ",isContentThresholdFiftyCharacters=" + isContentThresholdFiftyCharacters);
						//	}
						//}

						//if (isDebug == true) {
						//	FormatUtils.getInstance().logMemoryUsage(
						//			"process() ip=" + ip + ",contentDifference=" + contentDifference + ",contentChangeThreshold=" + contentChangeThreshold);
						//}

						if ((contentDifference >= contentChangeThreshold) || (isContentThresholdFiftyCharacters == true && contentDifference >= 50)) {
							targetUrlRequiredAlertSet.add(pageContentChangeEntity.getTargetUrlId());
							changeType = pageContentChangeEntity.getChangeType();
							// when change type is for additional content
							if (changeType == PageContentChangeUpdateCommand.CHANGE_TYPE_ADDITIONAL_CONTENT) {
								selectorTypeText = CrawlerUtils.getInstance().getSelectorTypeText(pageContentChangeEntity.getSelectorType());
								if (StringUtils.isBlank(selectorTypeText)) {
									FormatUtils.getInstance().logMemoryUsage("process() error--ip=" + ip + ",selectorTypeText cannot be determined for selectorType="
											+ pageContentChangeEntity.getSelectorType() + ",pageContentChangeEntity=" + pageContentChangeEntity.toString());
									continue nextPageContentChangeEntity;
								}
								mapKey = IConstants.SELECTOR_TYPE_COLON + selectorTypeText + IConstants.SELECTOR_COLON + pageContentChangeEntity.getSelector();
								if (pageTagContentChangeResults.getAdditionalContentUrlIdListMap().containsKey(mapKey)) {
									changeTypeUrlIdList = pageTagContentChangeResults.getAdditionalContentUrlIdListMap().get(mapKey);
								} else {
									changeTypeUrlIdList = new ArrayList<Long>();
								}
								changeTypeUrlIdList.add(pageContentChangeEntity.getTargetUrlId());
								pageTagContentChangeResults.getAdditionalContentUrlIdListMap().put(mapKey, changeTypeUrlIdList);
							}
							// when change type is not for additional content
							else {
								if (pageTagContentChangeResults.getChangeTypeUrlIdListMap().containsKey(changeType)) {
									changeTypeUrlIdList = pageTagContentChangeResults.getChangeTypeUrlIdListMap().get(changeType);
								} else {
									changeTypeUrlIdList = new ArrayList<Long>();
								}
								changeTypeUrlIdList.add(pageContentChangeEntity.getTargetUrlId());
								pageTagContentChangeResults.getChangeTypeUrlIdListMap().put(changeType, changeTypeUrlIdList);
							}
						}
					}
				}

				// calculate percent of target URLs in group tag that requires alert
				if (targetUrlRequiredAlertSet != null && targetUrlRequiredAlertSet.size() > 0) {
					totalTargetUrlsRequireAlert = targetUrlRequiredAlertSet.size();
					totalTargetUrlsInGroupTagDecimal = new BigDecimal(totalTargetUrlsInGroupTag).setScale(8, RoundingMode.HALF_UP);
					totalTargetUrlsRequireAlertDecimal = new BigDecimal(totalTargetUrlsRequireAlert).setScale(8, RoundingMode.HALF_UP);
					fractionOfTargetUrlsChanged = totalTargetUrlsRequireAlertDecimal.divide(totalTargetUrlsInGroupTagDecimal, RoundingMode.HALF_UP).setScale(8,
							RoundingMode.HALF_UP);
					percentOfTargetUrlsRequireAlert = fractionOfTargetUrlsChanged.multiply(DECIMAL_100).setScale(8, RoundingMode.HALF_UP);
				} else {
					totalTargetUrlsRequireAlert = 0;
					percentOfTargetUrlsRequireAlert = new BigDecimal(0);
				}
				pageTagContentChangeResults.setTotalTargetUrlsRequireAlert(totalTargetUrlsRequireAlert);
				pageTagContentChangeResults.setPercentOfTargetUrlsRequireAlert(percentOfTargetUrlsRequireAlert);

				FormatUtils.getInstance()
						.logMemoryUsage("process()  ip=" + ip + ",group tag ID=" + pageTagContentChangeResults.getGroupTagId() + ",group tag name="
								+ pageTagContentChangeResults.getGroupTagName() + ",number of target URLs in group tag="
								+ pageTagContentChangeResults.getTotalTargetUrlsInGroupTag() + ",number of target URLs require alert="
								+ pageTagContentChangeResults.getTotalTargetUrlsRequireAlert() + ",percent of target URLs require alert="
								+ pageTagContentChangeResults.getPercentOfTargetUrlsRequireAlert());

				// when percent of target URLs require alert exceeds tag level change threshold
				if (pageTagContentChangeResults.getPercentOfTargetUrlsRequireAlert().compareTo(tagLevelChangeThreshold) > 0) {
					pageTagAlertEmailValueObject = new PageTagAlertEmailValueObject();
					responseCodeChangeEmailAlertList = new ArrayList<PageTagResponseCodeChangeAlertEmailValueObject>();
					contentChangeEmailAlertList = new ArrayList<PageTagContentChangeAlertEmailValueObject>();
					additionalContentChangeEmailAlertList = new ArrayList<PageTagAdditionalContentChangeAlertEmailValueObject>();
					pageTagAlertEmailValueObject.setGroupTagId(pageTagContentChangeResults.getGroupTagId());
					pageTagAlertEmailValueObject.setGroupTagName(StringEscapeUtils.escapeHtml(pageTagContentChangeResults.getGroupTagName()));
					pageTagAlertEmailValueObject.setTotalTargetUrlsRequireAlert(pageTagContentChangeResults.getTotalTargetUrlsRequireAlert());
					pageTagAlertEmailValueObject.setTotalTargetUrlsInGroupTag(pageTagContentChangeResults.getTotalTargetUrlsInGroupTag());
					pageTagAlertEmailValueObject.setPercentTargetUrlsInGroupTag(
							pageTagContentChangeResults.getPercentOfTargetUrlsRequireAlert().setScale(2, RoundingMode.HALF_UP) + PERCENT);

					// when there are response code changes in group tag
					if (pageTagContentChangeResults.getResponseCodesUrlIdListMap() != null && pageTagContentChangeResults.getResponseCodesUrlIdListMap().size() > 0) {
						for (String tempResponseCodeChange : pageTagContentChangeResults.getResponseCodesUrlIdListMap().keySet()) {
							responseCodeUrlIdList = pageTagContentChangeResults.getResponseCodesUrlIdListMap().get(tempResponseCodeChange);
							pageTagResponseCodeChangeAlertEmailValueObject = new PageTagResponseCodeChangeAlertEmailValueObject();
							pageTagResponseCodeChangeAlertEmailValueObject.setGroupTagId(pageTagContentChangeResults.getGroupTagId());
							pageTagResponseCodeChangeAlertEmailValueObject.setProcessDateNumber(processDateNumber);
							pageTagResponseCodeChangeAlertEmailValueObject.setResponseCodeChange(tempResponseCodeChange);
							pageTagResponseCodeChangeAlertEmailValueObject.setTargetUrlIdList(responseCodeUrlIdList);
							pageTagResponseCodeChangeAlertEmailValueObject.setTotalTargetUrlsWithContentChanges(responseCodeUrlIdList.size());
							pageTagResponseCodeChangeAlertEmailValueObject.setTotalTargetUrlsInGroupTag(pageTagContentChangeResults.getTotalTargetUrlsInGroupTag());
							pageTagResponseCodeChangeAlertEmailValueObject.setPercentOfTargetUrlsWithContentChanges(
									pageTagContentChangeResults.getPercentOfTargetUrlsRequireAlert().setScale(2, RoundingMode.HALF_UP) + PERCENT);
							pageTagResponseCodeChangeAlertEmailValueObject.setChangeType(PageContentChangeUpdateCommand.CHANGE_TYPE_RESP_CODE);
							responseCodeChangeEmailAlertList.add(pageTagResponseCodeChangeAlertEmailValueObject);
						}
					}
					pageTagAlertEmailValueObject.setResponseCodeAlertEmailList(responseCodeChangeEmailAlertList);

					// when there are other changes (ie. title, meta desc, etc.) in group tag (but not additional content changes)
					if (pageTagContentChangeResults.getChangeTypeUrlIdListMap() != null && pageTagContentChangeResults.getChangeTypeUrlIdListMap().size() > 0) {
						for (Integer tempChangeType : pageTagContentChangeResults.getChangeTypeUrlIdListMap().keySet()) {
							changeTypeUrlIdList = pageTagContentChangeResults.getChangeTypeUrlIdListMap().get(tempChangeType);
							pageTagContentChangeAlertEmailValueObject = new PageTagContentChangeAlertEmailValueObject();
							pageTagContentChangeAlertEmailValueObject.setGroupTagId(pageTagContentChangeResults.getGroupTagId());
							pageTagContentChangeAlertEmailValueObject.setProcessDateNumber(processDateNumber);
							pageTagContentChangeAlertEmailValueObject.setAlertName(getAlertNameForChangeType(tempChangeType));
							pageTagContentChangeAlertEmailValueObject.setTargetUrlIdList(changeTypeUrlIdList);
							pageTagContentChangeAlertEmailValueObject.setTotalTargetUrlsWithContentChanges(changeTypeUrlIdList.size());
							pageTagContentChangeAlertEmailValueObject.setTotalTargetUrlsInGroupTag(pageTagContentChangeResults.getTotalTargetUrlsInGroupTag());
							pageTagContentChangeAlertEmailValueObject.setPercentOfTargetUrlsWithContentChanges(
									pageTagContentChangeResults.getPercentOfTargetUrlsRequireAlert().setScale(2, RoundingMode.HALF_UP) + PERCENT);
							pageTagContentChangeAlertEmailValueObject.setChangeType(tempChangeType);
							contentChangeEmailAlertList.add(pageTagContentChangeAlertEmailValueObject);
						}
					}
					pageTagAlertEmailValueObject.setContentChangeAlertEmailList(contentChangeEmailAlertList);

					// when there are additional content changes in group tag
					if (pageTagContentChangeResults.getAdditionalContentUrlIdListMap() != null
							&& pageTagContentChangeResults.getAdditionalContentUrlIdListMap().size() > 0) {
						for (String tempAdditionalContentChange : pageTagContentChangeResults.getAdditionalContentUrlIdListMap().keySet()) {
							additionalContentUrlIdList = pageTagContentChangeResults.getAdditionalContentUrlIdListMap().get(tempAdditionalContentChange);
							pageTagAdditionalContentChangeAlertEmailValueObject = new PageTagAdditionalContentChangeAlertEmailValueObject();
							pageTagAdditionalContentChangeAlertEmailValueObject.setGroupTagId(pageTagContentChangeResults.getGroupTagId());
							pageTagAdditionalContentChangeAlertEmailValueObject.setProcessDateNumber(processDateNumber);
							pageTagAdditionalContentChangeAlertEmailValueObject.setAdditionalContentChange(tempAdditionalContentChange);
							pageTagAdditionalContentChangeAlertEmailValueObject.setTargetUrlIdList(additionalContentUrlIdList);
							pageTagAdditionalContentChangeAlertEmailValueObject.setTotalTargetUrlsWithContentChanges(additionalContentUrlIdList.size());
							pageTagAdditionalContentChangeAlertEmailValueObject
									.setTotalTargetUrlsInGroupTag(pageTagContentChangeResults.getTotalTargetUrlsInGroupTag());
							pageTagAdditionalContentChangeAlertEmailValueObject.setPercentOfTargetUrlsWithContentChanges(
									pageTagContentChangeResults.getPercentOfTargetUrlsRequireAlert().setScale(2, RoundingMode.HALF_UP) + PERCENT);
							pageTagAdditionalContentChangeAlertEmailValueObject.setChangeType(PageContentChangeUpdateCommand.CHANGE_TYPE_ADDITIONAL_CONTENT);
							additionalContentChangeEmailAlertList.add(pageTagAdditionalContentChangeAlertEmailValueObject);
						}
					}
					pageTagAlertEmailValueObject.setAdditionalContentChangeAlertEmailList(additionalContentChangeEmailAlertList);

					pageTagAlertEmailValueObjectList.add(pageTagAlertEmailValueObject);
				}
			}
		}

		// when there are alerts to be sent....
		if (pageTagAlertEmailValueObjectList != null && pageTagAlertEmailValueObjectList.size() > 0) {

			// persist the alert data in the 'page_tag_content_change' MySQL table
			updateDatabase(pageTagAlertEmailValueObjectList);

			if (StringUtils.isNotBlank(pageTagAlertConfigEntity.getTagContentChangeAlertEmailAddresses())) {
				emailAddressArray = StringUtils.split(pageTagAlertConfigEntity.getTagContentChangeAlertEmailAddresses(), IConstants.COMMA);
				if (emailAddressArray != null && emailAddressArray.length > 0) {
					sendEmailAlert(contentChangeThreshold, tagLevelChangeThreshold, emailAddressArray, pageTagAlertEmailValueObjectList, ownDomainEntity);
				}
				try {
					sendZapierAlert(contentChangeThreshold, tagLevelChangeThreshold, pageTagAlertEmailValueObjectList, ownDomainEntity);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}

		CacheModleFactory.getInstance().setAliveIpAddress(ip);

		FormatUtils.getInstance().logMemoryUsage("process() ends. ip=" + ip + ",domainId=" + pageTagAlertConfigEntity.getDomainId() + ",createdByUserId="
				+ pageTagAlertConfigEntity.getCreatedByUserId() + ",dataContentChangeThreshold=" + pageTagAlertConfigEntity.getDataContentChangeThreshold()
				+ ",tagContentChangeThreshold=" + pageTagAlertConfigEntity.getTagContentChangeThreshold() + ",tagContentChangeThresholdPercent="
				+ pageTagAlertConfigEntity.getTagContentChangeThresholdPercent() + ",tagContentChangeTagIds=" + pageTagAlertConfigEntity.getTagContentChangeTagIds()
				+ ",tagContentChangeAlertEmailAddresses=" + pageTagAlertConfigEntity.getTagContentChangeAlertEmailAddresses() + ",elapsed time in sec.="
				+ (System.currentTimeMillis() - startTimestamp) / 1000);
	}

	private List<Integer> getAlertGroupTagIdList(int domainId, int alertId, String tagContentChangeTagIds) {
		List<Integer> alertGroupTagIdList = null;
		String[] groupTagIdStringArray = null;
		if (StringUtils.isNotBlank(tagContentChangeTagIds)) {
			groupTagIdStringArray = StringUtils.split(tagContentChangeTagIds, IConstants.COMMA);
			if (groupTagIdStringArray != null && groupTagIdStringArray.length > 0) {
				alertGroupTagIdList = new ArrayList<Integer>();
				for (int i = 0; i < groupTagIdStringArray.length; i++) {
					alertGroupTagIdList.add(NumberUtils.toInt(groupTagIdStringArray[i]));
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("getAlertGroupTagIdList() ip=" + ip + ",alertId=" + alertId + ",domainId=" + domainId + ",tagContentChangeTagIds="
				+ tagContentChangeTagIds + ",alertGroupTagIdList=" + alertGroupTagIdList);
		return alertGroupTagIdList;
	}

	// convert alert metric types in page tag alert configuration to change types
	private List<Integer> getChangeTypeList(int domainId, int alertId, String alertMetricTypes) {
		List<Integer> changeTypeList = new ArrayList<Integer>();

		if (StringUtils.isNotBlank(alertMetricTypes)) {
			String[] alertMetricTypeArray = StringUtils.split(alertMetricTypes, IConstants.COMMA);
			int alertMetricType = 0;
			for (int i = 0; i < alertMetricTypeArray.length; i++) {
				alertMetricType = NumberUtils.toInt(alertMetricTypeArray[i]);
				// title
				if (alertMetricType == ALERT_METRIC_TYPE_TITLE) {
					changeTypeList.add(PageContentChangeUpdateCommand.CHANGE_TYPE_TITLE);
				}
				// meta desc
				else if (alertMetricType == ALERT_METRIC_TYPE_META_DESC) {
					changeTypeList.add(PageContentChangeUpdateCommand.CHANGE_TYPE_META_DESC);
				}
				// number of h1
				else if (alertMetricType == ALERT_METRIC_TYPE_NUMBER_OF_H1) {
					changeTypeList.add(PageContentChangeUpdateCommand.CHANGE_TYPE_NUMBER_OF_H1);
				}
				// content of h1
				else if (alertMetricType == ALERT_METRIC_TYPE_CONTENT_OF_H1) {
					changeTypeList.add(PageContentChangeUpdateCommand.CHANGE_TYPE_CONTENT_OF_H1);
				}
				// number of h2
				else if (alertMetricType == ALERT_METRIC_TYPE_NUMBER_OF_H2) {
					changeTypeList.add(PageContentChangeUpdateCommand.CHANGE_TYPE_NUMBER_OF_H2);
				}
				// content of h2
				else if (alertMetricType == ALERT_METRIC_TYPE_CONTENT_OF_H2) {
					changeTypeList.add(PageContentChangeUpdateCommand.CHANGE_TYPE_CONTENT_OF_H2);
				}
				// response code
				else if (alertMetricType == ALERT_METRIC_TYPE_RESP_CODE) {
					changeTypeList.add(PageContentChangeUpdateCommand.CHANGE_TYPE_RESP_CODE);
				}
				// meta robots
				else if (alertMetricType == ALERT_METRIC_TYPE_META_ROBOTS) {
					changeTypeList.add(PageContentChangeUpdateCommand.CHANGE_TYPE_META_ROBOTS);
				}
				// canonical URL
				else if (alertMetricType == ALERT_METRIC_TYPE_CANONICAL_URL) {
					changeTypeList.add(PageContentChangeUpdateCommand.CHANGE_TYPE_CANONICAL_URL);
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("getChangeTypeList() ip=" + ip + ",alertId=" + alertId + ",domainId=" + domainId + ",alertMetricTypes="
				+ alertMetricTypes + ",changeTypeList=" + changeTypeList.toString());
		return changeTypeList;
	}

	private String getAlertNameForChangeType(int changeType) {
		String output = null;
		if (changeType == PageContentChangeUpdateCommand.CHANGE_TYPE_TITLE) {
			output = ALERT_METRIC_TITLE;
		} else if (changeType == PageContentChangeUpdateCommand.CHANGE_TYPE_META_DESC) {
			output = ALERT_METRIC_META_DESC;
		} else if (changeType == PageContentChangeUpdateCommand.CHANGE_TYPE_NUMBER_OF_H1) {
			output = ALERT_METRIC_NUMBER_OF_H1;
		} else if (changeType == PageContentChangeUpdateCommand.CHANGE_TYPE_CONTENT_OF_H1) {
			output = ALERT_METRIC_CONTENT_OF_H1;
		} else if (changeType == PageContentChangeUpdateCommand.CHANGE_TYPE_NUMBER_OF_H2) {
			output = ALERT_METRIC_NUMBER_OF_H2;
		} else if (changeType == PageContentChangeUpdateCommand.CHANGE_TYPE_CONTENT_OF_H2) {
			output = ALERT_METRIC_CONTENT_OF_H2;
		} else if (changeType == PageContentChangeUpdateCommand.CHANGE_TYPE_RESP_CODE) {
			output = ALERT_METRIC_RESP_CODE;
		} else if (changeType == PageContentChangeUpdateCommand.CHANGE_TYPE_META_ROBOTS) {
			output = ALERT_METRIC_META_ROBOTS;
		} else if (changeType == PageContentChangeUpdateCommand.CHANGE_TYPE_CANONICAL_URL) {
			output = ALERT_METRIC_CANONICAL_URL;
		}
		return output;
	}

	@Override
	protected void undo() throws Exception {
		// TODO Auto-generated method stub

	}

	private void updateDatabase(List<PageTagAlertEmailValueObject> pageTagAlertEmailValueObjectList) {
		long startTimestamp = System.currentTimeMillis();
		int totalResponseCodeChangeAlerts = 0;
		int totalOtherContentChangeAlerts = 0;
		PageTagContentChangeEntity pageTagContentChangeEntityExisting = null;
		FormatUtils.getInstance().logMemoryUsage("updateDatabase() ip=" + ip + ",pageTagAlertEmailValueObjectList.size()=" + pageTagAlertEmailValueObjectList.size());
		PageTagContentChangeEntity pageTagContentChangeEntity = null;
		List<PageTagContentChangeEntity> pageTagContentChangeEntityToBeCreatedList = new ArrayList<PageTagContentChangeEntity>();
		for (PageTagAlertEmailValueObject pageTagAlertEmailValueObject : pageTagAlertEmailValueObjectList) {

			// for response code change alerts
			if (pageTagAlertEmailValueObject.getResponseCodeAlertEmailList() != null && pageTagAlertEmailValueObject.getResponseCodeAlertEmailList().size() > 0) {
				for (PageTagResponseCodeChangeAlertEmailValueObject pageTagResponseCodeChangeAlertEmailValueObject : pageTagAlertEmailValueObject
						.getResponseCodeAlertEmailList()) {
					for (Long targetUrlId : pageTagResponseCodeChangeAlertEmailValueObject.getTargetUrlIdList()) {
						pageTagContentChangeEntity = new PageTagContentChangeEntity();
						pageTagContentChangeEntity.setDomainId(domainId);
						pageTagContentChangeEntity.setGroupTagId(pageTagResponseCodeChangeAlertEmailValueObject.getGroupTagId());
						pageTagContentChangeEntity.setChangeDate(pageTagResponseCodeChangeAlertEmailValueObject.getProcessDateNumber());
						pageTagContentChangeEntity.setTargetUrlId(targetUrlId);
						pageTagContentChangeEntity.setChangeType(pageTagResponseCodeChangeAlertEmailValueObject.getChangeType());
						pageTagContentChangeEntity.setRespCodeChange(pageTagResponseCodeChangeAlertEmailValueObject.getResponseCodeChange());
						pageTagContentChangeEntity.setAdditionalContentChange(IConstants.EMPTY_STRING);
						pageTagContentChangeEntity.setDivContentChange(IConstants.EMPTY_STRING);
						pageTagContentChangeEntityExisting = pageTagContentChangeDAO.get(pageTagContentChangeEntity.getDomainId(),
								pageTagContentChangeEntity.getGroupTagId(), pageTagContentChangeEntity.getChangeDate(), pageTagContentChangeEntity.getChangeType(),
								pageTagContentChangeEntity.getTargetUrlId());
						if (pageTagContentChangeEntityExisting == null) {
							pageTagContentChangeEntityToBeCreatedList.add(pageTagContentChangeEntity);
							totalResponseCodeChangeAlerts++;
							if (pageTagContentChangeEntityToBeCreatedList.size() >= MAX_NUMBER_OF_DB_RECORDS_PER_CREATION) {
								pageTagContentChangeDAO.insertMultiRowsBatch(pageTagContentChangeEntityToBeCreatedList);
								pageTagContentChangeEntityToBeCreatedList = new ArrayList<PageTagContentChangeEntity>();
							}
						}
					}
				}
			}

			// for additional content change alerts
			if (pageTagAlertEmailValueObject.getAdditionalContentChangeAlertEmailList() != null
					&& pageTagAlertEmailValueObject.getAdditionalContentChangeAlertEmailList().size() > 0) {
				for (PageTagAdditionalContentChangeAlertEmailValueObject pageTagAdditionalContentChangeAlertEmailValueObject : pageTagAlertEmailValueObject
						.getAdditionalContentChangeAlertEmailList()) {
					for (Long targetUrlId : pageTagAdditionalContentChangeAlertEmailValueObject.getTargetUrlIdList()) {
						pageTagContentChangeEntity = new PageTagContentChangeEntity();
						pageTagContentChangeEntity.setDomainId(domainId);
						pageTagContentChangeEntity.setGroupTagId(pageTagAdditionalContentChangeAlertEmailValueObject.getGroupTagId());
						pageTagContentChangeEntity.setChangeDate(pageTagAdditionalContentChangeAlertEmailValueObject.getProcessDateNumber());
						pageTagContentChangeEntity.setTargetUrlId(targetUrlId);
						pageTagContentChangeEntity.setChangeType(pageTagAdditionalContentChangeAlertEmailValueObject.getChangeType());
						pageTagContentChangeEntity.setAdditionalContentChange(pageTagAdditionalContentChangeAlertEmailValueObject.getAdditionalContentChange());
						pageTagContentChangeEntity.setDivContentChange(IConstants.EMPTY_STRING);
						pageTagContentChangeEntityExisting = pageTagContentChangeDAO.getAdditionalContentChange(pageTagContentChangeEntity.getDomainId(),
								pageTagContentChangeEntity.getGroupTagId(), pageTagContentChangeEntity.getChangeDate(), pageTagContentChangeEntity.getChangeType(),
								pageTagContentChangeEntity.getTargetUrlId(), pageTagContentChangeEntity.getAdditionalContentChange());
						if (pageTagContentChangeEntityExisting == null) {
							pageTagContentChangeEntityToBeCreatedList.add(pageTagContentChangeEntity);
							totalOtherContentChangeAlerts++;
							if (pageTagContentChangeEntityToBeCreatedList.size() >= MAX_NUMBER_OF_DB_RECORDS_PER_CREATION) {
								pageTagContentChangeDAO.insertMultiRowsBatch(pageTagContentChangeEntityToBeCreatedList);
								pageTagContentChangeEntityToBeCreatedList = new ArrayList<PageTagContentChangeEntity>();
							}
						}
					}
				}
			}

			// for other content change alerts
			if (pageTagAlertEmailValueObject.getContentChangeAlertEmailList() != null && pageTagAlertEmailValueObject.getContentChangeAlertEmailList().size() > 0) {
				for (PageTagContentChangeAlertEmailValueObject pageTagContentChangeAlertEmailValueObject : pageTagAlertEmailValueObject
						.getContentChangeAlertEmailList()) {
					for (Long targetUrlId : pageTagContentChangeAlertEmailValueObject.getTargetUrlIdList()) {
						pageTagContentChangeEntity = new PageTagContentChangeEntity();
						pageTagContentChangeEntity.setDomainId(domainId);
						pageTagContentChangeEntity.setGroupTagId(pageTagContentChangeAlertEmailValueObject.getGroupTagId());
						pageTagContentChangeEntity.setChangeDate(pageTagContentChangeAlertEmailValueObject.getProcessDateNumber());
						pageTagContentChangeEntity.setTargetUrlId(targetUrlId);
						pageTagContentChangeEntity.setChangeType(pageTagContentChangeAlertEmailValueObject.getChangeType());
						pageTagContentChangeEntity.setRespCodeChange(null);
						pageTagContentChangeEntity.setAdditionalContentChange(IConstants.EMPTY_STRING);
						pageTagContentChangeEntity.setDivContentChange(IConstants.EMPTY_STRING);
						pageTagContentChangeEntityExisting = pageTagContentChangeDAO.get(pageTagContentChangeEntity.getDomainId(),
								pageTagContentChangeEntity.getGroupTagId(), pageTagContentChangeEntity.getChangeDate(), pageTagContentChangeEntity.getChangeType(),
								pageTagContentChangeEntity.getTargetUrlId());
						if (pageTagContentChangeEntityExisting == null) {
							pageTagContentChangeEntityToBeCreatedList.add(pageTagContentChangeEntity);
							totalOtherContentChangeAlerts++;
							if (pageTagContentChangeEntityToBeCreatedList.size() >= MAX_NUMBER_OF_DB_RECORDS_PER_CREATION) {
								pageTagContentChangeDAO.insertMultiRowsBatch(pageTagContentChangeEntityToBeCreatedList);
								pageTagContentChangeEntityToBeCreatedList = new ArrayList<PageTagContentChangeEntity>();
							}
						}
					}
				}
			}
		}
		if (pageTagContentChangeEntityToBeCreatedList.size() > 0) {
			pageTagContentChangeDAO.insertMultiRowsBatch(pageTagContentChangeEntityToBeCreatedList);
			pageTagContentChangeEntityToBeCreatedList = new ArrayList<PageTagContentChangeEntity>();
		}
		FormatUtils.getInstance().logMemoryUsage("updateDatabase() ip=" + ip + ",totalOtherContentChangeAlerts=" + totalOtherContentChangeAlerts
				+ ",totalResponseCodeChangeAlerts=" + totalResponseCodeChangeAlerts + ",elapsed (ms.)=" + (System.currentTimeMillis() - startTimestamp));
	}

	private void sendEmailAlert(int contentChangeThreshold, BigDecimal tagLevelChangeThreshold, String[] emailAddressArray,
			List<PageTagAlertEmailValueObject> pageTagAlertEmailValueObjectList, OwnDomainEntity ownDomainEntity) throws Exception {
		for (int i = 0; i < emailAddressArray.length; i++) {
			FormatUtils.getInstance().logMemoryUsage("sendEmailAlert() ip=" + ip + ",emailAddress[" + i + "]=" + emailAddressArray[i]);
		}

		int retryCount = 0;

		for (PageTagAlertEmailValueObject pageTagAlertEmailValueObject : pageTagAlertEmailValueObjectList) {
			FormatUtils.getInstance()
					.logMemoryUsage("sendEmailAlert() ip=" + ip + ",pageTagAlertEmailValueObject.getGroupTagId()=" + pageTagAlertEmailValueObject.getGroupTagId());
			FormatUtils.getInstance()
					.logMemoryUsage("sendEmailAlert() ip=" + ip + ",pageTagAlertEmailValueObject.getGroupTagName()=" + pageTagAlertEmailValueObject.getGroupTagName());
			if (pageTagAlertEmailValueObject.getResponseCodeAlertEmailList() != null && pageTagAlertEmailValueObject.getResponseCodeAlertEmailList().size() > 0) {
				for (PageTagResponseCodeChangeAlertEmailValueObject responseCodeChangeAlertEmail : pageTagAlertEmailValueObject.getResponseCodeAlertEmailList()) {
					FormatUtils.getInstance().logMemoryUsage("sendEmailAlert() ip=" + ip + ",responseCodeChangeAlertEmail=" + responseCodeChangeAlertEmail.toString());
				}
			}
			if (pageTagAlertEmailValueObject.getContentChangeAlertEmailList() != null && pageTagAlertEmailValueObject.getContentChangeAlertEmailList().size() > 0) {
				for (PageTagContentChangeAlertEmailValueObject contentCodeChangeAlertEmail : pageTagAlertEmailValueObject.getContentChangeAlertEmailList()) {
					FormatUtils.getInstance().logMemoryUsage("sendEmailAlert() ip=" + ip + ",contentCodeChangeAlertEmail=" + contentCodeChangeAlertEmail.toString());
				}
			}
			if (pageTagAlertEmailValueObject.getAdditionalContentChangeAlertEmailList() != null
					&& pageTagAlertEmailValueObject.getAdditionalContentChangeAlertEmailList().size() > 0) {
				for (PageTagAdditionalContentChangeAlertEmailValueObject additionalContentChangeAlertEmail : pageTagAlertEmailValueObject
						.getAdditionalContentChangeAlertEmailList()) {
					FormatUtils.getInstance()
							.logMemoryUsage("sendEmailAlert() ip=" + ip + ",additionalContentChangeAlertEmail=" + additionalContentChangeAlertEmail.toString());
				}
			}
		}
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("pageTagAlertEmailValueObjectList", pageTagAlertEmailValueObjectList);
		map.put("domainId", ownDomainEntity.getId());
		map.put("ownDomain", ownDomainEntity);
		map.put("processDate", processDateString);
		map.put("processDateMMDDYY", DateFormatUtils.format(processDate, DATE_FORMAT_MMDDYY));
		map.put("contentChangeThreshold", contentChangeThreshold);
		String tagLevelChangeThresholdString = String.valueOf(tagLevelChangeThreshold.setScale(2, RoundingMode.HALF_UP));
		map.put("tagLevelChangeThreshold", tagLevelChangeThresholdString);
		String subject = EMAIL_SUBJECT + processDateString + DASH + ownDomainEntity.getDomain() + " (ID:" + ownDomainEntity.getId() + ")";
		AgencyInfoEntity agencyInfoEntity = agencyInfoService.getByDomainId(ownDomainEntity.getId());

		while (retryCount < IConstants.MAX_SEND_EMAIL_RETRY_COUNT) {
			try {
				emailSenderComponent.sendMimeMultiPartZeptoMailAndBcc(emailAddressArray, null, subject, "mail_page_tag_content_change_report_v2.txt",
						"mail_page_tag_content_change_report_v2.html", map, agencyInfoEntity);
				retryCount = IConstants.MAX_SEND_EMAIL_RETRY_COUNT;
			} catch (Exception e) {
				e.printStackTrace();
				retryCount++;
				if (retryCount >= IConstants.MAX_SEND_EMAIL_RETRY_COUNT) {
					throw e;
				} else {
					FormatUtils.getInstance()
							.logMemoryUsage("sendEmailAlert() ip=" + ip + ",retryCount=" + retryCount + ",number of emailAddresses=" + emailAddressArray.length);
					try {
						Thread.sleep(IConstants.RETRY_WAIT_TIME_IN_MILLISECONDS);
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("sendEmailAlert() ip=" + ip + ",number of emailAddresses=" + emailAddressArray.length);
	}

	private void sendZapierAlert(int contentChangeThreshold, BigDecimal tagLevelChangeThreshold, List<PageTagAlertEmailValueObject> pageTagAlertEmailValueObjectList,
			OwnDomainEntity ownDomainEntity) throws Exception {
		FormatUtils.getInstance().logMemoryUsage("sendZapierAlert() ip=" + ip + ",begins.");
		ZapierPageTagContentAlert zapierPageTagContentAlert = null;
		long startingId = System.currentTimeMillis();
		String callbackUrl = null;
		String requestParameters = null;
		String responseString = null;
		int pageTagIdRequiresAlert = 0;
		boolean isSendGetRequest = false;

		//if (isDebug == true) {
		//	for (PageTagAlertEmailValueObject pageTagAlertEmailValueObject : pageTagAlertEmailValueObjectList) {
		//		zapierPageTagContentAlert = getZapierPageTagContentAlert(ownDomainEntity.getId(), startingId++, ownDomainEntity.getDomain(), contentChangeThreshold,
		//				tagLevelChangeThreshold, pageTagAlertEmailValueObject);
		//		if (zapierPageTagContentAlert != null) {
		//			requestParameters = new Gson().toJson(zapierPageTagContentAlert, ZapierPageTagContentAlert.class);
		//			FormatUtils.getInstance().logMemoryUsage("sendZapierAlert() ip=" + ip + ",requestParameters=" + requestParameters);
		//		}
		//	}
		//}

		List<ZapierWebhookEntity> zapierWebhookEntityList = zapierWebhookDAO.getByDomainIdTriggerType(domainId, IConstants.ZAPIER_TRIGGER_TYPE_PAGE_TAG_CONTENT_ALERT);
		if (zapierWebhookEntityList != null && zapierWebhookEntityList.size() > 0) {
			nextZapierWebhookEntity: for (ZapierWebhookEntity zapierWebhookEntity : zapierWebhookEntityList) {

				// when the user is no longer active in the domain
				if (ContentGuardUtils.getInstance().isUserActiveInDomain(zapierWebhookEntity.getUserId(), zapierWebhookEntity.getDomainId()) == false) {
					FormatUtils.getInstance().logMemoryUsage(
							"sendZapierAlert() skipped, user ID no longer active. ip=" + ip + ",domainId=" + domainId + ",userId=" + zapierWebhookEntity.getUserId());
					continue nextZapierWebhookEntity;
				}

				FormatUtils.getInstance().logMemoryUsage("sendZapierAlert() ip=" + ip + ",processing zapierWebhookEntity=" + zapierWebhookEntity.toString());
				pageTagIdRequiresAlert = NumberUtils.toInt(zapierWebhookEntity.getSubType());

				for (PageTagAlertEmailValueObject pageTagAlertEmailValueObject : pageTagAlertEmailValueObjectList) {
					if (pageTagAlertEmailValueObject.getGroupTagId() == pageTagIdRequiresAlert) {
						callbackUrl = zapierWebhookEntity.getCallbackUrl();
						FormatUtils.getInstance()
								.logMemoryUsage("sendZapierAlert() ip=" + ip + ",processing pageTagAlertEmailValueObject=" + pageTagAlertEmailValueObject.toString());
						zapierPageTagContentAlert = getZapierPageTagContentAlert(ownDomainEntity.getId(), startingId++, ownDomainEntity.getDomain(),
								contentChangeThreshold, tagLevelChangeThreshold, pageTagAlertEmailValueObject);
						if (zapierPageTagContentAlert != null) {
							requestParameters = new Gson().toJson(zapierPageTagContentAlert, ZapierPageTagContentAlert.class);
							//if (isDebug == true) {
							//	System.out.println("sendZapierAlert() ip=" + ip + ",domainId=" + domainId + ",pageTagId=" + pageTagIdRequiresAlert
							//			+ ",requestParameters=" + requestParameters);
							//}
							try {
								responseString = HttpUtils.getInstance().getResponseString(callbackUrl, isSendGetRequest, requestParameters);
								//if (isDebug == true) {
								//	System.out.println("sendZapierAlert() ip=" + ip + ",domainId=" + domainId + ",pageTagId=" + pageTagIdRequiresAlert
								//			+ ",responseString=" + responseString);
								//}
							} catch (Exception e) {
								// re-throw exception when not 'httpStatusCode=410,httpReasonPhrase=Gone'
								if (StringUtils.containsIgnoreCase(e.getMessage(), "httpStatusCode=410,httpReasonPhrase=Gone") == false) {
									throw e;
								}
							}
						}
					}
				}
			}
		}
		FormatUtils.getInstance().logMemoryUsage("sendZapierAlert() ip=" + ip + ",ends.");
	}

	private ZapierPageTagContentAlert getZapierPageTagContentAlert(int domainId, Long id, String domainName, int contentChangeThreshold,
			BigDecimal tagLevelChangeThreshold, PageTagAlertEmailValueObject pageTagAlertEmailValueObject) throws Exception {
		ZapierPageTagContentAlert zapierPageTagContentAlert = new ZapierPageTagContentAlert();
		zapierPageTagContentAlert.setId(String.valueOf(id));
		zapierPageTagContentAlert.setAlert_timestamp(DateFormatUtils.format(new Date(), IConstants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS));
		zapierPageTagContentAlert.setDomain_id(domainId);
		zapierPageTagContentAlert.setDomain_name(domainName);
		zapierPageTagContentAlert.setPage_tag_id(pageTagAlertEmailValueObject.getGroupTagId());
		zapierPageTagContentAlert.setPage_tag_name(pageTagAlertEmailValueObject.getGroupTagName());
		zapierPageTagContentAlert.setContent_change_date(processDateString);
		if (contentChangeThreshold == 1) {
			zapierPageTagContentAlert.setContent_change_threshold("1 character");
		} else {
			zapierPageTagContentAlert.setContent_change_threshold(contentChangeThreshold + " characters");
		}
		String tagLevelChangeThresholdString = String.valueOf(tagLevelChangeThreshold.setScale(2, RoundingMode.HALF_UP));
		zapierPageTagContentAlert.setPage_tag_level_change_threshold(tagLevelChangeThresholdString);
		zapierPageTagContentAlert.setTotal_urls_changed(pageTagAlertEmailValueObject.getTotalTargetUrlsRequireAlert());
		zapierPageTagContentAlert.setTotal_urls_in_tag(pageTagAlertEmailValueObject.getTotalTargetUrlsInGroupTag());
		zapierPageTagContentAlert.setPercent_of_urls_changed(pageTagAlertEmailValueObject.getPercentTargetUrlsInGroupTag());
		StringBuilder stringBuilder = null;
		if (pageTagAlertEmailValueObject.getContentChangeAlertEmailList() != null && pageTagAlertEmailValueObject.getContentChangeAlertEmailList().size() > 0) {
			for (PageTagContentChangeAlertEmailValueObject pageTagContentChangeAlertEmailValueObject : pageTagAlertEmailValueObject.getContentChangeAlertEmailList()) {
				if (stringBuilder == null) {
					stringBuilder = new StringBuilder();
				} else {
					stringBuilder.append(IConstants.COMMA);
				}
				stringBuilder.append(pageTagContentChangeAlertEmailValueObject.getTotalTargetUrlsWithContentChanges() + " URLs with '"
						+ pageTagContentChangeAlertEmailValueObject.getAlertName() + "' changes");
			}
		}
		if (pageTagAlertEmailValueObject.getResponseCodeAlertEmailList() != null && pageTagAlertEmailValueObject.getResponseCodeAlertEmailList().size() > 0) {
			for (PageTagResponseCodeChangeAlertEmailValueObject pageTagResponseCodeChangeAlertEmailValueObject : pageTagAlertEmailValueObject
					.getResponseCodeAlertEmailList()) {
				if (stringBuilder == null) {
					stringBuilder = new StringBuilder();
				} else {
					stringBuilder.append(IConstants.COMMA);
				}
				stringBuilder.append(pageTagResponseCodeChangeAlertEmailValueObject.getTotalTargetUrlsWithContentChanges() + " URLs with '"
						+ pageTagResponseCodeChangeAlertEmailValueObject.getResponseCodeChange() + "' changes");
			}
		}
		if (pageTagAlertEmailValueObject.getAdditionalContentChangeAlertEmailList() != null
				&& pageTagAlertEmailValueObject.getAdditionalContentChangeAlertEmailList().size() > 0) {
			for (PageTagAdditionalContentChangeAlertEmailValueObject pageTagAdditionalContentChangeAlertEmailValueObject : pageTagAlertEmailValueObject
					.getAdditionalContentChangeAlertEmailList()) {
				if (stringBuilder == null) {
					stringBuilder = new StringBuilder();
				} else {
					stringBuilder.append(IConstants.COMMA);
				}
				stringBuilder.append(pageTagAdditionalContentChangeAlertEmailValueObject.getTotalTargetUrlsWithContentChanges() + " URLs with '"
						+ pageTagAdditionalContentChangeAlertEmailValueObject.getAdditionalContentChange() + "' changes");
			}
		}

		zapierPageTagContentAlert.setChanges("6 URLs with 'Title' changes, 5 URLs with 'Meta Desc' changes");
		return zapierPageTagContentAlert;
	}

	//	private void maintainSpecialHotelPropertyPagesTag() {
	//		boolean isRequiredSpecialPageTag = false;
	//		for (int i = 0; i < specialPageTagDomainArray.length; i++) {
	//			if (domainId == specialPageTagDomainArray[i]) {
	//				isRequiredSpecialPageTag = true;
	//				break;
	//			}
	//		}
	//		FormatUtils.getInstance().logMemoryUsage("maintainSpecialHotelPropertyPagesTag() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
	//				+ ",isRequiredSpecialPageTag=" + isRequiredSpecialPageTag);
	//		if (isRequiredSpecialPageTag == false) {
	//			return;
	//		}
	//
	//		// maintain a special page group tag for the target URLs that meet the following criteria:
	//		// 1) the page starts with "http(s)://DOMAIN/hotel/"
	//		// 2) the page ends with ".h" + one or more numbers of digits + optionally ends with "/"
	//
	//		// page tag name "Custom Content Change Alerts"
	//
	//		GroupTagRelationEntity groupTagRelationEntityToBeCreated = null;
	//
	//		int groupTagId = 0;
	//
	//		GroupTagEntity groupTagEntity = groupTagEntityDAO.getGroupTagEntity(domainId, PAGE_TAG_NAME_HOTEL_PROP_PAGES_FOR_CHANGE_ALERTS,
	//				GroupTagEntity.TAG_TYPE_TARGET_PAGE);
	//		if (groupTagEntity == null) {
	//			groupTagEntity = new GroupTagEntity();
	//			groupTagEntity.setChanged(GroupTagEntity.TAG_CHANGED);
	//			groupTagEntity.setCreateDate(DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH));
	//			groupTagEntity.setDomainId(domainId);
	//			groupTagEntity.setIsGroupHierarchy(GroupTagEntity.NOT_HIERARCHY_TAG);
	//			groupTagEntity.setPrivateTag(GroupTagEntity.PUBLIC_TAG_FLG);
	//			groupTagEntity.setTagName(PAGE_TAG_NAME_HOTEL_PROP_PAGES_FOR_CHANGE_ALERTS);
	//			groupTagEntity.setTagType(GroupTagEntity.TAG_TYPE_TARGET_PAGE);
	//			groupTagEntity.setUpdateDate(groupTagEntity.getCreateDate());
	//			groupTagId = groupTagEntityDAO.insert(groupTagEntity);
	//		} else {
	//			groupTagId = groupTagEntity.getId();
	//		}
	//
	//		// determine existing resource ID(s) in special group tag "Custom Content Change Alerts"
	//		List<GroupTagRelationEntity> existingGroupTagRelationEntityList = groupTagRelationEntityDAO.getByGroupTagId(domainId, groupTagId);
	//		List<Long> existingTargetUrlIdList = new ArrayList<Long>();
	//		if (existingGroupTagRelationEntityList != null && existingGroupTagRelationEntityList.size() > 0) {
	//			for (GroupTagRelationEntity groupTagRelationEntity : existingGroupTagRelationEntityList) {
	//				existingTargetUrlIdList.add(groupTagRelationEntity.getResourceId());
	//			}
	//		}
	//		Collections.sort(existingTargetUrlIdList);
	//		String existingTargetUrlIdListString = existingTargetUrlIdList.toString();
	//		FormatUtils.getInstance().logMemoryUsage("maintainSpecialHotelPropertyPagesTag() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
	//				+ ",existingTargetUrlIdList before=" + existingTargetUrlIdListString);
	//
	//		// determine the required resource ID(s) in special group tag "Custom Content Change Alerts"
	//		List<TargetUrlEntity> requiredTargetUrlList = targetUrlEntityDAO.getTargetUrlIdsByGenericSearchPattern(domainId, HOTEL_URL_SQL_SRCH_PATTERN);
	//		List<Long> requiredTargetUrlIdList = new ArrayList<Long>();
	//		if (requiredTargetUrlList != null && requiredTargetUrlList.size() > 0) {
	//			for (TargetUrlEntity targetUrlEntity : requiredTargetUrlList) {
	//				requiredTargetUrlIdList.add(targetUrlEntity.getId());
	//			}
	//		}
	//		Collections.sort(requiredTargetUrlIdList);
	//		String requiredTargetUrlIdListString = requiredTargetUrlIdList.toString();
	//		FormatUtils.getInstance().logMemoryUsage("maintainSpecialHotelPropertyPagesTag() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
	//				+ ",requiredTargetUrlIdList=" + requiredTargetUrlIdListString);
	//
	//		if (!StringUtils.equalsIgnoreCase(existingTargetUrlIdListString, requiredTargetUrlIdListString)) {
	//
	//			// determine target URL ID(s) to be added to special group tag
	//			List<Long> targetUrlIdToBeAddedList = new ArrayList<Long>();
	//			for (Long targetUrlId : requiredTargetUrlIdList) {
	//				if (!existingTargetUrlIdList.contains(targetUrlId)) {
	//					targetUrlIdToBeAddedList.add(targetUrlId);
	//				}
	//			}
	//
	//			// determine target URL ID(s) to be removed from special group tag
	//			List<Long> targetUrlIdToBeRemovedList = new ArrayList<Long>();
	//			for (Long targetUrlId : existingTargetUrlIdList) {
	//				if (!requiredTargetUrlIdList.contains(targetUrlId)) {
	//					targetUrlIdToBeRemovedList.add(targetUrlId);
	//				}
	//			}
	//
	//			// create new group tag relations
	//			if (targetUrlIdToBeAddedList != null && targetUrlIdToBeAddedList.size() > 0) {
	//				for (Long targetUrlId : targetUrlIdToBeAddedList) {
	//					groupTagRelationEntityToBeCreated = new GroupTagRelationEntity();
	//					groupTagRelationEntityToBeCreated.setDomainId(domainId);
	//					groupTagRelationEntityToBeCreated.setGroupTagId(groupTagId);
	//					groupTagRelationEntityToBeCreated.setResourceId(targetUrlId);
	//					groupTagRelationEntityToBeCreated.setResourceType(GroupTagRelationEntity.RESOURCE_TYPE_TARGETURL);
	//					groupTagRelationEntityDAO.insert(groupTagRelationEntityToBeCreated);
	//				}
	//			}
	//
	//			// remove old group tag relations
	//			if (targetUrlIdToBeRemovedList != null && targetUrlIdToBeRemovedList.size() > 0) {
	//				for (Long targetUrlId : targetUrlIdToBeRemovedList) {
	//					groupTagRelationEntityDAO.deleteRelation(domainId, groupTagId, targetUrlId, GroupTagRelationEntity.RESOURCE_TYPE_TARGETURL);
	//				}
	//			}
	//
	//			// re-retrieve special group tag
	//			existingGroupTagRelationEntityList = groupTagRelationEntityDAO.getByGroupTagId(domainId, groupTagId);
	//			existingTargetUrlIdList = new ArrayList<Long>();
	//			if (existingGroupTagRelationEntityList != null && existingGroupTagRelationEntityList.size() > 0) {
	//				for (GroupTagRelationEntity groupTagRelationEntity : existingGroupTagRelationEntityList) {
	//					existingTargetUrlIdList.add(groupTagRelationEntity.getResourceId());
	//				}
	//			}
	//			Collections.sort(existingTargetUrlIdList);
	//			FormatUtils.getInstance().logMemoryUsage("maintainSpecialHotelPropertyPagesTag() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
	//					+ ",existingTargetUrlIdList after=" + existingTargetUrlIdList.toString());
	//
	//		} else {
	//			FormatUtils.getInstance().logMemoryUsage("maintainSpecialHotelPropertyPagesTag() ip=" + ip + ",domainId=" + domainId + ",domainName=" + domainName
	//					+ ",existingTargetUrlIdListString and requiredTargetUrlIdListString identical.");
	//		}
	//	}
}
