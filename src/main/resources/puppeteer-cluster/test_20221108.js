
const { Cluster } = require('puppeteer-cluster');
const fse = require('fs-extra');

(async () => {
	console.log('begins.');
	// Create a cluster with 2 workers
	const cluster = await Cluster.launch({
		concurrency: Cluster.CONCURRENCY_CONTEXT,
		maxConcurrency: 2,
	});

	// Event handler to be called in case of problems
	cluster.on('taskerror', (err, data) => {
		console.log(`Error crawling ${data}: ${err.message}`);
	});

	// Define a task (in this case: screenshot of page)
	await cluster.task(async ({ page, data: url }) => {

		// timeout is 15 seconds
		page.setDefaultNavigationTimeout(10000);

		// user agent is 'Mozilla/5.0 (compatible; ClarityBot/9.0; +https://www.seoclarity.net/bot.html)'
		page.setUserAgent('Mozilla/5.0 (compatible; ClarityBot/9.0; +https://www.seoclarity.net/bot.html)');

		// block images
		await page.setRequestInterception(true);
		page.on('request', (req) => {
			if (req.resourceType() === 'image') {
				req.abort();
			}
			else {
				req.continue();
			}
		});

		const response = await page.goto(url, { waitUntil: 'networkidle2' });
		if (response.request().redirectChain().length > 0) {
			const redirectPageUrl = page.url();
			for (const r of response.request().redirectChain()) {
				console.log('final url=' + redirectPageUrl + ',redirected status=' + r.response().status() + ',redirected url=' + r.url());
			}
		} else {
			console.log('url=' + url + ',status=' + response.status());
		}
		const path = url.replace(/[^a-zA-Z]/g, '_') + '.html';
		const content = await page.content();
		await fse.outputFile('page_source/' + path, content);
	});

	// Add some pages to queue
	cluster.queue('https://www.google.com'); // response code 200
	cluster.queue('https://www.wikipedia.org'); // response code 200
	cluster.queue('https://github.com/'); // response code 200
	cluster.queue('https://www.seoclarity.net/nonexistpage.html'); // response code 404
	cluster.queue('http://www.seoclarity.net/'); // response code 301

	// Shutdown after everything is done
	await cluster.idle();
	await cluster.close();
	console.log('ends.');
})();
