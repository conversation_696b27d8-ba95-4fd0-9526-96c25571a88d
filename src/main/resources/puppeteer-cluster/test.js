
const { Cluster } = require('puppeteer-cluster');
const fs = require('fs').promises;
const fse = require('fs-extra');

(async () => {
	const totalWorkers = 12;
	const totalStartTime = performance.now();
	console.log('begins. totalWorkers=' + totalWorkers);
	// Create a cluster with many workers
	const cluster = await Cluster.launch({
		concurrency: Cluster.CONCURRENCY_CONTEXT,
		maxConcurrency: totalWorkers,
	});

	// Event handler to be called in case of problems
	cluster.on('taskerror', (err, data) => {
		console.log(`Error crawling ${data}: ${err.message}`);
	});

	// Define a task (in this case: screenshot of page)
	await cluster.task(async ({ page, data: url }) => {

		const startTime = performance.now();

		// timeout in milliseconds
		page.setDefaultNavigationTimeout(0);
		page.setDefaultTimeout(0);

		// user agent
		if (url.startsWith('https://www.reddit.com')) {
			page.setUserAgent('Mozilla/5.0 (Linux; Android 9; SM-G960F Build/PPR1.180610.011; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/74.0.3729.157 Mobile Safari/537.36 (compatible; ClarityBot/9.0; +https://www.seoclarity.net/bot.html)');
		} else {
			//page.setUserAgent('Mozilla/5.0 (compatible; ClarityBot/9.0; +https://www.seoclarity.net/bot.html)');
			page.setUserAgent('Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 (compatible; PoliteCrawl.v2.0)');
		}

		// block images
		await page.setRequestInterception(true);
		page.on('request', (req) => {
			if (req.resourceType() === 'image') {
				req.abort();
			}
			else {
				req.continue();
			}
		});

		//const response = await page.goto(url, { waitUntil: 'networkidle2', timeout: 0 });
		const response = await page.goto(url, { waitUntil: 'load', timeout: 0 });
		const content = await page.content();
		//const path = url.replace(/[^a-zA-Z]/g, '') + '.htm';
		//await fse.outputFile('page_source/' + path, content);
		const endTime = performance.now();
		const elapsedInS = Math.round((endTime - startTime) / 1000); //in seconds 

		if (response.request().redirectChain().length > 0) {
			const redirectPageUrl = page.url();
			for (const r of response.request().redirectChain()) {
				console.log('final url`' + redirectPageUrl + '`redirected status`=' + r.response().status() + '`redirected url`' + r.url() + '`elapsedInS`' + elapsedInS + "`timestamp`" + new Date().toLocaleString('en-US', { timeZone: 'CST', }),);
			}
		} else {
			console.log('url`' + url + '`status`' + response.status() + '`elapsedInS`' + elapsedInS + "`timestamp`" + new Date().toLocaleString('en-US', { timeZone: 'CST', }),);
		}
	});

	// Read the 'urls.with.resp.code.200.txt' file from the 'data' directory
	const textFile = await fs.readFile('data/urls.with.resp.code.200.txt', 'utf8');
	var lines = textFile.split('\n');
	console.log('total URLs begins. lines.length=' + lines.length + ",timestamp=" + new Date().toLocaleString('en-US', { timeZone: 'CST', }),);
	for (let i = lines.length - 1; i > 0; i--) {
		let j = Math.floor(Math.random() * (i + 1));
		[lines[i], lines[j]] = [lines[j], lines[i]];
	}
	let totalUrls;
	for (let i = 0; i < lines.length; i++) {

		totalUrls = i;
		// maximum number of URLs
		//if (totalUrls > 168) {
		//	break;
		//}

		const line = lines[i];
		cluster.queue(line);
	}
	console.log('total URLs ends totalUrls=' + totalUrls + ",timestamp=" + new Date().toLocaleString('en-US', { timeZone: 'CST', }),);

	// Shutdown after everything is done
	await cluster.idle();
	await cluster.close();
	const totalEndTime = performance.now();
	const totalElapsedInS = Math.round((totalEndTime - totalStartTime) / 1000); //in seconds
	console.log('ends. totalWorkers=' + totalWorkers + ",totalElapsedInS=" + totalElapsedInS);
})();
