##########################
##    JDBC Setting      ##
##########################

## MySQL

jdbc.driver=org.gjt.mm.mysql.Driver
jdbc.url=***************************************************************************************
#jdbc.url=************************************************************************************
jdbc.username=shinetech
jdbc.password=k8XBb2Yh

rankcheck.jdbc.driver.new=org.gjt.mm.mysql.Driver
rankcheck.jdbc.url.new=****************************************************************************************************
#rankcheck.jdbc.url.new=********************************************************************************************************
rankcheck.jdbc.username.new=shinetech
rankcheck.jdbc.password.new=k8XBb2Yh

crawlsite.jdbc.driver=org.gjt.mm.mysql.Driver
crawlsite.jdbc.url=*************************************************************************************************
crawlsite.jdbc.username=shinetech
crawlsite.jdbc.password=k8XBb2Yh

infobright.jdbc.driver=org.gjt.mm.mysql.Driver
infobright.jdbc.url=*************************************************************************************************
infobright.jdbc.username=haproxy
infobright.jdbc.password=fix@4321

crawlsite.infobright.jdbc.driver=org.gjt.mm.mysql.Driver
crawlsite.infobright.jdbc.url=*********************************************************************************************************
crawlsite.infobright.jdbc.username=shinetech
crawlsite.infobright.jdbc.password=k8XBb2Yh

rankcheck.infobright.jdbc.driver=org.gjt.mm.mysql.Driver
rankcheck.infobright.jdbc.url=************************************************************************************************************
rankcheck.infobright.jdbc.username=shinetech
rankcheck.infobright.jdbc.password=k8XBb2Yh

api.jdbc.driver=org.gjt.mm.mysql.Driver
api.jdbc.url=*******************************************************************************************
api.jdbc.username=shinetech
api.jdbc.password=k8XBb2Yh

linkexplorer.jdbc.driver=org.gjt.mm.mysql.Driver
#linkexplorer.jdbc.url=**************************************************************************************************
linkexplorer.jdbc.url=********************************************************************************************
linkexplorer.jdbc.username=shinetech
linkexplorer.jdbc.password=k8XBb2Yh

backlink.jdbc.driver=org.gjt.mm.mysql.Driver
backlink.jdbc.url=*********************************************************************************************
backlink.jdbc.username=shinetech
backlink.jdbc.password=k8XBb2Yh

infobright.backlink.jdbc.driver=org.gjt.mm.mysql.Driver
infobright.backlink.jdbc.url=*********************************************************************************************
infobright.backlink.jdbc.username=shinetech
infobright.backlink.jdbc.password=k8XBb2Yh

actoniaScript.jdbc.driver=org.gjt.mm.mysql.Driver
actoniaScript.jdbc.url=**********************************************************************************************
actoniaScript.jdbc.username=shinetech
actoniaScript.jdbc.password=k8XBb2Yh

queuing.jdbc.driver=org.gjt.mm.mysql.Driver
queuing.jdbc.url=**************************************************************************************************************
queuing.jdbc.username=shinetech
queuing.jdbc.password=k8XBb2Yh

metrics.jdbc.driver=org.gjt.mm.mysql.Driver
metrics.jdbc.url=***********************************************************************************************
metrics.jdbc.username=shinetech
metrics.jdbc.password=k8XBb2Yh

mgdrank.jdbc.driver=org.gjt.mm.mysql.Driver
mgdrank.jdbc.url=********************************************************************************************
mgdrank.jdbc.username=shinetech
mgdrank.jdbc.password=k8XBb2Yh

dailyranking.jdbc.driver=org.gjt.mm.mysql.Driver
dailyranking.jdbc.url=****************************************************************************************************
dailyranking.jdbc.username=shinetech
dailyranking.jdbc.password=k8XBb2Yh

monthlyranking.jdbc.driver=org.gjt.mm.mysql.Driver
monthlyranking.jdbc.url=***********************************************************************************************************
monthlyranking.jdbc.username=shinetech
monthlyranking.jdbc.password=k8XBb2Yh

actonia.ippool.jdbc.driver=org.gjt.mm.mysql.Driver
actonia.ippool.jdbc.url=***********************************************************************************************
actonia.ippool.jdbc.username=shinetech
actonia.ippool.jdbc.password=k8XBb2Yh


########################bot infobright jdbc##################################

infobright.bot.jdbc.driver=org.gjt.mm.mysql.Driver
infobright.bot.jdbc.url=************************************************************************************************
infobright.bot.jdbc.username=shinetech
infobright.bot.jdbc.password=k8XBb2Yh

infobright.bot.185.jdbc.driver=org.gjt.mm.mysql.Driver
infobright.bot.185.jdbc.url=****************************************************************************************************
infobright.bot.185.jdbc.username=shinetech
infobright.bot.185.jdbc.password=k8XBb2Yh

infobright.bot.182.jdbc.driver=org.gjt.mm.mysql.Driver
infobright.bot.182.jdbc.url=****************************************************************************************************
infobright.bot.182.jdbc.username=shinetech
infobright.bot.182.jdbc.password=k8XBb2Yh

infobright.bot.334.jdbc.driver=org.gjt.mm.mysql.Driver
infobright.bot.334.jdbc.url=****************************************************************************************************
infobright.bot.334.jdbc.username=shinetech
infobright.bot.334.jdbc.password=k8XBb2Yh

infobright.bot.334.other.jdbc.driver=org.gjt.mm.mysql.Driver
infobright.bot.334.other.jdbc.url=**********************************************************************************************************
infobright.bot.334.other.jdbc.username=shinetech
infobright.bot.334.other.jdbc.password=k8XBb2Yh

########################bot jdbc##################################
bot.jdbc.driver=org.gjt.mm.mysql.Driver
bot.jdbc.url=*******************************************************************************************
bot.jdbc.username=shinetech
bot.jdbc.password=k8XBb2Yh

bot.185.jdbc.driver=org.gjt.mm.mysql.Driver
bot.185.jdbc.url=***********************************************************************************************
bot.185.jdbc.username=shinetech
bot.185.jdbc.password=k8XBb2Yh

bot.182.jdbc.driver=org.gjt.mm.mysql.Driver
bot.182.jdbc.url=***********************************************************************************************
bot.182.jdbc.username=shinetech
bot.182.jdbc.password=k8XBb2Yh

bot.334.jdbc.driver=org.gjt.mm.mysql.Driver
bot.334.jdbc.url=***********************************************************************************************
bot.334.jdbc.username=shinetech
bot.334.jdbc.password=k8XBb2Yh

bot.334.other.jdbc.driver=org.gjt.mm.mysql.Driver
bot.334.other.jdbc.url=*****************************************************************************************************
bot.334.other.jdbc.username=shinetech
bot.334.other.jdbc.password=k8XBb2Yh

botInfobright.jdbc.driver=org.gjt.mm.mysql.Driver
botInfobright.jdbc.url=*****************************************************************************************************
botInfobright.jdbc.username=shinetech
botInfobright.jdbc.password=k8XBb2Yh

#######################crawlanywhre jdbc################################
crawlanywhere.jdbc.driver=org.gjt.mm.mysql.Driver
crawlanywhere.jdbc.url=jdbc:mysql://***********/crawler?useUnicode=true&characterEncoding=utf-8&autoReconnect=true
crawlanywhere.jdbc.username=shinetech
crawlanywhere.jdbc.password=k8XBb2Yh

#######################unused jdbc################################

rankcheck.jdbc.driver=org.gjt.mm.mysql.Driver
rankcheck.jdbc.url=***************************************************************************************************
rankcheck.jdbc.username=shinetech
rankcheck.jdbc.password=k8XBb2Yh


crawlsiteforoverstock.jdbc.driver=org.gjt.mm.mysql.Driver
crawlsiteforoverstock.jdbc.url=*************************************************************************************************************
crawlsiteforoverstock.jdbc.username=shinetech
crawlsiteforoverstock.jdbc.password=k8XBb2Yh

rankcheckca.jdbc.driver=org.gjt.mm.mysql.Driver
rankcheckca.jdbc.url=******************************************************************************************************
rankcheckca.jdbc.username=shinetech
rankcheckca.jdbc.password=k8XBb2Yh

rankcheckcafr.jdbc.driver=org.gjt.mm.mysql.Driver
rankcheckcafr.jdbc.url=*********************************************************************************************************
rankcheckcafr.jdbc.username=shinetech
rankcheckcafr.jdbc.password=k8XBb2Yh

rankcheckau.jdbc.driver=org.gjt.mm.mysql.Driver
rankcheckau.jdbc.url=******************************************************************************************************
rankcheckau.jdbc.username=shinetech
rankcheckau.jdbc.password=k8XBb2Yh

monitor.jdbc.driver=org.gjt.mm.mysql.Driver
monitor.jdbc.url=***********************************************************************************************
monitor.jdbc.username=shinetech
monitor.jdbc.password=k8XBb2Yh


#######################Vertica jdbc###########################
vertica.jdbc.driver=com.vertica.jdbc.Driver
vertica.jdbc.url=jdbc:vertica://*************:5433/daily_ranking
vertica.jdbc.username=dbadmin
vertica.jdbc.password=clarity99
#############################################################

# keyword_rank IEE database on solr100 server
infobright.keyword_rank.jdbc.driver=org.gjt.mm.mysql.Driver
infobright.keyword_rank.jdbc.url=jdbc:mysql://*************:5029/keyword_rank?useUnicode=true&characterEncoding=utf-8&autoReconnect=true
infobright.keyword_rank.jdbc.username=shinetech
infobright.keyword_rank.jdbc.password=k8XBb2Yh

# historical_rank IEE database on solr100 server
infobright.historical_rank.jdbc.driver=org.gjt.mm.mysql.Driver
infobright.historical_rank.jdbc.url=jdbc:mysql://*************:5029/historical_rank?useUnicode=true&characterEncoding=utf-8&autoReconnect=true
infobright.historical_rank.jdbc.username=shinetech
infobright.historical_rank.jdbc.password=k8XBb2Yh

#######################Monthly search volume cleanup entry###########################
searchVolume.jdbc.driver=org.gjt.mm.mysql.Driver
#db5.mgdkeyword.jdbc.url=*************************************************************************************************
searchVolume.jdbc.url=******************************************************************************************************************
searchVolume.jdbc.username=clarity
searchVolume.jdbc.password=fixyfoxy