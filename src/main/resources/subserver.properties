##########################
##    Server Config     ##
##########################

subserver.ip=************
subserver.tryCountIfBlock=10
#for *********** Demo Server
subserver.ipList=*************
subserver.delay=3000
subserver.delayForHtmlcontent=0
subserver.delayForPartnerHtmlcontent=0
subserver.delayForPR=5000
subserver.delayForYahooInlink=1
subserver.delayForCacheDate=10000
subserver.delayForPageNum=15000
subserver.cralwCycleForPR=7
subserver.cralwCycleForYahooInlink=30
subserver.cralwCycleForTargetUrlCacheDate=1
subserver.cralwCycleForPartnerUrlCacheDate=7
subserver.cralwCycleForCompetitorUrlCacheDate=1
subserver.cralwCycleForPartnerDomianPageCount=7
subserver.cralwCycleForCompetitorUrl=3
subserver.csvlocation=c:/test/temp/185/
subserver.csvkeywordandtargeturllocation=c:/test/
subserver.tagcsvlocation=/home/
subserver.sitemaplocation=c:/test/site/
subserver.overstockDomainId=185
subserver.crawlsitedomainId=185
#resource.path.googlewebmaster=/home/<USER>/uploadfiles/googlewebmaster/
resource.path.googlewebmaster=c:/test/
internationalrank.upload.log.status=false
#for ************** DB Server
#subserver.newipList=*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************,*************

#subserver.newipList=***************,**************,**************,**************,**************,***************,***************
#subserver.newipList=***************,***************,***************,***************,***************,***************,***************
#subserver.newipList=***************,***************,***************,***************,***************,***************
subserver.newipList=***************
subserver.virtualiplist=127.0.0.1,*********,*********,*********,*********,*********
subserver.proxyIpList=*************:6745,*************:6745,*************:6745,*************:6745,*************:6745,*************:6745,*************:6745,*************:6746,*************:6746,*************:6746,*************:6746,*************:6746,*************:6746,*************:6747,*************:6747,*************:6747,*************:6747,*************:6747,*************:6747,*************:6747,*************:6748,*************:6748,*************:6748,*************:6748,*************:6748,*************:6748,*************:6748
crawler.proxy.user=seoclarity
crawler.proxy.pwd=TEST
subserver.processDomainId=331
subserver.coreMetricsCsvLocation=C:/test/temp/cr/
subserver.divCsvLocation=C:/test/div/
subserver.partnerurlAndDateLocation=C:/4/
subserver.omnitureCsvLocation=C:/test/usbank/
subserver.omnitureClient=meijer,autoanything,workday,sybase,allstate,usbank
subserver.exportfilefolder=/home/<USER>/
subserver.deleteExportiFileLargethanXdays=14

subserver.domainIdneedcrawled=4
subserver.urlsFileStroeLoc=/home/<USER>/
subserver.ppcFileLoc=/home/<USER>/
#subserver.exportfilefolder=C:/test/
#Omniture Config
# meijer
#subserver.omnituredomainid=23
#subserver.omniture=s.pageName
#subserver.sitemaponinternate=http://www.meijer.com/sitemaps/detail0.xml;http://www.meijer.com/sitemaps/detail1.xml;http://www.meijer.com/sitemaps/detail2.xml;http://www.meijer.com/sitemaps/detail3.xml;http://www.meijer.com/sitemaps/navigation0.xml;http://www.meijer.com/sitemaps/navigation1.xml;http://www.meijer.com/sitemaps/navigation2.xml;http://www.meijer.com/sitemaps/searchterm0.xml;http://www.meijer.com/sitemaps/staticpage0.xml
#subserver.downloadxmlforomniture=c:/test/

# Autoanything
subserver.omnituredomainid=236
subserver.omniture=naf["pageName"]
subserver.sitemaponinternate=http://www.autoanything.com/sitemaps/sitemap_autoanything1.xml;http://www.autoanything.com/sitemaps/sitemap_autoanything2.xml;http://www.autoanything.com/sitemaps/sitemap_autoanything3.xml;http://www.autoanything.com/sitemaps/sitemap_autoanything4.xml
subserver.downloadxmlforomniture=c:/test/

subserver.homeAwayFileLoc=/home/<USER>/

subserver.scribeServer=174.123.3.242
subserver.scribeServerPort=1463


#added by zhaozh
#subserver.exportUnexcKeywordPath=D:/
#subserver.exportUnexcKeywordPath=/home/<USER>/
subserver.exportUnexcKeywordPath=/home/<USER>/tempfile
#added by zhaozh,Style:yyyy,MM,yyyy,MM;noly allowed two months;
subserver.dateYearAndMonthForUnexist=2011,12,2012,01,
#added by zhaozh,20111028,for query RetailMenot.com's searchvolume
subserver.dateRankForSearchVolume=2010-09,2011-09,
#added by zhaozh,for upload keyword list
#subserver.keywordListPathAndName=E:/Keywords_total.xls
subserver.keywordListPathAndName=/home/<USER>/upload/Keywords_total.xls

subserver.analyticsMongodbTempFolder=/home/<USER>/source/coremetrics_mongo/

# specifies whether the URL crawling process runs on cloud instances
subserver.url.crawl.cloud.instances=false

# specifies the number of queues for daily target URLs social crawl
subserver.total.social.crawl.queues=33

# specifies the number of concurrent threads per server instance for daily target URLs social crawl
subserver.social.crawl.threads.per.instance=3

# SOLR server connection URL for polite crawl data - begins
subserver.solr.connection.url=http://politesolr-1.seoclarity.net:8983/solr/
#subserver.solr.connection.url.read.only=http://server5:8088/solr/
subserver.solr.connection.url.read.only=http://politesolr-3.seoclarity.net:8983/solr/
#subserver.solr.connection.url.read.only=http://haproxyipfailover:8088/solr/
#subserver.solr.connection.url.read.only=http://************:8088/solr/
# SOLR server connection URL for polite crawl data - ends

subserver.disavowed.backlinks.upload.folder=/home/<USER>/
subserver.user.backlinks.upload.folder=/home/<USER>/
subserver.majestic.backlinks.upload.folder=/home/<USER>/