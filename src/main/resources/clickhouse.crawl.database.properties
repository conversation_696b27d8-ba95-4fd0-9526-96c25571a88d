
# ClickHouse configurations for the 'crawl' database for polite crawler v2.0 data storage

crawler.v3.clickhouse.db.port=8123
crawler.v3.clickhouse.db.name=crawl
crawler.v3.clickhouse.batch.creation.size=5000
crawler.v3.clickhouse.connection.timeout.milliseconds=8888888
crawler.v3.clickhouse.maximum.retry.counts=8
crawler.v3.clickhouse.retry.wait.milliseconds=1000

# for production environment (when batch run on 'crawler1' server)
crawler.v3.clickhouse.db.hostnames=***********
#crawler.v3.clickhouse.db.hostnames=*************
crawler.v3.clickhouse.user=default
crawler.v3.clickhouse.password=clarity99!
clickhouse.cluster.servers=***********,***********,***********
