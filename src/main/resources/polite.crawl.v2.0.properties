#### polite crawl v2.0 runtime properties begins ####

crawler.v3.controller.queue=TARGET_URL_HTML_QUEUE_NAMES

# crawl type 2 is target URL daily crawl
crawler.v3.crawltype=2

crawler.v3.messages_per_iteration=10
crawler.v3.user.agent=Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36 (compatible; PoliteCrawl.v2.0)
crawler.v3.process.page.analysis=true
crawler.v3.stop.crawl.queue=TARGET_URL_HTML_STOP_CRAWL_QUEUE

# test environment
#scrapy.crawler.endpoints=http://************:8888/crawl

# production environment
scrapy.crawler.endpoints=http://*************/crawl
scrapy.crawler.london.endpoints=http://***************/crawl
scrapy.crawler.access.key=cHVwcmVuZGVyX2ZhY3R3ZWF2ZXJz

javascript.crawler.endpoints=http://**************/execute

scrapy.mobile.user.agent=Mozilla/5.0 (Linux; U; Android 4.4.2; en-us; SCH-I535 Build/KOT49H) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30

domain.ids.with.restricted.crawl.time=1669,1670,1671,1672,3622,3623,3631,3632,3633,3636,3637,3639,3640,3643,3644,3646,3647,3651,3652,3801,3918,3919,3920,3921,5066,5593,5861,5862,6683

# The Polite Crawl web service runs on 'crawler01' server
polite.crawl.web.service.endpoint=http://************:16819/politeCrawlService

# for testing Polite Crawl web service changes after deployment to 'api.seoclarity.net'
main.web.service.endpoint=https://api.seoclarity.net/seoClarity

serp.analyzer.api.endpoint=http://************:7521/analyze/content


#### polite crawl v2.0 runtime properties ends ####
