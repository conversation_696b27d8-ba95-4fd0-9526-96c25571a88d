00001=Request JSON is required.
00002=Request parameter access_token {0} is invalid.
00003=Web service method exception {0}.
00004=Request parameter access_token is required.
00005=Request parameter test_market is required.
00006=Request parameter pre_period_start_date is required.
00007=Request parameter pre_period_end_date is required.
00008=Request parameter post_period_start_date is required.
00009=Request parameter post_period_end_date is required.
00010=Request parameter number_of_best_matches is required.
00011=Request parameter area_date_value_array is required.
00012=Request parameter test_market {0} is invalid.
00013=Request parameter pre_period_start_date {0} is invalid.
00014=Request parameter pre_period_end_date {0} is invalid.
00015=Request parameter post_period_start_date {0} is invalid.
00016=Request parameter post_period_end_date {0} is invalid.
00017=Request parameter pre_period_start_date and pre_period_end_date combination is invalid.
00018=Request parameter post_period_start_date must be one day after pre_period_end_date.
00019=Request parameter post_period_start_date and post_period_end_date combination is invalid.
00020=Request parameter area_date_value_array.area_array is required.
00021=Request parameter area_date_value_array.area_array is invalid.
00022=Request parameter area_date_value_array.date_array is required.
00023=Request parameter area_date_value_array.date_array is invalid.
00024=Request parameter area_date_value_array.value_array is required.
00025=Request parameter area_date_value_array.value_array is invalid.
00026=Number of dates in area_date_value_array does not match pre_period_start_date to post_period_end_date.
00027=Request parameter number_of_best_matches {0} is invalid.
