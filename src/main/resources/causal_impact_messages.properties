00001=Request JSON is required.
00002=Request parameter access_token {0} is invalid.
00003=Web service method exception {0}.
00004=Request command {0} is invalid.
00005=Request parameter access_token is required.
00006=Request parameter test_time_series is required.
00007=Request parameter control_name_list is required.
00008=Request parameter control_time_series_list is required.
00009=Total number of data points inconsistent among time series.
00010=Total number of control names must equal total number of control time series.
00011=Request parameter pre_period_start_date is required.
00012=Request parameter pre_period_end_date is required.
00013=Request parameter post_period_start_date is required.
00014=Request parameter post_period_end_date is required.
00015=Request parameter pre_period_start_date {0} is invalid.
00016=Request parameter pre_period_end_date {0} is invalid.
00017=Request parameter post_period_start_date {0} is invalid.
00018=Request parameter post_period_end_date {0} is invalid.
00019=Request parameter pre_period_start_date and pre_period_end_date combination is invalid.
00020=Request parameter post_period_start_date and post_period_end_date combination is invalid.
00021=Request parameter control name(s) cannot be blank.
00022=Request parameter control name(s) must be unique.
00023=Days between pre-period start and post-period end must equal (days between pre-period start and end + days between post-period start and end).
00024=Days between pre-period start and post-period end must equal total number of test data points.
