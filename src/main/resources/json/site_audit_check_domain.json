{"response": [{"rule": "robotsTextCheck", "time": "2020-03-05 09:19:36", "message": "Robots.txt file found (https://www.seoclarity.net/robots.txt). Got status code : 200", "robotsTxtContent": "User-agent: *\nDisallow: /thank-you\nDisallow: /thank-you/\nDisallow: /thank-you*\nDisallow: /*thank-you$\nDisallow: /blog/author*\nDisallow: /blog/tag*\nDisallow: /competitive-insights-report-3/\nDisallow: /see-it-in-action-enterprise-v1\nDisallow: /_hcms/preview/\nDisallow: /hs/manage-preferences/\n", "status": "SUCCESS", "statusCode": 1}, {"result": {"userAgents": {"baiduspider": "ALLOWED", "bingbot": "ALLOWED", "googlebot": "ALLOWED", "yahoo": "ALLOWED"}, "allUserAgents": {"*": {"isBlocked": false}}, "isAllBotsBlocked": false, "isAllPagesBlocked": false}, "rule": "robotsBlockCheck", "time": "2020-03-05 09:19:36", "message": "Checked the blocking status all bots", "status": "SUCCESS", "statusCode": 1}, {"rule": "siteMapCheck", "time": "2020-03-05 09:19:36", "message": "Failed: Robots.txt not contains Sitemap:", "status": "FAILED", "statusCode": 2}, {"siteMapUrls": {}, "rule": "siteMapValidationCheck", "time": "2020-03-05 09:19:36", "message": "Robots.txt not contains Sitemap:, Not testing", "status": "NOT_TESTED", "statusCode": 3}, {"rule": "trailingSlashCheck", "time": "2020-03-05 09:19:37", "message": "status code are : 200 and 200", "status": "FAILED", "statusCode": 2}, {"rule": "protocolCheck", "time": "2020-03-05 09:19:37", "message": "status code of http is 301 and https is 200", "status": "SUCCESS", "statusCode": 1}, {"rule": "errorPageCheck", "time": "2020-03-05 09:19:37", "message": "status code is 404 for url https://www.seoclarity.net/dinoop-abcedefghijklmno", "status": "SUCCESS", "statusCode": 1}, {"rule": "canonical<PERSON><PERSON><PERSON>", "time": "2020-03-05 09:19:38", "message": "Canonical link is same: https://www.seoclarity.net/", "status": "SUCCESS", "statusCode": 1}, {"rule": "alternate<PERSON>ag<PERSON><PERSON><PERSON>", "time": "2020-03-05 09:19:38", "message": "Html not contains meta rel=\"alternate\" , but contains meta name=\"viewport\"", "status": "SUCCESS", "statusCode": 1}], "messages": ["Info: Testing with url : www.seoclarity.net", "Warn: URL is formatted, Testing with: http://www.seoclarity.net", "Info: Status code of url is 301", "Warn: Redirected url is https://www.seoclarity.net/", "Info: Status code of Redirected url https://www.seoclarity.net/ is 200", "Info: URL checking completed"], "startTime": "2020-03-05 09:19:35", "endTime": "2020-03-05 09:19:38", "message": "Info: URL checking completed", "finalURL": "https://www.seoclarity.net/", "url": "www.seoclarity.net", "processingTime": "3 seconds", "status": 200}