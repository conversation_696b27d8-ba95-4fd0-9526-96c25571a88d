<div style="margin:0 auto; width:880px; color:#333; font:12px/1.5 Verdana, Geneva, sans-serif;">

    <!-- head -->
    <div style="border-bottom: 3px #f79646 solid; padding: 5px 10px;">
        <span style="padding-top:10px; font-weight:bold; color:#777;">$!etHeadTitle </span>
        <a target="_blank" href="$!{agencyInfo.websiteDomain}">
            <img style="border: none;" src="$!{agencyInfo.websiteDomain}/images/company/$!{agencyInfo.logoImage}/logo_email.png" title="$!{agencyInfo.companyTitle} Logo" alt="$!{agencyInfo.companyTitle} Logo" height="30" />
        </a>
    </div>

    <!-- body -->
    <div style="padding:25px 10px;">    
        <div style="margin:15px 0;">
    #if( $targetUrlChangeAlertOneSummary )
			<br>
			<br>
			<hr>
			<br>
			<br>
        	#if( $targetUrlChangeAlertOneSummary.changeIndicatorTotalChangesAddedList.size() > 0 )
        		<p>
          			<span style="text-align: left; font-size: 12px; color: black;"><b>ADDED</b></span>
        		</p>
	            <table style="border-collapse: collapse; border-spacing: 0; border: 1px #FFE5B9 solid; font-size: 12px;">
	                <thead>
	                <tr>
	                    <th style="vertical-align:top; text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;">
	                        CHANGE DESCRIPTION
	                    </th>
	                    <th style="vertical-align:top; text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;">
	                        TOTAL URLS
	                    </th>
	                </tr>
	                </thead>
	                <tbody>
            	#foreach($changeIndicatorTotalChanges in $targetUrlChangeAlertOneSummary.changeIndicatorTotalChangesAddedList)
	                <tr>
	                    <td style="vertical-align:top; border: 1px #FFE5B9 solid; padding: 5px;">	                    
	                    	<a >$!changeIndicatorTotalChanges.changeIndicatorDesc</a>
	                    </td>
	                    <td style="vertical-align:top; border: 1px #FFE5B9 solid; padding: 5px;">	                    
	                    	<a href="">$!changeIndicatorTotalChanges.totalChangesWithWebAppLink</a>
	                    </td>
	                </tr>
            	#end
	                </tbody>
	            </table>
        	#end
        	#if( $targetUrlChangeAlertOneSummary.changeIndicatorTotalChangesModifiedList.size() > 0 )
				<br>
				<br>
        		<p>
          			<span style="text-align: left; font-size: 12px; color: black;"><b>MODIFIED</b></span>
        		</p>
	            <table style="border-collapse: collapse; border-spacing: 0; border: 1px #FFE5B9 solid; font-size: 12px;">
	                <thead>
	                <tr>
	                    <th style="vertical-align:top; text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;">
	                        CHANGE DESCRIPTION
	                    </th>
	                    <th style="vertical-align:top; text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;">
	                        TOTAL URLS
	                    </th>
	                </tr>
	                </thead>
	                <tbody>
            	#foreach($changeIndicatorTotalChanges in $targetUrlChangeAlertOneSummary.changeIndicatorTotalChangesModifiedList)
	                <tr>
	                    <td style="vertical-align:top; border: 1px #FFE5B9 solid; padding: 5px;">	                    
	                    	<a >$!changeIndicatorTotalChanges.changeIndicatorDesc</a>
	                    </td>
	                    <td style="vertical-align:top; border: 1px #FFE5B9 solid; padding: 5px;">	                    
	                    	<a href="">$!changeIndicatorTotalChanges.totalChangesWithWebAppLink</a>
	                    </td>
	                </tr>
            	#end
	                </tbody>
	            </table>
        	#end
        	#if( $targetUrlChangeAlertOneSummary.changeIndicatorTotalChangesRemovedList.size() > 0 )
				<br>
				<br>
        		<p>
          			<span style="text-align: left; font-size: 12px; color: black;"><b>REMOVED</b></span>
        		</p>
	            <table style="border-collapse: collapse; border-spacing: 0; border: 1px #FFE5B9 solid; font-size: 12px;">
	                <thead>
	                <tr>
	                    <th style="vertical-align:top; text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;">
	                        CHANGE DESCRIPTION
	                    </th>
	                    <th style="vertical-align:top; text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;">
	                        TOTAL URLS
	                    </th>
	                </tr>
	                </thead>
	                <tbody>
            	#foreach($changeIndicatorTotalChanges in $targetUrlChangeAlertOneSummary.changeIndicatorTotalChangesRemovedList)
	                <tr>
	                    <td style="vertical-align:top; border: 1px #FFE5B9 solid; padding: 5px;">	                    
	                    	<a >$!changeIndicatorTotalChanges.changeIndicatorDesc</a>
	                    </td>
	                    <td style="vertical-align:top; border: 1px #FFE5B9 solid; padding: 5px;">	                    
	                    	<a href="">$!changeIndicatorTotalChanges.totalChangesWithWebAppLink</a>
	                    </td>
	                </tr>
            	#end
	                </tbody>
	            </table>
        	#end
    #end
    #if( $targetUrlChangeAlertSummaryList )
		#foreach($targetUrlChangeAlertSummary in $targetUrlChangeAlertSummaryList)
			<br>
			<br>
			<hr>
			<br>
			<br>
        	<p>
          		<span style="text-align: left; font-size: 12px; color: black;"><b>Group Tag: $!targetUrlChangeAlertSummary.pageTagName</b></span><br>
        	</p>
			<br>
        	#if( $targetUrlChangeAlertSummary.changeIndicatorTotalChangesAddedList.size() > 0 )
        		<p>
          			<span style="text-align: left; font-size: 12px; color: black;"><b>ADDED</b></span>
        		</p>
	            <table style="border-collapse: collapse; border-spacing: 0; border: 1px #FFE5B9 solid; font-size: 12px;">
	                <thead>
	                <tr>
	                    <th style="vertical-align:top; text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;">
	                        CHANGE DESCRIPTION
	                    </th>
	                    <th style="vertical-align:top; text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;">
	                        TOTAL URLS
	                    </th>
	                </tr>
	                </thead>
	                <tbody>
            	#foreach($changeIndicatorTotalChanges in $targetUrlChangeAlertSummary.changeIndicatorTotalChangesAddedList)
	                <tr>
	                    <td style="vertical-align:top; border: 1px #FFE5B9 solid; padding: 5px;">	                    
	                    	<a >$!changeIndicatorTotalChanges.changeIndicatorDesc</a>
	                    </td>
	                    <td style="vertical-align:top; border: 1px #FFE5B9 solid; padding: 5px;">	                    
	                    	<a href="">$!changeIndicatorTotalChanges.totalChangesWithWebAppLink</a>
	                    </td>
	                </tr>
            	#end
	                </tbody>
	            </table>
        	#end
        	#if( $targetUrlChangeAlertSummary.changeIndicatorTotalChangesModifiedList.size() > 0 )
        		<p>
          			<span style="text-align: left; font-size: 12px; color: black;"><b>MODIFIED</b></span>
        		</p>
	            <table style="border-collapse: collapse; border-spacing: 0; border: 1px #FFE5B9 solid; font-size: 12px;">
	                <thead>
	                <tr>
	                    <th style="vertical-align:top; text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;">
	                        CHANGE DESCRIPTION
	                    </th>
	                    <th style="vertical-align:top; text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;">
	                        TOTAL URLS
	                    </th>
	                </tr>
	                </thead>
	                <tbody>
            	#foreach($changeIndicatorTotalChanges in $targetUrlChangeAlertSummary.changeIndicatorTotalChangesModifiedList)
	                <tr>
	                    <td style="vertical-align:top; border: 1px #FFE5B9 solid; padding: 5px;">	                    
	                    	<a >$!changeIndicatorTotalChanges.changeIndicatorDesc</a>
	                    </td>
	                    <td style="vertical-align:top; border: 1px #FFE5B9 solid; padding: 5px;">	                    
	                    	<a href="">$!changeIndicatorTotalChanges.totalChangesWithWebAppLink</a>
	                    </td>
	                </tr>
            	#end
	                </tbody>
	            </table>
        	#end
        	#if( $targetUrlChangeAlertSummary.changeIndicatorTotalChangesRemovedList.size() > 0 )
        		<p>
          			<span style="text-align: left; font-size: 12px; color: black;"><b>REMOVED</b></span>
        		</p>
	            <table style="border-collapse: collapse; border-spacing: 0; border: 1px #FFE5B9 solid; font-size: 12px;">
	                <thead>
	                <tr>
	                    <th style="vertical-align:top; text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;">
	                        CHANGE DESCRIPTION
	                    </th>
	                    <th style="vertical-align:top; text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;">
	                        TOTAL URLS
	                    </th>
	                </tr>
	                </thead>
	                <tbody>
            	#foreach($changeIndicatorTotalChanges in $targetUrlChangeAlertSummary.changeIndicatorTotalChangesRemovedList)
	                <tr>
	                    <td style="vertical-align:top; border: 1px #FFE5B9 solid; padding: 5px;">	                    
	                    	<a >$!changeIndicatorTotalChanges.changeIndicatorDesc</a>
	                    </td>
	                    <td style="vertical-align:top; border: 1px #FFE5B9 solid; padding: 5px;">	                    
	                    	<a href="">$!changeIndicatorTotalChanges.totalChangesWithWebAppLink</a>
	                    </td>
	                </tr>
            	#end
	                </tbody>
	            </table>
        	#end
		#end
    #end            
        	<p>
          		<span style="text-align: left; font-size: 12px; color: black;">${informationalMessage}</span><br>
        	</p>
        </div>
    </div>

    <!-- foot -->
    <div style="border-top:3px #f79646 solid; padding-top:15px; text-align: center; line-height:1.5;">

        $!{agencyInfo.emailFooter.address} <br/>
        <div style="color:#777;">
            Support: <a href="mailto:$!{agencyInfo.emailReplyto}">$!{agencyInfo.emailReplyto}</a> <br/>
            Phone: $!{agencyInfo.emailFooter.phone} <br/>
            Fax: $!{agencyInfo.emailFooter.fax}
        </div>

        <input type="hidden" value="$!hostName"/>

    </div>
    
    <p style="text-align:center;border: 1px #ddd dotted;margin-top: 40px;font-style: italic;font-size: x-small;font-family: 'Proxima Nova', 'Open Sans', 'Helvetica Neue', Calibri, Helvetica, sans-serif;">
    Do not wish to receive these emails? <br/> Simply write to <a href="mailto:<EMAIL>"><EMAIL></a> to have these emails disabled for your account
    </p>

</div>
