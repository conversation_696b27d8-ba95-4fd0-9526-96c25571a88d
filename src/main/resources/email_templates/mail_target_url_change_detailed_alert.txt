<div style="margin:0 auto; color:#333; font:12px/1.5 Verdana, Geneva, sans-serif;">

    <!-- head -->
    <div style="border-bottom: 3px #f79646 solid; padding: 5px 10px;">
        <span style="padding-top:10px; font-weight:bold; color:#777;">$!etHeadTitle </span>
        <a target="_blank" href="$!{agencyInfo.websiteDomain}">
            <img style="border: none;" src="$!{agencyInfo.websiteDomain}/images/company/$!{agencyInfo.logoImage}/logo_email.png" title="$!{agencyInfo.companyTitle} Logo" alt="$!{agencyInfo.companyTitle} Logo" height="30" />
        </a>
    </div>

    <!-- body -->
    <div style="padding:25px 10px;">
        <div style="margin:15px 0;">
                
        #if( $targetUrlChangeAlertDetailsList.size() > 0 )
	            <table style="border-collapse: collapse; border-spacing: 0; border: 1px #FFE5B9 solid; width: 100%; font-size: 12px; margin: 10px auto;">
	                <thead>
	                <tr>
	                    <th style="vertical-align:top; text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;" nowrap>
	                         CHANGE DESCRIPTION
	                    </th>
	                    <th style="vertical-align:top; text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;" nowrap>
                            CHANGE TIME
                        </th>
	                    <th style="vertical-align:top; text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;" nowrap>
	                         SEVERITY
	                    </th>
	                    <th style="vertical-align:top; text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;" nowrap>
	                        PREVIOUS
	                    </th>
	                    <th style="vertical-align:top; text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;" nowrap>
	                        CURRENT
	                    </th>
	                    <th style="vertical-align:top; text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;" nowrap>
	                        URL
	                    </th>
	                </tr>
	                </thead>
	                <tbody>
            #foreach($targetUrlChangeAlertDetails in $targetUrlChangeAlertDetailsList)
            	#foreach($changeIndicatorPreviousCurrent in $targetUrlChangeAlertDetails.changeIndicatorPreviousCurrentList)
	                <tr>
	                    <td style="vertical-align:top; border: 1px #FFE5B9 solid; padding: 5px;" nowrap>
	                    	$!changeIndicatorPreviousCurrent.changeIndicatorDesc
	                    </td>
	                    <td style="vertical-align:top; border: 1px #FFE5B9 solid; padding: 5px;" nowrap>
                        	$!changeIndicatorPreviousCurrent.currentCrawlTimestamp
                        </td>
	                    <td style="vertical-align:top; border: 1px #FFE5B9 solid; padding: 5px;" nowrap>
	                    	$!changeIndicatorPreviousCurrent.severity
	                    </td>
	                    <td style="vertical-align:top; border: 1px #FFE5B9 solid; padding: 5px;" nowrap>
	                    	$!changeIndicatorPreviousCurrent.previous
	                    </td>
	                    <td style="vertical-align:top; border: 1px #FFE5B9 solid; padding: 5px;" nowrap>
	                    	$!changeIndicatorPreviousCurrent.current
	                    </td>
	                    <td style="vertical-align:top; border: 1px #FFE5B9 solid; padding: 5px;" nowrap>
	                    	$!changeIndicatorPreviousCurrent.url
	                    </td>
	                </tr> 
            	#end          
            #end 
	                </tbody>
	            </table>
        #end
        	<p>
          		<span style="text-align: left; font-size: 12px; color: black;">${informationalMessage}</span><br>
        	</p>
        </div>
    </div>

    <!-- foot -->
    <div style="border-top:3px #f79646 solid; padding-top:15px; text-align: center; line-hright:1.5;">

        $!{agencyInfo.emailFooter.address} <br/>
        <div style="color:#777;">
            Support: <a href="mailto:$!{agencyInfo.emailReplyto}">$!{agencyInfo.emailReplyto}</a> <br/>
            Phone: $!{agencyInfo.emailFooter.phone} <br/>
            Fax: $!{agencyInfo.emailFooter.fax}
        </div>

        <input type="hidden" value="$!hostName"/>

    </div>

</div>

