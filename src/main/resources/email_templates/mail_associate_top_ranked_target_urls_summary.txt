<div style="margin:0 auto; width:880px; color:#333; font:12px/1.5 Verdana, Geneva, sans-serif;">

    <!-- head -->
    <div style="border-bottom: 3px #f79646 solid; padding: 5px 10px;">
        <span style="padding-top:10px; font-weight:bold; color:#777;">$!etHeadTitle </span>
        <a target="_blank" href="$!{agencyInfo.websiteDomain}">
            <img style="border: none;" src="$!{agencyInfo.websiteDomain}/images/company/$!{agencyInfo.logoImage}/logo_email.png" title="$!{agencyInfo.companyTitle} Logo" alt="$!{agencyInfo.companyTitle} Logo" height="30" />
        </a>
    </div>

    <!-- body -->
    <div style="padding:25px 10px;">
        <div style="margin:15px 0;">        
        #if( $associateTopRankedTargetUrlSummaryValueObjectList.size() > 0 )
            <p>
            	<br><span style="font-size: 12px; font-weight: bold; color: black;">Process timestamp: $processTimestamp</span>
            	<br><span style="font-size: 12px; font-weight: bold; color: black;">Rank data date: $rankDate</span>
            	<br><span style="font-size: 12px; font-weight: bold; color: black;">Total domains processed: $totalDomainProcessed</span>
            </p>
	            <table style="border-collapse: collapse; border-spacing: 0; border: 1px #FFE5B9 solid; width: 100%; font-size: 12px; margin: 10px auto;">
	                <thead>
	                <tr>
	                    <th style="text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;">
	                        Domain ID
	                    </th>
	                    <th style="text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;">
	                        Domain Name
	                    </th>
	                    <th style="text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;">
	                        Ranked
	                    </th>
	                    <th style="text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;">
	                        Ranked With ID
	                    </th>
	                    <th style="text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;">
	                        Ranked Without ID
	                    </th>
	                    <th style="text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;">
	                        Associated Before
	                    </th>
	                    <th style="text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;">
	                        Already Type 1
	                    </th>
	                    <th style="text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;">
	                        Associated by Update (Status & Type)
	                    </th>
	                    <th style="text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;">
	                        Exception
	                    </th>
	                </tr>
	                </thead>
	                <tbody>
            #foreach($associateTopRankedTargetUrlSummary in $associateTopRankedTargetUrlSummaryValueObjectList)
	                <tr>
	                    <td style="border: 1px #FFE5B9 solid; padding: 5px;">
	                    	$!associateTopRankedTargetUrlSummary.domainId
	                    </td>
	                    <td style="border: 1px #FFE5B9 solid; padding: 5px;">
	                    	$!associateTopRankedTargetUrlSummary.domainName
	                    </td>
	                    <td style="border: 1px #FFE5B9 solid; padding: 5px;">
	                    	$!associateTopRankedTargetUrlSummary.totalTargetUrlsRanked
	                    </td>
	                    <td style="border: 1px #FFE5B9 solid; padding: 5px;">
	                    	$!associateTopRankedTargetUrlSummary.totalRankedUrlsHaveTargetUrlId
	                    </td>
	                    <td style="border: 1px #FFE5B9 solid; padding: 5px;">
	                    	$!associateTopRankedTargetUrlSummary.totalRankedUrlsDoNotHaveTargetUrlId
	                    </td>
	                    <td style="border: 1px #FFE5B9 solid; padding: 5px;">
	                    	$!associateTopRankedTargetUrlSummary.totalTargetUrlsAssociatedBefore
	                    </td>
	                    <td style="border: 1px #FFE5B9 solid; padding: 5px;">
	                    	$!associateTopRankedTargetUrlSummary.totalTargetUrlsAlreadyType1
	                    </td>
	                    <td style="border: 1px #FFE5B9 solid; padding: 5px;">
	                    	$!associateTopRankedTargetUrlSummary.totalTargetUrlsUpdatedWithoutUrl
	                    </td>
	                    <td style="border: 1px #FFE5B9 solid; padding: 5px;">
	                    	$!associateTopRankedTargetUrlSummary.exceptionMessage
	                    </td>
	                </tr>           
            #end
	                </tbody>
	            </table> 
        #end
        
        
        </div>
    </div>

    <!-- foot -->
    <div style="border-top:3px #f79646 solid; padding-top:15px; text-align: center; line-hright:1.5;">

        $!{agencyInfo.emailFooter.address} <br/>
        <div style="color:#777;">
            Support: <a href="mailto:$!{agencyInfo.emailReplyto}">$!{agencyInfo.emailReplyto}</a> <br/>
            Phone: $!{agencyInfo.emailFooter.phone} <br/>
            Fax: $!{agencyInfo.emailFooter.fax}
        </div>

        <input type="hidden" value="$!hostName"/>

    </div>
    
    <p style="text-align:center;border: 1px #ddd dotted;margin-top: 40px;font-style: italic;font-size: x-small;font-family: 'Proxima Nova', 'Open Sans', 'Helvetica Neue', Calibri, Helvetica, sans-serif;">
    Do not wish to receive these emails? <br/> Simply write to <a href="mailto:<EMAIL>"><EMAIL></a> to have these emails disabled for your account
    </p>

</div>
