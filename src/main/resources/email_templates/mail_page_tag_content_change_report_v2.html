<div style="margin:0 auto; width:550px; color:#333; font:12px/1.5 Verdana, Geneva, sans-serif;">

    <!-- head -->
    <div style="border-bottom: 3px #f79646 solid; padding: 5px 10px;">
        <span style="padding-top:10px; font-weight:bold; color:#777;">$!etHeadTitle </span>
        <a target="_blank" href="$!{agencyInfo.websiteDomain}">
            <img style="border: none;" src="$!{agencyInfo.websiteDomain}/images/company/$!{agencyInfo.logoImage}/logo_email.png" title="$!{agencyInfo.companyTitle} Logo" alt="$!{agencyInfo.companyTitle} Logo" height="30" />
        </a>
    </div>

    <!-- body -->
    <div style="padding:25px 10px;">

        <!-- body cnt-->
        <div style="margin:15px 0;">

            <p>Below is a summary of the content changes detected on ${processDate} for the page tags being tracked in $!{agencyInfo.companyTitle}.</p>
            <p>Content change threshold is ${contentChangeThreshold} character(s). Page tag level change threshold is ${tagLevelChangeThreshold}%.</p>
            
            #foreach($pageTagAlertEmailValueObject in $pageTagAlertEmailValueObjectList)
	
	            <table style="border-collapse: collapse; border-spacing: 0; border: 1px #FFE5B9 solid; width: 100%; font-size: 13px; margin: 10px auto;">
	                <thead>
	                <tr>
	                    <th style="text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;">
	                        Page Tag
	                    </th>
	                    <th style="text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;">
	                        URLs Changed
	                    </th>
	                    <th style="text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;">
	                        URLs in Tag
	                    </th>
	                    <th style="text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;">
	                        % Changed
	                    </th>
	                </tr>
	                </thead>
	                <tbody>
	                <tr>
	                    <td style="border: 1px #FFE5B9 solid; padding: 5px;">
	                        <a href="https://app.seoclarity.net/target/listbytag.do?gtid=$!pageTagAlertEmailValueObject.groupTagId&stChgDt=${processDateMMDDYY}&endChgDt=${processDateMMDDYY}&oid=${domainId}">$!pageTagAlertEmailValueObject.groupTagName</a>
	                    </td>
	                    <td style="border: 1px #FFE5B9 solid; padding: 5px;">
	                    	$!pageTagAlertEmailValueObject.totalTargetUrlsRequireAlert
	                    </td>
	                    <td style="border: 1px #FFE5B9 solid; padding: 5px;">
	                    	$!pageTagAlertEmailValueObject.totalTargetUrlsInGroupTag
	                    </td>
	                    <td style="border: 1px #FFE5B9 solid; padding: 5px;">
	                    	$!pageTagAlertEmailValueObject.percentTargetUrlsInGroupTag
	                    </td>
	                </tr>
	                </tbody>
	            </table>
            
	            #if( $pageTagAlertEmailValueObject.contentChangeAlertEmailList.size() > 0 )
	
	            <table style="border-collapse: collapse; border-spacing: 0; border: 1px #FFE5B9 solid; width: 60%; font-size: 13px; margin: 10px auto;">
	                <thead>
	                <tr>
	                    <th style="text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;">
	                        Metric Content
	                    </th>
	                    <th style="text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;">
	                        URLs Changed
	                    </th>
	                </tr>
	                </thead>
	                <tbody>
	                #foreach($contentChangeAlertEmail in $pageTagAlertEmailValueObject.contentChangeAlertEmailList)
	                <tr>
	                    <td style="text-align: left; border: 1px #FFE5B9 solid; padding: 5px;">
	                        $!contentChangeAlertEmail.alertName
	                    </td>
	                    <td style="text-align: left; border: 1px #FFE5B9 solid; padding: 5px;">
	                    	$!contentChangeAlertEmail.totalTargetUrlsWithContentChanges
	                    </td>
	                </tr>
	                #end
	                </tbody>
	            </table>
	            #end
            
	            #if( $pageTagAlertEmailValueObject.responseCodeAlertEmailList.size() > 0 )
	            <table style="border-collapse: collapse; border-spacing: 0; border: 1px #FFE5B9 solid; width: 60%; font-size: 13px; margin: 10px auto;">
	                <thead>
	                <tr>
	                    <th style="text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;">
	                        Response Code
	                    </th>
	                    <th style="text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;">
	                        URLs Changed
	                    </th>
	                </tr>
	                </thead>
	                <tbody>
	                #foreach($responseCodeChangeAlertEmail in $pageTagAlertEmailValueObject.responseCodeAlertEmailList)
	                <tr>
	                    <td style="text-align: left; border: 1px #FFE5B9 solid; padding: 5px;">
	                        $!responseCodeChangeAlertEmail.responseCodeChange
	                    </td>
	                    <td style="text-align: left; border: 1px #FFE5B9 solid; padding: 5px;">
	                    	$!responseCodeChangeAlertEmail.totalTargetUrlsWithContentChanges
	                    </td>
	                </tr>
	                #end
	                </tbody>
	            </table>
	            #end
            
	            #if( $pageTagAlertEmailValueObject.divContentChangeAlertEmailList.size() > 0 )
	            <table style="border-collapse: collapse; border-spacing: 0; border: 1px #FFE5B9 solid; width: 60%; font-size: 13px; margin: 10px auto;">
	                <thead>
	                <tr>
	                    <th style="text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;">
	                        DIV Content
	                    </th>
	                    <th style="text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;">
	                        URLs Changed
	                    </th>
	                </tr>
	                </thead>
	                <tbody>
	                #foreach($divContentChangeAlertEmail in $pageTagAlertEmailValueObject.divContentChangeAlertEmailList)
	                <tr>
	                    <td style="text-align: left; border: 1px #FFE5B9 solid; padding: 5px;">
	                        $!divContentChangeAlertEmail.divContentChange
	                    </td>
	                    <td style="text-align: left; border: 1px #FFE5B9 solid; padding: 5px;">
	                    	$!divContentChangeAlertEmail.totalTargetUrlsWithContentChanges
	                    </td>
	                </tr>
	                #end
	                </tbody>
	            </table>
	            #end
            
	            #if( $pageTagAlertEmailValueObject.additionalContentChangeAlertEmailList.size() > 0 )
	            <table style="border-collapse: collapse; border-spacing: 0; border: 1px #FFE5B9 solid; width: 60%; font-size: 13px; margin: 10px auto;">
	                <thead>
	                <tr>
	                    <th style="text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;">
	                        Additional Content
	                    </th>
	                    <th style="text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;">
	                        URLs Changed
	                    </th>
	                </tr>
	                </thead>
	                <tbody>
	                #foreach($additionalContentChangeAlertEmail in $pageTagAlertEmailValueObject.additionalContentChangeAlertEmailList)
	                <tr>
	                    <td style="text-align: left; border: 1px #FFE5B9 solid; padding: 5px;">
	                        $!additionalContentChangeAlertEmail.additionalContentChange
	                    </td>
	                    <td style="text-align: left; border: 1px #FFE5B9 solid; padding: 5px;">
	                    	$!additionalContentChangeAlertEmail.totalTargetUrlsWithContentChanges
	                    </td>
	                </tr>
	                #end
	                </tbody>
	            </table>
	            #end
            
            #end

        </div>

    </div>

    <!-- foot -->
    <div style="border-top:3px #f79646 solid; padding-top:15px; text-align: center; line-hright:1.5;">

        $!{agencyInfo.emailFooter.address} <br/>
        <div style="color:#777;">
            Support: <a href="mailto:$!{agencyInfo.emailReplyto}">$!{agencyInfo.emailReplyto}</a> <br/>
            Phone: $!{agencyInfo.emailFooter.phone} <br/>
            Fax: $!{agencyInfo.emailFooter.fax}
        </div>

        <input type="hidden" value="$!hostName"/>

    </div>

    <p style="text-align:center;border: 1px #ddd dotted;margin-top: 40px;font-style: italic;font-size: x-small;font-family: 'Proxima Nova', 'Open Sans', 'Helvetica Neue', Calibri, Helvetica, sans-serif;">
    Do not wish to receive these emails? <br/> Simply write to <a href="mailto:<EMAIL>"><EMAIL></a> to have these emails disabled for your account
    </p>

</div>

