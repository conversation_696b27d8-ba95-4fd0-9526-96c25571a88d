<div style="margin:0 auto; width:550px; color:#333; font:12px/1.5 Verdana, Geneva, sans-serif;">

    <!-- head -->
    <div style="border-bottom: 3px #f79646 solid; padding: 5px 10px;">
        <span style="float:right; padding-top:10px; font-weight:bold; color:#777;">$!etHeadTitle</span>

        <a target="_blank" href="$!{agencyInfo.websiteDomain}">
            <img style="border: none;" src="$!{agencyInfo.websiteDomain}/images/company/$!{agencyInfo.logoImage}/logo_email.png" title="$!{agencyInfo.companyTitle} Logo" alt="$!{agencyInfo.companyTitle} Logo" height="30" />
        </a>
    </div>

    <!-- body -->
    <div style="padding:25px 10px;">
        <span style="font-size: 20px; font-weight: bold; color: black;">Hi ${userEmail},</span>

        <!-- body cnt-->
        <div style="margin:15px 0;">

                <p>
                    The crawl you have requested for ${domainName} has been completed. 
                </p>

            <table style="border-collapse: collapse; border-spacing: 0; border: 1px #FFE5B9 solid; width: 100%; font-size: 13px; margin: 10px auto;">
                <thead>
                <tr >
                    <th style="text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;">
                        Crawl Request Information
                    </th>
                </tr>
                </thead>
                <tbody>
                
                #if( $crawlRequestLog.startingUrl && $crawlRequestLog.startingUrl > '')
                <tr>
                    <td style="border: 1px #FFE5B9 solid; padding: 5px;">
                   Starting URL: <b> ${crawlRequestLog.startingUrl}</b></td>
                </tr>
				#end
                
                #if( $crawlRequestUrlEntityList )  
					#foreach( $reportItem in $crawlRequestUrlEntityList )				
                	<tr>
                    	<td style="border: 1px #FFE5B9 solid; padding: 5px;">
                   			Starting URL: <b> ${reportItem.startingUrl}</b></td>
                	</tr>
					#end
				#end
                
                <tr >
                    <td style="border: 1px #FFE5B9 solid; padding: 5px;">
                        Speed: <b>${crawlRequestLog.crawlSpeed}</b></td>
                </tr>
                <tr >
                    <td style="border: 1px #FFE5B9 solid; padding: 5px;">
                        Depth: <b>${crawlRequestLog.depth}</b></td>
                </tr>
                <tr >
                    <td style="border: 1px #FFE5B9 solid; padding: 5px;">
                        Crawl Description: <b>${crawlRequestLog.crawlDescription}</b></td>
                </tr>
                <tr >
                    <td style="border: 1px #FFE5B9 solid; padding: 5px;">
                        Number of pages crawled: <b>${crawlPageCount}</b></td>
                </tr>
                </tbody>
            </table>


        </div>

        Sincerely,<br/>
        $!{agencyInfo.companyTitle}

    </div>

    <!-- foot -->
    <div style="border-top:3px #f79646 solid; padding-top:15px; text-align: center; line-hright:1.5;">

        $!{agencyInfo.emailFooter.address} <br/>
        <div style="color:#777;">
            Support: <a href="mailto:$!{agencyInfo.emailReplyto}">$!{agencyInfo.emailReplyto}</a> <br/>
            Phone: $!{agencyInfo.emailFooter.phone} <br/>
            Fax: $!{agencyInfo.emailFooter.fax}
        </div>

        <input type="hidden" value="$!hostName"/>

    </div>

</div>


