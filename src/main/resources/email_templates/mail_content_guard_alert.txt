<div style="margin:0 auto; width:880px; color:#333; font:12px/1.5 Verdana, Geneva, sans-serif;">

    <!-- head -->
    <div style="border-bottom: 3px #f79646 solid; padding: 5px 10px;">
        <span style="padding-top:10px; font-weight:bold; color:#777;">$!etHeadTitle </span>
        <a target="_blank" href="$!{agencyInfo.websiteDomain}">
            <img style="border: none;" src="$!{agencyInfo.websiteDomain}/images/company/$!{agencyInfo.logoImage}/logo_email.png" title="$!{agencyInfo.companyTitle} Logo" alt="$!{agencyInfo.companyTitle} Logo" height="30" />
        </a>
    </div>

    <!-- body -->
    <div style="padding:25px 10px;">
    
        <div style="margin:15px 0;">            
        		<p>
          			<span style="text-align: left; font-size: 12px; color: black;">${groupInformation1}.</span><br>
          			<span style="text-align: left; font-size: 12px; color: black;">${groupInformation2}.</span><br>
          			<span style="text-align: left; font-size: 12px; color: black;">${groupInformation3}.</span><br>
          			<span style="text-align: left; font-size: 12px; color: black;">The domain ID is ${domainId}.</span>
        		</p>
            #foreach($contentGuardAlertSummary in $contentGuardAlertSummaryList)
        		<br>
        		<br>
        		<p>
          			<span style="text-align: left; font-size: 12px; color: black;"><b>Alert:&nbsp;$!contentGuardAlertSummary.changeIndicatorDesc</b></span>
        		</p>
        		
        		 #if($!contentGuardAlertSummary.changeIndicatorDesc == 'Alternate links changed' || $!contentGuardAlertSummary.changeIndicatorDesc == 'Custom data added' || $!contentGuardAlertSummary.changeIndicatorDesc == 'Custom data changed' || $!contentGuardAlertSummary.changeIndicatorDesc == 'Custom data removed' || $!contentGuardAlertSummary.changeIndicatorDesc == 'Hreflang errors changed' || $!contentGuardAlertSummary.changeIndicatorDesc == 'Hreflang links changed' || $!contentGuardAlertSummary.changeIndicatorDesc == 'Hreflang links changed' || $!contentGuardAlertSummary.changeIndicatorDesc == 'Open graph markup changed' || $!contentGuardAlertSummary.changeIndicatorDesc == 'Page analysis results changed' || $!contentGuardAlertSummary.changeIndicatorDesc == 'Page links changed' || $!contentGuardAlertSummary.changeIndicatorDesc == 'Redirect chain changed' || $!contentGuardAlertSummary.changeIndicatorDesc == 'Structured data changed')
        		<!-- <table style="border-collapse: collapse; border-spacing: 0; border: 1px #FFE5B9 solid; font-size: 12px; margin: 10px auto;"> --> 
	            <table style="border-collapse: collapse; border-spacing: 0; border: 1px #FFE5B9 solid; font-size: 12px;">
	                <thead>
	                <tr>
	                    <th style="vertical-align:top; text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;">
	                        URL
	                    </th>
	                </tr>
	                </thead>
	                <tbody>
            		#foreach($contentGuardAlertDetails in $contentGuardAlertSummary.contentGuardAlertDetailsList) 
	                <tr>
	                    <td style="vertical-align:top; border: 1px #FFE5B9 solid; padding: 5px;">	                    
	                    	<a href="$!contentGuardAlertDetails.url">$!contentGuardAlertDetails.url</a>	                    	
	                    </td>
	                </tr>
            		#end
	                </tbody>
	            </table>
	            #else
        		<!-- <table style="border-collapse: collapse; border-spacing: 0; border: 1px #FFE5B9 solid; font-size: 12px; margin: 10px auto;"> --> 
	            <table style="border-collapse: collapse; border-spacing: 0; border: 1px #FFE5B9 solid; font-size: 12px;">
	                <thead>
	                <tr>
	                    <th style="vertical-align:top; text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;">
	                        URL
	                    </th>
	                    <th style="vertical-align:top; text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;">
	                        Previous
	                    </th>
	                    <th style="vertical-align:top; text-align: left; border: 1px #FFE5B9 solid; background: #FFF6E8; color: #555; padding: 5px;">
	                        Current
	                    </th>
	                </tr>
	                </thead>
	                <tbody>
            		#foreach($contentGuardAlertDetails in $contentGuardAlertSummary.contentGuardAlertDetailsList) 
	                <tr>
	                    <td style="vertical-align:top; border: 1px #FFE5B9 solid; padding: 5px;">	                    
	                    	<a href="$!contentGuardAlertDetails.url">$!contentGuardAlertDetails.url</a>
	                    </td>
	                    #if($!contentGuardAlertSummary.changeIndicatorDesc == 'Analyzed URL changed' || $!contentGuardAlertSummary.changeIndicatorDesc == 'Canonical changed' || $!contentGuardAlertSummary.changeIndicatorDesc == 'Canonical added' || $!contentGuardAlertSummary.changeIndicatorDesc == 'Canonical removed')
	                    <td style="vertical-align:top; border: 1px #FFE5B9 solid; padding: 5px;">                 
	                    	<a href="$!contentGuardAlertDetails.previousContent">$!contentGuardAlertDetails.previousContent</a>
	                    </td>
	                    <td style="vertical-align:top; border: 1px #FFE5B9 solid; padding: 5px;">                 
	                    	<a href="$!contentGuardAlertDetails.currentContent">$!contentGuardAlertDetails.currentContent</a>                    	
	                    </td>
	                    #else
	                    <td style="vertical-align:top; border: 1px #FFE5B9 solid; padding: 5px;">
	                    	$!contentGuardAlertDetails.previousContent
	                    </td>
	                    <td style="vertical-align:top; border: 1px #FFE5B9 solid; padding: 5px;">
	                    	$!contentGuardAlertDetails.currentContent
	                    </td>
	                    #end
	                </tr>
            		#end
	                </tbody>
	            </table>
	            #end
            #end 
        </div> 

    </div>

    <!-- foot -->
    <div style="border-top:3px #f79646 solid; padding-top:15px; text-align: center; line-height:1.5;">

        $!{agencyInfo.emailFooter.address} <br/>
        <div style="color:#777;">
            Support: <a href="mailto:$!{agencyInfo.emailReplyto}">$!{agencyInfo.emailReplyto}</a> <br/>
            Phone: $!{agencyInfo.emailFooter.phone} <br/>
            Fax: $!{agencyInfo.emailFooter.fax}
        </div>

        <input type="hidden" value="$!hostName"/>

    </div>
    
    <p style="text-align:center;border: 1px #ddd dotted;margin-top: 40px;font-style: italic;font-size: x-small;font-family: 'Proxima Nova', 'Open Sans', 'Helvetica Neue', Calibri, Helvetica, sans-serif;">
    Do not wish to receive these emails? <br/> Simply write to <a href="mailto:<EMAIL>"><EMAIL></a> to have these emails disabled for your account
    </p>

</div>
