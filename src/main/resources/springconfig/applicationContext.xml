<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.0.xsd
           http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-2.0.xsd
           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.0.xsd"
	default-autowire="byName" default-lazy-init="true">

	<!-- ============== PROPERTIES FILES ================ -->
	<bean id="propertyConfigurer"
		class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
		<property name="locations">
			<list>
				<value>classpath:jdbc.properties</value>
				<value>classpath:subserver.properties</value>
				<value>classpath:adwords.properties</value>
				<value>classpath:mailjet.properties</value>
			</list>
		</property>
	</bean>

	<bean id="wsJobFactory" class="com.actonia.subserver.utils.WsJobFactory">
	</bean>

	<bean id="keywordRankHandle" class="com.actonia.subserver.KeywordRankHandle">
		<property name="subServerIp" value="${subserver.ip}" />
		<property name="subServerEntityDAO">
			<ref bean="subServerEntityDAO" />
		</property>
		<property name="keywordGoogleQueryEntityDAO">
			<ref bean="keywordGoogleQueryEntityDAO" />
		</property>
		<property name="keywordGoogleQueryRankEntityDAO">
			<ref bean="keywordGoogleQueryRankEntityDAO" />
		</property>
		<property name="jobFactory">
			<ref bean="wsJobFactory" />
		</property>
	</bean>

	<bean id="partnerHandle" class="com.actonia.subserver.PartnerHandle">
		<property name="subServerIp" value="${subserver.ip}" />
		<property name="tryCountIfBlock" value="${subserver.tryCountIfBlock}" />
		<property name="subServerEntityDAO">
			<ref bean="subServerEntityDAO" />
		</property>
		<property name="logEntityDAO">
			<ref bean="logEntityDAO" />
		</property>
		<property name="jobFactory">
			<ref bean="wsJobFactory" />
		</property>
		<property name="partnerEntityDAO">
			<ref bean="partnerEntityDAO" />
		</property>
	</bean>

	<bean id="googleAdwordsConfiguration" class="com.actonia.subserver.vo.GoogleAdwordsConfiguration">
		<property name="localeCode" value="${google.adwords.config.localeCode}" />
		<property name="currencyCode" value="${google.adwords.config.currencyCode}" />
		<property name="country" value="${google.adwords.config.country}" />
		<property name="language" value="${google.adwords.config.language}" />
	</bean>

	<bean id="googleAdwordsKeyword" class="com.actonia.subserver.GoogleAdwordsKeyword">
		<property name="keywordDictionaryEntityDAO">
			<ref bean="adwordsKeywordDictionaryEntityDAO" />
		</property>
		<property name="keywordAdwordsDataEntityDAO">
			<ref bean="keywordAdwordsDataEntityDAO" />
		</property>
		<property name="googleAdwordsConfiguration">
			<ref bean="googleAdwordsConfiguration" />
		</property>
		<property name="developerToken" value="${google.adwords.developertoken}" />
		<property name="email" value="${google.adwords.email}" />
		<property name="password" value="${google.adwords.password}" />
		<property name="userAgent" value="${google.adwords.useragent}" />
		<property name="ipFlag" value="${google.adwords.ipflag}" />
	</bean>
	
	<!-- SeoClarity Adwords for new rankcheck database -->
	<bean id="scGoogleAdwords" class="com.actonia.subserver.ScGoogleAdwords">
		<property name="seoClarityKeywordEntityDAO">
			<ref bean="seoClarityKeywordEntityDAO" />
		</property>
		<property name="scKeywordAdwordsDataEntityDAO">
			<ref bean="scKeywordAdwordsDataEntityDAO" />
		</property>
		<property name="developerToken" value="${google.adwords.developertoken}" />
		<property name="email" value="${google.adwords.email}" />
		<property name="password" value="${google.adwords.password}" />
		<property name="userAgent" value="${google.adwords.useragent}" />
		<property name="ipFlag" value="${google.adwords.ipflag}" />
	</bean>
	
	<!-- SeoClarity Adwords for new rankcheck database -->
	<bean id="scGoogleAdwordsForOwnDomain" class="com.actonia.subserver.ScGoogleAdwordsForOwnDomain">
		<property name="seoClarityKeywordEntityDAO">
			<ref bean="seoClarityKeywordEntityDAO" />
		</property>
		<property name="scKeywordAdwordsDataEntityDAO">
			<ref bean="scKeywordAdwordsDataEntityDAO" />
		</property>
		<property name="developerToken" value="${google.adwords.developertoken}" />
		<property name="email" value="${google.adwords.email}" />
		<property name="password" value="${google.adwords.password}" />
		<property name="userAgent" value="${google.adwords.useragent}" />
		<property name="ipFlag" value="${google.adwords.ipflag}" />
		<property name="ownDomainEntityDAO">
			<ref bean="ownDomainEntityDAO" />
		</property>
		<property name="keywordEntityDAO">
			<ref bean="keywordEntityDAO" />
		</property>
		<property name="scKeywordRankManager">
			<ref bean="scKeywordRankManager" />
		</property>
	</bean>
	
	<bean id="googleAdwordsKeyword2" class="com.actonia.subserver.GoogleAdwordsKeyword2">
		<property name="keywordDictionaryEntityDAO">
			<ref bean="adwordsKeywordDictionaryEntityDAO" />
		</property>
		<property name="keywordAdwordsDataEntityDAO">
			<ref bean="keywordAdwordsDataEntityDAO" />
		</property>
		<property name="googleAdwordsConfiguration">
			<ref bean="googleAdwordsConfiguration" />
		</property>
		<property name="developerToken" value="${google.adwords.developertoken}" />
		<property name="email" value="${google.adwords.email}" />
		<property name="password" value="${google.adwords.password}" />
		<property name="userAgent" value="${google.adwords.useragent}" />
		<property name="ipFlag" value="${google.adwords.ipflag}" />
	</bean>
	
	<bean id="googleAdwordsKeywordForAU" class="com.actonia.subserver.GoogleAdwordsKeyword2">
		<property name="keywordDictionaryEntityDAO">
			<ref bean="keywordDictionaryAUEntityDAO" />
		</property>
		<property name="keywordAdwordsDataEntityDAO">
			<ref bean="keywordAdwordsDataAUEntityDAO" />
		</property>
		<property name="googleAdwordsConfiguration">
			<ref bean="googleAdwordsConfiguration" />
		</property>
		<property name="developerToken" value="${google.adwords.developertoken}" />
		<property name="email" value="${google.adwords.email}" />
		<property name="password" value="${google.adwords.password}" />
		<property name="userAgent" value="${google.adwords.useragent}" />
		<property name="ipFlag" value="${google.adwords.ipflag}" />
	</bean>

	<bean id="keywordRankTools" class="com.actonia.subserver.KeywordRankTools">
		<property name="subServerIp" value="${subserver.ip}" />
		<property name="subServerEntityDAO">
			<ref bean="subServerEntityDAO" />
		</property>
		<property name="keywordGoogleQueryEntityDAO">
			<ref bean="keywordGoogleQueryEntityDAO" />
		</property>
		<property name="keywordGoogleQueryRankEntityDAO">
			<ref bean="keywordGoogleQueryRankEntityDAO" />
		</property>
		<property name="keywordEntityDAO">
			<ref bean="keywordEntityDAO" />
		</property>
		<property name="keywordDictionaryEntityDAO">
			<ref bean="keywordDictionaryEntityDAO" />
		</property>
	</bean>

	<!-- for customize alert -->
	<bean id="keywordCustomizeAlertImpl" class="com.actonia.subserver.custalert.KeywordCustAlertImpl"
		scope="prototype">
		<property name="alertLogDAO">
			<ref bean="alertLogDAO" />
		</property>
		<property name="groupTagEntityDAO">
			<ref bean="groupTagEntityDAO" />
		</property>
		<property name="alertRuleConditionDAO">
			<ref bean="alertRuleConditionDAO" />
		</property>
		<property name="ownDomainEntityDAO">
			<ref bean="ownDomainEntityDAO" />
		</property>
		<property name="keywordEntityDAO">
			<ref bean="keywordEntityDAO" />
		</property>
		<property name="keywordConversionGaEntityDAO">
			<ref bean="keywordConversionGaEntityDAO" />
		</property>
		<property name="keywordStatGaDAO">
			<ref bean="keywordStatGaDAO" />
		</property>
		<property name="scKeywordRankManager">
			<ref bean="scKeywordRankManager" />
		</property>
		<property name="managedKeywordDAOInfoB">
			<ref bean="managedKeywordDAOInfoB" />
		</property>		
	</bean>

	<bean id="crawlEngine"
		class="com.actonia.subserver.multithread.core.common.CrawlEngine">
		<property name="keywordDictionaryEntityDAO">
			<ref bean="keywordDictionaryEntityDAO" />
		</property>
		<property name="keywordGoogleQueryEntityDAO">
			<ref bean="keywordGoogleQueryEntityDAO" />
		</property>
		<property name="keywordGoogleQueryRankEntityDAO">
			<ref bean="keywordGoogleQueryRankEntityDAO" />
		</property>
		<property name="emailSenderComponent">
			<ref bean="emailSenderComponent" />
		</property>
	</bean>

	<bean id="crawlHtmlContentEngine"
		class="com.actonia.subserver.multithread.core.common.CrawlHtmlContentEngine">
		<property name="targetUrlEntityDAO">
			<ref bean="targetUrlEntityDAO" />
		</property>
		<property name="logEntityDAO">
			<ref bean="logEntityDAO" />
		</property>
		<property name="logPageEntityDAO">
			<ref bean="logPageEntityDAO" />
		</property>
		<property name="optionEntityDAO">
			<ref bean="optionEntityDAO" />
		</property>
		<property name="emailSenderComponent">
			<ref bean="emailSenderComponent" />
		</property>
		<property name="notificationEntityDAO">
			<ref bean="notificationEntityDAO" />
		</property>
		<property name="urlMetricsDataEntityDAO">
			<ref bean="urlMetricsDataEntityDAO" />
		</property>
		<property name="eventEntityDAO">
			<ref bean="eventEntityDAO" />
		</property>
		<property name="eventElementRelEntityDAO">
			<ref bean="eventElementRelEntityDAO" />
		</property>	
		<property name="taskService">
			<ref bean="taskService" />
		</property>							
	</bean>

	<bean id="crawlPREngine"
		class="com.actonia.subserver.multithread.core.common.CrawlPREngine">
		<property name="targetUrlEntityDAO">
			<ref bean="targetUrlEntityDAO" />
		</property>
		<property name="partnerUrlEntityDAO">
			<ref bean="partnerUrlEntityDAO" />
		</property>
		<property name="competitorUrlEntityDAO">
			<ref bean="competitorUrlEntityDAO" />
		</property>
		<property name="logEntityDAO">
			<ref bean="logEntityDAO" />
		</property>
		<property name="urlMetricsDataEntityDAO">
			<ref bean="urlMetricsDataEntityDAO" />
		</property>
		<property name="emailSenderComponent">
			<ref bean="emailSenderComponent" />
		</property>
	</bean>

	<bean id="crawlCacheDateEngine"
		class="com.actonia.subserver.multithread.core.common.CrawlCacheDateEngine">
		<property name="targetUrlEntityDAO">
			<ref bean="targetUrlEntityDAO" />
		</property>
		<property name="partnerUrlEntityDAO">
			<ref bean="partnerUrlEntityDAO" />
		</property>
		<property name="competitorUrlEntityDAO">
			<ref bean="competitorUrlEntityDAO" />
		</property>
		<property name="logEntityDAO">
			<ref bean="logEntityDAO" />
		</property>
		<property name="urlMetricsDataEntityDAO">
			<ref bean="urlMetricsDataEntityDAO" />
		</property>
		<property name="emailSenderComponent">
			<ref bean="emailSenderComponent" />
		</property>
	</bean>

	<bean id="crawlGooglePageNumber" class="com.actonia.subserver.crawl.CrawlGooglePageNumber">
		<property name="ownDomainEntityDAO">
			<ref bean="ownDomainEntityDAO" />
		</property>
		<property name="partnerEntityDAO">
			<ref bean="partnerEntityDAO" />
		</property>
		<property name="competitorEntityDAO">
			<ref bean="competitorEntityDAO" />
		</property>
		<property name="partnerDomainDailySummaryEntityDAO">
			<ref bean="partnerDomainDailySummaryEntityDAO" />
		</property>
		<property name="competitorDomainDailySummaryEntityDAO">
			<ref bean="competitorDomainDailySummaryEntityDAO" />
		</property>
		<property name="domainDailySummaryEntityDAO">
			<ref bean="domainDailySummaryEntityDAO" />
		</property>
		<property name="emailSenderComponent">
			<ref bean="emailSenderComponent" />
		</property>
	</bean>

	<bean id="csvParserEngine" class="com.actonia.subserver.csvparser.CsvParserEngine">
		<property name="keywordEntityDAO">
			<ref bean="keywordEntityDAO" />
		</property>
		<property name="targetUrlEntityDAO">
			<ref bean="targetUrlEntityDAO" />
		</property>
		<property name="ownDomainEntityDAO">
			<ref bean="ownDomainEntityDAO" />
		</property>
		<property name="keywordStatGaDAO">
			<ref bean="keywordStatGaDAO" />
		</property>
		<property name="emailSenderComponent">
			<ref bean="emailSenderComponent" />
		</property>
		<property name="tempImportDataEntityDAO">
			<ref bean="tempImportDataEntityDAO" />
		</property>
		<property name="tempImportDataDailySummaryDAO">
			<ref bean="tempImportDataDailySummaryDAO" />
		</property>
		<property name="domainDailySummaryEntityDAO">
			<ref bean="domainDailySummaryEntityDAO" />
		</property>
		<property name="keywordConversionGaEntityDAO">
			<ref bean="keywordConversionGaEntityDAO" />
		</property>
	</bean>

	<bean id="crawlCompetitorHtmlContentEngine"
		class="com.actonia.subserver.multithread.core.common.CrawlCompetitorHtmlContentEngine">
		<property name="competitorUrlEntityDAO">
			<ref bean="competitorUrlEntityDAO" />
		</property>
		<property name="logEntityDAO">
			<ref bean="logEntityDAO" />
		</property>
		<property name="logPageEntityDAO">
			<ref bean="logPageEntityDAO" />
		</property>
		<property name="optionEntityDAO">
			<ref bean="optionEntityDAO" />
		</property>
		<property name="emailSenderComponent">
			<ref bean="emailSenderComponent" />
		</property>
		<property name="notificationEntityDAO">
			<ref bean="notificationEntityDAO" />
		</property>
		<property name="urlMetricsDataEntityDAO">
			<ref bean="urlMetricsDataEntityDAO" />
		</property>
	</bean>

	<bean id="crawlPartnerurlHtmlContentEngine"
		class="com.actonia.subserver.multithread.core.common.CrawlPartnerurlHtmlContentEngine">
		<property name="partnerEntityDAO">
			<ref bean="partnerEntityDAO" />
		</property>		
		<property name="ownDomainEntityDAO">
			<ref bean="ownDomainEntityDAO" />
		</property>
		<property name="competitorUrlEntityDAO">
			<ref bean="competitorUrlEntityDAO" />
		</property>
		<property name="partnerUrlEntityDAO">
			<ref bean="partnerUrlEntityDAO" />
		</property>
		<property name="logEntityDAO">
			<ref bean="logEntityDAO" />
		</property>
		<property name="targetUrlEntityDAO">
			<ref bean="targetUrlEntityDAO" />
		</property>
		<property name="anchorTextDAO">
			<ref bean="anchorTextDAO" />
		</property>
		<property name="logParserResultEntityDAO">
			<ref bean="logParserResultEntityDAO" />
		</property>
		<property name="targetUrlPartnerUrlEntityDAO">
			<ref bean="targetUrlPartnerUrlEntityDAO" />
		</property>
		<property name="urlMetricsDataEntityDAO">
			<ref bean="urlMetricsDataEntityDAO" />
		</property>
		<property name="notificationEntityDAO">
			<ref bean="notificationEntityDAO" />
		</property>
		<property name="emailSenderComponent">
			<ref bean="emailSenderComponent" />
		</property>
		<property name="keywordEntityDAO">
			<ref bean="keywordEntityDAO" />
		</property>
		<property name="optionEntityDAO">
			<ref bean="optionEntityDAO" />
		</property>
		<property name="logPageEntityDAO">
			<ref bean="logPageEntityDAO" />
		</property>
		<property name="eventEntityDAO">
			<ref bean="eventEntityDAO" />
		</property>
		<property name="eventElementRelEntityDAO">
			<ref bean="eventElementRelEntityDAO" />
		</property>		
		<property name="taskService">
			<ref bean="taskService" />
		</property>			
	</bean>

	<bean id="notificationForPaymentRemainder"
		class="com.actonia.subserver.crawl.NotificationForPaymentRemainder">
		<property name="targetUrlEntityDAO">
			<ref bean="targetUrlEntityDAO" />
		</property>
		<property name="targetUrlPartnerUrlEntityDAO">
			<ref bean="targetUrlPartnerUrlEntityDAO" />
		</property>
		<property name="notificationEntityDAO">
			<ref bean="notificationEntityDAO" />
		</property>
		<property name="eventEntityDAO">
			<ref bean="eventEntityDAO" />
		</property>
		<property name="eventElementRelEntityDAO">
			<ref bean="eventElementRelEntityDAO" />
		</property>				
	</bean>

	<bean id="crawlGoogalAnalyticsEngine"
		class="com.actonia.subserver.multithread.core.common.CrawlGoogalAnalyticsEngine">
		<property name="ownDomainEntityDAO">
			<ref bean="ownDomainEntityDAO" />
		</property>
		<property name="keywordEntityDAO">
			<ref bean="keywordEntityDAO" />
		</property>
		<property name="targetUrlEntityDAO">
			<ref bean="targetUrlEntityDAO" />
		</property>
		<property name="keywordStatGaEntityDAO">
			<ref bean="keywordStatGaDAO" />
		</property>
		<property name="keywordConversionGaEntityDAO">
			<ref bean="keywordConversionGaEntityDAO" />
		</property>
		<property name="emailSenderComponent">
			<ref bean="emailSenderComponent" />
		</property>
	</bean>	

	<bean id="crawlGoogalAnalyticsBaseOauthEngine"
		class="com.actonia.subserver.multithread.core.common.CrawlGoogalAnalyticsBaseOauthEngine">
		<property name="ownDomainEntityDAO">
			<ref bean="ownDomainEntityDAO" />
		</property>
		<property name="keywordEntityDAO">
			<ref bean="keywordEntityDAO" />
		</property>
		<property name="targetUrlEntityDAO">
			<ref bean="targetUrlEntityDAO" />
		</property>
		<property name="keywordStatGaEntityDAO">
			<ref bean="keywordStatGaDAO" />
		</property>
		<property name="keywordConversionGaEntityDAO">
			<ref bean="keywordConversionGaEntityDAO" />
		</property>
		<property name="emailSenderComponent">
			<ref bean="emailSenderComponent" />
		</property>
	</bean>	

	<bean id="crawlGoogalAnalyticsBaseOauthEngineV2"
		class="com.actonia.subserver.multithread.core.common.CrawlGoogalAnalyticsBaseOauthEngineV2">
		<property name="ownDomainEntityDAO">
			<ref bean="ownDomainEntityDAO" />
		</property>
		<property name="keywordEntityDAO">
			<ref bean="keywordEntityDAO" />
		</property>
		<property name="targetUrlEntityDAO">
			<ref bean="targetUrlEntityDAO" />
		</property>
		<property name="keywordStatGaEntityDAO">
			<ref bean="keywordStatGaDAO" />
		</property>
		<property name="keywordConversionGaEntityDAO">
			<ref bean="keywordConversionGaEntityDAO" />
		</property>
		<property name="emailSenderComponent">
			<ref bean="emailSenderComponent" />
		</property>
	</bean>	
	
	<bean id="crawlGoogalAnalyticsMongoEngine"
		class="com.actonia.subserver.multithread.core.common.CrawlGoogalAnalyticsMongoEngine">
		<property name="ownDomainEntityDAO">
			<ref bean="ownDomainEntityDAO" />
		</property>
		<property name="emailSenderComponent">
			<ref bean="emailSenderComponent" />
		</property>
	</bean>

	<bean id="csvParserEngineForTargetUrlAndKeyword"
		class="com.actonia.subserver.csvparser.CsvParserEngineForTargetUrlAndKeyword">
		<property name="keywordEntityDAO">
			<ref bean="keywordEntityDAO" />
		</property>
		<property name="targetUrlEntityDAO">
			<ref bean="targetUrlEntityDAO" />
		</property>
		<property name="ownDomainEntityDAO">
			<ref bean="ownDomainEntityDAO" />
		</property>
		<property name="keywordTargeturlEntityDAO">
			<ref bean="keywordTargeturlEntityDAO" />
		</property>
	</bean>

	<bean id="csfParserEngineForPartnerurl"
		class="com.actonia.subserver.csvparser.CsfParserEngineForPartnerurl">
		<property name="partnerEntityDAO">
			<ref bean="partnerEntityDAO" />
		</property>
		<property name="tempPartnerUrlDAO">
			<ref bean="tempPartnerUrlDAO" />
		</property>
		<property name="ownDomainEntityDAO">
			<ref bean="ownDomainEntityDAO" />
		</property>
		<property name="partnerUrlEntityDAO">
			<ref bean="partnerUrlEntityDAO" />
		</property>
	</bean>

	<bean id="googleWebMasterParser"
		class="com.actonia.subserver.googlewebmaster.GoogleWebMasterParser">
		<property name="uploadFileEntityDAO">
			<ref bean="uploadFileEntityDAO" />
		</property>
		<property name="urlRelationDAO">
			<ref bean="urlRelationDAO" />
		</property>
		<property name="urlRelationChangeEntityDAO">
			<ref bean="urlRelationChangeEntityDAO" />
		</property>
		<property name="urlRelationChangeDetailDAO">
			<ref bean="urlRelationChangeDetailDAO" />
		</property>
		<property name="targetUrlPartnerUrlEntityDAO">
			<ref bean="targetUrlPartnerUrlEntityDAO" />
		</property>
	</bean>


	<bean id="tagParserEngine" class="com.actonia.subserver.csvparser.TagParserEngine">
		<property name="ownDomainEntityDAO">
			<ref bean="ownDomainEntityDAO" />
		</property>
		<property name="keywordEntityDAO">
			<ref bean="keywordEntityDAO" />
		</property>
		<property name="targetUrlEntityDAO">
			<ref bean="targetUrlEntityDAO" />
		</property>
		<property name="groupTagEntityDAO">
			<ref bean="groupTagEntityDAO" />
		</property>
		<property name="groupTagRelationEntityDAO">
			<ref bean="groupTagRelationEntityDAO" />
		</property>
	</bean>

	<bean id="crawlSiteEngine" class="com.actonia.subserver.crawlsite.CrawlSiteEngine">
		<property name="crawlsiteUrlEntityDAO">
			<ref bean="crawlsiteUrlEntityDAO" />
		</property>
		<property name="crawlsiteSummayEntityDAO">
			<ref bean="crawlsiteSummayEntityDAO" />
		</property>
		<property name="crawlsiteOutlinkAnchorTextEntityDAO">
			<ref bean="crawlsiteOutlinkAnchorTextEntityDAO" />
		</property>
		<property name="crawlsiteInlinkAnchorTextEntityDAO">
			<ref bean="crawlsiteInlinkAnchorTextEntityDAO" />
		</property>
		<property name="crawlsiteDomainEntityDAO">
			<ref bean="crawlsiteDomainEntityDAO" />
		</property>
		<property name="ownDomainEntityDAO">
			<ref bean="ownDomainEntityDAO" />
		</property>
		<property name="pageResourceEntityDAO">
			<ref bean="pageResourceEntityDAO" />
		</property>
		<property name="canonicalUrlEntityDAO">
			<ref bean="canonicalUrlEntityDAO" />
		</property>
		<property name="domainDailySummaryEntityDAO">
			<ref bean="domainDailySummaryEntityDAO" />
		</property>
		<property name="crawlsiteDomainSummaryEntityDAO">
			<ref bean="crawlsiteDomainSummaryEntityDAO" />
		</property>		
	</bean>

	<bean id="findDuplicateKeyword" class="com.actonia.subserver.tools.FindDuplicateKeyword">
		<property name="keywordEntityDAO">
			<ref bean="keywordEntityDAO" />
		</property>
	</bean>

	<bean id="crawlCAkeywordEngine"
		class="com.actonia.subserver.multithread.core.common.CrawlKeywordRankForCanadaEngine">
		<property name="keywordDictionaryEntityDAO">
			<ref bean="keywordDictionaryCAEntityDAO" />
		</property>
		<property name="keywordGoogleQueryEntityDAO">
			<ref bean="keywordGoogleQueryCAEntityDAO" />
		</property>
		<property name="keywordGoogleQueryRankEntityDAO">
			<ref bean="keywordGoogleQueryRankCAEntityDAO" />
		</property>
		<property name="emailSenderComponent">
			<ref bean="emailSenderComponent" />
		</property>
	</bean>

	<bean id="crawlCAkeywordEngineFr"
		class="com.actonia.subserver.multithread.core.common.CrawlKeywordRankForCanadaFrenchEngine">
		<property name="keywordDictionaryEntityDAO">
			<ref bean="keywordDictionaryCAFrEntityDAO" />
		</property>
		<property name="keywordGoogleQueryEntityDAO">
			<ref bean="keywordGoogleQueryCAFrEntityDAO" />
		</property>
		<property name="keywordGoogleQueryRankEntityDAO">
			<ref bean="keywordGoogleQueryRankCAFrEntityDAO" />
		</property>
		<property name="emailSenderComponent">
			<ref bean="emailSenderComponent" />
		</property>
	</bean>

	<bean id="keywordTagsParserEngine" class="com.actonia.subserver.csvparser.KeywordTagsParserEngine">
		<property name="ownDomainEntityDAO">
			<ref bean="ownDomainEntityDAO" />
		</property>
		<property name="keywordEntityDAO">
			<ref bean="keywordEntityDAO" />
		</property>
		<property name="groupTagEntityDAO">
			<ref bean="groupTagEntityDAO" />
		</property>
		<property name="groupTagRelationEntityDAO">
			<ref bean="groupTagRelationEntityDAO" />
		</property>
		<property name="keywordDictionaryEntityDAO">
			<ref bean="keywordDictionaryEntityDAO" />
		</property>
		<property name="keywordRankService">
			<ref bean="keywordRankService" />
		</property>		
	</bean>

	<bean id="crawlSiteForOverstockEngine"
		class="com.actonia.subserver.crawlsite.CrawlSiteForOverstockEngine">
		<property name="crawlsiteUrlEntityDAO">
			<ref bean="crawlsiteForOverstockUrlEntityDAO" />
		</property>
		<property name="crawlsiteSummayEntityDAO">
			<ref bean="crawlsiteForOverstockSummayEntityDAO" />
		</property>
		<property name="crawlsiteOutlinkAnchorTextEntityDAO">
			<ref bean="crawlsiteForOverstockOutlinkAnchorTextEntityDAO" />
		</property>
		<property name="crawlsiteInlinkAnchorTextEntityDAO">
			<ref bean="crawlsiteForOverstockInlinkAnchorTextEntityDAO" />
		</property>
		<property name="crawlsiteDomainEntityDAO">
			<ref bean="crawlsiteForOverstockDomainEntityDAO" />
		</property>
		<property name="ownDomainEntityDAO">
			<ref bean="ownDomainEntityDAO" />
		</property>
	</bean>

	<bean id="crawlKeywordRankByXMLEngine"
		class="com.actonia.subserver.multithread.core.common.CrawlKeywordRankByXMLEngine">
		<property name="keywordDictionaryEntityDAO">
			<ref bean="keywordDictionaryEntityDAO" />
		</property>
		<property name="keywordGoogleQueryEntityDAO">
			<ref bean="keywordGoogleQueryEntityDAO" />
		</property>
		<property name="keywordGoogleQueryRankEntityDAO">
			<ref bean="keywordGoogleQueryRankEntityDAO" />
		</property>
		<property name="emailSenderComponent">
			<ref bean="emailSenderComponent" />
		</property>
	</bean>

	<bean id="targetUrlTagsParserEngine"
		class="com.actonia.subserver.csvparser.TargetUrlTagsParserEngine">
		<property name="ownDomainEntityDAO">
			<ref bean="ownDomainEntityDAO" />
		</property>
		<property name="targetUrlEntityDAO">
			<ref bean="targetUrlEntityDAO" />
		</property>
		<property name="groupTagEntityDAO">
			<ref bean="groupTagEntityDAO" />
		</property>
		<property name="groupTagRelationEntityDAO">
			<ref bean="groupTagRelationEntityDAO" />
		</property>
	</bean>


	<bean id="removeKeyword" class="com.actonia.subserver.csvparser.RemoveKeyword">
		<property name="ownDomainEntityDAO">
			<ref bean="ownDomainEntityDAO" />
		</property>
		<property name="keywordEntityDAO">
			<ref bean="keywordEntityDAO" />
		</property>
		<property name="keywordDictionaryEntityDAO">
			<ref bean="keywordDictionaryEntityDAO" />
		</property>
	</bean>

	<bean id="coreMetricsCsvParserEngine"
		class="com.actonia.subserver.csvparser.CoreMetricsCsvParserEngine">
		<property name="ownDomainEntityDAO">
			<ref bean="ownDomainEntityDAO" />
		</property>
		<property name="keywordEntityDAO">
			<ref bean="keywordEntityDAO" />
		</property>
		<property name="keywordStatGaDAO">
			<ref bean="keywordStatGaDAO" />
		</property>
		<property name="targetUrlEntityDAO">
			<ref bean="targetUrlEntityDAO" />
		</property>
		<property name="keywordConversionGaEntityDAO">
			<ref bean="keywordConversionGaEntityDAO" />
		</property>
	</bean>

	<bean id="coreMetricsCsvParserSpecailEngine"
		class="com.actonia.subserver.csvparser.CoreMetricsCsvParserSpecailEngine">
		<property name="ownDomainEntityDAO">
			<ref bean="ownDomainEntityDAO" />
		</property>
		<property name="keywordEntityDAO">
			<ref bean="keywordEntityDAO" />
		</property>
		<property name="keywordStatGaDAO">
			<ref bean="keywordStatGaDAO" />
		</property>
		<property name="targetUrlEntityDAO">
			<ref bean="targetUrlEntityDAO" />
		</property>
		<property name="keywordConversionGaEntityDAO">
			<ref bean="keywordConversionGaEntityDAO" />
		</property>
	</bean>

	<bean id="crawlSemRushEngine" class="com.actonia.subserver.semrush.CrawlSemRushEngine">
		<property name="targetUrlEntityDAO">
			<ref bean="targetUrlEntityDAO" />
		</property>
		<property name="competitorUrlEntityDAO">
			<ref bean="competitorUrlEntityDAO" />
		</property>
		<property name="semRushEntityDAO">
			<ref bean="semRushEntityDAO" />
		</property>
	</bean>

	<bean id="divParserEngine" class="com.actonia.subserver.csvparser.DivParserEngine">
		<property name="targetUrlEntityDAO">
			<ref bean="targetUrlEntityDAO" />
		</property>
		<property name="ownDomainEntityDAO">
			<ref bean="ownDomainEntityDAO" />
		</property>
		<property name="optionEntityDAO">
			<ref bean="optionEntityDAO" />
		</property>
	</bean>

	<bean id="crawlPageNameForAutoAnyThing"
		class="com.actonia.subserver.multithread.core.common.CrawlPageNameForAutoAnyThing">
		<property name="targetUrlEntityDAO">
			<ref bean="targetUrlEntityDAO" />
		</property>
		<property name="pagenameUrlEntityDAO">
			<ref bean="pagenameUrlEntityDAO" />
		</property>
	</bean>

	<bean id="partnerUrlAndDateParserEngine"
		class="com.actonia.subserver.csvparser.PartnerUrlAndDateParserEngine">
		<property name="targetUrlEntityDAO">
			<ref bean="targetUrlEntityDAO" />
		</property>
		<property name="ownDomainEntityDAO">
			<ref bean="ownDomainEntityDAO" />
		</property>
		<property name="partnerEntityDAO">
			<ref bean="partnerEntityDAO" />
		</property>
		<property name="targetUrlPartnerUrlEntityDAO">
			<ref bean="targetUrlPartnerUrlEntityDAO" />
		</property>
		<property name="anchorTextDAO">
			<ref bean="anchorTextDAO" />
		</property>
		<property name="partnerUrlEntityDAO">
			<ref bean="partnerUrlEntityDAO" />
		</property>
	</bean>

	<!-- For Keyword Rank Service -->
	<bean id="keywordRankService" class="com.actonia.subserver.service.KeywordRankService">
		<property name="manager">
			<ref bean="scKeywordRankManager" />
		</property>
	</bean>
	
	<bean id="scKeywordSearchVolumeManager" class="com.actonia.subserver.service.ScKeywordSearchVolumeManager"></bean>

	<bean id="scKeywordRankManager" class="com.actonia.subserver.service.ScKeywordRankManager">
		<property name="seoClarityKeywordEntityDAO">
			<ref bean="seoClarityKeywordEntityDAO" />
		</property>
		<property name="seoClarityKeywordRankQueryEntityDAO">
			<ref bean="seoClarityKeywordRankQueryEntityDAO" />
		</property>
		<property name="seoClarityKeywordCitySearchEngineRelationEntityDAO">
			<ref bean="seoClarityKeywordCitySearchEngineRelationEntityDAO" />
		</property>
		<property name="seoClarityKeywordMonthlySearchEngineRelationEntityDAO">
			<ref bean="seoClarityKeywordMonthlySearchEngineRelationEntityDAO" />
		</property>		
		<property name="seoClarityCityEntityDAO">
			<ref bean="seoClarityCityEntityDAO" />
		</property>		
		<property name="seoClarityKeywordRankQueryRecordEntityDAO">
			<ref bean="seoClarityKeywordRankQueryRecordEntityDAO" />
		</property>
		<property name="scKeywordAdwordsDataEntityDAO">
			<ref bean="scKeywordAdwordsDataEntityDAO" />
		</property>
		<property name="seoClarityKeywordSearchEngineRelationEntityDAO">
			<ref bean="seoClarityKeywordSearchEngineRelationEntityDAO" />
		</property>
		<property name="seoUrlEntityDAO">
			<ref bean="seoUrlEntityDAO" />
		</property>	
		<property name="seoDomainEntityDAO">
			<ref bean="seoDomainEntityDAO" />
		</property>		
	</bean>
	<!-- For Keyword Rank DB default -->
	<!-- 
	<bean id="keywordRankManagerDefault" class="com.actonia.subserver.service.KeywordRankManager">
		<property name="keywordDictionaryDAO">
			<ref bean="keywordDictionaryEntityDAO" />
		</property>
		<property name="keywordGoogleQueryDAO">
			<ref bean="keywordGoogleQueryEntityDAO" />
		</property>
		<property name="keywordGoogleQueryRankDAO">
			<ref bean="keywordGoogleQueryRankEntityDAO" />
		</property>
		<property name="keywordAdwordsDataEntityDAO">
			<ref bean="keywordAdwordsDataEntityDAO" />
		</property>
	</bean>
 -->
	<!-- For Keyword Rank DB CA -->
	<!-- 
	<bean id="keywordRankManagerCa" class="com.actonia.subserver.service.KeywordRankManager">
		<property name="keywordDictionaryDAO">
			<ref bean="keywordDictionaryCAEntityDAO" />
		</property>
		<property name="keywordGoogleQueryDAO">
			<ref bean="keywordGoogleQueryCAEntityDAO" />
		</property>
		<property name="keywordGoogleQueryRankDAO">
			<ref bean="keywordGoogleQueryRankCAEntityDAO" />
		</property>
		<property name="keywordAdwordsDataEntityDAO">
			<ref bean="keywordAdwordsDataCAEntityDAO" />
		</property>
	</bean>
 -->
	<!-- For Keyword Rank DB CA_fr -->
	<!--  
	<bean id="keywordRankManagerCaFr" class="com.actonia.subserver.service.KeywordRankManager">
		<property name="keywordDictionaryDAO">
			<ref bean="keywordDictionaryCAFrEntityDAO" />
		</property>
		<property name="keywordGoogleQueryDAO">
			<ref bean="keywordGoogleQueryCAFrEntityDAO" />
		</property>
		<property name="keywordGoogleQueryRankDAO">
			<ref bean="keywordGoogleQueryRankCAFrEntityDAO" />
		</property>
		<property name="keywordAdwordsDataEntityDAO">
			<ref bean="keywordAdwordsDataCAFrEntityDAO" />
		</property>
	</bean>
	-->
	
	<bean id="filterService" class="com.actonia.subserver.filter.FilterService">
		<property name="keywordCriteriaEntityDAO">
			<ref bean="keywordCriteriaEntityDAO" />
		</property>
		<property name="customizeFilterConditionDAO">
			<ref bean="customizeFilterConditionDAO" />
		</property>
		<property name="customizeFilterCriteriaDAO">
			<ref bean="customizeFilterCriteriaDAO" />
		</property>
		<property name="customizeFilterDAO">
			<ref bean="customizeFilterDAO" />
		</property>
		<property name="customizeFilterElementRelDAO">
			<ref bean="customizeFilterElementRelDAO" />
		</property>
		<property name="customizeFilterUserRelDAO">
			<ref bean="customizeFilterUserRelDAO" />
		</property>
		<property name="targetURLCriteriaDAO">
			<ref bean="targetURLCriteriaDAO" />
		</property>
	</bean>
	
	<bean id="omnitureCsvParserEngine" class="com.actonia.subserver.csvparser.OmnitureCsvParserEngine">
		<property name="ownDomainEntityDAO">
			<ref bean="ownDomainEntityDAO" />
		</property>
		<property name="emailSenderComponent">
			<ref bean="emailSenderComponent" />
		</property>
		<property name="keywordEntityDAO">
			<ref bean="keywordEntityDAO" />
		</property>
		<property name="pagenameUrlEntityDAO">
			<ref bean="pagenameUrlEntityDAO" />
		</property>
		<property name="keywordStatGaDAO">
			<ref bean="keywordStatGaDAO" />
		</property>
		<property name="keywordConversionGaEntityDAO">
			<ref bean="keywordConversionGaEntityDAO" />
		</property>
		<property name="targetUrlEntityDAO">
			<ref bean="targetUrlEntityDAO" />
		</property>		
	</bean>

	<bean id="createAssociations" class="com.actonia.subserver.associations.CreateAssociations">
		<property name="ownDomainEntityDAO">
			<ref bean="ownDomainEntityDAO" />
		</property>
		<property name="competitorEntityDAO">
			<ref bean="competitorEntityDAO" />
		</property>
		<property name="keywordStatGaEntityDAO">
			<ref bean="keywordStatGaDAO" />
		</property>
		<property name="keywordTargeturlEntityDAO">
			<ref bean="keywordTargeturlEntityDAO" />
		</property>
		<property name="keywordEntityDAO">
			<ref bean="keywordEntityDAO" />
		</property>
		<property name="competitorUrlEntityDAO">
			<ref bean="competitorUrlEntityDAO" />
		</property>
		<property name="keywordCompetitorUrlEntityDao">
			<ref bean="keywordCompetitorUrlEntityDao" />
		</property>
		<property name="targetUrlEntityDAO">
			<ref bean="targetUrlEntityDAO" />
		</property>
	</bean>
	<bean id="deleteKeywords" class="com.actonia.subserver.tools.DeleteKeywords">
		<property name="keywordDictionaryEntityDAO">
			<ref bean="keywordDictionaryEntityDAO" />
		</property>
		<property name="ownDomainEntityDAO">
			<ref bean="ownDomainEntityDAO" />
		</property>
		<property name="keywordEntityDAO">
			<ref bean="keywordEntityDAO" />
		</property>
	</bean>
	<bean id="transferQueryRankEntity" class="com.actonia.subserver.datatransfer.TransferQueryRankEntity">
		<property name="keywordGoogleQueryRankEntityDAO">
			<ref bean="keywordGoogleQueryRankEntityDAO" />
		</property>
		<property name="migrateTempEntityDAO">
			<ref bean="migrateTempEntityDAO" />
		</property>		
	</bean>	
	<bean id="transferQueryRankForLastXDaysEntity" class="com.actonia.subserver.datatransfer.TransferQueryRankForLastXDaysEntity">
		<property name="keywordGoogleQueryRankEntityDAO">
			<ref bean="keywordGoogleQueryRankEntityDAO" />
		</property>
		<property name="migrateTempEntityDAO">
			<ref bean="migrateTempEntityDAO" />
		</property>		
	</bean>		

	<bean id="addHttpForCompetitorUrl" class="com.actonia.subserver.tools.AddHttpForUrls">
		<property name="competitorUrlEntityDAO">
			<ref bean="competitorUrlEntityDAO" />
		</property>
	</bean>	

	<!-- For Keyword Rank DB CA_fr -->
	<bean id="keywordRankManagerAU" class="com.actonia.subserver.service.KeywordRankManager">
		<property name="keywordDictionaryDAO">
			<ref bean="keywordDictionaryAUEntityDAO" />
		</property>
		<property name="keywordGoogleQueryDAO">
			<ref bean="keywordGoogleQueryAUEntityDAO" />
		</property>
		<property name="keywordGoogleQueryRankDAO">
			<ref bean="keywordGoogleQueryRankAUEntityDAO" />
		</property>
		<property name="keywordAdwordsDataEntityDAO">
			<ref bean="keywordAdwordsDataAUEntityDAO" />
		</property>
	</bean>	
	
	<bean id="taskService" class="com.actonia.subserver.service.TaskService">
		<property name="taskEntityDAO">
			<ref bean="taskEntityDAO" />
		</property>
		<property name="eventTaskRelEntityDAO">
			<ref bean="eventTaskRelEntityDAO" />
		</property>
		<property name="taskTempleteEntityDAO">
			<ref bean="taskTempleteEntityDAO" />
		</property>
		<property name="statusTypeEntityDAO">
			<ref bean="statusTypeEntityDAO" />
		</property>
		<property name="taskElementRelEntityDAO">
			<ref bean="taskElementRelEntityDAO" />
		</property>		
	</bean>		
	
	<bean id="monitorScriptService" class="com.actonia.subserver.scriptmontitor.MonitorScriptService">
		<property name="scriptDomainStatusDAO">
			<ref bean="scriptDomainStatusDAO" />
		</property>
	</bean>		
	
	
	<bean id="exportExcelService" class="com.actonia.subserver.service.ExportExcelService">
		<property name="keywordEntityDAO">
			<ref bean="keywordEntityDAO" />
		</property>
		<property name="targetUrlEntityDAO">
			<ref bean="targetUrlEntityDAO" />
		</property>	
		<property name="ownDomainEntityDAO">
			<ref bean="ownDomainEntityDAO" />
		</property>		
		<property name="keywordRankService">
			<ref bean="keywordRankService" />
		</property>				
	</bean>
	
	<!-- Rebuild Download All -->
	<bean id="newExportExcelService" class="com.actonia.subserver.exportv2.base.ExportExcelServiceV2">		
	</bean>

	<bean id="solrQueryService" class="com.actonia.subserver.service.SolrQueryService"></bean>
	
	<bean id="remoteKeywordRankQueue" class="org.springframework.remoting.caucho.HessianProxyFactoryBean">
    	<property name="serviceUrl" value="http://*************:8080/central/remote/keywordRankQueue"/>
    	<property name="serviceInterface" value="com.actonia.webservice.inf.IKeywordRankQueueService"/>
    </bean>
    
	<bean id="mongoService" class="com.actonia.subserver.service.MongoService">
	</bean>	  
	
	<bean id="mongoAggregationService" class="com.actonia.subserver.service.MongoAggregationService">
	</bean>	  
	
	<bean id="bundleService" class="com.actonia.subserver.bundle.service.BundleService">
	</bean>		  
	  
	
	<bean id="botService" class="com.actonia.subserver.service.BotService">
		<property name="tBotSummaryEntityDAO">
			<ref bean="tBotSummaryEntityDAO" />
		</property>
		<property name="tBotUrlSummaryEntityDAO">
			<ref bean="tBotUrlSummaryEntityDAO" />
		</property>
		<property name="tBotEntityDAO">
			<ref bean="tBotEntityDAO" />
		</property>	
		<property name="tBotSearchEngineEntityDAO">
			<ref bean="tBotSearchEngineEntityDAO" />
		</property>	
		<property name="tBotUrlInfoEntityDAO">
			<ref bean="tBotUrlInfoEntityDAO" />
		</property>		
		<property name="tBotUrlEntityDAO">
			<ref bean="tBotUrlEntityDAO" />
		</property>	
		<property name="tBotInfoEntityDAO">
			<ref bean="tBotInfoEntityDAO" />
		</property>											
	</bean>	
	<!-- added by zhaozh start -->
	<bean id="crawlPageName"
		class="com.actonia.subserver.multithread.core.common.CrawlPageName">
		<property name="targetUrlEntityDAO">
			<ref bean="targetUrlEntityDAO" />
		</property>
		<property name="pagenameUrlEntityDAO">
			<ref bean="pagenameUrlEntityDAO" />
		</property>
	</bean>
	<bean id="crawlUrlandPageName"
		class="com.actonia.subserver.multithread.core.common.CrawlUrlandPageName">
		<property name="targetUrlEntityDAO">
			<ref bean="targetUrlEntityDAO" />
		</property>
		<property name="pagenameUrlEntityDAO">
			<ref bean="pagenameUrlEntityDAO" />
		</property>
	</bean>
	<bean id="crawlRealUrl"
		class="com.actonia.subserver.multithread.core.common.CrawlRealUrl">
		<property name="targetUrlEntityDAO">
			<ref bean="targetUrlEntityDAO" />
		</property>
		<property name="pagenameUrlEntityDAO">
			<ref bean="pagenameUrlEntityDAO" />
		</property>
	</bean>
	
	<bean id="omnitureCsvParserEngineForbusiness" class="com.actonia.subserver.csvparser.OmnitureCsvParserEngineForBusiness">
		<property name="ownDomainEntityDAO">
			<ref bean="ownDomainEntityDAO" />
		</property>
		<property name="emailSenderComponent">
			<ref bean="emailSenderComponent" />
		</property>
		<property name="keywordEntityDAO">
			<ref bean="keywordEntityDAO" />
		</property>
		<property name="pagenameUrlEntityDAO">
			<ref bean="pagenameUrlEntityDAO" />
		</property>
		<property name="keywordStatGaDAO">
			<ref bean="keywordStatGaDAO" />
		</property>
		<property name="keywordConversionGaEntityDAO">
			<ref bean="keywordConversionGaEntityDAO" />
		</property>
		<property name="targetUrlEntityDAO">
			<ref bean="targetUrlEntityDAO" />
		</property>		
	</bean>
	
	<bean id="omnitureCsvParserEngineForToysrus" class="com.actonia.subserver.csvparser.OmnitureCsvParserEngineForToysrus">
		<property name="ownDomainEntityDAO">
			<ref bean="ownDomainEntityDAO" />
		</property>
		<property name="emailSenderComponent">
			<ref bean="emailSenderComponent" />
		</property>
		<property name="keywordEntityDAO">
			<ref bean="keywordEntityDAO" />
		</property>
		<property name="pagenameUrlEntityDAO">
			<ref bean="pagenameUrlEntityDAO" />
		</property>
		<property name="keywordStatGaDAO">
			<ref bean="keywordStatGaDAO" />
		</property>
		<property name="keywordConversionGaEntityDAO">
			<ref bean="keywordConversionGaEntityDAO" />
		</property>
		<property name="targetUrlEntityDAO">
			<ref bean="targetUrlEntityDAO" />
		</property>		
	</bean>
	<!-- added by zhaozh end -->

	<bean id="groupTagMongoUpdate" class="com.actonia.subserver.summary.GroupTagMongoUpdate">
		<property name="groupTagRelationEntityDAO">
			<ref bean="groupTagRelationEntityDAO" />
		</property>
	</bean>
	
	<bean id="omnitureCsvParserMongoEngine" class="com.actonia.subserver.csvparser.OmnitureCsvParserMongoEngine">
		<property name="ownDomainEntityDAO">
			<ref bean="ownDomainEntityDAO" />
		</property>
		<property name="emailSenderComponent">
			<ref bean="emailSenderComponent" />
		</property>
		<property name="pagenameUrlEntityDAO">
			<ref bean="pagenameUrlEntityDAO" />
		</property>		
	</bean>	

	<bean id="overStockCsvParserEngine" class="com.actonia.subserver.csvparser.OverStockCsvParserEngine">
		<property name="keywordEntityDAO">
			<ref bean="keywordEntityDAO" />
		</property>
		<property name="targetUrlEntityDAO">
			<ref bean="targetUrlEntityDAO" />
		</property>
		<property name="ownDomainEntityDAO">
			<ref bean="ownDomainEntityDAO" />
		</property>
		<property name="keywordStatGaDAO">
			<ref bean="keywordStatGaDAO" />
		</property>
		<property name="emailSenderComponent">
			<ref bean="emailSenderComponent" />
		</property>
		<property name="tempImportDataEntityDAO">
			<ref bean="tempImportDataEntityDAO" />
		</property>
		<property name="tempImportDataDailySummaryDAO">
			<ref bean="tempImportDataDailySummaryDAO" />
		</property>
		<property name="keywordConversionGaEntityDAO">
			<ref bean="keywordConversionGaEntityDAO" />
		</property>
	</bean>
	
	<bean id="queuingService" class="com.actonia.subserver.service.QueuingService">
		<property name="processDescEntityDAO">
			<ref bean="processDescEntityDAO" />
		</property>
		<property name="domainProcessRelEntityDAO">
			<ref bean="domainProcessRelEntityDAO" />
		</property>
		<property name="statusEntityDAO">
			<ref bean="statusEntityDAO" />
		</property>
		<property name="domainProcessInfoEntityDAO">
			<ref bean="domainProcessInfoEntityDAO" />
		</property>
		<property name="scriptEntityDAO">
			<ref bean="scriptEntityDAO" />
		</property>
		<property name="domainScriptInfoEntityDAO">
			<ref bean="domainScriptInfoEntityDAO" />
		</property>
		<property name="processScriptRelEntityDAO">
			<ref bean="processScriptRelEntityDAO" />
		</property>
		<property name="stepEntityDAO">
			<ref bean="stepEntityDAO" />
		</property>
</bean>

	<bean id="crawlGoogalAnalyticsBaseOauthOrganicMobileEngine"
		class="com.actonia.subserver.multithread.core.common.CrawlGoogalAnalyticsBaseOauthOrganicMobileEngine">
		<property name="ownDomainEntityDAO">
			<ref bean="ownDomainEntityDAO" />
		</property>
		<property name="keywordEntityDAO">
			<ref bean="keywordEntityDAO" />
		</property>
		<property name="targetUrlEntityDAO">
			<ref bean="targetUrlEntityDAO" />
		</property>
		<property name="keywordStatGaEntityDAO">
			<ref bean="keywordStatGaDAO" />
		</property>
		<property name="keywordConversionGaEntityDAO">
			<ref bean="keywordConversionGaEntityDAO" />
		</property>
		<property name="emailSenderComponent">
			<ref bean="emailSenderComponent" />
		</property>
	</bean>	
	
	<bean id="agencyInfoService" class="com.actonia.service.AgencyInfoService">
		<property name="agencyInfoDAO">
			<ref bean="agencyInfoDAO" />
		</property>
		<property name="ownDomainSettingEntityDAO">
			<ref bean="ownDomainSettingEntityDAO" />
		</property>
	</bean>
	
	<bean id="siteCrawlMongodbService" class="com.actonia.service.SiteCrawlMongodbService">
	</bean>
	
	<bean id="digitalOceanAPIService" class="com.actonia.service.DigitalOceanAPIService">
		<property name="siteCrawlConfigDAO">
			<ref bean="siteCrawlConfigDAO" />
		</property>	
	</bean>
	
	<bean id="elasticSearchService" class="com.actonia.service.ElasticSearchService">
	</bean>

	<bean id="commonDataService" class="com.actonia.service.CommonDataService">
	</bean>

	<bean id="mainWebServiceClientService" class="com.actonia.service.MainWebServiceClientService"></bean>

	<bean id="politeCrawlWebServiceClientService" class="com.actonia.service.PoliteCrawlWebServiceClientService"></bean>

	<bean id="contentGuardService" class="com.actonia.service.ContentGuardService"></bean>

	<bean id="slackService" class="com.actonia.service.SlackService"></bean>

	<bean id="mainZapierWebServiceClientService" class="com.actonia.service.MainZapierWebServiceClientService"></bean>

	<bean id="accessTokenService" class="com.actonia.service.AccessTokenService"></bean>

	<bean id="zapierService" class="com.actonia.service.ZapierService"></bean>

	<bean id="serpAnalyzerApiService" class="com.actonia.service.SerpAnalyzerApiService"></bean>

	<bean id="listChangeIndicatorsService" class="com.actonia.service.ListChangeIndicatorsService"></bean>

	<bean id="getUrlSummaryService" class="com.actonia.service.GetUrlSummaryService"></bean>

</beans>