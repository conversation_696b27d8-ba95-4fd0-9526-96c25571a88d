<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:context="http://www.springframework.org/schema/context"
    xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-2.5.xsd"
    default-autowire="byName">

    <!-- ========================== TransactionManager ======================== -->
    <bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource">
            <ref bean="actoniaDataSource"/>
        </property>
    </bean>

    <bean id="targetUrlDailyCrawlTrackingEntityDAO" class="com.actonia.dao.TargetUrlDailyCrawlTrackingEntityDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="linkClarityCrawlTrackingEntityDAO" class="com.actonia.dao.LinkClarityCrawlTrackingEntityDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="competitorUrlWeeklyCrawlTrackingEntityDAO" class="com.actonia.dao.CompetitorUrlWeeklyCrawlTrackingEntityDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="associatedUrlEntityDAO" class="com.actonia.dao.AssociatedUrlEntityDAO">
        <property name="dataSource" ref="backlinkDataSource"/>
    </bean>

    <bean id="ownDomainEntityDAO" class="com.actonia.dao.OwnDomainEntityDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

	<bean id="competitorUrlEntityDAO" class="com.actonia.dao.CompetitorUrlEntityDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

	<bean id="associatedCompetitorUrlDAO" class="com.actonia.dao.AssociatedCompetitorUrlDAO">
        <property name="dataSource" ref="backlinkDataSource"/>
    </bean>

    <bean id="targetUrlEntityDAO" class="com.actonia.dao.TargetUrlEntityDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="targetUrlLogEntityDAO" class="com.actonia.dao.TargetUrlLogEntityDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="ownDomainSettingEntityDAO" class="com.actonia.dao.OwnDomainSettingEntityDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="tempCompetitorUrlsHtmlEntityDAO" class="com.actonia.dao.TempCompetitorUrlsHtmlEntityDAO">
        <property name="dataSource" ref="backlinkDataSource"/>
    </bean>

    <bean id="targeturlCrawlerEntityDAO" class="com.actonia.dao.TargeturlCrawlerEntityDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="alertMetricsNameDAO" class="com.actonia.dao.AlertMetricsNameDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="targetUrlCrawlAdditionalContentEntityDAO" class="com.actonia.dao.TargetUrlCrawlAdditionalContentEntityDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="userBacklinkDAO" class="com.actonia.dao.UserBacklinkDAO">
        <property name="dataSource" ref="backlinkDataSource"/>
    </bean>

    <bean id="elementMappingPatternEntityDAO" class="com.actonia.dao.ElementMappingPatternEntityDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="keywordTargeturlEntityDAO" class="com.actonia.dao.KeywordTargeturlEntityDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="pageClarityTrackingEntityDAO" class="com.actonia.dao.PageClarityTrackingEntityDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="targetURLCriteriaDAO" class="com.actonia.dao.TargetURLCriteriaDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="urlMetricsDataEntityDAO" class="com.actonia.dao.UrlMetricsDataEntityDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="urlMetricsDataHistoryEntityDAO" class="com.actonia.dao.UrlMetricsDataHistoryEntityDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="agencyInfoDAO" class="com.actonia.dao.AgencyInfoDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="pageContentChangeDAO" class="com.actonia.dao.PageContentChangeDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="pageTagAlertConfigAdditionalContentDAO" class="com.actonia.dao.PageTagAlertConfigAdditionalContentDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="pageTagAlertConfigDAO" class="com.actonia.dao.PageTagAlertConfigDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="pageTagContentChangeDAO" class="com.actonia.dao.PageTagContentChangeDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="groupTagEntityDAO" class="com.actonia.dao.GroupTagEntityDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="groupTagRelationEntityDAO" class="com.actonia.dao.GroupTagRelationEntityDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="commonParamDAO" class="com.actonia.dao.CommonParamDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="contentGuardUrlDAO" class="com.actonia.dao.ContentGuardUrlDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="contentGuardCrawlTrackingDAO" class="com.actonia.dao.ContentGuardCrawlTrackingDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="engineCountryLanguageMappingEntityDAO" class="com.actonia.dao.EngineCountryLanguageMappingEntityDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

	<bean id="associatedTargetUrlDAO" class="com.actonia.dao.AssociatedTargetUrlDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="contentGuardChangeTrackingDAO" class="com.actonia.dao.ContentGuardChangeTrackingDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="contentGuardChangeTrackingWithIdDAO" class="com.actonia.dao.ContentGuardChangeTrackingWithIdDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="contentGuardAlertDAO" class="com.actonia.dao.ContentGuardAlertDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="contentGuardSkipUrlDAO" class="com.actonia.dao.ContentGuardSkipUrlDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="contentGuardGroupDAO" class="com.actonia.dao.ContentGuardGroupDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="aiRulesDAO" class="com.actonia.dao.AiRulesDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

	<bean id="associatedCompetitorUrlAuditTrailDAO" class="com.actonia.dao.AssociatedCompetitorUrlAuditTrailDAO">
        <property name="dataSource" ref="backlinkDataSource"/>
    </bean>

    <bean id="competitorEntityDAO" class="com.actonia.dao.CompetitorEntityDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

	<bean id="competitorUrlMd5EntityDAO" class="com.actonia.dao.CompetitorUrlMd5EntityDAO">
        <property name="dataSource" ref="backlinkDataSource"/>
    </bean>

    <bean id="keywordCompetitorUrlEntityDao" class="com.actonia.dao.KeywordCompetitorUrlEntityDao">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="keywordEntityDAO" class="com.actonia.dao.KeywordEntityDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

	<bean id="seoClarityKeywordEntityDAO" class="com.actonia.dao.SeoClarityKeywordEntityDAO">
        <property name="dataSource" ref="seoclarityRankDataSource"/>
    </bean>

    <bean id="webhookDAO" class="com.actonia.dao.WebhookDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="zapierWebhookDAO" class="com.actonia.dao.ZapierWebhookDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

	<bean id="usersDAO" class="com.actonia.dao.UsersDAO">
		<property name="dataSource" ref="actoniaAPIDataSource" />
	</bean>

	<bean id="domainsDAO" class="com.actonia.dao.DomainsDAO">
		<property name="dataSource" ref="actoniaAPIDataSource" />
	</bean>

    <bean id="userDAO" class="com.actonia.dao.UserDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="zapierErrorCodeDAO" class="com.actonia.dao.ZapierErrorCodeDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="contentGuardAdditionalContentEntityDAO" class="com.actonia.dao.ContentGuardAdditionalContentEntityDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="googleTagJavascriptDAO" class="com.actonia.dao.GoogleTagJavascriptDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="googleTagJavascriptUrlDAO" class="com.actonia.dao.GoogleTagJavascriptUrlDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="pageRankNodeDAO" class="com.actonia.dao.PageRankNodeDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="pageRankEdgeDAO" class="com.actonia.dao.PageRankEdgeDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="keywordPageRelEntityDAO" class="com.actonia.dao.KeywordPageRelEntityDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="clarityAuditsEntityDAO" class="com.actonia.dao.ClarityAuditsEntityDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="crawlAuditRuleDAO" class="com.actonia.dao.CrawlAuditRuleDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="crawlRequestLogDAO" class="com.actonia.dao.CrawlRequestLogDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="domainSearchEngineRelDAO" class="com.actonia.dao.DomainSearchEngineRelDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="owndomainEngineRelDAO" class="com.actonia.dao.OwndomainEngineRelDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="scLanguageEntityDAO" class="com.actonia.dao.ScLanguageEntityDAO">
        <property name="dataSource" ref="seoclarityRankDataSource"/>
    </bean>

    <bean id="contentGuardUsageDAO" class="com.actonia.dao.ContentGuardUsageDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

	<bean id="backupAuditTrailDAO" class="com.actonia.dao.BackupAuditTrailDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="targetUrlChangeAlertDAO" class="com.actonia.dao.TargetUrlChangeAlertDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

	<bean id="contentGuardChangeTrackingDomainSettingDAO" class="com.actonia.dao.ContentGuardChangeTrackingDomainSettingDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="politeCrawlInstanceDAO" class="com.actonia.dao.PoliteCrawlInstanceDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="politeCrawlDomainSettingDAO" class="com.actonia.dao.PoliteCrawlDomainSettingDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>
    <bean id="politeCrawlStateLogDAO" class="com.actonia.dao.PoliteCrawlStateLogDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>
    <bean id="resourceSyncInfoEntityDAO" class="com.actonia.dao.ResourceSyncInfoEntityDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="resourceBatchDetailDAO" class="com.actonia.dao.ResourceBatchDetailDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="resourceBatchInfoDAO" class="com.actonia.dao.ResourceBatchInfoDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

    <bean id="ownDomainTrackingDAO" class="com.actonia.dao.OwnDomainTrackingDAO">
        <property name="dataSource" ref="actoniaDataSource"/>
    </bean>

</beans>

