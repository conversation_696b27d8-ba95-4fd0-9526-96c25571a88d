<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:aop="http://www.springframework.org/schema/aop"
	   xmlns:tx="http://www.springframework.org/schema/tx"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.0.xsd
           http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-2.0.xsd
           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.0.xsd"
	   default-autowire="byName" default-lazy-init="true">
    
    <!-- ========================== DATASOURCE DEFINITIONS ======================== 	-->
    <bean id="botSmallClientDataSource" class="org.apache.commons.dbcp.BasicDataSource" destroy-method="close">
        <property name="driverClassName" value="${bot.jdbc.driver}"/>
		<property name="url" value="${bot.jdbc.url}"/>
		<property name="username" value="${bot.jdbc.username}"/>
		<property name="password" value="${bot.jdbc.password}"/>
		<property name="initialSize" value="2"/>
		<property name="validationQuery" value="select id from t_bot_search_engine limit 1 "/>
    </bean>
    
    <bean id="bot185DataSource" class="org.apache.commons.dbcp.BasicDataSource" destroy-method="close">
        <property name="driverClassName" value="${bot.185.jdbc.driver}"/>
		<property name="url" value="${bot.185.jdbc.url}"/>
		<property name="username" value="${bot.185.jdbc.username}"/>
		<property name="password" value="${bot.185.jdbc.password}"/>
		<property name="initialSize" value="2"/>
		<property name="validationQuery" value="select id from t_bot_search_engine limit 1 "/>
    </bean>
    
    <bean id="bot182DataSource" class="org.apache.commons.dbcp.BasicDataSource" destroy-method="close">
        <property name="driverClassName" value="${bot.182.jdbc.driver}"/>
		<property name="url" value="${bot.182.jdbc.url}"/>
		<property name="username" value="${bot.182.jdbc.username}"/>
		<property name="password" value="${bot.182.jdbc.password}"/>
		<property name="initialSize" value="2"/>
		<property name="validationQuery" value="select id from t_bot_search_engine limit 1 "/>
    </bean>
    
    <bean id="bot334DataSource" class="org.apache.commons.dbcp.BasicDataSource" destroy-method="close">
        <property name="driverClassName" value="${bot.334.jdbc.driver}"/>
		<property name="url" value="${bot.334.jdbc.url}"/>
		<property name="username" value="${bot.334.jdbc.username}"/>
		<property name="password" value="${bot.334.jdbc.password}"/>
		<property name="initialSize" value="2"/>
		<property name="validationQuery" value="select id from t_bot_search_engine limit 1 "/>
    </bean>

    <bean id="bot334OtherDataSource" class="org.apache.commons.dbcp.BasicDataSource" destroy-method="close">
        <property name="driverClassName" value="${bot.334.other.jdbc.driver}"/>
		<property name="url" value="${bot.334.other.jdbc.url}"/>
		<property name="username" value="${bot.334.other.jdbc.username}"/>
		<property name="password" value="${bot.334.other.jdbc.password}"/>
		<property name="initialSize" value="2"/>
		<property name="validationQuery" value="select id from t_bot_search_engine limit 1 "/>
    </bean>    
    
    <bean id="IFbot334OtherDataSource" class="org.apache.commons.dbcp.BasicDataSource" destroy-method="close">
        <property name="driverClassName" value="${infobright.bot.334.other.jdbc.driver}"/>
		<property name="url" value="${infobright.bot.334.other.jdbc.url}"/>
		<property name="username" value="${infobright.bot.334.other.jdbc.username}"/>
		<property name="password" value="${infobright.bot.334.other.jdbc.password}"/>
		<property name="initialSize" value="2"/>
		<property name="validationQuery" value="select id from t_bot_search_engine limit 1 "/>
    </bean>   
    
    <bean id="IFbot334DataSource" class="org.apache.commons.dbcp.BasicDataSource" destroy-method="close">
        <property name="driverClassName" value="${infobright.bot.334.jdbc.driver}"/>
		<property name="url" value="${infobright.bot.334.jdbc.url}"/>
		<property name="username" value="${infobright.bot.334.jdbc.username}"/>
		<property name="password" value="${infobright.bot.334.jdbc.password}"/>
		<property name="initialSize" value="2"/>
		<property name="validationQuery" value="select id from t_bot_search_engine limit 1 "/>
    </bean>
    
    <bean id="IFbotSmallClientDataSource" class="org.apache.commons.dbcp.BasicDataSource" destroy-method="close" >
        <property name="driverClassName" value="${infobright.bot.jdbc.driver}"/>
		<property name="url" value="${infobright.bot.jdbc.url}"/>
		<property name="username" value="${infobright.bot.jdbc.username}"/>
		<property name="password" value="${infobright.bot.jdbc.password}"/>
		<property name="initialSize" value="2"/>
		<property name="validationQuery" value="select id from t_bot_search_engine limit 1 "/>
    </bean>
    
    <bean id="IFbot185DataSource" class="org.apache.commons.dbcp.BasicDataSource" destroy-method="close">
        <property name="driverClassName" value="${infobright.bot.185.jdbc.driver}"/>
		<property name="url" value="${infobright.bot.185.jdbc.url}"/>
		<property name="username" value="${infobright.bot.185.jdbc.username}"/>
		<property name="password" value="${infobright.bot.185.jdbc.password}"/>
		<property name="initialSize" value="2"/>
		<property name="validationQuery" value="select id from t_bot_search_engine limit 1 "/>
    </bean>
    
    <bean id="IFbot182DataSource" class="org.apache.commons.dbcp.BasicDataSource" destroy-method="close">
        <property name="driverClassName" value="${infobright.bot.182.jdbc.driver}"/>
		<property name="url" value="${infobright.bot.182.jdbc.url}"/>
		<property name="username" value="${infobright.bot.182.jdbc.username}"/>
		<property name="password" value="${infobright.bot.182.jdbc.password}"/>
		<property name="initialSize" value="2"/>
		<property name="validationQuery" value="select id from t_bot_search_engine limit 1 "/>
    </bean>
    
    <bean id="daymicDataSource" class="com.actonia.factorymamage.DynamicDataSource">
		<property name="targetDataSources">
			<map key-type="java.lang.String">
				<entry key="OVERSTOCK_185" value-ref="bot185DataSource"/>
				<entry key="HOMEDEPOT_182" value-ref="bot182DataSource"/>
				<entry key="HOMEAWAY_334" value-ref="bot334DataSource"/>
				<entry key="HOMEAWAY_334_OTHER" value-ref="bot334OtherDataSource"/>
				
				<entry key="OVERSTOCK_185_IF" value-ref="IFbot185DataSource"/>
				<entry key="HOMEDEPOT_182_IF" value-ref="IFbot182DataSource"/>
				<entry key="HOMEAWAY_334_IF" value-ref="IFbot334DataSource"/>
				<entry key="HOMEAWAY_334_OTHER_IF" value-ref="IFbot334OtherDataSource"/>
				<entry key="SMALL_CLIENT_IF" value-ref="IFbotSmallClientDataSource"/>
			</map>
		</property>
		<property name="defaultTargetDataSource" ref="botSmallClientDataSource"/>
	</bean>
	       
</beans>