<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.0.xsd
           http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-2.0.xsd
           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.0.xsd"
       default-autowire="byName" default-lazy-init="true">

    <bean id="stat-filter" class="com.alibaba.druid.filter.stat.StatFilter">
        <property name="slowSqlMillis" value="2000" />
        <property name="logSlowSql" value="true" />
        <property name="slowSqlLogLevel" value="warn" />
    </bean>

    <!-- ========================== DATASOURCE DEFINITIONS ======================== 	-->

    <bean id="actoniaDataSource" class="com.alibaba.druid.pool.DruidDataSource" destroy-method="close">
        <property name="driverClassName" value="${jdbc.driver}"/>
        <property name="url" value="${jdbc.url}"/>
        <property name="username" value="${jdbc.username}"/>
        <property name="password" value="${jdbc.password}"/>
        <property name="initialSize" value="2"/>
        <property name="validationQuery" value="select NOW() "/>
        <property name="socketTimeout" value="180000"/>
        <property name="connectTimeout" value="180000"/>
        <property name="filters" value="com.alibaba.druid.filter.mysql8datetime.MySQL8DateTimeSqlTypeFilter"/>
        <property name="maxActive" value="16" />
        <property name="minIdle" value="2" />
        <property name="timeBetweenEvictionRunsMillis" value="60000" />
        <property name="minEvictableIdleTimeMillis" value="400000"/>
        <property name="maxEvictableIdleTimeMillis" value="490000"/>
        <property name="testOnBorrow" value="false" />
        <property name="testOnReturn" value="false" />
        <property name="testWhileIdle" value="true" />
        <property name="keepAlive" value="true" />
        <property name="proxyFilters">
            <list>
                <ref bean="stat-filter" />
            </list>
        </property>
        <property name="timeBetweenLogStatsMillis" value="300000" />
    </bean>

    <bean id="actoniaAPIDataSource" class="com.alibaba.druid.pool.DruidDataSource" destroy-method="close">
        <property name="driverClassName" value="${api.jdbc.driver}"/>
        <property name="url" value="${api.jdbc.url}"/>
        <property name="username" value="${api.jdbc.username}"/>
        <property name="password" value="${api.jdbc.password}"/>
        <property name="initialSize" value="2"/>
        <property name="validationQuery" value="select NOW() "/>
        <property name="socketTimeout" value="180000"/>
        <property name="connectTimeout" value="180000"/>
        <property name="filters" value="com.alibaba.druid.filter.mysql8datetime.MySQL8DateTimeSqlTypeFilter"/>
        <property name="maxActive" value="16" />
        <property name="minIdle" value="2" />
        <property name="timeBetweenEvictionRunsMillis" value="60000" />
        <property name="minEvictableIdleTimeMillis" value="400000"/>
        <property name="maxEvictableIdleTimeMillis" value="490000"/>
        <property name="testOnBorrow" value="false" />
        <property name="testOnReturn" value="false" />
        <property name="testWhileIdle" value="true" />
        <property name="keepAlive" value="true" />
        <property name="proxyFilters">
            <list>
                <ref bean="stat-filter" />
            </list>
        </property>
        <property name="timeBetweenLogStatsMillis" value="300000" />
    </bean>

    <bean id="backlinkDataSource" class="com.alibaba.druid.pool.DruidDataSource" destroy-method="close">
        <property name="driverClassName" value="${backlink.jdbc.driver}"/>
        <property name="url" value="${backlink.jdbc.url}"/>
        <property name="username" value="${backlink.jdbc.username}"/>
        <property name="password" value="${backlink.jdbc.password}"/>
        <property name="initialSize" value="2"/>
        <property name="validationQuery" value="select NOW() "/>
        <property name="socketTimeout" value="180000"/>
        <property name="connectTimeout" value="180000"/>
        <property name="filters" value="com.alibaba.druid.filter.mysql8datetime.MySQL8DateTimeSqlTypeFilter"/>
        <property name="maxActive" value="16" />
        <property name="minIdle" value="2" />
        <property name="timeBetweenEvictionRunsMillis" value="60000" />
        <property name="minEvictableIdleTimeMillis" value="400000"/>
        <property name="maxEvictableIdleTimeMillis" value="490000"/>
        <property name="testOnBorrow" value="false" />
        <property name="testOnReturn" value="false" />
        <property name="testWhileIdle" value="true" />
        <property name="keepAlive" value="true" />
        <property name="proxyFilters">
            <list>
                <ref bean="stat-filter" />
            </list>
        </property>
        <property name="timeBetweenLogStatsMillis" value="300000" />
    </bean>

    <!-- ========================== DATASOURCE DEFINITIONS ======================== 	-->
    <bean id="seoclarityRankDataSource" class="com.alibaba.druid.pool.DruidDataSource" destroy-method="close">
        <property name="driverClassName" value="${rankcheck.jdbc.driver.new}"/>
        <property name="url" value="${rankcheck.jdbc.url.new}"/>
        <property name="username" value="${rankcheck.jdbc.username.new}"/>
        <property name="password" value="${rankcheck.jdbc.password.new}"/>
        <property name="initialSize" value="2"/>
        <!-- https://www.wrike.com/open.htm?id=20551091  by Harry -->
        <!-- <property name="validationQuery" value="select 1 "/> -->
        <property name="validationQuery" value="select NOW() "/>
        <property name="socketTimeout" value="180000"/>
        <property name="connectTimeout" value="180000"/>
        <property name="filters" value="com.alibaba.druid.filter.mysql8datetime.MySQL8DateTimeSqlTypeFilter"/>
        <property name="maxActive" value="16" />
        <property name="minIdle" value="2" />
        <property name="timeBetweenEvictionRunsMillis" value="60000" />
        <property name="minEvictableIdleTimeMillis" value="400000"/>
        <property name="maxEvictableIdleTimeMillis" value="490000"/>
        <property name="testOnBorrow" value="false" />
        <property name="testOnReturn" value="false" />
        <property name="testWhileIdle" value="true" />
        <property name="keepAlive" value="true" />
        <property name="proxyFilters">
            <list>
                <ref bean="stat-filter" />
            </list>
        </property>
        <property name="timeBetweenLogStatsMillis" value="300000" />
    </bean>

</beans>
