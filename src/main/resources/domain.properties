exec.domain=
notexec.domain=

# date format is yyyy-MM-dd, start date and end date must assign both.
# eg, retrieve.ga.startedate=2011-04-03
retrieve.ga.startedate=2011-08-19
retrieve.ga.enddate=2011-08-19
retrieve.ga.trackdate=

#MUST start and end with ',' 
summary.grouptag.domain.include=
summary.grouptag.domain.exclude=,,


summary.lastxday.entrance=30

enabled.restful=475,292


#copy rank for Zillow
copy.rank.areaGraph.tagList=
copy.rank.areaGraph.dateFrom=
copy.rank.areaGraph.dateTo.start=
copy.rank.areaGraph.dateTo.end=


#copy keyword
copy.engine=1
copy.language=1
#date format is yyy-MM-dd
copy.queryDate=
copy.copyToDate=

crawler.default-user-agent=Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.0 ; Claritybot)

# cloud hosting company
# group 1
crawler.hosting_api_access_username=seoclarity
crawler.hosting_api_access_key=78ae6e1ccbf9da6ecc881006dcab34e3

# group 2
#crawler.hosting_api_access_username=seoclarity2
#crawler.hosting_api_access_key=757bba90ee66ace406607081a65aedd2 


# google big query
google.bg.clientid=<EMAIL>
google.bg.privateKeyFromP12File=/home/<USER>/16ee35e31879d3d227504a0c4131239ed91af778-privatekey.p12


######Plot Solr Crawl####
subserver.solr.connection.url=http://politesolr-1.seoclarity.net:8983/solr/
# For the query against HAproxy
subserver.solr.connection.url.read.only=http://politesolr-2.seoclarity.net:8983/solr/

weeklyRankReport.exportType=xls
weeklyRankReport.mailTo=
weeklyRankReport.mailCc=