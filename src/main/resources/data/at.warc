WARC/1.0
WARC-Type: warcinfo
Content-Type: application/warc-fields
WARC-Date: 2013-04-09T00:11:14Z
WARC-Record-ID: <urn:uuid:972777d2-4177-4c63-9fde-3877dacc174e>
WARC-Filename: at.warc.gz
WARC-Block-Digest: sha1:3C6SPSGP5QN2HNHKPTLYDHDPFYKYAOIX
Content-Length: 233

software: Wget/1.13.4-2608 (linux-gnu)
format: WARC File Format 1.0
conformsTo: http://bibnum.bnf.fr/WARC/WARC_ISO_28500_version1_latestdraft.pdf
robots: classic
wget-arguments: "http://www.archiveteam.org/" "--warc-file=at" 



WARC/1.0
WARC-Type: request
WARC-Target-URI: http://www.archiveteam.org/
Content-Type: application/http;msgtype=request
WARC-Date: 2013-04-09T00:11:14Z
WARC-Record-ID: <urn:uuid:72627240-aacd-486d-9f9a-8abe3afabda3>
WARC-IP-Address: *************
WARC-Warcinfo-ID: <urn:uuid:972777d2-4177-4c63-9fde-3877dacc174e>
WARC-Block-Digest: sha1:N4NWRSTHXCBHNXX6AATO4HQXK6D66GRP
Content-Length: 124

GET / HTTP/1.1
User-Agent: Wget/1.13.4-2608 (linux-gnu)
Accept: */*
Host: www.archiveteam.org
Connection: Keep-Alive



WARC/1.0
WARC-Type: response
WARC-Record-ID: <urn:uuid:31198e82-3867-46e8-a76a-2fbff03ecaf8>
WARC-Warcinfo-ID: <urn:uuid:972777d2-4177-4c63-9fde-3877dacc174e>
WARC-Concurrent-To: <urn:uuid:72627240-aacd-486d-9f9a-8abe3afabda3>
WARC-Target-URI: http://www.archiveteam.org/
WARC-Date: 2013-04-09T00:11:14Z
WARC-IP-Address: *************
WARC-Block-Digest: sha1:QCUGI2J67ARRTBNMEQYIFZJFOJMO2UOZ
WARC-Payload-Digest: sha1:3I42H3S6NNFQ2MSVX7XZKYAYSCX5QBYJ
Content-Type: application/http;msgtype=response
Content-Length: 559

HTTP/1.1 301 Moved Permanently
Date: Tue, 09 Apr 2013 00:11:14 GMT
Server: Apache/2.2.21 (Unix) mod_ssl/2.2.21 OpenSSL/1.0.0-fips mod_bwlimited/1.4
X-Powered-By: PHP/5.2.17
X-Content-Type-Options: nosniff
Vary: Accept-Encoding,Cookie
Expires: Thu, 01 Jan 1970 00:00:00 GMT
Cache-Control: private, must-revalidate, max-age=0
Last-Modified: Tue, 09 Apr 2013 00:11:14 GMT
Location: http://www.archiveteam.org/index.php?title=Main_Page
Content-Length: 0
Keep-Alive: timeout=5, max=100
Connection: Keep-Alive
Content-Type: text/html; charset=utf-8



WARC/1.0
WARC-Type: request
WARC-Target-URI: http://www.archiveteam.org/index.php?title=Main_Page
Content-Type: application/http;msgtype=request
WARC-Date: 2013-04-09T00:11:14Z
WARC-Record-ID: <urn:uuid:7ee09f86-407c-4323-8793-82d4f3ec2b1b>
WARC-IP-Address: *************
WARC-Warcinfo-ID: <urn:uuid:972777d2-4177-4c63-9fde-3877dacc174e>
WARC-Block-Digest: sha1:LM2UACTK5HBN7NLY5G6XCMKHZ4SH6WMC
Content-Length: 149

GET /index.php?title=Main_Page HTTP/1.1
User-Agent: Wget/1.13.4-2608 (linux-gnu)
Accept: */*
Host: www.archiveteam.org
Connection: Keep-Alive



WARC/1.0
WARC-Type: response
WARC-Record-ID: <urn:uuid:103a7ae2-30ec-46f5-b749-d1368dee3641>
WARC-Warcinfo-ID: <urn:uuid:972777d2-4177-4c63-9fde-3877dacc174e>
WARC-Concurrent-To: <urn:uuid:7ee09f86-407c-4323-8793-82d4f3ec2b1b>
WARC-Target-URI: http://www.archiveteam.org/index.php?title=Main_Page
WARC-Date: 2013-04-09T00:11:14Z
WARC-IP-Address: *************
WARC-Block-Digest: sha1:O6KYP6554UOINEKFM76PXE2XTNWIYVR4
WARC-Payload-Digest: sha1:ZSGODTVU5V5765HRJBD4WQX6QDBAGMW4
Content-Type: application/http;msgtype=response
Content-Length: 24508

HTTP/1.1 200 OK
Date: Tue, 09 Apr 2013 00:11:14 GMT
Server: Apache/2.2.21 (Unix) mod_ssl/2.2.21 OpenSSL/1.0.0-fips mod_bwlimited/1.4
X-Powered-By: PHP/5.2.17
X-Content-Type-Options: nosniff
Content-language: en
Vary: Accept-Encoding,Cookie
Expires: Thu, 01 Jan 1970 00:00:00 GMT
Cache-Control: private, must-revalidate, max-age=0
Last-Modified: Tue, 02 Apr 2013 06:49:46 GMT
Keep-Alive: timeout=5, max=99
Connection: Keep-Alive
Transfer-Encoding: chunked
Content-Type: text/html; charset=UTF-8

5db1
<!DOCTYPE html>
<html lang="en" dir="ltr" class="client-nojs">
<head>
<title>Archiveteam</title>
<meta charset="UTF-8" />
<meta name="generator" content="MediaWiki 1.18.1" />
<link rel="shortcut icon" href="/favicon.ico" />
<link rel="search" type="application/opensearchdescription+xml" href="/opensearch_desc.php" title="Archiveteam (en)" />
<link rel="EditURI" type="application/rsd+xml" href="http://www.archiveteam.org/api.php?action=rsd" />
<link rel="alternate" type="application/atom+xml" title="Archiveteam Atom feed" href="/index.php?title=Special:RecentChanges&amp;feed=atom" />
<link rel="stylesheet" href="/load.php?debug=false&amp;lang=en&amp;modules=mediawiki.legacy.commonPrint%2Cshared%7Cskins.monobook&amp;only=styles&amp;skin=monobook&amp;*" />
<!--[if lt IE 5.5000]><link rel="stylesheet" href="/skins/monobook/IE50Fixes.css?303" media="screen" /><![endif]-->
<!--[if IE 5.5000]><link rel="stylesheet" href="/skins/monobook/IE55Fixes.css?303" media="screen" /><![endif]-->
<!--[if IE 6]><link rel="stylesheet" href="/skins/monobook/IE60Fixes.css?303" media="screen" /><![endif]-->
<!--[if IE 7]><link rel="stylesheet" href="/skins/monobook/IE70Fixes.css?303" media="screen" /><![endif]--><meta name="ResourceLoaderDynamicStyles" content="" />
<link rel="stylesheet" href="/load.php?debug=false&amp;lang=en&amp;modules=site&amp;only=styles&amp;skin=monobook&amp;*" />
<style>a:lang(ar),a:lang(ckb),a:lang(fa),a:lang(kk-arab),a:lang(mzn),a:lang(ps),a:lang(ur){text-decoration:none}a.new,#quickbar a.new{color:#ba0000}

/* cache key: archivet_archiveteamwiki-wiki_:resourceloader:filter:minify-css:4:c88e2bcd56513749bec09a7e29cb3ffa */
</style>
<script src="/load.php?debug=false&amp;lang=en&amp;modules=startup&amp;only=scripts&amp;skin=monobook&amp;*"></script>
<script>if(window.mw){
	mw.config.set({"wgCanonicalNamespace": "", "wgCanonicalSpecialPageName": false, "wgNamespaceNumber": 0, "wgPageName": "Main_Page", "wgTitle": "Main Page", "wgCurRevisionId": 10944, "wgArticleId": 1, "wgIsArticle": true, "wgAction": "view", "wgUserName": null, "wgUserGroups": ["*"], "wgCategories": [], "wgBreakFrames": false, "wgRestrictionEdit": ["sysop"], "wgRestrictionMove": ["sysop"], "wgIsMainPage": true});
}
</script><script>if(window.mw){
	mw.loader.load(["mediawiki.page.startup"]);
}
</script>
</head>
<body class="mediawiki ltr sitedir-ltr ns-0 ns-subject page-Main_Page action-view skin-monobook">
<div id="globalWrapper">
<div id="column-content"><div id="content">
	<a id="top"></a>
	
	<h1 id="firstHeading" class="firstHeading">Main Page</h1>
	<div id="bodyContent">
		<div id="siteSub">From Archiveteam</div>
		<div id="contentSub"></div>
		<div id="jump-to-nav">Jump to: <a href="#column-one">navigation</a>, <a href="#searchInput">search</a></div>
		<!-- start content -->
<div lang="en" dir="ltr" class="mw-content-ltr"><center>
<table style="width:100%;border-spacing:8px;margin:12px 0px 0px 0px">
<tr><td style="width:60%;border:1px solid #FFB9B9;background-color:#FFFFF0;vertical-align:top;color:#000">
<table class="thumb" width="100%" cellpadding="2" cellspacing="5" style="vertical-align:top;background-color:#FFFFF0;">
<tr><td>
</td><td style="color:#000;text-align:left;vertical-align:top">
<h3> <span class="mw-headline" id="HISTORY_IS_OUR_FUTURE"> HISTORY IS OUR FUTURE </span></h3>
<div class="floatright"><a href="/index.php?title=File:Archiveteam.jpg" class="image"><img alt="Archiveteam.jpg" src="/images/thumb/e/e6/Archiveteam.jpg/200px-Archiveteam.jpg" width="200" height="200" /></a></div>
<p><i>And we've been trashing our history</i>
</p><p>Archive Team is a loose collective of rogue archivists, programmers, writers and loudmouths dedicated to saving our digital heritage. Since 2009 this variant force of nature has caught wind of shutdowns, shutoffs, mergers, and plain old deletions - and done our best to save the history before it's lost forever. Along the way, we've gotten attention, resistance, press and discussion, but most importantly, we've gotten the message out: <b>IT DOESN'T HAVE TO BE THIS WAY</b>.
</p><p>This website is intended to be an offloading point and information depot for a number of archiving projects, all related to saving websites or data that is in danger of being lost. Besides serving as a hub for team-based pulling down and mirroring of data, this site will provide advice on managing your own data and rescuing it from the brink of destruction.
</p>
<h3> <span class="mw-headline" id="Currently_Active_Projects_.28Get_Involved_Here.21.29">Currently Active Projects (Get Involved Here!) </span></h3>
</tr><tr></td><td style="color:#000" colspan="2">
<ul><li><b><a href="/index.php?title=Posterous" title="Posterous">Posterous</a></b>: A blogging site shutting down April 30. IRC Channel #preposterus
</li><li><b><a href="/index.php?title=Yahoo!_Messages" title="Yahoo! Messages">Yahoo! Messages</a></b>: Message boards on Yahoo! shutting down April 1st. IRC Channel #BurnTheMessenger
</li><li><b><a href="/index.php?title=Opensolaris" title="Opensolaris" class="mw-redirect">Opensolaris</a></b>: Shutting down March 23. IRC Channel #ClosedSolaris
</li><li><b><a href="/index.php?title=Gamespy,_1up,_UGO,_IGN" title="Gamespy, 1up, UGO, IGN" class="mw-redirect">Gamespy, 1up, UGO, IGN</a></b>: Shutdown date unknown, announced Feb 21, 2013. IRC Channel #ispygames
</li><li><b><a href="/index.php?title=Formspring" title="Formspring">Formspring</a></b>: shutting down April 15. IRC Channel #firespring
</li></ul>
<p>Feel free to join us on the <a href="/index.php?title=IRC_Channel" title="IRC Channel">IRC channel</a>! We're on the EFnet network in a channel called <b>#archiveteam</b>, where we say truly awful things. 
</p><p><br />
</p>
<h3> <span class="mw-headline" id="Archive_Team_News">Archive Team News</span></h3>
</tr><tr><th colspan="2">
</th></tr>
<tr><td style="color:#000" colspan="2">
<ul><li> <b>March, 2013</b>: <a rel="nofollow" class="external text" href="http://www.archiveteam.org/index.php?title=Yahoo!_Messages"><span style="color:#000075">Yahoo burns the messenger.</span></a>
</li><li> <b>February, 2013</b>: <a rel="nofollow" class="external text" href="http://www.archiveteam.org/index.php?title=Posterous"><span style="color:#000075">Posterous announces it's deleting history at the end of April.</span></a>
</li><li> <b>January, 2013</b>: That took long enough! We've turned on new user account creation, with a "are you human" edit checker added.
</li><li> <b>August, 2012</b>: It's August Cleanup time! We're shutting off new user accounts while we clean out spam and generally shore up the ol' barge.
</li><li> <b>May, 2012</b>: <a rel="nofollow" class="external text" href="http://www.archiveteam.org/index.php?title=Tabblo"><span style="color:#000075">Tabblo announces its closure scheduled for May 30th, giving its userbase just ten days of warning. Archive Team is on the case.</span></a> 
</li><li> <b>May, 2012</b>: Archive Team's save of <a rel="nofollow" class="external text" href="http://web.archive.org/web/**************/http://crave.cnet.co.uk/0,********,********-10,00.htm">Stage6</a>, a defunct video sharing site run by DivX, Inc. is permanently preserved at the <a rel="nofollow" class="external text" href="http://archive.org/details/stage6">Internet Archive</a>.
</li><li> <b>April, 2012</b>: <a rel="nofollow" class="external text" href="http://www.archiveteam.org/index.php?title=Friendster"><span style="color:#000075">20 million Friendster accounts spanning 14 terabytes are successfully rescued for permanent storage by Archive Team.</span></a>  
</li><li> <b>March, 2012</b>: <a rel="nofollow" class="external text" href="http://www.archiveteam.org/index.php?title=FortuneCity"><span style="color:#000075">FortuneCity announced the end of its free-hosting model, threatening around one million user-generated websites.</span></a>
</li><li> <b>March, 2012</b>: We've switched servers to one of those new-fangled hosting companies that aren't hacked. We're going to sell you a lot less in the way of medical supplies now.
</li><li> <b>December, 2011</b>: POE News says it will soon be nevermore: <a rel="nofollow" class="external text" href="http://www.poe-news.com/forums/sp.php?pi=**********">Announcement</a>
</li><li> <b>December, 2011</b>: GamePro magazine halts publication and their website goes dark.
</li><li> <b>November, 2011</b>: <a rel="nofollow" class="external text" href="http://www.archiveteam.org/index.php?title=Splinder"><span style="color:#000000">Archive Team rescues over 1.3 million users' data from Splinder's closure.</span></a>
</li><li> <b>July 2011</b>: Archive Team teaches you how to <a href="/index.php?title=Rescuing_Floppy_Disks" title="Rescuing Floppy Disks">rescue data from Floppy Disks</a>.
</li><li> <b>May, 2011</b>: <a rel="nofollow" class="external text" href="http://www.archiveteam.org/index.php?title=Friendster"><span style="color:#000075">Friendster is deleting everything at the end of the month.</span></a>
</li><li> <b>May, 2011</b>: <a rel="nofollow" class="external text" href="http://www.archiveteam.org/index.php?title=poetry.com"><span style="color:#000075">Archive Team keeps it classy at poetry.com.</span></a>
</li><li> <b>April, 2011</b>: <a rel="nofollow" class="external text" href="http://www.archiveteam.org/index.php?title=Google_Video"><span style="color:#000075">How about some Google Video?</span></a>
</li><li> <b>March, 2011</b>: <a rel="nofollow" class="external text" href="http://www.archive.org/details/personalarchiveconf"><span style="color:#000075">The 2011 Personal Digital Archiving Conference talks are available.</span></a>
</li><li> <b>February, 2011</b>: <a rel="nofollow" class="external text" href="http://www.archiveteam.org/index.php?title=Yahoo!_Video"><span style="color:#000075">Let's watch some Yahoo! Video</span></a>
</li><li> <b>December, 2010</b>: <a rel="nofollow" class="external text" href="http://www.archiveteam.org/index.php?title=Delicious"><span style="color:#000075">Archiveteam is Delicious!</span></a>
</li><li> <b>October, 2010</b>: <a rel="nofollow" class="external text" href="http://www.archiveteam.org/index.php?title=Geocities"><span style="color:#000075">Archive Team offers Geocities as a torrent.</span></a>
</li><li> <b>December 23, 2009</b>: <a rel="nofollow" class="external text" href="http://www.archiveteam.org/index.php?title=starwars.yahoo.com"><span style="color:#000075">Yahoo shut down starwars.yahoo.com. We got a copy.</span></a>
</li><li> <b>October, 2009</b>: <a rel="nofollow" class="external text" href="http://www.archiveteam.org/index.php?title=Geocities"><span style="color:#000075">Geocities closing is definitely the top of the charts.</span></a>
</li></ul>
</td></tr>
</table>
</td><td style="width:40%;border:1px solid #cedff2;background-color:#f5faff;vertical-align:top">
<table width="100%" cellpadding="2" cellspacing="5" style="vertical-align:top;background-color:#f5faff">
<tr><td>
<p><a href="/index.php?title=File:Archivetime.png" class="image"><img alt="Archivetime.png" src="/images/8/8a/Archivetime.png" width="435" height="211" /></a>
</p>
</tr><tr><th>
<h2 style="margin:0;background:#cedff2;font-size:120%;font-weight:bold;border:1px solid #a3b0bf;text-align:left;color:#000;padding:0.2em 0.4em;"> <span class="mw-headline" id="What_is_What">What is What</span></h2>
</th></tr>
<tr><td style="color:#000">
<ul><li> <a href="/index.php?title=Who_We_Are" title="Who We Are">Who We Are</a> and how you can join our cause!
</li></ul>
<ul><li> <a href="/index.php?title=Deathwatch" title="Deathwatch">Deathwatch</a> is where we keep track of sites that are sickly, dying or dead.
</li></ul>
<ul><li> <a href="/index.php?title=Fire_Drill" title="Fire Drill" class="mw-redirect">Fire Drill</a> is where we keep track of sites that seem fine but a lot depends on them.
</li></ul>
<ul><li> <a href="/index.php?title=Projects" title="Projects">Projects</a> is a comprehensive list of AT endeavors.
</li></ul>
<ul><li> <a href="/index.php?title=Philosophy" title="Philosophy">Philosophy</a> describes the ideas underpinning our work.
</li></ul>
<ul><li> <a href="/index.php?title=Archives" title="Archives">Archives</a> 
</li></ul>
</td></tr>
<tr><th>
<h2 style="margin:0;background:#cedff2;font-size:120%;font-weight:bold;border:1px solid #a3b0bf;text-align:left;color:#000;padding:0.2em 0.4em;"> <span class="mw-headline" id="Some_Starting_Points">Some Starting Points</span></h2>
</th></tr>
<tr><td style="color:#000">
<ul><li> <a href="/index.php?title=Introduction" title="Introduction">The Introduction</a> is an overview of basic archiving methods.
</li></ul>
<ul><li> <a href="/index.php?title=Why_Back_Up%3F" title="Why Back Up?">Why Back Up?</a> Because they don't care about you.
</li></ul>
<ul><li> <a href="/index.php?title=Facebook" title="Facebook">Back Up your Facebook Data</a> Learn how to liberate your personal data from Facebook.
</li></ul>
<ul><li> <a href="/index.php?title=Software" title="Software">Software</a> will assist you in regaining control of your data by providing tools for information backup, archiving and distribution. 
</li></ul>
<ul><li> <a href="/index.php?title=Formats" title="Formats">Formats</a> will familiarise you with the various data formats, and how to ensure your files will be readable in the future.
</li></ul>
<ul><li> <a href="/index.php?title=Storage_Media" title="Storage Media">Storage Media</a> is about where to get it, what to get, and how to use it.
</li></ul>
<ul><li> <a href="/index.php?title=Recommended_Reading" title="Recommended Reading">Recommended Reading</a> links to others sites for further information.
</li></ul>
<ul><li> <a href="/index.php?title=Frequently_Asked_Questions" title="Frequently Asked Questions">Frequently Asked Questions</a> is where we answer common questions.
</li></ul>
</td></tr>
<tr><th>
<h2 style="margin:0;background:#cedff2;font-size:120%;font-weight:bold;border:1px solid #a3b0bf;text-align:left;color:#000;padding:0.2em 0.4em;"> <span class="mw-headline" id="Quote_of_the_Moment">Quote of the Moment</span></h2>
</th></tr>
<tr><td style="color:#000">
</tr><tr></td><td style="margin:20;background-color:#000000;font-size:200%;font-weight:bold;border:1px solid #a3b0bf;text-align:center;color:#fff;">
<p>"[Yahoo!] found the way to destroy 
the most massive amount of history
in the shortest amount of time 
with absolutely no recourse"
</p>
</td></tr>
<tr><td style="text-align:right">
<p><a rel="nofollow" class="external text" href="http://www.time.com/time/business/article/0,8599,1936645,00.html">Internet Atrocity! GeoCities' Demise Erases Web History</a> 
<br />By Dan Fletcher, TIME Magazine, Monday, Nov. 09, 2009
</p>
</td></tr>
<tr><td>
<h3> <span class="mw-headline" id="Ended_Projects"> Ended Projects </span></h3>
<ul><li> <b><a href="/index.php?title=MobileMe" title="MobileMe">MobileMe</a></b> - Apple's file storage and sharing service, currently hosting over 200 terabytes of data, shut down on June 30, 2012. 
<ul><li> Link to Mobile me search here
</li></ul>
</li><li> <b><a href="/index.php?title=Tabblo" title="Tabblo">Tabblo</a></b> - A site where users told stories with pictures. Closed May 30, 2012.
<ul><li> Link to search here
</li></ul>
</li><li> <b><a href="/index.php?title=FanFiction.Net" title="FanFiction.Net">FanFiction.Net</a></b> - Around 7 million fan-fiction stories hosted on what may be the largest site of its kind in the world. They're not shutting down but Archiveteam has a copy "just in case".
<ul><li> Link to archive?
</li></ul>
</li><li> <b><a href="/index.php?title=Geocities" title="Geocities" class="mw-redirect">Geocities</a></b> - We archived most of geocities mother fuckers!
<ul><li> Link to archive....
</li></ul>
</li><li> <b><a href="/index.php?title=Fortune_City&amp;action=edit&amp;redlink=1" class="new" title="Fortune City (page does not exist)">Fortune City</a></b> - It maybe gone but we've still got it
<ul><li> As always the link...
</li></ul>
</li></ul>
<p><a href="/index.php?title=Category:Rescued_Sites" title="Category:Rescued Sites"> More</a>
</p>
</td><td></tr>
</table>
</td></tr>
</table>
<p><b>Archive Team is in no way affiliated with the fine folks at <a rel="nofollow" class="external text" href="http://www.archive.org">ARCHIVE.ORG</a></b>
</p>
<b>Archive Team can always be reached at <a rel="nofollow" class="external text" href="mailto:<EMAIL>"><EMAIL></a></b></center>

<!-- 
NewPP limit report
Preprocessor node count: 66/1000000
Post-expand include size: 0/2097152 bytes
Template argument size: 0/2097152 bytes
Expensive parser function count: 0/100
-->

<!-- Saved in parser cache with key archivet_archiveteamwiki-wiki_:pcache:idhash:1-0!*!0!!*!2!* and timestamp 20130408070249 -->
</div><div class="printfooter">
Retrieved from "<a href="http://www.archiveteam.org/index.php?title=Main_Page&amp;oldid=10944">http://www.archiveteam.org/index.php?title=Main_Page&amp;oldid=10944</a>"</div>
		<div id='catlinks' class='catlinks catlinks-allhidden'></div>		<!-- end content -->
				<div class="visualClear"></div>
	</div>
</div></div>
<div id="column-one">
	<div id="p-cactions" class="portlet">
		<h5>Views</h5>
		<div class="pBody">
			<ul>
				<li id="ca-nstab-main" class="selected"><a href="/index.php?title=Main_Page" title="View the content page [c]" accesskey="c">Page</a></li>
				<li id="ca-talk"><a href="/index.php?title=Talk:Main_Page" title="Discussion about the content page [t]" accesskey="t">Discussion</a></li>
				<li id="ca-viewsource"><a href="/index.php?title=Main_Page&amp;action=edit" title="This page is protected.&#10;You can view its source [e]" accesskey="e">View source</a></li>
				<li id="ca-history"><a href="/index.php?title=Main_Page&amp;action=history" title="Past revisions of this page [h]" accesskey="h">History</a></li>
			</ul>
		</div>
	</div>
	<div class="portlet" id="p-personal">
		<h5>Personal tools</h5>
		<div class="pBody">
			<ul>
				<li id="pt-login"><a href="/index.php?title=Special:UserLogin&amp;returnto=Main+Page" title="You are encouraged to log in; however, it is not mandatory [o]" accesskey="o">Log in / create account</a></li>
			</ul>
		</div>
	</div>
	<div class="portlet" id="p-logo">
		<a title="Visit the main page" style="background-image: url(/images/Archiveteamsmall.png);" href="/index.php?title=Main_Page"></a>
	</div>
	<script type="text/javascript"> if (window.isMSIE55) fixalpha(); </script>
	<div class="generated-sidebar portlet" id="p-navigation">
		<h5>Navigation</h5>
		<div class='pBody'>
			<ul>
				<li id="n-mainpage-description"><a href="/index.php?title=Main_Page" title="Visit the main page [z]" accesskey="z">Main page</a></li>
				<li id="n-portal"><a href="/index.php?title=Archiveteam:Community_portal" title="About the project, what you can do, where to find things">Community portal</a></li>
				<li id="n-currentevents"><a href="/index.php?title=Archiveteam:Current_events" title="Find background information on current events">Current events</a></li>
				<li id="n-recentchanges"><a href="/index.php?title=Special:RecentChanges" title="A list of recent changes in the wiki [r]" accesskey="r">Recent changes</a></li>
				<li id="n-randompage"><a href="/index.php?title=Special:Random" title="Load a random page [x]" accesskey="x">Random page</a></li>
				<li id="n-help"><a href="/index.php?title=Help:Contents" title="The place to find out">Help</a></li>
			</ul>
		</div>
	</div>
	<div id="p-search" class="portlet">
		<h5><label for="searchInput">Search</label></h5>
		<div id="searchBody" class="pBody">
			<form action="/index.php" id="searchform">
				<input type='hidden' name="title" value="Special:Search"/>
				<input type="search" name="search" title="Search Archiveteam [f]" accesskey="f" id="searchInput" />
				<input type="submit" name="go" value="Go" title="Go to a page with this exact name if exists" id="searchGoButton" class="searchButton" />&#160;
				<input type="submit" name="fulltext" value="Search" title="Search the pages for this text" id="mw-searchButton" class="searchButton" />
			</form>
		</div>
	</div>
	<div class="portlet" id="p-tb">
		<h5>Toolbox</h5>
		<div class="pBody">
			<ul>
				<li id="t-whatlinkshere"><a href="/index.php?title=Special:WhatLinksHere/Main_Page" title="A list of all wiki pages that link here [j]" accesskey="j">What links here</a></li>
				<li id="t-recentchangeslinked"><a href="/index.php?title=Special:RecentChangesLinked/Main_Page" title="Recent changes in pages linked from this page [k]" accesskey="k">Related changes</a></li>
				<li id="t-specialpages"><a href="/index.php?title=Special:SpecialPages" title="A list of all special pages [q]" accesskey="q">Special pages</a></li>
				<li><a href="/index.php?title=Main_Page&amp;printable=yes" rel="alternate">Printable version</a></li>
				<li id="t-permalink"><a href="/index.php?title=Main_Page&amp;oldid=10944" title="Permanent link to this revision of the page">Permanent link</a></li>
			</ul>
		</div>
	</div>
</div><!-- end of the left (by default at least) column -->
<div class="visualClear"></div>
<div id="footer">
	<div id="f-poweredbyico">
		<a href="http://www.mediawiki.org/"><img src="/skins/common/images/poweredby_mediawiki_88x31.png" alt="Powered by MediaWiki" width="88" height="31" /></a>
	</div>
	<ul id="f-list">
		<li id="lastmod"> This page was last modified on 2 April 2013, at 02:49.</li>
		<li id="viewcount">This page has been accessed 587,325 times.</li>
		<li id="privacy"><a href="/index.php?title=Archiveteam:Privacy_policy" title="Archiveteam:Privacy policy">Privacy policy</a></li>
		<li id="about"><a href="/index.php?title=Archiveteam:About" title="Archiveteam:About">About Archiveteam</a></li>
		<li id="disclaimer"><a href="/index.php?title=Archiveteam:General_disclaimer" title="Archiveteam:General disclaimer">Disclaimers</a></li>
	</ul>
</div>
</div>
<script>if(window.mw){
	mw.loader.load(["mediawiki.user", "mediawiki.util", "mediawiki.page.ready", "mediawiki.legacy.wikibits", "mediawiki.legacy.ajax"]);
}
</script>
<script src="/load.php?debug=false&amp;lang=en&amp;modules=site&amp;only=scripts&amp;skin=monobook&amp;*"></script>
<script>if(window.mw){
	mw.user.options.set({"ccmeonemails":0,"cols":80,"date":"default","diffonly":0,"disablemail":0,"disablesuggest":0,"editfont":"default","editondblclick":0,"editsection":1,"editsectiononrightclick":0,"enotifminoredits":0,"enotifrevealaddr":0,"enotifusertalkpages":1,"enotifwatchlistpages":0,"extendwatchlist":0,"externaldiff":0,"externaleditor":0,"fancysig":0,"forceeditsummary":0,"gender":"unknown","hideminor":0,"hidepatrolled":0,"highlightbroken":1,"imagesize":2,"justify":0,"math":1,"minordefault":0,"newpageshidepatrolled":0,"nocache":0,"noconvertlink":0,"norollbackdiff":0,"numberheadings":0,"previewonfirst":0,"previewontop":1,"quickbar":5,"rcdays":7,"rclimit":50,"rememberpassword":0,"rows":25,"searchlimit":20,"showhiddencats":0,"showjumplinks":1,"shownumberswatching":1,"showtoc":1,"showtoolbar":1,"skin":"monobook","stubthreshold":0,"thumbsize":2,"underline":2,"uselivepreview":0,"usenewrc":0,"watchcreations":0,"watchdefault":0,"watchdeletion":0,"watchlistdays":3,"watchlisthideanons":0,
	"watchlisthidebots":0,"watchlisthideliu":0,"watchlisthideminor":0,"watchlisthideown":0,"watchlisthidepatrolled":0,"watchmoves":0,"wllimit":250,"variant":"en","language":"en","searchNs0":true,"searchNs1":false,"searchNs2":false,"searchNs3":false,"searchNs4":false,"searchNs5":false,"searchNs6":false,"searchNs7":false,"searchNs8":false,"searchNs9":false,"searchNs10":false,"searchNs11":false,"searchNs12":false,"searchNs13":false,"searchNs14":false,"searchNs15":false});;mw.user.tokens.set({"editToken":"+\\","watchToken":false});;mw.loader.state({"user.options":"ready","user.tokens":"ready"});
	
	/* cache key: archivet_archiveteamwiki-wiki_:resourceloader:filter:minify-js:4:99acc2c3ab516bb21085c70c2195f3df */
}
</script><!-- Served in 0.105 secs. --></body></html>
0



WARC/1.0
WARC-Type: resource
WARC-Record-ID: <urn:uuid:a858a494-ba58-4d76-98e9-1137023cc80f>
WARC-Warcinfo-ID: <urn:uuid:972777d2-4177-4c63-9fde-3877dacc174e>
WARC-Target-URI: metadata://gnu.org/software/wget/warc/MANIFEST.txt
WARC-Date: 2013-04-09T00:11:14Z
WARC-Block-Digest: sha1:S6M4K57K2XVAICGOBX5DPI3KOXXRK6KA
Content-Type: text/plain
Content-Length: 48

<urn:uuid:972777d2-4177-4c63-9fde-3877dacc174e>


WARC/1.0
WARC-Type: resource
WARC-Record-ID: <urn:uuid:a858a494-ba58-4d76-98e9-1137023cc80f>
WARC-Warcinfo-ID: <urn:uuid:972777d2-4177-4c63-9fde-3877dacc174e>
WARC-Target-URI: metadata://gnu.org/software/wget/warc/wget_arguments.txt
WARC-Date: 2013-04-09T00:11:14Z
WARC-Block-Digest: sha1:5KL3CJH32QFHYSN3HRTRKGEPQTECPJDS
Content-Type: text/plain
Content-Length: 48

"http://www.archiveteam.org/" "--warc-file=at" 


WARC/1.0
WARC-Type: resource
WARC-Record-ID: <urn:uuid:2599902d-73b6-4782-aa7a-4e900c005cce>
WARC-Warcinfo-ID: <urn:uuid:972777d2-4177-4c63-9fde-3877dacc174e>
WARC-Concurrent-To: <urn:uuid:a858a494-ba58-4d76-98e9-1137023cc80f>
WARC-Target-URI: metadata://gnu.org/software/wget/warc/wget.log
WARC-Date: 2013-04-09T00:11:14Z
WARC-Block-Digest: sha1:QKDSBTCLYSWFCN7JWSPFDDZT3RC6WY5G
Content-Type: text/plain
Content-Length: 800

Opening WARC file ‘at.warc.gz’.

--2013-04-08 20:11:14--  http://www.archiveteam.org/
Resolving www.archiveteam.org... *************
Connecting to www.archiveteam.org|*************|:80... connected.
HTTP request sent, awaiting response... 301 Moved Permanently
Location: http://www.archiveteam.org/index.php?title=Main_Page [following]

     0K                                                        0.00 =0s

--2013-04-08 20:11:14--  http://www.archiveteam.org/index.php?title=Main_Page
Reusing existing connection to www.archiveteam.org:80.
HTTP request sent, awaiting response... 200 OK
Length: unspecified [text/html]
Saving to: ‘index.html.1’

     0K .......... .......... ...                               300K=0.08s

2013-04-08 20:11:14 (300 KB/s) - ‘index.html.1’ saved [23985]



