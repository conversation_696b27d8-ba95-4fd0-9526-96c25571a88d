{"com.digitalpebble.stormcrawler.parse.ParseFilters": [{"class": "com.digitalpebble.stormcrawler.parse.filter.XPathFilter", "name": "XPath<PERSON><PERSON>er", "params": {"canonical": "//*[@rel=\"canonical\"]/@href", "parse.description": ["//*[@name=\"description\"]/@content", "//*[@name=\"Description\"]/@content"], "parse.title": ["//TITLE", "//META[@name=\"title\"]/@content"], "parse.keywords": "//META[@name=\"keywords\"]/@content"}}, {"class": "com.digitalpebble.stormcrawler.parse.filter.LinkParseFilter", "name": "LinkParse<PERSON>ilt<PERSON>", "params": {"pattern": "//FRAME/@src"}}, {"class": "com.digitalpebble.stormcrawler.parse.filter.DomainParseFilter", "name": "DomainParseFilter", "params": {"key": "domain", "byHost": false}}, {"class": "com.digitalpebble.stormcrawler.parse.filter.MimeTypeNormalization", "name": "MimeTypeNormalization"}, {"class": "com.digitalpebble.stormcrawler.parse.filter.CommaSeparatedToMultivaluedMetadata", "name": "CommaSeparatedToMultivaluedMetadata", "params": {"keys": ["parse.keywords"]}}]}