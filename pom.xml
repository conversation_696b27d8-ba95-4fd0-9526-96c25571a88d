<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>polite-crawl</groupId>
	<artifactId>polite-crawl-put-messages</artifactId>
	<version>1.0</version>
	<packaging>jar</packaging>

	<name>polite-crawl-put-messages</name>
	<url>http://maven.apache.org</url>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<maven.compiler.source>1.8</maven.compiler.source>
		<maven.compiler.target>1.8</maven.compiler.target>
	</properties>

	<repositories>
		<repository>
			<id>maven-restlet</id>
			<name>Public online Restlet repository</name>
			<url>https://maven.restlet.talend.com</url>
		</repository>
  		<repository>
    		<id>bedatadriven</id>
    		<name>bedatadriven public repo</name>
    		<url>https://nexus.bedatadriven.com/content/groups/public/</url>
  		</repository>
	</repositories>

	<dependencies>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-core</artifactId>
			<version>2.5</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-jdbc</artifactId>
			<version>2.5</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
			<version>3.0.4.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>javax.persistence</groupId>
			<artifactId>persistence-api</artifactId>
			<version>1.0.2</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context-support</artifactId>
			<version>3.0.5.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>javax.mail</groupId>
			<artifactId>javax.mail-api</artifactId>
			<version>1.5.1</version>
		</dependency>
		<dependency>
			<groupId>org.apache.velocity</groupId>
			<artifactId>velocity-tools</artifactId>
			<version>2.0</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>2.8.5</version>
		</dependency>
		<dependency>
			<groupId>commons-dbcp</groupId>
			<artifactId>commons-dbcp</artifactId>
			<version>1.4</version>
		</dependency>
		<dependency>
			<groupId>com.mysql</groupId>
			<artifactId>mysql-connector-j</artifactId>
			<version>8.0.33</version>
		</dependency>
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>5.1.37</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/com.alibaba/druid -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid</artifactId>
			<version>1.2.18</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/com.google.code.gson/gson -->
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
			<version>2.8.5</version>
		</dependency>

		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.5</version>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.12</version>
		</dependency>
		<dependency>
			<groupId>com.sun.mail</groupId>
			<artifactId>javax.mail</artifactId>
			<version>1.5.1</version>
		</dependency>

		<dependency>
    		<groupId>com.amazonaws</groupId>
    		<artifactId>aws-java-sdk-sqs</artifactId>
    		<version>1.11.982</version>
		</dependency>

		<dependency>
    		<groupId>com.amazonaws</groupId>
    		<artifactId>aws-java-sdk</artifactId>
    		<version>1.11.419</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.apache.jcs/jcs -->
		<dependency>
			<groupId>org.apache.jcs</groupId>
			<artifactId>jcs</artifactId>
			<version>1.3</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/ru.yandex.clickhouse/clickhouse-jdbc -->
		<dependency>
			<groupId>ru.yandex.clickhouse</groupId>
			<artifactId>clickhouse-jdbc</artifactId>
			<version>0.3.1-patch</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.apache.httpcomponents/httpcore -->
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpcore</artifactId>
			<version>4.4.13</version>
		</dependency>

		<dependency>
			<groupId>org.restlet.jse</groupId>
			<artifactId>org.restlet</artifactId>
			<version>2.4.3</version>
		</dependency>

		<dependency>
			<groupId>org.restlet.jse</groupId>
			<artifactId>org.restlet.ext.simple</artifactId>
			<version>2.4.3</version>
		</dependency>

		<dependency>
			<groupId>org.restlet.jee</groupId>
			<artifactId>org.restlet.ext.xml</artifactId>
			<version>2.4.3</version>
		</dependency>

		<dependency>
			<groupId>org.restlet.jee</groupId>
			<artifactId>org.restlet.ext.jackson</artifactId>
			<version>2.4.3</version>
		</dependency>

		<dependency>
			<groupId>org.restlet.jee</groupId>
			<artifactId>org.restlet.ext.gson</artifactId>
			<version>2.4.3</version>
		</dependency>

		<dependency>
    		<groupId>it.unimi.dsi</groupId>
    		<artifactId>fastutil</artifactId>
    		<version>8.4.3</version>
		</dependency>

		<dependency>
    		<groupId>com.google.guava</groupId>
    		<artifactId>guava</artifactId>
    		<version>30.1.1-jre</version>
		</dependency>

		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
			<version>4.5.13</version>
		</dependency>

		<dependency>
			<groupId>commons-lang</groupId>
			<artifactId>commons-lang</artifactId>
			<version>2.6</version>
		</dependency>

		<dependency>
    		<groupId>org.jsoup</groupId>
    		<artifactId>jsoup</artifactId>
    		<version>1.13.1</version>
		</dependency>

		<dependency>
    		<groupId>com.github.jbytecode</groupId>
    		<artifactId>RCaller</artifactId>
    		<version>3.0.2</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.jasypt/jasypt -->
		<dependency>
		    <groupId>org.jasypt</groupId>
    		<artifactId>jasypt</artifactId>
    		<version>1.9.2</version>
		</dependency>

		<dependency>
			<groupId>ch.ethz.ganymed</groupId>
			<artifactId>ganymed-ssh2</artifactId>
			<version>build210</version>
		</dependency>

		<dependency>
    		<groupId>org.apache.commons</groupId>
    		<artifactId>commons-csv</artifactId>
    		<version>1.9.0</version>
		</dependency>

		<!-- log4j2	-->
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
			<version>2.13.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
			<version>2.13.0</version>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-log4j12</artifactId>
			<version>1.7.30</version>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.18.34</version>
		</dependency>

	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<version>2.4.1</version>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<finalName>${project.artifactId}-${project.version}</finalName>
					<filters>
						<filter>
							<artifact>*:*</artifact>
							<excludes>
								<exclude>META-INF/*.SF</exclude>
								<exclude>META-INF/*.DSA</exclude>
								<exclude>META-INF/*.RSA</exclude>
							</excludes>
						</filter>
					</filters>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>exec-maven-plugin</artifactId>
				<version>1.5.0</version>
				<executions>

					<!-- extract shared counts cross reference data in the 'url_to_domains_xref' table in the clickhouse database -->
					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>sharedCountsCrossReferenceExtract</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DsharedCountsCrossReferenceExtract</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx16000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.SharedCountsCrossReferenceExtract</argument>

								<!-- runtime parameter 1 (trace date): 2017-06-19 -->
								<argument>2017-07-09</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<!-- extract shared counts detail data in the 'detail' table in the clickhouse database -->
					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>sharedCountsExtract</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DsharedCountsExtract</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx16000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.SharedCountsExtract</argument>

								<!-- runtime parameter 1 (trace date): 2017-06-19 -->
								<argument>2017-11-19</argument>

								<!-- runtime parameter 2 (database query limit): 1234 -->
								<!-- <argument>888</argument> -->

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<!-- On Sundays, send competitor URLs to weekly HTML polite crawl queues for SOLR -->
					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>sendAssociatedUrlsToCrawlQueuesByDomainHtmlWeekly</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DsendAssociatedUrlsToCrawlQueuesByDomainHtmlWeekly</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx56000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.put.messages.SendAssociatedUrlsToCrawlQueuesByDomain</argument>

								<!-- runtime parameter 1: process type (solr/clickhouse) (required) -->
								<argument>solr</argument>

								<!-- runtime parameter 2: number of queues (required) -->
								<argument>999</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<!-- update shared counts detail data in the 'detail' table in the clickhouse database -->
					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>sharedCountsUpdate</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DsharedCountsUpdate</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx16000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.SharedCountsUpdate</argument>

								<!-- runtime parameter 1 (trace date): 2017-06-19 -->
								<argument>2017-07-02</argument>

								<!-- runtime parameter 2 (database query limit): 1234 -->
								<argument>888888</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<!-- On Sundays, send competitor URLs to weekly HTML polite crawl queues for ClickHouse -->
					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>putStep1CompetitorUrlsToHtmlQueuesWeekly</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DputStep1CompetitorUrlsToHtmlQueuesWeekly</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx38000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.put.messages.SendAssociatedUrlsToCrawlQueuesByDomain</argument>

								<!-- runtime parameter 1: process type (solr/clickhouse) (required) -->
								<argument>clickhouse</argument>

								<!-- runtime parameter 2: number of queues (required) -->
								<argument>3168</argument>

								<!-- runtime parameter 3: step number (required) -->
								<argument>1</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<!-- On Sundays, send competitor URLs to weekly HTML polite crawl queues for ClickHouse -->
					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>putStep2CompetitorUrlsToHtmlQueuesWeekly</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DputStep2CompetitorUrlsToHtmlQueuesWeekly</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx68000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.put.messages.SendAssociatedUrlsToCrawlQueuesByDomain</argument>

								<!-- runtime parameter 1: process type (solr/clickhouse) (required) -->
								<argument>clickhouse</argument>

								<!-- runtime parameter 2: number of queues (required) -->
								<argument>3168</argument>

								<!-- runtime parameter 3: step number (required) -->
								<argument>2</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<!-- On Sundays, send competitor URLs to weekly HTML polite crawl queues for ClickHouse -->
					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>putStep3CompetitorUrlsToHtmlQueuesWeekly</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DputStep3CompetitorUrlsToHtmlQueuesWeekly</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx68000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.put.messages.SendAssociatedUrlsToCrawlQueuesByDomain</argument>

								<!-- runtime parameter 1: process type (solr/clickhouse) (required) -->
								<argument>clickhouse</argument>

								<!-- runtime parameter 2: number of queues (required) -->
								<argument>3168</argument>

								<!-- runtime parameter 3: step number (required) -->
								<argument>3</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>putTargetUrlsToHtmlQueuesDaily</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DputTargetUrlsToHtmlQueuesDaily</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx88000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.put.messages.SendUrlsToDailyCrawlQueuesByDomain</argument>

								<!-- runtime parameter 1: process type (solr/clickhouse/test/control) (required) -->
								<argument>clickhouse</argument>

								<!-- runtime parameter 2: is process domains with 10,000 or less target URLs (required) -->
								<argument>true</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>putLargeTargetUrlsToHtmlQueuesDaily</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DputLargeTargetUrlsToHtmlQueuesDaily</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx88000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.put.messages.SendUrlsToDailyCrawlQueuesByDomain</argument>

								<!-- runtime parameter 1: process type (solr/clickhouse/test/control) (required) -->
								<argument>clickhouse</argument>

								<!-- runtime parameter 2: is process domains with 10,000 or less target URLs (required) -->
								<argument>false</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>putTargetUrlsToHtmlQueuesDaily_test</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DputTargetUrlsToHtmlQueuesDaily_test</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx36000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.put.messages.SendUrlsToDailyCrawlQueuesByDomain</argument>

								<!-- runtime parameter 1: process type (solr/clickhouse/test/control) (required) -->
								<!-- <argument>test</argument> -->
								<argument>test</argument>

								<!-- runtime parameter 2: is process domains with 10,000 or less target URLs (required) -->
								<argument>true</argument>

								<!-- runtime parameter 3: domain IDs (optional) -->
								<!-- <argument>263,2432</argument> -->
								<argument>9632</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>updateTargetUrlCrawlControllerQueue</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DupdateTargetUrlCrawlControllerQueue</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx6000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.put.messages.SendUrlsToDailyCrawlQueuesByDomain</argument>

								<!-- runtime parameter 1: process type (solr/clickhouse/test/control) (required) -->
								<argument>control</argument>

								<!-- runtime parameter 2: -->
								<argument>-</argument>

								<!-- runtime parameter 3: -->
								<argument>-</argument>

								<!-- runtime parameter 4: process Skyscanner domains (true/false) -->
								<argument>false</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>updateCompetitorUrlCrawlControllerQueueClickHouse</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DupdateCompetitorUrlCrawlControllerQueueClickHouse</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx18000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.PutMesssagesToQueue</argument>

								<!-- runtime parameter 1: message data file -->
								<argument>/home/<USER>/source/polite_crawl_put_messages/data/competitor_html_queue_names_messages.txt</argument>

								<!-- runtime parameter 2: queue name -->
								<argument>COMPETITOR_URL_HTML_QUEUE_NAMES</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>updateCompetitorUrlCrawlControllerQueueSolr</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DupdateCompetitorUrlCrawlControllerQueueSolr</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx18000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.PutMesssagesToQueue</argument>

								<!-- runtime parameter 1: message data file -->
								<argument>/home/<USER>/source/polite_crawl_put_messages/data/associated_html_queue_names_messages.txt</argument>

								<!-- runtime parameter 2: queue name -->
								<argument>CRAWL_ASSOCIATED_URLS_HTML_QUEUE_NAMES</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>findTargetUrlsDifferByTrailingSlash</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DfindTargetUrlsDifferByTrailingSlash</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx18000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.put.messages.FindTargetUrlsDifferByTrailingSlash</argument>

								<!-- runtime parameter 1: domain IDs -->
								<!-- <argument>4726</argument> -->

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>updateTargetUrlHtmlDaily</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DupdateTargetUrlHtmlDaily</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx12000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.polite.crawl.UpdateTargetUrlHtmlDaily</argument>

								<!-- runtime parameter 1 (optional): domain IDs -->
								<!-- <argument>256</argument> -->

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>putStopTargetUrlHtmlCrawlMessage</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DputStopTargetUrlHtmlCrawlMessage</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.PutMesssagesToQueue</argument>

								<!-- runtime parameter 1: message data file -->
								<argument>/home/<USER>/source/polite_crawl_put_messages/data/stop_crawl_message.txt</argument>

								<!-- runtime parameter 2: queue name -->
								<argument>TARGET_URL_HTML_STOP_CRAWL_QUEUE</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>putStartTargetUrlHtmlCrawlMessage</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DputStartTargetUrlHtmlCrawlMessage</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.PutMesssagesToQueue</argument>

								<!-- runtime parameter 1: message data file -->
								<argument>/home/<USER>/source/polite_crawl_put_messages/data/start_crawl_message.txt</argument>

								<!-- runtime parameter 2: queue name -->
								<argument>TARGET_URL_HTML_STOP_CRAWL_QUEUE</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>putStopCompetitorUrlHtmlCrawlMessage</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DputStopCompetitorUrlHtmlCrawlMessage</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.PutMesssagesToQueue</argument>

								<!-- runtime parameter 1: message data file -->
								<argument>/home/<USER>/source/polite_crawl_put_messages/data/stop_crawl_message.txt</argument>

								<!-- runtime parameter 2: queue name -->
								<argument>COMPETITOR_URL_HTML_STOP_CRAWL_QUEUE</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>putStartCompetitorUrlHtmlCrawlMessage</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DputStartCompetitorUrlHtmlCrawlMessage</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.PutMesssagesToQueue</argument>

								<!-- runtime parameter 1: message data file -->
								<argument>/home/<USER>/source/polite_crawl_put_messages/data/start_crawl_message.txt</argument>

								<!-- runtime parameter 2: queue name -->
								<argument>COMPETITOR_URL_HTML_STOP_CRAWL_QUEUE</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>putMessagesToTestSharedCountsControllerQueue</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DputMessagesToTestSharedCountsControllerQueue</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx18000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.PutMesssagesToQueue</argument>

								<!-- runtime parameter 1: message data file -->
								<argument>/home/<USER>/source/polite_crawl_put_messages/data/test_shared_counts_queue_names_messages.txt</argument>

								<!-- runtime parameter 2: queue name -->
								<argument>TEST_SHARED_COUNTS_QUEUE_NAMES</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>putMessagesToTestSharedCounts1Queue</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DputMessagesToTestSharedCounts1Queue</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx18000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.PutMesssagesToQueue</argument>

								<!-- runtime parameter 1: message data file -->
								<argument>/home/<USER>/source/polite_crawl_put_messages/data/test_shared_counts_1_messages.txt</argument>

								<!-- runtime parameter 2: queue name -->
								<argument>TEST_SHARED_COUNTS_1</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>putMessagesToTargetUrlSharedCountsControllerQueue</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DputMessagesToTargetUrlSharedCountsControllerQueue</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx18000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.PutMesssagesToQueue</argument>

								<!-- runtime parameter 1: message data file -->
								<argument>/home/<USER>/source/polite_crawl_put_messages/data/target_url_shared_counts_queue_names_messages.txt</argument>

								<!-- runtime parameter 2: queue name -->
								<argument>TARGET_URL_SHARED_COUNTS_QUEUE_NAMES</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>putMesssagesToQueue</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DputMesssagesToQueue</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.PutMesssagesToQueue</argument>

								<!-- runtime parameter 1: message data file -->
								<argument>/home/<USER>/source/polite_crawl_put_messages/data/ad_hoc_domain_backlinks_historic_messages.txt</argument>

								<!-- runtime parameter 2: queue name -->
								<argument>AD_HOC_BACKLINK_DATA_RETRIEVAL</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>putDomain4765TargetUrlsToHtmlQueuesDaily</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DputDomain4765TargetUrlsToHtmlQueuesDaily</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.put.messages.SendUrlsToDailyCrawlQueuesByDomain</argument>

								<!-- runtime parameter 1: process type (solr/clickhouse/4765/control) (required) -->
								<argument>clickhouse</argument>

								<!-- runtime parameter 2: is process domains with 10,000 or less target URLs (required) -->
								<argument>false</argument>

								<!-- runtime parameter 3: domain IDs (optional) -->
								<argument>4765</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>updateTargetUrlHtmlDaily_test</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DupdateTargetUrlHtmlDaily_test</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx12000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.polite.crawl.UpdateTargetUrlHtmlDaily</argument>

								<!-- runtime parameter 1 (optional): domain IDs -->
								<argument>256</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>wrike_ticket_472033093</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-Dwrike_ticket_472033093</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx18000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.PutMesssagesToQueue</argument>

								<!-- runtime parameter 1: message data file -->
								<argument>/home/<USER>/source/polite_crawl_put_messages/data/expedia-france-url-crawl.txt</argument>

								<!-- runtime parameter 2: queue name -->
								<argument>EXTRACT_RAW_HTML_1</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>updateExtractRawHtmlQueueNames</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DupdateTestTargetUrlHtmlQueueNames</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx18000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.PutMesssagesToQueue</argument>

								<!-- runtime parameter 1: message data file -->
								<argument>/home/<USER>/source/polite_crawl_put_messages/data/updateExtractRawHtmlQueueNames.messages.txt</argument>

								<!-- runtime parameter 2: queue name -->
								<argument>EXTRACT_RAW_HTML_QUEUE_NAMES</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<!-- client domain robots.txt daily crawl -->
					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>robotsTxtCrawl</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DrobotsTxtCrawl</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.robots.txt.RobotsTxtCrawl</argument>

								<!-- runtime parameter 1 (optional): number of threads -->
								<argument>38</argument>

								<!-- runtime parameter 2 (optional): domain ID list -->
								<!-- <argument>256</argument> -->

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<!-- update target URL metrics for client domains with more than 10,000 target URLs -->
					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>targetUrlMetricsUpdate</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DtargetUrlMetricsUpdate</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx58000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.url.metrics.update.TargetUrlMetricsUpdate</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<!-- update target URL metrics for client domains with less than 10,000 target URLs -->
					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>urlMetricsStatusCodePoliteCrawlUpdate</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DurlMetricsStatusCodePoliteCrawlUpdate</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx58000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.url.metrics.update.UrlMetricsStatusCodePoliteCrawlUpdate</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>putMessagesToTestTargetUrlHtmlEn1701</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DputMessagesToTestTargetUrlHtmlEn1701</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx18000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.PutMesssagesToQueue</argument>

								<!-- runtime parameter 1: message data file -->
								<argument>/home/<USER>/source/polite_crawl_put_messages/data/test_target_url_html_en_1701_messages.txt</argument>

								<!-- runtime parameter 2: queue name -->
								<argument>TEST_TARGET_URL_HTML_EN_1701</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>targetUrlHtmlOptimization</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DtargetUrlHtmlOptimization</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TargetUrlHtmlOptimization</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<!-- to start Polite Crawl web service -->
					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>politeCrawlWebService</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DpoliteCrawlWebService</argument>

								<!-- a maximum physical memory -->
								<argument>-Xmx88000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.web.service.PoliteCrawlWebService</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<!-- polite crawl web service test -->
					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>politeCrawlWebServiceTest</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DpoliteCrawlWebServiceTest</argument>

								<!-- a maximum 8GB physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.test.PoliteCrawlWebServiceTest</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<!-- main web service test -->
					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>mainWebServiceTest</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DmainWebServiceTest</argument>

								<!-- a maximum 8GB physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.test.MainWebServiceTest</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<!-- for competitor URL HTML crawl v2.0 -->
					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>competitorUrlHtmlWeeklyCrawl</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx58000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DcompetitorUrlHtmlWeeklyCrawl</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.polite.crawl.PoliteCrawl</argument>

								<!-- runtime parameter 1 (optional): number of concurrent threads -->
								<argument>38</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<!-- for target URL HTML crawl v2.0 -->
					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>targetUrlHtmlDailyCrawl</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx88000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtargetUrlHtmlDailyCrawl</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.polite.crawl.PoliteCrawl</argument>

								<!-- runtime parameter 1 (optional): number of concurrent threads -->
								<argument>168</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<!-- extract one 'competitor_url_html' record -->
					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>competitorUrlHtmlExtract</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=UTC</argument>

								<!-- specify the process identifier -->
								<argument>-DcompetitorUrlHtmlExtract</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.CompetitorUrlHtmlExtract</argument>

								<!-- runtime parameter 1 (required): output file location path -->
								<!-- <argument>/home/<USER>/source/test_polite_crawl_maven/output/domain_1701.txt</argument> -->
								<argument>/home/<USER>/source/polite_crawl_put_messages/output/competitor_url_html.txt</argument>

								<!-- runtime parameter 3 (required): URL string -->
								<argument>https://www.apartments.com/dripping-springs-tx/</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<!-- re-index the shared counts ClickHouse table from one database server to another database server -->
					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>sharedCountsReindex</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx38000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DsharedCountsReindex</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.SharedCountsReindex</argument>

								<!-- runtime parameter 1 (required): source database connection URL -->
								<argument>10.5.32.10</argument>

								<!-- runtime parameter 2 (required): destination database connection URL -->
								<argument>10.5.32.134</argument>

								<!-- runtime parameter 3 (required): start track date (YYYY-MM-DD) -->
								<argument>2018-01-07</argument>

								<!-- runtime parameter 3 (required): end track date (YYYY-MM-DD) -->
								<argument>2019-12-01</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<!-- extract one 'target_url_html' record -->
					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>targetUrlHtmlExtract</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<argument>-Duser.timezone=America/Chicago</argument>
								<!-- <argument>-Duser.timezone=UTC</argument> -->								

								<!-- specify the process identifier -->
								<argument>-DtargetUrlHtmlExtract</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TargetUrlHtmlExtract</argument>

								<!-- runtime parameter 1 (required): output file location path -->
								<argument>/home/<USER>/source/test_polite_crawl_put_messages/output/domain_9688_custom_data_extract_20210510.txt</argument>

								<!-- runtime parameter 2 (required): domain ID -->
								<argument>9688</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<!-- store updated URLs on FTP server to database -->
					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>storeUploadedUrlsInDatabase</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DstoreUploadedUrlsInDatabase</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.extract.raw.html.StoreUploadedUrlsInDatabase</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<!-- page tag content change data maintenance -->
					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>pageTagContentChangeDataMaintenance</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DpageTagContentChangeDataMaintenance</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.page.content.change.PageTagContentChangeDataMaintenance</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<!-- page content change update -->
					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>pageContentChangeUpdate</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DpageContentChangeUpdate</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.page.content.change.PageContentChangeUpdate</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<!-- page tag content change update -->
					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>pageTagContentChangeUpdate</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DpageTagContentChangeUpdate</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.page.content.change.PageTagContentChangeUpdate</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<!-- for target URL HTML crawl v2.0 -->
					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>targetUrlHtmlDailyCrawlTest</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx36000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtargetUrlHtmlDailyCrawlTest</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.polite.crawl.PoliteCrawl</argument>

								<!-- runtime parameter 1 (optional): number of concurrent threads -->
								<argument>8</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<!-- target URL HTML data removal -->
					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>targetUrlHtmlDataRemoval</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx88000m</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtargetUrlHtmlDataRemoval</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TargetUrlHtmlDataRemoval</argument>

								<!-- runtime parameter 1 (optional): input file location path -->
								<argument>/home/<USER>/source/test_polite_crawl_put_messages/data/target_url_html_data_removal_20201215b.txt</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>putContentGuardUrlsToHtmlDailyQueues</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DputContentGuardUrlsToHtmlDailyQueues</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.put.messages.SendContentGuardCrawlQueuesByDomain</argument>

								<!-- runtime parameter 1 (required): process type (clickhouse/test/control) -->
								<argument>clickhouse</argument>

								<!-- runtime parameter 2 (required): crawl frequency type (1=daily,2=hourly) -->
								<argument>1</argument>

								<!-- runtime parameter 3 (optional): list of domain IDs (xxxx,xxxx,) -->
								<!-- <argument>1701</argument> -->

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>putContentGuardUrlsToHtmlHourlyQueues</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DputContentGuardUrlsToHtmlHourlyQueues</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.put.messages.SendContentGuardCrawlQueuesByDomain</argument>

								<!-- runtime parameter 1 (required): process type (clickhouse/test/control) -->
								<argument>clickhouse</argument>

								<!-- runtime parameter 2 (required): crawl frequency type (1=daily,2=hourly) -->
								<argument>2</argument>

								<!-- runtime parameter 3 (optional): list of domain IDs (xxxx,xxxx,) -->
								<!-- <argument>1701</argument> -->

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>putStopContentGuardDailyCrawlMessage</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DputStopContentGuardDailyCrawlMessage</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.PutMesssagesToQueue</argument>

								<!-- runtime parameter 1: message data file -->
								<argument>/home/<USER>/source/put_messages_content_guard_daily/data/stop_crawl_message.txt</argument>

								<!-- runtime parameter 2: queue name -->
								<argument>CONTENT_GUARD_DAILY_STOP_CRAWL_QUEUE</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>putStopContentGuardHourlyCrawlMessage</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DputStopContentGuardHourlyCrawlMessage</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.PutMesssagesToQueue</argument>

								<!-- runtime parameter 1: message data file -->
								<argument>/home/<USER>/source/put_messages_content_guard_hourly/data/stop_crawl_message.txt</argument>

								<!-- runtime parameter 2: queue name -->
								<argument>CONTENT_GUARD_HOURLY_STOP_CRAWL_QUEUE</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>putStartContentGuardDailyCrawlMessage</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DputStartContentGuardDailyCrawlMessage</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.PutMesssagesToQueue</argument>

								<!-- runtime parameter 1: message data file -->
								<argument>/home/<USER>/source/put_messages_content_guard_daily/data/start_crawl_message.txt</argument>

								<!-- runtime parameter 2: queue name -->
								<argument>CONTENT_GUARD_DAILY_STOP_CRAWL_QUEUE</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>putStartContentGuardHourlyCrawlMessage</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DputStartContentGuardHourlyCrawlMessage</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.PutMesssagesToQueue</argument>

								<!-- runtime parameter 1: message data file -->
								<argument>/home/<USER>/source/put_messages_content_guard_hourly/data/start_crawl_message.txt</argument>

								<!-- runtime parameter 2: queue name -->
								<argument>CONTENT_GUARD_HOURLY_STOP_CRAWL_QUEUE</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<!-- for content guard daily crawl -->
					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>contentGuardDailyCrawl</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>
<!--								<argument>-Dexec.cleanupDaemonThreads=false</argument>-->

								<!-- maximum physical memory -->
								<argument>-Xmx58000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DcontentGuardDailyCrawl</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.polite.crawl.PoliteCrawl</argument>

								<!-- runtime parameter 1 (optional): number of concurrent threads -->								
								<argument>2</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<!-- for content guard hourly crawl -->
					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>contentGuardHourlyCrawl</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx58000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DcontentGuardHourlyCrawl</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.polite.crawl.PoliteCrawl</argument>

								<!-- runtime parameter 1 (optional): number of concurrent threads -->
								<argument>368</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>updateContentGuardDailyCrawlControllerQueue</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DupdateContentGuardDailyCrawlControllerQueue</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.put.messages.SendContentGuardCrawlQueuesByDomain</argument>

								<!-- runtime parameter 1: process type (solr/clickhouse/test/control) (required) -->
								<argument>control</argument>

								<!-- runtime parameter 2 (required): crawl frequency type (1=daily,2=hourly) -->
								<argument>1</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>updateContentGuardHourlyCrawlControllerQueue</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DupdateContentGuardHourlyCrawlControllerQueue</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.put.messages.SendContentGuardCrawlQueuesByDomain</argument>

								<!-- runtime parameter 1: process type (solr/clickhouse/test/control) (required) -->
								<argument>control</argument>

								<!-- runtime parameter 2 (required): crawl frequency type (1=daily,2=hourly) -->
								<argument>2</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>solrQueryTest</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DsolrQueryTest</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.test.SolrQueryTest</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>putContentGuardUrlsToHtmlUploadedQueues</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DputContentGuardUrlsToHtmlUploadedQueues</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.put.messages.SendContentGuardCrawlQueuesByDomain</argument>

								<!-- runtime parameter 1 (required): process type (clickhouse/test/control) -->
								<argument>clickhouse</argument>

								<!-- runtime parameter 2 (required): crawl frequency type (1=uploaded,2=hourly,3=uploaded) -->
								<argument>3</argument>

								<!-- runtime parameter 3 (optional): list of domain IDs (xxxx,xxxx,) -->
								<!-- <argument>1701</argument> -->

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>putStopContentGuardUploadedCrawlMessage</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DputStopContentGuardUploadedCrawlMessage</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.PutMesssagesToQueue</argument>

								<!-- runtime parameter 1: message data file -->
								<argument>/home/<USER>/source/put_messages_content_guard_uploaded/data/stop_crawl_message.txt</argument>

								<!-- runtime parameter 2: queue name -->
								<argument>CONTENT_GUARD_UPLOADED_STOP_CRAWL_QUEUE</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>putStartContentGuardUploadedCrawlMessage</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DputStartContentGuardUploadedCrawlMessage</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.PutMesssagesToQueue</argument>

								<!-- runtime parameter 1: message data file -->
								<argument>/home/<USER>/source/put_messages_content_guard_uploaded/data/start_crawl_message.txt</argument>

								<!-- runtime parameter 2: queue name -->
								<argument>CONTENT_GUARD_UPLOADED_STOP_CRAWL_QUEUE</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<!-- for content guard uploaded crawl -->
					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>contentGuardUploadedCrawl</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DcontentGuardUploadedCrawl</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.polite.crawl.PoliteCrawl</argument>

								<!-- runtime parameter 1 (optional): number of concurrent threads -->								
								<argument>50</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>associateTopRankedTargetUrls</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DassociateTopRankedTargetUrls</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.auto.association.AssociateTopRankedTargetUrls</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>queueDepthReport</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DqueueDepthReport</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.QueueDepthReport</argument>

								<!-- runtime parameter 1 (required): output file location path -->
								<argument>/home/<USER>/source/polite_crawl_put_messages/output/queueDepthReport.txt</argument>

								<!-- runtime parameter 2 (required): queue prefixes -->
								<argument>TARGET_URL_HTML_</argument>
								<!-- <argument>COMPETITOR_URL_HTML_</argument> -->
								<!-- <argument>AD_HOC_BACKLINK_DATA_RETRIEVAL,AD_HOC_INDEX_ITEM_INFO_RETRIEVAL,CLIENT_DOMAIN_BACKLINK_DATA_RETRIEVAL,COMPETITOR_BACKLINK_DATA_RETRIEVAL,CONTENT_GUARD_DAILY_QUEUE_NAMES,CONTENT_GUARD_HOURLY_QUEUE_NAMES,CONTENT_GUARD_UPLOADED_QUEUE_NAMES,COMPETITOR_URL_HTML_QUEUE_NAMES,LINK_CLARITY_QUEUE_NAMES,TARGET_URL_HTML_QUEUE_NAMES,</argument> -->																

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>targetUrlQueueDepthReport</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtargetUrlQueueDepthReport</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.QueueDepthReport</argument>

								<!-- runtime parameter 1 (required): output file location path -->
								<argument>/home/<USER>/source/polite_crawl_put_messages/output/targetUrlQueueDepthReport.txt</argument>

								<!-- runtime parameter 2 (required): queue prefixes -->
								<argument>TARGET_URL_HTML_</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>competitorUrlQueueDepthReport</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DcompetitorUrlQueueDepthReport</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.QueueDepthReport</argument>

								<!-- runtime parameter 1 (required): output file location path -->
								<argument>/home/<USER>/source/polite_crawl_put_messages/output/competitorUrlQueueDepthReport.txt</argument>

								<!-- runtime parameter 2 (required): queue prefixes -->						
								<argument>COMPETITOR_URL_HTML_</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>contentGuardDailyQueueDepthReport</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DcontentGuardDailyQueueDepthReport</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.QueueDepthReport</argument>

								<!-- runtime parameter 1 (required): output file location path -->
								<argument>/home/<USER>/source/polite_crawl_put_messages/output/contentGuardDailyQueueDepthReport.txt</argument>

								<!-- runtime parameter 2 (required): queue prefixes -->
								<argument>CONTENT_GUARD_DAILY_</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>contentGuardHourlyQueueDepthReport</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DcontentGuardHourlyQueueDepthReport</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.QueueDepthReport</argument>

								<!-- runtime parameter 1 (required): output file location path -->
								<argument>/home/<USER>/source/polite_crawl_put_messages/output/contentGuardHourlyQueueDepthReport.txt</argument>

								<!-- runtime parameter 2 (required): queue prefixes -->
								<argument>CONTENT_GUARD_HOURLY_</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>contentGuardUploadedQueueDepthReport</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DcontentGuardUploadedQueueDepthReport</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.QueueDepthReport</argument>

								<!-- runtime parameter 1 (required): output file location path -->
								<argument>/home/<USER>/source/polite_crawl_put_messages/output/contentGuardUploadedQueueDepthReport.txt</argument>

								<!-- runtime parameter 2 (required): queue prefixes -->
								<argument>CONTENT_GUARD_UPLOADED_</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>testTargetUrlQueueDepthReport</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtestTargetUrlQueueDepthReport</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.QueueDepthReport</argument>

								<!-- runtime parameter 1 (required): output file location path -->
								<argument>/home/<USER>/source/polite_crawl_put_messages/output/testTargetUrlQueueDepthReport.txt</argument>

								<!-- runtime parameter 2 (required): queue prefixes -->
								<argument>TEST_TARGET_</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>pageContentDetection</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DpageContentDetection</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.polite.crawl.PageContentDetection</argument>

								<!-- runtime parameter 1 (required): input file location path -->
								<!-- <argument>/home/<USER>/source/test_polite_crawl_put_messages/data/page_content_detection_input.txt</argument> -->
								<argument>/home/<USER>/source/test_polite_crawl_put_messages/data/Expedia_PDP_content_re-crawl.txt</argument>								

								<!-- runtime parameter 2 (required): output file location path -->
								<!-- <argument>/home/<USER>/source/test_polite_crawl_put_messages/output/page_content_detection_output.txt</argument> -->								
								<argument>/home/<USER>/source/test_polite_crawl_put_messages/output/Expedia_PDP_content_re-crawl_output.txt</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>workersScriptVerificationTest</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DworkersScriptVerificationTest</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.test.WorkersScriptVerificationTest</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>contentGuardServiceTest</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DcontentGuardServiceTest</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.test.ContentGuardServiceTest</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>contentGuardResourceTest</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DcontentGuardResourceTest</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.test.ContentGuardResourceTest</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>mainContentGuardResourceTest</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DmainContentGuardResourceTest</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.test.MainContentGuardResourceTest</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>findTargetUrlsDifferencesTest</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DfindTargetUrlsDifferencesTest</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.test.FindTargetUrlsDifferencesTest</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>mainFindTargetUrlsDifferencesTest</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DmainFindTargetUrlsDifferencesTest</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.test.MainFindTargetUrlsDifferencesTest</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>contentGuardAlert_daily</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DcontentGuardAlert_daily</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.content.guard.alert.ContentGuardAlert</argument>

								<!-- runtime parameter 1 (required): crawl frequency type 1=daily, 2=hourly -->
								<argument>1</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>contentGuardAlert_hourly</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DcontentGuardAlert_hourly</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.content.guard.alert.ContentGuardAlert</argument>

								<!-- runtime parameter 1 (required): crawl frequency type 1=daily, 2=hourly -->
								<argument>2</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>putNewTargetUrlToQueue</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DputNewTargetUrlToQueue</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.polite.crawl.PutNewTargetUrlToQueue</argument>

								<!-- runtime parameter 1 (optional): domain IDs -->
								<!-- <argument>256</argument> -->

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<!-- put domain 475 group tag 1412118 to TEST_TARGET_URL_HTML_EN_475 queue daily -->
					<execution>
						<id>putTargetUrlsToHtmlQueuesDaily_475_1412118</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DputTargetUrlsToHtmlQueuesDaily_475_1412118</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.put.messages.SendUrlsToDailyCrawlQueuesByDomain</argument>

								<!-- runtime parameter 1: process type (solr/clickhouse/test/control) (required) -->
								<!-- <argument>test</argument> -->
								<argument>test</argument>

								<!-- runtime parameter 2: is process domains with 10,000 or less target URLs (required) -->
								<argument>false</argument>

								<!-- runtime parameter 3: domain IDs (optional) -->
								<argument>475</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<!-- crawl domain 475 group tag 1412118 in TEST_TARGET_URL_HTML_EN_475 daily -->
					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>domain_4765_daily_target_url_crawl</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-Ddomain_4765_daily_target_url_crawl</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.polite.crawl.PoliteCrawl</argument>

								<!-- runtime parameter 1 (optional): number of concurrent threads -->
								<argument>1</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<!-- for new URL HTML crawl -->
					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>newUrlHtmlDailyCrawl</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DnewUrlHtmlDailyCrawl</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.polite.crawl.PoliteCrawl</argument>

								<!-- runtime parameter 1 (optional): number of concurrent threads -->
								<argument>8</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>putStopNewUrlHtmlCrawlMessage</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DputStopNewUrlHtmlCrawlMessage</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.PutMesssagesToQueue</argument>

								<!-- runtime parameter 1: message data file -->
								<argument>/home/<USER>/source/polite_crawl_put_messages/data/stop_crawl_message.txt</argument>

								<!-- runtime parameter 2: queue name -->
								<argument>NEW_URL_HTML_STOP_CRAWL_QUEUE</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>putStartNewUrlHtmlCrawlMessage</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DputStartNewUrlHtmlCrawlMessage</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.PutMesssagesToQueue</argument>

								<!-- runtime parameter 1: message data file -->
								<argument>/home/<USER>/source/polite_crawl_put_messages/data/start_crawl_message.txt</argument>

								<!-- runtime parameter 2: queue name -->
								<argument>NEW_URL_HTML_STOP_CRAWL_QUEUE</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>associateTopRankedCompetitorUrls</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DassociateTopRankedCompetitorUrls</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx28000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.auto.association.AssociateTopRankedCompetitorUrls</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>disassociateTopRankedCompetitorUrls</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DdisassociateTopRankedCompetitorUrls</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx28000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.auto.association.DisassociateTopRankedCompetitorUrls</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>contentGuardAlert_test</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DcontentGuardAlert_test</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.content.guard.alert.ContentGuardAlert</argument>

								<!-- runtime parameter 1 (required): crawl frequency type 1=daily, 2=hourly -->
								<!-- <argument>1</argument> -->
								<argument>1</argument>

								<!-- runtime parameter 2 (optional): is debug mode? true/false -->	
								<argument>true</argument>

								<!-- runtime parameter 3 (optional): domain IDs -->
								<!-- <argument>9676</argument> -->
								<!-- <argument>9676</argument> -->
								<!-- <argument>9676</argument> -->
								<!-- <argument>7048</argument> -->
								<!-- <argument>256</argument> -->
								<argument>9511</argument>

								<!-- runtime parameter 4 (optional): crawl date override YYYY-MM-DD -->
								<!-- <argument>2021-04-08</argument> -->
								<!-- <argument>2021-02-10</argument> -->
								<!-- <argument>2021-04-09</argument> -->
								<!-- <argument>2021-04-07</argument> -->
								<!-- <argument>2021-04-09</argument> -->								
								<argument>2021-05-29</argument>

								<!-- runtime parameter 5 (optional): crawl hour override 00 - 23 -->
								<!-- <argument>15</argument> -->

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>mainZapierContentGuardAlertResourceTest</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DmainZapierContentGuardAlertResourceTest</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.test.MainZapierContentGuardAlertResourceTest</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>crawlerTest</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DcrawlerTest</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.test.CrawlerTest</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>mainZapierCrawlInitiatedAlertResourceTest</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DmainZapierCrawlInitiatedAlertResourceTest</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.test.MainZapierCrawlInitiatedAlertResourceTest</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>mainZapierCrawlCompletedAlertResourceTest</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DmainZapierCrawlCompletedAlertResourceTest</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.test.MainZapierCrawlCompletedAlertResourceTest</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>mainZapierPageTagContentAlertResourceTest</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DmainZapierPageTagContentAlertResourceTest</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.test.MainZapierPageTagContentAlertResourceTest</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>mainZapierResourceTest</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DmainZapierResourceTest</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.test.MainZapierResourceTest</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>mainGoogleTagResourceTest</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DmainGoogleTagResourceTest</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.test.MainGoogleTagResourceTest</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>evaluateInBrowserContext</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DevaluateInBrowserContext</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.playwright.EvaluateInBrowserContext</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>playwrightCrawl</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DplaywrightCrawl</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.playwright.PlaywrightCrawl</argument>

								<!-- runtime parameter 1 (required): number of concurrent threads -->
								<argument>168</argument>

								<!-- runtime parameter 2 (required): domain IDs -->
								<argument>9666,8614,2702,7691,7487,1335,9688,5813,1352,1909</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>playwrightCrawl_1335</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DplaywrightCrawl_1335</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.playwright.PlaywrightCrawl</argument>

								<!-- runtime parameter 1 (required): number of concurrent threads -->
								<argument>1</argument>

								<!-- runtime parameter 2 (required): domain IDs -->
								<argument>1335</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>playwrightCrawl_1352</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DplaywrightCrawl_1352</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.playwright.PlaywrightCrawl</argument>

								<!-- runtime parameter 1 (required): number of concurrent threads -->
								<argument>1</argument>

								<!-- runtime parameter 2 (required): domain IDs -->
								<argument>1352</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>playwrightCrawl_1909</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DplaywrightCrawl_1909</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.playwright.PlaywrightCrawl</argument>

								<!-- runtime parameter 1 (required): number of concurrent threads -->
								<argument>1</argument>

								<!-- runtime parameter 2 (required): domain IDs -->
								<argument>1909</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>playwrightCrawl_2702</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DplaywrightCrawl_2702</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.playwright.PlaywrightCrawl</argument>

								<!-- runtime parameter 1 (required): number of concurrent threads -->
								<argument>1</argument>

								<!-- runtime parameter 2 (required): domain IDs -->
								<argument>2702</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>playwrightCrawl_5813</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DplaywrightCrawl_5813</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.playwright.PlaywrightCrawl</argument>

								<!-- runtime parameter 1 (required): number of concurrent threads -->
								<argument>1</argument>

								<!-- runtime parameter 2 (required): domain IDs -->
								<argument>5813</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>playwrightCrawl_7487</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DplaywrightCrawl_7487</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.playwright.PlaywrightCrawl</argument>

								<!-- runtime parameter 1 (required): number of concurrent threads -->
								<argument>1</argument>

								<!-- runtime parameter 2 (required): domain IDs -->
								<argument>7487</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>playwrightCrawl_7691</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DplaywrightCrawl_7691</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.playwright.PlaywrightCrawl</argument>

								<!-- runtime parameter 1 (required): number of concurrent threads -->
								<argument>1</argument>

								<!-- runtime parameter 2 (required): domain IDs -->
								<argument>7691</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>playwrightCrawl_8614</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DplaywrightCrawl_8614</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.playwright.PlaywrightCrawl</argument>

								<!-- runtime parameter 1 (required): number of concurrent threads -->
								<argument>1</argument>

								<!-- runtime parameter 2 (required): domain IDs -->
								<argument>8614</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>playwrightCrawl_9666</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DplaywrightCrawl_9666</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.playwright.PlaywrightCrawl</argument>

								<!-- runtime parameter 1 (required): number of concurrent threads -->
								<argument>1</argument>

								<!-- runtime parameter 2 (required): domain IDs -->
								<argument>9666</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>playwrightCrawl_9688</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DplaywrightCrawl_9688</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.playwright.PlaywrightCrawl</argument>

								<!-- runtime parameter 1 (required): number of concurrent threads -->
								<argument>1</argument>

								<!-- runtime parameter 2 (required): domain IDs -->
								<argument>9688</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>pageRankInSQL</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DgtmTest_1000</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.PageRankInSQL</argument>

								<!-- runtime parameter 1 (required): number of URLs -->
								<argument>1000</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>amazonAdHocCrawl</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DamazonAdHocCrawl</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.polite.crawl.AmazonAdHocCrawl</argument>

								<!-- runtime parameter 1 (required): input file location path -->
								<!-- <argument>/home/<USER>/source/test_polite_crawl_put_messages/data/IT_URLs.csv</argument> -->
								<!-- <argument>/home/<USER>/source/test_polite_crawl_put_messages/data/test.csv</argument> -->								
								<argument>/home/<USER>/source/test_polite_crawl_put_messages/data/meta-label-extract-url-list-20210822.txt</argument>

								<!-- runtime parameter 2 (required): output file location path -->
								<!-- <argument>/home/<USER>/source/test_polite_crawl_put_messages/output/IT_URLs_output.txt</argument> -->
								<!-- <argument>/home/<USER>/source/test_polite_crawl_put_messages/output/test_output.txt</argument> -->
								<argument>/home/<USER>/source/test_polite_crawl_put_messages/output/meta-label-extract-url-list-20210822_output.txt</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>amazonAdHocCrawl_IT</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DamazonAdHocCrawl_IT</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.polite.crawl.AmazonAdHocCrawl</argument>

								<!-- runtime parameter 1 (required): input file location path -->
								<argument>/home/<USER>/source/test_polite_crawl_put_messages/data/IT_URLs.csv</argument>

								<!-- runtime parameter 2 (required): output file location path -->
								<argument>/home/<USER>/source/test_polite_crawl_put_messages/output/IT_URLs_output.txt</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>amazonAdHocCrawl_FR</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DamazonAdHocCrawl_FR</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.polite.crawl.AmazonAdHocCrawl</argument>

								<!-- runtime parameter 1 (required): input file location path -->
								<argument>/home/<USER>/source/test_polite_crawl_put_messages/data/FR_URLs.csv</argument>

								<!-- runtime parameter 2 (required): output file location path -->
								<argument>/home/<USER>/source/test_polite_crawl_put_messages/output/FR_URLs_output.txt</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>amazonAdHocCrawl_ES</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx38000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DamazonAdHocCrawl_ES</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.polite.crawl.AmazonAdHocCrawl</argument>

								<!-- runtime parameter 1 (required): input file location path -->
								<argument>/home/<USER>/source/test_polite_crawl_put_messages/data/ES_URLs.csv</argument>

								<!-- runtime parameter 2 (required): output file location path -->
								<argument>/home/<USER>/source/test_polite_crawl_put_messages/output/ES_URLs_output.txt</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>amazonAdHocCrawl_JP</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx38000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DamazonAdHocCrawl_JP</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.polite.crawl.AmazonAdHocCrawl</argument>

								<!-- runtime parameter 1 (required): input file location path -->
								<argument>/home/<USER>/source/test_polite_crawl_put_messages/data/JP_URLs.csv</argument>

								<!-- runtime parameter 2 (required): output file location path -->
								<argument>/home/<USER>/source/test_polite_crawl_put_messages/output/JP_URLs_output.txt</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>amazonAdHocCrawl_CA</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx38000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DamazonAdHocCrawl_CA</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.polite.crawl.AmazonAdHocCrawl</argument>

								<!-- runtime parameter 1 (required): input file location path -->
								<argument>/home/<USER>/source/test_polite_crawl_put_messages/data/CA_URLs.csv</argument>

								<!-- runtime parameter 2 (required): output file location path -->
								<argument>/home/<USER>/source/test_polite_crawl_put_messages/output/CA_URLs_output.txt</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>amazonAdHocCrawl_DE</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx38000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DamazonAdHocCrawl_DE</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.polite.crawl.AmazonAdHocCrawl</argument>

								<!-- runtime parameter 1 (required): input file location path -->
								<argument>/home/<USER>/source/test_polite_crawl_put_messages/data/DE_URLs.csv</argument>

								<!-- runtime parameter 2 (required): output file location path -->
								<argument>/home/<USER>/source/test_polite_crawl_put_messages/output/DE_URLs_output.txt</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>amazonAdHocCrawl_UK</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx38000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DamazonAdHocCrawl_UK</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.polite.crawl.AmazonAdHocCrawl</argument>

								<!-- runtime parameter 1 (required): input file location path -->
								<argument>/home/<USER>/source/test_polite_crawl_put_messages/data/UK_URLs.csv</argument>

								<!-- runtime parameter 2 (required): output file location path -->
								<argument>/home/<USER>/source/test_polite_crawl_put_messages/output/UK_URLs_output.txt</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>amazonAdHocCrawl_IN</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx38000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DamazonAdHocCrawl_IN</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.polite.crawl.AmazonAdHocCrawl</argument>

								<!-- runtime parameter 1 (required): input file location path -->
								<argument>/home/<USER>/source/test_polite_crawl_put_messages/data/IN_URLs.csv</argument>

								<!-- runtime parameter 2 (required): output file location path -->
								<argument>/home/<USER>/source/test_polite_crawl_put_messages/output/IN_URLs_output.txt</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>amazonAdHocCrawl_US</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx38000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DamazonAdHocCrawl_US</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.polite.crawl.AmazonAdHocCrawl</argument>

								<!-- runtime parameter 1 (required): input file location path -->
								<argument>/home/<USER>/source/test_polite_crawl_put_messages/data/US_URLs.csv</argument>

								<!-- runtime parameter 2 (required): output file location path -->
								<argument>/home/<USER>/source/test_polite_crawl_put_messages/output/US_URLs_output.txt</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>rCallerTest</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DrCallerTest</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.test.RCallerTest</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>causalImpactResourceTest</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DcausalImpactResourceTest</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.test.CausalImpactResourceTest</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>mainCausalImpactResourceTest</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DmainCausalImpactResourceTest</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.test.MainCausalImpactResourceTest</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>prophetResourceTest</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DprophetResourceTest</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.test.ProphetResourceTest</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>mainProphetResourceTest</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DmainProphetResourceTest</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.test.MainProphetResourceTest</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>ownDomainServiceTest</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DownDomainServiceTest</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.test.OwnDomainServiceTest</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>adHocCrawl</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx38000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DadHocCrawl</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.polite.crawl.AdHocCrawl</argument>

								<!-- runtime parameter 1 (required): input file location path -->
								<!-- <argument>/home/<USER>/source/test_polite_crawl_put_messages/data/IT_URLs.csv</argument> -->
								<!-- <argument>/home/<USER>/source/test_polite_crawl_put_messages/data/test.csv</argument> -->								
								<argument>/home/<USER>/source/test_polite_crawl_put_messages/data/meta-label-extract-url-list-20210822.txt</argument>

								<!-- runtime parameter 2 (required): output file location path -->
								<!-- <argument>/home/<USER>/source/test_polite_crawl_put_messages/output/IT_URLs_output.txt</argument> -->
								<!-- <argument>/home/<USER>/source/test_polite_crawl_put_messages/output/test_output.txt</argument> -->
								<argument>/home/<USER>/source/test_polite_crawl_put_messages/output/meta-label-extract-url-list-20210822-output.txt</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>mainMarketMatchingResourceTest</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DmainMarketMatchingResourceTest</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.test.MainMarketMatchingResourceTest</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>extractHtml</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DextractHtml</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.polite.crawl.ExtractHtml</argument>

								<!-- runtime parameter 1 (required): input file location path -->
								<!-- <argument>/home/<USER>/source/test_polite_crawl_put_messages/data/IT_URLs.csv</argument> -->
								<!-- <argument>/home/<USER>/source/test_polite_crawl_put_messages/data/test.csv</argument> -->								
								<argument>/home/<USER>/source/test_polite_crawl_put_messages/data/wrike-ticket-802087634-input.txt</argument>

								<!-- runtime parameter 2 (required): output file location path -->
								<!-- <argument>/home/<USER>/source/test_polite_crawl_put_messages/output/IT_URLs_output.txt</argument> -->
								<!-- <argument>/home/<USER>/source/test_polite_crawl_put_messages/output/test_output.txt</argument> -->
								<argument>/home/<USER>/source/test_polite_crawl_put_messages/output/wrike-ticket-802087634-summary.txt</argument>

								<!-- runtime parameter 3 (required): HTML folder location -->
								<!-- <argument>/home/<USER>/source/test_polite_crawl_put_messages/output/IT_URLs_output.txt</argument> -->
								<!-- <argument>/home/<USER>/source/test_polite_crawl_put_messages/output/test_output.txt</argument> -->
								<argument>/home/<USER>/source/test_polite_crawl_put_messages/wrike_ticket_802087634_html_files/</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<!-- On Sundays, send competitor URLs to weekly HTML polite crawl queues for ClickHouse -->
					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>testPutStep3CompetitorUrlsToHtmlQueuesWeekly</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DtestPutStep3CompetitorUrlsToHtmlQueuesWeekly</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx88000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.put.messages.TestSendAssociatedUrlsToCrawlQueuesByDomain</argument>

								<!-- runtime parameter 1: process type (solr/clickhouse) (required) -->
								<argument>clickhouse</argument>

								<!-- runtime parameter 2: number of queues (required) -->
								<argument>3168</argument>

								<!-- runtime parameter 3: step number (required) -->
								<argument>3</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<!-- for competitor URL HTML crawl v2.0 -->
					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>testPoliteCrawl</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtestPoliteCrawl</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.polite.crawl.TestPoliteCrawl</argument>

								<!-- runtime parameter 1 (optional): number of concurrent threads -->
								<argument>38</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>updateTestCrawlControllerQueue</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DupdateTestCrawlControllerQueue</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx18000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.PutMesssagesToQueue</argument>

								<!-- runtime parameter 1: message data file -->
								<argument>/home/<USER>/source/polite_crawl_put_messages/data/test_crawl_queue_names_messages.txt</argument>

								<!-- runtime parameter 2: queue name -->
								<argument>TEST_COMPETITOR_URL_HTML_QUEUE_NAMES</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>putStartTestCrawlMessage</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DputStartTestCrawlMessage</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.PutMesssagesToQueue</argument>

								<!-- runtime parameter 1: message data file -->
								<argument>/home/<USER>/source/polite_crawl_put_messages/data/start_crawl_message.txt</argument>

								<!-- runtime parameter 2: queue name -->
								<argument>TEST_TARGET_URL_HTML_STOP_CRAWL_QUEUE</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>putStopTestCrawlMessage</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DputStopTestCrawlMessage</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.PutMesssagesToQueue</argument>

								<!-- runtime parameter 1: message data file -->
								<argument>/home/<USER>/source/polite_crawl_put_messages/data/stop_crawl_message.txt</argument>

								<!-- runtime parameter 2: queue name -->
								<argument>TEST_TARGET_URL_HTML_STOP_CRAWL_QUEUE</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>autoAssociationMaintenance</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DautoAssociationMaintenance</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.auto.association.AutoAssociationMaintenance</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>mainSerpAnalyzerResourceTest</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DmainSerpAnalyzerResourceTest</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.test.MainSerpAnalyzerResourceTest</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>targetUrlHtml1BackupToS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtargetUrlHtml1BackupToS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TargetUrlHtmlBackupToS3</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (cdb21-url-001) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_target_url_html</argument>

								<!-- runtime parameter 3 (required): S3 bucket name -->						
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 4 (required): S3 prefix (partial) -->
								<argument>cdb21-url-001/crawl/local_target_url_html/</argument>

								<!-- runtime parameter 5 (required): backup type (initial or incremental) -->
								<!-- <argument>initial</argument> -->
								<argument>incremental</argument>

								<!-- runtime parameter 6 (optional): backup track date override for incremental backup (yyyy-mm-dd)  -->
								<!-- <argument>2022-04-09</argument> -->

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>targetUrlHtml2BackupToS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtargetUrlHtml2BackupToS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TargetUrlHtmlBackupToS3</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (cdb21-url-002) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_target_url_html</argument>

								<!-- runtime parameter 3 (required): S3 bucket name -->						
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 4 (required): S3 prefix (partial) -->
								<argument>cdb21-url-002/crawl/local_target_url_html/</argument>

								<!-- runtime parameter 5 (required): backup type (initial or incremental) -->
								<!-- <argument>initial</argument> -->
								<argument>incremental</argument>

								<!-- runtime parameter 6 (optional): backup track date override for incremental backup (yyyy-mm-dd)  -->
								<!-- <argument>2022-04-09</argument> -->

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>targetUrlHtml3BackupToS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtargetUrlHtml3BackupToS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TargetUrlHtmlBackupToS3</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (cdb21-url-003) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_target_url_html</argument>

								<!-- runtime parameter 3 (required): S3 bucket name -->						
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 4 (required): S3 prefix (partial) -->
								<argument>cdb21-url-003/crawl/local_target_url_html/</argument>

								<!-- runtime parameter 5 (required): backup type (initial or incremental) -->
								<!-- <argument>initial</argument> -->
								<argument>incremental</argument>

								<!-- runtime parameter 6 (optional): backup track date override for incremental backup (yyyy-mm-dd)  -->
								<!-- <argument>2022-04-09</argument> -->

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>targetUrlHtml1RestoreFromS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtargetUrlHtml1RestoreFromS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TargetUrlHtmlRestoreFromS3</argument>

								<!-- runtime parameter 1 (required): destination Clickhouse database server IP address (cdb21-url-001) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): destination Clickhouse database table name -->
								<argument>restored_target_url_html</argument>

								<!-- runtime parameter 3 (required): audit trail table name -->
								<argument>local_target_url_html</argument>

								<!-- runtime parameter 4 (required): S3 bucket name -->
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 5 (required): S3 prefix (partial) -->
								<argument>cdb21-url-001/crawl/local_target_url_html/</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>targetUrlHtml2RestoreFromS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtargetUrlHtml2RestoreFromS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TargetUrlHtmlRestoreFromS3</argument>

								<!-- runtime parameter 1 (required): destination Clickhouse database server IP address (cdb21-url-002) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): destination Clickhouse database table name -->
								<argument>restored_target_url_html</argument>

								<!-- runtime parameter 3 (required): audit trail table name -->
								<argument>local_target_url_html</argument>

								<!-- runtime parameter 4 (required): S3 bucket name -->
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 5 (required): S3 prefix (partial) -->
								<argument>cdb21-url-002/crawl/local_target_url_html/</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>targetUrlHtml3RestoreFromS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtargetUrlHtml3RestoreFromS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TargetUrlHtmlRestoreFromS3</argument>

								<!-- runtime parameter 1 (required): destination Clickhouse database server IP address (cdb21-url-003) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): destination Clickhouse database table name -->
								<argument>restored_target_url_html</argument>

								<!-- runtime parameter 3 (required): audit trail table name -->
								<argument>local_target_url_html</argument>

								<!-- runtime parameter 4 (required): S3 bucket name -->
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 5 (required): S3 prefix (partial) -->
								<argument>cdb21-url-003/crawl/local_target_url_html/</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>competitorUrlHtml1BackupToS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DcompetitorUrlHtml1BackupToS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.CompetitorUrlHtmlBackupToS3</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (cdb21-url-001) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_competitor_url_html</argument>

								<!-- runtime parameter 3 (required): S3 bucket name -->						
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 4 (required): S3 prefix (partial) -->
								<argument>cdb21-url-001/crawl/local_competitor_url_html/</argument>

								<!-- runtime parameter 5 (required): backup type (initial or incremental) -->
								<!-- <argument>initial</argument> -->								
								<argument>incremental</argument>

								<!-- runtime parameter 6 (optional): backup track date override for incremental backup (yyyy-mm-dd)  -->
								<!-- <argument>2022-04-09</argument> -->

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>competitorUrlHtml2BackupToS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DcompetitorUrlHtml2BackupToS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.CompetitorUrlHtmlBackupToS3</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (cdb21-url-002) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_competitor_url_html</argument>

								<!-- runtime parameter 3 (required): S3 bucket name -->						
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 4 (required): S3 prefix (partial) -->
								<argument>cdb21-url-002/crawl/local_competitor_url_html/</argument>

								<!-- runtime parameter 5 (required): backup type (initial or incremental) -->
								<!-- <argument>initial</argument> -->								
								<argument>incremental</argument>

								<!-- runtime parameter 6 (optional): backup track date override for incremental backup (yyyy-mm-dd)  -->
								<!-- <argument>2022-04-09</argument> -->

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>competitorUrlHtml3BackupToS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DcompetitorUrlHtml3BackupToS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.CompetitorUrlHtmlBackupToS3</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (cdb21-url-003) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_competitor_url_html</argument>

								<!-- runtime parameter 3 (required): S3 bucket name -->						
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 4 (required): S3 prefix (partial) -->
								<argument>cdb21-url-003/crawl/local_competitor_url_html/</argument>

								<!-- runtime parameter 5 (required): backup type (initial or incremental) -->
								<!-- <argument>initial</argument> -->								
								<argument>incremental</argument>

								<!-- runtime parameter 6 (optional): backup track date override for incremental backup (yyyy-mm-dd)  -->
								<!-- <argument>2022-04-09</argument> -->

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>competitorUrlHtml1RestoreFromS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DcompetitorUrlHtml1RestoreFromS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.CompetitorUrlHtmlRestoreFromS3</argument>

								<!-- runtime parameter 1 (required): destination Clickhouse database server IP address (cdb21-url-001) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): destination Clickhouse database table name -->
								<argument>restored_competitor_url_html</argument>

								<!-- runtime parameter 3 (required): audit trail table name -->
								<argument>local_competitor_url_html</argument>

								<!-- runtime parameter 4 (required): S3 bucket name -->
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 5 (required): S3 prefix (partial) -->
								<argument>cdb21-url-001/crawl/local_competitor_url_html/</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>competitorUrlHtml2RestoreFromS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DcompetitorUrlHtml2RestoreFromS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.CompetitorUrlHtmlRestoreFromS3</argument>

								<!-- runtime parameter 1 (required): destination Clickhouse database server IP address (cdb21-url-002) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): destination Clickhouse database table name -->
								<argument>restored_competitor_url_html</argument>

								<!-- runtime parameter 3 (required): audit trail table name -->
								<argument>local_competitor_url_html</argument>

								<!-- runtime parameter 4 (required): S3 bucket name -->
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 5 (required): S3 prefix (partial) -->
								<argument>cdb21-url-002/crawl/local_competitor_url_html/</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>competitorUrlHtml3RestoreFromS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DcompetitorUrlHtml3RestoreFromS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.CompetitorUrlHtmlRestoreFromS3</argument>

								<!-- runtime parameter 1 (required): destination Clickhouse database server IP address (cdb21-url-003) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): destination Clickhouse database table name -->
								<argument>restored_competitor_url_html</argument>

								<!-- runtime parameter 3 (required): audit trail table name -->
								<argument>local_competitor_url_html</argument>

								<!-- runtime parameter 4 (required): S3 bucket name -->
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 5 (required): S3 prefix (partial) -->
								<argument>cdb21-url-003/crawl/local_competitor_url_html/</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>contentGuard1BackupToS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DcontentGuard1BackupToS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.ContentGuardBackupToS3</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (cdb21-url-001) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_content_guard</argument>

								<!-- runtime parameter 3 (required): S3 bucket name -->						
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 4 (required): S3 prefix (partial) -->
								<argument>cdb21-url-001/crawl/local_content_guard/</argument>

								<!-- runtime parameter 5 (required): backup type (initial or incremental) -->								
								<!-- <argument>initial</argument> -->
								<argument>incremental</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>contentGuard2BackupToS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DcontentGuard2BackupToS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.ContentGuardBackupToS3</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (cdb21-url-002) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_content_guard</argument>

								<!-- runtime parameter 3 (required): S3 bucket name -->						
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 4 (required): S3 prefix (partial) -->
								<argument>cdb21-url-002/crawl/local_content_guard/</argument>

								<!-- runtime parameter 5 (required): backup type (initial or incremental) -->								
								<!-- <argument>initial</argument> -->
								<argument>incremental</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>contentGuard3BackupToS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DcontentGuard3BackupToS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.ContentGuardBackupToS3</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (cdb21-url-003) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_content_guard</argument>

								<!-- runtime parameter 3 (required): S3 bucket name -->						
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 4 (required): S3 prefix (partial) -->
								<argument>cdb21-url-003/crawl/local_content_guard/</argument>

								<!-- runtime parameter 5 (required): backup type (initial or incremental) -->								
								<!-- <argument>initial</argument> -->
								<argument>incremental</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>contentGuard1RestoreFromS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DcontentGuard1RestoreFromS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.ContentGuardRestoreFromS3</argument>

								<!-- runtime parameter 1 (required): destination Clickhouse database server IP address (cdb21-url-001) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): destination Clickhouse database table name -->
								<argument>restored_content_guard</argument>

								<!-- runtime parameter 3 (required): audit trail table name -->
								<argument>local_content_guard</argument>

								<!-- runtime parameter 4 (required): S3 bucket name -->
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 5 (required): S3 prefix (partial) -->
								<argument>cdb21-url-001/crawl/local_content_guard/</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>contentGuard2RestoreFromS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DcontentGuard2RestoreFromS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.ContentGuardRestoreFromS3</argument>

								<!-- runtime parameter 1 (required): destination Clickhouse database server IP address (cdb21-url-002) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): destination Clickhouse database table name -->
								<argument>restored_content_guard</argument>

								<!-- runtime parameter 3 (required): audit trail table name -->
								<argument>local_content_guard</argument>

								<!-- runtime parameter 4 (required): S3 bucket name -->
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 5 (required): S3 prefix (partial) -->
								<argument>cdb21-url-002/crawl/local_content_guard/</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>contentGuard3RestoreFromS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DcontentGuard3RestoreFromS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.ContentGuardRestoreFromS3</argument>

								<!-- runtime parameter 1 (required): destination Clickhouse database server IP address (cdb21-url-003) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): destination Clickhouse database table name -->
								<argument>restored_content_guard</argument>

								<!-- runtime parameter 3 (required): audit trail table name -->
								<argument>local_content_guard</argument>

								<!-- runtime parameter 4 (required): S3 bucket name -->
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 5 (required): S3 prefix (partial) -->
								<argument>cdb21-url-003/crawl/local_content_guard/</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>targetUrlHtmlFileName1BackupToS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtargetUrlHtmlFileName1BackupToS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TargetUrlHtmlFileNameBackupToS3</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (cdb21-url-001) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_target_url_html_file_name</argument>

								<!-- runtime parameter 3 (required): S3 bucket name -->						
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 4 (required): S3 prefix (partial) -->
								<argument>cdb21-url-001/crawl/local_target_url_html_file_name/</argument>

								<!-- runtime parameter 5 (required): backup type (initial or incremental) -->								
								<!-- <argument>initial</argument> -->
								<argument>incremental</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>targetUrlHtmlFileName2BackupToS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtargetUrlHtmlFileName2BackupToS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TargetUrlHtmlFileNameBackupToS3</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (cdb21-url-002) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_target_url_html_file_name</argument>

								<!-- runtime parameter 3 (required): S3 bucket name -->						
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 4 (required): S3 prefix (partial) -->
								<argument>cdb21-url-002/crawl/local_target_url_html_file_name/</argument>

								<!-- runtime parameter 5 (required): backup type (initial or incremental) -->								
								<!-- <argument>initial</argument> -->
								<argument>incremental</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>targetUrlHtmlFileName3BackupToS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtargetUrlHtmlFileName3BackupToS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TargetUrlHtmlFileNameBackupToS3</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (cdb21-url-003) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_target_url_html_file_name</argument>

								<!-- runtime parameter 3 (required): S3 bucket name -->						
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 4 (required): S3 prefix (partial) -->
								<argument>cdb21-url-003/crawl/local_target_url_html_file_name/</argument>

								<!-- runtime parameter 5 (required): backup type (initial or incremental) -->								
								<!-- <argument>initial</argument> -->
								<argument>incremental</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>targetUrlHtmlFileName1RestoreFromS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtargetUrlHtmlFileName1RestoreFromS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TargetUrlHtmlFileNameRestoreFromS3</argument>

								<!-- runtime parameter 1 (required): destination Clickhouse database server IP address (cdb21-url-001) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): destination Clickhouse database table name -->
								<argument>restored_target_url_html_file_name</argument>

								<!-- runtime parameter 3 (required): audit trail table name -->
								<argument>local_target_url_html_file_name</argument>

								<!-- runtime parameter 4 (required): S3 bucket name -->
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 5 (required): S3 prefix (partial) -->
								<argument>cdb21-url-001/crawl/local_target_url_html_file_name/</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>targetUrlHtmlFileName2RestoreFromS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtargetUrlHtmlFileName2RestoreFromS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TargetUrlHtmlFileNameRestoreFromS3</argument>

								<!-- runtime parameter 1 (required): destination Clickhouse database server IP address (cdb21-url-002) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): destination Clickhouse database table name -->
								<argument>restored_target_url_html_file_name</argument>

								<!-- runtime parameter 3 (required): audit trail table name -->
								<argument>local_target_url_html_file_name</argument>

								<!-- runtime parameter 4 (required): S3 bucket name -->
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 5 (required): S3 prefix (partial) -->
								<argument>cdb21-url-002/crawl/local_target_url_html_file_name/</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>targetUrlHtmlFileName3RestoreFromS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtargetUrlHtmlFileName3RestoreFromS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TargetUrlHtmlFileNameRestoreFromS3</argument>

								<!-- runtime parameter 1 (required): destination Clickhouse database server IP address (cdb21-url-003) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): destination Clickhouse database table name -->
								<argument>restored_target_url_html_file_name</argument>

								<!-- runtime parameter 3 (required): audit trail table name -->
								<argument>local_target_url_html_file_name</argument>

								<!-- runtime parameter 4 (required): S3 bucket name -->
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 5 (required): S3 prefix (partial) -->
								<argument>cdb21-url-003/crawl/local_target_url_html_file_name/</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>targetUrlHtmlDaily1BackupToS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtargetUrlHtmlDaily1BackupToS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TargetUrlHtmlDailyBackupToS3</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (cdb21-url-001) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_target_url_html_daily</argument>

								<!-- runtime parameter 3 (required): S3 bucket name -->						
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 4 (required): S3 prefix (partial) -->
								<argument>cdb21-url-001/crawl/local_target_url_html_daily/</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>targetUrlHtmlDaily2BackupToS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtargetUrlHtmlDaily2BackupToS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TargetUrlHtmlDailyBackupToS3</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (cdb21-url-002) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_target_url_html_daily</argument>

								<!-- runtime parameter 3 (required): S3 bucket name -->						
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 4 (required): S3 prefix (partial) -->
								<argument>cdb21-url-002/crawl/local_target_url_html_daily/</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>targetUrlHtmlDaily3BackupToS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtargetUrlHtmlDaily3BackupToS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TargetUrlHtmlDailyBackupToS3</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (cdb21-url-003) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_target_url_html_daily</argument>

								<!-- runtime parameter 3 (required): S3 bucket name -->						
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 4 (required): S3 prefix (partial) -->
								<argument>cdb21-url-003/crawl/local_target_url_html_daily/</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>targetUrlHtmlDaily1RestoreFromS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtargetUrlHtmlDaily1RestoreFromS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TargetUrlHtmlDailyRestoreFromS3</argument>

								<!-- runtime parameter 1 (required): destination Clickhouse database server IP address (cdb21-url-001) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): destination Clickhouse database table name -->
								<argument>restored_target_url_html_daily</argument>

								<!-- runtime parameter 3 (required): audit trail table name -->
								<argument>local_target_url_html_daily</argument>

								<!-- runtime parameter 4 (required): S3 bucket name -->
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 5 (required): S3 prefix (partial) -->
								<argument>cdb21-url-001/crawl/local_target_url_html_daily/</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>targetUrlHtmlDaily2RestoreFromS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtargetUrlHtmlDaily2RestoreFromS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TargetUrlHtmlDailyRestoreFromS3</argument>

								<!-- runtime parameter 1 (required): destination Clickhouse database server IP address (cdb21-url-002) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): destination Clickhouse database table name -->
								<argument>restored_target_url_html_daily</argument>

								<!-- runtime parameter 3 (required): audit trail table name -->
								<argument>local_target_url_html_daily</argument>

								<!-- runtime parameter 4 (required): S3 bucket name -->
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 5 (required): S3 prefix (partial) -->
								<argument>cdb21-url-002/crawl/local_target_url_html_daily/</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>targetUrlHtmlDaily3RestoreFromS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtargetUrlHtmlDaily3RestoreFromS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TargetUrlHtmlDailyRestoreFromS3</argument>

								<!-- runtime parameter 1 (required): destination Clickhouse database server IP address (cdb21-url-003) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): destination Clickhouse database table name -->
								<argument>restored_target_url_html_daily</argument>

								<!-- runtime parameter 3 (required): audit trail table name -->
								<argument>local_target_url_html_daily</argument>

								<!-- runtime parameter 4 (required): S3 bucket name -->
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 5 (required): S3 prefix (partial) -->
								<argument>cdb21-url-003/crawl/local_target_url_html_daily/</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>targetUrlCustomData1BackupToS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtargetUrlCustomData1BackupToS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TargetUrlCustomDataBackupToS3</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (cdb21-url-001) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_target_url_custom_data</argument>

								<!-- runtime parameter 3 (required): S3 bucket name -->						
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 4 (required): S3 prefix (partial) -->
								<argument>cdb21-url-001/crawl/local_target_url_custom_data/</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>targetUrlCustomData2BackupToS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtargetUrlCustomData2BackupToS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TargetUrlCustomDataBackupToS3</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (cdb21-url-002) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_target_url_custom_data</argument>

								<!-- runtime parameter 3 (required): S3 bucket name -->						
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 4 (required): S3 prefix (partial) -->
								<argument>cdb21-url-002/crawl/local_target_url_custom_data/</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>targetUrlCustomData3BackupToS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtargetUrlCustomData3BackupToS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TargetUrlCustomDataBackupToS3</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (cdb21-url-003) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_target_url_custom_data</argument>

								<!-- runtime parameter 3 (required): S3 bucket name -->						
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 4 (required): S3 prefix (partial) -->
								<argument>cdb21-url-003/crawl/local_target_url_custom_data/</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>targetUrlCustomData1RestoreFromS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtargetUrlCustomData1RestoreFromS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TargetUrlCustomDataRestoreFromS3</argument>

								<!-- runtime parameter 1 (required): destination Clickhouse database server IP address (cdb21-url-001) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): destination Clickhouse database table name -->
								<argument>restored_target_url_custom_data</argument>

								<!-- runtime parameter 3 (required): audit trail table name -->
								<argument>local_target_url_custom_data</argument>

								<!-- runtime parameter 4 (required): S3 bucket name -->
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 5 (required): S3 prefix (partial) -->
								<argument>cdb21-url-001/crawl/local_target_url_custom_data/</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>targetUrlCustomData2RestoreFromS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtargetUrlCustomData2RestoreFromS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TargetUrlCustomDataRestoreFromS3</argument>

								<!-- runtime parameter 1 (required): destination Clickhouse database server IP address (cdb21-url-002) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): destination Clickhouse database table name -->
								<argument>restored_target_url_custom_data</argument>

								<!-- runtime parameter 3 (required): audit trail table name -->
								<argument>local_target_url_custom_data</argument>

								<!-- runtime parameter 4 (required): S3 bucket name -->
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 5 (required): S3 prefix (partial) -->
								<argument>cdb21-url-002/crawl/local_target_url_custom_data/</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>targetUrlCustomData3RestoreFromS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtargetUrlCustomData3RestoreFromS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TargetUrlCustomDataRestoreFromS3</argument>

								<!-- runtime parameter 1 (required): destination Clickhouse database server IP address (cdb21-url-003) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): destination Clickhouse database table name -->
								<argument>restored_target_url_custom_data</argument>

								<!-- runtime parameter 3 (required): audit trail table name -->
								<argument>local_target_url_custom_data</argument>

								<!-- runtime parameter 4 (required): S3 bucket name -->
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 5 (required): S3 prefix (partial) -->
								<argument>cdb21-url-003/crawl/local_target_url_custom_data/</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>testCompetitorUrlQueueDepthReport</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtestCompetitorUrlQueueDepthReport</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.QueueDepthReport</argument>

								<!-- runtime parameter 1 (required): output file location path -->
								<argument>/home/<USER>/source/polite_crawl_put_messages/output/testCompetitorUrlQueueDepthReport.txt</argument>

								<!-- runtime parameter 2 (required): queue prefixes -->
								<argument>TEST_COMPETITOR_URL_HTML_</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>targetUrlAdditionalContentTest</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtargetUrlAdditionalContentTest</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.test.TargetUrlAdditionalContentTest</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>putMessagesToAdhocBacklinkDataRetrieval</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DputMessagesToAdhocBacklinkDataRetrieval</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx18000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.PutMesssagesToQueue</argument>

								<!-- runtime parameter 1: message data file -->
								<argument>/home/<USER>/source/test_polite_crawl_put_messages/data/test3.txt</argument>

								<!-- runtime parameter 2: queue name -->
								<argument>AD_HOC_BACKLINK_DATA_RETRIEVAL</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>updateTargetUrlCustomData</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DupdateTargetUrlCustomData</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx18000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.polite.crawl.UpdateTargetUrlCustomData</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>transferTargetUrlHtml1</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtransferTargetUrlHtml1</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TransferTargetUrlHtml</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (claritydb-url-001) -->
								<argument>**********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_target_url_html</argument>

								<!-- runtime parameter 3 (required): destination Clickhouse database server IP address (cdb21-url-001) -->
								<argument>***********</argument>

								<!-- runtime parameter 4 (required): destination table name -->
								<argument>local_target_url_html</argument>

								<!-- runtime parameter 5 (optional): start track date override (yyyy-mm-dd) -->
								<argument>2022-04-08</argument>

								<!-- runtime parameter 6 (optional): end track date override (yyyy-mm-dd) -->
								<argument>2022-04-10</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>transferTargetUrlHtml2</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtransferTargetUrlHtml2</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TransferTargetUrlHtml</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (claritydb-url-002) -->
								<argument>**********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_target_url_html</argument>

								<!-- runtime parameter 3 (required): destination Clickhouse database server IP address (cdb21-url-002) -->
								<argument>***********</argument>

								<!-- runtime parameter 4 (required): destination table name -->
								<argument>local_target_url_html</argument>

								<!-- runtime parameter 5 (optional): start track date override (yyyy-mm-dd) -->
								<argument>2022-04-08</argument>

								<!-- runtime parameter 6 (optional): end track date override (yyyy-mm-dd) -->
								<argument>2022-04-10</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>transferTargetUrlHtml3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtransferTargetUrlHtml3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TransferTargetUrlHtml</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (claritydb-url-003) -->
								<argument>**********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_target_url_html</argument>

								<!-- runtime parameter 3 (required): destination Clickhouse database server IP address (cdb21-url-003) -->
								<argument>***********</argument>

								<!-- runtime parameter 4 (required): destination table name -->
								<argument>local_target_url_html</argument>

								<!-- runtime parameter 5 (optional): start track date override (yyyy-mm-dd) -->
								<argument>2022-04-08</argument>

								<!-- runtime parameter 6 (optional): end track date override (yyyy-mm-dd) -->
								<argument>2022-04-10</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>transferCompetitorUrlHtml1</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtransferCompetitorUrlHtml1</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TransferCompetitorUrlHtml</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (claritydb-url-001) -->
								<argument>**********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_competitor_url_html</argument>

								<!-- runtime parameter 3 (required): destination Clickhouse database server IP address (cdb21-url-001) -->
								<argument>***********</argument>

								<!-- runtime parameter 4 (required): destination table name -->
								<!-- <argument>local_competitor_url_html</argument> -->								
								<argument>local_competitor_url_html</argument>

								<!-- runtime parameter 5 (optional): start track date override (yyyy-mm-dd) -->
								<argument>2022-04-08</argument>

								<!-- runtime parameter 6 (optional): end track date override (yyyy-mm-dd) -->
								<argument>2022-04-10</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>transferCompetitorUrlHtml2</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtransferCompetitorUrlHtml2</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TransferCompetitorUrlHtml</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (claritydb-url-002) -->
								<argument>**********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_competitor_url_html</argument>

								<!-- runtime parameter 3 (required): destination Clickhouse database server IP address (cdb21-url-002) -->
								<argument>***********</argument>

								<!-- runtime parameter 4 (required): destination table name -->
								<argument>local_competitor_url_html</argument>

								<!-- runtime parameter 5 (optional): start track date override (yyyy-mm-dd) -->
								<argument>2022-04-08</argument>

								<!-- runtime parameter 6 (optional): end track date override (yyyy-mm-dd) -->
								<argument>2022-04-10</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>transferCompetitorUrlHtml3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtransferCompetitorUrlHtml3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TransferCompetitorUrlHtml</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (claritydb-url-003) -->
								<argument>**********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_competitor_url_html</argument>

								<!-- runtime parameter 3 (required): destination Clickhouse database server IP address (cdb21-url-003) -->
								<argument>***********</argument>

								<!-- runtime parameter 4 (required): destination table name -->
								<argument>local_competitor_url_html</argument>

								<!-- runtime parameter 5 (optional): start track date override (yyyy-mm-dd) -->
								<argument>2022-04-08</argument>

								<!-- runtime parameter 6 (optional): end track date override (yyyy-mm-dd) -->
								<argument>2022-04-10</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>transferContentGuard1</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtransferContentGuard1</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TransferContentGuard</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (claritydb-url-001) -->
								<argument>**********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_content_guard</argument>

								<!-- runtime parameter 3 (required): destination Clickhouse database server IP address (cdb21-url-001) -->
								<argument>***********</argument>

								<!-- runtime parameter 4 (required): destination table name -->
								<argument>local_content_guard</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>transferContentGuard2</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtransferContentGuard2</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TransferContentGuard</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (claritydb-url-002) -->
								<argument>**********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_content_guard</argument>

								<!-- runtime parameter 3 (required): destination Clickhouse database server IP address (cdb21-url-002) -->
								<argument>***********</argument>

								<!-- runtime parameter 4 (required): destination table name -->
								<argument>local_content_guard</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>transferContentGuard3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtransferContentGuard3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TransferContentGuard</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (claritydb-url-003) -->
								<argument>**********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_content_guard</argument>

								<!-- runtime parameter 3 (required): destination Clickhouse database server IP address (cdb21-url-003) -->
								<argument>***********</argument>

								<!-- runtime parameter 4 (required): destination table name -->
								<argument>local_content_guard</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>transferTargetUrlHtmlFileName1</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtransferTargetUrlHtmlFileName1</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TransferTargetUrlHtmlFileName</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (claritydb-url-001) -->
								<argument>**********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_target_url_html_file_name</argument>

								<!-- runtime parameter 3 (required): destination Clickhouse database server IP address (cdb21-url-001) -->
								<argument>***********</argument>

								<!-- runtime parameter 4 (required): destination table name -->
								<argument>local_target_url_html_file_name</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>transferTargetUrlHtmlFileName2</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtransferTargetUrlHtmlFileName2</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TransferTargetUrlHtmlFileName</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (claritydb-url-002) -->
								<argument>**********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_target_url_html_file_name</argument>

								<!-- runtime parameter 3 (required): destination Clickhouse database server IP address (cdb21-url-002) -->
								<argument>***********</argument>

								<!-- runtime parameter 4 (required): destination table name -->
								<argument>local_target_url_html_file_name</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>transferTargetUrlHtmlFileName3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtransferTargetUrlHtmlFileName3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TransferTargetUrlHtmlFileName</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (claritydb-url-003) -->
								<argument>**********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_target_url_html_file_name</argument>

								<!-- runtime parameter 3 (required): destination Clickhouse database server IP address (cdb21-url-003) -->
								<argument>***********</argument>

								<!-- runtime parameter 4 (required): destination table name -->
								<argument>local_target_url_html_file_name</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>transferTargetUrlHtmlDaily1</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtransferTargetUrlHtmlDaily1</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TransferTargetUrlHtmlDaily</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (claritydb-url-001) -->
								<argument>**********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_target_url_html_daily</argument>

								<!-- runtime parameter 3 (required): destination Clickhouse database server IP address (cdb21-url-001) -->
								<argument>***********</argument>

								<!-- runtime parameter 4 (required): destination table name -->
								<argument>local_target_url_html_daily</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>transferTargetUrlHtmlDaily2</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtransferTargetUrlHtmlDaily2</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TransferTargetUrlHtmlDaily</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (claritydb-url-002) -->
								<argument>**********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_target_url_html_daily</argument>

								<!-- runtime parameter 3 (required): destination Clickhouse database server IP address (cdb21-url-002) -->
								<argument>***********</argument>

								<!-- runtime parameter 4 (required): destination table name -->
								<argument>local_target_url_html_daily</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>transferTargetUrlHtmlDaily3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtransferTargetUrlHtmlDaily3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TransferTargetUrlHtmlDaily</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (claritydb-url-003) -->
								<argument>**********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_target_url_html_daily</argument>

								<!-- runtime parameter 3 (required): destination Clickhouse database server IP address (cdb21-url-003) -->
								<argument>***********</argument>

								<!-- runtime parameter 4 (required): destination table name -->
								<argument>local_target_url_html_daily</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>transferTargetUrlCustomData1</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtransferTargetUrlCustomData1</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TransferTargetUrlCustomData</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (claritydb-url-001) -->
								<argument>**********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_target_url_custom_data</argument>

								<!-- runtime parameter 3 (required): destination Clickhouse database server IP address (cdb21-url-001) -->
								<argument>***********</argument>

								<!-- runtime parameter 4 (required): destination table name -->
								<argument>local_target_url_custom_data</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>transferTargetUrlCustomData2</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtransferTargetUrlCustomData2</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TransferTargetUrlCustomData</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (claritydb-url-002) -->
								<argument>**********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_target_url_custom_data</argument>

								<!-- runtime parameter 3 (required): destination Clickhouse database server IP address (cdb21-url-002) -->
								<argument>***********</argument>

								<!-- runtime parameter 4 (required): destination table name -->
								<argument>local_target_url_custom_data</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>transferTargetUrlCustomData3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtransferTargetUrlCustomData3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TransferTargetUrlCustomData</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (claritydb-url-003) -->
								<argument>**********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_target_url_custom_data</argument>

								<!-- runtime parameter 3 (required): destination Clickhouse database server IP address (cdb21-url-003) -->
								<argument>***********</argument>

								<!-- runtime parameter 4 (required): destination table name -->
								<argument>local_target_url_custom_data</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>targetUrlHtmlConversion1</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtargetUrlHtmlConversion1</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TargetUrlHtmlConversion</argument>

								<!-- runtime parameter 1 (required): Clickhouse database server IP address (cdb21-url-001) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_target_url_html</argument>

								<!-- runtime parameter 3 (required): destination table name -->
								<argument>test_target_url_html</argument>

								<!-- runtime parameter 4 (optional): start track date override (yyyy-mm-dd) -->
								<argument>2022-05-05</argument>

								<!-- runtime parameter 5 (optional): end track date override (yyyy-mm-dd) -->
								<argument>2022-05-09</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>targetUrlHtmlConversion2</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtargetUrlHtmlConversion2</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TargetUrlHtmlConversion</argument>

								<!-- runtime parameter 1 (required): Clickhouse database server IP address (cdb21-url-002) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_target_url_html</argument>

								<!-- runtime parameter 3 (required): destination table name -->
								<argument>test_target_url_html</argument>

								<!-- runtime parameter 4 (optional): start track date override (yyyy-mm-dd) -->
								<argument>2022-05-02</argument>

								<!-- runtime parameter 5 (optional): end track date override (yyyy-mm-dd) -->
								<argument>2022-05-05</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>targetUrlHtmlConversion3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtargetUrlHtmlConversion3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TargetUrlHtmlConversion</argument>

								<!-- runtime parameter 1 (required): Clickhouse database server IP address (cdb21-url-003) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_target_url_html</argument>

								<!-- runtime parameter 3 (required): destination table name -->
								<argument>test_target_url_html</argument>

								<!-- runtime parameter 4 (optional): start track date override (yyyy-mm-dd) -->
								<argument>2022-05-02</argument>

								<!-- runtime parameter 5 (optional): end track date override (yyyy-mm-dd) -->
								<argument>2022-05-05</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>updateTargetUrlChange</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DupdateTargetUrlChange</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx38000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.polite.crawl.UpdateTargetUrlChange</argument>

								<!-- runtime parameter 1 (optional): start crawl timestamp (YYYY-MM-DD HH:MM:SS) -->
								<!-- <argument>2022-05-17 10:17:12</argument> -->								

								<!-- runtime parameter 2 (optional): end crawl timestamp (YYYY-MM-DD HH:MM:SS) -->
								<!-- <argument>2022-05-17 10:33:59</argument> -->								

								<!-- runtime parameter 3 (optional): list of domain IDs -->
								<!-- <argument>7523</argument> -->

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>nearDuplicateDetection</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DnearDuplicateDetection</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx18000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.polite.crawl.NearDuplicateDetection</argument>

								<!-- runtime parameter 1 (required): number of concurrent threads -->
								<argument>1</argument>

								<!-- runtime parameter 2 (optional): list of domain IDs -->
								<!-- <argument>476</argument> -->
								<!-- <argument>7487</argument> -->
								<!-- <argument>6631</argument> -->
								<!-- <argument>6679</argument> -->
								<!-- <argument>10395</argument> -->
								<!-- <argument>10239</argument> -->								
								<argument>9153</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<!-- extract one 'target_url_change' record -->
					<execution>
						<id>targetUrlChangeExtract</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<argument>-Duser.timezone=America/Chicago</argument>
								<!-- <argument>-Duser.timezone=UTC</argument> -->								

								<!-- specify the process identifier -->
								<argument>-DtargetUrlChangeExtract</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TargetUrlChangeExtract</argument>

								<!-- runtime parameter 1 (required): output file location path -->
								<argument>/home/<USER>/source/test_polite_crawl_put_messages/output/target_url_change_extract.txt</argument>

								<!-- runtime parameter 2 (required): domain ID -->
								<argument>10755</argument>

								<!-- runtime parameter 3 (required): URL string -->
								<argument>https://www.angi.com/companylist/us/tx/highlands/pro-septic-repairs-reviews-9473874.htm</argument>

								<!-- runtime parameter 4 (required): crawl timestamp (YYYY-MM-DD HH:MM:SS) -->
								<argument>2022-05-20 13:14:59</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>targetUrlChangeAlert_daily</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<argument>-Duser.timezone=America/Chicago</argument>
								<!-- <argument>-Duser.timezone=UTC</argument> -->								

								<!-- specify the process identifier -->
								<argument>-DtargetUrlChangeAlert_daily</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.polite.crawl.TargetUrlChangeAlert</argument>

								<!-- runtime parameter 1 (required): alert frequency type (1-daily,2-hourly) -->
								<argument>1</argument>

								<!-- runtime parameter 2 (optional): is test? true/false -->
								<!-- <argument>true</argument> -->								

								<!-- runtime parameter 3 (optional): domain IDs -->
								<!-- <argument>4661</argument> -->								

								<!-- runtime parameter 4 (optional): crawl date override YYYY-MM-DD -->
								<!-- <argument>2022-08-22</argument> -->																

								<!-- runtime parameter 5 (optional): crawl hour override 00 - 23 -->
								<!-- <argument>08</argument> -->								

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>targetUrlChangeAlert_hourly</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<argument>-Duser.timezone=America/Chicago</argument>
								<!-- <argument>-Duser.timezone=UTC</argument> -->								

								<!-- specify the process identifier -->
								<argument>-DtargetUrlChangeAlert_hourly</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.polite.crawl.TargetUrlChangeAlert</argument>

								<!-- runtime parameter 1 (required): alert frequency type (1-daily,2-hourly) -->
								<argument>2</argument>

								<!-- runtime parameter 2 (optional): is test? true/false -->
								<!-- <argument>true</argument> -->																												

								<!-- runtime parameter 3 (optional): domain IDs -->
								<!-- <argument>4661</argument> -->								

								<!-- runtime parameter 4 (optional): crawl date override YYYY-MM-DD -->
								<!-- <argument>2022-08-22</argument> -->								

								<!-- runtime parameter 5 (optional): crawl hour override 00 - 23 -->
								<!-- <argument>19</argument> -->								

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>sqsUtilsTest</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DsqsUtilsTest</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx6000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.test.SQSUtilsTest</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>sqsUtilsTest_summary</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DsqsUtilsTest_summary</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx6000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.test.SQSUtilsTest</argument>

								<!-- runtime parameter 1: summary/detail -->
								<argument>summary</argument>

								<!-- runtime parameter 2: domain ID -->
								<argument>8711</argument>

								<!-- runtime parameter 3: start timestamp -->
								<argument>2022-05-01 00:00:00</argument>

								<!-- runtime parameter 4: end timestamp -->
								<argument>2022-06-08 23:59:59</argument>

								<!-- runtime parameter 5: change indicator(s) -->
								<argument>["h1_added_ind","h1_chg_ind","h1_removed_ind","h2_added_ind","h2_chg_ind","h2_removed_ind","title_added_ind","title_chg_ind","title_removed_ind","page_analysis_results_chg_ind_json"]</argument>								
								<!-- <argument>-</argument> -->

								<!-- runtime parameter 6: page tag ID(s) -->
								<argument>[6229331,1370808,1365655,1357660,1365654,1215835,1306297,1365656,1377844,1374728,7010425,7010427]</argument>
								<!-- <argument>-</argument> -->

								<!-- runtime parameter 7: URL filter -->
								<argument>(url like '%indeed.com/career-advice%')</argument>
								<!-- <argument>-</argument> -->								

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>sqsUtilsTest_detail</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DsqsUtilsTest_detail</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx6000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.test.SQSUtilsTest</argument>

								<!-- runtime parameter 1: summary/detail -->
								<argument>detail</argument>

								<!-- runtime parameter 2: domain ID -->
								<argument>8711</argument>

								<!-- runtime parameter 3: start timestamp -->
								<argument>2022-06-08 00:00:00</argument>

								<!-- runtime parameter 4: end timestamp -->
								<argument>2022-06-12 23:59:59</argument>

								<!-- runtime parameter 5: change indicator(s) -->
								<argument>["h1_added_ind","h1_chg_ind","h1_removed_ind","h2_added_ind","h2_chg_ind","h2_removed_ind","title_added_ind","title_chg_ind","title_removed_ind","page_analysis_results_chg_ind_json"]</argument>
								<!-- <argument>-</argument> -->								

								<!-- runtime parameter 6: page tag ID(s) -->
								<argument>[6229331,1370808,1365655,1357660,1365654,1215835,1306297,1365656,1377844,1374728,7010425,7010427]</argument>
								<!-- <argument>-</argument> -->

								<!-- runtime parameter 7: sortby 1 = url asc, 2 = url desc, 3 = crawl timestamp asc, 4 = crawl timestamp desc -->
								<argument>1</argument>

								<!-- runtime parameter 8: rows per page -->
								<argument>10</argument>

								<!-- runtime parameter 9: page number -->
								<argument>1</argument>

								<!-- runtime parameter 10: URL filter -->
								<argument>(url like '%indeed.com/career-advice%')</argument>
								<argument>-</argument>								

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>targetUrlChangeResourceTest</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DtargetUrlChangeResourceTest</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx6000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.test.TargetUrlChangeResourceTest</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>initializeTestCrawl</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DinitializeTestCrawl</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx6000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.InitializeTestCrawl</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>crawlerPerformanceComparison</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DcrawlerPerformanceComparison</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx6000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.CrawlerPerformanceComparison</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>zapierTargetUrlChangeAlertResourceTest</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DzapierTargetUrlChangeAlertResourceTest</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx6000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.test.ZapierTargetUrlChangeAlertResourceTest</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>targetUrlChangeIndResourceTest</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DtargetUrlChangeIndResourceTest</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx6000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.test.TargetUrlChangeIndResourceTest</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>targetUrlChangeInd1BackupToS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtargetUrlChangeInd1BackupToS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TargetUrlChangeIndBackupToS3</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (cdb21-url-001) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_target_url_change_ind</argument>

								<!-- runtime parameter 3 (required): S3 bucket name -->						
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 4 (required): S3 prefix (partial) -->
								<argument>cdb21-url-001/crawl/local_target_url_change_ind/</argument>

								<!-- runtime parameter 5 (required): backup type (initial or incremental) -->
								<!-- <argument>initial</argument> -->
								<argument>incremental</argument>

								<!-- runtime parameter 6 (optional): backup track date override for incremental backup (yyyy-mm-dd)  -->
								<!-- <argument>2022-04-09</argument> -->

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>targetUrlChangeInd2BackupToS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtargetUrlChangeInd2BackupToS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TargetUrlChangeIndBackupToS3</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (cdb21-url-002) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_target_url_change_ind</argument>

								<!-- runtime parameter 3 (required): S3 bucket name -->						
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 4 (required): S3 prefix (partial) -->
								<argument>cdb21-url-002/crawl/local_target_url_change_ind/</argument>

								<!-- runtime parameter 5 (required): backup type (initial or incremental) -->
								<!-- <argument>initial</argument> -->
								<argument>incremental</argument>

								<!-- runtime parameter 6 (optional): backup track date override for incremental backup (yyyy-mm-dd)  -->
								<!-- <argument>2022-04-09</argument> -->

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>targetUrlChangeInd3BackupToS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtargetUrlChangeInd3BackupToS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TargetUrlChangeIndBackupToS3</argument>

								<!-- runtime parameter 1 (required): source Clickhouse database server IP address (cdb21-url-003) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): source table name -->
								<argument>local_target_url_change_ind</argument>

								<!-- runtime parameter 3 (required): S3 bucket name -->						
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 4 (required): S3 prefix (partial) -->
								<argument>cdb21-url-003/crawl/local_target_url_change_ind/</argument>

								<!-- runtime parameter 5 (required): backup type (initial or incremental) -->
								<!-- <argument>initial</argument> -->
								<argument>incremental</argument>

								<!-- runtime parameter 6 (optional): backup track date override for incremental backup (yyyy-mm-dd)  -->
								<!-- <argument>2022-04-09</argument> -->

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>targetUrlChangeInd1RestoreFromS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtargetUrlChangeInd1RestoreFromS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TargetUrlChangeIndRestoreFromS3</argument>

								<!-- runtime parameter 1 (required): destination Clickhouse database server IP address (cdb21-url-001) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): destination Clickhouse database table name -->
								<argument>restored_target_url_change_ind</argument>

								<!-- runtime parameter 3 (required): audit trail table name -->
								<argument>local_target_url_change_ind</argument>

								<!-- runtime parameter 4 (required): S3 bucket name -->
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 5 (required): S3 prefix (partial) -->
								<argument>cdb21-url-001/crawl/local_target_url_change_ind/</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>targetUrlChangeInd2RestoreFromS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtargetUrlChangeInd2RestoreFromS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TargetUrlChangeIndRestoreFromS3</argument>

								<!-- runtime parameter 1 (required): destination Clickhouse database server IP address (cdb21-url-002) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): destination Clickhouse database table name -->
								<argument>restored_target_url_change_ind</argument>

								<!-- runtime parameter 3 (required): audit trail table name -->
								<argument>local_target_url_change_ind</argument>

								<!-- runtime parameter 4 (required): S3 bucket name -->
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 5 (required): S3 prefix (partial) -->
								<argument>cdb21-url-002/crawl/local_target_url_change_ind/</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>targetUrlChangeInd3RestoreFromS3</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DtargetUrlChangeInd3RestoreFromS3</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TargetUrlChangeIndRestoreFromS3</argument>

								<!-- runtime parameter 1 (required): destination Clickhouse database server IP address (cdb21-url-003) -->
								<argument>***********</argument>

								<!-- runtime parameter 2 (required): destination Clickhouse database table name -->
								<argument>restored_target_url_change_ind</argument>

								<!-- runtime parameter 3 (required): audit trail table name -->
								<argument>local_target_url_change_ind</argument>

								<!-- runtime parameter 4 (required): S3 bucket name -->
								<argument>seoclarity-cdb</argument>

								<!-- runtime parameter 5 (required): S3 prefix (partial) -->
								<argument>cdb21-url-003/crawl/local_target_url_change_ind/</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>targetUrlChangeAlert_test_daily</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<argument>-Duser.timezone=America/Chicago</argument>
								<!-- <argument>-Duser.timezone=UTC</argument> -->								

								<!-- specify the process identifier -->
								<argument>-DtargetUrlChangeAlert_test_daily</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.polite.crawl.TargetUrlChangeAlert</argument>

								<!-- runtime parameter 1 (required): alert frequency type (1-daily,2-hourly) -->
								<argument>1</argument>

								<!-- runtime parameter 2 (optional): is test? true/false -->
								<argument>true</argument>

								<!-- runtime parameter 3 (optional): domain IDs -->
								<argument>9998</argument>
								<!-- <argument>6457</argument> -->
								<!-- <argument>2119</argument> -->	
								<!-- <argument>2541</argument> -->

								<!-- runtime parameter 4 (optional): crawl date override YYYY-MM-DD -->
								<argument>2022-12-27</argument>
								<!-- <argument>2022-11-24</argument> -->
								<!-- <argument>2022-09-05</argument> -->
								<!-- <argument>2022-11-22</argument> -->

								<!-- runtime parameter 5 (optional): crawl hour override 00 - 23 -->
								<!-- <argument>03</argument> -->								

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>updateTestTargetUrlChange</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DupdateTestTargetUrlChange</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.polite.crawl.UpdateTargetUrlChange</argument>

								<!-- runtime parameter 1 (optional): start crawl timestamp (YYYY-MM-DD HH:MM:SS) -->
								<argument>2022-09-20 08:31:58</argument>								

								<!-- runtime parameter 2 (optional): end crawl timestamp (YYYY-MM-DD HH:MM:SS) -->
								<argument>2022-09-21 08:50:07</argument>								

								<!-- runtime parameter 3 (optional): list of domain IDs -->
								<argument>10777</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>putDomain4765TargetUrlsToHtmlDailyTestQueue</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DputDomain4765TargetUrlsToHtmlDailyTestQueue</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx36000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.put.messages.SendUrlsToDailyCrawlQueuesByDomain</argument>

								<!-- runtime parameter 1: process type (solr/clickhouse/test/control) (required) -->
								<argument>test</argument>

								<!-- runtime parameter 2: is process domains with 10,000 or less target URLs (required) -->
								<argument>false</argument>

								<!-- runtime parameter 3: domain IDs (optional) -->
								<argument>4765</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>targetUrlHtmlFileNameConversion</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DtargetUrlHtmlFileNameConversion</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx36000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TargetUrlHtmlFileNameConversion</argument>

								<!-- runtime parameter 1 (required): dis_content_guard.txt file path -->
								<argument>/home/<USER>/source/test_polite_crawl_put_messages/data/dis_content_guard.txt</argument>

								<!-- runtime parameter 2(required): dis_target_url_html_file_name.txt file path -->
								<argument>/home/<USER>/source/test_polite_crawl_put_messages/data/dis_target_url_html_file_name.txt</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>putMesssagesToFifoQueue</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DputMesssagesToFifoQueue</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.PutMesssagesToFifoQueue</argument>

								<!-- runtime parameter 1: message data file -->
								<argument>/home/<USER>/source/polite_crawl_put_messages/data/competitor_html_queue_names_messages.txt</argument>

								<!-- runtime parameter 2: queue name -->
								<argument>TEST_FIFO_QUEUE_1.fifo</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>puppeteerTest</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DpuppeteerTest</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.polite.crawl.PuppeteerTest</argument>

								<!-- runtime parameter 1 (required): number of concurrent threads -->
								<argument>4</argument>

								<!-- runtime parameter 2 (required): input file location path -->
								<argument>/home/<USER>/source/test_polite_crawl_put_messages/data/puppeteer.test.input.txt</argument>

								<!-- runtime parameter 3 (required): output file location path -->
								<argument>/home/<USER>/source/test_polite_crawl_put_messages/output/puppeteer.test.output.txt</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<!-- for target URL HTML Skyscanner crawl v2.0 -->
					<execution>
						<!-- specify a unique execution ID, very useful when executing same Java class with different runtime parameters -->
						<id>skyscannerDailyCrawl</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- <argument>-Duser.timezone=America/Chicago</argument> -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- process identifier for process restart and stop -->
								<argument>-DskyscannerDailyCrawl</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.polite.crawl.PoliteCrawl</argument>

								<!-- runtime parameter 1 (optional): number of concurrent threads -->
								<argument>1</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>

						</configuration>
					</execution>

					<execution>
						<id>updateSkyscannerControllerQueue</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DupdateSkyscannerControllerQueue</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx6000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.put.messages.SendUrlsToDailyCrawlQueuesByDomain</argument>

								<!-- runtime parameter 1: process type (solr/clickhouse/test/control) (required) -->
								<argument>control</argument>

								<!-- runtime parameter 2: -->
								<argument>-</argument>

								<!-- runtime parameter 3: -->
								<argument>-</argument>

								<!-- runtime parameter 4: process Skyscanner domains (true/false) -->
								<argument>true</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>putStartSkyscannerCrawlMessage</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DputStartSkyscannerCrawlMessage</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.PutMesssagesToQueue</argument>

								<!-- runtime parameter 1: message data file -->
								<argument>/home/<USER>/source/polite_crawl_put_messages/data/start_crawl_message.txt</argument>

								<!-- runtime parameter 2: queue name -->
								<argument>SKYSCANNER_STOP_CRAWL_QUEUE</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>putStopSkyscannerCrawlMessage</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DputStopSkyscannerCrawlMessage</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.PutMesssagesToQueue</argument>

								<!-- runtime parameter 1: message data file -->
								<argument>/home/<USER>/source/polite_crawl_put_messages/data/stop_crawl_message.txt</argument>

								<!-- runtime parameter 2: queue name -->
								<argument>SKYSCANNER_STOP_CRAWL_QUEUE</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>updateTestTargetUrlCrawlControllerQueue</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DupdateTestTargetUrlCrawlControllerQueue</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.put.messages.TestSendUrlsToDailyCrawlQueuesByDomain</argument>

								<!-- runtime parameter 1: process type (solr/clickhouse/test/control) (required) -->
								<argument>control</argument>

								<!-- runtime parameter 2: -->
								<argument>-</argument>

								<!-- runtime parameter 3: -->
								<argument>-</argument>

								<!-- runtime parameter 4: process Skyscanner domains (true/false) -->
								<argument>false</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>testPutMesssagesToQueue</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DtestPutMesssagesToQueue</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.TestPutMesssagesToQueue</argument>

								<!-- runtime parameter 1: message data file -->
								<argument>/home/<USER>/source/test_polite_crawl_put_messages/data/target.urls.2023-01-03.txt</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>compareCrawlData</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DcompareCrawlData</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx8000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.CompareCrawlData</argument>

							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>newTargetUrlSender</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>

								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DnewTargetUrlSender</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx88000m</argument>

								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.put.messages.TargetUrlSender</argument>
								<!-- runtime parameter 1 domainType: is process domains with 30,000 or less target URLs (required) -->
								<argument>1</argument>
								<!-- runtime parameter 2 (optional): number of concurrent threads -->
								<argument>12</argument>


							</arguments>
							<classpathScope>runtime</classpathScope>
						</configuration>
					</execution>

					<execution>
						<id>syncPoliteCrawlInstance</id>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>java</executable>
							<arguments>
								<!-- specify the JAR file created by mvn clean install -->
								<argument>-classpath</argument>
								<argument>target/polite-crawl-put-messages-1.0.jar</argument>

								<!-- runtime JVM argument 1 (required): user.timezone=UTC (for local environment) -->
								<!-- runtime JVM argument 1 (required): user.timezone=America/Chicago (for production server environment) -->
								<argument>-Duser.timezone=America/Chicago</argument>

								<!-- specify the process identifier -->
								<argument>-DsyncPoliteCrawlInstance</argument>

								<!-- specify maximum physical memory -->
								<argument>-Xmx88000m</argument>
								<!-- specify the JAVA class to be executed -->
								<argument>com.actonia.process.SyncPoliteCrawlInstance</argument>

							</arguments>
						</configuration>

					</execution>

				</executions>
			</plugin>
		</plugins>
	</build>
</project>
