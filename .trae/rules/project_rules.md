- Always follow Java code conventions, using Google Java Style Guide or Oracle's official standards
- Use PascalCase for class names, camelCase for method and variable names
- Use UPPERCASE with underscores for constants (e.g., MAX_VALUE)
- Package names should be all lowercase, using reverse domain name format (e.g., com.company.project)
- Every class, method, and field should have appropriate Javadoc comments
- Methods should not exceed 30 lines, classes should not exceed 500 lines
- Prefer composition over inheritance
- Follow SOLID principles when designing classes and interfaces
- Use Optional for potentially null return values, avoid returning null
- Use try-with-resources to automatically close resources
- Use Stream API and Lambda expressions to simplify collection operations
## Architecture Design Principles
- Adopt layered architecture (e.g., Controller-Service-Repository)
- Use dependency injection to manage component dependencies
- Apply Domain-Driven Design (DDD) principles for complex business logic
- Use design patterns to solve common problems, but avoid over-engineering
- Follow single responsibility principle in microservice architecture
- Design APIs according to RESTful principles
- Use DTOs for data transfer, avoid exposing entity classes directly
## Performance Optimization
- Use connection pools to manage database connections
- Use caching appropriately to reduce database access
- Avoid database operations inside loops
- Use batch processing for handling large volumes of data
- Use asynchronous programming for I/O-intensive operations
- Configure thread pool parameters appropriately
- Use CompletableFuture for concurrent tasks
## Security Practices
- Use parameterized queries to prevent SQL injection
- Validate and escape user input
- Encrypt sensitive data in storage
- Use HTTPS for data transmission
- Implement appropriate authentication and authorization mechanisms
- Avoid logging sensitive information
- Use secure password hashing algorithms (e.g., BCrypt)
## Testing Standards
- Unit test coverage should be at least 80%
- Use JUnit 5 and Mockito for testing
- Write readable test cases following the Given-When-Then pattern
- Use test fixtures to reduce test code duplication
- Include integration tests and end-to-end tests
- Follow Test-Driven Development (TDD) methodology
## Tools & Frameworks
- Spring Boot as the preferred application framework
- Hibernate/JPA for ORM
- Maven/Gradle for dependency management
- Lombok to reduce boilerplate code
- SLF4J + Logback for logging
- JaCoCo for test coverage analysis
- Swagger/OpenAPI for API documentation
## Code Review Standards
- Code must pass static code analysis tools (e.g., SonarQube)
- No duplicate code (DRY principle)
- No unhandled compiler warnings
- Follow team-agreed naming conventions
- Clear and understandable code logic
- Appropriate exception handling mechanisms
- Sufficient unit test coverage
## Continuous Integration/Continuous Deployment
- Every commit should trigger automated builds and tests
- Main branch code must pass all tests
- Use semantic versioning
- Follow Git Flow or similar branch management strategy
- Perform performance testing and security scanning before deployment