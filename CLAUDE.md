# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview
PoliteCrawl is a comprehensive Java-based web crawling and content monitoring system that specializes in polite web scraping, content change detection, and SEO analytics. The system uses <PERSON>ven for build management and implements a distributed architecture with AWS SQS queues, ClickHouse databases, and multiple data storage backends. The project focuses on enterprise-level web content monitoring with features like near-duplicate detection, content guard alerts, and change tracking.

## Key Technologies and Frameworks
- **Java 8** - Core programming language
- **Maven** - Build management and dependency resolution
- **Spring Framework 2.5/3.0** - Dependency injection and application context management
- **AWS SDK** - SQS queue management and cloud services integration
- **ClickHouse** - High-performance columnar database for analytics
- **MySQL** - Primary relational database
- **JUnit 4** - Unit testing framework
- **Jackson 2.8.5** - JSON processing
- **Jsoup 1.13.1** - HTML parsing and manipulation
- **Log4j2** - Logging framework
- **RCaller** - R language integration for statistical analysis

## Project Architecture
The system follows a layered architecture with domain-driven design principles:

### Core Packages
- `com.actonia.entity` - JPA entities and data models
- `com.actonia.dao` - Data access objects with ClickHouse and MySQL support
- `com.actonia.service` - Business logic services including web service clients
- `com.actonia.utils` - Utility classes for AWS, database, HTTP, and common operations
- `com.actonia.web` - Web controllers and REST endpoints

### Specialized Modules
- `com.actonia.polite.crawl` - Core crawling engine with message processing
- `com.actonia.content.guard` - Content monitoring and change detection
- `com.actonia.simhash` - SimHash algorithm for near-duplicate detection
- `com.actonia.put.messages` - Queue management and message distribution
- `com.actonia.robots.txt` - Robots.txt parsing and compliance
- `com.actonia.process` - Data processing and transformation pipelines
- `com.actonia.page.content.change` - Content change tracking and analysis

### Value Objects and Configuration
- `com.actonia.value.object` - Data transfer objects and value types
- `com.actonia.concurrency` - Thread pool management and concurrency utilities

## Common Commands

### Building the Project
```bash
mvn clean compile
```

### Running Tests
```bash
mvn test
# Run specific test class
mvn test -Dtest=TargetUrlEntityDAOTest
```

### Building JAR Package
```bash
mvn clean package
```

### Running Specific Executions
The project uses exec-maven-plugin with multiple predefined executions:
```bash
# Content extraction and processing
mvn exec:exec -DsharedCountsCrossReferenceExtract
mvn exec:exec -DsharedCountsExtract
mvn exec:exec -DsharedCountsUpdate
mvn exec:exec -DsharedCountsReindex

# HTML processing and conversion
mvn exec:exec -DtargetUrlHtmlConversion1
mvn exec:exec -DtargetUrlHtmlConversion2
mvn exec:exec -DtargetUrlHtmlConversion3

# Link clarity and analysis
mvn exec:exec -DlinkGainLossExtract
mvn exec:exec -DinternalLinksPageRank

# Data transfer and backup
mvn exec:exec -DtargetUrlHtmlBackupToS3
mvn exec:exec -DtargetUrlHtmlRestoreFromS3
mvn exec:exec -DcompetitorUrlHtmlBackupToS3

# Queue management
mvn exec:exec -DsendUrlsToDailyCrawlQueuesByDomain
mvn exec:exec -DsendAssociatedUrlsToCrawlQueuesByDomain
```

### Running Main Applications
```bash
# Core crawling applications
mvn exec:java -Dexec.mainClass="com.actonia.polite.crawl.PoliteCrawl"
mvn exec:java -Dexec.mainClass="com.actonia.polite.crawl.AdHocCrawl"
mvn exec:java -Dexec.mainClass="com.actonia.polite.crawl.NearDuplicateDetection"

# Content guard applications
mvn exec:java -Dexec.mainClass="com.actonia.polite.crawl.TargetUrlChangeAlert"
```

## Key Components and Workflows

### 1. Crawling System
- **PoliteCrawl** - Main crawling engine with rate limiting and politeness policies
- **AdHocCrawl** - On-demand crawling for specific URLs
- **ContentGuard** - Continuous monitoring for content changes
- **NearDuplicateDetection** - SimHash-based duplicate content detection

### 2. Queue Management
- **SQS Integration** - Distributed message processing with AWS SQS
- **Multi-queue Support** - Separate queues for different crawl types (HTML, social, competitor)
- **Dead Letter Handling** - Retry and error handling mechanisms

### 3. Data Storage
- **ClickHouse** - Primary analytics database for crawl data and metrics
- **MySQL** - Configuration and metadata storage
- **S3 Integration** - Backup and archival of HTML content
- **Solr** - Search indexing (optional, based on configuration)

### 4. Content Analysis
- **Change Detection** - Tracks content changes with severity levels
- **SimHash Algorithm** - Efficient near-duplicate detection
- **SEO Metrics** - Comprehensive SEO analysis and reporting
- **Custom Data Extraction** - XPath and CSS selector-based content extraction

## Configuration Files
- `src/main/resources/jdbc.properties` - Database connection settings
- `src/main/resources/clickhouse.properties` - ClickHouse configuration
- `src/main/resources/config.properties` - Application configuration
- `src/main/resources/springconfig/` - Spring application context files
- `src/main/resources/AwsCredentials.properties` - AWS credentials and settings

## Testing Structure
Tests are organized in two main directories:
- `src/test/java/` - Standard unit tests
- `src/main/java/com/actonia/test/` - Integration and functional tests

Key test categories:
- `*Test.java` - Unit tests for individual components
- `Main*Test.java` - Integration tests for web services
- `*ResourceTest.java` - REST API endpoint tests
- `*UtilsTest.java` - Utility function tests

## Code Standards and Patterns
- **Spring Dependency Injection** - Heavy use of XML-based Spring configuration
- **DAO Pattern** - Data access abstraction with support for multiple databases
- **Command Pattern** - Many operations implement command objects for queue processing
- **Factory Pattern** - Used for creating different types of crawlers and processors
- **Constants Interface** - `IConstants` interface defines all system-wide constants
- **Logging** - Comprehensive logging using Log4j2 with proper log levels

## Database Schema
The system uses multiple database schemas:
- **Primary MySQL** - User management, configuration, and metadata
- **ClickHouse Analytics** - Time-series crawl data, metrics, and analytics
- **Backup Systems** - S3 for HTML content archival with ClickHouse metadata

## AWS Integration
- **SQS Queues** - Distributed message processing with multiple queue types
- **S3 Storage** - HTML content backup and retrieval
- **IAM Roles** - Secure access to AWS resources
- **Regional Support** - Multi-region deployment support (Oregon, London)

## Error Handling and Monitoring
- **Comprehensive Logging** - Detailed logging at all levels
- **Alert System** - Email and webhook notifications for critical events
- **Retry Mechanisms** - Configurable retry logic for failed operations
- **Health Checks** - System health monitoring and reporting